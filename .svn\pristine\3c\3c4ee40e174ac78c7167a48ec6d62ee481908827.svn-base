﻿Imports System.IO
Imports System.Data.SqlClient
Imports System.Windows.Forms
Imports C1.Win.C1TrueDBGrid

Public Class fFicheArticle

    Public ajoutmodif As String = ""
    Public CodeArticle As String = ""
    Public DesignationArticle As String = ""
    Public StockArticle As Integer = 0

    Dim cmdArticle As New SqlCommand
    Dim daArticle As New SqlDataAdapter
    Dim dsArticle As New DataSet
    Dim cbArticle As New SqlCommandBuilder

    Dim cmdArticle1 As New SqlCommand
    Dim daArticle1 As New SqlDataAdapter
    Dim dsArticle1 As New DataSet
    Dim cbArticle1 As New SqlCommandBuilder

    Dim dsRecupereNum As New DataSet
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNumt As New SqlDataAdapter

    Dim CodeExiste As Boolean = False
    Dim CodePCTExist As Boolean = False

    Dim CmdCalcul As New SqlCommand
    Dim PrixModifier As Boolean = False
    '------------------------------------------------- variable pour la gestion des lots 

    Dim cmdLotsArticle As New SqlCommand
    Dim daLotsArticle As New SqlDataAdapter
    Dim cbLotsArticle As New SqlCommandBuilder
    Dim dsLotsArticle As New DataSet

    Dim xLotsArticle As Integer
    Dim StrSQL As String = ""

    Dim ValeurDuStock As Integer = 0

    Public AjoutReussi As Boolean = False
    Public CodeArticleAjout As String
    Public DesignationAjout As String
    Public PrixAchatAjout As String
    Public PrixVenteAjout As String
    Public CodeFormeAjout As String
    Public CodeRayonAjout As String
    Public CodeRayonRAjout As String

    Public EstConfirmer As Boolean = False

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "114" And bConfirmer.Enabled = True Then
            bConfirmer_Click(sender, e)
        End If
        If argument = "121" And bAnnuler.Enabled = True Then
            bAnnuler_Click(sender, e)
        End If
        If argument = "113" Then
            bNaviger_Click(sender, e)
        End If
    End Sub

    Public Sub Init()

        Dim StrSQL1 As String = ""
        Dim StrSQLdernierAchat As String = ""
        Dim stock As Integer = 0
        Dim cmdChimique As New SqlCommand
        Dim daChimique As New SqlDataAdapter
        Dim dsChimique As New DataSet
        Dim cbChimique As New SqlCommandBuilder

        'chargement des Formes
        StrSQL1 = "SELECT DISTINCT CodeForme,LibelleForme FROM FORME_ARTICLE Where SupprimeForme=0 ORDER BY LibelleForme ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "FORME_ARTICLE")
        cmbForme.DataSource = dsArticle.Tables("FORME_ARTICLE")
        cmbForme.ValueMember = "CodeForme"
        cmbForme.DisplayMember = "LibelleForme"
        cmbForme.ColumnHeaders = False
        cmbForme.Splits(0).DisplayColumns("CodeForme").Visible = False
        cmbForme.Splits(0).DisplayColumns("LibelleForme").Width = 10
        cmbForme.ExtendRightColumn = True

        'chargement des Categories
        StrSQL1 = "SELECT DISTINCT CodeCategorie,LibelleCategorie FROM Categorie WHERE SupprimeCategorie=0 ORDER BY LibelleCategorie ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "CATEGORIE")
        cmbCategorie.DataSource = dsArticle.Tables("CATEGORIE")
        cmbCategorie.ValueMember = "CodeCategorie"
        cmbCategorie.DisplayMember = "LibelleCategorie"
        cmbCategorie.ColumnHeaders = False
        cmbCategorie.Splits(0).DisplayColumns("CodeCategorie").Visible = False
        cmbCategorie.Splits(0).DisplayColumns("LibelleCategorie").Width = 10
        cmbCategorie.ExtendRightColumn = True

        'chargement des Laboratoires
        StrSQL1 = "SELECT DISTINCT CodeLabo,NomLabo FROM LABORATOIRE WHERE SupprimeLabo=0 ORDER BY NomLabo ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "LABORATOIRE")
        cmbLaboratoire.DataSource = dsArticle.Tables("LABORATOIRE")
        cmbLaboratoire.ValueMember = "CodeLabo"
        cmbLaboratoire.DisplayMember = "NomLabo"
        cmbLaboratoire.ColumnHeaders = False
        cmbLaboratoire.Splits(0).DisplayColumns("CodeLabo").Visible = False
        cmbLaboratoire.Splits(0).DisplayColumns("NomLabo").Width = 10
        cmbLaboratoire.ExtendRightColumn = True

        'chargement des Situations
        StrSQL1 = "SELECT DISTINCT CodeSituationArticle,LibelleSituationArticle FROM SITUATION_ARTICLE ORDER BY LibelleSituationArticle ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "SITUATION_ARTICLE")
        cmbSituation.DataSource = dsArticle.Tables("SITUATION_ARTICLE")
        cmbSituation.ValueMember = "CodeSituationArticle"
        cmbSituation.DisplayMember = "LibelleSituationArticle"
        cmbSituation.ColumnHeaders = False
        cmbSituation.Splits(0).DisplayColumns("CodeSituationArticle").Visible = False
        cmbSituation.Splits(0).DisplayColumns("LibelleSituationArticle").Width = 10
        cmbSituation.ExtendRightColumn = True

        'chargement des Valeurs Tableau
        StrSQL1 = "SELECT DISTINCT LibelleTableau FROM TABLEAU ORDER BY LibelleTableau ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "TABELAU")
        cmbTableau.DataSource = dsArticle.Tables("TABELAU")
        cmbTableau.ValueMember = "LibelleTableau"
        cmbTableau.ColumnHeaders = False
        cmbTableau.Splits(0).DisplayColumns("LibelleTableau").Width = 10
        cmbTableau.ExtendRightColumn = True

        'chargement des Categories Cnam
        StrSQL1 = "SELECT DISTINCT CodeCategorie,LibelleCategorie FROM CATEGORIE_CNAM ORDER BY LibelleCategorie ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "CATEGORIE_CNAM")
        cmbCategorieCnam.DataSource = dsArticle.Tables("CATEGORIE_CNAM")
        cmbCategorieCnam.ValueMember = "CodeCategorie"
        cmbCategorieCnam.DisplayMember = "LibelleCategorie"
        cmbCategorieCnam.ColumnHeaders = False
        cmbCategorieCnam.Splits(0).DisplayColumns("CodeCategorie").Visible = False
        cmbCategorieCnam.Splits(0).DisplayColumns("LibelleCategorie").Width = 10
        cmbCategorieCnam.ExtendRightColumn = True

        'chargement des Préparations
        StrSQL1 = "SELECT DISTINCT CodeTypePreparation,LibelleTypePreparation FROM TYPE_PREPARATION ORDER BY LibelleTypePreparation ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "TYPE_PREPARATION")
        cmbPreparation.DataSource = dsArticle.Tables("TYPE_PREPARATION")
        cmbPreparation.ValueMember = "CodeTypePreparation"
        cmbPreparation.DisplayMember = "LibelleTypePreparation"
        cmbPreparation.ColumnHeaders = False
        cmbPreparation.Splits(0).DisplayColumns("CodeTypePreparation").Visible = False
        cmbPreparation.Splits(0).DisplayColumns("LibelleTypePreparation").Width = 10
        cmbPreparation.ExtendRightColumn = True

        'chargement des DCIs
        StrSQL1 = "SELECT DISTINCT CodeDCI,LibelleDCI FROM DCI as DCI1 WHERE SupprimeDCI=0 ORDER BY LibelleDCI ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "DCI1")
        cmbDCI1.DataSource = dsArticle.Tables("DCI1")
        cmbDCI1.ValueMember = "CodeDCI"
        cmbDCI1.DisplayMember = "LibelleDCI"
        cmbDCI1.ColumnHeaders = False
        cmbDCI1.Splits(0).DisplayColumns("CodeDCI").Visible = False
        cmbDCI1.Splits(0).DisplayColumns("LibelleDCI").Width = 10
        cmbDCI1.ExtendRightColumn = True

        StrSQL1 = "SELECT DISTINCT CodeDCI,LibelleDCI FROM DCI as DCI2 WHERE SupprimeDCI=0 ORDER BY LibelleDCI ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "DCI2")
        cmbDCI2.DataSource = dsArticle.Tables("DCI2")
        cmbDCI2.ValueMember = "CodeDCI"
        cmbDCI2.DisplayMember = "LibelleDCI"
        cmbDCI2.ColumnHeaders = False
        cmbDCI2.Splits(0).DisplayColumns("CodeDCI").Visible = False
        cmbDCI2.Splits(0).DisplayColumns("LibelleDCI").Width = 10
        cmbDCI2.ExtendRightColumn = True

        StrSQL1 = "SELECT DISTINCT CodeDCI,LibelleDCI FROM DCI as DCI3 WHERE SupprimeDCI=0 ORDER BY LibelleDCI ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "DCI3")
        cmbDCI3.DataSource = dsArticle.Tables("DCI3")
        cmbDCI3.ValueMember = "CodeDCI"
        cmbDCI3.DisplayMember = "LibelleDCI"
        cmbDCI3.ColumnHeaders = False
        cmbDCI3.Splits(0).DisplayColumns("CodeDCI").Visible = False
        cmbDCI3.Splits(0).DisplayColumns("LibelleDCI").Width = 10
        cmbDCI3.ExtendRightColumn = True

        StrSQL1 = "SELECT DISTINCT CONVERT(VARCHAR(MAX), CodeTVA) AS CodeTVA, CONVERT(VARCHAR(MAX), ValeurTVA) AS ValeurTVA FROM TVA WHERE CONVERT(VARCHAR(MAX), ValeurTVA) <> '0' UNION SELECT '0', '0' "
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "TVA")
        CmbTVA.DataSource = dsArticle.Tables("TVA")
        CmbTVA.ValueMember = "CodeTVA"
        CmbTVA.DisplayMember = "ValeurTVA"
        CmbTVA.ColumnHeaders = False
        CmbTVA.Splits(0).DisplayColumns("CodeTVA").Visible = False
        CmbTVA.Splits(0).DisplayColumns("ValeurTVA").Width = 10
        CmbTVA.ExtendRightColumn = True
        CmbTVA.SelectedValue = 0

        'initialiser les prix a 0
        tPrixAchatHT.Value = "0.000"

        CmbTVA.SelectedValue = 0

        tPrixAchatTTC.Value = "0.000"

        tMarge.Value = "0"

        tPrixVenteHT.Value = "0.000"
        tHR.Value = "0"
        tPrixVenteTTC.Value = "0.000"

        tDesignation.CharacterCasing = CharacterCasing.Upper
        tCodeArticle.CharacterCasing = CharacterCasing.Upper
        cmbForme.CharacterCasing = CharacterCasing.Upper
        cmbCategorie.CharacterCasing = CharacterCasing.Upper

        If ajoutmodif = "A" Or ajoutmodif = "DUPLIQUER" Then
            tQuantiteUnitaire.Value = "1"
            cmbSituation.Text = "ACTIVITE"
        End If

        If ajoutmodif = "A" Then
            StrSQL1 = " SELECT TOP 0 * FROM ARTICLE"
            'bMagistrale.Enabled = True
        Else
            StrSQL1 =
                        "SELECT " + _
                        "   [CodeArticle] " + _
                        "   ,[CodeABarre]" + _
                        "   ,[Designation]" + _
                        "   ,[Dosage]" + _
                        "   ,[LibelleTableau]" + _
                        "   ,[QuantiteUnitaire]" + _
                        "   ,[ContenanceArticle]" + _
                        "   ,[PrixAchatHT]" + _
                        "   ,[PrixAchatTTC]" + _
                        "   ,[PrixVenteHT]" + _
                        "   ,[PrixVenteTTC]" + _
                        "   ,CASE WHEN [TVA].[CodeTVA] IS NULL THEN '0' ELSE [TVA].[CodeTVA] END AS TVA" + _
                        "   ,[Marge]" + _
                        "   ,[Exonorertva]" + _
                        "   ,[HR]" + _
                        "   ,[CodePCT]" + _
                        "   ,[CodeCategorieCNAM]" + _
                        "   ,[TarifDeReference]" + _
                        "   ,[AccordPrealable]" + _
                        "   ,[PriseEnCharge]" + _
                        "   ,[SansCodeBarre]" + _
                        "   ,[SansVignette]" + _
                        "   ,[StockAlerte]" + _
                        "   ,[QteACommander]" + _
                        "   ,[DateAlerte]" + _
                        "   ,[DateDerniereCommande]" + _
                        "   ,[QuantiteDernierCommande]" + _
                        "   ,[DateInitiale]" + _
                        "   ,[StockInitial]" + _
                        "   ,[CodeForme]" + _
                        "   ,[CodeCategorie]" + _
                        "   ,[CodeLabo]" + _
                        "   ,[Rayon]" + _
                        "   ,[CodeSituation]" + _
                        "   ,[CodeOperateur]" + _
                        "   ,[CodeGroupement]" + _
                        "   ,[CodeTypePreparation]" + _
                        "   ,[Section]" + _
                        "   ,[DCI1]" + _
                        "   ,[DCI2]" + _
                        "   ,[DCI3]" + _
                        "   ,[Supprime]" + _
                        "   ,[FemmeEnceinte]" + _
                        "   ,[StockArticle]" + _
                        "   ,[NumeroCirculaire]" + _
                        "FROM " + _
                        "   ARTICLE " + _
                        "   LEFT OUTER JOIN TVA ON ARTICLE.TVA = TVA.VALEURTVA " + _
                        "WHERE " + _
                        "   CodeArticle = " + Quote(CodeArticle)
        End If

        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "Article")
        cbArticle = New SqlCommandBuilder(daArticle)

        If ajoutmodif = "M" Then

            lNomArticle.Text = "Fiche ARTICLE : " + DesignationArticle
            tCodeArticle.Value = dsArticle.Tables("Article").Rows(0)("CodeABarre")
            tDesignation.Value = dsArticle.Tables("Article").Rows(0)("Designation")

            tCodeArticleGL.Value = dsArticle.Tables("Article").Rows(0)("CodeABarre")
            tDesignationGL.Value = dsArticle.Tables("Article").Rows(0)("Designation")

            cmbForme.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeForme")
            cmbCategorie.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeCategorie")

            cmbLaboratoire.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeLabo")
            cmbTableau.Text = dsArticle.Tables("Article").Rows(0)("LibelleTableau")

            tDosage.Value = dsArticle.Tables("Article").Rows(0)("Dosage")
            tRayon.Value = dsArticle.Tables("Article").Rows(0)("Rayon")
            tNumCirculaire.Value = dsArticle.Tables("Article").Rows(0)("NumeroCirculaire")

            tCodePCT.Value = dsArticle.Tables("Article").Rows(0)("CodePCT")
            tCodePCTGL.Value = dsArticle.Tables("Article").Rows(0)("CodePCT")

            cmbCategorieCnam.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeCategorieCNAM")
            cmbPreparation.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeTypePreparation")

            chkAccordPrealable.Checked = dsArticle.Tables("Article").Rows(0)("AccordPrealable")
            chkPriseEnCharge.Checked = dsArticle.Tables("Article").Rows(0)("PriseEnCharge")

            tTarifReference.Value = dsArticle.Tables("Article").Rows(0)("TarifDeReference")
            cmbSituation.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeSituation")

            lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsArticle.Tables("Article").Rows(0)("CodeOperateur"))
            chkSansVignette.Checked = dsArticle.Tables("Article").Rows(0)("SansVignette")

            chkSansCodeAbarre.Checked = dsArticle.Tables("Article").Rows(0)("SansCodeBarre")
            tQuantiteUnitaire.Value = dsArticle.Tables("Article").Rows(0)("QuantiteUnitaire")

            tContenance.Value = dsArticle.Tables("Article").Rows(0)("ContenanceArticle")
            tstockAlert.Value = dsArticle.Tables("Article").Rows(0)("StockAlerte")

            tQuantiteACommander.Value = dsArticle.Tables("Article").Rows(0)("QteACommander")
            'tStockInventaire.Value = dsArticle.Tables("Article").Rows(0)("Stockinitial")

            'tDateInventaire.Value = dsArticle.Tables("Article").Rows(0)("Dateinitiale")
            lDateDernierCommande.Text = dsArticle.Tables("Article").Rows(0)("DateDerniereCommande").ToString

            lQuantiteDernierCommande.Text = dsArticle.Tables("Article").Rows(0)("QuantiteDernierCommande").ToString
            tPrixAchatHT.Value = dsArticle.Tables("Article").Rows(0)("PrixAchatHT")

            CmbTVA.SelectedValue = dsArticle.Tables("Article").Rows(0)("TVA")
            tPrixAchatTTC.Value = dsArticle.Tables("Article").Rows(0)("PrixAchatTTC")

            tHR.Value = dsArticle.Tables("Article").Rows(0)("HR")
            tMarge.Value = dsArticle.Tables("Article").Rows(0)("Marge")
            chkExonere.Checked = dsArticle.Tables("Article").Rows(0)("Exonorertva")

            tPrixVenteHT.Value = dsArticle.Tables("Article").Rows(0)("PrixVenteHT")

            tPrixVenteTTC.Value = dsArticle.Tables("Article").Rows(0)("PrixVenteTTC")
            tSection.Value = dsArticle.Tables("ARTICLE").Rows(0)("Section")
            tDateAlerte.Value = dsArticle.Tables("ARTICLE").Rows(0)("DateAlerte")

            cmbDCI1.SelectedValue = dsArticle.Tables("ARTICLE").Rows(0)("DCI1")
            cmbDCI2.SelectedValue = dsArticle.Tables("ARTICLE").Rows(0)("DCI2")
            cmbDCI3.SelectedValue = dsArticle.Tables("ARTICLE").Rows(0)("DCI3")

            chbFemmeEnceinte.Checked = dsArticle.Tables("ARTICLE").Rows(0)("FemmeEnceinte")

            lStock.Text = (Int(StockArticle)).ToString   '/ CDbl(tQuantiteUnitaire.Text)
            Dim StcokUnite As Integer = 0
            Try
                StcokUnite = Int(Int(lStock.Text) / CDbl(tQuantiteUnitaire.Text))
            Catch ex As Exception

            End Try

            If Int(lStock.Text) < 0 Then
                StcokUnite += 1
            End If

            If cmbCategorie.Text = "PRODUITS CHIMIQUES" Then
                lUnite.Text = (StockArticle - (StcokUnite * tQuantiteUnitaire.Text)).ToString + " unités "
                lStock.Text = StcokUnite.ToString
                lUnite.Visible = True
            ElseIf (cmbCategorie.Text = "PREPARATION" And cmbPreparation.Text = "PHARMACEUTIQUE") Or cmbCategorie.Text = "PRODUITS CHIMIQUES" Then
                'lStock.Text = Math.Round(CDbl(lStock.Text) * CDbl(tQuantiteUnitaire.Text), 3).ToString
                lUnite.Text = StcokUnite.ToString + "    " + (StockArticle - (StcokUnite * tQuantiteUnitaire.Text)).ToString + " unités "
                lUnite.Visible = True
            Else
                lUnite.Visible = False
            End If

            'récupération de dernier date d'achat et dernier fournisseur
            If (dsArticle1.Tables.IndexOf("DATE_FOURNISSEUR_ACHAT") > -1) Then
                dsArticle1.Tables("DATE_FOURNISSEUR_ACHAT").Clear()
            End If

            Dim CMD1 As New SqlCommand
            CMD1.CommandText = "SELECT ACHAT_DETAILS.NumeroAchat,Date,FOURNISSEUR.NomFournisseur " + _
                              "from ACHAT " + _
                              "LEFT OUTER JOIN ACHAT_DETAILS ON ACHAT.NumeroAchat=ACHAT_DETAILS.NumeroAchat " + _
                              "LEFT OUTER JOIN FOURNISSEUR ON ACHAT.CodeFournisseur=FOURNISSEUR.CodeFournisseur " + _
                              "WHERE CodeArticle ='" + CodeArticle + "' ORDER BY Date desc "

            CMD1.Connection = ConnectionServeur
            daArticle1 = New SqlDataAdapter(CMD1)
            daArticle1.Fill(dsArticle1, "DATE_FOURNISSEUR_ACHAT")
            cbArticle1 = New SqlCommandBuilder(daArticle1)

            If dsArticle1.Tables("DATE_FOURNISSEUR_ACHAT").Rows.Count <> 0 Then
                lDateDernierAchat.Text = dsArticle1.Tables("DATE_FOURNISSEUR_ACHAT").Rows(0).Item("Date")
                lDernierFournisseur.Text = dsArticle1.Tables("DATE_FOURNISSEUR_ACHAT").Rows(0).Item("NomFournisseur").ToString
            Else
                lDateDernierAchat.Text = "-"
                lDernierFournisseur.Text = "-"
            End If

            'récupération de dernier date et stock d'inventaire 

            If (dsArticle1.Tables.IndexOf("DATE_INVENTAIRE_STOCK") > -1) Then
                dsArticle1.Tables("DATE_INVENTAIRE_STOCK").Clear()
            End If

            CMD1.CommandText = "SELECT INVENTAIRE.NumeroInventaire ,Date " + _
                              "FROM INVENTAIRE " + _
                              "LEFT OUTER JOIN INVENTAIRE_DETAILS ON INVENTAIRE.NumeroInventaire=INVENTAIRE_DETAILS.NumeroInventaire " + _
                              "WHERE INVENTAIRE_DETAILS .CodeArticle='" + CodeArticle + "' order by Date desc "
            CMD1.Connection = ConnectionServeur
            daArticle1 = New SqlDataAdapter(CMD1)
            daArticle1.Fill(dsArticle1, "DATE_INVENTAIRE_STOCK")
            cbArticle1 = New SqlCommandBuilder(daArticle1)

            If dsArticle1.Tables("DATE_INVENTAIRE_STOCK").Rows.Count <> 0 Then

                lDateInventaire.Text = dsArticle1.Tables("DATE_INVENTAIRE_STOCK").Rows(0).Item("Date")

                CMD1.CommandText = "SELECT SUM(StockActuel)  " + _
                                   "FROM INVENTAIRE_DETAILS  " + _
                                   "WHERE CodeArticle ='" + CodeArticle + "' AND NumeroInventaire ='" + dsArticle1.Tables("DATE_INVENTAIRE_STOCK").Rows(0).Item("NumeroInventaire") + "' "

                CMD1.Connection = ConnectionServeur
                'CMD1.CommandText = StrSQL
                Try
                    lStockInventaire.Text = CMD1.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
            End If
        Else
            chkExonere.Enabled = True
        End If

        If ajoutmodif = "DUPLIQUER" Then

            CodeArticle = ""

            lNomArticle.Text = "Fiche ARTICLE : " + DesignationArticle
            tDesignation.Value = dsArticle.Tables("Article").Rows(0)("Designation")
            tDesignationGL.Value = dsArticle.Tables("Article").Rows(0)("Designation")

            cmbForme.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeForme")
            cmbCategorie.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeCategorie")

            tDosage.Value = ""

            cmbLaboratoire.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeLabo")
            cmbTableau.Text = dsArticle.Tables("Article").Rows(0)("LibelleTableau")

            tRayon.Value = dsArticle.Tables("Article").Rows(0)("Rayon")
            tNumCirculaire.Value = dsArticle.Tables("Article").Rows(0)("NumeroCirculaire")

            cmbPreparation.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeTypePreparation")

            cmbSituation.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeSituation")

            lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsArticle.Tables("Article").Rows(0)("CodeOperateur"))

            tPrixAchatHT.Value = dsArticle.Tables("Article").Rows(0)("PrixAchatHT")

            CmbTVA.SelectedValue = dsArticle.Tables("Article").Rows(0)("TVA")
            tPrixAchatTTC.Value = dsArticle.Tables("Article").Rows(0)("PrixAchatTTC")

            tMarge.Value = dsArticle.Tables("Article").Rows(0)("Marge")
            chkExonere.Checked = dsArticle.Tables("Article").Rows(0)("Exonorertva")

            tPrixVenteHT.Value = dsArticle.Tables("Article").Rows(0)("PrixVenteHT")
            tHR.Value = dsArticle.Tables("Article").Rows(0)("HR")

            tPrixVenteTTC.Value = dsArticle.Tables("Article").Rows(0)("PrixVenteTTC")
            tSection.Value = dsArticle.Tables("ARTICLE").Rows(0)("Section")

            cmbDCI1.SelectedValue = dsArticle.Tables("ARTICLE").Rows(0)("DCI1")
            cmbDCI2.SelectedValue = dsArticle.Tables("ARTICLE").Rows(0)("DCI2")
            cmbDCI3.SelectedValue = dsArticle.Tables("ARTICLE").Rows(0)("DCI3")

            tDosage.Value = dsArticle.Tables("ARTICLE").Rows(0)("Dosage")
            tQuantiteUnitaire.Value = dsArticle.Tables("ARTICLE").Rows(0)("QuantiteUnitaire")
            tContenance.Value = dsArticle.Tables("ARTICLE").Rows(0)("ContenanceArticle")
            tstockAlert.Value = dsArticle.Tables("ARTICLE").Rows(0)("StockAlerte")
            tQuantiteACommander.Value = dsArticle.Tables("ARTICLE").Rows(0)("QteACommander")

            chbFemmeEnceinte.Checked = dsArticle.Tables("ARTICLE").Rows(0)("FemmeEnceinte")

            If (cmbCategorie.Text = "PREPARATION" And cmbPreparation.Text = "PHARMACEUTIQUE") Or cmbCategorie.Text = "PRODUIT CHIMIQUE" Then
                'lStock.Text = Math.Round(CDbl(lStock.Text) * CDbl(tQuantiteUnitaire.Text), 3).ToString
                lUnite.Visible = True
            Else
                lUnite.Visible = False
            End If

            'bMagistrale.Enabled = False

        End If


        dtpDate.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDate.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        'cmbLaboratoire.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        cmbCategorieCnam.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList

        AfficherLotsArticle()

        Tab.TabPages(0).Show()
        tCodeArticle.Focus()

        If ajoutmodif = "A" Or ajoutmodif = "DUPLIQUER" Then
            C1DockingTabPage3.Enabled = False
            bStatistiqueArticle.Enabled = False

            bChangementPrix.Enabled = False

            tPrixAchatHT.Enabled = True
            CmbTVA.Enabled = True
            tPrixAchatTTC.Enabled = True
            tMarge.Enabled = True
            tPrixVenteHT.Enabled = True
            tHR.Enabled = True
            tPrixVenteTTC.Enabled = True

        Else
            bChangementPrix.Enabled = True

            tPrixAchatHT.Enabled = False
            CmbTVA.Enabled = False
            tPrixAchatTTC.Enabled = False
            tMarge.Enabled = False
            tPrixVenteHT.Enabled = False
            tHR.Enabled = False
            tPrixVenteTTC.Enabled = False

        End If

        'Test pour desactiver la Qte Unitaire

        'Test si la catégorie du produit
        'si cat = 9 --> chimique 
        'donc on va desactiver la zone txt Qte Unit

        If cmbCategorie.SelectedValue = 9 Then

            tQuantiteUnitaire.Enabled = False

            tDernierPrixAchat.Visible = False
            dtpDerniereDateAchat.Visible = False
            lQuantiteUnitaire.Visible = False
            lDerniereDateAchat.Visible = False

        End If

        'Test si il y a un achat 
        'donc on va desactiver la zone txt Qte Unit
        'on affiche la date et le prix du dernier achat

        cmdChimique.Connection = ConnectionServeur
        cmdChimique.CommandText = "  select top 1 [date],PrixAchatHT  from ACHAT_DETAILS " & _
                                  "  left join ACHAT on ACHAT_DETAILS .NumeroAchat = ACHAT.NumeroAchat  " & _
                                  "  WHERE CodeArticle ='" & CodeArticle & "'" & _
                                  "  order by ACHAT_DETAILS.NumeroAchat  desc "

        daChimique = New SqlDataAdapter(cmdChimique)
        daChimique.Fill(dsChimique, "CHIMIQUE")
        cbChimique = New SqlCommandBuilder(daChimique)

        If dsChimique.Tables("CHIMIQUE").Rows.Count <> 0 Then

            dtpDerniereDateAchat.Value = dsChimique.Tables("CHIMIQUE").Rows(0).Item("Date")
            tDernierPrixAchat.Value = dsChimique.Tables("CHIMIQUE").Rows(0).Item("PrixAchatHT")

            tDernierPrixAchat.Visible = True
            dtpDerniereDateAchat.Visible = True
            lQuantiteUnitaire.Visible = True
            lDerniereDateAchat.Visible = True

            tQuantiteUnitaire.Enabled = False

        Else

            'tQuantiteUnitaire.Enabled = False

            tDernierPrixAchat.Visible = False
            dtpDerniereDateAchat.Visible = False
            lQuantiteUnitaire.Visible = False
            lDerniereDateAchat.Visible = False

        End If

        'Le groupe d'ajout de numéro de lot est invisible
        'grbAjoutLot.Visible = False

        AfficherAchats()
    End Sub

    Public Sub AfficherAchats()

        cmdLotsArticle.CommandText = "SELECT DISTINCT " + _
                                    "   ACHAT.NumeroAchat, " + _
                                    "   ACHAT.Date,  " + _
                                    "   FOURNISSEUR.NomFournisseur, " + _
                                    "   ACHAT_DETAILS.Qte, " + _
                                    "   [NumeroBL/Facture], " + _
                                    "   ACHAT_DETAILS.PrixAchatHT, " + _
                                    "   ACHAT.TotalHT, " + _
                                    "   ACHAT.TotalRemise, " + _
                                    "   ACHAT.TVA, " + _
                                    "   ACHAT.TotalTTC " + _
                                    "FROM " + _
                                    "   ACHAT " + _
                                    "   INNER JOIN FOURNISSEUR ON FOURNISSEUR.CodeFournisseur = ACHAT.CodeFournisseur " + _
                                    "   INNER JOIN ACHAT_DETAILS ON ACHAT.NumeroAchat = ACHAT_DETAILS.NumeroAchat AND ACHAT_DETAILS.CodeArticle = " + Quote(CodeArticle) + " " + _
                                    "ORDER BY " + _
                                    "	DATE DESC"

        cmdLotsArticle.Connection = ConnectionServeur
        daLotsArticle = New SqlDataAdapter(cmdLotsArticle)
        daLotsArticle.Fill(dsArticle, "ACHAT")
        cbLotsArticle = New SqlCommandBuilder(daLotsArticle)

        With gAchat
            .Columns.Clear()
            .DataSource = dsArticle
            .DataMember = "ACHAT"
            .Rebind(False)
            .Columns("NumeroAchat").Caption = "Numero Achat"
            .Columns("Date").Caption = "Date"
            .Columns("NomFournisseur").Caption = "Nom Fournisseur"
            .Columns("TotalHT").Caption = "Total HT"
            .Columns("TotalRemise").Caption = "Total Remise"
            .Columns("TVA").Caption = "TVA"
            .Columns("TotalTTC").Caption = "Total TTC"
            .Columns("PrixAchatHT").Caption = "Prix Achat HT"


            'Centrer tous les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            'Centrer tous les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("NumeroAchat").Width = 120
            .Splits(0).DisplayColumns("Date").Width = 120
            .Splits(0).DisplayColumns("NomFournisseur").Width = 120
            .Splits(0).DisplayColumns("TotalHT").Width = 100
            .Splits(0).DisplayColumns("TotalRemise").Width = 100
            .Splits(0).DisplayColumns("TVA").Width = 80
            .Splits(0).DisplayColumns("TotalTTC").Width = 100
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 100
            .Splits(0).DisplayColumns("Qte").Width = 60


            ''.Splits(0).DisplayColumns("TotalTTC").
            .Columns("TotalTTC").NumberFormat = "#,###0.000"
            .Columns("TotalRemise").NumberFormat = "#,###0.000"
            .Columns("TotalTTC").NumberFormat = "#,###0.000"
            .Columns("TVA").NumberFormat = "#,###0.000"

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gAchat)
        End With

    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        bConfirmer.Focus()
        Dim StrSQL1 As String = ""

        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        Dim dr As DataRow
        Dim StrSQL As String = ""

        Dim I As Integer = 0
        Dim ValeurFinal As Integer = 0

        TestCodeExistant()

        If (tHR.Value > 0 And cmbTableau.Text = "") Then
            MessageBox.Show("Veuillez remplir le champ Tableau.", "", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbTableau.Focus()
            Exit Sub
        End If

        'If chkSansCodeAbarre.Checked And Len(tCodeArticle.Text) > 10 Then
        '    MsgBox("Le code à barres ne doit pas dépasser 10 chiffres.", MsgBoxStyle.Information, "Erreur")
        '    tCodeArticle.Focus()
        '    Exit Sub
        'End If

        If tCodeArticle.Text = "" Then
            MsgBox("Veuillez saisir le code de l'article !", MsgBoxStyle.Critical, "Erreur")
            tCodeArticle.Focus()
            Exit Sub
        End If

        If tDesignation.Text = "" Then
            MsgBox("Veuillez saisir la désignation de l'article !", MsgBoxStyle.Critical, "Erreur")
            tDesignation.Focus()
            Exit Sub
        End If

        'If cmbCategorie.Text = "" Then
        '    MsgBox("Veuillez saisir la catégorie de l'article !", MsgBoxStyle.Critical, "Erreur")
        '    cmbCategorie.Focus()
        '    Exit Sub
        'End If

        If CodeExiste = True Then
            MsgBox("Code article existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
            tCodeArticle.Focus()
            Exit Sub
        End If

        If CodePCTExist = True Then
            MsgBox("Code PCT existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
            tCodePCT.Focus()
            Exit Sub
        End If

        For I = 0 To dsLotsArticle.Tables("LOT_ARTICLE").Rows.Count - 1
            ValeurFinal = ValeurFinal + dsLotsArticle.Tables("LOT_ARTICLE").Rows(I).Item("QteLotArticle")
        Next

        If ValeurDuStock <> ValeurFinal Then
            MsgBox("La somme des quantité des stocks n'est pas valide !  Valeur initial est : " + ValeurDuStock.ToString, MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        ''Controle du changement de la quantité unitaire 
        ''Dim stock As Integer = 0
        'Dim AncienneQteUnit As Integer = 0
        'Try
        '    AncienneQteUnit = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticle)
        'Catch ex As Exception
        '    AncienneQteUnit = 1
        'End Try

        '' stock = CalculeStock(CodeArticle)
        'If IsNumeric(tQuantiteUnitaire.Value) = False Then
        '    tQuantiteUnitaire.Value = 1
        'End If
        'If AncienneQteUnit <> tQuantiteUnitaire.Value Then
        '    Dim reponse As MsgBoxResult
        '    reponse = MsgBox("Ete-vous sûre de changer la Quantité unitaire", MsgBoxStyle.YesNo, "Erreur")
        '    If reponse = MsgBoxResult.No Then
        '        tQuantiteUnitaire.Value = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticle)
        '        tQuantiteUnitaire.Focus()
        '        Exit Sub
        '    End If
        'End If


        '------------------------------ demande du mot de passe
        Dim myMotDePasse As New fMotDePasse

        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If
        '------------------------------------------------------


        EstConfirmer = True


        '-----------------------------------------------------
        Dim stock As Integer = 0
        stock = CalculeStock(CodeArticle)

        'If stock <> 0 Then
        '    Dim AncienQuantiteUnitaire As Integer = 0
        '    AncienQuantiteUnitaire = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticle)
        '    If IsDBNull(tQuantiteUnitaire.Value) = False Then
        '        If Convert.ToInt32(tQuantiteUnitaire.Value) <> AncienQuantiteUnitaire Then
        '            MsgBox("Stock différent a zéro, vous ne pouvez pas modifier la quantite unitaire !" + Chr(13) + "Utiliser l'entré et la sortie diverse ", MsgBoxStyle.Critical, "Erreur")
        '            tQuantiteUnitaire.Focus()
        '            Exit Sub
        '        End If
        '    End If
        'End If

        If ajoutmodif = "A" Or ajoutmodif = "DUPLIQUER" Then

            StrSQL1 = " SELECT TOP(0) * FROM Article "
            cmdArticle.Connection = ConnectionServeur
            cmdArticle.CommandText = StrSQL1
            daArticle = New SqlDataAdapter(cmdArticle)
            daArticle.Fill(dsArticle, "Article")
            cbArticle = New SqlCommandBuilder(daArticle)

            With dsArticle
                dr = .Tables("Article").NewRow

                StrSQL = " SELECT MAX(CASE WHEN ISnumeric(CodeArticle)=1 THEN CONVERT (FLOAT , CodeArticle ) ELSE 0 END) FROM [ARTICLE] WHERE CASE WHEN ISnumeric(CodeArticle)=1 THEN CONVERT (FLOAT , CodeArticle ) ELSE 0 END<99999999999"
                cmdRecupereNum.Connection = ConnectionServeur
                cmdRecupereNum.CommandText = StrSQL
                Try
                    dr.Item("CodeArticle") = cmdRecupereNum.ExecuteScalar() + 1
                    CodeArticle = dr.Item("CodeArticle")
                Catch ex As Exception
                    dr.Item("CodeArticle") = 1
                    Console.WriteLine(ex.Message)
                End Try

                ' dr.Item("CodeArticle") = tCodeArticle.Text
                dr.Item("CodeABarre") = tCodeArticle.Text
                dr.Item("Designation") = tDesignation.Text

                If cmbForme.Text <> "" Then
                    dr.Item("codeForme") = cmbForme.SelectedValue
                End If
                If Not (cmbCategorie.SelectedValue) Is Nothing Then
                    dr.Item("CodeCategorie") = cmbCategorie.SelectedValue
                End If
                If cmbPreparation.Text <> "" And cmbCategorie.Text = "PREPARATION" Then
                    dr.Item("CodeTypePreparation") = cmbPreparation.SelectedValue
                Else
                    dr.Item("CodeTypePreparation") = DBNull.Value
                End If

                If cmbLaboratoire.Text <> "" Then
                    dr.Item("CodeLabo") = cmbLaboratoire.SelectedValue
                End If

                dr.Item("LibelleTableau") = cmbTableau.Text

                dr.Item("Dosage") = tDosage.Text


                dr.Item("Rayon") = tRayon.Text
                dr.Item("NumeroCirculaire") = tNumCirculaire.Text

                dr.Item("CodePCT") = tCodePCT.Text
                If IsNothing(cmbCategorieCnam.SelectedValue) Then
                    dr.Item("CodeCategorieCNAM") = DBNull.Value
                Else
                    dr.Item("CodeCategorieCNAM") = cmbCategorieCnam.SelectedValue
                End If

                dr.Item("AccordPrealable") = chkAccordPrealable.Checked
                dr.Item("PriseEnCharge") = chkPriseEnCharge.Checked
                If tTarifReference.Text <> "" Then
                    dr.Item("TarifDeReference") = tTarifReference.Text
                Else
                    dr.Item("TarifDeReference") = 0.0
                End If

                If cmbSituation.Text <> "" Then
                    dr.Item("CodeSituation") = cmbSituation.SelectedValue
                End If

                dr.Item("SansVignette") = chkSansVignette.Checked

                dr.Item("SansCodeBarre") = chkSansCodeAbarre.Checked
                If tQuantiteUnitaire.Text <> "" Then
                    dr.Item("QuantiteUnitaire") = tQuantiteUnitaire.Text
                End If
                If tContenance.Text <> "" Then
                    dr.Item("ContenanceArticle") = tContenance.Text
                End If
                If tstockAlert.Text <> "" Then
                    dr.Item("StockAlerte") = tstockAlert.Text
                End If
                If tQuantiteACommander.Text <> "" Then
                    dr.Item("QteACommander") = tQuantiteACommander.Text
                End If
                'If tStockInventaire.Text <> "" Then
                '    dr.Item("StockInitial") = tStockInventaire.Text
                'End If
                'If tDateInventaire.Text <> "" And tDateInventaire.Text <> "01/01/1900" Then
                '    dr.Item("Dateinitiale") = tDateInventaire.Text
                'End If
                dr.Item("PrixAchatHT") = CDec(tPrixAchatHT.Text)

                dr.Item("TVA") = CmbTVA.Text
                dr.Item("PrixAchatTTC") = CDec(tPrixAchatTTC.Text)

                dr.Item("Marge") = tMarge.Text
                dr.Item("Exonorertva") = chkExonere.Checked


                dr.Item("PrixVenteHT") = CDec(tPrixVenteHT.Text)
                If tHR.Text = "" Then
                    tHR.Text = 0
                End If
                dr.Item("HR") = CDec(tHR.Text)

                dr.Item("PrixVenteTTC") = CDec(tPrixVenteTTC.Text)
                dr.Item("Section") = tSection.Text
                dr.Item("CodeOperateur") = CodeOperateur

                If cmbDCI1.Text <> "" Then
                    dr.Item("DCI1") = cmbDCI1.SelectedValue
                Else
                    dr.Item("DCI1") = DBNull.Value
                End If
                If cmbDCI2.Text <> "" Then
                    dr.Item("DCI2") = cmbDCI2.SelectedValue
                Else
                    dr.Item("DCI2") = DBNull.Value
                End If
                If cmbDCI3.Text <> "" Then
                    dr.Item("DCI3") = cmbDCI3.SelectedValue
                Else
                    dr.Item("DCI3") = DBNull.Value
                End If

                dr.Item("FemmeEnceinte") = chbFemmeEnceinte.Checked
                dr.Item("DateInitiale") = "01/01/1900"



                .Tables("Article").Rows.Add(dr)
            End With

        ElseIf ajoutmodif = "M" Then

            If (dsArticle.Tables.IndexOf("Article") > -1) Then
                dsArticle.Tables("Article").Clear()
            End If

            StrSQL1 = " SELECT * FROM Article WHERE CodeArticle = " + Quote(CodeArticle)

            cmdArticle.Connection = ConnectionServeur
            cmdArticle.CommandText = StrSQL1
            daArticle = New SqlDataAdapter(cmdArticle)
            daArticle.Fill(dsArticle, "Article")
            cbArticle = New SqlCommandBuilder(daArticle)

            With dsArticle.Tables("Article")
                dr = .Rows(0)

                dr.Item("CodeABarre") = tCodeArticle.Text
                dr.Item("Designation") = tDesignation.Text

                If cmbForme.Text <> "" Then
                    dr.Item("codeForme") = cmbForme.SelectedValue
                End If
                If cmbCategorie.Text <> "" Then
                    dr.Item("CodeCategorie") = cmbCategorie.SelectedValue
                End If
                If cmbPreparation.Text <> "" And cmbCategorie.Text = "PREPARATION" Then
                    dr.Item("CodeTypePreparation") = cmbPreparation.SelectedValue
                Else
                    dr.Item("CodeTypePreparation") = DBNull.Value
                End If

                If cmbLaboratoire.Text <> "" Then
                    dr.Item("CodeLabo") = cmbLaboratoire.SelectedValue
                Else
                    dr.Item("CodeLabo") = 0
                End If

                dr.Item("LibelleTableau") = cmbTableau.Text

                dr.Item("Dosage") = tDosage.Text


                dr.Item("Rayon") = tRayon.Text
                dr.Item("NumeroCirculaire") = tNumCirculaire.Text

                dr.Item("CodePCT") = tCodePCT.Text

                If IsNothing(cmbCategorieCnam.SelectedValue) Then
                    dr.Item("CodeCategorieCNAM") = DBNull.Value
                Else
                    dr.Item("CodeCategorieCNAM") = cmbCategorieCnam.SelectedValue
                End If

                dr.Item("AccordPrealable") = chkAccordPrealable.Checked
                dr.Item("PriseEnCharge") = chkPriseEnCharge.Checked
                If tTarifReference.Text <> "" Then
                    dr.Item("TarifDeReference") = Convert.ToDecimal(tTarifReference.Text)
                Else
                    dr.Item("TarifDeReference") = Convert.ToDecimal("0.000")
                End If

                Try
                    If (RecupererValeurExecuteScalaire("CodeSituation", "ARTICLE", "CodeArticle", CodeArticle) = 2 And cmbSituation.SelectedValue = 1) Then
                        dr.Item("NombreCommande") = 0
                    End If
                Catch
                    dr.Item("NombreCommande") = 0
                End Try


                If cmbSituation.Text <> "" Then
                    dr.Item("CodeSituation") = cmbSituation.SelectedValue
                End If

                dr.Item("SansVignette") = chkSansVignette.Checked
                dr.Item("SansCodeBarre") = chkSansCodeAbarre.Checked

                dr.Item("QuantiteUnitaire") = tQuantiteUnitaire.Text
                dr.Item("ContenanceArticle") = tContenance.Text

                If tstockAlert.Text = "" Then
                    tstockAlert.Value = "0"
                End If
                dr.Item("StockAlerte") = tstockAlert.Text
                dr.Item("QteACommander") = tQuantiteACommander.Text

                'dr.Item("StockInitial") = tStockInventaire.Text

                'If tDateInventaire.Text <> "" And tDateInventaire.Text <> "01/01/1900" Then
                '    dr.Item("Dateinitiale") = tDateInventaire.Text
                'Else
                '    dr.Item("Dateinitiale") = DBNull.Value
                'End If

                dr.Item("PrixAchatHT") = CDec(tPrixAchatHT.Text)

                dr.Item("TVA") = CmbTVA.Text
                dr.Item("PrixAchatTTC") = Convert.ToDecimal(tPrixAchatTTC.Text)
                If tMarge.Text <> "+Infini" Then
                    dr.Item("Marge") = Convert.ToDecimal(tMarge.Text)
                End If
                dr.Item("Exonorertva") = chkExonere.Checked

                dr.Item("PrixVenteHT") = Convert.ToDecimal(tPrixVenteHT.Text)
                dr.Item("HR") = Convert.ToDecimal(tHR.Text)

                dr.Item("PrixVenteTTC") = Convert.ToDecimal(tPrixVenteTTC.Text)
                dr.Item("Section") = tSection.Text
                dr.Item("CodeOperateur") = CodeOperateur

                If Trim(cmbDCI1.Text) <> "" Then
                    dr.Item("DCI1") = cmbDCI1.SelectedValue
                Else
                    dr.Item("DCI1") = DBNull.Value
                End If
                If Trim(cmbDCI2.Text) <> "" Then
                    dr.Item("DCI2") = cmbDCI2.SelectedValue
                Else
                    dr.Item("DCI2") = DBNull.Value
                End If
                If Trim(cmbDCI3.Text) <> "" Then
                    dr.Item("DCI3") = cmbDCI3.SelectedValue
                Else
                    dr.Item("DCI3") = DBNull.Value
                End If

                dr.Item("FemmeEnceinte") = chbFemmeEnceinte.Checked



            End With
        End If

        Try
            daArticle.Update(dsArticle, "Article")
            If ajoutmodif = "A" Then
                InsertionDansLog("AJOUT_ARTICLE", "L ajout de l'article " + tDesignation.Text, CodeOperateur, System.DateTime.Now, "ARTICLE", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
            ElseIf ajoutmodif = "M" Then
                InsertionDansLog("MODIFICATION_ARTICLE", "La modification de l article " + tDesignation.Text, CodeOperateur, System.DateTime.Now, "ARTICLE", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
            End If

            ChangerPrixDesPreparations()

            If AncienneQteUnit <> 0 Then
                If lStock.Text <> "-" Then
                    If CInt(lStock.Text) <> StockArticle Then
                        cmdArticle.CommandText = " UPDATE LOT_ARTICLE SET QteLotArticle = (QteLotArticle / " + Quote(AncienneQteUnit) + "*" + Quote(CInt(tQuantiteUnitaire.Text)) + ")" + _
                                                 " WHERE QteLotArticle <> 0 AND CodeArticle=" + Quote(CodeArticle)
                        cmdArticle.ExecuteNonQuery()
                    End If
                End If
            End If



            '''''''''''''''''''''''''''''
            If cmbCategorie.SelectedValue = 9 And cmbPreparation.SelectedValue = 2 Then
                cmdArticle.Connection = ConnectionServeur
                Try
                    cmdArticle.CommandText = " INSERT INTO FORMULE_PREPARATION (CodePreparation, Designation,date,CodeTypePreparation )" + _
                                        " VALUES (" + Quote(CodeArticle) + ", " + Quote(tDesignation.Text) + "," + Quote(Now.Date) + ", 2 )"
                    cmdArticle.ExecuteNonQuery()
                Catch ex As Exception
                End Try
            End If
            ''''''''''''''''''''''''''''''

            ''''''''''''''''
            If cmbCategorie.Text <> "PREPARATION" Then
                cmdArticle.Connection = ConnectionServeur
                Try
                    cmdArticle.CommandText = " DELETE FROM FRACTIONNEMENT where CodeArticleFractionne = " + Quote(CodeArticle)
                    cmdArticle.ExecuteNonQuery()
                Catch ex As Exception
                End Try
            End If
            ''''''''''''''''




            'If cmbCategorie.Text = "PREPARATION" And (ajoutmodif = "A" Or ajoutmodif = "DUPLIQUER") Then
            '    If cmbPreparation.Enabled = True And cmbPreparation.Text = "FRACTIONNEMENT" Then
            '        bFractionnement_Click(sender, e)
            '    End If
            '    If cmbPreparation.Enabled = True And cmbPreparation.Text = "PHARMACEUTIQUE" Then
            '        bPharmaceutique_Click(sender, e)
            '    End If
            '    If cmbPreparation.Enabled = True And cmbPreparation.Text = "MAGISTRALE" Then
            '        bMagistrale_Click(sender, e)
            '    End If
            '    If cmbPreparation.Enabled = True And cmbPreparation.Text = "INJECTION" Then
            '        bInjection_Click(sender, e)
            '    End If
            'End If

        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsArticle.Reset()
            Me.Init()
        End Try

        '----------------------------------- enregistrement des lots d'articles 

        StrSQL = " SELECT TOP(0)* FROM LOT_ARTICLE WHERE CodeArticle = '" + gLots.Columns("CodeArticle").Value + "'"

        cmdLotsArticle.Connection = ConnectionServeur
        cmdLotsArticle.CommandText = StrSQL
        daLotsArticle = New SqlDataAdapter(cmdLotsArticle)
        daLotsArticle.Fill(dsLotsArticle, "LOT_ARTICLE")
        cbLotsArticle = New SqlCommandBuilder(daLotsArticle)

        Try
            daLotsArticle.Update(dsLotsArticle, "LOT_ARTICLE")

        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            AfficherLotsArticle()
            Exit Sub
        End Try


        If ajoutmodif = "A" Then
            ModuleSurveillance(1, "L'utilisateur " & NomUtilisateur & " a ajouté l'article " & tDesignation.Text)
        Else
            ModuleSurveillance(2, "L'utilisateur " & NomUtilisateur & " a modifié l'article " & tDesignation.Text)
            If PrixModifier = True Then
                ModuleSurveillance(4, "L'utilisateur " & NomUtilisateur & " a changé le prix de l'article " & tDesignation.Text)
            End If
        End If

        AjoutReussi = True
        CodeArticleAjout = CodeArticle
        DesignationAjout = tDesignation.Value
        PrixAchatAjout = tPrixAchatTTC.Value
        PrixVenteAjout = tPrixVenteTTC.Value
        CodeFormeAjout = cmbForme.Text 'SelectedValue
        CodeRayonAjout = tRayon.Text 'SelectedValue
        CodeRayonRAjout = tRayon.Text
        Me.Hide()

    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        If dsArticle.HasChanges Then
            If MsgBox("Voulez vous vraiment annuler les modifications de ce Article ?", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                EstConfirmer = False
                Me.Hide()
            End If
        Else
            EstConfirmer = False
            Me.Hide()
        End If
    End Sub

    Private Sub tCodeArticle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeArticle.KeyUp
        If e.KeyCode = Keys.Enter Then
            tDesignation.Focus()
        End If
    End Sub

    Private Sub tCodeArticle_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeArticle.LostFocus
        If lTest.Text = "Code valide" Then
            lTest.Visible = False
        End If
    End Sub

    Public Sub TestCodeExistant()
        Dim StrSQLtest As String = ""
        dsRecupereNum.Clear()
        If ajoutmodif = "A" Or ajoutmodif = "M" Or ajoutmodif = "DUPLIQUER" Then

            If tDesignation.Text = "" And ajoutmodif = "M" Then
                Exit Sub
            End If

            If ajoutmodif = "A" Or ajoutmodif = "DUPLIQUER" Then
                StrSQLtest = " SELECT CodeArticle FROM Article WHERE CodeABarre=" + Quote(tCodeArticle.Text)
            Else
                StrSQLtest = " SELECT CodeArticle FROM Article WHERE CodeABarre=" + Quote(tCodeArticle.Text) + " AND CodeArticle <>'" + CodeArticle + "'"
            End If
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQLtest
            daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
            daRecupereNumt.Fill(dsRecupereNum, "Article")

            If dsRecupereNum.Tables("Article").Rows.Count <> 0 Or tCodeArticle.Text = "" Then
                lTest.Text = "Code non valide déja existe"
                lTest.ForeColor = Color.OrangeRed
                lTest.Visible = True
                CodeExiste = True
            Else
                lTest.Text = "Code valide"
                lTest.ForeColor = Color.LimeGreen
                lTest.Visible = True
                CodeExiste = False
            End If
        End If
        If tCodeArticle.Text = "" Then
            lTest.Visible = False
        End If
    End Sub


    Private Sub tCodeArticle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeArticle.TextChanged
        TestCodeExistant()
    End Sub

    Private Sub tPrixAchatHT_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixAchatHT.Click
        tPrixAchatHT.SelectAll()
    End Sub

    Private Sub CmbTVA_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        CmbTVA.SelectAll()
    End Sub
    Private Sub tPrixAchatTTC_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixAchatTTC.Click
        tPrixAchatTTC.SelectAll()
    End Sub

    Private Sub tMarge_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles tMarge.Click
        tMarge.SelectAll()
    End Sub

    Private Sub tPrixVenteHT_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixVenteHT.Click
        tPrixVenteHT.SelectAll()
    End Sub

    Private Sub tHR_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        tHR.SelectAll()
    End Sub

    Private Sub tPrixVenteTTC_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixVenteTTC.Click
        tPrixVenteTTC.SelectAll()
    End Sub
    '######################################

    '%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    'teste sur le format des prix

    Private Sub tPrixAchatHT_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixAchatHT.LostFocus
        formatprix(tPrixAchatHT)
        Dim tPrixAchatTTC1 As Double = 0.0
        Dim tPrixVenteHT1 As Double = 0.0
        Dim tPrixVenteTTC1 As Double = 0.0

        tPrixAchatTTC1 = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100)
        tPrixVenteHT1 = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tMarge.Text) / 100)

        If chkExonere.Checked = True Then
            tPrixVenteTTC1 = tPrixVenteHT1 + CDbl(tHR.Text)
        Else
            tPrixVenteTTC1 = (((CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) + (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) * CDbl(tMarge.Text) / 100) + CDbl(tHR.Text))
        End If

        tPrixAchatTTC.Value = (Math.Round(tPrixAchatTTC1, 3)).ToString
        tPrixVenteHT.Value = (Math.Round(tPrixVenteHT1, 3)).ToString
        tPrixVenteTTC.Value = (Math.Round(tPrixVenteTTC1, 3)).ToString
    End Sub

    Private Sub tPrixAchatTTC_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixAchatTTC.LostFocus
        formatprix(tPrixAchatTTC)
    End Sub

    Private Sub tPrixVenteHT_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixVenteHT.LostFocus
        formatprix(tPrixVenteHT)

        tMarge.Text = (((CDbl(tPrixVenteHT.Text) / CDbl(tPrixAchatHT.Text)) - 1) * 100).ToString

        If chkExonere.Checked = False Then
            tPrixVenteTTC.Text = ((CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) + (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) * CDbl(tMarge.Text) / 100 + CDbl(tHR.Text)).ToString
        Else
            tPrixVenteTTC.Text = (CDbl(tPrixVenteHT.Text) + CDbl(tHR.Text)).ToString
        End If

        tMarge.Text = (Math.Round(CDbl(tMarge.Text), 3)).ToString
        tPrixVenteTTC.Text = (Math.Round(CDbl(tPrixVenteTTC.Text), 3, MidpointRounding.AwayFromZero)).ToString

    End Sub

    Private Sub tPrixVenteTTC_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixVenteTTC.LostFocus
        formatprix(tPrixVenteTTC)

        If CDbl(tHR.Text) <> 0.0 Then
            If chkExonere.Checked = False Then
                tPrixVenteHT.Value = ((100 * (CDbl(tPrixVenteTTC.Text) - CDbl(tHR.Text))) / (100 + CDbl(CmbTVA.Text))).ToString
            Else
                tPrixVenteHT.Value = CDbl(tPrixVenteTTC.Text) - CDbl(tHR.Text)
            End If
        Else
            If chkExonere.Checked = False Then
                tPrixVenteHT.Value = ((100 * CDbl(tPrixVenteTTC.Text)) / (100 + CDbl(CmbTVA.Text))).ToString
            Else
                tPrixVenteHT.Value = tPrixVenteTTC.Text
            End If
        End If
        If CDbl(tPrixAchatHT.Text) <> 0 Then
            tMarge.Value = (((CDbl(tPrixVenteHT.Text) / CDbl(tPrixAchatHT.Text)) - 1) * 100).ToString
        Else
            tMarge.Value = "0.000"
        End If

        tPrixVenteHT.Value = (Math.Round(CDbl(tPrixVenteHT.Text), 3, MidpointRounding.AwayFromZero)).ToString
        tMarge.Value = (Math.Round(CDbl(tMarge.Text), 3, MidpointRounding.AwayFromZero)).ToString


        '///////////////////////////////////////////////////
        formatprix(tPrixVenteHT)

        tMarge.Text = (((CDbl(tPrixVenteHT.Text) / CDbl(tPrixAchatHT.Text)) - 1) * 100).ToString

        If chkExonere.Checked = False Then
            tPrixVenteTTC.Text = ((CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) + (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) * CDbl(tMarge.Text) / 100 + CDbl(tHR.Text)).ToString
        Else
            tPrixVenteTTC.Text = (CDbl(tPrixVenteHT.Text) + CDbl(tHR.Text)).ToString
        End If

        tMarge.Text = (Math.Round(CDbl(tMarge.Text), 3, MidpointRounding.AwayFromZero)).ToString
        tPrixVenteTTC.Text = (Math.Round(CDbl(tPrixVenteTTC.Text), 3, MidpointRounding.AwayFromZero)).ToString
    End Sub

    Private Sub CmbTVA_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs)
        If IsNumeric(e.KeyChar) = False And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "," And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "." Then
            e.Handled = True
        End If
    End Sub

    Private Sub CmbTVA_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            tPrixAchatTTC.Focus()
        End If
    End Sub

    Private Sub CmbTVA_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs)
        If CmbTVA.Text = "" Then
            CmbTVA.Text = 0
        End If
        tPrixAchatTTC.Value = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100).ToString
        tPrixVenteHT.Value = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tMarge.Text) / 100).ToString

        If chkExonere.Checked = True Then
            tPrixVenteTTC.Value = (CDbl(tPrixVenteHT.Text) + CDbl(tHR.Text)).ToString 'tPrixVenteHT.Text
        Else
            tPrixVenteTTC.Value = (((CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) + (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) * CDbl(tMarge.Text) / 100) + CDbl(tHR.Text)).ToString
        End If
        'tPrixVenteTTC.Text = (CDbl(tPrixVenteTTC.Text) + CDbl(tHR.Text)).ToString

        tPrixAchatTTC.Value = (Math.Round(CDbl(tPrixAchatTTC.Text), 3)).ToString
        tPrixVenteHT.Value = (Math.Round(CDbl(tPrixVenteHT.Text), 3)).ToString
        tPrixVenteTTC.Value = (Math.Round(CDbl(tPrixVenteTTC.Text), 3)).ToString




        'eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee
        'formatprix(tPrixAchatHT)
        'Dim tPrixAchatTTC1 As Double = 0.0
        'Dim tPrixVenteHT1 As Double = 0.0
        'Dim tPrixVenteTTC1 As Double = 0.0

        'tPrixAchatTTC1 = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100)
        'tPrixVenteHT1 = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tMarge.Text) / 100)

        'If chkExonere.Checked = True Then
        '    tPrixVenteTTC1 = tPrixVenteHT1 + CDbl(tHR.Text)
        'Else
        '    tPrixVenteTTC1 = (((CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) + (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) * CDbl(tMarge.Text) / 100) + CDbl(tHR.Text))
        'End If

        'tPrixAchatTTC.Value = (Math.Round(tPrixAchatTTC1, 3)).ToString
        'tPrixVenteHT.Value = (Math.Round(tPrixVenteHT1, 3)).ToString
        'tPrixVenteTTC.Value = (Math.Round(tPrixVenteTTC1, 3)).ToString

    End Sub

    Private Sub tMarge_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tMarge.LostFocus
        tPrixVenteHT.Value = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tMarge.Text) / 100).ToString
        If chkExonere.Checked = True Then
            tPrixVenteTTC.Value = tPrixVenteHT.Text
        Else
            tPrixVenteTTC.Value = (((CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) + (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) * CDbl(tMarge.Text) / 100) + CDbl(tHR.Text)).ToString
        End If
        tPrixVenteTTC.Value = (CDbl(tPrixVenteTTC.Text) + CDbl(tHR.Text)).ToString

        tPrixVenteHT.Value = (Math.Round(CDbl(tPrixVenteHT.Text), 3)).ToString
        tPrixVenteTTC.Value = (Math.Round(CDbl(tPrixVenteTTC.Text), 3)).ToString

        tPrixVenteHT.Value = (Math.Round(CDbl(tPrixVenteHT.Text), 3)).ToString
        tPrixVenteTTC.Value = (Math.Round(CDbl(tPrixVenteTTC.Text), 3)).ToString


    End Sub

    Private Sub tMarge_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tMarge.KeyUp
        If tMarge.Text <> "" Then
            tPrixVenteHT.Text = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tMarge.Text) / 100).ToString
            tPrixVenteTTC.Text = (((CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) + (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) * CDbl(tMarge.Text) / 100) + CDbl(tHR.Text)).ToString
        End If
        If e.KeyCode = Keys.Enter Then
            tPrixVenteHT.Focus()
        End If
    End Sub
    'OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOz
    Private Sub tPrixAchatHT_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tPrixAchatHT.KeyPress
        If IsNumeric(e.KeyChar) = False And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "," And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "." Then
            e.Handled = True
        End If
    End Sub

    Private Sub tPrixAchatTTC_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tPrixAchatTTC.KeyPress
        If IsNumeric(e.KeyChar) = False And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "," And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "." Then
            e.Handled = True
        End If
    End Sub

    Private Sub tPrixVenteHT_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tPrixVenteHT.KeyPress
        If IsNumeric(e.KeyChar) = False And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "," And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "." Then
            e.Handled = True
        End If
    End Sub
    Private Sub tPrixVenteTTC_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tPrixVenteTTC.KeyPress
        If IsNumeric(e.KeyChar) = False And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "," And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "." Then
            e.Handled = True
        End If
    End Sub

    Private Sub cmbDCI2_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbDCI2.KeyUp
        If e.KeyCode = Keys.Enter Then
            If cmbDCI2.WillChangeToText <> "" Then
                cmbDCI2.Text = cmbDCI2.WillChangeToText
            ElseIf cmbDCI2.Text <> "" Then
                Dim Enregistrer As Boolean = False
                Dim MyAjouterDCI As New fAjouterDCI
                MyAjouterDCI.Valeur = cmbDCI2.Text
                MyAjouterDCI.ShowDialog()
                Enregistrer = MyAjouterDCI.Enregistrer

                If Enregistrer = False Then
                    cmbDCI2.Text = ""
                Else
                    Dim NouvelleDCI As String = cmbDCI2.Text
                    dsArticle.Tables("DCI2").Clear()
                    Dim StrSQL1 As String = ""
                    'chargement des DCI
                    StrSQL1 = "SELECT DISTINCT CodeDCI,LibelleDCI FROM DCI WHERE SupprimeDCI=0 ORDER BY LibelleDCI ASC"
                    cmdArticle.Connection = ConnectionServeur
                    cmdArticle.CommandText = StrSQL1
                    daArticle = New SqlDataAdapter(cmdArticle)
                    daArticle.Fill(dsArticle, "DCI2")
                    cmbDCI2.DataSource = dsArticle.Tables("DCI2")
                    cmbDCI2.ValueMember = "CodeDCI"
                    cmbDCI2.DisplayMember = "LibelleDCI"
                    cmbDCI2.ColumnHeaders = False
                    cmbDCI2.Splits(0).DisplayColumns("CodeDCI").Width = 0
                    cmbDCI2.Splits(0).DisplayColumns("LibelleDCI").Width = 160
                    cmbDCI2.Text = NouvelleDCI
                End If
            End If
            cmbDCI2.Focus()
        Else
            cmbDCI2.OpenCombo()
        End If

        If e.KeyCode = Keys.Enter Then
            If cmbDCI1.Text = "" Then
                cmbDCI1.Text = cmbDCI2.Text
                cmbDCI2.Text = ""
            End If
        End If

        If e.KeyCode = Keys.Enter And cmbDCI2.Text <> "" Then
            cmbDCI3.Focus()
        End If

        'If e.KeyCode = Keys.Enter Then
        '    cmbDCI2.Text = cmbDCI2.WillChangeToText
        'Else
        '    cmbDCI2.OpenCombo()
        'End If

        'If e.KeyCode = Keys.Enter Then
        '    If cmbDCI1.Text = "" Then
        '        cmbDCI1.Text = cmbDCI2.Text
        '        cmbDCI2.Text = ""
        '    End If
        'End If

    End Sub

    Private Sub cmbDCI2_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbDCI2.LostFocus
        If cmbDCI1.Text = "" Then
            cmbDCI1.Text = cmbDCI2.Text
            cmbDCI2.Text = ""
        End If
    End Sub

    Private Sub cmbDCI2_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbDCI2.TextChanged

    End Sub

    Private Sub cmbDCI2_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbDCI2.Validated
        If cmbDCI1.Text = "" Then
            cmbDCI1.Text = cmbDCI2.Text
            cmbDCI2.Text = ""
        End If
    End Sub

    Private Sub cmbDCI3_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbDCI3.KeyUp

        If e.KeyCode = Keys.Enter Then
            If cmbDCI3.WillChangeToText <> "" Then
                cmbDCI3.Text = cmbDCI3.WillChangeToText
            ElseIf cmbDCI3.Text <> "" Then
                Dim Enregistrer As Boolean = False
                Dim MyAjouterDCI As New fAjouterDCI
                MyAjouterDCI.Valeur = cmbDCI3.Text
                MyAjouterDCI.ShowDialog()
                Enregistrer = MyAjouterDCI.Enregistrer

                If Enregistrer = False Then
                    cmbDCI3.Text = ""
                Else
                    Dim NouvelleDCI As String = cmbDCI3.Text
                    dsArticle.Tables("DCI3").Clear()
                    Dim StrSQL1 As String = ""
                    'chargement des DCI
                    StrSQL1 = "SELECT DISTINCT CodeDCI,LibelleDCI FROM DCI WHERE SupprimeDCI=0 ORDER BY LibelleDCI ASC"
                    cmdArticle.Connection = ConnectionServeur
                    cmdArticle.CommandText = StrSQL1
                    daArticle = New SqlDataAdapter(cmdArticle)
                    daArticle.Fill(dsArticle, "DCI3")
                    cmbDCI3.DataSource = dsArticle.Tables("DCI3")
                    cmbDCI3.ValueMember = "CodeDCI"
                    cmbDCI3.DisplayMember = "LibelleDCI"
                    cmbDCI3.ColumnHeaders = False
                    cmbDCI3.Splits(0).DisplayColumns("CodeDCI").Width = 0
                    cmbDCI3.Splits(0).DisplayColumns("LibelleDCI").Width = 160
                    cmbDCI3.Text = NouvelleDCI
                End If
            End If
            cmbDCI3.Focus()
        Else
            cmbDCI3.OpenCombo()
        End If

        If e.KeyCode = Keys.Enter Then
            If cmbDCI1.Text = "" Then
                cmbDCI1.Text = cmbDCI3.Text
                cmbDCI3.Text = ""
                Exit Sub
            End If
            If cmbDCI2.Text = "" Then
                cmbDCI2.Text = cmbDCI3.Text
                cmbDCI3.Text = ""
            End If
        End If

        If e.KeyCode = Keys.Enter And cmbDCI3.Text <> "" Then
            cmbDCI1.Focus()
        End If

        'If e.KeyCode = Keys.Enter Then
        '    cmbDCI3.Text = cmbDCI3.WillChangeToText
        'Else
        '    cmbDCI3.OpenCombo()
        'End If
        'If e.KeyCode = Keys.Enter Then
        '    If cmbDCI1.Text = "" Then
        '        cmbDCI1.Text = cmbDCI3.Text
        '        cmbDCI3.Text = ""
        '        Exit Sub
        '    End If
        '    If cmbDCI2.Text = "" Then
        '        cmbDCI2.Text = cmbDCI3.Text
        '        cmbDCI3.Text = ""
        '    End If
        'End If
    End Sub

    Private Sub cmbDCI3_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbDCI3.LostFocus
        If cmbDCI1.Text = "" Then
            cmbDCI1.Text = cmbDCI3.Text
            cmbDCI3.Text = ""
            Exit Sub
        End If
        If cmbDCI2.Text = "" Then
            cmbDCI2.Text = cmbDCI3.Text
            cmbDCI3.Text = ""
        End If
    End Sub

    Private Sub cmbDCI3_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbDCI3.Validated
        If cmbDCI1.Text = "" Then
            cmbDCI1.Text = cmbDCI3.Text
            cmbDCI3.Text = ""
            Exit Sub
        End If
        If cmbDCI2.Text = "" Then
            cmbDCI2.Text = cmbDCI3.Text
            cmbDCI3.Text = ""
        End If
    End Sub

    Public Sub AfficherLotsArticle()
        ValeurDuStock = 0
        Dim I As Integer
        Dim Cond As String = " 1=1 "

        If (dsLotsArticle.Tables.IndexOf("LOT_ARTICLE") > -1) Then
            dsLotsArticle.Tables("LOT_ARTICLE").Clear()
        End If

        If tCodeArticle.Text <> "" Then
            Cond = Cond + " AND CodeArticle='" + CodeArticle + "'"
        Else
            Cond = Cond + " AND CodeArticle='RIEN'"
        End If

        If chbVoirLotsVides.Checked = False Then
            Cond = Cond + " AND QteLotArticle <> 0 "
        End If

        cmdLotsArticle.CommandText = " SELECT " + _
                                    " NumeroLotArticle, " + _
                                    " LOT_ARTICLE.CodeArticle, " + _
                                    " QteLotArticle, " + _
                                    " DatePeremptionArticle " + _
                                    " FROM LOT_ARTICLE " + _
                                    " WHERE " + Cond

        cmdLotsArticle.Connection = ConnectionServeur
        daLotsArticle = New SqlDataAdapter(cmdLotsArticle)
        daLotsArticle.Fill(dsLotsArticle, "LOT_ARTICLE")
        cbLotsArticle = New SqlCommandBuilder(daLotsArticle)

        With gLots
            .Columns.Clear()
            .DataSource = dsLotsArticle
            .DataMember = "LOT_ARTICLE"
            .Rebind(False)
            .Columns("NumeroLotArticle").Caption = "Num lot"
            .Columns("QteLotArticle").Caption = "Quantité"
            .Columns("DatePeremptionArticle").Caption = "Date Péremption"

            ' Centrer tous 
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
                .Splits(0).DisplayColumns(I).Style.Locked = False
            Next

            If tDesignationGL.Text <> "" Or tCodeArticle.Text <> "" Or tCodePCT.Text <> "" Then
                .Splits(0).DisplayColumns("QteLotArticle").Style.Locked = False
            Else
                .Splits(0).DisplayColumns("QteLotArticle").Style.Locked = True
            End If
            .Splits(0).DisplayColumns("NumeroLotArticle").Style.Locked = True


            .Splits(0).DisplayColumns("NumeroLotArticle").Width = 100
            .Splits(0).DisplayColumns("QteLotArticle").Width = 70
            .Splits(0).DisplayColumns("DatePeremptionArticle").Width = 80

            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False


            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
        End With

        For I = 0 To dsLotsArticle.Tables("LOT_ARTICLE").Rows.Count - 1
            ValeurDuStock = ValeurDuStock + dsLotsArticle.Tables("LOT_ARTICLE").Rows(I).Item("QteLotArticle")
        Next

        gLots.MoveRelative(xLotsArticle)
        cbLotsArticle = New SqlCommandBuilder(daLotsArticle)

        '
        'reaffciherQte(gLots)

    End Sub

    Private Sub bAjouterLot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterLot.Click
        Dim dr As DataRow
        Dim StrSQLtest As String = ""
        Dim StrSQLMiseAjour As String = ""
        Dim StrSQLAjoutNumeroLot As String = ""

        Dim daTestLot As New SqlDataAdapter
        Dim cmdAjoutLot As New SqlCommand

        Dim i As Integer = 0
        Dim NouveauNumeroLot As String = ""

        If (dsLotsArticle.Tables.IndexOf("LOT_ARTICLE_TEST") > -1) Then
            dsLotsArticle.Tables("LOT_ARTICLE_TEST").Clear()
        End If

        If tNumeroLot.Text = "" Then

            '------------------ recupération du dernier numéro de lot pour cet article puis incrémentation
            StrSQLAjoutNumeroLot = " SELECT MAX(NumeroLotArticle) FROM [LOT_ARTICLE] WHERE CodeArticle = (select CodeArticle FROM ARTICLE WHERE CodeAbarre ='" + _
                     tCodeArticle.Text + "')"
            cmdAjoutLot.Connection = ConnectionServeur
            cmdAjoutLot.CommandText = StrSQLAjoutNumeroLot
            Try
                NouveauNumeroLot = cmdAjoutLot.ExecuteScalar().ToString

                For i = 0 To dsLotsArticle.Tables("LOT_ARTICLE").Rows.Count - 1
                    If IsDBNull(dsLotsArticle.Tables("LOT_ARTICLE").Rows(i).Item("NumeroLotArticle")) = False Then
                        If dsLotsArticle.Tables("LOT_ARTICLE").Rows(i).Item("NumeroLotArticle") > NouveauNumeroLot Then
                            NouveauNumeroLot = dsLotsArticle.Tables("LOT_ARTICLE").Rows(i).Item("NumeroLotArticle")
                        End If
                    End If
                Next



                If NouveauNumeroLot <> "" Then
                    NouveauNumeroLot = (Convert.ToInt32(NouveauNumeroLot) + 1).ToString
                Else
                    NouveauNumeroLot = 1
                End If

            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            tNumeroLot.Text = NouveauNumeroLot
        End If

        If tQuantiteLot.Text = "" Then
            MsgBox("Veuillez saisir la quantité !", MsgBoxStyle.Critical, "Erreur")
            tQuantiteLot.Focus()
            Exit Sub
        End If

        If dtpDate.Text = "" Then
            MsgBox("Veuillez saisir la Date de péremption !", MsgBoxStyle.Critical, "Erreur")
            dtpDate.Focus()
            Exit Sub
        End If

        StrSQLtest = " SELECT * FROM LOT_ARTICLE as LOT_ARTICLE_TEST WHERE NumeroLotArticle='" + tNumeroLot.Text + "' AND CodeArticle= (select CodeArticle FROM ARTICLE WHERE CodeABarre='" + tCodeArticle.Text + "')"
        cmdLotsArticle.Connection = ConnectionServeur
        cmdLotsArticle.CommandText = StrSQLtest
        daTestLot = New SqlDataAdapter(cmdLotsArticle)
        daTestLot.Fill(dsLotsArticle, "LOT_ARTICLE_TEST")

        If dsLotsArticle.Tables("LOT_ARTICLE_TEST").Rows.Count <> 0 Then
            If dsLotsArticle.Tables("LOT_ARTICLE_TEST").Rows(0).Item("DatePeremptionArticle").ToString() <> dtpDate.Text Then
                MsgBox("Numéro de lot existe déja '", MsgBoxStyle.Critical, "Erreur")
                tNumeroLot.Focus()
                Exit Sub
            Else
                StrSQLtest = " UPDATE LOT_ARTICLE SET QteLotArticle=QteLotArticle+'" + tQuantiteLot.Text + _
                             "' WHERE NumeroLotArticle='" + tNumeroLot.Text + "'AND CodeArticle= (select CodeArticle FROM ARTICLE WHERE CodeABarre='" + tCodeArticle.Text + "')"
                cmdLotsArticle.Connection = ConnectionServeur
                cmdLotsArticle.CommandText = StrSQLtest
                Try
                    cmdLotsArticle.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                    MsgBox("Problème lors de l'ajout de la quantité au lot'", MsgBoxStyle.Critical, "Erreur")
                    Exit Sub
                End Try
                tNumeroLot.Text = ""
                tQuantiteLot.Text = ""
                dtpDate.Text = Today
                AfficherLotsArticle()
                Exit Sub
            End If
        End If

        For i = 0 To dsLotsArticle.Tables("LOT_ARTICLE").Rows.Count - 1
            If IsDBNull(dsLotsArticle.Tables("LOT_ARTICLE").Rows(i).Item("DatePeremptionArticle")) = False Then
                If dsLotsArticle.Tables("LOT_ARTICLE").Rows(i).Item("DatePeremptionArticle") = dtpDate.Text Then
                    MsgBox("Date de péremption existe déja '", MsgBoxStyle.Critical, "Erreur")
                    Exit Sub
                End If
            End If
        Next
        If dtpDate.Text <> "" Then
            If dtpDate.Text <= System.DateTime.Now Then
                MsgBox("Date de péremption dépassée '", MsgBoxStyle.Critical, "Erreur")
                Exit Sub
            End If
        End If

        With dsLotsArticle
            dr = .Tables("LOT_ARTICLE").NewRow
            dr.Item("NumeroLotArticle") = tNumeroLot.Text
            dr.Item("CodeArticle") = CodeArticle
            dr.Item("QteLotArticle") = tQuantiteLot.Text
            If dtpDate.Text <> "" Then
                dr.Item("DatePeremptionArticle") = dtpDate.Text
            End If

            .Tables("LOT_ARTICLE").Rows.Add(dr)
        End With
        tNumeroLot.Text = ""
        tQuantiteLot.Text = ""
        dtpDate.Value = Nothing
    End Sub

    Private Sub bValiderLots_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bValiderLots.Click
        Dim I As Integer = 0
        Dim ValeurFinal As Integer = 0

        'Dim dr As DataRow
        Dim StrSQL As String = ""

        For I = 0 To dsLotsArticle.Tables("LOT_ARTICLE").Rows.Count - 1
            ValeurFinal = ValeurFinal + dsLotsArticle.Tables("LOT_ARTICLE").Rows(I).Item("QteLotArticle")
        Next

        If ValeurDuStock <> ValeurFinal Then
            MsgBox("La somme des quantités des stocks n'est pas valide !  Valeur initial est : " + ValeurDuStock.ToString, MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If


        'StrSQL = " SELECT * FROM LOT_ARTICLE WHERE CodeArticle = " + gLots.Columns("CodeArticle").Value

        'cmdLotsArticle.Connection = ConnectionServeur
        'cmdLotsArticle.CommandText = StrSQL
        'daLotsArticle = New SqlDataAdapter(cmdLotsArticle)
        'daLotsArticle.Fill(dsLotsArticle, "LOT_ARTICLE")
        'cbLotsArticle = New SqlCommandBuilder(daLotsArticle)

        'Try
        '    daLotsArticle.Update(dsLotsArticle, "LOT_ARTICLE")

        'Catch ex As Exception
        '    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
        '    AfficherLotsArticle()
        '    Exit Sub
        'End Try

        tCodeArticle.Focus()

        'AfficherLotsArticle()
    End Sub



    Private Sub cmbCategorie_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCategorie.KeyDown

    End Sub



    Private Sub cmbCategorie_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCategorie.KeyUp

        If e.KeyCode = Keys.Enter Then
            If cmbCategorie.WillChangeToText <> "" Then
                cmbCategorie.Text = cmbCategorie.WillChangeToText
            ElseIf cmbCategorie.Text <> "" Then
                Dim Enregistrer As Boolean = False
                Dim MyAjouteCategorie As New fAjouterCategorie
                MyAjouteCategorie.Valeur = cmbCategorie.Text
                MyAjouteCategorie.ShowDialog()
                Enregistrer = MyAjouteCategorie.Enregistrer

                MyAjouteCategorie.Close()
                MyAjouteCategorie.Dispose()

                If Enregistrer = False Then
                    cmbCategorie.Text = ""
                Else

                    If (dsArticle.Tables.IndexOf("CATEGORIE") > -1) Then
                        dsArticle.Tables("CATEGORIE").Clear()
                    End If

                    Dim NouvelleCategorie As String = cmbCategorie.Text
                    dsArticle.Tables("CATEGORIE").Clear()
                    Dim StrSQL1 As String = ""
                    'chargement des Formes
                    StrSQL1 = "SELECT DISTINCT CodeCategorie,LibelleCategorie FROM CATEGORIE WHERE SupprimeCategorie=0 ORDER BY LibelleCategorie ASC"
                    cmdArticle.Connection = ConnectionServeur
                    cmdArticle.CommandText = StrSQL1
                    daArticle = New SqlDataAdapter(cmdArticle)
                    daArticle.Fill(dsArticle, "CATEGORIE")
                    cmbCategorie.DataSource = dsArticle.Tables("CATEGORIE")
                    cmbCategorie.ValueMember = "CodeCategorie"
                    cmbCategorie.DisplayMember = "LibelleCategorie"
                    cmbCategorie.ColumnHeaders = False
                    cmbCategorie.Splits(0).DisplayColumns("CodeCategorie").Width = 0
                    cmbCategorie.Splits(0).DisplayColumns("LibelleCategorie").Width = 160
                    cmbCategorie.Text = NouvelleCategorie
                End If
            End If
            If cmbCategorie.Text = "PREPARATION" Then
                cmbPreparation.Visible = True
                cmbPreparation.Focus()
            Else
                cmbPreparation.Visible = False
                cmbPreparation.Text = ""
                cmbLaboratoire.Focus()
            End If
        Else
            cmbCategorie.OpenCombo()
        End If

    End Sub

    Private Sub cmbCategorie_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbCategorie.LostFocus

        If cmbCategorie.Text = "PREPARATION" Then
            cmbPreparation.Visible = True
        Else
            cmbPreparation.Visible = False
            cmbPreparation.Text = ""
        End If

    End Sub
    Private Sub cmbCategorie_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbCategorie.TextChanged
        If cmbCategorie.Text = "PREPARATION" Then
            cmbPreparation.Visible = True
        Else
            cmbPreparation.Visible = False
        End If
    End Sub

    Private Sub cmbPreparation_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbPreparation.KeyUp
        If e.KeyCode = Keys.Enter Then
            If cmbPreparation.WillChangeToText <> "" Then
                cmbPreparation.Text = cmbPreparation.WillChangeToText
                cmbLaboratoire.Focus()
            End If
        Else
            cmbPreparation.OpenCombo()
        End If


    End Sub

    Private Sub cmbPreparation_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbPreparation.TextChanged

    End Sub
    Private Sub C1DockingTabPage2_Enter(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1DockingTabPage2.Enter

    End Sub
    Private Sub C1DockingTabPage1_Enter(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1DockingTabPage1.Enter
        'bFractionnement.Visible = True
        'bPharmaceutique.Visible = True
        'bInjection.Visible = True
        'bMagistrale.Visible = True
    End Sub


    Private Sub C1DockingTabPage3_Enter(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1DockingTabPage3.Enter

    End Sub

    Private Sub tQuantiteUnitaire_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tQuantiteUnitaire.KeyUp
        If e.KeyCode = Keys.Enter Then
            'Dim stock As Integer = 0
            'stock = CalculeStock(CodeArticle)

            'If stock <> 0 Then
            '    'MsgBox("Stock différent a zéro, vous ne pouvez pas modifier la quantite unitaire !" + Chr(13) + "Utiliser l'entrée et la sortie diverse ", MsgBoxStyle.Critical, "Erreur")
            '    Dim reponse As MsgBoxResult

            '    reponse = MsgBox("Ete-vous sûre de changer la Quantité unitaire", MsgBoxStyle.YesNo, "Erreur")
            '    If reponse = MsgBoxResult.No Then
            '        tQuantiteUnitaire.Text = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticle)
            '        tQuantiteUnitaire.Focus()
            '        Exit Sub
            '    End If

            'End If
        End If
        If e.KeyCode = Keys.Enter Then
            tContenance.Focus()
        End If
    End Sub

    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        Try
            Quote(ValeurCle)
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurCle)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function
    Private Sub tHR_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tHR.KeyPress
        If IsNumeric(e.KeyChar) = False And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "," And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "." Then
            e.Handled = True
        End If
    End Sub

    Private Sub tHR_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tHR.KeyUp
        If e.KeyCode = Keys.Enter Then
            tPrixVenteTTC.Focus()
        End If
    End Sub

    Private Sub tHR_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tHR.LostFocus
        If tHR.Text <> "0.080" And tHR.Text <> "0.08" And tHR.Text <> "0.100" And tHR.Text <> "0.10" And tHR.Text <> "0.1" And tHR.Text <> "0" And tHR.Text <> "0.0" And tHR.Text <> "0.00" And tHR.Text <> "0.000" Then
            MsgBox("Honoraire doit être 0.080 ou 0.100 ou 0 !", MsgBoxStyle.Critical, "Erreur")
            tHR.Value = "0.080"
        Else
            Dim PrixVenteTTC As Double = 0.0
            If chkExonere.Checked = True Then
                tPrixVenteTTC.Value = (CDbl(tPrixVenteHT.Text) + CDbl(tHR.Text)).ToString
            Else
                PrixVenteTTC = (((CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) + (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) * CDbl(tMarge.Text) / 100) + CDbl(tHR.Text))
                tPrixVenteTTC.Value = Math.Round(PrixVenteTTC, 3)
            End If

            tPrixVenteTTC.Value = (Math.Round(CDbl(tPrixVenteTTC.Text), 3)).ToString

        End If
    End Sub
    Private Sub tDesignation_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tDesignation.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbForme.Focus()
        End If
    End Sub

    Private Sub cmbForme_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbForme.KeyUp
        If e.KeyCode = Keys.Enter Then
            If cmbForme.WillChangeToText <> "" Then
                cmbForme.Text = cmbForme.WillChangeToText
            ElseIf cmbForme.Text <> "" Then
                Dim Enregistrer As Boolean = False
                Dim MyAjouterForme As New fAjouterForme
                MyAjouterForme.Valeur = cmbForme.Text
                MyAjouterForme.ShowDialog()
                Enregistrer = MyAjouterForme.Enregistrer

                If Enregistrer = False Then
                    cmbForme.Text = ""
                Else
                    Dim NouvelleForme As String = cmbForme.Text
                    dsArticle.Tables("FORME_ARTICLE").Clear()
                    Dim StrSQL1 As String = ""
                    'chargement des Formes
                    StrSQL1 = "SELECT DISTINCT CodeForme,LibelleForme FROM FORME_ARTICLE ORDER BY LibelleForme ASC"
                    cmdArticle.Connection = ConnectionServeur
                    cmdArticle.CommandText = StrSQL1
                    daArticle = New SqlDataAdapter(cmdArticle)
                    daArticle.Fill(dsArticle, "FORME_ARTICLE")
                    cmbForme.DataSource = dsArticle.Tables("FORME_ARTICLE")
                    cmbForme.ValueMember = "CodeForme"
                    cmbForme.DisplayMember = "LibelleForme"
                    cmbForme.ColumnHeaders = False
                    cmbForme.Splits(0).DisplayColumns("CodeForme").Width = 0
                    cmbForme.Splits(0).DisplayColumns("LibelleForme").Width = 160
                    cmbForme.Text = NouvelleForme
                End If
            End If
            cmbForme.Focus()
        Else
            cmbForme.OpenCombo()
        End If

        If e.KeyCode = Keys.Enter Then
            cmbCategorie.Focus()
        End If

    End Sub

    Private Sub cmbLaboratoire_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbLaboratoire.KeyUp
        If e.KeyCode = Keys.Enter Then
            If cmbLaboratoire.WillChangeToText <> "" Then
                cmbLaboratoire.Text = cmbLaboratoire.WillChangeToText
            ElseIf cmbLaboratoire.Text <> "" Then
                Dim Enregistrer As Boolean = False
                Dim MyAjouteLaboratoire As New fAjouterLaboratoire
                MyAjouteLaboratoire.Valeur = cmbLaboratoire.Text
                MyAjouteLaboratoire.ShowDialog()
                Enregistrer = MyAjouteLaboratoire.Enregistrer

                If Enregistrer = False Then
                    cmbLaboratoire.Text = ""
                Else
                    Dim NouvelleLaboratoire As String = cmbLaboratoire.Text
                    dsArticle.Tables("LABORATOIRE").Clear()
                    Dim StrSQL1 As String = ""
                    'chargement des Formes
                    StrSQL1 = "SELECT DISTINCT CodeLabo,NomLabo FROM LABORATOIRE WHERE SupprimeLabo=0 ORDER BY NomLabo ASC"
                    cmdArticle.Connection = ConnectionServeur
                    cmdArticle.CommandText = StrSQL1
                    daArticle = New SqlDataAdapter(cmdArticle)
                    daArticle.Fill(dsArticle, "LABORATOIRE")
                    cmbLaboratoire.DataSource = dsArticle.Tables("LABORATOIRE")
                    cmbLaboratoire.ValueMember = "CodeLabo"
                    cmbLaboratoire.DisplayMember = "NomLabo"
                    cmbLaboratoire.ColumnHeaders = False
                    cmbLaboratoire.Splits(0).DisplayColumns("CodeLabo").Width = 0
                    cmbLaboratoire.Splits(0).DisplayColumns("NomLabo").Width = 160
                    cmbLaboratoire.Text = NouvelleLaboratoire
                End If
            End If
        Else
            cmbLaboratoire.OpenCombo()
        End If


        If e.KeyCode = Keys.Enter Then
            tDosage.Focus()
        End If
    End Sub

    Private Sub cmbTableau_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbTableau.KeyUp
        If e.KeyCode = Keys.Enter Then
            tSection.Focus()
        End If
    End Sub

    Private Sub cmbTableau_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbTableau.TextChanged
        If Trim(cmbTableau.Text) = "A" Then
            tHR.Value = RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTA", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) '"0.080"
        ElseIf Trim(cmbTableau.Text) = "B" Then
            tHR.Value = RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTB", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) '"0.100"
        ElseIf Trim(cmbTableau.Text) = "C" Then
            tHR.Value = RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTC", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) '"0"
        Else
            tHR.Value = "0"
        End If

        tHR_LostFocus(sender, e)
    End Sub

    Private Sub tDosage_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tDosage.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbTableau.Focus()
        End If
    End Sub

    Private Sub tSection_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tSection.KeyUp
        If e.KeyCode = Keys.Enter Then
            tRayon.Focus()
        End If
    End Sub
    Private Sub tCodePCT_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodePCT.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbCategorieCnam.Focus()
        End If
    End Sub

    Private Sub tCodePCT_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodePCT.LostFocus
        lTestPCT.Visible = False
    End Sub

    Private Sub tCodePCT_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodePCT.TextChanged
        Dim StrSQLtest As String = ""
        dsRecupereNum.Clear()
        If ajoutmodif = "A" Then
            If tCodePCT.Text <> "" Then
                StrSQLtest = " SELECT CodeArticle FROM Article WHERE Supprime=0 AND CodePCT=" + Quote(tCodePCT.Text)
                cmdRecupereNum.Connection = ConnectionServeur
                cmdRecupereNum.CommandText = StrSQLtest
                daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
                daRecupereNumt.Fill(dsRecupereNum, "ArticlePCT")

                If dsRecupereNum.Tables("ArticlePCT").Rows.Count <> 0 Then
                    lTestPCT.Text = "Code PCT non valide déja existe"
                    lTestPCT.ForeColor = Color.OrangeRed
                    lTestPCT.Visible = True
                    CodePCTExist = True
                Else
                    lTestPCT.Text = "Code PCT valide"
                    lTestPCT.ForeColor = Color.LimeGreen
                    lTestPCT.Visible = True
                    CodePCTExist = False
                End If
            Else
                CodePCTExist = False
            End If
        End If

        If ajoutmodif = "M" Then
            If tCodePCT.Text <> "" Then
                StrSQLtest = " SELECT CodeArticle FROM Article WHERE CodePCT=" + Quote(tCodePCT.Text) & " AND CodeArticle != " & CodeArticle
                cmdRecupereNum.Connection = ConnectionServeur
                cmdRecupereNum.CommandText = StrSQLtest
                daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
                daRecupereNumt.Fill(dsRecupereNum, "ArticlePCT")

                If dsRecupereNum.Tables("ArticlePCT").Rows.Count <> 0 Or tCodePCT.Text = "" Then
                    lTestPCT.Text = "Code PCT non valide déja existe"
                    lTestPCT.ForeColor = Color.OrangeRed
                    lTestPCT.Visible = True
                    CodePCTExist = True
                Else
                    lTestPCT.Text = "Code PCT valide"
                    lTestPCT.ForeColor = Color.LimeGreen
                    lTestPCT.Visible = True
                    CodePCTExist = False
                End If
            Else
                CodePCTExist = False
            End If

        End If

        If tCodePCT.Text = "" Then
            lTestPCT.Visible = False
        End If
    End Sub

    Private Sub cmbCategorieCnam_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCategorieCnam.KeyUp
        If e.KeyCode = Keys.Enter Then
            tTarifReference.Focus()
        End If
    End Sub

    Private Sub cmbCategorieCnam_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbCategorieCnam.TextChanged
        If cmbCategorieCnam.Text = "CONFORT" Then
            chkPriseEnCharge.Checked = False
            chkPriseEnCharge.Enabled = False
            chkAccordPrealable.Checked = False
            chkAccordPrealable.Enabled = False
        Else
            chkPriseEnCharge.Enabled = True
            chkAccordPrealable.Enabled = True
            chkPriseEnCharge.Checked = True
        End If
    End Sub

    Private Sub tTarifReference_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tTarifReference.GotFocus
        tTarifReference.SelectAll()
    End Sub

    Private Sub tTarifReference_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTarifReference.KeyUp

        If IsNumeric(tTarifReference.Text) = False Then
            tTarifReference.Text = ""
            'Exit Sub
        End If

        If e.KeyCode = Keys.Enter Then
            tPrixAchatHT.Focus()
        End If
    End Sub

    Private Sub cmbSituation_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbSituation.KeyUp
        If e.KeyCode = Keys.Enter Then
            bConfirmer.Focus()
        End If
    End Sub
    Private Sub tPrixAchatHT_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tPrixAchatHT.KeyUp
        If e.KeyCode = Keys.Enter Then
            If tPrixAchatHT.Text = "" Then
                tPrixAchatHT.Text = 0
            End If
            If cmbCategorie.Text.Contains("MEDICAMENT") Or cmbCategorie.Text = "VETERINAIRE" Then
                If tPrixAchatHT.Text <= 1.022 And tPrixAchatHT.Text >= 0 Then
                    tMarge.Text = "42.9"
                ElseIf tPrixAchatHT.Text >= 1.023 And tPrixAchatHT.Text <= 1.596 Then
                    tMarge.Text = "38.9"
                ElseIf tPrixAchatHT.Text >= 1.597 And tPrixAchatHT.Text <= 9 Then
                    tMarge.Text = "35.1"
                ElseIf tPrixAchatHT.Text >= 9.001 And tPrixAchatHT.Text <= 1000 Then
                    tMarge.Text = "31.6"
                End If
            End If

            If tMarge.Text = "0.000" Or tMarge.Text = "0" Then
                If tPrixAchatHT.Text <= 1.022 And tPrixAchatHT.Text >= 0 Then
                    tMarge.Text = "42.9"
                ElseIf tPrixAchatHT.Text >= 1.023 And tPrixAchatHT.Text <= 1.596 Then
                    tMarge.Text = "38.9"
                ElseIf tPrixAchatHT.Text >= 1.597 And tPrixAchatHT.Text <= 9 Then
                    tMarge.Text = "35.1"
                ElseIf tPrixAchatHT.Text >= 9.001 And tPrixAchatHT.Text <= 1000 Then
                    tMarge.Text = "31.6"
                End If
            End If

            CmbTVA.Focus()
        End If
    End Sub
    Private Sub tPrixAchatTTC_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tPrixAchatTTC.KeyUp
        If e.KeyCode = Keys.Enter Then
            tMarge.Focus()
        End If
    End Sub
    Private Sub tPrixVenteHT_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tPrixVenteHT.KeyUp
        If e.KeyCode = Keys.Enter Then
            tHR.Focus()
        End If
    End Sub

    Private Sub tPrixVenteTTC_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tPrixVenteTTC.KeyUp
        If e.KeyCode = Keys.Enter Then
            'tQuantiteUnitaire.Focus()
            tstockAlert.Focus()
        End If
    End Sub

    Private Sub tContenance_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tContenance.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbSituation.Focus()
        End If
    End Sub
    Private Sub tstockAlert_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tstockAlert.KeyUp
        If e.KeyCode = Keys.Enter Then
            tQuantiteACommander.Focus()
        End If
    End Sub

    Private Sub tDateInitial_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            bConfirmer.Focus()
        End If
    End Sub

    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)

        If e.KeyCode = Keys.F10 Then
            e.SuppressKeyPress = True
        Else
            e.SuppressKeyPress = False
        End If


        If e.KeyData = Keys.F3 Then
            bConfirmer_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F2 Then
            bNaviger_Click(o, e)
            Exit Sub
        End If

    End Sub

    Private Sub fFicheArticle_Activated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Activated
        If ajoutmodif = "A" Then
            tCodeArticle.Focus()
        Else
            tDesignation.Focus()
        End If

    End Sub

    Private Sub fFicheArticle_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler
        'bMagistrale.Enabled = True
        'bFractionnement.Enabled = False
        'bInjection.Enabled = False
        'bPharmaceutique.Enabled = False
    End Sub

    Private Sub chkExonere_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkExonere.CheckedChanged
        Dim PrixVenteTTC As Double = 0.0

        If chkExonere.Checked = True Then
            tPrixVenteTTC.Value = (Math.Round(CDbl(tPrixVenteHT.Text) + CDbl(tHR.Text), 3)).ToString
        Else
            PrixVenteTTC = (((CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) + (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) * CDbl(tMarge.Text) / 100) + CDbl(tHR.Text))
            tPrixVenteTTC.Value = Math.Round(PrixVenteTTC, 3)
        End If
    End Sub

    Private Sub cmbDCI1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbDCI1.KeyUp
        If e.KeyCode = Keys.Enter Then
            If cmbDCI1.WillChangeToText <> "" Then
                cmbDCI1.Text = cmbDCI1.WillChangeToText
            ElseIf cmbDCI1.Text <> "" Then
                Dim Enregistrer As Boolean = False
                Dim MyAjouterDCI As New fAjouterDCI
                MyAjouterDCI.Valeur = cmbDCI1.Text
                MyAjouterDCI.ShowDialog()
                Enregistrer = MyAjouterDCI.Enregistrer

                If Enregistrer = False Then
                    cmbDCI1.Text = ""
                Else
                    Dim NouvelleDCI As String = cmbDCI1.Text
                    dsArticle.Tables("DCI1").Clear()
                    Dim StrSQL1 As String = ""
                    'chargement des DCI
                    StrSQL1 = "SELECT DISTINCT CodeDCI,LibelleDCI FROM DCI WHERE SupprimeDCI=0 ORDER BY LibelleDCI ASC"
                    cmdArticle.Connection = ConnectionServeur
                    cmdArticle.CommandText = StrSQL1
                    daArticle = New SqlDataAdapter(cmdArticle)
                    daArticle.Fill(dsArticle, "DCI1")
                    cmbDCI1.DataSource = dsArticle.Tables("DCI1")
                    cmbDCI1.ValueMember = "CodeDCI"
                    cmbDCI1.DisplayMember = "LibelleDCI"
                    cmbDCI1.ColumnHeaders = False
                    cmbDCI1.Splits(0).DisplayColumns("CodeDCI").Width = 0
                    cmbDCI1.Splits(0).DisplayColumns("LibelleDCI").Width = 160
                    cmbDCI1.Text = NouvelleDCI
                End If
            End If
            cmbDCI1.Focus()
        Else
            cmbDCI1.OpenCombo()
        End If
        If e.KeyCode = Keys.Enter And cmbDCI1.Text <> "" Then
            cmbDCI2.Focus()
        End If
    End Sub

    Private Sub tRayon_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRayon.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNumCirculaire.Focus()
        End If
    End Sub

    Private Sub bNaviger_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bNaviger.Click
        If C1DockingTabPage1.IsSelected = True Then
            C1DockingTabPage2.Show()
        ElseIf C1DockingTabPage2.IsSelected = True Then
            If ControleDAcces(2, "MANIPULATION_LOTS") = "False" Then
                C1DockingTabPage1.Show()
            Else
                C1DockingTabPage3.Show()
            End If
        ElseIf C1DockingTabPage3.IsSelected = True Then
            C1DockingTabPage1.Show()
        End If
    End Sub

    Private Sub gLots_AfterColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gLots.AfterColEdit
        Dim DatePermeption As Date
        Dim LotArticle As String = ""
        Dim i As Integer = 0

        If IsDBNull(gLots(gLots.Row, "DatePeremptionArticle")) = False Then
            DatePermeption = gLots(gLots.Row, "DatePeremptionArticle")
        End If
        LotArticle = gLots(gLots.Row, "NumeroLotArticle")

        For i = 0 To gLots.RowCount - 1
            If gLots(i, "DatePeremptionArticle").ToString <> "" Then
                If DatePermeption = gLots(i, "DatePeremptionArticle") And LotArticle <> gLots(i, "NumeroLotArticle") Then
                    MsgBox("Date de péremption est utilisé dans un autre lot !", MsgBoxStyle.Critical, "Erreur")
                    'gLots.EditActive = False
                    gLots.Columns("DatePeremptionArticle").Value = ""
                End If
            End If
        Next
    End Sub

    Private Sub gLots_Change(sender As Object, e As System.EventArgs) Handles gLots.Change
        Dim DatePermeption As Date
        Dim LotArticle As String = ""
        Dim i As Integer = 0

        If IsDBNull(gLots(gLots.Row, "DatePeremptionArticle")) = False Then
            DatePermeption = gLots(gLots.Row, "DatePeremptionArticle")
        End If
        LotArticle = gLots(gLots.Row, "NumeroLotArticle")

        For i = 0 To gLots.RowCount - 1
            If gLots(i, "DatePeremptionArticle").ToString <> "" Then
                If DatePermeption = gLots(i, "DatePeremptionArticle") And LotArticle <> gLots(i, "NumeroLotArticle") Then
                    MsgBox("Date de péremption est utilisé dans un autre lot !", MsgBoxStyle.Critical, "Erreur")
                    'gLots.EditActive = False
                    gLots.Columns("DatePeremptionArticle").Value = ""
                End If
            End If
        Next
    End Sub

    Private Sub gLots_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gLots.KeyUp
        If e.KeyCode = Keys.Delete And gLots.Col = 3 Then
            gLots.EditActive = False
            gLots.Columns("DatePeremptionArticle").Value = ""
        End If
    End Sub

    Private Sub tNumeroLot_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNumeroLot.KeyUp
        If e.KeyCode = Keys.Enter Then
            tQuantiteLot.Focus()
        End If
    End Sub

    Private Sub tNumeroLot_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNumeroLot.TextChanged

    End Sub

    Private Sub tQuantiteLot_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tQuantiteLot.KeyUp
        If e.KeyCode = Keys.Enter Then
            dtpDate.Focus()
        End If
    End Sub

    Private Sub tQuantiteLot_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tQuantiteLot.TextChanged

    End Sub

    Private Sub dtpDate_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDate.KeyUp
        If e.KeyCode = Keys.Enter Then
            bAjouterLot.Focus()
        End If
    End Sub

    Private Sub dtpDate_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDate.TextChanged

    End Sub

    Private Sub chkAccordPrealable_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkAccordPrealable.CheckedChanged
        If chkAccordPrealable.Checked = True Then
            chkPriseEnCharge.Checked = True
            chkPriseEnCharge.Enabled = False
        Else
            chkPriseEnCharge.Enabled = True
        End If
    End Sub

    Private Sub chbVoirLotsVides_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbVoirLotsVides.CheckedChanged
        AfficherLotsArticle()
    End Sub

    Private Sub bStatistiqueArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bStatistiqueArticle.Click
        If ControleDAcces(2, "STATISTIQUE_ARTICLE") = "False" And ModeADMIN <> "ADMIN" Then
            Exit Sub
        End If

        fStatistiqueVente.CodeArticle = CodeArticle 'tCodeArticle.Value
        fStatistiqueVente.Designation = tDesignation.Value
        fStatistiqueVente.Init()
        fStatistiqueVente.ShowDialog()
    End Sub

    Public Sub ChargerLesCombos()
        Dim StrSQL2 As String = ""
        Dim CMD1 As New SqlCommand
        Dim DA1 As New SqlDataAdapter

        Dim Formes As String = cmbForme.Text
        Dim Categories As String = cmbCategorie.Text
        Dim Laboratoires As String = cmbLaboratoire.Text
        Dim Situations As String = cmbSituation.Text
        Dim Tableau As String = cmbTableau.Text
        Dim CategoriesCnam As String = cmbCategorieCnam.Text
        Dim Préparations As String = cmbPreparation.Text

        'chargement des Formes
        StrSQL2 = "SELECT DISTINCT CodeForme,LibelleForme FROM FORME_ARTICLE WHERE SupprimeForme=0 ORDER BY LibelleForme ASC"
        CMD1.Connection = ConnectionServeur
        CMD1.CommandText = StrSQL2
        DA1 = New SqlDataAdapter(CMD1)
        DA1.Fill(dsArticle, "FORME_ARTICLE1")
        cmbForme.DataSource = dsArticle.Tables("FORME_ARTICLE1")
        cmbForme.ValueMember = "CodeForme"
        cmbForme.DisplayMember = "LibelleForme"
        cmbForme.ColumnHeaders = False
        cmbForme.Splits(0).DisplayColumns("CodeForme").Width = 0
        cmbForme.Splits(0).DisplayColumns("LibelleForme").Width = 160

        'chargement des Categories
        StrSQL2 = "SELECT DISTINCT CodeCategorie,LibelleCategorie FROM Categorie WHERE SupprimeCategorie =0 ORDER BY LibelleCategorie ASC"
        CMD1.Connection = ConnectionServeur
        CMD1.CommandText = StrSQL2
        DA1 = New SqlDataAdapter(CMD1)
        DA1.Fill(dsArticle, "CATEGORIE1")
        cmbCategorie.DataSource = dsArticle.Tables("CATEGORIE1")
        cmbCategorie.ValueMember = "CodeCategorie"
        cmbCategorie.DisplayMember = "LibelleCategorie"
        cmbCategorie.ColumnHeaders = False
        cmbCategorie.Splits(0).DisplayColumns("CodeCategorie").Width = 0
        cmbCategorie.Splits(0).DisplayColumns("LibelleCategorie").Width = 160

        'chargement des Laboratoires
        StrSQL2 = "SELECT DISTINCT CodeLabo,NomLabo FROM LABORATOIRE WHERE SupprimeLabo=0 ORDER BY NomLabo ASC"
        CMD1.Connection = ConnectionServeur
        CMD1.CommandText = StrSQL2
        DA1 = New SqlDataAdapter(CMD1)
        DA1.Fill(dsArticle, "LABORATOIRE1")
        cmbLaboratoire.DataSource = dsArticle.Tables("LABORATOIRE1")
        cmbLaboratoire.ValueMember = "CodeLabo"
        cmbLaboratoire.DisplayMember = "NomLabo"
        cmbLaboratoire.ColumnHeaders = False
        cmbLaboratoire.Splits(0).DisplayColumns("CodeLabo").Width = 0
        cmbLaboratoire.Splits(0).DisplayColumns("NomLabo").Width = 160

        'chargement des Situations
        StrSQL2 = "SELECT DISTINCT CodeSituationArticle,LibelleSituationArticle FROM SITUATION_ARTICLE ORDER BY LibelleSituationArticle ASC"
        CMD1.Connection = ConnectionServeur
        CMD1.CommandText = StrSQL2
        DA1 = New SqlDataAdapter(CMD1)
        DA1.Fill(dsArticle, "SITUATION_ARTICLE1")
        cmbSituation.DataSource = dsArticle.Tables("SITUATION_ARTICLE1")
        cmbSituation.ValueMember = "CodeSituationArticle"
        cmbSituation.DisplayMember = "LibelleSituationArticle"
        cmbSituation.ColumnHeaders = False
        cmbSituation.Splits(0).DisplayColumns("CodeSituationArticle").Width = 0
        cmbSituation.Splits(0).DisplayColumns("LibelleSituationArticle").Width = 160

        'chargement des Valeurs Tableau
        StrSQL2 = "SELECT DISTINCT LibelleTableau FROM TABLEAU ORDER BY LibelleTableau ASC"
        CMD1.Connection = ConnectionServeur
        CMD1.CommandText = StrSQL2
        DA1 = New SqlDataAdapter(CMD1)
        DA1.Fill(dsArticle, "TABELAU1")
        cmbTableau.DataSource = dsArticle.Tables("TABELAU1")
        cmbTableau.ValueMember = "LibelleTableau"
        cmbTableau.ColumnHeaders = False
        cmbTableau.Splits(0).DisplayColumns("LibelleTableau").Width = 160

        'chargement des Categories Cnam
        StrSQL2 = "SELECT DISTINCT CodeCategorie,LibelleCategorie FROM CATEGORIE_CNAM ORDER BY LibelleCategorie ASC"
        CMD1.Connection = ConnectionServeur
        CMD1.CommandText = StrSQL2
        DA1 = New SqlDataAdapter(CMD1)
        DA1.Fill(dsArticle, "CATEGORIE_CNAM1")
        cmbCategorieCnam.DataSource = dsArticle.Tables("CATEGORIE_CNAM1")
        cmbCategorieCnam.ValueMember = "CodeCategorie"
        cmbCategorieCnam.DisplayMember = "LibelleCategorie"
        cmbCategorieCnam.ColumnHeaders = False
        cmbCategorieCnam.Splits(0).DisplayColumns("CodeCategorie").Width = 0
        cmbCategorieCnam.Splits(0).DisplayColumns("LibelleCategorie").Width = 460

        'chargement des Préparations
        StrSQL2 = "SELECT DISTINCT CodeTypePreparation,LibelleTypePreparation FROM TYPE_PREPARATION ORDER BY LibelleTypePreparation ASC"
        CMD1.Connection = ConnectionServeur
        CMD1.CommandText = StrSQL2
        DA1 = New SqlDataAdapter(CMD1)
        DA1.Fill(dsArticle, "TYPE_PREPARATION1")
        cmbPreparation.DataSource = dsArticle.Tables("TYPE_PREPARATION1")
        cmbPreparation.ValueMember = "CodeTypePreparation"
        cmbPreparation.DisplayMember = "LibelleTypePreparation"
        cmbPreparation.ColumnHeaders = False
        cmbPreparation.Splits(0).DisplayColumns("CodeTypePreparation").Width = 0
        cmbPreparation.Splits(0).DisplayColumns("LibelleTypePreparation").Width = 119

        cmbForme.Text = Formes
        cmbCategorie.Text = Categories
        cmbLaboratoire.Text = Laboratoires
        cmbSituation.Text = Situations
        cmbTableau.Text = Tableau
        cmbCategorieCnam.Text = CategoriesCnam
        cmbPreparation.Text = Préparations

    End Sub

    Public Sub ChangerPrixDesPreparations()
        Dim StrMajLOT As String = ""
        Dim NouveauMontant As Double = 0.0
        Dim i As Integer = 0

        If (dsArticle.Tables.IndexOf("ARTICLE_FORMULE_PREPARATION_DETAILS") > -1) Then
            dsArticle.Tables("ARTICLE_FORMULE_PREPARATION_DETAILS").Clear()
        End If
        StrSQL = "SELECT FORMULE_PREPARATION_DETAILS.CodePreparation,CodeArticle " + _
                 " FROM " + _
                 "FORMULE_PREPARATION_DETAILS WHERE CodeArticle='" + tCodeArticle.Text + "'"

        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "ARTICLE_FORMULE_PREPARATION_DETAILS")

        '----------------------------------- mise a jour des prix des articles 
        For i = 0 To dsArticle.Tables("ARTICLE_FORMULE_PREPARATION_DETAILS").Rows.Count - 1
            If dsArticle.Tables("ARTICLE_FORMULE_PREPARATION_DETAILS").Rows(i).Item("CodeArticle") <> "" Then

                StrMajLOT = "UPDATE FORMULE_PREPARATION_DETAILS SET PrixVenteTTC=" + _
                        tPrixVenteTTC.Text + ", TotTTC= Qte* " + tPrixVenteTTC.Text + _
                        " WHERE CodeArticle = '" + _
                        tCodeArticle.Text + "' AND CodePreparation='" + _
                        dsArticle.Tables("ARTICLE_FORMULE_PREPARATION_DETAILS").Rows(i).Item("CodePreparation") + "'"

                cmdArticle.Connection = ConnectionServeur
                cmdArticle.CommandText = StrMajLOT
                Try
                    cmdArticle.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                '---------------------------------------- calcul du somme du préparation
                StrSQL = "SELECT SUM(PrixVenteTTC*Qte) " + _
                         " FROM " + _
                         "FORMULE_PREPARATION_DETAILS WHERE CodePreparation='" + _
                         dsArticle.Tables("ARTICLE_FORMULE_PREPARATION_DETAILS").Rows(i).Item("CodePreparation") + "'"

                cmdArticle.Connection = ConnectionServeur
                cmdArticle.CommandText = StrSQL
                Try
                    NouveauMontant = cmdArticle.ExecuteScalar
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
                '--------------------------------------- changemant du total 

                StrMajLOT = "UPDATE FORMULE_PREPARATION SET TotalArticle=" + _
                       NouveauMontant.ToString + ", TotalTTC=TotalIndemnite+" + NouveauMontant.ToString + _
                       " WHERE CodePreparation = '" + _
                       dsArticle.Tables("ARTICLE_FORMULE_PREPARATION_DETAILS").Rows(i).Item("CodePreparation") + "'"

                cmdArticle.Connection = ConnectionServeur
                cmdArticle.CommandText = StrMajLOT
                Try
                    cmdArticle.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

            End If
        Next
    End Sub

    Private Sub C1DockingTabPage3_Paint(ByVal sender As Object, ByVal e As System.Windows.Forms.PaintEventArgs) Handles C1DockingTabPage3.Paint
        If ControleDAcces(2, "MANIPULATION_LOTS") = "False" Then
            C1DockingTabPage1.Show()
            Exit Sub
        End If
    End Sub

    Private Sub bChangementPrix_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bChangementPrix.Click
        PrixModifier = True

        If ControleDAcces(2, "CHANGEMENT_DU_PRIX") = "False" Then
            Exit Sub
        End If

        tPrixAchatHT.Enabled = True
        CmbTVA.Enabled = True
        tPrixAchatTTC.Enabled = True
        tMarge.Enabled = True
        tPrixVenteHT.Enabled = True
        tHR.Enabled = True
        tPrixVenteTTC.Enabled = True
        chkExonere.Enabled = True
    End Sub

    Private Sub tMarge_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tMarge.KeyPress
        If IsNumeric(e.KeyChar) = False And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "," And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "." Then
            e.Handled = True
        End If
    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

        If gLots.RowCount > 0 Then
            'Test si la lign est NEW ADDED et elle est vide
            If gLots(gLots.Row, ("CodeArticle")) <> "" Then
                gLots.Delete()
            End If
        End If
        Exit Sub
    End Sub

    ''Vue qu'on a roncontré un probleme de pile lors de l'utilisation 
    ''de l'evenement fetchRowStyle pour reaffcier la Qte en decimal 
    ''ou en entier, on a utilisé cette methode pour debloquer la 
    ''siuation !!! elle reste à optimiser 24/04/2013
    'Private Sub reaffciherQte(ByVal grid As C1TrueDBGrid)

    '    For i As Integer = 0 To grid.RowCount - 1

    '        If IsInteger(grid(i, "QteLotArticle")) <> False Then
    '            grid(i, "QteLotArticle") = CInt(grid(i, "QteLotArticle"))
    '        End If

    '    Next

    'End Sub

    Private Sub bChangeQuantiteUnitaire_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bChangeQuantiteUnitaire.Click
        If MsgBox("Etes-vous sûr de changer la quantité unitaire ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Confirmation") = MsgBoxResult.Yes Then

            Dim myMotDePasse As New fMotDePasse
            Dim ConfirmerEnregistrer As Boolean = False
            Dim CodeOperateur As String = ""

            myMotDePasse.ShowDialog()

            ConfirmerEnregistrer = fMotDePasse.Confirmer
            CodeOperateur = fMotDePasse.CodeOperateur

            myMotDePasse.Dispose()
            myMotDePasse.Close()

            If ConfirmerEnregistrer = False Then
                Exit Sub
            End If

            Tab.SelectedIndex = 0

            tQuantiteUnitaire.Enabled = True
            tFocus.Focus()

        Else

        End If
    End Sub

    Dim AncienneQteUnit As Integer = 0

    Private Sub tQuantiteUnitaire_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tQuantiteUnitaire.LostFocus
        'Controle du changement de la quantité unitaire 
        Try
            AncienneQteUnit = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticle)
        Catch ex As Exception
            AncienneQteUnit = 1
        End Try

        ' stock = CalculeStock(CodeArticle)
        If Not IsNumeric(tQuantiteUnitaire.Value) Then
            tQuantiteUnitaire.Value = 1
        End If
        If IsNumeric(tQuantiteUnitaire.Text) Then
            '' ''If AncienneQteUnit <> CInt(tQuantiteUnitaire.Text) Then
            '' ''    lStock.Text = (StockArticle / AncienneQteUnit) * tQuantiteUnitaire.Text
            '' ''Else
            '' ''    lStock.Text = StockArticle
            '' ''End If
        Else
            tQuantiteUnitaire.Text = "1"
        End If

    End Sub

    Private Sub tFocus_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tFocus.KeyUp
        If e.KeyCode = Keys.Enter Then
            tQuantiteUnitaire.Focus()
        End If
    End Sub

    Private Sub C1DockingTabPage2_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1DockingTabPage2.GotFocus
        cmbDCI1.Focus()
    End Sub

    Private Sub gAchat_DoubleClick(sender As Object, e As System.EventArgs) Handles gAchat.DoubleClick
        Dim MyAchatAffiche As New fAchatJusteAffichage
        MyAchatAffiche.NumeroAchat = gAchat(gAchat.Row, "NumeroAchat")
        MyAchatAffiche.ShowDialog()
        MyAchatAffiche.Close()
        MyAchatAffiche.Dispose()
    End Sub

    Private Sub gAchat_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles gAchat.KeyUp
        If e.KeyCode = Keys.F1 Then
            Dim MyAchatAffiche As New fAchatJusteAffichage
            MyAchatAffiche.NumeroAchat = gAchat(gAchat.Row, "NumeroAchat")
            MyAchatAffiche.ShowDialog()
            MyAchatAffiche.Close()
            MyAchatAffiche.Dispose()
        End If
    End Sub

    Private Sub chkSansCodeAbarre_CheckedChanged(sender As System.Object, e As System.EventArgs) Handles chkSansCodeAbarre.CheckedChanged
        'If chkSansCodeAbarre.Checked And Len(tCodeArticle.Text) > 10 Then
        '    MsgBox("Le code à barres ne doit pas dépasser 10 chiffres.", MsgBoxStyle.Information, "Erreur")
        '    tCodeArticle.Focus()
        'End If
    End Sub

    Private Sub gLots_LostFocus(sender As Object, e As System.EventArgs) Handles gLots.LostFocus

    End Sub

    Private Sub CmbTVA_LostFocus1(sender As Object, e As System.EventArgs) Handles CmbTVA.LostFocus
        Dim exist As Boolean = False
        For i = 0 To dsArticle.Tables("TVA").Rows().Count - 1
            If (dsArticle.Tables("TVA").Rows(i).Item("ValeurTVA") = CmbTVA.Text) Then
                exist = True
            End If
        Next

        If (exist = False) Then
            MsgBox("TVA Invalide.", MsgBoxStyle.Information)
            CmbTVA.Text = ""
            CmbTVA.Focus()
            Exit Sub
        End If


        If CmbTVA.Text = "" Then
            CmbTVA.Text = 0
        End If
        tPrixAchatTTC.Value = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100).ToString
        tPrixVenteHT.Value = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tMarge.Text) / 100).ToString

        If chkExonere.Checked = True Then
            tPrixVenteTTC.Value = (CDbl(tPrixVenteHT.Text) + CDbl(tHR.Text)).ToString 'tPrixVenteHT.Text
        Else
            tPrixVenteTTC.Value = (((CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) + (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(CmbTVA.Text) / 100) * CDbl(tMarge.Text) / 100) + CDbl(tHR.Text)).ToString
        End If
        'tPrixVenteTTC.Text = (CDbl(tPrixVenteTTC.Text) + CDbl(tHR.Text)).ToString

        tPrixAchatTTC.Value = (Math.Round(CDbl(tPrixAchatTTC.Text), 3)).ToString
        tPrixVenteHT.Value = (Math.Round(CDbl(tPrixVenteHT.Text), 3)).ToString
        tPrixVenteTTC.Value = (Math.Round(CDbl(tPrixVenteTTC.Text), 3)).ToString
    End Sub

    Private Sub CmbTVA_MouseUp(sender As System.Object, e As System.Windows.Forms.MouseEventArgs) Handles CmbTVA.MouseUp

    End Sub

    Private Sub CmbTVA_KeyUp_1(sender As System.Object, e As System.Windows.Forms.KeyEventArgs) Handles CmbTVA.KeyUp
        If e.KeyCode = Keys.Enter Then
            tPrixAchatTTC.Focus()
        End If
    End Sub

    Private Sub tQuantiteACommander_KeyUp(sender As Object, e As KeyEventArgs) Handles tQuantiteACommander.KeyUp
        If e.KeyCode = Keys.Enter Then
            tQuantiteUnitaire.Focus()
        End If
    End Sub

    Private Sub C1DockingTabPage4_Click(sender As Object, e As EventArgs) Handles C1DockingTabPage4.Click
       
    End Sub

    Private Sub Tab_Click(sender As Object, e As EventArgs) Handles Tab.Click
        If Tab.SelectedTab.Text = "Historique achat" Then
            '   If ControleDAcces(2, "HISTORIQUE_ACHAT") = "False" Or ModeADMIN <> "ADMIN" Then
            'dsArticle.Tables("Achat").Clear()
            'dsArticle.Tables("Achat").AcceptChanges()
            Exit Sub
            '  End If
        End If
    End Sub

    Private Sub tNumCirculaire_KeyUp(sender As Object, e As KeyEventArgs) Handles tNumCirculaire.KeyUp
        If e.KeyCode = Keys.Enter Then
            tCodePCT.Focus()
        End If
    End Sub
End Class