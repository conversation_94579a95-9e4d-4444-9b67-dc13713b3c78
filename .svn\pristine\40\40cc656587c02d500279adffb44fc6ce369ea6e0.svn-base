﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="StockManagementModel.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2008" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
  <EntityContainer Name="StockManagementModelStoreContainer">
    <EntitySet Name="ARTICLE" EntityType="StockManagementModel.Store.ARTICLE" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="INVENTAIRE" EntityType="StockManagementModel.Store.INVENTAIRE" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="INVENTAIRE_DETAILS" EntityType="StockManagementModel.Store.INVENTAIRE_DETAILS" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="LOT_ARTICLE" EntityType="StockManagementModel.Store.LOT_ARTICLE" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="MOUVEMENT_ARTICLE" EntityType="StockManagementModel.Store.MOUVEMENT_ARTICLE" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="MOUVEMENT_LOT_ARTICLE" EntityType="StockManagementModel.Store.MOUVEMENT_LOT_ARTICLE" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="V_List_ArticlesRecherche" EntityType="StockManagementModel.Store.V_List_ArticlesRecherche" store:Type="Views" store:Schema="dbo" store:Name="V_List_ArticlesRecherche">
      <DefiningQuery>SELECT 
      [V_List_ArticlesRecherche].[Id] AS [Id], 
      [V_List_ArticlesRecherche].[CodeArticle] AS [CodeArticle], 
      [V_List_ArticlesRecherche].[CodeABarre] AS [CodeABarre], 
      [V_List_ArticlesRecherche].[Designation] AS [Designation], 
      [V_List_ArticlesRecherche].[PrixVenteHT] AS [PrixVenteHT], 
      [V_List_ArticlesRecherche].[PrixVenteTTC] AS [PrixVenteTTC], 
      [V_List_ArticlesRecherche].[PrixAchatHT] AS [PrixAchatHT], 
      [V_List_ArticlesRecherche].[PrixAchatTTC] AS [PrixAchatTTC], 
      [V_List_ArticlesRecherche].[TVA] AS [TVA], 
      [V_List_ArticlesRecherche].[Stock] AS [Stock], 
      [V_List_ArticlesRecherche].[Rayon] AS [Rayon], 
      [V_List_ArticlesRecherche].[LibelleForme] AS [LibelleForme], 
      [V_List_ArticlesRecherche].[LibelleCategorie] AS [LibelleCategorie]
      FROM [dbo].[V_List_ArticlesRecherche] AS [V_List_ArticlesRecherche]</DefiningQuery>
    </EntitySet>
    <EntitySet Name="V_List_Inventaire" EntityType="StockManagementModel.Store.V_List_Inventaire" store:Type="Views" store:Schema="dbo" store:Name="V_List_Inventaire">
      <DefiningQuery>SELECT 
      [V_List_Inventaire].[Id] AS [Id], 
      [V_List_Inventaire].[NumeroInventaire] AS [NumeroInventaire], 
      [V_List_Inventaire].[CodeArticle] AS [CodeArticle], 
      [V_List_Inventaire].[CodeABarre] AS [CodeABarre], 
      [V_List_Inventaire].[Designation] AS [Designation], 
      [V_List_Inventaire].[StockInitial] AS [StockInitial], 
      [V_List_Inventaire].[StockActuel] AS [StockActuel], 
      [V_List_Inventaire].[PrixVenteTTC] AS [PrixVenteTTC], 
      [V_List_Inventaire].[PrixAchatTTC] AS [PrixAchatTTC], 
      [V_List_Inventaire].[TotalAchatTTC] AS [TotalAchatTTC], 
      [V_List_Inventaire].[QuantiteUnitaire] AS [QuantiteUnitaire]
      FROM [dbo].[V_List_Inventaire] AS [V_List_Inventaire]</DefiningQuery>
    </EntitySet>
    <EntitySet Name="V_List_LotArticleInventaire" EntityType="StockManagementModel.Store.V_List_LotArticleInventaire" store:Type="Views" store:Schema="dbo" store:Name="V_List_LotArticleInventaire">
      <DefiningQuery>SELECT 
      [V_List_LotArticleInventaire].[Id] AS [Id], 
      [V_List_LotArticleInventaire].[CodeArticle] AS [CodeArticle], 
      [V_List_LotArticleInventaire].[NumeroLotArticle] AS [NumeroLotArticle], 
      [V_List_LotArticleInventaire].[DatePeremptionArticle] AS [DatePeremptionArticle], 
      [V_List_LotArticleInventaire].[QteLotArticle] AS [QteLotArticle], 
      [V_List_LotArticleInventaire].[Designation] AS [Designation]
      FROM [dbo].[V_List_LotArticleInventaire] AS [V_List_LotArticleInventaire]</DefiningQuery>
    </EntitySet>
    <EntitySet Name="V_List_NouvelInventaire" EntityType="StockManagementModel.Store.V_List_NouvelInventaire" store:Type="Views" store:Schema="dbo" store:Name="V_List_NouvelInventaire">
      <DefiningQuery>SELECT 
      [V_List_NouvelInventaire].[Id] AS [Id], 
      [V_List_NouvelInventaire].[NumeroInventaire] AS [NumeroInventaire], 
      [V_List_NouvelInventaire].[CodeArticle] AS [CodeArticle], 
      [V_List_NouvelInventaire].[CodeABarre] AS [CodeABarre], 
      [V_List_NouvelInventaire].[Designation] AS [Designation], 
      [V_List_NouvelInventaire].[StockInitial] AS [StockInitial], 
      [V_List_NouvelInventaire].[StockActuel] AS [StockActuel], 
      [V_List_NouvelInventaire].[PrixVenteTTC] AS [PrixVenteTTC], 
      [V_List_NouvelInventaire].[PrixAchatTTC] AS [PrixAchatTTC], 
      [V_List_NouvelInventaire].[TotalAchatTTC] AS [TotalAchatTTC], 
      [V_List_NouvelInventaire].[QuantiteUnitaire] AS [QuantiteUnitaire]
      FROM [dbo].[V_List_NouvelInventaire] AS [V_List_NouvelInventaire]</DefiningQuery>
    </EntitySet>
    <AssociationSet Name="FK_INVENTAIRE_DETAILS_LOT_ARTICLE" Association="StockManagementModel.Store.FK_INVENTAIRE_DETAILS_LOT_ARTICLE">
      <End Role="LOT_ARTICLE" EntitySet="LOT_ARTICLE" />
      <End Role="INVENTAIRE_DETAILS" EntitySet="INVENTAIRE_DETAILS" />
    </AssociationSet>
    <AssociationSet Name="FK_LOTS_ARTICLE_ARTICLE" Association="StockManagementModel.Store.FK_LOTS_ARTICLE_ARTICLE">
      <End Role="ARTICLE" EntitySet="ARTICLE" />
      <End Role="LOT_ARTICLE" EntitySet="LOT_ARTICLE" />
    </AssociationSet>
  </EntityContainer>
  <EntityType Name="ARTICLE">
    <Key>
      <PropertyRef Name="CodeArticle" />
    </Key>
    <Property Name="CodeArticle" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeABarre" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Designation" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Dosage" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="LibelleTableau" Type="varchar" Nullable="false" MaxLength="50" />
    <Property Name="QuantiteUnitaire" Type="numeric" Nullable="false" Precision="6" />
    <Property Name="ContenanceArticle" Type="int" Nullable="false" />
    <Property Name="PrixAchatHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="PrixAchatTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="PrixVenteHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="PrixVenteTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TVA" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="Marge" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="Exonorertva" Type="bit" Nullable="false" />
    <Property Name="HR" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="CodePCT" Type="varchar" Nullable="false" MaxLength="50" />
    <Property Name="CodeCategorieCNAM" Type="int" />
    <Property Name="TarifDeReference" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="AccordPrealable" Type="bit" Nullable="false" />
    <Property Name="PriseEnCharge" Type="bit" Nullable="false" />
    <Property Name="SansCodeBarre" Type="bit" Nullable="false" />
    <Property Name="SansVignette" Type="bit" Nullable="false" />
    <Property Name="StockAlerte" Type="numeric" Precision="6" />
    <Property Name="QteACommander" Type="numeric" Nullable="false" Precision="6" />
    <Property Name="DateAlerte" Type="date" />
    <Property Name="DateDerniereCommande" Type="date" />
    <Property Name="QuantiteDernierCommande" Type="numeric" Precision="6" />
    <Property Name="DateInitiale" Type="date" />
    <Property Name="StockInitial" Type="int" Nullable="false" />
    <Property Name="CodeForme" Type="int" Nullable="false" />
    <Property Name="CodeCategorie" Type="int" Nullable="false" />
    <Property Name="CodeLabo" Type="int" />
    <Property Name="Rayon" Type="varchar" Nullable="false" MaxLength="50" />
    <Property Name="CodeSituation" Type="int" />
    <Property Name="CodeOperateur" Type="varchar" Nullable="false" MaxLength="50" />
    <Property Name="CodeGroupement" Type="int" />
    <Property Name="CodeTypePreparation" Type="int" />
    <Property Name="Section" Type="varchar" Nullable="false" MaxLength="50" />
    <Property Name="DCI1" Type="int" />
    <Property Name="DCI2" Type="int" />
    <Property Name="DCI3" Type="int" />
    <Property Name="Supprime" Type="bit" Nullable="false" />
    <Property Name="FemmeEnceinte" Type="bit" Nullable="false" />
    <Property Name="StockArticle" Type="numeric" Nullable="false" Precision="6" />
    <Property Name="CodeFournisseur" Type="varchar" MaxLength="255" />
    <Property Name="NombreCommande" Type="int" />
  </EntityType>
  <EntityType Name="INVENTAIRE">
    <Key>
      <PropertyRef Name="NumeroInventaire" />
    </Key>
    <Property Name="NumeroInventaire" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Date" Type="datetime" Nullable="false" />
    <Property Name="ValeurAchatInitial" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="ValeurVenteInitial" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="ValeurAchatActuelle" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="ValeurVenteActuelle" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="ValeurAchatDifference" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="ValeurVenteDifference" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="Remarque" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodePersonnel" Type="varchar" Nullable="false" MaxLength="50" />
    <Property Name="Valide" Type="bit" Nullable="false" />
    <Property Name="EnCours" Type="bit" />
    <Property Name="Poste" Type="int" />
  </EntityType>
  <EntityType Name="INVENTAIRE_DETAILS">
    <Key>
      <PropertyRef Name="NumeroInventaire" />
      <PropertyRef Name="CodeArticle" />
      <PropertyRef Name="NumeroLot" />
    </Key>
    <Property Name="NumeroInventaire" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeArticle" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="NumeroLot" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeABarre" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Designation" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeForme" Type="int" Nullable="false" />
    <Property Name="Rayon" Type="varchar" MaxLength="50" />
    <Property Name="StockInitial" Type="int" Nullable="false" />
    <Property Name="StockActuel" Type="int" Nullable="false" />
    <Property Name="PrixAchatTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalAchatTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="PrixVenteTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="NumLigne" Type="int" />
    <Property Name="DatePeremption" Type="date" />
  </EntityType>
  <EntityType Name="LOT_ARTICLE">
    <Key>
      <PropertyRef Name="NumeroLotArticle" />
      <PropertyRef Name="CodeArticle" />
    </Key>
    <Property Name="NumeroLotArticle" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeArticle" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="QteLotArticle" Type="int" Nullable="false" />
    <Property Name="DatePeremptionArticle" Type="date" />
  </EntityType>
  <EntityType Name="MOUVEMENT_ARTICLE">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
    <Property Name="CodeArticle" Type="nvarchar" Nullable="false" MaxLength="50" />
    <Property Name="AncienStock" Type="int" Nullable="false" />
    <Property Name="Qte" Type="int" Nullable="false" />
    <Property Name="NouveauStock" Type="int" Nullable="false" />
    <Property Name="TypeOperation" Type="varchar" Nullable="false" MaxLength="300" />
    <Property Name="TypeMouvement" Type="varchar" Nullable="false" MaxLength="300" />
    <Property Name="NumOperation" Type="varchar" Nullable="false" MaxLength="300" />
    <Property Name="DateOperation" Type="datetime" Nullable="false" />
    <Property Name="Utilisateur" Type="varchar" Nullable="false" MaxLength="50" />
  </EntityType>
  <EntityType Name="MOUVEMENT_LOT_ARTICLE">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
    <Property Name="CodeArticle" Type="varchar" Nullable="false" MaxLength="50" />
    <Property Name="NumeroLotArticle" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="DatePeremptionArticle" Type="date" />
    <Property Name="AncienStock" Type="int" Nullable="false" />
    <Property Name="Qte" Type="int" Nullable="false" />
    <Property Name="NouveauStock" Type="int" Nullable="false" />
    <Property Name="TypeOperation" Type="varchar" Nullable="false" MaxLength="300" />
    <Property Name="TypeMouvement" Type="varchar" Nullable="false" MaxLength="300" />
    <Property Name="NumOperation" Type="varchar" Nullable="false" MaxLength="300" />
    <Property Name="DateOperation" Type="datetime" Nullable="false" />
    <Property Name="Utilisateur" Type="varchar" Nullable="false" MaxLength="50" />
    <Property Name="PrixAchatHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="PrixAchatTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="PrixVenteHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="PrixVenteTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TVA" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="Marge" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="HR" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="Remise" Type="decimal" Nullable="false" Scale="3" />
  </EntityType>
  <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « pharma.dbo.V_List_ArticlesRecherche » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
  <EntityType Name="V_List_ArticlesRecherche">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" />
    <Property Name="CodeArticle" Type="varchar" MaxLength="255" />
    <Property Name="CodeABarre" Type="varchar" MaxLength="255" />
    <Property Name="Designation" Type="varchar" MaxLength="255" />
    <Property Name="PrixVenteHT" Type="decimal" Scale="3" />
    <Property Name="PrixVenteTTC" Type="decimal" Scale="3" />
    <Property Name="PrixAchatHT" Type="decimal" Scale="3" />
    <Property Name="PrixAchatTTC" Type="decimal" Scale="3" />
    <Property Name="TVA" Type="decimal" Scale="3" />
    <Property Name="Stock" Type="int" />
    <Property Name="Rayon" Type="varchar" MaxLength="255" />
    <Property Name="LibelleForme" Type="varchar" MaxLength="255" />
    <Property Name="LibelleCategorie" Type="varchar" MaxLength="255" />
  </EntityType>
  <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « pharma.dbo.V_List_Inventaire » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
  <EntityType Name="V_List_Inventaire">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" />
    <Property Name="NumeroInventaire" Type="varchar" MaxLength="255" />
    <Property Name="CodeArticle" Type="varchar" MaxLength="255" />
    <Property Name="CodeABarre" Type="varchar" MaxLength="255" />
    <Property Name="Designation" Type="varchar" MaxLength="255" />
    <Property Name="StockInitial" Type="int" />
    <Property Name="StockActuel" Type="int" />
    <Property Name="PrixVenteTTC" Type="decimal" Scale="3" />
    <Property Name="PrixAchatTTC" Type="decimal" Scale="3" />
    <Property Name="TotalAchatTTC" Type="decimal" Scale="3" />
    <Property Name="QuantiteUnitaire" Type="numeric" Precision="6" />
  </EntityType>
  <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « pharma.dbo.V_List_LotArticleInventaire » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
  <EntityType Name="V_List_LotArticleInventaire">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" />
    <Property Name="CodeArticle" Type="varchar" MaxLength="255" />
    <Property Name="NumeroLotArticle" Type="varchar" MaxLength="255" />
    <Property Name="DatePeremptionArticle" Type="date" />
    <Property Name="QteLotArticle" Type="int" />
    <Property Name="Designation" Type="varchar" MaxLength="255" />
  </EntityType>
  <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « pharma.dbo.V_List_NouvelInventaire » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
  <EntityType Name="V_List_NouvelInventaire">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" />
    <Property Name="NumeroInventaire" Type="varchar" MaxLength="255" />
    <Property Name="CodeArticle" Type="varchar" MaxLength="255" />
    <Property Name="CodeABarre" Type="varchar" MaxLength="255" />
    <Property Name="Designation" Type="varchar" MaxLength="255" />
    <Property Name="StockInitial" Type="int" />
    <Property Name="StockActuel" Type="int" />
    <Property Name="PrixVenteTTC" Type="decimal" Scale="3" />
    <Property Name="PrixAchatTTC" Type="decimal" Scale="3" />
    <Property Name="TotalAchatTTC" Type="decimal" Scale="3" />
    <Property Name="QuantiteUnitaire" Type="numeric" Precision="6" />
  </EntityType>
  <Association Name="FK_INVENTAIRE_DETAILS_LOT_ARTICLE">
    <End Role="LOT_ARTICLE" Type="StockManagementModel.Store.LOT_ARTICLE" Multiplicity="1" />
    <End Role="INVENTAIRE_DETAILS" Type="StockManagementModel.Store.INVENTAIRE_DETAILS" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="LOT_ARTICLE">
        <PropertyRef Name="NumeroLotArticle" />
        <PropertyRef Name="CodeArticle" />
      </Principal>
      <Dependent Role="INVENTAIRE_DETAILS">
        <PropertyRef Name="NumeroLot" />
        <PropertyRef Name="CodeArticle" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_LOTS_ARTICLE_ARTICLE">
    <End Role="ARTICLE" Type="StockManagementModel.Store.ARTICLE" Multiplicity="1" />
    <End Role="LOT_ARTICLE" Type="StockManagementModel.Store.LOT_ARTICLE" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="ARTICLE">
        <PropertyRef Name="CodeArticle" />
      </Principal>
      <Dependent Role="LOT_ARTICLE">
        <PropertyRef Name="CodeArticle" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Function Name="P_List_ArticlesRecherche" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" />
  <Function Name="P_List_Inventaire" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="Tous" Type="bit" Mode="In" />
  </Function>
  <Function Name="P_List_LotArticleInventaire" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="CodeArticle" Type="varchar(max)" Mode="In" />
    <Parameter Name="DebutIntervalle" Type="varchar(max)" Mode="In" />
    <Parameter Name="FinIntervalle" Type="varchar(max)" Mode="In" />
    <Parameter Name="Forme" Type="varchar(max)" Mode="In" />
    <Parameter Name="Categorie" Type="varchar(max)" Mode="In" />
    <Parameter Name="Laboratoire" Type="varchar(max)" Mode="In" />
    <Parameter Name="Rayon" Type="varchar(max)" Mode="In" />
  </Function>
  <Function Name="P_List_NouvelInventaire" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="Section" Type="varchar(max)" Mode="In" />
    <Parameter Name="DebutIntervalle" Type="varchar(max)" Mode="In" />
    <Parameter Name="FinIntervalle" Type="varchar(max)" Mode="In" />
    <Parameter Name="Forme" Type="varchar(max)" Mode="In" />
    <Parameter Name="Categorie" Type="varchar(max)" Mode="In" />
    <Parameter Name="Laboratoire" Type="varchar(max)" Mode="In" />
    <Parameter Name="Rayon" Type="varchar(max)" Mode="In" />
  </Function>
</Schema>