//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class FOURNISSEUR
    {
        public FOURNISSEUR()
        {
            this.ACHAT_INSTANCE = new HashSet<ACHAT_INSTANCE>();
            this.COMMANDE_INSTANCE = new HashSet<COMMANDE_INSTANCE>();
            this.REGLEMENT_FOURNISSEUR = new HashSet<REGLEMENT_FOURNISSEUR>();
        }
    
        public string CodeFournisseur { get; set; }
        public string NomFournisseur { get; set; }
        public string Adresse { get; set; }
        public string CodePostal { get; set; }
        public string Tel { get; set; }
        public string Fax { get; set; }
        public string Remarque { get; set; }
        public decimal Remise { get; set; }
        public Nullable<int> CodeVille { get; set; }
        public int CodeBanque { get; set; }
        public string RIB { get; set; }
        public decimal SoldeInitial { get; set; }
        public System.DateTime DateInitiale { get; set; }
        public decimal ChiffreAffaire { get; set; }
        public decimal ChiffreAffairePourcent { get; set; }
        public bool Supprimer { get; set; }
    
        public virtual ICollection<ACHAT_INSTANCE> ACHAT_INSTANCE { get; set; }
        public virtual ICollection<COMMANDE_INSTANCE> COMMANDE_INSTANCE { get; set; }
        public virtual ICollection<REGLEMENT_FOURNISSEUR> REGLEMENT_FOURNISSEUR { get; set; }
    }
}
