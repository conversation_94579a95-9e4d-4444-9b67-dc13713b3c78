﻿Imports System
Imports System.Data
Imports System.Data.SqlClient
Imports System.Management
Imports System.IO
Imports System.Configuration

Public Class fLogin
    Dim cmd As New SqlCommand
    Dim cmdAcces As New SqlCommand
    Dim reader As SqlDataReader
    Public reussi As Boolean = False

    Dim dsHistorique As New DataSet
    Dim cmdHistorique As New SqlCommand
    Dim daHistorique As New SqlDataAdapter
    Dim cbHistorique As New SqlCommandBuilder

    Dim cmdChargementLogin As New SqlCommand
    Dim cbChargementLogin As New SqlCommandBuilder
    Dim dsChargementLogin As New DataSet
    Dim daChargementLogin As New SqlDataAdapter

    Dim Confirmation As Boolean = False ' variable pour que la deuxieme interpretation de la touche OK lors d'une inf manquante apres confirmation
    Dim ListeAdresseMac() As String
    Dim CodeActivation As String = ""
    Dim MouseIsDown As Boolean = False


    Private Sub bOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOK.Click

        Dim StrSQL As String = ""

        RelectureDesParametres()
        Try
            Dim Client As New FtpConnection()
            Dim FtpInstance As New FTP
            Client.Hostname = "************"
            Client.Username = "PHARMA2000"
            Client.Password = "Next;3U7+s4"
            FtpInstance.ConnectFTP(Client.Hostname, Client.Username, Client.Password)
            FtpInstance.DownloadFile("BCB", CodePharmacien & ".txt", LecteurUpdate + "\" + CodePharmacien & ".txt")
            Dim monStreamReader As StreamReader = New StreamReader(LecteurUpdate + "\" + CodePharmacien & ".txt")
            Dim ligne As String = monStreamReader.ReadLine()
            ligne = AES_Decrypt(ligne)
            Dim lignes() As String = Split(ligne, ";")
            cmd.Connection = ConnectionServeur
            cmd.CommandText = " Update PARAMETRE_PHARMACIE SET ActiverBCB = " & Quote(lignes(1)) & ", ActiverBCBDateFin = CONVERT(DATE, " & Quote(lignes(2)) & ", 103) "
            cmd.ExecuteNonQuery()
            monStreamReader.Close()
        Catch
            cmd.Connection = ConnectionServeur
            cmd.CommandText = " Update PARAMETRE_PHARMACIE SET ActiverBCB = 0"
            cmd.ExecuteNonQuery()
        End Try

        cmd.Connection = ConnectionServeur
        cmd.CommandText = " SELECT case when Version is null then '' else Version end FROM PARAMETRE_PHARMACIE"
        If cmd.ExecuteScalar() <> Application.ProductVersion Then
            MsgBox("Veiller installer la nouvelle version !", MsgBoxStyle.Critical, "Version")
            End
        End If

        'MsgBox("Le logiciel n'est pas encore activé." + vbCr + "Veuillez procéder à son activation pour pouvoir continuer !", MsgBoxStyle.Critical, "Activation")
        'tMpass.Focus()
        'Exit Sub
        'If Not LogicielActive Then
        'End If


        If cmbLogin.Text = "" Then
            MsgBox("Veuillez saisir votre login !", MsgBoxStyle.Critical, "Erreur")
            Confirmation = True
            cmbLogin.Focus()
            Exit Sub
        End If
        If tMpass.Text = "" Then
            MsgBox("Veuillez saisir votre mot de passe !", MsgBoxStyle.Critical, "Erreur")
            tMpass.Focus()
            Exit Sub
        End If

        If (cmbLogin.Text = "NEXT" Or cmbLogin.Text = "next") And (tMpass.Text = "TXENCGD" Or tMpass.Text = "txencgd") Then
            ModeADMIN = "ADMIN"
            CodeUtilisateur = "NEXT"
            NomUtilisateur = "NEXT"
            reussi = True
            GoTo AccesNext
        End If

        Dim adressseMac As String = GetSetting("PHARMA", "PHARMA", "AdresseMac", "")

        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = "SELECT count(*) FROM ADRESSE_MAC WHERE AdresseMAC=" + Quote(adressseMac)
        'If cmd.ExecuteScalar() = 0 Then
        '    MsgBox("Ce Poste n'est pas Autorisé !", MsgBoxStyle.Critical, "Autoristaion")
        '    End
        'End If

        cmd.Connection = ConnectionServeur
        cmd.CommandText = "SELECT * FROM UTILISATEUR WHERE Supprime = 0 AND Login=" + Quote(cmbLogin.Text)
        reader = cmd.ExecuteReader

        If reader.HasRows Then
            reader.Read()
            If Trim(reader("MotPass")) = Trim(tMpass.Text) Then

                reussi = True
                CodeUtilisateur = reader("CodeUtilisateur")
                NomUtilisateur = reader("Nom")
                If reader("Nom") = "ADMINISTRATEUR" Then
                    ModeADMIN = "ADMIN"
                Else
                    ModeADMIN = ""
                End If

            Else
                MsgBox("Le mot de passe que vous avez saisi est incorrect.", MsgBoxStyle.Critical, "Erreur")
                reussi = False
                Confirmation = True
                tMpass.Focus()
                tMpass.SelectAll()
            End If
        Else
            MsgBox("Le Login que vous avez saisi n'existe pas.", MsgBoxStyle.Critical, "Erreur")
            reussi = False
            Confirmation = True
            cmbLogin.Focus()
            cmbLogin.SelectAll()
        End If
        reader.Close()

AccesNext:

        Dim wassim As String
        wassim = DateTime.Now.ToString
        If reussi Then
            'Dim StrSQL As String = ""
            Dim dr As DataRow
            StrSQL = " SELECT TOP 0 * FROM HISTORIQUEACCES"
            cmdHistorique.Connection = ConnectionServeur
            cmdHistorique.CommandText = StrSQL
            daHistorique = New SqlDataAdapter(cmdHistorique)
            daHistorique.Fill(dsHistorique, "HISTORIQUEACCES")
            cbHistorique = New SqlCommandBuilder(daHistorique)

            With dsHistorique
                dr = .Tables("HISTORIQUEACCES").NewRow
                dr.Item("CodeUtilisateur") = CodeUtilisateur
                dr.Item("NomUtilisateur") = NomUtilisateur
                dr.Item("DateHeure") = wassim 'tDateHeure.Text
                .Tables("HISTORIQUEACCES").Rows.Add(dr)
            End With

            Try
                daHistorique.Update(dsHistorique, "HISTORIQUEACCES")
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                dsHistorique.Reset()
            End Try

            Me.Dispose()
        End If
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Me.Dispose()
    End Sub

    Private Sub tMpass_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tMpass.KeyUp
        If e.KeyCode = Keys.Enter And Confirmation = True Then
            Confirmation = False
            Exit Sub
        End If
        If e.KeyCode = Keys.Enter And Confirmation = False Then
            bOK_Click(sender, e)
        End If

    End Sub

    Private Sub tMpass_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tMpass.TextChanged
    End Sub

    Private Sub tLogin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tLogin.KeyUp
        If e.KeyCode = Keys.Enter And Confirmation = True Then
            Confirmation = False
            Exit Sub
        End If
        If e.KeyCode = Keys.Enter And Confirmation = False Then
            'bOK_Click(sender, e)
            tMpass.Focus()
        End If

    End Sub

    Private Sub tLogin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tLogin.TextChanged

    End Sub

    Private Sub fLogin_DragDrop(ByVal sender As Object, ByVal e As System.Windows.Forms.DragEventArgs) Handles Me.DragDrop
        Me.Location = PointToClient(New Point(e.X, e.Y))
    End Sub

    Private Sub fLogin_DragEnter(ByVal sender As Object, ByVal e As System.Windows.Forms.DragEventArgs) Handles Me.DragEnter
        e.Effect = DragDropEffects.Move
    End Sub

    Private Sub fLogin_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        Try
            Dim StrSQL As String = ""
            'chargement des utilisateurs
            StrSQL = "SELECT Login FROM UTILISATEUR WHERE Supprime = 0 ORDER BY Login ASC"
            cmdChargementLogin.Connection = ConnectionServeur
            cmdChargementLogin.CommandText = StrSQL
            daChargementLogin = New SqlDataAdapter(cmdChargementLogin)
            daChargementLogin.Fill(dsChargementLogin, "UTILISATEUR")
            cmbLogin.DataSource = dsChargementLogin.Tables("UTILISATEUR")
            cmbLogin.ValueMember = "Login"
            'cmbLogin.DisplayMember = "Login"
            cmbLogin.ColumnHeaders = False
            'cmbLogin.Splits(0).DisplayColumns("Login").Visible = False
            'cmbLogin.Splits(0).DisplayColumns("Login").Width = 10
            cmbLogin.ExtendRightColumn = True

            ''EcranDeDemarrage.Close()
            fMain.Activate()
            cmbLogin.Focus()


        Catch ex As Exception
        End Try

        AnalyseActivation()

    End Sub

    Private Sub cmbLogin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbLogin.KeyUp
        'If e.KeyCode = Keys.Enter Then
        '    tMpass.Focus()
        'End If

        If e.KeyCode = Keys.Enter Then
            cmbLogin.Text = cmbLogin.WillChangeToText
            tMpass.Focus()
        Else
            cmbLogin.OpenCombo()
        End If

    End Sub

    Private Sub bActiver_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bActiver.Click
        Dim MyCleRegistre As New fCleRegistre
        MyCleRegistre.ShowDialog()
        MyCleRegistre.Dispose()
        AnalyseActivation()
    End Sub

    Private Sub AnalyseActivation()

        ' Lecture des adresses mac des cartes installées
        FillNetworkAdapters()

        ' Lecture du code d'activation
        CodeActivation = GetSetting("PHARMA", "PHARMA", "CleActivation", "")

        ' Analyse du code d'activation
        Dim CleDecryptee As String = ""
        CleDecryptee = AES_Decrypt(CodeActivation, "NEXT4328")
        Dim ProduitActive As String = ""
        Dim ModeActive As String = ""
        Dim ClientActive As String = ""
        Dim AdresseMacActive As String = ""
        Dim ClientBase As String = ""
        Dim i As Integer = 0

        If InStr(CleDecryptee, "-") > 0 Then
            ProduitActive = Mid(CleDecryptee, 1, InStr(CleDecryptee, "-") - 1)
            CleDecryptee = Mid(CleDecryptee, InStr(CleDecryptee, "-") + 1)
        End If
        If InStr(CleDecryptee, "-") > 0 Then
            ModeActive = Mid(CleDecryptee, 1, InStr(CleDecryptee, "-") - 1)
            CleDecryptee = Mid(CleDecryptee, InStr(CleDecryptee, "-") + 1)
        End If
        If InStr(CleDecryptee, "-") > 0 Then
            ClientActive = Mid(CleDecryptee, 1, InStr(CleDecryptee, "-") - 1)
            CleDecryptee = Mid(CleDecryptee, InStr(CleDecryptee, "-") + 1)
        End If
        AdresseMacActive = CleDecryptee

        ' Lecture du nom du client à partir de la base
        Dim cmd As New SqlCommand
        cmd.Connection = ConnectionServeur
        cmd.CommandText = "SELECT MAX(ID) FROM ACTIVATION_CLIENT"
        If Not IsDBNull(cmd.ExecuteScalar) Then
            ClientBase = cmd.ExecuteScalar
            ClientBase = AES_Decrypt(ClientBase, "NEXT4328")
        End If

        LogicielActive = True
        If ProduitActive.ToUpper <> "PHARMA2000_PREMIUM".ToUpper Then
            LogicielActive = False
        End If
        If ClientActive.ToUpper <> ClientBase.ToUpper Then
            LogicielActive = False
        End If
        For i = 0 To ListeAdresseMac.Count - 1
            If ListeAdresseMac(i) = AdresseMacActive Then
                Exit For
            End If
        Next
        If i > ListeAdresseMac.Count - 1 Then
            LogicielActive = False
        End If

        If LogicielActive Then bActiver.Visible = False
        LogicielMulsociete = (ModeActive = "MULTI")

        'If LogicielMulsociete = False Then
        '    If cmbSociete.ListCount > 0 Then
        '        cmbSociete.SelectedIndex = 0
        '    End If
        'End If

    End Sub

    Private Sub FillNetworkAdapters()
        Dim i As Integer = 0
        Dim mc As ManagementClass
        'Creation d'une classe de gestion pour les adaptateurs de réseau 
        mc = New ManagementClass("Win32_NetworkAdapterConfiguration")
        'Collection des adaptateurs gérés par la classe de gestion. 
        Dim moc As ManagementObjectCollection = mc.GetInstances()
        'Recherche des éléments actives qui ont des adresses IP. 
        ReDim ListeAdresseMac(moc.Count - 1)
        For Each o As ManagementObject In moc
            ' If DirectCast(o.GetPropertyValue("IPEnabled"), Boolean) = True Then

            Dim strAdapter As String
            ' Prendre le nom de l'adaptateur. 
            strAdapter = o.GetPropertyValue("Caption").ToString()
            ' Ajout dans la liste des adresses mc
            ListeAdresseMac(i) = GetMACAddress(strAdapter)
            i += 1

            '   End If
        Next
    End Sub

    'Cette fonction se charge de chercher l'addresse MAC d'une carte réseau sur la machine locale. 
    Private Function GetMACAddress(ByVal Adapter As String) As String
        Dim mc As ManagementClass
        mc = New ManagementClass("Win32_NetworkAdapterConfiguration")
        Dim moc As ManagementObjectCollection = mc.GetInstances()
        'On cherche l'adaptateur passé en parametre pour la fonction 
        For Each o As ManagementObject In moc
            'If DirectCast(o.GetPropertyValue("IPEnabled"), Boolean) = True Then
            Dim strAdapter As String
            strAdapter = o.GetPropertyValue("Caption").ToString()
            'S'il est trouvé
            If strAdapter = Adapter Then
                If Not o.GetPropertyValue("MacAddress") Is Nothing Then
                    Return o.GetPropertyValue("MacAddress").ToString()
                End If

            End If
            '  End If
        Next
        ' Sinon 
        Return String.Empty
    End Function

    Private Sub fLogin_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Me.MouseDown
        MouseIsDown = True
    End Sub

    Private Sub fLogin_MouseMove(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Me.MouseMove
        If MouseIsDown Then
            Me.DoDragDrop(Me, DragDropEffects.Move)
        End If
        MouseIsDown = False

    End Sub
End Class