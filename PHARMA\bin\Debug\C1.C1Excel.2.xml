<?xml version="1.0"?>
<doc>
  <assembly>
    <name>C1.C1Excel.2</name>
  </assembly>
  <members>
    <member name="T:C1.C1Excel.FileFormat">
      <summary>
            Specifies the file format to use when loading or saving workbooks.
            </summary>
    </member>
    <member name="F:C1.C1Excel.FileFormat.Biff8">
      <summary>
            Excel 97/2003 format (Binary Interchange File Format revision 8).
            </summary>
    </member>
    <member name="F:C1.C1Excel.FileFormat.OpaqueBiff8">
      <summary>
            Excel 97/2003 format (Binary Interchange File Format revision 8) without parsing opaque table.
            </summary>
    </member>
    <member name="F:C1.C1Excel.FileFormat.OpenXml">
      <summary>
            Office 2007/2013 format. (Compressed Xml format).
            </summary>
    </member>
    <member name="F:C1.C1Excel.FileFormat.OpenXmlTemplate">
      <summary>
            Office 2007/2013 template format. (Compressed Xml format).
            </summary>
    </member>
    <member name="F:C1.C1Excel.FileFormat.OpenXmlMacro">
      <summary>
            Office 2007/2013 format with enable VBA macro. (Compressed Xml format).
            </summary>
    </member>
    <member name="F:C1.C1Excel.FileFormat.Csv">
      <summary>
            Simple Comma-separated values (CSV) format.
            </summary>
    </member>
    <member name="T:C1.C1Excel.CompatibilityMode">
      <summary>
            Specifies limits to sheet size that correspond to specific versions
            of Microsoft Excel.
            </summary>
    </member>
    <member name="F:C1.C1Excel.CompatibilityMode.Excel2003">
      <summary>
            Sheets may have up to 65,536 rows and 256 columns. Workbooks may have up to 4050 unique cell styles.
            </summary>
    </member>
    <member name="F:C1.C1Excel.CompatibilityMode.Excel2007">
      <summary>
        <para>Sheets may have up to 1,048,576 rows and 18,278 columns. Workbooks may have up to 65,536 unique cell styles.</para>
        <para>Workbooks that exceed the Excel2003 limits must be saved in OpenXml format instead of XLS.</para>
        <para>It may not be possible to open these workbooks with versions earlier than Excel 2007 (C1Excel will still open them correctly).</para>
      </summary>
    </member>
    <member name="F:C1.C1Excel.CompatibilityMode.NoLimits">
      <summary>
        <para>No size limits are enforced.</para>
        <para>It may not be possible to open these workbooks with any version of Excel (C1Excel will still open them correctly).</para>
      </summary>
    </member>
    <member name="T:C1.C1Excel.CalculationMode">
      <summary>
            Specifies calculation mode for all formulas in the workbook.
            </summary>
    </member>
    <member name="F:C1.C1Excel.CalculationMode.Manual">
      <summary>Manual calculation mode for all formulas in the workbook (F9 in MS Excel).</summary>
    </member>
    <member name="F:C1.C1Excel.CalculationMode.Auto">
      <summary>Automatic calculation mode for all formulas in the workbook.</summary>
    </member>
    <member name="F:C1.C1Excel.CalculationMode.AutoNoTable">
      <summary>Automatic no table calculation mode for all formulas in the workbook.</summary>
    </member>
    <member name="T:C1.C1Excel.C1XLBook">
      <summary>
        <para>Represents an Excel workbook containing one or more worksheets.</para>
        <para>Contains methods for loading and saving XLS files, and exposes
            a collection of <see cref="T:C1.C1Excel.XLSheet" /> objects that represent the individual
            worksheets.</para>
      </summary>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.#ctor">
      <summary>
            Creates a new instance of the <see cref="T:C1.C1Excel.C1XLBook" /> class. The 
            new workbook contains a single empty <see cref="T:C1.C1Excel.XLSheet" /> called "Sheet1".
            </summary>
    </member>
    <member name="P:C1.C1Excel.C1XLBook.Sheets">
      <summary>
            Gets a collection of <see cref="T:C1.C1Excel.XLSheet" /> objects that represent
            the worksheets in the <see cref="T:C1.C1Excel.C1XLBook" />.
            </summary>
      <remarks>
            The <see cref="T:C1.C1Excel.XLSheetCollection" /> returned has methods for counting, 
            enumerating, adding and removing sheets from the <see cref="T:C1.C1Excel.C1XLBook" />.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.C1XLBook.DefaultFont">
      <summary>
            Gets or sets the default font object for the <see cref="T:C1.C1Excel.C1XLBook" />.
            </summary>
      <remarks>
        <para>You can assign any font to any cell using <see cref="T:C1.C1Excel.XLStyle" /> objects. Cells
            that have no associated custom styles or have styles that do not define
            a custom font are displayed using the book's default font.</para>
        <para>The <see cref="P:C1.C1Excel.C1XLBook.DefaultFont" /> property is initially set to 10 pt Arial.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.C1XLBook.KeepFormulas">
      <summary>
            Specifies whether the component should store formulas read from XLS/XLSX files
            and write them back when saving the file.
            </summary>
      <remarks>
        <para>Setting this property to true allows you to load existing XLS/XLSX files, modify the values 
            in some cells, and save the file preserving the formulas. This is the default setting.</para>
        <para>Setting this property to false causes the component to remove the formulas in the book 
            when it is loaded. Saving the file in this case will retain the last calculated values but 
            will remove the formulas.</para>
        <para>Assigning any value to a cell will clear the formula in the cell.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.C1XLBook.OpaqueCopy">
      <summary>
            Gets or sets a value specifying whether the component should copy non-main BIFF records as opaque when loading and saving XLS files.
            </summary>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.Clear">
      <summary>
            Clears the <see cref="T:C1.C1Excel.C1XLBook" />, restoring the initial state with a single
            <see cref="T:C1.C1Excel.XLSheet" /> called "Sheet1".
            </summary>
      <remarks>
            The <see cref="M:C1.C1Excel.C1XLBook.Clear" /> method restores the <see cref="T:C1.C1Excel.C1XLBook" /> object
            to its initial state, with a single empty sheet called "Sheet1" and the 
            <see cref="P:C1.C1Excel.C1XLBook.DefaultFont" /> set to 10pt Arial.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.C1XLBook.Author">
      <summary>
            Gets or sets the name of the person, company, or application that created this <see cref="T:C1.C1Excel.C1XLBook" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.C1XLBook.ListSeparator">
      <summary>
            Gets or sets a list separator that used for CSV-data, by default used the list separator in the Windows system.
            </summary>
    </member>
    <member name="P:C1.C1Excel.C1XLBook.UseTempStorage">
      <summary>
            Specifies whether the component should use temporary storage at saving to XLSX/XSLT files for minimization used memory.
            </summary>
    </member>
    <member name="P:C1.C1Excel.C1XLBook.CalculationMode">
      <summary>
            Gets or sets the formula calculation mode.
            </summary>
    </member>
    <member name="P:C1.C1Excel.C1XLBook.NamedRanges">
      <summary>
            Gets the collection of <see cref="T:C1.C1Excel.XLNamedRange" /> objects for the current workbook.
            </summary>
    </member>
    <member name="P:C1.C1Excel.C1XLBook.Palette">
      <summary>
            Gets or sets color palette of this workbook.
            </summary>
      <remarks>The palette must be more 8 items (first 8 colors is standard pallete: Black, White, Red, Green, Blue, Yellow, Magenta, Cyan).</remarks>
    </member>
    <member name="P:C1.C1Excel.C1XLBook.IsLoading">
      <summary>
            Determines whether the workbook is currently loading.
            </summary>
    </member>
    <member name="P:C1.C1Excel.C1XLBook.CompatibilityMode">
      <summary>
            Gets or sets a value determining the limits on sheet size and the number of styles allowed per workbook.
            </summary>
      <remarks>
        <para>This property allows you to specify which version of Microsoft Excel 
            you want your workbooks to be compatible with.</para>
        <para>
          <b>Excel2003</b> mode allows you to create sheets with up to 65,536 rows and 256 columns.
            <b>Excel2007</b> mode allows you to create sheets with up to 1,048,576 rows and 18,278 columns.</para>
        <para>Note that the XLS file format is limited by the <b>Excel2003</b> limits. If you
            use the <b>Excel2007</b> mode and create large sheets, save them into <b>OpenXml</b> files
            instead of XLS.</para>
      </remarks>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.Load(System.String)">
      <summary>
            Loads an Excel worksheet from a file.
            </summary>
      <param name="fileName">Name of the file that contains the worksheet.</param>
      <remarks>
        <para>Component One Excel infers the file format automatically based on the 
            file name extension. "XLSX" and "ZIP" files are loaded as OpenXml; all 
            others are loaded as Biff8 files ("xls").</para>
        <para>If the file doesn't exist, is locked, or is not a valid Excel 
            file, an exception is thrown.</para>
      </remarks>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.Load(System.String,System.Boolean)">
      <summary>
            Loads an Excel worksheet from a file.
            </summary>
      <param name="fileName">Name of the file that contains the worksheet.</param>
      <param name="fillSheets">True to load data into the sheets; False to read the sheet names only.</param>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.Load(System.String,C1.C1Excel.FileFormat)">
      <summary>
            Loads an Excel worksheet from a file.
            </summary>
      <param name="fileName">Name of the file that contains the worksheet.</param>
      <param name="format">
        <see cref="P:C1.C1Excel.C1XLBook.FileFormat" /> value that specifies the file format.</param>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.Load(System.String,C1.C1Excel.FileFormat,System.Boolean)">
      <summary>
            Loads an Excel worksheet from a file.
            </summary>
      <param name="fileName">Name of the file that contains the worksheet.</param>
      <param name="format">
        <see cref="P:C1.C1Excel.C1XLBook.FileFormat" /> value that specifies the file format.</param>
      <param name="fillSheets">True to load data into the sheets; False to read the sheet names only.</param>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.Save(System.String)">
      <summary>
            Saves the worksheet to a file.
            </summary>
      <param name="fileName">Name of the file to save.</param>
      <remarks>
        <para>The format used to save the file is automatically determined by the 
            file name extension. "Xlsx" and "zip" files are saved as OpenXml; all others 
            are saved as Biff8 files ("xls").</para>
        <para>If the file can't be created, an exception is thrown. 
            This typically indicates that the file is currently open by another 
            application (such as Microsoft Excel).</para>
      </remarks>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.Save(System.String,C1.C1Excel.FileFormat)">
      <summary>
            Saves the worksheet to a file.
            </summary>
      <param name="fileName">Name of the file to save.</param>
      <param name="format">
        <see cref="P:C1.C1Excel.C1XLBook.FileFormat" /> value that specifies the type of file to save.</param>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.Load(System.IO.Stream)">
      <summary>
            Loads the worksheet from a stream.
            </summary>
      <param name="stream">
        <see cref="T:System.IO.Stream" /> that contains the worksheet.</param>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.Load(System.IO.Stream,System.Boolean)">
      <summary>
            Loads the worksheet from a stream.
            </summary>
      <param name="stream">
        <see cref="T:System.IO.Stream" /> that contains the worksheet.</param>
      <param name="fillSheets">True to load data into the sheets; False to read the sheet names only.</param>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.Load(System.IO.Stream,C1.C1Excel.FileFormat)">
      <summary>
            Loads the worksheet from a stream.
            </summary>
      <param name="stream">
        <see cref="T:System.IO.Stream" /> that contains the worksheet.</param>
      <param name="format">
        <see cref="P:C1.C1Excel.C1XLBook.FileFormat" /> value that specifies the file format.</param>
      <remarks>
            Loading the worksheets without their data is much faster than loading the entire workbook.
            This is useful in situations where you want to examine the contents of the file (for example, 
            to ensure that you will not overwrite an existing sheet).
            </remarks>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.Load(System.IO.Stream,C1.C1Excel.FileFormat,System.Boolean)">
      <summary>
            Loads the worksheet from a stream.
            </summary>
      <param name="stream">
        <see cref="T:System.IO.Stream" /> that contains the worksheet.</param>
      <param name="format">
        <see cref="P:C1.C1Excel.C1XLBook.FileFormat" /> value that specifies the file format.</param>
      <param name="fillSheets">
        <b>True</b> to load data into the sheets; <b>False</b> to read the sheet names only.</param>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.Save(System.IO.Stream)">
      <summary>
            Saves the worksheet into a stream.
            </summary>
      <param name="stream">
        <see cref="T:System.IO.Stream" /> where the worksheet is saved.</param>
      <remarks>
            This method allows saving the workbook directly into streams without using 
            temporary files. Typical uses include saving books to web page response streams
            or mail attachment streams.
            </remarks>
      <example>
            The code below saves a <see cref="T:C1.C1Excel.C1XLBook" /> into a <see cref="T:System.IO.MemoryStream" />, clears
            the book, then loads it back from the same stream.
            <code>
            // save book into new MemoryStream
            MemoryStream ms = new MemoryStream();
            _book.Save(ms);
            
            // clear book
            _book.Clear();
            
            // load it back from the MemoryStream
            ms.Position = 0;
            _book.Load(ms);
            </code></example>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.Save(System.IO.Stream,C1.C1Excel.FileFormat)">
      <summary>
            Saves the worksheet into a stream.
            </summary>
      <param name="stream">
        <see cref="T:System.IO.Stream" /> where the worksheet is saved.</param>
      <param name="format">
        <see cref="P:C1.C1Excel.C1XLBook.FileFormat" /> value that specifies the format to save the worksheet in.</param>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.PixelsToTwips(System.Double)">
      <summary>
            Converts a pixel measurement into twips (1/20th of a point).
            </summary>
      <param name="pix">Measurement in screen pixels.</param>
      <returns>Measurement in twips.</returns>
      <remarks>
        <para>Excel stores measurements in twips (1/20th of a point), a 
            resolution-independent unit. .NET controls, on the other hand, usually 
            express measurements in pixels. This method provides an easy way to 
            convert pixel measurements into twips using the current screen resolution.</para>
        <seealso cref="M:C1.C1Excel.C1XLBook.TwipsToPixels(System.Double)" />
      </remarks>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.TwipsToPixels(System.Double)">
      <summary>
            Converts a twip measurement into screen pixels.
            </summary>
      <param name="twip">Measurement in twips.</param>
      <returns>Measurement in screen pixels.</returns>
      <remarks>
        <para>Excel stores measurements in twips (1/20th of a point), 
            a resolution-independent unit. .NET controls, on the other hand, 
            usually express measurements in pixels. This method provides an 
            easy way to convert pixel measurements into twips using the 
            current screen resolution.</para>
        <para>
          <see cref="M:C1.C1Excel.C1XLBook.PixelsToTwips(System.Double)" />
        </para>
      </remarks>
    </member>
    <member name="M:C1.C1Excel.C1XLBook.Clone">
      <summary>
            Creates a copy of this C1Excel book.
            </summary>
      <returns>A new <see cref="T:C1.C1Excel.C1XLBook" /> object with the same contents and formatting as this book.</returns>
    </member>
    <member name="T:C1.C1Excel.XLOpaqueShape">
      <summary>
            Represents a shape that was inserted in the sheet using Excel 
            and is preserved but not fully exposed by the <see cref="T:C1.C1Excel.C1XLBook" /> component.
            </summary>
      <remarks>
        <para>
          <b>C1ExcelBook</b> can load and save all types of shapes present in Excel sheets.</para>
        <para>However, only image shapes are fully exposed (as <see cref="T:C1.C1Excel.XLPictureShape" /> objects).</para>
        <para>All other object types are loaded and saved as <b>XLOpaqueShape</b> objects that cannot be modified.
            These include graphical elements (such as lines, rectangles, and arcs), VBA controls (such as edit 
            boxes, and buttons), and comments.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLOpaqueShape.IsEmpty">
      <summary>
            Determines whether the shape is empty.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLOpaqueShape.ParseProperties(System.Collections.IDictionary)">
      <summary>
            Parse shape properties.
            </summary>
      <param name="properties">
        <see cref="T:System.Collections.IDictionary" /> that contains the shape properties.</param>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> that contains not handled the shape properties.</returns>
    </member>
    <member name="M:C1.C1Excel.XLOpaqueShape.CreateProperties(System.Collections.IDictionary)">
      <summary>
            Populates an <see cref="T:System.Collections.IDictionary" /> with the shape properties.
            </summary>
      <param name="properties">
        <see cref="T:System.Collections.IDictionary" /> where the shape properties are stored.</param>
    </member>
    <member name="T:C1.C1Excel.ImageScaling">
      <summary>
            Specifies how images are scaled within spreadsheet cells.
            </summary>
    </member>
    <member name="F:C1.C1Excel.ImageScaling.None">
      <summary>
            Images are rendered in their original size, regardless of cell size.
            </summary>
    </member>
    <member name="F:C1.C1Excel.ImageScaling.Clip">
      <summary>
            Images are clipped to fit within the cell.
            </summary>
    </member>
    <member name="F:C1.C1Excel.ImageScaling.Scale">
      <summary>
            Images are scaled to fill the cell while preserving their original aspect ratio.
            </summary>
    </member>
    <member name="F:C1.C1Excel.ImageScaling.Stretch">
      <summary>
            Images are stretched to fill the cell.
            </summary>
    </member>
    <member name="T:C1.C1Excel.XLPictureShape">
      <summary>
            Represents an <see cref="P:C1.C1Excel.XLPictureShape.Image" /> embedded in an <see cref="T:C1.C1Excel.XLSheet" />.
            </summary>
      <remarks>
        <para>
          <b>XLPictureShape</b> derives from the generic <see cref="T:C1.C1Excel.XLShape" /> class
            to expose properties of images embedded in sheets. These properties include
            the actual <see cref="P:C1.C1Excel.XLPictureShape.Image" /> as well as information on how it should be
            displayed, including <see cref="P:C1.C1Excel.XLPictureShape.Brightness" />, <see cref="P:C1.C1Excel.XLPictureShape.Contrast" />, and
            clipping information.</para>
        <para>You can add images to cells simply by assigning <see cref="P:C1.C1Excel.XLPictureShape.Image" /> objects
            directly to the <see cref="P:C1.C1Excel.XLCell.Value" /> property of <see cref="T:C1.C1Excel.XLCell" /> objects. In 
            this case, <see cref="T:C1.C1Excel.C1XLBook" /> will create and initialize an <b>XLPictureShape</b>
            automatically. However, this method does not provide a lot of flexibility in terms
            of aligning, scaling, and clipping the image.</para>
        <para>A more flexible option is to create an <b>XLPictureShape</b> object in code,
            using the constructor that takes alignment and scaling parameters, and then
            assign this <b>XLPictureShape</b> object to a cell's <see cref="P:C1.C1Excel.XLCell.Value" />
            property.</para>
      </remarks>
    </member>
    <member name="M:C1.C1Excel.XLPictureShape.#ctor(System.Drawing.Image,System.Int32,System.Int32)">
      <summary>
            Initializes a new instance of an <see cref="T:C1.C1Excel.XLPictureShape" />.
            </summary>
      <param name="img">The image contained in the new <see cref="T:C1.C1Excel.XLPictureShape" />.</param>
      <param name="x">The horizontal position of the new shape with respect to the cell, in twips.</param>
      <param name="y">The vertical position of the new shape with respect to the cell, in twips.</param>
      <example>
            The code below adds an image to a cell. The image is rendered in its original size, and is
            indented from the top left corner of the cell by 30 twips:
            <code>
            // get sheet and cell
            XLSheet sheet = c1ExcelBook1.Sheets[0];
            XLCell  cell  = sheet[row, col];
            
            // build XLPictureShape
            XLPictureShape pic = new XLPictureShape(image, 30, 30);
            
            // assign XLPictureShape to cell
            cell.Value = pic;
            </code></example>
    </member>
    <member name="M:C1.C1Excel.XLPictureShape.#ctor(System.Drawing.Image,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>
            Initializes a new instance of an <see cref="T:C1.C1Excel.XLPictureShape" />.
            </summary>
      <param name="img">The image contained in the new <see cref="T:C1.C1Excel.XLPictureShape" />.</param>
      <param name="x">The horizontal position of the image with respect to the cell, in twips.</param>
      <param name="y">The vertical position of the image with respect to the cell, in twips.</param>
      <param name="width">The width of the image, in twips.</param>
      <param name="height">The height of the image, in twips.</param>
      <example>
            The code below adds an image to a cell. The image is drawn within a rectangle centered on a 
            cell with a 60 twip edge around it:
            <code>
            // get sheet and cell
            XLSheet sheet = c1ExcelBook1.Sheets[0];
            XLCell  cell  = sheet[row, col];
            
            // calculate cell size to align picture
            Rectangle rc = new Rectangle(0, 0
                sheet.Columns[col].Width, 
                sheet.Rows[row].Height);
            
            // add 60 twip edge
            rc.Inflate(-60, -60);
            
            // build XLPictureShape
            XLPictureShape pic = new XLPictureShape(image, 
                rc.X, rc.Y, rc.Width, rc.Height);
            
            // assign XLPictureShape to cell
            cell.Value = pic;
            </code></example>
    </member>
    <member name="M:C1.C1Excel.XLPictureShape.#ctor(System.Drawing.Image,System.Drawing.Rectangle)">
      <summary>
            Initializes a new instance of an <see cref="T:C1.C1Excel.XLPictureShape" />.
            </summary>
      <param name="img">The image contained in the new <see cref="T:C1.C1Excel.XLPictureShape" />.</param>
      <param name="rc">The rectangle that specifies the image size and position 
            with respect to the cell, in twips.</param>
      <example>
            The code below adds an image to a cell. The image is drawn within a rectangle centered on a 
            cell with a 60 twip edge around it:
            <code>
            // get sheet and cell
            XLSheet sheet = c1ExcelBook1.Sheets[0];
            XLCell  cell  = sheet[row, col];
            
            // calculate cell size to align picture
            Rectangle rc = new Rectangle(0, 0
                sheet.Columns[col].Width, 
                sheet.Rows[row].Height);
            
            // add 60 twip edge
            rc.Inflate(-60, -60);
            
            // build XLPictureShape
            XLPictureShape pic = new XLPictureShape(image, rc);
            
            // assign XLPictureShape to cell
            cell.Value = pic;
            </code></example>
    </member>
    <member name="M:C1.C1Excel.XLPictureShape.#ctor(System.Drawing.Image)">
      <summary>
            Initializes a new instance of an <see cref="T:C1.C1Excel.XLPictureShape" />.
            </summary>
      <param name="img">The image contained in the new <see cref="T:C1.C1Excel.XLPictureShape" />.</param>
    </member>
    <member name="M:C1.C1Excel.XLPictureShape.#ctor(System.Drawing.Image,System.Drawing.Size,System.Drawing.ContentAlignment,C1.C1Excel.ImageScaling)">
      <summary>
            Initializes a new instance of an <see cref="T:C1.C1Excel.XLPictureShape" />.
            </summary>
      <param name="img">The <see cref="P:C1.C1Excel.XLPictureShape.Image" /> contained in the new <see cref="T:C1.C1Excel.XLPictureShape" />.</param>
      <param name="cellSize">The size of the cell that will contain the image, in pixels (used for aligning the image).</param>
      <param name="align">A <see cref="T:System.Drawing.ContentAlignment" /> value that specifies the position of the image in the cell.</param>
      <param name="scale">An <see cref="T:C1.C1Excel.ImageScaling" /> value that specifies the image scaling within the cell.</param>
      <remarks>
            This constructor automatically calculates the image size, position, and clipping based on the cell and image sizes
            and on the given alignment and scaling parameters.
            </remarks>
      <example>
            The code below adds an image to a cell. The image is centered within the cell and scaled to 
            fill the cell while preserving its aspect ratio.
            <code>
            // get sheet and cell
            XLSheet sheet = c1ExcelBook1.Sheets[0];
            XLCell  cell  = sheet[row, col];
            
            // calculate cell size to align picture
            Size cellSize = new Size(
                sheet.Columns[col].Width, 
                sheet.Rows[row].Height);
            
            // build XLPictureShape
            XLPictureShape pic = new XLPictureShape(
                image,
                cellSize,
                ContentAlignment.MiddleCenter,
                ImageScaling.Scale);
            
            // assign XLPictureShape to cell
            cell.Value = pic;
            </code></example>
    </member>
    <member name="M:C1.C1Excel.XLPictureShape.#ctor(C1.C1Excel.XLSheet,System.Drawing.Image,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>
            Initializes a new instance of an <see cref="T:C1.C1Excel.XLPictureShape" />.
            </summary>
      <param name="sheet">
        <see cref="T:C1.C1Excel.XLSheet" /> object that owns the new shape.</param>
      <param name="img">The image contained in the new <see cref="T:C1.C1Excel.XLPictureShape" />.</param>
      <param name="x">The horizontal position of the image with respect to the sheet, in twips.</param>
      <param name="y">The vertical position of the image with respect to the sheet, in twips.</param>
      <param name="width">The width of the image, in twips.</param>
      <param name="height">The height of the image, in twips.</param>
    </member>
    <member name="P:C1.C1Excel.XLPictureShape.IsEmpty">
      <summary>
            Determines whether the shape is empty.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPictureShape.Image">
      <summary>
            Gets a reference to the <see cref="P:C1.C1Excel.XLPictureShape.Image" /> contained in this <see cref="T:C1.C1Excel.XLPictureShape" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPictureShape.ViewType">
      <summary>
            Gets or sets whether this <see cref="T:C1.C1Excel.XLPictureShape" /> should be displayed in color, grayscale, or black and white.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPictureShape.Brightness">
      <summary>
            Gets or sets the brightness of this <see cref="T:C1.C1Excel.XLPictureShape" /> (between 0 and 1).
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPictureShape.Contrast">
      <summary>
            Gets or sets the contrast of this <see cref="T:C1.C1Excel.XLPictureShape" /> (between 0 and 1).
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPictureShape.BrightnessInPercents">
      <summary>
            Gets or sets the brightness in percents of this <see cref="T:C1.C1Excel.XLPictureShape" /> (between -100 and 100).
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPictureShape.ContrastInPercents">
      <summary>
            Gets or sets the contrast in percents of this <see cref="T:C1.C1Excel.XLPictureShape" /> (between -100 and 100).
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPictureShape.Transparent">
      <summary>
            Gets or sets the transparent color of this <see cref="T:C1.C1Excel.XLPictureShape" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPictureShape.LeftClip">
      <summary>
            Gets or sets the left clipping area of this <see cref="T:C1.C1Excel.XLPictureShape" />, in twips.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPictureShape.RightClip">
      <summary>
            Gets or sets the right clipping area of this <see cref="T:C1.C1Excel.XLPictureShape" />, in twips.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPictureShape.TopClip">
      <summary>
            Gets or sets the top clipping area of this <see cref="T:C1.C1Excel.XLPictureShape" />, in twips.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPictureShape.BottomClip">
      <summary>
            Gets or sets the bottom clipping area of this <see cref="T:C1.C1Excel.XLPictureShape" />, in twips.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPictureShape.ImageSize">
      <summary>
            Gets image size in twips.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPictureShape.FileName">
      <summary>
            Gets or sets the file name of this <see cref="T:C1.C1Excel.XLPictureShape" />.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLPictureShape.ParseProperties(System.Collections.IDictionary)">
      <summary>
            Parse shape properties.
            </summary>
      <param name="properties">
        <see cref="T:System.Collections.IDictionary" /> that contains the shape properties.</param>
    </member>
    <member name="M:C1.C1Excel.XLPictureShape.CreateProperties(System.Collections.IDictionary)">
      <summary>
            Populates an <see cref="T:System.Collections.IDictionary" /> with the shape properties.
            </summary>
      <param name="properties">
        <see cref="T:System.Collections.IDictionary" /> where the shape properties are stored.</param>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> that contains not handled the shape properties.</returns>
    </member>
    <member name="T:C1.C1Excel.ShapeCollection">
      <summary>
            Represents a collection of <see cref="T:C1.C1Excel.XLShape" /> objects on an <see cref="T:C1.C1Excel.XLSheet" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.ShapeCollection.Item(System.Int32)">
      <summary>
            Gets the <see cref="T:C1.C1Excel.XLShape" /> object at the specified position in the collection.
            </summary>
    </member>
    <member name="P:C1.C1Excel.ShapeCollection.Count">
      <summary>
            Gets count items in collection.
            </summary>
    </member>
    <member name="P:C1.C1Excel.ShapeCollection.Sheet">
      <summary>
            Gets the <see cref="P:C1.C1Excel.ShapeCollection.Sheet" /> object that owns the collection.
            </summary>
    </member>
    <member name="M:C1.C1Excel.ShapeCollection.Contains(C1.C1Excel.XLShape)">
      <summary>
            Checks whether the collection contains a specific <see cref="T:C1.C1Excel.XLShape" /> object.
            </summary>
      <param name="shape">
        <see cref="T:C1.C1Excel.XLShape" /> object to look for.</param>
      <returns>True if the collection contains the range, False otherwise.</returns>
    </member>
    <member name="M:C1.C1Excel.ShapeCollection.Contains(System.Int32)">
      <summary>
            Checks whether the collection contains a specific <see cref="T:C1.C1Excel.XLShape" /> object.
            </summary>
      <param name="id">The identifier of the object to look for.</param>
      <returns>True if the collection contains the range, False otherwise.</returns>
    </member>
    <member name="M:C1.C1Excel.ShapeCollection.IndexOf(C1.C1Excel.XLShape)">
      <summary>
            Gets the position of an <see cref="T:C1.C1Excel.XLShape" /> object in the collection.
            </summary>
      <param name="shape">
        <see cref="T:C1.C1Excel.XLShape" /> object to look for.</param>
      <returns>The position of the object in the collection, or -1 if the object is not a member of the collection.</returns>
    </member>
    <member name="M:C1.C1Excel.ShapeCollection.IndexOf(System.Int32)">
      <summary>
            Gets the position of an <see cref="T:C1.C1Excel.XLShape" /> object in the collection.
            </summary>
      <param name="id">The identifier of the object to look for.</param>
      <returns>The position of the object in the collection, or -1 if the object is not a member of the collection.</returns>
    </member>
    <member name="M:C1.C1Excel.ShapeCollection.Remove(C1.C1Excel.XLShape)">
      <summary>
            Removes an <see cref="T:C1.C1Excel.XLShape" /> object from the collection.
            </summary>
      <param name="shape">
        <see cref="T:C1.C1Excel.XLShape" /> object to remove from the collection.</param>
    </member>
    <member name="M:C1.C1Excel.ShapeCollection.Remove(System.Int32)">
      <summary>
            Removes an <see cref="T:C1.C1Excel.XLShape" /> object from the collection.
            </summary>
      <param name="id">The identifier of the object  to remove from the collection.</param>
    </member>
    <member name="M:C1.C1Excel.ShapeCollection.Add(C1.C1Excel.XLShape)">
      <summary>
            Appends an <see cref="T:C1.C1Excel.XLShape" /> object to the collection.
            </summary>
      <param name="shape">
        <see cref="T:C1.C1Excel.XLShape" /> object to add to the collection.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added to the collection (usually because it 
            overlaps another cell range already in the collection).</returns>
    </member>
    <member name="M:C1.C1Excel.ShapeCollection.Insert(System.Int32,C1.C1Excel.XLShape)">
      <summary>
            Inserts an <see cref="T:C1.C1Excel.XLShape" /> object at a specific position in the 
            collection.
            </summary>
      <param name="index">Position where the object will be inserted.</param>
      <param name="shape">Object to insert in the collection.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added to the collection (usually because it 
            overlaps another cell range already in the collection).</returns>
    </member>
    <member name="T:C1.C1Excel.XLShape">
      <summary>
            Represents a shape embedded in an <see cref="T:C1.C1Excel.XLSheet" />.
            </summary>
      <remarks>
        <para>Excel sheets may contain many types of embedded shapes, including 
            images, graphical elements, controls, and comments.</para>
        <para>The <b>XLShape</b> abstract class contains information that is common
            to all shape types, including the shape's location (<see cref="P:C1.C1Excel.XLShape.Sheet" />, <see cref="P:C1.C1Excel.XLShape.Row" />, 
            <see cref="P:C1.C1Excel.XLShape.Column" />, <see cref="P:C1.C1Excel.XLShape.Rectangle" />, <see cref="P:C1.C1Excel.XLShape.Rotation" />),
            the type of border drawn around the shape (<see cref="P:C1.C1Excel.XLShape.LineWidth" />, <see cref="P:C1.C1Excel.XLShape.LineColor" />, 
            <see cref="P:C1.C1Excel.XLShape.LineStyle" />), the <see cref="P:C1.C1Excel.XLShape.Rotation" /> applied to the shape, and
            <see cref="P:C1.C1Excel.XLShape.Hyperlink" /> information.</para>
        <para>The <see cref="T:C1.C1Excel.XLPictureShape" /> class derives from <b>XLShape</b> and 
            is used to embed images in sheets.</para>
      </remarks>
    </member>
    <member name="M:C1.C1Excel.XLShape.Clone">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLShape" /> object that is a copy of the current instance.
            </summary>
      <returns>A new <see cref="T:C1.C1Excel.XLShape" /> object that is a copy of the current instance.</returns>
    </member>
    <member name="P:C1.C1Excel.XLShape.IsEmpty">
      <summary>
            Determines whether the shape is empty.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLShape.Id">
      <summary>
            Gets the unique identifier of the shape.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLShape.Row">
      <summary>
            Gets or sets the index of the row to which the shape is attached.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLShape.Column">
      <summary>
            Gets or sets the index of the column to which the shape is attached.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLShape.Sheet">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.XLSheet" /> object.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLShape.Workbook">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.C1XLBook" /> object.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLShape.Rectangle">
      <summary>
            Gets or sets the rectangle that contains the shape, in twips.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLShape.Rotation">
      <summary>
            Gets or sets the rotation of the shape, in degrees.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLShape.LineWidth">
      <summary>
            Gets or sets the width of the border around the shape, in twips.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLShape.LineColor">
      <summary>
            Gets or sets the color of the border around the shape.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLShape.LineStyle">
      <summary>
            Gets or sets the style of the line or border around the shape.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLShape.DashedLineStyle">
      <summary>
            Gets or sets the dash style of the line or border around the shape.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLShape.Hyperlink">
      <summary>
            Gets or sets the hyperlink associated with the shape.
            </summary>
      <remarks>
            If you set this property to a URL, clicking the shape in Excel will
            open the browser and navigate to the URL.
            </remarks>
    </member>
    <member name="M:C1.C1Excel.XLShape.ParseProperties(System.Collections.IDictionary)">
      <summary>
            Parse shape properties.
            </summary>
      <param name="properties">
        <see cref="T:System.Collections.IDictionary" /> that contains the shape properties.</param>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> that contains not handled the shape properties.</returns>
    </member>
    <member name="M:C1.C1Excel.XLShape.CreateProperties(System.Collections.IDictionary)">
      <summary>
            Populates an <see cref="T:System.Collections.IDictionary" /> with the shape properties.
            </summary>
      <param name="properties">
        <see cref="T:System.Collections.IDictionary" /> where the shape properties are stored.</param>
    </member>
    <member name="T:C1.C1Excel.XLPictureViewType">
      <summary>
            Specifies how images should be displayed (color, grayscale, or black and white).
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLPictureViewType.Auto">
      <summary>
            Display the image using the image's own color information.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLPictureViewType.GrayScale">
      <summary>
            Display the image in grayscale.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLPictureViewType.BlackAndWhite">
      <summary>
            Display the image in black and white.
            </summary>
    </member>
    <member name="T:C1.C1Excel.XLShapeLineStyleEnum">
      <summary>
            Specifies the style of borders drawn around <see cref="T:C1.C1Excel.XLShape" /> objects.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeLineStyleEnum.Simple">
      <summary>Single line (of width LineWidth).</summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeLineStyleEnum.Double">
      <summary>Double lines of equal width.</summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeLineStyleEnum.ThickThin">
      <summary>Double lines, one thick, one thin.</summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeLineStyleEnum.ThinThick">
      <summary>Double lines, reverse order.</summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeLineStyleEnum.Triple">
      <summary>Three lines, thin, thick, thin.</summary>
    </member>
    <member name="T:C1.C1Excel.XLShapeDashedLineStyleEnum">
      <summary>
            Specifies the dash style of borders drawn around <see cref="T:C1.C1Excel.XLShape" /> objects.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeDashedLineStyleEnum.Solid">
      <summary>Solid (continuous) pen.</summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeDashedLineStyleEnum.Dash">
      <summary>Dash style, analogue PS_DASH pen of system.</summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeDashedLineStyleEnum.Dot">
      <summary>Dot style, analogue PS_DOT pen of system.</summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeDashedLineStyleEnum.DashDot">
      <summary>Dash-dot style, analogue PS_DASHDOT pen of system.</summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeDashedLineStyleEnum.DashDotDot">
      <summary>Dash-dot-dot style, analogue PS_DASHDOTDOT pen of system.</summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeDashedLineStyleEnum.SquareDot">
      <summary>Square dot style.</summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeDashedLineStyleEnum.ShortDash">
      <summary>Short dash style.</summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeDashedLineStyleEnum.LongDash">
      <summary>Long dash style.</summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeDashedLineStyleEnum.ShortDashDot">
      <summary>Short dash-dot style.</summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeDashedLineStyleEnum.LongDashDot">
      <summary>Long dash-dot style.</summary>
    </member>
    <member name="F:C1.C1Excel.XLShapeDashedLineStyleEnum.LongDashDotDot">
      <summary>Long dash-dot-dot style.</summary>
    </member>
    <member name="T:C1.C1Excel.XLCommentShape">
      <summary>
            Represents a text shape embedded in an <see cref="T:C1.C1Excel.XLSheet" />.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLCommentShape.#ctor(System.String,System.Int32,System.Int32)">
      <summary>
            Initializes a new instance of an <see cref="T:C1.C1Excel.XLCommentShape" />.
            </summary>
      <param name="text">The text contained in the new <see cref="T:C1.C1Excel.XLCommentShape" />.</param>
      <param name="x">The horizontal position of the new shape with respect to the cell, in twips.</param>
      <param name="y">The vertical position of the new shape with respect to the cell, in twips.</param>
    </member>
    <member name="M:C1.C1Excel.XLCommentShape.#ctor(System.String,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>
            Initializes a new instance of an <see cref="T:C1.C1Excel.XLCommentShape" />.
            </summary>
      <param name="text">The text contained in the new <see cref="T:C1.C1Excel.XLCommentShape" />.</param>
      <param name="x">The horizontal position of the image with respect to the cell, in twips.</param>
      <param name="y">The vertical position of the image with respect to the cell, in twips.</param>
      <param name="width">The width of the image, in twips.</param>
      <param name="height">The height of the image, in twips.</param>
    </member>
    <member name="M:C1.C1Excel.XLCommentShape.#ctor(System.String,System.Drawing.Rectangle)">
      <summary>
            Initializes a new instance of an <see cref="T:C1.C1Excel.XLCommentShape" />.
            </summary>
      <param name="text">The text contained in the new <see cref="T:C1.C1Excel.XLCommentShape" />.</param>
      <param name="rc">The rectangle that specifies the image size and position 
            with respect to the cell, in twips.</param>
    </member>
    <member name="M:C1.C1Excel.XLCommentShape.#ctor(System.String)">
      <summary>
            Initializes a new instance of an <see cref="T:C1.C1Excel.XLCommentShape" />.
            </summary>
      <param name="text">The text contained in the new <see cref="T:C1.C1Excel.XLCommentShape" />.</param>
    </member>
    <member name="P:C1.C1Excel.XLCommentShape.IsEmpty">
      <summary>
            Determines whether the shape is empty.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCommentShape.Visible">
      <summary>
            Determines whether the shape is visible.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLCommentShape.ParseProperties(System.Collections.IDictionary)">
      <summary>
            Parse shape properties.
            </summary>
      <param name="properties">
        <see cref="T:System.Collections.IDictionary" /> that contains the shape properties.</param>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> that contains not handled the shape properties.</returns>
    </member>
    <member name="M:C1.C1Excel.XLCommentShape.CreateProperties(System.Collections.IDictionary)">
      <summary>
            Populates an <see cref="T:System.Collections.IDictionary" /> with the shape properties.
            </summary>
      <param name="properties">
        <see cref="T:System.Collections.IDictionary" /> where the shape properties are stored.</param>
    </member>
    <member name="T:C1.C1Excel.XLHorizTextAlign">
      <summary>
            Specifies horizontal text alignment.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLHorizTextAlign.Left">
      <summary>Text is left aligned.</summary>
    </member>
    <member name="F:C1.C1Excel.XLHorizTextAlign.Center">
      <summary>Text is centered.</summary>
    </member>
    <member name="F:C1.C1Excel.XLHorizTextAlign.Right">
      <summary>Text is right aligned.</summary>
    </member>
    <member name="F:C1.C1Excel.XLHorizTextAlign.Justify">
      <summary>Text is justified.</summary>
    </member>
    <member name="T:C1.C1Excel.XLVertTextAlign">
      <summary>
            Specifies vertical text alignment.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLVertTextAlign.Top">
      <summary>Text is top aligned.</summary>
    </member>
    <member name="F:C1.C1Excel.XLVertTextAlign.Center">
      <summary>Text is centered vertically.</summary>
    </member>
    <member name="F:C1.C1Excel.XLVertTextAlign.Bottom">
      <summary>Text is bottom aligned.</summary>
    </member>
    <member name="F:C1.C1Excel.XLVertTextAlign.Justify">
      <summary>Text is justified vertically.</summary>
    </member>
    <member name="T:C1.C1Excel.XLTextOrientation">
      <summary>
            Specifies text orientation.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLTextOrientation.Default">
      <summary>Default (horizontal) orientation.</summary>
    </member>
    <member name="F:C1.C1Excel.XLTextOrientation.TopToBottom">
      <summary>Vertical text (top to bottom).</summary>
    </member>
    <member name="F:C1.C1Excel.XLTextOrientation.RightRotation">
      <summary>Text is rotated 90 degrees clockwise.</summary>
    </member>
    <member name="F:C1.C1Excel.XLTextOrientation.LeftRotation">
      <summary>Text is rotated 90 degrees counterclockwise.</summary>
    </member>
    <member name="T:C1.C1Excel.XLTextShape">
      <summary>
            Represents a common text shape embedded in an <see cref="T:C1.C1Excel.XLSheet" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLTextShape.Text">
      <summary>
            Gets or sets a reference to the text string contained in this <see cref="T:C1.C1Excel.XLTextShape" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLTextShape.Orientation">
      <summary>
            Gets or sets a orientation of this <see cref="T:C1.C1Excel.XLTextShape" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLTextShape.HorizAlign">
      <summary>
            Gets or sets a horizontal alignment of the text in this <see cref="T:C1.C1Excel.XLTextShape" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLTextShape.VertAlign">
      <summary>
            Gets or sets a vertical alignment of the text in this <see cref="T:C1.C1Excel.XLTextShape" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLTextShape.Locked">
      <summary>
            Gets or sets a locked of this <see cref="T:C1.C1Excel.XLTextShape" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLTextShape.Wrapped">
      <summary>
            Gets or sets a wrapped text of this <see cref="T:C1.C1Excel.XLTextShape" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLTextShape.TextScale">
      <summary>
            Gets or sets a text scale of this <see cref="T:C1.C1Excel.XLTextShape" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLTextShape.TextId">
      <summary>
            Gets or sets a text identifier of this <see cref="T:C1.C1Excel.XLTextShape" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLTextShape.Bidirectional">
      <summary>
            Gets or sets a bidirectional count of text shape of this <see cref="T:C1.C1Excel.XLTextShape" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLTextShape.TextToFit">
      <summary>
            Gets or sets a fit to shape of the text flag of this <see cref="T:C1.C1Excel.XLTextShape" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLTextShape.LeftMargin">
      <summary>
            Gets or sets the left margin area of this <see cref="T:C1.C1Excel.XLTextShape" />, in twips.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLTextShape.RightMargin">
      <summary>
            Gets or sets the right margin of this <see cref="T:C1.C1Excel.XLTextShape" />, in twips.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLTextShape.TopMargin">
      <summary>
            Gets or sets the top margin of this <see cref="T:C1.C1Excel.XLTextShape" />, in twips.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLTextShape.BottomMargin">
      <summary>
            Gets or sets the bottom margin of this <see cref="T:C1.C1Excel.XLTextShape" />, in twips.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLTextShape.ParseProperties(System.Collections.IDictionary)">
      <summary>
            Parse shape properties.
            </summary>
      <param name="properties">
        <see cref="T:System.Collections.IDictionary" /> that contains the shape properties.</param>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> that contains not handled the shape properties.</returns>
    </member>
    <member name="M:C1.C1Excel.XLTextShape.CreateProperties(System.Collections.IDictionary)">
      <summary>
            Populates an <see cref="T:System.Collections.IDictionary" /> with the shape properties.
            </summary>
      <param name="properties">
        <see cref="T:System.Collections.IDictionary" /> where the shape properties are stored.</param>
    </member>
    <member name="T:C1.C1Excel.XLCell">
      <summary>
            Represents individual cells in an <see cref="T:C1.C1Excel.XLSheet" /> and provides
            properties for getting and setting the cell <see cref="P:C1.C1Excel.XLCell.Value" />,
            <see cref="P:C1.C1Excel.XLCell.Style" />, and <see cref="P:C1.C1Excel.XLCell.Hyperlink" />.
            </summary>
      <remarks>
        <para>To create cells, use the <see cref="T:C1.C1Excel.XLSheet" /> indexer (Item property). 
            If the cell already exists, the reference will be returned as usual. 
            If not, the sheet will create the cell (as well as rows and columns if 
            necessary) and will return a reference to the new cell.</para>
        <para>Because it creates cells automatically, the indexer is especially 
            useful when creating and populating sheets.</para>
      </remarks>
      <example>
            For example, the code below creates a new <see cref="T:C1.C1Excel.C1XLBook" />, then 
            populates the first sheet with a 10 by 10 multiplication table:
            <code>
            C1XLBook book = new C1XLBook();
            XLSheet sheet = book.Sheets[0];
            for (int r = 0; r &lt; 10; r++)
            {
              for (int c = 0; c &lt; 10; c++)
              {
                XLCell cell = sheet[r, c];
            	cell.Value = (r+1) * (c+1);
              }
            }
            book.Save(@"c:\temp\test.xls");
            </code>
            Note how the code simply accesses the cells using the indexer. There's no 
            need to create any rows, columns, or cells. The indexer takes care of all 
            that automatically.
            </example>
    </member>
    <member name="M:C1.C1Excel.XLCell.Clone">
      <summary>
            Creates a copy of the current cell, including the value.
            </summary>
      <returns>A new <see cref="T:C1.C1Excel.XLCell" /> object that is a copy of the current instance.</returns>
      <remarks>The formula of the cell is cloned only as part of a row, a worksheet or a workbook.</remarks>
    </member>
    <member name="P:C1.C1Excel.XLCell.Value">
      <summary>
            Gets or sets the value stored in the cell.
            </summary>
      <remarks>
        <para>The value may contain strings, numeric, Boolean, DateTime, or
            null objects. Other types of objects cannot be saved in Excel files.</para>
        <para>DateTime values are internally converted into doubles, 
            and stored in the sheet as such. The only way to tell the difference 
            between a DateTime value and a double in Excel is by way of the 
            format associated with the cell (<see cref="P:C1.C1Excel.XLStyle.Format" /> property).
            </para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLCell.Style">
      <summary>
            Gets or sets the <see cref="T:C1.C1Excel.XLStyle" /> object associated with the cell.
            </summary>
      <remarks>
        <para>The appearance of each cell is defined by one or more XLStyle objects.</para>
        <para>When displaying a cell, Excel combines the row, column, and cell 
            styles and merges the style elements defined in each one in order to 
            determine how the cell should be displayed.</para>
        <para>The precedence of the styles is: (1) cell, (2) row, (3) column, 
            (4) default style. For example, if a cell style defines the font and 
            background color, those will be applied regardless of the settings in the 
            row and column styles. If the row style defines an alignment, that will 
            be applied regardless of the column style, and so on.</para>
        <para>The cell style may be null, in which case the cell is displayed 
            using the other styles available or the default book style if no others 
            are available.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLCell.Hyperlink">
      <summary>
            Gets or sets a string that specifies an action to take when the cell
            is clicked.
            </summary>
      <remarks>
        <para>Hyperlinks may contain URLs that when clicked open a browser window and
            navigate to the specified site (for example, "http://www.grapecity.com"). They
            may also contain references to files that are launched by the application
            associated with the file type (for example, "readme.doc"). Finally, hyperlinks
            can be used to send e-mails (for example, "mailto:<EMAIL>").</para>
        <para>Each cell may contain a hyperlink and a value. However, if you assign
            a hyperlink to a cell that has no value (<see cref="P:C1.C1Excel.XLCell.Value" /> == null), then
            the hyperlink text is automatically assigned to the cell value as well.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLCell.Formula">
      <summary>
            Gets or sets a string that specifies a formula of the cell.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLCell.SetValue(System.Object,C1.C1Excel.XLStyle)">
      <summary>
            Sets the <see cref="P:C1.C1Excel.XLCell.Value" /> and <see cref="P:C1.C1Excel.XLCell.Style" /> properties of a cell.
            </summary>
      <param name="value">New cell value.</param>
      <param name="style">New cell style.</param>
      <remarks>
            This method allows you to set the Value and Style properties of a cell
            simultaneously. This can make your code more compact and easier to maintain.
            For example:
            <code>
            // set cell value and style (short version)
            sheet[0,0].SetValue("Hello", styleBold);
            
            // set cell value and style (longer version)
            sheet[0,0].Value = "Hello";
            sheet[0,0].Style = styleBold;
            </code></remarks>
    </member>
    <member name="P:C1.C1Excel.XLCell.Text">
      <summary>
            Gets the string representation of the current cell value.
            </summary>
    </member>
    <member name="T:C1.C1Excel.XLReferenceMode">
      <summary>
            The reference mode.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLReferenceMode.R1C1">
      <summary>R1C1 style, default by formulas.</summary>
    </member>
    <member name="F:C1.C1Excel.XLReferenceMode.A1">
      <summary>A1 style, default by spreadsheet.</summary>
    </member>
    <member name="T:C1.C1Excel.XLReferenceType">
      <summary>
            The reference type.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLReferenceType.Absolute">
      <summary>Absolute coordinates.</summary>
    </member>
    <member name="F:C1.C1Excel.XLReferenceType.Relative">
      <summary>Relative coordinates.</summary>
    </member>
    <member name="T:C1.C1Excel.XLRangeType">
      <summary>
            The range type.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLRangeType.Default">
      <summary>Default range.</summary>
    </member>
    <member name="F:C1.C1Excel.XLRangeType.Indirect">
      <summary>Indirect range.</summary>
    </member>
    <member name="F:C1.C1Excel.XLRangeType.Offset">
      <summary>Offset range.</summary>
    </member>
    <member name="F:C1.C1Excel.XLRangeType.Name">
      <summary>Named range.</summary>
    </member>
    <member name="F:C1.C1Excel.XLRangeType.Text">
      <summary>Text range.</summary>
    </member>
    <member name="F:C1.C1Excel.XLRangeType.Formula">
      <summary>Formula range.</summary>
    </member>
    <member name="T:C1.C1Excel.XLCellRangeCollection">
      <summary>
            Represents a collection of <see cref="T:C1.C1Excel.XLCellRange" /> objects on 
            an <see cref="T:C1.C1Excel.XLSheet" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRangeCollection.Count">
      <summary>
            Gets the number of <see cref="T:C1.C1Excel.XLCellRange" /> objects in the collection.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRangeCollection.Sheet">
      <summary>
            Gets the <see cref="T:C1.C1Excel.XLSheet" /> object that owns the collection.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRangeCollection.Book">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.C1XLBook" /> object.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRangeCollection.ActiveIndex">
      <summary>
            Gets or sets the active index in the collection.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLCellRangeCollection.Clear">
      <summary>
            Removes all <see cref="T:C1.C1Excel.XLCellRange" /> objects from the collection.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLCellRangeCollection.Contains(C1.C1Excel.XLCellRange)">
      <summary>
            Checks whether the collection contains a specific <see cref="T:C1.C1Excel.XLCellRange" /> object.
            </summary>
      <param name="cr">
        <see cref="T:C1.C1Excel.XLCellRange" /> object to look for.</param>
      <returns>True if the collection contains the range, False otherwise.</returns>
    </member>
    <member name="M:C1.C1Excel.XLCellRangeCollection.IndexOf(C1.C1Excel.XLCellRange)">
      <summary>
            Gets the position of an <see cref="T:C1.C1Excel.XLCellRange" /> object in the collection.
            </summary>
      <param name="cr">
        <see cref="T:C1.C1Excel.XLCellRange" /> object to look for.</param>
      <returns>The position of the object in the collection, or -1 if the object is not a member of the collection.</returns>
    </member>
    <member name="M:C1.C1Excel.XLCellRangeCollection.Remove(C1.C1Excel.XLCellRange)">
      <summary>
            Removes an <see cref="T:C1.C1Excel.XLCellRange" /> object from the collection.
            </summary>
      <param name="cr">
        <see cref="T:C1.C1Excel.XLCellRange" /> object to remove from the collection.</param>
    </member>
    <member name="M:C1.C1Excel.XLCellRangeCollection.RemoveAt(System.Int32)">
      <summary>
            Removes an <see cref="T:C1.C1Excel.XLCellRange" /> object at a specific position from the collection.
            </summary>
      <param name="index">Index of the object to remove from the collection.</param>
    </member>
    <member name="M:C1.C1Excel.XLCellRangeCollection.Add(C1.C1Excel.XLCellRange)">
      <summary>
            Appends an <see cref="T:C1.C1Excel.XLCellRange" /> object to the collection.
            </summary>
      <param name="cr">
        <see cref="T:C1.C1Excel.XLCellRange" /> object to add to the collection.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added to the collection (usually because it 
            overlaps another cell range already in the collection).</returns>
    </member>
    <member name="P:C1.C1Excel.XLCellRangeCollection.Item(System.Int32)">
      <summary>
            Gets the <see cref="T:C1.C1Excel.XLCellRange" /> object at the specified position in the collection.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLCellRangeCollection.Add(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>
            Creates an <see cref="T:C1.C1Excel.XLCellRange" /> object and appends it to the collection.
            </summary>
      <param name="rowIndex">Index of the top row in the cell range.</param>
      <param name="colIndex">Index of the left column in the cell range.</param>
      <param name="rowCount">Number of rows in the cell range.</param>
      <param name="colCount">Number of columns in the cell range.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added to the collection (usually because it 
            overlaps another cell range already in the collection).</returns>
    </member>
    <member name="M:C1.C1Excel.XLCellRangeCollection.Insert(System.Int32,C1.C1Excel.XLCellRange)">
      <summary>
            Inserts an <see cref="T:C1.C1Excel.XLCellRange" /> object at a specific position in the 
            collection.
            </summary>
      <param name="index">Position where the object will be inserted.</param>
      <param name="cr">Object to insert in the collection.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added to the collection (usually because it 
            overlaps another cell range already in the collection).</returns>
    </member>
    <member name="T:C1.C1Excel.XLCellRange">
      <summary>
            Represents a range of <see cref="T:C1.C1Excel.XLCell" /> objects in an <see cref="T:C1.C1Excel.XLSheet" />.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLCellRange.#ctor">
      <summary>
            Creates an instance of an <see cref="T:C1.C1Excel.XLCellRange" /> object containing
            an empty range.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLCellRange.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>
            Creates an instance of an <see cref="T:C1.C1Excel.XLCellRange" /> object containing a specified range.
            </summary>
      <param name="rowFrom">Top row in the range.</param>
      <param name="rowTo">Bottom row in the range.</param>
      <param name="colFrom">Left column in the range.</param>
      <param name="colTo">Right column in the range.</param>
    </member>
    <member name="M:C1.C1Excel.XLCellRange.#ctor(C1.C1Excel.XLSheet,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>
            Creates an instance of an <see cref="T:C1.C1Excel.XLCellRange" /> object containing a specified range.
            </summary>
      <param name="sheet">The <see cref="T:C1.C1Excel.XLSheet" /> of this cell range.</param>
      <param name="rowFrom">Top row in the range.</param>
      <param name="rowTo">Bottom row in the range.</param>
      <param name="colFrom">Left column in the range.</param>
      <param name="colTo">Right column in the range.</param>
    </member>
    <member name="M:C1.C1Excel.XLCellRange.#ctor(C1.C1Excel.XLSheet,System.Int32,System.Int32,System.Int32,System.Int32,C1.C1Excel.XLReferenceType,C1.C1Excel.XLReferenceType,C1.C1Excel.XLReferenceType,C1.C1Excel.XLReferenceType)">
      <summary>
            Creates an instance of an <see cref="T:C1.C1Excel.XLCellRange" /> object containing a specified range.
            </summary>
      <param name="sheet">The <see cref="T:C1.C1Excel.XLSheet" /> of this cell range.</param>
      <param name="rowFrom">Top row in the range.</param>
      <param name="rowTo">Bottom row in the range.</param>
      <param name="colFrom">Left column in the range.</param>
      <param name="colTo">Right column in the range.</param>
      <param name="rowFromRef">The reference type of the top row in the range.</param>
      <param name="rowToRef">The reference type of the bottom row in the range.</param>
      <param name="colFromRef">The reference type of the left column in the range.</param>
      <param name="colToRef">The reference type of the right column in the range.</param>
    </member>
    <member name="M:C1.C1Excel.XLCellRange.#ctor(C1.C1Excel.XLSheet,System.Int32,System.Int32,C1.C1Excel.XLReferenceType,C1.C1Excel.XLReferenceType)">
      <summary>
            Creates an instance of an <see cref="T:C1.C1Excel.XLCellRange" /> object containing a specified range.
            </summary>
      <param name="ws">The <see cref="T:C1.C1Excel.XLSheet" /> of the range.</param>
      <param name="row">The row in the range equal one cell.</param>
      <param name="col">The column in the range equal one cell.</param>
      <param name="rowRef">The reference type of the row.</param>
      <param name="colRef">The reference type of the column.</param>
    </member>
    <member name="M:C1.C1Excel.XLCellRange.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,C1.C1Excel.XLReferenceType,C1.C1Excel.XLReferenceType,C1.C1Excel.XLReferenceType,C1.C1Excel.XLReferenceType)">
      <summary>
            Creates an instance of an <see cref="T:C1.C1Excel.XLCellRange" /> object containing a specified range.
            </summary>
      <param name="sheetFrom">First index of the <see cref="T:C1.C1Excel.XLSheet" /> of the range.</param>
      <param name="sheetTo">Last index of the <see cref="T:C1.C1Excel.XLSheet" /> of the range.</param>
      <param name="rowFrom">Top row in the range.</param>
      <param name="rowTo">Bottom row in the range.</param>
      <param name="colFrom">Left column in the range.</param>
      <param name="colTo">Right column in the range.</param>
      <param name="rowFromRef">The reference type of the top row in the range.</param>
      <param name="rowToRef">The reference type of the bottom row in the range.</param>
      <param name="colFromRef">The reference type of the left column in the range.</param>
      <param name="colToRef">The reference type of the right column in the range.</param>
    </member>
    <member name="M:C1.C1Excel.XLCellRange.#ctor(C1.C1Excel.XLSheet,System.String)">
      <summary>
            Creates an instance of an <see cref="T:C1.C1Excel.XLCellRange" /> object containing a specified range.
            </summary>
      <param name="sheet">The owner sheet for the range.</param>
      <param name="reference">The text presentation of a specified range without sheets.</param>
    </member>
    <member name="M:C1.C1Excel.XLCellRange.#ctor(C1.C1Excel.C1XLBook,System.String)">
      <summary>
            Creates an instance of an <see cref="T:C1.C1Excel.XLCellRange" /> object containing a specified range.
            </summary>
      <param name="book">The owner workbook for the range.</param>
      <param name="reference">The text presentation of a specified range.</param>
    </member>
    <member name="M:C1.C1Excel.XLCellRange.Clone">
      <summary>
            Creates a copy of this cell range.
            </summary>
      <returns>A new <see cref="T:C1.C1Excel.XLCellRange" /> object that is a copy of the current instance.</returns>
    </member>
    <member name="M:C1.C1Excel.XLCellRange.ToString">
      <summary>
            Returns a reference string that represents the range.
            </summary>
      <returns>A reference string that represents the range.</returns>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.IsEmpty">
      <summary>
            Determines whether the range is empty.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.RowFrom">
      <summary>
            Gets the index of the top row in the range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.ColumnFrom">
      <summary>
            Gets the index of the left column in the range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.RowCount">
      <summary>
            Gets the number of rows in the range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.ColumnCount">
      <summary>
            Gets the number of columns in the range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.SheetCount">
      <summary>
            Gets the number of sheets in the range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.RowTo">
      <summary>
            Gets or sets the index of the last row in the range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.ColumnTo">
      <summary>
            Gets or sets the index of the last column in the range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.RowFromRef">
      <summary>
            Gets the reference type of the top row in the range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.ColumnFromRef">
      <summary>
            Gets the reference type of the left column in the range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.RowToRef">
      <summary>
            Gets the reference type of the bottom row in the range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.ColumnToRef">
      <summary>
            Gets the reference type of the right column in the range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.Book">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.C1XLBook" /> object.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.RangeType">
      <summary>
            Gets or sets a type (default, indirect or offset) for this cell range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.SheetFrom">
      <summary>
            Gets the index of the first sheet in the range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.SheetTo">
      <summary>
            Gets or sets the index of the last sheet in the range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.Sheets">
      <summary>
            Gets an array of <see cref="T:C1.C1Excel.XLSheet" /> objects.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.Value">
      <summary>
            Gets or sets the complex value associated with this range of cells.
            </summary>
      <remarks>
            The value may contain strings, numeric, Boolean, DateTime, or
            null objects. Other types of objects cannot be saved in Excel files.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLCellRange.Style">
      <summary>
            Gets or sets the <see cref="T:C1.C1Excel.XLStyle" /> object associated with this range of cells.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLCellRange.Contains(C1.C1Excel.XLSheet,System.Int32,System.Int32)">
      <summary>
            Determines whether the range contains a specific cell.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLCellRange.Contains(C1.C1Excel.XLCellRange)">
      <summary>
            Determines whether the range contains a specific range.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLCellRange.Intersects(C1.C1Excel.XLCellRange)">
      <summary>
            Determines whether the range intersects another range.
            </summary>
    </member>
    <member name="T:C1.C1Excel.XLCommentCollection">
      <summary>
            Represents a collection of <see cref="T:C1.C1Excel.XLComment" /> objects in a <see cref="T:C1.C1Excel.XLSheet" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCommentCollection.Count">
      <summary>
            Gets the number of <see cref="T:C1.C1Excel.XLComment" /> objects in the collection.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCommentCollection.Sheet">
      <summary>
            Gets the <see cref="T:C1.C1Excel.XLSheet" /> object that owns the collection.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLCommentCollection.Item(System.Int32)">
      <summary>
            Gets the <see cref="T:C1.C1Excel.XLComment" /> object at the specified position in the collection.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLCommentCollection.Clear">
      <summary>
            Removes all <see cref="T:C1.C1Excel.XLComment" /> objects from the collection.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLCommentCollection.Contains(C1.C1Excel.XLComment)">
      <summary>
            Checks whether the collection contains a specific <see cref="T:C1.C1Excel.XLComment" /> object.
            </summary>
      <param name="comment">The <see cref="T:C1.C1Excel.XLComment" /> object to look for.</param>
      <returns>True if the collection contains the comment, false otherwise.</returns>
    </member>
    <member name="M:C1.C1Excel.XLCommentCollection.IndexOf(C1.C1Excel.XLComment)">
      <summary>
            Gets the index of a specific <see cref="T:C1.C1Excel.XLComment" /> object in the collection.
            </summary>
      <param name="comment">The <see cref="T:C1.C1Excel.XLComment" /> object to look for.</param>
      <returns>The position of the object in the collection, or -1 if the object is not a member of the collection.</returns>
    </member>
    <member name="M:C1.C1Excel.XLCommentCollection.Remove(C1.C1Excel.XLComment)">
      <summary>
            Removes an <see cref="T:C1.C1Excel.XLComment" /> object from the collection.
            </summary>
      <param name="comment">The <see cref="T:C1.C1Excel.XLComment" /> object to remove from the collection.</param>
    </member>
    <member name="M:C1.C1Excel.XLCommentCollection.RemoveAt(System.Int32)">
      <summary>
            Removes an <see cref="T:C1.C1Excel.XLComment" /> object at a specific position from the collection.
            </summary>
      <param name="index">Index of the object to remove from the collection.</param>
    </member>
    <member name="M:C1.C1Excel.XLCommentCollection.Add(C1.C1Excel.XLComment)">
      <summary>
            Appends an <see cref="T:C1.C1Excel.XLComment" /> object to the collection.
            </summary>
      <param name="comment">The <see cref="T:C1.C1Excel.XLComment" /> object to add to the collection.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added to the collection (usually because it 
            overlaps another comment already in the collection).</returns>
    </member>
    <member name="M:C1.C1Excel.XLCommentCollection.Add(System.Int32,System.Int32,System.String)">
      <summary>
            Creates an <see cref="T:C1.C1Excel.XLComment" /> object and appends it to the collection.
            </summary>
      <param name="rowIndex">Index of the top row in the comment.</param>
      <param name="colIndex">Index of the left column in the comment.</param>
      <param name="author">The author of the comment.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added to the collection (usually because it 
            overlaps another comment already in the collection).</returns>
    </member>
    <member name="M:C1.C1Excel.XLCommentCollection.Add(System.Int32,System.Int32,System.String,System.String)">
      <summary>
            Creates an <see cref="T:C1.C1Excel.XLComment" /> object and appends it to the collection.
            </summary>
      <param name="rowIndex">Index of the top row in the comment.</param>
      <param name="colIndex">Index of the left column in the comment.</param>
      <param name="author">The author of the comment.</param>
      <param name="text">The context of the comment.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added to the collection (usually because it 
            overlaps another comment already in the collection).</returns>
    </member>
    <member name="M:C1.C1Excel.XLCommentCollection.Add(System.Int32,System.Int32,System.String,System.String,System.Boolean)">
      <summary>
            Creates an <see cref="T:C1.C1Excel.XLComment" /> object and appends it to the collection.
            </summary>
      <param name="rowIndex">Index of the top row in the comment.</param>
      <param name="colIndex">Index of the left column in the comment.</param>
      <param name="author">The author of the comment.</param>
      <param name="text">The text of the comment.</param>
      <param name="toRtf">Flag indicating whether to convert <paramref name="author" /> and <paramref name="text" /> to RTF format.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added to the collection
            (usually because it overlaps another comment already in the collection).</returns>
    </member>
    <member name="M:C1.C1Excel.XLCommentCollection.Insert(System.Int32,C1.C1Excel.XLComment)">
      <summary>
            Inserts an <see cref="T:C1.C1Excel.XLComment" /> object at a specific position in the 
            collection.
            </summary>
      <param name="index">Position where the object will be inserted.</param>
      <param name="comment">Object to insert in the collection.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added to the collection (usually because it 
            overlaps another comment already in the collection).</returns>
    </member>
    <member name="T:C1.C1Excel.XLComment">
      <summary>
            Represents a comment associated with a cell.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLComment.#ctor(System.Int32,System.Int32,System.String,System.String)">
      <summary>
            Creates an instance of a <see cref="T:C1.C1Excel.XLComment" /> object containing
            a specified range.
            </summary>
      <param name="row">Row that the comment applies to.</param>
      <param name="col">Column that the comment applies to.</param>
      <param name="author">Comment author.</param>
      <param name="text">Comment content.</param>
    </member>
    <member name="M:C1.C1Excel.XLComment.Clone">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLComment" /> object that is a copy of the current instance.
            </summary>
      <returns>A new <see cref="T:C1.C1Excel.XLComment" /> object that is a copy of the current instance.</returns>
    </member>
    <member name="P:C1.C1Excel.XLComment.IsShow">
      <summary>
            Gets or sets the show flag of the comment.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLComment.RowIndex">
      <summary>
            Gets or sets the row index of the comment.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLComment.ColumnIndex">
      <summary>
            Gets or sets the column index of the comment.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLComment.Author">
      <summary>
            Gets a author for this comment.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLComment.Sheet">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.XLSheet" /> object.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLComment.Cell">
      <summary>
            Gets a reference to the cell for this comment.
            </summary>
      <remarks>A reference to the <see cref="T:C1.C1Excel.XLCell" /> object at the comment coordinates,
            or null if there is no cell at the specified position.</remarks>
    </member>
    <member name="P:C1.C1Excel.XLComment.TextBox">
      <summary>
            Gets a reference to the <see cref="T:C1.C1Excel.XLCommentShape" /> object with context.
            </summary>
    </member>
    <member name="T:C1.C1Excel.XLFontScript">
      <summary>
            The Excel font superscript/subscript style (escapement type).
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLFontScript.None">
      <summary>Without escapement (normal font).</summary>
    </member>
    <member name="F:C1.C1Excel.XLFontScript.Superscript">
      <summary>Superscript escapement type.</summary>
    </member>
    <member name="F:C1.C1Excel.XLFontScript.Subscript">
      <summary>Subscript escapement type.</summary>
    </member>
    <member name="T:C1.C1Excel.XLUnderlineStyle">
      <summary>
            Conditional formatting underline type for the font.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLUnderlineStyle.None">
      <summary>Without underline.</summary>
    </member>
    <member name="F:C1.C1Excel.XLUnderlineStyle.Single">
      <summary>Single underline.</summary>
    </member>
    <member name="F:C1.C1Excel.XLUnderlineStyle.Double">
      <summary>Double underline.</summary>
    </member>
    <member name="F:C1.C1Excel.XLUnderlineStyle.SingleAccounting">
      <summary>Single accounting underline.</summary>
    </member>
    <member name="F:C1.C1Excel.XLUnderlineStyle.DoubleAccounting">
      <summary>Double accounting underline.</summary>
    </member>
    <member name="T:C1.C1Excel.XLConditionalFormattingType">
      <summary>
            Conditional formatting type.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingType.CellIs">
      <summary>Conditional formatting type is cell value.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingType.Expression">
      <summary>Conditional formatting type is formula.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingType.EndsWith">
      <summary>Conditional formatting type is end with.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingType.ContainsText">
      <summary>Conditional formatting type is contains text.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingType.TimePeriod">
      <summary>Conditional formatting type is time periods (today, yesterday ...).</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingType.DuplicateValues">
      <summary>Conditional formatting type is duplicate values.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingType.Top10">
      <summary>Conditional formatting type is top n (rank) values.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingType.Top10Percent">
      <summary>Conditional formatting type is top n (rank) percent values.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingType.Bottom10">
      <summary>Conditional formatting type is bottom n (rank) values.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingType.Bottom10Percent">
      <summary>Conditional formatting type is bottom n (rank) percent values.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingType.AboveAverage">
      <summary>Conditional formatting type is above values then average value.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingType.BelowAverage">
      <summary>Conditional formatting type is below values then average value.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingType.Unknown">
      <summary>Conditional formatting type is unknown.</summary>
    </member>
    <member name="T:C1.C1Excel.XLConditionalFormattingOperator">
      <summary>
            Conditional formatting operator (applicable when cell type formating).
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingOperator.NoComparision">
      <summary>No conditional formatting.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingOperator.Between">
      <summary>Conditional formatting if value between.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingOperator.NotBetween">
      <summary>Conditional formatting if value not between.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingOperator.Equal">
      <summary>Conditional formatting if value equal.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingOperator.NotEqual">
      <summary>Conditional formatting if value not equal.</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingOperator.GreaterThan">
      <summary>Conditional formatting if value greater than (GT).</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingOperator.LessThan">
      <summary>Conditional formatting if value less than (LT).</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingOperator.GreaterThanOrEqual">
      <summary>Conditional formatting if value greater than or equal (GE).</summary>
    </member>
    <member name="F:C1.C1Excel.XLConditionalFormattingOperator.LessThanOrEqual">
      <summary>Conditional formatting if value less than or equal (LE).</summary>
    </member>
    <member name="T:C1.C1Excel.XLRange">
      <summary>
            The simple cell range.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLRange.#ctor(System.String)">
      <summary>
            Initialization range.
            </summary>
      <param name="areaRef">The area reference text (for exapmle: 'A4:D8').</param>
    </member>
    <member name="M:C1.C1Excel.XLRange.#ctor(System.Int32,System.Int32)">
      <summary>
            Initialization range.
            </summary>
      <param name="row">The index of the row in the cell.</param>
      <param name="col">The index of the column in the cell.</param>
    </member>
    <member name="M:C1.C1Excel.XLRange.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>
            Initialization range.
            </summary>
      <param name="rowFirst">The index of the top row in the range.</param>
      <param name="colFirst">The index of the left column in the range.</param>
      <param name="rowLast">The index of the bottom row in the range.</param>
      <param name="colLast">The index of the right column in the range.</param>
    </member>
    <member name="P:C1.C1Excel.XLRange.RowFirst">
      <summary>Gets the index of the top row in the range.</summary>
    </member>
    <member name="P:C1.C1Excel.XLRange.ColumnFirst">
      <summary>Gets or sets the index of the left column in the range.</summary>
    </member>
    <member name="P:C1.C1Excel.XLRange.RowLast">
      <summary>Gets or sets the index of the bottom row in the range.</summary>
    </member>
    <member name="P:C1.C1Excel.XLRange.ColumnLast">
      <summary>Gets or sets the index of the right column in the range.</summary>
    </member>
    <member name="M:C1.C1Excel.XLRange.Clone">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLRange" /> object that is a copy of the current instance.
            </summary>
      <returns>A new <see cref="T:C1.C1Excel.XLRange" /> object that is a copy of the current instance.</returns>
    </member>
    <member name="M:C1.C1Excel.XLRange.GetHashCode">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.C1Excel.XLRange.Equals(System.Object)">
      <summary>
      </summary>
      <param name="obj">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.C1Excel.XLRange.ToString">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="T:C1.C1Excel.XLFontFormatting">
      <summary>The font formatting.</summary>
    </member>
    <member name="M:C1.C1Excel.XLFontFormatting.#ctor">
      <summary>Initialization font formatting.</summary>
    </member>
    <member name="P:C1.C1Excel.XLFontFormatting.FontHeight">
      <summary>Gets or sets font height for this formating (-1 for automatic height).</summary>
    </member>
    <member name="P:C1.C1Excel.XLFontFormatting.Escapement">
      <summary>Gets or sets escapement type for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLFontFormatting.Underline">
      <summary>Gets or sets underline type for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLFontFormatting.FontWeight">
      <summary>Gets or sets font weight for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLFontFormatting.FontColor">
      <summary>Gets or sets font color for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLFontFormatting.Bold">
      <summary>Gets or sets font bold attribute for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLFontFormatting.Italic">
      <summary>Gets or sets font italic attribute for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLFontFormatting.Strikeout">
      <summary>Gets or sets font strikeout attribute for this formating.</summary>
    </member>
    <member name="T:C1.C1Excel.XLBorderFormatting">
      <summary>The border formatting.</summary>
    </member>
    <member name="M:C1.C1Excel.XLBorderFormatting.#ctor">
      <summary>Initialization border formatting.</summary>
    </member>
    <member name="P:C1.C1Excel.XLBorderFormatting.LeftBorder">
      <summary>Gets or sets left border for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLBorderFormatting.RightBorder">
      <summary>Gets or sets right border for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLBorderFormatting.TopBorder">
      <summary>Gets or sets top border for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLBorderFormatting.BottomBorder">
      <summary>Gets or sets bottom border for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLBorderFormatting.LeftColor">
      <summary>Gets or sets color of left border for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLBorderFormatting.RightColor">
      <summary>Gets or sets color of right border for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLBorderFormatting.TopColor">
      <summary>Gets or sets color of top border for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLBorderFormatting.BottomColor">
      <summary>Gets or sets color of bottom border for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLBorderFormatting.Diagonal">
      <summary>Gets or sets which diagonal lines to display (none, forward, backward).</summary>
    </member>
    <member name="P:C1.C1Excel.XLBorderFormatting.DiagonalStyle">
      <summary>Gets or sets type of diagonal line for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLBorderFormatting.DiagonalColor">
      <summary>Gets or sets color of diagonal border for this formating.</summary>
    </member>
    <member name="T:C1.C1Excel.XLPatternFormatting">
      <summary>The pattern formatting.</summary>
    </member>
    <member name="M:C1.C1Excel.XLPatternFormatting.#ctor">
      <summary>Initialization pattern formatting.</summary>
    </member>
    <member name="P:C1.C1Excel.XLPatternFormatting.Pattern">
      <summary>Gets or sets fill pattern for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLPatternFormatting.BackColor">
      <summary>Gets or sets fill background color for this formating.</summary>
    </member>
    <member name="P:C1.C1Excel.XLPatternFormatting.ForeColor">
      <summary>Gets or sets fill foreground color for this formating.</summary>
    </member>
    <member name="T:C1.C1Excel.XLConditionalFormattingRangeCollection">
      <summary>
            The collection of conditional formatting ranges.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormattingRangeCollection.ConditionalFormatting">
      <summary>Gets conditional formatting object.</summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormattingRangeCollection.ToughRecalc">
      <summary>Determines whether the appearance of the cell reauires significant procrssing.</summary>
    </member>
    <member name="M:C1.C1Excel.XLConditionalFormattingRangeCollection.ToString">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="T:C1.C1Excel.XLConditionalFormattingRule">
      <summary>
            Conditional formatting.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLConditionalFormattingRule.#ctor">
      <summary>
            Initialization conditional formatting object.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormattingRule.Type">
      <summary>Gets or sets conditional formatting type.</summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormattingRule.Operator">
      <summary>Gets or sets conditional formatting operator (applicable when cell type formating).</summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormattingRule.Priority">
      <summary>Gets or sets the priority of the conditional formatting.</summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormattingRule.Font">
      <summary>Gets or sets the text font and foreground color of the conditional formatting.</summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormattingRule.Border">
      <summary>Gets or sets the borders and diagonal of the conditional formatting.</summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormattingRule.Pattern">
      <summary>Gets or sets the pattern of the conditional formatting.</summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormattingRule.FirstFormula">
      <summary>Gets or sets first formula for this condition.</summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormattingRule.SecondFormula">
      <summary>Gets or sets second formula for this condition.</summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormattingRule.Parameter">
      <summary>Gets or sets style of parameter object for this condition.</summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormattingRule.IsEmpty">
      <summary>Gets is empty flag if not formatting.</summary>
    </member>
    <member name="M:C1.C1Excel.XLConditionalFormattingRule.Clone">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLConditionalFormattingRule" /> object that is a copy of the current instance.
            </summary>
      <returns>A new <see cref="T:C1.C1Excel.XLConditionalFormattingRule" /> object that is a copy of the current instance.</returns>
    </member>
    <member name="T:C1.C1Excel.XLConditionalFormattingRuleCollection">
      <summary>
            The collection of conditional formattin ranges.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormattingRuleCollection.ConditionalFormatting">
      <summary>Gets parent conditional formatting object.</summary>
    </member>
    <member name="T:C1.C1Excel.XLConditionalFormatting">
      <summary>
            Conditional formatting.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLConditionalFormatting.#ctor">
      <summary>
            Initialization conditional formatting object.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormatting.Rules">
      <summary>The collection of conditional formattin ranges.</summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormatting.Ranges">
      <summary>The collection of conditional formattin ranges.</summary>
    </member>
    <member name="M:C1.C1Excel.XLConditionalFormatting.Clone">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLConditionalFormatting" /> object that is a copy of the current instance.
            </summary>
      <returns>A new <see cref="T:C1.C1Excel.XLConditionalFormatting" /> object that is a copy of the current instance.</returns>
    </member>
    <member name="T:C1.C1Excel.XLConditionalFormattingCollection">
      <summary>
            The collection of conditional formattin ranges.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLConditionalFormattingCollection.Sheet">
      <summary>Gets conditional formatting object.</summary>
    </member>
    <member name="T:C1.C1Excel.Strings">
      <summary>
            Static class containing UI strings used by the designer.
            </summary>
    </member>
    <member name="T:C1.C1Excel.XLNamedRangeCollection">
      <summary>
            Represents a collection of <see cref="T:C1.C1Excel.XLNamedRange" /> objects.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLNamedRangeCollection.Count">
      <summary>
            Gets the number of <see cref="T:C1.C1Excel.XLNamedRange" /> objects in the collection.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLNamedRangeCollection.Book">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.C1XLBook" /> object.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.Clear">
      <summary>
            Removes all <see cref="T:C1.C1Excel.XLNamedRange" /> objects from the collection.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.Contains(C1.C1Excel.XLNamedRange)">
      <summary>
            Checks whether the collection contains a specific <see cref="T:C1.C1Excel.XLNamedRange" /> object.
            </summary>
      <param name="namedRange">The <see cref="T:C1.C1Excel.XLNamedRange" /> object to look for.</param>
      <returns>True if the collection contains the range, false otherwise.</returns>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.Contains(System.String)">
      <summary>
            Checks whether the collection contains an <see cref="T:C1.C1Excel.XLNamedRange" /> object with the specified name.
            </summary>
      <param name="name">The name of the <see cref="T:C1.C1Excel.XLNamedRange" /> object to look for.</param>
      <returns>True if the collection contains the range, false otherwise.</returns>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.IndexOf(C1.C1Excel.XLNamedRange)">
      <summary>
            Gets the position of an <see cref="T:C1.C1Excel.XLNamedRange" /> object in the collection.
            </summary>
      <param name="nr">The <see cref="T:C1.C1Excel.XLNamedRange" /> object to look for.</param>
      <returns>The position of the object in the collection, or -1 if the object is not a member of the collection.</returns>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.IndexOf(System.String)">
      <summary>
            Gets the position of an <see cref="T:C1.C1Excel.XLNamedRange" /> object with the specified name in the collection.
            </summary>
      <param name="name">The name of the <see cref="T:C1.C1Excel.XLNamedRange" /> object to look for.</param>
      <returns>The position of the object in the collection, or -1 if the object is not a member of the collection.</returns>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.Remove(C1.C1Excel.XLNamedRange)">
      <summary>
            Removes an <see cref="T:C1.C1Excel.XLNamedRange" /> object from the collection.
            </summary>
      <param name="nr">The <see cref="T:C1.C1Excel.XLNamedRange" /> object to remove from the collection.</param>
      <returns>True if the object was removed, false if it was not a member of the collection.</returns>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.Remove(System.String)">
      <summary>
            Removes an <see cref="T:C1.C1Excel.XLNamedRange" /> object with the specified name from the collection.
            </summary>
      <param name="name">The name of the range to remove from the collection (case-insensitive).</param>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.RemoveAt(System.Int32)">
      <summary>
            Removes an <see cref="T:C1.C1Excel.XLNamedRange" /> object at a specific position from the collection.
            </summary>
      <param name="index">The index of the object to remove from the collection.</param>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.Add(C1.C1Excel.XLNamedRange)">
      <summary>
            Appends an <see cref="T:C1.C1Excel.XLNamedRange" /> object to the collection.
            </summary>
      <param name="namedRange">The <see cref="T:C1.C1Excel.XLNamedRange" /> object to add to the collection.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added (usually because it 
            overlaps another cell range already in the collection).</returns>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.Add(System.String,C1.C1Excel.XLCellRange)">
      <summary>
            Creates an <see cref="T:C1.C1Excel.XLNamedRange" /> object and appends it to the collection.
            </summary>
      <param name="name">The name of the named range to create.</param>
      <param name="cellRange">The <see cref="T:C1.C1Excel.XLCellRange" /> to include in the named range.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added (usually because it 
            overlaps another cell range already in the collection).</returns>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.Add(System.String,C1.C1Excel.XLCellRange[])">
      <summary>
            Creates an <see cref="T:C1.C1Excel.XLNamedRange" /> object and appends it to the collection.
            </summary>
      <param name="name">The name of the named range to create.</param>
      <param name="cellRanges">The array of <see cref="T:C1.C1Excel.XLCellRange" /> to include in the named range.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added (usually because it 
            overlaps another cell range already in the collection).</returns>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.Add(System.String,C1.C1Excel.XLSheet,System.Int32,System.Int32)">
      <summary>
            Creates an <see cref="T:C1.C1Excel.XLNamedRange" /> object and appends it to the collection.
            This overload creates a range containing a single cell.
            </summary>
      <param name="name">The name of <see cref="T:C1.C1Excel.XLNamedRange" /> object.</param>
      <param name="sheet">The worksheet the range belongs to.</param>
      <param name="rowIndex">The row index of the cell in the range.</param>
      <param name="colIndex">The column index of the cell in the range.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added (usually because it 
            overlaps another cell range already in the collection).</returns>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.Add(System.String,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>
            Creates an <see cref="T:C1.C1Excel.XLNamedRange" /> object and appends it to the collection.
            This overload allows you to create a 3-D reference to the same range of cells on multiple worksheets.
            </summary>
      <param name="name">The name of <see cref="T:C1.C1Excel.XLNamedRange" /> object.</param>
      <param name="firstSheetIndex">The index of the first <see cref="T:C1.C1Excel.XLSheet" /> to include.</param>
      <param name="lastSheetIndex">The index of the last <see cref="T:C1.C1Excel.XLSheet" /> to include.</param>
      <param name="rowIndex">The index of the top row in the cell range.</param>
      <param name="colIndex">The index of the left column in the cell range.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added (usually because it 
            overlaps another cell range already in the collection).</returns>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.Add(System.String,C1.C1Excel.XLSheet,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>
            Creates an <see cref="T:C1.C1Excel.XLNamedRange" /> object and appends it to the collection.
            </summary>
      <param name="name">The name of <see cref="T:C1.C1Excel.XLNamedRange" /> object.</param>
      <param name="sheet">The worksheet containing the range.</param>
      <param name="rowIndex">The index of the top row in the cell range.</param>
      <param name="colIndex">The index of the left column in the cell range.</param>
      <param name="rowCount">The number of rows in the cell range.</param>
      <param name="colCount">The number of columns in the cell range.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added (usually because it 
            overlaps another cell range already in the collection).</returns>
    </member>
    <member name="M:C1.C1Excel.XLNamedRangeCollection.Insert(System.Int32,C1.C1Excel.XLNamedRange)">
      <summary>
            Inserts an <see cref="T:C1.C1Excel.XLNamedRange" /> object at a specific position in the collection.
            </summary>
      <param name="index">The position where the object will be inserted.</param>
      <param name="namedRange">The named range to insert in the collection.</param>
      <returns>A reference to the object if it was successfully added to the collection, 
            or null if the object could not be added (usually because it 
            overlaps another cell range already in the collection).</returns>
    </member>
    <member name="P:C1.C1Excel.XLNamedRangeCollection.Item(System.Int32)">
      <summary>
            Gets a reference to the <see cref="T:C1.C1Excel.XLNamedRange" /> object at the specified index.
            Returns null if an invalid index is specified.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLNamedRangeCollection.Item(System.String)">
      <summary>
            Gets a reference to the <see cref="T:C1.C1Excel.XLNamedRange" /> object with the specified name.
            Returns null if an object with the specified name could not be found in the collection.
            </summary>
    </member>
    <member name="T:C1.C1Excel.XLNamedRange">
      <summary>
            Represents a named range of <see cref="T:C1.C1Excel.XLCell" /> objects
            on one or several worksheets.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLNamedRange.Clone">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLNamedRange" /> object that is a copy of the current instance.
            </summary>
      <returns>A new <see cref="T:C1.C1Excel.XLNamedRange" /> object that is a copy of the current instance.</returns>
    </member>
    <member name="P:C1.C1Excel.XLNamedRange.IsEmpty">
      <summary>
            Determines whether the current range is empty.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLNamedRange.IsNameOnly">
      <summary>
            Determines whether the current named range has a valid name
            but does not define a range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLNamedRange.IsBuiltInName">
      <summary>
            Determines whether the range has a built-in name.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLNamedRange.Book">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.C1XLBook" /> object.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLNamedRange.CellRange">
      <summary>
            Gets a reference to the <see cref="T:C1.C1Excel.XLCellRange" /> object that determines
            which cells are contained in the current named range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLNamedRange.CellRanges">
      <summary>
            Gets a reference to array of the <see cref="T:C1.C1Excel.XLCellRange" /> objects that determines
            which cells are contained in the current named range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLNamedRange.Name">
      <summary>
            Gets or sets the name of the current named range.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLNamedRange.Comment">
      <summary>
            Gets or sets the comment text associated with the current named range.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLNamedRange.Contains(C1.C1Excel.XLSheet,System.Int32,System.Int32)">
      <summary>
            Determines whether the range contains a specific cell.
            </summary>
      <param name="sheet">The worksheet containing the cell.</param>
      <param name="row">The row index of the cell.</param>
      <param name="col">The column index of the cell.</param>
      <returns>True if the current named range contains the specified cell, false otherwise.</returns>
    </member>
    <member name="T:C1.C1Excel.XLPrintSettings">
      <summary>
            Provides options and settings for printing <see cref="T:C1.C1Excel.XLSheet" /> objects.
            </summary>
      <remarks>
        <para>The settings are applied to each sheet and are accessible through the
            sheet's <see cref="P:C1.C1Excel.XLSheet.PrintSettings" /> property.</para>
        <para>Note that <see cref="T:C1.C1Excel.C1XLBook" /> does not provide any printing
            services. The settings are used when printing the sheet from Excel.</para>
      </remarks>
    </member>
    <member name="M:C1.C1Excel.XLPrintSettings.Clone">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLPrintSettings" /> object that is a copy of the current instance.
            </summary>
      <returns>A new <see cref="T:C1.C1Excel.XLPrintSettings" /> object that is a copy of the current instance.</returns>
    </member>
    <member name="M:C1.C1Excel.XLPrintSettings.#ctor">
      <summary>
            Creates a new instance of the <see cref="T:C1.C1Excel.XLPrintSettings" /> class.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.Header">
      <summary>
            Gets or sets the string to be displayed as a page header when the sheet is printed.
            </summary>
      <remarks>
        <para>The header string may contain special commands, i.e. placeholders for the page number, 
            current date, or text formatting attributes. Most of these fields are represented by single 
            letters with a leading ampersand ("&amp;").</para>
        <para>The page header is divided into 3 sections: left, center, and right. Each section is 
            introduced by a special command ("&amp;L", "&amp;C", and "&amp;R"). All text and all commands following 
            are part of the selected section.</para>
        <para>The following commands are available:</para>
        <para>&amp;L Start of the left section</para>
        <para>&amp;C Start of the centered section</para>
        <para>&amp;R Start of the right section</para>
        <para>&amp;P Current page number</para>
        <para>&amp;N Page count</para>
        <para>&amp;D Current date</para>
        <para>&amp;T Current time</para>
        <para>&amp;A Sheet name</para>
        <para>&amp;F File name without path</para>
        <para>&amp;Z File path without file name</para>
        <para>&amp;G Picture (file name)</para>
        <para>&amp;B Bold toggle</para>
        <para>&amp;I Italic toggle</para>
        <para>&amp;U Underline toggle</para>
        <para>&amp;E Double underline toggle</para>
        <para>&amp;S Strikeout toggle</para>
        <para>&amp;X Superscript toggle</para>
        <para>&amp;Y Subscript toggle</para>
        <para>&amp;"[FontName]" Set new font</para>
        <para>&amp;"[FontName,FontStyle]" Set new font with specified style. The style is in most cases 
            "Regular", "Bold", "Italic", or "Bold Italic".</para>
            &amp;[fontheight] Set font height in points.
            </remarks>
      <example>
            The code below creates a header with left, center, and right portions.
            <code>
            PrintSettings ps = sheet.PrintSettings;
            ps.Header = "&amp;LHeader Left&amp;CHeader Center&amp;RHeader Right";
            </code></example>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.Footer">
      <summary>
            Gets or sets the string to be displayed as a page footer when the sheet is printed.
            </summary>
      <remarks>
            The footer string has the same structure and embedded commands as the header string. 
            See the <see cref="P:C1.C1Excel.XLPrintSettings.Header" /> property for details.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.CenterHorizontal">
      <summary>
            Gets or sets whether the sheet should be centered horizontally on the page when printed.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.CenterVertical">
      <summary>
            Gets or sets whether the sheet should be centered vertically on the page when printed.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.MarginLeft">
      <summary>
            Gets or sets the left margin, in inches. Set to a negative value to use the default margin.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.MarginRight">
      <summary>
            Gets or sets the right margin, in inches. Set to a negative value to use the default margin.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.MarginTop">
      <summary>
            Gets or sets the top margin, in inches. Set to a negative value to use the default margin.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.MarginBottom">
      <summary>
            Gets or sets the bottom margin, in inches. Set to a negative value to use the default margin.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.MarginHeader">
      <summary>
            Gets or sets the header margin, in inches. Set to a negative value to use the default margin.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.MarginFooter">
      <summary>
            Gets or sets the footer margin, in inches. Set to a negative value to use the default margin.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.PrintHeaders">
      <summary>
            Gets or sets whether row and column headers (the areas with row numbers and column letters) will be printed.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.PrintGridlines">
      <summary>
            Gets or sets whether the gridlines will be printed.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.PaperKind">
      <summary>
            Gets or sets the paper size to use when printing the sheet.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.ScalingFactor">
      <summary>
            Gets or sets the scaling factor (in percent) to use when printing the sheet.
            </summary>
      <remarks>
            Setting the <see cref="P:C1.C1Excel.XLPrintSettings.ScalingFactor" /> property automatically sets the <see cref="P:C1.C1Excel.XLPrintSettings.AutoScale" />
            property to false, causing C1Excel to use the selected scaling factor and to ignore the
            value of the <see cref="P:C1.C1Excel.XLPrintSettings.FitPagesAcross" /> and <see cref="P:C1.C1Excel.XLPrintSettings.FitPagesDown" /> properties.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.AutoScale">
      <summary>
            Gets or sets the scaling mode used for printed output.
            </summary>
      <remarks>
        <para>If <see cref="P:C1.C1Excel.XLPrintSettings.AutoScale" /> is set to true, then the printed sheet will be 
            automatically scaled to fit the number of pages specified by the <see cref="P:C1.C1Excel.XLPrintSettings.FitPagesAcross" /> 
            and <see cref="P:C1.C1Excel.XLPrintSettings.FitPagesDown" /> properties.</para>
        <para>If <see cref="P:C1.C1Excel.XLPrintSettings.AutoScale" /> is set to false, then the printed sheet will be 
            scaled according to the value of the <see cref="P:C1.C1Excel.XLPrintSettings.ScalingFactor" /> property.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.HeaderEven">
      <summary>
            Gets or sets the string to be displayed as a even page header when the sheet is printed.
            </summary>
      <remarks>
            The string for a odd page header in the <see cref="P:C1.C1Excel.XLPrintSettings.Header" /> property.
            This string has the same structure and embedded commands as the main header string. 
            See the <see cref="P:C1.C1Excel.XLPrintSettings.Header" /> property for details.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.FooterEven">
      <summary>
            Gets or sets the string to be displayed as a even page footer when the sheet is printed.
            </summary>
      <remarks>
            The string for a odd page footer in the <see cref="P:C1.C1Excel.XLPrintSettings.Footer" /> property.
            This string has the same structure and embedded commands as the main header string. 
            See the <see cref="P:C1.C1Excel.XLPrintSettings.Header" /> property for details.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.HeaderFirst">
      <summary>
            Gets or sets the string to be displayed as a first page header when the sheet is printed.
            </summary>
      <remarks>
            This string has the same structure and embedded commands as the main header string. 
            See the <see cref="P:C1.C1Excel.XLPrintSettings.Header" /> property for details.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.FooterFirst">
      <summary>
            Gets or sets the string to be displayed as a first page footer when the sheet is printed.
            </summary>
      <remarks>
            This string has the same structure and embedded commands as the main header string. 
            See the <see cref="P:C1.C1Excel.XLPrintSettings.Header" /> property for details.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.ScaleWithDocument">
      <summary>
            Gets or sets the scales header/footer with document used for printed output.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.AlignWithMargins">
      <summary>
            Gets or sets the align header/footer with page margins used for printed output.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.StartPage">
      <summary>
            Gets or sets the initial page number to use when printing the sheet.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.FitPagesAcross">
      <summary>
            Fit the sheet to this number of pages across (0 means use as many as needed).
            </summary>
      <remarks>
            Causes C1Excel to select "fit to page" print mode, ignoring the value of the 
            <see cref="P:C1.C1Excel.XLPrintSettings.ScalingFactor" /> property.
            Setting the <see cref="P:C1.C1Excel.XLPrintSettings.FitPagesAcross" /> or <see cref="P:C1.C1Excel.XLPrintSettings.FitPagesDown" /> properties automatically 
            sets the <see cref="P:C1.C1Excel.XLPrintSettings.AutoScale" /> property to true, causing C1Excel to calculate the scaling factor 
            based on the given number of pages and to ignore the value of the <see cref="P:C1.C1Excel.XLPrintSettings.ScalingFactor" />
            property.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.FitPagesDown">
      <summary>
            Fit the sheet to this number of pages down (0 means use as many as needed).
            </summary>
      <remarks>
            Setting the <see cref="P:C1.C1Excel.XLPrintSettings.FitPagesAcross" /> or <see cref="P:C1.C1Excel.XLPrintSettings.FitPagesDown" /> properties automatically 
            sets the <see cref="P:C1.C1Excel.XLPrintSettings.AutoScale" /> property to true, causing C1Excel to calculate the scaling factor 
            based on the given number of pages and to ignore the value of the <see cref="P:C1.C1Excel.XLPrintSettings.ScalingFactor" />
            property.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.Copies">
      <summary>
            Gets or sets the number of copies to print.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.PrintPagesInRows">
      <summary>
            Gets or sets whether to print the pages in rows (across first) or in columns (down first).
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.Landscape">
      <summary>
            Gets or sets whether to print the sheet in landscape mode.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.BlackAndWhite">
      <summary>
            Gets or sets whether to print the sheet in monochrome mode.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.DraftQuality">
      <summary>
            Gets or sets whether to print the sheet in draft quality mode.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.HeaderPictureLeft">
      <summary>
            Gets or sets the <see cref="T:System.Drawing.Image" /> or <see cref="T:C1.C1Excel.XLPictureShape" /> for the left part of the header.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.HeaderPictureCenter">
      <summary>
            Gets or sets the <see cref="T:System.Drawing.Image" /> or <see cref="T:C1.C1Excel.XLPictureShape" /> for the center part of the header.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.HeaderPictureRight">
      <summary>
            Gets or sets the <see cref="T:System.Drawing.Image" /> or <see cref="T:C1.C1Excel.XLPictureShape" /> for the right part of the header.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.FooterPictureLeft">
      <summary>
            Gets or sets the <see cref="T:System.Drawing.Image" /> or <see cref="T:C1.C1Excel.XLPictureShape" /> for the left part of the footer.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.FooterPictureCenter">
      <summary>
            Gets or sets the <see cref="T:System.Drawing.Image" /> or <see cref="T:C1.C1Excel.XLPictureShape" /> for the center part of the footer.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLPrintSettings.FooterPictureRight">
      <summary>
            Gets or sets the <see cref="T:System.Drawing.Image" /> or <see cref="T:C1.C1Excel.XLPictureShape" /> for the right part of the footer.
            </summary>
    </member>
    <member name="T:C1.C1Excel.XLRowCollection">
      <summary>
            Represents a collection of <see cref="T:C1.C1Excel.XLRow" /> objects that represent the 
            individual rows in each <see cref="T:C1.C1Excel.XLSheet" />.
            </summary>
      <remarks>
            The collection has methods for counting, enumerating, adding, and removing 
            rows from the collection.
            </remarks>
      <example>
            Note that you can create rows automatically by using the sheet's indexer. 
            For example, the following code retrieves the cell at coordinates (3,3) 
            and in doing so automatically creates four rows and four columns automatically:
            <code>
            C1XLBook book = new C1XLBook();
            sheet = book.Sheets[0];
            XLCell cell   = sheet[3,3]; // creates 4 rows and 4 columns
            </code></example>
    </member>
    <member name="P:C1.C1Excel.XLRowCollection.Book">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.C1XLBook" /> object.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLRowCollection.Count">
      <summary>
            Gets the number of items in the collection.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLRowCollection.Sheet">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.XLSheet" /> object.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLRowCollection.Clear">
      <summary>
            Removes all items from the collection.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLRowCollection.Add">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLRow" /> object and adds it to the collection.
            </summary>
      <returns>A reference to the new <see cref="T:C1.C1Excel.XLRow" /> object.</returns>
    </member>
    <member name="M:C1.C1Excel.XLRowCollection.Add(C1.C1Excel.XLRow)">
      <summary>
            Adds an <see cref="T:C1.C1Excel.XLRow" /> object to the collection.
            </summary>
      <param name="row">The item to add to the collection.</param>
      <returns>A reference to the item that was added to the collection 
            (in this case, always the <paramref name="row" /> parameter).</returns>
    </member>
    <member name="M:C1.C1Excel.XLRowCollection.Insert(System.Int32)">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLRow" /> object and inserts it at a 
            specific position in the collection.
            </summary>
      <param name="index">Position where the new item will be inserted.</param>
      <returns>A reference to the new item.</returns>
    </member>
    <member name="M:C1.C1Excel.XLRowCollection.Contains(C1.C1Excel.XLRow)">
      <summary>
            Determines whether an <see cref="T:C1.C1Excel.XLRow" /> is a member of the collection.
            </summary>
      <param name="row">Item to look for.</param>
      <returns>True if the collection contains the item, False otherwise.</returns>
    </member>
    <member name="M:C1.C1Excel.XLRowCollection.IndexOf(C1.C1Excel.XLRow)">
      <summary>
            Gets the index of a given <see cref="T:C1.C1Excel.XLRow" /> object in the collection.
            </summary>
      <param name="row">Item to look for.</param>
      <returns>The position of the item in the collection, or -1 if
            the item is not a member of the collection.</returns>
    </member>
    <member name="M:C1.C1Excel.XLRowCollection.RemoveAt(System.Int32)">
      <summary>
            Removes the <see cref="T:C1.C1Excel.XLRow" /> object at a given position from the collection.
            </summary>
      <param name="index">Index of the item to remove from the collection.</param>
      <returns>A reference to the item that was removed from the collection.</returns>
    </member>
    <member name="M:C1.C1Excel.XLRowCollection.Insert(System.Int32,C1.C1Excel.XLRow)">
      <summary>
            Inserts an <see cref="T:C1.C1Excel.XLRow" /> object at a specific position in the collection.
            </summary>
      <param name="index">Position where the item will be inserted.</param>
      <param name="row">Item that will be inserted.</param>
      <returns>A reference to the item that was added to the collection.</returns>
      <remarks>
        <para>The maximum number of <see cref="T:C1.C1Excel.XLRow" /> objects in a <see cref="T:C1.C1Excel.XLSheet" /> 
            is 65,536. This is a limitation imposed by Excel 2003 and below.</para>
        <para>For Excel 2007 and above, the maximum number of <see cref="T:C1.C1Excel.XLRow" /> objects
            in an <see cref="T:C1.C1Excel.XLSheet" /> is 1,048,576.</para>
      </remarks>
    </member>
    <member name="M:C1.C1Excel.XLRowCollection.Remove(C1.C1Excel.XLRow)">
      <summary>
            Removes an <see cref="T:C1.C1Excel.XLRow" /> object from the collection.
            </summary>
      <param name="row">Item to be removed from the collection.</param>
      <returns>A reference to the item that was removed.</returns>
    </member>
    <member name="P:C1.C1Excel.XLRowCollection.Item(System.Int32)">
      <summary>
            Returns a reference to the <see cref="T:C1.C1Excel.XLRow" /> object at the specified index.
            </summary>
      <remarks>
            The indexer will create a new <see cref="T:C1.C1Excel.XLRow" /> object at the
            specified position if necessary. It never returns null.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLRowCollection.Frozen">
      <summary>
            Gets or sets the number of frozen rows in the collection.
            </summary>
      <remarks>
            Frozen rows are displayed on the top of the sheet and do not scroll vertically.
            They are useful for displaying column headers.
            </remarks>
    </member>
    <member name="T:C1.C1Excel.XLColumnCollection">
      <summary>
            Represents a collection of <see cref="T:C1.C1Excel.XLColumn" /> objects that represent the 
            individual columns in each <see cref="T:C1.C1Excel.XLSheet" />.
            </summary>
      <remarks>
        <para>The collection has methods for counting, enumerating, adding, and removing 
            columns from the collection.</para>
        <para>The <see cref="T:C1.C1Excel.XLColumn" /> objects do not contain any data. If you remove 
            a column from the collection, the data will be lost. If you later re-insert that 
            same column back into the collection, the column will be blank.</para>
      </remarks>
      <example>
            Note that you can create columns automatically by using the sheet's indexer. 
            For example, the following code retrieves the cell at coordinates (3,3) 
            and in doing so automatically creates four rows and four columns automatically:
            <code>
            C1XLBook book  = new C1XLBook();
            XLSheet  sheet = book.Sheets[0];
            XLCell   cell  = sheet[3,3]; // creates 4 rows and 4 columns
            </code></example>
    </member>
    <member name="P:C1.C1Excel.XLColumnCollection.Count">
      <summary>
            Gets the number of items in the collection.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLColumnCollection.Book">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.C1XLBook" /> object.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLColumnCollection.Sheet">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.XLSheet" /> object.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLColumnCollection.Clear">
      <summary>
            Removes all items from the collection.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLColumnCollection.Add">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLColumn" /> object and adds it to the collection.
            </summary>
      <returns>A reference to the new <see cref="T:C1.C1Excel.XLColumn" /> object.</returns>
    </member>
    <member name="M:C1.C1Excel.XLColumnCollection.Add(C1.C1Excel.XLColumn)">
      <summary>
            Adds an <see cref="T:C1.C1Excel.XLColumn" /> object to the collection.
            </summary>
      <param name="col">The item to add to the collection.</param>
      <returns>A reference to the item that was added to the collection 
            (in this case, always the <paramref name="col" /> parameter).</returns>
    </member>
    <member name="M:C1.C1Excel.XLColumnCollection.Insert(System.Int32)">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLColumn" /> object and inserts it at a 
            specific position in the collection.
            </summary>
      <param name="index">Position where the new item will be inserted.</param>
      <returns>A reference to the new item.</returns>
    </member>
    <member name="M:C1.C1Excel.XLColumnCollection.Contains(C1.C1Excel.XLColumn)">
      <summary>
            Determines whether an <see cref="T:C1.C1Excel.XLColumn" /> is a member of the collection.
            </summary>
      <param name="col">Item to look for.</param>
      <returns>True if the collection contains the item, False otherwise.</returns>
    </member>
    <member name="M:C1.C1Excel.XLColumnCollection.IndexOf(C1.C1Excel.XLColumn)">
      <summary>
            Gets the index of a given <see cref="T:C1.C1Excel.XLRow" /> object in the collection.
            </summary>
      <param name="col">Item to look for.</param>
      <returns>The position of the item in the collection, or -1 if
            the item is not a member of the collection.</returns>
    </member>
    <member name="M:C1.C1Excel.XLColumnCollection.RemoveAt(System.Int32)">
      <summary>
            Removes the <see cref="T:C1.C1Excel.XLColumn" /> object at a given position from the collection.
            </summary>
      <param name="index">Index of the item to remove from the collection.</param>
      <returns>A reference to the item that was removed from the collection.</returns>
    </member>
    <member name="M:C1.C1Excel.XLColumnCollection.Insert(System.Int32,C1.C1Excel.XLColumn)">
      <summary>
            Inserts an <see cref="T:C1.C1Excel.XLColumn" /> object at a specific position in the collection.
            </summary>
      <param name="index">Position where the item will be inserted.</param>
      <param name="col">Item that will be inserted.</param>
      <returns>A reference to the item that was added to the collection.</returns>
      <remarks>
        <para>The maximum number of <see cref="T:C1.C1Excel.XLColumn" /> objects in an <see cref="T:C1.C1Excel.XLSheet" /> 
            is 256. This is a limitation imposed by Excel 2003 and below.</para>
        <para>For Excel 2007 and above, the maximum number of <see cref="T:C1.C1Excel.XLColumn" /> objects
            in an <see cref="T:C1.C1Excel.XLSheet" /> is 18,278.</para>
      </remarks>
    </member>
    <member name="M:C1.C1Excel.XLColumnCollection.Insert(System.Int32,C1.C1Excel.XLColumn,C1.C1Excel.XLCell[])">
      <summary>
            Inserts an <see cref="T:C1.C1Excel.XLColumn" /> object at a specific position in the collection.
            </summary>
      <param name="index">Position where the item will be inserted.</param>
      <param name="col">Item that will be inserted.</param>
      <param name="cells">Array ot the cells for inserted column.</param>
      <returns>A reference to the item that was added to the collection.</returns>
    </member>
    <member name="M:C1.C1Excel.XLColumnCollection.Remove(C1.C1Excel.XLColumn)">
      <summary>
            Removes an <see cref="T:C1.C1Excel.XLColumn" /> object from the collection.
            </summary>
      <param name="col">Item to be removed from the collection.</param>
      <returns>A reference to the item that was removed.</returns>
    </member>
    <member name="P:C1.C1Excel.XLColumnCollection.Item(System.Int32)">
      <summary>
            Returns a reference to the <see cref="T:C1.C1Excel.XLColumn" /> object at the specified
            index.
            </summary>
      <remarks>
            The indexer will create a new <see cref="T:C1.C1Excel.XLColumn" /> object at the
            specified position if necessary. It never returns null.</remarks>
    </member>
    <member name="P:C1.C1Excel.XLColumnCollection.Frozen">
      <summary>
            Gets or sets the number of frozen columns in the collection.
            </summary>
      <remarks>
            Frozen columns are displayed on the right side of the sheet and do not scroll horizontally. 
            They are useful for displaying row headers.
            </remarks>
    </member>
    <member name="T:C1.C1Excel.XLRow">
      <summary>
            Represents a row in a worksheet, provides properties for setting 
            the row's height, style, and visibility.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLRow.#ctor">
      <summary>
            Creates a new instance of the <see cref="T:C1.C1Excel.XLRow" /> class.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLRow.Clone">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLRow" /> object that is a copy of the current instance.
            </summary>
      <returns>A new <see cref="T:C1.C1Excel.XLRow" /> object that is a copy of the current instance.</returns>
    </member>
    <member name="P:C1.C1Excel.XLRow.Visible">
      <summary>
            Gets or sets whether the row is visible.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLRow.PageBreak">
      <summary>
            Gets or sets whether there will be a forced page break after this row.
            </summary>
      <example>
            The code below inserts forced page breaks at every 10th row on a sheet, and
            clears the breaks at all other rows.
            <code>
            C1.C1Excel.XLSheet sheet = c1XLBook1.Sheets[0];
            for (int r = 0; r &lt; sheet.Rows.Count; r++)
            {
                sheet.Rows[r].PageBreak = (r &gt; 0 &amp;&amp; r % 10 == 0);
            }
            </code></example>
    </member>
    <member name="P:C1.C1Excel.XLRow.Height">
      <summary>
            Gets or sets the height of the row, in twips.
            </summary>
      <remarks>
        <para>A value of -1 causes the row to be displayed using the sheet's <see cref="P:C1.C1Excel.XLSheet.DefaultRowHeight" />
            if cells of the row not contain data, otherwise this height of the row depended from used font of the data.</para>
        <para>To convert between pixels and twips, use the <see cref="M:C1.C1Excel.C1XLBook.TwipsToPixels(System.Double)" />
            and <see cref="M:C1.C1Excel.C1XLBook.PixelsToTwips(System.Double)" /> methods.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLRow.Style">
      <summary>
            Gets or sets the <see cref="T:C1.C1Excel.XLStyle" /> object that determines the appearance
            of the row.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLRow.Sheet">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.XLSheet" /> object.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLRow.Book">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.C1XLBook" /> object.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLRow.IsCustomHeight">
      <summary>
            Gets or sets custom height flag for the row.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLRow.IsSubtotal">
      <summary>
            Gets whether the row is subtotal.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLRow.OutlineLevel">
      <summary>
            Gets or sets subtotal outline level for the row.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLRow.Collapsed">
      <summary>
            Gets or sets collapsed flag for the row.
            </summary>
    </member>
    <member name="T:C1.C1Excel.XLColumn">
      <summary>
            Represents a column in a worksheet. This class provides properties for setting 
            the column's width, style, and visibility.
            </summary>
      <remarks>The <see cref="T:C1.C1Excel.XLColumn" /> objects do not contain any data. If you remove 
            a column from the collection, the data will be lost. If you later re-insert that 
            same column back into the collection, the column will be blank.
            </remarks>
    </member>
    <member name="M:C1.C1Excel.XLColumn.#ctor">
      <summary>
            Creates a new instance of the <see cref="T:C1.C1Excel.XLColumn" /> class.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLColumn.Clone">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLColumn" /> object that is a copy of the current instance.
            </summary>
      <returns>A new <see cref="T:C1.C1Excel.XLColumn" /> object that is a copy of the current instance.</returns>
    </member>
    <member name="M:C1.C1Excel.XLColumn.Clone(C1.C1Excel.XLCell[]@)">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLColumn" /> object that is a copy of the current instance.
            </summary>
      <param name="cells">The array of the cells for the current instance.</param>
      <returns>A new <see cref="T:C1.C1Excel.XLColumn" /> object that is a copy of the current instance.</returns>
    </member>
    <member name="P:C1.C1Excel.XLColumn.Visible">
      <summary>
            Gets or sets whether the column is visible.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLColumn.PageBreak">
      <summary>
            Gets or sets whether there will be a forced page break after this column.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLColumn.Width">
      <summary>
            Gets or sets the width of the column, in twips.
            </summary>
      <remarks>
        <para>A value of -1 indicates that the column should be displayed using
            the sheet's <see cref="P:C1.C1Excel.XLSheet.DefaultColumnWidth" />.</para>
        <para>To convert between pixels and twips, use the <see cref="M:C1.C1Excel.C1XLBook.TwipsToPixels(System.Double)" />
            and <see cref="M:C1.C1Excel.C1XLBook.PixelsToTwips(System.Double)" /> methods.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLColumn.Style">
      <summary>
            Gets or sets the <see cref="T:C1.C1Excel.XLStyle" /> object that determines the appearance
            of the column.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLColumn.Sheet">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.XLSheet" /> object.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLColumn.Book">
      <summary>
            Gets a reference to the parent <see cref="T:C1.C1Excel.C1XLBook" /> object.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLColumn.IsSubtotal">
      <summary>
            Gets whether the column is subtotal.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLColumn.OutlineLevel">
      <summary>
            Gets or sets subtotal outline level for the column.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLColumn.Collapsed">
      <summary>
            Gets or sets collapsed flag for the column.
            </summary>
    </member>
    <member name="T:C1.C1Excel.ConsolidationFunction">
      <summary>
            Specifies consolidation function for sheet subtotals.
            </summary>
    </member>
    <member name="F:C1.C1Excel.ConsolidationFunction.Sum">
      <summary>Represents Sum function.</summary>
    </member>
    <member name="F:C1.C1Excel.ConsolidationFunction.Count">
      <summary>Represents Count function.</summary>
    </member>
    <member name="F:C1.C1Excel.ConsolidationFunction.Average">
      <summary>Represents Average function.</summary>
    </member>
    <member name="F:C1.C1Excel.ConsolidationFunction.Max">
      <summary>Represents Max function.</summary>
    </member>
    <member name="F:C1.C1Excel.ConsolidationFunction.Min">
      <summary>Represents Min function.</summary>
    </member>
    <member name="F:C1.C1Excel.ConsolidationFunction.Product">
      <summary>Represents Product function.</summary>
    </member>
    <member name="F:C1.C1Excel.ConsolidationFunction.CountNums">
      <summary>Represents Count Numbers function.</summary>
    </member>
    <member name="F:C1.C1Excel.ConsolidationFunction.StdDev">
      <summary>Represents StdDev function.</summary>
    </member>
    <member name="F:C1.C1Excel.ConsolidationFunction.StdDevp">
      <summary>Represents StdDevp function.</summary>
    </member>
    <member name="F:C1.C1Excel.ConsolidationFunction.Var">
      <summary>Represents Var function.</summary>
    </member>
    <member name="F:C1.C1Excel.ConsolidationFunction.Varp">
      <summary>Represents Varp function.</summary>
    </member>
    <member name="T:C1.C1Excel.XLSheetCollection">
      <summary>
            Collection of <see cref="T:C1.C1Excel.XLSheet" /> objects that represent the individual 
            worksheets in a <see cref="T:C1.C1Excel.C1XLBook" />.
            </summary>
      <remarks>
            Provides methods and properties for counting, enumerating, adding, and removing sheets from 
            the workbook.
            </remarks>
      <example>
            Use the <see cref="T:C1.C1Excel.C1XLBook" /><see cref="P:C1.C1Excel.C1XLBook.Sheets" /> property to get the 
            book's sheet collection. For example, the code below gets a reference 
            to the first sheet on the book and then prints the sheet's name:
            <code>
            C1XLBook book = new C1XLBook();
            XLSheet sheet = book.Sheets[0];
            Debug.WriteLine(sheet.Name);
            </code></example>
    </member>
    <member name="P:C1.C1Excel.XLSheetCollection.Book">
      <summary>
            Gets a reference to the <see cref="T:C1.C1Excel.C1XLBook" /> that owns the collection.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheetCollection.Count">
      <summary>
            Gets the number of <see cref="T:C1.C1Excel.XLSheet" /> objects in the collection.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLSheetCollection.Add">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLSheet" /> and appends it to the collection.
            </summary>
      <returns>A reference to the new <see cref="T:C1.C1Excel.XLSheet" /> object.</returns>
    </member>
    <member name="M:C1.C1Excel.XLSheetCollection.Add(C1.C1Excel.XLSheet)">
      <summary>
            Appends an existing <see cref="T:C1.C1Excel.XLSheet" /> to the collection.
            </summary>
      <param name="sheet">The object to add to the collection.</param>
      <returns>A reference to the object that was added to the collection
            (in this case, always the <paramref name="sheet" /> parameter).</returns>
    </member>
    <member name="M:C1.C1Excel.XLSheetCollection.Insert(System.Int32)">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLSheet" /> and inserts it at a specific 
            position in the collection.
            </summary>
      <param name="index">Index where the new <see cref="T:C1.C1Excel.XLSheet" /> will be added.</param>
      <returns>A reference to the new <see cref="T:C1.C1Excel.XLSheet" /> object.</returns>
    </member>
    <member name="M:C1.C1Excel.XLSheetCollection.Contains(C1.C1Excel.XLSheet)">
      <summary>
            Determines whether the collection contains a specific <see cref="T:C1.C1Excel.XLSheet" /> object.
            </summary>
      <param name="sheet">The object to look for in the collection.</param>
      <returns>True if the collection contains the object; False otherwise.</returns>
    </member>
    <member name="M:C1.C1Excel.XLSheetCollection.Contains(System.String)">
      <summary>
            Determines whether the collection contains an <see cref="T:C1.C1Excel.XLSheet" /> object
            with a given name.
            </summary>
      <param name="sheetName">The name of the sheet to look for (case-insensitive).</param>
      <returns>True if the collection contains an <see cref="T:C1.C1Excel.XLSheet" /> with the
            given <paramref name="sheetName" />; False otherwise.</returns>
    </member>
    <member name="M:C1.C1Excel.XLSheetCollection.IndexOf(C1.C1Excel.XLSheet)">
      <summary>
            Gets the index of a given <see cref="T:C1.C1Excel.XLSheet" /> in the collection.
            </summary>
      <param name="sheet">The object to look for.</param>
      <returns>The index of the object in the collection, or -1 if the object is not
            a member of the collection.</returns>
    </member>
    <member name="M:C1.C1Excel.XLSheetCollection.Remove(C1.C1Excel.XLSheet)">
      <summary>
            Removes an <see cref="T:C1.C1Excel.XLSheet" /> from the collection.
            </summary>
      <param name="sheet">The <see cref="T:C1.C1Excel.XLSheet" /> object to remove from the collection.</param>
    </member>
    <member name="M:C1.C1Excel.XLSheetCollection.RemoveAt(System.Int32)">
      <summary>
            Removes the <see cref="T:C1.C1Excel.XLSheet" /> at a specific index from the collection.
            </summary>
      <param name="index">Index of the object to remove from the collection.</param>
    </member>
    <member name="M:C1.C1Excel.XLSheetCollection.Clear">
      <summary>
            Removes all items from the collection.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLSheetCollection.Add(System.String)">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLSheet" /> with a given name and appends it to the collection.
            </summary>
      <param name="name">The name of the new <see cref="T:C1.C1Excel.XLSheet" />.</param>
      <returns>A reference to the new <see cref="T:C1.C1Excel.XLSheet" /> object.</returns>
    </member>
    <member name="M:C1.C1Excel.XLSheetCollection.Remove(System.String)">
      <summary>
            Removes the <see cref="T:C1.C1Excel.XLSheet" /> with the specified name from the collection.
            </summary>
      <param name="name">Name of the sheet to remove from the collection (case-insensitive).</param>
    </member>
    <member name="M:C1.C1Excel.XLSheetCollection.Insert(System.Int32,C1.C1Excel.XLSheet)">
      <summary>
            Inserts an <see cref="T:C1.C1Excel.XLSheet" /> object into the collection at the specified position.
            </summary>
      <param name="index">Position where the item will be inserted.</param>
      <param name="sheet">
        <see cref="T:C1.C1Excel.XLSheet" /> object to add to the collection.</param>
      <returns>A reference to the object that was added to the collection
            (in this case, always the <paramref name="sheet" /> parameter).</returns>
    </member>
    <member name="M:C1.C1Excel.XLSheetCollection.IndexOf(System.String)">
      <summary>
            Gets the position of the sheet with the specified name in the collection.
            </summary>
      <param name="name">Name of the object to look for (case-insensitive).</param>
      <returns>The index of the sheet in the collection, or -1 if the sheet can't be 
            found in the collection.</returns>
    </member>
    <member name="P:C1.C1Excel.XLSheetCollection.FirstIndex">
      <summary>
            Gets or sets the index of the sheet that is initially opened
            when a <see cref="T:C1.C1Excel.C1XLBook" /> file is loaded into Excel.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheetCollection.SelectedIndex">
      <summary>
            Gets or sets the index of the sheet that is selected
            when a <see cref="T:C1.C1Excel.C1XLBook" /> file is loaded into Excel.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheetCollection.Item(System.Int32)">
      <summary>
            Gets the <see cref="T:C1.C1Excel.XLSheet" /> at a given position in the collection.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheetCollection.Item(System.String)">
      <summary>
            Gets the <see cref="T:C1.C1Excel.XLSheet" /> with the given name (case-insensitive).
            </summary>
    </member>
    <member name="T:C1.C1Excel.XLSheet">
      <summary>
            Represents individual worksheets in an Excel workbook (<see cref="T:C1.C1Excel.C1XLBook" />).
            </summary>
      <remarks>
            Provides indexers to get or set the value of individual cells (<see cref="T:C1.C1Excel.XLCell" />) 
            and to access the <see cref="P:C1.C1Excel.XLSheet.Rows" /> and <see cref="P:C1.C1Excel.XLSheet.Columns" /> on the sheet.
            </remarks>
      <example>
            Use the <see cref="T:C1.C1Excel.C1XLBook" /><see cref="P:C1.C1Excel.C1XLBook.Sheets" /> indexer to get an individual 
            sheet. For example, the code below gets a reference to the first sheet on the book, then 
            prints the number of rows and columns on the sheet:
            <code>
            C1XLBook book = new C1XLBook();
            XLSheet sheet = book.Sheets[0];
            Debug.WriteLine("Sheet has {0} rows and {1} columns",
              sheet.Rows.Count, sheet.Columns.Count);
            </code></example>
    </member>
    <member name="M:C1.C1Excel.XLSheet.Clone">
      <summary>
            Creates a copy of this sheet.
            </summary>
      <returns>A new <see cref="T:C1.C1Excel.XLSheet" /> object with the same contents and formatting as this sheet.</returns>
      <remarks>
        <para>After cloning a sheet, you must rename it and then add it to the book (duplicate names are not
            allowed).</para>
        <para>This method is useful for applications that generate books with a large number of similar sheets.</para>
      </remarks>
      <example>
            The code below loads a book that contains a template sheet, creates 12 copies of that sheet, removes the
            template sheet, then saves the file with a new name.
            <code>
            // load book with template sheet
            _c1xl.Load(@"c:\temp\template.xls");
            
            // create 12 copies of the template sheet
            XLSheet templateSheet = _c1xl.Sheets["Template"];
            for (int month = 1; month &lt;= 12; month++)
            {
              XLSheet newSheet = templateSheet.Clone();
              newSheet.Name = month.ToString(); // rename clone
              newSheet[0,0].Value = month;      // make changes
              _c1xl.Sheets.Add(newSheet);       // add clone to book
            }
            
            // remove the template sheet and save with new name
            _c1xl.Sheets.Remove("Template");
            _c1xl.Save(@"C:\temp\expense_report.xls");
            </code></example>
    </member>
    <member name="P:C1.C1Excel.XLSheet.Book">
      <summary>
            Gets a reference to the <see cref="T:C1.C1Excel.C1XLBook" /> that owns the sheet.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.Name">
      <summary>
            Gets or sets the name of the <see cref="T:C1.C1Excel.XLSheet" />.
            </summary>
      <remarks>
        <para>When you open a workbook in Excel, the sheet names appear in the tabs 
            below the work area.</para>
        <para>Sheet names can be used as indexers, so they should be unique.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLSheet.Rows">
      <summary>
            Gets a reference to the sheet's row collection.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.Columns">
      <summary>
            Gets a reference to sheet's column collection.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.MergedCells">
      <summary>
            Gets an <see cref="T:C1.C1Excel.XLCellRangeCollection" /> that contains the
            collection of cells that are merged on the sheet.
            </summary>
      <remarks>
            The collection has methods for inspecting, adding, or clearing merged ranges in a sheet. 
            Each merged range is represented by an <see cref="T:C1.C1Excel.XLCellRange" /> object.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLSheet.SelectedCells">
      <summary>
            Gets an <see cref="T:C1.C1Excel.XLCellRangeCollection" /> that contains the
            collection of cells that are selected on the sheet.
            </summary>
      <remarks>
            The collection has methods for inspecting, adding, or clearing merged ranges in a sheet. 
            Each selected range is represented by an <see cref="T:C1.C1Excel.XLCellRange" /> object.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLSheet.Comments">
      <summary>
            Gets an <see cref="T:C1.C1Excel.XLCommentCollection" /> that contains the
            collection of comments that on the sheet.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.DefaultRowHeight">
      <summary>
            Gets or sets the default row height for the sheet (in twips).
            </summary>
      <remarks>
        <para>You can set the height of individual rows using the <see cref="T:C1.C1Excel.XLRow" /> class. 
            Any rows that do not have a custom height assigned to them will be displayed using 
            the sheet's <see cref="P:C1.C1Excel.XLSheet.DefaultRowHeight" />.</para>
        <para>The <see cref="P:C1.C1Excel.XLSheet.DefaultRowHeight" /> property is expressed in twips (1/20th of a point), 
            rather than pixels. This allows sheets to maintain their aspect regardless of the resolution 
            of the display.</para>
        <para>To convert twips into pixels, use the <see cref="M:C1.C1Excel.C1XLBook.TwipsToPixels(System.Double)" /> method 
            in <see cref="T:C1.C1Excel.C1XLBook" />. To convert pixels into twips, use the <see cref="M:C1.C1Excel.C1XLBook.PixelsToTwips(System.Double)" /> 
            method.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLSheet.DefaultColumnWidth">
      <summary>
            Gets or sets the default column width for the sheet (in twips).
            </summary>
      <remarks>
        <para>You can set the width of individual rows using the <see cref="T:C1.C1Excel.XLColumn" /> class. Any columns that 
            do not have a custom width assigned to them will be displayed using the sheet's <see cref="P:C1.C1Excel.XLSheet.DefaultColumnWidth" />.</para>
        <para>The <see cref="P:C1.C1Excel.XLSheet.DefaultColumnWidth" /> property is expressed in twips (1/20th of a point), 
            rather than pixels. This allows sheets to maintain their aspect regardless of the resolution 
            of the display.</para>
        <para>To convert twips into pixels, use the <see cref="M:C1.C1Excel.C1XLBook.TwipsToPixels(System.Double)" /> method in 
            <see cref="T:C1.C1Excel.C1XLBook" />. To convert pixels into twips, use the <see cref="M:C1.C1Excel.C1XLBook.PixelsToTwips(System.Double)" /> method.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLSheet.Item(System.Int32,System.Int32)">
      <summary>
            Gets the cell at a specified position on the sheet, creating a new cell if necessary.
            </summary>
      <remarks>
        <para>If the specified cell doesn't exist when the indexer is invoked, the sheet will be expanded and 
            a new cell will be created, then returned. This makes it easy to create and populate sheets.</para>
        <para>If you want to determine whether a specific cell has been defined, use the <see cref="M:C1.C1Excel.XLSheet.GetCell(System.Int32,System.Int32)" /> 
            method instead.</para>
        <para>The indexer is generally more useful when creating and populating sheets. The <see cref="M:C1.C1Excel.XLSheet.GetCell(System.Int32,System.Int32)" /> 
            method is more useful when loading existing sheets.</para>
      </remarks>
    </member>
    <member name="M:C1.C1Excel.XLSheet.GetCell(System.Int32,System.Int32)">
      <summary>
            Gets a reference to a cell at the specified coordinates or null if the cell doesn't exist.
            </summary>
      <param name="rowIndex">Index of the row.</param>
      <param name="colIndex">Index of the column.</param>
      <returns>A reference to the <see cref="T:C1.C1Excel.XLCell" /> object at the specified coordinates, or null if there
            is no cell at the specified position.</returns>
      <remarks>
        <para>To populate new sheets, use the <see cref="P:C1.C1Excel.C1XLBook.Sheets" /> indexer instead.</para>
        <para>The indexer will automatically create new rows, columns, and cells as needed, and will never return null.</para>
      </remarks>
    </member>
    <member name="M:C1.C1Excel.XLSheet.GetFormattedText(System.Int32,System.Int32)">
      <summary>
            Gets formatted text is using <see cref="T:C1.C1Excel.XLStyle" /> format value of the cell.
            </summary>
      <param name="rowIndex">Index of the row.</param>
      <param name="colIndex">Index of the column.</param>
      <returns>The formatted text of the cell with row and column indexes.</returns>
    </member>
    <member name="P:C1.C1Excel.XLSheet.Locked">
      <summary>
            Gets or sets a value that determines if the sheet is locked for editing.
            </summary>
      <remarks>
        <para>Sheets and styles can be locked. By default, sheets are unlocked and styles are locked. 
            This combination allows users to edit the cells in Excel.</para>
        <para>To protect a cell against editing in Excel, both the sheet and the cell style must have the 
            <see cref="P:C1.C1Excel.XLSheet.Locked" /> property set to true.</para>
        <para>To lock most cells on a sheet and allow editing of only a few cells, lock the sheet, then 
            create an unlocked style and assign it to the cells that should be editable.</para>
      </remarks>
      <example>
            The code below creates a data entry sheet. Most cells are locked, except for the ones where the user 
            is supposed to enter data.
            <code>
            // start with a single locked sheet
            _c1xl.Clear();
            XLSheet sheet = _c1xl.Sheets[0];
            sheet.Locked = true;
            
            // create an unlocked style
            XLStyle dataEntry = new XLStyle(_c1xl);
            dataEntry.Locked = false;
            dataEntry.BackColor = Color.Beige;
            
            // create data entry titles
            sheet[0,0].Value = "Name:";
            sheet[1,0].Value = "Address:";
            sheet[2,0].Value = "Phone #:";
            
            create data entry cells (unlocked)
            sheet[0,1].Style = dataEntry;
            sheet[1,1].Style = dataEntry;
            sheet[2,1].Style = dataEntry;
            
            // save the book
            _c1xl.Save(@"c:\temp\Protected.xls");
            </code></example>
    </member>
    <member name="P:C1.C1Excel.XLSheet.Visible">
      <summary>
            Gets or sets the sheet's visibility.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.ShowGridLines">
      <summary>
            Gets or sets whether Excel should show the grid lines when displaying the sheet.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.ShowHeaders">
      <summary>
            Gets or sets whether Excel should show the row and column headers when displaying the sheet.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.PageBreakPreview">
      <summary>
            Gets or sets whether Excel should show page break preview when displaying the sheet.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.ShowZeros">
      <summary>
            Gets or sets whether Excel should show the zero values on the sheet.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.Scale">
      <summary>
            Gets or sets the view scale of this worksheet using percentages.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.GridColor">
      <summary>
            Gets or sets the color used to display gridlines.
            </summary>
      <remarks>
        <para>Set this property to <b>Color.Transparent</b> to display the grid lines using the default color.</para>
        <para>To hide the grid lines, set the <see cref="P:C1.C1Excel.XLSheet.ShowGridLines" /> property to false.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLSheet.TabColor">
      <summary>
            Gets or sets the color used to display the tab of this sheet.
            </summary>
      <remarks>
        <para>Set this property to <b>Color.Transparent</b> to display the sheet tab using the default color.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLSheet.PrintSettings">
      <summary>
            Gets or sets an <see cref="T:C1.C1Excel.XLPrintSettings" /> object that controls how the
            sheet is printed.
            </summary>
      <example>
            The code below creates a header for the sheet and sets the orientation to landscape:
            <code>
            XLPrintSettings pp = sheet.PrintSettings();
            pp.Landscape = true;
            pp.Header = "&amp;LLeft Header&amp;CCenter Header&amp;RRight Header";
            </code></example>
    </member>
    <member name="P:C1.C1Excel.XLSheet.Shapes">
      <summary>
            Gets a reference to the <see cref="T:C1.C1Excel.ShapeCollection" /> for the sheet.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.TotalsBelowData">
      <summary>
            Gets whether Excel should show the subtotals data when displaying the sheet.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.OutlinesBelow">
      <summary>
            Gets or sets a value indicating the vertical location of outline buttons.
            </summary>
      <remarks>
            If this property is set to true, outline buttons are located below the outline group,
            otherwise they are located above the outline group.
            <para>Th default value is <b>true</b></para>.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLSheet.OutlinesRight">
      <summary>
            Gets or sets a value indicating the horizontal location of outline buttons.
            </summary>
      <remarks>
            If this property is set to true, outline buttons are located to the right the outline group,
            otherwise they are located to the left of the outline group.
            <para>Th default value is <b>true</b></para>.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLSheet.MaxOutlineLevel">
      <summary>
            Gets the maximum subtotals outline level for rows or columns.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.RowMaxLevel">
      <summary>
            Gets maximum outline level for rows.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.ColumnMaxLevel">
      <summary>
            Gets maximum outline level for columns.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLSheet.ConditionalFormattings">
      <summary>
            Gets the collection of <see cref="T:C1.C1Excel.XLConditionalFormatting" /> objects for the current workbook.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLSheet.CopyFormula(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>
            Copies the formula from a source cell to a destination cell, adjusting relative references.
            </summary>
      <param name="rowFrom">The row index of a source cell with exist formula.</param>
      <param name="colFrom">The column index of a source cell with exist formula.</param>
      <param name="rowTo">The row index of a destination cell for copies the formula.</param>
      <param name="colTo">The column index of a destination cell for copies the formula.</param>
    </member>
    <member name="M:C1.C1Excel.XLSheet.Load(System.String,System.Boolean)">
      <summary>
            Loads the Excel worksheet from a file.
            </summary>
      <param name="fileName">Name of the file that contains the worksheet.</param>
      <param name="last">
        <b>True</b> to finish loading from the workbook; <b>False</b> to load data into the other worksheets.</param>
      <remarks>It was previously necessary to load the workbook from the stream without filling the worksheets.</remarks>
    </member>
    <member name="M:C1.C1Excel.XLSheet.Load(System.IO.Stream,System.Boolean)">
      <summary>
            Loads the Excel worksheet from a stream.
            </summary>
      <param name="stream">
        <see cref="T:System.IO.Stream" /> that contains the worksheet.</param>
      <param name="last">
        <b>True</b> to finish loading from the workbook; <b>False</b> to load data into the other worksheets.</param>
      <remarks>It was previously necessary to load the workbook from the stream without filling the worksheets.</remarks>
    </member>
    <member name="M:C1.C1Excel.XLSheet.LoadCsv(System.String)">
      <summary>
            Loads data from a file containing comma-separated values (.csv) into the current sheet.
            </summary>
      <param name="fileName">The name of a file containing data in .csv format.</param>
    </member>
    <member name="M:C1.C1Excel.XLSheet.LoadCsv(System.IO.Stream)">
      <summary>
            Loads data from a stream containing comma-separated values (.csv) into the current sheet.
            </summary>
      <param name="stream">A stream containing data in .csv format.</param>
    </member>
    <member name="M:C1.C1Excel.XLSheet.SaveCsv(System.String)">
      <summary>
            Saves data from the current sheet into a comma-separated values (.csv) formatted file.
            </summary>
      <param name="fileName">The target (.csv) file name.</param>
    </member>
    <member name="M:C1.C1Excel.XLSheet.SaveCsv(System.IO.Stream)">
      <summary>
            Saves data from the current sheet into a comma-separated values (.csv) formatted stream.
            </summary>
      <param name="stream">The target stream.</param>
    </member>
    <member name="M:C1.C1Excel.XLSheet.GetRangeToRepeat(System.Int32@,System.Int32@,System.Int32@,System.Int32@)">
      <summary>
            Returns a value indicating whether print titles are specified for this worksheet.
            (Print titles are rows repeated at top, and columns repeated at left of each page
            when the sheet is printed.)
            Output parameters indicate the indices of title rows and columns.
            </summary>
      <param name="rowFrom">OUT: index of the first title row (repeated at top).</param>
      <param name="rowTo">OUT: index of the last title row (repeated at top).</param>
      <param name="colFrom">OUT: index of the first title column (repeated at left).</param>
      <param name="colTo">OUT: index of the last title column (repeated at left).</param>
      <returns>True if title rows or columns are specified for this sheet, false otherwise.</returns>
    </member>
    <member name="M:C1.C1Excel.XLSheet.SetRangeToRepeat(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>
            Specifies the range of rows and columns to use as print titles for this worksheet.
            (Print titles are rows repeated at top, and columns repeated at left of each page
            when the sheet is printed.)
            </summary>
      <param name="rowFrom">Index of the first title row (repeated at top).</param>
      <param name="rowTo">Index of the last title row (repeated at top).</param>
      <param name="colFrom">Index of the first title column (repeated at left).</param>
      <param name="colTo">Index of the last title column (repeated at left).</param>
    </member>
    <member name="T:C1.C1Excel.XLAlignHorzEnum">
      <summary>
            Specifies how to align cell content horizontally within a cell.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLAlignHorzEnum.Undefined">
      <summary>Not specified (use default).</summary>
    </member>
    <member name="F:C1.C1Excel.XLAlignHorzEnum.General">
      <summary>Align strings to the left, numbers to the right.</summary>
    </member>
    <member name="F:C1.C1Excel.XLAlignHorzEnum.Left">
      <summary>Align to cell left.</summary>
    </member>
    <member name="F:C1.C1Excel.XLAlignHorzEnum.Center">
      <summary>Align to cell center.</summary>
    </member>
    <member name="F:C1.C1Excel.XLAlignHorzEnum.Right">
      <summary>Align to cell right.</summary>
    </member>
    <member name="F:C1.C1Excel.XLAlignHorzEnum.Fill">
      <summary>Fill cell, repeating content as necessary.</summary>
    </member>
    <member name="F:C1.C1Excel.XLAlignHorzEnum.Justify">
      <summary>Justify content horizontally to span the whole cell width.</summary>
    </member>
    <member name="F:C1.C1Excel.XLAlignHorzEnum.Selection">
      <summary>Align to selection center (may span multiple cells).</summary>
    </member>
    <member name="T:C1.C1Excel.XLAlignVertEnum">
      <summary>
            Specifies how to align cell content vertically within a cell.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLAlignVertEnum.Undefined">
      <summary>Not specified (use default).</summary>
    </member>
    <member name="F:C1.C1Excel.XLAlignVertEnum.Top">
      <summary>Align to cell top.</summary>
    </member>
    <member name="F:C1.C1Excel.XLAlignVertEnum.Center">
      <summary>Align to cell center.</summary>
    </member>
    <member name="F:C1.C1Excel.XLAlignVertEnum.Bottom">
      <summary>Align to cell bottom.</summary>
    </member>
    <member name="F:C1.C1Excel.XLAlignVertEnum.Justify">
      <summary>Justify content vertically to span the whole cell height.</summary>
    </member>
    <member name="T:C1.C1Excel.XLDiagonalFlags">
      <summary>
            Specifies the type of diagonal line to draw across the cells.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLDiagonalFlags.None">
      <summary>No diagonal.</summary>
    </member>
    <member name="F:C1.C1Excel.XLDiagonalFlags.Forward">
      <summary>Forward diagonal (\).</summary>
    </member>
    <member name="F:C1.C1Excel.XLDiagonalFlags.Backward">
      <summary>Backward diagonal (/).</summary>
    </member>
    <member name="T:C1.C1Excel.XLLineStyleEnum">
      <summary>
            Specifies the line style used for the cell borders.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLLineStyleEnum.None">
      <summary>No line.</summary>
    </member>
    <member name="F:C1.C1Excel.XLLineStyleEnum.Thin">
      <summary>Thin.</summary>
    </member>
    <member name="F:C1.C1Excel.XLLineStyleEnum.Medium">
      <summary>Medium.</summary>
    </member>
    <member name="F:C1.C1Excel.XLLineStyleEnum.Dashed">
      <summary>Dashed.</summary>
    </member>
    <member name="F:C1.C1Excel.XLLineStyleEnum.Dotted">
      <summary>Dotted.</summary>
    </member>
    <member name="F:C1.C1Excel.XLLineStyleEnum.Thick">
      <summary>Thick.</summary>
    </member>
    <member name="F:C1.C1Excel.XLLineStyleEnum.Double">
      <summary>Double.</summary>
    </member>
    <member name="F:C1.C1Excel.XLLineStyleEnum.Hair">
      <summary>Hair (one pixel).</summary>
    </member>
    <member name="F:C1.C1Excel.XLLineStyleEnum.MediumDashed">
      <summary>Medium dashed.</summary>
    </member>
    <member name="F:C1.C1Excel.XLLineStyleEnum.ThinDashDotted">
      <summary>Thin dash-dot.</summary>
    </member>
    <member name="F:C1.C1Excel.XLLineStyleEnum.MediumDashDotted">
      <summary>Medium dash-dot.</summary>
    </member>
    <member name="F:C1.C1Excel.XLLineStyleEnum.ThinDashDotDotted">
      <summary>Thin dash-dot-dot.</summary>
    </member>
    <member name="F:C1.C1Excel.XLLineStyleEnum.MediumDashDotDotted">
      <summary>Medium dash-dot-dot.</summary>
    </member>
    <member name="F:C1.C1Excel.XLLineStyleEnum.SlantedMediumDashDotted">
      <summary>Slanted dash-dot.</summary>
    </member>
    <member name="T:C1.C1Excel.XLPatternEnum">
      <summary>
            Specifies the pattern used to fill the cell background.
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.None">
      <summary>No pattern (transparent).</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.Solid">
      <summary>Solid background.</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.Gray50">
      <summary>50% dotted pattern.</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.Gray75">
      <summary>75% dotted pattern.</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.Gray25">
      <summary>25% dotted pattern.</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.HorizontalStripe">
      <summary>Horizontal stripe pattern.</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.VerticalStripe">
      <summary>Vertical stripe pattern.</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.ReverseDiagonalStripe">
      <summary>Reverse diagonal stripe pattern (\).</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.DiagonalStripe">
      <summary>Diagonal stripe pattern (/).</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.DiagonalCrosshatch">
      <summary>Diagonal crosshatch pattern.</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.ThickDiagonalCrosshatch">
      <summary>Thick diagonal crosshatch pattern.</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.ThinHorizontalStripe">
      <summary>Thin horizontal stripe pattern.</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.ThinVerticalStripe">
      <summary>Thin vertical stripe pattern.</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.ThinReverseDiagonalStripe">
      <summary>Thin reverse diagonal stripe pattern (\).</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.ThinDiagonalStripe">
      <summary>Thin diagonal stripe pattern (/).</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.ThinHorizontalCrosshatch">
      <summary>Thin horizontal crosshatch pattern (squares).</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.ThinDiagonalCrosshatch">
      <summary>Thin diagonal crosshatch pattern (diamonds).</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.Gray12">
      <summary>12% dotted pattern (less dots).</summary>
    </member>
    <member name="F:C1.C1Excel.XLPatternEnum.Gray06">
      <summary>6% dotted pattern (least dots).</summary>
    </member>
    <member name="T:C1.C1Excel.XLReadingDirection">
      <summary>
            Specifies the reading direction (East Asian versions of MS Excel).
            </summary>
    </member>
    <member name="F:C1.C1Excel.XLReadingDirection.Context">
      <summary>Using current context (by default).</summary>
    </member>
    <member name="F:C1.C1Excel.XLReadingDirection.LeftToRight">
      <summary>Left-to-right direction.</summary>
    </member>
    <member name="F:C1.C1Excel.XLReadingDirection.RightToLeft">
      <summary>Right-to-left direction.</summary>
    </member>
    <member name="T:C1.C1Excel.XLStyle">
      <summary>
            Contains style elements used to define the appearance of the cells.
            </summary>
      <remarks>
        <para>Each <see cref="T:C1.C1Excel.XLStyle" /> object may define one or more of the following 
            elements: font, format, background color, background pattern, foreground color, 
            alignment, text direction, and word wrapping.</para>
        <para>When displaying a cell, Excel combines the row, column, and cell styles 
            and merges the style elements defined in each one in order to determine how 
            the cell should be displayed. The precedence of the styles is: (1) cell, 
            (2) row, (3) column, (4) default style.</para>
        <para>Every <see cref="T:C1.C1Excel.XLStyle" /> belongs to a <see cref="T:C1.C1Excel.C1XLBook" />, and may 
            be assigned to one or more <see cref="T:C1.C1Excel.XLRow" />, <see cref="T:C1.C1Excel.XLColumn" />, and 
            <see cref="T:C1.C1Excel.XLCell" /> objects through their <see cref="P:C1.C1Excel.XLCell.Style" /> property.</para>
      </remarks>
    </member>
    <member name="M:C1.C1Excel.XLStyle.#ctor(C1.C1Excel.C1XLBook)">
      <summary>
            Creates a new instance of <see cref="T:C1.C1Excel.XLStyle" /> and adds it to the
            specified <see cref="T:C1.C1Excel.C1XLBook" />.
            </summary>
      <param name="book">Parent <see cref="T:C1.C1Excel.C1XLBook" />.</param>
    </member>
    <member name="M:C1.C1Excel.XLStyle.Clone">
      <summary>
            Creates a new <see cref="T:C1.C1Excel.XLStyle" /> object that is a copy of the current instance.
            </summary>
      <returns>A new <see cref="T:C1.C1Excel.XLStyle" /> object that is a copy of the current instance.</returns>
    </member>
    <member name="P:C1.C1Excel.XLStyle.Font">
      <summary>
            Gets or sets the font for this <see cref="T:C1.C1Excel.XLStyle" />.
            </summary>
      <remarks>
            Set the <see cref="P:C1.C1Excel.XLStyle.Font" /> property to null in order to suppress 
            this style element and use the default font instead. The default font is 
            determined by the book's <see cref="P:C1.C1Excel.C1XLBook.DefaultFont" /> property.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.ForeColor">
      <summary>
            Gets or sets the foreground color for this <see cref="T:C1.C1Excel.XLStyle" />.
            </summary>
      <remarks>
        <para>Set the <see cref="P:C1.C1Excel.XLStyle.ForeColor" /> property to <b>Color.Transparent</b> to
            suppress this style element and use the default foreground color instead (black).</para>
        <para>All colors on the <see cref="T:C1.C1Excel.C1XLBook" /> are mapped to a palette. This 
            means that if you assign colors to styles, save the book, and then load it back, 
            you probably won't get exactly the same colors used when the book was created. 
            You will get a fairly close approximation though.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.BackColor">
      <summary>
            Gets or sets the background color for this <see cref="T:C1.C1Excel.XLStyle" />.
            </summary>
      <remarks>
        <para>Set the <see cref="P:C1.C1Excel.XLStyle.BackColor" /> property to <b>Color.Transparent</b> to
            suppress this style element and use the default background color instead (white).</para>
        <para>All colors on the <see cref="T:C1.C1Excel.C1XLBook" /> are mapped to a palette. This 
            means that if you assign colors to styles, save the book, and then load it back, 
            you probably won't get exactly the same colors used when the book was created. 
            You will get a fairly close approximation though.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.BackPattern">
      <summary>
            Gets or sets the background pattern for this <see cref="T:C1.C1Excel.XLStyle" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLStyle.PatternColor">
      <summary>
            Gets or sets the color of the background pattern for this <see cref="T:C1.C1Excel.XLStyle" />.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLStyle.Format">
      <summary>
            Gets or sets the format associated with this <see cref="T:C1.C1Excel.XLStyle" />.
            </summary>
      <remarks>
        <para>Excel uses formats similar, but not identical to .NET. Refer
            to the Excel documentation for details on how to create format strings.</para>
        <para>You can use the <see cref="M:C1.C1Excel.XLStyle.FormatXLToDotNet(System.String)" /> and <see cref="M:C1.C1Excel.XLStyle.FormatDotNetToXL(System.String,System.Globalization.CultureInfo)" />
            methods to convert common Excel format strings to and from .NET format strings.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.AlignHorz">
      <summary>
            Gets or sets the horizontal alignment for this <see cref="T:C1.C1Excel.XLStyle" />.
            </summary>
      <remarks>
            Set the <see cref="P:C1.C1Excel.XLStyle.AlignHorz" /> property to <see cref="F:C1.C1Excel.XLAlignHorzEnum.Undefined" /> to suppress
            this style element and use the default horizontal alignment instead (<see cref="F:C1.C1Excel.XLAlignHorzEnum.General" />).
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.AlignVert">
      <summary>
            Gets or sets the vertical alignment for this <see cref="T:C1.C1Excel.XLStyle" />.
            </summary>
      <remarks>
            Set the <see cref="P:C1.C1Excel.XLStyle.AlignVert" /> property to <see cref="F:C1.C1Excel.XLAlignVertEnum.Undefined" /> to suppress
            this style element and use the default horizontal alignment instead (<see cref="F:C1.C1Excel.XLAlignVertEnum.Bottom" />).
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.ShrinkToFit">
      <summary>
            Gets or sets whether this <see cref="T:C1.C1Excel.XLStyle" /> causes cell contents to shrink to fit.
            </summary>
      <remarks>
            Cells that do shrink to fit if content more that cell width.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.WordWrap">
      <summary>
            Gets or sets whether this <see cref="T:C1.C1Excel.XLStyle" /> causes cell contents to wrap.
            </summary>
      <remarks>
            Cells that do not wrap will spill onto adjacent cells and will be kept on 
            a single line even if they contain line-break characters.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.Indent">
      <summary>
            Gets or sets the indent cell contents for this <see cref="T:C1.C1Excel.XLStyle" />.
            </summary>
      <remarks>
            This property should be set to values between 0 and 255 (between 0 and 15 for Excel 2003 compatible mode),
            indent calculate as value multiplied by the step (similar TAB value in the text) is equal 2,5 points (180 twips).
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.Direction">
      <summary>
            Gets or sets the context reading direction for this <see cref="T:C1.C1Excel.XLStyle" />.
            </summary>
      <remarks>
            This property used only for East Asian versions of MS Excel.
            </remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.Rotation">
      <summary>
            Gets or sets the rotation for this <see cref="T:C1.C1Excel.XLStyle" />, in degrees.
            </summary>
      <remarks>
        <para>This property should be set to values between 0 and 180, or 255, 
            as explained below:</para>
        <para>Zero means no rotation.</para>
        <para>1-90 means 1 to 90 degrees counter-clockwise (90 causes text to be 
            displayed in the vertical direction going up the cell).</para>
        <para>91-180 means 1 to 90 degrees clockwise (180 causes text to be 
            displayed in the vertical direction going down the cell).</para>
        <para>255 causes letters to be stacked top to bottom and not rotated.</para>
      </remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.Locked">
      <summary>
            Gets or sets whether the cell should be locked for editing when the <see cref="T:C1.C1Excel.XLSheet" />
            is protected.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLStyle.SetBorderStyle(C1.C1Excel.XLLineStyleEnum)">
      <summary>
            Sets the border style for this <see cref="T:C1.C1Excel.XLStyle" />.
            </summary>
      <param name="style">Line style used to draw the border.</param>
      <remarks>This method applies the setting to all four borders.</remarks>
    </member>
    <member name="M:C1.C1Excel.XLStyle.SetBorderColor(System.Drawing.Color)">
      <summary>
            Sets the border color for this <see cref="T:C1.C1Excel.XLStyle" />.
            </summary>
      <param name="color">Color used to draw the border.</param>
      <remarks>This method applies the setting to all four borders.</remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.BorderLeft">
      <summary>
            Gets or sets the line style used to draw the left border.
            </summary>
      <remarks>Use the <see cref="M:C1.C1Excel.XLStyle.SetBorderStyle(C1.C1Excel.XLLineStyleEnum)" /> method to set all borders at once.</remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.BorderRight">
      <summary>
            Gets or sets the line style used to draw the right border.
            </summary>
      <remarks>Use the <see cref="M:C1.C1Excel.XLStyle.SetBorderStyle(C1.C1Excel.XLLineStyleEnum)" /> method to set all borders at once.</remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.BorderTop">
      <summary>
            Gets or sets the line style used to draw the top border.
            </summary>
      <remarks>Use the <see cref="M:C1.C1Excel.XLStyle.SetBorderStyle(C1.C1Excel.XLLineStyleEnum)" /> method to set all borders at once.</remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.BorderBottom">
      <summary>
            Gets or sets the line style used to draw the bottom border.
            </summary>
      <remarks>Use the <see cref="M:C1.C1Excel.XLStyle.SetBorderStyle(C1.C1Excel.XLLineStyleEnum)" /> method to set all borders at once.</remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.BorderColorLeft">
      <summary>
            Gets or sets the color used to draw the left border.
            </summary>
      <remarks>Use the <see cref="M:C1.C1Excel.XLStyle.SetBorderColor(System.Drawing.Color)" /> method to set all borders at once.</remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.BorderColorRight">
      <summary>
            Gets or sets the color used to draw the right border.
            </summary>
      <remarks>Use the <see cref="M:C1.C1Excel.XLStyle.SetBorderColor(System.Drawing.Color)" /> method to set all borders at once.</remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.BorderColorTop">
      <summary>
            Gets or sets the color used to draw the top border.
            </summary>
      <remarks>Use the <see cref="M:C1.C1Excel.XLStyle.SetBorderColor(System.Drawing.Color)" /> method to set all borders at once.</remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.BorderColorBottom">
      <summary>
            Gets or sets the color used to draw the bottom border.
            </summary>
      <remarks>Use the <see cref="M:C1.C1Excel.XLStyle.SetBorderColor(System.Drawing.Color)" /> method to set all borders at once.</remarks>
    </member>
    <member name="P:C1.C1Excel.XLStyle.Diagonal">
      <summary>
            Gets or sets which diagonal lines to display (none, forward, backward).
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLStyle.DiagonalStyle">
      <summary>
            Gets or sets the line style used to draw the diagonal lines.
            </summary>
    </member>
    <member name="P:C1.C1Excel.XLStyle.DiagonalColor">
      <summary>
            Gets or sets the color used to draw the diagonal lines.
            </summary>
    </member>
    <member name="M:C1.C1Excel.XLStyle.FormatDotNetToXL(System.String,System.Type)">
      <summary>
            Converts a .NET-style format string into an Excel format string.
            </summary>
      <param name="fmt">.NET-style format to convert.</param>
      <param name="dataType">Data type to be formatted.</param>
      <returns>An Excel-style format string.</returns>
    </member>
    <member name="M:C1.C1Excel.XLStyle.FormatDotNetToXL(System.String,System.Type,System.Globalization.CultureInfo)">
      <summary>
            Converts a .NET-style format string into an Excel format string.
            </summary>
      <param name="fmt">.NET-style format to convert.</param>
      <param name="dataType">Data type to be formatted.</param>
      <param name="culture">The <see cref="T:System.Globalization.CultureInfo" /> object.</param>
      <returns>An Excel-style format string.</returns>
    </member>
    <member name="M:C1.C1Excel.XLStyle.FormatDotNetToXL(System.String)">
      <summary>
            Converts a .NET-style format string into an Excel format string.
            </summary>
      <param name="fmt">.NET-style format to convert.</param>
      <returns>An Excel-style format string.</returns>
    </member>
    <member name="M:C1.C1Excel.XLStyle.FormatDotNetToXL(System.String,System.Globalization.CultureInfo)">
      <summary>
            Converts a .NET-style format string into an Excel format string.
            </summary>
      <param name="fmt">.NET-style format to convert.</param>
      <param name="culture">The <see cref="T:System.Globalization.CultureInfo" /> object.</param>
      <returns>An Excel-style format string.</returns>
    </member>
    <member name="M:C1.C1Excel.XLStyle.FormatXLToDotNet(System.String)">
      <summary>
            Converts an Excel-style format string into a .NET-style format string.
            </summary>
      <param name="fmt">Excel-style format to convert.</param>
      <returns>A .NET-style format string.</returns>
    </member>
    <member name="M:C1.C1Excel.XLStyle.FormatXLToDotNet(System.String,System.Globalization.CultureInfo)">
      <summary>
            Converts an Excel-style format string into a .NET-style format string.
            </summary>
      <param name="fmt">Excel-style format to convert.</param>
      <param name="culture">The <see cref="T:System.Globalization.CultureInfo" /> object.</param>
      <returns>A .NET-style format string.</returns>
    </member>
    <member name="M:C1.C1Excel.XLStyle.Equals(System.Object)">
      <summary>
            Determines whether two <see cref="T:C1.C1Excel.XLStyle" /> objects are equivalent.
            </summary>
      <param name="obj">
        <see cref="T:C1.C1Excel.XLStyle" /> object to compare to the current instance.</param>
      <returns>True if the styles are equivalent, False otherwise.</returns>
    </member>
    <member name="M:C1.C1Excel.XLStyle.GetHashCode">
      <summary>
            Serves as a hash function suitable for use in hashing algorithms and 
            data structures like a hash table.
            </summary>
      <returns>A hash code for the current <see cref="T:C1.C1Excel.XLStyle" />.</returns>
    </member>
    <member name="T:C1.C1Excel.Design.DesignStrings">
      <summary>
            Static class containing UI strings used by the designer.
            </summary>
    </member>
  </members>
</doc>