﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fReceptionFichierTerminal
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.bValiderReception = New C1.Win.C1Input.C1Button()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.bParcourir = New C1.Win.C1Input.C1Button()
        Me.tCheminFichier = New C1.Win.C1Input.C1TextBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.OpenFileDialog = New System.Windows.Forms.OpenFileDialog()
        Me.GroupBox4.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        CType(Me.tCheminFichier, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.bAnnuler)
        Me.GroupBox4.Controls.Add(Me.bValiderReception)
        Me.GroupBox4.Location = New System.Drawing.Point(12, 102)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(537, 62)
        Me.GroupBox4.TabIndex = 11
        Me.GroupBox4.TabStop = False
        Me.GroupBox4.Text = "Valider la réception du fichier"
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Location = New System.Drawing.Point(223, 31)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(102, 25)
        Me.bAnnuler.TabIndex = 9
        Me.bAnnuler.Text = "&Annuler"
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bValiderReception
        '
        Me.bValiderReception.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bValiderReception.Location = New System.Drawing.Point(11, 31)
        Me.bValiderReception.Name = "bValiderReception"
        Me.bValiderReception.Size = New System.Drawing.Size(206, 25)
        Me.bValiderReception.TabIndex = 8
        Me.bValiderReception.Text = "&Valider la réception"
        Me.bValiderReception.UseVisualStyleBackColor = True
        Me.bValiderReception.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.bParcourir)
        Me.GroupBox3.Controls.Add(Me.tCheminFichier)
        Me.GroupBox3.Controls.Add(Me.Label4)
        Me.GroupBox3.Location = New System.Drawing.Point(12, 12)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(537, 84)
        Me.GroupBox3.TabIndex = 10
        Me.GroupBox3.TabStop = False
        Me.GroupBox3.Text = "Sélectionner le fichier"
        '
        'bParcourir
        '
        Me.bParcourir.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bParcourir.Location = New System.Drawing.Point(410, 50)
        Me.bParcourir.Name = "bParcourir"
        Me.bParcourir.Size = New System.Drawing.Size(105, 25)
        Me.bParcourir.TabIndex = 7
        Me.bParcourir.Text = "&Parcourir"
        Me.bParcourir.UseVisualStyleBackColor = True
        Me.bParcourir.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tCheminFichier
        '
        Me.tCheminFichier.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCheminFichier.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCheminFichier.Location = New System.Drawing.Point(86, 26)
        Me.tCheminFichier.Name = "tCheminFichier"
        Me.tCheminFichier.Size = New System.Drawing.Size(429, 18)
        Me.tCheminFichier.TabIndex = 2
        Me.tCheminFichier.Tag = Nothing
        Me.tCheminFichier.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCheminFichier.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Location = New System.Drawing.Point(16, 28)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(68, 13)
        Me.Label4.TabIndex = 0
        Me.Label4.Text = "Fichier reçu :"
        '
        'fReceptionFichierTerminal
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.ClientSize = New System.Drawing.Size(561, 177)
        Me.Controls.Add(Me.GroupBox4)
        Me.Controls.Add(Me.GroupBox3)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fReceptionFichierTerminal"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Reception du Fichier Terminal"
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        CType(Me.tCheminFichier, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bValiderReception As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents bParcourir As C1.Win.C1Input.C1Button
    Friend WithEvents tCheminFichier As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents OpenFileDialog As System.Windows.Forms.OpenFileDialog
End Class
