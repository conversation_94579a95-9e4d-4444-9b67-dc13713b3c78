<?xml version="1.0"?>
<doc>
  <assembly>
    <name>C1.Win.C1List.2</name>
  </assembly>
  <members>
    <member name="T:C1.Data.IC1GetDataSource">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Data.IC1GetDataSource.GetDataSource">
      <summary>
            Gets the data source.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Data.IC1GetDataSource.GetDataView">
      <summary>
            Gets the data view.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Data.IC1DataSource">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Data.IC1DataSource.SetBoundControl(System.Object,System.String)">
      <summary>
            Sets the bound control.
            </summary>
      <param name="boundControl">The bound control.</param>
      <param name="name">The name.</param>
    </member>
    <member name="M:C1.Data.IC1DataSource.GetVirtualSegmentSize">
      <summary>
            Gets the size of the virtual segment.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Data.IC1DataSource.SetVisibleRowCount(System.Int32,System.String)">
      <summary>
            Sets the visible row count.
            </summary>
      <param name="value">The value.</param>
      <param name="name">The name.</param>
    </member>
    <member name="M:C1.Data.IC1DataSource.BoundControlScrolled">
      <summary>
            Bounds the control scrolled.
            </summary>
    </member>
    <member name="T:C1.Data.IC1ComplexBoundControl">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Data.IC1ComplexBoundControl.GetFirstVisibleRow">
      <summary>
            Gets the first visible row.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Data.IC1ComplexBoundControl.GetVisibleRowCount">
      <summary>
            Gets the visible row count.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Data.IC1ComplexBoundControl.ScrollToRow(System.Int32,System.Int32)">
      <summary>
            Scrolls to row.
            </summary>
      <param name="firstVisibleRow">The first visible row.</param>
      <param name="rowCount">The row count.</param>
    </member>
    <member name="T:C1.Data.IC1EditableObject">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Data.IC1EditableObject.RequestModification">
      <summary>
            Requests the modification.
            </summary>
    </member>
    <member name="T:C1.Data.IC1DataView">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Data.IC1DataView.SetRowFilter(System.String)">
      <summary>
            Sets the row filter.
            </summary>
      <param name="value">The value.</param>
    </member>
    <member name="M:C1.Data.IC1DataView.SetSort(System.String)">
      <summary>
            Sets the sort.
            </summary>
      <param name="value">The value.</param>
    </member>
    <member name="M:C1.Data.IC1DataView.Refresh">
      <summary>
            Refreshes this instance.
            </summary>
    </member>
    <member name="T:C1.Data.LateBindingIC1DataSource">
      <summary>
            Interfaces used by bound controls and other C1Data consumers:
            Dynamic, late bound access to the interfaces, via reflection
            </summary>
    </member>
    <member name="M:C1.Data.LateBindingIC1DataSource.GetDataSource(System.Object)">
      <summary>
            Gets the data source.
            </summary>
      <param name="obj">The obj.</param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Data.LateBindingIC1ComplexBoundControl">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Data.LateBindingIC1ComplexBoundControl.NewLateBindingIC1ComplexBoundControl(System.Object)">
      <summary>
            News the late binding I c1 complex bound control.
            </summary>
      <param name="obj">The obj.</param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Data.LateBindingIC1DataView">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Data.LateBindingIC1DataView.GetDataView(System.Object)">
      <summary>
            Gets the data view.
            </summary>
      <param name="obj">The obj.</param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.C1PreviewClient.GraphicsHolder">
      <summary>
            Holds a Graphics object together with (printer) device or bitmap that was used
            to create it, if any. The point is to dispose the device or bitmap together
            with the graphics when it is no longer needed (i.e. to avoid resource leaks).
            </summary>
    </member>
    <member name="F:C1.C1PreviewClient.GraphicsHolder.Empty">
      <summary>
            An empty graphics holder instance.
            </summary>
    </member>
    <member name="M:C1.C1PreviewClient.GraphicsHolder.FromDC(System.IntPtr)">
      <summary>
            Creates a graphics holder from a device context.
            When the holder is disposed, both graphics and the device context are released (DeleteDC is called on the device context).
            </summary>
      <param name="dc">The device context from which graphics is created.</param>
      <returns>The new instance of the graphics holder.</returns>
    </member>
    <member name="M:C1.C1PreviewClient.GraphicsHolder.FromGraphics(System.Drawing.Graphics)">
      <summary>
            Creates a graphics holder from a graphics instance.
            When the holder is disposed, the graphics is left alone (i.e. NOT disposed).
            </summary>
      <param name="g">The graphics to store in the new holder.</param>
      <returns>The new instance of the graphics holder.</returns>
    </member>
    <member name="M:C1.C1PreviewClient.GraphicsHolder.FromScreen">
      <summary>
            Creates a graphics holder from screen. If that fails (e.g. on Azure), the graphics is created from a bitmap.
            When the holder is disposed, both graphics and the bitmap are released.
            </summary>
      <returns>The new instance of the graphics holder.</returns>
    </member>
    <member name="M:C1.C1PreviewClient.GraphicsHolder.FromBitmap">
      <summary>
            Creates a graphics holder from a bitmap. The graphics and the bitmap are released when the holder is disposed.
            This should never fail.
            </summary>
      <returns>The new instance of the graphics holder.</returns>
    </member>
    <member name="M:C1.C1PreviewClient.GraphicsHolder.Dispose">
      <summary>
            Disposes the current graphics holder.
            </summary>
    </member>
    <member name="P:C1.C1PreviewClient.GraphicsHolder.Graphics">
      <summary>
            Gets the graphics object held by the current instance.
            </summary>
    </member>
    <member name="P:C1.C1PreviewClient.GraphicsHolder.DpiX">
      <summary>
            Gets the horizontal resolution of graphics held by the current instance.
            </summary>
    </member>
    <member name="P:C1.C1PreviewClient.GraphicsHolder.DpiY">
      <summary>
            Gets the vertical resolution of graphics held by the current instance.
            </summary>
    </member>
    <member name="T:C1.C1PreviewClient.Serialization.ZipStructureException">
      <summary>
            Represents an exception that occurred during C1DX or C1MDX serialization/deserialization
            if the underlying zip stream has invalid format.
            </summary>
    </member>
    <member name="T:C1.C1PreviewClient.Serialization.SerializeExceptionBase">
      <summary>
            Abstract base class for <see cref="T:C1.C1PreviewClient.Serialization.SerializeException" /> and <see cref="T:C1.C1PreviewClient.Serialization.DeserializeException" />
            classes.
            </summary>
    </member>
    <member name="P:C1.C1PreviewClient.Serialization.SerializeExceptionBase.Log">
      <summary>
            Gets the list of strings describing the actions that lead to the exception that occurred
            during serialization or deserialization.
            </summary>
    </member>
    <member name="T:C1.C1PreviewClient.Serialization.SerializeException">
      <summary>
            Represents an exception that occurred during serialization.
            </summary>
    </member>
    <member name="T:C1.C1PreviewClient.Serialization.DeserializeException">
      <summary>
            Represents an exception that occurred during deserialization.
            </summary>
    </member>
    <member name="P:C1.C1PreviewClient.Serialization.DeserializeException.LineNumber">
      <summary>
            Gets the line number associated with the exception.
            </summary>
    </member>
    <member name="P:C1.C1PreviewClient.Serialization.DeserializeException.LinePosition">
      <summary>
            Gets the position in line associated with the exception.
            </summary>
    </member>
    <member name="T:C1.Util.CodeRange">
      <summary>
            Represents range of character codes.
            </summary>
    </member>
    <member name="M:C1.Util.CodeRange.#ctor(System.Char)">
      <summary>
      </summary>
      <param name="ch">Adding character.</param>
    </member>
    <member name="M:C1.Util.CodeRange.#ctor(System.Char,System.Char)">
      <summary>
      </summary>
      <param name="first">
      </param>
      <param name="last">
      </param>
    </member>
    <member name="M:C1.Util.CodeRange.Clone">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Util.CodeRange.Contains(C1.Util.CodeRange)">
      <summary>
      </summary>
      <param name="cr">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Util.CodeRange.First">
      <summary>The begin code of character.</summary>
    </member>
    <member name="P:C1.Util.CodeRange.Last">
      <summary>The end code of character.</summary>
    </member>
    <member name="T:C1.Util.CodeRanges">
      <summary>
            Represents collection of unique <see cref="T:C1.Util.CodeRange" /> objects.
            All CodeRange objects within collection not intersect and sorted
            by First field.
            </summary>
    </member>
    <member name="M:C1.Util.CodeRanges.AddNoCheck(C1.Util.CodeRange)">
      <summary>
            Adds <see cref="T:C1.Util.CodeRange" /> object to current without any check, typically used during deserialization.
            </summary>
      <param name="cr">
        <see cref="T:C1.Util.CodeRange" /> to add.</param>
    </member>
    <member name="M:C1.Util.CodeRanges.GetGlyphs">
      <summary>
            Gets the array of chars contained in this list of code ranges.
            </summary>
      <returns>Array of char</returns>
    </member>
    <member name="M:C1.Util.CodeRanges.ContainsAllChars">
      <summary>
            Gets a value indicating whether the <see cref="T:C1.Util.CodeRanges" /> collection
            contains all characters from 0x0000 to 0xFFFF.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Design.UITypeEditorStrings">
      <summary>
            Static class containing UI strings used by the designer.
            </summary>
    </member>
    <member name="T:C1.Util.UIStringsItemEventHandler">
      <summary>
            Represents a handler for an <see cref="T:C1.Util.UIStrings" /> item related event.
            </summary>
    </member>
    <member name="T:C1.Util.UIStringsItemEventArgs">
      <summary>
            Provides data for an <see cref="T:C1.Util.UIStrings" /> item related event.
            </summary>
    </member>
    <member name="P:C1.Util.UIStringsItemEventArgs.Key">
      <summary>
            Gets key of the item being added or changed.
            </summary>
      <value>The key.</value>
    </member>
    <member name="P:C1.Util.UIStringsItemEventArgs.Value">
      <summary>
            Gets the string value.
            </summary>
      <value>The value.</value>
    </member>
    <member name="P:C1.Util.UIStringsItemEventArgs.IsDefault">
      <summary>
            Gets a value indicating whether this instance is default.
            </summary>
      <value>
        <c>true</c> if this instance is default; otherwise, <c>false</c>.
            </value>
    </member>
    <member name="P:C1.Util.UIStringsItemEventArgs.Description">
      <summary>
            Gets the description.
            </summary>
      <value>The description.</value>
    </member>
    <member name="T:C1.Util.UIStrings">
      <summary>
            Represents a collection of end user visible UI strings.
            </summary>
    </member>
    <member name="M:C1.Util.UIStrings.Add(System.Object,System.Int32,System.String,System.String)">
      <summary>
            Adds a string to the collection, specifying the ordinal.
            </summary>
      <param name="key">The key of the string.</param>
      <param name="ordinal">The ordinal of the string.</param>
      <param name="value">The string.</param>
      <param name="description">The description of the string.</param>
    </member>
    <member name="M:C1.Util.UIStrings.Add(System.Object,System.String,System.String)">
      <summary>
            Adds a string to the collection in alphabetical order.
            </summary>
      <param name="key">The key of the string.</param>
      <param name="value">The string.</param>
      <param name="description">The description of the string.</param>
    </member>
    <member name="M:C1.Util.UIStrings.AddInOrder(System.Object,System.String,System.String)">
      <summary>
            Adds a string to the collection, preserving the order.
            </summary>
      <param name="key">The key of the string.</param>
      <param name="value">The string.</param>
      <param name="description">The description of the string.</param>
    </member>
    <member name="M:C1.Util.UIStrings.Reset">
      <summary>
            Sets all strings in collection to their default values.
            </summary>
    </member>
    <member name="M:C1.Util.UIStrings.ShouldSerialize">
      <summary>
            Indicates whether any of the strings in the current collection
            have non-default values.
            </summary>
      <returns>
        <c>true</c> if any of the strings have non-default values, <c>false</c> otherwise.</returns>
    </member>
    <member name="M:C1.Util.UIStrings.IsDefault(System.Object)">
      <summary>
            Tests whether a string in the collection has default value.
            </summary>
      <param name="key">The key of the string to test.</param>
      <returns>
        <c>true</c> if the string has default value, <c>false</c> otherwise.</returns>
    </member>
    <member name="M:C1.Util.UIStrings.GetDescription(System.Object)">
      <summary>
            Returns the description of a string.
            </summary>
      <param name="key">The key of the string to get the description of.</param>
      <returns>The string's description</returns>
    </member>
    <member name="M:C1.Util.UIStrings.Reset(System.Object)">
      <summary>
            Resets a string to its default value.
            </summary>
      <param name="key">The key of the string to reset.</param>
    </member>
    <member name="M:C1.Util.UIStrings.GetKeyAt(System.Int32)">
      <summary>
            Returns the key of an item with the specified index.
            </summary>
      <param name="index">The item index.</param>
      <returns>The item's key.</returns>
    </member>
    <member name="M:C1.Util.UIStrings.GetValueAt(System.Int32)">
      <summary>
            Gets the string by its index.
            </summary>
      <param name="index">The string index.</param>
      <returns>The string.</returns>
    </member>
    <member name="M:C1.Util.UIStrings.SetValueAt(System.Int32,System.String)">
      <summary>
            Sets the value of a string with the specified index.
            </summary>
      <param name="index">The string index.</param>
      <param name="value">The new string value.</param>
    </member>
    <member name="M:C1.Util.UIStrings.OnItemAdded(C1.Util.UIStringsItemEventArgs)">
      <summary>
            Fires the <see cref="E:C1.Util.UIStrings.ItemAdded" /> event.
            </summary>
      <param name="e">The event data.</param>
    </member>
    <member name="M:C1.Util.UIStrings.OnItemChanged(C1.Util.UIStringsItemEventArgs)">
      <summary>
            Fires the <see cref="E:C1.Util.UIStrings.ItemChanged" /> event.
            </summary>
      <param name="e">The event data.</param>
    </member>
    <member name="M:C1.Util.UIStrings.OnCollectionChanged(System.EventArgs)">
      <summary>
            Fires the <see cref="E:C1.Util.UIStrings.CollectionChanged" /> event.
            </summary>
      <param name="e">The event data.</param>
    </member>
    <member name="P:C1.Util.UIStrings.Count">
      <summary>
            Gets the number of elements contained in the collection.
            </summary>
    </member>
    <member name="E:C1.Util.UIStrings.ItemAdded">
      <summary>
            Occurs when a new item is added to the collection.
            </summary>
    </member>
    <member name="E:C1.Util.UIStrings.ItemChanged">
      <summary>
            Occurs when an item in the collection is changed.
            </summary>
    </member>
    <member name="E:C1.Util.UIStrings.CollectionChanged">
      <summary>
            Occurs when the collection has been changed.
            </summary>
    </member>
    <member name="T:C1.Util.UIStrings.TypeConverter">
      <summary>
            Provides type conversion for the <see cref="T:C1.Util.UIStrings" /> type.
            </summary>
    </member>
    <member name="M:C1.Util.UIStrings.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>
            For internal use.
            </summary>
      <param name="context">
      </param>
      <param name="culture">
      </param>
      <param name="value">
      </param>
      <param name="type">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Util.UIStrings.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])">
      <summary>
            For internal use.
            </summary>
      <param name="context">
      </param>
      <param name="value">
      </param>
      <param name="attrFilter">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Util.UIStrings.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)">
      <summary>
            For internal use.
            </summary>
      <param name="context">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1List.C1Combo">
      <summary>
            The C1Combo control is used as a multicolumn drop-down list box. 
            </summary>
      <remarks>
        <para>
            C1Combo is independent of C1List, so it can be used alone or in conjunction with C1List. 
            You can place a C1Combo control on a Visual Basic form at design time as you would a C1List control. 
            </para>
        <para>
            The C1Combo control also supports incremental search.
            </para>
      </remarks>
    </member>
    <member name="T:C1.Win.C1List.ComboBoxContainer">
      <summary>
            Summary description for ComboBoxContainer.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.#ctor">
      <summary>
            Initialize a new instance of ComboBoxContainer class.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.#ctor(System.Windows.Forms.Control,System.Windows.Forms.Control)">
      <summary>
            Initialize a new instance of ComboBoxContainer class.
            </summary>
      <param name="contentCtrl">The editor control.</param>
      <param name="dropDownCtrl">The dropdown control.</param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.Dispose(System.Boolean)">
      <summary>
      </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.ProcessKeyPreview(System.Windows.Forms.Message@)">
      <summary>
      </summary>
      <param name="m">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.PreProcessMessage(System.Windows.Forms.Message@)">
      <summary>
      </summary>
      <param name="m">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.OnOpen(System.EventArgs)">
      <summary>
            Raises the Open event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.OnBeforeOpen(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raises the BeforeOpen event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.OnClose(System.EventArgs)">
      <summary>
            Raises the Close event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.OnBorderStyleChanged(System.EventArgs)">
      <summary>
            Raises the BorderStyleChanged event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.OnComboBoxStyleChanged(System.EventArgs)">
      <summary>
            Raises the CombBoxStyleChanged event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.OnHandleCreated(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.OnLayout(System.Windows.Forms.LayoutEventArgs)">
      <summary>
      </summary>
      <param name="levent">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.ProcessDialogKey(System.Windows.Forms.Keys)">
      <summary>
      </summary>
      <param name="keyData">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.IsInputKey(System.Windows.Forms.Keys)">
      <summary>
      </summary>
      <param name="keyData">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.IsInputChar(System.Char)">
      <summary>
      </summary>
      <param name="charCode">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.OnMouseEnter(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.OnMouseLeave(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.GetPopupLocation">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.PerformDropDown">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.PerformCloseUp">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.SetBoundsCore(System.Int32,System.Int32,System.Int32,System.Int32,System.Windows.Forms.BoundsSpecified)">
      <summary>
      </summary>
      <param name="x">
      </param>
      <param name="y">
      </param>
      <param name="width">
      </param>
      <param name="height">
      </param>
      <param name="specified">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ComboBoxContainer.WndProc(System.Windows.Forms.Message@)">
      <summary>
      </summary>
      <param name="m">
      </param>
    </member>
    <member name="E:C1.Win.C1List.ComboBoxContainer.BeforeOpen">
      <summary>
            Fires before the list is dropped down.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ComboBoxContainer.Open">
      <summary>
            Fires when the user opens the list portion of the control by clicking the dropdown button or by pressing ALT + DOWN ARROW while the control is closed.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ComboBoxContainer.Close">
      <summary>
            Fires when the user closes the list portion of the control by clicking the dropdown button or by pressing ALT + Down Arrow while the control is open.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ComboBoxContainer.ComboStyleChanged">
      <summary>
            Fires when the user changes the ComboStyle property.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ComboBoxContainer.BorderStyleChanged">
      <summary>
            Fires when BorderStyle property is changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.VisualStyle">
      <summary>
            Gets or sets a value that determines the overall appearance of the control.
            </summary>
      <remarks>
        <para>This property allows you to quickly customize the appearance of the control so
            it matches the appearance of your application.</para>
        <para>The settings available include <b>System</b>, various Microsoft Office color 
            schemes, and <b>Custom</b>, which relies on the controls standard styles and appearance 
            properties.</para>
      </remarks>
    </member>
    <member name="E:C1.Win.C1List.ComboBoxContainer.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property changes.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.ReadOnly">
      <summary>
            Controls whether the text in the control can be changed or not.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.AutoSize">
      <summary>
            Determines whether or not the combo height can be resized.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.RightToLeft">
      <summary>
            When True, applies right to left text functionality.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.BorderStyle">
      <summary>
            Returns or sets the border style for the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.ButtonWidth">
      <summary>
            Gets the width for the combo button.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.ComboStyle">
      <summary>
            Returns or sets a value indicating the display type and behavior of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.ContextMenu">
      <summary>
            Gets or sets the shortcut menu associated with the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.DroppedDown">
      <summary>
            Returns or sets whether the drop-down window is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.Focused">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.FlatStyle">
      <summary>
            Controls the 3D look of the combo box.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.GapHeight">
      <summary>
            Returns or sets the length between the edit portion and the dropdown portion of the combo box.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.ContainsFocus">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.ContentHeight">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.DropDownHeight">
      <summary>
            Returns or sets the height of the dropped down.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.DropDownWidth">
      <summary>
            Returns or sets the width of the dropped down.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.DropdownPosition">
      <summary>
            Returns or sets the dropdown position.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ComboBoxContainer.ToolTip">
      <summary>
            Gets or sets the tooltip text.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.#ctor">
      <summary>
            Initialize a new instance of C1Combo control.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.Dispose(System.Boolean)">
      <summary>
            Releases the unmanaged resources used by the object and optionally releases the managed resources.
            </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.CreateAccessibilityInstance">
      <summary>
            Creates a new accessibility object for the control.
            </summary>
      <returns>A new <see cref="T:System.Windows.Forms.AccessibleObject" /> for the control.</returns>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.LoadLayout(System.String)">
      <summary>
            Loads a saved layout from the given file.
            </summary>
      <param name="filename">
            The file containing a saved layout.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.LoadLayout(System.IO.Stream)">
      <summary>
            Loads a saved layout from the given stream.
            </summary>
      <param name="stream">
            The Stream containing a saved layout.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.SaveLayout(System.IO.Stream)">
      <summary>
            Saves the grid's layout.
            </summary>
      <param name="stream">
            The Stream to contain the grid layout.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.SaveLayout(System.String,System.Boolean)">
      <summary>
            Saves the grid's layout.
            </summary>
      <param name="filename">
            File to contain the grid layout.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.SaveLayout(System.IO.Stream,System.Boolean)">
      <summary>
            Saves the grid's layout.
            </summary>
      <param name="stream">
            The Stream to contain the grid layout.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.BeginInit">
      <summary>
            Signals the object that initialization is starting.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.EndInit">
      <summary>
            Signals the object that initialization is complete.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ShouldSerializeCaptionHeight">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ShouldSerializeColumnCaptionHeight">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ShouldSerializeColumnFooterHeight">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ShouldSerializeItemHeight">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ResetVisualStyle">
      <summary>
            Resets <see cref="P:C1.Win.C1List.C1Combo.VisualStyle" /> to the default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ShouldSerializeVisualStyle">
      <summary>
            Tests whether <see cref="P:C1.Win.C1List.C1Combo.VisualStyle" /> should be serialized.
            </summary>
      <returns>True if <see cref="P:C1.Win.C1List.C1Combo.VisualStyle" /> should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnChange(System.EventArgs)">
      <summary>
            Raises the Change event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnLeftColChange(C1.Win.C1List.SplitEventArgs)">
      <summary>
            Raises the LeftColChange event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnColFootClick(C1.Win.C1List.ColEventArgs)">
      <summary>
            Raises the ColFootClick event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnColHeadClick(C1.Win.C1List.ColEventArgs)">
      <summary>
            Raises the ColHeadClick event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnAfterSort(C1.Win.C1List.FilterEventArgs)">
      <summary>
            Raises the AfterSort event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnColMove(C1.Win.C1List.ColMoveEventArgs)">
      <summary>
            Raises the ColMove event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnColResize(C1.Win.C1List.ColResizeEventArgs)">
      <summary>
            Raises the ColResize event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnComboBoxStyleChanged(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnComboBoxStyleChange(System.EventArgs)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnDataSourceChanged(System.EventArgs)">
      <summary>
            Raises the DataSourceChanged event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnError(C1.Win.C1List.ErrorEventArgs)">
      <summary>
            Raises the Error event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnDisplayMemberChanged(System.EventArgs)">
      <summary>
            Raises the DisplayMemberChanged event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnFetchRowStyle(C1.Win.C1List.FetchRowStyleEventArgs)">
      <summary>
            Raises the FetchRowStyle event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnFetchCellStyle(C1.Win.C1List.FetchCellStyleEventArgs)">
      <summary>
            Raises the FetchCellStyle event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnFetchCellTips(C1.Win.C1List.FetchCellTipsEventArgs)">
      <summary>
            Raises the FetchCellTips event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnFetchScrollTips(C1.Win.C1List.FetchScrollTipsEventArgs)">
      <summary>
            Raises the FetchScrollTips event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnGotFocus(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnLostFocus(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnMouseWheel(System.Windows.Forms.MouseEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnFormatText(C1.Win.C1List.FormatTextEventArgs)">
      <summary>
            Raises the FormatText event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnItemChanged(System.EventArgs)">
      <summary>
            Raises the ItemChanged event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnRowChange(System.EventArgs)">
      <summary>
            Raises the RowChange event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnRowResize(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raises the RowSize event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnScroll(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raises the Scroll event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnDrawItem(C1.Win.C1List.OwnerDrawCellEventArgs)">
      <summary>
            Raises the DrawItem event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnDragDrop(System.Windows.Forms.DragEventArgs)">
      <summary>
      </summary>
      <param name="drgevent">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnDragEnter(System.Windows.Forms.DragEventArgs)">
      <summary>
      </summary>
      <param name="drgevent">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnDragOver(System.Windows.Forms.DragEventArgs)">
      <summary>
      </summary>
      <param name="drgevent">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnSelChange(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raises the SelChange event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnSelectionChangeCommitted(System.EventArgs)">
      <summary>
            Raises the SelectionChangeCommitted event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnSelectedValueChanged(System.EventArgs)">
      <summary>
            Raises the SelectedValueChanged event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnTopIndexChange(C1.Win.C1List.SplitEventArgs)">
      <summary>
            Raises the TopIndexChange event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnUnboundColumnFetch(C1.Win.C1List.UnboundColumnFetchEventArgs)">
      <summary>
            Raises the UnboundColumnFetch
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnNotInList(C1.Win.C1List.NotInListEventArgs)">
      <summary>
            Raises the NotInList event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnMismatch(C1.Win.C1List.MismatchEventArgs)">
      <summary>
            Raises the Mismatch event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnValueMemberChanged(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OnKeyPress(System.Windows.Forms.KeyPressEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.AddCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style)">
      <summary>
            Controls the font and color of cells within a list, column, or split according to value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.AddItem(System.String)">
      <summary>
            Add a new item to the end in the AddItem mode.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.AddItemTitles(System.String)">
      <summary>
            Sets the column titles in the AddItem mode.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.AddItemF(System.String)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.AddItemEnd">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.SuspendBinding">
      <summary>
            Temporary suspension of data binding notifications from the datasource.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ResumeBinding">
      <summary>
            Resumes data binding notification from the datasource.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.AddRegexCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style,System.String)">
      <summary>
            Controls the font and color of cells within a list, column, or split according to their contents.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ClearRegexCellStyle(C1.Win.C1List.CellStyleFlag)">
      <summary>
            Removes a cell condition established with a previous call to the <see cref="M:C1.Win.C1List.C1Combo.AddRegexCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style,System.String)" /> method.
            </summary>
      <param name="condition">
            Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> enumerations.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ClearRegexCellStyle(C1.Win.C1List.CellStyleFlag,System.String)">
      <summary>
            Removes a cell condition established with a previous call to the <see cref="M:C1.Win.C1List.C1Combo.AddRegexCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style,System.String)" /> method.
            </summary>
      <param name="condition">
            Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> enumerations.
            </param>
      <param name="regex">
            A regular expression string.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.CaptureImage">
      <summary>
            Returns an image of the drop down list control.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.CellContaining(System.Int32,System.Int32,System.Int32@,System.Int32@)">
      <summary>
            Returns the cell position for a set of coordinates.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ColContaining(System.Int32)">
      <summary>
            Returns the column index containing the specified coordinate.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ClearCellStyle(C1.Win.C1List.CellStyleFlag)">
      <summary>
            Removes a cell condition established with a previous call to the AddCellStyle method.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ClearFields">
      <summary>
            Restores the default list layout.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ClearItems">
      <summary>
            Clear all of the items in the AddItem mode.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ClearSelCols">
      <summary>
            Deselects all selected columns in the current split.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ClearSelected">
      <summary>
            Clears all selected rows.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.CloseCombo">
      <summary>
            Closes the dropdown list of the combo.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.FindString(System.String)">
      <summary>
            Finds the first row index such that the cell text starts with string s in the column specified by the DisplayMember property.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.FindString(System.String,System.Int32)">
      <summary>
            Finds the first row index after startIndex such that the cell text starts with string s in the column specified by the DisplayMember property.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.FindString(System.String,System.Int32,System.Int32)">
      <summary>
            Finds the first row index after startIndex such that the cell text starts with string s in the given column.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.FindString(System.String,System.Int32,System.String)">
      <summary>
            Finds the first row index after startIndex such that the cell text starts with string s in the given column.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.FindStringExact(System.String)">
      <summary>
            Finds the first row index such that the cell text is exactly same with string s in the column specified by the DisplayMember property.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.FindStringExact(System.String,System.Int32,System.Int32)">
      <summary>
            Finds the first row index after startIndex such that the cell text is exactly same with string s in the given column.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.FindStringExact(System.String,System.Int32,System.String)">
      <summary>
            Finds the first row index after startIndex such that the cell text is exactly same with string s in the given column.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.FindStringExact(System.String,System.Int32)">
      <summary>
            Finds the first row index after startIndex such that the cell text is exactly same with string s in the column specified by the DisplayMember property.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.GetItemText(System.Int32,System.Int32)">
      <summary>
            Get the cell text for C1Combo.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.GetItemText(System.Int32,System.String)">
      <summary>
            Gets the cell text for any cell.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.HoldFields">
      <summary>
            Holds the current column/field layout.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.InsertHorizontalSplit(System.Int32)">
      <summary>
            Inserts a horizontal split at the specified index.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.InsertItem(System.String,System.Int32)">
      <summary>
            Inserts a new item at the index position in the AddItem mode.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.InsertVerticalSplit(System.Int32)">
      <summary>
            Inserts a vertical split at the specified index.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.OpenCombo">
      <summary>
            Drops down the list of the combo.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.PointAt(System.Int32,System.Int32)">
      <summary>
            Returns one of the PointAtEnum constants, which indicates the kind of list element beneath the specified coordinate pair.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.Rebind">
      <summary>
            Re-establishes the connection with the bound data source.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.Rebind(System.Boolean)">
      <summary>
            Re-establishes the connection with the bound data source.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.RemoveHorizontalSplit(System.Int32)">
      <summary>
            Removes a horizontal split at the specified index.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.RemoveItem(System.Int32)">
      <summary>
            Removes the item at the given position in the AddItem mode.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.RemoveVerticalSplit(System.Int32)">
      <summary>
            Removes a vertical split at the specified index.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.RowContaining(System.Int32)">
      <summary>
            Returns the zero-based index of the display row containing the specified coordinate¡£
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.RowTop(System.Int32)">
      <summary>
            Returns the Y coordinate of the top of a visible row.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.ScrollCtl(System.Int32,System.Int32)">
      <summary>
            Scrolls the list data area by the specified number of rows and columns.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.Select(System.Int32,System.Int32)">
      <summary>
            Selects specified text in a combo box's text box.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.SelectAll">
      <summary>
            Selects all of the text in a combo box's text box.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.SetAddItemData(System.Int32,System.Int32,System.String)">
      <summary>
            Updates the cell data for the AddItem mode.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.Sort(System.Int32,C1.Win.C1List.SortDirEnum)">
      <summary>
            Sorts a specific column for the control.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.Sort(System.String,C1.Win.C1List.SortDirEnum)">
      <summary>
            Sorts a specific column for the control.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.SplitContaining(System.Int32,System.Int32)">
      <summary>
            Returns the Index value of the split containing the specified coordinate pair.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1Combo.PerformCloseUp">
      <summary>
      </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.Change">
      <summary>
            Fires only if the ComboStyle property is set to 0-Dropdown Combo or 1-Simple Combo, and the user changes the text by typing into the text box portion of the C1Combo control.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.ColMove">
      <summary>
            Fires when the user has finished moving the selected columns.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.ColResize">
      <summary>
            Fires after the user has finished resizing a column.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.ColFootClick">
      <summary>
            Fires when the footer of the control is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.ColHeadClick">
      <summary>
            Fires when the headers of the control is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.AfterSort">
      <summary>
            Fires after a column is sorted by clicking the column header.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.ItemChanged">
      <summary>
            Fires when the contents of the text box portion of a combo box and the current row in the list portion change simultaneously.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.Scroll">
      <summary>
            Fires when the user scrolls the control horizontally or vertically using the scroll bars.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.DataSourceChanged">
      <summary>
            Fires when a bound data source is changed or requeried.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.LeftColChange">
      <summary>
            Fires when the first visible column of a list or split is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.NotInList">
      <summary>
            Fires when the user enters a value in the text portion of a combo box that is not found in the list portion.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.Mismatch">
      <summary>
            Fires when the user enters a value in the text portion of a combo box that is not found in the list portion.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.RowChange">
      <summary>
            Fires when the user changes a row in the list.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.RowResize">
      <summary>
            Fires when the user has finished resizing a list row.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.SelChange">
      <summary>
            Fires when the user selects a different range of rows or columns.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.SelectionChangeCommitted">
      <summary>
            Fires when the selected item has changed and that change is displayed in the C1ComboBox.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.Error">
      <summary>
            Fires when a data access error occurs.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.FormatText">
      <summary>
            Fires when the list is about to display cell data in a column whose NumberFormat property is set.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.DrawItem">
      <summary>
            Fires when any cell is required to be redrawn and DrawMode is set to OwnerDrawFixed or OwnerDrawVariable.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.UnboundColumnFetch">
      <summary>
            Fires when the control needs to display the value of a cell in an unbound column.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.FetchCellTips">
      <summary>
            Fires when the control has focus and the cursor is idle for a small amount of time (defined by the CellTipsDelay property) over a data cell, column header, column footer, split header or control caption bar.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.FetchCellStyle">
      <summary>
            Fires when the list is about to display cell data in a column whose FetchStyle property is set to True.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.FetchRowStyle">
      <summary>
            Fires whenever the list is about to display a row of data and the FetchRowStyles property is True.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.FetchScrollTips">
      <summary>
            Fires whenever the list has focus and the scrollbar thumb is moved using the mouse.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.TopIndexChange">
      <summary>
            Fires when the first displayed row of a control or split is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.SelectedValueChanged">
      <summary>
            Occurs when the SelectedValue property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.DisplayMemberChanged">
      <summary>
            Occurs when the DisplayMember property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.C1Combo.ValueMemberChanged">
      <summary>
            Occurs when the ValueMember property changes.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.AddItemCols">
      <summary>
            Returns or sets the column number for the AddItem mode.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.AddItemSeparator">
      <summary>
            Determines the separation string for columns when using the AddItem method in AddItem mode.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.AllowColMove">
      <summary>
            Enables/disables interactive column movement.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.AllowColSelect">
      <summary>
            Enables/disables interactive column selection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.AllowHorizontalSplit">
      <summary>
            Specifies if a user is allowed to create horizontal splits.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.AllowVerticalSplit">
      <summary>
            Specifies if a user is allowed to create vertical splits.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.AllowSort">
      <summary>
            Returns or sets whether or not column sort is allowed.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.AlternatingRows">
      <summary>
            Determines if a list or split displays odd-numbered rows in one style and even-numbered rows in another.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.AutoCompletion">
      <summary>
            Determines whether matching incremental search values are automatically copied to the text portion of a combo during editing.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.AutoDropDown">
      <summary>
            Determines whether the control automatically opens when the user types a character into a cell.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.AllowDrag">
      <summary>
            Returns or sets the flag indicating if text can be taken from the editor by drag and drop.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.DragMode">
      <summary>
            Returns or sets the value that indicates whether the combo box or the programmer handles a drag operation.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.DropMode">
      <summary>
            Returns or sets a value that indicates how the combo box handles drop operations.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.BackColor">
      <summary>
            Controls the background color of the list portion of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.FetchRowStyles">
      <summary>
            Specifies whether the FetchRowStyle event will be fired.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.Bookmark">
      <summary>
            Returns or sets the bookmark identifying the current row in the list.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.Caption">
      <summary>
            Returns or sets the caption for list.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.CaptionHeight">
      <summary>
            Returns or sets the height of the list caption area.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.CaptionStyle">
      <summary>
            Returns or sets the Style object that controls the appearance of the caption area.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.CaptionVisible">
      <summary>
            Returns or sets the flag indicating if list¡¯s caption is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.CellTips">
      <summary>
            Determines whether the list displays a pop-up text window when the cursor is idle.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.CellTipsDelay">
      <summary>
            Determines the amount of time before the cell tip window is displayed.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.CellTipsWidth">
      <summary>
            Returns or sets the width of the cell tip window.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.Col">
      <summary>
            Returns or sets the column position of the current cell.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.ColumnFooters">
      <summary>
            Specifies whether footers are displayed.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.ColumnHeaders">
      <summary>
            Specifies whether headers are displayed.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.ColumnCaptionHeight">
      <summary>
            Returns or sets the height of the column headers.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.ColumnFooterHeight">
      <summary>
            Returns or sets the height of the column footers.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.Columns">
      <summary>
            Returns a collection of C1DataColumn objects.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.ColumnWidth">
      <summary>
            Returns or sets the column width for each column in the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.CharacterCasing">
      <summary>
            Indicates if all characters should be left alone or converted to uppercase or lowercase.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.DataMode">
      <summary>
            Specifies the normal or additem mode.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.DeadAreaBackColor">
      <summary>
            Controls the background color of the dead area in the list.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.DefColWidth">
      <summary>
            Specifies column width for auto-created columns.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.DisplayMember">
      <summary>
            Returns or sets the DataSource field used for incremental search.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.DrawMode">
      <summary>
            Gets or sets a value indicating whether your code or the operating system will handle drawing of elements in the list.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.EditorBackColor">
      <summary>
            Controls the background color of the editor portion of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.EditorForeColor">
      <summary>
            Controls the foreground color of the editor portion of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.EditorFont">
      <summary>
            Returns or sets the font associated with the text box portion of a C1Combo control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.EditorHeight">
      <summary>
            Gets/Sets the editor height.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.EmptyRows">
      <summary>
            Returns or sets a value that determines how the list displays rows below the last data row.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.ExtendRightColumn">
      <summary>
            Returns or sets whether the last column will extend to fill the dead area of the list.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.FlatStyle">
      <summary>
            Determines the appearance of the list.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.ForeColor">
      <summary>
            Controls the foreground color of the list portion of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.Font">
      <summary>
            Returns or sets the font associated with the list portion of a C1Combo control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.FooterStyle">
      <summary>
            Returns the Style object that controls the appearance of column footers.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.FocusedSplit">
      <summary>
            Gets the Split that has focus.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.HeadingStyle">
      <summary>
            Returns the Style object that controls the appearance of column heading.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.HighLightRowStyle">
      <summary>
            Returns the Style object that controls the appearance of hight light row.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.HScrollBar">
      <summary>
            Returns the HBar object that controls the appearance of the horizontal scrollbar.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.IntegralHeight">
      <summary>
            Controls whether partial rows are displayed.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.ItemHeight">
      <summary>
            Returns or sets the height of all list rows.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.KeepForeColor">
      <summary>
            Determines whether the fore ground color of text box keeps unchanged when the combo is disabled, this only takes effect when the ComboStyle is DropdownList.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.LeftCol">
      <summary>
            Returns or sets the zero-based index of the leftmost column in a list or split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.LimitToList">
      <summary>
            Determines if users can enter an entry which is not in the list.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.ListCount">
      <summary>
            Returns the total row number for the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.MatchCol">
      <summary>
            Specifies the column where the incremental search is performed on.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.MatchEntry">
      <summary>
            Returns or sets a value indicating how the control performs searches based on user's input.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.MatchEntryTimeout">
      <summary>
            Returns or sets a value indicating the timeout, in milliseconds, for incremental searching.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.MaxDropDownItems">
      <summary>
            The maximum number of items of in the drop-down portion of the combo box.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.MaxLength">
      <summary>
            Gets or sets the maximum number of characters allowed in the editable portion of a combo box.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.MouseCursor">
      <summary>
            Determines the style of the mouse cursor when it is over list portion of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.Enabled">
      <summary>
            Indicates whether the control is enabled.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.EvenRowStyle">
      <summary>
            Returns or sets the Style object that controls the appearance of an even-numbered row.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.OddRowStyle">
      <summary>
            Returns or sets the Style object that controls the appearance of an odd-numbered row.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.PartialRightColumn">
      <summary>
            True if rightmost column can be clipped to the edge of a split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.PreferredHeight">
      <summary>
            Gets the preferred height of the item area of the combo box.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.ReadOnly">
      <summary>
            Controls whether the text in the control can be changed or not.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.Row">
      <summary>
            Returns or sets the position of current list row relative to the first displayed row.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.RowDivider">
      <summary>
            Determines the style of the border drawn between list rows.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.RowSubDividerColor">
      <summary>
            Returns or sets the color of a RowSubDivider.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.RowTracking">
      <summary>
            Controls whether rows are automatically highlighted as the mouse is moved over the C1List control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.ScrollTips">
      <summary>
            Determines whether the list displays a pop-up text window when the scrollbar thumb is dragged.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.ScrollTrack">
      <summary>
            Determines whether the list constantly displays information as it scrolls.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.SelectedCols">
      <summary>
            Returns the SelectedColumnCollection object.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.SelectedIndex">
      <summary>
            Returns or sets the bookmark of the currently selected item.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.SelectedStyle">
      <summary>
            Returns the Style object that controls the appearance of selected cells.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.SelectedText">
      <summary>
            Returns or sets the string containing the currently selected text within the combo box editing window.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.SelectedValue">
      <summary>
            Gets or sets the value of the member property specified by the ValueMember property.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.SelectionLength">
      <summary>
            Returns or sets the number of characters selected within the editing window.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.SelectionStart">
      <summary>
            Returns or sets the starting point of the text selection within the editing window.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.Split">
      <summary>
            Returns current split number.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.Splits">
      <summary>
            Returns a Collection of Split objects.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.Style">
      <summary>
            Returns or sets the normal Style object for the list.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.Styles">
      <summary>
            Returns a collection of named Style objects.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.SuperBack">
      <summary>
            Controls the BackSpace behavior when AutoCompletion and LimitToList are set to true.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.Text">
      <summary>
            Returns or sets the text displayed in the editor.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.TextAlign">
      <summary>
            Determines how the text will be aligned within the editor.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.TopIndex">
      <summary>
            Returns or sets a value containing the bookmark for the first visible row in a list or split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.WillChangeToIndex">
      <summary>
            Returns the bookmark identifying the will-be selected item in a control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.WillChangeToText">
      <summary>
            Returns the string identifying the text for the item to be selected.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.WillChangeToValue">
      <summary>
            Returns the object identifying the value to be selected.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.ValueMember">
      <summary>
            Specifies the field name for binding purposes.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.VisibleCols">
      <summary>
            Returns the number of visible columns in the current split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.VisibleRows">
      <summary>
            Returns the number of visible rows in the list.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.VScrollBar">
      <summary>
            Returns or sets the VBar object that controls the appearance of the vertical scrollbar.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.VisualStyle">
      <summary>
            Gets or sets a value that determines the overall appearance of the control.
            </summary>
      <remarks>
        <para>This property allows you to quickly customize the appearance of the control so
            it matches the appearance of your application.</para>
        <para>The settings available include <b>System</b>, various Microsoft Office color 
            schemes, and <b>Custom</b>, which relies on the controls standard styles and appearance 
            properties.</para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.DataSource">
      <summary>
            Specifies the data source object used to bind the control to.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.DataMember">
      <summary>
            Returns or sets the name of the data member used to populate the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1Combo.AutoSelect">
      <summary>
            Determines whether the combo sets its index to the current position of the datasource when it is firstly filled with data.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.Design.GridStyleCollectionEditor">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.Design.GridStyleCollectionEditor.m_propGrid">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Design.GridStyleCollectionEditor.#ctor(System.Type)">
      <summary>
      </summary>
      <param name="type">
      </param>
    </member>
    <member name="M:C1.Win.C1List.Design.GridStyleCollectionEditor.CreateCollectionForm">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Design.GridStyleCollectionEditor.CanRemoveInstance(System.Object)">
      <summary>
      </summary>
      <param name="value">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Design.GridStyleCollectionEditor.DestroyInstance(System.Object)">
      <summary>
      </summary>
      <param name="inst">
      </param>
    </member>
    <member name="T:C1.Win.C1List.Design.SplitCollectionEditor">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Design.SplitCollectionEditor.#ctor(System.Type)">
      <summary>
      </summary>
      <param name="type">
      </param>
    </member>
    <member name="M:C1.Win.C1List.Design.SplitCollectionEditor.CreateCollectionForm">
      <summary>
      </summary>
      <returns>
      </returns>
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Design.SplitCollectionEditor.CanRemoveInstance(System.Object)">
      <summary>
      </summary>
      <param name="value">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1List.PrintOptionsForm">
      <summary>
            Represents print/export options form.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.OnExporterChanged(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.OnActionChanged(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_exporter">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_action">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_actionMask">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_rowHeight">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_pageBreaks">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_fillEmpty">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_wrapText">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_outputFileName">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_chkUseGridColors">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_numFooterHeight">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_numHeaderHeight">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_txtFooter">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_txtHeader">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_numMaxRowHeight">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_cmbRowHeight">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_chkColFooter">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_chkColHeader">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_chkSplitHeader">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_chkGridHeader">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_chkPrintHorzSplits">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_cmbStretchToWidth">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_cmbWrapText">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_cmbHorzPageBreak">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_chkShowProgress">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_grpPageHeaders">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_lblFooterHeight">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_lblHeaderHeight">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_lblFooter">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_lblHeader">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_grpRenderOptions">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_lblRowHeight">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_lblStretchToWidth">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_lblWrapText">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_lblHorzPageBreak">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_btnOK">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_btnCancel">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_lblAction">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_btnOutputFileName">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_btnOptions">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_cmbAction">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_lblOutputFileName">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_lblMaxRowHeight">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_grpFormView">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.m_chkOneFormPerPage">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.#ctor">
      <summary>
            Creates a new instance of the PrintOptionsFrom class.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.Dispose(System.Boolean)">
      <summary>
            Called when the class is being disposed.
            </summary>
      <param name="disposing">
            True to cleanup.
            </param>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.m_btnOutputFileName_Click(System.Object,System.EventArgs)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.m_btnOptions_Click(System.Object,System.EventArgs)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.m_cmbAction_SelectedIndexChanged(System.Object,System.EventArgs)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.UpdateStatus">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.PrintOptionsForm_Load(System.Object,System.EventArgs)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.PrintOptionsForm_Closing(System.Object,System.ComponentModel.CancelEventArgs)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.m_cmbRowHeight_SelectedIndexChanged(System.Object,System.EventArgs)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.m_cmbHorzPageBreak_SelectedIndexChanged(System.Object,System.EventArgs)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.m_cmbStretchToWidth_SelectedIndexChanged(System.Object,System.EventArgs)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.m_cmbWrapText_SelectedIndexChanged(System.Object,System.EventArgs)">
      <summary>
      </summary>
    </member>
    <member name="E:C1.Win.C1List.PrintOptionsForm.ExporterChanged">
      <summary>
      </summary>
    </member>
    <member name="E:C1.Win.C1List.PrintOptionsForm.ActionChanged">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintOptionsForm.Exporter">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintOptionsForm.OutputFileName">
      <summary>
            Gets or sets the output file name.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintOptionsForm.Action">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintOptionsForm.ActionMask">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintOptionsForm.RowHeight">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintOptionsForm.PageBreaks">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintOptionsForm.FillEmpty">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintOptionsForm.WrapText">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintOptionsForm.ActionItem">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.ActionItem.Empty">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.ActionItem.#ctor(C1.Win.C1List.PrintInfo.ActionFlags,System.Object)">
      <summary>
            Creates a new instance of the ActionItem class. 
            </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.ActionItem.ToString">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintOptionsForm.ActionItem.Action">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintOptionsForm.ActionItem.Provider">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintOptionsForm.RowHeightItem">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.RowHeightItem.RowHeight">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.RowHeightItem.#ctor(C1.Win.C1List.PrintInfo.RowHeightEnum)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.RowHeightItem.ToString">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.RowHeightItem.RowHeightItems">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintOptionsForm.PageBreakItem">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.PageBreakItem.PageBreaks">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.PageBreakItem.#ctor(C1.Win.C1List.PrintInfo.PageBreaksEnum)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.PageBreakItem.ToString">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.PageBreakItem.PageBreakItems">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintOptionsForm.FillEmptyItem">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.FillEmptyItem.FillEmpty">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.FillEmptyItem.#ctor(C1.Win.C1List.PrintInfo.FillEmptyEnum)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.FillEmptyItem.ToString">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.FillEmptyItem.FillEmptyItems">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintOptionsForm.WrapTextItem">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.WrapTextItem.WrapText">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.WrapTextItem.#ctor(C1.Win.C1List.PrintInfo.WrapTextEnum)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintOptionsForm.WrapTextItem.ToString">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintOptionsForm.WrapTextItem.WrapTextItems">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.C1TrueDBGridPrintOptionsFormDropDownEditor">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.C1OwnerDrawPrint">
      <summary>
            Object to manage custom page headers and footers when printing and previewing.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1OwnerDrawPrint.RenderDirectText(System.Object,System.Object,System.String,System.Object,System.Drawing.Font,System.Drawing.Color,C1.Win.C1List.AlignHorzEnum)">
      <summary>
            Renders text for the document.
            </summary>
      <param name="x">X-coordinate to start the rendering.</param>
      <param name="y">Y-coordinate to start the rendering.</param>
      <param name="text">The text to render.</param>
      <param name="width">The width for the rendered text.</param>
      <param name="font">The font to use.</param>
      <param name="textColor">The color of the text.</param>
      <param name="horzAlign">The alignment to render the text.</param>
    </member>
    <member name="M:C1.Win.C1List.C1OwnerDrawPrint.RenderDirectImage(System.Object,System.Object,System.Drawing.Image,System.Object,System.Object,C1.Win.C1List.BackgroundPictureDrawModeEnum)">
      <summary>
            Renders an image for the document.
            </summary>
      <param name="x">X-coordinate.</param>
      <param name="y">Y-coordinate.</param>
      <param name="image">Image to render.</param>
      <param name="width">Width to render.</param>
      <param name="height">Height to render.</param>
      <param name="imageAlign">Alignment options.</param>
    </member>
    <member name="M:C1.Win.C1List.C1OwnerDrawPrint.RenderDirectLine(System.Object,System.Object,System.Object,System.Object,System.Drawing.Color,System.Double)">
      <summary>
            Renders a line for the document.
            </summary>
      <param name="fromX">Start x-coordinate.</param>
      <param name="fromY">Start y-coordinate.</param>
      <param name="toX">End x-coordinate.</param>
      <param name="toY">End y-coordinate.</param>
      <param name="color">Color of the line.</param>
      <param name="width">Width of the line.</param>
    </member>
    <member name="P:C1.Win.C1List.C1OwnerDrawPrint.HeightInch">
      <summary>
            The height of drawing area in inches
            </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintForm">
      <summary>
            The print preview form.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintForm.#ctor">
      <summary>
            Creates a new instance of the PrintFrom class.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintForm.Init">
      <summary>
             Additional initialization of the form.
             </summary>
      <remarks>
             To override properties like FormBorderStyle, MaximizeBox, MinimizeBox, ControlBox etc. of a Form,
             inherited from "C1.Win.C1List.PrintForm", override the Init method of the PrintForm.
             First call the base.Init(), then set the properties you want.
             </remarks>
      <example>
             The example is a custom PrintForm which has sizable border, control box is on, and minimize and maximize boxes are off.
             <code>
                 public class CustPrintForm : C1.Win.C1List.PrintForm
                {
                    public CustPrintForm(): base()
                    {
                    }
            
                    protected override void Init()
                    {
                        base.Init();
                        FormBorderStyle = FormBorderStyle.Sizable;
                        this.ControlBox = true;
                        this.MinimizeBox = false;
                        this.MaximizeBox = false;
                    }
                }
             </code></example>
    </member>
    <member name="M:C1.Win.C1List.PrintForm.Dispose(System.Boolean)">
      <summary>
            Overloaded. Overridden. Releases all resources used by the Control.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="T:C1.Win.C1List.C1TrueDBGridPreviewFormDropDownEditor">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintCancelException">
      <summary>
            Represents exception that is thrown if printing or exporting
            a <see cref="T:C1.Win.C1List.C1List" /> is cancelled by the user.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintCancelException.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1List.PrintCancelException" /> class.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintInfo">
      <summary>
            Specifies how a <see cref="T:C1.Win.C1List.C1List" /> should be printed
            or exported when using such methods as
            <see cref="!:C1List.ExportTo()" />,
            <see cref="M:C1.Win.C1List.PrintInfo.Print" /> etc.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintInfo.ToString">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.PrintInfo.ShouldSerializePageSettings">
      <summary>
            Indicates whether the <see cref="P:C1.Win.C1List.PrintInfo.PageSettings" /> property should be serialized.
            </summary>
      <returns>
        <b>true</b> if <see cref="P:C1.Win.C1List.PrintInfo.PageSettings" /> should be serialized, <b>false</b> otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1List.PrintInfo.ShouldSerializePrintOptionsFormClassName">
      <summary>
            Indicates whether the <see cref="P:C1.Win.C1List.PrintInfo.PrintOptionsFormClassName" /> property should be serialized.
            </summary>
      <returns>
        <b>true</b> if <see cref="P:C1.Win.C1List.PrintInfo.PrintOptionsFormClassName" /> should be serialized, <b>false</b> otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1List.PrintInfo.ResetPrintOptionsFormClassName">
      <summary>
            Resets the <see cref="P:C1.Win.C1List.PrintInfo.PrintOptionsFormClassName" /> property to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintInfo.ShouldSerializePreviewFormClassName">
      <summary>
            Indicates whether the <see cref="P:C1.Win.C1List.PrintInfo.PreviewFormClassName" /> property should be serialized.
            </summary>
      <returns>
        <b>true</b> if <see cref="P:C1.Win.C1List.PrintInfo.PreviewFormClassName" /> should be serialized, <b>false</b> otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1List.PrintInfo.ResetPreviewFormClassName">
      <summary>
            Resets the <see cref="P:C1.Win.C1List.PrintInfo.PreviewFormClassName" /> property to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintInfo.PrintPreview">
      <summary>
            Opens a separate modal window in which end users can preview the output
            that would be generated by the print operation.
            </summary>
      <remarks>
        <see cref="P:C1.Win.C1List.PrintInfo.PreviewFormClassName" /> allows to specify a custom form for
            the preview dialog.
            </remarks>
    </member>
    <member name="M:C1.Win.C1List.PrintInfo.Print">
      <summary>
            Prints the grid.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintInfo.Print(System.Drawing.Printing.PrinterSettings)">
      <summary>
            Prints the grid.
            </summary>
      <param name="printerSettings">Specifies the printer settings (including the printer) to use.</param>
    </member>
    <member name="M:C1.Win.C1List.PrintInfo.Print(System.IO.Stream)">
      <summary>
            Saves the current grid to a stream in <b>C1D</b>
            (native <b>C1PrintDocument</b>) format.
            <para>
            Note that unlike most other print/export methods, this method
            does not require <b>C1Report</b> assemblies.
            </para></summary>
      <param name="stream">The output stream.</param>
      <remarks>
        <para>
            The stream's <b>Position</b> is set to 0 when the method returns.
            </para>
        <para>This method does not show progress dialog
            (ignoring <see cref="P:C1.Win.C1List.PrintInfo.ShowProgressForm" /> property).
            </para>
      </remarks>
    </member>
    <member name="M:C1.Win.C1List.PrintInfo.PrintToStream">
      <summary>
            Saves the current grid to a memory stream in <b>C1D</b>
            (native <b>C1PrintDocument</b>) format.
            <para>
            The stream's <b>Position</b> is set to 0 when the method returns.
            </para></summary>
      <returns>The stream containing the saved grid.</returns>
    </member>
    <member name="M:C1.Win.C1List.PrintInfo.SaveAsC1d(System.String)">
      <summary>
            Saves the current grid as a <b>C1D</b> file, which can later be loaded
            into a <b>C1PrintDocument</b> (provided by <b>C1Report</b> product).
            <para>
            Note that unlike most other print/export methods, this method
            does not require <b>C1Report</b> assemblies.
            </para></summary>
      <param name="outputFileName">The name of the output file.</param>
      <returns>
        <b>true</b> if the file was successfully saved,
            <b>false</b> otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1List.PrintInfo.SaveAsC1d(System.IO.Stream)">
      <summary>
            Saves the current grid to a stream in <b>C1D</b>
            (native <b>C1PrintDocument</b>) format.
            <para>
            Note that unlike most other print/export methods, this method
            does not require <b>C1Report</b> assemblies.
            </para></summary>
      <param name="stream">The output stream.</param>
      <returns>
        <b>true</b> if the stream was successfully written,
            <b>false</b> otherwise.</returns>
      <remarks>
            Unlike the <see cref="M:C1.Win.C1List.PrintInfo.Print(System.IO.Stream)" /> method,
            this method does not reset the stream's position after saving.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.RowCanSplit">
      <summary>
            Gets or sets a value indicating whether the grid rows can split between pages.
            </summary>
      <remarks>
            This new property helps to print very long text in columns on multiple pages. 
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.UseGridColors">
      <summary>
            Gets or sets a value indicating whether the grid's color scheme is translated to the print page.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.OwnerDrawPageHeader">
      <summary>
            Gets or sets a value indicating whether the page header is owner-drawn.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.OwnerDrawPageFooter">
      <summary>
            Gets or sets a value indicating whether the page footer is owner-drawn.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.PageHeaderStyle">
      <summary>
            Gets or sets the style used to render the page header.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.PageHeader">
      <summary>
            Gets or sets the string to be printed at the top of each page.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.PageHeaderHeight">
      <summary>
            Gets or sets the height of the Page header.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.PageFooterStyle">
      <summary>
            Gets or sets the style used to render the page footer.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.PageFooterHeight">
      <summary>
            Gets or sets the page footer height in hundredths of an inch.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.PageFooter">
      <summary>
            Gets or sets a string to be printed at the bottom of each page.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.RepeatColumnFooters">
      <summary>
            Gets or sets a value indicating whether column footers should appear on each page.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.RepeatGridHeader">
      <summary>
            Gets or sets a value indicating whether the grid caption should appear on each page.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.RepeatSplitHeaders">
      <summary>
            Gets or sets a value inidcating whether split captions should appear on each page.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.PrintHorizontalSplits">
      <summary>
            Gets or sets a value indicating whether horizontal splits are previewed and printed.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.RepeatColumnHeaders">
      <summary>
            Gets or sets a value indicating whether column headers should appear on each page.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.FillAreaWidth">
      <summary>
            Gets or sets a value indicating how empty space left on a printed page is handled.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.PageBreak">
      <summary>
            Gets or sets the horizontal page break mode.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.WrapText">
      <summary>
            Gets or sets a value that controls how text is wrapped in a cell.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.PrintEmptyGrid">
      <summary>
            Gets or sets a value whether to print a grid if it has no data rows.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.MaxRowHeight">
      <summary>
            Gets or sets the maximum row height in hundredths of an inch
            (used if <see cref="P:C1.Win.C1List.PrintInfo.VarRowHeight" /> is <see cref="F:C1.Win.C1List.PrintInfo.RowHeightEnum.StretchToMax" />).
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.VarRowHeight">
      <summary>
            Gets or sets a value indicating how row height of the printed grid is determined.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.GridLines">
      <summary>
            Gets or sets a value indicating how grid lines are rendered.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.ShowOptionsDialog">
      <summary>
            Gets or sets a value indicating whether the options dialog is displayed
            when the grid is printed or exported.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.ShowProgressForm">
      <summary>
            Gets or sets a value indicating whether the progress dialog is displayed when the grid is printed or exported.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.OneFormPerPage">
      <summary>
            Gets or sets a value indicating whether the grid in Form view style is printed each record per page.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.ShowSelection">
      <summary>
            Gets or sets a value indicating whether selected cells will be highlighted when previewing or printing.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.PageSettings">
      <summary>
            Gets or sets the PageSettings for printing.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.ProgressCaption">
      <summary>
            Gets or sets the caption of the print progress dialog.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.PrintOptionsFormClassName">
      <summary>
            Gets or sets the class name of the form used as the print options dialog.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintInfo.PreviewFormClassName">
      <summary>
            Gets or sets the class name of the form used as the preview form.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintInfo.FillEmptyEnum">
      <summary>
            Specifies how empty space left on a printed page is handled (filled or otherwise).
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.FillEmptyEnum.ExtendAll">
      <summary>
            All columns are extended proportionally to fill the page.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.FillEmptyEnum.None">
      <summary>
            Empty space on the right of the page is left as is.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.FillEmptyEnum.ExtendLast">
      <summary>
            Rightmost column on the page is extended to fill the empty space.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintInfo.PageBreaksEnum">
      <summary>
            Specifies how grid is broken into extension (horizontal) pages
            when it is too wide to fit into one page.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.PageBreaksEnum.FitIntoArea">
      <summary>
            All columns are made narrow enough to fit into one page.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.PageBreaksEnum.ClipInArea">
      <summary>
            Columns that do not fit into one page are clipped.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.PageBreaksEnum.OnSplit">
      <summary>
            Horizontal page breaks can be inserted on grid splits as necessary.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.PageBreaksEnum.OnColumn">
      <summary>
            Horizontal page breaks can be inserted on any column as necessary.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintInfo.WrapTextEnum">
      <summary>
            Specifies how cell text is wrapped.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.WrapTextEnum.Wrap">
      <summary>
            Text can wrap in any cell as needed.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.WrapTextEnum.NoWrap">
      <summary>
            Text in cells never wraps.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.WrapTextEnum.LikeColumn">
      <summary>
            Use column's <see cref="P:C1.Win.C1List.Style.WrapText" /> property.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintInfo.GridLinesEnum">
      <summary>
            Specifies how grid lines are rendered.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.GridLinesEnum.Always">
      <summary>
            Grid lines are rendered.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.GridLinesEnum.None">
      <summary>
            Grid lines are not rendered.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintInfo.RowHeightEnum">
      <summary>
            Specifies how rows' heights are determined.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.RowHeightEnum.StretchToFit">
      <summary>
            Stretch rows vertically to fit all data.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.RowHeightEnum.LikeGrid">
      <summary>
            Use the grid's row height.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.RowHeightEnum.StretchToMax">
      <summary>
            Stretch rows vertically but not greater than <see cref="P:C1.Win.C1List.PrintInfo.MaxRowHeight" />.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintInfo.ActionFlags">
      <summary>
            Specify allowed print options.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.ActionFlags.None">
      <summary>
            No print/export operations are allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.ActionFlags.Print">
      <summary>
            Printing is allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.ActionFlags.Preview">
      <summary>
            Preview is allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.ActionFlags.Export">
      <summary>
            Export is allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PrintInfo.ActionFlags.MaskAll">
      <summary>
            All print/export operations are allowed.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.PrintPreviewWinSettings">
      <summary>
            Specifies the characteristics of the print preview window
            shown by the <see cref="M:C1.Win.C1List.PrintInfo.PrintPreview" /> method.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.PrintPreviewWinSettings.ToString">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.PrintPreviewWinSettings.ShouldSerializeUIStrings">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.PrintPreviewWinSettings.ResetUIStrings">
      <summary>
            Resets the array of UIStrings back to the default locale.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintPreviewWinSettings.Caption">
      <summary>
            Gets or sets the caption of the preview window.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintPreviewWinSettings.NavigationPaneDockingStyle">
      <summary>
            Gets or sets the position and manner in which the control is docked in the navigation page.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintPreviewWinSettings.Location">
      <summary>
            Gets or sets the location of the preview window.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintPreviewWinSettings.AllowSizing">
      <summary>
            Gets or sets a value indicating whether the end user has the ability to size the preview window.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintPreviewWinSettings.ZoomFactor">
      <summary>
            Gets or sets the zoom factor for print preview.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintPreviewWinSettings.Size">
      <summary>
            Gets or sets the size of the form.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintPreviewWinSettings.ToolBars">
      <summary>
            Gets or sets a value indicating the visibiity of toolbars.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.PrintPreviewWinSettings.UIStrings">
      <summary>
            Gets the array of user interface strings.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.C1PrintProgress">
      <summary>
            Print progress window.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1PrintProgress.#ctor">
      <summary>
            Initializes a new instance of the C1PrintProgress class.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1PrintProgress.Dispose(System.Boolean)">
      <summary>
            Overloaded. Releases the resources used by the component.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1PrintProgress.TxtPrinting">
      <summary>
            Gets or sets the text used to display the current progress when the grid is printed/previewed.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1PrintProgress.TxtCancel">
      <summary>
            Gets or sets the text for the Cancel button.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1PrintProgress.TxtTitle">
      <summary>
            Gets or sets the text for the Windows caption.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1PrintProgress.CancelClicked">
      <summary>
            Gets a value indicating if the print/preview was cancelled.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ListBase.Frame">
      <summary>
             Summary description for BaseGridFrame.
             </summary>
    </member>
    <member name="F:C1.Win.C1List.ListBase.Frame.m_serializing">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.#ctor">
      <summary>
            Ctor for the split container class.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.Dispose(System.Boolean)">
      <summary>
            Called when the class is being disposed.
            </summary>
      <param name="disposing">
            True to cleanup.
            </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.LoadResources">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.BeginInit">
      <summary>
            ISupportInitialize interface.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnBindingContextChanged(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.EndInit">
      <summary>
            ISupportInitialize interface.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnVisibleChanged(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.InitializeStyles">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnSizeChanged(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnResize(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnFontChanged(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.IsInputChar(System.Char)">
      <summary>
      </summary>
      <param name="charCode">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.IsInputKey(System.Windows.Forms.Keys)">
      <summary>
      </summary>
      <param name="keyData">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnKeyPress(System.Windows.Forms.KeyPressEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnKeyUp(System.Windows.Forms.KeyEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.saveImages">
      <summary>
            Helper method for serializing images.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnCursorChanged(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnMouseWheel(System.Windows.Forms.MouseEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnMouseLeave(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnClick(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnDoubleClick(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnLeave(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.WndProc(System.Windows.Forms.Message@)">
      <summary>
      </summary>
      <param name="m">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.createDataColumn(System.ComponentModel.PropertyDescriptor)">
      <summary>
      </summary>
      <param name="p">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.ClearFields">
      <summary>
            Restores the default list layout.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.GetSchema(System.ComponentModel.PropertyDescriptorCollection,System.Int32,System.Data.DataRelation,System.ComponentModel.PropertyDescriptor[])">
      <summary>
      </summary>
      <param name="props">
      </param>
      <param name="level">
      </param>
      <param name="r">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.SetDataSource(System.Object,System.Windows.Forms.BindingMemberInfo,System.Boolean)">
      <summary>
      </summary>
      <param name="datasource">
      </param>
      <param name="newDisplayMember">
      </param>
      <param name="force">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.UnboundGetData(System.Int32,C1.Win.C1List.C1DataColumn)">
      <summary>
      </summary>
      <param name="row">
      </param>
      <param name="dc">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.UnboundColUpdate(C1.Win.C1List.C1DataColumn,System.String)">
      <summary>
      </summary>
      <param name="dc">
      </param>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.UnboundGetRelativeBookmak(System.Int32,System.Int32)">
      <summary>
      </summary>
      <param name="start">
      </param>
      <param name="offset">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.UnboundAddRow">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.UnboundDeleteRow">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.UnboundGetMaxRow">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.UnboundReset(System.Boolean)">
      <summary>
      </summary>
      <param name="first">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.ShouldSerializeCaptionHeight">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.ResetVisualStyle">
      <summary>
            Resets <see cref="P:C1.Win.C1List.ListBase.Frame.VisualStyle" /> to the default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.ShouldSerializeVisualStyle">
      <summary>
            Tests whether <see cref="P:C1.Win.C1List.ListBase.Frame.VisualStyle" /> should be serialized.
            </summary>
      <returns>True if <see cref="P:C1.Win.C1List.ListBase.Frame.VisualStyle" /> should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.canCreateSplits">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnAfterColUpdate(C1.Win.C1List.ColEventArgs)">
      <summary>
            Raised after the datasource has been updated for a column.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnAfterDelete(System.EventArgs)">
      <summary>
            Raised after a row has been deleted.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnAfterInsert(System.EventArgs)">
      <summary>
            Raised after a row has been inserted.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnAfterUpdate(System.EventArgs)">
      <summary>
            Raised after a row has been updated.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnAfterSort(C1.Win.C1List.FilterEventArgs)">
      <summary>
            Raised after the datasource has been sorted.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnBeforeColUpdate(C1.Win.C1List.BeforeColUpdateEventArgs)">
      <summary>
            Raised before a column has been updated.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnBeforeDelete(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raised before a row is deleted.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnBeforeInsert(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raised before a row is inserted.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnBeforeUpdate(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raised before a row is updated.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnColResize(C1.Win.C1List.ColResizeEventArgs)">
      <summary>
            Raised when a column has been resized.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnHeadClick(C1.Win.C1List.ColEventArgs)">
      <summary>
            Raised when a column header has been clicked.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnFootClick(C1.Win.C1List.ColEventArgs)">
      <summary>
            Raised when a column footer has been clicked.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnOwnerDrawCell(C1.Win.C1List.OwnerDrawCellEventArgs)">
      <summary>
            Raised when a cell has to be rendered.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnOwnerDrawCellPrint(C1.Win.C1List.OwnerDrawCellEventArgs)">
      <summary>
            Raised when a cell has to be printed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnOwnerDrawPageHeader(C1.Win.C1List.OwnerDrawPageEventArgs)">
      <summary>
            Raised when Page header needs to be printed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnOwnerDrawPageFooter(C1.Win.C1List.OwnerDrawPageEventArgs)">
      <summary>
            Raised when a Page footer needs to be printed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnBeforeRowColChange(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raised before a row or column currency is changed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnRowColChange(C1.Win.C1List.RowColChangeEventArgs)">
      <summary>
            Raised after a row or column currency has been changed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnRowResize(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raised before a row is resized.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnScroll1(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raised when the grid scrolls.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnSelChange(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raised when a selection has changed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnSplitChange(System.EventArgs)">
      <summary>
            Raised when split currency has changed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnSplitRemoved(System.EventArgs)">
      <summary>
            Raised when horizontal or vertical splits are removed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnChange(System.EventArgs)">
      <summary>
            Raised when the grids cell content has been modified.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnUnboundColumnFetch(C1.Win.C1List.UnboundColumnFetchEventArgs)">
      <summary>
            Raised to fetch data for an unbound column.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnAfterColEdit(C1.Win.C1List.ColEventArgs)">
      <summary>
            Raised after a column has been edited.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnColSelected(C1.Win.C1List.ColEventArgs)">
      <summary>
            Raised when column is selected.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnBeforeColEdit(C1.Win.C1List.BeforeColEditEventArgs)">
      <summary>
            Raised before a column edit.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnColEdit(C1.Win.C1List.ColEventArgs)">
      <summary>
            Raised when a column has been edited.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnTopIndexChange(C1.Win.C1List.SplitEventArgs)">
      <summary>
            Raised when the top row has changed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnLeftColChange(C1.Win.C1List.SplitEventArgs)">
      <summary>
            Raised when the left column has changed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnFormatText(C1.Win.C1List.FormatTextEventArgs)">
      <summary>
            Raised when a cell value needs custom formatting.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnValueItemError(C1.Win.C1List.ColEventArgs)">
      <summary>
            Raised when a value item is selected that's not in the ValueItems collection.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnFetchCellStyle(C1.Win.C1List.FetchCellStyleEventArgs)">
      <summary>
            Raised when a custom style is to be used for rendering a cell.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnFetchRowStyle(C1.Win.C1List.FetchRowStyleEventArgs)">
      <summary>
            Raised when a custom style is to be used for renderind a grouped cell.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnAddNew(System.EventArgs)">
      <summary>
            Raised when a new row is added.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnColMove(C1.Win.C1List.ColMoveEventArgs)">
      <summary>
            Raised when a column is dragged.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnFetchCellTips(C1.Win.C1List.FetchCellTipsEventArgs)">
      <summary>
            Raised when a cell tip is to be displayed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnInit(System.EventArgs)">
      <summary>
            Raised when the grid is initialized.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnDataSourceChanged(System.EventArgs)">
      <summary>
            Raised when the datasource is changed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnLayoutReady(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnFetchScrollTips(C1.Win.C1List.FetchScrollTipsEventArgs)">
      <summary>
            Raised when scroll tips are to be displayed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnFilterChange(System.EventArgs)">
      <summary>
            Raised when a filter condition has changed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnFilterButtonClick(C1.Win.C1List.ColEventArgs)">
      <summary>
            Raised when a button in the filter bar is clicked.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnBeforeOpen(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raised before a child grid is displayed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnSort(C1.Win.C1List.FilterEventArgs)">
      <summary>
            Raised when the datasource is sorted by clicking the column header.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnFilter(C1.Win.C1List.FilterEventArgs)">
      <summary>
            Raised when AllowFilter is false.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.Frame.OnError(C1.Win.C1List.ErrorEventArgs)">
      <summary>
            Raised when the grid encounters an error through the UI.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="P:C1.Win.C1List.ListBase.Frame.ShowHeaderCheckBox">
      <summary>
            Determines whether a check box which can select/deselect all rows on header section is displayed in CheckBox selection mode.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.Frame.BorderStyle">
      <summary>
            Returns or sets the border style for the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.Frame.AllowSort">
      <summary>
            Returns or sets whether or not column sort is allowed.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.Frame.Caption">
      <summary>
            Returns or sets the caption for list.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.Frame.CaptionHeight">
      <summary>
            Returns or sets the height of the list caption area.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.Frame.DataMember">
      <summary>
            Returns or sets the name of the data member used to populate the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.Frame.DataSource">
      <summary>
            Specifies the data source object used to bind the control to.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.Frame.ScrollTrack">
      <summary>
            Determines whether the list constantly displays information as it scrolls.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.Frame.VisualStyle">
      <summary>
            Gets or sets a value that determines the overall appearance of the control.
            </summary>
      <remarks>
        <para>This property allows you to quickly customize the appearance of the grid so
            it matches the appearance of your application.</para>
        <para>The settings available include <b>System</b>, various Microsoft Office color 
            schemes, and <b>Custom</b>, which relies on the controls standard styles and appearance 
            properties.</para>
      </remarks>
    </member>
    <member name="E:C1.Win.C1List.ListBase.Frame.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property changes.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ListBase.View">
      <summary>
            Base class for splits.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.Dispose">
      <summary>
            Releases the resources used by the view.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the view.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.ToString">
      <summary>
            Returns the string that represents the current object.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.FixedCols">
      <summary>
            Returns the number of fixed columns.
            </summary>
      <returns>The number of fixed columns</returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.getLeftStartPos">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.getRecordSelectorRect(System.Int32)">
      <summary>
      </summary>
      <param name="row">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.getColumnFooterRect(System.Int32)">
      <summary>
      </summary>
      <param name="col">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.getEditRect(C1.Win.C1List.Style)">
      <summary>
      </summary>
      <param name="style">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.drawMarquee(System.Windows.Forms.PaintEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.drawCellLines(System.Windows.Forms.PaintEventArgs,System.Drawing.Rectangle,C1.Win.C1List.C1DisplayColumn)">
      <summary>
      </summary>
      <param name="e">
      </param>
      <param name="r">
      </param>
      <param name="c">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.renderInnerCell(System.Drawing.Graphics,C1.Win.C1List.Style,System.Drawing.Rectangle@,System.Int32,System.Int32,System.String,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Drawing.Image)">
      <summary>
      </summary>
      <param name="g">
      </param>
      <param name="style">
      </param>
      <param name="r">
      </param>
      <param name="col">
      </param>
      <param name="row">
      </param>
      <param name="val">
      </param>
      <param name="button">
      </param>
      <param name="buttonText">
      </param>
      <param name="buttonAlways">
      </param>
      <param name="activecell">
      </param>
      <param name="buttonPicture">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.drawCells(System.Windows.Forms.PaintEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.needsHorzSB">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.setHBarPages">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.adjustBottomRow(System.Int32)">
      <summary>
      </summary>
      <param name="row">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.getHScrollRect">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.pointInColWidth(System.Drawing.Point)">
      <summary>
      </summary>
      <param name="p">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.partialRow(System.Int32)">
      <summary>
      </summary>
      <param name="row">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.setCurrentCell(System.Int32,System.Int32)">
      <summary>
      </summary>
      <param name="row">
      </param>
      <param name="col">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.invalidateCurrentCell">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.endHorizontalResizing">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.beginMoveColumn(System.Drawing.Point,System.Int32)">
      <summary>
      </summary>
      <param name="p">
      </param>
      <param name="col">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.isLeftInsert(System.Int32,System.Drawing.Point)">
      <summary>
            returns true if we displaying the insertion point on the leftside of the rect
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.displayInsertionPoint(System.Int32,System.Drawing.Point)">
      <summary>
      </summary>
      <param name="col">
      </param>
      <param name="p">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.continueMoveColumn(System.Drawing.Point)">
      <summary>
      </summary>
      <param name="p">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.endMoveColumn(System.Drawing.Point)">
      <summary>
      </summary>
      <param name="p">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.canEdit">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.keyUp">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.keyUp(System.Boolean)">
      <summary>
      </summary>
      <param name="eval">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.keyLeft">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.keyLeft(System.Char,System.Boolean)">
      <summary>
      </summary>
      <param name="key">
      </param>
      <param name="eval">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.keyRight">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.keyRight(System.Boolean)">
      <summary>
      </summary>
      <param name="eval">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.keyPgDown">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.keyPgDown(System.Boolean)">
      <summary>
      </summary>
      <param name="eval">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.keyPgUp">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.keyPgUp(System.Boolean)">
      <summary>
      </summary>
      <param name="eval">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.keyHome">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.keyHome(System.Boolean)">
      <summary>
      </summary>
      <param name="eval">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.keyEnd">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.keyEnd(System.Boolean)">
      <summary>
      </summary>
      <param name="eval">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.getEditData(System.Int32,C1.Win.C1List.C1DisplayColumn)">
      <summary>
      </summary>
      <param name="row">
      </param>
      <param name="dc">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.computeRectAreas">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.computeColHeaderArea">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.computeColFooterArea">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.clipDataRect(System.Windows.Forms.PaintEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.clearSort(System.Int32)">
      <summary>
      </summary>
      <param name="col">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.View.findTopRow(System.Int32,System.Int32)">
      <summary>
      </summary>
      <param name="low">
      </param>
      <param name="high">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1List.RelationState">
      <summary>
            Defined the relationship of a column.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.RelationState.None">
      <summary>
            Column is not related.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.RelationState.Parent">
      <summary>
            Column is the parent.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.RelationState.Child">
      <summary>
            Column is the child.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.C1DataColumn">
      <summary>
            Represents a column that defines binding information for the datasource.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumn.#ctor">
      <summary>
            Creates a new instance of this object.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumn.ToString">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumn.CellText(System.Int32)">
      <summary>
            Gets the display value for a cell in a given row.
            </summary>
      <param name="row">The row to fetch.</param>
      <returns>The display value.</returns>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumn.CellValue(System.Int32)">
      <summary>
            Gets the cell value for a given row.
            </summary>
      <param name="row">The row to fetch.</param>
      <returns>The underlying data from the data source.</returns>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumn.Refresh">
      <summary>
            Invalidate the current column in all visible rows.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumn.RefreshCell">
      <summary>
            Invalidate the current cell.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumn.RefreshCell(System.Int32)">
      <summary>
            Invalidate the cell at the given row.
            </summary>
      <param name="row">Row to invalidate.</param>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumn.Refetch">
      <summary>
            Repopulate the entire grid from the data source.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumn.RefetchCell">
      <summary>
            Repopulates the current cell from the data source.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumn.RefetchCell(System.Int32)">
      <summary>
            Repopulates the specified data from the data source.
            </summary>
      <param name="row">The row to refetch.</param>
    </member>
    <member name="P:C1.Win.C1List.C1DataColumn.Caption">
      <summary>
            Gets or sets the text in the column header.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DataColumn.DataChanged">
      <summary>
            Gets or sets a value indicating whether data in this column has been modified.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DataColumn.DataField">
      <summary>
            Gets or sets the database field name for a column.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DataColumn.DataWidth">
      <summary>
            Gets or sets the maximum number of characters which may be entered for cells in this column.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DataColumn.DefaultValue">
      <summary>
            Gets or sets the default value for a column when a new row is added by the grid.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DataColumn.FooterText">
      <summary>
            Gets or sets the text displayed in the column footer.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DataColumn.NumberFormat">
      <summary>
            Gets or sets the formmating string for a column.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DataColumn.ValueItems">
      <summary>
            Gets the <see cref="P:C1.Win.C1List.C1DataColumn.ValueItems" /> object for this column.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DataColumn.Text">
      <summary>
            Gets or sets the display value for the current cell.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DataColumn.Value">
      <summary>
            Sets or retrieves the value of the current cell.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DataColumn.Tag">
      <summary>
            Gets or sets a user defined objects associated with this column.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.C1DisplayColumn">
      <summary>
            Represents the columns in a split.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumn.Dispose">
      <summary>
            Releases the resources used by the component.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumn.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the component.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumn.FixColumn(System.Boolean)">
      <summary>
            Fixes the column to keep it from scrolling when v is True, and unfixes the column when v is False.
            </summary>
      <param name="v">True to fix the column.</param>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumn.FixColumn(System.Boolean,System.Int32)">
      <summary>
            Fixes the column to keep it from scrolling when v is True, and unfixes the column when v is False.
            </summary>
      <param name="v">True to fix the column.</param>
      <param name="pos">The column index position after it is fixed.</param>
      <remarks>
             if v is True, the column is fixed and moved to the start position. 
             If v is False, the column is unfixed and moved to the last position.
            </remarks>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumn.AddCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style)">
      <summary>
            Controls the <see cref="P:C1.Win.C1List.C1DisplayColumn.Style" /> used to change the appearance for cells meeting the specified condition.
            </summary>
      <param name="condition">
            Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> enumerations.
            </param>
      <param name="style">
        <see cref="P:C1.Win.C1List.C1DisplayColumn.Style" /> object that specifies appearance attributes.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumn.AddRegexCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style,System.String)">
      <summary>
            Controls the <see cref="P:C1.Win.C1List.C1DisplayColumn.Style" /> used to change the appearance of cells according to their contents.
            </summary>
      <param name="condition">
            Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> enumerations.
            </param>
      <param name="style">
        <see cref="P:C1.Win.C1List.C1DisplayColumn.Style" /> object that specifies appearance attributes.
            </param>
      <param name="regex">
            A regular expression string.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumn.ClearCellStyle(C1.Win.C1List.CellStyleFlag)">
      <summary>
            Removes a cell condition established with a previous call to the <see cref="M:C1.Win.C1List.C1DisplayColumn.AddCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style)" /> method.
            </summary>
      <param name="condition">
            Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> enumerations.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumn.ClearRegexCellStyle(C1.Win.C1List.CellStyleFlag)">
      <summary>
            Removes a cell condition established with a previous call to the <see cref="M:C1.Win.C1List.C1DisplayColumn.AddRegexCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style,System.String)" /> method.
            </summary>
      <param name="condition">
            Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> enumerations.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumn.ClearRegexCellStyle(C1.Win.C1List.CellStyleFlag,System.String)">
      <summary>
            Removes a cell condition established with a previous call to the <see cref="M:C1.Win.C1List.C1DisplayColumn.AddRegexCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style,System.String)" /> method.
            </summary>
      <param name="condition">
            Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> enumerations.
            </param>
      <param name="regex">
            A regular expression string.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumn.AutoSize">
      <summary>
            Adjusts the width of a column to accommodate the longest visible field within that column.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.CellTop">
      <summary>
            Gets the vertical offset of the top of the cell for the current row.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.HeadingStyle">
      <summary>
            Gets or sets the <see cref="P:C1.Win.C1List.C1DisplayColumn.Style" /> that controls the appearance of the column headers.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.Style">
      <summary>
            Gets or sets the root <see cref="P:C1.Win.C1List.C1DisplayColumn.Style" /> for this column.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.FooterStyle">
      <summary>
            Gets or sets the <see cref="P:C1.Win.C1List.C1DisplayColumn.Style" /> object that controls the appearance of column footers.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.Visible">
      <summary>
            Gets or sets a value indicating the visibility of a column.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.ColumnDivider">
      <summary>
            Gets or sets the style of the border drawn between columns.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.Width">
      <summary>
            Gets or sets the width of a column.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.Height">
      <summary>
            Gets or sets the height of the column.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.AllowSizing">
      <summary>
            Gets or sets a value indicating whether column resizing is allowed.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.MinWidth">
      <summary>
            Gets or sets the minimum width a column can be resized to when in <see cref="P:C1.Win.C1List.ListBase.C1ListBase.SpringMode" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.HeaderDivider">
      <summary>
            Sets or retrieves whether to display the divider for this column in the header area. 
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.FooterDivider">
      <summary>
            Gets or sets a value indicating whether to display the column divider in the footer area.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.FetchStyle">
      <summary>
            Gets or sets a value indicating whether the FetchCellStyle event will be raised for a column.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.ButtonHeader">
      <summary>
            Gets or sets a value indicating whether a column header will act like a button.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.ButtonFooter">
      <summary>
            Gets or sets a value indicating whether a column footer will act like a button.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.OwnerDraw">
      <summary>
            Gets or sets a value indicating whether cells in this column are drawn by the user in the OwnerDrawCell event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.DataColumn">
      <summary>
            Gets the associted <see cref="T:C1.Win.C1List.C1DataColumn" /> associated with this object.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumn.Name">
      <summary>
            Gets the caption of the associated <see cref="T:C1.Win.C1List.C1DataColumn" /> objects.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.BaseTypeConverter">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.BaseTypeConverter.ObjFromString(System.String)">
      <summary>
      </summary>
      <param name="str">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.BaseTypeConverter.StringFromObj(System.Object)">
      <summary>
      </summary>
      <param name="o">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.BaseTypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>
      </summary>
      <param name="context">
      </param>
      <param name="sourceType">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.BaseTypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>
      </summary>
      <param name="context">
      </param>
      <param name="type">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.BaseTypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>
      </summary>
      <param name="context">
      </param>
      <param name="culture">
      </param>
      <param name="value">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.BaseTypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>
      </summary>
      <param name="context">
      </param>
      <param name="culture">
      </param>
      <param name="value">
      </param>
      <param name="type">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1List.BitmapTypeConverter">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.BitmapTypeConverter.ObjFromString(System.String)">
      <summary>
      </summary>
      <param name="str">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.BitmapTypeConverter.StringFromObj(System.Object)">
      <summary>
      </summary>
      <param name="o">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1List.GenericTypeConverter">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.GenericTypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>
      </summary>
      <param name="context">
      </param>
      <param name="sourceType">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.GenericTypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>
      </summary>
      <param name="context">
      </param>
      <param name="type">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1List.Util.GridLines">
      <summary>
            Represents the line used for row and column dividers.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.GridLines.#ctor">
      <summary>
            Creates a new instance of this object.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.GridLines.op_Equality(C1.Win.C1List.Util.GridLines,C1.Win.C1List.Util.GridLines)">
      <summary>
      </summary>
      <param name="a">
      </param>
      <param name="b">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Util.GridLines.op_Inequality(C1.Win.C1List.Util.GridLines,C1.Win.C1List.Util.GridLines)">
      <summary>
      </summary>
      <param name="a">
      </param>
      <param name="b">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Util.GridLines.ToString">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1List.Util.GridLines.Color">
      <summary>
            Gets or sets the color of lines used for row and column dividers.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Util.GridLines.Style">
      <summary>
            Gets or sets the style of lines used for row and column dividers.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.Split">
      <summary>
            Represents a horizontal or vertical pane to display and edit data.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Split.computeColHeaderArea">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Split.getHScrollRect">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Split.getFilterTextRect(System.Int32)">
      <summary>
      </summary>
      <param name="col">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Split.getFilterRect(System.Int32)">
      <summary>
      </summary>
      <param name="col">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Split.drawMarquee(System.Windows.Forms.PaintEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.Split.renderInnerCell(System.Drawing.Graphics,C1.Win.C1List.Style,System.Drawing.Rectangle@,System.Int32,System.Int32,System.String,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Drawing.Image)">
      <summary>
      </summary>
      <param name="g">
      </param>
      <param name="style">
      </param>
      <param name="r">
      </param>
      <param name="col">
      </param>
      <param name="row">
      </param>
      <param name="val">
      </param>
      <param name="button">
      </param>
      <param name="buttonText">
      </param>
      <param name="buttonAlways">
      </param>
      <param name="activecell">
      </param>
      <param name="buttonPicture">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Split.setCurrentCell(System.Int32,System.Int32)">
      <summary>
      </summary>
      <param name="row">
      </param>
      <param name="col">
      </param>
    </member>
    <member name="M:C1.Win.C1List.Split.OnFilterChanged">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Split.invalidateCurrentCell">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Split.FilterColumnAt(System.Drawing.Point,System.Int32@)">
      <summary>
      </summary>
      <param name="p">
      </param>
      <param name="col">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Split.keyUp">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Split.keyLeft">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Split.keyRight">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Split.keyPgDown">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Split.keyPgUp">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Split.getEditData(System.Int32,C1.Win.C1List.C1DisplayColumn)">
      <summary>
      </summary>
      <param name="row">
      </param>
      <param name="dc">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Split.ShouldSerializeCaptionHeight">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Split.ShouldSerializeColumnCaptionHeight">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Split.ShouldSerializeColumnFooterHeight">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Split.AddCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style)">
      <summary>
            Controls the <see cref="P:C1.Win.C1List.Split.Style" /> used to change the appearance for cells meeting the specified condition.
            </summary>
      <param name="condition">
            Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> enumerations.
            </param>
      <param name="style">
        <see cref="P:C1.Win.C1List.Split.Style" /> object that specifies appearance attributes.
            </param>
    </member>
    <member name="M:C1.Win.C1List.Split.AddRegexCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style,System.String)">
      <summary>
            Controls the <see cref="P:C1.Win.C1List.Split.Style" /> used to change the appearance of cells according to their contents.
            </summary>
      <param name="condition">
            Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> enumerations.
            </param>
      <param name="style">
        <see cref="P:C1.Win.C1List.Split.Style" /> object that specifies appearance attributes.
            </param>
      <param name="regex">
            A regular expression string.
            </param>
    </member>
    <member name="M:C1.Win.C1List.Split.ClearCellStyle(C1.Win.C1List.CellStyleFlag)">
      <summary>
            Removes a cell condition established with a previous call to the <see cref="M:C1.Win.C1List.Split.AddCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style)" /> method.
            </summary>
      <param name="condition">
            Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> enumerations.
            </param>
    </member>
    <member name="M:C1.Win.C1List.Split.ClearRegexCellStyle(C1.Win.C1List.CellStyleFlag)">
      <summary>
            Removes a cell condition established with a previous call to the <see cref="M:C1.Win.C1List.Split.AddCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style)" /> method.
            </summary>
      <param name="condition">
            Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> enumerations.
            </param>
    </member>
    <member name="M:C1.Win.C1List.Split.ClearRegexCellStyle(C1.Win.C1List.CellStyleFlag,System.String)">
      <summary>
            Removes a cell condition established with a previous call to the <see cref="M:C1.Win.C1List.Split.AddRegexCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style,System.String)" /> method.
            </summary>
      <param name="condition">
            Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> enumerations.
            </param>
      <param name="regex">
            A regular expression string.
            </param>
    </member>
    <member name="P:C1.Win.C1List.Split.AllowFocus">
      <summary>
            Gets or sets a value indicating whether the split can recive focus.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.AllowColMove">
      <summary>
            Gets or sets a value indicating the ability to move columns.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.AllowColSelect">
      <summary>
            Gets or sets a value indicating the ability to select columns.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.Name">
      <summary>
            Gets or sets the name of a split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.AllowRowSizing">
      <summary>
            Gets or sets how interactive row resizing is performed.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.AllowHorizontalSizing">
      <summary>
            Gets or sets a value indicating whether a user is allowed to resize horizontal splits.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.AllowVerticalSizing">
      <summary>
            Gets or sets a value indicating whether a user is allowed to resize vertical splits.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.AlternatingRowStyle">
      <summary>
            Gets or sets a value indicating whether the split uses the <see cref="P:C1.Win.C1List.Split.OddRowStyle" /> for odd-numbered rows and <see cref="P:C1.Win.C1List.Split.EvenRowStyle" /> for even-numbered rows.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.Caption">
      <summary>
            Gets or sets the caption.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.CaptionHeight">
      <summary>
            Gets or sets the height of the caption.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.ColumnCaptionHeight">
      <summary>
            Gets or sets the height of the column captions.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.ColumnFooterHeight">
      <summary>
            Gets or sets the height of column footers.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.CurrentCellVisible">
      <summary>
            Gets or sets a value indicating the visibility of the current cell in a split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.ExtendRightColumn">
      <summary>
            Gets or sets a value that determines how the last column will extend to fill the dead area of the split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.FetchRowStyles">
      <summary>
            Gets or sets a value indicating whether the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.FetchRowStyle" /> event will be raised.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.FirstRow">
      <summary>
            Gets or sets the row index for the first visible row in a split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.GroupColumns">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.LeftCol">
      <summary>
            Gets or sets the left most visible column for a split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.VerticalScrollGroup">
      <summary>
            Gets or sets the group which synchronizes verticall scrolling between splits. 
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.HorizontalScrollGroup">
      <summary>
            Gets or sets the group which synchronizes horizontal scrolling between splits. 
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.VScrollBar">
      <summary>
            Gets the <see cref="T:C1.Win.C1List.Util.VBar" /> object that controls the appearance of the vertical scrollbar.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.HScrollBar">
      <summary>
            Gets the <see cref="T:C1.Win.C1List.Util.HBar" /> object that controls the appearance of the horizontal scrollbar.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.SplitSize">
      <summary>
            Gets or sets the size of a split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.SplitSizeMode">
      <summary>
            Gets or sets a value indicating how the <see cref="P:C1.Win.C1List.Split.SplitSize" /> property is used to determine the actual size of a split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.CaptionStyle">
      <summary>
            Gets or sets the <see cref="P:C1.Win.C1List.Split.Style" /> object that controls the appearance of the caption area.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.EvenRowStyle">
      <summary>
            Gets or sets the <see cref="P:C1.Win.C1List.Split.Style" /> object that controls the appearance of an even-numbered row when using <see cref="P:C1.Win.C1List.ListBase.C1ListBase.AlternatingRows" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.FooterStyle">
      <summary>
            Gets or sets the <see cref="P:C1.Win.C1List.Split.Style" /> object that controls the appearance of column footers.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.HeadingStyle">
      <summary>
            Gets or sets the <see cref="P:C1.Win.C1List.Split.Style" /> object that controls the appearance of the grids column headers.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.HighLightRowStyle">
      <summary>
            Gets or sets the <see cref="P:C1.Win.C1List.Split.Style" /> object that controls the current row/cell when the <see cref="P:C1.Win.C1List.Split.MarqueeStyle" /> is set to Highlight Row/Cell.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.OddRowStyle">
      <summary>
            Gets or sets the <see cref="P:C1.Win.C1List.Split.Style" /> object that controls the appearance of an odd-numbered row when using <see cref="P:C1.Win.C1List.ListBase.C1ListBase.AlternatingRows" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.RecordSelectorStyle">
      <summary>
            Gets or sets the <see cref="P:C1.Win.C1List.Split.Style" /> object that controls the appearance of the RecordSelectors"/&gt;.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.SelectedStyle">
      <summary>
            Gets or sets the <see cref="P:C1.Win.C1List.Split.Style" /> object that controls the appearance of selected rows and columns.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.Style">
      <summary>
            Gets or sets the root <see cref="P:C1.Win.C1List.Split.Style" /> object for the Split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Split.DisplayColumns">
      <summary>
            Gets a collection of <see cref="T:C1.Win.C1List.C1DisplayColumn" /> objects.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ValueItems">
      <summary>
            Represents an object that defines how cells are rendered.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ValueItems.AnnotatePicture">
      <summary>
            True to display both value and display value.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ValueItems.DefaultItem">
      <summary>
            Sets or retrieves the index of default ValueItem or -1 if no default.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ValueItems.Presentation">
      <summary>
            Sets or retrieves how ValueItems are displayed.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ValueItems.Translate">
      <summary>
            Sets or retrieves whether or not data values in the column are translated using ValueItems.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ValueItems.Validate">
      <summary>
            Sets or retrieves whether input values are validated using the ValueItemCollection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ValueItems.Values">
      <summary>
            Returns a collection of Value/DisplayValue pairs.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ValueItemCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1List.ValueItem" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ValueItemCollection.Add(C1.Win.C1List.ValueItem)">
      <summary>
            Adds a <see cref="T:C1.Win.C1List.ValueItem" /> to the end of the collection.
            </summary>
      <param name="vi">The ValueItem to add.</param>
      <returns>The index at which the ValueItem has been added.</returns>
    </member>
    <member name="M:C1.Win.C1List.ValueItemCollection.OnInsert(System.Int32,System.Object)">
      <summary>
      </summary>
      <param name="index">
      </param>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ValueItemCollection.Insert(System.Int32,C1.Win.C1List.ValueItem)">
      <summary>
            Inserts a <see cref="T:C1.Win.C1List.ValueItem" /> at the specified index.
            </summary>
      <param name="index">The zero-based index at which the ValueItem should be inserted.</param>
      <param name="vi">The ValueItem to insert.</param>
    </member>
    <member name="M:C1.Win.C1List.ValueItemCollection.IndexOf(C1.Win.C1List.ValueItem)">
      <summary>
            Gets the index of the specified <see cref="T:C1.Win.C1List.ValueItem" />.
            </summary>
      <param name="vi">The ValueItem to search.</param>
      <returns>The index of the ValueItem.</returns>
    </member>
    <member name="P:C1.Win.C1List.ValueItemCollection.Item(System.Int32)">
      <summary>
            Gets or sets the specified <see cref="T:C1.Win.C1List.ValueItem" /> from the collection at the specified index.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ValueItem">
      <summary>
            Represents an object that defines a value/display value pair.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ValueItem.#ctor">
      <summary>
            Initializes a new instance of the ValueItem class.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ValueItem.#ctor(System.String,System.Object)">
      <summary>
            Initializes a new instance of the ValueItem class.
            </summary>
      <param name="value">Underlying data value.</param>
      <param name="displayValue">Translated value.</param>
    </member>
    <member name="M:C1.Win.C1List.ValueItem.ToString">
      <summary>
            Returns a string that represents the current object.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1List.ValueItem.Value">
      <summary>
            Sets or retrieves the raw (untranslated) value of this item.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ValueItem.DisplayValue">
      <summary>
            Sets or retrieves the formatted (display) value of this item.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.C1DataColumnCollection">
      <summary>
            Contains a collection of <see cref="T:C1.Win.C1List.C1DataColumn" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumnCollection.OnInsert(System.Int32,System.Object)">
      <summary>
      </summary>
      <param name="index">
      </param>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumnCollection.Clear">
      <summary>
            Removes all elements from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumnCollection.Add(C1.Win.C1List.C1DataColumn)">
      <summary>
            Adds a C1DataColumn to the end of the collection.
            </summary>
      <param name="dc">The C1DataColumn to add.</param>
      <returns>The index at which the C1DataColumn has been added.</returns>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumnCollection.RemoveAt(System.Int32)">
      <summary>
            Removes the C1DataColumn at the specified index.
            </summary>
      <param name="index">The zero-based index of the row to remove.</param>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumnCollection.Insert(System.Int32,C1.Win.C1List.C1DataColumn)">
      <summary>
            Inserts a <see cref="T:C1.Win.C1List.C1DataColumn" /> at the specified index.
            </summary>
      <param name="index">The zero-based index at which the C1DataColumn should be inserted.</param>
      <param name="dc">The C1DataColumn to insert.</param>
    </member>
    <member name="M:C1.Win.C1List.C1DataColumnCollection.IndexOf(C1.Win.C1List.C1DataColumn)">
      <summary>
            Gets the index of the specified <see cref="T:C1.Win.C1List.C1DataColumn" />.
            </summary>
      <param name="dc">
      </param>
      <returns>The index of the the C1DataColumn.</returns>
    </member>
    <member name="P:C1.Win.C1List.C1DataColumnCollection.Item(System.Int32)">
      <summary>
            Gets the specified C1DataColumn from the collection at the specified index.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DataColumnCollection.Item(System.String)">
      <summary>
            Gets the specified C1DataColumn from the collection with the specified name.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.C1DisplayColumnCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1List.C1DisplayColumn" /> in a <see cref="T:C1.Win.C1List.Split" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumnCollection.Insert(System.Int32,C1.Win.C1List.C1DisplayColumn)">
      <summary>
            Inserts a <see cref="T:C1.Win.C1List.C1DisplayColumn" /> at the specified index.
            </summary>
      <param name="index">The zero-based index at which the C1DisplayColumn should be inserted.</param>
      <param name="vc">The C1DataColumn to insert.</param>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumnCollection.IndexOf(C1.Win.C1List.C1DisplayColumn)">
      <summary>
            Gets the index of the <see cref="T:C1.Win.C1List.C1DisplayColumn" />.
            </summary>
      <param name="vc">
      </param>
      <returns>The index of the C1DisplayColumn.</returns>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumnCollection.IndexOf(C1.Win.C1List.C1DataColumn)">
      <summary>
            Gets the index of the <see cref="T:C1.Win.C1List.C1DisplayColumn" /> specified by the <see cref="T:C1.Win.C1List.C1DataColumn" />.
            </summary>
      <param name="dc">
      </param>
      <returns>The index of the C1DisplayColumn.</returns>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumnCollection.OnRemove(System.Int32,System.Object)">
      <summary>
      </summary>
      <param name="index">
      </param>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumnCollection.OnRemoveComplete(System.Int32,System.Object)">
      <summary>
      </summary>
      <param name="index">
      </param>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumnCollection.OnClear">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.C1DisplayColumnCollection.OnClearComplete">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumnCollection.Item(System.Int32)">
      <summary>
            Gets the specified <see cref="T:C1.Win.C1List.C1DisplayColumn" /> from the collection at the specified index.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumnCollection.Item(C1.Win.C1List.C1DataColumn)">
      <summary>
            Gets the specified <see cref="T:C1.Win.C1List.C1DisplayColumn" /> from the collection wich contains the specified <see cref="T:C1.Win.C1List.C1DataColumn" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1DisplayColumnCollection.Item(System.String)">
      <summary>
            Gets the specified <see cref="T:C1.Win.C1List.C1DisplayColumn" /> from the collection with the specified name.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.C1PropImagesCollection">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.C1PropImagesCollection.Add(System.Drawing.Image)">
      <summary>
      </summary>
      <param name="m">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1List.GridStyleCollection">
      <summary>
            Represents a collection of named <see cref="T:C1.Win.C1List.Style" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.GridStyleCollection.#ctor(C1.Win.C1List.ListBase.Frame)">
      <summary>
      </summary>
      <param name="owner">
      </param>
    </member>
    <member name="M:C1.Win.C1List.GridStyleCollection.OnInsert(System.Int32,System.Object)">
      <summary>
      </summary>
      <param name="index">
      </param>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1List.GridStyleCollection.Add(C1.Win.C1List.Style)">
      <summary>
            Adds a <see cref="T:C1.Win.C1List.Style" /> to the end of the collection.
            </summary>
      <param name="style">The Style to add.</param>
      <returns>The index at which the <see cref="T:C1.Win.C1List.Style" /> has been added.</returns>
    </member>
    <member name="M:C1.Win.C1List.GridStyleCollection.Insert(System.Int32,C1.Win.C1List.Style)">
      <summary>
            Inserts a <see cref="T:C1.Win.C1List.Style" /> at the specified index.
            </summary>
      <param name="index">The zero-based index at which the Style should be inserted.</param>
      <param name="style">The Style to insert.</param>
    </member>
    <member name="M:C1.Win.C1List.GridStyleCollection.IndexOf(C1.Win.C1List.Style)">
      <summary>
            Gets the index of the specified <see cref="T:C1.Win.C1List.Style" />.
            </summary>
      <param name="style">
      </param>
      <returns>The index of the Style.</returns>
    </member>
    <member name="M:C1.Win.C1List.GridStyleCollection.RemoveAt(System.Int32)">
      <summary>
            Removes the <see cref="T:C1.Win.C1List.Style" /> at the specified index.
            </summary>
      <param name="index">Teh zero-based index of the Style to remove.</param>
    </member>
    <member name="P:C1.Win.C1List.GridStyleCollection.Item(System.Int32)">
      <summary>
            Gets the specified Style from the collection given its index.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.GridStyleCollection.Item(System.String)">
      <summary>
            Gets the specified Style from the collection given its name.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.SelectedColumnCollection">
      <summary>
            Contains a collection of <see cref="T:C1.Win.C1List.C1DataColumn" /> objects that represent columns that are selected.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.SelectedColumnCollection.Clear">
      <summary>
            Removes all elements from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.SelectedColumnCollection.RemoveAt(System.Int32)">
      <summary>
            Removes the C1DataColumn at the specified index.
            </summary>
      <param name="index">The zero-based index of the row to remove.</param>
    </member>
    <member name="M:C1.Win.C1List.SelectedColumnCollection.Add(C1.Win.C1List.C1DataColumn)">
      <summary>
            Adds a C1DataColumn to the end of the collection.
            </summary>
      <param name="dc">The C1DataColumn to add.</param>
      <returns>The index at which the C1DataColumn has been added.</returns>
    </member>
    <member name="M:C1.Win.C1List.SelectedColumnCollection.Insert(System.Int32,C1.Win.C1List.C1DataColumn)">
      <summary>
            Inserts a <see cref="T:C1.Win.C1List.C1DataColumn" /> at the specified index.
            </summary>
      <param name="index">The zero-based index at which the C1DataColumn should be inserted.</param>
      <param name="dc">The C1DataColumn to insert.</param>
    </member>
    <member name="T:C1.Win.C1List.SelectedRowCollection">
      <summary>
            Represents a collection of Selected rows.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.SelectedRowCollection.Clear">
      <summary>
            Removes all elements from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.SelectedRowCollection.RemoveAt(System.Int32)">
      <summary>
            Removes the row at the specified index.
            </summary>
      <param name="index">The zero-based index of the row to remove.</param>
    </member>
    <member name="M:C1.Win.C1List.SelectedRowCollection.Add(System.Int32)">
      <summary>
            Adds a row to the end of the collection.
            </summary>
      <param name="row">Row number to add.</param>
      <returns>The index at which the row has been added.</returns>
    </member>
    <member name="M:C1.Win.C1List.SelectedRowCollection.Insert(System.Int32,System.Int32)">
      <summary>
            Inserts a row at the specified index.
            </summary>
      <param name="index">The zero-based index at which row should be inserted.</param>
      <param name="row">The row to insert.</param>
    </member>
    <member name="M:C1.Win.C1List.SelectedRowCollection.IndexOf(System.Int32)">
      <summary>
            Gets the index of the specified row.
            </summary>
      <param name="row">
      </param>
      <returns>The index of the row.</returns>
    </member>
    <member name="M:C1.Win.C1List.SelectedRowCollection.Contains(System.Int32)">
      <summary>
            Determines whether the collection contains the specified row.
            </summary>
      <param name="row">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1List.SelectedRowCollection.Item(System.Int32)">
      <summary>
            Gets or sets the specified Row index from the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.SplitCollection">
      <summary>
            Represents a collection of Split objects.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.Util.Xml.Serialization.IMemberTypeEncoder">
      <summary>
            This interface is used to allow objects and collection to serialize
            types of their members in a custom way.
            If a collection implements this interface, TypeToString is invoked during
            serialization of collection items, and for all items for which it returns a
            non-null string, that string is used as the element name of the item.
            If a class implements this interface, AND a member of that class has
            attribute TypeNameSerialization.Custom, AND does not have attribute
            XmlAttribute (i.e. is serialized as an element), TypeToString is invoked
            on the owner when that member is serialized, and if that returns a non-null
            string, that string is used as the value of TypeName attribute.
            When deserializing a collection which implements this interface,
            StringToType is invoked for each new item in the collection, and if that
            returns a non-null type, that type is used to create the item. Otherwise,
            TypeNameSerialization attribute is used.
            When deserializing a class which implements this interface, StringToType
            is invoked on that class for members with TypeNameSerialization.Custom
            attribute set.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.IMemberTypeEncoder.TypeToString(System.Object)">
      <summary>
            Returns a string representing the type of the object
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.IMemberTypeEncoder.StringToType(System.String)">
      <summary>
            Returns the type restored from the serialized string
            </summary>
    </member>
    <member name="M:C1.Win.C1List.SplitCollection.IndexOf(C1.Win.C1List.ListBase.View)">
      <summary>
            Gets the index if the specified Split.
            </summary>
      <param name="v">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1List.SplitCollection.Item(System.Int32)">
      <summary>
            Gets the specified Split object from the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.SplitCollection.Item(System.String)">
      <summary>
            Gets the specified Split object from the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.SplitCollection.Item(System.Int32,System.Int32)">
      <summary>
            Gets the specified Split object from the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.SplitCollection.RowCount">
      <summary>
            Gets the number of vertical splits in the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.SplitCollection.ColCount">
      <summary>
            Gets the number of horizontal splits in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ChangeEventHandler">
      <summary>
            Delegate for Change event.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.NotInListEventHandler">
      <summary>
            Delegate for NotInList event.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.MismatchEventHandler">
      <summary>
            Delegate for Mismatch event.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.OpenEventHandler">
      <summary>
            Handler for <see cref="E:C1.Win.C1List.ComboBoxContainer.Open" /> event.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.CloseEventHandler">
      <summary>
            Handler for <see cref="E:C1.Win.C1List.ComboBoxContainer.Close" /> event.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.NotInListEventArgs">
      <summary>
            Defines the event data for <see cref="E:C1.Win.C1List.C1Combo.NotInList" /> event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.NotInListEventArgs.NewEntry">
      <summary>
            String representing the text that was entered by the user but not found in the list.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.NotInListEventArgs.Retry">
      <summary>
            True to force the control to requery the list for the new entry.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.MismatchEventArgs">
      <summary>
            Defines event data for the <see cref="E:C1.Win.C1List.C1Combo.Mismatch" /> event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.MismatchEventArgs.NewEntry">
      <summary>
            String representing the text that was entered by the user but not found in the list.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.MismatchEventArgs.Reposition">
      <summary>
            False to prevent the current row from moving back to the top of the list when a mismatch has been typed. 
            </summary>
    </member>
    <member name="T:C1.Win.C1List.DragModeEnum">
      <summary>
            Defines the value that determines whether the combo box or the programmer handles a drag operation.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.DragModeEnum.Automatic">
      <summary>
            The combo box handles the drag operation.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.DragModeEnum.Manual">
      <summary>
            The combo box handles the drag operation.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.DropModeEnum">
      <summary>
            Defines value that determines how the combo box handles drop operations.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.DropModeEnum.Automatic">
      <summary>
            The combo box handles the drop operation.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.DropModeEnum.Manual">
      <summary>
            The programmer can manually handle the drop operation.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ComboStyleEnum">
      <summary>
            Defined the combobox style.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ComboStyleEnum.DropdownCombo">
      <summary>
            Includes a text box and a dropdown list. The user can select from the list or type in the text box.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ComboStyleEnum.SimpleCombo">
      <summary>
            Includes a text box and a list which is always displayed. 
            The user can select from the list or type in the box. 
            The size of a simple combo box includes both the text and list portions. 
            By default, a simple combo box is sized so that none of the list is displayed. 
            To display more of the list, resize the control at design time or increase its 
            Height property at run time.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ComboStyleEnum.DropdownList">
      <summary>
            Includes a text box and dropdown list as in setting 0 ¨C Dropdown Combo. 
            However, this style disallows text entry and permits selection only from the dropdown list.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.DropdownPositionEnum">
      <summary>
            Defines the position of the drop down list.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.DropdownPositionEnum.DefaultPosition">
      <summary>
            The dropdown list will open to the right and down.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.DropdownPositionEnum.RightDown">
      <summary>
            The dropdown list will open to the right and down.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.DropdownPositionEnum.RightUp">
      <summary>
            The dropdown list will open to the right and up.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.DropdownPositionEnum.LeftDown">
      <summary>
            The dropdown list will open to the left and down.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.DropdownPositionEnum.LeftUp">
      <summary>
            The dropdown list will open to the left and up.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.DropDownList">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.C1List">
      <summary>
            A multicolumn list box control.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ListBase.C1ListBase">
      <summary>
            The abstract base class for C1List.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.#ctor">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.Dispose(System.Boolean)">
      <summary>
            Clean up any resources being used.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.EndInit">
      <summary>
            Signals the object that initialization is complete.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.InitializeStyles">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.SetDataSource(System.Object,System.Windows.Forms.BindingMemberInfo,System.Boolean)">
      <summary>
      </summary>
      <param name="datasource">
      </param>
      <param name="newDisplayMember">
      </param>
      <param name="force">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ShouldSerializeColumnCaptionHeight">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ShouldSerializeColumnFooterHeight">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ShouldSerializeItemHeight">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.AddCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style)">
      <summary>
            Controls the font and color of cells within a list, column, or split according to value.
            </summary>
      <param name="condition">Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> constants</param>
      <param name="style">
        <see cref="T:C1.Win.C1List.Style" /> object that specifies font and color attributes.</param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.AddRegexCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style,System.String)">
      <summary>
            Controls the font and color of cells within a list, column, or split according to their contents.
            </summary>
      <param name="condition">Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> constants</param>
      <param name="style">
        <see cref="T:C1.Win.C1List.Style" /> object that specifies font and color attributes.</param>
      <param name="regex"> A regular expression string.</param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ClearRegexCellStyle(C1.Win.C1List.CellStyleFlag)">
      <summary>
            Removes a cell condition established with a previous call to the <see cref="M:C1.Win.C1List.ListBase.C1ListBase.AddRegexCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style,System.String)" /> method.
            </summary>
      <param name="condition">
            Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> enumerations.
            </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ClearRegexCellStyle(C1.Win.C1List.CellStyleFlag,System.String)">
      <summary>
            Removes a cell condition established with a previous call to the <see cref="M:C1.Win.C1List.ListBase.C1ListBase.AddRegexCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style,System.String)" /> method.
            </summary>
      <param name="condition">
            Combination of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> enumerations.
            </param>
      <param name="regex">
            A regular expression string.
            </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.AddItemTitles(System.String)">
      <summary>
            Sets the column titles in the <see cref="M:C1.Win.C1List.ListBase.C1ListBase.AddItem(System.String)" /> mode.
            </summary>
      <param name="titles">
            The titles to be set. It is delimited by the semicolon (;).
            </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.AddItem(System.String)">
      <summary>
            Add a new item to the end in the <see cref="M:C1.Win.C1List.ListBase.C1ListBase.AddItem(System.String)" /> mode.
            </summary>
      <param name="newItem">The item to be added. It is delimited by the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.AddItemSeparator" />.</param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.InsertItem(System.String,System.Int32)">
      <summary>
            Inserts a new item at the index position in the <see cref="M:C1.Win.C1List.ListBase.C1ListBase.AddItem(System.String)" /> mode.
            </summary>
      <param name="item">The item to be added. It is delimited by the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.AddItemSeparator" />.</param>
      <param name="index">The position to insert.</param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.RemoveItem(System.Int32)">
      <summary>
            Removes the item at the given position in the <see cref="M:C1.Win.C1List.ListBase.C1ListBase.AddItem(System.String)" /> mode.
            </summary>
      <param name="index">Item index.</param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ClearItems">
      <summary>
            Clear all of the items in the <see cref="M:C1.Win.C1List.ListBase.C1ListBase.AddItem(System.String)" /> mode.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.SuspendBinding">
      <summary>
            Temporary suspension of data binding notifications from the datasource.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ResumeBinding">
      <summary>
            Resumes data binding notification from the datasource.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.CellContaining(System.Int32,System.Int32,System.Int32@,System.Int32@)">
      <summary>
            Returns the cell position for a set of coordinates.
            </summary>
      <param name="x">Integer that defines the x coordinate in pixels.</param>
      <param name="y">Integer that defines the y coordinate in pixels.</param>
      <param name="rowAt">Integer that receives the zero-based index of the row beneath the specified y coordinate.</param>
      <param name="colAt">Integer that receives the zero-based index of the column beneath the specified y coordinate.</param>
      <value>A boolean that indicates whether a data cell is beneath the specified coordinate pair.</value>
      <remarks>
            The CellContaining method combines the <see cref="M:C1.Win.C1List.ListBase.C1ListBase.ColContaining(System.Int32)" /> and <see cref="M:C1.Win.C1List.ListBase.C1ListBase.RowContaining(System.Int32)" /> methods into one call. 
            If the coordinate pair specified by x and y points to a data cell, this method returns True, and the rowindex and colindex arguments receive zero-based indexes that identify the cell.
            </remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.SetAddItemData(System.Int32,System.Int32,System.String)">
      <summary>
            Updates the cell data for the AddItem mode.
            </summary>
      <param name="row">The row index.</param>
      <param name="col">The column index.</param>
      <param name="data">The new data for the cell.</param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.CaptureImage">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ClearCellStyle(C1.Win.C1List.CellStyleFlag)">
      <summary>
            Removes a cell condition established with a previous call to the <see cref="M:C1.Win.C1List.ListBase.C1ListBase.AddCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style)" /> method.
            </summary>
      <remarks>
        <para>
            The ClearCellStyle method removes a cell condition established with a previous call to the 
            <see cref="M:C1.Win.C1List.ListBase.C1ListBase.AddCellStyle(C1.Win.C1List.CellStyleFlag,C1.Win.C1List.Style)" /> method for the object in question. If no such cell condition exists, 
            then calling this method has no effect. 
            </para>
        <para>
            If the condition argument is CellStyleFlag.AllCells, then all non-regex cell conditions are removed, regardless of status.
            </para>
      </remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ClearSelCols">
      <summary>
            Deselects all selected columns in the current split.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ClearSelected">
      <summary>
            Clears all selected rows.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ColContaining(System.Int32)">
      <summary>
            Returns the column index containing the specified coordinate.
            </summary>
      <param name="x">Integer that defines a horizontal coordinate (X value) in pixels.</param>
      <value>
            An integer corresponding to the index of the column beneath the specified X coordinate.
            </value>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.GetDisplayText(System.Int32,System.Int32)">
      <summary>
            Gets the display text for the specified row and column.
            </summary>
      <param name="rowIndex">The row index.</param>
      <param name="colIndex">The column index.</param>
      <value>
            Returns a string which identifies the cell text.
            </value>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.GetItemText(System.Int32,System.Int32)">
      <summary>
            Gets the cell text for any cell.
            </summary>
      <param name="rowIndex">The row index.</param>
      <param name="colIndex">The column index.</param>
      <value>
            Returns a string which identifies the cell text.
            </value>
      <remarks>
            This method will get the text for any cell directly, instead of using the Column object.
            </remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.GetItemText(System.Int32,System.String)">
      <summary>
            Gets the cell text for any cell.
            </summary>
      <param name="rowIndex">The row index.</param>
      <param name="colName">The column name..</param>
      <value>
            Returns a string which identifies the cell text.
            </value>
      <remarks>
            This method will get the text for any cell directly, instead of using the Column object.
            </remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.Sort(System.Int32,C1.Win.C1List.SortDirEnum)">
      <summary>
            Sorts a specific column for the control.
            </summary>
      <param name="col">The column number identifying the column to sort.</param>
      <param name="dir">The sort direction.</param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.Sort(System.String,C1.Win.C1List.SortDirEnum)">
      <summary>
            Sorts a specific column for the control.
            </summary>
      <param name="colName">The column name identifying the column to sort.</param>
      <param name="dir">The sort direction.</param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnAfterSort(C1.Win.C1List.FilterEventArgs)">
      <summary>
            Raises the AfterSort event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.HoldFields">
      <summary>
            Holds the current column/field layout.
            </summary>
      <remarks>
        <para>
            The HoldFields method sets the current column/field layout as the customized layout so that subsequent 
            <see cref="M:C1.Win.C1List.ListBase.C1ListBase.Rebind" /> operations will use the current layout for display. 
            You can resume the list's automatic layout behavior by invoking the <see cref="M:C1.Win.C1List.ListBase.Frame.ClearFields" /> method.
            </para>
        <para>
            The HoldFields method is especially useful in the unbound modes when you have specified the column layout 
            in code and would like to keep it intact after a <see cref="M:C1.Win.C1List.ListBase.C1ListBase.Rebind" /> operation. 
            </para>
      </remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.PointAt(System.Int32,System.Int32)">
      <summary>
            Returns one of the <see cref="T:C1.Win.C1List.PointAtEnum" /> constants, which indicates the kind of list element beneath the specified coordinate pair.
            </summary>
      <param name="x">The x coordinate in pixels.</param>
      <param name="y">The y coordinate in pixels.</param>
      <remarks>
        <para>
            The PointAt method returns one of the constants defined in <see cref="T:C1.Win.C1List.PointAtEnum" />, which indicates the kind of control element beneath the specified coordinate pair.
            </para>
      </remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.RowTop(System.Int32)">
      <summary>
            Returns the Y coordinate of the top of a visible row.
            </summary>
      <param name="row">The row index.</param>
      <value>
            The Y position of the specified display row, based on the coordinate system of the list's container. 
            </value>
      <remarks>
        <para>
            The RowTop method returns the Y coordinate of the top of a visible row relative to the top of the list, as given by the list's Top property.
            </para>
        <para>
            Use the RowTop method in conjunction with ItemHeight, Left, and Width to determine the size and placement of controls displayed on top of a list cell. 
            </para>
      </remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.Rebind">
      <summary>
            Re-establishes the connection with the bound data source.
            </summary>
      <remarks>
        <para>
            This method re-establishes the connection with the bound data source, 
            causing the control to perform the same operations that occur when you set the DataSource property at design time. 
            </para>
        <para>
            If you have not modified the list columns at design time, then executing the ReBind method will reset the columns, headings, and other properties based on the current data source. 
            </para>
        <para>
            To force the list to reset the column bindings even if the columns were modified at design time, 
            invoke the <see cref="M:C1.Win.C1List.ListBase.Frame.ClearFields" /> method immediately before ReBind. 
            Conversely, to cancel the list's automatic layout response and force the list to use the current 
            column/field layout, invoke the <see cref="M:C1.Win.C1List.ListBase.C1ListBase.HoldFields" /> method immediately before ReBind.
            </para>
      </remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.Rebind(System.Boolean)">
      <summary>
            Reinitializes list with data from its data source.
            </summary>
      <param name="holdFields">
            True to preserves current column layout.  False retrieves the schema from the datasource.
            </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.RowContaining(System.Int32)">
      <summary>
            Returns the zero-based index of the display row containing the specified coordinate.
            </summary>
      <param name="y">The y coordinate in pixels.</param>
      <value>An integer corresponding to the display row beneath the specified Y coordinate.</value>
      <remarks>
        <para>
            The RowContaining method returns the zero-based index of the display row containing the specified coordinate. This value ranges from 0 to VisibleRows - 1. 
            </para>
        <para>
            If y is outside of the list's data area, this method returns -1.
            </para>
      </remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.IndexFromPoint(System.Drawing.Point)">
      <summary>
            Returns the row index for a given point.
            </summary>
      <param name="p">The given point.</param>
      <returns>The row index.</returns>
      <remarks>If the point is not on any row, then it will return ¨C1. Otherwise it will return the row index.</remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.SplitContaining(System.Int32,System.Int32)">
      <summary>
            Returns the Index value of the split containing the specified coordinate pair.
            </summary>
      <param name="x">The x coordinate in pixels.</param>
      <param name="y">The y coordinate in pixels.</param>
      <returns>An integer corresponding to the index of the split beneath the specified coordinate pair.</returns>
      <remarks>
        <para>
            The returned value ranges from 0 to Split.Count ¨C 1.
            </para>
        <para>
            If either argument is outside of the list's data area, this method returns -1.
            </para>
      </remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ScrollCtl(System.Int32,System.Int32)">
      <summary>
            Scrolls the list data area by the specified number of rows and columns.
            </summary>
      <param name="rows">The number of columns to scroll.</param>
      <param name="cols">The number of rows to scroll.</param>
      <remarks>
        <para>
            Positive offsets scroll right and down. Negative offsets scroll left and up. Column offsets that are out of range cause a trappable error. Row offsets that are out of range scroll to the beginning or end of the database. 
            </para>
        <para>
            The same effect can be achieved by setting the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.LeftCol" /> and <see cref="P:C1.Win.C1List.ListBase.C1ListBase.TopIndex" /> properties, but these must be set independently. 
            </para>
      </remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ExportToDelimitedFile(System.String,C1.Win.C1List.RowSelectorEnum)">
      <summary>
            Exports the specified rows from the list to the specified file as delimited ASCII text.
            </summary>
      <param name="outPath">Specifies the file to which list rows are exported.</param>
      <param name="selector">Specifies an optional value that specifies the rows to be exported.</param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ExportToDelimitedFile(System.String,C1.Win.C1List.RowSelectorEnum,System.String)">
      <summary>
            Exports the specified rows from the list to the specified file as delimited ASCII text.
            </summary>
      <param name="outPath">Specifies the file to which list rows are exported.</param>
      <param name="selector">Specifies an optional value that specifies the rows to be exported.</param>
      <param name="delim">Specifies an optional delimiter string used to separate fields.</param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ExportToDelimitedFile(System.String,C1.Win.C1List.RowSelectorEnum,System.String,System.String)">
      <summary>
            Exports the specified rows from the list to the specified file as delimited ASCII text.
            </summary>
      <param name="outPath">Specifies the file to which list rows are exported.</param>
      <param name="selector">Specifies an optional value that specifies the rows to be exported.</param>
      <param name="delim">Specifies an optional delimiter string used to separate fields.</param>
      <param name="prefix">Specifies an optional string used with suffix to surround each value.</param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ExportToDelimitedFile(System.String,C1.Win.C1List.RowSelectorEnum,System.String,System.String,System.String)">
      <summary>
            Exports the specified rows from the list to the specified file as delimited ASCII text.
            </summary>
      <param name="outPath">Specifies the file to which list rows are exported.</param>
      <param name="selector">Specifies an optional value that specifies the rows to be exported.</param>
      <param name="delim">Specifies an optional delimiter string used to separate fields.</param>
      <param name="prefix">Specifies an optional string used with suffix to surround each value.</param>
      <param name="suffix">Specifies an optional string used with prefix to surround each value.</param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.ExportToDelimitedFile(System.String,C1.Win.C1List.RowSelectorEnum,System.String,System.String,System.String,System.Boolean)">
      <summary>
            Exports the specified rows from the list to the specified file as delimited ASCII text.
            </summary>
      <param name="outPath">Specifies the file to which list rows are exported.</param>
      <param name="selector">Specifies an optional value that specifies the rows to be exported.</param>
      <param name="delim">Specifies an optional delimiter string used to separate fields.</param>
      <param name="prefix">Specifies an optional string used with suffix to surround each value.</param>
      <param name="suffix">Specifies an optional string used with prefix to surround each value.</param>
      <param name="headers">Specifies whether the columns headers will be written to the file on the first line.</param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.InsertVerticalSplit(System.Int32)">
      <summary>
            Inserts a vertical split at the specified index.
            </summary>
      <param name="idx">A zero-based integer that identifies the position of the new split.</param>
      <remarks>By default the list has one vertical split.</remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.RemoveVerticalSplit(System.Int32)">
      <summary>
            Removes a vertical split at the specified index.
            </summary>
      <param name="idx">A zero-based integer that identifies the position of the split to be removed.</param>
      <remarks>By default the list has one vertical split.</remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.InsertHorizontalSplit(System.Int32)">
      <summary>
            Inserts a horizontal split at the specified index.
            </summary>
      <param name="idx">A zero-based integer that identifies the position of the new split.</param>
      <remarks>By default the list has one horizontal split.</remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.RemoveHorizontalSplit(System.Int32)">
      <summary>
            Removes a horizontal split at the specified index.
            </summary>
      <param name="idx">A zero-based integer that identifies the position of the split to be removed.</param>
      <remarks>By default the list has one horizontal split.</remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnHeadClick(C1.Win.C1List.ColEventArgs)">
      <summary>
            Raises the HeadClick event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnFootClick(C1.Win.C1List.ColEventArgs)">
      <summary>
            Raises the FootClick event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnColMove(C1.Win.C1List.ColMoveEventArgs)">
      <summary>
            Raises the ColMoveEvent.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnColSelected(C1.Win.C1List.ColEventArgs)">
      <summary>
            Raises the ColSelected event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnColResize(C1.Win.C1List.ColResizeEventArgs)">
      <summary>
            Raises the ColResize event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnRowResize(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raises the RowResize event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnScroll1(System.ComponentModel.CancelEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnRowChange(System.EventArgs)">
      <summary>
            Raises the RowChange event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnRowColChange(C1.Win.C1List.RowColChangeEventArgs)">
      <summary>
            Raises the RowColChange event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnTopIndexChange(C1.Win.C1List.SplitEventArgs)">
      <summary>
            Raises the TopIndexChange event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnLeftColChange(C1.Win.C1List.SplitEventArgs)">
      <summary>
            Raises the LeftColChange event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnSelChange(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raises the SelChang event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnSplitChange(System.EventArgs)">
      <summary>
            Raises the SplitChange event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnSplitRemoved(System.EventArgs)">
      <summary>
            Raised when horizontal or vertical splits are removed.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnDisplayMemberChanged(System.EventArgs)">
      <summary>
            Raises the DisplayMemberChanged event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnDataSourceChanged(System.EventArgs)">
      <summary>
            Raises the DataSourceChanged event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnValueMemberChanged(System.EventArgs)">
      <summary>
            Raises the ValueMemberChanged event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnSelectedValueChanged(System.EventArgs)">
      <summary>
            Raises the SelectedValueChanged event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnFetchRowStyle(C1.Win.C1List.FetchRowStyleEventArgs)">
      <summary>
            Raises the FetchRowStyle event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnUnboundColumnFetch(C1.Win.C1List.UnboundColumnFetchEventArgs)">
      <summary>
            Raises the UnboundColumnFetch event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnFetchCellStyle(C1.Win.C1List.FetchCellStyleEventArgs)">
      <summary>
            Raises the FetchCellStyle event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnFetchCellTips(C1.Win.C1List.FetchCellTipsEventArgs)">
      <summary>
            Raises the FetchCellTips event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnFormatText(C1.Win.C1List.FormatTextEventArgs)">
      <summary>
             Raises the FromatText event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnError(C1.Win.C1List.ErrorEventArgs)">
      <summary>
            Raises the Error event
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnOwnerDrawCell(C1.Win.C1List.OwnerDrawCellEventArgs)">
      <summary>
            Raises the OwnerDrawCell event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnLayoutReady(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnFetchScrollTips(C1.Win.C1List.FetchScrollTipsEventArgs)">
      <summary>
            Raises the FetchScrollTips event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.GetSelected(System.Int32)">
      <summary>
            Checks whether the given row is selected.
            </summary>
      <remarks>
            If it returns True, then the row is selected. Otherwise, it is not selected.
            </remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.SetSelected(System.Int32,System.Boolean)">
      <summary>
            Selects or unselects a row from the code.
            </summary>
      <param name="index">The zero-based integer that identifies the row number of the control.</param>
      <param name="value">The boolean value indicates the status of the selection.</param>
      <remarks>
            SetSelected(index, True) will select the row if it is not selected. SetSelected(index, False) will unselect the row if it is selected.
            </remarks>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.ListBase.C1ListBase.canCreateSplits">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.AllowHorizontalSplit">
      <summary>
            Specifies if a user is allowed to create horizontal splits.
            </summary>
      <value>The default value is false.</value>
      <remarks>
        <para>
            If True, then the user is able to split the current list into multiple horizontal splits at run-time.
            </para>
        <para>
            If False, then the user is prevented from splitting the list horizontally.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.AllowVerticalSplit">
      <summary>
            Specifies if a user is allowed to create vertical splits.
            </summary>
      <value>The default value is false.</value>
      <remarks>
        <para>
            If True, then the user is able to create multiple vertical splits in the list.
            </para>
        <para>
            If False, then the user is restricted from creating vertical splits.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.AllowRowSizing">
      <summary>
            Specifies if a user is allowed to resize the rows in the list.
            </summary>
      <remarks>
        <para>
            This property determines whether a user is allowed to resize rows individually, all together, or not at all.
            </para>
        <para>
            This property only works when the SelectionMode property is set to CheckBox.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.AllowColMove">
      <summary>
            Enables/disables interactive column movement.
            </summary>
      <value>The default value is true.</value>
      <remarks>
        <para>
            If True (the default), the user can move columns. 
            </para>
        <para>
            If False, the user cannot move columns. 
            </para>
        <para>
            Use the AllowColMove property to control whether the user can move columns by dragging the column header to 
            the desired location. Any change in column order causes a <see cref="E:C1.Win.C1List.ListBase.C1ListBase.ColMove" /> event. 
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.AlternatingRows">
      <summary>
            Determines if a list or split displays odd-numbered rows in one style and even-numbered rows in another.
            </summary>
      <value>The default value is false.</value>
      <remarks>
        <para>
            If True, the OddRowStyle and EvenRowStyle properties control the appearance of rows within the specified object.
            </para>
        <para>
            If False (the default), the Style property controls the display of rows within the specified object.
            </para>
        <para>
            At design time, you can change the colors and fonts used to render odd (even) rows by modifying the built-in OddRow (EvenRow) style using the Styles property page.
            </para>
        <para>
            At run time, you can change the colors and fonts used to render odd (even) rows by modifying the Style object returned by the OddRowStyle (EvenRowStyle) property.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.AddItemSeparator">
      <summary>
            Determines the separation string for columns when using the AddItem method in AddItem mode.
            </summary>
      <remarks>
            This property takes ; as the default value.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.Bookmark">
      <summary>
            Returns or sets the bookmark identifying the current row in the list.
            </summary>
      <remarks>
        <para>
            Use the value returned by the Bookmark property to save a reference to the current row that remains valid even after another row becomes current. 
            </para>
        <para>
            When you set the Bookmark property to a valid value in code, the row associated with that value becomes the current row, and the list adjusts its display to bring the new current row into view if necessary.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.CaptionStyle">
      <summary>
            Returns or sets the <see cref="T:C1.Win.C1List.Style" /> object that controls the appearance of the caption area.
            </summary>
      <remarks>
            This property sets or returns the Style object that controls the appearance of a C1List control's caption bar or a Split object's heading area. By default, this is the built-in Caption style.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.CellTips">
      <summary>
            Determines whether the list displays a pop-up text window when the cursor is idle.
            </summary>
      <remarks>
        <para>
            The CellTips property determines whether the list displays a pop-up text window when the cursor is idle.  
            By default, this property is set to NoCellTips, and cell tips are not displayed.
            </para>
        <para>
            If the CellTips property is set to either Anchored or Floating, 
            the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.FetchCellTips" /> event will be fired whenever the list has focus and the cursor is 
            idle over a list cell, record selector, column header, split header, or list caption. The event will not fire if 
            the cursor is over the scroll bars.
            </para>
        <para>
            The setting Anchored aligns the cell tip window with either the left or right edge of the cell. 
            The left edge is favored, but the right edge will be used if necessary in order to display as much text as possible.
            </para>
        <para>
            The setting Floating displays the cell tip window below the cursor, if possible.
            </para>
        <para>
            If you do not provide a handler for the FetchCellTips event, and the cursor is over a list cell, 
            the default behavior is to display a text box containing the cell's contents (up to 256 characters). 
            This enables the user to peruse the contents of a cell even if it is not big enough to be displayed in its entirety. 
            You can also program the FetchCellTips event to override the default cell text display in order to provide users with 
            context-sensitive help.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.CellTipsDelay">
      <summary>
            Determines the amount of time before the cell tip window is displayed.
            </summary>
      <value>
            The default value is 500 milliseconds.
            </value>
      <remarks>
        <para>
            Setting this property to zero does not disable cell tips, but restores the default value of 500. 
            To disable cell tips, set the CellTips property to NoCellTips.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.CellTipsWidth">
      <summary>
            Returns or sets the width of the cell tip window.
            </summary>
      <remarks>
        <para>
            The CellTipsWidth property returns or sets the width of the cell tip window in terms of the coordinate system of the list's container.
            </para>
        <para>
            By default, this property is set to zero, which causes the cell tip window to grow or shrink to accommodate the cell tip text. You can override this behavior and give the cell tip window a fixed width by specifying a non-zero value for this property.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.Col">
      <summary>
            Gets or sets the column position of the current cell in the current split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.ColumnFooters">
      <summary>
            Specifies whether footers are displayed.
            </summary>
      <value>The default value is false.</value>
      <remarks>
            Use the FooterText property to set the footing text of an individual column.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.ColumnHeaders">
      <summary>
            Specifies whether headers are displayed.
            </summary>
      <value>The default value is true.</value>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.ColumnCaptionHeight">
      <summary>
            Returns or sets the height of the column headers.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.ColumnFooterHeight">
      <summary>
            Returns or sets the height of the column footers.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.Columns">
      <summary>
            Returns a collection of <see cref="T:C1.Win.C1List.C1DataColumn" /> objects.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.CurrentCellVisible">
      <summary>
            Gets or sets a value indicating the visibility of the current cell in a split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.DataMode">
      <summary>
            Specifies the normal or additem mode.
            </summary>
      <remarks>
            Please see the <see cref="T:C1.Win.C1List.DataModeEnum" /> for details.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.DataView">
      <summary>
            Returns or sets the method by which the list will display data.
            </summary>
      <remarks>
            Please see <see cref="T:C1.Win.C1List.DataViewEnum" /> for details.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.DeadAreaBackColor">
      <summary>
            Controls the background color of the dead area in the list.
            </summary>
      <remarks>
        <para>
            Setting this property will affect the appearance of the "dead area" of under populated controls, such as the area to the right of the last column, the area below the last data row, and the Outlook Grouping area.
            </para>
        <para>
            If the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.EmptyRows" /> property is True, the empty data rows it creates are not considered part of the "dead area" 
            and will not be displayed in the DeadAreaBackColor.  If you wish for the area occupied by the empty rows to be 
            shown in the DeadAreaBackColor, you should first set the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.EmptyRows" /> property to False.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.DefColWidth">
      <summary>
            Specifies column width for auto-created columns.
            </summary>
      <remarks>
        <para>
            Setting the DefColWidth property at run time does not affect existing columns, only those that are subsequently created in code. 
            </para>
        <para>
            In bound mode, some data sources do not provide text field widths when requested by the list. Therefore, if DefColWidth is 0, the actual column widths may not be what you expect since the list must supply a default width.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.DestinationCol">
      <summary>
            Indicates which list column will be the destination after cell movement.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.DestinationRow">
      <summary>
            Indicates which list row will be the destination after cell movement.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.DestinationSplit">
      <summary>
            Indicates which list split will be the destination after cell movement.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.EmptyRows">
      <summary>
            Returns or sets a value that determines how the list displays rows below the last data row.
            </summary>
      <value>The default value is false.</value>
      <remarks>
            If all of the records in the data source do not fill up the entire list, setting EmptyRows to True will fill the unpopulated list area with empty data rows. If EmptyRows is False (the default), then the unpopulated list area will be blank and will be filled with the system 3D Objects color (or the system Button Face color) as determined by your Control Panel settings. 
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.EvenRowStyle">
      <summary>
            Returns or sets the Style object that controls the appearance of an even-numbered row.
            </summary>
      <remarks>
        <para>
            This property sets or returns the Style object that controls the appearance of an even-numbered row in a 
            list or split where the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.AlternatingRows" /> property is set to True. By default, this is the built-in EvenRow style.
            </para>
        <para>
            To change the appearance of odd-numbered rows, set the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.OddRowStyle" /> property. 
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.RightToLeft">
      <summary>
            When True, applies right to left text functionality.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.ExtendRightColumn">
      <summary>
            Returns or sets whether the last column will extend to fill the dead area of the list.
            </summary>
      <value>The default value is false.</value>
      <remarks>
            If the columns in the list don¡¯t take up all of the space available, then there is an area which is exposed.  
            This area is called the dead area and has a default back color of gray.  
            This property sets whether this area is to be filled with the last column of the list.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.FetchRowStyles">
      <summary>
            Specifies whether the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.FetchRowStyle" /> event will be fired.
            </summary>
      <value>The default value is false.</value>
      <remarks>
        <para>
            If True, the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.FetchRowStyle" /> event will be fired whenever the list is about to display a row of data.
            </para>
        <para>
            If False (the default), the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.FetchRowStyle" /> event is not fired.
            </para>
        <para>
            Set this value to True when you need to perform complex per-row formatting operations that can only be done 
            using the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.FetchRowStyle" /> event. For example, if you want to apply fonts and/or colors to all rows that satisfy 
            certain criteria, then you need to set the FetchRowStyles property to True and write code for the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.FetchRowStyle" /> event.
            </para>
        <para>
            To display every other row in a different color or font, you can simply set the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.AlternatingRows" /> property to True.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.TopIndex">
      <summary>
            Returns or sets a value containing the bookmark for the first visible row in a list or split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.ColumnWidth">
      <summary>
            Returns or sets the column width for each column in the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.FlatStyle">
      <summary>
            Determines the appearance of the list.
            </summary>
      <remarks>
            Please see <see cref="T:C1.Win.C1List.FlatModeEnum" /> for more details.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.FooterStyle">
      <summary>
            Returns the Style object that controls the appearance of column footers.
            </summary>
      <remarks>
            By default, this is the built-in Footing style.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.HeadingStyle">
      <summary>
            Returns the Style object that controls the appearance of column heading.
            </summary>
      <remarks>
            This property returns the <see cref="T:C1.Win.C1List.Style" /> object that controls the appearance of column headings within a list, column, or split. 
            By default, this is the built-in Heading style. 
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.HighLightRowStyle">
      <summary>
            Returns the <see cref="T:C1.Win.C1List.Style" /> object that controls the appearance of hight light row.
            </summary>
      <remarks>
            This property returns the <see cref="T:C1.Win.C1List.Style" /> object that controls the appearance of ahighlighted row.
            By default, this is the built-in HighlightRow style. 
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.HScrollBar">
      <summary>
            Returns the <see cref="T:C1.Win.C1List.Util.HBar" /> object that controls the appearance of the horizontal scrollbar.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.VScrollBar">
      <summary>
            Returns or sets the <see cref="T:C1.Win.C1List.Util.VBar" /> object that controls the appearance of the vertical scrollbar.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.IntegralHeight">
      <summary>
            Controls whether partial rows are displayed.
            </summary>
      <value>The default value is false.</value>
      <remarks>
        <para>
            If True, partial rows are not displayed, and the height of the control will be reduced to eliminate the last partial row if necessary.
            </para>
        <para>
            If False, partial rows are displayed, and the control retains its design-time height.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.LeftCol">
      <summary>
            Returns or sets the zero-based index of the leftmost column in a list or split.
            </summary>
      <remarks>
            Use this property in code to scroll a list or split horizontally. Use the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.TopIndex" /> property to determine the bookmark of the first visible row in a list or split.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.DisplayMember">
      <summary>
            Returns or sets the DataSource field used for incremental search.
            </summary>
      <remarks>
            If the DisplayMember property is not specified, the first column in the control will be used for both incremental search and the selection value.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.SelectedValue">
      <summary>
            Gets or sets the value of the member property specified by the ValueMember property.
            </summary>
      <remarks>
            SelectedValue is bindable; the common usage is to bind it to a field in another DataSource.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.SelectedText">
      <summary>
            Gets or sets the text of the current selected row specified by the DisplayMember property.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.ValueMember">
      <summary>
            Specifies the field name for binding purposes.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.OddRowStyle">
      <summary>
            Returns or sets the <see cref="T:C1.Win.C1List.Style" /> object that controls the appearance of an odd-numbered row.
            </summary>
      <remarks>
        <para>
            This property sets or returns the <see cref="T:C1.Win.C1List.Style" /> object that controls the appearance of an odd-numbered 
            row in a list or split where the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.AlternatingRows" /> property is set to True. 
            By default, this is the built-in OddRow style.
            </para>
        <para>
            To change the appearance of even-numbered rows, set the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.EvenRowStyle" /> property.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.PartialRightColumn">
      <summary>
            True if rightmost column can be clipped to the edge of a split.
            </summary>
      <remarks>
        <para>
            If True (the default), the rightmost column will be clipped if the control or split is not wide enough to accommodate the entire column.
            </para>
        <para>
            If False, the rightmost column will not be clipped while other columns are visible. In this case, the rightmost column must be scrolled into view as the only visible column in the control or split.
            </para>
        <para>
            If a control contains multiple splits, then setting its PartialRightColumn property has the same effect as setting the PartialRightColumn property of each split individually.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.Row">
      <summary>
            Returns or sets the position of current list row relative to the first displayed row.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.RowDivider">
      <summary>
            Determines the style of the border drawn between list rows.
            </summary>
      <remarks>
            The Style property of RowDivider does not control whether rows can be resized by dragging their border. 
            Use the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.AllowRowSizing" /> property to control this behavior.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.RowSubDividerColor">
      <summary>
            Returns or sets the color of a RowSubDivider.
            </summary>
      <remarks>
            This property controls the row subdivider color of the list.  
            It is used when the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.DataView" /> is set to DataViewEnum.MultipleLines.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.ItemHeight">
      <summary>
            Returns or sets the height of all list rows.
            </summary>
      <remarks>
        <para>
            The default value depends upon the character height of the current font. 
            </para>
        <para>
            If the control's <see cref="P:C1.Win.C1List.ListBase.C1ListBase.AllowRowSizing" /> property is set to True 
            and the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.SelectionMode" /> 
            property is set to CheckBox, then the user can adjust the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.ItemHeight" /> property at run time 
            by dragging the row divider between any pair of checkboxes. 
            </para>
        <para>
        </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.FocusedSplit">
      <summary>
            Gets the Split that has focus.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.RowTracking">
      <summary>
            Controls whether rows are automatically highlighted as the mouse is moved over the C1List control.
            </summary>
      <value>The default value is true.</value>
      <remarks>
        <para>
            If True (the default), individual rows are highlighted as the mouse moves over them.
            </para>
        <para>
            If False, moving the mouse does not change the highlighted row.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.ScrollTips">
      <summary>
            Determines whether the list displays a pop-up text window when the scrollbar thumb is dragged.
            </summary>
      <value>The default value is false.</value>
      <remarks>
        <para>
            The ScrollTips property determines whether the list displays a pop-up text window when the scrollbar thumb is dragged. 
            By default, this property is set to False, and ScrollTips are not displayed.
            </para>
        <para>
            If the ScrollTips property is set to True, the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.FetchScrollTips" /> event will be fired whenever the list¡¯s scrollbar thumb is dragged. 
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.SelectedIndex">
      <summary>
            Returns or sets the bookmark of the currently selected item.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.WillChangeToIndex">
      <summary>
            Returns the bookmark identifying the will-be selected item in a control.
            </summary>
      <remarks>
            This property is used in the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.SelChange" /> event. 
            When you handle these events, the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.SelectedIndex" /> will be the old selected index, and the WillChangeToIndex will be the new selected index.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.WillChangeToText">
      <summary>
            Returns the string identifying the text for the item to be selected.
            </summary>
      <remarks>
        <para>
            This property will most likely be used during SelChange event, and it will give you the text of the new selected item. 
            It is related to <see cref="P:C1.Win.C1List.ListBase.C1ListBase.WillChangeToIndex" />, which refers to the new selected index.
            </para>
        <para>
            The WillChangeToText property returns an empty string when the <see cref="P:C1.Win.C1List.ListBase.C1ListBase.WillChangeToIndex" /> property is set to -1.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.WillChangeToValue">
      <summary>
            Returns the object identifying the value to be selected.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.SelectedIndices">
      <summary>
            Returns the <see cref="T:C1.Win.C1List.SelectedRowCollection" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.SelectedCols">
      <summary>
            Returns the <see cref="T:C1.Win.C1List.SelectedColumnCollection" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.SelectedStyle">
      <summary>
            Returns the <see cref="T:C1.Win.C1List.Style" /> object that controls the appearance of selected cells.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.Split">
      <summary>
            Returns current split number.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.Splits">
      <summary>
            Returns a Collection of <see cref="T:C1.Win.C1List.Split" /> objects.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.SplitIndex">
      <summary>
            Gets or sets the index of the current <see cref="P:C1.Win.C1List.ListBase.C1ListBase.Split" /> within the <see cref="T:C1.Win.C1List.SplitCollection" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.SpringMode">
      <summary>
            Specifies whether the columns will resize when the list is resized.
            </summary>
      <remarks>
        <para>
            When this property is set to True and the list is horizontally resized, the column widths for visible columns will expand and/or shrink proportionally.
            </para>
        <para>
            When set to False (the default), column widths do not change as the list is horizontally resized.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.Style">
      <summary>
            Returns or sets the normal Style object for the list.
            </summary>
      <remarks>
            This property returns or sets the <see cref="T:C1.Win.C1List.Style" /> object that controls the normal appearance of a cell within a list, column, or split. 
            By default, this is the built-in Normal style.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.Styles">
      <summary>
            Returns a collection of named Style objects.
            </summary>
      <remarks>
            This property returns a collection of named Style objects.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.ValueTranslate">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.VisibleCols">
      <summary>
            Returns the number of visible columns in the current split.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.VisibleRows">
      <summary>
            Returns the number of visible rows in the list.
            </summary>
      <remarks>
            The value returned includes both fully and partially displayed rows. 
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.ListCount">
      <summary>
            Returns the total row number for the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.AddItemCols">
      <summary>
            Returns or sets the column number for the AddItem mode.
            </summary>
      <remarks>
            You can set the AddItemCols property explicitly, or if it is not set (in that case, the AddItemCols is 0), 
            when you call <see cref="M:C1.Win.C1List.ListBase.C1ListBase.AddItem(System.String)" /> or <see cref="M:C1.Win.C1List.ListBase.C1ListBase.AddItemTitles(System.String)" />, the control will set the AddItemCols property for you.
            </remarks>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.AfterSort">
      <summary>
            Fires after a column is sorted by clicking the column header.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.ColHeadClick">
      <summary>
            Fires when the headers of the control is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.ColFootClick">
      <summary>
            Fires when the footer of the control is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.ColMove">
      <summary>
            Fires when the user has finished moving the selected columns.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.ColSelected">
      <summary>
            Fires when a column is selected.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.ColResize">
      <summary>
            Fires after the user has finished resizing a column.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.RowResize">
      <summary>
            Fires when the user has finished resizing a list row.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.OnScroll">
      <summary>
            Fires when the user scrolls the list horizontally or vertically using the scroll bars.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.RowChange">
      <summary>
            Fires when the user changes a row in the list.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.TopIndexChange">
      <summary>
            Fires when the first displayed row of a control or split is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.LeftColChange">
      <summary>
            Fires when the first visible column of a list or split is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.SelChange">
      <summary>
            Fires when the user selects a different range of rows or columns.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.SplitChange">
      <summary>
            Occurs whenever a Split changes focus.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.SplitRemoved">
      <summary>
            Occurs whenever horizontal or vertical splits are removed.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.DisplayMemberChanged">
      <summary>
            Occurs when the DisplayMember property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.DataSourceChanged">
      <summary>
            Fires when a bound data source is changed or requeried.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.ValueMemberChanged">
      <summary>
            Occurs when the ValueMember property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.SelectedValueChanged">
      <summary>
            Occurs when the SelectedValue property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.FetchRowStyle">
      <summary>
            Fires whenever the list is about to display a row of data and the FetchRowStyles property is True.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.UnboundColumnFetch">
      <summary>
            Fires when the control needs to display the value of a cell in an unbound column.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.FetchCellStyle">
      <summary>
            Fires when the list is about to display cell data in a column whose FetchStyle property is set to True.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.FetchCellTips">
      <summary>
            Fires when the control has focus and the cursor is idle for a small amount of time (defined by the CellTipsDelay property) over a data cell, column header, column footer, split header or control caption bar.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.FormatText">
      <summary>
            Fires when the list is about to display cell data in a column whose NumberFormat property is set.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.Error">
      <summary>
            Fires when a data access error occurs.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.OwnerDrawCell">
      <summary>
            Fires just before the cell is to be painted when the column's OwnerDraw is true.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.LayoutReady">
      <summary>
            Fires when asynchronous downloading of a control layout file has been completed.
            </summary>
    </member>
    <member name="E:C1.Win.C1List.ListBase.C1ListBase.FetchScrollTips">
      <summary>
            Fires whenever the list has focus and the scrollbar thumb is moved using the mouse.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ListBase.C1ListBase.SelectionMode">
      <summary>
            Returns or sets the type of selection allowed in the list.
            </summary>
      <remarks>
            Please see the <see cref="T:C1.Win.C1List.SelectionModeEnum" /> for more details.
            </remarks>
    </member>
    <member name="M:C1.Win.C1List.C1List.#ctor">
      <summary>
            Initialize a new instance of C1List control.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1List.#ctor(C1.Win.C1List.C1Combo)">
      <summary>
      </summary>
      <param name="owner">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1List.ExportToExcel(System.String)">
      <summary>
            Exports the grid to a XLS file.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1List.ExportToExcel(System.String,System.Boolean)">
      <summary>
            Exports the grid to a XLS file.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.C1List.CreateAccessibilityInstance">
      <summary>
            Creates a new accessibility object for the control.
            </summary>
      <returns>A new <see cref="T:System.Windows.Forms.AccessibleObject" /> for the control.</returns>
    </member>
    <member name="M:C1.Win.C1List.C1List.LoadLayout(System.String)">
      <summary>
            Loads a saved layout from the given file.
            </summary>
      <param name="filename">
            The file containing a saved layout.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1List.LoadLayout(System.IO.Stream)">
      <summary>
            Loads a saved layout from the given stream.
            </summary>
      <param name="stream">
            The Stream containing a saved layout.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1List.SaveLayout(System.IO.Stream)">
      <summary>
            Saves the grid's layout.
            </summary>
      <param name="stream">
            The Stream to contain the grid layout.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1List.SaveLayout(System.String,System.Boolean)">
      <summary>
            Saves the grid's layout.
            </summary>
      <param name="filename">
            File to contain the grid layout.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1List.SaveLayout(System.IO.Stream,System.Boolean)">
      <summary>
            Saves the grid's layout.
            </summary>
      <param name="stream">
            The Stream to contain the grid layout.
            </param>
    </member>
    <member name="M:C1.Win.C1List.C1List.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1List.OnGotFocus(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1List.OnClick(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1List.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1List.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1List.OnKeyPress(System.Windows.Forms.KeyPressEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.C1List.Find(System.String,C1.Win.C1List.MatchCompareEnum,System.Boolean,System.Int32,System.Int32)">
      <summary>
            Searches a string in a specific column.
            </summary>
      <param name="target">The target string.</param>
      <param name="searchMode">Comparation mode.</param>
      <param name="includeStart">Start search from the first row.</param>
      <param name="startBmk">The start row to search.</param>
      <param name="colIndex">The column index to search.</param>
    </member>
    <member name="M:C1.Win.C1List.C1List.Find(System.String,C1.Win.C1List.MatchCompareEnum,System.Boolean,System.Int32,System.String)">
      <summary>
            Searches a string in a specific column.
            </summary>
      <param name="target">The target string.</param>
      <param name="searchMode">Comparation mode.</param>
      <param name="includeStart">Start search from the first row.</param>
      <param name="startBmk">The start row to search.</param>
      <param name="fieldName">The column to search specified by the field name.</param>
    </member>
    <member name="M:C1.Win.C1List.C1List.FindString(System.String)">
      <summary>
            Finds the first row index such that the cell text starts with string s in the column specified by the DisplayMember property.
            </summary>
      <param name="s">The target string.</param>
    </member>
    <member name="M:C1.Win.C1List.C1List.FindString(System.String,System.Int32)">
      <summary>
            Finds the first row index after startIndex such that the cell text starts with string s in the column specified by the DisplayMember property.
            </summary>
      <param name="s">The target string.</param>
      <param name="startIndex">The row started.</param>
    </member>
    <member name="M:C1.Win.C1List.C1List.FindString(System.String,System.Int32,System.Int32)">
      <summary>
            Finds the first row index after startIndex such that the cell text starts with string s in the given column.
            </summary>
      <param name="s">The target string.</param>
      <param name="startIndex">The row started.</param>
      <param name="col">The index of column.</param>
    </member>
    <member name="M:C1.Win.C1List.C1List.FindString(System.String,System.Int32,System.String)">
      <summary>
            Finds the first row index after startIndex such that the cell text starts with string s in the given column.
            </summary>
      <param name="s">The target string.</param>
      <param name="startIndex">The row started.</param>
      <param name="field">The field name of the column.</param>
    </member>
    <member name="M:C1.Win.C1List.C1List.FindStringExact(System.String)">
      <summary>
            Finds the first row index such that the cell text is exactly same with string s in the column specified by the DisplayMember property.
            </summary>
      <param name="s">The target string.</param>
    </member>
    <member name="M:C1.Win.C1List.C1List.FindStringExact(System.String,System.Int32,System.Int32)">
      <summary>
            Finds the first row index after startIndex such that the cell text is exactly same with string s in the given column.
            </summary>
      <param name="s">The target string.</param>
      <param name="startIndex">The row started.</param>
      <param name="col">The index of column.</param>
    </member>
    <member name="M:C1.Win.C1List.C1List.FindStringExact(System.String,System.Int32,System.String)">
      <summary>
            Finds the first row index after startIndex such that the cell text is exactly same with string s in the given column.
            </summary>
      <param name="s">The target string.</param>
      <param name="startIndex">The row started.</param>
      <param name="field">The field name of the column.</param>
    </member>
    <member name="M:C1.Win.C1List.C1List.FindStringExact(System.String,System.Int32)">
      <summary>
            Finds the first row index after startIndex such that the cell text is exactly same with string s in the column specified by the DisplayMember property.
            </summary>
      <param name="s">The target string.</param>
      <param name="startIndex">The row started.</param>
    </member>
    <member name="P:C1.Win.C1List.C1List.PrintInfo">
      <summary>
            Gets the <see cref="P:C1.Win.C1List.C1List.PrintInfo" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1List.PreviewInfo">
      <summary>
            Gets the <see cref="T:C1.Win.C1List.PrintPreviewWinSettings" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1List.AllowColSelect">
      <summary>
            Enables/disables interactive column selection.
            </summary>
      <remarks>
            The default value is true.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.C1List.MatchEntry">
      <summary>
            Returns or sets a value indicating how the control performs searches based on user's input.
            </summary>
      <remarks>
            The default value is MatchEntryEnum.None.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.C1List.MatchCol">
      <summary>
            Sets the search column when the MatchEntry property is not None.
            </summary>
      <remarks>
            The default value is MatchColEnum.DisplayMember.
            </remarks>
    </member>
    <member name="P:C1.Win.C1List.C1List.MatchCompare">
      <summary>
            Returns or sets the comparing mode for a search.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.C1List.MatchEntryTimeout">
      <summary>
            Returns or sets a value indicating the timeout, in milliseconds, for incremental searching.
            </summary>
      <remarks>
            The default value is 2000 miliseconds.
            </remarks>
    </member>
    <member name="M:C1.Win.C1List.DropDownList.#ctor(C1.Win.C1List.C1Combo)">
      <summary>
            Initialize a new instance of DroDownList class.
            </summary>
      <param name="owner">
      </param>
    </member>
    <member name="M:C1.Win.C1List.DropDownList.GetService(System.Type)">
      <summary>
      </summary>
      <param name="service">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.DropDownList.OnChange(System.EventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.DropDownList.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="T:C1.Win.C1List.Design.SelectColumnActionUITypeEditor">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Design.SelectColumnActionUITypeEditor.#ctor">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Design.SelectColumnActionUITypeEditor.GetEditStyle(System.ComponentModel.ITypeDescriptorContext)">
      <summary>
      </summary>
      <param name="context">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Design.SelectColumnActionUITypeEditor.OnListBoxClick(System.Object,System.EventArgs)">
      <summary>
      </summary>
      <param name="sender">
      </param>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.Design.SelectColumnActionUITypeEditor.EditValue(System.ComponentModel.ITypeDescriptorContext,System.IServiceProvider,System.Object)">
      <summary>
      </summary>
      <param name="context">
      </param>
      <param name="provider">
      </param>
      <param name="value">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1List.Util.Xml.Serialization.IOnLongOpInProgressProvider">
      <summary>
            Interface used to provide feedback and the ability to cancel potentially long operations
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.IOnLongOpInProgressProvider.OnLongOpInProgress(System.Boolean@)">
      <summary>
      </summary>
      <param name="cancel">
      </param>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.IOnLongOpInProgressProvider.OnLongOpInProgress">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.Util.Xml.Serialization.LongOpInProgressArgs">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.Util.Xml.Serialization.LongOpInProgressArgs.CancelIfPossible">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.Util.Xml.Serialization.LongOpInProgressHandler">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.Util.Xml.Serialization.C1Stack">
      <summary>
            Simple stack with indexed access, based on System.Collections.ArrayList
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.C1Stack.Push(System.Object)">
      <summary>
      </summary>
      <param name="o">
      </param>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.C1Stack.Pop">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.C1Stack.Peek">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.C1Stack.IsIndexInBounds(System.Int32)">
      <summary>
      </summary>
      <param name="idx">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1List.Util.Xml.Serialization.C1Stack.Top">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.Util.Xml.Serialization.C1Stack.Length">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.Util.Xml.Serialization.C1Stack.IsEmpty">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.Util.Xml.Serialization.Pair">
      <summary>
            General purpose pair class. (Should be moved to gp utils.)
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Pair.#ctor">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Pair.#ctor(System.Object,System.Object)">
      <summary>
      </summary>
      <param name="first">
      </param>
      <param name="second">
      </param>
    </member>
    <member name="P:C1.Win.C1List.Util.Xml.Serialization.Pair.First">
      <summary>
      </summary>
    </member>
    <member name="P:C1.Win.C1List.Util.Xml.Serialization.Pair.Second">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.Design.PropBagAttribute">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.Design.LayoutAttribute">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.Util.Xml.Serialization.TypeSerialization">
      <summary>
            Determines how item types are serialized
            </summary>
    </member>
    <member name="F:C1.Win.C1List.Util.Xml.Serialization.TypeSerialization.None">
      <summary>
            Item type is not serialized (this is the default)
            </summary>
    </member>
    <member name="F:C1.Win.C1List.Util.Xml.Serialization.TypeSerialization.FullName">
      <summary>
            Item type is serialized as a fully qualified name
            </summary>
    </member>
    <member name="F:C1.Win.C1List.Util.Xml.Serialization.TypeSerialization.AssemblyQualifiedName">
      <summary>
            Item type is serialized as a fully qualified name and assembly name
            </summary>
    </member>
    <member name="F:C1.Win.C1List.Util.Xml.Serialization.TypeSerialization.Custom">
      <summary>
            Use owner's IMemberTypeEncoder for members
            (for collection items, this is done automatically).
            </summary>
    </member>
    <member name="T:C1.Win.C1List.Util.Xml.Serialization.MemberTypeEncoderHelper">
      <summary>
            Helper class for implementors of IMemberTypeEncoder
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.MemberTypeEncoderHelper.TypeToString(System.Object)">
      <summary>
      </summary>
      <param name="o">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.MemberTypeEncoderHelper.StringToType(System.String)">
      <summary>
      </summary>
      <param name="str">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1List.Util.Xml.Serialization.TypeSerializationAttribute">
      <summary>
            Forces the serializer to serialize the type name of a property or field
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.TypeSerializationAttribute.#ctor(C1.Win.C1List.Util.Xml.Serialization.TypeSerialization)">
      <summary>
      </summary>
      <param name="ts">
      </param>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.TypeSerializationAttribute.ToString">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1List.Util.Xml.Serialization.TypeSerializationAttribute.TypeSerialization">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.Util.Xml.Serialization.CollectionElementTypeAttribute">
      <summary>
            This attribute allows to specify collection's element type name
            (in that case there is no need to store it).
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.CollectionElementTypeAttribute.#ctor(System.Type)">
      <summary>
      </summary>
      <param name="type">
      </param>
    </member>
    <member name="P:C1.Win.C1List.Util.Xml.Serialization.CollectionElementTypeAttribute.CollectionElementType">
      <summary>
      </summary>
    </member>
    <member name="T:C1.Win.C1List.Util.Xml.Serialization.Serializer">
      <summary>
            Serializes and deserializes objects into and from XML.
            </summary>
      <remarks>
            All the methods in this class are static (Shared in Visual Basic).
            You cannot create an instance of this class.
            </remarks>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Serializer.Serialize(System.IO.Stream,System.Object,System.Xml.Serialization.XmlSerializerNamespaces,System.Xml.XmlTextWriter@)">
      <summary>
            Serializes the specified Object and writes the XML-document instance to a file using the specified Stream. 
            DOES NOT close the underlying stream. Useful for copying objects.
            Caller is responsible to call out writer.Close() to close writer and underlying stream.
            </summary>
      <param name="stream">The Stream used to write the XML-document instance</param>
      <param name="o">The Object to serialize</param>
      <param name="namespaces">The XmlSerializerNamespaces referenced by the object</param>
      <param name="writer">The XmlWriter object reference. Call writer.Close after working with stream/writer.</param>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Serializer.Serialize(System.IO.Stream,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>
            Serializes the specified Object and writes the XML-document instance to a file using the specified Stream.
            </summary>
      <param name="stream">The Stream used to write the XML-document instance</param>
      <param name="o">The Object to serialize</param>
      <param name="namespaces">The XmlSerializerNamespaces referenced by the object</param>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Serializer.Serialize(System.Xml.XmlTextWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>
            Serializes the specified Object and writes the XML-document instance to a file using the specified XmlWriter
            </summary>
      <param name="writer">The XmlWriter used to write the XML-document instance</param>
      <param name="o">The Object to serialize</param>
      <param name="namespaces">The XmlSerializerNamespaces referenced by the object</param>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Serializer.Serialize(System.IO.Stream,System.Object,System.Reflection.MemberInfo,System.Xml.Serialization.XmlSerializerNamespaces,System.Xml.XmlTextWriter@)">
      <summary>
            Serializes the specified Object and writes the XML-document instance to a file using the specified Stream. 
            DOES NOT close the underlying stream. Useful for copying objects.
            Caller is responsible to call out writer.Close() to close writer and underlying stream.
            </summary>
      <param name="stream">The Stream used to write the XML-document instance</param>
      <param name="o">The Object to serialize</param>
      <param name="mi">The FieldInfo or MemberInfo object context for the object to serialize</param>
      <param name="namespaces">The XmlSerializerNamespaces referenced by the object</param>
      <param name="writer">The XmlWriter object reference. Call writer.Close after working with stream/writer.</param>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Serializer.Serialize(System.IO.Stream,System.Object,System.Reflection.MemberInfo,System.Xml.Serialization.XmlSerializerNamespaces,System.Xml.XmlTextWriter@,System.Attribute[])">
      <summary>
      </summary>
      <param name="stream">
      </param>
      <param name="o">
      </param>
      <param name="mi">
      </param>
      <param name="namespaces">
      </param>
      <param name="writer">
      </param>
      <param name="filter">
      </param>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Serializer.Serialize(System.IO.Stream,System.Object,System.Reflection.MemberInfo,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>
            Serializes the specified Object and writes the XML-document instance to a file using the specified Stream.
            </summary>
      <param name="stream">The Stream used to write the XML-document instance</param>
      <param name="o">The Object to serialize</param>
      <param name="mi">The FieldInfo or MemberInfo object context for the object to serialize</param>
      <param name="namespaces">The XmlSerializerNamespaces referenced by the object</param>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Serializer.Serialize(System.Xml.XmlTextWriter,System.Object,System.Reflection.MemberInfo,System.Xml.Serialization.XmlSerializerNamespaces,System.Attribute[])">
      <summary>
            Serializes the specified Object and writes the XML-document instance to a file using the specified XmlWriter
            </summary>
      <param name="writer">The XmlWriter used to write the XML-document instance</param>
      <param name="o">The Object to serialize</param>
      <param name="mi">The FieldInfo or MemberInfo object context for the object to serialize</param>
      <param name="namespaces">The XmlSerializerNamespaces referenced by the object</param>
      <param name="filter">Array of attributes that specify what to serialize</param>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Serializer.Serialize(System.Xml.XmlTextWriter,System.Object,System.Reflection.MemberInfo,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>
            Serializes the specified Object and writes the XML-document instance to a file using the specified XmlWriter
            </summary>
      <param name="writer">The XmlWriter used to write the XML-document instance</param>
      <param name="o">The Object to serialize</param>
      <param name="mi">The FieldInfo or MemberInfo object context for the object to serialize</param>
      <param name="namespaces">The XmlSerializerNamespaces referenced by the object</param>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Serializer.Serialize(System.Object,System.Reflection.MemberInfo,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>
            Serializes the specified object to an XML formatted string.
            </summary>
      <param name="o">
      </param>
      <param name="mi">
      </param>
      <param name="namespaces">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Serializer.Deserialize(System.IO.Stream,System.Type)">
      <summary>
            Deserializes an XML-document instance
            </summary>
      <param name="stream">The Stream containing the XML-document instance to deserialize</param>
      <param name="type">The type of object being deserialized</param>
      <returns>The Object being deserialized</returns>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Serializer.Deserialize(System.Xml.XmlTextReader,System.Type)">
      <summary>
            Deserializes an XML-document instance
            </summary>
      <param name="reader">The XmlReader containing the XML-document instance to deserialize</param>
      <param name="type">The type of object being deserialized</param>
      <returns>The Object being deserialized</returns>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Serializer.Deserialize(System.Xml.XmlTextReader,System.Object,System.Type,System.Type)">
      <summary>
            Deserializes object
            </summary>
      <param name="reader">The XmlReader containing the XML-document instance to deserialize</param>
      <param name="o">The Object being deserialized</param>
      <param name="type">The type of object being deserialized</param>
      <param name="elementType">The type of array elements (in case the object is an array)</param>
      <returns>The Object being deserialized</returns>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Serializer.Deserialize(System.String,System.Type)">
      <summary>
            Deserializes an XML document string 
            </summary>
      <param name="XMLString">The XML string.</param>
      <param name="type">The type of the object.</param>
      <returns>The object from the string.</returns>
    </member>
    <member name="M:C1.Win.C1List.Util.Xml.Serialization.Serializer.DeserializeText(System.Xml.XmlTextReader,System.Type)">
      <summary>
            Deserializes text of element or attribute into object of appropriate type
            </summary>
      <param name="reader">The XmlReader containing the XML-document instance to deserialize</param>
      <param name="type">The type of object being deserialized</param>
      <returns>The Object being deserialized</returns>
    </member>
    <member name="P:C1.Win.C1List.Util.Xml.Serialization.Serializer.OnLongOpInProgressProvider">
      <summary>
            Returns or sets the object implementing IOnLongOpInProgressProvider interface
            (can be used to provide visual feedback to the user during serialization).
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Util.Xml.Serialization.Serializer.SerializeDefaultValues">
      <summary>
            Gets or sets a value indicating whether all the values are to be persistent.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Util.Xml.Serialization.Serializer.Formatting">
      <summary>
            Returns or sets formatting used by the XML writer.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Util.Xml.Serialization.Serializer.Indentation">
      <summary>
            Returns or sets indentation used by the XML writer.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Util.Xml.Serialization.Serializer.NonPublic">
      <summary>
            Returns or sets serialization of non-public properties.  If true non-public properties
            are included, but are hidden by default. Public properties are always visible by default.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.MatchEntryEnum">
      <summary>
            Determines how a C1List control performs searches based on user input.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchEntryEnum.None">
      <summary>
            The default. The control does not perform any incremental searches.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchEntryEnum.Standard">
      <summary>
            The search argument is limited to one character, and the control attempts to find 
            a match for the character entered using the first letter of entries in the list. 
            Repeatedly typing the same letter cycles through all of the entries in the list 
            beginning with that letter.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchEntryEnum.Extended">
      <summary>
            The control searches for an entry matching all characters entered. 
            The search is performed incrementally as characters are typed. 
            The search argument is cleared when the user presses Backspace or hesitates for a few seconds.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.MatchColEnum">
      <summary>
            Determines where the search is performed.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchColEnum.DisplayMember">
      <summary>
            Search the column of the DisplayMember.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchColEnum.CurrentMousePos">
      <summary>
            Search the column under the mouse cursor.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchColEnum.CurrentSelectedCol">
      <summary>
            Search the current selected column.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchColEnum.LeftVisibleCol">
      <summary>
            Search the left most visible column.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchColEnum.RightVisibleCol">
      <summary>
            Search the right most visible column.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchColEnum.AllCols">
      <summary>
            Search all the visible columns.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.MatchCompareEnum">
      <summary>
            The comparation methods while searching the column.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchCompareEnum.LessThan">
      <summary>
            Used to search for strings, numbers and dates that are less than the stated value.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchCompareEnum.LessThanOrEqual">
      <summary>
            Used to search for strings, numbers and dates that are less than or equal to the stated value.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchCompareEnum.Equal">
      <summary>
            Used to search for strings, numbers and dates that are equal to the stated value.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchCompareEnum.GreaterThanOrEqual">
      <summary>
            Used to search for strings, numbers and dates that are greater than or equal to the stated value.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchCompareEnum.GreaterThan">
      <summary>
            Used to search for strings, numbers and dates that are greater than the stated value.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchCompareEnum.PartiallyEqual">
      <summary>
            The PartiallyEqual is the default mode, which is the same as incremental search
            </summary>
    </member>
    <member name="F:C1.Win.C1List.MatchCompareEnum.IncludeEqual">
      <summary>
            Used to match a string inside another string.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.DataModeEnum">
      <summary>
            Specifies the normal or additem mode. 
            </summary>
    </member>
    <member name="F:C1.Win.C1List.DataModeEnum.Normal">
      <summary>
            The control displays data available from its bound DataSource.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.DataModeEnum.AddItem">
      <summary>
            The list portion of the control is populated with data via the AddItem method. 
            The first item added starts at zero. When in AddItem mode, 
            the DataSource and DataMember properties are ignored.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.SortDirEnum">
      <summary>
            Specifies the glyph used to denote a sort direction in the column header.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.SortDirEnum.None">
      <summary>
            Column is not sorted.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.SortDirEnum.ASC">
      <summary>
            Column is sorted in ascending order.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.SortDirEnum.DESC">
      <summary>
            Column is sorted in descending order.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.SelectionModeEnum">
      <summary>
            Specifies the UI behavior for selecting rows and columns.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.SelectionModeEnum.None">
      <summary>
            The user cannot select rows.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.SelectionModeEnum.One">
      <summary>
            Only one item in the list can be selected.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.SelectionModeEnum.MultiSimple">
      <summary>
            If the user clicks a row, that row is selected, and the SelectedIndex property returns a bookmark for that row.  
            If more than one row is selected, the SelectedIndex property returns the bookmark 
            of the most recently selected row, and the SelectedIndices collection contains the 
            bookmarks for all selected rows. The user can also unselect a row.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.SelectionModeEnum.MultiExtended">
      <summary>
            Pressing Shift and clicking the mouse or pressing Shift and one of the arrow keys 
            (Up Arrow, Down Arrow, Left Arrow, or Right Arrow) extends the selection from 
            the previously selected item to the current item.  Pressing Ctrl and clicking 
            the mouse selects or deselects an item in the list.  Since selected rows do not 
            have to be adjacent, the user can also operate the vertical scroll bar to bring 
            other rows into view if desired.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.SelectionModeEnum.CheckBox">
      <summary>
            The user can select or unselect rows by clicking checkboxes.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.FlatModeEnum">
      <summary>
            Specifies how 3D elements are rendered.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.FlatModeEnum.Standard">
      <summary>
            The grid’s column headers and recordselectors are rendered with an inset three-dimensional look
            </summary>
    </member>
    <member name="F:C1.Win.C1List.FlatModeEnum.Flat">
      <summary>
            Three-dimensional elements appear flat
            </summary>
    </member>
    <member name="F:C1.Win.C1List.FlatModeEnum.Popup">
      <summary>
            Three-dimensional elements are flat, but when the user drags the cursor over a column heading or recordselector, they become three-dimensional and appear to pop up.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.FlatModeEnum.System">
      <summary>
            Three-dimensional elements uses XP Themes if available.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.SizeModeEnum">
      <summary>
            Specifies the sizing mode for splits.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.SizeModeEnum.Scalable">
      <summary>
            The <see cref="P:C1.Win.C1List.Split.SplitSize" /> indicates the relative size of the split with respect to other scalable splits. 
            </summary>
    </member>
    <member name="F:C1.Win.C1List.SizeModeEnum.Exact">
      <summary>
            The <see cref="P:C1.Win.C1List.Split.SplitSize" /> indicates the size of the split in pixels.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.SizeModeEnum.NumberOfColumns">
      <summary>
            The <see cref="P:C1.Win.C1List.Split.SplitSize" /> indicates the number of columns displayed in the split.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.AlignVertEnum">
      <summary>
            Specifies the vertical alignment of text or images in a cell.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.AlignVertEnum.Top">
      <summary>
            Text is rendered at the top of the cell.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.AlignVertEnum.Center">
      <summary>
            Text is rendered at the center of the cell.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.AlignVertEnum.Bottom">
      <summary>
            Text is rendered at the bottom of the cell.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.AlignHorzEnum">
      <summary>
            Specifies the horizontal alignment of text or images in a cell.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.AlignHorzEnum.General">
      <summary>
            Text is aligned Near and numeric values Far
            </summary>
    </member>
    <member name="F:C1.Win.C1List.AlignHorzEnum.Near">
      <summary>
            Text is aligned to the left.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.AlignHorzEnum.Center">
      <summary>
            Text is aligned centered.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.AlignHorzEnum.Far">
      <summary>
            Text is aligned to the right.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.AlignHorzEnum.Justify">
      <summary>
            Text is aligned with respect to the cells boundries.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ScrollBarStyleEnum">
      <summary>
            Specifies the visibility of ScrollBars.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ScrollBarStyleEnum.None">
      <summary>
            ScrollBars are never displayed.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ScrollBarStyleEnum.Always">
      <summary>
            ScrollBars are always displayed.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ScrollBarStyleEnum.Automatic">
      <summary>
            ScrollBars are displayed only if the object's contents extend beyond its borders.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.LineStyleEnum">
      <summary>
            Specifies the line style for row and column dividers.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.LineStyleEnum.None">
      <summary>
            No line.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.LineStyleEnum.Single">
      <summary>
            Single line.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.LineStyleEnum.Double">
      <summary>
            Double line.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.LineStyleEnum.Raised">
      <summary>
            Line with 3D raised appearance.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.LineStyleEnum.Inset">
      <summary>
            Line with 3D inset appearance.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.DataViewEnum">
      <summary>
            Specifies how the grid displays its data.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.DataViewEnum.Normal">
      <summary>
            The grid will only display flat files and will not support a hierarchical view. If the data source is a hierarchical dataset, the grid will only display data from the master table.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.DataViewEnum.MultipleLines">
      <summary>
            The grid will display all the fields in the current grid area with multiple lines.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.CellTipEnum">
      <summary>
            Specifies the behavior of the pop-up window when the cursor is idle over the grid.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.CellTipEnum.NoCellTips">
      <summary>
            No cell tips will be displayed.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.CellTipEnum.Anchored">
      <summary>
            Cell tips will be displayed in the bounding rectable of the cell.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.CellTipEnum.Floating">
      <summary>
            Cell tips will be displayed under the mouse cursor.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ExposeCellModeEnum">
      <summary>
            Specifies how the grid exposes the rightmost column when it gets focus.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ExposeCellModeEnum.ScrollOnSelect">
      <summary>
            The grid will scroll to the left to display the rightmost column in its entirety.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ExposeCellModeEnum.ScrollOnEdit">
      <summary>
            The grid will not move when the rightmost column is clicked initially. However, if the user attempts to edit the cell, then the grid will scroll to the left to display the rightmost column in its entirety.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ExposeCellModeEnum.ScrollNever">
      <summary>
            The grid will always leave the rightmost column clipped.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ForeGroundPicturePositionEnum">
      <summary>
            Specifies the location of the foreground image in a cell.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ForeGroundPicturePositionEnum.Near">
      <summary>
            Image is rendered in the near side of the cell.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ForeGroundPicturePositionEnum.Far">
      <summary>
            Image is rendered in the far side of the cell.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ForeGroundPicturePositionEnum.LeftOfText">
      <summary>
            Image is rendered to the left of any text in the cell.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ForeGroundPicturePositionEnum.RightOfText">
      <summary>
            Image is rendered to the right of any text in the cell.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ForeGroundPicturePositionEnum.TopOfText">
      <summary>
            Image is rendered on top of any text in the cell.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ForeGroundPicturePositionEnum.BottomOfText">
      <summary>
            Image is rendered below any text in the cell.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ForeGroundPicturePositionEnum.PictureOnly">
      <summary>
            Text is not displayed.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ForeGroundPicturePositionEnum.TextOnly">
      <summary>
            Image is not displayed.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.BackgroundPictureDrawModeEnum">
      <summary>
            Specifies how the background image is rendered.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.BackgroundPictureDrawModeEnum.Center">
      <summary>
            The image is rendered in the center of the cell.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.BackgroundPictureDrawModeEnum.Tile">
      <summary>
            The image is tiled in the cell.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.BackgroundPictureDrawModeEnum.Stretch">
      <summary>
            The image is stretched to fit within the cell.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.RowSizingEnum">
      <summary>
            Specifies how rows can be resized.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.RowSizingEnum.None">
      <summary>
            Row can not be resized.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.RowSizingEnum.AllRows">
      <summary>
            All rows will be sized to the same height or width.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.RowSizingEnum.IndividualRows">
      <summary>
            Rows can be sized indepentently.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.RowSelectorEnum">
      <summary>
            Specifies which rows are to be previewed/printed.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.RowSelectorEnum.AllRows">
      <summary>
            All rows are to be previewed/printed.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.RowSelectorEnum.SelectedRows">
      <summary>
            Only selected rows will be previewed/printed.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.RowSelectorEnum.CurrentRow">
      <summary>
            Only the current row will be previewed/printed.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.PointAtEnum">
      <summary>
            Specifies the type of ui-element for a coordinate.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PointAtEnum.NotInList">
      <summary>
            Coordinates are not in the list.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PointAtEnum.AtCaption">
      <summary>
            Coordinates are in the caption area
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PointAtEnum.AtSplitHeader">
      <summary>
            Coordinates are in the split's header
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PointAtEnum.AtSplitSizeBox">
      <summary>
            Coordinates are in the split's resizing box
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PointAtEnum.AtRowSelect">
      <summary>
            Coordinates are in the row selector.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PointAtEnum.AtRowSize">
      <summary>
            Coordinates are in the row resizing box.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PointAtEnum.AtColumnHeader">
      <summary>
            Coordinates are in the column headers.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PointAtEnum.AtColumnFooter">
      <summary>
            Coordinates are in the column footers.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PointAtEnum.AtColumnSize">
      <summary>
            Coordinates are in the column resizing box.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PointAtEnum.AtDataArea">
      <summary>
            Coordinates are in the data area.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.PresentationEnum">
      <summary>
            Specifies how ValueItems are rendered.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PresentationEnum.Normal">
      <summary>
            Values are displayed as text or graphics.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PresentationEnum.RadioButton">
      <summary>
            Values are displayed as a group of Radio Buttons.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.PresentationEnum.CheckBox">
      <summary>
            Values are displayed as a checkbox.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.TabActionEnum">
      <summary>
            Specifies how focus is handled when the Tab key is entered.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.TabActionEnum.ControlNavigation">
      <summary>
            The tab key moves to the next or previous control on the form.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.TabActionEnum.ColumnNavigation">
      <summary>
            The tab key moves the current cell to the next or previous column. However, if this action would cause the current row to change, then the next or previous control on the form receives focus.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.TabActionEnum.GridNavigation">
      <summary>
            The tab key moves the current cell to the next or previous column.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.BorderTypeEnum">
      <summary>
            Specifies the borders for a <see cref="T:C1.Win.C1List.Style" />.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.BorderTypeEnum.None">
      <summary>
            No borders.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.BorderTypeEnum.Flat">
      <summary>
            Borders have a Flat appearance.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.BorderTypeEnum.Raised">
      <summary>
            Borders have a 3D raised appearance.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.BorderTypeEnum.Inset">
      <summary>
            Borders have a 3D inset appearance.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.BorderTypeEnum.Groove">
      <summary>
            A line around the inside of the border.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.BorderTypeEnum.Fillet">
      <summary>
            A fillet type border.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.BorderTypeEnum.RaisedBevel">
      <summary>
            Borders have a 3D raised with a bevel.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.BorderTypeEnum.InsetBevel">
      <summary>
            Borders have a 3D inset with a bevel.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.CellStyleFlag">
      <summary>
            Describes the disposition of a cell.
            <para>This enumeration has a FlagsAttribute attribute that allows a bitwise combination of its member values.</para></summary>
    </member>
    <member name="F:C1.Win.C1List.CellStyleFlag.NormalCell">
      <summary>
            The cell satisfies none of the conditions. For grouped rows, this is the only applicable cell style.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.CellStyleFlag.CurrentCell">
      <summary>
            The cell is the that currently has focus. At any given time, only one cell can have this status. When the MarqueeStyle property is set to Floating Editor, this condition is ignored.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.CellStyleFlag.MarqueeRow">
      <summary>
            The cell is part of a highlighted row marquee. When the MarqueeStyle property indicates that the entire current row is to be highlighted, all visible cells in the current row have this additional condition set.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.CellStyleFlag.SelectedRow">
      <summary>
            The cell is part of a row selected by the user or in code. The SelectedRowCollection contains the index for each selected row.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.CellStyleFlag.AllCells">
      <summary>
            All cells.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ScrollBarEnum">
      <summary>
            Identifies the type of scrollbar.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ScrollBarEnum.Horizontal">
      <summary>
            The Horizontal scrollbar.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.ScrollBarEnum.Vertical">
      <summary>
            The Vertical scrollbar.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.BeforeColUpdateEventHandler">
      <summary>
            Raised before a column enters edit mode.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.BeforeColUpdateEventArgs">
      <summary>
            Provides data for the BeforeColEdit event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.BeforeColUpdateEventArgs.ColIndex">
      <summary>
            Indicates the position in the DisplayColumns collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.BeforeColUpdateEventArgs.OldValue">
      <summary>
            Gets or sets the value of the original cell data.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.BeforeColUpdateEventArgs.Cancel">
      <summary>
            Gets or sets a value that prevents the user from moving focus to another cell.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.BeforeColUpdateEventArgs.Column">
      <summary>
            Gets the C1DisplayColumn for the column being edited.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ColResizeEventHandler">
      <summary>
            Raised after a column has been resized.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ColResizeEventArgs">
      <summary>
            Provides data for the ColReisize event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ColResizeEventArgs.ColIndex">
      <summary>
            Indicates the position in the DisplayColumns collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ColResizeEventArgs.Cancel">
      <summary>
            Gets or sets a value indicating that sizing should be ignored.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ColEventHandler">
      <summary>
            Raised when an action is perfored on a column.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ColEventArgs">
      <summary>
            Provides data for the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.ColHeadClick" />, <see cref="E:C1.Win.C1List.ListBase.C1ListBase.ColFootClick" /> events.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ColEventArgs.ColIndex">
      <summary>
            Indicates the position in the DisplayColumns collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.OwnerDrawCellEventHandler">
      <summary>
            Raised when a cell is to rendered by the event code.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.OwnerDrawCellEventArgs">
      <summary>
            Provides data for the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.OwnerDrawCell" /> event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.OwnerDrawCellEventArgs.CellRect">
      <summary>
            The bounding rectangle for the cell that needs to be rendered.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.OwnerDrawCellEventArgs.Row">
      <summary>
            The index of the row for the cell being rendered.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.OwnerDrawCellEventArgs.Split">
      <summary>
            The index of the split for the cell being rendered.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.OwnerDrawCellEventArgs.Col">
      <summary>
            The index of the column in the DisplayColumns collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.OwnerDrawCellEventArgs.Graphics">
      <summary>
            The GDI+ graphics object to render on.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.OwnerDrawCellEventArgs.Text">
      <summary>
            The text of the cell.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.OwnerDrawCellEventArgs.Style">
      <summary>
            The Style used to render the cell.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.OwnerDrawCellEventArgs.Handled">
      <summary>
            Indicates whether the event code rendered the cell.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.OwnerDrawCellEventArgs.Column">
      <summary>
            The C1DisplayColumn being rendered.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.OwnerDrawCellEventArgs.DrawCellFlags">
      <summary>
            Indicates whether the custom renderer handles the background, border or content. 
            </summary>
    </member>
    <member name="T:C1.Win.C1List.RowColChangeEventHandler">
      <summary>
            Raised after the current Row or Column changes.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.RowColChangeEventArgs">
      <summary>
            Provides data for the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.RowColChange" /> event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.RowColChangeEventArgs.LastRow">
      <summary>
            The previous row index.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.RowColChangeEventArgs.LastCol">
      <summary>
            The previous column index.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.UnboundColumnFetchEventHandler">
      <summary>
            Raised when an unbound column needs to be rendered.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.UnboundColumnFetchEventArgs">
      <summary>
             Provides data for the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.UnboundColumnFetch" /> event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.UnboundColumnFetchEventArgs.Row">
      <summary>
            The index of the row.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.UnboundColumnFetchEventArgs.Col">
      <summary>
            Indicates the position in the Columns collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.UnboundColumnFetchEventArgs.Value">
      <summary>
            Gets or sets the value of the Column.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.BeforeColEditEventHandler">
      <summary>
            Raised before a column enters edit mode.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.BeforeColEditEventArgs">
      <summary>
            Provides data for the BeforeColEdit event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.BeforeColEditEventArgs.Cancel">
      <summary>
            Gets or sets a value indicating that editing should be disallowed.
            </summary>
      <remarks>If event procedure sets the Cancel argument to True, the cell will not enter edit mode. Otherwise, the ColEdit event is raised immediately, followed by the Change event for the KeyChar property, if non-zero.</remarks>
    </member>
    <member name="P:C1.Win.C1List.BeforeColEditEventArgs.KeyChar">
      <summary>
            Indicates the character that initiated the editing operation.
            </summary>
      <remarks>The BeforeColEdit event occurs just before the user enters edit mode by typing a character. If a floating editor marquee is not in use, this event also occurs when the user clicks the current cell or double clicks another cell.</remarks>
    </member>
    <member name="P:C1.Win.C1List.BeforeColEditEventArgs.ColIndex">
      <summary>
            Indicates the position in the DisplayColumns collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.SplitEventHandler">
      <summary>
            Raised when Split specific actions are performed.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.SplitEventArgs">
      <summary>
            Provides data for the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.TopIndexChange" /> and <see cref="E:C1.Win.C1List.ListBase.C1ListBase.LeftColChange" /> events.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.SplitEventArgs.SplitIndex">
      <summary>
            Indicates the position of the Split in the Splits collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.FormatTextEventHandler">
      <summary>
            Raised when a cell needs custom formatting.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.FormatTextEventArgs">
      <summary>
            Provides data for the FromatText event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FormatTextEventArgs.ColIndex">
      <summary>
            Indicates the position in the Columns collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FormatTextEventArgs.Row">
      <summary>
            The row index for the cell to be formatted.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FormatTextEventArgs.Value">
      <summary>
            The value to format.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.FetchCellStyleEventHandler">
      <summary>
            Raised when the <see cref="T:C1.Win.C1List.Style" /> used to render a cell needs customization.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.FetchCellStyleEventArgs">
      <summary>
            Provides data for the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.FetchCellStyle" /> event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchCellStyleEventArgs.Condition">
      <summary>
            The sum of one or more <see cref="T:C1.Win.C1List.CellStyleFlag" /> constants describing the disposition of the cell being rendered.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchCellStyleEventArgs.Split">
      <summary>
            Indicates the position in the Splits collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchCellStyleEventArgs.Row">
      <summary>
            The index of the row for the cell being rendered.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchCellStyleEventArgs.Col">
      <summary>
            The index of the column in the DisplayColumns collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchCellStyleEventArgs.CellStyle">
      <summary>
            The Style used to render the cell.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.FetchRowStyleEventHandler">
      <summary>
            Raised when the <see cref="T:C1.Win.C1List.Style" /> used to render a row needs customization.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.FetchRowStyleEventArgs">
      <summary>
            Provides data for the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.FetchRowStyle" /> event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchRowStyleEventArgs.Split">
      <summary>
            Indicates the position in the Splits collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchRowStyleEventArgs.Row">
      <summary>
            The index of the row for the cell being rendered.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchRowStyleEventArgs.CellStyle">
      <summary>
            The Style used to render the row.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ColMoveEventHandler">
      <summary>
            Raised when a column is being repositioned.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ColMoveEventArgs">
      <summary>
            Provides data for the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.ColMove" /> event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ColMoveEventArgs.Position">
      <summary>
            Indicates the target index of the column being moved.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ColMoveEventArgs.ColIndex">
      <summary>
            Indicates the starting position in the DisplayColumns collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ColMoveEventArgs.Cancel">
      <summary>
            Gets or sets a value indicating whether the column moving action is to be canceled.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.FetchCellTipsEventHandler">
      <summary>
            Raised when cell tips are to be displayed.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.FetchCellTipsEventArgs">
      <summary>
            Provides data for the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.FetchCellTips" /> event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchCellTipsEventArgs.SplitIndex">
      <summary>
            Indicates the position of the Split in the Splits collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchCellTipsEventArgs.ColIndex">
      <summary>
            Indicates the position in the DisplayColumns collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchCellTipsEventArgs.Row">
      <summary>
            The index of the row for the cell tip.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchCellTipsEventArgs.FullyDisplayed">
      <summary>
            Indicates if the contents of the cell is fully displayed.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchCellTipsEventArgs.TipStyle">
      <summary>
            The Style used to render the cell tip.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchCellTipsEventArgs.CellTip">
      <summary>
            The text to be displayed in the cell tip.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.FetchScrollTipsEventHandler">
      <summary>
            Raised when scroll tips are to be displayed.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.FetchScrollTipsEventArgs">
      <summary>
            Provides data for the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.FetchScrollTips" /> event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchScrollTipsEventArgs.SplitIndex">
      <summary>
            Indicates the position of the Split in the Splits collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchScrollTipsEventArgs.ColIndex">
      <summary>
            Indicates the position in the DisplayColumns collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchScrollTipsEventArgs.Row">
      <summary>
            The index of the topmost row for the scroll tip.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchScrollTipsEventArgs.ScrollBar">
      <summary>
            Indicates the scrollbar that was moved.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchScrollTipsEventArgs.ScrollTip">
      <summary>
            The text to be displayed in the scroll tip.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.FetchScrollTipsEventArgs.TipStyle">
      <summary>
            The Style used to render the scroll tip.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.FilterEventHandler">
      <summary>
            Raised when the grid is sorted.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.FilterEventArgs">
      <summary>
            Provides data for the <see cref="M:C1.Win.C1List.ListBase.C1ListBase.Sort(System.Int32,C1.Win.C1List.SortDirEnum)" />, and <see cref="E:C1.Win.C1List.ListBase.C1ListBase.AfterSort" /></summary>
    </member>
    <member name="P:C1.Win.C1List.FilterEventArgs.Condition">
      <summary>
            The sort condition.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ErrorEventHandler">
      <summary>
            Raised when an excpetion is thrown via the UI.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.ErrorEventArgs">
      <summary>
            Provides data for the <see cref="E:C1.Win.C1List.ListBase.C1ListBase.Error" /> event.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ErrorEventArgs.Exception">
      <summary>
            The exception which caused the Event to be raised.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.ErrorEventArgs.Handled">
      <summary>
            True if the exception has been handled.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.OwnerDrawPageEventHandler">
      <summary>
            Raised when custom page headers and footers need to be rendered.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.OwnerDrawPageEventArgs">
      <summary>
            Provides data for the <see cref="!:C1List.OwnerDrawPageHeader" /> and <see cref="!:C1List.OwnerDrawPageHeader" /> events.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.OwnerDrawPageEventArgs.OwnerDrawPrint">
      <summary>
            The <see cref="T:C1.Win.C1List.C1OwnerDrawPrint" /> object used to render the custom header or footer
            </summary>
    </member>
    <member name="T:C1.Win.C1List.Styles.DrawCellFlags">
      <summary>
            Specifies which elements of the cell should be drawn by the grid.
            </summary>
      <remarks>
        <para>This enumeration is used when rendering owner-drawn cells.</para>
      </remarks>
    </member>
    <member name="F:C1.Win.C1List.Styles.DrawCellFlags.None">
      <summary>
            Draw nothing.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.Styles.DrawCellFlags.Background">
      <summary>
            Draw the cell background.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.Styles.DrawCellFlags.Border">
      <summary>
            Draw the cell border.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.Styles.DrawCellFlags.Content">
      <summary>
            Draw the cell content (text, images, checkboxes, etc).
            </summary>
    </member>
    <member name="F:C1.Win.C1List.Styles.DrawCellFlags.All">
      <summary>
            Draw all cell elements (background, border, and contents).
            </summary>
    </member>
    <member name="T:C1.Win.C1List.CellBorders">
      <summary>
            Represents the border in a <see cref="T:C1.Win.C1List.Style" /></summary>
    </member>
    <member name="P:C1.Win.C1List.CellBorders.BorderType">
      <summary>
            Gets or sets the type of border.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.CellBorders.Left">
      <summary>
            Gets or sets the width of the left border.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.CellBorders.Right">
      <summary>
            Gets or sets the width of the right border.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.CellBorders.Top">
      <summary>
            Gets or sets the width of the top border.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.CellBorders.Bottom">
      <summary>
            Gets or sets the width of the bottom border.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.CellBorders.Color">
      <summary>
            Gets or sets the color of the border.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.Style">
      <summary>
            Represents an object used to render grid elements.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.#ctor">
      <summary>
            Creates a new instance of this object.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.Dispose">
      <summary>
            Called when the class is being disposed.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.Dispose(System.Boolean)">
      <summary>
            Called when the class is being disposed.
            </summary>
      <param name="disposing">
            True to cleanup.
            </param>
    </member>
    <member name="M:C1.Win.C1List.Style.ToString">
      <summary>
      </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Style.ResetBackColor">
      <summary>
            Reset BackColor to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.ResetForeColor">
      <summary>
            Resets ForeColor to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.ResetFont">
      <summary>
            Resets Font to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.ResetHorizontalAlignment">
      <summary>
            Resets HorizontalAlignment to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.ResetVerticalAlignment">
      <summary>
            Resets VerticalAlignment to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.ResetBackgroundPictureDrawMode">
      <summary>
            Resets BackgroundPictureDrawMode to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.ResetBackgroundImage">
      <summary>
            Resets the BackgroundImage to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.ResetForeGroundPicturePosition">
      <summary>
            Resets ForGroundImagePosition to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.ResetForegroundImage">
      <summary>
            Resets ForegroundImage to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.ResetLocked">
      <summary>
            Resets Locked to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.ResetWrapText">
      <summary>
            Resets WrapText to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.ResetTrimming">
      <summary>
            Resets Trimming to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Style.Reset">
      <summary>
            Resets all specialized attributes.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.Borders">
      <summary>
            Gets the <see cref="T:C1.Win.C1List.CellBorders" /> associated with this Style.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.BackColor">
      <summary>
            Gets or sets the background color associated with a Style.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.BackColor2">
      <summary>
            Gets or sets the background color associated with a Style.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.ForeColor">
      <summary>
            Gets or sets the foreground color associated with a Style.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.Font">
      <summary>
            Gets or sets the Font associated with a Style.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.HorizontalAlignment">
      <summary>
            Gets or sets the horizontal text alignment.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.VerticalAlignment">
      <summary>
            Gets or sets the vertical text alignment.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.BackgroundPictureDrawMode">
      <summary>
            Gets or sets the rendering method for a <see cref="P:C1.Win.C1List.Style.BackgroundImage" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.BackgroundImage">
      <summary>
            Gets or sets the background image associated with a Style.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.ForeGroundPicturePosition">
      <summary>
            Gets or sets the position that the ForGroupImage is rendered.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.ForegroundImage">
      <summary>
            Gets or sets the foreground image associated with a style.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.WrapText">
      <summary>
            Gets or sets a value indicating whether text is word-wrapped when it does not fit into a layout shape.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.Name">
      <summary>
            Gets or sets the name of the Style.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.Trimming">
      <summary>
            Gets or sets the trim characters for a string that does not completely fit into a layout shape.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Style.Padding">
      <summary>
            Gets or sets the spacing between cell content and its edges.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.Util.HBar">
      <summary>
            Represents a horizontal scrollbar.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.HBar.#ctor(C1.Win.C1List.ListBase.View)">
      <summary>
            Create a new instance of HBar class.
            </summary>
      <param name="owner">
      </param>
    </member>
    <member name="M:C1.Win.C1List.Util.HBar.Dispose(System.Boolean)">
      <summary>
            Releases the unmanaged resources used by the object and optionally releases the managed resources.
            </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1List.Util.HBar.ToString">
      <summary>
            Returns a string the represents the current object.
            </summary>
      <returns>A string the represents the current object</returns>
    </member>
    <member name="P:C1.Win.C1List.Util.HBar.Height">
      <summary>
            Specifies the horizontal scroll bar height.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Util.HBar.Style">
      <summary>
            Specifies the <see cref="T:C1.Win.C1List.ScrollBarEnum" /> value for the horizontal scroll bar.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Util.HBar.Visible">
      <summary>
            Gets a value indicating whether the scrollbar is displayed.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.Util.NumberFormatTypeEditor">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.NumberFormatTypeEditor.#ctor">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.NumberFormatTypeEditor.GetEditStyle(System.ComponentModel.ITypeDescriptorContext)">
      <summary>
      </summary>
      <param name="context">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.Util.NumberFormatTypeEditor.OnListBoxClick(System.Object,System.EventArgs)">
      <summary>
      </summary>
      <param name="sender">
      </param>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1List.Util.NumberFormatTypeEditor.EditValue(System.ComponentModel.ITypeDescriptorContext,System.IServiceProvider,System.Object)">
      <summary>
      </summary>
      <param name="context">
      </param>
      <param name="provider">
      </param>
      <param name="value">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1List.Util.VBar">
      <summary>
            Represents a vertical scrollbar.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.Util.VBar.#ctor(C1.Win.C1List.ListBase.View)">
      <summary>
            Initializes a new instance of the VBar class.
            </summary>
      <param name="owner">
      </param>
    </member>
    <member name="M:C1.Win.C1List.Util.VBar.Dispose(System.Boolean)">
      <summary>
            Releases the unmanaged resources used by the object and optionally releases the managed resources.
            </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1List.Util.VBar.ToString">
      <summary>
            Returns a string the represents the current object.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1List.Util.VBar.Width">
      <summary>
            Gets or sets the width of the vertical scrollbar.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Util.VBar.Style">
      <summary>
            Gets or sets the visibility of the scrollbars.
            </summary>
    </member>
    <member name="P:C1.Win.C1List.Util.VBar.Visible">
      <summary>
            Gets a value indicating whether the scrollbar is displayed.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.VisualStyle">
      <summary>
            Specifies a visual style to use when rendering the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.VisualStyle.Custom">
      <summary>
            Do not use any visual styles. Render the control using the styles and properties only.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.VisualStyle.System">
      <summary>
            Render the control with an appearance based on the current system settings.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.VisualStyle.Office2007Blue">
      <summary>
            Render the control with an appearance based on the Office 2007 Blue color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.VisualStyle.Office2007Silver">
      <summary>
            Render the control with an appearance based on the Office 2007 Silver color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.VisualStyle.Office2007Black">
      <summary>
            Render the control with an appearance based on the Office 2007 Black color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.VisualStyle.Office2010Blue">
      <summary>
            Render the control with an appearance based on the Office 2010 Blue color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.VisualStyle.Office2010Silver">
      <summary>
            Render the control with an appearance based on the Office 2010 Silver color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1List.VisualStyle.Office2010Black">
      <summary>
            Render the control with an appearance based on the Office 2010 Black color scheme.
            </summary>
    </member>
    <member name="T:C1.Win.C1List.GridRenderer">
      <summary>
            Base class for the grid renderers.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.GridRenderer.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1List.GridRenderer" /> class.
            </summary>
    </member>
    <member name="M:C1.Win.C1List.GridRenderer.OnDrawCell(C1.Win.C1List.ListBase.Frame,C1.Win.C1List.OwnerDrawCellEventArgs,C1.Win.C1List.GridRenderer.CellType)">
      <summary>
      </summary>
    </member>
    <member name="M:C1.Win.C1List.GridRenderer.GetBackgroundBrush(C1.Win.C1List.ListBase.Frame,C1.Win.C1List.GridRenderer.CellType,System.Drawing.Rectangle)">
      <summary>
            Gets the background brush.
            </summary>
      <param name="grid">The grid.</param>
      <param name="cellType">Type of the cell.</param>
      <param name="r">The r.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.GridRenderer.GetBorderPen(C1.Win.C1List.GridRenderer.CellType)">
      <summary>
            Gets the border pen.
            </summary>
      <param name="cellType">Type of the cell.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.GridRenderer.StyleHasCustomBackground(C1.Win.C1List.Style)">
      <summary>
            Styles the has custom background.
            </summary>
      <param name="style">The style.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1List.GridRenderer.StyleHasCustomBorder(C1.Win.C1List.Style)">
      <summary>
            Styles the has custom border.
            </summary>
      <param name="style">The style.</param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1List.GridRenderer.CellType">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.Normal">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.Caption">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.TopLeft">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.BottomLeft">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.ColumnHeader">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.ColumnHeaderSelected">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.ColumnHeaderHot">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.ColumnFooter">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.ColumnFooterSelected">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.ColumnFooterHot">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.RecordSelector">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.RecordSelectorSelected">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.RecordSelectorHot">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.Highlight">
      <summary>
      </summary>
    </member>
    <member name="F:C1.Win.C1List.GridRenderer.CellType.Cursor">
      <summary>
      </summary>
    </member>
  </members>
</doc>