﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fPharmacie
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fPharmacie))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.lListeClient = New System.Windows.Forms.Label()
        Me.bSuprimerPharmacie = New C1.Win.C1Input.C1Button()
        Me.bModifierPharmacie = New C1.Win.C1Input.C1Button()
        Me.bAjouterPharmacie = New C1.Win.C1Input.C1Button()
        Me.gPharmacie = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.cmbVillePharmacie = New C1.Win.C1List.C1Combo()
        Me.lVillePharmacie = New System.Windows.Forms.Label()
        Me.chbSolde = New System.Windows.Forms.CheckBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.bViderLesChamps = New C1.Win.C1Input.C1Button()
        Me.bRechercher = New C1.Win.C1Input.C1Button()
        Me.cmbNomPharmacie = New C1.Win.C1List.C1Combo()
        Me.lNomClient = New System.Windows.Forms.Label()
        Me.Panel.SuspendLayout()
        CType(Me.gPharmacie, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.cmbVillePharmacie, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbNomPharmacie, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.lListeClient)
        Me.Panel.Controls.Add(Me.bSuprimerPharmacie)
        Me.Panel.Controls.Add(Me.bModifierPharmacie)
        Me.Panel.Controls.Add(Me.bAjouterPharmacie)
        Me.Panel.Controls.Add(Me.gPharmacie)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1028, 573)
        Me.Panel.TabIndex = 1
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(913, 20)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(103, 45)
        Me.bQuitter.TabIndex = 50
        Me.bQuitter.Text = "Fermer             F12   "
        Me.bQuitter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lListeClient
        '
        Me.lListeClient.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lListeClient.AutoSize = True
        Me.lListeClient.Location = New System.Drawing.Point(12, 111)
        Me.lListeClient.Name = "lListeClient"
        Me.lListeClient.Size = New System.Drawing.Size(106, 13)
        Me.lListeClient.TabIndex = 8
        Me.lListeClient.Text = "Liste des pharmacies" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        '
        'bSuprimerPharmacie
        '
        Me.bSuprimerPharmacie.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bSuprimerPharmacie.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSuprimerPharmacie.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler2
        Me.bSuprimerPharmacie.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSuprimerPharmacie.Location = New System.Drawing.Point(811, 516)
        Me.bSuprimerPharmacie.Name = "bSuprimerPharmacie"
        Me.bSuprimerPharmacie.Size = New System.Drawing.Size(100, 45)
        Me.bSuprimerPharmacie.TabIndex = 7
        Me.bSuprimerPharmacie.Text = "Suprimer"
        Me.bSuprimerPharmacie.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSuprimerPharmacie.UseVisualStyleBackColor = True
        Me.bSuprimerPharmacie.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bModifierPharmacie
        '
        Me.bModifierPharmacie.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bModifierPharmacie.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bModifierPharmacie.Image = Global.Pharma2000Premium.My.Resources.Resources.voir_modifierfpharmacien
        Me.bModifierPharmacie.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bModifierPharmacie.Location = New System.Drawing.Point(916, 516)
        Me.bModifierPharmacie.Name = "bModifierPharmacie"
        Me.bModifierPharmacie.Size = New System.Drawing.Size(100, 45)
        Me.bModifierPharmacie.TabIndex = 6
        Me.bModifierPharmacie.Text = "Modifier"
        Me.bModifierPharmacie.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bModifierPharmacie.UseVisualStyleBackColor = True
        Me.bModifierPharmacie.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouterPharmacie
        '
        Me.bAjouterPharmacie.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouterPharmacie.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouterPharmacie.Image = Global.Pharma2000Premium.My.Resources.Resources.nouveau
        Me.bAjouterPharmacie.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouterPharmacie.Location = New System.Drawing.Point(706, 516)
        Me.bAjouterPharmacie.Name = "bAjouterPharmacie"
        Me.bAjouterPharmacie.Size = New System.Drawing.Size(100, 45)
        Me.bAjouterPharmacie.TabIndex = 5
        Me.bAjouterPharmacie.Text = "Ajouter"
        Me.bAjouterPharmacie.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouterPharmacie.UseVisualStyleBackColor = True
        Me.bAjouterPharmacie.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gPharmacie
        '
        Me.gPharmacie.AllowUpdate = False
        Me.gPharmacie.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gPharmacie.GroupByCaption = "Drag a column header here to group by that column"
        Me.gPharmacie.Images.Add(CType(resources.GetObject("gPharmacie.Images"), System.Drawing.Image))
        Me.gPharmacie.Location = New System.Drawing.Point(12, 127)
        Me.gPharmacie.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gPharmacie.Name = "gPharmacie"
        Me.gPharmacie.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gPharmacie.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gPharmacie.PreviewInfo.ZoomFactor = 75.0R
        Me.gPharmacie.PrintInfo.PageSettings = CType(resources.GetObject("gPharmacie.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gPharmacie.Size = New System.Drawing.Size(1004, 381)
        Me.gPharmacie.TabIndex = 1
        Me.gPharmacie.Text = "C1TrueDBGrid1"
        Me.gPharmacie.PropBag = resources.GetString("gPharmacie.PropBag")
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.Controls.Add(Me.cmbVillePharmacie)
        Me.GroupBox1.Controls.Add(Me.lVillePharmacie)
        Me.GroupBox1.Controls.Add(Me.chbSolde)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Controls.Add(Me.bViderLesChamps)
        Me.GroupBox1.Controls.Add(Me.bRechercher)
        Me.GroupBox1.Controls.Add(Me.cmbNomPharmacie)
        Me.GroupBox1.Controls.Add(Me.lNomClient)
        Me.GroupBox1.Location = New System.Drawing.Point(12, 12)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(708, 91)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Critères de recherche"
        '
        'cmbVillePharmacie
        '
        Me.cmbVillePharmacie.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbVillePharmacie.Caption = ""
        Me.cmbVillePharmacie.CaptionHeight = 17
        Me.cmbVillePharmacie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbVillePharmacie.ColumnCaptionHeight = 17
        Me.cmbVillePharmacie.ColumnFooterHeight = 17
        Me.cmbVillePharmacie.ContentHeight = 15
        Me.cmbVillePharmacie.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbVillePharmacie.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbVillePharmacie.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbVillePharmacie.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbVillePharmacie.EditorHeight = 15
        Me.cmbVillePharmacie.Images.Add(CType(resources.GetObject("cmbVillePharmacie.Images"), System.Drawing.Image))
        Me.cmbVillePharmacie.ItemHeight = 15
        Me.cmbVillePharmacie.Location = New System.Drawing.Point(374, 25)
        Me.cmbVillePharmacie.MatchEntryTimeout = CType(2000, Long)
        Me.cmbVillePharmacie.MaxDropDownItems = CType(5, Short)
        Me.cmbVillePharmacie.MaxLength = 32767
        Me.cmbVillePharmacie.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbVillePharmacie.Name = "cmbVillePharmacie"
        Me.cmbVillePharmacie.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbVillePharmacie.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbVillePharmacie.Size = New System.Drawing.Size(163, 21)
        Me.cmbVillePharmacie.TabIndex = 49
        Me.cmbVillePharmacie.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbVillePharmacie.PropBag = resources.GetString("cmbVillePharmacie.PropBag")
        '
        'lVillePharmacie
        '
        Me.lVillePharmacie.AutoSize = True
        Me.lVillePharmacie.Location = New System.Drawing.Point(287, 28)
        Me.lVillePharmacie.Name = "lVillePharmacie"
        Me.lVillePharmacie.Size = New System.Drawing.Size(79, 13)
        Me.lVillePharmacie.TabIndex = 48
        Me.lVillePharmacie.Text = "Ville Pharmacie"
        '
        'chbSolde
        '
        Me.chbSolde.AutoSize = True
        Me.chbSolde.Location = New System.Drawing.Point(161, 61)
        Me.chbSolde.Name = "chbSolde"
        Me.chbSolde.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chbSolde.Size = New System.Drawing.Size(15, 14)
        Me.chbSolde.TabIndex = 46
        Me.chbSolde.UseVisualStyleBackColor = True
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(20, 61)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(135, 13)
        Me.Label2.TabIndex = 47
        Me.Label2.Text = "Pharmacien ayant un solde"
        '
        'bViderLesChamps
        '
        Me.bViderLesChamps.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bViderLesChamps.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bViderLesChamps.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bViderLesChamps.Location = New System.Drawing.Point(547, 50)
        Me.bViderLesChamps.Name = "bViderLesChamps"
        Me.bViderLesChamps.Size = New System.Drawing.Size(155, 35)
        Me.bViderLesChamps.TabIndex = 24
        Me.bViderLesChamps.Text = "Vider tous les champs"
        Me.bViderLesChamps.UseVisualStyleBackColor = True
        Me.bViderLesChamps.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bRechercher
        '
        Me.bRechercher.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bRechercher.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bRechercher.Image = Global.Pharma2000Premium.My.Resources.Resources.recherche2
        Me.bRechercher.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bRechercher.Location = New System.Drawing.Point(547, 10)
        Me.bRechercher.Name = "bRechercher"
        Me.bRechercher.Size = New System.Drawing.Size(155, 35)
        Me.bRechercher.TabIndex = 23
        Me.bRechercher.Text = "Rechercher"
        Me.bRechercher.UseVisualStyleBackColor = True
        Me.bRechercher.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbNomPharmacie
        '
        Me.cmbNomPharmacie.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbNomPharmacie.Caption = ""
        Me.cmbNomPharmacie.CaptionHeight = 17
        Me.cmbNomPharmacie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbNomPharmacie.ColumnCaptionHeight = 17
        Me.cmbNomPharmacie.ColumnFooterHeight = 17
        Me.cmbNomPharmacie.ContentHeight = 15
        Me.cmbNomPharmacie.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbNomPharmacie.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbNomPharmacie.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbNomPharmacie.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbNomPharmacie.EditorHeight = 15
        Me.cmbNomPharmacie.Images.Add(CType(resources.GetObject("cmbNomPharmacie.Images"), System.Drawing.Image))
        Me.cmbNomPharmacie.ItemHeight = 15
        Me.cmbNomPharmacie.Location = New System.Drawing.Point(110, 22)
        Me.cmbNomPharmacie.MatchEntryTimeout = CType(2000, Long)
        Me.cmbNomPharmacie.MaxDropDownItems = CType(5, Short)
        Me.cmbNomPharmacie.MaxLength = 32767
        Me.cmbNomPharmacie.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbNomPharmacie.Name = "cmbNomPharmacie"
        Me.cmbNomPharmacie.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbNomPharmacie.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbNomPharmacie.Size = New System.Drawing.Size(157, 21)
        Me.cmbNomPharmacie.TabIndex = 3
        Me.cmbNomPharmacie.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbNomPharmacie.PropBag = resources.GetString("cmbNomPharmacie.PropBag")
        '
        'lNomClient
        '
        Me.lNomClient.AutoSize = True
        Me.lNomClient.Location = New System.Drawing.Point(20, 25)
        Me.lNomClient.Name = "lNomClient"
        Me.lNomClient.Size = New System.Drawing.Size(82, 13)
        Me.lNomClient.TabIndex = 2
        Me.lNomClient.Text = "Nom Pharmacie"
        '
        'fPharmacie
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1028, 573)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fPharmacie"
        Me.Text = "fPharmacie"
        Me.Panel.ResumeLayout(False)
        Me.Panel.PerformLayout()
        CType(Me.gPharmacie, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.cmbVillePharmacie, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbNomPharmacie, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents lListeClient As System.Windows.Forms.Label
    Friend WithEvents bSuprimerPharmacie As C1.Win.C1Input.C1Button
    Friend WithEvents bModifierPharmacie As C1.Win.C1Input.C1Button
    Friend WithEvents bAjouterPharmacie As C1.Win.C1Input.C1Button
    Friend WithEvents gPharmacie As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents chbSolde As System.Windows.Forms.CheckBox
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents bViderLesChamps As C1.Win.C1Input.C1Button
    Friend WithEvents bRechercher As C1.Win.C1Input.C1Button
    Friend WithEvents cmbNomPharmacie As C1.Win.C1List.C1Combo
    Friend WithEvents lNomClient As System.Windows.Forms.Label
    Friend WithEvents cmbVillePharmacie As C1.Win.C1List.C1Combo
    Friend WithEvents lVillePharmacie As System.Windows.Forms.Label
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
End Class
