<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class TestForm
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.lblInstructions = New System.Windows.Forms.Label()
        Me.lblCodeClient = New System.Windows.Forms.Label()
        Me.tCodeClient = New System.Windows.Forms.TextBox()
        Me.lblStatus = New System.Windows.Forms.Label()
        Me.lblResult = New System.Windows.Forms.Label()
        Me.lblScanType = New System.Windows.Forms.Label()
        Me.lblDetails = New System.Windows.Forms.Label()
        Me.btnClear = New System.Windows.Forms.Button()
        Me.btnTest = New System.Windows.Forms.Button()
        Me.lstHistory = New System.Windows.Forms.ListBox()
        Me.lblHistory = New System.Windows.Forms.Label()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.GroupBox1.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        Me.SuspendLayout()
        '
        'lblInstructions
        '
        Me.lblInstructions.Location = New System.Drawing.Point(12, 9)
        Me.lblInstructions.Name = "lblInstructions"
        Me.lblInstructions.Size = New System.Drawing.Size(560, 60)
        Me.lblInstructions.TabIndex = 0
        Me.lblInstructions.Text = "Instructions"
        '
        'lblCodeClient
        '
        Me.lblCodeClient.AutoSize = True
        Me.lblCodeClient.Location = New System.Drawing.Point(6, 25)
        Me.lblCodeClient.Name = "lblCodeClient"
        Me.lblCodeClient.Size = New System.Drawing.Size(67, 13)
        Me.lblCodeClient.TabIndex = 1
        Me.lblCodeClient.Text = "Code Client:"
        '
        'tCodeClient
        '
        Me.tCodeClient.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodeClient.Location = New System.Drawing.Point(79, 19)
        Me.tCodeClient.Name = "tCodeClient"
        Me.tCodeClient.Size = New System.Drawing.Size(200, 26)
        Me.tCodeClient.TabIndex = 2
        '
        'lblStatus
        '
        Me.lblStatus.AutoSize = True
        Me.lblStatus.Location = New System.Drawing.Point(6, 55)
        Me.lblStatus.Name = "lblStatus"
        Me.lblStatus.Size = New System.Drawing.Size(37, 13)
        Me.lblStatus.TabIndex = 3
        Me.lblStatus.Text = "Status"
        '
        'lblResult
        '
        Me.lblResult.AutoSize = True
        Me.lblResult.Location = New System.Drawing.Point(6, 25)
        Me.lblResult.Name = "lblResult"
        Me.lblResult.Size = New System.Drawing.Size(46, 13)
        Me.lblResult.TabIndex = 4
        Me.lblResult.Text = "Résultat"
        '
        'lblScanType
        '
        Me.lblScanType.AutoSize = True
        Me.lblScanType.Location = New System.Drawing.Point(6, 45)
        Me.lblScanType.Name = "lblScanType"
        Me.lblScanType.Size = New System.Drawing.Size(31, 13)
        Me.lblScanType.TabIndex = 5
        Me.lblScanType.Text = "Type"
        '
        'lblDetails
        '
        Me.lblDetails.Location = New System.Drawing.Point(6, 65)
        Me.lblDetails.Name = "lblDetails"
        Me.lblDetails.Size = New System.Drawing.Size(250, 60)
        Me.lblDetails.TabIndex = 6
        Me.lblDetails.Text = "Détails"
        '
        'btnClear
        '
        Me.btnClear.Location = New System.Drawing.Point(295, 19)
        Me.btnClear.Name = "btnClear"
        Me.btnClear.Size = New System.Drawing.Size(75, 26)
        Me.btnClear.TabIndex = 7
        Me.btnClear.Text = "Effacer"
        Me.btnClear.UseVisualStyleBackColor = True
        '
        'btnTest
        '
        Me.btnTest.Location = New System.Drawing.Point(376, 19)
        Me.btnTest.Name = "btnTest"
        Me.btnTest.Size = New System.Drawing.Size(75, 26)
        Me.btnTest.TabIndex = 8
        Me.btnTest.Text = "Test"
        Me.btnTest.UseVisualStyleBackColor = True
        '
        'lstHistory
        '
        Me.lstHistory.FormattingEnabled = True
        Me.lstHistory.Location = New System.Drawing.Point(300, 160)
        Me.lstHistory.Name = "lstHistory"
        Me.lstHistory.Size = New System.Drawing.Size(272, 160)
        Me.lstHistory.TabIndex = 9
        '
        'lblHistory
        '
        Me.lblHistory.AutoSize = True
        Me.lblHistory.Location = New System.Drawing.Point(297, 144)
        Me.lblHistory.Name = "lblHistory"
        Me.lblHistory.Size = New System.Drawing.Size(58, 13)
        Me.lblHistory.TabIndex = 10
        Me.lblHistory.Text = "Historique:"
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.lblCodeClient)
        Me.GroupBox1.Controls.Add(Me.tCodeClient)
        Me.GroupBox1.Controls.Add(Me.btnTest)
        Me.GroupBox1.Controls.Add(Me.lblStatus)
        Me.GroupBox1.Controls.Add(Me.btnClear)
        Me.GroupBox1.Location = New System.Drawing.Point(12, 72)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(560, 80)
        Me.GroupBox1.TabIndex = 11
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Test Scanner"
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.lblResult)
        Me.GroupBox2.Controls.Add(Me.lblScanType)
        Me.GroupBox2.Controls.Add(Me.lblDetails)
        Me.GroupBox2.Location = New System.Drawing.Point(12, 160)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(270, 160)
        Me.GroupBox2.TabIndex = 12
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "Résultats"
        '
        'TestForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(584, 341)
        Me.Controls.Add(Me.GroupBox2)
        Me.Controls.Add(Me.GroupBox1)
        Me.Controls.Add(Me.lblHistory)
        Me.Controls.Add(Me.lstHistory)
        Me.Controls.Add(Me.lblInstructions)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.Name = "TestForm"
        Me.Text = "Test Scanner Code à Barres"
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents lblInstructions As Label
    Friend WithEvents lblCodeClient As Label
    Friend WithEvents tCodeClient As TextBox
    Friend WithEvents lblStatus As Label
    Friend WithEvents lblResult As Label
    Friend WithEvents lblScanType As Label
    Friend WithEvents lblDetails As Label
    Friend WithEvents btnClear As Button
    Friend WithEvents btnTest As Button
    Friend WithEvents lstHistory As ListBox
    Friend WithEvents lblHistory As Label
    Friend WithEvents GroupBox1 As GroupBox
    Friend WithEvents GroupBox2 As GroupBox
End Class
