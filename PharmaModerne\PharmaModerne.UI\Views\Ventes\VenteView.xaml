<UserControl x:Class="PharmaModerne.UI.Views.Ventes.VenteView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="900" d:DesignWidth="1400">

    <Grid Margin="16">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        
        <!-- Zone principale de vente -->
        <Grid Grid.Column="0" Margin="0,0,8,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- En-tête Point de Vente -->
            <Border Grid.Row="0" 
                    Background="DarkGreen" 
                    CornerRadius="10" 
                    Padding="20" 
                    Margin="0,0,0,16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <TextBlock Text="🛒" FontSize="32" Foreground="White" VerticalAlignment="Center"/>
                        <StackPanel Margin="16,0,0,0" VerticalAlignment="Center">
                            <TextBlock Text="Point de Vente" 
                                     FontSize="24" 
                                     FontWeight="Bold"
                                     Foreground="White"/>
                            <TextBlock Text="Scanner intégré - Vente rapide" 
                                     FontSize="14" 
                                     Foreground="LightGreen"/>
                        </StackPanel>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <TextBlock Text="{Binding NumeroVente, StringFormat='Vente N° {0}'}" 
                                 FontSize="18" 
                                 FontWeight="Bold"
                                 Foreground="White"
                                 VerticalAlignment="Center"
                                 Margin="0,0,20,0"/>
                        
                        <Button Content="🔄 NOUVELLE VENTE"
                                Command="{Binding NouvelleVenteCommand}"
                                Padding="15,8"
                                Background="Orange"
                                Foreground="White"
                                BorderThickness="0"
                                FontWeight="Bold"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- Scanner et recherche d'articles -->
            <Border Grid.Row="1" 
                    Background="White" 
                    CornerRadius="10" 
                    Padding="20" 
                    Margin="0,0,0,16"
                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Scanner principal -->
                    <Grid Grid.Row="0" Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Grid.Column="0"
                                 x:Name="ScannerTextBox"
                                 Text="{Binding CodeScanne, UpdateSourceTrigger=PropertyChanged}"
                                 FontSize="18"
                                 Padding="15"
                                 Background="LightYellow"
                                 BorderBrush="Green"
                                 BorderThickness="2">
                            <TextBox.InputBindings>
                                <KeyBinding Key="Enter" Command="{Binding AjouterArticleCommand}"/>
                            </TextBox.InputBindings>
                        </TextBox>
                        <TextBlock Text="📱 Scanner ou saisir un code article / code-barres..."
                                 IsHitTestVisible="False"
                                 VerticalAlignment="Center"
                                 HorizontalAlignment="Left"
                                 Margin="20,0,0,0"
                                 Foreground="Gray"
                                 FontSize="16">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding CodeScanne}" Value="">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                        
                        <Button Grid.Column="1"
                                Content="➕ AJOUTER"
                                Command="{Binding AjouterArticleCommand}"
                                Padding="20,12"
                                Margin="10,0"
                                Background="Green"
                                Foreground="White"
                                BorderThickness="0"
                                FontWeight="Bold"
                                FontSize="14"/>
                        
                        <Button Grid.Column="2"
                                Content="🔍 RECHERCHER"
                                Command="{Binding RechercherArticleCommand}"
                                Padding="20,12"
                                Background="Blue"
                                Foreground="White"
                                BorderThickness="0"
                                FontWeight="Bold"
                                FontSize="14"/>
                    </Grid>
                    
                    <!-- Informations scanner -->
                    <StackPanel Grid.Row="1" Orientation="Horizontal">
                        <Border Background="Green"
                              CornerRadius="15"
                              Padding="10,5"
                              Margin="0,0,10,0"
                              Visibility="{Binding IsScannerActive}">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📡" Foreground="White" FontSize="14"/>
                                <TextBlock Text="Scanner Actif" 
                                         Foreground="White"
                                         Margin="5,0,0,0"
                                         FontWeight="Bold"/>
                            </StackPanel>
                        </Border>
                        
                        <TextBlock Text="{Binding DernierScan, StringFormat='Dernier scan: {0}'}" 
                                 VerticalAlignment="Center"
                                 Foreground="Gray"
                                 FontSize="12"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- Liste des articles de la vente -->
            <Border Grid.Row="2" 
                    Background="White" 
                    CornerRadius="10" 
                    Padding="0"
                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- En-tête panier -->
                    <Border Grid.Row="0" 
                          Background="LightBlue"
                          Padding="16,12">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0"
                                     Text="🛒 Panier de vente"
                                     FontWeight="Bold"
                                     FontSize="16"/>
                            
                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <TextBlock Text="{Binding NombreArticles, StringFormat='{}{0} article(s)'}"
                                         Margin="0,0,16,0"
                                         FontWeight="Bold"/>
                                
                                <Button Content="🗑️ VIDER"
                                      Command="{Binding ViderPanierCommand}"
                                      Padding="8,4"
                                      Background="Red"
                                      Foreground="White"
                                      BorderThickness="0"
                                      FontSize="12"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                    
                    <!-- DataGrid des articles -->
                    <DataGrid Grid.Row="1"
                            ItemsSource="{Binding ArticlesVente}"
                            SelectedItem="{Binding ArticleSelectionne}"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            GridLinesVisibility="Horizontal"
                            HeadersVisibility="Column"
                            SelectionMode="Single"
                            RowHeight="60"
                            FontSize="14"
                            Margin="16">
                        
                        <DataGrid.Columns>
                            <!-- Code Article -->
                            <DataGridTextColumn Header="Code" 
                                              Binding="{Binding CodeArticle}" 
                                              Width="100"
                                              FontFamily="Consolas"
                                              FontWeight="Bold"/>
                            
                            <!-- Désignation -->
                            <DataGridTemplateColumn Header="Article" Width="*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock Text="{Binding Designation}" 
                                                     FontWeight="Bold"
                                                     TextWrapping="Wrap"/>
                                            <TextBlock Text="{Binding CodeBarre, StringFormat='Code-barres: {0}'}" 
                                                     FontSize="12"
                                                     Foreground="Gray"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            
                            <!-- Quantité -->
                            <DataGridTemplateColumn Header="Qté" Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="-"
                                                  Command="{Binding DataContext.DiminuerQuantiteCommand, 
                                                          RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                  CommandParameter="{Binding}"
                                                  Width="25" Height="25"
                                                  Background="Red"
                                                  Foreground="White"
                                                  BorderThickness="0"/>
                                            
                                            <TextBox Text="{Binding Quantite, UpdateSourceTrigger=PropertyChanged}"
                                                   Width="40"
                                                   TextAlignment="Center"
                                                   Margin="5,0"
                                                   FontWeight="Bold"/>
                                            
                                            <Button Content="+"
                                                  Command="{Binding DataContext.AugmenterQuantiteCommand, 
                                                          RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                  CommandParameter="{Binding}"
                                                  Width="25" Height="25"
                                                  Background="Green"
                                                  Foreground="White"
                                                  BorderThickness="0"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            
                            <!-- Prix unitaire -->
                            <DataGridTextColumn Header="Prix Unit." 
                                              Binding="{Binding PrixUnitaire, StringFormat=C}" 
                                              Width="100"
                                              FontWeight="Bold"/>
                            
                            <!-- Total ligne -->
                            <DataGridTextColumn Header="Total" 
                                              Binding="{Binding TotalLigne, StringFormat=C}" 
                                              Width="100"
                                              FontWeight="Bold"
                                              Foreground="DarkGreen"/>
                            
                            <!-- Actions -->
                            <DataGridTemplateColumn Header="Actions" Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Content="🗑️"
                                              Command="{Binding DataContext.SupprimerArticleCommand, 
                                                      RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                              CommandParameter="{Binding}"
                                              Padding="8"
                                              Background="Red"
                                              Foreground="White"
                                              BorderThickness="0"
                                              ToolTip="Supprimer"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>
            
            <!-- Actions rapides -->
            <Border Grid.Row="3" 
                    Background="White" 
                    CornerRadius="10" 
                    Padding="16" 
                    Margin="0,16,0,0"
                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                <UniformGrid Columns="4">
                    <Button Content="💊 MÉDICAMENTS"
                            Command="{Binding FiltrerMedicamentsCommand}"
                            Padding="10"
                            Margin="5"
                            Background="Blue"
                            Foreground="White"
                            BorderThickness="0"/>
                    
                    <Button Content="🧴 PARAPHARMACIE"
                            Command="{Binding FiltrerParapharmacieCommand}"
                            Padding="10"
                            Margin="5"
                            Background="Purple"
                            Foreground="White"
                            BorderThickness="0"/>
                    
                    <Button Content="🎁 PROMOTIONS"
                            Command="{Binding FiltrerPromotionsCommand}"
                            Padding="10"
                            Margin="5"
                            Background="Orange"
                            Foreground="White"
                            BorderThickness="0"/>
                    
                    <Button Content="⭐ FAVORIS"
                            Command="{Binding FiltrerFavorisCommand}"
                            Padding="10"
                            Margin="5"
                            Background="Gold"
                            Foreground="Black"
                            BorderThickness="0"/>
                </UniformGrid>
            </Border>
        </Grid>
        
        <!-- Panneau de droite - Totaux et paiement -->
        <Grid Grid.Column="1" Margin="8,0,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Client -->
            <Border Grid.Row="0" 
                    Background="White" 
                    CornerRadius="10" 
                    Padding="16" 
                    Margin="0,0,0,16"
                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                <StackPanel>
                    <TextBlock Text="👤 CLIENT" 
                             FontWeight="Bold" 
                             FontSize="16"
                             Margin="0,0,0,10"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Grid.Column="0"
                               Text="{Binding CodeClient, UpdateSourceTrigger=PropertyChanged}"
                               FontSize="14"
                               Padding="8"
                               Margin="0,0,8,0">
                            <TextBox.InputBindings>
                                <KeyBinding Key="Enter" Command="{Binding RechercherClientCommand}"/>
                            </TextBox.InputBindings>
                        </TextBox>
                        
                        <Button Grid.Column="1"
                              Content="🔍"
                              Command="{Binding RechercherClientCommand}"
                              Padding="8"
                              Background="Blue"
                              Foreground="White"
                              BorderThickness="0"/>
                    </Grid>
                    
                    <TextBlock Text="{Binding ClientSelectionne.NomComplet}" 
                             FontWeight="Bold"
                             Margin="0,10,0,0"
                             Visibility="{Binding ClientSelectionne, Converter={StaticResource NullToVisibilityConverter}}"/>
                    
                    <TextBlock Text="{Binding ClientSelectionne.Telephone}" 
                             FontSize="12"
                             Foreground="Gray"
                             Visibility="{Binding ClientSelectionne, Converter={StaticResource NullToVisibilityConverter}}"/>
                </StackPanel>
            </Border>
            
            <!-- Totaux -->
            <Border Grid.Row="1" 
                    Background="DarkBlue" 
                    CornerRadius="10" 
                    Padding="16" 
                    Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="💰 TOTAUX" 
                             FontWeight="Bold" 
                             FontSize="16"
                             Foreground="White"
                             Margin="0,0,0,15"/>
                    
                    <Grid Margin="0,0,0,8">
                        <TextBlock Text="Sous-total HT :" Foreground="LightGray"/>
                        <TextBlock Text="{Binding SousTotalHT, StringFormat=C}" 
                                 HorizontalAlignment="Right"
                                 Foreground="White"
                                 FontWeight="Bold"/>
                    </Grid>
                    
                    <Grid Margin="0,0,0,8">
                        <TextBlock Text="TVA :" Foreground="LightGray"/>
                        <TextBlock Text="{Binding MontantTVA, StringFormat=C}" 
                                 HorizontalAlignment="Right"
                                 Foreground="White"
                                 FontWeight="Bold"/>
                    </Grid>
                    
                    <Separator Background="White" Margin="0,8"/>
                    
                    <Grid Margin="0,8,0,0">
                        <TextBlock Text="TOTAL TTC :" 
                                 FontSize="18"
                                 FontWeight="Bold"
                                 Foreground="Yellow"/>
                        <TextBlock Text="{Binding TotalTTC, StringFormat=C}" 
                                 HorizontalAlignment="Right"
                                 FontSize="24"
                                 FontWeight="Bold"
                                 Foreground="Yellow"/>
                    </Grid>
                </StackPanel>
            </Border>
            
            <!-- Remises -->
            <Border Grid.Row="2" 
                    Background="White" 
                    CornerRadius="10" 
                    Padding="16" 
                    Margin="0,0,0,16"
                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                <StackPanel>
                    <TextBlock Text="🎯 REMISES" 
                             FontWeight="Bold" 
                             FontSize="14"
                             Margin="0,0,0,10"/>
                    
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Grid.Column="0"
                               Text="{Binding RemisePourcentage, UpdateSourceTrigger=PropertyChanged}"
                               Padding="5"
                               Margin="0,0,5,0"/>
                        
                        <TextBlock Grid.Column="1" 
                                 Text="%" 
                                 VerticalAlignment="Center"
                                 Margin="0,0,5,0"/>
                        
                        <Button Grid.Column="2"
                              Content="✓"
                              Command="{Binding AppliquerRemiseCommand}"
                              Padding="5"
                              Background="Green"
                              Foreground="White"
                              BorderThickness="0"/>
                    </Grid>
                    
                    <TextBlock Text="{Binding MontantRemise, StringFormat='Remise: {0:C}'}" 
                             FontSize="12"
                             Foreground="Green"
                             FontWeight="Bold"/>
                </StackPanel>
            </Border>
            
            <!-- Historique rapide -->
            <Border Grid.Row="3" 
                    Background="White" 
                    CornerRadius="10" 
                    Padding="16" 
                    Margin="0,0,0,16"
                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                <StackPanel>
                    <TextBlock Text="📋 DERNIÈRES VENTES" 
                             FontWeight="Bold" 
                             FontSize="14"
                             Margin="0,0,0,10"/>
                    
                    <ListBox ItemsSource="{Binding DernieresVentes}"
                           Height="200"
                           BorderThickness="0">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Margin="0,5">
                                    <TextBlock Text="{Binding NumeroVente, StringFormat='Vente #{0}'}" 
                                             FontWeight="Bold"
                                             FontSize="12"/>
                                    <TextBlock Text="{Binding DateVente}"
                                             FontSize="10"
                                             Foreground="Gray"/>
                                    <TextBlock Text="{Binding MontantTTC}"
                                             FontSize="12"
                                             Foreground="Green"
                                             FontWeight="Bold"/>
                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </StackPanel>
            </Border>
            
            <!-- Boutons de paiement -->
            <Border Grid.Row="4" 
                    Background="Green" 
                    CornerRadius="10" 
                    Padding="16">
                <StackPanel>
                    <Button Content="💳 PAYER"
                            Command="{Binding PayerCommand}"
                            Padding="20"
                            FontSize="18"
                            FontWeight="Bold"
                            Background="DarkGreen"
                            Foreground="White"
                            BorderThickness="0"
                            Margin="0,0,0,10"/>
                    
                    <UniformGrid Columns="2">
                        <Button Content="💵 ESPÈCES"
                                Command="{Binding PayerEspecesCommand}"
                                Padding="10"
                                Margin="2"
                                Background="Orange"
                                Foreground="White"
                                BorderThickness="0"/>
                        
                        <Button Content="💳 CARTE"
                                Command="{Binding PayerCarteCommand}"
                                Padding="10"
                                Margin="2"
                                Background="Blue"
                                Foreground="White"
                                BorderThickness="0"/>
                    </UniformGrid>
                    
                    <Button Content="🧾 TICKET"
                            Command="{Binding ImprimerTicketCommand}"
                            Padding="10"
                            Margin="0,10,0,0"
                            Background="Purple"
                            Foreground="White"
                            BorderThickness="0"/>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>

</UserControl>
