﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Public Class fListeDesEquivalents


    Dim cmdArticle As New SqlCommand
    Dim daArticle As New SqlDataAdapter
    Dim dsArticle As New DataSet

    Public CodeForme As Integer = 0
    Public Dosage As String = ""
    Public CodeArticleDeRetour As String = ""

    Public DCI1 As String = ""
    Public DCI2 As String = ""
    Public DCI3 As String = ""

    Public Sub AfficherArticle()

        Dim I As Integer
        Dim Cond As String = "1=1"
        Dim Trie As String = " Designation"

        If (dsArticle.Tables.IndexOf("Article") > -1) Then
            dsArticle.Tables("Article").Clear()
        End If

        If DCI1 <> 0 Then
            Cond += " AND DCI1 ='" + DCI1 + "'"
        End If

        If DCI2 <> 0 Then
            Cond += " AND DCI2 ='" + DCI2 + "'"
        End If

        If DCI3 <> 0 Then
            Cond += " AND DCI3 ='" + DCI3 + "'"
        End If

        'Composer la condition de la requête  
        If chbMemeForme.Checked = True Then
            Cond += " AND ARTICLE.CodeForme =" + CodeForme.ToString
        End If

        If chbMemeDosage.Checked = True Then
            Cond += " AND ARTICLE.Dosage ='" + Dosage + "'"
        End If

        If rdbDesignation.Checked = True Then
            Trie = " Designation"
        Else
            Trie = " PrixVenteTTC"
        End If

        If chbDisponibleEnStock.Checked = True Then
            Cond += " AND (SELECT SUM(QteLotArticle) AS Expr1 FROM dbo.LOT_ARTICLE AS LOT_ARTICLE_1 " + _
                    "WHERE (CodeArticle = dbo.ARTICLE.CodeArticle) AND (DatePeremptionArticle > GETDATE() " + _
                    "OR  DatePeremptionArticle IS NULL OR DatePeremptionArticle = '01/01/1900'))>0 "
        End If

        If DCI1 = "" And DCI2 = "" And DCI3 = "" Then
            Cond = " DCI1=9999999999 "
        End If

        cmdArticle.CommandText = "SELECT  ARTICLE.CodeArticle," + _
                                        " CodeABarre," + _
                                        " Designation,  " + _
                                        " FORME_ARTICLE.LibelleForme," + _
                                        " ARTICLE.PrixVenteTTC " + _
                                        " FROM Article" + _
                                        " LEFT OUTER JOIN FORME_ARTICLE ON FORME_ARTICLE.CodeForme=ARTICLE.CodeForme " + _
                                        " WHERE " + Cond + _
                                        " ORDER BY " + Trie

        cmdArticle.Connection = ConnectionServeur
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "ARTICLE")

        With gArticles
            .Columns.Clear()
            .DataSource = dsArticle
            .DataMember = "ARTICLE"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code Article"
            .Columns("Designation").Caption = "Designation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("PrixVenteTTC").Caption = "PV TTC"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 70
            .Splits(0).DisplayColumns("Designation").Width = 250
            .Splits(0).DisplayColumns("LibelleForme").Width = 50
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 50


            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gArticles)
        End With

        Dim Col As New C1.Win.C1TrueDBGrid.C1DataColumn
        Dim dc As C1.Win.C1TrueDBGrid.C1DisplayColumn

        With gArticles
            .Columns.Insert(0, Col)
            Col.Caption = "Stock"
            dc = .Splits(0).DisplayColumns.Item("Stock")
            dc.Width = 40
            .Splits(0).DisplayColumns(4).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns(4).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            dc.Visible = True
            .Rebind(True)
        End With

    End Sub

    Private Sub gArticles_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub gArticle_UnboundColumnFetch(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gArticles.UnboundColumnFetch
        Dim y As String
        y = gArticles(e.Row, ("CodeArticle"))
        e.Value = CalculeStock(y)
    End Sub

    Private Sub fListeDesEquivalents_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        chbMemeDosage.Checked = True
        chbMemeForme.Checked = True
        chbDisponibleEnStock.Checked = True
        rdbDesignation.Checked = True
        AfficherArticle()
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        CodeArticleDeRetour = ""
        Me.Hide()
    End Sub

    Private Sub bOk_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOk.Click
        CodeArticleDeRetour = gArticles.Columns("CodeArticle").Value
        Me.Hide()
    End Sub

    Private Sub chbDisponibleEnStock_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbDisponibleEnStock.CheckedChanged
        AfficherArticle()
    End Sub

    Private Sub PAnel_Paint(ByVal sender As System.Object, ByVal e As System.Windows.Forms.PaintEventArgs) Handles PAnel.Paint

    End Sub

    Private Sub chbMemeDosage_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbMemeDosage.CheckedChanged
        AfficherArticle()
    End Sub

    Private Sub chbMemeForme_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbMemeForme.CheckedChanged
        AfficherArticle()
    End Sub

    Private Sub chbMemeForme_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles chbMemeForme.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub chbMemeDosage_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles chbMemeDosage.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub chbDisponibleEnStock_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles chbDisponibleEnStock.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbDesignation_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbDesignation.CheckedChanged
        AfficherArticle()
    End Sub

    Private Sub rdbDesignation_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbDesignation.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbPrix_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbPrix.CheckedChanged
        AfficherArticle()
    End Sub

    Private Sub rdbPrix_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbPrix.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub gArticles_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gArticles.Click

    End Sub

    Private Sub bOk_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bOk.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub bAnnuler_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bAnnuler.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub
End Class