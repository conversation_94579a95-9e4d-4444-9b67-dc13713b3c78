﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:cl="http://schemas.microsoft.com/sqlserver/reporting/2010/01/componentdefinition" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2010/01/reportdefinition">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="SERVEUR_DS">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString>Data Source=HMARWEN;Initial Catalog=pharma</ConnectString>
        <Prompt>Spécifiez un nom d'utilisateur et un mot de passe pour la source de données DataSource2 :</Prompt>
      </ConnectionProperties>
      <rd:SecurityType>DataBase</rd:SecurityType>
      <rd:DataSourceID>571c9c0d-6679-4707-aecd-de9007a68d08</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DS_Etatdesventes">
      <Query>
        <DataSourceName>SERVEUR_DS</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@DateDebut">
            <Value>=Parameters!DateDebut.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@DateFin">
            <Value>=Parameters!DateFin.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@CodeClient">
            <Value>=Parameters!CodeClient.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@ModePaiement">
            <Value>=Parameters!ModePaiement.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@TypeMutuelle">
            <Value>=Parameters!TypeMutuelle.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@CodePersonnel">
            <Value>=Parameters!CodePersonnel.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@Poste">
            <Value>=Parameters!Poste.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandType>StoredProcedure</CommandType>
        <CommandText>P_Report_EtatDesVentes</CommandText>
      </Query>
      <Fields>
        <Field Name="NumeroOperation">
          <DataField>NumeroOperation</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Id">
          <DataField>Id</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="Date">
          <DataField>Date</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="TotalTTC">
          <DataField>TotalTTC</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="TotalRemise">
          <DataField>TotalRemise</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="TypeOperation">
          <DataField>TypeOperation</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CodeClient">
          <DataField>CodeClient</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CodeNatureReglement">
          <DataField>CodeNatureReglement</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="Nom">
          <DataField>Nom</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MP">
          <DataField>MP</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CodePersonnel">
          <DataField>CodePersonnel</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NomUtilisateur">
          <DataField>NomUtilisateur</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LibellePoste">
          <DataField>LibellePoste</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Type">
          <DataField>Type</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NumeroFacture">
          <DataField>NumeroFacture</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CodeMutuelle">
          <DataField>CodeMutuelle</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Mutuelle">
          <DataField>Mutuelle</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Vide">
          <DataField>Vide</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="MontantCnam">
          <DataField>MontantCnam</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="MontantMutuelle">
          <DataField>MontantMutuelle</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="Credit">
          <DataField>Credit</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="Debit">
          <DataField>Debit</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="DS_Pharmacie">
      <Query>
        <DataSourceName>SERVEUR_DS</DataSourceName>
        <CommandText>SELECT
  PARAMETRE_PHARMACIE.Code
  ,PARAMETRE_PHARMACIE.CodePharmacie
  ,PARAMETRE_PHARMACIE.Pharmacie
  ,PARAMETRE_PHARMACIE.NCnam
  ,PARAMETRE_PHARMACIE.Affiliation1
  ,PARAMETRE_PHARMACIE.Affiliation2
  ,PARAMETRE_PHARMACIE.Adresse
  ,PARAMETRE_PHARMACIE.Telephone
  ,PARAMETRE_PHARMACIE.Fax
  ,PARAMETRE_PHARMACIE.CodeTVA
  ,PARAMETRE_PHARMACIE.Rib
  ,PARAMETRE_PHARMACIE.Messagederoulant1
  ,PARAMETRE_PHARMACIE.Messagederoulant2
  ,PARAMETRE_PHARMACIE.Timbre
  ,PARAMETRE_PHARMACIE.DemandeMotDePasse
  ,PARAMETRE_PHARMACIE.DateMigration
  ,PARAMETRE_PHARMACIE.SmtpMail
  ,PARAMETRE_PHARMACIE.PortMail
  ,PARAMETRE_PHARMACIE.AdresseMailDestinateur
  ,PARAMETRE_PHARMACIE.SujetMail
  ,PARAMETRE_PHARMACIE.TexteMail
  ,PARAMETRE_PHARMACIE.MotDePasseDestinateur
  ,PARAMETRE_PHARMACIE.AutoriserEnvoiMail
  ,PARAMETRE_PHARMACIE.NbreJourValiditeParDefaut
  ,PARAMETRE_PHARMACIE.NumeroLotProduction
  ,PARAMETRE_PHARMACIE.Latitude_Longitude
  ,PARAMETRE_PHARMACIE.TailleCodeCNAM
  ,PARAMETRE_PHARMACIE.TailleListe
  ,PARAMETRE_PHARMACIE.TailleCaractere
  ,PARAMETRE_PHARMACIE.PoliceCaractere
  ,PARAMETRE_PHARMACIE.Texte
  ,PARAMETRE_PHARMACIE.TauxRemise
  ,PARAMETRE_PHARMACIE.CodeGSU
  ,PARAMETRE_PHARMACIE.ImageCodeABarre
  ,PARAMETRE_PHARMACIE.ActiverOMFAPCI
FROM
  PARAMETRE_PHARMACIE</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="Code">
          <DataField>Code</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CodePharmacie">
          <DataField>CodePharmacie</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Pharmacie">
          <DataField>Pharmacie</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NCnam">
          <DataField>NCnam</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Affiliation1">
          <DataField>Affiliation1</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Affiliation2">
          <DataField>Affiliation2</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Adresse">
          <DataField>Adresse</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Telephone">
          <DataField>Telephone</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Fax">
          <DataField>Fax</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CodeTVA">
          <DataField>CodeTVA</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Rib">
          <DataField>Rib</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Messagederoulant1">
          <DataField>Messagederoulant1</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Messagederoulant2">
          <DataField>Messagederoulant2</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Timbre">
          <DataField>Timbre</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="DemandeMotDePasse">
          <DataField>DemandeMotDePasse</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="DateMigration">
          <DataField>DateMigration</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="SmtpMail">
          <DataField>SmtpMail</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PortMail">
          <DataField>PortMail</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="AdresseMailDestinateur">
          <DataField>AdresseMailDestinateur</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SujetMail">
          <DataField>SujetMail</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TexteMail">
          <DataField>TexteMail</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MotDePasseDestinateur">
          <DataField>MotDePasseDestinateur</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="AutoriserEnvoiMail">
          <DataField>AutoriserEnvoiMail</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="NbreJourValiditeParDefaut">
          <DataField>NbreJourValiditeParDefaut</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="NumeroLotProduction">
          <DataField>NumeroLotProduction</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Latitude_Longitude">
          <DataField>Latitude_Longitude</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TailleCodeCNAM">
          <DataField>TailleCodeCNAM</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TailleListe">
          <DataField>TailleListe</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TailleCaractere">
          <DataField>TailleCaractere</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PoliceCaractere">
          <DataField>PoliceCaractere</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Texte">
          <DataField>Texte</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TauxRemise">
          <DataField>TauxRemise</DataField>
          <rd:TypeName>System.Int16</rd:TypeName>
        </Field>
        <Field Name="CodeGSU">
          <DataField>CodeGSU</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ImageCodeABarre">
          <DataField>ImageCodeABarre</DataField>
          <rd:TypeName>System.Byte[]</rd:TypeName>
        </Field>
        <Field Name="ActiverOMFAPCI">
          <DataField>ActiverOMFAPCI</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Textbox Name="ReportTitle">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ETAT DES VENTES</Value>
                    <Style>
                      <FontFamily>Verdana</FontFamily>
                      <FontSize>20pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:WatermarkTextbox>Title</rd:WatermarkTextbox>
            <rd:DefaultName>ReportTitle</rd:DefaultName>
            <Top>0.70556mm</Top>
            <Left>8.14917cm</Left>
            <Height>10.16mm</Height>
            <Width>118.3474mm</Width>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </TopBorder>
              <BottomBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </BottomBorder>
              <LeftBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </RightBorder>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Rectangle Name="Rectangle1">
            <ReportItems>
              <Textbox Name="DateDebut">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!DateDebut.Value</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>DateDebut</rd:DefaultName>
                <Top>0mm</Top>
                <Left>29.57033mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox2">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Période du :</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>0mm</Top>
                <Left>1.93699mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox4">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Au :</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox4</rd:DefaultName>
                <Top>0mm</Top>
                <Left>56.33421mm</Left>
                <Height>6mm</Height>
                <Width>10.95835mm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="DateFin">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!DateFin.Value</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>DateFin</rd:DefaultName>
                <Top>0mm</Top>
                <Left>69.05645mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox3">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Type :</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>7.8027mm</Top>
                <Left>1.93699mm</Left>
                <Height>6mm</Height>
                <Width>12.82917mm</Width>
                <ZIndex>4</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox6">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>M. P.:</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>14.86104mm</Top>
                <Left>1.93699mm</Left>
                <Height>6mm</Height>
                <Width>12.82917mm</Width>
                <ZIndex>5</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox8">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Client :</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>7.41111mm</Top>
                <Left>43.63421mm</Left>
                <Height>6mm</Height>
                <Width>15.73958mm</Width>
                <ZIndex>6</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox10">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Vendeur:</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>14.46945mm</Top>
                <Left>43.63421mm</Left>
                <Height>6mm</Height>
                <Width>15.73958mm</Width>
                <ZIndex>7</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox12">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Poste:</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>7.41111mm</Top>
                <Left>95.75391mm</Left>
                <Height>6mm</Height>
                <Width>11.77083mm</Width>
                <ZIndex>8</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="TypeMutuelle">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!TypeMutuelle.Value</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>TypeMutuelle</rd:DefaultName>
                <Top>7.8027mm</Top>
                <Left>16.17727mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <ZIndex>9</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="ModePaiement">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!ModePaiement.Value</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>ModePaiement</rd:DefaultName>
                <Top>14.86104mm</Top>
                <Left>16.17727mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <ZIndex>10</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="CodeClient">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!CodeClient.Value</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>CodeClient</rd:DefaultName>
                <Top>7.05833mm</Top>
                <Left>61.13768mm</Left>
                <Height>6mm</Height>
                <Width>30.29167mm</Width>
                <ZIndex>11</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Poste">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!Poste.Value</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Poste</rd:DefaultName>
                <Top>7.41111mm</Top>
                <Left>108.58307mm</Left>
                <Height>6mm</Height>
                <Width>6.33124mm</Width>
                <ZIndex>12</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="CodePersonnel">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!CodePersonnel.Value</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>CodePersonnel</rd:DefaultName>
                <Top>14.46944mm</Top>
                <Left>61.13768mm</Left>
                <Height>6mm</Height>
                <Width>30.29167mm</Width>
                <ZIndex>13</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>11.96271mm</Top>
            <Left>81.49167mm</Left>
            <Height>22.35417mm</Height>
            <Width>118.3474mm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </TopBorder>
              <BottomBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </BottomBorder>
              <LeftBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </RightBorder>
            </Style>
          </Rectangle>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>22.02378mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>34.10372mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>19.97952mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>18.37916mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>18.00631mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>48.23492mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>23.16449mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>10.82266mm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Numéro</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox5">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Date</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox5</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox22">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Type</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox22</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox26">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>M. P.</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox26</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox7">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Total TTC</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox7</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox24">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Client</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox24</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox9">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Vendeur</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox9</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox11">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Poste</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox11</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="NumeroOperation">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!NumeroOperation.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>NumeroOperation</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Date">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Date.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Date</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Mutuelle">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Mutuelle.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Mutuelle</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox27">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!MP.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox27</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TotalTTC">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TotalTTC.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>TotalTTC</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Nom">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Nom.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Nom</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="NomUtilisateur">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!NomUtilisateur.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>NomUtilisateur</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="LibellePoste">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!LibellePoste.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>LibellePoste</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Détails" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DS_Etatdesventes</DataSetName>
            <Filters>
              <Filter>
                <FilterExpression>=Fields!TypeOperation.Value</FilterExpression>
                <Operator>Equal</Operator>
                <FilterValues>
                  <FilterValue>Vente</FilterValue>
                </FilterValues>
              </Filter>
            </Filters>
            <Top>37.53908mm</Top>
            <Left>5.12451mm</Left>
            <Height>12mm</Height>
            <Width>194.71456mm</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Rectangle Name="Rectangle3">
            <ReportItems>
              <Textbox Name="Textbox14">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>PHARMACIE</Value>
                        <Style>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>1.41111mm</Top>
                <Left>20.90208mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox16">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Code TVA :</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>25.72659mm</Top>
                <Left>3.175mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox20">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Téléphone :</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>18.31548mm</Top>
                <Left>3.175mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Pharmacie">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=First(Fields!Pharmacie.Value, "DS_Pharmacie")</Value>
                        <Style>
                          <FontSize>14pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Pharmacie</rd:DefaultName>
                <Top>7.81756mm</Top>
                <Left>3.31041mm</Left>
                <Height>9.43959mm</Height>
                <Width>64.6875mm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Telephone">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=First(Fields!Telephone.Value, "DS_Pharmacie")</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Telephone</rd:DefaultName>
                <Top>18.31548mm</Top>
                <Left>32.67916mm</Left>
                <Height>6mm</Height>
                <Width>35.31875mm</Width>
                <ZIndex>4</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="CodeTVA">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=First(Fields!CodeTVA.Value, "DS_Pharmacie")</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>CodeTVA</rd:DefaultName>
                <Top>26.07937mm</Top>
                <Left>32.67916mm</Left>
                <Height>6mm</Height>
                <Width>35.31875mm</Width>
                <ZIndex>5</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>0.70556mm</Top>
            <Left>5.12451mm</Left>
            <Height>33.5725mm</Height>
            <Width>72.75416mm</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
            </Style>
          </Rectangle>
          <Tablix Name="Tablix2">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>16.00417mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>32.67292mm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox17">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Espèce:</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox17</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox37">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum( iif(Fields!CodeNatureReglement.Value ="1", Fields!Credit.Value, CDec(0)))</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox37</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox25">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Chèque:</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox25</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox38">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum( iif(Fields!CodeNatureReglement.Value ="2", Fields!Credit.Value, CDec(0)))</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox38</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox33">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Carte:</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox33</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox39">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum( iif(Fields!CodeNatureReglement.Value ="4", Fields!Credit.Value, CDec(0)))</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox39</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox35">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Crédit</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox35</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox40">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum(Fields!Debit.Value) - SUM(Fields!Credit.Value)</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DS_Etatdesventes</DataSetName>
            <Top>55.79533mm</Top>
            <Left>5.12451mm</Left>
            <Height>24mm</Height>
            <Width>48.67709mm</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </TopBorder>
              <BottomBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </BottomBorder>
              <LeftBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </RightBorder>
            </Style>
          </Tablix>
          <Tablix Name="Tablix3">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>20.76667mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>32.67292mm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox18">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>TotalTTC :</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox17</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox41">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum(Fields!TotalTTC.Value)</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox37</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox28">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>CNAM:</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox25</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox42">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum(Fields!MontantCnam.Value)</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox38</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox34">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Mutuelle :</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox33</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox43">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum(Fields!MontantMutuelle.Value)</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox39</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox36">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Remise:</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox35</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox44">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum(Fields!TotalRemise.Value)</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox40</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox49">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Nb Ventes:</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox49</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Telephone2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Count(Fields!Id.Value, "DS_Etatdesventes")</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Telephone</rd:DefaultName>
                          <ZIndex>6</ZIndex>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DS_Etatdesventes</DataSetName>
            <Top>52.00853mm</Top>
            <Left>146.39948mm</Left>
            <Height>30mm</Height>
            <Width>53.43959mm</Width>
            <ZIndex>5</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </TopBorder>
              <BottomBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </BottomBorder>
              <LeftBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </RightBorder>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>84.4995mm</Height>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Body>
      <Width>207.89951mm</Width>
      <Page>
        <PageHeader>
          <Height>5.55625mm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageFooter>
          <Height>7.46125mm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="ExecutionTime">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Globals!ExecutionTime</Value>
                      <Style />
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>ExecutionTime</rd:DefaultName>
              <Top>0mm</Top>
              <Left>0.22659mm</Left>
              <Height>6.35mm</Height>
              <Width>35.98333mm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageFooter>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>21cm</PageWidth>
        <LeftMargin>2cm</LeftMargin>
        <RightMargin>2cm</RightMargin>
        <TopMargin>2cm</TopMargin>
        <BottomMargin>2cm</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="DateDebut">
      <DataType>String</DataType>
      <Prompt>Date Debut</Prompt>
    </ReportParameter>
    <ReportParameter Name="DateFin">
      <DataType>String</DataType>
      <Prompt>Date Fin</Prompt>
    </ReportParameter>
    <ReportParameter Name="CodeClient">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <Prompt>Code Client</Prompt>
    </ReportParameter>
    <ReportParameter Name="ModePaiement">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>Mode Paiement</Prompt>
    </ReportParameter>
    <ReportParameter Name="TypeMutuelle">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <Prompt>Type Mutuelle</Prompt>
    </ReportParameter>
    <ReportParameter Name="CodePersonnel">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>Code Personnel</Prompt>
    </ReportParameter>
    <ReportParameter Name="Poste">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>Poste</Prompt>
    </ReportParameter>
  </ReportParameters>
  <rd:ReportUnitType>Mm</rd:ReportUnitType>
  <rd:ReportID>53014682-0b5b-4e63-901d-99a3ea010ba1</rd:ReportID>
</Report>