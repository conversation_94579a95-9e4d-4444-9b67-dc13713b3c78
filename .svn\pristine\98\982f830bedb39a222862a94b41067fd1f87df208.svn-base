﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fStockParCategorie

    Dim dsListe As New DataSet
    Dim cmdListe As New SqlCommand
    Dim daListe As New SqlDataAdapter
    Dim cbListe As New SqlCommandBuilder

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub Init()
        Dim i As Integer
        Dim TotalAchat As Decimal = 0
        Dim TotalVente As Decimal = 0

        'Vider dataset
        dsListe.Clear()

        'Initialiser la gride
        cmdListe.CommandText = " SELECT LibelleCategorie, " + _
                               " ValeurAchatHT, 0.0 AS ValeurAchatHTP, " + _
                               " ValeurVenteHT, 0.0 AS ValeurVenteHTP " + _
                               " FROM Vue_StockParCategorie " + _
                               " GROUP BY LibelleCategorie, ValeurAchatHT, ValeurVenteHT "

        cmdListe.Connection = ConnectionServeur
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "StockParCategorie")

        With gListe
            .Columns.Clear()
            .DataSource = dsListe
            .DataMember = "StockParCategorie"
            .Rebind(False)
            .Columns("LibelleCategorie").Caption = "Categorie"
            .Columns("ValeurAchatHT").Caption = "Valeur Achat HT"
            .Columns("ValeurAchatHTP").Caption = "%"
            .Columns("ValeurVenteHT").Caption = "Valeur Vente HT"
            .Columns("ValeurVenteHTP").Caption = "%"

            'Centrer tous les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = AlignHorzEnum.Far
            Next

            .Splits(0).DisplayColumns("LibelleCategorie").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(0).DisplayColumns("LibelleCategorie").Width = 420
            .Splits(0).DisplayColumns("ValeurAchatHT").Width = 100
            .Splits(0).DisplayColumns("ValeurAchatHTP").Width = 100
            .Splits(0).DisplayColumns("ValeurVenteHT").Width = 100
            .Splits(0).DisplayColumns("ValeurVenteHTP").Width = 100

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True

            '''''Calcule les totaux
            For i = 0 To gListe.RowCount - 1
                TotalAchat += gListe(i, "ValeurAchatHT")
                TotalVente += gListe(i, "ValeurVenteHT")
            Next

            lTotAchat.Text = Math.Round(TotalAchat, 3).ToString("### ### ##0.000")
            lTotVente.Text = Math.Round(TotalVente, 3).ToString("### ### ##0.000")

            For i = 0 To gListe.RowCount - 1
                If TotalAchat <> 0 Then
                    gListe(i, "ValeurAchatHTP") = Math.Round(gListe(i, "ValeurAchatHT") * 100 / TotalAchat, 3)
                End If
                If TotalVente <> 0 Then
                    gListe(i, "ValeurVenteHTP") = Math.Round(gListe(i, "ValeurVenteHT") * 100 / TotalVente, 3)
                End If
            Next
            'Style du Caractere et du grid
            ParametreGrid(gListe)
        End With

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click

        Dim I As Integer

        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression du stock par categrie" Then
                num = I
            End If
        Next
        etatStockParCategorie1.FileName = Application.StartupPath + "\EtatStockParCategorie.rpt"

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo

        Try
            For Each tbCurrent In etatStockParCategorie1.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent
        Catch ex As Exception
            For Each tbCurrent In etatStockParCategorie1.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent
        End Try

        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = etatStockParCategorie1
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression du stock par categrie"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    Private Sub gListe_DoubleClick(sender As Object, e As EventArgs) Handles gListe.DoubleClick
        Dim fStockParCategorieIntervalleMarge As New fStockParCategorieIntervalleMarge
        fStockParCategorieIntervalleMarge.LibelleCategorie = gListe.Columns("LibelleCategorie").Value
        fStockParCategorieIntervalleMarge.Init()
        fStockParCategorieIntervalleMarge.ShowDialog()
    End Sub

    Private Sub C1Button1_Click(sender As Object, e As EventArgs)
        Dim fStockParCategorieIntervalleMarge As New fStockParCategorieIntervalleMarge
        fStockParCategorieIntervalleMarge.LibelleCategorie = gListe.Columns("LibelleCategorie").Value
        fStockParCategorieIntervalleMarge.Init()
        fStockParCategorieIntervalleMarge.ShowDialog()
    End Sub

    Private Sub bStockIntervalleMarge_Click(sender As Object, e As EventArgs) Handles bStockIntervalleMarge.Click
        Dim fStockParCategorieIntervalleMarge As New fStockParCategorieIntervalleMarge
        fStockParCategorieIntervalleMarge.LibelleCategorie = gListe.Columns("LibelleCategorie").Value
        fStockParCategorieIntervalleMarge.Init()
        fStockParCategorieIntervalleMarge.ShowDialog()
    End Sub
End Class