using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PharmaModerne.Core.Interfaces;
using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace PharmaModerne.UI.ViewModels
{
    /// <summary>
    /// ViewModel principal pour la fenêtre principale de PHARMA2000 Moderne
    /// </summary>
    public partial class MainWindowViewModel : ObservableObject
    {
        private readonly IScannerService _scannerService;

        public MainWindowViewModel(IScannerService scannerService)
        {
            _scannerService = scannerService;
            
            // Initialisation
            CurrentPageTitle = "Dashboard";
            CurrentUser = "Utilisateur Demo";
            IsMenuOpen = true;
            
            // Abonnement aux événements du scanner
            _scannerService.CodeScanned += OnCodeScanned;
            _scannerService.ScannerActivated += OnScannerActivated;
            _scannerService.ScannerDeactivated += OnScannerDeactivated;
        }

        #region Propriétés observables

        [ObservableProperty]
        private string currentPageTitle = "Dashboard";

        [ObservableProperty]
        private string currentUser = "Utilisateur";

        [ObservableProperty]
        private bool isMenuOpen = true;

        [ObservableProperty]
        private bool isScannerActive = false;

        [ObservableProperty]
        private string scannerIcon = "Radar";

        [ObservableProperty]
        private string globalSearchText = string.Empty;

        [ObservableProperty]
        private object? currentView;

        [ObservableProperty]
        private bool hasNotifications = false;

        [ObservableProperty]
        private int notificationCount = 0;

        #endregion

        #region Commandes de navigation

        [RelayCommand]
        private void ToggleMenu()
        {
            IsMenuOpen = !IsMenuOpen;
        }

        [RelayCommand]
        private async Task ToggleScanner()
        {
            try
            {
                if (IsScannerActive)
                {
                    await _scannerService.DeactivateAsync();
                }
                else
                {
                    await _scannerService.ActivateAsync();
                }
            }
            catch (Exception ex)
            {
                // Gérer l'erreur
                MessageBox.Show($"Erreur scanner : {ex.Message}", "Erreur", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void GlobalSearch()
        {
            if (string.IsNullOrWhiteSpace(GlobalSearchText))
                return;

            // Logique de recherche globale
            CurrentPageTitle = $"Recherche : {GlobalSearchText}";
            
            // TODO: Implémenter la recherche globale
            MessageBox.Show($"Recherche globale pour : {GlobalSearchText}", "Recherche", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region Commandes des modules - Ventes

        [RelayCommand]
        private void OpenVente()
        {
            CurrentPageTitle = "Nouvelle Vente";
            // TODO: Charger la vue de vente
            MessageBox.Show("Module Nouvelle Vente", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void OpenVenteList()
        {
            CurrentPageTitle = "Liste des Ventes";
            // TODO: Charger la liste des ventes
            MessageBox.Show("Module Liste des Ventes", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void OpenCaisse()
        {
            CurrentPageTitle = "Caisse";
            // TODO: Charger le module caisse
            MessageBox.Show("Module Caisse", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region Commandes des modules - Clients

        [RelayCommand]
        private void OpenClient()
        {
            CurrentPageTitle = "Nouveau Client";
            // TODO: Charger la vue de création client
            MessageBox.Show("Module Nouveau Client", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void OpenClientList()
        {
            CurrentPageTitle = "Liste des Clients";
            // TODO: Charger la liste des clients
            MessageBox.Show("Module Liste des Clients", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void OpenClientScanner()
        {
            CurrentPageTitle = "Scanner Client";
            // TODO: Charger le module scanner client
            MessageBox.Show("Module Scanner Client", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region Commandes des modules - Articles

        [RelayCommand]
        private void OpenArticle()
        {
            CurrentPageTitle = "Nouvel Article";
            // TODO: Charger la vue de création article
            MessageBox.Show("Module Nouvel Article", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void OpenArticleList()
        {
            CurrentPageTitle = "Liste des Articles";
            // TODO: Charger la liste des articles
            MessageBox.Show("Module Liste des Articles", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void OpenStock()
        {
            CurrentPageTitle = "Gestion Stock";
            // TODO: Charger le module de gestion stock
            MessageBox.Show("Module Gestion Stock", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void OpenStockAlerts()
        {
            CurrentPageTitle = "Alertes Stock";
            // TODO: Charger les alertes de stock
            MessageBox.Show("Module Alertes Stock", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region Commandes des modules - Fournisseurs

        [RelayCommand]
        private void OpenFournisseur()
        {
            CurrentPageTitle = "Nouveau Fournisseur";
            // TODO: Charger la vue de création fournisseur
            MessageBox.Show("Module Nouveau Fournisseur", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void OpenFournisseurList()
        {
            CurrentPageTitle = "Liste des Fournisseurs";
            // TODO: Charger la liste des fournisseurs
            MessageBox.Show("Module Liste des Fournisseurs", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void OpenCommandes()
        {
            CurrentPageTitle = "Commandes";
            // TODO: Charger le module commandes
            MessageBox.Show("Module Commandes", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region Commandes des modules - Rapports

        [RelayCommand]
        private void OpenDashboard()
        {
            CurrentPageTitle = "Dashboard";
            // TODO: Charger le dashboard
            MessageBox.Show("Module Dashboard", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void OpenAnalyses()
        {
            CurrentPageTitle = "Analyses Ventes";
            // TODO: Charger les analyses
            MessageBox.Show("Module Analyses Ventes", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void OpenInventaires()
        {
            CurrentPageTitle = "Inventaires";
            // TODO: Charger le module inventaires
            MessageBox.Show("Module Inventaires", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region Commandes des modules - Administration

        [RelayCommand]
        private void OpenUtilisateurs()
        {
            CurrentPageTitle = "Utilisateurs";
            // TODO: Charger la gestion des utilisateurs
            MessageBox.Show("Module Utilisateurs", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void OpenParametres()
        {
            CurrentPageTitle = "Paramètres";
            // TODO: Charger les paramètres
            MessageBox.Show("Module Paramètres", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void OpenSecurite()
        {
            CurrentPageTitle = "Sécurité";
            // TODO: Charger le module sécurité
            MessageBox.Show("Module Sécurité", "Navigation", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region Commandes système

        [RelayCommand]
        private void OpenNotifications()
        {
            // TODO: Ouvrir le panneau de notifications
            MessageBox.Show("Notifications", "Système", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void OpenSettings()
        {
            // TODO: Ouvrir les paramètres
            MessageBox.Show("Paramètres système", "Système", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void Logout()
        {
            var result = MessageBox.Show("Êtes-vous sûr de vouloir vous déconnecter ?", 
                                       "Déconnexion", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                // TODO: Logique de déconnexion
                Application.Current.Shutdown();
            }
        }

        #endregion

        #region Gestion des événements du scanner

        private void OnCodeScanned(object? sender, string code)
        {
            try
            {
                Application.Current?.Dispatcher?.Invoke(() =>
                {
                    // Traiter le code scanné
                    GlobalSearchText = code;

                    // Déclencher une recherche automatique
                    GlobalSearchCommand?.Execute(null);
                });
            }
            catch (Exception ex)
            {
                // Log l'erreur sans planter l'application
                System.Diagnostics.Debug.WriteLine($"Erreur OnCodeScanned: {ex.Message}");
            }
        }

        private void OnScannerActivated(object? sender, object e)
        {
            try
            {
                Application.Current?.Dispatcher?.Invoke(() =>
                {
                    IsScannerActive = true;
                    ScannerIcon = "RadarScan";

                    // Afficher une notification
                    // TODO: Implémenter le système de notifications
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur OnScannerActivated: {ex.Message}");
            }
        }

        private void OnScannerDeactivated(object? sender, object e)
        {
            try
            {
                Application.Current?.Dispatcher?.Invoke(() =>
                {
                    IsScannerActive = false;
                    ScannerIcon = "Radar";

                    // Afficher une notification
                    // TODO: Implémenter le système de notifications
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur OnScannerDeactivated: {ex.Message}");
            }
        }

        #endregion

        #region Nettoyage

        protected override void OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs e)
        {
            base.OnPropertyChanged(e);
            
            // Logique supplémentaire si nécessaire
        }

        public void Dispose()
        {
            // Désabonnement des événements
            if (_scannerService != null)
            {
                _scannerService.CodeScanned -= OnCodeScanned;
                _scannerService.ScannerActivated -= OnScannerActivated;
                _scannerService.ScannerDeactivated -= OnScannerDeactivated;
            }
        }

        #endregion
    }
}
