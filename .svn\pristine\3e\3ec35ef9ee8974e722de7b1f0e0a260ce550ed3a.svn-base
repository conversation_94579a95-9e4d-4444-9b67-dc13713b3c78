//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class RELEVE_MUTUELLE_DETAILS
    {
        public RELEVE_MUTUELLE_DETAILS()
        {
            this.REGLEMENT_MUTUELLE_VENTE = new HashSet<REGLEMENT_MUTUELLE_VENTE>();
        }
    
        public string NumeroReleve { get; set; }
        public string NumeroVente { get; set; }
        public System.DateTime Date { get; set; }
        public string CodeClient { get; set; }
        public System.DateTime DateOrdonnance { get; set; }
        public decimal TotalTTC { get; set; }
        public decimal Regle { get; set; }
        public decimal MontantMutuelle { get; set; }
    
        public virtual ICollection<REGLEMENT_MUTUELLE_VENTE> REGLEMENT_MUTUELLE_VENTE { get; set; }
        public virtual RELEVE_MUTUELLE RELEVE_MUTUELLE { get; set; }
        public virtual VENTE VENTE { get; set; }
    }
}
