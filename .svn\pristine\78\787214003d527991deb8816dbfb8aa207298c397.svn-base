﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fListeArticleSansInventaire
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fListeArticleSansInventaire))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.bImprimer = New C1.Win.C1Input.C1Button()
        Me.gListe = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.groupbox = New System.Windows.Forms.GroupBox()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.dtpFin = New C1.Win.C1Input.C1DateEdit()
        Me.dtpDebut = New C1.Win.C1Input.C1DateEdit()
        Me.cmbCategorie = New C1.Win.C1List.C1Combo()
        Me.cmbForme = New C1.Win.C1List.C1Combo()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.lDu = New System.Windows.Forms.Label()
        Me.etatArticlePerime1 = New Pharma2000Premium.EtatArticlePerime()
        Me.Panel.SuspendLayout()
        CType(Me.gListe, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.groupbox.SuspendLayout()
        CType(Me.dtpFin, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpDebut, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbCategorie, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbForme, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.bImprimer)
        Me.Panel.Controls.Add(Me.gListe)
        Me.Panel.Controls.Add(Me.groupbox)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1028, 534)
        Me.Panel.TabIndex = 9
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(903, 12)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(113, 45)
        Me.bQuitter.TabIndex = 79
        Me.bQuitter.Text = "Fermer            F12   "
        Me.bQuitter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bImprimer
        '
        Me.bImprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.Imprimante
        Me.bImprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bImprimer.Location = New System.Drawing.Point(903, 60)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(113, 45)
        Me.bImprimer.TabIndex = 78
        Me.bImprimer.Text = "Imprimer                F9"
        Me.bImprimer.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gListe
        '
        Me.gListe.AllowUpdate = False
        Me.gListe.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gListe.CaptionHeight = 17
        Me.gListe.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gListe.GroupByCaption = "Drag a column header here to group by that column"
        Me.gListe.Images.Add(CType(resources.GetObject("gListe.Images"), System.Drawing.Image))
        Me.gListe.LinesPerRow = 2
        Me.gListe.Location = New System.Drawing.Point(12, 110)
        Me.gListe.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gListe.Name = "gListe"
        Me.gListe.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gListe.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gListe.PreviewInfo.ZoomFactor = 75.0R
        Me.gListe.PrintInfo.PageSettings = CType(resources.GetObject("gListe.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gListe.RowHeight = 15
        Me.gListe.Size = New System.Drawing.Size(1004, 394)
        Me.gListe.TabIndex = 18
        Me.gListe.Text = "C1TrueDBGrid1"
        Me.gListe.PropBag = resources.GetString("gListe.PropBag")
        '
        'groupbox
        '
        Me.groupbox.Controls.Add(Me.Label7)
        Me.groupbox.Controls.Add(Me.dtpFin)
        Me.groupbox.Controls.Add(Me.dtpDebut)
        Me.groupbox.Controls.Add(Me.cmbCategorie)
        Me.groupbox.Controls.Add(Me.cmbForme)
        Me.groupbox.Controls.Add(Me.Label3)
        Me.groupbox.Controls.Add(Me.Label2)
        Me.groupbox.Controls.Add(Me.lDu)
        Me.groupbox.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.groupbox.ForeColor = System.Drawing.SystemColors.ControlText
        Me.groupbox.Location = New System.Drawing.Point(12, 12)
        Me.groupbox.Name = "groupbox"
        Me.groupbox.Size = New System.Drawing.Size(857, 92)
        Me.groupbox.TabIndex = 14
        Me.groupbox.TabStop = False
        Me.groupbox.Text = "Critére de recherche"
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.Location = New System.Drawing.Point(49, 59)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(20, 13)
        Me.Label7.TabIndex = 78
        Me.Label7.Text = "Au"
        '
        'dtpFin
        '
        Me.dtpFin.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpFin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpFin.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpFin.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFin.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFin.Location = New System.Drawing.Point(104, 59)
        Me.dtpFin.Name = "dtpFin"
        Me.dtpFin.Size = New System.Drawing.Size(120, 18)
        Me.dtpFin.TabIndex = 77
        Me.dtpFin.Tag = Nothing
        Me.dtpFin.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'dtpDebut
        '
        Me.dtpDebut.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDebut.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDebut.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDebut.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebut.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebut.Location = New System.Drawing.Point(104, 22)
        Me.dtpDebut.Name = "dtpDebut"
        Me.dtpDebut.Size = New System.Drawing.Size(120, 18)
        Me.dtpDebut.TabIndex = 76
        Me.dtpDebut.Tag = Nothing
        Me.dtpDebut.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebut.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbCategorie
        '
        Me.cmbCategorie.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbCategorie.Caption = ""
        Me.cmbCategorie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbCategorie.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbCategorie.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbCategorie.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbCategorie.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbCategorie.Images.Add(CType(resources.GetObject("cmbCategorie.Images"), System.Drawing.Image))
        Me.cmbCategorie.Location = New System.Drawing.Point(503, 18)
        Me.cmbCategorie.MatchEntryTimeout = CType(2000, Long)
        Me.cmbCategorie.MaxDropDownItems = CType(5, Short)
        Me.cmbCategorie.MaxLength = 32767
        Me.cmbCategorie.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbCategorie.Name = "cmbCategorie"
        Me.cmbCategorie.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbCategorie.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbCategorie.Size = New System.Drawing.Size(130, 22)
        Me.cmbCategorie.TabIndex = 67
        Me.cmbCategorie.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbCategorie.PropBag = resources.GetString("cmbCategorie.PropBag")
        '
        'cmbForme
        '
        Me.cmbForme.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbForme.Caption = ""
        Me.cmbForme.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbForme.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbForme.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbForme.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbForme.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbForme.Images.Add(CType(resources.GetObject("cmbForme.Images"), System.Drawing.Image))
        Me.cmbForme.Location = New System.Drawing.Point(287, 18)
        Me.cmbForme.MatchEntryTimeout = CType(2000, Long)
        Me.cmbForme.MaxDropDownItems = CType(5, Short)
        Me.cmbForme.MaxLength = 32767
        Me.cmbForme.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbForme.Name = "cmbForme"
        Me.cmbForme.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbForme.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbForme.Size = New System.Drawing.Size(137, 22)
        Me.cmbForme.TabIndex = 66
        Me.cmbForme.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbForme.PropBag = resources.GetString("cmbForme.PropBag")
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.Location = New System.Drawing.Point(445, 23)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(52, 13)
        Me.Label3.TabIndex = 51
        Me.Label3.Text = "Catégorie"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(245, 22)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(36, 13)
        Me.Label2.TabIndex = 49
        Me.Label2.Text = "Forme"
        '
        'lDu
        '
        Me.lDu.AutoSize = True
        Me.lDu.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDu.Location = New System.Drawing.Point(9, 25)
        Me.lDu.Name = "lDu"
        Me.lDu.Size = New System.Drawing.Size(60, 13)
        Me.lDu.TabIndex = 44
        Me.lDu.Text = "Période Du"
        '
        'fListeArticleSansInventaire
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1028, 534)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fListeArticleSansInventaire"
        Me.Text = "fListeArticlePerime"
        Me.Panel.ResumeLayout(False)
        CType(Me.gListe, System.ComponentModel.ISupportInitialize).EndInit()
        Me.groupbox.ResumeLayout(False)
        Me.groupbox.PerformLayout()
        CType(Me.dtpFin, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpDebut, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbCategorie, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbForme, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents gListe As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents groupbox As System.Windows.Forms.GroupBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents lDu As System.Windows.Forms.Label
    Friend WithEvents cmbCategorie As C1.Win.C1List.C1Combo
    Friend WithEvents cmbForme As C1.Win.C1List.C1Combo
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
    Friend WithEvents dtpDebut As C1.Win.C1Input.C1DateEdit
    Friend WithEvents etatArticlePerime1 As Pharma2000Premium.EtatArticlePerime
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents dtpFin As C1.Win.C1Input.C1DateEdit
End Class
