﻿Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms
Imports System.Net

Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared

Imports System.Collections.Generic
Imports System.Net.WebRequestMethods.Ftp

Imports System.Net.Sockets
Imports System.Text

Public Class fUpdateArticle

    Dim cmdUpdate As New SqlCommand
    Dim cbUpdate As New SqlCommandBuilder
    Dim dsUpdate As New DataSet
    Dim daUpdate As New SqlDataAdapter

    Dim cmd As New SqlCommand

    Dim dr As DataRow = Nothing
    Dim drArticleModif As DataRow = Nothing

    Dim instance As FtpWebRequest

    Dim Jour As String = ""
    Dim Mois As String = ""
    Dim Annee As String = ""

    Dim Dossier_Du_Jour As String = ""
    Dim Nom_Fichier_A_Telecharger As String = ""
    Dim Nom_Fichier_Local As String = ""

    Dim WithEvents Client As New FtpConnection()

    Dim StrSQL As String = ""

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub
    Public Sub init()

        Annee = System.DateTime.Now.Year.ToString
        Mois = System.DateTime.Now.Month.ToString
        If Mois.Length < 2 Then
            Mois = "0" + Mois
        End If
        Jour = System.DateTime.Now.Day.ToString
        If Jour.Length < 2 Then
            Jour = "0" + Jour
        End If

        If LecteurUpdate = "" Then
            MsgBox("Veuillez vérifier votre Lecteur Update !", MsgBoxStyle.Critical, "Vérification de la Connexion")
            AffichageDUneListeVide()
            Exit Sub
        End If

        If LecteurUpdate.Substring(LecteurUpdate.Length - 1, 1) <> "\" Then
            LecteurUpdate += "\"
        End If


        Dossier_Du_Jour = Annee + "-" + Mois + "-" + Jour
        Nom_Fichier_A_Telecharger = CodePharmacien + "-" + Annee + "-" + Mois + "-" + Jour + ".af"
        'Nom_Fichier_A_Telecharger = Dossier_Du_Jour + CodePharmacien + "-" + Annee + "-" + Mois + "-" + Jour + ".af"
        'Nom_Fichier_Local = LecteurUpdate + CodePharmacien + "-" + Annee + "-" + Mois + "-" + Jour + ".af"

        'Paramètrage de la connexion
        'Client.Hostname = "************"
        'Client.Username = "PHARMA2000"
        'Client.Password = "Next;3U7+s4"

        'recuperation des parametres FTP des parametres generaux de lapplication
        Dim cmdRecupereHostname As New SqlCommand
        StrSQL = " SELECT TOP(1) hostname FROM PARAMETRE_PHARMACIE"
        cmdRecupereHostname.Connection = ConnectionServeur
        cmdRecupereHostname.CommandText = StrSQL
        Client.Hostname = cmdRecupereHostname.ExecuteScalar()

        Dim cmdRecupereUsername As New SqlCommand
        StrSQL = " SELECT TOP(1) username FROM PARAMETRE_PHARMACIE"
        cmdRecupereUsername.Connection = ConnectionServeur
        cmdRecupereUsername.CommandText = StrSQL
        Client.Username = cmdRecupereUsername.ExecuteScalar()

        Dim cmdRecuperePassword As New SqlCommand
        StrSQL = " SELECT TOP(1) password FROM PARAMETRE_PHARMACIE"
        cmdRecuperePassword.Connection = ConnectionServeur
        cmdRecuperePassword.CommandText = StrSQL
        Client.Password = cmdRecuperePassword.ExecuteScalar()

        Dim FtpInstance As New FTP
        FtpInstance.ConnectFTP(Client.Hostname, Client.Username, Client.Password)
        FtpInstance.DownloadFile(Dossier_Du_Jour, Nom_Fichier_A_Telecharger, LecteurUpdate + Nom_Fichier_A_Telecharger)

        If ConnectionFailed = True Then
            MsgBox("Veuillez vérifier votre connexion internet !", MsgBoxStyle.Critical, "Vérification de la Connexion")
            AffichageDUneListeVide()
            Exit Sub
        End If

        Dim fs As Object
        fs = CreateObject("Scripting.FileSystemObject")
        If fs.FileExists(LecteurUpdate + Nom_Fichier_A_Telecharger) = False Then
            MsgBox("Vous devez vérifier votre code Pharma2000 !", MsgBoxStyle.Critical, "Information")
            AffichageDUneListeVide()
            Exit Sub
        End If

        Dim monStreamReader As StreamReader = New StreamReader(LecteurUpdate + Nom_Fichier_A_Telecharger)
        Dim ligne As String = ""
        ligne = monStreamReader.ReadLine()
        If ligne.Contains("Bonjour") = False Then
            MsgBox("Vous n'ête pas sous contrat !", MsgBoxStyle.Critical, "Information")
            monStreamReader.Close()
            File.Delete(LecteurUpdate + Nom_Fichier_A_Telecharger)
            Exit Sub
        End If
        monStreamReader.Close()
        File.Delete(LecteurUpdate + Nom_Fichier_A_Telecharger)

        Nom_Fichier_A_Telecharger = "Presentation" + Annee + "-" + Mois + "-" + Jour + ".txt"
        FtpInstance.DownloadFile(Dossier_Du_Jour, Nom_Fichier_A_Telecharger, LecteurUpdate + Nom_Fichier_A_Telecharger)

        If ConnectionFailed = True Then
            MsgBox("Veuillez vérifier votre connexion internet !", MsgBoxStyle.Critical, "Vérification de la Connexion")
            AffichageDUneListeVide()
            Exit Sub
        End If

        'accusé de recéption
        File.CreateText(LecteurUpdate + CodePharmacien + "-" + Annee + "-" + Mois + "-" + Jour + ".ok").Close()

        FtpInstance.Upload(Dossier_Du_Jour + "\" + CodePharmacien + "-" + Annee + "-" + Mois + "-" + Jour + ".ok", LecteurUpdate + CodePharmacien + "-" + Annee + "-" + Mois + "-" + Jour + ".ok")

        AffichageDUneListeVide()
        ChargemenetDesMisesAJours(LecteurUpdate + Nom_Fichier_A_Telecharger)
        rdbTous.Checked = True
        rdbCode.Checked = True
        AfficherLesDifferences()

        File.Delete(LecteurUpdate + CodePharmacien + "-" + Annee + "-" + Mois + "-" + Jour + ".ok")
        File.Delete(LecteurUpdate + Nom_Fichier_A_Telecharger)
        bCocherTous.Enabled = False

        '' Connexion au serveur      //   ancien  code
        'Client.Connect()

        'If Not Client.IsConnected Then
        '    MsgBox("Problème de connexion au serveur Pharma2000Update, veuillez verifier votre connexion internet !", MsgBoxStyle.Critical, "Information")
        '    Exit Sub
        'Else

        '    ' Download du fichier autorisation

        '    If Not Client.DownloadFile(Nom_Fichier_A_Telecharger, "c:\" + Nom_Fichier_Local) Then '"2012-01-02\10029-2012-01-02.af"
        '        MsgBox("Problème de connexion au serveur Pharme2000Update, veuillez verifier votre connexion internet !", MsgBoxStyle.Critical, "Information")
        '        Exit Sub
        '    End If

        '    ' verification du fichier

        '    Dim monStreamReader As StreamReader = New StreamReader(Nom_Fichier_Local)
        '    Dim ligne As String = ""
        '    ligne = monStreamReader.ReadLine()
        '    If ligne.Contains("Bonjour") = False Then
        '        MsgBox("Vous n'ête pas sous contrat !", MsgBoxStyle.Critical, "Information")
        '        Exit Sub
        '    End If
        '    monStreamReader.Close()
        '    File.Delete(Nom_Fichier_Local)

        '    ' download du fichier mise a jour

        '    Nom_Fichier_A_Telecharger = Dossier_Du_Jour + "Presentation" + Annee + "-" + Mois + "-" + Jour + ".txt"
        '    Nom_Fichier_Local = LecteurUpdate + "Presentation" + "-" + Annee + "-" + Mois + "-" + Jour + ".txt"

        '    If Not Client.DownloadFile(Nom_Fichier_A_Telecharger, Nom_Fichier_Local) Then   '"2012-01-02\10029-2012-01-02.af"
        '        MsgBox("Problème de connexion au serveur Pharme2000Update, veuillez verifier votre connexion internet !", MsgBoxStyle.Critical, "Information")
        '        Exit Sub
        '    End If

        '    ' upload du fichier accusé

        '    File.CreateText(LecteurUpdate + CodePharmacien + "-" + Annee + "-" + Mois + "-" + Jour + ".ok")
        '    If Not Client.UploadFile(LecteurUpdate + CodePharmacien + "-" + Annee + "-" + Mois + "-" + Jour + ".ok", Dossier_Du_Jour + CodePharmacien + "-" + Annee + "-" + Mois + "-" + Jour + ".ok") Then
        '        MsgBox("Problème lors de l'envoi de l accusé de recéption !", MsgBoxStyle.Critical, "Information")
        '    End If
        '    File.Delete(LecteurUpdate + CodePharmacien + "-" + Annee + "-" + Mois + "-" + Jour + ".ok")
        '    AffichageDUneListeVide()
        '    ChargemenetDesMisesAJours(Nom_Fichier_Local)
        '    rdbTous.Checked = True
        '    AfficherLesDifferences()

        'End If
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub
    Private Sub ChargemenetDesMisesAJours(ByVal Nom_Fichier_Local As String)

        Dim monStreamReader1 As StreamReader = New StreamReader(Nom_Fichier_Local)
        Dim Ligne As String = ""
        Dim StrSQL As String = ""
        Dim I As Integer = 0
        Dim PrixPahramacien As Double = 0.0
        Dim PrixPublic As Double = 0.0
        Dim AccordPrealable As Boolean = False
        Dim tarifReference As Double = 0.0
        Dim CategorieCNAM As String = ""
        Dim Changement As String = ""
        Dim cmdchangmentCodePCT As New SqlCommand
        'Dim cmd As New SqlCommand

        cmd.Connection = ConnectionServeur
        cmd.CommandText = "DELETE FROM ARTICLE_MODIFICATION"
        cmd.ExecuteScalar()

        cmd.Connection = ConnectionServeur
        cmd.CommandText = "DELETE FROM ARTICLE_MISE_A_JOUR"
        cmd.ExecuteScalar()

        ' chargement de la table de mise à jour en mémoire 
        StrSQL = "SELECT Top (0) CodePCT," + _
                "Designation," + _
                "Forme," + _
                "Labo," + _
                "PrixAchat, " + _
                "PrixHT," + _
                "Tableau," + _
                "TarifDeReference," + _
                "CategorieCNAM," + _
                "Accordprealable," + _
                "NumeroCirculaire," + _
                "DateCirculaire " + _
                "FROM " + _
                "ARTICLE_MISE_A_JOUR "
        cmdUpdate.Connection = ConnectionServeur
        cmdUpdate.CommandText = StrSQL
        daUpdate = New SqlDataAdapter(cmdUpdate)
        daUpdate.Fill(dsUpdate, "ARTICLE_MISE_A_JOUR")
        cbUpdate = New SqlCommandBuilder(daUpdate)

        ' préparation de la table de mise à jour accepté en mémoire 
        StrSQL = "SELECT Top (0) CodePCT," + _
                "Designation," + _
                "Forme," + _
                "Labo," + _
                "PrixAchat, " + _
                "PrixHT," + _
                "Tableau," + _
                "TarifDeReference," + _
                "CategorieCNAM," + _
                "Accordprealable," + _
                "Changement," + _
                "NumeroCirculaire," + _
                "DateCirculaire," + _
                "CAST(0 as bit) Valider," + _
                "CAST(0 as bit) Ignorer " + _
                "FROM " + _
                "ARTICLE_MODIFICATION "
        cmdUpdate.Connection = ConnectionServeur
        cmdUpdate.CommandText = StrSQL
        daUpdate = New SqlDataAdapter(cmdUpdate)
        daUpdate.Fill(dsUpdate, "ARTICLE_MODIFICATION")
        cbUpdate = New SqlCommandBuilder(daUpdate)

        ' lecture du fichier mise à jour et recherche des différences 

        Do
            Ligne = monStreamReader1.ReadLine
            If Ligne <> Nothing Then
                If Ligne.Length = 237 Then
                    dr = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").NewRow()

                    dr.Item("CodePCT") = Ligne.Substring(0, 10)
                    dr.Item("Designation") = Ligne.Substring(10, 50)
                    dr.Item("Forme") = Ligne.Substring(60, 30)
                    dr.Item("Labo") = Ligne.Substring(90, 30)
                    dr.Item("PrixAchat") = Ligne.Substring(120, 15)
                    dr.Item("PrixHT") = Ligne.Substring(135, 15)
                    dr.Item("Tableau") = Ligne.Substring(190, 1)
                    dr.Item("TarifDeReference") = Ligne.Substring(191, 10)
                    dr.Item("CategorieCNAM") = Ligne.Substring(201, 15)

                    If Ligne.Substring(216, 1) = "F" Then
                        dr.Item("Accordprealable") = False
                    Else
                        dr.Item("Accordprealable") = True
                    End If

                    dr.Item("NumeroCirculaire") = Ligne.Substring(217, 10)
                    dr.Item("DateCirculaire") = Ligne.Substring(227, 10)

                    dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows.Add(dr)
                End If
            End If
        Loop Until Ligne Is Nothing
        monStreamReader1.Close()

        '******************************************************************************************************************************************************
        '********************************************* recherche des différences entre les bases **************************************************************
        '******************************************************************************************************************************************************

        'Dim J As Integer
        Dim unite As Double = 0.0
       
        ProgressBar.Visible = True
        GroupeJauge.Visible = True
        ProgressBar.Value = 0

        unite = 100 / (dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows.Count - 1)

        'Arrêter la capture d'evenement clavier sur le contrôle 
        RemoveHandler Me.bQuitter.Click, AddressOf Me.bQuitter_Click
        ' pour empecher l'annulation : elle cause des problemes il faut 
        ' attendre la fin de l'execusion

        For I = 0 To dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows.Count - 1
            If dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CodePCT").ToString = "307417    " Then
                Changement = ""
            End If
            If unite * I < (dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows.Count - 1) Then
                If (unite * I) <= 100 Then
                    ProgressBar.Value = unite * I
                Else
                    ProgressBar.Value = 100
                End If
            End If

            Application.DoEvents()

            lArticleEnCours.Text = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("Designation")

            Changement = ""

            'test si il est parmi les articles ignorées ou non 
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "SELECT COUNT(CodePCT) FROM ARTICLE_IGNORER WHERE CodePCT='" + dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CodePCT") + "'"

            'If Trim(dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CodePCT")) = "104283" Then
            '    MessageBox.Show("jjjj")
            'End If

            If cmd.ExecuteScalar = 0 Then

                'test si l'article existe ou nn 
                cmd.Connection = ConnectionServeur
                cmd.CommandText = "SELECT COUNT(CodeArticle) FROM ARTICLE WHERE CodePCT='" + dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CodePCT") + "'"

                'test si larticle a changer de code PCT ou nn
                cmdchangmentCodePCT.Connection = ConnectionServeur
                cmdchangmentCodePCT.CommandText = "select count(*) from article where( select SOUNDEX(designation))=(select SOUNDEX('" + dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("Designation") + "')) and supprime=0 and codesituation <> 3 and codepct <> ' " + dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CodePCT") + "'"

                If cmd.ExecuteScalar > 0 Then

                    'si l'article existe dans la base

                    PrixPahramacien = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodePCT", dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CodePCT"))
                    PrixPublic = RecupererValeurExecuteScalaire("PrixVenteTTC", "ARTICLE", "CodePCT", dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CodePCT"))
                    AccordPrealable = RecupererValeurExecuteScalaire("AccordPrealable", "ARTICLE", "CodePCT", dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CodePCT"))
                    tarifReference = RecupererValeurExecuteScalaire("TarifDeReference", "ARTICLE", "CodePCT", dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CodePCT"))
                    CategorieCNAM = RecupererValeurExecuteScalaire("LibelleCategorie", "CATEGORIE_CNAM", "CodeCategorie", RecupererValeurExecuteScalaire("CodeCategorieCNAM", "ARTICLE", "CodePCT", dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CodePCT")))

                    If dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("PrixAchat") <> 0 Then
                        If Math.Abs(dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("PrixAchat") - PrixPahramacien) > 0.001 Then
                            Changement = "Prix Pharmacien "
                        End If

                        If Math.Abs(dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("PrixHT") - PrixPublic) > 0.001 Then
                            If Changement = "" Then
                                Changement = "Prix Public "
                            Else
                                Changement += " / Prix Public "
                            End If
                        End If
                    End If

                    If dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("Accordprealable") <> AccordPrealable Then
                        If Changement = "" Then
                            Changement = "Accord préalable "
                        Else
                            Changement += " / Accord préalable "
                        End If
                    End If

                    If dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("TarifDeReference") <> tarifReference And dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("Accordprealable") = True Then
                        If Changement = "" Then
                            Changement = "Tarif de reférence "
                        Else
                            Changement += " / Tarif de reférence "
                        End If
                    End If

                    If Trim(dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CategorieCNAM").ToString).ToUpper <> CategorieCNAM Then
                        If Changement = "" Then
                            Changement = "Categorie Cnam "
                        Else
                            Changement += " / Categorie Cnam "
                        End If
                    End If

     

                    If Changement <> "" Then

                        drArticleModif = dsUpdate.Tables("ARTICLE_MODIFICATION").NewRow()

                        drArticleModif.Item("CodePCT") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CodePCT")
                        drArticleModif.Item("Designation") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("Designation")
                        drArticleModif.Item("Forme") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("Forme")
                        drArticleModif.Item("Labo") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("Labo")
                        drArticleModif.Item("PrixAchat") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("PrixAchat")
                        drArticleModif.Item("PrixHT") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("PrixHT")
                        drArticleModif.Item("Tableau") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("Tableau")
                        drArticleModif.Item("TarifDeReference") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("TarifDeReference")
                        drArticleModif.Item("CategorieCNAM") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CategorieCNAM")

                        drArticleModif.Item("Accordprealable") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("Accordprealable")

                        If drArticleModif.Item("Accordprealable") = False Then
                            drArticleModif.Item("TarifDeReference") = 0.0
                        End If

                        drArticleModif.Item("Changement") = Changement
                        drArticleModif.Item("NumeroCirculaire") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("NumeroCirculaire")
                        drArticleModif.Item("DateCirculaire") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("DateCirculaire")

                        drArticleModif.Item("Valider") = False
                        drArticleModif.Item("Ignorer") = False

                        dsUpdate.Tables("ARTICLE_MODIFICATION").Rows.Add(drArticleModif)

                    End If

                ElseIf cmdchangmentCodePCT.ExecuteScalar > 0 Then
                    If Changement = "" Then
                        Changement = "code PCT "
                    End If

                    Else

                        'si un nouvel article qui n'existe pas dans la base 

                        If dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("PrixAchat") = 0 Then
                            Changement = "Article absent"
                        Else
                            Changement = "Nouvel article"
                        End If

                        drArticleModif = dsUpdate.Tables("ARTICLE_MODIFICATION").NewRow()

                        drArticleModif.Item("CodePCT") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CodePCT")
                        drArticleModif.Item("Designation") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("Designation")
                        drArticleModif.Item("Forme") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("Forme")
                        drArticleModif.Item("Labo") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("Labo")
                        drArticleModif.Item("PrixAchat") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("PrixAchat")
                        drArticleModif.Item("PrixHT") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("PrixHT")
                        drArticleModif.Item("Tableau") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("Tableau")
                        drArticleModif.Item("TarifDeReference") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("TarifDeReference")
                        drArticleModif.Item("CategorieCNAM") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("CategorieCNAM")

                        drArticleModif.Item("Accordprealable") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("Accordprealable")

                        If drArticleModif.Item("Accordprealable") = False Then
                            drArticleModif.Item("TarifDeReference") = 0.0
                        End If

                        drArticleModif.Item("Changement") = Changement
                        drArticleModif.Item("NumeroCirculaire") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("NumeroCirculaire")
                        drArticleModif.Item("DateCirculaire") = dsUpdate.Tables("ARTICLE_MISE_A_JOUR").Rows(I).Item("DateCirculaire")

                        drArticleModif.Item("Valider") = False
                        drArticleModif.Item("Ignorer") = False

                        dsUpdate.Tables("ARTICLE_MODIFICATION").Rows.Add(drArticleModif)

                    End If

            End If

        Next

        'Reprendre la capture d'evenement clavier sur le contrôle 
        AddHandler Me.bQuitter.Click, AddressOf Me.bQuitter_Click

        ProgressBar.Value = 100
        ProgressBar.Visible = False
        GroupeJauge.Visible = False


        Try
            daUpdate.Update(dsUpdate, "ARTICLE_MODIFICATION")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsUpdate.Reset()
        End Try

        ' ajout dans l 'historique des updates

        cmd.Connection = ConnectionServeur
        cmd.CommandText = "INSERT INTO HISTORIQUE_UPDATE (""Date""" + _
                                             ",""Action""" + _
                                             ",""CodePCT""" + _
                                             ",""Designation""" + _
                                             ",""Changement""" + _
                                             ",""NumeroCirculaire""" + _
                                             ",""DateCirculaire"") " + _
                          "VALUES ('" + System.DateTime.Now + _
                                  "','Recherche des nouvelles mise a jour" + _
                                  "','-" + _
                                  "','-" + _
                                  "','-" + _
                                  "','-" + _
                                  "',NULL) "

        Try
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

    End Sub
    Private Sub AffichageDUneListeVide()
        Dim I As Integer = 0
        Dim Cond As String = " 1=1 "

        ' chargement de la table de mise à jour accepté en mémoire 
        StrSQL = "SELECT TOP(0)  CodePCT," + _
                "Designation," + _
                "Forme," + _
                "Labo," + _
                "PrixAchat, " + _
                "PrixHT," + _
                "Tableau," + _
                "TarifDeReference," + _
                "CategorieCNAM," + _
                "Accordprealable," + _
                "Changement," + _
                "CAST(0 as bit) Valider," + _
                "CAST(0 as bit) Ignorer, " + _
                "NumeroCirculaire," + _
                "DateCirculaire " + _
                "FROM " + _
                "ARTICLE_MODIFICATION "

        cmdUpdate.Connection = ConnectionServeur
        cmdUpdate.CommandText = StrSQL
        daUpdate = New SqlDataAdapter(cmdUpdate)
        daUpdate.Fill(dsUpdate, "ARTICLE_MODIFICATION_UPDATE1")
        cbUpdate = New SqlCommandBuilder(daUpdate)

        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsUpdate
            Catch ex As Exception
            End Try
            .DataMember = "ARTICLE_MODIFICATION_UPDATE1"
            .Rebind(False)
            .Columns("CodePCT").Caption = "Code PCT"
            .Columns("Designation").Caption = "Désignation"
            .Columns("PrixAchat").Caption = "Prix Pharmacien"
            .Columns("PrixHT").Caption = "Prix Public"
            .Columns("TarifDeReference").Caption = " Tarif De Réf"
            .Columns("CategorieCNAM").Caption = "Cat CNAM"
            .Columns("Accordprealable").Caption = "Acc pré"
            .Columns("Changement").Caption = "Changement"
            .Columns("Valider").Caption = "Valider"

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Changement").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchat").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("PrixHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TarifDeReference").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("CodePCT").Width = 100
            .Splits(0).DisplayColumns("Designation").Width = 400
            .Splits(0).DisplayColumns("PrixAchat").Width = 80
            .Splits(0).DisplayColumns("PrixAchat").Visible = False
            .Splits(0).DisplayColumns("PrixHT").Width = 80
            .Splits(0).DisplayColumns("PrixHT").Visible = False

            .Splits(0).DisplayColumns("TarifDeReference").Width = 80
            .Splits(0).DisplayColumns("TarifDeReference").Visible = False
            .Splits(0).DisplayColumns("CategorieCNAM").Width = 80
            .Splits(0).DisplayColumns("CategorieCNAM").Visible = False
            .Splits(0).DisplayColumns("Accordprealable").Width = 30
            .Splits(0).DisplayColumns("Accordprealable").Visible = False

            .Splits(0).DisplayColumns("Changement").Width = 250
            .Splits(0).DisplayColumns("Changement").Visible = False

            .Splits(0).DisplayColumns("Forme").Width = 0
            .Splits(0).DisplayColumns("Forme").Visible = False
            .Splits(0).DisplayColumns("Labo").Width = 0
            .Splits(0).DisplayColumns("Labo").Visible = False
            .Splits(0).DisplayColumns("Tableau").Width = 0
            .Splits(0).DisplayColumns("Tableau").Visible = False

            .Splits(0).DisplayColumns("Valider").Width = 50
            .Splits(0).DisplayColumns("Valider").Visible = False
            .Splits(0).DisplayColumns("Ignorer").Width = 50
            .Splits(0).DisplayColumns("Ignorer").Visible = False

            .Splits(0).DisplayColumns("Valider").Locked = False
            .Splits(0).DisplayColumns("Ignorer").Locked = False

            .Splits(0).DisplayColumns("NumeroCirculaire").Visible = False
            .Splits(0).DisplayColumns("DateCirculaire").Visible = False

            .Splits(0).SplitSize = 500
            .Splits(0).SplitSizeMode = SizeModeEnum.Exact
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone



            ' deuxieme split 

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(1).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(1).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(1).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("Changement").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("PrixAchat").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("PrixHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("TarifDeReference").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(1).DisplayColumns("CodePCT").Width = 50
            .Splits(1).DisplayColumns("CodePCT").Visible = False
            .Splits(1).DisplayColumns("Designation").Width = 250
            .Splits(1).DisplayColumns("Designation").Visible = False
            .Splits(1).DisplayColumns("PrixAchat").Width = 80
            .Splits(1).DisplayColumns("PrixHT").Width = 80

            .Splits(1).DisplayColumns("TarifDeReference").Width = 80
            .Splits(1).DisplayColumns("CategorieCNAM").Width = 80
            .Splits(1).DisplayColumns("Accordprealable").Width = 30

            .Splits(1).DisplayColumns("Changement").Width = 250

            .Splits(1).DisplayColumns("Forme").Width = 0
            .Splits(1).DisplayColumns("Forme").Visible = False
            .Splits(1).DisplayColumns("Labo").Width = 0
            .Splits(1).DisplayColumns("Labo").Visible = False
            .Splits(1).DisplayColumns("Tableau").Width = 0
            .Splits(1).DisplayColumns("Tableau").Visible = False

            .Splits(1).DisplayColumns("Valider").Width = 50
            .Splits(1).DisplayColumns("Ignorer").Width = 50

            .Splits(1).DisplayColumns("Valider").Locked = False
            .Splits(1).DisplayColumns("Ignorer").Locked = False

            .Splits(1).ColumnCaptionHeight = 40
            .Splits(1).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

            'Style du Caractere et du grid
            ParametreGrid(gArticles)
        End With
    End Sub
    Private Sub AfficherLesDifferences()
        Dim I As Integer = 0
        Dim Cond As String = " 1=1 "
        Dim Trie As String = ""

        If rdbCode.Checked = True Then
            Trie = "  ORDER BY CodePCT "
        End If

        If rdbDesignation.Checked = True Then
            Trie = "  ORDER BY Designation "
        End If

        If rdbDateCirculaire.Checked = True Then
            Trie = "  ORDER BY DateCirculaire DESC "
        End If


        If rdbNouveauxArticles.Checked = True Then
            Cond += " AND Changement LIKE '%Nouvel article%'"
        End If

        If rdbChangementsPrix.Checked = True Then
            Cond += " AND (Changement LIKE '%Prix Pharmacien%' OR Changement LIKE '%Prix Public%') "
        End If

        If rdbChangementsCNAM.Checked = True Then
            Cond += " AND (Changement LIKE '%Accord préalable%' OR Changement LIKE '%Tarif de reférence%' OR Changement LIKE '%Categorie Cnam%')"
        End If

        If rdbChangemenstPrixCNAM.Checked = True Then
            Cond += " AND (Changement LIKE '%Prix Pharmacien%' OR Changement LIKE '%Prix Public%' OR Changement LIKE '%Accord préalable%' OR Changement LIKE '%Tarif de reférence%' OR Changement LIKE '%Categorie Cnam%' )"
        End If

        If rdbArticlesAbsents.Checked = True Then
            Cond += " AND Changement LIKE '%Article absent%'"
        End If

        If rbChangPCT.Checked = True Then
            Cond += " AND Changement LIKE '%PCT%'"
        End If

        If dsUpdate.Tables.IndexOf("ARTICLE_MODIFICATION_UPDATE") > -1 Then
            dsUpdate.Tables("ARTICLE_MODIFICATION_UPDATE").Clear()
        End If

        ' chargement de la table de mise à jour accepté en mémoire 
        StrSQL = "SELECT  CodePCT," + _
                "Designation," + _
                "Forme," + _
                "Labo," + _
                "PrixAchat, " + _
                "PrixHT," + _
                "Tableau," + _
                "TarifDeReference," + _
                "CategorieCNAM," + _
                "Accordprealable," + _
                "Changement," + _
                "CAST(0 as bit) Valider," + _
                "CAST(0 as bit) Ignorer, " + _
                "NumeroCirculaire," + _
                "DateCirculaire " + _
                "FROM " + _
                "ARTICLE_MODIFICATION " + _
                "WHERE " + Cond + Trie

        cmdUpdate.Connection = ConnectionServeur
        cmdUpdate.CommandText = StrSQL
        daUpdate = New SqlDataAdapter(cmdUpdate)
        daUpdate.Fill(dsUpdate, "ARTICLE_MODIFICATION_UPDATE")
        cbUpdate = New SqlCommandBuilder(daUpdate)

        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsUpdate
            Catch ex As Exception
            End Try
            .DataMember = "ARTICLE_MODIFICATION_UPDATE"
            .Rebind(False)
            .Columns("CodePCT").Caption = "Code PCT"
            .Columns("Designation").Caption = "Désignation"
            .Columns("PrixAchat").Caption = "Prix Pharmacien"
            .Columns("PrixHT").Caption = "Prix Public"
            .Columns("TarifDeReference").Caption = " Tarif De Réf"
            .Columns("CategorieCNAM").Caption = "Cat CNAM"
            .Columns("Accordprealable").Caption = "Acc pré"
            .Columns("Changement").Caption = "Changement"
            .Columns("Valider").Caption = "Valider"
            .Columns("NumeroCirculaire").Caption = "Numero Circulaire"
            .Columns("DateCirculaire").Caption = "Date Circulaire"

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Changement").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchat").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("PrixHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TarifDeReference").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("CodePCT").Width = 100
            .Splits(0).DisplayColumns("Designation").Width = 400
            .Splits(0).DisplayColumns("PrixAchat").Width = 50
            .Splits(0).DisplayColumns("PrixAchat").Visible = False
            .Splits(0).DisplayColumns("PrixHT").Width = 50
            .Splits(0).DisplayColumns("PrixHT").Visible = False

            .Splits(0).DisplayColumns("TarifDeReference").Width = 70
            .Splits(0).DisplayColumns("TarifDeReference").Visible = False
            .Splits(0).DisplayColumns("CategorieCNAM").Width = 80
            .Splits(0).DisplayColumns("CategorieCNAM").Visible = False
            .Splits(0).DisplayColumns("Accordprealable").Width = 30
            .Splits(0).DisplayColumns("Accordprealable").Visible = False

            .Splits(0).DisplayColumns("Changement").Width = 250
            .Splits(0).DisplayColumns("Changement").Visible = False

            .Splits(0).DisplayColumns("Forme").Width = 0
            .Splits(0).DisplayColumns("Forme").Visible = False
            .Splits(0).DisplayColumns("Labo").Width = 0
            .Splits(0).DisplayColumns("Labo").Visible = False
            .Splits(0).DisplayColumns("Tableau").Width = 0
            .Splits(0).DisplayColumns("Tableau").Visible = False

            .Splits(0).DisplayColumns("Valider").Width = 50
            .Splits(0).DisplayColumns("Valider").Visible = False
            .Splits(0).DisplayColumns("Ignorer").Width = 50
            .Splits(0).DisplayColumns("Ignorer").Visible = False

            .Splits(0).DisplayColumns("Valider").Locked = False
            .Splits(0).DisplayColumns("Ignorer").Locked = False

            .Splits(0).DisplayColumns("NumeroCirculaire").Visible = False
            .Splits(0).DisplayColumns("DateCirculaire").Visible = False

            .Splits(0).SplitSize = 500
            .Splits(0).SplitSizeMode = SizeModeEnum.Exact
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone



            ' deuxieme split 

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(1).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(1).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(1).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("Changement").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("PrixAchat").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("PrixHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("TarifDeReference").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(1).DisplayColumns("CodePCT").Width = 50
            .Splits(1).DisplayColumns("CodePCT").Visible = False
            .Splits(1).DisplayColumns("Designation").Width = 250
            .Splits(1).DisplayColumns("Designation").Visible = False
            .Splits(1).DisplayColumns("PrixAchat").Width = 80
            .Splits(1).DisplayColumns("PrixHT").Width = 80

            .Splits(1).DisplayColumns("TarifDeReference").Width = 80
            .Splits(1).DisplayColumns("CategorieCNAM").Width = 80
            .Splits(1).DisplayColumns("Accordprealable").Width = 30

            .Splits(1).DisplayColumns("Changement").Width = 250

            .Splits(1).DisplayColumns("Forme").Width = 0
            .Splits(1).DisplayColumns("Forme").Visible = False
            .Splits(1).DisplayColumns("Labo").Width = 0
            .Splits(1).DisplayColumns("Labo").Visible = False
            .Splits(1).DisplayColumns("Tableau").Width = 0
            .Splits(1).DisplayColumns("Tableau").Visible = False

            .Splits(1).DisplayColumns("Valider").Width = 50
            .Splits(1).DisplayColumns("Ignorer").Width = 50

            .Splits(1).DisplayColumns("Tableau").Locked = True
            .Splits(1).DisplayColumns("Labo").Locked = True
            .Splits(1).DisplayColumns("Forme").Locked = True
            .Splits(1).DisplayColumns("Changement").Locked = True
            .Splits(1).DisplayColumns("AccordPrealable").Locked = True
            .Splits(1).DisplayColumns("CategorieCNAM").Locked = True
            .Splits(1).DisplayColumns("TarifDeReference").Locked = True
            .Splits(1).DisplayColumns("PrixAchat").Locked = True
            .Splits(1).DisplayColumns("PrixHT").Locked = True
            .Splits(1).DisplayColumns("NumeroCirculaire").Locked = True
            .Splits(1).DisplayColumns("DateCirculaire").Locked = True

            .Splits(1).DisplayColumns("Valider").Locked = False
            .Splits(1).DisplayColumns("Ignorer").Locked = False

            .Splits(1).ColumnCaptionHeight = 40
            .Splits(1).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

        End With

        lNombreArticle.Text = (dsUpdate.Tables("ARTICLE_MODIFICATION_UPDATE").Rows.Count).ToString + "  Articles"

    End Sub

    Private Sub rdbNouveauxArticles_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbNouveauxArticles.CheckedChanged
        AfficherLesDifferences()
        bCocherTous.Enabled = False
    End Sub

    Private Sub rdbTous_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbTous.CheckedChanged
        AfficherLesDifferences()
        bCocherTous.Enabled = False
    End Sub

    Private Sub rdbChangementsPrix_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbChangementsPrix.CheckedChanged
        AfficherLesDifferences()
        bCocherTous.Enabled = True
    End Sub

    Private Sub rdbChangementsCNAM_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbChangementsCNAM.CheckedChanged
        AfficherLesDifferences()
        bCocherTous.Enabled = True
    End Sub

    Private Sub rdbChangemenstPrixCNAM_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbChangemenstPrixCNAM.CheckedChanged
        AfficherLesDifferences()
        bCocherTous.Enabled = True
    End Sub

    Private Sub rdbArticlesAbsents_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbArticlesAbsents.CheckedChanged
        AfficherLesDifferences()
        bCocherTous.Enabled = False
    End Sub

    Private Sub bAccepterLesModifications_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAccepterLesModifications.Click
        Dim I As Integer = 0
        Dim CodePCT As String = ""
        Dim MessageChangement As String = ""
        Dim PrixAchat As Double = 0.0
        Dim PrixPublic As Double = 0.0
        Dim TarifeReference As Double = 0.0
        Dim CategorieCNAM As String = ""
        Dim AccordPrealable As Boolean = False
        Dim Designation As String = ""
        Dim NumeroCirculaire As String = ""
        Dim DateCirculaire As DateTime
        Dim ExisteMiseAjour As Boolean = False

        'For I = 0 To dsUpdate.Tables("ARTICLE_MODIFICATION_UPDATE").Rows.Count - 1
        '    If dsUpdate.Tables("ARTICLE_MODIFICATION_UPDATE").Rows(I).Item("Valider") = True Or dsUpdate.Tables("ARTICLE_MODIFICATION_UPDATE").Rows(I).Item("Ignorer") = True Then
        '        ExisteMiseAjour = True
        '        Exit For
        '    End If
        'Next

        For I = 0 To gArticles.RowCount - 1
            If gArticles(I, "valider") = True Or gArticles(I, "Ignorer") = True Then
                ExisteMiseAjour = True
                Exit For
            End If
        Next

        If ExisteMiseAjour = False Then
            MsgBox("vous n'avez séléctionné aucune modification a éffectuer !" + Chr(13), MsgBoxStyle.Information, "Message")
            Exit Sub
        End If

        For I = 0 To gArticles.RowCount - 1
            If gArticles(I, "valider").ToString <> "" Then
                If gArticles(I, "valider") = True Then

                    CodePCT = gArticles(I, "CodePCT")
                    Designation = gArticles(I, "Designation")
                    MessageChangement = gArticles(I, "Changement")
                    NumeroCirculaire = gArticles(I, "NumeroCirculaire")
                    DateCirculaire = gArticles(I, "DateCirculaire")

                    PrixAchat = gArticles(I, "PrixAchat")
                    PrixPublic = gArticles(I, "PrixHT")
                    TarifeReference = gArticles(I, "TarifDeReference")
                    CategorieCNAM = gArticles(I, "CategorieCNAM")
                    AccordPrealable = gArticles(I, "Accordprealable")

                    MiseAJours(CodePCT, Designation, NumeroCirculaire, DateCirculaire, MessageChangement, PrixAchat, PrixPublic, TarifeReference, CategorieCNAM, AccordPrealable)

                    'dsUpdate.Tables("ARTICLE_MODIFICATION_UPDATE").Rows(I).Delete()

                ElseIf gArticles(I, "Ignorer") = True Then

                    ' Insertion dans la table des articles ignorées 

                    StrSQL = "INSERT INTO ARTICLE_IGNORER  (""CodePCT""" + _
                                                            ",""Designation"") " + _
                             " VALUES ('" + gArticles(I, "CodePCT") + _
                                      "','" + gArticles(I, "Designation") + _
                                      "') "

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                    ' ajout dans l 'historique des updates

                    ''''''''''''''''''''''
                    CodePCT = gArticles(I, "CodePCT")
                    Designation = gArticles(I, "Designation")
                    MessageChangement = gArticles(I, "Changement")
                    NumeroCirculaire = gArticles(I, "NumeroCirculaire")
                    DateCirculaire = gArticles(I, "DateCirculaire")
                    ''''''''''''''''''''''

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "INSERT INTO HISTORIQUE_UPDATE (""Date""" + _
                                                         ",""Action""" + _
                                                         ",""CodePCT""" + _
                                                         ",""Designation""" + _
                                                         ",""Changement""" + _
                                                         ",""NumeroCirculaire""" + _
                                                         ",""DateCirculaire"") " + _
                                      "VALUES ('" + System.DateTime.Now + _
                                              "','Article ignoré" + _
                                              "','" + CodePCT + _
                                              "','" + Designation + _
                                              "','" + MessageChangement + _
                                              "','" + NumeroCirculaire + _
                                              "','" + DateCirculaire.ToString + "') "

                    Try
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                    'dsUpdate.Tables("ARTICLE_MODIFICATION_UPDATE").Rows(I).Delete()

                End If
            End If
            
        Next
        MsgBox("Modification terminée avec succès !" + Chr(13), MsgBoxStyle.Information, "Message")

        I = 0
        While I <= gArticles.RowCount - 1
            If gArticles(I, "valider") = True Or gArticles(I, "Ignorer") = True Then
                'dsUpdate.Tables("ARTICLE_MODIFICATION_UPDATE").Rows(I).Delete()
                'gArticles.Rows(I).Delete()
                'I = 0
                StrSQL = "DELETE FROM  ARTICLE_MODIFICATION  WHERE CodePCT='" + gArticles(I, "CodePCT") + "'"
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
            End If
            I += 1
        End While
        AfficherLesDifferences()
    End Sub

    Private Sub MiseAJours(ByVal CodePCT As String, ByVal Designation As String, ByVal NumeroCirculaire As String, ByVal DateCirculaire As DateTime, ByVal MessageChangement As String, ByVal PrixAchat As Double, ByVal PrixPublic As Double, ByVal TarifeReference As Double, ByVal CategorieCNAM As String, ByVal AccordPrealable As Boolean)

        Dim ExonoreTVA As Boolean = False
        Dim TVA As String = ""
        Dim HR As String = ""
        Dim CodeCategorieCNAM As Integer = 0
        Dim PriseEnCharge As Boolean = False
        Dim NouveauCodeArticle As Integer = 0
        Dim PrixVenteTTC As String = ""
        Dim PrixAchatTTC As String = ""
        Dim PrixVenteHT As String = ""
        Dim PrixAchatHT As String = ""

        'changement des prix avec ou sans MAJ CNAM
        If InStr(MessageChangement, "Prix") > 0 Then

            'voir si exonore ou non 
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "SELECT Exonorertva FROM ARTICLE WHERE CodePCT=" + Quote(CodePCT)
            Try
                ExonoreTVA = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            cmd.CommandText = "SELECT HR FROM ARTICLE WHERE CodePCT=" + Quote(CodePCT)
            Try
                HR = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            cmd.CommandText = "SELECT TVA FROM ARTICLE WHERE CodePCT=" + Quote(CodePCT)
            Try
                TVA = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If ExonoreTVA Then
                PrixVenteTTC = PrixPublic '+ HR
                PrixAchatTTC = PrixAchat
                PrixVenteHT = PrixPublic - HR
                PrixAchatHT = PrixAchat
            Else
                PrixVenteTTC = PrixPublic '(PrixPublic * (1 + (TVA / 100))) '+ HR
                PrixAchatTTC = PrixAchat  '(PrixAchat * (1 + (TVA / 100)))
                PrixVenteHT = ((PrixPublic - HR) * (1 - (TVA / 100))) '+ HR
                PrixAchatHT = (PrixAchat * (1 - (TVA / 100)))
            End If

            Try
                CodeCategorieCNAM = RecupererValeurExecuteScalaire("CodeCategorie", "CATEGORIE_CNAM", "LibelleCategorie", CategorieCNAM)
            Catch ex As Exception
                CodeCategorieCNAM = 0
            End Try

            If Trim(CategorieCNAM.ToUpper) = "INTERMEDIARE" Or Trim(CategorieCNAM.ToUpper) = "VITAL" Or Trim(CategorieCNAM.ToUpper) = "ESSENTIEL" Then
                PriseEnCharge = True
            Else
                PriseEnCharge = False
            End If

            If AccordPrealable = True Then
                PriseEnCharge = True
            End If

            StrSQL = "Update ARTICLE SET PrixAchatHT= " + _
                             PrixAchatHT.ToString + _
                             " ,PrixVenteHT =" + _
                             PrixVenteHT.ToString + _
                             " ,Marge=" + IIf(PrixAchat.ToString = "0", "0", (((PrixPublic.ToString - PrixAchat.ToString) * 100) / PrixAchat.ToString).ToString) + _
                             " ,PrixAchatTTC =" + _
                             PrixAchatTTC.ToString + _
                             " ,PrixVenteTTC =" + _
                             PrixVenteTTC.ToString + _
                             " ,TarifDeReference =" + _
                             TarifeReference.ToString + _
                             " ,CodeCategorieCNAM =" + _
                             IIf(CodeCategorieCNAM <> 0, CodeCategorieCNAM.ToString, "NULL") + _
                             " ,AccordPrealable ='" + _
                             AccordPrealable.ToString + _
                             "' ,PriseEnCharge ='" + _
                             PriseEnCharge.ToString + _
                             "' WHERE CodePCT = '" + CodePCT + "'"

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                cmd.ExecuteNonQuery()

            Catch ex As Exception
                Console.WriteLine(ex.Message)
                Exit Sub
            End Try

            ' ajout dans l 'historique des updates
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "INSERT INTO HISTORIQUE_UPDATE (""Date""" + _
                                                 ",""Action""" + _
                                                 ",""CodePCT""" + _
                                                 ",""Designation""" + _
                                                 ",""Changement""" + _
                                                 ",""NumeroCirculaire""" + _
                                                 ",""DateCirculaire"") " + _
                              "VALUES ('" + System.DateTime.Now + _
                                      "','Accéptation de mettre a jour un article" + _
                                      "','" + CodePCT + _
                                      "','" + Designation + _
                                      "','" + MessageChangement + _
                                      "','" + NumeroCirculaire + _
                                      "','" + DateCirculaire.ToString + "') "

            Try
                cmd.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
                Exit Sub
            End Try

            ' Nouvel Article ou Article Abscent
        ElseIf InStr(MessageChangement.ToUpper, "ARTICLE") > 0 Then

            Try
                CodeCategorieCNAM = RecupererValeurExecuteScalaire("CodeCategorie", "CATEGORIE_CNAM", "LibelleCategorie", Trim(CategorieCNAM.ToUpper))
            Catch ex As Exception
                CodeCategorieCNAM = Nothing
            End Try

            If CategorieCNAM = "INTERMEDIARE" Or CategorieCNAM = "VITAL" Or CategorieCNAM = "ESSENTIEL" Then
                PriseEnCharge = True
            Else
                PriseEnCharge = False
            End If

            If AccordPrealable = True Then
                PriseEnCharge = True
            End If

            'Récuperation du dernier code article 
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "SELECT MAX(CAST(CodeArticle as integer)) FROM ARTICLE "

            Try
                NouveauCodeArticle = cmd.ExecuteScalar() + 1
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            PrixVenteTTC = PrixPublic


            StrSQL = "INSERT INTO ARTICLE  (""CodeArticle""" + _
                                            ",""CodeABarre""" + _
                                            ",""Designation""" + _
                                            ",""CodePCT""" + _
                                            ",""PrixAchatHT""" + _
                                            ",""Marge""" + _
                                            ",""PrixVenteHT""" + _
                                            ",""PrixAchatTTC""" + _
                                            ",""PrixVenteTTC""" + _
                                            ",""TarifDeReference""" + _
                                            ",""CodeCategorieCNAM""" + _
                                            ",""AccordPrealable""" + _
                                            ",""PriseEnCharge"") " + _
                     " VALUES ('" + NouveauCodeArticle.ToString + _
                                      "','" + CodePCT + _
                                      "','" + Designation + _
                                      "','" + CodePCT + _
                                      "','" + PrixAchat.ToString + _
                                      "','" + IIf(PrixAchat.ToString = "0", "0", (((PrixPublic.ToString - PrixAchat.ToString) * 100) / PrixAchat.ToString).ToString) + _
                                      "','" + PrixPublic.ToString + _
                                      "','" + PrixAchat.ToString + _
                                      "','" + PrixVenteTTC.ToString + _
                                      "','" + TarifeReference.ToString + _
                                      "'," + IIf(CodeCategorieCNAM <> 0, Quote(CodeCategorieCNAM.ToString), "NULL") + _
                                      ",'" + AccordPrealable.ToString + _
                                      "','" + PriseEnCharge.ToString + "') "

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                cmd.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
                Exit Sub
            End Try

            'affichage de la liste 
            Dim MyFicheArticle As New fFicheArticle
            MyFicheArticle.CodeArticle = NouveauCodeArticle
            MyFicheArticle.StockArticle = 0
            MyFicheArticle.DesignationArticle = Designation
            MyFicheArticle.ajoutmodif = "M"
            MyFicheArticle.Init()
            MyFicheArticle.tQuantiteUnitaire.Value = 1
            MyFicheArticle.ShowDialog()

            MyFicheArticle.Close()
            MyFicheArticle.Dispose()

            ' ajout dans l 'historique des updates
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "INSERT INTO HISTORIQUE_UPDATE (""Date""" + _
                                                 ",""Action""" + _
                                                 ",""CodePCT""" + _
                                                 ",""Designation""" + _
                                                 ",""Changement""" + _
                                                 ",""NumeroCirculaire""" + _
                                                 ",""DateCirculaire"") " + _
                              "VALUES ('" + System.DateTime.Now + _
                                      "','Ajout d''un article inéxistant" + _
                                      "'," + Quote(CodePCT) + _
                                      "," + Quote(Designation) + _
                                      "," + Quote(MessageChangement) + _
                                      "," + Quote(NumeroCirculaire) + _
                                      ",'" + DateCirculaire.ToString + "') "

            Try
                cmd.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
                Exit Sub
            End Try

            'changement CNAM seulement
        Else
            Try
                CodeCategorieCNAM = RecupererValeurExecuteScalaire("CodeCategorie", "CATEGORIE_CNAM", "LibelleCategorie", CategorieCNAM)
            Catch ex As Exception
                CodeCategorieCNAM = 0
            End Try

            If Trim(CategorieCNAM.ToUpper) = "INTERMEDIARE" Or Trim(CategorieCNAM.ToUpper) = "VITAL" Or Trim(CategorieCNAM.ToUpper) = "ESSENTIEL" Then
                PriseEnCharge = True
            Else
                PriseEnCharge = False
            End If

            If AccordPrealable = True Then
                PriseEnCharge = True
            End If

            StrSQL = "Update ARTICLE SET TarifDeReference =" + _
                             TarifeReference.ToString + _
                             " ,CodeCategorieCNAM =" + _
                             IIf(CodeCategorieCNAM <> 0, CodeCategorieCNAM.ToString, "NULL") + _
                             " ,AccordPrealable ='" + _
                             AccordPrealable.ToString + _
                             "' ,PriseEnCharge ='" + _
                             PriseEnCharge.ToString + _
                             "' WHERE CodePCT = '" + CodePCT + "'"

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                cmd.ExecuteNonQuery()

            Catch ex As Exception
                Console.WriteLine(ex.Message)
                Exit Sub
            End Try

            ' ajout dans l 'historique des updates
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "INSERT INTO HISTORIQUE_UPDATE (""Date""" + _
                                                 ",""Action""" + _
                                                 ",""CodePCT""" + _
                                                 ",""Designation""" + _
                                                 ",""Changement""" + _
                                                 ",""NumeroCirculaire""" + _
                                                 ",""DateCirculaire"") " + _
                              "VALUES ('" + System.DateTime.Now + _
                                      "','Accéptation de mettre a jour un article" + _
                                      "','" + CodePCT + _
                                      "','" + Designation + _
                                      "','" + MessageChangement + _
                                      "','" + NumeroCirculaire + _
                                      "','" + DateCirculaire.ToString + "') "

            Try
                cmd.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
                Exit Sub
            End Try
        End If
    End Sub

    Private Sub bCocherTous_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCocherTous.Click
        Dim I As Integer = 0
        For I = 0 To dsUpdate.Tables("ARTICLE_MODIFICATION_UPDATE").Rows.Count - 1
            dsUpdate.Tables("ARTICLE_MODIFICATION_UPDATE").Rows(I).Item("Valider") = True
        Next
    End Sub

    Private Sub bDecocherTous_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bDecocherTous.Click
        Dim I As Integer = 0
        For I = 0 To dsUpdate.Tables("ARTICLE_MODIFICATION_UPDATE").Rows.Count - 1
            dsUpdate.Tables("ARTICLE_MODIFICATION_UPDATE").Rows(I).Item("Valider") = False
        Next
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim CondCrystal As String = ""
        CondCrystal = "1=1 "

        If rdbNouveauxArticles.Checked = True Then
            CondCrystal = CondCrystal + " AND InStr ({ARTICLE_MODIFICATION.Changement},'Nouvel article')>0"
        End If

        If rdbChangementsPrix.Checked = True Then
            CondCrystal = CondCrystal + " AND (InStr ({ARTICLE_MODIFICATION.Changement},'Prix Pharmacien')>0 OR InStr ({ARTICLE_MODIFICATION.Changement},'Prix Public')>0)"
        End If

        If rdbChangementsCNAM.Checked = True Then
            CondCrystal = CondCrystal + " AND (InStr ({ARTICLE_MODIFICATION.Changement},'Accord préalable')>0 OR InStr ({ARTICLE_MODIFICATION.Changement},'Tarif de reférence')>0)"
        End If

        If rdbChangemenstPrixCNAM.Checked = True Then
            CondCrystal = CondCrystal + " AND (InStr ({ARTICLE_MODIFICATION.Changement},'Prix Pharmacien')>0 OR InStr ({ARTICLE_MODIFICATION.Changement},'Prix Public')>0 OR InStr ({ARTICLE_MODIFICATION.Changement},'Accord préalable')>0 OR InStr ({ARTICLE_MODIFICATION.Changement},'Tarif de reférence')>0)"
        End If

        If rdbArticlesAbsents.Checked = True Then
            CondCrystal = CondCrystal + " AND InStr ({ARTICLE_MODIFICATION.Changement},'Article absent')>0"
        End If

   

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression des articles PHARMA2000 Premium Update" Then
                num = I
            End If
        Next

        CR.FileName = Application.StartupPath + "\EtatPharma2000PremiumUpdate.rpt"

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent
        CR.RecordSelectionFormula = CondCrystal
        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression des articles PHARMA2000 Premium Update"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    '    Public Sub Downloding(ByVal Nom_Fichier_A_Telecharger)

    '        Dim request As FtpWebRequest
    '        Dim response As FtpWebResponse
    '        Dim responseStream As Stream
    '        Dim reader As StreamReader

    '        Dim URI As String = ""

    '        'Dim ftp As System.Net.FtpWebRequest = CType(FtpWebRequest.Create("ftp://************/2012-01-27/99199-2012-01-27.af"), FtpWebRequest)

    '        'ftp.Credentials = New System.Net.NetworkCredential("PHARMA2000", "Next;3U7+s4")

    '        ''3. Settings and action
    '        'ftp.KeepAlive = False
    '        ''we want a binary transfer, not textual data'
    '        'ftp.UseBinary = True
    '        ''Define the action required (in this case, download a file)
    '        'ftp.Method = System.Net.WebRequestMethods.Ftp.DownloadFile
    '        ''''''''''''''''''''''''''''
    '        'Dim myFtp As New FtpClient(hostname, username, password)
    '        'myFtp.Download("/pub/myfile.bin", "C:\myfile.bin")

    '        request = WebRequest.Create("ftp://************/2012-01-27/10835-2012-01-27.af") '(Nom_Fichier_A_Telecharger)

    '        '' This example assumes the FTP site uses anonymous logon.
    '        request.Credentials = New NetworkCredential("PHARMA2000", "Next;3U7+s4")

    '        request.Method = WebRequestMethods.Ftp.DownloadFile

    '        response = request.GetResponse()

    '        responseStream = response.GetResponseStream()
    '        reader = New StreamReader(responseStream)
    '        Console.WriteLine(reader.ReadToEnd())

    '        MsgBox("Download Complete, status {0}", MsgBoxStyle.Information)

    '        reader.Close()
    '        response.Close()

    '    End Sub
    'End Class

    'Public Class ftpCreate
    '    Private ftpTcpClient As TcpClient
    '    Public ResponseStream As NetworkStream
    '    Public ReturnNameMessage As String
    '    Public ReturnPwdMessage As String

    '    Public Sub ftpLogin(ByVal strName As String, ByVal strPWD As String, ByVal strftpLogin As String)
    '        Try

    '            Dim strCommand As String
    '            Dim strReturnMessage As String
    '            Dim bteSendBytes() As Byte
    '            Dim bteRetruenBytes() As Byte
    '            Dim intReturnByteLength As Integer
    '            Dim ftpTcpClient As TcpClient = New TcpClient(strftpLogin, 21)

    '            ResponseStream = ftpTcpClient.GetStream
    '            strCommand = "USER " + strName + vbCrLf
    '            bteSendBytes = Encoding.ASCII.GetBytes(strCommand)
    '            ResponseStream.Write(bteSendBytes, 0, bteSendBytes.Length)

    '            intReturnByteLength = ftpTcpClient.ReceiveBufferSize
    '            ReDim bteRetruenBytes(intReturnByteLength)
    '            ResponseStream.Read(bteRetruenBytes, 0, intReturnByteLength)
    '            strReturnMessage = Encoding.ASCII.GetString(bteRetruenBytes) + "/ "
    '            ReturnNameMessage = strCommand + strReturnMessage
    '            strCommand = "PASS " + strPWD + vbCrLf
    '            Array.Clear(bteSendBytes, 0, bteSendBytes.Length)
    '            bteSendBytes = Encoding.ASCII.GetBytes(strCommand)
    '            ResponseStream.Write(bteSendBytes, 0, bteSendBytes.Length)
    '            intReturnByteLength = ftpTcpClient.ReceiveBufferSize
    '            ReDim bteRetruenBytes(intReturnByteLength)
    '            ResponseStream.Read(bteRetruenBytes, 0, intReturnByteLength)
    '            strReturnMessage = Encoding.ASCII.GetString(bteRetruenBytes) + "/ "

    '            ReturnPwdMessage = strCommand + strReturnMessage + vbCrLf

    '        Catch ex As SocketException
    '            ReturnPwdMessage = ex.Message
    '        End Try
    '    End Sub

    'End Class

    'Public Class ftpClient

    '    Dim ReturnNameMessage
    '    Dim ReturnPwdMessage
    '    Dim myftpCreate As ftpCreate

    '    Public Sub LogInFTP(ByVal strName As String, ByVal strPWD As String, ByVal strftpLogin As String)
    '        myftpCreate = New ftpCreate()
    '        myftpCreate.ftpLogin(strName, strPWD, strftpLogin)
    '        ReturnNameMessage = myftpCreate.ReturnNameMessage
    '        ReturnPwdMessage = myftpCreate.ReturnPwdMessage
    '    End Sub

    '    Public ReadOnly Property GetReturnNameMessage() As String
    '        Get
    '            Return ReturnNameMessage
    '        End Get
    '    End Property

    '    Public ReadOnly Property GetReturnPwdMessage() As String
    '        Get
    '            Return ReturnPwdMessage
    '        End Get
    '    End Property

    '    Public Sub FTPUpLoad(ByVal strFilePath As String, ByVal strFtpPath As String, ByRef pstrReturnMessage As String)

    '        Dim UPFile As New FileStream(strFilePath, FileMode.Open)
    '        Dim bytUPFile() As Byte
    '        Dim lngFileLength As Long
    '        Dim ftpStream As NetworkStream = myftpCreate.ResponseStream
    '        Dim returnMessage As String
    '        Dim UpLoadStream As NetworkStream
    '        Try
    '            lngFileLength = UPFile.Length
    '            ReDim bytUPFile(lngFileLength)
    '            UPFile.Read(bytUPFile, 0, lngFileLength)
    '            FTPCommands(ftpStream, "PASV", returnMessage)
    '            UpLoadStream = GetConnectTcpClient(returnMessage)
    '            FTPCommands(ftpStream, "TYPE I", returnMessage)
    '            FTPCommands(ftpStream, "STOR " + strFtpPath, returnMessage)
    '            pstrReturnMessage += returnMessage.TrimEnd
    '            UpLoadStream.Write(bytUPFile, 0, lngFileLength)
    '            UpLoadStream.Close()
    '            UPFile.Close()
    '        Catch ex As Exception
    '            pstrReturnMessage = ex.Message
    '        End Try
    '    End Sub

    '    Public Sub FTPDownLoad(ByVal strFilePath As String, ByVal strFtpPath As String, ByRef pstrReturnMessage As String)

    '        Dim UPFile As New FileStream(strFtpPath, FileMode.Create)
    '        Dim bytUPFile() As Byte
    '        Dim lngFileLength As Long
    '        Dim ftpStream As NetworkStream = myftpCreate.ResponseStream
    '        Dim returnMessage As String = ""

    '        FTPCommands(ftpStream, "PASV", returnMessage)
    '        Dim DownloadStream As NetworkStream
    '        DownloadStream = GetConnectTcpClient(returnMessage)
    '        FTPCommands(ftpStream, "TYPE I", returnMessage)
    '        FTPCommands(ftpStream, "RETR " + strFilePath, returnMessage)
    '        pstrReturnMessage += returnMessage

    '        ReDim bytUPFile(1024)
    '        Do
    '            lngFileLength = DownloadStream.Read(bytUPFile, 0, 1024)
    '            UPFile.Write(bytUPFile, 0, lngFileLength)
    '        Loop While lngFileLength > 0

    '        UPFile.Close()
    '        DownloadStream.Close()
    '    End Sub

    '    Private Function FTPCommands(ByVal ftpStream As NetworkStream, ByVal strCommand As String, ByRef strMessage As String) As Integer
    '        Dim bteCommand() As Byte
    '        bteCommand = Encoding.ASCII.GetBytes(strCommand + vbCrLf)
    '        ftpStream.Write(bteCommand, 0, bteCommand.Length)
    '        Dim b(360000) As Byte
    '        ftpStream.Read(b, 0, 360000)
    '        strMessage = Encoding.ASCII.GetString(b)
    '    End Function
    '    Private Function GetConnectTcpClient(ByVal respMessage As String) As NetworkStream

    '        Dim i As Integer
    '        Dim strIP As String
    '        Dim strIPs() As String
    '        Dim strAddress As String
    '        Dim intPort As Integer
    '        Try
    '            strIP = respMessage.Substring(respMessage.IndexOf("(")).Replace("(", "")
    '            strIPs = strIP.Split(",")
    '            strIP = ""
    '            For i = 0 To 3
    '                strIP += strIPs(i) + "."
    '            Next
    '            strAddress = strIP.Substring(0, strIP.Length - 1)
    '            intPort = Integer.Parse(strIPs(4)) * 256 + _
    '                      Integer.Parse(strIPs(5).Substring(0, strIPs(5).IndexOf(")")))
    '            Dim dataClient As New TcpClient()
    '            Dim cIPEP As New IPEndPoint(IPAddress.Parse(strAddress), intPort)
    '            dataClient.Connect(cIPEP)
    '            Return dataClient.GetStream
    '        Catch ex As Exception
    '            MessageBox.Show(ex.Message)
    '        End Try
    '    End Function

    Private Sub rdbCode_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbCode.CheckedChanged
        AfficherLesDifferences()
    End Sub

    Private Sub rdbDesignation_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbDesignation.CheckedChanged
        AfficherLesDifferences()
    End Sub

    Private Sub gArticles_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gArticles.FetchRowStyle


        If gArticles.Columns("Changement").CellText(e.Row).ToString.Contains("Nouvel article") Then
            e.CellStyle.BackColor = System.Drawing.Color.Cyan
        ElseIf gArticles.Columns("Changement").CellText(e.Row).ToString.Contains("Prix Pharmacien") Or gArticles.Columns("Changement").CellText(e.Row).ToString.Contains("Prix Public") Then
            e.CellStyle.BackColor = Color.FromArgb(255, 255, 192, 192) 'System.Drawing.Color.Red
        Else
            e.CellStyle.BackColor = Color.FromArgb(255, 255, 255, 128) ' System.Drawing.Color.Yellow  '
        End If
    End Sub

    Private Sub rdbDateCirculaire_CheckedChanged(sender As System.Object, e As System.EventArgs) Handles rdbDateCirculaire.CheckedChanged
        AfficherLesDifferences()
    End Sub

    Private Sub gArticles_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyDown
        If e.KeyCode = Keys.F1 Then
            Dim StrSql As String
            Dim Stock As Integer = 0
            Dim cmd As New SqlCommand
            Dim CodeArticle As String = ""

            Try
                CodeArticle = RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "CodePCT", gArticles(gArticles.Row, "CodePCT"))
                If CodeArticle.ToString = "" Then
                    Exit Sub
                End If
            Catch
                Exit Sub
            End Try

            StrSql = "SELECT SUM(QteLotArticle) AS Stock FROM dbo.LOT_ARTICLE WHERE CodeArticle='" + _
                     CodeArticle + "'"

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSql
            Try
                Stock = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            AfficherFicheArticle(CodeArticle, Stock)
            'AfficherFicheArticle(gListe(gListe.Row, "Codearticle"), gListe(gListe.Row, "QuantitePerime"))
        End If
    End Sub

    Private Sub AfficherFicheArticle(ByVal CodeArticle, ByVal StockArticle)
        Dim MyFicheArticle As New fFicheArticle
        MyFicheArticle.CodeArticle = CodeArticle
        MyFicheArticle.StockArticle = StockArticle
        MyFicheArticle.DesignationArticle = gArticles(gArticles.Row, "Designation")
        MyFicheArticle.ajoutmodif = "M"
        MyFicheArticle.Init()
        MyFicheArticle.ShowDialog()
        MyFicheArticle.Close()
        MyFicheArticle.Dispose()
    End Sub

    Private Sub rbChangPCT_CheckedChanged(sender As Object, e As EventArgs) Handles rbChangPCT.CheckedChanged
        AfficherLesDifferences()
        bCocherTous.Enabled = True
    End Sub
End Class
