﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Linq.Dynamic
Imports Microsoft.Reporting.WinForms

Public Class fEtatOrdonnancier
    Dim _SalesReportService As New Bll.Reporting.SalesReport
    Dim _VOrderBy As String = "DateVente"
    Dim _VAscDesc As String = "Asc"

    Dim cmdOrdonnancier As New SqlCommand
    Dim daOrdonnancier As New SqlDataAdapter
    Dim dsOrdonnancier As New DataSet

    Dim MontantTotal As Double = 0
    Dim MontantRegle As Double = 0
    Dim Reste As Double = 0
    Dim CondCrystalReport As String = ""

    Dim Initialisation As Boolean = False

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub Init()
        dtpDebut.Text = Today
        dtpFin.Text = Today
        dtpDebut.Focus()

        Initialisation = True
        AfficherOrdonnancier()
    End Sub
    Public Sub AfficherOrdonnancier()
        Dim I As Integer
        Dim Cond As String = "1=1"

        Dim NombreVente As Integer = 0
        Dim List

        If dtpDebut.Text = "" Then
            dtpDebut.Value = Date.Now
        End If

        If dtpFin.Text = "" Then
            dtpFin.Value = Date.Now
        End If

        'Dim P_Report_EtatOrdonnancier_Results = _SalesReportService.GetEtatOrdonnancier(
        '                                            dtpDebut.Value, _
        '                                            dtpFin.Value)

        Dim P_Report_EtatOrdonnancier_Results = _SalesReportService.GetEtatOrdonnancier(
                                                    dtpDebut.Text, _
                                                    dtpFin.Text)

        List = New Library.SortableBindingList(Of Data.Reporting.P_Report_EtatOrdonnancier_Result)(P_Report_EtatOrdonnancier_Results)

        With gOrd
            .Columns.Clear()
            .DataSource = List
            .Rebind(False)
            .Columns("DateVente").Caption = "Date"
            .Columns("NomMedecin").Caption = "Médecin"
            .Columns("Numero").Caption = "Numéro"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Designation").Caption = "Désignation"
            .Columns("Quantite").Caption = "Qté"
            .Columns("Malade").Caption = "Malade"
            .Columns("CIN").Caption = "CIN"
            .Columns("Adresse").Caption = "Adresse"
            .Columns("DateVente").NumberFormat = "dd/MM/yyyy"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Numero").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("DateVente").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("DateVente").Width = 140
            .Splits(0).DisplayColumns("NomMedecin").Width = 150
            .Splits(0).DisplayColumns("Numero").Width = 80
            .Splits(0).DisplayColumns("LibelleForme").Width = 100
            If Screen.PrimaryScreen.Bounds.Width < 1350 Then
                .Splits(0).DisplayColumns("Designation").Width = 340 '400
            Else
                .Splits(0).DisplayColumns("Designation").Width = Screen.PrimaryScreen.Bounds.Width - 920
            End If
            .Splits(0).DisplayColumns("Quantite").Width = 60
            .Splits(0).DisplayColumns("CIN").Width = 80
            .Splits(0).DisplayColumns("Adresse").Width = 80
            .Splits(0).DisplayColumns("NumeroVente").Visible = False
            .Splits(0).DisplayColumns("DateOrdonnancier").Visible = False
            .Splits(0).DisplayColumns("NumeroOrdonnacier").Visible = False
            .Splits(0).DisplayColumns("Row").Visible = False
            .Splits(0).DisplayColumns("TypeOrdonnance").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True

            'Style du Caractere et du grid
            ParametreGrid(gOrd)

            '.Splits(0).ColumnCaptionHeight = 40
            '.Splits(0).RecordSelectors = False
            '.ExtendRightColumn = True
            '.EmptyRows = True
            '.FetchRowStyles = True
            ''Style du Caractere et du grid
            'ParametreGrid(gOrd)
        End With

        lNombreDesLignes.Text = P_Report_EtatOrdonnancier_Results.Count
    End Sub

    Private Sub gOrd_AfterSort(sender As Object, e As FilterEventArgs) Handles gOrd.AfterSort
        Try
            _VOrderBy = e.Condition.Substring(0, e.Condition.IndexOf(" "))
            _VAscDesc = e.Condition.Substring(e.Condition.IndexOf(" ") + 1, e.Condition.Length - e.Condition.IndexOf(" ") - 1)
        Catch ex As Exception
            _VOrderBy = e.Condition
            _VAscDesc = "Asc"
        End Try
    End Sub

    Private Sub gOrd_DoubleClick(sender As Object, e As System.EventArgs) Handles gOrd.DoubleClick
        Dim MyVenteAffiche As New fVenteJusteAffichage
        MyVenteAffiche.NumeroVente = gOrd.Columns("NumeroVente").Value
        MyVenteAffiche.ShowDialog()
        MyVenteAffiche.Close()
        MyVenteAffiche.Dispose()
    End Sub

    Private Sub gOrd_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs)
        e.CellStyle.Font = New System.Drawing.Font("Calibri", 9, FontStyle.Regular)
        If dsOrdonnancier.Tables("ORDONNANCIER").Rows(e.Row).Item("Supprimer") = True Then
            e.CellStyle.ForeColor = Color.Red
        End If
    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherOrdonnancier()
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDebut.TextChanged

    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherOrdonnancier()
            gOrd.Focus()
        End If
    End Sub

    Private Sub dtpFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpFin.TextChanged

    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim _Parameters As New List(Of ReportParameter)()

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Reporting Ventes" Then
                num = I
            End If
        Next
        Dim dt

        Dim MyViewer As New fImpressionReportingVente

        Dim _DateDebut As New ReportParameter()
        _DateDebut.Name = "DateDebut"
        _DateDebut.Values.Add(dtpDebut.Value)
        _Parameters.Add(_DateDebut)

        Dim _DateFin As New ReportParameter()
        _DateFin.Name = "DateFin"
        _DateFin.Values.Add(dtpFin.Value)
        _Parameters.Add(_DateFin)


        dt = _SalesReportService.GetEtatOrdonnancier(
                                                    dtpDebut.Value, _
                                                    dtpFin.Value).OrderBy(_VOrderBy + " " + _VAscDesc)



        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_EtatOrdonnancier", dt))
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Pharmacie", _SalesReportService.GetPharmacie()))
        MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatOrdonnancier.rdl"



        MyViewer.ReportViewer1.LocalReport.SetParameters(_Parameters)

        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel1)

        MyViewer.ReportViewer1.RefreshReport()

        fMain.Tab.SelectedTab.Text = "Impression Reporting Ventes"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub fEtatOrdonnancier_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

    End Sub
End Class