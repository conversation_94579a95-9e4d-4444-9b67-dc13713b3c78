﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fSituationArticle
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fSituationArticle))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bSupprimerSituationArticle = New C1.Win.C1Input.C1Button()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.tLibelleSituationArticle = New C1.Win.C1Input.C1TextBox()
        Me.bAjouterSituationArticle = New C1.Win.C1Input.C1Button()
        Me.tCodeSituationArticle = New C1.Win.C1Input.C1TextBox()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.gSituationArticle = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Panel.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        CType(Me.tLibelleSituationArticle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodeSituationArticle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gSituationArticle, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Panel.Controls.Add(Me.bSupprimerSituationArticle)
        Me.Panel.Controls.Add(Me.GroupBox3)
        Me.Panel.Controls.Add(Me.Label10)
        Me.Panel.Controls.Add(Me.gSituationArticle)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1028, 566)
        Me.Panel.TabIndex = 0
        '
        'bSupprimerSituationArticle
        '
        Me.bSupprimerSituationArticle.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.bSupprimerSituationArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimerSituationArticle.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler21
        Me.bSupprimerSituationArticle.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSupprimerSituationArticle.Location = New System.Drawing.Point(916, 499)
        Me.bSupprimerSituationArticle.Name = "bSupprimerSituationArticle"
        Me.bSupprimerSituationArticle.Size = New System.Drawing.Size(100, 45)
        Me.bSupprimerSituationArticle.TabIndex = 2
        Me.bSupprimerSituationArticle.Text = "Supprimer"
        Me.bSupprimerSituationArticle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSupprimerSituationArticle.UseVisualStyleBackColor = True
        Me.bSupprimerSituationArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox3
        '
        Me.GroupBox3.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.GroupBox3.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox3.Controls.Add(Me.Label1)
        Me.GroupBox3.Controls.Add(Me.tLibelleSituationArticle)
        Me.GroupBox3.Controls.Add(Me.bAjouterSituationArticle)
        Me.GroupBox3.Controls.Add(Me.tCodeSituationArticle)
        Me.GroupBox3.Controls.Add(Me.Label12)
        Me.GroupBox3.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox3.Location = New System.Drawing.Point(12, 486)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(898, 68)
        Me.GroupBox3.TabIndex = 1
        Me.GroupBox3.TabStop = False
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.Location = New System.Drawing.Point(62, 39)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(107, 13)
        Me.Label1.TabIndex = 12
        Me.Label1.Text = "Libellé de la Situation"
        '
        'tLibelleSituationArticle
        '
        Me.tLibelleSituationArticle.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tLibelleSituationArticle.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tLibelleSituationArticle.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tLibelleSituationArticle.Location = New System.Drawing.Point(175, 37)
        Me.tLibelleSituationArticle.Name = "tLibelleSituationArticle"
        Me.tLibelleSituationArticle.Size = New System.Drawing.Size(165, 18)
        Me.tLibelleSituationArticle.TabIndex = 1
        Me.tLibelleSituationArticle.Tag = Nothing
        Me.tLibelleSituationArticle.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tLibelleSituationArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouterSituationArticle
        '
        Me.bAjouterSituationArticle.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouterSituationArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouterSituationArticle.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bAjouterSituationArticle.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouterSituationArticle.Location = New System.Drawing.Point(789, 13)
        Me.bAjouterSituationArticle.Name = "bAjouterSituationArticle"
        Me.bAjouterSituationArticle.Size = New System.Drawing.Size(100, 45)
        Me.bAjouterSituationArticle.TabIndex = 2
        Me.bAjouterSituationArticle.Text = "Ajouter"
        Me.bAjouterSituationArticle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouterSituationArticle.UseVisualStyleBackColor = True
        Me.bAjouterSituationArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tCodeSituationArticle
        '
        Me.tCodeSituationArticle.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodeSituationArticle.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodeSituationArticle.Location = New System.Drawing.Point(175, 13)
        Me.tCodeSituationArticle.Name = "tCodeSituationArticle"
        Me.tCodeSituationArticle.Size = New System.Drawing.Size(165, 18)
        Me.tCodeSituationArticle.TabIndex = 0
        Me.tCodeSituationArticle.Tag = Nothing
        Me.tCodeSituationArticle.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodeSituationArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label12
        '
        Me.Label12.AutoSize = True
        Me.Label12.BackColor = System.Drawing.Color.Transparent
        Me.Label12.Location = New System.Drawing.Point(67, 15)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(102, 13)
        Me.Label12.TabIndex = 8
        Me.Label12.Text = "Code de la Situation"
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.BackColor = System.Drawing.Color.Transparent
        Me.Label10.Location = New System.Drawing.Point(12, 19)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(152, 13)
        Me.Label10.TabIndex = 87
        Me.Label10.Text = "Liste des situations des articles"
        '
        'gSituationArticle
        '
        Me.gSituationArticle.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gSituationArticle.GroupByCaption = "Drag a column header here to group by that column"
        Me.gSituationArticle.Images.Add(CType(resources.GetObject("gSituationArticle.Images"), System.Drawing.Image))
        Me.gSituationArticle.Location = New System.Drawing.Point(12, 36)
        Me.gSituationArticle.Name = "gSituationArticle"
        Me.gSituationArticle.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gSituationArticle.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gSituationArticle.PreviewInfo.ZoomFactor = 75.0R
        Me.gSituationArticle.PrintInfo.PageSettings = CType(resources.GetObject("gSituationArticle.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gSituationArticle.Size = New System.Drawing.Size(1004, 444)
        Me.gSituationArticle.TabIndex = 0
        Me.gSituationArticle.Text = "C1TrueDBGrid4"
        Me.gSituationArticle.PropBag = resources.GetString("gSituationArticle.PropBag")
        '
        'fSituationArticle
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1028, 566)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fSituationArticle"
        Me.Text = "fSituationArticle"
        Me.Panel.ResumeLayout(False)
        Me.Panel.PerformLayout()
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        CType(Me.tLibelleSituationArticle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodeSituationArticle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gSituationArticle, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents bSupprimerSituationArticle As C1.Win.C1Input.C1Button
    Friend WithEvents tLibelleSituationArticle As C1.Win.C1Input.C1TextBox
    Friend WithEvents bAjouterSituationArticle As C1.Win.C1Input.C1Button
    Friend WithEvents tCodeSituationArticle As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents gSituationArticle As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Label1 As System.Windows.Forms.Label
End Class
