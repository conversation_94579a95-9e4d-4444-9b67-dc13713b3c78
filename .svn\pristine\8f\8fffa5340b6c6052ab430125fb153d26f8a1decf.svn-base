﻿SQL Installer.NET Database Project
----------------------------------
A project for developing relational databases and integrating with Visual Studio 
and/or Team Foundation Server builds. Requires SQL Installer.NET to be installed 
(http://sqlinstaller.codeplex.com). You can also find more detailed documentation
regarding the layout and configuration options of this project through that link.

Usage
-----
This template project calls the SQL Installer.NET command line utility through
the Post-build event (Project->Properties->Build Events). By building this 
project, you will in effect build out the database using the scripts contained
within.

The _first_ thing you will want to do is review/edit the SQLInstaller.xml file
which contains configuration options for the SQL Installer.NET utility. It is a 
good possiblity you will need to modify the provider (if not using SQL Server)
and/or the database connection string. 

Output
------
The base for this project template is the C# Class Library Project. You can opt 
to have this project only contain DDL scripts and simply ignore/disregard the 
assembly output file. As an alternative, you may decide to add database logic 
(e.g. repository access classes) to this project. In this case, the project would 
serve a dual-purpose: developing/building the database as well as containing data
access logic.

Developing
----------
Add/modify/delete files from the scripts folder structure as necessary. 

The *Install* folder contains all the latest DDL scripts broken down by type. 
You can modify and add additional subfolders - in any hierarchy you want. 
The important thing to remember is to use the naming convention established by 
SQL Installer.NET (*.Table.sql,*.StoredProcedure.sql, etc.). This will also
determine the order in which scripts are executed. Note that you can establish
your own naming convention by adding a FileTypes node to the SQLInstaller.xml
file (see documentation on CodePlex site).

The *Upgrade* folder will contain any migration scripts (for migrating
a database from release-to-release). By convention, the name of the subfolder
underneath the *Upgrade* folder will correspond to the release name/number.

For team development (i.e. with TFS), you can integrate others changes with your 
local database instance by simply doing a'get latest' then build. You may also 
include this project to your CI (continuous integration) builds. Just make sure
and install the SQL Installer.NET utility onto the build server.

For more detailed documentation please visit the CodePlex site at:
http://sqlinstaller.codeplex.com.
