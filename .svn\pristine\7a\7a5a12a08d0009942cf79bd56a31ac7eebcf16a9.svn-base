﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fCategorie

    Dim cmdCategorie As New SqlCommand
    Dim daCategorie As New SqlDataAdapter
    Dim cbCategorie As New SqlCommandBuilder
    Dim dsCategorie As New DataSet
    Dim xCategorie As Integer
    Dim ModeCategorie As String
    Dim CodeCategorie As String
    Dim dsRecupereNum As New DataSet
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNumt As New SqlDataAdapter
    Dim CodeExiste As Boolean = False

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub afficherCategorie()
        Dim I As Integer
        Dim Cond As String = "1=1"
        dsCategorie.Clear()

        If tRecherche.Text <> "" Then
            Cond += " and LibelleCategorie like " + Quote("%" + tRecherche.Text + "%")
        End If

        'cmdCategorie.CommandText = " SELECT " + _
        '                            " CodeCategorie, " + _
        '                            " LibelleCategorie " + _
        '                            " FROM CATEGORIE WHERE SupprimeCategorie =0 AND " + Cond + _
        '                            " ORDER BY CodeCategorie"

        cmdCategorie.CommandText = " SELECT * " + _
                                   " FROM CATEGORIE WHERE SupprimeCategorie =0 AND " + Cond + _
                                   " ORDER BY CodeCategorie"

        cmdCategorie.Connection = ConnectionServeur
        daCategorie = New SqlDataAdapter(cmdCategorie)
        daCategorie.Fill(dsCategorie, "CATEGORIE")

        With gCategorie
            .Columns.Clear()
            .DataSource = dsCategorie
            .DataMember = "CATEGORIE"
            .Rebind(False)
            .Columns("CodeCategorie").Caption = "Code de la Categorie"
            .Columns("LibelleCategorie").Caption = "Libelle de la Categorie"
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("CodeCategorie").Width = 120
            .Splits(0).DisplayColumns("CodeCategorie").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("LibelleCategorie").Width = 80
            .Splits(0).DisplayColumns("LibelleCategorie").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(0).DisplayColumns("CodeCategorie").Locked = True
            .Splits(0).DisplayColumns("LibelleCategorie").Locked = True
            .Splits(0).DisplayColumns("SupprimeCategorie").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gCategorie)
        End With
        gCategorie.MoveRelative(xCategorie)
        cbCategorie = New SqlCommandBuilder(daCategorie)
    End Sub

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click

        ModeCategorie = "Ajout"

        tCodeCategorie.Value = ""
        tLibelleCategorie.Value = ""
        tCodeCategorie.Enabled = True
        tLibelleCategorie.Enabled = True

        'Dim dr As DataRow
        'Dim StrSQL As String = ""

        'If tCodeCategorie.Text = "" Then
        '    MsgBox("Veuillez saisir le code de la Catégorie Client !", MsgBoxStyle.Critical, "Erreur")
        '    tCodeCategorie.Focus()
        '    Exit Sub
        'End If
        'If tLibelleCategorie.Text = "" Then
        '    MsgBox("Veuillez saisir le libelle de la Catégorie Client !", MsgBoxStyle.Critical, "Erreur")
        '    tLibelleCategorie.Focus()
        '    Exit Sub
        'End If

        'If CodeExiste = True Then
        '    MsgBox("Code catégorie existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
        '    tCodeCategorie.Focus()
        '    Exit Sub
        'End If

        'With dsCategorie
        '    dr = .Tables("CATEGORIE").NewRow
        '    dr.Item("LibelleCategorie") = tLibelleCategorie.Text
        '    dr.Item("CodeCategorie") = tCodeCategorie.Text
        '    .Tables("CATEGORIE").Rows.Add(dr)
        'End With

        'Try
        '    daCategorie.Update(dsCategorie, "CATEGORIE")
        '    afficherCategorie()
        'Catch ex As Exception
        '    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
        '    dsCategorie.Reset()
        'End Try

        'tCodeCategorie.Text = ""
        'tLibelleCategorie.Text = ""
    End Sub

    Private Sub bSupprimerCategorie_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerCategorie.Click
        Dim cmd As New SqlCommand
        Dim StrSQL As String = ""
        Dim NbrDesArticles As Integer = 0

        If gCategorie.RowCount > 0 Then
            If MsgBox("Voulez vous vraiment supprimer cette Catégorie " + Quote(gCategorie(gCategorie.Row, "LibelleCategorie")) + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then

                ''---------------------- vérification si ce FOURNISSEUR a des reglements 
                'StrSQL = "SELECT COUNT(CodeArticle) FROM ARTICLE " + _
                '         "WHERE CodeCategorie ='" + gCategorie(gCategorie.Row, "CodeCategorie").ToString + "'"
                'cmd.Connection = ConnectionServeur
                'cmd.CommandText = StrSQL
                'Try
                '    NbrDesArticles = cmd.ExecuteScalar()
                'Catch ex As Exception
                '    Console.WriteLine(ex.Message)
                'End Try

                'If NbrDesArticles <> 0 Then
                '    MsgBox("Cette Catégorie est affécté a des artrilces, Suppression réfusé !", MsgBoxStyle.Critical, "Erreur")
                '    Exit Sub
                'End If

                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "UPDATE CATEGORIE Set SupprimeCategorie = 1 WHERE CodeCategorie =" + Quote(gCategorie(gCategorie.Row, "CodeCategorie"))
                    cmd.ExecuteNonQuery()
                    afficherCategorie()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub

    Private Sub gCategorie_AfterColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gCategorie.AfterColEdit
        Dim dr As DataRow
        Dim StrSQL As String = ""

        With dsCategorie.Tables("CATEGORIE_MAJ")
            dr = .Rows(0)
            dr.Item("LibelleCategorie") = gCategorie(gCategorie.Row, "LibelleCategorie")

        End With
        Try
            daCategorie.Update(dsCategorie, "CATEGORIE_MAJ")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            afficherCategorie()
        End Try
    End Sub

    Private Sub gCategorie_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gCategorie.Change
    End Sub

    Private Sub gCategorie_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles gCategorie.Click
        Dim StrSQL As String = ""
        CodeCategorie = Quote(gCategorie(gCategorie.Row, "CodeCategorie"))
        If CodeCategorie = "" Then
            MsgBox("Veuillez sélectionner le libelle de la Catégorie !", MsgBoxStyle.Critical, "Erreur")
            gCategorie.Focus()
            Exit Sub
        End If
        If (dsCategorie.Tables.IndexOf("CATEGORIE_MAJ") > -1) Then
            dsCategorie.Tables("CATEGORIE_MAJ").Clear()
        End If

        StrSQL = " SELECT * FROM CATEGORIE WHERE CodeCategorie = " + CodeCategorie
        cmdCategorie.Connection = ConnectionServeur
        cmdCategorie.CommandText = StrSQL
        daCategorie = New SqlDataAdapter(cmdCategorie)
        daCategorie.Fill(dsCategorie, "CATEGORIE_MAJ")
        cbCategorie = New SqlCommandBuilder(daCategorie)
    End Sub

    Private Sub tCodeCategorie_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeCategorie.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLibelleCategorie.Focus()
        End If
    End Sub

    Private Sub tCodeCategorie_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeCategorie.LostFocus
        lTest.Visible = False
    End Sub

    Private Sub tCodeCategorie_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeCategorie.TextChanged

        If tCodeCategorie.Text <> "" Then
            If IsNumeric(tCodeCategorie.Text.Substring(Len(tCodeCategorie.Text) - 1, 1)) = False Then
                MsgBox("Veuillez introduire des chiffres !", MsgBoxStyle.Critical, "Erreur")
                tCodeCategorie.Text = tCodeCategorie.Text.Substring(0, Len(tCodeCategorie.Text) - 1)
                tCodeCategorie.Select(Len(tCodeCategorie.Text), 0)
            End If
        End If

        Dim StrSQLtest As String = ""
        If (dsRecupereNum.Tables.IndexOf("CATEGORIE_TEST") > -1) Then
            dsRecupereNum.Tables("CATEGORIE_TEST").Clear()
        End If

        StrSQLtest = " SELECT * FROM CATEGORIE as CATEGORIE_TEST WHERE CodeCategorie=" + Quote(tCodeCategorie.Text)
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQLtest
        daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
        daRecupereNumt.Fill(dsRecupereNum, "CATEGORIE_TEST")

        If dsRecupereNum.Tables("CATEGORIE_TEST").Rows.Count <> 0 Then
            lTest.Text = "Code non valide déja existe"
            lTest.ForeColor = Color.OrangeRed
            lTest.Visible = True
            CodeExiste = True
        Else
            lTest.Text = "Code valide"
            lTest.ForeColor = Color.LawnGreen
            lTest.Visible = True
            CodeExiste = False
        End If
        If tCodeCategorie.Text = "" Then
            lTest.Visible = False
        End If
    End Sub

    Public Sub Init()
        afficherCategorie()
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub bModifierForme_Click(sender As Object, e As EventArgs) Handles bModifierForme.Click
        ModeCategorie = "Modif"

        tLibelleCategorie.Value = gCategorie(gCategorie.Row, "libelleCategorie")
        tCodeCategorie.Value = gCategorie(gCategorie.Row, "CodeCategorie")

        tLibelleCategorie.Enabled = True
        tCodeCategorie.Enabled = False

        lTest.Text = ""
    End Sub

    Private Sub tRecherche_TextChanged(sender As Object, e As EventArgs) Handles tRecherche.TextChanged
        afficherCategorie()
    End Sub

    Private Sub bConfirmerMedecin_Click(sender As Object, e As EventArgs) Handles bConfirmerMedecin.Click
        Dim dr As DataRow
        Dim StrSQL As String = ""

        If ModeCategorie = "Ajout" Then

            If tCodeCategorie.Text = "" Then
                MsgBox("Veuillez saisir le code du Categorie !", MsgBoxStyle.Critical, "Erreur")
                tCodeCategorie.Focus()
                Exit Sub
            End If

            If tLibelleCategorie.Text = "" Then
                MsgBox("Veuillez saisir le libelle du Categorie !", MsgBoxStyle.Critical, "Erreur")
                tLibelleCategorie.Focus()
                Exit Sub
            End If

            If CodeExiste = True Then
                MsgBox("Code Categorie existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
                tCodeCategorie.Focus()
                Exit Sub
            End If

            With dsCategorie
                dr = .Tables("CATEGORIE").NewRow
                dr.Item("LibelleCategorie") = tLibelleCategorie.Text
                dr.Item("CodeCategorie") = tCodeCategorie.Text
                dr.Item("SupprimeCategorie") = False
                .Tables("CATEGORIE").Rows.Add(dr)
            End With

            Try
                daCategorie.Update(dsCategorie, "CATEGORIE")
                afficherCategorie()
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                dsCategorie.Reset()
            End Try

            tCodeCategorie.Text = ""
            tLibelleCategorie.Text = ""

        ElseIf ModeCategorie = "Modif" Then
            Dim cmd As New SqlCommand
            If gCategorie.RowCount = 0 Then
                MsgBox("Liste Vide", MsgBoxStyle.OkOnly)
            Else
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "UPDATE CATEGORIE SET LibelleCategorie = " + Quote(tLibelleCategorie.Text) + " WHERE CodeCategorie = " + Quote(tCodeCategorie.Value)
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                    Exit Sub
                End Try
                afficherCategorie()
                tLibelleCategorie.Value = ""
                tCodeCategorie.Value = ""
            End If
        End If
    End Sub
End Class