﻿'Test Modif TFS
Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared

Public Class fAchat

    'Dim cmdChargement As New SqlCommand
    'Dim cbChargement As New SqlCommandBuilder
    'Dim dsChargement As New DataSet
    'Dim daChargement As New SqlDataAdapter

    Dim StrSQL As String = ""

    Dim cmdAchat As New SqlCommand
    Dim dsAchat As New DataSet
    Dim daAchat As New SqlDataAdapter
    Dim cbAchat As New SqlCommandBuilder

    Dim cmdAchatEntete As New SqlCommand
    Dim daAchatEntete As New SqlDataAdapter
    Dim cbAchatEntete As New SqlCommandBuilder
    Dim cmdAchatDetail As New SqlCommand
    Dim daAchatDetails As New SqlDataAdapter
    Dim cbAchatDetails As New SqlCommandBuilder
    Dim Mode As String = ""

    Public NumeroAchat As String = ""
    Public NumeroligneAchat As Integer = 0

    Dim cmdCodeABarre As New SqlCommand
    Dim daCodeABarre As New SqlDataAdapter
    Dim dsCodeABarre As New DataSet
    Dim cbCodeABarre As New SqlCommandBuilder

    Public TotalTTCAchat As Double = 0.0
    Public TotalHTNETAchat As Double = 0.0
    Public TotalRemiseAchat As Double = 0.0
    Public TotalTVAAchat As Double = 0.0
    Public TotalVenteTTCAchat As Double = 0.0
    Public TotalQte As Integer = 0

    Public TVA As Double = 0.0
    Public Timbre As Double = 0.0 '0.3

    '------------------------------------------------- les informations de paiement (retour de fenetre paiement)
    Public PaiementOkNo As String = ""
    Public Operateur As Integer = 0

    Public NouvelleAchat As DataRow = Nothing 'datarow pour charger l'entête dans la datatable ACHAT
    Public NouvelArticle As DataRow = Nothing 'datarow pour charger les détails dans la datatable ACHAT_DETAIL
    Public dr As DataRow = Nothing 'datarow pour charger dans la datatable ARTICLE qui contient les articles recherchés pendant la saisie de la designation

    '---------------------------------------- variables pour confirmer la mise en instance d'une Commande
    Dim ComfirmerMettreEnINstance As Boolean = False
    Dim ConfirmerInstance As Boolean = False
    Dim NomInstance As String = ""
    Dim CodeOperateurInstance As String = ""
    '---------------------------------------- Tableau pour enregistrer les comandes convertis en achat
    Dim TableauCommande(10) As String
    '---------------------------------------- variable pour recuperer le code article à partir de la fenêtre recherchre multicritere
    Dim CodeArticleRechercheMC As String = ""
    '---------------------------------------- variable pour savoir si l achat est depuis commande ou nn
    Dim DepuisCommande As Boolean = False

    Dim NombreAchatInstance As Integer = 0

    Public TransactionAchat As SqlTransaction

    Dim Confirmation As Boolean = False ' variable pour que la deuxieme interpretation de la touche OK lors d'une inf manquante apres confirmation
    Dim Modification As Boolean = False

    Dim retourDatePerm As Boolean = False

    Dim ArtcleConfirmer As Boolean = False


    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        Try

            If argument = "113" And bFournisseur.Enabled = True Then
                bFournisseur_Click(sender, e)
            End If
            If argument = "114" And bConfirmer.Enabled = True Then
                bConfirmer_Click(sender, e)
            End If
            If argument = "115" And bRecherche.Enabled = True Then
                bRecherche_Click(sender, e)
            End If
            If argument = "116" And bAjouter.Enabled = True Then
                bAjouter_Click(sender, e)
            End If
            If argument = "117" And bInstance.Enabled = True Then
                bInstance_Click(sender, e)
            End If
            If argument = "118" And bSupprimer.Enabled = True Then
                bSupprimer_Click(sender, e)
            End If
            If argument = "119" And Mode = "Ajout" Then
                bRemise_Click(sender, e)
            End If
            'If argument = "119" And Mode = "Modif" Then
            If argument = "119" And Mode = "Consultation" Then
                bModifier_Click(sender, e)
            End If
            If argument = "120" And bCommande.Enabled = True Then
                bCommande_Click(sender, e)
            End If
            If argument = "121" And bAnnuler.Enabled = True Then
                bAnnuler_Click(sender, e)
            End If
            If argument = "122" And bImprimer.Enabled = True Then
                bImprimer_Click(sender, e)
            End If
            '--------------------- boutton close 
            If argument = "123" And bQuitter.Enabled = True Then
                bQuitter_Click(sender, e)
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "fonctionsF", ex.Message, "0000620", "Erreur lors de cliquer sur une touche", True, True, True)
            Return

        End Try

    End Sub

    Public Sub Init()

        Try

            'Initialiser les controles
            initLoadControl()

            'Charger Fournisseur
            initFournisseur()

            'Appel Pour selectionner le dernier ligne 
            NumeroligneAchat = selectionDernierLigneAchat()

            'Initialiser la DS ACHAT
            initAchat()

            'Initialiser la DS ACHAT_DETAILS
            initAchatDetails()

            'Appel pour charger les information de l'Achat en question
            ChargerAchat(NumeroligneAchat)

            'Mise en forme de la Grid
            initgArticles()

            'Charger liste Article
            initArticle()

            'Initialiser le nombre d'Achat en instance
            initNombreAchatInstance()


        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "Init", ex.Message, "0000618", "Erreur lors de l'initialisation de la forme", True, True, True)
            Return

        End Try

    End Sub

    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)

        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        CmdCalcul.Transaction = TransactionAchat

        Try
            Quote(ValeurCle)

        Catch ex As Exception

            Return Nothing
            Exit Function

        End Try


        Try
            StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurCle)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL

            Valeur = CmdCalcul.ExecuteScalar().ToString

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "RecupererValeurExecuteScalaire", ex.Message, "0000019", "Erreur lors d'executer la requette", True, True, True)
            Return Valeur

        End Try

        Return (Valeur)

    End Function

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click

        Try
            bTerminal.Enabled = True
            'Suivi du scénario 
            fMessageException.Show("Achat", "fAchat", "bAjouter_Click", "NoException", "NoError", "Clic sur le bouton Ajouter", False, True, False)

            'le parametre False 
            'Indique qu'on va initialiser les controls de l'Achat


            fAjout(False)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "bAjouter_Click", ex.Message, "0000618", "Erreur Clic sur le bouton Ajouter", True, True, True)
            Return

        End Try

    End Sub

    Private Sub fAjout(ByVal initShow As Boolean)

        Try

            'Appel ChargerAchat: Pour Récuperer la 
            'structure des DS ACHAT et ACHAT_DEAILS
            'La valeur 0 est inexistant
            If (initShow = False) Then

                'Changer le MODE en Ajout
                Mode = "Ajout"

                ChargerAchat("0")

                'Initialiser les Controls utilisés lors de l'opération de l'Achat
                initControlAjout()

            End If

            'Ajout d'un nouvel enregistrement vide dans la table ACHAT_DETAIL
            NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
            NouvelArticle("Designation") = ""
            NouvelArticle("CodeArticle") = ""
            NouvelArticle("CodeABarre") = ""
            dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "fAjout", ex.Message, "0000621", "Erreur d'excution de fAjout", True, True, True)
            Return

        End Try

    End Sub

    Private Sub gArticles_AfterColEdit(sender As Object, e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gArticles.AfterColEdit
        '---------------------------------- Controler la qte
        If gArticles.Col = 5 Then
            If Not IsDBNull(gArticles.Columns("QuantiteUnitaire").Value) And Not IsDBNull(gArticles.Columns("Stock").Value) Then
                If (gArticles.Columns("QuantiteUnitaire").Value < 0) And (Math.Abs(gArticles.Columns("QuantiteUnitaire").Value) > gArticles.Columns("Stock").Value) Then
                    MsgBox("Veuillez vérifier la quantité !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Focus()
                    gArticles.Col = 5
                    gArticles.EditActive = True
                    Exit Sub
                End If
            End If
        End If
    End Sub

    'Private Sub gArticles_BeforeColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColEditEventArgs) Handles gArticles.BeforeColEdit
    '    'If e.Column.Name = "Date de péremption" Then
    '    '    Dim det As New C1.Win.C1Input.C1DateEdit
    '    '    det.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
    '    '    gArticles.Columns("DatePeremption").Editor = det
    '    'End If
    'End Sub

    Private Sub gArticles_BeforeColUpdate(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColUpdateEventArgs) Handles gArticles.BeforeColUpdate

        If e.ColIndex = gArticles.Columns.IndexOf(gArticles.Columns("Qte")) Then
            If gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999 Then

                MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                'gArticles.Columns("Qte").Value = "1"
                gArticles.Columns("Qte").Value = e.OldValue
            End If
        End If

    End Sub

    Private Sub gArticles_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gArticles.Change

        Try


            If (gArticles.Columns(gArticles.Col).DataField() = "Designation" And gArticles.Columns("Designation").Value <> "") Or gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" Then
                Dim StrSQL1 As String = ""
                Dim I As Integer
                Dim NbLigne As Integer
                NbLigne = gArticles.RowCount

                'With gListeRecherche
                '    .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
                '    '.Top = Me.gArticles.Top + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight

                '    ' .Top = Me.gArticles.Row '- 50 '- Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight

                'End With

                If gArticles.Row <= 8 Then
                    With gListeRecherche

                        .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
                        .Top = Me.gArticles.Top + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight + 10
                    End With
                Else

                    gListeRecherche.Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
                    gListeRecherche.Top = Me.gArticles.Top '' + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight + 10

                End If



                ''If gArticles.Row <= 8 Then
                ''    '' ''gListeRecherche.Location = New Point(109, ((gArticles.Row) * 17) + 161)
                ''    '' ''gListeRecherche.Visible = True
                ''    gListeRecherche.Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
                ''    gListeRecherche.Top = Me.gArticles.Top + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight + 10

                ''Else
                ''    'gListeRecherche.Location = New Point(109, 101)
                ''    ''gListeRecherche.Location = New Point(109, 120)
                ''    ''gListeRecherche.Visible = True

                ''    gListeRecherche.Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
                ''    gListeRecherche.Top = Me.gArticles.Top '' + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight + 10

                ''End If


                Try
                    dsAchat.Tables("ARTICLE").Clear()
                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "gArticles_Change", ex.Message, "0000020", "Erreur lors de vider la DS ARTICLE", True, True, True)

                End Try

                'If gArticles.Row = dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 And gArticles.Columns(gArticles.Col).DataField() = "Designation" Then

                If gArticles.Row = gArticles.RowCount - 1 And gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                    If Mode <> "Consultation" Then
                        gListeRecherche.Visible = True
                    End If

                Else
                    gListeRecherche.Visible = False
                End If

                'chargement des articles qui sont mis en jeu
                If gArticles.Columns(gArticles.Col).DataField() = "Designation" Then

                    If gArticles.Columns("Designation").Value.ToString.Length > 1 Then

                        If gArticles.Columns("Designation").Value.ToString.IndexOf(".") > 0 And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1)) Then   'gArticles.Columns("Designation").Value.ToString.Substring(gArticles.Columns("Designation").Value.ToString.Length - 1, 1) Like "." And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1))

                            StrSQL1 = "SELECT CodeArticle," + _
                                      "Designation," + _
                                      "LibelleForme," + _
                                      "PrixAchatHT, " + _
                                      "QuantiteUnitaire" + _
                                      " FROM ARTICLE " + _
                                      "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                      "WHERE " + _
                                      "ltrim(str(PrixAchatHT,10,3)) LIKE '" + _
                                      gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + "%' AND Supprime=0 AND CodeCategorie <> '9' ORDER BY PrixVenteTTC"
                        Else

                            StrSQL1 = "SELECT CodeArticle," + _
                                      "Designation," + _
                                      "LibelleForme," + _
                                      "PrixAchatHT, " + _
                                      "QuantiteUnitaire" + _
                                      " FROM ARTICLE " + _
                                      "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                      "WHERE " + _
                                      " Designation LIKE " + Quote(gArticles.Columns("Designation").Value.ToString + "%") + _
                                      " AND Supprime=0 AND CodeCategorie <> '9' ORDER BY Designation"
                        End If

                    Else

                        StrSQL1 = "SELECT CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixAchatHT, " + _
                                  "QuantiteUnitaire" + _
                                  " FROM ARTICLE " + _
                                  "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  "WHERE " + _
                                  " Designation LIKE " + Quote(gArticles.Columns("Designation").Value.ToString + "%") + _
                                  " AND Supprime=0 AND CodeCategorie <> '9' ORDER BY Designation"
                    End If

                ElseIf gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" Then

                    StrSQL1 = "SELECT CodeArticle," + _
                              "Designation," + _
                              "LibelleForme," + _
                              "PrixAchatHT, " + _
                              "QuantiteUnitaire" + _
                              " FROM ARTICLE " + _
                              "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                              "WHERE " + _
                              "CodeABarre = " + Quote(gArticles.Columns("CodeABarre").Value) + _
                              " AND Supprime=0 AND CodeCategorie <> '9' ORDER BY Designation"

                End If

                Try

                    cmdAchat.Connection = ConnectionServeur
                    cmdAchat.CommandText = StrSQL1
                    daAchat = New SqlDataAdapter(cmdAchat)
                    daAchat.Fill(dsAchat, "ARTICLE")

                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "gArticles_Change", ex.Message, "0000021", "Erreur lors de remplire la DS ARTICLE", True, True, True)

                End Try

                If dsAchat.Tables("ARTICLE").Rows.Count > 0 Then
                    dr = dsAchat.Tables("ARTICLE").Rows(0)
                End If

                With gListeRecherche

                    .Columns.Clear()
                    .DataSource = dsAchat
                    .DataMember = "ARTICLE"
                    .Rebind(False)
                    .Columns("CodeArticle").Caption = "Code Article"
                    .Columns("Designation").Caption = "Designation"
                    .Columns("LibelleForme").Caption = "Forme"
                    .Columns("PrixAchatHT").Caption = "Prix d'achat"
                    .Columns("QuantiteUnitaire").Caption = "Qte Unit"

                    ' Centrer tous les entêtes
                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    Next
                    ' CentreR tous les valeurs
                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    Next

                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).Visible = False
                    Next

                    .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                    .Splits(0).DisplayColumns("CodeArticle").Visible = True
                    .Splits(0).DisplayColumns("Designation").Visible = True
                    .Splits(0).DisplayColumns("LibelleForme").Visible = True
                    .Splits(0).DisplayColumns("PrixAchatHT").Visible = True
                    .Splits(0).DisplayColumns("QuantiteUnitaire").Visible = False

                    .Splits(0).DisplayColumns("CodeArticle").Width = 0
                    .Splits(0).DisplayColumns("Designation").Width = 460
                    .Splits(0).DisplayColumns("LibelleForme").Width = 100
                    .Splits(0).DisplayColumns("PrixAchatHT").Width = 120
                    .Splits(0).DisplayColumns("QuantiteUnitaire").Width = 0

                    .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 255, 217)
                    .Splits(0).DisplayColumns("LibelleForme").Style.BackColor = Color.FromArgb(250, 250, 200)
                    .Splits(0).DisplayColumns("PrixAchatHT").Style.BackColor = Color.FromArgb(210, 240, 255)

                    .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                    .Splits(0).ColumnCaptionHeight = 20
                    .Splits(0).RecordSelectors = False
                    .ExtendRightColumn = True
                    .EmptyRows = True
                    .FetchRowStyles = True

                End With

                Dim Col As New C1.Win.C1TrueDBGrid.C1DataColumn
                Dim dc As C1.Win.C1TrueDBGrid.C1DisplayColumn

                With gListeRecherche

                    .Columns.Insert(0, Col)
                    Col.Caption = "Stock"
                    dc = .Splits(0).DisplayColumns.Item("Stock")
                    dc.Width = 40
                    .Splits(0).DisplayColumns("Stock").HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    .Splits(0).DisplayColumns("Stock").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(210, 255, 230)
                    dc.Visible = True
                    .Rebind(True)

                End With
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "gArticles_Change", ex.Message, "0000622", "Erreur d'excution de gArticles_Change", True, True, True)
            Return
        End Try
    End Sub

    Public Function RecupereNumero()

        'Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        Try
            Try

                StrSQL = " SELECT max([NumeroACHAT]) " + _
                         "FROM (SELECT NumeroAchat as NumeroAchat FROM [ACHAT] UNION SELECT NumeroAchat as NumeroAchat FROM ACHAT_SUPPRIME) " + _
                         "as R WHERE SUBSTRING (NumeroACHAT,0,5)=YEAR(getdate())"
                cmdRecupereNum.Connection = ConnectionServeur
                cmdRecupereNum.CommandText = StrSQL

                ValeurActuel = cmdRecupereNum.ExecuteScalar().ToString

            Catch ex As Exception

                'Gérer l'Exception
                fMessageException.Show("Achat", "fAchat", "gArticles_Change", ex.Message, "0000022", "Erreur lors d'executer la requette", True, True, True)
                ValeurActuel = 0

            End Try

            '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
            If ValeurActuel.Length > 4 Then

                If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then

                    Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                    Numero = Numero + 1
                    numeroConvertit = Numero.ToString

                    For i = 0 To 5 - numeroConvertit.Length
                        numeroConvertit = "0" + numeroConvertit
                    Next
                    ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
                Else
                    ValeurRetour = Year(Today).ToString + "/" + "000001"
                End If
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
            Return ValeurRetour
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "RecupereNumero", ex.Message, "0000623", "Erreur d'excution de RecupereNumero", True, True, True)
            Return ValeurRetour
        End Try
    End Function


    Private Sub gArticles_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyUp

        Dim i As Integer = 0
        'Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim reponse As New MsgBoxResult

        'Dim TestNumeroLot As Integer
        Dim QuantiteAAjouter As Integer = 0

        Try

            '---------------------------------- test si on est en mode saisi ou non ---------------------------
            If Mode <> "Ajout" And Mode <> "Modif" Then
                Exit Sub
            End If

            If e.KeyCode = Keys.F2 Then
                cmbFournisseur.Focus()
                Exit Sub
            End If



            If gArticles.Columns("CodeArticle").Value = "" And (e.KeyCode = Keys.Right Or e.KeyCode = Keys.Left) Then
                'gArticles.Col = 2


                '''''''''''
                gArticles.Columns("Designation").Value = ""
                ''''''''''''


                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeArticle"))
                gArticles.EditActive = True
                Exit Sub

            End If

            ''en cas ou un code erroné
            'If gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" And gArticles.Columns("CodeArticle").Value = "" And e.KeyCode = Keys.Enter Then
            '    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
            '    gArticles.EditActive = True
            '    lInfoBulle.Visible = True
            '    Exit Sub
            'Else
            '    lInfoBulle.Visible = False
            'End If

            ''en cas ou un code erroné
            'If gArticles.Columns(gArticles.Col).DataField() = "Designation" And gArticles.Columns("CodeArticle").Value = "" And e.KeyCode = Keys.Enter Then
            '    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
            '    gArticles.EditActive = True
            '    lInfoBulle.Visible = True
            '    Exit Sub
            'Else
            '    lInfoBulle.Visible = False
            'End If

            '--------------------- si entrer dans la colonne designation dans une ligne vide retour au code            
            If e.KeyCode = Keys.Enter And gArticles.Columns("Designation").Value.ToString = "" And gArticles.Columns(gArticles.Col).DataField().ToString <> "CodeABarre" Then
                gArticles.Focus()
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
                gArticles.EditActive = True
                Exit Sub
            End If

            '-------------------- si f1 c'est afficher la fiche article ou la fenêtre de recherche multicritère
            If e.KeyCode = Keys.F1 And gArticles.Columns("CodeArticle").Value.ToString <> "" Then
                AfficherFicheArticle(gArticles.Columns("CodeArticle").Value, gArticles.Columns("Stock").Value, gArticles.Columns("Designation").Value)
                Exit Sub
            End If

            If e.KeyCode = Keys.F1 And Mode = "Ajout" And gArticles.Columns("CodeArticle").Value = "" Then
                Dim RechercheMulticritere As New fRechercheArticleMultiCritere

                RechercheMulticritere.ShowDialog()

                CodeArticleRechercheMC = RechercheMulticritere.CodeArticleRecherche

                gArticles.MoveLast()
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))

                gArticles.Columns("CodeArticle").Value = CodeArticleRechercheMC
                ChargerDetailArticle(CodeArticleRechercheMC)
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))

                RechercheMulticritere.Close()
                RechercheMulticritere.Dispose()
                gArticles.EditActive = True
            End If

            '---------------------------------- suppression du dernier ligne vide si on a deux lignes au mm temps vide
            '---------------------------------- Cas ou on supprime dernier ligne
            If dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 = 1 And gArticles(0, "CodeArticle") = "" And gArticles(1, "CodeArticle") = "" Then
                gArticles.MoveLast()
                gArticles.MovePrevious()
                gArticles.Delete()
            End If

            '---------------------------------- test du type de la valeur d'entrée dans la colonne quantité (numéric) ------------
            '---------------------------------- test du  valeur d'entrée dans la colonne quantité < 0 à éliminer ------------

            If gArticles.Columns(gArticles.Col).DataField() = "Qte" Then
                If gArticles.Columns("Qte").Value.ToString <> "-" Then   ' pour les quantité négatives
                    If IsNumeric(gArticles.Columns("Qte").Value) = False Then
                        gArticles.Columns("Qte").Value = "1"
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                        gArticles.EditActive = False
                        Exit Sub
                    End If
                    If (gArticles.Columns("Qte").Value = 0) And e.KeyCode = Keys.Enter Then
                        gArticles.Columns("Qte").Value = ""
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                        gArticles.EditActive = True
                        Exit Sub
                    End If
                    If (gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999) And e.KeyCode = Keys.Enter Then
                        gArticles.Columns("Qte").Value = "1"
                        MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                        gArticles.EditActive = True
                        Exit Sub
                    End If

                End If
            End If

            '---------------------------------- Controler la qte
            If gArticles.Col = 5 And e.KeyCode = Keys.Enter Then
                If Not IsDBNull(gArticles.Columns("QuantiteUnitaire").Value) And Not IsDBNull(gArticles.Columns("Stock").Value) Then
                    If (gArticles.Columns("QuantiteUnitaire").Value < 0) And (Math.Abs(gArticles.Columns("QuantiteUnitaire").Value) > gArticles.Columns("Stock").Value) Then
                        e.SuppressKeyPress = False
                        gArticles.Focus()
                        gArticles.Col = 5
                        gArticles.EditActive = True
                        Exit Sub
                    End If
                End If
            End If

            '---------------------------------- interdiction de l'insertion de double point dans le prix achat 
            '---------------------------------- et l affichage de 3 chiffres aprés la virgule

            If gArticles.Columns(gArticles.Col).DataField() = "PrixAchatHT" Then
                '*******************************************************************************
                Dim Position As Integer = 0
                If e.KeyCode = Keys.Decimal Or e.KeyCode = Keys.OemPeriod Then
                    Position = InStr(gArticles.Columns("PrixAchatHT").Value.Substring(0, gArticles.Columns("PrixAchatHT").Value.ToString.Length - 1), ".")
                    If Position <> 0 Then
                        gArticles.Columns("PrixAchatHT").Value = ""
                        gArticles.EditActive = True
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("PrixAchatHT"))
                    End If
                    Exit Sub
                End If

                If IsNumeric(gArticles.Columns("PrixAchatHT").Value) = False And gArticles.Columns("PrixAchatHT").Value.ToString <> "" Then
                    MsgBox("Vous devez saisir une valeur numérique", MsgBoxStyle.Information)
                    gArticles.Columns("PrixAchatHT").Value = ""
                    Exit Sub
                End If
            End If


            '/*/*/*/*/*/*/*/*/*/*/*/*/*/**
            'If e.KeyCode = Keys.Enter And gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" And IsDBNull(gArticles.Columns("DatePeremption").Text) = False Then
            '    'If gArticles.Columns("DatePeremption").Value < System.DateTime.Now Then
            '    If gArticles.Columns("DatePeremption").Text <> "" And gArticles.Columns("DatePeremption").Text <> "__/__/____" Then
            '        If gArticles.Columns("DatePeremption").Text.ToString < System.DateTime.Now Then

            '            MsgBox("Date de péremption périmé !", MsgBoxStyle.Critical, "Erreur")
            '            gArticles.EditActive = False
            '            gArticles.Columns("DatePeremption").Text = ""
            '            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '            gArticles.EditActive = False
            '            Exit Sub
            '        End If
            '    End If
            'End If
            '/*/*/*/*/*/*/*/*/*/*/*/*/*/**

            ' Ajouter Dernierement apres modif type datePermp ''''''''''''''''
            '' ''If e.KeyCode = Keys.Enter And gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" And IsDBNull(gArticles.Columns("DatePeremption").Text) = False Then
            '' ''    'If gArticles.Columns("DatePeremption").Value < System.DateTime.Now Then
            '' ''    If gArticles.Columns("DatePeremption").Text <> "" And gArticles.Columns("DatePeremption").Text <> "__/__/____" Then
            '' ''        If convertirADate(gArticles.Columns("DatePeremption").Text) < System.DateTime.Now Then

            '' ''            MsgBox("Date de péremption périmé !", MsgBoxStyle.Critical, "Erreur")
            '' ''            gArticles.EditActive = False
            '' ''            gArticles.Columns("DatePeremption").Text = ""
            '' ''            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '' ''            gArticles.EditActive = False
            '' ''            Exit Sub
            '' ''        End If
            '' ''    End If
            '' ''End If
            ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''



            '/*/*/*/*/*/*/*/*/*/*/*/*/*/**
            ''------- interdire l insertion d un lot avec un numero et sans une date de peremp ------------            
            'If gArticles.Columns(gArticles.Col).DataField() = "PrixAchatHT" And e.KeyCode = Keys.Enter Then

            '    If gArticles.Columns("DatePeremption").Text = "" And gArticles.Columns("NumeroLotArticle").Value.ToString <> "" Then
            '        '''''If gArticles.Columns("DatePeremption").Value.ToString() = "" And gArticles.Columns("NumeroLotArticle").Value.ToString <> "" Then
            '        MsgBox("Date de péremption manquant !", MsgBoxStyle.Critical, "Erreur")
            '        'gArticles.Col = 7
            '        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '        Exit Sub
            '    End If
            'End If
            '/*/*/*/*/*/*/*/*/*/*/*/*/*/**


            '/*/*/*/*/*/*/*/*/*/*/*/*/*/**
            ''-------- afféctation du num de lot pour la date de péremption si elle existe deja DANS LA BASE ------
            'If e.KeyCode = Keys.Enter And gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then

            '    ''''If gArticles.Columns("DatePeremption").Value.ToString <> "" Then
            '    If gArticles.Columns("DatePeremption").Text <> "" And gArticles.Columns("DatePeremption").Text <> "__/__/____" Then


            '        '' ''StrSQL = " SELECT count(NumeroLotArticle) FROM [LOT_ARTICLE] WHERE DatePeremptionArticle='" + _
            '        '' ''gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
            '        '' ''gArticles.Columns("CodeArticle").Value + "'"

            '        StrSQL = " SELECT count(NumeroLotArticle) FROM [LOT_ARTICLE] WHERE DatePeremptionArticle=" + _
            '        Quote(gArticles.Columns("DatePeremption").Text) + " AND CodeArticle=" + _
            '        Quote(gArticles.Columns("CodeArticle").Value)

            '        cmd.Connection = ConnectionServeur
            '        cmd.CommandText = StrSQL

            '        If cmd.ExecuteScalar <> 0 Then

            '            '' ''StrSQL = " SELECT NumeroLotArticle FROM [LOT_ARTICLE] WHERE DatePeremptionArticle='" + _
            '            '' ''         gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
            '            '' ''         gArticles.Columns("CodeArticle").Value + "'"

            '            StrSQL = " SELECT NumeroLotArticle FROM [LOT_ARTICLE] WHERE DatePeremptionArticle=" + _
            '                     Quote(gArticles.Columns("DatePeremption").Text) + " AND CodeArticle=" + _
            '                     Quote(gArticles.Columns("CodeArticle").Value)

            '            cmd.Connection = ConnectionServeur
            '            cmd.CommandText = StrSQL
            '            gArticles.Columns("NumeroLotArticle").Value = cmd.ExecuteScalar
            '        Else
            '            gArticles.Columns("NumeroLotArticle").Value = ""
            '        End If
            '    End If
            'End If
            '/*/*/*/*/*/*/*/*/*/*/*/*/*/**


            '/*/*/*/*/*/*/*/*/*/*/*/*/*/**
            ''-------- afféctation du num de lot pour la date de péremption si elle existe deja DANS LA LISTE -----            
            'If gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            '    Dim CodeNewArticle As String = ""
            '    Dim DateNewArticle As Date

            '    CodeNewArticle = gArticles.Columns("CodeArticle").Value
            '    ''''DateNewArticle = gArticles.Columns("DatePeremption").Value
            '    DateNewArticle = gArticles.Columns("DatePeremption").Text

            '    If gArticles.Columns("NumeroLotArticle").Value = "" Then
            '        i = 0
            '        Do While i < gArticles.RowCount - 1
            '            If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '                If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") = DateNewArticle Then
            '                    gArticles.Columns("NumeroLotArticle").Value = gArticles(i, "NumeroLotArticle")
            '                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '                    gArticles.EditActive = True
            '                End If
            '            End If
            '            i = i + 1
            '        Loop
            '    End If
            'End If
            ''--------------------------- test de l'existance d'un lot avec cette date de péremption -----            
            'If e.KeyCode = Keys.Enter And gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle" Then
            '    If gArticles.Columns("DatePeremption").Text.ToString <> "" And gArticles.Columns("DatePeremption").Text.ToString <> "__/__/____" And gArticles.Columns("NumeroLotArticle").Value.ToString <> "" Then
            '        StrSQL = " SELECT count(NumeroLotArticle) FROM [LOT_ARTICLE]WHERE DatePeremptionArticle='" + _
            '                 gArticles.Columns("DatePeremption").Text.ToString + "' AND CodeArticle='" + _
            '                 gArticles.Columns("CodeArticle").Value + "'"
            '        cmd.Connection = ConnectionServeur
            '        cmd.CommandText = StrSQL

            '        If cmd.ExecuteScalar <> 0 Then
            '            StrSQL = " SELECT NumeroLotArticle FROM [LOT_ARTICLE]WHERE DatePeremptionArticle='" + _
            '                     gArticles.Columns("DatePeremption").Text.ToString + "' AND CodeArticle='" + _
            '                     gArticles.Columns("CodeArticle").Value + "'"
            '            cmd.Connection = ConnectionServeur
            '            cmd.CommandText = StrSQL

            '            If gArticles.Columns("NumeroLotArticle").Value <> cmd.ExecuteScalar Then
            '                MsgBox("Date de péremption existe pour un autre lot !", MsgBoxStyle.Critical, "Erreur")
            '                gArticles.Columns("DatePeremption").Text = ""
            '                gArticles.Columns("NumeroLotArticle").Value = ""
            '                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '                gArticles.EditActive = False
            '                Exit Sub
            '            End If
            '        End If
            '    End If
            'End If
            ''---------------------------------- test de l'existance du numero de lot pour une autre date --------            
            'If (gArticles.Columns(gArticles.Col).DataField() = "QteCommander" Or gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle") And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down) Then
            '    StrSQL = " SELECT COUNT(NumeroLotArticle) FROM [LOT_ARTICLE] WHERE NumeroLotArticle='" + _
            '        gArticles.Columns("NumeroLotArticle").Value.ToString + "' AND CodeArticle='" + _
            '        gArticles.Columns("CodeArticle").Value + "' AND DatePeremptionArticle <> '" + _
            '        gArticles.Columns("DatePeremption").Text + "'"
            '    cmd.Connection = ConnectionServeur
            '    cmd.CommandText = StrSQL

            '    If cmd.ExecuteScalar() <> 0 Then
            '        MsgBox("Numero de lot existe déja !", MsgBoxStyle.Critical, "Erreur")
            '        gArticles.Columns("NumeroLotArticle").Value = ""
            '        gArticles.Columns("DatePeremption").Text = ""
            '        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '        gArticles.EditActive = False
            '        Exit Sub
            '    End If

            'End If

            ''--------------- test si le mm numero du lot existe dans la liste au dessus pour le mm article mais 
            ''--------------- avec une date de péremption differente et vise versa--------            
            'If (gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle" Or gArticles.Columns(gArticles.Col).DataField() = "PrixAchatHT") And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            '    Dim CodeNewArticle As String = ""
            '    Dim DateNewArticle As Date
            '    Dim NumeroLotNewArticle As String = ""

            '    CodeNewArticle = gArticles.Columns("CodeArticle").Value
            '    DateNewArticle = gArticles.Columns("DatePeremption").Text
            '    NumeroLotNewArticle = gArticles.Columns("NumeroLotArticle").Value.ToString

            '    Dim QteNewArticle As Integer = 0

            '    '-------------------------- mm code mm numero de lot rempli mais date different
            '    i = 0
            '    Do While i < gArticles.RowCount - 1
            '        If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '            If gArticles(i, "NumeroLotArticle") <> "" Then
            '                If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") <> DateNewArticle And gArticles(i, "NumeroLotArticle") = NumeroLotNewArticle Then
            '                    MsgBox("Numero de lot existe dans la liste pour le mm article mais avec une autre date de péremption!", MsgBoxStyle.Critical, "Erreur")
            '                    gArticles.Columns("NumeroLotArticle").Value = ""
            '                    'gArticles.Columns("DatePeremption").Value = ""
            '                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '                    gArticles.EditActive = False
            '                    Exit Sub
            '                End If
            '            End If
            '        End If
            '        i = i + 1
            '    Loop

            '    '-------------------------- mm code mm date de lot mais numero different
            '    i = 0
            '    Do While i < gArticles.RowCount - 1
            '        If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '            If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") = DateNewArticle And gArticles(i, "NumeroLotArticle") <> NumeroLotNewArticle Then
            '                MsgBox("Date de péremption existe dans la liste pour le mm article mais avec un autre numéro de lot!", MsgBoxStyle.Critical, "Erreur")
            '                gArticles.Columns("DatePeremption").Text = ""
            '                gArticles.Columns("NumeroLotArticle").Value = ""
            '                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '                gArticles.EditActive = False
            '                Exit Sub
            '            End If
            '        End If
            '        i = i + 1
            '    Loop
            'End If
            '/*/*/*/*/*/*/*/*/*/*/*/*/*/**

            '--------------- test de l'existance du mm article avec la mm date au dessus dans la 
            '--------------- liste (cas ou on a une date non null)

            If (gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle" Or gArticles.Columns(gArticles.Col).DataField() = "PrixAchatHT") And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremptionSaisie").Value) = False Then
                Dim CodeNewArticle As String = ""
                Dim DateNewArticle As String 'Date
                Dim NumeroLotNewArticle As String = ""
                Dim QteNewArticle As Integer = 0

                CodeNewArticle = gArticles.Columns("CodeArticle").Value
                DateNewArticle = gArticles.Columns("DatePeremptionSaisie").Text
                NumeroLotNewArticle = gArticles.Columns("NumeroLotArticle").Value.ToString
                QteNewArticle = gArticles.Columns("Qte").Value

                i = 0
                Do While i < gArticles.RowCount - 1
                    If IsDBNull(gArticles(i, "DatePeremptionSaisie")) = True Then 'Or IsDBNull(gArticles(i, "NumeroLotArticle")) = True Then
                        gArticles(i, "DatePeremptionSaisie") = ""
                        gArticles(i, "NumeroLotArticle") = ""
                    End If
                    If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremptionSaisie") = DateNewArticle And i <> gArticles.Row Then
                        gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QteNewArticle).ToString
                        gArticles.MoveLast()
                        gArticles.Delete()
                        If gArticles(gArticles.RowCount - 1, ("CodeArticle")).ToString <> "" Then
                            NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
                            NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                            NouvelArticle("CodeArticle") = ""
                            NouvelArticle("CodeABarre") = ""
                            dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)
                        End If
                    End If

                    i = i + 1
                Loop
            End If

            '--------------- test de l'existance du mm article sans date aussi au dessus dans la 
            '--------------- liste (cas ou on a une date null)

            If (gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle" Or gArticles.Columns(gArticles.Col).DataField() = "PrixAchatHT") And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremptionSaisie").Text) = True Then
                Dim CodeNewArticle As String = ""
                Dim QteNewArticle As Integer = 0

                CodeNewArticle = gArticles.Columns("CodeArticle").Value
                QteNewArticle = gArticles.Columns("Qte").Value

                i = 0
                Do While i < gArticles.RowCount - 1
                    If IsDBNull(gArticles(i, "DatePeremptionSaisie")) = True And IsDBNull(gArticles(i, "NumeroLotArticle")) = True Then
                        If gArticles(i, "CodeArticle") = CodeNewArticle And i <> gArticles.Row Then
                            gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QteNewArticle).ToString
                            gArticles.MoveLast()
                            gArticles.Delete()
                            If gArticles(gArticles.RowCount - 1, ("CodeArticle")).ToString <> "" Then
                                NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
                                NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                                NouvelArticle("CodeArticle") = ""
                                NouvelArticle("CodeABarre") = ""
                                dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)
                            End If
                        End If
                    End If
                    i = i + 1
                Loop
            End If

            '---------------------------------- test du type de la valeur d'entrée dans la colonne Remise (decimal) ------------            
            If gArticles.Columns(gArticles.Col).DataField() = "PrixAchatHT" Then
                If IsNumeric(gArticles.Columns("Remise").Value) = False And gArticles.Columns("Remise").Value.ToString.Contains(".") = False Then
                    gArticles.Columns("Remise").Value = ""
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("PrixAchatHT"))
                    gArticles.EditActive = True
                    Exit Sub
                End If
            End If


            '' ''If retourDatePerm = True Then
            '' ''    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '' ''End If


            '---------------------------------- verouillage des lignes déja confirmées -------------------------

            If (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) And gArticles.Row <> dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
                gArticles.Splits(0).DisplayColumns("Designation").Locked = True

            ElseIf gArticles.Row = gArticles.RowCount - 1 Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
            End If

            If gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" And gArticles.Columns("Designation").Value.ToString <> "" Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
                gArticles.Splits(0).DisplayColumns("Designation").Locked = True
            End If

            If gArticles.Columns(gArticles.Col).DataField() = "Designation" And gArticles.Columns("CodeArticle").Value.ToString <> "" Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
                gArticles.Splits(0).DisplayColumns("Designation").Locked = True
            End If

            '------------------------------ suppression d'une date de péremption            
            If e.KeyCode = Keys.Delete And gArticles.Row <= dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 And gArticles.Columns(gArticles.Col).DataField() = "DatePeremptionSaisie" Then
                gArticles.EditActive = False
                gArticles.Columns("DatePeremptionSaisie").Text = ""
            End If

            '------------------------------ suppression de numéro de lot            
            If e.KeyCode = Keys.Delete And gArticles.Row <= dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 And gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle" Then
                gArticles.EditActive = False
                gArticles.Columns("NumeroLotArticle").Value = ""
                gArticles.EditActive = True
            End If

            '------------------------------ recherche par code ----------------------------------------------           
            If gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" And e.KeyCode = Keys.Enter And gArticles.Row = dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 Then 'And IsDBNull(gArticles.Columns("Qte").Value) = True Then
                ChargerDetailArticle(gArticles.Columns("CodeABarre").Value.ToString)
                VerifierSiDejaSaisi(gArticles.Columns("CodeArticle").Value.ToString)
                gArticles.EditActive = True
                Exit Sub
            ElseIf gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" And e.KeyCode = Keys.Enter And gArticles.Row < dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 Then 'And IsDBNull(gArticles.Columns("Qte").Value) = True Then
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
                gArticles.EditActive = True
            End If

            '---------------------------------- masquer la liste de recherche si la designation est vide -----------
            If e.KeyCode = Keys.Back And gArticles.Columns("Designation").Value = "" And gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                gListeRecherche.Visible = False
            End If

            '---------------------------- pour passer à la navigation dans la petite liste de recherhce ----------------
            If (e.KeyCode = Keys.Down Or e.KeyCode = Keys.Up) And gListeRecherche.Visible = True Then
                gListeRecherche.Focus()
                gListeRecherche.Col = 1
                gListeRecherche.Row = 1
            End If

            '---------------------------- si l'article n'existe pas dans la liste de recherhce sa quantité doit etre 0  ----
            If dsAchat.Tables("ARTICLE").Rows.Count <= 0 And gListeRecherche.Visible = True Then '
                gArticles.Columns("Qte").Value = 0
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
                gArticles.EditActive = True
            End If

            '---------------------------- calcul des montants --------------------------------------------------------
            If (gArticles.Columns(gArticles.Col).DataField() = "Qte" Or gArticles.Columns(gArticles.Col).DataField() = "Remise" Or gArticles.Columns(gArticles.Col).DataField() = "PrixAchatHT") And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) Then
                CalculerMontants()
            End If

            '--------------------- traitement du clique sur le bouton ENTREE selon la colonne ---------------------------

            If e.KeyCode = Keys.Enter Then 'And (dsAchat.Tables("ARTICLE").Rows.Count > 0 Or gArticles.Row <> dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1) Then


                If gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                    gListeRecherche_KeyUp(sender, e)     ' pour charger les informations de l'article a partir du fiche article
                    VerifierSiDejaSaisi(gArticles.Columns("CodeArticle").Value)

                End If

                If gArticles.Columns(gArticles.Col).DataField() = "Designation" Then

                    If gArticles.Columns("CodeABarre").Value.ToString = "" Then    ' si l'article choisit n'existe pas dans la liste de recherche
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
                        gArticles.Columns("Designation").Value = ""
                        gArticles.EditActive = True
                    Else
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                        gArticles.EditActive = True
                    End If

                ElseIf gArticles.Columns(gArticles.Col).DataField() = "Qte" And e.KeyCode = Keys.Enter Then

                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("QuantiteUnitaire"))

                ElseIf gArticles.Columns(gArticles.Col).DataField() = "QuantiteUnitaire" Then


                    '------------------------------ cas ou on a un changement de Qte Unitaire ---------------------------------------            
                    If e.KeyCode = Keys.Enter Then  'And gArticles.Columns(gArticles.Col).DataField() = "QuantiteUnitaire" Then
                        Dim AncienneQteUnitaire As Double = 0.0
                        Dim ConfirmerChangemant As Boolean = False

                        AncienneQteUnitaire = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", gArticles.Columns("CodeArticle").Value)

                        If AncienneQteUnitaire.ToString <> gArticles.Columns("QuantiteUnitaire").Value.ToString Then  ' And InterventionAvecUnAssistant = True

                            '********************************* Contrôle de l accée *******************************************
                            Dim cmdAssistant As New SqlCommand
                            Dim DemandePotDePasse As Boolean = False

                            cmdAssistant.Connection = ConnectionServeur
                            cmdAssistant.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'INTERVENTION_AVEC_ASSISTANT_DE_CHANGEMANT_DE_PRIX' AND CodeUtilisateur='" + CodeUtilisateur + "'"

                            Try
                                If cmdAssistant.ExecuteScalar() >= 1 Then

                                    Dim InstanceMaquetteChangerPrix As New fMaquetteChangerPrix
                                    fMaquetteChangerPrix.CodeArticle = gArticles.Columns("CodeArticle").Value
                                    fMaquetteChangerPrix.NouveauPrix = gArticles.Columns("PrixAchatHT").Value
                                    fMaquetteChangerPrix.NouvelleQteUnitaire = gArticles.Columns("QuantiteUnitaire").Value

                                    InstanceMaquetteChangerPrix.ShowDialog()

                                    ConfirmerChangemant = InstanceMaquetteChangerPrix.ConfirmerChangemant

                                    InstanceMaquetteChangerPrix.Dispose()
                                    InstanceMaquetteChangerPrix.Close()

                                    gArticles.Columns("PrixVenteTTC").Value = fMaquetteChangerPrix.PrixVenteTTC
                                    gArticles.Columns("TVA").Value = fMaquetteChangerPrix.NouveauTVA

                                    CalculerMontants()

                                    'If ConfirmerChangemant = False Then
                                    '    Exit Sub
                                    'End If

                                End If

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try
                            '*********************************************************************

                        End If
                    End If

                    '----------------*********************

                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremptionSaisie"))

                ElseIf gArticles.Columns(gArticles.Col).DataField() = "QteCommander" Then

                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremptionSaisie"))

                ElseIf gArticles.Columns(gArticles.Col).DataField() = "DatePeremptionSaisie" Then

                    '' ''    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("NumeroLotArticle"))

                    '' ''ElseIf gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle" Then

                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("PrixAchatHT"))

                ElseIf gArticles.Columns(gArticles.Col).DataField() = "Stock" Then

                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("PrixAchatHT"))

                ElseIf gArticles.Columns(gArticles.Col).DataField() = "Remise" Then

                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("PrixAchatHT"))

                ElseIf gArticles.Columns(gArticles.Col).DataField() = "TVA" Then

                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("PrixAchatHT"))

                ElseIf gArticles.Columns(gArticles.Col).DataField() = "PrixAchatHT" Then ' si on est dans la colonne prix d'achat on passe au nouveau ligne                    


                    '------------------------------ cas ou on a un changement de prix ---------------------------------------            
                    If e.KeyCode = Keys.Enter Then 'And gArticles.Columns(gArticles.Col).DataField() = "PrixAchatHT" Then
                        Dim AncienPrix As Decimal = 0.0
                        Dim ConfirmerChangemant As Boolean = False

                        AncienPrix = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", gArticles.Columns("CodeArticle").Value)

                        Dim AutoriserModificationPrixDansAchat As Boolean
                        Try
                            AutoriserModificationPrixDansAchat = RecupererValeurExecuteScalaire("AutoriserModificationPrixDansAchat", "PARAMETRE_PHARMACIE", "1", "1")
                        Catch
                            AutoriserModificationPrixDansAchat = True
                        End Try


                        If AncienPrix <> CDec(gArticles.Columns("PrixAchatHT").Value) Then  ' And InterventionAvecUnAssistant = True



                            '********************************* Contrôle de l accée *******************************************
                            Dim cmdAssistant As New SqlCommand
                            Dim DemandePotDePasse As Boolean = False

                            cmdAssistant.Connection = ConnectionServeur
                            cmdAssistant.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'INTERVENTION_AVEC_ASSISTANT_DE_CHANGEMANT_DE_PRIX' AND CodeUtilisateur='" + CodeUtilisateur + "'"

                            Try
                                If cmdAssistant.ExecuteScalar() >= 1 Then

                                    Dim InstanceMaquetteChangerPrix As New fMaquetteChangerPrix
                                    fMaquetteChangerPrix.CodeArticle = gArticles.Columns("CodeArticle").Value
                                    fMaquetteChangerPrix.NouveauPrix = gArticles.Columns("PrixAchatHT").Value
                                    fMaquetteChangerPrix.NouvelleQteUnitaire = gArticles.Columns("QuantiteUnitaire").Value.ToString()

                                    InstanceMaquetteChangerPrix.ShowDialog()

                                    ConfirmerChangemant = InstanceMaquetteChangerPrix.ConfirmerChangemant

                                    InstanceMaquetteChangerPrix.Dispose()
                                    InstanceMaquetteChangerPrix.Close()

                                    gArticles.Columns("PrixVenteTTC").Value = fMaquetteChangerPrix.PrixVenteTTC
                                    gArticles.Columns("TVA").Value = fMaquetteChangerPrix.NouveauTVA

                                    CalculerMontants()

                                    If ConfirmerChangemant = False Then
                                        Exit Sub
                                    End If
                                Else
                                    If (Not AutoriserModificationPrixDansAchat) Then
                                        MsgBox("Vous n'avez pas le droit pour changer le prix.")
                                        gArticles.Columns("PrixAchatHT").Value = AncienPrix
                                        CalculerMontants()
                                    End If
                                End If
                            Catch ex As Exception
                                'Console.WriteLine(ex.Message)
                            End Try
                            '*********************************************************************

                        End If
                    End If


                    '-------------------*****************



                    ''------------------------------ cas ou on a un changement de Qte Unitaire ---------------------------------------            
                    'If e.KeyCode = Keys.Enter Then  'And gArticles.Columns(gArticles.Col).DataField() = "QuantiteUnitaire" Then
                    '    Dim AncienneQteUnitaire As Double = 0.0
                    '    Dim ConfirmerChangemant As Boolean = False

                    '    AncienneQteUnitaire = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", gArticles.Columns("CodeArticle").Value)

                    '    If AncienneQteUnitaire.ToString <> gArticles.Columns("QuantiteUnitaire").Value.ToString Then  ' And InterventionAvecUnAssistant = True

                    '        '********************************* Contrôle de l accée *******************************************
                    '        Dim cmdAssistant As New SqlCommand
                    '        Dim DemandePotDePasse As Boolean = False

                    '        cmdAssistant.Connection = ConnectionServeur
                    '        cmdAssistant.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'INTERVENTION_AVEC_ASSISTANT_DE_CHANGEMANT_DE_PRIX' AND CodeUtilisateur='" + CodeUtilisateur + "'"

                    '        Try
                    '            If cmdAssistant.ExecuteScalar() >= 1 Then

                    '                Dim InstanceMaquetteChangerPrix As New fMaquetteChangerPrix
                    '                fMaquetteChangerPrix.CodeArticle = gArticles.Columns("CodeArticle").Value
                    '                fMaquetteChangerPrix.NouveauPrix = gArticles.Columns("PrixAchatHT").Value
                    '                fMaquetteChangerPrix.NouvelleQteUnitaire = gArticles.Columns("QuantiteUnitaire").Value

                    '                InstanceMaquetteChangerPrix.ShowDialog()

                    '                ConfirmerChangemant = InstanceMaquetteChangerPrix.ConfirmerChangemant

                    '                InstanceMaquetteChangerPrix.Dispose()
                    '                InstanceMaquetteChangerPrix.Close()

                    '                gArticles.Columns("PrixVenteTTC").Value = fMaquetteChangerPrix.PrixVenteTTC
                    '                gArticles.Columns("TVA").Value = fMaquetteChangerPrix.NouveauTVA

                    '                CalculerMontants()

                    '                'If ConfirmerChangemant = False Then
                    '                '    Exit Sub
                    '                'End If

                    '            End If

                    '        Catch ex As Exception
                    '            Console.WriteLine(ex.Message)
                    '        End Try
                    '        '*********************************************************************

                    '    End If
                    'End If

                    ''----------------*********************

                    If gArticles(gArticles.RowCount - 1, ("CodeArticle")).ToString <> "" Then

                        NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
                        NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                        NouvelArticle("CodeArticle") = ""
                        NouvelArticle("CodeABarre") = ""
                        NouvelArticle("NumeroLotArticle") = ""
                        dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)

                    End If
                    '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''gArticles.MoveLast()
                    '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''gArticles.FirstRow = gArticles.RowCount - 1
                    gArticles.Row = gArticles.Row + 1

                    Try
                        dsAchat.Tables("ARTICLE").Clear()

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "gArticles_KeyUp", ex.Message, "0000625", "Erreur d'excution de gArticles_KeyUp", True, True, True)
                        Return

                    End Try
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))

                End If

                'If gArticles.Col <> 6 Then
                '    gArticles.EditActive = True
                'End If

            End If

            lInfoBulle.Visible = False

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "gArticles_KeyUp", ex.Message, "0000624", "Erreur d'excution de gArticles_KeyUp", True, True, True)
            Return

        End Try

    End Sub

    Public Sub VerifierSiDejaSaisi(ByVal CodeNewArticle As String)

        Dim i As Integer = 0
        Dim reponse As MsgBoxResult

        Try

            '---------------------------------- test si l article existe deja dans la liste ou nn
            i = 0
            Do While i < gArticles.RowCount - 1

                If gArticles(i, "CodeArticle") = CodeNewArticle And i <> gArticles.Row Then

                    reponse = MsgBox("Article : " + gArticles(i, "Designation") + " existe déja, " + Chr(13) + " OK : pour continuer " + Chr(13) + " ANNULER : pour effacer l article", MsgBoxStyle.OkCancel, "Erreur")

                    If reponse = MsgBoxResult.Cancel Then

                        gArticles.MoveLast()
                        gArticles.Delete()

                        If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then

                            NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
                            NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                            NouvelArticle("CodeArticle") = ""
                            NouvelArticle("CodeABarre") = ""
                            dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)

                        End If

                        Exit Sub

                    Else

                        gArticles.Columns("NumeroLotArticle").Value = ""
                        gArticles.EditActive = False
                        gArticles.Columns("DatePeremptionSaisie").Text = ""
                        gListeRecherche.Visible = False

                    End If

                End If

                i = i + 1

            Loop

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "VerifierSiDejaSaisi", ex.Message, "0000626", "Erreur d'excution de VerifierSiDejaSaisi", True, True, True)
            Return

        End Try

    End Sub

    Private Sub gListeRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListeRecherche.KeyUp
        ' dans cet évènenment on teste si l évènenment est due à un clique sur le boutton entrée et si l'utilisateur a choisi 
        ' un article et si le curseur est dans la colonne de designation 

        'Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date
        Dim NumeroLot As String = ""

        Dim CategorieArticle As Integer = 0

        Dim j As Integer
        Dim NumeroLigne As Integer
        Dim DataRowRecherche As DataRow

        Try

            If gListeRecherche.Visible = False Then
                Exit Sub
            End If

            If e.KeyCode = Keys.Back Then
                gArticles.Focus()
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
                gArticles.MoveLast()
                gArticles.EditActive = True
            End If

            If e.KeyCode = Keys.Enter And (gArticles.Columns(gArticles.Col).DataField() = "Qte" Or gArticles.Columns(gArticles.Col).DataField() = "Designation") Then    'And gArticles.Columns("Designation").Value <> ""

                If dsAchat.Tables("ARTICLE").Rows.Count > 0 Then

                    '--------------------- rehcerche de l'index de la ligne qui contient l'article recherché ---------------

                    For j = 0 To dsAchat.Tables("ARTICLE").Rows.Count - 1

                        DataRowRecherche = dsAchat.Tables("ARTICLE").Rows(j)

                        If DataRowRecherche.Item("CodeArticle") = gListeRecherche.Columns("CodeArticle").Value Then
                            NumeroLigne = j
                        End If

                    Next

                    '------------------- chargement des données ---------------------------------------------- 
                    dr = dsAchat.Tables("ARTICLE").Rows(NumeroLigne)
                    NouvelArticle("NumeroAchat") = RecupereNumero()

                    '---------------------- les préparations ne sont pas autorisées
                    CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", dr.Item("CodeArticle"))

                    If CategorieArticle = 9 Then

                        MsgBox("Saisie des préparations interdite !", MsgBoxStyle.Critical, "Erreur")
                        gArticles.Columns("CodeArticle").Value = ""
                        gArticles.Columns("Designation").Value = ""
                        gArticles.Columns("CodeABarre").Value = ""
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
                        gArticles.EditActive = True

                        Exit Sub

                    End If

                    NouvelArticle("CodeArticle") = dr.Item("CodeArticle")
                    NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))


                    Try

                        'NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                        'NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "gListeRecherche_KeyUp", ex.Message, "0000628", "Erreur d'excution de gListeRecherche_KeyUp", True, True, True)

                    End Try

                    NouvelArticle("Qte") = 1
                    NouvelArticle("QteCommander") = 0
                    NouvelArticle("Stock") = CalculeStock(dr.Item("CodeArticle"))
                    NouvelArticle("Remise") = 0
                    NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("TotalAchatHT") = NouvelArticle("PrixAchatHT") '* dr.Item("PrixVenteTTC")
                    NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    gArticles.Columns("Designation").Value = NouvelArticle("Designation")
                    '----------------------- récupération de la date de péremption et le numéro de lot

                    ' ''Dim AfficherDateLot As String
                    ' ''AfficherDateLot = RecupererValeurExecuteScalaire("AfficherLesDernieresDatesDePeremptionDansLesNouveauxAchats", "PARAMETRES", "POSTE", 0)


                    If AfficherLesDerniereDDPeremptionDansNouveauAchat = True Then
                        StrSQL = " SELECT top(1) DatePeremptionArticle FROM LOT_ARTICLE " + _
                                 "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                                 "' AND CodeArticle='" + dr.Item("CodeArticle") + _
                                 "' Order by DatePeremptionArticle DESC "

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL

                        Try
                            DatePeremption = cmd.ExecuteScalar()
                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Achat", "fAchat", "gListeRecherche_KeyUp", ex.Message, "0000629", "Erreur d'excution de gListeRecherche_KeyUp", True, True, True)

                        End Try

                        If DatePeremption = #12:00:00 AM# Then

                        Else

                            NouvelArticle("DatePeremptionSaisie") = Convert.ToString(DatePeremption).Replace(" 00:00:00", "")

                        End If

                        '' ''StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
                        '' ''         " WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                        '' ''         "' AND CodeArticle='" + dr.Item("CodeArticle") + _
                        '' ''         "' Order by DatePeremptionArticle DESC "

                        '' ''cmd.Connection = ConnectionServeur
                        '' ''cmd.CommandText = StrSQL

                        '' ''Try

                        '' ''    NumeroLot = cmd.ExecuteScalar()

                        '' ''Catch ex As Exception

                        '' ''    'Gérer l'Exception
                        '' ''    fMessageException.Show("Achat", "fAchat", "gListeRecherche_KeyUp", ex.Message, "0000630", "Erreur d'excution de gListeRecherche_KeyUp", True, True, True)

                        '' ''End Try

                        '' ''If NumeroLot = "" Then

                        '' ''Else

                        '' ''    NouvelArticle("NumeroLotArticle") = NumeroLot

                        '' ''End If
                    End If


                    gArticles.Refresh()

                End If

                gListeRecherche.Visible = False
                gArticles.Focus()

                If NumeroLigne = 0 Then

                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))

                Else

                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))

                End If


                gArticles.ScrollGrid(0, gArticles.Row)


            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "gListeRecherche_KeyUp", ex.Message, "0000627", "Erreur d'excution de gListeRecherche_KeyUp", True, True, True)
            Return

        End Try

    End Sub

    Public Sub ChargerDetailArticle(ByVal CodeABarre As String)

        Dim resultat As String = ""
        Dim Supprime As String = ""
        'Dim StrSQL As String = ""
        Dim cmd As New SqlCommand

        Dim DatePeremption As Date
        Dim NumeroLot As String = ""
        Dim CodeArticle As String = ""
        Dim nbre As Integer = 0


        Dim CategorieArticle As Integer = 0

        Try

            If CodeABarre = "" Then
                gArticles.Columns("CodeABarre").Value = ""
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
                Exit Sub
            End If

            Try

                StrSQL = "Select CodeABarre from Article"
                cmdAchat.CommandText = StrSQL
                cmdAchat.Connection = ConnectionServeur
                daAchat = New SqlDataAdapter(cmdAchat)
                daAchat.Fill(dsAchat, "Verification_CodeABarre")
                cbAchat = New SqlCommandBuilder(daAchat)

                For i = 0 To dsAchat.Tables("Verification_CodeABarre").Rows.Count - 1
                    If CodeABarre.ToUpper = dsAchat.Tables("Verification_CodeABarre").Rows(i).Item("CodeABarre").ToString.ToUpper Then
                        nbre = nbre + 1
                    End If
                Next

            Catch ex As Exception
                WriteLine(ex.Message)
            End Try


            If nbre = 0 Then
                lInfoBulle.Visible = True
                lInfoBulle.Text = "Code Article non Valide"
                gArticles.Columns("CodeABarre").Value = ""
                Exit Sub
            Else
                lInfoBulle.Text = ""
                lInfoBulle.Visible = False
            End If

            CodeArticle = RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "CodeABarre", CodeABarre)
            resultat = RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle)
            Supprime = RecupererValeurExecuteScalaire("Supprime", "ARTICLE", "CodeArticle", CodeArticle)

            If resultat <> "" And Supprime = "False" Then

                CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", CodeArticle)
                If CategorieArticle = 9 Then
                    MsgBox("Saisie des préparations interdite !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Columns("CodeABarre").Value = ""
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
                    Exit Sub
                End If

                NouvelArticle("NumeroAchat") = RecupereNumero()
                NouvelArticle("CodeArticle") = CodeArticle
                NouvelArticle("CodeABarre") = CodeABarre
                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)

                Try

                    ' NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    ' NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "ChargerDetailArticle", ex.Message, "0000632", "Erreur d'excution de ChargerDetailArticle", True, True, True)

                End Try

                If IsDBNull(NouvelArticle("Qte")) Then
                    NouvelArticle("Qte") = 1
                End If

                NouvelArticle("QteCommander") = 0

                'NouvelArticle("DatePeremption") = System.DateTime.Now
                NouvelArticle("Stock") = CalculeStock(CodeArticle)
                NouvelArticle("Remise") = 0

                NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("TotalAchatHT") = NouvelArticle("PrixAchatHT")
                NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", CodeArticle)

                If AfficherLesDerniereDDPeremptionDansNouveauAchat = True Then

                    '----------------------- récupération de la date de péremption et du numéro de lot
                    StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] " + _
                             " WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                             "' AND CodeArticle='" + CodeArticle + _
                             "' Order by DatePeremptionArticle DESC "

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        DatePeremption = cmd.ExecuteScalar()

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "ChargerDetailArticle", ex.Message, "0000633", "Erreur d'excution de ChargerDetailArticle", True, True, True)

                    End Try

                    If DatePeremption = #12:00:00 AM# Then

                    Else

                        NouvelArticle("DatePeremptionSaisie") = Convert.ToString(DatePeremption).Replace(" 00:00:00", "") 'DatePeremption

                    End If

                    '' ''StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
                    '' ''        "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                    '' ''        "' AND CodeArticle='" + CodeArticle + _
                    '' ''        "' Order by DatePeremptionArticle DESC "

                    '' ''cmd.Connection = ConnectionServeur
                    '' ''cmd.CommandText = StrSQL

                    '' ''Try

                    '' ''    NumeroLot = cmd.ExecuteScalar()

                    '' ''Catch ex As Exception

                    '' ''    'Gérer l'Exception
                    '' ''    fMessageException.Show("Achat", "fAchat", "ChargerDetailArticle", ex.Message, "0000634", "Erreur d'excution de ChargerDetailArticle", True, True, True)

                    '' ''End Try

                    '' ''If NumeroLot = "" Then

                    '' ''Else

                    '' ''    NouvelArticle("NumeroLotArticle") = NumeroLot

                    '' ''End If

                End If

                gArticles.Refresh()
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))

            Else

                gArticles.Columns("CodeABarre").Value = ""
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))

            End If



        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "ChargerDetailArticle", ex.Message, "0000631", "Erreur d'excution de ChargerDetailArticle", True, True, True)
            Return

        End Try

    End Sub

    Public Sub ModifierDetailArticle(ByVal CodeABarre As String)

        Dim resultat As String = ""
        Dim Supprime As String = ""
        'Dim StrSQL As String = ""
        Dim cmd As New SqlCommand

        Dim DatePeremption As Date
        Dim NumeroLot As String = ""
        Dim CodeArticle As String = ""
        Dim nbre As Integer = 0


        Dim CategorieArticle As Integer = 0

        Try

            If CodeABarre = "" Then
                gArticles.Columns("CodeABarre").Value = ""
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
                Exit Sub
            End If

            Try

                StrSQL = "Select CodeABarre from Article"
                cmdAchat.CommandText = StrSQL
                cmdAchat.Connection = ConnectionServeur
                daAchat = New SqlDataAdapter(cmdAchat)
                daAchat.Fill(dsAchat, "Verification_CodeABarre")
                cbAchat = New SqlCommandBuilder(daAchat)

                For i = 0 To dsAchat.Tables("Verification_CodeABarre").Rows.Count - 1
                    If CodeABarre = dsAchat.Tables("Verification_CodeABarre").Rows(i).Item("CodeABarre") Then
                        nbre = nbre + 1
                    End If
                Next

            Catch ex As Exception
                WriteLine(ex.Message)
            End Try


            If nbre = 0 Then
                lInfoBulle.Visible = True
                lInfoBulle.Text = "Code Article non Valide"
                gArticles.Columns("CodeABarre").Value = ""
                Exit Sub
            Else
                lInfoBulle.Text = ""
                lInfoBulle.Visible = False
            End If

            CodeArticle = RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "CodeABarre", CodeABarre)
            resultat = RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle)
            Supprime = RecupererValeurExecuteScalaire("Supprime", "ARTICLE", "CodeArticle", CodeArticle)

            If resultat <> "" And Supprime = "False" Then

                CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", CodeArticle)
                If CategorieArticle = 9 Then
                    MsgBox("Saisie des préparations interdite !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Columns("CodeABarre").Value = ""
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
                    Exit Sub
                End If

                gArticles(gArticles.Row, "NumeroAchat") = RecupereNumero()
                gArticles(gArticles.Row, "CodeArticle") = CodeArticle
                gArticles(gArticles.Row, "CodeABarre") = CodeABarre
                gArticles(gArticles.Row, "Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)

                Try

                    ' NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    ' NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "ChargerDetailArticle", ex.Message, "0000632", "Erreur d'excution de ChargerDetailArticle", True, True, True)

                End Try

                If IsDBNull(gArticles(gArticles.Row, "Qte")) Then
                    gArticles(gArticles.Row, "Qte") = 1
                End If

                gArticles(gArticles.Row, "QteCommander") = 0

                'gArticles(gArticles.Row, "DatePeremption") = System.DateTime.Now
                gArticles(gArticles.Row, "Stock") = CalculeStock(CodeArticle)
                gArticles(gArticles.Row, "Remise") = 0

                gArticles(gArticles.Row, "PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", CodeArticle)
                gArticles(gArticles.Row, "PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", CodeArticle)
                gArticles(gArticles.Row, "QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", CodeArticle)
                gArticles(gArticles.Row, "TotalAchatHT") = gArticles(gArticles.Row, "PrixAchatHT")
                gArticles(gArticles.Row, "TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", CodeArticle)

                If AfficherLesDerniereDDPeremptionDansNouveauAchat = True Then

                    '----------------------- récupération de la date de péremption et du numéro de lot
                    StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] " + _
                             " WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                             "' AND CodeArticle='" + CodeArticle + _
                             "' Order by DatePeremptionArticle DESC "

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        DatePeremption = cmd.ExecuteScalar()

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "ChargerDetailArticle", ex.Message, "0000633", "Erreur d'excution de ChargerDetailArticle", True, True, True)

                    End Try

                    If DatePeremption = #12:00:00 AM# Then

                    Else

                        gArticles(gArticles.Row, "DatePeremptionSaisie") = Convert.ToString(DatePeremption).Replace(" 00:00:00", "") 'DatePeremption

                    End If

                    '' ''StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
                    '' ''        "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                    '' ''        "' AND CodeArticle='" + CodeArticle + _
                    '' ''        "' Order by DatePeremptionArticle DESC "

                    '' ''cmd.Connection = ConnectionServeur
                    '' ''cmd.CommandText = StrSQL

                    '' ''Try

                    '' ''    NumeroLot = cmd.ExecuteScalar()

                    '' ''Catch ex As Exception

                    '' ''    'Gérer l'Exception
                    '' ''    fMessageException.Show("Achat", "fAchat", "ChargerDetailArticle", ex.Message, "0000634", "Erreur d'excution de ChargerDetailArticle", True, True, True)

                    '' ''End Try

                    '' ''If NumeroLot = "" Then

                    '' ''Else

                    '' ''    gArticles(gArticles.Row, "NumeroLotArticle") = NumeroLot

                    '' ''End If

                End If

                gArticles.Refresh()
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))

            Else

                gArticles.Columns("CodeABarre").Value = ""
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))

            End If



        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "ChargerDetailArticle", ex.Message, "0000631", "Erreur d'excution de ChargerDetailArticle", True, True, True)
            Return

        End Try

    End Sub

    Public Sub CalculerMontants()

        Dim i As Integer
        Dim NouvSolde As Double = 0.0

        Dim MontantHTLigne As Double = 0.0
        Dim TVALigne As Double = 0.0
        Dim RemiseLignel As Double = 0.0

        Dim TotalArticleCourant As Double = 0.0
        Dim QteLigne As Double

        Dim TauxRemiseLigne As Double = 0.0
        Dim MontantRemiseLigne As Double = 0.0

        Dim PrixAchatHTNetLigne As Double = 0.0
        Dim PrixAchatHTLigne As Double = 0.0
        Dim PrixAchatTTCLigne As Double = 0.0
        Dim TotalPrixAchatHTNetLigne As Double = 0.0
        Dim TotalPrixAchatHTLigne As Double = 0.0

        Dim PrixVenteTTCLigne As Double = 0.0
        Dim TotalPrixVenteTTC As Double = 0.0

        Dim TauxTVALigne As Double = 0.0
        Dim MontantTVALigne As Double = 0.0

        Dim TotalHTAchat As Double = 0.0

        Dim AutreMonatant As Double = 0.0

        Try

            TotalTTCAchat = 0.0
            TotalHTNETAchat = 0.0
            TotalRemiseAchat = 0.0
            TotalTVAAchat = 0.0
            TotalVenteTTCAchat = 0.0
            TotalQte = 0
            TotalRemiseAchat = 0.0

            TotalTTCAchat = 0.0
            TotalHTNETAchat = 0.0
            TotalRemiseAchat = 0.0
            TotalTVAAchat = 0.0
            TotalVenteTTCAchat = 0.0
            TotalQte = 0
            TotalRemiseAchat = 0.0



            'If CDbl(tAutre.Text) = 0 Then
            '    AutreMonatant = RecupererValeurExecuteScalaire("Timbre", "PARAMETRE_PHARMACIE", "Code", "1")
            '    tAutre.Text = AutreMonatant
            'Else
            '    AutreMonatant = CDbl(tAutre.Text)
            'End If

            AutreMonatant = CDbl(tAutre.Text)

            '''''''''''''''''''''''''''''''''''''''''
            'Bloque pour  le calcule
            i = 0
            Do While i < gArticles.RowCount

                If gArticles(i, "Designation") <> "" Then

                    If gArticles(i, "PrixAchatHT").ToString = "" Then
                        gArticles(i, "PrixAchatHT") = 0
                    End If

                    'Affecter les valeurs de la Grid au variables
                    QteLigne = gArticles(i, "Qte")
                    TauxRemiseLigne = gArticles(i, "Remise")
                    PrixAchatHTNetLigne = gArticles(i, "PrixAchatHT")
                    PrixAchatHTLigne = gArticles(i, "PrixAchatHT")
                    PrixVenteTTCLigne = gArticles(i, "PrixVenteTTC")
                    TauxTVALigne = gArticles(i, "TVA") / 100

                    'Calculer le mnt total des achats par ligne
                    TotalPrixAchatHTNetLigne = (PrixAchatHTNetLigne * QteLigne)
                    TotalPrixAchatHTLigne = (PrixAchatHTLigne * QteLigne)
                    MontantRemiseLigne = TotalPrixAchatHTNetLigne * (TauxRemiseLigne / 100)
                    TotalPrixAchatHTNetLigne = TotalPrixAchatHTNetLigne - MontantRemiseLigne
                    TotalPrixAchatHTLigne = TotalPrixAchatHTLigne
                    MontantTVALigne = TauxTVALigne * TotalPrixAchatHTNetLigne
                    PrixAchatTTCLigne = TotalPrixAchatHTNetLigne + MontantTVALigne
                    TotalPrixVenteTTC = PrixVenteTTCLigne * QteLigne

                    'Bloque  pour calculer les totaux                
                    TotalRemiseAchat = TotalRemiseAchat + MontantRemiseLigne
                    TotalHTNETAchat = TotalHTNETAchat + TotalPrixAchatHTNetLigne
                    TotalHTAchat = TotalHTAchat + TotalPrixAchatHTLigne
                    TotalTTCAchat = TotalTTCAchat + PrixAchatTTCLigne
                    TotalQte = TotalQte + QteLigne
                    TotalVenteTTCAchat = TotalVenteTTCAchat + TotalPrixVenteTTC
                    TotalTVAAchat = TotalTVAAchat + MontantTVALigne

                    'Bloque Affichage
                    lTotHT.Text = Math.Round(TotalHTAchat, 3)
                    lRemise.Text = Math.Round(TotalRemiseAchat, 3)
                    lHTNet.Text = Math.Round(TotalHTNETAchat, 3)
                    lTotalTTC.Text = Math.Round(TotalTTCAchat, 3)
                    lTotalQte.Text = TotalQte
                    lTotalTVA.Text = Math.Round(TotalTVAAchat, 3)
                    lValeurVenteTTC.Text = Math.Round(TotalVenteTTCAchat, 3)
                    lRemise.Text = Math.Round(TotalRemiseAchat, 3)
                    gArticles(i, "TotalAchatHT") = FormatNumber(Math.Round(TotalPrixAchatHTNetLigne, 3), 3)


                End If

                i = i + 1

            Loop

            'Le cas ou la liste est vide
            If gArticles.RowCount = 1 And gArticles(0, "Designation") = "" Then

                'Bloque Affichage
                lTotHT.Text = Math.Round(0, 3)
                lRemise.Text = Math.Round(0, 3)
                lHTNet.Text = Math.Round(0, 3)
                lTotalTTC.Text = Math.Round(0, 3)
                lTotalQte.Text = 0
                lTotalTVA.Text = Math.Round(0, 3)
                lValeurVenteTTC.Text = Math.Round(0, 3)
                lRemise.Text = Math.Round(0, 3)
                'gArticles(0, "TotalAchatHT") = FormatNumber(Math.Round(0, 3), 3)

            End If

            NouvSolde = CDbl(lSoldeFournisseur.Text) + CDbl(lTotalTTC.Text)
            lNouvSolde.Text = NouvSolde.ToString
            'tAutre.Text = FormatNumber(tAutre.Text, 3)
            lTotalTTC.Text = Math.Round(TotalTTCAchat + AutreMonatant, 3)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "CalculerMontants", ex.Message, "0000635", "Erreur d'excution de CalculerMontants", True, True, True)
            Return

        End Try

    End Sub

    Private Sub bFournisseur_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFournisseur.Click

        'Suivi du scénario 
        fMessageException.Show("Achat", "fAchat", "bFournisseur_Click", "NoException", "NoError", "Clic sur le bouton Fournisseur", False, True, False)

        cmbFournisseur.Focus()
    End Sub

    Private Function convertirADate(ByVal ValeurAConvert As String)
        Dim ValeurReturn As Date
        Dim x As String

        'x = ValeurAConvert.Substring(0, 2) + "/" + ValeurAConvert.Substring(2, 2) + "/" + ValeurAConvert.Substring(4, 4)

        Try
            ValeurReturn = ValeurAConvert 'x
        Catch ex As Exception
            'MsgBox("Veuillez Saisir une date valide!", MsgBoxStyle.Exclamation)

        End Try


        Return ValeurReturn
    End Function

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        Dim _BusinessManagementEntities As Data.BusinessManagement.BusinessManagementEntities = New Data.BusinessManagement.BusinessManagementEntities()
        Dim _Achat As Data.BusinessManagement.ACHAT = New Data.BusinessManagement.ACHAT()
        Dim _AchatDetails As Data.BusinessManagement.ACHAT_DETAILS = New Data.BusinessManagement.ACHAT_DETAILS()

        'Suivi du scénario 
        fMessageException.Show("Achat", "fAchat", "bConfirmer_Click", "NoException", "NoError", "Clic sur le bouton Confirmer", False, True, False)

        Dim I As Integer = 0
        Dim cmd As New SqlCommand
        Dim DaTestFractionnement As New SqlDataAdapter
        'Dim DsTestFractionnement As New DataSet

        Dim QteUnitaireArticle As Integer = 0
        Dim CategorieArticle As Integer = 0
        Dim PreparationArticle As Integer = 0

        Dim NumeroLot As Integer = 0
        Dim NumeroLot1 As Integer = 0
        Dim TestNumeroLot As String = ""
        Dim NouveauNumeroLot As String = ""

        Dim QuantiteLotSansNumero As Integer = 0
        Dim QuantiteLotAInsere As Integer = 0
        Dim NouvelleQuantiteDuLotNegative As Integer = 0

        'Dim StrSQL As String = ""
        Dim DatePeremptionAEnregistrer As String = ""
        Dim AchatAvecLeMemeNumeroBl As Integer = 0

        Dim NumeroLotExistePourLeMmArticle As Integer = 0
        Dim NumeroLotExistePourLaDateIntroduite As String = ""

        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""
        Dim Ordre As Integer = 1

        If Mode = "Modif" Then

            If gArticles.RowCount - 1 = 0 And gArticles(0, "CodeArticle") = "" Then

                If MsgBox("La liste des détails est vide, Voulez-vous supprimer l'Achat N° : " + lNumeroAchat.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Enregistrer") = MsgBoxResult.Yes Then

                    'Pour appeler le Bouton Annuler
                    bAnnuler.PerformClick()

                    'Pour appeler le bouton de Suppression
                    supprimerAchat(True)

                    Exit Sub

                Else

                    'Pour appeler le Bouton Annuler
                    bAnnuler.PerformClick()

                    'Quitter la procedure apres faire annuler
                    Exit Sub

                End If

            Else

                'Exit Sub

            End If

        End If

        'test si achat vide
        If dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle") = "" Then

            MsgBox("Achat Vide !", MsgBoxStyle.Critical, "Erreur")

            If dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 < 0 Then
                bAjouter_Click(sender, e)
            End If

            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
            gArticles.EditActive = True

            Exit Sub

        End If

        '--------------------------- pour verifier le calcul : si l'utilisateur ne clique pas entree
        '--------------------------- sur la cellule qte du dernier ligne la somme TTC sera fausse
        CalculerMontants()
        '--------------------------------------------------------------------------------------

        If cmbFournisseur.Text = "" Then
            MsgBox("Veuillez choisir un Fournisseur !", MsgBoxStyle.Critical, "Erreur")
            Confirmation = True
            cmbFournisseur.Focus()
            Exit Sub
        End If

        If tNumeroBlFact.Text = "" Then
            MsgBox("Veuillez saisir un numero de Facture !", MsgBoxStyle.Critical, "Erreur")
            Confirmation = True
            tNumeroBlFact.Focus()
            Exit Sub
        End If

        If dDateBlFacture.Text = "" Then
            MsgBox("Veuillez saisir la date de la Facture !", MsgBoxStyle.Critical, "Erreur")
            Confirmation = True
            dDateBlFacture.Focus()
            Exit Sub
        End If

        If IsNumeric(tTotalHT.Text) = False Then
            MsgBox("Total Hors Taxe n'est pas une valeur numerique !", MsgBoxStyle.Critical, "Erreur")
            Confirmation = True
            tTotalHT.Focus()
            Exit Sub
        End If

        If CDbl(tTotalHT.Text) <> CDbl(lHTNet.Text) Then
            MsgBox("Total Hors Taxe saisi est différent du Total Hors Taxe Calculé !", MsgBoxStyle.Critical, "Erreur")
            Confirmation = True
            tTotalHT.Focus()
            Exit Sub
        End If

        '------------------------------ verification si le fournisseur admet le même numero du BL 
        StrSQL = " SELECT COUNT(NumeroAchat) FROM [ACHAT] " + _
                 " WHERE CodeFournisseur ='" + cmbFournisseur.SelectedValue + _
                 "' AND [NumeroBL/Facture]='" + tNumeroBlFact.Text + "'"

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL

        Try '''''''''''''''*''''''''''''''''''''
            AchatAvecLeMemeNumeroBl = cmd.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        If AchatAvecLeMemeNumeroBl > 0 And Mode = "Ajout" Then
            MsgBox("Numéro du BL déja utilisé par le même fournisseur !", MsgBoxStyle.Critical, "Erreur")
            tNumeroBlFact.Value = ""
            tNumeroBlFact.Focus()
            Exit Sub
        End If

        '----------------------- contrôle des Numeros des lots, codes articles : insertion des doublons
        '----------------------- (Violation du clé primaire dans la table achat details)

        Dim p As Integer = 0
        Dim q As Integer = 0
        For p = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
            For q = p To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
                If gArticles(p, "CodeArticle") = gArticles(q, "CodeArticle") And p <> q And gArticles(p, "CodeArticle") <> "" And gArticles(q, "CodeArticle") <> "" Then
                    If gArticles(p, "DatePeremptionSaisie").ToString = gArticles(q, "DatePeremptionSaisie").ToString Then
                        MsgBox("l'article " + gArticles(p, "Designation") + " apparaît deux fois avec le même numero de lot ! veuillez supprimer un et ajouter sa quantité dans l'autre", MsgBoxStyle.Critical, "Erreur")
                        Exit Sub
                    End If
                End If
            Next
        Next

        '-------------------- contrôle si on a un lot avec un numéro et sans date de péremption 
        For p = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
            If IsDBNull(gArticles(p, "NumeroLotArticle")) = False Then
                If gArticles(p, "NumeroLotArticle") <> "" And gArticles(p, "DatePeremptionSaisie").ToString = "" Then
                    gArticles(p, "NumeroLotArticle") = ""
                End If
            End If
        Next

        '----------------------- contrôle des dates de péremption si il y a un qui est périmé

        For p = 0 To gArticles.RowCount - 1
            If gArticles(p, "CodeArticle") <> "" And gArticles(p, "DatePeremptionSaisie").ToString <> "" Then
                If convertirADate(gArticles(p, "DatePeremptionSaisie")) < Date.Today Then
                    MsgBox("l'article " + dsAchat.Tables("ACHAT_DETAILS").Rows(p).Item("Designation") + " admet une date de péremption deja dépassé ! veuillez corriger sa date de péremption", MsgBoxStyle.Critical, "Erreur")
                    Exit Sub
                End If
            End If
        Next


        If tTotalHT.Text = "" Then
            tTotalHT.Text = "0.000"
        End If

        '------------------------------ demande du mot de passe
        Dim myMotDePasse As New fMotDePasse
        myMotDePasse.ShowDialog()


        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If

        Dim isPeremier As Boolean = True

repeterSauve:


        If Mode = "Ajout" Then
            NumeroAchat = RecupereNumero()
        End If


        '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''Mode = "Consultation"

        I = 0
        Do While I < dsAchat.Tables("ACHAT_DETAILS").Rows.Count
            If dsAchat.Tables("ACHAT_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then
                If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                    dsAchat.Tables("ACHAT_DETAILS").Rows(I).Delete()
                End If
            End If
            I = I + 1
        Loop

        For I = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
            If dsAchat.Tables("ACHAT_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then
                If Mode = "Modif" Then
                    dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroAchat") = lNumeroAchat.Text
                Else
                    dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroAchat") = NumeroAchat
                End If

                If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremptionSaisie").ToString <> "" Then
                    dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremption") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremptionSaisie")
                End If

                Try
                    If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Remise").ToString() = ".0" Or dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Remise").ToString() = "0." Then
                        dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Remise") = "0"
                    End If

                    If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("TVA").ToString() = ".0" Or dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("TVA").ToString() = "0." Then
                        dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("TVA") = "0"
                    End If

                    If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Stock").ToString() = ".0" Or dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Stock").ToString() = "0." Then
                        dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Stock") = "0"
                    End If

                    If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Qte").ToString() = ".0" Or dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Qte").ToString() = "0." Then
                        dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Qte") = "0"
                    End If
                Catch
                End Try
            End If
        Next


        cmd.Connection = ConnectionServeur

        For I = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
            If Not (dsAchat.Tables("ACHAT_DETAILS").Rows(I).RowState = DataRowState.Deleted) Then

                ''''''''''''''''''Changer etat article
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "UPDATE ARTICLE SET CodeSituation = 1 WHERE CodeArticle = " + Quote(dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle").ToString())
                    cmd.ExecuteNonQuery()
                Catch
                End Try

                '''''''''''''''''''''''''''''''''''''''''''''''''''''''
                If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle").ToString() <> "" And dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremptionSaisie").ToString() <> "" Then
                    StrSQL = " SELECT NumeroLotArticle FROM [LOT_ARTICLE] WHERE CodeArticle =" + _
                             Quote(dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle")) + " AND DatePeremptionArticle = " + Quote(dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremptionSaisie"))
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try '''''''''''''''*''''''''''''''''''''
                        NouveauNumeroLot = cmd.ExecuteScalar().ToString
                        dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle") = NouveauNumeroLot
                    Catch ex As Exception
                        NouveauNumeroLot = ""
                        Console.WriteLine(ex.Message)
                    End Try

                    If NouveauNumeroLot = "" Then
                        '------------------ recupération du dernier numéro de lot pour cet article puis incrémentation
                        StrSQL = " SELECT MAX(case when (isnumeric(NumeroLotArticle)=1) then NumeroLotArticle Else 0 END) FROM [LOT_ARTICLE] WHERE CodeArticle ='" + _
                                 dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") + "'"
                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        Try '''''''''''''''*''''''''''''''''''''
                            NouveauNumeroLot = cmd.ExecuteScalar().ToString
                            If NouveauNumeroLot <> "" Then
                                NouveauNumeroLot = (Convert.ToInt32(NouveauNumeroLot) + 1).ToString
                            Else
                                NouveauNumeroLot = 1
                            End If
                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try
                        dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle") = NouveauNumeroLot
                    End If
                End If
                '''''''''''''''''''''''''''''''''''''''''''''''''''''''




                If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle").ToString <> "" Then
                    StrSQL = " IF NOT EXISTS " + _
                             " (SELECT * FROM LOT_ARTICLE WHERE CodeArticle=" + Quote(dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle")) + _
                             " AND NumeroLotArticle=" + Quote(dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle")) + ")" + _
                             " INSERT INTO LOT_ARTICLE (CodeArticle,NumeroLotArticle,DatePeremptionArticle)" + _
                             " VALUES (" + Quote(dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle")) + _
                             " ," + Quote(dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle").ToString) + _
                             " ," + Quote(convertirADate(dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremptionSaisie").ToString)) + ")"
                    cmd.CommandText = StrSQL

                    Try '''''''''''''''*''''''''''''''''''''
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        MsgBox(ex.Message)
                    End Try
                ElseIf dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle").ToString = "" Then
                    StrSQL = " IF NOT EXISTS " + _
                            " (SELECT * FROM LOT_ARTICLE WHERE CodeArticle=" + Quote(dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle")) + _
                            " AND NumeroLotArticle= '')" + _
                            " INSERT INTO LOT_ARTICLE (CodeArticle,NumeroLotArticle,DatePeremptionArticle)" + _
                            " VALUES (" + Quote(dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle")) + _
                            " , '', NULL) "
                    cmd.CommandText = StrSQL

                    Try '''''''''''''''*''''''''''''''''''''
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        MsgBox(ex.Message)
                    End Try

                End If
            End If
        Next
        cmd.Dispose()

        'daAchatDetails.Update(dsAchat, "ACHAT_DETAILS")


        '------------------------------ enregistrement de l'entête de l'achat -------------------------
        Try

            If Mode = "Ajout" Then
                _Achat = New Data.BusinessManagement.ACHAT()
                _Achat.NumeroAchat = NumeroAchat
            Else
                _Achat = _BusinessManagementEntities.ACHAT.Include("ACHAT_DETAILS").FirstOrDefault(Function(item As Data.BusinessManagement.ACHAT) item.NumeroAchat = lNumeroAchat.Text)
                _Achat.ACHAT_DETAILS.Clear()
            End If


            _Achat.NumeroBL_Facture = tNumeroBlFact.Text
            _Achat.Date = System.DateTime.Now
            _Achat.TotalHT = TotalHTNETAchat + CDbl(lRemise.Text)
            _Achat.TotalTTC = lTotalTTC.Text
            _Achat.TVA = lTotalTVA.Text
            _Achat.TotalRemise = lRemise.Text
            _Achat.Timbre = Timbre
            _Achat.LibellePoste = System.Environment.GetEnvironmentVariable("Poste")
            _Achat.CodePersonnel = CodeOperateur
            _Achat.CodeFournisseur = cmbFournisseur.SelectedValue
            _Achat.Note = "rien"
            _Achat.ValeurVenteTTC = lValeurVenteTTC.Text
            _Achat.Autre = tAutre.Text
            _Achat.DateBlFacture = Convert.ToDateTime(dDateBlFacture.Text)

            dsAchat.Tables("ACHAT_DETAILS").AcceptChanges()
            For I = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
                _AchatDetails = New Data.BusinessManagement.ACHAT_DETAILS()
                _AchatDetails.NumeroAchat = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroAchat")
                _AchatDetails.CodeArticle = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle")
                If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle").ToString = "" Then
                    _AchatDetails.NumeroLotArticle = ""
                Else
                    _AchatDetails.NumeroLotArticle = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle")
                End If
                _AchatDetails.Ordre = I
                _AchatDetails.CodeABarre = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeABarre")
                _AchatDetails.Designation = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Designation")
                If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Designation").ToString() = "" Then
                    _AchatDetails.Designation = RecupererValeurExecuteScalaire("Designation", "ARTICLE", "CodeArticle", _AchatDetails.CodeArticle)
                Else
                    _AchatDetails.Designation = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Designation").ToString()
                End If
                Try
                    If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("LibelleForme").ToString = "" Then
                        _AchatDetails.CodeForme = 0
                    Else
                        _AchatDetails.CodeForme = RecupererValeurExecuteScalaire("CodeForme", "FORME_ARTICLE", "LibelleForme", dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("LibelleForme").ToString)
                    End If
                Catch
                End Try
                _AchatDetails.Qte = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Qte")
                _AchatDetails.Stock = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Stock")
                _AchatDetails.PrixAchatHT = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("PrixAchatHT")
                _AchatDetails.TotalAchatHT = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("TotalAchatHT")
                _AchatDetails.PrixVenteTTC = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("PrixVenteTTC")
                _AchatDetails.Remise = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Remise")
                _AchatDetails.TVA = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("TVA")
                If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremption").ToString = "" Then
                    _AchatDetails.DatePeremption = Nothing
                Else
                    _AchatDetails.DatePeremption = Convert.ToDateTime(dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremption").ToString)
                End If

                If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("QuantiteUnitaire").ToString = "" Then
                    _AchatDetails.QuantiteUnitaire = 1
                Else
                    _AchatDetails.QuantiteUnitaire = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("QuantiteUnitaire")
                End If
                _Achat.ACHAT_DETAILS.Add(_AchatDetails)
            Next


            If Mode = "Ajout" Then
                _BusinessManagementEntities.ACHAT.AddObject(_Achat)
            End If
            _BusinessManagementEntities.SaveChanges()
        Catch ex As Exception
            BLL.ErrorManagement.ErrorService.AddMessage("Achat", Date.Now, System.Environment.GetEnvironmentVariable("Poste"), Operateur, ex.InnerException.Message)
            MessageBox.Show("Problème d'enregistrement." & vbCrLf & "Veuillez contacter votre administrateur système", "", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End Try

        '-------------------------------- Affectation des numeros de facture au commandes convertis         
        Dim k As Integer = 0

        For k = 0 To TableauCommande.Length - 1
            If TableauCommande(k) <> "" Then
                StrSQL = "UPDATE COMMANDE SET NumeroFacture='" + NumeroAchat + _
                         "' WHERE NumeroCommande = '" + TableauCommande(k) + "'"
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL
                Try '''''''''''''''*''''''''''''''''''''
                    cmd.ExecuteNonQuery()

                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "bConfirmer_Click", ex.Message, "0000039", "Erreur d'excution de bConfirmer_Click", True, True, True)

                End Try
            End If
        Next


        Try
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "UPDATE " + _
                            "	ARTICLE " + _
                            "SET " + _
                            "	ARTICLE.NombreCommande = 0 " + _
                            "FROM " + _
                            "	ACHAT_DETAILS " + _
                            "WHERE " + _
                            "	ACHAT_DETAILS.CodeArticle = ARTICLE.CodeArticle " + _
                            "	AND ACHAT_DETAILS.NumeroAchat =  " + Quote(NumeroAchat)
            cmd.ExecuteNonQuery()
        Catch
        End Try

        If Mode = "Modif" Then
            ModuleSurveillance(14, "L'utilisateur " & NomUtilisateur & " a modifié l'achat N°" & NumeroAchat)
        End If

        If Mode = "Ajout" Then
            'Recuperer le dernier Achat
            NumeroligneAchat = selectionDernierLigneAchat()
        End If


        'Initialiser les controles
        initLoadControl()

        'Changer Le MODE en consultation
        Mode = "Consultation"

        'Refreche de dArticle par le dernier Achat
        ChargerAchat(NumeroligneAchat)

        'Verouiller les controles 
        verrouillageBouttonsAchat()

        initNombreAchatInstance()

        GroupeSituationFournisseur.Visible = False
        bQuitter.Focus()


    End Sub


    Private Function ReglerArticlesFractionneesNegatives(ByVal CodeArticle)

        'Dim StrSQL As String = ""
        Dim CmdRegler As New SqlCommand
        Dim DaRegler As New SqlDataAdapter
        Dim Dsregler As New DataSet
        Dim i As Integer = 0
        Dim QteLot As Integer = 0
        Dim QteFinal1 As Double = 0.0
        Dim QteFinal As Integer = 0
        Dim QuantiteARetrancher As Integer = 0

        Try

            CmdRegler.Transaction = TransactionAchat

            StrSQL = "SELECT CodeFractionnement,QuantiteUnitaireMere,QuantiteUnitaire " + _
                     "FROM FRACTIONNEMENT WHERE CodeArticleMere='" + CodeArticle + "'"
            CmdRegler.Connection = ConnectionServeur
            CmdRegler.CommandText = StrSQL
            DaRegler = New SqlDataAdapter(CmdRegler)
            DaRegler.Fill(Dsregler, "FRACTIONNEMENT")

            For i = 0 To Dsregler.Tables("FRACTIONNEMENT").Rows.Count - 1

                StrSQL = " SELECT QteLotArticle FROM [LOT_ARTICLE] " + _
                         " WHERE CodeArticle='" + _
                           Dsregler.Tables("FRACTIONNEMENT").Rows(i).Item("CodeFractionnement") + "' AND " + _
                         " QteLotArticle<0 "

                CmdRegler.Connection = ConnectionServeur
                CmdRegler.CommandText = StrSQL

                Try
                    QteLot = CmdRegler.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If QteLot < 0 Then
                    QteFinal1 = Math.Abs(QteLot * Dsregler.Tables("FRACTIONNEMENT").Rows(i).Item("QuantiteUnitaire")) / Dsregler.Tables("FRACTIONNEMENT").Rows(i).Item("QuantiteUnitaireMere")
                    QteFinal = Math.Round(QteFinal1, 0, MidpointRounding.AwayFromZero) + 1

                    StrSQL = " UPDATE LOT_ARTICLE SET QteLotArticle=QteLotArticle + " + (QteFinal * Dsregler.Tables("FRACTIONNEMENT").Rows(i).Item("QuantiteUnitaireMere") / Dsregler.Tables("FRACTIONNEMENT").Rows(i).Item("QuantiteUnitaire")).ToString + _
                             " WHERE CodeArticle = '" + _
                               Dsregler.Tables("FRACTIONNEMENT").Rows(i).Item("CodeFractionnement") + "' AND " + _
                             " QteLotArticle<0 "

                    CmdRegler.Connection = ConnectionServeur
                    CmdRegler.CommandText = StrSQL

                    Try
                        CmdRegler.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try
                    QuantiteARetrancher += QteFinal
                End If

            Next

            Return QuantiteARetrancher

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "ReglerArticlesFractionneesNegatives", ex.Message, "0000640", "Erreur d'excution de ReglerArticlesFractionneesNegatives", True, True, True)
            Return QuantiteARetrancher

        End Try

    End Function



    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click

        'Suivi du scénario 
        fMessageException.Show("Achat", "fAchat", "bAnnuler_Click", "NoException", "NoError", "Clic sur le bouton Annuler", False, True, False)

        Try

            'Confirmer l'annulation de l'Oppération de l'ajout
            If dsAchat.Tables("ACHAT_DETAILS") IsNot Nothing Then

                If dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 > 0 And gArticles(0, "CodeArticle") <> "" Then
                    If MsgBox("Voulez vous vraiment annuler cet achat ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Annuler Achat") = MsgBoxResult.No Then
                        bTerminal.Enabled = True
                        Exit Sub
                    End If
                End If

            End If

            'Changer Le MODE en consultation
            Mode = "Consultation"

            bTerminal.Enabled = False
            'Initialiser les controles
            initLoadControl()

            'Refreche liste achat 
            ChargerAchat(NumeroligneAchat)

            'Affichage du nombre d'achats en instance 
            initNombreAchatInstance()

            'Verouiller les controles 
            verrouillageBouttonsAchat()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "bAnnuler_Click", ex.Message, "0000641", "Erreur d'excution de bAnnuler_Click", True, True, True)
            Return

        End Try

    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click

        'Suivi du scénario 
        fMessageException.Show("Achat", "fAchat", "bImprimer_Click", "NoException", "NoError", "Clic sur le bouton Imprimer", False, True, False)

        Dim CondCrystal As String = ""
        Dim I As Integer
        Dim num As Integer = 999
        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        Dim MyViewer As New fViewer

        Try

            CondCrystal = "1=1 AND {Vue_EtatAchat.NumeroAchat} = '" + lNumeroAchat.Text + "'"

            For I = 0 To fMain.Tab.TabPages.Count - 1
                If fMain.Tab.TabPages(I).Text = "Impression d'achat" Then
                    'fMain.Tab.TabPages(I).Show()
                    'Exit Sub
                    num = I
                End If
            Next

            CR.FileName = Application.StartupPath + "\EtatAchat.rpt"
            CR.SetParameterValue("pNumeroAchat", lNumeroAchat.Text)


            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)

            Next tbCurrent

            CR.RecordSelectionFormula = CondCrystal

            MyViewer.CRViewer.ReportSource = CR
            fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
            fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
            fMain.Tab.SelectedTab.Text = "Impression d'achat"

            If num <> 999 Then
                fMain.Tab.TabPages(num).Dispose()
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "bImprimer_Click", ex.Message, "0000642", "Erreur d'excution de bImprimer_Click", True, True, True)
            Return

        End Try

    End Sub

    Private Sub bRemise_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRemise.Click
        tRemisePourcentage.Focus()
    End Sub

    Private Sub bRecherche_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRecherche.Click

        'Suivi du scénario 
        fMessageException.Show("Achat", "fAchat", "bRecherche_Click", "NoException", "NoError", "Clic sur le bouton Recherche", False, True, False)

        Mode = "Consultation"
        bTerminal.Enabled = False
        tRecherche.Visible = True

        GroupeFournisseur.Enabled = False 'True
        tNumeroBlFact.Enabled = False 'True
        tNumeroBlFact.Value = ""
        cmbFournisseur.Enabled = False
        dDateBlFacture.Enabled = False
        tTotalHT.Enabled = False

        tRecherche.Text = System.DateTime.Now.Year.ToString + "/"
        tRecherche.Focus()
        tRecherche.Select(tRecherche.Text.Length, 0)

    End Sub

    Public Sub AfficherFicheFournisseur(ByVal CodeFournisseur)

        Dim MyFournisseur As New fFicheFournisseur

        Try

            MyFournisseur.CodeFournisseur = CodeFournisseur
            MyFournisseur.ajoutmodif = "M"
            MyFournisseur.Init()
            MyFournisseur.ShowDialog()
            MyFournisseur.Close()
            MyFournisseur.Dispose()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "AfficherFicheFournisseur", ex.Message, "0000643", "Erreur d'excution de AfficherFicheFournisseur", True, True, True)
            Return

        End Try


    End Sub

    Private Sub cmbFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbFournisseur.KeyUp

        Try

            If e.KeyCode = Keys.F1 And Mode = "Ajout" And cmbFournisseur.Text <> "" Then
                AfficherFicheFournisseur(cmbFournisseur.SelectedValue)
                Exit Sub
            End If

            If e.KeyCode = Keys.Enter Then

                If cmbFournisseur.Text = "" Then
                    cmbFournisseur.Focus()
                ElseIf cmbFournisseur.WillChangeToText = "" And cmbFournisseur.Text <> "" Then

                    '----------------- nouveau fournisseur 
                    Dim reponse As MsgBoxResult
                    reponse = MsgBox("Fournisseur " + cmbFournisseur.Text + " inéxistant, voulez vous le créer  ", MsgBoxStyle.OkCancel, "Erreur")
                    If reponse = MsgBoxResult.Ok Then

                        Dim MyFournisseur As New fFicheFournisseur
                        MyFournisseur.ajoutmodif = "A"
                        MyFournisseur.NomFournisseur = cmbFournisseur.Text

                        MyFournisseur.Init()
                        MyFournisseur.StartPosition = FormStartPosition.CenterScreen
                        MyFournisseur.tCodeFournisseur.Focus()
                        MyFournisseur.FormBorderStyle = Windows.Forms.FormBorderStyle.None
                        MyFournisseur.ShowDialog()

                        If MyFournisseur.ConfirmerEnregistrementDepuisAchat = False Then
                            cmbFournisseur.Text = ""
                        End If

                        MyFournisseur.Close()
                        MyFournisseur.Dispose()

                        'chargement des fournisseurs
                        dsAchat.Tables("FOURNISSEUR").Clear()
                        'Dim StrSQL As String = ""
                        StrSQL = "SELECT CodeFournisseur,NomFournisseur FROM FOURNISSEUR WHERE Supprimer = 0 ORDER BY NomFournisseur ASC"
                        cmdAchat.Connection = ConnectionServeur
                        cmdAchat.CommandText = StrSQL
                        daAchat = New SqlDataAdapter(cmdAchat)
                        daAchat.Fill(dsAchat, "FOURNISSEUR")
                        cmbFournisseur.DataSource = dsAchat.Tables("FOURNISSEUR")
                        cmbFournisseur.ValueMember = "CodeFournisseur"
                        cmbFournisseur.DisplayMember = "NomFournisseur"
                        cmbFournisseur.ColumnHeaders = False
                        cmbFournisseur.Splits(0).DisplayColumns("CodeFournisseur").Width = 0
                        cmbFournisseur.Splits(0).DisplayColumns("NomFournisseur").Width = 160
                        cmbFournisseur.Text = cmbFournisseur.Text
                    Else
                        cmbFournisseur.Text = ""
                    End If

                Else
                    cmbFournisseur.Text = cmbFournisseur.WillChangeToText

                    tNumeroBlFact.Focus()

                End If
            Else
                cmbFournisseur.OpenCombo()
            End If

            If e.KeyCode = Keys.Enter And Confirmation = False Then
                tNumeroBlFact.Focus()
            End If

            Confirmation = False

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "cmbFournisseur_KeyUp", ex.Message, "0000644", "Erreur d'excution de cmbFournisseur_KeyUp", True, True, True)
            Return

        End Try


    End Sub

    'Private Sub cmbFournisseur_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbFournisseur.LostFocus

    '    Try

    '        If cmbFournisseur.WillChangeToText = "" And cmbFournisseur.Text <> "" Then

    '            '----------------- nouveau fournisseur 
    '            Dim reponse As MsgBoxResult
    '            reponse = MsgBox("Fournisseur " + cmbFournisseur.Text + " inéxistant, voulez vous le créer  ", MsgBoxStyle.OkCancel, "Erreur")
    '            If reponse = MsgBoxResult.Ok Then

    '                Dim MyFournisseur As New fFicheFournisseur
    '                MyFournisseur.ajoutmodif = "A"
    '                MyFournisseur.NomFournisseur = cmbFournisseur.Text

    '                MyFournisseur.Init()
    '                MyFournisseur.StartPosition = FormStartPosition.CenterScreen
    '                MyFournisseur.tCodeFournisseur.Focus()
    '                MyFournisseur.FormBorderStyle = Windows.Forms.FormBorderStyle.None
    '                MyFournisseur.ShowDialog()
    '                If MyFournisseur.ConfirmerEnregistrementDepuisAchat = False Then
    '                    cmbFournisseur.Text = ""
    '                End If
    '                MyFournisseur.Close()
    '                MyFournisseur.Dispose()

    '                'chargement des fournisseurs
    '                dsAchat.Tables("FOURNISSEUR").Clear()
    '                'Dim StrSQL As String = ""
    '                StrSQL = "SELECT CodeFournisseur,NomFournisseur FROM FOURNISSEUR ORDER BY NomFournisseur ASC"
    '                cmdAchat.Connection = ConnectionServeur
    '                cmdAchat.CommandText = StrSQL
    '                daAchat = New SqlDataAdapter(cmdAchat)
    '                daAchat.Fill(dsAchat, "FOURNISSEUR")
    '                cmbFournisseur.DataSource = dsAchat.Tables("FOURNISSEUR")
    '                cmbFournisseur.ValueMember = "CodeFournisseur"
    '                cmbFournisseur.DisplayMember = "NomFournisseur"
    '                cmbFournisseur.ColumnHeaders = False
    '                cmbFournisseur.Splits(0).DisplayColumns("CodeFournisseur").Width = 0
    '                cmbFournisseur.Splits(0).DisplayColumns("NomFournisseur").Width = 160
    '                cmbFournisseur.Text = cmbFournisseur.Text
    '            Else
    '                cmbFournisseur.Text = ""
    '            End If
    '        End If

    '    Catch ex As Exception

    '        'Gérer l'Exception
    '        fMessageException.Show("Achat", "fAchat", "cmbFournisseur_LostFocus", ex.Message, "0000645", "Erreur d'excution de cmbFournisseur_LostFocus", True, True, True)
    '        Return

    '    End Try

    'End Sub

    Private Sub cmbFournisseur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbFournisseur.TextChanged

        Try

            If cmbFournisseur.SelectedValue <> Nothing Then

                Dim StrSQLdernierAchat As String = ""
                Dim CmdCalcul As New SqlCommand
                Dim Dernier_Date_Achat As String = ""
                Dim Dernier_Date_Reglement As String = ""
                Dim StrSQLSolde As String = ""
                Dim Somme_Facture As Double = 0.0
                Dim Somme_Reglement As Double = 0.0
                Dim Somme_Echeance As Double = 0.0
                Dim Solde_Intial As Double = 0.0
                Dim difference As Double = 0.0
                Dim StrMatricule As String = ""

                ' récupération de la dernière date d'achat pour le client concerné 
                StrSQLdernierAchat = "SELECT MAX(Date) FROM ACHAT WHERE CodeFournisseur =" + Quote(cmbFournisseur.SelectedValue)
                CmdCalcul.Connection = ConnectionServeur
                CmdCalcul.CommandText = StrSQLdernierAchat

                Try

                    Dernier_Date_Achat = CmdCalcul.ExecuteScalar().ToString

                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "cmbFournisseur_TextChanged", ex.Message, "0000647", "Erreur d'excution de cmbFournisseur_TextChanged", True, True, True)

                End Try

                ' récupération de la dernière date de règlement pour le client concerné 
                StrSQLdernierAchat = "SELECT MAX(Date) FROM REGLEMENT_FOURNISSEUR WHERE CodeFournisseur =" + Quote(cmbFournisseur.SelectedValue)
                CmdCalcul.Connection = ConnectionServeur
                CmdCalcul.CommandText = StrSQLdernierAchat

                Try

                    Dernier_Date_Reglement = CmdCalcul.ExecuteScalar().ToString

                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "cmbFournisseur_TextChanged", ex.Message, "0000648", "Erreur d'excution de cmbFournisseur_TextChanged", True, True, True)

                End Try

                'calcul du solde client en retranchant la somme des montants des règlements de la somme des montants des ventes 
                StrSQLSolde = "SELECT COALESCE(SUM(TotalTTC),0) FROM ACHAT LEFT OUTER JOIN FOURNISSEUR ON ACHAT.CodeFournisseur=FOURNISSEUR.CodeFournisseur WHERE ACHAT.CodeFournisseur =" + Quote(cmbFournisseur.SelectedValue) + " AND ACHAT.Date>FOURNISSEUR.DateInitiale"
                CmdCalcul.Connection = ConnectionServeur
                CmdCalcul.CommandText = StrSQLSolde

                Try

                    Somme_Facture = CmdCalcul.ExecuteScalar().ToString

                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "cmbFournisseur_TextChanged", ex.Message, "0000649", "Erreur d'excution de cmbFournisseur_TextChanged", True, True, True)

                End Try

                StrSQLSolde = "SELECT COALESCE(SUM(Montant),0) FROM REGLEMENT_FOURNISSEUR LEFT OUTER JOIN FOURNISSEUR ON REGLEMENT_FOURNISSEUR.CodeFournisseur=FOURNISSEUR.CodeFournisseur WHERE REGLEMENT_FOURNISSEUR.CodeFournisseur =" + Quote(cmbFournisseur.SelectedValue) + " AND REGLEMENT_FOURNISSEUR.Date>FOURNISSEUR.DateInitiale"
                CmdCalcul.Connection = ConnectionServeur
                CmdCalcul.CommandText = StrSQLSolde

                Try

                    Somme_Reglement = CmdCalcul.ExecuteScalar().ToString


                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "cmbFournisseur_TextChanged", ex.Message, "0000650", "Erreur d'excution de cmbFournisseur_TextChanged", True, True, True)

                End Try

                StrSQLSolde = "SELECT SoldeInitial FROM FOURNISSEUR WHERE CodeFournisseur =" + Quote(cmbFournisseur.SelectedValue)
                CmdCalcul.Connection = ConnectionServeur
                CmdCalcul.CommandText = StrSQLSolde

                Try

                    Solde_Intial = CmdCalcul.ExecuteScalar().ToString
                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "cmbFournisseur_TextChanged", ex.Message, "0000651", "Erreur d'excution de cmbFournisseur_TextChanged", True, True, True)

                End Try

                ' récupération des montants des règlements antidaté nn encaissé
                StrSQLSolde = "SELECT SUM(Montant) " + _
                              "FROM REGLEMENT_FOURNISSEUR LEFT OUTER JOIN NATURE_REGLEMENT ON " + _
                              " REGLEMENT_FOURNISSEUR.CodeNatureReglement=NATURE_REGLEMENT.CodeNatureReglement" + _
                              " WHERE CodeFournisseur =" + _
                              Quote(cmbFournisseur.SelectedValue) + " AND DateEcheance > '" + _
                              System.DateTime.Now.Date.ToString + _
                              "' AND NATURE_REGLEMENT.LibelleNatureReglement='CHEQUE' "

                CmdCalcul.Connection = ConnectionServeur
                CmdCalcul.CommandText = StrSQLSolde

                Try

                    If Not IsDBNull(CmdCalcul.ExecuteScalar()) Then
                        Somme_Echeance = CmdCalcul.ExecuteScalar().ToString
                    End If

                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "cmbFournisseur_TextChanged", ex.Message, "0000652", "Erreur d'excution de cmbFournisseur_TextChanged", True, True, True)

                End Try
                '------------------------------------------------------------------------------
                '-------------------------------- afféctation des valeurs ---------------------
                If (Dernier_Date_Achat) <> "" Then
                    lDDA.Text = Dernier_Date_Achat
                Else
                    lDDA.Text = "-"
                End If

                If (Dernier_Date_Reglement) <> "" Then
                    lDDR.Text = Dernier_Date_Reglement
                Else
                    lDDR.Text = "-"
                End If

                difference = Somme_Facture - Somme_Reglement + Solde_Intial
                lSoldeFournisseur.Text = Convert.ToString(difference)
                lNouvSolde.Text = (CDbl(lSoldeFournisseur.Text) + CDbl(lTotalTTC.Text)).ToString
                lSoldeEnCours.Text = Somme_Echeance.ToString

                ''''GroupeSituationFournisseur.Visible = True

                If ModeADMIN <> "ADMIN" Then
                    GroupeSituationFournisseur.Visible = False
                    lValeurVenteTTC.Visible = False
                    Label14.Visible = False
                Else
                    GroupeSituationFournisseur.Visible = True
                    lValeurVenteTTC.Visible = True
                    Label14.Visible = True
                End If

            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "cmbFournisseur_TextChanged", ex.Message, "0000646", "Erreur d'excution de cmbFournisseur_TextChanged", True, True, True)
            Return

        End Try

    End Sub

    Private Sub bLast_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLast.Click

        'Suivi du scénario 
        fMessageException.Show("Achat", "fAchat", "bLast_Click", "NoException", "NoError", "Clic sur le bouton Laste", False, True, False)

        Try

            'Changer le mode en Mode Consultation
            Mode = "Consultation"
            bTerminal.Enabled = False
            'Appel Pour selectionner la derniere ligne 
            NumeroligneAchat = selectionDernierLigneAchat()

            'Appel pour charger les information de l'Achat en question
            ChargerAchat(NumeroligneAchat)

            GroupeSituationFournisseur.Visible = False

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "bLast_Click", ex.Message, "0000653", "Erreur d'excution de bLast_Click", True, True, True)
            Return

        End Try

    End Sub
    'Paramétre: pNumeroLigneAchat
    '           C'est l'élément actuel, on va faire 
    '           seulement une selection de la ligne en question

    Private Sub ChargerAchat(ByVal pNumeroLigneAchat As String)

        '--------------------------Declaration 
        Dim StrSQL As String
        Dim DataRowAffichageEntete As DataRow
        Dim HTNet As Double = 0.0


        Try

            '--------------------------Traitement

            'Vider la DS ACHAT
            dsAchat.Tables("ACHAT").Clear()

            'Vider la DS ACHAT_DETAILS
            dsAchat.Tables("ACHAT_DETAILS").Clear()

            'Chargement de l'entête de l'achat 
            Try

                StrSQL = " SELECT * FROM (  " + _
                " SELECT *, ROW_NUMBER() OVER (ORDER BY NumeroAchat) as row FROM ACHAT " + _
                "              ) a WHERE row > " & pNumeroLigneAchat - 1 & " AND  row <= " & pNumeroLigneAchat

                cmdAchatEntete.Connection = ConnectionServeur
                cmdAchatEntete.CommandText = StrSQL
                daAchatEntete = New SqlDataAdapter(cmdAchatEntete)
                daAchatEntete.Fill(dsAchat, "ACHAT")
                cbAchatEntete = New SqlCommandBuilder(daAchatEntete)

            Catch ex As Exception

                'Gérer l'Exception
                fMessageException.Show("Achat", "fAchat", "ChargerAchat", ex.Message, "0000012", "Erreur lors d'executer la requette", True, True, True)

            End Try

            'Chargement Details Achat
            'Lire le numéro Achat
            If dsAchat.Tables("ACHAT").Rows.Count > 0 Then

                NumeroAchat = dsAchat.Tables("ACHAT").Rows(0).Item("NumeroAchat")

            Else

                NumeroAchat = "0"

            End If

            'Tester si on atteint la premiere ligne
            'pour desactiver les BTN Précedent et Premier élément        
            If NumeroligneAchat = 1 Then

                bPrevious.Enabled = False
                bFirst.Enabled = False

            Else

                bPrevious.Enabled = True
                bFirst.Enabled = True

            End If

            'Tester si on atteint la derniere ligne
            'pour desactiver les BTN Siuvant et Dernier élément
            If NumeroligneAchat = selectionDernierLigneAchat() Then

                bNext.Enabled = False
                bLast.Enabled = False

            Else

                bNext.Enabled = True
                bLast.Enabled = True

            End If

            If NumeroligneAchat = 0 Then

                bNext.Enabled = False
                bLast.Enabled = False
                bNext.Enabled = False
                bLast.Enabled = False

            End If


            Try

                ' Chargement des détails des achats 
                StrSQL = "SELECT NumeroAchat," + _
                         "CodeArticle," + _
                         "CodeABarre," + _
                         "Designation," + _
                         "'' AS LibelleForme," + _
                         "Qte," + _
                         "QuantiteUnitaire, " + _
                         " '-' AS QteCommander," + _
                         "convert(varchar, DatePeremption, 103) as DatePeremptionSaisie," + _
                         "NumeroLotArticle," + _
                         "Stock," + _
                          "Remise " + _
                         "Remise," + _
                         "PrixAchatHT," + _
                         "PrixVenteTTC," + _
                         "TotalAchatHT," + _
                         "TVA," + _
                         "Ordre, " + _
                         "DatePeremption, " + _
                         "'' AS Vide " + _
                         "FROM " + _
                         "ACHAT_DETAILS " + _
                         "WHERE NumeroAchat =" + Quote(NumeroAchat) + " ORDER BY Ordre"

                '"CASE   WHEN Remise = 0 THEN SUBSTRING ( CONVERT(varchar(22), '0.000'),1,1) else SUBSTRING ( CONVERT(varchar(22), ACHAT_DETAILS.Remise), 1,21)  END   AS " + _

                cmdAchatDetail.Connection = ConnectionServeur
                cmdAchatDetail.CommandText = StrSQL
                daAchatDetails = New SqlDataAdapter(cmdAchatDetail)
                daAchatDetails.Fill(dsAchat, "ACHAT_DETAILS")
                cbAchatDetails = New SqlCommandBuilder(daAchatDetails)

            Catch ex As Exception

                'Gérer l'Exception
                fMessageException.Show("Achat", "fAchat", "ChargerAchat", ex.Message, "0000025", "Erreur lors d'executer la requette", True, True, True)

            End Try

            ' Affichage ds informations de l'achat

            Try

                'Si le  mode est modification
                If Mode = "Modif" Or Mode = "Consultation" Then

                    If dsAchat.Tables("ACHAT").Rows.Count > 0 Then

                        DataRowAffichageEntete = dsAchat.Tables("ACHAT").Select("NumeroAchat=" + Quote(NumeroAchat))(0)

                        lNumeroAchat.Text = dsAchat.Tables("ACHAT").Rows(dsAchat.Tables("ACHAT").Rows.Count - 1)("NumeroAchat")
                        lDateAchat.Text = dsAchat.Tables("ACHAT").Rows(dsAchat.Tables("ACHAT").Rows.Count - 1)("Date")
                        cmbFournisseur.SelectedValue = dsAchat.Tables("ACHAT").Rows(dsAchat.Tables("ACHAT").Rows.Count - 1)("CodeFournisseur")
                        dDateBlFacture.Value = dsAchat.Tables("ACHAT").Rows(dsAchat.Tables("ACHAT").Rows.Count - 1)("DateBlFacture")

                        tNumeroBlFact.Value = dsAchat.Tables("ACHAT").Rows(dsAchat.Tables("ACHAT").Rows.Count - 1)("NumeroBL/Facture").ToString

                        TotalTTCAchat = dsAchat.Tables("ACHAT").Rows(dsAchat.Tables("ACHAT").Rows.Count - 1)("TotalTTC")
                        TotalHTNETAchat = dsAchat.Tables("ACHAT").Rows(dsAchat.Tables("ACHAT").Rows.Count - 1)("TotalHT")
                        lTotHT.Text = TotalHTNETAchat.ToString

                        lTotalTVA.Text = dsAchat.Tables("ACHAT").Rows(dsAchat.Tables("ACHAT").Rows.Count - 1)("TVA")
                        lValeurVenteTTC.Text = dsAchat.Tables("ACHAT").Rows(dsAchat.Tables("ACHAT").Rows.Count - 1)("ValeurVenteTTC")

                        lRemise.Text = dsAchat.Tables("ACHAT").Rows(dsAchat.Tables("ACHAT").Rows.Count - 1)("TotalRemise")
                        tAutre.Value = dsAchat.Tables("ACHAT").Rows(dsAchat.Tables("ACHAT").Rows.Count - 1)("Autre")

                        lTotalTTC.Text = Math.Round(TotalTTCAchat, 3)

                        lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsAchat.Tables("ACHAT").Rows(dsAchat.Tables("ACHAT").Rows.Count - 1)("CodePersonnel"))

                        HTNet = (CDbl(lTotHT.Text) - CDbl(lRemise.Text)).ToString

                        lHTNet.Text = HTNet.ToString
                        tTotalHT.Value = lHTNet.Text

                        'Information fournisseur Invisible
                        GroupeSituationFournisseur.Visible = False

                    End If

                End If
                If Mode = "Modif" Or Mode = "Ajout" Then
                    tAutre.Enabled = True
                Else
                    tAutre.Enabled = False
                End If
            Catch ex As Exception

                'Gérer l'Exception
                fMessageException.Show("Achat", "fAchat", "ChargerAchat", ex.Message, "0000655", "Erreur lors d'executer la requette", True, True, True)

            End Try


        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "ChargerAchat", ex.Message, "0000654", "Erreur d'excution de ChargerAchat", True, True, True)
            Return

        End Try

    End Sub

    Private Sub bFirst_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFirst.Click

        'Suivi du scénario 
        fMessageException.Show("Achat", "fAchat", "bFirst_Click", "NoException", "NoError", "Clic sur le bouton First", False, True, False)

        Try

            'Changer le mode en Mode Consultation
            Mode = "Consultation"

            bTerminal.Enabled = False
            'Appel Pour selectionner le dernier ligne 
            selectionPrmierLigneAchat()

            'Appel pour charger les information de l'Achat en question
            ChargerAchat(NumeroligneAchat)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "ChargerAchat", ex.Message, "0000656", "Erreur d'excution de ChargerAchat", True, True, True)
            Return

        End Try

    End Sub

    Private Sub bNext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bNext.Click

        'Suivi du scénario 
        fMessageException.Show("Achat", "fAchat", "bNext_Click", "NoException", "NoError", "Clic sur le bouton Next", False, True, False)

        Try

            'Changer le mode en Mode Consultation
            Mode = "Consultation"

            bTerminal.Enabled = False

            'Appel Pour selectionner l'element suivant 
            selectionLigneAchatSuivant()

            'Appel pour charger les information de l'Achat en question
            ChargerAchat(NumeroligneAchat)

            GroupeSituationFournisseur.Visible = False

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "ChargerAchat", ex.Message, "0000656", "Erreur d'excution de ChargerAchat", True, True, True)
            Return

        End Try

    End Sub

    Private Sub bPrevious_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPrevious.Click

        'Suivi du scénario 
        fMessageException.Show("Achat", "fAchat", "bPrevious_Click", "NoException", "NoError", "Clic sur le bouton Previous", False, True, False)

        Try

            'Changer le mode en Mode Consultation
            Mode = "Consultation"

            bTerminal.Enabled = False

            'Appel Pour selectionner l'element precedent 
            selectionLigneAchatPrecedent()

            'Appel pour charger les information de l'Achat en question
            ChargerAchat(NumeroligneAchat)

            GroupeSituationFournisseur.Visible = False

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "bPrevious_Click", ex.Message, "0000657", "Erreur d'excution de bPrevious_Click", True, True, True)
            Return

        End Try

    End Sub

    Private Sub tRemisePourcentage_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRemisePourcentage.KeyUp


        Dim I As Integer

        Try

            If e.KeyCode = Keys.Enter Then
                If tRemisePourcentage.Text = "" Then
                    tRemisePourcentage.Text = "0.000"
                End If
                '---------------------- rendre le remis dans toutes les lignes
                For I = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
                    If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") <> "" Then
                        dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Remise") = tRemisePourcentage.Text
                    End If
                Next

                CalculerMontants()
                '---------------------- calculer le nouveau net à payer
                tRemisePourcentage.Text = (Math.Round(CDbl(tRemisePourcentage.Text), 3)).ToString
                '------------ déja calculé dans CalculerMonatant
                'tNetAPayer.Text = (CDbl(lTotalTTC.Text) / 100) * (100 - CDbl(tRemisePourcentage.Text))
                'lRemise.Text = Math.Round((CDbl(lTotalTTC.Text) / 100) * CDbl(tRemisePourcentage.Text), 3)
            End If

            '------------------------ controle du point et de 3 chiffres aprés la virgule 


            Dim Position As Integer = 0

            If e.KeyCode = Keys.Decimal Then
                Position = InStr(tRemisePourcentage.Text, ".")
                tRemisePourcentage.Text = tRemisePourcentage.Text.Substring(0, Position)
                tRemisePourcentage.Select(tRemisePourcentage.Text.Length, 0)
                Exit Sub
            End If

            If tRemisePourcentage.Text.Contains(".") Then
                Position = InStr(tRemisePourcentage.Text, ".")
                If tRemisePourcentage.Text.Length - Position > 3 Then
                    tRemisePourcentage.Text = tRemisePourcentage.Text.Substring(0, Position + 3)
                    tRemisePourcentage.Select(tRemisePourcentage.Text.Length, 0)
                End If
            End If

            If e.KeyCode = Keys.Enter Then
                bConfirmer.Focus()
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "tRemisePourcentage_KeyUp", ex.Message, "0000658", "Erreur d'excution de tRemisePourcentage_KeyUp", True, True, True)
            Return

        End Try

    End Sub

    Private Sub tRemisePourcentage_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tRemisePourcentage.LostFocus

        Try

            tRemisePourcentage.Text = tRemisePourcentage.Text
            If tRemisePourcentage.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(tRemisePourcentage.Text, ".")
                If tRemisePourcentage.Text.Length - x = 1 Then
                    tRemisePourcentage.Text = tRemisePourcentage.Text + ("00")
                ElseIf tRemisePourcentage.Text.Length - x = 2 Then
                    tRemisePourcentage.Text = tRemisePourcentage.Text + ("0")
                End If
            Else
                tRemisePourcentage.Text = tRemisePourcentage.Text + ".000"
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "tRemisePourcentage_LostFocus", ex.Message, "0000659", "Erreur d'excution de tRemisePourcentage_LostFocus", True, True, True)
            Return

        End Try

    End Sub


    Private Sub tNetAPayer_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)

        Try

            If e.KeyCode = Keys.Enter Then
                tRemisePourcentage.Text = 100 - (100 / CDbl(lTotalTTC.Text) * CDbl(lHTNet.Text))
                lRemise.Text = (CDbl(lTotalTTC.Text) - CDbl(lHTNet.Text)).ToString
                bConfirmer.Focus()
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "tNetAPayer_KeyUp", ex.Message, "0000660", "Erreur d'excution de tNetAPayer_KeyUp", True, True, True)
            Return

        End Try

    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click

        'Le variable  msgShow est de type boolean, qui va autorise (ou pas ) l'affichage du 
        'Message de la suppression de tout l'achat
        supprimerAchat(False)

    End Sub

    'Le variable  msgShow est de type boolean, qui va autorise (ou pas ) l'affichage du 
    'Message de la suppression de tout l'achat
    Private Sub supprimerAchat(ByVal msgShow As Boolean)

        Try


            Dim cmd As New SqlCommand
            'Dim NumeroAchat As String = ""
            Dim i As Integer = 0
            'Dim StrSQL As String = ""
            Dim NbreDesLots As Integer = 0
            Dim NBRAchat As Integer = 0

            Dim QteRestante As Integer = 0

            Dim DataRowAchatSupprime As DataRow = Nothing
            Dim NumeroAchatSupprime As String = ""

            If Mode = "Ajout" Or Mode = "Modif" Then

                'Si  la liste est vide, quitter la procedure
                If gArticles.RowCount = 0 Then
                    Exit Sub
                End If


                'If gArticles.Row <= dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 Then
                If gArticles.RowCount > 0 Then

                    'Test si la lign est NEW ADDED et elle est vide
                    If gArticles(gArticles.Row, ("CodeArticle")) <> "" Then

                        'Suivi du scénario 
                        fMessageException.Show("Achat", "fAchat", "supprimerAchat", "NoException", "NoError", "Clic sur le bouton Supprimer Détail Achat : " + gArticles(gArticles.Row, ("CodeArticle")), False, True, False)

                        gArticles.Delete()
                        CalculerMontants()
                    End If

                    'If dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 < 0 Then
                    If gArticles.RowCount = 0 Then
                        'bAjouter.PerformClick()
                        fAjout(True)
                    End If

                    'gArticles.Col = 2
                    'gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
                    dsAchat.Tables("ACHAT_DETAILS").AcceptChanges()
                    Exit Sub
                Else

                    CalculerMontants()

                End If

            Else

                If ControleDAcces(6, "SUPPRESSION_ACHAT") = "False" Then
                    Exit Sub
                End If


                'Suivi du scénario 
                fMessageException.Show("Achat", "fAchat", "supprimerAchat", "NoException", "NoError", "Clic sur le bouton Supprimer Achat: " + NumeroAchat, False, True, False)


                'Controle si cet achat est réglé ou non
                StrSQL = "SELECT COUNT(NumeroAchat) FROM REGLEMENT_FOURNISSEUR_ACHAT " + _
                                "WHERE NumeroAchat =" + Quote(NumeroAchat) + ""
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL
                Try

                    If Not IsDBNull(cmd.ExecuteScalar()) Then
                        NBRAchat = cmd.ExecuteScalar()
                    End If

                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000662", "Erreur d'excution de supprimerAchat", True, True, True)

                End Try

                If NBRAchat <> 0 Then
                    MsgBox("Cet achat est réglé, Suppression réfusé !", MsgBoxStyle.Critical, "Erreur")
                    Exit Sub
                End If

                If msgShow = False Then ' avec message de confirmation

                    If MsgBox("Voulez vous vraiment supprimer cet achat " + lNumeroAchat.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then

                        If NumeroAchat = "" Then
                            MsgBox("Aucun achat à supprimer !", MsgBoxStyle.Critical, "Information")
                            Exit Sub
                        End If

                        Dim ConfirmerEnregistrer As Boolean = False
                        Dim CodeOperateur As String = ""

                        'Application.DoEvents()

                        '-------- demande du mot de passe

                        Dim myMotDePasse As New fMotDePasse
                        myMotDePasse.ShowDialog()
                        ConfirmerEnregistrer = fMotDePasse.Confirmer
                        CodeOperateur = fMotDePasse.CodeOperateur
                        myMotDePasse.Dispose()
                        myMotDePasse.Close()
                        If ConfirmerEnregistrer = False Then
                            Exit Sub

                        End If

                        If (dsAchat.Tables.IndexOf("ACHAT_A_SUPPRIME") > -1) Then
                            dsAchat.Tables("ACHAT_A_SUPPRIME").Clear()
                        End If
                        If (dsAchat.Tables.IndexOf("ACHAT_SUPPRIME") > -1) Then
                            dsAchat.Tables("ACHAT_SUPPRIME").Clear()
                        End If
                        If (dsAchat.Tables.IndexOf("ACHAT_DETAILS_A_SUPPRIME") > -1) Then
                            dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Clear()
                        End If
                        If (dsAchat.Tables.IndexOf("ACHAT_SUPPRIME_DETAILS") > -1) Then
                            dsAchat.Tables("ACHAT_SUPPRIME_DETAILS").Clear()
                        End If

                        NumeroAchatSupprime = RecupereNumeroAchatSupprime()

                        Try

                            'chargement des Entêtes de la achat          
                            StrSQL = "SELECT * FROM ACHAT WHERE NumeroAchat='" + NumeroAchat + "'"
                            cmdAchat.Connection = ConnectionServeur
                            cmdAchat.CommandText = StrSQL
                            daAchat = New SqlDataAdapter(cmdAchat)
                            daAchat.Fill(dsAchat, "ACHAT_A_SUPPRIME")

                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000663", "Erreur d'excution de supprimerAchat", True, True, True)

                        End Try

                        Try

                            'chargement de l'entête dans la datatble qui correspond à la table ACHAT_SUPPRIME de l achat 
                            StrSQL = "SELECT TOP(0) * FROM ACHAT_SUPPRIME "
                            cmdAchat.Connection = ConnectionServeur
                            cmdAchat.CommandText = StrSQL
                            daAchat = New SqlDataAdapter(cmdAchat)
                            daAchat.Fill(dsAchat, "ACHAT_SUPPRIME")
                            cbAchat = New SqlCommandBuilder(daAchat)

                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000664", "Erreur d'excution de supprimerAchat", True, True, True)

                        End Try

                        Try

                            'ajout d'un nouvel enregistrement vide dans les datatables convenables
                            DataRowAchatSupprime = dsAchat.Tables("ACHAT_SUPPRIME").NewRow()

                            DataRowAchatSupprime("NumeroAchatSupprime") = NumeroAchatSupprime

                            DataRowAchatSupprime("NumeroAchat") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("NumeroAchat")
                            DataRowAchatSupprime("Date") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("Date")
                            DataRowAchatSupprime("TotalHT") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("TotalHT")
                            DataRowAchatSupprime("TVA") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("TVA")
                            DataRowAchatSupprime("TotalTTC") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("TotalTTC")
                            DataRowAchatSupprime("TotalRemise") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("TotalRemise")
                            DataRowAchatSupprime("Timbre") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("Timbre")
                            DataRowAchatSupprime("CodeFournisseur") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("CodeFournisseur")
                            DataRowAchatSupprime("CodePersonnel") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("CodePersonnel")
                            DataRowAchatSupprime("NumeroBL/Facture") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("NumeroBL/Facture")
                            DataRowAchatSupprime("LibellePoste") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("LibellePoste")
                            DataRowAchatSupprime("DateBlFacture") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("DateBlFacture")
                            DataRowAchatSupprime("ValeurVenteTTC") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("ValeurVenteTTC")

                            DataRowAchatSupprime("Autre") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("Autre")

                            DataRowAchatSupprime("DateSuppression") = System.DateTime.Now
                            DataRowAchatSupprime("CodePersonnelSupprime") = CodeOperateur
                            DataRowAchatSupprime("NomPersonnelSupprime") = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", CodeOperateur)

                            dsAchat.Tables("ACHAT_SUPPRIME").Rows.Add(DataRowAchatSupprime)

                            daAchat.Update(dsAchat, "ACHAT_SUPPRIME")

                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000665", "Erreur d'excution de supprimerAchat", True, True, True)

                            dsAchat.Reset()

                        End Try


                        Try

                            'chargement des Details de la vente 
                            StrSQL = "SELECT * FROM ACHAT_DETAILS WHERE NumeroAchat='" + NumeroAchat + "'"
                            cmdAchat.Connection = ConnectionServeur
                            cmdAchat.CommandText = StrSQL
                            daAchat = New SqlDataAdapter(cmdAchat)
                            daAchat.Fill(dsAchat, "ACHAT_DETAILS_A_SUPPRIME")

                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000666", "Erreur d'excution de supprimerAchat", True, True, True)

                        End Try

                        Try

                            'chargement des Details de la vente dans une datatable qui correspond a la tabel VENTE_SUPPRIME_DETAILS
                            StrSQL = "SELECT * FROM ACHAT_SUPPRIME_DETAILS "
                            cmdAchat.Connection = ConnectionServeur
                            cmdAchat.CommandText = StrSQL
                            daAchat = New SqlDataAdapter(cmdAchat)
                            daAchat.Fill(dsAchat, "ACHAT_SUPPRIME_DETAILS")
                            cbAchat = New SqlCommandBuilder(daAchat)

                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000667", "Erreur d'excution de supprimerAchat", True, True, True)

                        End Try

                        Try

                            For i = 0 To dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows.Count - 1

                                DataRowAchatSupprime = dsAchat.Tables("ACHAT_SUPPRIME_DETAILS").NewRow()

                                DataRowAchatSupprime("NumeroAchatSupprime") = NumeroAchatSupprime

                                DataRowAchatSupprime("NumeroAchat") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("NumeroAchat")
                                DataRowAchatSupprime("CodeArticle") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("CodeArticle")
                                DataRowAchatSupprime("CodeABarre") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("CodeABarre")
                                DataRowAchatSupprime("NumeroLotArticle") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("NumeroLotArticle")
                                DataRowAchatSupprime("Designation") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("Designation")

                                DataRowAchatSupprime("CodeForme") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("CodeForme")
                                DataRowAchatSupprime("Qte") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("Qte")
                                DataRowAchatSupprime("Stock") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("Stock")

                                DataRowAchatSupprime("PrixAchatHT") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("PrixAchatHT")
                                DataRowAchatSupprime("TotalAchatHT") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("TotalAchatHT")
                                DataRowAchatSupprime("PrixVenteTTC") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("PrixVenteTTC")

                                DataRowAchatSupprime("Remise") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("Remise")
                                DataRowAchatSupprime("TVA") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("TVA")
                                DataRowAchatSupprime("DatePeremption") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("DatePeremption")

                                dsAchat.Tables("ACHAT_SUPPRIME_DETAILS").Rows.Add(DataRowAchatSupprime)

                            Next

                            daAchat.Update(dsAchat, "ACHAT_SUPPRIME_DETAILS")

                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000668", "Erreur d'excution de supprimerAchat", True, True, True)

                            dsAchat.Reset()

                        End Try

                        '--------------- suppression des affectation des commandes qui existe 
                        Try
                            StrSQL = "UPDATE COMMANDE SET NumeroFacture=NULL WHERE NumeroFacture ='" + lNumeroAchat.Text + "'"

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL


                            cmd.ExecuteNonQuery()

                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000669", "Erreur d'excution de supprimerAchat", True, True, True)

                        End Try

                        '--------------- suppression des details de la achats
                        Try
                            NumeroAchat = lNumeroAchat.Text

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = "DELETE FROM ACHAT_DETAILS WHERE NumeroAchat ='" + NumeroAchat + "'"
                            cmd.ExecuteNonQuery()

                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000670", "Erreur d'excution de supprimerAchat", True, True, True)

                        End Try

                        '--------------- suppression de la achats
                        Try
                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = "DELETE FROM ACHAT WHERE NumeroAchat ='" + NumeroAchat + "'"
                            cmd.ExecuteNonQuery()

                            'Init()

                            'Ma nouvelle position
                            If NumeroligneAchat > 1 Then
                                NumeroligneAchat = NumeroligneAchat - 1
                            End If

                            ChargerAchat(NumeroligneAchat)

                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000671", "Erreur d'excution de supprimerAchat", True, True, True)

                        End Try

                        MsgBox("Achat supprimé !", MsgBoxStyle.Information, "Information")
                        ModuleSurveillance(15, "L'utilisateur " + NomUtilisateur + " a supprimé l'achat N° " + NumeroAchatSupprime)

                    End If
                Else '  sans message de confirmation

                    If NumeroAchat = "" Then
                        MsgBox("Aucun achat à supprimer !", MsgBoxStyle.Critical, "Information")
                        Exit Sub
                    End If

                    Dim ConfirmerEnregistrer As Boolean = False
                    Dim CodeOperateur As String = ""

                    Application.DoEvents()

                    '------------------------------ demande du mot de passe

                    Try

                        Dim myMotDePasse As New fMotDePasse
                        myMotDePasse.ShowDialog()

                        ConfirmerEnregistrer = fMotDePasse.Confirmer
                        CodeOperateur = fMotDePasse.CodeOperateur

                        myMotDePasse.Dispose()
                        myMotDePasse.Close()

                        If ConfirmerEnregistrer = False Then
                            Exit Sub
                        End If

                        If (dsAchat.Tables.IndexOf("ACHAT_A_SUPPRIME") > -1) Then
                            dsAchat.Tables("ACHAT_A_SUPPRIME").Clear()
                        End If
                        If (dsAchat.Tables.IndexOf("ACHAT_SUPPRIME") > -1) Then
                            dsAchat.Tables("ACHAT_SUPPRIME").Clear()
                        End If
                        If (dsAchat.Tables.IndexOf("ACHAT_DETAILS_A_SUPPRIME") > -1) Then
                            dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Clear()
                        End If
                        If (dsAchat.Tables.IndexOf("ACHAT_SUPPRIME_DETAILS") > -1) Then
                            dsAchat.Tables("ACHAT_SUPPRIME_DETAILS").Clear()
                        End If

                        NumeroAchatSupprime = RecupereNumeroAchatSupprime()

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000672", "Erreur d'excution de supprimerAchat", True, True, True)

                    End Try

                    Try

                        'chargement des Entêtes de la achat          
                        StrSQL = "SELECT * FROM ACHAT WHERE NumeroAchat='" + NumeroAchat + "'"
                        cmdAchat.Connection = ConnectionServeur
                        cmdAchat.CommandText = StrSQL
                        daAchat = New SqlDataAdapter(cmdAchat)
                        daAchat.Fill(dsAchat, "ACHAT_A_SUPPRIME")

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000673", "Erreur d'excution de supprimerAchat", True, True, True)

                    End Try

                    Try

                        'chargement de l'entête dans la datatble qui correspond à la table ACHAT_SUPPRIME de l achat 
                        StrSQL = "SELECT TOP(0) * FROM ACHAT_SUPPRIME "
                        cmdAchat.Connection = ConnectionServeur
                        cmdAchat.CommandText = StrSQL
                        daAchat = New SqlDataAdapter(cmdAchat)
                        daAchat.Fill(dsAchat, "ACHAT_SUPPRIME")
                        cbAchat = New SqlCommandBuilder(daAchat)

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000674", "Erreur d'excution de supprimerAchat", True, True, True)

                    End Try

                    Try
                        'ajout d'un nouvel enregistrement vide dans les datatables convenables
                        DataRowAchatSupprime = dsAchat.Tables("ACHAT_SUPPRIME").NewRow()

                        DataRowAchatSupprime("NumeroAchatSupprime") = NumeroAchatSupprime

                        DataRowAchatSupprime("NumeroAchat") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("NumeroAchat")
                        DataRowAchatSupprime("Date") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("Date")
                        DataRowAchatSupprime("TotalHT") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("TotalHT")
                        DataRowAchatSupprime("TVA") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("TVA")
                        DataRowAchatSupprime("TotalTTC") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("TotalTTC")
                        DataRowAchatSupprime("TotalRemise") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("TotalRemise")
                        DataRowAchatSupprime("Timbre") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("Timbre")
                        DataRowAchatSupprime("CodeFournisseur") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("CodeFournisseur")
                        DataRowAchatSupprime("CodePersonnel") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("CodePersonnel")
                        DataRowAchatSupprime("NumeroBL/Facture") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("NumeroBL/Facture")
                        DataRowAchatSupprime("LibellePoste") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("LibellePoste")
                        DataRowAchatSupprime("DateBlFacture") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("DateBlFacture")
                        DataRowAchatSupprime("ValeurVenteTTC") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("ValeurVenteTTC")

                        DataRowAchatSupprime("Autre") = dsAchat.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("Autre")

                        DataRowAchatSupprime("DateSuppression") = System.DateTime.Now
                        DataRowAchatSupprime("CodePersonnelSupprime") = CodeOperateur
                        DataRowAchatSupprime("NomPersonnelSupprime") = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", CodeOperateur)

                        dsAchat.Tables("ACHAT_SUPPRIME").Rows.Add(DataRowAchatSupprime)


                        daAchat.Update(dsAchat, "ACHAT_SUPPRIME")

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000675", "Erreur d'excution de supprimerAchat", True, True, True)

                        dsAchat.Reset()

                    End Try

                    Try

                        'chargement des Details de la vente 
                        StrSQL = "SELECT * FROM ACHAT_DETAILS WHERE NumeroAchat='" + NumeroAchat + "'"
                        cmdAchat.Connection = ConnectionServeur
                        cmdAchat.CommandText = StrSQL
                        daAchat = New SqlDataAdapter(cmdAchat)
                        daAchat.Fill(dsAchat, "ACHAT_DETAILS_A_SUPPRIME")

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000676", "Erreur d'excution de supprimerAchat", True, True, True)

                    End Try

                    Try

                        'chargement des Details de la vente dans une datatable qui correspond a la tabel VENTE_SUPPRIME_DETAILS
                        StrSQL = "SELECT * FROM ACHAT_SUPPRIME_DETAILS "
                        cmdAchat.Connection = ConnectionServeur
                        cmdAchat.CommandText = StrSQL
                        daAchat = New SqlDataAdapter(cmdAchat)
                        daAchat.Fill(dsAchat, "ACHAT_SUPPRIME_DETAILS")
                        cbAchat = New SqlCommandBuilder(daAchat)

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000677", "Erreur d'excution de supprimerAchat", True, True, True)

                    End Try

                    Try

                        For i = 0 To dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows.Count - 1

                            DataRowAchatSupprime = dsAchat.Tables("ACHAT_SUPPRIME_DETAILS").NewRow()

                            DataRowAchatSupprime("NumeroAchatSupprime") = NumeroAchatSupprime

                            DataRowAchatSupprime("NumeroAchat") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("NumeroAchat")
                            DataRowAchatSupprime("CodeArticle") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("CodeArticle")
                            DataRowAchatSupprime("CodeABarre") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("CodeABarre")
                            DataRowAchatSupprime("NumeroLotArticle") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("NumeroLotArticle")
                            DataRowAchatSupprime("Designation") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("Designation")

                            DataRowAchatSupprime("CodeForme") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("CodeForme")
                            DataRowAchatSupprime("Qte") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("Qte")
                            DataRowAchatSupprime("Stock") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("Stock")

                            DataRowAchatSupprime("PrixAchatHT") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("PrixAchatHT")
                            DataRowAchatSupprime("TotalAchatHT") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("TotalAchatHT")
                            DataRowAchatSupprime("PrixVenteTTC") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("PrixVenteTTC")

                            DataRowAchatSupprime("Remise") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("Remise")
                            DataRowAchatSupprime("TVA") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("TVA")
                            DataRowAchatSupprime("DatePeremptionSaisie") = dsAchat.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("DatePeremptionSaisie")

                            dsAchat.Tables("ACHAT_SUPPRIME_DETAILS").Rows.Add(DataRowAchatSupprime)

                        Next


                        daAchat.Update(dsAchat, "ACHAT_SUPPRIME_DETAILS")
                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000678", "Erreur d'excution de supprimerAchat", True, True, True)

                        dsAchat.Reset()

                    End Try

                    '--------------- suppression des affectation des commandes qui existe
                    Try
                        StrSQL = "Update COMMANDE SET NumeroFacture=NULL WHERE NumeroFacture ='" + lNumeroAchat.Text + "'"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL


                        cmd.ExecuteNonQuery()
                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000679", "Erreur d'excution de supprimerAchat", True, True, True)

                    End Try

                    '--------------- suppression des details de la achats
                    NumeroAchat = lNumeroAchat.Text

                    Try
                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = "DELETE FROM ACHAT_DETAILS WHERE NumeroAchat ='" + NumeroAchat + "'"
                        cmd.ExecuteNonQuery()

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000680", "Erreur d'excution de supprimerAchat", True, True, True)

                    End Try
                    '--------------- suppression de la achats
                    Try
                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = "DELETE FROM ACHAT WHERE NumeroAchat ='" + NumeroAchat + "'"
                        cmd.ExecuteNonQuery()

                        'Init()

                        'Ma nouvelle position
                        If NumeroligneAchat > 1 Then
                            NumeroligneAchat = NumeroligneAchat - 1
                        End If

                        ChargerAchat(NumeroligneAchat)

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000681", "Erreur d'excution de supprimerAchat", True, True, True)

                    End Try

                    MsgBox("Achat supprimé !", MsgBoxStyle.Information, "Information")

                End If

            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "tNetAPayer_KeyUp", ex.Message, "0000661", "Erreur d'excution de tNetAPayer_KeyUp", True, True, True)
            Return

        End Try

    End Sub
    Public Function RecupereNumeroAchatSupprime()

        'Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        Try

            StrSQL = " SELECT max([NumeroAchatSupprime]) FROM [ACHAT_SUPPRIME] WHERE SUBSTRING (NumeroAchatSupprime,0,5)=YEAR(getdate())"
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQL

            ValeurActuel = cmdRecupereNum.ExecuteScalar().ToString


            '----------------------- récupération du dernier numero sequenciel de l année en cours ou initialisation d un nouveau compteur  ----
            If ValeurActuel.Length > 4 Then
                If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                    Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                    Numero = Numero + 1
                    numeroConvertit = Numero.ToString
                    For i = 0 To 5 - numeroConvertit.Length
                        numeroConvertit = "0" + numeroConvertit
                    Next
                    ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
                Else
                    ValeurRetour = Year(Today).ToString + "/" + "000001"
                End If
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If

            Return ValeurRetour

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "supprimerAchat", ex.Message, "0000682", "Erreur d'excution de supprimerAchat", True, True, True)
            Return ValeurRetour

        End Try

    End Function

    Private Sub SupprimerTransactionArticlesPourModification()

        Dim cmd As New SqlCommand
        Dim NumeroAchat As String = ""
        Dim i As Integer = 0
        'Dim StrSQL As String = ""
        Dim NbreDesLots As Integer = 0

        Dim QteRestante As Integer = 0

        Try

            '--------------- suppression des quantités des lots introduits a partir de cet achat
            For i = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1

                StrSQL = "SELECT QteLotArticle FROM [LOT_ARTICLE] " + _
                         "WHERE NumeroLotArticle='" + _
                         dsAchat.Tables("ACHAT_DETAILS").Rows(i).Item("NumeroLotArticle") + _
                         "' AND CodeArticle='" + _
                         dsAchat.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + "'"

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try

                    If Not IsDBNull(cmd.ExecuteScalar()) Then
                        QteRestante = cmd.ExecuteScalar()
                    End If

                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "SupprimerTransactionArticlesPourModification", ex.Message, "0000683", "Erreur d'excution de SupprimerTransactionArticlesPourModification", True, True, True)

                End Try

                If QteRestante < dsAchat.Tables("ACHAT_DETAILS").Rows(i).Item("Qte") Then  'And dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("NumeroLotArticle") <> ""
                    '----------- s il y a des ventes qui ont diminuer du stock intoduit par cet achat 

                    StrSQL = "Update LOT_ARTICLE SET QteLotArticle=0" + _
                             " WHERE NumeroLotArticle ='" + _
                             dsAchat.Tables("ACHAT_DETAILS").Rows(i).Item("NumeroLotArticle") + "'" + _
                             " AND CodeArticle ='" + _
                             dsAchat.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + "'"

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try

                        cmd.ExecuteNonQuery()

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "SupprimerTransactionArticlesPourModification", ex.Message, "0000684", "Erreur d'excution de SupprimerTransactionArticlesPourModification", True, True, True)

                    End Try

                    '------------ on cherche si on a un lot qui n'admet pas de date de peremption 
                    StrSQL = "SELECT COUNT(NumeroLotArticle) FROM [LOT_ARTICLE] " + _
                             "WHERE CodeArticle='" + _
                             dsAchat.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + _
                             "' AND DatePeremptionArticle IS NULL"

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try

                        If Not IsDBNull(cmd.ExecuteScalar()) Then
                            NbreDesLots = cmd.ExecuteScalar()
                        End If

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "SupprimerTransactionArticlesPourModification", ex.Message, "0000685", "Erreur d'excution de SupprimerTransactionArticlesPourModification", True, True, True)

                    End Try

                    If NbreDesLots > 0 Then

                        Try
                            StrSQL = "Update LOT_ARTICLE SET QteLotArticle=QteLotArticle-" + _
                            (dsAchat.Tables("ACHAT_DETAILS").Rows(i).Item("Qte") - QteRestante).ToString + _
                            " WHERE NumeroLotArticle='' AND CodeArticle ='" + _
                            dsAchat.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + "'"

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL


                            cmd.ExecuteNonQuery()

                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Achat", "fAchat", "SupprimerTransactionArticlesPourModification", ex.Message, "0000686", "Erreur d'excution de SupprimerTransactionArticlesPourModification", True, True, True)

                        End Try

                    Else

                        Try
                            StrSQL = "INSERT INTO LOT_ARTICLE " + _
                                     "(""NumeroLotArticle"",""CodeArticle"",""QteLotArticle"",""DatePeremptionArticle"") " + _
                                     " VALUES(''" + _
                                     ",'" + dsAchat.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + _
                                     "',-" + (dsAchat.Tables("ACHAT_DETAILS").Rows(i).Item("Qte") - QteRestante).ToString + _
                                     ",NULL)"

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL

                            cmd.ExecuteNonQuery()

                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Achat", "fAchat", "SupprimerTransactionArticlesPourModification", ex.Message, "0000687", "Erreur d'excution de SupprimerTransactionArticlesPourModification", True, True, True)

                        End Try

                    End If

                Else

                    Try

                        StrSQL = "Update LOT_ARTICLE SET QteLotArticle=QteLotArticle-" + _
                                 dsAchat.Tables("ACHAT_DETAILS").Rows(i).Item("Qte").ToString + _
                                 " WHERE NumeroLotArticle ='" + _
                                 dsAchat.Tables("ACHAT_DETAILS").Rows(i).Item("NumeroLotArticle") + "'" + _
                                 " AND CodeArticle ='" + _
                                 dsAchat.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + "'"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL

                        cmd.ExecuteNonQuery()

                    Catch ex As Exception

                        'Gérer l'Exception
                        fMessageException.Show("Achat", "fAchat", "SupprimerTransactionArticlesPourModification", ex.Message, "0000688", "Erreur d'excution de SupprimerTransactionArticlesPourModification", True, True, True)

                    End Try

                End If

            Next

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "SupprimerTransactionArticlesPourModification", ex.Message, "0000689", "Erreur d'excution de SupprimerTransactionArticlesPourModification", True, True, True)
            Return

        End Try

    End Sub

    Private Sub tRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRecherche.KeyUp

        Try

            If e.KeyCode = Keys.Enter And tRecherche.Text <> "" Then

                'Recuprére le Row de l'element sellectioné

                'Lors du press Enter, on va appler la procedure rechercheAchat
                rechercheAchat(tRecherche.Text)

            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "tRecherche_KeyUp", ex.Message, "0000690", "Erreur d'excution de tRecherche_KeyUp", True, True, True)
            Return

        End Try

    End Sub

    Private Sub tRecherche_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tRecherche.LostFocus
        tRecherche.Visible = False
    End Sub

    Private Sub lValeurVenteTTC_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lValeurVenteTTC.TextChanged

        Try

            lValeurVenteTTC.Text = lValeurVenteTTC.Text

            If lValeurVenteTTC.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lValeurVenteTTC.Text, ".")
                If lValeurVenteTTC.Text.Length - x = 1 Then
                    lValeurVenteTTC.Text = lValeurVenteTTC.Text + ("00")
                ElseIf lValeurVenteTTC.Text.Length - x = 2 Then
                    lValeurVenteTTC.Text = lValeurVenteTTC.Text + ("0")
                End If
            Else
                lValeurVenteTTC.Text = lValeurVenteTTC.Text + ".000"
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "lValeurVenteTTC_TextChanged", ex.Message, "0000691", "Erreur d'excution de lValeurVenteTTC_TextChanged", True, True, True)
            Return

        End Try

    End Sub

    Private Sub lTotalTTC_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotalTTC.TextChanged

        Try

            lTotalTTC.Text = lTotalTTC.Text
            If lTotalTTC.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lTotalTTC.Text, ".")
                If lTotalTTC.Text.Length - x = 1 Then
                    lTotalTTC.Text = lTotalTTC.Text + ("00")
                ElseIf lTotalTTC.Text.Length - x = 2 Then
                    lTotalTTC.Text = lTotalTTC.Text + ("0")
                End If
            Else
                lTotalTTC.Text = lTotalTTC.Text + ".000"
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "lTotalTTC_TextChanged", ex.Message, "0000692", "Erreur d'excution de lTotalTTC_TextChanged", True, True, True)
            Return

        End Try

    End Sub

    Private Sub lTotHT_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotHT.TextChanged

        Try

            lTotHT.Text = lTotHT.Text
            If lTotHT.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lTotHT.Text, ".")
                If lTotHT.Text.Length - x = 1 Then
                    lTotHT.Text = lTotHT.Text + ("00")
                ElseIf lTotHT.Text.Length - x = 2 Then
                    lTotHT.Text = lTotHT.Text + ("0")
                End If
            Else
                lTotHT.Text = lTotHT.Text + ".000"
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "lTotHT_TextChanged", ex.Message, "0000693", "Erreur d'excution de lTotHT_TextChanged", True, True, True)
            Return

        End Try

    End Sub

    Private Sub lTotalTVA_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotalTVA.TextChanged

        Try

            lTotalTVA.Text = lTotalTVA.Text
            If lTotalTVA.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lTotalTVA.Text, ".")
                If lTotalTVA.Text.Length - x = 1 Then
                    lTotalTVA.Text = lTotalTVA.Text + ("00")
                ElseIf lTotalTVA.Text.Length - x = 2 Then
                    lTotalTVA.Text = lTotalTVA.Text + ("0")
                End If
            Else
                lTotalTVA.Text = lTotalTVA.Text + ".000"
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "lTotalTVA_TextChanged", ex.Message, "0000694", "Erreur d'excution de lTotalTVA_TextChanged", True, True, True)
            Return

        End Try

    End Sub

    Private Sub tAutre_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tAutre.KeyUp

        Dim Position As Integer = 0

        Try
            If e.KeyCode = Keys.Decimal Then
                Position = InStr(tAutre.Text, ".")
                tAutre.Text = tAutre.Text.Substring(0, Position)
                tAutre.Select(tAutre.Text.Length, 0)
                Exit Sub
            End If

            If tAutre.Text.Contains(".") Then
                Position = InStr(tAutre.Text, ".")
                If tAutre.Text.Length - Position > 3 Then
                    tAutre.Text = tAutre.Text.Substring(0, Position + 3)
                    tAutre.Select(tAutre.Text.Length, 0)
                End If
            End If

            If e.KeyCode = Keys.Enter And IsNumeric(tAutre.Text) = True Then
                lTotalTTC.Text = (CDbl(lTotalTTC.Text) + CDbl(tAutre.Text)).ToString
                CalculerMontants()
                gArticles.Focus()
                'gArticles.Col = 1
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeArticle"))
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "tAutre_KeyUp", ex.Message, "0000695", "Erreur d'excution de tAutre_KeyUp", True, True, True)
            Return

        End Try

    End Sub

    Private Sub tAutre_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tAutre.LostFocus

        Try

            tAutre.Value = tAutre.Text
            If tAutre.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(tAutre.Text, ".")
                If tAutre.Text.Length - x = 1 Then
                    tAutre.Text = tAutre.Text + ("00")
                ElseIf tAutre.Text.Length - x = 2 Then
                    tAutre.Text = tAutre.Text + ("0")
                End If
            Else
                tAutre.Text = tAutre.Text + ".000"
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "tAutre_LostFocus", ex.Message, "0000696", "Erreur d'excution de tAutre_LostFocus", True, True, True)
            Return

        End Try

    End Sub


    Private Sub lRemise_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lRemise.TextChanged

        Try

            lRemise.Text = lRemise.Text
            If lRemise.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lRemise.Text, ".")
                If lRemise.Text.Length - x = 1 Then
                    lRemise.Text = lRemise.Text + ("00")
                ElseIf lRemise.Text.Length - x = 2 Then
                    lRemise.Text = lRemise.Text + ("0")
                End If
            Else
                lRemise.Text = lRemise.Text + ".000"
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "lRemise_TextChanged", ex.Message, "0000697", "Erreur d'excution de lRemise_TextChanged", True, True, True)
            Return

        End Try

    End Sub

    Private Sub bInstance_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bInstance.Click

        Dim cmd As New SqlCommand
        Dim NumeroAchatInstance As String = ""
        Dim StrMajLOT As String = ""
        Dim StrMajReglement As String = ""
        Dim I As Integer = 0
        Dim NumeroLot As String = "RIEN"
        Dim NouveauNumeroLot As String = ""
        Dim Ordre As Integer = 1

        'Dim StrSQL As String = ""

        '**************************************************************************************************
        '********* Test du cas ou la liste est vide : chargement des anciens ventes en instance ***********
        '**************************************************************************************************

        Try

            If dsAchat.Tables("ACHAT_DETAILS") Is Nothing Then
                Exit Sub
            End If

            If dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle") = "" Then

                Dim MyAchatInstance As New fListeDesAchatsEnInstance

                MyAchatInstance.ShowDialog()
                NumeroAchatInstance = fListeDesAchatsEnInstance.NumeroAchat
                ComfirmerMettreEnINstance = fListeDesAchatsEnInstance.ComfirmerMettreEnINstance

                MyAchatInstance.Dispose()
                MyAchatInstance.Close()

                If ComfirmerMettreEnINstance = False Then
                    'gArticles.Col = 2
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
                    gArticles.EditActive = True
                    Exit Sub
                End If

                '----------------------- chargement des datatables 

                '------------------- chargement de l'Entêtes de la commandes  
                Try
                    dsAchat.Tables("ACHAT_INSTANCE").Clear()
                Catch ex As Exception

                    'Gérer l'Exception
                    'fMessageException.Show("Achat", "fAchat", "bInstance_Click", ex.Message, "0000699", "Erreur d'excution de bInstance_Click", True, True, True)

                End Try
                Try
                    dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Clear()
                Catch ex As Exception

                    'Gérer l'Exception
                    'fMessageException.Show("Achat", "fAchat", "bInstance_Click", ex.Message, "0000700", "Erreur d'excution de bInstance_Click", True, True, True)

                End Try

                Try

                    StrSQL = "SELECT * FROM ACHAT_INSTANCE WHERE NumeroAchatInstance ='" + NumeroAchatInstance + "'"
                    cmdAchat.Connection = ConnectionServeur
                    cmdAchat.CommandText = StrSQL
                    daAchat = New SqlDataAdapter(cmdAchat)
                    daAchat.Fill(dsAchat, "ACHAT_INSTANCE")



                    If dsAchat.Tables("ACHAT_INSTANCE").Rows.Count > 0 Then

                        lDateAchat.Text = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("Date")
                        TotalTTCAchat = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("TotalTTC")

                        TotalHTNETAchat = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("TotalHT")
                        lTotHT.Text = TotalHTNETAchat.ToString

                        TVA = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("TotalTVA")
                        lTotalTVA.Text = TVA

                        lTotalTTC.Text = Math.Round(dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("TotalTTC"), 3)
                        lValeurVenteTTC.Text = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("ValeurVenteTTC")

                        lRemise.Text = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("TotalRemise")
                        tAutre.Value = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("Autre")

                        lHTNet.Text = (CDbl(lTotHT.Text) - CDbl(lRemise.Text)).ToString
                        lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("CodeOperateur"))

                        tNumeroBlFact.Text = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("NumeroBL/Facture")
                        dDateBlFacture.Text = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("DateBlFacture")

                        cmbFournisseur.SelectedValue = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("CodeFournisseur")
                    End If

                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "bInstance_Click", ex.Message, "0000701", "Erreur d'excution de bInstance_Click", True, True, True)

                End Try
                '----------------------chargement des détails des Commandes 

                Try
                    StrSQL = "SELECT NumeroAchatInstance, " + _
                             "CodeArticle, " + _
                             "CodeABarre, " + _
                             "Designation, " + _
                             "LibelleForme, " + _
                             "Qte, " + _
                             "convert(varchar,DatePeremption, 103) as DatePeremptionSaisie, " + _
                             "Stock, " + _
                             "Remise, " + _
                             "PrixAchatHT, " + _
                             "PrixVenteTTC, " + _
                             "TotalAchatHT, " + _
                             "TVA, " + _
                             "ACHAT_INSTANCE_DETAILS.CodeForme, " + _
                             "DatePeremption " + _
                             " FROM ACHAT_INSTANCE_DETAILS,FORME_ARTICLE " + _
                             " WHERE ACHAT_INSTANCE_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
                             " AND NumeroAchatInstance =" + _
                             Quote(NumeroAchatInstance) + " ORDER BY Ordre"

                    cmdAchat.Connection = ConnectionServeur
                    cmdAchat.CommandText = StrSQL
                    daAchat = New SqlDataAdapter(cmdAchat)
                    daAchat.Fill(dsAchat, "ACHAT_INSTANCE_DETAILS")

                    'Dim DatePeremption As Date

                    For I = 0 To dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows.Count - 1
                        If dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("CodeArticle") <> "" Then
                            dr = dsAchat.Tables("ACHAT_DETAILS").NewRow

                            dr.Item("CodeArticle") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("CodeArticle")
                            dr.Item("CodeABarre") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("CodeABarre")
                            dr.Item("Designation") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("Designation")
                            dr.Item("LibelleForme") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("LibelleForme")
                            'dr.Item("CodeForme") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("CodeForme")
                            dr.Item("Qte") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("Qte")

                            dr.Item("DatePeremptionSaisie") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("DatePeremptionSaisie")
                            dr.Item("Stock") = CalculeStock(dr.Item("CodeArticle"))
                            dr.Item("Remise") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("Remise")
                            dr.Item("PrixAchatHT") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("PrixAchatHT")
                            dr.Item("PrixVenteTTC") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("PrixVenteTTC")
                            dr.Item("TotalAchatHT") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("TotalAchatHT")
                            dr.Item("TVA") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("TVA")

                            ''---------------------- ajout des informations manquantes dans l'instance 

                            ''----------------------- récupération de la date de péremption

                            'StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
                            'System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
                            'dr.Item("CodeArticle") + "' Order by DatePeremptionArticle ASC "
                            'cmd.Connection = ConnectionServeur
                            'cmd.CommandText = StrSQL
                            'Try
                            '    DatePeremption = cmd.ExecuteScalar()
                            'Catch ex As Exception
                            '    Console.WriteLine(ex.Message)
                            'End Try
                            'If DatePeremption = #12:00:00 AM# Then
                            'Else
                            '    dr.Item("DatePeremption") = DatePeremption
                            'End If
                            '----------------------- calcul du stock

                            dsAchat.Tables("ACHAT_DETAILS").Rows.Add(dr)
                        End If

                    Next

                    I = 0
                    '---------------------- Suppression des ligne vides 
                    Do While I < dsAchat.Tables("ACHAT_DETAILS").Rows.Count
                        If dsAchat.Tables("ACHAT_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then
                            If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                                dsAchat.Tables("ACHAT_DETAILS").Rows(I).Delete()
                            End If
                        End If
                        I = I + 1
                    Loop

                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "bInstance_Click", ex.Message, "0000702", "Erreur d'excution de bInstance_Click", True, True, True)

                End Try

                '---------------------- Suppression de l'instance 

                'details

                Try
                    StrSQL = "DELETE FROM ACHAT_INSTANCE_DETAILS WHERE NumeroAchatInstance='" + NumeroAchatInstance + "'"
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    cmd.ExecuteNonQuery()
                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "bInstance_Click", ex.Message, "0000703", "Erreur d'excution de bInstance_Click", True, True, True)

                End Try

                'entête
                Try

                    StrSQL = "DELETE FROM ACHAT_INSTANCE WHERE NumeroAchatInstance='" + NumeroAchatInstance + "'"
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    cmd.ExecuteNonQuery()
                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Achat", "fAchat", "bInstance_Click", ex.Message, "0000704", "Erreur d'excution de bInstance_Click", True, True, True)

                End Try

                'gArticles.Col = 2
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
                gArticles.MoveLast()
                gArticles.EditActive = True
                CalculerMontants()
                Exit Sub

            End If

            If cmbFournisseur.Text = "" Then
                MsgBox("Veuillez choisir un Fournisseur !", MsgBoxStyle.Critical, "Erreur")
                cmbFournisseur.Focus()
                Exit Sub
            End If

            '**************************************************************************************************
            '***************** Enregistrement d'une nouvelle vente en instance  *******************************
            '**************************************************************************************************

            '--------------------------- pour verifier le calcul : si l'utilisateur ne clique pas entree
            '--------------------------- sur la cellule qte du dernier ligne la somme TTC sera fausse
            CalculerMontants()
            '---------------------------------- récupération du dernier numero de vente --------------------
            NumeroAchatInstance = RecupereNumeroInstance()
            '------------------------------ enregistrement de l'entête de la vente -------------------------
            '-----------------------------------------------------------------------------------------------
            '-----------------------------------------------------------------------------------------------
            Try
                If dsAchat.Tables("ACHAT_INSTANCE") IsNot Nothing Then
                    dsAchat.Tables("ACHAT_INSTANCE").Clear()
                End If
            Catch ex As Exception

                'Gérer l'Exception
                fMessageException.Show("Achat", "fAchat", "bInstance_Click", ex.Message, "0000705", "Erreur d'excution de bInstance_Click", True, True, True)

            End Try

            Try
                If dsAchat.Tables("ACHAT_INSTANCE_DETAILS") IsNot Nothing Then
                    dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Clear()
                End If
            Catch ex As Exception

                'Gérer l'Exception
                fMessageException.Show("Achat", "fAchat", "bInstance_Click", ex.Message, "0000706", "Erreur d'excution de bInstance_Click", True, True, True)

            End Try


            For I = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
                If dsAchat.Tables("ACHAT_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then
                    If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") <> "" Then
                        For j = I + 1 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
                            If dsAchat.Tables("ACHAT_DETAILS").Rows(j).RowState <> DataRowState.Deleted Then
                                If dsAchat.Tables("ACHAT_DETAILS").Rows(j).Item("CodeArticle") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") Then
                                    MsgBox("L'article " + dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Designation") + " apparaît plus qu'une fois. Impossible de mettre cet achat en frigo", MsgBoxStyle.Critical)
                                    Exit Sub
                                End If
                            End If

                        Next

                    End If
                End If
            Next




            Dim myNomInstance As New fNomInstance
            fNomInstance.NomTable = "ACHAT_INSTANCE"
            myNomInstance.ShowDialog()

            ConfirmerInstance = fNomInstance.Confirmer
            NomInstance = fNomInstance.NomInstance
            CodeOperateurInstance = fNomInstance.CodeOperateur

            myNomInstance.Dispose()
            myNomInstance.Close()

            If ConfirmerInstance = False Then
                Exit Sub
            End If


            Try

                StrSQL = "SELECT * FROM ACHAT_INSTANCE ORDER BY NumeroAchatInstance ASC"
                cmdAchat.Connection = ConnectionServeur
                cmdAchat.CommandText = StrSQL
                daAchat = New SqlDataAdapter(cmdAchat)
                daAchat.Fill(dsAchat, "ACHAT_INSTANCE")
                cbAchat = New SqlCommandBuilder(daAchat)
                dr = dsAchat.Tables("ACHAT_INSTANCE").NewRow()

                With dsAchat
                    dr.Item("NumeroAchatInstance") = NumeroAchatInstance
                    dr.Item("Date") = System.DateTime.Now

                    dr.Item("TotalHT") = TotalHTNETAchat
                    dr.Item("TotalTTC") = TotalTTCAchat
                    dr.Item("TotalTVA") = TVA
                    dr.Item("Timbre") = 0.0 '0.3


                    dr.Item("NumeroBL/Facture") = tNumeroBlFact.Text
                    dr.Item("TotalRemise") = TotalRemiseAchat

                    dr.Item("LibellePoste") = System.Environment.GetEnvironmentVariable("Poste")
                    dr.Item("CodeOperateur") = CodeOperateurInstance
                    dr.Item("DateBlFacture") = dDateBlFacture.Text
                    dr.Item("CodeFournisseur") = cmbFournisseur.SelectedValue
                    dr.Item("Note") = "rien"
                    dr.Item("ValeurVenteTTC") = lValeurVenteTTC.Text
                    dr.Item("NomAchatInstance") = NomInstance

                    dsAchat.Tables("ACHAT_INSTANCE").Rows.Add(dr)
                End With

                '***************************************** enregistrement de l'entête de l'achat *************

                daAchat.Update(dsAchat, "ACHAT_INSTANCE")
            Catch ex As Exception

                'Gérer l'Exception
                fMessageException.Show("Achat", "fAchat", "bInstance_Click", ex.Message, "0000707", "Erreur d'excution de bInstance_Click", True, True, True)

                dsAchat.Reset()
                Exit Sub
            End Try

            '-------------------- préparation des détails de l'achat pour enregistrement ------------------
            '-----------------------------------------------------------------------------------------------
            '-----------------------------------------------------------------------------------------------
            Try

                StrSQL = "SELECT TOP (0) * FROM ACHAT_INSTANCE_DETAILS ORDER BY NumeroAchatInstance ASC"
                cmdAchat.Connection = ConnectionServeur
                cmdAchat.CommandText = StrSQL
                daAchat = New SqlDataAdapter(cmdAchat)
                daAchat.Fill(dsAchat, "ACHAT_INSTANCE_DETAILS")
                cbAchat = New SqlCommandBuilder(daAchat)

                For I = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
                    If dsAchat.Tables("ACHAT_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then
                        If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") <> "" Then

                            dr = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").NewRow()

                            dr.Item("NumeroAchatInstance") = NumeroAchatInstance
                            dr.Item("CodeArticle") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle")
                            dr.Item("CodeABarre") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeABarre")
                            dr.Item("Designation") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Designation")
                            'dr.Item("CodeForme") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeForme")
                            dr.Item("Qte") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Qte")
                            dr.Item("Stock") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Stock")
                            dr.Item("PrixAchatHT") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("PrixAchatHT")
                            dr.Item("TotalAchatHT") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("TotalAchatHT")
                            dr.Item("PrixVenteTTC") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("PrixVenteTTC")
                            dr.Item("Remise") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Remise")
                            dr.Item("TVA") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("TVA")
                            If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremptionSaisie").ToString <> "" Then
                                dr.Item("DatePeremption") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremptionSaisie")
                            End If

                            dr.Item("Ordre") = Ordre
                            Ordre += 1

                            dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows.Add(dr)

                        End If
                    End If

                Next

                '***************************************** enregistrement des détails de l'achat *************

                daAchat.Update(dsAchat, "ACHAT_INSTANCE_DETAILS")
                'Init()

            Catch ex As Exception

                'Gérer l'Exception
                fMessageException.Show("Achat", "fAchat", "bInstance_Click", ex.Message, "0000708", "Erreur d'excution de bInstance_Click", True, True, True)

                dsAchat.Reset()
                CalculerMontants()
                Exit Sub
            End Try

            If Mode = "Ajout" Then
                'Recuperer le dernier Achat
                NumeroligneAchat = selectionDernierLigneAchat()
            End If

            'Changer Le MODE en consultation
            Mode = "Consultation"

            'Refreche de dArticle par le dernier Achat
            ChargerAchat(NumeroligneAchat)



            '''''''''''
            gArticles.EditActive = False
            '''''''''''

            'Init
            bTerminal.Enabled = False
            bAnnuler.Enabled = False
            bConfirmer.Enabled = False

            bRemise.Enabled = False
            bRecherche.Enabled = True

            bFirst.Visible = True
            bPrevious.Visible = True
            bNext.Visible = True
            bLast.Visible = True
            bAjouter.Enabled = True
            bModifier.Visible = True

            GroupeFournisseur.Enabled = False

            'pour refresh le champ de frigo
            initNombreAchatInstance()
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "bInstance_Click", ex.Message, "0000698", "Erreur d'excution de bInstance_Click", True, True, True)
            Return

        End Try

    End Sub
    Public Function RecupereNumeroInstance()
        'Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        Try

            StrSQL = " SELECT max(NumeroAchatInstance) FROM ACHAT_INSTANCE"
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQL

            ValeurActuel = cmdRecupereNum.ExecuteScalar().ToString

            '----------------------- récupération du dernier numero séquenciel de l année en cours ou initialisation d un nouveau compteur  ----
            If ValeurActuel.Length > 4 Then
                If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                    Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                    Numero = Numero + 1
                    numeroConvertit = Numero.ToString
                    For i = 0 To 5 - numeroConvertit.Length
                        numeroConvertit = "0" + numeroConvertit
                    Next
                    ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
                Else
                    ValeurRetour = Year(Today).ToString + "/" + "000001"
                End If
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If

            Return ValeurRetour

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "RecupereNumeroInstance", ex.Message, "0000709", "Erreur d'excution de RecupereNumeroInstance", True, True, True)

            Return ValeurRetour

        End Try

    End Function

    Private Sub bCommande_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCommande.Click

        'Suivi du scénario 
        fMessageException.Show("Achat", "fAchat", "bCommande_Click", "NoException", "NoError", "Clic sur le bouton Commande", False, True, False)

        Dim I As Integer = 0
        Dim J As Integer = 0
        Dim k As Integer = 0
        'Dim StrSQL As String = ""
        Dim NumeroAchat As String = ""
        Dim cmd As New SqlCommand
        Dim NouveauNumeroLot As String = ""

        Try
            For k = 0 To TableauCommande.Length - 1
                TableauCommande(k) = ""
            Next

            Dim MyCommandeListe As New fListeDesCommandePourAchat
            If cmbFournisseur.Text <> "" Then
                MyCommandeListe.CodeFournisseur = cmbFournisseur.SelectedValue
            End If



            'fListeDesCommandePourAchat.NumeroCommandeExiste = ""



            MyCommandeListe.ShowDialog()

            If fListeDesCommandePourAchat.ComfirmerChargementDesCommandes = False Then
                Exit Sub
            End If

            NumeroAchat = RecupereNumero()

            For I = 0 To fListeDesCommandePourAchat.dsCommandeEnAchat.Tables("COMMANDE").Rows.Count - 1
                If fListeDesCommandePourAchat.dsCommandeEnAchat.Tables("COMMANDE").Rows(I).Item("Cocher") = True Then
                    '------------------ pour savoir si l'achat est depuis des commandes ou nn
                    DepuisCommande = True
                    For k = 0 To TableauCommande.Length - 1
                        If TableauCommande(k) = "" Then  ' pour atteindre la fin de la liste des numero 
                            Exit For                     ' des commande enregistré dans le tableau
                        End If
                    Next
                    ' ajout de la commande dans l achat
                    TableauCommande(k) = fListeDesCommandePourAchat.dsCommandeEnAchat.Tables("COMMANDE").Rows(I).Item("NumeroCommande")

                    Try
                        dsAchat.Tables("COMMANDE_DETAILS").Clear()
                    Catch ex As Exception
                    End Try
                    ' ajout des détails de commande dans détails achat
                    StrSQL = "SELECT CodeArticle," + _
                             "CodeABarre," + _
                             "Designation," + _
                             "CodeForme," + _
                             "Qte," + _
                             "QteACommander," + _
                             "Stock," + _
                             "PrixAchatHT," + _
                             "TotalTTCAchat," + _
                             "TVA," + _
                             "convert(varchar, DatePeremption, 103) as DatePeremptionSaisie ," + _
                             "COMMANDE.CodeFournisseur " + _
                             " FROM COMMANDE_DETAILS,COMMANDE " + _
                             "WHERE COMMANDE_DETAILS.NumeroCommande=COMMANDE.NumeroCommande and  COMMANDE_DETAILS.NumeroCommande='" + _
                             fListeDesCommandePourAchat.dsCommandeEnAchat.Tables("COMMANDE").Rows(I).Item("NumeroCommande") + _
                             "' ORDER BY Designation"

                    cmdAchat.Connection = ConnectionServeur
                    cmdAchat.CommandText = StrSQL
                    daAchat = New SqlDataAdapter(cmdAchat)
                    daAchat.Fill(dsAchat, "COMMANDE_DETAILS")

                    For J = 0 To dsAchat.Tables("COMMANDE_DETAILS").Rows.Count - 1

                        NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
                        dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)


                        NouvelArticle("NumeroAchat") = NumeroAchat
                        NouvelArticle("CodeArticle") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("CodeArticle")
                        NouvelArticle("CodeABarre") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("CodeABarre")
                        NouvelArticle("NumeroLotArticle") = ""
                        NouvelArticle("Designation") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("Designation")
                        'NouvelArticle("CodeForme") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("CodeForme")
                        'NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", "FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
                        NouvelArticle("Qte") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("Qte")
                        NouvelArticle("QteCommander") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("Qte")
                        NouvelArticle("Stock") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("Stock")
                        NouvelArticle("PrixAchatHT") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("PrixAchatHT")
                        NouvelArticle("TotalAchatHT") = NouvelArticle("PrixAchatHT") * NouvelArticle("Qte")
                        NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle")) '* NouvelArticle("Qte")
                        NouvelArticle("Remise") = 0
                        NouvelArticle("TVA") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("TVA")
                        If AfficherLesDerniereDDPeremptionDansNouveauAchat = True Then
                            NouvelArticle("DatePeremptionSaisie") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("DatePeremptionSaisie")
                        End If

                        cmbFournisseur.SelectedValue = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("CodeFournisseur")
                    Next

                End If
            Next


            '------------------ pour savoir si l'achat est depuis des commandes ou nn
            'If dsAchat.Tables("COMMANDE_DETAILS").Rows.Count <> 0 Then

            'End If

            I = 0
            '---------------------- Suppression des ligne vides 
            Do While I < dsAchat.Tables("ACHAT_DETAILS").Rows.Count
                If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                    dsAchat.Tables("ACHAT_DETAILS").Rows(I).Delete()
                End If
                I = I + 1
            Loop

            '----------------------- ajout des numéros des lots pour ceux qui ont des dates de péremption sans numlot

            For p = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1

                If gArticles(p, "CodeArticle") <> "" And gArticles(p, "DatePeremptionSaisie").ToString <> "" Then
                    If dsAchat.Tables("ACHAT_DETAILS").Rows(p).RowState <> DataRowState.Deleted Then
                        If dsAchat.Tables("ACHAT_DETAILS").Rows(p).Item("NumeroLotArticle").ToString = "" Then

                            '------------------ recupération du dernier numéro de lot pour cet article puis incrémentation
                            StrSQL = " SELECT MAX(case when (isnumeric(NumeroLotArticle)=1) then NumeroLotArticle Else 0 END) FROM [LOT_ARTICLE] WHERE CodeArticle ='" + _
                                     dsAchat.Tables("ACHAT_DETAILS").Rows(p).Item("CodeArticle") + "'"
                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try
                                NouveauNumeroLot = cmd.ExecuteScalar().ToString
                                If NouveauNumeroLot <> "" Then
                                    NouveauNumeroLot = (Convert.ToInt32(NouveauNumeroLot) + 1).ToString
                                Else
                                    NouveauNumeroLot = 1
                                End If

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                            For q = p To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
                                If dsAchat.Tables("ACHAT_DETAILS").Rows(p).Item("CodeArticle") = gArticles(q, "CodeArticle") And gArticles(q, "NumeroLotArticle").ToString = "" Then
                                    ' ----------- affectation des numero pour ceux qui n'ont pas de numéro de lots (incrémentation)
                                    dsAchat.Tables("ACHAT_DETAILS").Rows(q).Item("NumeroLotArticle") = NouveauNumeroLot
                                    NouveauNumeroLot = NouveauNumeroLot + 1
                                End If

                            Next

                        End If
                    End If

                End If
            Next

            CalculerMontants()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "bCommande_Click", ex.Message, "0000710", "Erreur d'excution de bCommande_Click", True, True, True)

        End Try

    End Sub

    Private Sub bListe_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bListe.Click

        'Suivi du scénario 
        fMessageException.Show("Achat", "fAchat", "bListe_Click", "NoException", "NoError", "Clic sur le bouton Liste", False, True, False)

        Try

            Dim fInstanceListeDesAchats As New fListeDesAchats
            fInstanceListeDesAchats.ShowDialog()

            NumeroAchat = fListeDesAchats.NumeroAchat

            fInstanceListeDesAchats.Dispose()
            fInstanceListeDesAchats.Close()

            If NumeroAchat <> "" Then
                Dim j As Integer
                Dim DataRowRecherche As DataRow
                Dim NumeroLigne As Integer
                For j = 0 To dsAchat.Tables("ACHAT").Rows.Count - 1
                    DataRowRecherche = dsAchat.Tables("ACHAT").Rows(j)
                    If DataRowRecherche.Item("NumeroAchat") = NumeroAchat Then
                        NumeroLigne = j
                    End If
                Next

                If NumeroLigne <= dsAchat.Tables("ACHAT").Rows.Count - 1 Then
                    DataRowRecherche = dsAchat.Tables("ACHAT").Rows(NumeroLigne)
                    'chargement des informations entête
                    lNumeroAchat.Text = dsAchat.Tables("ACHAT").Rows(NumeroLigne)("NumeroAchat")
                    lDateAchat.Text = dsAchat.Tables("ACHAT").Rows(NumeroLigne)("Date")
                    cmbFournisseur.SelectedValue = dsAchat.Tables("ACHAT").Rows(NumeroLigne)("CodeFournisseur")

                    TotalTTCAchat = dsAchat.Tables("ACHAT").Rows(NumeroLigne)("TotalTTC")
                    TotalHTNETAchat = dsAchat.Tables("ACHAT").Rows(NumeroLigne)("TotalHT")
                    lTotHT.Text = TotalHTNETAchat.ToString

                    lTotalTVA.Text = dsAchat.Tables("ACHAT").Rows(NumeroLigne)("TVA")
                    lValeurVenteTTC.Text = dsAchat.Tables("ACHAT").Rows(NumeroLigne)("ValeurVenteTTC")

                    lRemise.Text = dsAchat.Tables("ACHAT").Rows(NumeroLigne)("TotalRemise")
                    tAutre.Value = dsAchat.Tables("ACHAT").Rows(NumeroLigne)("Autre")

                    lTotalTTC.Text = Math.Round(TotalTTCAchat, 3)

                    lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsAchat.Tables("ACHAT").Rows(NumeroLigne)("CodePersonnel"))
                    lHTNet.Text = (CDbl(lTotHT.Text) - CDbl(lRemise.Text)).ToString

                    'NumeroAchat = DataRowRecherche.Item("NumeroAchat")
                    'ChargerAchat(NumeroAchat)
                    rechercheAchat(NumeroAchat)
                    GroupeSituationFournisseur.Visible = False
                End If

            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "bListe_Click", ex.Message, "0000711", "Erreur d'excution de bListe_Click", True, True, True)
            Return

        End Try

    End Sub

    Private Sub tTotalHT_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tTotalHT.GotFocus
        Try

            tTotalHT.SelectAll()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "tTotalHT_GotFocus", ex.Message, "0000712", "Erreur d'excution de tTotalHT_GotFocus", True, True, True)
            Return

        End Try

    End Sub

    Private Sub tTotalHT_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTotalHT.KeyDown

        ' ''Try

        ' ''    If e.KeyCode = Keys.Enter And Confirmation = False Then
        ' ''        gArticles.Focus()
        ' ''        'gArticles.Col = 2
        ' ''        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
        ' ''        gArticles.EditActive = True
        ' ''    End If
        ' ''    Confirmation = False

        ' ''Catch ex As Exception

        ' ''    'Gérer l'Exception
        ' ''    fMessageException.Show("Achat", "fAchat", "tTotalHT_KeyDown", ex.Message, "0000713", "Erreur d'excution de tTotalHT_KeyDown", True, True, True)
        ' ''    Return

        ' ''End Try
        If e.KeyCode = Keys.Enter Then
            gArticles.Focus()
        End If

    End Sub

    Private Sub tTotalHT_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTotalHT.KeyUp

        Dim Position As Integer = 0

        Try

            If e.KeyCode = Keys.Decimal Then
                Position = InStr(tTotalHT.Text, ".")
                tTotalHT.Text = tTotalHT.Text.Substring(0, Position)
                tTotalHT.Select(tTotalHT.Text.Length, 0)
                Exit Sub
            End If

            If tTotalHT.Text.Contains(".") Then
                Position = InStr(tTotalHT.Text, ".")
                If tTotalHT.Text.Length - Position > 3 Then
                    tTotalHT.Text = tTotalHT.Text.Substring(0, Position + 3)
                    tTotalHT.Select(tTotalHT.Text.Length, 0)
                End If
            End If



        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "tTotalHT_KeyUp", ex.Message, "0000714", "Erreur d'excution de tTotalHT_KeyUp", True, True, True)
            Return

        End Try

    End Sub

    Private Sub tTotalHT_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tTotalHT.LostFocus

        Try

            tTotalHT.Value = tTotalHT.Text
            If tTotalHT.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(tTotalHT.Text, ".")
                If tTotalHT.Text.Length - x = 1 Then
                    tTotalHT.Text = tTotalHT.Text + ("00")
                ElseIf tTotalHT.Text.Length - x = 2 Then
                    tTotalHT.Text = tTotalHT.Text + ("0")
                End If
            Else
                tTotalHT.Text = tTotalHT.Text + ".000"
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "tTotalHT_LostFocus", ex.Message, "0000715", "Erreur d'excution de tTotalHT_LostFocus", True, True, True)
            Return

        End Try

    End Sub

    Private Sub tTotalHT_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles tTotalHT.MouseClick

        Try

            tTotalHT.SelectAll()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "tTotalHT_MouseClick", ex.Message, "0000716", "Erreur d'excution de tTotalHT_MouseClick", True, True, True)
            Return

        End Try

    End Sub

    Private Sub tTotalHT_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tTotalHT.TextChanged

        'Dim x As Integer = 0

        'x = InStr(tTotalHT.Text, ".")
        'If tTotalHT.Text.Length - x = 3 And x <> 0 Then
        '    bConfirmer.Focus()
        'End If

    End Sub

    Private Sub lHTNet_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lHTNet.TextChanged

        Try

            lHTNet.Text = lHTNet.Text
            If lHTNet.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lHTNet.Text, ".")
                If lHTNet.Text.Length - x = 1 Then
                    lHTNet.Text = lHTNet.Text + ("00")
                ElseIf lHTNet.Text.Length - x = 2 Then
                    lHTNet.Text = lHTNet.Text + ("0")
                End If
            Else
                lHTNet.Text = lHTNet.Text + ".000"
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "lHTNet_TextChanged", ex.Message, "0000717", "Erreur d'excution de lHTNet_TextChanged", True, True, True)
            Return

        End Try

    End Sub

    Private Sub tNumeroBlFact_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNumeroBlFact.KeyDown

        Try

            If e.KeyCode = Keys.Enter And Confirmation = False Then
                dDateBlFacture.Focus()
            End If
            Confirmation = False

        Catch ex As Exception

            'Gérer l'Exception
            'fMessageException.Show("Achat", "fAchat", "tNumeroBlFact_KeyDown", ex.Message, "0000718", "Erreur d'excution de tNumeroBlFact_KeyDown", True, True, True)
            'Return

        End Try

    End Sub

    Private Sub tNumeroBlFact_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNumeroBlFact.KeyUp
        ''If e.KeyCode = Keys.Enter And Confirmation = False Then
        ''    cmbDateBlFacture.Focus()
        ''End If
        ''Confirmation = False

        'Dim cmd As New SqlCommand
        ''Dim StrSQL As String = ""
        'Dim AchatAvecLeMemeNumeroBl As Integer = 0
        'Dim NumeroAchatAPartirDuNumeroBL As String = ""

        'Try

        '    If Mode = "Consultation" And tNumeroBlFact.Text <> "" And e.KeyCode = Keys.Enter Then
        '        StrSQL = " SELECT NumeroAchat FROM [ACHAT] " + _
        '            "WHERE rtrim(ltrim([NumeroBL/Facture])) ='" + tNumeroBlFact.Text + "'"

        '        cmd.Connection = ConnectionServeur
        '        cmd.CommandText = StrSQL

        '        Try
        '            NumeroAchatAPartirDuNumeroBL = cmd.ExecuteScalar()
        '        Catch ex As Exception
        '            Console.WriteLine(ex.Message)
        '        End Try

        '        If NumeroAchatAPartirDuNumeroBL <> "" Then
        '            tRecherche.Value = NumeroAchatAPartirDuNumeroBL
        '            tRecherche_KeyUp(sender, e)
        '        End If

        '        tNumeroBlFact.Enabled = True
        '        cmbFournisseur.Enabled = True
        '        dDateBlFacture.Enabled = True
        '        tTotalHT.Enabled = True
        '        GroupeFournisseur.Enabled = False

        '        Exit Sub
        '    End If

        '    '------------------------------ verification si le fournisseur admet le même numero du BL 
        '    StrSQL = " SELECT COUNT(NumeroAchat) FROM [ACHAT] " + _
        '             "WHERE CodeFournisseur ='" + cmbFournisseur.SelectedValue + _
        '             "' AND [NumeroBL/Facture]='" + tNumeroBlFact.Text + "'"

        '    cmd.Connection = ConnectionServeur
        '    cmd.CommandText = StrSQL

        '    Try
        '        AchatAvecLeMemeNumeroBl = cmd.ExecuteScalar()
        '    Catch ex As Exception
        '        Console.WriteLine(ex.Message)
        '    End Try

        '    If AchatAvecLeMemeNumeroBl > 0 And Mode = "Ajout" Then
        '        MsgBox("Numéro du BL déja utilisé par le même fournisseur !", MsgBoxStyle.Critical, "Erreur")
        '        tNumeroBlFact.Value = ""
        '        tNumeroBlFact.Focus()
        '        Exit Sub
        '    End If

        'Catch ex As Exception

        '    'Gérer l'Exception
        '    fMessageException.Show("Achat", "fAchat", "tNumeroBlFact_KeyUp", ex.Message, "0000719", "Erreur d'excution de tNumeroBlFact_KeyUp", True, True, True)
        '    Return

        'End Try


    End Sub

    Private Sub cmbDateBlFacture_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dDateBlFacture.KeyDown

        Try

            If e.KeyCode = Keys.Enter And Confirmation = False Then
                tTotalHT.Focus()
            End If
            Confirmation = False

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "cmbDateBlFacture_KeyDown", ex.Message, "0000720", "Erreur d'excution de cmbDateBlFacture_KeyDown", True, True, True)
            Return

        End Try

    End Sub

    Public Sub AfficherFicheArticle(ByVal CodeArticle, ByVal StockArticle, ByVal Designation)

        Try

            Dim MyFicheArticle As New fFicheArticle
            MyFicheArticle.CodeArticle = CodeArticle
            MyFicheArticle.StockArticle = StockArticle
            MyFicheArticle.DesignationArticle = Designation
            MyFicheArticle.ajoutmodif = "M"

            MyFicheArticle.Init()
            MyFicheArticle.ShowDialog()

            ArtcleConfirmer = MyFicheArticle.EstConfirmer

            MyFicheArticle.Close()
            MyFicheArticle.Dispose()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "AfficherFicheArticle", ex.Message, "0000721", "Erreur d'excution de AfficherFicheArticle", True, True, True)
            Return

        End Try

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click

        'Suivi du scénario 
        fMessageException.Show("Achat", "fAchat", "bQuitter_Click", "NoException", "NoError", "Clic sur le bouton Fermer", False, True, False)

        If Mode = "Consultation" Then
            fMain.Tab.SelectedTab.Dispose()
            Exit Sub
        End If

        If dsAchat.Tables("ACHAT_DETAILS").Rows.Count > 0 And gArticles(0, "CodeArticle") <> "" Then
            If MsgBox("Voulez vous vraiment Fermer cet Achat ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Fermer Achat") = MsgBoxResult.No Then
                Exit Sub
            End If
        End If

        fMain.Tab.SelectedTab.Dispose()
    End Sub


    Private Sub gArticles_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gArticles.MouseClick

        Try
            '---------------------------------- Controler la qte
            If Not IsDBNull(gArticles.Columns("QuantiteUnitaire").Value) And Not IsDBNull(gArticles.Columns("Stock").Value) Then
                If gArticles.Columns("QuantiteUnitaire").Value.ToString() <> "" And gArticles.Columns("Stock").Value.ToString() <> "" Then
                    If (gArticles.Columns("QuantiteUnitaire").Value < 0) And (Math.Abs(gArticles.Columns("QuantiteUnitaire").Value) > gArticles.Columns("Stock").Value) Then
                        gArticles.Focus()
                        gArticles.Col = 5
                        gArticles.EditActive = True
                        Exit Sub
                    End If
                Else
                    bAjouter_Click(sender, e)
                End If
            End If

            If Mode = "Ajout" Then
                If gArticles.Row <> dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 Then
                    gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
                    gArticles.Splits(0).DisplayColumns("Designation").Locked = True
                ElseIf gArticles.Row = dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 Then
                    gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
                    gArticles.Splits(0).DisplayColumns("Designation").Locked = False
                End If
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "gArticles_MouseClick", ex.Message, "0000723", "Erreur d'excution de gArticles_MouseClick", True, True, True)
            Return

        End Try

    End Sub

    Private Sub bModifier_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifier.Click

        'Suivi du scénario 
        fMessageException.Show("Achat", "fAchat", "bModifier_Click", "NoException", "NoError", "Clic sur le bouton Modifier", False, True, False)

        Dim i As Integer = 0

        Dim cmd As New SqlCommand
        Dim NBRAchat As Integer = 0


        Try
            If ControleDAcces(6, "MODIFICATION_ACHAT") = "False" Then
                Exit Sub
            End If

            'Controle si cet achat est réglé ou non
            StrSQL = "SELECT COUNT(NumeroAchat) FROM REGLEMENT_FOURNISSEUR_ACHAT " + _
                            "WHERE NumeroAchat =" + Quote(NumeroAchat) + ""
            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            If Not IsDBNull(cmd.ExecuteScalar()) Then
                NBRAchat = cmd.ExecuteScalar()
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "bModifier_Click", ex.Message, "0000724", "Erreur d'excution de bModifier_Click", True, True, True)

        End Try

        If NBRAchat <> 0 Then
            MsgBox("Cet achat est réglé, Suppression réfusé !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        Try

            'ajout d'un nouvel enregistrement vide dans les datatables convenables
            NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
            NouvelArticle("Designation") = ""
            NouvelArticle("CodeArticle") = ""
            dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)

            For i = 0 To dsAchat.Tables("ACHAT_DETAILS").Columns.Count - 1
                Try
                    Me.gArticles.Splits(0).DisplayColumns(i).AllowFocus = False
                Catch ex As Exception
                End Try
            Next

            With gArticles
                .Splits(0).DisplayColumns("CodeABarre").Locked = False
                .Splits(0).DisplayColumns("Designation").Locked = False
                .Splits(0).DisplayColumns("Qte").Locked = False
                .Splits(0).DisplayColumns("Remise").Locked = False
                .Splits(0).DisplayColumns("NumeroLotArticle").Locked = False
                .Splits(0).DisplayColumns("DatePeremptionSaisie").Locked = False
                .Splits(0).DisplayColumns("PrixAchatHT").Locked = False
                .Splits(0).DisplayColumns("QuantiteUnitaire").Locked = False

                '.Splits(0).DisplayColumns("CodeArticle").AllowFocus = True
                .Splits(0).DisplayColumns("CodeABarre").AllowFocus = True
                .Splits(0).DisplayColumns("Designation").AllowFocus = True
                .Splits(0).DisplayColumns("Qte").AllowFocus = True
                .Splits(0).DisplayColumns("Remise").AllowFocus = True
                .Splits(0).DisplayColumns("NumeroLotArticle").AllowFocus = True
                .Splits(0).DisplayColumns("DatePeremptionSaisie").AllowFocus = True
                .Splits(0).DisplayColumns("PrixAchatHT").AllowFocus = True
                .Splits(0).DisplayColumns("QuantiteUnitaire").AllowFocus = True

            End With

            initControlModification()

            cmbFournisseur.Focus()
            tAutre.Enabled = True
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "bModifier_Click", ex.Message, "0000725", "Erreur d'excution de bModifier_Click", True, True, True)
            Return
        End Try

    End Sub

    Private Sub gListeRecherche_UnboundColumnFetch(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gListeRecherche.UnboundColumnFetch

        Try

            Dim y As String
            y = gListeRecherche(e.Row, ("CodeArticle"))
            e.Value = CalculeStock(y)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "gListeRecherche_UnboundColumnFetch", ex.Message, "0000726", "Erreur d'excution de gListeRecherche_UnboundColumnFetch", True, True, True)
            Return

        End Try

    End Sub

    Private Sub gArticles_UnboundColumnFetch(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gArticles.UnboundColumnFetch

        'Récuperer la valeur Désignation FORME ARTICLE 
        Try

            StrSQL = " SELECT LibelleForme FROM FORME_ARTICLE AS F JOIN ARTICLE AS A ON  " + _
                     " F.CodeForme = A.CodeForme" + _
                     " WHERE CodeArticle = " + Quote(gArticles(e.Row, "CodeArticle"))

            cmdAchat.Connection = ConnectionServeur
            cmdAchat.CommandText = StrSQL

            e.Value = cmdAchat.ExecuteScalar()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "gArticles_UnboundColumnFetch", ex.Message, "0000001", "Erreur d'excution de gArticles_UnboundColumnFetch", True, True, True)

        End Try

    End Sub

    Private Function selectionDernierLigneAchat()

        selectionDernierLigneAchat = 0

        Try

            'Affécter le nombre de ligne au variable global  NumeroligneAchat
            StrSQL = " SELECT COUNT(*) FROM ACHAT "

            cmdAchat.Connection = ConnectionServeur
            cmdAchat.CommandText = StrSQL

            selectionDernierLigneAchat = cmdAchat.ExecuteScalar()

            Return selectionDernierLigneAchat

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "init", ex.Message, "0000009", "Erreur d'excution de selectionDernierLigneAchat", True, True, True)
            Return selectionDernierLigneAchat

        End Try

    End Function

    Private Sub selectionPrmierLigneAchat()

        'Affécter le numéro 1 au variable global  NumeroligneAchat
        NumeroligneAchat = 1

    End Sub

    Private Sub selectionLigneAchatSuivant()

        'Affécter le numéro 1 au variable global  NumeroligneAchat
        NumeroligneAchat = NumeroligneAchat + 1

    End Sub

    Private Sub selectionLigneAchatPrecedent()

        'Affécter le numéro 1 au variable global  NumeroligneAchat
        NumeroligneAchat = NumeroligneAchat - 1

    End Sub

    Private Sub initAchat()

        'Chargement de l'entête de l'achat
        Try

            StrSQL = " SELECT TOP (0 )* FROM ACHAT "
            cmdAchatEntete.Connection = ConnectionServeur
            cmdAchatEntete.CommandText = StrSQL
            daAchatEntete = New SqlDataAdapter(cmdAchatEntete)
            daAchatEntete.Fill(dsAchat, "ACHAT")
            cbAchatEntete = New SqlCommandBuilder(daAchatEntete)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "initAchat", ex.Message, "0000014", "Erreur d'excution de initAchat", True, True, True)
            Return

        End Try

    End Sub

    Private Sub initAchatDetails()

        Try

            ' Chargement des détails des achats


            'set DateFormat dmy 
            StrSQL = "SELECT TOP(0) NumeroAchat," + _
                     "CodeArticle," + _
                     "CodeABarre," + _
                     "Designation," + _
                     "'' AS LibelleForme," + _
                     "Qte," + _
                     "QuantiteUnitaire, " + _
                     " '-' AS QteCommander," + _
                     "convert(varchar, DatePeremption, 103) as DatePeremptionSaisie ," + _
                     "NumeroLotArticle," + _
                     "Stock," + _
                     "Remise," + _
                     "PrixAchatHT," + _
                     "PrixVenteTTC," + _
                     "TotalAchatHT," + _
                     "TVA," + _
                     "Ordre,  " + _
                     "DatePeremption,  " + _
                     "'' AS Vide " + _
                     "FROM " + _
                     "ACHAT_DETAILS " + _
                     "WHERE NumeroAchat =" + Quote(NumeroAchat) + " ORDER BY Ordre"

            cmdAchatDetail.Connection = ConnectionServeur
            cmdAchatDetail.CommandText = StrSQL
            daAchatDetails = New SqlDataAdapter(cmdAchatDetail)
            daAchatDetails.Fill(dsAchat, "ACHAT_DETAILS")
            cbAchatDetails = New SqlCommandBuilder(daAchatDetails)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "initAchatDetails", ex.Message, "0000015", "Erreur d'excution de initAchatDetails", True, True, True)
            Return

        End Try

    End Sub

    Private Sub initFournisseur()


        Try

            Try
                dsAchat.Tables("FOURNISSEUR").Clear()
            Catch ex As Exception

            End Try

            'Chargement des fournisseurs
            StrSQL = "SELECT CodeFournisseur, NomFournisseur FROM FOURNISSEUR WHERE Supprimer = 0 ORDER BY NomFournisseur ASC"
            cmdAchat.Connection = ConnectionServeur
            cmdAchat.CommandText = StrSQL
            daAchat = New SqlDataAdapter(cmdAchat)
            daAchat.Fill(dsAchat, "FOURNISSEUR")
            cmbFournisseur.DataSource = dsAchat.Tables("FOURNISSEUR")
            cmbFournisseur.ValueMember = "CodeFournisseur"
            cmbFournisseur.DisplayMember = "NomFournisseur"
            cmbFournisseur.ColumnHeaders = False
            cmbFournisseur.Splits(0).DisplayColumns("CodeFournisseur").Visible = False
            cmbFournisseur.Splits(0).DisplayColumns("NomFournisseur").Width = 10
            cmbFournisseur.ExtendRightColumn = True

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "initFournisseur", ex.Message, "0000016", "Erreur d'excution de initFournisseur", True, True, True)
            Return

        End Try

    End Sub

    Private Sub initgArticles()


        Try

            With gArticles
                .Columns.Clear()
                .DataSource = dsAchat
                .DataMember = "ACHAT_DETAILS"
                .Columns("CodeABarre").Caption = "Code article"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Qte").Caption = "Qte"
                .Columns("QteCommander").Caption = " Qte Cmd"

                .Columns("NumeroLotArticle").Caption = "Numero Lot"
                .Columns("DatePeremptionSaisie").Caption = "Date de péremption"
                .Columns("Stock").Caption = "Stock"
                .Columns("Remise").Caption = "Remise"
                .Columns("PrixAchatHT").Caption = "Prix A HT "
                .Columns("PrixVenteTTC").Caption = "Prix V TTC "
                .Columns("TotalAchatHT").Caption = "Total A HT"
                .Columns("TVA").Caption = "TVA"
                .Columns("QuantiteUnitaire").Caption = "Qte Unit"

                'colonne vide
                .Columns("Vide").Caption = ""

                ' Colonne non liée : LibelleForme
                .Columns("LibelleForme").DataField = ""



                ' Centrer toutes les entêtes
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Locked = True
                Next
                Dim cc As New C1.Win.C1Input.C1DateEdit
                '.Splits(0).DisplayColumns("DatePeremption").Style.f()



                '.Columns("DatePeremption").EnableDateTimeEditor = False
                .Columns("DatePeremptionSaisie").EditMask = "##/##/####"
                .Columns("DatePeremptionSaisie").EditMaskUpdate = True






                '.Columns("DatePeremption").DefaultValue
                '("##/##/####")

                ''''.Columns("DatePeremption").DataType = System.Type.GetType("System.DateTime")


                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far

                .Splits(0).DisplayColumns("NumeroAchat").Width = 0
                .Splits(0).DisplayColumns("NumeroAchat").Visible = False
                .Splits(0).DisplayColumns("NumeroAchat").AllowSizing = False
                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

                If Screen.PrimaryScreen.Bounds.Width < 1350 Then
                    .Splits(0).DisplayColumns("CodeABarre").Width = 150
                    .Splits(0).DisplayColumns("Designation").Width = 240 '300
                    .Splits(0).DisplayColumns("Forme").Width = 90
                    .Splits(0).DisplayColumns("Qte").Width = 50
                    .Splits(0).DisplayColumns("DatePeremptionSaisie").Width = 140
                    .Splits(0).DisplayColumns("Stock").Width = 50
                    .Splits(0).DisplayColumns("Remise").Width = 60
                    .Splits(0).DisplayColumns("PrixAchatHT").Width = 100
                    .Splits(0).DisplayColumns("PrixVenteTTC").Width = 110
                    .Splits(0).DisplayColumns("TotalAchatHT").Width = 110
                    .Splits(0).DisplayColumns("TVA").Width = 80
                Else
                    .Splits(0).DisplayColumns("CodeABarre").Width = 150
                    .Splits(0).DisplayColumns("Designation").Width = Screen.PrimaryScreen.Bounds.Width - 980
                    .Splits(0).DisplayColumns("Forme").Width = 90
                    .Splits(0).DisplayColumns("Qte").Width = 50
                    .Splits(0).DisplayColumns("DatePeremptionSaisie").Width = 140
                    .Splits(0).DisplayColumns("Stock").Width = 50
                    .Splits(0).DisplayColumns("Remise").Width = 60
                    .Splits(0).DisplayColumns("PrixAchatHT").Width = 100
                    .Splits(0).DisplayColumns("PrixVenteTTC").Width = 110
                    .Splits(0).DisplayColumns("TotalAchatHT").Width = 110
                    .Splits(0).DisplayColumns("TVA").Width = 80
                End If


                .Splits(0).DisplayColumns("Remise").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far


                .Splits(0).DisplayColumns("QteCommander").Visible = False
                .Splits(0).DisplayColumns("QuantiteUnitaire").Visible = False


                .Splits(0).DisplayColumns("NumeroLotArticle").Visible = False

                .Splits(0).DisplayColumns("DatePeremption").Visible = False


                'colonne vide ajouter
                .Splits(0).DisplayColumns("Vide").Width = 50
                .Splits(0).DisplayColumns("Vide").Visible = False

                .Splits(0).DisplayColumns("Ordre").Width = 0
                .Splits(0).DisplayColumns("Ordre").Visible = False
                .Splits(0).DisplayColumns("Ordre").AllowSizing = False

                ''coloriage de la liste 
                'For i = 0 To .Columns.Count - 1
                '    .Splits(0).DisplayColumns(i).Style.BackColor = Color.BlanchedAlmond
                'Next

                '.Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.Cornsilk
                '.Splits(0).DisplayColumns("Designation").Style.BackColor = Color.Cornsilk
                '.Splits(0).DisplayColumns("Qte").Style.BackColor = Color.Cornsilk
                '.Splits(0).DisplayColumns("Remise").Style.BackColor = Color.Cornsilk
                '.Splits(0).DisplayColumns("QuantiteUnitaire").Style.BackColor = Color.Cornsilk

                '"#######################

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.BackColor = Color.FromArgb(255, 238, 234)
                Next

                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(210, 240, 255)

                .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(252, 252, 252)
                .Splits(0).DisplayColumns("DatePeremptionSaisie").Style.BackColor = Color.FromArgb(255, 238, 234)

                .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(255, 238, 234)
                .Splits(0).DisplayColumns("Remise").Style.BackColor = Color.FromArgb(255, 238, 234)
                .Splits(0).DisplayColumns("TVA").Style.BackColor = Color.FromArgb(255, 238, 234)
                .Splits(0).DisplayColumns("Vide").Style.BackColor = Color.FromArgb(255, 238, 234)
                .Splits(0).DisplayColumns("NumeroLotArticle").Style.BackColor = Color.FromArgb(255, 238, 234)


                .Splits(0).DisplayColumns("QteCommander").Style.BackColor = Color.FromArgb(252, 252, 252)
                .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(252, 252, 252)

                .Splits(0).DisplayColumns("PrixAchatHT").Style.BackColor = Color.FromArgb(252, 252, 252)
                .Splits(0).DisplayColumns("PrixVenteTTC").Style.BackColor = Color.FromArgb(210, 240, 255)
                .Splits(0).DisplayColumns("TotalAchatHT").Style.BackColor = Color.FromArgb(252, 252, 252)
                .Splits(0).DisplayColumns("QuantiteUnitaire").Style.BackColor = Color.FromArgb(252, 252, 252)

                '"#######################


                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                .AllowSort = False
                .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

                'Style du Caractere et du grid
                ParametreGrid(gArticles)
            End With

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "initFournisseur", ex.Message, "0000727", "Erreur d'excution de initgArticles", True, True, True)
            Return

        End Try

    End Sub

    Private Sub initArticle()

        '--------------------initialisation de la datatable article qui est utilisée dans la liste de 
        '--------------------recherche alimenté selon les entrés de l'utilisateur dans la colonne designation
        Try

            StrSQL = " SELECT TOP 0 CodeArticle," + _
                     " Designation," + _
                     " LibelleForme," + _
                     " PrixVenteTTC," + _
                     " QuantiteUnitaire" + _
                     " FROM ARTICLE " + _
                     " JOIN FORME_ARTICLE ON ARTICLE.CodeForme = FORME_ARTICLE.CodeForme "

            cmdAchat.Connection = ConnectionServeur
            cmdAchat.CommandText = StrSQL
            daAchat = New SqlDataAdapter(cmdAchat)
            daAchat.Fill(dsAchat, "ARTICLE")

            With gListeRecherche
                .Columns.Clear()
                .DataSource = dsAchat
                .DataMember = "ARTICLE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code Article"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("PrixVenteTTC").Caption = "Prix de vente"
                .Columns("QuantiteUnitaire").Caption = "Qte Unit"

                ' Centrer tous les entêtes
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centre tous les valeurs
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Visible = False
                Next

                .Splits(0).DisplayColumns("CodeArticle").Visible = True
                .Splits(0).DisplayColumns("Designation").Visible = True
                .Splits(0).DisplayColumns("LibelleForme").Visible = True
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True
                .Splits(0).DisplayColumns("QuantiteUnitaire").Visible = False

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("Designation").Width = 300
                .Splits(0).DisplayColumns("LibelleForme").Width = 100
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120
                .Splits(0).DisplayColumns("QuantiteUnitaire").Width = 0

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 20
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True

                'Style du Caractere et du grid
                ParametreGrid(gListeRecherche)

            End With

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "initArticle", ex.Message, "0000728", "Erreur d'excution de initArticle", True, True, True)
            Return

        End Try

    End Sub

    Private Sub initLoadControl()

        Try

            Timer1.Start()

            lTotHT.Text = "0.000"
            lTotalTTC.Text = "0.000"
            lTotalTVA.Text = "0.000"
            lTotalQte.Text = "0"
            tRemisePourcentage.Value = "0"
            lRemise.Text = "0.000"
            lValeurVenteTTC.Text = "0.000"
            lHTNet.Text = "0.000"
            tAutre.Value = "0.000"

            tRemisePourcentage.Visible = False
            lRemisePourcentAfficher.Visible = False
            lTotalQteAffiche.Visible = False
            lTotalQte.Visible = False

            DepuisCommande = False

            '-------------------------------- initialisation du tableau des commandes convertis en achat
            Dim k As Integer = 0

            Mode = "Consultation"

            For k = 0 To TableauCommande.Length - 1
                TableauCommande(k) = ""
            Next

            bTerminal.Enabled = False
            bConfirmer.Enabled = False
            bAnnuler.Enabled = False
            bCommande.Enabled = False
            bFournisseur.Enabled = False
            bRemise.Enabled = False
            bAjouter.Enabled = True
            bRecherche.Enabled = True
            bInstance.Enabled = False
            bInstance.Visible = False

            bFirst.Enabled = True
            bPrevious.Enabled = True
            'bNext.Enabled = True
            'bLast.Enabled = True

            GroupeFournisseur.Enabled = False
            GroupeSituationFournisseur.Visible = False

            dDateBlFacture.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
            dDateBlFacture.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

            'Rendre la liste en mode read only
            For i = 0 To gArticles.Columns.Count - 1
                gArticles.Splits(0).DisplayColumns(i).Locked = True
            Next

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "initLoadControl", ex.Message, "0000729", "Erreur d'excution de initLoadControl", True, True, True)
            Return

        End Try

    End Sub

    Private Sub initNombreAchatInstance()

        Try

            '------------------------- affichage du nombre d'achats en instance 
            StrSQL = " SELECT COUNT(*) FROM ACHAT_INSTANCE "
            cmdAchat.Connection = ConnectionServeur
            cmdAchat.CommandText = StrSQL

            If Not IsDBNull(cmdAchat.ExecuteScalar()) Then
                NombreAchatInstance = cmdAchat.ExecuteScalar()
                lNbreInstance.Text = "Achat en frigo : " + NombreAchatInstance.ToString
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "initNombreAchatInstance", ex.Message, "0000018", "Erreur d'excution de initNombreAchatInstance", True, True, True)
            Return

        End Try

    End Sub

    Private Sub initControlAjout()

        '---------------------------------Debloquer le saisie

        Try

            With gArticles

                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Locked = True
                Next

                .Splits(0).DisplayColumns("CodeABarre").Locked = False
                .Splits(0).DisplayColumns("Designation").Locked = False
                .Splits(0).DisplayColumns("Qte").Locked = False
                .Splits(0).DisplayColumns("Remise").Locked = False
                .Splits(0).DisplayColumns("NumeroLotArticle").Locked = False
                .Splits(0).DisplayColumns("DatePeremptionSaisie").Locked = False
                ' .Splits(0).DisplayColumns("DatePeremption")
                .Splits(0).DisplayColumns("PrixAchatHT").Locked = False
                .Splits(0).DisplayColumns("QuantiteUnitaire").Locked = False

            End With


            '------------------------------ initialisation des differents zones de textes 

            lRemise.Visible = True
            lHTNet.Visible = True
            tRemisePourcentage.Visible = True
            tAutre.Enabled = True

            lRemisePourcentAfficher.Visible = True
            lRemiseAfficher.Visible = True
            lHTNetAfficher.Visible = True

            TotalTTCAchat = 0.0
            TotalHTNETAchat = 0.0
            TVA = 0.0
            Timbre = 0.0 '0.3
            TotalTVAAchat = 0.0
            TotalVenteTTCAchat = 0.0

            lHTNet.Text = "0.000"
            lRemise.Text = "0.000"
            tRemisePourcentage.Value = "0.000"
            lTotHT.Text = "0.000"
            lTotalTTC.Text = "0.000"
            lTotalQte.Text = "0"
            lValeurVenteTTC.Text = "0.000"
            lTotalTVA.Text = "0.000"
            tAutre.Value = "0.000"
            tTotalHT.Value = "0.000"

            lSoldeFournisseur.Text = "0.000"
            lDDA.Text = "-"
            lDDR.Text = "-"

            lTotalQteAffiche.Visible = True
            lTotalQte.Visible = True

            lOperateur.Text = "-"

            lDateAchat.Text = System.DateTime.Now
            cmbFournisseur.Text = ""
            lNumeroAchat.Text = "-------------"

            bAnnuler.Enabled = True
            bConfirmer.Enabled = True
            bCommande.Enabled = True
            bFournisseur.Enabled = True
            bRemise.Visible = True
            bRemise.Enabled = True
            bRecherche.Enabled = False
            bModifier.Visible = False
            bImprimer.Enabled = False
            bListe.Enabled = False
            bListe.Visible = False
            bFirst.Visible = False
            bPrevious.Visible = False
            bNext.Visible = False
            bLast.Visible = False
            bAjouter.Enabled = False
            bInstance.Enabled = True
            bInstance.Visible = True
            'bQuitter.Enabled = False

            tNumeroBlFact.Value = ""
            dDateBlFacture.Value = System.DateTime.Now

            GroupeSituationFournisseur.Visible = False
            GroupeFournisseur.Enabled = True

            cmbFournisseur.Focus()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "initNombreAchatInstance", ex.Message, "0000730", "Erreur d'excution de initControlAjout", True, True, True)
            Return

        End Try

    End Sub

    Private Sub verrouillageBouttonsAchat()


        Try

            '-------------------------------Verrouillage des boutons

            bAnnuler.Enabled = False
            bConfirmer.Enabled = False
            bCommande.Enabled = False
            bFournisseur.Enabled = False
            bRemise.Visible = False
            bRecherche.Enabled = True
            bModifier.Visible = True
            bImprimer.Enabled = True
            bFirst.Visible = True
            bPrevious.Visible = True
            bNext.Visible = True
            bLast.Visible = True
            bAjouter.Enabled = True
            bListe.Enabled = True
            bListe.Visible = True
            bQuitter.Enabled = True
            bInstance.Enabled = False
            bInstance.Visible = False

            tRemisePourcentage.Visible = False
            lRemisePourcentAfficher.Visible = False
            lTotalQteAffiche.Visible = False
            lTotalQte.Visible = False
            tAutre.Enabled = False

            gListeRecherche.Visible = False

            GroupeSituationFournisseur.Visible = False

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "verrouillageBouttonsAchat", ex.Message, "0000731", "Erreur d'excution de verrouillageBouttonsAchat", True, True, True)
            Return

        End Try

    End Sub


    Private Sub rechercheAchat(ByVal pNumeroAchat As String)


        Try

            ''----------------------------------Traitement

            If tRecherche.Text.Length < 11 Then
                If tRecherche.Text.Contains(Date.Now.Year.ToString() + "/") Then
                    pNumeroAchat = pNumeroAchat.Substring(0, 5) + pNumeroAchat.Substring(5, pNumeroAchat.Length - 5).PadLeft(6, "0")
                End If
            End If

            'Recuperer la valeur de la row
            recupererNumRowRechrche(pNumeroAchat)

            'Vente inexistante, charger lelement courant
            If NumeroligneAchat <> 0 Then
                ChargerAchat(NumeroligneAchat)
            End If

            tRecherche.Value = ""
            tRecherche.Visible = False

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "rechercheAchat", ex.Message, "0000732", "Erreur d'excution de rechercheAchat", True, True, True)
            Return

        End Try

    End Sub

    Private Sub recupererNumRowRechrche(ByVal pRechercheCritere As String)

        Try
            '------------------------- affichage du nombre d'achats en instance 
            StrSQL = " SELECT RowNumber " + _
                     " FROM (select ROW_NUMBER() OVER(ORDER BY NumeroAchat) " + _
                     " AS 'RowNumber' , NumeroAchat, [NumeroBL/Facture]  from ACHAT) AS ACHATLISTE " + _
                     " where ACHATLISTE.NumeroAchat =  " & Quote(pRechercheCritere) + _
                     " OR ACHATLISTE.[NumeroBL/Facture] =  " & Quote(pRechercheCritere)

            cmdAchat.Connection = ConnectionServeur
            cmdAchat.CommandText = StrSQL

            NumeroligneAchat = cmdAchat.ExecuteScalar()

            If NumeroligneAchat = 0 Then
                MsgBox("Achat inéxistant", MsgBoxStyle.Exclamation, "Rcherche")
                NumeroligneAchat = selectionDernierLigneAchat()
            End If

            'Tester si on atteint la premiere ligne
            'pour desactiver les BTN Précedent et Premier élément        
            If NumeroligneAchat = 1 Or NumeroligneAchat = 0 Then

                bPrevious.Enabled = False
                bFirst.Enabled = False

            Else

                bPrevious.Enabled = True
                bFirst.Enabled = True

            End If

            'Tester si on atteint la derniere ligne
            'pour desactiver les BTN Siuvant et Dernier élément
            If NumeroligneAchat = selectionDernierLigneAchat() Then

                bNext.Enabled = False
                bLast.Enabled = False

            Else

                bNext.Enabled = True
                bLast.Enabled = True

            End If


        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "recupererNumRowRechrche", ex.Message, "0000733", "Erreur d'excution de recupererNumRowRechrche", True, True, True)
            Return

        End Try

    End Sub

    Private Sub initControlModification()

        Try

            GroupeFournisseur.Enabled = True

            Mode = "Modif"

            bAnnuler.Enabled = True
            bConfirmer.Enabled = True

            bCommande.Enabled = True
            bFournisseur.Enabled = True

            bRemise.Visible = True
            bRemise.Enabled = True
            bRecherche.Enabled = False

            bModifier.Visible = False
            bImprimer.Enabled = False

            bListe.Enabled = False
            bListe.Visible = False

            bFirst.Visible = False
            bPrevious.Visible = False

            bNext.Visible = False
            bLast.Visible = False

            bAjouter.Enabled = False
            bInstance.Enabled = True

            bInstance.Visible = True
            'bQuitter.Enabled = False

            lTotalQte.Visible = True
            lTotalQteAffiche.Visible = True
            tRemisePourcentage.Visible = True
            lRemisePourcentAfficher.Visible = True


            tNumeroBlFact.Enabled = True
            cmbFournisseur.Enabled = True
            dDateBlFacture.Enabled = True
            tTotalHT.Enabled = True

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "initControlModification", ex.Message, "0000734", "Erreur d'excution de initControlModification", True, True, True)
            Return

        End Try

    End Sub


    Private Sub tNumeroBlFact_Leave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNumeroBlFact.Leave
        'If e.KeyCode = Keys.Enter And Confirmation = False Then
        '    cmbDateBlFacture.Focus()
        'End If
        'Confirmation = False

        Dim cmd As New SqlCommand
        'Dim StrSQL As String = ""
        Dim AchatAvecLeMemeNumeroBl As Integer = 0
        Dim NumeroAchatAPartirDuNumeroBL As String = ""

        Try

            If Mode = "Consultation" And tNumeroBlFact.Text <> "" Then
                StrSQL = " SELECT NumeroAchat FROM [ACHAT] " + _
                         " WHERE rtrim(ltrim([NumeroBL/Facture])) ='" + tNumeroBlFact.Text + "'"

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    NumeroAchatAPartirDuNumeroBL = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If NumeroAchatAPartirDuNumeroBL <> "" Then
                    tRecherche.Value = NumeroAchatAPartirDuNumeroBL
                    tRecherche_KeyUp(sender, e)
                End If

                tNumeroBlFact.Enabled = True
                cmbFournisseur.Enabled = True
                dDateBlFacture.Enabled = True
                tTotalHT.Enabled = True
                GroupeFournisseur.Enabled = False

                Exit Sub
            End If

            '------------------------------ verification si le fournisseur admet le même numero du BL 
            StrSQL = " SELECT COUNT(NumeroAchat) FROM [ACHAT] " + _
                     " WHERE CodeFournisseur ='" + cmbFournisseur.SelectedValue + _
                     " ' AND [NumeroBL/Facture]='" + tNumeroBlFact.Text + "'"

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                AchatAvecLeMemeNumeroBl = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If AchatAvecLeMemeNumeroBl > 0 And Mode = "Ajout" Then
                MsgBox("Numéro du BL déja utilisé par le même fournisseur !", MsgBoxStyle.Critical, "Erreur")
                If (MessageBox.Show("Voulez vous afficher BL", "", MessageBoxButtons.YesNo) = DialogResult.Yes) Then
                    Dim NumeroBL As String = tNumeroBlFact.Text
                    bAnnuler_Click(sender, e)
                    rechercheAchat(NumeroBL)
                    Exit Sub
                End If
                tNumeroBlFact.Value = ""
                tNumeroBlFact.Focus()
                Exit Sub
            End If

        Catch ex As Exception

            'Gérer l'Exception
            'fMessageException.Show("Achat", "fAchat", "tNumeroBlFact_KeyUp", ex.Message, "0000719", "Erreur d'excution de tNumeroBlFact_KeyUp", True, True, True)
            ' Return

        End Try
    End Sub

    Private Sub gArticles_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyDown
        If e.KeyCode = Keys.F1 And gArticles.Columns("CodeArticle").Value.ToString <> "" Then
            Dim stock As Integer
            Dim CodeArticle As String = gArticles.Columns("CodeArticle").Value
            Dim CodeABarre As String = ""
            Dim Qte As Integer = gArticles.Columns("Qte").Value

            stock = CalculeStock(gArticles.Columns("CodeArticle").Value)
            AfficherFicheArticle(gArticles.Columns("CodeArticle").Value, stock, gArticles.Columns("Designation").Value)

            cmdCodeABarre.Connection = ConnectionServeur
            cmdCodeABarre.CommandText = "SELECT CodeABarre FROM ARTICLE WHERE CodeArticle = " & Quote(CodeArticle)
            Try
                CodeABarre = cmdCodeABarre.ExecuteScalar().ToString
            Catch ex As Exception
            End Try

            If ArtcleConfirmer = True Then
                ModifierDetailArticle(CodeABarre)
            End If
            VerifierSiDejaSaisi(CodeArticle)
            gArticles.EditActive = True
            Exit Sub
        End If
    End Sub

    Private Sub bTerminal_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bTerminal.Click

        Dim MyAchatLectureTerminal As New fAchatLectureTerminal
        MyAchatLectureTerminal.Mode = "Achat"

        Dim dsNewAchat As DataSet
        Dim total, j As Integer

        MyAchatLectureTerminal.Init()
        MyAchatLectureTerminal.ShowDialog()
        dsNewAchat = MyAchatLectureTerminal.dsInventaire

        If MyAchatLectureTerminal.Valider = True Then
            j = 0
            total = dsAchat.Tables("ACHAT_DETAILS").Rows.Count
            While j < total
                If dsAchat.Tables("ACHAT_DETAILS").Rows(j).Item("CodeArticle") = "" Then
                    dsAchat.Tables("ACHAT_DETAILS").Rows(j).Delete()
                    total -= 1
                End If
                j += 1
            End While
            For i = 0 To dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1


                'Ecraser les lignes achats et remplcer par l'inventaire
                If dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 > 0 Then
                    For j = 0 To dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1
                        If dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(j).Item("CodeArticle") <> "" Then
                            If j > dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 Then
                                Exit For
                            End If
                            If dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("CodeArticle") = dsAchat.Tables("ACHAT_DETAILS").Rows(j).Item("CodeArticle") And dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("DatePeremption").ToString() = dsAchat.Tables("ACHAT_DETAILS").Rows(j).Item("DatePeremptionSaisie").ToString() Then 'And dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("Zone") = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(j).Item("Zone") Then
                                dsAchat.Tables("ACHAT_DETAILS").Rows(j).Item("Stock") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("Stock")
                                dsAchat.Tables("ACHAT_DETAILS").Rows(j).Item("Qte") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("Qte")
                                GoTo Endfor
                            End If
                        End If
                    Next
                End If



                NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
                NouvelArticle("NumeroAchat") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("NumeroAchat")
                NouvelArticle("CodeArticle") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("CodeArticle")
                NouvelArticle("CodeABarre") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("CodeABarre")
                NouvelArticle("Designation") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("Designation")
                NouvelArticle("Remise") = "0.0"
                NouvelArticle("TVA") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("TVA")
                NouvelArticle("Qte") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("Qte")
                NouvelArticle("Stock") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("Stock")
                NouvelArticle("QuantiteUnitaire") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("QuantiteUnitaire")
                NouvelArticle("QteCommander") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("QteACommander")
                NouvelArticle("NumeroLotArticle") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("NumeroLotArticle")

                NouvelArticle("PrixAchatHT") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("PrixAchatHT")
                NouvelArticle("PrixVenteTTC") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("PrixVenteTTC")

                NouvelArticle("TotalAchatHT") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("TotalAchatHT")

                NouvelArticle("LibelleForme") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("LibelleForme")
                NouvelArticle("DatePeremptionSaisie") = dsNewAchat.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("DatePeremption")
                dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)

Endfor:
            Next
            CalculerMontants()
        End If
    End Sub

    Public Sub ImprimerCodeBarre(Code As String, Designation As String, Qte As String, PrixVenteTTC As String)
        Dim LineWrite As String = ""
        Dim TexteEtiquette As String = ""
        Dim NomSysteme As String = ""

        '        cmdCodeABarre.Connection = ConnectionServeur
        '        cmdCodeABarre.CommandText = " select NomDeLordinateurDImpressionCodeABarre  from PARAMETRES where POSTE =  " + System.Environment.GetEnvironmentVariable("Poste")
        '        Try
        '            NomSysteme = cmdCodeABarre.ExecuteScalar()
        '            NomOrdinateurImpressionCodeABarre = cmdCodeABarre.ExecuteScalar()
        '        Catch ex As Exception
        '            NomSysteme = "ZDesigner GK420t"
        '            NomOrdinateurImpressionCodeABarre = cmdCodeABarre.ExecuteScalar()
        '        End Try

        '        If Not NomSysteme.Contains("420") Then GoTo Imprimme

        '        'If (Len(Code) = 13) Then
        '        '    TexteEtiquette = "^XA" + vbCrLf
        '        '    TexteEtiquette += "^FO0,50^BY2" + vbCrLf
        '        '    TexteEtiquette += "^BEN,70,Y,N" + vbCrLf
        '        '    TexteEtiquette += "^FD" + Code + "^FS" + vbCrLf
        '        '    TexteEtiquette += "^FO300,50^BY2" + vbCrLf
        '        '    TexteEtiquette += "^BEN,70,Y,N" + vbCrLf
        '        '    TexteEtiquette += "^FD" + Code + "^FS" + vbCrLf
        '        '    TexteEtiquette += "^XZ"
        '        'End If

        '        If (Len(Code) = 10 Or Len(Code) = 12 Or Len(Code) = 14) Then
        '            TexteEtiquette = "^XA" + vbCrLf
        '            TexteEtiquette += "^FO0,50^BY2" + vbCrLf
        '            TexteEtiquette += "^B2N,70,Y,N,N" + vbCrLf
        '            TexteEtiquette += "^FD" + Code + "^FS" + vbCrLf
        '            TexteEtiquette += "^FO340,50^BY2" + vbCrLf
        '            TexteEtiquette += "^B2N,70,Y,N,N" + vbCrLf
        '            TexteEtiquette += "^FD" + Code + "^FS" + vbCrLf
        '            TexteEtiquette += "^XZ"
        '        End If

        '        'If (Len(Code) >= 3 And Len(Code) <= 8) Then
        '        '    TexteEtiquette = "^XA" + vbCrLf
        '        '    TexteEtiquette += "^FO0,50^BY2" + vbCrLf
        '        '    TexteEtiquette += "^B3N,N,70,Y,N" + vbCrLf
        '        '    TexteEtiquette += "^FD" + Code + "^FS" + vbCrLf
        '        '    TexteEtiquette += "^FO300,50^BY2" + vbCrLf
        '        '    TexteEtiquette += "^B3N,N,70,Y,N" + vbCrLf
        '        '    TexteEtiquette += "^FD" + Code + "^FS" + vbCrLf
        '        '    TexteEtiquette += "^XZ"
        '        'End If

        '        If (Len(Code) >= 3 And Len(Code) < 8) Then
        '            TexteEtiquette = "^XA" + vbCrLf
        '            TexteEtiquette += "^FO0,50^BY2" + vbCrLf
        '            TexteEtiquette += "^BCN,70,Y,N,N" + vbCrLf
        '            TexteEtiquette += "^FD" + Code + "^FS" + vbCrLf
        '            TexteEtiquette += "^FO340,50^BY2" + vbCrLf
        '            TexteEtiquette += "^BCN,70,Y,N,N" + vbCrLf
        '            TexteEtiquette += "^FD" + Code + "^FS" + vbCrLf
        '            TexteEtiquette += "^XZ"
        '        End If

        '        If (Len(Code) = 8 Or Len(Code) = 9 Or Len(Code) = 11) Then
        '            TexteEtiquette = "^XA" + vbCrLf
        '            TexteEtiquette += "^FO0,50^BY2" + vbCrLf
        '            TexteEtiquette += "^BCN,70,Y,N,N" + vbCrLf
        '            TexteEtiquette += "^FD" + Code + "^FS" + vbCrLf
        '            TexteEtiquette += "^FO340,50^BY2" + vbCrLf
        '            TexteEtiquette += "^BCN,70,Y,N,N" + vbCrLf
        '            TexteEtiquette += "^FD" + Code + "^FS" + vbCrLf
        '            TexteEtiquette += "^XZ"
        '        End If

        '        '################################


        '        If Not IsDBNull(TexteEtiquette) Then
        '            If TexteEtiquette <> "" Then
        '                For I As Integer = 1 To Convert.ToInt32(Qte)
        '                    LineWrite = TexteEtiquette

        '                    Application.DoEvents()

        '                    Try
        '                        cmdCodeABarre.Connection = ConnectionServeur
        '                        cmdCodeABarre.CommandText = " select NomDeLordinateurDImpressionCodeABarre  from PARAMETRES where POSTE =  " + System.Environment.GetEnvironmentVariable("Poste")
        '                        Try
        '                            NomSysteme = cmdCodeABarre.ExecuteScalar()
        '                        Catch ex As Exception
        '                            NomSysteme = "ZDesigner GK420t"
        '                        End Try
        '                        '"ZDesigner"
        '                        If Not IsDBNull(NomSysteme) Then
        '                            RawPrinterHelper.SendStringToPrinter(NomSysteme, LineWrite)
        '                        Else
        '                            MsgBox("Le nom système de l'étiqueteuse n'est pas correctement configurée !", MsgBoxStyle.Critical, "Erreur")
        '                        End If
        '                    Catch ex As Exception
        '                        MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
        '                    End Try
        '                Next
        '                Exit Sub
        '            End If
        '        Else
        '            MsgBox("Erreur dans le fichier de l'étiquette !", MsgBoxStyle.Critical, "Erreur")
        '        End If




Imprimme:
        Dim J As Integer = 0
        Dim nbCol As Integer = 0
        Dim dr As DataRow

        cmdCodeABarre.Connection = ConnectionServeur
        cmdCodeABarre.CommandText = "UPDATE PARAMETRE_PHARMACIE SET ImageCodeABarre=NULL"
        cmdCodeABarre.ExecuteNonQuery()

        cmdCodeABarre.CommandText = "SELECT * FROM PARAMETRE_PHARMACIE"
        daCodeABarre = New SqlDataAdapter(cmdCodeABarre)
        daCodeABarre.Fill(dsCodeABarre, "IMAGE")
        cbCodeABarre = New SqlCommandBuilder(daCodeABarre)

        BarcodeProfessional.Code = Code
        BarcodeProfessional.Text = ""
        BarcodeProfessional.Symbology = Neodynamic.WinControls.BarcodeProfessional.Symbology.Code128
        BarcodeProfessional.BarHeight = 1.0F
        BarcodeProfessional.BarWidth = 0.04F

        With dsCodeABarre.Tables("IMAGE")
            dr = .Rows(0)
            dr.Item("ImageCodeABarre") = ImageToByteArray(BarcodeProfessional.GetBarcodeImage())
        End With

        Try
            daCodeABarre.Update(dsCodeABarre, "IMAGE")

        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End Try

        If NomOrdinateurImpressionCodeABarre = "" Then

            cmdCodeABarre.Connection = ConnectionServeur
            cmdCodeABarre.CommandText = "DELETE FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE"
            cmdCodeABarre.ExecuteNonQuery()

            cmdCodeABarre.CommandText = "SELECT * FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE"
            daCodeABarre = New SqlDataAdapter(cmdCodeABarre)
            daCodeABarre.Fill(dsCodeABarre, "IMAGE")
            cbCodeABarre = New SqlCommandBuilder(daCodeABarre)



            If CInt(Qte) > 0 And CInt(Qte) <= 5 Then
                J = 1
                nbCol = CInt(Qte)
            ElseIf CInt(Qte) > 5 And CInt(Qte) <= 10 Then
                J = 2
                nbCol = CInt(Qte) - 5
            ElseIf CInt(Qte) > 10 And CInt(Qte) <= 15 Then
                J = 3
                nbCol = CInt(Qte) - 10
            ElseIf CInt(Qte) > 15 And CInt(Qte) <= 20 Then
                J = 4
                nbCol = CInt(Qte) - 15
            ElseIf CInt(Qte) > 20 And CInt(Qte) <= 25 Then
                J = 5
                nbCol = CInt(Qte) - 20
            ElseIf CInt(Qte) > 25 And CInt(Qte) <= 30 Then
                J = 6
                nbCol = CInt(Qte) - 25
            End If



            For I As Integer = 0 To J - 1
                With dsCodeABarre
                    dr = .Tables("IMAGE").NewRow

                    dr.Item("Col1") = " "
                    dr.Item("Col2") = " "
                    dr.Item("Col3") = " "
                    dr.Item("Col4") = " "
                    dr.Item("Col5") = " "
                    dr.Item("Ordre") = I
                    .Tables("IMAGE").Rows.Add(dr)
                End With
            Next
            Try
                daCodeABarre.Update(dsCodeABarre, "IMAGE")
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                Exit Sub
            End Try

            If nbCol = 1 Then
                cmdCodeABarre.CommandText = " UPDATE TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE SET Col2=NULL,COL3=NULL,Col4=NULL,COL5=NULL " + _
                                            " WHERE Ordre IN" + _
                                            " ( SELECT TOP(1) Ordre FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE ORDER BY Ordre DESC ) "
            ElseIf nbCol = 2 Then
                cmdCodeABarre.CommandText = " UPDATE TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE SET COL3=NULL,Col4=NULL,COL5=NULL " + _
                                            " WHERE Ordre IN" + _
                                            " ( SELECT TOP(1) Ordre FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE ORDER BY Ordre DESC ) "
            ElseIf nbCol = 3 Then
                cmdCodeABarre.CommandText = " UPDATE TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE SET Col4=NULL,COL5=NULL " + _
                                            " WHERE Ordre IN" + _
                                            " ( SELECT TOP(1) Ordre FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE ORDER BY Ordre DESC ) "
            ElseIf nbCol = 4 Then
                cmdCodeABarre.CommandText = " UPDATE TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE SET COL5=NULL " + _
                                            " WHERE Ordre IN" + _
                                            " ( SELECT TOP(1) Ordre FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE ORDER BY Ordre DESC ) "
            End If

            cmdCodeABarre.ExecuteNonQuery()

            Me.Dispose()
            Dim MyVisionneurCodeABarre As New fVisionneurCodeABarre
            MyVisionneurCodeABarre.ShowDialog()

        Else

            'If chbA4.Checked = True Then

            '    Dim NouvelEnregistrement As DataRow = Nothing

            '    cmdCodeABarre.Connection = ConnectionServeur
            '    cmdCodeABarre.CommandText = "DELETE FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE_A4 "
            '    cmdCodeABarre.ExecuteNonQuery()

            '    cmdCodeABarre.CommandText = "SELECT * FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE_A4"
            '    daCodeABarre = New SqlDataAdapter(cmdCodeABarre)
            '    daCodeABarre.Fill(dsCodeABarre, "IMAGE_A4")
            '    cbCodeABarre = New SqlCommandBuilder(daCodeABarre)

            '    With dsCodeABarre.Tables("IMAGE_A4")
            '        For i = 0 To CInt(Qte) - 1
            '            'dr = .Rows(i)
            '            'dr.Item("ImageCodeABarre") = ImageToByteArray(C1BarCode.GetImage(System.Drawing.Imaging.ImageFormat.Jpeg))

            '            NouvelEnregistrement = .NewRow()
            '            NouvelEnregistrement("ImageCodeABarre") = ImageToByteArray(C1BarCode.GetImage(System.Drawing.Imaging.ImageFormat.Jpeg))
            '            .Rows.Add(NouvelEnregistrement)

            '        Next
            '    End With


            '    Try
            '        daCodeABarre.Update(dsCodeABarre, "IMAGE_A4")

            '    Catch ex As Exception
            '        MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            '        Exit Sub
            '    End Try


            '    'Impression
            '    CRA4.FileName = Application.StartupPath + "\EtatImpressionCodeABarreA4.rpt"

            '    CRA4.SetParameterValue("Designation", Designation)
            '    CRA4.SetParameterValue("PrixVenteTTC", tPrix.Text)

            '    Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            '    Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
            '    For Each tbCurrent In CRA4.Database.Tables
            '        tliCurrent = tbCurrent.LogOnInfo
            '        With tliCurrent.ConnectionInfo
            '            .ServerName = NomServeur
            '            .UserID = NomUtilisateurSQL
            '            .Password = MotDePasseSQL
            '            .DatabaseName = NomBase
            '        End With
            '        tbCurrent.ApplyLogOnInfo(tliCurrent)
            '    Next tbCurrent

            '    CRA4.PrintOptions.PrinterName = NomOrdinateurImpressionCodeABarre
            '    CRA4.PrintToPrinter(1, False, 1, 1)


            'Else

            'Impression

            'CR.FileName = Application.StartupPath + "\EtatImpressionCodeABarre.rpt"

            'CR.SetParameterValue("Designation", Designation)

            'Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            'Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
            'For Each tbCurrent In CR.Database.Tables
            '    tliCurrent = tbCurrent.LogOnInfo
            '    With tliCurrent.ConnectionInfo
            '        .ServerName = NomServeur
            '        .UserID = NomUtilisateurSQL
            '        .Password = MotDePasseSQL
            '        .DatabaseName = NomBase
            '    End With
            '    tbCurrent.ApplyLogOnInfo(tliCurrent)
            'Next tbCurrent

            ''Dim MyViewer As New fViewer
            ''MyViewer.CRViewer.ReportSource = CR
            ''fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            ''fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
            ''fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
            ''fMain.Tab.SelectedTab.Text = "Impression Code à barre"

            'CR.PrintOptions.PrinterName = NomOrdinateurImpressionCodeABarre
            'CR.PrintToPrinter(Convert.ToInt32(Qte), False, 1, 1)

            ''Code = ""
            ''Designation = ""
            ''tPrix.Text = ""
            ''Qte = ""
            CR.FileName = Application.StartupPath + "\EtatImpressionCodeABarre.rpt"

            CR.SetParameterValue("Designation", Designation)
            CR.SetParameterValue("PrixVenteTTC", PrixVenteTTC)

            Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent

            'Dim MyViewer As New fViewer
            'MyViewer.CRViewer.ReportSource = CR
            'fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            'fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
            'fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
            'fMain.Tab.SelectedTab.Text = "Impression Code à barre"

            CR.PrintOptions.PrinterName = NomOrdinateurImpressionCodeABarre
            CR.PrintToPrinter(Qte, False, 1, 1)
        End If
    End Sub


    Private Sub bCodeABarre_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCodeABarre.Click
        If ControleDAcces(6, "IMPRESSION_CODEABARRE") = "False" Then
            Exit Sub
        End If

        Dim cmdSansCodeABarre As New SqlCommand
        cmdSansCodeABarre.Connection = ConnectionServeur
        Dim AucunProduitSansCodeABarre As Boolean = True

        For I As Integer = 0 To gArticles.RowCount - 1

            cmdSansCodeABarre.CommandText = "SELECT SansCodeBarre FROM ARTICLE WHERE CodeArticle=" + Quote(gArticles(I, "CodeArticle"))

            If cmdSansCodeABarre.ExecuteScalar = True Then
                Dim Qte As String = "0"
                If Convert.ToInt32(gArticles(I, "Qte")) Mod 2 = 0 Then
                    Qte = (Convert.ToInt32(gArticles(I, "Qte")) / 2).ToString()
                Else
                    Qte = (Convert.ToInt32((Convert.ToInt32(gArticles(I, "Qte") + 1) / 2))).ToString()
                End If


                ImprimerCodeBarre(gArticles(I, "CodeABarre"), gArticles(I, "Designation"), Qte, gArticles(I, "PrixVenteTTC"))
                AucunProduitSansCodeABarre = False
            End If
        Next

        If AucunProduitSansCodeABarre = True Then
            MsgBox("Aucun article dans la liste sans code à barre !", MsgBoxStyle.Information, "Information")
        End If
    End Sub



    Private Sub gArticles_BeforeRowColChange(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.CancelEventArgs) Handles gArticles.BeforeRowColChange
        Dim cmd As New SqlCommand
        Dim i As Integer

        retourDatePerm = False

        If Mode = "Consultation" Then
            Exit Sub
        End If

        If gArticles.Columns(gArticles.Col).DataField() = "DatePeremptionSaisie" Then
            Dim DatePerim As Date
            If gArticles.Columns("DatePeremptionSaisie").Text <> "" And gArticles.Columns("DatePeremptionSaisie").Text <> "__/__/____" Then
                Try
                    DatePerim = gArticles.Columns("DatePeremptionSaisie").Text
                Catch ex As Exception
                    MsgBox("Veuillez Saisir une date Valide", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Columns("DatePeremptionSaisie").Value = ""
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremptionSaisie"))
                    retourDatePerm = True
                    Exit Sub
                End Try

                If DatePerim < System.DateTime.Now Then
                    MsgBox("Date de péremption périmé !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.EditActive = False
                    gArticles.Columns("DatePeremptionSaisie").Value = ""
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremptionSaisie"))
                    gArticles.EditActive = False
                    retourDatePerm = True
                    Exit Sub
                End If
            End If

        End If
    End Sub

    Private Sub gArticles_RowColChange(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles gArticles.RowColChange
        If retourDatePerm = True Then
            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremptionSaisie"))
        End If
    End Sub

    Private Sub gArticles_DoubleClick(sender As Object, e As System.EventArgs) Handles gArticles.DoubleClick
        If (Not String.IsNullOrEmpty(gArticles.Columns("CodeArticle").Value)) Then
            'AfficherFicheArticle(gArticles.Columns("CodeArticle").Value, gArticles.Columns("Stock").Value, gArticles.Columns("Designation").Value)
                Dim stock As Integer
                Dim CodeArticle As String = gArticles.Columns("CodeArticle").Value
                Dim CodeABarre As String = ""
                Dim Qte As Integer = gArticles.Columns("Qte").Value

                stock = CalculeStock(gArticles.Columns("CodeArticle").Value)
                AfficherFicheArticle(gArticles.Columns("CodeArticle").Value, stock, gArticles.Columns("Designation").Value)

                Mode = "Ajout"

                cmdCodeABarre.Connection = ConnectionServeur
                cmdCodeABarre.CommandText = "SELECT CodeABarre FROM ARTICLE WHERE CodeArticle = " & Quote(CodeArticle)
                Try
                    CodeABarre = cmdCodeABarre.ExecuteScalar().ToString
                Catch ex As Exception
                End Try

                ModifierDetailArticle(CodeABarre)
                VerifierSiDejaSaisi(CodeArticle)
                gArticles.EditActive = True
                Exit Sub
            End If
    End Sub

    Private Sub gArticles_KeyPress(sender As Object, e As System.Windows.Forms.KeyPressEventArgs) Handles gArticles.KeyPress
        Try

            'If e.KeyChar = "" Or e.KeyChar = Chr(13) Or e.KeyChar = "." Or (Char.IsDigit(e.KeyChar) And (gArticles.Columns("Designation").Value = "" Or Char.IsDigit(gArticles.Columns("Designation").Value))) Then   '
            If e.KeyChar = "" Or e.KeyChar = Chr(13) Or e.KeyChar = "." And (gArticles.Columns("Designation").Value = "") Then   '
                Exit Sub
            End If

            Dim StrSQL As String = ""
            Dim ValeurAChercher As String = ""
            ValeurAChercher = gArticles.Columns("Designation").Value + e.KeyChar
            Try
                dsAchat.Tables("ARTICLE1").Clear()
            Catch ex As Exception

            End Try

            StrSQL = "SELECT CodeArticle," + _
                                 "Designation," + _
                                 "LibelleForme," + _
                                 "PrixVenteTTC " + _
                                 "FROM ARTICLE " + _
                                 "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                 "WHERE " + _
                                 " Designation LIKE " + Quote(ValeurAChercher + "%") + " OR ltrim(str(PrixVenteTTC,10,3)) LIKE " + Quote(ValeurAChercher + "%") + _
                                 " AND Supprime=0 ORDER BY Designation"

            cmdAchat.Connection = ConnectionServeur
            cmdAchat.CommandText = StrSQL
            daAchat = New SqlDataAdapter(cmdAchat)
            Try
                daAchat.Fill(dsAchat, "ARTICLE1")
            Catch ex As Exception
            End Try


            If dsAchat.Tables("ARTICLE1").Rows.Count = 0 And gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                e.Handled = True
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", " gArticles_KeyPress", ex.Message, "0000248", "Erreur d'exécution de gArticles_KeyPress ", True, True, True)

        End Try
    End Sub

    Private Sub Panel_Paint(sender As Object, e As PaintEventArgs) Handles Panel.Paint

    End Sub

    Private Sub GroupBox2_Enter(sender As Object, e As EventArgs) Handles GroupBox2.Enter

    End Sub

    Private Sub GroupeSituationFournisseur_Enter(sender As Object, e As EventArgs) Handles GroupeSituationFournisseur.Enter

    End Sub

    Private Sub gListeRecherche_Click(sender As Object, e As EventArgs) Handles gListeRecherche.Click

    End Sub

    Private Sub tNumeroBlFact_TextChanged(sender As Object, e As EventArgs) Handles tNumeroBlFact.TextChanged

    End Sub

    Private Sub dDateBlFacture_TextChanged(sender As Object, e As EventArgs) Handles dDateBlFacture.TextChanged

    End Sub
End Class
