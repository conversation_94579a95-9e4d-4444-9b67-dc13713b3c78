﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fPaiementMultiple
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fPaiementMultiple))
        Me.Panel = New System.Windows.Forms.Panel
        Me.gListeVente = New C1.Win.C1TrueDBGrid.C1TrueDBGrid
        Me.GroupBox5 = New System.Windows.Forms.GroupBox
        Me.lReste = New System.Windows.Forms.Label
        Me.Label12 = New System.Windows.Forms.Label
        Me.bSupprimerReglement = New C1.Win.C1Input.C1Button
        Me.lRendu = New System.Windows.Forms.Label
        Me.gReglement = New C1.Win.C1TrueDBGrid.C1TrueDBGrid
        Me.Label14 = New System.Windows.Forms.Label
        Me.lRenduAffiche = New System.Windows.Forms.Label
        Me.lSomme = New System.Windows.Forms.Label
        Me.GroupBox4 = New System.Windows.Forms.GroupBox
        Me.Label5 = New System.Windows.Forms.Label
        Me.lNetAPayer = New System.Windows.Forms.Label
        Me.lOrdonnanceEnCours = New System.Windows.Forms.Label
        Me.lOrdonnancePrecedentes = New System.Windows.Forms.Label
        Me.Label7 = New System.Windows.Forms.Label
        Me.Label2 = New System.Windows.Forms.Label
        Me.Label8 = New System.Windows.Forms.Label
        Me.GroupBox3 = New System.Windows.Forms.GroupBox
        Me.bImprimer = New C1.Win.C1Input.C1Button
        Me.C1Button1 = New C1.Win.C1Input.C1Button
        Me.chbOrdonnancier = New System.Windows.Forms.CheckBox
        Me.Clignote = New System.Windows.Forms.PictureBox
        Me.PictureBox1 = New System.Windows.Forms.PictureBox
        Me.tMotPasse = New C1.Win.C1Input.C1TextBox
        Me.bAnnuler = New C1.Win.C1Input.C1Button
        Me.bOk = New C1.Win.C1Input.C1Button
        Me.GroupBox1 = New System.Windows.Forms.GroupBox
        Me.cmbBanque = New C1.Win.C1List.C1Combo
        Me.tEcheance = New C1.Win.C1Input.C1DateEdit
        Me.Label1 = New System.Windows.Forms.Label
        Me.tNumeroCheque = New C1.Win.C1Input.C1TextBox
        Me.bAjouter = New C1.Win.C1Input.C1Button
        Me.Label10 = New System.Windows.Forms.Label
        Me.tMontant = New C1.Win.C1Input.C1TextBox
        Me.Label9 = New System.Windows.Forms.Label
        Me.Label6 = New System.Windows.Forms.Label
        Me.tNom = New C1.Win.C1Input.C1TextBox
        Me.Label4 = New System.Windows.Forms.Label
        Me.Label3 = New System.Windows.Forms.Label
        Me.cmbModePaiement = New C1.Win.C1List.C1Combo
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.SerialPortTiroir = New System.IO.Ports.SerialPort(Me.components)
        Me.Panel.SuspendLayout()
        CType(Me.gListeVente, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox5.SuspendLayout()
        CType(Me.gReglement, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox4.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        CType(Me.Clignote, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tMotPasse, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.cmbBanque, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tEcheance, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNumeroCheque, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tMontant, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNom, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbModePaiement, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.gListeVente)
        Me.Panel.Controls.Add(Me.GroupBox5)
        Me.Panel.Controls.Add(Me.GroupBox4)
        Me.Panel.Controls.Add(Me.GroupBox3)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(708, 465)
        Me.Panel.TabIndex = 0
        '
        'gListeVente
        '
        Me.gListeVente.GroupByCaption = "Drag a column header here to group by that column"
        Me.gListeVente.Images.Add(CType(resources.GetObject("gListeVente.Images"), System.Drawing.Image))
        Me.gListeVente.Location = New System.Drawing.Point(197, 80)
        Me.gListeVente.Name = "gListeVente"
        Me.gListeVente.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gListeVente.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gListeVente.PreviewInfo.ZoomFactor = 75
        Me.gListeVente.PrintInfo.PageSettings = CType(resources.GetObject("gListeVente.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gListeVente.Size = New System.Drawing.Size(237, 131)
        Me.gListeVente.TabIndex = 1
        Me.gListeVente.Text = "C1TrueDBGrid1"
        Me.gListeVente.Visible = False
        Me.gListeVente.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gListeVente.PropBag = resources.GetString("gListeVente.PropBag")
        '
        'GroupBox5
        '
        Me.GroupBox5.Controls.Add(Me.lReste)
        Me.GroupBox5.Controls.Add(Me.Label12)
        Me.GroupBox5.Controls.Add(Me.bSupprimerReglement)
        Me.GroupBox5.Controls.Add(Me.lRendu)
        Me.GroupBox5.Controls.Add(Me.gReglement)
        Me.GroupBox5.Controls.Add(Me.Label14)
        Me.GroupBox5.Controls.Add(Me.lRenduAffiche)
        Me.GroupBox5.Controls.Add(Me.lSomme)
        Me.GroupBox5.Location = New System.Drawing.Point(6, 91)
        Me.GroupBox5.Name = "GroupBox5"
        Me.GroupBox5.Size = New System.Drawing.Size(693, 200)
        Me.GroupBox5.TabIndex = 1
        Me.GroupBox5.TabStop = False
        Me.GroupBox5.Text = "Liste des règlements"
        '
        'lReste
        '
        Me.lReste.AutoSize = True
        Me.lReste.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lReste.Location = New System.Drawing.Point(261, 168)
        Me.lReste.Name = "lReste"
        Me.lReste.Size = New System.Drawing.Size(54, 20)
        Me.lReste.TabIndex = 22
        Me.lReste.Text = "0.000"
        '
        'Label12
        '
        Me.Label12.AutoSize = True
        Me.Label12.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label12.Location = New System.Drawing.Point(202, 168)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(50, 16)
        Me.Label12.TabIndex = 21
        Me.Label12.Text = "Reste :"
        '
        'bSupprimerReglement
        '
        Me.bSupprimerReglement.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimerReglement.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSupprimerReglement.Location = New System.Drawing.Point(599, 152)
        Me.bSupprimerReglement.Name = "bSupprimerReglement"
        Me.bSupprimerReglement.Size = New System.Drawing.Size(84, 42)
        Me.bSupprimerReglement.TabIndex = 17
        Me.bSupprimerReglement.Text = "Supprimer"
        Me.bSupprimerReglement.UseVisualStyleBackColor = True
        Me.bSupprimerReglement.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lRendu
        '
        Me.lRendu.AutoSize = True
        Me.lRendu.Font = New System.Drawing.Font("Microsoft Sans Serif", 20.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lRendu.Location = New System.Drawing.Point(481, 160)
        Me.lRendu.Name = "lRendu"
        Me.lRendu.Size = New System.Drawing.Size(87, 31)
        Me.lRendu.TabIndex = 20
        Me.lRendu.Text = "0.000"
        Me.lRendu.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'gReglement
        '
        Me.gReglement.GroupByCaption = "Drag a column header here to group by that column"
        Me.gReglement.Images.Add(CType(resources.GetObject("gReglement.Images"), System.Drawing.Image))
        Me.gReglement.Location = New System.Drawing.Point(7, 18)
        Me.gReglement.Name = "gReglement"
        Me.gReglement.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gReglement.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gReglement.PreviewInfo.ZoomFactor = 75
        Me.gReglement.PrintInfo.PageSettings = CType(resources.GetObject("gReglement.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gReglement.Size = New System.Drawing.Size(680, 131)
        Me.gReglement.TabIndex = 0
        Me.gReglement.Text = "C1TrueDBGrid1"
        Me.gReglement.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gReglement.PropBag = resources.GetString("gReglement.PropBag")
        '
        'Label14
        '
        Me.Label14.AutoSize = True
        Me.Label14.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label14.Location = New System.Drawing.Point(11, 168)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(61, 16)
        Me.Label14.TabIndex = 17
        Me.Label14.Text = "Somme :"
        '
        'lRenduAffiche
        '
        Me.lRenduAffiche.AutoSize = True
        Me.lRenduAffiche.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lRenduAffiche.Location = New System.Drawing.Point(393, 162)
        Me.lRenduAffiche.Name = "lRenduAffiche"
        Me.lRenduAffiche.Size = New System.Drawing.Size(87, 25)
        Me.lRenduAffiche.TabIndex = 19
        Me.lRenduAffiche.Text = "Rendu :"
        Me.lRenduAffiche.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lSomme
        '
        Me.lSomme.AutoSize = True
        Me.lSomme.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lSomme.Location = New System.Drawing.Point(80, 168)
        Me.lSomme.Name = "lSomme"
        Me.lSomme.Size = New System.Drawing.Size(54, 20)
        Me.lSomme.TabIndex = 18
        Me.lSomme.Text = "0.000"
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.Label5)
        Me.GroupBox4.Controls.Add(Me.lNetAPayer)
        Me.GroupBox4.Controls.Add(Me.lOrdonnanceEnCours)
        Me.GroupBox4.Controls.Add(Me.lOrdonnancePrecedentes)
        Me.GroupBox4.Controls.Add(Me.Label7)
        Me.GroupBox4.Controls.Add(Me.Label2)
        Me.GroupBox4.Controls.Add(Me.Label8)
        Me.GroupBox4.Location = New System.Drawing.Point(6, 4)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(693, 84)
        Me.GroupBox4.TabIndex = 0
        Me.GroupBox4.TabStop = False
        '
        'Label5
        '
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.Location = New System.Drawing.Point(184, 63)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(35, 13)
        Me.Label5.TabIndex = 16
        Me.Label5.Text = "Vente"
        '
        'lNetAPayer
        '
        Me.lNetAPayer.Font = New System.Drawing.Font("Microsoft Sans Serif", 24.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNetAPayer.ForeColor = System.Drawing.Color.Red
        Me.lNetAPayer.Location = New System.Drawing.Point(440, 28)
        Me.lNetAPayer.Name = "lNetAPayer"
        Me.lNetAPayer.Size = New System.Drawing.Size(245, 39)
        Me.lNetAPayer.TabIndex = 15
        Me.lNetAPayer.Text = "0.000"
        Me.lNetAPayer.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lOrdonnanceEnCours
        '
        Me.lOrdonnanceEnCours.Font = New System.Drawing.Font("Microsoft Sans Serif", 24.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lOrdonnanceEnCours.ForeColor = System.Drawing.Color.Red
        Me.lOrdonnanceEnCours.Location = New System.Drawing.Point(222, 28)
        Me.lOrdonnanceEnCours.Name = "lOrdonnanceEnCours"
        Me.lOrdonnanceEnCours.Size = New System.Drawing.Size(208, 39)
        Me.lOrdonnanceEnCours.TabIndex = 14
        Me.lOrdonnanceEnCours.Text = "0.000"
        Me.lOrdonnanceEnCours.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lOrdonnancePrecedentes
        '
        Me.lOrdonnancePrecedentes.Font = New System.Drawing.Font("Microsoft Sans Serif", 24.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lOrdonnancePrecedentes.ForeColor = System.Drawing.Color.Red
        Me.lOrdonnancePrecedentes.Location = New System.Drawing.Point(11, 28)
        Me.lOrdonnancePrecedentes.Name = "lOrdonnancePrecedentes"
        Me.lOrdonnancePrecedentes.Size = New System.Drawing.Size(205, 39)
        Me.lOrdonnancePrecedentes.TabIndex = 13
        Me.lOrdonnancePrecedentes.Text = "0.000"
        Me.lOrdonnancePrecedentes.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.Location = New System.Drawing.Point(529, 11)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(73, 13)
        Me.Label7.TabIndex = 12
        Me.Label7.Text = "Net à payer"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(269, 11)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(122, 13)
        Me.Label2.TabIndex = 10
        Me.Label2.Text = "Ordonnace en cours"
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.Location = New System.Drawing.Point(42, 11)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(144, 13)
        Me.Label8.TabIndex = 8
        Me.Label8.Text = "Ordonnace Précédentes"
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.bImprimer)
        Me.GroupBox3.Controls.Add(Me.C1Button1)
        Me.GroupBox3.Controls.Add(Me.chbOrdonnancier)
        Me.GroupBox3.Controls.Add(Me.Clignote)
        Me.GroupBox3.Controls.Add(Me.PictureBox1)
        Me.GroupBox3.Controls.Add(Me.tMotPasse)
        Me.GroupBox3.Controls.Add(Me.bAnnuler)
        Me.GroupBox3.Controls.Add(Me.bOk)
        Me.GroupBox3.Location = New System.Drawing.Point(6, 386)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(693, 69)
        Me.GroupBox3.TabIndex = 1
        Me.GroupBox3.TabStop = False
        '
        'bImprimer
        '
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.Location = New System.Drawing.Point(602, 21)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(81, 34)
        Me.bImprimer.TabIndex = 13
        Me.bImprimer.Text = "Imprimer      F9"
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1Button1
        '
        Me.C1Button1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.C1Button1.Location = New System.Drawing.Point(283, 21)
        Me.C1Button1.Name = "C1Button1"
        Me.C1Button1.Size = New System.Drawing.Size(81, 34)
        Me.C1Button1.TabIndex = 11
        Me.C1Button1.Text = "Paiement simple"
        Me.C1Button1.UseVisualStyleBackColor = True
        Me.C1Button1.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'chbOrdonnancier
        '
        Me.chbOrdonnancier.AutoSize = True
        Me.chbOrdonnancier.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chbOrdonnancier.Location = New System.Drawing.Point(98, 42)
        Me.chbOrdonnancier.Name = "chbOrdonnancier"
        Me.chbOrdonnancier.Size = New System.Drawing.Size(175, 17)
        Me.chbOrdonnancier.TabIndex = 9
        Me.chbOrdonnancier.Text = "F5 Inscription Sur Ordonnancier"
        Me.chbOrdonnancier.UseVisualStyleBackColor = True
        '
        'Clignote
        '
        Me.Clignote.BackColor = System.Drawing.SystemColors.Control
        Me.Clignote.Image = Global.Pharma2000Premium.My.Resources.Resources._1
        Me.Clignote.Location = New System.Drawing.Point(69, 39)
        Me.Clignote.Name = "Clignote"
        Me.Clignote.Size = New System.Drawing.Size(23, 22)
        Me.Clignote.TabIndex = 8
        Me.Clignote.TabStop = False
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Pharma2000Premium.My.Resources.Resources.cle11
        Me.PictureBox1.Location = New System.Drawing.Point(5, 10)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(56, 53)
        Me.PictureBox1.TabIndex = 7
        Me.PictureBox1.TabStop = False
        '
        'tMotPasse
        '
        Me.tMotPasse.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tMotPasse.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMotPasse.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tMotPasse.Location = New System.Drawing.Point(69, 10)
        Me.tMotPasse.Name = "tMotPasse"
        Me.tMotPasse.PasswordChar = Global.Microsoft.VisualBasic.ChrW(42)
        Me.tMotPasse.Size = New System.Drawing.Size(98, 24)
        Me.tMotPasse.TabIndex = 0
        Me.tMotPasse.Tag = Nothing
        Me.tMotPasse.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.tMotPasse.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tMotPasse.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAnnuler
        '
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Location = New System.Drawing.Point(518, 21)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(81, 34)
        Me.bAnnuler.TabIndex = 2
        Me.bAnnuler.Text = "Annuler        F10"
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bOk
        '
        Me.bOk.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bOk.Location = New System.Drawing.Point(434, 21)
        Me.bOk.Name = "bOk"
        Me.bOk.Size = New System.Drawing.Size(81, 34)
        Me.bOk.TabIndex = 1
        Me.bOk.Text = "OK                             F3"
        Me.bOk.UseVisualStyleBackColor = True
        Me.bOk.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.cmbBanque)
        Me.GroupBox1.Controls.Add(Me.tEcheance)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Controls.Add(Me.tNumeroCheque)
        Me.GroupBox1.Controls.Add(Me.bAjouter)
        Me.GroupBox1.Controls.Add(Me.Label10)
        Me.GroupBox1.Controls.Add(Me.tMontant)
        Me.GroupBox1.Controls.Add(Me.Label9)
        Me.GroupBox1.Controls.Add(Me.Label6)
        Me.GroupBox1.Controls.Add(Me.tNom)
        Me.GroupBox1.Controls.Add(Me.Label4)
        Me.GroupBox1.Controls.Add(Me.Label3)
        Me.GroupBox1.Controls.Add(Me.cmbModePaiement)
        Me.GroupBox1.Location = New System.Drawing.Point(6, 297)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(693, 90)
        Me.GroupBox1.TabIndex = 2
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Ajout d'un règlement"
        '
        'cmbBanque
        '
        Me.cmbBanque.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbBanque.Caption = ""
        Me.cmbBanque.CaptionHeight = 17
        Me.cmbBanque.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbBanque.ColumnCaptionHeight = 17
        Me.cmbBanque.ColumnFooterHeight = 17
        Me.cmbBanque.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.cmbBanque.ContentHeight = 15
        Me.cmbBanque.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbBanque.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbBanque.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbBanque.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbBanque.EditorHeight = 15
        Me.cmbBanque.Images.Add(CType(resources.GetObject("cmbBanque.Images"), System.Drawing.Image))
        Me.cmbBanque.ItemHeight = 15
        Me.cmbBanque.Location = New System.Drawing.Point(196, 55)
        Me.cmbBanque.MatchEntryTimeout = CType(2000, Long)
        Me.cmbBanque.MaxDropDownItems = CType(5, Short)
        Me.cmbBanque.MaxLength = 32767
        Me.cmbBanque.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbBanque.Name = "cmbBanque"
        Me.cmbBanque.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbBanque.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbBanque.Size = New System.Drawing.Size(110, 21)
        Me.cmbBanque.TabIndex = 18
        Me.cmbBanque.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbBanque.PropBag = resources.GetString("cmbBanque.PropBag")
        '
        'tEcheance
        '
        Me.tEcheance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tEcheance.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.tEcheance.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.tEcheance.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tEcheance.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tEcheance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.LongDate
        Me.tEcheance.EditFormat.Inherit = CType(((((C1.Win.C1Input.FormatInfoInheritFlags.CustomFormat Or C1.Win.C1Input.FormatInfoInheritFlags.NullText) _
                    Or C1.Win.C1Input.FormatInfoInheritFlags.EmptyAsNull) _
                    Or C1.Win.C1Input.FormatInfoInheritFlags.TrimStart) _
                    Or C1.Win.C1Input.FormatInfoInheritFlags.TrimEnd), C1.Win.C1Input.FormatInfoInheritFlags)
        Me.tEcheance.FormatType = C1.Win.C1Input.FormatTypeEnum.LongDate
        Me.tEcheance.Location = New System.Drawing.Point(197, 20)
        Me.tEcheance.Name = "tEcheance"
        Me.tEcheance.Size = New System.Drawing.Size(109, 18)
        Me.tEcheance.TabIndex = 17
        Me.tEcheance.Tag = Nothing
        Me.tEcheance.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tEcheance.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(307, 49)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(50, 32)
        Me.Label1.TabIndex = 16
        Me.Label1.Text = "Numero Chèque"
        '
        'tNumeroCheque
        '
        Me.tNumeroCheque.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNumeroCheque.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNumeroCheque.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNumeroCheque.Location = New System.Drawing.Point(360, 56)
        Me.tNumeroCheque.Name = "tNumeroCheque"
        Me.tNumeroCheque.Size = New System.Drawing.Size(92, 20)
        Me.tNumeroCheque.TabIndex = 4
        Me.tNumeroCheque.Tag = Nothing
        Me.tNumeroCheque.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNumeroCheque.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouter
        '
        Me.bAjouter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouter.Image = Global.Pharma2000Premium.My.Resources.Resources.nouveau
        Me.bAjouter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouter.Location = New System.Drawing.Point(596, 22)
        Me.bAjouter.Name = "bAjouter"
        Me.bAjouter.Size = New System.Drawing.Size(88, 52)
        Me.bAjouter.TabIndex = 6
        Me.bAjouter.Text = "Ajouter"
        Me.bAjouter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bAjouter.UseVisualStyleBackColor = True
        Me.bAjouter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label10.Location = New System.Drawing.Point(457, 21)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(130, 13)
        Me.Label10.TabIndex = 14
        Me.Label10.Text = "Montant du règlement"
        '
        'tMontant
        '
        Me.tMontant.AutoSize = False
        Me.tMontant.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tMontant.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMontant.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tMontant.Location = New System.Drawing.Point(456, 39)
        Me.tMontant.Name = "tMontant"
        Me.tMontant.Size = New System.Drawing.Size(131, 38)
        Me.tMontant.TabIndex = 5
        Me.tMontant.Tag = Nothing
        Me.tMontant.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.tMontant.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tMontant.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label9
        '
        Me.Label9.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.Location = New System.Drawing.Point(17, 29)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(109, 18)
        Me.Label9.TabIndex = 12
        Me.Label9.Text = "Mode de Paiement :"
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.Location = New System.Drawing.Point(321, 23)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(32, 13)
        Me.Label6.TabIndex = 11
        Me.Label6.Text = "Nom"
        '
        'tNom
        '
        Me.tNom.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNom.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNom.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNom.Location = New System.Drawing.Point(360, 20)
        Me.tNom.Name = "tNom"
        Me.tNom.Size = New System.Drawing.Size(92, 20)
        Me.tNom.TabIndex = 3
        Me.tNom.Tag = Nothing
        Me.tNom.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNom.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.Location = New System.Drawing.Point(139, 58)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(50, 13)
        Me.Label4.TabIndex = 9
        Me.Label4.Text = "Banque"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.Location = New System.Drawing.Point(134, 22)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(64, 13)
        Me.Label3.TabIndex = 8
        Me.Label3.Text = "Echéance"
        '
        'cmbModePaiement
        '
        Me.cmbModePaiement.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbModePaiement.Caption = ""
        Me.cmbModePaiement.CaptionHeight = 17
        Me.cmbModePaiement.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbModePaiement.ColumnCaptionHeight = 17
        Me.cmbModePaiement.ColumnFooterHeight = 17
        Me.cmbModePaiement.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.cmbModePaiement.ContentHeight = 15
        Me.cmbModePaiement.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbModePaiement.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbModePaiement.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbModePaiement.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbModePaiement.EditorHeight = 15
        Me.cmbModePaiement.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbModePaiement.Images.Add(CType(resources.GetObject("cmbModePaiement.Images"), System.Drawing.Image))
        Me.cmbModePaiement.ItemHeight = 15
        Me.cmbModePaiement.Location = New System.Drawing.Point(14, 50)
        Me.cmbModePaiement.MatchEntryTimeout = CType(2000, Long)
        Me.cmbModePaiement.MaxDropDownItems = CType(5, Short)
        Me.cmbModePaiement.MaxLength = 32767
        Me.cmbModePaiement.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbModePaiement.Name = "cmbModePaiement"
        Me.cmbModePaiement.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbModePaiement.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbModePaiement.Size = New System.Drawing.Size(117, 21)
        Me.cmbModePaiement.TabIndex = 0
        Me.cmbModePaiement.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbModePaiement.PropBag = resources.GetString("cmbModePaiement.PropBag")
        '
        'Timer1
        '
        '
        'fPaiementMultiple
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(708, 465)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fPaiementMultiple"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        CType(Me.gListeVente, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox5.ResumeLayout(False)
        Me.GroupBox5.PerformLayout()
        CType(Me.gReglement, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        CType(Me.Clignote, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tMotPasse, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.cmbBanque, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tEcheance, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNumeroCheque, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tMontant, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNom, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbModePaiement, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents cmbModePaiement As C1.Win.C1List.C1Combo
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bOk As C1.Win.C1Input.C1Button
    Friend WithEvents tMotPasse As C1.Win.C1Input.C1TextBox
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Timer1 As System.Windows.Forms.Timer
    Friend WithEvents Clignote As System.Windows.Forms.PictureBox
    Friend WithEvents chbOrdonnancier As System.Windows.Forms.CheckBox
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents tNom As C1.Win.C1Input.C1TextBox
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents GroupBox5 As System.Windows.Forms.GroupBox
    Friend WithEvents lNetAPayer As System.Windows.Forms.Label
    Friend WithEvents lOrdonnanceEnCours As System.Windows.Forms.Label
    Friend WithEvents lOrdonnancePrecedentes As System.Windows.Forms.Label
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents tMontant As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents bAjouter As C1.Win.C1Input.C1Button
    Friend WithEvents gReglement As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents tNumeroCheque As C1.Win.C1Input.C1TextBox
    Friend WithEvents gListeVente As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents lRendu As System.Windows.Forms.Label
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents lRenduAffiche As System.Windows.Forms.Label
    Friend WithEvents lSomme As System.Windows.Forms.Label
    Friend WithEvents C1Button1 As C1.Win.C1Input.C1Button
    Friend WithEvents bSupprimerReglement As C1.Win.C1Input.C1Button
    Friend WithEvents tEcheance As C1.Win.C1Input.C1DateEdit
    Friend WithEvents cmbBanque As C1.Win.C1List.C1Combo
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
    Friend WithEvents SerialPortTiroir As System.IO.Ports.SerialPort
    Friend WithEvents lReste As System.Windows.Forms.Label
    Friend WithEvents Label12 As System.Windows.Forms.Label
End Class
