//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class ACHAT
    {
        public ACHAT()
        {
            this.ACHAT_DETAILS = new HashSet<ACHAT_DETAILS>();
            this.REGLEMENT_FOURNISSEUR_ACHAT = new HashSet<REGLEMENT_FOURNISSEUR_ACHAT>();
        }
    
        public string NumeroAchat { get; set; }
        public System.DateTime Date { get; set; }
        public decimal TotalHT { get; set; }
        public decimal TVA { get; set; }
        public decimal TotalTTC { get; set; }
        public decimal TotalRemise { get; set; }
        public decimal Timbre { get; set; }
        public string CodeFournisseur { get; set; }
        public string CodePersonnel { get; set; }
        public string Note { get; set; }
        public string NumeroBL_Facture { get; set; }
        public string LibellePoste { get; set; }
        public System.DateTime DateBlFacture { get; set; }
        public decimal ValeurVenteTTC { get; set; }
        public decimal Autre { get; set; }
    
        public virtual ICollection<ACHAT_DETAILS> ACHAT_DETAILS { get; set; }
        public virtual ICollection<REGLEMENT_FOURNISSEUR_ACHAT> REGLEMENT_FOURNISSEUR_ACHAT { get; set; }
    }
}
