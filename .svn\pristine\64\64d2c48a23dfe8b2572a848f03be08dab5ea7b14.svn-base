﻿Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports System.IO.Ports

Public Class fInventaireLectureTerminal
    Dim cmdChargement As New SqlCommand
    Dim cbChargement As New SqlCommandBuilder
    Dim dsChargement As New DataSet
    Dim daChargement As New SqlDataAdapter
    Public cmdInventaire As New SqlCommand
    Public cbInventaire As New SqlCommandBuilder
    Public dsInventaire As New DataSet
    Public daInventaire As New SqlDataAdapter
    Public Mode As String = ""
    Public NumeroInventaire As String = ""
    Public ValeurActuelleAchatTTC As Double = 0.0
    Public ValeurActuelleVenteTTC As Double = 0.0
    Public ValeurInitialeAchatTTC As Double = 0.0
    Public ValeurInitialeVenteTTC As Double = 0.0
    Public DifferenceAchatTTC As Double = 0.0
    Public DifferenceVenteTTC As Double = 0.0
    Public DifferenceAchatTTCPourcentage As Double = 0.0
    Public DifferenceVenteTTCPourcentage As Double = 0.0

    '------------------------------------------------- les informations de paiement (retour de fenetre paiement)
    Public Operateur As Integer = 0
    Public NouvelleAchat As DataRow = Nothing 'datarow pour charger l'entête dans la datatable ACHAT
    Public NouvelArticle As DataRow = Nothing 'datarow pour charger les détails dans la datatable ACHAT_DETAIL_AFFICHE
    Public NouvelArticleEnregistrement As DataRow = Nothing 'datarow pour charger les détails dans la datatable ACHAT_DETAIL
    Public dr As DataRow = Nothing 'datarow pour charger dans la datatable ARTICLE qui contient les articles recherchés pendant la saisie de la designation
    'Public tableLotArticle As String = "LOT_ARTICLE_DETAIL"
    Dim ConfirmerEnregistrer As Boolean = False
    Dim CodeOperateur As String = ""
    Public CodeArticleAjout As String
    Public DesignationAjout As String
    Public PrixAchatAjout As String
    Public PrixVenteAjout As String
    Public CodeFormeAjout As String
    Public CodeRayonAjout As String

    'Public ZoneTerminal As String

    Dim vRecupereStockActuel As String

    Public Valider As Boolean = False

    Public Sub ToucheDeFonction(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.F4 And bSupprimeArticle.Enabled = True Then
            bSupprimeArticle_Click(sender, e)
        End If
        If e.KeyCode = Keys.F1 And bAjoutArticle.Enabled = True Then
            bAjoutArticle_Click(sender, e)
        End If
        If e.KeyCode = Keys.F3 And bValider.Enabled = True Then
            bValider_Click(sender, e)
        End If

        If e.KeyCode = Keys.F5 And bLire.Enabled = True Then
            bLire_Click(sender, e)
        End If
        If e.KeyCode = Keys.F12 And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If

    End Sub

    Public Sub Init()


        If GetSetting("PHARMA", "PHARMA", "TypeTerminal", "") = "PORT_SERIE" Then

            VitessePort = GetSetting("PHARMA", "PHARMA", "VitessePort", "")
            DataBitPort = GetSetting("PHARMA", "PHARMA", "DataBitPort", "")
            StopBitPort = GetSetting("PHARMA", "PHARMA", "StopBitPort", "")
            PartiyPort = GetSetting("PHARMA", "PHARMA", "PartiyPort", "")
            ProtocolePort = GetSetting("PHARMA", "PHARMA", "ProtocolePort", "Aucune")
            NomPort = GetSetting("PHARMA", "PHARMA", "NomPort", "")

            Try

                PortSerie.BaudRate = VitessePort
                PortSerie.DataBits = DataBitPort
                PortSerie.StopBits = StopBitPort
                If PartiyPort = "Paire" Then
                    PortSerie.Parity = Ports.Parity.Even
                ElseIf PartiyPort = "Impaire" Then
                    PortSerie.Parity = Ports.Parity.Odd
                Else
                    PortSerie.Parity = Ports.Parity.None
                End If
                If ProtocolePort = "Aucune" Or ProtocolePort = "AUCUNE" Then
                    PortSerie.Handshake = Ports.Handshake.None
                End If
                PortSerie.PortName = NomPort

            Catch ex As Exception
                MsgBox("Merci de remplir les paramétres du terminal dans Paramètres Généreaux !" + ex.Message)
                fMain.Tab.SelectedTab.Dispose()
            End Try

            Try
                PortSerie.Open()
            Catch ex As Exception
                MsgBox("Erreur d'ouverture de port série " + ex.Message)
                fMain.Tab.SelectedTab.Dispose()
            End Try

        ElseIf GetSetting("PHARMA", "PHARMA", "TypeTerminal", "") = "FICHIER_TEXTE" Then
            bLire.Text = "Sélectionnez le fichier   F5"
        ElseIf GetSetting("PHARMA", "PHARMA", "TypeTerminal", "") = "RESEAU" Then
        Else
            MsgBox("Merci de remplir les paramétres du terminal dans Paramètres Généreaux !", MsgBoxStyle.Information)
            fMain.Tab.SelectedTab.Dispose()
        End If



        bAjoutArticle.Enabled = False
        bSupprimeArticle.Enabled = False

    End Sub
    Private Sub bLire_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLire.Click
        Dim StrSQL As String = ""
        Dim i As Integer = 0
        Dim J As Integer = 0
        Dim Cond As String = ""
        Dim unite As Double = 0.0

        If Mode = "Inventaire" Then

            '------------------------------ préparation des datatables vides 
            If (dsInventaire.Tables.IndexOf("INVENTAIRE_DETAILS_AFFICHE") > -1) Then
                dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Clear()
            End If
            If (dsInventaire.Tables.IndexOf("INVENTAIRE") > -1) Then
                dsInventaire.Tables("INVENTAIRE").Clear()
            End If
            If (dsInventaire.Tables.IndexOf("INVENTAIRE_DETAILS") > -1) Then
                dsInventaire.Tables("INVENTAIRE_DETAILS").Clear()
            End If

            'chargement des Entêtes des inventaires 
            StrSQL = "SELECT TOP (0) * FROM INVENTAIRE ORDER BY NumeroInventaire ASC"
            cmdInventaire.Connection = ConnectionServeur
            cmdInventaire.CommandText = StrSQL
            daInventaire = New SqlDataAdapter(cmdInventaire)
            daInventaire.Fill(dsInventaire, "INVENTAIRE")
            cbInventaire = New SqlCommandBuilder(daInventaire)

            '************************** création d'une table INVENTAIRE_DETAILS qui doit etre une copie de celle de
            '************************** la base de données et qui contient chaque article avec les lots disponible
            '************************** chaque un dans un enregistrement seul, c'est sur cette table qu'on doit appliquer 
            '************************** la methode update du dataAdapter
            StrSQL = "SELECT NumeroInventaire," + _
                     "INVENTAIRE_DETAILS.CodeArticle," + _
                     "INVENTAIRE_DETAILS.CodeABarre," + _
                     "INVENTAIRE_DETAILS.Designation," + _
                     "NumeroLot," + _
                     "LibelleForme," + _
                     "INVENTAIRE_DETAILS.CodeForme," + _
                     "INVENTAIRE_DETAILS.Rayon," + _
                     "INVENTAIRE_DETAILS.StockInitial," + _
                     "StockActuel," + _
                     "INVENTAIRE_DETAILS.PrixAchatTTC," + _
                     "INVENTAIRE_DETAILS.TotalAchatTTC," + _
                     "INVENTAIRE_DETAILS.PrixVenteTTC, " + _
                     "'' AS QteChange, " + _
                     "'' AS DatePeremptionArticle " + _
                     "FROM " + _
                     "INVENTAIRE_DETAILS " + _
                     "LEFT OUTER JOIN ARTICLE ON INVENTAIRE_DETAILS.CodeArticle=ARTICLE.CodeArticle " + _
                     "LEFT OUTER JOIN FORME_ARTICLE ON INVENTAIRE_DETAILS.CodeForme = FORME_ARTICLE.CodeForme " + _
                     "WHERE NumeroInventaire ='0' AND ARTICLE.supprime = 0 "

            cmdInventaire.Connection = ConnectionServeur
            cmdInventaire.CommandText = StrSQL
            daInventaire.Fill(dsInventaire, "INVENTAIRE_DETAILS")
            cbInventaire = New SqlCommandBuilder(daInventaire)

            'ajout d'un nouvel enregistrement vide dans les datatables convenables
            NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
            NouvelArticleEnregistrement("Designation") = ""
            NouvelArticleEnregistrement("CodeArticle") = ""
            NouvelArticleEnregistrement("NumeroLot") = ""
            dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)

            'chargement des détails des inventaires juste une table pour formater la gride par les colonnes (vide)
            StrSQL = " SELECT TOP (0) NumeroInventaire," + _
                     " INVENTAIRE_DETAILS.CodeArticle," + _
                     " '' AS CodeABarre, " + _
                     " MAX(INVENTAIRE_DETAILS.Designation) AS Designation," + _
                     " MAX(LibelleForme) AS LibelleForme," + _
                     " MAX(INVENTAIRE_DETAILS.CodeForme) AS CodeForme," + _
                     " MAX(INVENTAIRE_DETAILS.Rayon) AS Rayon," + _
                     " SUM(INVENTAIRE_DETAILS.StockInitial) as StockInitial," + _
                     " SUM(StockActuel)as StockActuel," + _
                     " MAX(INVENTAIRE_DETAILS.PrixAchatTTC) AS PrixAchatTTC," + _
                     " SUM(INVENTAIRE_DETAILS.TotalAchatTTC) AS TotalAchatTTC," + _
                     " MAX(INVENTAIRE_DETAILS.PrixVenteTTC) AS PrixVenteTTC ," + _
                     " '' AS QteChange " + _
                     " FROM " + _
                     " INVENTAIRE_DETAILS " + _
                     " LEFT OUTER JOIN ARTICLE ON INVENTAIRE_DETAILS.CodeArticle=ARTICLE.CodeArticle " + _
                     " LEFT OUTER JOIN FORME_ARTICLE ON INVENTAIRE_DETAILS.CodeForme = FORME_ARTICLE.CodeForme " + _
                     " GROUP BY NumeroInventaire,INVENTAIRE_DETAILS.CodeArticle "

            '" SUM(StockActuel)as DifferenceStock," + _

            cmdInventaire.Connection = ConnectionServeur
            cmdInventaire.CommandText = StrSQL
            daInventaire.Fill(dsInventaire, "INVENTAIRE_DETAILS_AFFICHE")
            cbInventaire = New SqlCommandBuilder(daInventaire)
            With gInventaire
                .Columns.Clear()
                .DataSource = dsInventaire
                .DataMember = "INVENTAIRE_DETAILS_AFFICHE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code article"
                .Columns("CodeABarre").Caption = "Code Article" '"Code A Barre"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Rayon").Caption = "Rayon"
                .Columns("StockInitial").Caption = "Stock Théorique" '"Stock Initial"
                .Columns("StockActuel").Caption = "Quantité lue" '"Stock Réel" '"Nouveau Stock"
                '.Columns("DifferenceStock").Caption = "Difference Stock"
                .Columns("PrixAchatTTC").Caption = "PU Achat" '"Prix achat TTC"
                .Columns("TotalAchatTTC").Caption = "Valeur Achat Total" '"Total Achat TTC"
                .Columns("QteChange").Caption = "QteChange"
                .Columns("QteChange").DataField = ""
                ' Centrer toutes les entêtes
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Locked = True
                Next
                .FilterBar = True
                .Splits(0).DisplayColumns("NumeroInventaire").Width = 0
                .Splits(0).DisplayColumns("NumeroInventaire").Visible = False
                .Splits(0).DisplayColumns("PrixAchatTTC").Visible = False
                .Splits(0).DisplayColumns("TotalAchatTTC").Visible = False

                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                '.Splits(0).DisplayColumns("Rayon").Visible = False
                '.Splits(0).DisplayColumns("StockInitial").Visible = False
                '.Splits(0).DisplayColumns("StockActuel").Visible = False

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                '.Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
                .Splits(0).DisplayColumns("CodeArticle").Width = 100
                .Splits(0).DisplayColumns("Designation").Width = 320
                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
                .Splits(0).DisplayColumns("LibelleForme").Width = 80
                .Splits(0).DisplayColumns("Rayon").Width = 80
                .Splits(0).DisplayColumns("CodeForme").Width = 0
                .Splits(0).DisplayColumns("StockInitial").Width = 80
                .Splits(0).DisplayColumns("StockActuel").Width = 80
                .Splits(0).DisplayColumns("PrixAchatTTC").Width = 80
                '.Columns("PrixAchatTTC").NumberFormat = "#,###" + NombreDeChiffreApresVirgule()
                .Splits(0).DisplayColumns("TotalAchatTTC").Width = 80
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 0
                .Splits(0).DisplayColumns("NumeroInventaire").Visible = False
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = False
                .Splits(0).DisplayColumns("CodeForme").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("StockActuel").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("QteChange").Visible = False
                .Splits(0).DisplayColumns("LibelleForme").Visible = False
                .Splits(0).DisplayColumns("Rayon").Visible = False
                '.Splits(0).DisplayColumns("StockInitial").Visible = False
                .Splits(0).DisplayColumns("PrixAchatTTC").Visible = False
                .Splits(0).DisplayColumns("TotalAchatTTC").Visible = False

                '.Splits(0).DisplayColumns("StockActuel").Locked = False

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                ParametreGrid(gInventaire)
            End With
            bLire.Enabled = False


            '''''''''''''''''''''' DEBUT TERMINAL ''''''''''''''''''''''''
            Dim Line As String = ""
            Dim LineCodeABarre As String ' CodeArticle
            Dim LineCodeABarreOld As String ' CodeABarre
            Dim LineQuantite As String = ""
            Dim LineDesignation As String
            Dim LineQuantiteTest As String
            Dim test As Integer = 0
            Dim Car As Byte
            Dim nbre As Integer = 0
            Dim testQuantie As Boolean = True
            Dim TestEntete As Boolean = False
            Dim QteTotal As Integer = 0
            Dim testlotvide As Integer = 0

            Line = ""
            TestEntete = False

            If GetSetting("PHARMA", "PHARMA", "TypeTerminal", "") = "PORT_SERIE" Then

                While PortSerie.BytesToRead
                    Car = PortSerie.ReadByte
                    If Chr(Car) = "[" Then
                        nbre += 1
                    End If
                    Line += Chr(Car)
                    If nbre = 2 Then
                        Line = ""
                        nbre = 1000
                        Car = PortSerie.ReadByte
                        Line += Chr(Car)
                        While PortSerie.BytesToRead

                            If Chr(Car) = ">" Then
                                LineCodeABarre = Replace(Line, ">", "")
                                'boucle qte
                                Car = PortSerie.ReadByte
boucleQuantite2:
                                'Car = PortSerie.ReadByte
                                If Chr(Car) = "]" Then
                                    LineQuantite = ""
                                    testQuantie = True
                                    While testQuantie = True
                                        Car = PortSerie.ReadByte
                                        LineQuantiteTest = LineQuantite + Chr(Car)
                                        If IsNumeric(LineQuantiteTest) Then
                                            LineQuantite = LineQuantiteTest
                                            'Car = PortSerie.ReadByte
                                        Else
                                            testQuantie = False
                                            TestEntete = True
                                        End If
                                    End While
                                ElseIf Car = 23 Then
                                    LineQuantite = "1"
                                    TestEntete = True
                                    Car = PortSerie.ReadByte
                                    'GoTo boucleTEST
                                Else
                                    If PortSerie.BytesToRead > 0 Then
                                        Car = PortSerie.ReadByte
                                        GoTo boucleQuantite2
                                    Else
                                        'Exit While
                                    End If
                                End If
                                'If LineQuantite = "" Then

                                'End If
                                '''''''''''' DATATABLE INVENTAIE_DETAIL_AFFICHE ''''''''''''''''''''
                                LineCodeABarreOld = LineCodeABarre
                                StrSQL = "SELECT CodeArticle FROM ARTICLE WHERE Supprime = 0 AND (CodeArticle = " + Quote(LineCodeABarre) + " OR CodeABarre = " + Quote(LineCodeABarre) + ")"
                                cmdChargement.Connection = ConnectionServeur
                                cmdChargement.CommandText = StrSQL
                                If LineQuantite <> "0" Then
                                    Try
                                        LineCodeABarre = cmdChargement.ExecuteScalar().ToString

                                        LineDesignation = RecupererValeurExecuteScalaire("Designation", "ARTICLE", "CodeArticle", LineCodeABarre)
                                        NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                                        NouvelArticle("NumeroInventaire") = RecupereNumuero()
                                        NouvelArticle("CodeArticle") = LineCodeABarre
                                        NouvelArticle("CodeABarre") = LineCodeABarreOld
                                        NouvelArticle("Designation") = LineDesignation

                                        NouvelArticle("StockActuel") = LineQuantite

                                        StrSQL = "SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE CodeArticle = " + Quote(LineCodeABarre)
                                        cmdChargement.Connection = ConnectionServeur
                                        cmdChargement.CommandText = StrSQL
                                        If cmdChargement.ExecuteScalar() > 0 Then
                                            NouvelArticle("StockInitial") = cmdChargement.ExecuteScalar()
                                        Else
                                            NouvelArticle("StockInitial") = 0
                                        End If
                                        NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", LineCodeABarre)
                                        NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", "ARTICLE", "CodeArticle", LineCodeABarre)

                                        NouvelArticle("TotalAchatTTC") = NouvelArticle("PrixAchatTTC") * NouvelArticle("StockActuel")
                                        Try
                                            NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", LineCodeABarre))
                                        Catch ex As Exception
                                        End Try

                                        NouvelArticle("Rayon") = RecupererValeurExecuteScalaire("Rayon", "ARTICLE", "CodeArticle", LineCodeABarre)

                                        dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)
                                        If (dsInventaire.Tables.IndexOf("LOT_ARTICLE") > -1) Then
                                            dsInventaire.Tables("LOT_ARTICLE").Clear()
                                        End If
                                        If (dsInventaire.Tables.IndexOf("LOT_ARTICLE_COMPLET") > -1) Then
                                            dsInventaire.Tables("LOT_ARTICLE_COMPLET").Clear()
                                        End If
                                        '--------------- pour la table d'affichage somme des quantités une seule ligne par article
                                        StrSQL = "SELECT CodeArticle,SUM(QteLotArticle) as QteLotArticle FROM LOT_ARTICLE WHERE " + _
                                " CodeArticle = " + Quote(LineCodeABarre) + _
                                " GROUP BY CodeArticle"

                                        cmdInventaire.Connection = ConnectionServeur
                                        cmdInventaire.CommandText = StrSQL
                                        daInventaire = New SqlDataAdapter(cmdInventaire)
                                        daInventaire.Fill(dsInventaire, "LOT_ARTICLE")

                                        '--------------- pour la table d'enregistrement chaque lots dans une ligne
                                        StrSQL = "SELECT * FROM LOT_ARTICLE WHERE " + _
                                " CodeArticle = " + Quote(LineCodeABarre) + ""

                                        cmdInventaire.Connection = ConnectionServeur
                                        cmdInventaire.CommandText = StrSQL
                                        daInventaire = New SqlDataAdapter(cmdInventaire)
                                        daInventaire.Fill(dsInventaire, "LOT_ARTICLE_COMPLET")

                                        If dsInventaire.Tables("LOT_ARTICLE").Rows.Count > 0 Then

                                            '************ enregistrement dans la table INVENTAIRE_DETAILS (juste on ajoute le 
                                            '************ numero de lot et chaque lot dans une ligne)
                                            For J = 0 To dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows.Count - 1

                                                NouvelArticleEnregistrement("NumeroInventaire") = RecupereNumuero()
                                                NouvelArticleEnregistrement("CodeArticle") = LineCodeABarre
                                                NouvelArticleEnregistrement("CodeABarre") = LineCodeABarreOld
                                                NouvelArticleEnregistrement("Designation") = LineDesignation
                                                NouvelArticleEnregistrement("NumeroLot") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("NumeroLotArticle")

                                                Try
                                                    NouvelArticleEnregistrement("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", LineCodeABarre)
                                                Catch ex As Exception
                                                End Try

                                                NouvelArticleEnregistrement("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))


                                                NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", LineCodeABarre)
                                                NouvelArticleEnregistrement("StockInitial") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("QteLotArticle")

                                                NouvelArticleEnregistrement("StockActuel") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("QteLotArticle") ' ajouter


                                                ' NouvelArticleEnregistrement("StockActuel") = LineQuantite
                                                NouvelArticleEnregistrement("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", LineCodeABarre)
                                                NouvelArticleEnregistrement("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                                NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                                                NouvelArticleEnregistrement("Designation") = ""  'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                                                NouvelArticleEnregistrement("CodeArticle") = ""
                                                NouvelArticleEnregistrement("NumeroLot") = ""
                                                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)
                                            Next

                                            ''''''
                                            Try


                                                QteTotal = 0
                                                For l = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
                                                    If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("CodeArticle") <> "" Then
                                                        If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE").Rows(0).Item("CodeArticle") Then
                                                            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("NumeroLot") <> "" Then
                                                                QteTotal += dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("StockActuel")
                                                            End If
                                                        End If

                                                    End If
                                                Next
                                                For l = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
                                                    If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("CodeArticle") <> "" Then
                                                        If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE").Rows(0).Item("CodeArticle") Then
                                                            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("NumeroLot") = "" Then
                                                                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("StockActuel") = LineQuantite - QteTotal '+ dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("StockActuel")
                                                            End If
                                                        End If

                                                    End If
                                                Next
                                            Catch ex As Exception
                                                MsgBox(ex.Message)
                                            End Try

                                            For i = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
                                                If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") <> "" Then
                                                    If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE").Rows(0).Item("CodeArticle") Then
                                                        If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroLot") = "" Then
                                                            testlotvide = 0
                                                            Exit For
                                                        Else
                                                            testlotvide = 1
                                                        End If
                                                    End If
                                                End If
                                            Next

                                            If testlotvide = 1 Then
                                                NouvelArticleEnregistrement("NumeroInventaire") = RecupereNumuero()
                                                NouvelArticleEnregistrement("CodeArticle") = LineCodeABarre
                                                NouvelArticleEnregistrement("CodeABarre") = LineCodeABarreOld
                                                NouvelArticleEnregistrement("Designation") = LineDesignation
                                                NouvelArticleEnregistrement("NumeroLot") = ""
                                                Try
                                                    NouvelArticleEnregistrement("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", LineCodeABarre)
                                                    NouvelArticleEnregistrement("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticleEnregistrement("CodeForme"))
                                                Catch ex As Exception
                                                End Try


                                                NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", LineCodeABarre)

                                                NouvelArticleEnregistrement("StockInitial") = 0
                                                NouvelArticleEnregistrement("StockActuel") = LineQuantite - QteTotal
                                                NouvelArticleEnregistrement("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                                NouvelArticleEnregistrement("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                                NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                                                NouvelArticleEnregistrement("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                                                NouvelArticleEnregistrement("CodeArticle") = ""
                                                NouvelArticleEnregistrement("NumeroLot") = ""
                                                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)

                                            End If


                                        ElseIf dsInventaire.Tables("LOT_ARTICLE").Rows.Count = 0 Then

                                            '************ enregistrement dans la table INVENTAIRE_DETAILS (juste on ajoute le 
                                            '************ numero de lot)
                                            NouvelArticleEnregistrement("NumeroInventaire") = RecupereNumuero()
                                            NouvelArticleEnregistrement("CodeArticle") = LineCodeABarre
                                            NouvelArticleEnregistrement("CodeABarre") = LineCodeABarreOld
                                            NouvelArticleEnregistrement("Designation") = LineDesignation
                                            NouvelArticleEnregistrement("NumeroLot") = ""
                                            Try
                                                NouvelArticleEnregistrement("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", LineCodeABarre)
                                                NouvelArticleEnregistrement("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
                                            Catch ex As Exception
                                            End Try

                                            NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", LineCodeABarre)

                                            'NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("CodeRayon", " ARTICLE", "CodeArticle", LineCodeABarre)
                                            NouvelArticleEnregistrement("StockInitial") = 0
                                            NouvelArticleEnregistrement("StockActuel") = 0
                                            NouvelArticleEnregistrement("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                            NouvelArticleEnregistrement("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                            NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                                            NouvelArticleEnregistrement("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                                            NouvelArticleEnregistrement("CodeArticle") = ""
                                            NouvelArticleEnregistrement("NumeroLot") = ""
                                            dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)
                                        End If
                                    Catch ex As Exception
                                        Console.WriteLine(ex.Message)
                                        ' afficher code A Barre n existe pa                   
                                        NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                                        NouvelArticle("NumeroInventaire") = RecupereNumuero()
                                        NouvelArticle("CodeArticle") = ""
                                        NouvelArticle("CodeABarre") = LineCodeABarre
                                        NouvelArticle("Designation") = "ARTICLE INEXISTANT"
                                        NouvelArticle("StockActuel") = LineQuantite
                                        NouvelArticle("StockInitial") = 0
                                        'NouvelArticle("DifferenceStock") = LineQuantite - 0
                                        dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)
                                    End Try
                                End If

                                '''''''''''' DATATABLE INVENTAIE_DETAIL_AFFICHE ''''''''''''''''''''
                            End If
                            If PortSerie.BytesToRead > 0 Then
                                Car = PortSerie.ReadByte
                                If TestEntete = True Then
                                    Line = ""
                                    TestEntete = False
                                End If
                                Line += Chr(Car)
                                If Chr(Car) = "[" Then
                                    Line = ""
                                End If
                            End If
                        End While
                    End If
                End While
                PortSerie.Close()

            ElseIf GetSetting("PHARMA", "PHARMA", "TypeTerminal", "") = "FICHIER_TEXTE" Then

                Dim Chemin As String = ""
                Dim yLigne() As String

                Dim MyReceptionFichier As New fReceptionFichierTerminal
                MyReceptionFichier.ShowDialog()


                If MyReceptionFichier.FileName <> "" Then
                    Chemin = MyReceptionFichier.FileName

                    Dim FichierTerminal As New IO.StreamReader(Chemin)

                    Do
                        yLigne = FichierTerminal.ReadLine.Split(",")

                        LineCodeABarre = yLigne(0)
                        LineQuantite = yLigne(1)

                        '''''''''''' DATATABLE INVENTAIE_DETAIL_AFFICHE ''''''''''''''''''''
                        LineCodeABarreOld = LineCodeABarre

                        StrSQL = "SELECT CodeArticle FROM ARTICLE WHERE Supprime = 0 AND (CodeArticle = " + Quote(LineCodeABarre) + " OR CodeABarre = " + Quote(LineCodeABarre) + ")"
                        cmdChargement.Connection = ConnectionServeur
                        cmdChargement.CommandText = StrSQL

                        If LineQuantite <> "0" Then
                            Try
                                LineCodeABarre = cmdChargement.ExecuteScalar().ToString

                                LineDesignation = RecupererValeurExecuteScalaire("Designation", "ARTICLE", "CodeArticle", LineCodeABarre)
                                NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                                NouvelArticle("NumeroInventaire") = RecupereNumuero()
                                NouvelArticle("CodeArticle") = LineCodeABarre
                                NouvelArticle("CodeABarre") = LineCodeABarreOld
                                NouvelArticle("Designation") = LineDesignation

                                NouvelArticle("StockActuel") = LineQuantite

                                StrSQL = "SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE CodeArticle = " + Quote(LineCodeABarre)
                                cmdChargement.Connection = ConnectionServeur
                                cmdChargement.CommandText = StrSQL
                                If cmdChargement.ExecuteScalar() > 0 Then
                                    NouvelArticle("StockInitial") = cmdChargement.ExecuteScalar()
                                Else
                                    NouvelArticle("StockInitial") = 0
                                End If
                                NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", LineCodeABarre)
                                NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", "ARTICLE", "CodeArticle", LineCodeABarre)

                                NouvelArticle("TotalAchatTTC") = NouvelArticle("PrixAchatTTC") * NouvelArticle("StockActuel")
                                Try
                                    NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", LineCodeABarre))
                                Catch ex As Exception
                                End Try

                                NouvelArticle("Rayon") = RecupererValeurExecuteScalaire("Rayon", "ARTICLE", "CodeArticle", LineCodeABarre)

                                dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)
                                If (dsInventaire.Tables.IndexOf("LOT_ARTICLE") > -1) Then
                                    dsInventaire.Tables("LOT_ARTICLE").Clear()
                                End If
                                If (dsInventaire.Tables.IndexOf("LOT_ARTICLE_COMPLET") > -1) Then
                                    dsInventaire.Tables("LOT_ARTICLE_COMPLET").Clear()
                                End If
                                '--------------- pour la table d'affichage somme des quantités une seule ligne par article
                                StrSQL = " SELECT CodeArticle,SUM(QteLotArticle) as QteLotArticle FROM LOT_ARTICLE WHERE " + _
                                         " CodeArticle = " + Quote(LineCodeABarre) + _
                                         " GROUP BY CodeArticle"

                                cmdInventaire.Connection = ConnectionServeur
                                cmdInventaire.CommandText = StrSQL
                                daInventaire = New SqlDataAdapter(cmdInventaire)
                                daInventaire.Fill(dsInventaire, "LOT_ARTICLE")

                                '--------------- pour la table d'enregistrement chaque lots dans une ligne
                                StrSQL = " SELECT * FROM LOT_ARTICLE WHERE " + _
                                         " CodeArticle = " + Quote(LineCodeABarre) + ""

                                cmdInventaire.Connection = ConnectionServeur
                                cmdInventaire.CommandText = StrSQL
                                daInventaire = New SqlDataAdapter(cmdInventaire)
                                daInventaire.Fill(dsInventaire, "LOT_ARTICLE_COMPLET")

                                If dsInventaire.Tables("LOT_ARTICLE").Rows.Count > 0 Then

                                    '************ enregistrement dans la table INVENTAIRE_DETAILS (juste on ajoute le 
                                    '************ numero de lot et chaque lot dans une ligne)
                                    For J = 0 To dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows.Count - 1

                                        NouvelArticleEnregistrement("NumeroInventaire") = RecupereNumuero()
                                        NouvelArticleEnregistrement("CodeArticle") = LineCodeABarre
                                        NouvelArticleEnregistrement("CodeABarre") = LineCodeABarreOld
                                        NouvelArticleEnregistrement("Designation") = LineDesignation
                                        NouvelArticleEnregistrement("NumeroLot") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("NumeroLotArticle")

                                        Try
                                            NouvelArticleEnregistrement("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", LineCodeABarre)
                                        Catch ex As Exception
                                        End Try

                                        NouvelArticleEnregistrement("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))


                                        NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", LineCodeABarre)
                                        NouvelArticleEnregistrement("StockInitial") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("QteLotArticle")

                                        NouvelArticleEnregistrement("StockActuel") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("QteLotArticle") ' ajouter


                                        ' NouvelArticleEnregistrement("StockActuel") = LineQuantite
                                        NouvelArticleEnregistrement("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", LineCodeABarre)
                                        NouvelArticleEnregistrement("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                        NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                                        NouvelArticleEnregistrement("Designation") = ""  'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                                        NouvelArticleEnregistrement("CodeArticle") = ""
                                        NouvelArticleEnregistrement("NumeroLot") = ""
                                        dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)
                                    Next

                                    ''''''
                                    Try


                                        QteTotal = 0
                                        For l = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
                                            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("CodeArticle") <> "" Then
                                                If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE").Rows(0).Item("CodeArticle") Then
                                                    If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("NumeroLot") <> "" Then
                                                        QteTotal += dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("StockActuel")
                                                    End If
                                                End If

                                            End If
                                        Next
                                        For l = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
                                            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("CodeArticle") <> "" Then
                                                If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE").Rows(0).Item("CodeArticle") Then
                                                    If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("NumeroLot") = "" Then
                                                        dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("StockActuel") = LineQuantite - QteTotal '+ dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("StockActuel")
                                                    End If
                                                End If

                                            End If
                                        Next
                                    Catch ex As Exception
                                        MsgBox(ex.Message)
                                    End Try

                                    For i = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
                                        If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") <> "" Then
                                            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE").Rows(0).Item("CodeArticle") Then
                                                If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroLot") = "" Then
                                                    testlotvide = 0
                                                    Exit For
                                                Else
                                                    testlotvide = 1
                                                End If
                                            End If
                                        End If
                                    Next

                                    If testlotvide = 1 Then
                                        NouvelArticleEnregistrement("NumeroInventaire") = RecupereNumuero()
                                        NouvelArticleEnregistrement("CodeArticle") = LineCodeABarre
                                        NouvelArticleEnregistrement("CodeABarre") = LineCodeABarreOld
                                        NouvelArticleEnregistrement("Designation") = LineDesignation
                                        NouvelArticleEnregistrement("NumeroLot") = ""
                                        Try
                                            NouvelArticleEnregistrement("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", LineCodeABarre)
                                            NouvelArticleEnregistrement("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticleEnregistrement("CodeForme"))
                                        Catch ex As Exception
                                        End Try


                                        NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", LineCodeABarre)

                                        NouvelArticleEnregistrement("StockInitial") = 0
                                        NouvelArticleEnregistrement("StockActuel") = LineQuantite - QteTotal
                                        NouvelArticleEnregistrement("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                        NouvelArticleEnregistrement("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                        NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                                        NouvelArticleEnregistrement("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                                        NouvelArticleEnregistrement("CodeArticle") = ""
                                        NouvelArticleEnregistrement("NumeroLot") = ""
                                        dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)

                                    End If


                                ElseIf dsInventaire.Tables("LOT_ARTICLE").Rows.Count = 0 Then

                                    '************ enregistrement dans la table INVENTAIRE_DETAILS (juste on ajoute le 
                                    '************ numero de lot)
                                    NouvelArticleEnregistrement("NumeroInventaire") = RecupereNumuero()
                                    NouvelArticleEnregistrement("CodeArticle") = LineCodeABarre
                                    NouvelArticleEnregistrement("CodeABarre") = LineCodeABarreOld
                                    NouvelArticleEnregistrement("Designation") = LineDesignation
                                    NouvelArticleEnregistrement("NumeroLot") = ""
                                    Try
                                        NouvelArticleEnregistrement("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", LineCodeABarre)
                                        NouvelArticleEnregistrement("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
                                    Catch ex As Exception
                                    End Try

                                    NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", LineCodeABarre)

                                    'NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("CodeRayon", " ARTICLE", "CodeArticle", LineCodeABarre)
                                    NouvelArticleEnregistrement("StockInitial") = 0
                                    NouvelArticleEnregistrement("StockActuel") = 0
                                    NouvelArticleEnregistrement("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                    NouvelArticleEnregistrement("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                    NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                                    NouvelArticleEnregistrement("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                                    NouvelArticleEnregistrement("CodeArticle") = ""
                                    NouvelArticleEnregistrement("NumeroLot") = ""
                                    dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)
                                End If
                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                                ' afficher code A Barre n existe pa                   
                                NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                                NouvelArticle("NumeroInventaire") = RecupereNumuero()
                                NouvelArticle("CodeArticle") = ""
                                NouvelArticle("CodeABarre") = LineCodeABarre
                                NouvelArticle("Designation") = "ARTICLE INEXISTANT"
                                NouvelArticle("StockActuel") = LineQuantite
                                NouvelArticle("StockInitial") = 0
                                'NouvelArticle("DifferenceStock") = LineQuantite - 0
                                dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)
                            End Try
                        End If


                    Loop Until FichierTerminal.EndOfStream = True
                    FichierTerminal.Close()
                Else
                    bLire.Enabled = True
                    Exit Sub
                End If

            ElseIf GetSetting("PHARMA", "PHARMA", "TypeTerminal", "") = "RESEAU" Then

                StrSQL = " SELECT BarCode, SUM(Quantity) AS Quantity FROM Terminal GROUP BY BarCode ORDER BY MAX(Id) "

                cmdInventaire.Connection = ConnectionServeur
                cmdInventaire.CommandText = StrSQL
                daInventaire = New SqlDataAdapter(cmdInventaire)
                daInventaire.Fill(dsInventaire, "Terminal")

                For Item = 0 To dsInventaire.Tables("Terminal").Rows.Count - 1

                    LineCodeABarre = dsInventaire.Tables("Terminal").Rows(Item).Item("BarCode").ToString()
                    LineQuantite = dsInventaire.Tables("Terminal").Rows(Item).Item("Quantity").ToString()

                    '''''''''''' DATATABLE INVENTAIE_DETAIL_AFFICHE ''''''''''''''''''''
                    LineCodeABarreOld = LineCodeABarre

                    StrSQL = "SELECT CodeArticle FROM ARTICLE WHERE Supprime = 0 AND (CodeArticle = " + Quote(LineCodeABarre) + " OR CodeABarre = " + Quote(LineCodeABarre) + ")"
                    cmdChargement.Connection = ConnectionServeur
                    cmdChargement.CommandText = StrSQL

                    If LineQuantite <> "0" Then
                        Try
                            LineCodeABarre = cmdChargement.ExecuteScalar().ToString

                            LineDesignation = RecupererValeurExecuteScalaire("Designation", "ARTICLE", "CodeArticle", LineCodeABarre)
                            NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                            NouvelArticle("NumeroInventaire") = RecupereNumuero()
                            NouvelArticle("CodeArticle") = LineCodeABarre
                            NouvelArticle("CodeABarre") = LineCodeABarreOld
                            NouvelArticle("Designation") = LineDesignation

                            NouvelArticle("StockActuel") = LineQuantite

                            StrSQL = "SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE CodeArticle = " + Quote(LineCodeABarre)
                            cmdChargement.Connection = ConnectionServeur
                            cmdChargement.CommandText = StrSQL
                            If cmdChargement.ExecuteScalar() > 0 Then
                                NouvelArticle("StockInitial") = cmdChargement.ExecuteScalar()
                            Else
                                NouvelArticle("StockInitial") = 0
                            End If
                            NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", LineCodeABarre)
                            NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", "ARTICLE", "CodeArticle", LineCodeABarre)

                            NouvelArticle("TotalAchatTTC") = NouvelArticle("PrixAchatTTC") * NouvelArticle("StockActuel")
                            Try
                                NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", LineCodeABarre))
                            Catch ex As Exception
                            End Try

                            NouvelArticle("Rayon") = RecupererValeurExecuteScalaire("Rayon", "ARTICLE", "CodeArticle", LineCodeABarre)

                            dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)
                            If (dsInventaire.Tables.IndexOf("LOT_ARTICLE") > -1) Then
                                dsInventaire.Tables("LOT_ARTICLE").Clear()
                            End If
                            If (dsInventaire.Tables.IndexOf("LOT_ARTICLE_COMPLET") > -1) Then
                                dsInventaire.Tables("LOT_ARTICLE_COMPLET").Clear()
                            End If
                            '--------------- pour la table d'affichage somme des quantités une seule ligne par article
                            StrSQL = " SELECT CodeArticle,SUM(QteLotArticle) as QteLotArticle FROM LOT_ARTICLE WHERE " + _
                                     " CodeArticle = " + Quote(LineCodeABarre) + _
                                     " GROUP BY CodeArticle"

                            cmdInventaire.Connection = ConnectionServeur
                            cmdInventaire.CommandText = StrSQL
                            daInventaire = New SqlDataAdapter(cmdInventaire)
                            daInventaire.Fill(dsInventaire, "LOT_ARTICLE")

                            '--------------- pour la table d'enregistrement chaque lots dans une ligne
                            StrSQL = " SELECT * FROM LOT_ARTICLE WHERE " + _
                                     " CodeArticle = " + Quote(LineCodeABarre) + ""

                            cmdInventaire.Connection = ConnectionServeur
                            cmdInventaire.CommandText = StrSQL
                            daInventaire = New SqlDataAdapter(cmdInventaire)
                            daInventaire.Fill(dsInventaire, "LOT_ARTICLE_COMPLET")

                            If dsInventaire.Tables("LOT_ARTICLE").Rows.Count > 0 Then

                                '************ enregistrement dans la table INVENTAIRE_DETAILS (juste on ajoute le 
                                '************ numero de lot et chaque lot dans une ligne)
                                For J = 0 To dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows.Count - 1

                                    NouvelArticleEnregistrement("NumeroInventaire") = RecupereNumuero()
                                    NouvelArticleEnregistrement("CodeArticle") = LineCodeABarre
                                    NouvelArticleEnregistrement("CodeABarre") = LineCodeABarreOld
                                    NouvelArticleEnregistrement("Designation") = LineDesignation
                                    NouvelArticleEnregistrement("NumeroLot") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("NumeroLotArticle")

                                    Try
                                        NouvelArticleEnregistrement("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", LineCodeABarre)
                                    Catch ex As Exception
                                    End Try

                                    NouvelArticleEnregistrement("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))


                                    NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", LineCodeABarre)
                                    NouvelArticleEnregistrement("StockInitial") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("QteLotArticle")

                                    NouvelArticleEnregistrement("StockActuel") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("QteLotArticle") ' ajouter


                                    ' NouvelArticleEnregistrement("StockActuel") = LineQuantite
                                    NouvelArticleEnregistrement("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", LineCodeABarre)
                                    NouvelArticleEnregistrement("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                    NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                                    NouvelArticleEnregistrement("Designation") = ""  'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                                    NouvelArticleEnregistrement("CodeArticle") = ""
                                    NouvelArticleEnregistrement("NumeroLot") = ""
                                    dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)
                                Next

                                ''''''
                                Try


                                    QteTotal = 0
                                    For l = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
                                        If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("CodeArticle") <> "" Then
                                            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE").Rows(0).Item("CodeArticle") Then
                                                If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("NumeroLot") <> "" Then
                                                    QteTotal += dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("StockActuel")
                                                End If
                                            End If

                                        End If
                                    Next
                                    For l = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
                                        If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("CodeArticle") <> "" Then
                                            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE").Rows(0).Item("CodeArticle") Then
                                                If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("NumeroLot") = "" Then
                                                    dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("StockActuel") = LineQuantite - QteTotal '+ dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(l).Item("StockActuel")
                                                End If
                                            End If

                                        End If
                                    Next
                                Catch ex As Exception
                                    MsgBox(ex.Message)
                                End Try

                                For i = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
                                    If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") <> "" Then
                                        If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE").Rows(0).Item("CodeArticle") Then
                                            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroLot") = "" Then
                                                testlotvide = 0
                                                Exit For
                                            Else
                                                testlotvide = 1
                                            End If
                                        End If
                                    End If
                                Next

                                If testlotvide = 1 Then
                                    NouvelArticleEnregistrement("NumeroInventaire") = RecupereNumuero()
                                    NouvelArticleEnregistrement("CodeArticle") = LineCodeABarre
                                    NouvelArticleEnregistrement("CodeABarre") = LineCodeABarreOld
                                    NouvelArticleEnregistrement("Designation") = LineDesignation
                                    NouvelArticleEnregistrement("NumeroLot") = ""
                                    Try
                                        NouvelArticleEnregistrement("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", LineCodeABarre)
                                        NouvelArticleEnregistrement("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticleEnregistrement("CodeForme"))
                                    Catch ex As Exception
                                    End Try


                                    NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", LineCodeABarre)

                                    NouvelArticleEnregistrement("StockInitial") = 0
                                    NouvelArticleEnregistrement("StockActuel") = LineQuantite - QteTotal
                                    NouvelArticleEnregistrement("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                    NouvelArticleEnregistrement("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                    NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                                    NouvelArticleEnregistrement("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                                    NouvelArticleEnregistrement("CodeArticle") = ""
                                    NouvelArticleEnregistrement("NumeroLot") = ""
                                    dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)

                                End If


                            ElseIf dsInventaire.Tables("LOT_ARTICLE").Rows.Count = 0 Then

                                '************ enregistrement dans la table INVENTAIRE_DETAILS (juste on ajoute le 
                                '************ numero de lot)
                                NouvelArticleEnregistrement("NumeroInventaire") = RecupereNumuero()
                                NouvelArticleEnregistrement("CodeArticle") = LineCodeABarre
                                NouvelArticleEnregistrement("CodeABarre") = LineCodeABarreOld
                                NouvelArticleEnregistrement("Designation") = LineDesignation
                                NouvelArticleEnregistrement("NumeroLot") = ""
                                Try
                                    NouvelArticleEnregistrement("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", LineCodeABarre)
                                    NouvelArticleEnregistrement("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
                                Catch ex As Exception
                                End Try

                                NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", LineCodeABarre)

                                'NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("CodeRayon", " ARTICLE", "CodeArticle", LineCodeABarre)
                                NouvelArticleEnregistrement("StockInitial") = 0
                                NouvelArticleEnregistrement("StockActuel") = 0
                                NouvelArticleEnregistrement("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                NouvelArticleEnregistrement("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", LineCodeABarre)
                                NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                                NouvelArticleEnregistrement("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                                NouvelArticleEnregistrement("CodeArticle") = ""
                                NouvelArticleEnregistrement("NumeroLot") = ""
                                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)
                            End If
                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                            ' afficher code A Barre n existe pa                   
                            NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                            NouvelArticle("NumeroInventaire") = RecupereNumuero()
                            NouvelArticle("CodeArticle") = ""
                            NouvelArticle("CodeABarre") = LineCodeABarre
                            NouvelArticle("Designation") = "ARTICLE INEXISTANT"
                            NouvelArticle("StockActuel") = LineQuantite
                            NouvelArticle("StockInitial") = 0
                            'NouvelArticle("DifferenceStock") = LineQuantite - 0
                            dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)
                        End Try
                    End If
                Next


                '''''''''''' DATATABLE INVENTAIE_DETAIL_AFFICHE ''''''''''''''''''''

                NouvelleAchat = dsInventaire.Tables("INVENTAIRE").NewRow()
                dsInventaire.Tables("INVENTAIRE").Rows.Add(NouvelleAchat)

            End If


            '''''''''''' DATATABLE INVENTAIE_DETAIL_AFFICHE ''''''''''''''''''''

            NouvelleAchat = dsInventaire.Tables("INVENTAIRE").NewRow()
            dsInventaire.Tables("INVENTAIRE").Rows.Add(NouvelleAchat)

            '''''''''''''''''''''' FIN TERMINAL ''''''''''''''''''''''''''
        ElseIf Mode = "Achat" Then

            '------------------------------ préparation des datatables vides 
            If (dsInventaire.Tables.IndexOf("INVENTAIRE_DETAILS_AFFICHE") > -1) Then
                dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Clear()
            End If
            If (dsInventaire.Tables.IndexOf("INVENTAIRE") > -1) Then
                dsInventaire.Tables("INVENTAIRE").Clear()
            End If
            If (dsInventaire.Tables.IndexOf("INVENTAIRE_DETAILS") > -1) Then
                dsInventaire.Tables("INVENTAIRE_DETAILS").Clear()
            End If

            'chargement des Entêtes des achats 
            StrSQL = "SELECT TOP (0) * FROM ACHAT ORDER BY NumeroAchat ASC"
            cmdInventaire.Connection = ConnectionServeur
            cmdInventaire.CommandText = StrSQL
            daInventaire = New SqlDataAdapter(cmdInventaire)
            daInventaire.Fill(dsInventaire, "INVENTAIRE")
            cbInventaire = New SqlCommandBuilder(daInventaire)

            '************************** création d'une table INVENTAIRE_DETAILS qui doit etre une copie de celle de
            '************************** la base de données et qui contient chaque article avec les lots disponible
            '************************** chaque un dans un enregistrement seul, c'est sur cette table qu'on doit appliquer 
            '************************** la methode update du dataAdapter
            StrSQL = " SELECT NumeroAchat," + _
                     " ACHAT_DETAILS.CodeArticle," + _
                     " ACHAT_DETAILS.CodeABarre," + _
                     " ACHAT_DETAILS.Designation," + _
                     " ACHAT_DETAILS.NumeroLotArticle, " + _
                     " LibelleForme," + _
                     " ACHAT_DETAILS.CodeForme, " + _
                     " ACHAT_DETAILS.Qte," + _
                     " ACHAT_DETAILS.Stock," + _
                     " ACHAT_DETAILS.PrixAchatHT," + _
                     " ACHAT_DETAILS.TotalAchatHT," + _
                     " ACHAT_DETAILS.PrixVenteTTC," + _
                     " ACHAT_DETAILS.DatePeremption, " + _
                     " ARTICLE.QuantiteUnitaire, " + _
                     " ARTICLE.QteACommander, " + _
                     " ARTICLE.TVA " + _
                     " FROM " + _
                     " ACHAT_DETAILS " + _
                     " LEFT OUTER JOIN ARTICLE ON ACHAT_DETAILS.CodeArticle=ARTICLE.CodeArticle " + _
                     " LEFT OUTER JOIN FORME_ARTICLE ON ACHAT_DETAILS.CodeForme = FORME_ARTICLE.CodeForme " + _
                     " WHERE NumeroAchat ='0' AND ARTICLE.supprime = 0 "

            cmdInventaire.Connection = ConnectionServeur
            cmdInventaire.CommandText = StrSQL
            daInventaire.Fill(dsInventaire, "INVENTAIRE_DETAILS")
            cbInventaire = New SqlCommandBuilder(daInventaire)

            'ajout d'un nouvel enregistrement vide dans les datatables convenables
            NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
            NouvelArticleEnregistrement("Designation") = ""
            NouvelArticleEnregistrement("CodeArticle") = ""
            NouvelArticleEnregistrement("NumeroLotArticle") = ""
            dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)

            'chargement des détails des achats juste une table pour formater la gride par les colonnes (vide)
            StrSQL = " SELECT TOP (0) NumeroAchat, " + _
                     " ACHAT_DETAILS.CodeArticle, " + _
                     " '' AS CodeABarre, " + _
                     " MAX(ACHAT_DETAILS.Designation) AS Designation," + _
                     " MAX(ACHAT_DETAILS.NumeroLotArticle) AS NumeroLotArticle," + _
                     " MAX(LibelleForme) AS LibelleForme," + _
                     " MAX(ACHAT_DETAILS.CodeForme) AS CodeForme," + _
                     " SUM(ACHAT_DETAILS.Qte) as Qte," + _
                     " SUM(ACHAT_DETAILS.Stock) AS Stock," + _
                     " MAX(ACHAT_DETAILS.PrixAchatHT) AS PrixAchatHT," + _
                     " MAX(ACHAT_DETAILS.TotalAchatHT) AS TotalAchatHT," + _
                     " MAX(ACHAT_DETAILS.PrixVenteTTC) AS PrixVenteTTC ," + _
                     " MAX(ACHAT_DETAILS.DatePeremption) AS DatePeremption, " + _
                     " MAX(ARTICLE.QuantiteUnitaire) AS QuantiteUnitaire, " + _
                     " MAX(ARTICLE.QteACommander) AS QteACommander, " + _
                      "MAX(ARTICLE.TVA) AS TVA " + _
                     " FROM " + _
                     " ACHAT_DETAILS " + _
                     " LEFT OUTER JOIN ARTICLE ON ACHAT_DETAILS.CodeArticle=ARTICLE.CodeArticle " + _
                     " LEFT OUTER JOIN FORME_ARTICLE ON ACHAT_DETAILS.CodeForme = FORME_ARTICLE.CodeForme " + _
                     " GROUP BY NumeroAchat,ACHAT_DETAILS.CodeArticle "


            cmdInventaire.Connection = ConnectionServeur
            cmdInventaire.CommandText = StrSQL
            daInventaire.Fill(dsInventaire, "INVENTAIRE_DETAILS_AFFICHE")
            cbInventaire = New SqlCommandBuilder(daInventaire)
            With gInventaire
                .Columns.Clear()
                .DataSource = dsInventaire
                .DataMember = "INVENTAIRE_DETAILS_AFFICHE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code article"
                .Columns("CodeABarre").Caption = "Code Article" '"Code A Barre"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Qte").Caption = "Quantité lue"
                .Columns("Stock").Caption = "Stock"
                .Columns("PrixAchatHT").Caption = "PU Achat HT"
                .Columns("TotalAchatHT").Caption = "Valeur Achat HT"
                .Columns("PrixVenteTTC").Caption = "Prix Vente TTC"
                .Columns("DatePeremption").DataField = "DatePeremption"
                ' Centrer toutes les entêtes
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Locked = True
                Next
                .FilterBar = True
                .Splits(0).DisplayColumns("NumeroAchat").Width = 0
                .Splits(0).DisplayColumns("NumeroAchat").Visible = False
                .Splits(0).DisplayColumns("PrixAchatHT").Visible = False
                .Splits(0).DisplayColumns("TotalAchatHT").Visible = False
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = False

                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                '.Splits(0).DisplayColumns("Rayon").Visible = False
                '.Splits(0).DisplayColumns("StockInitial").Visible = False
                '.Splits(0).DisplayColumns("StockActuel").Visible = False

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                '.Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
                .Splits(0).DisplayColumns("CodeArticle").Width = 100
                .Splits(0).DisplayColumns("Designation").Width = 320
                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
                .Splits(0).DisplayColumns("LibelleForme").Width = 80
                .Splits(0).DisplayColumns("CodeForme").Width = 0
                .Splits(0).DisplayColumns("Stock").Width = 80
                .Splits(0).DisplayColumns("Qte").Width = 80
                .Splits(0).DisplayColumns("PrixAchatHT").Width = 80
                '.Columns("PrixAchatTTC").NumberFormat = "#,###" + NombreDeChiffreApresVirgule()
                .Splits(0).DisplayColumns("TotalAchatHT").Width = 80
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 0
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = False
                .Splits(0).DisplayColumns("CodeForme").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                '.Splits(0).DisplayColumns("Qte").Visible = False
                .Splits(0).DisplayColumns("LibelleForme").Visible = False
                .Splits(0).DisplayColumns("PrixAchatHT").Visible = False
                .Splits(0).DisplayColumns("TotalAchatHT").Visible = False

                '.Splits(0).DisplayColumns("StockActuel").Locked = False

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                ParametreGrid(gInventaire)
            End With
            bLire.Enabled = False


            '''''''''''''''''''''' DEBUT TERMINAL ''''''''''''''''''''''''

            Dim Line As String = ""
            Dim LineCodeABarre As String ' CodeArticle
            Dim LineCodeABarreOld As String ' CodeABarre
            Dim LineQuantite As String = ""
            Dim LineDesignation As String
            Dim test As Integer = 0
            Dim nbre As Integer = 0
            Dim testQuantie As Boolean = True
            Dim TestEntete As Boolean = False
            Dim QteTotal As Integer = 0
            Dim testlotvide As Integer = 0

            Line = ""
            TestEntete = False

            If GetSetting("PHARMA", "PHARMA", "TypeTerminal", "") = "FICHIER_TEXTE" Then

                Dim Chemin As String = ""
                Dim yLigne() As String

                Dim MyReceptionFichier As New fReceptionFichierTerminal
                MyReceptionFichier.ShowDialog()


                If MyReceptionFichier.FileName <> "" Then
                    Chemin = MyReceptionFichier.FileName

                    Dim FichierTerminal As New IO.StreamReader(Chemin)

                    Do
                        yLigne = FichierTerminal.ReadLine.Split(",")

                        LineCodeABarre = yLigne(0)
                        LineQuantite = yLigne(1)

                        '''''''''''' DATATABLE INVENTAIE_DETAIL_AFFICHE ''''''''''''''''''''
                        LineCodeABarreOld = LineCodeABarre

                        StrSQL = "SELECT CodeArticle FROM ARTICLE WHERE Supprime = 0 AND (CodeArticle = " + Quote(LineCodeABarre) + " OR CodeABarre = " + Quote(LineCodeABarre) + ")"
                        cmdChargement.Connection = ConnectionServeur
                        cmdChargement.CommandText = StrSQL

                        If LineQuantite <> "0" Then
                            Try
                                LineCodeABarre = cmdChargement.ExecuteScalar().ToString

                                LineDesignation = RecupererValeurExecuteScalaire("Designation", "ARTICLE", "CodeArticle", LineCodeABarre)
                                NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                                NouvelArticle("NumeroAchat") = RecupereNumueroAchat()
                                NouvelArticle("CodeArticle") = LineCodeABarre
                                NouvelArticle("CodeABarre") = LineCodeABarreOld
                                NouvelArticle("Designation") = LineDesignation

                                NouvelArticle("Qte") = LineQuantite

                                StrSQL = "SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE CodeArticle = " + Quote(LineCodeABarre)
                                cmdChargement.Connection = ConnectionServeur
                                cmdChargement.CommandText = StrSQL
                                If cmdChargement.ExecuteScalar() > 0 Then
                                    NouvelArticle("Stock") = cmdChargement.ExecuteScalar()
                                Else
                                    NouvelArticle("Stock") = 0
                                End If

                                StrSQL = "SELECT MAX(DatePeremptionArticle) FROM LOT_ARTICLE WHERE CodeArticle = " + Quote(LineCodeABarre)
                                cmdChargement.Connection = ConnectionServeur
                                cmdChargement.CommandText = StrSQL
                                If Not IsDBNull(cmdChargement.ExecuteScalar) Then
                                    NouvelArticle("DatePeremption") = cmdChargement.ExecuteScalar()
                                    StrSQL = "SELECT NumeroLotArticle FROM LOT_ARTICLE WHERE DatePeremptionArticle = " + Quote(cmdChargement.ExecuteScalar()) + " AND CodeArticle=" + Quote(LineCodeABarre)
                                    cmdChargement.Connection = ConnectionServeur
                                    cmdChargement.CommandText = StrSQL
                                    NouvelArticle("NumeroLotArticle") = cmdChargement.ExecuteScalar()
                                End If

                                NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", LineCodeABarre)
                                NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", "ARTICLE", "CodeArticle", LineCodeABarre)
                                NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", "ARTICLE", "CodeArticle", LineCodeABarre)
                                NouvelArticle("QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", LineCodeABarre)
                                NouvelArticle("QteACommander") = RecupererValeurExecuteScalaire("QteACommander", "ARTICLE", "CodeArticle", LineCodeABarre)

                                NouvelArticle("TotalAchatHT") = NouvelArticle("PrixAchatHT") * NouvelArticle("Qte")
                                Try
                                    NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", LineCodeABarre))
                                Catch ex As Exception
                                End Try


                                dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                                ' afficher code A Barre n existe pa                   
                                NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                                NouvelArticle("NumeroAchat") = RecupereNumueroAchat()
                                NouvelArticle("CodeArticle") = ""
                                NouvelArticle("CodeABarre") = LineCodeABarre
                                NouvelArticle("Designation") = "ARTICLE INEXISTANT"
                                NouvelArticle("Qte") = LineQuantite
                                NouvelArticle("QuantiteUnitaire") = 0
                                NouvelArticle("QteACommander") = 0
                                NouvelArticle("Stock") = 0
                                NouvelArticle("TVA") = 0
                                'NouvelArticle("DifferenceStock") = LineQuantite - 0
                                dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)
                            End Try
                        End If


                    Loop Until FichierTerminal.EndOfStream = True
                    FichierTerminal.Close()
                Else
                    bLire.Enabled = True
                    Exit Sub
                End If
            ElseIf GetSetting("PHARMA", "PHARMA", "TypeTerminal", "") = "RESEAU" Then

                StrSQL = " SELECT BarCode, SUM(Quantity) AS Quantity FROM Terminal GROUP BY BarCode ORDER BY MAX(Id)"

                cmdInventaire.Connection = ConnectionServeur
                cmdInventaire.CommandText = StrSQL
                daInventaire = New SqlDataAdapter(cmdInventaire)
                daInventaire.Fill(dsInventaire, "Terminal")

                For Item = 0 To dsInventaire.Tables("Terminal").Rows.Count - 1

                    LineCodeABarre = dsInventaire.Tables("Terminal").Rows(Item).Item("BarCode").ToString()
                    LineQuantite = dsInventaire.Tables("Terminal").Rows(Item).Item("Quantity").ToString()

                    '''''''''''' DATATABLE INVENTAIE_DETAIL_AFFICHE ''''''''''''''''''''
                    LineCodeABarreOld = LineCodeABarre

                    StrSQL = "SELECT CodeArticle FROM ARTICLE WHERE Supprime = 0 AND (CodeArticle = " + Quote(LineCodeABarre) + " OR CodeABarre = " + Quote(LineCodeABarre) + ")"
                    cmdChargement.Connection = ConnectionServeur
                    cmdChargement.CommandText = StrSQL

                    If LineQuantite <> "0" Then
                        Try
                            LineCodeABarre = cmdChargement.ExecuteScalar().ToString

                            LineDesignation = RecupererValeurExecuteScalaire("Designation", "ARTICLE", "CodeArticle", LineCodeABarre)
                            NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                            NouvelArticle("NumeroAchat") = RecupereNumueroAchat()
                            NouvelArticle("CodeArticle") = LineCodeABarre
                            NouvelArticle("CodeABarre") = LineCodeABarreOld
                            NouvelArticle("Designation") = LineDesignation

                            NouvelArticle("Qte") = LineQuantite

                            StrSQL = "SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE CodeArticle = " + Quote(LineCodeABarre)
                            cmdChargement.Connection = ConnectionServeur
                            cmdChargement.CommandText = StrSQL
                            If cmdChargement.ExecuteScalar() > 0 Then
                                NouvelArticle("Stock") = cmdChargement.ExecuteScalar()
                            Else
                                NouvelArticle("Stock") = 0
                            End If

                            StrSQL = "SELECT MAX(DatePeremptionArticle) FROM LOT_ARTICLE WHERE CodeArticle = " + Quote(LineCodeABarre)
                            cmdChargement.Connection = ConnectionServeur
                            cmdChargement.CommandText = StrSQL
                            If Not IsDBNull(cmdChargement.ExecuteScalar) Then
                                NouvelArticle("DatePeremption") = cmdChargement.ExecuteScalar()
                                StrSQL = "SELECT NumeroLotArticle FROM LOT_ARTICLE WHERE DatePeremptionArticle = " + Quote(cmdChargement.ExecuteScalar()) + " AND CodeArticle=" + Quote(LineCodeABarre)
                                cmdChargement.Connection = ConnectionServeur
                                cmdChargement.CommandText = StrSQL
                                NouvelArticle("NumeroLotArticle") = cmdChargement.ExecuteScalar()
                            End If

                            NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", LineCodeABarre)
                            NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", "ARTICLE", "CodeArticle", LineCodeABarre)
                            NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", "ARTICLE", "CodeArticle", LineCodeABarre)
                            NouvelArticle("QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", LineCodeABarre)
                            NouvelArticle("QteACommander") = RecupererValeurExecuteScalaire("QteACommander", "ARTICLE", "CodeArticle", LineCodeABarre)

                            NouvelArticle("TotalAchatHT") = NouvelArticle("PrixAchatHT") * NouvelArticle("Qte")
                            Try
                                NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", LineCodeABarre))
                            Catch ex As Exception
                            End Try


                            dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)

                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                            ' afficher code A Barre n existe pa                   
                            NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                            NouvelArticle("NumeroAchat") = RecupereNumueroAchat()
                            NouvelArticle("CodeArticle") = ""
                            NouvelArticle("CodeABarre") = LineCodeABarre
                            NouvelArticle("Designation") = "ARTICLE INEXISTANT"
                            NouvelArticle("Qte") = LineQuantite
                            NouvelArticle("QuantiteUnitaire") = 0
                            NouvelArticle("QteACommander") = 0
                            NouvelArticle("Stock") = 0
                            NouvelArticle("TVA") = 0
                            'NouvelArticle("DifferenceStock") = LineQuantite - 0
                            dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)
                        End Try
                    End If
                Next
            End If

            '''''''''''' DATATABLE INVENTAIE_DETAIL_AFFICHE ''''''''''''''''''''

            NouvelleAchat = dsInventaire.Tables("INVENTAIRE").NewRow()
            dsInventaire.Tables("INVENTAIRE").Rows.Add(NouvelleAchat)

            '''''''''''''''''''''' FIN TERMINAL ''''''''''''''''''''''''''
        End If

        bAjoutArticle.Enabled = True
        bSupprimeArticle.Enabled = True
        bValider.Enabled = True


    End Sub

    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        Try
            Quote(CStr(ValeurCle))
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(CStr(ValeurCle))
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function

    Private Sub bStop_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        PortSerie.Close()
    End Sub

    Private Sub bValider_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bValider.Click
        'Dim strSQL As String
        Dim cmd As New SqlCommand

        'If dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count = 0 Then
        If gInventaire.RowCount = 0 Then
            Exit Sub
        End If

        '------------- Test s il y des lignes avec article inexistant
        For I = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1
            If dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(I).Item("CodeArticle").ToString = "" Then
                MsgBox("Veuillez Vérifier la Liste des Articles Avant de Valider", MsgBoxStyle.Critical)
                Valider = False
                Exit Sub
                I = 0
            End If
        Next
        '------------------

        '------------------------------ demande du mot de passe
        'Dim myMotDePasse As New fAuthentifier
        'myMotDePasse.ShowDialog()
        'ConfirmerEnregistrer = myMotDePasse.Trouver
        'CodeOperateur = myMotDePasse.CodePersonnel
        'myMotDePasse.Dispose()
        'myMotDePasse.Close()
        'If ConfirmerEnregistrer = False Then
        '    Valider = False
        '    Exit Sub
        'Else
        Valider = True
        Me.Dispose()
        'End If
    End Sub

    Public Function RecupereNumuero()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max([NumeroInventaire]) FROM [INVENTAIRE]"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If
        Return ValeurRetour
    End Function

    Public Function RecupereNumueroAchat()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT MAX([NumeroAchat]) FROM [ACHAT]"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If
        Return ValeurRetour
    End Function

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        PortSerie.Close()
        'fMain.Tab.SelectedTab.Dispose()
        Me.Hide()
        Me.Close()
    End Sub

    Private Sub bAjoutArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjoutArticle.Click
        If gInventaire.Columns("Designation").Value = "ARTICLE INEXISTANT" Then
            Dim InstanceFicheArticle As New fFicheArticle
            InstanceFicheArticle.ajoutmodif = "A"
            InstanceFicheArticle.Init() '.Init("")
            'InstanceFicheArticle.tCIP.Value = gInventaire.Columns("CodeABarre").Value
            InstanceFicheArticle.tCodeArticle.Value = gInventaire.Columns("CodeABarre").Value
            InstanceFicheArticle.ShowDialog()

            If InstanceFicheArticle.AjoutReussi = True Then
                gInventaire.Columns("CodeABarre").Value = gInventaire.Columns("CodeABarre").Value  '
                gInventaire.Columns("CodeArticle").Value = InstanceFicheArticle.CodeArticleAjout
                gInventaire.Columns("Designation").Value = InstanceFicheArticle.DesignationAjout
                gInventaire.Columns("PrixAchatTTC").Value = InstanceFicheArticle.PrixAchatAjout

                gInventaire.Columns("StockActuel").Value = gInventaire.Columns("StockActuel").Value
                gInventaire.Columns("StockInitial").Value = 0

                gInventaire.Columns("Rayon").Value = InstanceFicheArticle.CodeRayonAjout

                gInventaire.Columns("LibelleForme").Value = InstanceFicheArticle.CodeFormeAjout

                NouvelArticleEnregistrement("NumeroInventaire") = RecupereNumuero()
                NouvelArticleEnregistrement("CodeArticle") = InstanceFicheArticle.CodeArticleAjout
                NouvelArticleEnregistrement("Designation") = InstanceFicheArticle.DesignationAjout

                NouvelArticleEnregistrement("NumeroLot") = ""
                Try
                    NouvelArticleEnregistrement("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", InstanceFicheArticle.CodeArticleAjout)
                    NouvelArticleEnregistrement("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME", "CodeForme", NouvelArticle("CodeForme"))
                Catch ex As Exception
                End Try
                NouvelArticleEnregistrement("Rayon") = InstanceFicheArticle.CodeRayonAjout
                NouvelArticleEnregistrement("StockInitial") = 0
                NouvelArticleEnregistrement("StockActuel") = gInventaire.Columns("StockActuel").Value
                NouvelArticleEnregistrement("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", InstanceFicheArticle.CodeArticleAjout)
                NouvelArticleEnregistrement("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", InstanceFicheArticle.CodeArticleAjout)
                NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                NouvelArticleEnregistrement("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                NouvelArticleEnregistrement("CodeArticle") = ""
                NouvelArticleEnregistrement("NumeroLot") = ""
                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)
            End If
            InstanceFicheArticle.Dispose()
            InstanceFicheArticle.Close()

        ElseIf gInventaire.Columns("Designation").Value <> "ARTICLE INEXISTANT" Then
            Dim InstanceFicheArticle As New fFicheArticle
            InstanceFicheArticle.ajoutmodif = "M"
            InstanceFicheArticle.CodeArticle = gInventaire.Columns("CodeArticle").Value
            InstanceFicheArticle.StockArticle = gInventaire.Columns("StockInitial").Value
            InstanceFicheArticle.Init() '(gInventaire.Columns("CodeArticle").Value)
            InstanceFicheArticle.bConfirmer.Enabled = False
            'InstanceFicheArticle.bGestionStock.Enabled = False
            InstanceFicheArticle.ShowDialog()
        End If
    End Sub

    Private Sub bSupprimeArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimeArticle.Click
        '---------------------------------- suppression de la ligne selectionnées -------------------------
        Dim test As Integer = 0
        Dim testCodeArticle As String
        Dim testCount As Integer
        Dim i As Integer

        testCodeArticle = gInventaire.Columns("CodeArticle").Value.ToString()
        testCount = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
        i = 0
        If gInventaire.RowCount > 0 Then
            gInventaire.Delete()
        End If
        Do While i < testCount
            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle").ToString = testCodeArticle Then
                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Delete()
                testCount = testCount - 1
                test += 1
            Else
                i = i + 1
            End If
        Loop
        gInventaire.Focus()
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        PortSerie.Close()
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub gInventaire_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gInventaire.FetchRowStyle
        If gInventaire.Columns("Designation").CellText(e.Row) = "ARTICLE INEXISTANT" Then
            e.CellStyle.BackColor = Color.FromArgb(255, 255, 192, 192)
        Else
            e.CellStyle.BackColor = Color.White
        End If
    End Sub

    Private Sub ChangerLotsArticle(ByVal CodeArticle, ByVal Qte)
        For i = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") = CodeArticle And Trim(dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroLot").ToString) <> "" Then
                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel") = 0
            End If
            If Trim(dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroLot").ToString) = "" And dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle").ToString = CodeArticle Then
                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel") = Qte
            End If
        Next
        'gArticles.Columns("StockActuel").Value = Qte
    End Sub

    Public Sub ChargerDetailArticle(ByVal CodeABarre As String)
        Dim resultat As String = ""
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim NumeroLot As String = ""
        Dim i As Integer = 0
        Dim j As Integer = 0
        Dim CodeArticle As String = ""

        CodeArticle = CStr(RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "CodeArticle", CodeABarre))
        If CodeArticle = "" Then
            'gArticles.Columns("CodeABarre").Value = ""
            gInventaire.Columns("CodeArticle").Value = ""
            gInventaire.Col = 3
            'gArticles.Col = 2
            Exit Sub
        End If
        If (dsInventaire.Tables.IndexOf("LOT_ARTICLE") > -1) Then
            dsInventaire.Tables("LOT_ARTICLE").Clear()
        End If
        If (dsInventaire.Tables.IndexOf("LOT_ARTICLE_SELECTION") > -1) Then
            dsInventaire.Tables("LOT_ARTICLE_SELECTION").Clear()
        End If
        StrSQL = " SELECT CodeArticle,SUM(Qte) as QteArticle FROM LOT_ARTICLE " + _
                " WHERE CodeArticle = " + Quote(CodeArticle) + _
                " GROUP BY CodeArticle"
        cmdInventaire.Connection = ConnectionServeur
        cmdInventaire.CommandText = StrSQL
        daInventaire = New SqlDataAdapter(cmdInventaire)
        daInventaire.Fill(dsInventaire, "LOT_ARTICLE")

        If dsInventaire.Tables("LOT_ARTICLE").Rows.Count = 0 Then
            '----- pour les articles qui n'ont pas de lot : possibilité de création d'un nouveau lot
            resultat = CStr(RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle))
            If resultat <> "" Then
                Dim reponse As MsgBoxResult
                reponse = MsgBox("Cet Article n'admet pas de lot," + Chr(13) + "Voulez vous créer un nouveau lot!" + Chr(13), MsgBoxStyle.YesNo, "Erreur")
                If reponse = MsgBoxResult.Yes Then
                    NouvelArticle("NumeroInventaire") = RecupereNumuero()
                    NouvelArticle("CodeArticle") = CodeArticle
                    NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)
                    Try
                        NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                        NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME", "CodeForme", NouvelArticle("CodeForme"))
                    Catch ex As Exception
                    End Try


                    NouvelArticle("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", CodeArticle)

                    NouvelArticle("StockInitial") = 0
                    NouvelArticle("StockActuel") = 0
                    NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", CodeArticle)
                    NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", CodeArticle)
                    '------------------ ajout dans la table des lots article elle rassemble à celle de la base
                    NouvelArticleEnregistrement("NumeroInventaire") = NouvelArticle("NumeroInventaire")
                    NouvelArticleEnregistrement("CodeArticle") = NouvelArticle("CodeArticle")
                    NouvelArticleEnregistrement("Designation") = NouvelArticle("Designation")
                    NouvelArticleEnregistrement("NumeroLot") = ""
                    NouvelArticleEnregistrement("CodeForme") = NouvelArticle("CodeForme")
                    NouvelArticleEnregistrement("LibelleForme") = NouvelArticle("LibelleForme")
                    NouvelArticleEnregistrement("Rayon") = NouvelArticle("Rayon")
                    NouvelArticleEnregistrement("StockInitial") = 0
                    NouvelArticleEnregistrement("StockActuel") = 0
                    NouvelArticleEnregistrement("PrixAchatTTC") = NouvelArticle("PrixAchatTTC")
                    NouvelArticleEnregistrement("PrixVenteTTC") = NouvelArticle("PrixVenteTTC")
                Else
                    gInventaire.Columns("CodeArticle").Value = ""
                End If
                'gArticles.Refresh()
                'gArticles.Col = 3
            Else
                gInventaire.Columns("CodeArticle").Value = ""
                'gArticles.Col = 3
                'gArticles.Refresh()
            End If
            gInventaire.Refresh()
            'gArticles.Col = 3
            gInventaire.Col = 3

            Exit Sub
        End If
        '---------- affichage de tous les lots disponible pour l'article choisie
        For i = 0 To dsInventaire.Tables("LOT_ARTICLE").Rows.Count - 1
            resultat = CStr(RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle))
            If resultat <> "" Then
                '------------------ ajout dans la table qui somme les quantité (juste pour l affichage)
                NouvelArticle("NumeroInventaire") = RecupereNumuero()
                NouvelArticle("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE").Rows(i).Item("CodeArticle")
                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)

                NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CIP", " ARTICLE", "CodeArticle", CodeArticle)

                'NouvelArticle("NumeroLot") = dsInventaire.Tables("LOT_ARTICLE").Rows(i).Item("NumeroLotArticle")
                Try
                    NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", CodeArticle)
                Catch ex As Exception
                End Try
                NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME", "CodeForme", NouvelArticle("CodeForme"))

                NouvelArticle("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("StockInitial") = dsInventaire.Tables("LOT_ARTICLE").Rows(i).Item("QteArticle")
                NouvelArticle("StockActuel") = NouvelArticle("StockInitial")
                NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", CodeArticle)
                '------------------ ajout dans la table des lots article elle rassemble à celle de la base
                'chargement des lots
                StrSQL = "SELECT NumeroLot AS NumeroLotArticle, CodeArticle, Qte AS QteLotArticle" + _
                        " FROM LOT_ARTICLE WHERE LOT_ARTICLE.CodeArticle='" + _
                        gInventaire.Columns("CodeArticle").Value.ToString + "'"
                cmdInventaire.Connection = ConnectionServeur
                cmdInventaire.CommandText = StrSQL
                daInventaire.Fill(dsInventaire, "LOT_ARTICLE_SELECTION")
                cbInventaire = New SqlCommandBuilder(daInventaire)
                For j = 0 To dsInventaire.Tables("LOT_ARTICLE_SELECTION").Rows.Count - 1
                    NouvelArticleEnregistrement("NumeroInventaire") = NouvelArticle("NumeroInventaire")
                    NouvelArticleEnregistrement("CodeArticle") = NouvelArticle("CodeArticle")
                    NouvelArticleEnregistrement("Designation") = NouvelArticle("Designation")
                    NouvelArticleEnregistrement("NumeroLot") = dsInventaire.Tables("LOT_ARTICLE_SELECTION").Rows(j).Item("NumeroLotArticle")
                    NouvelArticleEnregistrement("CodeForme") = NouvelArticle("CodeForme")
                    NouvelArticleEnregistrement("LibelleForme") = NouvelArticle("LibelleForme")
                    NouvelArticleEnregistrement("Rayon") = NouvelArticle("Rayon")
                    NouvelArticleEnregistrement("StockInitial") = dsInventaire.Tables("LOT_ARTICLE_SELECTION").Rows(j).Item("QteLotArticle")
                    NouvelArticleEnregistrement("StockActuel") = dsInventaire.Tables("LOT_ARTICLE_SELECTION").Rows(j).Item("QteLotArticle")
                    NouvelArticleEnregistrement("PrixAchatTTC") = NouvelArticle("PrixAchatTTC")
                    NouvelArticleEnregistrement("PrixVenteTTC") = NouvelArticle("PrixVenteTTC")
                    NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                    NouvelArticleEnregistrement("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                    NouvelArticleEnregistrement("CodeArticle") = ""
                    dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)
                Next
            Else
                gInventaire.Columns("CodeArticle").Value = ""
                'gArticles.Col = 3
                gInventaire.Col = 2
            End If
        Next
    End Sub

    Private Sub fInventaireLectureTerminal_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Me.KeyDown
        If e.KeyCode = Keys.F4 And bSupprimeArticle.Enabled = True Then
            bSupprimeArticle_Click(sender, e)
        End If
        If e.KeyCode = Keys.F1 And bAjoutArticle.Enabled = True Then
            bAjoutArticle_Click(sender, e)
        End If
        If e.KeyCode = Keys.F3 And bValider.Enabled = True Then
            bValider_Click(sender, e)
        End If

        If e.KeyCode = Keys.F5 And bLire.Enabled = True Then
            bLire_Click(sender, e)
        End If
        If e.KeyCode = Keys.F12 And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

End Class