﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fReglementMutuelle
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fReglementMutuelle))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.gAchats = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bconfirmerReglement = New C1.Win.C1Input.C1Button()
        Me.bannulerReglement = New C1.Win.C1Input.C1Button()
        Me.GroupBox13 = New System.Windows.Forms.GroupBox()
        Me.lNumeroReglement = New System.Windows.Forms.Label()
        Me.dtEcheance = New C1.Win.C1Input.C1DateEdit()
        Me.tNomInscrit = New C1.Win.C1Input.C1TextBox()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.cmbBanque = New C1.Win.C1List.C1Combo()
        Me.Label19 = New System.Windows.Forms.Label()
        Me.tLibelle = New C1.Win.C1Input.C1TextBox()
        Me.chbEncaisse = New System.Windows.Forms.CheckBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.tMontant = New C1.Win.C1Input.C1TextBox()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.tNumeroCheque = New C1.Win.C1Input.C1TextBox()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.cmbNature = New C1.Win.C1List.C1Combo()
        Me.Label24 = New System.Windows.Forms.Label()
        Me.Panel.SuspendLayout()
        CType(Me.gAchats, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox13.SuspendLayout()
        CType(Me.dtEcheance, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNomInscrit, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbBanque, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tLibelle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tMontant, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNumeroCheque, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbNature, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.gAchats)
        Me.Panel.Controls.Add(Me.bconfirmerReglement)
        Me.Panel.Controls.Add(Me.bannulerReglement)
        Me.Panel.Controls.Add(Me.GroupBox13)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(835, 416)
        Me.Panel.TabIndex = 2
        '
        'gAchats
        '
        Me.gAchats.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.gAchats.FetchRowStyles = True
        Me.gAchats.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gAchats.GroupByCaption = "Drag a column header here to group by that column"
        Me.gAchats.Images.Add(CType(resources.GetObject("gAchats.Images"), System.Drawing.Image))
        Me.gAchats.LinesPerRow = 2
        Me.gAchats.Location = New System.Drawing.Point(3, 152)
        Me.gAchats.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gAchats.Name = "gAchats"
        Me.gAchats.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gAchats.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gAchats.PreviewInfo.ZoomFactor = 75.0R
        Me.gAchats.PrintInfo.PageSettings = CType(resources.GetObject("gAchats.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gAchats.Size = New System.Drawing.Size(828, 197)
        Me.gAchats.TabIndex = 70
        Me.gAchats.Text = "C1TrueDBGrid1"
        Me.gAchats.PropBag = resources.GetString("gAchats.PropBag")
        '
        'bconfirmerReglement
        '
        Me.bconfirmerReglement.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bconfirmerReglement.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bconfirmerReglement.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bconfirmerReglement.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bconfirmerReglement.Location = New System.Drawing.Point(602, 359)
        Me.bconfirmerReglement.Name = "bconfirmerReglement"
        Me.bconfirmerReglement.Size = New System.Drawing.Size(113, 45)
        Me.bconfirmerReglement.TabIndex = 67
        Me.bconfirmerReglement.Text = "Confirmer            F3"
        Me.bconfirmerReglement.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bconfirmerReglement.UseVisualStyleBackColor = True
        Me.bconfirmerReglement.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bannulerReglement
        '
        Me.bannulerReglement.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bannulerReglement.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bannulerReglement.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bannulerReglement.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bannulerReglement.Location = New System.Drawing.Point(719, 359)
        Me.bannulerReglement.Name = "bannulerReglement"
        Me.bannulerReglement.Size = New System.Drawing.Size(111, 45)
        Me.bannulerReglement.TabIndex = 68
        Me.bannulerReglement.Text = "Annuler F10"
        Me.bannulerReglement.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bannulerReglement.UseVisualStyleBackColor = True
        Me.bannulerReglement.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox13
        '
        Me.GroupBox13.Controls.Add(Me.lNumeroReglement)
        Me.GroupBox13.Controls.Add(Me.dtEcheance)
        Me.GroupBox13.Controls.Add(Me.tNomInscrit)
        Me.GroupBox13.Controls.Add(Me.Label20)
        Me.GroupBox13.Controls.Add(Me.cmbBanque)
        Me.GroupBox13.Controls.Add(Me.Label19)
        Me.GroupBox13.Controls.Add(Me.tLibelle)
        Me.GroupBox13.Controls.Add(Me.chbEncaisse)
        Me.GroupBox13.Controls.Add(Me.Label2)
        Me.GroupBox13.Controls.Add(Me.tMontant)
        Me.GroupBox13.Controls.Add(Me.Label18)
        Me.GroupBox13.Controls.Add(Me.tNumeroCheque)
        Me.GroupBox13.Controls.Add(Me.Label15)
        Me.GroupBox13.Controls.Add(Me.Label13)
        Me.GroupBox13.Controls.Add(Me.cmbNature)
        Me.GroupBox13.Controls.Add(Me.Label24)
        Me.GroupBox13.Location = New System.Drawing.Point(3, 12)
        Me.GroupBox13.Name = "GroupBox13"
        Me.GroupBox13.Size = New System.Drawing.Size(828, 126)
        Me.GroupBox13.TabIndex = 50
        Me.GroupBox13.TabStop = False
        '
        'lNumeroReglement
        '
        Me.lNumeroReglement.AutoSize = True
        Me.lNumeroReglement.Font = New System.Drawing.Font("Calibri", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNumeroReglement.Location = New System.Drawing.Point(4, 9)
        Me.lNumeroReglement.Name = "lNumeroReglement"
        Me.lNumeroReglement.Size = New System.Drawing.Size(10, 13)
        Me.lNumeroReglement.TabIndex = 51
        Me.lNumeroReglement.Text = "-"
        '
        'dtEcheance
        '
        Me.dtEcheance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtEcheance.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtEcheance.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtEcheance.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtEcheance.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtEcheance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.dtEcheance.Location = New System.Drawing.Point(304, 26)
        Me.dtEcheance.Name = "dtEcheance"
        Me.dtEcheance.Size = New System.Drawing.Size(115, 18)
        Me.dtEcheance.TabIndex = 1
        Me.dtEcheance.Tag = Nothing
        Me.dtEcheance.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtEcheance.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tNomInscrit
        '
        Me.tNomInscrit.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNomInscrit.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNomInscrit.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNomInscrit.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNomInscrit.Location = New System.Drawing.Point(590, 28)
        Me.tNomInscrit.Name = "tNomInscrit"
        Me.tNomInscrit.Size = New System.Drawing.Size(192, 18)
        Me.tNomInscrit.TabIndex = 5
        Me.tNomInscrit.Tag = Nothing
        Me.tNomInscrit.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNomInscrit.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label20
        '
        Me.Label20.AutoSize = True
        Me.Label20.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label20.Location = New System.Drawing.Point(458, 30)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(126, 13)
        Me.Label20.TabIndex = 49
        Me.Label20.Text = "Nom inscrit sur le chèque"
        '
        'cmbBanque
        '
        Me.cmbBanque.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbBanque.Caption = ""
        Me.cmbBanque.CaptionHeight = 17
        Me.cmbBanque.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbBanque.ColumnCaptionHeight = 17
        Me.cmbBanque.ColumnFooterHeight = 17
        Me.cmbBanque.ContentHeight = 16
        Me.cmbBanque.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbBanque.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbBanque.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbBanque.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbBanque.EditorHeight = 16
        Me.cmbBanque.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbBanque.Images.Add(CType(resources.GetObject("cmbBanque.Images"), System.Drawing.Image))
        Me.cmbBanque.ItemHeight = 15
        Me.cmbBanque.Location = New System.Drawing.Point(64, 59)
        Me.cmbBanque.MatchEntryTimeout = CType(2000, Long)
        Me.cmbBanque.MaxDropDownItems = CType(5, Short)
        Me.cmbBanque.MaxLength = 32767
        Me.cmbBanque.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbBanque.Name = "cmbBanque"
        Me.cmbBanque.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbBanque.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbBanque.Size = New System.Drawing.Size(106, 22)
        Me.cmbBanque.TabIndex = 2
        Me.cmbBanque.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbBanque.PropBag = resources.GetString("cmbBanque.PropBag")
        '
        'Label19
        '
        Me.Label19.AutoSize = True
        Me.Label19.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label19.Location = New System.Drawing.Point(14, 64)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(44, 13)
        Me.Label19.TabIndex = 48
        Me.Label19.Text = "Banque"
        '
        'tLibelle
        '
        Me.tLibelle.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tLibelle.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tLibelle.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tLibelle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tLibelle.Location = New System.Drawing.Point(64, 95)
        Me.tLibelle.Name = "tLibelle"
        Me.tLibelle.Size = New System.Drawing.Size(410, 18)
        Me.tLibelle.TabIndex = 4
        Me.tLibelle.Tag = Nothing
        Me.tLibelle.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tLibelle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'chbEncaisse
        '
        Me.chbEncaisse.AutoSize = True
        Me.chbEncaisse.Enabled = False
        Me.chbEncaisse.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chbEncaisse.Location = New System.Drawing.Point(625, 99)
        Me.chbEncaisse.Name = "chbEncaisse"
        Me.chbEncaisse.Size = New System.Drawing.Size(69, 17)
        Me.chbEncaisse.TabIndex = 7
        Me.chbEncaisse.Text = "Encaissé"
        Me.chbEncaisse.UseVisualStyleBackColor = True
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(21, 97)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(37, 13)
        Me.Label2.TabIndex = 2
        Me.Label2.Text = "Libellé"
        '
        'tMontant
        '
        Me.tMontant.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tMontant.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMontant.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tMontant.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tMontant.Location = New System.Drawing.Point(648, 61)
        Me.tMontant.Name = "tMontant"
        Me.tMontant.Size = New System.Drawing.Size(108, 18)
        Me.tMontant.TabIndex = 6
        Me.tMontant.Tag = Nothing
        Me.tMontant.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tMontant.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label18
        '
        Me.Label18.AutoSize = True
        Me.Label18.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label18.Location = New System.Drawing.Point(596, 63)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(46, 13)
        Me.Label18.TabIndex = 44
        Me.Label18.Text = "Montant"
        '
        'tNumeroCheque
        '
        Me.tNumeroCheque.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNumeroCheque.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNumeroCheque.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNumeroCheque.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNumeroCheque.Location = New System.Drawing.Point(304, 59)
        Me.tNumeroCheque.Name = "tNumeroCheque"
        Me.tNumeroCheque.Size = New System.Drawing.Size(115, 18)
        Me.tNumeroCheque.TabIndex = 3
        Me.tNumeroCheque.Tag = Nothing
        Me.tNumeroCheque.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNumeroCheque.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label15.Location = New System.Drawing.Point(217, 61)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(83, 13)
        Me.Label15.TabIndex = 42
        Me.Label15.Text = "Numéro chèque"
        '
        'Label13
        '
        Me.Label13.AutoSize = True
        Me.Label13.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label13.Location = New System.Drawing.Point(242, 29)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(56, 13)
        Me.Label13.TabIndex = 40
        Me.Label13.Text = "Echéance"
        '
        'cmbNature
        '
        Me.cmbNature.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbNature.Caption = ""
        Me.cmbNature.CaptionHeight = 17
        Me.cmbNature.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbNature.ColumnCaptionHeight = 17
        Me.cmbNature.ColumnFooterHeight = 17
        Me.cmbNature.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.cmbNature.ContentHeight = 16
        Me.cmbNature.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbNature.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbNature.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbNature.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbNature.EditorHeight = 16
        Me.cmbNature.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbNature.Images.Add(CType(resources.GetObject("cmbNature.Images"), System.Drawing.Image))
        Me.cmbNature.ItemHeight = 15
        Me.cmbNature.Location = New System.Drawing.Point(64, 23)
        Me.cmbNature.MatchEntryTimeout = CType(2000, Long)
        Me.cmbNature.MaxDropDownItems = CType(5, Short)
        Me.cmbNature.MaxLength = 32767
        Me.cmbNature.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbNature.Name = "cmbNature"
        Me.cmbNature.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbNature.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbNature.Size = New System.Drawing.Size(106, 22)
        Me.cmbNature.TabIndex = 0
        Me.cmbNature.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbNature.PropBag = resources.GetString("cmbNature.PropBag")
        '
        'Label24
        '
        Me.Label24.AutoSize = True
        Me.Label24.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label24.Location = New System.Drawing.Point(19, 28)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(39, 13)
        Me.Label24.TabIndex = 39
        Me.Label24.Text = "Nature"
        '
        'fReglementMutuelle
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(835, 416)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None
        Me.Name = "fReglementMutuelle"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "fReglementMutuelle"
        Me.Panel.ResumeLayout(False)
        CType(Me.gAchats, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox13.ResumeLayout(False)
        Me.GroupBox13.PerformLayout()
        CType(Me.dtEcheance, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNomInscrit, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbBanque, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tLibelle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tMontant, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNumeroCheque, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbNature, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents gAchats As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bconfirmerReglement As C1.Win.C1Input.C1Button
    Friend WithEvents bannulerReglement As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox13 As System.Windows.Forms.GroupBox
    Friend WithEvents lNumeroReglement As System.Windows.Forms.Label
    Friend WithEvents dtEcheance As C1.Win.C1Input.C1DateEdit
    Friend WithEvents tNomInscrit As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label20 As System.Windows.Forms.Label
    Friend WithEvents cmbBanque As C1.Win.C1List.C1Combo
    Friend WithEvents Label19 As System.Windows.Forms.Label
    Friend WithEvents tLibelle As C1.Win.C1Input.C1TextBox
    Friend WithEvents chbEncaisse As System.Windows.Forms.CheckBox
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents tMontant As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label18 As System.Windows.Forms.Label
    Friend WithEvents tNumeroCheque As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents cmbNature As C1.Win.C1List.C1Combo
    Friend WithEvents Label24 As System.Windows.Forms.Label
End Class
