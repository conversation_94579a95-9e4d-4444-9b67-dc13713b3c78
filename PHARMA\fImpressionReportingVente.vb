﻿Public Class fImpressionReportingVente

    Public WithEvents CrystalReportViewer1 As New CrystalDecisions.Windows.Forms.CrystalReportViewer

    Private Sub fImpressionReportingVente_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Try
            Panel1.Controls.Add(ReportViewer1)
            Panel1.Controls.Add(CrystalReportViewer1)

            ReportViewer1.Dock = DockStyle.Fill
            CrystalReportViewer1.Dock = DockStyle.Fill

            ReportViewer1.SetDisplayMode(Microsoft.Reporting.WinForms.DisplayMode.PrintLayout)
            ReportViewer1.ZoomMode = Microsoft.Reporting.WinForms.ZoomMode.PageWidth
            
            CrystalReportViewer1.ShowCloseButton = False
            CrystalReportViewer1.ShowGroupTreeButton = False
            CrystalReportViewer1.ShowRefreshButton = False

        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
End Class