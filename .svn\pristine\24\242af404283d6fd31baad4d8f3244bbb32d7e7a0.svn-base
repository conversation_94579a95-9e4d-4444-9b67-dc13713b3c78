﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Public Class fChangerLoginMotDePasse
    Public CodeUtilisateur = ""
    Public NomUtilisateur As String = ""
    Dim cbMotDePasse As New SqlCommandBuilder
    Dim dsMotDePasse As New DataSet
    Dim daMotDePasse As New SqlDataAdapter
    Dim cmdMotDePasse As New SqlCommand
    Public Mode As String = ""

    Private Sub fChangerLoginMotDePasse_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim StrSQL As String = ""

        lTitre.Text = "Changement du mot de passe de : " + NomUtilisateur

        StrSQL = "SELECT * FROM UTILISATEUR WHERE CodeUtilisateur =" + Quote(CodeUtilisateur)

        cmdMotDePasse.Connection = ConnectionServeur
        cmdMotDePasse.CommandText = StrSQL
        daMotDePasse = New SqlDataAdapter(cmdMotDePasse)
        daMotDePasse.Fill(dsMotDePasse, "PASSE")
        cbMotDePasse = New SqlCommandBuilder(daMotDePasse)

        'liaison

        tPasse.Text = "" ' dsPharmacien.Tables("PASSE").Rows(0)("MotPass")
        tPasseConfirmer.Text = "" ' dsPharmacien.Tables("PASSE").Rows(0)("MotPass")

        tLogIn.Value = dsMotDePasse.Tables("PASSE").Rows(0)("Login")
        tAncienPasse.Value = dsMotDePasse.Tables("PASSE").Rows(0)("MotPass")

        chbAfficherCaracteres.Checked = True

        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler

    End Sub
    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)
        If e.KeyCode = Keys.F3 Then
            bOk_Click(o, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(o, e)
            Exit Sub
        End If
    End Sub

    Private Sub bOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOK.Click
        Dim dr As DataRow
        
        If tPasse.Text = "" Then
            MsgBox("Veuillez saisir le nouveau mot de passe !", MsgBoxStyle.Critical, "Erreur")
            tPasse.Focus()
            Exit Sub
        End If
        If tPasseConfirmer.Text = "" Then
            MsgBox("Veuillez confirmer le nouveau mot de passe !", MsgBoxStyle.Critical, "Erreur")
            tPasseConfirmer.Focus()
            Exit Sub
        End If
        'tAncienPasse.Text = dsMotDePasse.Tables("PASSE").Rows(0)("MotPass")
        'If (tAncienPasse.Text = dsMotDePasse.Tables("PASSE").Rows(0)("MotPass")) Then
        If (tPasse.Text = tPasseConfirmer.Text) Then
            With dsMotDePasse.Tables("PASSE")
                dr = .Rows(0)
                dr.Item("Login") = tLogIn.Text
                dr.Item("MotPass") = tPasse.Text
            End With
            'mise à jour de la base de données
            Try
                daMotDePasse.Update(dsMotDePasse, "PASSE")
                Me.Dispose()
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            End Try
        Else
            MsgBox("Le mot de passe et sa confirmation ne sont pas identiques !", MsgBoxStyle.Critical, "Erreur")
            tPasseConfirmer.Focus()
        End If
        'Else
        'MsgBox("Votre ancien mot de passe n'est pas valide !", MsgBoxStyle.Critical, "Erreur")
        'tAncienPasse.Focus()
        'End If

        '************************************* ancien code sans modification
        '***********************************************************************
        'If tPasse.Text = "" Then
        '    MsgBox("Veuillez saisir le nouveau mot de passe !", MsgBoxStyle.Critical, "Erreur")
        '    tPasse.Focus()
        '    Exit Sub
        'End If
        'If tPasseConfirmer.Text = "" Then
        '    MsgBox("Veuillez confirmer le nouveau mot de passe !", MsgBoxStyle.Critical, "Erreur")
        '    tPasseConfirmer.Focus()
        '    Exit Sub
        'End If

        'If (tAncienPasse.Text = dsMotDePasse.Tables("PASSE").Rows(0)("MotPass")) Then
        '    If (tPasse.Text = tPasseConfirmer.Text) Then
        '        With dsMotDePasse.Tables("PASSE")
        '            dr = .Rows(0)
        '            dr.Item("MotPass") = tPasse.Text
        '        End With
        '        'mise à jour de la base de données
        '        Try
        '            daMotDePasse.Update(dsMotDePasse, "PASSE")
        '            Me.Dispose()
        '        Catch ex As Exception
        '            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
        '        End Try
        '    Else
        '        MsgBox("Le mot de passe et sa confirmation ne sont pas identiques !", MsgBoxStyle.Critical, "Erreur")
        '        tPasseConfirmer.Focus()
        '    End If
        'Else
        '    MsgBox("Votre ancien mot de passe n'est pas valide !", MsgBoxStyle.Critical, "Erreur")
        '    tAncienPasse.Focus()
        'End If
        '***********************************************************************************************

    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        If dsMotDePasse.HasChanges Then
            If MsgBox("Voulez vous vraiment annuler ces modifications ?", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                Me.Dispose()
            End If
        Else
            Me.Dispose()
        End If
    End Sub

    Private Sub chbAfficherCaracteres_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbAfficherCaracteres.CheckedChanged
        If chbAfficherCaracteres.Checked = True Then
            tAncienPasse.PasswordChar = "*"
            tPasse.PasswordChar = "*"
            tPasseConfirmer.PasswordChar = "*"
        Else
            tAncienPasse.PasswordChar = ""
            tPasse.PasswordChar = ""
            tPasseConfirmer.PasswordChar = ""
        End If
    End Sub
End Class