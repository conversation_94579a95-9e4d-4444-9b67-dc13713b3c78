﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fPreparation
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fPreparation))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bFermer = New C1.Win.C1Input.C1Button()
        Me.gListeRecherche = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.gListeRecherche1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bVerifierPrix = New C1.Win.C1Input.C1Button()
        Me.tTotTTC = New C1.Win.C1Input.C1TextBox()
        Me.lTotArticle = New System.Windows.Forms.Label()
        Me.lTotIndemnite = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.GroupeFournisseur = New System.Windows.Forms.GroupBox()
        Me.tNbreJourValidite = New C1.Win.C1Input.C1TextBox()
        Me.tQuantiteUnitaire = New C1.Win.C1Input.C1TextBox()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.lTest = New System.Windows.Forms.Label()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.tDesignation = New C1.Win.C1Input.C1TextBox()
        Me.tCodeArticle = New C1.Win.C1Input.C1TextBox()
        Me.tNumeroOrdonnance = New C1.Win.C1Input.C1TextBox()
        Me.lDate = New System.Windows.Forms.Label()
        Me.lType = New System.Windows.Forms.Label()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.lMatricule = New System.Windows.Forms.Label()
        Me.bProduire = New C1.Win.C1Input.C1Button()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.gIndemnites = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.bConfirmer = New C1.Win.C1Input.C1Button()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.gArticles = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bIndemnites = New C1.Win.C1Input.C1Button()
        Me.bSupprimer = New C1.Win.C1Input.C1Button()
        Me.Panel.SuspendLayout()
        CType(Me.gListeRecherche, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gListeRecherche1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tTotTTC, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupeFournisseur.SuspendLayout()
        CType(Me.tNbreJourValidite, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tQuantiteUnitaire, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDesignation, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodeArticle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNumeroOrdonnance, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gIndemnites, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.bFermer)
        Me.Panel.Controls.Add(Me.gListeRecherche)
        Me.Panel.Controls.Add(Me.gListeRecherche1)
        Me.Panel.Controls.Add(Me.bVerifierPrix)
        Me.Panel.Controls.Add(Me.tTotTTC)
        Me.Panel.Controls.Add(Me.lTotArticle)
        Me.Panel.Controls.Add(Me.lTotIndemnite)
        Me.Panel.Controls.Add(Me.Label1)
        Me.Panel.Controls.Add(Me.GroupeFournisseur)
        Me.Panel.Controls.Add(Me.bProduire)
        Me.Panel.Controls.Add(Me.Label8)
        Me.Panel.Controls.Add(Me.Label7)
        Me.Panel.Controls.Add(Me.Label6)
        Me.Panel.Controls.Add(Me.gIndemnites)
        Me.Panel.Controls.Add(Me.Label3)
        Me.Panel.Controls.Add(Me.bConfirmer)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.Label2)
        Me.Panel.Controls.Add(Me.gArticles)
        Me.Panel.Controls.Add(Me.bIndemnites)
        Me.Panel.Controls.Add(Me.bSupprimer)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1262, 606)
        Me.Panel.TabIndex = 1
        '
        'bFermer
        '
        Me.bFermer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bFermer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bFermer.Image = Global.Pharma2000Premium.My.Resources.Resources.afermer
        Me.bFermer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bFermer.Location = New System.Drawing.Point(530, 562)
        Me.bFermer.Name = "bFermer"
        Me.bFermer.Size = New System.Drawing.Size(79, 34)
        Me.bFermer.TabIndex = 87
        Me.bFermer.Text = "Fermer" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "   F12"
        Me.bFermer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bFermer.UseVisualStyleBackColor = True
        Me.bFermer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gListeRecherche
        '
        Me.gListeRecherche.AllowUpdate = False
        Me.gListeRecherche.AlternatingRows = True
        Me.gListeRecherche.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gListeRecherche.GroupByCaption = "Drag a column header here to group by that column"
        Me.gListeRecherche.Images.Add(CType(resources.GetObject("gListeRecherche.Images"), System.Drawing.Image))
        Me.gListeRecherche.LinesPerRow = 2
        Me.gListeRecherche.Location = New System.Drawing.Point(114, 204)
        Me.gListeRecherche.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gListeRecherche.Name = "gListeRecherche"
        Me.gListeRecherche.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gListeRecherche.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gListeRecherche.PreviewInfo.ZoomFactor = 75.0R
        Me.gListeRecherche.PrintInfo.PageSettings = CType(resources.GetObject("gListeRecherche.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gListeRecherche.Size = New System.Drawing.Size(647, 148)
        Me.gListeRecherche.TabIndex = 65
        Me.gListeRecherche.Text = "C1TrueDBGrid1"
        Me.gListeRecherche.Visible = False
        Me.gListeRecherche.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gListeRecherche.PropBag = resources.GetString("gListeRecherche.PropBag")
        '
        'gListeRecherche1
        '
        Me.gListeRecherche1.AllowUpdate = False
        Me.gListeRecherche1.AlternatingRows = True
        Me.gListeRecherche1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gListeRecherche1.GroupByCaption = "Drag a column header here to group by that column"
        Me.gListeRecherche1.Images.Add(CType(resources.GetObject("gListeRecherche1.Images"), System.Drawing.Image))
        Me.gListeRecherche1.LinesPerRow = 2
        Me.gListeRecherche1.Location = New System.Drawing.Point(791, 205)
        Me.gListeRecherche1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gListeRecherche1.Name = "gListeRecherche1"
        Me.gListeRecherche1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gListeRecherche1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gListeRecherche1.PreviewInfo.ZoomFactor = 75.0R
        Me.gListeRecherche1.PrintInfo.PageSettings = CType(resources.GetObject("gListeRecherche1.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gListeRecherche1.Size = New System.Drawing.Size(402, 148)
        Me.gListeRecherche1.TabIndex = 82
        Me.gListeRecherche1.Text = "C1TrueDBGrid1"
        Me.gListeRecherche1.Visible = False
        Me.gListeRecherche1.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gListeRecherche1.PropBag = resources.GetString("gListeRecherche1.PropBag")
        '
        'bVerifierPrix
        '
        Me.bVerifierPrix.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bVerifierPrix.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bVerifierPrix.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bVerifierPrix.Location = New System.Drawing.Point(273, 562)
        Me.bVerifierPrix.Name = "bVerifierPrix"
        Me.bVerifierPrix.Size = New System.Drawing.Size(79, 34)
        Me.bVerifierPrix.TabIndex = 84
        Me.bVerifierPrix.Text = "Verifier les prix "
        Me.bVerifierPrix.UseVisualStyleBackColor = True
        Me.bVerifierPrix.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tTotTTC
        '
        Me.tTotTTC.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tTotTTC.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tTotTTC.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTotTTC.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tTotTTC.Location = New System.Drawing.Point(1152, 566)
        Me.tTotTTC.Name = "tTotTTC"
        Me.tTotTTC.Size = New System.Drawing.Size(96, 18)
        Me.tTotTTC.TabIndex = 83
        Me.tTotTTC.Tag = Nothing
        Me.tTotTTC.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.tTotTTC.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tTotTTC.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lTotArticle
        '
        Me.lTotArticle.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lTotArticle.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lTotArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotArticle.Location = New System.Drawing.Point(518, 532)
        Me.lTotArticle.Name = "lTotArticle"
        Me.lTotArticle.Size = New System.Drawing.Size(78, 20)
        Me.lTotArticle.TabIndex = 80
        Me.lTotArticle.Text = "0.000"
        Me.lTotArticle.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lTotIndemnite
        '
        Me.lTotIndemnite.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lTotIndemnite.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lTotIndemnite.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotIndemnite.Location = New System.Drawing.Point(1152, 533)
        Me.lTotIndemnite.Name = "lTotIndemnite"
        Me.lTotIndemnite.Size = New System.Drawing.Size(96, 20)
        Me.lTotIndemnite.TabIndex = 79
        Me.lTotIndemnite.Text = "0.000"
        Me.lTotIndemnite.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("HandelGotDLig", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(13, -2)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(996, 46)
        Me.Label1.TabIndex = 78
        Me.Label1.Text = "PREPARATION"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'GroupeFournisseur
        '
        Me.GroupeFournisseur.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupeFournisseur.Controls.Add(Me.tNbreJourValidite)
        Me.GroupeFournisseur.Controls.Add(Me.tQuantiteUnitaire)
        Me.GroupeFournisseur.Controls.Add(Me.Label13)
        Me.GroupeFournisseur.Controls.Add(Me.lTest)
        Me.GroupeFournisseur.Controls.Add(Me.Label14)
        Me.GroupeFournisseur.Controls.Add(Me.Label11)
        Me.GroupeFournisseur.Controls.Add(Me.tDesignation)
        Me.GroupeFournisseur.Controls.Add(Me.tCodeArticle)
        Me.GroupeFournisseur.Controls.Add(Me.tNumeroOrdonnance)
        Me.GroupeFournisseur.Controls.Add(Me.lDate)
        Me.GroupeFournisseur.Controls.Add(Me.lType)
        Me.GroupeFournisseur.Controls.Add(Me.Label12)
        Me.GroupeFournisseur.Controls.Add(Me.Label4)
        Me.GroupeFournisseur.Controls.Add(Me.Label10)
        Me.GroupeFournisseur.Controls.Add(Me.lMatricule)
        Me.GroupeFournisseur.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeFournisseur.Font = New System.Drawing.Font("Calibri", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupeFournisseur.Location = New System.Drawing.Point(12, 42)
        Me.GroupeFournisseur.Name = "GroupeFournisseur"
        Me.GroupeFournisseur.Size = New System.Drawing.Size(1236, 95)
        Me.GroupeFournisseur.TabIndex = 0
        Me.GroupeFournisseur.TabStop = False
        '
        'tNbreJourValidite
        '
        Me.tNbreJourValidite.AutoSize = False
        Me.tNbreJourValidite.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNbreJourValidite.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNbreJourValidite.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNbreJourValidite.Location = New System.Drawing.Point(143, 60)
        Me.tNbreJourValidite.Name = "tNbreJourValidite"
        Me.tNbreJourValidite.Size = New System.Drawing.Size(51, 22)
        Me.tNbreJourValidite.TabIndex = 75
        Me.tNbreJourValidite.Tag = Nothing
        Me.tNbreJourValidite.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.tNbreJourValidite.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNbreJourValidite.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tQuantiteUnitaire
        '
        Me.tQuantiteUnitaire.AutoSize = False
        Me.tQuantiteUnitaire.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tQuantiteUnitaire.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tQuantiteUnitaire.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tQuantiteUnitaire.Location = New System.Drawing.Point(508, 60)
        Me.tQuantiteUnitaire.Name = "tQuantiteUnitaire"
        Me.tQuantiteUnitaire.Size = New System.Drawing.Size(94, 22)
        Me.tQuantiteUnitaire.TabIndex = 74
        Me.tQuantiteUnitaire.Tag = Nothing
        Me.tQuantiteUnitaire.Value = "1"
        Me.tQuantiteUnitaire.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tQuantiteUnitaire.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label13
        '
        Me.Label13.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label13.Location = New System.Drawing.Point(402, 60)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(100, 17)
        Me.Label13.TabIndex = 73
        Me.Label13.Text = "Quantité unitaire :"
        Me.Label13.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lTest
        '
        Me.lTest.AutoSize = True
        Me.lTest.BackColor = System.Drawing.Color.Transparent
        Me.lTest.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTest.ForeColor = System.Drawing.Color.Black
        Me.lTest.Location = New System.Drawing.Point(264, 39)
        Me.lTest.Name = "lTest"
        Me.lTest.Size = New System.Drawing.Size(19, 13)
        Me.lTest.TabIndex = 72
        Me.lTest.Text = "55"
        Me.lTest.Visible = False
        '
        'Label14
        '
        Me.Label14.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label14.Location = New System.Drawing.Point(8, 56)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(125, 29)
        Me.Label14.TabIndex = 71
        Me.Label14.Text = " Nombre Jours Validité :"
        Me.Label14.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label11
        '
        Me.Label11.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label11.Location = New System.Drawing.Point(423, 36)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(70, 17)
        Me.Label11.TabIndex = 71
        Me.Label11.Text = "Désignation :"
        Me.Label11.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tDesignation
        '
        Me.tDesignation.AutoSize = False
        Me.tDesignation.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tDesignation.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDesignation.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDesignation.Location = New System.Drawing.Point(508, 34)
        Me.tDesignation.Name = "tDesignation"
        Me.tDesignation.Size = New System.Drawing.Size(333, 22)
        Me.tDesignation.TabIndex = 70
        Me.tDesignation.Tag = Nothing
        Me.tDesignation.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDesignation.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tCodeArticle
        '
        Me.tCodeArticle.AutoSize = False
        Me.tCodeArticle.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodeArticle.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodeArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodeArticle.Location = New System.Drawing.Point(144, 34)
        Me.tCodeArticle.Name = "tCodeArticle"
        Me.tCodeArticle.Size = New System.Drawing.Size(114, 22)
        Me.tCodeArticle.TabIndex = 1
        Me.tCodeArticle.Tag = Nothing
        Me.tCodeArticle.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodeArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tNumeroOrdonnance
        '
        Me.tNumeroOrdonnance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNumeroOrdonnance.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNumeroOrdonnance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNumeroOrdonnance.Location = New System.Drawing.Point(767, 59)
        Me.tNumeroOrdonnance.Name = "tNumeroOrdonnance"
        Me.tNumeroOrdonnance.Size = New System.Drawing.Size(74, 18)
        Me.tNumeroOrdonnance.TabIndex = 68
        Me.tNumeroOrdonnance.Tag = Nothing
        Me.tNumeroOrdonnance.Visible = False
        Me.tNumeroOrdonnance.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNumeroOrdonnance.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lDate
        '
        Me.lDate.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lDate.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDate.Location = New System.Drawing.Point(144, 13)
        Me.lDate.Name = "lDate"
        Me.lDate.Size = New System.Drawing.Size(170, 17)
        Me.lDate.TabIndex = 64
        Me.lDate.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lType
        '
        Me.lType.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lType.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lType.Location = New System.Drawing.Point(508, 13)
        Me.lType.Name = "lType"
        Me.lType.Size = New System.Drawing.Size(333, 17)
        Me.lType.TabIndex = 63
        Me.lType.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label12
        '
        Me.Label12.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label12.Location = New System.Drawing.Point(638, 59)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(123, 17)
        Me.Label12.TabIndex = 67
        Me.Label12.Text = "Numero Ordonnance :"
        Me.Label12.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label12.Visible = False
        '
        'Label4
        '
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.Location = New System.Drawing.Point(87, 39)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(40, 17)
        Me.Label4.TabIndex = 62
        Me.Label4.Text = "Code :"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label10
        '
        Me.Label10.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label10.Location = New System.Drawing.Point(88, 16)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(40, 17)
        Me.Label10.TabIndex = 58
        Me.Label10.Text = "Date :"
        Me.Label10.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lMatricule
        '
        Me.lMatricule.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lMatricule.Location = New System.Drawing.Point(455, 13)
        Me.lMatricule.Name = "lMatricule"
        Me.lMatricule.Size = New System.Drawing.Size(61, 17)
        Me.lMatricule.TabIndex = 56
        Me.lMatricule.Text = "Type :"
        Me.lMatricule.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'bProduire
        '
        Me.bProduire.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bProduire.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bProduire.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bProduire.Location = New System.Drawing.Point(359, 562)
        Me.bProduire.Name = "bProduire"
        Me.bProduire.Size = New System.Drawing.Size(79, 34)
        Me.bProduire.TabIndex = 75
        Me.bProduire.Text = "Produire " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "    F8"
        Me.bProduire.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bProduire.UseVisualStyleBackColor = True
        Me.bProduire.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label8
        '
        Me.Label8.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.Location = New System.Drawing.Point(1084, 568)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(62, 13)
        Me.Label8.TabIndex = 74
        Me.Label8.Text = "Total TTC :"
        Me.Label8.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label7
        '
        Me.Label7.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.Location = New System.Drawing.Point(1028, 539)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(118, 13)
        Me.Label7.TabIndex = 72
        Me.Label7.Text = "Total des indémnités :"
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.Location = New System.Drawing.Point(958, 150)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(163, 13)
        Me.Label6.TabIndex = 69
        Me.Label6.Text = "Indemnités"
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'gIndemnites
        '
        Me.gIndemnites.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gIndemnites.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gIndemnites.GroupByCaption = "Drag a column header here to group by that column"
        Me.gIndemnites.Images.Add(CType(resources.GetObject("gIndemnites.Images"), System.Drawing.Image))
        Me.gIndemnites.LinesPerRow = 2
        Me.gIndemnites.Location = New System.Drawing.Point(778, 175)
        Me.gIndemnites.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
        Me.gIndemnites.Name = "gIndemnites"
        Me.gIndemnites.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gIndemnites.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gIndemnites.PreviewInfo.ZoomFactor = 75.0R
        Me.gIndemnites.PrintInfo.PageSettings = CType(resources.GetObject("gIndemnites.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gIndemnites.Size = New System.Drawing.Size(470, 343)
        Me.gIndemnites.TabIndex = 68
        Me.gIndemnites.Text = "C1TrueDBGrid1"
        Me.gIndemnites.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gIndemnites.PropBag = resources.GetString("gIndemnites.PropBag")
        '
        'Label3
        '
        Me.Label3.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.Location = New System.Drawing.Point(419, 534)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(99, 13)
        Me.Label3.TabIndex = 66
        Me.Label3.Text = "Total des articles :"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'bConfirmer
        '
        Me.bConfirmer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bConfirmer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmer.Image = Global.Pharma2000Premium.My.Resources.Resources.avalider
        Me.bConfirmer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmer.Location = New System.Drawing.Point(188, 562)
        Me.bConfirmer.Name = "bConfirmer"
        Me.bConfirmer.Size = New System.Drawing.Size(79, 34)
        Me.bConfirmer.TabIndex = 66
        Me.bConfirmer.Text = "Confirmer" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "     F3"
        Me.bConfirmer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bConfirmer.UseVisualStyleBackColor = True
        Me.bConfirmer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.aannuler
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(445, 562)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(79, 34)
        Me.bAnnuler.TabIndex = 67
        Me.bAnnuler.Text = "Annuler " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "   F10"
        Me.bAnnuler.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label2
        '
        Me.Label2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(198, 150)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(402, 13)
        Me.Label2.TabIndex = 65
        Me.Label2.Text = "Composant"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'gArticles
        '
        Me.gArticles.AllowAddNew = True
        Me.gArticles.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gArticles.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gArticles.GroupByCaption = "Drag a column header here to group by that column"
        Me.gArticles.Images.Add(CType(resources.GetObject("gArticles.Images"), System.Drawing.Image))
        Me.gArticles.LinesPerRow = 2
        Me.gArticles.Location = New System.Drawing.Point(12, 175)
        Me.gArticles.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
        Me.gArticles.Name = "gArticles"
        Me.gArticles.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gArticles.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gArticles.PreviewInfo.ZoomFactor = 75.0R
        Me.gArticles.PrintInfo.PageSettings = CType(resources.GetObject("gArticles.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gArticles.Size = New System.Drawing.Size(761, 343)
        Me.gArticles.TabIndex = 5
        Me.gArticles.Text = "C1TrueDBGrid1"
        Me.gArticles.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gArticles.PropBag = resources.GetString("gArticles.PropBag")
        '
        'bIndemnites
        '
        Me.bIndemnites.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bIndemnites.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bIndemnites.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bIndemnites.Location = New System.Drawing.Point(102, 562)
        Me.bIndemnites.Name = "bIndemnites"
        Me.bIndemnites.Size = New System.Drawing.Size(79, 34)
        Me.bIndemnites.TabIndex = 86
        Me.bIndemnites.Text = " Indemnités " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "       F5"
        Me.bIndemnites.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bIndemnites.UseVisualStyleBackColor = True
        Me.bIndemnites.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bSupprimer
        '
        Me.bSupprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bSupprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.asupprimer
        Me.bSupprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSupprimer.Location = New System.Drawing.Point(12, 562)
        Me.bSupprimer.Name = "bSupprimer"
        Me.bSupprimer.Size = New System.Drawing.Size(83, 34)
        Me.bSupprimer.TabIndex = 85
        Me.bSupprimer.Text = "Supprimer " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "    F7"
        Me.bSupprimer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSupprimer.UseVisualStyleBackColor = True
        Me.bSupprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fPreparation
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1262, 606)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.SizableToolWindow
        Me.MaximizeBox = False
        Me.Name = "fPreparation"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        CType(Me.gListeRecherche, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gListeRecherche1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tTotTTC, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupeFournisseur.ResumeLayout(False)
        Me.GroupeFournisseur.PerformLayout()
        CType(Me.tNbreJourValidite, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tQuantiteUnitaire, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDesignation, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodeArticle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNumeroOrdonnance, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gIndemnites, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents bConfirmer As C1.Win.C1Input.C1Button
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents gListeRecherche As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents gArticles As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents gIndemnites As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents bProduire As C1.Win.C1Input.C1Button
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents GroupeFournisseur As System.Windows.Forms.GroupBox
    Friend WithEvents lDate As System.Windows.Forms.Label
    Friend WithEvents lType As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents lMatricule As System.Windows.Forms.Label
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents lTotArticle As System.Windows.Forms.Label
    Friend WithEvents lTotIndemnite As System.Windows.Forms.Label
    Friend WithEvents gListeRecherche1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid

    Friend WithEvents tNumeroOrdonnance As C1.Win.C1Input.C1TextBox
    Friend WithEvents tTotTTC As C1.Win.C1Input.C1TextBox
    Friend WithEvents bVerifierPrix As C1.Win.C1Input.C1Button
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents tDesignation As C1.Win.C1Input.C1TextBox
    Friend WithEvents tCodeArticle As C1.Win.C1Input.C1TextBox
    Friend WithEvents lTest As System.Windows.Forms.Label
    Friend WithEvents tQuantiteUnitaire As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents tNbreJourValidite As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents bSupprimer As C1.Win.C1Input.C1Button
    Friend WithEvents bIndemnites As C1.Win.C1Input.C1Button
    Friend WithEvents bFermer As C1.Win.C1Input.C1Button
End Class
