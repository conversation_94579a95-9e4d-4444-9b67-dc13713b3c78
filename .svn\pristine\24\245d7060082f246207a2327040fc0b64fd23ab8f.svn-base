﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSet_ETAT_Exception" targetNamespace="http://tempuri.org/DataSet_ETAT_Exception.xsd" xmlns:mstns="http://tempuri.org/DataSet_ETAT_Exception.xsd" xmlns="http://tempuri.org/DataSet_ETAT_Exception.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSet_ETAT_Exception" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DataSet_ETAT_Exception" msprop:Generator_UserDSName="DataSet_ETAT_Exception">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable_Etat" msprop:Generator_TableClassName="DataTable_EtatDataTable" msprop:Generator_TableVarName="tableDataTable_Etat" msprop:Generator_TablePropName="DataTable_Etat" msprop:Generator_RowDeletingName="DataTable_EtatRowDeleting" msprop:Generator_UserTableName="DataTable_Etat" msprop:Generator_RowChangingName="DataTable_EtatRowChanging" msprop:Generator_RowEvHandlerName="DataTable_EtatRowChangeEventHandler" msprop:Generator_RowDeletedName="DataTable_EtatRowDeleted" msprop:Generator_RowEvArgName="DataTable_EtatRowChangeEvent" msprop:Generator_RowChangedName="DataTable_EtatRowChanged" msprop:Generator_RowClassName="DataTable_EtatRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="img_imp" msprop:Generator_ColumnVarNameInTable="columnimg_imp" msprop:Generator_ColumnPropNameInRow="img_imp" msprop:Generator_ColumnPropNameInTable="img_impColumn" msprop:Generator_UserColumnName="img_imp" type="xs:base64Binary" minOccurs="0" />
              <xs:element name="codeDePharmacien" msprop:Generator_ColumnVarNameInTable="columncodeDePharmacien" msprop:Generator_ColumnPropNameInRow="codeDePharmacien" msprop:Generator_ColumnPropNameInTable="codeDePharmacienColumn" msprop:Generator_UserColumnName="codeDePharmacien" type="xs:string" minOccurs="0" />
              <xs:element name="nomUser" msprop:Generator_ColumnVarNameInTable="columnnomUser" msprop:Generator_ColumnPropNameInRow="nomUser" msprop:Generator_ColumnPropNameInTable="nomUserColumn" msprop:Generator_UserColumnName="nomUser" type="xs:string" minOccurs="0" />
              <xs:element name="NumeroMessageErreur" msprop:Generator_ColumnVarNameInTable="columnNumeroMessageErreur" msprop:Generator_ColumnPropNameInRow="NumeroMessageErreur" msprop:Generator_ColumnPropNameInTable="NumeroMessageErreurColumn" msprop:Generator_UserColumnName="NumeroMessageErreur" type="xs:string" minOccurs="0" />
              <xs:element name="nomDuModule" msprop:Generator_ColumnVarNameInTable="columnnomDuModule" msprop:Generator_ColumnPropNameInRow="nomDuModule" msprop:Generator_ColumnPropNameInTable="nomDuModuleColumn" msprop:Generator_UserColumnName="nomDuModule" type="xs:string" minOccurs="0" />
              <xs:element name="nomException" msprop:Generator_ColumnVarNameInTable="columnnomException" msprop:Generator_ColumnPropNameInRow="nomException" msprop:Generator_ColumnPropNameInTable="nomExceptionColumn" msprop:Generator_UserColumnName="nomException" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>