//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class MUTUELLE
    {
        public MUTUELLE()
        {
            this.REGLEMENT_MUTUELLE = new HashSet<REGLEMENT_MUTUELLE>();
            this.RELEVE_MUTUELLE = new HashSet<RELEVE_MUTUELLE>();
            this.VENTE_INSTANCE = new HashSet<VENTE_INSTANCE>();
            this.ARTICLE = new HashSet<ARTICLE>();
            this.ARTICLE1 = new HashSet<ARTICLE>();
            this.CATEGORIE = new HashSet<CATEGORIE>();
            this.CATEGORIE1 = new HashSet<CATEGORIE>();
            this.ARTICLE2 = new HashSet<ARTICLE>();
            this.CATEGORIE2 = new HashSet<CATEGORIE>();
        }
    
        public string CodeMutuelle { get; set; }
        public string NomMutuelle { get; set; }
        public decimal PriseEnCharge { get; set; }
        public string Adresse { get; set; }
        public string CodePostal { get; set; }
        public string Tel { get; set; }
        public string Fax { get; set; }
        public string Remarque { get; set; }
        public Nullable<int> CodeVille { get; set; }
        public bool AfficherLaTotaliteDuMontant { get; set; }
        public decimal SoldeInitial { get; set; }
        public System.DateTime DateInitiale { get; set; }
        public string Libelle1 { get; set; }
        public string Libelle2 { get; set; }
        public string Libelle3 { get; set; }
        public string Libelle4 { get; set; }
        public string Libelle5 { get; set; }
        public string CheminReleve { get; set; }
        public string CodePharmacien { get; set; }
        public string CodeTva { get; set; }
    
        public virtual VILLE VILLE { get; set; }
        public virtual ICollection<REGLEMENT_MUTUELLE> REGLEMENT_MUTUELLE { get; set; }
        public virtual ICollection<RELEVE_MUTUELLE> RELEVE_MUTUELLE { get; set; }
        public virtual ICollection<VENTE_INSTANCE> VENTE_INSTANCE { get; set; }
        public virtual ICollection<ARTICLE> ARTICLE { get; set; }
        public virtual ICollection<ARTICLE> ARTICLE1 { get; set; }
        public virtual ICollection<CATEGORIE> CATEGORIE { get; set; }
        public virtual ICollection<CATEGORIE> CATEGORIE1 { get; set; }
        public virtual ICollection<ARTICLE> ARTICLE2 { get; set; }
        public virtual ICollection<CATEGORIE> CATEGORIE2 { get; set; }
    }
}
