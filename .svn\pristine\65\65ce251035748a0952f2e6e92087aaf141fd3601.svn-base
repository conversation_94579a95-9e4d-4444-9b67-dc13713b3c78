﻿Imports System
Imports System.Data
Imports System.Data.SqlClient
Imports System.IO
Public Class fReceptionFichierTerminal

    Dim InfoFichier As FileInfo
    Public FileName As String = ""

    Private Sub bParcourir_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bParcourir.Click
        With OpenFileDialog
            .Multiselect = False
            .CheckFileExists = True
            .CheckPathExists = True
            .Filter = "Fichier Texte (*.txt)|*.txt"
            .ShowDialog()
        End With
        If DialogResult.OK Then
            If OpenFileDialog.FileName <> "" Then
                InfoFichier = New FileInfo(OpenFileDialog.FileName)
                If InfoFichier.Name.Length > 0 Then
                    tCheminFichier.Text = OpenFileDialog.FileName
                    FileName = OpenFileDialog.FileName
                End If
            End If
        End If
    End Sub

    Private Sub bValiderReception_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bValiderReception.Click
        Me.Dispose()
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        FileName = ""
        Me.Dispose()
    End Sub
End Class