﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fValeurSimulation
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.Panel = New System.Windows.Forms.Panel()
        Me.lValeurActuelle = New System.Windows.Forms.Label()
        Me.tValeurFixee = New C1.Win.C1Input.C1TextBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.lNetAPayerAfficher = New System.Windows.Forms.Label()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.Panel.SuspendLayout()
        CType(Me.tValeurFixee, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.lValeurActuelle)
        Me.Panel.Controls.Add(Me.tValeurFixee)
        Me.Panel.Controls.Add(Me.Label1)
        Me.Panel.Controls.Add(Me.lNetAPayerAfficher)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(232, 111)
        Me.Panel.TabIndex = 0
        '
        'lValeurActuelle
        '
        Me.lValeurActuelle.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lValeurActuelle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lValeurActuelle.Location = New System.Drawing.Point(108, 22)
        Me.lValeurActuelle.Name = "lValeurActuelle"
        Me.lValeurActuelle.Size = New System.Drawing.Size(110, 15)
        Me.lValeurActuelle.TabIndex = 71
        Me.lValeurActuelle.Text = "Valeur actuelle"
        Me.lValeurActuelle.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tValeurFixee
        '
        Me.tValeurFixee.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tValeurFixee.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tValeurFixee.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tValeurFixee.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tValeurFixee.Location = New System.Drawing.Point(108, 55)
        Me.tValeurFixee.Name = "tValeurFixee"
        Me.tValeurFixee.Size = New System.Drawing.Size(110, 18)
        Me.tValeurFixee.TabIndex = 0
        Me.tValeurFixee.Tag = Nothing
        Me.tValeurFixee.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tValeurFixee.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label1
        '
        Me.Label1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(7, 56)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(95, 15)
        Me.Label1.TabIndex = 70
        Me.Label1.Text = "Valeur Fixée"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lNetAPayerAfficher
        '
        Me.lNetAPayerAfficher.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lNetAPayerAfficher.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNetAPayerAfficher.Location = New System.Drawing.Point(7, 21)
        Me.lNetAPayerAfficher.Name = "lNetAPayerAfficher"
        Me.lNetAPayerAfficher.Size = New System.Drawing.Size(95, 15)
        Me.lNetAPayerAfficher.TabIndex = 69
        Me.lNetAPayerAfficher.Text = "Valeur actuelle"
        Me.lNetAPayerAfficher.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(111, 81)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(107, 25)
        Me.bQuitter.TabIndex = 74
        Me.bQuitter.Text = "Quitter"
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fValeurSimulation
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(232, 111)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fValeurSimulation"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        CType(Me.tValeurFixee, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents tValeurFixee As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents lNetAPayerAfficher As System.Windows.Forms.Label
    Friend WithEvents lValeurActuelle As System.Windows.Forms.Label
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
End Class
