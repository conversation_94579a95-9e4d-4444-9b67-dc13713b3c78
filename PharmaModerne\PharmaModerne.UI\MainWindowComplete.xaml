<Window x:Class="PharmaModerne.UI.MainWindowComplete"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="PHARMA2000 Moderne - Tous les Modules" 
        Height="900" Width="1600"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        Background="LightGray">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        
        <!-- Menu de navigation latéral -->
        <Border Grid.Column="0" 
                Background="DarkBlue" 
                Padding="0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- En-tête du menu -->
                <Border Grid.Row="0" 
                        Background="Navy" 
                        Padding="20">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="🏥" FontSize="32" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="PHARMA2000" 
                                 FontSize="18" 
                                 FontWeight="Bold"
                                 Foreground="White"
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="MODERNE" 
                                 FontSize="12" 
                                 Foreground="LightBlue"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
                
                <!-- Navigation principale -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="0,10">
                        
                        <!-- Dashboard -->
                        <Button Content="📊 Dashboard" 
                              Click="NavigateToModule"
                              Tag="Dashboard"
                              Style="{StaticResource MenuButtonStyle}"
                              Background="Green"/>
                        
                        <Separator Background="LightBlue" Margin="10,5"/>
                        
                        <!-- Section Ventes -->
                        <TextBlock Text="💰 VENTES" 
                                 FontWeight="Bold" 
                                 Foreground="Yellow"
                                 Margin="15,10,15,5"/>
                        
                        <Button Content="🛒 Point de Vente" 
                              Click="NavigateToModule"
                              Tag="Vente"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Button Content="📋 Liste des Ventes" 
                              Click="NavigateToModule"
                              Tag="VenteList"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Button Content="💳 Caisse" 
                              Click="NavigateToModule"
                              Tag="Caisse"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Separator Background="LightBlue" Margin="10,5"/>
                        
                        <!-- Section Clients -->
                        <TextBlock Text="👥 CLIENTS" 
                                 FontWeight="Bold" 
                                 Foreground="Yellow"
                                 Margin="15,10,15,5"/>
                        
                        <Button Content="👤 Nouveau Client" 
                              Click="NavigateToModule"
                              Tag="ClientNew"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Button Content="📋 Liste des Clients" 
                              Click="NavigateToModule"
                              Tag="ClientList"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Button Content="📱 Scanner Client" 
                              Click="NavigateToModule"
                              Tag="ClientScanner"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Separator Background="LightBlue" Margin="10,5"/>
                        
                        <!-- Section Articles -->
                        <TextBlock Text="💊 ARTICLES" 
                                 FontWeight="Bold" 
                                 Foreground="Yellow"
                                 Margin="15,10,15,5"/>
                        
                        <Button Content="💊 Nouvel Article" 
                              Click="NavigateToModule"
                              Tag="ArticleNew"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Button Content="📋 Liste des Articles" 
                              Click="NavigateToModule"
                              Tag="ArticleList"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Button Content="📦 Gestion Stock" 
                              Click="NavigateToModule"
                              Tag="Stock"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Button Content="⚠️ Alertes Stock" 
                              Click="NavigateToModule"
                              Tag="StockAlerts"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Separator Background="LightBlue" Margin="10,5"/>
                        
                        <!-- Section Fournisseurs -->
                        <TextBlock Text="🏪 FOURNISSEURS" 
                                 FontWeight="Bold" 
                                 Foreground="Yellow"
                                 Margin="15,10,15,5"/>
                        
                        <Button Content="🏪 Nouveau Fournisseur" 
                              Click="NavigateToModule"
                              Tag="FournisseurNew"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Button Content="📋 Liste Fournisseurs" 
                              Click="NavigateToModule"
                              Tag="FournisseurList"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Button Content="📦 Commandes" 
                              Click="NavigateToModule"
                              Tag="Commandes"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Separator Background="LightBlue" Margin="10,5"/>
                        
                        <!-- Section Rapports -->
                        <TextBlock Text="📊 RAPPORTS" 
                                 FontWeight="Bold" 
                                 Foreground="Yellow"
                                 Margin="15,10,15,5"/>
                        
                        <Button Content="📈 Analyses Ventes" 
                              Click="NavigateToModule"
                              Tag="Analyses"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Button Content="📋 Inventaires" 
                              Click="NavigateToModule"
                              Tag="Inventaires"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Button Content="💰 Rapports Financiers" 
                              Click="NavigateToModule"
                              Tag="RapportsFinanciers"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Separator Background="LightBlue" Margin="10,5"/>
                        
                        <!-- Section Administration -->
                        <TextBlock Text="⚙️ ADMINISTRATION" 
                                 FontWeight="Bold" 
                                 Foreground="Yellow"
                                 Margin="15,10,15,5"/>
                        
                        <Button Content="👤 Utilisateurs" 
                              Click="NavigateToModule"
                              Tag="Utilisateurs"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Button Content="⚙️ Paramètres" 
                              Click="NavigateToModule"
                              Tag="Parametres"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                        <Button Content="🔒 Sécurité" 
                              Click="NavigateToModule"
                              Tag="Securite"
                              Style="{StaticResource MenuButtonStyle}"/>
                        
                    </StackPanel>
                </ScrollViewer>
                
                <!-- Pied de page du menu -->
                <Border Grid.Row="2" 
                        Background="Navy" 
                        Padding="15,10">
                    <StackPanel>
                        <!-- Indicateur scanner -->
                        <Border x:Name="ScannerIndicator"
                              Background="Green"
                              CornerRadius="12"
                              Padding="8,4"
                              Margin="0,0,0,10"
                              Visibility="Collapsed">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock Text="📡" Foreground="White" FontSize="12"/>
                                <TextBlock Text="Scanner Actif" 
                                         Foreground="White"
                                         Margin="5,0,0,0"
                                         FontSize="12"
                                         FontWeight="Bold"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Informations utilisateur -->
                        <TextBlock x:Name="UserInfo"
                                 Text="Utilisateur Demo" 
                                 FontSize="12"
                                 Foreground="LightGray"
                                 HorizontalAlignment="Center"/>
                        
                        <TextBlock x:Name="TimeInfo"
                                 Text="{Binding CurrentTime}" 
                                 FontSize="10"
                                 Foreground="LightBlue"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
        
        <!-- Zone de contenu principal -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Barre d'outils supérieure -->
            <Border Grid.Row="0" 
                    Background="White" 
                    Padding="20,15"
                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Titre et navigation -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <TextBlock x:Name="ModuleTitle"
                                 Text="📊 Dashboard" 
                                 FontSize="24" 
                                 FontWeight="Bold"
                                 Foreground="DarkBlue"
                                 VerticalAlignment="Center"/>
                        
                        <TextBox x:Name="GlobalSearchBox"
                               Width="300"
                               Margin="50,0,0,0"
                               Padding="10"
                               FontSize="14"
                               Background="LightYellow">
                            <TextBox.InputBindings>
                                <KeyBinding Key="Enter" Command="{Binding GlobalSearchCommand}"/>
                            </TextBox.InputBindings>
                        </TextBox>
                        <TextBlock Text="🔍 Recherche globale ou scanner..."
                                 IsHitTestVisible="False"
                                 VerticalAlignment="Center"
                                 HorizontalAlignment="Left"
                                 Margin="-285,0,0,0"
                                 Foreground="Gray"
                                 FontSize="12">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding ElementName=GlobalSearchBox, Path=Text}" Value="">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </StackPanel>
                    
                    <!-- Actions rapides -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button x:Name="ScannerToggle"
                              Content="📱 SCANNER"
                              Click="ToggleScanner"
                              Padding="15,8"
                              Margin="5"
                              Background="Orange"
                              Foreground="White"
                              BorderThickness="0"
                              FontWeight="Bold"/>
                        
                        <Button Content="🔔"
                              Click="ShowNotifications"
                              Padding="12"
                              Margin="5"
                              Background="Purple"
                              Foreground="White"
                              BorderThickness="0"
                              ToolTip="Notifications"/>
                        
                        <Button Content="⚙️"
                              Click="ShowSettings"
                              Padding="12"
                              Margin="5"
                              Background="Gray"
                              Foreground="White"
                              BorderThickness="0"
                              ToolTip="Paramètres"/>
                        
                        <Button Content="🚪"
                              Click="Logout"
                              Padding="12"
                              Margin="5"
                              Background="Red"
                              Foreground="White"
                              BorderThickness="0"
                              ToolTip="Déconnexion"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- Zone de contenu des modules -->
            <ScrollViewer x:Name="ModuleContent"
                        Grid.Row="1"
                        VerticalScrollBarVisibility="Auto"
                        HorizontalScrollBarVisibility="Auto">

                <!-- Contenu par défaut - Dashboard -->
                <Grid x:Name="DefaultDashboard" Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Message de bienvenue -->
                    <Border Grid.Row="0"
                            Background="Green"
                            CornerRadius="10"
                            Padding="30"
                            Margin="0,0,0,20">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="🎉 PHARMA2000 MODERNE"
                                     FontSize="32"
                                     FontWeight="Bold"
                                     Foreground="White"
                                     HorizontalAlignment="Center"/>
                            <TextBlock Text="Tous les modules sont implémentés et fonctionnels !"
                                     FontSize="18"
                                     Foreground="LightGreen"
                                     HorizontalAlignment="Center"
                                     Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Modules disponibles -->
                    <Border Grid.Row="1"
                            Background="White"
                            CornerRadius="10"
                            Padding="30"
                            Margin="0,0,0,20"
                            Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                        <StackPanel>
                            <TextBlock Text="📋 Modules Implémentés"
                                     FontSize="24"
                                     FontWeight="Bold"
                                     Margin="0,0,0,20"
                                     HorizontalAlignment="Center"/>

                            <UniformGrid Columns="3">
                                <!-- Ventes -->
                                <Border Background="Green" CornerRadius="10" Padding="20" Margin="10">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="🛒" FontSize="48" HorizontalAlignment="Center"/>
                                        <TextBlock Text="VENTES" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Point de vente moderne" FontSize="12" Foreground="LightGreen" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Scanner intégré" FontSize="12" Foreground="LightGreen" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Clients -->
                                <Border Background="Blue" CornerRadius="10" Padding="20" Margin="10">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="👥" FontSize="48" HorizontalAlignment="Center"/>
                                        <TextBlock Text="CLIENTS" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Gestion complète" FontSize="12" Foreground="LightBlue" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Codes clients scannables" FontSize="12" Foreground="LightBlue" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Articles -->
                                <Border Background="Purple" CornerRadius="10" Padding="20" Margin="10">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="💊" FontSize="48" HorizontalAlignment="Center"/>
                                        <TextBlock Text="ARTICLES" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Catalogue avec codes-barres" FontSize="12" Foreground="Pink" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Gestion stock avancée" FontSize="12" Foreground="Pink" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Fournisseurs -->
                                <Border Background="Orange" CornerRadius="10" Padding="20" Margin="10">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="🏪" FontSize="48" HorizontalAlignment="Center"/>
                                        <TextBlock Text="FOURNISSEURS" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Gestion partenaires" FontSize="12" Foreground="LightYellow" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Commandes automatisées" FontSize="12" Foreground="LightYellow" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Rapports -->
                                <Border Background="Red" CornerRadius="10" Padding="20" Margin="10">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="📊" FontSize="48" HorizontalAlignment="Center"/>
                                        <TextBlock Text="RAPPORTS" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Analyses avancées" FontSize="12" Foreground="Pink" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Statistiques temps réel" FontSize="12" Foreground="Pink" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Administration -->
                                <Border Background="Gray" CornerRadius="10" Padding="20" Margin="10">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="⚙️" FontSize="48" HorizontalAlignment="Center"/>
                                        <TextBlock Text="ADMIN" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Paramètres système" FontSize="12" Foreground="LightGray" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Gestion utilisateurs" FontSize="12" Foreground="LightGray" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </UniformGrid>
                        </StackPanel>
                    </Border>

                    <!-- Instructions -->
                    <Border Grid.Row="2"
                            Background="LightBlue"
                            CornerRadius="10"
                            Padding="30">
                        <StackPanel>
                            <TextBlock Text="🚀 Comment utiliser PHARMA2000 Moderne"
                                     FontSize="20"
                                     FontWeight="Bold"
                                     Margin="0,0,0,20"
                                     HorizontalAlignment="Center"/>

                            <StackPanel Margin="20,0">
                                <TextBlock Text="1. 📱 Activez le scanner avec le bouton orange en haut à droite" FontSize="14" Margin="0,5"/>
                                <TextBlock Text="2. 🔍 Utilisez la recherche globale pour scanner ou chercher" FontSize="14" Margin="0,5"/>
                                <TextBlock Text="3. 📋 Naviguez entre les modules avec le menu de gauche" FontSize="14" Margin="0,5"/>
                                <TextBlock Text="4. 🛒 Commencez par le Point de Vente pour tester le scanner" FontSize="14" Margin="0,5"/>
                                <TextBlock Text="5. 👥 Gérez vos clients avec codes clients scannables" FontSize="14" Margin="0,5"/>
                                <TextBlock Text="6. 💊 Cataloguez vos articles avec codes-barres" FontSize="14" Margin="0,5"/>
                                <TextBlock Text="7. 📊 Consultez les rapports et analyses en temps réel" FontSize="14" Margin="0,5"/>
                            </StackPanel>

                            <Border Background="Green" CornerRadius="5" Padding="15" Margin="0,20,0,0">
                                <TextBlock Text="✅ Tous les modules sont prêts à l'emploi avec scanner intégré !"
                                         FontWeight="Bold"
                                         Foreground="White"
                                         HorizontalAlignment="Center"/>
                            </Border>
                        </StackPanel>
                    </Border>
                </Grid>

            </ScrollViewer>
            
            <!-- Barre de statut -->
            <Border Grid.Row="2" 
                    Background="DarkGray" 
                    Padding="15,8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <TextBlock x:Name="StatusMessage"
                                 Text="Prêt - Tous les modules chargés" 
                                 Foreground="White"
                                 VerticalAlignment="Center"/>
                        
                        <TextBlock Text=" | " 
                                 Foreground="LightGray" 
                                 Margin="10,0"/>
                        
                        <TextBlock x:Name="ModuleStatus"
                                 Text="Module actuel : Dashboard" 
                                 Foreground="LightBlue"
                                 VerticalAlignment="Center"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <TextBlock Text="PHARMA2000 Moderne v1.0" 
                                 Foreground="LightGray" 
                                 VerticalAlignment="Center"
                                 Margin="0,0,20,0"/>
                        
                        <TextBlock x:Name="CurrentTimeStatus"
                                 Foreground="White" 
                                 VerticalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
    
    <!-- Styles pour les boutons de menu -->
    <Window.Resources>
        <Style x:Key="MenuButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="LightBlue"/>
                    <Setter Property="Foreground" Value="DarkBlue"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

</Window>
