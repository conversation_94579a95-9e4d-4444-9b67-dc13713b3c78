//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class COPY_ARTICLE
    {
        public string CodeArticle { get; set; }
        public string <PERSON><PERSON>arre { get; set; }
        public string Designation { get; set; }
        public string Dosage { get; set; }
        public string LibelleTableau { get; set; }
        public decimal QuantiteUnitaire { get; set; }
        public int ContenanceArticle { get; set; }
        public decimal PrixAchatHT { get; set; }
        public decimal PrixAchatTTC { get; set; }
        public decimal PrixVenteHT { get; set; }
        public decimal PrixVenteTTC { get; set; }
        public decimal TVA { get; set; }
        public decimal Marge { get; set; }
        public bool Exonorertva { get; set; }
        public decimal HR { get; set; }
        public string CodePCT { get; set; }
        public Nullable<int> CodeCategorieCNAM { get; set; }
        public decimal TarifDeReference { get; set; }
        public bool AccordPrealable { get; set; }
        public bool PriseEnCharge { get; set; }
        public bool SansCodeBarre { get; set; }
        public bool SansVignette { get; set; }
        public Nullable<decimal> StockAlerte { get; set; }
        public decimal QteACommander { get; set; }
        public Nullable<System.DateTime> DateAlerte { get; set; }
        public Nullable<System.DateTime> DateDerniereCommande { get; set; }
        public Nullable<decimal> QuantiteDernierCommande { get; set; }
        public Nullable<System.DateTime> DateInitiale { get; set; }
        public int StockInitial { get; set; }
        public int CodeForme { get; set; }
        public int CodeCategorie { get; set; }
        public Nullable<int> CodeLabo { get; set; }
        public string Rayon { get; set; }
        public Nullable<int> CodeSituation { get; set; }
        public string CodeOperateur { get; set; }
        public Nullable<int> CodeGroupement { get; set; }
        public Nullable<int> CodeTypePreparation { get; set; }
        public string Section { get; set; }
        public Nullable<int> DCI1 { get; set; }
        public Nullable<int> DCI2 { get; set; }
        public Nullable<int> DCI3 { get; set; }
        public bool Supprime { get; set; }
        public bool FemmeEnceinte { get; set; }
        public decimal StockArticle { get; set; }
        public string CodeFournisseur { get; set; }
        public Nullable<int> NombreCommande { get; set; }
    }
}
