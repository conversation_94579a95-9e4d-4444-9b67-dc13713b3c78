﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports Microsoft.Reporting.WinForms
Imports System.Linq.Dynamic

Public Class fEtatDesVentesParAnnee
    Dim _SalesReportService As New Bll.Reporting.SalesReport
    Dim _VOrderBy As String = "Id"
    Dim _VAscDesc As String = "Asc"

    Dim StrSQL As String = ""

    Dim cmdVente As New SqlCommand
    Dim daVente As New SqlDataAdapter
    Dim dsVente As New DataSet

    Public Source As String = ""

    Dim TotalComptant As Double = 0.0
    Dim TotalCredit As Double = 0.0
    Dim TotalMutuelle As Double = 0.0
    Dim TotalCNAM As Double = 0.0
    Dim Total As Double = 0.0
    Dim TotalRemise As Double = 0.0
    Dim TotalCheque As Double = 0.0
    Dim TotalCarte As Double = 0.0

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub init()
        AfficherVentes()
    End Sub

    Public Sub AfficherVentes()

        Dim List As New Library.SortableBindingList(Of Data.Reporting.P_Report_VentesAnnuelles_Result)(_SalesReportService.GetVentesAnnuelles())
        With gVentes
            .Columns.Clear()
            .DataSource = List
            '.DataMember = "VENTES"
            .Rebind(False)
            .Columns("Annee").Caption = "Année"
            .Columns("Espece").Caption = "Comptant"
            .Columns("MontantMutuelle").Caption = "Mutuelle"
            .Columns("MontantCNAM").Caption = "CNAM"
            .Columns("TotalTTC").Caption = "Total TTC"
            .Columns("Remise").Caption = "Remise"
            .Columns("CREDIT").Caption = "Crédit"


            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            '.Splits(0).DisplayColumns("NumeroMois").Visible = False
            .Splits(0).DisplayColumns("Annee").Width = 100
            .Splits(0).DisplayColumns("Espece").Width = 130
            .Splits(0).DisplayColumns("Espece").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("MontantMutuelle").Width = 130
            .Splits(0).DisplayColumns("MontantMutuelle").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("MontantCNAM").Width = 130
            .Splits(0).DisplayColumns("MontantCNAM").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTTC").Width = 130
            .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Remise").Width = 130
            .Splits(0).DisplayColumns("Remise").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("CREDIT").Width = 130
            .Splits(0).DisplayColumns("CREDIT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Id").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gVentes)
        End With

        TotalComptant = 0.0
        TotalCredit = 0.0
        TotalMutuelle = 0.0
        TotalCNAM = 0.0
        Total = 0.0
        TotalRemise = 0.0

        For I = 0 To gVentes.RowCount - 1
            If gVentes(I, "Espece").ToString <> "" Then
                TotalComptant = TotalComptant + gVentes(I, "Espece")
            End If
            If gVentes(I, "MontantMutuelle").ToString <> "" Then
                TotalMutuelle = TotalMutuelle + gVentes(I, "MontantMutuelle")
            End If
            If gVentes(I, "MontantCNAM").ToString <> "" Then
                TotalCNAM = TotalCNAM + gVentes(I, "MontantCNAM")
            End If
            If gVentes(I, "TotalTTC").ToString <> "" Then
                Total = Total + gVentes(I, "TotalTTC")
            End If
            If gVentes(I, "CREDIT").ToString <> "" Then
                TotalCredit = TotalCredit + gVentes(I, "CREDIT")
            End If
            If gVentes(I, "Remise").ToString <> "" Then
                TotalRemise = TotalRemise + gVentes(I, "Remise")
            End If
            If gVentes(I, "Cheque").ToString <> "" Then
                TotalCheque = TotalCheque + gVentes(I, "Cheque")
            End If
            If gVentes(I, "Carte").ToString <> "" Then
                TotalCarte = TotalCarte + gVentes(I, "Carte")
            End If
            'If gVentes(I, "NumeroVente").ToString <> "" Then
            '    NombreVente = NombreVente + 1
            'End If

        Next

        lComptant.Text = (TotalComptant + TotalCheque + TotalCarte).ToString("### ### ##0.000")
        lMutuelle.Text = TotalMutuelle.ToString("### ### ##0.000")
        lCNAM.Text = TotalCNAM.ToString("### ### ##0.000")
        lTotalTTC.Text = Total.ToString("### ### ##0.000")
        lCredit.Text = TotalCredit.ToString("### ### ##0.000")
        LRemise.Text = TotalRemise.ToString("### ### ##0.000")

        'lNombreDesVentes.Text = NombreVente
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Reporting Ventes" Then
                num = I
            End If
        Next
        Dim dt

        Dim MyViewer As New fImpressionReportingVente

        dt = _SalesReportService.GetVentesAnnuelles().OrderBy(_VOrderBy + " " + _VAscDesc)
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_VentesAnnuelles", dt))
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Pharmacie", _SalesReportService.GetPharmacie()))
        MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatVentesAnnuelles.rdl"

        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel1)

        MyViewer.ReportViewer1.RefreshReport()

        fMain.Tab.SelectedTab.Text = "Impression Reporting Ventes"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    Private Sub gVentes_AfterSort(sender As Object, e As FilterEventArgs) Handles gVentes.AfterSort
        Try
            _VOrderBy = e.Condition.Substring(0, e.Condition.IndexOf(" "))
            _VAscDesc = e.Condition.Substring(e.Condition.IndexOf(" ") + 1, e.Condition.Length - e.Condition.IndexOf(" ") - 1)
        Catch ex As Exception
            _VOrderBy = e.Condition
            _VAscDesc = "Asc"
        End Try
    End Sub
End Class