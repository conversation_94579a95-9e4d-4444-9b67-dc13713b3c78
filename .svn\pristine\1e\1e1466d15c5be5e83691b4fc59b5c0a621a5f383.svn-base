﻿Imports System.Data.SqlClient
Imports Microsoft.Office.Interop
Imports C1.C1Excel
Imports Microsoft.Reporting.WinForms

Public Class fRecapitulatifCaisse
    Dim _SalesReportService As New Bll.Reporting.SalesReport

    Dim Condition As String = ""
    Dim Condition1 As String = ""
    Dim Condition2 As String = ""
    Dim StrSQL As String = ""
    Dim cmd As New SqlCommand
    Dim da As New SqlDataAdapter
    Dim ds As New DataSet

    Dim CondCalculDiff As String = " WHERE 1=1 "

    Private Sub dtpDateFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDateFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            dtpDateFin.ValidateText()
            AfficherCaisse()
            bVenteAuComptant.Focus()
        End If
    End Sub

    Public Sub AfficherCaisse()

        lEspeceVente.Text = _SalesReportService.GetAmount(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement vente", 1)
        lChequeVente.Text = _SalesReportService.GetAmount(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement vente", 2)
        lCarteVente.Text = _SalesReportService.GetAmount(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement vente", 4)
        lTraiteVente.Text = _SalesReportService.GetAmount(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement vente", 5)
        lTotalVente.Text = _SalesReportService.GetAmount(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement vente", Nothing)

        lEspeceReglement.Text = _SalesReportService.GetAmountRegulation(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement", 1)
        lChequeReglement.Text = _SalesReportService.GetAmountRegulation(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement", 2)
        lCarteReglement.Text = _SalesReportService.GetAmountRegulation(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement", 4)
        lTraiteReglement.Text = _SalesReportService.GetAmountRegulation(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement", 5)
        ' lTotalReglement.Text = _SalesReportService.GetAmountRegulation(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement", Nothing)
        lTotalReglement.Text = Convert.ToDecimal(lEspeceReglement.Text) + Convert.ToDecimal(lChequeReglement.Text) + Convert.ToDecimal(lCarteReglement.Text) + Convert.ToDecimal(lTraiteReglement.Text)

        lTotalEspece.Text = _SalesReportService.GetAmount(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement vente", 1) +
                            _SalesReportService.GetAmountRegulation(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement", 1)
        lTotalCheque.Text = _SalesReportService.GetAmount(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement vente", 2) +
                            _SalesReportService.GetAmountRegulation(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement", 2)
        lTotalCarte.Text = _SalesReportService.GetAmount(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement vente", 4) +
                            _SalesReportService.GetAmountRegulation(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement", 4)
        lTotalTraite.Text = _SalesReportService.GetAmount(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement vente", 5) +
                            _SalesReportService.GetAmountRegulation(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement", 5)

        lVirementVente.Text = _SalesReportService.GetAmount(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement vente", 6)
        lVirementReglement.Text = _SalesReportService.GetAmountRegulation(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement", 6)
        lTotalVirement.Text = _SalesReportService.GetAmount(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement vente", 6) +
                            _SalesReportService.GetAmountRegulation(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue, "Reglement", 6)

        lRemiseReglements.Text = _SalesReportService.GetDiscountRegulation(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, "Reglement client", cmbPoste.SelectedValue) +
            GetRetenueALaSource()


        lRemiseVentes.Text = _SalesReportService.GetDiscount(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, "Vente", cmbPoste.SelectedValue)
        lRetourVentes.Text = _SalesReportService.GetReturnSale(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue)
        lCredit.Text = _SalesReportService.GetCredit(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue)
        lMutuelle.Text = _SalesReportService.GetAmountMutual(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue)
        lCNAM.Text = _SalesReportService.GetAmountCNAM(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), rdbToutes.Checked, cmbPoste.SelectedValue)

        lFondCaisse.Text = _SalesReportService.GetCashBase(cmbPoste.SelectedValue)
        lTotalTotaux.Text = Convert.ToDecimal(lTotalVente.Text) + Convert.ToDecimal(lTotalReglement.Text)
        lTotal.Text = _SalesReportService.GetCashBase(cmbPoste.SelectedValue) + Convert.ToDecimal(lTotalTotaux.Text)
        tTotalReel.Text = lTotal.Text
    End Sub

    Private Function GetRetenueALaSource()
        Dim Condition As String = " 1=1 "

        If cmbPoste.Text <> "" Then
            Condition += " AND LibellePoste ='" + cmbPoste.Text + "'"
        End If
        If rdbNonVidees.Checked = True Then
            Condition += " AND Vider='False' "
        End If
        If dtpDateDebut.Text <> "" And dtpDateFin.Text <> "" And rdbToutes.Checked = True Then
            Condition += " AND Date >=CAST('" + dtpDateDebut.Text + "' as date)AND Date <=CAST('" + _
            DateAdd(DateInterval.Day, 1, dtpDateFin.Value) + "' as date)"
        End If



        Dim Cmd As New SqlCommand
        Dim MontantRetenueSourceCnam As Double = 0.0
        Dim MontantRetenueSourceMutuelle As Double = 0.0

        Cmd.Connection = ConnectionServeur
        Cmd.CommandText = "SELECT SUM(MontantRetenueSource) FROM REGLEMENT_CNAM WHERE " + Condition
        Try
            MontantRetenueSourceCnam = Cmd.ExecuteScalar()
        Catch ex As Exception
            MontantRetenueSourceCnam = 0
        End Try

        Cmd.Connection = ConnectionServeur
        Cmd.CommandText = "SELECT SUM(MontantRetenueSource) FROM REGLEMENT_MUTUELLE WHERE " + Condition
        Try
            MontantRetenueSourceMutuelle = Cmd.ExecuteScalar()
        Catch ex As Exception
            MontantRetenueSourceMutuelle = 0
        End Try

        Return MontantRetenueSourceCnam + MontantRetenueSourceMutuelle
    End Function


    Private Sub dtpDateFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDateFin.TextChanged

    End Sub

    Private Sub rdbNonVidees_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbNonVidees.CheckedChanged
        If rdbNonVidees.Checked = True Then
            dtpDateDebut.Enabled = False
            dtpDateFin.Enabled = False
            init()
        End If
    End Sub

    Private Sub fRecapitulatifCaisse_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        init()
        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler
    End Sub

    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F9 Then
            bImprimer_Click(o, e)
            Exit Sub
        End If
    End Sub

    Public Sub init()

        Dim StrSQL1 As String = ""

        If (ds.Tables.IndexOf("POSTE") > -1) Then
            ds.Tables("POSTE").Clear()
        End If
        'chargement des Postes
        StrSQL1 = "SELECT DISTINCT LibellePoste  FROM POSTE ORDER BY LibellePoste ASC"
        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL1
        da = New SqlDataAdapter(cmd)
        da.Fill(ds, "POSTE")
        cmbPoste.DataSource = ds.Tables("POSTE")
        cmbPoste.ValueMember = "LibellePoste"
        cmbPoste.DisplayMember = "LibellePoste"
        cmbPoste.ColumnHeaders = False
        cmbPoste.Splits(0).DisplayColumns("LibellePoste").Width = 10
        cmbPoste.ExtendRightColumn = True

        Condition = " 1=1 "
        rdbNonVidees.Checked = True

        If cmbPoste.Text <> "" Then
            Condition += " AND LibellePoste ='" + cmbPoste.Text + "'"
        End If
        If rdbNonVidees.Checked = True Then
            Condition += " AND Vider='False' "
        End If
        If dtpDateDebut.Text <> "" And dtpDateFin.Text <> "" And rdbToutes.Checked = True Then
            Condition += " AND Date >=CAST('" + dtpDateDebut.Text + "' as date)AND Date <=CAST('" + _
            dtpDateFin.Text + "' as date)"
        End If

        lEspeceVente.Text = "0"
        lEspeceReglement.Text = "0"
        lTotalEspece.Text = "0"
        lChequeVente.Text = "0"
        lChequeReglement.Text = "0"
        lTotalCheque.Text = "0"
        lCarteVente.Text = "0"
        lCarteReglement.Text = "0"
        lTotalCarte.Text = "0"
        lTraiteVente.Text = "0"
        lTraiteReglement.Text = "0"
        lTotalTraite.Text = "0"
        lVirementVente.Text = "0"
        lVirementReglement.Text = "0"
        lTotalVirement.Text = "0"
        lTotalVente.Text = "0"
        lTotalReglement.Text = "0"
        lTotalTotaux.Text = "0"

        lRemiseReglements.Text = "0"
        lRemiseVentes.Text = "0"
        lRetourVentes.Text = "0"
        lMutuelle.Text = "0"
        lCredit.Text = "0"
        lCNAM.Text = "0"

        AfficherCaisse()

    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Me.Hide()
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click




        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Reporting Ventes" Then
                num = I
            End If
        Next
        Dim dt

        Dim Parameters As New List(Of ReportParameter)()

        If dtpDateDebut.Text <> "" Then
            dtpDateDebut.ValidateText()
        End If

        Dim DateDebut As New ReportParameter()
        DateDebut.Name = "DateDebut"
        DateDebut.Values.Add(IIf(dtpDateDebut.Text = "", Date.Now, dtpDateDebut.Value))
        Parameters.Add(DateDebut)

        If dtpDateFin.Text <> "" Then
            dtpDateFin.ValidateText()
        End If

        Dim DateFin As New ReportParameter()
        DateFin.Name = "DateFin"
        DateFin.Values.Add(IIf(dtpDateFin.Text = "", Date.Now, dtpDateFin.Value))
        Parameters.Add(DateFin)

        Dim Vide As New ReportParameter()
        Vide.Name = "Vide"
        Vide.Values.Add(rdbNonVidees.Checked)
        Parameters.Add(Vide)

        Dim Poste As New ReportParameter()
        Poste.Name = "Poste"
        Poste.Values.Add(cmbPoste.SelectedValue)
        Parameters.Add(Poste)

        Dim EspeceVente As New ReportParameter()
        EspeceVente.Name = "EspeceVente"
        EspeceVente.Values.Add(lEspeceVente.Text)
        Parameters.Add(EspeceVente)

        Dim ChequeVente As New ReportParameter()
        ChequeVente.Name = "ChequeVente"
        ChequeVente.Values.Add(lChequeVente.Text)
        Parameters.Add(ChequeVente)

        Dim CarteVente As New ReportParameter()
        CarteVente.Name = "CarteVente"
        CarteVente.Values.Add(lCarteVente.Text)
        Parameters.Add(CarteVente)

        Dim TraiteVente As New ReportParameter()
        TraiteVente.Name = "TraiteVente"
        TraiteVente.Values.Add(lTraiteVente.Text)
        Parameters.Add(TraiteVente)

        Dim TotalVente As New ReportParameter()
        TotalVente.Name = "TotalVente"
        TotalVente.Values.Add(lTotalVente.Text)
        Parameters.Add(TotalVente)

        Dim EspeceReglement As New ReportParameter()
        EspeceReglement.Name = "EspeceReglement"
        EspeceReglement.Values.Add(lEspeceReglement.Text)
        Parameters.Add(EspeceReglement)

        Dim ChequeReglement As New ReportParameter()
        ChequeReglement.Name = "ChequeReglement"
        ChequeReglement.Values.Add(lChequeReglement.Text)
        Parameters.Add(ChequeReglement)

        Dim CarteReglement As New ReportParameter()
        CarteReglement.Name = "CarteReglement"
        CarteReglement.Values.Add(lCarteReglement.Text)
        Parameters.Add(CarteReglement)

        Dim TraiteReglement As New ReportParameter()
        TraiteReglement.Name = "TraiteReglement"
        TraiteReglement.Values.Add(lTraiteReglement.Text)
        Parameters.Add(TraiteReglement)

        Dim TotalEspece As New ReportParameter()
        TotalEspece.Name = "TotalEspece"
        TotalEspece.Values.Add(lTotalEspece.Text)
        Parameters.Add(TotalEspece)

        Dim TotalCheque As New ReportParameter()
        TotalCheque.Name = "TotalCheque"
        TotalCheque.Values.Add(lTotalCheque.Text)
        Parameters.Add(TotalCheque)

        Dim TotalCarte As New ReportParameter()
        TotalCarte.Name = "TotalCarte"
        TotalCarte.Values.Add(lTotalCarte.Text)
        Parameters.Add(TotalCarte)

        Dim TotalTraite As New ReportParameter()
        TotalTraite.Name = "TotalTraite"
        TotalTraite.Values.Add(lTotalTraite.Text)
        Parameters.Add(TotalTraite)

        Dim TotalReglement As New ReportParameter()
        TotalReglement.Name = "TotalReglement"
        TotalReglement.Values.Add(lTotalReglement.Text)
        Parameters.Add(TotalReglement)

        Dim TotalTotaux As New ReportParameter()
        TotalTotaux.Name = "TotalTotaux"
        TotalTotaux.Values.Add(lTotalTotaux.Text)
        Parameters.Add(TotalTotaux)

        Dim VirementVente As New ReportParameter()
        VirementVente.Name = "VirementVente"
        VirementVente.Values.Add(lVirementVente.Text)
        Parameters.Add(VirementVente)

        Dim VirementReglement As New ReportParameter()
        VirementReglement.Name = "VirementReglement"
        VirementReglement.Values.Add(lVirementReglement.Text)
        Parameters.Add(VirementReglement)

        Dim RemiseReglement As New ReportParameter()
        RemiseReglement.Name = "RemiseReglement"
        RemiseReglement.Values.Add(lRemiseReglements.Text)
        Parameters.Add(RemiseReglement)

        Dim RemiseVente As New ReportParameter()
        RemiseVente.Name = "RemiseVente"
        RemiseVente.Values.Add(lRemiseVentes.Text)
        Parameters.Add(RemiseVente)

        Dim RetourVente As New ReportParameter()
        RetourVente.Name = "RetourVente"
        RetourVente.Values.Add(lRetourVentes.Text)
        Parameters.Add(RetourVente)

        Dim Mutuelle As New ReportParameter()
        Mutuelle.Name = "Mutuelle"
        Mutuelle.Values.Add(lMutuelle.Text)
        Parameters.Add(Mutuelle)

        Dim Credit As New ReportParameter()
        Credit.Name = "Credit"
        Credit.Values.Add(lCredit.Text)
        Parameters.Add(Credit)

        Dim CNAM As New ReportParameter()
        CNAM.Name = "CNAM"
        CNAM.Values.Add(lCNAM.Text)
        'Dim CondCrystal As String = ""

        'Dim I As Integer

        Parameters.Add(CNAM)

        Dim TotalVirement As New ReportParameter()
        TotalVirement.Name = "TotalVirement"
        TotalVirement.Values.Add(lTotalVirement.Text)
        Parameters.Add(TotalVirement)

        Dim FondCaisse As New ReportParameter()
        FondCaisse.Name = "FondCaisse"
        FondCaisse.Values.Add(lFondCaisse.Text)
        Parameters.Add(FondCaisse)

        Dim Total As New ReportParameter()
        Total.Name = "Total"
        Total.Values.Add(lTotal.Text)
        Parameters.Add(Total)
        Dim MyViewer As New fImpressionReportingVente

        dt = _SalesReportService.GetEtatRecapCaisse(IIf(dtpDateDebut.Text <> "", dtpDateDebut.Value, Nothing), _
                                                    IIf(dtpDateFin.Text <> "", dtpDateFin.Value, Nothing), _
                                                    rdbToutes.Checked, _
                                                    cmbPoste.SelectedValue)

        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_EtatRecapCaisse", dt))
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Pharmacie", _SalesReportService.GetPharmacie()))

        MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatRecapCaisse.rdl"

        MyViewer.ReportViewer1.LocalReport.SetParameters(Parameters)

        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel1)

        MyViewer.ReportViewer1.RefreshReport()

        fMain.Tab.SelectedTab.Text = "Impression Reporting Ventes"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If

        'Dim num As Integer = 999
        'For I = 0 To fMain.Tab.TabPages.Count - 1
        '    If fMain.Tab.TabPages(I).Text = "Impression de Récap Caisse" Then
        '        num = I
        '    End If
        'Next
        'CR.FileName = Application.StartupPath + "\EtatRecapCaisse.rpt"

        'CR.SetParameterValue("VenteAuComptantEspece", lEspeceVente.Text)
        'CR.SetParameterValue("VenteAuComptantCheque", lChequeVente.Text)
        'CR.SetParameterValue("VenteAuComptantCarte", lCarteVente.Text)
        'CR.SetParameterValue("VenteAuComptantTraite", lTraiteVente.Text)
        'CR.SetParameterValue("VenteAuComptantVirement", lVirementVente.Text)
        'CR.SetParameterValue("VenteAuComptantTotaux", lTotalVente.Text)

        'CR.SetParameterValue("ReglementCreditEspece", lEspeceReglement.Text)
        'CR.SetParameterValue("ReglementCreditCheque", lChequeReglement.Text)
        'CR.SetParameterValue("ReglementCreditCarte", lCarteReglement.Text)
        'CR.SetParameterValue("ReglementCreditTraite", lTraiteReglement.Text)
        'CR.SetParameterValue("ReglementCreditVirement", lVirementReglement.Text)
        'CR.SetParameterValue("ReglementCreditTotaux", lTotalReglement.Text)

        'CR.SetParameterValue("TotauxEspece", lTotalEspece.Text)
        'CR.SetParameterValue("TotauxCheque", lTotalCheque.Text)
        'CR.SetParameterValue("TotauxCarte", lTotalCarte.Text)
        'CR.SetParameterValue("TotauxTraite", lTotalTraite.Text)
        'CR.SetParameterValue("TotauxVirement", lTotalVirement.Text)
        'CR.SetParameterValue("TotauxTotaux", lTotalTotaux.Text)

        'CR.SetParameterValue("Credit", lCredit.Text)
        'CR.SetParameterValue("Cnam", lCNAM.Text)
        'CR.SetParameterValue("Mutuelle", lMutuelle.Text)
        'CR.SetParameterValue("RemiseReglement", lRemiseReglements.Text)
        'CR.SetParameterValue("RemiseVente", lRemiseVentes.Text)

        'CR.SetParameterValue("lFondDeCaisse", lFondCaisse.Text)
        'CR.SetParameterValue("lTotal", lTotal.Text)

        'If rdbNonVidees.Checked = True Then
        '    CR.SetParameterValue("pDateDu", "tt")
        '    CR.SetParameterValue("pDateAu", "tt")
        'Else
        '    CR.SetParameterValue("pDateDu", dtpDateDebut.Text)
        '    CR.SetParameterValue("pDateAu", dtpDateFin.Text)
        'End If

        'Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        'Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        'For Each tbCurrent In CR.Database.Tables
        '    tliCurrent = tbCurrent.LogOnInfo
        '    With tliCurrent.ConnectionInfo
        '        .ServerName = NomServeur
        '        .UserID = NomUtilisateurSQL
        '        .Password = MotDePasseSQL
        '        .DatabaseName = NomBase
        '    End With
        '    tbCurrent.ApplyLogOnInfo(tliCurrent)
        'Next tbCurrent
        'CR.RecordSelectionFormula = CondCrystal

        'Dim MyViewer As New fViewer
        'MyViewer.CRViewer.ReportSource = CR
        'fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        'fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        'fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        'fMain.Tab.SelectedTab.Text = "Impression de Récap Caisse"
        'If num <> 999 Then
        '    fMain.Tab.TabPages(num).Dispose()
        'End If
        'Me.Hide()
    End Sub

    Private Sub bVenteAuComptant_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bVenteAuComptant.Click
        Dim MyListeRecapCaisse As New fListesPourRecapCaisse
        If (dtpDateDebut.Text <> "") Then
            dtpDateDebut.ValidateText()
            MyListeRecapCaisse.DateDebut = dtpDateDebut.Value
        Else
            MyListeRecapCaisse.DateDebut = Date.Now
        End If
        If (dtpDateFin.Text <> "") Then
            dtpDateFin.ValidateText()
            MyListeRecapCaisse.DateFin = dtpDateFin.Value
        Else
            MyListeRecapCaisse.DateFin = Date.Now
        End If
        MyListeRecapCaisse.Vide = rdbNonVidees.Checked
        MyListeRecapCaisse.LibellePoste = cmbPoste.SelectedValue
        MyListeRecapCaisse.Titre = "VENTE AU COMPTANT"
        MyListeRecapCaisse.ListeDesVentes = True
        MyListeRecapCaisse.ShowDialog()
        MyListeRecapCaisse.Close()
        MyListeRecapCaisse.Dispose()
    End Sub

    Private Sub bReglementCreditMutuelle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bReglementCreditMutuelle.Click
        Dim MyListeRecapCaisse As New fListesPourRecapCaisse
        If (dtpDateDebut.Text <> "") Then
            dtpDateDebut.ValidateText()
            MyListeRecapCaisse.DateDebut = dtpDateDebut.Value
        Else
            MyListeRecapCaisse.DateDebut = Date.Now
        End If
        If (dtpDateFin.Text <> "") Then
            dtpDateFin.ValidateText()
            MyListeRecapCaisse.DateFin = dtpDateFin.Value
        Else
            MyListeRecapCaisse.DateFin = Date.Now
        End If
        MyListeRecapCaisse.Vide = rdbNonVidees.Checked
        MyListeRecapCaisse.LibellePoste = cmbPoste.SelectedValue
        MyListeRecapCaisse.Titre = "REGLEMENT CREDIT / MUTUELLE"
        MyListeRecapCaisse.ListeDesVentes = False
        MyListeRecapCaisse.ShowDialog()
        MyListeRecapCaisse.Close()
        MyListeRecapCaisse.Dispose()
    End Sub

    Private Sub bRemiseReglements_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRemiseReglements.Click
        Dim MyListeRecapCaisse As New fListesPourRecapCaisse
        If (dtpDateDebut.Text <> "") Then
            dtpDateDebut.ValidateText()
            MyListeRecapCaisse.DateDebut = dtpDateDebut.Value
        Else
            MyListeRecapCaisse.DateDebut = Date.Now
        End If
        If (dtpDateFin.Text <> "") Then
            dtpDateFin.ValidateText()
            MyListeRecapCaisse.DateFin = dtpDateFin.Value
        Else
            MyListeRecapCaisse.DateFin = Date.Now
        End If
        MyListeRecapCaisse.Vide = rdbNonVidees.Checked
        MyListeRecapCaisse.LibellePoste = cmbPoste.SelectedValue
        MyListeRecapCaisse.Titre = "REMISE REGLEMENT"
        MyListeRecapCaisse.ListeDesVentes = False
        MyListeRecapCaisse.ShowDialog()
        MyListeRecapCaisse.Close()
        MyListeRecapCaisse.Dispose()
    End Sub

    Private Sub bRemiseVente_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRemiseVente.Click
        Dim MyListeRecapCaisse As New fListesPourRecapCaisse
        If (dtpDateDebut.Text <> "") Then
            dtpDateDebut.ValidateText()
            MyListeRecapCaisse.DateDebut = dtpDateDebut.Value
        Else
            MyListeRecapCaisse.DateDebut = Date.Now
        End If
        If (dtpDateFin.Text <> "") Then
            dtpDateFin.ValidateText()
            MyListeRecapCaisse.DateFin = dtpDateFin.Value
        Else
            MyListeRecapCaisse.DateFin = Date.Now
        End If
        MyListeRecapCaisse.Vide = rdbNonVidees.Checked
        MyListeRecapCaisse.LibellePoste = cmbPoste.SelectedValue
        MyListeRecapCaisse.Titre = "REMISE VENTE"
        MyListeRecapCaisse.ListeDesVentes = True
        MyListeRecapCaisse.ShowDialog()
        MyListeRecapCaisse.Close()
        MyListeRecapCaisse.Dispose()
    End Sub

    Private Sub bRetourVentes_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRetourVentes.Click
        Dim MyListeRecapCaisse As New fListesPourRecapCaisse
        If (dtpDateDebut.Text <> "") Then
            dtpDateDebut.ValidateText()
            MyListeRecapCaisse.DateDebut = dtpDateDebut.Value
        Else
            MyListeRecapCaisse.DateDebut = Date.Now
        End If
        If (dtpDateFin.Text <> "") Then
            dtpDateFin.ValidateText()
            MyListeRecapCaisse.DateFin = dtpDateFin.Value
        Else
            MyListeRecapCaisse.DateFin = Date.Now
        End If
        MyListeRecapCaisse.Vide = rdbNonVidees.Checked
        MyListeRecapCaisse.LibellePoste = cmbPoste.SelectedValue
        MyListeRecapCaisse.Titre = "RETOUR VENTE"
        MyListeRecapCaisse.ListeDesVentes = True
        MyListeRecapCaisse.ShowDialog()
        MyListeRecapCaisse.Close()
        MyListeRecapCaisse.Dispose()
    End Sub

    Private Sub bMutuelle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bMutuelle.Click
        Dim MyListeRecapCaisse As New fListesPourRecapCaisse
        If (dtpDateDebut.Text <> "") Then
            dtpDateDebut.ValidateText()
            MyListeRecapCaisse.DateDebut = dtpDateDebut.Value
        Else
            MyListeRecapCaisse.DateDebut = Date.Now
        End If
        If (dtpDateFin.Text <> "") Then
            dtpDateFin.ValidateText()
            MyListeRecapCaisse.DateFin = dtpDateFin.Value
        Else
            MyListeRecapCaisse.DateFin = Date.Now
        End If
        MyListeRecapCaisse.Vide = rdbNonVidees.Checked
        MyListeRecapCaisse.LibellePoste = cmbPoste.SelectedValue
        MyListeRecapCaisse.Titre = "MUTUELLE"
        MyListeRecapCaisse.ListeDesVentes = True
        MyListeRecapCaisse.ShowDialog()
        MyListeRecapCaisse.Close()
        MyListeRecapCaisse.Dispose()

    End Sub

    Private Sub bCredit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCredit.Click
        Dim MyListeRecapCaisse As New fListesPourRecapCaisse
        If (dtpDateDebut.Text <> "") Then
            dtpDateDebut.ValidateText()
            MyListeRecapCaisse.DateDebut = dtpDateDebut.Value
        Else
            MyListeRecapCaisse.DateDebut = Date.Now
        End If
        If (dtpDateFin.Text <> "") Then
            dtpDateFin.ValidateText()
            MyListeRecapCaisse.DateFin = dtpDateFin.Value
        Else
            MyListeRecapCaisse.DateFin = Date.Now
        End If
        MyListeRecapCaisse.Vide = rdbNonVidees.Checked
        MyListeRecapCaisse.LibellePoste = cmbPoste.SelectedValue
        MyListeRecapCaisse.Titre = "CREDIT"
        MyListeRecapCaisse.ListeDesVentes = True
        MyListeRecapCaisse.ShowDialog()
        MyListeRecapCaisse.Close()
        MyListeRecapCaisse.Dispose()
    End Sub

    Private Sub bCNAM_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCNAM.Click
        Dim MyListeRecapCaisse As New fListesPourRecapCaisse
        If (dtpDateDebut.Text <> "") Then
            dtpDateDebut.ValidateText()
            MyListeRecapCaisse.DateDebut = dtpDateDebut.Value
        Else
            MyListeRecapCaisse.DateDebut = Date.Now
        End If
        If (dtpDateFin.Text <> "") Then
            dtpDateFin.ValidateText()
            MyListeRecapCaisse.DateFin = dtpDateFin.Value
        Else
            MyListeRecapCaisse.DateFin = Date.Now
        End If
        MyListeRecapCaisse.Vide = rdbNonVidees.Checked
        MyListeRecapCaisse.LibellePoste = cmbPoste.SelectedValue
        MyListeRecapCaisse.Titre = "CNAM"
        MyListeRecapCaisse.ListeDesVentes = True
        MyListeRecapCaisse.ShowDialog()
        MyListeRecapCaisse.Close()
        MyListeRecapCaisse.Dispose()

    End Sub

    Private Sub rdbToutes_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbToutes.CheckedChanged
        If rdbToutes.Checked = True Then
            dtpDateDebut.Enabled = True
            dtpDateFin.Enabled = True
            dtpDateDebut.Focus()
            dtpDateDebut.Value = System.DateTime.Today
            dtpDateFin.Value = System.DateTime.Today + " 23:59:59"
        End If
    End Sub

    Private Sub cmbPoste_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbPoste.KeyUp
        If e.KeyCode = Keys.Enter Then
            Condition = " 1=1 "

            If cmbPoste.Text <> "" Then
                Condition += " AND LibellePoste ='" + cmbPoste.Text + "'"
            End If
            If rdbNonVidees.Checked = True Then
                Condition += " AND Vider='False' "
            End If
            If dtpDateDebut.Text <> "" And dtpDateFin.Text <> "" And rdbToutes.Checked = True Then
                Condition += " AND Date >=CAST('" + dtpDateDebut.Text + "' as date)AND Date <=CAST('" + _
                DateAdd(DateInterval.Day, 1, dtpDateFin.Value) + "' as date)"
            End If

            AfficherCaisse()

            bVenteAuComptant.Focus()
        End If
    End Sub

    Private Sub cmbPoste_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbPoste.TextChanged
        AfficherCaisse()
    End Sub

    Private Sub dtpDateDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDateDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            dtpDateFin.Focus()
        End If
    End Sub

    Private Sub bVider_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bVider.Click

        If MsgBox("Voulez vous vraiment vider la caisse ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Vidage") = MsgBoxResult.No Then
            Exit Sub
        End If

        Dim Cond As String = " 1=1 "

        If cmbPoste.Text <> "" Then
            Cond += " AND LibellePoste=" + cmbPoste.Text
        End If

        ' ''''''''
        'If rdbNonVidees.Checked = False Then
        '    StrSQL = "UPDATE DIFFERENCE_RECAP_CAISSE set Vider =1 where " + Cond + " AND DateDu >=" + Quote(dtpDateDebut.Value) + " AND DateAu <= " + Quote(dtpDateFin.Value)
        'Else
        '    StrSQL = "UPDATE DIFFERENCE_RECAP_CAISSE set Vider =1 where " + Cond
        'End If

        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = StrSQL
        'Try
        '    cmd.ExecuteNonQuery()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try
        '''''''
        StrSQL = "Update MOUVEMENT_ETATS set Vider=1 where " + Cond

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL
        Try
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        'vidage du reglement client 
        StrSQL = "Update REGLEMENT_CLIENT set Vider=1 where " + Cond

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL
        Try
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        'vidage des ventes client 
        StrSQL = "Update VENTE set Vider=1 where " + Cond

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL
        Try
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        'vidage des ventes supprimées 
        StrSQL = "Update VENTE_SUPPRIME set Vider=1 where " + Cond

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL
        Try
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try


        'vidage reglement mutuelle
        StrSQL = "Update REGLEMENT_MUTUELLE set Vider=1 where " + Cond

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL
        Try
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        'vidage reglement CNAM
        StrSQL = "Update REGLEMENT_CNAM set Vider=1 where " + Cond

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL
        Try
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        'Vidage caisse 
        If cmbPoste.Text <> "" Then
            StrSQL = "Update CAISSE set MontantCaisse=0 where Poste = '" + cmbPoste.Text + "'"

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            Try
                cmd.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

        Else
            StrSQL = "Update CAISSE set MontantCaisse=0 "

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            Try
                cmd.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

        End If

        MsgBox("Vidage terminé ... !", MsgBoxStyle.Information, "Information")
        AfficherCaisse()

    End Sub

    Private Sub tTotalReel_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTotalReel.KeyUp
        If e.KeyCode = Keys.Enter Then

            If cmbPoste.Text <> "" Then
                Condition += " AND LibellePoste ='" + cmbPoste.Text + "'"

                CondCalculDiff += " AND LibellePoste ='" + cmbPoste.Text + "'"

            End If
            If rdbNonVidees.Checked = True Then
                Condition += " AND Vider='False' "
            End If

            If dtpDateDebut.Text <> "" And dtpDateFin.Text <> "" And rdbToutes.Checked = True Then
                Condition += " AND (Date BETWEEN '" + dtpDateDebut.Text + "' AND '" + dtpDateFin.Text + "') "

                CondCalculDiff += " AND DateDu = '" + dtpDateDebut.Text + "' AND DateAu = '" + dtpDateFin.Text + "' "

            End If

            ' Insertion ds la table diff
            ''''''''''''
            StrSQL = "SELECT count(*) FROM DIFFERENCE_RECAP_CAISSE " + CondCalculDiff
            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            If cmd.ExecuteScalar() = 0 Then
                tDifferenceReelCalcule.Text = tTotalReel.Text - lTotal.Text
                Try
                    If cmbPoste.Text = "" Then
                        StrSQL = "INSERT INTO DIFFERENCE_RECAP_CAISSE values(" + Quote(dtpDateDebut.Text) + ", " + _
                            Quote(dtpDateFin.Text) + ", NULL , " + Val(tTotalReel.Text).ToString() + ", " + _
                            Val(lTotal.Text).ToString() + ", " + Val(tDifferenceReelCalcule.Text).ToString() + ",0)"
                    Else
                        StrSQL = "INSERT INTO DIFFERENCE_RECAP_CAISSE values(" + Quote(dtpDateDebut.Text) + ", " + _
                            Quote(dtpDateFin.Text) + ", " + Quote(cmbPoste.Text) + ", " + Val(tTotalReel.Text).ToString() + ", " + _
                            Val(lTotal.Text).ToString() + ", " + Val(tDifferenceReelCalcule.Text).ToString() + ",0)"
                    End If

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                End Try
            Else
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM DIFFERENCE_RECAP_CAISSE " + CondCalculDiff
                    cmd.ExecuteNonQuery()

                    If cmbPoste.Text = "" Then
                        StrSQL = "INSERT INTO DIFFERENCE_RECAP_CAISSE values(" + Quote(dtpDateDebut.Text) + ", " + _
                            Quote(dtpDateFin.Text) + ", NULL , " + Val(tTotalReel.Text).ToString() + ", " + _
                            Val(lTotal.Text).ToString() + ", " + Val(tDifferenceReelCalcule.Text).ToString() + ",0)"
                    Else
                        StrSQL = "INSERT INTO DIFFERENCE_RECAP_CAISSE values(" + Quote(dtpDateDebut.Text) + ", " + _
                            Quote(dtpDateFin.Text) + ", " + Quote(cmbPoste.Text) + ", " + Val(tTotalReel.Text).ToString() + ", " + _
                            Val(lTotal.Text).ToString() + ", " + Val(tDifferenceReelCalcule.Text).ToString() + ",0)"
                    End If

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                End Try

            End If
            bImprimerDifferenceCaisse.Focus()

            ''''''''''''


            tDifferenceReelCalcule.Text = Val(tTotalReel.Text - lTotal.Text).ToString("### ### ##0.000")
        End If
    End Sub

    Private Sub bImprimerDifferenceCaisse_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bImprimerDifferenceCaisse.Click
        Dim CondCrystal As String = " 1 = 1 "

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression de Différence Caisse Théorique et physique" Then
                num = I
            End If
        Next

        CondCrystal += " AND {DIFFERENCE_RECAP_CAISSE.Vider} = false "

        If cmbPoste.Text <> "" Then
            CondCrystal += " AND {DIFFERENCE_RECAP_CAISSE.LibellePoste} ='" + cmbPoste.Text + "'"
        End If
        If dtpDateDebut.Text <> "" And dtpDateFin.Text <> "" And rdbToutes.Checked = True Then
            'CondCrystal += " AND {DIFFERENCE_RECAP_CAISSE.DateDu} = DateTime('" + dtpDateDebut.Text + "') AND {DIFFERENCE_RECAP_CAISSE.DateAu} = DateTime('" + dtpDateFin.Text + "') "

            CondCrystal += " AND {DIFFERENCE_RECAP_CAISSE.DateDu} >= DateTime('" + dtpDateDebut.Text + "') AND {DIFFERENCE_RECAP_CAISSE.DateAu} <= DateTime('" + dtpDateFin.Text + "') "

        End If

        CR2.FileName = Application.StartupPath + "\EtatDifferenceCaisseCalculeReel.rpt"

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo

        For Each tbCurrent In CR2.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent
        CR2.RecordSelectionFormula = CondCrystal

        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR2
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression de Différence Caisse Théorique et physique"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
        Me.Hide()

    End Sub

    Private Sub bExportExcel_Click(sender As System.Object, e As System.EventArgs) Handles bExportExcel.Click
        ExportExcel()
    End Sub

    Private Sub ExportExcel()
        Dim xls As New C1XLBook
        Dim i As Integer = 0, j As Integer = 0, k As Integer = 0
        Dim Sheet As XLSheet = xls.Sheets(0)
        Dim Style As New XLStyle(xls)
        Dim BackStyle As New XLStyle(xls)
        Dim yFontconverter As New FontConverter
        Dim Col As C1.Win.C1TrueDBGrid.C1DataColumn
        Dim OKBackStyle As Boolean = False
        Dim BaseTableau As Integer = 0
        Dim cmd As New SqlCommand
        cmd.Connection = ConnectionServeur

        BaseTableau = 0
        Sheet(BaseTableau, 0).Value = "RECAPULTATIF DE CAISSE"
        BaseTableau += 2
        Sheet(BaseTableau, 0).Value = "Critères:"
        BaseTableau += 1
        Sheet(BaseTableau, 0).Value = "Poste:"
        Sheet(BaseTableau, 1).Value = cmbPoste.Text
        Sheet(BaseTableau, 2).Value = "Vente"
        Sheet(BaseTableau, 3).Value = IIf(rdbToutes.Checked = True, "Toutes", "Non vidées")
        If rdbToutes.Checked = True Then
            Sheet(BaseTableau, 4).Value = "Du:"
            Sheet(BaseTableau, 5).Value = dtpDateDebut.Text
            Sheet(BaseTableau, 6).Value = "Au:"
            Sheet(BaseTableau, 7).Value = dtpDateFin.Text
        End If
        BaseTableau += 2
        Sheet(BaseTableau, 1).Value = "Ventes Au Comptant"
        Sheet(BaseTableau, 2).Value = "Règlements crédit+Mutuelles"
        Sheet(BaseTableau, 3).Value = "Totaux"
        BaseTableau += 1
        Sheet(BaseTableau, 0).Value = "Espèce:"
        Sheet(BaseTableau, 1).Value = lEspeceVente.Text
        Sheet(BaseTableau, 2).Value = lEspeceReglement.Text
        Sheet(BaseTableau, 3).Value = lTotalEspece.Text
        BaseTableau += 1
        Sheet(BaseTableau, 0).Value = "Chèque:"
        Sheet(BaseTableau, 1).Value = lChequeVente.Text
        Sheet(BaseTableau, 2).Value = lChequeReglement.Text
        Sheet(BaseTableau, 3).Value = lTotalCheque.Text
        BaseTableau += 1
        Sheet(BaseTableau, 0).Value = "Carte:"
        Sheet(BaseTableau, 1).Value = lCarteVente.Text
        Sheet(BaseTableau, 2).Value = lCarteReglement.Text
        Sheet(BaseTableau, 3).Value = lTotalReglement.Text
        BaseTableau += 1
        Sheet(BaseTableau, 0).Value = "Traite:"
        Sheet(BaseTableau, 1).Value = lTraiteVente.Text
        Sheet(BaseTableau, 2).Value = lTraiteReglement.Text
        Sheet(BaseTableau, 3).Value = lTotalTraite.Text

        BaseTableau += 1
        Sheet(BaseTableau, 0).Value = "Totaux:"
        Sheet(BaseTableau, 1).Value = lTotalVente.Text
        Sheet(BaseTableau, 2).Value = lTotalReglement.Text
        Sheet(BaseTableau, 3).Value = lTotalTotaux.Text

        BaseTableau += 1
        Sheet(BaseTableau, 2).Value = "Fond de caisse:"
        Sheet(BaseTableau, 3).Value = lFondCaisse.Text
        BaseTableau += 1
        Sheet(BaseTableau, 2).Value = "Total Théorique:"
        Sheet(BaseTableau, 3).Value = lTotal.Text

        BaseTableau += 1

        Sheet.Rows(BaseTableau).Height *= 3
        BaseTableau += 1

        Application.DoEvents()
        xls.Sheets(0).Columns(0).Width = 1000
        ' Lecture de l'option Format du fichier Excel'
        FormatExcel = GetSetting("PHARMA", "PHARMA", "FormatExcel", "Excel")
        ' Lecture de l'option Dossier du fichier Excel'
        DossierExcel = GetSetting("PHARMA", "PHARMA", "DossierExcel", "")
        If DossierExcel = "" Then
            'DossierExcel = "C:\ExportAlliance"
            MsgBox("Veuiller Saisir le Dossier d'export Excel dans le menu Paramètres Généraux")
            Exit Sub
        End If

        If System.IO.Directory.Exists(DossierExcel) = False Then
            System.IO.Directory.CreateDirectory(DossierExcel)
        End If

        Dim yNomFichierXLS As String = ""
        yNomFichierXLS = DossierExcel + "\RecapCaisse" + Format(Date.Now, "ddMMyyyy-hhmmss") + ".XLS"

        Try
            If FormatExcel = "EXCEL2007" Then
                yNomFichierXLS = Replace(yNomFichierXLS, ".XLS", ".XLSX")
                xls.Save(yNomFichierXLS, FileFormat.OpenXml)
            Else
                xls.Save(yNomFichierXLS, FileFormat.Biff8)
            End If
            System.Diagnostics.Process.Start(yNomFichierXLS)
        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
        End Try
    End Sub
End Class