﻿Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms
Imports System.Byte
Imports System.Drawing
Imports DirectX.Capture
Imports System.Drawing.Imaging
Imports System.IO.Ports
Imports System.Data.SqlTypes
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports System.Net.Mail
Imports System.ComponentModel

Public Class fVenteDetailImage
    '#############################################

    Declare Function TWAIN_AcquireToClipboard Lib "EZTW32.DLL" (ByVal hwndApp&, ByVal wPixTypes&) As Integer
    Declare Function TWAIN_SetHideUI Lib "Eztw32.dll" Alias "TWAIN_SetHideUI" (ByVal ui As Long) As Long
    Declare Function TWAIN_OpenDefaultSource Lib "Eztw32.DLL" Alias "TWAIN_OpenDefaultSource" (ByVal hwnd As Long) As Integer
    Declare Function TWAIN_SetCurrentResolution Lib "Eztw32.dll" Alias "TWAIN_SetCurrentResolution" (ByVal neufdix As Double) As Long
    Declare Function TWAIN_SetCurrentPixelType Lib "Eztw32.dll" Alias "TWAIN_SetCurrentPixelType" (ByVal deux As Long) As Long
    Declare Function TWAIN_LoadSourceManager Lib "Eztw32.dll" Alias "TWAIN_LoadSourceManager" () As Long
    Declare Function TWAIN_SetCurrentUnits Lib "Eztw32.dll" Alias "TWAIN_SetCurrentUnits" (ByVal zero As Long) As Long
    Declare Function TWAIN_SetBitDepth Lib "Eztw32.dll" Alias "TWAIN_SetBitDepth" (ByVal zero As Long) As Long
    Declare Function TWAIN_OpenSourceManager Lib "EZTW32.DLL" Alias "TWAIN_OpenSourceManager" (ByVal hwnd As Long) As Long
    Declare Function TWAIN_CloseSource Lib "EZTW32.DLL" Alias "TWAIN_CloseSource" () As Long
    Declare Function TWAIN_SelectImageSource Lib "EZTW32.DLL" Alias "TWAIN_SelectImageSource" (ByVal hwnd As Long) As Integer
    Declare Function TWAIN_State Lib "EZTW32.DLL" Alias "TWAIN_State" () As Integer

    Declare Function TWAIN_AcquireToFilename Lib "Eztwain3.dll" (ByVal hwndApp As Long, ByVal sFile As String) As Long
    Declare Function TWAIN_SelectFeeder Lib "EZTW32.DLL" (ByVal fYes As Long) As Long
    Declare Function TWAIN_SetCurrentResolution Lib "EZTW32.DLL" (ByVal nRes As Long) As Long

    Declare Function TWAIN_LogFile Lib "EZTW32.DLL" (ByVal fLog As Long) As Long
    Declare Function TWAIN_SetAutoScan Lib "EZTW32.DLL" (ByVal fYes As Long) As Long
    Declare Function TWAIN_SetRegion Lib "EZTW32.DLL" (ByVal L As Double, ByVal T As Double, ByVal R As Double, ByVal B As Double) As Long

    Declare Function TWAIN_AcquireMultipageFile Lib "EZTW32.DLL" (ByVal hwndApp As Long, ByVal FileName As String) As Long
    Declare Function TWAIN_LastErrorCode Lib "EZTW32.DLL" () As Long
    Declare Function TWAIN_ReportLastError Lib "EZTW32.DLL" (ByVal pzMsg As String) As Long

    Declare Function TWAIN_GetCurrentResolution Lib "EZTW32.DLL" () As Long
    '#############################################
    Dim cmdVente As New SqlCommand
    Dim cbVente As New SqlCommandBuilder
    Dim dsVente As New DataSet
    Dim daVente As New SqlDataAdapter

    Private m_barrImg As Byte()
    Private iBytesRead As Integer
    Private ImageCarnet As Image

    Dim dr As DataRow

    Private BtnChoisir As String = ""
    Public ModeVenteImage As String
    Public NumeroVenteModif As String

    Dim StrSQL As String = ""
    Public NumeroligneVenteImage As Integer = 0
    Dim NumeroVenteImage As String
    Dim DataRowRecherche As DataRow

    Private X_initialC As Integer
    Private Y_initialC As Integer

    Private X_initialO As Integer
    Private Y_initialO As Integer
    Dim TabPrecedent As String = ""


    Private Sub bCapturerO_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCapturerO.Click
        Dim MyCameraOrdonnance As New fCameraOrdonnance
        MyCameraOrdonnance.ShowDialog()
        If MyCameraOrdonnance.OK = True Then
            Image_Ordonnance.Image = MyCameraOrdonnance.ImageOrdonnance
            Image_Ordonnance.SizeMode = PictureBoxSizeMode.Zoom 'CenterImage


            If Image_Ordonnance.Image Is Nothing Then
                bValiderO.Enabled = False
            Else
                bValiderO.Enabled = True
            End If

        End If

    End Sub

    Private Sub bCapurerC_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCapurerC.Click
        Dim MyCameraClient As New fCameraClient
        MyCameraClient.ShowDialog()
        If MyCameraClient.OK = True Then
            Image_Client.Image = MyCameraClient.ImageClient
            Image_Client.SizeMode = PictureBoxSizeMode.Zoom 'CenterImage

            If Image_Client.Image Is Nothing Then
                bValiderC.Enabled = False
            Else
                bValiderC.Enabled = True
            End If

        End If

    End Sub

    Private Sub bParcourirC_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        'le btn 
        'BtnChoisir = "bParcourir"
        OpenFileDialog1.Filter = "All Pictures|*.bmp;*.gif;*.jpg;*.png|Bitmap|*.bmp|GIF|*.gif|JPEG|*.jpg|PNG|*.png"

        If (OpenFileDialog1.ShowDialog() = DialogResult.OK) Then

            Image_Client.Image = New Bitmap(OpenFileDialog1.FileName)

        End If
    End Sub

    Private Sub bParcourirO_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bParcourirO.Click
        'le btn 
        'BtnChoisir = "bParcourir"
        OpenFileDialog1.Filter = "All Pictures|*.bmp;*.gif;*.jpg;*.png|Bitmap|*.bmp|GIF|*.gif|JPEG|*.jpg|PNG|*.png"

        If (OpenFileDialog1.ShowDialog() = DialogResult.OK) Then

            Image_Ordonnance.Image = New Bitmap(OpenFileDialog1.FileName)

            bValiderO.Enabled = True

        End If
    End Sub


    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

        'Dim NumeroVenteImage As String = ""
        'Dim NumeroVente As String = ""
        'Dim CodeArticle As String = ""
        ''------image Ordonnance
        'If Image_Ordonnance.Image Is Nothing Then
        '    Image_Ordonnance.Image = Nothing
        'Else

        '    If BtnChoisir = "bParcourir" Then

        '        Image_Ordonnance.Image = New Bitmap(OpenFileDialog1.FileName)
        '        Dim fiImage As New FileInfo(OpenFileDialog1.FileName)
        '        Dim fs As New FileStream(OpenFileDialog1.FileName, FileMode.Open, FileAccess.Read, FileShare.Read)

        '        m_barrImg = New Byte(Convert.ToInt32(fiImage.Length)) {}

        '        Dim iBytesRead As Integer = fs.Read(m_barrImg, 0, Convert.ToInt32(fiImage.Length))

        '        fs.Close()

        '    End If


        'End If


        'cmdVente.Connection = ConnectionServeur
        'cmdVente.CommandText = "Select * FROM VENTE_DETAILS_IMAGE  "
        'daVente = New SqlDataAdapter(cmdVente)
        'daVente.Fill(dsVente, "VENTE_DETAILS_IMAGE")
        'cbVente = New SqlCommandBuilder(daVente)

        ''If ModeVenteImage = "Ajout" Then

        ''    With dsVente
        ''        dr = .Tables("VENTE_DETAILS_IMAGE").NewRow

        ''        dr.Item("NumeroVenteImage") = RecupereNumeroVenteImage()
        ''        dr.Item("NumeroVente") = lNumeroVenteO.Text


        ''        If Image_Ordonnance.Image Is Nothing Then
        ''            dr.Item("PhotoOrdonnance") = Nothing
        ''        Else
        ''            dr.Item("PhotoOrdonnance") = ImageToByteArray(Image_Ordonnance.Image)
        ''        End If

        ''        If Image_Client.Image Is Nothing Then
        ''            dr.Item("PhotoClient") = Nothing
        ''        Else
        ''            dr.Item("PhotoClient") = ImageToByteArray(Image_Client.Image)
        ''        End If

        ''        dr.Item("NomClient") = lNomO.Text
        ''        dr.Item("Date") = lDateO.Text
        ''        dr.Item("Vendeur") = lVendeurO.Text

        ''        .Tables("VENTE_DETAILS_IMAGE").Rows.Add(dr)
        ''    End With

        ''Else 'Mode = Modif
        ''    With dsVente.Tables("VENTE_DETAILS_IMAGE")
        ''        dr = .Rows(0)

        ''        dr.Item("NumeroVenteImage") = RecupereNumeroVenteImage()
        ''        dr.Item("NumeroVente") = lNumeroVenteO.Text


        ''        If Image_Ordonnance.Image Is Nothing Then
        ''            dr.Item("PhotoOrdonnance") = Nothing
        ''        Else
        ''            dr.Item("PhotoOrdonnance") = ImageToByteArray(Image_Ordonnance.Image)
        ''        End If

        ''        If Image_Client.Image Is Nothing Then
        ''            dr.Item("PhotoClient") = Nothing
        ''        Else
        ''            dr.Item("PhotoClient") = ImageToByteArray(Image_Client.Image)
        ''        End If

        ''        dr.Item("NomClient") = lNomO.Text
        ''        dr.Item("Date") = lDateO.Text
        ''        dr.Item("Vendeur") = lVendeurO.Text
        ''    End With
        ''End If
        'daVente.Update(dsVente, "VENTE_DETAILS_IMAGE")

    End Sub

    Private Sub bSupprimerC_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerC.Click

        If MsgBox("Voulez-vous supprimer l'image  ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Photo") = MsgBoxResult.Yes Then

            Image_Client.Image = Nothing

            'delete les images vides
            Try
                StrSQL = "delete from VENTE_DETAILS_IMAGE where NumeroVenteImage='" + NumeroVenteImage + "'"
                cmdVente.Connection = ConnectionServeur
                cmdVente.CommandText = StrSQL
                cmdVente.ExecuteNonQuery()

            Catch ex As Exception
                WriteLine(ex.Message)
            End Try
        End If
        fVenteDetailImage_Load(sender, e)
    End Sub

    Private Sub bSupprimerO_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerO.Click

        If MsgBox("Voulez-vous supprimer l'image  ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Photo") = MsgBoxResult.Yes Then

            Image_Ordonnance.Image = Nothing

            'delete les images vides
            Try
                StrSQL = "delete from VENTE_DETAILS_IMAGE where NumeroVenteImage='" + NumeroVenteImage + "'"
                cmdVente.Connection = ConnectionServeur
                cmdVente.CommandText = StrSQL
                cmdVente.ExecuteNonQuery()

            Catch ex As Exception
                WriteLine(ex.Message)
            End Try
        End If
        fVenteDetailImage_Load(sender, e)
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click

        Me.Close()

    End Sub

    Private Sub bScannerO_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bScannerO.Click
        'ScannerOrdonnance()
        '21/05/2013
        ChoixScan()
        Scanner()
    End Sub
 
    Public Sub ChoixScan()

        Try

            Dim Ret As Integer = 0
            'Fermeture de la source du scan
            TWAIN_CloseSource()
            TWAIN_LoadSourceManager()
            TWAIN_OpenSourceManager(THdl)



            Ret = TWAIN_SelectImageSource(THdl)
            If Ret = 1 Then
                Ret = TWAIN_OpenDefaultSource(THdl)
            End If

            If TWAIN_State() < 4 Then
                'MsgBox("Impossible de paramêtrer le scanner", MsgBoxStyle.Exclamation)
            Else
                'parametres du scanner
                TWAIN_SetCurrentUnits(0) 'DPI
                TWAIN_SetCurrentResolution(150) 'en points par pouces
                TWAIN_SetCurrentPixelType(2) ' Scan format 0 = B&W, 1 Grey, 2 RGB
                TWAIN_SetBitDepth(8) ' Bit Depth 1, 2, 4, 8 but depends on Pixeltype
                TWAIN_SetHideUI(1)
            End If

        Catch ex As Exception
            MsgBox(ex.Message)
        End Try

    End Sub

    Public Sub Scanner()


        Dim Img As Drawing.Image = Nothing
        Try

            Dim Ret As Integer = 0

            ''Paramètre du Scan
            If TWAIN_State() < 4 Then
                'MsgBox("Impossible de paramêtrer le scanner", MsgBoxStyle.Exclamation)
            Else
                'parametres du scanner
                TWAIN_SetCurrentUnits(0) 'DPI
                TWAIN_SetCurrentResolution(600) 'en points par pouces
                TWAIN_SetCurrentPixelType(2) ' Scan format 0 = B&W, 1 Grey, 2 RGB
                TWAIN_SetBitDepth(8) ' Bit Depth 1, 2, 4, 8 but depends on Pixeltype
                TWAIN_SetHideUI(1)
            End If

            'scan du document
            Ret = TWAIN_AcquireToClipboard(THdl, 32)
            Img = CType(Clipboard.GetDataObject.GetData(System.Windows.Forms.DataFormats.Bitmap), Bitmap)

            Image_Ordonnance.Image = Img 'on la met dans une picture box

            Image_Ordonnance.SizeMode = PictureBoxSizeMode.Zoom

            bValiderO.Enabled = True
        Catch ex As Exception
            Img = Nothing
        End Try

        'Return Img

    End Sub

    Private Sub ScannerOrdonnance()

        If ChoixScanO() Then
            ScannerO()
        End If

    End Sub


    '------------- scan
    Dim THdl As Integer = 0

    Public Function ChoixScanO()

        Try

            Dim Ret As Integer = 0
            'Fermeture de la source du scan
            TWAIN_CloseSource()
            TWAIN_LoadSourceManager()
            TWAIN_OpenSourceManager(THdl)

            Ret = TWAIN_SelectImageSource(THdl)
            If Ret = 1 Then
                Ret = TWAIN_OpenDefaultSource(THdl)
            Else

                'not OK
                Return False
            End If

            'it is ok
            Return True
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try

    End Function

    Public Sub ScannerO()

        Dim Img As Drawing.Image = Nothing
        Try

            Dim Ret As Integer = 0
      
            Ret = TWAIN_AcquireToClipboard(THdl, 32)

            Img = CType(Clipboard.GetDataObject.GetData(System.Windows.Forms.DataFormats.Bitmap), Bitmap)

            Image_Ordonnance.Image = Img 'on la met dans une picture box

            'Image_Ordonnance.SizeMode = PictureBoxSizeMode.Zoom

            bValiderO.Enabled = True


        Catch ex As Exception
            Img = Nothing
        End Try

    End Sub

    Private Sub bScannerC_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        ChoixScanC()
        ScannerC()
    End Sub
    Public Sub ScannerC()
        Dim Img As Drawing.Image = Nothing
        Try

            Dim Ret As Integer = 0

            ''Paramètre du Scan
            If TWAIN_State() < 4 Then
                'MsgBox("Impossible de paramêtrer le scanner", MsgBoxStyle.Exclamation)
            Else
                'parametres du scanner
                TWAIN_SetCurrentUnits(0) 'DPI
                TWAIN_SetCurrentResolution(600) 'en points par pouces
                TWAIN_SetCurrentPixelType(2) ' Scan format 0 = B&W, 1 Grey, 2 RGB
                TWAIN_SetBitDepth(8) ' Bit Depth 1, 2, 4, 8 but depends on Pixeltype
                TWAIN_SetHideUI(1)
            End If

            'scan du document
            Ret = TWAIN_AcquireToClipboard(THdl, 32)
            Img = CType(Clipboard.GetDataObject.GetData(System.Windows.Forms.DataFormats.Bitmap), Bitmap)

            Image_Client.Image = Img 'on la met dans une picture box
            'Pic_Carnet_CNAM.Image.Save(ChemainSauv, System.Drawing.Imaging.ImageFormat.Jpeg)
            Image_Client.SizeMode = PictureBoxSizeMode.Zoom
        Catch ex As Exception
            Img = Nothing
        End Try

        'Return Img

    End Sub

    Public Sub ChoixScanC()

        Try

            Dim Ret As Integer = 0
            'Fermeture de la source du scan
            TWAIN_CloseSource()
            TWAIN_LoadSourceManager()
            TWAIN_OpenSourceManager(THdl)



            Ret = TWAIN_SelectImageSource(THdl)
            If Ret = 1 Then
                Ret = TWAIN_OpenDefaultSource(THdl)
            End If

            If TWAIN_State() < 4 Then
                'MsgBox("Impossible de paramêtrer le scanner", MsgBoxStyle.Exclamation)
            Else
                'parametres du scanner
                TWAIN_SetCurrentUnits(0) 'DPI
                TWAIN_SetCurrentResolution(150) 'en points par pouces
                TWAIN_SetCurrentPixelType(2) ' Scan format 0 = B&W, 1 Grey, 2 RGB
                TWAIN_SetBitDepth(8) ' Bit Depth 1, 2, 4, 8 but depends on Pixeltype
                TWAIN_SetHideUI(1)
            End If

        Catch ex As Exception
            MsgBox(ex.Message)
        End Try

    End Sub


    Public Function ImageToByteArray(ByVal img As Image) As Byte()
        Dim stream As New MemoryStream
        img.Save(stream, System.Drawing.Imaging.ImageFormat.Jpeg)
        Return stream.ToArray
    End Function

    Public Function ByteArrayToImage(ByVal ByteArray As Byte()) As Image
        Dim stream As New MemoryStream(ByteArray, 0, ByteArray.Length)
        Return Image.FromStream(stream, True)
    End Function

    Private Sub bValiderO_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bValiderO.Click
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If Image_Ordonnance.Image Is Nothing Then
            MsgBox("Image Ordonnance Vide !!", MsgBoxStyle.Exclamation, "Ordonnance")
            Exit Sub
        End If
        '------------------------------ demande du mot de passe
        Dim myMotDePasse As New fMotDePasse
        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If

        If dsVente.Tables("VENTE_DETAILS_IMAGE") IsNot Nothing Then
            dsVente.Tables("VENTE_DETAILS_IMAGE").Clear()
        End If
        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = "Select * FROM VENTE_DETAILS_IMAGE  "
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "VENTE_DETAILS_IMAGE")
        cbVente = New SqlCommandBuilder(daVente)

        With dsVente
            dr = .Tables("VENTE_DETAILS_IMAGE").NewRow

            'dr.Item("NumeroVenteImage") = RecupereNumeroVenteImage()
            dr.Item("NumeroVente") = lNumeroVenteO.Text


            If Image_Ordonnance.Image Is Nothing Then
                dr.Item("Photo") = Nothing
            Else
                dr.Item("Photo") = ImageToByteArray(Image_Ordonnance.Image)
            End If

            dr.Item("TypePhoto") = "O" 'ImageToByteArray(Image_Client.Image)

            dr.Item("NomClient") = lNomO.Text
            dr.Item("Date") = lDateO.Text
            dr.Item("Vendeur") = lVendeurO.Text

            .Tables("VENTE_DETAILS_IMAGE").Rows.Add(dr)
        End With
        daVente.Update(dsVente, "VENTE_DETAILS_IMAGE")

        fVenteDetailImage_Load(sender, e)

        bValiderO.Enabled = False
    End Sub

    Private Sub bValiderC_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bValiderC.Click
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If Image_Client.Image Is Nothing Then
            MsgBox("Photo Vide !!", MsgBoxStyle.Exclamation, "Photo")
            Exit Sub
        End If

        '------------------------------ demande du mot de passe
        Dim myMotDePasse As New fMotDePasse
        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If

        If dsVente.Tables("VENTE_DETAILS_IMAGE") IsNot Nothing Then
            dsVente.Tables("VENTE_DETAILS_IMAGE").Clear()
        End If
        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = "Select * FROM VENTE_DETAILS_IMAGE  "
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "VENTE_DETAILS_IMAGE")
        cbVente = New SqlCommandBuilder(daVente)

        With dsVente
            dr = .Tables("VENTE_DETAILS_IMAGE").NewRow

            dr.Item("NumeroVente") = lNumeroVenteO.Text


            If Image_Client.Image Is Nothing Then
                dr.Item("Photo") = Nothing
            Else
                dr.Item("Photo") = ImageToByteArray(Image_Client.Image)
            End If

            dr.Item("TypePhoto") = "C"

            dr.Item("NomClient") = lNomO.Text
            dr.Item("Date") = lDateO.Text
            dr.Item("Vendeur") = lVendeurO.Text

            .Tables("VENTE_DETAILS_IMAGE").Rows.Add(dr)
        End With
        daVente.Update(dsVente, "VENTE_DETAILS_IMAGE")

        fVenteDetailImage_Load(sender, e)

        bValiderC.Enabled = False
    End Sub


    Private Sub ChargerVenteImage(ByVal pNumeroLigneVenteImage As Integer)


        If dsVente.Tables("VENTE_DETAILS_IMAGE") IsNot Nothing Then
            dsVente.Tables("VENTE_DETAILS_IMAGE").Clear()
        End If

        If Tab.SelectedTab Is TabOrdonnance Then
            TabPrecedent = "TabOrdonnance"

            StrSQL = " SELECT * FROM (  " + _
            " SELECT *, ROW_NUMBER() OVER (ORDER BY NumeroVenteImage) as row FROM VENTE_DETAILS_IMAGE WHERE NumeroVente='" + lNumeroVenteO.Text + "' and TypePhoto = 'O' " + _
            "              ) a WHERE row > " & pNumeroLigneVenteImage - 1 & " AND  row <= " & pNumeroLigneVenteImage

            cmdVente.Connection = ConnectionServeur
            cmdVente.CommandText = StrSQL
            daVente = New SqlDataAdapter(cmdVente)
            daVente.Fill(dsVente, "VENTE_DETAILS_IMAGE")
            cbVente = New SqlCommandBuilder(daVente)

        End If

        If Tab.SelectedTab Is TabClient Then

            TabPrecedent = "TabClient"

            StrSQL = " SELECT * FROM (  " + _
            " SELECT *, ROW_NUMBER() OVER (ORDER BY NumeroVenteImage) as row FROM VENTE_DETAILS_IMAGE  WHERE NumeroVente='" + lNumeroVenteC.Text + "' and TypePhoto = 'C' " + _
            "              ) a WHERE row > " & pNumeroLigneVenteImage - 1 & " AND  row <= " & pNumeroLigneVenteImage

            cmdVente.Connection = ConnectionServeur
            cmdVente.CommandText = StrSQL
            daVente = New SqlDataAdapter(cmdVente)
            daVente.Fill(dsVente, "VENTE_DETAILS_IMAGE")
            cbVente = New SqlCommandBuilder(daVente)

        End If

        initBoutons()


        'Lire le numéro Production


        If dsVente.Tables("VENTE_DETAILS_IMAGE").Rows.Count > 0 Then

            NumeroVenteImage = dsVente.Tables("VENTE_DETAILS_IMAGE").Rows(0).Item("NumeroVenteImage")

        Else

            NumeroVenteImage = "0"

        End If

        If dsVente.Tables("VENTE_DETAILS_IMAGE").Rows.Count > 0 Then

            DataRowRecherche = dsVente.Tables("VENTE_DETAILS_IMAGE").Select("NumeroVenteImage=" + Quote(NumeroVenteImage))(0)

            '---chargement les informations

            'affichage image de l'ordonnance
            If Tab.SelectedTab Is TabOrdonnance Then


                If IsDBNull(dsVente.Tables("VENTE_DETAILS_IMAGE").Rows(0)("Photo")) = True Then
                    Image_Ordonnance.Image = Nothing
                Else
                    ImageCarnet = ByteArrayToImage(dsVente.Tables("VENTE_DETAILS_IMAGE").Rows(dsVente.Tables("VENTE_DETAILS_IMAGE").Rows.Count - 1)("Photo"))
                    Image_Ordonnance.Image = ImageCarnet
                End If
            End If

            'affichage image de client
            If Tab.SelectedTab Is TabClient Then


                If IsDBNull(dsVente.Tables("VENTE_DETAILS_IMAGE").Rows(0)("Photo")) = True Then
                    Image_Client.Image = Nothing
                Else
                    ImageCarnet = ByteArrayToImage(dsVente.Tables("VENTE_DETAILS_IMAGE").Rows(dsVente.Tables("VENTE_DETAILS_IMAGE").Rows.Count - 1)("Photo"))
                    Image_Client.Image = ImageCarnet
                End If
            End If

            NumeroVenteImage = DataRowRecherche.Item("NumeroVenteImage")

        End If



    End Sub

    Private Function selectionLigneVenteImageSuivante()

        ' incrémenter le numéro 1 au variable global  
        NumeroligneVenteImage = NumeroligneVenteImage + 1
        Return NumeroligneVenteImage
    End Function
    Private Function selectionLigneVenteImagePrecedent()

        ' incrémenter le numéro 1 au variable global  
        NumeroligneVenteImage = NumeroligneVenteImage - 1
        Return NumeroligneVenteImage
    End Function



    Private Sub bNextO_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bNextO.Click

        'Appel Pour selectionner l'element suivant 
        NumeroligneVenteImage = selectionLigneVenteImageSuivante()

        'Appel pour charger les information 
        ChargerVenteImage(NumeroligneVenteImage)

    End Sub
    Private Sub bPreviousO_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPreviousO.Click
        'Appel Pour selectionner l'element suivant 
        NumeroligneVenteImage = selectionLigneVenteImagePrecedent()

        'Appel pour charger les information 
        ChargerVenteImage(NumeroligneVenteImage)
    End Sub


    Private Sub initBoutons()

        If NumeroligneVenteImage = selectionDernierLigneVenteImage() Then

            bNextO.Enabled = False
        Else

            bNextO.Enabled = True

        End If

        If selectionDernierLigneVenteImage() = 0 Then

            'Bloque navigation
            bNextO.Enabled = False

            bPreviousO.Enabled = False

        End If

        If NumeroligneVenteImage = 1 Then

            bPreviousO.Enabled = False
        Else
            bPreviousO.Enabled = True

        End If
    End Sub

    '------------------------------------les procédure de navigation avec NumeroligneProduction---------------------------------------------------------

    Private Function selectionDernierLigneVenteImage()
        Try
            Dim StrSQL As String = ""
            'Affécter le nombre de ligne au variable global  
            If Tab.SelectedTab Is TabOrdonnance Then
                StrSQL = " SELECT COUNT(*) FROM VENTE_DETAILS_IMAGE where TypePhoto = 'O' and NumeroVente='" + lNumeroVenteO.Text + "' "
            End If

            If Tab.SelectedTab Is TabClient Then
                StrSQL = " SELECT COUNT(*) FROM VENTE_DETAILS_IMAGE where TypePhoto = 'C' and NumeroVente='" + lNumeroVenteO.Text + "' "
            End If

            cmdVente.Connection = ConnectionServeur
            cmdVente.CommandText = StrSQL



            selectionDernierLigneVenteImage = cmdVente.ExecuteScalar()

            Return selectionDernierLigneVenteImage

        Catch ex As Exception
            Return 0
            WriteLine(ex.Message)
        End Try
    End Function

    Private Sub selectionPremierLigneVenteImage()

        NumeroligneVenteImage = 1

    End Sub

    Private Sub fVenteDetailImage_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        X_initialC = Image_Client.Width
        Y_initialC = Image_Client.Height

        X_initialO = Image_Ordonnance.Width
        Y_initialO = Image_Ordonnance.Height

        selectionPremierLigneVenteImage()
        ChargerVenteImage(NumeroligneVenteImage)

    End Sub

    Public Function sendEmailEnvoyer(Optional ByVal msg As String = "") As Boolean

        'Declaration
        Dim avecFichierLog As Boolean = True
        Dim fichierLogJoint As System.Net.Mail.Attachment
        Dim mailSent As Boolean = False
        Dim mail As Net.Mail.MailMessage = Nothing
        Dim smtp As Net.Mail.SmtpClient = Nothing
        Dim fichierLog As String
        Dim fichiers As String

        Dim AdresseMail As String = ""
        Dim MotDePasseMail As String = ""
        Dim PortMail As Integer = 0
        Dim SMTPMail As String = ""

        Try



            AdresseMail = RecupererValeur("AdresseMailDestinateur", "PARAMETRE_PHARMACIE")
            MotDePasseMail = RecupererValeur("MotDePasseDestinateur", "PARAMETRE_PHARMACIE")
            PortMail = RecupererValeur("PortMail", "PARAMETRE_PHARMACIE")
            SMTPMail = RecupererValeur("SmtpMail", "PARAMETRE_PHARMACIE")


            cmdVente.Connection = ConnectionServeur
            cmdVente.CommandText = "Select * FROM VENTE_DETAILS_IMAGE where NumeroVente='" + lNumeroVenteO.Text + "'  "
            daVente = New SqlDataAdapter(cmdVente)
            daVente.Fill(dsVente, "VENTE_DETAILS_IMAGE")
            cbVente = New SqlCommandBuilder(daVente)



            For i = 0 To dsVente.Tables("VENTE_DETAILS_IMAGE").Rows.Count - 1


                Try
                    'pour indiquer le nombre total de liste


                    'fichiers = CheckedListBox1.Items(i)

                    fichierLog = Environment.CurrentDirectory & "\" & "Log\Fichier\" + fichiers

                    fichierLogJoint = New System.Net.Mail.Attachment(fichierLog)


                    mail = New Net.Mail.MailMessage

                    mail.From = New Net.Mail.MailAddress(AdresseMail)

                    mail.Priority = Net.Mail.MailPriority.High

                    mail.To.Add(AdresseMail)

                    mail.Subject = "Test d'Envoi"

                    mail.Body = msg

                    mail.Attachments.Add(fichierLogJoint)

                    smtp = New Net.Mail.SmtpClient(SMTPMail)

                    smtp.Port = PortMail

                    smtp.Credentials = New System.Net.NetworkCredential(AdresseMail, MotDePasseMail)

                    smtp.EnableSsl = True

                    AddHandler smtp.SendCompleted, AddressOf SendCompletedCallback

                    smtp.SendAsync(mail, mail)

                    mailSent = True


                Catch ex As Exception

                    Debug.Print(ex.Message)

                End Try

                If mail IsNot Nothing Then

                    mail = Nothing

                End If

                If smtp IsNot Nothing Then

                    smtp = Nothing

                End If

                Return mailSent

            Next

        Catch ex As Exception

        End Try
    End Function

    Private Sub SendCompletedCallback(ByVal sender As Object, ByVal e As AsyncCompletedEventArgs)
        Dim myform As New fMain
        Try

            Dim test As Boolean = False

            Dim i As Integer

            Dim mail As Net.Mail.MailMessage = CType(e.UserState, Net.Mail.MailMessage)

            'pour tester l'envoi email asynchrone
            If e.Cancelled Then

                Throw New Exception("Send mail got cancelled")

            ElseIf e.Error IsNot Nothing Then

                Throw e.Error

            End If

            For i = (mail.Attachments.Count - 1) To 0 Step -1

                mail.Attachments(i).Dispose()

            Next

            mail.Dispose()

            'si l'e-mail a été envoyé avec succès
            RemoveHandler CType(sender, Net.Mail.SmtpClient).SendCompleted, AddressOf SendCompletedCallback


            myform.AfficheMessageNotification(True)

            'si il'y a une erreur d'envoi
        Catch ex As Exception

            myform.AfficheMessageNotification(False)
        End Try

    End Sub


    Public Function RecupererValeur(ByVal ValeurRecherche, ByVal Table)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""


        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " "
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function

    Private Sub TrackBarC_Scroll(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TrackBarC.Scroll
        LabelC.Text = "Zoom : " + TrackBarC.Value.ToString + " X"
        Image_Client.Height = Y_initialC + (100 * TrackBarC.Value)
        Image_Client.Width = X_initialC + (100 * TrackBarC.Value)
    End Sub
    Private Sub TrackBarO_Scroll(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TrackBarO.Scroll
        LabelO.Text = "Zoom : " + TrackBarO.Value.ToString + " X"
        Image_Ordonnance.Height = Y_initialO + (100 * TrackBarO.Value)
        Image_Ordonnance.Width = X_initialO + (100 * TrackBarO.Value)
    End Sub


    Private Sub TabOrdonnance_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TabOrdonnance.Enter


        If TabPrecedent = "TabClient" Then
            NumeroligneVenteImage = 1
        End If

        ChargerVenteImage(NumeroligneVenteImage)

    End Sub

    Private Sub TabClient_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TabClient.Enter


        If TabPrecedent = "TabOrdonnance" Then
            NumeroligneVenteImage = 1
        End If

        ChargerVenteImage(NumeroligneVenteImage)

    End Sub

    Private Sub bZoomO_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bZoomO.Click
        Dim myfZoomImage As New fZoomImage
        myfZoomImage.PictureBox_ZOOM.Image = Image_Ordonnance.Image
        myfZoomImage.ShowDialog()
    End Sub

    Private Sub bImprimerO_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimerO.Click

        Dim CondCrystal As String = ""

        CondCrystal = " 1=1 AND {Vue_Etat_Ordonnance_Medicale.NumeroVenteImage} = " + NumeroVenteImage + " "



        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Ordonnance Médicale" Then
                num = I
            End If
        Next
        CRO.FileName = Application.StartupPath + "\EtatPhotoOrdonnance.rpt"


        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo


        For Each tbCurrent In CRO.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent

        CRO.RecordSelectionFormula = CondCrystal
        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CRO
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression Ordonnance Médicale"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If

        Me.Close()

    End Sub

    Private Sub bZoomC_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bZoomC.Click
        Dim myfZoomImage As New fZoomImage
        myfZoomImage.PictureBox_ZOOM.Image = Image_Client.Image
        myfZoomImage.ShowDialog()
    End Sub

    Private Sub bImprimerC_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimerC.Click
        Dim CondCrystal As String = ""

        CondCrystal = " 1=1 AND {Vue_Etat_Ordonnance_Medicale.NumeroVenteImage} = " + NumeroVenteImage + " "

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Photo Client" Then
                num = I
            End If
        Next
        CRC.FileName = Application.StartupPath + "\EtatPhotoClient.rpt"

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo


        For Each tbCurrent In CRC.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent

        CRC.RecordSelectionFormula = CondCrystal
        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CRC
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression Photo Client"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If

        Me.Close()
    End Sub


    Private Sub bRotation_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRotation.Click
        If Image_Ordonnance.Image IsNot Nothing Then

            Image_Ordonnance.Image.RotateFlip(RotateFlipType.Rotate90FlipNone)
            Image_Ordonnance.Refresh()

            'bValiderO.Enabled = True
        Else
            Exit Sub
        End If

    End Sub



End Class