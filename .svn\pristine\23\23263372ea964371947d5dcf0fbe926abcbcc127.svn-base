﻿Imports System.Drawing.Printing
Imports System.IO
Public Class TestCodeABarre
    Dim EANfinal, PartieDroite As String
    Public codebinaire As String
    Dim cle, ordonneeY, abscisseX, ximpr, yimpr As Integer
    Dim espace As Char = Chr(32)
    Public chemin As String
    Dim rang As Integer
    Public VersBas As Integer = 12
    Public a(0 To 9, 0 To 3)
    Public caractere(0 To 9, 0 To 3)

    Public Code As String = ""
    Public Desigantion As String = ""
    Public Prix As String = ""

    Public Sub init()

    End Sub
    Private Sub CalculeImpression_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CalculeImpression.Click
        Dim j, somme, i, chiffre, resultat As Integer
        Dim multiplie As String = "131313131313"
        'Binaire.TxtChaine.Text = String.Empty
        Code = "619200838596"
        Desigantion = "PANADOL CCCC"
        Prix = "Prix : 15.260"

        Code = Code.Replace(" ", "") 'espaces interdits
        Code = Code.PadRight(12, "0") 'si len(code) < 12 compléter avec des 0 à droite
        Prix = Prix.Trim(" ")
        Desigantion = Desigantion.Trim(" ")
        If Desigantion.Contains("@") Then
            MessageBox.Show("Le caractère @ est interdit dans le titre.", "Code-barres EAN 13", MessageBoxButtons.OK, MessageBoxIcon.Information)
            'Titre.Focus()
            Exit Sub
        End If

        For i = 0 To Code.Length - 1
            chiffre = CInt(Code.Substring(i, 1))
            resultat = chiffre * CInt(multiplie.Substring(j, 1))
            j += 1
            somme += resultat
        Next

        cle = 10 - somme Mod 10
        If cle = 10 Then cle = 0
        'Label3.Text = cle
        EANfinal = Code & cle
        Call graphe(EANfinal)

        AddHandler PrintDocument1.PrintPage, AddressOf Me.ImprimerImage
        'PrintDocument1.Print()

    End Sub
    '**************** IMPRIMER
    Private Sub ImprimerImage(ByVal sender As Object, ByVal ev As PrintPageEventArgs)
        ev.HasMorePages = False      'imprimer une seule page
        ev.Graphics.CompositingMode = Drawing2D.CompositingMode.SourceOver
        ev.Graphics.CompositingQuality = Drawing2D.CompositingQuality.HighQuality
        ev.Graphics.DrawImageUnscaled(PictureBox2.Image, 0, 0)
    End Sub

    '************************************   CALCUL DU CODE BARRE
    Sub graphe(ByVal EANfinal)
        Dim i As Integer
        Dim chiffre As String = String.Empty
        For i = 0 To 9       '..... 1ère colonne caractère codé 0 à 9
            a(i, 0) = i
        Next

        a(0, 3) = "1110010"   '..... colonne table C partie droite (4ème colonne)
        a(1, 3) = "1100110"
        a(2, 3) = "1101100"
        a(3, 3) = "1000010"
        a(4, 3) = "1011100"
        a(5, 3) = "1001110"
        a(6, 3) = "1010000"
        a(7, 3) = "1000100"
        a(8, 3) = "1001000"
        a(9, 3) = "1110100"

        a(0, 2) = "0100111"   '..... colonne table B (3ème colonne)
        a(1, 2) = "0110011"
        a(2, 2) = "0011011"
        a(3, 2) = "0100001"
        a(4, 2) = "0011101"
        a(5, 2) = "0111001"
        a(6, 2) = "0000101"
        a(7, 2) = "0010001"
        a(8, 2) = "0001001"
        a(9, 2) = "0010111"

        '..... colonne jeu A

        a(0, 1) = "0001101"   '..... colonne table A (2ème colonne)
        a(1, 1) = "0011001"
        a(2, 1) = "0010011"
        a(3, 1) = "0111101"
        a(4, 1) = "0100011"
        a(5, 1) = "0110001"
        a(6, 1) = "0101111"
        a(7, 1) = "0111011"
        a(8, 1) = "0110111"
        a(9, 1) = "0001011"
        '                                   ci-dessous utile pour code-chaine du code-barres
        caractere(0, 1) = "A"   '..... colonne table A (2ème colonne)
        caractere(1, 1) = "B"
        caractere(2, 1) = "C"
        caractere(3, 1) = "D"
        caractere(4, 1) = "E"
        caractere(5, 1) = "F"
        caractere(6, 1) = "G"
        caractere(7, 1) = "H"
        caractere(8, 1) = "I"
        caractere(9, 1) = "J"

        caractere(0, 2) = "K"   '..... colonne table B (3ème colonne)
        caractere(1, 2) = "L"
        caractere(2, 2) = "M"
        caractere(3, 2) = "N"
        caractere(4, 2) = "O"
        caractere(5, 2) = "P"
        caractere(6, 2) = "Q"
        caractere(7, 2) = "R"
        caractere(8, 2) = "S"
        caractere(9, 2) = "T"

        caractere(0, 3) = "a"   '..... colonne table C (4ème colonne)
        caractere(1, 3) = "b"
        caractere(2, 3) = "c"
        caractere(3, 3) = "d"
        caractere(4, 3) = "e"
        caractere(5, 3) = "f"
        caractere(6, 3) = "g"
        caractere(7, 3) = "h"
        caractere(8, 3) = "i"
        caractere(9, 3) = "j"
        '............................... Table ab pour utilisation jeu A ou B en fonction du premier chiffre
        Dim ab(0 To 9, 0 To 6)
        Dim table, LitCellule, PartieGauche As String
        Dim ligne, colonne, ChiffreSuivant As Integer

        For i = 0 To 9       '..... 1ère colonne caractère codé 0 à 9
            ab(i, 0) = i
        Next

        i = 0
        table = "AAAAAAAAAAAAAABBBBBBABBBABBAABAABBAABBBAABABBAAABBABBABBABAA"

        For colonne = 1 To 6
            For ligne = 0 To 9
                ab(ligne, colonne) = table.Substring(i, 1)
                i += 1
            Next
        Next

        '...................................Le premier chiffre non codé
        Dim PremierChiffre As Integer = CInt(Code.Substring(0, 1))     '..... référence pour la 2ème table
        '.................... Partie de gauche sans le séparateur
        PartieGauche = String.Empty
        For i = 1 To 6      '....... les 6 chiffres suivants
            ChiffreSuivant = (CInt(Code.Substring(i, 1)))
            LitCellule = ab(PremierChiffre, i)   '............................. lit A ou B dans le 2ème tableau
            If LitCellule = "A" Then colonne = 1 Else colonne = 2
            PartieGauche = PartieGauche & espace & a(ChiffreSuivant, colonne)
            'Binaire.TxtChaine.Text += caractere(ChiffreSuivant, colonne)
        Next
        'Binaire.TxtChaine.Text += Chr(42)
        '..................... Gestion partie droite sans le séparateur=01010
        PartieDroite = String.Empty
        For i = 7 To 12
            chiffre = EANfinal.Substring(i, 1)
            PartieDroite = PartieDroite & a(chiffre, 3) & espace
            'Binaire.TxtChaine.Text += caractere(chiffre, 3)
        Next
        'Binaire.TxtChaine.Text = TxtCode.Text.Substring(0, 1) & Binaire.TxtChaine.Text & Chr(43)
        codebinaire = "101" & PartieGauche & " 01010 " & PartieDroite & "101"
        codebinaire = codebinaire.Replace(espace, "")

        If codebinaire.Length <> 95 Then  '95 = constante pour EAN 13
            MessageBox.Show("Erreur sur la longueur du code binaire.", "Code-barres EAN 13", MessageBoxButtons.OK, MessageBoxIcon.Error)
            'BtnReset.PerformClick()
            Exit Sub
        End If
        Call dessine()
    End Sub
    '************************ AFFICHAGE GRAPHIQUE DU CODE BARRES ..... Echelle > 1
    Sub dessine()
        Dim i, hauteur As Integer
        Dim couleur As Brush
        abscisseX = 115 : ordonneeY = 10
        PictureBox1.Image = New Bitmap(496, 105)
        Dim g As Graphics = Graphics.FromImage(PictureBox1.Image)
        g.CompositingQuality = Drawing2D.CompositingQuality.HighQuality
        g.Clear(Color.White)

        For i = 0 To codebinaire.Length - 1
            abscisseX += 3
            If i = 0 Or i = 2 Or i = 46 Or i = 48 Or i = 92 Or i = 94 Then couleur = Brushes.SteelBlue : hauteur = 87 Else hauteur = 80 : couleur = Brushes.Black
            If codebinaire.Substring(i, 1) = "1" Then
                Dim noir As New Rectangle(abscisseX, ordonneeY, 3, hauteur)
                g.FillRectangle(couleur, noir)
            Else
                Dim blanc As New Rectangle(abscisseX, ordonneeY, 3, hauteur)
                g.FillRectangle(Brushes.White, blanc)
            End If
        Next
        Call echellereduite()
    End Sub

    '************** CODE-BARRE A L'ECHELLE 1 - SUR DEMANDE IMAGE ENREGISTREE SOUS C:\EAN13\
    Sub echellereduite()
        Dim code2 As String = Code
        Dim i, hauteur, pas, gauche As Integer
        ordonneeY = 37
        Try
            PictureBox2.Image = New Bitmap(360, 128)
            Dim h As Graphics = Graphics.FromImage(PictureBox2.Image)
            h.CompositingQuality = Drawing2D.CompositingQuality.HighQuality          'Qualité image
            h.TextRenderingHint = Drawing.Text.TextRenderingHint.AntiAliasGridFit   'Qualité texte
            h.Clear(Color.White)
            Dim hFont, decal1, decal2, decal3 As Integer

            pas = 1 : abscisseX = 20 : gauche = 78 : hFont = 8
            decal1 = 3 : decal2 = 14 : decal3 = 62
           
            '.......................................... Dessin du code-barres selon 3 couleurs possibles
            For i = 0 To codebinaire.Length - 1
                If codebinaire.Substring(i, 1) = "1" Then
                    abscisseX += pas
                    If i = 0 Or i = 2 Or i = 46 Or i = 48 Or i = 92 Or i = 94 Then hauteur = ordonneeY + 7 Else hauteur = ordonneeY

                    Dim noir As New Rectangle(abscisseX, ordonneeY, pas, hauteur)
                    Dim noir1 As New Rectangle(abscisseX + 145, ordonneeY, pas, hauteur)

                    Dim couleur = New SolidBrush(Color.Black)
                    h.FillRectangle(couleur, noir)
                    h.FillRectangle(couleur, noir1)
                Else
                    abscisseX += pas
                    Dim blanc As New Rectangle(abscisseX, ordonneeY, pas, hauteur)
                    h.FillRectangle(Brushes.White, blanc)
                End If
            Next

            '................................ affichage graphique du titre - commentaire - chaine
            code2 = Code & cle
            Dim f As New Font("arial", hFont, FontStyle.Regular)
            Dim LargeurTitre As Integer = h.MeasureString(Desigantion, f).Width    'pour centrage du titre
            h.DrawString(Desigantion, f, Brushes.Black, 24, 25)
            h.DrawString(Desigantion, f, Brushes.Black, 170, 25)
          
            h.DrawString(code2.Substring(0, 1), f, Brushes.Black, 155 + decal1, 73)
            h.DrawString(code2.Substring(1, 6), f, Brushes.Black, 155 + decal2, 73)
            h.DrawString(code2.Substring(7, 6), f, Brushes.Black, 155 + decal3, 73)

            h.DrawString(code2.Substring(0, 1), f, Brushes.Black, decal1 + 10, 73)
            h.DrawString(code2.Substring(1, 6), f, Brushes.Black, decal2 + 10, 73)
            h.DrawString(code2.Substring(7, 6), f, Brushes.Black, decal3 + 10, 73)

            Dim LargeurCommentaire As Integer = h.MeasureString(Prix, f).Width    'pour centrage du commentaire

            h.DrawString(Prix, f, Brushes.Black, 24, 85)
            h.DrawString(Prix, f, Brushes.Black, 165, 85)
        Catch ex As Exception
            MessageBox.Show(Err.Description & " - Erreur no " & Err.Number, "Code-barres EAN 13", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub PictureBox2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles PictureBox2.Click

    End Sub
End Class