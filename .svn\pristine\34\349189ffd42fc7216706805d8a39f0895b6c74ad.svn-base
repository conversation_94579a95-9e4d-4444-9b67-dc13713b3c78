﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fMouvementDesClients
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fMouvementDesClients))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.lDifference = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.lSolde = New System.Windows.Forms.Label()
        Me.lTotalCredit = New System.Windows.Forms.Label()
        Me.lTotalDebit = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.lSoldeActuel = New System.Windows.Forms.Label()
        Me.bImprimer = New C1.Win.C1Input.C1Button()
        Me.gDetails = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.lSoldeAvant = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.lCreditAvant = New System.Windows.Forms.Label()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.lDebitAvant = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.lDateInitial = New System.Windows.Forms.Label()
        Me.lSoldeInitial = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.lClient = New System.Windows.Forms.Label()
        Me.dtpFin = New C1.Win.C1Input.C1DateEdit()
        Me.dtpDebut = New C1.Win.C1Input.C1DateEdit()
        Me.cmbClient = New C1.Win.C1List.C1Combo()
        Me.lAu = New System.Windows.Forms.Label()
        Me.lDu = New System.Windows.Forms.Label()
        Me.CR = New Pharma2000Premium.EtatMouvementDuClient()
        Me.tCodeRecherche = New C1.Win.C1Input.C1TextBox()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Panel.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.gDetails, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.dtpFin, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpDebut, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbClient, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodeRecherche, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.GroupBox2)
        Me.Panel.Controls.Add(Me.Label4)
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.lDifference)
        Me.Panel.Controls.Add(Me.Label6)
        Me.Panel.Controls.Add(Me.lSolde)
        Me.Panel.Controls.Add(Me.lTotalCredit)
        Me.Panel.Controls.Add(Me.lTotalDebit)
        Me.Panel.Controls.Add(Me.Label5)
        Me.Panel.Controls.Add(Me.lSoldeActuel)
        Me.Panel.Controls.Add(Me.bImprimer)
        Me.Panel.Controls.Add(Me.gDetails)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1278, 623)
        Me.Panel.TabIndex = 1
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.Label1)
        Me.GroupBox2.Location = New System.Drawing.Point(12, 13)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(292, 107)
        Me.GroupBox2.TabIndex = 77
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "Mouvement des Clients"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Cursor = System.Windows.Forms.Cursors.Default
        Me.Label1.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Label1.Font = New System.Drawing.Font("HandelGotDLig", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(7, 17)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(277, 80)
        Me.Label1.TabIndex = 10
        Me.Label1.Text = "Mouvement des" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "  Clients"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label4
        '
        Me.Label4.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label4.Location = New System.Drawing.Point(418, 567)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(69, 22)
        Me.Label4.TabIndex = 40
        Me.Label4.Text = "Total Crédit"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(1162, 20)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(104, 45)
        Me.bQuitter.TabIndex = 39
        Me.bQuitter.Text = "Fermer             F12   "
        Me.bQuitter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lDifference
        '
        Me.lDifference.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lDifference.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lDifference.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDifference.ForeColor = System.Drawing.Color.Green
        Me.lDifference.Location = New System.Drawing.Point(547, 589)
        Me.lDifference.Name = "lDifference"
        Me.lDifference.Size = New System.Drawing.Size(90, 22)
        Me.lDifference.TabIndex = 38
        Me.lDifference.Text = "0"
        Me.lDifference.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.lDifference.Visible = False
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label6.Location = New System.Drawing.Point(558, 567)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(69, 22)
        Me.Label6.TabIndex = 37
        Me.Label6.Text = "Différence"
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label6.Visible = False
        '
        'lSolde
        '
        Me.lSolde.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lSolde.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lSolde.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lSolde.Location = New System.Drawing.Point(1135, 583)
        Me.lSolde.Name = "lSolde"
        Me.lSolde.Size = New System.Drawing.Size(131, 22)
        Me.lSolde.TabIndex = 33
        Me.lSolde.Text = "0"
        Me.lSolde.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lTotalCredit
        '
        Me.lTotalCredit.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lTotalCredit.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lTotalCredit.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotalCredit.ForeColor = System.Drawing.Color.Green
        Me.lTotalCredit.Location = New System.Drawing.Point(407, 589)
        Me.lTotalCredit.Name = "lTotalCredit"
        Me.lTotalCredit.Size = New System.Drawing.Size(90, 22)
        Me.lTotalCredit.TabIndex = 36
        Me.lTotalCredit.Text = "0"
        Me.lTotalCredit.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lTotalDebit
        '
        Me.lTotalDebit.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lTotalDebit.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lTotalDebit.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotalDebit.ForeColor = System.Drawing.Color.Green
        Me.lTotalDebit.Location = New System.Drawing.Point(295, 589)
        Me.lTotalDebit.Name = "lTotalDebit"
        Me.lTotalDebit.Size = New System.Drawing.Size(90, 22)
        Me.lTotalDebit.TabIndex = 35
        Me.lTotalDebit.Text = "0"
        Me.lTotalDebit.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label5
        '
        Me.Label5.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label5.Location = New System.Drawing.Point(306, 567)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(69, 22)
        Me.Label5.TabIndex = 34
        Me.Label5.Text = "Total Débit"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lSoldeActuel
        '
        Me.lSoldeActuel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lSoldeActuel.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lSoldeActuel.Location = New System.Drawing.Point(1025, 583)
        Me.lSoldeActuel.Name = "lSoldeActuel"
        Me.lSoldeActuel.Size = New System.Drawing.Size(105, 22)
        Me.lSoldeActuel.TabIndex = 30
        Me.lSoldeActuel.Text = "Solde actuel"
        Me.lSoldeActuel.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bImprimer
        '
        Me.bImprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.imprmante1
        Me.bImprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bImprimer.Location = New System.Drawing.Point(1162, 73)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(104, 45)
        Me.bImprimer.TabIndex = 14
        Me.bImprimer.Text = "Imprimer                F9"
        Me.bImprimer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gDetails
        '
        Me.gDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gDetails.GroupByCaption = "Drag a column header here to group by that column"
        Me.gDetails.Images.Add(CType(resources.GetObject("gDetails.Images"), System.Drawing.Image))
        Me.gDetails.LinesPerRow = 2
        Me.gDetails.Location = New System.Drawing.Point(12, 126)
        Me.gDetails.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gDetails.Name = "gDetails"
        Me.gDetails.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gDetails.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gDetails.PreviewInfo.ZoomFactor = 75.0R
        Me.gDetails.PrintInfo.PageSettings = CType(resources.GetObject("gDetails.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gDetails.Size = New System.Drawing.Size(1254, 423)
        Me.gDetails.TabIndex = 13
        Me.gDetails.Text = "C1TrueDBGrid1"
        Me.gDetails.PropBag = resources.GetString("gDetails.PropBag")
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.Controls.Add(Me.tCodeRecherche)
        Me.GroupBox1.Controls.Add(Me.Label8)
        Me.GroupBox1.Controls.Add(Me.lSoldeAvant)
        Me.GroupBox1.Controls.Add(Me.Label10)
        Me.GroupBox1.Controls.Add(Me.lCreditAvant)
        Me.GroupBox1.Controls.Add(Me.Label9)
        Me.GroupBox1.Controls.Add(Me.lDebitAvant)
        Me.GroupBox1.Controls.Add(Me.Label7)
        Me.GroupBox1.Controls.Add(Me.lDateInitial)
        Me.GroupBox1.Controls.Add(Me.lSoldeInitial)
        Me.GroupBox1.Controls.Add(Me.Label3)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Controls.Add(Me.lClient)
        Me.GroupBox1.Controls.Add(Me.dtpFin)
        Me.GroupBox1.Controls.Add(Me.dtpDebut)
        Me.GroupBox1.Controls.Add(Me.cmbClient)
        Me.GroupBox1.Controls.Add(Me.lAu)
        Me.GroupBox1.Controls.Add(Me.lDu)
        Me.GroupBox1.Location = New System.Drawing.Point(310, 13)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(846, 107)
        Me.GroupBox1.TabIndex = 1
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Critères de recherche"
        '
        'lSoldeAvant
        '
        Me.lSoldeAvant.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lSoldeAvant.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lSoldeAvant.Location = New System.Drawing.Point(487, 67)
        Me.lSoldeAvant.Name = "lSoldeAvant"
        Me.lSoldeAvant.Size = New System.Drawing.Size(90, 22)
        Me.lSoldeAvant.TabIndex = 41
        Me.lSoldeAvant.Text = "0"
        Me.lSoldeAvant.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.lSoldeAvant.Visible = False
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label10.Location = New System.Drawing.Point(518, 50)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(34, 13)
        Me.Label10.TabIndex = 40
        Me.Label10.Text = "Solde"
        Me.Label10.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label10.Visible = False
        '
        'lCreditAvant
        '
        Me.lCreditAvant.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lCreditAvant.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lCreditAvant.Location = New System.Drawing.Point(365, 67)
        Me.lCreditAvant.Name = "lCreditAvant"
        Me.lCreditAvant.Size = New System.Drawing.Size(90, 22)
        Me.lCreditAvant.TabIndex = 39
        Me.lCreditAvant.Text = "0"
        Me.lCreditAvant.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.lCreditAvant.Visible = False
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.Location = New System.Drawing.Point(392, 50)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(34, 13)
        Me.Label9.TabIndex = 38
        Me.Label9.Text = "Crédit"
        Me.Label9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label9.Visible = False
        '
        'lDebitAvant
        '
        Me.lDebitAvant.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lDebitAvant.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDebitAvant.Location = New System.Drawing.Point(230, 67)
        Me.lDebitAvant.Name = "lDebitAvant"
        Me.lDebitAvant.Size = New System.Drawing.Size(90, 22)
        Me.lDebitAvant.TabIndex = 37
        Me.lDebitAvant.Text = "0"
        Me.lDebitAvant.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.lDebitAvant.Visible = False
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.Location = New System.Drawing.Point(263, 50)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(32, 13)
        Me.Label7.TabIndex = 36
        Me.Label7.Text = "Débit"
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label7.Visible = False
        '
        'lDateInitial
        '
        Me.lDateInitial.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lDateInitial.Location = New System.Drawing.Point(721, 57)
        Me.lDateInitial.Name = "lDateInitial"
        Me.lDateInitial.Size = New System.Drawing.Size(100, 20)
        Me.lDateInitial.TabIndex = 35
        Me.lDateInitial.Text = "0"
        Me.lDateInitial.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lSoldeInitial
        '
        Me.lSoldeInitial.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lSoldeInitial.Location = New System.Drawing.Point(721, 24)
        Me.lSoldeInitial.Name = "lSoldeInitial"
        Me.lSoldeInitial.Size = New System.Drawing.Size(100, 20)
        Me.lSoldeInitial.TabIndex = 34
        Me.lSoldeInitial.Text = "0"
        Me.lSoldeInitial.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Location = New System.Drawing.Point(659, 61)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(56, 13)
        Me.Label3.TabIndex = 32
        Me.Label3.Text = "Date initial"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(655, 28)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(60, 13)
        Me.Label2.TabIndex = 31
        Me.Label2.Text = "Solde initial"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lClient
        '
        Me.lClient.AutoSize = True
        Me.lClient.Location = New System.Drawing.Point(184, 26)
        Me.lClient.Name = "lClient"
        Me.lClient.Size = New System.Drawing.Size(33, 13)
        Me.lClient.TabIndex = 29
        Me.lClient.Text = "Client"
        '
        'dtpFin
        '
        Me.dtpFin.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpFin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpFin.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpFin.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFin.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFin.Location = New System.Drawing.Point(37, 70)
        Me.dtpFin.Name = "dtpFin"
        Me.dtpFin.Size = New System.Drawing.Size(141, 18)
        Me.dtpFin.TabIndex = 28
        Me.dtpFin.Tag = Nothing
        Me.dtpFin.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'dtpDebut
        '
        Me.dtpDebut.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDebut.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDebut.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDebut.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebut.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebut.Location = New System.Drawing.Point(37, 24)
        Me.dtpDebut.Name = "dtpDebut"
        Me.dtpDebut.Size = New System.Drawing.Size(141, 18)
        Me.dtpDebut.TabIndex = 27
        Me.dtpDebut.Tag = Nothing
        Me.dtpDebut.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebut.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbClient
        '
        Me.cmbClient.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbClient.Caption = ""
        Me.cmbClient.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbClient.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbClient.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbClient.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbClient.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbClient.Images.Add(CType(resources.GetObject("cmbClient.Images"), System.Drawing.Image))
        Me.cmbClient.Location = New System.Drawing.Point(223, 22)
        Me.cmbClient.MatchEntryTimeout = CType(2000, Long)
        Me.cmbClient.MaxDropDownItems = CType(5, Short)
        Me.cmbClient.MaxLength = 32767
        Me.cmbClient.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbClient.Name = "cmbClient"
        Me.cmbClient.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbClient.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbClient.Size = New System.Drawing.Size(354, 22)
        Me.cmbClient.TabIndex = 26
        Me.cmbClient.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbClient.PropBag = resources.GetString("cmbClient.PropBag")
        '
        'lAu
        '
        Me.lAu.AutoSize = True
        Me.lAu.Location = New System.Drawing.Point(11, 73)
        Me.lAu.Name = "lAu"
        Me.lAu.Size = New System.Drawing.Size(20, 13)
        Me.lAu.TabIndex = 11
        Me.lAu.Text = "Au"
        '
        'lDu
        '
        Me.lDu.AutoSize = True
        Me.lDu.Location = New System.Drawing.Point(10, 28)
        Me.lDu.Name = "lDu"
        Me.lDu.Size = New System.Drawing.Size(21, 13)
        Me.lDu.TabIndex = 10
        Me.lDu.Text = "Du"
        '
        'tCodeRecherche
        '
        Me.tCodeRecherche.AutoSize = False
        Me.tCodeRecherche.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodeRecherche.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodeRecherche.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodeRecherche.Location = New System.Drawing.Point(223, 66)
        Me.tCodeRecherche.Name = "tCodeRecherche"
        Me.tCodeRecherche.Size = New System.Drawing.Size(94, 22)
        Me.tCodeRecherche.TabIndex = 56
        Me.tCodeRecherche.Tag = Nothing
        Me.tCodeRecherche.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodeRecherche.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.Location = New System.Drawing.Point(186, 70)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(32, 13)
        Me.Label8.TabIndex = 57
        Me.Label8.Text = "Code"
        '
        'fMouvementDesClients
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1278, 623)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fMouvementDesClients"
        Me.Text = "fMouvementDesClients"
        Me.Panel.ResumeLayout(False)
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.gDetails, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.dtpFin, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpDebut, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbClient, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodeRecherche, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents gDetails As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents lAu As System.Windows.Forms.Label
    Friend WithEvents lDu As System.Windows.Forms.Label
    Friend WithEvents dtpFin As C1.Win.C1Input.C1DateEdit
    Friend WithEvents dtpDebut As C1.Win.C1Input.C1DateEdit
    Friend WithEvents cmbClient As C1.Win.C1List.C1Combo
    Friend WithEvents lClient As System.Windows.Forms.Label
    Friend WithEvents lDateInitial As System.Windows.Forms.Label
    Friend WithEvents lSoldeInitial As System.Windows.Forms.Label
    Friend WithEvents lSolde As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents lSoldeActuel As System.Windows.Forms.Label
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
    Friend WithEvents lDifference As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents lTotalCredit As System.Windows.Forms.Label
    Friend WithEvents lTotalDebit As System.Windows.Forms.Label
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents CR As Pharma2000Premium.EtatMouvementDuClient
    Friend WithEvents lCreditAvant As System.Windows.Forms.Label
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents lDebitAvant As System.Windows.Forms.Label
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents lSoldeAvant As System.Windows.Forms.Label
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents tCodeRecherche As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label8 As System.Windows.Forms.Label
End Class
