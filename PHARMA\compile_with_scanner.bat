@echo off
echo ========================================
echo Compilation Pharma2000 avec Scanner
echo ========================================

echo.
echo Etape 1: Nettoyage des fichiers temporaires...
if exist "bin\Debug\Pharma2000Premium.exe" (
    echo Sauvegarde de l'executable existant...
    copy "bin\Debug\Pharma2000Premium.exe" "bin\Debug\Pharma2000Premium_old_%date:~-4,4%%date:~-10,2%%date:~-7,2%.exe"
)

echo.
echo Etape 2: Compilation avec MSBuild...
"C:\Windows\Microsoft.NET\Framework64\v4.0.30319\MSBuild.exe" "Pharma2000Premium.vbproj" /p:Configuration=Debug /p:Platform=AnyCPU /verbosity:minimal /p:TreatWarningsAsErrors=false

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo COMPILATION REUSSIE !
    echo ========================================
    echo.
    echo L'executable avec le scanner de code a barres est disponible dans:
    echo bin\Debug\Pharma2000Premium.exe
    echo.
    echo Modifications incluses:
    echo - Champ Code Client modifiable
    echo - Support scanner de code a barres
    echo - Validation automatique des codes
    echo - Conversion en majuscules
    echo.
) else (
    echo.
    echo ========================================
    echo ERREURS DE COMPILATION DETECTEES
    echo ========================================
    echo.
    echo Tentative de compilation partielle...
    echo Veuillez verifier les erreurs ci-dessus.
    echo.
    echo Les modifications du scanner sont implementees dans:
    echo - fFicheClient.vb (lignes 102-111, 411, 1241-1310)
    echo.
)

echo.
echo Appuyez sur une touche pour continuer...
pause >nul
