﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fAffectationDesAutorisations
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fAffectationDesAutorisations))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.bSupprimerTous = New C1.Win.C1Input.C1Button()
        Me.bAjouterUn = New C1.Win.C1Input.C1Button()
        Me.bAjoutertous = New C1.Win.C1Input.C1Button()
        Me.bSupprimerUn = New C1.Win.C1Input.C1Button()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.bConfirmer = New C1.Win.C1Input.C1Button()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.gPointsDeControleAttribue = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.cmbModule = New C1.Win.C1List.C1Combo()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.gPointsDeControleNonAttribue = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.lstutilisateurs = New System.Windows.Forms.CheckedListBox()
        Me.cmbUtilisateursAcopierLesAutorisations = New C1.Win.C1List.C1Combo()
        Me.bAffecterDepuisUtilisateur = New C1.Win.C1Input.C1Button()
        Me.cmbProfil = New C1.Win.C1List.C1Combo()
        Me.bAffecterDepuisProfil = New C1.Win.C1Input.C1Button()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.lblitemselected = New System.Windows.Forms.Label()
        Me.Panel.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.gPointsDeControleAttribue, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.cmbModule, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gPointsDeControleNonAttribue, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox4.SuspendLayout()
        CType(Me.cmbUtilisateursAcopierLesAutorisations, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbProfil, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.GroupBox3)
        Me.Panel.Controls.Add(Me.Label5)
        Me.Panel.Controls.Add(Me.bConfirmer)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.GroupBox2)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Controls.Add(Me.GroupBox4)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1290, 661)
        Me.Panel.TabIndex = 2
        '
        'GroupBox3
        '
        Me.GroupBox3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.GroupBox3.Controls.Add(Me.bSupprimerTous)
        Me.GroupBox3.Controls.Add(Me.bAjouterUn)
        Me.GroupBox3.Controls.Add(Me.bAjoutertous)
        Me.GroupBox3.Controls.Add(Me.bSupprimerUn)
        Me.GroupBox3.Location = New System.Drawing.Point(580, 158)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(68, 491)
        Me.GroupBox3.TabIndex = 50
        Me.GroupBox3.TabStop = False
        '
        'bSupprimerTous
        '
        Me.bSupprimerTous.Image = Global.Pharma2000Premium.My.Resources.Resources.first_1
        Me.bSupprimerTous.Location = New System.Drawing.Point(11, 269)
        Me.bSupprimerTous.Name = "bSupprimerTous"
        Me.bSupprimerTous.Size = New System.Drawing.Size(45, 45)
        Me.bSupprimerTous.TabIndex = 7
        Me.bSupprimerTous.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSupprimerTous.UseVisualStyleBackColor = True
        Me.bSupprimerTous.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouterUn
        '
        Me.bAjouterUn.Image = Global.Pharma2000Premium.My.Resources.Resources.next_1
        Me.bAjouterUn.Location = New System.Drawing.Point(11, 146)
        Me.bAjouterUn.Name = "bAjouterUn"
        Me.bAjouterUn.Size = New System.Drawing.Size(45, 45)
        Me.bAjouterUn.TabIndex = 2
        Me.bAjouterUn.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouterUn.UseVisualStyleBackColor = True
        Me.bAjouterUn.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjoutertous
        '
        Me.bAjoutertous.Image = Global.Pharma2000Premium.My.Resources.Resources.last_1
        Me.bAjoutertous.Location = New System.Drawing.Point(11, 80)
        Me.bAjoutertous.Name = "bAjoutertous"
        Me.bAjoutertous.Size = New System.Drawing.Size(45, 45)
        Me.bAjoutertous.TabIndex = 1
        Me.bAjoutertous.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjoutertous.UseVisualStyleBackColor = True
        Me.bAjoutertous.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bSupprimerUn
        '
        Me.bSupprimerUn.Image = Global.Pharma2000Premium.My.Resources.Resources.previous_1
        Me.bSupprimerUn.Location = New System.Drawing.Point(11, 207)
        Me.bSupprimerUn.Name = "bSupprimerUn"
        Me.bSupprimerUn.Size = New System.Drawing.Size(45, 45)
        Me.bSupprimerUn.TabIndex = 6
        Me.bSupprimerUn.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSupprimerUn.UseVisualStyleBackColor = True
        Me.bSupprimerUn.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label5
        '
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label5.Location = New System.Drawing.Point(12, 17)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(745, 44)
        Me.Label5.TabIndex = 49
        Me.Label5.Text = "AFFECTATION DES AUTORISATIONS"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bConfirmer
        '
        Me.bConfirmer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bConfirmer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmer.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__1
        Me.bConfirmer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmer.Location = New System.Drawing.Point(1046, 15)
        Me.bConfirmer.Name = "bConfirmer"
        Me.bConfirmer.Size = New System.Drawing.Size(113, 45)
        Me.bConfirmer.TabIndex = 16
        Me.bConfirmer.Text = "Confirmer                 F3"
        Me.bConfirmer.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bConfirmer.UseVisualStyleBackColor = True
        Me.bConfirmer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(1163, 15)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(113, 45)
        Me.bAnnuler.TabIndex = 17
        Me.bAnnuler.Text = "Fermer              F12"
        Me.bAnnuler.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox2
        '
        Me.GroupBox2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox2.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox2.Controls.Add(Me.gPointsDeControleAttribue)
        Me.GroupBox2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox2.Location = New System.Drawing.Point(654, 158)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(622, 491)
        Me.GroupBox2.TabIndex = 15
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "Liste des Points de contrôle"
        '
        'gPointsDeControleAttribue
        '
        Me.gPointsDeControleAttribue.AllowColMove = False
        Me.gPointsDeControleAttribue.AllowRowSizing = C1.Win.C1TrueDBGrid.RowSizingEnum.None
        Me.gPointsDeControleAttribue.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gPointsDeControleAttribue.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.gPointsDeControleAttribue.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gPointsDeControleAttribue.GroupByCaption = "Drag a column header here to group by that column"
        Me.gPointsDeControleAttribue.Images.Add(CType(resources.GetObject("gPointsDeControleAttribue.Images"), System.Drawing.Image))
        Me.gPointsDeControleAttribue.LinesPerRow = 2
        Me.gPointsDeControleAttribue.Location = New System.Drawing.Point(20, 33)
        Me.gPointsDeControleAttribue.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
        Me.gPointsDeControleAttribue.Name = "gPointsDeControleAttribue"
        Me.gPointsDeControleAttribue.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gPointsDeControleAttribue.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gPointsDeControleAttribue.PreviewInfo.ZoomFactor = 75.0R
        Me.gPointsDeControleAttribue.PrintInfo.PageSettings = CType(resources.GetObject("gPointsDeControleAttribue.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gPointsDeControleAttribue.Size = New System.Drawing.Size(586, 437)
        Me.gPointsDeControleAttribue.TabIndex = 3
        Me.gPointsDeControleAttribue.Text = "C1TrueDBGrid1"
        Me.gPointsDeControleAttribue.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gPointsDeControleAttribue.PropBag = resources.GetString("gPointsDeControleAttribue.PropBag")
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox1.Controls.Add(Me.cmbModule)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Controls.Add(Me.gPointsDeControleNonAttribue)
        Me.GroupBox1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox1.Location = New System.Drawing.Point(12, 158)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(562, 491)
        Me.GroupBox1.TabIndex = 14
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Liste de tout les Points de contrôle"
        '
        'cmbModule
        '
        Me.cmbModule.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbModule.Caption = ""
        Me.cmbModule.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbModule.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbModule.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbModule.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbModule.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbModule.Images.Add(CType(resources.GetObject("cmbModule.Images"), System.Drawing.Image))
        Me.cmbModule.Location = New System.Drawing.Point(69, 33)
        Me.cmbModule.MatchEntryTimeout = CType(2000, Long)
        Me.cmbModule.MaxDropDownItems = CType(5, Short)
        Me.cmbModule.MaxLength = 32767
        Me.cmbModule.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbModule.Name = "cmbModule"
        Me.cmbModule.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbModule.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbModule.Size = New System.Drawing.Size(261, 21)
        Me.cmbModule.TabIndex = 15
        Me.cmbModule.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbModule.PropBag = resources.GetString("cmbModule.PropBag")
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.Location = New System.Drawing.Point(15, 37)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(48, 13)
        Me.Label1.TabIndex = 14
        Me.Label1.Text = "Module :"
        '
        'gPointsDeControleNonAttribue
        '
        Me.gPointsDeControleNonAttribue.AllowUpdate = False
        Me.gPointsDeControleNonAttribue.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gPointsDeControleNonAttribue.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.gPointsDeControleNonAttribue.GroupByCaption = "Drag a column header here to group by that column"
        Me.gPointsDeControleNonAttribue.Images.Add(CType(resources.GetObject("gPointsDeControleNonAttribue.Images"), System.Drawing.Image))
        Me.gPointsDeControleNonAttribue.Location = New System.Drawing.Point(16, 73)
        Me.gPointsDeControleNonAttribue.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gPointsDeControleNonAttribue.Name = "gPointsDeControleNonAttribue"
        Me.gPointsDeControleNonAttribue.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gPointsDeControleNonAttribue.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gPointsDeControleNonAttribue.PreviewInfo.ZoomFactor = 75.0R
        Me.gPointsDeControleNonAttribue.PrintInfo.PageSettings = CType(resources.GetObject("gPointsDeControleNonAttribue.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gPointsDeControleNonAttribue.Size = New System.Drawing.Size(526, 397)
        Me.gPointsDeControleNonAttribue.TabIndex = 4
        Me.gPointsDeControleNonAttribue.Text = "C1TrueDBGrid4"
        Me.gPointsDeControleNonAttribue.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gPointsDeControleNonAttribue.PropBag = resources.GetString("gPointsDeControleNonAttribue.PropBag")
        '
        'GroupBox4
        '
        Me.GroupBox4.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox4.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox4.Controls.Add(Me.lblitemselected)
        Me.GroupBox4.Controls.Add(Me.lstutilisateurs)
        Me.GroupBox4.Controls.Add(Me.cmbUtilisateursAcopierLesAutorisations)
        Me.GroupBox4.Controls.Add(Me.bAffecterDepuisUtilisateur)
        Me.GroupBox4.Controls.Add(Me.cmbProfil)
        Me.GroupBox4.Controls.Add(Me.bAffecterDepuisProfil)
        Me.GroupBox4.Controls.Add(Me.Label15)
        Me.GroupBox4.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox4.Location = New System.Drawing.Point(12, 66)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(1266, 86)
        Me.GroupBox4.TabIndex = 0
        Me.GroupBox4.TabStop = False
        '
        'lstutilisateurs
        '
        Me.lstutilisateurs.BackColor = System.Drawing.SystemColors.GradientInactiveCaption
        Me.lstutilisateurs.CheckOnClick = True
        Me.lstutilisateurs.FormattingEnabled = True
        Me.lstutilisateurs.Location = New System.Drawing.Point(131, 14)
        Me.lstutilisateurs.Name = "lstutilisateurs"
        Me.lstutilisateurs.Size = New System.Drawing.Size(247, 64)
        Me.lstutilisateurs.TabIndex = 17
        '
        'cmbUtilisateursAcopierLesAutorisations
        '
        Me.cmbUtilisateursAcopierLesAutorisations.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbUtilisateursAcopierLesAutorisations.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.cmbUtilisateursAcopierLesAutorisations.AutoCompletion = True
        Me.cmbUtilisateursAcopierLesAutorisations.AutoDropDown = True
        Me.cmbUtilisateursAcopierLesAutorisations.Caption = ""
        Me.cmbUtilisateursAcopierLesAutorisations.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbUtilisateursAcopierLesAutorisations.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbUtilisateursAcopierLesAutorisations.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbUtilisateursAcopierLesAutorisations.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbUtilisateursAcopierLesAutorisations.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbUtilisateursAcopierLesAutorisations.Images.Add(CType(resources.GetObject("cmbUtilisateursAcopierLesAutorisations.Images"), System.Drawing.Image))
        Me.cmbUtilisateursAcopierLesAutorisations.LimitToList = True
        Me.cmbUtilisateursAcopierLesAutorisations.Location = New System.Drawing.Point(1039, 53)
        Me.cmbUtilisateursAcopierLesAutorisations.MatchEntryTimeout = CType(2000, Long)
        Me.cmbUtilisateursAcopierLesAutorisations.MaxDropDownItems = CType(5, Short)
        Me.cmbUtilisateursAcopierLesAutorisations.MaxLength = 32767
        Me.cmbUtilisateursAcopierLesAutorisations.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbUtilisateursAcopierLesAutorisations.Name = "cmbUtilisateursAcopierLesAutorisations"
        Me.cmbUtilisateursAcopierLesAutorisations.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbUtilisateursAcopierLesAutorisations.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbUtilisateursAcopierLesAutorisations.Size = New System.Drawing.Size(221, 21)
        Me.cmbUtilisateursAcopierLesAutorisations.TabIndex = 16
        Me.cmbUtilisateursAcopierLesAutorisations.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbUtilisateursAcopierLesAutorisations.PropBag = resources.GetString("cmbUtilisateursAcopierLesAutorisations.PropBag")
        '
        'bAffecterDepuisUtilisateur
        '
        Me.bAffecterDepuisUtilisateur.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.bAffecterDepuisUtilisateur.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAffecterDepuisUtilisateur.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAffecterDepuisUtilisateur.Location = New System.Drawing.Point(745, 48)
        Me.bAffecterDepuisUtilisateur.Name = "bAffecterDepuisUtilisateur"
        Me.bAffecterDepuisUtilisateur.Size = New System.Drawing.Size(288, 30)
        Me.bAffecterDepuisUtilisateur.TabIndex = 15
        Me.bAffecterDepuisUtilisateur.Text = "Affecter les mêmes autorisations de l'utilisateur"
        Me.bAffecterDepuisUtilisateur.TextImageRelation = System.Windows.Forms.TextImageRelation.TextAboveImage
        Me.bAffecterDepuisUtilisateur.UseVisualStyleBackColor = True
        Me.bAffecterDepuisUtilisateur.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbProfil
        '
        Me.cmbProfil.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbProfil.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.cmbProfil.AutoCompletion = True
        Me.cmbProfil.AutoDropDown = True
        Me.cmbProfil.Caption = ""
        Me.cmbProfil.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbProfil.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbProfil.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbProfil.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbProfil.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbProfil.Images.Add(CType(resources.GetObject("cmbProfil.Images"), System.Drawing.Image))
        Me.cmbProfil.LimitToList = True
        Me.cmbProfil.Location = New System.Drawing.Point(1039, 16)
        Me.cmbProfil.MatchEntryTimeout = CType(2000, Long)
        Me.cmbProfil.MaxDropDownItems = CType(5, Short)
        Me.cmbProfil.MaxLength = 32767
        Me.cmbProfil.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbProfil.Name = "cmbProfil"
        Me.cmbProfil.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbProfil.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbProfil.Size = New System.Drawing.Size(221, 21)
        Me.cmbProfil.TabIndex = 14
        Me.cmbProfil.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbProfil.PropBag = resources.GetString("cmbProfil.PropBag")
        '
        'bAffecterDepuisProfil
        '
        Me.bAffecterDepuisProfil.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.bAffecterDepuisProfil.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAffecterDepuisProfil.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAffecterDepuisProfil.Location = New System.Drawing.Point(745, 11)
        Me.bAffecterDepuisProfil.Name = "bAffecterDepuisProfil"
        Me.bAffecterDepuisProfil.Size = New System.Drawing.Size(288, 30)
        Me.bAffecterDepuisProfil.TabIndex = 6
        Me.bAffecterDepuisProfil.Text = "Affecter depuis le profil"
        Me.bAffecterDepuisProfil.TextImageRelation = System.Windows.Forms.TextImageRelation.TextAboveImage
        Me.bAffecterDepuisProfil.UseVisualStyleBackColor = True
        Me.bAffecterDepuisProfil.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.BackColor = System.Drawing.Color.Transparent
        Me.Label15.Location = New System.Drawing.Point(13, 21)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(103, 13)
        Me.Label15.TabIndex = 8
        Me.Label15.Text = "Liste des Utilisateurs"
        '
        'lblitemselected
        '
        Me.lblitemselected.AutoSize = True
        Me.lblitemselected.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblitemselected.Location = New System.Drawing.Point(384, 37)
        Me.lblitemselected.Name = "lblitemselected"
        Me.lblitemselected.Size = New System.Drawing.Size(102, 13)
        Me.lblitemselected.TabIndex = 18
        Me.lblitemselected.Text = "(nbr séléctionné)"
        '
        'fAffectationDesAutorisations
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1290, 661)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fAffectationDesAutorisations"
        Me.Text = "fAffectationDesAutorisations"
        Me.Panel.ResumeLayout(False)
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox2.ResumeLayout(False)
        CType(Me.gPointsDeControleAttribue, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.cmbModule, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gPointsDeControleNonAttribue, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.cmbUtilisateursAcopierLesAutorisations, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbProfil, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents bConfirmer As C1.Win.C1Input.C1Button
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents cmbModule As C1.Win.C1List.C1Combo
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents gPointsDeControleNonAttribue As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bSupprimerUn As C1.Win.C1Input.C1Button
    Friend WithEvents bSupprimerTous As C1.Win.C1Input.C1Button
    Friend WithEvents bAjoutertous As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents bAjouterUn As C1.Win.C1Input.C1Button
    Friend WithEvents bAffecterDepuisProfil As C1.Win.C1Input.C1Button
    Friend WithEvents cmbProfil As C1.Win.C1List.C1Combo
    Friend WithEvents cmbUtilisateursAcopierLesAutorisations As C1.Win.C1List.C1Combo
    Friend WithEvents bAffecterDepuisUtilisateur As C1.Win.C1Input.C1Button
    Friend WithEvents gPointsDeControleAttribue As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents lstutilisateurs As System.Windows.Forms.CheckedListBox
    Friend WithEvents lblitemselected As System.Windows.Forms.Label
End Class
