﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fListeDeRecalculDeStock
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fListeDeRecalculDeStock))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bMaintenanceBase = New C1.Win.C1Input.C1Button()
        Me.grbPatientez = New System.Windows.Forms.GroupBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.bRecalculer = New C1.Win.C1Input.C1Button()
        Me.lNombreLigne = New System.Windows.Forms.Label()
        Me.lTitre = New System.Windows.Forms.Label()
        Me.gListe = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.ProgressBar = New System.Windows.Forms.ProgressBar()
        Me.Panel.SuspendLayout()
        Me.grbPatientez.SuspendLayout()
        CType(Me.gListe, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.bMaintenanceBase)
        Me.Panel.Controls.Add(Me.grbPatientez)
        Me.Panel.Controls.Add(Me.bRecalculer)
        Me.Panel.Controls.Add(Me.lNombreLigne)
        Me.Panel.Controls.Add(Me.lTitre)
        Me.Panel.Controls.Add(Me.gListe)
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(909, 486)
        Me.Panel.TabIndex = 9
        '
        'bMaintenanceBase
        '
        Me.bMaintenanceBase.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bMaintenanceBase.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bMaintenanceBase.Image = Global.Pharma2000Premium.My.Resources.Resources.baseSQL1
        Me.bMaintenanceBase.Location = New System.Drawing.Point(714, 432)
        Me.bMaintenanceBase.Name = "bMaintenanceBase"
        Me.bMaintenanceBase.Size = New System.Drawing.Size(188, 45)
        Me.bMaintenanceBase.TabIndex = 35
        Me.bMaintenanceBase.Text = "Maintenance de la base         F11"
        Me.bMaintenanceBase.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bMaintenanceBase.UseVisualStyleBackColor = True
        Me.bMaintenanceBase.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'grbPatientez
        '
        Me.grbPatientez.BackColor = System.Drawing.Color.Transparent
        Me.grbPatientez.Controls.Add(Me.ProgressBar)
        Me.grbPatientez.Controls.Add(Me.Label1)
        Me.grbPatientez.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.grbPatientez.Location = New System.Drawing.Point(269, 229)
        Me.grbPatientez.Name = "grbPatientez"
        Me.grbPatientez.Size = New System.Drawing.Size(441, 100)
        Me.grbPatientez.TabIndex = 34
        Me.grbPatientez.TabStop = False
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(81, 35)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(287, 16)
        Me.Label1.TabIndex = 0
        Me.Label1.Text = "L'opération en cours veuillez patienter ..."
        '
        'bRecalculer
        '
        Me.bRecalculer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bRecalculer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bRecalculer.Image = Global.Pharma2000Premium.My.Resources.Resources.etat5
        Me.bRecalculer.Location = New System.Drawing.Point(652, 4)
        Me.bRecalculer.Name = "bRecalculer"
        Me.bRecalculer.Size = New System.Drawing.Size(124, 45)
        Me.bRecalculer.TabIndex = 33
        Me.bRecalculer.Text = "Recalculer           F10"
        Me.bRecalculer.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bRecalculer.UseVisualStyleBackColor = True
        Me.bRecalculer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lNombreLigne
        '
        Me.lNombreLigne.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNombreLigne.Location = New System.Drawing.Point(3, 7)
        Me.lNombreLigne.Name = "lNombreLigne"
        Me.lNombreLigne.Size = New System.Drawing.Size(73, 40)
        Me.lNombreLigne.TabIndex = 32
        Me.lNombreLigne.Text = "-------------------"
        Me.lNombreLigne.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lTitre
        '
        Me.lTitre.Font = New System.Drawing.Font("HandelGotDLig", 20.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTitre.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.lTitre.Location = New System.Drawing.Point(82, 7)
        Me.lTitre.Name = "lTitre"
        Me.lTitre.Size = New System.Drawing.Size(567, 40)
        Me.lTitre.TabIndex = 31
        Me.lTitre.Text = "RECALCUL DES STOCKS DES ARTICLES"
        Me.lTitre.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'gListe
        '
        Me.gListe.AllowUpdate = False
        Me.gListe.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gListe.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gListe.GroupByCaption = "Drag a column header here to group by that column"
        Me.gListe.Images.Add(CType(resources.GetObject("gListe.Images"), System.Drawing.Image))
        Me.gListe.LinesPerRow = 2
        Me.gListe.Location = New System.Drawing.Point(5, 55)
        Me.gListe.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gListe.Name = "gListe"
        Me.gListe.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gListe.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gListe.PreviewInfo.ZoomFactor = 75.0R
        Me.gListe.PrintInfo.PageSettings = CType(resources.GetObject("gListe.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gListe.Size = New System.Drawing.Size(897, 371)
        Me.gListe.TabIndex = 4
        Me.gListe.Text = "C1TrueDBGrid1"
        Me.gListe.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gListe.PropBag = resources.GetString("gListe.PropBag")
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.Location = New System.Drawing.Point(783, 4)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(119, 45)
        Me.bQuitter.TabIndex = 3
        Me.bQuitter.Text = "Fermer            F12"
        Me.bQuitter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'ProgressBar
        '
        Me.ProgressBar.Location = New System.Drawing.Point(6, 71)
        Me.ProgressBar.Name = "ProgressBar"
        Me.ProgressBar.Size = New System.Drawing.Size(429, 23)
        Me.ProgressBar.TabIndex = 1
        '
        'fListeDeRecalculDeStock
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(909, 486)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fListeDeRecalculDeStock"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        Me.grbPatientez.ResumeLayout(False)
        Me.grbPatientez.PerformLayout()
        CType(Me.gListe, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents lNombreLigne As System.Windows.Forms.Label
    Friend WithEvents lTitre As System.Windows.Forms.Label
    Friend WithEvents gListe As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents bRecalculer As C1.Win.C1Input.C1Button
    Friend WithEvents grbPatientez As System.Windows.Forms.GroupBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents bMaintenanceBase As C1.Win.C1Input.C1Button
    Friend WithEvents ProgressBar As System.Windows.Forms.ProgressBar
End Class
