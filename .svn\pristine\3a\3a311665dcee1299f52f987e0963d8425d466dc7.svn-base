﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="ErrorManagementModel" Alias="Self" p1:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:p1="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
  <EntityContainer Name="ErrorManagementEntities" p1:LazyLoadingEnabled="true">
    <EntitySet Name="MESSAGE_ERROR" EntityType="ErrorManagementModel.MESSAGE_ERROR" />
  </EntityContainer>
  <EntityType Name="MESSAGE_ERROR">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
    <Property Name="Module" Type="String" MaxLength="500" Unicode="false" FixedLength="false" />
    <Property Name="Date" Type="DateTime" Precision="0" />
    <Property Name="LibellePoste" Type="String" MaxLength="500" Unicode="false" FixedLength="false" />
    <Property Name="CodePersonnel" Type="String" MaxLength="500" Unicode="false" FixedLength="false" />
    <Property Name="Message" Type="String" MaxLength="Max" Unicode="false" FixedLength="false" />
  </EntityType>
</Schema>