﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fMesVoisinage
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fMesVoisinage))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.SplitContainer1 = New System.Windows.Forms.SplitContainer()
        Me.SplitContainer2 = New System.Windows.Forms.SplitContainer()
        Me.PictureBox6 = New System.Windows.Forms.PictureBox()
        Me.lblNbrResultat = New System.Windows.Forms.Label()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.PictureBox5 = New System.Windows.Forms.PictureBox()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.Button2 = New System.Windows.Forms.Button()
        Me.Button1 = New System.Windows.Forms.Button()
        Me.PictureBox4 = New System.Windows.Forms.PictureBox()
        Me.PictureBox3 = New System.Windows.Forms.PictureBox()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.rdbHomme = New System.Windows.Forms.RadioButton()
        Me.rdbFemme = New System.Windows.Forms.RadioButton()
        Me.rdbLesDeuxSexe = New System.Windows.Forms.RadioButton()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.cmbVille = New C1.Win.C1List.C1Combo()
        Me.cmbEtablissement = New C1.Win.C1List.C1Combo()
        Me.tNom = New C1.Win.C1Input.C1TextBox()
        Me.cmbSpecialite = New C1.Win.C1List.C1Combo()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.btnRechercher = New System.Windows.Forms.Button()
        Me.gListeOneKey = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.myMap = New GMap.NET.WindowsForms.GMapControl()
        Me.tkbZoom = New System.Windows.Forms.TrackBar()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.lklMapharmacie = New System.Windows.Forms.LinkLabel()
        Me.cmbTypeCarte = New C1.Win.C1List.C1Combo()
        Me.Label34 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.chkMesVoisinages = New System.Windows.Forms.CheckBox()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.updMetres = New System.Windows.Forms.NumericUpDown()
        Me.Panel.SuspendLayout()
        Me.SplitContainer1.Panel1.SuspendLayout()
        Me.SplitContainer1.Panel2.SuspendLayout()
        Me.SplitContainer1.SuspendLayout()
        Me.SplitContainer2.Panel1.SuspendLayout()
        Me.SplitContainer2.Panel2.SuspendLayout()
        Me.SplitContainer2.SuspendLayout()
        CType(Me.PictureBox6, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox3.SuspendLayout()
        CType(Me.PictureBox5, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbVille, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbEtablissement, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNom, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbSpecialite, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gListeOneKey, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel2.SuspendLayout()
        CType(Me.tkbZoom, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        CType(Me.cmbTypeCarte, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.updMetres, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.SplitContainer1)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1284, 742)
        Me.Panel.TabIndex = 0
        '
        'SplitContainer1
        '
        Me.SplitContainer1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.SplitContainer1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.SplitContainer1.Location = New System.Drawing.Point(0, 0)
        Me.SplitContainer1.Name = "SplitContainer1"
        '
        'SplitContainer1.Panel1
        '
        Me.SplitContainer1.Panel1.Controls.Add(Me.SplitContainer2)
        '
        'SplitContainer1.Panel2
        '
        Me.SplitContainer1.Panel2.Controls.Add(Me.Panel2)
        Me.SplitContainer1.Panel2.Controls.Add(Me.Panel1)
        Me.SplitContainer1.Size = New System.Drawing.Size(1284, 742)
        Me.SplitContainer1.SplitterDistance = 344
        Me.SplitContainer1.TabIndex = 8
        '
        'SplitContainer2
        '
        Me.SplitContainer2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.SplitContainer2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.SplitContainer2.FixedPanel = System.Windows.Forms.FixedPanel.Panel1
        Me.SplitContainer2.Location = New System.Drawing.Point(0, 0)
        Me.SplitContainer2.Name = "SplitContainer2"
        Me.SplitContainer2.Orientation = System.Windows.Forms.Orientation.Horizontal
        '
        'SplitContainer2.Panel1
        '
        Me.SplitContainer2.Panel1.Controls.Add(Me.PictureBox6)
        Me.SplitContainer2.Panel1.Controls.Add(Me.lblNbrResultat)
        Me.SplitContainer2.Panel1.Controls.Add(Me.GroupBox3)
        Me.SplitContainer2.Panel1.Controls.Add(Me.GroupBox1)
        Me.SplitContainer2.Panel1.Controls.Add(Me.btnRechercher)
        '
        'SplitContainer2.Panel2
        '
        Me.SplitContainer2.Panel2.Controls.Add(Me.gListeOneKey)
        Me.SplitContainer2.Size = New System.Drawing.Size(344, 742)
        Me.SplitContainer2.SplitterDistance = 285
        Me.SplitContainer2.TabIndex = 1
        '
        'PictureBox6
        '
        Me.PictureBox6.Image = Global.Pharma2000Premium.My.Resources.Resources.statistique
        Me.PictureBox6.Location = New System.Drawing.Point(16, 10)
        Me.PictureBox6.Name = "PictureBox6"
        Me.PictureBox6.Size = New System.Drawing.Size(23, 20)
        Me.PictureBox6.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox6.TabIndex = 86
        Me.PictureBox6.TabStop = False
        '
        'lblNbrResultat
        '
        Me.lblNbrResultat.AutoSize = True
        Me.lblNbrResultat.Location = New System.Drawing.Point(9, 251)
        Me.lblNbrResultat.Name = "lblNbrResultat"
        Me.lblNbrResultat.Size = New System.Drawing.Size(144, 13)
        Me.lblNbrResultat.TabIndex = 84
        Me.lblNbrResultat.Text = "Nombre des résultats trouvés"
        '
        'GroupBox3
        '
        Me.GroupBox3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox3.Controls.Add(Me.PictureBox5)
        Me.GroupBox3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox3.Location = New System.Drawing.Point(10, 10)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(316, 64)
        Me.GroupBox3.TabIndex = 83
        Me.GroupBox3.TabStop = False
        Me.GroupBox3.Text = "        Voisinages"
        '
        'PictureBox5
        '
        Me.PictureBox5.Image = Global.Pharma2000Premium.My.Resources.Resources.onekey
        Me.PictureBox5.Location = New System.Drawing.Point(68, 20)
        Me.PictureBox5.Name = "PictureBox5"
        Me.PictureBox5.Size = New System.Drawing.Size(121, 33)
        Me.PictureBox5.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox5.TabIndex = 85
        Me.PictureBox5.TabStop = False
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.Controls.Add(Me.Button2)
        Me.GroupBox1.Controls.Add(Me.Button1)
        Me.GroupBox1.Controls.Add(Me.PictureBox4)
        Me.GroupBox1.Controls.Add(Me.PictureBox3)
        Me.GroupBox1.Controls.Add(Me.PictureBox2)
        Me.GroupBox1.Controls.Add(Me.Label8)
        Me.GroupBox1.Controls.Add(Me.rdbHomme)
        Me.GroupBox1.Controls.Add(Me.rdbFemme)
        Me.GroupBox1.Controls.Add(Me.rdbLesDeuxSexe)
        Me.GroupBox1.Controls.Add(Me.PictureBox1)
        Me.GroupBox1.Controls.Add(Me.cmbVille)
        Me.GroupBox1.Controls.Add(Me.cmbEtablissement)
        Me.GroupBox1.Controls.Add(Me.tNom)
        Me.GroupBox1.Controls.Add(Me.cmbSpecialite)
        Me.GroupBox1.Controls.Add(Me.Label5)
        Me.GroupBox1.Controls.Add(Me.Label4)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox1.Location = New System.Drawing.Point(10, 77)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(316, 141)
        Me.GroupBox1.TabIndex = 5
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "         Critères de recherche:"
        '
        'Button2
        '
        Me.Button2.Location = New System.Drawing.Point(9, 109)
        Me.Button2.Name = "Button2"
        Me.Button2.Size = New System.Drawing.Size(19, 23)
        Me.Button2.TabIndex = 94
        Me.Button2.Text = "Button2"
        Me.Button2.UseVisualStyleBackColor = True
        Me.Button2.Visible = False
        '
        'Button1
        '
        Me.Button1.Location = New System.Drawing.Point(9, 85)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(19, 23)
        Me.Button1.TabIndex = 93
        Me.Button1.Text = "Button1"
        Me.Button1.UseVisualStyleBackColor = True
        Me.Button1.Visible = False
        '
        'PictureBox4
        '
        Me.PictureBox4.Image = Global.Pharma2000Premium.My.Resources.Resources.hommeR
        Me.PictureBox4.Location = New System.Drawing.Point(240, 114)
        Me.PictureBox4.Name = "PictureBox4"
        Me.PictureBox4.Size = New System.Drawing.Size(12, 21)
        Me.PictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox4.TabIndex = 92
        Me.PictureBox4.TabStop = False
        '
        'PictureBox3
        '
        Me.PictureBox3.Image = Global.Pharma2000Premium.My.Resources.Resources.femmeR
        Me.PictureBox3.Location = New System.Drawing.Point(190, 115)
        Me.PictureBox3.Name = "PictureBox3"
        Me.PictureBox3.Size = New System.Drawing.Size(13, 20)
        Me.PictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox3.TabIndex = 91
        Me.PictureBox3.TabStop = False
        '
        'PictureBox2
        '
        Me.PictureBox2.Image = Global.Pharma2000Premium.My.Resources.Resources.homme_femme
        Me.PictureBox2.Location = New System.Drawing.Point(132, 114)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(23, 20)
        Me.PictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox2.TabIndex = 90
        Me.PictureBox2.TabStop = False
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Location = New System.Drawing.Point(78, 117)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(37, 13)
        Me.Label8.TabIndex = 89
        Me.Label8.Text = "Sexe :"
        '
        'rdbHomme
        '
        Me.rdbHomme.AutoSize = True
        Me.rdbHomme.Location = New System.Drawing.Point(225, 117)
        Me.rdbHomme.Name = "rdbHomme"
        Me.rdbHomme.Size = New System.Drawing.Size(14, 13)
        Me.rdbHomme.TabIndex = 88
        Me.rdbHomme.UseVisualStyleBackColor = True
        '
        'rdbFemme
        '
        Me.rdbFemme.AutoSize = True
        Me.rdbFemme.Location = New System.Drawing.Point(175, 117)
        Me.rdbFemme.Name = "rdbFemme"
        Me.rdbFemme.Size = New System.Drawing.Size(14, 13)
        Me.rdbFemme.TabIndex = 87
        Me.rdbFemme.UseVisualStyleBackColor = True
        '
        'rdbLesDeuxSexe
        '
        Me.rdbLesDeuxSexe.AutoSize = True
        Me.rdbLesDeuxSexe.Checked = True
        Me.rdbLesDeuxSexe.Location = New System.Drawing.Point(117, 117)
        Me.rdbLesDeuxSexe.Name = "rdbLesDeuxSexe"
        Me.rdbLesDeuxSexe.Size = New System.Drawing.Size(14, 13)
        Me.rdbLesDeuxSexe.TabIndex = 86
        Me.rdbLesDeuxSexe.TabStop = True
        Me.rdbLesDeuxSexe.UseVisualStyleBackColor = True
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Pharma2000Premium.My.Resources.Resources.rech11
        Me.PictureBox1.Location = New System.Drawing.Point(5, 0)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(23, 20)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox1.TabIndex = 85
        Me.PictureBox1.TabStop = False
        '
        'cmbVille
        '
        Me.cmbVille.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbVille.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbVille.Caption = ""
        Me.cmbVille.CaptionHeight = 17
        Me.cmbVille.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbVille.ColumnCaptionHeight = 17
        Me.cmbVille.ColumnFooterHeight = 17
        Me.cmbVille.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbVille.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbVille.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbVille.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbVille.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbVille.Images.Add(CType(resources.GetObject("cmbVille.Images"), System.Drawing.Image))
        Me.cmbVille.ItemHeight = 15
        Me.cmbVille.Location = New System.Drawing.Point(122, 67)
        Me.cmbVille.MatchEntryTimeout = CType(2000, Long)
        Me.cmbVille.MaxDropDownItems = CType(5, Short)
        Me.cmbVille.MaxLength = 32767
        Me.cmbVille.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbVille.Name = "cmbVille"
        Me.cmbVille.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbVille.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbVille.Size = New System.Drawing.Size(186, 22)
        Me.cmbVille.TabIndex = 16
        Me.cmbVille.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbVille.PropBag = resources.GetString("cmbVille.PropBag")
        '
        'cmbEtablissement
        '
        Me.cmbEtablissement.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbEtablissement.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbEtablissement.Caption = ""
        Me.cmbEtablissement.CaptionHeight = 17
        Me.cmbEtablissement.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbEtablissement.ColumnCaptionHeight = 17
        Me.cmbEtablissement.ColumnFooterHeight = 17
        Me.cmbEtablissement.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbEtablissement.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbEtablissement.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbEtablissement.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbEtablissement.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbEtablissement.Images.Add(CType(resources.GetObject("cmbEtablissement.Images"), System.Drawing.Image))
        Me.cmbEtablissement.ItemHeight = 15
        Me.cmbEtablissement.Location = New System.Drawing.Point(122, 19)
        Me.cmbEtablissement.MatchEntryTimeout = CType(2000, Long)
        Me.cmbEtablissement.MaxDropDownItems = CType(5, Short)
        Me.cmbEtablissement.MaxLength = 32767
        Me.cmbEtablissement.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbEtablissement.Name = "cmbEtablissement"
        Me.cmbEtablissement.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbEtablissement.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbEtablissement.Size = New System.Drawing.Size(186, 22)
        Me.cmbEtablissement.TabIndex = 15
        Me.cmbEtablissement.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbEtablissement.PropBag = resources.GetString("cmbEtablissement.PropBag")
        '
        'tNom
        '
        Me.tNom.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tNom.AutoSize = False
        Me.tNom.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNom.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNom.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNom.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNom.Location = New System.Drawing.Point(122, 90)
        Me.tNom.Name = "tNom"
        Me.tNom.Size = New System.Drawing.Size(186, 19)
        Me.tNom.TabIndex = 14
        Me.tNom.Tag = Nothing
        Me.tNom.Value = ""
        Me.tNom.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNom.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbSpecialite
        '
        Me.cmbSpecialite.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbSpecialite.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbSpecialite.Caption = ""
        Me.cmbSpecialite.CaptionHeight = 17
        Me.cmbSpecialite.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbSpecialite.ColumnCaptionHeight = 17
        Me.cmbSpecialite.ColumnFooterHeight = 17
        Me.cmbSpecialite.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbSpecialite.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbSpecialite.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbSpecialite.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbSpecialite.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbSpecialite.Images.Add(CType(resources.GetObject("cmbSpecialite.Images"), System.Drawing.Image))
        Me.cmbSpecialite.ItemHeight = 15
        Me.cmbSpecialite.Location = New System.Drawing.Point(122, 43)
        Me.cmbSpecialite.MatchEntryTimeout = CType(2000, Long)
        Me.cmbSpecialite.MaxDropDownItems = CType(5, Short)
        Me.cmbSpecialite.MaxLength = 32767
        Me.cmbSpecialite.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbSpecialite.Name = "cmbSpecialite"
        Me.cmbSpecialite.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbSpecialite.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbSpecialite.Size = New System.Drawing.Size(186, 22)
        Me.cmbSpecialite.TabIndex = 13
        Me.cmbSpecialite.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbSpecialite.PropBag = resources.GetString("cmbSpecialite.PropBag")
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Location = New System.Drawing.Point(80, 94)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(35, 13)
        Me.Label5.TabIndex = 11
        Me.Label5.Text = "Nom :"
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Location = New System.Drawing.Point(82, 71)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(32, 13)
        Me.Label4.TabIndex = 10
        Me.Label4.Text = "Ville :"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(2, 24)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(111, 13)
        Me.Label2.TabIndex = 6
        Me.Label2.Text = "* Type établissement :"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(48, 48)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(66, 13)
        Me.Label1.TabIndex = 0
        Me.Label1.Text = "* Spécialité :"
        '
        'btnRechercher
        '
        Me.btnRechercher.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnRechercher.Location = New System.Drawing.Point(185, 245)
        Me.btnRechercher.Name = "btnRechercher"
        Me.btnRechercher.Size = New System.Drawing.Size(141, 23)
        Me.btnRechercher.TabIndex = 3
        Me.btnRechercher.Text = "Rechercher"
        Me.btnRechercher.UseVisualStyleBackColor = True
        '
        'gListeOneKey
        '
        Me.gListeOneKey.AllowColMove = False
        Me.gListeOneKey.AllowColSelect = False
        Me.gListeOneKey.AllowSort = False
        Me.gListeOneKey.AllowUpdate = False
        Me.gListeOneKey.AllowUpdateOnBlur = False
        Me.gListeOneKey.AllowVerticalSplit = True
        Me.gListeOneKey.AlternatingRows = True
        Me.gListeOneKey.Dock = System.Windows.Forms.DockStyle.Fill
        Me.gListeOneKey.EmptyRows = True
        Me.gListeOneKey.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gListeOneKey.GroupByCaption = "Drag a column header here to group by that column"
        Me.gListeOneKey.Images.Add(CType(resources.GetObject("gListeOneKey.Images"), System.Drawing.Image))
        Me.gListeOneKey.LinesPerRow = 2
        Me.gListeOneKey.Location = New System.Drawing.Point(0, 0)
        Me.gListeOneKey.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gListeOneKey.MultiSelect = C1.Win.C1TrueDBGrid.MultiSelectEnum.Simple
        Me.gListeOneKey.Name = "gListeOneKey"
        Me.gListeOneKey.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gListeOneKey.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gListeOneKey.PreviewInfo.ZoomFactor = 75.0R
        Me.gListeOneKey.PrintInfo.PageSettings = CType(resources.GetObject("gListeOneKey.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gListeOneKey.RecordSelectors = False
        Me.gListeOneKey.Size = New System.Drawing.Size(340, 449)
        Me.gListeOneKey.TabIndex = 4
        Me.gListeOneKey.Text = "C1TrueDBGrid1"
        Me.gListeOneKey.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2010Blue
        Me.gListeOneKey.PropBag = resources.GetString("gListeOneKey.PropBag")
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.myMap)
        Me.Panel2.Controls.Add(Me.tkbZoom)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel2.Location = New System.Drawing.Point(0, 31)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(932, 707)
        Me.Panel2.TabIndex = 8
        '
        'myMap
        '
        Me.myMap.Bearing = 0.0!
        Me.myMap.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.myMap.CanDragMap = True
        Me.myMap.Cursor = System.Windows.Forms.Cursors.Cross
        Me.myMap.Dock = System.Windows.Forms.DockStyle.Fill
        Me.myMap.GrayScaleMode = True
        Me.myMap.LevelsKeepInMemmory = 5
        Me.myMap.Location = New System.Drawing.Point(45, 0)
        Me.myMap.MarkersEnabled = True
        Me.myMap.MaxZoom = 2
        Me.myMap.MinZoom = 2
        Me.myMap.MouseWheelZoomType = GMap.NET.MouseWheelZoomType.MousePositionAndCenter
        Me.myMap.Name = "myMap"
        Me.myMap.NegativeMode = False
        Me.myMap.PolygonsEnabled = True
        Me.myMap.RetryLoadTile = 0
        Me.myMap.RoutesEnabled = True
        Me.myMap.ShowTileGridLines = False
        Me.myMap.Size = New System.Drawing.Size(887, 707)
        Me.myMap.TabIndex = 5
        Me.myMap.Zoom = 0.0R
        '
        'tkbZoom
        '
        Me.tkbZoom.Dock = System.Windows.Forms.DockStyle.Left
        Me.tkbZoom.Location = New System.Drawing.Point(0, 0)
        Me.tkbZoom.Maximum = 20
        Me.tkbZoom.Name = "tkbZoom"
        Me.tkbZoom.Orientation = System.Windows.Forms.Orientation.Vertical
        Me.tkbZoom.Size = New System.Drawing.Size(45, 707)
        Me.tkbZoom.TabIndex = 6
        Me.tkbZoom.Value = 17
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.Label10)
        Me.Panel1.Controls.Add(Me.lklMapharmacie)
        Me.Panel1.Controls.Add(Me.cmbTypeCarte)
        Me.Panel1.Controls.Add(Me.Label34)
        Me.Panel1.Controls.Add(Me.Label7)
        Me.Panel1.Controls.Add(Me.chkMesVoisinages)
        Me.Panel1.Controls.Add(Me.Label6)
        Me.Panel1.Controls.Add(Me.Label3)
        Me.Panel1.Controls.Add(Me.updMetres)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(932, 31)
        Me.Panel1.TabIndex = 7
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Location = New System.Drawing.Point(643, 8)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(9, 13)
        Me.Label10.TabIndex = 71
        Me.Label10.Text = "|"
        '
        'lklMapharmacie
        '
        Me.lklMapharmacie.AutoSize = True
        Me.lklMapharmacie.Location = New System.Drawing.Point(659, 9)
        Me.lklMapharmacie.Name = "lklMapharmacie"
        Me.lklMapharmacie.Size = New System.Drawing.Size(75, 13)
        Me.lklMapharmacie.TabIndex = 7
        Me.lklMapharmacie.TabStop = True
        Me.lklMapharmacie.Text = "Ma Pharmacie"
        '
        'cmbTypeCarte
        '
        Me.cmbTypeCarte.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbTypeCarte.Caption = ""
        Me.cmbTypeCarte.CaptionHeight = 17
        Me.cmbTypeCarte.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbTypeCarte.ColumnCaptionHeight = 17
        Me.cmbTypeCarte.ColumnFooterHeight = 17
        Me.cmbTypeCarte.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbTypeCarte.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbTypeCarte.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbTypeCarte.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbTypeCarte.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbTypeCarte.Images.Add(CType(resources.GetObject("cmbTypeCarte.Images"), System.Drawing.Image))
        Me.cmbTypeCarte.ItemHeight = 15
        Me.cmbTypeCarte.Location = New System.Drawing.Point(452, 4)
        Me.cmbTypeCarte.MatchEntryTimeout = CType(2000, Long)
        Me.cmbTypeCarte.MaxDropDownItems = CType(5, Short)
        Me.cmbTypeCarte.MaxLength = 32767
        Me.cmbTypeCarte.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbTypeCarte.Name = "cmbTypeCarte"
        Me.cmbTypeCarte.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbTypeCarte.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbTypeCarte.Size = New System.Drawing.Size(184, 22)
        Me.cmbTypeCarte.TabIndex = 70
        Me.cmbTypeCarte.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbTypeCarte.PropBag = resources.GetString("cmbTypeCarte.PropBag")
        '
        'Label34
        '
        Me.Label34.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label34.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label34.Location = New System.Drawing.Point(325, 4)
        Me.Label34.Name = "Label34"
        Me.Label34.Size = New System.Drawing.Size(121, 19)
        Me.Label34.TabIndex = 69
        Me.Label34.Text = "Type de la carte MAP :"
        Me.Label34.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Location = New System.Drawing.Point(314, 7)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(9, 13)
        Me.Label7.TabIndex = 4
        Me.Label7.Text = "|"
        '
        'chkMesVoisinages
        '
        Me.chkMesVoisinages.AutoSize = True
        Me.chkMesVoisinages.Location = New System.Drawing.Point(21, 8)
        Me.chkMesVoisinages.Name = "chkMesVoisinages"
        Me.chkMesVoisinages.Size = New System.Drawing.Size(15, 14)
        Me.chkMesVoisinages.TabIndex = 0
        Me.chkMesVoisinages.UseVisualStyleBackColor = True
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Location = New System.Drawing.Point(42, 7)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(56, 13)
        Me.Label6.TabIndex = 3
        Me.Label6.Text = "Rayon de "
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Location = New System.Drawing.Point(150, 8)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(155, 13)
        Me.Label3.TabIndex = 2
        Me.Label3.Text = "mètres autour de ma pharmacie"
        '
        'updMetres
        '
        Me.updMetres.Location = New System.Drawing.Point(98, 5)
        Me.updMetres.Maximum = New Decimal(New Integer() {2000, 0, 0, 0})
        Me.updMetres.Name = "updMetres"
        Me.updMetres.Size = New System.Drawing.Size(50, 20)
        Me.updMetres.TabIndex = 1
        Me.updMetres.Value = New Decimal(New Integer() {100, 0, 0, 0})
        '
        'fMesVoisinage
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1284, 742)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fMesVoisinage"
        Me.Text = "OneKey"
        Me.Panel.ResumeLayout(False)
        Me.SplitContainer1.Panel1.ResumeLayout(False)
        Me.SplitContainer1.Panel2.ResumeLayout(False)
        Me.SplitContainer1.ResumeLayout(False)
        Me.SplitContainer2.Panel1.ResumeLayout(False)
        Me.SplitContainer2.Panel1.PerformLayout()
        Me.SplitContainer2.Panel2.ResumeLayout(False)
        Me.SplitContainer2.ResumeLayout(False)
        CType(Me.PictureBox6, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox3.ResumeLayout(False)
        CType(Me.PictureBox5, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbVille, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbEtablissement, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNom, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbSpecialite, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gListeOneKey, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel2.ResumeLayout(False)
        Me.Panel2.PerformLayout()
        CType(Me.tkbZoom, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        CType(Me.cmbTypeCarte, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.updMetres, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents SplitContainer1 As System.Windows.Forms.SplitContainer
    Friend WithEvents SplitContainer2 As System.Windows.Forms.SplitContainer
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents updMetres As System.Windows.Forms.NumericUpDown
    Friend WithEvents chkMesVoisinages As System.Windows.Forms.CheckBox
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents btnRechercher As System.Windows.Forms.Button
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents myMap As GMap.NET.WindowsForms.GMapControl
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents cmbSpecialite As C1.Win.C1List.C1Combo
    Friend WithEvents cmbVille As C1.Win.C1List.C1Combo
    Friend WithEvents cmbEtablissement As C1.Win.C1List.C1Combo
    Friend WithEvents tNom As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents rdbHomme As System.Windows.Forms.RadioButton
    Friend WithEvents rdbFemme As System.Windows.Forms.RadioButton
    Friend WithEvents rdbLesDeuxSexe As System.Windows.Forms.RadioButton
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents PictureBox2 As System.Windows.Forms.PictureBox
    Friend WithEvents PictureBox4 As System.Windows.Forms.PictureBox
    Friend WithEvents PictureBox3 As System.Windows.Forms.PictureBox
    Friend WithEvents lblNbrResultat As System.Windows.Forms.Label
    Friend WithEvents tkbZoom As System.Windows.Forms.TrackBar
    Friend WithEvents PictureBox5 As System.Windows.Forms.PictureBox
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents cmbTypeCarte As C1.Win.C1List.C1Combo
    Friend WithEvents Label34 As System.Windows.Forms.Label
    Friend WithEvents lklMapharmacie As System.Windows.Forms.LinkLabel
    Friend WithEvents Button1 As System.Windows.Forms.Button
    Friend WithEvents Button2 As System.Windows.Forms.Button
    Friend WithEvents PictureBox6 As System.Windows.Forms.PictureBox
    Friend WithEvents gListeOneKey As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Label10 As System.Windows.Forms.Label
End Class
