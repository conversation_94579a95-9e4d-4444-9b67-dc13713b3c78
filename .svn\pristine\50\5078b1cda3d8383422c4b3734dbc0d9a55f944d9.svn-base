﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fClient
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fClient))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.chbVoirClientsSupprimes = New System.Windows.Forms.CheckBox()
        Me.lNbreDesClients = New System.Windows.Forms.Label()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.lResteAPayer = New System.Windows.Forms.Label()
        Me.lTotalEnCours = New System.Windows.Forms.Label()
        Me.lTotalSolde = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.bImprimer = New C1.Win.C1Input.C1Button()
        Me.bSuprimerClient = New C1.Win.C1Input.C1Button()
        Me.bModifierClient = New C1.Win.C1Input.C1Button()
        Me.bAjouterClient = New C1.Win.C1Input.C1Button()
        Me.gClient = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.chbCodeExact = New System.Windows.Forms.CheckBox()
        Me.tMatriculeMutuelle = New C1.Win.C1Input.C1TextBox()
        Me.tIdCnam = New C1.Win.C1Input.C1TextBox()
        Me.tNomRecherche = New C1.Win.C1Input.C1TextBox()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.tCode = New C1.Win.C1Input.C1TextBox()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.cmbNomClient = New C1.Win.C1List.C1Combo()
        Me.lNomClient = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.tSoldeMin = New C1.Win.C1Input.C1TextBox()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.chbNonCNAMISTE = New System.Windows.Forms.CheckBox()
        Me.chbNonMutualiste = New System.Windows.Forms.CheckBox()
        Me.chbMutualiste = New System.Windows.Forms.CheckBox()
        Me.chbCrediteur = New System.Windows.Forms.CheckBox()
        Me.chbCNAMISTE = New System.Windows.Forms.CheckBox()
        Me.cmbMutuelle = New C1.Win.C1List.C1Combo()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.dtpDernierReglement = New C1.Win.C1Input.C1DateEdit()
        Me.dtpDernierAchat = New C1.Win.C1Input.C1DateEdit()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.chbSolde = New System.Windows.Forms.CheckBox()
        Me.bViderLesChamps = New C1.Win.C1Input.C1Button()
        Me.bRechercher = New C1.Win.C1Input.C1Button()
        Me.cmbVille = New C1.Win.C1List.C1Combo()
        Me.lVilleClient = New System.Windows.Forms.Label()
        Me.cmbSituation = New C1.Win.C1List.C1Combo()
        Me.lSituationClient = New System.Windows.Forms.Label()
        Me.CR = New Pharma2000Premium.EtatDesClients()
        Me.dtpDernierAchatFin = New C1.Win.C1Input.C1DateEdit()
        Me.dtpDernierReglementFin = New C1.Win.C1Input.C1DateEdit()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.Label17 = New System.Windows.Forms.Label()
        Me.tSoldeMax = New C1.Win.C1Input.C1TextBox()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.Panel.SuspendLayout()
        Me.GroupBox4.SuspendLayout()
        CType(Me.gClient, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.tMatriculeMutuelle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tIdCnam, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNomRecherche, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCode, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbNomClient, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tSoldeMin, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox3.SuspendLayout()
        CType(Me.cmbMutuelle, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox2.SuspendLayout()
        CType(Me.dtpDernierReglement, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpDernierAchat, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbVille, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbSituation, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpDernierAchatFin, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpDernierReglementFin, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tSoldeMax, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.Label11)
        Me.Panel.Controls.Add(Me.Label12)
        Me.Panel.Controls.Add(Me.Label13)
        Me.Panel.Controls.Add(Me.Label14)
        Me.Panel.Controls.Add(Me.GroupBox4)
        Me.Panel.Controls.Add(Me.chbVoirClientsSupprimes)
        Me.Panel.Controls.Add(Me.lNbreDesClients)
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.lResteAPayer)
        Me.Panel.Controls.Add(Me.lTotalEnCours)
        Me.Panel.Controls.Add(Me.lTotalSolde)
        Me.Panel.Controls.Add(Me.Label6)
        Me.Panel.Controls.Add(Me.Label5)
        Me.Panel.Controls.Add(Me.Label2)
        Me.Panel.Controls.Add(Me.bImprimer)
        Me.Panel.Controls.Add(Me.bSuprimerClient)
        Me.Panel.Controls.Add(Me.bModifierClient)
        Me.Panel.Controls.Add(Me.bAjouterClient)
        Me.Panel.Controls.Add(Me.gClient)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1273, 631)
        Me.Panel.TabIndex = 0
        '
        'Label11
        '
        Me.Label11.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label11.AutoSize = True
        Me.Label11.Location = New System.Drawing.Point(677, 590)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(46, 13)
        Me.Label11.TabIndex = 92
        Me.Label11.Text = "Solde>0"
        '
        'Label12
        '
        Me.Label12.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label12.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(250, Byte), Integer), CType(CType(200, Byte), Integer))
        Me.Label12.Location = New System.Drawing.Point(632, 590)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(39, 13)
        Me.Label12.TabIndex = 93
        '
        'Label13
        '
        Me.Label13.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label13.AutoSize = True
        Me.Label13.Location = New System.Drawing.Point(677, 574)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(46, 13)
        Me.Label13.TabIndex = 90
        Me.Label13.Text = "Solde<0"
        '
        'Label14
        '
        Me.Label14.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label14.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(220, Byte), Integer), CType(CType(220, Byte), Integer))
        Me.Label14.Location = New System.Drawing.Point(632, 574)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(39, 13)
        Me.Label14.TabIndex = 91
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.Label9)
        Me.GroupBox4.Location = New System.Drawing.Point(12, 12)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(146, 143)
        Me.GroupBox4.TabIndex = 89
        Me.GroupBox4.TabStop = False
        Me.GroupBox4.Text = "Client"
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Cursor = System.Windows.Forms.Cursors.Default
        Me.Label9.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Label9.Font = New System.Drawing.Font("Microsoft Sans Serif", 20.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label9.Location = New System.Drawing.Point(4, 17)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(137, 31)
        Me.Label9.TabIndex = 10
        Me.Label9.Text = "CLIENTS"
        Me.Label9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'chbVoirClientsSupprimes
        '
        Me.chbVoirClientsSupprimes.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.chbVoirClientsSupprimes.Location = New System.Drawing.Point(1058, 554)
        Me.chbVoirClientsSupprimes.Name = "chbVoirClientsSupprimes"
        Me.chbVoirClientsSupprimes.Size = New System.Drawing.Size(201, 24)
        Me.chbVoirClientsSupprimes.TabIndex = 88
        Me.chbVoirClientsSupprimes.Text = "Voir les clients supprimés (Historique)"
        Me.chbVoirClientsSupprimes.UseVisualStyleBackColor = True
        Me.chbVoirClientsSupprimes.Visible = False
        '
        'lNbreDesClients
        '
        Me.lNbreDesClients.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lNbreDesClients.AutoSize = True
        Me.lNbreDesClients.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNbreDesClients.Location = New System.Drawing.Point(12, 565)
        Me.lNbreDesClients.Name = "lNbreDesClients"
        Me.lNbreDesClients.Size = New System.Drawing.Size(11, 15)
        Me.lNbreDesClients.TabIndex = 25
        Me.lNbreDesClients.Text = "-"
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(1155, 579)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(100, 45)
        Me.bQuitter.TabIndex = 24
        Me.bQuitter.Text = "Fermer             F12   "
        Me.bQuitter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lResteAPayer
        '
        Me.lResteAPayer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lResteAPayer.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lResteAPayer.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lResteAPayer.ForeColor = System.Drawing.Color.Green
        Me.lResteAPayer.Location = New System.Drawing.Point(494, 600)
        Me.lResteAPayer.Name = "lResteAPayer"
        Me.lResteAPayer.Size = New System.Drawing.Size(110, 22)
        Me.lResteAPayer.TabIndex = 20
        Me.lResteAPayer.Text = "Reste à payer"
        Me.lResteAPayer.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lTotalEnCours
        '
        Me.lTotalEnCours.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lTotalEnCours.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lTotalEnCours.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotalEnCours.ForeColor = System.Drawing.Color.Green
        Me.lTotalEnCours.Location = New System.Drawing.Point(494, 574)
        Me.lTotalEnCours.Name = "lTotalEnCours"
        Me.lTotalEnCours.Size = New System.Drawing.Size(110, 22)
        Me.lTotalEnCours.TabIndex = 19
        Me.lTotalEnCours.Text = "Total Encours"
        Me.lTotalEnCours.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lTotalSolde
        '
        Me.lTotalSolde.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lTotalSolde.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lTotalSolde.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotalSolde.ForeColor = System.Drawing.Color.Green
        Me.lTotalSolde.Location = New System.Drawing.Point(255, 574)
        Me.lTotalSolde.Name = "lTotalSolde"
        Me.lTotalSolde.Size = New System.Drawing.Size(110, 22)
        Me.lTotalSolde.TabIndex = 18
        Me.lTotalSolde.Text = "Total Solde"
        Me.lTotalSolde.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.Location = New System.Drawing.Point(415, 605)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(73, 13)
        Me.Label6.TabIndex = 17
        Me.Label6.Text = "Reste à payer"
        '
        'Label5
        '
        Me.Label5.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.Location = New System.Drawing.Point(415, 578)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(73, 13)
        Me.Label5.TabIndex = 16
        Me.Label5.Text = "Total Encours"
        '
        'Label2
        '
        Me.Label2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(188, 578)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(61, 13)
        Me.Label2.TabIndex = 15
        Me.Label2.Text = "Total Solde"
        '
        'bImprimer
        '
        Me.bImprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.imprmante1
        Me.bImprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bImprimer.Location = New System.Drawing.Point(947, 579)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(100, 45)
        Me.bImprimer.TabIndex = 14
        Me.bImprimer.Text = "Imprimer                F11"
        Me.bImprimer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bSuprimerClient
        '
        Me.bSuprimerClient.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bSuprimerClient.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSuprimerClient.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler2
        Me.bSuprimerClient.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSuprimerClient.Location = New System.Drawing.Point(738, 579)
        Me.bSuprimerClient.Name = "bSuprimerClient"
        Me.bSuprimerClient.Size = New System.Drawing.Size(100, 45)
        Me.bSuprimerClient.TabIndex = 7
        Me.bSuprimerClient.Text = "Suprimer                 F7"
        Me.bSuprimerClient.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSuprimerClient.UseVisualStyleBackColor = True
        Me.bSuprimerClient.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bModifierClient
        '
        Me.bModifierClient.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bModifierClient.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bModifierClient.Image = Global.Pharma2000Premium.My.Resources.Resources.voir_modifierfpharmacien
        Me.bModifierClient.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bModifierClient.Location = New System.Drawing.Point(843, 579)
        Me.bModifierClient.Name = "bModifierClient"
        Me.bModifierClient.Size = New System.Drawing.Size(100, 45)
        Me.bModifierClient.TabIndex = 6
        Me.bModifierClient.Text = "Modifier                F8"
        Me.bModifierClient.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bModifierClient.UseVisualStyleBackColor = True
        Me.bModifierClient.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouterClient
        '
        Me.bAjouterClient.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouterClient.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouterClient.Image = Global.Pharma2000Premium.My.Resources.Resources.nouveau
        Me.bAjouterClient.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouterClient.Location = New System.Drawing.Point(1051, 579)
        Me.bAjouterClient.Name = "bAjouterClient"
        Me.bAjouterClient.Size = New System.Drawing.Size(100, 45)
        Me.bAjouterClient.TabIndex = 5
        Me.bAjouterClient.Text = "Ajouter                 F5"
        Me.bAjouterClient.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouterClient.UseVisualStyleBackColor = True
        Me.bAjouterClient.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gClient
        '
        Me.gClient.AllowUpdate = False
        Me.gClient.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gClient.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.gClient.GroupByCaption = "Drag a column header here to group by that column"
        Me.gClient.Images.Add(CType(resources.GetObject("gClient.Images"), System.Drawing.Image))
        Me.gClient.Location = New System.Drawing.Point(12, 161)
        Me.gClient.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gClient.Name = "gClient"
        Me.gClient.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gClient.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gClient.PreviewInfo.ZoomFactor = 75.0R
        Me.gClient.PrintInfo.PageSettings = CType(resources.GetObject("gClient.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gClient.Size = New System.Drawing.Size(1244, 391)
        Me.gClient.TabIndex = 1
        Me.gClient.Text = "C1TrueDBGrid1"
        Me.gClient.PropBag = resources.GetString("gClient.PropBag")
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.Controls.Add(Me.Label18)
        Me.GroupBox1.Controls.Add(Me.tSoldeMax)
        Me.GroupBox1.Controls.Add(Me.chbCodeExact)
        Me.GroupBox1.Controls.Add(Me.tMatriculeMutuelle)
        Me.GroupBox1.Controls.Add(Me.tIdCnam)
        Me.GroupBox1.Controls.Add(Me.tNomRecherche)
        Me.GroupBox1.Controls.Add(Me.Label15)
        Me.GroupBox1.Controls.Add(Me.tCode)
        Me.GroupBox1.Controls.Add(Me.Label10)
        Me.GroupBox1.Controls.Add(Me.cmbNomClient)
        Me.GroupBox1.Controls.Add(Me.lNomClient)
        Me.GroupBox1.Controls.Add(Me.Label8)
        Me.GroupBox1.Controls.Add(Me.Label7)
        Me.GroupBox1.Controls.Add(Me.tSoldeMin)
        Me.GroupBox1.Controls.Add(Me.GroupBox3)
        Me.GroupBox1.Controls.Add(Me.cmbMutuelle)
        Me.GroupBox1.Controls.Add(Me.GroupBox2)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Controls.Add(Me.chbSolde)
        Me.GroupBox1.Controls.Add(Me.bViderLesChamps)
        Me.GroupBox1.Controls.Add(Me.bRechercher)
        Me.GroupBox1.Controls.Add(Me.cmbVille)
        Me.GroupBox1.Controls.Add(Me.lVilleClient)
        Me.GroupBox1.Controls.Add(Me.cmbSituation)
        Me.GroupBox1.Controls.Add(Me.lSituationClient)
        Me.GroupBox1.Location = New System.Drawing.Point(163, 12)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(1091, 143)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Critères de recherche"
        '
        'chbCodeExact
        '
        Me.chbCodeExact.AutoSize = True
        Me.chbCodeExact.Location = New System.Drawing.Point(123, 23)
        Me.chbCodeExact.Name = "chbCodeExact"
        Me.chbCodeExact.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chbCodeExact.Size = New System.Drawing.Size(80, 17)
        Me.chbCodeExact.TabIndex = 60
        Me.chbCodeExact.Text = "Code exact"
        Me.chbCodeExact.UseVisualStyleBackColor = True
        '
        'tMatriculeMutuelle
        '
        Me.tMatriculeMutuelle.AutoSize = False
        Me.tMatriculeMutuelle.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tMatriculeMutuelle.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMatriculeMutuelle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tMatriculeMutuelle.Location = New System.Drawing.Point(440, 99)
        Me.tMatriculeMutuelle.Name = "tMatriculeMutuelle"
        Me.tMatriculeMutuelle.Size = New System.Drawing.Size(94, 22)
        Me.tMatriculeMutuelle.TabIndex = 59
        Me.tMatriculeMutuelle.Tag = Nothing
        Me.tMatriculeMutuelle.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tMatriculeMutuelle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tIdCnam
        '
        Me.tIdCnam.AutoSize = False
        Me.tIdCnam.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tIdCnam.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tIdCnam.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tIdCnam.Location = New System.Drawing.Point(297, 73)
        Me.tIdCnam.Name = "tIdCnam"
        Me.tIdCnam.Size = New System.Drawing.Size(238, 22)
        Me.tIdCnam.TabIndex = 58
        Me.tIdCnam.Tag = Nothing
        Me.tIdCnam.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tIdCnam.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tNomRecherche
        '
        Me.tNomRecherche.AutoSize = False
        Me.tNomRecherche.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNomRecherche.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNomRecherche.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNomRecherche.Location = New System.Drawing.Point(43, 47)
        Me.tNomRecherche.Name = "tNomRecherche"
        Me.tNomRecherche.Size = New System.Drawing.Size(158, 22)
        Me.tNomRecherche.TabIndex = 56
        Me.tNomRecherche.Tag = Nothing
        Me.tNomRecherche.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNomRecherche.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label15.Location = New System.Drawing.Point(6, 49)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(29, 13)
        Me.Label15.TabIndex = 57
        Me.Label15.Text = "Nom"
        '
        'tCode
        '
        Me.tCode.AutoSize = False
        Me.tCode.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCode.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCode.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCode.Location = New System.Drawing.Point(43, 21)
        Me.tCode.Name = "tCode"
        Me.tCode.Size = New System.Drawing.Size(76, 22)
        Me.tCode.TabIndex = 54
        Me.tCode.Tag = Nothing
        Me.tCode.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCode.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label10.Location = New System.Drawing.Point(6, 25)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(32, 13)
        Me.Label10.TabIndex = 55
        Me.Label10.Text = "Code"
        '
        'cmbNomClient
        '
        Me.cmbNomClient.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbNomClient.Caption = ""
        Me.cmbNomClient.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbNomClient.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbNomClient.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbNomClient.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbNomClient.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbNomClient.Images.Add(CType(resources.GetObject("cmbNomClient.Images"), System.Drawing.Image))
        Me.cmbNomClient.Location = New System.Drawing.Point(297, 46)
        Me.cmbNomClient.MatchEntryTimeout = CType(2000, Long)
        Me.cmbNomClient.MaxDropDownItems = CType(5, Short)
        Me.cmbNomClient.MaxLength = 32767
        Me.cmbNomClient.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbNomClient.Name = "cmbNomClient"
        Me.cmbNomClient.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbNomClient.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbNomClient.Size = New System.Drawing.Size(238, 21)
        Me.cmbNomClient.TabIndex = 3
        Me.cmbNomClient.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbNomClient.PropBag = resources.GetString("cmbNomClient.PropBag")
        '
        'lNomClient
        '
        Me.lNomClient.AutoSize = True
        Me.lNomClient.Location = New System.Drawing.Point(210, 52)
        Me.lNomClient.Name = "lNomClient"
        Me.lNomClient.Size = New System.Drawing.Size(82, 13)
        Me.lNomClient.TabIndex = 2
        Me.lNomClient.Text = "Nom (Selection)"
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Location = New System.Drawing.Point(242, 78)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(50, 13)
        Me.Label8.TabIndex = 52
        Me.Label8.Text = "Id CNAM"
        '
        'Label7
        '
        Me.Label7.Location = New System.Drawing.Point(31, 73)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(170, 18)
        Me.Label7.TabIndex = 51
        Me.Label7.Text = "Client ayant un solde entre :"
        '
        'tSoldeMin
        '
        Me.tSoldeMin.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tSoldeMin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tSoldeMin.DataType = GetType(Decimal)
        Me.tSoldeMin.Location = New System.Drawing.Point(43, 94)
        Me.tSoldeMin.Name = "tSoldeMin"
        Me.tSoldeMin.Size = New System.Drawing.Size(59, 18)
        Me.tSoldeMin.TabIndex = 50
        Me.tSoldeMin.Tag = Nothing
        Me.tSoldeMin.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tSoldeMin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.chbNonCNAMISTE)
        Me.GroupBox3.Controls.Add(Me.chbNonMutualiste)
        Me.GroupBox3.Controls.Add(Me.chbMutualiste)
        Me.GroupBox3.Controls.Add(Me.chbCrediteur)
        Me.GroupBox3.Controls.Add(Me.chbCNAMISTE)
        Me.GroupBox3.Location = New System.Drawing.Point(806, 8)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(150, 129)
        Me.GroupBox3.TabIndex = 9
        Me.GroupBox3.TabStop = False
        '
        'chbNonCNAMISTE
        '
        Me.chbNonCNAMISTE.AutoSize = True
        Me.chbNonCNAMISTE.Location = New System.Drawing.Point(27, 78)
        Me.chbNonCNAMISTE.Name = "chbNonCNAMISTE"
        Me.chbNonCNAMISTE.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chbNonCNAMISTE.Size = New System.Drawing.Size(107, 17)
        Me.chbNonCNAMISTE.TabIndex = 59
        Me.chbNonCNAMISTE.Text = "Client non CNAM"
        Me.chbNonCNAMISTE.UseVisualStyleBackColor = True
        '
        'chbNonMutualiste
        '
        Me.chbNonMutualiste.AutoSize = True
        Me.chbNonMutualiste.Location = New System.Drawing.Point(11, 101)
        Me.chbNonMutualiste.Name = "chbNonMutualiste"
        Me.chbNonMutualiste.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chbNonMutualiste.Size = New System.Drawing.Size(123, 17)
        Me.chbNonMutualiste.TabIndex = 58
        Me.chbNonMutualiste.Text = "Client non mutualiste"
        Me.chbNonMutualiste.UseVisualStyleBackColor = True
        '
        'chbMutualiste
        '
        Me.chbMutualiste.AutoSize = True
        Me.chbMutualiste.Location = New System.Drawing.Point(32, 56)
        Me.chbMutualiste.Name = "chbMutualiste"
        Me.chbMutualiste.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chbMutualiste.Size = New System.Drawing.Size(102, 17)
        Me.chbMutualiste.TabIndex = 57
        Me.chbMutualiste.Text = "Client mutualiste"
        Me.chbMutualiste.UseVisualStyleBackColor = True
        '
        'chbCrediteur
        '
        Me.chbCrediteur.AutoSize = True
        Me.chbCrediteur.Location = New System.Drawing.Point(38, 13)
        Me.chbCrediteur.Name = "chbCrediteur"
        Me.chbCrediteur.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chbCrediteur.Size = New System.Drawing.Size(96, 17)
        Me.chbCrediteur.TabIndex = 54
        Me.chbCrediteur.Text = "Client créditeur"
        Me.chbCrediteur.UseVisualStyleBackColor = True
        '
        'chbCNAMISTE
        '
        Me.chbCNAMISTE.AutoSize = True
        Me.chbCNAMISTE.Location = New System.Drawing.Point(48, 36)
        Me.chbCNAMISTE.Name = "chbCNAMISTE"
        Me.chbCNAMISTE.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chbCNAMISTE.Size = New System.Drawing.Size(86, 17)
        Me.chbCNAMISTE.TabIndex = 55
        Me.chbCNAMISTE.Text = "Client CNAM"
        Me.chbCNAMISTE.UseVisualStyleBackColor = True
        '
        'cmbMutuelle
        '
        Me.cmbMutuelle.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbMutuelle.Caption = ""
        Me.cmbMutuelle.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbMutuelle.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbMutuelle.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbMutuelle.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbMutuelle.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbMutuelle.Images.Add(CType(resources.GetObject("cmbMutuelle.Images"), System.Drawing.Image))
        Me.cmbMutuelle.Location = New System.Drawing.Point(297, 100)
        Me.cmbMutuelle.MatchEntryTimeout = CType(2000, Long)
        Me.cmbMutuelle.MaxDropDownItems = CType(5, Short)
        Me.cmbMutuelle.MaxLength = 32767
        Me.cmbMutuelle.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbMutuelle.Name = "cmbMutuelle"
        Me.cmbMutuelle.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbMutuelle.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbMutuelle.Size = New System.Drawing.Size(138, 21)
        Me.cmbMutuelle.TabIndex = 49
        Me.cmbMutuelle.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbMutuelle.PropBag = resources.GetString("cmbMutuelle.PropBag")
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.Label17)
        Me.GroupBox2.Controls.Add(Me.Label16)
        Me.GroupBox2.Controls.Add(Me.dtpDernierReglementFin)
        Me.GroupBox2.Controls.Add(Me.dtpDernierAchatFin)
        Me.GroupBox2.Controls.Add(Me.Label3)
        Me.GroupBox2.Controls.Add(Me.dtpDernierReglement)
        Me.GroupBox2.Controls.Add(Me.dtpDernierAchat)
        Me.GroupBox2.Controls.Add(Me.Label4)
        Me.GroupBox2.Location = New System.Drawing.Point(546, 8)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(254, 129)
        Me.GroupBox2.TabIndex = 8
        Me.GroupBox2.TabStop = False
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Location = New System.Drawing.Point(8, 17)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(94, 13)
        Me.Label3.TabIndex = 50
        Me.Label3.Text = "Dernier achat Du :"
        '
        'dtpDernierReglement
        '
        Me.dtpDernierReglement.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDernierReglement.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDernierReglement.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDernierReglement.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDernierReglement.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDernierReglement.Location = New System.Drawing.Point(8, 79)
        Me.dtpDernierReglement.Name = "dtpDernierReglement"
        Me.dtpDernierReglement.Size = New System.Drawing.Size(118, 18)
        Me.dtpDernierReglement.TabIndex = 53
        Me.dtpDernierReglement.Tag = Nothing
        Me.dtpDernierReglement.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDernierReglement.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'dtpDernierAchat
        '
        Me.dtpDernierAchat.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDernierAchat.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDernierAchat.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDernierAchat.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDernierAchat.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDernierAchat.Location = New System.Drawing.Point(7, 33)
        Me.dtpDernierAchat.Name = "dtpDernierAchat"
        Me.dtpDernierAchat.Size = New System.Drawing.Size(118, 18)
        Me.dtpDernierAchat.TabIndex = 51
        Me.dtpDernierAchat.Tag = Nothing
        Me.dtpDernierAchat.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDernierAchat.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Location = New System.Drawing.Point(5, 63)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(113, 13)
        Me.Label4.TabIndex = 52
        Me.Label4.Text = "Dernier règlement Du :"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(245, 104)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(47, 13)
        Me.Label1.TabIndex = 48
        Me.Label1.Text = "Mutuelle"
        '
        'chbSolde
        '
        Me.chbSolde.Location = New System.Drawing.Point(11, 73)
        Me.chbSolde.Name = "chbSolde"
        Me.chbSolde.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chbSolde.Size = New System.Drawing.Size(16, 17)
        Me.chbSolde.TabIndex = 46
        Me.chbSolde.UseVisualStyleBackColor = True
        '
        'bViderLesChamps
        '
        Me.bViderLesChamps.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bViderLesChamps.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bViderLesChamps.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bViderLesChamps.Location = New System.Drawing.Point(968, 70)
        Me.bViderLesChamps.Name = "bViderLesChamps"
        Me.bViderLesChamps.Size = New System.Drawing.Size(115, 35)
        Me.bViderLesChamps.TabIndex = 24
        Me.bViderLesChamps.Text = "Vider les champs"
        Me.bViderLesChamps.UseVisualStyleBackColor = True
        Me.bViderLesChamps.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bRechercher
        '
        Me.bRechercher.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bRechercher.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bRechercher.Image = Global.Pharma2000Premium.My.Resources.Resources.recherche2
        Me.bRechercher.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bRechercher.Location = New System.Drawing.Point(968, 11)
        Me.bRechercher.Name = "bRechercher"
        Me.bRechercher.Size = New System.Drawing.Size(115, 53)
        Me.bRechercher.TabIndex = 23
        Me.bRechercher.Text = "Rechercher                F4"
        Me.bRechercher.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bRechercher.UseVisualStyleBackColor = True
        Me.bRechercher.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbVille
        '
        Me.cmbVille.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbVille.Caption = ""
        Me.cmbVille.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbVille.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbVille.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbVille.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbVille.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbVille.Images.Add(CType(resources.GetObject("cmbVille.Images"), System.Drawing.Image))
        Me.cmbVille.Location = New System.Drawing.Point(43, 115)
        Me.cmbVille.MatchEntryTimeout = CType(2000, Long)
        Me.cmbVille.MaxDropDownItems = CType(5, Short)
        Me.cmbVille.MaxLength = 32767
        Me.cmbVille.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbVille.Name = "cmbVille"
        Me.cmbVille.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbVille.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbVille.Size = New System.Drawing.Size(158, 21)
        Me.cmbVille.TabIndex = 11
        Me.cmbVille.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbVille.PropBag = resources.GetString("cmbVille.PropBag")
        '
        'lVilleClient
        '
        Me.lVilleClient.AutoSize = True
        Me.lVilleClient.Location = New System.Drawing.Point(10, 119)
        Me.lVilleClient.Name = "lVilleClient"
        Me.lVilleClient.Size = New System.Drawing.Size(26, 13)
        Me.lVilleClient.TabIndex = 10
        Me.lVilleClient.Text = "Ville"
        '
        'cmbSituation
        '
        Me.cmbSituation.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbSituation.Caption = ""
        Me.cmbSituation.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbSituation.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbSituation.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbSituation.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbSituation.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbSituation.Images.Add(CType(resources.GetObject("cmbSituation.Images"), System.Drawing.Image))
        Me.cmbSituation.Location = New System.Drawing.Point(297, 19)
        Me.cmbSituation.MatchEntryTimeout = CType(2000, Long)
        Me.cmbSituation.MaxDropDownItems = CType(5, Short)
        Me.cmbSituation.MaxLength = 32767
        Me.cmbSituation.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbSituation.Name = "cmbSituation"
        Me.cmbSituation.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbSituation.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbSituation.Size = New System.Drawing.Size(238, 21)
        Me.cmbSituation.TabIndex = 9
        Me.cmbSituation.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbSituation.PropBag = resources.GetString("cmbSituation.PropBag")
        '
        'lSituationClient
        '
        Me.lSituationClient.AutoSize = True
        Me.lSituationClient.Location = New System.Drawing.Point(244, 23)
        Me.lSituationClient.Name = "lSituationClient"
        Me.lSituationClient.Size = New System.Drawing.Size(48, 13)
        Me.lSituationClient.TabIndex = 8
        Me.lSituationClient.Text = "Situation"
        '
        'dtpDernierAchatFin
        '
        Me.dtpDernierAchatFin.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDernierAchatFin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDernierAchatFin.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDernierAchatFin.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDernierAchatFin.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDernierAchatFin.Location = New System.Drawing.Point(133, 33)
        Me.dtpDernierAchatFin.Name = "dtpDernierAchatFin"
        Me.dtpDernierAchatFin.Size = New System.Drawing.Size(118, 18)
        Me.dtpDernierAchatFin.TabIndex = 54
        Me.dtpDernierAchatFin.Tag = Nothing
        Me.dtpDernierAchatFin.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDernierAchatFin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'dtpDernierReglementFin
        '
        Me.dtpDernierReglementFin.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDernierReglementFin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDernierReglementFin.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDernierReglementFin.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDernierReglementFin.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDernierReglementFin.Location = New System.Drawing.Point(130, 79)
        Me.dtpDernierReglementFin.Name = "dtpDernierReglementFin"
        Me.dtpDernierReglementFin.Size = New System.Drawing.Size(118, 18)
        Me.dtpDernierReglementFin.TabIndex = 55
        Me.dtpDernierReglementFin.Tag = Nothing
        Me.dtpDernierReglementFin.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDernierReglementFin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label16
        '
        Me.Label16.AutoSize = True
        Me.Label16.Location = New System.Drawing.Point(133, 18)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(26, 13)
        Me.Label16.TabIndex = 56
        Me.Label16.Text = "Au :"
        '
        'Label17
        '
        Me.Label17.AutoSize = True
        Me.Label17.Location = New System.Drawing.Point(133, 63)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(26, 13)
        Me.Label17.TabIndex = 57
        Me.Label17.Text = "Au :"
        '
        'tSoldeMax
        '
        Me.tSoldeMax.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tSoldeMax.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tSoldeMax.DataType = GetType(Decimal)
        Me.tSoldeMax.Location = New System.Drawing.Point(137, 94)
        Me.tSoldeMax.Name = "tSoldeMax"
        Me.tSoldeMax.Size = New System.Drawing.Size(64, 18)
        Me.tSoldeMax.TabIndex = 61
        Me.tSoldeMax.Tag = Nothing
        Me.tSoldeMax.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tSoldeMax.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label18
        '
        Me.Label18.AutoSize = True
        Me.Label18.Location = New System.Drawing.Point(111, 95)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(17, 13)
        Me.Label18.TabIndex = 62
        Me.Label18.Text = "Et"
        '
        'fClient
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1273, 631)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fClient"
        Me.Text = "fClient"
        Me.Panel.ResumeLayout(False)
        Me.Panel.PerformLayout()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.gClient, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.tMatriculeMutuelle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tIdCnam, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNomRecherche, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCode, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbNomClient, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tSoldeMin, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        CType(Me.cmbMutuelle, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.dtpDernierReglement, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpDernierAchat, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbVille, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbSituation, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpDernierAchatFin, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpDernierReglementFin, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tSoldeMax, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents cmbNomClient As C1.Win.C1List.C1Combo
    Friend WithEvents lNomClient As System.Windows.Forms.Label
    Friend WithEvents cmbVille As C1.Win.C1List.C1Combo
    Friend WithEvents lVilleClient As System.Windows.Forms.Label
    Friend WithEvents cmbSituation As C1.Win.C1List.C1Combo
    Friend WithEvents lSituationClient As System.Windows.Forms.Label
    Friend WithEvents bSuprimerClient As C1.Win.C1Input.C1Button
    Friend WithEvents bModifierClient As C1.Win.C1Input.C1Button
    Friend WithEvents bAjouterClient As C1.Win.C1Input.C1Button
    Friend WithEvents gClient As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bViderLesChamps As C1.Win.C1Input.C1Button
    Friend WithEvents bRechercher As C1.Win.C1Input.C1Button
    Friend WithEvents chbSolde As System.Windows.Forms.CheckBox
    Friend WithEvents dtpDernierAchat As C1.Win.C1Input.C1DateEdit
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents cmbMutuelle As C1.Win.C1List.C1Combo
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents chbCNAMISTE As System.Windows.Forms.CheckBox
    Friend WithEvents chbCrediteur As System.Windows.Forms.CheckBox
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents dtpDernierReglement As C1.Win.C1Input.C1DateEdit
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents tSoldeMin As C1.Win.C1Input.C1TextBox
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
    Friend WithEvents CR As Pharma2000Premium.EtatDesClients
    Friend WithEvents lResteAPayer As System.Windows.Forms.Label
    Friend WithEvents lTotalEnCours As System.Windows.Forms.Label
    Friend WithEvents lTotalSolde As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents lNbreDesClients As System.Windows.Forms.Label
    Friend WithEvents chbVoirClientsSupprimes As System.Windows.Forms.CheckBox
    Friend WithEvents chbMutualiste As System.Windows.Forms.CheckBox
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents tCode As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents tNomRecherche As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents chbNonCNAMISTE As System.Windows.Forms.CheckBox
    Friend WithEvents chbNonMutualiste As System.Windows.Forms.CheckBox
    Friend WithEvents tIdCnam As C1.Win.C1Input.C1TextBox
    Friend WithEvents tMatriculeMutuelle As C1.Win.C1Input.C1TextBox
    Friend WithEvents chbCodeExact As System.Windows.Forms.CheckBox
    Friend WithEvents Label18 As System.Windows.Forms.Label
    Friend WithEvents tSoldeMax As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label17 As System.Windows.Forms.Label
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents dtpDernierReglementFin As C1.Win.C1Input.C1DateEdit
    Friend WithEvents dtpDernierAchatFin As C1.Win.C1Input.C1DateEdit
End Class
