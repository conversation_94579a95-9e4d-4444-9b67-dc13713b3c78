﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fRechercheArticleMultiCritere

    Dim cmdArticleRechercheMC As New SqlCommand
    Dim daArticleRechercheMC As New SqlDataAdapter
    Dim dsArticleRechercheMC As New DataSet

    Public CodeArticleRecherche As String = ""


    Public Sub init()

        Dim StrSQL As String = ""

        'chargement des Formes
        StrSQL = "SELECT DISTINCT LibelleForme,CodeForme FROM FORME_ARTICLE Where SupprimeForme=0 ORDER BY LibelleForme ASC"
        cmdArticleRechercheMC.Connection = ConnectionServeur
        cmdArticleRechercheMC.CommandText = StrSQL
        daArticleRechercheMC = New SqlDataAdapter(cmdArticleRechercheMC)
        daArticleRechercheMC.Fill(dsArticleRechercheMC, "FORME")
        cmbForme.DataSource = dsArticleRechercheMC.Tables("FORME")
        cmbForme.ValueMember = "CodeForme"
        cmbForme.DisplayMember = "LibelleForme"
        cmbForme.ColumnHeaders = False
        cmbForme.Splits(0).DisplayColumns("CodeForme").Visible = False
        cmbForme.Splits(0).DisplayColumns("LibelleForme").Width = 10
        cmbForme.ExtendRightColumn = True

        'chargement des Categories
        StrSQL = "SELECT DISTINCT LibelleCategorie,CodeCategorie FROM CATEGORIE WHERE SupprimeCategorie=0 ORDER BY LibelleCategorie ASC"
        cmdArticleRechercheMC.Connection = ConnectionServeur
        cmdArticleRechercheMC.CommandText = StrSQL
        daArticleRechercheMC = New SqlDataAdapter(cmdArticleRechercheMC)
        daArticleRechercheMC.Fill(dsArticleRechercheMC, "CATEGORIE")
        cmbCategorie.DataSource = dsArticleRechercheMC.Tables("CATEGORIE")
        cmbCategorie.ValueMember = "CodeCategorie"
        cmbCategorie.DisplayMember = "LibelleCategorie"
        cmbCategorie.ColumnHeaders = False
        cmbCategorie.Splits(0).DisplayColumns("CodeCategorie").Visible = False
        cmbCategorie.Splits(0).DisplayColumns("LibelleCategorie").Width = 10
        cmbCategorie.ExtendRightColumn = True

        'chargement des DCI
        StrSQL = "SELECT DISTINCT LibelleDCI,CodeDCI FROM DCI WHERE SupprimeDCI=0 ORDER BY LibelleDCI ASC"
        cmdArticleRechercheMC.Connection = ConnectionServeur
        cmdArticleRechercheMC.CommandText = StrSQL
        daArticleRechercheMC = New SqlDataAdapter(cmdArticleRechercheMC)
        daArticleRechercheMC.Fill(dsArticleRechercheMC, "DCI")
        cmbDCI.DataSource = dsArticleRechercheMC.Tables("DCI")
        cmbDCI.ValueMember = "CodeDCI"
        cmbDCI.DisplayMember = "LibelleDCI"
        cmbDCI.ColumnHeaders = False
        cmbDCI.Splits(0).DisplayColumns("CodeDCI").Visible = False
        cmbDCI.Splits(0).DisplayColumns("LibelleDCI").Width = 10
        cmbDCI.ExtendRightColumn = True

        'chargement des LABO
        StrSQL = "SELECT DISTINCT NomLabo,CodeLabo FROM LABORATOIRE WHERE SupprimeLabo=0 ORDER BY NomLabo ASC"
        cmdArticleRechercheMC.Connection = ConnectionServeur
        cmdArticleRechercheMC.CommandText = StrSQL
        daArticleRechercheMC = New SqlDataAdapter(cmdArticleRechercheMC)
        daArticleRechercheMC.Fill(dsArticleRechercheMC, "LABORATOIRE")
        cmbLabo.DataSource = dsArticleRechercheMC.Tables("LABORATOIRE")
        cmbLabo.ValueMember = "CodeLabo"
        cmbLabo.DisplayMember = "NomLabo"
        cmbLabo.ColumnHeaders = False
        cmbLabo.Splits(0).DisplayColumns("CodeLabo").Visible = False
        cmbLabo.Splits(0).DisplayColumns("NomLabo").Width = 10
        cmbLabo.ExtendRightColumn = True

        AfficherArticle()
    End Sub

    Public Sub AfficherArticle()

        Dim I As Integer
        Dim Cond As String = "Supprime=0"

        If (dsArticleRechercheMC.Tables.IndexOf("Article") > -1) Then
            dsArticleRechercheMC.Tables("Article").Clear()
        End If

        'Composer la condition de la requête  

        If tCommence.Text <> "" Then
            Cond += " AND ARTICLE.Designation LIKE '" + tCommence.Text.Replace("'", "''") + "%'"
        End If

        If tContient.Text <> "" Then
            Cond += " AND ARTICLE.Designation LIKE '%" + tContient.Text.Replace("'", "''") + "%'"
        End If

        If tTermine.Text <> "" Then
            Cond += " AND ARTICLE.Designation LIKE '%" + tTermine.Text.Replace("'", "''") + "'"
        End If

        If tPrixBas.Text <> "" Then
            Cond += " AND ARTICLE.PrixVenteTTC >=" + Quote(tPrixBas.Text.ToString)
        End If

        If tPrixHaut.Text <> "" Then
            Cond += " AND ARTICLE.PrixVenteTTC <= " + Quote(tPrixHaut.Text.ToString)
        End If

        If cmbForme.Text <> "" Then
            Cond += " AND FORME_ARTICLE.LibelleForme LIKE '%" + cmbForme.Text.Replace("'", "''") + "%'"
        End If

        If cmbCategorie.Text <> "" Then
            Cond += " AND CATEGORIE.LibelleCategorie LIKE '%" + cmbCategorie.Text.Replace("'", "''") + "%'"
        End If

        If cmbDCI.Text <> "" Then
            Cond += " AND LibelleDCI LIKE '" + cmbDCI.Text + "%'"
        End If

        If cmbLabo.Text <> "" Then
            Cond += " AND NomLabo LIKE '%" + cmbLabo.Text.Replace("'", "''") + "%'"
        End If

        cmdArticleRechercheMC.CommandText = "SELECT ARTICLE.CodeArticle, ARTICLE.CodeABarre, " + _
                                            " Designation,  " + _
                                            " PrixVenteTTC, " + _
                                            " FORME_ARTICLE.LibelleForme, " + _
                                            " CASE WHEN Stock IS NULL THEN 0 ELSE Stock END as STock, " + _
                                            " LABORATOIRE.NomLabo, " + _
                                            " LibelleCategorie," + _
                                            " LibelleDCI," + _
                                            " SITUATION_ARTICLE.LibelleSituationArticle" + _
                                            " FROM Article" + _
                                            " LEFT OUTER JOIN FORME_ARTICLE ON FORME_ARTICLE.CodeForme=Article.CodeForme " + _
                                            " LEFT OUTER JOIN SITUATION_ARTICLE ON SITUATION_ARTICLE.CodeSituationArticle=ARTICLE.CodeSituation" + _
                                            " LEFT OUTER JOIN LABORATOIRE on LABORATOIRE.CodeLabo=ARTICLE.CodeLabo" + _
                                            " LEFT OUTER JOIN CATEGORIE on CATEGORIE.CodeCategorie=ARTICLE.CodeCategorie" + _
                                            " LEFT OUTER JOIN DCI on DCI.CodeDCI=ARTICLE.DCI1" + _
                                            " LEFT OUTER JOIN (SELECT  CodeArticle, SUM(QteLotArticle) AS Stock FROM dbo.LOT_ARTICLE  GROUP BY CodeArticle) AS STOCK ON STOCK.CodeArticle = Article.CodeArticle " + _
                                            " WHERE " + Cond + _
                                            " ORDER BY CodeArticle"

        cmdArticleRechercheMC.Connection = ConnectionServeur
        daArticleRechercheMC = New SqlDataAdapter(cmdArticleRechercheMC)
        daArticleRechercheMC.Fill(dsArticleRechercheMC, "Article")

        With gArticles
            .Columns.Clear()
            .DataSource = dsArticleRechercheMC
            .DataMember = "ARTICLE"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code Article"
            .Columns("Designation").Caption = "Designation"
            .Columns("PrixVenteTTC").Caption = "Prix Vente TTC"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("NomLabo").Caption = "Laboratoire"
            .Columns("LibelleSituationArticle").Caption = "Situation"
            .Columns("LibelleCategorie").Caption = "Catégorie"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("CodeArticle").Width = 120
            .Splits(0).DisplayColumns("Stock").Width = 40
            .Splits(0).DisplayColumns("Stock").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("CodeABarre").Width = 120
            .Splits(0).DisplayColumns("Designation").Width = 320
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 80
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("NomLabo").Width = 80
            .Splits(0).DisplayColumns("NomLabo").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("LibelleSituationArticle").Width = 80
            .Splits(0).DisplayColumns("LibelleSituationArticle").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("LibelleCategorie").Width = 80
            .Splits(0).DisplayColumns("LibelleCategorie").Style.HorizontalAlignment = AlignHorzEnum.Center

            .Splits(0).DisplayColumns("CodeArticle").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True

            'Style du Caractere et du grid
            ParametreGrid(gArticles)
        End With

        'Dim Col As New C1.Win.C1TrueDBGrid.C1DataColumn
        'Dim dc As C1.Win.C1TrueDBGrid.C1DisplayColumn

        'With gArticles
        '    .Columns.Insert(0, Col)
        '    Col.Caption = "Stock"
        '    dc = .Splits(0).DisplayColumns.Item("Stock")
        '    dc.Width = 40
        '    .Splits(0).DisplayColumns(7).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
        '    .Splits(0).DisplayColumns(7).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
        '    dc.Visible = True
        '    .Rebind(True)
        'End With

        gArticles.MarqueeStyle = MarqueeEnum.HighlightRow
        'tCommence.Focus()

    End Sub

    Public Function Calcule_Stock(ByVal CodeArticle)
        Dim StrSQLStock As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Stock As Integer = 0

        'calcul du Stock
        StrSQLStock = "SELECT SUM (QteLotArticle) FROM [PHARMA].[dbo].[LOT_ARTICLE] WHERE CodeArticle=" + _
        Quote(CodeArticle) '+ " And ([DatePeremptionArticle] > '" + System.DateTime.Now.Date + "'" + _
        '" OR [DatePeremptionArticle] is null)"

        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQLStock
        Try
            Stock = CmdCalcul.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        Return (Convert.ToSingle(Stock))
    End Function

    Private Sub gArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gArticles.Click

    End Sub
    Public Sub AfficherFicheArticle(ByVal CodeArticle, ByVal StockArticle, ByVal Designation)
        Dim MyFicheArticle As New fFicheArticle

        MyFicheArticle.CodeArticle = CodeArticle
        MyFicheArticle.StockArticle = StockArticle
        MyFicheArticle.DesignationArticle = Designation
        MyFicheArticle.ajoutmodif = "M"

        MyFicheArticle.Init()
        MyFicheArticle.ShowDialog()
        MyFicheArticle.Close()
        MyFicheArticle.Dispose()
    End Sub

    Private Sub gArticles_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyUp

        If e.KeyCode = Keys.F1 And gArticles.Columns("CodeArticle").Value <> "" Then
            AfficherFicheArticle(gArticles.Columns("CodeArticle").Value, gArticles.Columns("Stock").Value, gArticles.Columns("Designation").Value)
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub gArticle_UnboundColumnFetch(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gArticles.UnboundColumnFetch
        Dim y As String = ""
        y = gArticles(e.Row, ("CodeArticle"))
        e.Value = CalculeStock(y)
    End Sub

    Private Sub bOk_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOk.Click
        'CodeArticleRecherche = gArticles(gArticles.Row, ("CodeArticle"))
        CodeArticleRecherche = gArticles(gArticles.Row, ("CodeABarre"))
        Me.Hide()
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Me.Hide()
    End Sub

    Private Sub fRechercheArticleMultiCritere_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        init()
        tCommence.Focus()
    End Sub

    Private Sub cmbForme_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbForme.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbCategorie.Focus()
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub cmbForme_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbForme.TextChanged
        AfficherArticle()
    End Sub

    Private Sub cmbCategorie_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCategorie.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbDCI.Focus()
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub cmbCategorie_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbCategorie.TextChanged
        AfficherArticle()
    End Sub

    Private Sub cmbDCI_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbDCI.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbLabo.Focus()
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub cmbDCI_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbDCI.TextChanged
        AfficherArticle()
    End Sub

    Private Sub cmbLabo_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbLabo.KeyUp
        If e.KeyCode = Keys.Enter Then
            tPrixBas.Focus()
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub cmbLabo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbLabo.TextChanged
        AfficherArticle()
    End Sub

    Private Sub tCommence_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCommence.KeyUp
        If e.KeyCode = Keys.Enter Then
            tContient.Focus()
            Exit Sub
        End If
        If e.KeyCode = Keys.Down Then
            gArticles.Focus()
            Exit Sub
        End If
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub tCommence_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCommence.TextChanged
        AfficherArticle()
    End Sub

    Private Sub tContient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tContient.KeyUp
        If e.KeyCode = Keys.Enter Then
            tTermine.Focus()
            Exit Sub
        End If
        If e.KeyCode = Keys.Down Then
            gArticles.Focus()
            Exit Sub
        End If
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub tContient_Layout(ByVal sender As Object, ByVal e As System.Windows.Forms.LayoutEventArgs) Handles tContient.Layout

    End Sub

    Private Sub tContient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tContient.TextChanged
        AfficherArticle()
    End Sub

    Private Sub tTermine_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTermine.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbForme.Focus()
            Exit Sub
        End If
        If e.KeyCode = Keys.Down Then
            gArticles.Focus()
            Exit Sub
        End If
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub tTermine_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tTermine.TextChanged
        AfficherArticle()
    End Sub

    Private Sub tPrixBas_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tPrixBas.KeyUp
        If e.KeyCode = Keys.Enter Then
            tPrixHaut.Focus()
            Exit Sub
        End If
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub tPrixBas_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixBas.TextChanged
        AfficherArticle()
    End Sub

    Private Sub tPrixHaut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tPrixHaut.KeyUp
        If e.KeyCode = Keys.Enter Then
            gArticles .Focus()
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub tPrixHaut_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixHaut.TextChanged
        AfficherArticle()
    End Sub

    Private Sub GroupBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GroupBox1.Enter

    End Sub

    Private Sub GroupBox1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles GroupBox1.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub
End Class