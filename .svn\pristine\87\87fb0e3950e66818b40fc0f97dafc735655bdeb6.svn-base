﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fProfilUtilisateur
    Dim cmdProfil As New SqlCommand
    Dim daProfil As New SqlDataAdapter
    Dim dsProfil As New DataSet
    Dim cbProfil As New SqlCommandBuilder

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "116" And bAjouter.Enabled = True Then
            bAjouter_Click(sender, e)
        End If

        If argument = "118" And bSupprimer.Enabled = True Then
            bSupprimer_Click(sender, e)
        End If

        If argument = "123" And bSupprimer.Enabled = True Then
            bQuitter_Click(sender, e)
        End If

    End Sub

    Public Sub Init()
        AfficherProfil()
    End Sub

    Private Sub AfficherProfil()
        Dim StrSQL As String = ""
        Dim I As Integer
        Dim Cond As String = "1=1"
        dsProfil.Clear()

        StrSQL = " SELECT " + _
                 " CodeProfil, " + _
                 " NomProfil, " + _
                 " Remarque " + _
                 " FROM PROFIL "

        cmdProfil.Connection = ConnectionServeur
        cmdProfil.CommandText = StrSQL
        daProfil = New SqlDataAdapter(cmdProfil)
        daProfil.Fill(dsProfil, "PROFIL")

        With gProfil
            .Columns.Clear()
            .DataSource = dsProfil
            .DataMember = "PROFIL"
            .Rebind(False)
            .Columns("CodeProfil").Caption = "Code du profil"
            .Columns("NomProfil").Caption = "Nom"
            .Columns("Remarque").Caption = "Remarque"
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("CodeProfil").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).ExtendRightColumn = True
            .Splits(0).RecordSelectors = False

            .Splits(0).DisplayColumns("CodeProfil").Width = 200
            .Splits(0).DisplayColumns("CodeProfil").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("NomProfil").Width = 200
            .Splits(0).DisplayColumns("Remarque").Width = 200

            'Style du Caractere et du grid
            ParametreGrid(gProfil)
        End With
    End Sub

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click
        Dim StrSQL = ""
        Dim dr As DataRow
        If (tCodeProfil.Text = "") Then
            MsgBox("Veuillez saisir le code du profil à ajouter !", MsgBoxStyle.Critical, "Erreur")
            tCodeProfil.Focus()
            Exit Sub
        End If
        If Not IsNumeric(tCodeProfil.Text) Then
            MsgBox("Veuillez saisir un code valide !", MsgBoxStyle.Critical, "Erreur")
            tCodeProfil.Focus()
            Exit Sub
        End If
        If tNomProfil.Text = "" Then
            MsgBox("Veuillez saisir le nom du profil à ajouter !", MsgBoxStyle.Critical, "Erreur")
            tNomProfil.Focus()
            Exit Sub
        End If

        dsProfil.Clear()
        StrSQL = "SELECT TOP 0 * FROM PROFIL"

        cmdProfil.Connection = ConnectionServeur
        cmdProfil.CommandText = StrSQL
        daProfil = New SqlDataAdapter(cmdProfil)
        daProfil.Fill(dsProfil, "PROFIL")
        cbProfil = New SqlCommandBuilder(daProfil)

        With dsProfil
            dr = .Tables("PROFIL").NewRow
            dr.Item("CodeProfil") = tCodeProfil.Text
            dr.Item("NomProfil") = tNomProfil.Text
            dr.Item("Remarque") = tRemarque.Text
            .Tables("PROFIL").Rows.Add(dr)
        End With
        Try
            daProfil.Update(dsProfil, "PROFIL")
            AfficherProfil()
            tCodeProfil.Text = ""
            tNomProfil.Text = ""
            tRemarque.Text = ""
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + "Code existe déjà, vous devez changer le code user", MsgBoxStyle.Critical, "Erreur")
            tCodeProfil.Text = ""
            'MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
        End Try
    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click
        Dim cmd As New SqlCommand
        With gProfil
            If .RowCount > 0 Then
                If MsgBox("Voulez vous vraiment supprimer ce profil ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                    Try
                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = "DELETE FROM PROFIL WHERE CodeProfil =" + Quote(gProfil(gProfil.Row, "CodeProfil"))
                        cmd.ExecuteNonQuery()

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = "DELETE FROM PROFIL_DETAILS WHERE CodeProfil =" + Quote(gProfil(gProfil.Row, "CodeProfil"))
                        cmd.ExecuteNonQuery()

                        AfficherProfil()
                    Catch ex As Exception
                        MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                    End Try
                End If
            End If
        End With
    End Sub

    Private Sub bAffecterAutorisations_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAffecterAutorisations.Click

        Dim MyAffectationAutorisationProfil As New fAffectationDesAutorisationsProfil
        MyAffectationAutorisationProfil.CodeProfil = gProfil(gProfil.Row, "CodeProfil")
        MyAffectationAutorisationProfil.lTitre.Text = "AFFECTATION DES AUTORISATIONS AU PROFIL :        " + gProfil(gProfil.Row, "NomProfil")
        MyAffectationAutorisationProfil.init()

        bAjouter.Enabled = False
        bAffecterAutorisations.Enabled = False
        bSupprimer.Enabled = False

        MyAffectationAutorisationProfil.ShowDialog()

        bAjouter.Enabled = True
        bAffecterAutorisations.Enabled = True
        bSupprimer.Enabled = True
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub tCodeProfil_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeProfil.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNomProfil.Focus()
        End If
    End Sub

    Private Sub tCodeProfil_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeProfil.TextChanged

    End Sub

    Private Sub tNomProfil_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNomProfil.KeyUp
        If e.KeyCode = Keys.Enter Then
            tRemarque.Focus()
        End If
    End Sub

    Private Sub tNomProfil_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNomProfil.TextChanged

    End Sub

    Private Sub tRemarque_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRemarque.KeyUp
        If e.KeyCode = Keys.Enter Then
            bAjouter.Focus()
        End If
    End Sub

    Private Sub tRemarque_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tRemarque.TextChanged

    End Sub
End Class