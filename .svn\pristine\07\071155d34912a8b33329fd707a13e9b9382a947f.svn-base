﻿Public Class clsCircle
    Inherits GMap.NET.WindowsForms.GMapMarker
    Private MyMapControl As GMap.NET.WindowsForms.GMapControl = Nothing
    Private MyCentralPoint As GMap.NET.PointLatLng = Nothing

    Public Sub New(ByVal p As GMap.NET.PointLatLng, ByVal _MapControl As GMap.NET.WindowsForms.GMapControl)
        MyBase.New(p)
        Me.MyCentralPoint = p
        Me.MyMapControl = _MapControl
    End Sub
    Public Property [Radius] As Double = 200
    Public Property [Pen] As Pen = New Pen(Brushes.Orange, 2)
    Public Property [Brush] As Brush = New SolidBrush(Color.FromArgb(60, Me.Pen.Color))
    Public Property [Fill] As Boolean = True

    Public Overrides Sub OnRender(ByVal g As Graphics)
        Dim _Side As Integer = (Me.Radius / Me.MyMapControl.MapProvider.Projection.GetGroundResolution(Me.MyMapControl.Zoom, Position.Lat)) * 2
        Me.Size = New Size(_Side, _Side)
        Me.Offset = New Point(-_Side / 2, -_Side / 2)
        '---
        g.SmoothingMode = Drawing2D.SmoothingMode.AntiAlias
        Dim _rr As New Rectangle(Me.LocalPosition.X - (_Side / 2), Me.LocalPosition.Y - (_Side / 2), _Side, _Side)
        If Me.Fill = True Then
            g.FillEllipse(Me.Brush, _rr)
        End If
        'g.DrawRectangle(Me.Pen, _rr)
        g.DrawEllipse(Me.Pen, _rr)
        '---
    End Sub
End Class