﻿Imports System.Data.SqlClient

Public Class fMaquetteCnam
    '----------------- les variables de retour vers la vente
    Public Shared NoteVente As String = ""
    Public Shared Confirmer As Boolean = False
    Public Shared CodeMalade As Integer = 0
    Public Shared CodeAPCI As Integer = 0
    Public Shared DureeTraitement As Integer = 0

    Public Shared NomMalade As String = ""
    Public Shared Rang As Integer = 0
    Public Shared DateNaissance As Date
    Public Shared CodeLienDeParente As Integer = 0
    Public Shared LibelleLienDeParente As String = ""
    Public Shared MontantPrixEnChargeParLaCNAMAppareillage As Double = 0.0

    Public TableArticleJoursTraitement As New DataTable

    '----------------- variables locaux
    Dim cmdChargement As New SqlCommand
    Dim cbChargement As New SqlCommandBuilder
    Dim dsChargement As New DataSet
    Dim daChargement As New SqlDataAdapter

    Public StrSQL As String = ""
    Public CodeClient As String = ""
    Public PriseEnCharge As Boolean = False
    Public Appareillage As Boolean = False
    Public MontantTotalDuVente As Double = 0.0

    Dim cmd As New SqlCommand

    Dim LienExiste As Boolean = False
    Dim TestValidite As Boolean = False

    Private Sub fMaquetteCnam_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'chargement des membre de la famille
        Try
            dsChargement.Tables("FAMILLE").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT CodeClient," + _
                 "CodeDeFamille," + _
                 "Nom," + _
                 "LibelleLienDeParente," + _
                 "CONVERT(DATE, DateNaissance) AS DateNaissance," + _
                 "CONVERT(DATE, DateValidite) AS DateValidite " + _
                 "FROM CLIENT_FAMILLE,LIEN_PARENTE " + _
                 "WHERE CLIENT_FAMILLE.CodeLienDeParente=LIEN_PARENTE.CodeLienDeParente " + _
                 " AND CodeClient= " + Quote(CodeClient) + _
                 " UNION SELECT CodeClient,0, Nom,'ASSURE', DateNaissance,DateValidite FROM CLIENT WHERE CodeClient =" + Quote(CodeClient) + _
                 " ORDER BY Nom ASC"

        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "FAMILLE")
        cmbMalade.DataSource = dsChargement.Tables("FAMILLE")
        cmbMalade.ValueMember = "CodeDeFamille"
        cmbMalade.DisplayMember = "Nom"
        cmbMalade.ColumnHeaders = False
        cmbMalade.ColumnFooterHeight = 50
        cmbMalade.Splits(0).DisplayColumns("CodeClient").Width = 0
        cmbMalade.Splits(0).DisplayColumns("CodeDeFamille").Width = 0
        cmbMalade.Splits(0).DisplayColumns("Nom").Width = 100
        cmbMalade.Splits(0).DisplayColumns("LibelleLienDeParente").Width = 90
        cmbMalade.Splits(0).DisplayColumns("DateNaissance").Width = 90
        cmbMalade.Splits(0).DisplayColumns("DateValidite").Width = 90
        cmbMalade.Columns("DateNaissance").NumberFormat = "dd/MM/yyyy"
        cmbMalade.Columns("DateValidite").NumberFormat = "dd/MM/yyyy"

        cmbMalade.ExtendRightColumn = True
        cmbMalade.ItemHeight = 30


        If AutoriserSaisieNonMembeFamille = False Then
            cmbMalade.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownCombo
        Else
            cmbMalade.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        End If

        'chargement des LIENS DE PARENTE
        Try
            dsChargement.Tables("LIEN_PARENTE").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT CodeLienDeParente,LibelleLienDeParente FROM LIEN_PARENTE ORDER BY LibelleLienDeParente ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "LIEN_PARENTE")
        cmbLienDeParente.DataSource = dsChargement.Tables("LIEN_PARENTE")
        cmbLienDeParente.ValueMember = "CodeLienDeParente"
        cmbLienDeParente.DisplayMember = "LibelleLienDeParente"
        cmbLienDeParente.ColumnHeaders = False
        cmbLienDeParente.Splits(0).DisplayColumns("CodeLienDeParente").Visible = False
        cmbLienDeParente.Splits(0).DisplayColumns("LibelleLienDeParente").Width = 10
        cmbLienDeParente.ExtendRightColumn = True

        'chargement des Codes APCI
        Try
            dsChargement.Tables("APCI").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT CodeAPCI,NomAPCI FROM APCI ORDER BY CodeAPCI ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "APCI")
        cmbAPCI.DataSource = dsChargement.Tables("APCI")
        cmbAPCI.ValueMember = "CodeAPCI"
        cmbAPCI.DisplayMember = "NomAPCI"
        cmbAPCI.ColumnHeaders = False
        cmbAPCI.Splits(0).DisplayColumns("CodeAPCI").Visible = False
        cmbAPCI.Splits(0).DisplayColumns("NomAPCI").Width = 10
        cmbAPCI.ExtendRightColumn = True


        NoteVente = ""

        If PriseEnCharge = True Then
            cmbAPCI.Enabled = False
        End If

        If Appareillage = True Then
            cmbAPCI.Visible = False
            lAPCI.Visible = False
            lMontantCnam.Visible = True
            tMontantCNAM.Visible = True
        End If

        tDateNaissance.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        tDateNaissance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        cmbLienDeParente.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        'cmbAPCI.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList

        ' Paramétrage de la grille des articles vendus
        With gArticleDureeTraitement
            .DataSource = TableArticleJoursTraitement
            .AllowAddNew = False
            .AllowUpdate = True
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.SolidCellBorder
            .Columns("Designation").Caption = "Désignation"
            .Columns("DureeTraitement").Caption = "Durée de traitement"
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
            .Splits(0).DisplayColumns("DureeTraitement").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Designation").Locked = False
            .Splits(0).DisplayColumns("Designation").Width = 500
            .Splits(0).DisplayColumns("DureeTraitement").Width = 100
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(210, 240, 255)
            .Splits(0).DisplayColumns("DureeTraitement").Style.BackColor = Color.FromArgb(250, 250, 200)
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .Visible = True
        End With
        'Style du Caractere et du grid
        ParametreGrid(gArticleDureeTraitement)

    End Sub

    Private Sub bOk_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOk.Click
        If cmbMalade.Text = "" Then
            MsgBox("Veuillez choisir un malade !", MsgBoxStyle.Critical, "Erreur")
            cmbMalade.Focus()
            Exit Sub
        End If

        If cmbLienDeParente.Text = "" Then
            MsgBox("Veuillez choisir un lien de parenté !", MsgBoxStyle.Critical, "Erreur")
            cmbLienDeParente.Focus()
            Exit Sub
        End If

        If cmbAPCI.Text = "" And cmbAPCI.Enabled = True And PriseEnCharge = False And Appareillage = False Then
            MsgBox("Veuillez choisir une APCI !", MsgBoxStyle.Critical, "Erreur")
            cmbAPCI.Focus()
            Exit Sub
        End If

        If Not Appareillage Then
            If tDureeTraitement.Text = "" Or tDureeTraitement.Text = "0" Then
                MsgBox("Veuillez saisir a durée de traitement !", MsgBoxStyle.Critical, "Erreur")
                tDureeTraitement.Focus()
                Exit Sub
            End If
        End If


        If Appareillage = True Then
            If tMontantCNAM.Value.ToString = "" Then
                tMontantCNAM.Value = "0.000"
            End If
        End If

        If Appareillage = True Then
            If CDbl(tMontantCNAM.Text) > MontantTotalDuVente Then
                MsgBox("Le montant pris en charge par la CNAM est supérieur au montant total !", MsgBoxStyle.Critical, "Erreur")
                tMontantCNAM.Focus()
                tMontantCNAM.Value = "0.000"
                Exit Sub
            End If
            If CDbl(tMontantCNAM.Text) <= 0 Then
                MsgBox("Le montant pris en charge par la CNAM est invalide !", MsgBoxStyle.Critical, "Erreur")
                tMontantCNAM.Focus()
                tMontantCNAM.Value = "0.000"
                Exit Sub
            End If
        End If

        CodeMalade = cmbMalade.SelectedValue
        If cmbAPCI.Text <> "" Then
            CodeAPCI = cmbAPCI.SelectedValue
        Else
            CodeAPCI = 0
        End If

        If tDureeTraitement.Text <> "" Then
            DureeTraitement = Convert.ToInt32(tDureeTraitement.Text)
        End If
        Confirmer = True

        NoteVente = cmbMalade.Text + "," + tDateNaissance.Text + "," + tRang.Text + "," + cmbLienDeParente.Text

        NomMalade = cmbMalade.Text
        If tRang.Text <> "" Then
            Rang = tRang.Text
        End If
        If tDateNaissance.Text <> "" Then
            DateNaissance = tDateNaissance.Text
        End If
        If cmbLienDeParente.Text <> "" Then
            CodeLienDeParente = cmbLienDeParente.SelectedValue
        End If
        LibelleLienDeParente = cmbLienDeParente.Text

        If Appareillage = True Then
            MontantPrixEnChargeParLaCNAMAppareillage = tMontantCNAM.Text
        End If

        Me.Hide()
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        CodeMalade = 0
        CodeAPCI = 0
        If tDureeTraitement.Text <> "" Then
            DureeTraitement = 0
        End If
        Confirmer = False
        Me.Hide()
    End Sub

    Private Sub cmbLienDeParente_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbLienDeParente.KeyUp
        If e.KeyCode = Keys.Enter Then
            If tRang.Enabled = True Then
                tRang.Focus()
            Else
                If Appareillage = True Then
                    tMontantCNAM.Focus()
                Else
                    If cmbAPCI.Enabled = True Then
                        cmbAPCI.Focus()
                    Else
                        tDureeTraitement.Focus()
                    End If
                End If
            End If
        End If
    End Sub

    Private Sub tRang_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRang.KeyUp
        If e.KeyCode = Keys.Enter Then
            If Appareillage = True Then
                tMontantCNAM.Focus()
            Else
                If cmbAPCI.Enabled = True Then
                    cmbAPCI.Focus()
                Else
                    tDureeTraitement.Focus()
                End If
            End If
        End If
    End Sub

    Private Sub cmbAPCI_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbAPCI.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbAPCI.Text = cmbAPCI.WillChangeToText
            tDureeTraitement.Focus()
        Else
            cmbAPCI.OpenCombo()
        End If
    End Sub

    Private Sub tDureeTraitement_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tDureeTraitement.KeyUp
        If e.KeyCode = Keys.Enter Then
            If tDureeTraitement.Text = "" Then tDureeTraitement.Text = "0"
            For i As Integer = 0 To TableArticleJoursTraitement.Rows.Count - 1
                If IsNumeric(TableArticleJoursTraitement.Rows(i)("DureeTraitement")) Then
                    TableArticleJoursTraitement.Rows(i)("DureeTraitement") = CInt(tDureeTraitement.Text)
                Else
                    TableArticleJoursTraitement.Rows(i)("DureeTraitement") = 0
                End If
            Next
            bOk.Focus()
        End If
    End Sub

    Private Sub cmbMalade_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMalade.KeyDown

        If e.KeyCode = Keys.Enter Then
            If cmbMalade.WillChangeToText <> "" Then
                cmbMalade.Text = cmbMalade.WillChangeToText
            End If
            tDateNaissance.Focus()
        Else
            cmbMalade.OpenCombo()
        End If


        ' '' ''--------------- récupération des informations du client lors du clique entrer
        ' '' ''--------------- (l'assure ou l'un de sa famille)
        '' ''If e.KeyCode = Keys.Enter Then

        '' ''    If TestValidite = True Then
        '' ''        TestValidite = False
        '' ''        Exit Sub
        '' ''    End If

        '' ''    If cmbMalade.Text <> cmbMalade.WillChangeToText Or cmbMalade.Text = "" Then
        '' ''        cmbLienDeParente.Enabled = True
        '' ''        tDateNaissance.Focus()
        '' ''        Exit Sub
        '' ''    End If

        '' ''    Dim DateNaissance As String = ""
        '' ''    Dim LibelleLienDeParente As String = ""

        '' ''    DateNaissance = dsChargement.Tables("FAMILLE").Rows(cmbMalade.SelectedIndex).Item("DateNaissance").ToString
        '' ''    LibelleLienDeParente = dsChargement.Tables("FAMILLE").Rows(cmbMalade.SelectedIndex).Item("LibelleLienDeParente").ToString

        '' ''    tDateNaissance.FormatType = C1.Win.C1Input.FormatTypeEnum.DefaultFormat
        '' ''    tDateNaissance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.DefaultFormat

        '' ''    If DateNaissance <> "" Then
        '' ''        tDateNaissance.Text = DateNaissance
        '' ''    Else
        '' ''        tDateNaissance.Text = ""
        '' ''    End If

        '' ''    tDateNaissance.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        '' ''    tDateNaissance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        '' ''    cmbLienDeParente.Text = LibelleLienDeParente

        '' ''    '-------------------- rang 
        '' ''    StrSQL = "SELECT Rang FROM CLIENT_FAMILLE WHERE CodeClient=" + Quote(CodeClient) + " AND CodeDeFamille= " + Quote(cmbMalade.SelectedValue.ToString)

        '' ''    cmd.Connection = ConnectionServeur
        '' ''    cmd.CommandText = StrSQL
        '' ''    Try
        '' ''        tRang.Text = cmd.ExecuteScalar().ToString
        '' ''        'tRang.Value = cmd.ExecuteScalar().ToString
        '' ''    Catch ex As Exception
        '' ''        Console.WriteLine(ex.Message)
        '' ''        tRang.Value = ""
        '' ''    End Try
        '' ''    '' ''If tRang.Text = "0" Then
        '' ''    '' ''    tRang.Text = ""
        '' ''    '' ''End If

        '' ''    '' ''cmbLienDeParente.Enabled = False

        '' ''    tDateNaissance.Focus()
        '' ''End If
    End Sub

    Private Sub cmbMalade_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMalade.KeyUp
       

    End Sub

    Private Sub cmbMalade_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbMalade.LostFocus


        ''''''''''''''''''''''''''''''''
        If TestValidite = True Then
            TestValidite = False
            Exit Sub
        End If

        If cmbMalade.Text <> cmbMalade.WillChangeToText Or cmbMalade.Text = "" Then
            cmbLienDeParente.Enabled = True
            tDateNaissance.Focus()
            Exit Sub
        End If

        Dim DateNaissance As String = ""
        Dim LibelleLienDeParente As String = ""

        DateNaissance = dsChargement.Tables("FAMILLE").Rows(cmbMalade.SelectedIndex).Item("DateNaissance").ToString
        LibelleLienDeParente = dsChargement.Tables("FAMILLE").Rows(cmbMalade.SelectedIndex).Item("LibelleLienDeParente").ToString

        tDateNaissance.FormatType = C1.Win.C1Input.FormatTypeEnum.DefaultFormat
        tDateNaissance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.DefaultFormat

        If DateNaissance <> "" Then
            tDateNaissance.Text = DateNaissance
        Else
            tDateNaissance.Text = ""
        End If

        tDateNaissance.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        tDateNaissance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        cmbLienDeParente.Text = LibelleLienDeParente

        '-------------------- rang 
        StrSQL = "SELECT Rang FROM CLIENT_FAMILLE WHERE CodeClient=" + Quote(CodeClient) + " AND CodeDeFamille= " + Quote(cmbMalade.SelectedValue.ToString)

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL
        Try
            tRang.Text = cmd.ExecuteScalar().ToString
            'tRang.Value = cmd.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
            tRang.Value = ""
        End Try
        '' ''If tRang.Text = "0" Then
        '' ''    tRang.Text = ""
        '' ''End If

        '' ''cmbLienDeParente.Enabled = False

        tDateNaissance.Focus()
        '''''''''''''''''''''''''''''''''



        '----------------------------- test de la date de validité 
        ' If e.KeyCode = Keys.Enter Then
        Dim DateValiditeTest As Date
        'Dim StrSQL As String = ""

        Try
            'StrSQL = " SELECT DateValidite FROM CLIENT_FAMILLE WHERE CodeClient ='"+
            'DateValiditeTest = RecupererValeurExecuteScalaire("DateValidite", "CLIENT_FAMILLE", "CodeClient", cmbClient.SelectedValue)
            DateValiditeTest = dsChargement.Tables("FAMILLE").Rows(cmbMalade.SelectedIndex).Item("DateValidite").ToString
            If DateValiditeTest < System.DateTime.Today And Not Appareillage And Not PriseEnCharge Then
                MsgBox("Date de validité dépassé !!!", MsgBoxStyle.OkOnly, "Message CNAM")
                cmbMalade.Text = ""
                cmbLienDeParente.Text = ""
                tDateNaissance.Text = ""
                tRang.Text = ""
                tDureeTraitement.Text = ""
                TestValidite = True
                cmbMalade.Focus()
                Exit Sub
            End If
        Catch ex As Exception
            'MsgBox("Client n'admet pas de date de validité !!!", MsgBoxStyle.Information, "Message CNAM")
        End Try
        'End If
    End Sub

    Private Sub tDateNaissance_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tDateNaissance.GotFocus
        fMain.MyFicheClient.CodeClient = "44"
        'MessageBox.Show("true")
    End Sub

    Private Sub tDateNaissance_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tDateNaissance.KeyUp

        If e.KeyCode = Keys.Enter Then
            If cmbAPCI.Enabled = True Then
                cmbAPCI.Focus()
            Else
                tRang.Focus()
            End If

            '    cmbAPCI.Focus()

            'If cmbLienDeParente.Enabled = True Then
            '    
            '    cmbLienDeParente.Focus()
            'Else
            '    cmbLienDeParente.Enabled = True
            '    cmbLienDeParente.Focus()
            'End If
        End If

    End Sub

    Private Sub tDateNaissance_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tDateNaissance.LostFocus
        If tDateNaissance.Text = System.DateTime.Today.ToString.Substring(0, 10) Then
            tDateNaissance.FormatType = C1.Win.C1Input.FormatTypeEnum.DefaultFormat
            tDateNaissance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.DefaultFormat
            tDateNaissance.Text = ""
            tDateNaissance.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
            tDateNaissance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        End If

        ''cmbLienDeParente.Focus()

    End Sub

    Private Sub tMontantCNAM_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tMontantCNAM.KeyUp
        Dim Position As Integer = 0
        If e.KeyCode = Keys.Decimal Then
            Position = InStr(tMontantCNAM.Text, ".")
            tMontantCNAM.Text = tMontantCNAM.Text.Substring(0, Position)
            tMontantCNAM.Select(tMontantCNAM.Text.Length, 0)
            Exit Sub
        End If
        If tMontantCNAM.Text.Contains(".") Then
            Position = InStr(tMontantCNAM.Text, ".")
            If tMontantCNAM.Text.Length - Position > 3 Then
                tMontantCNAM.Text = tMontantCNAM.Text.Substring(0, Position + 3)
                tMontantCNAM.Select(tMontantCNAM.Text.Length, 0)
            End If
        End If

        If e.KeyCode = Keys.Enter Then
            tDureeTraitement.Focus()
        End If
    End Sub

    Private Sub tMontantCNAM_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tMontantCNAM.LostFocus
        tMontantCNAM.Text = tMontantCNAM.Text
        If tMontantCNAM.Text = "" Then
            tMontantCNAM.Text = "0.000"
            Exit Sub
        End If

        If tMontantCNAM.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(tMontantCNAM.Text, ".")
            If tMontantCNAM.Text.Length - x = 1 Then
                tMontantCNAM.Text = tMontantCNAM.Text + ("00")
            ElseIf tMontantCNAM.Text.Length - x = 2 Then
                tMontantCNAM.Text = tMontantCNAM.Text + ("0")
            End If
        Else
            tMontantCNAM.Text = tMontantCNAM.Text + ".000"
        End If
    End Sub

    Private Sub cmbLienDeParente_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbLienDeParente.TextChanged
        'If cmbLienDeParente.Text = "ENFANT" Then
        '    tRang.Enabled = True
        'Else
        '    tRang.Enabled = False
        'End If
    End Sub

    Private Sub cmbMalade_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbMalade.TextChanged

        ''''''''''''''''''''''''''''''''
        'If TestValidite = True Then
        '    TestValidite = False
        '    Exit Sub
        'End If

        'If cmbMalade.Text <> cmbMalade.WillChangeToText Or cmbMalade.Text = "" Then
        '    cmbLienDeParente.Enabled = True
        '    tDateNaissance.Focus()
        '    Exit Sub
        'End If

        'Dim DateNaissance As String = ""
        'Dim LibelleLienDeParente As String = ""

        'DateNaissance = dsChargement.Tables("FAMILLE").Rows(cmbMalade.SelectedIndex).Item("DateNaissance").ToString
        'LibelleLienDeParente = dsChargement.Tables("FAMILLE").Rows(cmbMalade.SelectedIndex).Item("LibelleLienDeParente").ToString

        'tDateNaissance.FormatType = C1.Win.C1Input.FormatTypeEnum.DefaultFormat
        'tDateNaissance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.DefaultFormat

        'If DateNaissance <> "" Then
        '    tDateNaissance.Text = DateNaissance
        'Else
        '    tDateNaissance.Text = ""
        'End If

        'tDateNaissance.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        'tDateNaissance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        'cmbLienDeParente.Text = LibelleLienDeParente

        ''-------------------- rang 
        'StrSQL = "SELECT Rang FROM CLIENT_FAMILLE WHERE CodeClient=" + Quote(CodeClient) + " AND CodeDeFamille= " + Quote(cmbMalade.SelectedValue.ToString)

        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = StrSQL
        'Try
        '    tRang.Text = cmd.ExecuteScalar().ToString
        '    'tRang.Value = cmd.ExecuteScalar().ToString
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        '    tRang.Value = ""
        'End Try
        ' '' ''If tRang.Text = "0" Then
        ' '' ''    tRang.Text = ""
        ' '' ''End If

        ' '' ''cmbLienDeParente.Enabled = False

        'tDateNaissance.Focus()
        '''''''''''''''''''''''''''''''''
    End Sub

    Private Sub gArticleDureeTraitement_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gArticleDureeTraitement.Change
        If TableArticleJoursTraitement.Rows.Count > 0 Then
            If gArticleDureeTraitement.Columns(gArticleDureeTraitement.Col).DataField = "DureeTraitement" Then
                If gArticleDureeTraitement.Columns("DureeTraitement").Value <> "" And IsNumeric(gArticleDureeTraitement.Columns("DureeTraitement").Value) Then
                    If gArticleDureeTraitement.Columns("DureeTraitement").Value > 30 And cmbAPCI.Text = "MO" Then
                        gArticleDureeTraitement(gArticleDureeTraitement.Row, "DureeTraitement") = "0"
                    End If
                End If
            End If
        End If
    End Sub
End Class