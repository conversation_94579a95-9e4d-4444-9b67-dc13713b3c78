﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Public Class fHistoriqueDAcces

    Dim cmdListe As New SqlCommand
    Dim cbListe As New SqlCommandBuilder
    Dim dsListe As New DataSet
    Dim daListe As New SqlDataAdapter

    Public StrSQL As String = ""

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub init()

        Dim StrSQL As String = ""

        'charger les Utilisateurs

        StrSQL = "SELECT CodeUtilisateur,Nom FROM UTILISATEUR  ORDER BY Nom ASC"

        cmdListe.Connection = ConnectionServeur
        cmdListe.CommandText = StrSQL
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "UTILISATEUR")
        cmbUtilisateurs.DataSource = dsListe.Tables("UTILISATEUR")
        cmbUtilisateurs.ValueMember = "CodeUtilisateur"
        cmbUtilisateurs.DisplayMember = "Nom"
        cmbUtilisateurs.ColumnHeaders = False
        cmbUtilisateurs.Splits(0).DisplayColumns("CodeUtilisateur").Visible = False
        cmbUtilisateurs.Splits(0).DisplayColumns("Nom").Width = 10
        cmbUtilisateurs.ExtendRightColumn = True

        dtpDate.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDate.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        AfficherAcces()

    End Sub

    Public Sub AfficherAcces()

        Dim i As Integer = 0

        If (dsListe.Tables.IndexOf("LISTE_ACCES") > -1) Then
            dsListe.Tables("LISTE_ACCES").Clear()
        End If

        StrSQL = "select CodeUtilisateur," + _
                 "NomUtilisateur," + _
                 "DateHeure " + _
                 "FROM HISTORIQUEACCES order by DateHeure desc "

        If cmbUtilisateurs.Text <> "" Then
            StrSQL += " WHERE CodeUtilisateur=" + cmbUtilisateurs.SelectedValue
        ElseIf tMotIntegre.Text <> "" Then
            StrSQL += " WHERE NomUtilisateur LIKE '%" + tMotIntegre.Text + "%'"
        End If

        cmdListe.Connection = ConnectionServeur
        cmdListe.CommandText = StrSQL
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "LISTE_ACCES")
        cbListe = New SqlCommandBuilder(daListe)

        With gListe
            .Columns.Clear()
            Try
                .DataSource = dsListe
            Catch ex As Exception
            End Try
            .DataMember = "LISTE_ACCES"
            .Rebind(False)
            .Columns("CodeUtilisateur").Caption = "Code Utilisateur"
            .Columns("NomUtilisateur").Caption = "Nom Utilisateur"
            .Columns("DateHeure").Caption = "Date/Heure"
            .Columns("DateHeure").NumberFormat = "dd/MM/yyyy  HH:mm:ss"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("NomUtilisateur").Style.HorizontalAlignment = AlignHorzEnum.Near

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next
            
            .Splits(0).DisplayColumns("CodeUtilisateur").Width = 100
            .Splits(0).DisplayColumns("NomUtilisateur").Width = 400
            .Splits(0).DisplayColumns("DateHeure").Width = 280

            .Splits(0).DisplayColumns("DateHeure").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gListe)
        End With
    End Sub
    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click

        Dim cmd As New SqlCommand
        If dtpDate.Text = "" Then
            MsgBox("Veillez donnez une date ", MsgBoxStyle.Information, "Information")
            dtpDate.Focus()
            Exit Sub
        End If

        If MsgBox("Voulez vous vraiment supprimer les accès enregistré avant le " + dtpDate.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
            '--------------- suppression des acces
            Try
                cmd.Connection = ConnectionServeur
                cmd.CommandText = "DELETE FROM HISTORIQUEACCES WHERE DateHeure <='" + dtpDate.Text + " 23:59:59'"
                cmd.ExecuteNonQuery()

            Catch ex As Exception
                MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
            End Try
            init()
        End If

    End Sub

    Private Sub cmbUtilisateurs_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbUtilisateurs.TextChanged
        AfficherAcces()
    End Sub

    Private Sub tMotIntegre_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tMotIntegre.TextChanged
        AfficherAcces()
    End Sub
End Class