﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports Microsoft.Reporting.WinForms

Public Class fValeurTVAVente
    Dim _SalesReportService As New Bll.Reporting.SalesReport

    Dim StrSQL As String = ""

    Dim cmdMouvement As New SqlCommand
    Dim daMouvement As New SqlDataAdapter
    Dim dsMouvement As New DataSet


    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub init()
        Dim I As Integer
        Dim TotalBase As Decimal = 0.0
        Dim TotalMontant As Decimal = 0.0
        Dim TotHr As Decimal = 0.0

        If dtpDebut.Text = "" Then
            dtpDebut.Value = Date.Today
        End If
        If dtpFin.Text = "" Then
            dtpFin.Value = Date.Today
        End If

        Dim List As New Library.SortableBindingList(Of Data.Reporting.P_Report_EtatValeurTVAVente_Result)(_SalesReportService.GetEtatValeurTVAVente(dtpDebut.Value, _
                                                                                                        dtpFin.Value, _
                                                                                                        0))

        With gDetails
            .Columns.Clear()
            .DataSource = List
            .Rebind(False)

            .Splits(0).DisplayColumns("TVA").Width = 120
            .Splits(0).DisplayColumns("Base").Width = 120
            .Splits(0).DisplayColumns("MontantTVA").Width = 120
            .Splits(0).DisplayColumns("Id").Visible = False

            .Columns("Base").NumberFormat = "#,###0.000"
            .Columns("MontantTVA").NumberFormat = "#,###0.000"

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gDetails)
        End With

        ' AfficherMouvement()
        For I = 0 To gDetails.RowCount - 1
            TotalBase += gDetails(I, "Base")
            TotalMontant += gDetails(I, "MontantTVA")
            TotHr += gDetails(I, "Honoraire")
        Next



        lTotBase.Text = Math.Round(TotalBase, 3).ToString("### ### ##0.000")
        lTotMontant.Text = Math.Round(TotalMontant, 3).ToString("### ### ##0.000")
        lTotHr.Text = Math.Round(TotHr, 3).ToString("### ### ##0.000")

    End Sub

    Public Sub AfficherMouvement()
        Dim I As Integer
        Dim TotalBase As Decimal = 0.0
        Dim TotalMontant As Decimal = 0.0

        Try
            dsMouvement.Tables("ValeurTVAVente").Clear()
            dsMouvement.Tables("TVA").Clear()
        Catch ex As Exception

        End Try

        StrSQL = " SELECT * FROM dbo.FT_ValeurTVAVente(" & Quote(dtpDebut.Text) & ", " & Quote(dtpFin.Text) & ") "

        'Initialiser les table

        cmdMouvement.Connection = ConnectionServeur
        cmdMouvement.CommandText = StrSQL
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "ValeurTVAVente")

        For I = 0 To gDetails.RowCount - 1
            TotalBase += gDetails(I, "Base")
            TotalMontant += gDetails(I, "MontantTVA")
        Next



        lTotBase.Text = Math.Round(TotalBase, 3).ToString("### ### ##0.000")
        lTotMontant.Text = Math.Round(TotalMontant, 3).ToString("### ### ##0.000")

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Reporting Ventes" Then
                num = I
            End If
        Next
        Dim dt

        Dim _Parameters As New List(Of ReportParameter)()

        Dim _DateDebut As New ReportParameter()
        _DateDebut.Name = "DateDebut"
        _DateDebut.Values.Add(dtpDebut.Value)
        _Parameters.Add(_DateDebut)

        Dim _DateFin As New ReportParameter()
        _DateFin.Name = "DateFin"
        _DateFin.Values.Add(dtpFin.Value)
        _Parameters.Add(_DateFin)

        Dim _Exonere As New ReportParameter()
        _Exonere.Name = "Exonere"
        _Exonere.Values.Add(False)
        _Parameters.Add(_Exonere)

        Dim MyViewer As New fImpressionReportingVente

        dt = _SalesReportService.GetEtatValeurTVAVente(dtpDebut.Value, dtpFin.Value, 0)
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_EtatValeurTVAVente", dt))
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Pharmacie", _SalesReportService.GetPharmacie()))
        MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatValeurTVAVentes.rdl"

        MyViewer.ReportViewer1.LocalReport.SetParameters(_Parameters)

        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel1)

        MyViewer.ReportViewer1.RefreshReport()

        fMain.Tab.SelectedTab.Text = "Impression Reporting Ventes"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    Private Sub dtpDateDebut_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyDown

      

    End Sub

    Private Sub dtpDateFin_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyDown

    End Sub

    Private Sub bDetails_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bDetails.Click
        Dim FtvaDETAILS As New fDetailTVA
        FtvaDETAILS.TVA = gDetails(gDetails.Row, "Tva")
        FtvaDETAILS.DateDebut = dtpDebut.Value
        FtvaDETAILS.DateFin = dtpFin.Value
        FtvaDETAILS.TVAExonere = 0
        FtvaDETAILS.init()
        FtvaDETAILS.ShowDialog()
    End Sub

    Private Sub dtpDebut_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles dtpDebut.Validated
        init()
    End Sub

    Private Sub dtpFin_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles dtpFin.Validated
        init()
    End Sub

    Private Sub dtpDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDebut.TextChanged

    End Sub

    Private Sub dtpFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpFin.TextChanged

    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            init()
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            init()
            dtpDebut.Focus()
        End If
    End Sub

    Private Sub bExportExcel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bExportExcel.Click
        Dim tab(1) As String
        Dim tabIndice(2), tabValeur(2) As String
        Dim x As String


        tabIndice(0) = "0"
        tabIndice(1) = "1"
        tabIndice(2) = "2"

        tabValeur(0) = "Total"
        tabValeur(1) = lTotBase.Text
        tabValeur(2) = lTotMontant.Text


        x = dtpDebut.Text
        tab(0) = "Période DU : " + x + "  AU :  "
        x = dtpFin.Text
        tab(0) = tab(0) + x

        GeneralExportExcel(gDetails, "JOURNAL TVA VENTE", "JournalTVAVente", tab, tabIndice, tabValeur)
    End Sub

    Private Sub chbTVAExonere_CheckedChanged(sender As Object, e As System.EventArgs)
        init()
    End Sub
End Class