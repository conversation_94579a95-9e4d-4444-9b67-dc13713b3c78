<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings />
  <configProtectedData />
  <system.windows.forms />
  <System.Windows.Forms.ApplicationConfigurationSection />
  <uri />
  <connectionStrings>
    <add name="StockManagementEntities" connectionString="metadata=res://*/StockManagement.csdl|res://*/StockManagement.ssdl|res://*/StockManagement.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=MARWEN\SQLSRV;initial catalog=PharmaMardi;persist security info=True;user id=sa;password=********;MultipleActiveResultSets=True;App=EntityFramework&quot;"
      providerName="System.Data.EntityClient" />
    <add name="SaleReportEntities" connectionString="metadata=res://*/SaleReport.csdl|res://*/SaleReport.ssdl|res://*/SaleReport.msl;provider=System.Data.SqlClient;                        provider connection string=&quot;data source=MARWEN\SQLSRV;initial catalog=PharmaMardi;persist security info=True;user id=sa;password=********;MultipleActiveResultSets=True;App=EntityFramework&quot;"
      providerName="System.Data.EntityClient" />
    <add name="BusinessManagementEntities" connectionString="metadata=res://*/BusinessManagement.csdl|res://*/BusinessManagement.ssdl|res://*/BusinessManagement.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=MARWEN\SQLSRV;initial catalog=PharmaMardi;persist security info=True;user id=sa;password=********;MultipleActiveResultSets=True;App=EntityFramework&quot;"
      providerName="System.Data.EntityClient" />
    <add name="ErrorManagementEntities" connectionString="metadata=res://*/ErrorManagement.csdl|res://*/ErrorManagement.ssdl|res://*/ErrorManagement.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=MARWEN\SQLSRV;initial catalog=PharmaMardi;persist security info=True;user id=sa;password=********;MultipleActiveResultSets=True;App=EntityFramework&quot;"
      providerName="System.Data.EntityClient" />
  </connectionStrings>
  <system.diagnostics>
    <sources>
      <source name="DefaultSource" switchName="DefaultSwitch">
        <listeners>
          <add type="System.Diagnostics.DefaultTraceListener" name="Default">
            <filter type="" />
          </add>
          <add name="FileLog">
            <filter type="" />
          </add>
        </listeners>
      </source>
    </sources>
    <sharedListeners>
      <add initializeData="FileLogWriter" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
        name="FileLog">
        <filter type="" />
      </add>
    </sharedListeners>
    <switches>
      <add name="DefaultSwitch" value="Information" />
    </switches>
  </system.diagnostics>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="C1.Win.C1Chart.2" publicKeyToken="A22E16972C085838"
          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.20132.23192" newVersion="2.0.20132.23192" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="C1.Win.C1Chart.2" publicKeyToken="a22e16972c085838"
          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.20132.23192" newVersion="2.0.20132.23192" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.serviceModel>
    <behaviors />
    <comContracts />
    <diagnostics />
    <extensions />
    <protocolMapping />
    <routing />
    <serviceHostingEnvironment />
    <services />
    <standardEndpoints />
    <tracking />
    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_IServiceOne" />
        <binding name="BCBDextherEtrPortBinding" maxReceivedMessageSize="1000000">
          <security mode="Transport" />
        </binding>
        <binding name="BCBDextherEtrPortBinding1" />
        <binding name="BCBDextherEtrPortBinding2">
          <security mode="Transport" />
        </binding>
        <binding name="BCBDextherEtrPortBinding3" />
      </basicHttpBinding>
    </bindings>
    <client>
      <endpoint address="http://www.nextsoftware.com.tn:8080/ServiceOne.svc/ServiceOneKey"
        binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IServiceOne"
        contract="ServiceOneKey.IServiceOne" name="BasicHttpBinding_IServiceOne" />
      <endpoint address="https://bcbdexther.com/tn/BCBDexther/BCBDextherEtr"
        binding="basicHttpBinding" bindingConfiguration="BCBDextherEtrPortBinding"
        contract="ServiceBCB.BCBDextherEtr" name="BCBDextherEtrPort" />
    </client>
  </system.serviceModel>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" />
  </startup>
  <system.net>
    <authenticationModules />
    <connectionManagement />
    <defaultProxy />
    <requestCaching />
    <settings />
    <webRequestModules />
    <mailSettings>
      <smtp />
    </mailSettings>
  </system.net>
  <system.runtime.caching>
    <memoryCache />
  </system.runtime.caching>
  <system.runtime.serialization>
    <dataContractSerializer />
  </system.runtime.serialization>
  <system.serviceModel.activation>
    <diagnostics />
    <net.pipe />
    <net.tcp />
  </system.serviceModel.activation>
  <system.transactions>
    <defaultSettings />
  </system.transactions>
  <system.web>
    <anonymousIdentification />
    <authentication />
    <authorization />
    <clientTarget />
    <compilation />
    <customErrors />
    <deployment />
    <deviceFilters />
    <fullTrustAssemblies />
    <globalization />
    <healthMonitoring />
    <hostingEnvironment />
    <httpCookies />
    <httpHandlers />
    <httpModules />
    <httpRuntime />
    <identity />
    <machineKey />
    <membership />
    <mobileControls />
    <pages />
    <partialTrustVisibleAssemblies />
    <processModel />
    <profile />
    <protocols />
    <roleManager />
    <securityPolicy />
    <sessionPageState />
    <sessionState />
    <siteMap />
    <trace />
    <trust level="Full" />
    <urlMappings />
    <webControls clientScriptsLocation="/aspnet_client/{0}/{1}/" />
    <webParts />
    <webServices />
    <xhtmlConformance />
    <caching>
      <cache />
      <outputCache />
      <outputCacheSettings />
      <sqlCacheDependency />
    </caching>
  </system.web>
  <system.web.extensions>
    <scripting>
      <scriptResourceHandler />
      <webServices>
        <authenticationService />
        <jsonSerialization />
        <profileService />
        <roleService />
      </webServices>
    </scripting>
  </system.web.extensions>
  <system.xaml.hosting>
    <httpHandlers />
  </system.xaml.hosting>
  <system.xml.serialization>
    <dateTimeSerialization />
    <schemaImporterExtensions />
    <xmlSerializer />
  </system.xml.serialization>
</configuration>
<!--maxReceivedMessageSize = "1000000"-->