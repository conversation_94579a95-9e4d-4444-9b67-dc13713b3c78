﻿Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared

Public Class fEmprunt

    Dim cmdEmprunt As New SqlCommand
    Dim cbEmprunt As New SqlCommandBuilder
    Dim dsEmprunt As New DataSet
    Dim daEmprunt As New SqlDataAdapter

    Dim cmdEmpruntEntete As New SqlCommand
    Dim daEmpruntEntete As New SqlDataAdapter
    Dim cbEmpruntEntete As New SqlCommandBuilder

    Dim cmdEmpruntDetail As New SqlCommand
    Dim daEmpruntDetails As New SqlDataAdapter
    Dim cbEmpruntDetails As New SqlCommandBuilder

    Dim mode As String = ""
    Dim StrSQL As String = ""
    Dim I As Integer

    Dim DataRowRecherche As DataRow

    Public NumeroEmprunt As String = ""
    Public NumeroligneEmprunt As Integer = 0

    Public TotalTTCAchat As Double = 0.0
    Public TotalHTAchat As Double = 0.0
    Public TotalTVA As Double = 0.0

    Public TVA As Double = 0.0
    Public Timbre As Double = 0.3

    '------------------------------------------------- les informations de paiement (retour de fenetre paiement)
    Public PaiementOkNo As String = ""
    Public Operateur As Integer = 0

    Public NouvelleEmprunt As DataRow = Nothing 'datarow pour charger l'entête dans la datatable EMPRUNT
    Public NouvelArticle As DataRow = Nothing 'datarow pour charger les détails dans la datatable EMPRUNT_DETAIL
    Public dr As DataRow = Nothing 'datarow pour charger dans la datatable ARTICLE qui contient les articles recherchés pendant la saisie de la designation

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        Try
            If argument = "114" And bConfirmer.Enabled = True Then
                bConfirmer_Click(sender, e)
            End If
            If argument = "115" And bRecherche.Enabled = True Then
                bRecherche_Click(sender, e)
            End If

            If argument = "116" And bAjouter.Enabled = True Then
                bAjouter_Click(sender, e)
            End If

            If argument = "118" And bSupprimer.Enabled = True Then
                bSupprimer_Click(sender, e)
            End If

            If argument = "119" And bModifier.Enabled = True Then
                'bModifier_Click(sender, e)
                bModifier.PerformClick()
            End If

            If argument = "121" And bAnnuler.Enabled = True Then
                bAnnuler_Click(sender, e)
            End If

            If argument = "122" And bImprimer.Enabled = True Then
                bImprimer_Click(sender, e)
            End If
            '--------------------- boutton close 
            If argument = "123" And bQuitter.Enabled = True Then
                bQuitter_Click(sender, e)
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "fonctionF()", ex.Message, "0000052", "Erreur de raccourcir", True, True, True)

        End Try
    End Sub

    Public Sub Init()

        'Initialiser les controles
        initLoadControl()

        'Charger Pharmacies
        initPharmacies()

        'Appel Pour selectionner le dernier ligne 
        NumeroligneEmprunt = selectionDernierLigneEmprunt()

        'Initialiser la DS Emprunt
        initEmpruntEntete()

        'Initialiser la DS Emprunt Details
        initEmpruntDetails()


        'Appel pour charger les information de l'Emprunt en question
        ChargerEmprunt(NumeroligneEmprunt)


        'Mise en forme de la Grid gArticles
        initgArticles()

        'Charger Liste Article
        initArticle()

    End Sub

    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)

        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""
        Try
            Try
                Quote(ValeurCle)
            Catch ex As Exception
                Return Nothing
                Exit Function
            End Try

            StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurCle)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                Valeur = CmdCalcul.ExecuteScalar().ToString
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try


        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "RecupererValeurExecuteScalaire()", ex.Message, "0000053", "Erreur de Recuperer Valeur Execute Scalaire", True, True, True)

        End Try

        Return (Valeur)
    End Function

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click

        Try


            'Suivi du scénario 
            fMessageException.Show("Emprunt", "fEmprunt", "bAjouter_Click", "NoException", "NoError", "Clic sur le bouton Ajouter", False, True, False)

            'Changer le MODE en Ajout

            mode = "Ajout"

            'AppelChargerEmprunt: Pour Récuperer la 
            'structure des DS EMPRUNT et EMPRUNT_DETAILS
            'La valeur 0 est inexistant

            ChargerEmprunt("0")

            '-----------------------------------ajout d'un nouvel enregistrement vide dans les datatables convenables

            NouvelArticle = dsEmprunt.Tables("EMPRUNT_DETAILS").NewRow()
            NouvelArticle("Designation") = ""
            NouvelArticle("CodeArticle") = ""
            dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Add(NouvelArticle)

            NouvelleEmprunt = dsEmprunt.Tables("EMPRUNT").NewRow()
            dsEmprunt.Tables("EMPRUNT").Rows.Add(NouvelleEmprunt)

            Me.gArticles.Splits(0).DisplayColumns(1).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(4).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(5).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(9).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(10).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(11).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(12).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(13).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(14).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(15).AllowFocus = False


            'Initialiser les Controls utilisés lors de l'opération de l'Emprunt
            initControlEmprunt()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "bAjouter_Click()", ex.Message, "0000054", "Erreur Lorsque taper btn Ajouter", True, True, True)

        End Try
    End Sub

    Private Sub gArticles_BeforeColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColEditEventArgs) Handles gArticles.BeforeColEdit
        If e.Column.Name = "Date péremption" Then 'gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then ' 
            Dim det As New C1.Win.C1Input.C1DateEdit
            det.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
            gArticles.Columns("DatePeremption").Editor = det
        End If
    End Sub

    Private Sub gArticles_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gArticles.Change

        Try
            If (gArticles.Columns(gArticles.Col).DataField() = "Designation" And gArticles.Columns("Designation").Value <> "") Or gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" Then
                Dim StrSQL1 As String = ""
                Dim I As Integer
                Dim NbLigne As Integer
                NbLigne = gArticles.RowCount

                With gListeRecherche
                    .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
                    .Top = Me.gArticles.Top + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight
                End With

                Try
                    dsEmprunt.Tables("ARTICLE").Clear()
                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Emprunt", "fEmprunt", "gArticles_Change", ex.Message, "0000050", "Erreur lors de vider la DS ARTICLE", True, True, True)


                End Try
                If gArticles.Row = dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1 And gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                    gListeRecherche.Visible = True
                Else
                    gListeRecherche.Visible = False
                End If

                'chargement des articles qui sont mis en jeu
                If gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                    If gArticles.Columns("Designation").Value.ToString.Length > 1 Then
                        If gArticles.Columns("Designation").Value.ToString.IndexOf(".") > 0 And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1)) Then    'gArticles.Columns("Designation").Value.ToString.Substring(gArticles.Columns("Designation").Value.ToString.Length - 1, 1) Like "." And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1))

                            StrSQL1 = "SELECT CodeArticle," + _
                                      "Designation," + _
                                      "LibelleForme," + _
                                      "PrixVenteTTC" + _
                                      " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                      " WHERE  " + _
                                      " and ltrim(str(PrixVenteTTC,10,3)) LIKE '" + _
                                      gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + "%' AND Supprime=0 ORDER BY PrixVenteTTC"
                        Else
                            StrSQL1 = "SELECT CodeArticle," + _
                                      "Designation," + _
                                      "LibelleForme," + _
                                      "PrixVenteTTC" + _
                                      " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                      " WHERE  " + _
                                      " Designation LIKE '" + gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + _
                                      "%' AND Supprime=0 ORDER BY Designation"
                        End If
                    Else
                        StrSQL1 = "SELECT CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC" + _
                                  " FROM ARTICLE lEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  " WHERE " + _
                                  " Designation LIKE '" + gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + _
                                  "%' AND Supprime=0  ORDER BY Designation"
                    End If
                    ElseIf gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" Then
                        StrSQL1 = "SELECT CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC" + _
                                  " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  " WHERE " + _
                                  "CodeABarre LIKE '" + gArticles.Columns("CodeArticle").Value + _
                                  "' AND Supprime=0 ORDER BY Designation"

                    End If
                cmdEmprunt.Connection = ConnectionServeur
                cmdEmprunt.CommandText = StrSQL1
                daEmprunt = New SqlDataAdapter(cmdEmprunt)
                daEmprunt.Fill(dsEmprunt, "ARTICLE")

                If dsEmprunt.Tables("ARTICLE").Rows.Count > 0 Then
                    dr = dsEmprunt.Tables("ARTICLE").Rows(0)
                End If

                With gListeRecherche
                    .Columns.Clear()
                    .DataSource = dsEmprunt
                    .DataMember = "ARTICLE"
                    .Rebind(False)
                    .Columns("CodeArticle").Caption = "Code Article"
                    .Columns("Designation").Caption = "Designation"
                    .Columns("LibelleForme").Caption = "Forme"
                    .Columns("PrixVenteTTC").Caption = "Prix de vente"

                    ' Centrer tous les entêtes
                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    Next
                    ' CentreR tous les valeurs
                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    Next

                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).Visible = False
                    Next

                    .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                    .Splits(0).DisplayColumns("CodeArticle").Visible = False
                    .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
                    .Splits(0).DisplayColumns("Designation").Visible = True
                    .Splits(0).DisplayColumns("LibelleForme").Visible = True
                    .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

                    .Splits(0).DisplayColumns("CodeArticle").Width = 0
                    .Splits(0).DisplayColumns("Designation").Width = 260
                    .Splits(0).DisplayColumns("LibelleForme").Width = 100
                    .Splits(0).DisplayColumns("PrixVenteTTC").Width = 90


                    '.Splits(0).DisplayColumns("Designation").Style.BackColor = Color.Aqua
                    '.Splits(0).DisplayColumns("LibelleForme").Style.BackColor = Color.Aqua
                    '.Splits(0).DisplayColumns("PrixVenteTTC").Style.BackColor = Color.Aqua

                    .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                    .Splits(0).ColumnCaptionHeight = 20
                    .Splits(0).RecordSelectors = False
                    .ExtendRightColumn = True
                    .EmptyRows = True
                    .FetchRowStyles = True
                End With

                Dim Col As New C1.Win.C1TrueDBGrid.C1DataColumn
                Dim dc As C1.Win.C1TrueDBGrid.C1DisplayColumn

                With gListeRecherche
                    .Columns.Insert(0, Col)
                    Col.Caption = "Stock"
                    dc = .Splits(0).DisplayColumns.Item("Stock")
                    dc.Width = 40
                    .Splits(0).DisplayColumns(4).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    .Splits(0).DisplayColumns(4).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(210, 255, 230)
                    dc.Visible = True
                    .Rebind(True)
                End With

            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "gArticles_Change()", ex.Message, "0000055", "Erreur dans la Procédure gArticles_Change()", True, True, True)

        End Try

    End Sub
    Public Function RecupereNumero()

        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer
        Try


            StrSQL = " SELECT max([NumeroEmprunt]) FROM EMPRUNT"
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQL
            Try
                ValeurActuel = cmdRecupereNum.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
            If ValeurActuel.Length > 4 Then
                If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                    Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                    Numero = Numero + 1
                    numeroConvertit = Numero.ToString
                    For i = 0 To 5 - numeroConvertit.Length
                        numeroConvertit = "0" + numeroConvertit
                    Next
                    ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
                Else
                    ValeurRetour = Year(Today).ToString + "/" + "000001"
                End If
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "RecupereNumero()", ex.Message, "0000056", "Erreur dans la Fonction RecupereNumero()", True, True, True)

        End Try

        Return ValeurRetour

    End Function

    Private Sub gArticles_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyUp

        Try
            Dim i As Integer = 0
            Dim StrSQL As String = ""
            Dim cmd As New SqlCommand
            Dim QuantiteAAjouter As Integer = 0

            '---------------------------------- test si on est en mode saisi ou non ---------------------------
            If mode <> "Ajout" And mode <> "Modif" Then
                Exit Sub
            End If
            '---------------------------------- suppression du dernier ligne vide si on a deux lignes au mm temps vide
            '---------------------------------- cas ou on supprime dernier ligne
            If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1 = 1 And gArticles(0, "CodeArticle") = "" And gArticles(1, "CodeArticle") = "" Then
                gArticles.MoveLast()
                gArticles.MovePrevious()
                gArticles.Delete()
            End If
            '---------------------------------- test du type de la valeur d'entrée dans la colonne quantité (numéric) ------------
            '---------------------------------- test du  valeur d'entrée dans la colonne quantité < 99999 ------------

            If gArticles.Columns(gArticles.Col).DataField() = "Qte" Then

                If gArticles.Columns("Qte").Value.ToString <> "-" Then   ' pour les quantité négatives
                    If IsNumeric(gArticles.Columns("Qte").Value) = False Then
                        gArticles.Columns("Qte").Value = "1"
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                        gArticles.EditActive = True
                        Exit Sub
                    End If

                    If (gArticles.Columns("Qte").Value = 0) And e.KeyCode = Keys.Enter Then
                        gArticles.Columns("Qte").Value = ""
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                        gArticles.EditActive = True
                        Exit Sub
                    End If

                    If (gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999) And e.KeyCode = Keys.Enter Then
                        gArticles.Columns("Qte").Value = "1"
                        MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                        gArticles.EditActive = True
                        Exit Sub
                    End If

                End If

            End If


            '********************************** Contrôles des numéros de lot et des dates de péremption ************
            '' ''Try
            '' ''    '-------- afféctation du num de lot pour la date de péremption si elle existe deja DANS LA BASE ------
            '' ''    If e.KeyCode = Keys.Enter And gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then
            '' ''        If gArticles.Columns("DatePeremption").Value.ToString <> "" Then

            '' ''            StrSQL = " SELECT count(NumeroLotArticle) FROM [LOT_ARTICLE] WHERE DatePeremptionArticle='" + _
            '' ''            gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
            '' ''            gArticles.Columns("CodeArticle").Value + "'"
            '' ''            cmd.Connection = ConnectionServeur
            '' ''            cmd.CommandText = StrSQL

            '' ''            If cmd.ExecuteScalar <> 0 Then

            '' ''                StrSQL = " SELECT NumeroLotArticle FROM [LOT_ARTICLE] WHERE DatePeremptionArticle='" + _
            '' ''                         gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
            '' ''                         gArticles.Columns("CodeArticle").Value + "'"
            '' ''                cmd.Connection = ConnectionServeur
            '' ''                cmd.CommandText = StrSQL
            '' ''                gArticles.Columns("NumeroLotArticle").Value = cmd.ExecuteScalar
            '' ''            Else
            '' ''                gArticles.Columns("NumeroLotArticle").Value = ""
            '' ''            End If
            '' ''        End If
            '' ''    End If
            '' ''Catch ex As Exception

            '' ''    'Gérer l'Exception
            '' ''    fMessageException.Show("Emprunt", "fEmprunt", "gArticles_KeyUp()", ex.Message, "0000093", "Contrôles des numéros de lot et des dates de péremption", True, True, True)
            '' ''End Try
            ' '' ''-------- afféctation du num de lot pour la date de péremption si elle existe deja DANS LA LISTE -----
            '' ''If gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            '' ''    Dim CodeNewArticle As String = ""
            '' ''    Dim DateNewArticle As Date

            '' ''    CodeNewArticle = gArticles.Columns("CodeArticle").Value
            '' ''    DateNewArticle = gArticles.Columns("DatePeremption").Value

            '' ''    If gArticles.Columns("NumeroLotArticle").Value = "" Then
            '' ''        i = 0
            '' ''        Do While i < gArticles.RowCount - 1
            '' ''            If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '' ''                If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") = DateNewArticle Then
            '' ''                    gArticles.Columns("NumeroLotArticle").Value = gArticles(i, "NumeroLotArticle")
            '' ''                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '' ''                    gArticles.EditActive = True
            '' ''                End If
            '' ''            End If
            '' ''            i = i + 1
            '' ''        Loop
            '' ''    End If
            '' ''End If
            ' '' ''--------------------------- test de l'existance d'un ancien lot avec cette date de péremption -----
            '' ''Try
            '' ''    If e.KeyCode = Keys.Enter And gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then
            '' ''        If gArticles.Columns("DatePeremption").Value.ToString <> "" And gArticles.Columns("NumeroLotArticle").Value.ToString <> "" Then
            '' ''            StrSQL = " SELECT count(NumeroLotArticle) FROM [LOT_ARTICLE]WHERE DatePeremptionArticle='" + _
            '' ''                     gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
            '' ''                     gArticles.Columns("CodeArticle").Value + "'"
            '' ''            cmd.Connection = ConnectionServeur
            '' ''            cmd.CommandText = StrSQL

            '' ''            If cmd.ExecuteScalar <> 0 Then
            '' ''                StrSQL = " SELECT NumeroLotArticle FROM [LOT_ARTICLE]WHERE DatePeremptionArticle='" + _
            '' ''                         gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
            '' ''                         gArticles.Columns("CodeArticle").Value + "'"
            '' ''                cmd.Connection = ConnectionServeur
            '' ''                cmd.CommandText = StrSQL

            '' ''                If gArticles.Columns("NumeroLotArticle").Value <> cmd.ExecuteScalar Then
            '' ''                    MsgBox("Date de péremption existe pour un autre lot !", MsgBoxStyle.Critical, "Erreur")
            '' ''                    gArticles.Columns("DatePeremption").Value = ""
            '' ''                    gArticles.Columns("NumeroLotArticle").Value = ""
            '' ''                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '' ''                    gArticles.EditActive = False
            '' ''                    Exit Sub
            '' ''                End If
            '' ''            End If
            '' ''        End If
            '' ''    End If

            '' ''Catch ex As Exception

            '' ''    'Gérer l'Exception
            '' ''    fMessageException.Show("Emprunt", "fEmprunt", "gArticles_KeyUp()", ex.Message, "0000092", "test de l'existance d'un ancien lot avec cette date de péremption", True, True, True)

            '' ''End Try
            ' '' ''---------------------------------- test de l'existance du numero de lot pour une autre date --------
            '' ''Try
            '' ''    If (gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle") And e.KeyCode = Keys.Enter Then
            '' ''        StrSQL = " SELECT COUNT(NumeroLotArticle) FROM [LOT_ARTICLE] WHERE NumeroLotArticle='" + _
            '' ''            gArticles.Columns("NumeroLotArticle").Value.ToString + "' AND CodeArticle='" + _
            '' ''            gArticles.Columns("CodeArticle").Value + "' AND DatePeremptionArticle <> '" + _
            '' ''            gArticles.Columns("DatePeremption").Value + "'"
            '' ''        cmd.Connection = ConnectionServeur
            '' ''        cmd.CommandText = StrSQL

            '' ''        If cmd.ExecuteScalar() <> 0 Then
            '' ''            MsgBox("Numero de lot existe déja !", MsgBoxStyle.Critical, "Erreur")
            '' ''            gArticles.Columns("NumeroLotArticle").Value = ""
            '' ''            gArticles.Columns("DatePeremption").Value = ""
            '' ''            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '' ''            gArticles.EditActive = False
            '' ''            Exit Sub
            '' ''        End If
            '' ''    End If
            '' ''Catch ex As Exception

            '' ''    'Gérer l'Exception
            '' ''    fMessageException.Show("Emprunt", "fEmprunt", "gArticles_KeyUp()", ex.Message, "0000091", "test de l'existance du numero de lot pour une autre date", True, True, True)

            '' ''End Try
            ' '' ''--------------- test si le mm numero du lot existe dans la liste au dessus pour le mm article mais 
            ' '' ''--------------- avec une date de péremption differente et vise versa--------
            '' ''If (gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle") And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            '' ''    Dim CodeNewArticle As String = ""
            '' ''    Dim DateNewArticle As Date
            '' ''    Dim NumeroLotNewArticle As String = ""

            '' ''    CodeNewArticle = gArticles.Columns("CodeArticle").Value
            '' ''    DateNewArticle = gArticles.Columns("DatePeremption").Value
            '' ''    NumeroLotNewArticle = gArticles.Columns("NumeroLotArticle").Value
            '' ''    Dim QteNewArticle As Integer = 0
            '' ''    '-------------------------- mm code mm numero de lot mais date different
            '' ''    i = 0
            '' ''    Do While i < gArticles.RowCount - 1
            '' ''        If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '' ''            If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") <> DateNewArticle And gArticles(i, "NumeroLotArticle") = NumeroLotNewArticle Then
            '' ''                MsgBox("Numero de lot existe dans la liste pour le mm article mais avec une autre date de péremption!", MsgBoxStyle.Critical, "Erreur")
            '' ''                gArticles.Columns("NumeroLotArticle").Value = ""
            '' ''                gArticles.Columns("DatePeremption").Value = ""
            '' ''                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '' ''                gArticles.EditActive = False
            '' ''                Exit Sub
            '' ''            End If
            '' ''        End If
            '' ''        i = i + 1
            '' ''    Loop
            '' ''    '-------------------------- mm code mm date de lot mais numero different
            '' ''    i = 0
            '' ''    Do While i < gArticles.RowCount - 1
            '' ''        If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '' ''            If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") = DateNewArticle And gArticles(i, "NumeroLotArticle") <> NumeroLotNewArticle Then
            '' ''                MsgBox("Date de péremption existe dans la liste pour le mm article mais avec un autre numéro de lot!", MsgBoxStyle.Critical, "Erreur")
            '' ''                gArticles.Columns("DatePeremption").Value = ""
            '' ''                gArticles.Columns("NumeroLotArticle").Value = ""
            '' ''                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '' ''                gArticles.EditActive = False
            '' ''                Exit Sub
            '' ''            End If
            '' ''        End If
            '' ''        i = i + 1
            '' ''    Loop
            '' ''End If

            ' '' ''--------------- test de l'existance du mm article avec la mm date au dessus dans la 
            ' '' ''--------------- liste (cas ou on a une date non null)

            '' ''If (gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Or gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle") And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            '' ''    Dim CodeNewArticle As String = ""
            '' ''    Dim DateNewArticle As Date
            '' ''    Dim NumeroLotNewArticle As String = ""
            '' ''    Dim QteNewArticle As Integer = 0

            '' ''    CodeNewArticle = gArticles.Columns("CodeArticle").Value
            '' ''    DateNewArticle = gArticles.Columns("DatePeremption").Value
            '' ''    NumeroLotNewArticle = gArticles.Columns("NumeroLotArticle").Value
            '' ''    QteNewArticle = gArticles.Columns("Qte").Value

            '' ''    i = 0
            '' ''    Do While i < gArticles.RowCount - 1
            '' ''        If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '' ''            If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") = DateNewArticle And gArticles(i, "NumeroLotArticle") = NumeroLotNewArticle And i <> gArticles.Row Then
            '' ''                gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QteNewArticle).ToString
            '' ''                gArticles.MoveLast()
            '' ''                gArticles.Delete()
            '' ''                If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
            '' ''                    NouvelArticle = dsEmprunt.Tables("EMPRUNT_DETAILS").NewRow()
            '' ''                    NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
            '' ''                    NouvelArticle("CodeArticle") = ""
            '' ''                    NouvelArticle("CodeABarre") = ""
            '' ''                    dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Add(NouvelArticle)
            '' ''                End If
            '' ''            End If
            '' ''        End If
            '' ''        i = i + 1
            '' ''    Loop
            '' ''End If

            ' '' ''--------------- test de l'existance du mm article sans date aussi au dessus dans la 
            ' '' ''--------------- liste (cas ou on a une date null)

            '' ''If (gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Or gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle") And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = True Then
            '' ''    Dim CodeNewArticle As String = ""
            '' ''    Dim QteNewArticle As Integer = 0

            '' ''    CodeNewArticle = gArticles.Columns("CodeArticle").Value
            '' ''    QteNewArticle = gArticles.Columns("Qte").Value

            '' ''    i = 0
            '' ''    Do While i < gArticles.RowCount - 1
            '' ''        If IsDBNull(gArticles(i, "DatePeremption")) = True And gArticles(i, "NumeroLotArticle").ToString = "" Then
            '' ''            If gArticles(i, "CodeArticle") = CodeNewArticle And i <> gArticles.Row Then
            '' ''                gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QteNewArticle).ToString
            '' ''                gArticles.MoveLast()
            '' ''                gArticles.Delete()
            '' ''                If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
            '' ''                    NouvelArticle = dsEmprunt.Tables("EMPRUNT_DETAILS").NewRow()
            '' ''                    NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
            '' ''                    NouvelArticle("CodeArticle") = ""
            '' ''                    NouvelArticle("CodeABarre") = ""
            '' ''                    dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Add(NouvelArticle)

            '' ''                End If
            '' ''            End If
            '' ''        End If
            '' ''        i = i + 1
            '' ''    Loop
            '' ''End If
            '*******************************************************************************************************

            '------------------------------ recherche par code ----------------------------------------------
            If gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" And e.KeyCode = Keys.Enter And gArticles.Row = dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1 Then
                ChargerDetailArticle(gArticles.Columns("CodeABarre").Value.ToString())
                Exit Sub
            ElseIf gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" And e.KeyCode = Keys.Enter And gArticles.Row < dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1 Then
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
            End If
            '---------------------------------- masquer la liste de recherche si la designation est vide -----------
            If e.KeyCode = Keys.Back And gArticles.Columns("Designation").Value = "" And gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                gListeRecherche.Visible = False
            End If
            '---------------------------- pour passer à la navigation dans la petite liste de recherhce ----------------
            If (e.KeyCode = Keys.Down Or e.KeyCode = Keys.Up) And gListeRecherche.Visible = True Then
                gListeRecherche.Focus()
                gListeRecherche.Col = 2
                gListeRecherche.Row = 1
            End If
            '---------------------------- si l'artcile n'existe pas dans la liste de recherhce sa quantité doit etre 0  ----
            If dsEmprunt.Tables("ARTICLE").Rows.Count - 1 <= 0 And gListeRecherche.Visible = True Then '
                gArticles.Columns("Qte").Value = 0
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
            End If
            '---------------------------- calcul des montants --------------------------------------------------------
            If (gArticles.Columns(gArticles.Col).DataField() = "Qte") And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) Then
                CalculerMontants()
            End If

            '---------------------------------- verouillage des lignes déja confirmées -------------------------
            If (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) And gArticles.Row <> dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1 Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
                gArticles.Splits(0).DisplayColumns("Designation").Locked = True
            ElseIf gArticles.Row = dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
            End If

            '------------------------------ suppression d'une date de péremption
            If e.KeyCode = Keys.Delete And gArticles.Row <= dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count And gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then
                gArticles.EditActive = False
                gArticles.Columns("DatePeremption").Value = ""
            End If
            '------------------------------ suppression de numéro de lot
            If e.KeyCode = Keys.Delete And gArticles.Row <= dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count And gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle" Then
                gArticles.EditActive = False
                gArticles.Columns("NumeroLotArticle").Value = ""
                gArticles.EditActive = True
            End If

            '--------------------- traitement du clique sur le bouton ENTREE selon la colonne ---------------------------
            If e.KeyCode = Keys.Enter Then ' And (dsEmprunt.Tables("ARTICLE").Rows.Count > 0 Or gArticles.Row <> dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1) Then
                'gListeRecherche_KeyUp(sender, e)     ' pour charger les informations de l'article a partir du fiche article
                ChargerGride()
                If gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                    If gArticles.Columns("CodeArticle").Value = "" Then    ' si l'article choisit n'existe pas dans la liste de recherche
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
                        gArticles.Columns("Designation").Value = ""
                    Else
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                    End If

                ElseIf gArticles.Columns(gArticles.Col).DataField() = "Qte" And e.KeyCode = Keys.Enter Then
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
                ElseIf gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then
                    '' ''    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("NumeroLotArticle"))
                    '' ''ElseIf gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle" Then   ' si on est dans la colonne date de péremption on passe au nouveau ligne
                    If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                        NouvelArticle = dsEmprunt.Tables("EMPRUNT_DETAILS").NewRow()
                        NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                        NouvelArticle("CodeArticle") = ""
                        NouvelArticle("CodeABarre") = ""
                        dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Add(NouvelArticle)
                    End If
                    gArticles.MoveLast()
                    dsEmprunt.Tables("ARTICLE").Clear()
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
                End If
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "gArticles_KeyUp()", ex.Message, "0000057", "Erreur dans gArticles_KeyUp()", True, True, True)
        End Try
    End Sub

    Private Sub gListeRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListeRecherche.KeyUp

        Try
            ' dans cet évènenment on teste si l évènenment est due à un clique sur le boutton entrée et si l'utilisateur a choisi 
            ' un article et si le curseur est dans la colonne de designation 


            Dim StrSQL As String = ""
            Dim cmd As New SqlCommand
            Dim DatePeremption As Date
            Dim NumeroLot As String = ""

            If gListeRecherche.Visible = False Then
                Exit Sub
            End If
            If e.KeyCode = Keys.Back Then
                gArticles.Focus()
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
                gArticles.MoveLast()
                gArticles.EditActive = True
            End If

            Dim j As Integer
            Dim NumeroLigne As Integer
            Dim DataRowRecherche As DataRow
            If e.KeyCode = Keys.Enter And (gArticles.Columns(gArticles.Col).DataField() = "LibelleForme" Or gArticles.Columns(gArticles.Col).DataField() = "Designation") Then    'And gArticles.Columns("Designation").Value <> ""
                If dsEmprunt.Tables("ARTICLE").Rows.Count > 0 Then
                    '--------------------- rehcerche de l'index de la ligne qui contient l'article recherché ---------------
                    For j = 0 To dsEmprunt.Tables("ARTICLE").Rows.Count - 1
                        DataRowRecherche = dsEmprunt.Tables("ARTICLE").Rows(j)
                        If DataRowRecherche.Item("CodeArticle") = gListeRecherche.Columns("CodeArticle").Value Then
                            NumeroLigne = j
                        End If
                    Next

                    '------------------- chargement des données ---------------------------------------------- 
                    dr = dsEmprunt.Tables("ARTICLE").Rows(NumeroLigne)
                    NouvelArticle("NumeroEmprunt") = RecupereNumero()
                    NouvelArticle("CodeArticle") = dr.Item("CodeArticle")
                    NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    Try
                        NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    Catch ex As Exception
                    End Try

                    NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                    ''''
                    NouvelArticle("QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    If NouvelArticle("QuantiteUnitaire") = 0 Then NouvelArticle("QuantiteUnitaire") = 1
                    ''''

                    NouvelArticle("Qte") = NouvelArticle("QuantiteUnitaire") '1

                    NouvelArticle("Stock") = CalculeStock(dr.Item("CodeArticle"))

                    NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("TotalAchatHT") = Math.Round(NouvelArticle("PrixAchatHT") / NouvelArticle("QuantiteUnitaire"), 3)
                    NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("TotalAchatTTC") = Math.Round(NouvelArticle("PrixAchatTTC") / NouvelArticle("QuantiteUnitaire"), 3)
                    NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("TotalTVA") = NouvelArticle("TVA")
                    '----------------------- récupération de la date de péremption

                    StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] " + _
                             "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                             "' AND CodeArticle='" + dr.Item("CodeArticle") + _
                             "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        DatePeremption = cmd.ExecuteScalar()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                    If DatePeremption = #12:00:00 AM# Then
                        'NouvelArticle("DatePeremption") = "01/01/1900" 'System.DateTime.Today.Day.ToString + "/" + System.DateTime.Today.Month.ToString + "/" + System.DateTime.Today.Year.ToString  
                    Else
                        NouvelArticle("DatePeremption") = DatePeremption
                    End If

                    '' ''StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
                    '' ''     "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                    '' ''     "' AND CodeArticle='" + NouvelArticle("CodeArticle") + _
                    '' ''     "' Order by DatePeremptionArticle ASC "

                    '' ''cmd.Connection = ConnectionServeur
                    '' ''cmd.CommandText = StrSQL

                    '' ''Try
                    '' ''    NumeroLot = cmd.ExecuteScalar()
                    '' ''Catch ex As Exception
                    '' ''    Console.WriteLine(ex.Message)
                    '' ''End Try

                    '' ''If NumeroLot = "" Then
                    '' ''Else
                    '' ''    NouvelArticle("NumeroLotArticle") = NumeroLot
                    '' ''End If

                    gArticles.Refresh()
                End If
                gListeRecherche.Visible = False
                gArticles.Focus()
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "gListeRecherche_KeyUp()", ex.Message, "0000058", "Erreur dans gListeRecherche_KeyUp()", True, True, True)

        End Try
    End Sub

    Public Sub ChargerGride()
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date
        Dim NumeroLot As String = ""

        If gListeRecherche.Visible = False Then
            Exit Sub
        End If
   

        Dim j As Integer
        Dim NumeroLigne As Integer
        Dim DataRowRecherche As DataRow
        'And gArticles.Columns("Designation").Value <> ""
        If dsEmprunt.Tables("ARTICLE").Rows.Count > 0 Then
            '--------------------- rehcerche de l'index de la ligne qui contient l'article recherché ---------------
            For j = 0 To dsEmprunt.Tables("ARTICLE").Rows.Count - 1
                DataRowRecherche = dsEmprunt.Tables("ARTICLE").Rows(j)
                If DataRowRecherche.Item("CodeArticle") = gListeRecherche.Columns("CodeArticle").Value Then
                    NumeroLigne = j
                End If
            Next

            '------------------- chargement des données ---------------------------------------------- 
            dr = dsEmprunt.Tables("ARTICLE").Rows(NumeroLigne)
            NouvelArticle("NumeroEmprunt") = RecupereNumero()
            NouvelArticle("CodeArticle") = dr.Item("CodeArticle")
            NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            Try
                NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            Catch ex As Exception
            End Try

            NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

            ''''
            NouvelArticle("QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            If NouvelArticle("QuantiteUnitaire") = 0 Then NouvelArticle("QuantiteUnitaire") = 1
            ''''

            NouvelArticle("Qte") = NouvelArticle("QuantiteUnitaire") '1

            NouvelArticle("Stock") = CalculeStock(dr.Item("CodeArticle"))

            NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("TotalAchatHT") = Math.Round(NouvelArticle("PrixAchatHT") / NouvelArticle("QuantiteUnitaire"), 3)
            NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("TotalAchatTTC") = Math.Round(NouvelArticle("PrixAchatTTC") / NouvelArticle("QuantiteUnitaire"), 3)
            NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("TotalTVA") = NouvelArticle("TVA")
            '----------------------- récupération de la date de péremption

            StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] " + _
                     "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                     "' AND CodeArticle='" + dr.Item("CodeArticle") + _
                     "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                DatePeremption = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If DatePeremption = #12:00:00 AM# Then
                'NouvelArticle("DatePeremption") = "01/01/1900" 'System.DateTime.Today.Day.ToString + "/" + System.DateTime.Today.Month.ToString + "/" + System.DateTime.Today.Year.ToString  
            Else
                NouvelArticle("DatePeremption") = DatePeremption
            End If

            '' ''StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
            '' ''     "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
            '' ''     "' AND CodeArticle='" + NouvelArticle("CodeArticle") + _
            '' ''     "' Order by DatePeremptionArticle ASC "

            '' ''cmd.Connection = ConnectionServeur
            '' ''cmd.CommandText = StrSQL

            '' ''Try
            '' ''    NumeroLot = cmd.ExecuteScalar()
            '' ''Catch ex As Exception
            '' ''    Console.WriteLine(ex.Message)
            '' ''End Try

            '' ''If NumeroLot = "" Then
            '' ''Else
            '' ''    NouvelArticle("NumeroLotArticle") = NumeroLot
            '' ''End If

            gArticles.Refresh()
        End If
        gListeRecherche.Visible = False
        gArticles.Focus()

    End Sub

    Public Sub ChargerDetailArticle(ByVal CodeABarre As String)
        Try
            Dim resultat As String
            Dim cmd As New SqlCommand
            Dim DatePeremption As Date
            Dim NumeroLot As String = ""
            Dim CodeArticle As String = ""
            Dim Supprime As String = ""
            Dim QteUnitaireArticle As Integer = 0
            Dim CategorieArticle As Integer = 0
            Dim PreparationArticle As Integer = 0
            Dim CodeArticleMereFractionnement As Integer = 0
            Dim QteUnitaireArticleMere As Integer = 0
            Dim StockArticleMere As Integer = 0

            CodeArticle = RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "CodeABarre", CodeABarre)
            resultat = RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle)
            Supprime = RecupererValeurExecuteScalaire("Supprime", "ARTICLE", "CodeArticle", CodeArticle)

            If resultat <> "" And Supprime = "False" Then

                NouvelArticle("NumeroEmprunt") = RecupereNumero()
                NouvelArticle("CodeArticle") = CodeArticle
                NouvelArticle("CodeABarre") = CodeABarre
                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)

                Try
                    NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                Catch ex As Exception
                End Try

                NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", CodeArticle)
                If CategorieArticle = 9 Then
                    PreparationArticle = RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", CodeArticle)
                End If
                If CategorieArticle = 8 Or (CategorieArticle = 9 And PreparationArticle = 1) Then
                    QteUnitaireArticle = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticle)
                Else
                    QteUnitaireArticle = 1
                End If

                If CategorieArticle = 9 And PreparationArticle = 4 Then
                    Try
                        CodeArticleMereFractionnement = RecupererValeurExecuteScalaire("CodeArticleMere", "FRACTIONNEMENT", "CodeArticleFractionne", CodeArticle)
                        QteUnitaireArticleMere = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticleMereFractionnement)
                        StockArticleMere = CalculeStock(CodeArticleMereFractionnement) * QteUnitaireArticleMere
                    Catch ex As Exception
                    End Try
                End If

                ' ''''
                'NouvelArticle("QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", CodeArticle)
                'If NouvelArticle("QuantiteUnitaire") = 0 Then NouvelArticle("QuantiteUnitaire") = 1
                ' ''''

                NouvelArticle("Qte") = QteUnitaireArticle
                NouvelArticle("QuantiteUnitaire") = QteUnitaireArticle
                NouvelArticle("Stock") = CalculeStock(CodeArticle)

                NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("TotalAchatHT") = Math.Round(NouvelArticle("PrixAchatHT") / NouvelArticle("QuantiteUnitaire"), 3)
                NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("TotalAchatTTC") = Math.Round(NouvelArticle("PrixAchatTTC") / NouvelArticle("QuantiteUnitaire"), 3)
                NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("TotalTVA") = NouvelArticle("TVA")

                '----------------------- récupération de la date de péremption
                StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] " + _
                         "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                         "' AND CodeArticle='" + CodeArticle + _
                         "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    DatePeremption = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If DatePeremption = #12:00:00 AM# Then
                    ' NouvelArticle("DatePeremption") = "01/01/1900"
                Else
                    NouvelArticle("DatePeremption") = DatePeremption
                End If

                StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
                        "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                        "' AND CodeArticle='" + CodeArticle + _
                        "' Order by DatePeremptionArticle ASC "

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    NumeroLot = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If NumeroLot = "" Then
                Else
                    NouvelArticle("NumeroLotArticle") = NumeroLot
                End If

                gArticles.Refresh()
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
            Else
                gArticles.Columns("CodeABarre").Value = ""
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "ChargerDetailArticle()", ex.Message, "0000059", "Erreur dans ChargerDetailArticle()", True, True, True)
        End Try
    End Sub

    Public Sub CalculerMontants()

        Dim i As Integer = 0

        Dim QteUnitaireArticle As Integer = 1
        Dim CategorieArticle As Integer = 0
        Dim PreparationArticle As Integer = 0

        TotalTTCAchat = 0.0
        TotalHTAchat = 0.0
        TotalTVA = 0.0
        Try

            Do While i < gArticles.RowCount

                If gArticles(i, "Designation") <> "" Then
                    gArticles(i, "TotalAchatHT") = Math.Round(gArticles(i, "PrixAchatHT") * gArticles(i, "Qte"), 3)
                    gArticles(i, "TotalAchatTTC") = Math.Round(gArticles(i, "PrixAchatTTC") * gArticles(i, "Qte"), 3)
                    gArticles(i, "TotalTVA") = Math.Round((gArticles(i, "TotalAchatHT") / 100) * (gArticles(i, "TVA")), 3)

                    CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))
                    If CategorieArticle = 9 Then
                        If RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle")) <> "" Then
                            PreparationArticle = RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))
                        End If
                    End If

                    If CategorieArticle = 8 Or (CategorieArticle = 9 And PreparationArticle = 1) Then
                        QteUnitaireArticle = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))
                    Else
                        QteUnitaireArticle = 1
                    End If

                    ' gArticles(i, "TotalTTC") = Math.Round(gArticles(i, "TotalTTC") / QteUnitaireArticle, 3)
                    gArticles(i, "TotalAchatHT") = Math.Round(gArticles(i, "TotalAchatHT") / QteUnitaireArticle, 3)
                    gArticles(i, "TotalAchatTTC") = Math.Round(gArticles(i, "TotalAchatTTC") / QteUnitaireArticle, 3)
                    gArticles(i, "TotalTVA") = Math.Round(gArticles(i, "TotalTVA") / QteUnitaireArticle, 3)

                    TotalTTCAchat = TotalTTCAchat + gArticles(i, "TotalAchatTTC")
                    TotalHTAchat = TotalHTAchat + gArticles(i, "TotalAchatHT")
                    TotalTVA = TotalTVA + gArticles(i, "TotalTVA")
                End If
                i = i + 1
            Loop


            lTotalHT.Text = TotalTTCAchat
            lTotalTTC.Text = TotalHTAchat
            lTotalTVA.Text = TotalTVA

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "CalculerMontants()", ex.Message, "0000060", "Erreur dans CalculerMontants()", True, True, True)

        End Try

    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click

        Try

            'Suivi du scénario 
            fMessageException.Show("Emprunt", "fEmprunt", "bConfirmer_Click", "NoException", "NoError", "Clic sur le bouton Confirmer", False, True, False)

            Dim I As Integer = 0
            Dim cmd As New SqlCommand
            Dim NumeroLot As String = "RIEN"
            Dim TestNumeroLot As String = ""
            Dim NouveauNumeroLot As String = ""
            Dim QuantiteLotSansNumero As Integer = 0
            Dim QuantiteLotAInsere As Integer = 0
            Dim StrSQL As String = ""
            Dim ConfirmerEnregistrer As Boolean = False
            Dim CodeOperateur As String = ""


            'Mode Modif
            If mode = "Modif" Then
                If gArticles.RowCount - 1 = 0 And gArticles(0, "CodeArticle") = "" Then
                    If MsgBox("La liste des détails est vide, Voulez-vous supprimer l'Emprunt N° : " + lNumeroEmprunt.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Enregistrer") = MsgBoxResult.Yes Then
                        'Pour appeler le Bouton Annuler
                        bAnnuler.PerformClick()
                        'Pour appeler le bouton de Suppression
                        supprimerEmprunt(True)
                        Exit Sub
                    Else
                        'Pour appeler le Bouton Annuler
                        bAnnuler.PerformClick()
                        'Quitter la procedure apres faire annuler
                        Exit Sub
                    End If
                End If
            Else
                'Mode Ajout
                If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle") = "" Then
                    MsgBox("Entree Vide !", MsgBoxStyle.Critical, "Erreur")
                    If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1 < 0 Then
                        bAjouter_Click(sender, e)
                    End If
                    '----------------------
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
                    gArticles.EditActive = True
                    Exit Sub
                End If
            End If
            '-------------------------pour verifier le calcul : si l'utilisateur ne clique pas entree
            '-------------------------sur la cellule qte du dernier ligne la somme TTC sera fausse
            CalculerMontants()
            '-----------------------------------------------------------------------

            If cmbpharmacie.Text = "" Then
                MsgBox("Veuillez choisir une pharmacie !", MsgBoxStyle.Critical, "Erreur")
                cmbpharmacie.Focus()
                Exit Sub
            End If


            '-------------------------- élémination des lignes vides 
            I = 0
            Do While I < dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count
                If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then
                    If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                        dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Delete()
                    End If
                End If
                I = I + 1
            Loop
            CalculerMontants()

            '----------------------- controle des Numeros des lots, codes articles : insertion des doublons
            '----------------------- (Violation du clé primaire dans la table Emprunt details)

            'Dim p As Integer = 0
            'Dim q As Integer = 0
            'For p = 0 To dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1
            '    For q = p To dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1
            '        If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(p).RowState <> DataRowState.Deleted Then
            '            If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(p).Item("CodeArticle") = dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(q).Item("CodeArticle") And p <> q Then
            '                If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(p).Item("NumeroLotArticle") = dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(q).Item("NumeroLotArticle") Then
            '                    MsgBox("l'article " + dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(p).Item("Designation") + " apparaît deux fois avec le même numero de lot ! veuillez supprimer un et ajouter sa quantité dans l'autre", MsgBoxStyle.Critical, "Erreur")
            '                    Exit Sub
            '                End If
            '            End If
            '        End If
            '    Next
            'Next

            Dim p As Integer = 0
            Dim q As Integer = 0
            For p = 0 To dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1
                For q = p To dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1
                    If gArticles(p, "CodeArticle") = gArticles(q, "CodeArticle") And p <> q And gArticles(p, "CodeArticle") <> "" And gArticles(q, "CodeArticle") <> "" Then
                        If gArticles(p, "DatePeremption").ToString = gArticles(q, "DatePeremption").ToString Then
                            MsgBox("l'article " + gArticles(p, "Designation") + " apparaît deux fois avec le même numero de lot ! veuillez supprimer un et ajouter sa quantité dans l'autre", MsgBoxStyle.Critical, "Erreur")
                            Exit Sub
                        End If
                    End If
                Next
            Next

            '----------------------- contrôle des dates de péremption si il y a un qui est périmé

            For p = 0 To dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1
                If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(p).RowState <> DataRowState.Deleted Then
                    If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(p).Item("DatePeremption").ToString <> "" Then
                        If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(p).Item("DatePeremption") < Date.Today Then
                            MsgBox("l'article " + dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(p).Item("Designation") + " admet une date de péremption deja dépassé ! veuillez corriger sa date de péremption", MsgBoxStyle.Critical, "Erreur")
                            Exit Sub
                        End If
                    End If
                End If
            Next


            '------------------------------ demande du mot de passe
            Dim myMotDePasse As New fMotDePasse
            myMotDePasse.ShowDialog()

            ConfirmerEnregistrer = fMotDePasse.Confirmer
            CodeOperateur = fMotDePasse.CodeOperateur

            myMotDePasse.Dispose()
            myMotDePasse.Close()

            If ConfirmerEnregistrer = False Then
                Exit Sub
            End If

            '------------------------------ enregistrement de l'entête de l'Emprunt -------------------------

            '----------------------------------------

            If mode = "Ajout" Then
                NumeroEmprunt = RecupereNumero()
            End If

            If mode = "Modif" Then
                With dsEmprunt
                    .Tables("EMPRUNT").Rows(0)("NumeroEmprunt") = lNumeroEmprunt.Text
                    .Tables("EMPRUNT").Rows(0)("CodePharmacie") = cmbpharmacie.SelectedValue
                    .Tables("EMPRUNT").Rows(0)("CodePersonnel") = CodeOperateur
                    .Tables("EMPRUNT").Rows(0)("Date") = lDateEmprunt.Text
                    .Tables("EMPRUNT").Rows(0)("TotalHT") = TotalHTAchat
                    .Tables("EMPRUNT").Rows(0)("TotalTVA") = TotalTVA
                    .Tables("EMPRUNT").Rows(0)("TotalTTC") = TotalTTCAchat
                    .Tables("EMPRUNT").Rows(0)("Vider") = 0

                End With
                Try
                    daEmpruntEntete.Update(dsEmprunt, "EMPRUNT")
                Catch ex As Exception
                    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            Else ' Mode = "Ajout"
                dsEmprunt.Tables("EMPRUNT").Clear()

                StrSQL = "SELECT top(0) * FROM EMPRUNT ORDER BY NumeroEmprunt ASC"
                cmdEmprunt.Connection = ConnectionServeur
                cmdEmprunt.CommandText = StrSQL
                daEmprunt = New SqlDataAdapter(cmdEmprunt)
                daEmprunt.Fill(dsEmprunt, "EMPRUNT")
                cbEmprunt = New SqlCommandBuilder(daEmprunt)
                dr = dsEmprunt.Tables("EMPRUNT").NewRow()

                With dsEmprunt
                    dr.Item("NumeroEmprunt") = NumeroEmprunt
                    dr.Item("Date") = System.DateTime.Now
                    dr.Item("CodePharmacie") = cmbpharmacie.SelectedValue
                    dr.Item("TotalHT") = lTotalTTC.Text
                    dr.Item("TotalTTC") = lTotalHT.Text
                    dr.Item("TotalTVA") = lTotalTVA.Text
                    dr.Item("CodePersonnel") = CodeOperateur
                    dsEmprunt.Tables("EMPRUNT").Rows.Add(dr)
                End With
                Try
                    daEmpruntEntete.Update(dsEmprunt, "EMPRUNT")
                Catch ex As Exception
                    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                    'dsEmprunt.Reset()
                End Try
            End If
            ''------------------------------ enregistrement des détails de l'Emprunt -------------------------

            I = 0
            Do While I < dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count
                If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then
                    If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                        dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Delete()
                    Else
                        If mode = "Modif" Then
                            dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("NumeroEmprunt") = lNumeroEmprunt.Text
                        Else
                            dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("NumeroEmprunt") = NumeroEmprunt
                        End If
                    End If
                End If
                I = I + 1
            Loop

            ''-----------------------------------------------------------------------------------------------

            'cmdEmprunt.Connection = ConnectionServeur
            'cmdEmprunt.CommandText = "Select top(0) * FROM EMPRUNT_DETAILS"
            'daEmprunt = New SqlDataAdapter(cmdEmprunt)
            'daEmprunt.Fill(dsEmprunt, "EMPRUNT_DETAILS")
            'cbEmprunt = New SqlCommandBuilder(daEmprunt)



            cmd.Connection = ConnectionServeur

            For I = 0 To dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1
                If Not (dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).RowState = DataRowState.Deleted) Then

                    '''''''''''''''''''''''''''''''''''''''''''''''''''''''
                    If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("CodeArticle").ToString() <> "" And dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("DatePeremption").ToString() <> "" Then

                        Dim t() As String
                        Dim dateperm As String = dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("DatePeremption").ToString

                        Try
                            t = dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("DatePeremption").ToString.Split("/")
                            dateperm = t(0) + "/" + _
                                                    t(1) + "/" + _
                                                    Year(dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("DatePeremption")).ToString


                            If t(2).Substring(5) <> "00:00:00" Then
                                dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("DatePeremption") = CType((dateperm + " 00:00:00"), Date)
                            End If
                        Catch ex As Exception
                        End Try

                        StrSQL = " SELECT NumeroLotArticle FROM [LOT_ARTICLE] WHERE CodeArticle =" + _
                                 Quote(dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("CodeArticle")) + " AND DatePeremptionArticle = " + Quote(dateperm)
                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL

                        Try '''''''''''''''*''''''''''''''''''''
                            NouveauNumeroLot = cmd.ExecuteScalar().ToString
                            dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("NumeroLotArticle") = NouveauNumeroLot
                        Catch ex As Exception
                            NouveauNumeroLot = ""
                            Console.WriteLine(ex.Message)
                        End Try

                        If NouveauNumeroLot = "" Then
                            '------------------ recupération du dernier numéro de lot pour cet article puis incrémentation
                            StrSQL = " SELECT MAX(case when (isnumeric(NumeroLotArticle)=1) then NumeroLotArticle Else 0 END) FROM [LOT_ARTICLE] WHERE CodeArticle ='" + _
                                     dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("CodeArticle") + "'"
                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try '''''''''''''''*''''''''''''''''''''
                                NouveauNumeroLot = cmd.ExecuteScalar().ToString
                                If NouveauNumeroLot <> "" Then
                                    NouveauNumeroLot = (Convert.ToInt32(NouveauNumeroLot) + 1).ToString
                                Else
                                    NouveauNumeroLot = 1
                                End If
                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try
                            dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("NumeroLotArticle") = NouveauNumeroLot
                        End If
                    End If
                    '''''''''''''''''''''''''''''''''''''''''''''''''''''''



                    If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("NumeroLotArticle").ToString <> "" Then
                        StrSQL = " IF NOT EXISTS " + _
                                 " (SELECT * FROM LOT_ARTICLE WHERE CodeArticle=" + Quote(dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("CodeArticle")) + _
                                 " AND NumeroLotArticle=" + Quote(dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("NumeroLotArticle")) + ")" + _
                                 " INSERT INTO LOT_ARTICLE (CodeArticle,NumeroLotArticle,DatePeremptionArticle)" + _
                                 " VALUES (" + Quote(dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("CodeArticle")) + _
                                 " ," + Quote(dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("NumeroLotArticle").ToString) + _
                                 " ," + Quote(dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("DatePeremption").ToString) + ")"
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()
                        Catch ex As Exception
                            MsgBox(ex.Message)
                        End Try


                    ElseIf dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("NumeroLotArticle").ToString = "" Then
                        StrSQL = " IF NOT EXISTS " + _
                                 " (SELECT * FROM LOT_ARTICLE WHERE CodeArticle=" + Quote(dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("CodeArticle")) + _
                                 " AND NumeroLotArticle= '' )" + _
                                 " INSERT INTO LOT_ARTICLE (CodeArticle,NumeroLotArticle,DatePeremptionArticle)" + _
                                 " VALUES (" + Quote(dsEmprunt.Tables("EMPRUNT_DETAILS").Rows(I).Item("CodeArticle")) + _
                                 " ,'', NULL)"
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()
                        Catch ex As Exception
                            MsgBox(ex.Message)
                        End Try
                    End If
                End If
            Next
            cmd.Dispose()




            Try
                daEmpruntDetails.Update(dsEmprunt, "EMPRUNT_DETAILS")
            Catch ex As Exception
                'Gérer l'Exception
                MsgBox(ex.Message)
                dsEmprunt.Reset()
            End Try

            'si le mode Ajout
            If mode = "Ajout" Then
                'Appel Pour selectionner le dernier ligne 
                NumeroligneEmprunt = selectionDernierLigneEmprunt()
            End If

            'changer le mode en consultation
            mode = "Consultation"

            'Appel pour charger les information  en question
            ChargerEmprunt(NumeroligneEmprunt)

            'initialisation des btns
            initBoutons()

            gListeRecherche.Visible = False

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " bConfirmer_Click()", ex.Message, "0000061", "Erreur dans  bConfirmer_Click()", True, True, True)
        End Try
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click


        Try
            'Suivi du scénario
            fMessageException.Show("Emprunt", "fEmprunt", "bAnnuler_Click", "NoException", "NoError", "Clic sur le bouton Annuler", False, True, False)

            If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1 > 0 And gArticles(0, "CodeArticle") <> "" Then
                If MsgBox("Voulez vous vraiment annuler cette Emprunt ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Annuler Emprunt") = MsgBoxResult.No Then
                    Exit Sub
                End If
            End If


            '--------changer le mode en concultation

            mode = "Consultation"


            'Refreche liste Emprunt

            ChargerEmprunt(NumeroligneEmprunt)

            'Pour initialiser les btns suivant le mode et l'etat du table dans la BD
            initBoutons()

            gListeRecherche.Visible = False

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " bAnnuler_Click()", ex.Message, "0000062", "Erreur dans  bAnnuler_Click()", True, True, True)

        End Try

    End Sub

    Private Sub cmbpharmacie_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbpharmacie.KeyDown
        Try

            If e.KeyData <> "123" Then 'F12: Quitter
                If e.KeyCode = Keys.Enter Then
                    cmbpharmacie.Text = cmbpharmacie.WillChangeToText
                    gArticles.Focus()
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
                    gArticles.EditActive = True
                Else
                    cmbpharmacie.OpenCombo()
                End If
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " cmbpharmacie_KeyDown()", ex.Message, "0000063", "Erreur dans  cmbpharmacie_KeyDown()", True, True, True)

        End Try
    End Sub


    Private Sub bLast_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLast.Click
        Try
            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner la derniere ligne 
            NumeroligneEmprunt = selectionDernierLigneEmprunt()

            'Appel pour charger les information de l'Emprunt en question
            ChargerEmprunt(NumeroligneEmprunt)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " bLast_Click()", ex.Message, "0000064", "Erreur dans  bLast_Click()", True, True, True)

        End Try
    End Sub

    Private Sub ChargerEmprunt(ByVal pNumeroLigneEmprunt As String)

        Try
            Try
                '----------Vider la DS EMPRUNT_DETAILS

                dsEmprunt.Tables("EMPRUNT_DETAILS").Clear()

                '----------Vider la DS EMPRUNT

                dsEmprunt.Tables("EMPRUNT").Clear()
            Catch ex As Exception

            End Try

            '-----------------------------chargement des Entêtes des emprunts
            Try

                StrSQL = " SELECT * FROM (  " + _
                " SELECT *, ROW_NUMBER() OVER (ORDER BY NumeroEmprunt) as row FROM EMPRUNT " + _
                "              ) a WHERE row > " & pNumeroLigneEmprunt - 1 & " AND  row <= " & pNumeroLigneEmprunt

                cmdEmpruntEntete.Connection = ConnectionServeur
                cmdEmpruntEntete.CommandText = StrSQL
                daEmpruntEntete = New SqlDataAdapter(cmdEmpruntEntete)
                daEmpruntEntete.Fill(dsEmprunt, "EMPRUNT")
                cbEmpruntEntete = New SqlCommandBuilder(daEmpruntEntete)

            Catch ex As Exception

                'Gérer l'Exception
                fMessageException.Show("Emprunt", "fEmprunt", "ChargerEmprunt", ex.Message, "0000033", "Erreur lors d'executer la requette", True, True, True)

            End Try


            'Lire le numéro Emprunt

            If dsEmprunt.Tables("EMPRUNT").Rows.Count > 0 Then

                NumeroEmprunt = dsEmprunt.Tables("EMPRUNT").Rows(0).Item("NumeroEmprunt")

            Else

                NumeroEmprunt = "0"

            End If

            'Pour initialiser les btns suivant le mode et l'etat du table dans la BD
            initBoutons()

            'chargement des détails des emprunts

            '**************************************************************
            Try


                StrSQL = "SELECT NumeroEmprunt," + _
                        "CodeArticle," + _
                        "CodeABarre," + _
                        "Designation," + _
                        "EMPRUNT_DETAILS.CodeForme," + _
                         "'' AS LibelleForme," + _
                        "Qte," + _
                        "NumeroLotArticle," + _
                        "PrixAchatHT," + _
                        "TotalAchatHT," + _
                        "PrixAchatTTC," + _
                        "TotalAchatTTC," + _
                        "TVA," + _
                        "TotalTVA," + _
                        "Stock," + _
                        "DatePeremption, " + _
                        "(SELECT CASE WHEN QuantiteUnitaire = 0 then 1 else QuantiteUnitaire end From ARTICLE WHERE ARTICLE.CodeArticle = EMPRUNT_DETAILS.CodeArticle) as QuantiteUnitaire ," + _
                         "'' AS Vide " + _
                        "FROM " + _
                        "EMPRUNT_DETAILS " + _
                        "WHERE NumeroEmprunt =" + Quote(NumeroEmprunt) + ""

                cmdEmpruntDetail.Connection = ConnectionServeur
                cmdEmpruntDetail.CommandText = StrSQL
                daEmpruntDetails = New SqlDataAdapter(cmdEmpruntDetail)
                daEmpruntDetails.Fill(dsEmprunt, "EMPRUNT_DETAILS")
                cbEmpruntDetails = New SqlCommandBuilder(daEmpruntDetails)

            Catch ex As Exception

                'Gérer l'Exception
                fMessageException.Show("Emprunt", "fEmprunt", "chargement des détails des emprunts", ex.Message, "0000041", "Erreur lors d'executer la requette", True, True, True)
            End Try

            initgArticles()

            '-----chargement des pharmacies

            initPharmacies()

            ' Affichage ds informations de l'emprunt

            'Si le  mode est consultation

            If mode = "Modif" Or mode = "Consultation" Then

                If dsEmprunt.Tables("EMPRUNT").Rows.Count > 0 Then

                    DataRowRecherche = dsEmprunt.Tables("EMPRUNT").Select("NumeroEmprunt=" + Quote(NumeroEmprunt))(0)

                    'chargement des informations entête
                    lNumeroEmprunt.Text = dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1)("NumeroEmprunt")
                    lDateEmprunt.Text = dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1)("Date")
                    cmbpharmacie.SelectedValue = dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1)("CodePharmacie")

                    lTotalTTC.Text = dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1)("TotalTTC")
                    lTotalHT.Text = dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1)("TotalHT")
                    lTotalTVA.Text = dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1)("TotalTVA")
                    lTotalTVA.Text = dsEmprunt.Tables("EMPRUNT").Rows(0)("TotalTVA")

                    lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1)("CodePersonnel"))

                    NumeroEmprunt = DataRowRecherche.Item("NumeroEmprunt")

                End If
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " ChargerEmprunt()", ex.Message, "0000065", "Erreur dans  ChargerEmprunt()", True, True, True)

        End Try
    End Sub

    Private Sub bFirst_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFirst.Click
        Try

            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner le dernier ligne 
            selectionPremierLigneEmprunt()

            'Appel pour charger les information de l'Emprunt en question
            ChargerEmprunt(NumeroligneEmprunt)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " bFirst_Click()", ex.Message, "0000066", "Erreur dans  bFirst_Click()", True, True, True)

        End Try
    End Sub

    Private Sub bNext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bNext.Click

        Try
            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner l'element suivant 
            selectionLigneEmpruntSuivante()

            'Appel pour charger les information de l'Emprunt en question
            ChargerEmprunt(NumeroligneEmprunt)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " bNext_Click()", ex.Message, "0000067", "Erreur dans  bNext_Click()", True, True, True)

        End Try

    End Sub

    Private Sub bPrevious_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPrevious.Click
        Try
            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner l'element precedent 
            selectionLigneEmpruntPrecedent()

            'Appel pour charger les information de l'Emprunt en question
            ChargerEmprunt(NumeroligneEmprunt)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " bPrevious_Click()", ex.Message, "0000068", "Erreur dans  bPrevious_Click()", True, True, True)

        End Try
    End Sub

    Private Sub lTotalTTC_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotalTTC.TextChanged
        Try
            lTotalTTC.Text = lTotalTTC.Text
            If lTotalTTC.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lTotalTTC.Text, ".")
                If lTotalTTC.Text.Length - x = 1 Then
                    lTotalTTC.Text = lTotalTTC.Text + ("00")
                ElseIf lTotalTTC.Text.Length - x = 2 Then
                    lTotalTTC.Text = lTotalTTC.Text + ("0")
                End If
            Else
                lTotalTTC.Text = lTotalTTC.Text + ".000"
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " lTotalTTC_TextChanged()", ex.Message, "0000069", "Erreur dans  lTotalTTC_TextChanged()", True, True, True)

        End Try
    End Sub

    Private Sub lTotHT_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotalHT.TextChanged
        Try
            lTotalHT.Text = lTotalHT.Text
            If lTotalHT.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lTotalHT.Text, ".")
                If lTotalHT.Text.Length - x = 1 Then
                    lTotalHT.Text = lTotalHT.Text + ("00")
                ElseIf lTotalHT.Text.Length - x = 2 Then
                    lTotalHT.Text = lTotalHT.Text + ("0")
                End If
            Else
                lTotalHT.Text = lTotalHT.Text + ".000"
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " lTotHT_TextChanged()", ex.Message, "0000070", "Erreur dans  lTotHT_TextChanged()", True, True, True)

        End Try
    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click
        Try

            supprimerEmprunt(False)


        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " bSupprimer_Click()", ex.Message, "0000071", "Erreur dans  bSupprimer_Click()", True, True, True)

        End Try
    End Sub

    Private Sub supprimerEmprunt(ByVal msgShow As Boolean)
        Try

            'Suivi du scénario 
            fMessageException.Show("Emprunt", "fEmprunt", "bSupprimer_Click", "NoException", "NoError", "Clic sur le bouton Supprimer", False, True, False)

            Dim NumeroEmprunt As String
            Dim ConfirmerEnregistrer As Boolean = False
            Dim CodeOperateur As String = ""
            NumeroEmprunt = lNumeroEmprunt.Text

            'Si le mode est Ajout ou Modif
            If mode = "Ajout" Or mode = "Modif" Then

                'Si  la liste est vide, quitter la procedure
                If gArticles.RowCount = 0 Then
                    Exit Sub
                End If


                If gArticles.RowCount > 0 Then

                    'Test si la lign est NEW ADDED et elle est vide
                    If gArticles(gArticles.Row, ("CodeArticle")) <> "" Then
                        gArticles.Delete()
                        CalculerMontants()
                    End If


                    If gArticles.RowCount <= 0 Then
                        bAjouter.PerformClick()

                    End If

                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))

                    Exit Sub
                Else

                    CalculerMontants()

                End If

            Else 'mode  consultation
                If NumeroEmprunt = "" Then
                    MsgBox("Aucun Emprunt à supprimer !", MsgBoxStyle.Critical, "Information")
                    Exit Sub
                Else

                    If msgShow = False Then

                        If MsgBox("Voulez vous vraiment supprimer cet Emprunt " + lNumeroEmprunt.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then

                            '-------- demande du mot de passe

                            Dim myMotDePasse As New fMotDePasse
                            myMotDePasse.ShowDialog()
                            ConfirmerEnregistrer = fMotDePasse.Confirmer
                            CodeOperateur = fMotDePasse.CodeOperateur
                            myMotDePasse.Dispose()
                            myMotDePasse.Close()
                            If ConfirmerEnregistrer = False Then
                                Exit Sub

                            End If

                            Try
                                'delete de la table Emprunt details
                                cmdEmprunt.Connection = ConnectionServeur
                                cmdEmprunt.CommandText = "DELETE FROM EMPRUNT_DETAILS WHERE NumeroEmprunt ='" + NumeroEmprunt + "'"
                                cmdEmprunt.ExecuteNonQuery()
                                'delete de la table Emprunt
                                cmdEmprunt.Connection = ConnectionServeur
                                cmdEmprunt.CommandText = "DELETE FROM EMPRUNT WHERE NumeroEmprunt ='" + NumeroEmprunt + "'"
                                cmdEmprunt.ExecuteNonQuery()

                                'Ma nouvelle position

                                If NumeroligneEmprunt > 1 Then
                                    NumeroligneEmprunt = NumeroligneEmprunt - 1
                                ElseIf NumeroligneEmprunt = 1 Then
                                    initLoadControl()

                                End If
                                'charger la nouvelle position
                                ChargerEmprunt(NumeroligneEmprunt)

                            Catch ex As Exception

                                'Gérer l'Exception
                                fMessageException.Show("Emprunt", "fEmprunt", "supprimerEmprunt()", ex.Message, "00000048", "Erreur lors d'executer la requette", True, True, True)

                            End Try

                            MsgBox("Emprunt supprimé !", MsgBoxStyle.Information, "Information")

                        Else

                            Exit Sub

                        End If

                    Else

                        '-------- demande du mot de passe

                        Dim myMotDePasse As New fMotDePasse
                        myMotDePasse.ShowDialog()
                        ConfirmerEnregistrer = fMotDePasse.Confirmer
                        CodeOperateur = fMotDePasse.CodeOperateur
                        myMotDePasse.Dispose()
                        myMotDePasse.Close()
                        If ConfirmerEnregistrer = False Then
                            Exit Sub

                        End If

                        Try
                            'delete de la table Emprunt details
                            cmdEmprunt.Connection = ConnectionServeur
                            cmdEmprunt.CommandText = "DELETE FROM EMPRUNT_DETAILS WHERE NumeroEmprunt ='" + NumeroEmprunt + "'"
                            cmdEmprunt.ExecuteNonQuery()
                            'delete de la table Emprunt
                            cmdEmprunt.Connection = ConnectionServeur
                            cmdEmprunt.CommandText = "DELETE FROM EMPRUNT WHERE NumeroEmprunt ='" + NumeroEmprunt + "'"
                            cmdEmprunt.ExecuteNonQuery()

                            'Ma nouvelle position

                            If NumeroligneEmprunt > 1 Then
                                NumeroligneEmprunt = NumeroligneEmprunt - 1
                            ElseIf NumeroligneEmprunt = 1 Then
                                initLoadControl()

                            End If
                            'charger la nouvelle position
                            ChargerEmprunt(NumeroligneEmprunt)

                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Emprunt", "fEmprunt", "supprimerEmprunt()", ex.Message, "00000048", "Erreur lors d'executer la requette", True, True, True)

                        End Try

                        MsgBox("Emprunt supprimé !", MsgBoxStyle.Information, "Information")

                        Exit Sub

                    End If

                End If

            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " supprimerEmprunt()", ex.Message, "0000072", "Erreur dans  supprimerEmprunt()", True, True, True)

        End Try

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        Try

            'Suivi du scénario
            fMessageException.Show("Emprunt", "fEmprunt", "bQuitter_Click", "NoException", "NoError", "Clic sur le bouton Fermer", False, True, False)

            If mode = "Consultation" Then
                fMain.Tab.SelectedTab.Dispose()
                Exit Sub
            End If

            If dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count > 0 And gArticles(0, "CodeArticle") <> "" Then
                If MsgBox("Voulez vous vraiment Fermer cette Emprunt ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Fermer Emprunt") = MsgBoxResult.No Then
                    Exit Sub
                End If
            End If

            fMain.Tab.SelectedTab.Dispose()
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " bQuitter_Click()", ex.Message, "0000073", "Erreur dans  bQuitter_Click()", True, True, True)

        End Try
    End Sub


    Private Sub gArticles_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gArticles.MouseClick
        Try
            '---------------------------------- verouillage des lignes déja confirmées -------------------------
            If mode = "Ajout" Then
                If gArticles.Row <> dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1 Then
                    gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
                    gArticles.Splits(0).DisplayColumns("Designation").Locked = True
                ElseIf gArticles.Row = dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1 Then
                    gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
                    gArticles.Splits(0).DisplayColumns("Designation").Locked = False
                End If
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " gArticles_MouseClick()", ex.Message, "0000074", "Erreur dans  gArticles_MouseClick()", True, True, True)

        End Try
    End Sub

    Private Sub gListeRecherche_UnboundColumnFetch(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gListeRecherche.UnboundColumnFetch
        Try
            Dim y As String
            Dim CategorieArticle As Integer = 0
            Dim PreparationArticle As Integer = 0
            Dim CodeArticleMereFractionnement As Integer = 0
            Dim QteUnitaireArticleMere As Integer = 0
            Dim StockArticleMere As Integer = 0

            y = gListeRecherche(e.Row, ("CodeArticle"))

            CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", y)
            If CategorieArticle = 9 Then
                If RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", y) <> "" Then
                    PreparationArticle = RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", y)
                End If
            End If

            If CategorieArticle = 9 And PreparationArticle = 4 Then
                Try
                    CodeArticleMereFractionnement = RecupererValeurExecuteScalaire("CodeArticleMere", "FRACTIONNEMENT", "CodeFractionnement", y)
                    QteUnitaireArticleMere = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticleMereFractionnement)
                    StockArticleMere = CalculeStock(CodeArticleMereFractionnement) * QteUnitaireArticleMere
                Catch ex As Exception
                End Try
            End If

            If CategorieArticle = 9 And PreparationArticle = 4 Then
                e.Value = CalculeStock(y) + StockArticleMere
            Else
                e.Value = CalculeStock(y)
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " gListeRecherche_UnboundColumnFetch()", ex.Message, "0000075", "Erreur dans  gListeRecherche_UnboundColumnFetch()", True, True, True)

        End Try
    End Sub


    Private Sub initControlEmprunt()
        Try

            '---------------------------------Debloquer le saisie
            Dim I As Integer

            With gArticles

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Locked = True
                Next

                .Splits(0).DisplayColumns("CodeABarre").Locked = False
                .Splits(0).DisplayColumns("Designation").Locked = False
                .Splits(0).DisplayColumns("CodeForme").Locked = False
                .Splits(0).DisplayColumns("Qte").Locked = False
                .Splits(0).DisplayColumns("DatePeremption").Locked = False
                .Splits(0).DisplayColumns("NumeroLotArticle").Locked = False
                .Splits(0).DisplayColumns("PrixAchatHT").Locked = False
                .Splits(0).DisplayColumns("TotalAchatHT").Locked = False
                .Splits(0).DisplayColumns("PrixAchatTTC").Locked = False
                .Splits(0).DisplayColumns("TotalAchatTTC").Locked = False
                .Splits(0).DisplayColumns("TVA").Locked = False
                .Splits(0).DisplayColumns("TotalTVA").Locked = False
                .Splits(0).DisplayColumns("Stock").Locked = False

            End With

            '------------------------------ initialisation des differents zones de textes 
            '------------------------------ initialisation des variables globaux 

            TotalHTAchat = 0.0
            TVA = 0.0
            Timbre = 0.3

            lTotalHT.Text = "0.000"
            lTotalTTC.Text = "0.000"
            lTotalTVA.Text = "0.000"

            lOperateur.Text = "-"

            lDateEmprunt.Text = System.DateTime.Now
            cmbpharmacie.Text = ""
            lNumeroEmprunt.Text = ""

            bAnnuler.Enabled = True
            bConfirmer.Enabled = True
            bQuitter.Enabled = True
            bSupprimer.Enabled = True

            bFirst.Visible = False
            bPrevious.Visible = False
            bNext.Visible = False
            bLast.Visible = False
            bAjouter.Enabled = False

            GroupePharmacie.Enabled = True

            cmbpharmacie.Focus()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " initControlEmprunt()", ex.Message, "0000076", "Erreur dans  initControlEmprunt()", True, True, True)

        End Try
    End Sub

    '------------------------------------les procédure de navigation avec NumeroligneEmprunt---------------------------------------------------------

    Private Function selectionDernierLigneEmprunt()
        Try
            Dim StrSQL As String
            'Affécter le nombre de ligne au variable global  NumeroligneEmprunt
            StrSQL = " SELECT COUNT(*) FROM EMPRUNT "

            cmdEmprunt.Connection = ConnectionServeur
            cmdEmprunt.CommandText = StrSQL

            selectionDernierLigneEmprunt = cmdEmprunt.ExecuteScalar()

            Return selectionDernierLigneEmprunt

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "selectionDernierLigneEmprunt()", ex.Message, "00000034", "Erreur lors d'executer la requette", True, True, True)
            Return 0

        End Try

    End Function


    Private Sub selectionPremierLigneEmprunt()

        Try

            'Affécter le numéro 1 au variable global  NumeroligneEmprunt
            NumeroligneEmprunt = 1

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " selectionPremierLigneEmprunt()", ex.Message, "0000077", "Erreur dans  selectionPremierLigneEmprunt()", True, True, True)

        End Try

    End Sub


    Private Sub selectionLigneEmpruntPrecedent()

        Try
            'décrémenter le numéro 1 au variable global  NumeroligneEmprunt
            NumeroligneEmprunt = NumeroligneEmprunt - 1

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " selectionLigneEmpruntPrecedent()", ex.Message, "0000078", "Erreur dans  selectionLigneEmpruntPrecedent()", True, True, True)
        End Try

    End Sub

    Private Sub selectionLigneEmpruntSuivante()
        Try
            ' incrémenter le numéro 1 au variable global NumeroligneEmprunt  
            NumeroligneEmprunt = NumeroligneEmprunt + 1

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " selectionLigneEmpruntSuivante()", ex.Message, "0000079", "Erreur dans  selectionLigneEmpruntSuivante()", True, True, True)
        End Try
    End Sub

    Private Sub initgArticles()
        Try
            Dim I As Integer
            With gArticles
                .Columns.Clear()
                Try
                    .DataSource = dsEmprunt
                Catch ex As Exception
                End Try
                .DataMember = "EMPRUNT_DETAILS"
                .Rebind(False)
                .Columns("CodeABarre").Caption = "Code article"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Qte").Caption = "Qte"
                .Columns("NumeroLotArticle").Caption = "Numero Lot"
                .Columns("PrixAchatHT").Caption = "Prix A HT "
                .Columns("PrixAchatTTC").Caption = "Prix A TTC "
                .Columns("TotalAchatHT").Caption = "Total A HT"
                .Columns("TotalAchatTTC").Caption = "Total A TTC"
                .Columns("TVA").Caption = "TVA"
                .Columns("TotalTVA").Caption = "Total V HT"
                .Columns("DatePeremption").Caption = "Date péremption"
                .Columns("Stock").Caption = "Stock"
                'colonne vide
                .Columns("Vide").Caption = ""

                ' Colonne non liée : LibelleForme
                .Columns("LibelleForme").DataField = ""

                ' Centrer toutes les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Locked = True
                Next

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("PrixAchatTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalAchatTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalTVA").Style.HorizontalAlignment = AlignHorzEnum.Far

                .Splits(0).DisplayColumns("NumeroEmprunt").Width = 0
                .Splits(0).DisplayColumns("NumeroEmprunt").Visible = False
                .Splits(0).DisplayColumns("NumeroEmprunt").AllowSizing = False
                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

                .Splits(0).DisplayColumns("CodeABarre").Width = 120 '60
                .Splits(0).DisplayColumns("NumeroLotArticle").Width = 60
                .Splits(0).DisplayColumns("Designation").Width = 310 '260
                .Splits(0).DisplayColumns("CodeForme").Width = 0
                '.Splits(0).DisplayColumns("LibelleForme").Width = 100
                .Splits(0).DisplayColumns("Qte").Width = 70
                .Splits(0).DisplayColumns("PrixAchatHT").Width = 100
                .Splits(0).DisplayColumns("TotalAchatHT").Width = 100
                .Splits(0).DisplayColumns("PrixAchatTTC").Width = 100
                .Splits(0).DisplayColumns("TotalAchatTTC").Width = 100
                .Splits(0).DisplayColumns("TVA").Width = 70
                .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalTVA").Width = 90
                .Splits(0).DisplayColumns("DatePeremption").Width = 110 '100
                .Splits(0).DisplayColumns("Stock").Width = 50
                .Splits(0).DisplayColumns("CodeForme").Visible = False

                .Splits(0).DisplayColumns("NumeroLotArticle").Visible = False
                .Splits(0).DisplayColumns("QuantiteUnitaire").Visible = False
                'colonne vide ajouter
                .Splits(0).DisplayColumns("Vide").Width = 50
                .Splits(0).DisplayColumns("Vide").Visible = False

                'Couleur de la Grid
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.BackColor = Color.FromArgb(250, 250, 200)
                Next

                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(210, 240, 255)
                .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(210, 240, 255)
                .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(210, 255, 230)
                .Splits(0).DisplayColumns("Vide").Style.BackColor = Color.FromArgb(250, 250, 200)


                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                .AllowSort = False
                .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
                'Style du Caractere et du grid
                ParametreGrid(gArticles)
            End With

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " initgArticles()", ex.Message, "0000080", "Erreur dans  initgArticles()", True, True, True)
        End Try

    End Sub

    '-----chargement des pharmacies

    Private Sub initPharmacies()

        Try
            Try
                dsEmprunt.Tables("PHARMACIE").Clear()
            Catch ex As Exception
            End Try

            StrSQL = "SELECT CodePharmacie,Nom FROM PHARMACIE ORDER BY Nom ASC"
            cmdEmprunt.Connection = ConnectionServeur
            cmdEmprunt.CommandText = StrSQL
            daEmprunt = New SqlDataAdapter(cmdEmprunt)
            daEmprunt.Fill(dsEmprunt, "PHARMACIE")
            cmbpharmacie.DataSource = dsEmprunt.Tables("PHARMACIE")
            cmbpharmacie.ValueMember = "CodePharmacie"
            cmbpharmacie.DisplayMember = "Nom"
            cmbpharmacie.ColumnHeaders = False
            cmbpharmacie.Splits(0).DisplayColumns("CodePharmacie").Visible = False
            cmbpharmacie.Splits(0).DisplayColumns("Nom").Width = 10
            cmbpharmacie.ExtendRightColumn = True

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "initPharmacies()", ex.Message, "0000035", "Erreur lors d'executer la requette", True, True, True)

        End Try

    End Sub

    'chargement des Entêtes des emprunts

    Private Sub ChargementEnteteEmprunt()
        Try


            StrSQL = "SELECT * FROM EMPRUNT ORDER BY NumeroEmprunt ASC"
            cmdEmpruntEntete.Connection = ConnectionServeur
            cmdEmpruntEntete.CommandText = StrSQL
            daEmpruntEntete = New SqlDataAdapter(cmdEmpruntEntete)
            daEmpruntEntete.Fill(dsEmprunt, "EMPRUNT")
            If dsEmprunt.Tables("EMPRUNT").Rows.Count > 0 Then
                NumeroEmprunt = dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1).Item("NumeroEmprunt")

                lNumeroEmprunt.Text = dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1)("NumeroEmprunt")
                lDateEmprunt.Text = dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1)("Date")
                cmbpharmacie.SelectedValue = dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1)("CodePharmacie")

                lTotalHT.Text = dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1)("TotalHT")
                lTotalTTC.Text = Math.Round(dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1)("TotalTTC"), 3)
                lTotalTVA.Text = dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1)("TotalTVA")

                lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsEmprunt.Tables("EMPRUNT").Rows(dsEmprunt.Tables("EMPRUNT").Rows.Count - 1)("CodePersonnel"))

            End If
        Catch ex As Exception
            fMessageException.Show("Emprunt", "fEmprunt", "ChargementEnteteEmprunt()", ex.Message, "0000036", "Erreur lors d'executer la requette", True, True, True)
        End Try
    End Sub


    'chargement des détails des emprunts

    Private Sub ChargementDetailsEmprunts()
        Try


            StrSQL = "SELECT NumeroEmprunt," + _
                  "CodeArticle," + _
                  "CodeABarre," + _
                  "Designation," + _
                  "EMPRUNT_DETAILS.CodeForme," + _
                  "LibelleForme," + _
                  "Qte," + _
                  "DatePeremption, " + _
                  "NumeroLotArticle," + _
                  "PrixAchatHT," + _
                  "TotalAchatHT," + _
                  "PrixAchatTTC," + _
                  "TotalAchatTTC," + _
                  "TVA," + _
                  "TotalTVA," + _
                  "Stock " + _
                  "FROM " + _
                  "EMPRUNT_DETAILS,FORME_ARTICLE " + _
                  "WHERE EMPRUNT_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
                  "AND NumeroEmprunt =" + Quote(NumeroEmprunt) + ""

            cmdEmpruntDetail.Connection = ConnectionServeur
            cmdEmpruntDetail.CommandText = StrSQL
            daEmpruntDetails = New SqlDataAdapter(cmdEmpruntDetail)
            daEmpruntDetails.Fill(dsEmprunt, "EMPRUNT_DETAILS")
            cbEmpruntDetails = New SqlCommandBuilder(daEmpruntDetails)

        Catch ex As Exception
            fMessageException.Show("Emprunt", "fEmprunt", "ChargementDetailsEmprunts()", ex.Message, "0000037", "Erreur lors d'executer la requette", True, True, True)
        End Try

    End Sub

    '--------------------initialisation de la datatable article 
    '--------------------recherche alimenté selon les entrés de l'utilisateur dans la colonne designation

    Private Sub initArticle()
        Dim I As Integer
        Try



            StrSQL = "SELECT CodeArticle," + _
                    "Designation," + _
                    "LibelleForme," + _
                    "PrixVenteTTC" + _
                    " FROM ARTICLE,FORME_ARTICLE " + _
                    "WHERE ARTICLE.CodeForme=FORME_ARTICLE.CodeForme and " + _
                    "Designation LIKE " + Quote(gArticles.Columns("Designation").Value) + " ORDER BY Designation"

            cmdEmprunt.Connection = ConnectionServeur
            cmdEmprunt.CommandText = StrSQL
            daEmprunt = New SqlDataAdapter(cmdEmprunt)
            daEmprunt.Fill(dsEmprunt, "ARTICLE")

            With gListeRecherche
                .Columns.Clear()
                .DataSource = dsEmprunt
                .DataMember = "ARTICLE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code Article"
                .Columns("Designation").Caption = "Designation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("PrixVenteTTC").Caption = "Prix de vente"

                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centre tous les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Visible = False
                Next

                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
                .Splits(0).DisplayColumns("Designation").Visible = True
                .Splits(0).DisplayColumns("LibelleForme").Visible = True
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("Designation").Width = 300
                .Splits(0).DisplayColumns("LibelleForme").Width = 100
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 20
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True

                'Style du Caractere et du grid
                ParametreGrid(gListeRecherche)

            End With

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "initArticle()", ex.Message, "0000038", "Erreur lors d'executer la requette", True, True, True)

        End Try
    End Sub

    Private Sub initEmpruntEntete()

        Try

            StrSQL = " SELECT TOP (0 )* FROM EMPRUNT "
            cmdEmpruntEntete.Connection = ConnectionServeur
            cmdEmpruntEntete.CommandText = StrSQL
            daEmpruntEntete = New SqlDataAdapter(cmdEmpruntEntete)
            daEmpruntEntete.Fill(dsEmprunt, "EMPRUNT")
            cbEmpruntEntete = New SqlCommandBuilder(daEmpruntEntete)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "initEmprunt()", ex.Message, "0000039", "Erreur lors d'executer la requette", True, True, True)

        End Try

    End Sub

    Private Sub initEmpruntDetails()

        Try

            StrSQL = "SELECT TOP(0) NumeroEmprunt," + _
          "CodeArticle," + _
          "CodeABarre," + _
          "Designation," + _
          "EMPRUNT_DETAILS.CodeForme," + _
           "'' AS LibelleForme," + _
          "Qte," + _
          "DatePeremption, " + _
          "NumeroLotArticle," + _
          "PrixAchatHT," + _
          "TotalAchatHT," + _
          "PrixAchatTTC," + _
          "TotalAchatTTC," + _
          "TVA," + _
          "TotalTVA," + _
          "Stock, " + _
          "1 as QuantiteUnitaire, " + _
           "'' AS Vide " + _
          "FROM " + _
          "EMPRUNT_DETAILS " + _
          "WHERE  NumeroEmprunt =" + Quote(NumeroEmprunt) + ""

            cmdEmpruntDetail.Connection = ConnectionServeur
            cmdEmpruntDetail.CommandText = StrSQL
            daEmpruntDetails = New SqlDataAdapter(cmdEmpruntDetail)
            daEmpruntDetails.Fill(dsEmprunt, "EMPRUNT_DETAILS")
            cbEmpruntDetails = New SqlCommandBuilder(daEmpruntDetails)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "initEmpruntDetails()", ex.Message, "0000040", "Erreur lors d'executer la requette", True, True, True)

        End Try

    End Sub

    Private Sub initLoadControl()
        Try
            'mode en consultation
            mode = "Consultation"

            'Initialiser les controles
            lTotalHT.Text = "0.000"
            lTotalTTC.Text = "0.000"
            lTotalTVA.Text = "0.000"

            bConfirmer.Enabled = False
            bAnnuler.Enabled = False
            bAjouter.Enabled = True
            bFirst.Enabled = True
            bPrevious.Enabled = True
            bSupprimer.Enabled = True

            GroupePharmacie.Enabled = False

            lNumeroEmprunt.Text = "-------------"
            lDateEmprunt.Text = "Date"
            lOperateur.Text = "-"
            cmbpharmacie.SelectedValue = -1

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " initLoadControl()", ex.Message, "0000081", "Erreur dans  initLoadControl()", True, True, True)
        End Try
    End Sub

    Private Sub bModifier_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifier.Click

        Try

            'Suivi du scénario 
            fMessageException.Show("Emprunt", "fEmprunt", "bModifier_Click", "NoException", "NoError", "Clic sur le bouton Modifier", False, True, False)

            mode = "Modif"

            Dim I As Integer
            NouvelArticle = dsEmprunt.Tables("EMPRUNT_DETAILS").NewRow()
            NouvelArticle("Designation") = ""
            NouvelArticle("CodeArticle") = ""
            dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Add(NouvelArticle)

            For I = 0 To dsEmprunt.Tables("EMPRUNT_DETAILS").Columns.Count - 1
                Me.gArticles.Splits(0).DisplayColumns(I).AllowFocus = False
            Next


            With gArticles

                '.Splits(0).DisplayColumns("CodeArticle").Locked = False
                .Splits(0).DisplayColumns("Designation").Locked = False
                .Splits(0).DisplayColumns("Qte").Locked = False
                .Splits(0).DisplayColumns("DatePeremption").Locked = False
                .Splits(0).DisplayColumns("NumeroLotArticle").Locked = False
                .Splits(0).DisplayColumns("CodeABarre").Locked = False


                '.Splits(0).DisplayColumns("CodeArticle").AllowFocus = True
                .Splits(0).DisplayColumns("Designation").AllowFocus = True
                .Splits(0).DisplayColumns("Qte").AllowFocus = True
                .Splits(0).DisplayColumns("DatePeremption").AllowFocus = True
                .Splits(0).DisplayColumns("NumeroLotArticle").AllowFocus = True
                .Splits(0).DisplayColumns("CodeABarre").AllowFocus = True

            End With

            'Pour initialiser les btns suivant le mode et l'etat du table dans la BD
            initBoutons()

            cmbpharmacie.Focus()

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " bModifier_Click()", ex.Message, "0000082", "Erreur dans  bModifier_Click()", True, True, True)
        End Try

    End Sub

    Private Sub bRecherche_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRecherche.Click
        Try
            'Suivi du scénario 
            fMessageException.Show("Emprunt", "fEmprunt", "bRecherche_Click", "NoException", "NoError", "Clic sur le bouton Recherche", False, True, False)

            mode = "Consultation"
            tRecherche.Visible = True
            tRecherche.Text = System.DateTime.Now.Year.ToString + "/"
            tRecherche.Focus()
            tRecherche.Select(tRecherche.Text.Length, 0)
            bAnnuler.Enabled = True

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " bRecherche_Click()", ex.Message, "0000083", "Erreur dans  bRecherche_Click()", True, True, True)
        End Try
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Try
            'Suivi du scénario 
            fMessageException.Show("Emprunt", "fEmprunt", "bImprimer_Click", "NoException", "NoError", "Clic sur le bouton Imprimer", False, True, False)

            If lNumeroEmprunt.Text = "" Then
                Exit Sub
            End If

            Dim CondCrystal As String = ""
            CondCrystal = " 1=1 AND {Vue_EtatEmprunt.NumeroEmprunt} = '" & lNumeroEmprunt.Text & "' "

            Dim I As Integer
            Dim num As Integer = 999
            For I = 0 To fMain.Tab.TabPages.Count - 1
                If fMain.Tab.TabPages(I).Text = "Impression Emprunt" Then
                    num = I
                End If
            Next
            CR.FileName = Application.StartupPath + "\EtatEmprunt.rpt"

            CR.SetParameterValue("pNumeroEmprunt", lNumeroEmprunt.Text)

            Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo


            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent

            CR.RecordSelectionFormula = CondCrystal
            Dim MyViewer As New fViewer
            MyViewer.CRViewer.ReportSource = CR
            fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
            fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
            fMain.Tab.SelectedTab.Text = "Impression Emprunt"
            If num <> 999 Then
                fMain.Tab.TabPages(num).Dispose()
            End If



        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " bImprimer_Click()", ex.Message, "0000084", "Erreur dans  bImprimer_Click()", True, True, True)
        End Try

    End Sub


    Private Sub rechercheEmprunt(ByVal pNumeroEmprunt As String)
        Try
            '----------------------------------Traitement

            If tRecherche.Text.Length < 11 Then
                tRecherche.Text = tRecherche.Text.Substring(0, 5) + tRecherche.Text.Substring(5, tRecherche.Text.Length - 5).PadLeft(6, "0")
            End If



            'Recuperer la valeur de la row
            recupererNumRowRechrche()


            If NumeroligneEmprunt <> 0 Then
                ChargerEmprunt(NumeroligneEmprunt)
            End If

            tRecherche.Value = ""
            tRecherche.Visible = False

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " rechercheEmprunt()", ex.Message, "0000085", "Erreur dans  rechercheEmprunt()", True, True, True)
        End Try
    End Sub

    Private Sub recupererNumRowRechrche()

        Try
            '------------------------- affichage du nombre d'Emprunt en instance 
            StrSQL = " SELECT RowNumber " + _
                     " FROM (select ROW_NUMBER() OVER(ORDER BY NumeroEmprunt) " + _
                     " AS 'RowNumber' , NumeroEmprunt  from EMPRUNT) AS EMPRUNTLISTE " + _
                     " where EMPRUNTLISTE.NumeroEmprunt =  " & Quote(tRecherche.Text)

            cmdEmprunt.Connection = ConnectionServeur
            cmdEmprunt.CommandText = StrSQL

            NumeroligneEmprunt = cmdEmprunt.ExecuteScalar()

            If NumeroligneEmprunt = 0 Then
                MsgBox("Emprunt inéxistant", MsgBoxStyle.Exclamation, "Recherche")
                NumeroligneEmprunt = selectionDernierLigneEmprunt()
            End If

            'Tester si on atteint la premiere ligne
            'pour desactiver les BTN Précedent et Premier élément        
            If NumeroligneEmprunt = 1 Or NumeroligneEmprunt = 0 Then

                bPrevious.Enabled = False
                bFirst.Enabled = False

            Else

                bPrevious.Enabled = True
                bFirst.Enabled = True

            End If

            'Tester si on atteint la derniere ligne
            'pour desactiver les BTN Siuvant et Dernier élément
            If NumeroligneEmprunt = selectionDernierLigneEmprunt() Then

                bNext.Enabled = False
                bLast.Enabled = False

            Else

                bNext.Enabled = True
                bLast.Enabled = True

            End If


        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "recupererNumRowRechrche()", ex.Message, "0000049", "Erreur lors d'executer la requette", True, True, True)

        End Try

    End Sub


    Private Sub tRecherche_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tRecherche.LostFocus
        Try
            tRecherche.Visible = False

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " tRecherche_LostFocus()", ex.Message, "0000086", "Erreur dans  tRecherche_LostFocus()", True, True, True)
        End Try
    End Sub


    Private Sub tRecherche_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRecherche.KeyUp
        Try

            If e.KeyCode = Keys.Enter And tRecherche.Text <> "" Then

                'Recuprére le Row de l'element sellectioné

                'Lors du press Enter, on va appler la procedure rechercheEmprunt

                rechercheEmprunt(tRecherche.Text)

            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " tRecherche_KeyUp()", ex.Message, "0000087", "Erreur dans  tRecherche_KeyUp()", True, True, True)
        End Try
    End Sub


    'init les controles lors de la modification

    Private Sub initControlModification()
        Try

            mode = "Modif"

            lPharmacie.Enabled = True

            GroupePharmacie.Enabled = True

            bAnnuler.Enabled = True

            bConfirmer.Enabled = True

            bRecherche.Enabled = False

            bModifier.Enabled = False

            bImprimer.Enabled = False

            bNext.Visible = False

            bPrevious.Visible = False

            bLast.Visible = False

            bFirst.Visible = False

            bAjouter.Enabled = False

            bQuitter.Enabled = True

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " initControlModification()", ex.Message, "0000088", "Erreur dans  initControlModification()", True, True, True)
        End Try

    End Sub

    Private Sub gArticles_UnboundColumnFetch(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gArticles.UnboundColumnFetch

        'Récuperer la valeur Désignation FORME ARTICLE 
        Try

            StrSQL = " SELECT LibelleForme FROM FORME_ARTICLE AS F JOIN ARTICLE AS A ON  " + _
                     " F.CodeForme = A.CodeForme" + _
                     " WHERE CodeArticle = " + Quote(gArticles(e.Row, "CodeArticle"))

            cmdEmprunt.Connection = ConnectionServeur
            cmdEmprunt.CommandText = StrSQL

           e.Value = cmdEmprunt.ExecuteScalar()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", "gArticles_UnboundColumnFetch()", ex.Message, "0000051", "Erreur lors de lire des informations du table LibelleForme", True, True, True)

        End Try

    End Sub

    'Pour initialiser les btns suivant le mode et l'etat du table dans la BD

    Private Sub initBoutons()

        Try
            'Tester si on atteint la premiere ligne
            'pour desactiver les BTN Précedent et Premier élément        
            If NumeroligneEmprunt = 1 Then

                bPrevious.Enabled = False
                bFirst.Enabled = False

            Else

                bPrevious.Enabled = True
                bFirst.Enabled = True

            End If

            'Tester si on atteint la derniere ligne
            'pour desactiver les BTN Siuvant et Dernier élément
            If NumeroligneEmprunt = selectionDernierLigneEmprunt() Then

                bNext.Enabled = False
                bLast.Enabled = False


            Else

                bNext.Enabled = True
                bLast.Enabled = True

            End If

            'Le cas ou la table est vide
            If NumeroligneEmprunt = 0 Then

                bNext.Enabled = False
                bLast.Enabled = False
                bPrevious.Enabled = False
                bFirst.Enabled = False

            End If

            'Tester si la table est vide
            'pour desactiver les BTN Siuvant et Dernier élément
            If selectionDernierLigneEmprunt() = 0 Then

                'Bloque navigation
                bNext.Enabled = False
                bLast.Enabled = False
                bNext.Enabled = False
                bLast.Enabled = False

                'Bouton de ctrl
                bAnnuler.Enabled = False
                bConfirmer.Enabled = False
                bImprimer.Enabled = False
                bModifier.Enabled = False
                bRecherche.Enabled = False
                bSupprimer.Enabled = False
                bQuitter.Enabled = True

            End If   ' le cas on a ajouté un element

            'le mode en Cosultation et on a des enregistrements
            If selectionDernierLigneEmprunt() <> 0 And mode = "Consultation" Then

                'Bouton de ctrl
                bAnnuler.Enabled = False
                bConfirmer.Enabled = False
                bImprimer.Enabled = True
                bModifier.Enabled = True
                bRecherche.Enabled = True
                bSupprimer.Enabled = True
                bAjouter.Enabled = True
                bQuitter.Enabled = True

                bFirst.Visible = True
                bPrevious.Visible = True
                bNext.Visible = True
                bLast.Visible = True

                GroupePharmacie.Enabled = False

                'le mode est modif/Ajout et on a des enregistrements
            ElseIf selectionDernierLigneEmprunt() <> 0 And mode <> "Consultation" Then

                bAnnuler.Enabled = True
                bConfirmer.Enabled = True
                bImprimer.Enabled = False
                bModifier.Enabled = False
                bAjouter.Enabled = False
                bRecherche.Enabled = False
                bSupprimer.Enabled = True
                bQuitter.Enabled = True

                'pour rendre visible si le mode est Modif ou Ajout
                bLast.Visible = False
                bNext.Visible = False
                bPrevious.Visible = False
                bFirst.Visible = False

                GroupePharmacie.Enabled = True


                'le mode en Cosultation et on  a pas des enregistrements
            ElseIf selectionDernierLigneEmprunt() = 0 And mode = "Consultation" Then

                bAjouter.Enabled = True
                bAnnuler.Enabled = False
                bConfirmer.Enabled = False
                bQuitter.Enabled = True

                'pour rendre invisible si le mode est Consultation
                bFirst.Visible = True
                bPrevious.Visible = True
                bNext.Visible = True
                bLast.Visible = True

                GroupePharmacie.Enabled = False
                cmbpharmacie.SelectedValue = -1



            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " initBoutons()", ex.Message, "0000089", "Erreur dans  initBoutons()", True, True, True)
        End Try
    End Sub
    ''pour rende invisible la grid gliste recherche
    'Private Sub gArticles_Leave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gArticles.Leave
    '    gListeRecherche.Visible = False
    'End Sub

    Private Sub gArticles_AfterColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gArticles.AfterColUpdate
        Try
            If e.ColIndex = gArticles.Columns.IndexOf(gArticles.Columns("Qte")) Then
                If gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999 Then
                    MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Columns("Qte").Value = "1"
                End If
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Emprunt", "fEmprunt", " gArticles_AfterColUpdate()", ex.Message, "0000090", "Erreur dans  gArticles_AfterColUpdate()", True, True, True)
        End Try
    End Sub

    Private Sub gArticles_BeforeColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColUpdateEventArgs) Handles gArticles.BeforeColUpdate
        If e.ColIndex = gArticles.Columns.IndexOf(gArticles.Columns("Qte")) Then
            If gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999 Then
                MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                gArticles.Columns("Qte").Value = e.OldValue
            End If
        End If
    End Sub

    Private Sub gArticles_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyDown
        If e.KeyCode = Keys.F1 And gArticles.Columns("CodeArticle").Value.ToString <> "" Then
            Dim stock As Integer
            stock = CalculeStock(gArticles.Columns("CodeArticle").Value)
            AfficherFicheArticle(gArticles.Columns("CodeArticle").Value, stock, gArticles.Columns("Designation").Value)
            Exit Sub
        End If
    End Sub

    Public Sub AfficherFicheArticle(ByVal CodeArticle, ByVal StockArticle, ByVal Designation)
        Try
            Dim MyFicheArticle As New fFicheArticle
            MyFicheArticle.CodeArticle = CodeArticle
            MyFicheArticle.StockArticle = StockArticle
            MyFicheArticle.DesignationArticle = Designation
            MyFicheArticle.ajoutmodif = "M"
            MyFicheArticle.Init()
            MyFicheArticle.ShowDialog()
            MyFicheArticle.Close()
            MyFicheArticle.Dispose()
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Entree", "fEntree", "AfficherFicheArticle", ex.Message, "0000721", "Erreur d'excution de AfficherFicheArticle", True, True, True)
            Return
        End Try
    End Sub
End Class

