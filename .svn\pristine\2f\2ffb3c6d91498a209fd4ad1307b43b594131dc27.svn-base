﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="pharmaModel.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2008" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
  <EntityContainer Name="pharmaModelStoreContainer">
    <EntitySet Name="ACHAT" EntityType="pharmaModel.Store.ACHAT" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="ACHAT_DETAILS" EntityType="pharmaModel.Store.ACHAT_DETAILS" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="COMMANDE" EntityType="pharmaModel.Store.COMMANDE" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="COMMANDE_DETAILS" EntityType="pharmaModel.Store.COMMANDE_DETAILS" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="SIMULATION_STOCK" EntityType="pharmaModel.Store.SIMULATION_STOCK" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="SIMULATION_STOCK_DETAILS" EntityType="pharmaModel.Store.SIMULATION_STOCK_DETAILS" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="VENTE" EntityType="pharmaModel.Store.VENTE" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="VENTE_DETAILS" EntityType="pharmaModel.Store.VENTE_DETAILS" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="VENTE_NUMERO" EntityType="pharmaModel.Store.VENTE_NUMERO" store:Type="Tables" store:Schema="dbo" store:Name="VENTE_NUMERO">
      <DefiningQuery>SELECT 
      [VENTE_NUMERO].[NumeroVente] AS [NumeroVente]
      FROM [dbo].[VENTE_NUMERO] AS [VENTE_NUMERO]</DefiningQuery>
    </EntitySet>
    <AssociationSet Name="FK_SIMULATION_STOCK_DETAILS_SIMULATION_STOCK" Association="pharmaModel.Store.FK_SIMULATION_STOCK_DETAILS_SIMULATION_STOCK">
      <End Role="SIMULATION_STOCK" EntitySet="SIMULATION_STOCK" />
      <End Role="SIMULATION_STOCK_DETAILS" EntitySet="SIMULATION_STOCK_DETAILS" />
    </AssociationSet>
  </EntityContainer>
  <EntityType Name="ACHAT">
    <Key>
      <PropertyRef Name="NumeroAchat" />
    </Key>
    <Property Name="NumeroAchat" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Date" Type="datetime" Nullable="false" />
    <Property Name="TotalHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TVA" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalRemise" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="Timbre" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="CodeFournisseur" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodePersonnel" Type="varchar" Nullable="false" MaxLength="50" />
    <Property Name="Note" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="NumeroBL/Facture" Type="varchar" MaxLength="255" />
    <Property Name="LibellePoste" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="DateBlFacture" Type="date" Nullable="false" />
    <Property Name="ValeurVenteTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="Autre" Type="decimal" Nullable="false" Scale="3" />
  </EntityType>
  <EntityType Name="ACHAT_DETAILS">
    <Key>
      <PropertyRef Name="NumeroAchat" />
      <PropertyRef Name="CodeArticle" />
      <PropertyRef Name="NumeroLotArticle" />
    </Key>
    <Property Name="NumeroAchat" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeArticle" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="NumeroLotArticle" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Ordre" Type="bigint" Nullable="false" />
    <Property Name="CodeABarre" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Designation" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeForme" Type="int" Nullable="false" />
    <Property Name="Qte" Type="int" Nullable="false" />
    <Property Name="Stock" Type="int" Nullable="false" />
    <Property Name="PrixAchatHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalAchatHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="PrixVenteTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="Remise" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TVA" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="DatePeremption" Type="date" />
    <Property Name="QuantiteUnitaire" Type="int" Nullable="false" />
  </EntityType>
  <EntityType Name="COMMANDE">
    <Key>
      <PropertyRef Name="NumeroCommande" />
    </Key>
    <Property Name="NumeroCommande" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Date" Type="datetime" Nullable="false" />
    <Property Name="TotalHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalTVA" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="LibellePoste" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodePersonnel" Type="varchar" Nullable="false" MaxLength="50" />
    <Property Name="CodeFournisseur" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Note" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="NumeroFacture" Type="varchar" MaxLength="255" />
    <Property Name="TypeCommande" Type="varchar" Nullable="false" MaxLength="255" />
  </EntityType>
  <EntityType Name="COMMANDE_DETAILS">
    <Key>
      <PropertyRef Name="NumeroCommande" />
      <PropertyRef Name="CodeArticle" />
    </Key>
    <Property Name="NumeroCommande" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeArticle" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeABarre" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Designation" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeForme" Type="int" Nullable="false" />
    <Property Name="Qte" Type="int" Nullable="false" />
    <Property Name="Stock" Type="int" Nullable="false" />
    <Property Name="DatePeremption" Type="datetime" />
    <Property Name="PrixAchatHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TVA" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalTTCAchat" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="StockAlerte" Type="numeric" Nullable="false" Precision="6" />
    <Property Name="EnCours" Type="numeric" Nullable="false" Precision="6" />
    <Property Name="QteACommander" Type="numeric" Nullable="false" Precision="6" />
    <Property Name="QteUnitaire" Type="numeric" Nullable="false" Precision="6" />
    <Property Name="Ordre" Type="int" />
  </EntityType>
  <EntityType Name="SIMULATION_STOCK">
    <Key>
      <PropertyRef Name="NumeroSimulation" />
    </Key>
    <Property Name="NumeroSimulation" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Date" Type="datetime" Nullable="false" />
    <Property Name="DateImpression" Type="date" Nullable="false" />
    <Property Name="TotalAHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalATTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalVTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="CodeCategorie" Type="int" />
    <Property Name="CodePersonnel" Type="int" Nullable="false" />
  </EntityType>
  <EntityType Name="SIMULATION_STOCK_DETAILS">
    <Key>
      <PropertyRef Name="NumeroSimulation" />
      <PropertyRef Name="CodeArticle" />
    </Key>
    <Property Name="NumeroSimulation" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeArticle" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Designation" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeForme" Type="int" Nullable="false" />
    <Property Name="Qte" Type="int" Nullable="false" />
    <Property Name="PrixAchatHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="PrixAchatTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="PrixVenteTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalAchatHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalAchatTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalVenteTTC" Type="decimal" Nullable="false" Scale="3" />
  </EntityType>
  <EntityType Name="VENTE">
    <Key>
      <PropertyRef Name="NumeroVente" />
    </Key>
    <Property Name="NumeroVente" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Date" Type="datetime" Nullable="false" />
    <Property Name="TotalHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TVA" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalRemise" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="Timbre" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="CodeClient" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodePersonnel" Type="varchar" Nullable="false" MaxLength="50" />
    <Property Name="CodeAPCI" Type="int" />
    <Property Name="CodeDeFamille" Type="int" />
    <Property Name="CodeMedecinFamille" Type="varchar" MaxLength="255" />
    <Property Name="CodeMedecinPrescripteur" Type="varchar" MaxLength="255" />
    <Property Name="LibellePoste" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Recu" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="DateOrdonnance" Type="date" />
    <Property Name="MontantCnam" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="MontantMutuelle" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="Note" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeNatureReglement" Type="int" />
    <Property Name="CodeMutuelle" Type="varchar" MaxLength="255" />
    <Property Name="NomMalade" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Rang" Type="int" />
    <Property Name="DateNaissance" Type="date" />
    <Property Name="CodeLienDeParente" Type="int" />
    <Property Name="LibelleLienDeParente" Type="varchar" Nullable="false" MaxLength="50" />
    <Property Name="TiersPayant" Type="bit" Nullable="false" />
    <Property Name="PriseEnCharge" Type="bit" Nullable="false" />
    <Property Name="Appareillage" Type="bit" Nullable="false" />
    <Property Name="IdentifiantCNAMMedecin" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Libelle1" Type="varchar" MaxLength="255" />
    <Property Name="Libelle2" Type="varchar" MaxLength="255" />
    <Property Name="Libelle3" Type="varchar" MaxLength="255" />
    <Property Name="Libelle4" Type="varchar" MaxLength="255" />
    <Property Name="Libelle5" Type="varchar" MaxLength="255" />
    <Property Name="NumeroFacture" Type="varchar" MaxLength="255" />
    <Property Name="Totaliseur" Type="bit" />
    <Property Name="Vider" Type="bit" Nullable="false" />
    <Property Name="NumeroPriseEnCharge" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="NumeroBonAchat" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="DureeTraitement" Type="int" Nullable="false" />
    <Property Name="OMF" Type="bit" />
    <Property Name="APCI" Type="bit" />
    <Property Name="CodeAppareillage" Type="varchar" MaxLength="10" />
    <Property Name="NomInscritSurLeCheque" Type="varchar" MaxLength="255" />
    <Property Name="NumeroCheque" Type="varchar" MaxLength="255" />
    <Property Name="DateEcheance" Type="date" />
    <Property Name="IDFacturationClient" Type="int" />
  </EntityType>
  <EntityType Name="VENTE_DETAILS">
    <Key>
      <PropertyRef Name="Id" />
      <PropertyRef Name="NumeroVente" />
      <PropertyRef Name="CodeArticle" />
      <PropertyRef Name="NumeroLotArticle" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
    <Property Name="NumeroVente" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeArticle" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="NumeroLotArticle" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Ordre" Type="int" Nullable="false" />
    <Property Name="CodeABarre" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Designation" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeForme" Type="int" Nullable="false" />
    <Property Name="Qte" Type="int" Nullable="false" />
    <Property Name="PrixAchat" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="PrixHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalHT" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="PrixTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalTTC" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="Remise" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TVA" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="TotalTVA" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="Honoraire" Type="decimal" Nullable="false" Scale="3" />
    <Property Name="Stock" Type="int" Nullable="false" />
    <Property Name="DateDePeremption" Type="date" />
    <Property Name="PriseEnCharge" Type="bit" />
    <Property Name="AccordPrealable" Type="bit" />
    <Property Name="TarifDeReference" Type="decimal" Scale="3" />
    <Property Name="DureeTraitement" Type="int" Nullable="false" />
    <Property Name="MontantMutuelle" Type="decimal" Scale="3" />
    <Property Name="MontantCNAM" Type="decimal" Scale="3" />
  </EntityType>
  <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « pharma.dbo.VENTE_NUMERO » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
  <EntityType Name="VENTE_NUMERO">
    <Key>
      <PropertyRef Name="NumeroVente" />
    </Key>
    <Property Name="NumeroVente" Type="varchar" Nullable="false" MaxLength="50" />
  </EntityType>
  <Association Name="FK_SIMULATION_STOCK_DETAILS_SIMULATION_STOCK">
    <End Role="SIMULATION_STOCK" Type="pharmaModel.Store.SIMULATION_STOCK" Multiplicity="1" />
    <End Role="SIMULATION_STOCK_DETAILS" Type="pharmaModel.Store.SIMULATION_STOCK_DETAILS" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="SIMULATION_STOCK">
        <PropertyRef Name="NumeroSimulation" />
      </Principal>
      <Dependent Role="SIMULATION_STOCK_DETAILS">
        <PropertyRef Name="NumeroSimulation" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
</Schema>