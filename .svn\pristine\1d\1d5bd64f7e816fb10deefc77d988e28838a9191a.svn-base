﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fListesPourSpot
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fListesPourSpot))
        Me.gListe = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.lTitre = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.lNombreLigne = New System.Windows.Forms.Label()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        CType(Me.gListe, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'gListe
        '
        Me.gListe.AllowUpdate = False
        Me.gListe.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gListe.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gListe.GroupByCaption = "Drag a column header here to group by that column"
        Me.gListe.Images.Add(CType(resources.GetObject("gListe.Images"), System.Drawing.Image))
        Me.gListe.LinesPerRow = 2
        Me.gListe.Location = New System.Drawing.Point(12, 87)
        Me.gListe.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gListe.Name = "gListe"
        Me.gListe.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gListe.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gListe.PreviewInfo.ZoomFactor = 75.0R
        Me.gListe.PrintInfo.PageSettings = CType(resources.GetObject("gListe.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gListe.Size = New System.Drawing.Size(860, 335)
        Me.gListe.TabIndex = 14
        Me.gListe.Text = "C1TrueDBGrid1"
        Me.gListe.PropBag = resources.GetString("gListe.PropBag")
        '
        'lTitre
        '
        Me.lTitre.AutoSize = True
        Me.lTitre.Cursor = System.Windows.Forms.Cursors.Default
        Me.lTitre.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.lTitre.Font = New System.Drawing.Font("Microsoft Sans Serif", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTitre.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.lTitre.Location = New System.Drawing.Point(255, 9)
        Me.lTitre.Name = "lTitre"
        Me.lTitre.Size = New System.Drawing.Size(0, 42)
        Me.lTitre.TabIndex = 15
        Me.lTitre.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Location = New System.Drawing.Point(12, 458)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(94, 13)
        Me.Label5.TabIndex = 36
        Me.Label5.Text = "Nombre de ventes"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lNombreLigne
        '
        Me.lNombreLigne.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lNombreLigne.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNombreLigne.ForeColor = System.Drawing.Color.Green
        Me.lNombreLigne.Location = New System.Drawing.Point(112, 453)
        Me.lNombreLigne.Name = "lNombreLigne"
        Me.lNombreLigne.Size = New System.Drawing.Size(100, 22)
        Me.lNombreLigne.TabIndex = 37
        Me.lNombreLigne.Text = "0"
        Me.lNombreLigne.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(761, 430)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(111, 45)
        Me.bQuitter.TabIndex = 56
        Me.bQuitter.Text = "Fermer         F12"
        Me.bQuitter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fListesPourSpot
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(884, 480)
        Me.Controls.Add(Me.bQuitter)
        Me.Controls.Add(Me.Label5)
        Me.Controls.Add(Me.lNombreLigne)
        Me.Controls.Add(Me.lTitre)
        Me.Controls.Add(Me.gListe)
        Me.Name = "fListesPourSpot"
        Me.Text = "fListesPourSpot"
        CType(Me.gListe, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents gListe As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents lTitre As System.Windows.Forms.Label
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents lNombreLigne As System.Windows.Forms.Label
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
End Class
