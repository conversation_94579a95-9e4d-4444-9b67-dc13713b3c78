//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class ARTICLE
    {
        public ARTICLE()
        {
            this.ACHAT_INSTANCE_DETAILS = new HashSet<ACHAT_INSTANCE_DETAILS>();
            this.ARTICLE_SURVEILLER = new HashSet<ARTICLE_SURVEILLER>();
            this.BON = new HashSet<BON>();
            this.CHANGEMANT_DE_PRIX = new HashSet<CHANGEMANT_DE_PRIX>();
            this.COMMANDE_INSTANCE_DETAILS = new HashSet<COMMANDE_INSTANCE_DETAILS>();
            this.FORMULE_PREPARATION_DETAILS = new HashSet<FORMULE_PREPARATION_DETAILS>();
            this.FRACTIONNEMENT = new HashSet<FRACTIONNEMENT>();
            this.LOT_ARTICLE = new HashSet<LOT_ARTICLE>();
            this.PRET_DETAILS = new HashSet<PRET_DETAILS>();
            this.VENTE_INSTANCE_DETAILS = new HashSet<VENTE_INSTANCE_DETAILS>();
            this.MUTUELLE = new HashSet<MUTUELLE>();
            this.MUTUELLE1 = new HashSet<MUTUELLE>();
            this.MUTUELLE2 = new HashSet<MUTUELLE>();
        }
    
        public string CodeArticle { get; set; }
        public string CodeABarre { get; set; }
        public string Designation { get; set; }
        public string Dosage { get; set; }
        public string LibelleTableau { get; set; }
        public decimal QuantiteUnitaire { get; set; }
        public int ContenanceArticle { get; set; }
        public decimal PrixAchatHT { get; set; }
        public decimal PrixAchatTTC { get; set; }
        public decimal PrixVenteHT { get; set; }
        public decimal PrixVenteTTC { get; set; }
        public decimal TVA { get; set; }
        public decimal Marge { get; set; }
        public bool Exonorertva { get; set; }
        public decimal HR { get; set; }
        public string CodePCT { get; set; }
        public Nullable<int> CodeCategorieCNAM { get; set; }
        public decimal TarifDeReference { get; set; }
        public bool AccordPrealable { get; set; }
        public bool PriseEnCharge { get; set; }
        public bool SansCodeBarre { get; set; }
        public bool SansVignette { get; set; }
        public Nullable<decimal> StockAlerte { get; set; }
        public decimal QteACommander { get; set; }
        public Nullable<System.DateTime> DateAlerte { get; set; }
        public Nullable<System.DateTime> DateDerniereCommande { get; set; }
        public Nullable<decimal> QuantiteDernierCommande { get; set; }
        public Nullable<System.DateTime> DateInitiale { get; set; }
        public int StockInitial { get; set; }
        public int CodeForme { get; set; }
        public int CodeCategorie { get; set; }
        public Nullable<int> CodeLabo { get; set; }
        public string Rayon { get; set; }
        public Nullable<int> CodeSituation { get; set; }
        public string CodeOperateur { get; set; }
        public Nullable<int> CodeGroupement { get; set; }
        public Nullable<int> CodeTypePreparation { get; set; }
        public string Section { get; set; }
        public Nullable<int> DCI1 { get; set; }
        public Nullable<int> DCI2 { get; set; }
        public Nullable<int> DCI3 { get; set; }
        public bool Supprime { get; set; }
        public bool FemmeEnceinte { get; set; }
        public decimal StockArticle { get; set; }
        public string CodeFournisseur { get; set; }
        public Nullable<int> NombreCommande { get; set; }
        public string NumeroCirculaire { get; set; }
        public Nullable<System.DateTime> DateDerniereAchat { get; set; }
    
        public virtual ICollection<ACHAT_INSTANCE_DETAILS> ACHAT_INSTANCE_DETAILS { get; set; }
        public virtual CATEGORIE CATEGORIE { get; set; }
        public virtual CATEGORIE_CNAM CATEGORIE_CNAM { get; set; }
        public virtual DCI DCI { get; set; }
        public virtual DCI DCI4 { get; set; }
        public virtual DCI DCI5 { get; set; }
        public virtual FORME_ARTICLE FORME_ARTICLE { get; set; }
        public virtual LABORATOIRE LABORATOIRE { get; set; }
        public virtual SITUATION_ARTICLE SITUATION_ARTICLE { get; set; }
        public virtual ICollection<ARTICLE_SURVEILLER> ARTICLE_SURVEILLER { get; set; }
        public virtual TABLEAU TABLEAU { get; set; }
        public virtual TYPE_PREPARATION TYPE_PREPARATION { get; set; }
        public virtual UTILISATEUR UTILISATEUR { get; set; }
        public virtual ICollection<BON> BON { get; set; }
        public virtual ICollection<CHANGEMANT_DE_PRIX> CHANGEMANT_DE_PRIX { get; set; }
        public virtual ICollection<COMMANDE_INSTANCE_DETAILS> COMMANDE_INSTANCE_DETAILS { get; set; }
        public virtual ICollection<FORMULE_PREPARATION_DETAILS> FORMULE_PREPARATION_DETAILS { get; set; }
        public virtual ICollection<FRACTIONNEMENT> FRACTIONNEMENT { get; set; }
        public virtual ICollection<LOT_ARTICLE> LOT_ARTICLE { get; set; }
        public virtual ICollection<PRET_DETAILS> PRET_DETAILS { get; set; }
        public virtual ICollection<VENTE_INSTANCE_DETAILS> VENTE_INSTANCE_DETAILS { get; set; }
        public virtual ICollection<MUTUELLE> MUTUELLE { get; set; }
        public virtual ICollection<MUTUELLE> MUTUELLE1 { get; set; }
        public virtual ICollection<MUTUELLE> MUTUELLE2 { get; set; }
    }
}
