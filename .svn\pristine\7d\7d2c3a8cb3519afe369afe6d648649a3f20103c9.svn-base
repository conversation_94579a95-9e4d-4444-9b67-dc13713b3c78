﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fImpressionDesRelevesCNAM
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.Panel = New System.Windows.Forms.Panel
        Me.GroupBox3 = New System.Windows.Forms.GroupBox
        Me.bTous = New C1.Win.C1Input.C1Button
        Me.bOrdonnance = New C1.Win.C1Input.C1Button
        Me.bFacture = New C1.Win.C1Input.C1Button
        Me.bAnnuler = New C1.Win.C1Input.C1Button
        Me.Panel.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.GroupBox3)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(249, 245)
        Me.Panel.TabIndex = 7
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.bTous)
        Me.GroupBox3.Controls.Add(Me.bOrdonnance)
        Me.GroupBox3.Controls.Add(Me.bFacture)
        Me.GroupBox3.Controls.Add(Me.bAnnuler)
        Me.GroupBox3.Location = New System.Drawing.Point(11, 9)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(222, 223)
        Me.GroupBox3.TabIndex = 0
        Me.GroupBox3.TabStop = False
        '
        'bTous
        '
        Me.bTous.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bTous.Location = New System.Drawing.Point(15, 12)
        Me.bTous.Name = "bTous"
        Me.bTous.Size = New System.Drawing.Size(189, 45)
        Me.bTous.TabIndex = 3
        Me.bTous.Text = "Tous"
        Me.bTous.UseVisualStyleBackColor = True
        Me.bTous.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bOrdonnance
        '
        Me.bOrdonnance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bOrdonnance.Location = New System.Drawing.Point(15, 65)
        Me.bOrdonnance.Name = "bOrdonnance"
        Me.bOrdonnance.Size = New System.Drawing.Size(189, 45)
        Me.bOrdonnance.TabIndex = 0
        Me.bOrdonnance.Text = "Reglés Uniquement"
        Me.bOrdonnance.UseVisualStyleBackColor = True
        Me.bOrdonnance.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bFacture
        '
        Me.bFacture.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bFacture.Location = New System.Drawing.Point(15, 118)
        Me.bFacture.Name = "bFacture"
        Me.bFacture.Size = New System.Drawing.Size(189, 45)
        Me.bFacture.TabIndex = 1
        Me.bFacture.Text = "Non Reglés Uniquement"
        Me.bFacture.UseVisualStyleBackColor = True
        Me.bFacture.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAnnuler
        '
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Location = New System.Drawing.Point(15, 171)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(189, 45)
        Me.bAnnuler.TabIndex = 2
        Me.bAnnuler.Text = "Annuler"
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fImpressionDesRelevesCNAM
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(249, 245)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fImpressionDesRelevesCNAM"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        Me.GroupBox3.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents bOrdonnance As C1.Win.C1Input.C1Button
    Friend WithEvents bFacture As C1.Win.C1Input.C1Button
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bTous As C1.Win.C1Input.C1Button
End Class
