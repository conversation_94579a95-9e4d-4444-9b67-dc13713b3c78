{"format": 1, "restore": {"F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.UI\\PharmaModerne.UI.csproj": {}}, "projects": {"F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Core\\PharmaModerne.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Core\\PharmaModerne.Core.csproj", "projectName": "PharmaModerne.Core", "projectPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Core\\PharmaModerne.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Shared\\PharmaModerne.Shared.csproj": {"projectPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Shared\\PharmaModerne.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Data\\PharmaModerne.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Data\\PharmaModerne.Data.csproj", "projectName": "PharmaModerne.Data", "projectPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Data\\PharmaModerne.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Data\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Shared\\PharmaModerne.Shared.csproj": {"projectPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Shared\\PharmaModerne.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Services\\PharmaModerne.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Services\\PharmaModerne.Services.csproj", "projectName": "PharmaModerne.Services", "projectPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Services\\PharmaModerne.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Services\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Core\\PharmaModerne.Core.csproj": {"projectPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Core\\PharmaModerne.Core.csproj"}, "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Data\\PharmaModerne.Data.csproj": {"projectPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Data\\PharmaModerne.Data.csproj"}, "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Shared\\PharmaModerne.Shared.csproj": {"projectPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Shared\\PharmaModerne.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Shared\\PharmaModerne.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Shared\\PharmaModerne.Shared.csproj", "projectName": "PharmaModerne.Shared", "projectPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Shared\\PharmaModerne.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Shared\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.UI\\PharmaModerne.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.UI\\PharmaModerne.UI.csproj", "projectName": "PharmaModerne.UI", "projectPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.UI\\PharmaModerne.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.UI\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Core\\PharmaModerne.Core.csproj": {"projectPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Core\\PharmaModerne.Core.csproj"}, "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Services\\PharmaModerne.Services.csproj": {"projectPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Services\\PharmaModerne.Services.csproj"}, "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Shared\\PharmaModerne.Shared.csproj": {"projectPath": "F:\\PHARMA2000 - Original\\PHARMA2000 - Original\\PharmaModerne\\PharmaModerne.Shared\\PharmaModerne.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}