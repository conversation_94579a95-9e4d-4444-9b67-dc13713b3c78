﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fMaquetteCnam
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fMaquetteCnam))
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.cmbAPCI = New C1.Win.C1List.C1Combo()
        Me.tMontantCNAM = New C1.Win.C1Input.C1TextBox()
        Me.lMontantCnam = New System.Windows.Forms.Label()
        Me.tDateNaissance = New C1.Win.C1Input.C1DateEdit()
        Me.cmbMalade = New C1.Win.C1List.C1Combo()
        Me.lAPCI = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.cmbLienDeParente = New C1.Win.C1List.C1Combo()
        Me.tDureeTraitement = New C1.Win.C1Input.C1TextBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.tRang = New C1.Win.C1Input.C1TextBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.tNouveauMalade = New C1.Win.C1Input.C1TextBox()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.bOk = New C1.Win.C1Input.C1Button()
        Me.Panel = New System.Windows.Forms.Panel()
        Me.gArticleDureeTraitement = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.GroupBox3.SuspendLayout()
        CType(Me.cmbAPCI, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tMontantCNAM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDateNaissance, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbMalade, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbLienDeParente, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDureeTraitement, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tRang, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNouveauMalade, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel.SuspendLayout()
        CType(Me.gArticleDureeTraitement, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.cmbAPCI)
        Me.GroupBox3.Controls.Add(Me.tMontantCNAM)
        Me.GroupBox3.Controls.Add(Me.lMontantCnam)
        Me.GroupBox3.Controls.Add(Me.tDateNaissance)
        Me.GroupBox3.Controls.Add(Me.cmbMalade)
        Me.GroupBox3.Controls.Add(Me.lAPCI)
        Me.GroupBox3.Controls.Add(Me.Label4)
        Me.GroupBox3.Controls.Add(Me.cmbLienDeParente)
        Me.GroupBox3.Controls.Add(Me.tDureeTraitement)
        Me.GroupBox3.Controls.Add(Me.Label2)
        Me.GroupBox3.Controls.Add(Me.tRang)
        Me.GroupBox3.Controls.Add(Me.Label1)
        Me.GroupBox3.Controls.Add(Me.Label3)
        Me.GroupBox3.Controls.Add(Me.tNouveauMalade)
        Me.GroupBox3.Controls.Add(Me.Label9)
        Me.GroupBox3.Location = New System.Drawing.Point(11, 43)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(920, 168)
        Me.GroupBox3.TabIndex = 0
        Me.GroupBox3.TabStop = False
        '
        'cmbAPCI
        '
        Me.cmbAPCI.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbAPCI.Caption = ""
        Me.cmbAPCI.CaptionHeight = 17
        Me.cmbAPCI.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbAPCI.ColumnCaptionHeight = 17
        Me.cmbAPCI.ColumnFooterHeight = 17
        Me.cmbAPCI.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbAPCI.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown
        Me.cmbAPCI.DropDownWidth = 400
        Me.cmbAPCI.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbAPCI.EditorFont = New System.Drawing.Font("Verdana", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbAPCI.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbAPCI.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbAPCI.Images.Add(CType(resources.GetObject("cmbAPCI.Images"), System.Drawing.Image))
        Me.cmbAPCI.ItemHeight = 22
        Me.cmbAPCI.Location = New System.Drawing.Point(319, 115)
        Me.cmbAPCI.MatchEntryTimeout = CType(2000, Long)
        Me.cmbAPCI.MaxDropDownItems = CType(5, Short)
        Me.cmbAPCI.MaxLength = 32767
        Me.cmbAPCI.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbAPCI.Name = "cmbAPCI"
        Me.cmbAPCI.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbAPCI.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbAPCI.Size = New System.Drawing.Size(181, 24)
        Me.cmbAPCI.TabIndex = 31
        Me.cmbAPCI.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbAPCI.PropBag = resources.GetString("cmbAPCI.PropBag")
        '
        'tMontantCNAM
        '
        Me.tMontantCNAM.AutoSize = False
        Me.tMontantCNAM.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tMontantCNAM.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMontantCNAM.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tMontantCNAM.Location = New System.Drawing.Point(319, 118)
        Me.tMontantCNAM.Name = "tMontantCNAM"
        Me.tMontantCNAM.Size = New System.Drawing.Size(181, 20)
        Me.tMontantCNAM.TabIndex = 29
        Me.tMontantCNAM.Tag = Nothing
        Me.tMontantCNAM.Visible = False
        Me.tMontantCNAM.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tMontantCNAM.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lMontantCnam
        '
        Me.lMontantCnam.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lMontantCnam.Location = New System.Drawing.Point(7, 118)
        Me.lMontantCnam.Name = "lMontantCnam"
        Me.lMontantCnam.Size = New System.Drawing.Size(306, 18)
        Me.lMontantCnam.TabIndex = 30
        Me.lMontantCnam.Text = "Montant Pris En Charge Par La CNAM :"
        Me.lMontantCnam.Visible = False
        '
        'tDateNaissance
        '
        Me.tDateNaissance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tDateNaissance.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.tDateNaissance.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.tDateNaissance.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDateNaissance.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDateNaissance.EmptyAsNull = True
        Me.tDateNaissance.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDateNaissance.Location = New System.Drawing.Point(664, 31)
        Me.tDateNaissance.Name = "tDateNaissance"
        Me.tDateNaissance.Size = New System.Drawing.Size(250, 22)
        Me.tDateNaissance.TabIndex = 28
        Me.tDateNaissance.Tag = Nothing
        Me.tDateNaissance.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDateNaissance.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbMalade
        '
        Me.cmbMalade.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbMalade.AutoDropDown = True
        Me.cmbMalade.Caption = ""
        Me.cmbMalade.CaptionHeight = 17
        Me.cmbMalade.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbMalade.ColumnCaptionHeight = 17
        Me.cmbMalade.ColumnFooterHeight = 17
        Me.cmbMalade.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbMalade.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbMalade.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbMalade.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbMalade.FlatStyle = C1.Win.C1List.FlatModeEnum.Standard
        Me.cmbMalade.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbMalade.Images.Add(CType(resources.GetObject("cmbMalade.Images"), System.Drawing.Image))
        Me.cmbMalade.ItemHeight = 15
        Me.cmbMalade.Location = New System.Drawing.Point(81, 33)
        Me.cmbMalade.MatchEntry = C1.Win.C1List.MatchEntryEnum.None
        Me.cmbMalade.MatchEntryTimeout = CType(2000, Long)
        Me.cmbMalade.MaxDropDownItems = CType(5, Short)
        Me.cmbMalade.MaxLength = 32767
        Me.cmbMalade.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbMalade.Name = "cmbMalade"
        Me.cmbMalade.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbMalade.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbMalade.Size = New System.Drawing.Size(419, 23)
        Me.cmbMalade.TabIndex = 0
        Me.cmbMalade.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbMalade.PropBag = resources.GetString("cmbMalade.PropBag")
        '
        'lAPCI
        '
        Me.lAPCI.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lAPCI.Location = New System.Drawing.Point(6, 118)
        Me.lAPCI.Name = "lAPCI"
        Me.lAPCI.Size = New System.Drawing.Size(307, 21)
        Me.lAPCI.TabIndex = 27
        Me.lAPCI.Text = "Code APCI (MO=Maladies ordinaire)"
        '
        'Label4
        '
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.Location = New System.Drawing.Point(7, 77)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(133, 19)
        Me.Label4.TabIndex = 25
        Me.Label4.Text = "Lien de parenté :"
        '
        'cmbLienDeParente
        '
        Me.cmbLienDeParente.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbLienDeParente.AutoDropDown = True
        Me.cmbLienDeParente.Caption = ""
        Me.cmbLienDeParente.CaptionHeight = 17
        Me.cmbLienDeParente.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbLienDeParente.ColumnCaptionHeight = 17
        Me.cmbLienDeParente.ColumnFooterHeight = 17
        Me.cmbLienDeParente.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbLienDeParente.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbLienDeParente.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbLienDeParente.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbLienDeParente.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbLienDeParente.Images.Add(CType(resources.GetObject("cmbLienDeParente.Images"), System.Drawing.Image))
        Me.cmbLienDeParente.ItemHeight = 15
        Me.cmbLienDeParente.Location = New System.Drawing.Point(146, 75)
        Me.cmbLienDeParente.MatchEntryTimeout = CType(2000, Long)
        Me.cmbLienDeParente.MaxDropDownItems = CType(5, Short)
        Me.cmbLienDeParente.MaxLength = 32767
        Me.cmbLienDeParente.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbLienDeParente.Name = "cmbLienDeParente"
        Me.cmbLienDeParente.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbLienDeParente.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbLienDeParente.Size = New System.Drawing.Size(354, 23)
        Me.cmbLienDeParente.TabIndex = 1
        Me.cmbLienDeParente.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbLienDeParente.PropBag = resources.GetString("cmbLienDeParente.PropBag")
        '
        'tDureeTraitement
        '
        Me.tDureeTraitement.AutoSize = False
        Me.tDureeTraitement.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tDureeTraitement.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDureeTraitement.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDureeTraitement.Location = New System.Drawing.Point(664, 116)
        Me.tDureeTraitement.MaxLength = 3
        Me.tDureeTraitement.Name = "tDureeTraitement"
        Me.tDureeTraitement.Size = New System.Drawing.Size(250, 20)
        Me.tDureeTraitement.TabIndex = 5
        Me.tDureeTraitement.Tag = Nothing
        Me.tDureeTraitement.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDureeTraitement.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(506, 119)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(166, 18)
        Me.Label2.TabIndex = 22
        Me.Label2.Text = "Durée de traitement :"
        '
        'tRang
        '
        Me.tRang.AutoSize = False
        Me.tRang.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tRang.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tRang.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tRang.Location = New System.Drawing.Point(664, 75)
        Me.tRang.Name = "tRang"
        Me.tRang.Size = New System.Drawing.Size(250, 20)
        Me.tRang.TabIndex = 4
        Me.tRang.Tag = Nothing
        Me.tRang.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tRang.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(506, 77)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(57, 18)
        Me.Label1.TabIndex = 20
        Me.Label1.Text = "Rang :"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.Location = New System.Drawing.Point(506, 33)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(78, 18)
        Me.Label3.TabIndex = 18
        Me.Label3.Text = "Né(e) le :"
        '
        'tNouveauMalade
        '
        Me.tNouveauMalade.AutoSize = False
        Me.tNouveauMalade.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNouveauMalade.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNouveauMalade.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNouveauMalade.Location = New System.Drawing.Point(146, 9)
        Me.tNouveauMalade.Name = "tNouveauMalade"
        Me.tNouveauMalade.Size = New System.Drawing.Size(299, 22)
        Me.tNouveauMalade.TabIndex = 17
        Me.tNouveauMalade.Tag = Nothing
        Me.tNouveauMalade.Visible = False
        Me.tNouveauMalade.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNouveauMalade.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label9
        '
        Me.Label9.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.Location = New System.Drawing.Point(7, 33)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(68, 31)
        Me.Label9.TabIndex = 16
        Me.Label9.Text = "Prénom du malade :"
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bAnnuler.Location = New System.Drawing.Point(816, 380)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(115, 45)
        Me.bAnnuler.TabIndex = 2
        Me.bAnnuler.Text = "Annuler"
        Me.bAnnuler.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bOk
        '
        Me.bOk.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bOk.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bOk.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bOk.Location = New System.Drawing.Point(695, 380)
        Me.bOk.Name = "bOk"
        Me.bOk.Size = New System.Drawing.Size(115, 45)
        Me.bOk.TabIndex = 1
        Me.bOk.Text = "OK"
        Me.bOk.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bOk.UseVisualStyleBackColor = True
        Me.bOk.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.gArticleDureeTraitement)
        Me.Panel.Controls.Add(Me.Label5)
        Me.Panel.Controls.Add(Me.GroupBox3)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.bOk)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(945, 435)
        Me.Panel.TabIndex = 5
        '
        'gArticleDureeTraitement
        '
        Me.gArticleDureeTraitement.AllowUpdate = False
        Me.gArticleDureeTraitement.AlternatingRows = True
        Me.gArticleDureeTraitement.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gArticleDureeTraitement.GroupByCaption = "Drag a column header here to group by that column"
        Me.gArticleDureeTraitement.Images.Add(CType(resources.GetObject("gArticleDureeTraitement.Images"), System.Drawing.Image))
        Me.gArticleDureeTraitement.LinesPerRow = 2
        Me.gArticleDureeTraitement.Location = New System.Drawing.Point(11, 217)
        Me.gArticleDureeTraitement.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gArticleDureeTraitement.Name = "gArticleDureeTraitement"
        Me.gArticleDureeTraitement.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gArticleDureeTraitement.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gArticleDureeTraitement.PreviewInfo.ZoomFactor = 75.0R
        Me.gArticleDureeTraitement.PrintInfo.PageSettings = CType(resources.GetObject("gArticleDureeTraitement.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gArticleDureeTraitement.Size = New System.Drawing.Size(920, 154)
        Me.gArticleDureeTraitement.TabIndex = 50
        Me.gArticleDureeTraitement.Text = "C1TrueDBGrid1"
        Me.gArticleDureeTraitement.Visible = False
        Me.gArticleDureeTraitement.PropBag = resources.GetString("gArticleDureeTraitement.PropBag")
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Cursor = System.Windows.Forms.Cursors.Default
        Me.Label5.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 18.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label5.Location = New System.Drawing.Point(6, 11)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(237, 29)
        Me.Label5.TabIndex = 11
        Me.Label5.Text = "Informations CNAM"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'fMaquetteCnam
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(945, 435)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fMaquetteCnam"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        CType(Me.cmbAPCI, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tMontantCNAM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDateNaissance, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbMalade, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbLienDeParente, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDureeTraitement, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tRang, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNouveauMalade, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel.ResumeLayout(False)
        Me.Panel.PerformLayout()
        CType(Me.gArticleDureeTraitement, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents lAPCI As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents cmbLienDeParente As C1.Win.C1List.C1Combo
    Friend WithEvents tDureeTraitement As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents tRang As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents tNouveauMalade As C1.Win.C1Input.C1TextBox
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bOk As C1.Win.C1Input.C1Button
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents cmbMalade As C1.Win.C1List.C1Combo
    Friend WithEvents tDateNaissance As C1.Win.C1Input.C1DateEdit
    Friend WithEvents tMontantCNAM As C1.Win.C1Input.C1TextBox
    Friend WithEvents lMontantCnam As System.Windows.Forms.Label
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents gArticleDureeTraitement As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents cmbAPCI As C1.Win.C1List.C1Combo
End Class
