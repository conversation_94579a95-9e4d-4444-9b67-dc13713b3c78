﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fListeDesCommandesEnInstance
    Dim cmdCommandeInstance As New SqlCommand
    Dim cbCommandeInstance As New SqlCommandBuilder
    Dim dsCommandeInstance As New DataSet
    Dim daCommandeInstance As New SqlDataAdapter
    Dim StrSQL As String = ""

    Public Shared ComfirmerMettreEnINstance As Boolean = False
    Public Shared NumeroCommande As String = ""
    Public Sub init()
        Dim I As Integer
        Try
            dsCommandeInstance.Tables("COMMANDE_INSTANCE").Clear()
        Catch ex As Exception

        End Try
        'intialisation de la gride      
        StrSQL = "SELECT NomCommandeInstance," + _
                 "NomFournisseur," + _
                 "Date," + _
                 "TotalTTC," + _
                 "TotalHT," + _
                 "Nom " + _
                 "FROM COMMANDE_INSTANCE LEFT OUTER JOIN FOURNISSEUR  " + _
                 "ON COMMANDE_INSTANCE.CodeFournisseur=FOURNISSEUR.CodeFournisseur " + _
                 "LEFT OUTER JOIN UTILISATEUR ON COMMANDE_INSTANCE.CodeOperateur=UTILISATEUR.CodeUtilisateur order by Date desc"

        cmdCommandeInstance.Connection = ConnectionServeur
        cmdCommandeInstance.CommandText = StrSQL
        daCommandeInstance = New SqlDataAdapter(cmdCommandeInstance)
        daCommandeInstance.Fill(dsCommandeInstance, "COMMANDE_INSTANCE")
        With gCommandesInstance
            .Columns.Clear()
            Try
                .DataSource = dsCommandeInstance
            Catch ex As Exception
            End Try
            .DataMember = "COMMANDE_INSTANCE"
            .Rebind(False)
            .Columns("NomCommandeInstance").Caption = "nom Commande"
            .Columns("NomFournisseur").Caption = "Fournisseur"
            .Columns("Date").Caption = "Date"
            .Columns("TotalTTC").Caption = "Total TTC"
            .Columns("TotalHT").Caption = "Total HT"
            .Columns("Nom").Caption = "Vendeur"

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns(2).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
            .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalHT").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("NomCommandeInstance").Width = 100
            .Splits(0).DisplayColumns("NomFournisseur").Width = 80
            .Splits(0).DisplayColumns("Date").Width = 100
            .Splits(0).DisplayColumns("TotalTTC").Width = 100
            .Splits(0).DisplayColumns("TotalHT").Width = 100
            .Splits(0).DisplayColumns("Nom").Width = 100

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gCommandesInstance)
        End With
        NumeroCommande = RecupererValeurExecuteScalaire("NumeroCommandeInstance", "COMMANDE_INSTANCE", "NomCommandeInstance", gCommandesInstance(0, "NomCommandeInstance"))
        AfficherDetailsCommandeInstance(NumeroCommande)

    End Sub
    Private Sub Panel_Paint(ByVal sender As System.Object, ByVal e As System.Windows.Forms.PaintEventArgs) Handles Panel.Paint

    End Sub

    Private Sub fListeDesVentesEnInstance_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        init()
    End Sub

    Private Sub gVenteInstance_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gCommandesInstance.Click

    End Sub
    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        Try
            Quote(ValeurCle)
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurCle)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function

    Private Sub gCommandesInstance_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gCommandesInstance.KeyUp
        If e.KeyData = Keys.Down Or e.KeyData = Keys.Up Then
            NumeroCommande = RecupererValeurExecuteScalaire("NumeroCommandeInstance", "COMMANDE_INSTANCE", "NomCommandeInstance", gCommandesInstance(gCommandesInstance.Row, "NomCommandeInstance"))
            AfficherDetailsCommandeInstance(NumeroCommande)
        End If

        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub gVenteInstance_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gCommandesInstance.MouseClick
        NumeroCommande = RecupererValeurExecuteScalaire("NumeroCommandeInstance", "COMMANDE_INSTANCE", "NomCommandeInstance", gCommandesInstance(gCommandesInstance.Row, "NomCommandeInstance"))
        AfficherDetailsCommandeInstance(NumeroCommande)
    End Sub
    Public Sub AfficherDetailsCommandeInstance(ByVal NumeroCommandeInstance)
        Dim I As Integer
        Try
            dsCommandeInstance.Tables("COMMANDE_INSTANCE_DETAILS").Clear()
        Catch ex As Exception

        End Try
        'intialisation de la gride      
        StrSQL = "SELECT CodeArticle,CodeABarre,Designation,Qte FROM COMMANDE_INSTANCE_DETAILS " + _
        "WHERE NumeroCommandeInstance ='" + NumeroCommandeInstance + "'"

        cmdCommandeInstance.Connection = ConnectionServeur
        cmdCommandeInstance.CommandText = StrSQL
        daCommandeInstance = New SqlDataAdapter(cmdCommandeInstance)
        daCommandeInstance.Fill(dsCommandeInstance, "COMMANDE_INSTANCE_DETAILS")

        With gDetailsCommandeInstance
            .Columns.Clear()
            Try
                .DataSource = dsCommandeInstance
            Catch ex As Exception
            End Try
            .DataMember = "COMMANDE_INSTANCE_DETAILS"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code Article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("Qte").Caption = "Qte"

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 100
            .Splits(0).DisplayColumns("Designation").Width = 300
            .Splits(0).DisplayColumns("Qte").Width = 100

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gDetailsCommandeInstance)
        End With
    End Sub

    Private Sub bOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOK.Click
        NumeroCommande = RecupererValeurExecuteScalaire("NumeroCommandeInstance", "COMMANDE_INSTANCE", "NomCommandeInstance", gCommandesInstance(gCommandesInstance.Row, "NomCommandeInstance"))
        ComfirmerMettreEnINstance = True
        Me.Hide()
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        ComfirmerMettreEnINstance = False
        Me.Hide()
    End Sub

    Private Sub gDetailsCommandeInstance_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gDetailsCommandeInstance.Click

    End Sub

    Private Sub gDetailsCommandeInstance_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gDetailsCommandeInstance.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub GroupBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GroupBox1.Enter

    End Sub

    Private Sub GroupBox1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles GroupBox1.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub GroupBox5_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GroupBox5.Enter

    End Sub

    Private Sub GroupBox5_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles GroupBox5.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub bOK_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bOK.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub bAnnuler_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bAnnuler.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub
End Class