﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fFicheFournisseur
    Public ValidationCodeTest As Boolean = False

    Public ajoutmodif As String = ""
    Public AJoutModifReglement As String = ""
    Public CodeFournisseur As String = ""
    Public NomFournisseur As String = ""

    Dim cmdFournisseur As New SqlCommand
    Dim cbFournisseur As New SqlCommandBuilder
    Dim dsFournisseur As New DataSet
    Dim daFournisseur As New SqlDataAdapter

    Dim cmdReglement As New SqlCommand
    Dim cbReglement As New SqlCommandBuilder
    Dim daReglement As New SqlDataAdapter

    Dim dsRecupereNum As New DataSet
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNumt As New SqlDataAdapter

    Dim cmdFacture As New SqlCommand
    Dim dsFacture As New DataSet
    Dim daFacture As New SqlDataAdapter

    Dim cmdFactureDetail As New SqlCommand
    Dim dsFactureDetail As New DataSet
    Dim daFactureDetail As New SqlDataAdapter

    Dim NumeroReglementAModifier As String = ""
    Dim NumeroAchatAAfficher As String = ""

    Dim BlanchirListeReglement As Boolean = False

    Dim CmdCalcul As New SqlCommand
    Dim CodeExiste As Boolean = False

    Public ConfirmerEnregistrementDepuisAchat As Boolean = False

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "114" And bConfirmer.Enabled = True Then
            bConfirmer_Click(sender, e)
        End If

        If argument = "121" And bAnnuler.Enabled = True Then
            bAnnuler_Click(sender, e)
        End If

        If argument = "116" And bAjouterReglement.Enabled = True Then
            bAjouterReglement_Click(sender, e)
        End If

        If argument = "118" And bSupprimerReglement.Enabled = True Then
            bSupprimerReglement_Click(sender, e)
        End If

        If argument = "117" And bModifierReglement.Enabled = True Then
            bModifierReglement_Click(sender, e)
        End If

    End Sub

    Public Sub Init()
        Dim StrSQL1 As String = ""
        'Dim StrSQL2 As String = ""
        'Dim StrSQL3 As String = ""

        Dim StrSQLdernierAchat As String = ""
        Dim Dernier_Date_Achat As String = ""
        Dim StrSQLSolde As String = ""
        Dim Somme_Facture As Double = 0.0
        Dim Somme_Reglement As Double = 0.0
        Dim Solde_Intial As Double = 0.0
        Dim difference As Double = 0.0
        Dim EnCours As Double = 0.0

        Dim TotalAchatFournisseur As Double = 0.0
        Dim TotalAchat As Double = 0.0

        'chargement des villes
        StrSQL1 = "SELECT DISTINCT CodeVille,NomVille FROM VILLE WHERE SupprimeVille=0 ORDER BY NomVille ASC"
        cmdFournisseur.Connection = ConnectionServeur
        cmdFournisseur.CommandText = StrSQL1
        daFournisseur = New SqlDataAdapter(cmdFournisseur)
        daFournisseur.Fill(dsFournisseur, "VILLE")
        cmbVilleFournisseur.DataSource = dsFournisseur.Tables("VILLE")
        cmbVilleFournisseur.ValueMember = "CodeVille"
        cmbVilleFournisseur.DisplayMember = "NomVille"
        cmbVilleFournisseur.ColumnWidth = 10
        cmbVilleFournisseur.ColumnHeaders = False
        cmbVilleFournisseur.Splits(0).DisplayColumns("NomVille").Width = 10
        cmbVilleFournisseur.Splits(0).DisplayColumns("CodeVille").Visible = False
        cmbVilleFournisseur.ExtendRightColumn = True

        'chargement des villes
        StrSQL1 = "SELECT DISTINCT CodeBanque,NomBanque FROM BANQUE ORDER BY NomBanque ASC"
        cmdFournisseur.Connection = ConnectionServeur
        cmdFournisseur.CommandText = StrSQL1
        daFournisseur = New SqlDataAdapter(cmdFournisseur)
        daFournisseur.Fill(dsFournisseur, "BANQUE")
        cmbBanqueFournisseur.DataSource = dsFournisseur.Tables("BANQUE")
        cmbBanqueFournisseur.ValueMember = "CodeBanque"
        cmbBanqueFournisseur.DisplayMember = "NomBanque"
        cmbBanqueFournisseur.ColumnHeaders = False
        cmbBanqueFournisseur.Splits(0).DisplayColumns("CodeBanque").Visible = False
        cmbBanqueFournisseur.Splits(0).DisplayColumns("NomBanque").Width = 10
        cmbBanqueFournisseur.ExtendRightColumn = True

        If ajoutmodif = "A" Then
            StrSQL1 = " SELECT TOP 0 * FROM Fournisseur"
        Else
            StrSQL1 = " SELECT * FROM Fournisseur WHERE CodeFournisseur = " + Quote(CodeFournisseur)
        End If

        'AfficherInformationsClient()
        cmdFournisseur.Connection = ConnectionServeur
        cmdFournisseur.CommandText = StrSQL1
        daFournisseur = New SqlDataAdapter(cmdFournisseur)
        daFournisseur.Fill(dsFournisseur, "Fournisseur")
        cbFournisseur = New SqlCommandBuilder(daFournisseur)

        If ajoutmodif = "A" And NomFournisseur <> "" Then
            tNomFournisseur.Value = NomFournisseur
        End If

        If ajoutmodif = "M" Then
            AfficherFacture()
            AfficherFactureDetail()

            LTitreFournisseur.Text = "Fiche Fournisseur : " + dsFournisseur.Tables("Fournisseur").Rows(0)("NomFournisseur")

            tCodeFournisseur.Value = dsFournisseur.Tables("Fournisseur").Rows(0)("CodeFournisseur")
            tNomFournisseur.Value = dsFournisseur.Tables("Fournisseur").Rows(0)("NomFournisseur")

            tAdresseFournisseur.Value = dsFournisseur.Tables("Fournisseur").Rows(0)("Adresse")
            cmbVilleFournisseur.SelectedValue = dsFournisseur.Tables("Fournisseur").Rows(0)("Codeville")
            tTelephoneFournisseur.Value = dsFournisseur.Tables("Fournisseur").Rows(0)("Tel")

            tFaxFournisseur.Value = dsFournisseur.Tables("Fournisseur").Rows(0)("Fax")
            tCodePostalFournisseur.Value = dsFournisseur.Tables("Fournisseur").Rows(0)("CodePostal")

            cmbBanqueFournisseur.SelectedValue = dsFournisseur.Tables("Fournisseur").Rows(0)("CodeBanque")
            tRemiseFournisseur.Value = dsFournisseur.Tables("Fournisseur").Rows(0)("Remise")

            tSoldeInitialFournisseur.Value = dsFournisseur.Tables("Fournisseur").Rows(0)("SoldeInitial")
            tDateInitialFournisseur.Value = dsFournisseur.Tables("Fournisseur").Rows(0)("DateInitiale")

            tRemarqueFournisseur.Value = dsFournisseur.Tables("Fournisseur").Rows(0)("Remarque")
            tRibFournisseur.Value = dsFournisseur.Tables("Fournisseur").Rows(0)("RIB")

            ' récupération de la dernière date d'achat pour le fournisseur concerné 
            StrSQLdernierAchat = "SELECT MAX(Date) FROM ACHAT WHERE CodeFournisseur =" + Quote(CodeFournisseur)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLdernierAchat

            Try
                Dernier_Date_Achat = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            'calcul du solde fournisseur en retranchat la somme des montant des règlements de la somme des montants des ventes 
            StrSQLSolde = "SELECT SUM(TotalTTC) FROM ACHAT LEFT OUTER JOIN FOURNISSEUR ON ACHAT.CodeFournisseur=FOURNISSEUR.CodeFournisseur WHERE ACHAT.CodeFournisseur =" + Quote(CodeFournisseur) + " AND ACHAT.Date>FOURNISSEUR.DateInitiale"
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLSolde
            Try
                Somme_Facture = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Somme_Facture = 0
                Console.WriteLine(ex.Message)
            End Try

            StrSQLSolde = " SELECT SUM(Montant) FROM REGLEMENT_FOURNISSEUR " + _
                          " LEFT OUTER JOIN FOURNISSEUR ON REGLEMENT_FOURNISSEUR.CodeFournisseur=FOURNISSEUR.CodeFournisseur " + _
                          " WHERE REGLEMENT_FOURNISSEUR.CodeFournisseur =" + Quote(CodeFournisseur) + _
                          " AND REGLEMENT_FOURNISSEUR.Date>FOURNISSEUR.DateInitiale " + _
                          " AND Encaisse=1 "

            'AND (CodeNatureReglement<> 2 AND CodeNatureReglement<> 5)

            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLSolde
            Try
                Somme_Reglement = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Somme_Reglement = 0
                Console.WriteLine(ex.Message)
            End Try

            StrSQLSolde = "SELECT SoldeInitial FROM FOURNISSEUR WHERE CodeFournisseur =" + Quote(CodeFournisseur)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLSolde
            Try
                Solde_Intial = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Solde_Intial = 0
                Console.WriteLine(ex.Message)
            End Try


            If (Dernier_Date_Achat) <> "" Then
                lDateDernierAchatFournisseur.Text = Dernier_Date_Achat
            End If
            difference = (Somme_Facture - Somme_Reglement + Solde_Intial)
            lSoldeFournisseur.Text = Convert.ToString(difference)
            tCodeFournisseur.Focus()

            'calcul du solde fournisseur en cours (somme des cheque qui ont ne sont pas encaissé) 
            StrSQLSolde = "SELECT SUM(Montant) FROM REGLEMENT_FOURNISSEUR WHERE CodeFournisseur =" + Quote(CodeFournisseur) + _
            " AND (CodeNatureReglement= 2 OR CodeNatureReglement= 5) AND Encaisse=0"
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLSolde

            Try
                EnCours = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            lEnCours.Text = EnCours.ToString
            lResteAPayer.Text = difference - EnCours

            'calcul du chiffre d'affaire fournisseur  (ainsie que le pourcentage par apport au total des achats)
            StrSQLSolde = "SELECT SUM(TotalTTC) FROM ACHAT WHERE CodeFournisseur =" + Quote(CodeFournisseur)

            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLSolde

            Try
                TotalAchatFournisseur = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
           

            StrSQLSolde = "SELECT SUM(TotalTTC) FROM ACHAT "

            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLSolde

            Try
                TotalAchat = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            '------------------------------ verouillage du code pour interdire la modification --------
            tCodeFournisseur.Enabled = False

            '------------------------------------------------------------------------------------------

            Try
                cmdFournisseur.Connection = ConnectionServeur
                cmdFournisseur.CommandText = " SELECT SUM(TTC) FROM Vue_StatistiqueDesFournisseurs WHERE YEAR(date) = YEAR(" & Quote(Date.Now.Date) & ")"
                Dim TotalAnnuel As Decimal = cmdFournisseur.ExecuteScalar()

                cmdFournisseur.Connection = ConnectionServeur
                cmdFournisseur.CommandText = " SELECT * FROM Vue_StatistiqueDesFournisseurs WHERE Vue_StatistiqueDesFournisseurs.CodeFournisseur = " & Quote(CodeFournisseur) & " AND YEAR(date) = YEAR(" & Quote(Date.Now.Date) & ")"
                daFournisseur = New SqlDataAdapter(cmdFournisseur)
                daFournisseur.Fill(dsFournisseur, "StatistiqueDesFournisseurs")
                tTotalAchatHT.Value = dsFournisseur.Tables("StatistiqueDesFournisseurs").Rows(0).Item("HT")
                tTotalAchatTTC.Value = dsFournisseur.Tables("StatistiqueDesFournisseurs").Rows(0).Item("TTC")
                tTotalTVA.Value = dsFournisseur.Tables("StatistiqueDesFournisseurs").Rows(0).Item("TVA")
            Catch ex As Exception
                tTotalAchatHT.Value = 0
                tTotalAchatTTC.Value = 0
                tTotalTVA.Value = 0
            End Try


        End If
        '------------------------------ verouillage du solde et du de la date du dernier achat qui 
        '---------------------------------seront calculés automatiquement --------
        'lSoldeFournisseur.Enabled = False
        'tDateDernierAchatFournisseur.Enabled = False
        'lEnCours.Enabled = False
        'lResteAPayer.Enabled = False

        dtEcheance.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtEcheance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        TAB.TabPages(0).Show()

        C1DockingTabPage1.Focus()
        grbCodeNom.Focus()

        tCodeFournisseur.Focus()


    End Sub

    Private Sub AfficherFacture()
        Dim I As Integer
        dsFacture.Clear()
        cmdFacture.CommandText = " SELECT " + _
                                 " NumeroAchat, " + _
                                 " DateBlFacture as Date,  " + _
                                 " TotalHT, " + _
                                 " TotalRemise, " + _
                                 " TVA, " + _
                                 " TotalTTC " + _
                                 " FROM ACHAT " + _
                                 " WHERE CodeFournisseur = " + Quote(CodeFournisseur) + _
                                 " ORDER BY Date DESC"

        cmdFacture.Connection = ConnectionServeur
        daFacture = New SqlDataAdapter(cmdFacture)
        daFacture.Fill(dsFacture, "FACTURE")

        With gAchat
            .Columns.Clear()
            .DataSource = dsFacture
            .DataMember = "FACTURE"
            .Rebind(False)
            .Columns("NumeroAchat").Caption = "Numéro"
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("NumeroAchat").Width = 160
            '.Splits(0).DisplayColumns("CodeForme").Style.HorizontalAlignment = AlignHorzEnum.Center
            '.Splits(0).DisplayColumns("LibelleForme").Width = 80
            '.Splits(0).DisplayColumns("LibelleForme").Style.HorizontalAlignment = AlignHorzEnum.Near

            '.Splits(0).DisplayColumns("CodeForme").Locked = True
            '.Splits(0).DisplayColumns("LibelleForme").Locked = True

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gAchat)
        End With

    End Sub

    Private Sub AfficherFactureDetail()

        If gAchat.RowCount <> 0 Then
            Dim I As Integer
            dsFactureDetail.Clear()


            cmdFactureDetail.CommandText = " SELECT " + _
                                           " CodeArticle AS Code " + _
                                           " ,Designation AS Designation  " + _
                                           " ,FORME_ARTICLE.LibelleForme AS Forme " + _
                                           " ,Qte AS Qté " + _
                                           " ,PrixAchatHT " + _
                                           " ,TotalAchatHT " + _
                                           " FROM ACHAT_DETAILS " + _
                                           " LEFT JOIN FORME_ARTICLE ON ACHAT_DETAILS.CodeForme = FORME_ARTICLE.CodeForme  " + _
                                           " WHERE NumeroAchat = " + Quote(gAchat(gAchat.Row, "NumeroAchat")) + _
                                           " ORDER BY Designation"

            cmdFactureDetail.Connection = ConnectionServeur
            daFactureDetail = New SqlDataAdapter(cmdFactureDetail)
            daFactureDetail.Fill(dsFactureDetail, "FACTURE_DETAIL")

            With gDetailAchat
                .Columns.Clear()
                .DataSource = dsFactureDetail
                .DataMember = "FACTURE_DETAIL"
                .Rebind(False)
                '.Columns("NumeroVente").Caption = "Numéro"
                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                .Splits(0).DisplayColumns("Designation").Width = 300
                '.Splits(0).DisplayColumns("CodeForme").Style.HorizontalAlignment = AlignHorzEnum.Center
                '.Splits(0).DisplayColumns("LibelleForme").Width = 80
                '.Splits(0).DisplayColumns("LibelleForme").Style.HorizontalAlignment = AlignHorzEnum.Near

                '.Splits(0).DisplayColumns("CodeForme").Locked = True
                '.Splits(0).DisplayColumns("LibelleForme").Locked = True

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                'Style du Caractere et du grid
                ParametreGrid(gDetailAchat)
            End With
        End If
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        If dsFournisseur.HasChanges Then
            If MsgBox("Voulez vous vraiment annuler les modifications de ce Fournisseur ?", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                Me.Hide()
            End If
        Else
            If NomFournisseur = "" Then
                'fMain.Tab.SelectedTab.Dispose()
                Me.Hide()
            Else
                Me.Hide()
                ConfirmerEnregistrementDepuisAchat = False
            End If

        End If

    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        Dim dr As DataRow
        Dim StrSQL As String = ""

        If tCodeFournisseur.Text = "" Then
            MsgBox("Veuillez saisir le code du Fournisseur !", MsgBoxStyle.Critical, "Erreur")
            tCodeFournisseur.Focus()
            Exit Sub
        End If

        If tNomFournisseur.Text = "" Then
            MsgBox("Veuillez saisir le nom du Fournisseur !", MsgBoxStyle.Critical, "Erreur")
            tNomFournisseur.Focus()
            Exit Sub
        End If

        '------------------------------ demande du mot de passe
        Dim myMotDePasse As New fMotDePasse
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If
        '------------------------------------------------------

        If CodeExiste = True Then
            MsgBox("Code Fournisseur existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
            tCodeFournisseur.Focus()
            Exit Sub
        End If

        If ajoutmodif = "A" Then
            With dsFournisseur
                dr = .Tables("Fournisseur").NewRow

                dr.Item("CodeFournisseur") = tCodeFournisseur.Text
                dr.Item("NomFournisseur") = tNomFournisseur.Text

                dr.Item("Adresse") = tAdresseFournisseur.Text

                If cmbVilleFournisseur.Text <> "" Then
                    dr.Item("Codeville") = cmbVilleFournisseur.SelectedValue
                Else
                    dr.Item("Codeville") = DBNull.Value
                End If

                dr.Item("Tel") = tTelephoneFournisseur.Text
                dr.Item("Fax") = tFaxFournisseur.Text
                dr.Item("CodePostal") = tCodePostalFournisseur.Text

                Try
                    dr.Item("CodeBanque") = cmbBanqueFournisseur.SelectedValue
                Catch
                    dr.Item("CodeBanque") = 0
                End Try

                dr.Item("Rib") = tRibFournisseur.Text
                dr.Item("Remise") = CDec(tRemiseFournisseur.Text)
                dr.Item("Remarque") = tRemarqueFournisseur.Text

                dr.Item("SoldeInitial") = tSoldeInitialFournisseur.Text
                If tDateInitialFournisseur.Text <> "" Then
                    dr.Item("DateInitiale") = Convert.ToDateTime(tDateInitialFournisseur.Text)
                End If
                dr.Item("Supprimer") = False

                .Tables("Fournisseur").Rows.Add(dr)
            End With

        ElseIf ajoutmodif = "M" Then
            With dsFournisseur.Tables("Fournisseur")
                dr = .Rows(0)
                dr.Item("CodeFournisseur") = tCodeFournisseur.Text
                dr.Item("NomFournisseur") = tNomFournisseur.Text

                dr.Item("Adresse") = tAdresseFournisseur.Text
                If cmbVilleFournisseur.Text <> "" Then
                    dr.Item("Codeville") = cmbVilleFournisseur.SelectedValue
                End If
                dr.Item("Tel") = tTelephoneFournisseur.Text
                dr.Item("Fax") = tFaxFournisseur.Text
                dr.Item("CodePostal") = tCodePostalFournisseur.Text

                Try
                    dr.Item("CodeBanque") = cmbBanqueFournisseur.SelectedValue
                Catch
                    dr.Item("CodeBanque") = 0
                End Try

                dr.Item("Rib") = tRibFournisseur.Text
                dr.Item("Remise") = CDec(tRemiseFournisseur.Text)
                dr.Item("Remarque") = tRemarqueFournisseur.Text

                dr.Item("SoldeInitial") = tSoldeInitialFournisseur.Text
                dr.Item("DateInitiale") = tDateInitialFournisseur.Text

            End With

        End If
        Try

            cmdFournisseur.Connection = ConnectionServeur
            cmdFournisseur.CommandText = " SELECT TOP 0 * FROM Fournisseur"
            daFournisseur = New SqlDataAdapter(cmdFournisseur)
            daFournisseur.Fill(dsFournisseur, "Fournisseur")
            cbFournisseur = New SqlCommandBuilder(daFournisseur)
            daFournisseur.Update(dsFournisseur, "Fournisseur")

            daFournisseur.Update(dsFournisseur, "Fournisseur")

            If ajoutmodif = "A" Then
                InsertionDansLog("AJOUT_FOURNISSEUR", "L ajout du fournisseur " + tNomFournisseur.Text, CodeOperateur, System.DateTime.Now, "FOURNISSEUR", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
            ElseIf ajoutmodif = "M" Then
                InsertionDansLog("MODIFICATION_FOURNISSEUR", "La modification du fournisseur " + tNomFournisseur.Text, CodeOperateur, System.DateTime.Now, "FOURNISSEUR", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
            End If

            If NomFournisseur = "" Then
                'fMain.Tab.SelectedTab.Dispose()
                Me.Hide()
            Else
                Me.Hide()
                ConfirmerEnregistrementDepuisAchat = True
            End If

        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsFournisseur.Reset()
            Me.Init()
        End Try

    End Sub

    Private Sub cmbBanqueFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbBanqueFournisseur.KeyUp
        'Recherche_Automatique_fiche(e, cmbBanqueFournisseur, cmbBanqueFournisseur.Columns("NomBanque"))
        If e.KeyCode = Keys.Enter Then
            cmbBanqueFournisseur.Text = cmbBanqueFournisseur.WillChangeToText
            tRibFournisseur.Focus()
        Else
            cmbBanqueFournisseur.OpenCombo()
        End If
    End Sub

    Private Sub cmbVilleFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        'Recherche_Automatique_fiche(e, cmbVilleFournisseur, cmbVilleFournisseur.Columns("NomVille"))
        If e.KeyCode = Keys.Enter Then
            cmbVilleFournisseur.Text = cmbVilleFournisseur.WillChangeToText
        Else
            cmbVilleFournisseur.OpenCombo()
        End If
    End Sub

    Private Sub tCodeFournisseur_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tCodeFournisseur.KeyPress
        If IsNumeric(e.KeyChar) = False And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back Then
            e.Handled = True
        End If
    End Sub

    Private Sub tCodeFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeFournisseur.KeyUp
        '' ''Dim StrSQLtest As String = ""
        '' ''dsRecupereNum.Clear()
        '' ''If ajoutmodif = "A" Then
        '' ''    StrSQLtest = " SELECT * FROM FOURNISSEUR WHERE CodeFournisseur=" + Quote(tCodeFournisseur.Text)
        '' ''    cmdRecupereNum.Connection = ConnectionServeur
        '' ''    cmdRecupereNum.CommandText = StrSQLtest
        '' ''    daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
        '' ''    daRecupereNumt.Fill(dsRecupereNum, "FOURNISSEUR")

        '' ''    If dsRecupereNum.Tables("FOURNISSEUR").Rows.Count <> 0 Then
        '' ''        lTest.Text = "Code non valide déja existe"
        '' ''        lTest.ForeColor = Color.OrangeRed
        '' ''        lTest.Visible = True
        '' ''        ValidationCodeTest = False
        '' ''        CodeExiste = True
        '' ''        tCodeFournisseur.SelectAll()
        '' ''        Exit Sub
        '' ''    Else
        '' ''        lTest.Text = "Code valide"
        '' ''        lTest.ForeColor = Color.LimeGreen
        '' ''        lTest.Visible = True
        '' ''        ValidationCodeTest = True
        '' ''        CodeExiste = False
        '' ''    End If
        '' ''End If
        If tCodeFournisseur.Text = "" Then
            lTest.Visible = False
        End If

        If e.KeyCode = Keys.Enter Then
            If tCodeFournisseur.Text <> "" Then
                tNomFournisseur.Focus()
                Exit Sub
            End If
        End If
    End Sub


    Private Sub tCodeFournisseur_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeFournisseur.LostFocus
        Dim StrSQLtest As String = ""
        dsRecupereNum.Clear()
        If ajoutmodif = "A" Then
            StrSQLtest = " SELECT * FROM FOURNISSEUR WHERE CodeFournisseur=" + Quote(tCodeFournisseur.Text)
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQLtest
            daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
            daRecupereNumt.Fill(dsRecupereNum, "FOURNISSEUR")

            If dsRecupereNum.Tables("FOURNISSEUR").Rows.Count <> 0 Then
                lTest.Text = "Code non valide déja existe"
                lTest.ForeColor = Color.OrangeRed
                lTest.Visible = True
                ValidationCodeTest = False
                CodeExiste = True
                tCodeFournisseur.SelectAll()
                Exit Sub
            Else
                lTest.Text = "Code valide"
                lTest.ForeColor = Color.LimeGreen
                lTest.Visible = True
                ValidationCodeTest = True
                CodeExiste = False
            End If
        End If


        lTest.Visible = False
        'If ValidationCodeTest = False Then
        '    MsgBox("Code du fournisseur non valide !", MsgBoxStyle.Critical, "Erreur")
        '    tCodeFournisseur.Focus()
        'End If
    End Sub

    Private Sub tCodeFournisseur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeFournisseur.TextChanged
        Dim StrSQLtest As String = ""
        dsRecupereNum.Clear()
        If ajoutmodif = "A" Then
            StrSQLtest = " SELECT * FROM FOURNISSEUR WHERE CodeFournisseur=" + Quote(tCodeFournisseur.Text)
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQLtest
            daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
            daRecupereNumt.Fill(dsRecupereNum, "FOURNISSEUR")

            If dsRecupereNum.Tables("FOURNISSEUR").Rows.Count <> 0 Then
                lTest.Text = "Code non valide déja existe"
                lTest.ForeColor = Color.OrangeRed
                lTest.Visible = True
                ValidationCodeTest = False
                CodeExiste = True
            Else
                lTest.Text = "Code valide"
                lTest.ForeColor = Color.LimeGreen
                lTest.Visible = True
                ValidationCodeTest = True
                CodeExiste = False
            End If
        End If
        If tCodeFournisseur.Text = "" Then
            lTest.Visible = False
        End If
    End Sub

    Private Sub cmbVilleFournisseur_KeyUp1(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbVilleFournisseur.KeyUp
        'Recherche_Automatique_fiche(e, cmbVilleFournisseur, cmbVilleFournisseur.Columns("NomVille"))
        If e.KeyCode = Keys.Enter Then
            cmbVilleFournisseur.Text = cmbVilleFournisseur.WillChangeToText
            tTelephoneFournisseur.Focus()
        Else
            cmbVilleFournisseur.OpenCombo()
        End If
    End Sub

    Private Sub tChiffreAffairePourcent_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
    End Sub

    Private Sub tChiffreAffairePourcent_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub C1DockingTab1_Enter(ByVal sender As Object, ByVal e As System.EventArgs) Handles TAB.Enter
        AfficherReglement(0)
    End Sub
    Public Sub AfficherReglement(ByVal NumAchat As String)
        Dim StrSQL As String = ""
        Dim NumeroPremierReglement As Integer = 0
        Dim i As Integer = 0
        Dim conditionReglement As String = " "

        'chargement des règlements

        If (dsFournisseur.Tables.IndexOf("REGLEMENT_FOURNISSEUR_AFFICHAGE") > -1) Then
            dsFournisseur.Tables("REGLEMENT_FOURNISSEUR_AFFICHAGE").Clear()
        End If

        If rdbTous.Checked = True Then
            conditionReglement = "  "
        ElseIf rdbRegle.Checked = True Then
            conditionReglement = " And Encaisse = 1 "
        ElseIf rdbNonRegle.Checked = True Then
            conditionReglement = " AND Encaisse = 0"
        End If

        StrSQL = "SELECT NumeroReglementFournisseur," + _
                 "LibelleReglement AS libelle ," + _
                 "NATURE_REGLEMENT.LibelleNatureReglement AS LibelleNatureReglement," + _
                 "REGLEMENT_FOURNISSEUR.CodeNatureReglement, " + _
                 "Date," + _
                 "DateEcheance," + _
                 "Montant," + _
                 "NumeroCheque," + _
                 "NumeroPoste," + _
                 "NomInscritSurLeCheque," + _
                 "CodeFournisseur," + _
                 "BANQUE.NomBanque," + _
                 "REGLEMENT_FOURNISSEUR.CodeBanque," + _
                 "Encaisse " + _
                 "FROM " + _
                 "REGLEMENT_FOURNISSEUR " + _
                 "LEFT OUTER JOIN NATURE_REGLEMENT ON REGLEMENT_FOURNISSEUR.CodeNatureReglement=NATURE_REGLEMENT.CodeNatureReglement " + _
                 "LEFT OUTER JOIN BANQUE ON REGLEMENT_FOURNISSEUR.CodeBanque=BANQUE.CodeBanque " + _
                 "WHERE REGLEMENT_FOURNISSEUR.CodeFournisseur='" + tCodeFournisseur.Text + "'" & conditionReglement


        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsFournisseur, "REGLEMENT_FOURNISSEUR_AFFICHAGE")
        cbReglement = New SqlCommandBuilder(daReglement)

        With gReglements
            .Columns.Clear()
            Try
                .DataSource = dsFournisseur
            Catch ex As Exception
            End Try
            .DataMember = "REGLEMENT_FOURNISSEUR_AFFICHAGE"
            .Rebind(False)
            .Columns("NumeroReglementFournisseur").Caption = "Numero reg"
            .Columns("libelle").Caption = "libellé"
            .Columns("LibelleNatureReglement").Caption = "Nature"
            .Columns("Date").Caption = "Date"
            .Columns("DateEcheance").Caption = "Date Echéance"
            .Columns("Montant").Caption = "Montant"
            .Columns("NumeroCheque").Caption = "Numero Cheque "
            .Columns("NumeroPoste").Caption = "Numero Poste"
            .Columns("NomInscritSurLeCheque").Caption = "Nom inscrit"
            .Columns("CodeFournisseur").Caption = "Code Client"
            .Columns("NomBanque").Caption = "BANQUE"
            .Columns("Encaisse").Caption = "Encaissé"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns(2).Style.HorizontalAlignment = AlignHorzEnum.Near
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("NumeroReglementFournisseur").Width = 50
            .Splits(0).DisplayColumns("libelle").Width = 170
            .Splits(0).DisplayColumns("LibelleNatureReglement").Width = 80
            .Splits(0).DisplayColumns("CodeNatureReglement").Visible = False
            .Splits(0).DisplayColumns("Date").Width = 70
            .Splits(0).DisplayColumns("DateEcheance").Width = 70
            .Splits(0).DisplayColumns("Montant").Width = 50
            .Splits(0).DisplayColumns("NumeroCheque").Width = 70
            .Splits(0).DisplayColumns("NumeroPoste").Width = 0
            .Splits(0).DisplayColumns("NumeroPoste").Visible = False
            .Splits(0).DisplayColumns("NomInscritSurLeCheque").Width = 50
            .Splits(0).DisplayColumns("CodeFournisseur").Width = 50
            .Splits(0).DisplayColumns("NomBanque").Width = 60
            .Splits(0).DisplayColumns("CodeBanque").Visible = False
            .Splits(0).DisplayColumns("Encaisse").Width = 50

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

        End With
        'NumeroReglementAModifier = gReglements(0, "NumeroReglementFournisseur")

        StrSQL = "SELECT TOP(1) REGLEMENT_FOURNISSEUR.NumeroReglementFournisseur FROM REGLEMENT_FOURNISSEUR LEFT OUTER JOIN REGLEMENT_FOURNISSEUR_ACHAT" + _
               " ON REGLEMENT_FOURNISSEUR.NumeroReglementFournisseur=REGLEMENT_FOURNISSEUR_ACHAT.NumeroReglementFournisseur WHERE NumeroAchat='" + _
               NumAchat.ToString + "' ORDER BY REGLEMENT_FOURNISSEUR.Date"

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL

        Try
            NumeroPremierReglement = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        If NumeroPremierReglement <> 0 Then
            For i = 0 To dsFournisseur.Tables("REGLEMENT_FOURNISSEUR_AFFICHAGE").Rows.Count - 1
                If gReglements(i, "NumeroReglementFournisseur") = NumeroPremierReglement Then
                    gReglements.MoveRelative(i)
                End If
            Next
        End If

        If NumeroAchatAAfficher = "" And BlanchirListeReglement = False Then
            NumeroReglementAModifier = gReglements(0, "NumeroReglementFournisseur").ToString
            If NumeroReglementAModifier = "" Then
                AfficherAchat(0)
            Else
                AfficherAchat(NumeroReglementAModifier)
            End If

        End If
    End Sub

    Public Sub AfficherAchat(ByVal NumReglement As Integer)

        Dim StrSQL As String = ""
        Dim i As Integer = 0
        Dim NumeroPremierAchat As String = ""
        Dim x As Integer = 0

        If (dsFournisseur.Tables.IndexOf("ACHAT_REGLEMENT_FOURNISSEUR_AFFICHAGE") > -1) Then
            dsFournisseur.Tables("ACHAT_REGLEMENT_FOURNISSEUR_AFFICHAGE").Clear()
        End If

        ' chargement des achat 

        'date
        StrSQL = "SELECT NumeroAchat," + _
                 "DateBlFacture as Date," + _
                 "[NumeroBL/Facture] as NumeroBL," + _
                 "TotalHT," + _
                 "TotalTTC," + _
                 "TVA," + _
                 "TotalRemise AS Remise " + _
                 "FROM ACHAT " + _
                 "WHERE CodeFournisseur = " + Quote(tCodeFournisseur.Text) + _
                 " ORDER BY DateBLFacture"

        'ORDER BY Date


        '"' AND NumeroAchat IN(SELECT NumeroAchat " + _
        '         "FROM REGLEMENT_FOURNISSEUR_ACHAT )" + _

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsFournisseur, "ACHAT_REGLEMENT_FOURNISSEUR_AFFICHAGE")
        cbReglement = New SqlCommandBuilder(daReglement)

        With gAchats
            .Columns.Clear()
            Try
                .DataSource = dsFournisseur
            Catch ex As Exception
            End Try
            .DataMember = "ACHAT_REGLEMENT_FOURNISSEUR_AFFICHAGE"
            .Rebind(False)
            .Columns("NumeroAchat").Caption = "Numero Achat"
            .Columns("Date").Caption = "Date"
            .Columns("TotalHT").Caption = "Total HT"
            .Columns("TotalTTC").Caption = "Total TTC"
            .Columns("TVA").Caption = "TVA"
            .Columns("Remise").Caption = "Remise"

            .Columns("NumeroBL").Caption = "N° BL/Facture"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("NumeroAchat").Width = 180
            .Splits(0).DisplayColumns("Date").Width = 150
            .Splits(0).DisplayColumns("NumeroBL").Width = 150
            .Splits(0).DisplayColumns("TotalHT").Width = 100
            .Splits(0).DisplayColumns("TotalTTC").Width = 100
            .Splits(0).DisplayColumns("TVA").Width = 100
            .Splits(0).DisplayColumns("Remise").Width = 100

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

        End With

        StrSQL = "SELECT TOP(1) ACHAT.NumeroAchat FROM ACHAT LEFT OUTER JOIN REGLEMENT_FOURNISSEUR_ACHAT" + _
                 " ON ACHAT.NumeroAchat=REGLEMENT_FOURNISSEUR_ACHAT.NumeroAchat WHERE NumeroReglementFournisseur=" + _
                 NumReglement.ToString + " ORDER BY ACHAT.Date"

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL

        Try
            NumeroPremierAchat = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        If NumeroPremierAchat <> "" Then
            For i = 0 To dsFournisseur.Tables("ACHAT_REGLEMENT_FOURNISSEUR_AFFICHAGE").Rows.Count - 1
                If gAchats(i, "NumeroAchat") = NumeroPremierAchat Then
                    gAchats.MoveRelative(i)
                End If
            Next
        Else
            gAchats.MoveFirst()
        End If

    End Sub

    Private Sub bAjouterReglement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterReglement.Click

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(3, "AJOUT_REGLEMENT_FOURNISSEUR") = "False" Then
            Exit Sub
        End If

        Dim MyReglement As New fReglementFournisseur
        MyReglement.ajoutmodif = "A"
        MyReglement.Solde = lSoldeFournisseur.Text
        MyReglement.CodeFournisseur = tCodeFournisseur.Text
        MyReglement.init()
        MyReglement.ShowDialog()
        AfficherReglement(0)
    End Sub

    Private Sub bModifierReglement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifierReglement.Click

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(3, "MODIFICATION_REGLEMENT_FOURNISSEUR") = "False" Then
            Exit Sub
        End If

        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = "SELECT DemandeMotDePasse FROM [POINTS_CONTROLE] WHERE CodePointControle LIKE 'MODIFICATION_REGLEMENT_FOURNISSEUR' AND CodeModule=3"

        'Try
        '    DemandePotDePasse = cmd.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'If DemandePotDePasse = True Then
        '    '------------------------------ demande du mot de passe
        '    Dim myMotDePasse As New fMotDePasse
        '    myMotDePasse.ShowDialog()

        '    ConfirmerEnregistrer = fMotDePasse.Confirmer
        '    CodeOperateur = fMotDePasse.CodeOperateur

        '    myMotDePasse.Dispose()
        '    myMotDePasse.Close()

        '    If ConfirmerEnregistrer = False Then
        '        Exit Sub
        '    End If

        '    cmd.Connection = ConnectionServeur
        '    cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'MODIFICATION_REGLEMENT_FOURNISSEUR' AND CodeUtilisateur='" + CodeOperateur + "'"

        '    Try
        '        If cmd.ExecuteScalar() < 1 Then
        '            MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
        '            Exit Sub
        '        End If
        '    Catch ex As Exception
        '        Console.WriteLine(ex.Message)
        '    End Try
        'End If

        If gReglements.RowCount = 0 Then
            MsgBox("Liste vide !", MsgBoxStyle.OkOnly)
            Exit Sub
        End If

        If gReglements(gReglements.Row, "Encaisse") = True Then
            MsgBox("Il est interdit de modifier un réglement encaissé !", MsgBoxStyle.OkOnly)
            Exit Sub
        End If

        Dim MyReglement As New fReglementFournisseur
        MyReglement.CodeReglement = gReglements(gReglements.Row, "NumeroReglementFournisseur")
        MyReglement.CodeFournisseur = tCodeFournisseur.Text
        MyReglement.Solde = lSoldeFournisseur.Text
        MyReglement.ajoutmodif = "M"
        MyReglement.init()
        MyReglement.ShowDialog()
        AfficherReglement(0)

    End Sub

    Private Sub gReglements_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gReglements.Click

    End Sub

    Private Sub gReglements_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gReglements.FetchRowStyle
        Dim NumeroReglement As Integer = gReglements.Columns("NumeroReglementFournisseur").CellText(e.Row).ToString
        Dim StrSQL As String = ""
        Dim TestRegleOuNon As Integer = 0

        StrSQL = "SELECT count(NumeroReglementFournisseur) FROM REGLEMENT_FOURNISSEUR_ACHAT WHERE NumeroReglementFournisseur=" + _
                    NumeroReglement.ToString + " AND NumeroAchat='" + NumeroAchatAAfficher + "'"

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL

        Try
            TestRegleOuNon = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        If TestRegleOuNon > 0 Then
            ' le coloriage 
            e.CellStyle.BackColor = System.Drawing.Color.Blue
            e.CellStyle.ForeColor = Color.White
        End If
    End Sub

    Private Sub gAchats_DoubleClick(sender As Object, e As System.EventArgs) Handles gAchats.DoubleClick
        Dim MyVenteAffiche As New fAchatJusteAffichage
        MyVenteAffiche.NumeroAchat = gAchats(gAchats.Row, "NumeroAchat")
        MyVenteAffiche.ShowDialog()
        MyVenteAffiche.Close()
        MyVenteAffiche.Dispose()
    End Sub
    Private Sub gAchats_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gAchats.FetchRowStyle
        Dim NumeroAchat As String = gAchats.Columns("NumeroAchat").CellText(e.Row).ToString
        Dim StrSQL As String = ""
        Dim TestRegleOuNon As Integer = 0

        StrSQL = "SELECT count(NumeroReglementFournisseur) FROM REGLEMENT_FOURNISSEUR_ACHAT WHERE NumeroReglementFournisseur=" + _
                    NumeroReglementAModifier.ToString + " AND NumeroAchat='" + NumeroAchat + "'"

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL

        Try
            TestRegleOuNon = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        If TestRegleOuNon > 0 Then
            ' le coloriage 
            e.CellStyle.BackColor = System.Drawing.Color.Lime
            e.CellStyle.ForeColor = Color.Black
        End If
    End Sub

    Private Sub gReglements_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gReglements.MouseClick
        Dim I As Integer = 0
        Dim index As Integer = 0

        NumeroReglementAModifier = gReglements(gReglements.Row, "NumeroReglementFournisseur")

        NumeroAchatAAfficher = ""
        BlanchirListeReglement = True
        AfficherReglement("")
        BlanchirListeReglement = False
        For I = 0 To gReglements.RowCount - 1
            If gReglements(I, "NumeroReglementFournisseur") = NumeroReglementAModifier Then
                index = I
            End If
        Next

        gReglements.MoveRelative(index)
        gReglements.HighLightRowStyle.BackColor = Color.Blue
        If NumeroReglementAModifier <> "" Then
            AfficherAchat(NumeroReglementAModifier)
        End If

    End Sub
    Private Sub gAchats_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gAchats.MouseClick
        Dim I As Integer = 0
        Dim index As Integer = 0

        NumeroAchatAAfficher = gAchats(gAchats.Row, "NumeroAchat")
        NumeroReglementAModifier = 0
        AfficherAchat(0)
        For I = 0 To gAchats.RowCount - 1
            If gAchats(I, "NumeroAchat") = NumeroAchatAAfficher Then
                index = I
            End If
        Next

        gAchats.MoveRelative(index)
        gAchats.HighLightRowStyle.BackColor = Color.Lime
        AfficherReglement(NumeroAchatAAfficher)

    End Sub

    Private Sub bSupprimerReglement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerReglement.Click

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(3, "SUPPRESSION_REGLEMENT_FOURNISSEUR") = "False" Then
            Exit Sub
        Else
            CodeOperateur = ControleDAcces(3, "SUPPRESSION_REGLEMENT_FOURNISSEUR")
        End If
        If CodeOperateur = "" Then
            CodeOperateur = CodeUtilisateur
        End If

        Dim NumeroDesVentesRegle As Integer = 0
        Dim StrSQL As String = ""

        Connect()
        If gReglements.RowCount = 0 Then
            MsgBox("Liste Vide", MsgBoxStyle.OkOnly)
        Else

            If gReglements(gReglements.Row, "Encaisse") = True Then
                MsgBox("Il est interdit de supprimer un réglement encaissé !", MsgBoxStyle.OkOnly)
                Exit Sub
            End If

            If NumeroReglementAModifier = "" Then
                MsgBox("Vous devez sélectionner un règlement", MsgBoxStyle.OkOnly)
                Exit Sub
            End If
            If MsgBox("Voulez vous vraiment supprimer ce Règlement  ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then

                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM REGLEMENT_FOURNISSEUR_ACHAT WHERE NumeroReglementFournisseur = " + NumeroReglementAModifier.ToString
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try

                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM REGLEMENT_FOURNISSEUR WHERE NumeroReglementFournisseur = " + NumeroReglementAModifier.ToString
                    cmd.ExecuteNonQuery()
                    InsertionDansLog("SUPPRESSION_REGLEMENT_FOURNISSEUR", "La suppression du règlement du fournisseur n°" + NumeroReglementAModifier.ToString, CodeOperateur, System.DateTime.Now, "FOURNISSEUR", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try

            End If
        End If
        AfficherReglement(0)
    End Sub

    Private Sub tNomFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNomFournisseur.KeyUp
        If e.KeyCode = Keys.Enter Then
            tAdresseFournisseur.Focus()
            Exit Sub
        End If
    End Sub


  
    Private Sub tNomFournisseur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNomFournisseur.TextChanged

    End Sub

    Private Sub tAdresseFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tAdresseFournisseur.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbVilleFournisseur.Focus()
            Exit Sub
        End If
    End Sub

    Private Sub tAdresseFournisseur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tAdresseFournisseur.TextChanged

    End Sub

    Private Sub tTelephoneFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTelephoneFournisseur.KeyUp
        If e.KeyCode = Keys.Enter Then
            tFaxFournisseur.Focus()
            Exit Sub
        End If
    End Sub

    Private Sub tTelephoneFournisseur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tTelephoneFournisseur.TextChanged

    End Sub

    Private Sub tFaxFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tFaxFournisseur.KeyUp
        If e.KeyCode = Keys.Enter Then
            tCodePostalFournisseur.Focus()
            Exit Sub
        End If
    End Sub

    Private Sub tFaxFournisseur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tFaxFournisseur.TextChanged

    End Sub

    Private Sub tCodePostalFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodePostalFournisseur.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbBanqueFournisseur.Focus()
            Exit Sub
        End If
    End Sub

    Private Sub tCodePostalFournisseur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodePostalFournisseur.TextChanged

    End Sub

    Private Sub tRibFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRibFournisseur.KeyUp
        If e.KeyCode = Keys.Enter Then
            tRemiseFournisseur.Focus()
            Exit Sub
        End If
    End Sub

    Private Sub tRibFournisseur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tRibFournisseur.TextChanged

    End Sub

    Private Sub tRemiseFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRemiseFournisseur.KeyUp
        If e.KeyCode = Keys.Enter Then
            tSoldeInitialFournisseur.Focus()
            Exit Sub
        End If
    End Sub

    Private Sub tRemiseFournisseur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tRemiseFournisseur.TextChanged

    End Sub

    Private Sub tSoldeInitialFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tSoldeInitialFournisseur.KeyUp
        If e.KeyCode = Keys.Enter Then
            tDateInitialFournisseur.Focus()
            Exit Sub
        End If
    End Sub

    Private Sub tSoldeInitialFournisseur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tSoldeInitialFournisseur.TextChanged

    End Sub

    Private Sub tDateInitialFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tDateInitialFournisseur.KeyUp
        If e.KeyCode = Keys.Enter Then
            tRemarqueFournisseur.Focus()
            Exit Sub
        End If
    End Sub

    Private Sub tDateInitialFournisseur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tDateInitialFournisseur.TextChanged

    End Sub

    Private Sub tRemarqueFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRemarqueFournisseur.KeyUp
        If e.KeyCode = Keys.Tab Then
            bConfirmer.Focus()
            Exit Sub
        End If
    End Sub

    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)
        If e.KeyData = Keys.F3 Then
            bConfirmer_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F5 Then
            bAjouterReglement_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F7 Then
            bSupprimerReglement_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F8 Then
            bModifierReglement_Click(o, e)
            Exit Sub
        End If

    End Sub

    Private Sub fFicheFournisseur_Activated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Activated
        If ajoutmodif = "A" Then
            tCodeFournisseur.Focus()
        Else
            tNomFournisseur.Focus()
        End If
    End Sub


    Private Sub fFicheFournisseur_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler       
    End Sub

    Private Sub gAchats_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gAchats.Click

    End Sub

 
    Private Sub rdbTous_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbTous.Click
        AfficherReglement(0)
    End Sub

    Private Sub rdbRegle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbRegle.Click
        AfficherReglement(0)
    End Sub

    Private Sub rdbNonRegle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbNonRegle.Click
        AfficherReglement(0)
    End Sub

    Private Sub gAchats_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gAchats.KeyUp

        If e.KeyCode = Keys.F1 Then
            Dim MyAchatAffiche As New fAchatJusteAffichage
            MyAchatAffiche.NumeroAchat = gAchats(gAchats.Row, "NumeroAchat")
            MyAchatAffiche.ShowDialog()
            MyAchatAffiche.Close()
            MyAchatAffiche.Dispose()

        End If
    End Sub

    Private Sub gAchat_MouseClick(sender As Object, e As System.Windows.Forms.MouseEventArgs) Handles gAchat.MouseClick
        AfficherFactureDetail()
    End Sub
End Class