﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fListeDesManquants

    Dim cmdManquant As New SqlCommand
    Dim cbManquant As New SqlCommandBuilder
    Public Shared dsManquant As New DataSet
    Dim daManquant As New SqlDataAdapter

    Public Shared Requette As String = ""
    Public Shared CondCrystal As String = ""
    Public Shared FromCommandeOrProjetCommande As Boolean = False

    Public Shared ConvertirEnCommande As Boolean = False
    Public Shared CommandeEnCours As Boolean

    Dim StrSQL As String = ""
    Dim cmdRecupereNum As New SqlCommand

    Public Sub Init()

        Dim StrSQL As String = ""
        Dim I As Integer

        Try
            dsManquant.Tables("MANQUANT_DETAILS").Clear()
        Catch ex As Exception
        End Try

        'chargement des détails des achats 
        StrSQL = Requette
        cmdManquant.Connection = ConnectionServeur
        cmdManquant.CommandText = StrSQL
        cmdManquant.CommandTimeout = 900
        daManquant = New SqlDataAdapter(cmdManquant)
        daManquant.Fill(dsManquant, "MANQUANT_DETAILS")



        'For I = 0 To dsManquant.Tables("MANQUANT_DETAILS").Rows.Count - 1

        '    If DejaSaisie(dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("CodeArticle")) = True Then
        '        dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Delete()
        '    Else
        '        dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Stock") = CalculeStock(dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("CodeArticle"))
        '        dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Qte") = dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("StockAlerte") - dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Stock") + dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("QteACommander")

        '        If CommandeEnCours = True Then
        '            dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Qte") = dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Qte") - CalculerEnCours(dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("CodeArticle"))
        '            If dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Qte") < 0 Then
        '                dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Qte") = 0
        '            End If
        '        End If

        '        If dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Qte") <= 0 Then
        '            dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Delete()
        '        End If
        '    End If
        'Next

        '---------------------------------------- élemination des lignes vide ou qte =0 
        'I = 0
        'Do While I < dsManquant.Tables("MANQUANT_DETAILS").Rows.Count
        '    If dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Qte") <= 0 Then
        '        dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Delete()
        '    End If
        '    I = 0
        'Loop
        'For I = 0 To dsManquant.Tables("MANQUANT_DETAILS").Rows.Count - 1
        '    If dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Qte") < 0 Then
        '        dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Qte") = 0

        '    End If

        'Next

        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsManquant
            Catch ex As Exception
            End Try

            .DataMember = "MANQUANT_DETAILS"
            .Rebind(False)
            .Columns("Cocher").Caption = "Choisir"
            .Columns("CodeABarre").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qte"
            .Columns("Stock").Caption = "Stock"
            .Columns("DatePeremption").Caption = "Date Péremption"
            
            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns(2).Style.HorizontalAlignment = AlignHorzEnum.Near
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next
            .Splits(0).DisplayColumns("Cocher").Width = 50
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 100
            .Splits(0).DisplayColumns("Designation").Width = 290
            .Splits(0).DisplayColumns("LibelleForme").Width = 100
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("Stock").Width = 50

            '.Splits(0).DisplayColumns("CodeForme").Width = 0
            .Splits(0).DisplayColumns("QteACommander").Width = 0
            .Splits(0).DisplayColumns("StockAlerte").Width = 0
            '.Splits(0).DisplayColumns("CodeForme").Visible = False
            '.Splits(0).DisplayColumns("QteACommander").Visible = False
            '.Splits(0).DisplayColumns("StockAlerte").Visible = False

            .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).DisplayColumns(0).Locked = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = True
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            .VisualStyle = VisualStyle.Office2007Blue
            'Style du Caractere et du grid
            ParametreGrid(gArticles)
        End With

        AfficherStatistique(gArticles(gArticles.Row, "CodeArticle"))
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        ConvertirEnCommande = False
        Me.Hide()
    End Sub

    Private Sub fListeDesManquants_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Init()
    End Sub

    Private Sub bConvertirEnCommande_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConvertirEnCommande.Click
        ConvertirEnCommande = True
        Me.Hide()
    End Sub
    Public Function CalculerEnCours(ByVal CodeArticle)

        Dim QteEnCours As Integer = 0

        StrSQL = " SELECT SUM(Qte) FROM COMMANDE_DETAILS,COMMANDE WHERE  CodeArticle ='" + _
                 CodeArticle + "' AND COMMANDE .NumeroCommande =COMMANDE_DETAILS .NumeroCommande " + _
                 "AND COMMANDE .NumeroFacture is null"

        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            QteEnCours = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return QteEnCours
    End Function

    Private Sub gArticles_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gArticles.Click

    End Sub

    Private Sub gArticles_FilterChange(sender As Object, e As System.EventArgs) Handles gArticles.FilterChange

    End Sub

    Private Sub gArticles_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyUp
        If (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down) Then
            AfficherStatistique(gArticles(gArticles.Row, "CodeArticle"))
            'Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If

    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression de la liste des manquants" Then
                num = I
            End If
        Next

        CR.FileName = Application.StartupPath + "\EtatListeDesManquants.rpt"

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent
        CR.RecordSelectionFormula = CondCrystal
        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression de la liste des manquants"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
        Me.Hide()
    End Sub

    Private Sub bAnnuler_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bAnnuler.KeyUp
        If e.KeyCode = Keys.F6 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub bImprimer_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bImprimer.KeyUp
        If e.KeyCode = Keys.F6 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub bConvertirEnCommande_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bConvertirEnCommande.KeyUp
        If e.KeyCode = Keys.F6 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub
    Public Function DejaSaisie(ByVal CodeArticle As String)
        Dim Existe As Boolean = False
        If FromCommandeOrProjetCommande = True Then
            For I = 0 To fCommande.dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1
                If fCommande.dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle") = CodeArticle Then
                    Existe = True
                    Return Existe
                    Exit Function
                End If
            Next
        Else
            For I = 0 To fProjetCommande.dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1
                If fProjetCommande.dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(I).Item("CodeArticle") = CodeArticle Then
                    Existe = True
                    Return Existe
                    Exit Function
                End If
            Next
        End If


        Return Existe
    End Function

    Private Sub InitialiserStatistique()
        lMois1.Text = "0"
        lMois2.Text = "0"
        lMois3.Text = "0"
        lMois4.Text = "0"
        lMois5.Text = "0"
        lMois6.Text = "0"
        lMois7.Text = "0"
        lMois8.Text = "0"
        lMois9.Text = "0"
        lMois10.Text = "0"
        lMois11.Text = "0"
        lMois12.Text = "0"
        LSum1.Text = "0"

        lMoisPrec1.Text = "0"
        lMoisPrec2.Text = "0"
        lMoisPrec3.Text = "0"
        lMoisPrec4.Text = "0"
        lMoisPrec5.Text = "0"
        lMoisPrec6.Text = "0"
        lMoisPrec7.Text = "0"
        lMoisPrec8.Text = "0"
        lMoisPrec9.Text = "0"
        lMoisPrec10.Text = "0"
        lMoisPrec11.Text = "0"
        lMoisPrec12.Text = "0"
        LSum2.Text = "0"
    End Sub

    Private Sub AfficherStatistique(ByVal CodeArticle As String)

        Dim i As Integer = 0
        Dim mois As String = ""

        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim CodeAbarre As String = ""

        CodeAbarre = RecupererValeurExecuteScalaire("CodeABarre", "ARTICLE", "CodeArticle", CodeArticle)

        If (dsManquant.Tables.IndexOf("STATISTIQUE_ARTICLE") > -1) Then
            dsManquant.Tables("STATISTIQUE_ARTICLE").Clear()
        End If
        If (dsManquant.Tables.IndexOf("STATISTIQUE_ARTICLE_PREC") > -1) Then
            dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Clear()
        End If

        InitialiserStatistique()

        lAnDernier.Text = System.DateTime.Now.Year
        lAnAvantDernier.Text = DateAdd(DateInterval.Year, -1, System.DateTime.Now).Year

        '********************** récupération des valeurs de vente de 12 mois pour les 2 ans 

        StrSQL = "  SELECT MONTH (date) AS MOIS,SUM(Qte) AS QTE " + _
                 "  FROM VENTE_DETAILS  " + _
                 " LEFT OUTER JOIN VENTE ON VENTE .NumeroVente =VENTE_DETAILS .NumeroVente " + _
                 " WHERE CodeArticle='" + CodeArticle + _
                 "' AND YEAR (date)=YEAR(getdate())  GROUP BY MONTH (date)"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        daManquant = New SqlDataAdapter(cmdRecupereNum)
        daManquant.Fill(dsManquant, "STATISTIQUE_ARTICLE")


        StrSQL = "  SELECT MONTH (date) AS MOIS,SUM(Qte) AS QTE " + _
                 "  FROM VENTE_DETAILS  " + _
                 " LEFT OUTER JOIN VENTE ON VENTE .NumeroVente =VENTE_DETAILS .NumeroVente " + _
                 " WHERE CodeArticle='" + CodeArticle + _
                 "' AND YEAR (date)=YEAR(getdate())-1  GROUP BY MONTH (date)"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        daManquant = New SqlDataAdapter(cmdRecupereNum)
        daManquant.Fill(dsManquant, "STATISTIQUE_ARTICLE_PREC")


        For i = 0 To dsManquant.Tables("STATISTIQUE_ARTICLE").Rows.Count - 1
            mois = dsManquant.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("Mois").ToString
            If mois <> "" Then
                If mois = "1" Then
                    lMois1.Text = dsManquant.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "2" Then
                    lMois2.Text = dsManquant.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "3" Then
                    lMois3.Text = dsManquant.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "4" Then
                    lMois4.Text = dsManquant.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "5" Then
                    lMois5.Text = dsManquant.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "6" Then
                    lMois6.Text = dsManquant.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "7" Then
                    lMois7.Text = dsManquant.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "8" Then
                    lMois8.Text = dsManquant.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "9" Then
                    lMois9.Text = dsManquant.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "10" Then
                    lMois10.Text = dsManquant.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "11" Then
                    lMois11.Text = dsManquant.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "12" Then
                    lMois12.Text = dsManquant.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If

            End If
        Next

        For i = 0 To dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Rows.Count - 1
            mois = dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("Mois").ToString
            If mois <> "" Then
                If mois = "1" Then
                    lMoisPrec1.Text = dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "2" Then
                    lMoisPrec2.Text = dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "3" Then
                    lMoisPrec3.Text = dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "4" Then
                    lMoisPrec4.Text = dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "5" Then
                    lMoisPrec5.Text = dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "6" Then
                    lMoisPrec6.Text = dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "7" Then
                    lMoisPrec7.Text = dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "8" Then
                    lMoisPrec8.Text = dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "9" Then
                    lMoisPrec9.Text = dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "10" Then
                    lMoisPrec10.Text = dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "11" Then
                    lMoisPrec11.Text = dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "12" Then
                    lMoisPrec12.Text = dsManquant.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If

            End If
        Next

        If lAnAvantDernier.Text = "2011" Then

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=1 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec1.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=2 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec2.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=3 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec3.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=4 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec4.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=5 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec5.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=6 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec6.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=7 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec7.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=8 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec8.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=9 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec9.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=10 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec10.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=11 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec11.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=12 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec12.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

        End If

        LSum1.Text = Convert.ToInt32(lMois1.Text) + lMois2.Text + lMois3.Text + lMois4.Text + lMois5.Text + lMois6.Text + lMois7.Text + lMois8.Text + lMois9.Text + lMois10.Text + lMois11.Text + lMois12.Text
        LSum2.Text = Convert.ToInt32(lMoisPrec1.Text) + lMoisPrec2.Text + lMoisPrec3.Text + lMoisPrec4.Text + lMoisPrec5.Text + lMoisPrec6.Text + lMoisPrec7.Text + lMoisPrec8.Text + lMoisPrec9.Text + lMoisPrec10.Text + lMoisPrec11.Text + lMoisPrec12.Text

    End Sub

    Private Sub etatListeDesManquants1_InitReport(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub gArticles_MouseClick(sender As Object, e As System.Windows.Forms.MouseEventArgs) Handles gArticles.MouseClick
        AfficherStatistique(gArticles(gArticles.Row, "CodeArticle"))
    End Sub

    Private Sub bCocherTous_Click(sender As System.Object, e As System.EventArgs) Handles bCocherTous.Click
        Dim I As Integer = 0
        If bCocherTous.Text = "Cocher tous" Then
            For I = 0 To dsManquant.Tables("MANQUANT_DETAILS").Rows.Count - 1
                dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Cocher") = True
            Next
            bCocherTous.Text = "Décocher tous"
        Else
            For I = 0 To dsManquant.Tables("MANQUANT_DETAILS").Rows.Count - 1
                dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Cocher") = False
            Next
            bCocherTous.Text = "Cocher tous"
        End If
    End Sub
End Class