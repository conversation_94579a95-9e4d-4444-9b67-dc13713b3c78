﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fFicheReleveeCNAM
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim Style33 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style34 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style35 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style36 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style37 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fFicheReleveeCNAM))
        Dim Style38 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style39 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style40 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style41 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style42 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style43 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style44 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style45 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style46 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style47 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style48 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Me.PAnel = New System.Windows.Forms.Panel()
        Me.bSuprimerreleve = New C1.Win.C1Input.C1Button()
        Me.bImprimer = New C1.Win.C1Input.C1Button()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.lMontantARegle = New System.Windows.Forms.Label()
        Me.lMontantDejaRegle = New System.Windows.Forms.Label()
        Me.lMontantEnCoursReglementt = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.bPriseEnCharge = New C1.Win.C1Input.C1Button()
        Me.bAppareillage = New C1.Win.C1Input.C1Button()
        Me.bAPCI_MO = New C1.Win.C1Input.C1Button()
        Me.bReglement = New C1.Win.C1Input.C1Button()
        Me.tdbdLienDeParente = New C1.Win.C1TrueDBGrid.C1TrueDBDropdown()
        Me.tdbdAPCI = New C1.Win.C1TrueDBGrid.C1TrueDBDropdown()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.lType = New System.Windows.Forms.Label()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.lReste = New System.Windows.Forms.Label()
        Me.lMontant = New System.Windows.Forms.Label()
        Me.lTotal = New System.Windows.Forms.Label()
        Me.lAu = New System.Windows.Forms.Label()
        Me.lDu = New System.Windows.Forms.Label()
        Me.lDate = New System.Windows.Forms.Label()
        Me.lNumero = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.gReleves = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bValiderReleve = New C1.Win.C1Input.C1Button()
        Me.CR = New Pharma2000Premium.EtatDeReleveCNAM()
        Me.SaveFileDialog1 = New System.Windows.Forms.SaveFileDialog()
        Me.Save = New System.Windows.Forms.FolderBrowserDialog()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.PAnel.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.tdbdLienDeParente, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tdbdAPCI, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.gReleves, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PAnel
        '
        Me.PAnel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.PAnel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.PAnel.Controls.Add(Me.Label13)
        Me.PAnel.Controls.Add(Me.bSuprimerreleve)
        Me.PAnel.Controls.Add(Me.bImprimer)
        Me.PAnel.Controls.Add(Me.Label12)
        Me.PAnel.Controls.Add(Me.bAnnuler)
        Me.PAnel.Controls.Add(Me.Label11)
        Me.PAnel.Controls.Add(Me.Label10)
        Me.PAnel.Controls.Add(Me.lMontantARegle)
        Me.PAnel.Controls.Add(Me.lMontantDejaRegle)
        Me.PAnel.Controls.Add(Me.lMontantEnCoursReglementt)
        Me.PAnel.Controls.Add(Me.Label8)
        Me.PAnel.Controls.Add(Me.GroupBox2)
        Me.PAnel.Controls.Add(Me.bReglement)
        Me.PAnel.Controls.Add(Me.tdbdLienDeParente)
        Me.PAnel.Controls.Add(Me.tdbdAPCI)
        Me.PAnel.Controls.Add(Me.GroupBox1)
        Me.PAnel.Controls.Add(Me.gReleves)
        Me.PAnel.Controls.Add(Me.bValiderReleve)
        Me.PAnel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.PAnel.Location = New System.Drawing.Point(0, 0)
        Me.PAnel.Name = "PAnel"
        Me.PAnel.Size = New System.Drawing.Size(1030, 557)
        Me.PAnel.TabIndex = 3
        '
        'bSuprimerreleve
        '
        Me.bSuprimerreleve.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bSuprimerreleve.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSuprimerreleve.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler2
        Me.bSuprimerreleve.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSuprimerreleve.Location = New System.Drawing.Point(687, 496)
        Me.bSuprimerreleve.Name = "bSuprimerreleve"
        Me.bSuprimerreleve.Size = New System.Drawing.Size(100, 45)
        Me.bSuprimerreleve.TabIndex = 51
        Me.bSuprimerreleve.Text = "Suprimer                          F7"
        Me.bSuprimerreleve.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSuprimerreleve.UseVisualStyleBackColor = True
        Me.bSuprimerreleve.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bImprimer
        '
        Me.bImprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.Imprimante
        Me.bImprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bImprimer.Location = New System.Drawing.Point(799, 6)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(109, 45)
        Me.bImprimer.TabIndex = 33
        Me.bImprimer.Text = "Imprimer                 F9"
        Me.bImprimer.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label12
        '
        Me.Label12.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.Label12.Location = New System.Drawing.Point(398, 531)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(100, 13)
        Me.Label12.TabIndex = 50
        Me.Label12.Text = "M. en cours de rég"
        Me.Label12.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(916, 6)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(100, 45)
        Me.bAnnuler.TabIndex = 2
        Me.bAnnuler.Text = "Fermer                     F12"
        Me.bAnnuler.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label11
        '
        Me.Label11.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.Label11.Location = New System.Drawing.Point(406, 509)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(93, 13)
        Me.Label11.TabIndex = 49
        Me.Label11.Text = "M. Déja réglé"
        Me.Label11.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label10
        '
        Me.Label10.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.Label10.Location = New System.Drawing.Point(405, 488)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(93, 13)
        Me.Label10.TabIndex = 41
        Me.Label10.Text = "Montant à régler "
        Me.Label10.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lMontantARegle
        '
        Me.lMontantARegle.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.lMontantARegle.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lMontantARegle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lMontantARegle.ForeColor = System.Drawing.Color.Green
        Me.lMontantARegle.Location = New System.Drawing.Point(505, 486)
        Me.lMontantARegle.Name = "lMontantARegle"
        Me.lMontantARegle.Size = New System.Drawing.Size(83, 18)
        Me.lMontantARegle.TabIndex = 48
        Me.lMontantARegle.Text = "0"
        Me.lMontantARegle.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lMontantDejaRegle
        '
        Me.lMontantDejaRegle.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.lMontantDejaRegle.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lMontantDejaRegle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lMontantDejaRegle.ForeColor = System.Drawing.Color.Green
        Me.lMontantDejaRegle.Location = New System.Drawing.Point(505, 508)
        Me.lMontantDejaRegle.Name = "lMontantDejaRegle"
        Me.lMontantDejaRegle.Size = New System.Drawing.Size(83, 18)
        Me.lMontantDejaRegle.TabIndex = 47
        Me.lMontantDejaRegle.Text = "0"
        Me.lMontantDejaRegle.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lMontantEnCoursReglementt
        '
        Me.lMontantEnCoursReglementt.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.lMontantEnCoursReglementt.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lMontantEnCoursReglementt.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lMontantEnCoursReglementt.ForeColor = System.Drawing.Color.Green
        Me.lMontantEnCoursReglementt.Location = New System.Drawing.Point(505, 529)
        Me.lMontantEnCoursReglementt.Name = "lMontantEnCoursReglementt"
        Me.lMontantEnCoursReglementt.Size = New System.Drawing.Size(83, 18)
        Me.lMontantEnCoursReglementt.TabIndex = 41
        Me.lMontantEnCoursReglementt.Text = "0"
        Me.lMontantEnCoursReglementt.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label8
        '
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label8.Location = New System.Drawing.Point(12, 3)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(766, 42)
        Me.Label8.TabIndex = 46
        Me.Label8.Text = "RELEVE CNAM"
        Me.Label8.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'GroupBox2
        '
        Me.GroupBox2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.GroupBox2.Controls.Add(Me.bPriseEnCharge)
        Me.GroupBox2.Controls.Add(Me.bAppareillage)
        Me.GroupBox2.Controls.Add(Me.bAPCI_MO)
        Me.GroupBox2.Location = New System.Drawing.Point(12, 477)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(346, 73)
        Me.GroupBox2.TabIndex = 32
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "Générer fichiers CNAM"
        '
        'bPriseEnCharge
        '
        Me.bPriseEnCharge.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bPriseEnCharge.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bPriseEnCharge.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bPriseEnCharge.Location = New System.Drawing.Point(231, 19)
        Me.bPriseEnCharge.Name = "bPriseEnCharge"
        Me.bPriseEnCharge.Size = New System.Drawing.Size(110, 46)
        Me.bPriseEnCharge.TabIndex = 23
        Me.bPriseEnCharge.Text = "Prise En Charge"
        Me.bPriseEnCharge.UseVisualStyleBackColor = True
        Me.bPriseEnCharge.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAppareillage
        '
        Me.bAppareillage.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAppareillage.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAppareillage.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAppareillage.Location = New System.Drawing.Point(4, 19)
        Me.bAppareillage.Name = "bAppareillage"
        Me.bAppareillage.Size = New System.Drawing.Size(110, 46)
        Me.bAppareillage.TabIndex = 21
        Me.bAppareillage.Text = "APPAREILLAGE"
        Me.bAppareillage.UseVisualStyleBackColor = True
        Me.bAppareillage.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAPCI_MO
        '
        Me.bAPCI_MO.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAPCI_MO.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAPCI_MO.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAPCI_MO.Location = New System.Drawing.Point(118, 19)
        Me.bAPCI_MO.Name = "bAPCI_MO"
        Me.bAPCI_MO.Size = New System.Drawing.Size(110, 46)
        Me.bAPCI_MO.TabIndex = 22
        Me.bAPCI_MO.Text = " APCI+MO"
        Me.bAPCI_MO.UseVisualStyleBackColor = True
        Me.bAPCI_MO.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bReglement
        '
        Me.bReglement.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bReglement.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bReglement.Image = Global.Pharma2000Premium.My.Resources.Resources.saisie
        Me.bReglement.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bReglement.Location = New System.Drawing.Point(793, 497)
        Me.bReglement.Name = "bReglement"
        Me.bReglement.Size = New System.Drawing.Size(119, 45)
        Me.bReglement.TabIndex = 20
        Me.bReglement.Text = "Règlement                 F8"
        Me.bReglement.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bReglement.UseVisualStyleBackColor = True
        Me.bReglement.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tdbdLienDeParente
        '
        Me.tdbdLienDeParente.AllowColMove = True
        Me.tdbdLienDeParente.AllowColSelect = True
        Me.tdbdLienDeParente.AllowRowSizing = C1.Win.C1TrueDBGrid.RowSizingEnum.AllRows
        Me.tdbdLienDeParente.AlternatingRows = False
        Me.tdbdLienDeParente.CaptionStyle = Style33
        Me.tdbdLienDeParente.ColumnCaptionHeight = 17
        Me.tdbdLienDeParente.ColumnFooterHeight = 17
        Me.tdbdLienDeParente.EvenRowStyle = Style34
        Me.tdbdLienDeParente.FetchRowStyles = False
        Me.tdbdLienDeParente.FooterStyle = Style35
        Me.tdbdLienDeParente.HeadingStyle = Style36
        Me.tdbdLienDeParente.HighLightRowStyle = Style37
        Me.tdbdLienDeParente.Images.Add(CType(resources.GetObject("tdbdLienDeParente.Images"), System.Drawing.Image))
        Me.tdbdLienDeParente.Location = New System.Drawing.Point(531, 189)
        Me.tdbdLienDeParente.Name = "tdbdLienDeParente"
        Me.tdbdLienDeParente.OddRowStyle = Style38
        Me.tdbdLienDeParente.RecordSelectorStyle = Style39
        Me.tdbdLienDeParente.RowDivider.Color = System.Drawing.Color.DarkGray
        Me.tdbdLienDeParente.RowDivider.Style = C1.Win.C1TrueDBGrid.LineStyleEnum.[Single]
        Me.tdbdLienDeParente.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.tdbdLienDeParente.ScrollTips = False
        Me.tdbdLienDeParente.Size = New System.Drawing.Size(100, 150)
        Me.tdbdLienDeParente.Style = Style40
        Me.tdbdLienDeParente.TabIndex = 19
        Me.tdbdLienDeParente.Text = "C1TrueDBDropdown1"
        Me.tdbdLienDeParente.Visible = False
        Me.tdbdLienDeParente.PropBag = resources.GetString("tdbdLienDeParente.PropBag")
        '
        'tdbdAPCI
        '
        Me.tdbdAPCI.AllowColMove = True
        Me.tdbdAPCI.AllowColSelect = True
        Me.tdbdAPCI.AllowRowSizing = C1.Win.C1TrueDBGrid.RowSizingEnum.AllRows
        Me.tdbdAPCI.AlternatingRows = False
        Me.tdbdAPCI.CaptionStyle = Style41
        Me.tdbdAPCI.ColumnCaptionHeight = 17
        Me.tdbdAPCI.ColumnFooterHeight = 17
        Me.tdbdAPCI.EvenRowStyle = Style42
        Me.tdbdAPCI.FetchRowStyles = False
        Me.tdbdAPCI.FooterStyle = Style43
        Me.tdbdAPCI.HeadingStyle = Style44
        Me.tdbdAPCI.HighLightRowStyle = Style45
        Me.tdbdAPCI.Images.Add(CType(resources.GetObject("tdbdAPCI.Images"), System.Drawing.Image))
        Me.tdbdAPCI.Location = New System.Drawing.Point(653, 189)
        Me.tdbdAPCI.Name = "tdbdAPCI"
        Me.tdbdAPCI.OddRowStyle = Style46
        Me.tdbdAPCI.RecordSelectorStyle = Style47
        Me.tdbdAPCI.RowDivider.Color = System.Drawing.Color.DarkGray
        Me.tdbdAPCI.RowDivider.Style = C1.Win.C1TrueDBGrid.LineStyleEnum.[Single]
        Me.tdbdAPCI.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.tdbdAPCI.ScrollTips = False
        Me.tdbdAPCI.Size = New System.Drawing.Size(100, 150)
        Me.tdbdAPCI.Style = Style48
        Me.tdbdAPCI.TabIndex = 18
        Me.tdbdAPCI.Text = "C1TrueDBDropdown1"
        Me.tdbdAPCI.Visible = False
        Me.tdbdAPCI.PropBag = resources.GetString("tdbdAPCI.PropBag")
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.Controls.Add(Me.lType)
        Me.GroupBox1.Controls.Add(Me.Label9)
        Me.GroupBox1.Controls.Add(Me.lReste)
        Me.GroupBox1.Controls.Add(Me.lMontant)
        Me.GroupBox1.Controls.Add(Me.lTotal)
        Me.GroupBox1.Controls.Add(Me.lAu)
        Me.GroupBox1.Controls.Add(Me.lDu)
        Me.GroupBox1.Controls.Add(Me.lDate)
        Me.GroupBox1.Controls.Add(Me.lNumero)
        Me.GroupBox1.Controls.Add(Me.Label7)
        Me.GroupBox1.Controls.Add(Me.Label6)
        Me.GroupBox1.Controls.Add(Me.Label5)
        Me.GroupBox1.Controls.Add(Me.Label4)
        Me.GroupBox1.Controls.Add(Me.Label3)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Location = New System.Drawing.Point(12, 51)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(1007, 82)
        Me.GroupBox1.TabIndex = 16
        Me.GroupBox1.TabStop = False
        '
        'lType
        '
        Me.lType.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lType.Location = New System.Drawing.Point(230, 17)
        Me.lType.Name = "lType"
        Me.lType.Size = New System.Drawing.Size(389, 18)
        Me.lType.TabIndex = 40
        Me.lType.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Location = New System.Drawing.Point(193, 19)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(31, 13)
        Me.Label9.TabIndex = 39
        Me.Label9.Text = "Type"
        '
        'lReste
        '
        Me.lReste.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lReste.Location = New System.Drawing.Point(883, 48)
        Me.lReste.Name = "lReste"
        Me.lReste.Size = New System.Drawing.Size(100, 18)
        Me.lReste.TabIndex = 38
        Me.lReste.Text = "Numéro"
        Me.lReste.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lMontant
        '
        Me.lMontant.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lMontant.Location = New System.Drawing.Point(715, 50)
        Me.lMontant.Name = "lMontant"
        Me.lMontant.Size = New System.Drawing.Size(100, 18)
        Me.lMontant.TabIndex = 37
        Me.lMontant.Text = "Numéro"
        Me.lMontant.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lTotal
        '
        Me.lTotal.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lTotal.Location = New System.Drawing.Point(715, 16)
        Me.lTotal.Name = "lTotal"
        Me.lTotal.Size = New System.Drawing.Size(100, 18)
        Me.lTotal.TabIndex = 36
        Me.lTotal.Text = "Numéro"
        Me.lTotal.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lAu
        '
        Me.lAu.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lAu.Location = New System.Drawing.Point(435, 54)
        Me.lAu.Name = "lAu"
        Me.lAu.Size = New System.Drawing.Size(100, 18)
        Me.lAu.TabIndex = 35
        Me.lAu.Text = "lAu"
        '
        'lDu
        '
        Me.lDu.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lDu.Location = New System.Drawing.Point(313, 54)
        Me.lDu.Name = "lDu"
        Me.lDu.Size = New System.Drawing.Size(100, 18)
        Me.lDu.TabIndex = 34
        Me.lDu.Text = "lDu"
        '
        'lDate
        '
        Me.lDate.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lDate.Location = New System.Drawing.Point(54, 48)
        Me.lDate.Name = "lDate"
        Me.lDate.Size = New System.Drawing.Size(100, 18)
        Me.lDate.TabIndex = 33
        Me.lDate.Text = "lDate"
        '
        'lNumero
        '
        Me.lNumero.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lNumero.Location = New System.Drawing.Point(54, 17)
        Me.lNumero.Name = "lNumero"
        Me.lNumero.Size = New System.Drawing.Size(100, 18)
        Me.lNumero.TabIndex = 32
        Me.lNumero.Text = "Numéro"
        '
        'Label7
        '
        Me.Label7.Location = New System.Drawing.Point(625, 20)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(84, 13)
        Me.Label7.TabIndex = 31
        Me.Label7.Text = "Montant Total"
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label6
        '
        Me.Label6.Location = New System.Drawing.Point(634, 52)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(75, 13)
        Me.Label6.TabIndex = 29
        Me.Label6.Text = "M.A Rem"
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label5
        '
        Me.Label5.Location = New System.Drawing.Point(842, 50)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(35, 13)
        Me.Label5.TabIndex = 27
        Me.Label5.Text = "Reste"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Location = New System.Drawing.Point(466, 37)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(20, 13)
        Me.Label4.TabIndex = 26
        Me.Label4.Text = "Au"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Location = New System.Drawing.Point(342, 37)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(21, 13)
        Me.Label3.TabIndex = 25
        Me.Label3.Text = "Du"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(9, 51)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(30, 13)
        Me.Label2.TabIndex = 24
        Me.Label2.Text = "Date"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(9, 19)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(44, 13)
        Me.Label1.TabIndex = 23
        Me.Label1.Text = "Numéro"
        '
        'gReleves
        '
        Me.gReleves.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gReleves.GroupByCaption = "Drag a column header here to group by that column"
        Me.gReleves.Images.Add(CType(resources.GetObject("gReleves.Images"), System.Drawing.Image))
        Me.gReleves.Location = New System.Drawing.Point(12, 138)
        Me.gReleves.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gReleves.Name = "gReleves"
        Me.gReleves.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gReleves.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gReleves.PreviewInfo.ZoomFactor = 75.0R
        Me.gReleves.PrintInfo.PageSettings = CType(resources.GetObject("gReleves.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gReleves.Size = New System.Drawing.Size(1004, 336)
        Me.gReleves.TabIndex = 12
        Me.gReleves.Text = "C1TrueDBGrid1"
        Me.gReleves.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gReleves.PropBag = resources.GetString("gReleves.PropBag")
        '
        'bValiderReleve
        '
        Me.bValiderReleve.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bValiderReleve.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bValiderReleve.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bValiderReleve.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bValiderReleve.Location = New System.Drawing.Point(916, 497)
        Me.bValiderReleve.Name = "bValiderReleve"
        Me.bValiderReleve.Size = New System.Drawing.Size(100, 45)
        Me.bValiderReleve.TabIndex = 1
        Me.bValiderReleve.Text = "Valider                  F3"
        Me.bValiderReleve.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bValiderReleve.UseVisualStyleBackColor = True
        Me.bValiderReleve.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'CR
        '
        '
        'Label13
        '
        Me.Label13.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label13.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.Label13.Location = New System.Drawing.Point(923, 479)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(93, 13)
        Me.Label13.TabIndex = 52
        Me.Label13.Text = "Vente OMF"
        Me.Label13.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'fFicheReleveeCNAM
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1030, 557)
        Me.Controls.Add(Me.PAnel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.SizableToolWindow
        Me.Name = "fFicheReleveeCNAM"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "fFicheReleveeCNAM"
        Me.WindowState = System.Windows.Forms.FormWindowState.Maximized
        Me.PAnel.ResumeLayout(False)
        Me.GroupBox2.ResumeLayout(False)
        CType(Me.tdbdLienDeParente, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tdbdAPCI, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.gReleves, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents PAnel As System.Windows.Forms.Panel
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents gReleves As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bValiderReleve As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents tdbdAPCI As C1.Win.C1TrueDBGrid.C1TrueDBDropdown
    Friend WithEvents tdbdLienDeParente As C1.Win.C1TrueDBGrid.C1TrueDBDropdown
    Friend WithEvents bReglement As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents bPriseEnCharge As C1.Win.C1Input.C1Button
    Friend WithEvents bAppareillage As C1.Win.C1Input.C1Button
    Friend WithEvents bAPCI_MO As C1.Win.C1Input.C1Button
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
    Friend WithEvents lReste As System.Windows.Forms.Label
    Friend WithEvents lMontant As System.Windows.Forms.Label
    Friend WithEvents lTotal As System.Windows.Forms.Label
    Friend WithEvents lAu As System.Windows.Forms.Label
    Friend WithEvents lDu As System.Windows.Forms.Label
    Friend WithEvents lDate As System.Windows.Forms.Label
    Friend WithEvents lNumero As System.Windows.Forms.Label
    Friend WithEvents CR As Pharma2000Premium.EtatDeReleveCNAM
    Friend WithEvents lType As System.Windows.Forms.Label
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents lMontantARegle As System.Windows.Forms.Label
    Friend WithEvents lMontantDejaRegle As System.Windows.Forms.Label
    Friend WithEvents lMontantEnCoursReglementt As System.Windows.Forms.Label
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents SaveFileDialog1 As System.Windows.Forms.SaveFileDialog
    Friend WithEvents Save As System.Windows.Forms.FolderBrowserDialog
    Friend WithEvents bSuprimerreleve As C1.Win.C1Input.C1Button
    Friend WithEvents Label13 As System.Windows.Forms.Label
End Class
