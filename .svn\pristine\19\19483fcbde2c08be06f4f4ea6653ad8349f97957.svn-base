﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fQuantiteADelivrer
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fQuantiteADelivrer))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.lQuantite = New System.Windows.Forms.Label()
        Me.ltitre = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.cmbForme = New C1.Win.C1List.C1Combo()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.rdbJour = New System.Windows.Forms.RadioButton()
        Me.rdb5jourSemaine = New System.Windows.Forms.RadioButton()
        Me.rdbSemaine = New System.Windows.Forms.RadioButton()
        Me.rdb1JourSur2 = New System.Windows.Forms.RadioButton()
        Me.rdbMois = New System.Windows.Forms.RadioButton()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.rdbGoutte = New System.Windows.Forms.RadioButton()
        Me.rdbCpInj = New System.Windows.Forms.RadioButton()
        Me.rdbCCafe = New System.Windows.Forms.RadioButton()
        Me.rdbCSouppe = New System.Windows.Forms.RadioButton()
        Me.rdbCDessert = New System.Windows.Forms.RadioButton()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.tContenanceDuProduit = New C1.Win.C1Input.C1TextBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.tNombreDePrise = New C1.Win.C1Input.C1TextBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.tQuantitePrescrire = New C1.Win.C1Input.C1TextBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.tDureDeTraitement = New C1.Win.C1Input.C1TextBox()
        Me.bCalculer = New C1.Win.C1Input.C1Button()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.Panel.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.cmbForme, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox4.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        CType(Me.tContenanceDuProduit, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNombreDePrise, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tQuantitePrescrire, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDureDeTraitement, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.Label7)
        Me.Panel.Controls.Add(Me.Label8)
        Me.Panel.Controls.Add(Me.lQuantite)
        Me.Panel.Controls.Add(Me.ltitre)
        Me.Panel.Controls.Add(Me.Label6)
        Me.Panel.Controls.Add(Me.Label5)
        Me.Panel.Controls.Add(Me.GroupBox2)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Controls.Add(Me.bCalculer)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(757, 377)
        Me.Panel.TabIndex = 1
        '
        'Label7
        '
        Me.Label7.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.Location = New System.Drawing.Point(253, 144)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(30, 14)
        Me.Label7.TabIndex = 21
        Me.Label7.Text = "Par :"
        '
        'Label8
        '
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.Location = New System.Drawing.Point(140, 223)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(263, 40)
        Me.Label8.TabIndex = 26
        Me.Label8.Text = ">> Quantité à délivrer    :"
        Me.Label8.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lQuantite
        '
        Me.lQuantite.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lQuantite.Location = New System.Drawing.Point(409, 223)
        Me.lQuantite.Name = "lQuantite"
        Me.lQuantite.Size = New System.Drawing.Size(190, 40)
        Me.lQuantite.TabIndex = 25
        Me.lQuantite.Text = "-"
        Me.lQuantite.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'ltitre
        '
        Me.ltitre.Font = New System.Drawing.Font("HandelGotDLig", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ltitre.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.ltitre.Location = New System.Drawing.Point(10, 2)
        Me.ltitre.Name = "ltitre"
        Me.ltitre.Size = New System.Drawing.Size(733, 40)
        Me.ltitre.TabIndex = 1
        Me.ltitre.Text = "CALCUL DE LA QUANTITE A DELIVRER "
        Me.ltitre.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label6
        '
        Me.Label6.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.Location = New System.Drawing.Point(10, 320)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(733, 18)
        Me.Label6.TabIndex = 23
        Me.Label6.Text = "_________________________________________________________________________________" & _
    "__________________________________"
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.TopCenter
        '
        'Label5
        '
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.Location = New System.Drawing.Point(10, 346)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(733, 23)
        Me.Label5.TabIndex = 22
        Me.Label5.Text = "(1 Goutte=0.05 ml, 1 c.à café=5ml, 1c. à dessert=10ml, 1c. à soupe=15ml)"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.cmbForme)
        Me.GroupBox2.Controls.Add(Me.GroupBox4)
        Me.GroupBox2.Controls.Add(Me.GroupBox3)
        Me.GroupBox2.Controls.Add(Me.Label9)
        Me.GroupBox2.Location = New System.Drawing.Point(287, 42)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(456, 134)
        Me.GroupBox2.TabIndex = 1
        Me.GroupBox2.TabStop = False
        '
        'cmbForme
        '
        Me.cmbForme.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbForme.Caption = ""
        Me.cmbForme.CaptionHeight = 17
        Me.cmbForme.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbForme.ColumnCaptionHeight = 17
        Me.cmbForme.ColumnFooterHeight = 17
        Me.cmbForme.ColumnWidth = 120
        Me.cmbForme.ContentHeight = 16
        Me.cmbForme.DataMode = C1.Win.C1List.DataModeEnum.AddItem
        Me.cmbForme.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbForme.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbForme.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbForme.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbForme.EditorHeight = 16
        Me.cmbForme.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbForme.Images.Add(CType(resources.GetObject("cmbForme.Images"), System.Drawing.Image))
        Me.cmbForme.ItemHeight = 15
        Me.cmbForme.Location = New System.Drawing.Point(140, 19)
        Me.cmbForme.MatchEntryTimeout = CType(2000, Long)
        Me.cmbForme.MaxDropDownItems = CType(5, Short)
        Me.cmbForme.MaxLength = 32767
        Me.cmbForme.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbForme.Name = "cmbForme"
        Me.cmbForme.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbForme.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbForme.Size = New System.Drawing.Size(126, 22)
        Me.cmbForme.TabIndex = 0
        Me.cmbForme.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbForme.PropBag = resources.GetString("cmbForme.PropBag")
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.rdbJour)
        Me.GroupBox4.Controls.Add(Me.rdb5jourSemaine)
        Me.GroupBox4.Controls.Add(Me.rdbSemaine)
        Me.GroupBox4.Controls.Add(Me.rdb1JourSur2)
        Me.GroupBox4.Controls.Add(Me.rdbMois)
        Me.GroupBox4.Location = New System.Drawing.Point(9, 88)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(437, 40)
        Me.GroupBox4.TabIndex = 57
        Me.GroupBox4.TabStop = False
        '
        'rdbJour
        '
        Me.rdbJour.AutoSize = True
        Me.rdbJour.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbJour.Location = New System.Drawing.Point(6, 14)
        Me.rdbJour.Name = "rdbJour"
        Me.rdbJour.Size = New System.Drawing.Size(45, 17)
        Me.rdbJour.TabIndex = 0
        Me.rdbJour.TabStop = True
        Me.rdbJour.Text = "Jour"
        Me.rdbJour.UseVisualStyleBackColor = True
        '
        'rdb5jourSemaine
        '
        Me.rdb5jourSemaine.AutoSize = True
        Me.rdb5jourSemaine.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdb5jourSemaine.Location = New System.Drawing.Point(312, 15)
        Me.rdb5jourSemaine.Name = "rdb5jourSemaine"
        Me.rdb5jourSemaine.Size = New System.Drawing.Size(106, 17)
        Me.rdb5jourSemaine.TabIndex = 4
        Me.rdb5jourSemaine.TabStop = True
        Me.rdb5jourSemaine.Text = "5 jours / semaine"
        Me.rdb5jourSemaine.UseVisualStyleBackColor = True
        '
        'rdbSemaine
        '
        Me.rdbSemaine.AutoSize = True
        Me.rdbSemaine.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbSemaine.Location = New System.Drawing.Point(69, 15)
        Me.rdbSemaine.Name = "rdbSemaine"
        Me.rdbSemaine.Size = New System.Drawing.Size(66, 17)
        Me.rdbSemaine.TabIndex = 1
        Me.rdbSemaine.TabStop = True
        Me.rdbSemaine.Text = "Semaine"
        Me.rdbSemaine.UseVisualStyleBackColor = True
        '
        'rdb1JourSur2
        '
        Me.rdb1JourSur2.AutoSize = True
        Me.rdb1JourSur2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdb1JourSur2.Location = New System.Drawing.Point(229, 15)
        Me.rdb1JourSur2.Name = "rdb1JourSur2"
        Me.rdb1JourSur2.Size = New System.Drawing.Size(77, 17)
        Me.rdb1JourSur2.TabIndex = 3
        Me.rdb1JourSur2.TabStop = True
        Me.rdb1JourSur2.Text = "1 jour sur 2"
        Me.rdb1JourSur2.UseVisualStyleBackColor = True
        '
        'rdbMois
        '
        Me.rdbMois.AutoSize = True
        Me.rdbMois.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbMois.Location = New System.Drawing.Point(141, 15)
        Me.rdbMois.Name = "rdbMois"
        Me.rdbMois.Size = New System.Drawing.Size(47, 17)
        Me.rdbMois.TabIndex = 2
        Me.rdbMois.TabStop = True
        Me.rdbMois.Text = "Mois"
        Me.rdbMois.UseVisualStyleBackColor = True
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.rdbGoutte)
        Me.GroupBox3.Controls.Add(Me.rdbCpInj)
        Me.GroupBox3.Controls.Add(Me.rdbCCafe)
        Me.GroupBox3.Controls.Add(Me.rdbCSouppe)
        Me.GroupBox3.Controls.Add(Me.rdbCDessert)
        Me.GroupBox3.Location = New System.Drawing.Point(9, 48)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(437, 40)
        Me.GroupBox3.TabIndex = 56
        Me.GroupBox3.TabStop = False
        '
        'rdbGoutte
        '
        Me.rdbGoutte.AutoSize = True
        Me.rdbGoutte.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbGoutte.Location = New System.Drawing.Point(6, 14)
        Me.rdbGoutte.Name = "rdbGoutte"
        Me.rdbGoutte.Size = New System.Drawing.Size(57, 17)
        Me.rdbGoutte.TabIndex = 0
        Me.rdbGoutte.TabStop = True
        Me.rdbGoutte.Text = "Goutte"
        Me.rdbGoutte.UseVisualStyleBackColor = True
        '
        'rdbCpInj
        '
        Me.rdbCpInj.AutoSize = True
        Me.rdbCpInj.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbCpInj.Location = New System.Drawing.Point(312, 15)
        Me.rdbCpInj.Name = "rdbCpInj"
        Me.rdbCpInj.Size = New System.Drawing.Size(66, 17)
        Me.rdbCpInj.TabIndex = 4
        Me.rdbCpInj.TabStop = True
        Me.rdbCpInj.Text = "CP / INJ"
        Me.rdbCpInj.UseVisualStyleBackColor = True
        '
        'rdbCCafe
        '
        Me.rdbCCafe.AutoSize = True
        Me.rdbCCafe.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbCCafe.Location = New System.Drawing.Point(69, 15)
        Me.rdbCCafe.Name = "rdbCCafe"
        Me.rdbCCafe.Size = New System.Drawing.Size(68, 17)
        Me.rdbCCafe.TabIndex = 1
        Me.rdbCCafe.TabStop = True
        Me.rdbCCafe.Text = "C. à café"
        Me.rdbCCafe.UseVisualStyleBackColor = True
        '
        'rdbCSouppe
        '
        Me.rdbCSouppe.AutoSize = True
        Me.rdbCSouppe.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbCSouppe.Location = New System.Drawing.Point(229, 15)
        Me.rdbCSouppe.Name = "rdbCSouppe"
        Me.rdbCSouppe.Size = New System.Drawing.Size(76, 17)
        Me.rdbCSouppe.TabIndex = 3
        Me.rdbCSouppe.TabStop = True
        Me.rdbCSouppe.Text = "C. à soupe"
        Me.rdbCSouppe.UseVisualStyleBackColor = True
        '
        'rdbCDessert
        '
        Me.rdbCDessert.AutoSize = True
        Me.rdbCDessert.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbCDessert.Location = New System.Drawing.Point(141, 15)
        Me.rdbCDessert.Name = "rdbCDessert"
        Me.rdbCDessert.Size = New System.Drawing.Size(81, 17)
        Me.rdbCDessert.TabIndex = 2
        Me.rdbCDessert.TabStop = True
        Me.rdbCDessert.Text = "C. à dessert"
        Me.rdbCDessert.UseVisualStyleBackColor = True
        '
        'Label9
        '
        Me.Label9.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.Location = New System.Drawing.Point(8, 22)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(126, 18)
        Me.Label9.TabIndex = 14
        Me.Label9.Text = "Présentation du produit:"
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.Label3)
        Me.GroupBox1.Controls.Add(Me.tContenanceDuProduit)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Controls.Add(Me.tNombreDePrise)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Controls.Add(Me.tQuantitePrescrire)
        Me.GroupBox1.Controls.Add(Me.Label4)
        Me.GroupBox1.Controls.Add(Me.tDureDeTraitement)
        Me.GroupBox1.Location = New System.Drawing.Point(6, 42)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(245, 169)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        '
        'Label3
        '
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.Location = New System.Drawing.Point(6, 134)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(153, 14)
        Me.Label3.TabIndex = 20
        Me.Label3.Text = "Contenance du produit:"
        '
        'tContenanceDuProduit
        '
        Me.tContenanceDuProduit.AutoSize = False
        Me.tContenanceDuProduit.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tContenanceDuProduit.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tContenanceDuProduit.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tContenanceDuProduit.Location = New System.Drawing.Point(165, 133)
        Me.tContenanceDuProduit.Name = "tContenanceDuProduit"
        Me.tContenanceDuProduit.Size = New System.Drawing.Size(69, 20)
        Me.tContenanceDuProduit.TabIndex = 3
        Me.tContenanceDuProduit.Tag = Nothing
        Me.tContenanceDuProduit.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tContenanceDuProduit.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label2
        '
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(6, 101)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(153, 14)
        Me.Label2.TabIndex = 18
        Me.Label2.Text = "Nombre de prise:"
        '
        'tNombreDePrise
        '
        Me.tNombreDePrise.AutoSize = False
        Me.tNombreDePrise.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNombreDePrise.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNombreDePrise.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNombreDePrise.Location = New System.Drawing.Point(165, 100)
        Me.tNombreDePrise.Name = "tNombreDePrise"
        Me.tNombreDePrise.Size = New System.Drawing.Size(69, 20)
        Me.tNombreDePrise.TabIndex = 2
        Me.tNombreDePrise.Tag = Nothing
        Me.tNombreDePrise.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNombreDePrise.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(6, 63)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(153, 14)
        Me.Label1.TabIndex = 16
        Me.Label1.Text = "Quantité prescrite par prise:"
        '
        'tQuantitePrescrire
        '
        Me.tQuantitePrescrire.AutoSize = False
        Me.tQuantitePrescrire.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tQuantitePrescrire.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tQuantitePrescrire.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tQuantitePrescrire.Location = New System.Drawing.Point(165, 62)
        Me.tQuantitePrescrire.Name = "tQuantitePrescrire"
        Me.tQuantitePrescrire.Size = New System.Drawing.Size(69, 20)
        Me.tQuantitePrescrire.TabIndex = 1
        Me.tQuantitePrescrire.Tag = Nothing
        Me.tQuantitePrescrire.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tQuantitePrescrire.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label4
        '
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.Location = New System.Drawing.Point(6, 21)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(153, 14)
        Me.Label4.TabIndex = 9
        Me.Label4.Text = "Durée de traitement en jours:"
        '
        'tDureDeTraitement
        '
        Me.tDureDeTraitement.AutoSize = False
        Me.tDureDeTraitement.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tDureDeTraitement.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDureDeTraitement.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDureDeTraitement.Location = New System.Drawing.Point(165, 20)
        Me.tDureDeTraitement.Name = "tDureDeTraitement"
        Me.tDureDeTraitement.Size = New System.Drawing.Size(69, 20)
        Me.tDureDeTraitement.TabIndex = 0
        Me.tDureDeTraitement.Tag = Nothing
        Me.tDureDeTraitement.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDureDeTraitement.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bCalculer
        '
        Me.bCalculer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bCalculer.Location = New System.Drawing.Point(264, 266)
        Me.bCalculer.Name = "bCalculer"
        Me.bCalculer.Size = New System.Drawing.Size(100, 45)
        Me.bCalculer.TabIndex = 2
        Me.bCalculer.Text = "Calculer"
        Me.bCalculer.UseVisualStyleBackColor = True
        Me.bCalculer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAnnuler
        '
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Location = New System.Drawing.Point(372, 266)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(100, 45)
        Me.bAnnuler.TabIndex = 3
        Me.bAnnuler.Text = "Fermer"
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fQuantiteADelivrer
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(757, 377)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fQuantiteADelivrer"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.cmbForme, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        Me.GroupBox1.ResumeLayout(False)
        CType(Me.tContenanceDuProduit, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNombreDePrise, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tQuantitePrescrire, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDureDeTraitement, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bCalculer As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents tDureDeTraitement As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents tContenanceDuProduit As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents tNombreDePrise As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents tQuantitePrescrire As C1.Win.C1Input.C1TextBox
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents rdbCpInj As System.Windows.Forms.RadioButton
    Friend WithEvents rdbCSouppe As System.Windows.Forms.RadioButton
    Friend WithEvents rdbCDessert As System.Windows.Forms.RadioButton
    Friend WithEvents rdbCCafe As System.Windows.Forms.RadioButton
    Friend WithEvents rdbGoutte As System.Windows.Forms.RadioButton
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents rdbJour As System.Windows.Forms.RadioButton
    Friend WithEvents rdb5jourSemaine As System.Windows.Forms.RadioButton
    Friend WithEvents rdbSemaine As System.Windows.Forms.RadioButton
    Friend WithEvents rdb1JourSur2 As System.Windows.Forms.RadioButton
    Friend WithEvents rdbMois As System.Windows.Forms.RadioButton
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents lQuantite As System.Windows.Forms.Label
    Friend WithEvents ltitre As System.Windows.Forms.Label
    Friend WithEvents cmbForme As C1.Win.C1List.C1Combo
    Friend WithEvents Label7 As System.Windows.Forms.Label
End Class
