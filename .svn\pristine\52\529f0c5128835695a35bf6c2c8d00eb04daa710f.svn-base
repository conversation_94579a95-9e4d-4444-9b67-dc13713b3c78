<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="gListe.Images" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAkAAAAJCAYAAADgkQYQAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAA3SURBVChTY2DABP+xiIGFkCVwsVEUwhThNREkiaEAJoiP
        RnEmskKs7kd3C1YrYTrx+g6bIrAYAKCqHOQvFu6BAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="gListe.PrintInfo.PageSettings" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAACRTeXN0ZW0uRHJh
        d2luZy5QcmludGluZy5QYWdlU2V0dGluZ3MHAAAAD3ByaW50ZXJTZXR0aW5ncwVjb2xvcglwYXBlclNp
        emULcGFwZXJTb3VyY2URcHJpbnRlclJlc29sdXRpb24JbGFuZHNjYXBlB21hcmdpbnMEBAQEBAQEJ1N5
        c3RlbS5EcmF3aW5nLlByaW50aW5nLlByaW50ZXJTZXR0aW5ncwIAAAAgU3lzdGVtLkRyYXdpbmcuUHJp
        bnRpbmcuVHJpU3RhdGUCAAAAIVN5c3RlbS5EcmF3aW5nLlByaW50aW5nLlBhcGVyU2l6ZQIAAAAjU3lz
        dGVtLkRyYXdpbmcuUHJpbnRpbmcuUGFwZXJTb3VyY2UCAAAAKVN5c3RlbS5EcmF3aW5nLlByaW50aW5n
        LlByaW50ZXJSZXNvbHV0aW9uAgAAACBTeXN0ZW0uRHJhd2luZy5QcmludGluZy5UcmlTdGF0ZQIAAAAf
        U3lzdGVtLkRyYXdpbmcuUHJpbnRpbmcuTWFyZ2lucwIAAAACAAAACQMAAAAF/P///yBTeXN0ZW0uRHJh
        d2luZy5QcmludGluZy5UcmlTdGF0ZQEAAAAFdmFsdWUAAgIAAAAACgoKAfv////8////AAkGAAAABQMA
        AAAnU3lzdGVtLkRyYXdpbmcuUHJpbnRpbmcuUHJpbnRlclNldHRpbmdzEgAAAAtwcmludGVyTmFtZQpk
        cml2ZXJOYW1lCm91dHB1dFBvcnQLcHJpbnRUb0ZpbGUUcHJpbnREaWFsb2dEaXNwbGF5ZWQKZXh0cmFi
        eXRlcwlleHRyYWluZm8GY29waWVzBmR1cGxleAdjb2xsYXRlE2RlZmF1bHRQYWdlU2V0dGluZ3MIZnJv
        bVBhZ2UGdG9QYWdlB21heFBhZ2UHbWluUGFnZQpwcmludFJhbmdlDGRldm1vZGVieXRlcw1jYWNoZWRE
        ZXZtb2RlAQEBAAAABwAEBAQAAAAABAAHAQEHAgceU3lzdGVtLkRyYXdpbmcuUHJpbnRpbmcuRHVwbGV4
        AgAAACBTeXN0ZW0uRHJhd2luZy5QcmludGluZy5UcmlTdGF0ZQIAAAAkU3lzdGVtLkRyYXdpbmcuUHJp
        bnRpbmcuUGFnZVNldHRpbmdzAgAAAAgICAgiU3lzdGVtLkRyYXdpbmcuUHJpbnRpbmcuUHJpbnRSYW5n
        ZQIAAAAHAgIAAAAKBgcAAAAACQcAAAAAAAAACv//Bfj///8eU3lzdGVtLkRyYXdpbmcuUHJpbnRpbmcu
        RHVwbGV4AQAAAAd2YWx1ZV9fAAgCAAAA/////wH3/////P///wAJCgAAAAAAAAAAAAAADycAAAAAAAAF
        9f///yJTeXN0ZW0uRHJhd2luZy5QcmludGluZy5QcmludFJhbmdlAQAAAAd2YWx1ZV9fAAgCAAAAAAAA
        AAAACgUGAAAAH1N5c3RlbS5EcmF3aW5nLlByaW50aW5nLk1hcmdpbnMEAAAABGxlZnQFcmlnaHQDdG9w
        BmJvdHRvbQAAAAAICAgIAgAAAGQAAABkAAAAZAAAAGQAAAABCgAAAAEAAAAJAwAAAAHz/////P///wAK
        CgoB8v////z///8ACQ8AAAABDwAAAAYAAABkAAAAZAAAAGQAAABkAAAACw==
</value>
  </data>
  <data name="gListe.PropBag" xml:space="preserve">
    <value>&lt;?xml version="1.0"?&gt;&lt;Blob&gt;&lt;Styles type="C1.Win.C1TrueDBGrid.Design.ContextWrapper"&gt;&lt;Data&gt;Style51{}Style46{}OddRow{}Style53{}Style57{}RecordSelector{AlignImage:Center;}Style48{}Style54{AlignHorz:Near;}Inactive{ForeColor:InactiveCaptionText;BackColor:InactiveCaption;}Style55{}FilterWatermark{ForeColor:InfoText;BackColor:Info;}Style16{}HighlightRow{ForeColor:HighlightText;BackColor:Highlight;}Editor{}Style15{}FilterBar{}EvenRow{BackColor:234, 241, 250;}Style52{}Group{BackColor:ControlDark;Border:None,,0, 0, 0, 0;AlignVert:Center;}Caption{AlignHorz:Center;}Selected{ForeColor:WindowText;BackColor:239, 239, 247;}Footer{}Style45{}Style49{}Style47{}Normal{}Style58{}Heading{Wrap:True;AlignVert:Center;Border:Flat,ControlDark,0, 1, 0, 1;ForeColor:ControlText;BackColor:Control;}Style50{}Style56{}&lt;/Data&gt;&lt;/Styles&gt;&lt;Splits&gt;&lt;C1.Win.C1TrueDBGrid.MergeView Name="" CaptionHeight="17" ColumnCaptionHeight="17" ColumnFooterHeight="17" MarqueeStyle="DottedCellBorder" RecordSelectorWidth="17" DefRecSelWidth="17" VerticalScrollGroup="1" HorizontalScrollGroup="1"&gt;&lt;CaptionStyle parent="Heading" me="Style54" /&gt;&lt;EditorStyle parent="Editor" me="Style46" /&gt;&lt;EvenRowStyle parent="EvenRow" me="Style52" /&gt;&lt;FilterBarStyle parent="FilterBar" me="Style57" /&gt;&lt;FilterWatermarkStyle parent="FilterWatermark" me="Style58" /&gt;&lt;FooterStyle parent="Footer" me="Style48" /&gt;&lt;GroupStyle parent="Group" me="Style56" /&gt;&lt;HeadingStyle parent="Heading" me="Style47" /&gt;&lt;HighLightRowStyle parent="HighlightRow" me="Style51" /&gt;&lt;InactiveStyle parent="Inactive" me="Style50" /&gt;&lt;OddRowStyle parent="OddRow" me="Style53" /&gt;&lt;RecordSelectorStyle parent="RecordSelector" me="Style55" /&gt;&lt;SelectedStyle parent="Selected" me="Style49" /&gt;&lt;Style parent="Normal" me="Style45" /&gt;&lt;ClientRect&gt;0, 0, 895, 60&lt;/ClientRect&gt;&lt;BorderSide&gt;0&lt;/BorderSide&gt;&lt;/C1.Win.C1TrueDBGrid.MergeView&gt;&lt;/Splits&gt;&lt;NamedStyles&gt;&lt;Style parent="" me="Normal" /&gt;&lt;Style parent="Normal" me="Heading" /&gt;&lt;Style parent="Heading" me="Footer" /&gt;&lt;Style parent="Heading" me="Caption" /&gt;&lt;Style parent="Heading" me="Inactive" /&gt;&lt;Style parent="Normal" me="Selected" /&gt;&lt;Style parent="Normal" me="Editor" /&gt;&lt;Style parent="Normal" me="HighlightRow" /&gt;&lt;Style parent="Normal" me="EvenRow" /&gt;&lt;Style parent="Normal" me="OddRow" /&gt;&lt;Style parent="Heading" me="RecordSelector" /&gt;&lt;Style parent="Normal" me="FilterBar" /&gt;&lt;Style parent="FilterBar" me="FilterWatermark" /&gt;&lt;Style parent="Caption" me="Group" /&gt;&lt;/NamedStyles&gt;&lt;vertSplits&gt;1&lt;/vertSplits&gt;&lt;horzSplits&gt;1&lt;/horzSplits&gt;&lt;Layout&gt;None&lt;/Layout&gt;&lt;DefaultRecSelWidth&gt;17&lt;/DefaultRecSelWidth&gt;&lt;ClientArea&gt;0, 0, 895, 60&lt;/ClientArea&gt;&lt;PrintPageHeaderStyle parent="" me="Style15" /&gt;&lt;PrintPageFooterStyle parent="" me="Style16" /&gt;&lt;/Blob&gt;</value>
  </data>
  <data name="c1Chart1.PropBag" xml:space="preserve">
    <value>&lt;?xml version="1.0"?&gt;&lt;Chart2DPropBag Version="2.0.20111.21027" ColorGeneration="Standard"&gt;&lt;StyleCollection&gt;&lt;NamedStyle Name="Area" ParentName="Area.default" StyleData="BackColor=Window;Border=Solid,ControlDark,1;Rounding=10 10 10 10;" /&gt;&lt;NamedStyle Name="LabelStyleDefault.default" ParentName="Control" StyleData="BackColor=Transparent;Border=None,Transparent,1;" /&gt;&lt;NamedStyle Name="Control" ParentName="Control.default" StyleData="BackColor=234, 242, 251;Border=Solid,ControlDark,1;" /&gt;&lt;NamedStyle Name="AxisY2" ParentName="Area" StyleData="AlignHorz=Far;AlignVert=Center;Rotation=Rotate90;" /&gt;&lt;NamedStyle Name="Header" ParentName="Control" StyleData="Border=None,Transparent,1;" /&gt;&lt;NamedStyle Name="Footer" ParentName="Control" StyleData="Border=None,Transparent,1;" /&gt;&lt;NamedStyle Name="Area.default" ParentName="Control" StyleData="AlignVert=Top;Border=None,Transparent,1;" /&gt;&lt;NamedStyle Name="AxisY" ParentName="Area" StyleData="AlignHorz=Near;AlignVert=Center;Font=Microsoft Sans Serif, 8.25pt;ForeColor=ControlDarkDark;Rotation=Rotate270;" /&gt;&lt;NamedStyle Name="AxisX" ParentName="Area" StyleData="AlignHorz=Center;AlignVert=Bottom;Font=Microsoft Sans Serif, 8.25pt;ForeColor=Black;Rotation=Rotate0;" /&gt;&lt;NamedStyle Name="Legend" ParentName="Legend.default" StyleData="AlignHorz=Center;AlignVert=Top;" /&gt;&lt;NamedStyle Name="LabelStyleDefault" ParentName="LabelStyleDefault.default" /&gt;&lt;NamedStyle Name="PlotArea" ParentName="Area" StyleData="BackColor=Window;BackColor2=;Border=None,Transparent,1;GradientStyle=None;HatchStyle=None;Opaque=True;" /&gt;&lt;NamedStyle Name="Legend.default" ParentName="Control" StyleData="AlignVert=Top;Border=None,Transparent,1;Wrap=False;" /&gt;&lt;NamedStyle Name="Control.default" ParentName="" StyleData="BackColor=Control;Border=None,Transparent,1;ForeColor=ControlText;" /&gt;&lt;/StyleCollection&gt;&lt;ChartGroupsCollection&gt;&lt;ChartGroup Name="Group1" ChartType="Bar" Use3D="False"&gt;&lt;DataSerializer DefaultSet="True"&gt;&lt;DataSeriesCollection&gt;&lt;DataSeriesSerializer&gt;&lt;LineStyle Color="148, 215, 82" /&gt;&lt;SymbolStyle Color="148, 215, 82" OutlineColor="85, 138, 32" Shape="Dot" /&gt;&lt;SeriesLabel&gt;Achat&lt;/SeriesLabel&gt;&lt;X&gt;1;2;3;4;5;6;3.4028234663852886E+38&lt;/X&gt;&lt;Y&gt;20;22;19;24;25;25;3.4028234663852886E+38&lt;/Y&gt;&lt;DataTypes&gt;Single;Single;Double;Double;Double&lt;/DataTypes&gt;&lt;FillStyle /&gt;&lt;Histogram /&gt;&lt;/DataSeriesSerializer&gt;&lt;DataSeriesSerializer&gt;&lt;LineStyle Color="0, 182, 82" /&gt;&lt;SymbolStyle Color="0, 182, 82" OutlineColor="0, 104, 47" Shape="Dot" /&gt;&lt;SeriesLabel&gt;Vente&lt;/SeriesLabel&gt;&lt;X&gt;1;2;2;4;5;6;3.4028234663852886E+38&lt;/X&gt;&lt;Y&gt;2;12;10;12;15;14;3.4028234663852886E+38&lt;/Y&gt;&lt;DataTypes&gt;Single;Single;Double;Double;Double&lt;/DataTypes&gt;&lt;FillStyle FillType="Gradient" GradientStyle="Diagonal" /&gt;&lt;Histogram /&gt;&lt;/DataSeriesSerializer&gt;&lt;DataSeriesSerializer&gt;&lt;LineStyle Color="135, 62, 183" /&gt;&lt;SymbolStyle Color="135, 62, 183" Shape="Diamond" /&gt;&lt;SeriesLabel&gt;series 1&lt;/SeriesLabel&gt;&lt;DataTypes&gt;Double;Double;Double;Double;Double&lt;/DataTypes&gt;&lt;FillStyle /&gt;&lt;Histogram /&gt;&lt;/DataSeriesSerializer&gt;&lt;/DataSeriesCollection&gt;&lt;Highlight /&gt;&lt;/DataSerializer&gt;&lt;/ChartGroup&gt;&lt;ChartGroup Name="Group2" ChartType="Bar" Visible="False"&gt;&lt;DataSerializer&gt;&lt;Highlight /&gt;&lt;/DataSerializer&gt;&lt;/ChartGroup&gt;&lt;/ChartGroupsCollection&gt;&lt;Header Compass="North" Visible="False"&gt;&lt;Text&gt;Header&lt;/Text&gt;&lt;/Header&gt;&lt;Footer Compass="South" Visible="False"&gt;&lt;Text&gt;Footer&lt;/Text&gt;&lt;/Footer&gt;&lt;Legend Compass="East" Visible="False" /&gt;&lt;ChartArea LocationDefault="-1, -1" SizeDefault="-1, -1" Depth="23" Rotation="45" Elevation="45" PlotLocation="-1, -1" PlotSize="-1, -1"&gt;&lt;Margin /&gt;&lt;/ChartArea&gt;&lt;Axes&gt;&lt;Axis Max="6.4464285714285712" Min="0.5535714285714286" AnnoFormat="DateMonth" UnitMajor="1" UnitMinor="0.5" AutoMajor="True" AutoMinor="True" AutoMax="True" AutoMin="True" _onTop="-1" Compass="South"&gt;&lt;Text&gt;Mois&lt;/Text&gt;&lt;GridMajor Visible="True" Spacing="1" /&gt;&lt;ValueLabels&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;ValueLabel Text="qqsd" /&gt;&lt;ValueLabel Text="xcvxcvx" /&gt;&lt;ValueLabel Moveable="True" GridLine="True" Text="xcvxvxv" /&gt;&lt;/ValueLabels&gt;&lt;/Axis&gt;&lt;Axis Max="26" Min="0" UnitMajor="2" UnitMinor="1" AutoMajor="True" AutoMinor="True" AutoMax="True" AutoMin="True" _onTop="-1" Compass="West"&gt;&lt;Text&gt;Qte&lt;/Text&gt;&lt;GridMajor Visible="True" Spacing="2" /&gt;&lt;/Axis&gt;&lt;Axis Max="0" Min="0" UnitMajor="0" UnitMinor="0" AutoMajor="True" AutoMinor="True" AutoMax="True" AutoMin="True" Compass="East" Visible="False" /&gt;&lt;/Axes&gt;&lt;AutoLabelArrangement /&gt;&lt;VisualEffectsData&gt;0,1,0.6,0.2,0.5,0.9,0,1,0.15,0,0,1,0.5,-25,0,0,0,1,64,1;Group0=45,1,0.6,0.1,0.5,0.9,0,0,0.15,0,0,1,0.5,-25,0,0,0,1,64,1;Group1=45,1,0.6,0.1,0.5,0.9,0,0,0.15,0,0,1,0.5,-25,0,0,0,1,64,1&lt;/VisualEffectsData&gt;&lt;/Chart2DPropBag&gt;</value>
  </data>
</root>