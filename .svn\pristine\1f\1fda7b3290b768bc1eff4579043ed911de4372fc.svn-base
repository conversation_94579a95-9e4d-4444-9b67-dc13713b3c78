﻿Public Class fEquivalentOuVenteEnNegative

    Public Retour As String = ""
    Public NomArticle As String = ""

    Private Sub bOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRetour.Click
        Retour = "Vente"
        Me.Hide()
    End Sub

    Private Sub bOK_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bRetour.KeyUp
        If e.KeyCode = Keys.F12 Then
            bOK_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F11 Then
            bEquivalents_Click(sender, e)
            Exit Sub
        End If
    End Sub
    
    Private Sub bEquivalents_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bEquivalents.Click
        Retour = "Equivalent"
        Me.Hide()
    End Sub

    Private Sub bEquivalents_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bEquivalents.KeyUp
        If e.KeyCode = Keys.F12 Then
            bOK_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F11 Then
            bEquivalents_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub fEquivalentOuVenteEnNegative_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        lNomArticle.Text = NomArticle
        bRetour.Focus()
    End Sub
End Class