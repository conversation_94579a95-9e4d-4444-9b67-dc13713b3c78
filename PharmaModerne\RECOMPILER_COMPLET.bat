@echo off
echo ========================================
echo    RECOMPILATION COMPLETE
echo    PHARMA2000 Moderne - Nettoyage Total
echo ========================================
echo.

cd /d "%~dp0"

echo 🧹 NETTOYAGE COMPLET EN COURS...
echo.

echo 1. Suppression des dossiers bin et obj...
if exist "PharmaModerne.Shared\bin" rmdir /s /q "PharmaModerne.Shared\bin"
if exist "PharmaModerne.Shared\obj" rmdir /s /q "PharmaModerne.Shared\obj"
if exist "PharmaModerne.Core\bin" rmdir /s /q "PharmaModerne.Core\bin"
if exist "PharmaModerne.Core\obj" rmdir /s /q "PharmaModerne.Core\obj"
if exist "PharmaModerne.Data\bin" rmdir /s /q "PharmaModerne.Data\bin"
if exist "PharmaModerne.Data\obj" rmdir /s /q "PharmaModerne.Data\obj"
if exist "PharmaModerne.Services\bin" rmdir /s /q "PharmaModerne.Services\bin"
if exist "PharmaModerne.Services\obj" rmdir /s /q "PharmaModerne.Services\obj"
if exist "PharmaModerne.UI\bin" rmdir /s /q "PharmaModerne.UI\bin"
if exist "PharmaModerne.UI\obj" rmdir /s /q "PharmaModerne.UI\obj"
if exist "PharmaModerne.Tests\bin" rmdir /s /q "PharmaModerne.Tests\bin"
if exist "PharmaModerne.Tests\obj" rmdir /s /q "PharmaModerne.Tests\obj"

echo ✅ Dossiers nettoyes
echo.

echo 2. Nettoyage avec dotnet clean...
dotnet clean PharmaModerne.sln
if %errorlevel% neq 0 (
    echo ⚠️ Erreur lors du nettoyage, mais on continue...
)
echo.

echo 3. Restauration des packages NuGet...
dotnet restore PharmaModerne.sln --force
if %errorlevel% neq 0 (
    echo ❌ Erreur lors de la restauration des packages
    pause
    exit /b 1
)
echo ✅ Packages restaures
echo.

echo 4. Compilation en mode Debug...
dotnet build PharmaModerne.sln --configuration Debug --no-restore --verbosity normal
if %errorlevel% neq 0 (
    echo ❌ Erreur lors de la compilation Debug
    echo.
    echo 🔧 TENTATIVE DE CORRECTION :
    echo.
    
    echo Compilation projet par projet...
    echo.
    
    echo - Compilation PharmaModerne.Shared...
    dotnet build PharmaModerne.Shared\PharmaModerne.Shared.csproj --configuration Debug
    if %errorlevel% neq 0 (
        echo ❌ Erreur dans PharmaModerne.Shared
        pause
        exit /b 1
    )
    
    echo - Compilation PharmaModerne.Core...
    dotnet build PharmaModerne.Core\PharmaModerne.Core.csproj --configuration Debug
    if %errorlevel% neq 0 (
        echo ❌ Erreur dans PharmaModerne.Core
        pause
        exit /b 1
    )
    
    echo - Compilation PharmaModerne.Data...
    dotnet build PharmaModerne.Data\PharmaModerne.Data.csproj --configuration Debug
    if %errorlevel% neq 0 (
        echo ❌ Erreur dans PharmaModerne.Data
        pause
        exit /b 1
    )
    
    echo - Compilation PharmaModerne.Services...
    dotnet build PharmaModerne.Services\PharmaModerne.Services.csproj --configuration Debug
    if %errorlevel% neq 0 (
        echo ❌ Erreur dans PharmaModerne.Services
        pause
        exit /b 1
    )
    
    echo - Compilation PharmaModerne.UI...
    dotnet build PharmaModerne.UI\PharmaModerne.UI.csproj --configuration Debug
    if %errorlevel% neq 0 (
        echo ❌ Erreur dans PharmaModerne.UI
        pause
        exit /b 1
    )
    
    echo ✅ Compilation projet par projet reussie !
) else (
    echo ✅ Compilation Debug reussie !
)

echo.
echo 5. Verification de l'executable...
if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe" (
    echo ✅ EXECUTABLE CREE AVEC SUCCES !
    echo.
    echo 📁 Emplacement : PharmaModerne.UI\bin\Debug\net9.0-windows\
    echo 📄 Fichier : PharmaModerne.UI.exe
    echo.
    
    REM Afficher les informations du fichier
    dir "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe"
    echo.
    
    echo 🎉 COMPILATION COMPLETE REUSSIE !
    echo.
    echo 🚀 PROCHAINES ETAPES :
    echo 1. Lancez OUVRIR_DOSSIER_EXECUTABLE.bat pour voir le fichier
    echo 2. Ou lancez LANCER_PHARMA_MODERNE.bat pour demarrer l'app
    echo 3. Ou double-cliquez sur l'executable directement
    echo.
    
    set /p launch="Voulez-vous lancer l'application maintenant ? (O/N) : "
    if /i "%launch%"=="O" (
        echo.
        echo 🚀 Lancement de PHARMA2000 Moderne...
        start "" "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe"
        echo ✅ Application lancee !
    )
    
) else (
    echo ❌ EXECUTABLE NON CREE !
    echo.
    echo 🔍 DIAGNOSTIC :
    echo.
    
    if exist "PharmaModerne.UI\bin" (
        echo ✅ Dossier bin existe
        if exist "PharmaModerne.UI\bin\Debug" (
            echo ✅ Dossier Debug existe
            if exist "PharmaModerne.UI\bin\Debug\net9.0-windows" (
                echo ✅ Dossier net9.0-windows existe
                echo.
                echo 📁 Contenu du dossier :
                dir "PharmaModerne.UI\bin\Debug\net9.0-windows" /B
            ) else (
                echo ❌ Dossier net9.0-windows manquant
            )
        ) else (
            echo ❌ Dossier Debug manquant
        )
    ) else (
        echo ❌ Dossier bin manquant
    )
    
    echo.
    echo 🔧 SOLUTIONS POSSIBLES :
    echo 1. Verifiez que .NET 9 SDK est installe
    echo 2. Verifiez les permissions de fichiers
    echo 3. Essayez de compiler avec Visual Studio
    echo 4. Verifiez l'antivirus (peut bloquer la creation)
    echo.
)

echo.
echo ========================================
echo    RESUME DE LA COMPILATION
echo ========================================
echo.
echo 📊 STATUT DES PROJETS :
if exist "PharmaModerne.Shared\bin\Debug\net9.0\PharmaModerne.Shared.dll" (
    echo ✅ PharmaModerne.Shared - OK
) else (
    echo ❌ PharmaModerne.Shared - ECHEC
)

if exist "PharmaModerne.Core\bin\Debug\net9.0\PharmaModerne.Core.dll" (
    echo ✅ PharmaModerne.Core - OK
) else (
    echo ❌ PharmaModerne.Core - ECHEC
)

if exist "PharmaModerne.Data\bin\Debug\net9.0\PharmaModerne.Data.dll" (
    echo ✅ PharmaModerne.Data - OK
) else (
    echo ❌ PharmaModerne.Data - ECHEC
)

if exist "PharmaModerne.Services\bin\Debug\net9.0\PharmaModerne.Services.dll" (
    echo ✅ PharmaModerne.Services - OK
) else (
    echo ❌ PharmaModerne.Services - ECHEC
)

if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe" (
    echo ✅ PharmaModerne.UI - OK
) else (
    echo ❌ PharmaModerne.UI - ECHEC
)

echo.
echo Appuyez sur une touche pour fermer...
pause >nul
