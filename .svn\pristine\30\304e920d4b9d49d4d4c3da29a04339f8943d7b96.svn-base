﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fNatureEntreeSortie

    Dim cmdNatureEntree As New SqlCommand
    Dim daNatureEntree As New SqlDataAdapter
    Dim daNatureEntreeTestCode As New SqlDataAdapter
    Dim cbNatureEntree As New SqlCommandBuilder
    Dim dsNatureEntree As New DataSet

    Dim cmdNatureSortie As New SqlCommand
    Dim daNatureSortie As New SqlDataAdapter
    Dim daNatureSortieTestCode As New SqlDataAdapter
    Dim cbNatureSortie As New SqlCommandBuilder
    Dim dsNatureSortie As New DataSet


    Dim xNatureEntree As Integer
    Dim ModeNatureEntree As String
    Dim NomNatureEntree As String

    Dim xNatureSortie As Integer
    Dim ModeNatureSortie As String
    Dim NomNatureSortie As String

    Dim CodeExiste As Boolean = False
    Public Sub init()
        afficherNatureEntree()
        afficherNatureSortie()
    End Sub
    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub afficherNatureEntree()
        Dim I As Integer
        Dim Cond As String = "1=1"
        dsNatureEntree.Clear()
        cmdNatureEntree.CommandText = " SELECT * " + _
                                      " FROM NATURE_ENTREE WHERE SupprimeNatureEntre=0 AND " + Cond + _
                                      " ORDER BY CodeNatureEntre"

        '        " CodeNatureEntre, " + _
        '" LibelleNatureEntre " + _

        cmdNatureEntree.Connection = ConnectionServeur
        daNatureEntree = New SqlDataAdapter(cmdNatureEntree)
        daNatureEntree.Fill(dsNatureEntree, "NATURE_ENTREE")

        With gNatureEntree
            .Columns.Clear()
            .DataSource = dsNatureEntree
            .DataMember = "NATURE_ENTREE"
            .Rebind(False)
            .Columns("CodeNatureEntre").Caption = "Code Nature"
            .Columns("LibelleNatureEntre").Caption = "Libelle Nature"
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("CodeNatureEntre").Width = 120
            .Splits(0).DisplayColumns("CodeNatureEntre").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("LibelleNatureEntre").Width = 80
            .Splits(0).DisplayColumns("LibelleNatureEntre").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(0).DisplayColumns("CodeNatureEntre").Locked = True
            .Splits(0).DisplayColumns("LibelleNatureEntre").Locked = True
            .Splits(0).DisplayColumns("SupprimeNatureEntre").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gNatureEntree)
        End With
        gNatureEntree.MoveRelative(xNatureEntree)
        cbNatureEntree = New SqlCommandBuilder(daNatureEntree)

    End Sub

    Private Sub bAjouterEntree_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterEntree.Click
        Dim dr As DataRow
        Dim StrSQL As String = ""

        If tCodeNatureEntree.Text = "" Then
            MsgBox("Veuillez saisir le code de la nature !", MsgBoxStyle.Critical, "Erreur")
            tCodeNatureEntree.Focus()
            Exit Sub
        End If

        If tLibelleNatureEntree.Text = "" Then
            MsgBox("Veuillez saisir le libelle de la nature !", MsgBoxStyle.Critical, "Erreur")
            tLibelleNatureEntree.Focus()
            Exit Sub
        End If

        If CodeExiste = True Then
            MsgBox("Code nature existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
            tCodeNatureEntree.Focus()
            Exit Sub
        End If

        With dsNatureEntree
            dr = .Tables("NATURE_ENTREE").NewRow
            dr.Item("LibelleNatureEntre") = tLibelleNatureEntree.Text
            dr.Item("CodeNatureEntre") = tCodeNatureEntree.Text
            .Tables("NATURE_ENTREE").Rows.Add(dr)
        End With

        Try
            daNatureEntree.Update(dsNatureEntree, "NATURE_ENTREE")
            afficherNatureEntree()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsNatureEntree.Reset()
        End Try

        tCodeNatureEntree.Text = ""
        tLibelleNatureEntree.Text = ""
    End Sub

    Private Sub bSupprimerEntree_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerEntree.Click
        Dim cmd As New SqlCommand
        If gNatureEntree.RowCount > 0 Then
            If MsgBox("Voulez vous vraiment supprimer cette Nature " + Quote(gNatureEntree(gNatureEntree.Row, "LibelleNatureEntre")) + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "UPDATE NATURE_ENTREE SET SupprimeNatureEntre = 1 WHERE CodeNatureEntre =" + Quote(gNatureEntree(gNatureEntree.Row, "CodeNatureEntre"))
                    cmd.ExecuteNonQuery()
                    afficherNatureEntree()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub

    Private Sub gNatureEntree_AfterColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gNatureEntree.AfterColEdit
        Dim dr As DataRow
        Dim StrSQL As String = ""

        With dsNatureEntree.Tables("NATURE_ENTREE_MAJ")
            dr = .Rows(0)
            dr.Item("LibelleNatureEntre") = gNatureEntree(gNatureEntree.Row, "LibelleNatureEntre")

        End With

        Try
            daNatureEntree.Update(dsNatureEntree, "NATURE_ENTREE_MAJ")

        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            afficherNatureEntree()
        End Try
    End Sub

    Private Sub gNatureEntree_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles gNatureEntree.Click
        Dim StrSQL As String = ""
        NomNatureEntree = Quote(gNatureEntree(gNatureEntree.Row, "LibelleNatureEntre"))
        If NomNatureEntree = "" Then
            MsgBox("Veuillez sélectionner le libelle de la Nature !", MsgBoxStyle.Critical, "Erreur")
            gNatureEntree.Focus()
            Exit Sub
        End If

        If (dsNatureEntree.Tables.IndexOf("NATURE_ENTREE_MAJ") > -1) Then
            dsNatureEntree.Tables("NATURE_ENTREE_MAJ").Clear()
        End If

        StrSQL = " SELECT * FROM NATURE_ENTREE WHERE LibelleNatureEntre = " + NomNatureEntree

        cmdNatureEntree.Connection = ConnectionServeur
        cmdNatureEntree.CommandText = StrSQL
        daNatureEntree = New SqlDataAdapter(cmdNatureEntree)
        daNatureEntree.Fill(dsNatureEntree, "NATURE_ENTREE_MAJ")
        cbNatureEntree = New SqlCommandBuilder(daNatureEntree)
    End Sub

    Private Sub tCodeNatureEntree_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeNatureEntree.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLibelleNatureEntree.Focus()
        End If
    End Sub

    Private Sub tCodeNatureEntree_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeNatureEntree.LostFocus
        lTest.Visible = False
    End Sub

    Private Sub tCodeNatureEntree_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeNatureEntree.TextChanged
        If tCodeNatureEntree.Text <> "" Then
            If IsNumeric(tCodeNatureEntree.Text.Substring(Len(tCodeNatureEntree.Text) - 1, 1)) = False Then
                MsgBox("Veuillez introduire des chiffres !", MsgBoxStyle.Critical, "Erreur")
                tCodeNatureEntree.Text = tCodeNatureEntree.Text.Substring(0, Len(tCodeNatureEntree.Text) - 1)
                tCodeNatureEntree.Select(Len(tCodeNatureEntree.Text), 0)
            End If
        End If

        Dim StrSQLtest As String = ""
        If (dsNatureEntree.Tables.IndexOf("NATURE_ENTREE_TEST") > -1) Then
            dsNatureEntree.Tables("NATURE_ENTREE_TEST").Clear()
        End If

        StrSQLtest = " SELECT * FROM NATURE_ENTREE as NATURE_ENTREE_TEST WHERE CodeNatureEntre=" + Quote(tCodeNatureEntree.Text)
        cmdNatureEntree.Connection = ConnectionServeur
        cmdNatureEntree.CommandText = StrSQLtest
        daNatureEntreeTestCode = New SqlDataAdapter(cmdNatureEntree)
        daNatureEntreeTestCode.Fill(dsNatureEntree, "NATURE_ENTREE_TEST")

        If dsNatureEntree.Tables("NATURE_ENTREE_TEST").Rows.Count <> 0 Then
            lTest.Text = "Code non valide déja existe"
            lTest.ForeColor = Color.OrangeRed
            lTest.Visible = True
            CodeExiste = True
        Else
            lTest.Text = "Code valide"
            lTest.ForeColor = Color.LawnGreen
            lTest.Visible = True
            CodeExiste = False
        End If

        If tCodeNatureEntree.Text = "" Then
            lTest.Visible = False
        End If
    End Sub

    '***********************************************************************************************************
    '********************************************* sortie ******************************************************
    '***********************************************************************************************************

    Public Sub afficherNatureSortie()
        Dim I As Integer
        Dim Cond As String = "1=1"
        dsNatureSortie.Clear()
        cmdNatureSortie.CommandText = " SELECT * " + _
                                      " FROM NATURE_SORTIE WHERE SupprimeNatureSortie=0 AND " + Cond + _
                                      " ORDER BY CodeNatureSortie"

        '        " CodeNatureSortie, " + _
        '" LibelleNatureSortie " + _

        cmdNatureSortie.Connection = ConnectionServeur
        daNatureSortie = New SqlDataAdapter(cmdNatureSortie)
        daNatureSortie.Fill(dsNatureSortie, "NATURE_SORTIE")

        With gNatureSortie
            .Columns.Clear()
            .DataSource = dsNatureSortie
            .DataMember = "NATURE_SORTIE"
            .Rebind(False)
            .Columns("CodeNatureSortie").Caption = "Code Nature"
            .Columns("LibelleNatureSortie").Caption = "Libelle Nature"
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("CodeNatureSortie").Width = 120
            .Splits(0).DisplayColumns("CodeNatureSortie").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("LibelleNatureSortie").Width = 80
            .Splits(0).DisplayColumns("LibelleNatureSortie").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(0).DisplayColumns("CodeNatureSortie").Locked = True
            .Splits(0).DisplayColumns("LibelleNatureSortie").Locked = True
            .Splits(0).DisplayColumns("SupprimeNatureSortie").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gNatureSortie)
        End With
        gNatureSortie.MoveRelative(xNatureSortie)
        cbNatureSortie = New SqlCommandBuilder(daNatureSortie)
    End Sub

    Private Sub bAjouterSortie_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterSortie.Click
        Dim dr As DataRow
        Dim StrSQL As String = ""

        If tCodeNatureSortie.Text = "" Then
            MsgBox("Veuillez saisir le code de la nature !", MsgBoxStyle.Critical, "Erreur")
            tCodeNatureSortie.Focus()
            Exit Sub
        End If
        If tLibelleNatureSortie.Text = "" Then
            MsgBox("Veuillez saisir le libelle de la nature !", MsgBoxStyle.Critical, "Erreur")
            tLibelleNatureSortie.Focus()
            Exit Sub
        End If
        If CodeExiste = True Then
            MsgBox("Code nature existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
            tCodeNatureSortie.Focus()
            Exit Sub
        End If
        With dsNatureSortie
            dr = .Tables("NATURE_SORTIE").NewRow
            dr.Item("LibelleNatureSortie") = tLibelleNatureSortie.Text
            dr.Item("CodeNatureSortie") = tCodeNatureSortie.Text
            .Tables("NATURE_SORTIE").Rows.Add(dr)
        End With
        Try
            daNatureSortie.Update(dsNatureSortie, "NATURE_SORTIE")
            afficherNatureSortie()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsNatureSortie.Reset()
        End Try

        tCodeNatureSortie.Text = ""
        tLibelleNatureSortie.Text = ""
    End Sub

    Private Sub bSupprimerSortie_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerSortie.Click
        Dim cmd As New SqlCommand
        If gNatureSortie.RowCount > 0 Then
            If MsgBox("Voulez vous vraiment supprimer cette Nature " + Quote(gNatureSortie(gNatureSortie.Row, "LibelleNatureSortie")) + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "UPDATE NATURE_SORTIE SET SupprimeNatureSortie = 1 WHERE CodeNatureSortie =" + Quote(gNatureSortie(gNatureSortie.Row, "CodeNatureSortie"))
                    cmd.ExecuteNonQuery()
                    afficherNatureSortie()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub

    Private Sub gNatureSortie_AfterColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gNatureSortie.AfterColEdit
        Dim dr As DataRow
        Dim StrSQL As String = ""

        With dsNatureSortie.Tables("NATURE_SORTIE_MAJ")
            dr = .Rows(0)
            dr.Item("LibelleNatureSortie") = gNatureSortie(gNatureSortie.Row, "LibelleNatureSortie")
        End With
        Try
            daNatureSortie.Update(dsNatureSortie, "NATURE_SORTIE_MAJ")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            afficherNatureSortie()
        End Try
    End Sub

    Private Sub gNatureSortie_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gNatureSortie.Click
        Dim StrSQL As String = ""
        NomNatureSortie = Quote(gNatureSortie(gNatureSortie.Row, "LibelleNatureSortie"))
        If NomNatureSortie = "" Then
            MsgBox("Veuillez sélectionner le libelle de la Nature !", MsgBoxStyle.Critical, "Erreur")
            gNatureSortie.Focus()
            Exit Sub
        End If

        If (dsNatureSortie.Tables.IndexOf("NATURE_SORTIE_MAJ") > -1) Then
            dsNatureSortie.Tables("NATURE_SORTIE_MAJ").Clear()
        End If

        StrSQL = " SELECT * FROM NATURE_SORTIE WHERE LibelleNatureSortie = " + NomNatureSortie

        cmdNatureSortie.Connection = ConnectionServeur
        cmdNatureSortie.CommandText = StrSQL
        daNatureSortie = New SqlDataAdapter(cmdNatureSortie)
        daNatureSortie.Fill(dsNatureSortie, "NATURE_SORTIE_MAJ")
        cbNatureSortie = New SqlCommandBuilder(daNatureSortie)
    End Sub

    Private Sub tCodeNatureSortie_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeNatureSortie.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLibelleNatureSortie.Focus()
        End If
    End Sub

    Private Sub tCodeNatureSortie_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeNatureSortie.LostFocus
        lTest1.Visible = False
    End Sub

    Private Sub tCodeNatureSortie_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeNatureSortie.TextChanged
        If tCodeNatureSortie.Text <> "" Then
            If IsNumeric(tCodeNatureSortie.Text.Substring(Len(tCodeNatureSortie.Text) - 1, 1)) = False Then
                MsgBox("Veuillez introduire des chiffres !", MsgBoxStyle.Critical, "Erreur")
                tCodeNatureSortie.Text = tCodeNatureSortie.Text.Substring(0, Len(tCodeNatureSortie.Text) - 1)
                tCodeNatureSortie.Select(Len(tCodeNatureSortie.Text), 0)
            End If
        End If

        Dim StrSQLtest As String = ""
        If (dsNatureSortie.Tables.IndexOf("NATURE_SORTIE_TEST") > -1) Then
            dsNatureSortie.Tables("NATURE_SORTIE_TEST").Clear()
        End If

        StrSQLtest = " SELECT * FROM NATURE_SORTIE as NATURE_SORTIE_TEST WHERE CodeNatureSortie=" + Quote(tCodeNatureSortie.Text)
        cmdNatureSortie.Connection = ConnectionServeur
        cmdNatureSortie.CommandText = StrSQLtest
        daNatureSortieTestCode = New SqlDataAdapter(cmdNatureSortie)
        daNatureSortieTestCode.Fill(dsNatureSortie, "NATURE_SORTIE_TEST")

        If dsNatureSortie.Tables("NATURE_SORTIE_TEST").Rows.Count <> 0 Then
            lTest1.Text = "Code non valide déja existe"
            lTest1.ForeColor = Color.OrangeRed
            lTest1.Visible = True
            CodeExiste = True
        Else
            lTest1.Text = "Code valide"
            lTest1.ForeColor = Color.LawnGreen
            lTest1.Visible = True
            CodeExiste = False
        End If

        If tCodeNatureSortie.Text = "" Then
            lTest1.Visible = False
        End If
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub
End Class