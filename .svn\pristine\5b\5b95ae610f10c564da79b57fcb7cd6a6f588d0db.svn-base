﻿Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared

Public Class fPret

    Dim cmdChargement As New SqlCommand
    Dim cbChargement As New SqlCommandBuilder
    Dim dsPret As New DataSet
    Dim daChargement As New SqlDataAdapter

    Dim cmdPret As New SqlCommand
    Dim cbPret As New SqlCommandBuilder

    Dim daPret As New SqlDataAdapter
    Dim daPret1 As New SqlDataAdapter

    Dim cmdPretEntete As New SqlCommand
    Dim daPretEntete As New SqlDataAdapter
    Dim cbPretEntete As New SqlCommandBuilder

    Dim cmdPretDetail As New SqlCommand
    Dim daPretDetails As New SqlDataAdapter
    Dim cbPretDetails As New SqlCommandBuilder

    Dim mode As String = ""
    Dim StrSQL As String = ""

    Dim DataRowRecherche As DataRow

    Public NumeroPret As String = ""
    Public NumerolignePret As Integer = 0

    Public TotalTTCAchat As Double = 0.0
    Public TotalHTAchat As Double = 0.0

    Public TVA As Double = 0.0
    Public Timbre As Double = 0.3

    '------------------------------------------------- les informations de paiement (retour de fenetre paiement)
    Public PaiementOkNo As String = ""
    Public Operateur As Integer = 0

    Public NouvellePret As DataRow = Nothing 'datarow pour charger l'entête dans la datatable PRET
    Public NouvelArticle As DataRow = Nothing 'datarow pour charger les détails dans la datatable PRET_DETAIL
    Public dr As DataRow = Nothing 'datarow pour charger dans la datatable ARTICLE qui contient les articles recherchés pendant la saisie de la designation

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        Try
            If argument = "114" And bConfirmer.Enabled = True Then
                bConfirmer_Click(sender, e)
            End If
            If argument = "115" And bRecherche.Enabled = True Then
                bRecherche_Click(sender, e)
            End If

            If argument = "116" And bAjouter.Enabled = True Then
                bAjouter_Click(sender, e)
            End If

            If argument = "118" And bSupprimer.Enabled = True Then
                bSupprimer_Click(sender, e)
            End If

            If argument = "119" And bModifier.Enabled = True Then
                bModifier_Click(sender, e)
            End If

            If argument = "121" And bAnnuler.Enabled = True Then
                bAnnuler_Click(sender, e)
            End If

            If argument = "122" And bImprimer.Enabled = True Then
                bImprimer_Click(sender, e)
            End If
            '--------------------- boutton close 
            If argument = "123" And bQuitter.Enabled = True Then
                bQuitter_Click(sender, e)
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "fonctionsF", ex.Message, "0000094", "Erreur  d'exécution  de fonctionsF", True, True, True)

        End Try
    End Sub

    Public Sub Init()
        Try
            'Initialiser les controles
            initLoadControl()

            'chargement des Pharmacies
            initPharmacies()

            'Appel Pour selectionner le dernier ligne 
            NumerolignePret = selectionDernierLignePret()

            'Initialiser la DS Pret
            initPret()

            'Initialiser la DS Pret Details
            initPretDetails()

            'Appel pour charger les information de pret en question
            ChargerPret(NumerolignePret)

            'Mise en forme de la Grid gArticles
            initgArticles()

            'Charger Liste Article
            initArticle()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "Init", ex.Message, "0000095", "Erreur  d'exécution  de Init", True, True, True)

        End Try
    End Sub

    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)

        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""
        Try
            Try
                Quote(ValeurCle)
            Catch ex As Exception
                Return Nothing
                Exit Function
            End Try

            StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurCle)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                Valeur = CmdCalcul.ExecuteScalar().ToString
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "RecupererValeurExecuteScalaire", ex.Message, "0000096", "Erreur  d'exécution  de RecupererValeurExecuteScalaire", True, True, True)

        End Try

        Return (Valeur)
    End Function

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click
        Try
            'Suivi du scénario 
            fMessageException.Show("Pret", "fPret", "bAjouter_Click", "NoException", "NoError", "Clic sur le bouton Ajouter", False, True, False)

            mode = "Ajout"

            'AppelChargerPret: Pour Récuperer la 
            'structure des DS PRET et PRET_DETAILS
            'La valeur 0 est inexistant

            ChargerPret("0")


            '-----------------------------------ajout d'un nouvel enregistrement vide dans les datatables convenables
            NouvelArticle = dsPret.Tables("PRET_DETAILS").NewRow()
            NouvelArticle("Designation") = ""
            NouvelArticle("CodeArticle") = ""
            dsPret.Tables("PRET_DETAILS").Rows.Add(NouvelArticle)

            NouvellePret = dsPret.Tables("PRET").NewRow()
            dsPret.Tables("PRET").Rows.Add(NouvellePret)

            Me.gArticles.Splits(0).DisplayColumns(1).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(4).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(5).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(9).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(10).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(11).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(12).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(13).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(14).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(15).AllowFocus = False



            'Initialiser les Controls utilisés lors de l'opération de Pret
            initControlAjout()


        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "bAjouter_Click", ex.Message, "0000097", "Erreur  d'exécution  de bAjouter_Click", True, True, True)

        End Try
    End Sub


    Private Sub gArticles_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gArticles.Change
        Try

            If (gArticles.Columns(gArticles.Col).DataField() = "Designation" And gArticles.Columns("Designation").Value <> "") Or gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" Then
                Dim StrSQL1 As String = ""
                Dim I As Integer
                Dim NbLigne As Integer
                NbLigne = gArticles.RowCount

                With gListeRecherche
                    .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
                    .Top = Me.gArticles.Top + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight
                End With

                Try
                    dsPret.Tables("ARTICLE").Clear()
                Catch ex As Exception

                    'Gérer l'Exception
                    fMessageException.Show("Pret", "Fpret", "gArticles_Change", ex.Message, "0000052", "Erreur lors de vider la DS ARTICLE", True, True, True)


                End Try
                If gArticles.Row = dsPret.Tables("PRET_DETAILS").Rows.Count - 1 And gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                    gListeRecherche.Visible = True
                Else
                    gListeRecherche.Visible = False
                End If

                'chargement des articles qui sont mis en jeu
                If gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                    If gArticles.Columns("Designation").Value.ToString.Length > 1 Then
                        If gArticles.Columns("Designation").Value.ToString.IndexOf(".") > 0 And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1)) Then    'gArticles.Columns("Designation").Value.ToString.Substring(gArticles.Columns("Designation").Value.ToString.Length - 1, 1) Like "." And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1))
                                StrSQL1 = "SELECT CodeArticle," + _
                                          "Designation," + _
                                          "LibelleForme," + _
                                          "PrixVenteTTC" + _
                                          " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                          " WHERE  " + _
                                          " and ltrim(str(PrixVenteTTC,10,3)) LIKE '" + _
                                          gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + "%' AND Supprime=0 ORDER BY PrixVenteTTC"
                            Else
                                StrSQL1 = "SELECT CodeArticle," + _
                                          "Designation," + _
                                          "LibelleForme," + _
                                          "PrixVenteTTC" + _
                                          " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                          " WHERE  " + _
                                          " Designation LIKE '" + gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + _
                                          "%' AND Supprime=0 ORDER BY Designation"
                            End If
                        Else
                            StrSQL1 = "SELECT CodeArticle," + _
                                      "Designation," + _
                                      "LibelleForme," + _
                                      "PrixVenteTTC" + _
                                      " FROM ARTICLE lEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                      " WHERE " + _
                                      " Designation LIKE '" + gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + _
                                      "%' AND Supprime=0 ORDER BY Designation"
                        End If
                    ElseIf gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" Then
                        StrSQL1 = "SELECT CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC" + _
                                  " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  " WHERE " + _
                                  "CodeABarre LIKE '" + gArticles.Columns("CodeArticle").Value + _
                                  "' AND Supprime=0 ORDER BY Designation"

                    End If
                cmdPret.Connection = ConnectionServeur
                cmdPret.CommandText = StrSQL1
                daPret = New SqlDataAdapter(cmdPret)
                daPret.Fill(dsPret, "ARTICLE")

                If dsPret.Tables("ARTICLE").Rows.Count > 0 Then
                    dr = dsPret.Tables("ARTICLE").Rows(0)
                End If

                With gListeRecherche
                    .Columns.Clear()
                    .DataSource = dsPret
                    .DataMember = "ARTICLE"
                    .Rebind(False)
                    .Columns("CodeArticle").Caption = "Code Article"
                    .Columns("Designation").Caption = "Designation"
                    .Columns("LibelleForme").Caption = "Forme"
                    .Columns("PrixVenteTTC").Caption = "Prix de vente"

                    ' Centrer tous les entêtes
                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    Next
                    ' CentreR tous les valeurs
                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    Next

                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).Visible = False
                    Next

                    .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                    .Splits(0).DisplayColumns("CodeArticle").Visible = False
                    .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
                    .Splits(0).DisplayColumns("Designation").Visible = True
                    .Splits(0).DisplayColumns("LibelleForme").Visible = True
                    .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

                    .Splits(0).DisplayColumns("CodeArticle").Width = 0
                    .Splits(0).DisplayColumns("Designation").Width = 300
                    .Splits(0).DisplayColumns("LibelleForme").Width = 80
                    .Splits(0).DisplayColumns("PrixVenteTTC").Width = 80


                    .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.Aqua
                    .Splits(0).DisplayColumns("LibelleForme").Style.BackColor = Color.Aqua
                    .Splits(0).DisplayColumns("PrixVenteTTC").Style.BackColor = Color.Aqua

                    .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                    .Splits(0).ColumnCaptionHeight = 20
                    .Splits(0).RecordSelectors = False
                    .ExtendRightColumn = True
                    .EmptyRows = True
                    .FetchRowStyles = True
                End With

                Dim Col As New C1.Win.C1TrueDBGrid.C1DataColumn
                Dim dc As C1.Win.C1TrueDBGrid.C1DisplayColumn

                With gListeRecherche
                    .Columns.Insert(0, Col)
                    Col.Caption = "Stock"
                    dc = .Splits(0).DisplayColumns.Item("Stock")
                    dc.Width = 40
                    .Splits(0).DisplayColumns(4).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    .Splits(0).DisplayColumns(4).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.Aqua
                    dc.Visible = True
                    .Rebind(True)
                End With

            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "gArticles_Change", ex.Message, "0000098", "Erreur  d'exécution  de gArticles_Change", True, True, True)

        End Try
    End Sub

    Public Function RecupereNumero()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        Try

            StrSQL = " SELECT max([NumeroPret]) FROM [PRET]"
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQL
            Try
                ValeurActuel = cmdRecupereNum.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
            If ValeurActuel.Length > 4 Then
                If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                    Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                    Numero = Numero + 1
                    numeroConvertit = Numero.ToString
                    For i = 0 To 5 - numeroConvertit.Length
                        numeroConvertit = "0" + numeroConvertit
                    Next
                    ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
                Else
                    ValeurRetour = Year(Today).ToString + "/" + "000001"
                End If
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "RecupereNumero", ex.Message, "0000099", "Erreur  d'exécution  de RecupereNumero", True, True, True)

        End Try

        Return ValeurRetour
    End Function

    Private Sub gArticles_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyUp
        Dim i As Integer = 0
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim QuantiteAAjouter As Integer = 0

        Try
            '---------------------------------- test si on est en mode saisi ou non ---------------------------
            If mode <> "Ajout" And mode <> "Modif" Then
                Exit Sub
            End If
            '---------------------------------- suppression du dernier ligne vide si on a deux lignes au mm temps vide
            '---------------------------------- cas ou on supprime dernier ligne
            'If dsPret.Tables("PRET_DETAILS").Rows.Count - 1 = 1 And gArticles(0, "CodeArticle") = "" And gArticles(1, "CodeArticle") = "" Then
            If dsPret.Tables("PRET_DETAILS").Rows.Count - 1 <> 0 And gArticles(0, "CodeArticle") = "" And gArticles(1, "CodeArticle") = "" Then
                gArticles.MoveLast()
                gArticles.MovePrevious()
                gArticles.Delete()
            End If
            '---------------------------------- test du type de la valeur d'entrée dans la colonne quantité (numéric) ------------
            '---------------------------------- test du  valeur d'entrée dans la colonne quantité < 99999 ------------

            If gArticles.Columns(gArticles.Col).DataField() = "Qte" Then
                If gArticles.Columns("Qte").Value.ToString <> "-" Then   ' pour les quantité négatives
                    If IsNumeric(gArticles.Columns("Qte").Value) = False Then
                        gArticles.Columns("Qte").Value = "1"
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                        gArticles.EditActive = True
                        Exit Sub
                    End If
                    If (gArticles.Columns("Qte").Value = 0) And e.KeyCode = Keys.Enter Then
                        gArticles.Columns("Qte").Value = ""
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                        gArticles.EditActive = True
                        Exit Sub
                    End If
                    If (gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999) And e.KeyCode = Keys.Enter Then
                        gArticles.Columns("Qte").Value = "1"
                        MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                        gArticles.EditActive = True
                        Exit Sub
                    End If
                End If

            End If
            '********************************** Contrôles des numéros de lot et des dates de péremption ************

            ''-------- afféctation du num de lot pour la date de péremption si elle existe deja DANS LA BASE ------
            'If e.KeyCode = Keys.Enter And gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then
            '    If gArticles.Columns("DatePeremption").Value.ToString <> "" Then
            '        StrSQL = " SELECT count(NumeroLotArticle) FROM [LOT_ARTICLE] WHERE DatePeremptionArticle='" + _
            '        gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
            '        gArticles.Columns("CodeArticle").Value + "'"
            '        cmd.Connection = ConnectionServeur
            '        cmd.CommandText = StrSQL

            '        If cmd.ExecuteScalar <> 0 Then

            '            StrSQL = " SELECT NumeroLotArticle FROM [LOT_ARTICLE] WHERE DatePeremptionArticle='" + _
            '                     gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
            '                     gArticles.Columns("CodeArticle").Value + "'"
            '            cmd.Connection = ConnectionServeur
            '            cmd.CommandText = StrSQL
            '            gArticles.Columns("NumeroLotArticle").Value = cmd.ExecuteScalar
            '        Else
            '            gArticles.Columns("NumeroLotArticle").Value = ""
            '        End If
            '    End If
            'End If
            ''-------- afféctation du num de lot pour la date de péremption si elle existe deja DANS LA LISTE -----
            'If gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            '    Dim CodeNewArticle As String = ""
            '    Dim DateNewArticle As Date

            '    CodeNewArticle = gArticles.Columns("CodeArticle").Value
            '    DateNewArticle = gArticles.Columns("DatePeremption").Value

            '    If gArticles.Columns("NumeroLotArticle").Value = "" Then
            '        i = 0
            '        Do While i < gArticles.RowCount - 1
            '            If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '                If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") = DateNewArticle Then
            '                    gArticles.Columns("NumeroLotArticle").Value = gArticles(i, "NumeroLotArticle")
            '                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '                    gArticles.EditActive = True
            '                End If
            '            End If
            '            i = i + 1
            '        Loop
            '    End If
            'End If
            ''--------------------------- test de l'existance d'un ancien lot avec cette date de péremption -----
            'If e.KeyCode = Keys.Enter And gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then
            '    If gArticles.Columns("DatePeremption").Value.ToString <> "" And gArticles.Columns("NumeroLotArticle").Value.ToString <> "" Then
            '        StrSQL = " SELECT count(NumeroLotArticle) FROM [LOT_ARTICLE]WHERE DatePeremptionArticle='" + _
            '                 gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
            '                 gArticles.Columns("CodeArticle").Value + "'"
            '        cmd.Connection = ConnectionServeur
            '        cmd.CommandText = StrSQL

            '        If cmd.ExecuteScalar <> 0 Then
            '            StrSQL = " SELECT NumeroLotArticle FROM [LOT_ARTICLE]WHERE DatePeremptionArticle='" + _
            '                     gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
            '                     gArticles.Columns("CodeArticle").Value + "'"
            '            cmd.Connection = ConnectionServeur
            '            cmd.CommandText = StrSQL

            '            If gArticles.Columns("NumeroLotArticle").Value <> cmd.ExecuteScalar Then
            '                MsgBox("Date de péremption existe pour un autre lot !", MsgBoxStyle.Critical, "Erreur")
            '                gArticles.Columns("DatePeremption").Value = ""
            '                gArticles.Columns("NumeroLotArticle").Value = ""
            '                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '                gArticles.EditActive = False
            '                Exit Sub
            '            End If
            '        End If
            '    End If
            'End If

            ''---------------------------------- test de l'existance du numero de lot pour une autre date --------
            'If (gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle") And e.KeyCode = Keys.Enter Then
            '    StrSQL = " SELECT COUNT(NumeroLotArticle) FROM [LOT_ARTICLE] WHERE NumeroLotArticle='" + _
            '        gArticles.Columns("NumeroLotArticle").Value.ToString + "' AND CodeArticle='" + _
            '        gArticles.Columns("CodeArticle").Value + "' AND DatePeremptionArticle <> '" + _
            '        gArticles.Columns("DatePeremption").Value + "'"
            '    cmd.Connection = ConnectionServeur
            '    cmd.CommandText = StrSQL

            '    If cmd.ExecuteScalar() <> 0 Then
            '        MsgBox("Numero de lot existe déja !", MsgBoxStyle.Critical, "Erreur")
            '        gArticles.Columns("NumeroLotArticle").Value = ""
            '        gArticles.Columns("DatePeremption").Value = ""
            '        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '        gArticles.EditActive = False
            '        Exit Sub
            '    End If
            'End If

            ''--------------- test si le mm numero du lot existe dans la liste au dessus pour le mm article mais 
            ''--------------- avec une date de péremption differente et vise versa--------
            'If (gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle") And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            '    Dim CodeNewArticle As String = ""
            '    Dim DateNewArticle As Date
            '    Dim NumeroLotNewArticle As String = ""

            '    CodeNewArticle = gArticles.Columns("CodeArticle").Value
            '    DateNewArticle = gArticles.Columns("DatePeremption").Value
            '    NumeroLotNewArticle = gArticles.Columns("NumeroLotArticle").Value
            '    Dim QteNewArticle As Integer = 0
            '    '-------------------------- mm code mm numero de lot mais date different
            '    i = 0
            '    Do While i < gArticles.RowCount - 1
            '        If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '            If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") <> DateNewArticle And gArticles(i, "NumeroLotArticle") = NumeroLotNewArticle Then
            '                MsgBox("Numero de lot existe dans la liste pour le mm article mais avec une autre date de péremption!", MsgBoxStyle.Critical, "Erreur")
            '                gArticles.Columns("NumeroLotArticle").Value = ""
            '                gArticles.Columns("DatePeremption").Value = ""
            '                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '                gArticles.EditActive = False
            '                Exit Sub
            '            End If
            '        End If
            '        i = i + 1
            '    Loop
            '    '-------------------------- mm code mm date de lot mais numero different
            '    i = 0
            '    Do While i < gArticles.RowCount - 1
            '        If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '            If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") = DateNewArticle And gArticles(i, "NumeroLotArticle") <> NumeroLotNewArticle Then
            '                MsgBox("Date de péremption existe dans la liste pour le mm article mais avec un autre numéro de lot!", MsgBoxStyle.Critical, "Erreur")
            '                gArticles.Columns("DatePeremption").Value = ""
            '                gArticles.Columns("NumeroLotArticle").Value = ""
            '                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '                gArticles.EditActive = False
            '                Exit Sub
            '            End If
            '        End If
            '        i = i + 1
            '    Loop
            'End If

            ''--------------- test de l'existance du mm article avec la mm date au dessus dans la 
            ''--------------- liste (cas ou on a une date non null)

            'If (gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Or gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle") And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            '    Dim CodeNewArticle As String = ""
            '    Dim DateNewArticle As Date
            '    Dim NumeroLotNewArticle As String = ""
            '    Dim QteNewArticle As Integer = 0

            '    CodeNewArticle = gArticles.Columns("CodeArticle").Value
            '    DateNewArticle = gArticles.Columns("DatePeremption").Value
            '    NumeroLotNewArticle = gArticles.Columns("NumeroLotArticle").Value
            '    QteNewArticle = gArticles.Columns("Qte").Value

            '    i = 0
            '    Do While i < gArticles.RowCount - 1
            '        If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '            If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") = DateNewArticle And gArticles(i, "NumeroLotArticle") = NumeroLotNewArticle And i <> gArticles.Row Then
            '                gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QteNewArticle).ToString
            '                gArticles.MoveLast()
            '                gArticles.Delete()
            '                If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
            '                    NouvelArticle = dsPret.Tables("PRET_DETAILS").NewRow()
            '                    NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
            '                    NouvelArticle("CodeArticle") = ""
            '                    NouvelArticle("CodeABarre") = ""
            '                    dsPret.Tables("PRET_DETAILS").Rows.Add(NouvelArticle)
            '                End If
            '            End If
            '        End If
            '        i = i + 1
            '    Loop
            'End If

            ''--------------- test de l'existance du mm article sans date aussi au dessus dans la 
            ''--------------- liste (cas ou on a une date null)

            'If (gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Or gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle") And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = True Then
            '    Dim CodeNewArticle As String = ""
            '    Dim QteNewArticle As Integer = 0

            '    CodeNewArticle = gArticles.Columns("CodeArticle").Value


            '    QteNewArticle = gArticles.Columns("Qte").Value '--------------------------------------------------

            '    i = 0
            '    Do While i < gArticles.RowCount - 1
            '        If IsDBNull(gArticles(i, "DatePeremption")) = True And gArticles(i, "NumeroLotArticle").ToString = "" Then
            '            If gArticles(i, "CodeArticle") = CodeNewArticle And i <> gArticles.Row Then
            '                gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QteNewArticle).ToString
            '                gArticles.MoveLast()
            '                gArticles.Delete()
            '                If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
            '                    NouvelArticle = dsPret.Tables("PRET_DETAILS").NewRow()
            '                    NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
            '                    NouvelArticle("CodeArticle") = ""
            '                    NouvelArticle("CodeABarre") = ""
            '                    dsPret.Tables("PRET_DETAILS").Rows.Add(NouvelArticle)

            '                End If
            '            End If
            '        End If
            '        i = i + 1
            '    Loop
            'End If
            '*******************************************************************************************************
            '####################################################################################################################
            '--------------- test de l'existance du mm article sans date aussi au dessus dans la 
            '--------------- liste (cas ou on a une date null)

            If gArticles.Columns(gArticles.Col).DataField() = "Qte" Then
                Dim CodeNewArticle As String = ""
                Dim QteNewArticle As Integer = 0

                CodeNewArticle = gArticles.Columns("CodeArticle").Value
                QteNewArticle = Val(gArticles.Columns("Qte").Value)

                i = 0
                Do While i < gArticles.RowCount - 1

                    If gArticles(i, "CodeArticle") = CodeNewArticle And i <> gArticles.Row Then
                        gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QteNewArticle).ToString
                        gArticles.MoveLast()
                        gArticles.Delete()
                        If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                            NouvelArticle = dsPret.Tables("Pret_DETAILS").NewRow()
                            NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                            NouvelArticle("CodeArticle") = ""
                            NouvelArticle("CodeABarre") = ""
                            dsPret.Tables("PRET_DETAILS").Rows.Add(NouvelArticle)


                        End If
                    End If
                    i = i + 1
                Loop
            End If
            '#####################################################################################################################
            '------------------------------ recherche par code ----------------------------------------------
            If gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" And e.KeyCode = Keys.Enter And gArticles.Row = dsPret.Tables("PRET_DETAILS").Rows.Count - 1 Then
                ChargerDetailArticle(gArticles.Columns("CodeABarre").Value.ToString())
                Exit Sub
            ElseIf gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" And e.KeyCode = Keys.Enter And gArticles.Row < dsPret.Tables("PRET_DETAILS").Rows.Count - 1 Then
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
            End If
            '---------------------------------- masquer la liste de recherche si la designation est vide -----------
            If e.KeyCode = Keys.Back And gArticles.Columns("Designation").Value = "" And gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                gListeRecherche.Visible = False
            End If
            '---------------------------- pour passer à la navigation dans la petite liste de recherhce ----------------
            If (e.KeyCode = Keys.Down Or e.KeyCode = Keys.Up) And gListeRecherche.Visible = True Then
                gListeRecherche.Focus()
                gListeRecherche.Col = 2
                gListeRecherche.Row = 1
            End If
            '---------------------------- si l'artcile n'existe pas dans la liste de recherhce sa quantité doit etre 0  ----
            If dsPret.Tables("ARTICLE").Rows.Count - 1 <= 0 And gListeRecherche.Visible = True Then '
                gArticles.Columns("Qte").Value = 0
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
            End If
            '---------------------------- calcul des montants --------------------------------------------------------
            If (gArticles.Columns(gArticles.Col).DataField() = "Qte") And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) Then
                CalculerMontants()
            End If

            '---------------------------------- verouillage des lignes déja confirmées -------------------------
            If (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) And gArticles.Row <> dsPret.Tables("PRET_DETAILS").Rows.Count - 1 Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
                gArticles.Splits(0).DisplayColumns("Designation").Locked = True
            ElseIf gArticles.Row = dsPret.Tables("PRET_DETAILS").Rows.Count Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
            End If

            '------------------------------ suppression d'une date de péremption
            If e.KeyCode = Keys.Delete And gArticles.Row <= dsPret.Tables("PRET_DETAILS").Rows.Count And gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then
                gArticles.EditActive = False
                gArticles.Columns("DatePeremption").Value = ""
            End If
            '------------------------------ suppression de numéro de lot
            If e.KeyCode = Keys.Delete And gArticles.Row <= dsPret.Tables("PRET_DETAILS").Rows.Count And gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle" Then
                gArticles.EditActive = False
                gArticles.Columns("NumeroLotArticle").Value = ""
                gArticles.EditActive = True
            End If

            '--------------------- traitement du clique sur le bouton ENTREE selon la colonne ---------------------------
            If e.KeyCode = Keys.Enter Then ' And (dsPret.Tables("ARTICLE").Rows.Count > 0 Or gArticles.Row <> dsPret.Tables("PRET_DETAILS").Rows.Count - 1) Then
                ChargerGride()    ' pour charger les informations de l'article a partir du fiche article

                If gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                    If gArticles.Columns("CodeArticle").Value = "" Then    ' si l'article choisit n'existe pas dans la liste de recherche
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
                        gArticles.Columns("Designation").Value = ""
                    Else
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                    End If

                ElseIf gArticles.Columns(gArticles.Col).DataField() = "Qte" And e.KeyCode = Keys.Enter Then
                    '    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
                    'ElseIf gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then
                    '    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("NumeroLotArticle"))
                    'ElseIf gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle" Then   ' si on est dans la colonne date de péremption on passe au nouveau ligne

                    If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                        NouvelArticle = dsPret.Tables("PRET_DETAILS").NewRow()
                        NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                        NouvelArticle("CodeArticle") = ""
                        NouvelArticle("CodeABarre") = ""
                        dsPret.Tables("PRET_DETAILS").Rows.Add(NouvelArticle)
                    End If
                    gArticles.MoveLast()
                    Try
                        dsPret.Tables("ARTICLE").Clear()
                    Catch ex As Exception
                    End Try
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))

                End If

            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "gArticles_KeyUp", ex.Message, "0000100", "Erreur  d'exécution  de gArticles_KeyUp", True, True, True)

        End Try
    End Sub

    Private Sub gListeRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListeRecherche.KeyUp
        Try
            ' dans cet évènenment on teste si l évènenment est due à un clique sur le boutton entrée et si l'utilisateur a choisi 
            ' un article et si le curseur est dans la colonne de designation 

            Dim StrSQL As String = ""
            Dim cmd As New SqlCommand
            Dim DatePeremption As Date
            Dim NumeroLotArticle As String = ""

            If gListeRecherche.Visible = False Then
                Exit Sub
            End If
            If e.KeyCode = Keys.Back Then
                gArticles.Focus()
                'gArticles.Col = 3
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
                gArticles.MoveLast()
                gArticles.EditActive = True
            End If

            Dim j As Integer
            Dim NumeroLigne As Integer
            Dim DataRowRecherche As DataRow
            'If e.KeyCode = Keys.Enter And (gArticles.Col = 5 Or gArticles.Col = 3) Then
            If e.KeyCode = Keys.Enter And (gArticles.Columns(gArticles.Col).DataField() = "LibelleForme" Or gArticles.Columns(gArticles.Col).DataField() = "Designation") Then
                If dsPret.Tables("ARTICLE").Rows.Count > 0 Then
                    '--------------------- rehcerche de l'index de la ligne qui contient l'article recherché ---------------
                    For j = 0 To dsPret.Tables("ARTICLE").Rows.Count - 1
                        DataRowRecherche = dsPret.Tables("ARTICLE").Rows(j)
                        If DataRowRecherche.Item("CodeArticle") = gListeRecherche.Columns("CodeArticle").Value Then
                            NumeroLigne = j
                        End If
                    Next

                    '------------------- chargement des données ---------------------------------------------- 
                    dr = dsPret.Tables("ARTICLE").Rows(NumeroLigne)
                    NouvelArticle("NumeroPret") = RecupereNumero()
                    NouvelArticle("CodeArticle") = dr.Item("CodeArticle")
                    NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))

                    Try
                        NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    Catch ex As Exception
                    End Try

                    NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                    ''''
                    NouvelArticle("QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    If NouvelArticle("QuantiteUnitaire") = 0 Then NouvelArticle("QuantiteUnitaire") = 1
                    ''''

                    NouvelArticle("Qte") = NouvelArticle("QuantiteUnitaire") '1

                    NouvelArticle("Stock") = CalculeStock(dr.Item("CodeArticle"))

                    NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("TotalAchatHT") = Math.Round(NouvelArticle("PrixAchatHT") / NouvelArticle("QuantiteUnitaire"), 3) '* dr.Item("PrixVenteTTC")
                    NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("TotalAchatTTC") = Math.Round(NouvelArticle("PrixAchatTTC") / NouvelArticle("QuantiteUnitaire"), 3)
                    NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("TotalTVA") = NouvelArticle("TVA")


                  

                    '----------------------- récupération de la date de péremption
                    StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] " + _
                             "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                             "' AND CodeArticle='" + dr.Item("CodeArticle") + _
                             "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        DatePeremption = cmd.ExecuteScalar()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                    If DatePeremption = #12:00:00 AM# Then
                        'NouvelArticle("DatePeremption") = "01/01/1900" 'System.DateTime.Today.Day.ToString + "/" + System.DateTime.Today.Month.ToString + "/" + System.DateTime.Today.Year.ToString  
                    Else
                        NouvelArticle("DatePeremption") = DatePeremption
                    End If


                    '----------------------- récupération du numéro de lot
                    StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
                             " WHERE DatePeremptionArticle ='" + NouvelArticle("DatePeremption") + _
                             "' AND CodeArticle='" + dr.Item("CodeArticle") + _
                             "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        NumeroLotArticle = cmd.ExecuteScalar()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try
                    If NumeroLotArticle = "" Then
                        NouvelArticle("NumeroLotArticle") = ""
                    Else
                        NouvelArticle("NumeroLotArticle") = NumeroLotArticle
                    End If

                    gArticles.Refresh()
                End If
                gListeRecherche.Visible = False
                gArticles.Focus()
                'gArticles.Col = 3
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "gListeRecherche_KeyUp", ex.Message, "0000101", "Erreur  d'exécution  de gListeRecherche_KeyUp", True, True, True)

        End Try
    End Sub

    Public Sub ChargerGride()
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date
        Dim NumeroLot As String = ""
        Dim NumeroLotArticle As String = ""

        If gListeRecherche.Visible = False Then
            Exit Sub
        End If


        Dim j As Integer
        Dim NumeroLigne As Integer
        Dim DataRowRecherche As DataRow
        'And gArticles.Columns("Designation").Value <> ""
        If dsPret.Tables("ARTICLE").Rows.Count > 0 Then
            '--------------------- rehcerche de l'index de la ligne qui contient l'article recherché ---------------
            For j = 0 To dsPret.Tables("ARTICLE").Rows.Count - 1
                DataRowRecherche = dsPret.Tables("ARTICLE").Rows(j)
                If DataRowRecherche.Item("CodeArticle") = gListeRecherche.Columns("CodeArticle").Value Then
                    NumeroLigne = j
                End If
            Next

            '------------------- chargement des données ---------------------------------------------- 
            dr = dsPret.Tables("ARTICLE").Rows(NumeroLigne)
            NouvelArticle("NumeroPret") = RecupereNumero()
            NouvelArticle("CodeArticle") = dr.Item("CodeArticle")
            NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))

            Try
                NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            Catch ex As Exception
            End Try

            NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

            ''''
            NouvelArticle("QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            If NouvelArticle("QuantiteUnitaire") = 0 Then NouvelArticle("QuantiteUnitaire") = 1
            ''''

            NouvelArticle("Qte") = NouvelArticle("QuantiteUnitaire") '1

            NouvelArticle("Stock") = CalculeStock(dr.Item("CodeArticle"))

            NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("TotalAchatHT") = Math.Round(NouvelArticle("PrixAchatHT") / NouvelArticle("QuantiteUnitaire"), 3) '* dr.Item("PrixVenteTTC")
            NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("TotalAchatTTC") = Math.Round(NouvelArticle("PrixAchatTTC") / NouvelArticle("QuantiteUnitaire"), 3)
            NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("TotalTVA") = NouvelArticle("TVA")




            '----------------------- récupération de la date de péremption
            StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] " + _
                     "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                     "' AND CodeArticle='" + dr.Item("CodeArticle") + _
                     "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                DatePeremption = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If DatePeremption = #12:00:00 AM# Then
                'NouvelArticle("DatePeremption") = "01/01/1900" 'System.DateTime.Today.Day.ToString + "/" + System.DateTime.Today.Month.ToString + "/" + System.DateTime.Today.Year.ToString  
            Else
                NouvelArticle("DatePeremption") = DatePeremption
            End If


            '----------------------- récupération du numéro de lot
            StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
                     " WHERE DatePeremptionArticle ='" + NouvelArticle("DatePeremption") + _
                     "' AND CodeArticle='" + dr.Item("CodeArticle") + _
                     "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                NumeroLotArticle = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            If NumeroLotArticle = "" Then
                NouvelArticle("NumeroLotArticle") = ""
            Else
                NouvelArticle("NumeroLotArticle") = NumeroLotArticle
            End If

            gArticles.Refresh()
        End If
        gListeRecherche.Visible = False
        gArticles.Focus()

    End Sub

    Public Sub ChargerDetailArticle(ByVal CodeABarre As String)
        Dim resultat As String
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date '= "1 / 1 / 1900"
        Dim NumeroLotArticle As String = ""
        Dim CodeArticle As String = ""
        Dim Supprime As String = ""
        Dim QteUnitaireArticle As Integer = 0
        Dim CategorieArticle As Integer = 0
        Dim PreparationArticle As Integer = 0
        Dim CodeArticleMereFractionnement As Integer = 0
        Dim QteUnitaireArticleMere As Integer = 0
        Dim StockArticleMere As Integer = 0
        Try

            CodeArticle = RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "CodeABarre", CodeABarre)
            resultat = RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle)
            Supprime = RecupererValeurExecuteScalaire("Supprime", "ARTICLE", "CodeArticle", CodeArticle)

            If resultat <> "" And Supprime = "False" Then

                NouvelArticle("NumeroPret") = RecupereNumero()
                NouvelArticle("CodeArticle") = CodeArticle
                NouvelArticle("CodeABarre") = CodeABarre
                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)


                ''''
                NouvelArticle("QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", CodeArticle)
                If NouvelArticle("QuantiteUnitaire") = 0 Then NouvelArticle("QuantiteUnitaire") = 1
                ''''

                Try
                    NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                Catch ex As Exception
                End Try


                CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", CodeArticle)
                If CategorieArticle = 9 Then
                    PreparationArticle = RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", CodeArticle)
                End If
                If CategorieArticle = 8 Or (CategorieArticle = 9 And PreparationArticle = 1) Then
                    QteUnitaireArticle = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticle)
                Else
                    QteUnitaireArticle = 1
                End If

                If CategorieArticle = 9 And PreparationArticle = 4 Then
                    Try
                        CodeArticleMereFractionnement = RecupererValeurExecuteScalaire("CodeArticleMere", "FRACTIONNEMENT", "CodeArticleFractionne", CodeArticle)
                        QteUnitaireArticleMere = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticleMereFractionnement)
                        StockArticleMere = CalculeStock(CodeArticleMereFractionnement) * QteUnitaireArticleMere
                    Catch ex As Exception
                    End Try
                End If

                ' ''''
                'NouvelArticle("QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", CodeArticle)
                'If NouvelArticle("QuantiteUnitaire") = 0 Then NouvelArticle("QuantiteUnitaire") = 1
                ' ''''

                NouvelArticle("Qte") = QteUnitaireArticle
                NouvelArticle("QuantiteUnitaire") = QteUnitaireArticle
                NouvelArticle("Stock") = CalculeStock(CodeArticle)

                NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("TotalAchatHT") = Math.Round(NouvelArticle("PrixAchatHT") / NouvelArticle("QuantiteUnitaire"), 3)
                NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("TotalAchatTTC") = Math.Round(NouvelArticle("PrixAchatTTC") / NouvelArticle("QuantiteUnitaire"), 3)
                NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("TotalTVA") = NouvelArticle("TVA")

                '----------------------- récupération de la date de péremption
                StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] " + _
                         "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                         "' AND CodeArticle='" + CodeArticle + _
                         "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    DatePeremption = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If DatePeremption = #12:00:00 AM# Then
                    'NouvelArticle("DatePeremption") = "01/01/1900"
                Else
                    NouvelArticle("DatePeremption") = DatePeremption
                End If

                '----------------------- récupération du numéro de lot
                StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
                         " WHERE DatePeremptionArticle ='" + NouvelArticle("DatePeremption") + _
                         "' AND CodeArticle='" + CodeArticle + _
                         "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    NumeroLotArticle = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
                If NumeroLotArticle = "" Then
                    NouvelArticle("NumeroLotArticle") = ""
                Else
                    NouvelArticle("NumeroLotArticle") = NumeroLotArticle
                End If

                gArticles.Refresh()
                'gArticles.Col = 5
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
            Else
                gArticles.Columns("CodeABarre").Value = ""
                'gArticles.Col = 3
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "ChargerDetailArticle", ex.Message, "0000102", "Erreur  d'exécution  de ChargerDetailArticle", True, True, True)
        End Try
    End Sub

    Public Sub CalculerMontants()
        Dim i As Integer = 0
        Dim TotalTVA As Double = 0.0

        Dim QteUnitaireArticle As Integer = 1
        Dim CategorieArticle As Integer = 0
        Dim PreparationArticle As Integer = 0

        TotalTTCAchat = 0.0
        TotalHTAchat = 0.0

        Try
            Do While i < gArticles.RowCount
                If gArticles(i, "Designation") <> "" Then
                    gArticles(i, "TotalAchatHT") = Math.Round(gArticles(i, "PrixAchatHT") * gArticles(i, "Qte"), 3)
                    gArticles(i, "TotalAchatTTC") = Math.Round(gArticles(i, "PrixAchatTTC") * gArticles(i, "Qte"), 3)

                    CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))
                    If CategorieArticle = 9 Then
                        If RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle")) <> "" Then
                            PreparationArticle = RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))
                        End If
                    End If

                    If CategorieArticle = 8 Or (CategorieArticle = 9 And PreparationArticle = 1) Then
                        QteUnitaireArticle = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))
                    Else
                        QteUnitaireArticle = 1
                    End If

                    ' gArticles(i, "TotalTTC") = Math.Round(gArticles(i, "TotalTTC") / QteUnitaireArticle, 3)
                    gArticles(i, "TotalAchatHT") = Math.Round(gArticles(i, "TotalAchatHT") / QteUnitaireArticle, 3)
                    gArticles(i, "TotalAchatTTC") = Math.Round(gArticles(i, "TotalAchatTTC") / QteUnitaireArticle, 3)
                    gArticles(i, "TotalTVA") = Math.Round(gArticles(i, "TotalTVA") / QteUnitaireArticle, 3)

                    TotalTTCAchat = TotalTTCAchat + gArticles(i, "TotalAchatTTC")
                    TotalHTAchat = TotalHTAchat + gArticles(i, "TotalAchatHT")
                    TotalTVA = TotalTVA + gArticles(i, "TotalTVA")
                End If
                i = i + 1
            Loop

            lTotalHT.Text = TotalHTAchat
            lTotalTTC.Text = TotalTTCAchat
            lTotalTVA.Text = TotalTVA

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "CalculerMontants", ex.Message, "0000103", "Erreur  d'exécution  de CalculerMontants", True, True, True)
        End Try

    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        Try
            'Suivi du scénario 
            fMessageException.Show("Pret", "fPret", "bConfirmer_Click", "NoException", "NoError", "Clic sur le bouton Confirmer", False, True, False)

            Dim I As Integer = 0
            Dim cmd As New SqlCommand
            Dim NumeroLot As String = ""
            Dim TestNumeroLot As String = ""
            Dim NouveauNumeroLot As String = ""
            Dim QuantiteLotSansNumero As Integer = 0
            Dim QuantiteLotAInsere As Integer = 0

            Dim StrMajLOT As String = ""
            Dim NumeroDeLotAEnregistrer As String = ""
            'Dim DateDePeremptionAEnregistrer As Date

            Dim ConfirmerEnregistrer As Boolean = False
            Dim CodeOperateur As String = ""

            NumeroPret = RecupereNumero()

            'Mode Modif

            'Mode Modif
            If mode = "Modif" Then
                If gArticles.RowCount - 1 = 0 And gArticles(0, "CodeArticle") = "" Then
                    If MsgBox("La liste des détails est vide, Voulez-vous supprimer le Pret N° : " + lNumeroPret.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Enregistrer") = MsgBoxResult.Yes Then
                        'Pour appeler le Bouton Annuler
                        bAnnuler.PerformClick()
                        'Pour appeler le bouton de Suppression
                        supprimerPret(True)
                        Exit Sub
                    Else
                        'Pour appeler le Bouton Annuler
                        bAnnuler.PerformClick()
                        'Quitter la procedure apres faire annuler
                        Exit Sub
                    End If
                End If
            Else
                'Mode Ajout
                If dsPret.Tables("PRET_DETAILS").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle") = "" Then
                    MsgBox("Entree Vide !", MsgBoxStyle.Critical, "Erreur")
                    If dsPret.Tables("PRET_DETAILS").Rows.Count - 1 < 0 Then
                        bAjouter_Click(sender, e)
                    End If
                    '----------------------
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
                    gArticles.EditActive = True
                    Exit Sub
                End If
            End If


            '-------------------------pour verifier le calcul : si l'utilisateur ne clique pas entree
            '-------------------------sur la cellule qte du dernier ligne la somme TTC sera fausse
            CalculerMontants()
            '-----------------------------------------------------------------------
            '-----------------------------------------------------------------------

            If cmbPharmacie.Text = "" Then
                MsgBox("Veuillez choisir une pharmacie !", MsgBoxStyle.Critical, "Erreur")
                cmbPharmacie.Focus()
                Exit Sub
            End If

            '------------------------------ demande du mot de passe
            Dim myMotDePasse As New fMotDePasse
            myMotDePasse.ShowDialog()

            ConfirmerEnregistrer = fMotDePasse.Confirmer
            CodeOperateur = fMotDePasse.CodeOperateur

            myMotDePasse.Dispose()
            myMotDePasse.Close()

            If ConfirmerEnregistrer = False Then
                Exit Sub
            End If

            '-------------------------- élémination des lignes vides 
            I = 0
            Do While I < dsPret.Tables("PRET_DETAILS").Rows.Count
                If dsPret.Tables("PRET_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then
                    If dsPret.Tables("PRET_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                        dsPret.Tables("PRET_DETAILS").Rows(I).Delete()
                    End If
                End If
                I = I + 1
            Loop
            CalculerMontants()

            '----------------------- controle des Numeros des lots, codes articles : insertion des doublons
            '----------------------- (Violation du clé primaire dans la table Pret details)


            'Dim p As Integer = 0
            'Dim q As Integer = 0
            'For p = 0 To dsPret.Tables("PRET_DETAILS").Rows.Count - 1
            '    For q = p To dsPret.Tables("PRET_DETAILS").Rows.Count - 1
            '        If dsPret.Tables("PRET_DETAILS").Rows(p).RowState <> DataRowState.Deleted Then
            '            If dsPret.Tables("PRET_DETAILS").Rows(p).Item("CodeArticle") = dsPret.Tables("PRET_DETAILS").Rows(q).Item("CodeArticle") And p <> q Then
            '                If dsPret.Tables("PRET_DETAILS").Rows(p).Item("NumeroLotArticle") = dsPret.Tables("PRET_DETAILS").Rows(q).Item("NumeroLotArticle") Then
            '                    MsgBox("l'article " + dsPret.Tables("PRET_DETAILS").Rows(p).Item("Designation") + " apparaît deux fois avec le même numero de lot ! veuillez supprimer un et ajouter sa quantité dans l'autre", MsgBoxStyle.Critical, "Erreur")
            '                    Exit Sub
            '                End If
            '            End If
            '        End If
            '    Next
            'Next

            Dim p As Integer = 0
            Dim q As Integer = 0
            For p = 0 To dsPret.Tables("PRET_DETAILS").Rows.Count - 1
                For q = p To dsPret.Tables("PRET_DETAILS").Rows.Count - 1
                    If gArticles(p, "CodeArticle") = gArticles(q, "CodeArticle") And p <> q And gArticles(p, "CodeArticle") <> "" And gArticles(q, "CodeArticle") <> "" Then
                        If gArticles(p, "DatePeremption").ToString = gArticles(q, "DatePeremption").ToString Then
                            MsgBox("l'article " + gArticles(p, "Designation") + " apparaît deux fois avec le même numero de lot ! veuillez supprimer un et ajouter sa quantité dans l'autre", MsgBoxStyle.Critical, "Erreur")
                            Exit Sub
                        End If
                    End If
                Next
            Next


            '----------------------- contrôle des dates de péremption si il y a un qui est périmé

            For p = 0 To dsPret.Tables("PRET_DETAILS").Rows.Count - 1
                If dsPret.Tables("PRET_DETAILS").Rows(p).RowState <> DataRowState.Deleted Then
                    If dsPret.Tables("PRET_DETAILS").Rows(p).Item("DatePeremption").ToString <> "" Then
                        If dsPret.Tables("PRET_DETAILS").Rows(p).Item("DatePeremption") < Date.Today Then
                            MsgBox("l'article " + dsPret.Tables("PRET_DETAILS").Rows(p).Item("Designation") + " admet une date de péremption deja dépassé ! veuillez corriger sa date de péremption", MsgBoxStyle.Critical, "Erreur")
                            Exit Sub
                        End If
                    End If
                End If
            Next

            '----------------------------------------
            If mode = "Ajout" Then
                NumeroPret = RecupereNumero()
            End If
            '------------------------------ enregistrement de l'entête de Pret-------------------------
            '-----------------------------------------------------------------------------------------------
            '-----------------------------------------------------------------------------------------------
            If mode = "Modif" Then
                With dsPret
                    .Tables("PRET").Rows(0)("NumeroPret") = lNumeroPret.Text
                    .Tables("PRET").Rows(0)("CodePharmacie") = cmbPharmacie.SelectedValue
                    .Tables("PRET").Rows(0)("CodePersonnel") = CodeOperateur
                    .Tables("PRET").Rows(0)("Date") = lDatePret.Text
                    .Tables("PRET").Rows(0)("TotalHT") = TotalHTAchat
                    .Tables("PRET").Rows(0)("TotalTVA") = lTotalTVA.Text
                    .Tables("PRET").Rows(0)("TotalTTC") = TotalTTCAchat
                    .Tables("PRET").Rows(0)("Vider") = 0
                End With
                Try
                    daPretEntete.Update(dsPret, "PRET")
                Catch ex As Exception
                    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            Else ' Mode = "Ajout"
                dsPret.Tables("PRET").Clear()

                StrSQL = "SELECT top(0) * FROM PRET ORDER BY NumeroPret ASC"
                cmdPret.Connection = ConnectionServeur
                cmdPret.CommandText = StrSQL
                daPret = New SqlDataAdapter(cmdPret)
                daPret.Fill(dsPret, "PRET")
                cbPret = New SqlCommandBuilder(daPret)
                dr = dsPret.Tables("PRET").NewRow()

                With dsPret
                    dr.Item("NumeroPret") = NumeroPret
                    dr.Item("Date") = System.DateTime.Now
                    dr.Item("CodePharmacie") = cmbPharmacie.SelectedValue
                    dr.Item("TotalHT") = lTotalHT.Text
                    dr.Item("TotalTTC") = lTotalTTC.Text
                    dr.Item("TotalTVA") = lTotalTVA.Text
                    dr.Item("CodePersonnel") = CodeOperateur
                    dsPret.Tables("PRET").Rows.Add(dr)
                End With
                Try
                    daPretEntete.Update(dsPret, "PRET")
                Catch ex As Exception
                    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
            '#############################################################################################################################
            ' ''------------------------------ enregistrement des détails de Pret -------------------------
            ' ''-----------------------------------------------------------------------------------------------

            'I = 0
            'Do While I < dsPret.Tables("PRET_DETAILS").Rows.Count

            '    If dsPret.Tables("PRET_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then
            '        If dsPret.Tables("PRET_DETAILS").Rows(I).Item("CodeArticle") = "" Then
            '            dsPret.Tables("PRET_DETAILS").Rows(I).Delete()
            '        Else

            '            If mode = "Modif" Then
            '                dsPret.Tables("PRET_DETAILS").Rows(I).Item("NumeroPret") = lNumeroPret.Text
            '            Else
            '                dsPret.Tables("PRET_DETAILS").Rows(I).Item("NumeroPret") = NumeroPret
            '            End If

            '        End If

            '    End If
            '    I = I + 1
            'Loop
            ' ''-----------------------------------------------------------------------------------------------

            'Try
            '    daPretDetails.Update(dsPret, "PRET_DETAILS")
            'Catch ex As Exception
            '    'Gérer l'Exception
            '    MsgBox(ex.Message)
            '    dsPret.Reset()
            'End Try
            '#############################################################################################################################

            'Gestion du numero de lot

            '#############################################################################################################################
            '#############################################################################################################################
            '#############################################################################################################################

            Dim J As Integer = 0
            Dim DatePeremption As Date
            Dim NumeroLotArticle As String
            Dim QteDemande As Integer
            Dim QteParLot As Integer
            Dim StrSQL1 As String = ""
            Dim SommeQteLotArticle As Integer = 0

            If mode = "Ajout" Then

                Do While J < gArticles.RowCount

                    Try
                        'Clacule la somme de la Qte d'un  Article
                        StrSQL1 = "SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE CodeArticle= " & Quote(gArticles(J, "CodeArticle"))

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL1
                        SommeQteLotArticle = Val(cmd.ExecuteScalar)

                    Catch ex As Exception

                    End Try

                    'Inialiser la Qte a demander
                    QteDemande = gArticles(J, "Qte")

                    '------------------ gestion des numeros de lot
                    If dsPret.Tables("LOT_ARTICLE") IsNot Nothing Then
                        dsPret.Tables("LOT_ARTICLE").Clear()
                    End If

                    StrSQL = " SELECT  NumeroLotArticle, DatePeremptionArticle, QteLotArticle, CodeArticle , 1 as OrdreTri FROM LOT_ARTICLE " + _
                    " WHERE DatePeremptionArticle >'" + _
                    System.DateTime.Now.Date.ToString & "'" + _
                    " AND CodeArticle= " & Quote(gArticles(J, "CodeArticle")) & "" + _
                    " AND QteLotArticle > 0   " & "" + _
                    " UNION  SELECT  NumeroLotArticle, DatePeremptionArticle, QteLotArticle, CodeArticle, 0 as OrdreTri  FROM LOT_ARTICLE " + _
                    " WHERE DatePeremptionArticle is null " + _
                    " AND CodeArticle= " & Quote(gArticles(J, "CodeArticle")) & "" + _
                    " AND QteLotArticle > 0   " + _
                    " ORDER BY OrdreTri desc, DatePeremptionArticle "

                    cmdPret.Connection = ConnectionServeur
                    cmdPret.CommandText = StrSQL
                    daPret = New SqlDataAdapter(cmdPret)
                    daPret.Fill(dsPret, "LOT_ARTICLE")
                    cbPret = New SqlCommandBuilder(daPret)

                    For k = 0 To dsPret.Tables("LOT_ARTICLE").Rows.Count - 1

                        QteParLot = dsPret.Tables("LOT_ARTICLE").Rows(k).Item("QteLotArticle")

                        If (dsPret.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString = "" And
                                dsPret.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "") Then

                            If QteDemande <= QteParLot Then

                                NumeroLotArticle = ""

                                DatePeremption = #12:00:00 AM#

                                StrSQL = "INSERT INTO PRET_DETAILS "
                                StrSQL = StrSQL & "(""NumeroPret"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""Designation"",""CodeForme"",""Qte"",""PrixAchatHT"",""TotalAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""TVA"",""TotalTVA"",""DatePeremption"",""Stock"") "
                                StrSQL = StrSQL & " VALUES('"
                                StrSQL = StrSQL & NumeroPret
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & NumeroLotArticle
                                StrSQL = StrSQL & "',"
                                StrSQL = StrSQL & Quote(gArticles(J, "CodeABarre").ToString)
                                StrSQL = StrSQL & ","
                                StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & QteDemande.ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatHT") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "TVA").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "TotalTVA").ToString
                                StrSQL = StrSQL & "',"
                                If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                    StrSQL = StrSQL & "'" & DatePeremption & "'"
                                Else
                                    StrSQL = StrSQL & "NULL"
                                End If
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & gArticles(J, "Stock").ToString
                                StrSQL = StrSQL & "')"

                                QteDemande = 0

                                cmd.Connection = ConnectionServeur
                                cmd.CommandText = StrSQL
                                Try
                                    cmd.ExecuteNonQuery()

                                Catch ex As Exception
                                    Console.WriteLine(ex.Message)
                                End Try

                            Else

                                NumeroLotArticle = ""

                                DatePeremption = #12:00:00 AM#

                                StrSQL = "INSERT INTO PRET_DETAILS "
                                StrSQL = StrSQL & "(""NumeroPret"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""Designation"",""CodeForme"",""Qte"",""PrixAchatHT"",""TotalAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""TVA"",""TotalTVA"",""DatePeremption"",""Stock"") "
                                StrSQL = StrSQL & " VALUES('"
                                StrSQL = StrSQL & NumeroPret
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & NumeroLotArticle
                                StrSQL = StrSQL & "',"
                                StrSQL = StrSQL & Quote(gArticles(J, "CodeABarre").ToString)
                                StrSQL = StrSQL & ","
                                StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & QteParLot
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatHT") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "TVA").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "TotalTVA").ToString
                                StrSQL = StrSQL & "',"
                                If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                    StrSQL = StrSQL & "'" & DatePeremption & "'"
                                Else
                                    StrSQL = StrSQL & "NULL"
                                End If
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & gArticles(J, "Stock").ToString
                                StrSQL = StrSQL & "')"

                                cmd.Connection = ConnectionServeur
                                cmd.CommandText = StrSQL
                                Try
                                    cmd.ExecuteNonQuery()

                                Catch ex As Exception
                                    Console.WriteLine(ex.Message)
                                End Try
                                QteDemande = QteDemande - QteParLot
                            End If
                            Exit For
                        End If

                        If QteDemande <= QteParLot Then

                            NumeroLotArticle = dsPret.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString

                            If dsPret.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "" Then
                                DatePeremption = #12:00:00 AM#

                            Else
                                DatePeremption = dsPret.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString
                            End If

                            StrSQL = "INSERT INTO PRET_DETAILS "
                            StrSQL = StrSQL & "(""NumeroPret"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""Designation"",""CodeForme"",""Qte"",""PrixAchatHT"",""TotalAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""TVA"",""TotalTVA"",""DatePeremption"",""Stock"") "
                            StrSQL = StrSQL & " VALUES('"
                            StrSQL = StrSQL & NumeroPret
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & NumeroLotArticle
                            StrSQL = StrSQL & "',"
                            StrSQL = StrSQL & Quote(gArticles(J, "CodeABarre").ToString)
                            StrSQL = StrSQL & ","
                            StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & QteDemande
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatHT") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "TVA").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "TotalTVA").ToString
                            StrSQL = StrSQL & "',"
                            If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                StrSQL = StrSQL & "'" & DatePeremption & "'"
                            Else
                                StrSQL = StrSQL & "NULL"
                            End If
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & gArticles(J, "Stock").ToString
                            StrSQL = StrSQL & "')"

                            QteDemande = 0

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try
                                cmd.ExecuteNonQuery()

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                            Exit For
                        Else

                            QteDemande = QteDemande - QteParLot

                            NumeroLotArticle = dsPret.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString

                            If dsPret.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "" Then
                                DatePeremption = #12:00:00 AM#

                            Else
                                DatePeremption = dsPret.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString
                            End If

                            StrSQL = "INSERT INTO PRET_DETAILS "
                            StrSQL = StrSQL & "(""NumeroPret"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""Designation"",""CodeForme"",""Qte"",""PrixAchatHT"",""TotalAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""TVA"",""TotalTVA"",""DatePeremption"",""Stock"") "
                            StrSQL = StrSQL & " VALUES('"
                            StrSQL = StrSQL & NumeroPret
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & NumeroLotArticle
                            StrSQL = StrSQL & "',"
                            StrSQL = StrSQL & Quote(gArticles(J, "CodeABarre").ToString)
                            StrSQL = StrSQL & ","
                            StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & QteParLot
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatHT") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "TVA").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "TotalTVA").ToString
                            StrSQL = StrSQL & "',"
                            If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                StrSQL = StrSQL & "'" & DatePeremption & "'"
                            Else
                                StrSQL = StrSQL & "NULL"
                            End If
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & gArticles(J, "Stock").ToString
                            StrSQL = StrSQL & "')"

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try
                                cmd.CommandTimeout = 360
                                cmd.ExecuteNonQuery()

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                        End If
                    Next

                    If QteDemande > 0 Then

                        NumeroLotArticle = ""

                        DatePeremption = #12:00:00 AM#

                        StrSQL = "INSERT INTO PRET_DETAILS "
                        StrSQL = StrSQL & "(""NumeroPret"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""Designation"",""CodeForme"",""Qte"",""PrixAchatHT"",""TotalAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""TVA"",""TotalTVA"",""DatePeremption"",""Stock"") "
                        StrSQL = StrSQL & " VALUES('"
                        StrSQL = StrSQL & NumeroPret
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & NumeroLotArticle
                        StrSQL = StrSQL & "',"
                        StrSQL = StrSQL & Quote(gArticles(J, "CodeABarre").ToString)
                        StrSQL = StrSQL & ","
                        StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & QteDemande
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatHT") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "TVA").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "TotalTVA").ToString
                        StrSQL = StrSQL & "',"
                        If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                            StrSQL = StrSQL & "'" & DatePeremption & "'"
                        Else
                            StrSQL = StrSQL & "NULL"
                        End If
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & gArticles(J, "Stock").ToString
                        StrSQL = StrSQL & "')"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()

                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try

                    ElseIf QteDemande < 0 And SommeQteLotArticle Then
                        NumeroLotArticle = ""

                        DatePeremption = #12:00:00 AM#

                        StrSQL = "INSERT INTO PRET_DETAILS "
                        StrSQL = StrSQL & "(""NumeroPret"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""Designation"",""CodeForme"",""Qte"",""PrixAchatHT"",""TotalAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""TVA"",""TotalTVA"",""DatePeremption"",""Stock"") "
                        StrSQL = StrSQL & " VALUES('"
                        StrSQL = StrSQL & NumeroPret
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & NumeroLotArticle
                        StrSQL = StrSQL & "',"
                        StrSQL = StrSQL & Quote(gArticles(J, "CodeABarre").ToString)
                        StrSQL = StrSQL & ","
                        StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & QteDemande
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatHT") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "TVA").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "TotalTVA").ToString
                        StrSQL = StrSQL & "',"
                        If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                            StrSQL = StrSQL & "'" & DatePeremption & "'"
                        Else
                            StrSQL = StrSQL & "NULL"
                        End If
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & gArticles(J, "Stock").ToString
                        StrSQL = StrSQL & "')"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()

                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try
                    End If

                    J = J + 1
                Loop


            ElseIf mode = "Modif" Then

                Try
                    cmdPret.Connection = ConnectionServeur
                    cmdPret.CommandText = "DELETE FROM PRET_DETAILS WHERE NumeroPret ='" + lNumeroPret.Text + "'"
                    cmdPret.ExecuteNonQuery()

                Catch ex As Exception
                    WriteLine(ex.Message)
                End Try


                '------------------ gestion des numeros de lot
                Do While J < gArticles.RowCount
                    Try
                        'Clacule la somme de la Qte d'un  Article
                        StrSQL1 = "SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE CodeArticle= " & Quote(gArticles(J, "CodeArticle"))
                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL1
                        SommeQteLotArticle = Val(cmd.ExecuteScalar)
                    Catch ex As Exception
                    End Try

                    If dsPret.Tables("LOT_ARTICLE") IsNot Nothing Then
                        dsPret.Tables("LOT_ARTICLE").Clear()
                    End If

                    StrSQL = " SELECT  NumeroLotArticle, DatePeremptionArticle, QteLotArticle, CodeArticle , 1 as OrdreTri FROM LOT_ARTICLE " + _
                    " WHERE DatePeremptionArticle >'" + _
                    System.DateTime.Now.Date.ToString & "'" + _
                    " AND CodeArticle= " & Quote(gArticles(J, "CodeArticle")) & "" + _
                    " AND QteLotArticle > 0   " & "" + _
                    " UNION  SELECT  NumeroLotArticle, DatePeremptionArticle, QteLotArticle, CodeArticle, 0 as OrdreTri  FROM LOT_ARTICLE " + _
                    " WHERE DatePeremptionArticle is null " + _
                    " AND CodeArticle= " & Quote(gArticles(J, "CodeArticle")) & "" + _
                    " AND QteLotArticle > 0   " + _
                    " ORDER BY OrdreTri desc, DatePeremptionArticle "

                    cmdPret.Connection = ConnectionServeur
                    cmdPret.CommandText = StrSQL
                    daPret = New SqlDataAdapter(cmdPret)
                    daPret.Fill(dsPret, "LOT_ARTICLE")
                    cbPret = New SqlCommandBuilder(daPret)

                    'Inialiser la Qte a demander
                    QteDemande = gArticles(J, "Qte")

                    For k = 0 To dsPret.Tables("LOT_ARTICLE").Rows.Count - 1

                        QteParLot = dsPret.Tables("LOT_ARTICLE").Rows(k).Item("QteLotArticle")

                        If (dsPret.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString = "" And
                                dsPret.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "") Then

                            If QteDemande <= QteParLot Then

                                NumeroLotArticle = ""

                                DatePeremption = #12:00:00 AM#

                                StrSQL = "INSERT INTO PRET_DETAILS "
                                StrSQL = StrSQL & "(""NumeroPret"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""Designation"",""CodeForme"",""Qte"",""PrixAchatHT"",""TotalAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""TVA"",""TotalTVA"",""DatePeremption"",""Stock"") "
                                StrSQL = StrSQL & " VALUES('"
                                StrSQL = StrSQL & lNumeroPret.Text
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & NumeroLotArticle
                                StrSQL = StrSQL & "',"
                                StrSQL = StrSQL & Quote(gArticles(J, "CodeABarre").ToString)
                                StrSQL = StrSQL & ","
                                StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & QteDemande.ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatHT") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "TVA").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "TotalTVA").ToString
                                StrSQL = StrSQL & "',"
                                If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                    StrSQL = StrSQL & "'" & DatePeremption & "'"
                                Else
                                    StrSQL = StrSQL & "NULL"
                                End If
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & gArticles(J, "Stock").ToString
                                StrSQL = StrSQL & "')"

                                QteDemande = 0

                                cmd.Connection = ConnectionServeur
                                cmd.CommandText = StrSQL
                                Try
                                    cmd.ExecuteNonQuery()

                                Catch ex As Exception
                                    Console.WriteLine(ex.Message)
                                End Try

                            Else

                                NumeroLotArticle = ""

                                DatePeremption = #12:00:00 AM#


                                StrSQL = "INSERT INTO PRET_DETAILS "
                                StrSQL = StrSQL & "(""NumeroPret"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""Designation"",""CodeForme"",""Qte"",""PrixAchatHT"",""TotalAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""TVA"",""TotalTVA"",""DatePeremption"",""Stock"") "
                                StrSQL = StrSQL & " VALUES('"
                                StrSQL = StrSQL & lNumeroPret.Text
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & NumeroLotArticle
                                StrSQL = StrSQL & "',"
                                StrSQL = StrSQL & Quote(gArticles(J, "CodeABarre").ToString)
                                StrSQL = StrSQL & ","
                                StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & QteParLot
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatHT") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "TVA").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "TotalTVA").ToString
                                StrSQL = StrSQL & "',"
                                If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                    StrSQL = StrSQL & "'" & DatePeremption & "'"
                                Else
                                    StrSQL = StrSQL & "NULL"
                                End If
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & gArticles(J, "Stock").ToString
                                StrSQL = StrSQL & "')"

                                cmd.Connection = ConnectionServeur
                                cmd.CommandText = StrSQL
                                Try
                                    cmd.ExecuteNonQuery()

                                Catch ex As Exception
                                    Console.WriteLine(ex.Message)
                                End Try

                                QteDemande = QteDemande - QteParLot

                            End If

                            Exit For

                        End If

                        If QteDemande <= QteParLot Then


                            NumeroLotArticle = dsPret.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString

                            If dsPret.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "" Then
                                DatePeremption = #12:00:00 AM#

                            Else
                                DatePeremption = dsPret.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString
                            End If


                            StrSQL = "INSERT INTO PRET_DETAILS "
                            StrSQL = StrSQL & "(""NumeroPret"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""Designation"",""CodeForme"",""Qte"",""PrixAchatHT"",""TotalAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""TVA"",""TotalTVA"",""DatePeremption"",""Stock"") "
                            StrSQL = StrSQL & " VALUES('"
                            StrSQL = StrSQL & lNumeroPret.Text
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & NumeroLotArticle
                            StrSQL = StrSQL & "',"
                            StrSQL = StrSQL & Quote(gArticles(J, "CodeABarre").ToString)
                            StrSQL = StrSQL & ","
                            StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & QteDemande
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatHT") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "TVA").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "TotalTVA").ToString
                            StrSQL = StrSQL & "',"
                            If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                StrSQL = StrSQL & "'" & DatePeremption & "'"
                            Else
                                StrSQL = StrSQL & "NULL"
                            End If
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & gArticles(J, "Stock").ToString
                            StrSQL = StrSQL & "')"

                            QteDemande = 0

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try
                                cmd.ExecuteNonQuery()

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                            Exit For

                        Else

                            QteDemande = QteDemande - QteParLot

                            NumeroLotArticle = dsPret.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString

                            If dsPret.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "" Then
                                DatePeremption = #12:00:00 AM#

                            Else
                                DatePeremption = dsPret.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString
                            End If

                            StrSQL = "INSERT INTO PRET_DETAILS "
                            StrSQL = StrSQL & "(""NumeroPret"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""Designation"",""CodeForme"",""Qte"",""PrixAchatHT"",""TotalAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""TVA"",""TotalTVA"",""DatePeremption"",""Stock"") "
                            StrSQL = StrSQL & " VALUES('"
                            StrSQL = StrSQL & lNumeroPret.Text
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & NumeroLotArticle
                            StrSQL = StrSQL & "',"
                            StrSQL = StrSQL & Quote(gArticles(J, "CodeABarre").ToString)
                            StrSQL = StrSQL & ","
                            StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & QteParLot
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatHT") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "TVA").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "TotalTVA").ToString
                            StrSQL = StrSQL & "',"
                            If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                StrSQL = StrSQL & "'" & DatePeremption & "'"
                            Else
                                StrSQL = StrSQL & "NULL"
                            End If
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & gArticles(J, "Stock").ToString
                            StrSQL = StrSQL & "')"

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try
                                cmd.ExecuteNonQuery()

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                        End If

                    Next

                    If QteDemande > 0 Then

                        NumeroLotArticle = ""

                        DatePeremption = #12:00:00 AM#



                        StrSQL = "INSERT INTO PRET_DETAILS "
                        StrSQL = StrSQL & "(""NumeroPret"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""Designation"",""CodeForme"",""Qte"",""PrixAchatHT"",""TotalAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""TVA"",""TotalTVA"",""DatePeremption"",""Stock"") "
                        StrSQL = StrSQL & " VALUES('"
                        StrSQL = StrSQL & lNumeroPret.Text
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & NumeroLotArticle
                        StrSQL = StrSQL & "',"
                        StrSQL = StrSQL & Quote(gArticles(J, "CodeABarre").ToString)
                        StrSQL = StrSQL & ","
                        StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & QteDemande
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatHT") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "TVA").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "TotalTVA").ToString
                        StrSQL = StrSQL & "',"
                        If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                            StrSQL = StrSQL & "'" & DatePeremption & "'"
                        Else
                            StrSQL = StrSQL & "NULL"
                        End If
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & gArticles(J, "Stock").ToString
                        StrSQL = StrSQL & "')"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()

                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try

                    ElseIf QteDemande < 0 And SommeQteLotArticle < 0 Then

                        NumeroLotArticle = ""

                        DatePeremption = #12:00:00 AM#



                        StrSQL = "INSERT INTO PRET_DETAILS "
                        StrSQL = StrSQL & "(""NumeroPret"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""Designation"",""CodeForme"",""Qte"",""PrixAchatHT"",""TotalAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""TVA"",""TotalTVA"",""DatePeremption"",""Stock"") "
                        StrSQL = StrSQL & " VALUES('"
                        StrSQL = StrSQL & lNumeroPret.Text
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & NumeroLotArticle
                        StrSQL = StrSQL & "',"
                        StrSQL = StrSQL & Quote(gArticles(J, "CodeABarre").ToString)
                        StrSQL = StrSQL & ","
                        StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & QteDemande
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatHT") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "TVA").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "TotalTVA").ToString
                        StrSQL = StrSQL & "',"
                        If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                            StrSQL = StrSQL & "'" & DatePeremption & "'"
                        Else
                            StrSQL = StrSQL & "NULL"
                        End If
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & gArticles(J, "Stock").ToString
                        StrSQL = StrSQL & "')"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()

                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try

                    End If

                    J = J + 1
                Loop


            End If
            '#############################################################################################################################
            '#############################################################################################################################
            '#############################################################################################################################




            'si le mode Ajout
            If mode = "Ajout" Then

                'Appel Pour selectionner le dernier ligne 
                NumerolignePret = selectionDernierLignePret()

            End If

            'changer le mode en consultation
            mode = "Consultation"

            'Appel pour charger les information de pret en question
            ChargerPret(NumerolignePret)


            'initialisation des btns
            initBoutons()

            'pour bloquer le saisie
            'bloquerSaisie()

            gListeRecherche.Visible = False

        Catch ex As Exception
            'Gérer l'Exception
            'fMessageException.Show("Pret", "fPret", "bConfirmer_Click", ex.Message, "0000104", "Erreur  d'exécution  de bConfirmer_Click", True, True, True)
        End Try
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Try
            'Suivi du scénario

            fMessageException.Show("Pret", "fPret", "bAnnuler_Click", "NoException", "NoError", "Clic sur le bouton Annuler", False, True, False)

            If dsPret.Tables("PRET_DETAILS").Rows.Count - 1 > 0 And gArticles(0, "CodeArticle") <> "" Then
                If MsgBox("Voulez vous vraiment annuler cette Pret ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Annuler Pret") = MsgBoxResult.No Then
                    Exit Sub
                End If
            End If
            '--------changer le mode en concultation

            mode = "Consultation"

            'Refreche liste Pret

            ChargerPret(NumerolignePret)

            'Pour initialiser les btns suivant le mode et l'etat du table dans la BD

            initBoutons()

            gListeRecherche.Visible = False

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "bAnnuler_Click", ex.Message, "0000105", "Erreur  d'exécution  de bAnnuler_Click", True, True, True)
        End Try

    End Sub

    Private Sub cmbPharmacie_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbPharmacie.KeyDown
        Try
            If e.KeyData <> "123" Then 'F12: Quitter
                If e.KeyCode = Keys.Enter Then
                    cmbPharmacie.Text = cmbPharmacie.WillChangeToText
                    gArticles.Focus()
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
                    gArticles.EditActive = True
                Else
                    cmbPharmacie.OpenCombo()
                End If
            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "cmbPharmacie_KeyDown", ex.Message, "0000106", "Erreur  d'exécution  de cmbPharmacie_KeyDown", True, True, True)
        End Try
    End Sub


    Private Sub bLast_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLast.Click
        Try
            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner la derniere ligne 
            NumerolignePret = selectionDernierLignePret()

            'Appel pour charger les information de l'Pret en question
            ChargerPret(NumerolignePret)

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "bLast_Click", ex.Message, "0000107", "Erreur  d'exécution  de bLast_Click", True, True, True)
        End Try
    End Sub


    Private Sub bFirst_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFirst.Click
        Try
            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner le dernier ligne 
            selectionPremierLignePret()

            'Appel pour charger les information de l'Pret en question
            ChargerPret(NumerolignePret)

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "bFirst_Click", ex.Message, "0000108", "Erreur  d'exécution  de bFirst_Click", True, True, True)
        End Try
    End Sub

    Private Sub bNext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bNext.Click
        Try
            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner l'element suivant 
            selectionLignePretSuivante()

            'Appel pour charger les information de l'Pret en question
            ChargerPret(NumerolignePret)

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "bNext_Click", ex.Message, "0000109", "Erreur  d'exécution  de bNext_Click", True, True, True)
        End Try
    End Sub

    Private Sub bPrevious_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPrevious.Click
        Try
            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner l'element precedent 
            selectionLignePretPrecedent()

            'Appel pour charger les information de l'Pret en question
            ChargerPret(NumerolignePret)

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "bPrevious_Click", ex.Message, "0000110", "Erreur  d'exécution  de bPrevious_Click", True, True, True)
        End Try

    End Sub

    Private Sub lTotalTTC_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotalTTC.TextChanged
        Try
            lTotalTTC.Text = lTotalTTC.Text
            If lTotalTTC.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lTotalTTC.Text, ".")
                If lTotalTTC.Text.Length - x = 1 Then
                    lTotalTTC.Text = lTotalTTC.Text + ("00")
                ElseIf lTotalTTC.Text.Length - x = 2 Then
                    lTotalTTC.Text = lTotalTTC.Text + ("0")
                End If
            Else
                lTotalTTC.Text = lTotalTTC.Text + ".000"
            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "lTotalTTC_TextChanged", ex.Message, "0000111", "Erreur  d'exécution  de lTotalTTC_TextChanged", True, True, True)
        End Try
    End Sub

    Private Sub lTotHT_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotalHT.TextChanged
        Try
            lTotalHT.Text = lTotalHT.Text
            If lTotalHT.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lTotalHT.Text, ".")
                If lTotalHT.Text.Length - x = 1 Then
                    lTotalHT.Text = lTotalHT.Text + ("00")
                ElseIf lTotalHT.Text.Length - x = 2 Then
                    lTotalHT.Text = lTotalHT.Text + ("0")
                End If
            Else
                lTotalHT.Text = lTotalHT.Text + ".000"
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "lTotHT_TextChanged", ex.Message, "0000112", "Erreur  d'exécution  de lTotHT_TextChanged", True, True, True)
        End Try
    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click
        Try
            'Suivi du scénario 
            fMessageException.Show("Pret", "fPret", "bSupprimer_Click", "NoException", "NoError", "Clic sur le bouton Supprimer", False, True, False)

            supprimerPret(False)

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "bSupprimer_Click", ex.Message, "0000113", "Erreur  d'exécution  de bSupprimer_Click", True, True, True)
        End Try

    End Sub

    Private Sub supprimerPret(ByVal msgShow As Boolean)

        Dim NumeroPret As String
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""
        NumeroPret = lNumeroPret.Text

        Try
            'Si le mode est Ajout ou Modif
            If mode = "Ajout" Or mode = "Modif" Then

                'Si  la liste est vide, quitter la procedure
                If gArticles.RowCount = 0 Then
                    Exit Sub
                End If

                If gArticles.RowCount > 0 Then

                    'Test si la lign est NEW ADDED et elle est vide
                    If gArticles(gArticles.Row, ("CodeArticle")) <> "" Then
                        gArticles.Delete()
                        CalculerMontants()
                    End If

                    If gArticles.RowCount <= 0 Then
                        bAjouter.PerformClick()

                    End If

                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))

                    Exit Sub
                Else

                    CalculerMontants()

                End If

            Else 'mode  consultation
                If NumeroPret = "" Then
                    MsgBox("Aucun Pret à supprimer !", MsgBoxStyle.Critical, "Information")
                    Exit Sub
                Else

                    If msgShow = False Then

                        If MsgBox("Voulez vous vraiment supprimer cet Pret " + lNumeroPret.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then

                            '-------- demande du mot de passe

                            Dim myMotDePasse As New fMotDePasse
                            myMotDePasse.ShowDialog()
                            ConfirmerEnregistrer = fMotDePasse.Confirmer
                            CodeOperateur = fMotDePasse.CodeOperateur
                            myMotDePasse.Dispose()
                            myMotDePasse.Close()
                            If ConfirmerEnregistrer = False Then
                                Exit Sub

                            End If

                            Try
                                'delete de la table Pret details
                                cmdPret.Connection = ConnectionServeur
                                cmdPret.CommandText = "DELETE FROM PRET_DETAILS WHERE NumeroPret ='" + NumeroPret + "'"
                                cmdPret.ExecuteNonQuery()
                                'delete de la table Pret
                                cmdPret.Connection = ConnectionServeur
                                cmdPret.CommandText = "DELETE FROM PRET WHERE NumeroPret ='" + NumeroPret + "'"
                                cmdPret.ExecuteNonQuery()

                                'Ma nouvelle position

                                If NumerolignePret > 1 Then
                                    NumerolignePret = NumerolignePret - 1
                                ElseIf NumerolignePret = 1 Then
                                    initLoadControl()

                                End If
                                'charger la nouvelle position
                                ChargerPret(NumerolignePret)

                            Catch ex As Exception

                                'Gérer l'Exception
                                fMessageException.Show("Pret", "fPret", "supprimerPret()", ex.Message, "0000116", "Erreur lors d'executer la requette", True, True, True)

                            End Try

                            MsgBox("Pret supprimé !", MsgBoxStyle.Information, "Information")

                        Else

                            Exit Sub

                        End If

                    Else

                        '-------- demande du mot de passe

                        Dim myMotDePasse As New fMotDePasse
                        myMotDePasse.ShowDialog()
                        ConfirmerEnregistrer = fMotDePasse.Confirmer
                        CodeOperateur = fMotDePasse.CodeOperateur
                        myMotDePasse.Dispose()
                        myMotDePasse.Close()
                        If ConfirmerEnregistrer = False Then
                            Exit Sub

                        End If

                        Try
                            'delete de la table Pret details
                            cmdPret.Connection = ConnectionServeur
                            cmdPret.CommandText = "DELETE FROM PRET_DETAILS WHERE NumeroPret ='" + NumeroPret + "'"
                            cmdPret.ExecuteNonQuery()
                            'delete de la table Pret
                            cmdPret.Connection = ConnectionServeur
                            cmdPret.CommandText = "DELETE FROM PRET WHERE NumeroPret ='" + NumeroPret + "'"
                            cmdPret.ExecuteNonQuery()

                            'Ma nouvelle position

                            If NumerolignePret > 1 Then
                                NumerolignePret = NumerolignePret - 1
                            ElseIf NumerolignePret = 1 Then
                                initLoadControl()

                            End If
                            'charger la nouvelle position
                            ChargerPret(NumerolignePret)

                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Pret", "fPret", "supprimerPret()", ex.Message, "0000115", "Erreur lors d'executer la requette", True, True, True)

                        End Try

                        MsgBox("Pret supprimé !", MsgBoxStyle.Information, "Information")

                        Exit Sub

                    End If

                End If

            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "supprimerPret", ex.Message, "0000114", "Erreur  d'exécution  de supprimerPret", True, True, True)
        End Try
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        Try
            'Suivi du scénario 
            fMessageException.Show("Pret", "fPret", "bQuitter_Click", "NoException", "NoError", "Clic sur le bouton Quiter", False, True, False)

            If mode = "Consultation" Then
                fMain.Tab.SelectedTab.Dispose()
                Exit Sub
            End If

            If dsPret.Tables("PRET_DETAILS").Rows.Count > 0 And gArticles(0, "CodeArticle") <> "" Then
                If MsgBox("Voulez vous vraiment Fermer cette Pret ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Fermer Pret") = MsgBoxResult.No Then
                    Exit Sub
                End If
            End If
            fMain.Tab.SelectedTab.Dispose()

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "bQuitter_Click", ex.Message, "0000117", "Erreur  d'exécution  de bQuitter_Click", True, True, True)
        End Try
    End Sub


    Private Sub gArticles_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gArticles.MouseClick
        Try
            '---------------------------------- verouillage des lignes déja confirmées -------------------------
            If mode = "Ajout" Then
                If gArticles.Row <> dsPret.Tables("PRET_DETAILS").Rows.Count - 1 Then
                    gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = True
                    gArticles.Splits(0).DisplayColumns("Designation").Locked = True
                ElseIf gArticles.Row = dsPret.Tables("PRET_DETAILS").Rows.Count - 1 Then
                    gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = False
                    gArticles.Splits(0).DisplayColumns("Designation").Locked = False
                End If
            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "gArticles_MouseClick", ex.Message, "0000118", "Erreur  d'exécution  de gArticles_MouseClick", True, True, True)
        End Try
    End Sub

    Private Sub gListeRecherche_UnboundColumnFetch(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gListeRecherche.UnboundColumnFetch
        Dim y As String
        Dim CategorieArticle As Integer = 0
        Dim PreparationArticle As Integer = 0
        Dim CodeArticleMereFractionnement As Integer = 0
        Dim QteUnitaireArticleMere As Integer = 0
        Dim StockArticleMere As Integer = 0

        Try


            y = gListeRecherche(e.Row, ("CodeArticle"))

            CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", y)
            If CategorieArticle = 9 Then
                If RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", y) <> "" Then
                    PreparationArticle = RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", y)
                End If
            End If

            If CategorieArticle = 9 And PreparationArticle = 4 Then
                Try
                    CodeArticleMereFractionnement = RecupererValeurExecuteScalaire("CodeArticleMere", "FRACTIONNEMENT", "CodeFractionnement", y)
                    QteUnitaireArticleMere = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticleMereFractionnement)
                    StockArticleMere = CalculeStock(CodeArticleMereFractionnement) * QteUnitaireArticleMere
                Catch ex As Exception
                End Try
            End If

            If CategorieArticle = 9 And PreparationArticle = 4 Then
                e.Value = CalculeStock(y) + StockArticleMere
            Else
                e.Value = CalculeStock(y)
            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "gListeRecherche_UnboundColumnFetch", ex.Message, "0000119", "Erreur  d'exécution  de gListeRecherche_UnboundColumnFetch", True, True, True)
        End Try
    End Sub

    Private Sub ChargerPret(ByVal pNumeroLignePret As String)
        Try
            Try
                '----------Vider la DS PRET_DETAILS
                dsPret.Tables("PRET_DETAILS").Clear()
                '----------Vider la DS PRET
                dsPret.Tables("PRET").Clear()
            Catch ex As Exception
            End Try
            '-----------------------------chargement des Entêtes des prets
            Try
                StrSQL = " SELECT * FROM (  " + _
                " SELECT *, ROW_NUMBER() OVER (ORDER BY NumeroPret) as row FROM PRET " + _
                "              ) a WHERE row > " & pNumeroLignePret - 1 & " AND  row <= " & pNumeroLignePret

                cmdPretEntete.Connection = ConnectionServeur
                cmdPretEntete.CommandText = StrSQL
                daPretEntete = New SqlDataAdapter(cmdPretEntete)
                daPretEntete.Fill(dsPret, "PRET")
                cbPretEntete = New SqlCommandBuilder(daPretEntete)
            Catch ex As Exception
                'Gérer l'Exception
                fMessageException.Show("Pret", "fPret", "ChargerPret", ex.Message, "0000120", "Erreur lors d'executer la requette", True, True, True)
            End Try
            'Lire le numéro Pret
            If dsPret.Tables("PRET").Rows.Count > 0 Then
                NumeroPret = dsPret.Tables("PRET").Rows(0).Item("NumeroPret")
            Else
                NumeroPret = "0"
            End If
            'Pour initialiser les btns suivant le mode et l'etat du table dans la BD
            initBoutons()

            'chargement des détails des prets
            '**************************************************************
            Try
                StrSQL = "SELECT NumeroPret," + _
                         "CodeArticle," + _
                         "CodeABarre," + _
                         "Designation," + _
                         "PRET_DETAILS.CodeForme," + _
                         "'' AS LibelleForme," + _
                         "Qte," + _
                         "NumeroLotArticle," + _
                         "PrixAchatHT," + _
                         "TotalAchatHT," + _
                         "PrixAchatTTC," + _
                         "TotalAchatTTC," + _
                         "TVA," + _
                         "TotalTVA," + _
                         "Stock," + _
                         "DatePeremption, " + _
                         "(SELECT CASE WHEN QuantiteUnitaire = 0 then 1 else QuantiteUnitaire end From ARTICLE WHERE ARTICLE.CodeArticle = PRET_DETAILS.CodeArticle) as QuantiteUnitaire ," + _
                         "'' AS Vide " + _
                         "FROM " + _
                         "PRET_DETAILS " + _
                         "WHERE  NumeroPret =" + Quote(NumeroPret) + ""

                cmdPretDetail.Connection = ConnectionServeur
                cmdPretDetail.CommandText = StrSQL
                daPretDetails = New SqlDataAdapter(cmdPretDetail)
                daPretDetails.Fill(dsPret, "PRET_DETAILS")
                cbPretDetails = New SqlCommandBuilder(daPretDetails)

            Catch ex As Exception

                'Gérer l'Exception
                fMessageException.Show("Pret", "fPret", "ChargerPret", ex.Message, "0000121", "Erreur lors d'executer la requette", True, True, True)
            End Try

            initgArticles()

            '-----chargement des pharmacies

            initPharmacies()

            ' Affichage ds informations de pret

            'Si le  mode est consultation

            If mode = "Modif" Or mode = "Consultation" Then

                If dsPret.Tables("PRET").Rows.Count > 0 Then

                    DataRowRecherche = dsPret.Tables("PRET").Select("NumeroPret=" + Quote(NumeroPret))(0)

                    'chargement des informations entête
                    lNumeroPret.Text = dsPret.Tables("PRET").Rows(dsPret.Tables("PRET").Rows.Count - 1)("NumeroPret")
                    lDatePret.Text = dsPret.Tables("PRET").Rows(dsPret.Tables("PRET").Rows.Count - 1)("Date")
                    cmbPharmacie.SelectedValue = dsPret.Tables("PRET").Rows(dsPret.Tables("PRET").Rows.Count - 1)("CodePharmacie")

                    lTotalHT.Text = dsPret.Tables("PRET").Rows(dsPret.Tables("PRET").Rows.Count - 1)("TotalHT")
                    lTotalTTC.Text = Math.Round(dsPret.Tables("PRET").Rows(dsPret.Tables("PRET").Rows.Count - 1)("TotalTTC"), 3)
                    lTotalTVA.Text = dsPret.Tables("PRET").Rows(dsPret.Tables("PRET").Rows.Count - 1)("TotalTVA")
                    lOperateur.Text = RecupererValeurExecuteScalaire("NomOperateur", "OPERATEUR", "CodeOperateur", dsPret.Tables("PRET").Rows(dsPret.Tables("PRET").Rows.Count - 1)("CodePersonnel"))
                    NumeroPret = DataRowRecherche.Item("NumeroPret")
                End If
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "ChargerPret", ex.Message, "0000122", "Erreur  d'exécution  de ChargerPret", True, True, True)
        End Try
    End Sub
    '------------------------------ initialisation des differents zones de textes 
    '------------------------------ initialisation des variables globaux 
    Private Sub initControlAjout()
        Try
            '---------------------------------Debloquer le saisie
            Dim I As Integer

            With gArticles

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Locked = True
                Next

                .Splits(0).DisplayColumns("CodeABarre").Locked = False
                .Splits(0).DisplayColumns("Designation").Locked = False
                .Splits(0).DisplayColumns("CodeForme").Locked = False
                .Splits(0).DisplayColumns("Qte").Locked = False
                .Splits(0).DisplayColumns("DatePeremption").Locked = False
                .Splits(0).DisplayColumns("NumeroLotArticle").Locked = False
                .Splits(0).DisplayColumns("PrixAchatHT").Locked = False
                .Splits(0).DisplayColumns("TotalAchatHT").Locked = False
                .Splits(0).DisplayColumns("PrixAchatTTC").Locked = False
                .Splits(0).DisplayColumns("TotalAchatTTC").Locked = False
                .Splits(0).DisplayColumns("TVA").Locked = False
                .Splits(0).DisplayColumns("TotalTVA").Locked = False
                .Splits(0).DisplayColumns("Stock").Locked = False



            End With

            '------------------------------ initialisation des differents zones de textes 
            '------------------------------ initialisation des variables globaux 

            TotalHTAchat = 0.0
            TVA = 0.0
            Timbre = 0.3

            lTotalHT.Text = "0.000"
            lTotalTTC.Text = "0.000"
            lTotalTVA.Text = "0.000"

            lOperateur.Text = "-"

            lDatePret.Text = System.DateTime.Now
            cmbPharmacie.Text = ""
            lNumeroPret.Text = "-------------"

            bAnnuler.Enabled = True
            bConfirmer.Enabled = True

            bSupprimer.Enabled = True

            bFirst.Visible = False
            bPrevious.Visible = False
            bNext.Visible = False
            bLast.Visible = False
            bAjouter.Enabled = False

            GroupeNature.Enabled = True

            cmbPharmacie.Focus()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "initControlAjout", ex.Message, "0000123", "Erreur  d'exécution  de initControlAjout", True, True, True)
        End Try
    End Sub

    'initialisation de la grid Articles
    Sub initgArticles()

        Try

            Dim i As Integer
            With gArticles
                .Columns.Clear()
                .DataSource = dsPret
                .DataMember = "PRET_DETAILS"
                .Rebind(False)
                .Columns("CodeABarre").Caption = "Code article"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Qte").Caption = "Qte"
                .Columns("NumeroLotArticle").Caption = "Numero Lot"
                .Columns("PrixAchatHT").Caption = "Prix A HT "
                .Columns("PrixAchatTTC").Caption = "Prix A TTC "
                .Columns("TotalAchatHT").Caption = "Total A HT"
                .Columns("TotalAchatTTC").Caption = "Total A TTC"
                .Columns("TVA").Caption = "TVA"
                .Columns("TotalTVA").Caption = "Total TVA"
                .Columns("DatePeremption").Caption = "Date péremption"
                .Columns("Stock").Caption = "Stock"
                'colonne vide
                .Columns("Vide").Caption = ""

                ' Colonne non liée : LibelleForme
                .Columns("LibelleForme").DataField = ""

                ' Centrer toutes les entêtes
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Locked = True
                Next
                '-------------------------------------------------------------------

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("PrixAchatTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalAchatTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalTVA").Style.HorizontalAlignment = AlignHorzEnum.Far

                .Splits(0).DisplayColumns("NumeroPret").Width = 0
                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

                .Splits(0).DisplayColumns("CodeABarre").Width = 120 '60
                .Splits(0).DisplayColumns("NumeroLotArticle").Width = 60
                .Splits(0).DisplayColumns("Designation").Width = 350 '260
                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("CodeForme").Width = 0
                '.Splits(0).DisplayColumns("LibelleForme").Width = 100
                .Splits(0).DisplayColumns("Qte").Width = 50
                .Splits(0).DisplayColumns("PrixAchatHT").Width = 100
                .Splits(0).DisplayColumns("TotalAchatHT").Width = 100
                .Splits(0).DisplayColumns("PrixAchatTTC").Width = 100
                .Splits(0).DisplayColumns("TotalAchatTTC").Width = 100
                .Splits(0).DisplayColumns("TVA").Width = 70
                .Splits(0).DisplayColumns("TotalTVA").Width = 90
                .Splits(0).DisplayColumns("DatePeremption").Width = 110 '100
                .Splits(0).DisplayColumns("Stock").Width = 50

                .Splits(0).DisplayColumns("NumeroPret").Visible = False
                .Splits(0).DisplayColumns("CodeForme").Visible = False

                'colonne vide ajouter
                .Splits(0).DisplayColumns("Vide").Width = 50
                .Splits(0).DisplayColumns("Vide").Visible = False

                .Splits(0).DisplayColumns("NumeroLotArticle").Visible = False


                .Splits(0).DisplayColumns("QuantiteUnitaire").Visible = False

                If mode = "Ajout" Then
                    .Splits(0).DisplayColumns("DatePeremption").Visible = False
                    ''''.Splits(0).DisplayColumns("NumeroLotArticle").Visible = False
                End If

                If mode = "Consultation" Then
                    .Splits(0).DisplayColumns("DatePeremption").Visible = True
                    ''''.Splits(0).DisplayColumns("NumeroLotArticle").Visible = True
                End If

                'Couleur de la Grid
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Style.BackColor = Color.FromArgb(250, 250, 200)
                Next
                '--------------------------------------------------------------------------
                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(210, 240, 255)
                .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(210, 240, 255)
                .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(210, 255, 230)
                .Splits(0).DisplayColumns("Vide").Style.BackColor = Color.FromArgb(250, 250, 200)

                '-------------------------------------------------------------------------

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                .AllowSort = False
                .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
                'Style du Caractere et du grid
                ParametreGrid(gArticles)
            End With

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "initgArticles", ex.Message, "0000124", "Erreur  d'exécution  de initgArticles", True, True, True)
        End Try
    End Sub

    '------------------------------------les procédure de navigation avec NumerolignePret---------------------------------------------------------

    Private Function selectionDernierLignePret()

        Dim StrSQL As String

        Try

            'Affécter le nombre de ligne au variable global  NumerolignePret
            StrSQL = " SELECT COUNT(*) FROM PRET "

            cmdPret.Connection = ConnectionServeur
            cmdPret.CommandText = StrSQL

            selectionDernierLignePret = cmdPret.ExecuteScalar()

            Return selectionDernierLignePret

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "selectionDernierLignePret", ex.Message, "0000135", "Erreur  d'exécution  de selectionDernierLignePret", True, True, True)
            Return 0
        End Try


    End Function


    Private Sub selectionPremierLignePret()

        'Affécter le numéro 1 au variable global  NumerolignePret
        NumerolignePret = 1

    End Sub


    Private Sub selectionLignePretPrecedent()

        'décrémenter le numéro 1 au variable global  NumerolignePert
        NumerolignePret = NumerolignePret - 1

    End Sub

    Private Sub selectionLignePretSuivante()

        ' incrémenter le numéro 1 au variable global NumerolignePret 
        NumerolignePret = NumerolignePret + 1
    End Sub

    '-----chargement des pharmacies
    Private Sub initPharmacies()
        Try
            Try
                dsPret.Tables("PHARMACIE").Clear()
            Catch ex As Exception
            End Try

            StrSQL = "SELECT CodePharmacie,Nom FROM PHARMACIE ORDER BY Nom ASC"
            cmdPret.Connection = ConnectionServeur
            cmdPret.CommandText = StrSQL
            daPret = New SqlDataAdapter(cmdPret)
            daPret.Fill(dsPret, "PHARMACIE")
            cmbPharmacie.DataSource = dsPret.Tables("PHARMACIE")
            cmbPharmacie.ValueMember = "CodePharmacie"
            cmbPharmacie.DisplayMember = "Nom"
            cmbPharmacie.ColumnHeaders = False
            cmbPharmacie.Splits(0).DisplayColumns("CodePharmacie").Visible = False
            cmbPharmacie.Splits(0).DisplayColumns("Nom").Width = 10
            cmbPharmacie.ExtendRightColumn = True

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "initPharmacies", ex.Message, "0000136", "Erreur  d'exécution  de initPharmacies", True, True, True)
        End Try

    End Sub

    Private Sub initLoadControl()
        Try
            'mode en consultation
            mode = "Consultation"

            'Initialiser les controles
            lTotalHT.Text = "0.000"
            lTotalTTC.Text = "0.000"
            lTotalTVA.Text = "0.000"

            bConfirmer.Enabled = False
            bAnnuler.Enabled = False
            bAjouter.Enabled = True
            bFirst.Enabled = True
            bPrevious.Enabled = True
            bSupprimer.Enabled = True

            GroupeNature.Enabled = False

            lNumeroPret.Text = "-------------"
            lDatePret.Text = "Date"
            lOperateur.Text = "-"
            cmbPharmacie.SelectedValue = -1
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "initLoadControl", ex.Message, "0000137", "Erreur  d'exécution  de initLoadControl", True, True, True)
        End Try
    End Sub
    Private Sub initPretDetails()
        Try

            StrSQL = "SELECT NumeroPret," + _
                        "CodeArticle," + _
                        "CodeABarre," + _
                        "Designation," + _
                        "PRET_DETAILS.CodeForme," + _
                          "'' AS LibelleForme," + _
                        "Qte," + _
                           "DatePeremption, " + _
                        "NumeroLotArticle," + _
                        "PrixAchatHT," + _
                        "TotalAchatHT," + _
                        "PrixAchatTTC," + _
                        "TotalAchatTTC," + _
                        "TVA," + _
                        "TotalTVA," + _
                        "Stock, " + _
                        "1 as QuantiteUnitaire, " + _
                         "'' AS Vide " + _
                        "FROM " + _
                        "PRET_DETAILS " + _
                        "WHERE  NumeroPret =" + Quote(NumeroPret) + ""

            cmdChargement.Connection = ConnectionServeur
            cmdChargement.CommandText = StrSQL
            daChargement = New SqlDataAdapter(cmdChargement)
            daChargement.Fill(dsPret, "PRET_DETAILS")
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "initPretDetails", ex.Message, "0000138", "Erreur  d'exécution  de initPretDetails", True, True, True)
        End Try
    End Sub

    '--------------------initialisation de la datatable article 
    '--------------------recherche alimenté selon les entrés de l'utilisateur dans la colonne designation

    Private Sub initArticle()
        Dim I As Integer
        Try


            StrSQL = "SELECT CodeArticle," + _
                  "Designation," + _
                  "LibelleForme," + _
                  "PrixVenteTTC" + _
                  " FROM ARTICLE,FORME_ARTICLE " + _
                  "WHERE ARTICLE.CodeForme=FORME_ARTICLE.CodeForme and " + _
                  "Designation LIKE " + Quote(gArticles.Columns("Designation").Value) + " ORDER BY Designation"

            cmdPret.Connection = ConnectionServeur
            cmdPret.CommandText = StrSQL
            daPret = New SqlDataAdapter(cmdPret)
            daPret.Fill(dsPret, "ARTICLE")

            With gListeRecherche
                .Columns.Clear()
                .DataSource = dsPret
                .DataMember = "ARTICLE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code Article"
                .Columns("Designation").Caption = "Designation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("PrixVenteTTC").Caption = "Prix de vente"

                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centre tous les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Visible = False
                Next

                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
                .Splits(0).DisplayColumns("Designation").Visible = True
                .Splits(0).DisplayColumns("LibelleForme").Visible = True
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("Designation").Width = 300
                .Splits(0).DisplayColumns("LibelleForme").Width = 100
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 20
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True

                'Style du Caractere et du grid
                ParametreGrid(gListeRecherche)

            End With

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "initArticle", ex.Message, "0000139", "Erreur  d'exécution  de initArticle", True, True, True)
        End Try
    End Sub

    Private Sub initPret()

        Try

            StrSQL = " SELECT TOP (0 )* FROM PRET "
            cmdPretEntete.Connection = ConnectionServeur
            cmdPretEntete.CommandText = StrSQL
            daPretEntete = New SqlDataAdapter(cmdPretEntete)
            daPretEntete.Fill(dsPret, "PRET")
            cbPretEntete = New SqlCommandBuilder(daPretEntete)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "initPret", ex.Message, "0000140", "Erreur  d'exécution  de initPret", True, True, True)
        End Try

    End Sub


    Private Sub initBoutons()
        Try
            'Tester si on atteint la premiere ligne
            'pour desactiver les BTN Précedent et Premier élément        
            If NumerolignePret = 1 Then

                bPrevious.Enabled = False
                bFirst.Enabled = False

            Else

                bPrevious.Enabled = True
                bFirst.Enabled = True

            End If

            'Tester si on atteint la derniere ligne
            'pour desactiver les BTN Siuvant et Dernier élément
            If NumerolignePret = selectionDernierLignePret() Then

                bNext.Enabled = False
                bLast.Enabled = False


            Else

                bNext.Enabled = True
                bLast.Enabled = True

            End If

            'Le cas ou la table est vide
            If NumerolignePret = 0 Then

                bNext.Enabled = False
                bLast.Enabled = False
                bPrevious.Enabled = False
                bFirst.Enabled = False

            End If

            'Tester si la table est vide
            'pour desactiver les BTN Siuvant et Dernier élément
            If selectionDernierLignePret() = 0 Then

                'Bloque navigation
                bNext.Enabled = False
                bLast.Enabled = False
                bNext.Enabled = False
                bLast.Enabled = False

                'Bouton de ctrl
                bAnnuler.Enabled = False
                bConfirmer.Enabled = False
                bImprimer.Enabled = False
                bModifier.Enabled = False
                bRecherche.Enabled = False
                bSupprimer.Enabled = False
                bQuitter.Enabled = True

            End If   ' le cas on a ajouté un element

            'le mode en Cosultation et on a des enregistrements
            If selectionDernierLignePret() <> 0 And mode = "Consultation" Then

                'Bouton de ctrl
                bAnnuler.Enabled = False
                bConfirmer.Enabled = False
                bImprimer.Enabled = True
                bModifier.Enabled = True
                bRecherche.Enabled = True
                bSupprimer.Enabled = True
                bAjouter.Enabled = True
                bQuitter.Enabled = True

                bFirst.Visible = True
                bPrevious.Visible = True
                bNext.Visible = True
                bLast.Visible = True

                GroupeNature.Enabled = False

                'le mode est modif/Ajout et on a des enregistrements
            ElseIf selectionDernierLignePret() <> 0 And mode <> "Consultation" Then

                bAnnuler.Enabled = True
                bConfirmer.Enabled = True
                bImprimer.Enabled = False
                bModifier.Enabled = False
                bAjouter.Enabled = False
                bRecherche.Enabled = False
                bSupprimer.Enabled = True
                bQuitter.Enabled = True

                'pour rendre visible si le mode est Modif ou Ajout
                bLast.Visible = False
                bNext.Visible = False
                bPrevious.Visible = False
                bFirst.Visible = False

                GroupeNature.Enabled = True


                'le mode en Cosultation et on  a pas des enregistrements
            ElseIf selectionDernierLignePret() = 0 And mode = "Consultation" Then

                bAjouter.Enabled = True
                bAnnuler.Enabled = False
                bConfirmer.Enabled = False
                bQuitter.Enabled = True

                'pour rendre invisible si le mode est Consultation
                bFirst.Visible = True
                bPrevious.Visible = True
                bNext.Visible = True
                bLast.Visible = True

                GroupeNature.Enabled = False
                cmbPharmacie.SelectedValue = -1


            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "initBoutons", ex.Message, "0000125", "Erreur  d'exécution  de initBoutons", True, True, True)
        End Try
    End Sub


    Private Sub bModifier_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifier.Click

        Try

       
        'Suivi du scénario 
        fMessageException.Show("Pret", "fPret", "bModifier_Click", "NoException", "NoError", "Clic sur le bouton Modifier", False, True, False)

        mode = "Modif"

        Dim I As Integer
        NouvelArticle = dsPret.Tables("PRET_DETAILS").NewRow()
        NouvelArticle("Designation") = ""
        NouvelArticle("CodeArticle") = ""
        dsPret.Tables("PRET_DETAILS").Rows.Add(NouvelArticle)

        For I = 0 To dsPret.Tables("PRET_DETAILS").Columns.Count - 1
            Me.gArticles.Splits(0).DisplayColumns(I).AllowFocus = False
        Next


        With gArticles

            '.Splits(0).DisplayColumns("CodeArticle").Locked = False
            .Splits(0).DisplayColumns("Designation").Locked = False
            .Splits(0).DisplayColumns("Qte").Locked = False
            .Splits(0).DisplayColumns("DatePeremption").Locked = False
            .Splits(0).DisplayColumns("NumeroLotArticle").Locked = False
            .Splits(0).DisplayColumns("CodeABarre").Locked = False


            '.Splits(0).DisplayColumns("CodeArticle").AllowFocus = True
            .Splits(0).DisplayColumns("Designation").AllowFocus = True
            .Splits(0).DisplayColumns("Qte").AllowFocus = True
            .Splits(0).DisplayColumns("DatePeremption").AllowFocus = True
            .Splits(0).DisplayColumns("NumeroLotArticle").AllowFocus = True
                .Splits(0).DisplayColumns("CodeABarre").AllowFocus = True


                If mode = "Modif" Then
                    .Splits(0).DisplayColumns("DatePeremption").Visible = False
                    .Splits(0).DisplayColumns("NumeroLotArticle").Visible = False
                End If

        End With

        'Pour initialiser les btns suivant le mode et l'etat du table dans la BD
        initBoutons()

        'Pour initialiser le Focus a cmb Pharmacie
            cmbPharmacie.Focus()

            '#########################################################################################################

            '------------------------------------------------------------------------------------------------------------------------------------------------
            Dim k As Integer = 0
            Dim j As Integer = 0
            Dim L As Integer = 0
            Dim H As Integer = 0
            Dim M As Integer = 0


            Do While j < gArticles.RowCount

                For k = j To dsPret.Tables("PRET_DETAILS").Rows.Count - 1

                    If gArticles(j, "CodeArticle") = gArticles(k, "CodeArticle") And j <> k Then
                        gArticles(j, "Qte") = (Convert.ToInt32(gArticles(j, "Qte")) + (Convert.ToInt32(gArticles(k, "Qte")))).ToString

                        gArticles(k, "CodeArticle") = ""
                        gArticles(k, "CodeABarre") = ""
                        gArticles(k, "Designation") = ""
                        gArticles(k, "Forme") = ""
                        gArticles(k, "Qte") = ""
                        gArticles(k, "DatePeremption") = ""
                        gArticles(k, "NumeroLotArticle") = ""
                        gArticles(k, "PrixAchatHT") = ""
                        gArticles(k, "TotalAchatHT") = ""
                        gArticles(k, "PrixAchatTTC") = ""
                        gArticles(k, "TotalAchatTTC") = ""
                        gArticles(k, "TVA") = ""
                        gArticles(k, "TotalTVA") = ""
                        gArticles(k, "Stock") = ""

                        gArticles(k, "QuantiteUnitaire") = ""

                        gArticles(j, "DatePeremption") = ""
                        gArticles(j, "NumeroLotArticle") = ""
                    End If
                Next
                '#########################################################################################################
                I = 0
                Do While I < dsPret.Tables("PRET_DETAILS").Rows.Count

                    If dsPret.Tables("PRET_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then

                        If dsPret.Tables("PRET_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                            dsPret.Tables("PRET_DETAILS").Rows(I).Delete()

                        End If

                    End If
                    I = I + 1
                Loop

                j = j + 1
            Loop

            Do While H < gArticles.RowCount

                gArticles(H, "DatePeremption") = ""
                gArticles(H, "NumeroLotArticle") = ""

                H = H + 1
            Loop

            CalculerMontants()
            '#########################################################################################################

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "bModifier_Click", ex.Message, "0000126", "Erreur  d'exécution  de bModifier_Click", True, True, True)
        End Try
    End Sub

    Private Sub bRecherche_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRecherche.Click
        Try
            'Suivi du scénario 
            fMessageException.Show("Pret", "fPret", "bRecherche_Click", "NoException", "NoError", "Clic sur le bouton Recherche", False, True, False)

            mode = "Consultation"
            tRecherche.Visible = True
            tRecherche.Text = System.DateTime.Now.Year.ToString + "/"
            tRecherche.Focus()
            tRecherche.Select(tRecherche.Text.Length, 0)
            bAnnuler.Enabled = True

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "bRecherche_Click", ex.Message, "0000127", "Erreur  d'exécution  de bRecherche_Click", True, True, True)
        End Try
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Try
            'Suivi du scénario 
            fMessageException.Show("Pret", "fPret", "bImprimer_Click", "NoException", "NoError", "Clic sur le bouton Imprimer", False, True, False)

            If lNumeroPret.Text = "" Then
                Exit Sub
            End If

            Dim CondCrystal As String = ""
            'CondCrystal = " AND {PRET_DETAILS.NumeroPret} = '" & lNumeroPret.Text & "' "
            CondCrystal = "1=1 AND {Vue_EtatPret.NumeroPret} = '" & lNumeroPret.Text & "' "

            Dim I As Integer
            Dim num As Integer = 999
            For I = 0 To fMain.Tab.TabPages.Count - 1
                If fMain.Tab.TabPages(I).Text = "Impression Pret" Then
                    num = I
                End If
            Next
            CR.FileName = Application.StartupPath + "\EtatPret1.rpt"

            CR.SetParameterValue("pNumeroPret", lNumeroPret.Text)

            Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo

            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent

            CR.RecordSelectionFormula = CondCrystal
            Dim MyViewer As New fViewer
            MyViewer.CRViewer.ReportSource = CR
            fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
            fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
            fMain.Tab.SelectedTab.Text = "Impression Pret"
            If num <> 999 Then
                fMain.Tab.TabPages(num).Dispose()
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "bImprimer_Click", ex.Message, "0000128", "Erreur  d'exécution  de bImprimer_Click", True, True, True)
        End Try
    End Sub

    Private Sub tRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRecherche.KeyUp
        Try
            If e.KeyCode = Keys.Enter And tRecherche.Text <> "" Then

                'Recuprére le Row de l'element sellectioné

                'Lors du press Enter, on va appler la procedure recherchePret

                recherchePret(tRecherche.Text)

            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "tRecherche_KeyUp", ex.Message, "0000129", "Erreur  d'exécution  de tRecherche_KeyUp", True, True, True)
        End Try
    End Sub

    Private Sub recherchePret(ByVal pNumeroPret As String)

        Try
            '----------------------------------Traitement

            If tRecherche.Text.Length < 11 Then
                tRecherche.Text = tRecherche.Text.Substring(0, 5) + tRecherche.Text.Substring(5, tRecherche.Text.Length - 5).PadLeft(6, "0")
            End If

            'Recuperer la valeur de la row
            recupererNumRowRechrche()

            If NumerolignePret <> 0 Then
                ChargerPret(NumerolignePret)
            End If

            tRecherche.Value = ""
            tRecherche.Visible = False

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "recherchePret", ex.Message, "0000130", "Erreur  d'exécution  de recherchePret", True, True, True)
        End Try
    End Sub

    Private Sub recupererNumRowRechrche()

        Try
            '------------------------- affichage du nombre d'Pret en instance 
            StrSQL = " SELECT RowNumber " + _
                     " FROM (select ROW_NUMBER() OVER(ORDER BY NumeroPret) " + _
                     " AS 'RowNumber' , NumeroPret  from PRET) AS PRETLISTE " + _
                     " where PRETLISTE.NumeroPret =  " & Quote(tRecherche.Text)

            cmdPret.Connection = ConnectionServeur
            cmdPret.CommandText = StrSQL

            NumerolignePret = cmdPret.ExecuteScalar()

            If NumerolignePret = 0 Then
                MsgBox("Pret inéxistant", MsgBoxStyle.Exclamation, "Recherche")
                NumerolignePret = selectionDernierLignePret()
            End If

            'Tester si on atteint la premiere ligne
            'pour desactiver les BTN Précedent et Premier élément        
            If NumerolignePret = 1 Or NumerolignePret = 0 Then

                bPrevious.Enabled = False
                bFirst.Enabled = False

            Else

                bPrevious.Enabled = True
                bFirst.Enabled = True

            End If

            'Tester si on atteint la derniere ligne
            'pour desactiver les BTN Siuvant et Dernier élément
            If NumerolignePret = selectionDernierLignePret() Then

                bNext.Enabled = False
                bLast.Enabled = False

            Else

                bNext.Enabled = True
                bLast.Enabled = True

            End If


        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "recupererNumRowRechrche", ex.Message, "0000131", "Erreur  d'exécution  de recupererNumRowRechrche", True, True, True)

        End Try

    End Sub

    Private Sub tRecherche_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tRecherche.LostFocus
        tRecherche.Visible = False
    End Sub

    Private Sub gArticles_UnboundColumnFetch(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gArticles.UnboundColumnFetch
        'Récuperer la valeur Désignation FORME ARTICLE 
        Try

            StrSQL = " SELECT LibelleForme FROM FORME_ARTICLE AS F JOIN ARTICLE AS A ON  " + _
                     " F.CodeForme = A.CodeForme" + _
                     " WHERE CodeArticle = " + Quote(gArticles(e.Row, "CodeArticle"))

            cmdPret.Connection = ConnectionServeur
            cmdPret.CommandText = StrSQL

            e.Value = cmdPret.ExecuteScalar()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "gArticles_UnboundColumnFetch", ex.Message, "0000132", "Erreur  d'exécution  de gArticles_UnboundColumnFetch", True, True, True)

        End Try
    End Sub

    ''pour rendre invisible la grid glisteRecherche

    'Private Sub gArticles_Leave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gArticles.Leave
    '    Try
    '        gListeRecherche.Visible = False

    '    Catch ex As Exception

    '        'Gérer l'Exception
    '        fMessageException.Show("Pret", "fPret", "gArticles_Leave", ex.Message, "0000133", "Erreur  d'exécution  de gArticles_Leave", True, True, True)

    '    End Try
    'End Sub

    Private Sub gArticles_AfterColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gArticles.AfterColUpdate
        Try
            If e.ColIndex = gArticles.Columns.IndexOf(gArticles.Columns("Qte")) Then
                If gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999 Then
                    MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Columns("Qte").Value = "1"
                End If
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Pret", "fPret", "gArticles_AfterColUpdate", ex.Message, "0000134", "Erreur  d'exécution  de gArticles_AfterColUpdate", True, True, True)
        End Try
    End Sub

    Private Sub gArticles_BeforeColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColUpdateEventArgs) Handles gArticles.BeforeColUpdate

        If e.ColIndex = gArticles.Columns.IndexOf(gArticles.Columns("Qte")) Then

            If gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999 Then

                MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")

                gArticles.Columns("Qte").Value = e.OldValue

            End If

        End If

    End Sub

    Private Sub gArticles_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyDown
        If e.KeyCode = Keys.F1 And gArticles.Columns("CodeArticle").Value.ToString <> "" Then
            Dim stock As Integer
            stock = CalculeStock(gArticles.Columns("CodeArticle").Value)
            AfficherFicheArticle(gArticles.Columns("CodeArticle").Value, stock, gArticles.Columns("Designation").Value)
            Exit Sub
        End If
    End Sub

    Public Sub AfficherFicheArticle(ByVal CodeArticle, ByVal StockArticle, ByVal Designation)
        Try
            Dim MyFicheArticle As New fFicheArticle
            MyFicheArticle.CodeArticle = CodeArticle
            MyFicheArticle.StockArticle = StockArticle
            MyFicheArticle.DesignationArticle = Designation
            MyFicheArticle.ajoutmodif = "M"
            MyFicheArticle.Init()
            MyFicheArticle.ShowDialog()
            MyFicheArticle.Close()
            MyFicheArticle.Dispose()
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Entree", "fEntree", "AfficherFicheArticle", ex.Message, "0000721", "Erreur d'excution de AfficherFicheArticle", True, True, True)
            Return
        End Try
    End Sub

End Class