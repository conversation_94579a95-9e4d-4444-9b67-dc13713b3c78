﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="BusinessManagementModel" Alias="Self" p1:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:p1="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
  <EntityContainer Name="BusinessManagementEntities" p1:LazyLoadingEnabled="true">
    <EntitySet Name="VENTE_NUMERO" EntityType="BusinessManagementModel.VENTE_NUMERO" />
    <EntitySet Name="VENTE" EntityType="BusinessManagementModel.VENTE" />
    <EntitySet Name="VENTE_DETAILS" EntityType="BusinessManagementModel.VENTE_DETAILS" />
    <AssociationSet Name="VENTEVENTE_DETAILS" Association="BusinessManagementModel.VENTEVENTE_DETAILS">
      <End Role="VENTE" EntitySet="VENTE" />
      <End Role="VENTE_DETAILS" EntitySet="VENTE_DETAILS" />
    </AssociationSet>
    <EntitySet Name="COMMANDE" EntityType="BusinessManagementModel.COMMANDE" />
    <EntitySet Name="COMMANDE_DETAILS" EntityType="BusinessManagementModel.COMMANDE_DETAILS" />
    <AssociationSet Name="COMMANDECOMMANDE_DETAILS" Association="BusinessManagementModel.COMMANDECOMMANDE_DETAILS">
      <End Role="COMMANDE" EntitySet="COMMANDE" />
      <End Role="COMMANDE_DETAILS" EntitySet="COMMANDE_DETAILS" />
    </AssociationSet>
    <EntitySet Name="SIMULATION_STOCK" EntityType="BusinessManagementModel.SIMULATION_STOCK" />
    <EntitySet Name="SIMULATION_STOCK_DETAILS" EntityType="BusinessManagementModel.SIMULATION_STOCK_DETAILS" />
    <AssociationSet Name="SIMULATION_STOCKSIMULATION_STOCK_DETAILS" Association="BusinessManagementModel.SIMULATION_STOCKSIMULATION_STOCK_DETAILS">
      <End Role="SIMULATION_STOCK" EntitySet="SIMULATION_STOCK" />
      <End Role="SIMULATION_STOCK_DETAILS" EntitySet="SIMULATION_STOCK_DETAILS" />
    </AssociationSet>
    <EntitySet Name="ACHAT" EntityType="BusinessManagementModel.ACHAT" />
    <EntitySet Name="ACHAT_DETAILS" EntityType="BusinessManagementModel.ACHAT_DETAILS" />
    <AssociationSet Name="ACHATACHAT_DETAILS" Association="BusinessManagementModel.ACHATACHAT_DETAILS">
      <End Role="ACHAT" EntitySet="ACHAT" />
      <End Role="ACHAT_DETAILS" EntitySet="ACHAT_DETAILS" />
    </AssociationSet>
  </EntityContainer>
  <EntityType Name="VENTE_NUMERO">
    <Key>
      <PropertyRef Name="NumeroVente" />
    </Key>
    <Property Type="String" Name="NumeroVente" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
  </EntityType>
  <EntityType Name="VENTE">
    <Key>
      <PropertyRef Name="NumeroVente" />
    </Key>
    <Property Type="String" Name="NumeroVente" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="Date" Nullable="false" Precision="3" />
    <Property Type="Decimal" Name="TotalHT" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalRemise" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Timbre" Nullable="false" Precision="18" Scale="3" />
    <Property Type="String" Name="CodeClient" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodePersonnel" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="CodeAPCI" />
    <Property Type="Int32" Name="CodeDeFamille" />
    <Property Type="String" Name="CodeMedecinFamille" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeMedecinPrescripteur" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="LibellePoste" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Decimal" Name="Recu" Nullable="false" Precision="18" Scale="3" />
    <Property Type="DateTime" Name="DateOrdonnance" Precision="0" />
    <Property Type="Decimal" Name="MontantCnam" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantMutuelle" Nullable="false" Precision="18" Scale="3" />
    <Property Type="String" Name="Note" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="CodeNatureReglement" />
    <Property Type="String" Name="CodeMutuelle" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NomMalade" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="Rang" />
    <Property Type="DateTime" Name="DateNaissance" Precision="0" />
    <Property Type="Int32" Name="CodeLienDeParente" />
    <Property Type="String" Name="LibelleLienDeParente" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
    <Property Type="Boolean" Name="TiersPayant" Nullable="false" />
    <Property Type="Boolean" Name="PriseEnCharge" Nullable="false" />
    <Property Type="Boolean" Name="Appareillage" Nullable="false" />
    <Property Type="String" Name="IdentifiantCNAMMedecin" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Libelle1" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Libelle2" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Libelle3" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Libelle4" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Libelle5" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NumeroFacture" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Boolean" Name="Totaliseur" />
    <Property Type="Boolean" Name="Vider" Nullable="false" />
    <Property Type="String" Name="NumeroPriseEnCharge" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NumeroBonAchat" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="DureeTraitement" Nullable="false" />
    <Property Type="Boolean" Name="OMF" />
    <Property Type="Boolean" Name="APCI" />
    <Property Type="String" Name="CodeAppareillage" MaxLength="10" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NomInscritSurLeCheque" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NumeroCheque" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="DateEcheance" Precision="0" />
    <Property Type="Int32" Name="IDFacturationClient" />
    <NavigationProperty Name="VENTE_DETAILS" Relationship="BusinessManagementModel.VENTEVENTE_DETAILS" FromRole="VENTE" ToRole="VENTE_DETAILS" />
  </EntityType>
  <EntityType Name="VENTE_DETAILS">
    <Key>
      <PropertyRef Name="Id" />
      <PropertyRef Name="NumeroVente" />
      <PropertyRef Name="CodeArticle" />
      <PropertyRef Name="NumeroLotArticle" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Type="String" Name="NumeroVente" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NumeroLotArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="Ordre" Nullable="false" />
    <Property Type="String" Name="CodeABarre" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Designation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="CodeForme" Nullable="false" />
    <Property Type="Int32" Name="Qte" Nullable="false" />
    <Property Type="Decimal" Name="PrixAchat" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="PrixHT" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalHT" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="PrixTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Remise" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTVA" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Honoraire" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Int32" Name="Stock" Nullable="false" />
    <Property Type="DateTime" Name="DateDePeremption" Precision="0" />
    <Property Type="Boolean" Name="PriseEnCharge" />
    <Property Type="Boolean" Name="AccordPrealable" />
    <Property Type="Decimal" Name="TarifDeReference" Precision="18" Scale="3" />
    <Property Type="Int32" Name="DureeTraitement" Nullable="false" />
    <Property Type="Decimal" Name="MontantMutuelle" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantCNAM" Precision="18" Scale="3" />
  </EntityType>
  <Association Name="VENTEVENTE_DETAILS">
    <End Type="BusinessManagementModel.VENTE" Role="VENTE" Multiplicity="1" />
    <End Type="BusinessManagementModel.VENTE_DETAILS" Role="VENTE_DETAILS" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="VENTE">
        <PropertyRef Name="NumeroVente" />
      </Principal>
      <Dependent Role="VENTE_DETAILS">
        <PropertyRef Name="NumeroVente" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <EntityType Name="COMMANDE">
    <Key>
      <PropertyRef Name="NumeroCommande" />
    </Key>
    <Property Type="String" Name="NumeroCommande" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="Date" Nullable="false" Precision="3" />
    <Property Type="Decimal" Name="TotalHT" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTVA" Nullable="false" Precision="18" Scale="3" />
    <Property Type="String" Name="LibellePoste" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodePersonnel" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeFournisseur" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Note" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NumeroFacture" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="TypeCommande" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <NavigationProperty Name="COMMANDE_DETAILS" Relationship="BusinessManagementModel.COMMANDECOMMANDE_DETAILS" FromRole="COMMANDE" ToRole="COMMANDE_DETAILS" />
  </EntityType>
  <EntityType Name="COMMANDE_DETAILS">
    <Key>
      <PropertyRef Name="NumeroCommande" />
      <PropertyRef Name="CodeArticle" />
    </Key>
    <Property Type="String" Name="NumeroCommande" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeABarre" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Designation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="CodeForme" Nullable="false" />
    <Property Type="Int32" Name="Qte" Nullable="false" />
    <Property Type="Int32" Name="Stock" Nullable="false" />
    <Property Type="DateTime" Name="DatePeremption" Precision="3" />
    <Property Type="Decimal" Name="PrixAchatHT" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTCAchat" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="StockAlerte" Nullable="false" Precision="6" Scale="0" />
    <Property Type="Decimal" Name="EnCours" Nullable="false" Precision="6" Scale="0" />
    <Property Type="Decimal" Name="QteACommander" Nullable="false" Precision="6" Scale="0" />
    <Property Type="Decimal" Name="QteUnitaire" Nullable="false" Precision="6" Scale="0" />
    <Property Type="Int32" Name="Ordre" />
  </EntityType>
  <Association Name="COMMANDECOMMANDE_DETAILS">
    <End Type="BusinessManagementModel.COMMANDE" Role="COMMANDE" Multiplicity="1" />
    <End Type="BusinessManagementModel.COMMANDE_DETAILS" Role="COMMANDE_DETAILS" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="COMMANDE">
        <PropertyRef Name="NumeroCommande" />
      </Principal>
      <Dependent Role="COMMANDE_DETAILS">
        <PropertyRef Name="NumeroCommande" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <EntityType Name="SIMULATION_STOCK">
    <Key>
      <PropertyRef Name="NumeroSimulation" />
    </Key>
    <Property Type="String" Name="NumeroSimulation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="Date" Nullable="false" Precision="3" />
    <Property Type="DateTime" Name="DateImpression" Nullable="false" Precision="0" />
    <Property Type="Decimal" Name="TotalAHT" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalATTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalVTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Int32" Name="CodeCategorie" />
    <Property Type="Int32" Name="CodePersonnel" Nullable="false" />
    <NavigationProperty Name="SIMULATION_STOCK_DETAILS" Relationship="BusinessManagementModel.SIMULATION_STOCKSIMULATION_STOCK_DETAILS" FromRole="SIMULATION_STOCK" ToRole="SIMULATION_STOCK_DETAILS" />
  </EntityType>
  <EntityType Name="SIMULATION_STOCK_DETAILS">
    <Key>
      <PropertyRef Name="NumeroSimulation" />
      <PropertyRef Name="CodeArticle" />
    </Key>
    <Property Type="String" Name="NumeroSimulation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Designation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="CodeForme" Nullable="false" />
    <Property Type="Int32" Name="Qte" Nullable="false" />
    <Property Type="Decimal" Name="PrixAchatHT" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="PrixAchatTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="PrixVenteTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalAchatHT" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalAchatTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalVenteTTC" Nullable="false" Precision="18" Scale="3" />
  </EntityType>
  <Association Name="SIMULATION_STOCKSIMULATION_STOCK_DETAILS">
    <End Type="BusinessManagementModel.SIMULATION_STOCK" Role="SIMULATION_STOCK" Multiplicity="1" />
    <End Type="BusinessManagementModel.SIMULATION_STOCK_DETAILS" Role="SIMULATION_STOCK_DETAILS" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="SIMULATION_STOCK">
        <PropertyRef Name="NumeroSimulation" />
      </Principal>
      <Dependent Role="SIMULATION_STOCK_DETAILS">
        <PropertyRef Name="NumeroSimulation" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <EntityType Name="ACHAT">
    <Key>
      <PropertyRef Name="NumeroAchat" />
    </Key>
    <Property Type="String" Name="NumeroAchat" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="Date" Nullable="false" Precision="3" />
    <Property Type="Decimal" Name="TotalHT" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalRemise" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Timbre" Nullable="false" Precision="18" Scale="3" />
    <Property Type="String" Name="CodeFournisseur" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodePersonnel" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Note" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NumeroBL_Facture" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="LibellePoste" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="DateBlFacture" Nullable="false" Precision="0" />
    <Property Type="Decimal" Name="ValeurVenteTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Autre" Nullable="false" Precision="18" Scale="3" />
    <NavigationProperty Name="ACHAT_DETAILS" Relationship="BusinessManagementModel.ACHATACHAT_DETAILS" FromRole="ACHAT" ToRole="ACHAT_DETAILS" />
  </EntityType>
  <EntityType Name="ACHAT_DETAILS">
    <Key>
      <PropertyRef Name="NumeroAchat" />
      <PropertyRef Name="CodeArticle" />
      <PropertyRef Name="NumeroLotArticle" />
    </Key>
    <Property Type="String" Name="NumeroAchat" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NumeroLotArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int64" Name="Ordre" Nullable="false" />
    <Property Type="String" Name="CodeABarre" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Designation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="CodeForme" Nullable="false" />
    <Property Type="Int32" Name="Qte" Nullable="false" />
    <Property Type="Int32" Name="Stock" Nullable="false" />
    <Property Type="Decimal" Name="PrixAchatHT" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalAchatHT" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="PrixVenteTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Remise" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA" Nullable="false" Precision="18" Scale="3" />
    <Property Type="DateTime" Name="DatePeremption" Precision="0" />
    <Property Type="Int32" Name="QuantiteUnitaire" Nullable="false" />
    <Property Type="String" Name="CodePCT" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Type="Int32" Name="QteGratuite" />
  </EntityType>
  <Association Name="ACHATACHAT_DETAILS">
    <End Type="BusinessManagementModel.ACHAT" Role="ACHAT" Multiplicity="1" />
    <End Type="BusinessManagementModel.ACHAT_DETAILS" Role="ACHAT_DETAILS" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="ACHAT">
        <PropertyRef Name="NumeroAchat" />
      </Principal>
      <Dependent Role="ACHAT_DETAILS">
        <PropertyRef Name="NumeroAchat" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
</Schema>