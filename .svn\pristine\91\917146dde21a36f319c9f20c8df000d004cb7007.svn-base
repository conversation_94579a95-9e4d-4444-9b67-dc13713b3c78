﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fDCI

    Dim cmdDCI As New SqlCommand
    Dim daDCI As New SqlDataAdapter
    Dim cbDCI As New SqlCommandBuilder
    Dim dsDCI As New DataSet

    Dim xDCI As Integer
    Dim ModeDCI As String
    Dim CodeDCI As String

    Dim dsRecupereNum As New DataSet
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNumt As New SqlDataAdapter

    Dim CodeExiste As Boolean = False

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub
    Public Sub afficherDCI()
        Dim I As Integer
        Dim Cond As String = "1=1"
        dsDCI.Clear()


        If tRecherche.Text <> "" Then
            Cond += " and LibelleDCI like " + Quote("%" + tRecherche.Text + "%")
        End If

        'cmdDCI.CommandText = " SELECT " + _
        '                            " CodeDCI, " + _
        '                            " LibelleDCI " + _
        '                            " FROM DCI WHERE SupprimeDCI =0 AND " + Cond + _
        '                            " ORDER BY CodeDCI"

        cmdDCI.CommandText = " SELECT * " + _
                             " FROM DCI WHERE SupprimeDCI =0 AND " + Cond + _
                             " ORDER BY CodeDCI"

        cmdDCI.Connection = ConnectionServeur
        daDCI = New SqlDataAdapter(cmdDCI)
        daDCI.Fill(dsDCI, "DCI")

        With gDCI
            .Columns.Clear()
            .DataSource = dsDCI
            .DataMember = "DCI"
            .Rebind(False)
            .Columns("CodeDCI").Caption = "Code DCI"
            .Columns("LibelleDCI").Caption = "Libelle DCI"
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("CodeDCI").Width = 120
            .Splits(0).DisplayColumns("CodeDCI").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("LibelleDCI").Width = 80
            .Splits(0).DisplayColumns("LibelleDCI").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(0).DisplayColumns("CodeDCI").Locked = True
            .Splits(0).DisplayColumns("LibelleDCI").Locked = True
            .Splits(0).DisplayColumns("SupprimeDCI").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gDCI)
        End With
        gDCI.MoveRelative(xDCI)
        cbDCI = New SqlCommandBuilder(daDCI)
    End Sub

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click
        Dim dr As DataRow
        Dim StrSQL As String = ""

        If tCodeDCi.Text = "" Then
            MsgBox("Veuillez saisir le code de la DCI !", MsgBoxStyle.Critical, "Erreur")
            tCodeDCi.Focus()
            Exit Sub
        End If
        If tLibelleDCI.Text = "" Then
            MsgBox("Veuillez saisir le libelle de la DCI !", MsgBoxStyle.Critical, "Erreur")
            tLibelleDCI.Focus()
            Exit Sub
        End If
        If CodeExiste = True Then
            MsgBox("Code DCI existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
            tCodeDCi.Focus()
            Exit Sub
        End If
        With dsDCI
            dr = .Tables("DCI").NewRow
            dr.Item("LibelleDCI") = tLibelleDCI.Text
            dr.Item("CodeDCI") = tCodeDCi.Text
            .Tables("DCI").Rows.Add(dr)
        End With
        Try
            daDCI.Update(dsDCI, "DCI")
            afficherDCI()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsDCI.Reset()
        End Try
        tCodeDCi.Text = ""
        tLibelleDCI.Text = ""
    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click
        Dim cmd As New SqlCommand
        If gDCI.RowCount > 0 Then
            If MsgBox("Voulez vous vraiment supprimer cette DCI " + Quote(gDCI(gDCI.Row, "LibelleDCI")) + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "UPDATE DCI SET SupprimeDCI=1 WHERE CodeDCI =" + Quote(gDCI(gDCI.Row, "CodeDCI"))
                    cmd.ExecuteNonQuery()
                    afficherDCI()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub

    Private Sub gDCI_AfterColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gDCI.AfterColEdit
        Dim dr As DataRow
        Dim StrSQL As String = ""

        With dsDCI.Tables("DCI_MAJ")
            dr = .Rows(0)
            dr.Item("LibelleDCI") = gDCI(gDCI.Row, "LibelleDCI")
        End With
        Try
            daDCI.Update(dsDCI, "DCI_MAJ")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            afficherDCI()
        End Try
    End Sub

    Private Sub gDCI_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gDCI.Click
        Dim StrSQL As String = ""
        CodeDCI = Quote(gDCI(gDCI.Row, "CodeDCI"))
        If CodeDCI = "" Then
            MsgBox("Veuillez sélectionner le libelle de la DCI !", MsgBoxStyle.Critical, "Erreur")
            gDCI.Focus()
            Exit Sub
        End If
        If (dsDCI.Tables.IndexOf("DCI_MAJ") > -1) Then
            dsDCI.Tables("DCI_MAJ").Clear()
        End If

        StrSQL = " SELECT * FROM DCI WHERE CodeDCI = " + CodeDCI
        cmdDCI.Connection = ConnectionServeur
        cmdDCI.CommandText = StrSQL
        daDCI = New SqlDataAdapter(cmdDCI)
        daDCI.Fill(dsDCI, "DCI_MAJ")
        cbDCI = New SqlCommandBuilder(daDCI)

        

    End Sub

    Private Sub tCodeDCi_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeDCi.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLibelleDCI.Focus()
        End If
    End Sub

    Private Sub tCodeDCi_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeDCi.LostFocus
        lTest.Visible = False
    End Sub

    Private Sub tCodeDCi_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeDCi.TextChanged
        If tCodeDCi.Text <> "" Then
            If IsNumeric(tCodeDCi.Text.Substring(Len(tCodeDCi.Text) - 1, 1)) = False Then
                MsgBox("Veuillez introduire des chiffres !", MsgBoxStyle.Critical, "Erreur")
                tCodeDCi.Text = tCodeDCi.Text.Substring(0, Len(tCodeDCi.Text) - 1)
                tCodeDCi.Select(Len(tCodeDCi.Text), 0)
            End If
        End If

        Dim StrSQLtest As String = ""
        If (dsRecupereNum.Tables.IndexOf("DCI_TEST") > -1) Then
            dsRecupereNum.Tables("DCI_TEST").Clear()
        End If

        StrSQLtest = " SELECT * FROM DCI as DCI_TEST WHERE CodeDCI=" + Quote(tCodeDCi.Text)
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQLtest
        daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
        daRecupereNumt.Fill(dsRecupereNum, "DCI_TEST")

        If dsRecupereNum.Tables("DCI_TEST").Rows.Count <> 0 Then
            lTest.Text = "Code non valide déja existe"
            lTest.ForeColor = Color.OrangeRed
            lTest.Visible = True
            CodeExiste = True
        Else
            lTest.Text = "Code valide"
            lTest.ForeColor = Color.LawnGreen
            lTest.Visible = True
            CodeExiste = False
        End If
        If tCodeDCi.Text = "" Then
            lTest.Visible = False
        End If
    End Sub

    Public Sub Init()
        afficherDCI()
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub tRecherche_TextChanged(sender As Object, e As EventArgs) Handles tRecherche.TextChanged
        afficherDCI()
    End Sub

    Private Sub bModifier_Click(sender As Object, e As EventArgs) Handles bModifier.Click
        Dim StrSql As String = ""
        If tCodeDCi.Text = "" Then
            MsgBox("Veuillez saisir le code de la DCI !", MsgBoxStyle.Critical, "Erreur")
            tCodeDCi.Focus()
            Exit Sub
        End If
        If tLibelleDCI.Text = "" Then
            MsgBox("Veuillez saisir le libelle de la DCI !", MsgBoxStyle.Critical, "Erreur")
            tLibelleDCI.Focus()
            Exit Sub
        End If

        StrSql = "Update DCI Set LibelleDCI= '" + tLibelleDCI.Text + "' Where CodeDCI=" + Quote(tCodeDCi.Text)
        cmdDCI.Connection = ConnectionServeur
        cmdDCI.CommandText = StrSql
        cmdDCI.ExecuteNonQuery()
        afficherDCI()

    End Sub

    Private Sub gDCI_FetchRowStyle(sender As Object, e As FetchRowStyleEventArgs) Handles gDCI.FetchRowStyle
        tCodeDCi.Value = gDCI(gDCI.Row, "CodeDCI")
        tLibelleDCI.Value = gDCI(gDCI.Row, "LibelleDCI")
    End Sub
End Class