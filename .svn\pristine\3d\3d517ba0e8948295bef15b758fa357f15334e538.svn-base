﻿
Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fReglementFactureClient

    Declare Function InitFRIUSBLibrary Lib "FIRIDLLU.dll" () As Integer
    Declare Function OpenFRIDoor Lib "FIRIDLLU.dll" () As Integer
    Declare Function CloseFRIUSBLibrary Lib "FIRIDLLU.dll" () As Integer

    Dim cmdReglement As New SqlCommand
    Dim cbReglement As New SqlCommandBuilder
    Dim dsReglement As New DataSet
    Dim daReglement As New SqlDataAdapter

    Public ajoutmodif As String = ""
    Public CodeClient As String = ""
    Public CodeReglement As Integer = 0

    Dim StrSQL As String = ""
    Dim StrSQL1 As String = ""
    Dim i As Integer = 0
    Dim NumeroPremiereVente As String = ""

    Public SoldeClient As Double = 0.0
    'Public SoldeClient As String
    Dim x As Integer = 0
    Dim Regler As Boolean

    Public Sub init()
        Dim MontantPositif As Double = 0.0

        Dim i As Integer = 0
        'chargement des Banque
        StrSQL1 = "SELECT DISTINCT CodeBanque,NomBanque FROM BANQUE ORDER BY NomBanque ASC"
        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL1
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsReglement, "BANQUE")
        cmbBanque.DataSource = dsReglement.Tables("BANQUE")
        cmbBanque.ValueMember = "CodeBanque"
        cmbBanque.DisplayMember = "NomBanque"
        cmbBanque.ColumnHeaders = False
        cmbBanque.Splits(0).DisplayColumns("CodeBanque").Visible = False
        cmbBanque.Splits(0).DisplayColumns("NomBanque").Width = 10
        cmbBanque.ExtendRightColumn = True

        'chargement des Nature reglements
        StrSQL1 = "SELECT DISTINCT CodeNatureReglement,LibelleNatureReglement FROM NATURE_REGLEMENT WHERE LibelleNatureReglement<>'MULTIPLE' AND LibelleNatureReglement<>'CREDIT' ORDER BY LibelleNatureReglement ASC"
        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL1
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsReglement, "NATURE_REGLEMENT")
        cmbNature.DataSource = dsReglement.Tables("NATURE_REGLEMENT")
        cmbNature.ValueMember = "CodeNatureReglement"
        cmbNature.DisplayMember = "LibelleNatureReglement"
        cmbNature.ColumnHeaders = False
        cmbNature.Splits(0).DisplayColumns("CodeNatureReglement").Visible = False
        cmbNature.Splits(0).DisplayColumns("LibelleNatureReglement").Width = 10
        cmbNature.ExtendRightColumn = True

        If (dsReglement.Tables.IndexOf("FACTURE_CLIENT") > -1) Then
            dsReglement.Tables("FACTURE_CLIENT").Clear()
        End If

        ' chargement des ventes 

        StrSQL = " SELECT CAST(0 AS bit) Cocher," + _
                 " Id AS Nombre," + _
                 " Numero," + _
                 " Date," + _
                 " FACTURATION_CLIENT.TotalTTC - ISNULL(FACTURATION_CLIENT.TotalCNAM, 0) - ISNULL(FACTURATION_CLIENT.TotalMutuelle, 0) AS TotalTTC," + _
                 " ISNULL(TotalTVA, 0) AS TotalTVA," + _
                 " ISNULL(TotalRemise, 0) AS TotalRemise," + _
                 " ISNULL(FACTURATION_CLIENT.TotalTTC, 0) - ISNULL(FACTURATION_CLIENT.TotalRemise, 0) - ISNULL(FACTURATION_CLIENT.TotalCNAM, 0) - ISNULL(FACTURATION_CLIENT.TotalMutuelle, 0) AS TotalFacture " + _
                 " FROM FACTURATION_CLIENT " + _
                 " WHERE CodeClient = " & Quote(CodeClient)



        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsReglement, "FACTURE_CLIENT")
        cbReglement = New SqlCommandBuilder(daReglement)

        With gVentes
            .Columns.Clear()
            Try
                .DataSource = dsReglement
            Catch ex As Exception
            End Try
            .DataMember = "FACTURE_CLIENT"
            .Rebind(False)
            .Columns("Cocher").Caption = "Régler"
            .Columns("Numero").Caption = "Numero Facture"
            .Columns("Date").Caption = "Date"
            .Columns("TotalTVA").Caption = "Total TVA"
            .Columns("TotalTTC").Caption = "Total Facture"
            .Columns("TotalRemise").Caption = "Total Remise"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTVA").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalRemise").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("Nombre").Visible = False

            .Splits(0).DisplayColumns("Cocher").Width = 45
            .Splits(0).DisplayColumns("Numero").Width = 100
            .Splits(0).DisplayColumns("Date").Width = 70
            .Splits(0).DisplayColumns("TotalTTC").Width = 100
            .Splits(0).DisplayColumns("TotalTVA").Width = 100
            .Splits(0).DisplayColumns("TotalRemise").Width = 100

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gVentes)
        End With

        gVentes.HighLightRowStyle.ForeColor = Color.Black

        tNomInscrit.Enabled = True
        tMontant.Enabled = True
        ' chbEncaisse.Enabled = True
        dtEcheance.Enabled = True
        tNumeroCheque.Enabled = True
        tLibelle.Enabled = True
        cmbNature.Enabled = True
        cmbBanque.Enabled = True

        If ajoutmodif = "A" Then

            tNomInscrit.Value = ""
            'If SoldeClient > 0 Then
            'tMontant.Value = SoldeClient
            'Else
            tMontant.Value = 0.0
            'End If

            chbEncaisse.Checked = False
            dtEcheance.Value = System.DateTime.Today
            tNumeroCheque.Value = ""
            tLibelle.Value = ""
            cmbNature.Text = ""
            cmbBanque.Text = ""

            cmbNature.Focus()

        ElseIf ajoutmodif = "M" Then

            tNomInscrit.Value = RecupererValeurExecuteScalaire("NomInscritSurLeCheque", "REGLEMENT_CLIENT", "NumeroReglementClient", CodeReglement)
            MontantPositif = RecupererValeurExecuteScalaire("Montant", "REGLEMENT_CLIENT", "NumeroReglementClient", CodeReglement)

            'pour affiche uniquement les soldes positives
            If MontantPositif > 0 Then
                tMontant.Value = MontantPositif
            Else
                tMontant.Value = SoldeClient
            End If

            dtEcheance.Value = RecupererValeurExecuteScalaire("DateEcheance", "REGLEMENT_CLIENT", "NumeroReglementClient", CodeReglement)
            tNumeroCheque.Value = RecupererValeurExecuteScalaire("NumeroCheque", "REGLEMENT_CLIENT", "NumeroReglementClient", CodeReglement)
            tLibelle.Value = RecupererValeurExecuteScalaire("LibelleReglement", "REGLEMENT_CLIENT", "NumeroReglementClient", CodeReglement)
            cmbNature.SelectedValue = RecupererValeurExecuteScalaire("CodeNatureReglement", "REGLEMENT_CLIENT", "NumeroReglementClient", CodeReglement)
            lNumeroReglement.Text = CodeClient

            chbEncaisse.Checked = RecupererValeurExecuteScalaire("Encaisse", "REGLEMENT_CLIENT", "NumeroReglementClient", CodeReglement)

            If (RecupererValeurExecuteScalaire("CodeBanque", "REGLEMENT_CLIENT", "NumeroReglementClient", CodeReglement)) = "" Then
                cmbBanque.Text = ""
            Else
                cmbBanque.SelectedValue = RecupererValeurExecuteScalaire("CodeBanque", "REGLEMENT_CLIENT", "NumeroReglementClient", CodeReglement)
            End If

            Dim Existe As Integer = 0
            For i = 0 To gVentes.RowCount - 1
                StrSQL = " SELECT count(NumeroVente) FROM REGLEMENT_CLIENT_VENTE WHERE " + _
                         "NumeroReglementClient=" + CodeReglement.ToString + " AND NumeroVente='" + _
                         gVentes(i, "NumeroVente") + "'"

                cmdReglement.Connection = ConnectionServeur
                cmdReglement.CommandText = StrSQL

                Try
                    Existe = cmdReglement.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If Existe <> 0 Then
                    gVentes(i, "Cocher") = True
                    gVentes(i, "Nombre") = Existe

                    Existe = 0
                End If
            Next

        End If

        dtEcheance.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtEcheance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        If ajoutmodif = "A" Then
            cmbNature.Text = "ESPECE"
            chbEncaisse.Checked = True
        Else
            chbEncaisse.Checked = False
            chbEncaisse.Enabled = True
        End If



        lSoldeClient.Text = SoldeClient 'tMontant.Value



        bImprimer.Enabled = False

        'bCocherTous.Text = "Décocher tous"

        'For i = 0 To dsReglement.Tables("FACTURE_CLIENT").Rows.Count - 1
        '    dsReglement.Tables("FACTURE_CLIENT").Rows(i).Item("Cocher") = True
        'Next
        tMontant.Value = 0
    End Sub

    Private Function CalculSoldeActuel(ByVal CodeClient, ByVal DateActuel)
        Dim SommeVente As Double = 0.0
        Dim SommeReglement As Double = 0.0
        Dim SoldeInitiale As Double = 0.0
        Dim cmd As New SqlCommand


        cmd.CommandText = "select SUM(Montant) from REGLEMENT_CLIENT " + _
                          "left outer join CLIENT ON REGLEMENT_CLIENT.CodeClient=CLIENT.CodeClient " + _
                          "where REGLEMENT_CLIENT.CodeClient ='" + CodeClient + _
                          "' and  REGLEMENT_CLIENT.Date >= DateInitial "
        cmd.Connection = ConnectionServeur
        Try
            SommeReglement = cmd.ExecuteScalar()
        Catch ex As Exception
            SommeReglement = 0
        End Try

        cmd.CommandText = " SELECT SUM(TotalTTC -MontantCnam -MontantMutuelle) " + _
                          " FROM VENTE LEFT OUTER JOIN CLIENT ON VENTE.CodeClient=CLIENT.CodeClient " + _
                          " WHERE CLIENT.CodeClient ='" + CodeClient + _
                          "' AND Date >= DateInitial "
        cmd.Connection = ConnectionServeur
        Try
            SommeVente = cmd.ExecuteScalar()
        Catch ex As Exception
            SommeVente = 0
        End Try


        cmd.CommandText = "select SoldeInitial FROM CLIENT WHERE CodeClient ='" + CodeClient + "'"
        cmd.Connection = ConnectionServeur
        Try
            SoldeInitiale = cmd.ExecuteScalar()
        Catch ex As Exception
            SoldeInitiale = 0
        End Try


        Return (SommeVente - SommeReglement + SoldeInitiale)

    End Function

    Private Sub bconfirmerReglement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bconfirmerReglement.Click

        Dim StrSQL As String = ""
        Dim NumeroReglement As Integer = 0
        Dim i As Integer = 0
        Dim Reste As String
        Dim MontantAEnregistrer As Double = 0.0
        Dim CodeNatureReglement As Integer = 0
        Dim DateEcheance As String = ""
        Dim Montant As String
        Dim CodeBanque As String = ""

        If cmbNature.Text = "" Then
            MsgBox("Vous devez sélectionner une nature", MsgBoxStyle.OkOnly)
            cmbNature.Focus()
            Exit Sub
        End If

        If tMontant.Text = "0" Then
            MsgBox("Montant = 0 ", MsgBoxStyle.OkOnly)
            tMontant.Focus()
            Exit Sub
        End If

        'If cmbNature.Text = "CHEQUE" And cmbBanque.Text = "" Then
        '    MsgBox("Vous devez sélectionner la banque !", MsgBoxStyle.Critical, "Erreur")
        '    cmbBanque.Focus()
        '    Exit Sub
        'End If

        '------------------------------ demande du mot de passe
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        Dim myMotDePasse As New fMotDePasse


        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If

        If ajoutmodif = "A" Then

            '------------- ajout du nouveau reglement client

            StrSQL = " SELECT max([NumeroReglementClient]) FROM [REGLEMENT_CLIENT]"
            cmdReglement.Connection = ConnectionServeur
            cmdReglement.CommandText = StrSQL
            Try
                NumeroReglement = cmdReglement.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            NumeroReglement = NumeroReglement + 1

            CodeNatureReglement = cmbNature.SelectedValue


            If dtEcheance.Text <> "" Then
                DateEcheance = dtEcheance.Text
            Else
                DateEcheance = "Null"
            End If

            If tMontant.Text = "" Then
                Montant = "0"
            Else
                Montant = tMontant.Text
            End If

            If cmbBanque.Text <> "" Then
                CodeBanque = cmbBanque.SelectedValue
            Else
                CodeBanque = "Null"
            End If



            StrSQL = "INSERT INTO REGLEMENT_CLIENT " + _
                     "(""NumeroReglementClient"",""LibelleReglement"",""CodeNatureReglement""" + _
                     ",""Date"",""DateEcheance"",""MontantRegle"",""Montant"",""NumeroCheque"",""LibellePoste""" + _
                     ",""NomInscritSurLeCheque"",""CodeClient"",""CodeBanque"",""Vider"",""Encaisse"",""CodePersonnel"") " + _
                     " VALUES('" + NumeroReglement.ToString + _
                     "','" + tLibelle.Text + _
                     "','" + CodeNatureReglement.ToString + _
                     "','" + System.DateTime.Now.ToString + _
                     "','" + DateEcheance + _
                     "','" + Montant.ToString + _
                     "','" + Montant.ToString + _
                     "','" + tNumeroCheque.Text + _
                     "','" + System.Environment.GetEnvironmentVariable("Poste") + _
                     "','" + tNomInscrit.Text + _
                     "','" + CodeClient + _
                     "'," + CodeBanque + _
                     ",'" + "False" + _
                     "','" + chbEncaisse.Checked.ToString + _
                     "','" + CodeOperateur.ToString + " ')"

            cmdReglement.Connection = ConnectionServeur
            cmdReglement.CommandText = StrSQL
            Try
                cmdReglement.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

        Else
            '--------------------- suppression des afféctations des anciennes ventes à ce règlement
            Try
                cmdReglement.Connection = ConnectionServeur
                cmdReglement.CommandText = "DELETE FROM REGLEMENT_CLIENT_VENTE WHERE NumeroReglementClient = " + CodeReglement.ToString
                cmdReglement.ExecuteNonQuery()
            Catch ex As Exception
                MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
            End Try

            '---------------------------------------------------------------------------------------

            If dtEcheance.Text <> "" Then
                DateEcheance = dtEcheance.Text
            Else
                DateEcheance = "Null"
            End If

            If tMontant.Text = "" Then
                Montant = "0"
            Else
                Montant = tMontant.Text
            End If

            If cmbBanque.Text <> "" Then
                CodeBanque = cmbBanque.SelectedValue.ToString
            Else
                CodeBanque = "Null"
            End If

            StrSQL = " UPDATE REGLEMENT_CLIENT SET LibelleReglement='" + tLibelle.Text + _
                     "',CodeNatureReglement=" + cmbNature.SelectedValue.ToString + _
                     ",Date='" + System.DateTime.Now.ToString + _
                     "',DateEcheance='" + DateEcheance + _
                     "',Montant=" + Montant.ToString + _
                     ",NumeroCheque='" + tNumeroCheque.Text + _
                     "',LibellePoste='" + System.Environment.GetEnvironmentVariable("Poste") + _
                     "',NomInscritSurLeCheque='" + tNomInscrit.Text + _
                     "',CodeClient='" + CodeClient + _
                     "',CodeBanque=" + CodeBanque + _
                     ",CodePersonnel=" + CodeOperateur + _
                     ",Encaisse='" + chbEncaisse.Checked.ToString + _
                     "' WHERE NumeroReglementClient='" + CodeReglement.ToString + "'"

            cmdReglement.Connection = ConnectionServeur
            cmdReglement.CommandText = StrSQL
            Try
                cmdReglement.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            NumeroReglement = CodeReglement
        End If

        '----------- ajout des ventes règlées par ce règlement dans la table règlement_client_vente

        Reste = tMontant.Text
        For i = 0 To dsReglement.Tables("FACTURE_CLIENT").Rows.Count - 1
            If dsReglement.Tables("FACTURE_CLIENT").Rows(i).Item("Cocher") = True Then

                If Reste > dsReglement.Tables("FACTURE_CLIENT").Rows(i).Item("TotalTTC") - (dsReglement.Tables("FACTURE_CLIENT").Rows(i).Item("MontantCnam") + dsReglement.Tables("FACTURE_CLIENT").Rows(i).Item("MontantMutuelle")) Then
                    MontantAEnregistrer = dsReglement.Tables("FACTURE_CLIENT").Rows(i).Item("TotalTTC") - (dsReglement.Tables("FACTURE_CLIENT").Rows(i).Item("MontantCnam") + dsReglement.Tables("FACTURE_CLIENT").Rows(i).Item("MontantMutuelle"))
                    Reste = Reste - dsReglement.Tables("FACTURE_CLIENT").Rows(i).Item("TotalTTC") - (dsReglement.Tables("FACTURE_CLIENT").Rows(i).Item("MontantCnam") + dsReglement.Tables("FACTURE_CLIENT").Rows(i).Item("MontantMutuelle"))
                Else
                    MontantAEnregistrer = Reste
                    Reste = 0
                End If

                StrSQL = "INSERT INTO REGLEMENT_CLIENT_VENTE " + _
                     "(""NumeroReglementClient"",""NumeroVente"",""MontantRegle"") " + _
                     " VALUES('" + NumeroReglement.ToString + _
                     "','" + dsReglement.Tables("FACTURE_CLIENT").Rows(i).Item("NumeroVente") + _
                     "','" + MontantAEnregistrer.ToString + "')"

                cmdReglement.Connection = ConnectionServeur
                cmdReglement.CommandText = StrSQL
                Try
                    cmdReglement.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

            End If
        Next

        '----------------------------------------------------------------------------------------
        'impression du réçu du règlement 

        If ImprimanteATicket And ajoutmodif = "A" Then
            ImprimerRecu(NumeroReglement)
        End If

        ' ouverture de tiroir 

        If Tiroir And tMontant.Text <> 0 And cmbNature.SelectedValue <> 2 And cmbNature.SelectedValue <> 4 And cmbNature.SelectedValue <> 3 And cmbNature.SelectedValue <> 5 And cmbNature.SelectedValue <> 7 Then
            'If Tiroir And MontantAEnregistrer <> 0 Then
            If TiroirUSB Then
                Try
                    InitFRIUSBLibrary()
                    OpenFRIDoor()
                    CloseFRIUSBLibrary()
                Catch ex As Exception
                End Try
            End If
            If TiroirCOM Then
                Try
                    If Not (SerialPortTiroir.IsOpen = True) Then
                        SerialPortTiroir.Open()
                    End If
                    SerialPortTiroir.Write("g")
                Catch ex As Exception
                    Try
                        Dim start As New ProcessStartInfo()
                        Dim Port As String = ""
                        Try
                            Port = RecupererValeurExecuteScalaire("COM", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
                        Catch
                        End Try
                        Shell("cmd.exe /K dir > COM" & Port, AppWinStyle.Hide, True, 2000)
                    Catch
                    End Try
                End Try
            End If
        End If

        If cmbNature.Text = "REMISE" Then
            ModuleSurveillance(14, "L'utilisateur " & NomUtilisateur & " a effectué un réglement avec remise")
        End If

        lSoldeClient.Text = CalculSoldeActuel(CodeClient, "01/01/2000").ToString()

        tNomInscrit.Enabled = False
        tMontant.Enabled = False
        chbEncaisse.Enabled = False
        dtEcheance.Enabled = False
        tNumeroCheque.Enabled = False
        tLibelle.Enabled = False
        cmbNature.Enabled = False
        cmbBanque.Enabled = False

        bannulerReglement.Enabled = True
        bconfirmerReglement.Enabled = False
        bImprimer.Enabled = True
        bannulerReglement.Text = "Sortir     F10"
    End Sub

    Public Sub ImprimerRecu(ByVal NumeroReglment As String)

        Dim CondCrystal As String = ""

        'Dim I As Integer
        'Dim num As Integer = 999
        'For I = 0 To fMain.Tab.TabPages.Count - 1
        '    If fMain.Tab.TabPages(I).Text = "Impression de Réçu" Then
        '        num = I
        '    End If
        'Next

        CondCrystal = " {Vue_EtatRecuReglement.NumeroReglementClient} = " + NumeroReglment + ""
        CR1.FileName = Application.StartupPath + "\EtatRecuReglement.rpt"
        'CR1.SetParameterValue("APayer", lTotalTTC.Text)

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        For Each tbCurrent In CR1.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent
        CR1.RecordSelectionFormula = CondCrystal
        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR1



        Try
            CR1.PrintOptions.PrinterName = NomDeLImprimante
            CR1.PrintToPrinter(1, False, 1, 100)
            MyViewer.Dispose()

        Catch ex As Exception
            MsgBox("Nom de l'imprimante ticket invalide !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
        End Try

        'fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        'fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        'fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        'fMain.Tab.SelectedTab.Text = "Impression de Réçu"
        'If num <> 999 Then
        '    fMain.Tab.TabPages(num).Dispose()
        'End If
    End Sub

    Private Sub bannulerReglement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bannulerReglement.Click
        tNomInscrit.Enabled = False
        tMontant.Enabled = False
        chbEncaisse.Enabled = False
        dtEcheance.Enabled = False
        tNumeroCheque.Enabled = False
        tLibelle.Enabled = False
        cmbNature.Enabled = False
        cmbBanque.Enabled = False

        bannulerReglement.Enabled = False
        bconfirmerReglement.Enabled = False
        Me.Hide()
    End Sub

    Private Sub tMontant_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tMontant.KeyUp
        Dim TestConversion As Double = 0.0
        If tMontant.Text <> "-" Then
            Try   ' test si un valeur numerique ou non
                TestConversion = Math.Round(CDbl(tMontant.Text), 3)
            Catch ex As Exception
                MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
                tMontant.Text = "0.000"
                tMontant.Focus()
                tMontant.SelectionLength = tMontant.Text.Length
                Exit Sub
            End Try
        End If

        If e.KeyCode = Keys.Enter Then
            bconfirmerReglement.Focus()
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub tMontant_LostFocus(sender As Object, e As System.EventArgs) Handles tMontant.LostFocus
        Try
            If tMontant.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(tMontant.Text, ".")
                If tMontant.Text.Length - x = 1 Then
                    tMontant.Text = tMontant.Text + ("00")
                ElseIf tMontant.Text.Length - x = 2 Then
                    tMontant.Text = tMontant.Text + ("0")
                End If
            Else
                tMontant.Text = tMontant.Text + ".000"
            End If
        Catch
        End Try
        If tMontant.Text <> "" Then
            If tMontant.Value = "Non Numérique" Then
                tMontant.Value = "0.000"
            End If
            tMontant.Value = Format$(CDec(tMontant.Text), "0.000")
        Else
            tMontant.Value = "0,000"
        End If

    End Sub

    Private Sub tMontant_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tMontant.TextChanged

    End Sub

    Private Sub bannulerReglement_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bannulerReglement.KeyUp
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub cmbNature_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbNature.KeyDown
        If cmbNature.Text = "REMISE" Then
            tTaux.Enabled = True
            ltaux.Enabled = True
        Else
            tTaux.Enabled = False
            ltaux.Enabled = False
        End If
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub cmbNature_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbNature.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbNature.Text = cmbNature.WillChangeToText
        ElseIf e.KeyCode <> Keys.F5 Then
            cmbNature.OpenCombo()
        End If

        If e.KeyCode = Keys.Enter And cmbNature.Text <> "ESPECE" Then
            dtEcheance.Focus()
            Exit Sub
        ElseIf e.KeyCode = Keys.Enter And cmbNature.Text = "ESPECE" Then
            tLibelle.Focus()
            Exit Sub
        End If


    End Sub

    Private Sub cmbNature_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbNature.LostFocus
        Dim i As Integer
        Dim trouve As Boolean = False
        If cmbNature.Text = "REMISE" Then
            tTaux.Enabled = True
            ltaux.Enabled = True
        Else
            tTaux.Enabled = False
            ltaux.Enabled = False
        End If
        For i = 0 To cmbNature.ListCount - 1
            If cmbNature.Columns("LibelleNatureReglement").CellValue(i) Like cmbNature.Text Then
                trouve = True
            End If
        Next
        If trouve = False Then
            cmbNature.Text = ""
        End If
    End Sub

    Private Sub cmbNature_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbNature.TextChanged
        If cmbNature.Text = "REMISE" Then
            chbTaux.Checked = True
        Else
            chbTaux.Checked = False
        End If
        If cmbNature.Text = "ESPECE" Or cmbNature.Text = "CARTE" Then
            cmbBanque.Enabled = False
            dtEcheance.Enabled = False
            tNumeroCheque.Enabled = False
            tNomInscrit.Enabled = False
        Else
            cmbBanque.Enabled = True
            dtEcheance.Enabled = True
            tNumeroCheque.Enabled = True
            tNomInscrit.Enabled = True
        End If
    End Sub

    Private Sub dtEcheance_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtEcheance.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbBanque.Focus()
            Exit Sub
        End If
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub dtEcheance_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtEcheance.TextChanged
        If dtEcheance.Text > System.DateTime.Today Then
            chbEncaisse.Checked = False
        Else
            chbEncaisse.Checked = True
        End If
    End Sub

    Private Sub cmbBanque_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbBanque.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNumeroCheque.Focus()
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub cmbBanque_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbBanque.TextChanged

    End Sub

    Private Sub tNumeroCheque_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNumeroCheque.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNomInscrit.Focus()
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub tNumeroCheque_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNumeroCheque.TextChanged

    End Sub

    Private Sub tLibelle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tLibelle.KeyUp
        If e.KeyCode = Keys.Enter Then
            tMontant.Focus()
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub tLibelle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tLibelle.TextChanged

    End Sub

    Private Sub tNomInscrit_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNomInscrit.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLibelle.Focus()
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub tNomInscrit_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNomInscrit.TextChanged

    End Sub

    Private Sub chbEncaisse_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbEncaisse.CheckedChanged

    End Sub

    Private Sub chbEncaisse_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles chbEncaisse.KeyUp
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub gVentes_AfterColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gVentes.AfterColEdit
        Dim i As Integer = 0
        Dim TotalMontant As Decimal = 0.0
        If Regler Then
            gVentes.Columns("Cocher").Value = True
        End If

        For i = 0 To gVentes.RowCount - 1
            If gVentes(i, "Cocher") = True Then
                TotalMontant += gVentes(i, "TotalTTC") - gVentes(i, "MontantCnam") - gVentes(i, "MontantMutuelle")
            End If
        Next
        tMontant.Value = TotalMontant
    End Sub

    Private Sub gVentes_BeforeColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColEditEventArgs) Handles gVentes.BeforeColEdit
        If gVentes(gVentes.Row, "Cocher") = True And gVentes(gVentes.Row, "Nombre") > 0 Then
            Regler = True
        Else
            Regler = False
        End If
    End Sub

    Private Sub gVentes_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gVentes.KeyUp
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub bconfirmerReglement_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bconfirmerReglement.KeyUp
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click

        Dim CondCrystal As String = ""
        Dim Somme_Echeance As Double = 0.0
        Dim StrSQLSolde As String = ""
        Dim Vue As String = ""

        CR.FileName = Application.StartupPath + "\EtatDeBonDeReglement.rpt"

        CR.SetParameterValue("CodeClient", CodeClient)
        CR.SetParameterValue("pharmacie", Pharmacie)
        CR.SetParameterValue("NomClient", RecupererValeurExecuteScalaire("Nom", "CLIENT", "CodeClient", CodeClient))
        CR.SetParameterValue("TypeReglement", cmbNature.Text)
        CR.SetParameterValue("Somme", tMontant.Text)
        CR.SetParameterValue("SoldeRestant", (SoldeClient - CDbl(Val(tMontant.Text))))

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo

        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent

        Dim MyViewer As New fAfficheurEtat
        MyViewer.CRViewer.ReportSource = CR
        Me.Close()
        MyViewer.ShowDialog()

    End Sub


    Private Sub bCocherTous_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCocherTous.Click
        Dim I As Integer = 0
        Dim TotalMontant As Decimal = 0.0
        If bCocherTous.Text = "Cocher tous" Then
            For I = 0 To dsReglement.Tables("FACTURE_CLIENT").Rows.Count - 1
                dsReglement.Tables("FACTURE_CLIENT").Rows(I).Item("Cocher") = True
                TotalMontant += gVentes(I, "TotalTTC") - gVentes(I, "MontantCnam") - gVentes(I, "MontantMutuelle")
            Next
            tMontant.Value = TotalMontant
            bCocherTous.Text = "Décocher tous"
        Else
            For I = 0 To dsReglement.Tables("FACTURE_CLIENT").Rows.Count - 1
                dsReglement.Tables("FACTURE_CLIENT").Rows(I).Item("Cocher") = False
            Next
            bCocherTous.Text = "Cocher tous"

            tMontant.Value = 0.0
        End If
    End Sub

    Private Sub gVentes_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gVentes.MouseClick

    End Sub

    Private Sub tTaux_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles tTaux.KeyUp
        If e.KeyCode = Keys.Enter Then
            tMontant.Focus()
            tMontant.Value = Math.Round((SoldeClient * tTaux.Value / 100), 3)
        End If
    End Sub

    Private Sub tTaux_LostFocus(sender As Object, e As System.EventArgs) Handles tTaux.LostFocus
        tMontant.Value = Math.Round((SoldeClient * tTaux.Value / 100), 3)
    End Sub

    Private Sub chbTaux_CheckedChanged(sender As System.Object, e As System.EventArgs) Handles chbTaux.CheckedChanged
        If chbTaux.Checked Then
            cmbNature.SelectedValue = 7
            tTaux.Enabled = True
            ltaux.Enabled = True
        Else
            cmbNature.SelectedValue = 1
            tTaux.Enabled = False
            ltaux.Enabled = False
        End If
    End Sub
End Class