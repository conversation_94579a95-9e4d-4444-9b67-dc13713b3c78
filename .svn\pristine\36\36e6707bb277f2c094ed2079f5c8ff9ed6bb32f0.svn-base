﻿Imports System.IO
Imports System.Data.SqlClient
Imports System.Windows.Forms

Public Class fFicheClient111

    Public ajoutmodif As String = ""
    Public CodeClient As String = ""

    Dim cmdClient As New SqlCommand
    Dim cbClient As New SqlCommandBuilder
    Dim dsClient As New DataSet
    Dim daClient As New SqlDataAdapter

    Dim dsRecupereNum As New DataSet
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNumt As New SqlDataAdapter
    Dim CodeExiste As Boolean = False

    Dim CmdCalcul As New SqlCommand

    Public Sub Init()

        Dim StrSQL As String = ""
        Dim StrSQL1 As String = ""
        Dim StrSQLdernierAchat As String = ""
        Dim Dernier_Date_Achat As String = ""
        Dim StrSQLSolde As String = ""
        Dim Somme_Facture As Double = 0.0
        Dim Somme_Reglement As Double = 0.0
        Dim difference As Double = 0.0

        'Dim ConnectionServeur As New SqlConnection

        'chargement des villes
        StrSQL1 = "SELECT DISTINCT CodeVille,NomVille FROM VILLE WHERE SupprimeVille=0 ORDER BY NomVille ASC"
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "VILLE")
        cmbVilleClient.DataSource = dsClient.Tables("VILLE")
        cmbVilleClient.ValueMember = "CodeVille"
        cmbVilleClient.DisplayMember = "NomVille"
        cmbVilleClient.ColumnHeaders = False
        cmbVilleClient.Splits(0).DisplayColumns("CodeVille").Visible = False
        cmbVilleClient.Splits(0).DisplayColumns("NomVille").Width = 10

        'chargement des situations
        StrSQL1 = "SELECT DISTINCT CodeSituationClient,LibelleSituationClient FROM SITUATION_CLIENT ORDER BY LibelleSituationClient ASC"
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "SITUATION_CLIENT")
        cmbSituationClient.DataSource = dsClient.Tables("SITUATION_CLIENT")
        cmbSituationClient.ValueMember = "CodeSituationClient"
        cmbSituationClient.DisplayMember = "LibelleSituationClient"
        cmbSituationClient.ColumnHeaders = False
        cmbSituationClient.Splits(0).DisplayColumns("CodeSituationClient").Visible = False
        cmbSituationClient.Splits(0).DisplayColumns("LibelleSituationClient").Width = 10

        'chargement des Medecins
        StrSQL1 = "SELECT DISTINCT CodeMedecin,NomMedecin FROM MEDECIN ORDER BY NomMedecin ASC"
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "MEDECIN")
        cmbMedecin.DataSource = dsClient.Tables("MEDECIN")
        cmbMedecin.ValueMember = "CodeMedecin"
        cmbMedecin.DisplayMember = "NomMedecin"
        cmbMedecin.ColumnHeaders = False
        cmbMedecin.Splits(0).DisplayColumns("CodeMedecin").Visible = False
        cmbMedecin.Splits(0).DisplayColumns("NomMedecin").Width = 10

        If ajoutmodif = "A" Then
            StrSQL = " SELECT TOP 0 * FROM CLIENT"
        Else
            StrSQL = " SELECT * FROM CLIENT WHERE CodeClient = " + Quote(CodeClient)
        End If

        'AfficherInformationsClient()
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "CLIENT")
        cbClient = New SqlCommandBuilder(daClient)


        If ajoutmodif = "M" Then
            tCodeClient.Text = dsClient.Tables("CLIENT").Rows(0)("CodeClient")
            tNomClient.Text = dsClient.Tables("CLIENT").Rows(0)("Nom")

            tAdresseClient.Text = dsClient.Tables("CLIENT").Rows(0)("Adresse")
            cmbVilleClient.SelectedValue = dsClient.Tables("CLIENT").Rows(0)("Codeville")
            tTelephoneClient.Text = dsClient.Tables("CLIENT").Rows(0)("Telephone")
            tFaxClient.Text = dsClient.Tables("CLIENT").Rows(0)("Fax")
            tCodePostalClient.Text = dsClient.Tables("CLIENT").Rows(0)("CodePostal")

            tIdCNAM.Text = dsClient.Tables("CLIENT").Rows(0)("IdentifiantCnam")
            cmbMedecin.SelectedValue = dsClient.Tables("CLIENT").Rows(0)("CodeMedecin")
            tDateValidite.Text = dsClient.Tables("CLIENT").Rows(0)("DateValidite")

            cmbSituationClient.SelectedValue = dsClient.Tables("CLIENT").Rows(0)("CodeSituation")

            chbPaimentComptantClient.Text = dsClient.Tables("CLIENT").Rows(0)("PaiementComptant")
            tCrediMaxClient.Text = dsClient.Tables("CLIENT").Rows(0)("CrediMax")

            tSoldeInitial.Text = dsClient.Tables("CLIENT").Rows(0)("SoldeInitial")
            tDateInitial.Text = dsClient.Tables("CLIENT").Rows(0)("DateInitial")

            tRemarqueClient.Text = dsClient.Tables("CLIENT").Rows(0)("Remarque")


            ' récupération de la dernière date d'achat pour le client concerné 
            StrSQLdernierAchat = "SELECT MAX(DateVente) FROM VENTE WHERE CodeClient =" + Quote(CodeClient)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLdernierAchat

            Try
                Dernier_Date_Achat = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            'calcul du solde client en retranchant la somme des montants des règlements de la somme des montants des ventes 
            StrSQLSolde = "SELECT SUM(TotalTTC) FROM VENTE WHERE CodeClient =" + Quote(CodeClient)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLSolde

            Try
                Somme_Facture = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQLSolde = "SELECT SUM(Montant) FROM REGLEMENT_CLIENT WHERE CodeClient =" + Quote(CodeClient)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLSolde

            Try
                Somme_Reglement = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            If (Dernier_Date_Achat) <> "" Then
                tDateDernierAchatClient.Text = Dernier_Date_Achat
            End If
            difference = Somme_Facture - Somme_Reglement
            tSoldeClient.Text = Convert.ToString(difference)
            '---------------------- verouillage du champs Code client pour interdire la modification ------------
            tCodeClient.Enabled = False

        End If
        '---------------------- verouillage des champs indisponibles ------------
        tDateDernierAchatClient.Enabled = False
        tSoldeClient.Enabled = False

        tCodeClient.Focus()
    End Sub

    Private Sub fFicheClient_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
    End Sub


    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click

        Dim DernierCode As String = ""
        Dim dr As DataRow
        Dim StrSQL As String = ""

        If tCodeClient.Text = "" Then

            StrSQL = " SELECT max(CodeClient) FROM CLIENT WHERE codeclient LIKE 'Customer%'"
            cmdClient.Connection = ConnectionServeur
            cmdClient.CommandText = StrSQL
            Try
                DernierCode = cmdClient.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            If DernierCode = "" Then
                tCodeClient.Text = "Customer1"
            Else
                DernierCode = (Convert.ToInt32(DernierCode.Substring(8, DernierCode.Length - 8)) + 1).ToString
                tCodeClient.Text = "Customer" + DernierCode
            End If
            lTest.Visible = False
        End If

        If tNomClient.Text = "" Then
            MsgBox("Veuillez saisir le nom du client !", MsgBoxStyle.Critical, "Erreur")
            tNomClient.Focus()
            Exit Sub
        End If

        

        If cmbVilleClient.Text = "" Then
            MsgBox("Veuillez saisir la ville du client !", MsgBoxStyle.Critical, "Erreur")
            cmbVilleClient.Focus()
            Exit Sub
        End If

        If cmbSituationClient.Text = "" Then
            MsgBox("Veuillez saisir la situation du client !", MsgBoxStyle.Critical, "Erreur")
            cmbSituationClient.Focus()
            Exit Sub
        End If

        If CodeExiste = True Then
            MsgBox("Code client existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
            tCodeClient.Focus()
            Exit Sub
        End If

        If ajoutmodif = "A" Then
            With dsClient
                dr = .Tables("CLIENT").NewRow

                dr.Item("CodeClient") = tCodeClient.Text
                dr.Item("Nom") = tNomClient.Text

                dr.Item("Adresse") = tAdresseClient.Text

                If cmbVilleClient.Text <> "" Then
                    dr.Item("Codeville") = cmbVilleClient.SelectedValue
                End If

                dr.Item("Telephone") = tTelephoneClient.Text

                dr.Item("Fax") = tFaxClient.Text
                dr.Item("CodePostal") = tCodePostalClient.Text

                dr.Item("IdentifiantCnam") = tIdCNAM.Text

                If cmbMedecin.Text <> "" Then
                    dr.Item("CodeMedecin") = cmbMedecin.SelectedValue
                End If

                If tDateValidite.Text <> "" Then
                    dr.Item("DateValidite") = Convert.ToDateTime(tDateValidite.Text)
                End If

                dr.Item("CodeSituation") = cmbSituationClient.SelectedValue

                dr.Item("PaiementComptant") = chbPaimentComptantClient.Checked
                dr.Item("CrediMax") = tCrediMaxClient.Text

                dr.Item("SoldeInitial") = tSoldeInitial.Text
                dr.Item("DateInitial") = tDateInitial.Text

                dr.Item("Remarque") = tRemarqueClient.Text

                .Tables("CLIENT").Rows.Add(dr)
            End With


        ElseIf ajoutmodif = "M" Then
            With dsClient.Tables("CLIENT")
                dr = .Rows(0)
                dr.Item("CodeClient") = tCodeClient.Text
                dr.Item("Nom") = tNomClient.Text

                dr.Item("Adresse") = tAdresseClient.Text
                dr.Item("Codeville") = cmbVilleClient.SelectedValue
                dr.Item("Telephone") = tTelephoneClient.Text

                dr.Item("Fax") = tFaxClient.Text
                dr.Item("CodePostal") = tCodePostalClient.Text

                dr.Item("IdentifiantCnam") = tIdCNAM.Text
                dr.Item("CodeMedecin") = cmbMedecin.SelectedValue
                dr.Item("DateValidite") = tDateValidite.Text

                dr.Item("CodeSituation") = cmbSituationClient.SelectedValue

                dr.Item("PaiementComptant") = chbPaimentComptantClient.Checked
                dr.Item("CrediMax") = tCrediMaxClient.Text

                dr.Item("SoldeInitial") = tSoldeInitial.Text
                dr.Item("DateInitial") = tDateInitial.Text

                dr.Item("Remarque") = tRemarqueClient.Text
            End With

        End If
        Try
            daClient.Update(dsClient, "CLIENT")
            fMain.TAB.SelectedTab.Dispose()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsClient.Reset()
            Me.Init()
        End Try
    End Sub
    Private Sub Panel_Paint(ByVal sender As System.Object, ByVal e As System.Windows.Forms.PaintEventArgs) Handles Panel.Paint

    End Sub
    Private Sub tCodeClient_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeClient.LostFocus
        lTest.Visible = False
    End Sub
    Private Sub tCodeClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeClient.TextChanged
        Dim StrSQLtest As String = ""
        dsRecupereNum.Clear()
        If ajoutmodif = "A" Then
            StrSQLtest = " SELECT * FROM CLIENT WHERE CodeClient=" + Quote(tCodeClient.Text)
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQLtest
            daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
            daRecupereNumt.Fill(dsRecupereNum, "CLIENT")

            If dsRecupereNum.Tables("CLIENT").Rows.Count <> 0 Or tCodeClient.Text = "" Then
                lTest.Text = "Code non valide déja existe"
                lTest.ForeColor = Color.OrangeRed
                lTest.Visible = True
                CodeExiste = True
            Else
                lTest.Text = "Code valide"
                lTest.ForeColor = Color.LimeGreen
                lTest.Visible = True
                CodeExiste = False
            End If
        End If
        If tCodeClient.Text = "" Then
            lTest.Visible = False
        End If

    End Sub

    Private Sub lTest_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lTest.Click

    End Sub

    Private Sub tTelephoneClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tTelephoneClient.TextChanged

    End Sub

    Private Sub cmbVilleClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbVilleClient.KeyUp
        'Recherche_Automatique_fiche(e, cmbVilleClient, cmbVilleClient.Columns("NomVille"))
        If e.KeyCode = Keys.Enter Then
            cmbVilleClient.Text = cmbVilleClient.WillChangeToText
        Else
            cmbVilleClient.OpenCombo()
        End If
    End Sub

    Private Sub cmbVilleClient_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbVilleClient.LostFocus
        Dim i As Integer
        Dim trouve As Boolean = False
        For i = 0 To cmbVilleClient.ListCount - 1
            If cmbVilleClient.Columns("NomVille").CellValue(i) Like cmbVilleClient.Text Then
                trouve = True
            End If
        Next
        If trouve = False Then
            cmbVilleClient.Text = ""
        End If
    End Sub

    Private Sub cmbVilleClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbVilleClient.TextChanged

    End Sub

    Private Sub cmbSecteurClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub cmbBanqueClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub cmbSituationClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbSituationClient.KeyUp
        'Recherche_Automatique_fiche(e, cmbSituationClient, cmbSituationClient.Columns("LibelleSituationClient"))
        If e.KeyCode = Keys.Enter Then
            cmbSituationClient.Text = cmbSituationClient.WillChangeToText
        Else
            cmbSituationClient.OpenCombo()
        End If
    End Sub

    Private Sub cmbSituationClient_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbSituationClient.LostFocus
        Dim i As Integer
        Dim trouve As Boolean = False
        For i = 0 To cmbSituationClient.ListCount - 1
            If cmbSituationClient.Columns("LibelleSituationClient").CellValue(i) Like cmbSituationClient.Text Then
                trouve = True
            End If
        Next
        If trouve = False Then
            cmbSituationClient.Text = ""
        End If
    End Sub

    Private Sub cmbSituationClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbSituationClient.TextChanged

    End Sub

    Private Sub cmbVendeurClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        If dsClient.HasChanges Then
            If MsgBox("Voulez vous vraiment annuler les modifications de ce Client ?", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                fMain.Tab.SelectedTab.Dispose()
            End If
        Else
            fMain.Tab.SelectedTab.Dispose()
        End If
    End Sub

End Class
