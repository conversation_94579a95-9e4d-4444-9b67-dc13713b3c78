﻿<?xml version="1.0" encoding="utf-8"?>
<Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
  <EntityContainerMapping StorageEntityContainer="SaleReportModelStoreContainer" CdmEntityContainer="SaleReportEntities">
    <EntitySetMapping Name="PARAMETRE_PHARMACIE">
      <EntityTypeMapping TypeName="SaleReportModel.PARAMETRE_PHARMACIE">
        <MappingFragment StoreEntitySet="PARAMETRE_PHARMACIE">
          <ScalarProperty Name="ImprimerUnEtiquette" ColumnName="ImprimerUnEtiquette" />
          <ScalarProperty Name="AutoriserSaisieNonMembeFamille" ColumnName="AutoriserSaisieNonMembeFamille" />
          <ScalarProperty Name="AfficherReglementsSupprimes" ColumnName="AfficherReglementsSupprimes" />
          <ScalarProperty Name="MettreAJourPrixFrigo" ColumnName="MettreAJourPrixFrigo" />
          <ScalarProperty Name="AutoriserModificationPrixDansAchat" ColumnName="AutoriserModificationPrixDansAchat" />
          <ScalarProperty Name="NombreCopieImpBon" ColumnName="NombreCopieImpBon" />
          <ScalarProperty Name="DateDerniereMiseAJour" ColumnName="DateDerniereMiseAJour" />
          <ScalarProperty Name="TypeClassementManquant" ColumnName="TypeClassementManquant" />
          <ScalarProperty Name="NbrCommandePourClasserManquant" ColumnName="NbrCommandePourClasserManquant" />
          <ScalarProperty Name="HistoriqueMouvementArticle" ColumnName="HistoriqueMouvementArticle" />
          <ScalarProperty Name="ImpressionDirectApresVente" ColumnName="ImpressionDirectApresVente" />
          <ScalarProperty Name="QuantiteMultipleDeCinq" ColumnName="QuantiteMultipleDeCinq" />
          <ScalarProperty Name="NombreJoursValiditerAppareillage" ColumnName="NombreJoursValiditerAppareillage" />
          <ScalarProperty Name="NombreJoursValiditerPriseEnCharge" ColumnName="NombreJoursValiditerPriseEnCharge" />
          <ScalarProperty Name="NombreJoursValiditerOrdonnance" ColumnName="NombreJoursValiditerOrdonnance" />
          <ScalarProperty Name="GererBon" ColumnName="GererBon" />
          <ScalarProperty Name="Version" ColumnName="Version" />
          <ScalarProperty Name="ActiverBCBDateFin" ColumnName="ActiverBCBDateFin" />
          <ScalarProperty Name="ActiverBCB" ColumnName="ActiverBCB" />
          <ScalarProperty Name="ActiverOMFAPCI" ColumnName="ActiverOMFAPCI" />
          <ScalarProperty Name="ImageCodeABarre" ColumnName="ImageCodeABarre" />
          <ScalarProperty Name="CodeGSU" ColumnName="CodeGSU" />
          <ScalarProperty Name="TauxRemise" ColumnName="TauxRemise" />
          <ScalarProperty Name="Texte" ColumnName="Texte" />
          <ScalarProperty Name="PoliceCaractere" ColumnName="PoliceCaractere" />
          <ScalarProperty Name="TailleCaractere" ColumnName="TailleCaractere" />
          <ScalarProperty Name="TailleListe" ColumnName="TailleListe" />
          <ScalarProperty Name="TailleCodeCNAM" ColumnName="TailleCodeCNAM" />
          <ScalarProperty Name="Latitude_Longitude" ColumnName="Latitude_Longitude" />
          <ScalarProperty Name="NumeroLotProduction" ColumnName="NumeroLotProduction" />
          <ScalarProperty Name="NbreJourValiditeParDefaut" ColumnName="NbreJourValiditeParDefaut" />
          <ScalarProperty Name="AutoriserEnvoiMail" ColumnName="AutoriserEnvoiMail" />
          <ScalarProperty Name="MotDePasseDestinateur" ColumnName="MotDePasseDestinateur" />
          <ScalarProperty Name="TexteMail" ColumnName="TexteMail" />
          <ScalarProperty Name="SujetMail" ColumnName="SujetMail" />
          <ScalarProperty Name="AdresseMailDestinateur" ColumnName="AdresseMailDestinateur" />
          <ScalarProperty Name="PortMail" ColumnName="PortMail" />
          <ScalarProperty Name="SmtpMail" ColumnName="SmtpMail" />
          <ScalarProperty Name="DateMigration" ColumnName="DateMigration" />
          <ScalarProperty Name="DemandeMotDePasse" ColumnName="DemandeMotDePasse" />
          <ScalarProperty Name="Timbre" ColumnName="Timbre" />
          <ScalarProperty Name="Messagederoulant2" ColumnName="Messagederoulant2" />
          <ScalarProperty Name="Messagederoulant1" ColumnName="Messagederoulant1" />
          <ScalarProperty Name="Rib" ColumnName="Rib" />
          <ScalarProperty Name="CodeTVA" ColumnName="CodeTVA" />
          <ScalarProperty Name="Fax" ColumnName="Fax" />
          <ScalarProperty Name="Telephone" ColumnName="Telephone" />
          <ScalarProperty Name="Adresse" ColumnName="Adresse" />
          <ScalarProperty Name="Affiliation2" ColumnName="Affiliation2" />
          <ScalarProperty Name="Affiliation1" ColumnName="Affiliation1" />
          <ScalarProperty Name="NCnam" ColumnName="NCnam" />
          <ScalarProperty Name="Pharmacie" ColumnName="Pharmacie" />
          <ScalarProperty Name="CodePharmacie" ColumnName="CodePharmacie" />
          <ScalarProperty Name="Code" ColumnName="Code" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="MOUVEMENT_ETATS">
      <EntityTypeMapping TypeName="SaleReportModel.MOUVEMENT_ETATS">
        <MappingFragment StoreEntitySet="MOUVEMENT_ETATS">
          <ScalarProperty Name="NumeroLot" ColumnName="NumeroLot" />
          <ScalarProperty Name="Supprimer" ColumnName="Supprimer" />
          <ScalarProperty Name="Recu" ColumnName="Recu" />
          <ScalarProperty Name="Vider" ColumnName="Vider" />
          <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
          <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
          <ScalarProperty Name="DateEcheance" ColumnName="DateEcheance" />
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
          <ScalarProperty Name="MontantRegle" ColumnName="MontantRegle" />
          <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
          <ScalarProperty Name="NumeroReglement" ColumnName="NumeroReglement" />
          <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="PrixAchatTTC" ColumnName="PrixAchatTTC" />
          <ScalarProperty Name="PrixAchatHT" ColumnName="PrixAchatHT" />
          <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
          <ScalarProperty Name="Tva" ColumnName="Tva" />
          <ScalarProperty Name="Exonorertva" ColumnName="Exonorertva" />
          <ScalarProperty Name="Honoraire" ColumnName="Honoraire" />
          <ScalarProperty Name="PrixVenteTTC" ColumnName="PrixVenteTTC" />
          <ScalarProperty Name="PrixVenteHT" ColumnName="PrixVenteHT" />
          <ScalarProperty Name="QuantiteUnitaire" ColumnName="QuantiteUnitaire" />
          <ScalarProperty Name="Quantite" ColumnName="Quantite" />
          <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
          <ScalarProperty Name="CodeCategorie" ColumnName="CodeCategorie" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="TotalAchatTTC" ColumnName="TotalAchatTTC" />
          <ScalarProperty Name="TotalAchatHT" ColumnName="TotalAchatHT" />
          <ScalarProperty Name="CodeFournisseur" ColumnName="CodeFournisseur" />
          <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
          <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
          <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
          <ScalarProperty Name="TotalVenteHT" ColumnName="TotalVenteHT" />
          <ScalarProperty Name="CodeMutuelle" ColumnName="CodeMutuelle" />
          <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
          <ScalarProperty Name="Debit" ColumnName="Debit" />
          <ScalarProperty Name="Credit" ColumnName="Credit" />
          <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="TypeOperation" ColumnName="TypeOperation" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="CAISSE">
      <EntityTypeMapping TypeName="SaleReportModel.CAISSE">
        <MappingFragment StoreEntitySet="CAISSE">
          <ScalarProperty Name="MontantCaisse" ColumnName="MontantCaisse" />
          <ScalarProperty Name="Poste" ColumnName="Poste" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatDesFactures" FunctionName="SaleReportModel.Store.P_Report_EtatDesFactures" />
    <FunctionImportMapping FunctionImportName="P_Report_EtatDetailsTVA" FunctionName="SaleReportModel.Store.P_Report_EtatDetailsTVA">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatDetailsTVA_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="Quantite" ColumnName="Quantite" />
          <ScalarProperty Name="PrixVenteHT" ColumnName="PrixVenteHT" />
          <ScalarProperty Name="TotalVenteHT" ColumnName="TotalVenteHT" />
          <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatValeurTVAVente" FunctionName="SaleReportModel.Store.P_Report_EtatValeurTVAVente">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatValeurTVAVente_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="TVA" ColumnName="TVA" />
          <ScalarProperty Name="Base" ColumnName="Base" />
          <ScalarProperty Name="MontantTVA" ColumnName="MontantTVA" />
          <ScalarProperty Name="Honoraire" ColumnName="Honoraire" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_VentesAnnuelles" FunctionName="SaleReportModel.Store.P_Report_VentesAnnuelles">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_VentesAnnuelles_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="Annee" ColumnName="Annee" />
          <ScalarProperty Name="Espece" ColumnName="Espece" />
          <ScalarProperty Name="Cheque" ColumnName="Cheque" />
          <ScalarProperty Name="Carte" ColumnName="Carte" />
          <ScalarProperty Name="Credit" ColumnName="Credit" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="Autre" ColumnName="Autre" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="ReglementClient" ColumnName="ReglementClient" />
          <ScalarProperty Name="Reglementcnammutuelle" ColumnName="Reglementcnammutuelle" />
          <ScalarProperty Name="Caisse" ColumnName="Caisse" />
          <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
          <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_VentesMensuelles" FunctionName="SaleReportModel.Store.P_Report_VentesMensuelles">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_VentesMensuelles_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="Mois" ColumnName="Mois" />
          <ScalarProperty Name="Espece" ColumnName="Espece" />
          <ScalarProperty Name="Cheque" ColumnName="Cheque" />
          <ScalarProperty Name="Carte" ColumnName="Carte" />
          <ScalarProperty Name="Credit" ColumnName="Credit" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="Autre" ColumnName="Autre" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="ReglementClient" ColumnName="ReglementClient" />
          <ScalarProperty Name="Reglementcnammutuelle" ColumnName="Reglementcnammutuelle" />
          <ScalarProperty Name="Caisse" ColumnName="Caisse" />
          <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
          <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_VentesQuotidienne" FunctionName="SaleReportModel.Store.P_Report_VentesQuotidienne">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_VentesQuotidienne_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="NumeroJour" ColumnName="NumeroJour" />
          <ScalarProperty Name="Espece" ColumnName="Espece" />
          <ScalarProperty Name="Cheque" ColumnName="Cheque" />
          <ScalarProperty Name="Carte" ColumnName="Carte" />
          <ScalarProperty Name="Credit" ColumnName="Credit" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="Autre" ColumnName="Autre" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="ReglementClient" ColumnName="ReglementClient" />
          <ScalarProperty Name="Reglementcnammutuelle" ColumnName="Reglementcnammutuelle" />
          <ScalarProperty Name="Caisse" ColumnName="Caisse" />
          <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
          <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Relever_CNAM_MaladieOrdinaire" FunctionName="SaleReportModel.Store.P_Relever_CNAM_MaladieOrdinaire">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Relever_CNAM_MaladieOrdinaire_Result">
          <ScalarProperty Name="NumeroVente" ColumnName="NumeroVente" />
          <ScalarProperty Name="Ligne" ColumnName="Ligne" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_DetailsTVA" FunctionName="SaleReportModel.Store.P_Report_DetailsTVA">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_DetailsTVA_Result">
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="Quantite" ColumnName="Quantite" />
          <ScalarProperty Name="PrixVenteHT" ColumnName="PrixVenteHT" />
          <ScalarProperty Name="TotalVenteHT" ColumnName="TotalVenteHT" />
          <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_RecapCaisse_Mutuelle" FunctionName="SaleReportModel.Store.P_RecapCaisse_Mutuelle">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_Mutuelle_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="Numero" ColumnName="Numero" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Client" ColumnName="Client" />
          <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="Reglement" ColumnName="Reglement" />
          <ScalarProperty Name="MP" ColumnName="MP" />
          <ScalarProperty Name="Echeance" ColumnName="Echeance" />
          <ScalarProperty Name="Poste" ColumnName="Poste" />
          <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_RecapCaisse_ReglementCreditMutuelle" FunctionName="SaleReportModel.Store.P_RecapCaisse_ReglementCreditMutuelle">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_ReglementCreditMutuelle_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="Numero" ColumnName="Numero" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Client" ColumnName="Client" />
          <ScalarProperty Name="Reglement" ColumnName="Reglement" />
          <ScalarProperty Name="MP" ColumnName="MP" />
          <ScalarProperty Name="Echeance" ColumnName="Echeance" />
          <ScalarProperty Name="Libelle" ColumnName="Libelle" />
          <ScalarProperty Name="Solde" ColumnName="Solde" />
          <ScalarProperty Name="Poste" ColumnName="Poste" />
          <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_RecapCaisse_RemiseRglement" FunctionName="SaleReportModel.Store.P_RecapCaisse_RemiseRglement">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_RemiseRglement_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="Numero" ColumnName="Numero" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Client" ColumnName="Client" />
          <ScalarProperty Name="Reglement" ColumnName="Reglement" />
          <ScalarProperty Name="MP" ColumnName="MP" />
          <ScalarProperty Name="Echeance" ColumnName="Echeance" />
          <ScalarProperty Name="Libelle" ColumnName="Libelle" />
          <ScalarProperty Name="Poste" ColumnName="Poste" />
          <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_RecapCaisse_RemiseVente" FunctionName="SaleReportModel.Store.P_RecapCaisse_RemiseVente">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_RemiseVente_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="Numero" ColumnName="Numero" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Client" ColumnName="Client" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="MP" ColumnName="MP" />
          <ScalarProperty Name="Echeance" ColumnName="Echeance" />
          <ScalarProperty Name="Poste" ColumnName="Poste" />
          <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_RecapCaisse_RetourVente" FunctionName="SaleReportModel.Store.P_RecapCaisse_RetourVente">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_RetourVente_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="Numero" ColumnName="Numero" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Client" ColumnName="Client" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="Reglement" ColumnName="Reglement" />
          <ScalarProperty Name="MP" ColumnName="MP" />
          <ScalarProperty Name="Echeance" ColumnName="Echeance" />
          <ScalarProperty Name="Poste" ColumnName="Poste" />
          <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_RecapCaisse_CNAM" FunctionName="SaleReportModel.Store.P_RecapCaisse_CNAM">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_CNAM_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="Numero" ColumnName="Numero" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Client" ColumnName="Client" />
          <ScalarProperty Name="MontantCNAM" ColumnName="MontantCNAM" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="Reglement" ColumnName="Reglement" />
          <ScalarProperty Name="MP" ColumnName="MP" />
          <ScalarProperty Name="Echeance" ColumnName="Echeance" />
          <ScalarProperty Name="Poste" ColumnName="Poste" />
          <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_RecapCaisse_VenteAuComptant" FunctionName="SaleReportModel.Store.P_RecapCaisse_VenteAuComptant">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_VenteAuComptant_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="Numero" ColumnName="Numero" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Client" ColumnName="Client" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="Reglement" ColumnName="Reglement" />
          <ScalarProperty Name="MP" ColumnName="MP" />
          <ScalarProperty Name="Echeance" ColumnName="Echeance" />
          <ScalarProperty Name="Type" ColumnName="Type" />
          <ScalarProperty Name="Poste" ColumnName="Poste" />
          <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatRecapCaisse" FunctionName="SaleReportModel.Store.P_Report_EtatRecapCaisse">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatRecapCaisse_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
          <ScalarProperty Name="TypeOperation" ColumnName="TypeOperation" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="Quantite" ColumnName="Quantite" />
          <ScalarProperty Name="Tva" ColumnName="Tva" />
          <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
          <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
          <ScalarProperty Name="Credit" ColumnName="Credit" />
          <ScalarProperty Name="Debit" ColumnName="Debit" />
          <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
          <ScalarProperty Name="TotalVenteHT" ColumnName="TotalVenteHT" />
          <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatHitParade" FunctionName="SaleReportModel.Store.P_Report_EtatHitParade">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatHitParade_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="LibelleForme" ColumnName="LibelleForme" />
          <ScalarProperty Name="LibelleCategorie" ColumnName="LibelleCategorie" />
          <ScalarProperty Name="Quantite" ColumnName="Quantite" />
          <ScalarProperty Name="Stock" ColumnName="Stock" />
          <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
          <ScalarProperty Name="Rayon" ColumnName="Rayon" />
          <ScalarProperty Name="nom" ColumnName="nom" />
          <ScalarProperty Name="DatePeremption" ColumnName="DatePeremption" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Etat_ListeDesReleveCNAM" FunctionName="SaleReportModel.Store.P_Etat_ListeDesReleveCNAM">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Etat_ListeDesReleveCNAM_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="NumeroReleve" ColumnName="NumeroReleve" />
          <ScalarProperty Name="Type" ColumnName="Type" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="DateDebut" ColumnName="DateDebut" />
          <ScalarProperty Name="DateFin" ColumnName="DateFin" />
          <ScalarProperty Name="MontantARembourser" ColumnName="MontantARembourser" />
          <ScalarProperty Name="Reste" ColumnName="Reste" />
          <ScalarProperty Name="MontantTotal" ColumnName="MontantTotal" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <EntitySetMapping Name="V_Report_EtatDetailDesVentes">
      <EntityTypeMapping TypeName="SaleReportModel.V_Report_EtatDetailDesVentes">
        <MappingFragment StoreEntitySet="V_Report_EtatDetailDesVentes">
          <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
          <ScalarProperty Name="CodeCategorie" ColumnName="CodeCategorie" />
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
          <ScalarProperty Name="TotalVenteHT" ColumnName="TotalVenteHT" />
          <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
          <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="TotalCNAM" ColumnName="TotalCNAM" />
          <ScalarProperty Name="TotalMutuelle" ColumnName="TotalMutuelle" />
          <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
          <ScalarProperty Name="Tva" ColumnName="Tva" />
          <ScalarProperty Name="Quantite" ColumnName="Quantite" />
          <ScalarProperty Name="Nom" ColumnName="Nom" />
          <ScalarProperty Name="LibelleCategorie" ColumnName="LibelleCategorie" />
          <ScalarProperty Name="LibelleForme" ColumnName="LibelleForme" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="V_Report_EtatDesVentes">
      <EntityTypeMapping TypeName="SaleReportModel.V_Report_EtatDesVentes">
        <MappingFragment StoreEntitySet="V_Report_EtatDesVentes">
          <ScalarProperty Name="Marge" ColumnName="Marge" />
          <ScalarProperty Name="Debit" ColumnName="Debit" />
          <ScalarProperty Name="Credit" ColumnName="Credit" />
          <ScalarProperty Name="Vider" ColumnName="Vider" />
          <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
          <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
          <ScalarProperty Name="Mutuelle" ColumnName="Mutuelle" />
          <ScalarProperty Name="CodeMutuelle" ColumnName="CodeMutuelle" />
          <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
          <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
          <ScalarProperty Name="TypeOperation" ColumnName="TypeOperation" />
          <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
          <ScalarProperty Name="NomUtilisateur" ColumnName="NomUtilisateur" />
          <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
          <ScalarProperty Name="MP" ColumnName="MP" />
          <ScalarProperty Name="Nom" ColumnName="Nom" />
          <ScalarProperty Name="Type" ColumnName="Type" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="V_Report_EtatHitParade">
      <EntityTypeMapping TypeName="SaleReportModel.V_Report_EtatHitParade">
        <MappingFragment StoreEntitySet="V_Report_EtatHitParade">
          <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
          <ScalarProperty Name="CodeCategorie" ColumnName="CodeCategorie" />
          <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
          <ScalarProperty Name="CodeLabo" ColumnName="CodeLabo" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="DatePeremption" ColumnName="DatePeremption" />
          <ScalarProperty Name="nom" ColumnName="nom" />
          <ScalarProperty Name="Rayon" ColumnName="Rayon" />
          <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
          <ScalarProperty Name="Stock" ColumnName="Stock" />
          <ScalarProperty Name="Quantite" ColumnName="Quantite" />
          <ScalarProperty Name="LibelleCategorie" ColumnName="LibelleCategorie" />
          <ScalarProperty Name="LibelleForme" ColumnName="LibelleForme" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatJournalDesVentes" FunctionName="SaleReportModel.Store.P_Report_EtatJournalDesVentes">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatJournalDesVentes_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Exonore" ColumnName="Exonore" />
          <ScalarProperty Name="BaseTVA6" ColumnName="BaseTVA6" />
          <ScalarProperty Name="TVA6" ColumnName="TVA6" />
          <ScalarProperty Name="BaseTVA12" ColumnName="BaseTVA12" />
          <ScalarProperty Name="TVA12" ColumnName="TVA12" />
          <ScalarProperty Name="BaseTVA18" ColumnName="BaseTVA18" />
          <ScalarProperty Name="TVA18" ColumnName="TVA18" />
          <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
          <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="HR" ColumnName="HR" />
          <ScalarProperty Name="Nom" ColumnName="Nom" />
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <EntitySetMapping Name="V_Report_EtatJournalDesVentes">
      <EntityTypeMapping TypeName="SaleReportModel.V_Report_EtatJournalDesVentes">
        <MappingFragment StoreEntitySet="V_Report_EtatJournalDesVentes">
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
          <ScalarProperty Name="Nom" ColumnName="Nom" />
          <ScalarProperty Name="HR" ColumnName="HR" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
          <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
          <ScalarProperty Name="TVA18" ColumnName="TVA18" />
          <ScalarProperty Name="BaseTVA18" ColumnName="BaseTVA18" />
          <ScalarProperty Name="TVA12" ColumnName="TVA12" />
          <ScalarProperty Name="BaseTVA12" ColumnName="BaseTVA12" />
          <ScalarProperty Name="TVA6" ColumnName="TVA6" />
          <ScalarProperty Name="BaseTVA6" ColumnName="BaseTVA6" />
          <ScalarProperty Name="Exonore" ColumnName="Exonore" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="V_Report_EtatJournalDesVentesDetaillee">
      <EntityTypeMapping TypeName="SaleReportModel.V_Report_EtatJournalDesVentesDetaillee">
        <MappingFragment StoreEntitySet="V_Report_EtatJournalDesVentesDetaillee">
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
          <ScalarProperty Name="Nom" ColumnName="Nom" />
          <ScalarProperty Name="HR" ColumnName="HR" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
          <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
          <ScalarProperty Name="TVA18" ColumnName="TVA18" />
          <ScalarProperty Name="BaseTVA18" ColumnName="BaseTVA18" />
          <ScalarProperty Name="TVA12" ColumnName="TVA12" />
          <ScalarProperty Name="BaseTVA12" ColumnName="BaseTVA12" />
          <ScalarProperty Name="TVA6" ColumnName="TVA6" />
          <ScalarProperty Name="BaseTVA6" ColumnName="BaseTVA6" />
          <ScalarProperty Name="Exonore" ColumnName="Exonore" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatStatistiqueFournisseurs" FunctionName="SaleReportModel.Store.P_Report_EtatStatistiqueFournisseurs">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatStatistiqueFournisseurs_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="NomFournisseur" ColumnName="NomFournisseur" />
          <ScalarProperty Name="HT" ColumnName="HT" />
          <ScalarProperty Name="TVA" ColumnName="TVA" />
          <ScalarProperty Name="TTC" ColumnName="TTC" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="VenteTTC" ColumnName="VenteTTC" />
          <ScalarProperty Name="ResteAPayer" ColumnName="ResteAPayer" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatJournalDesAchats" FunctionName="SaleReportModel.Store.P_Report_EtatJournalDesAchats">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatJournalDesAchats_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="NumeroAchat" ColumnName="NumeroAchat" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="DateBlFacture" ColumnName="DateBlFacture" />
          <ScalarProperty Name="NomFournisseur" ColumnName="NomFournisseur" />
          <ScalarProperty Name="NumeroBL_Facture" ColumnName="NumeroBL_Facture" />
          <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
          <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
          <ScalarProperty Name="TVA" ColumnName="TVA" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="CodeFournisseur" ColumnName="CodeFournisseur" />
          <ScalarProperty Name="ValeurVenteTTC" ColumnName="ValeurVenteTTC" />
          <ScalarProperty Name="TotalTVAVente" ColumnName="TotalTVAVente" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatDesVentes" FunctionName="SaleReportModel.Store.P_Report_EtatDesVentes">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatDesVentes_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Type" ColumnName="Type" />
          <ScalarProperty Name="Nom" ColumnName="Nom" />
          <ScalarProperty Name="MP" ColumnName="MP" />
          <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
          <ScalarProperty Name="NomUtilisateur" ColumnName="NomUtilisateur" />
          <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
          <ScalarProperty Name="TypeOperation" ColumnName="TypeOperation" />
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
          <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
          <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
          <ScalarProperty Name="CodeMutuelle" ColumnName="CodeMutuelle" />
          <ScalarProperty Name="Mutuelle" ColumnName="Mutuelle" />
          <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
          <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
          <ScalarProperty Name="Vider" ColumnName="Vider" />
          <ScalarProperty Name="Credit" ColumnName="Credit" />
          <ScalarProperty Name="Debit" ColumnName="Debit" />
          <ScalarProperty Name="Marge" ColumnName="Marge" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatDetailDesVentes" FunctionName="SaleReportModel.Store.P_Report_EtatDetailDesVentes">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatDetailDesVentes_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="LibelleForme" ColumnName="LibelleForme" />
          <ScalarProperty Name="LibelleCategorie" ColumnName="LibelleCategorie" />
          <ScalarProperty Name="Nom" ColumnName="Nom" />
          <ScalarProperty Name="Quantite" ColumnName="Quantite" />
          <ScalarProperty Name="Tva" ColumnName="Tva" />
          <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
          <ScalarProperty Name="TotalMutuelle" ColumnName="TotalMutuelle" />
          <ScalarProperty Name="TotalCNAM" ColumnName="TotalCNAM" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
          <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
          <ScalarProperty Name="TotalVenteHT" ColumnName="TotalVenteHT" />
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
          <ScalarProperty Name="CodeCategorie" ColumnName="CodeCategorie" />
          <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
          <ScalarProperty Name="PrixVenteHT" ColumnName="PrixVenteHT" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatJournalReleveMutuelle" FunctionName="SaleReportModel.Store.P_Report_EtatJournalReleveMutuelle">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatJournalReleveMutuelle_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="NumeroReleve" ColumnName="NumeroReleve" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="DateDebut" ColumnName="DateDebut" />
          <ScalarProperty Name="DateFin" ColumnName="DateFin" />
          <ScalarProperty Name="MontantTotal" ColumnName="MontantTotal" />
          <ScalarProperty Name="MontantRegle" ColumnName="MontantRegle" />
          <ScalarProperty Name="Reste" ColumnName="Reste" />
          <ScalarProperty Name="CodeMutuelle" ColumnName="CodeMutuelle" />
          <ScalarProperty Name="NomMutuelle" ColumnName="NomMutuelle" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatFacureJournaliere" FunctionName="SaleReportModel.Store.P_Report_EtatFacureJournaliere">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatFacureJournaliere_Result">
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
          <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="Caisse" ColumnName="Caisse" />
          <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
          <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatStockParCategorieIntervalleMarge" FunctionName="SaleReportModel.Store.P_Report_EtatStockParCategorieIntervalleMarge">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatStockParCategorieIntervalleMarge_Result">
          <ScalarProperty Name="Marge" ColumnName="Marge" />
          <ScalarProperty Name="ValeurAchatHT" ColumnName="ValeurAchatHT" />
          <ScalarProperty Name="ValeurAchatHTP" ColumnName="ValeurAchatHTP" />
          <ScalarProperty Name="ValeurVenteHT" ColumnName="ValeurVenteHT" />
          <ScalarProperty Name="ValeurVenteHTP" ColumnName="ValeurVenteHTP" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatListeDesMedecins" FunctionName="SaleReportModel.Store.P_Report_EtatListeDesMedecins">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatListeDesMedecins_Result">
          <ScalarProperty Name="CodeMedecin" ColumnName="CodeMedecin" />
          <ScalarProperty Name="NomMedecin" ColumnName="NomMedecin" />
          <ScalarProperty Name="LibelleSpecialite" ColumnName="LibelleSpecialite" />
          <ScalarProperty Name="CodeSpecialite" ColumnName="CodeSpecialite" />
          <ScalarProperty Name="Adresse" ColumnName="Adresse" />
          <ScalarProperty Name="Tel" ColumnName="Tel" />
          <ScalarProperty Name="Fax" ColumnName="Fax" />
          <ScalarProperty Name="Email" ColumnName="Email" />
          <ScalarProperty Name="IdentifiantCNAM" ColumnName="IdentifiantCNAM" />
          <ScalarProperty Name="NomVille" ColumnName="NomVille" />
          <ScalarProperty Name="CodeVille" ColumnName="CodeVille" />
          <ScalarProperty Name="bloquer" ColumnName="bloquer" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatDetailsCaisse" FunctionName="SaleReportModel.Store.P_Report_EtatDetailsCaisse">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatDetailsCaisse_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="Operation" ColumnName="Operation" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Nom" ColumnName="Nom" />
          <ScalarProperty Name="Reglement" ColumnName="Reglement" />
          <ScalarProperty Name="NatureReglement" ColumnName="NatureReglement" />
          <ScalarProperty Name="DateEcheance" ColumnName="DateEcheance" />
          <ScalarProperty Name="Vendeur" ColumnName="Vendeur" />
          <ScalarProperty Name="Poste" ColumnName="Poste" />
          <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
          <ScalarProperty Name="Type" ColumnName="Type" />
          <ScalarProperty Name="TypeOperation" ColumnName="TypeOperation" />
          <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
          <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
          <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
          <ScalarProperty Name="Credit" ColumnName="Credit" />
          <ScalarProperty Name="Debit" ColumnName="Debit" />
          <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
          <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatOrdonnancier" FunctionName="SaleReportModel.Store.P_Report_EtatOrdonnancier">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatOrdonnancier_Result">
          <ScalarProperty Name="DateVente" ColumnName="DateVente" />
          <ScalarProperty Name="DateOrdonnancier" ColumnName="DateOrdonnancier" />
          <ScalarProperty Name="NomMedecin" ColumnName="NomMedecin" />
          <ScalarProperty Name="Numero" ColumnName="Numero" />
          <ScalarProperty Name="NumeroOrdonnacier" ColumnName="NumeroOrdonnacier" />
          <ScalarProperty Name="LibelleForme" ColumnName="LibelleForme" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="Quantite" ColumnName="Quantite" />
          <ScalarProperty Name="Malade" ColumnName="Malade" />
          <ScalarProperty Name="CIN" ColumnName="CIN" />
          <ScalarProperty Name="Adresse" ColumnName="Adresse" />
          <ScalarProperty Name="TypeOrdonnance" ColumnName="TypeOrdonnance" />
          <ScalarProperty Name="NumeroVente" ColumnName="NumeroVente" />
          <ScalarProperty Name="Supprimer" ColumnName="Supprimer" />
          <ScalarProperty Name="Row" ColumnName="Row" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="P_RecapCaisse_Credit" FunctionName="SaleReportModel.Store.P_RecapCaisse_Credit">
      <ResultMapping>
        <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_Credit_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="Numero" ColumnName="Numero" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Client" ColumnName="Client" />
          <ScalarProperty Name="Solde" ColumnName="Solde" />
          <ScalarProperty Name="MontantCNAM" ColumnName="MontantCNAM" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="Reglement" ColumnName="Reglement" />
          <ScalarProperty Name="Credit" ColumnName="Credit" />
          <ScalarProperty Name="MP" ColumnName="MP" />
          <ScalarProperty Name="Echeance" ColumnName="Echeance" />
          <ScalarProperty Name="Poste" ColumnName="Poste" />
          <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
  </EntityContainerMapping>
</Mapping>