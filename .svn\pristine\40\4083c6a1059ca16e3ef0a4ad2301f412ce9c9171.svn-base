﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:cl="http://schemas.microsoft.com/sqlserver/reporting/2010/01/componentdefinition" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2010/01/reportdefinition">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="pharma">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString>Data Source=MARWEN\SQLSRV;Initial Catalog=pharma</ConnectString>
        <Prompt>Spécifiez un nom d'utilisateur et un mot de passe pour la source de données pharma :</Prompt>
      </ConnectionProperties>
      <rd:SecurityType>DataBase</rd:SecurityType>
      <rd:DataSourceID>571c9c0d-6679-4707-aecd-de9007a68d08</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DS_Pharmacie">
      <Query>
        <DataSourceName>pharma</DataSourceName>
        <CommandText>SELECT
  PARAMETRE_PHARMACIE.Code
  ,PARAMETRE_PHARMACIE.CodePharmacie
  ,PARAMETRE_PHARMACIE.Pharmacie
  ,PARAMETRE_PHARMACIE.NCnam
  ,PARAMETRE_PHARMACIE.Affiliation1
  ,PARAMETRE_PHARMACIE.Affiliation2
  ,PARAMETRE_PHARMACIE.Adresse
  ,PARAMETRE_PHARMACIE.Telephone
  ,PARAMETRE_PHARMACIE.Fax
  ,PARAMETRE_PHARMACIE.CodeTVA
  ,PARAMETRE_PHARMACIE.Rib
  ,PARAMETRE_PHARMACIE.Messagederoulant1
  ,PARAMETRE_PHARMACIE.Messagederoulant2
  ,PARAMETRE_PHARMACIE.Timbre
  ,PARAMETRE_PHARMACIE.DemandeMotDePasse
  ,PARAMETRE_PHARMACIE.DateMigration
  ,PARAMETRE_PHARMACIE.SmtpMail
  ,PARAMETRE_PHARMACIE.PortMail
  ,PARAMETRE_PHARMACIE.AdresseMailDestinateur
  ,PARAMETRE_PHARMACIE.SujetMail
  ,PARAMETRE_PHARMACIE.TexteMail
  ,PARAMETRE_PHARMACIE.MotDePasseDestinateur
  ,PARAMETRE_PHARMACIE.AutoriserEnvoiMail
  ,PARAMETRE_PHARMACIE.NbreJourValiditeParDefaut
  ,PARAMETRE_PHARMACIE.NumeroLotProduction
  ,PARAMETRE_PHARMACIE.Latitude_Longitude
  ,PARAMETRE_PHARMACIE.TailleCodeCNAM
  ,PARAMETRE_PHARMACIE.TailleListe
  ,PARAMETRE_PHARMACIE.TailleCaractere
  ,PARAMETRE_PHARMACIE.PoliceCaractere
  ,PARAMETRE_PHARMACIE.Texte
  ,PARAMETRE_PHARMACIE.TauxRemise
  ,PARAMETRE_PHARMACIE.CodeGSU
  ,PARAMETRE_PHARMACIE.ImageCodeABarre
  ,PARAMETRE_PHARMACIE.ActiverOMFAPCI
FROM
  PARAMETRE_PHARMACIE</CommandText>
        <rd:DesignerState><QueryDefinition xmlns="http://schemas.microsoft.com/ReportingServices/QueryDefinition/Relational"><AutoJoin>False</AutoJoin><SelectedColumns><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="Code" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="CodePharmacie" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="Pharmacie" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="NCnam" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="Affiliation1" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="Affiliation2" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="Adresse" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="Telephone" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="Fax" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="CodeTVA" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="Rib" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="Messagederoulant1" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="Messagederoulant2" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="Timbre" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="DemandeMotDePasse" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="DateMigration" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="SmtpMail" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="PortMail" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="AdresseMailDestinateur" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="SujetMail" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="TexteMail" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="MotDePasseDestinateur" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="AutoriserEnvoiMail" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="NbreJourValiditeParDefaut" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="NumeroLotProduction" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="Latitude_Longitude" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="TailleCodeCNAM" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="TailleListe" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="TailleCaractere" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="PoliceCaractere" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="Texte" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="TauxRemise" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="CodeGSU" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="ImageCodeABarre" /><ColumnExpression ColumnOwner="PARAMETRE_PHARMACIE" ColumnName="ActiverOMFAPCI" /></SelectedColumns></QueryDefinition></rd:DesignerState>
      </Query>
      <Fields>
        <Field Name="Code">
          <DataField>Code</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Pharmacie">
          <DataField>Pharmacie</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Telephone">
          <DataField>Telephone</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Fax">
          <DataField>Fax</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NCnam">
          <DataField>NCnam</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Affiliation1">
          <DataField>Affiliation1</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Affiliation2">
          <DataField>Affiliation2</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Adresse">
          <DataField>Adresse</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CodeTVA">
          <DataField>CodeTVA</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CodePharmacie">
          <DataField>CodePharmacie</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Rib">
          <DataField>Rib</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Messagederoulant1">
          <DataField>Messagederoulant1</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Messagederoulant2">
          <DataField>Messagederoulant2</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Timbre">
          <DataField>Timbre</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="DemandeMotDePasse">
          <DataField>DemandeMotDePasse</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="DateMigration">
          <DataField>DateMigration</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="SmtpMail">
          <DataField>SmtpMail</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PortMail">
          <DataField>PortMail</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="AdresseMailDestinateur">
          <DataField>AdresseMailDestinateur</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SujetMail">
          <DataField>SujetMail</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TexteMail">
          <DataField>TexteMail</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MotDePasseDestinateur">
          <DataField>MotDePasseDestinateur</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="AutoriserEnvoiMail">
          <DataField>AutoriserEnvoiMail</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="NbreJourValiditeParDefaut">
          <DataField>NbreJourValiditeParDefaut</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="NumeroLotProduction">
          <DataField>NumeroLotProduction</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Latitude_Longitude">
          <DataField>Latitude_Longitude</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TailleCodeCNAM">
          <DataField>TailleCodeCNAM</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TailleListe">
          <DataField>TailleListe</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TailleCaractere">
          <DataField>TailleCaractere</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PoliceCaractere">
          <DataField>PoliceCaractere</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Texte">
          <DataField>Texte</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TauxRemise">
          <DataField>TauxRemise</DataField>
          <rd:TypeName>System.Int16</rd:TypeName>
        </Field>
        <Field Name="CodeGSU">
          <DataField>CodeGSU</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ImageCodeABarre">
          <DataField>ImageCodeABarre</DataField>
          <rd:TypeName>System.Byte[]</rd:TypeName>
        </Field>
        <Field Name="ActiverOMFAPCI">
          <DataField>ActiverOMFAPCI</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="DS_Detailscaissse">
      <Query>
        <DataSourceName>pharma</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@DateDebut">
            <Value>=Parameters!DateDebut.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@Vide">
            <Value>=Parameters!Vide.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@DateFin">
            <Value>=Parameters!DateFin.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@ModePaiement">
            <Value>=Parameters!ModePaiement.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@Type">
            <Value>=Parameters!Type.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@CodePersonnel">
            <Value>=Parameters!CodePersonnel.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@Poste">
            <Value>=Parameters!Poste.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandType>StoredProcedure</CommandType>
        <CommandText>P_Report_EtatDetailsCaisse</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="Id">
          <DataField>Id</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="CodeNatureReglement">
          <DataField>CodeNatureReglement</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="Type">
          <DataField>Type</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Date">
          <DataField>Date</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="Operation">
          <DataField>Operation</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TypeOperation">
          <DataField>TypeOperation</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NatureReglement">
          <DataField>NatureReglement</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CodeClient">
          <DataField>CodeClient</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Nom">
          <DataField>Nom</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TotalVenteTTC">
          <DataField>TotalVenteTTC</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="Credit">
          <DataField>Credit</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="Reglement">
          <DataField>Reglement</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="Remise">
          <DataField>Remise</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="DateEcheance">
          <DataField>DateEcheance</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="CodePersonnel">
          <DataField>CodePersonnel</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Vendeur">
          <DataField>Vendeur</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Poste">
          <DataField>Poste</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NumeroOperation">
          <DataField>NumeroOperation</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Debit">
          <DataField>Debit</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="MontantCnam">
          <DataField>MontantCnam</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="MontantMutuelle">
          <DataField>MontantMutuelle</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Textbox Name="ReportTitle">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ETAT DETAILS CAISSE</Value>
                    <Style>
                      <FontFamily>Verdana</FontFamily>
                      <FontSize>20pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:WatermarkTextbox>Title</rd:WatermarkTextbox>
            <rd:DefaultName>ReportTitle</rd:DefaultName>
            <Top>1.18534mm</Top>
            <Left>7.90704cm</Left>
            <Height>9.10167mm</Height>
            <Width>116.09604mm</Width>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </TopBorder>
              <BottomBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </BottomBorder>
              <LeftBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </RightBorder>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Rectangle Name="Rectangle1">
            <ReportItems>
              <Textbox Name="DateDebut">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!DateDebut.Value</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>DateDebut</rd:DefaultName>
                <Top>0.93133mm</Top>
                <Left>30.80834mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox2">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Période du :</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>0.93133mm</Top>
                <Left>3.175mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox4">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Au :</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox4</rd:DefaultName>
                <Top>0.93133mm</Top>
                <Left>57.57222mm</Left>
                <Height>6mm</Height>
                <Width>10.95835mm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="DateFin">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!DateFin.Value</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>DateFin</rd:DefaultName>
                <Top>0.93133mm</Top>
                <Left>70.29446mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox3">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Type :</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>8.34244mm</Top>
                <Left>3.175mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <ZIndex>4</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox6">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Réglement :</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>15.75355mm</Top>
                <Left>3.175mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <ZIndex>5</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>11.69812mm</Top>
            <Left>88.28103mm</Left>
            <Height>22.99931mm</Height>
            <Width>100.53541mm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
            </Style>
          </Rectangle>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>28.12267mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>34.97766mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>44.98629mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>19.24783mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>21.33783mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>19.75905mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>22.1828mm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox37">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Type</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox37</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox9">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Date</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox9</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox11">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Client</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox11</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox13">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Crédit</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox13</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox15">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Mode de payement</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox15</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox17">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Echéance</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox17</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox19">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Vendeur</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox19</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TypeOperation">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TypeOperation.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>TypeOperation</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Date">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Date.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Date</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Nom">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Nom.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Nom</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Credit">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Credit.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Credit</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Type1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Type.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Type1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DateEcheance">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DateEcheance.Value</Value>
                                  <Style>
                                    <Format>d</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>DateEcheance</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Vendeur">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Vendeur.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Vendeur</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Détails" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DS_Detailscaissse</DataSetName>
            <Top>36.46132mm</Top>
            <Left>4.55231mm</Left>
            <Height>12mm</Height>
            <Width>190.61413mm</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </TopBorder>
              <BottomBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </BottomBorder>
              <LeftBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </RightBorder>
            </Style>
          </Tablix>
          <Rectangle Name="Rectangle3">
            <ReportItems>
              <Textbox Name="Textbox14">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>PHARMACIE</Value>
                        <Style>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>1.41111mm</Top>
                <Left>20.90208mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox16">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Code TVA :</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>25.72659mm</Top>
                <Left>3.175mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox20">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Téléphone :</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox2</rd:DefaultName>
                <Top>18.31548mm</Top>
                <Left>3.175mm</Left>
                <Height>6mm</Height>
                <Width>25mm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Pharmacie">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=First(Fields!Pharmacie.Value, "DS_Pharmacie")</Value>
                        <Style>
                          <FontSize>14pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Pharmacie</rd:DefaultName>
                <Top>7.81756mm</Top>
                <Left>3.31041mm</Left>
                <Height>9.43959mm</Height>
                <Width>64.6875mm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Telephone">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=First(Fields!Telephone.Value, "DS_Pharmacie")</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Telephone</rd:DefaultName>
                <Top>18.31548mm</Top>
                <Left>32.67916mm</Left>
                <Height>6mm</Height>
                <Width>35.31875mm</Width>
                <ZIndex>4</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="CodeTVA">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=First(Fields!CodeTVA.Value, "DS_Pharmacie")</Value>
                        <Style />
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>CodeTVA</rd:DefaultName>
                <Top>26.07937mm</Top>
                <Left>32.67916mm</Left>
                <Height>6mm</Height>
                <Width>35.31875mm</Width>
                <ZIndex>5</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>1.12493mm</Top>
            <Left>4.55231mm</Left>
            <Height>33.5725mm</Height>
            <Width>72.75416mm</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
            </Style>
          </Rectangle>
          <Tablix Name="Tablix2">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>25.9283mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>22.95579mm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>30.34708mm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>7.19062mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Id">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum( iif(Fields!CodeNatureReglement.Value ="1", Fields!Credit.Value, CDec(0)))</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Id</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox32">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>  Autre:</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox32</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox34">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum( iif(Fields!CodeNatureReglement.Value &lt;&gt;"1" and Fields!CodeNatureReglement.Value &lt;&gt;"2" and Fields!CodeNatureReglement.Value &lt;&gt;"4", Fields!Credit.Value, CDec(0)))</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox34</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox36">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum( iif(Fields!CodeNatureReglement.Value ="2", Fields!Credit.Value, CDec(0)))</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox36</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox38">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>  Remise:</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox38</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Remise">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum(Fields!Remise.Value)</Value>
                                  <Style>
                                    <Format>#,0.000;-#,0.000;'-'</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Remise</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>6mm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox41">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum( iif(Fields!CodeNatureReglement.Value ="4", Fields!Credit.Value, CDec(0)))</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox41</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <RightBorder>
                              <Color>Black</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox42">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>  Total:</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox42</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox43">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum( Fields!Credit.Value)</Value>
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox43</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="RowGroup">
                    <GroupExpressions>
                      <GroupExpression />
                    </GroupExpressions>
                  </Group>
                  <TablixHeader>
                    <Size>18.53697mm</Size>
                    <CellContents>
                      <Textbox Name="Textbox24">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Espèce:</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox24</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>18.53697mm</Size>
                    <CellContents>
                      <Textbox Name="Textbox35">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Cheque</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox35</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>18.53697mm</Size>
                    <CellContents>
                      <Textbox Name="Textbox40">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>Carte:</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox40</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DS_Detailscaissse</DataSetName>
            <Top>49.16688mm</Top>
            <Left>97.3983mm</Left>
            <Height>19.19062mm</Height>
            <Width>97.76814mm</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </TopBorder>
              <BottomBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </BottomBorder>
              <LeftBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </RightBorder>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>69.68284mm</Height>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Body>
      <Width>195.16644mm</Width>
      <Page>
        <PageHeader>
          <Height>4.49792mm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageFooter>
          <Height>7.19667mm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="ExecutionTime">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Globals!ExecutionTime</Value>
                      <Style />
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>ExecutionTime</rd:DefaultName>
              <Top>0mm</Top>
              <Left>7.72731mm</Left>
              <Height>6.35mm</Height>
              <Width>50.8mm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageFooter>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>21cm</PageWidth>
        <LeftMargin>0.5cm</LeftMargin>
        <RightMargin>0.5cm</RightMargin>
        <TopMargin>0.5cm</TopMargin>
        <BottomMargin>0.5cm</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="DateDebut">
      <DataType>DateTime</DataType>
      <Nullable>true</Nullable>
      <Prompt>Date Debut</Prompt>
    </ReportParameter>
    <ReportParameter Name="Vide">
      <DataType>Boolean</DataType>
      <Nullable>true</Nullable>
      <Prompt>Vide</Prompt>
    </ReportParameter>
    <ReportParameter Name="DateFin">
      <DataType>DateTime</DataType>
      <Nullable>true</Nullable>
      <Prompt>Date Fin</Prompt>
    </ReportParameter>
    <ReportParameter Name="ModePaiement">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>Mode Paiement</Prompt>
    </ReportParameter>
    <ReportParameter Name="Type">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Type</Prompt>
    </ReportParameter>
    <ReportParameter Name="CodePersonnel">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>Code Personnel</Prompt>
    </ReportParameter>
    <ReportParameter Name="Poste">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <Prompt>Poste</Prompt>
    </ReportParameter>
  </ReportParameters>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>53014682-0b5b-4e63-901d-99a3ea010ba1</rd:ReportID>
</Report>