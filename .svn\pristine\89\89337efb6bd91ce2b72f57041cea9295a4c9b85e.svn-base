<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="ServiceOne" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://www.nextsoftware.com.tn:8080/ServiceOne.svc?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://www.nextsoftware.com.tn:8080/ServiceOne.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://www.nextsoftware.com.tn:8080/ServiceOne.svc?xsd=xsd2" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IServiceOne_RechercheOneKey_InputMessage">
    <wsdl:part name="parameters" element="tns:RechercheOneKey" />
  </wsdl:message>
  <wsdl:message name="IServiceOne_RechercheOneKey_OutputMessage">
    <wsdl:part name="parameters" element="tns:RechercheOneKeyResponse" />
  </wsdl:message>
  <wsdl:message name="IServiceOne_LOG_InputMessage">
    <wsdl:part name="parameters" element="tns:LOG" />
  </wsdl:message>
  <wsdl:message name="IServiceOne_LOG_OutputMessage">
    <wsdl:part name="parameters" element="tns:LOGResponse" />
  </wsdl:message>
  <wsdl:message name="IServiceOne_RechercheClient_InputMessage">
    <wsdl:part name="parameters" element="tns:RechercheClient" />
  </wsdl:message>
  <wsdl:message name="IServiceOne_RechercheClient_OutputMessage">
    <wsdl:part name="parameters" element="tns:RechercheClientResponse" />
  </wsdl:message>
  <wsdl:message name="IServiceOne_ListTypeEtablissement_InputMessage">
    <wsdl:part name="parameters" element="tns:ListTypeEtablissement" />
  </wsdl:message>
  <wsdl:message name="IServiceOne_ListTypeEtablissement_OutputMessage">
    <wsdl:part name="parameters" element="tns:ListTypeEtablissementResponse" />
  </wsdl:message>
  <wsdl:message name="IServiceOne_ListeSpecialte_InputMessage">
    <wsdl:part name="parameters" element="tns:ListeSpecialte" />
  </wsdl:message>
  <wsdl:message name="IServiceOne_ListeSpecialte_OutputMessage">
    <wsdl:part name="parameters" element="tns:ListeSpecialteResponse" />
  </wsdl:message>
  <wsdl:message name="IServiceOne_ListeVille_InputMessage">
    <wsdl:part name="parameters" element="tns:ListeVille" />
  </wsdl:message>
  <wsdl:message name="IServiceOne_ListeVille_OutputMessage">
    <wsdl:part name="parameters" element="tns:ListeVilleResponse" />
  </wsdl:message>
  <wsdl:portType name="IServiceOne">
    <wsdl:operation name="RechercheOneKey">
      <wsdl:input wsaw:Action="http://tempuri.org/IServiceOne/RechercheOneKey" message="tns:IServiceOne_RechercheOneKey_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IServiceOne/RechercheOneKeyResponse" message="tns:IServiceOne_RechercheOneKey_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LOG">
      <wsdl:input wsaw:Action="http://tempuri.org/IServiceOne/LOG" message="tns:IServiceOne_LOG_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IServiceOne/LOGResponse" message="tns:IServiceOne_LOG_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="RechercheClient">
      <wsdl:input wsaw:Action="http://tempuri.org/IServiceOne/RechercheClient" message="tns:IServiceOne_RechercheClient_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IServiceOne/RechercheClientResponse" message="tns:IServiceOne_RechercheClient_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ListTypeEtablissement">
      <wsdl:input wsaw:Action="http://tempuri.org/IServiceOne/ListTypeEtablissement" message="tns:IServiceOne_ListTypeEtablissement_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IServiceOne/ListTypeEtablissementResponse" message="tns:IServiceOne_ListTypeEtablissement_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ListeSpecialte">
      <wsdl:input wsaw:Action="http://tempuri.org/IServiceOne/ListeSpecialte" message="tns:IServiceOne_ListeSpecialte_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IServiceOne/ListeSpecialteResponse" message="tns:IServiceOne_ListeSpecialte_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ListeVille">
      <wsdl:input wsaw:Action="http://tempuri.org/IServiceOne/ListeVille" message="tns:IServiceOne_ListeVille_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IServiceOne/ListeVilleResponse" message="tns:IServiceOne_ListeVille_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IServiceOne" type="tns:IServiceOne">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="RechercheOneKey">
      <soap:operation soapAction="http://tempuri.org/IServiceOne/RechercheOneKey" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LOG">
      <soap:operation soapAction="http://tempuri.org/IServiceOne/LOG" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RechercheClient">
      <soap:operation soapAction="http://tempuri.org/IServiceOne/RechercheClient" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListTypeEtablissement">
      <soap:operation soapAction="http://tempuri.org/IServiceOne/ListTypeEtablissement" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListeSpecialte">
      <soap:operation soapAction="http://tempuri.org/IServiceOne/ListeSpecialte" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListeVille">
      <soap:operation soapAction="http://tempuri.org/IServiceOne/ListeVille" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="ServiceOne">
    <wsdl:port name="BasicHttpBinding_IServiceOne" binding="tns:BasicHttpBinding_IServiceOne">
      <soap:address location="http://www.nextsoftware.com.tn:8080/ServiceOne.svc/ServiceOneKey" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>