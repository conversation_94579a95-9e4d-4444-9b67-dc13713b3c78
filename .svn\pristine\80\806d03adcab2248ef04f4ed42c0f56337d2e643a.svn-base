//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class VENTE_SUPPRIME
    {
        public VENTE_SUPPRIME()
        {
            this.VENTE_SUPPRIME_DETAILS = new HashSet<VENTE_SUPPRIME_DETAILS>();
        }
    
        public string NumeroVenteSupprime { get; set; }
        public string NumeroVente { get; set; }
        public System.DateTime Date { get; set; }
        public decimal TotalHT { get; set; }
        public decimal TotalTTC { get; set; }
        public decimal TVA { get; set; }
        public decimal TotalRemise { get; set; }
        public decimal Timbre { get; set; }
        public string CodeClient { get; set; }
        public int CodePersonnel { get; set; }
        public Nullable<int> CodeAPCI { get; set; }
        public Nullable<int> CodeDeFamille { get; set; }
        public string CodeMedecinFamille { get; set; }
        public string CodeMedecinPrescripteur { get; set; }
        public string LibellePoste { get; set; }
        public decimal Recu { get; set; }
        public Nullable<System.DateTime> DateOrdonnance { get; set; }
        public decimal MontantCnam { get; set; }
        public decimal MontantMutuelle { get; set; }
        public int DureeTraitement { get; set; }
        public string Note { get; set; }
        public Nullable<int> CodeNatureReglement { get; set; }
        public string CodeMutuelle { get; set; }
        public string NomMalade { get; set; }
        public Nullable<int> Rang { get; set; }
        public Nullable<System.DateTime> DateNaissance { get; set; }
        public int CodeLienDeParente { get; set; }
        public string LibelleLienDeParente { get; set; }
        public bool TiersPayant { get; set; }
        public bool PriseEnCharge { get; set; }
        public bool Appareillage { get; set; }
        public string IdentifiantCNAMMedecin { get; set; }
        public string Libelle1 { get; set; }
        public string Libelle2 { get; set; }
        public string Libelle3 { get; set; }
        public string Libelle4 { get; set; }
        public string Libelle5 { get; set; }
        public string NumeroFacture { get; set; }
        public Nullable<System.DateTime> DateSuppression { get; set; }
        public Nullable<int> CodePersonnelSupprime { get; set; }
        public string NomPersonnelSupprime { get; set; }
        public Nullable<bool> Vider { get; set; }
    
        public virtual ICollection<VENTE_SUPPRIME_DETAILS> VENTE_SUPPRIME_DETAILS { get; set; }
    }
}
