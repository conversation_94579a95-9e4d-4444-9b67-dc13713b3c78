Imports System.Data.SqlClient
Imports System.Data.OleDb
Imports System.IO
Imports System.Reflection
Imports System.Web
Imports System.Configuration

Public Class fMain
    ' ...existing code...

    Private Sub ConfigurationDesRapportsToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles ConfigurationDesRapportsToolStripMenuItem.Click
        Dim fConfig As New fConfigurationRapports()
        fConfig.ShowDialog()
    End Sub

    ' ...existing code...

    'Déclaration des fonctions API
    Private Declare Function GetLocaleInfo Lib "kernel32" Alias "GetLocaleInfoA" (ByVal Locale As Integer, ByVal LCTYPE As Integer, ByVal lpLCData As String, ByVal cchData As Integer) As Integer
    Private Declare Function SetLocaleInfo Lib "kernel32" Alias "SetLocaleInfoA" (ByVal Locale As Integer, ByVal LCTYPE As Integer, ByVal lpLCData As String) As Integer
    Private Declare Function GetUserDefaultLCID Lib "kernel32" () As Integer
    'Déclaration de la constante séparateur décimal
    Private Const LOCALE_SDECIMAL = &HE


    Declare Function InitFRIUSBLibrary Lib "FIRIDLLU.dll" () As Integer
    Declare Function OpenFRIDoor Lib "FIRIDLLU.dll" () As Integer
    Declare Function CloseFRIUSBLibrary Lib "FIRIDLLU.dll" () As Integer

    Dim mylogin As New fLogin
    Public MyVentes As New List(Of fVente)
    Public IndexVente As Int32 = 1
    Public MySupprimerVente As New fSuppressionDesVentes
    Public MyVenteNonEnregistree As New fVenteNonEnregistree
    Public MyAchat As New fAchat
    ' Public MyClient As New fClient ' Temporairement commenté - formulaire exclu
    Public MyFournisseur As New fFournisseur
    Public MyArticle As New fArticle
    Public MyListePreparation As New fListePreparation
    Public MyMutuelle As New fMutuelle
    Public MyFicheArticle As New fFicheArticle
    Public MyFicheMutuelle As New fFicheMutuelle
    Public MyFicheFournisseur As New fFicheFournisseur
    Public MyFicheClient As New fFicheClient
    Public MyCNAM As New fCNAM

    Public MyProjetCommande As New fProjetCommande
    Public MyCommande As New fCommande
    ' Public MyEtatDesVentes As New fEtatDesVentes ' Temporairement commenté - formulaire exclu
    Public MyEtatDesFactures As New fEtatDesFactures
    Public MyEtatDesVentesParJour As New fEtatDesVentesParJour
    Public MyEtatDesVentesParMois As New fEtatDesVentesParMois
    Public MyEtatDesVentesParAnnee As New fEtatDesVentesParAnnee
    Public MyJournalDesVentes As New fEtatJournalDesVentes
    Public MyJournalDesAchats As New fEtatJournalDesAchats
    Public MyEtatDetailsDesVentes As New fEtatDetailsDesVentes

    Public MyReleveMutuelle As New fReleveeMutuelle
    Public MyReleveCNAM As New fReleveeCNAM

    Public MyMouvementClient As New fMouvementDesClients
    Public MyEcheanceClient As New fEcheancesDesClients

    Public MyEntree As New fEntree
    Public MySortie As New fSortie
    Public MyPret As New fPret
    Public MyEmprunt As New fEmprunt

    Public MyInventaire As New fInventaire

    Public MyArticleNonRemb As New fArticleNonRemboursable
    Public MyArticleRemb As New fArticleRemboursable
    Public MyFamilleNonRemb As New fFamilleNonRemboursable
    Public MyFamilleRemb As New fFamilleRemboursable
    Public MyVenteSupprime As New fVenteSupprime
    Public MyVenteAnnuler As New fVenteAnnuler
    Public MyProduction As New fProduction

    Public MyArticlePerimes As New fListeArticlePerime
    Public MyHitParade As New fHitParadeArticle
    Public MyJournalTVA As New fValeurTVAVente
    Public MyMouvementArticle As New fMouvementArticle
    Public MyNMouvementArticle As New fNMouvementArticle
    Public MyHistoriqueMouvementArticle As New fHistoriquesMouvements
    Public MyMouvementPharmacie As New fMouvementPharmacien
    Public MyStockParCategorie As New fStockParCategorie
    Public MyEtatEntree As New fEtatEntreeArticle
    Public MyEtatSortie As New fEtatSortiArticle
    Public MyEtatJournalReleveCNAM As New fEtatJournalReleveCNAM
    Public MyEtatJournalReleveMutuelle As New fEtatJournalReleveMutuelle
    Public MyEtatOrdonnancier As New fEtatOrdonnancier
    Public MyEtatOrdonnanceNonRegleCNAM As New fEtatOrdonnanceNonRegleCNAM
    Public MyEtatOrdonnanceNonRegleMutuelle As New fEtatOrdonnanceNonRegleMutuelle
    Public MyTvaParMois As New fValeurTVAVenteMois
    Public MyTvaAchat As New fValeurTVAAchat
    Public MyReleveMouvementCNAM As New fEtatReleveMouvementCNAM
    Public MyReleveMouvementMutuelle As New fEtatReleveMouvementMutuelle
    Public MyDetailsCaisse As New fEtatRegelement
    Public MyPreparationAProduire As New fListeDesPreparationAProduire
    Public MyStatistiqueFournisseur As New fStatistiqueDesFournisseurs
    Public MyListeDesBons As New fListeDesBons
    Public MyAchatSupprime As New fAchatSupprime
    Public MyStrategieDeStockage As New fStrategieDeStockage
    Public MyChiffreAffaireParCategorie As New fEtatChiffreAffaireCategorie
    Public MyMouvementFournisseur As New fMouvementDesFournisseurs
    Public MyfListeArticleSansInventaire As New fListeArticleSansInventaire

    Public MyEcheanceFournisseur As New fEcheancesDesFournisseurs

    Public MyUtilisateur As New fUtilisateur

    Public MyAPCI As New fAPCI
    Public MyMedecin As New fMedecin
    Public MySpecialiteMedecin As New fSpecialiteMedecin
    Public MyLaboratoire As New fLaboratoire

    Public MyForme As New fForme
    Public MyCategorie As New fCategorie
    Public MyInteraction As New fInteractionsCreation
    Public MyInteractionBCB As New fVisionneurInteraction
    Public MyIndemnite As New fIndemnite
    Public MyDCI As New fDCI
    Public MyBanque As New fBanque
    Public MyVille As New fVille
    Public MyNatureEntreeSortie As New fNatureEntreeSortie
    Public MyPharmacie As New fPharmacie

    Public MyGestionPointControle As New fGestionPointsControle
    Public MyAffectationDesAutorisations As New fAffectationDesAutorisations
    Public MyGestionDesProfils As New fProfilUtilisateur

    Public MyHistoriqueDAcces As New fHistoriqueDAcces
    Public MyHistoriqueDesChangementsPrix As New fHistoriqueDesChangementsDesPrix

    Public MyAlimentationCaisse As New fAlimentationCaisse
    Public MyChangemantFichierArticle As New fChangemantDuFichierArticle
    Public MyHsitoriqueDesActions As New fHistoriqueDesActions
    Public MyUpdateArticle As New fUpdateArticle
    Public myMesVoisinages As New fMesVoisinage

    Public MyEtatInventaire As New fEtatInventaire

    Public MyHistoriqueDesAchats As New fHistoriqueDesAchats
    Public MyReglemntSupprime As New fReglemntSupprime

    Public MySaisiePerimes As New fSaisiePerimes
    Public MyAvoirAchat As New FAvoirAchat


    Dim SauvegardeReussi As Boolean = False

    Public Version As Version = Nothing
    Public Assembly As Assembly = Nothing
    Public AssemblyName As AssemblyName = Nothing

    Dim rowIconBureau As DataRow
    Dim cmdIconBureau As New SqlCommand
    Dim dsIconBureau As New DataSet
    Dim daIconBureau As New SqlDataAdapter
    Dim cbIconBureau As New SqlCommandBuilder
    Dim NombreOnglet As Integer = 0
    Dim cmdSpot As New SqlCommand
    Dim daSpot As New SqlDataAdapter
    Dim dsSpot As New DataSet

    Dim cmdMenu As New SqlCommand
    Dim dsMenu As New DataSet
    Dim daMenu As New SqlDataAdapter
    Dim cbMenu As New SqlCommandBuilder

    Dim StrSQL As String

    'Affecter ls btns a un group pour faciliter la manipulation
    Dim monBueauBouton As New Collection()
    Dim monBueauLabel As New Collection()
    Dim monBueauLien As New Collection()

    ' Date et heure de la dernière sauvegarde
    Dim DateHeureDerniereSauvegarde As Date = Now

    'Instance de la classe FtpConnection
    Dim WithEvents ClientFTP As New FtpConnection()

    Public TypeFermeture As String

    Private Sub fMain_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        Quitter()

        If TypeFermeture = "Annuler" Then
            e.Cancel = True
        Else
            Timer1.Stop()
        End If
    End Sub
    Public Property DecimalSeparator() As String
        Get
            Dim nLength As Integer
            Dim nLocale As Integer
            nLocale = GetUserDefaultLCID()
            nLength = GetLocaleInfo(nLocale, LOCALE_SDECIMAL, vbNullString, 0) - 1
            DecimalSeparator = Space$(nLength)
            GetLocaleInfo(nLocale, LOCALE_SDECIMAL, DecimalSeparator, nLength)
        End Get
        Set(ByVal value As String)
            Dim nLocale As Integer
            If value <> DecimalSeparator Then
                If value = "." Or value = "," Then
                    nLocale = GetUserDefaultLCID()
                    SetLocaleInfo(nLocale, LOCALE_SDECIMAL, value)
                End If
            End If
        End Set
    End Property

    Private Sub DesactiveX()
        'Dim hMenu As Long
        'Dim nCount As Long
        'Dim MeHwnd As Long
        'MeHwnd = FindWindowA(vbNullString, Me.Text)
        'hMenu = GetSystemMenu(MeHwnd, 0)
        'nCount = GetMenuItemCount(hMenu)

        'Call RemoveMenu(hMenu, nCount - 1, MF_REMOVE Or MF_BYPOSITION)
        'Call RemoveMenu(hMenu, nCount - 2, MF_REMOVE Or MF_BYPOSITION)

        'DrawMenuBar(MeHwnd)
    End Sub


    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)

        If Tab.TabPages.Count <> 0 Then

            If e.KeyCode = "121" Then
                e.SuppressKeyPress = True
            Else
                e.SuppressKeyPress = False
            End If

            ''If e.KeyCode = "65" Or e.KeyCode <= "90" Then
            ''    Exit Sub
            ''End If

            '''''''''''''''''''''''''''''' vente ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text.Length > 4 Then
                If Tab.SelectedTab.Text.Substring(0, 5) = "Vente" Then
                    If IsNumeric(Tab.SelectedTab.Text.Substring(5, Tab.SelectedTab.Text.Length - 5)) Then
                        'If e.KeyCode = "114" Then
                        '    e.SuppressKeyPress = True
                        '    Application.DoEvents()
                        'End If

                        'If e.KeyCode = "121" Then
                        '    e.SuppressKeyPress = True
                        'End If

                        If e.KeyCode = "120" Then
                            If TestPremierCliqueToucheF = 0 Then TestPremierCliqueToucheF = 1
                            If TestPremierCliqueToucheF = 1 Then
                                MyVentes.FirstOrDefault(Function(x) x.Text = Tab.SelectedTab.Text).fonctionsF(e.KeyCode, o, e)
                                'MyVente.fonctionsF(e.KeyCode, o, e)
                            End If
                            TestPremierCliqueToucheF = TestPremierCliqueToucheF + 1
                        Else
                            MyVentes.FirstOrDefault(Function(x) x.Text = Tab.SelectedTab.Text).fonctionsF(e.KeyCode, o, e)
                            'MyVente.fonctionsF(e.KeyCode, o, e)
                        End If
                        Exit Sub
                    End If
                End If
            End If

            '''''''''''''''''''''''''''''' Suppression vente ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Suppression Vente" Then
                MySupprimerVente.fonctionsF(e.KeyCode, o, e)
                Exit Sub
            End If

            ''''''''''''''''''''''''''''' achat ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Achat" Then
                MyAchat.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            ''''''''''''''''''''''''''''' AvoirAchat ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Avoir" Then
                MyAvoirAchat.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' commande ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Commande" Then
                MyCommande.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Projet de commande ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Projet de Commande" Then
                MyProjetCommande.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' client ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Clients" Then
                MyClient.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' fiche client ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Création d'un nouveau client" Or Tab.SelectedTab.Text = "Modification d'un Client" Then
                MyFicheClient.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' article ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Article" Then
                MyArticle.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' liste des préparations ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Préparations" Then
                MyListePreparation.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' mutuelle ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Mutuelle" Or Tab.SelectedTab.Text = "Mutuelle" Then
                MyMutuelle.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Fiche mutuelle ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Création d'un nouveau Mutuelle" Or Tab.SelectedTab.Text = "Modification du Mutuelle" Then
                MyFicheMutuelle.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' fournisseur ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Fournisseur" Then
                MyFournisseur.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Fiche fournisseur ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Création d'un nouveau fournisseur" Or Tab.SelectedTab.Text = "Modification du Fournisseur" Then
                MyFicheFournisseur.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            ''''''''''''''''''''''''''''' releve mutuelle ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Relevé Mutuelle" Then
                MyReleveMutuelle.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' releve CNAM ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Relevé CNAM" Then
                MyReleveCNAM.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' intéraction création ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Intéraction" Then
                MyInteraction.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' CNAM ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "CNAM" Then
                MyCNAM.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If


            '''''''''''''''''''''''''''''' Journal des achats ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Journal des achats" Then
                MyJournalDesAchats.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Mouvement des clients ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Mouvement Client" Then
                MyMouvementClient.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Echaence client ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Echéance" Then
                MyEcheanceClient.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Etat des facture ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Etat Des Ventes" Then
                MyEtatDesVentes.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Etat des facture ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Etat Des Factures" Then
                MyEtatDesFactures.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' ENTREE ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Entrée" Then
                MyEntree.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' SORTIE ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Sortie" Then
                MySortie.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' EMPRUNT ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Emprunt Pharmacie (Entrée)" Then
                MyEmprunt.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' PRET ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Prêt Pharmacie (Sortie)" Then
                MyPret.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' INVENTAIRE ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Inventaire" Then
                MyInventaire.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Article Non Remboursable ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Article Non Remboursable" Then
                MyArticleNonRemb.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Article Remboursable ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Article Remboursable" Then
                MyArticleNonRemb.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Catégories Non Remboursable ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Catégories Non Remboursable" Then
                MyFamilleNonRemb.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Catégories Remboursable ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Catégories Remboursable" Then
                MyFamilleRemb.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Vente supprime ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Vente Supprimées" Then
                MyVenteSupprime.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Production ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Production" Then
                MyProduction.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' liste des Articles perimés ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Etat articles perimés" Then
                MyArticlePerimes.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Hit parade ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Hit Parade" Then
                MyHitParade.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Journal TVA (TVA PERCUE) ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Journal TVA Vente" Then
                MyJournalTVA.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Mouvement article ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Mouvement article" Then
                MyMouvementArticle.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            If Tab.SelectedTab.Text = "Mouvement des articles" Then
                MyNMouvementArticle.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            If Tab.SelectedTab.Text = "Historique Mouvement des articles" Then
                MyHistoriqueMouvementArticle.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Mouvement pharmacie ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Mouvement pharmacie" Then
                MyMouvementPharmacie.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Stoch par catégorie ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Stock par catégorie" Then
                MyStockParCategorie.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' etat entree article ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Etat Entree" Then
                MyEtatEntree.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' etat sortie article ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Etat Sortie" Then
                MyEtatSortie.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Etat Journal des relevés CNAM ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Etat Journal des relevés CNAM" Then
                MyEtatJournalReleveCNAM.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Etat Journal des relevés Mutuelle ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Etat Journal des relevés Mutuelle" Then
                MyEtatJournalReleveMutuelle.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Etat des ordonnanciers ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Ordonnancier" Then
                MyEtatOrdonnancier.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Etat des ordonnances non reglées CNAM ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Etat des ordonnances non reglées CNAM" Then
                MyEtatOrdonnanceNonRegleCNAM.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Etat des ordonnances non reglées Mutuelle ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Etat des ordonnances non reglées Mutuelle" Then
                MyEtatOrdonnanceNonRegleMutuelle.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' TVA par mois ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "TVA Vente / Mois" Then
                MyTvaParMois.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' TVA achat ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "TVA Achat" Then
                MyTvaAchat.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Mouvement CNAM ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Mouvemnet CNAM" Then
                MyReleveMouvementCNAM.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Mouvement Mutuelle ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Mouvement Mutuelle" Then
                MyReleveMouvementMutuelle.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Détails caisse (etat reglement) ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Détails Caisse" Then
                MyDetailsCaisse.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Préparation à produire  ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Préparation à produire" Then
                MyPreparationAProduire.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Préparation à produire  ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Statistique Fournisseur" Then
                MyStatistiqueFournisseur.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Suivi des bons  ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Liste des bons" Then
                MyListeDesBons.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' achats supprimés  ''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Achats supprimés" Then
                MyAchatSupprime.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Stratégie de stockage  ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Simulation de stockage" Then
                MyStrategieDeStockage.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Chiffre d'affaire par catégorie  ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Vente achat bénéfice par catégorie" Then
                MyChiffreAffaireParCategorie.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Etat détails des ventes  ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Etat Détails Des Ventes" Then
                MyEtatDetailsDesVentes.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Etat des ventes par jour  ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Etat Des Vente Par Jour" Then
                MyEtatDesVentesParJour.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Etat des ventes par mois  ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Etat Des Vente Par Mois" Then
                MyEtatDesVentesParMois.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Etat des ventes par année  ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Etat Des Vente Par Année" Then
                MyEtatDesVentesParAnnee.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Etat JOURNAL des ventes ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Journal Des Ventes" Then
                MyJournalDesVentes.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Mouvement fournisseur  ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Mouvement Fournisseur" Then
                MyMouvementFournisseur.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Echeance fournisseur  ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Echéance Fournisseur" Then
                MyEcheanceFournisseur.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Journal des achats ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Journal des achats" Then
                MyJournalDesAchats.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Utilisateurs ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Utilisateurs" Then
                MyUtilisateur.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' APCI ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "APCI" Then
                MyAPCI.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Medecin ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Medecin" Then
                MyMedecin.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Specialite ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Spécialité" Then
                MySpecialiteMedecin.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Laboratoire ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Laboratoire" Then
                MyLaboratoire.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Forme ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Forme" Then
                MyForme.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Catégories ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Catégories" Then
                MyCategorie.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Intéraction ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Intéraction" Then
                MyInteraction.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Intéraction ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Indemnite" Then
                MyIndemnite.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' DCI ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "DCI" Then
                MyDCI.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Banque ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Banque" Then
                MyBanque.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Ville ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Ville" Then
                MyVille.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Libelle Entrée Sortie ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Libelle Entrée Sortie" Then
                MyNatureEntreeSortie.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Pharmacie ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Pharmacie" Then
                MyPharmacie.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Gestion des Points de Controle ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Gestion des Points de Controle" Then
                MyGestionPointControle.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Afféctation des autorisations ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Afféctation des autorisations" Then
                MyAffectationDesAutorisations.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Gestion des profils ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Gestion des profils" Then
                MyGestionDesProfils.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' historique d'accès ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Historique d'accès" Then
                MyHistoriqueDAcces.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' historique des changements des prix ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Historique des changements des prix" Then
                MyHistoriqueDesChangementsPrix.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Alimentation Caisse ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Alimentation Caisse" Then
                MyAlimentationCaisse.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Changemant de fiche article ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Changemant fichier article" Then
                MyChangemantFichierArticle.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Historique des actions ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Historique des actions" Then
                MyHsitoriqueDesActions.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''' Update des articles ''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Pharma 2000 PREMIUM Update" Then
                MyUpdateArticle.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If


            '''''''''''''''''''''''''''''' SAISIE PERIME ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If Tab.SelectedTab.Text = "Saisie Périmés" Then
                MySaisiePerimes.fonctionsF(e.KeyData, o, e)
                Exit Sub
            End If

            '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
        End If

    End Sub

    Private Sub bQuitter7_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter7.Click
        Quitter()
    End Sub
    Private Sub bClient_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bClient.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Clients" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        ' Dim MyClient As New fClient ' Temporairement commenté - formulaire exclu
        ' Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        ' Tab.SelectedIndex = Tab.TabPages.Count - 1
        ' Tab.SelectedTab.Controls.Add(MyClient.Panel)
        Tab.SelectedTab.Text = "Clients"
        MyClient.init()
    End Sub

    Private Sub RibbonButton2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton2.Click
        If ControleDAcces(10, "FOURNISSEURS") = "False" Then
            Exit Sub
        End If

        Dim i As Integer
        For i = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(i).Text = "Fournisseur" Then
                Tab.TabPages(i).Show()
                Exit Sub
            End If
        Next
        MyFournisseur = New fFournisseur
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyFournisseur.Panel)
        Tab.SelectedTab.Text = "Fournisseur"
        MyFournisseur.Init()
    End Sub

    Private Sub bQuitter2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter2.Click
        Quitter()
    End Sub

    Private Sub bQuitter3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter3.Click
        Quitter()
    End Sub

    Private Sub bQuitter4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter4.Click
        Quitter()
    End Sub

    Private Sub RibbonButton1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton1.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Article" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        Dim MyArticle As New fArticle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyArticle.PAnel)
        Tab.SelectedTab.Text = "Article"
        MyArticle.Init()
    End Sub


    Private Sub bSituationClient_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Situation client" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        Dim MySituationClient As New fSituationClient
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MySituationClient.Panel)
        Tab.SelectedTab.Text = "Situation client"
        MySituationClient.Init()
    End Sub

    Private Sub bSituationArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Situation article" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        Dim MySituationArticle As New fSituationArticle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MySituationArticle.Panel)
        Tab.SelectedTab.Text = "Situation article"
        MySituationArticle.Init()
    End Sub

    Private Sub BtnVente_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnVente.Click
        Dim I As Integer
        Dim cmd As New SqlCommand
        Dim Autorise As Boolean = False

        cmd.Connection = ConnectionServeur
        cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'PLUSIEURS_VENTES' AND CodeUtilisateur='" + CodeUtilisateur + "'"

        Try
            Autorise = cmd.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        Dim MyVente As New fVente
        If Autorise = False Then
            For I = 0 To Tab.TabPages.Count - 1
                If Tab.TabPages(I).Text = "Vente" + (IndexVente - 1).ToString() Then
                    Tab.TabPages(I).Show()
                    Exit Sub
                End If
            Next
            ''''
            'IndexVente = 1
            ''''
            MyVente.Text = "Vente" + IndexVente.ToString() ' + "1"
            IndexVente = IndexVente + 1
            MyVentes.Add(MyVente)
            Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            Tab.SelectedIndex = Tab.TabPages.Count - 1
            Tab.SelectedTab.Controls.Add(MyVente.Panel)
            Tab.SelectedTab.Text = MyVente.Text
            MyVente.Init()
            MyVente.bAjouter_Click(sender, e)
        Else
            MyVente.Text = "Vente" + IndexVente.ToString()
            IndexVente = IndexVente + 1
            MyVentes.Add(MyVente)
            Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            Tab.SelectedIndex = Tab.TabPages.Count - 1
            Tab.SelectedTab.Controls.Add(MyVente.Panel)
            Tab.SelectedTab.Text = MyVente.Text
            MyVente.Init()
            MyVente.bAjouter_Click(sender, e)
        End If



    End Sub


    Private Sub bLaboratoire_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLaboratoire.Click
        If ControleDAcces(10, "LABORATOIRES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Laboratoire" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyLaboratoire = New fLaboratoire
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyLaboratoire.Panel)
        Tab.SelectedTab.Text = "Laboratoire"
        MyLaboratoire.Init()
    End Sub

    Private Sub menuReporting_DropDown(ByVal sender As System.Object, ByVal e As System.EventArgs)
        'menuReporting.Items (0).
    End Sub

    Private Sub bQuitter8_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter8.Click
        Quitter()
    End Sub

    Private Sub bQuitter6_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter6.Click
        Quitter()
    End Sub

    Private Sub bQuitter5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter5.Click
        Quitter()
    End Sub


    Private Sub bMutuelle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bMutuelle.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Mutuelle" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        Dim MyMutuelle As New fMutuelle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyMutuelle.Panel)
        Tab.SelectedTab.Text = "Mutuelle"
        MyMutuelle.init()
    End Sub

    Private Sub bVente_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bVente.Click

        Dim I As Integer
        Dim cmd As New SqlCommand
        Dim Autorise As Boolean = False

        cmd.Connection = ConnectionServeur
        cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'PLUSIEURS_VENTES' AND CodeUtilisateur='" + CodeUtilisateur + "'"

        Try
            Autorise = cmd.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        If Autorise = False Then
            For I = 0 To Tab.TabPages.Count - 1
                If Tab.TabPages(I).Text = "Vente" Then
                    Tab.TabPages(I).Show()
                    Exit Sub
                End If
            Next
        End If

        Dim MyVente As New fVente
        MyVente.Text = "Vente" + IndexVente.ToString()
        IndexVente = IndexVente + 1
        MyVentes.Add(MyVente)
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyVente.Panel)
        Tab.SelectedTab.Text = MyVente.Text
        MyVente.Init()
        MyVente.bAjouter_Click(sender, e)
    End Sub

    Private Sub bArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bArticle.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Article" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyArticle = New fArticle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyArticle.PAnel)
        Tab.SelectedTab.Text = "Article"
        MyArticle.Init()
    End Sub

    Private Sub bRaccourciClient_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRaccourciClient.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Clients" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyClient = New fClient
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyClient.Panel)
        Tab.SelectedTab.Text = "Clients"
        MyClient.init()

    End Sub

    Private Sub RibbonButton48_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton48.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Mutuelle" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyMutuelle = New fMutuelle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyMutuelle.Panel)
        Tab.SelectedTab.Text = "Mutuelle"
        MyMutuelle.init()
    End Sub

    Private Sub RibbonButton49_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton49.Click
        If ControleDAcces(10, "FOURNISSEURS") = "False" Then
            Exit Sub
        End If

        Dim i As Integer
        For i = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(i).Text = "Fournisseur" Then
                Tab.TabPages(i).Show()
                Exit Sub
            End If
        Next
        MyFournisseur = New fFournisseur
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyFournisseur.Panel)
        Tab.SelectedTab.Text = "Fournisseur"
        MyFournisseur.Init()
    End Sub

    Private Sub bQuitter1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter1.Click
        Quitter()
    End Sub

    Private Sub bAchatRaccourci_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAchatRaccourci.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Achat" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyAchat = New fAchat
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyAchat.Panel)

        Tab.SelectedTab.Text = "Achat"
        MyAchat.Init()
    End Sub

    Private Sub bCommandeRaccourci_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCommandeRaccourci.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Commande" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyCommande = New fCommande
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyCommande.Panel)

        Tab.SelectedTab.Text = "Commande"
        MyCommande.Init()
    End Sub

    Private Sub Timer1_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer1.Tick
        ActualiserStatusBar()
    End Sub

    Private Sub ActualiserStatusBar()
        ' Jour de la semaine
        Dim JourSemaine As String = ""
        Dim NomFichierAEnvoyer As String = ""


        Select Case Now.Date.DayOfWeek
            Case 1
                JourSemaine = "Lundi"
            Case 2
                JourSemaine = "Mardi"
            Case 3
                JourSemaine = "Mercredi"
            Case 4
                JourSemaine = "Jeudi"
            Case 5
                JourSemaine = "Vendredi"
            Case 6
                JourSemaine = "Samedi"
            Case 7
                JourSemaine = "Dimanche"
        End Select

        ' Affichage du statut
        lStatutIdentification.Text = "Pharma 2000 Premium, Version " + Application.ProductVersion + ", " + NomServeur + ", " + NomBase + ", " + NomUtilisateur + ", Poste " + System.Environment.GetEnvironmentVariable("Poste") + "    Date Exe : 20-04-2018"
        lStatutDateHeure.Text = JourSemaine + " " + Date.Now.ToString() 'Format(Date.Now, "dd/MM/yyyy hh:mm")
        lStatutMessage.Width = StatusBar.Width - lStatutIdentification.Width - lStatutDateHeure.Width - 20
        Application.DoEvents()

        If Not Tab.SelectedTab Is Nothing Then
            If Tab.SelectedTab.Text = "Bureau" Then
                AfficherSpot()
            End If
        End If

        ' Lancement de la sauvegarde de la base
        If DateDiff(DateInterval.Minute, DateHeureDerniereSauvegarde, Now) >= DelaiSauvegarde And DelaiSauvegarde > 0 Then
            Dim StrSQL As String = ""
            Dim cmd As New SqlCommand
            Dim NomFichier As String = ""

            NomFichier = LecteurSauvegarde + "\Backup_" + NomBase + "_" + JourSemaine + ".bak"
            Try

                If System.Console.In.Peek > 0 Then
                    lStatutMessage.Text = "Report de la sauvegarde automatique ..."
                    lStatutMessage.ForeColor = Color.Black
                    Application.DoEvents()
                Else
                    lStatutMessage.Text = "Démarrage de la sauvegarde automatique ..."
                    lStatutMessage.ForeColor = Color.Black
                    Application.DoEvents()
                    If File.Exists(NomFichier) Then
                        File.Delete(NomFichier)
                    End If
                    StrSQL = "BACKUP DATABASE " + NomBase + " TO DISK = " + Quote(NomFichier)
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL
                    cmd.CommandTimeout = 0
                    cmd.ExecuteNonQuery()

                    Try
                        NomFichierAEnvoyer = "Sauvegarde " + CodePharmacien + ".ok"

                        'Génération du fichier 

                        If File.Exists(LecteurSauvegarde + "\" + NomFichierAEnvoyer) Then
                            File.Delete(LecteurSauvegarde + "\" + NomFichierAEnvoyer)
                        End If

                        Dim FichierSauv As New StreamWriter(LecteurSauvegarde + "\" + NomFichierAEnvoyer)

                        FichierSauv.WriteLine(CodePharmacien)
                        FichierSauv.WriteLine(Format(Now, "dd-MM-yyyy-hh-mm-ss"))
                        FichierSauv.WriteLine(System.Environment.GetEnvironmentVariable("Poste"))
                        FichierSauv.WriteLine(NomUtilisateur)
                        FichierSauv.WriteLine("Automatique")
                        FichierSauv.Close()

                        'Paramètrage de la connexion
                        ClientFTP.Hostname = "************"
                        ClientFTP.Username = "PHARMA2000"
                        ClientFTP.Password = "Next;3U7+s4"

                        Dim FtpInstance As New FTP
                        FtpInstance.ConnectFTP(ClientFTP.Hostname, ClientFTP.Username, ClientFTP.Password)

                        'suppression de l'ancien fichier
                        FtpInstance.ListDirectory("Sauvegarde")
                        For Each yLigne As String In FtpInstance.Liste
                            If yLigne = NomFichierAEnvoyer Then
                                FtpInstance.DeleteFile("Sauvegarde\" + NomFichierAEnvoyer)
                                Exit For
                            End If
                        Next
                        'Envoi du fichier
                        FtpInstance.Upload("Sauvegarde\" + NomFichierAEnvoyer, LecteurSauvegarde + "\" + NomFichierAEnvoyer)

                        If File.Exists(LecteurSauvegarde + "\" + NomFichierAEnvoyer) Then
                            File.Delete(LecteurSauvegarde + "\" + NomFichierAEnvoyer)
                        End If

                        Try
                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = "SELECT LecteurSauvegardeDistant FROM PARAMETRES WHERE POSTE = " + Quote(System.Environment.GetEnvironmentVariable("Poste"))


                            File.Copy(NomFichier, cmd.ExecuteScalar + "\Backup_" + NomBase + "_" + Format(Now, "dd-MM-yyyy-hh-mm-ss") + ".bak")
                        Catch
                        End Try
                    Catch ex As Exception

                    End Try



                    '
                    Try

                        If Directory.Exists("C:\SauveBase") = False Then
                            Directory.CreateDirectory("C:\SauveBase")
                        End If

                        Dim NomFLocal As String
                        Dim ipServeur As String()

                        NomFLocal = NomFichier.Substring(NomFichier.IndexOf("\"))

                        If File.Exists("C:\SauveBase" + "\Backup_" + NomBase + "_" + JourSemaine + ".bak") Then
                            File.Delete("C:\SauveBase" + "\Backup_" + NomBase + "_" + JourSemaine + ".bak")
                        End If

                        ipServeur = GetIPaddresses(NomServeur)

                        My.Computer.FileSystem.CopyFile("\\" + ipServeur(0) + NomFLocal, "C:\SauveBase" + "\Backup_" + NomBase + "_" + JourSemaine + ".bak")
                    Catch ex As Exception
                    End Try
                    '



                    lStatutMessage.Text = "Réussite de la sauvegarde automatique !"
                    lStatutMessage.ForeColor = Color.Green
                    InsertionDansLog("SAUVEGARDE_BASE", "Réussite de la sauvegarde automatique !", CodeUtilisateur, System.DateTime.Now, "BASE", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
                End If

                DateHeureDerniereSauvegarde = Now
            Catch ex As Exception
                InsertionDansLog("SAUVEGARDE_BASE", "Echec de la sauvegarde automatique !", CodeUtilisateur, System.DateTime.Now, "BASE", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
                lStatutMessage.Text = "Echec de la sauvegarde automatique !"
                lStatutMessage.ForeColor = Color.Red
            End Try
        End If
    End Sub

    Private Sub C1Ribbon1_RibbonEvent(ByVal sender As System.Object, ByVal e As C1.Win.C1Ribbon.RibbonEventArgs) Handles C1Ribbon1.RibbonEvent

    End Sub

    Private Sub bProjetCommande_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Projet de Commande" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyProjetCommande = New fProjetCommande
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyProjetCommande.Panel)

        Tab.SelectedTab.Text = "Projet de Commande"
        MyProjetCommande.Init()
    End Sub

    Private Sub RibbonButton36_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton36.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Commande" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyCommande = New fCommande
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyCommande.Panel)

        Tab.SelectedTab.Text = "Commande"
        MyCommande.Init()
    End Sub

    Private Sub RibbonButton37_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton37.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Achat" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyAchat = New fAchat
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyAchat.Panel)

        Tab.SelectedTab.Text = "Achat"
        MyAchat.Init()
    End Sub

    Private Sub RibbonButton11_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton11.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "CNAM" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyCNAM = New fCNAM
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyCNAM.Panel)
        Tab.SelectedTab.Text = "CNAM"
        MyCNAM.Init()
    End Sub

    'Private Sub bEtatSortie_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bEtatSortie.Click
    '    Dim I As Integer
    '    For I = 0 To Tab.TabPages.Count - 1
    '        If Tab.TabPages(I).Text = "Entrée" Then
    '            Tab.TabPages(I).Show()
    '            Exit Sub
    '        End If
    '    Next
    '    Dim MyEntree As New fEntree
    '    Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
    '    Tab.SelectedIndex = Tab.TabPages.Count - 1
    '    Tab.SelectedTab.Controls.Add(MyEntree.Panel)
    '    Tab.SelectedTab.Text = "Entrée"
    '    MyEntree.Init()
    'End Sub

    Private Sub bEntree_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bEntree.Click

        If ControleDAcces(8, "ENTREE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Entrée" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEntree = New fEntree
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEntree.Panel)
        Tab.SelectedTab.Text = "Entrée"
        MyEntree.Init()
    End Sub

    Private Sub bSortie_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSortie.Click

        If ControleDAcces(8, "SORTIES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Sortie" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MySortie = New fSortie
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MySortie.Panel)
        Tab.SelectedTab.Text = "Sortie"
        MySortie.Init()
    End Sub

    Private Sub RibbonButton76_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPharmacie.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Pharmacie" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyPharmacie = New fPharmacie
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyPharmacie.Panel)
        Tab.SelectedTab.Text = "Pharmacie"
        MyPharmacie.init()
    End Sub

    Private Sub bEmprunt_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bEmprunt.Click

        If ControleDAcces(8, "EMPRUNT") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Emprunt Pharmacie (Entrée)" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEmprunt = New fEmprunt
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEmprunt.Panel)
        Tab.SelectedTab.Text = "Emprunt Pharmacie (Entrée)"
        MyEmprunt.Init()
    End Sub

    Private Sub bPret_Click_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPret.Click

        If ControleDAcces(8, "PRET") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Prêt Pharmacie (Sortie)" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyPret = New fPret
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyPret.Panel)
        Tab.SelectedTab.Text = "Prêt Pharmacie (Sortie)"
        MyPret.Init()
    End Sub

    Private Sub bCategorieNonRemboursable_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCategorieNonRemboursable.Click

        If ControleDAcces(4, "CATEGORIE_NON_REMBOURSABLE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Catégories Non Remboursable" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyFamilleNonRemb = New fFamilleNonRemboursable
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyFamilleNonRemb.Panel)
        Tab.SelectedTab.Text = "Catégories Non Remboursable"
        MyFamilleNonRemb.init()
    End Sub

    Private Sub bArticleNonRemboursable_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bArticleNonRemboursable.Click

        If ControleDAcces(4, "ARTICLE_NON_REMBOURSABLE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Article Non Remboursable" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyArticleNonRemb = New fArticleNonRemboursable
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyArticleNonRemb.Panel)
        Tab.SelectedTab.Text = "Article Non Remboursable"
        MyArticleNonRemb.init()
    End Sub

    Private Sub bSaiseInventaire_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSaiseInventaire.Click

        If ControleDAcces(8, "SAISIE_DINVENTAIRE") = "False" Then
            Exit Sub
        End If


        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Inventaire" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyInventaire = New fInventaire
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyInventaire.Panel)
        Tab.SelectedTab.Text = "Inventaire"
        MyInventaire.Init()
    End Sub

    Private Sub RibbonButton9_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton9.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Mouvement Client" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyMouvementClient = New fMouvementDesClients
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyMouvementClient.Panel)
        Tab.SelectedTab.Text = "Mouvement Client"
        MyMouvementClient.init()
    End Sub

    Private Sub bEcheancier_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bEcheancier.Click

        If ControleDAcces(5, "ECHEANCIER") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Echéance" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEcheanceClient = New fEcheancesDesClients
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEcheanceClient.Panel)
        Tab.SelectedTab.Text = "Echéance"
        MyEcheanceClient.init()
    End Sub

    Private Sub bCategorieRemboursable_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCategorieRemboursable.Click

        If ControleDAcces(4, "CATEGORIE_REMBOURSABLE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Catégories Remboursable" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyFamilleRemb = New fFamilleRemboursable
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyFamilleRemb.Panel)
        Tab.SelectedTab.Text = "Catégories Remboursable"
        MyFamilleRemb.init()
    End Sub

    Private Sub bArticleRemboursable_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bArticleRemboursable.Click

        If ControleDAcces(4, "ARTICLE_REMBOURSABLE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Article Remboursable" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyArticleRemb = New fArticleRemboursable
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyArticleRemb.Panel)
        Tab.SelectedTab.Text = "Article Remboursable"
        MyArticleRemb.init()
    End Sub

    Private Sub RibbonButton65_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton65.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Relevé CNAM" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyReleveCNAM = New fReleveeCNAM
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyReleveCNAM.PAnel)
        Tab.SelectedTab.Text = "Relevé CNAM"
        MyReleveCNAM.Init()
    End Sub

    Private Sub RibbonButton69_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton69.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Relevé Mutuelle" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyReleveMutuelle = New fReleveeMutuelle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyReleveMutuelle.PAnel)
        Tab.SelectedTab.Text = "Relevé Mutuelle"
        MyReleveMutuelle.Init()
    End Sub

    Private Sub bAPCI_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAPCI.Click

        If ControleDAcces(10, "APCI") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "APCI" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyAPCI = New fAPCI
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyAPCI.Panel)
        Tab.SelectedTab.Text = "APCI"
        MyAPCI.Init()
    End Sub

    Private Sub bParametresGenereaux_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bParametresGenereaux.Click

        Dim MyParametreGeneraux As New fParametresGeneraux
        MyParametreGeneraux.init()
        MyParametreGeneraux.ShowDialog()
        MyParametreGeneraux.Close()
        MyParametreGeneraux.Dispose()
        'AfficherReleve()
    End Sub

    Private Sub RibbonGroup5_DialogLauncherClick(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub bMedecin_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bMedecin.Click
        If ControleDAcces(10, "MEDECINS") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Medecin" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyMedecin = New fMedecin
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyMedecin.Panel)
        Tab.SelectedTab.Text = "Medecin"
        MyMedecin.Init()
    End Sub

    Private Sub bRecapCaisseMenu_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRecapCaisseMenu.Click

        If ControleDAcces(5, "RECAP_CAISSE") = "False" Then
            Exit Sub
        End If

        Dim MyRecapCaisse As New fRecapitulatifCaisse
        MyRecapCaisse.ShowDialog()
        MyRecapCaisse.Close()
        MyRecapCaisse.Dispose()
    End Sub

    Private Sub RibbonButton17_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton17.Click

        If ControleDAcces(5, "ETAT_DETAILS_DES_VENTES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat Détails Des Ventes" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEtatDetailsDesVentes = New fEtatDetailsDesVentes
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEtatDetailsDesVentes.Panel)
        Tab.SelectedTab.Text = "Etat Détails Des Ventes"
        MyEtatDetailsDesVentes.init()
    End Sub

    Private Sub RibbonButton19_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton19.Click
        If ControleDAcces(5, "ETAT_DES_VENTES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat Des Ventes" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEtatDesVentes = New fEtatDesVentes
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEtatDesVentes.Panel)
        Tab.SelectedTab.Text = "Etat Des Ventes"
        MyEtatDesVentes.init()
    End Sub

    Private Sub RibbonButton16_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

        If ControleDAcces(5, "ETAT_DES_VENTES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat Des Factures" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEtatDesFactures = New fEtatDesFactures
        MyEtatDesFactures.Source = "VenteClient"
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEtatDesFactures.Panel)
        Tab.SelectedTab.Text = "Etat Des Factures"
        MyEtatDesFactures.init()
    End Sub

    Private Sub RibbonButton15_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuotidienne.Click

        If ControleDAcces(5, "ETAT_DES_VENTES_PAR_JOUR") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat Des Vente Par Jour" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEtatDesVentesParJour = New fEtatDesVentesParJour
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEtatDesVentesParJour.Panel)
        Tab.SelectedTab.Text = "Etat Des Vente Par Jour"
        MyEtatDesVentesParJour.init()
    End Sub

    Private Sub RibbonButton18_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bMensuelle.Click

        If ControleDAcces(5, "ETAT_DES_VENTES_PAR_MOIS") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat Des Vente Par Mois" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEtatDesVentesParMois = New fEtatDesVentesParMois
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEtatDesVentesParMois.Panel)
        Tab.SelectedTab.Text = "Etat Des Vente Par Mois"
        MyEtatDesVentesParMois.init()
    End Sub

    Private Sub bJournaldesVentes_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bJournaldesVentes.Click

        If ControleDAcces(5, "ETAT_DES_VENTES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Journal Des Ventes" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyJournalDesVentes = New fEtatJournalDesVentes
        MyJournalDesVentes.Source = "VenteClient"
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyJournalDesVentes.Panel)
        Tab.SelectedTab.Text = "Journal Des Ventes"
        MyJournalDesVentes.init()
    End Sub

    Private Sub RibbonButton3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnuelle.Click

        If ControleDAcces(5, "ETAT_DES_VENTES_PAR_ANNEE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat Des Vente Par Année" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEtatDesVentesParAnnee = New fEtatDesVentesParAnnee
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEtatDesVentesParAnnee.Panel)
        Tab.SelectedTab.Text = "Etat Des Vente Par Année"
        MyEtatDesVentesParAnnee.init()
    End Sub

    Private Sub RibbonButton34_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bMouvementFornisseur.Click

        If ControleDAcces(6, "MOUVEMENT_FOURNISSEUR") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Mouvement Fournisseur" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyMouvementFournisseur = New fMouvementDesFournisseurs
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyMouvementFournisseur.Panel)
        Tab.SelectedTab.Text = "Mouvement Fournisseur"
        MyMouvementFournisseur.init()
    End Sub

    Private Sub RibbonButton35_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton35.Click

        If ControleDAcces(6, "ECHEANCIER_FOURNISSEUR") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Echéance Fournisseur" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEcheanceFournisseur = New fEcheancesDesFournisseurs
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEcheanceFournisseur.Panel)
        Tab.SelectedTab.Text = "Echéance Fournisseur"
        MyEcheanceFournisseur.init()
    End Sub

    Private Sub RibbonButton38_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton38.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Journal des achats" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyJournalDesAchats = New fEtatJournalDesAchats
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyJournalDesAchats.Panel)
        Tab.SelectedTab.Text = "Journal des achats"
        MyJournalDesAchats.init()
    End Sub

    Private Sub RibbonGroup5_DialogLauncherClick_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonGroup5.DialogLauncherClick

    End Sub

    Private Sub RibbonGroup6_DialogLauncherClick(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub bForme_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bForme.Click
        If ControleDAcces(10, "FORMES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Forme" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyForme = New fForme
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyForme.Panel)
        Tab.SelectedTab.Text = "Forme"
        MyForme.init()
    End Sub

    Private Sub bCategorie_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCategorie.Click
        If ControleDAcces(10, "CATEGORIES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Catégories" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyCategorie = New fCategorie
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyCategorie.Panel)
        Tab.SelectedTab.Text = "Catégories"
        MyCategorie.Init()
    End Sub

    Private Sub bIndemnite_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bIndemnite.Click
        If ControleDAcces(10, "INDEMNITES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Indemnite" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyIndemnite = New fIndemnite
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyIndemnite.Panel)
        Tab.SelectedTab.Text = "Indemnite"
        MyIndemnite.init()
    End Sub

    Private Sub bInteraction_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bInteraction.Click
        If ControleDAcces(10, "INTERACTION") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Intéraction" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyInteraction = New fInteractionsCreation
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyInteraction.Panel)
        Tab.SelectedTab.Text = "Intéraction"
        MyInteraction.init()
    End Sub

    Private Sub bBanque_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bBanque.Click
        If ControleDAcces(10, "BANQUES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Banque" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyBanque = New fBanque
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyBanque.Panel)
        Tab.SelectedTab.Text = "Banque"
        MyBanque.Init()
    End Sub

    Private Sub bVille_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bVille.Click
        If ControleDAcces(10, "VILLES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Ville" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        Dim MyVille As New fVille
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyVille.Panel)
        Tab.SelectedTab.Text = "Ville"
        MyVille.Init()
    End Sub

    Private Sub bLibelleES_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLibelleES.Click
        If ControleDAcces(10, "LIBELLES_ENTRES_SORTIES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Libelle Entrée Sortie" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyNatureEntreeSortie = New fNatureEntreeSortie
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyNatureEntreeSortie.Panel)
        Tab.SelectedTab.Text = "Libelle Entrée Sortie"
        MyNatureEntreeSortie.init()
    End Sub

    Private Sub bDCI_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bDCI.Click
        If ControleDAcces(10, "DCI") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "DCI" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyDCI = New fDCI
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyDCI.Panel)
        Tab.SelectedTab.Text = "DCI"
        MyDCI.Init()
    End Sub

    Private Sub bSpecialite_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSpecialite.Click
        If ControleDAcces(10, "SPECIALITE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Spécialité" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MySpecialiteMedecin = New fSpecialiteMedecin
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MySpecialiteMedecin.Panel)
        Tab.SelectedTab.Text = "Spécialité"
        MySpecialiteMedecin.Init()
    End Sub

    Private Sub bVentesSupprimees_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bVentesSupprimees.Click

        If ControleDAcces(9, "VENTES_SUPPRIMES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Vente Supprimées" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyVenteSupprime = New fVenteSupprime
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyVenteSupprime.Panel)
        Tab.SelectedTab.Text = "Vente Supprimées"
        MyVenteSupprime.init()
    End Sub

    Private Sub bProduction_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bProduction.Click

        If ControleDAcces(8, "PRODUCTION") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Production" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyProduction = New fProduction
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyProduction.Panel)
        Tab.SelectedTab.Text = "Production"
        MyProduction.Init()
    End Sub

    Private Sub bSuppressionDesVentes_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSuppressionDesVentes.Click

        If ControleDAcces(9, "SUPPRESSION_DES_VENTES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Suppression Vente" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MySupprimerVente = New fSuppressionDesVentes
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        MySupprimerVente.Source = "SUPRESSION"
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MySupprimerVente.Panel)
        Tab.SelectedTab.Text = "Suppression Vente"
        MySupprimerVente.Init()
    End Sub

    Private Sub bArticlesPerimes_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bArticlesPerimes.Click

        If ControleDAcces(8, "ARTICLES_PERIMES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat articles perimés" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyArticlePerimes = New fListeArticlePerime
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyArticlePerimes.Panel)
        Tab.SelectedTab.Text = "Etat articles perimés"
        MyArticlePerimes.Init()
    End Sub


    Private Sub bHitParadeArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bHitParadeArticle.Click

        If ControleDAcces(8, "HIT_PARADE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Hit Parade" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyHitParade = New fHitParadeArticle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyHitParade.Panel)
        Tab.SelectedTab.Text = "Hit Parade"
        MyHitParade.Init()
    End Sub

    Private Sub bTVAPercue_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bTVAPercue.Click

        If ControleDAcces(9, "TVA_PERCU") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Journal TVA Vente" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyJournalTVA = New fValeurTVAVente
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyJournalTVA.Panel)
        Tab.SelectedTab.Text = "Journal TVA Vente"
        MyJournalTVA.init()
    End Sub

    Private Sub bMouvementArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bMouvementArticle.Click

        'If ControleDAcces(8, "MOUVEMENT_DES_ARTICLES") = "False" Then
        '    Exit Sub
        'End If

        'Dim I As Integer
        'For I = 0 To Tab.TabPages.Count - 1
        '    If Tab.TabPages(I).Text = "Mouvement article" Then
        '        Tab.TabPages(I).Show()
        '        Exit Sub
        '    End If
        'Next
        'MyMouvementArticle = New fMouvementArticle
        'Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        'Tab.SelectedIndex = Tab.TabPages.Count - 1
        'Tab.SelectedTab.Controls.Add(MyMouvementArticle.Panel)
        'Tab.SelectedTab.Text = "Mouvement article"
        'MyMouvementArticle.init()
        'Dim I As Integer
        'For I = 0 To Tab.TabPages.Count - 1
        '    If Tab.TabPages(I).Text = "Mouvement des articles" Then
        '        Tab.TabPages(I).Show()
        '        Exit Sub
        '    End If
        'Next
        MyNMouvementArticle = New fNMouvementArticle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyNMouvementArticle.Panel)
        Tab.SelectedTab.Text = "Mouvement des articles"
        MyNMouvementArticle.init()
    End Sub

    Private Sub bMouvementPharmacie_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bMouvementPharmacie.Click

        If ControleDAcces(8, "MOUVEMENT_PHARMACIE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Mouvement pharmacie" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyMouvementPharmacie = New fMouvementPharmacien
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyMouvementPharmacie.Panel)
        Tab.SelectedTab.Text = "Mouvement pharmacie"
        MyMouvementPharmacie.Init()
    End Sub

    Private Sub bStockParCategorie_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bStockParCategorie.Click

        If ControleDAcces(8, "STOCK_PAR_CATEGORIE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Stock par catégorie" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyStockParCategorie = New fStockParCategorie
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyStockParCategorie.Panel)
        Tab.SelectedTab.Text = "Stock par catégorie"
        MyStockParCategorie.Init()
    End Sub

    Private Sub bJournalReleveCNAM_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bJournalReleveCNAM.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat Journal des relevés CNAM" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEtatJournalReleveCNAM = New fEtatJournalReleveCNAM
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEtatJournalReleveCNAM.Panel)
        Tab.SelectedTab.Text = "Etat Journal des relevés CNAM"
        MyEtatJournalReleveCNAM.Init()
    End Sub

    Private Sub bJournalReleveMutuelle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bJournalReleveMutuelle.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat Journal des relevés Mutuelle" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEtatJournalReleveMutuelle = New fEtatJournalReleveMutuelle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEtatJournalReleveMutuelle.Panel)
        Tab.SelectedTab.Text = "Etat Journal des relevés Mutuelle"
        MyEtatJournalReleveMutuelle.Init()
    End Sub

    Private Sub bOrdonnancier_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOrdonnancier.Click

        If ControleDAcces(9, "ORDONNANCIER") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat des ordonnanciers" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEtatOrdonnancier = New fEtatOrdonnancier
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEtatOrdonnancier.Panel)
        Tab.SelectedTab.Text = "Etat des ordonnanciers"
        MyEtatOrdonnancier.Init()
    End Sub

    Private Sub bOrdonnanceNonRegleCNAM_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOrdonnanceNonRegleCNAM.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat des ordonnances non reglées CNAM" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEtatOrdonnanceNonRegleCNAM = New fEtatOrdonnanceNonRegleCNAM
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEtatOrdonnanceNonRegleCNAM.Panel)
        Tab.SelectedTab.Text = "Etat des ordonnances non reglées CNAM"
        MyEtatOrdonnanceNonRegleCNAM.Init()
    End Sub

    Private Sub bOrdonnanceNonRegleMutuelle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOrdonnanceNonRegleMutuelle.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat des ordonnances non reglées Mutuelle" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEtatOrdonnanceNonRegleMutuelle = New fEtatOrdonnanceNonRegleMutuelle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEtatOrdonnanceNonRegleMutuelle.Panel)
        Tab.SelectedTab.Text = "Etat des ordonnances non reglées Mutuelle"
        MyEtatOrdonnanceNonRegleMutuelle.Init()
    End Sub


    Private Sub bTvaVenteParMois_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

        If ControleDAcces(9, "TVA_PAR_MOIS") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "TVA Vente / Mois" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyTvaParMois = New fValeurTVAVenteMois
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyTvaParMois.Panel)
        Tab.SelectedTab.Text = "TVA Vente / Mois"
        MyTvaParMois.init()
    End Sub

    Private Sub bTVAPaye_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bTVAPaye.Click

        If ControleDAcces(6, "TVA_PAYE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "TVA Achat" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyTvaAchat = New fValeurTVAAchat
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyTvaAchat.Panel)
        Tab.SelectedTab.Text = "TVA Achat"
        MyTvaAchat.init()
    End Sub

    Private Sub bMouvementCNAM_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bMouvementCNAM.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Mouvemnet CNAM" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyReleveMouvementCNAM = New fEtatReleveMouvementCNAM
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyReleveMouvementCNAM.Panel)
        Tab.SelectedTab.Text = "Mouvemnet CNAM"
        MyReleveMouvementCNAM.Init()
    End Sub

    Private Sub bMouvementMutuelle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bMouvementMutuelle.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Mouvement Mutuelle" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyReleveMouvementMutuelle = New fEtatReleveMouvementMutuelle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyReleveMouvementMutuelle.Panel)
        Tab.SelectedTab.Text = "Mouvement Mutuelle"
        MyReleveMouvementMutuelle.Init()
    End Sub
    'Private Sub bRecapCaisse_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
    '    Dim MyRecapCaisse As New fRecapitulatifCaisse
    '    MyRecapCaisse.ShowDialog()
    '    MyRecapCaisse.Close()
    '    MyRecapCaisse.Dispose()
    'End Sub

    'Private Sub bDetailsCaisse_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
    '    Dim I As Integer
    '    For I = 0 To Tab.TabPages.Count - 1
    '        If Tab.TabPages(I).Text = "Détails Caisse" Then
    '            Tab.TabPages(I).Show()
    '            Exit Sub
    '        End If
    '    Next
    '    MyDetailsCaisse = New fEtatRegelement
    '    Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
    '    Tab.SelectedIndex = Tab.TabPages.Count - 1
    '    Tab.SelectedTab.Controls.Add(MyDetailsCaisse.Panel)
    '    Tab.SelectedTab.Text = "Détails Caisse"
    '    MyDetailsCaisse.Init()
    'End Sub


    Private Sub bPreparationAProduire_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPreparationAProduire.Click

        If ControleDAcces(8, "PREPARATION_A_PRODUIRE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Préparation à produire" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyPreparationAProduire = New fListeDesPreparationAProduire
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyPreparationAProduire.Panel)
        Tab.SelectedTab.Text = "Préparation à produire"
        MyPreparationAProduire.init()
    End Sub

    Private Sub bStatistiqueFournisseur_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bStatistiqueFournisseur.Click

        If ControleDAcces(6, "STATISTIQUE_FOURNISSEUR") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Statistique Fournisseur" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyStatistiqueFournisseur = New fStatistiqueDesFournisseurs
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyStatistiqueFournisseur.Panel)
        Tab.SelectedTab.Text = "Statistique Fournisseur"
        MyStatistiqueFournisseur.init()
    End Sub

    Private Sub bSuiviDesBons_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSuiviDesBons.Click

        If ControleDAcces(9, "SUIVIE_DES_BONS") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Liste des bons" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyListeDesBons = New fListeDesBons
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyListeDesBons.Panel)
        Tab.SelectedTab.Text = "Liste des bons"
        MyListeDesBons.init()
    End Sub


    Private Sub bAchatSupprime_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAchatSupprime.Click

        If ControleDAcces(6, "ACHAT_SUPPRIMES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Achats supprimés" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyAchatSupprime = New fAchatSupprime
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyAchatSupprime.Panel)
        Tab.SelectedTab.Text = "Achats supprimés"
        MyAchatSupprime.init()
    End Sub


    Private Sub bStrategieDeStockage_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bStrategieDeStockage.Click

        If ControleDAcces(8, "STRATEGIE_DES_STOCKAGE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Simulation de stockage" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyStrategieDeStockage = New fStrategieDeStockage
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyStrategieDeStockage.Panel)
        Tab.SelectedTab.Text = "Simulation de stockage"
        MyStrategieDeStockage.Init()
    End Sub


    Private Sub bVenteAchatParCategorie_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bVenteAchatParCategorie.Click

        If ControleDAcces(9, "VENTES_ACHATS_PAR_CATEGORIE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Vente achat bénéfice par catégorie" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyChiffreAffaireParCategorie = New fEtatChiffreAffaireCategorie
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyChiffreAffaireParCategorie.Panel)
        Tab.SelectedTab.Text = "Vente achat bénéfice par catégorie"
        MyChiffreAffaireParCategorie.init()
    End Sub

    Private Sub bInformationUtilisateurs_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bInformationUtilisateurs.Click
        If ModeADMIN <> "ADMIN" Then
            MsgBox("Vous n'avez pas le droit d'accéder à cette rubrique !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Utilisateurs" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyUtilisateur = New fUtilisateur
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyUtilisateur.Panel)
        Tab.SelectedTab.Text = "Utilisateurs"
        MyUtilisateur.Init()
    End Sub

    Private Sub bGestionPonitsControle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bGestionPonitsControle.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Gestion des Points de Controle" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyGestionPointControle = New fGestionPointsControle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyGestionPointControle.Panel)
        Tab.SelectedTab.Text = "Gestion des Points de Controle"
        MyGestionPointControle.Init()
    End Sub


    Private Sub bAffectationDesAutorisations_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAffectationDesAutorisations.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Afféctation des autorisations" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyAffectationDesAutorisations = New fAffectationDesAutorisations
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyAffectationDesAutorisations.Panel)
        Tab.SelectedTab.Text = "Afféctation des autorisations"
        MyAffectationDesAutorisations.init()
    End Sub

    Private Sub bGestionDesProfil_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bGestionDesProfil.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Gestion des profils" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyGestionDesProfils = New fProfilUtilisateur
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyGestionDesProfils.Panel)
        Tab.SelectedTab.Text = "Gestion des profils"
        MyGestionDesProfils.Init()
    End Sub

    Private Sub bParametresConnexion_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bParametresConnexion.Click
        Dim MyBaseSQL As New fBaseSQL
        'MyBaseSQL.init()
        MyBaseSQL.ShowDialog()
        MyBaseSQL.Close()
        MyBaseSQL.Dispose()
    End Sub

    Private Sub bParametresPoste_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bParametresPoste.Click
        Dim MyParametrePoste As New fParametresPoste
        MyParametrePoste.init()
        MyParametrePoste.ShowDialog()
        MyParametrePoste.Close()
        MyParametrePoste.Dispose()
    End Sub

    Public Sub InitialiserMenu()


        Try
            bAmouvementArticle.Visible = RecupererValeurExecuteScalaire("HistoriqueMouvementArticle", "PARAMETRE_PHARMACIE", "Code", "1")
        Catch
        End Try

        If ModeADMIN <> "ADMIN" Then


            cmdMenu.Connection = ConnectionServeur
            cmdMenu.CommandText = "SELECT DISTINCT CodePointControle FROM AUTORISATION WHERE CodeUtilisateur =  " & Quote(CodeUtilisateur)
            daMenu = New SqlDataAdapter(cmdMenu)
            daMenu.Fill(dsMenu, "MENU")

            bAchatSupprime.Visible = False
            bArticleRemboursable.Visible = False
            bArticleNonRemboursable.Visible = False
            bArticlesPerimes.Visible = False
            bBanque.Visible = False
            bCategorieNonRemboursable.Visible = False
            bCategorieRemboursable.Visible = False
            bCategorie.Visible = False
            bDCI.Visible = False
            bDetailsCaisse.Visible = False
            bEcheancier.Visible = False
            bEmprunt.Visible = False
            bEntree.Visible = False
            bEtatEntree.Visible = False
            bEtatSortie.Visible = False
            bEtatInventaire.Visible = False
            bForme.Visible = False
            bHitParadeArticle.Visible = False
            bIndemnite.Visible = False
            bLaboratoire.Visible = False
            bMouvementArticles.Visible = False
            bMouvementFornisseur.Visible = False
            bMouvementPharmacie.Visible = False
            bOrdonnancier.Visible = False
            bOuvertureCaisse.Visible = False
            'bPharmacie.Visible = False
            bPret.Visible = False
            bProduction.Visible = False
            bRecapCaisseMenu.Visible = False
            bSaiseInventaire.Visible = False
            bSortie.Visible = False
            bSpecialite.Visible = False
            bStatistiqueFournisseur.Visible = False
            bStockParCategorie.Visible = False
            bStrategieDeStockage.Visible = False
            bSuiviDesBons.Visible = False
            'bTvaVenteParMois.Visible = False
            bTVAPaye.Visible = False
            bTVAPercue.Visible = False
            bPharmaUpdate.Visible = False
            bVenteAchatParCategorie.Visible = False
            bVentesSupprimees.Visible = False
            bVille.Visible = False
            bAmouvementArticle.Visible = False



            For I As Integer = 0 To dsMenu.Tables("MENU").Rows.Count - 1
                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "ACHAT_SUPPRIMES") Then
                    bAchatSupprime.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "ARTICLE_REMBOURSABLE") Then
                    bArticleRemboursable.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "ARTICLE_NON_REMBOURSABLE") Then
                    bArticleNonRemboursable.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "ARTICLES_PERIMES") Then
                    bArticlesPerimes.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "BANQUES") Then
                    bBanque.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "CATEGORIE_NON_REMBOURSABLE") Then
                    bCategorieNonRemboursable.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "CATEGORIE_REMBOURSABLE") Then
                    bCategorieRemboursable.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "CATEGORIES") Then
                    bCategorie.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "DCI") Then
                    bDCI.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "DETAILS_CAISSE") Then
                    bDetailsCaisse.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "ECHEANCIER") Then
                    bEcheancier.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "EMPRUNT") Then
                    bEmprunt.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "ENTREE") Then
                    bEntree.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "ETAT_DES_ENTREES_SORTIES") Then
                    bEtatEntree.Visible = True
                    bEtatSortie.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "FORMES") Then
                    bForme.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "HIT_PARADE") Then
                    bHitParadeArticle.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "INDEMNITES") Then
                    bIndemnite.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "LABORATOIRES") Then
                    bLaboratoire.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "MOUVEMENT_DES_ARTICLES") Then
                    bMouvementArticles.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "MOUVEMENT_FOURNISSEUR") Then
                    bMouvementFornisseur.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "MOUVEMENT_PHARMACIE") Then
                    bMouvementPharmacie.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "ORDONNANCIER") Then
                    bOrdonnancier.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "OUVRIR_CAISSE") Then
                    bOuvertureCaisse.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "Pharmacie") Then
                    bPharmacie.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "PRET") Then
                    bPret.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "PRODUCTION") Then
                    bProduction.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "RECAP_CAISSE") Then
                    bRecapCaisseMenu.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "SAISIE_DINVENTAIRE") Then
                    bSaiseInventaire.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "SORTIES") Then
                    bSortie.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "SPECIALITE") Then
                    bSpecialite.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "STATISTIQUE_FOURNISSEUR") Then
                    bStatistiqueFournisseur.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "STOCK_PAR_CATEGORIE") Then
                    bStockParCategorie.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "STRATEGIE_DES_STOCKAGE") Then
                    bStrategieDeStockage.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "SUIVIE_DES_BONS") Then
                    bSuiviDesBons.Visible = True
                End If

                'If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "TVA_PAR_MOIS") Then
                '    bTvaVenteParMois.Visible = True
                'End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "TVA_PAYE") Then
                    bTVAPaye.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "TVA_PERCU") Then
                    bTVAPercue.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "UPDATE_ARTICLE") Then
                    bPharmaUpdate.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "VENTES_ACHATS_PAR_CATEGORIE") Then
                    bVenteAchatParCategorie.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "VENTES_SUPPRIMES") Then
                    bVentesSupprimees.Visible = True
                End If

                If (dsMenu.Tables("MENU").Rows(I).Item("CodePointControle").ToString() = "VILLES") Then
                    bVille.Visible = True
                End If
            Next
        End If

        If ModeADMIN = "ADMIN" Then
            bAmouvementArticle.Visible = True
        End If

    End Sub

    Private Sub fMain_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        C1Ribbon1.Minimized = True


        ' Masquer le bureau
        Tab.Visible = False

        ' Paramètres de connexion
        MotDePasseSQL = GetSetting("PHARMA", "PHARMA", "MotDePasseSQL", "")
        NomUtilisateurSQL = GetSetting("PHARMA", "PHARMA", "NomUtilisateurSQL", "")
        NomBase = GetSetting("PHARMA", "PHARMA", "NomBase", "")
        NomServeur = GetSetting("PHARMA", "PHARMA", "NomServeur", "")

        MotDePasseSQLBCB = GetSetting("PHARMA", "PHARMA", "MotDePasseSqlBCB", "")
        NomUtilisateurSQLBCB = GetSetting("PHARMA", "PHARMA", "NomUtilisateurSqlBCB", "")
        NomBaseBCB = GetSetting("PHARMA", "PHARMA", "NomBaseBCB", "")
        NomServeurBCB = GetSetting("PHARMA", "PHARMA", "NomServeurBCB", "")
        'NomLogiqueDeLaMachine = GetSetting("PHARMA", "PHARMA", "NomMachine", "")

        Connect()
        ConnectDextherBCB()


        '-----------------------------------------------------------------------

        ApplicationOuvert = True

        Try
            Dim cmde As New SqlCommand
            cmde.Connection = ConnectionServeur
            cmde.CommandText = "SELECT AfficherAnalyse FROM PARAMETRES WHERE POSTE = " + Quote(System.Environment.GetEnvironmentVariable("Poste"))
            bAnalyseAchat.Enabled = cmde.ExecuteScalar()
            bAnalyseVente.Enabled = cmde.ExecuteScalar()
            bAnalyseVenteDetail.Enabled = cmde.ExecuteScalar()
        Catch
        End Try

        If connectionOK Then
            Dim mylogin As New fLogin
            mylogin.ShowDialog()
            mylogin.cmbLogin.Focus()
            If Not mylogin.reussi Then
                Me.Dispose()
                Exit Sub
            End If
        Else
            If MsgBox("Connexion à la base de données échouée. Voulez vous afficher les paramètres de connexion ?", MsgBoxStyle.DefaultButton1 + MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Erreur") = MsgBoxResult.Yes Then
                Dim fBase As New fBaseSQL
                fBase.ShowDialog()
                Connect()
                If Not connectionOK Then Me.Dispose()
                Dim mylogin As New fLogin
                mylogin.ShowDialog()
            ElseIf Not mylogin.reussi Then
                Me.Dispose()
            Else
                Me.Dispose()
                End
            End If
        End If

        'Rappel
        Try
            Dim cmdRap As New SqlCommand
            Dim dsRap As New DataSet
            Dim daRap As New SqlDataAdapter
            cmdRap.Connection = ConnectionServeur
            cmdRap.CommandText = "SELECT Nom, InformationRappel FROM CLIENT WHERE CONVERT(DATE, DateRappel) = CONVERT(DATE, " & Quote(Date.Now) & ")"
            daRap = New SqlDataAdapter(cmdRap)
            daRap.Fill(dsRap, "RAPPEL")

            Dim I As Integer = 0
            For I = 0 To dsRap.Tables("RAPPEL").Rows.Count - 1
                AfficheMessageNotification("Le client " & dsRap.Tables("RAPPEL").Rows(I).Item("Nom").ToString() & ":" & vbLf & dsRap.Tables("RAPPEL").Rows(I).Item("InformationRappel").ToString(), "Client", 5000000)
            Next
        Catch
        End Try


        ' Afficher le bureau
        Tab.Visible = True
        NombreOnglet = Tab.TabPages.Count - 1

        For J As Integer = 0 To NombreOnglet
            If Tab.TabPages(J).Text <> "Bureau" Then
                Tab.TabPages(J).Dispose()
                NombreOnglet = Tab.TabPages.Count - 1
                J = 0
                If NombreOnglet = 0 Then
                    Exit For
                End If
            End If
        Next

        ActualiserStatusBar()

        If ModeADMIN <> "ADMIN" Then
            RibbonTab5.Visible = False
        Else
            RibbonTab5.Visible = True
            bCorrectionLotArticle.Visible = False
        End If
        'bChangerUser.Visible = False

        If System.Environment.GetEnvironmentVariable("POSTE") = Nothing Then
            MsgBox("Numéro de poste inexistant !", MsgBoxStyle.Critical, "Erreur")
            Me.Dispose()
        End If

        'régler les paramtres regionaux
        DecimalSeparator = "."

        ' ajout du nom de la poste dans la table POSTE 
        Dim cmd As New SqlCommand
        'MessageBox.Show(System.Environment.GetEnvironmentVariable("Poste"))
        cmd.Connection = ConnectionServeur
        cmd.CommandText = "SELECT COUNT(LibellePoste) FROM POSTE WHERE LibellePoste LIKE '" + System.Environment.GetEnvironmentVariable("Poste") + "'" 'Environment.MachineName.ToString 
        Try
            If cmd.ExecuteScalar() < 1 Then
                cmd.CommandText = "INSERT INTO POSTE (""LibellePoste"") VALUES ('" + System.Environment.GetEnvironmentVariable("Poste") + "')"
                Try
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
            End If
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        '********************************** affichage de la vente
        'bVente_Click(sender, e)
        'Dim I As Integer
        'For I = 0 To Tab.TabPages.Count - 1
        '    If Tab.TabPages(I).Text = "Bureau" Then
        '        Me.Tab.SelectedTab.Dispose()
        '        GoTo Suivant
        '    End If
        'Next

Suivant:

        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Vente" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next

        Dim MyVente As New fVente
        MyVente.Text = "Vente" + IndexVente.ToString().ToString()
        IndexVente = IndexVente + 1
        MyVentes.Add(MyVente)
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyVente.Panel)
        Tab.SelectedTab.Text = MyVente.Text
        MyVente.Init()
        MyVente.bAjouter_Click(sender, e)

        '*******************************************************

        ' ajout du nom de la poste dans la table PARAMETRES 
        'Dim cmd As New SqlCommand
        cmd.Connection = ConnectionServeur
        cmd.CommandText = "SELECT COUNT(POSTE) FROM PARAMETRES  WHERE POSTE LIKE '" + System.Environment.GetEnvironmentVariable("Poste") + "'"
        Try
            If cmd.ExecuteScalar() < 1 Then
                cmd.CommandText = "INSERT INTO PARAMETRES (""POSTE""" + _
                                                     ",""HonoraireDeResponsabiliteTA""" + _
                                                     ",""HonoraireDeResponsabiliteTB""" + _
                                                     ",""HonoraireDeResponsabiliteTC""" + _
                                                     ",""MinimumDePerception""" + _
                                                     ",""RechercheArticleForme""" + _
                                                     ",""RechercheArticleDCI""" + _
                                                     ",""RechercheArticlePrixTTC""" + _
                                                     ",""AutoriserLesMiseAjourEnLigne""" + _
                                                     ",""AutoriserLesChangementsAutomatiquesDesPrixPreparation""" + _
                                                     ",""ImprimanteATicket""" + _
                                                     ",""Tiroir""" + _
                                                     ",""USB""" + _
                                                     ",""COM""" + _
                                                     ",""ImprimerLesBons""" + _
                                                     ",""ValiderQteEgalA1""" + _
                                                     ",""ControlerLeNombredesUnitesvendues""" + _
                                                     ",""NombreDesTentativesPourControlerLeNombreDesArticles""" + _
                                                     ",""CreationDesClientsCreditAuNiveauPreparateur""" + _
                                                     ",""InscriptionAutomatiqueSurOrdonnacier""" + _
                                                     ",""InterdirelaVenteDesProduitsPerimes""" + _
                                                     ",""VerifierSiProduitPrisEnChargeParLaCNAM""" + _
                                                     ",""DureeAffichageDesAlertes""" + _
                                                     ",""InterdireDeChoisirLesArticlesParDesignation""" + _
                                                     ",""RetrancherDuStockLorsDuneVenteenInstance""" + _
                                                     ",""DerniersJours""" + _
                                                     ",""CmdGroupeJournaliere""" + _
                                                     ",""QteMultipleDe5""" + _
                                                     ",""NePAsSortirLesManquantsDepuis""" + _
                                                     ",""CongeDuAnneeCourant""" + _
                                                     ",""CongeAuAnneeCourant""" + _
                                                     ",""CongeDuAnneeProchaine""" + _
                                                     ",""CongeAuAnneeProchaine""" + _
                                                     ",""NomDeLordinateurDImpressionCodeABarre""" + _
                                                     ",""NePAsMettreAJourLaFicheArticleSiLePrixChange""" + _
                                                     ",""InterventionAvecUnAssistantSiLePrixChange""" + _
                                                     ",""AfficherLesDernieresDatesDePeremptionDansLesNouveauxAchats""" + _
                                                     ",""LecteurUpdate""" + _
                                                     ",""LecteurSauvegarde""" + _
                                                     ",""NomDeLImprimante""" + _
                                                     ",""AjouterTimbreAFacture"") " + _
                                  "VALUES ('" + System.Environment.GetEnvironmentVariable("Poste") + _
                                          "','0.080" + _
                                          "','0.100" + _
                                          "','0.080" + _
                                          "','0.250" + _
                                          "','False" + _
                                          "','False" + _
                                          "','False" + _
                                          "','False" + _
                                          "','False" + _
                                          "','False" + _
                                          "','False" + _
                                          "','False" + _
                                          "','" + _
                                          "','False" + _
                                          "','False" + _
                                          "','False" + _
                                          "','3" + _
                                          "','False" + _
                                          "','False" + _
                                          "','False" + _
                                          "','False" + _
                                          "','" + _
                                          "','False" + _
                                          "','False" + _
                                          "','" + _
                                          "','" + _
                                          "','False" + _
                                          "','" + _
                                          "',NULL" + _
                                          ",NULL" + _
                                          ",NULL" + _
                                          ",NULL" + _
                                          ",'" + _
                                          "','False" + _
                                          "','False" + _
                                          "','False" + _
                                          "',''" + _
                                          ",''" + _
                                          ",''" + _
                                          ",'False')"
                Try
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
            End If
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        ' lecture des paramètres globaux

        If RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTA", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            HonoraireTableauA = RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTA", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            HonoraireTableauA = 0
        End If

        If RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTB", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            HonoraireTableauB = RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTB", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            HonoraireTableauB = 0
        End If

        If RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTC", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            HonoraireTableauC = RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTC", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            HonoraireTableauC = 0
        End If

        If RecupererValeurExecuteScalaire("MinimumDePerception", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            MinimumDePerception = RecupererValeurExecuteScalaire("MinimumDePerception", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            MinimumDePerception = 0
        End If

        If RecupererValeurExecuteScalaire("AutoriserLesMiseAjourEnLigne", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            AutoriserMiseAjoursEnLigneArticles = RecupererValeurExecuteScalaire("AutoriserLesMiseAjourEnLigne", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            AutoriserMiseAjoursEnLigneArticles = False
        End If

        If RecupererValeurExecuteScalaire("AutoriserLesChangementsAutomatiquesDesPrixPreparation", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            AutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations = RecupererValeurExecuteScalaire("AutoriserLesChangementsAutomatiquesDesPrixPreparation", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            AutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations = False
        End If

        If RecupererValeurExecuteScalaire("ImprimanteATicket", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            ImprimanteATicket = RecupererValeurExecuteScalaire("ImprimanteATicket", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            ImprimanteATicket = False
        End If

        If RecupererValeurExecuteScalaire("Tiroir", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            Tiroir = RecupererValeurExecuteScalaire("Tiroir", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            Tiroir = False
        End If

        If RecupererValeurExecuteScalaire("USB", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            TiroirUSB = RecupererValeurExecuteScalaire("USB", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            TiroirUSB = False
        End If

        If RecupererValeurExecuteScalaire("COM", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            TiroirCOM = RecupererValeurExecuteScalaire("COM", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            TiroirCOM = False
        End If

        If RecupererValeurExecuteScalaire("ImprimerLesBons", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            ImprimerBon = RecupererValeurExecuteScalaire("ImprimerLesBons", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            ImprimerBon = False
        End If

        If RecupererValeurExecuteScalaire("ValiderQteEgalA1", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            ValiderQteEgalA1 = RecupererValeurExecuteScalaire("ValiderQteEgalA1", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            ValiderQteEgalA1 = False
        End If

        If RecupererValeurExecuteScalaire("ControlerLeNombredesUnitesvendues", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            ControleNombreUnites = RecupererValeurExecuteScalaire("ControlerLeNombredesUnitesvendues", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            ControleNombreUnites = False
        End If

        If RecupererValeurExecuteScalaire("NombreDesTentativesPourControlerLeNombreDesArticles", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            NbrDesTentatives = RecupererValeurExecuteScalaire("NombreDesTentativesPourControlerLeNombreDesArticles", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            NbrDesTentatives = 1
        End If

        If RecupererValeurExecuteScalaire("CreationDesClientsCreditAuNiveauPreparateur", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            CreationDesClientsCreditDansNiveauPreparateur = RecupererValeurExecuteScalaire("CreationDesClientsCreditAuNiveauPreparateur", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            CreationDesClientsCreditDansNiveauPreparateur = False
        End If

        If RecupererValeurExecuteScalaire("InscriptionAutomatiqueSurOrdonnacier", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            InscriptionSurOrdonnancierAutomatique = RecupererValeurExecuteScalaire("InscriptionAutomatiqueSurOrdonnacier", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            InscriptionSurOrdonnancierAutomatique = False
        End If

        If RecupererValeurExecuteScalaire("InterdirelaVenteDesProduitsPerimes", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            InterdireLaVenteDesPerimes = RecupererValeurExecuteScalaire("InterdirelaVenteDesProduitsPerimes", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            InterdireLaVenteDesPerimes = False
        End If

        If RecupererValeurExecuteScalaire("VerifierSiProduitPrisEnChargeParLaCNAM", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            VerifierProduitPrisEnChargeParCNAM = RecupererValeurExecuteScalaire("VerifierSiProduitPrisEnChargeParLaCNAM", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            VerifierProduitPrisEnChargeParCNAM = False
        End If

        'If RecupererValeurExecuteScalaire("PermettreUtiliserLesFrigosEnVente", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
        '    PermettreUtiliserFrigosEuVente = RecupererValeurExecuteScalaire("PermettreUtiliserLesFrigosEnVente", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        'Else
        '    PermettreUtiliserFrigosEuVente = False
        'End If


        If RecupererValeurExecuteScalaire("InterdireDeChoisirLesArticlesParDesignation", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            InterdireChoisirParDesignation = RecupererValeurExecuteScalaire("InterdireDeChoisirLesArticlesParDesignation", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            InterdireChoisirParDesignation = False
        End If

        If RecupererValeurExecuteScalaire("RetrancherDuStockLorsDuneVenteenInstance", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            RetarancheDuStockLorsDeVenteInstance = RecupererValeurExecuteScalaire("RetrancherDuStockLorsDuneVenteenInstance", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            RetarancheDuStockLorsDeVenteInstance = False
        End If

        If RecupererValeurExecuteScalaire("CmdGroupeJournaliere", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            CommandeGroupeJ = RecupererValeurExecuteScalaire("CmdGroupeJournaliere", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            CommandeGroupeJ = ""
        End If

        If RecupererValeurExecuteScalaire("NePAsSortirLesManquantsDepuis", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            NePasSortirManquantsDepuis = RecupererValeurExecuteScalaire("NePAsSortirLesManquantsDepuis", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            NePasSortirManquantsDepuis = ""
        End If

        If RecupererValeurExecuteScalaire("CongeDuAnneeCourant", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            CongeDuAnneeCourant = RecupererValeurExecuteScalaire("CongeDuAnneeCourant", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            'CongeDuAnneeCourant = ""
        End If

        If RecupererValeurExecuteScalaire("CongeAuAnneeCourant", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            CongeAuAnneeCourant = RecupererValeurExecuteScalaire("CongeAuAnneeCourant", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            'CongeAuAnneeCourant = ""
        End If

        If RecupererValeurExecuteScalaire("CongeDuAnneeProchaine", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            CongeDuAnneeProchaine = RecupererValeurExecuteScalaire("CongeDuAnneeProchaine", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            'CongeDuAnneeProchaine = ""
        End If

        If RecupererValeurExecuteScalaire("CongeAuAnneeProchaine", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            CongeAuAnneeProchaine = RecupererValeurExecuteScalaire("CongeAuAnneeProchaine", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            'CongeAuAnneeProchaine = ""
        End If

        If RecupererValeurExecuteScalaire("NomDeLordinateurDImpressionCodeABarre", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            NomOrdinateurImpressionCodeABarre = RecupererValeurExecuteScalaire("NomDeLordinateurDImpressionCodeABarre", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            NomOrdinateurImpressionCodeABarre = ""
        End If

        If RecupererValeurExecuteScalaire("InterventionAvecUnAssistantSiLePrixChange", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            InterventionAvecUnAssistant = RecupererValeurExecuteScalaire("InterventionAvecUnAssistantSiLePrixChange", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            InterventionAvecUnAssistant = False
        End If

        If RecupererValeurExecuteScalaire("AfficherLesDernieresDatesDePeremptionDansLesNouveauxAchats", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            AfficherLesDerniereDDPeremptionDansNouveauAvoirAchat = RecupererValeurExecuteScalaire("AfficherLesDernieresDatesDePeremptionDansLesNouveauxAchats", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            AfficherLesDerniereDDPeremptionDansNouveauAvoirAchat = False
        End If

        If RecupererValeurExecuteScalaire("AjouterTimbreAFacture", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            AjouterTimbreALaFacture = RecupererValeurExecuteScalaire("AjouterTimbreAFacture", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            AjouterTimbreALaFacture = False
        End If

        If RecupererValeurExecuteScalaire("MinimumDePerception", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            MinimumDePerception = RecupererValeurExecuteScalaire("MinimumDePerception", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            MinimumDePerception = 0
        End If

        If RecupererValeurExecuteScalaire("LecteurUpdate", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            LecteurUpdate = RecupererValeurExecuteScalaire("LecteurUpdate", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            LecteurUpdate = ""
        End If

        If RecupererValeurExecuteScalaire("LecteurSauvegarde", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            LecteurSauvegarde = RecupererValeurExecuteScalaire("LecteurSauvegarde", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            LecteurSauvegarde = ""
        End If

        If RecupererValeurExecuteScalaire("DelaiSauvegarde", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            DelaiSauvegarde = RecupererValeurExecuteScalaire("DelaiSauvegarde", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            DelaiSauvegarde = 0
        End If

        If RecupererValeurExecuteScalaire("NomDeLImprimante", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            NomDeLImprimante = RecupererValeurExecuteScalaire("NomDeLImprimante", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Else
            NomDeLImprimante = ""
        End If


        Try
            MettreAJourPrixFrigo = RecupererValeurExecuteScalaire("MettreAJourPrixFrigo", "PARAMETRE_PHARMACIE", "Code", 1)
        Catch ex As Exception
            MettreAJourPrixFrigo = False
        End Try

        Try
            AfficherReglementsSupprimes = RecupererValeurExecuteScalaire("AfficherReglementsSupprimes", "PARAMETRE_PHARMACIE", "Code", 1)
        Catch ex As Exception
            AfficherReglementsSupprimes = False
        End Try

        Try
            AutoriserSaisieNonMembeFamille = RecupererValeurExecuteScalaire("AutoriserSaisieNonMembeFamille", "PARAMETRE_PHARMACIE", "Code", 1)
        Catch ex As Exception
            AutoriserSaisieNonMembeFamille = False
        End Try

        Try
            ImprimerUnEtiquette = RecupererValeurExecuteScalaire("ImprimerUnEtiquette", "PARAMETRE_PHARMACIE", "Code", 1)
        Catch ex As Exception
            ImprimerUnEtiquette = False
        End Try


        '*****************************************
        cmd.Connection = ConnectionServeur
        cmd.CommandText = "SELECT CodePharmacie FROM PARAMETRE_PHARMACIE "
        Try
            CodePharmacien = cmd.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '*****************************************

        'If RecupererValeurExecuteScalaire("CodePharmacie", "PARAMETRE_PHARMACIE", "CodePharmacie", 1) <> "" Then
        '    CodePharmacien = RecupererValeurExecuteScalaire("CodePharmacie", "PARAMETRE_PHARMACIE", "CodePharmacie", 1)
        'Else
        '    CodePharmacien = ""
        'End If

        If RecupererValeurExecuteScalaire("Pharmacie", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
            Pharmacie = RecupererValeurExecuteScalaire("Pharmacie", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
        Else
            Pharmacie = ""
        End If
        If RecupererValeurExecuteScalaire("NCnam", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
            CNAMPharmacien = RecupererValeurExecuteScalaire("NCnam", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
        Else
            CNAMPharmacien = ""
        End If
        If RecupererValeurExecuteScalaire("Affiliation1", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
            NumeroAffiliation1 = RecupererValeurExecuteScalaire("Affiliation1", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
        Else
            NumeroAffiliation1 = ""
        End If
        If RecupererValeurExecuteScalaire("Affiliation2", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
            NumeroAffiliation2 = RecupererValeurExecuteScalaire("Affiliation2", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
        Else
            NumeroAffiliation2 = ""
        End If
        If RecupererValeurExecuteScalaire("Adresse", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
            AdressePharmacien = RecupererValeurExecuteScalaire("Adresse", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
        Else
            AdressePharmacien = ""
        End If

        If RecupererValeurExecuteScalaire("Telephone", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
            TelephonePharmacien = RecupererValeurExecuteScalaire("Telephone", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
        Else
            TelephonePharmacien = ""
        End If

        If RecupererValeurExecuteScalaire("Fax", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
            FaxPharmacien = RecupererValeurExecuteScalaire("Fax", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
        Else
            FaxPharmacien = ""
        End If

        If RecupererValeurExecuteScalaire("CodeTVA", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
            CodeTvaPharmacien = RecupererValeurExecuteScalaire("CodeTVA", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
        Else
            CodeTvaPharmacien = ""
        End If

        If RecupererValeurExecuteScalaire("Rib", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
            Rib = RecupererValeurExecuteScalaire("Rib", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
        Else
            Rib = ""
        End If

        If RecupererValeurExecuteScalaire("Messagederoulant", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
            Messagederoulant = RecupererValeurExecuteScalaire("Messagederoulant", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
        Else
            Messagederoulant = ""
        End If

        If RecupererValeurExecuteScalaire("Timbre", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
            Timbre = RecupererValeurExecuteScalaire("Timbre", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
        Else
            Timbre = 0
        End If

        If RecupererValeurExecuteScalaire("DateMigration", "PARAMETRE_PHARMACIE", "Code", 1) <> "" Then
            DateMigration = RecupererValeurExecuteScalaire("DateMigration", "PARAMETRE_PHARMACIE", "Code", 1)
        Else
            DateMigration = Nothing
        End If


        'Recupération des text afficher sur le boutons 
        Me.bLien1.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien1")
        Me.bLien2.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien2")
        Me.bLien3.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien3")
        Me.bLien4.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien4")
        Me.bLien5.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien5")
        Me.bLien6.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien6")
        Me.bLien7.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien7")
        Me.bLien8.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien8")
        Me.bLien9.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien9")
        Me.bLien10.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien10")
        Me.bLien11.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien11")
        Me.bLien12.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien12")
        Me.bLien13.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien13")
        Me.bLien14.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien14")
        Me.bLien15.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien15")
        Me.bLien16.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien16")

        If Me.bLien1.Text = "" Then
            Me.bLien1.Text = "?"
        End If
        If Me.bLien2.Text = "" Then
            Me.bLien2.Text = "?"
        End If
        If Me.bLien3.Text = "" Then
            Me.bLien3.Text = "?"
        End If
        If Me.bLien4.Text = "" Then
            Me.bLien4.Text = "?"
        End If
        If Me.bLien5.Text = "" Then
            Me.bLien5.Text = "?"
        End If
        If Me.bLien6.Text = "" Then
            Me.bLien6.Text = "?"
        End If
        If Me.bLien7.Text = "" Then
            Me.bLien7.Text = "?"
        End If
        If Me.bLien8.Text = "" Then
            Me.bLien8.Text = "?"
        End If
        If Me.bLien9.Text = "" Then
            Me.bLien9.Text = "?"
        End If
        If Me.bLien10.Text = "" Then
            Me.bLien10.Text = "?"
        End If
        If Me.bLien11.Text = "" Then
            Me.bLien11.Text = "?"
        End If
        If Me.bLien12.Text = "" Then
            Me.bLien12.Text = "?"
        End If
        If Me.bLien13.Text = "" Then
            Me.bLien13.Text = "?"
        End If
        If Me.bLien14.Text = "" Then
            Me.bLien14.Text = "?"
        End If
        If Me.bLien15.Text = "" Then
            Me.bLien15.Text = "?"
        End If
        If Me.bLien16.Text = "" Then
            Me.bLien16.Text = "?"
        End If

        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler


        ''''''''''''''
        CorrectionLotsVide()
        '''''''''''''

        If CodeUtilisateur <> "NEXT" Then
            initIconBureau(-1)

            'Afficher Bureau
            AffciherBureau()
        End If

        Timer1.Start()




        'pour affiche la notification de la message d'envoi
        Me.NotifyIcon_MessageMain.Icon = SystemIcons.Information
        Me.NotifyIcon_MessageMain.Icon = SystemIcons.Application ' Affiche l'icône par défaut.
        Me.NotifyIcon_MessageMain.Icon = SystemIcons.Error ' Affiche l'icône d'erreur.
        Me.NotifyIcon_MessageMain.Icon = SystemIcons.Warning ' Affiche l'icône de danger.
        Me.NotifyIcon_MessageMain.Icon = SystemIcons.Question ' Affiche l'icône de question.
        Me.NotifyIcon_MessageMain.Icon = SystemIcons.Shield
        'SendMail()

        InitialiserMenu()

    End Sub

    Private Sub CorrectionLotsVide()

        '    Dim StrSQL As String = ""

        '    Dim cmdLot As New SqlCommand
        '    Dim dsLot As New DataSet
        '    Dim daLot As New SqlDataAdapter
        '    Dim cbLot As New SqlCommandBuilder


        '    'Try
        '    '    cmdLot.Connection = ConnectionServeur
        '    '    cmdLot.CommandText = "UPDATE LOT_ARTICLE " + _
        '    '                         "Set DatePeremptionArticle = NULL " + _
        '    '                         "where NumeroLotArticle = '' and DatePeremptionArticle is not NULL "
        '    '    cmdLot.ExecuteNonQuery()
        '    'Catch ex As Exception
        '    '    Console.Write(ex.Message)
        '    'End Try


        '    Try
        '        dsLot.Tables("ARTICLE_SANS_LOT").Clear()
        '    Catch ex As Exception
        '    End Try

        '    StrSQL = "select * from ARTICLE " + _
        '             "where CodeArticle not in " + _
        '             "(select CodeArticle from LOT_ARTICLE where NumeroLotArticle = '')"

        '    cmdLot.Connection = ConnectionServeur
        '    cmdLot.CommandText = StrSQL
        '    daLot = New SqlDataAdapter(cmdLot)
        '    daLot.Fill(dsLot, "ARTICLE_SANS_LOT")
        '    cbLot = New SqlCommandBuilder(daLot)

        '    For i = 0 To dsLot.Tables("ARTICLE_SANS_LOT").Rows.Count - 1
        '        Try
        '            cmdLot.Connection = ConnectionServeur
        '            cmdLot.CommandText = " INSERT INTO LOT_ARTICLE (NumeroLotArticle ,CodeArticle) values ( " + _
        '                                "''," + Quote(dsLot.Tables("ARTICLE_SANS_LOT").Rows(i).Item("CodeArticle")) + ")"
        '            cmdLot.ExecuteNonQuery()
        '        Catch ex As Exception
        '            Console.Write(ex.Message)
        '        End Try
        '    Next

    End Sub


    Private Sub CorrectionDateLotArticle()

        Dim StrSQL As String = ""

        Dim cmdLot As New SqlCommand
        Dim dsLot As New DataSet
        Dim daLot As New SqlDataAdapter
        Dim cbLot As New SqlCommandBuilder


        If ModeADMIN <> "ADMIN" Then
            MsgBox("Vous n'avez pas le droit d'executer cette fonction")
            Exit Sub
        End If


        Try
            dsLot.Tables("ARTICLE_WASSIM").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "select NumeroVente, CodeArticle, DateDePeremption, SUM(qte) as qte " + _
                    "from VENTE_DETAILS " + _
                    "group by NumeroVente, CodeArticle, DateDePeremption " + _
                    "having COUNT(*)> 1 "

        cmdLot.Connection = ConnectionServeur
        cmdLot.CommandText = StrSQL
        daLot = New SqlDataAdapter(cmdLot)
        daLot.Fill(dsLot, "ARTICLE_WASSIM")
        cbLot = New SqlCommandBuilder(daLot)


        For i = 0 To dsLot.Tables("ARTICLE_WASSIM").Rows.Count - 1

            Try
                cmdLot.Connection = ConnectionServeur
                StrSQL = "update VENTE_DETAILS " + _
                        " set Qte = (select SUM(qte) " + _
                        " from VENTE_DETAILS " + _
                        " where NumeroVente =  " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("NumeroVente")) + _
                        " and CodeArticle = " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("CodeArticle")) + _
                        " and DateDePeremption = " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("DateDePeremption")) + _
                        " group by NumeroVente, CodeArticle, DateDePeremption " + _
                        " having COUNT(*)> 1) " + _
                        " where NumeroVente = " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("NumeroVente")) + _
                        " and CodeArticle = " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("CodeArticle")) + _
                        " and DateDePeremption = " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("DateDePeremption"))
                cmdLot.CommandText = StrSQL
                cmdLot.ExecuteNonQuery()
            Catch ex As Exception
                Console.Write(ex.Message)
            End Try

            Try
                cmdLot.Connection = ConnectionServeur
                StrSQL = "update VENTE_DETAILS " + _
                         " SET TotalHT = Qte * PrixHT, " + _
                         " TotalTTC = PrixTTC * Qte " + _
                         " where NumeroVente = " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("NumeroVente")) + _
                         " and CodeArticle = " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("CodeArticle")) + _
                         " and DateDePeremption = " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("DateDePeremption"))
                cmdLot.CommandText = StrSQL
                cmdLot.ExecuteNonQuery()
            Catch ex As Exception
                Console.Write(ex.Message)
            End Try

            Try
                cmdLot.Connection = ConnectionServeur
                StrSQL = "DELETE FROM VENTE_DETAILS " + _
                        " where NumeroLotArticle not in (select MIN(NumeroLotArticle) " + _
                        " from VENTE_DETAILS " + _
                        " where NumeroVente =  " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("NumeroVente")) + _
                        " and CodeArticle = " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("CodeArticle")) + _
                        " and DateDePeremption = " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("DateDePeremption")) + _
                        " ) " + _
                        " and NumeroVente = " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("NumeroVente")) + _
                        " and DateDePeremption = " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("DateDePeremption")) + _
                        " and Codearticle = " + Quote(dsLot.Tables("ARTICLE_WASSIM").Rows(i).Item("CodeArticle"))
                cmdLot.CommandText = StrSQL
                cmdLot.ExecuteNonQuery()
            Catch ex As Exception
                Console.Write(ex.Message)
            End Try
        Next



        Try
            dsLot.Tables("INVENTAIRE_WASSIM").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "select NumeroInventaire, INVENTAIRE_DETAILS.CodeArticle, DatePeremptionArticle " + _
                " from INVENTAIRE_DETAILS " + _
                " join LOT_ARTICLE on INVENTAIRE_DETAILS.NumeroLot = LOT_ARTICLE.NumeroLotArticle  and INVENTAIRE_DETAILS.CodeArticle = LOT_ARTICLE.CodeArticle " + _
                " group by NumeroInventaire, INVENTAIRE_DETAILS.CodeArticle, DatePeremptionArticle " + _
                " having COUNT(*)>1 "

        cmdLot.Connection = ConnectionServeur
        cmdLot.CommandText = StrSQL
        daLot = New SqlDataAdapter(cmdLot)
        daLot.Fill(dsLot, "INVENTAIRE_WASSIM")
        cbLot = New SqlCommandBuilder(daLot)


        For i = 0 To dsLot.Tables("INVENTAIRE_WASSIM").Rows.Count - 1

            Try
                cmdLot.Connection = ConnectionServeur
                StrSQL = "update INVENTAIRE_DETAILS " + _
                        "set StockActuel = (SELECT SUM(StockActuel) " + _
                        "				   from INVENTAIRE_DETAILS " + _
                        "				   join LOT_ARTICLE on INVENTAIRE_DETAILS.NumeroLot = LOT_ARTICLE.NumeroLotArticle  and INVENTAIRE_DETAILS.CodeArticle = LOT_ARTICLE.CodeArticle " + _
                        "				   WHERE NumeroInventaire = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("NumeroInventaire")) + _
                        "				   and INVENTAIRE_DETAILS.CodeArticle = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("CodeArticle")) + _
                        "				   and DatePeremptionArticle = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("DatePeremptionArticle")) + _
                        "				   group by NumeroInventaire, INVENTAIRE_DETAILS.CodeArticle, DatePeremptionArticle " + _
                        "				   having COUNT(*)>1 " + _
                        "				    ) " + _
                        ", StockInitial = (SELECT SUM(StockInitial ) " + _
                        "				   from INVENTAIRE_DETAILS " + _
                        "				   join LOT_ARTICLE on INVENTAIRE_DETAILS.NumeroLot = LOT_ARTICLE.NumeroLotArticle  and INVENTAIRE_DETAILS.CodeArticle = LOT_ARTICLE.CodeArticle " + _
                        "				   WHERE NumeroInventaire = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("NumeroInventaire")) + _
                        "				   and INVENTAIRE_DETAILS.CodeArticle = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("CodeArticle")) + _
                        "				   and DatePeremptionArticle = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("DatePeremptionArticle")) + _
                        "				   group by NumeroInventaire, INVENTAIRE_DETAILS.CodeArticle, DatePeremptionArticle " + _
                        "				   having COUNT(*)>1 " + _
                        "				    ) " + _
                        "WHERE NumeroInventaire = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("NumeroInventaire")) + _
                        "and INVENTAIRE_DETAILS.CodeArticle = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("CodeArticle")) + _
                        "and NumeroLot  in ( select NumeroLotArticle from LOT_ARTICLE where CodeArticle =" + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("CodeArticle")) + " and DatePeremptionArticle = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("DatePeremptionArticle")) + " ) "

                cmdLot.CommandText = StrSQL
                cmdLot.ExecuteNonQuery()
            Catch ex As Exception
                Console.Write(ex.Message)
            End Try

            Try
                cmdLot.Connection = ConnectionServeur
                StrSQL = "update INVENTAIRE_DETAILS " + _
                         " SET TotalAchatTTC = StockActuel * PrixAchatTTC " + _
                         " where NumeroInventaire = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("NumeroInventaire")) + _
                         "and INVENTAIRE_DETAILS.CodeArticle = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("CodeArticle")) + _
                         "and NumeroLot  in ( select NumeroLotArticle from LOT_ARTICLE where CodeArticle =" + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("CodeArticle")) + " and DatePeremptionArticle = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("DatePeremptionArticle")) + " ) "

                cmdLot.CommandText = StrSQL
                cmdLot.ExecuteNonQuery()
            Catch ex As Exception
                Console.Write(ex.Message)
            End Try

            Try
                cmdLot.Connection = ConnectionServeur
                StrSQL = "delete from INVENTAIRE_DETAILS " + _
                        "where NumeroLot not in (select MIN(NumeroLot) " + _
                        "						from INVENTAIRE_DETAILS " + _
                        "						join LOT_ARTICLE on INVENTAIRE_DETAILS.NumeroLot = LOT_ARTICLE.NumeroLotArticle  and INVENTAIRE_DETAILS.CodeArticle = LOT_ARTICLE.CodeArticle " + _
                        "						WHERE NumeroInventaire =  " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("NumeroInventaire")) + _
                        "						and INVENTAIRE_DETAILS.CodeArticle = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("CodeArticle")) + _
                        "						and DatePeremptionArticle = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("DatePeremptionArticle")) + _
                        "						group by NumeroInventaire, INVENTAIRE_DETAILS.CodeArticle, DatePeremptionArticle " + _
                        "						having COUNT(*)>1 " + _
                        "						) " + _
                        "and NumeroInventaire = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("NumeroInventaire")) + _
                        "and INVENTAIRE_DETAILS.CodeArticle = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("CodeArticle")) + _
                        "and NumeroLot  in ( select NumeroLotArticle from LOT_ARTICLE where CodeArticle =" + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("CodeArticle")) + " and DatePeremptionArticle = " + Quote(dsLot.Tables("INVENTAIRE_WASSIM").Rows(i).Item("DatePeremptionArticle")) + " ) "

                cmdLot.CommandText = StrSQL
                cmdLot.ExecuteNonQuery()
            Catch ex As Exception
                Console.Write(ex.Message)
            End Try
        Next


        Dim sommeQte As Integer = 0

        Try
            dsLot.Tables("ARTICLE_DATE_LOT_EN_DOUBLE").Clear()
        Catch ex As Exception
        End Try

        StrSQL = " select CodeArticle " + _
                " from LOT_ARTICLE " + _
                " where NumeroLotArticle <> '' and " + _
                " (select Supprime from ARTICLE where CodeArticle = LOT_ARTICLE.CodeArticle )= 0 " + _
                " group by CodeArticle, DatePeremptionArticle " + _
                " having COUNT(*)> 1 "

        cmdLot.Connection = ConnectionServeur
        cmdLot.CommandText = StrSQL
        daLot = New SqlDataAdapter(cmdLot)
        daLot.Fill(dsLot, "ARTICLE_DATE_LOT_EN_DOUBLE")
        cbLot = New SqlCommandBuilder(daLot)

        For i = 0 To dsLot.Tables("ARTICLE_DATE_LOT_EN_DOUBLE").Rows.Count - 1

            Try
                dsLot.Tables("ARTICLE_DATE_LOT_EN_DOUBLE_DETAIL").Clear()
            Catch ex As Exception
            End Try


            StrSQL = " select CodeArticle, DatePeremptionArticle " + _
                    " from LOT_ARTICLE " + _
                    " where NumeroLotArticle <> '' and CodeArticle = " + Quote(dsLot.Tables("ARTICLE_DATE_LOT_EN_DOUBLE").Rows(i).Item("CodeArticle")) + _
                    " group by CodeArticle, DatePeremptionArticle " + _
                    " having COUNT(*)> 1 "

            cmdLot.Connection = ConnectionServeur
            cmdLot.CommandText = StrSQL
            daLot = New SqlDataAdapter(cmdLot)
            daLot.Fill(dsLot, "ARTICLE_DATE_LOT_EN_DOUBLE_DETAIL")
            cbLot = New SqlCommandBuilder(daLot)

            For j = 0 To dsLot.Tables("ARTICLE_DATE_LOT_EN_DOUBLE_DETAIL").Rows.Count - 1


                Try
                    dsLot.Tables("LOT_DATE_ENREGISTREMENT").Clear()
                Catch ex As Exception
                End Try


                StrSQL = " select * from LOT_ARTICLE " + _
                        " where CodeArticle = " + Quote(dsLot.Tables("ARTICLE_DATE_LOT_EN_DOUBLE_DETAIL").Rows(j).Item("CodeArticle")) + _
                        " and DatePeremptionArticle = " + Quote(dsLot.Tables("ARTICLE_DATE_LOT_EN_DOUBLE_DETAIL").Rows(j).Item("DatePeremptionArticle")) + _
                        " order by NumeroLotArticle "

                cmdLot.Connection = ConnectionServeur
                cmdLot.CommandText = StrSQL
                daLot = New SqlDataAdapter(cmdLot)
                daLot.Fill(dsLot, "LOT_DATE_ENREGISTREMENT")
                cbLot = New SqlCommandBuilder(daLot)

                cmdLot.Connection = ConnectionServeur
                cmdLot.CommandText = "SELECT SUM(QteLotArticle) FROM LOT_ARTICLE " + _
                                     " WHERE CodeArticle = " + Quote(dsLot.Tables("ARTICLE_DATE_LOT_EN_DOUBLE_DETAIL").Rows(j).Item("CodeArticle")) + _
                                     " AND DatePeremptionArticle = " + Quote(dsLot.Tables("ARTICLE_DATE_LOT_EN_DOUBLE_DETAIL").Rows(j).Item("DatePeremptionArticle"))

                sommeQte = cmdLot.ExecuteScalar()


                For k = 0 To dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows.Count - 1
                    If k = 0 Then
                        Try
                            cmdLot.Connection = ConnectionServeur
                            cmdLot.CommandText = " UPDATE LOT_ARTICLE SET QteLotArticle = " + sommeQte + _
                                                 " WHERE CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("CodeArticle")) + _
                                                 " AND NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                                                 " AND DatePeremptionArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("DatePeremptionArticle"))

                            cmdLot.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.Write(ex.Message)
                        End Try
                    Else

                        Try
                            cmdLot.Connection = ConnectionServeur
                            cmdLot.CommandText = " UPDATE ACHAT_DETAILS SET NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                                                 " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                                                 " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle"))

                            cmdLot.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.Write(ex.Message)
                        End Try

                        Try
                            cmdLot.Connection = ConnectionServeur
                            cmdLot.CommandText = " UPDATE ACHAT_SUPPRIME_DETAILS SET NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                                                 " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                                                 " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle"))

                            cmdLot.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.Write(ex.Message)
                        End Try

                        Try
                            cmdLot.Connection = ConnectionServeur
                            cmdLot.CommandText = " UPDATE INVENTAIRE_DETAILS SET NumeroLot = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                                                 " WHERE NumeroLot = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                                                 " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle"))

                            cmdLot.ExecuteNonQuery()
                        Catch ex As Exception

                            ''Try
                            ''    cmdLot.Connection = ConnectionServeur
                            ''    cmdLot.CommandText = " DELETE FROM INVENTAIRE_DETAILS " + _
                            ''                         " WHERE NumeroLot = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                            ''                         " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle"))

                            ''    cmdLot.ExecuteNonQuery()

                            ''Catch ex1 As Exception
                            ''    Console.Write(ex1.Message)
                            ''End Try



                        End Try

                        Try
                            cmdLot.Connection = ConnectionServeur
                            cmdLot.CommandText = " UPDATE PRET_DETAILS SET NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                                                 " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                                                 " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle"))

                            cmdLot.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.Write(ex.Message)
                        End Try


                        Try
                            cmdLot.Connection = ConnectionServeur
                            cmdLot.CommandText = " UPDATE PRODUCTION_DETAILS SET NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                                                 " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                                                 " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle"))

                            cmdLot.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.Write(ex.Message)
                        End Try

                        Try
                            cmdLot.Connection = ConnectionServeur
                            cmdLot.CommandText = " UPDATE PRODUCTION_DETAILS_FORMULE SET NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                                                 " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                                                 " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle"))

                            cmdLot.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.Write(ex.Message)
                        End Try

                        Try
                            cmdLot.Connection = ConnectionServeur
                            cmdLot.CommandText = " UPDATE VENTE_DETAILS SET NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                                                 " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                                                 " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle"))

                            cmdLot.ExecuteNonQuery()
                        Catch ex As Exception
                            'Console.Write(ex.Message)




                            'Try
                            '    cmdLot.Connection = ConnectionServeur
                            '    cmdLot.CommandText = " UPDATE VENTE_DETAILS SET Qte = Qte + " + _
                            '                         " (Select Qte FROM VENTE_DETAILS WHERE NumeroVente = VENTE_DETAILS.NumeroVente " + _
                            '                         " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle")) + _
                            '                         " AND NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                            '                         ")" + _
                            '                         " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                            '                         " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("CodeArticle"))



                            '    cmdLot.ExecuteNonQuery()

                            '    cmdLot.Connection = ConnectionServeur
                            '    cmdLot.CommandText = " UPDATE VENTE_DETAILS SET TotalHT = Qte * PrixHT, " + _
                            '                         " TotalTTC = PrixTTC * Qte " + _
                            '                         " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                            '                         " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("CodeArticle"))

                            '    cmdLot.ExecuteNonQuery()

                            'Catch ex1 As Exception
                            '    Console.Write(ex1.Message)
                            'End Try

                            'Try
                            '    cmdLot.Connection = ConnectionServeur
                            '    cmdLot.CommandText = " DELETE FROM VENTE_DETAILS " + _
                            '                         " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                            '                         " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle"))

                            '    cmdLot.ExecuteNonQuery()
                            'Catch ex1 As Exception
                            '    Console.Write(ex1.Message)
                            'End Try


                        End Try

                        Try
                            cmdLot.Connection = ConnectionServeur
                            cmdLot.CommandText = " UPDATE VENTE_SUPPRIME_DETAILS SET NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                                                 " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                                                 " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle"))

                            cmdLot.ExecuteNonQuery()
                        Catch ex As Exception
                            'Console.Write(ex.Message)
                            '''''''''''''''''''''''''''''
                            'Try
                            '    cmdLot.Connection = ConnectionServeur
                            '    cmdLot.CommandText = " UPDATE VENTE_SUPPRIME_DETAILS SET Qte = Qte + " + _
                            '                         " (Select SUM(Qte) FROM VENTE_SUPPRIME_DETAILS WHERE NumeroVente = VENTE_DETAILS.NumeroVente " + _
                            '                         " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle")) + _
                            '                         " AND NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                            '                         ")" + _
                            '                         " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                            '                         " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("CodeArticle"))

                            '    cmdLot.ExecuteNonQuery()

                            '    cmdLot.Connection = ConnectionServeur
                            '    cmdLot.CommandText = " UPDATE VENTE_SUPPRIME_DETAILS SET TotalHT = Qte * PrixHT, " + _
                            '                         " TotalTTC = PrixTTC * Qte " + _
                            '                         " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                            '                         " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("CodeArticle"))

                            '    cmdLot.ExecuteNonQuery()

                            'Catch ex1 As Exception
                            '    Console.Write(ex1.Message)
                            'End Try

                            'Try
                            '    cmdLot.Connection = ConnectionServeur
                            '    cmdLot.CommandText = " DELETE FROM VENTE_DETAILS " + _
                            '                         " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                            '                         " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle"))

                            '    cmdLot.ExecuteNonQuery()
                            'Catch ex1 As Exception
                            '    Console.Write(ex1.Message)
                            'End Try
                            '''''''''''''''''''''''''''''''''''''''''''''''

                        End Try


                        Try
                            cmdLot.Connection = ConnectionServeur
                            cmdLot.CommandText = " UPDATE SORTIE_DETAILS SET NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                                                 " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                                                 " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle"))

                            cmdLot.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.Write(ex.Message)
                        End Try


                        Try
                            cmdLot.Connection = ConnectionServeur
                            cmdLot.CommandText = " UPDATE ENTREE_DETAILS SET NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(0).Item("NumeroLotArticle")) + _
                                                 " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                                                 " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle"))

                            cmdLot.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.Write(ex.Message)
                        End Try


                        Try
                            cmdLot.Connection = ConnectionServeur
                            cmdLot.CommandText = " DELETE FROM LOT_ARTICLE " + _
                                                 " WHERE NumeroLotArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                                                 " AND CodeArticle = " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle"))

                            cmdLot.ExecuteNonQuery()

                            InsertionDansLog("CorrectionLots", "Suppression LOT_ARTICLE NumeroLot : " + _
                                             Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("NumeroLotArticle")) + _
                                             " de l article : " + Quote(dsLot.Tables("LOT_DATE_ENREGISTREMENT").Rows(k).Item("CodeArticle")), _
                                             CodeUtilisateur, Quote(Date.Now), "", "", "")
                        Catch ex As Exception
                            Console.Write(ex.Message)
                        End Try

                    End If

                Next
            Next

        Next

        MsgBox("Correction du Lot terminée")
    End Sub

    'Afficher Bureau
    Private Sub AffciherBureau()



        'Initialiser Pic
        initMonBueauPic()

        'Initialiser lbl
        initMonBureauLblTitre()

        'Initialiser lbl
        initMonBureauLblLien()



        Try

            'Vider la DS
            Try
                dsIconBureau.Tables("IconBureau").Clear()
            Catch ex As Exception

            End Try

            StrSQL = " SELECT CodeUtilisateur, Titre, Icon, UrlProgramme, NumeroBouton FROM MON_BUREAU WHERE [CodeUtilisateur] = " & CodeUtilisateur
            cmdIconBureau.Connection = ConnectionServeur
            cmdIconBureau.CommandText = StrSQL
            daIconBureau = New SqlDataAdapter(cmdIconBureau)
            daIconBureau.Fill(dsIconBureau, "IconBureau")
            cbIconBureau = New SqlCommandBuilder(daIconBureau)

            'Parcourir la liste et affecter les informations au pic
            For cpt As Integer = 0 To dsIconBureau.Tables("IconBureau").Rows.Count - 1

                Dim Img As Drawing.Image = Nothing
                CType(monBueauBouton(Val(dsIconBureau.Tables("IconBureau").Rows(cpt).Item("NumeroBouton"))), PictureBox).Image = ByteArrayToImage((dsIconBureau.Tables("IconBureau").Rows(cpt).Item("Icon")))

            Next

            'Parcourir la liste et affecter les informations au lbl
            For cpt As Integer = 0 To dsIconBureau.Tables("IconBureau").Rows.Count - 1

                CType(monBueauLabel(Val(dsIconBureau.Tables("IconBureau").Rows(cpt).Item("NumeroBouton"))), Label).Text = dsIconBureau.Tables("IconBureau").Rows(cpt).Item("Titre").ToString

            Next

            'Parcourir la liste et affecter les informations au lien
            For cpt As Integer = 0 To dsIconBureau.Tables("IconBureau").Rows.Count - 1

                CType(monBueauLien(Val(dsIconBureau.Tables("IconBureau").Rows(cpt).Item("NumeroBouton"))), Label).Text = dsIconBureau.Tables("IconBureau").Rows(cpt).Item("UrlProgramme").ToString

            Next

        Catch ex As Exception

            'Gérer l'initIconBureau
            fMessageException.Show("Bureau", "fMain", "initAchat", ex.Message, "0000806", "Erreur d'excution de initIconBureau", True, True, True)
            Return

        End Try

    End Sub

    Private Sub initMonBureauLblLien()

        'Clear pic si exist
        For i As Integer = 1 To monBueauLien.Count - 1
            CType(monBueauLien(i), Label).Text = "..."
        Next

        '-----Remplir lbl

        'La 1 ere ligne 
        monBueauLien.Add(lblLien1_3)
        monBueauLien.Add(lblLien1_4)
        monBueauLien.Add(lblLien1_5)
        monBueauLien.Add(lblLien1_6)

        'La 2 imme ligne 
        monBueauLien.Add(lblLien2_3)
        monBueauLien.Add(lblLien2_4)
        monBueauLien.Add(lblLien2_5)
        monBueauLien.Add(lblLien2_6)

        'La 3 iemme ligne 
        monBueauLien.Add(lblLien3_3)
        monBueauLien.Add(lblLien3_4)
        monBueauLien.Add(lblLien3_5)
        monBueauLien.Add(lblLien3_6)

        'La 4 iemme ligne 
        monBueauLien.Add(lblLien4_3)
        monBueauLien.Add(lblLien4_4)
        monBueauLien.Add(lblLien4_5)
        monBueauLien.Add(lblLien4_6)

    End Sub

    Private Sub initMonBureauLblTitre()

        'Clear pic si exist
        For i As Integer = 1 To monBueauLabel.Count - 1
            CType(monBueauLabel(i), Label).Text = "..."
        Next

        '-----Remplir lbl

        'La 1 ere ligne 
        monBueauLabel.Add(lblTitre1_3)
        monBueauLabel.Add(lblTitre1_4)
        monBueauLabel.Add(lblTitre1_5)
        monBueauLabel.Add(lblTitre1_6)

        'La 2 imme ligne 
        monBueauLabel.Add(lblTitre2_3)
        monBueauLabel.Add(lblTitre2_4)
        monBueauLabel.Add(lblTitre2_5)
        monBueauLabel.Add(lblTitre2_6)

        'La 3 iemme ligne 
        monBueauLabel.Add(lblTitre3_3)
        monBueauLabel.Add(lblTitre3_4)
        monBueauLabel.Add(lblTitre3_5)
        monBueauLabel.Add(lblTitre3_6)

        'La 4 iemme ligne 
        monBueauLabel.Add(lblTitre4_3)
        monBueauLabel.Add(lblTitre4_4)
        monBueauLabel.Add(lblTitre4_5)
        monBueauLabel.Add(lblTitre4_6)

    End Sub

    Private Sub initMonBueauPic()

        'Clear pic si exist
        For i As Integer = 1 To monBueauBouton.Count - 1
            CType(monBueauBouton(i), PictureBox).Image = Nothing
        Next

        '-------Remplir Pic
        'La 1 ere ligne 
        monBueauBouton.Add(pic1_3)
        monBueauBouton.Add(pic1_4)
        monBueauBouton.Add(pic1_5)
        monBueauBouton.Add(pic1_6)

        'La 2 imme ligne 
        monBueauBouton.Add(pic2_3)
        monBueauBouton.Add(pic2_4)
        monBueauBouton.Add(pic2_5)
        monBueauBouton.Add(pic2_6)

        'La 3 iemme ligne 
        monBueauBouton.Add(pic3_3)
        monBueauBouton.Add(pic3_4)
        monBueauBouton.Add(pic3_5)
        monBueauBouton.Add(pic3_6)

        'La 4 iemme ligne 
        monBueauBouton.Add(pic4_3)
        monBueauBouton.Add(pic4_4)
        monBueauBouton.Add(pic4_5)
        monBueauBouton.Add(pic4_6)
    End Sub

    Public Function ByteArrayToImage(ByVal ByteArray As Byte()) As Image
        Dim stream As New MemoryStream(ByteArray, 0, ByteArray.Length)
        Return Image.FromStream(stream, True)
    End Function

    Private Sub initIconBureau(ByVal pBtnNum As Integer)

        'La 1 ire ligne 
        lblLien1_3.Visible = False
        lblLien1_4.Visible = False
        lblLien1_5.Visible = False
        lblLien1_6.Visible = False

        'La 2 imme ligne 
        lblLien2_3.Visible = False
        lblLien2_4.Visible = False
        lblLien2_5.Visible = False
        lblLien2_6.Visible = False

        'La 3 iemme ligne 
        lblLien3_3.Visible = False
        lblLien3_4.Visible = False
        lblLien3_5.Visible = False
        lblLien3_6.Visible = False

        'La 4 iemme ligne 
        lblLien4_3.Visible = False
        lblLien4_4.Visible = False
        lblLien4_5.Visible = False
        lblLien4_6.Visible = False

        Dim StrSQL As String

        'Vider la DS
        Try
            dsIconBureau.Tables("IconBureau").Clear()
        Catch ex As Exception
        End Try

        Try

            StrSQL = " SELECT * FROM MON_BUREAU WHERE [CodeUtilisateur] = " & CodeUtilisateur & " AND [NumeroBouton]  = " & pBtnNum

            cmdIconBureau.Connection = ConnectionServeur
            cmdIconBureau.CommandText = StrSQL
            daIconBureau = New SqlDataAdapter(cmdIconBureau)
            daIconBureau.Fill(dsIconBureau, "IconBureau")
            cbIconBureau = New SqlCommandBuilder(daIconBureau)

        Catch ex As Exception

            'Gérer l'initIconBureau
            fMessageException.Show("Bureau", "fMain", "initAchat", ex.Message, "0000806", "Erreur d'excution de initIconBureau", True, True, True)
            Return

        End Try

    End Sub

    Private Sub AjoutIconeBureau(ByVal numBtn As Integer)

        Dim drIconBureau As DataRow
        Dim fsIconBureau As FileStream
        Dim brIconBureau As BinaryReader
        Dim imgIconBureau As Byte()
        Try

            'test if ajout
            If (CType(monBueauLabel(numBtn), Label)).Text = "..." Then

                'Affecter parametres
                fAjoutIconBurau.mode = "ajout"
                fAjoutIconBurau.numBouton = numBtn

                'Afficher Form
                fAjoutIconBurau.ShowDialog()

                fsIconBureau = New FileStream(fAjoutIconBurau.pathIcon, FileMode.Open)
                brIconBureau = New BinaryReader(fsIconBureau)

                imgIconBureau = New Byte(fsIconBureau.Length + 1) {}
                imgIconBureau = brIconBureau.ReadBytes(Convert.ToInt32(fsIconBureau.Length))

                drIconBureau = dsIconBureau.Tables("IconBureau").NewRow()

                drIconBureau("Titre") = fAjoutIconBurau.TitreLien
                drIconBureau("Icon") = imgIconBureau
                drIconBureau("CodeUtilisateur") = CodeUtilisateur
                drIconBureau("UrlProgramme") = fAjoutIconBurau.PathLien
                drIconBureau("NumeroBouton") = numBtn

                dsIconBureau.Tables(0).Rows.Add(drIconBureau)
                daIconBureau.Update(dsIconBureau, "IconBureau")

            Else 'test if modif

                'On va tester si le user va changer l'image ou non 

                'Affecter parametres
                fAjoutIconBurau.TitreLien = CType(monBueauLabel(numBtn), Label).Text
                fAjoutIconBurau.PathLien = CType(monBueauLien(numBtn), Label).Text
                fAjoutIconBurau.mode = "modif"
                fAjoutIconBurau.numBouton = numBtn

                'Affciher form
                fAjoutIconBurau.ShowDialog()

                If CodeUtilisateur <> "NEXT" Then
                    'pour re-initaliser la DS
                    initIconBureau(numBtn)
                End If


                If (fAjoutIconBurau.pathIcon <> "") Then

                    fsIconBureau = New FileStream(fAjoutIconBurau.pathIcon, FileMode.Open)
                    brIconBureau = New BinaryReader(fsIconBureau)
                    imgIconBureau = New Byte(fsIconBureau.Length + 1) {}
                    imgIconBureau = brIconBureau.ReadBytes(Convert.ToInt32(fsIconBureau.Length))

                    dsIconBureau.Tables("IconBureau").Rows(0)("Icon") = imgIconBureau

                End If

                If dsIconBureau.Tables("IconBureau").Rows.Count > 0 Then

                    dsIconBureau.Tables("IconBureau").Rows(0)("Titre") = fAjoutIconBurau.TitreLien
                    dsIconBureau.Tables("IconBureau").Rows(0)("CodeUtilisateur") = CodeUtilisateur
                    dsIconBureau.Tables("IconBureau").Rows(0)("UrlProgramme") = fAjoutIconBurau.PathLien
                    dsIconBureau.Tables("IconBureau").Rows(0)("NumeroBouton") = numBtn

                    daIconBureau.Update(dsIconBureau, "IconBureau")
                End If

            End If

            'Refreshir les icons du bureau
            AffciherBureau()

        Catch ex As Exception
            MsgBox(ex.Message)
        End Try

    End Sub

    Private Sub bDetailsCaisse_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bDetailsCaisse.Click

        If ControleDAcces(5, "DETAILS_CAISSE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Détails Caisse" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyDetailsCaisse = New fEtatRegelement
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyDetailsCaisse.Panel)
        Tab.SelectedTab.Text = "Détails Caisse"
        MyDetailsCaisse.Init()
    End Sub


    Private Sub bAlimentationCaisse_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAlimentationCaisse.Click
        'Exit Sub
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Alimentation Caisse" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyAlimentationCaisse = New fAlimentationCaisse
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyAlimentationCaisse.Panel)
        Tab.SelectedTab.Text = "Alimentation Caisse"
        MyAlimentationCaisse.init()
    End Sub


    Private Sub bMiseAJourFichierArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bMiseAJourFichierArticle.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Changemant fichier article" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyChangemantFichierArticle = New fChangemantDuFichierArticle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyChangemantFichierArticle.Panel)
        Tab.SelectedTab.Text = "Changement fichier article"
        MyChangemantFichierArticle.init()
    End Sub


    Private Sub bPharmaUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPharmaUpdate.Click

        If ControleDAcces(2, "UPDATE_ARTICLE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Pharma 2000 PREMIUM Update" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyUpdateArticle = New fUpdateArticle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyUpdateArticle.Panel)
        Tab.SelectedTab.Text = "Pharma 2000 PREMIUM Update"
        MyUpdateArticle.init()
    End Sub

    Private Sub bLien1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien1.Click
        AffecterLien("bLien1")
    End Sub

    Public Sub AffecterLien(ByVal NomBouton As String)

        Dim Lien As String = ""
        Dim NouveauLien As String = ""
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim NombreDesButtons As Integer = 0
        Dim TextBouton As String = ""

        Lien = RecupererValeurExecuteScalaire("Lien", "LIENS_BOUTONS_BUREAU", "NomBouton", NomBouton)
        If Lien <> "" Then
            NouveauLien = AjouterLien(Lien)
            If NouveauLien = "" Then
                StrSQL = "UPDATE LIENS_BOUTONS_BUREAU SET Lien='',TextBouton='' WHERE NomBouton LIKE  '" + NomBouton + "'"
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    cmd.ExecuteNonQuery()
                    RecupererTextBoutons()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
            End If
        Else
            NouveauLien = AjouterLien(Lien)

            Dim MyNomBoutonLien As New fNomBoutonLien
            MyNomBoutonLien.ShowDialog()

            TextBouton = fNomBoutonLien.Nom

            MyNomBoutonLien.Dispose()
            MyNomBoutonLien.Close()

            cmd.CommandText = "SELECT COUNT(*) FROM LIENS_BOUTONS_BUREAU WHERE NomBouton like '" + NomBouton + "'"
            cmd.Connection = ConnectionServeur

            Try
                NombreDesButtons = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If NombreDesButtons <> 0 Then
                StrSQL = "UPDATE LIENS_BOUTONS_BUREAU SET Lien='" + NouveauLien + "',TextBouton='" + TextBouton + "' WHERE NomBouton LIKE  '" + NomBouton + "'"
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    cmd.ExecuteNonQuery()
                    RecupererTextBoutons()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

            Else
                StrSQL = "INSERT INTO LIENS_BOUTONS_BUREAU VALUES('" + NomBouton + "','" + NouveauLien + "','" + TextBouton + "')"
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    cmd.ExecuteNonQuery()
                    RecupererTextBoutons()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
            End If
        End If

    End Sub

    Public Function AjouterLien(ByVal Lien As String)

        On Error GoTo err

        Dim procID As Integer
        Dim path As String

        If Lien = "" Then
            With FileDlg
                .Title = "Ouvrir"        'Titre de la barre de titre
                .InitialDirectory = "c:\"   'répertoire de départ        
                ' .Filter = "Fichiers exe|*.exe" ' on travaille uniquement sur les .txt
                .Multiselect = False        'sélectionner 1 seul fichier
                .CheckFileExists = True     'Message  si nom de fichier qui n'existe pas.
                .ValidateNames = True        'n'accepte que les noms valides (win 32)
                .AddExtension = True        'ajoute une extension au nom s'il n'y en a pas
            End With
            If FileDlg.ShowDialog = DialogResult.OK Then
                path = FileDlg.FileName()
                procID = Shell(path, AppWinStyle.NormalFocus)
            Else
                path = ""
            End If
            Return path
        Else
            procID = Shell(Lien, AppWinStyle.NormalFocus)
            Return Lien
        End If
        'MessageBox.Show(String.IsNullOrEmpty(path).ToString)
        Exit Function
err:
        Shell(path + "l")
        MessageBox.Show(Err.Description)
        Return ""
    End Function
    Public Sub RecupererTextBoutons()
        'Recupération des text afficher sur le boutons 
        Me.bLien1.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien1")
        Me.bLien2.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien2")
        Me.bLien3.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien3")
        Me.bLien4.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien4")
        Me.bLien5.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien5")
        Me.bLien6.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien6")
        Me.bLien7.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien7")
        Me.bLien8.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien8")
        Me.bLien9.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien9")
        Me.bLien10.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien10")
        Me.bLien11.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien11")
        Me.bLien12.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien12")
        Me.bLien13.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien13")
        Me.bLien14.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien14")
        Me.bLien15.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien15")
        Me.bLien16.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien16")

        If Me.bLien1.Text = "" Then
            Me.bLien1.Text = "?"
        End If
        If Me.bLien2.Text = "" Then
            Me.bLien2.Text = "?"
        End If
        If Me.bLien3.Text = "" Then
            Me.bLien3.Text = "?"
        End If
        If Me.bLien4.Text = "" Then
            Me.bLien4.Text = "?"
        End If
        If Me.bLien5.Text = "" Then
            Me.bLien5.Text = "?"
        End If
        If Me.bLien6.Text = "" Then
            Me.bLien6.Text = "?"
        End If
        If Me.bLien7.Text = "" Then
            Me.bLien7.Text = "?"
        End If
        If Me.bLien8.Text = "" Then
            Me.bLien8.Text = "?"
        End If
        If Me.bLien9.Text = "" Then
            Me.bLien9.Text = "?"
        End If
        If Me.bLien10.Text = "" Then
            Me.bLien10.Text = "?"
        End If
        If Me.bLien11.Text = "" Then
            Me.bLien11.Text = "?"
        End If
        If Me.bLien12.Text = "" Then
            Me.bLien12.Text = "?"
        End If
        If Me.bLien13.Text = "" Then
            Me.bLien13.Text = "?"
        End If
        If Me.bLien14.Text = "" Then
            Me.bLien14.Text = "?"
        End If
        If Me.bLien15.Text = "" Then
            Me.bLien15.Text = "?"
        End If
        If Me.bLien16.Text = "" Then
            Me.bLien16.Text = "?"
        End If
    End Sub

    Private Sub bLien2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien2.Click
        AffecterLien("bLien2")
    End Sub

    Private Sub bLien3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien3.Click
        AffecterLien("bLien3")
    End Sub

    Private Sub bLien4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien4.Click
        AffecterLien("bLien4")
    End Sub

    Private Sub bLien5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien5.Click
        AffecterLien("bLien5")
    End Sub

    Private Sub bLien6_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien6.Click
        AffecterLien("bLien6")
    End Sub

    Private Sub bLien7_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien7.Click
        AffecterLien("bLien7")
    End Sub

    Private Sub bLien8_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien8.Click
        AffecterLien("bLien8")
    End Sub

    Private Sub bLien9_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien9.Click
        AffecterLien("bLien9")
    End Sub

    Private Sub bLien10_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien10.Click
        AffecterLien("bLien10")
    End Sub

    Private Sub bLien11_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien11.Click
        AffecterLien("bLien11")
    End Sub

    Private Sub bLien12_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien12.Click
        AffecterLien("bLien12")
    End Sub

    Private Sub bLien13_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien13.Click
        AffecterLien("bLien13")
    End Sub

    Private Sub bLien14_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien14.Click
        AffecterLien("bLien14")
    End Sub

    Private Sub bLien15_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien15.Click
        AffecterLien("bLien15")
    End Sub

    Private Sub bLien16_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLien16.Click
        AffecterLien("bLien16")
    End Sub

    Public Sub SupprimerRaccourci(ByVal NomBouton As String)

        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand

        StrSQL = "UPDATE LIENS_BOUTONS_BUREAU SET Lien='',TextBouton='' WHERE NomBouton LIKE  '" + NomBouton + "'"
        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL

        Try
            cmd.ExecuteNonQuery()
            RecupererTextBoutons()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
    End Sub

    Private Sub RibbonGroup18_DialogLauncherClick(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub bOuvertureCaisse_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOuvertureCaisse.Click
        'If ControleDAcces(5, "OUVRIR_CAISSE") = "False" Then
        '    Exit Sub
        'End If

        '********************************* Contrôle de l accée *******************************************
        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        cmd.Connection = ConnectionServeur
        cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE '" + "OUVRIR_CAISSE" + "' AND CodeUtilisateur='" + CodeUtilisateur + "'"

        Try
            If cmd.ExecuteScalar() < 1 Then
                MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
                Exit Sub
            End If
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try


        Dim MyMotDePasseOuvertureTiroir As New fMotDePasseOuvertureTiroir
        MyMotDePasseOuvertureTiroir.ShowDialog()
        MyMotDePasseOuvertureTiroir.Close()
        MyMotDePasseOuvertureTiroir.Dispose()

    End Sub

    Private Sub MenuBouton_Opening(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles MenuBouton1.Opening

    End Sub

    Private Sub RibbonButton4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton4.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "CodeAbarre" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        Dim MyUpdateArticle As New fTestStatisqtique
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyUpdateArticle.Panel)
        Tab.SelectedTab.Text = "CodeAbarre"
        MyUpdateArticle.init()
    End Sub

    Private Sub ToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem.Click
        SupprimerRaccourci("bLien1")
    End Sub

    Private Sub ToolStripMenuItem1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem1.Click
        SupprimerRaccourci("bLien2")
    End Sub

    Private Sub ToolStripMenuItem2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem2.Click
        SupprimerRaccourci("bLien3")
    End Sub

    Private Sub ToolStripMenuItem3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem3.Click
        SupprimerRaccourci("bLien4")
    End Sub

    Private Sub ToolStripMenuItem4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem4.Click
        SupprimerRaccourci("bLien5")
    End Sub

    Private Sub ToolStripMenuItem5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem5.Click
        SupprimerRaccourci("bLien6")
    End Sub

    Private Sub ToolStripMenuItem6_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem6.Click
        SupprimerRaccourci("bLien7")
    End Sub

    Private Sub ToolStripMenuItem7_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem7.Click
        SupprimerRaccourci("bLien8")
    End Sub

    Private Sub ToolStripMenuItem8_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem8.Click
        SupprimerRaccourci("bLien9")
    End Sub

    Private Sub ToolStripMenuItem14_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem14.Click
        SupprimerRaccourci("bLien10")
    End Sub

    Private Sub ToolStripMenuItem9_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem9.Click
        SupprimerRaccourci("bLien11")
    End Sub

    Private Sub ToolStripMenuItem10_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem10.Click
        SupprimerRaccourci("bLien12")
    End Sub

    Private Sub ToolStripMenuItem11_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem11.Click
        SupprimerRaccourci("bLien13")
    End Sub

    Private Sub ToolStripMenuItem12_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem12.Click
        SupprimerRaccourci("bLien14")
    End Sub

    Private Sub ToolStripMenuItem15_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem15.Click
        SupprimerRaccourci("bLien15")
    End Sub

    Private Sub ToolStripMenuItem13_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem13.Click
        SupprimerRaccourci("bLien16")
    End Sub

    Private Sub bHistoriqueDesActions_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bHistoriqueDesActions.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Historique des actions" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyHsitoriqueDesActions = New fHistoriqueDesActions
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyHsitoriqueDesActions.Panel)
        Tab.SelectedTab.Text = "Historique des actions"
        MyHsitoriqueDesActions.init()
    End Sub

    Private Sub bHistoriqueAcces_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bHistoriqueAcces.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Historique d'accès" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyHistoriqueDAcces = New fHistoriqueDAcces
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyHistoriqueDAcces.Panel)
        Tab.SelectedTab.Text = "Historique d'accès"
        MyHistoriqueDAcces.init()
    End Sub



    Private Sub bHistoriqueChangementsPrix_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bHistoriqueChangementsPrix.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Historique des changements des prix" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyHistoriqueDesChangementsPrix = New fHistoriqueDesChangementsDesPrix
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyHistoriqueDesChangementsPrix.Panel)
        Tab.SelectedTab.Text = "Historique des changements des prix"
        MyHistoriqueDesChangementsPrix.init()
    End Sub

    Private Sub bSauvegarde_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSauvegarde.Click
        EnregistrementBase()
    End Sub

    Private Sub bChangerUser_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bChangerUser.Click
        If MsgBox("Voulez vous vraiment changer l'utilisateur ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Changer l'utilisateur") = MsgBoxResult.Yes Then

            ' Masquer le bureau
            Tab.Visible = False

            ' Paramètres de connexion
            MotDePasseSQL = GetSetting("PHARMA", "PHARMA", "MotDePasseSQL", "")
            NomUtilisateurSQL = GetSetting("PHARMA", "PHARMA", "NomUtilisateurSQL", "")
            NomBase = GetSetting("PHARMA", "PHARMA", "NomBase", "")
            NomServeur = GetSetting("PHARMA", "PHARMA", "NomServeur", "")

            MotDePasseSQLBCB = GetSetting("PHARMA", "PHARMA", "MotDePasseSqlBCB", "")
            NomUtilisateurSQLBCB = GetSetting("PHARMA", "PHARMA", "NomUtilisateurSqlBCB", "")
            NomBaseBCB = GetSetting("PHARMA", "PHARMA", "NomBaseBCB", "")
            NomServeurBCB = GetSetting("PHARMA", "PHARMA", "NomServeurBCB", "")
            'NomLogiqueDeLaMachine = GetSetting("PHARMA", "PHARMA", "NomMachine", "")

            Connect()
            ConnectDextherBCB()

            If connectionOK Then
                Dim mylogin As New fLogin
                mylogin.ShowDialog()
                If Not mylogin.reussi Then
                    Me.Dispose()
                    Exit Sub
                End If
            Else
                If MsgBox("Connexion à la base de données échouée. Voulez vous afficher les paramètres de connexion ?", MsgBoxStyle.DefaultButton1 + MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Erreur") = MsgBoxResult.Yes Then
                    Dim fBase As New fBaseSQL
                    fBase.ShowDialog()
                    Connect()
                    If Not connectionOK Then Me.Dispose()
                    Dim mylogin As New fLogin
                    mylogin.ShowDialog()
                ElseIf Not mylogin.reussi Then
                    Me.Dispose()
                Else
                    Me.Dispose()
                    End
                End If
            End If

            ' Afficher le bureau
            Tab.Visible = True
            NombreOnglet = Tab.TabPages.Count - 1

            For J As Integer = 0 To NombreOnglet
                If Tab.TabPages(J).Text <> "Bureau" Then
                    Tab.TabPages(J).Dispose()
                    NombreOnglet = Tab.TabPages.Count - 1
                    J = 0
                    If NombreOnglet = 0 Then
                        Exit For
                    End If
                End If
            Next

            ActualiserStatusBar()

            If ModeADMIN <> "ADMIN" Then
                RibbonTab5.Visible = False
            Else
                RibbonTab5.Visible = True
            End If
            'bChangerUser.Visible = False

            If System.Environment.GetEnvironmentVariable("POSTE") = Nothing Then
                MsgBox("Numéro de poste inexistant !", MsgBoxStyle.Critical, "Erreur")
                Me.Dispose()
            End If

            'régler les paramtres regionaux
            DecimalSeparator = "."

            ' ajout du nom de la poste dans la table POSTE 
            Dim cmd As New SqlCommand
            'MessageBox.Show(System.Environment.GetEnvironmentVariable("Poste"))
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "SELECT COUNT(LibellePoste) FROM POSTE WHERE LibellePoste LIKE '" + System.Environment.GetEnvironmentVariable("Poste") + "'" 'Environment.MachineName.ToString 
            Try
                If cmd.ExecuteScalar() < 1 Then
                    cmd.CommandText = "INSERT INTO POSTE (""LibellePoste"") VALUES ('" + System.Environment.GetEnvironmentVariable("Poste") + "')"
                    Try
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try
                End If
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            '********************************** affichage de la vente
            'bVente_Click(sender, e)
            Dim I As Integer
            'For I = 0 To Tab.TabPages.Count - 1
            '    If Tab.TabPages(I).Text = "Bureau" Then
            '        Me.Tab.SelectedTab.Dispose()
            '        GoTo Suivant
            '    End If
            'Next

Suivant:

            For I = 0 To Tab.TabPages.Count - 1
                If Tab.TabPages(I).Text = "Vente" Then
                    Tab.TabPages(I).Show()
                    Exit Sub
                End If
            Next

            Dim MyVente As New fVente
            MyVente.Text = "Vente" + IndexVente.ToString()
            IndexVente = IndexVente + 1
            MyVentes.Add(MyVente)
            Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            Tab.SelectedIndex = Tab.TabPages.Count - 1
            Tab.SelectedTab.Controls.Add(MyVente.Panel)
            Tab.SelectedTab.Text = MyVente.Text
            MyVente.Init()
            MyVente.bAjouter_Click(sender, e)

            '*******************************************************

            ' ajout du nom de la poste dans la table PARAMETRES 
            'Dim cmd As New SqlCommand
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "SELECT COUNT(POSTE) FROM PARAMETRES  WHERE POSTE LIKE '" + System.Environment.GetEnvironmentVariable("Poste") + "'"
            Try
                If cmd.ExecuteScalar() < 1 Then
                    cmd.CommandText = "INSERT INTO PARAMETRES (""POSTE""" + _
                                                         ",""HonoraireDeResponsabiliteTA""" + _
                                                         ",""HonoraireDeResponsabiliteTB""" + _
                                                         ",""HonoraireDeResponsabiliteTC""" + _
                                                         ",""MinimumDePerception""" + _
                                                         ",""RechercheArticleForme""" + _
                                                         ",""RechercheArticleDCI""" + _
                                                         ",""RechercheArticlePrixTTC""" + _
                                                         ",""AutoriserLesMiseAjourEnLigne""" + _
                                                         ",""AutoriserLesChangementsAutomatiquesDesPrixPreparation""" + _
                                                         ",""ImprimanteATicket""" + _
                                                         ",""Tiroir""" + _
                                                         ",""USB""" + _
                                                         ",""COM""" + _
                                                         ",""ImprimerLesBons""" + _
                                                         ",""ValiderQteEgalA1""" + _
                                                         ",""ControlerLeNombredesUnitesvendues""" + _
                                                         ",""NombreDesTentativesPourControlerLeNombreDesArticles""" + _
                                                         ",""CreationDesClientsCreditAuNiveauPreparateur""" + _
                                                         ",""InscriptionAutomatiqueSurOrdonnacier""" + _
                                                         ",""InterdirelaVenteDesProduitsPerimes""" + _
                                                         ",""VerifierSiProduitPrisEnChargeParLaCNAM""" + _
                                                         ",""DureeAffichageDesAlertes""" + _
                                                         ",""InterdireDeChoisirLesArticlesParDesignation""" + _
                                                         ",""RetrancherDuStockLorsDuneVenteenInstance""" + _
                                                         ",""DerniersJours""" + _
                                                         ",""CmdGroupeJournaliere""" + _
                                                         ",""QteMultipleDe5""" + _
                                                         ",""NePAsSortirLesManquantsDepuis""" + _
                                                         ",""CongeDuAnneeCourant""" + _
                                                         ",""CongeAuAnneeCourant""" + _
                                                         ",""CongeDuAnneeProchaine""" + _
                                                         ",""CongeAuAnneeProchaine""" + _
                                                         ",""NomDeLordinateurDImpressionCodeABarre""" + _
                                                         ",""NePAsMettreAJourLaFicheArticleSiLePrixChange""" + _
                                                         ",""InterventionAvecUnAssistantSiLePrixChange""" + _
                                                         ",""AfficherLesDernieresDatesDePeremptionDansLesNouveauxAchats""" + _
                                                         ",""LecteurUpdate""" + _
                                                         ",""LecteurSauvegarde""" + _
                                                         ",""NomDeLImprimante""" + _
                                                         ",""AjouterTimbreAFacture"") " + _
                                      "VALUES ('" + System.Environment.GetEnvironmentVariable("Poste") + _
                                              "','0.080" + _
                                              "','0.100" + _
                                              "','0.080" + _
                                              "','0.250" + _
                                              "','False" + _
                                              "','False" + _
                                              "','False" + _
                                              "','False" + _
                                              "','False" + _
                                              "','False" + _
                                              "','False" + _
                                              "','False" + _
                                              "','" + _
                                              "','False" + _
                                              "','False" + _
                                              "','False" + _
                                              "','3" + _
                                              "','False" + _
                                              "','False" + _
                                              "','False" + _
                                              "','False" + _
                                              "','" + _
                                              "','False" + _
                                              "','False" + _
                                              "','" + _
                                              "','" + _
                                              "','False" + _
                                              "','" + _
                                              "',NULL" + _
                                              ",NULL" + _
                                              ",NULL" + _
                                              ",NULL" + _
                                              ",'" + _
                                              "','False" + _
                                              "','False" + _
                                              "','False" + _
                                              "',''" + _
                                              ",''" + _
                                              ",''" + _
                                              ",'False')"
                    Try
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try
                End If
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            ' lecture des paramètres globaux

            If RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTA", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                HonoraireTableauA = RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTA", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                HonoraireTableauA = 0
            End If

            If RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTB", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                HonoraireTableauB = RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTB", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                HonoraireTableauB = 0
            End If

            If RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTC", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                HonoraireTableauC = RecupererValeurExecuteScalaire("HonoraireDeResponsabiliteTC", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                HonoraireTableauC = 0
            End If

            If RecupererValeurExecuteScalaire("MinimumDePerception", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                MinimumDePerception = RecupererValeurExecuteScalaire("MinimumDePerception", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                MinimumDePerception = 0
            End If

            If RecupererValeurExecuteScalaire("AutoriserLesMiseAjourEnLigne", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                AutoriserMiseAjoursEnLigneArticles = RecupererValeurExecuteScalaire("AutoriserLesMiseAjourEnLigne", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                AutoriserMiseAjoursEnLigneArticles = False
            End If

            If RecupererValeurExecuteScalaire("AutoriserLesChangementsAutomatiquesDesPrixPreparation", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                AutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations = RecupererValeurExecuteScalaire("AutoriserLesChangementsAutomatiquesDesPrixPreparation", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                AutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations = False
            End If

            If RecupererValeurExecuteScalaire("ImprimanteATicket", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                ImprimanteATicket = RecupererValeurExecuteScalaire("ImprimanteATicket", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                ImprimanteATicket = False
            End If

            If RecupererValeurExecuteScalaire("Tiroir", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                Tiroir = RecupererValeurExecuteScalaire("Tiroir", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                Tiroir = False
            End If

            If RecupererValeurExecuteScalaire("USB", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                TiroirUSB = RecupererValeurExecuteScalaire("USB", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                TiroirUSB = False
            End If

            If RecupererValeurExecuteScalaire("COM", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                TiroirCOM = RecupererValeurExecuteScalaire("COM", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                TiroirCOM = False
            End If

            If RecupererValeurExecuteScalaire("ImprimerLesBons", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                ImprimerBon = RecupererValeurExecuteScalaire("ImprimerLesBons", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                ImprimerBon = False
            End If

            If RecupererValeurExecuteScalaire("ValiderQteEgalA1", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                ValiderQteEgalA1 = RecupererValeurExecuteScalaire("ValiderQteEgalA1", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                ValiderQteEgalA1 = False
            End If

            If RecupererValeurExecuteScalaire("ControlerLeNombredesUnitesvendues", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                ControleNombreUnites = RecupererValeurExecuteScalaire("ControlerLeNombredesUnitesvendues", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                ControleNombreUnites = False
            End If

            If RecupererValeurExecuteScalaire("NombreDesTentativesPourControlerLeNombreDesArticles", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                NbrDesTentatives = RecupererValeurExecuteScalaire("NombreDesTentativesPourControlerLeNombreDesArticles", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                NbrDesTentatives = 1
            End If

            If RecupererValeurExecuteScalaire("CreationDesClientsCreditAuNiveauPreparateur", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                CreationDesClientsCreditDansNiveauPreparateur = RecupererValeurExecuteScalaire("CreationDesClientsCreditAuNiveauPreparateur", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                CreationDesClientsCreditDansNiveauPreparateur = False
            End If

            If RecupererValeurExecuteScalaire("InscriptionAutomatiqueSurOrdonnacier", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                InscriptionSurOrdonnancierAutomatique = RecupererValeurExecuteScalaire("InscriptionAutomatiqueSurOrdonnacier", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                InscriptionSurOrdonnancierAutomatique = False
            End If

            If RecupererValeurExecuteScalaire("InterdirelaVenteDesProduitsPerimes", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                InterdireLaVenteDesPerimes = RecupererValeurExecuteScalaire("InterdirelaVenteDesProduitsPerimes", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                InterdireLaVenteDesPerimes = False
            End If

            If RecupererValeurExecuteScalaire("VerifierSiProduitPrisEnChargeParLaCNAM", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                VerifierProduitPrisEnChargeParCNAM = RecupererValeurExecuteScalaire("VerifierSiProduitPrisEnChargeParLaCNAM", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                VerifierProduitPrisEnChargeParCNAM = False
            End If

            'If RecupererValeurExecuteScalaire("PermettreUtiliserLesFrigosEnVente", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
            '    PermettreUtiliserFrigosEuVente = RecupererValeurExecuteScalaire("PermettreUtiliserLesFrigosEnVente", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            'Else
            '    PermettreUtiliserFrigosEuVente = False
            'End If


            If RecupererValeurExecuteScalaire("InterdireDeChoisirLesArticlesParDesignation", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                InterdireChoisirParDesignation = RecupererValeurExecuteScalaire("InterdireDeChoisirLesArticlesParDesignation", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                InterdireChoisirParDesignation = False
            End If

            If RecupererValeurExecuteScalaire("RetrancherDuStockLorsDuneVenteenInstance", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                RetarancheDuStockLorsDeVenteInstance = RecupererValeurExecuteScalaire("RetrancherDuStockLorsDuneVenteenInstance", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                RetarancheDuStockLorsDeVenteInstance = False
            End If

            If RecupererValeurExecuteScalaire("CmdGroupeJournaliere", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                CommandeGroupeJ = RecupererValeurExecuteScalaire("CmdGroupeJournaliere", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                CommandeGroupeJ = ""
            End If

            If RecupererValeurExecuteScalaire("NePAsSortirLesManquantsDepuis", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                NePasSortirManquantsDepuis = RecupererValeurExecuteScalaire("NePAsSortirLesManquantsDepuis", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                NePasSortirManquantsDepuis = ""
            End If

            If RecupererValeurExecuteScalaire("CongeDuAnneeCourant", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                CongeDuAnneeCourant = RecupererValeurExecuteScalaire("CongeDuAnneeCourant", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                'CongeDuAnneeCourant = ""
            End If

            If RecupererValeurExecuteScalaire("CongeAuAnneeCourant", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                CongeAuAnneeCourant = RecupererValeurExecuteScalaire("CongeAuAnneeCourant", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                'CongeAuAnneeCourant = ""
            End If

            If RecupererValeurExecuteScalaire("CongeDuAnneeProchaine", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                CongeDuAnneeProchaine = RecupererValeurExecuteScalaire("CongeDuAnneeProchaine", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                'CongeDuAnneeProchaine = ""
            End If

            If RecupererValeurExecuteScalaire("CongeAuAnneeProchaine", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                CongeAuAnneeProchaine = RecupererValeurExecuteScalaire("CongeAuAnneeProchaine", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                'CongeAuAnneeProchaine = ""
            End If

            If RecupererValeurExecuteScalaire("NomDeLordinateurDImpressionCodeABarre", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                NomOrdinateurImpressionCodeABarre = RecupererValeurExecuteScalaire("NomDeLordinateurDImpressionCodeABarre", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                NomOrdinateurImpressionCodeABarre = ""
            End If

            If RecupererValeurExecuteScalaire("InterventionAvecUnAssistantSiLePrixChange", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                InterventionAvecUnAssistant = RecupererValeurExecuteScalaire("InterventionAvecUnAssistantSiLePrixChange", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                InterventionAvecUnAssistant = False
            End If

            If RecupererValeurExecuteScalaire("AfficherLesDernieresDatesDePeremptionDansLesNouveauxAchats", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                AfficherLesDerniereDDPeremptionDansNouveauAvoirAchat = RecupererValeurExecuteScalaire("AfficherLesDernieresDatesDePeremptionDansLesNouveauxAchats", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                AfficherLesDerniereDDPeremptionDansNouveauAvoirAchat = False
            End If

            If RecupererValeurExecuteScalaire("AjouterTimbreAFacture", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                AjouterTimbreALaFacture = RecupererValeurExecuteScalaire("AjouterTimbreAFacture", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                AjouterTimbreALaFacture = False
            End If

            If RecupererValeurExecuteScalaire("MinimumDePerception", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                MinimumDePerception = RecupererValeurExecuteScalaire("MinimumDePerception", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                MinimumDePerception = 0
            End If

            If RecupererValeurExecuteScalaire("LecteurUpdate", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                LecteurUpdate = RecupererValeurExecuteScalaire("LecteurUpdate", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                LecteurUpdate = ""
            End If

            If RecupererValeurExecuteScalaire("LecteurSauvegarde", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                LecteurSauvegarde = RecupererValeurExecuteScalaire("LecteurSauvegarde", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                LecteurSauvegarde = ""
            End If

            If RecupererValeurExecuteScalaire("DelaiSauvegarde", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                DelaiSauvegarde = RecupererValeurExecuteScalaire("DelaiSauvegarde", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                DelaiSauvegarde = 0
            End If

            If RecupererValeurExecuteScalaire("NomDeLImprimante", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste")) <> "" Then
                NomDeLImprimante = RecupererValeurExecuteScalaire("NomDeLImprimante", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
            Else
                NomDeLImprimante = ""
            End If

            '*****************************************
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "SELECT CodePharmacie FROM PARAMETRE_PHARMACIE "
            Try
                CodePharmacien = cmd.ExecuteScalar().ToString
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            '*****************************************

            'If RecupererValeurExecuteScalaire("CodePharmacie", "PARAMETRE_PHARMACIE", "CodePharmacie", 1) <> "" Then
            '    CodePharmacien = RecupererValeurExecuteScalaire("CodePharmacie", "PARAMETRE_PHARMACIE", "CodePharmacie", 1)
            'Else
            '    CodePharmacien = ""
            'End If

            If RecupererValeurExecuteScalaire("Pharmacie", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
                Pharmacie = RecupererValeurExecuteScalaire("Pharmacie", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
            Else
                Pharmacie = ""
            End If
            If RecupererValeurExecuteScalaire("NCnam", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
                CNAMPharmacien = RecupererValeurExecuteScalaire("NCnam", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
            Else
                CNAMPharmacien = ""
            End If
            If RecupererValeurExecuteScalaire("Affiliation1", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
                NumeroAffiliation1 = RecupererValeurExecuteScalaire("Affiliation1", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
            Else
                NumeroAffiliation1 = ""
            End If
            If RecupererValeurExecuteScalaire("Affiliation2", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
                NumeroAffiliation2 = RecupererValeurExecuteScalaire("Affiliation2", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
            Else
                NumeroAffiliation2 = ""
            End If
            If RecupererValeurExecuteScalaire("Adresse", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
                AdressePharmacien = RecupererValeurExecuteScalaire("Adresse", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
            Else
                AdressePharmacien = ""
            End If

            If RecupererValeurExecuteScalaire("Telephone", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
                TelephonePharmacien = RecupererValeurExecuteScalaire("Telephone", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
            Else
                TelephonePharmacien = ""
            End If

            If RecupererValeurExecuteScalaire("Fax", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
                FaxPharmacien = RecupererValeurExecuteScalaire("Fax", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
            Else
                FaxPharmacien = ""
            End If

            If RecupererValeurExecuteScalaire("CodeTVA", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
                CodeTvaPharmacien = RecupererValeurExecuteScalaire("CodeTVA", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
            Else
                CodeTvaPharmacien = ""
            End If

            If RecupererValeurExecuteScalaire("Rib", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
                Rib = RecupererValeurExecuteScalaire("Rib", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
            Else
                Rib = ""
            End If

            If RecupererValeurExecuteScalaire("Messagederoulant", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
                Messagederoulant = RecupererValeurExecuteScalaire("Messagederoulant", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
            Else
                Messagederoulant = ""
            End If

            If RecupererValeurExecuteScalaire("Timbre", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien) <> "" Then
                Timbre = RecupererValeurExecuteScalaire("Timbre", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
            Else
                Timbre = 0
            End If

            If RecupererValeurExecuteScalaire("DateMigration", "PARAMETRE_PHARMACIE", "Code", 1) <> "" Then
                DateMigration = RecupererValeurExecuteScalaire("DateMigration", "PARAMETRE_PHARMACIE", "Code", 1)
            Else
                DateMigration = Nothing
            End If


            'Recupération des text afficher sur le boutons 
            Me.bLien1.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien1")
            Me.bLien2.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien2")
            Me.bLien3.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien3")
            Me.bLien4.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien4")
            Me.bLien5.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien5")
            Me.bLien6.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien6")
            Me.bLien7.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien7")
            Me.bLien8.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien8")
            Me.bLien9.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien9")
            Me.bLien10.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien10")
            Me.bLien11.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien11")
            Me.bLien12.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien12")
            Me.bLien13.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien13")
            Me.bLien14.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien14")
            Me.bLien15.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien15")
            Me.bLien16.Text = RecupererValeurExecuteScalaire("TextBouton", "LIENS_BOUTONS_BUREAU", "NomBouton", "bLien16")

            If Me.bLien1.Text = "" Then
                Me.bLien1.Text = "?"
            End If
            If Me.bLien2.Text = "" Then
                Me.bLien2.Text = "?"
            End If
            If Me.bLien3.Text = "" Then
                Me.bLien3.Text = "?"
            End If
            If Me.bLien4.Text = "" Then
                Me.bLien4.Text = "?"
            End If
            If Me.bLien5.Text = "" Then
                Me.bLien5.Text = "?"
            End If
            If Me.bLien6.Text = "" Then
                Me.bLien6.Text = "?"
            End If
            If Me.bLien7.Text = "" Then
                Me.bLien7.Text = "?"
            End If
            If Me.bLien8.Text = "" Then
                Me.bLien8.Text = "?"
            End If
            If Me.bLien9.Text = "" Then
                Me.bLien9.Text = "?"
            End If
            If Me.bLien10.Text = "" Then
                Me.bLien10.Text = "?"
            End If
            If Me.bLien11.Text = "" Then
                Me.bLien11.Text = "?"
            End If
            If Me.bLien12.Text = "" Then
                Me.bLien12.Text = "?"
            End If
            If Me.bLien13.Text = "" Then
                Me.bLien13.Text = "?"
            End If
            If Me.bLien14.Text = "" Then
                Me.bLien14.Text = "?"
            End If
            If Me.bLien15.Text = "" Then
                Me.bLien15.Text = "?"
            End If
            If Me.bLien16.Text = "" Then
                Me.bLien16.Text = "?"
            End If

            Me.KeyPreview = True
            AddHandler Me.KeyDown, AddressOf KeyDownHandler

            If CodeUtilisateur <> "NEXT" Then
                initIconBureau(-1)

                'Afficher Bureau
                AffciherBureau()
            End If

            Timer1.Start()

            'pour affiche la notification de la message d'envoi
            Me.NotifyIcon_MessageMain.Icon = SystemIcons.Information
            Me.NotifyIcon_MessageMain.Icon = SystemIcons.Application ' Affiche l'icône par défaut.
            Me.NotifyIcon_MessageMain.Icon = SystemIcons.Error ' Affiche l'icône d'erreur.
            Me.NotifyIcon_MessageMain.Icon = SystemIcons.Warning ' Affiche l'icône de danger.
            Me.NotifyIcon_MessageMain.Icon = SystemIcons.Question ' Affiche l'icône de question.
            Me.NotifyIcon_MessageMain.Icon = SystemIcons.Shield
        End If
    End Sub


    Private Sub bRaccourciPreparation_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRaccourciPreparation.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Préparations" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyListePreparation = New fListePreparation
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyListePreparation.PAnel)
        Tab.SelectedTab.Text = "Préparations"
        MyListePreparation.Init()
    End Sub

    Private Sub bRaccourciPharmacie_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Pharmacie" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyPharmacie = New fPharmacie
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyPharmacie.Panel)
        Tab.SelectedTab.Text = "Pharmacie"
        MyPharmacie.init()
    End Sub

    Private Sub bAjouterArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(2, "AJOUT_ARTICLE") = "False" Then
            Exit Sub
        End If

        Dim MyFicheArticle As New fFicheArticle
        MyFicheArticle.DesignationArticle = "-"
        MyFicheArticle.ajoutmodif = "A"
        MyFicheArticle.Init()
        MyFicheArticle.ShowDialog()
        MyFicheArticle.Close()
        MyFicheArticle.Dispose()
        'AfficherArticle()
    End Sub

    Private Sub bRecalculDeStock_Click_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRecalculDeStock.Click
        Dim MyListedeRecalculDeStock As New fListeDeRecalculDeStock
        MyListedeRecalculDeStock.ShowDialog()
        MyListedeRecalculDeStock.Close()
        MyListedeRecalculDeStock.Dispose()
    End Sub

    Public Function AfficheMessageNotification(ByVal text As String, ByVal type As String, ByVal time As Integer) As Boolean
        NotifyIcon_MessageMain.Visible = True

        If type = "Client" Then
            With NotifyIcon_MessageMain

                .BalloonTipIcon = ToolTipIcon.Info ' Icône information de Windows.
                .BalloonTipTitle = "PHARMA 2000 Premium  " ' Titre du message.
                .BalloonTipText = text ' Corps du message.

            End With
        End If

        Me.NotifyIcon_MessageMain.ShowBalloonTip(time)

    End Function

    Public Function AfficheMessageNotification(ByVal affiche As Boolean) As Boolean
        NotifyIcon_MessageMain.Visible = True
        If affiche Then

            With NotifyIcon_MessageMain

                .BalloonTipIcon = ToolTipIcon.Info ' Icône information de Windows.
                .BalloonTipTitle = "PHARMA 2000 Premium  " ' Titre du message.
                .BalloonTipText = "Votre E-mail à été envoyé avec succès" ' Corps du message.

            End With

            Me.NotifyIcon_MessageMain.ShowBalloonTip(0)

        Else

            With NotifyIcon_MessageMain

                .BalloonTipIcon = ToolTipIcon.Error ' Icône information de Windows.
                .BalloonTipTitle = "PHARMA 2000 Premium " ' Titre du message.
                .BalloonTipText = "Échec d'envoi de courrier électronique " ' Corps du message.

            End With

            Me.NotifyIcon_MessageMain.ShowBalloonTip(0)

        End If

    End Function

    Private Sub bMesVoisinages_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bMesVoisinages.Click
        Dim I As Integer

        'En cas ou les parametre de Lattitude et Longitude ne sont pas remplis
        If getLatLonPharmacie() = "" Then Exit Sub

        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Mes voisinages" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        myMesVoisinages = New fMesVoisinage
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(myMesVoisinages.Panel)

        Tab.SelectedTab.Text = "Mes voisinages"
        myMesVoisinages.Init()
    End Sub

    Private Function getLatLonPharmacie()

        Dim cmd As New SqlCommand

        cmd.Connection = ConnectionServeur
        cmd.CommandText = "SELECT top (1) [Latitude_Longitude] FROM [PARAMETRE_PHARMACIE] "

        Try
            If cmd.ExecuteScalar() = Nothing Then
                MsgBox("Vous devez aller sur [Paramétres Généreaux | Mes voisinages] ,  pour remplir les paramétres", MsgBoxStyle.Critical, "Mes voisinages")
                getLatLonPharmacie = ""
                Exit Function
            End If

            getLatLonPharmacie = cmd.ExecuteScalar()

        Catch ex As Exception
            Console.WriteLine(ex.Message)
            getLatLonPharmacie = ""
        End Try

    End Function

    Private Sub btnPicParametre1_3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre1_3.Click
        AjoutIconeBureau("1") '1_3
    End Sub

    Private Sub btnPicParametre1_4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre1_4.Click
        AjoutIconeBureau("2") '1_4
    End Sub

    Private Sub btnPicParametre1_5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre1_5.Click
        AjoutIconeBureau("3") '1_5
    End Sub

    Private Sub btnPicParametre1_6_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre1_6.Click
        AjoutIconeBureau("4") '1_6
    End Sub

    Private Sub btnPicParametre2_3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre2_3.Click
        AjoutIconeBureau("5") '2_3
    End Sub

    Private Sub btnPicParametre2_4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre2_4.Click
        AjoutIconeBureau("6") '2_4
    End Sub

    Private Sub btnPicParametre2_5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre2_5.Click
        AjoutIconeBureau("7") '2_5
    End Sub

    Private Sub btnPicParametre2_6_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre2_6.Click
        AjoutIconeBureau("8") '2_6
    End Sub

    Private Sub btnPicParametre3_3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre3_3.Click
        AjoutIconeBureau("9") '3_3
    End Sub

    Private Sub btnPicParametre3_4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre3_4.Click
        AjoutIconeBureau("10") '3_4
    End Sub

    Private Sub btnPicParametre3_5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre3_5.Click
        AjoutIconeBureau("11") '3_5
    End Sub

    Private Sub btnPicParametre3_6_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre3_6.Click
        AjoutIconeBureau("12") '3_6
    End Sub

    Private Sub btnPicParametre4_3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre4_3.Click
        AjoutIconeBureau("13") '4_3
    End Sub

    Private Sub btnPicParametre4_4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre4_4.Click
        AjoutIconeBureau("14") '4_4
    End Sub

    Private Sub btnPicParametre4_5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre4_5.Click
        AjoutIconeBureau("15") '4_5
    End Sub

    Private Sub btnPicParametre4_6_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPicParametre4_6.Click
        AjoutIconeBureau("16") '4_6
    End Sub

    Private Sub executerLien(ByVal pLien As String)
        Try
            Process.Start(pLien)
        Catch ex As Exception
            MsgBox("Lien invalide")
        End Try

    End Sub

    'Private Sub pic1_1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic
    '    executerLien(CType(monBueauLien(1), Label).Text)
    'End Sub

    'Private Sub pic1_2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
    '    executerLien(CType(monBueauLien(2), Label).Text)
    'End Sub

    Private Sub pic1_3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic1_3.Click
        executerLien(CType(monBueauLien(1), Label).Text)
    End Sub

    Private Sub pic1_4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic1_4.Click
        executerLien(CType(monBueauLien(2), Label).Text)
    End Sub

    Private Sub pic1_5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic1_5.Click
        executerLien(CType(monBueauLien(3), Label).Text)
    End Sub

    Private Sub pic1_6_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic1_6.Click
        executerLien(CType(monBueauLien(4), Label).Text)
    End Sub

    'Private Sub pic2_1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic
    '    executerLien(CType(monBueauLien(7), Label).Text)
    'End Sub

    'Private Sub pic2_2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
    '    executerLien(CType(monBueauLien(8), Label).Text)
    'End Sub

    Private Sub pic2_3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic2_3.Click
        executerLien(CType(monBueauLien(5), Label).Text)
    End Sub

    Private Sub pic2_4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic2_4.Click
        executerLien(CType(monBueauLien(6), Label).Text)
    End Sub

    Private Sub pic2_5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic2_5.Click
        executerLien(CType(monBueauLien(7), Label).Text)
    End Sub

    Private Sub pic2_6_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic2_6.Click
        executerLien(CType(monBueauLien(8), Label).Text)
    End Sub

    'Private Sub pic3_1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)Handles pic
    '    executerLien(CType(monBueauLien(13), Label).Text)
    'End Sub

    'Private Sub pic3_2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
    '    executerLien(CType(monBueauLien(14), Label).Text)
    'End Sub

    Private Sub pic3_3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic3_3.Click
        executerLien(CType(monBueauLien(9), Label).Text)
    End Sub

    Private Sub pic3_4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic3_4.Click
        executerLien(CType(monBueauLien(10), Label).Text)
    End Sub

    Private Sub pic3_5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic3_5.Click
        executerLien(CType(monBueauLien(11), Label).Text)
    End Sub

    Private Sub pic3_6_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic3_6.Click
        executerLien(CType(monBueauLien(12), Label).Text)
    End Sub

    Private Sub pic4_3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic4_3.Click
        executerLien(CType(monBueauLien(13), Label).Text)
    End Sub

    Private Sub pic4_4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic4_4.Click
        executerLien(CType(monBueauLien(14), Label).Text)
    End Sub

    Private Sub pic4_5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic4_5.Click
        executerLien(CType(monBueauLien(15), Label).Text)
    End Sub

    Private Sub pic4_6_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pic4_6.Click
        executerLien(CType(monBueauLien(16), Label).Text)
    End Sub

    Private Sub Tab_TabPageClosing(sender As Object, e As C1.Win.C1Command.TabPageCancelEventArgs) Handles Tab.TabPageClosing
        If Tab.SelectedTab Is tabBureau Then
            MsgBox("La page du bureau ne peut pas être fermée !", MsgBoxStyle.Critical, "Erreur")
            e.Cancel = True
        End If

        If Tab.SelectedTab.Text = "Inventaire" Then
            MyInventaire.SupprimerInventaire()

            Dim cmd As New SqlCommand
            StrSQL = "UPDATE INVENTAIRE set EnCours=0 WHERE Poste = " & Quote(System.Environment.GetEnvironmentVariable("Poste"))

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            Try
                cmd.ExecuteNonQuery()
            Catch
            End Try

        End If
    End Sub

    Private Sub RibbonButton5_Click_1(sender As System.Object, e As System.EventArgs) Handles RibbonButton5.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Bureau" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
    End Sub

    Private Sub RibbonButton51_Click(sender As System.Object, e As System.EventArgs) Handles RibbonButton51.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Bureau" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
    End Sub

    Private Sub RibbonButton511_Click(sender As System.Object, e As System.EventArgs) Handles RibbonButton511.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Bureau" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
    End Sub

    Private Sub RibbonButton512_Click(sender As System.Object, e As System.EventArgs) Handles RibbonButton512.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Bureau" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
    End Sub

    Private Sub RibbonButton513_Click(sender As System.Object, e As System.EventArgs) Handles RibbonButton513.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Bureau" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
    End Sub

    Private Sub RibbonButton514_Click(sender As System.Object, e As System.EventArgs) Handles RibbonButton514.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Bureau" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
    End Sub

    Private Sub RibbonButton515_Click(sender As System.Object, e As System.EventArgs) Handles RibbonButton515.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Bureau" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
    End Sub

    Private Sub RibbonButton516_Click(sender As System.Object, e As System.EventArgs) Handles RibbonButton516.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Bureau" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
    End Sub

    Private Sub bProjetCommande1_Click(sender As System.Object, e As System.EventArgs) Handles bProjetCommande1.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Projet de Commande" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyProjetCommande = New fProjetCommande
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyProjetCommande.Panel)

        Tab.SelectedTab.Text = "Projet de Commande"
        MyProjetCommande.Init()
    End Sub

    Private Sub bEtatEntree_Click(sender As System.Object, e As System.EventArgs) Handles bEtatEntree.Click
        If ControleDAcces(8, "ETAT_DES_ENTREES_SORTIES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat Entree" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEtatEntree = New fEtatEntreeArticle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEtatEntree.Panel)
        Tab.SelectedTab.Text = "Etat Entree"
        MyEtatEntree.Init()
    End Sub

    Private Sub bEtatSortie_Click(sender As System.Object, e As System.EventArgs) Handles bEtatSortie.Click
        If ControleDAcces(8, "ETAT_DES_ENTREES_SORTIES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat Sortie" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEtatSortie = New fEtatSortiArticle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEtatSortie.Panel)
        Tab.SelectedTab.Text = "Etat Sortie"
        MyEtatSortie.Init()
    End Sub

    Private Sub bAnalyseVenteDetail_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnalyseVenteDetail.Click

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Analyse des ventes détail" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        Dim MyCubeVenteDetail As New fCubeVenteDetail
        MyCubeVenteDetail.Mode = "Vente Detail"
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyCubeVenteDetail.Panel)
        Tab.SelectedTab.Text = "Analyse des ventes détail"
        MyCubeVenteDetail.Init()
    End Sub

    Private Sub bAnalyseAchat_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnalyseAchat.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Analyse des achats" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        Dim MyCubeVenteDetail As New fCubeVenteDetail
        MyCubeVenteDetail.Mode = "Achat Detail"
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyCubeVenteDetail.Panel)
        Tab.SelectedTab.Text = "Analyse des achats"
        MyCubeVenteDetail.Init()
    End Sub

    Private Sub bAnalyseVente_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnalyseVente.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Analyse des ventes" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        Dim MyCubeVenteDetail As New fCubeVenteDetail
        MyCubeVenteDetail.Mode = "Vente"
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyCubeVenteDetail.Panel)
        Tab.SelectedTab.Text = "Analyse des ventes"
        MyCubeVenteDetail.Init()
    End Sub

    Private Sub AfficherSpot()
        Dim I As Integer
        Dim DateVidage As String = ""

        dsSpot.Clear()
        cmdSpot.Connection = ConnectionServeur
        cmdSpot.CommandText = " SELECT CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse FROM VENTE WHERE Vider=1 "

        If Not IsDBNull(cmdSpot.ExecuteScalar) Then
            DateVidage = cmdSpot.ExecuteScalar.ToString
        End If

        cmdSpot.CommandText = " SELECT Indicateur,Valeur FROM Vue_Spot ORDER BY Rang ASC"


        daSpot = New SqlDataAdapter(cmdSpot)
        daSpot.Fill(dsSpot, "SPOT")


        With gSpot
            .Columns.Clear()
            .DataSource = dsSpot
            .DataMember = "SPOT"
            .Rebind(False)
            .Columns("Valeur").NumberFormat = "#"
            .Columns("Indicateur").Caption = "Indicateur " + DateVidage.ToString

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Indicateur").Width = 220
            '.Splits(0).DisplayColumns("Indicateur").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Valeur").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).SplitSizeMode = C1.Win.C1TrueDBGrid.SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gSpot)
            .RowHeight *= 1.5
            .RowDivider.Color = Color.White
            .Splits(0).DisplayColumns(0).ColumnDivider.Color = Color.White
            .Splits(0).DisplayColumns(1).ColumnDivider.Color = Color.White
            .Splits(0).DisplayColumns(1).Style.Font = New Font(.Style.Font, FontStyle.Bold)
        End With
    End Sub

    Private Sub gSpot_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles gSpot.Click
        'AfficherSpot()
    End Sub

    Private Sub gSpot_DoubleClick(sender As Object, e As System.EventArgs) Handles gSpot.DoubleClick
        Dim Requette As String = ""




        Try
            If ModeADMIN = "ADMIN" Then
                If gSpot.Item(gSpot.Row, "Valeur") > 0 Then
                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Suppression des clients" Then
                        Requette = "SELECT " + _
                                    "	 '' AS Numero " + _
                                    "	,LOG.DateTime AS Date " + _
                                    "	,REPLACE(LOG.Libelle, 'La Suppression du client ', '') AS Nom " + _
                                    "	,'' AS Montant " + _
                                    "	,'' AS LibelleNatureReglement " + _
                                    "	,'' AS DateEcheance " + _
                                    "	,'' AS LibelleReglement " + _
                                    "	,'' AS Nom1 " + _
                                    "FROM " + _
                                    "	LOG  " + _
                                    "WHERE " + _
                                    "	(CodeLog = 'SUPPRESSION_CLIENT') " + _
                                    "	AND (LOG.DateTime > (SELECT " + _
                                    "							CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse " + _
                                    "						FROM  " + _
                                    "							VENTE AS VENTE " + _
                                    "                       WHERE " + _
                                    "							(VENTE.Vider = 0)))"
                        '"	INNER JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = LOG.CodePersonnel  " + _









                        Dim MyListeRecapCaisse As New fListesPourSpot
                        MyListeRecapCaisse.Requette = Requette
                        MyListeRecapCaisse.Titre = "Suppression des clients"
                        MyListeRecapCaisse.ListeDesVentes = True
                        MyListeRecapCaisse.ShowDialog()
                        MyListeRecapCaisse.Close()
                        MyListeRecapCaisse.Dispose()

                    End If

                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Suppression des articles" Then
                        Requette = "SELECT " + _
                                    "	 '' AS Numero " + _
                                    "	,LOG.DateTime AS Date " + _
                                    "	,REPLACE(LOG.Libelle, 'La suppression de l article ', '') AS Nom " + _
                                    "	,'' AS Montant " + _
                                    "	,'' AS LibelleNatureReglement " + _
                                    "	,'' AS DateEcheance " + _
                                    "	,'' AS LibelleReglement " + _
                                    "	,'' AS Nom1 " + _
                                    "FROM " + _
                                    "	LOG  " + _
                                    "WHERE " + _
                                    "	(CodeLog = 'SUPPRESSION_ARTICLE') " + _
                                    "	AND (LOG.DateTime > (SELECT " + _
                                    "							CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse " + _
                                    "						FROM  " + _
                                    "							VENTE AS VENTE " + _
                                    "                       WHERE " + _
                                    "							(VENTE.Vider = 0)))"
                        '"	,UTILISATEUR.Nom AS Nom1 " + _
                        '"	INNER JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = LOG.CodePersonnel  " + _

                        Dim MyListeRecapCaisse As New fListesPourSpot
                        MyListeRecapCaisse.Requette = Requette
                        MyListeRecapCaisse.Titre = "Suppression des articles"
                        MyListeRecapCaisse.ListeDesVentes = True
                        MyListeRecapCaisse.ShowDialog()
                        MyListeRecapCaisse.Close()
                        MyListeRecapCaisse.Dispose()
                    End If





                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Vente des articles à surveiller" Then
                        Requette = "SELECT " + _
                                    "	 VENTE.NumeroVente AS Numero " + _
                                    "	,VENTE.Date AS Date " + _
                                    "	,VENTE_DETAILS.Designation AS Nom " + _
                                    "	,VENTE.TotalTTC AS Montant " + _
                                    "	,'' AS LibelleNatureReglement " + _
                                    "	,'' AS DateEcheance " + _
                                    "	,'' AS LibelleReglement " + _
                                    "	,UTILISATEUR.Nom AS Nom1 " + _
                                    "FROM " + _
                                    "	VENTE  " + _
                                    "   INNER JOIN VENTE_DETAILS ON VENTE.NumeroVente = VENTE_DETAILS.NumeroVente    " + _
                                    "	INNER JOIN NATURE_REGLEMENT ON VENTE.CodeNatureReglement = NATURE_REGLEMENT.CodeNatureReglement " + _
                                    "	INNER JOIN CLIENT ON VENTE.CodeClient = CLIENT.CodeClient " + _
                                    "	INNER JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = VENTE.CodePersonnel " + _
                                    "WHERE " + _
                                    "	 VENTE_DETAILS.CodeArticle IN (	SELECT  " + _
                                    "										CodeArticle  " + _
                                    "									FROM " + _
                                    "										ARTICLE_SURVEILLER) " + _
                                    "	AND (VENTE.Date > (SELECT " + _
                                    "							CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse " + _
                                    "						FROM " + _
                                    "							VENTE AS VENTE " + _
                                    "                       WHERE " + _
                                    "							(VENTE.Vider = 0))) "

                        Dim MyListeRecapCaisse As New fListesPourSpot
                        MyListeRecapCaisse.Requette = Requette
                        MyListeRecapCaisse.Titre = "Suppression des articles"
                        MyListeRecapCaisse.ListeDesVentes = True
                        MyListeRecapCaisse.ShowDialog()
                        MyListeRecapCaisse.Close()
                        MyListeRecapCaisse.Dispose()
                    End If























                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Suppression des ventes" Then
                        Requette = "SELECT " + _
                                    "	 VENTE_SUPPRIME.NumeroVente AS Numero " + _
                                    "	,VENTE_SUPPRIME.Date AS Date " + _
                                    "	,CLIENT.Nom AS Nom " + _
                                    "	,VENTE_SUPPRIME.TotalTTC AS Montant " + _
                                    "	,NATURE_REGLEMENT.LibelleNatureReglement AS LibelleNatureReglement " + _
                                    "	,'' AS DateEcheance " + _
                                    "	,'' AS LibelleReglement " + _
                                    "	,UTILISATEUR.Nom AS Nom1 " + _
                                    "FROM " + _
                                    "	VENTE_SUPPRIME  " + _
                                    "	INNER JOIN NATURE_REGLEMENT ON VENTE_SUPPRIME.CodeNatureReglement = NATURE_REGLEMENT.CodeNatureReglement " + _
                                    "	INNER JOIN CLIENT ON VENTE_SUPPRIME.CodeClient = CLIENT.CodeClient " + _
                                    "	INNER JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = VENTE_SUPPRIME.CodePersonnel " + _
                                    "WHERE " + _
                                    "	(VENTE_SUPPRIME.Date > (SELECT " + _
                                    "							    CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse " + _
                                    "						    FROM " + _
                                    "							    VENTE AS VENTE " + _
                                    "                           WHERE " + _
                                    "							    (VENTE.Vider = 0)))"

                        Dim MyListeRecapCaisse As New fListesPourSpot
                        MyListeRecapCaisse.Requette = Requette
                        MyListeRecapCaisse.Titre = "Suppression des ventes"
                        MyListeRecapCaisse.ListeDesVentes = True
                        MyListeRecapCaisse.ShowDialog()
                        MyListeRecapCaisse.Close()
                        MyListeRecapCaisse.Dispose()

                    End If

                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Suppression des achats" Then
                        Requette = " SELECT " + _
                                    "	 ACHAT_SUPPRIME.NumeroAchat AS Numero " + _
                                    "	,ACHAT_SUPPRIME.Date AS Date " + _
                                    "	,FOURNISSEUR.NomFournisseur AS Nom " + _
                                    "	,ACHAT_SUPPRIME.TotalTTC AS Montant " + _
                                    "	,'' AS LibelleNatureReglement " + _
                                    "	,'' AS DateEcheance " + _
                                    "	,'' AS LibelleReglement " + _
                                    "	,UTILISATEUR.Nom AS Nom1 " + _
                                    "FROM " + _
                                    "	ACHAT_SUPPRIME  " + _
                                    "	INNER JOIN FOURNISSEUR ON ACHAT_SUPPRIME.CodeFournisseur = FOURNISSEUR.CodeFournisseur " + _
                                    "	INNER JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = ACHAT_SUPPRIME.CodePersonnel " + _
                                    "WHERE " + _
                                    "   (ACHAT_SUPPRIME.Date > (SELECT " + _
                                    "							    CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse " + _
                                    "						    FROM " + _
                                    "							    VENTE AS VENTE " + _
                                    "                           WHERE " + _
                                    "							    (VENTE.Vider = 0)))"

                        Dim MyListeRecapCaisse As New fListesPourSpot
                        MyListeRecapCaisse.Requette = Requette
                        MyListeRecapCaisse.Titre = "Suppression des achats"
                        MyListeRecapCaisse.ListeDesVentes = True
                        MyListeRecapCaisse.ShowDialog()
                        MyListeRecapCaisse.Close()
                        MyListeRecapCaisse.Dispose()

                    End If

                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Modification des clients" Then
                        Requette = "SELECT " + _
                                    "	 '' AS Numero " + _
                                    "	,LOG.DateTime AS Date " + _
                                    "	,REPLACE(LOG.Libelle, 'La modification du client ', '') AS Nom " + _
                                    "	,'' AS Montant " + _
                                    "	,'' AS LibelleNatureReglement " + _
                                    "	,'' AS DateEcheance " + _
                                    "	,'' AS LibelleReglement " + _
                                    "	,UTILISATEUR.Nom AS Nom1 " + _
                                    "FROM " + _
                                    "	LOG  " + _
                                    "	INNER JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = LOG.CodePersonnel  " + _
                                    "WHERE " + _
                                    "	(CodeLog = 'MODIFICATION_CLIENT') " + _
                                    "	AND (LOG.DateTime > (SELECT " + _
                                    "							CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse " + _
                                    "						FROM  " + _
                                    "							VENTE AS VENTE " + _
                                    "                       WHERE " + _
                                    "							(VENTE.Vider = 0)))"

                        Dim MyListeRecapCaisse As New fListesPourSpot
                        MyListeRecapCaisse.Requette = Requette
                        MyListeRecapCaisse.Titre = "Modification des clients"
                        MyListeRecapCaisse.ListeDesVentes = True
                        MyListeRecapCaisse.ShowDialog()
                        MyListeRecapCaisse.Close()
                        MyListeRecapCaisse.Dispose()

                    End If

                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Modification des articles" Then
                        Requette = "SELECT " + _
                                    "	 '' AS Numero " + _
                                    "	,LOG.DateTime AS Date " + _
                                    "	,REPLACE(LOG.Libelle, 'La modification de l article ', '') AS Nom " + _
                                    "	,'' AS Montant " + _
                                    "	,'' AS LibelleNatureReglement " + _
                                    "	,'' AS DateEcheance " + _
                                    "	,'' AS LibelleReglement " + _
                                    "	,UTILISATEUR.Nom AS Nom1 " + _
                                    "FROM " + _
                                    "	LOG  " + _
                                    "	INNER JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = LOG.CodePersonnel  " + _
                                    "WHERE " + _
                                    "	(CodeLog = 'MODIFICATION_ARTICLE') " + _
                                    "	AND (LOG.DateTime > (SELECT " + _
                                    "							CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse " + _
                                    "						FROM  " + _
                                    "							VENTE AS VENTE " + _
                                    "                       WHERE " + _
                                    "							(VENTE.Vider = 0)))"

                        Dim MyListeRecapCaisse As New fListesPourSpot
                        MyListeRecapCaisse.Requette = Requette
                        MyListeRecapCaisse.Titre = "Modification des articles"
                        MyListeRecapCaisse.ListeDesVentes = True
                        MyListeRecapCaisse.ShowDialog()
                        MyListeRecapCaisse.Close()
                        MyListeRecapCaisse.Dispose()

                    End If

                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Modification des achats" Then
                        Requette = "SELECT " + _
                                    "	 '' AS Numero " + _
                                    "	,LOG.DateTime AS Date " + _
                                    "	,REPLACE(LOG.Libelle, 'La modification du client ', '') AS Nom " + _
                                    "	,'' AS Montant " + _
                                    "	,'' AS LibelleNatureReglement " + _
                                    "	,'' AS DateEcheance " + _
                                    "	,'' AS LibelleReglement " + _
                                    "	,UTILISATEUR.Nom AS Nom1 " + _
                                    "FROM " + _
                                    "	LOG  " + _
                                    "	INNER JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = LOG.CodePersonnel  " + _
                                    "WHERE " + _
                                    "	(CodeLog = 'MODIFICATION_ACHAT') " + _
                                    "	AND (LOG.DateTime > (SELECT " + _
                                    "							CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse " + _
                                    "						FROM  " + _
                                    "							VENTE AS VENTE " + _
                                    "                       WHERE " + _
                                    "							(VENTE.Vider = 0)))"

                        Dim MyListeRecapCaisse As New fListesPourSpot
                        MyListeRecapCaisse.Requette = Requette
                        MyListeRecapCaisse.Titre = "Modification des achats"
                        MyListeRecapCaisse.ListeDesVentes = True
                        MyListeRecapCaisse.ShowDialog()
                        MyListeRecapCaisse.Close()
                        MyListeRecapCaisse.Dispose()

                    End If

                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Changement des prix des articles" Then
                        Dim I As Integer
                        For I = 0 To Tab.TabPages.Count - 1
                            If Tab.TabPages(I).Text = "Historique des changements des prix" Then
                                Tab.TabPages(I).Show()
                                Exit Sub
                            End If
                        Next
                        MyHistoriqueDesChangementsPrix = New fHistoriqueDesChangementsDesPrix
                        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
                        Tab.SelectedIndex = Tab.TabPages.Count - 1
                        Tab.SelectedTab.Controls.Add(MyHistoriqueDesChangementsPrix.Panel)
                        Tab.SelectedTab.Text = "Historique des changements des prix"
                        MyHistoriqueDesChangementsPrix.init()
                        '    Requette = ""

                        '    Dim MyListeRecapCaisse As New fListesPourSpot
                        '    MyListeRecapCaisse.Requette = Requette
                        '    MyListeRecapCaisse.Titre = "Changement des prix des articles"
                        '    MyListeRecapCaisse.ListeDesVentes = True
                        '    MyListeRecapCaisse.ShowDialog()
                        '    MyListeRecapCaisse.Close()
                        '    MyListeRecapCaisse.Dispose()

                    End If

                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Ventes négative" Then
                        Requette = " SELECT " + _
                                    "	 VENTE.NumeroVente AS Numero " + _
                                    "	,VENTE.Date AS Date " + _
                                    "	,CLIENT.Nom AS Nom " + _
                                    "	,VENTE.TotalTTC AS Montant " + _
                                    "	,NATURE_REGLEMENT.LibelleNatureReglement AS LibelleNatureReglement " + _
                                    "	,VENTE.DateEcheance AS DateEcheance " + _
                                    "	,'' AS LibelleReglement " + _
                                    "	,UTILISATEUR.Nom AS Nom1 " + _
                                    "FROM " + _
                                    "	VENTE  " + _
                                    "	INNER JOIN NATURE_REGLEMENT ON VENTE.CodeNatureReglement = NATURE_REGLEMENT.CodeNatureReglement " + _
                                    "	INNER JOIN CLIENT ON VENTE.CodeClient = CLIENT.CodeClient " + _
                                    "	INNER JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = VENTE.CodePersonnel " + _
                                    "WHERE " + _
                                    "	(VENTE.TotalTTC < 0) " + _
                                    "	AND (VENTE.Date > (SELECT " + _
                                    "							CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse " + _
                                    "						FROM " + _
                                    "							VENTE AS VENTE " + _
                                    "                        WHERE " + _
                                    "							(VENTE.Vider = 0)))"

                        Dim MyListeRecapCaisse As New fListesPourSpot
                        MyListeRecapCaisse.Requette = Requette
                        MyListeRecapCaisse.Titre = "Ventes négative"
                        MyListeRecapCaisse.ListeDesVentes = True
                        MyListeRecapCaisse.ShowDialog()
                        MyListeRecapCaisse.Close()
                        MyListeRecapCaisse.Dispose()

                    End If

                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Remise à la vente" Then
                        Requette = " SELECT " + _
                                    "	 VENTE.NumeroVente AS Numero " + _
                                    "	,VENTE.Date AS Date " + _
                                    "	,CLIENT.Nom AS Nom " + _
                                    "	,VENTE.TotalTTC AS Montant " + _
                                    "	,NATURE_REGLEMENT.LibelleNatureReglement AS LibelleNatureReglement " + _
                                    "	,VENTE.DateEcheance AS DateEcheance " + _
                                    "	,'' AS LibelleReglement " + _
                                    "	,UTILISATEUR.Nom AS Nom1 " + _
                                    "FROM " + _
                                    "	VENTE  " + _
                                    "	INNER JOIN NATURE_REGLEMENT ON VENTE.CodeNatureReglement = NATURE_REGLEMENT.CodeNatureReglement " + _
                                    "	INNER JOIN CLIENT ON VENTE.CodeClient = CLIENT.CodeClient " + _
                                    "	INNER JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = VENTE.CodePersonnel " + _
                                    "WHERE " + _
                                    "	(VENTE.TotalRemise > 0) " + _
                                    "	AND (VENTE.Date > (SELECT " + _
                                    "							CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse " + _
                                    "						FROM " + _
                                    "							VENTE AS VENTE " + _
                                    "                        WHERE " + _
                                    "							(VENTE.Vider = 0)))"

                        Dim MyListeRecapCaisse As New fListesPourSpot
                        MyListeRecapCaisse.Requette = Requette
                        MyListeRecapCaisse.Titre = "Remise à la vente"
                        MyListeRecapCaisse.ListeDesVentes = True
                        MyListeRecapCaisse.ShowDialog()
                        MyListeRecapCaisse.Close()
                        MyListeRecapCaisse.Dispose()

                    End If

                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Règlement avec remise" Then
                        Requette = "SELECT     " + _
                                    "	 REGLEMENT_CLIENT.NumeroReglementClient AS Numero " + _
                                    "	,REGLEMENT_CLIENT.Date AS Date " + _
                                    "	,CLIENT.Nom AS Nom " + _
                                    "	,REGLEMENT_CLIENT.Montant AS Montant " + _
                                    "	,NATURE_REGLEMENT.LibelleNatureReglement AS LibelleNatureReglement " + _
                                    "	,REGLEMENT_CLIENT.DateEcheance AS DateEcheance " + _
                                    "	,REGLEMENT_CLIENT.LibelleReglement AS LibelleReglement " + _
                                    "	,UTILISATEUR.Nom AS Nom1 " + _
                                    "FROM  " + _
                                    "	REGLEMENT_CLIENT  " + _
                                    "	INNER JOIN CLIENT ON CLIENT.CodeClient = REGLEMENT_CLIENT.CodeClient  " + _
                                    "	INNER JOIN NATURE_REGLEMENT ON REGLEMENT_CLIENT.CodeNatureReglement = NATURE_REGLEMENT.CodeNatureReglement " + _
                                    "	INNER JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = REGLEMENT_CLIENT.CodePersonnel  " + _
                                    "WHERE      " + _
                                    "	NATURE_REGLEMENT.LibelleNatureReglement = 'REMISE' " + _
                                    "	AND (REGLEMENT_CLIENT.Date > (SELECT      " + _
                                    "									CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse " + _
                                    "								FROM           " + _
                                    "									VENTE AS VENTE " + _
                                    "							   WHERE       " + _
                                    "									(VENTE.Vider = 0))) "

                        Dim MyListeRecapCaisse As New fListesPourSpot
                        MyListeRecapCaisse.Requette = Requette
                        MyListeRecapCaisse.Titre = "Règlement avec remise"
                        MyListeRecapCaisse.ListeDesVentes = True
                        MyListeRecapCaisse.ShowDialog()
                        MyListeRecapCaisse.Close()
                        MyListeRecapCaisse.Dispose()

                    End If

                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Ajout des articles" Then
                        Requette = "SELECT " + _
                                    "	 '' AS Numero " + _
                                    "	,LOG.DateTime AS Date " + _
                                    "	,REPLACE(LOG.Libelle, 'L ajout du article ', '') AS Nom " + _
                                    "	,'' AS Montant " + _
                                    "	,'' AS LibelleNatureReglement " + _
                                    "	,'' AS DateEcheance " + _
                                    "	,'' AS LibelleReglement " + _
                                    "	,UTILISATEUR.Nom AS Nom1 " + _
                                    "FROM " + _
                                    "	LOG  " + _
                                    "	INNER JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = LOG.CodePersonnel  " + _
                                    "WHERE " + _
                                    "	(CodeLog = 'AJOUT_ARTICLE') " + _
                                    "	AND (LOG.DateTime > (SELECT " + _
                                    "							CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse " + _
                                    "						FROM  " + _
                                    "							VENTE AS VENTE " + _
                                    "                       WHERE " + _
                                    "							(VENTE.Vider = 0)))"

                        Dim MyListeRecapCaisse As New fListesPourSpot
                        MyListeRecapCaisse.Requette = Requette
                        MyListeRecapCaisse.Titre = "Ajout des articles"
                        MyListeRecapCaisse.ListeDesVentes = True
                        MyListeRecapCaisse.ShowDialog()
                        MyListeRecapCaisse.Close()
                        MyListeRecapCaisse.Dispose()

                    End If

                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Ajout des clients" Then
                        Requette = "SELECT " + _
                                    "	 '' AS Numero " + _
                                    "	,LOG.DateTime AS Date " + _
                                    "	,REPLACE(LOG.Libelle, 'L ajout du client ', '') AS Nom " + _
                                    "	,'' AS Montant " + _
                                    "	,'' AS LibelleNatureReglement " + _
                                    "	,'' AS DateEcheance " + _
                                    "	,'' AS LibelleReglement " + _
                                    "	,UTILISATEUR.Nom AS Nom1 " + _
                                    "FROM " + _
                                    "	LOG  " + _
                                    "	INNER JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = LOG.CodePersonnel  " + _
                                    "WHERE " + _
                                    "	(CodeLog = 'AJOUT_CLIENT') " + _
                                    "	AND (LOG.DateTime > (SELECT " + _
                                    "							CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse " + _
                                    "						FROM  " + _
                                    "							VENTE AS VENTE " + _
                                    "                       WHERE " + _
                                    "							(VENTE.Vider = 0)))"

                        Dim MyListeRecapCaisse As New fListesPourSpot
                        MyListeRecapCaisse.Requette = Requette
                        MyListeRecapCaisse.Titre = "Ajout des clients"
                        MyListeRecapCaisse.ListeDesVentes = True
                        MyListeRecapCaisse.ShowDialog()
                        MyListeRecapCaisse.Close()
                        MyListeRecapCaisse.Dispose()

                    End If

                    If gSpot.Item(gSpot.Row, "Indicateur").ToString = "Ventes à crédit" Then
                        Requette = " SELECT " + _
                                    "	 VENTE.NumeroVente AS Numero " + _
                                    "	,VENTE.Date AS Date " + _
                                    "	,CLIENT.Nom AS Nom " + _
                                    "	,VENTE.TotalTTC AS Montant " + _
                                    "	,NATURE_REGLEMENT.LibelleNatureReglement AS LibelleNatureReglement " + _
                                    "	,VENTE.DateEcheance AS DateEcheance " + _
                                    "	,'' AS LibelleReglement " + _
                                    "	,UTILISATEUR.Nom AS Nom1 " + _
                                    "FROM " + _
                                    "	VENTE  " + _
                                    "	INNER JOIN NATURE_REGLEMENT ON VENTE.CodeNatureReglement = NATURE_REGLEMENT.CodeNatureReglement " + _
                                    "	INNER JOIN CLIENT ON VENTE.CodeClient = CLIENT.CodeClient " + _
                                    "	INNER JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = VENTE.CodePersonnel " + _
                                    "WHERE " + _
                                    "	(NATURE_REGLEMENT.LibelleNatureReglement = 'CREDIT') " + _
                                    "	AND (VENTE.Date > (SELECT " + _
                                    "							CAST(MIN(Date) AS SMALLDATETIME) AS DateVidageCaisse " + _
                                    "						FROM " + _
                                    "							VENTE AS VENTE " + _
                                    "                        WHERE " + _
                                    "							(VENTE.Vider = 0)))"

                        Dim MyListeRecapCaisse As New fListesPourSpot
                        MyListeRecapCaisse.Requette = Requette
                        MyListeRecapCaisse.Titre = "Ventes à crédit"
                        MyListeRecapCaisse.ListeDesVentes = True
                        MyListeRecapCaisse.ShowDialog()
                        MyListeRecapCaisse.Close()
                        MyListeRecapCaisse.Dispose()

                    End If
                End If
            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub gSpot_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gSpot.FetchRowStyle
        If CDbl(gSpot(e.Row, "Valeur")) = 0 Then
            e.CellStyle.ForeColor = Color.Gray
        Else
            e.CellStyle.ForeColor = Color.Black
        End If

    End Sub

    Private Sub bSiteNext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSiteNext.Click
        Dim Client As String = ""
        Dim NumeroPoste As String = ""
        Dim Utilisateur As String = ""

        Client = CodePharmacien
        Utilisateur = NomUtilisateur
        NumeroPoste = System.Environment.GetEnvironmentVariable("Poste")


        Dim codeC As String = System.Web.HttpUtility.UrlEncode(EncryptAccesSite(Client.Trim()))
        Dim PostN As String = System.Web.HttpUtility.UrlEncode(EncryptAccesSite(NumeroPoste.Trim()))
        Dim NomOp As String = System.Web.HttpUtility.UrlEncode(EncryptAccesSite(Utilisateur.Trim()))

        Process.Start("http://www.nextsoftware.com.tn/ReclamationClient.aspx?CodeClient=" & codeC + "&Poste=" & PostN + "&NomOperateur=" & NomOp)
    End Sub

    Private Sub bRemiseAZero_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRemiseAZero.Click

    End Sub

    Private Sub bEtatInventaire_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bEtatInventaire.Click
        If ControleDAcces(8, "ETAT_DES_ENTREES_SORTIES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat Inventaire" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyEtatInventaire = New fEtatInventaire
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyEtatInventaire.Panel)
        Tab.SelectedTab.Text = "Etat Inventaire"
        MyEtatInventaire.Init()
    End Sub

    Private Sub bCorrectionLotArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCorrectionLotArticle.Click
        CorrectionDateLotArticle()
    End Sub

    Private Sub RibbonButton381_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RibbonButton381.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Historique des achats" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyHistoriqueDesAchats = New fHistoriqueDesAchats
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyHistoriqueDesAchats.Panel)
        Tab.SelectedTab.Text = "Historique des achats"
        MyHistoriqueDesAchats.init()
    End Sub

    Private Sub bMouvementArticles_Click(sender As System.Object, e As System.EventArgs) Handles bMouvementArticles.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Mouvement des articles" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyNMouvementArticle = New fNMouvementArticle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyNMouvementArticle.Panel)
        Tab.SelectedTab.Text = "Mouvement des articles"
        MyNMouvementArticle.init()
    End Sub

    Private Sub bVidageBD_Click(sender As System.Object, e As System.EventArgs) Handles bVidageBD.Click
        If MsgBox("Voulez vous vraiment vider la base  ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Vidage Pharama") = MsgBoxResult.Yes Then
            If MsgBox("Voulez vous vraiment vider la base  ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Vidage Pharama") = MsgBoxResult.Yes Then
                If (1 = 2) Then
                    Try
                        Dim cmd As New SqlCommand
                        StrSQL = "EXEC VidagePharma "
                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        cmd.ExecuteNonQuery()
                        MsgBox("opération terminé avec succès !", MsgBoxStyle.Critical, "Erreur")
                    Catch ex As Exception
                        MsgBox("Le solde de ce fournisseur différent de 0, Suppression réfusé !", MsgBoxStyle.Critical, "Erreur")
                    End Try
                End If
            End If
        End If
    End Sub

    Private Sub bCopieBD_Click(sender As System.Object, e As System.EventArgs) Handles bCopieBD.Click

        Dim NomServeurCopie As String = GetSetting("PHARMA", "PHARMA", "NomServeurCopie", "")
        Dim NomBaseDonnesCopie As String = GetSetting("PHARMA", "PHARMA", "NomBaseCopie", "")

        If NomBaseDonnesCopie = "" Or NomBaseDonnesCopie = "" Then
            MsgBox("Veuiller bien remplir les paramètres de connexion de BD Copie")
            Exit Sub
        End If

        fCopieBD.ShowDialog()
    End Sub

    Private Sub gSpot_GotFocus(sender As Object, e As System.EventArgs) Handles gSpot.GotFocus
        AfficherSpot()
    End Sub

    Private Sub RibbonButton3_Click_1(sender As System.Object, e As System.EventArgs) Handles RibbonButton3.Click
        MyVenteAnnuler = New fVenteAnnuler
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyVenteAnnuler.Panel)
        Tab.SelectedTab.Text = "Ventes Annulées"
        MyVenteAnnuler.init()
    End Sub

    Private Sub bAmouvementArticle_Click(sender As Object, e As EventArgs) Handles bAmouvementArticle.Click
        If ControleDAcces(8, "MOUVEMENT_DES_ARTICLES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Mouvement article" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyMouvementArticle = New fMouvementArticle
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyMouvementArticle.Panel)
        Tab.SelectedTab.Text = "Mouvement article"
        MyMouvementArticle.init()

    End Sub

    Private Sub C1Ribbon1_DoubleClick(sender As Object, e As EventArgs) Handles C1Ribbon1.DoubleClick
        C1Ribbon1.Minimized = True
    End Sub
    Private Sub Quitter()
        Dim fMessageBoxPourQuitter As fMessageBoxPourQuitter = New fMessageBoxPourQuitter

        fMessageBoxPourQuitter.ShowDialog()

        If fMessageBoxPourQuitter.TypeFermeture = "Annuler" Then
            TypeFermeture = "Annuler"
            Exit Sub
        End If

        If fMessageBoxPourQuitter.TypeFermeture = "QuitterSansMiseAjour" Then
            End
        End If

        If fMessageBoxPourQuitter.TypeFermeture = "QuitterAvecMiseAjour" Then
            EnregistrementBase()
            End
        End If
    End Sub

    Private Sub EnregistrementBase()
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim NomFichier As String = ""
        Dim NomFichierAEnvoyer As String

        NomFichier = LecteurSauvegarde + "\Backup_" + NomBase + "_" + Format(Now, "dd-MM-yyyy-hh-mm-ss") + ".bak"
        Try
            File.Delete(NomFichier)
            StrSQL = "BACKUP DATABASE " + NomBase + " TO DISK = " + Quote(NomFichier)
            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            cmd.CommandTimeout = 0
            cmd.ExecuteNonQuery()

            Try
                cmd.Connection = ConnectionServeur
                cmd.CommandText = "SELECT LecteurSauvegardeDistant FROM PARAMETRES WHERE POSTE = " + Quote(System.Environment.GetEnvironmentVariable("Poste"))


                File.Copy(NomFichier, cmd.ExecuteScalar + "\Backup_" + NomBase + "_" + Format(Now, "dd-MM-yyyy-hh-mm-ss") + ".bak")
            Catch
            End Try

            Try
                NomFichierAEnvoyer = "Sauvegarde " + CodePharmacien + ".ok"

                'Génération du fichier 

                If File.Exists(LecteurSauvegarde + "\" + NomFichierAEnvoyer) Then
                    File.Delete(LecteurSauvegarde + "\" + NomFichierAEnvoyer)
                End If

                Dim FichierSauv As New StreamWriter(LecteurSauvegarde + "\" + NomFichierAEnvoyer)

                FichierSauv.WriteLine(CodePharmacien)
                FichierSauv.WriteLine(Format(Now, "dd-MM-yyyy-hh-mm-ss"))
                FichierSauv.WriteLine(System.Environment.GetEnvironmentVariable("Poste"))
                FichierSauv.WriteLine(NomUtilisateur)
                FichierSauv.WriteLine("Manuelle")
                FichierSauv.Close()

                'Paramètrage de la connexion
                ClientFTP.Hostname = "************"
                ClientFTP.Username = "PHARMA2000"
                ClientFTP.Password = "Next;3U7+s4"

                Dim FtpInstance As New FTP
                FtpInstance.ConnectFTP(ClientFTP.Hostname, ClientFTP.Username, ClientFTP.Password)

                'suppression dans le serveur FTP
                FtpInstance.ListDirectory("Sauvegarde")
                For Each yLigne As String In FtpInstance.Liste
                    If yLigne = NomFichierAEnvoyer Then
                        FtpInstance.DeleteFile("Sauvegarde\" + NomFichierAEnvoyer)
                        Exit For
                    End If
                Next

                FtpInstance.Upload("Sauvegarde\" + NomFichierAEnvoyer, LecteurSauvegarde + "\" + NomFichierAEnvoyer)

                If File.Exists(LecteurSauvegarde + "\" + NomFichierAEnvoyer) Then
                    File.Delete(LecteurSauvegarde + "\" + NomFichierAEnvoyer)
                End If

            Catch ex As Exception

            End Try

            MsgBox("Sauvegarde achevée !", MsgBoxStyle.Information, "Information")
            InsertionDansLog("SAUVEGARDE_BASE", "Réussite de la sauvegarde manuelle !", CodeUtilisateur, System.DateTime.Now, "BASE", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)

        Catch ex As Exception
            InsertionDansLog("SAUVEGARDE_BASE", "Erreur de sauvegarde vers " + NomFichier + " : " + ex.Message, CodeUtilisateur, System.DateTime.Now, "BASE", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
            MsgBox("Erreur de sauvegarde vers " + NomFichier + " : " + ex.Message, MsgBoxStyle.Critical, "Erreur")
        End Try
    End Sub

    Private Sub RibbonButton6_Click(sender As Object, e As EventArgs)

        'If ControleDAcces(9, "SUPPRESSION_DES_VENTES") = "False" Then
        '    Exit Sub
        'End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Suppression Vente" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyVenteNonEnregistree = New fVenteNonEnregistree
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyVenteNonEnregistree.Panel)
        Tab.SelectedTab.Text = "Suppression Vente"
        MyVenteNonEnregistree.init()
    End Sub

    Private Sub bSaisiePerime_Click(sender As Object, e As EventArgs) Handles bSaisiePerime.Click
        If ControleDAcces(8, "PERIMES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Saisie Périmés" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MySaisiePerimes = New fSaisiePerimes
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MySaisiePerimes.Panel)
        Tab.SelectedTab.Text = "Saisie Périmés"
        MySaisiePerimes.Init()
    End Sub

    Private Sub bReglementSupprime_Click(sender As Object, e As EventArgs) Handles bReglementSupprime.Click
        If ControleDAcces(1, "REGLEMENT_SUPPRIMES") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Règlements supprimés" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyReglemntSupprime = New fReglemntSupprime
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyReglemntSupprime.Panel)
        Tab.SelectedTab.Text = "Règlements supprimés"
        MyReglemntSupprime.init()
    End Sub

    Private Sub bArticleSansInventaire_Click(sender As Object, e As EventArgs) Handles bArticleSansInventaire.Click
        If ControleDAcces(8, "ARTICLES_SANS_INVENTAIRE") = "False" Then
            Exit Sub
        End If

        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Etat articles sans inventaire" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyfListeArticleSansInventaire = New fListeArticleSansInventaire
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyfListeArticleSansInventaire.Panel)
        Tab.SelectedTab.Text = "Etat articles sans inventaire"
        MyfListeArticleSansInventaire.Init()
    End Sub

    Private Sub RibbonButton6_Click_1(sender As Object, e As EventArgs) Handles RibbonButton6.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Avoir" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyAvoirAchat = New FAvoirAchat
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyAvoirAchat.Panel)
        Tab.SelectedTab.Text = "Avoir"
        MyAvoirAchat.Init()
    End Sub

    Private Sub RibbonButton7_Click(sender As Object, e As EventArgs) Handles RibbonButton7.Click
        Dim I As Integer
        For I = 0 To Tab.TabPages.Count - 1
            If Tab.TabPages(I).Text = "Historique Mouvement des articles" Then
                Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        MyHistoriqueMouvementArticle = New fHistoriquesMouvements
        Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        Tab.SelectedIndex = Tab.TabPages.Count - 1
        Tab.SelectedTab.Controls.Add(MyHistoriqueMouvementArticle.Panel)
        Tab.SelectedTab.Text = "Historique Mouvement des articles"
        MyHistoriqueMouvementArticle.init()
    End Sub
End Class
