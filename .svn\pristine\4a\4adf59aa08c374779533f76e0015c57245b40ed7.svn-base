﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fMouvementPharmacien

    Dim StrSQL As String = ""

    Dim cmdMouvement As New SqlCommand
    Dim daMouvement As New SqlDataAdapter
    Dim dsMouvement As New DataSet

    Dim VOrderBy As String = "Designation"
    Dim VAscDesc As String = "Asc"


    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub Init()
        Dim I As Integer

        'Vider la dataset 
        dsMouvement.Clear()

        'chargement des noms Pharmaciens
        StrSQL = " SELECT CodePharmacie, Nom " + _
                 " FROM  Pharmacie " + _
                 " ORDER BY Nom ASC"
        cmdMouvement.Connection = ConnectionServeur
        cmdMouvement.CommandText = StrSQL
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "Pharmacie")
        cmbPharmacien.DataSource = dsMouvement.Tables("Pharmacie")
        cmbPharmacien.ValueMember = "CodePharmacie"
        cmbPharmacien.DisplayMember = "Nom"
        cmbPharmacien.ColumnHeaders = False
        cmbPharmacien.Splits(0).DisplayColumns("CodePharmacie").Visible = False
        cmbPharmacien.Splits(0).DisplayColumns("Nom").Width = 10
        cmbPharmacien.ExtendRightColumn = True

        'Initialiser les h
        dtpDebut.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDebut.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpFin.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpFin.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpDebut.Value = CDate(Date.Today.ToShortDateString + " 00:00:00")
        dtpFin.Value = CDate(Date.Today.ToShortDateString + " 23:59:59")

        'Initialiser les table
        cmdMouvement.CommandText = " SELECT TOP(0) Type, Numero, DateMouvement, " + _
                                   " CASE Type WHEN 'Emprunt' THEN TotalHT ELSE 0 END AS Emprunt, " + _
                                   " CASE Type WHEN 'Prêt' THEN TotalHT ELSE 0 END AS Pret, " + _
                                   " CASE Type WHEN 'Règlement' THEN TotalHT ELSE 0 END AS Reglement, " + _
                                   " CodeABarre, Designation, Quantite, TotalHT, TotalTVA " + _
                                   " FROM Vue_MouvementPharmacie "

        cmdMouvement.Connection = ConnectionServeur
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "MouvementPharmacie")

        With gDetails
            .Columns.Clear()
            .DataSource = dsMouvement
            .DataMember = "MouvementPharmacie"
            .Rebind(False)
            .Columns("Type").Caption = "Type"
            .Columns("Numero").Caption = "Numero"
            .Columns("DateMouvement").Caption = "Date"
            .Columns("Emprunt").Caption = "Emprunt"
            .Columns("Pret").Caption = "Prêt"
            .Columns("CodeABarre").Caption = "Code Article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("Quantite").Caption = "Qté"
            .Columns("Emprunt").NumberFormat = "#,###0.000"
            .Columns("Pret").NumberFormat = "#,###0.000"
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Type").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Emprunt").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Pret").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("CodeABarre").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("TotalHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTVA").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("Type").Width = 80
            .Splits(0).DisplayColumns("Numero").Width = 120
            .Splits(0).DisplayColumns("DateMouvement").Width = 100
            .Splits(0).DisplayColumns("Emprunt").Width = 100
            .Splits(0).DisplayColumns("Pret").Width = 100
            .Splits(0).DisplayColumns("CodeABarre").Width = 120
            .Splits(0).DisplayColumns("Designation").Width = 320
            .Splits(0).DisplayColumns("TotalHT").Visible = False
            .Splits(0).DisplayColumns("TotalTVA").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gDetails)
        End With

        rdbNonVidees.Checked = True

        AfficherMouvement()

        cmbPharmacien.Focus()

    End Sub

    Public Sub AfficherMouvement()
        Dim I As Integer

        Dim TotReg As Decimal = 0
        Dim TotPret As Decimal = 0
        Dim TotEmprunt As Decimal = 0
        Dim TotTVAEmprunt As Decimal = 0
        Dim TotaTVAPret As Decimal = 0
        Dim Cond As String = " AND 1=1 "

        Try
            dsMouvement.Tables("MouvementPharmacie").Clear()
        Catch
        End Try

        'Composer la condition de la requête    
        If cmbPharmacien.Text = "" Then
            Exit Sub
        End If

        If rdbNonVidees.Checked = True Then
            Cond += " AND Vider=0 "
        Else
            Cond += " AND DateMouvement Between '" & dtpDebut.Value & "' AND '" & dtpFin.Value & "' "
        End If

        cmdMouvement.CommandText = " SELECT Type, Numero, DateMouvement, " + _
                                   " CASE Type WHEN 'Emprunt' THEN TotalHT ELSE 0 END AS Emprunt, " + _
                                   " CASE Type WHEN 'Prêt' THEN TotalHT ELSE 0 END AS Pret, " + _
                                   " CASE Type WHEN 'Règlement' THEN TotalHT ELSE 0 END AS Reglement, " + _
                                   " CodeABarre, Designation, Quantite, TotalHT, TotalTVA " + _
                                   " FROM Vue_MouvementPharmacie " + _
                                   " WHERE CodePharmacie = '" & cmbPharmacien.SelectedValue & "' " + _
                                   Cond

        cmdMouvement.Connection = ConnectionServeur
        daMouvement = New SqlDataAdapter(cmdMouvement)

        daMouvement.Fill(dsMouvement, "MouvementPharmacie")

        'Calcule Solde Actuel
        cmdMouvement.CommandText = " SELECT " + _
                                   " SUM(CASE Type WHEN 'Prêt' THEN TotalHT ELSE -TotalHT END) AS TotalHT, " + _
                                   " SUM(CASE Type WHEN 'Prêt' THEN TotalTVA ELSE -TotalTVA END) AS TotalTVA  " + _
                                   " FROM Vue_MouvementPharmacie " + _
                                   " WHERE CodePharmacie = '" & cmbPharmacien.SelectedValue & "' "
        cmdMouvement.Connection = ConnectionServeur
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "SoldePharmacie")

        If dsMouvement.Tables("SoldePharmacie").Rows(0)("TotalHT").ToString = "" Then
            lSoldeactHT.Text = 0.0
        Else
            lSoldeactHT.Text = Math.Round(dsMouvement.Tables("SoldePharmacie").Rows(0)("TotalHT"), 3)
        End If

        If dsMouvement.Tables("SoldePharmacie").Rows(0)("TotalTVA").ToString = "" Then
            lSoldeActTVA.Text = 0.0
        Else
            lSoldeActTVA.Text = Math.Round(dsMouvement.Tables("SoldePharmacie").Rows(0)("TotalTVA"), 3)
        End If

        lSoldeActTTC.Text = Math.Round(Calcule_Solde(cmbPharmacien.SelectedValue), 3)

        dsMouvement.Tables("SoldePharmacie").Clear()


        'Solde Periode
        For I = 0 To gDetails.RowCount - 1
            TotPret += gDetails(I, "Pret")
            TotEmprunt += gDetails(I, "Emprunt")
            TotReg += gDetails(I, "Reglement")

            Select Case gDetails(I, "Type")
                Case "Emprunt"
                    TotTVAEmprunt += gDetails(I, "TotalTVA")
                Case "Prêt"
                    TotaTVAPret += gDetails(I, "TotalTVA")
            End Select
        Next

        lTotalEmprunt.Text = TotEmprunt
        lTotalPret.Text = TotPret
        lTotReglement.Text = TotReg

        lSoldePerHT.Text = Math.Round(TotEmprunt - TotPret + TotReg, 3) * -1
        lSoldePerTVA.Text = Math.Round(TotTVAEmprunt - TotaTVAPret, 3) * -1
        lSoldePertTTC.Text = Math.Round(CDec(lSoldePerHT.Text) + CDec(lSoldePerTVA.Text), 3)


    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        If cmbPharmacien.Text = "" Then
            Exit Sub
        End If

        Dim CondCrystal As String = ""
        Dim FieldDef As CrystalDecisions.CrystalReports.Engine.FieldDefinition

        If rdbNonVidees.Checked = True Then
            CondCrystal = "{Vue_MouvementPharmacie.Vider}=False AND {Vue_MouvementPharmacie.CodePharmacie} = '" & cmbPharmacien.SelectedValue & "' "
        Else
            CondCrystal = " 1=1 AND {Vue_MouvementPharmacie.CodePharmacie} = '" & cmbPharmacien.SelectedValue & "' " + _
                     " AND date({Vue_MouvementPharmacie.DateMouvement}) >= date('" & dtpDebut.Text & "') " + _
                     " AND date({Vue_MouvementPharmacie.DateMouvement}) <= date('" & dtpFin.Text & "') "
        End If

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression des mouvements de la pharmacie" Then
                num = I
            End If
        Next
        CR.FileName = Application.StartupPath + "\EtatMouvementPharmacie.rpt"

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo

        Try
            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent

            CR.SetParameterValue("Debut", dtpDebut.Text)
            CR.SetParameterValue("Fin", dtpFin.Text)
            CR.SetParameterValue("SoldeActuel", lSoldeActTTC.Text)

            Try
                FieldDef = CR.Database.Tables("Vue_MouvementPharmacie").Fields(VOrderBy)
                CR.DataDefinition.SortFields(0).Field = FieldDef
                If VAscDesc = "Asc" Then
                    CR.DataDefinition.SortFields(0).SortDirection = CrystalDecisions.Shared.SortDirection.AscendingOrder
                Else
                    CR.DataDefinition.SortFields(0).SortDirection = CrystalDecisions.Shared.SortDirection.DescendingOrder
                End If
            Catch ex As Exception
            End Try

            CR.RecordSelectionFormula = CondCrystal
            Dim MyViewer As New fViewer
            MyViewer.CRViewer.ReportSource = CR
            fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
            fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
            fMain.Tab.SelectedTab.Text = "Impression des mouvements de la pharmacie"
            If num <> 999 Then
                fMain.Tab.TabPages(num).Dispose()
            End If

        Catch ex As Exception
            MsgBox("Erreur dinitialisation du rapport : conexion à la base données échouée." + vbCrLf + ex.Message, MsgBoxStyle.Critical, "Erreur")
        End Try

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub cmbPharmacien_SelectedValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbPharmacien.SelectedValueChanged
        AfficherMouvement()
    End Sub

    Private Sub cmbPharmacien_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbPharmacien.Validated
        AfficherMouvement()
    End Sub

    Private Sub dtpDebut_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles dtpDebut.Validated
        AfficherMouvement()
    End Sub

    Private Sub dtpFin_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles dtpFin.Validated
        AfficherMouvement()
    End Sub

    Private Sub cmbPharmacien_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbPharmacien.TextChanged

    End Sub

    Private Sub cmbPharmacien_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbPharmacien.KeyUp

        'Recherche_Automatique_liste(e, cmbDesignation, cmbDesignation.Columns("Designation"))
        If e.KeyCode = Keys.Enter Then
            cmbPharmacien.Text = cmbPharmacien.WillChangeToText
            AfficherMouvement()
            dtpDebut.Focus()
        Else
            cmbPharmacien.OpenCombo()
        End If

    End Sub

    Private Sub dtpDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDebut.TextChanged

    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherMouvement()
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherMouvement()
            cmbPharmacien.Focus()
        End If
    End Sub

    Private Sub rdbNonVidees_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbNonVidees.CheckedChanged
        If rdbNonVidees.Checked = True Then
            dtpDebut.Enabled = False
            dtpFin.Enabled = False
        End If
        AfficherMouvement()
    End Sub

    Private Sub rdbPeriode_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbPeriode.CheckedChanged
        If rdbPeriode.Checked = True Then
            dtpDebut.Enabled = True
            dtpFin.Enabled = True
            dtpDebut.Focus()
        End If
        AfficherMouvement()
    End Sub

    Private Sub lSoldeActTVA_Click(sender As System.Object, e As System.EventArgs) Handles lSoldeActTVA.Click

    End Sub

    Private Sub lSoldeactHT_Click(sender As System.Object, e As System.EventArgs) Handles lSoldeactHT.Click

    End Sub

    Private Sub Label9_Click(sender As System.Object, e As System.EventArgs) Handles Label9.Click

    End Sub

    Private Sub Label7_Click(sender As System.Object, e As System.EventArgs) Handles Label7.Click

    End Sub

    Public Function Calcule_Solde(ByVal CodePharmacie)
        Dim StrSQLSolde As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Somme_Entree As Decimal = 0.0
        Dim Somme_Sortie As Decimal = 0.0
        Dim Solde_Initial As Decimal = 0.0
        Dim TotalReglement As Decimal = 0.0
        Dim difference As Decimal = 0.0


        'calcul du solde client en retranchat la somme des montant des règlements de la somme des montants des ventes 
        StrSQLSolde = "SELECT SUM(TotalTTC) FROM EMPRUNT WHERE Vider = 0 AND CodePharmacie =" + Quote(CodePharmacie)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQLSolde
        Try
            Somme_Entree = CmdCalcul.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        StrSQLSolde = "SELECT SUM(TotalTTC) FROM PRET WHERE Vider = 0 AND CodePharmacie =" + Quote(CodePharmacie)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQLSolde
        Try
            Somme_Sortie = CmdCalcul.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        StrSQLSolde = "SELECT SoldeInitial FROM PHARMACIE WHERE CodePharmacie =" + Quote(CodePharmacie)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQLSolde
        Try
            Solde_Initial = CmdCalcul.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        StrSQLSolde = "SELECT SUM(Montant) FROM REGLEMENT_PHARMACIE WHERE Vider = 0 AND CodePharmacie =" + Quote(CodePharmacie)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQLSolde
        Try
            TotalReglement = CmdCalcul.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        difference = Somme_Sortie - Somme_Entree + Solde_Initial - TotalReglement

        Return (Convert.ToSingle(difference))
    End Function

    Private Sub gDetails_AfterSort(sender As Object, e As FilterEventArgs) Handles gDetails.AfterSort
        Try
            VOrderBy = e.Condition.Substring(0, e.Condition.IndexOf(" "))
            VAscDesc = e.Condition.Substring(e.Condition.IndexOf(" ") + 1, e.Condition.Length - e.Condition.IndexOf(" ") - 1)
        Catch ex As Exception
            VOrderBy = e.Condition
            VAscDesc = "Asc"
        End Try
    End Sub
End Class