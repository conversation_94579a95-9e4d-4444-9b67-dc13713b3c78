﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fParametresGeneraux
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fParametresGeneraux))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.Label30 = New System.Windows.Forms.Label()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.bConfirmer = New C1.Win.C1Input.C1Button()
        Me.Tab = New C1.Win.C1Command.C1DockingTab()
        Me.C1DockingTabPage1 = New C1.Win.C1Command.C1DockingTabPage()
        Me.Label62 = New System.Windows.Forms.Label()
        Me.tMail = New C1.Win.C1Input.C1TextBox()
        Me.Label52 = New System.Windows.Forms.Label()
        Me.tTexte = New C1.Win.C1Input.C1TextBox()
        Me.GroupBox8 = New System.Windows.Forms.GroupBox()
        Me.Label45 = New System.Windows.Forms.Label()
        Me.Label44 = New System.Windows.Forms.Label()
        Me.tMessagederoulant1 = New C1.Win.C1Input.C1TextBox()
        Me.tMessagederoulant2 = New C1.Win.C1Input.C1TextBox()
        Me.dtpDateMigration = New C1.Win.C1Input.C1DateEdit()
        Me.Label21 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.tRib = New C1.Win.C1Input.C1TextBox()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.tTimbre = New C1.Win.C1Input.C1TextBox()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.tCodeTva = New C1.Win.C1Input.C1TextBox()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.tFax = New C1.Win.C1Input.C1TextBox()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.tTelephone = New C1.Win.C1Input.C1TextBox()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.tAdresse = New C1.Win.C1Input.C1TextBox()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.tNumeroAffiliation2 = New C1.Win.C1Input.C1TextBox()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.tNumeroAffiliation1 = New C1.Win.C1Input.C1TextBox()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.tCNAM = New C1.Win.C1Input.C1TextBox()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.tPharmacie = New C1.Win.C1Input.C1TextBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.tCodePharmacien = New C1.Win.C1Input.C1TextBox()
        Me.C1DockingTabPage2 = New C1.Win.C1Command.C1DockingTabPage()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.tMinimumdePerception = New C1.Win.C1Input.C1TextBox()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.tHonoraireTableauC = New C1.Win.C1Input.C1TextBox()
        Me.Label17 = New System.Windows.Forms.Label()
        Me.tHonoraireTableauB = New C1.Win.C1Input.C1TextBox()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.tHonoraireTableauA = New C1.Win.C1Input.C1TextBox()
        Me.C1DockingTabPage3 = New C1.Win.C1Command.C1DockingTabPage()
        Me.ChbImprimerUnEtiquette = New System.Windows.Forms.CheckBox()
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat = New System.Windows.Forms.CheckBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.tNomOrdinateurImpressionCodeABarre = New C1.Win.C1Input.C1TextBox()
        Me.C1DockingTabPage4 = New C1.Win.C1Command.C1DockingTabPage()
        Me.GroupBox11 = New System.Windows.Forms.GroupBox()
        Me.cbStockAlerte = New System.Windows.Forms.CheckBox()
        Me.Label68 = New System.Windows.Forms.Label()
        Me.Label26 = New System.Windows.Forms.Label()
        Me.tNePasSortirManquantsDepuis = New C1.Win.C1Input.C1TextBox()
        Me.rbManquantNbrCommande = New System.Windows.Forms.RadioButton()
        Me.rbManquantJour = New System.Windows.Forms.RadioButton()
        Me.Label67 = New System.Windows.Forms.Label()
        Me.tNbrCommande = New C1.Win.C1Input.C1TextBox()
        Me.Label66 = New System.Windows.Forms.Label()
        Me.chbMultipleDeCinq = New System.Windows.Forms.CheckBox()
        Me.lAnneeProchaine = New System.Windows.Forms.Label()
        Me.lAnneeCourant = New System.Windows.Forms.Label()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.dtpfinAnneeProchaine = New C1.Win.C1Input.C1DateEdit()
        Me.dtpDebutAnneeProchaine = New C1.Win.C1Input.C1DateEdit()
        Me.Label24 = New System.Windows.Forms.Label()
        Me.dtpFinAnneeCourant = New C1.Win.C1Input.C1DateEdit()
        Me.Label25 = New System.Windows.Forms.Label()
        Me.dtpDebutAnneeCourant = New C1.Win.C1Input.C1DateEdit()
        Me.Label22 = New System.Windows.Forms.Label()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.tCommandeGroupeJ = New C1.Win.C1Input.C1TextBox()
        Me.C1DockingTabPage5 = New C1.Win.C1Command.C1DockingTabPage()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.tOrdonnancier = New C1.Win.C1Input.C1TextBox()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.tBlDevis = New C1.Win.C1Input.C1TextBox()
        Me.Label19 = New System.Windows.Forms.Label()
        Me.tFacture = New C1.Win.C1Input.C1TextBox()
        Me.C1DockingTabPage6 = New C1.Win.C1Command.C1DockingTabPage()
        Me.ChbAutoriserSaisieNonMembeFamille = New System.Windows.Forms.CheckBox()
        Me.ChbAfficherReglementsSupprimes = New System.Windows.Forms.CheckBox()
        Me.chbMettreAJourPrixFrigo = New System.Windows.Forms.CheckBox()
        Me.Label65 = New System.Windows.Forms.Label()
        Me.tNbrAppareillage = New C1.Win.C1Input.C1TextBox()
        Me.Label64 = New System.Windows.Forms.Label()
        Me.tNbrPriseEnCharge = New C1.Win.C1Input.C1TextBox()
        Me.Label63 = New System.Windows.Forms.Label()
        Me.tNbrOrdonnance = New C1.Win.C1Input.C1TextBox()
        Me.chbGestionBon = New System.Windows.Forms.CheckBox()
        Me.chbActiverOMFAPCI = New System.Windows.Forms.CheckBox()
        Me.Label53 = New System.Windows.Forms.Label()
        Me.tRemise = New C1.Win.C1Input.C1TextBox()
        Me.chbAjouterMontantTimbreFacture = New System.Windows.Forms.CheckBox()
        Me.C1DockingTabPage7 = New C1.Win.C1Command.C1DockingTabPage()
        Me.gEnvoiLog = New System.Windows.Forms.GroupBox()
        Me.CheckedListBox1 = New System.Windows.Forms.CheckedListBox()
        Me.pbTestEnvoiFichierLog = New System.Windows.Forms.PictureBox()
        Me.Label31 = New System.Windows.Forms.Label()
        Me.bEnvoiLog = New C1.Win.C1Input.C1Button()
        Me.LTestEnvoi = New System.Windows.Forms.Label()
        Me.chbAutoriseEnvoiMail = New System.Windows.Forms.CheckBox()
        Me.gPramatresMail = New System.Windows.Forms.GroupBox()
        Me.pbTestEnvoiEmail = New System.Windows.Forms.PictureBox()
        Me.LTestMoTdePass = New System.Windows.Forms.Label()
        Me.LTestMail = New System.Windows.Forms.Label()
        Me.LTestPort = New System.Windows.Forms.Label()
        Me.LTestSMTPMail = New System.Windows.Forms.Label()
        Me.bTestEmail = New C1.Win.C1Input.C1Button()
        Me.LTestEnvoiEmail = New System.Windows.Forms.Label()
        Me.tSMTPMail = New C1.Win.C1Input.C1TextBox()
        Me.Label23 = New System.Windows.Forms.Label()
        Me.Label29 = New System.Windows.Forms.Label()
        Me.tPortMail = New C1.Win.C1Input.C1TextBox()
        Me.tMotDePasseMail = New C1.Win.C1Input.C1TextBox()
        Me.Label27 = New System.Windows.Forms.Label()
        Me.Label28 = New System.Windows.Forms.Label()
        Me.tAdresseMail = New C1.Win.C1Input.C1TextBox()
        Me.C1DockingTabPage8 = New C1.Win.C1Command.C1DockingTabPage()
        Me.GroupBox5 = New System.Windows.Forms.GroupBox()
        Me.Label32 = New System.Windows.Forms.Label()
        Me.tNbreJourValiditeParDefaut = New System.Windows.Forms.TextBox()
        Me.C1DockingTabPage9 = New C1.Win.C1Command.C1DockingTabPage()
        Me.GBProduitSurveiller = New System.Windows.Forms.GroupBox()
        Me.tCode = New C1.Win.C1Input.C1TextBox()
        Me.Label33 = New System.Windows.Forms.Label()
        Me.LNom = New System.Windows.Forms.Label()
        Me.Label39 = New System.Windows.Forms.Label()
        Me.cmbArticle = New C1.Win.C1List.C1Combo()
        Me.chbScannerOrdonnance = New System.Windows.Forms.CheckBox()
        Me.Label42 = New System.Windows.Forms.Label()
        Me.chbCapturerPhoto = New System.Windows.Forms.CheckBox()
        Me.Label38 = New System.Windows.Forms.Label()
        Me.chbEnvoyerNotification = New System.Windows.Forms.CheckBox()
        Me.LArticleValide = New System.Windows.Forms.Label()
        Me.Label34 = New System.Windows.Forms.Label()
        Me.bModifierP = New C1.Win.C1Input.C1Button()
        Me.bQuitterP = New C1.Win.C1Input.C1Button()
        Me.bAnnulerP = New C1.Win.C1Input.C1Button()
        Me.bConfirmerP = New C1.Win.C1Input.C1Button()
        Me.bAjouterP = New C1.Win.C1Input.C1Button()
        Me.bSupprimerP = New C1.Win.C1Input.C1Button()
        Me.gArticle = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.C1DockingTabPage10 = New C1.Win.C1Command.C1DockingTabPage()
        Me.tLatLong = New C1.Win.C1Input.C1TextBox()
        Me.Label35 = New System.Windows.Forms.Label()
        Me.C1DockingTabPage11 = New C1.Win.C1Command.C1DockingTabPage()
        Me.GroupBox6 = New System.Windows.Forms.GroupBox()
        Me.Label37 = New System.Windows.Forms.Label()
        Me.Label36 = New System.Windows.Forms.Label()
        Me.tTailleCodeCNAM = New System.Windows.Forms.TextBox()
        Me.C1DockingTabPage12 = New C1.Win.C1Command.C1DockingTabPage()
        Me.cmbMeme = New C1.Win.C1List.C1Combo()
        Me.Label54 = New System.Windows.Forms.Label()
        Me.GroupBox7 = New System.Windows.Forms.GroupBox()
        Me.Label43 = New System.Windows.Forms.Label()
        Me.cmbPolice = New System.Windows.Forms.ComboBox()
        Me.cmbCaractere = New System.Windows.Forms.ComboBox()
        Me.cmbListe = New System.Windows.Forms.ComboBox()
        Me.Label41 = New System.Windows.Forms.Label()
        Me.Label40 = New System.Windows.Forms.Label()
        Me.C1DockingTabPage13 = New C1.Win.C1Command.C1DockingTabPage()
        Me.GroupBox10 = New System.Windows.Forms.GroupBox()
        Me.chReseau = New System.Windows.Forms.RadioButton()
        Me.chFichierTexte = New System.Windows.Forms.RadioButton()
        Me.chPortSerie = New System.Windows.Forms.RadioButton()
        Me.GroupBoxTerminal = New System.Windows.Forms.GroupBox()
        Me.Label46 = New System.Windows.Forms.Label()
        Me.cmbProtocole = New C1.Win.C1List.C1Combo()
        Me.Label47 = New System.Windows.Forms.Label()
        Me.tNomPort = New C1.Win.C1Input.C1TextBox()
        Me.cmbDataBit = New C1.Win.C1List.C1Combo()
        Me.cmbParity = New C1.Win.C1List.C1Combo()
        Me.Label48 = New System.Windows.Forms.Label()
        Me.cmbStopBit = New C1.Win.C1List.C1Combo()
        Me.Label49 = New System.Windows.Forms.Label()
        Me.cmbVitesse = New C1.Win.C1List.C1Combo()
        Me.Label50 = New System.Windows.Forms.Label()
        Me.Label51 = New System.Windows.Forms.Label()
        Me.C1DockingTabPage14 = New C1.Win.C1Command.C1DockingTabPage()
        Me.cmbFormatExcel = New C1.Win.C1List.C1Combo()
        Me.Label55 = New System.Windows.Forms.Label()
        Me.Label56 = New System.Windows.Forms.Label()
        Me.tDossierExcel = New C1.Win.C1Input.C1TextBox()
        Me.C1DockingTabPage16 = New C1.Win.C1Command.C1DockingTabPage()
        Me.GroupBox9 = New System.Windows.Forms.GroupBox()
        Me.Label61 = New System.Windows.Forms.Label()
        Me.Label60 = New System.Windows.Forms.Label()
        Me.Label59 = New System.Windows.Forms.Label()
        Me.Label58 = New System.Windows.Forms.Label()
        Me.Label57 = New System.Windows.Forms.Label()
        Me.ChbModuleArticle5 = New System.Windows.Forms.CheckBox()
        Me.ChbModuleClient1 = New System.Windows.Forms.CheckBox()
        Me.ChbModuleClient2 = New System.Windows.Forms.CheckBox()
        Me.ChbModuleClient3 = New System.Windows.Forms.CheckBox()
        Me.ChbModuleArticle4 = New System.Windows.Forms.CheckBox()
        Me.ChbModuleArticle1 = New System.Windows.Forms.CheckBox()
        Me.ChbModuleArticle2 = New System.Windows.Forms.CheckBox()
        Me.ChbModuleAchat2 = New System.Windows.Forms.CheckBox()
        Me.ChbModuleReglement1 = New System.Windows.Forms.CheckBox()
        Me.ChbModuleVente3 = New System.Windows.Forms.CheckBox()
        Me.ChbModuleVente1 = New System.Windows.Forms.CheckBox()
        Me.ChbModuleVente2 = New System.Windows.Forms.CheckBox()
        Me.ChbModuleVente4 = New System.Windows.Forms.CheckBox()
        Me.ChbModuleAchat1 = New System.Windows.Forms.CheckBox()
        Me.ChbModuleArticle3 = New System.Windows.Forms.CheckBox()
        Me.C1DockingTabPage15 = New C1.Win.C1Command.C1DockingTabPage()
        Me.label = New System.Windows.Forms.Label()
        Me.Label70 = New System.Windows.Forms.Label()
        Me.Label71 = New System.Windows.Forms.Label()
        Me.Password = New C1.Win.C1Input.C1TextBox()
        Me.hostname = New C1.Win.C1Input.C1TextBox()
        Me.UserName = New C1.Win.C1Input.C1TextBox()
        Me.FTP = New System.Windows.Forms.GroupBox()
        Me.Panel.SuspendLayout()
        CType(Me.Tab, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Tab.SuspendLayout()
        Me.C1DockingTabPage1.SuspendLayout()
        CType(Me.tMail, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tTexte, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox8.SuspendLayout()
        CType(Me.tMessagederoulant1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tMessagederoulant2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpDateMigration, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tRib, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tTimbre, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodeTva, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tFax, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tTelephone, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tAdresse, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNumeroAffiliation2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNumeroAffiliation1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCNAM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tPharmacie, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodePharmacien, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage2.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.tMinimumdePerception, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.tHonoraireTableauC, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tHonoraireTableauB, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tHonoraireTableauA, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage3.SuspendLayout()
        CType(Me.tNomOrdinateurImpressionCodeABarre, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage4.SuspendLayout()
        Me.GroupBox11.SuspendLayout()
        CType(Me.tNePasSortirManquantsDepuis, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNbrCommande, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox4.SuspendLayout()
        CType(Me.dtpfinAnneeProchaine, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpDebutAnneeProchaine, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpFinAnneeCourant, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpDebutAnneeCourant, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCommandeGroupeJ, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage5.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        CType(Me.tOrdonnancier, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tBlDevis, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tFacture, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage6.SuspendLayout()
        CType(Me.tNbrAppareillage, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNbrPriseEnCharge, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNbrOrdonnance, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tRemise, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage7.SuspendLayout()
        Me.gEnvoiLog.SuspendLayout()
        CType(Me.pbTestEnvoiFichierLog, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.gPramatresMail.SuspendLayout()
        CType(Me.pbTestEnvoiEmail, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tSMTPMail, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tPortMail, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tMotDePasseMail, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tAdresseMail, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage8.SuspendLayout()
        Me.GroupBox5.SuspendLayout()
        Me.C1DockingTabPage9.SuspendLayout()
        Me.GBProduitSurveiller.SuspendLayout()
        CType(Me.tCode, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbArticle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gArticle, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage10.SuspendLayout()
        CType(Me.tLatLong, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage11.SuspendLayout()
        Me.GroupBox6.SuspendLayout()
        Me.C1DockingTabPage12.SuspendLayout()
        CType(Me.cmbMeme, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox7.SuspendLayout()
        Me.C1DockingTabPage13.SuspendLayout()
        Me.GroupBox10.SuspendLayout()
        Me.GroupBoxTerminal.SuspendLayout()
        CType(Me.cmbProtocole, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNomPort, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbDataBit, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbParity, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbStopBit, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbVitesse, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage14.SuspendLayout()
        CType(Me.cmbFormatExcel, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDossierExcel, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage16.SuspendLayout()
        Me.GroupBox9.SuspendLayout()
        Me.C1DockingTabPage15.SuspendLayout()
        CType(Me.Password, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.hostname, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.UserName, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.FTP.SuspendLayout()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.Label30)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.bConfirmer)
        Me.Panel.Controls.Add(Me.Tab)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1186, 489)
        Me.Panel.TabIndex = 17
        '
        'Label30
        '
        Me.Label30.Font = New System.Drawing.Font("Microsoft Sans Serif", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label30.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label30.Location = New System.Drawing.Point(11, 12)
        Me.Label30.Name = "Label30"
        Me.Label30.Size = New System.Drawing.Size(1051, 56)
        Me.Label30.TabIndex = 87
        Me.Label30.Text = "Paramètres généraux"
        Me.Label30.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bAnnuler
        '
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler21
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(1068, 133)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(103, 45)
        Me.bAnnuler.TabIndex = 3
        Me.bAnnuler.Text = "Annuler"
        Me.bAnnuler.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bConfirmer
        '
        Me.bConfirmer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmer.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bConfirmer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmer.Location = New System.Drawing.Point(1069, 81)
        Me.bConfirmer.Name = "bConfirmer"
        Me.bConfirmer.Size = New System.Drawing.Size(103, 45)
        Me.bConfirmer.TabIndex = 2
        Me.bConfirmer.Text = "Confirmer"
        Me.bConfirmer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bConfirmer.UseVisualStyleBackColor = True
        Me.bConfirmer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Tab
        '
        Me.Tab.Controls.Add(Me.C1DockingTabPage1)
        Me.Tab.Controls.Add(Me.C1DockingTabPage2)
        Me.Tab.Controls.Add(Me.C1DockingTabPage3)
        Me.Tab.Controls.Add(Me.C1DockingTabPage4)
        Me.Tab.Controls.Add(Me.C1DockingTabPage5)
        Me.Tab.Controls.Add(Me.C1DockingTabPage6)
        Me.Tab.Controls.Add(Me.C1DockingTabPage7)
        Me.Tab.Controls.Add(Me.C1DockingTabPage8)
        Me.Tab.Controls.Add(Me.C1DockingTabPage9)
        Me.Tab.Controls.Add(Me.C1DockingTabPage10)
        Me.Tab.Controls.Add(Me.C1DockingTabPage11)
        Me.Tab.Controls.Add(Me.C1DockingTabPage12)
        Me.Tab.Controls.Add(Me.C1DockingTabPage13)
        Me.Tab.Controls.Add(Me.C1DockingTabPage14)
        Me.Tab.Controls.Add(Me.C1DockingTabPage16)
        Me.Tab.Controls.Add(Me.C1DockingTabPage15)
        Me.Tab.Location = New System.Drawing.Point(9, 81)
        Me.Tab.Name = "Tab"
        Me.Tab.SelectedIndex = 15
        Me.Tab.Size = New System.Drawing.Size(1054, 395)
        Me.Tab.TabIndex = 0
        Me.Tab.TabsSpacing = 5
        Me.Tab.TabStyle = C1.Win.C1Command.TabStyleEnum.Office2007
        Me.Tab.VisualStyle = C1.Win.C1Command.VisualStyle.Office2007Blue
        Me.Tab.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage1
        '
        Me.C1DockingTabPage1.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.C1DockingTabPage1.Controls.Add(Me.Label62)
        Me.C1DockingTabPage1.Controls.Add(Me.tMail)
        Me.C1DockingTabPage1.Controls.Add(Me.Label52)
        Me.C1DockingTabPage1.Controls.Add(Me.tTexte)
        Me.C1DockingTabPage1.Controls.Add(Me.GroupBox8)
        Me.C1DockingTabPage1.Controls.Add(Me.dtpDateMigration)
        Me.C1DockingTabPage1.Controls.Add(Me.Label21)
        Me.C1DockingTabPage1.Controls.Add(Me.Label2)
        Me.C1DockingTabPage1.Controls.Add(Me.tRib)
        Me.C1DockingTabPage1.Controls.Add(Me.Label13)
        Me.C1DockingTabPage1.Controls.Add(Me.tTimbre)
        Me.C1DockingTabPage1.Controls.Add(Me.Label14)
        Me.C1DockingTabPage1.Controls.Add(Me.Label15)
        Me.C1DockingTabPage1.Controls.Add(Me.tCodeTva)
        Me.C1DockingTabPage1.Controls.Add(Me.Label10)
        Me.C1DockingTabPage1.Controls.Add(Me.tFax)
        Me.C1DockingTabPage1.Controls.Add(Me.Label11)
        Me.C1DockingTabPage1.Controls.Add(Me.tTelephone)
        Me.C1DockingTabPage1.Controls.Add(Me.Label12)
        Me.C1DockingTabPage1.Controls.Add(Me.tAdresse)
        Me.C1DockingTabPage1.Controls.Add(Me.Label9)
        Me.C1DockingTabPage1.Controls.Add(Me.tNumeroAffiliation2)
        Me.C1DockingTabPage1.Controls.Add(Me.Label8)
        Me.C1DockingTabPage1.Controls.Add(Me.tNumeroAffiliation1)
        Me.C1DockingTabPage1.Controls.Add(Me.Label7)
        Me.C1DockingTabPage1.Controls.Add(Me.tCNAM)
        Me.C1DockingTabPage1.Controls.Add(Me.Label6)
        Me.C1DockingTabPage1.Controls.Add(Me.tPharmacie)
        Me.C1DockingTabPage1.Controls.Add(Me.Label4)
        Me.C1DockingTabPage1.Controls.Add(Me.tCodePharmacien)
        Me.C1DockingTabPage1.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage1.Name = "C1DockingTabPage1"
        Me.C1DockingTabPage1.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage1.TabIndex = 0
        Me.C1DockingTabPage1.Text = "Entête "
        '
        'Label62
        '
        Me.Label62.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label62.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label62.Location = New System.Drawing.Point(608, 37)
        Me.Label62.Name = "Label62"
        Me.Label62.Size = New System.Drawing.Size(98, 13)
        Me.Label62.TabIndex = 94
        Me.Label62.Text = "Mail :"
        Me.Label62.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tMail
        '
        Me.tMail.AutoSize = False
        Me.tMail.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMail.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tMail.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tMail.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tMail.Location = New System.Drawing.Point(712, 35)
        Me.tMail.Name = "tMail"
        Me.tMail.Size = New System.Drawing.Size(303, 19)
        Me.tMail.TabIndex = 93
        Me.tMail.Tag = Nothing
        Me.tMail.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label52
        '
        Me.Label52.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label52.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label52.Location = New System.Drawing.Point(14, 173)
        Me.Label52.Name = "Label52"
        Me.Label52.Size = New System.Drawing.Size(98, 13)
        Me.Label52.TabIndex = 92
        Me.Label52.Text = "Texte :"
        Me.Label52.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tTexte
        '
        Me.tTexte.AutoSize = False
        Me.tTexte.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTexte.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tTexte.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tTexte.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tTexte.Location = New System.Drawing.Point(118, 171)
        Me.tTexte.Name = "tTexte"
        Me.tTexte.Size = New System.Drawing.Size(408, 19)
        Me.tTexte.TabIndex = 91
        Me.tTexte.Tag = Nothing
        Me.tTexte.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox8
        '
        Me.GroupBox8.Controls.Add(Me.Label45)
        Me.GroupBox8.Controls.Add(Me.Label44)
        Me.GroupBox8.Controls.Add(Me.tMessagederoulant1)
        Me.GroupBox8.Controls.Add(Me.tMessagederoulant2)
        Me.GroupBox8.Location = New System.Drawing.Point(118, 196)
        Me.GroupBox8.Name = "GroupBox8"
        Me.GroupBox8.Size = New System.Drawing.Size(408, 72)
        Me.GroupBox8.TabIndex = 90
        Me.GroupBox8.TabStop = False
        '
        'Label45
        '
        Me.Label45.AutoSize = True
        Me.Label45.Location = New System.Drawing.Point(19, 17)
        Me.Label45.Name = "Label45"
        Me.Label45.Size = New System.Drawing.Size(60, 13)
        Me.Label45.TabIndex = 82
        Me.Label45.Text = "1 ère Ligne"
        '
        'Label44
        '
        Me.Label44.AutoSize = True
        Me.Label44.Location = New System.Drawing.Point(14, 45)
        Me.Label44.Name = "Label44"
        Me.Label44.Size = New System.Drawing.Size(65, 13)
        Me.Label44.TabIndex = 82
        Me.Label44.Text = "2 ème Ligne"
        '
        'tMessagederoulant1
        '
        Me.tMessagederoulant1.AutoSize = False
        Me.tMessagederoulant1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMessagederoulant1.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tMessagederoulant1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tMessagederoulant1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tMessagederoulant1.Location = New System.Drawing.Point(85, 15)
        Me.tMessagederoulant1.MaxLength = 19
        Me.tMessagederoulant1.Name = "tMessagederoulant1"
        Me.tMessagederoulant1.Size = New System.Drawing.Size(299, 19)
        Me.tMessagederoulant1.TabIndex = 81
        Me.tMessagederoulant1.Tag = Nothing
        Me.tMessagederoulant1.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tMessagederoulant2
        '
        Me.tMessagederoulant2.AutoSize = False
        Me.tMessagederoulant2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMessagederoulant2.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tMessagederoulant2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tMessagederoulant2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tMessagederoulant2.Location = New System.Drawing.Point(85, 43)
        Me.tMessagederoulant2.MaxLength = 19
        Me.tMessagederoulant2.Name = "tMessagederoulant2"
        Me.tMessagederoulant2.Size = New System.Drawing.Size(299, 19)
        Me.tMessagederoulant2.TabIndex = 81
        Me.tMessagederoulant2.Tag = Nothing
        Me.tMessagederoulant2.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'dtpDateMigration
        '
        Me.dtpDateMigration.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDateMigration.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDateMigration.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDateMigration.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDateMigration.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDateMigration.EmptyAsNull = True
        Me.dtpDateMigration.Location = New System.Drawing.Point(118, 334)
        Me.dtpDateMigration.Name = "dtpDateMigration"
        Me.dtpDateMigration.Size = New System.Drawing.Size(182, 18)
        Me.dtpDateMigration.TabIndex = 89
        Me.dtpDateMigration.Tag = Nothing
        Me.dtpDateMigration.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDateMigration.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label21
        '
        Me.Label21.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label21.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label21.Location = New System.Drawing.Point(14, 334)
        Me.Label21.Name = "Label21"
        Me.Label21.Size = New System.Drawing.Size(98, 13)
        Me.Label21.TabIndex = 88
        Me.Label21.Text = "Date de migration :"
        Me.Label21.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label2
        '
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label2.Location = New System.Drawing.Point(14, 276)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(98, 13)
        Me.Label2.TabIndex = 86
        Me.Label2.Text = "Rib :"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tRib
        '
        Me.tRib.AutoSize = False
        Me.tRib.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tRib.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tRib.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tRib.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tRib.Location = New System.Drawing.Point(118, 274)
        Me.tRib.Name = "tRib"
        Me.tRib.Size = New System.Drawing.Size(260, 19)
        Me.tRib.TabIndex = 85
        Me.tRib.Tag = Nothing
        Me.tRib.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label13
        '
        Me.Label13.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label13.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label13.Location = New System.Drawing.Point(14, 306)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(98, 13)
        Me.Label13.TabIndex = 84
        Me.Label13.Text = "Timbre :"
        Me.Label13.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tTimbre
        '
        Me.tTimbre.AutoSize = False
        Me.tTimbre.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTimbre.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tTimbre.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tTimbre.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tTimbre.Location = New System.Drawing.Point(118, 304)
        Me.tTimbre.Name = "tTimbre"
        Me.tTimbre.Size = New System.Drawing.Size(104, 19)
        Me.tTimbre.TabIndex = 83
        Me.tTimbre.Tag = Nothing
        Me.tTimbre.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label14
        '
        Me.Label14.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label14.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label14.Location = New System.Drawing.Point(7, 201)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(105, 15)
        Me.Label14.TabIndex = 82
        Me.Label14.Text = "Message déroulant :"
        Me.Label14.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label15
        '
        Me.Label15.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label15.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label15.Location = New System.Drawing.Point(14, 144)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(98, 13)
        Me.Label15.TabIndex = 80
        Me.Label15.Text = "CodeTVA :"
        Me.Label15.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tCodeTva
        '
        Me.tCodeTva.AutoSize = False
        Me.tCodeTva.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodeTva.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tCodeTva.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tCodeTva.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tCodeTva.Location = New System.Drawing.Point(118, 142)
        Me.tCodeTva.Name = "tCodeTva"
        Me.tCodeTva.Size = New System.Drawing.Size(104, 19)
        Me.tCodeTva.TabIndex = 79
        Me.tCodeTva.Tag = Nothing
        Me.tCodeTva.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label10.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label10.Location = New System.Drawing.Point(322, 116)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(30, 13)
        Me.Label10.TabIndex = 78
        Me.Label10.Text = "Fax :"
        Me.Label10.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tFax
        '
        Me.tFax.AutoSize = False
        Me.tFax.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tFax.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tFax.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tFax.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tFax.Location = New System.Drawing.Point(356, 114)
        Me.tFax.Name = "tFax"
        Me.tFax.Size = New System.Drawing.Size(104, 19)
        Me.tFax.TabIndex = 77
        Me.tFax.Tag = Nothing
        Me.tFax.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label11
        '
        Me.Label11.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label11.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label11.Location = New System.Drawing.Point(14, 116)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(98, 13)
        Me.Label11.TabIndex = 76
        Me.Label11.Text = "Tél :"
        Me.Label11.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tTelephone
        '
        Me.tTelephone.AutoSize = False
        Me.tTelephone.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTelephone.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tTelephone.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tTelephone.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tTelephone.Location = New System.Drawing.Point(118, 114)
        Me.tTelephone.Name = "tTelephone"
        Me.tTelephone.Size = New System.Drawing.Size(198, 19)
        Me.tTelephone.TabIndex = 75
        Me.tTelephone.Tag = Nothing
        Me.tTelephone.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label12
        '
        Me.Label12.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label12.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label12.Location = New System.Drawing.Point(14, 89)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(98, 13)
        Me.Label12.TabIndex = 74
        Me.Label12.Text = "Adresse :"
        Me.Label12.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tAdresse
        '
        Me.tAdresse.AutoSize = False
        Me.tAdresse.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tAdresse.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tAdresse.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tAdresse.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tAdresse.Location = New System.Drawing.Point(118, 87)
        Me.tAdresse.Name = "tAdresse"
        Me.tAdresse.Size = New System.Drawing.Size(303, 19)
        Me.tAdresse.TabIndex = 73
        Me.tAdresse.Tag = Nothing
        Me.tAdresse.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label9
        '
        Me.Label9.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label9.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label9.Location = New System.Drawing.Point(466, 64)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(17, 13)
        Me.Label9.TabIndex = 72
        Me.Label9.Text = "/"
        Me.Label9.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tNumeroAffiliation2
        '
        Me.tNumeroAffiliation2.AutoSize = False
        Me.tNumeroAffiliation2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNumeroAffiliation2.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNumeroAffiliation2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNumeroAffiliation2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tNumeroAffiliation2.Location = New System.Drawing.Point(485, 61)
        Me.tNumeroAffiliation2.Name = "tNumeroAffiliation2"
        Me.tNumeroAffiliation2.Size = New System.Drawing.Size(41, 19)
        Me.tNumeroAffiliation2.TabIndex = 71
        Me.tNumeroAffiliation2.Tag = Nothing
        Me.tNumeroAffiliation2.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label8
        '
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label8.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label8.Location = New System.Drawing.Point(243, 63)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(110, 13)
        Me.Label8.TabIndex = 70
        Me.Label8.Text = "N° d'affiliation social :"
        Me.Label8.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tNumeroAffiliation1
        '
        Me.tNumeroAffiliation1.AutoSize = False
        Me.tNumeroAffiliation1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNumeroAffiliation1.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNumeroAffiliation1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNumeroAffiliation1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tNumeroAffiliation1.Location = New System.Drawing.Point(356, 61)
        Me.tNumeroAffiliation1.Name = "tNumeroAffiliation1"
        Me.tNumeroAffiliation1.Size = New System.Drawing.Size(104, 19)
        Me.tNumeroAffiliation1.TabIndex = 69
        Me.tNumeroAffiliation1.Tag = Nothing
        Me.tNumeroAffiliation1.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label7
        '
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label7.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label7.Location = New System.Drawing.Point(14, 63)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(98, 13)
        Me.Label7.TabIndex = 68
        Me.Label7.Text = "N° CNAM :"
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tCNAM
        '
        Me.tCNAM.AutoSize = False
        Me.tCNAM.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCNAM.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tCNAM.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tCNAM.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tCNAM.Location = New System.Drawing.Point(118, 61)
        Me.tCNAM.Name = "tCNAM"
        Me.tCNAM.Size = New System.Drawing.Size(104, 19)
        Me.tCNAM.TabIndex = 67
        Me.tCNAM.Tag = Nothing
        Me.tCNAM.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label6
        '
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label6.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label6.Location = New System.Drawing.Point(14, 37)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(98, 13)
        Me.Label6.TabIndex = 66
        Me.Label6.Text = "Pharmacie :"
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tPharmacie
        '
        Me.tPharmacie.AutoSize = False
        Me.tPharmacie.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tPharmacie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tPharmacie.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tPharmacie.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tPharmacie.Location = New System.Drawing.Point(118, 35)
        Me.tPharmacie.Name = "tPharmacie"
        Me.tPharmacie.Size = New System.Drawing.Size(303, 19)
        Me.tPharmacie.TabIndex = 65
        Me.tPharmacie.Tag = Nothing
        Me.tPharmacie.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label4
        '
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label4.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label4.Location = New System.Drawing.Point(14, 12)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(98, 13)
        Me.Label4.TabIndex = 62
        Me.Label4.Text = "Code Pharmacien :"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tCodePharmacien
        '
        Me.tCodePharmacien.AutoSize = False
        Me.tCodePharmacien.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodePharmacien.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tCodePharmacien.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tCodePharmacien.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tCodePharmacien.Location = New System.Drawing.Point(118, 10)
        Me.tCodePharmacien.Name = "tCodePharmacien"
        Me.tCodePharmacien.Size = New System.Drawing.Size(50, 19)
        Me.tCodePharmacien.TabIndex = 61
        Me.tCodePharmacien.Tag = Nothing
        Me.tCodePharmacien.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage2
        '
        Me.C1DockingTabPage2.Controls.Add(Me.GroupBox2)
        Me.C1DockingTabPage2.Controls.Add(Me.GroupBox1)
        Me.C1DockingTabPage2.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage2.Name = "C1DockingTabPage2"
        Me.C1DockingTabPage2.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage2.TabIndex = 1
        Me.C1DockingTabPage2.Text = "Article"
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.tMinimumdePerception)
        Me.GroupBox2.Location = New System.Drawing.Point(8, 175)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(578, 74)
        Me.GroupBox2.TabIndex = 69
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "Minimum de Perception"
        '
        'tMinimumdePerception
        '
        Me.tMinimumdePerception.AutoSize = False
        Me.tMinimumdePerception.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMinimumdePerception.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tMinimumdePerception.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tMinimumdePerception.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tMinimumdePerception.Location = New System.Drawing.Point(80, 31)
        Me.tMinimumdePerception.Name = "tMinimumdePerception"
        Me.tMinimumdePerception.Size = New System.Drawing.Size(119, 19)
        Me.tMinimumdePerception.TabIndex = 63
        Me.tMinimumdePerception.Tag = Nothing
        Me.tMinimumdePerception.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.Label18)
        Me.GroupBox1.Controls.Add(Me.tHonoraireTableauC)
        Me.GroupBox1.Controls.Add(Me.Label17)
        Me.GroupBox1.Controls.Add(Me.tHonoraireTableauB)
        Me.GroupBox1.Controls.Add(Me.Label16)
        Me.GroupBox1.Controls.Add(Me.tHonoraireTableauA)
        Me.GroupBox1.Location = New System.Drawing.Point(8, 28)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(578, 128)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Honoraire de responsabilité"
        '
        'Label18
        '
        Me.Label18.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label18.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label18.Location = New System.Drawing.Point(16, 89)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(98, 13)
        Me.Label18.TabIndex = 68
        Me.Label18.Text = "Tableau C :"
        Me.Label18.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tHonoraireTableauC
        '
        Me.tHonoraireTableauC.AutoSize = False
        Me.tHonoraireTableauC.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tHonoraireTableauC.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tHonoraireTableauC.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tHonoraireTableauC.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tHonoraireTableauC.Location = New System.Drawing.Point(120, 87)
        Me.tHonoraireTableauC.Name = "tHonoraireTableauC"
        Me.tHonoraireTableauC.Size = New System.Drawing.Size(119, 19)
        Me.tHonoraireTableauC.TabIndex = 67
        Me.tHonoraireTableauC.Tag = Nothing
        Me.tHonoraireTableauC.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label17
        '
        Me.Label17.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label17.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label17.Location = New System.Drawing.Point(16, 60)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(98, 13)
        Me.Label17.TabIndex = 66
        Me.Label17.Text = "Tableau B :"
        Me.Label17.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tHonoraireTableauB
        '
        Me.tHonoraireTableauB.AutoSize = False
        Me.tHonoraireTableauB.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tHonoraireTableauB.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tHonoraireTableauB.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tHonoraireTableauB.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tHonoraireTableauB.Location = New System.Drawing.Point(120, 58)
        Me.tHonoraireTableauB.Name = "tHonoraireTableauB"
        Me.tHonoraireTableauB.Size = New System.Drawing.Size(119, 19)
        Me.tHonoraireTableauB.TabIndex = 65
        Me.tHonoraireTableauB.Tag = Nothing
        Me.tHonoraireTableauB.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label16
        '
        Me.Label16.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label16.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label16.Location = New System.Drawing.Point(16, 31)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(98, 13)
        Me.Label16.TabIndex = 64
        Me.Label16.Text = "Tableau A :"
        Me.Label16.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tHonoraireTableauA
        '
        Me.tHonoraireTableauA.AutoSize = False
        Me.tHonoraireTableauA.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tHonoraireTableauA.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tHonoraireTableauA.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tHonoraireTableauA.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tHonoraireTableauA.Location = New System.Drawing.Point(120, 29)
        Me.tHonoraireTableauA.Name = "tHonoraireTableauA"
        Me.tHonoraireTableauA.Size = New System.Drawing.Size(119, 19)
        Me.tHonoraireTableauA.TabIndex = 63
        Me.tHonoraireTableauA.Tag = Nothing
        Me.tHonoraireTableauA.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage3
        '
        Me.C1DockingTabPage3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.C1DockingTabPage3.Controls.Add(Me.ChbImprimerUnEtiquette)
        Me.C1DockingTabPage3.Controls.Add(Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat)
        Me.C1DockingTabPage3.Controls.Add(Me.Label1)
        Me.C1DockingTabPage3.Controls.Add(Me.tNomOrdinateurImpressionCodeABarre)
        Me.C1DockingTabPage3.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage3.Name = "C1DockingTabPage3"
        Me.C1DockingTabPage3.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage3.TabIndex = 2
        Me.C1DockingTabPage3.Text = "Achat"
        '
        'ChbImprimerUnEtiquette
        '
        Me.ChbImprimerUnEtiquette.BackColor = System.Drawing.Color.Transparent
        Me.ChbImprimerUnEtiquette.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbImprimerUnEtiquette.Location = New System.Drawing.Point(19, 80)
        Me.ChbImprimerUnEtiquette.Name = "ChbImprimerUnEtiquette"
        Me.ChbImprimerUnEtiquette.Size = New System.Drawing.Size(406, 19)
        Me.ChbImprimerUnEtiquette.TabIndex = 78
        Me.ChbImprimerUnEtiquette.Text = "Imprimer une seule étiquette"
        Me.ChbImprimerUnEtiquette.UseVisualStyleBackColor = False
        '
        'chbAfficherLesDerniereDDPeremptionDansNouveauAchat
        '
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.BackColor = System.Drawing.Color.Transparent
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.Location = New System.Drawing.Point(19, 115)
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.Name = "chbAfficherLesDerniereDDPeremptionDansNouveauAchat"
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.Size = New System.Drawing.Size(406, 19)
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.TabIndex = 77
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.Text = "Afficher les dernières dates de péremption dans les nouveaux achats "
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.UseVisualStyleBackColor = False
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label1.ForeColor = System.Drawing.Color.Red
        Me.Label1.Location = New System.Drawing.Point(16, 57)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(237, 13)
        Me.Label1.TabIndex = 76
        Me.Label1.Text = "Nom de l'ordinateur d'impression code à barre :"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tNomOrdinateurImpressionCodeABarre
        '
        Me.tNomOrdinateurImpressionCodeABarre.AutoSize = False
        Me.tNomOrdinateurImpressionCodeABarre.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNomOrdinateurImpressionCodeABarre.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNomOrdinateurImpressionCodeABarre.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNomOrdinateurImpressionCodeABarre.ForeColor = System.Drawing.Color.Red
        Me.tNomOrdinateurImpressionCodeABarre.Location = New System.Drawing.Point(259, 55)
        Me.tNomOrdinateurImpressionCodeABarre.Name = "tNomOrdinateurImpressionCodeABarre"
        Me.tNomOrdinateurImpressionCodeABarre.Size = New System.Drawing.Size(119, 19)
        Me.tNomOrdinateurImpressionCodeABarre.TabIndex = 75
        Me.tNomOrdinateurImpressionCodeABarre.Tag = Nothing
        Me.tNomOrdinateurImpressionCodeABarre.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage4
        '
        Me.C1DockingTabPage4.Controls.Add(Me.GroupBox11)
        Me.C1DockingTabPage4.Controls.Add(Me.rbManquantNbrCommande)
        Me.C1DockingTabPage4.Controls.Add(Me.rbManquantJour)
        Me.C1DockingTabPage4.Controls.Add(Me.Label67)
        Me.C1DockingTabPage4.Controls.Add(Me.tNbrCommande)
        Me.C1DockingTabPage4.Controls.Add(Me.Label66)
        Me.C1DockingTabPage4.Controls.Add(Me.chbMultipleDeCinq)
        Me.C1DockingTabPage4.Controls.Add(Me.lAnneeProchaine)
        Me.C1DockingTabPage4.Controls.Add(Me.lAnneeCourant)
        Me.C1DockingTabPage4.Controls.Add(Me.GroupBox4)
        Me.C1DockingTabPage4.Controls.Add(Me.Label22)
        Me.C1DockingTabPage4.Controls.Add(Me.Label20)
        Me.C1DockingTabPage4.Controls.Add(Me.tCommandeGroupeJ)
        Me.C1DockingTabPage4.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage4.Name = "C1DockingTabPage4"
        Me.C1DockingTabPage4.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage4.TabIndex = 3
        Me.C1DockingTabPage4.Text = "Commande"
        '
        'GroupBox11
        '
        Me.GroupBox11.Controls.Add(Me.cbStockAlerte)
        Me.GroupBox11.Controls.Add(Me.Label68)
        Me.GroupBox11.Controls.Add(Me.Label26)
        Me.GroupBox11.Controls.Add(Me.tNePasSortirManquantsDepuis)
        Me.GroupBox11.Location = New System.Drawing.Point(596, 28)
        Me.GroupBox11.Name = "GroupBox11"
        Me.GroupBox11.Size = New System.Drawing.Size(370, 133)
        Me.GroupBox11.TabIndex = 86
        Me.GroupBox11.TabStop = False
        Me.GroupBox11.Text = "Commande Journalière"
        '
        'cbStockAlerte
        '
        Me.cbStockAlerte.AutoSize = True
        Me.cbStockAlerte.Location = New System.Drawing.Point(41, 78)
        Me.cbStockAlerte.Name = "cbStockAlerte"
        Me.cbStockAlerte.Size = New System.Drawing.Size(173, 17)
        Me.cbStockAlerte.TabIndex = 79
        Me.cbStockAlerte.Text = "Tenir en compte Stock D'alerte"
        Me.cbStockAlerte.UseVisualStyleBackColor = True
        '
        'Label68
        '
        Me.Label68.AutoSize = True
        Me.Label68.Location = New System.Drawing.Point(27, 44)
        Me.Label68.Name = "Label68"
        Me.Label68.Size = New System.Drawing.Size(120, 13)
        Me.Label68.TabIndex = 78
        Me.Label68.Text = "Classer manquant aprés"
        '
        'Label26
        '
        Me.Label26.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label26.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label26.Location = New System.Drawing.Point(200, 44)
        Me.Label26.Name = "Label26"
        Me.Label26.Size = New System.Drawing.Size(61, 13)
        Me.Label26.TabIndex = 77
        Me.Label26.Text = "Jours"
        Me.Label26.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tNePasSortirManquantsDepuis
        '
        Me.tNePasSortirManquantsDepuis.AutoSize = False
        Me.tNePasSortirManquantsDepuis.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNePasSortirManquantsDepuis.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNePasSortirManquantsDepuis.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNePasSortirManquantsDepuis.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tNePasSortirManquantsDepuis.Location = New System.Drawing.Point(158, 40)
        Me.tNePasSortirManquantsDepuis.Name = "tNePasSortirManquantsDepuis"
        Me.tNePasSortirManquantsDepuis.Size = New System.Drawing.Size(36, 19)
        Me.tNePasSortirManquantsDepuis.TabIndex = 76
        Me.tNePasSortirManquantsDepuis.Tag = Nothing
        Me.tNePasSortirManquantsDepuis.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'rbManquantNbrCommande
        '
        Me.rbManquantNbrCommande.AutoSize = True
        Me.rbManquantNbrCommande.Location = New System.Drawing.Point(60, 97)
        Me.rbManquantNbrCommande.Name = "rbManquantNbrCommande"
        Me.rbManquantNbrCommande.Size = New System.Drawing.Size(14, 13)
        Me.rbManquantNbrCommande.TabIndex = 85
        Me.rbManquantNbrCommande.TabStop = True
        Me.rbManquantNbrCommande.UseVisualStyleBackColor = True
        '
        'rbManquantJour
        '
        Me.rbManquantJour.AutoSize = True
        Me.rbManquantJour.Location = New System.Drawing.Point(60, 74)
        Me.rbManquantJour.Name = "rbManquantJour"
        Me.rbManquantJour.Size = New System.Drawing.Size(14, 13)
        Me.rbManquantJour.TabIndex = 84
        Me.rbManquantJour.TabStop = True
        Me.rbManquantJour.UseVisualStyleBackColor = True
        '
        'Label67
        '
        Me.Label67.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label67.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label67.Location = New System.Drawing.Point(249, 93)
        Me.Label67.Name = "Label67"
        Me.Label67.Size = New System.Drawing.Size(137, 20)
        Me.Label67.TabIndex = 83
        Me.Label67.Text = "commandes"
        Me.Label67.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tNbrCommande
        '
        Me.tNbrCommande.AutoSize = False
        Me.tNbrCommande.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNbrCommande.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNbrCommande.DataType = GetType(Integer)
        Me.tNbrCommande.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNbrCommande.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tNbrCommande.Location = New System.Drawing.Point(207, 95)
        Me.tNbrCommande.Name = "tNbrCommande"
        Me.tNbrCommande.Size = New System.Drawing.Size(36, 19)
        Me.tNbrCommande.TabIndex = 82
        Me.tNbrCommande.Tag = Nothing
        Me.tNbrCommande.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label66
        '
        Me.Label66.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label66.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label66.Location = New System.Drawing.Point(80, 93)
        Me.Label66.Name = "Label66"
        Me.Label66.Size = New System.Drawing.Size(122, 20)
        Me.Label66.TabIndex = 81
        Me.Label66.Text = "Classer manquant aprés"
        Me.Label66.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'chbMultipleDeCinq
        '
        Me.chbMultipleDeCinq.BackColor = System.Drawing.Color.Transparent
        Me.chbMultipleDeCinq.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbMultipleDeCinq.Location = New System.Drawing.Point(29, 127)
        Me.chbMultipleDeCinq.Name = "chbMultipleDeCinq"
        Me.chbMultipleDeCinq.Size = New System.Drawing.Size(406, 19)
        Me.chbMultipleDeCinq.TabIndex = 80
        Me.chbMultipleDeCinq.Text = "Quantité  multiple de 5"
        Me.chbMultipleDeCinq.UseVisualStyleBackColor = False
        '
        'lAnneeProchaine
        '
        Me.lAnneeProchaine.AutoSize = True
        Me.lAnneeProchaine.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.lAnneeProchaine.ForeColor = System.Drawing.SystemColors.ControlText
        Me.lAnneeProchaine.Location = New System.Drawing.Point(34, 243)
        Me.lAnneeProchaine.Name = "lAnneeProchaine"
        Me.lAnneeProchaine.Size = New System.Drawing.Size(23, 13)
        Me.lAnneeProchaine.TabIndex = 79
        Me.lAnneeProchaine.Text = "DU"
        Me.lAnneeProchaine.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lAnneeCourant
        '
        Me.lAnneeCourant.AutoSize = True
        Me.lAnneeCourant.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.lAnneeCourant.ForeColor = System.Drawing.SystemColors.ControlText
        Me.lAnneeCourant.Location = New System.Drawing.Point(34, 208)
        Me.lAnneeCourant.Name = "lAnneeCourant"
        Me.lAnneeCourant.Size = New System.Drawing.Size(23, 13)
        Me.lAnneeCourant.TabIndex = 77
        Me.lAnneeCourant.Text = "DU"
        Me.lAnneeCourant.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.dtpfinAnneeProchaine)
        Me.GroupBox4.Controls.Add(Me.dtpDebutAnneeProchaine)
        Me.GroupBox4.Controls.Add(Me.Label24)
        Me.GroupBox4.Controls.Add(Me.dtpFinAnneeCourant)
        Me.GroupBox4.Controls.Add(Me.Label25)
        Me.GroupBox4.Controls.Add(Me.dtpDebutAnneeCourant)
        Me.GroupBox4.Location = New System.Drawing.Point(69, 162)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(268, 109)
        Me.GroupBox4.TabIndex = 78
        Me.GroupBox4.TabStop = False
        Me.GroupBox4.Text = "Congé"
        '
        'dtpfinAnneeProchaine
        '
        Me.dtpfinAnneeProchaine.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpfinAnneeProchaine.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpfinAnneeProchaine.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpfinAnneeProchaine.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpfinAnneeProchaine.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpfinAnneeProchaine.EmptyAsNull = True
        Me.dtpfinAnneeProchaine.Location = New System.Drawing.Point(133, 76)
        Me.dtpfinAnneeProchaine.Name = "dtpfinAnneeProchaine"
        Me.dtpfinAnneeProchaine.Size = New System.Drawing.Size(99, 18)
        Me.dtpfinAnneeProchaine.TabIndex = 77
        Me.dtpfinAnneeProchaine.Tag = Nothing
        Me.dtpfinAnneeProchaine.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpfinAnneeProchaine.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'dtpDebutAnneeProchaine
        '
        Me.dtpDebutAnneeProchaine.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDebutAnneeProchaine.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDebutAnneeProchaine.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDebutAnneeProchaine.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutAnneeProchaine.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutAnneeProchaine.EmptyAsNull = True
        Me.dtpDebutAnneeProchaine.Location = New System.Drawing.Point(28, 76)
        Me.dtpDebutAnneeProchaine.Name = "dtpDebutAnneeProchaine"
        Me.dtpDebutAnneeProchaine.Size = New System.Drawing.Size(99, 18)
        Me.dtpDebutAnneeProchaine.TabIndex = 80
        Me.dtpDebutAnneeProchaine.Tag = Nothing
        Me.dtpDebutAnneeProchaine.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutAnneeProchaine.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label24
        '
        Me.Label24.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label24.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label24.Location = New System.Drawing.Point(143, 20)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(86, 13)
        Me.Label24.TabIndex = 66
        Me.Label24.Text = "AU"
        Me.Label24.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'dtpFinAnneeCourant
        '
        Me.dtpFinAnneeCourant.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpFinAnneeCourant.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpFinAnneeCourant.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpFinAnneeCourant.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFinAnneeCourant.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFinAnneeCourant.EmptyAsNull = True
        Me.dtpFinAnneeCourant.Location = New System.Drawing.Point(133, 45)
        Me.dtpFinAnneeCourant.Name = "dtpFinAnneeCourant"
        Me.dtpFinAnneeCourant.Size = New System.Drawing.Size(99, 18)
        Me.dtpFinAnneeCourant.TabIndex = 79
        Me.dtpFinAnneeCourant.Tag = Nothing
        Me.dtpFinAnneeCourant.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFinAnneeCourant.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label25
        '
        Me.Label25.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label25.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label25.Location = New System.Drawing.Point(30, 20)
        Me.Label25.Name = "Label25"
        Me.Label25.Size = New System.Drawing.Size(86, 13)
        Me.Label25.TabIndex = 64
        Me.Label25.Text = "DU"
        Me.Label25.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'dtpDebutAnneeCourant
        '
        Me.dtpDebutAnneeCourant.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDebutAnneeCourant.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDebutAnneeCourant.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDebutAnneeCourant.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutAnneeCourant.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutAnneeCourant.EmptyAsNull = True
        Me.dtpDebutAnneeCourant.Location = New System.Drawing.Point(28, 44)
        Me.dtpDebutAnneeCourant.Name = "dtpDebutAnneeCourant"
        Me.dtpDebutAnneeCourant.Size = New System.Drawing.Size(99, 18)
        Me.dtpDebutAnneeCourant.TabIndex = 78
        Me.dtpDebutAnneeCourant.Tag = Nothing
        Me.dtpDebutAnneeCourant.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutAnneeCourant.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label22
        '
        Me.Label22.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label22.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label22.Location = New System.Drawing.Point(80, 72)
        Me.Label22.Name = "Label22"
        Me.Label22.Size = New System.Drawing.Size(122, 17)
        Me.Label22.TabIndex = 72
        Me.Label22.Text = "Classer manquant aprés "
        Me.Label22.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label20
        '
        Me.Label20.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label20.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label20.Location = New System.Drawing.Point(25, 41)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(98, 13)
        Me.Label20.TabIndex = 70
        Me.Label20.Text = "Cmde Groupée/J :"
        Me.Label20.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tCommandeGroupeJ
        '
        Me.tCommandeGroupeJ.AutoSize = False
        Me.tCommandeGroupeJ.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCommandeGroupeJ.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tCommandeGroupeJ.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tCommandeGroupeJ.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tCommandeGroupeJ.Location = New System.Drawing.Point(143, 38)
        Me.tCommandeGroupeJ.Name = "tCommandeGroupeJ"
        Me.tCommandeGroupeJ.Size = New System.Drawing.Size(119, 19)
        Me.tCommandeGroupeJ.TabIndex = 69
        Me.tCommandeGroupeJ.Tag = Nothing
        Me.tCommandeGroupeJ.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage5
        '
        Me.C1DockingTabPage5.Controls.Add(Me.GroupBox3)
        Me.C1DockingTabPage5.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage5.Name = "C1DockingTabPage5"
        Me.C1DockingTabPage5.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage5.TabIndex = 4
        Me.C1DockingTabPage5.Text = "Numéro séquentiel"
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.Label3)
        Me.GroupBox3.Controls.Add(Me.tOrdonnancier)
        Me.GroupBox3.Controls.Add(Me.Label5)
        Me.GroupBox3.Controls.Add(Me.tBlDevis)
        Me.GroupBox3.Controls.Add(Me.Label19)
        Me.GroupBox3.Controls.Add(Me.tFacture)
        Me.GroupBox3.Location = New System.Drawing.Point(19, 22)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(570, 128)
        Me.GroupBox3.TabIndex = 1
        Me.GroupBox3.TabStop = False
        Me.GroupBox3.Text = "Numéro à prendre prochainement"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label3.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label3.Location = New System.Drawing.Point(37, 89)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(77, 13)
        Me.Label3.TabIndex = 68
        Me.Label3.Text = "Ordonnancier :"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tOrdonnancier
        '
        Me.tOrdonnancier.AutoSize = False
        Me.tOrdonnancier.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tOrdonnancier.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tOrdonnancier.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tOrdonnancier.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tOrdonnancier.Location = New System.Drawing.Point(120, 87)
        Me.tOrdonnancier.Name = "tOrdonnancier"
        Me.tOrdonnancier.Size = New System.Drawing.Size(119, 19)
        Me.tOrdonnancier.TabIndex = 67
        Me.tOrdonnancier.Tag = Nothing
        Me.tOrdonnancier.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label5.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label5.Location = New System.Drawing.Point(50, 60)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(64, 13)
        Me.Label5.TabIndex = 66
        Me.Label5.Text = "BL / Devis :"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tBlDevis
        '
        Me.tBlDevis.AutoSize = False
        Me.tBlDevis.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tBlDevis.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tBlDevis.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tBlDevis.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tBlDevis.Location = New System.Drawing.Point(120, 58)
        Me.tBlDevis.Name = "tBlDevis"
        Me.tBlDevis.Size = New System.Drawing.Size(119, 19)
        Me.tBlDevis.TabIndex = 65
        Me.tBlDevis.Tag = Nothing
        Me.tBlDevis.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label19
        '
        Me.Label19.AutoSize = True
        Me.Label19.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label19.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label19.Location = New System.Drawing.Point(65, 31)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(49, 13)
        Me.Label19.TabIndex = 64
        Me.Label19.Text = "Facture :"
        Me.Label19.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tFacture
        '
        Me.tFacture.AutoSize = False
        Me.tFacture.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tFacture.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tFacture.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tFacture.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tFacture.Location = New System.Drawing.Point(120, 29)
        Me.tFacture.Name = "tFacture"
        Me.tFacture.Size = New System.Drawing.Size(119, 19)
        Me.tFacture.TabIndex = 63
        Me.tFacture.Tag = Nothing
        Me.tFacture.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage6
        '
        Me.C1DockingTabPage6.Controls.Add(Me.ChbAutoriserSaisieNonMembeFamille)
        Me.C1DockingTabPage6.Controls.Add(Me.ChbAfficherReglementsSupprimes)
        Me.C1DockingTabPage6.Controls.Add(Me.chbMettreAJourPrixFrigo)
        Me.C1DockingTabPage6.Controls.Add(Me.Label65)
        Me.C1DockingTabPage6.Controls.Add(Me.tNbrAppareillage)
        Me.C1DockingTabPage6.Controls.Add(Me.Label64)
        Me.C1DockingTabPage6.Controls.Add(Me.tNbrPriseEnCharge)
        Me.C1DockingTabPage6.Controls.Add(Me.Label63)
        Me.C1DockingTabPage6.Controls.Add(Me.tNbrOrdonnance)
        Me.C1DockingTabPage6.Controls.Add(Me.chbGestionBon)
        Me.C1DockingTabPage6.Controls.Add(Me.chbActiverOMFAPCI)
        Me.C1DockingTabPage6.Controls.Add(Me.Label53)
        Me.C1DockingTabPage6.Controls.Add(Me.tRemise)
        Me.C1DockingTabPage6.Controls.Add(Me.chbAjouterMontantTimbreFacture)
        Me.C1DockingTabPage6.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage6.Name = "C1DockingTabPage6"
        Me.C1DockingTabPage6.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage6.TabIndex = 5
        Me.C1DockingTabPage6.Text = "Vente"
        '
        'ChbAutoriserSaisieNonMembeFamille
        '
        Me.ChbAutoriserSaisieNonMembeFamille.BackColor = System.Drawing.Color.Transparent
        Me.ChbAutoriserSaisieNonMembeFamille.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbAutoriserSaisieNonMembeFamille.Location = New System.Drawing.Point(19, 219)
        Me.ChbAutoriserSaisieNonMembeFamille.Name = "ChbAutoriserSaisieNonMembeFamille"
        Me.ChbAutoriserSaisieNonMembeFamille.Size = New System.Drawing.Size(505, 19)
        Me.ChbAutoriserSaisieNonMembeFamille.TabIndex = 91
        Me.ChbAutoriserSaisieNonMembeFamille.Text = "Chosir le nom de malade seulement de la liste"
        Me.ChbAutoriserSaisieNonMembeFamille.UseVisualStyleBackColor = False
        '
        'ChbAfficherReglementsSupprimes
        '
        Me.ChbAfficherReglementsSupprimes.BackColor = System.Drawing.Color.Transparent
        Me.ChbAfficherReglementsSupprimes.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbAfficherReglementsSupprimes.Location = New System.Drawing.Point(19, 180)
        Me.ChbAfficherReglementsSupprimes.Name = "ChbAfficherReglementsSupprimes"
        Me.ChbAfficherReglementsSupprimes.Size = New System.Drawing.Size(406, 19)
        Me.ChbAfficherReglementsSupprimes.TabIndex = 90
        Me.ChbAfficherReglementsSupprimes.Text = "Afficher les règlements supprimés"
        Me.ChbAfficherReglementsSupprimes.UseVisualStyleBackColor = False
        '
        'chbMettreAJourPrixFrigo
        '
        Me.chbMettreAJourPrixFrigo.BackColor = System.Drawing.Color.Transparent
        Me.chbMettreAJourPrixFrigo.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbMettreAJourPrixFrigo.Location = New System.Drawing.Point(18, 155)
        Me.chbMettreAJourPrixFrigo.Name = "chbMettreAJourPrixFrigo"
        Me.chbMettreAJourPrixFrigo.Size = New System.Drawing.Size(406, 19)
        Me.chbMettreAJourPrixFrigo.TabIndex = 89
        Me.chbMettreAJourPrixFrigo.Text = "Mettre à jour les prix des Frigos"
        Me.chbMettreAJourPrixFrigo.UseVisualStyleBackColor = False
        '
        'Label65
        '
        Me.Label65.AutoSize = True
        Me.Label65.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label65.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label65.Location = New System.Drawing.Point(16, 318)
        Me.Label65.Name = "Label65"
        Me.Label65.Size = New System.Drawing.Size(204, 13)
        Me.Label65.TabIndex = 88
        Me.Label65.Text = "Nombre de jours de validiter appareillage :"
        Me.Label65.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tNbrAppareillage
        '
        Me.tNbrAppareillage.AutoSize = False
        Me.tNbrAppareillage.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNbrAppareillage.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNbrAppareillage.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNbrAppareillage.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tNbrAppareillage.Location = New System.Drawing.Point(238, 316)
        Me.tNbrAppareillage.Name = "tNbrAppareillage"
        Me.tNbrAppareillage.Size = New System.Drawing.Size(119, 19)
        Me.tNbrAppareillage.TabIndex = 87
        Me.tNbrAppareillage.Tag = Nothing
        Me.tNbrAppareillage.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label64
        '
        Me.Label64.AutoSize = True
        Me.Label64.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label64.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label64.Location = New System.Drawing.Point(16, 293)
        Me.Label64.Name = "Label64"
        Me.Label64.Size = New System.Drawing.Size(220, 13)
        Me.Label64.TabIndex = 86
        Me.Label64.Text = "Nombre de jours de validiter prise en charge :"
        Me.Label64.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tNbrPriseEnCharge
        '
        Me.tNbrPriseEnCharge.AutoSize = False
        Me.tNbrPriseEnCharge.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNbrPriseEnCharge.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNbrPriseEnCharge.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNbrPriseEnCharge.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tNbrPriseEnCharge.Location = New System.Drawing.Point(238, 291)
        Me.tNbrPriseEnCharge.Name = "tNbrPriseEnCharge"
        Me.tNbrPriseEnCharge.Size = New System.Drawing.Size(119, 19)
        Me.tNbrPriseEnCharge.TabIndex = 85
        Me.tNbrPriseEnCharge.Tag = Nothing
        Me.tNbrPriseEnCharge.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label63
        '
        Me.Label63.AutoSize = True
        Me.Label63.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label63.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label63.Location = New System.Drawing.Point(16, 268)
        Me.Label63.Name = "Label63"
        Me.Label63.Size = New System.Drawing.Size(204, 13)
        Me.Label63.TabIndex = 84
        Me.Label63.Text = "Nombre de jours de validiter ordonnance :"
        Me.Label63.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tNbrOrdonnance
        '
        Me.tNbrOrdonnance.AutoSize = False
        Me.tNbrOrdonnance.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNbrOrdonnance.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNbrOrdonnance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNbrOrdonnance.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tNbrOrdonnance.Location = New System.Drawing.Point(238, 266)
        Me.tNbrOrdonnance.Name = "tNbrOrdonnance"
        Me.tNbrOrdonnance.Size = New System.Drawing.Size(119, 19)
        Me.tNbrOrdonnance.TabIndex = 83
        Me.tNbrOrdonnance.Tag = Nothing
        Me.tNbrOrdonnance.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'chbGestionBon
        '
        Me.chbGestionBon.BackColor = System.Drawing.Color.Transparent
        Me.chbGestionBon.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbGestionBon.Location = New System.Drawing.Point(18, 130)
        Me.chbGestionBon.Name = "chbGestionBon"
        Me.chbGestionBon.Size = New System.Drawing.Size(406, 19)
        Me.chbGestionBon.TabIndex = 82
        Me.chbGestionBon.Text = "Gestion des bons"
        Me.chbGestionBon.UseVisualStyleBackColor = False
        '
        'chbActiverOMFAPCI
        '
        Me.chbActiverOMFAPCI.BackColor = System.Drawing.Color.Transparent
        Me.chbActiverOMFAPCI.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbActiverOMFAPCI.Location = New System.Drawing.Point(18, 105)
        Me.chbActiverOMFAPCI.Name = "chbActiverOMFAPCI"
        Me.chbActiverOMFAPCI.Size = New System.Drawing.Size(406, 19)
        Me.chbActiverOMFAPCI.TabIndex = 81
        Me.chbActiverOMFAPCI.Text = "Activer l'option OMF / APCI"
        Me.chbActiverOMFAPCI.UseVisualStyleBackColor = False
        '
        'Label53
        '
        Me.Label53.AutoSize = True
        Me.Label53.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label53.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label53.Location = New System.Drawing.Point(16, 56)
        Me.Label53.Name = "Label53"
        Me.Label53.Size = New System.Drawing.Size(75, 13)
        Me.Label53.TabIndex = 80
        Me.Label53.Text = "Taux Remise :"
        Me.Label53.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tRemise
        '
        Me.tRemise.AutoSize = False
        Me.tRemise.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tRemise.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tRemise.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tRemise.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tRemise.Location = New System.Drawing.Point(238, 54)
        Me.tRemise.Name = "tRemise"
        Me.tRemise.Size = New System.Drawing.Size(119, 19)
        Me.tRemise.TabIndex = 79
        Me.tRemise.Tag = Nothing
        Me.tRemise.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'chbAjouterMontantTimbreFacture
        '
        Me.chbAjouterMontantTimbreFacture.BackColor = System.Drawing.Color.Transparent
        Me.chbAjouterMontantTimbreFacture.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbAjouterMontantTimbreFacture.Location = New System.Drawing.Point(18, 80)
        Me.chbAjouterMontantTimbreFacture.Name = "chbAjouterMontantTimbreFacture"
        Me.chbAjouterMontantTimbreFacture.Size = New System.Drawing.Size(406, 19)
        Me.chbAjouterMontantTimbreFacture.TabIndex = 78
        Me.chbAjouterMontantTimbreFacture.Text = "Ajouter le montant du timbre à la facture"
        Me.chbAjouterMontantTimbreFacture.UseVisualStyleBackColor = False
        '
        'C1DockingTabPage7
        '
        Me.C1DockingTabPage7.Controls.Add(Me.gEnvoiLog)
        Me.C1DockingTabPage7.Controls.Add(Me.chbAutoriseEnvoiMail)
        Me.C1DockingTabPage7.Controls.Add(Me.gPramatresMail)
        Me.C1DockingTabPage7.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage7.Name = "C1DockingTabPage7"
        Me.C1DockingTabPage7.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage7.TabIndex = 6
        Me.C1DockingTabPage7.Text = "Gestion des erreurs"
        '
        'gEnvoiLog
        '
        Me.gEnvoiLog.Controls.Add(Me.CheckedListBox1)
        Me.gEnvoiLog.Controls.Add(Me.pbTestEnvoiFichierLog)
        Me.gEnvoiLog.Controls.Add(Me.Label31)
        Me.gEnvoiLog.Controls.Add(Me.bEnvoiLog)
        Me.gEnvoiLog.Controls.Add(Me.LTestEnvoi)
        Me.gEnvoiLog.Location = New System.Drawing.Point(19, 228)
        Me.gEnvoiLog.Name = "gEnvoiLog"
        Me.gEnvoiLog.Size = New System.Drawing.Size(571, 115)
        Me.gEnvoiLog.TabIndex = 80
        Me.gEnvoiLog.TabStop = False
        Me.gEnvoiLog.Text = "Envoi du fichier de suivi des erreurs au service support de Next Software"
        '
        'CheckedListBox1
        '
        Me.CheckedListBox1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CheckedListBox1.BackColor = System.Drawing.SystemColors.GradientActiveCaption
        Me.CheckedListBox1.FormattingEnabled = True
        Me.CheckedListBox1.Location = New System.Drawing.Point(12, 19)
        Me.CheckedListBox1.Name = "CheckedListBox1"
        Me.CheckedListBox1.Size = New System.Drawing.Size(193, 64)
        Me.CheckedListBox1.TabIndex = 96
        '
        'pbTestEnvoiFichierLog
        '
        Me.pbTestEnvoiFichierLog.Image = Global.Pharma2000Premium.My.Resources.Resources.loading_transparent_4
        Me.pbTestEnvoiFichierLog.Location = New System.Drawing.Point(391, 19)
        Me.pbTestEnvoiFichierLog.Name = "pbTestEnvoiFichierLog"
        Me.pbTestEnvoiFichierLog.Size = New System.Drawing.Size(84, 78)
        Me.pbTestEnvoiFichierLog.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.pbTestEnvoiFichierLog.TabIndex = 95
        Me.pbTestEnvoiFichierLog.TabStop = False
        Me.pbTestEnvoiFichierLog.Visible = False
        '
        'Label31
        '
        Me.Label31.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label31.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label31.Location = New System.Drawing.Point(6, 91)
        Me.Label31.Name = "Label31"
        Me.Label31.Size = New System.Drawing.Size(337, 22)
        Me.Label31.TabIndex = 65
        Me.Label31.Text = "NB:  L'envoi peut prendre quelques minutes suivant la taille de fichier"
        Me.Label31.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'bEnvoiLog
        '
        Me.bEnvoiLog.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bEnvoiLog.Image = Global.Pharma2000Premium.My.Resources.Resources.archive1
        Me.bEnvoiLog.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bEnvoiLog.Location = New System.Drawing.Point(222, 33)
        Me.bEnvoiLog.Name = "bEnvoiLog"
        Me.bEnvoiLog.Size = New System.Drawing.Size(93, 35)
        Me.bEnvoiLog.TabIndex = 3
        Me.bEnvoiLog.Text = "Envoyer"
        Me.bEnvoiLog.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bEnvoiLog.UseVisualStyleBackColor = True
        Me.bEnvoiLog.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'LTestEnvoi
        '
        Me.LTestEnvoi.ForeColor = System.Drawing.Color.Red
        Me.LTestEnvoi.Location = New System.Drawing.Point(341, 19)
        Me.LTestEnvoi.Name = "LTestEnvoi"
        Me.LTestEnvoi.Size = New System.Drawing.Size(224, 78)
        Me.LTestEnvoi.TabIndex = 97
        '
        'chbAutoriseEnvoiMail
        '
        Me.chbAutoriseEnvoiMail.BackColor = System.Drawing.Color.Transparent
        Me.chbAutoriseEnvoiMail.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbAutoriseEnvoiMail.Location = New System.Drawing.Point(31, 25)
        Me.chbAutoriseEnvoiMail.Name = "chbAutoriseEnvoiMail"
        Me.chbAutoriseEnvoiMail.Size = New System.Drawing.Size(532, 19)
        Me.chbAutoriseEnvoiMail.TabIndex = 78
        Me.chbAutoriseEnvoiMail.Text = "Autoriser l'envoi des e-mails au service support de Next Software en cas d'erreur" & _
    " au niveau de l'application"
        Me.chbAutoriseEnvoiMail.UseVisualStyleBackColor = False
        '
        'gPramatresMail
        '
        Me.gPramatresMail.Controls.Add(Me.pbTestEnvoiEmail)
        Me.gPramatresMail.Controls.Add(Me.LTestMoTdePass)
        Me.gPramatresMail.Controls.Add(Me.LTestMail)
        Me.gPramatresMail.Controls.Add(Me.LTestPort)
        Me.gPramatresMail.Controls.Add(Me.LTestSMTPMail)
        Me.gPramatresMail.Controls.Add(Me.bTestEmail)
        Me.gPramatresMail.Controls.Add(Me.LTestEnvoiEmail)
        Me.gPramatresMail.Controls.Add(Me.tSMTPMail)
        Me.gPramatresMail.Controls.Add(Me.Label23)
        Me.gPramatresMail.Controls.Add(Me.Label29)
        Me.gPramatresMail.Controls.Add(Me.tPortMail)
        Me.gPramatresMail.Controls.Add(Me.tMotDePasseMail)
        Me.gPramatresMail.Controls.Add(Me.Label27)
        Me.gPramatresMail.Controls.Add(Me.Label28)
        Me.gPramatresMail.Controls.Add(Me.tAdresseMail)
        Me.gPramatresMail.Location = New System.Drawing.Point(19, 27)
        Me.gPramatresMail.Name = "gPramatresMail"
        Me.gPramatresMail.Size = New System.Drawing.Size(571, 183)
        Me.gPramatresMail.TabIndex = 79
        Me.gPramatresMail.TabStop = False
        '
        'pbTestEnvoiEmail
        '
        Me.pbTestEnvoiEmail.Image = Global.Pharma2000Premium.My.Resources.Resources.loading_transparent_4
        Me.pbTestEnvoiEmail.Location = New System.Drawing.Point(391, 53)
        Me.pbTestEnvoiEmail.Name = "pbTestEnvoiEmail"
        Me.pbTestEnvoiEmail.Size = New System.Drawing.Size(84, 80)
        Me.pbTestEnvoiEmail.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.pbTestEnvoiEmail.TabIndex = 94
        Me.pbTestEnvoiEmail.TabStop = False
        Me.pbTestEnvoiEmail.Visible = False
        '
        'LTestMoTdePass
        '
        Me.LTestMoTdePass.AutoSize = True
        Me.LTestMoTdePass.ForeColor = System.Drawing.Color.Red
        Me.LTestMoTdePass.Location = New System.Drawing.Point(315, 127)
        Me.LTestMoTdePass.Name = "LTestMoTdePass"
        Me.LTestMoTdePass.Size = New System.Drawing.Size(0, 13)
        Me.LTestMoTdePass.TabIndex = 93
        '
        'LTestMail
        '
        Me.LTestMail.AutoSize = True
        Me.LTestMail.ForeColor = System.Drawing.Color.Red
        Me.LTestMail.Location = New System.Drawing.Point(315, 99)
        Me.LTestMail.Name = "LTestMail"
        Me.LTestMail.Size = New System.Drawing.Size(0, 13)
        Me.LTestMail.TabIndex = 92
        '
        'LTestPort
        '
        Me.LTestPort.AutoSize = True
        Me.LTestPort.ForeColor = System.Drawing.Color.Red
        Me.LTestPort.Location = New System.Drawing.Point(315, 70)
        Me.LTestPort.Name = "LTestPort"
        Me.LTestPort.Size = New System.Drawing.Size(0, 13)
        Me.LTestPort.TabIndex = 91
        '
        'LTestSMTPMail
        '
        Me.LTestSMTPMail.AutoSize = True
        Me.LTestSMTPMail.ForeColor = System.Drawing.Color.Red
        Me.LTestSMTPMail.Location = New System.Drawing.Point(315, 40)
        Me.LTestSMTPMail.Name = "LTestSMTPMail"
        Me.LTestSMTPMail.Size = New System.Drawing.Size(0, 13)
        Me.LTestSMTPMail.TabIndex = 90
        '
        'bTestEmail
        '
        Me.bTestEmail.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bTestEmail.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bTestEmail.Location = New System.Drawing.Point(127, 149)
        Me.bTestEmail.Name = "bTestEmail"
        Me.bTestEmail.Size = New System.Drawing.Size(78, 27)
        Me.bTestEmail.TabIndex = 88
        Me.bTestEmail.Text = "Tester"
        Me.bTestEmail.UseVisualStyleBackColor = True
        Me.bTestEmail.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'LTestEnvoiEmail
        '
        Me.LTestEnvoiEmail.ForeColor = System.Drawing.Color.Red
        Me.LTestEnvoiEmail.Location = New System.Drawing.Point(300, 53)
        Me.LTestEnvoiEmail.Name = "LTestEnvoiEmail"
        Me.LTestEnvoiEmail.Size = New System.Drawing.Size(265, 78)
        Me.LTestEnvoiEmail.TabIndex = 89
        '
        'tSMTPMail
        '
        Me.tSMTPMail.AutoSize = False
        Me.tSMTPMail.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tSMTPMail.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tSMTPMail.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tSMTPMail.Location = New System.Drawing.Point(127, 38)
        Me.tSMTPMail.Name = "tSMTPMail"
        Me.tSMTPMail.Size = New System.Drawing.Size(161, 19)
        Me.tSMTPMail.TabIndex = 63
        Me.tSMTPMail.Tag = Nothing
        Me.tSMTPMail.TrimStart = True
        Me.tSMTPMail.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label23
        '
        Me.Label23.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label23.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label23.Location = New System.Drawing.Point(23, 40)
        Me.Label23.Name = "Label23"
        Me.Label23.Size = New System.Drawing.Size(98, 13)
        Me.Label23.TabIndex = 64
        Me.Label23.Text = "SMTP Mail :"
        Me.Label23.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label29
        '
        Me.Label29.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label29.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label29.Location = New System.Drawing.Point(23, 127)
        Me.Label29.Name = "Label29"
        Me.Label29.Size = New System.Drawing.Size(98, 13)
        Me.Label29.TabIndex = 70
        Me.Label29.Text = "Mot de passe :"
        Me.Label29.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tPortMail
        '
        Me.tPortMail.AutoSize = False
        Me.tPortMail.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tPortMail.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tPortMail.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tPortMail.Location = New System.Drawing.Point(127, 68)
        Me.tPortMail.Name = "tPortMail"
        Me.tPortMail.Size = New System.Drawing.Size(161, 19)
        Me.tPortMail.TabIndex = 65
        Me.tPortMail.Tag = Nothing
        Me.tPortMail.TrimStart = True
        Me.tPortMail.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tMotDePasseMail
        '
        Me.tMotDePasseMail.AutoSize = False
        Me.tMotDePasseMail.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMotDePasseMail.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tMotDePasseMail.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tMotDePasseMail.Location = New System.Drawing.Point(127, 125)
        Me.tMotDePasseMail.Name = "tMotDePasseMail"
        Me.tMotDePasseMail.PasswordChar = Global.Microsoft.VisualBasic.ChrW(42)
        Me.tMotDePasseMail.Size = New System.Drawing.Size(161, 19)
        Me.tMotDePasseMail.TabIndex = 69
        Me.tMotDePasseMail.Tag = Nothing
        Me.tMotDePasseMail.TrimStart = True
        Me.tMotDePasseMail.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label27
        '
        Me.Label27.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label27.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label27.Location = New System.Drawing.Point(23, 70)
        Me.Label27.Name = "Label27"
        Me.Label27.Size = New System.Drawing.Size(98, 13)
        Me.Label27.TabIndex = 66
        Me.Label27.Text = "Port :"
        Me.Label27.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label28
        '
        Me.Label28.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label28.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label28.Location = New System.Drawing.Point(23, 99)
        Me.Label28.Name = "Label28"
        Me.Label28.Size = New System.Drawing.Size(98, 13)
        Me.Label28.TabIndex = 68
        Me.Label28.Text = "Mail :"
        Me.Label28.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tAdresseMail
        '
        Me.tAdresseMail.AutoSize = False
        Me.tAdresseMail.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tAdresseMail.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tAdresseMail.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tAdresseMail.Location = New System.Drawing.Point(127, 97)
        Me.tAdresseMail.Name = "tAdresseMail"
        Me.tAdresseMail.Size = New System.Drawing.Size(161, 19)
        Me.tAdresseMail.TabIndex = 67
        Me.tAdresseMail.Tag = Nothing
        Me.tAdresseMail.TrimStart = True
        Me.tAdresseMail.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage8
        '
        Me.C1DockingTabPage8.Controls.Add(Me.GroupBox5)
        Me.C1DockingTabPage8.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage8.Name = "C1DockingTabPage8"
        Me.C1DockingTabPage8.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage8.TabIndex = 7
        Me.C1DockingTabPage8.Text = "Préparation"
        '
        'GroupBox5
        '
        Me.GroupBox5.Controls.Add(Me.Label32)
        Me.GroupBox5.Controls.Add(Me.tNbreJourValiditeParDefaut)
        Me.GroupBox5.Location = New System.Drawing.Point(29, 45)
        Me.GroupBox5.Name = "GroupBox5"
        Me.GroupBox5.Size = New System.Drawing.Size(312, 109)
        Me.GroupBox5.TabIndex = 2
        Me.GroupBox5.TabStop = False
        Me.GroupBox5.Text = "Le Nombre de jour de validité par défaut de la préparation"
        '
        'Label32
        '
        Me.Label32.AutoSize = True
        Me.Label32.Location = New System.Drawing.Point(18, 39)
        Me.Label32.Name = "Label32"
        Me.Label32.Size = New System.Drawing.Size(151, 13)
        Me.Label32.TabIndex = 0
        Me.Label32.Text = "Nombre de Jours de Validité   :"
        '
        'tNbreJourValiditeParDefaut
        '
        Me.tNbreJourValiditeParDefaut.Location = New System.Drawing.Point(171, 36)
        Me.tNbreJourValiditeParDefaut.Name = "tNbreJourValiditeParDefaut"
        Me.tNbreJourValiditeParDefaut.Size = New System.Drawing.Size(55, 20)
        Me.tNbreJourValiditeParDefaut.TabIndex = 1
        Me.tNbreJourValiditeParDefaut.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'C1DockingTabPage9
        '
        Me.C1DockingTabPage9.Controls.Add(Me.GBProduitSurveiller)
        Me.C1DockingTabPage9.Controls.Add(Me.LArticleValide)
        Me.C1DockingTabPage9.Controls.Add(Me.Label34)
        Me.C1DockingTabPage9.Controls.Add(Me.bModifierP)
        Me.C1DockingTabPage9.Controls.Add(Me.bQuitterP)
        Me.C1DockingTabPage9.Controls.Add(Me.bAnnulerP)
        Me.C1DockingTabPage9.Controls.Add(Me.bConfirmerP)
        Me.C1DockingTabPage9.Controls.Add(Me.bAjouterP)
        Me.C1DockingTabPage9.Controls.Add(Me.bSupprimerP)
        Me.C1DockingTabPage9.Controls.Add(Me.gArticle)
        Me.C1DockingTabPage9.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage9.Name = "C1DockingTabPage9"
        Me.C1DockingTabPage9.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage9.TabIndex = 8
        Me.C1DockingTabPage9.Text = "Articles à surveiller"
        '
        'GBProduitSurveiller
        '
        Me.GBProduitSurveiller.Controls.Add(Me.tCode)
        Me.GBProduitSurveiller.Controls.Add(Me.Label33)
        Me.GBProduitSurveiller.Controls.Add(Me.LNom)
        Me.GBProduitSurveiller.Controls.Add(Me.Label39)
        Me.GBProduitSurveiller.Controls.Add(Me.cmbArticle)
        Me.GBProduitSurveiller.Controls.Add(Me.chbScannerOrdonnance)
        Me.GBProduitSurveiller.Controls.Add(Me.Label42)
        Me.GBProduitSurveiller.Controls.Add(Me.chbCapturerPhoto)
        Me.GBProduitSurveiller.Controls.Add(Me.Label38)
        Me.GBProduitSurveiller.Controls.Add(Me.chbEnvoyerNotification)
        Me.GBProduitSurveiller.Location = New System.Drawing.Point(7, 11)
        Me.GBProduitSurveiller.Name = "GBProduitSurveiller"
        Me.GBProduitSurveiller.Size = New System.Drawing.Size(1038, 87)
        Me.GBProduitSurveiller.TabIndex = 95
        Me.GBProduitSurveiller.TabStop = False
        Me.GBProduitSurveiller.Text = "Produit à surveiller"
        Me.GBProduitSurveiller.Visible = False
        '
        'tCode
        '
        Me.tCode.AutoSize = False
        Me.tCode.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCode.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCode.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCode.Location = New System.Drawing.Point(81, 22)
        Me.tCode.Name = "tCode"
        Me.tCode.Size = New System.Drawing.Size(124, 22)
        Me.tCode.TabIndex = 98
        Me.tCode.Tag = Nothing
        Me.tCode.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCode.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label33
        '
        Me.Label33.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label33.Location = New System.Drawing.Point(17, 26)
        Me.Label33.Name = "Label33"
        Me.Label33.Size = New System.Drawing.Size(58, 13)
        Me.Label33.TabIndex = 97
        Me.Label33.Text = "Code :"
        Me.Label33.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'LNom
        '
        Me.LNom.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LNom.Location = New System.Drawing.Point(1, 56)
        Me.LNom.Name = "LNom"
        Me.LNom.Size = New System.Drawing.Size(74, 17)
        Me.LNom.TabIndex = 96
        Me.LNom.Text = "Désignation :"
        Me.LNom.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label39
        '
        Me.Label39.AutoSize = True
        Me.Label39.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label39.Location = New System.Drawing.Point(777, 40)
        Me.Label39.Name = "Label39"
        Me.Label39.Size = New System.Drawing.Size(78, 13)
        Me.Label39.TabIndex = 92
        Me.Label39.Text = "Capturer Photo"
        '
        'cmbArticle
        '
        Me.cmbArticle.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbArticle.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbArticle.Caption = ""
        Me.cmbArticle.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbArticle.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbArticle.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbArticle.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbArticle.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbArticle.Images.Add(CType(resources.GetObject("cmbArticle.Images"), System.Drawing.Image))
        Me.cmbArticle.Location = New System.Drawing.Point(81, 55)
        Me.cmbArticle.MatchEntryTimeout = CType(2000, Long)
        Me.cmbArticle.MaxDropDownItems = CType(5, Short)
        Me.cmbArticle.MaxLength = 32767
        Me.cmbArticle.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbArticle.Name = "cmbArticle"
        Me.cmbArticle.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbArticle.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbArticle.Size = New System.Drawing.Size(328, 22)
        Me.cmbArticle.TabIndex = 88
        Me.cmbArticle.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbArticle.PropBag = resources.GetString("cmbArticle.PropBag")
        '
        'chbScannerOrdonnance
        '
        Me.chbScannerOrdonnance.AutoSize = True
        Me.chbScannerOrdonnance.Location = New System.Drawing.Point(873, 17)
        Me.chbScannerOrdonnance.Name = "chbScannerOrdonnance"
        Me.chbScannerOrdonnance.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chbScannerOrdonnance.Size = New System.Drawing.Size(15, 14)
        Me.chbScannerOrdonnance.TabIndex = 89
        Me.chbScannerOrdonnance.UseVisualStyleBackColor = True
        '
        'Label42
        '
        Me.Label42.AutoSize = True
        Me.Label42.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label42.Location = New System.Drawing.Point(753, 64)
        Me.Label42.Name = "Label42"
        Me.Label42.Size = New System.Drawing.Size(102, 13)
        Me.Label42.TabIndex = 94
        Me.Label42.Text = "Envoyer Notification"
        '
        'chbCapturerPhoto
        '
        Me.chbCapturerPhoto.AutoSize = True
        Me.chbCapturerPhoto.Location = New System.Drawing.Point(874, 40)
        Me.chbCapturerPhoto.Name = "chbCapturerPhoto"
        Me.chbCapturerPhoto.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chbCapturerPhoto.Size = New System.Drawing.Size(15, 14)
        Me.chbCapturerPhoto.TabIndex = 91
        Me.chbCapturerPhoto.UseVisualStyleBackColor = True
        '
        'Label38
        '
        Me.Label38.AutoSize = True
        Me.Label38.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label38.Location = New System.Drawing.Point(747, 15)
        Me.Label38.Name = "Label38"
        Me.Label38.Size = New System.Drawing.Size(109, 13)
        Me.Label38.TabIndex = 90
        Me.Label38.Text = "Scanner Ordonnance"
        '
        'chbEnvoyerNotification
        '
        Me.chbEnvoyerNotification.AutoSize = True
        Me.chbEnvoyerNotification.Location = New System.Drawing.Point(874, 65)
        Me.chbEnvoyerNotification.Name = "chbEnvoyerNotification"
        Me.chbEnvoyerNotification.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chbEnvoyerNotification.Size = New System.Drawing.Size(15, 14)
        Me.chbEnvoyerNotification.TabIndex = 93
        Me.chbEnvoyerNotification.UseVisualStyleBackColor = True
        '
        'LArticleValide
        '
        Me.LArticleValide.AutoSize = True
        Me.LArticleValide.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LArticleValide.ForeColor = System.Drawing.Color.Red
        Me.LArticleValide.Location = New System.Drawing.Point(11, 342)
        Me.LArticleValide.Name = "LArticleValide"
        Me.LArticleValide.Size = New System.Drawing.Size(15, 13)
        Me.LArticleValide.TabIndex = 103
        Me.LArticleValide.Text = "--"
        Me.LArticleValide.Visible = False
        '
        'Label34
        '
        Me.Label34.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label34.Location = New System.Drawing.Point(7, 50)
        Me.Label34.Name = "Label34"
        Me.Label34.Size = New System.Drawing.Size(1040, 56)
        Me.Label34.TabIndex = 104
        Me.Label34.Text = "Liste des Articles à surveiller"
        Me.Label34.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bModifierP
        '
        Me.bModifierP.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bModifierP.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bModifierP.Image = Global.Pharma2000Premium.My.Resources.Resources.amodifier
        Me.bModifierP.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bModifierP.Location = New System.Drawing.Point(620, 337)
        Me.bModifierP.Name = "bModifierP"
        Me.bModifierP.Size = New System.Drawing.Size(80, 23)
        Me.bModifierP.TabIndex = 102
        Me.bModifierP.Text = "Modifier "
        Me.bModifierP.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bModifierP.UseVisualStyleBackColor = True
        Me.bModifierP.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.bModifierP.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bQuitterP
        '
        Me.bQuitterP.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitterP.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitterP.Image = Global.Pharma2000Premium.My.Resources.Resources.afermer
        Me.bQuitterP.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitterP.Location = New System.Drawing.Point(965, 337)
        Me.bQuitterP.Name = "bQuitterP"
        Me.bQuitterP.Size = New System.Drawing.Size(80, 23)
        Me.bQuitterP.TabIndex = 101
        Me.bQuitterP.Text = "Fermer"
        Me.bQuitterP.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bQuitterP.UseVisualStyleBackColor = True
        Me.bQuitterP.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAnnulerP
        '
        Me.bAnnulerP.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnulerP.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnulerP.Image = Global.Pharma2000Premium.My.Resources.Resources.aannuler
        Me.bAnnulerP.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnulerP.Location = New System.Drawing.Point(878, 337)
        Me.bAnnulerP.Name = "bAnnulerP"
        Me.bAnnulerP.Size = New System.Drawing.Size(80, 23)
        Me.bAnnulerP.TabIndex = 99
        Me.bAnnulerP.Text = "Annuler"
        Me.bAnnulerP.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAnnulerP.UseVisualStyleBackColor = True
        Me.bAnnulerP.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bConfirmerP
        '
        Me.bConfirmerP.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bConfirmerP.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmerP.Image = Global.Pharma2000Premium.My.Resources.Resources.avalider
        Me.bConfirmerP.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmerP.Location = New System.Drawing.Point(792, 337)
        Me.bConfirmerP.Name = "bConfirmerP"
        Me.bConfirmerP.Size = New System.Drawing.Size(80, 23)
        Me.bConfirmerP.TabIndex = 98
        Me.bConfirmerP.Text = "Confirmer"
        Me.bConfirmerP.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bConfirmerP.UseVisualStyleBackColor = True
        Me.bConfirmerP.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouterP
        '
        Me.bAjouterP.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouterP.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouterP.Image = Global.Pharma2000Premium.My.Resources.Resources.aajouter
        Me.bAjouterP.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouterP.Location = New System.Drawing.Point(707, 337)
        Me.bAjouterP.Name = "bAjouterP"
        Me.bAjouterP.Size = New System.Drawing.Size(80, 23)
        Me.bAjouterP.TabIndex = 97
        Me.bAjouterP.Text = "Ajouter"
        Me.bAjouterP.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouterP.UseVisualStyleBackColor = True
        Me.bAjouterP.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bSupprimerP
        '
        Me.bSupprimerP.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bSupprimerP.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimerP.Image = Global.Pharma2000Premium.My.Resources.Resources.asupprimer
        Me.bSupprimerP.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSupprimerP.Location = New System.Drawing.Point(533, 337)
        Me.bSupprimerP.Name = "bSupprimerP"
        Me.bSupprimerP.Size = New System.Drawing.Size(80, 23)
        Me.bSupprimerP.TabIndex = 100
        Me.bSupprimerP.Text = "Supprimer"
        Me.bSupprimerP.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSupprimerP.UseVisualStyleBackColor = True
        Me.bSupprimerP.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gArticle
        '
        Me.gArticle.AllowUpdate = False
        Me.gArticle.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gArticle.GroupByCaption = "Drag a column header here to group by that column"
        Me.gArticle.Images.Add(CType(resources.GetObject("gArticle.Images"), System.Drawing.Image))
        Me.gArticle.Location = New System.Drawing.Point(7, 108)
        Me.gArticle.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gArticle.Name = "gArticle"
        Me.gArticle.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gArticle.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gArticle.PreviewInfo.ZoomFactor = 75.0R
        Me.gArticle.PrintInfo.PageSettings = CType(resources.GetObject("gArticle.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gArticle.Size = New System.Drawing.Size(1040, 218)
        Me.gArticle.TabIndex = 96
        Me.gArticle.Text = "C1TrueDBGrid1"
        Me.gArticle.PropBag = resources.GetString("gArticle.PropBag")
        '
        'C1DockingTabPage10
        '
        Me.C1DockingTabPage10.Controls.Add(Me.tLatLong)
        Me.C1DockingTabPage10.Controls.Add(Me.Label35)
        Me.C1DockingTabPage10.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage10.Name = "C1DockingTabPage10"
        Me.C1DockingTabPage10.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage10.TabIndex = 9
        Me.C1DockingTabPage10.Text = "Mes voisinages"
        '
        'tLatLong
        '
        Me.tLatLong.AutoSize = False
        Me.tLatLong.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tLatLong.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tLatLong.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tLatLong.Location = New System.Drawing.Point(157, 61)
        Me.tLatLong.Name = "tLatLong"
        Me.tLatLong.Size = New System.Drawing.Size(173, 23)
        Me.tLatLong.TabIndex = 99
        Me.tLatLong.Tag = Nothing
        Me.tLatLong.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tLatLong.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label35
        '
        Me.Label35.AutoSize = True
        Me.Label35.Location = New System.Drawing.Point(35, 63)
        Me.Label35.Name = "Label35"
        Me.Label35.Size = New System.Drawing.Size(116, 13)
        Me.Label35.TabIndex = 0
        Me.Label35.Text = "Lattitude et Longitude :"
        '
        'C1DockingTabPage11
        '
        Me.C1DockingTabPage11.Controls.Add(Me.GroupBox6)
        Me.C1DockingTabPage11.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage11.Name = "C1DockingTabPage11"
        Me.C1DockingTabPage11.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage11.TabIndex = 10
        Me.C1DockingTabPage11.Text = "Paramètres  CNAM"
        '
        'GroupBox6
        '
        Me.GroupBox6.Controls.Add(Me.Label37)
        Me.GroupBox6.Controls.Add(Me.Label36)
        Me.GroupBox6.Controls.Add(Me.tTailleCodeCNAM)
        Me.GroupBox6.Location = New System.Drawing.Point(13, 17)
        Me.GroupBox6.Name = "GroupBox6"
        Me.GroupBox6.Size = New System.Drawing.Size(917, 109)
        Me.GroupBox6.TabIndex = 3
        Me.GroupBox6.TabStop = False
        Me.GroupBox6.Text = "Taille Code CNAM"
        '
        'Label37
        '
        Me.Label37.AutoSize = True
        Me.Label37.Location = New System.Drawing.Point(180, 38)
        Me.Label37.Name = "Label37"
        Me.Label37.Size = New System.Drawing.Size(42, 13)
        Me.Label37.TabIndex = 2
        Me.Label37.Text = "Chiffres"
        '
        'Label36
        '
        Me.Label36.AutoSize = True
        Me.Label36.Location = New System.Drawing.Point(18, 38)
        Me.Label36.Name = "Label36"
        Me.Label36.Size = New System.Drawing.Size(100, 13)
        Me.Label36.TabIndex = 0
        Me.Label36.Text = "Taille Code CNAM :"
        '
        'tTailleCodeCNAM
        '
        Me.tTailleCodeCNAM.Location = New System.Drawing.Point(124, 35)
        Me.tTailleCodeCNAM.Name = "tTailleCodeCNAM"
        Me.tTailleCodeCNAM.Size = New System.Drawing.Size(55, 20)
        Me.tTailleCodeCNAM.TabIndex = 1
        Me.tTailleCodeCNAM.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'C1DockingTabPage12
        '
        Me.C1DockingTabPage12.Controls.Add(Me.cmbMeme)
        Me.C1DockingTabPage12.Controls.Add(Me.Label54)
        Me.C1DockingTabPage12.Controls.Add(Me.GroupBox7)
        Me.C1DockingTabPage12.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage12.Name = "C1DockingTabPage12"
        Me.C1DockingTabPage12.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage12.TabIndex = 11
        Me.C1DockingTabPage12.Text = "Style Caractère"
        '
        'cmbMeme
        '
        Me.cmbMeme.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbMeme.AutoSize = False
        Me.cmbMeme.Caption = ""
        Me.cmbMeme.CaptionVisible = False
        Me.cmbMeme.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbMeme.ContentHeight = 12
        Me.cmbMeme.DataMode = C1.Win.C1List.DataModeEnum.AddItem
        Me.cmbMeme.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbMeme.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbMeme.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbMeme.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbMeme.Images.Add(CType(resources.GetObject("cmbMeme.Images"), System.Drawing.Image))
        Me.cmbMeme.Location = New System.Drawing.Point(328, 183)
        Me.cmbMeme.MatchEntryTimeout = CType(2000, Long)
        Me.cmbMeme.MaxDropDownItems = CType(5, Short)
        Me.cmbMeme.MaxLength = 32767
        Me.cmbMeme.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbMeme.Name = "cmbMeme"
        Me.cmbMeme.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbMeme.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbMeme.Size = New System.Drawing.Size(93, 18)
        Me.cmbMeme.TabIndex = 101
        Me.cmbMeme.Visible = False
        Me.cmbMeme.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbMeme.PropBag = resources.GetString("cmbMeme.PropBag")
        '
        'Label54
        '
        Me.Label54.AutoSize = True
        Me.Label54.Location = New System.Drawing.Point(14, 188)
        Me.Label54.Name = "Label54"
        Me.Label54.Size = New System.Drawing.Size(286, 13)
        Me.Label54.TabIndex = 8
        Me.Label54.Text = "Appliquer le meme style du grid pour l'ajout et la consultaion"
        Me.Label54.Visible = False
        '
        'GroupBox7
        '
        Me.GroupBox7.Controls.Add(Me.Label43)
        Me.GroupBox7.Controls.Add(Me.cmbPolice)
        Me.GroupBox7.Controls.Add(Me.cmbCaractere)
        Me.GroupBox7.Controls.Add(Me.cmbListe)
        Me.GroupBox7.Controls.Add(Me.Label41)
        Me.GroupBox7.Controls.Add(Me.Label40)
        Me.GroupBox7.Location = New System.Drawing.Point(7, 6)
        Me.GroupBox7.Name = "GroupBox7"
        Me.GroupBox7.Size = New System.Drawing.Size(388, 127)
        Me.GroupBox7.TabIndex = 2
        Me.GroupBox7.TabStop = False
        Me.GroupBox7.Text = "Style Caractères"
        '
        'Label43
        '
        Me.Label43.AutoSize = True
        Me.Label43.Location = New System.Drawing.Point(6, 91)
        Me.Label43.Name = "Label43"
        Me.Label43.Size = New System.Drawing.Size(203, 13)
        Me.Label43.TabIndex = 7
        Me.Label43.Text = "La Police des Caractères de mes Tables :"
        '
        'cmbPolice
        '
        Me.cmbPolice.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.cmbPolice.Location = New System.Drawing.Point(215, 88)
        Me.cmbPolice.Name = "cmbPolice"
        Me.cmbPolice.Size = New System.Drawing.Size(159, 21)
        Me.cmbPolice.TabIndex = 6
        '
        'cmbCaractere
        '
        Me.cmbCaractere.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.cmbCaractere.FormattingEnabled = True
        Me.cmbCaractere.Items.AddRange(New Object() {"8", "9", "10", "11", "12", "13", "14", "15", "16"})
        Me.cmbCaractere.Location = New System.Drawing.Point(215, 59)
        Me.cmbCaractere.Name = "cmbCaractere"
        Me.cmbCaractere.Size = New System.Drawing.Size(44, 21)
        Me.cmbCaractere.TabIndex = 5
        '
        'cmbListe
        '
        Me.cmbListe.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.cmbListe.Items.AddRange(New Object() {"15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30"})
        Me.cmbListe.Location = New System.Drawing.Point(215, 28)
        Me.cmbListe.Name = "cmbListe"
        Me.cmbListe.Size = New System.Drawing.Size(44, 21)
        Me.cmbListe.TabIndex = 4
        '
        'Label41
        '
        Me.Label41.AutoSize = True
        Me.Label41.Location = New System.Drawing.Point(7, 62)
        Me.Label41.Name = "Label41"
        Me.Label41.Size = New System.Drawing.Size(202, 13)
        Me.Label41.TabIndex = 1
        Me.Label41.Text = " La Taille des Caractères de mes Tables :"
        '
        'Label40
        '
        Me.Label40.AutoSize = True
        Me.Label40.Location = New System.Drawing.Point(25, 31)
        Me.Label40.Name = "Label40"
        Me.Label40.Size = New System.Drawing.Size(184, 13)
        Me.Label40.TabIndex = 1
        Me.Label40.Text = "La Taille des Cellules de mes Tables :"
        '
        'C1DockingTabPage13
        '
        Me.C1DockingTabPage13.Controls.Add(Me.GroupBox10)
        Me.C1DockingTabPage13.Controls.Add(Me.GroupBoxTerminal)
        Me.C1DockingTabPage13.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage13.Name = "C1DockingTabPage13"
        Me.C1DockingTabPage13.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage13.TabIndex = 12
        Me.C1DockingTabPage13.Text = "Paramètres Terminal"
        '
        'GroupBox10
        '
        Me.GroupBox10.Controls.Add(Me.chReseau)
        Me.GroupBox10.Controls.Add(Me.chFichierTexte)
        Me.GroupBox10.Controls.Add(Me.chPortSerie)
        Me.GroupBox10.Location = New System.Drawing.Point(8, 15)
        Me.GroupBox10.Name = "GroupBox10"
        Me.GroupBox10.Size = New System.Drawing.Size(378, 58)
        Me.GroupBox10.TabIndex = 111
        Me.GroupBox10.TabStop = False
        Me.GroupBox10.Text = "Type"
        '
        'chReseau
        '
        Me.chReseau.AutoSize = True
        Me.chReseau.Location = New System.Drawing.Point(250, 23)
        Me.chReseau.Name = "chReseau"
        Me.chReseau.Size = New System.Drawing.Size(95, 17)
        Me.chReseau.TabIndex = 6
        Me.chReseau.TabStop = True
        Me.chReseau.Text = "Réseau / USB"
        Me.chReseau.UseVisualStyleBackColor = True
        '
        'chFichierTexte
        '
        Me.chFichierTexte.AutoSize = True
        Me.chFichierTexte.Location = New System.Drawing.Point(144, 23)
        Me.chFichierTexte.Name = "chFichierTexte"
        Me.chFichierTexte.Size = New System.Drawing.Size(82, 17)
        Me.chFichierTexte.TabIndex = 5
        Me.chFichierTexte.TabStop = True
        Me.chFichierTexte.Text = "Fichier texte"
        Me.chFichierTexte.UseVisualStyleBackColor = True
        '
        'chPortSerie
        '
        Me.chPortSerie.AutoSize = True
        Me.chPortSerie.Location = New System.Drawing.Point(51, 23)
        Me.chPortSerie.Name = "chPortSerie"
        Me.chPortSerie.Size = New System.Drawing.Size(69, 17)
        Me.chPortSerie.TabIndex = 4
        Me.chPortSerie.TabStop = True
        Me.chPortSerie.Text = "Port série"
        Me.chPortSerie.UseVisualStyleBackColor = True
        '
        'GroupBoxTerminal
        '
        Me.GroupBoxTerminal.Controls.Add(Me.Label46)
        Me.GroupBoxTerminal.Controls.Add(Me.cmbProtocole)
        Me.GroupBoxTerminal.Controls.Add(Me.Label47)
        Me.GroupBoxTerminal.Controls.Add(Me.tNomPort)
        Me.GroupBoxTerminal.Controls.Add(Me.cmbDataBit)
        Me.GroupBoxTerminal.Controls.Add(Me.cmbParity)
        Me.GroupBoxTerminal.Controls.Add(Me.Label48)
        Me.GroupBoxTerminal.Controls.Add(Me.cmbStopBit)
        Me.GroupBoxTerminal.Controls.Add(Me.Label49)
        Me.GroupBoxTerminal.Controls.Add(Me.cmbVitesse)
        Me.GroupBoxTerminal.Controls.Add(Me.Label50)
        Me.GroupBoxTerminal.Controls.Add(Me.Label51)
        Me.GroupBoxTerminal.Enabled = False
        Me.GroupBoxTerminal.Location = New System.Drawing.Point(8, 84)
        Me.GroupBoxTerminal.Name = "GroupBoxTerminal"
        Me.GroupBoxTerminal.Size = New System.Drawing.Size(378, 204)
        Me.GroupBoxTerminal.TabIndex = 3
        Me.GroupBoxTerminal.TabStop = False
        Me.GroupBoxTerminal.Text = "Paramètres de Terminal"
        '
        'Label46
        '
        Me.Label46.AutoSize = True
        Me.Label46.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Label46.Location = New System.Drawing.Point(62, 165)
        Me.Label46.Name = "Label46"
        Me.Label46.Size = New System.Drawing.Size(52, 13)
        Me.Label46.TabIndex = 111
        Me.Label46.Text = "Protocole"
        '
        'cmbProtocole
        '
        Me.cmbProtocole.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbProtocole.AutoSize = False
        Me.cmbProtocole.Caption = ""
        Me.cmbProtocole.CaptionVisible = False
        Me.cmbProtocole.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbProtocole.ContentHeight = 12
        Me.cmbProtocole.DataMode = C1.Win.C1List.DataModeEnum.AddItem
        Me.cmbProtocole.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbProtocole.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbProtocole.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbProtocole.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbProtocole.Images.Add(CType(resources.GetObject("cmbProtocole.Images"), System.Drawing.Image))
        Me.cmbProtocole.Location = New System.Drawing.Point(120, 162)
        Me.cmbProtocole.MatchEntryTimeout = CType(2000, Long)
        Me.cmbProtocole.MaxDropDownItems = CType(5, Short)
        Me.cmbProtocole.MaxLength = 32767
        Me.cmbProtocole.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbProtocole.Name = "cmbProtocole"
        Me.cmbProtocole.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbProtocole.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbProtocole.Size = New System.Drawing.Size(206, 18)
        Me.cmbProtocole.TabIndex = 110
        Me.cmbProtocole.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbProtocole.PropBag = resources.GetString("cmbProtocole.PropBag")
        '
        'Label47
        '
        Me.Label47.AutoSize = True
        Me.Label47.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Label47.Location = New System.Drawing.Point(48, 41)
        Me.Label47.Name = "Label47"
        Me.Label47.Size = New System.Drawing.Size(66, 13)
        Me.Label47.TabIndex = 109
        Me.Label47.Text = "Nom du Port"
        '
        'tNomPort
        '
        Me.tNomPort.AutoSize = False
        Me.tNomPort.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNomPort.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNomPort.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNomPort.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tNomPort.Location = New System.Drawing.Point(120, 39)
        Me.tNomPort.Name = "tNomPort"
        Me.tNomPort.Size = New System.Drawing.Size(146, 19)
        Me.tNomPort.TabIndex = 108
        Me.tNomPort.Tag = Nothing
        Me.tNomPort.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbDataBit
        '
        Me.cmbDataBit.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbDataBit.AutoSize = False
        Me.cmbDataBit.Caption = ""
        Me.cmbDataBit.CaptionVisible = False
        Me.cmbDataBit.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbDataBit.ContentHeight = 12
        Me.cmbDataBit.DataMode = C1.Win.C1List.DataModeEnum.AddItem
        Me.cmbDataBit.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbDataBit.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbDataBit.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbDataBit.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbDataBit.Images.Add(CType(resources.GetObject("cmbDataBit.Images"), System.Drawing.Image))
        Me.cmbDataBit.Location = New System.Drawing.Point(120, 88)
        Me.cmbDataBit.MatchEntryTimeout = CType(2000, Long)
        Me.cmbDataBit.MaxDropDownItems = CType(5, Short)
        Me.cmbDataBit.MaxLength = 32767
        Me.cmbDataBit.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbDataBit.Name = "cmbDataBit"
        Me.cmbDataBit.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbDataBit.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbDataBit.Size = New System.Drawing.Size(206, 18)
        Me.cmbDataBit.TabIndex = 101
        Me.cmbDataBit.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbDataBit.PropBag = resources.GetString("cmbDataBit.PropBag")
        '
        'cmbParity
        '
        Me.cmbParity.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbParity.AutoSize = False
        Me.cmbParity.Caption = ""
        Me.cmbParity.CaptionVisible = False
        Me.cmbParity.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbParity.ContentHeight = 12
        Me.cmbParity.DataMode = C1.Win.C1List.DataModeEnum.AddItem
        Me.cmbParity.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbParity.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbParity.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbParity.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbParity.Images.Add(CType(resources.GetObject("cmbParity.Images"), System.Drawing.Image))
        Me.cmbParity.Location = New System.Drawing.Point(120, 138)
        Me.cmbParity.MatchEntryTimeout = CType(2000, Long)
        Me.cmbParity.MaxDropDownItems = CType(5, Short)
        Me.cmbParity.MaxLength = 32767
        Me.cmbParity.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbParity.Name = "cmbParity"
        Me.cmbParity.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbParity.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbParity.Size = New System.Drawing.Size(206, 18)
        Me.cmbParity.TabIndex = 102
        Me.cmbParity.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbParity.PropBag = resources.GetString("cmbParity.PropBag")
        '
        'Label48
        '
        Me.Label48.AutoSize = True
        Me.Label48.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Label48.Location = New System.Drawing.Point(36, 91)
        Me.Label48.Name = "Label48"
        Me.Label48.Size = New System.Drawing.Size(78, 13)
        Me.Label48.TabIndex = 105
        Me.Label48.Text = "Bit de données"
        '
        'cmbStopBit
        '
        Me.cmbStopBit.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbStopBit.AutoSize = False
        Me.cmbStopBit.Caption = ""
        Me.cmbStopBit.CaptionVisible = False
        Me.cmbStopBit.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbStopBit.ContentHeight = 12
        Me.cmbStopBit.DataMode = C1.Win.C1List.DataModeEnum.AddItem
        Me.cmbStopBit.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbStopBit.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbStopBit.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbStopBit.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbStopBit.Images.Add(CType(resources.GetObject("cmbStopBit.Images"), System.Drawing.Image))
        Me.cmbStopBit.Location = New System.Drawing.Point(120, 112)
        Me.cmbStopBit.MatchEntryTimeout = CType(2000, Long)
        Me.cmbStopBit.MaxDropDownItems = CType(5, Short)
        Me.cmbStopBit.MaxLength = 32767
        Me.cmbStopBit.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbStopBit.Name = "cmbStopBit"
        Me.cmbStopBit.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbStopBit.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbStopBit.Size = New System.Drawing.Size(206, 18)
        Me.cmbStopBit.TabIndex = 104
        Me.cmbStopBit.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbStopBit.PropBag = resources.GetString("cmbStopBit.PropBag")
        '
        'Label49
        '
        Me.Label49.AutoSize = True
        Me.Label49.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Label49.Location = New System.Drawing.Point(73, 66)
        Me.Label49.Name = "Label49"
        Me.Label49.Size = New System.Drawing.Size(41, 13)
        Me.Label49.TabIndex = 103
        Me.Label49.Text = "Vitesse"
        '
        'cmbVitesse
        '
        Me.cmbVitesse.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbVitesse.AutoSize = False
        Me.cmbVitesse.Caption = ""
        Me.cmbVitesse.CaptionVisible = False
        Me.cmbVitesse.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbVitesse.ContentHeight = 12
        Me.cmbVitesse.DataMode = C1.Win.C1List.DataModeEnum.AddItem
        Me.cmbVitesse.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbVitesse.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbVitesse.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbVitesse.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbVitesse.Images.Add(CType(resources.GetObject("cmbVitesse.Images"), System.Drawing.Image))
        Me.cmbVitesse.Location = New System.Drawing.Point(120, 64)
        Me.cmbVitesse.MatchEntryTimeout = CType(2000, Long)
        Me.cmbVitesse.MaxDropDownItems = CType(5, Short)
        Me.cmbVitesse.MaxLength = 32767
        Me.cmbVitesse.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbVitesse.Name = "cmbVitesse"
        Me.cmbVitesse.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbVitesse.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbVitesse.Size = New System.Drawing.Size(206, 18)
        Me.cmbVitesse.TabIndex = 100
        Me.cmbVitesse.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbVitesse.PropBag = resources.GetString("cmbVitesse.PropBag")
        '
        'Label50
        '
        Me.Label50.AutoSize = True
        Me.Label50.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Label50.Location = New System.Drawing.Point(63, 115)
        Me.Label50.Name = "Label50"
        Me.Label50.Size = New System.Drawing.Size(51, 13)
        Me.Label50.TabIndex = 106
        Me.Label50.Text = "Bit d'arrêt"
        '
        'Label51
        '
        Me.Label51.AutoSize = True
        Me.Label51.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Label51.Location = New System.Drawing.Point(80, 141)
        Me.Label51.Name = "Label51"
        Me.Label51.Size = New System.Drawing.Size(34, 13)
        Me.Label51.TabIndex = 107
        Me.Label51.Text = "Parité"
        '
        'C1DockingTabPage14
        '
        Me.C1DockingTabPage14.Controls.Add(Me.cmbFormatExcel)
        Me.C1DockingTabPage14.Controls.Add(Me.Label55)
        Me.C1DockingTabPage14.Controls.Add(Me.Label56)
        Me.C1DockingTabPage14.Controls.Add(Me.tDossierExcel)
        Me.C1DockingTabPage14.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage14.Name = "C1DockingTabPage14"
        Me.C1DockingTabPage14.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage14.TabIndex = 13
        Me.C1DockingTabPage14.Text = "Exportation Excel"
        '
        'cmbFormatExcel
        '
        Me.cmbFormatExcel.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbFormatExcel.AutoSize = False
        Me.cmbFormatExcel.Caption = ""
        Me.cmbFormatExcel.CaptionVisible = False
        Me.cmbFormatExcel.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbFormatExcel.ContentHeight = 12
        Me.cmbFormatExcel.DataMode = C1.Win.C1List.DataModeEnum.AddItem
        Me.cmbFormatExcel.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbFormatExcel.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbFormatExcel.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbFormatExcel.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbFormatExcel.Images.Add(CType(resources.GetObject("cmbFormatExcel.Images"), System.Drawing.Image))
        Me.cmbFormatExcel.Location = New System.Drawing.Point(180, 84)
        Me.cmbFormatExcel.MatchEntryTimeout = CType(2000, Long)
        Me.cmbFormatExcel.MaxDropDownItems = CType(5, Short)
        Me.cmbFormatExcel.MaxLength = 32767
        Me.cmbFormatExcel.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbFormatExcel.Name = "cmbFormatExcel"
        Me.cmbFormatExcel.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbFormatExcel.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbFormatExcel.Size = New System.Drawing.Size(206, 18)
        Me.cmbFormatExcel.TabIndex = 106
        Me.cmbFormatExcel.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbFormatExcel.PropBag = resources.GetString("cmbFormatExcel.PropBag")
        '
        'Label55
        '
        Me.Label55.AutoSize = True
        Me.Label55.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Label55.Location = New System.Drawing.Point(36, 89)
        Me.Label55.Name = "Label55"
        Me.Label55.Size = New System.Drawing.Size(76, 13)
        Me.Label55.TabIndex = 105
        Me.Label55.Text = "Format d'Excel"
        '
        'Label56
        '
        Me.Label56.AutoSize = True
        Me.Label56.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Label56.Location = New System.Drawing.Point(36, 49)
        Me.Label56.Name = "Label56"
        Me.Label56.Size = New System.Drawing.Size(112, 13)
        Me.Label56.TabIndex = 104
        Me.Label56.Text = "Dossier d'Export Excel"
        '
        'tDossierExcel
        '
        Me.tDossierExcel.AutoSize = False
        Me.tDossierExcel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDossierExcel.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tDossierExcel.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tDossierExcel.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tDossierExcel.Location = New System.Drawing.Point(180, 43)
        Me.tDossierExcel.Name = "tDossierExcel"
        Me.tDossierExcel.Size = New System.Drawing.Size(357, 19)
        Me.tDossierExcel.TabIndex = 103
        Me.tDossierExcel.Tag = Nothing
        Me.tDossierExcel.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage16
        '
        Me.C1DockingTabPage16.Controls.Add(Me.GroupBox9)
        Me.C1DockingTabPage16.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage16.Name = "C1DockingTabPage16"
        Me.C1DockingTabPage16.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage16.TabIndex = 15
        Me.C1DockingTabPage16.Text = "Surveillance"
        '
        'GroupBox9
        '
        Me.GroupBox9.Controls.Add(Me.Label61)
        Me.GroupBox9.Controls.Add(Me.Label60)
        Me.GroupBox9.Controls.Add(Me.Label59)
        Me.GroupBox9.Controls.Add(Me.Label58)
        Me.GroupBox9.Controls.Add(Me.Label57)
        Me.GroupBox9.Controls.Add(Me.ChbModuleArticle5)
        Me.GroupBox9.Controls.Add(Me.ChbModuleClient1)
        Me.GroupBox9.Controls.Add(Me.ChbModuleClient2)
        Me.GroupBox9.Controls.Add(Me.ChbModuleClient3)
        Me.GroupBox9.Controls.Add(Me.ChbModuleArticle4)
        Me.GroupBox9.Controls.Add(Me.ChbModuleArticle1)
        Me.GroupBox9.Controls.Add(Me.ChbModuleArticle2)
        Me.GroupBox9.Controls.Add(Me.ChbModuleAchat2)
        Me.GroupBox9.Controls.Add(Me.ChbModuleReglement1)
        Me.GroupBox9.Controls.Add(Me.ChbModuleVente3)
        Me.GroupBox9.Controls.Add(Me.ChbModuleVente1)
        Me.GroupBox9.Controls.Add(Me.ChbModuleVente2)
        Me.GroupBox9.Controls.Add(Me.ChbModuleVente4)
        Me.GroupBox9.Controls.Add(Me.ChbModuleAchat1)
        Me.GroupBox9.Controls.Add(Me.ChbModuleArticle3)
        Me.GroupBox9.Location = New System.Drawing.Point(8, 28)
        Me.GroupBox9.Name = "GroupBox9"
        Me.GroupBox9.Size = New System.Drawing.Size(1031, 329)
        Me.GroupBox9.TabIndex = 80
        Me.GroupBox9.TabStop = False
        Me.GroupBox9.Text = "Liste des modules à surveiller."
        '
        'Label61
        '
        Me.Label61.AutoSize = True
        Me.Label61.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label61.ForeColor = System.Drawing.Color.MidnightBlue
        Me.Label61.Location = New System.Drawing.Point(816, 51)
        Me.Label61.Name = "Label61"
        Me.Label61.Size = New System.Drawing.Size(88, 18)
        Me.Label61.TabIndex = 100
        Me.Label61.Text = "Réglement"
        '
        'Label60
        '
        Me.Label60.AutoSize = True
        Me.Label60.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label60.ForeColor = System.Drawing.Color.MidnightBlue
        Me.Label60.Location = New System.Drawing.Point(438, 51)
        Me.Label60.Name = "Label60"
        Me.Label60.Size = New System.Drawing.Size(50, 18)
        Me.Label60.TabIndex = 99
        Me.Label60.Text = "Vente"
        '
        'Label59
        '
        Me.Label59.AutoSize = True
        Me.Label59.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label59.ForeColor = System.Drawing.Color.MidnightBlue
        Me.Label59.Location = New System.Drawing.Point(438, 221)
        Me.Label59.Name = "Label59"
        Me.Label59.Size = New System.Drawing.Size(50, 18)
        Me.Label59.TabIndex = 98
        Me.Label59.Text = "Achat"
        '
        'Label58
        '
        Me.Label58.AutoSize = True
        Me.Label58.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label58.ForeColor = System.Drawing.Color.MidnightBlue
        Me.Label58.Location = New System.Drawing.Point(38, 221)
        Me.Label58.Name = "Label58"
        Me.Label58.Size = New System.Drawing.Size(51, 18)
        Me.Label58.TabIndex = 97
        Me.Label58.Text = "Client"
        '
        'Label57
        '
        Me.Label57.AutoSize = True
        Me.Label57.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label57.ForeColor = System.Drawing.Color.MidnightBlue
        Me.Label57.Location = New System.Drawing.Point(38, 51)
        Me.Label57.Name = "Label57"
        Me.Label57.Size = New System.Drawing.Size(55, 18)
        Me.Label57.TabIndex = 96
        Me.Label57.Text = "Article"
        '
        'ChbModuleArticle5
        '
        Me.ChbModuleArticle5.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleArticle5.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleArticle5.Location = New System.Drawing.Point(41, 172)
        Me.ChbModuleArticle5.Name = "ChbModuleArticle5"
        Me.ChbModuleArticle5.Size = New System.Drawing.Size(394, 19)
        Me.ChbModuleArticle5.TabIndex = 95
        Me.ChbModuleArticle5.Text = "Vente des articles à surveiller"
        Me.ChbModuleArticle5.UseVisualStyleBackColor = False
        '
        'ChbModuleClient1
        '
        Me.ChbModuleClient1.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleClient1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleClient1.Location = New System.Drawing.Point(41, 242)
        Me.ChbModuleClient1.Name = "ChbModuleClient1"
        Me.ChbModuleClient1.Size = New System.Drawing.Size(394, 19)
        Me.ChbModuleClient1.TabIndex = 94
        Me.ChbModuleClient1.Text = "Ajout des clients"
        Me.ChbModuleClient1.UseVisualStyleBackColor = False
        '
        'ChbModuleClient2
        '
        Me.ChbModuleClient2.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleClient2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleClient2.Location = New System.Drawing.Point(41, 267)
        Me.ChbModuleClient2.Name = "ChbModuleClient2"
        Me.ChbModuleClient2.Size = New System.Drawing.Size(394, 19)
        Me.ChbModuleClient2.TabIndex = 93
        Me.ChbModuleClient2.Text = "Modification des clients"
        Me.ChbModuleClient2.UseVisualStyleBackColor = False
        '
        'ChbModuleClient3
        '
        Me.ChbModuleClient3.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleClient3.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleClient3.Location = New System.Drawing.Point(41, 292)
        Me.ChbModuleClient3.Name = "ChbModuleClient3"
        Me.ChbModuleClient3.Size = New System.Drawing.Size(394, 19)
        Me.ChbModuleClient3.TabIndex = 91
        Me.ChbModuleClient3.Text = "Suppression des clients"
        Me.ChbModuleClient3.UseVisualStyleBackColor = False
        '
        'ChbModuleArticle4
        '
        Me.ChbModuleArticle4.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleArticle4.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleArticle4.Location = New System.Drawing.Point(41, 147)
        Me.ChbModuleArticle4.Name = "ChbModuleArticle4"
        Me.ChbModuleArticle4.Size = New System.Drawing.Size(394, 19)
        Me.ChbModuleArticle4.TabIndex = 90
        Me.ChbModuleArticle4.Text = "Changement des prix des articles"
        Me.ChbModuleArticle4.UseVisualStyleBackColor = False
        '
        'ChbModuleArticle1
        '
        Me.ChbModuleArticle1.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleArticle1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleArticle1.Location = New System.Drawing.Point(41, 72)
        Me.ChbModuleArticle1.Name = "ChbModuleArticle1"
        Me.ChbModuleArticle1.Size = New System.Drawing.Size(394, 19)
        Me.ChbModuleArticle1.TabIndex = 89
        Me.ChbModuleArticle1.Text = "Ajout des articles"
        Me.ChbModuleArticle1.UseVisualStyleBackColor = False
        '
        'ChbModuleArticle2
        '
        Me.ChbModuleArticle2.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleArticle2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleArticle2.Location = New System.Drawing.Point(41, 97)
        Me.ChbModuleArticle2.Name = "ChbModuleArticle2"
        Me.ChbModuleArticle2.Size = New System.Drawing.Size(394, 19)
        Me.ChbModuleArticle2.TabIndex = 88
        Me.ChbModuleArticle2.Text = "Modification des articles"
        Me.ChbModuleArticle2.UseVisualStyleBackColor = False
        '
        'ChbModuleAchat2
        '
        Me.ChbModuleAchat2.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleAchat2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleAchat2.Location = New System.Drawing.Point(441, 267)
        Me.ChbModuleAchat2.Name = "ChbModuleAchat2"
        Me.ChbModuleAchat2.Size = New System.Drawing.Size(372, 19)
        Me.ChbModuleAchat2.TabIndex = 87
        Me.ChbModuleAchat2.Text = "Suppression des achats"
        Me.ChbModuleAchat2.UseVisualStyleBackColor = False
        '
        'ChbModuleReglement1
        '
        Me.ChbModuleReglement1.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleReglement1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleReglement1.Location = New System.Drawing.Point(819, 72)
        Me.ChbModuleReglement1.Name = "ChbModuleReglement1"
        Me.ChbModuleReglement1.Size = New System.Drawing.Size(161, 19)
        Me.ChbModuleReglement1.TabIndex = 86
        Me.ChbModuleReglement1.Text = "Règlement avec remise"
        Me.ChbModuleReglement1.UseVisualStyleBackColor = False
        '
        'ChbModuleVente3
        '
        Me.ChbModuleVente3.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleVente3.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleVente3.Location = New System.Drawing.Point(441, 122)
        Me.ChbModuleVente3.Name = "ChbModuleVente3"
        Me.ChbModuleVente3.Size = New System.Drawing.Size(372, 19)
        Me.ChbModuleVente3.TabIndex = 85
        Me.ChbModuleVente3.Text = "Ventes à crédit"
        Me.ChbModuleVente3.UseVisualStyleBackColor = False
        '
        'ChbModuleVente1
        '
        Me.ChbModuleVente1.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleVente1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleVente1.Location = New System.Drawing.Point(441, 72)
        Me.ChbModuleVente1.Name = "ChbModuleVente1"
        Me.ChbModuleVente1.Size = New System.Drawing.Size(372, 19)
        Me.ChbModuleVente1.TabIndex = 84
        Me.ChbModuleVente1.Text = "Ventes négative"
        Me.ChbModuleVente1.UseVisualStyleBackColor = False
        '
        'ChbModuleVente2
        '
        Me.ChbModuleVente2.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleVente2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleVente2.Location = New System.Drawing.Point(441, 97)
        Me.ChbModuleVente2.Name = "ChbModuleVente2"
        Me.ChbModuleVente2.Size = New System.Drawing.Size(372, 19)
        Me.ChbModuleVente2.TabIndex = 83
        Me.ChbModuleVente2.Text = "Remise à la vente"
        Me.ChbModuleVente2.UseVisualStyleBackColor = False
        '
        'ChbModuleVente4
        '
        Me.ChbModuleVente4.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleVente4.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleVente4.Location = New System.Drawing.Point(441, 147)
        Me.ChbModuleVente4.Name = "ChbModuleVente4"
        Me.ChbModuleVente4.Size = New System.Drawing.Size(372, 19)
        Me.ChbModuleVente4.TabIndex = 82
        Me.ChbModuleVente4.Text = "Suppression des ventes"
        Me.ChbModuleVente4.UseVisualStyleBackColor = False
        '
        'ChbModuleAchat1
        '
        Me.ChbModuleAchat1.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleAchat1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleAchat1.Location = New System.Drawing.Point(441, 242)
        Me.ChbModuleAchat1.Name = "ChbModuleAchat1"
        Me.ChbModuleAchat1.Size = New System.Drawing.Size(372, 19)
        Me.ChbModuleAchat1.TabIndex = 81
        Me.ChbModuleAchat1.Text = "Modification des achats"
        Me.ChbModuleAchat1.UseVisualStyleBackColor = False
        '
        'ChbModuleArticle3
        '
        Me.ChbModuleArticle3.BackColor = System.Drawing.Color.Transparent
        Me.ChbModuleArticle3.ForeColor = System.Drawing.SystemColors.ControlText
        Me.ChbModuleArticle3.Location = New System.Drawing.Point(41, 122)
        Me.ChbModuleArticle3.Name = "ChbModuleArticle3"
        Me.ChbModuleArticle3.Size = New System.Drawing.Size(394, 19)
        Me.ChbModuleArticle3.TabIndex = 80
        Me.ChbModuleArticle3.Text = "Suppression des articles"
        Me.ChbModuleArticle3.UseVisualStyleBackColor = False
        '
        'C1DockingTabPage15
        '
        Me.C1DockingTabPage15.Controls.Add(Me.FTP)
        Me.C1DockingTabPage15.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage15.Name = "C1DockingTabPage15"
        Me.C1DockingTabPage15.Size = New System.Drawing.Size(1052, 370)
        Me.C1DockingTabPage15.TabIndex = 16
        Me.C1DockingTabPage15.Text = "serveur update"
        '
        'label
        '
        Me.label.AutoSize = True
        Me.label.Location = New System.Drawing.Point(56, 46)
        Me.label.Name = "label"
        Me.label.Size = New System.Drawing.Size(60, 13)
        Me.label.TabIndex = 0
        Me.label.Text = "Host Name"
        '
        'Label70
        '
        Me.Label70.AutoSize = True
        Me.Label70.Location = New System.Drawing.Point(56, 76)
        Me.Label70.Name = "Label70"
        Me.Label70.Size = New System.Drawing.Size(60, 13)
        Me.Label70.TabIndex = 1
        Me.Label70.Text = "User Name"
        '
        'Label71
        '
        Me.Label71.AutoSize = True
        Me.Label71.Location = New System.Drawing.Point(56, 107)
        Me.Label71.Name = "Label71"
        Me.Label71.Size = New System.Drawing.Size(53, 13)
        Me.Label71.TabIndex = 2
        Me.Label71.Text = "Password"
        '
        'Password
        '
        Me.Password.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.Password.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Password.Location = New System.Drawing.Point(145, 107)
        Me.Password.Name = "Password"
        Me.Password.PasswordChar = Global.Microsoft.VisualBasic.ChrW(42)
        Me.Password.Size = New System.Drawing.Size(173, 18)
        Me.Password.TabIndex = 5
        Me.Password.Tag = Nothing
        Me.Password.Value = ""
        Me.Password.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.Password.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'hostname
        '
        Me.hostname.AutoSize = False
        Me.hostname.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.hostname.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.hostname.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.hostname.Location = New System.Drawing.Point(145, 44)
        Me.hostname.Name = "hostname"
        Me.hostname.Size = New System.Drawing.Size(173, 23)
        Me.hostname.TabIndex = 100
        Me.hostname.Tag = Nothing
        Me.hostname.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.hostname.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'UserName
        '
        Me.UserName.AutoSize = False
        Me.UserName.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.UserName.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.UserName.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.UserName.Location = New System.Drawing.Point(145, 74)
        Me.UserName.Name = "UserName"
        Me.UserName.Size = New System.Drawing.Size(173, 23)
        Me.UserName.TabIndex = 101
        Me.UserName.Tag = Nothing
        Me.UserName.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.UserName.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'FTP
        '
        Me.FTP.Controls.Add(Me.hostname)
        Me.FTP.Controls.Add(Me.UserName)
        Me.FTP.Controls.Add(Me.label)
        Me.FTP.Controls.Add(Me.Label70)
        Me.FTP.Controls.Add(Me.Password)
        Me.FTP.Controls.Add(Me.Label71)
        Me.FTP.Location = New System.Drawing.Point(62, 38)
        Me.FTP.Name = "FTP"
        Me.FTP.Size = New System.Drawing.Size(419, 171)
        Me.FTP.TabIndex = 102
        Me.FTP.TabStop = False
        Me.FTP.Text = "FTP"
        '
        'fParametresGeneraux
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1186, 489)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "fParametresGeneraux"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        CType(Me.Tab, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Tab.ResumeLayout(False)
        Me.C1DockingTabPage1.ResumeLayout(False)
        Me.C1DockingTabPage1.PerformLayout()
        CType(Me.tMail, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tTexte, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox8.ResumeLayout(False)
        Me.GroupBox8.PerformLayout()
        CType(Me.tMessagederoulant1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tMessagederoulant2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpDateMigration, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tRib, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tTimbre, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodeTva, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tFax, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tTelephone, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tAdresse, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNumeroAffiliation2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNumeroAffiliation1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCNAM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tPharmacie, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodePharmacien, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage2.ResumeLayout(False)
        Me.GroupBox2.ResumeLayout(False)
        CType(Me.tMinimumdePerception, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        CType(Me.tHonoraireTableauC, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tHonoraireTableauB, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tHonoraireTableauA, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage3.ResumeLayout(False)
        CType(Me.tNomOrdinateurImpressionCodeABarre, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage4.ResumeLayout(False)
        Me.C1DockingTabPage4.PerformLayout()
        Me.GroupBox11.ResumeLayout(False)
        Me.GroupBox11.PerformLayout()
        CType(Me.tNePasSortirManquantsDepuis, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNbrCommande, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox4.ResumeLayout(False)
        CType(Me.dtpfinAnneeProchaine, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpDebutAnneeProchaine, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpFinAnneeCourant, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpDebutAnneeCourant, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCommandeGroupeJ, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage5.ResumeLayout(False)
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        CType(Me.tOrdonnancier, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tBlDevis, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tFacture, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage6.ResumeLayout(False)
        Me.C1DockingTabPage6.PerformLayout()
        CType(Me.tNbrAppareillage, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNbrPriseEnCharge, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNbrOrdonnance, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tRemise, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage7.ResumeLayout(False)
        Me.gEnvoiLog.ResumeLayout(False)
        CType(Me.pbTestEnvoiFichierLog, System.ComponentModel.ISupportInitialize).EndInit()
        Me.gPramatresMail.ResumeLayout(False)
        Me.gPramatresMail.PerformLayout()
        CType(Me.pbTestEnvoiEmail, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tSMTPMail, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tPortMail, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tMotDePasseMail, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tAdresseMail, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage8.ResumeLayout(False)
        Me.GroupBox5.ResumeLayout(False)
        Me.GroupBox5.PerformLayout()
        Me.C1DockingTabPage9.ResumeLayout(False)
        Me.C1DockingTabPage9.PerformLayout()
        Me.GBProduitSurveiller.ResumeLayout(False)
        Me.GBProduitSurveiller.PerformLayout()
        CType(Me.tCode, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbArticle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gArticle, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage10.ResumeLayout(False)
        Me.C1DockingTabPage10.PerformLayout()
        CType(Me.tLatLong, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage11.ResumeLayout(False)
        Me.GroupBox6.ResumeLayout(False)
        Me.GroupBox6.PerformLayout()
        Me.C1DockingTabPage12.ResumeLayout(False)
        Me.C1DockingTabPage12.PerformLayout()
        CType(Me.cmbMeme, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox7.ResumeLayout(False)
        Me.GroupBox7.PerformLayout()
        Me.C1DockingTabPage13.ResumeLayout(False)
        Me.GroupBox10.ResumeLayout(False)
        Me.GroupBox10.PerformLayout()
        Me.GroupBoxTerminal.ResumeLayout(False)
        Me.GroupBoxTerminal.PerformLayout()
        CType(Me.cmbProtocole, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNomPort, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbDataBit, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbParity, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbStopBit, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbVitesse, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage14.ResumeLayout(False)
        Me.C1DockingTabPage14.PerformLayout()
        CType(Me.cmbFormatExcel, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDossierExcel, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage16.ResumeLayout(False)
        Me.GroupBox9.ResumeLayout(False)
        Me.GroupBox9.PerformLayout()
        Me.C1DockingTabPage15.ResumeLayout(False)
        CType(Me.Password, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.hostname, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.UserName, System.ComponentModel.ISupportInitialize).EndInit()
        Me.FTP.ResumeLayout(False)
        Me.FTP.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents Label30 As System.Windows.Forms.Label
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bConfirmer As C1.Win.C1Input.C1Button
    Friend WithEvents Tab As C1.Win.C1Command.C1DockingTab
    Friend WithEvents C1DockingTabPage1 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents tTimbre As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents tMessagederoulant1 As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents tCodeTva As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents tFax As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents tTelephone As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents tAdresse As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents tNumeroAffiliation2 As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents tNumeroAffiliation1 As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents tCNAM As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents tPharmacie As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents tCodePharmacien As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1DockingTabPage2 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents tMinimumdePerception As C1.Win.C1Input.C1TextBox
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents Label18 As System.Windows.Forms.Label
    Friend WithEvents tHonoraireTableauC As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label17 As System.Windows.Forms.Label
    Friend WithEvents tHonoraireTableauB As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents tHonoraireTableauA As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1DockingTabPage3 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents C1DockingTabPage4 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents Label22 As System.Windows.Forms.Label
    Friend WithEvents Label20 As System.Windows.Forms.Label
    Friend WithEvents tCommandeGroupeJ As C1.Win.C1Input.C1TextBox
    Friend WithEvents chbAfficherLesDerniereDDPeremptionDansNouveauAchat As System.Windows.Forms.CheckBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents tNomOrdinateurImpressionCodeABarre As C1.Win.C1Input.C1TextBox
    Friend WithEvents lAnneeProchaine As System.Windows.Forms.Label
    Friend WithEvents lAnneeCourant As System.Windows.Forms.Label
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents dtpfinAnneeProchaine As C1.Win.C1Input.C1DateEdit
    Friend WithEvents dtpDebutAnneeProchaine As C1.Win.C1Input.C1DateEdit
    Friend WithEvents Label24 As System.Windows.Forms.Label
    Friend WithEvents dtpFinAnneeCourant As C1.Win.C1Input.C1DateEdit
    Friend WithEvents Label25 As System.Windows.Forms.Label
    Friend WithEvents dtpDebutAnneeCourant As C1.Win.C1Input.C1DateEdit
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents tRib As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1DockingTabPage5 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents tOrdonnancier As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents tBlDevis As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label19 As System.Windows.Forms.Label
    Friend WithEvents tFacture As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1DockingTabPage6 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents chbAjouterMontantTimbreFacture As System.Windows.Forms.CheckBox
    Friend WithEvents dtpDateMigration As C1.Win.C1Input.C1DateEdit
    Friend WithEvents Label21 As System.Windows.Forms.Label
    Friend WithEvents C1DockingTabPage7 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents Label23 As System.Windows.Forms.Label
    Friend WithEvents tSMTPMail As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label29 As System.Windows.Forms.Label
    Friend WithEvents tMotDePasseMail As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label28 As System.Windows.Forms.Label
    Friend WithEvents tAdresseMail As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label27 As System.Windows.Forms.Label
    Friend WithEvents tPortMail As C1.Win.C1Input.C1TextBox
    Friend WithEvents gPramatresMail As System.Windows.Forms.GroupBox
    Friend WithEvents chbAutoriseEnvoiMail As System.Windows.Forms.CheckBox
    Friend WithEvents gEnvoiLog As System.Windows.Forms.GroupBox
    Friend WithEvents Label31 As System.Windows.Forms.Label
    Friend WithEvents bEnvoiLog As C1.Win.C1Input.C1Button
    Friend WithEvents bTestEmail As C1.Win.C1Input.C1Button
    Friend WithEvents LTestMoTdePass As System.Windows.Forms.Label
    Friend WithEvents LTestMail As System.Windows.Forms.Label
    Friend WithEvents LTestPort As System.Windows.Forms.Label
    Friend WithEvents LTestSMTPMail As System.Windows.Forms.Label
    Friend WithEvents LTestEnvoiEmail As System.Windows.Forms.Label
    Friend WithEvents pbTestEnvoiEmail As System.Windows.Forms.PictureBox
    Friend WithEvents pbTestEnvoiFichierLog As System.Windows.Forms.PictureBox
    Friend WithEvents CheckedListBox1 As System.Windows.Forms.CheckedListBox
    Friend WithEvents LTestEnvoi As System.Windows.Forms.Label
    Friend WithEvents C1DockingTabPage8 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents tNbreJourValiditeParDefaut As System.Windows.Forms.TextBox
    Friend WithEvents Label32 As System.Windows.Forms.Label
    Friend WithEvents GroupBox5 As System.Windows.Forms.GroupBox
    Friend WithEvents C1DockingTabPage9 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents cmbArticle As C1.Win.C1List.C1Combo
    Friend WithEvents GBProduitSurveiller As System.Windows.Forms.GroupBox
    Friend WithEvents Label39 As System.Windows.Forms.Label
    Friend WithEvents Label42 As System.Windows.Forms.Label
    Friend WithEvents chbScannerOrdonnance As System.Windows.Forms.CheckBox
    Friend WithEvents chbEnvoyerNotification As System.Windows.Forms.CheckBox
    Friend WithEvents Label38 As System.Windows.Forms.Label
    Friend WithEvents chbCapturerPhoto As System.Windows.Forms.CheckBox
    Friend WithEvents LNom As System.Windows.Forms.Label
    Friend WithEvents gArticle As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bModifierP As C1.Win.C1Input.C1Button
    Friend WithEvents bQuitterP As C1.Win.C1Input.C1Button
    Friend WithEvents bAnnulerP As C1.Win.C1Input.C1Button
    Friend WithEvents bConfirmerP As C1.Win.C1Input.C1Button
    Friend WithEvents bAjouterP As C1.Win.C1Input.C1Button
    Friend WithEvents bSupprimerP As C1.Win.C1Input.C1Button
    Friend WithEvents tCode As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label33 As System.Windows.Forms.Label
    Friend WithEvents LArticleValide As System.Windows.Forms.Label
    Friend WithEvents Label34 As System.Windows.Forms.Label
    Friend WithEvents C1DockingTabPage10 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents tLatLong As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label35 As System.Windows.Forms.Label
    Friend WithEvents C1DockingTabPage11 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents GroupBox6 As System.Windows.Forms.GroupBox
    Friend WithEvents Label36 As System.Windows.Forms.Label
    Friend WithEvents tTailleCodeCNAM As System.Windows.Forms.TextBox
    Friend WithEvents Label37 As System.Windows.Forms.Label
    Friend WithEvents C1DockingTabPage12 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents GroupBox7 As System.Windows.Forms.GroupBox
    Friend WithEvents Label41 As System.Windows.Forms.Label
    Friend WithEvents Label40 As System.Windows.Forms.Label
    Friend WithEvents cmbListe As System.Windows.Forms.ComboBox
    Friend WithEvents cmbCaractere As System.Windows.Forms.ComboBox
    Friend WithEvents Label43 As System.Windows.Forms.Label
    Friend WithEvents cmbPolice As System.Windows.Forms.ComboBox
    Friend WithEvents tMessagederoulant2 As C1.Win.C1Input.C1TextBox
    Friend WithEvents GroupBox8 As System.Windows.Forms.GroupBox
    Friend WithEvents Label45 As System.Windows.Forms.Label
    Friend WithEvents Label44 As System.Windows.Forms.Label
    Friend WithEvents C1DockingTabPage13 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents GroupBoxTerminal As System.Windows.Forms.GroupBox
    Friend WithEvents Label46 As System.Windows.Forms.Label
    Friend WithEvents cmbProtocole As C1.Win.C1List.C1Combo
    Friend WithEvents Label47 As System.Windows.Forms.Label
    Friend WithEvents tNomPort As C1.Win.C1Input.C1TextBox
    Friend WithEvents cmbDataBit As C1.Win.C1List.C1Combo
    Friend WithEvents cmbParity As C1.Win.C1List.C1Combo
    Private WithEvents Label48 As System.Windows.Forms.Label
    Friend WithEvents cmbStopBit As C1.Win.C1List.C1Combo
    Friend WithEvents Label49 As System.Windows.Forms.Label
    Friend WithEvents cmbVitesse As C1.Win.C1List.C1Combo
    Friend WithEvents Label50 As System.Windows.Forms.Label
    Friend WithEvents Label51 As System.Windows.Forms.Label
    Friend WithEvents Label52 As System.Windows.Forms.Label
    Friend WithEvents tTexte As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label53 As System.Windows.Forms.Label
    Friend WithEvents tRemise As C1.Win.C1Input.C1TextBox
    Friend WithEvents GroupBox10 As System.Windows.Forms.GroupBox
    Friend WithEvents chFichierTexte As System.Windows.Forms.RadioButton
    Friend WithEvents chPortSerie As System.Windows.Forms.RadioButton
    Friend WithEvents Label54 As System.Windows.Forms.Label
    Friend WithEvents cmbMeme As C1.Win.C1List.C1Combo
    Friend WithEvents C1DockingTabPage14 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents cmbFormatExcel As C1.Win.C1List.C1Combo
    Friend WithEvents Label55 As System.Windows.Forms.Label
    Friend WithEvents Label56 As System.Windows.Forms.Label
    Friend WithEvents tDossierExcel As C1.Win.C1Input.C1TextBox
    Friend WithEvents chbActiverOMFAPCI As System.Windows.Forms.CheckBox
    Friend WithEvents C1DockingTabPage16 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents GroupBox9 As System.Windows.Forms.GroupBox
    Friend WithEvents Label61 As System.Windows.Forms.Label
    Friend WithEvents Label60 As System.Windows.Forms.Label
    Friend WithEvents Label59 As System.Windows.Forms.Label
    Friend WithEvents Label58 As System.Windows.Forms.Label
    Friend WithEvents Label57 As System.Windows.Forms.Label
    Friend WithEvents ChbModuleArticle5 As System.Windows.Forms.CheckBox
    Friend WithEvents ChbModuleClient1 As System.Windows.Forms.CheckBox
    Friend WithEvents ChbModuleClient2 As System.Windows.Forms.CheckBox
    Friend WithEvents ChbModuleClient3 As System.Windows.Forms.CheckBox
    Friend WithEvents ChbModuleArticle4 As System.Windows.Forms.CheckBox
    Friend WithEvents ChbModuleArticle1 As System.Windows.Forms.CheckBox
    Friend WithEvents ChbModuleArticle2 As System.Windows.Forms.CheckBox
    Friend WithEvents ChbModuleAchat2 As System.Windows.Forms.CheckBox
    Friend WithEvents ChbModuleReglement1 As System.Windows.Forms.CheckBox
    Friend WithEvents ChbModuleVente3 As System.Windows.Forms.CheckBox
    Friend WithEvents ChbModuleVente1 As System.Windows.Forms.CheckBox
    Friend WithEvents ChbModuleVente2 As System.Windows.Forms.CheckBox
    Friend WithEvents ChbModuleVente4 As System.Windows.Forms.CheckBox
    Friend WithEvents ChbModuleAchat1 As System.Windows.Forms.CheckBox
    Friend WithEvents ChbModuleArticle3 As System.Windows.Forms.CheckBox
    Friend WithEvents Label62 As System.Windows.Forms.Label
    Friend WithEvents tMail As C1.Win.C1Input.C1TextBox
    Friend WithEvents chbGestionBon As System.Windows.Forms.CheckBox
    Friend WithEvents chReseau As System.Windows.Forms.RadioButton
    Friend WithEvents Label65 As System.Windows.Forms.Label
    Friend WithEvents tNbrAppareillage As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label64 As System.Windows.Forms.Label
    Friend WithEvents tNbrPriseEnCharge As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label63 As System.Windows.Forms.Label
    Friend WithEvents tNbrOrdonnance As C1.Win.C1Input.C1TextBox
    Friend WithEvents chbMultipleDeCinq As System.Windows.Forms.CheckBox
    Friend WithEvents Label67 As System.Windows.Forms.Label
    Friend WithEvents tNbrCommande As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label66 As System.Windows.Forms.Label
    Friend WithEvents rbManquantNbrCommande As System.Windows.Forms.RadioButton
    Friend WithEvents rbManquantJour As System.Windows.Forms.RadioButton
    Friend WithEvents chbMettreAJourPrixFrigo As System.Windows.Forms.CheckBox
    Friend WithEvents ChbAfficherReglementsSupprimes As System.Windows.Forms.CheckBox
    Friend WithEvents ChbAutoriserSaisieNonMembeFamille As System.Windows.Forms.CheckBox
    Friend WithEvents ChbImprimerUnEtiquette As System.Windows.Forms.CheckBox
    Friend WithEvents GroupBox11 As System.Windows.Forms.GroupBox
    Friend WithEvents cbStockAlerte As System.Windows.Forms.CheckBox
    Friend WithEvents Label68 As System.Windows.Forms.Label
    Friend WithEvents Label26 As System.Windows.Forms.Label
    Friend WithEvents tNePasSortirManquantsDepuis As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1DockingTabPage15 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents Label71 As System.Windows.Forms.Label
    Friend WithEvents Label70 As System.Windows.Forms.Label
    Friend WithEvents label As System.Windows.Forms.Label
    Friend WithEvents UserName As C1.Win.C1Input.C1TextBox
    Friend WithEvents hostname As C1.Win.C1Input.C1TextBox
    Friend WithEvents Password As C1.Win.C1Input.C1TextBox
    Friend WithEvents FTP As System.Windows.Forms.GroupBox
End Class
