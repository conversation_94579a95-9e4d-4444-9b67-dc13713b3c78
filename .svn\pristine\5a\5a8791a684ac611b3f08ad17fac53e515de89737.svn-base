﻿Public Class fNomPourFactureVente
    Public Shared Nom1 As String = ""
    Public Shared Nom2 As String = ""
    Public Shared Nom3 As String = ""
    Public Shared NbreCopies As String = ""

    Private Sub bOk_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOk.Click

        Nom1 = tNom1.Text
        Nom2 = tNom2.Text
        Nom3 = tNom3.Text

        Me.Hide()
    End Sub

    Private Sub tNom1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            tNom2.Focus()
        End If
    End Sub

    Private Sub tNom2_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            tNom3.Focus()
        End If
    End Sub



    Private Sub tNom3_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            bOk.Focus()
        End If
    End Sub

    Private Sub fNomPourFactureVente_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        tNom1.Text = Nom1
    End Sub
End Class