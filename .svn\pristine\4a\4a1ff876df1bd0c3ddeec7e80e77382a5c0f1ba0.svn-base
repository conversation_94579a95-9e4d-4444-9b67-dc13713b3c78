//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class LOT_ARTICLE
    {
        public LOT_ARTICLE()
        {
            this.EMPRUNT_DETAILS = new HashSet<EMPRUNT_DETAILS>();
            this.ENTREE_DETAILS = new HashSet<ENTREE_DETAILS>();
            this.INVENTAIRE_DETAILS = new HashSet<INVENTAIRE_DETAILS>();
            this.SORTIE_DETAILS = new HashSet<SORTIE_DETAILS>();
        }
    
        public string NumeroLotArticle { get; set; }
        public string CodeArticle { get; set; }
        public int QteLotArticle { get; set; }
        public Nullable<System.DateTime> DatePeremptionArticle { get; set; }
    
        public virtual ARTICLE ARTICLE { get; set; }
        public virtual ICollection<EMPRUNT_DETAILS> EMPRUNT_DETAILS { get; set; }
        public virtual ICollection<ENTREE_DETAILS> ENTREE_DETAILS { get; set; }
        public virtual ICollection<INVENTAIRE_DETAILS> INVENTAIRE_DETAILS { get; set; }
        public virtual ICollection<SORTIE_DETAILS> SORTIE_DETAILS { get; set; }
    }
}
