@echo off
echo ========================================
echo    LOCALISATION DE L'EXECUTABLE
echo    PHARMA2000 Moderne
echo ========================================
echo.

cd /d "%~dp0"

echo Verification des dossiers...
echo.

if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe" (
    echo ✅ EXECUTABLE TROUVE !
    echo.
    echo 📁 Emplacement : PharmaModerne.UI\bin\Debug\net9.0-windows\
    echo 📄 Fichier : PharmaModerne.UI.exe
    echo.
    
    REM Afficher les informations du fichier
    dir "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe"
    echo.
    
    echo 🚀 Ouverture du dossier dans l'Explorateur...
    explorer "PharmaModerne.UI\bin\Debug\net9.0-windows"
    
    echo.
    echo ✅ Le dossier s'est ouvert dans l'Explorateur Windows
    echo ✅ Vous devriez voir le fichier PharmaModerne.UI.exe
    echo.
    echo 💡 POUR LANCER L'APPLICATION :
    echo    Double-cliquez sur PharmaModerne.UI.exe
    echo    Ou utilisez le script LANCER_PHARMA_MODERNE.bat
    echo.
    
) else if exist "PharmaModerne.UI\bin\Release\net9.0-windows\PharmaModerne.UI.exe" (
    echo ✅ EXECUTABLE RELEASE TROUVE !
    echo.
    echo 📁 Emplacement : PharmaModerne.UI\bin\Release\net9.0-windows\
    echo 📄 Fichier : PharmaModerne.UI.exe
    echo.
    
    dir "PharmaModerne.UI\bin\Release\net9.0-windows\PharmaModerne.UI.exe"
    echo.
    
    echo 🚀 Ouverture du dossier dans l'Explorateur...
    explorer "PharmaModerne.UI\bin\Release\net9.0-windows"
    
) else (
    echo ❌ EXECUTABLE NON TROUVE !
    echo.
    echo 🔍 VERIFICATION DES DOSSIERS :
    echo.
    
    if exist "PharmaModerne.UI" (
        echo ✅ Dossier PharmaModerne.UI existe
        
        if exist "PharmaModerne.UI\bin" (
            echo ✅ Dossier PharmaModerne.UI\bin existe
            echo.
            echo 📁 Contenu du dossier bin :
            dir "PharmaModerne.UI\bin" /B
            echo.
            
            if exist "PharmaModerne.UI\bin\Debug" (
                echo ✅ Dossier Debug existe
                echo.
                echo 📁 Contenu du dossier Debug :
                dir "PharmaModerne.UI\bin\Debug" /B
                echo.
                
                if exist "PharmaModerne.UI\bin\Debug\net9.0-windows" (
                    echo ✅ Dossier net9.0-windows existe
                    echo.
                    echo 📁 Contenu du dossier net9.0-windows :
                    dir "PharmaModerne.UI\bin\Debug\net9.0-windows" /B
                    echo.
                    echo ❌ Mais PharmaModerne.UI.exe n'est pas present !
                ) else (
                    echo ❌ Dossier net9.0-windows n'existe pas
                )
            ) else (
                echo ❌ Dossier Debug n'existe pas
            )
        ) else (
            echo ❌ Dossier bin n'existe pas
        )
    ) else (
        echo ❌ Dossier PharmaModerne.UI n'existe pas
    )
    
    echo.
    echo 🔧 SOLUTION :
    echo 1. Executez COMPILER_PROJET_MODERNE.bat
    echo 2. Ou compilez manuellement :
    echo    dotnet build PharmaModerne.sln --configuration Debug
    echo.
)

echo.
echo ========================================
echo    INFORMATIONS SUPPLEMENTAIRES
echo ========================================
echo.
echo 📂 STRUCTURE ATTENDUE :
echo PharmaModerne\
echo ├── PharmaModerne.UI\
echo │   └── bin\
echo │       └── Debug\
echo │           └── net9.0-windows\
echo │               └── PharmaModerne.UI.exe
echo.
echo 🔍 SI VOUS NE VOYEZ PAS LE DOSSIER :
echo - Verifiez que vous etes dans le bon repertoire
echo - Actualisez l'Explorateur Windows (F5)
echo - Verifiez les permissions de fichiers
echo.
echo 💻 CHEMIN COMPLET ACTUEL :
echo %CD%
echo.

pause
