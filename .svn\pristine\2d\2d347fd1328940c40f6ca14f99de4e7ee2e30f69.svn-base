﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fUtilisateur
    Dim cmdUser As New SqlCommand
    Dim daUser As New SqlDataAdapter
    Dim dsUser As New DataSet

    Dim cbUtilisateur As New SqlCommandBuilder
    Dim cmdUtilisateur As New SqlCommand
    Dim daUtilisateur As New SqlDataAdapter
    Dim dsUtilisateur As New DataSet

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "116" And bAjouter.Enabled = True Then
            bAjouter_Click(sender, e)
        End If

        If argument = "118" And bSupprimer.Enabled = True Then
            bSupprimer_Click(sender, e)
        End If

        If argument = "123" And bAjouter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
       
    End Sub

    Public Sub Init()
        AfficherUtilisateur()
        ' If mode <> "adminis" Then
        'bAjouter.Visible = False
        'bSupprimer.Visible = False
        'bChangerPasse.Visible = False

        'Label2.Visible = False
        'Label1.Visible = False
        'Label5.Visible = False

        'tCodeUtilisateur.Visible = False
        'tNom.Visible = False
        'tLogin.Visible = False

        ' End If

        lCodeNomUilisateur.Visible = False
        tCodeUtilisateur.Visible = False

    End Sub

    Private Sub AfficherUtilisateur()
        Dim StrSQL As String = ""
        Dim I As Integer
        Dim Cond As String = "1=1"
        dsUser.Clear()

        StrSQL = " SELECT " + _
                 " CodeUtilisateur, " + _
                 " Nom, " + _
                 " Login " + _
                 " FROM UTILISATEUR WHERE Supprime=0"

        cmdUser.Connection = ConnectionServeur
        cmdUser.CommandText = StrSQL
        daUser = New SqlDataAdapter(cmdUser)
        daUser.Fill(dsUser, "UTILISATEUR")

        With gUtilisateur
            .Columns.Clear()
            .DataSource = dsUser
            .DataMember = "UTILISATEUR"
            .Rebind(False)
            .Columns("CodeUtilisateur").Caption = "Code de l'utilisateur"
            .Columns("Nom").Caption = "Nom"
            .Columns("Login").Caption = "Login"
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("CodeUtilisateur").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).ExtendRightColumn = True
            .Splits(0).RecordSelectors = False

            .Splits(0).DisplayColumns("CodeUtilisateur").Width = 200
            .Splits(0).DisplayColumns("CodeUtilisateur").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Nom").Width = 200

            .Splits(0).DisplayColumns("CodeUtilisateur").Locked = True
            .Splits(0).DisplayColumns("Nom").Locked = True
            .Splits(0).DisplayColumns("Login").Locked = True

            'Style du Caractere et du grid
            ParametreGrid(gUtilisateur)
        End With
    End Sub

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click
        Dim StrSQL = ""
        Dim dr As DataRow
        'If (tCodeUtilisateur.Text = "") Then
        '    MsgBox("Veuillez saisir le code de l'utilisateur à ajouter !", MsgBoxStyle.Critical, "Erreur")
        '    tCodeUtilisateur.Focus()
        '    Exit Sub
        'End If
        'If Not IsNumeric(tCodeUtilisateur.Text) Then
        '    MsgBox("Veuillez saisir un code valide !", MsgBoxStyle.Critical, "Erreur")
        '    tCodeUtilisateur.Focus()
        '    Exit Sub
        'End If
        If tNom.Text = "" Then
            MsgBox("Veuillez saisir le nom de l'utilisateur à ajouter !", MsgBoxStyle.Critical, "Erreur")
            tNom.Focus()
            Exit Sub
        End If
        If tLogin.Text = "" Then
            MsgBox("Veuillez saisir le login de l'utilisateur à ajouter !", MsgBoxStyle.Critical, "Erreur")
            tLogin.Focus()
            Exit Sub
        End If

        dsUtilisateur.Clear()
        StrSQL = "SELECT TOP 0 * FROM UTILISATEUR"

        cmdUtilisateur.Connection = ConnectionServeur
        cmdUtilisateur.CommandText = StrSQL
        daUtilisateur = New SqlDataAdapter(cmdUtilisateur)
        daUtilisateur.Fill(dsUtilisateur, "UTILISATEUR")
        cbUtilisateur = New SqlCommandBuilder(daUtilisateur)

        With dsUtilisateur
            dr = .Tables("UTILISATEUR").NewRow
            dr.Item("CodeUtilisateur") = getMaxCodeUser() 'tCodeUtilisateur.Text
            dr.Item("Nom") = tNom.Text
            dr.Item("Login") = tLogin.Text
            .Tables("UTILISATEUR").Rows.Add(dr)
        End With
        Try
            daUtilisateur.Update(dsUtilisateur, "UTILISATEUR")
            AfficherUtilisateur()
            'tCodeUtilisateur.Value = ""
            tNom.Value = ""
            tLogin.Value = ""
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + "Code existe déjà, vous devez changer le code user", MsgBoxStyle.Critical, "Erreur")
            'tCodeUtilisateur.Value = ""
            'MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
        End Try
    End Sub

    
    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click
        Dim cmd As New SqlCommand
        With gUtilisateur
            If gUtilisateur(gUtilisateur.Row, "Nom") = "ADMINISTRATEUR" Then
                MsgBox("Impossible de supprimer cet utilisateur  !!", MsgBoxStyle.Information, "Erreur")
                Exit Sub
            End If
            If .RowCount > 0 Then
                If MsgBox("Voulez vous vraiment supprimer cet utilisateur ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                    Try
                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = "UPDATE UTILISATEUR SET Supprime=1 WHERE CodeUtilisateur =" + Quote(gUtilisateur(gUtilisateur.Row, "CodeUtilisateur"))
                        cmd.ExecuteNonQuery()
                        InsertionDansLog("SUPPRESSION_UTILISATEUR", "Suppression de l'utilisateur" + gUtilisateur(gUtilisateur.Row, "Nom"), "-", System.DateTime.Now, "CNAM", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
                        AfficherUtilisateur()
                    Catch ex As Exception
                        MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                    End Try
                End If
            End If
        End With
    End Sub

    Private Sub bChangerPasse_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bChangerPasse.Click
        Dim MyLoginMotDePasse As New fChangerLoginMotDePasse
        MyLoginMotDePasse.CodeUtilisateur = gUtilisateur(gUtilisateur.Row, "CodeUtilisateur")
        MyLoginMotDePasse.Mode = ""
        MyLoginMotDePasse.NomUtilisateur = gUtilisateur(gUtilisateur.Row, "Nom")
        MyLoginMotDePasse.ShowDialog()
        AfficherUtilisateur()
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub tCodeUtilisateur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeUtilisateur.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNom.Focus()
        End If
    End Sub

    Private Sub tCodeUtilisateur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeUtilisateur.TextChanged

    End Sub

    Private Sub tNom_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNom.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLogin.Focus()
        End If
    End Sub

    Private Sub tNom_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNom.TextChanged

    End Sub

    Private Sub tLogin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tLogin.KeyUp
        If e.KeyCode = Keys.Enter Then
            bAjouter.Focus()
        End If
    End Sub

    Private Sub tLogin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tLogin.TextChanged

    End Sub

    Private Sub gUtilisateur_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gUtilisateur.Click

    End Sub

    Private Function getMaxCodeUser()

        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim MaxCodeUser As Integer = 0

        StrSQL = " SELECT max(convert(int,[CodeUtilisateur])) + 1 FROM [UTILISATEUR] "
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            MaxCodeUser = cmdRecupereNum.ExecuteScalar()
            Return MaxCodeUser
        Catch ex As Exception
            Console.WriteLine(ex.Message)
            Return 0
        End Try
    End Function
End Class