﻿Public Class fNomBoutonLien
    Public Shared Nom As String = ""

    Private Sub bOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOK.Click
        Nom = tNom.Text
        Me.Hide()
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Nom = ""
        Me.Hide()
    End Sub

    Private Sub fNomBoutonLien_Activated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Activated
        tNom.Focus()
    End Sub

    Private Sub fNomBoutonLien_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        tNom.Focus()
    End Sub

    Private Sub tNom_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNom.KeyUp
        If e.KeyCode = Keys.Enter Then
            bOK_Click(sender, e)
        End If
    End Sub

    Private Sub tNom_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNom.TextChanged

    End Sub
End Class