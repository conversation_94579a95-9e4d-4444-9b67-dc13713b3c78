﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fAchatLectureTerminal
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fInventaireLectureTerminal))
        Me.bLire = New C1.Win.C1Input.C1Button()
        Me.PortSerie = New System.IO.Ports.SerialPort(Me.components)
        Me.gInventaire = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bValider = New C1.Win.C1Input.C1Button()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.Panel = New System.Windows.Forms.Panel()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.bAjoutArticle = New C1.Win.C1Input.C1Button()
        Me.bSupprimeArticle = New C1.Win.C1Input.C1Button()
        Me.tParity = New C1.Win.C1Input.C1TextBox()
        Me.tHandshake = New C1.Win.C1Input.C1TextBox()
        Me.tStopBit = New C1.Win.C1Input.C1TextBox()
        Me.tDataBit = New C1.Win.C1Input.C1TextBox()
        Me.tBaudRate = New C1.Win.C1Input.C1TextBox()
        Me.tPortName = New C1.Win.C1Input.C1TextBox()
        CType(Me.gInventaire, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel.SuspendLayout()
        Me.GroupBox4.SuspendLayout()
        CType(Me.tParity, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tHandshake, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tStopBit, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDataBit, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tBaudRate, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tPortName, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'bLire
        '
        Me.bLire.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bLire.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bLire.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bLire.Location = New System.Drawing.Point(753, 390)
        Me.bLire.Name = "bLire"
        Me.bLire.Size = New System.Drawing.Size(132, 39)
        Me.bLire.TabIndex = 95
        Me.bLire.Text = "Afficher       F5"
        Me.bLire.UseVisualStyleBackColor = True
        Me.bLire.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gInventaire
        '
        Me.gInventaire.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gInventaire.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gInventaire.GroupByCaption = "Drag a column header here to group by that column"
        Me.gInventaire.Images.Add(CType(resources.GetObject("gInventaire.Images"), System.Drawing.Image))
        Me.gInventaire.LinesPerRow = 2
        Me.gInventaire.Location = New System.Drawing.Point(12, 120)
        Me.gInventaire.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
        Me.gInventaire.Name = "gInventaire"
        Me.gInventaire.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gInventaire.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gInventaire.PreviewInfo.ZoomFactor = 75.0R
        Me.gInventaire.PrintInfo.PageSettings = CType(resources.GetObject("gInventaire.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gInventaire.Size = New System.Drawing.Size(1024, 260)
        Me.gInventaire.TabIndex = 112
        Me.gInventaire.Text = "C1TrueDBGrid1"
        Me.gInventaire.PropBag = resources.GetString("gInventaire.PropBag")
        '
        'bValider
        '
        Me.bValider.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bValider.Enabled = False
        Me.bValider.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bValider.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bValider.Location = New System.Drawing.Point(890, 390)
        Me.bValider.Name = "bValider"
        Me.bValider.Size = New System.Drawing.Size(71, 39)
        Me.bValider.TabIndex = 116
        Me.bValider.Text = "Valider            F3"
        Me.bValider.UseVisualStyleBackColor = True
        Me.bValider.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(965, 390)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(71, 39)
        Me.bQuitter.TabIndex = 128
        Me.bQuitter.Text = "Quitter        F12"
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.GroupBox4)
        Me.Panel.Controls.Add(Me.bAjoutArticle)
        Me.Panel.Controls.Add(Me.bSupprimeArticle)
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.gInventaire)
        Me.Panel.Controls.Add(Me.bLire)
        Me.Panel.Controls.Add(Me.bValider)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1048, 441)
        Me.Panel.TabIndex = 129
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.Label9)
        Me.GroupBox4.Location = New System.Drawing.Point(12, 3)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(200, 111)
        Me.GroupBox4.TabIndex = 131
        Me.GroupBox4.TabStop = False
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Cursor = System.Windows.Forms.Cursors.Default
        Me.Label9.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Label9.Font = New System.Drawing.Font("HandelGotDLig", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label9.Location = New System.Drawing.Point(18, 36)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(159, 40)
        Me.Label9.TabIndex = 132
        Me.Label9.Text = "Terminal"
        Me.Label9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bAjoutArticle
        '
        Me.bAjoutArticle.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bAjoutArticle.Enabled = False
        Me.bAjoutArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjoutArticle.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjoutArticle.Location = New System.Drawing.Point(13, 390)
        Me.bAjoutArticle.Name = "bAjoutArticle"
        Me.bAjoutArticle.Size = New System.Drawing.Size(138, 39)
        Me.bAjoutArticle.TabIndex = 130
        Me.bAjoutArticle.Text = "Créer / Visualiser Article   F1"
        Me.bAjoutArticle.UseVisualStyleBackColor = True
        Me.bAjoutArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bSupprimeArticle
        '
        Me.bSupprimeArticle.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bSupprimeArticle.Enabled = False
        Me.bSupprimeArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimeArticle.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSupprimeArticle.Location = New System.Drawing.Point(157, 390)
        Me.bSupprimeArticle.Name = "bSupprimeArticle"
        Me.bSupprimeArticle.Size = New System.Drawing.Size(84, 39)
        Me.bSupprimeArticle.TabIndex = 129
        Me.bSupprimeArticle.Text = "Supprimer     F4"
        Me.bSupprimeArticle.UseVisualStyleBackColor = True
        Me.bSupprimeArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tParity
        '
        Me.tParity.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tParity.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tParity.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tParity.Location = New System.Drawing.Point(365, 66)
        Me.tParity.Name = "tParity"
        Me.tParity.Size = New System.Drawing.Size(108, 18)
        Me.tParity.TabIndex = 99
        Me.tParity.Tag = Nothing
        Me.tParity.Visible = False
        Me.tParity.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tParity.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tHandshake
        '
        Me.tHandshake.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tHandshake.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tHandshake.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tHandshake.Location = New System.Drawing.Point(334, 24)
        Me.tHandshake.Name = "tHandshake"
        Me.tHandshake.Size = New System.Drawing.Size(122, 18)
        Me.tHandshake.TabIndex = 102
        Me.tHandshake.Tag = Nothing
        Me.tHandshake.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tHandshake.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tStopBit
        '
        Me.tStopBit.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tStopBit.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tStopBit.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tStopBit.Location = New System.Drawing.Point(125, 91)
        Me.tStopBit.Name = "tStopBit"
        Me.tStopBit.Size = New System.Drawing.Size(122, 18)
        Me.tStopBit.TabIndex = 101
        Me.tStopBit.Tag = Nothing
        Me.tStopBit.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tStopBit.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tDataBit
        '
        Me.tDataBit.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tDataBit.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDataBit.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tDataBit.Location = New System.Drawing.Point(125, 67)
        Me.tDataBit.Name = "tDataBit"
        Me.tDataBit.Size = New System.Drawing.Size(122, 18)
        Me.tDataBit.TabIndex = 100
        Me.tDataBit.Tag = Nothing
        Me.tDataBit.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDataBit.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tBaudRate
        '
        Me.tBaudRate.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tBaudRate.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tBaudRate.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tBaudRate.Location = New System.Drawing.Point(125, 43)
        Me.tBaudRate.Name = "tBaudRate"
        Me.tBaudRate.Size = New System.Drawing.Size(122, 18)
        Me.tBaudRate.TabIndex = 98
        Me.tBaudRate.Tag = Nothing
        Me.tBaudRate.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tBaudRate.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tPortName
        '
        Me.tPortName.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tPortName.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tPortName.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tPortName.Location = New System.Drawing.Point(123, 19)
        Me.tPortName.Name = "tPortName"
        Me.tPortName.Size = New System.Drawing.Size(122, 18)
        Me.tPortName.TabIndex = 103
        Me.tPortName.Tag = Nothing
        Me.tPortName.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tPortName.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fInventaireLectureTerminal
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CancelButton = Me.bQuitter
        Me.ClientSize = New System.Drawing.Size(1048, 441)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fInventaireLectureTerminal"
        Me.Text = "Lecture du Terminal"
        Me.WindowState = System.Windows.Forms.FormWindowState.Maximized
        CType(Me.gInventaire, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel.ResumeLayout(False)
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.tParity, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tHandshake, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tStopBit, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDataBit, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tBaudRate, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tPortName, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents bLire As C1.Win.C1Input.C1Button
    Friend WithEvents PortSerie As System.IO.Ports.SerialPort
    Friend WithEvents gInventaire As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bValider As C1.Win.C1Input.C1Button
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents tParity As C1.Win.C1Input.C1TextBox
    Friend WithEvents tHandshake As C1.Win.C1Input.C1TextBox
    Friend WithEvents tStopBit As C1.Win.C1Input.C1TextBox
    Friend WithEvents tDataBit As C1.Win.C1Input.C1TextBox
    Friend WithEvents tBaudRate As C1.Win.C1Input.C1TextBox
    Friend WithEvents tPortName As C1.Win.C1Input.C1TextBox
    Friend WithEvents bAjoutArticle As C1.Win.C1Input.C1Button
    Friend WithEvents bSupprimeArticle As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents Label9 As System.Windows.Forms.Label
End Class
