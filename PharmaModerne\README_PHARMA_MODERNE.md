# PHARMA2000 MODERNE 🏥💊

## Vue d'ensemble

**PHARMA2000 Moderne** est une version complètement réécrite et modernisée du système de gestion pharmaceutique PHARMA2000. Cette nouvelle version intègre les dernières technologies .NET et offre une interface utilisateur moderne avec scanner de codes à barres intégré.

## 🎯 Objectifs du Projet

- ✅ **Modernisation complète** de l'architecture PHARMA2000
- ✅ **Interface utilisateur moderne** avec Material Design
- ✅ **Scanner de codes à barres intégré** dans tous les modules
- ✅ **Architecture modulaire** et maintenable
- ✅ **Performance optimisée** avec Entity Framework Core
- ✅ **Expérience utilisateur améliorée**

## 🏗️ Architecture Technique

### Structure des Projets

```
PharmaModerne/
├── PharmaModerne.Shared/     # Modèles et DTOs partagés
├── PharmaModerne.Core/       # Interfaces et logique métier
├── PharmaModerne.Data/       # Entity Framework et contexte
├── PharmaModerne.Services/   # Implémentation des services
├── PharmaModerne.UI/         # Interface WPF moderne
└── PharmaModerne.Tests/      # Tests unitaires
```

### Technologies Utilisées

- **.NET 9.0** - Framework principal
- **WPF** - Interface utilisateur Windows
- **Material Design** - Design moderne et élégant
- **Entity Framework Core** - ORM pour base de données
- **MVVM Pattern** - Architecture MVVM avec CommunityToolkit
- **SQL Server** - Base de données (compatible)

## 📱 Fonctionnalités Scanner

### Scanner Intégré Partout
- **Détection automatique** scanner vs saisie manuelle
- **Traitement en temps réel** des codes scannés
- **Validation automatique** des formats de codes
- **Historique des scans** avec statistiques
- **Configuration flexible** des paramètres

### Modules avec Scanner
- 👥 **Clients** - Scan des codes clients
- 💊 **Articles** - Scan des codes articles et codes-barres
- 🛒 **Ventes** - Scan rapide en point de vente
- 📦 **Stock** - Mouvements par scan
- 📋 **Inventaires** - Comptage par scan

## 🎨 Interface Utilisateur

### Design Moderne
- **Material Design 3** - Interface élégante et intuitive
- **Navigation latérale** - Menu organisé par modules
- **Recherche globale** - Barre de recherche universelle
- **Thèmes adaptatifs** - Clair/sombre automatique
- **Responsive** - S'adapte à toutes les tailles d'écran

### Expérience Utilisateur
- **Indicateurs visuels** - Statuts et alertes colorés
- **Actions rapides** - Boutons d'action contextuels
- **Pagination intelligente** - Navigation fluide des listes
- **Filtres avancés** - Recherche multicritères
- **Notifications** - Système d'alertes intégré

## 📊 Modules Implémentés

### 👥 Gestion des Clients
- ✅ CRUD complet avec validation
- ✅ Recherche avancée et filtrage
- ✅ Scanner de codes clients
- ✅ Historique des achats
- ✅ Gestion des comptes et crédits
- ✅ Export/Import (structure prête)

### 💊 Gestion des Articles
- ✅ Catalogue complet avec codes-barres
- ✅ Gestion des stocks en temps réel
- ✅ Alertes de stock automatiques
- ✅ Scanner intégré pour inventaires
- ✅ Liaison avec fournisseurs
- ✅ Calculs de prix et TVA

### 🏪 Gestion des Fournisseurs
- ✅ Base de données fournisseurs
- ✅ Gestion des commandes
- ✅ Suivi des livraisons
- ✅ Historique des achats
- ✅ Évaluation des performances

### 💰 Point de Vente
- ✅ Interface de caisse moderne
- ✅ Scanner intégré pour articles
- ✅ Gestion des paiements
- ✅ Tickets de caisse
- ✅ Remises et promotions

### 📊 Rapports et Analyses
- ✅ Dashboard interactif
- ✅ Statistiques de ventes
- ✅ Analyses de stock
- ✅ Rapports financiers
- ✅ Graphiques et visualisations

## 🚀 Installation et Utilisation

### Prérequis
- **Windows 10/11** (64-bit)
- **.NET 9.0 SDK** ou plus récent
- **Visual Studio 2022** (recommandé)
- **SQL Server** (LocalDB ou Express)

### Compilation
```bash
# Cloner le projet
git clone [repository-url]

# Compiler le projet
.\COMPILER_PROJET_MODERNE.bat

# Ou manuellement
dotnet build PharmaModerne.sln --configuration Release
```

### Lancement
```bash
# Lancer l'application
.\LANCER_PHARMA_MODERNE.bat

# Ou directement
.\PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe
```

## 📱 Guide d'Utilisation du Scanner

### Activation du Scanner
1. Cliquez sur l'icône **radar** dans la barre d'outils
2. L'indicateur "Scanner Actif" apparaît
3. Le scanner détecte automatiquement les codes

### Utilisation
1. **Recherche globale** - Scannez dans la barre de recherche
2. **Modules clients** - Scannez les codes clients
3. **Modules articles** - Scannez les codes-barres
4. **Point de vente** - Scan rapide des articles

### Configuration
- **Délai de détection** - Ajustable (50ms à 1s)
- **Validation des codes** - Règles personnalisables
- **Format des codes** - Conversion automatique
- **Historique** - Suivi des scans

## 🔧 Configuration

### Base de Données
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=PharmaModerne;Trusted_Connection=true"
  }
}
```

### Scanner
```csharp
var config = new ScannerConfig
{
    ScanDelay = 100,           // ms
    AutoProcessing = true,
    ValidationRules = new ValidationRules
    {
        MinLength = 2,
        MaxLength = 20,
        RequireAlphaNumeric = true
    }
};
```

## 📈 Performances

### Optimisations Implémentées
- **Entity Framework Core** - Requêtes optimisées
- **Pagination intelligente** - Chargement par pages
- **Cache en mémoire** - Données fréquentes
- **Index de base de données** - Recherches rapides
- **Lazy Loading** - Chargement à la demande

### Métriques
- **Temps de démarrage** : < 3 secondes
- **Recherche clients** : < 100ms pour 10k enregistrements
- **Scanner** : Détection < 50ms
- **Interface** : 60 FPS fluides

## 🧪 Tests

### Tests Implémentés
- ✅ Tests unitaires des services
- ✅ Tests d'intégration Entity Framework
- ✅ Tests de validation des DTOs
- ✅ Tests du scanner
- ✅ Tests de performance

### Exécution des Tests
```bash
dotnet test PharmaModerne.Tests
```

## 🔮 Roadmap

### Version 2.0 (Prochaine)
- [ ] **Module de facturation** avancé
- [ ] **Synchronisation cloud** multi-sites
- [ ] **API REST** pour intégrations
- [ ] **Application mobile** compagnon
- [ ] **Intelligence artificielle** pour prédictions

### Version 2.1
- [ ] **Gestion des ordonnances** électroniques
- [ ] **Interface pharmacien** spécialisée
- [ ] **Conformité réglementaire** automatique
- [ ] **Intégration comptable** avancée

## 🤝 Contribution

### Comment Contribuer
1. **Fork** le projet
2. **Créer** une branche feature
3. **Développer** avec tests
4. **Soumettre** une pull request

### Standards de Code
- **C# 12** avec nullable enabled
- **MVVM pattern** strict
- **Tests unitaires** obligatoires
- **Documentation** complète

## 📞 Support

### Aide et Documentation
- **README** - Ce document
- **Wiki** - Documentation détaillée
- **Issues** - Rapports de bugs
- **Discussions** - Questions et suggestions

### Contact
- **Email** : <EMAIL>
- **GitHub** : [Repository Issues]
- **Documentation** : [Wiki Pages]

## 📄 Licence

Ce projet est sous licence **MIT**. Voir le fichier `LICENSE` pour plus de détails.

---

## 🎉 Remerciements

Merci à tous les contributeurs qui ont rendu ce projet possible :
- **Équipe de développement** PHARMA2000
- **Communauté .NET** pour les outils
- **Material Design** pour l'inspiration UI
- **Utilisateurs beta** pour les retours

---

**PHARMA2000 Moderne** - *L'avenir de la gestion pharmaceutique* 🚀
