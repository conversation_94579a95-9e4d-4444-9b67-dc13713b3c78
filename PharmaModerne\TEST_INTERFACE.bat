@echo off
echo ========================================
echo    TEST INTERFACE - DIAGNOSTIC APPROFONDI
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 DIAGNOSTIC DE L'INTERFACE WPF...
echo.

REM Test avec dotnet run pour voir les erreurs
echo 1. TEST AVEC DOTNET RUN (capture erreurs) :
echo.
dotnet run --project PharmaModerne.UI\PharmaModerne.UI.csproj 2>&1

echo.
echo ========================================
echo    PROBLEMES POSSIBLES DETECTES
echo ========================================
echo.

echo 🔧 SOLUTIONS A ESSAYER :
echo.
echo 1. PROBLEME XAML/INTERFACE :
echo    - Erreur dans MainWindowComplete.xaml
echo    - Ressources manquantes
echo    - Styles non charges
echo.
echo 2. PROBLEME DEPENDANCES :
echo    - MaterialDesign non charge
echo    - WPF non disponible
echo    - .NET Desktop Runtime manquant
echo.
echo 3. PROBLEME DEMARRAGE :
echo    - Exception non geree
echo    - Erreur dans App.xaml
echo    - Probleme d'initialisation
echo.

echo ========================================
echo    SOLUTION IMMEDIATE
echo ========================================
echo.

echo 🔧 CREATION D'UNE VERSION SIMPLIFIEE...
echo.

REM Créer une version de test simple
cd PharmaModerne.UI

echo Modification du fichier App.xaml pour test...
echo ^<Application x:Class="PharmaModerne.UI.App" > App_Test.xaml
echo              xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" >> App_Test.xaml
echo              xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" >> App_Test.xaml
echo              StartupUri="TestWindow.xaml"^> >> App_Test.xaml
echo ^</Application^> >> App_Test.xaml

echo.
echo ✅ Version de test creee
echo.

echo 🔄 Recompilation avec version simplifiee...
copy App_Test.xaml App.xaml /Y
dotnet build --configuration Debug

if %errorlevel% equ 0 (
    echo ✅ Compilation reussie !
    echo.
    echo 🚀 Test de la version simplifiee...
    cd bin\Debug\net9.0-windows
    start "" "PharmaModerne.UI.exe"
    cd ..\..\..\..
    
    echo.
    echo 💡 Si cette version fonctionne, le probleme vient de MainWindowComplete.xaml
    echo 💡 Si cette version ne fonctionne pas non plus, probleme plus profond
    
) else (
    echo ❌ Erreur de compilation persistante
    cd ..
)

echo.
echo ========================================
echo    INSTRUCTIONS UTILISATEUR
echo ========================================
echo.

echo 📋 VERIFIEZ MAINTENANT :
echo.
echo 1. Une fenetre de test s'est-elle ouverte ?
echo    ✅ OUI = Probleme avec MainWindowComplete.xaml
echo    ❌ NON = Probleme systeme plus profond
echo.
echo 2. Si aucune fenetre :
echo    - Verifiez le Gestionnaire des taches
echo    - Cherchez "PharmaModerne" dans les processus
echo    - Regardez s'il y a des erreurs dans l'Observateur d'evenements
echo.
echo 3. Messages d'erreur affiches ci-dessus ?
echo    - Notez les erreurs exactes
echo    - Recherchez les solutions specifiques
echo.

pause
