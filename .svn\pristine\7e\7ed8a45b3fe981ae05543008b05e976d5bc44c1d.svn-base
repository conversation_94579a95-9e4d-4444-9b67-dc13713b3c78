//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class VENTE_INSTANCE_DETAILS
    {
        public string NumeroVenteInstance { get; set; }
        public string CodeArticle { get; set; }
        public string CodeABarre { get; set; }
        public string Designation { get; set; }
        public int CodeForme { get; set; }
        public int Qte { get; set; }
        public decimal PrixAchat { get; set; }
        public decimal PrixHT { get; set; }
        public decimal TotalHT { get; set; }
        public decimal PrixTTC { get; set; }
        public decimal TotalTTC { get; set; }
        public decimal Remise { get; set; }
        public decimal TVA { get; set; }
        public decimal TotalTVA { get; set; }
        public Nullable<decimal> Honoraire { get; set; }
        public Nullable<int> Stock { get; set; }
        public Nullable<System.DateTime> DateDePeremption { get; set; }
        public Nullable<bool> PriseEnCharge { get; set; }
        public Nullable<bool> AccordPrealable { get; set; }
        public Nullable<decimal> TarifDeReference { get; set; }
        public Nullable<int> Ordre { get; set; }
    
        public virtual ARTICLE ARTICLE { get; set; }
        public virtual VENTE_INSTANCE VENTE_INSTANCE { get; set; }
    }
}
