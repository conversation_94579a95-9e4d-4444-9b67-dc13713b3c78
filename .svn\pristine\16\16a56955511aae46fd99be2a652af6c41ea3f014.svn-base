//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class VENTE_HISTORIQUE
    {
        public string NumeroVente { get; set; }
        public Nullable<System.DateTime> Date { get; set; }
        public Nullable<decimal> TotalHT { get; set; }
        public Nullable<decimal> TotalTTC { get; set; }
        public Nullable<decimal> TVA { get; set; }
        public Nullable<decimal> TotalRemise { get; set; }
        public Nullable<decimal> Timbre { get; set; }
        public string CodeClient { get; set; }
        public string CodePersonnel { get; set; }
        public Nullable<int> CodeAPCI { get; set; }
        public Nullable<int> CodeDeFamille { get; set; }
        public string CodeMedecinFamille { get; set; }
        public string CodeMedecinPrescripteur { get; set; }
        public string LibellePoste { get; set; }
        public Nullable<decimal> Recu { get; set; }
        public Nullable<System.DateTime> DateOrdonnance { get; set; }
        public Nullable<decimal> MontantCnam { get; set; }
        public Nullable<decimal> MontantMutuelle { get; set; }
        public Nullable<bool> TiersPayant { get; set; }
        public Nullable<bool> PriseEnCharge { get; set; }
        public Nullable<bool> Appareillage { get; set; }
        public string IdentifiantCNAMMedecin { get; set; }
        public string NumeroPriseEnCharge { get; set; }
        public string NumeroBonAchat { get; set; }
        public Nullable<int> DureeTraitement { get; set; }
        public Nullable<bool> OMF { get; set; }
        public Nullable<bool> APCI { get; set; }
    }
}
