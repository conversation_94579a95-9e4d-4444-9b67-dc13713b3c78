﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Text
Imports Pharma2000Premium.ServiceBCB
Imports System.Data.OleDb

Public Class fArticle
    Dim x As Integer
    Dim FeuilleInitialisee As Boolean = False
    Dim cmd As New SqlCommand
    Dim cmdArticle As New SqlCommand
    Dim daArticle As New SqlDataAdapter
    Dim dsArticle As New DataSet
    Dim TotalAchatHT As Double = 0.0
    Dim TotalAchatTTC As Double = 0.0
    Dim TotalVenteHT As Double = 0.0
    Dim TotalVenteTTC As Double = 0.0
    Dim VOrderBy As String = "Designation"
    Dim VAscDesc As String = "Asc"
    Dim cmdCommande As New SqlCommand

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "116" And bAjouterArticle.Enabled = True Then
            bAjouterArticle_Click(sender, e)
        End If
        If argument = "117" And bRechercher.Enabled = True Then
            bRechercher_Click(sender, e)
        End If
        If argument = "118" And bSuprimerArticle.Enabled = True Then
            bSuprimerArticle_Click(sender, e)
        End If
        If argument = "119" And bModifierArticle.Enabled = True Then
            bModifierArticle_Click(sender, e)
        End If
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    'changment de situtation en cas ou larticle manquant
    Public Sub ArticleMAnquant()
        'If SansManquantDepuis <> "" Then
        Try
            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = "UPDATE " + _
                                    "	ARTICLE " + _
                                    "SET " + _
                                    "	ARTICLE.CodeSituation = '2' " + _
                                    " where DATEDIFF ( DAY  , ARTICLE.DateDerniereCommande   , GETDATE() ) >= (SELECT TOP(1) ISNULL(NePAsSortirLesManquantsDepuis, 5) FROM PARAMETRES WHERE NePAsSortirLesManquantsDepuis <> '' )" & _
                                     " AND ((ARTICLE.DateDerniereCommande >Article.DateDerniereAchat) OR (Article.DateDerniereAchat is null))"

            cmdCommande.ExecuteNonQuery()

        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try


    End Sub

    Public Sub SetArticleDateDerniereCommande()

        Try
            For I = 0 To gArticle.RowCount - 1

                cmdCommande.Connection = ConnectionServeur
                cmdCommande.CommandText = "UPDATE " + _
                                        "	ARTICLE " + _
                                        "SET " + _
                                        "	ARTICLE.DateDerniereCommande=(SELECT top(1) Date from COMMANDE LEFT OUTER JOIN COMMANDE_DETAILS ON COMMANDE.NumeroCommande=COMMANDE_DETAILS.NumeroCommande LEFT OUTER JOIN FOURNISSEUR ON COMMANDE.CodeFournisseur=FOURNISSEUR.CodeFournisseur WHERE CodeArticle ='" & gArticle.Rows(I).Item("CodeArticle").ToString() & "'" & _
                                        " ORDER BY Date desc ) " + _
                                        " WHERE ARTICLE.CodeArticle ='" & gArticle.Rows(I).Item("CodeArticle").ToString() & "'"

                cmdCommande.ExecuteNonQuery()

            Next


        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try


    End Sub


    Public Sub SetArticleDateDernierAchat()

        Try
            For I = 0 To gArticle.RowCount - 1
                cmdCommande.Connection = ConnectionServeur
                cmdCommande.CommandText = "UPDATE " + _
                                        "	ARTICLE " + _
                                        "SET " + _
                                        "	ARTICLE.DateDerniereAchat=(select top 1 [date] from ACHAT_DETAILS left join ACHAT on ACHAT_DETAILS .NumeroAchat = ACHAT.NumeroAchat WHERE CodeArticle ='" & gArticle.Rows(I).Item("CodeArticle").ToString() & "'" & _
                                        " ORDER BY date desc ) " + _
                                        " WHERE ARTICLE.CodeArticle ='" & gArticle.Rows(I).Item("CodeArticle").ToString() & "'"

                cmdCommande.ExecuteNonQuery()

            Next


        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try


    End Sub

    Public Sub Init()
        Try
            Dim cmd As New SqlCommand
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "  SELECT TOP(1) ActiverBCB FROM PARAMETRE_PHARMACIE  "
            bMonographie.Visible = cmd.ExecuteScalar()
        Catch
        End Try

        Dim StrSQL As String = ""

        FeuilleInitialisee = False
        AfficherArticle(ModePreview:=True)

        FeuilleInitialisee = False

        If ModeADMIN <> "ADMIN" Then
            Label5.Visible = False
            Label6.Visible = False
            Label8.Visible = False
            Label9.Visible = False
            lTotalAchatHT.Visible = False
            lTotalVenteHT.Visible = False
            lTotalAchatTTC.Visible = False
            lTotalVenteTTC.Visible = False
        End If

        'chargement des noms Articles
        StrSQL = "SELECT DISTINCT Designation FROM ARTICLE AS ARTICLE_DESIGNATION_LISTE where Supprime=0 ORDER BY Designation ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "ARTICLE_DESIGNATION_LISTE")
        cmbDesignation.DataSource = dsArticle.Tables("ARTICLE_DESIGNATION_LISTE")
        cmbDesignation.ValueMember = "Designation"
        cmbDesignation.DisplayMember = "Designation"
        cmbDesignation.ColumnHeaders = False
        cmbDesignation.Splits(0).DisplayColumns("Designation").Width = 10
        cmbDesignation.ExtendRightColumn = True

        'chargement des Formes
        StrSQL = "SELECT DISTINCT LibelleForme,CodeForme FROM FORME_ARTICLE where SupprimeForme = 0 ORDER BY LibelleForme ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "FORME")
        cmbForme.DataSource = dsArticle.Tables("FORME")
        cmbForme.ValueMember = "CodeForme"
        cmbForme.DisplayMember = "LibelleForme"
        cmbForme.ColumnHeaders = False
        cmbForme.Splits(0).DisplayColumns("CodeForme").Width = 0
        cmbForme.Splits(0).DisplayColumns("CodeForme").Visible = False
        cmbForme.Splits(0).DisplayColumns("LibelleForme").Width = 10
        cmbForme.ExtendRightColumn = True

        'chargement des DCI
        StrSQL = "SELECT DISTINCT LibelleDCI,CodeDCI FROM DCI WHERE SupprimeDCI=0 ORDER BY LibelleDCI ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "DCI")
        cmbDCI.DataSource = dsArticle.Tables("DCI")
        cmbDCI.ValueMember = "CodeDCI"
        cmbDCI.DisplayMember = "LibelleDCI"
        cmbDCI.ColumnHeaders = False
        cmbDCI.Splits(0).DisplayColumns("CodeDCI").Visible = False
        cmbDCI.Splits(0).DisplayColumns("LibelleDCI").Width = 10
        cmbDCI.ExtendRightColumn = True

        'chargement des Labo
        StrSQL = "SELECT DISTINCT NomLabo, CodeLabo FROM LABORATOIRE WHERE SupprimeLabo = 0 ORDER BY NomLabo ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "LABO")
        cmbLabo.DataSource = dsArticle.Tables("LABO")
        cmbLabo.ValueMember = "CodeLabo"
        cmbLabo.DisplayMember = "NomLabo"
        cmbLabo.ColumnHeaders = False
        cmbLabo.Splits(0).DisplayColumns("CodeLabo").Width = 0
        cmbLabo.Splits(0).DisplayColumns("CodeLabo").Visible = False
        cmbLabo.Splits(0).DisplayColumns("NomLabo").Width = 10
        cmbLabo.ExtendRightColumn = True

        'chargement des Categories
        StrSQL = "SELECT DISTINCT LibelleCategorie,CodeCategorie FROM CATEGORIE WHERE SupprimeCategorie=0 ORDER BY LibelleCategorie ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "CATEGORIE")
        cmbCategorie.DataSource = dsArticle.Tables("CATEGORIE")
        cmbCategorie.ValueMember = "CodeCategorie"
        cmbCategorie.DisplayMember = "LibelleCategorie"
        cmbCategorie.ColumnHeaders = False
        cmbCategorie.Splits(0).DisplayColumns("CodeCategorie").Width = 0
        cmbCategorie.Splits(0).DisplayColumns("CodeCategorie").Visible = False
        cmbCategorie.Splits(0).DisplayColumns("LibelleCategorie").Width = 10
        cmbCategorie.ExtendRightColumn = True

        'chargement des Categories
        StrSQL = "SELECT 0 AS Code, 'Tous' As Libelle UNION SELECT 1 AS Code, 'Qte < 1' As Libelle UNION SELECT 2 AS Code, 'Qte = 1' As Libelle UNION SELECT 3 AS Code, 'Qte > 1' As Libelle"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "QTEUNIT")
        cmbQteUnit.DataSource = dsArticle.Tables("QTEUNIT")
        cmbQteUnit.ValueMember = "Code"
        cmbQteUnit.DisplayMember = "Libelle"
        cmbQteUnit.ColumnHeaders = False
        cmbQteUnit.Splits(0).DisplayColumns("Code").Width = 0
        cmbQteUnit.Splits(0).DisplayColumns("Code").Visible = False
        cmbQteUnit.Splits(0).DisplayColumns("Libelle").Width = 10
        cmbQteUnit.ExtendRightColumn = True

        'chargement des CodePCT
        StrSQL = "SELECT DISTINCT CodePCT FROM ARTICLE AS CODE_PCT ORDER BY CodePCT ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "CODE_PCT")
        cmbCodePCT.DataSource = dsArticle.Tables("CODE_PCT")
        cmbCodePCT.ValueMember = "CodePCT"
        cmbCodePCT.DisplayMember = "CodePCT"
        cmbCodePCT.ColumnHeaders = False
        cmbCodePCT.Splits(0).DisplayColumns("CodePCT").Width = 10
        cmbCodePCT.ExtendRightColumn = True

        'chargement des Rayons
        StrSQL = "SELECT DISTINCT Rayon FROM ARTICLE ORDER BY Rayon ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "RAYON")
        cmbRayon.DataSource = dsArticle.Tables("RAYON")
        cmbRayon.ValueMember = "Rayon"
        cmbRayon.DisplayMember = "Rayon"
        cmbRayon.ColumnHeaders = False
        cmbRayon.Splits(0).DisplayColumns("Rayon").Width = 10
        cmbRayon.ExtendRightColumn = True

        'chargement des Sections
        StrSQL = "SELECT DISTINCT Section FROM ARTICLE ORDER BY Section ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "Section")
        cmbSection.DataSource = dsArticle.Tables("Section")
        cmbSection.ValueMember = "Section"
        cmbSection.DisplayMember = "Section"
        cmbSection.ColumnHeaders = False
        cmbSection.Splits(0).DisplayColumns("Section").Width = 10
        cmbSection.ExtendRightColumn = True

        'chargement les fournisseur
        StrSQL = "SELECT DISTINCT CodeFournisseur, NomFournisseur FROM FOURNISSEUR WHERE Supprimer <> 1 ORDER BY NomFournisseur ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "Fournisseur")
        cmbFournisseur.DataSource = dsArticle.Tables("Fournisseur")
        cmbFournisseur.ValueMember = "CodeFournisseur"
        cmbFournisseur.DisplayMember = "NomFournisseur"
        cmbFournisseur.ColumnHeaders = False
        cmbFournisseur.Splits(0).DisplayColumns("CodeFournisseur").Width = 0
        cmbFournisseur.Splits(0).DisplayColumns("CodeFournisseur").Visible = False
        cmbFournisseur.Splits(0).DisplayColumns("NomFournisseur").Width = 10
        cmbFournisseur.ExtendRightColumn = True

        rdbTousLesArticles.Checked = True
        rdbTousArticlesSuspendus.Checked = True

        x = 0
        tNonMouvementees.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        tNonMouvementees.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        FeuilleInitialisee = True
        AfficherArticle(ModePreview:=False)
        cmbDesignation.Focus()
    End Sub

    Public Sub AfficherArticle(ModePreview As Boolean)

        Dim I As Integer
        Dim Cond As String = "1=1"
        Dim StockArticle As Integer = 0

        Dim QteUnitaireArticle As Integer = 1

        TotalAchatHT = 0.0
        TotalAchatTTC = 0.0
        TotalVenteHT = 0.0
        TotalVenteTTC = 0.0

        If ModePreview Then
            Cond = "1=0"
        Else
            If Not FeuilleInitialisee Then Exit Sub
            If (dsArticle.Tables.IndexOf("Article") > -1) Then
                dsArticle.Tables("Article").Clear()
            End If

            'Composer la condition de la requête  

            If cmbQteUnit.SelectedValue <> 0 Then
                If cmbQteUnit.SelectedValue = 1 Then
                    Cond += " AND ARTICLE.QuantiteUnitaire < 1 "
                End If
                If cmbQteUnit.SelectedValue = 2 Then
                    Cond += " AND ARTICLE.QuantiteUnitaire = 1 "
                End If
                If cmbQteUnit.SelectedValue = 3 Then
                    Cond += " AND ARTICLE.QuantiteUnitaire > 1 "
                End If
            End If

            If cmbLabo.Text <> "" Then
                Cond += " AND ARTICLE.CodeLabo LIKE " + Quote(cmbLabo.SelectedValue)
            End If

            If cmbFournisseur.Text <> "" Then
                Cond += " AND ARTICLE.CodeFournisseur = " + Quote(cmbFournisseur.SelectedValue)
            End If

            If tCode.Text <> "" Then
                Cond += " AND ARTICLE.CodeABarre LIKE " + Quote(tCode.Text)
            End If

            If cmbDCI.Text <> "" Then
                Cond += " AND (DCI1Libelle.LibelleDCI = " + Quote(cmbDCI.Text) + " OR DCI2Libelle.LibelleDCI = " + Quote(cmbDCI.Text) + " OR DCI3Libelle.LibelleDCI = " + Quote(cmbDCI.Text) + ")"
            End If

            If cmbDesignation.Text <> "" Then
                Cond += " AND Designation LIKE " + Quote(cmbDesignation.Text + "%")
            End If

            If cmbForme.Text <> "" Then
                Cond += " AND FORME_ARTICLE.LibelleForme LIKE " + Quote(cmbForme.Text)
            End If

            If cmbCodePCT.Text <> "" Then
                Cond += " AND CodePCT LIKE " + Quote(cmbCodePCT.Text)
            End If

            If cmbCategorie.Text <> "" Then
                Cond += " AND CATEGORIE.LibelleCategorie LIKE " + Quote(cmbCategorie.Text)
            End If

            If cmbRayon.Text <> "" Then
                Cond += " AND RAYON LIKE " + Quote(cmbRayon.Text)
            End If

            If cmbSection.Text <> "" Then
                Cond += " AND ARTICLE.Section LIKE " + Quote(cmbSection.Text)
            End If

            If rdbStockSup0.Checked = True Then
                Cond += " AND (SELECT SUM(QteLotArticle) AS Expr1 FROM dbo.LOT_ARTICLE AS LOT_ARTICLE_1 " + _
                        "WHERE (CodeArticle = dbo.ARTICLE.CodeArticle))>0 "

            ElseIf rdbStockInf0.Checked = True Then
                Cond += " AND (SELECT SUM(QteLotArticle) AS Expr1 FROM dbo.LOT_ARTICLE AS LOT_ARTICLE_1 " + _
                       "WHERE (CodeArticle = dbo.ARTICLE.CodeArticle))<0  "

            ElseIf rdbStockEgal0.Checked = True Then
                Cond += " AND ((SELECT SUM(QteLotArticle) AS Expr1 FROM dbo.LOT_ARTICLE AS LOT_ARTICLE_1 " + _
                       "WHERE (CodeArticle = dbo.ARTICLE.CodeArticle))=0 OR   " + _
                       "(SELECT COUNT(*) FROM LOT_ARTICLE WHERE dbo.LOT_ARTICLE.CodeArticle = dbo.ARTICLE.CodeArticle)=0)"

            End If

            ' AND (DatePeremptionArticle > GETDATE() OR  DatePeremptionArticle IS NULL OR DatePeremptionArticle = '01/01/1900')

            If rdbSuspendus.Checked Then
                Cond += " AND ARTICLE.CodeSituation = 3 "
            ElseIf rdbNonSuspendus.Checked = True Then
                Cond += " AND (ARTICLE.CodeSituation <> 3 OR ARTICLE.CodeSituation IS NULL) "
            End If

            If chbNonMouvementees.Checked = True Then
                If tNonMouvementees.Text = "" Then
                    MsgBox("Veuillez saisir la date de base pour la détermination des articles non vendus !", MsgBoxStyle.Critical, "Erreur")
                    tNonMouvementees.Focus()
                    Exit Sub
                End If
                Cond += " AND (((SELECT MAX(dbo.VENTE.Date)  FROM dbo.VENTE INNER JOIN dbo.VENTE_DETAILS ON dbo.VENTE.NumeroVente = dbo.VENTE_DETAILS.NumeroVente WHERE  (dbo.VENTE_DETAILS.CodeArticle = dbo.ARTICLE.CodeArticle)) <='" + tNonMouvementees.Text + "') OR  ((SELECT MAX(dbo.VENTE.Date)  FROM dbo.VENTE INNER JOIN dbo.VENTE_DETAILS ON dbo.VENTE.NumeroVente = dbo.VENTE_DETAILS.NumeroVente WHERE  (dbo.VENTE_DETAILS.CodeArticle = dbo.ARTICLE.CodeArticle)) IS NULL))"

            End If

        End If

        cmdArticle.CommandText = "SELECT  ARTICLE.CodeArticle," + _
                                        " CodeABarre," + _
                                        " CodePCT, " + _
                                        " Designation,  " + _
                                        " CASE WHEN Stock IS NULL THEN 0 ELSE Stock END as STock, " + _
                                        " Article.CodeCategorie ," + _
                                        " DatePeremption.DatePeremptionArticle ," + _
                                        " PrixVenteHT, " + _
                                        " TVA,  " + _
                                        " PrixVenteTTC, " + _
                                        " Marge, " + _
                                        " PrixAchatHT," + _
                                        " PrixAchatTTC," + _
                                        " LibelleTableau,  " + _
                                        " CodeTypePreparation,  " + _
                                        " FORME_ARTICLE.LibelleForme, " + _
                                        " CATEGORIE.LibelleCategorie, " + _
                                        " Exonorertva, " + _
                                        " Section, " + _
                                        " RAYON, " + _
                                        " StockAlerte," + _
                                        " QteACommander, " + _
                                        " LABORATOIRE.NomLabo, " + _
                                        " SITUATION_ARTICLE.LibelleSituationArticle," + _
                                        " CATEGORIE_CNAM.LibelleCategorie as CatCNAM," + _
                                        " AccordPrealable, " + _
                                        " PriseEnCharge," + _
                                        " TarifDeReference," + _
                                        " DCI1Libelle.LibelleDCI as DCI1," + _
                                        " DCI2Libelle.LibelleDCI as DCI2," + _
                                        " DCI3Libelle.LibelleDCI as DCI3, " + _
                                        " case when Article.QuantiteUnitaire is null or QuantiteUnitaire = 0 then 1 else QuantiteUnitaire end as QuantiteUnitaire " + _
                                        " FROM Article" + _
                                        " LEFT OUTER JOIN FORME_ARTICLE ON FORME_ARTICLE.CodeForme=Article.CodeForme " + _
                                        " LEFT OUTER JOIN SITUATION_ARTICLE ON SITUATION_ARTICLE.CodeSituationArticle=ARTICLE.CodeSituation " + _
                                        " LEFT OUTER JOIN LABORATOIRE ON LABORATOIRE.CodeLabo=ARTICLE.CodeLabo " + _
                                        " LEFT OUTER JOIN CATEGORIE_CNAM ON ARTICLE.CodeCategorieCNAM=CATEGORIE_CNAM.CodeCategorie " + _
                                        " LEFT OUTER JOIN CATEGORIE ON CATEGORIE.CodeCategorie=ARTICLE.CodeCategorie " + _
                                        " LEFT OUTER JOIN DCI as DCI1Libelle on ARTICLE .DCI1=DCI1Libelle.CodeDCI " + _
                                        " LEFT OUTER JOIN DCI as DCI2Libelle on ARTICLE .DCI2=DCI2Libelle.CodeDCI " + _
                                        " LEFT OUTER JOIN DCI as DCI3Libelle on ARTICLE .DCI3=DCI3Libelle.CodeDCI" + _
                                        " LEFT OUTER JOIN (SELECT CodeArticle, MAX(DatePeremptionArticle) AS DatePeremptionArticle FROM LOT_ARTICLE GROUP BY CodeArticle) AS DatePeremption ON DatePeremption.CodeArticle = Article.CodeArticle " + _
                                        " LEFT OUTER JOIN " + _
                                        " (SELECT  CodeArticle, SUM(QteLotArticle) AS Stock " + _
                                        " FROM dbo.LOT_ARTICLE " + _
                                        " GROUP BY CodeArticle) AS StockArticle ON dbo.ARTICLE.CodeArticle = StockArticle.CodeArticle " + _
                                        " WHERE " + Cond + " AND Supprime=0" + _
                                        " ORDER BY Designation"

        cmdArticle.Connection = ConnectionServeur
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "ARTICLE")

        With gArticle
            .Columns.Clear()
            .DataSource = dsArticle
            .DataMember = "ARTICLE"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code Article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("Rayon").Caption = "Rayon"
            .Columns("DatePeremptionArticle").Caption = "Date Peremption"
            .Columns("CodePCT").Caption = "Code PCT"
            .Columns("PrixVenteHT").Caption = "Prix Vente HT"
            .Columns("TVA").Caption = "TVA"
            .Columns("PrixVenteTTC").Caption = "Prix Vente TTC"
            .Columns("PrixAchatHT").Caption = "Prix Achat HT"
            .Columns("PrixAchatTTC").Caption = "Prix Achat TTC"
            .Columns("LibelleTableau").Caption = "Tableau"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("NomLabo").Caption = "Laboratoire"
            .Columns("LibelleSituationArticle").Caption = "Situation"
            .Columns("LibelleCategorie").Caption = "Catégorie"
            .Columns("Section").Caption = "Section"
            .Columns("Stock").Caption = "Stock"
            .Columns("CatCNAM").Caption = "Cat CNAM"
            .Columns("AccordPrealable").Caption = "Accord Préalable"
            .Columns("PriseEnCharge").Caption = "Prise en charge"
            .Columns("TarifDeReference").Caption = "Tarif de référence"
            .Columns("StockAlerte").Caption = "Stock d'alerte"
            .Columns("QteACommander").Caption = "Quantité à commander"
            .Columns("DCI1").Caption = "DCI 1"
            .Columns("DCI2").Caption = "DCI 2"
            .Columns("DCI3").Caption = "DCI 3"

            '' ''.Columns("Stock").NumberFormat = "#"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("DatePeremptionArticle").Width = 220
            .Splits(0).DisplayColumns("CodeABarre").Width = 180
            .Splits(0).DisplayColumns("Designation").Width = 300
            .Splits(0).DisplayColumns("Rayon").Width = 50
            .Splits(0).DisplayColumns("Rayon").Visible = False
            .Splits(0).DisplayColumns("CodePCT").Visible = False
            .Splits(0).DisplayColumns("CodeTypePreparation").Visible = False
            .Splits(0).DisplayColumns("Exonorertva").Visible = False
            .Splits(0).DisplayColumns("PrixVenteHT").Visible = False
            .Splits(0).DisplayColumns("TVA").Visible = False
            .Splits(0).DisplayColumns("PrixVenteTTC").Visible = False
            .Splits(0).DisplayColumns("LibelleTableau").Visible = False
            .Splits(0).DisplayColumns("Stock").Visible = False
            .Splits(0).DisplayColumns("LibelleForme").Visible = False
            .Splits(0).DisplayColumns("NomLabo").Visible = False
            .Splits(0).DisplayColumns("LibelleSituationArticle").Visible = False
            .Splits(0).DisplayColumns("LibelleCategorie").Visible = False
            .Splits(0).DisplayColumns("Section").Visible = False
            .Splits(0).DisplayColumns("CatCNAM").Visible = False
            .Splits(0).DisplayColumns("AccordPrealable").Visible = False
            .Splits(0).DisplayColumns("PriseEnCharge").Visible = False
            .Splits(0).DisplayColumns("TarifDeReference").Visible = False
            .Splits(0).DisplayColumns("StockAlerte").Visible = False
            .Splits(0).DisplayColumns("QteACommander").Visible = False
            .Splits(0).DisplayColumns("DCI1").Visible = False
            .Splits(0).DisplayColumns("DCI2").Visible = False
            .Splits(0).DisplayColumns("DCI3").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
            .Splits(0).DisplayColumns("PrixAchatHT").Visible = False
            .Splits(0).DisplayColumns("PrixAchatTTC").Visible = False
            .Splits(0).DisplayColumns("CodeCategorie").Visible = False
            .Splits(0).DisplayColumns("QuantiteUnitaire").Visible = False

            .Splits(0).SplitSize = 2
            .Splits(0).SplitSizeMode = SizeModeEnum.NumberOfColumns
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True

            '********************************* le deuxieme split 
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(1).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                .Splits(1).DisplayColumns(I).Locked = True
            Next

            .Splits(1).DisplayColumns("CodeArticle").Width = 0
            .Splits(1).DisplayColumns("CodeABarre").Width = 150
            .Splits(1).DisplayColumns("DatePeremptionArticle").Width = 120
            .Splits(1).DisplayColumns("CodeABarre").Visible = False
            .Splits(1).DisplayColumns("Designation").Width = 300
            .Splits(1).DisplayColumns("Designation").Visible = False
            .Splits(1).DisplayColumns("CodeTypePreparation").Visible = False
            .Splits(1).DisplayColumns("Rayon").Width = 50
            .Splits(1).DisplayColumns("Rayon").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("CodePCT").Width = 80
            .Splits(1).DisplayColumns("CodePCT").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("PrixVenteHT").Width = 80
            .Splits(1).DisplayColumns("TVA").Width = 70
            .Splits(1).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(1).DisplayColumns("PrixVenteTTC").Width = 80
            .Splits(1).DisplayColumns("LibelleTableau").Width = 60
            .Splits(1).DisplayColumns("LibelleTableau").Style.HorizontalAlignment = AlignHorzEnum.Center

            .Splits(1).DisplayColumns("Stock").Width = 60
            .Splits(1).DisplayColumns("Stock").Style.Font = New Font(.Font, FontStyle.Bold)
            .Splits(1).DisplayColumns("Stock").Style.HorizontalAlignment = AlignHorzEnum.Center

            .Splits(1).DisplayColumns("LibelleForme").Width = 120
            .Splits(1).DisplayColumns("NomLabo").Width = 120
            .Splits(1).DisplayColumns("NomLabo").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("LibelleSituationArticle").Width = 120
            .Splits(1).DisplayColumns("LibelleSituationArticle").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("LibelleCategorie").Width = 120
            .Splits(1).DisplayColumns("LibelleCategorie").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("Section").Width = 60
            .Splits(1).DisplayColumns("Exonorertva").Width = 40
            .Splits(1).DisplayColumns("LibelleTableau").Width = 40
            .Splits(1).DisplayColumns("Section").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(1).DisplayColumns("CatCNAM").Width = 120
            .Splits(1).DisplayColumns("CatCNAM").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("AccordPrealable").Width = 80
            .Splits(1).DisplayColumns("AccordPrealable").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(1).DisplayColumns("PriseEnCharge").Width = 80
            .Splits(1).DisplayColumns("PriseEnCharge").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(1).DisplayColumns("TarifDeReference").Width = 80
            .Splits(1).DisplayColumns("TarifDeReference").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("StockAlerte").Width = 80
            .Splits(1).DisplayColumns("StockAlerte").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(1).DisplayColumns("QteACommander").Width = 80
            .Splits(1).DisplayColumns("QteACommander").Style.HorizontalAlignment = AlignHorzEnum.Center

            .Splits(1).DisplayColumns("DCI1").Width = 200
            .Splits(1).DisplayColumns("DCI2").Width = 200
            .Splits(1).DisplayColumns("DCI3").Width = 200

            .Splits(1).DisplayColumns("CodeArticle").Visible = False
            .Splits(1).DisplayColumns("CodeArticle").AllowSizing = False
            .Splits(1).DisplayColumns("PrixAchatHT").Width = 80
            .Splits(1).DisplayColumns("PrixAchatTTC").Width = 80

            .Splits(1).DisplayColumns("CodeCategorie").Visible = False
            .Splits(1).DisplayColumns("QuantiteUnitaire").Visible = False

            .Splits(1).ColumnCaptionHeight = 40
            .Splits(1).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .MarqueeStyle = MarqueeEnum.HighlightRowRaiseCell

            If ModeADMIN = "ADMIN" Then
                .Splits(0).DisplayColumns("Marge").Visible = True
                .Splits(1).DisplayColumns("Marge").Visible = True
            Else
                .Splits(0).DisplayColumns("Marge").Visible = False
                .Splits(1).DisplayColumns("Marge").Visible = False
            End If

            'Style du Caractere et du grid
            ParametreGrid(gArticle)
        End With

        gArticle.MoveRelative(x)

        For I = 0 To gArticle.RowCount - 1
        

            'If dsArticle.Tables("ARTICLE").Rows(I).Item("CodeCategorie") = 8 Or dsArticle.Tables("ARTICLE").Rows(I).Item("CodeCategorie") = 9 Then
            '    QteUnitaireArticle = dsArticle.Tables("ARTICLE").Rows(I).Item("QuantiteUnitaire")
            '    If QteUnitaireArticle = 0 Then
            '        QteUnitaireArticle = 1
            '    End If
            'Else
            '    QteUnitaireArticle = 1
            'End If
            '(cmbCategorie.Text = "PREPARATION" And cmbPreparation.Text = "PHARMACEUTIQUE")
            If dsArticle.Tables("ARTICLE").Rows(I).Item("LibelleCategorie").ToString() = "PRODUITS CHIMIQUES" Or (dsArticle.Tables("ARTICLE").Rows(I).Item("LibelleCategorie").ToString() = "PREPARATION" And dsArticle.Tables("ARTICLE").Rows(I).Item("CodeTypePreparation").ToString() = "1") Then
                TotalAchatHT += dsArticle.Tables("ARTICLE").Rows(I).Item("PrixAchatHT") * dsArticle.Tables("ARTICLE").Rows(I).Item("Stock") / dsArticle.Tables("ARTICLE").Rows(I).Item("QuantiteUnitaire")
                TotalAchatTTC += dsArticle.Tables("ARTICLE").Rows(I).Item("PrixAchatTTC") * dsArticle.Tables("ARTICLE").Rows(I).Item("Stock") / dsArticle.Tables("ARTICLE").Rows(I).Item("QuantiteUnitaire")
                TotalVenteHT += dsArticle.Tables("ARTICLE").Rows(I).Item("PrixVenteHT") * dsArticle.Tables("ARTICLE").Rows(I).Item("Stock") / dsArticle.Tables("ARTICLE").Rows(I).Item("QuantiteUnitaire")
                TotalVenteTTC += dsArticle.Tables("ARTICLE").Rows(I).Item("PrixVenteTTC") * dsArticle.Tables("ARTICLE").Rows(I).Item("Stock") / dsArticle.Tables("ARTICLE").Rows(I).Item("QuantiteUnitaire")
            Else
                TotalAchatHT += dsArticle.Tables("ARTICLE").Rows(I).Item("PrixAchatHT") * dsArticle.Tables("ARTICLE").Rows(I).Item("Stock")
                TotalAchatTTC += dsArticle.Tables("ARTICLE").Rows(I).Item("PrixAchatTTC") * dsArticle.Tables("ARTICLE").Rows(I).Item("Stock")
                TotalVenteHT += dsArticle.Tables("ARTICLE").Rows(I).Item("PrixVenteHT") * dsArticle.Tables("ARTICLE").Rows(I).Item("Stock")
                TotalVenteTTC += dsArticle.Tables("ARTICLE").Rows(I).Item("PrixVenteTTC") * dsArticle.Tables("ARTICLE").Rows(I).Item("Stock")
            End If


        Next
        lTotalAchatHT.Text = Math.Round(TotalAchatHT, 3).ToString("### ### ##0.000") 'dsArticle.Tables("ARTICLE").AsEnumerable().Sum(Function(Item) Math.Round(Item.Item("PrixAchatHT") * Item.Item("Stock") / Item.Item("QuantiteUnitaire"), 3)).ToString("### ### ##0.000")
        lTotalAchatTTC.Text = Math.Round(TotalAchatTTC, 3).ToString("### ### ##0.000")
        lTotalVenteHT.Text = Math.Round(TotalVenteHT, 3).ToString("### ### ##0.000")
        lTotalVenteTTC.Text = Math.Round(TotalVenteTTC, 3).ToString("### ### ##0.000")

        lNbreDesArticles.Text = dsArticle.Tables("ARTICLE").Rows.Count.ToString + " Articles"
        Application.DoEvents()

    End Sub

    Private Sub bAjouterArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterArticle.Click

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(2, "AJOUT_ARTICLE") = "False" Then
            Exit Sub
        End If

        Dim MyFicheArticle As New fFicheArticle
        MyFicheArticle.DesignationArticle = "-"
        MyFicheArticle.ajoutmodif = "A"
        MyFicheArticle.Init()
        MyFicheArticle.ShowDialog()
        MyFicheArticle.Close()
        MyFicheArticle.Dispose()
        AfficherArticle(ModePreview:=False)

        'chargement des noms Articles
        cmdArticle.CommandText = "SELECT DISTINCT Designation FROM ARTICLE as ARTICLE_DESIGNATION_LISTE where Supprime=0 ORDER BY Designation ASC"
        cmdArticle.Connection = ConnectionServeur
        daArticle = New SqlDataAdapter(cmdArticle)
        If (dsArticle.Tables.IndexOf("ARTICLE_DESIGNATION_LISTE") > -1) Then
            dsArticle.Tables("ARTICLE_DESIGNATION_LISTE").Clear()
        End If

        daArticle.Fill(dsArticle, "ARTICLE_DESIGNATION_LISTE")
        cmbDesignation.DataSource = dsArticle.Tables("ARTICLE_DESIGNATION_LISTE")
        cmbDesignation.DisplayMember = "Designation"
        cmbDesignation.ColumnHeaders = False
        cmbDesignation.Splits(0).DisplayColumns("Designation").Width = 10
        cmbDesignation.ExtendRightColumn = True

    End Sub

    Private Sub bModifierArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifierArticle.Click
        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""
        Dim StrSQL As String = ""
        Dim Stock As Integer = 0

        If ControleDAcces(2, "MODIFICATION_ARTICLE") = "False" Then
            Exit Sub
        End If

        If gArticle(gArticle.Row, "Stock").ToString = "" Then
            Exit Sub
        End If

        Dim MyFicheArticle As New fFicheArticle
        MyFicheArticle.CodeArticle = gArticle(gArticle.Row, "CodeArticle")

        Dim rowArticle As String = gArticle(gArticle.Row, "CodeArticle") 'gArticle.Row

        StrSQL = "SELECT SUM(QteLotArticle) AS Stock FROM dbo.LOT_ARTICLE WHERE CodeArticle='" + _
                 gArticle(gArticle.Row, "CodeArticle") + "'"

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL
        Try
            Stock = cmd.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try


        MyFicheArticle.StockArticle = Stock ' gArticle(gArticle.Row, "Stock")
        MyFicheArticle.DesignationArticle = gArticle(gArticle.Row, "Designation")
        MyFicheArticle.ajoutmodif = "M"

        MyFicheArticle.Init()

        MyFicheArticle.ShowDialog()
        MyFicheArticle.Close()
        MyFicheArticle.Dispose()

        'AfficherArticle(ModePreview:=False)
        Try
            gArticle(gArticle.Row, "CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "CodePCT") = RecupererValeurExecuteScalaire("CodePCT", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "PrixVenteHT") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "LibelleTableau") = RecupererValeurExecuteScalaire("LibelleTableau", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "Section") = RecupererValeurExecuteScalaire("Section", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "Exonorertva") = RecupererValeurExecuteScalaire("Exonorertva", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "CatCNAM") = RecupererValeurExecuteScalaire("CatCNAM", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "AccordPrealable") = RecupererValeurExecuteScalaire("AccordPrealable", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "PriseEnCharge") = RecupererValeurExecuteScalaire("PriseEnCharge", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "TarifDeReference") = RecupererValeurExecuteScalaire("TarifDeReference", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "QteACommander") = RecupererValeurExecuteScalaire("QteACommander", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "StockAlerte") = RecupererValeurExecuteScalaire("StockAlerte", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))

        Catch
        End Try

        For i = 0 To gArticle.RowCount - 1
            If gArticle(i, "CodeArticle") = rowArticle Then
                rowArticle = i
                Exit For
            End If
        Next

        gArticle.Row = rowArticle

        ''gArticle.Rows.Find(rowArticle)
        'Dim ss(0) As System.Data.DataColumn
        'dsArticle.Tables("ARTICLE").PrimaryKey = ss

        'dsArticle.Tables("ARTICLE").Rows.Find(rowArticle)

    End Sub

    Private Sub bSuprimerArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSuprimerArticle.Click

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""
        Connect()

        Dim CodeArticleASupprimer As String = ""
        Dim DesignationArticleASupprimer As String = ""

        CodeArticleASupprimer = gArticle(gArticle.Row, "CodeArticle")
        DesignationArticleASupprimer = gArticle(gArticle.Row, "Designation")

        If gArticle(gArticle.Row, "Stock") <> 0 Then
            MsgBox("Cet article admet un stock. Sa suppression est impossible !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        If gArticle.RowCount > 0 Then
            If MsgBox("Voulez vous vraiment supprimer l'article " + gArticle(gArticle.Row, "Designation") + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then

                'If CodeOperateur = ControleDAcces(2, "SUPPRESSION_ARTICLE") = "False" Then
                '    Exit Sub
                'Else
                '    CodeOperateur = ControleDAcces(2, "SUPPRESSION_ARTICLE")
                'End If

                CodeOperateur = ControleDAcces(2, "SUPPRESSION_ARTICLE")
                If CodeOperateur = "False" Then
                    Exit Sub
                End If
                If CodeOperateur = "" Then
                    CodeOperateur = CodeUtilisateur
                End If

                Dim rowArticle As Int64 = gArticle.Row

                Try
                    cmd.Connection = ConnectionServeur
                    'cmd.CommandText = "DELETE FROM article WHERE Codearticle = " + Quote(gArticle(gArticle.Row, "CodeArticle"))
                    cmd.CommandText = "UPDATE ARTICLE SET Supprime=1 WHERE Codearticle = " + Quote(CodeArticleASupprimer)
                    cmd.ExecuteNonQuery()
                    cmd.CommandText = "UPDATE ARTICLE SET CodeABarre='' WHERE Codearticle = " + Quote(CodeArticleASupprimer)
                    cmd.ExecuteNonQuery()
                    cmd.CommandText = "UPDATE ARTICLE SET CodePCT='' WHERE Codearticle = " + Quote(CodeArticleASupprimer)
                    cmd.ExecuteNonQuery()
                    InsertionDansLog("SUPPRESSION_ARTICLE", "La Suppression de l article " + DesignationArticleASupprimer, CodeOperateur, System.DateTime.Now, "ARTICLE", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
                    AfficherArticle(ModePreview:=False)

                    gArticle.Row = rowArticle

                    ModuleSurveillance(3, "L'utilisateur " & NomUtilisateur & " a supprimé l'article " & DesignationArticleASupprimer)
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub

    Private Sub bRechercher_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRechercher.Click
        AfficherArticle(ModePreview:=False)
    End Sub

    Private Sub bViderLesChamps_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bViderLesChamps.Click
        cmbCategorie.Text = ""
        cmbCodePCT.Text = ""
        cmbDesignation.Text = ""
        cmbForme.Text = ""
        cmbRayon.Text = ""
        cmbSection.Text = ""
        tCode.Text = ""
        chMasquerStock.Checked = False
        chbNonMouvementees.Checked = False
        rdbTousLesArticles.Checked = True
        rdbTousArticlesSuspendus.Checked = True
        AfficherArticle(ModePreview:=False)
    End Sub

    Private Sub cmbDesignation_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbDesignation.KeyUp
        'Recherche_Automatique_liste(e, cmbDesignation, cmbDesignation.Columns("Designation"))
        If e.KeyCode = Keys.Enter Then
            cmbDesignation.Text = cmbDesignation.WillChangeToText

            'Dim myKey As String = RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "Designation", cmbDesignation.Text)
            'Dim ind As Integer = 0

            ''gArticle.Rows.Find(myKey)

            'For i = 0 To gArticle.RowCount - 1
            '    If gArticle(i, "Designation") = cmbDesignation.Text Then
            '        ind = i
            '        Exit For
            '    End If
            'Next
            'gArticle.SetActiveCell(ind, 0)

            AfficherArticle(ModePreview:=False)
            cmbCodePCT.Focus()
        Else
            'cmbDesignation.OpenCombo()
            AfficherArticle(ModePreview:=False)

        End If
    End Sub

    Private Sub cmbForme_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbForme.KeyUp
        'Recherche_Automatique_liste(e, cmbForme, cmbForme.Columns("LibelleForme"))
        If e.KeyCode = Keys.Enter Then
            cmbForme.Text = cmbForme.WillChangeToText
            AfficherArticle(ModePreview:=False)
            cmbCategorie.Focus()
        Else
            cmbForme.OpenCombo()
        End If
    End Sub

    Private Sub cmbForme_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbForme.TextChanged

    End Sub

    Private Sub cmbCodePCT_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCodePCT.KeyUp
        'Recherche_Automatique_liste(e, cmbCodePCT, cmbCodePCT.Columns("CodePCT"))
        If e.KeyCode = Keys.Enter Then
            cmbCodePCT.Text = cmbCodePCT.WillChangeToText
            AfficherArticle(ModePreview:=False)
            cmbForme.Focus()
        Else
            cmbCodePCT.OpenCombo()
        End If
    End Sub

    Private Sub cmbCodePCT_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbCodePCT.TextChanged

    End Sub

    Private Sub cmbCategorie_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCategorie.KeyUp
        'Recherche_Automatique_liste(e, cmbCategorie, cmbCategorie.Columns("LibelleCategorie"))
        If e.KeyCode = Keys.Enter Then
            cmbCategorie.Text = cmbCategorie.WillChangeToText
            AfficherArticle(ModePreview:=False)
            cmbDCI.Focus()
        Else
            cmbCategorie.OpenCombo()
        End If
    End Sub

    Private Sub gArticle_AfterSort(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FilterEventArgs) Handles gArticle.AfterSort
        Try

            VOrderBy = e.Condition.Substring(0, e.Condition.IndexOf(" "))
            VAscDesc = e.Condition.Substring(e.Condition.IndexOf(" ") + 1, e.Condition.Length - e.Condition.IndexOf(" ") - 1)
        Catch ex As Exception
            VOrderBy = e.Condition
            VAscDesc = "Asc"
        End Try
    End Sub

    Private Sub gArticle_BeforeColEdit(sender As Object, e As C1.Win.C1TrueDBGrid.BeforeColEditEventArgs) Handles gArticle.BeforeColEdit

    End Sub

    Private Sub gArticle_DoubleClick(sender As Object, e As System.EventArgs) Handles gArticle.DoubleClick
        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""
        Dim StrSQL As String = ""
        Dim Stock As Integer = 0

        If ControleDAcces(2, "MODIFICATION_ARTICLE") = "False" Then
            Exit Sub
        End If

        If gArticle(gArticle.Row, "Stock").ToString = "" Then
            Exit Sub
        End If

        Dim MyFicheArticle As New fFicheArticle
        MyFicheArticle.CodeArticle = gArticle(gArticle.Row, "CodeArticle")

        Dim rowArticle As Int64 = 0
        Try
            rowArticle = gArticle(gArticle.Row, "CodeArticle")
        Catch
        End Try


        StrSQL = "SELECT SUM(QteLotArticle) AS Stock FROM dbo.LOT_ARTICLE WHERE CodeArticle='" + _
                 gArticle(gArticle.Row, "CodeArticle") + "'"

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL
        Try
            Stock = cmd.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try


        MyFicheArticle.StockArticle = Stock ' gArticle(gArticle.Row, "Stock")
        MyFicheArticle.DesignationArticle = gArticle(gArticle.Row, "Designation")
        MyFicheArticle.ajoutmodif = "M"

        MyFicheArticle.Init()

        MyFicheArticle.ShowDialog()
        MyFicheArticle.Close()
        MyFicheArticle.Dispose()

        'AfficherArticle(ModePreview:=False)
        Try
            gArticle(gArticle.Row, "CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "CodePCT") = RecupererValeurExecuteScalaire("CodePCT", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "PrixVenteHT") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "LibelleTableau") = RecupererValeurExecuteScalaire("LibelleTableau", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "Section") = RecupererValeurExecuteScalaire("Section", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "Exonorertva") = RecupererValeurExecuteScalaire("Exonorertva", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "CatCNAM") = RecupererValeurExecuteScalaire("CatCNAM", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "AccordPrealable") = RecupererValeurExecuteScalaire("AccordPrealable", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "PriseEnCharge") = RecupererValeurExecuteScalaire("PriseEnCharge", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "TarifDeReference") = RecupererValeurExecuteScalaire("TarifDeReference", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "QteACommander") = RecupererValeurExecuteScalaire("QteACommander", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "StockAlerte") = RecupererValeurExecuteScalaire("StockAlerte", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
            gArticle(gArticle.Row, "PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))

        Catch
        End Try

        For i = 0 To gArticle.RowCount - 1
            Try
                If gArticle(i, "CodeArticle") = rowArticle Then
                    rowArticle = i
                    Exit For
                End If
            Catch
            End Try

        Next

        gArticle.Row = rowArticle
    End Sub

    Private Sub gArticle_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gArticle.FetchRowStyle

        SetArticleDateDernierAchat()
        SetArticleDateDerniereCommande()
        ArticleMAnquant()

        ' Afficher en rouge les lignes où le stock est négatif
        If gArticle.Columns("Stock").CellText(e.Row).ToString <> "" Then
            If gArticle.Columns("Stock").CellText(e.Row) <= 0 Then ' .ToString <> "" Then
                e.CellStyle.BackColor = Color.FromArgb(255, 220, 220)
                Exit Sub
            End If
        Else
            e.CellStyle.BackColor = Color.FromArgb(255, 220, 220)
        End If

        ' afficher en jaune les lignes où les articles sont en cours de commande
        Dim cmd As New SqlCommand
        Dim StrSQL As String = ""
        Dim TestEnCoursOuNN As Integer = 0

        StrSQL = "SELECT count(CodeArticle) FROM COMMANDE_DETAILS LEFT OUTER JOIN COMMANDE ON COMMANDE_DETAILS.NumeroCommande=COMMANDE.NumeroCommande" + _
                    " WHERE NumeroFacture is NULL " + " AND CodeArticle='" + gArticle.Columns("CodeArticle").CellText(e.Row) + "'"

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL

        Try
            TestEnCoursOuNN = cmd.ExecuteScalar()
            If TestEnCoursOuNN > 0 Then
                e.CellStyle.BackColor = Color.FromArgb(250, 250, 200)
                Exit Sub
            End If
        Catch ex As Exception
            MsgBox("Erreur de calcul des commandes en cours : " + ex.Message, MsgBoxStyle.Critical, "Erreur")
        End Try

    End Sub

    Private Sub gArticle_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles gArticle.KeyUp

        If e.KeyCode = Keys.F1 Then
            Dim cmd As New SqlCommand
            Dim DemandePotDePasse As Boolean = False
            Dim ConfirmerEnregistrer As Boolean = False
            Dim CodeOperateur As String = ""
            Dim StrSQL As String = ""
            Dim Stock As Integer = 0

            If ControleDAcces(2, "MODIFICATION_ARTICLE") = "False" Then
                Exit Sub
            End If

            If gArticle(gArticle.Row, "Stock").ToString = "" Then
                Exit Sub
            End If

            Dim MyFicheArticle As New fFicheArticle
            MyFicheArticle.CodeArticle = gArticle(gArticle.Row, "CodeArticle")

            Dim rowArticle As Int64 = gArticle(gArticle.Row, "CodeArticle") 'gArticle.Row

            StrSQL = "SELECT SUM(QteLotArticle) AS Stock FROM dbo.LOT_ARTICLE WHERE CodeArticle='" + _
                     gArticle(gArticle.Row, "CodeArticle") + "'"

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            Try
                Stock = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try


            MyFicheArticle.StockArticle = Stock ' gArticle(gArticle.Row, "Stock")
            MyFicheArticle.DesignationArticle = gArticle(gArticle.Row, "Designation")
            MyFicheArticle.ajoutmodif = "M"

            MyFicheArticle.Init()

            MyFicheArticle.ShowDialog()
            MyFicheArticle.Close()
            MyFicheArticle.Dispose()

            AfficherArticle(ModePreview:=False)

            For i = 0 To gArticle.RowCount - 1
                If gArticle(i, "CodeArticle") = rowArticle Then
                    rowArticle = i
                    Exit For
                End If
            Next

            gArticle.Row = rowArticle
        End If

    End Sub

    Private Sub gArticle_UnboundColumnFetch(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gArticle.UnboundColumnFetch
        Dim y As String
        y = gArticle(e.Row, ("CodeArticle"))
        e.Value = CalculeStock(y)
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim CondCrystal As String = ""
        CondCrystal = "1=1 "

        Dim FieldDef As CrystalDecisions.CrystalReports.Engine.FieldDefinition

        'dans une phase de developpemnt
        'If chbNonMouvementees.Checked Then
        '    CondCrystal += " AND {Vue_ListeDesArticles.DerniereVente} <= Date('" + tNonMouvementees.Text + "') "
        '    CondCrystal += " AND {Vue_ListeDesArticle_DateMin.DerniereVente} <= Date('" + tNonMouvementees.Text + "') "
        'End If

        'cas des articles détaillés
        If chbParLot.Checked Then
            If cmbQteUnit.SelectedValue <> 0 Then
                If cmbQteUnit.SelectedValue = 1 Then
                    CondCrystal += " AND {Vue_ListeDesArticles.QuantiteUnitaire} < 1 "
                End If
                If cmbQteUnit.SelectedValue = 2 Then
                    CondCrystal += " AND {Vue_ListeDesArticles.QuantiteUnitaire} = 1 "
                End If
                If cmbQteUnit.SelectedValue = 3 Then
                    CondCrystal += " AND {Vue_ListeDesArticles.QuantiteUnitaire} > 1 "
                End If
            End If
            If cmbDCI.Text <> "" Then
                CondCrystal = CondCrystal + " AND ({Vue_ListeDesArticles.LibelleDCI1} = " + Quote(cmbDCI.Text) + " OR {Vue_ListeDesArticles.LibelleDCI2} = " + Quote(cmbDCI.Text) + " OR {Vue_ListeDesArticles.LibelleDCI3} = " + Quote(cmbDCI.Text) + ")"
            End If

            If cmbLabo.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticles.CodeLabo} =" + cmbLabo.SelectedValue.ToString() + ""
            End If

            If tCode.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticles.CodeABarre} ='" + tCode.Text + "'"
            End If

            If cmbDesignation.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticles.Designation} like '" + cmbDesignation.Text + "*'"
            End If

            If cmbFournisseur.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticles.CodeFournisseur} ='" + cmbFournisseur.SelectedValue + "'"
            End If

            If cmbForme.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticles.LibelleForme} ='" + cmbForme.Text + "'"
            End If

            If cmbCodePCT.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticles.CodePCT} ='" + cmbCodePCT.Text + "'"
            End If

            If cmbCategorie.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticles.LibelleCategorie} ='" + cmbCategorie.Text + "'"
            End If

            If cmbRayon.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticles.Rayon} ='" + cmbRayon.Text + "'"
            End If

            If cmbSection.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticles.Section} ='" + cmbSection.Text + "'"
            End If

            If chbNonMouvementees.Checked = True Then
                If tNonMouvementees.Text = "" Then
                    MsgBox("Veuillez saisir la date de base pour la détermination des articles non vendus !", MsgBoxStyle.Critical, "Erreur")
                    tNonMouvementees.Focus()
                    Exit Sub
                End If
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticles.DerniereVente} <= Date('" + tNonMouvementees.Text + "')"
            End If
            CondCrystal = CondCrystal + "AND {Vue_ListeDesArticles.QteLotArticle} <> 0 "
            If rdbSuspendus.Checked = True Then
                CondCrystal = CondCrystal + "AND {Vue_ListeDesArticles.CodeSituation} = 3 "
            ElseIf rdbNonSuspendus.Checked = True Then
                CondCrystal = CondCrystal + "AND {Vue_ListeDesArticles.CodeSituation} <> 3 "
            End If

            If chbNonMouvementees.Checked Then
                CondCrystal += " AND {Vue_ListeDesArticles.DerniereVente} <= Date('" + tNonMouvementees.Text + "') "
            End If
            'cas de min date
        Else
            If cmbDCI.Text <> "" Then
                CondCrystal = CondCrystal + " AND ({Vue_ListeDesArticle_DateMin.LibelleDCI1} = " + Quote(cmbDCI.Text) + " OR {Vue_ListeDesArticle_DateMin.LibelleDCI2} = " + Quote(cmbDCI.Text) + " OR {Vue_ListeDesArticle_DateMin.LibelleDCI3} = " + Quote(cmbDCI.Text) + ")"
            End If

            If cmbQteUnit.SelectedValue <> 0 Then
                If cmbQteUnit.SelectedValue = 1 Then
                    CondCrystal += " AND {Vue_ListeDesArticle_DateMin.QuantiteUnitaire} < 1 "
                End If
                If cmbQteUnit.SelectedValue = 2 Then
                    CondCrystal += " AND {Vue_ListeDesArticle_DateMin.QuantiteUnitaire} = 1 "
                End If
                If cmbQteUnit.SelectedValue = 3 Then
                    CondCrystal += " AND {Vue_ListeDesArticle_DateMin.QuantiteUnitaire} > 1 "
                End If
            End If

            If cmbLabo.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticle_DateMin.CodeLabo} =" + cmbLabo.SelectedValue.ToString + ""
            End If

            If tCode.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticle_DateMin.CodeABarre} ='" + tCode.Text + "'"
            End If

            If cmbDesignation.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticle_DateMin.Designation} like '" + cmbDesignation.Text + "*'"
            End If
            If cmbFournisseur.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticle_DateMin.CodeFournisseur} ='" + cmbFournisseur.SelectedValue + "'"
            End If
            If cmbForme.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticle_DateMin.LibelleForme} ='" + cmbForme.Text + "'"
            End If

            If cmbCodePCT.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticle_DateMin.CodePCT} ='" + cmbCodePCT.Text + "'"
            End If

            If cmbCategorie.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticle_DateMin.LibelleCategorie} ='" + cmbCategorie.Text + "'"
            End If

            If cmbRayon.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticle_DateMin.Rayon} ='" + cmbRayon.Text + "'"
            End If

            If cmbSection.Text <> "" Then
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticle_DateMin.Section} ='" + cmbSection.Text + "'"
            End If

            If chbNonMouvementees.Checked = True Then
                If tNonMouvementees.Text = "" Then
                    MsgBox("Veuillez saisir la date de base pour la détermination des articles non vendus !", MsgBoxStyle.Critical, "Erreur")
                    tNonMouvementees.Focus()
                    Exit Sub
                End If
                CondCrystal = CondCrystal + " AND {Vue_ListeDesArticle_DateMin.DerniereVente} <= Date('" + tNonMouvementees.Text + "')"
            End If

            If rdbStockSup0.Checked = True Then
                CondCrystal = CondCrystal + "AND {Vue_ListeDesArticle_DateMin.Stock} > 0 "
            ElseIf rdbStockEgal0.Checked = True Then
                CondCrystal = CondCrystal + "AND (isnull({Vue_ListeDesArticle_DateMin.Stock}) OR {Vue_ListeDesArticle_DateMin.Stock} = 0 ) "
            ElseIf rdbStockInf0.Checked = True Then
                CondCrystal = CondCrystal + "AND {Vue_ListeDesArticle_DateMin.Stock} < 0 "
            End If

            If rdbSuspendus.Checked = True Then
                CondCrystal = CondCrystal + "AND {Vue_ListeDesArticle_DateMin.CodeSituation} = 3 "
            ElseIf rdbNonSuspendus.Checked = True Then
                CondCrystal = CondCrystal + "AND {Vue_ListeDesArticle_DateMin.CodeSituation} <> 3 "
            End If

            If chbNonMouvementees.Checked Then
                CondCrystal += " AND {Vue_ListeDesArticle_DateMin.DerniereVente} <= Date('" + tNonMouvementees.Text + "') "
            End If
        End If

        ' If Not String.IsNullOrEmpty(VOrderBy) Then
        'CondCrystal = CondCrystal + " ODER BY " + VOrderBy + " " + VAscDesc
        ' End If


        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression de la liste des articles" Then
                num = I
            End If
        Next

        If chbParLot.Checked Then
            CR.FileName = Application.StartupPath + "\EtatDesArticles.rpt"
        Else
            CR.FileName = Application.StartupPath + "\EtatDesArticlesDateMin.rpt"
        End If


        If chMasquerStock.Checked = True Then
            CR.SetParameterValue("PMasque", "Masque")
        Else
            CR.SetParameterValue("PMasque", "NonMasque")
        End If

        CR.SetParameterValue("Forme", IIf(cmbForme.Text <> "", cmbForme.Text, "-"))
        CR.SetParameterValue("Categorie", IIf(cmbCategorie.Text <> "", cmbCategorie.Text, "-"))
        CR.SetParameterValue("Section", IIf(cmbSection.Text <> "", cmbSection.Text, "-"))
        CR.SetParameterValue("Rayon", IIf(cmbRayon.Text <> "", cmbRayon.Text, "-"))

        'les totaux
        CR.SetParameterValue("TotalAchatTTC", lTotalAchatTTC.Text)
        CR.SetParameterValue("TotalAchatHT", lTotalAchatHT.Text)
        CR.SetParameterValue("TotalVenteTTC", lTotalVenteTTC.Text)
        CR.SetParameterValue("TotalVenteHT", lTotalVenteHT.Text)

        Application.DoEvents()

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent

        If chbParLot.Checked Then
            FieldDef = CR.Database.Tables("Vue_ListeDesArticles").Fields(VOrderBy)
        Else
            FieldDef = CR.Database.Tables("Vue_ListeDesArticle_DateMin").Fields(VOrderBy)
        End If
        CR.DataDefinition.SortFields(0).Field = FieldDef
        If VAscDesc = "Asc" Then
            CR.DataDefinition.SortFields(0).SortDirection = CrystalDecisions.Shared.SortDirection.AscendingOrder
        Else
            CR.DataDefinition.SortFields(0).SortDirection = CrystalDecisions.Shared.SortDirection.DescendingOrder
        End If



        CR.RecordSelectionFormula = CondCrystal



        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression de la liste des articles"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If

    End Sub

    Private Sub rdbTousLesArticles_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbTousLesArticles.CheckedChanged
        AfficherArticle(ModePreview:=False)
    End Sub

    Private Sub rdbStockSup0_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbStockSup0.CheckedChanged
        AfficherArticle(ModePreview:=False)
    End Sub

    Private Sub rdbStockEgal0_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbStockEgal0.CheckedChanged
        AfficherArticle(ModePreview:=False)
    End Sub

    Private Sub rdbStockInf0_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbStockInf0.CheckedChanged
        AfficherArticle(ModePreview:=False)
    End Sub

    Private Sub rdbTousArticlesSuspendus_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbTousArticlesSuspendus.CheckedChanged
        AfficherArticle(ModePreview:=False)
    End Sub

    Private Sub rdbNonSuspendus_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbNonSuspendus.CheckedChanged
        AfficherArticle(ModePreview:=False)
    End Sub

    Private Sub rdbSuspendus_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbSuspendus.CheckedChanged
        AfficherArticle(ModePreview:=False)
    End Sub

    Private Sub cmbRayon_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbRayon.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbRayon.Text = cmbRayon.WillChangeToText
            AfficherArticle(ModePreview:=False)
        Else
            cmbRayon.OpenCombo()
        End If
    End Sub

    Private Sub cmbSection_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbSection.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbSection.Text = cmbSection.WillChangeToText
            AfficherArticle(ModePreview:=False)
            cmbRayon.Focus()
        Else
            cmbSection.OpenCombo()
        End If
    End Sub

    Private Sub tCode_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCode.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherArticle(ModePreview:=False)
            cmbDesignation.Focus()
        End If
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub bDupliquerArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bDupliquerArticle.Click
        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(2, "AJOUT_ARTICLE") = "False" Then
            Exit Sub
        End If

        Dim MyFicheArticle As New fFicheArticle

        MyFicheArticle.CodeArticle = gArticle(gArticle.Row, "CodeArticle")
        MyFicheArticle.DesignationArticle = gArticle(gArticle.Row, "Designation")

        MyFicheArticle.ajoutmodif = "DUPLIQUER"
        MyFicheArticle.Init()
        MyFicheArticle.ShowDialog()
        MyFicheArticle.Close()
        MyFicheArticle.Dispose()
    End Sub

    Private Sub tNonMouvementees_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNonMouvementees.KeyUp
        'If tNonMouvementees.Text = "" Then
        '    MsgBox("Veuillez saisir la date de base pour la détermination des articles non vendus !", MsgBoxStyle.Critical, "Erreur")
        '    tNonMouvementees.Focus()
        '    Exit Sub
        'Else
        '    AfficherArticle(ModePreview:=False)
        'End If
    End Sub

    Private Sub chbParLot_CheckedChanged(sender As System.Object, e As System.EventArgs) Handles chbParLot.CheckedChanged
        GroupStock.Enabled = Not chbParLot.Checked
    End Sub

    Private Sub bMonographie_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bMonographie.Click
        'ConnectDextherBCB()
        'Dim BaseDexther As New BCBV3_TN.c_Database(StrConnectionBCB)

        'Dim cmd As New SqlCommand
        'Dim cmdBCB As New OleDb.OleDbCommand

        'If BaseDexther.Open Then
        '    cmd.Connection = ConnectionServeur
        '    cmdBCB.Connection = ConnectionServeurBCB

        '    Dim strMonographiePath As String = Application.StartupPath + "\monographie\"
        '    Dim strMonographieFile As String = "MonoTUN.htm"
        '    Dim strMonographieTemplateFile As String = "monographie_template.html"
        '    '-------------------------------------------------------------------------------------------------------

        '    Dim strMonographieFilePath As String = strMonographiePath & strMonographieFile
        '    Dim strMonographieTemplateFilePath As String = strMonographiePath & strMonographieTemplateFile

        '    If gArticle(gArticle.Row, "Designation") = "" Then
        '        MessageBox.Show("Ligne vide !", "Anomalie", MessageBoxButtons.OK, MessageBoxIcon.Error)
        '        Exit Sub
        '    End If

        '    cmd.CommandText = "SELECT CodePCT FROM ARTICLE WHERE CodeArticle=" + Quote(gArticle.Columns("CodeArticle").Value.ToString)

        '    Dim Mono As New BCBV3_TN.c_Monographie(cmd.ExecuteScalar.ToString, gArticle(gArticle.Row, "Designation"), "Monographie", strMonographieFilePath, strMonographieTemplateFilePath)

        '    If Mono.Generate = True Then
        '        Try
        '            'System.Diagnostics.Process.Start("Chrome.exe", strMonographieFilePath)
        '            Dim I As Integer
        '            For I = 0 To fMain.Tab.TabPages.Count - 1
        '                If fMain.Tab.TabPages(I).Text = "BCB Dexther" Then
        '                    fMain.Tab.TabPages(I).Show()
        '                    Exit Sub
        '                End If
        '            Next
        '            Dim MYVisionneurWeb As New fVisionneurHTML
        '            MYVisionneurWeb.Chemin = strMonographieFilePath
        '            fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        '            fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        '            fMain.Tab.SelectedTab.Controls.Add(MYVisionneurWeb.Panel)

        '            fMain.Tab.SelectedTab.Text = "BCB Dexther"
        '            MYVisionneurWeb.Init()

        '        Catch ex As Exception
        '            MessageBox.Show(ex.Message, "Anomalie", MessageBoxButtons.OK, MessageBoxIcon.Error)
        '        End Try
        '    Else
        '        If cmd.ExecuteScalar = "" Then
        '            MessageBox.Show("Veuillez remplir le code PCT de l'article !", "Anomalie", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        '        Else
        '            MessageBox.Show("La monographie de ce produit n'est pas disponible !", "Message", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        '        End If
        '    End If
        '    Mono = Nothing
        'Else
        '    MessageBox.Show("La base de données Dexther n'est pas installée, les monographies des produits ne peuvent pas être affichés !", "Information", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        'End If
        'BaseDexther.Close() : BaseDexther = Nothing
        ' Dim vCodearticle As String = RecupererValeurExecuteScalaire("CodePCT", "ARTICLE", "CodeArticle", gArticle(gArticle.Row, "CodeArticle"))
        Dim vCodearticle As String = gArticle(gArticle.Row, "CodePCT")

        If vCodearticle = "" Then
            MsgBox("Le code PCT inexistant.", MsgBoxStyle.Information, "Erreur")
            Exit Sub
        End If

        Dim BCBDextherEtrClient As New ServiceBCB.BCBDextherEtrClient()
        Dim Cle As String = generateKey("NEXT", "")
        Dim s As String = Environment.CurrentDirectory

        Dim bcbSecurity As ServiceBCB.bcbSecurity = New ServiceBCB.bcbSecurity
        bcbSecurity.codeEditeur = "NEXT"
        bcbSecurity.idPS = Cle
        bcbSecurity.secretEditeur = ""

        Dim IdProduit As Long = BCBDextherEtrClient.rechercheParCodePCT(vCodearticle, bcbSecurity)(0).idProduit

        Dim HTMLEquivalentStricts As String = ""
        Dim HTMLEquivalentProche As String = ""
        Dim HTMLEquivalentAutre As String = ""

        Dim Equivalent = BCBDextherEtrClient.rechercheEquivalents(IdProduit, bcbSecurity)

        If Equivalent.lstEquivalentsStricts IsNot Nothing Then
            For Each Item As bcbProduitMini In Equivalent.lstEquivalentsStricts
                HTMLEquivalentStricts += "<TR><TD>" & Item.libelle & "</TD></TR>"
            Next
        End If

        If Equivalent.lstEquivalentsProches IsNot Nothing Then
            For Each Item As bcbProduitMini In Equivalent.lstEquivalentsProches
                HTMLEquivalentProche += "<TR><TD>" & Item.libelle & "</TD></TR>"
            Next
        End If

        If Equivalent.lstEquivalentsAutres IsNot Nothing Then
            For Each Item As bcbProduitMini In Equivalent.lstEquivalentsAutres
                HTMLEquivalentAutre += "<TR><TD>" & Item.libelle & "</TD></TR>"
            Next
        End If

        Dim bcbProduitEtr As ServiceBCB.bcbProduitEtr = BCBDextherEtrClient.getInformationProduitEtr(IdProduit, 1, 0, True, bcbSecurity)

        Dim htmlbase64 As bcbHtml = bcbProduitEtr.monographieHTML
        Dim utf8 = Encoding.UTF8
        Dim html As Byte() = htmlbase64.monographie
        Dim affiche As [String] = utf8.GetString(html)
        Dim lstRessource = htmlbase64.lstResource
        Dim chemin As [String] = Environment.CurrentDirectory + "\BCB"

        For i As Integer = 0 To lstRessource.Count() - 1
            Dim imageBin As Byte() = lstRessource(i).data
            Dim nomImage As [String] = lstRessource(i).nom
            Dim cheminImage As [String] = lstRessource(i).chemin
            System.IO.File.WriteAllBytes(chemin + cheminImage + nomImage, html)
        Next

        Try
            Dim I As Integer
            For I = 0 To fMain.Tab.TabPages.Count - 1
                If fMain.Tab.TabPages(I).Text = "BCB Dexther" Then
                    fMain.Tab.TabPages(I).Show()
                    Exit Sub
                End If
            Next
            Dim MYVisionneurWeb As New fVisionneurHTML
            Dim uri = New System.Uri(chemin + "\images\myriadpro-regular-webfont.eot")

            Dim fileContents = System.IO.File.ReadAllText(chemin + "\images\myriadpro-regular-webfont.eot")

            fileContents = fileContents.Replace(fileContents.Substring(fileContents.IndexOf("class='signet_tr' onclick="), fileContents.IndexOf("ementaires</TD></TR>") + 20 - fileContents.IndexOf("class='signet_tr' onclick=")), fileContents.Substring(fileContents.IndexOf("class='signet_tr' onclick="), fileContents.IndexOf("ementaires</TD></TR>") + 20 - fileContents.IndexOf("class='signet_tr' onclick=")) + "<TR class='signet_tr' onclick=""SelectionSignet(this,'ancre_DONNEESEQUIVALENT')"" onmouseover='ChangementCurseur(this)' onmouseout='ChangementCurseur(this)'><TD class='signet_td_puce' id='signet_td_puce' name='signet_td_puce'><img src='./images/puce1.png' border='0' alt='' /></TD><TD class='signet_td' id='signet_td' name='signet_td'>Equivalent</TD></TR>")
            fileContents = fileContents.Replace(fileContents.Substring(fileContents.IndexOf("<div id='ancre_FORMEPRESENTATION' style='display:block'>"), fileContents.LastIndexOf("</TABLE><BR /><BR /></div>") + 26 - fileContents.IndexOf("<div id='ancre_FORMEPRESENTATION' style='display:block'>")), fileContents.Substring(fileContents.IndexOf("<div id='ancre_FORMEPRESENTATION' style='display:block'>"), fileContents.LastIndexOf("</TABLE><BR /><BR /></div>") + 26 - fileContents.IndexOf("<div id='ancre_FORMEPRESENTATION' style='display:block'>")) + "<div id='ancre_DONNEESEQUIVALENT' style='display:block'><a name='ancre_DONNEESEQUIVALENT'><span class='chapitre_titre'>Liste des équivalents proche</span></a><br><br><TABLE  class='chapitre_table' cellpadding='0' cellspacing='0'>" & HTMLEquivalentProche & "</TABLE><BR /><BR /><a name='ancre_DONNEESEQUIVALENT'><span class='chapitre_titre'>Liste des équivalents stricts</span></a><br><br><TABLE  class='chapitre_table' cellpadding='0' cellspacing='0'>" & HTMLEquivalentStricts & "</TABLE><BR /><BR /><a name='ancre_DONNEESEQUIVALENT'><span class='chapitre_titre'>Liste des autres équivalents</span></a><br><br><TABLE  class='chapitre_table' cellpadding='0' cellspacing='0'>" & HTMLEquivalentAutre & "</TABLE><BR /><BR /></div>")

            fileContents = fileContents.Replace("è", "&#232;")
            fileContents = fileContents.Replace("é", "&#233;")
            fileContents = fileContents.Replace("ç", "&#231;")
            fileContents = fileContents.Replace("à", "&#224;")
            fileContents = fileContents.Replace("ë", "&#235;")
            fileContents = fileContents.Replace("ü", "&#252;")
            fileContents = fileContents.Replace("û", "&#251;")
            fileContents = fileContents.Replace("ô", "&#244;")
            fileContents = fileContents.Replace("ô", "&#244;")

            System.IO.File.WriteAllText(chemin + "\images\myriadpro-regular-webfont.eot", fileContents)

            Dim sbColumnData As New StringBuilder

            '"<TR class='signet_tr' onclick="
            '"<TR class='signet_tr' onclick='SelectionSignet(this,'ancre_DONNEESEQUIVALENT')' onmouseover='ChangementCurseur(this)' onmouseout='ChangementCurseur(this)'><TD class='signet_td_puce' id='signet_td_puce' name='signet_td_puce'><img src='./images/puce1.png' border='0' alt='' /></TD><TD class='signet_td' id='signet_td' name='signet_td'>AAA</TD></TR>"
            '"<div id='ancre_FORMEPRESENTATION' style='display:block'>"
            '"<div id='ancre_DONNEESEQUIVALENT' style='display:block'><a name='ancre_DONNEESEQUIVALENT'><span class='chapitre_titre'>AAA</span></a><br><br><TABLE  class='chapitre_table' cellpadding='0' cellspacing='0'><TR><TD>Code CIP711111111111</TD><TD></TD></TR><TR><TD>Médicament T2A</TD><TD>Non</TD></TR><TR><TD>Laboratoire titulaire AMM</TD><TD>&nbsp;</TD></TR><TR><TD>Laboratoire exploitant</TD><TD></TD></TR><TR><TD>Médicament d'exception</TD><TD>Non</TD></TR><TR><TD>Agrément collectivités</TD><TD>Non</TD></TR></TABLE><BR /><BR /></div>"


            MYVisionneurWeb.Chemin = uri.AbsoluteUri
            fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
            fMain.Tab.SelectedTab.Controls.Add(MYVisionneurWeb.Panel)

            fMain.Tab.SelectedTab.Text = "BCB Dexther"
            MYVisionneurWeb.Init()

        Catch ex As Exception
            MessageBox.Show(ex.Message, "Anomalie", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub bCodeABarre_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCodeABarre.Click
        If ControleDAcces(6, "IMPRESSION_CODEABARRE") = "False" Then
            Exit Sub
        End If

        Dim MyImpressionTicket As New fImpressionEtiquette
        MyImpressionTicket.CodeABarre = gArticle(gArticle.Row, "CodeABarre")
        MyImpressionTicket.Designation = gArticle(gArticle.Row, "Designation")
        MyImpressionTicket.PrixVenteTTC = gArticle(gArticle.Row, "PrixVenteTTC")
        MyImpressionTicket.ShowDialog()
    End Sub

    Private Sub cmbLabo_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbLabo.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbLabo.Text = cmbLabo.WillChangeToText
            AfficherArticle(ModePreview:=False)
            cmbSection.Focus()
        Else
            cmbLabo.OpenCombo()
        End If
    End Sub

    Private Sub cmbQteUnit_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbQteUnit.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbQteUnit.Text = cmbQteUnit.WillChangeToText
            AfficherArticle(ModePreview:=False)
            tCode.Focus()
        Else
            cmbQteUnit.OpenCombo()
        End If
    End Sub

    Private Sub cmbFournisseur_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbFournisseur.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbFournisseur.Text = cmbFournisseur.WillChangeToText
            AfficherArticle(ModePreview:=False)
            cmbQteUnit.Focus()
        Else
            cmbFournisseur.OpenCombo()
        End If
    End Sub

    Private Sub cmbDCI_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbDCI.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbDCI.Text = cmbDCI.WillChangeToText
            AfficherArticle(ModePreview:=False)
            cmbLabo.Focus()
        Else
            cmbDCI.OpenCombo()
        End If
    End Sub


    Private Sub bModifierCNAM_Click(sender As Object, e As EventArgs) Handles bModifierCNAM.Click
        Dim fd As OpenFileDialog = New OpenFileDialog()
        Dim strFileName As String = ""
        If fd.ShowDialog() = DialogResult.OK Then
            strFileName = fd.FileName
        Else
            Exit Sub
        End If

        ImportDataFromExcel(strFileName)
    End Sub

    Public Sub ImportDataFromExcel(excelFilePath As String)
        'declare variables - edit these based on your particular situation 
        Dim ssqltable As String = "FICHER_CNAM"
        ' make sure your sheet name is correct, here sheet name is sheet1, so you can change your sheet name if have    different 
        Dim myexceldataquery As String = "select Code_PCT, NOM_COMMERCIAL, PRIX_PUBLIC, TARIF_REFERENCE, CATEGORIE, DCI, AP from [VEI$]"
        Try
            'create our connection strings 
            Dim sexcelconnectionstring As String = (Convert.ToString("provider=microsoft.jet.oledb.4.0;data source=") & excelFilePath) + ";extended properties=" + """excel 8.0;hdr=yes;"""
            '''''  Dim ssqlconnectionstring As String = "Data Source=SAYYED;Initial Catalog=SyncDB;Integrated Security=True"
            'execute a query to erase any previous data from our destination table 
            Dim sclearsql As String = Convert.ToString("delete from ") & ssqltable
            '''''' Dim sqlconn As New SqlConnection(ssqlconnectionstring)
            Dim sqlcmd As New SqlCommand(sclearsql, ConnectionServeur)
            sqlcmd.ExecuteNonQuery()
            'series of commands to bulk copy data from the excel file into our sql table 
            Process.Start(excelFilePath)
            Dim oledbconn As New OleDbConnection(sexcelconnectionstring)
            Dim oledbcmd As New OleDbCommand(myexceldataquery, oledbconn)
            oledbconn.Open()
            Dim dr As OleDbDataReader = oledbcmd.ExecuteReader()
            Dim bulkcopy As New SqlBulkCopy(ConnectionServeur)
            bulkcopy.DestinationTableName = ssqltable
            While dr.Read()
                Try
                    bulkcopy.WriteToServer(dr)
                Catch ex As Exception

                End Try

            End While
            dr.Close()
            oledbconn.Close()
            'Label1.Text = "File imported into sql server."
            'handle exception 



            Dim cmd As New SqlCommand
            cmd.Connection = ConnectionServeur
            cmd.CommandText = " UPDATE " + _
                            " 	ARTICLE " + _
                            " SET " + _
                            " 	CodeCategorieCNAM = IIF(FICHER_CNAM.CATEGORIE = 'I', 1, IIF(FICHER_CNAM.CATEGORIE = 'V', 2, IIF(FICHER_CNAM.CATEGORIE = 'E', 3, 4))), " + _
                            " 	AccordPrealable = IIF(FICHER_CNAM.AP = 'O', 1, 0), " + _
                            " 	PriseEnCharge = IIF(FICHER_CNAM.CATEGORIE = 'I' OR FICHER_CNAM.CATEGORIE = 'V' OR  FICHER_CNAM.CATEGORIE = 'E', 1, 0), " + _
                            " 	TarifDeReference = FICHER_CNAM.TARIF_REFERENCE " + _
                            " FROM " + _
                            " 	FICHER_CNAM " + _
                            " WHERE " + _
                            " 	ARTICLE.CodePCT = FICHER_CNAM.Code_PCT "
            cmd.ExecuteScalar()

            cmd.CommandText = " EXEC dbo.ModificationDciArticle "
            cmd.ExecuteScalar()

            MessageBox.Show("Opération terminé avec succès.", "", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show("Probleme fichier CNAM.", "", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub tNonMouvementees_Validated(sender As Object, e As EventArgs) Handles tNonMouvementees.Validated
        If tNonMouvementees.Text = "" Then
            MsgBox("Veuillez saisir la date de base pour la détermination des articles non vendus !", MsgBoxStyle.Critical, "Erreur")
            tNonMouvementees.Focus()
            Exit Sub
        Else
            AfficherArticle(ModePreview:=False)
        End If
    End Sub
End Class