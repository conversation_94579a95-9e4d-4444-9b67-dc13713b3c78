﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fVenteSupprime

    Dim cmdVente As New SqlCommand
    Dim cbVente As New SqlCommandBuilder
    Dim dsVente As New DataSet
    Dim daVente As New SqlDataAdapter

    Dim StrSQL As String = ""

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If

    End Sub

    Public Sub init()
        Dim i As Integer = 0
        Try

      
        If (dsVente.Tables.IndexOf("VENTE_SUPPRIME") > -1) Then
            dsVente.Tables("VENTE_SUPPRIME").Clear()
        End If

        If (dsVente.Tables.IndexOf("VENTE_SUPPRIME_DETAILS") > -1) Then
            dsVente.Tables("VENTE_SUPPRIME_DETAILS").Clear()
        End If

        'chargement des Ventes

            StrSQL = "SELECT NumeroVenteSupprime," + _
                     "NumeroVente," + _
                     "Date ," + _
                     "VENTE_SUPPRIME.CodePersonnel," + _
                     "UTILISATEUR.Nom AS NomUtilisateur," + _
                     "VENTE_SUPPRIME.CodeClient, " + _
                     "CASE WHEN MontantCnam <>0 THEN ('CNAM') WHEN MontantMutuelle <>0 THEN ('MUTUIELLE') ELSE ('COMPTOIR') END as Type," + _
                     "CLIENT.Nom," + _
                     "MontantCnam," + _
                     "MontantMutuelle," + _
                     "TotalTTC," + _
                     "NomPersonnelSupprime," + _
                     "DateSuppression, " + _
                     "CONVERT(varchar ,DateSuppression,108) as Heure,  " + _
                      "'' as Vide " + _
                     "FROM " + _
                     "VENTE_SUPPRIME " + _
                     "LEFT OUTER JOIN CLIENT ON VENTE_SUPPRIME.CodeClient=CLIENT.CodeClient " + _
                     "LEFT OUTER JOIN UTILISATEUR ON VENTE_SUPPRIME.CodePersonnel=UTILISATEUR.CodeUtilisateur " + _
                     " Order by DateSuppression Desc "

        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = StrSQL
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "VENTE_SUPPRIME")
        cbVente = New SqlCommandBuilder(daVente)

        With gVente
            .Columns.Clear()
            Try
                .DataSource = dsVente
            Catch ex As Exception
            End Try
            .DataMember = "VENTE_SUPPRIME"
            .Rebind(False)
            .Columns("NumeroVenteSupprime").Caption = "Numéro"
            .Columns("NumeroVente").Caption = "'Numéro"
            .Columns("Date").Caption = "Date"
            .Columns("NomUtilisateur").Caption = "Vendeur"
            .Columns("Type").Caption = "Type"
            .Columns("Nom").Caption = "Client"
            .Columns("MontantCnam").Caption = "Montant Cnam"
            .Columns("MontantMutuelle").Caption = "Montant Mutuelle"
            .Columns("TotalTTC").Caption = "Total TTC"
            .Columns("NomPersonnelSupprime").Caption = "Personnel (sup)"
            .Columns("DateSuppression").Caption = "Date suppression"
                .Columns("Heure").Caption = "Heure"
                .Columns("Vide").Caption = ""

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Nom").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("NomUtilisateur").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("NomPersonnelSupprime").Style.HorizontalAlignment = AlignHorzEnum.Near

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("MontantCnam").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("MontantMutuelle").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("NumeroVenteSupprime").Width = 0
                .Splits(0).DisplayColumns("NumeroVente").Width = 120 '80
                .Splits(0).DisplayColumns("Date").Width = 120 '80
            .Splits(0).DisplayColumns("NomUtilisateur").Width = 200
            .Splits(0).DisplayColumns("Type").Width = 100
                .Splits(0).DisplayColumns("Nom").Width = 200
            .Splits(0).DisplayColumns("MontantCnam").Width = 100
            .Splits(0).DisplayColumns("MontantMutuelle").Width = 100
                .Splits(0).DisplayColumns("TotalTTC").Width = 120
                .Splits(0).DisplayColumns("NomPersonnelSupprime").Width = 200
                .Splits(0).DisplayColumns("DateSuppression").Width = 120
                .Splits(0).DisplayColumns("Heure").Width = 80

            .Splits(0).DisplayColumns("NumeroVenteSupprime").Visible = False
            .Splits(0).DisplayColumns("CodePersonnel").Visible = False
                .Splits(0).DisplayColumns("NomUtilisateur").Visible = True
                .Splits(0).DisplayColumns("CodeClient").Visible = False
                .Splits(0).DisplayColumns("Vide").Visible = False

            .Splits(0).DisplayColumns("NomPersonnelSupprime").Style.BackColor = Color.FromArgb(255, 255, 192, 128)
            .Splits(0).DisplayColumns("DateSuppression").Style.BackColor = Color.FromArgb(255, 255, 192, 128)
            .Splits(0).DisplayColumns("Heure").Style.BackColor = Color.FromArgb(255, 255, 192, 128)

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                .AllowSort = False
                .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
                'Style du Caractere et du grid
                ParametreGrid(gVente)
            End With

            'chargement des détails des Ventes

            StrSQL = "SELECT VENTE_SUPPRIME_DETAILS.CodeArticle," + _
                     "Designation ," + _
                     "LibelleForme," + _
                     "Qte," + _
                     "PrixTTC, " + _
                     "TotalTTC, " + _
                      "'' as Vide " + _
                     "FROM " + _
                     "VENTE_SUPPRIME_DETAILS " + _
                     "LEFT OUTER JOIN FORME_ARTICLE ON VENTE_SUPPRIME_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
                     " WHERE NumeroVenteSupprime='"
            If dsVente.Tables("VENTE_SUPPRIME").Rows.Count <> 0 Then
                StrSQL += dsVente.Tables("VENTE_SUPPRIME").Rows(0).Item("NumeroVenteSupprime") + "'"
            Else
                StrSQL += "0'"
            End If


            cmdVente.Connection = ConnectionServeur
            cmdVente.CommandText = StrSQL
            daVente = New SqlDataAdapter(cmdVente)
            daVente.Fill(dsVente, "VENTE_SUPPRIME_DETAILS")
            cbVente = New SqlCommandBuilder(daVente)

            With gDetails
                .Columns.Clear()
                Try
                    .DataSource = dsVente
                Catch ex As Exception
                End Try
                .DataMember = "VENTE_SUPPRIME_DETAILS"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Qte").Caption = "Qte"
                .Columns("PrixTTC").Caption = "Prix TTC"
                .Columns("TotalTTC").Caption = "Total TTC"
                .Columns("Vide").Caption = ""

                ' Centrer toutes les entêtes
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Locked = True
                Next

                .Splits(0).DisplayColumns("PrixTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

                .Splits(0).DisplayColumns("CodeArticle").Width = 100
                .Splits(0).DisplayColumns("Designation").Width = 400
                .Splits(0).DisplayColumns("LibelleForme").Width = 150
                .Splits(0).DisplayColumns("Qte").Width = 100
                .Splits(0).DisplayColumns("PrixTTC").Width = 130
                .Splits(0).DisplayColumns("TotalTTC").Width = 70

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                .AllowSort = False
                .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
                'Style du Caractere et du grid
                ParametreGrid(gDetails)
            End With
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Ventes Supprimées", "fVenteSupprime", " init", ex.Message, "0000282", "Erreur d'exécution de init ", True, True, True)

        End Try
    End Sub

    Public Sub ChargerDetailsVente(ByVal NumeroVente)
        Dim i As Integer = 0
        Try
            If (dsVente.Tables.IndexOf("VENTE_SUPPRIME_DETAILS") > -1) Then
                dsVente.Tables("VENTE_SUPPRIME_DETAILS").Clear()
            End If

            'chargement des détails des Ventes

            StrSQL = "SELECT VENTE_SUPPRIME_DETAILS.CodeArticle," + _
                     "Designation ," + _
                     "LibelleForme," + _
                     "Qte," + _
                     "PrixTTC, " + _
                     "TotalTTC, " + _
                      "'' as Vide " + _
                     "FROM " + _
                     "VENTE_SUPPRIME_DETAILS " + _
                     "LEFT OUTER JOIN FORME_ARTICLE ON VENTE_SUPPRIME_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
                     " WHERE NumeroVenteSupprime='" + _
                     NumeroVente + "'"

            cmdVente.Connection = ConnectionServeur
            cmdVente.CommandText = StrSQL
            daVente = New SqlDataAdapter(cmdVente)
            daVente.Fill(dsVente, "VENTE_SUPPRIME_DETAILS")
            cbVente = New SqlCommandBuilder(daVente)

            With gDetails
                .Columns.Clear()
                Try
                    .DataSource = dsVente
                Catch ex As Exception
                End Try
                .DataMember = "VENTE_SUPPRIME_DETAILS"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Qte").Caption = "Qte"
                .Columns("PrixTTC").Caption = "Prix TTC"
                .Columns("TotalTTC").Caption = "Total TTC"
                .Columns("Vide").Caption = ""

                ' Centrer toutes les entêtes
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Locked = True
                Next

                .Splits(0).DisplayColumns("PrixTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

                .Splits(0).DisplayColumns("CodeArticle").Width = 100
                .Splits(0).DisplayColumns("Designation").Width = 400
                .Splits(0).DisplayColumns("LibelleForme").Width = 150
                .Splits(0).DisplayColumns("Qte").Width = 100
                .Splits(0).DisplayColumns("PrixTTC").Width = 130
                .Splits(0).DisplayColumns("TotalTTC").Width = 70

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                .AllowSort = False
                .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

            End With
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Ventes Supprimées", "fVenteSupprime", " ChargerDetailsVente", ex.Message, "0000283", "Erreur d'exécution de ChargerDetailsVente ", True, True, True)

        End Try
    End Sub



    Private Sub gVente_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gVente.KeyUp
        Try
            If e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Then
                ChargerDetailsVente(gVente(gVente.Row, "NumeroVenteSupprime"))
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Ventes Supprimées", "fVenteSupprime", " gVente_KeyUp", ex.Message, "0000284", "Erreur d'exécution de gVente_KeyUp ", True, True, True)

        End Try
    End Sub

    Private Sub gVente_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gVente.MouseClick
        Try

            ChargerDetailsVente(gVente(gVente.Row, "NumeroVenteSupprime"))


        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Ventes Supprimées", "fVenteSupprime", " gVente_MouseClick", ex.Message, "0000285", "Erreur d'exécution de gVente_MouseClick ", True, True, True)

        End Try
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        Try

            'Suivi du scénario
            fMessageException.Show("Ventes Supprimées", "fVenteSupprime", "bQuitter_Click", "NoException", "NoError", "Clic sur le bouton bQuitter", False, True, False)

            fMain.Tab.SelectedTab.Dispose()

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Ventes Supprimées", "fVenteSupprime", " bQuitter_Click", ex.Message, "0000286", "Erreur d'exécution de bQuitter_Click ", True, True, True)

        End Try
    End Sub
End Class