﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fCommandeDesManquants
    Dim cmdChargementCommande As New SqlCommand
    Dim cbChargementCommande As New SqlCommandBuilder
    Dim dsChargementCommande As New DataSet
    Dim daChargementCommande As New SqlDataAdapter

    Dim cmdCommande As New SqlCommand
    Dim cbCommande As New SqlCommandBuilder
    Dim dsCommande As New DataSet
    Dim daCommande As New SqlDataAdapter
    Dim daCommande1 As New SqlDataAdapter
    Dim mode As String = ""

    Public NumeroCommande As String = ""

    Public TotalTTCAchat As Double = 0.0
    Public TotalHTAchat As Double = 0.0
    Public TotalTVA As Double = 0.0

    Public TVA As Double = 0.0
    Public Timbre As Double = 0.3

    '****************************************************************************************
    '******************************* Critéres de selection Commande *************************
    '****************************************************************************************
    Public TypeCommande As String = ""                'journalière ou groupé
    Public NombreDeJour As Integer = 0
    Public SansManquantDepuis As String = ""
    Public Reference As String = ""
    Public mois As Integer = 0
    Public DebutPeriode As String = ""
    Public FinPeriode As String = ""
    Public Section As String = ""
    Public DebutIntervalle As String = ""
    Public FinIntervalle As String = ""
    Public Forme As Integer = 0
    Public Categorie As Integer = 0
    Public Labo As Integer = 0
    Public Rayon As String = ""
    Public RayonSelectionne As String = ""
    Public Trie As String = ""
    Public CommandeEnCours As Boolean = False

    '------------------------------------------------- les informations de paiement (retour de fenetre paiement)
    Public Operateur As Integer = 0

    Public NouvelleCommande As DataRow = Nothing 'datarow pour charger l'entête dans la datatable ACHAT
    Public NouvelArticle As DataRow = Nothing 'datarow pour charger les détails dans la datatable ACHAT_DETAIL
    Public dr As DataRow = Nothing 'datarow pour charger dans la datatable ARTICLE qui contient les articles recherchés pendant la saisie de la designation

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "116" And bAjouter.Enabled = True Then
            bAjouter_Click(sender, e)
        End If
        If argument = "114" And bConfirmer.Enabled = True Then
            bConfirmer_Click(sender, e)
        End If
        If argument = "121" And bAnnuler.Enabled = True Then
            bAnnuler_Click(sender, e)
        End If
        If argument = "113" And bFournisseur.Enabled = True Then
            bFournisseur_Click(sender, e)
        End If
        '--------------------- boutton close 
        'If argument = "123" Then
        '    Dim i As Integer
        '    For i = 0 To fMain.Tab.TabPages.Count - 1
        '        If fMain.Tab.TabPages(i).Text = "Vente" Then
        '            fMain.Tab.TabPages(i).Hide()
        '            Exit Sub
        '        End If
        '    Next
        'End If
    End Sub

    Public Sub Init()

        mode = "Ajout"

        lTotHTAchat.Text = "0.000"
        lTotalTTCAchat.Text = "0.000"
        lTotalTVA.Text = "0.000"

        Dim StrSQL As String = ""
        Dim I As Integer

        With cmbType
            .HoldFields()
            .AddItem("JOURNALIERE")
            .AddItem("GROUPEE")
            .ColumnHeaders = False
        End With

        Try
            dsChargementCommande.Tables("COMMANDE_DETAILS").Clear()
        Catch ex As Exception
        End Try

        Try
            dsChargementCommande.Tables("COMMANDE").Clear()
        Catch ex As Exception
        End Try

        Try
            dsChargementCommande.Tables("FOURNISSEUR").Clear()
        Catch ex As Exception
        End Try

        'chargement des Fournisseurs
        StrSQL = "SELECT CodeFournisseur,NomFournisseur FROM FOURNISSEUR ORDER BY NomFournisseur ASC"
        cmdChargementCommande.Connection = ConnectionServeur
        cmdChargementCommande.CommandText = StrSQL
        daChargementCommande = New SqlDataAdapter(cmdChargementCommande)
        daChargementCommande.Fill(dsChargementCommande, "FOURNISSEUR")
        cmbFournisseur.DataSource = dsChargementCommande.Tables("FOURNISSEUR")
        cmbFournisseur.ValueMember = "CodeFournisseur"
        cmbFournisseur.DisplayMember = "NomFournisseur"
        cmbFournisseur.ColumnHeaders = False
        cmbFournisseur.Splits(0).DisplayColumns("CodeFournisseur").Visible = False
        cmbFournisseur.Splits(0).DisplayColumns("NomFournisseur").Width = 10
        cmbFournisseur.ExtendRightColumn = True

        '------------------------- préparation de la table Entêtes des commandes         
        StrSQL = "SELECT top 0 *  FROM COMMANDE ORDER BY NumeroCommande ASC"
        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande = New SqlDataAdapter(cmdCommande)
        daCommande.Fill(dsCommande, "COMMANDE")

        '------------------------- préparation de la table détails des commandes 
        StrSQL = "SELECT NumeroCommande,CodeArticle,Designation,LibelleForme,Qte,Stock,DatePeremption," + _
       "PrixAchatHT,TVA,TotalTTCAchat,StockAlerte,EnCours,QteJour,QteUnitaire,COMMANDE_DETAILS.CodeForme FROM " + _
       "COMMANDE_DETAILS,FORME_ARTICLE WHERE COMMANDE_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
       "AND NumeroCommande ='0'"
        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande.Fill(dsCommande, "COMMANDE_DETAILS")
        cbCommande = New SqlCommandBuilder(daCommande)

        '--------------'''''''''''''' chargement des détails de la commande a partir des manquants 
        NumeroCommande = RecupereNumero()
        Dim DatePeremption As Date

        For I = 0 To fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows.Count - 1
            If fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Cocher") = True Then

                NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
                dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)

                NouvelArticle("CodeArticle") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("CodeArticle")

                NouvelArticle("NumeroCommande") = NumeroCommande
                NouvelArticle("Designation") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Designation")

                NouvelArticle("CodeForme") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("CodeForme")
                NouvelArticle("Stock") = CalculeStock(NouvelArticle("CodeArticle"))

                NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

                NouvelArticle("StockAlerte") = RecupererValeurExecuteScalaire("StockAlerte", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("EnCours") = 0

                NouvelArticle("QteJour") = RecupererValeurExecuteScalaire("QteACommander", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("Qte") = (NouvelArticle("StockAlerte") - NouvelArticle("Stock") + NouvelArticle("QteJour"))

                NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
                NouvelArticle("QteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

                NouvelArticle("LibelleForme") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("LibelleForme")

                '------------------ récupération de la date de péremption

                StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
                System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
                NouvelArticle("CodeArticle") + "' Order by DatePeremptionArticle ASC "

                cmdCommande.Connection = ConnectionServeur
                cmdCommande.CommandText = StrSQL

                Try
                    DatePeremption = cmdCommande.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If DatePeremption = #12:00:00 AM# Then
                    NouvelArticle("DatePeremption") = "01/01/1900"
                Else
                    NouvelArticle("DatePeremption") = DatePeremption
                End If

            End If

        Next

        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsCommande
            Catch ex As Exception
            End Try
            .DataMember = "COMMANDE_DETAILS"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qte"
            .Columns("Stock").Caption = "Stock"
            .Columns("DatePeremption").Caption = "Date de péremption"
            .Columns("PrixAchatHT").Caption = "Prix A HT "
            .Columns("TVA").Caption = "TVA"
            .Columns("TotalTTCAchat").Caption = "Total TTC "
            .Columns("StockAlerte").Caption = "S Alerte"
            .Columns("EnCours").Caption = "Encours "
            .Columns("QteJour").Caption = "Qte/Jour"
            .Columns("QteUnitaire").Caption = "Q unitaire"

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTTCAchat").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("CodeArticle").Locked = False
            .Splits(0).DisplayColumns("Designation").Locked = False
            .Splits(0).DisplayColumns("Qte").Locked = False

            .Splits(0).DisplayColumns("NumeroCommande").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Width = 80
            .Splits(0).DisplayColumns("Designation").Width = 280
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("Stock").Width = 50
            .Splits(0).DisplayColumns("DatePeremption").Width = 70
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 60
            .Splits(0).DisplayColumns("TVA").Width = 50
            .Splits(0).DisplayColumns("TotalTTCAchat").Width = 60
            .Splits(0).DisplayColumns("StockAlerte").Width = 50
            .Splits(0).DisplayColumns("EnCours").Width = 50
            .Splits(0).DisplayColumns("QteJour").Width = 50
            .Splits(0).DisplayColumns("QteUnitaire").Width = 50
            .Splits(0).DisplayColumns("CodeForme").Width = 0

            .Splits(0).DisplayColumns("CodeArticle").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gArticles)
        End With

        NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
        NouvelArticle("Designation") = "" 'initialisation pour que le calcul des montants ne compare pas 
        NouvelArticle("CodeArticle") = "" ' des chaines de caractères avec des NULL
        dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)

        NouvelleCommande = dsCommande.Tables("COMMANDE").NewRow()
        dsCommande.Tables("COMMANDE").Rows.Add(NouvelleCommande)

        Me.gArticles.Splits(0).DisplayColumns(0).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(3).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(5).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(6).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(7).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(8).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(9).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(10).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(11).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(12).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(13).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(14).AllowFocus = False
        '****************************************************************************************
        '********initialisation de la datatable article qui est utilisé dans la liste de ********
        '***recherche alimenté selon les entrés de l utilisateur dans la colonne designation*****
        '****************************************************************************************

        StrSQL = "SELECT CodeArticle,Designation,LibelleForme,PrixVenteTTC" + _
                 " FROM ARTICLE,FORME_ARTICLE WHERE ARTICLE.CodeForme=FORME_ARTICLE.CodeForme and " + _
                 "Designation LIKE '" + gArticles.Columns("Designation").Value + "'ORDER BY Designation"

        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande = New SqlDataAdapter(cmdCommande)
        daCommande.Fill(dsCommande, "ARTICLE")

        With gListeRecherche
            .Columns.Clear()
            .DataSource = dsCommande
            .DataMember = "ARTICLE"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code Article"
            .Columns("Designation").Caption = "Designation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("PrixVenteTTC").Caption = "Prix de vente"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centre tous les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Visible = False
            Next

            .Splits(0).DisplayColumns("CodeArticle").Visible = True
            .Splits(0).DisplayColumns("Designation").Visible = True
            .Splits(0).DisplayColumns("LibelleForme").Visible = True
            .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("Designation").Width = 300
            .Splits(0).DisplayColumns("LibelleForme").Width = 100
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 20
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
        End With

        cbCommande = New SqlCommandBuilder(daCommande)

        '-----------------------------------------------------------------------------
        TotalTTCAchat = 0.0
        TotalHTAchat = 0.0
        TVA = 0.0
        Timbre = 0.3
        TotalTVA = 0.0

        lTotHTAchat.Text = "0.000"
        lTotalTTCAchat.Text = "0.000"

        lTotalTVA.Text = "0.000"

        lOperateur.Text = "-"

        lDateCommande.Text = System.DateTime.Now
        cmbFournisseur.Text = ""
        lNumeroCommande.Text = "-------------"
        bAnnuler.Enabled = True
        bConfirmer.Enabled = True
        bFournisseur.Enabled = True
        tNumeroBlFact.Value = ""
        bAjouter.Enabled = False
        GroupeFournisseur.Enabled = True

        CalculerMontants()

        gArticles.Focus()
        gArticles.Col = 1
        gArticles.EditActive = True

    End Sub

    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        Try
            Quote(ValeurCle)
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurCle)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click
        Dim StrSQL As String = ""
        Dim i As Integer
        Dim Cond As String = ""
        Dim NumeroCommande As String = ""

        mode = "Ajout"

        '------------------------------ préparation des datatable vides 

        Try
            dsCommande.Tables("COMMANDE_DETAILS").Clear()
        Catch ex As Exception
        End Try

        Try
            dsCommande.Tables("COMMANDE").Clear()
        Catch ex As Exception
        End Try

        Try
            dsCommande.Tables("ARTICLE_JOURNALIERE").Clear()
        Catch ex As Exception
        End Try

        Try
            dsCommande.Tables("ARTICLE_GROUPEE").Clear()
        Catch ex As Exception
        End Try

        '----------------------------- chargement des Entêtes des ventes 

        StrSQL = "SELECT top 0 * FROM COMMANDE ORDER BY NumeroCommande ASC"
        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande = New SqlDataAdapter(cmdCommande)
        daCommande.Fill(dsCommande, "COMMANDE")
        cbCommande = New SqlCommandBuilder(daCommande)

        '----------------------------- chargement des détails des ventes 

        StrSQL = "SELECT NumeroCommande,CodeArticle,Designation,LibelleForme,Qte,Stock,DatePeremption," + _
        "PrixAchatHT,TVA,TotalTTCAchat,StockAlerte,EnCours,QteJour,QteUnitaire,COMMANDE_DETAILS.CodeForme FROM " + _
        "COMMANDE_DETAILS,FORME_ARTICLE WHERE COMMANDE_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
        "AND NumeroCommande ='0'"
        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande.Fill(dsCommande, "COMMANDE_DETAILS")
        cbCommande = New SqlCommandBuilder(daCommande)

        With gArticles
            .Columns.Clear()
            .DataSource = dsCommande
            .DataMember = "COMMANDE_DETAILS"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qte"
            .Columns("Stock").Caption = "Stock"
            .Columns("DatePeremption").Caption = "Date de péremption"
            .Columns("PrixAchatHT").Caption = "Prix A HT "
            .Columns("TVA").Caption = "TVA"
            .Columns("TotalTTCAchat").Caption = "Total TTC "
            .Columns("StockAlerte").Caption = "S Alerte"
            .Columns("EnCours").Caption = "Encours "
            .Columns("QteJour").Caption = "Qte/Jour"
            .Columns("QteUnitaire").Caption = "Q unitaire"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTTCAchat").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("CodeArticle").Locked = False
            .Splits(0).DisplayColumns("Designation").Locked = False
            .Splits(0).DisplayColumns("Qte").Locked = False

            .Splits(0).DisplayColumns("NumeroCommande").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Width = 80
            .Splits(0).DisplayColumns("Designation").Width = 280
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("Stock").Width = 50
            .Splits(0).DisplayColumns("DatePeremption").Width = 70
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 60
            .Splits(0).DisplayColumns("TVA").Width = 50
            .Splits(0).DisplayColumns("TotalTTCAchat").Width = 60
            .Splits(0).DisplayColumns("StockAlerte").Width = 50
            .Splits(0).DisplayColumns("EnCours").Width = 50
            .Splits(0).DisplayColumns("QteJour").Width = 50
            .Splits(0).DisplayColumns("QteUnitaire").Width = 50
            .Splits(0).DisplayColumns("CodeForme").Visible = False

            .Splits(0).DisplayColumns("CodeArticle").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True

        End With
        'ajout d'un nouvel enregistrement vide dans les datatables convenables
        NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
        NouvelArticle("Designation") = "" 'initialisation pour que le calcul des montants ne compare pas 
        NouvelArticle("CodeArticle") = "" ' des chaines de caractères avec des NULL
        dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)

        NouvelleCommande = dsCommande.Tables("COMMANDE").NewRow()
        dsCommande.Tables("COMMANDE").Rows.Add(NouvelleCommande)

        Me.gArticles.Splits(0).DisplayColumns(0).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(3).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(5).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(6).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(7).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(8).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(9).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(10).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(11).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(12).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(13).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(14).AllowFocus = False
        'Me.gArticles.Splits(0).DisplayColumns(15).AllowFocus = False

        '****************************************************************************************
        '********************** initialisation des differents zones de textes *******************
        '*************************** initialisation des variables globaux************************
        '****************************************************************************************

        TotalTTCAchat = 0.0
        TotalHTAchat = 0.0
        TVA = 0.0
        Timbre = 0.3
        TotalTVA = 0.0

        lTotHTAchat.Text = "0.000"
        lTotalTTCAchat.Text = "0.000"

        lTotalTVA.Text = "0.000"

        lOperateur.Text = "-"

        lDateCommande.Text = System.DateTime.Now
        cmbFournisseur.Text = ""
        lNumeroCommande.Text = "-------------"

        bAnnuler.Enabled = True
        bConfirmer.Enabled = True
        bFournisseur.Enabled = True
        tNumeroBlFact.Value = ""

        bAjouter.Enabled = False

        GroupeFournisseur.Enabled = True

        gArticles.Focus()
        gArticles.Col = 1
        gArticles.EditActive = True

        Dim MyCritereDeSelectionCommande As New fCritereDeSelectionCommande
        MyCritereDeSelectionCommande.ShowDialog()

        TypeCommande = fCritereDeSelectionCommande.TypeCommande
        NombreDeJour = fCritereDeSelectionCommande.NombreDeJour
        SansManquantDepuis = fCritereDeSelectionCommande.SansManquantDepuis
        Reference = fCritereDeSelectionCommande.Reference
        mois = fCritereDeSelectionCommande.mois
        DebutPeriode = fCritereDeSelectionCommande.DebutPeriode
        FinPeriode = fCritereDeSelectionCommande.FinPeriode
        Section = fCritereDeSelectionCommande.Section
        DebutIntervalle = fCritereDeSelectionCommande.DebutIntervalle
        FinIntervalle = fCritereDeSelectionCommande.FinIntervalle
        Forme = fCritereDeSelectionCommande.Forme
        Categorie = fCritereDeSelectionCommande.Categorie
        Labo = fCritereDeSelectionCommande.Labo
        Rayon = fCritereDeSelectionCommande.Rayon
        RayonSelectionne = fCritereDeSelectionCommande.RayonSelectionne
        Trie = fCritereDeSelectionCommande.Trie
        CommandeEnCours = fCritereDeSelectionCommande.CommandeEnCours

        MyCritereDeSelectionCommande.Dispose()
        MyCritereDeSelectionCommande.Close()

        '***************************************************************************************************
        '************************************ Cas de HIT PARADE ********************************************
        '***************************************************************************************************

        NumeroCommande = RecupereNumero()



        If TypeCommande = "HIT PARADE" Then
            Exit Sub
        End If

        '***************************************************************************************************
        '************************************ Cas de Journalière ********************************************
        '***************************************************************************************************

        If TypeCommande = "JOURNALIERE" Then
            If SansManquantDepuis <> "" Then
                Cond = Cond + " AND ARTICLE.DateAlerte >'" + SansManquantDepuis + "'"
            End If
            If Section = "INTERVALLE" Then
                Cond = Cond + " AND Section > " + DebutIntervalle.ToString + " AND Section < " + FinIntervalle.ToString
            End If
            If Forme <> 0 Then
                Cond = Cond + " AND ARTICLE.CodeForme = " + Forme.ToString
            End If
            If Categorie <> 0 Then
                Cond = Cond + " AND CodeCategorie = " + Categorie.ToString
            End If
            If Labo <> 0 Then
                Cond = Cond + " AND CodeLabo = " + Labo.ToString
            End If
            If Rayon = "RAYON" Then
                Cond = Cond + " AND Rayon = " + RayonSelectionne
            End If
            Cond = Cond + " ORDER BY " + Trie

            StrSQL = "SELECT CodeArticle,Designation,ARTICLE.CodeForme,PrixAchatHT,TVA" + _
            ",StockAlerte,QteACommander,QuantiteUnitaire,LibelleForme FROM ARTICLE,FORME_ARTICLE WHERE  " + _
            " ARTICLE.CodeForme=FORME_ARTICLE.CodeForme AND " + _
            "(SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE LOT_ARTICLE.CodeArticle =ARTICLE.CodeArticle " + _
            "AND (LOT_ARTICLE.DatePeremptionArticle >' " + System.DateTime.Now + _
            "' OR LOT_ARTICLE.DatePeremptionArticle='01/01/1900' OR LOT_ARTICLE.DatePeremptionArticle is null ))<ARTICLE.StockAlerte " + Cond

            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL
            daCommande1 = New SqlDataAdapter(cmdCommande)
            daCommande.Fill(dsCommande, "ARTICLE_JOURNALIERE")
            cbCommande = New SqlCommandBuilder(daCommande)

            Dim J As Integer
            'Dim NouvelArticleSelectionne As DataRow = Nothing
            Dim DataRowRecherche As DataRow = Nothing
            Dim DatePeremption As Date

            For J = 0 To dsCommande.Tables("ARTICLE_JOURNALIERE").Rows.Count - 1

                DataRowRecherche = dsCommande.Tables("ARTICLE_JOURNALIERE").Rows(J)

                NouvelArticle("NumeroCommande") = NumeroCommande
                NouvelArticle("CodeArticle") = DataRowRecherche.Item("CodeArticle")
                NouvelArticle("Designation") = DataRowRecherche.Item("Designation")
                NouvelArticle("CodeForme") = DataRowRecherche.Item("CodeForme")
                NouvelArticle("Stock") = CalculeStock(NouvelArticle("CodeArticle"))
                NouvelArticle("PrixAchatHT") = DataRowRecherche.Item("PrixAchatHT")
                NouvelArticle("TVA") = DataRowRecherche.Item("TVA")
                NouvelArticle("StockAlerte") = DataRowRecherche.Item("StockAlerte")
                NouvelArticle("EnCours") = 0
                NouvelArticle("QteJour") = DataRowRecherche.Item("QteACommander")
                NouvelArticle("Qte") = (NouvelArticle("StockAlerte") - NouvelArticle("Stock") + NouvelArticle("QteJour")) * NombreDeJour
                NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
                NouvelArticle("QteUnitaire") = DataRowRecherche.Item("QuantiteUnitaire")
                NouvelArticle("LibelleForme") = DataRowRecherche.Item("LibelleForme")
                '------------------ récupération de la date de péremption

                StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
                System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
                NouvelArticle("CodeArticle") + "' Order by DatePeremptionArticle ASC "

                cmdCommande.Connection = ConnectionServeur
                cmdCommande.CommandText = StrSQL

                Try
                    DatePeremption = cmdCommande.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If DatePeremption = #12:00:00 AM# Then
                    NouvelArticle("DatePeremption") = "01/01/1900"
                Else
                    NouvelArticle("DatePeremption") = DatePeremption
                End If

                NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
                dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)
                NouvelArticle("Designation") = "" 'initialisation pour que le calcul des montants ne compare pas 
                NouvelArticle("CodeArticle") = "" ' des chaines de caractères avec des NULL

                gArticles.Refresh()

            Next J
            cmbType.Text = "JOURNALIERE"
            cmbType.Enabled = False
            CalculerMontants()
        End If

        '***************************************************************************************************
        '************************************ Cas de Groupée ********************************************
        '***************************************************************************************************

        Dim DebutDeConge As Date = Convert.ToDateTime("01/07/2012")
        Dim FinDeConge As Date = Convert.ToDateTime("15/07/2012")
        Dim NombreDeDimanche As Integer = 0
        Dim NombreDeDimancheConge As Integer = 0
        Dim NombreDesJoursDeReference As Integer = 0
        If TypeCommande = "GROUPEE" Then

            '---------------------------- calcul des jours en éléminant les jours de congés

            If NombreDeJour <> 1 Then
                If DebutDeConge > System.DateTime.Now And FinDeConge < DateAdd(DateInterval.Day, NombreDeJour, System.DateTime.Now) Then
                    NombreDeDimancheConge = (FinDeConge - DebutDeConge).Days / 7
                    NombreDeDimanche = NombreDeJour / 7 - NombreDeDimancheConge
                    NombreDeJour = NombreDeJour - (FinDeConge - DebutDeConge).Days - NombreDeDimanche
                End If
                If DebutDeConge < System.DateTime.Now And FinDeConge < DateAdd(DateInterval.Day, NombreDeJour, System.DateTime.Now) And FinDeConge > System.DateTime.Now Then
                    NombreDeDimancheConge = (FinDeConge - System.DateTime.Now).Days / 7
                    NombreDeDimanche = NombreDeJour / 7 - NombreDeDimancheConge
                    NombreDeJour = NombreDeJour - (FinDeConge - DebutDeConge).Days - NombreDeDimanche
                End If
                If DebutDeConge > System.DateTime.Now And DebutDeConge < DateAdd(DateInterval.Day, NombreDeJour, System.DateTime.Now) And FinDeConge > DateAdd(DateInterval.Day, NombreDeJour, System.DateTime.Now) Then
                    NombreDeDimancheConge = (FinDeConge - System.DateTime.Now).Days / 7
                    NombreDeDimanche = NombreDeJour / 7 - NombreDeDimancheConge
                    NombreDeJour = NombreDeJour - (FinDeConge - DebutDeConge).Days - NombreDeDimanche
                End If
            End If
            '---------------------------- Cas ou on a une période
            If Reference = "PERIODE" Then
                Dim JourMoisDebut As String = ""
                Dim JourMoisFin As String = ""
                Dim AnneeDebut As String = ""
                Dim AnneeFin As String = ""

                NombreDesJoursDeReference = DateDiff(DateInterval.Day, Convert.ToDateTime(DebutPeriode), Convert.ToDateTime(FinPeriode)) + 20

                JourMoisDebut = DebutPeriode.Substring(0, 6)
                AnneeDebut = DebutPeriode.Substring(6, 4)
                JourMoisFin = FinPeriode.Substring(0, 6)
                AnneeFin = FinPeriode.Substring(6, 4)

                StrSQL = "SELECT distinct(Codearticle),sum(Qte) as QTE FROM VENTE_DETAIL,VENTE WHERE VENTE.NumeroVente=" + _
                         "VENTE_DETAIL.NumeroVente AND (VENTE.Date between '" + JourMoisDebut + _
                         (Convert.ToInt32(AnneeDebut) - 1).ToString + "' AND '" + JourMoisFin + _
                         (Convert.ToInt32(AnneeFin) - 1).ToString + "'" + _
                         " OR VENTE.Date between '" + _
                         DateAdd(DateInterval.Day, -20, System.DateTime.Now) + "' AND '" + System.DateTime.Now + "')" + _
                         " GROUP BY Codearticle"

                cmdCommande.Connection = ConnectionServeur
                cmdCommande.CommandText = StrSQL
                daCommande.Fill(dsCommande, "ARTICLE_GROUPEE")
                cbCommande = New SqlCommandBuilder(daCommande)



                '---------------------------- Cas ou on a un mois
            ElseIf Reference = "MOIS" Then

                NombreDesJoursDeReference = 50

                StrSQL = "SELECT distinct(Codearticle),sum(Qte) as QTE FROM VENTE_DETAIL,VENTE WHERE VENTE.NumeroVente=" + _
                         "VENTE_DETAIL.NumeroVente AND (MONTH(VENTE.Date)=" + mois.ToString + _
                         " AND YEAR(VENTE.Date)=" + (Convert.ToInt32(System.DateTime.Now.Year) - 1).ToString + _
                         " OR VENTE.Date between '" + DateAdd(DateInterval.Day, -20, System.DateTime.Now) + _
                         "' AND '" + System.DateTime.Now + "')" + " GROUP BY Codearticle"

                cmdCommande.Connection = ConnectionServeur
                cmdCommande.CommandText = StrSQL
                daCommande.Fill(dsCommande, "ARTICLE_GROUPEE")
                cbCommande = New SqlCommandBuilder(daCommande)

            End If

            '-------------------------- chargement de la tables COMMANDE_DETAILS

            Dim J As Integer
            Dim DataRowRecherche As DataRow = Nothing
            Dim DatePeremption As Date

            For J = 0 To dsCommande.Tables("ARTICLE_GROUPEE").Rows.Count - 1

                DataRowRecherche = dsCommande.Tables("ARTICLE_GROUPEE").Rows(J)

                NouvelArticle("NumeroCommande") = NumeroCommande
                NouvelArticle("CodeArticle") = DataRowRecherche.Item("CodeArticle")
                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("Stock") = CalculeStock(NouvelArticle("CodeArticle"))
                NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("StockAlerte") = RecupererValeurExecuteScalaire("StockAlerte", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("EnCours") = 0
                NouvelArticle("QteJour") = RecupererValeurExecuteScalaire("QteACommander", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("Qte") = Int((DataRowRecherche.Item("QTE") / NombreDesJoursDeReference) * NombreDeJour)
                NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
                NouvelArticle("QteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", "FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
                '------------------ récupération de la date de péremption

                StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
                System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
                NouvelArticle("CodeArticle") + "' Order by DatePeremptionArticle ASC "

                cmdCommande.Connection = ConnectionServeur
                cmdCommande.CommandText = StrSQL

                Try
                    DatePeremption = cmdCommande.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If DatePeremption = #12:00:00 AM# Then
                    NouvelArticle("DatePeremption") = "01/01/1900"
                Else
                    NouvelArticle("DatePeremption") = DatePeremption
                End If

                NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
                dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)
                NouvelArticle("Designation") = "" 'initialisation pour que le calcul des montants ne compare pas 
                NouvelArticle("CodeArticle") = "" ' des chaines de caractères avec des NULL

                gArticles.Refresh()

            Next J

            cmbType.Text = "GROUPEE"
            cmbType.Enabled = False
            CalculerMontants()

        End If
    End Sub

    Private Sub gArticles_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gArticles.Change
        If (gArticles.Col = 2 And gArticles.Columns("Designation").Value <> "") Or gArticles.Col = 1 Then
            Dim StrSQL1 As String = ""
            Dim I As Integer
            Dim NbLigne As Integer
            NbLigne = gArticles.RowCount

            With gListeRecherche
                .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(1).Width
                .Top = Me.gArticles.Top + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight
            End With

            Try
                dsCommande.Tables("ARTICLE").Clear()
            Catch ex As Exception

            End Try
            If gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 And gArticles.Col = 2 Then
                gListeRecherche.Visible = True
            Else
                gListeRecherche.Visible = False
            End If

            'chargement des articles qui sont mis en jeu
            If gArticles.Col = 2 Then
                If gArticles.Columns("Designation").Value.ToString.Length > 1 Then
                    If gArticles.Columns("Designation").Value.ToString.IndexOf(".") > 0 Then
                        StrSQL1 = "SELECT CodeArticle,Designation,LibelleForme,PrixVenteTTC" + _
                           " FROM ARTICLE,FORME_ARTICLE WHERE ARTICLE.CodeForme=FORME_ARTICLE.CodeForme and " + _
                           "ltrim(str(PrixVenteTTC,10,3)) LIKE '" + gArticles.Columns("Designation").Value + "%'ORDER BY PrixVenteTTC"
                    Else
                        StrSQL1 = "SELECT CodeArticle,Designation,LibelleForme,PrixVenteTTC" + _
                         " FROM ARTICLE,FORME_ARTICLE WHERE ARTICLE.CodeForme=FORME_ARTICLE.CodeForme and " + _
                         "Designation LIKE '" + gArticles.Columns("Designation").Value + "%'ORDER BY Designation"
                    End If
                Else
                    StrSQL1 = "SELECT CodeArticle,Designation,LibelleForme,PrixVenteTTC" + _
                         " FROM ARTICLE,FORME_ARTICLE WHERE ARTICLE.CodeForme=FORME_ARTICLE.CodeForme and " + _
                         "Designation LIKE '" + gArticles.Columns("Designation").Value + "%'ORDER BY Designation"
                End If
            ElseIf gArticles.Col = 1 Then
                StrSQL1 = "SELECT CodeArticle,Designation,LibelleForme,PrixVenteTTC" + _
                         " FROM ARTICLE,FORME_ARTICLE WHERE ARTICLE.CodeForme=FORME_ARTICLE.CodeForme and " + _
                         "CodeArticle LIKE '" + gArticles.Columns("CodeArticle").Value + "'ORDER BY Designation"

            End If
            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL1
            daCommande = New SqlDataAdapter(cmdCommande)
            daCommande.Fill(dsCommande, "ARTICLE")

            If dsCommande.Tables("ARTICLE").Rows.Count > 0 Then
                dr = dsCommande.Tables("ARTICLE").Rows(0)
            End If

            With gListeRecherche
                .Columns.Clear()
                .DataSource = dsCommande
                .DataMember = "ARTICLE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code Article"
                .Columns("Designation").Caption = "Designation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("PrixVenteTTC").Caption = "Prix de vente"

                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' CentreR tous les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Visible = False
                Next

                .Splits(0).DisplayColumns("CodeArticle").Visible = True
                .Splits(0).DisplayColumns("Designation").Visible = True
                .Splits(0).DisplayColumns("LibelleForme").Visible = True
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("Designation").Width = 300
                .Splits(0).DisplayColumns("LibelleForme").Width = 100
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120

                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.Aqua
                .Splits(0).DisplayColumns("LibelleForme").Style.BackColor = Color.Aqua
                .Splits(0).DisplayColumns("PrixVenteTTC").Style.BackColor = Color.Aqua

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 20
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
            End With
        End If
    End Sub
    Public Function RecupereNumero()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max([NumeroCommande]) FROM [COMMANDE]"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If
        Return ValeurRetour
    End Function

    Private Sub gArticles_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyUp
        Dim i As Integer = 0
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        ' Dim TestNumeroLot As Integer
        Dim QuantiteAAjouter As Integer = 0

        '****************************************************************************************
        '*********************** test si on est en mode saisi ou non* ***************************
        '****************************************************************************************

        If mode <> "Ajout" Then
            Exit Sub
        End If

        '****************************************************************************************
        '****** suppression du dernier ligne vide si on a deux lignes au mm temps vide **********
        '****************************** cas ou on supprime dernier ligne ************************
        '****************************************************************************************

        If dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 = 1 And gArticles(0, "CodeArticle") = "" And gArticles(1, "CodeArticle") = "" Then
            gArticles.MoveLast()
            gArticles.MovePrevious()
            gArticles.Delete()
        End If

        '****************************************************************************************
        '******* test du type de la valeur d'entrée dans la colonne quantité (numéric) **********
        '**************** test du  valeur d'entrée dans la colonne quantité < 99999 *************
        '****************************************************************************************

        If gArticles.Col = 4 Then
            If gArticles.Columns("Qte").Value.ToString <> "-" Then   ' pour les quantité négatives
                If IsNumeric(gArticles.Columns("Qte").Value) = False Then
                    gArticles.Columns("Qte").Value = ""
                    gArticles.Col = 4
                    gArticles.EditActive = True
                    Exit Sub
                End If
                If (gArticles.Columns("Qte").Value = 0) And e.KeyCode = Keys.Enter Then
                    gArticles.Columns("Qte").Value = ""
                    gArticles.Col = 4
                    gArticles.EditActive = True
                    Exit Sub
                End If
                If (gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999) And e.KeyCode = Keys.Enter Then
                    gArticles.Columns("Qte").Value = "1"
                    MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Col = 4
                    gArticles.EditActive = True
                    Exit Sub
                End If
            End If

        End If

        '****************************************************************************************
        '*********************** suppression de la ligne selectionnées  *************************
        '****************************************************************************************

        If (e.KeyCode = Keys.F7 Or e.KeyCode = Keys.Delete) And gArticles.Row <= dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 Then
            gArticles.Delete()
            CalculerMontants()
            If dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 <= 0 Then
                bAjouter_Click(sender, e)
            End If
            Exit Sub
        End If

        '****************************************************************************************
        '************************************** recherche par code***** *************************
        '****************************************************************************************

        If gArticles.Col = 1 And e.KeyCode = Keys.Enter And gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 Then
            ChargerDetailArticle(gArticles.Columns("CodeArticle").Value)
            Exit Sub
        ElseIf gArticles.Col = 1 And e.KeyCode = Keys.Enter And gArticles.Row < dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 Then
            gArticles.Col = 2
        End If

        '****************************************************************************************
        '********* masquer la liste de recherche si la designation est vide *********************
        '****************************************************************************************

        If e.KeyCode = Keys.Back And gArticles.Columns("Designation").Value = "" And gArticles.Col = 2 Then
            gListeRecherche.Visible = False
        End If

        '****************************************************************************************
        '********* pour passer à la navigation dans la petite liste de recherhce ****************
        '****************************************************************************************

        If (e.KeyCode = Keys.Down Or e.KeyCode = Keys.Up) And gListeRecherche.Visible = True Then
            gListeRecherche.Focus()
            gListeRecherche.Col = 1
        End If

        '****************************************************************************************
        '******* si l'artcile n'existe pas dans la liste de recherhce sa quantité doit etre 0 ***
        '****************************************************************************************

        If dsCommande.Tables("ARTICLE").Rows.Count <= 0 And gListeRecherche.Visible = True Then
            gArticles.Columns("Qte").Value = 0
            gArticles.Col = 2
        End If

        '****************************************************************************************
        '*********************************** calcul des montants ********************************
        '****************************************************************************************

        If (gArticles.Col = 4) And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) Then
            CalculerMontants()
        End If

        '****************************************************************************************
        '*************** traitement du clique sur le bouton ENTREE selon la colonne *************
        '****************************************************************************************

        If e.KeyCode = Keys.Enter And (dsCommande.Tables("ARTICLE").Rows.Count > 0 Or gArticles.Row <> dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1) Then
            gListeRecherche_KeyUp(sender, e)     ' pour charger les informations de l'article a partir du fiche article

            If gArticles.Col = 2 Then
                If gArticles.Columns("CodeArticle").Value = "" Then    ' si l'article choisit n'existe pas dans la liste de recherche
                    gArticles.Col = 2
                    gArticles.Columns("Designation").Value = ""
                Else
                    gArticles.Col = 4
                End If

            ElseIf gArticles.Col = 4 And e.KeyCode = Keys.Enter Then
                ' si l'utilisateur a choisit un article on ajoute un nouvel enregistrement dans la datatable puis on passe à la ligne
                ' suivant dans la gride avec un test si cet article est déja choisi ou non si c'est le cas on ajoute seulement la quntité

                If gArticles.Columns("Designation").Value <> "" Then
                    QuantiteAAjouter = gArticles.Columns("Qte").Value
                    If gArticles.RowCount >= 1 And gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 Then
                        Do While i < gArticles.RowCount - 1
                            If gArticles(i, "CodeArticle") = dr.Item("CodeArticle") Then
                                gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QuantiteAAjouter).ToString
                                gArticles.MoveLast()
                                gArticles.Delete()
                            End If
                            i = i + 1
                        Loop
                    End If
                End If
                If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                    NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
                    NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                    NouvelArticle("CodeArticle") = ""
                    dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)
                End If
                gArticles.MoveLast()
                Try
                    dsCommande.Tables("ARTICLE").Clear()
                Catch ex As Exception
                End Try
                gArticles.Col = 1

            End If
        End If
    End Sub

    Private Sub gListeRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListeRecherche.KeyUp
        ' dans cet évènenment on teste si l évènenment est due à un clique sur le boutton entrée et si l'utilisateur a choisi 
        ' un article et si le curseur est dans la colonne de designation 

        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date

        If gListeRecherche.Visible = False Then
            Exit Sub
        End If
        If e.KeyCode = Keys.Back Then
            gArticles.Focus()
            gArticles.Col = 2
            gArticles.MoveLast()
            gArticles.EditActive = True
        End If

        Dim j As Integer
        Dim NumeroLigne As Integer
        Dim DataRowRecherche As DataRow
        If e.KeyCode = Keys.Enter And (gArticles.Col = 4 Or gArticles.Col = 2) Then    'And gArticles.Columns("Designation").Value <> ""
            If dsCommande.Tables("ARTICLE").Rows.Count > 0 Then

                '--------------------- rehcerche de l'index de la ligne qui contient l'article recherché ---------------

                For j = 0 To dsCommande.Tables("ARTICLE").Rows.Count - 1
                    DataRowRecherche = dsCommande.Tables("ARTICLE").Rows(j)
                    If DataRowRecherche.Item("CodeArticle") = gListeRecherche.Columns("CodeArticle").Value Then
                        NumeroLigne = j
                    End If
                Next

                '------------------- chargement des données ---------------------------------------------- 

                dr = dsCommande.Tables("ARTICLE").Rows(NumeroLigne)
                NouvelArticle("NumeroCommande") = RecupereNumero()
                NouvelArticle("CodeArticle") = dr.Item("CodeArticle")
                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
                NouvelArticle("Qte") = 1
                NouvelArticle("Stock") = CalculeStock(dr.Item("CodeArticle"))
                NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
                NouvelArticle("StockAlerte") = RecupererValeurExecuteScalaire("StockAlerte", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                'NouvelArticle("EnCours") = RecupererValeurExecuteScalaire("StockAlerte", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("QteJour") = RecupererValeurExecuteScalaire("QteACommander", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("QteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))


                '------------------ récupération de la date de péremption

                StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
                System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
                dr.Item("CodeArticle") + "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    DatePeremption = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If DatePeremption = #12:00:00 AM# Then
                    NouvelArticle("DatePeremption") = "01/01/1900"
                Else
                    NouvelArticle("DatePeremption") = DatePeremption
                End If

                gArticles.Refresh()
            End If
            gListeRecherche.Visible = False
            gArticles.Focus()
            gArticles.Col = 2
        End If
    End Sub

    Public Sub ChargerDetailArticle(ByVal CodeArticle As String)
        Dim resultat As String
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date '= "1 / 1 / 1900"

        resultat = RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle)
        If resultat <> "" Then

            NouvelArticle("NumeroCommande") = RecupereNumero()
            NouvelArticle("CodeArticle") = CodeArticle
            NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)
            NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
            NouvelArticle("Qte") = 1
            NouvelArticle("Stock") = CalculeStock(CodeArticle)
            NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", CodeArticle)
            NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", CodeArticle)
            NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
            NouvelArticle("StockAlerte") = RecupererValeurExecuteScalaire("StockAlerte", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            'NouvelArticle("EnCours") = RecupererValeurExecuteScalaire("StockAlerte", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("QteJour") = RecupererValeurExecuteScalaire("QteACommander", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("QteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))

            '----------------------- récupération de la date de péremption

            StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
            System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
            dr.Item("CodeArticle") + "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                DatePeremption = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If DatePeremption = #12:00:00 AM# Then
                NouvelArticle("DatePeremption") = "01/01/1900"
            Else
                NouvelArticle("DatePeremption") = DatePeremption
            End If

            gArticles.Refresh()
            gArticles.Col = 4
        Else
            gArticles.Columns("CodeArticle").Value = ""
            gArticles.Col = 2
        End If
    End Sub
    Public Sub CalculerMontants()
        Dim i As Integer = 0
        TotalTTCAchat = 0.0
        TotalHTAchat = 0.0
        TotalTVA = 0.0

        Do While i < gArticles.RowCount
            If gArticles(i, "Designation") <> "" Then
                gArticles(i, "TotalTTCAchat") = Math.Round(gArticles(i, "PrixAchatHT") * gArticles(i, "Qte") * (100 + gArticles(i, "TVA")) / 100, 3)
                TotalHTAchat = Math.Round(TotalHTAchat + gArticles(i, "PrixAchatHT") * gArticles(i, "Qte"), 3)
                TotalTTCAchat = TotalTTCAchat + gArticles(i, "TotalTTCAchat")
                TotalTVA = TotalTVA + gArticles(i, "PrixAchatHT") * gArticles(i, "TVA") / 100 * gArticles(i, "Qte")

            End If
            i = i + 1
        Loop

        lTotHTAchat.Text = TotalHTAchat
        lTotalTTCAchat.Text = TotalTTCAchat.ToString
        lTotalTVA.Text = TotalTVA.ToString

    End Sub

    Private Sub bFournisseur_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFournisseur.Click
        cmbFournisseur.Focus()
    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        Dim I As Integer = 0
        Dim cmd As New SqlCommand
        Dim NumeroLot As Integer = 0
        Dim TestNumeroLot As String = ""
        Dim NouveauNumeroLot As String = ""
        Dim QuantiteLotSansNumero As Integer = 0
        Dim QuantiteLotAInsere As Integer = 0
        NumeroCommande = RecupereNumero()

        If dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle") = "" Then
            MsgBox("Commande Vide !", MsgBoxStyle.Critical, "Erreur")
            If dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 < 0 Then
                bAjouter_Click(sender, e)
            End If
            '----------------------
            gArticles.Col = 1
            gArticles.EditActive = True
            Exit Sub
        End If

        If cmbFournisseur.Text = "" Then
            MsgBox("Veuillez choisir un Fournisseur !", MsgBoxStyle.Critical, "Erreur")
            cmbFournisseur.Focus()
            Exit Sub
        End If

        '----------------------------- contrôle si la liste est vide ou nn



        '****************************************************************************************
        '************************* enregistrement de l'entête de l'achat ************************
        '****************************************************************************************

        dsCommande.Tables("COMMANDE").Clear()
        Dim StrSQL As String = ""
        StrSQL = "SELECT top(0) * FROM COMMANDE ORDER BY NumeroCommande ASC"
        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande = New SqlDataAdapter(cmdCommande)
        daCommande.Fill(dsCommande, "COMMANDE")
        cbCommande = New SqlCommandBuilder(daCommande)
        dr = dsCommande.Tables("COMMANDE").NewRow()

        With dsCommande
            dr.Item("NumeroCommande") = NumeroCommande
            dr.Item("Date") = System.DateTime.Now
            dr.Item("TotalHT") = TotalHTAchat
            dr.Item("TotalTTC") = TotalTTCAchat
            dr.Item("TotalTVA") = lTotalTVA.Text
            dr.Item("LibellePoste") = System.Environment.GetEnvironmentVariable("Poste")
            dr.Item("CodePersonnel") = 1 'Operateur
            dr.Item("CodeFournisseur") = cmbFournisseur.SelectedValue
            dr.Item("Note") = "rien"

            dsCommande.Tables("COMMANDE").Rows.Add(dr)
        End With
        Try
            daCommande.Update(dsCommande, "COMMANDE")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsCommande.Reset()
        End Try

        '****************************************************************************************
        '************************* enregistrement des détails de l'achat ************************
        '****************************************************************************************

        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = "Select top(0) * FROM COMMANDE_DETAILS"
        daCommande = New SqlDataAdapter(cmdCommande)
        daCommande.Fill(dsCommande, "COMMANDE_DETAILS")
        cbCommande = New SqlCommandBuilder(daCommande)

        '-------------------------- élémination des lignes vides 
        I = 0
        Do While I < dsCommande.Tables("COMMANDE_DETAILS").Rows.Count
            If dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Delete()
            End If
            I = I + 1
        Loop

        Try
            daCommande.Update(dsCommande, "COMMANDE_DETAILS")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsCommande.Reset()
        End Try


        Init()
        mode = "Consultation"

        bAnnuler.Enabled = False
        bConfirmer.Enabled = False
        bFournisseur.Enabled = False
        bAjouter.Enabled = True

        fMain.Tab.SelectedTab.Dispose()

    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Init()
        mode = "Consultation"

        bAnnuler.Enabled = False
        bConfirmer.Enabled = False
        bFournisseur.Enabled = False
        bAjouter.Enabled = True
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub ChargerDetails()
        Dim StrSQL As String
        Dim I As Integer
        dsChargementCommande.Tables("COMMANDE_DETAILS").Clear()

        '------------------------------------ chargement des détails des Achats 

        StrSQL = "SELECT NumeroCommande,CodeArticle,Designation,LibelleForme,Qte,Stock,DatePeremption," + _
        "PrixAchatHT,TVA,TotalTTCAchat,StockAlerte,EnCours,QteJour,QteUnitaire FROM " + _
        "COMMANDE_DETAILS,FORME_ARTICLE WHERE COMMANDE_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
        "AND NumeroCommande =" + Quote(NumeroCommande) + ""
        cmdChargementCommande.Connection = ConnectionServeur
        cmdChargementCommande.CommandText = StrSQL
        daChargementCommande = New SqlDataAdapter(cmdChargementCommande)
        daChargementCommande.Fill(dsChargementCommande, "COMMANDE_DETAILS")

        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsChargementCommande
            Catch ex As Exception
            End Try
            .DataMember = "COMMANDE_DETAILS"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qte"
            .Columns("Stock").Caption = "Stock"
            .Columns("DatePeremption").Caption = "Date de péremption"
            .Columns("PrixAchatHT").Caption = "Prix A HT "
            .Columns("TVA").Caption = "TVA"
            .Columns("TotalTTCAchat").Caption = "Total TTC "
            .Columns("StockAlerte").Caption = "S Alerte"
            .Columns("EnCours").Caption = "Encours "
            .Columns("QteJour").Caption = "Qte/Jour"
            .Columns("QteUnitaire").Caption = "Q unitaire"

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns(2).Style.HorizontalAlignment = AlignHorzEnum.Near
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("NumeroCommande").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Width = 80
            .Splits(0).DisplayColumns("Designation").Width = 290
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("Stock").Width = 50
            .Splits(0).DisplayColumns("DatePeremption").Width = 60
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 60
            .Splits(0).DisplayColumns("TVA").Width = 50
            .Splits(0).DisplayColumns("TotalTTCAchat").Width = 60
            .Splits(0).DisplayColumns("StockAlerte").Width = 50
            .Splits(0).DisplayColumns("EnCours").Width = 50
            .Splits(0).DisplayColumns("QteJour").Width = 50
            .Splits(0).DisplayColumns("QteUnitaire").Width = 50

            .Splits(0).DisplayColumns("CodeArticle").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

        End With

    End Sub

    Private Sub lTotalTTC_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotalTTCAchat.TextChanged
        lTotalTTCAchat.Text = lTotalTTCAchat.Text
        If lTotalTTCAchat.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lTotalTTCAchat.Text, ".")
            If lTotalTTCAchat.Text.Length - x = 1 Then
                lTotalTTCAchat.Text = lTotalTTCAchat.Text + ("00")
            ElseIf lTotalTTCAchat.Text.Length - x = 2 Then
                lTotalTTCAchat.Text = lTotalTTCAchat.Text + ("0")
            End If
        Else
            lTotalTTCAchat.Text = lTotalTTCAchat.Text + ".000"
        End If
    End Sub

    Private Sub lTotHT_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotHTAchat.TextChanged
        lTotHTAchat.Text = lTotHTAchat.Text
        If lTotHTAchat.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lTotHTAchat.Text, ".")
            If lTotHTAchat.Text.Length - x = 1 Then
                lTotHTAchat.Text = lTotHTAchat.Text + ("00")
            ElseIf lTotHTAchat.Text.Length - x = 2 Then
                lTotHTAchat.Text = lTotHTAchat.Text + ("0")
            End If
        Else
            lTotHTAchat.Text = lTotHTAchat.Text + ".000"
        End If
    End Sub

    Private Sub lTotalTVA_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotalTVA.TextChanged
        lTotalTVA.Text = lTotalTVA.Text
        If lTotalTVA.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lTotalTVA.Text, ".")
            If lTotalTVA.Text.Length - x = 1 Then
                lTotalTVA.Text = lTotalTVA.Text + ("00")
            ElseIf lTotalTVA.Text.Length - x = 2 Then
                lTotalTVA.Text = lTotalTVA.Text + ("0")
            End If
        Else
            lTotalTVA.Text = lTotalTVA.Text + ".000"
        End If
    End Sub

    Private Sub fCommande_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        With cmbType
            .HoldFields()
            .AddItem("Journalière")
            .AddItem("Groupée")
        End With
    End Sub

End Class