@echo off
echo ========================================
echo    LANCEMENT PHARMA2000 MODERNE
echo    Interface WPF avec Scanner Integre
echo ========================================
echo.

cd /d "%~dp0"

echo Verification de l'executable...
if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe" (
    echo ✅ Executable trouve : PharmaModerne.UI.exe
    echo.
    echo 🚀 Lancement de PHARMA2000 Moderne...
    echo.
    echo FONCTIONNALITES DISPONIBLES :
    echo ✅ Interface Material Design moderne
    echo ✅ Scanner de codes a barres integre
    echo ✅ Gestion complete des clients
    echo ✅ Recherche avancee et filtrage
    echo ✅ Navigation intuitive par modules
    echo ✅ Themes clair/sombre
    echo.
    echo L'application va s'ouvrir dans quelques secondes...
    echo.
    
    REM Lancer l'application
    start "" "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe"
    
    echo ✅ Application lancee avec succes !
    echo.
    echo UTILISATION DU SCANNER :
    echo 1. Cliquez sur le bouton Scanner dans la barre d'outils
    echo 2. Ou utilisez la recherche globale en haut
    echo 3. Scannez un code client ou article
    echo 4. L'application detecte automatiquement le scanner
    echo.
    echo MODULES DISPONIBLES :
    echo 👥 Clients - Gestion complete avec scanner
    echo 💊 Articles - Catalogue avec codes barres
    echo 🏪 Fournisseurs - Gestion des partenaires
    echo 💰 Ventes - Point de vente moderne
    echo 📊 Rapports - Analyses et statistiques
    echo ⚙️ Administration - Parametres et utilisateurs
    echo.
    
) else if exist "PharmaModerne.UI\bin\Release\net9.0-windows\PharmaModerne.UI.exe" (
    echo ✅ Executable Release trouve : PharmaModerne.UI.exe
    echo.
    echo 🚀 Lancement de PHARMA2000 Moderne (Release)...
    start "" "PharmaModerne.UI\bin\Release\net9.0-windows\PharmaModerne.UI.exe"
    echo ✅ Application lancee avec succes !
    
) else (
    echo ❌ Executable non trouve !
    echo.
    echo SOLUTION :
    echo 1. Executez d'abord COMPILER_PROJET_MODERNE.bat
    echo 2. Ou compilez manuellement avec Visual Studio
    echo 3. Verifiez que la compilation s'est bien deroulee
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo    SUPPORT ET AIDE
echo ========================================
echo.
echo 📚 AIDE RAPIDE :
echo - Menu lateral : Cliquez sur l'icone hamburger
echo - Scanner : Bouton radar dans la barre d'outils
echo - Recherche : Champ de recherche en haut
echo - Modules : Navigation par categories dans le menu
echo.
echo 🔧 EN CAS DE PROBLEME :
echo - Verifiez que .NET 9 est installe
echo - Recompilez le projet si necessaire
echo - Consultez les logs d'erreur
echo.
echo Appuyez sur une touche pour fermer...
pause >nul
