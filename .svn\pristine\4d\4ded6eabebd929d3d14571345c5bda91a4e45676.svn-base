﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="VentesReportsModel" Alias="Self" p1:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:p1="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
  <EntityContainer Name="VentesReportsEntities" p1:LazyLoadingEnabled="true">
    <EntitySet Name="PARAMETRE_PHARMACIE" EntityType="VentesReportsModel.PARAMETRE_PHARMACIE" />
    <EntitySet Name="V_Report_EtatDesFactures" EntityType="VentesReportsModel.V_Report_EtatDesFactures" />
    <EntitySet Name="V_Report_EtatDetailDesVentes" EntityType="VentesReportsModel.V_Report_EtatDetailDesVentes" />
    <EntitySet Name="V_Report_EtatDetailsCaisse" EntityType="VentesReportsModel.V_Report_EtatDetailsCaisse" />
    <EntitySet Name="V_Report_EtatJournalDesVentes" EntityType="VentesReportsModel.V_Report_EtatJournalDesVentes" />
    <EntitySet Name="V_Report_VentesAnnuelles" EntityType="VentesReportsModel.V_Report_VentesAnnuelles" />
    <EntitySet Name="V_Report_VentesMensuelles" EntityType="VentesReportsModel.V_Report_VentesMensuelles" />
    <EntitySet Name="V_Report_VentesQuotidiennes" EntityType="VentesReportsModel.V_Report_VentesQuotidiennes" />
    <FunctionImport Name="P_Report_EtatDesFactures" EntitySet="V_Report_EtatDesFactures" ReturnType="Collection(VentesReportsModel.V_Report_EtatDesFactures)">
      <Parameter Name="DateDebut" Mode="In" Type="String" />
      <Parameter Name="DateFin" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatDesVentes">
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="CodeClient" Mode="In" Type="String" />
      <Parameter Name="ModePaiement" Mode="In" Type="Int32" />
      <Parameter Name="TypeMutuelle" Mode="In" Type="String" />
      <Parameter Name="CodePersonnel" Mode="In" Type="Int32" />
      <Parameter Name="Poste" Mode="In" Type="Int32" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatDetailDesVentes" EntitySet="V_Report_EtatDetailDesVentes" ReturnType="Collection(VentesReportsModel.V_Report_EtatDetailDesVentes)">
      <Parameter Name="DateDebut" Mode="In" Type="String" />
      <Parameter Name="DateFin" Mode="In" Type="String" />
      <Parameter Name="CodeArticle" Mode="In" Type="String" />
      <Parameter Name="CodeCategorie" Mode="In" Type="Int32" />
      <Parameter Name="CodeClient" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatDetailsCaisse" EntitySet="V_Report_EtatDetailsCaisse" ReturnType="Collection(VentesReportsModel.V_Report_EtatDetailsCaisse)">
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="ModePaiement" Mode="In" Type="Int32" />
      <Parameter Name="Type" Mode="In" Type="String" />
      <Parameter Name="CodePersonnel" Mode="In" Type="Int32" />
      <Parameter Name="Poste" Mode="In" Type="Int32" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatJournalDesVentes" EntitySet="V_Report_EtatJournalDesVentes" ReturnType="Collection(VentesReportsModel.V_Report_EtatJournalDesVentes)">
      <Parameter Name="Exonorertva" Mode="In" Type="String" />
      <Parameter Name="DateDebut" Mode="In" Type="String" />
      <Parameter Name="DateFin" Mode="In" Type="String" />
      <Parameter Name="TenirCompteHonoraire" Mode="In" Type="Boolean" />
    </FunctionImport>
    <FunctionImport Name="P_Report_VentesAnnuelles" EntitySet="V_Report_VentesAnnuelles" ReturnType="Collection(VentesReportsModel.V_Report_VentesAnnuelles)" />
    <FunctionImport Name="P_Report_VentesMensuelles" EntitySet="V_Report_VentesMensuelles" ReturnType="Collection(VentesReportsModel.V_Report_VentesMensuelles)">
      <Parameter Name="Annee" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_Report_VentesQuotidienne" EntitySet="V_Report_VentesQuotidiennes" ReturnType="Collection(VentesReportsModel.V_Report_VentesQuotidiennes)">
      <Parameter Name="Mois" Mode="In" Type="Int32" />
      <Parameter Name="Annee" Mode="In" Type="String" />
    </FunctionImport>
  </EntityContainer>
  <EntityType Name="PARAMETRE_PHARMACIE">
    <Key>
      <PropertyRef Name="Code" />
    </Key>
    <Property Type="String" Name="Code" Nullable="false" MaxLength="10" FixedLength="true" Unicode="true" />
    <Property Type="String" Name="CodePharmacie" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Pharmacie" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NCnam" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Affiliation1" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Affiliation2" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Adresse" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Telephone" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Fax" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeTVA" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Rib" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Messagederoulant1" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Messagederoulant2" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Decimal" Name="Timbre" Precision="18" Scale="3" />
    <Property Type="Boolean" Name="DemandeMotDePasse" />
    <Property Type="DateTime" Name="DateMigration" Precision="0" />
    <Property Type="String" Name="SmtpMail" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="PortMail" MaxLength="50" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="AdresseMailDestinateur" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="SujetMail" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="TexteMail" MaxLength="550" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="MotDePasseDestinateur" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Boolean" Name="AutoriserEnvoiMail" Nullable="false" />
    <Property Type="Int32" Name="NbreJourValiditeParDefaut" />
    <Property Type="String" Name="NumeroLotProduction" Nullable="false" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Type="String" Name="Latitude_Longitude" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="TailleCodeCNAM" />
    <Property Type="Int32" Name="TailleListe" />
    <Property Type="Int32" Name="TailleCaractere" />
    <Property Type="String" Name="PoliceCaractere" MaxLength="50" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Texte" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int16" Name="TauxRemise" Nullable="false" />
    <Property Type="String" Name="CodeGSU" MaxLength="5" FixedLength="false" Unicode="false" />
    <Property Type="Binary" Name="ImageCodeABarre" MaxLength="Max" FixedLength="false" />
    <Property Type="Boolean" Name="ActiverOMFAPCI" Nullable="false" />
  </EntityType>
  <EntityType Name="V_Report_EtatDesFactures">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="String" Name="CodeClient" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Nom" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NumeroOperation" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="Date" Precision="0" />
    <Property Type="Decimal" Name="Honoraire" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalVenteHT" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Tva" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalVenteTTC" Precision="18" Scale="3" />
    <Property Type="String" Name="NumeroFacture" MaxLength="255" FixedLength="false" Unicode="false" />
  </EntityType>
  <EntityType Name="V_Report_EtatDetailDesVentes">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="String" Name="NumeroOperation" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="Date" Precision="0" />
    <Property Type="String" Name="CodeArticle" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeABarre" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Designation" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="LibelleForme" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="LibelleCategorie" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeClient" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Nom" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="Quantite" />
    <Property Type="Decimal" Name="Tva" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Remise" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalVenteTTC" Precision="18" Scale="3" />
    <Property Type="String" Name="NumeroFacture" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="CodeCategorie" />
    <Property Type="Int32" Name="CodeForme" />
  </EntityType>
  <EntityType Name="V_Report_EtatDetailsCaisse">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="Int32" Name="CodeNatureReglement" />
    <Property Type="String" Name="Type" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="Date" Precision="0" />
    <Property Type="String" Name="CodeClient" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Nom" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Decimal" Name="TotalVenteTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Credit" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Reglement" Precision="18" Scale="3" />
    <Property Type="DateTime" Name="DateEcheance" Precision="0" />
    <Property Type="String" Name="CodePersonnel" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Vendeur" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Poste" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NumeroOperation" MaxLength="255" FixedLength="false" Unicode="false" />
  </EntityType>
  <EntityType Name="V_Report_EtatJournalDesVentes">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="DateTime" Name="Date" Precision="0" />
    <Property Type="Decimal" Name="Exonore" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="BaseTVA6" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA6" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="BaseTVA12" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA12" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="BaseTVA18" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA18" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalHT" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTVA" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="HR" Precision="18" Scale="3" />
    <Property Type="String" Name="NumeroFacture" MaxLength="255" FixedLength="false" Unicode="false" />
  </EntityType>
  <EntityType Name="V_Report_VentesAnnuelles">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="Int32" Name="Annee" />
    <Property Type="Decimal" Name="Comptant" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Credit" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Mutuelle" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Cnam" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Remise" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Caisse" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Precision="18" Scale="3" />
  </EntityType>
  <EntityType Name="V_Report_VentesMensuelles">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="Int32" Name="NumeroMois" />
    <Property Type="String" Name="NomMois" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="Decimal" Name="Comptant" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Credit" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Mutuelle" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Cnam" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Remise" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Caisse" Precision="18" Scale="3" />
    <Property Type="Int32" Name="NumMois" />
    <Property Type="Int32" Name="NumYear" />
  </EntityType>
  <EntityType Name="V_Report_VentesQuotidiennes">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="Int32" Name="NumeroJour" />
    <Property Type="Decimal" Name="Comptant" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Mutuelle" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Cnam" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Reglement" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Caisse" Precision="18" Scale="3" />
    <Property Type="Int32" Name="NumJour" />
    <Property Type="Int32" Name="NumMois" />
    <Property Type="Int32" Name="NumYear" />
  </EntityType>
</Schema>