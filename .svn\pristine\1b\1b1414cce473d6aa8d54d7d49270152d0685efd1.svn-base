﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fFichePharmacie
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fFichePharmacie))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.Tab = New C1.Win.C1Command.C1DockingTab()
        Me.C1DockingTabPage1 = New C1.Win.C1Command.C1DockingTabPage()
        Me.dtpDate = New C1.Win.C1Input.C1DateEdit()
        Me.GroupBox7 = New System.Windows.Forms.GroupBox()
        Me.lDateInitiale = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.lSoldeInitial = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.lSolde = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.bViderMouvements = New C1.Win.C1Input.C1Button()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.LRemarque = New System.Windows.Forms.Label()
        Me.tRemarquePharmacie = New C1.Win.C1Input.C1TextBox()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.cmbVillePharmacie = New C1.Win.C1List.C1Combo()
        Me.tCodePostalPharmacie = New C1.Win.C1Input.C1TextBox()
        Me.tTelephonePharmacie = New C1.Win.C1Input.C1TextBox()
        Me.tFaxPharmacie = New C1.Win.C1Input.C1TextBox()
        Me.LRuePharmacie = New System.Windows.Forms.Label()
        Me.LCodePostal = New System.Windows.Forms.Label()
        Me.LFaxPharmacie = New System.Windows.Forms.Label()
        Me.tRuePharmacie = New C1.Win.C1Input.C1TextBox()
        Me.LTelephonePharmacie = New System.Windows.Forms.Label()
        Me.LVillePharmacie = New System.Windows.Forms.Label()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.lTest = New System.Windows.Forms.Label()
        Me.tNomPharmacie = New C1.Win.C1Input.C1TextBox()
        Me.tCodePharmacie = New C1.Win.C1Input.C1TextBox()
        Me.LNomPharmacie = New System.Windows.Forms.Label()
        Me.LCodePharmacie = New System.Windows.Forms.Label()
        Me.bConfirmer = New C1.Win.C1Input.C1Button()
        Me.C1DockingTabPage2 = New C1.Win.C1Command.C1DockingTabPage()
        Me.GroupBox17 = New System.Windows.Forms.GroupBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.lTotalPrets = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.lTotalEmprunts = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.gDetailsEmprunts = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.gEmprunts = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Label24 = New System.Windows.Forms.Label()
        Me.Label23 = New System.Windows.Forms.Label()
        Me.gDetailsPrets = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.gPrets = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.C1DockingTabPage3 = New C1.Win.C1Command.C1DockingTabPage()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.gReglements = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bModifierReglement = New C1.Win.C1Input.C1Button()
        Me.bSupprimerReglement = New C1.Win.C1Input.C1Button()
        Me.bAjouterReglement = New C1.Win.C1Input.C1Button()
        Me.DataSet_EtatInventaireTemporaire1 = New Pharma2000Premium.DataSet_EtatInventaireTemporaire()
        Me.Panel.SuspendLayout()
        CType(Me.Tab, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Tab.SuspendLayout()
        Me.C1DockingTabPage1.SuspendLayout()
        CType(Me.dtpDate, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox7.SuspendLayout()
        Me.GroupBox4.SuspendLayout()
        CType(Me.tRemarquePharmacie, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox3.SuspendLayout()
        CType(Me.cmbVillePharmacie, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodePostalPharmacie, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tTelephonePharmacie, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tFaxPharmacie, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tRuePharmacie, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox2.SuspendLayout()
        CType(Me.tNomPharmacie, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodePharmacie, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage2.SuspendLayout()
        Me.GroupBox17.SuspendLayout()
        CType(Me.gDetailsEmprunts, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gEmprunts, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gDetailsPrets, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gPrets, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage3.SuspendLayout()
        CType(Me.gReglements, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DataSet_EtatInventaireTemporaire1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Panel.Controls.Add(Me.Tab)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1028, 566)
        Me.Panel.TabIndex = 1
        '
        'Tab
        '
        Me.Tab.Controls.Add(Me.C1DockingTabPage1)
        Me.Tab.Controls.Add(Me.C1DockingTabPage2)
        Me.Tab.Controls.Add(Me.C1DockingTabPage3)
        Me.Tab.Location = New System.Drawing.Point(3, 10)
        Me.Tab.Name = "Tab"
        Me.Tab.SelectedIndex = 2
        Me.Tab.Size = New System.Drawing.Size(954, 554)
        Me.Tab.TabIndex = 4
        Me.Tab.TabsSpacing = 5
        Me.Tab.TabStyle = C1.Win.C1Command.TabStyleEnum.Office2007
        Me.Tab.VisualStyle = C1.Win.C1Command.VisualStyle.Office2007Blue
        Me.Tab.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage1
        '
        Me.C1DockingTabPage1.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.C1DockingTabPage1.Controls.Add(Me.dtpDate)
        Me.C1DockingTabPage1.Controls.Add(Me.GroupBox7)
        Me.C1DockingTabPage1.Controls.Add(Me.bViderMouvements)
        Me.C1DockingTabPage1.Controls.Add(Me.GroupBox4)
        Me.C1DockingTabPage1.Controls.Add(Me.GroupBox3)
        Me.C1DockingTabPage1.Controls.Add(Me.bAnnuler)
        Me.C1DockingTabPage1.Controls.Add(Me.GroupBox2)
        Me.C1DockingTabPage1.Controls.Add(Me.bConfirmer)
        Me.C1DockingTabPage1.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage1.Name = "C1DockingTabPage1"
        Me.C1DockingTabPage1.Size = New System.Drawing.Size(952, 529)
        Me.C1DockingTabPage1.TabIndex = 0
        Me.C1DockingTabPage1.Text = "Information de la Pharmacie"
        '
        'dtpDate
        '
        Me.dtpDate.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.dtpDate.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDate.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDate.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDate.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDate.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDate.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.dtpDate.Location = New System.Drawing.Point(249, 281)
        Me.dtpDate.Name = "dtpDate"
        Me.dtpDate.Size = New System.Drawing.Size(121, 19)
        Me.dtpDate.TabIndex = 54
        Me.dtpDate.Tag = Nothing
        Me.dtpDate.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDate.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox7
        '
        Me.GroupBox7.Controls.Add(Me.lDateInitiale)
        Me.GroupBox7.Controls.Add(Me.Label8)
        Me.GroupBox7.Controls.Add(Me.lSoldeInitial)
        Me.GroupBox7.Controls.Add(Me.Label7)
        Me.GroupBox7.Controls.Add(Me.lSolde)
        Me.GroupBox7.Controls.Add(Me.Label6)
        Me.GroupBox7.Location = New System.Drawing.Point(376, 229)
        Me.GroupBox7.Name = "GroupBox7"
        Me.GroupBox7.Size = New System.Drawing.Size(352, 71)
        Me.GroupBox7.TabIndex = 37
        Me.GroupBox7.TabStop = False
        '
        'lDateInitiale
        '
        Me.lDateInitiale.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDateInitiale.Location = New System.Drawing.Point(251, 16)
        Me.lDateInitiale.Name = "lDateInitiale"
        Me.lDateInitiale.Size = New System.Drawing.Size(95, 13)
        Me.lDateInitiale.TabIndex = 40
        Me.lDateInitiale.Text = "DateInit"
        Me.lDateInitiale.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Location = New System.Drawing.Point(188, 16)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(69, 13)
        Me.Label8.TabIndex = 39
        Me.Label8.Text = "Date Initiale :"
        '
        'lSoldeInitial
        '
        Me.lSoldeInitial.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lSoldeInitial.Location = New System.Drawing.Point(69, 16)
        Me.lSoldeInitial.Name = "lSoldeInitial"
        Me.lSoldeInitial.Size = New System.Drawing.Size(95, 13)
        Me.lSoldeInitial.TabIndex = 38
        Me.lSoldeInitial.Text = "SoldeInit"
        Me.lSoldeInitial.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Location = New System.Drawing.Point(6, 16)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(63, 13)
        Me.Label7.TabIndex = 37
        Me.Label7.Text = "Solde initial:"
        '
        'lSolde
        '
        Me.lSolde.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lSolde.Location = New System.Drawing.Point(52, 43)
        Me.lSolde.Name = "lSolde"
        Me.lSolde.Size = New System.Drawing.Size(112, 25)
        Me.lSolde.TabIndex = 36
        Me.lSolde.Text = "Solde"
        Me.lSolde.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Location = New System.Drawing.Point(6, 50)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(40, 13)
        Me.Label6.TabIndex = 33
        Me.Label6.Text = "Solde :"
        '
        'bViderMouvements
        '
        Me.bViderMouvements.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bViderMouvements.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bViderMouvements.Location = New System.Drawing.Point(18, 277)
        Me.bViderMouvements.Name = "bViderMouvements"
        Me.bViderMouvements.Size = New System.Drawing.Size(225, 28)
        Me.bViderMouvements.TabIndex = 53
        Me.bViderMouvements.Text = "Vider les mouvements avant le :"
        Me.bViderMouvements.UseVisualStyleBackColor = True
        Me.bViderMouvements.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.LRemarque)
        Me.GroupBox4.Controls.Add(Me.tRemarquePharmacie)
        Me.GroupBox4.Location = New System.Drawing.Point(376, 52)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(355, 170)
        Me.GroupBox4.TabIndex = 16
        Me.GroupBox4.TabStop = False
        '
        'LRemarque
        '
        Me.LRemarque.AutoSize = True
        Me.LRemarque.Location = New System.Drawing.Point(19, 18)
        Me.LRemarque.Name = "LRemarque"
        Me.LRemarque.Size = New System.Drawing.Size(56, 13)
        Me.LRemarque.TabIndex = 35
        Me.LRemarque.Text = "Remarque"
        '
        'tRemarquePharmacie
        '
        Me.tRemarquePharmacie.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tRemarquePharmacie.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tRemarquePharmacie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tRemarquePharmacie.Location = New System.Drawing.Point(81, 16)
        Me.tRemarquePharmacie.Multiline = True
        Me.tRemarquePharmacie.Name = "tRemarquePharmacie"
        Me.tRemarquePharmacie.Size = New System.Drawing.Size(265, 140)
        Me.tRemarquePharmacie.TabIndex = 0
        Me.tRemarquePharmacie.Tag = Nothing
        Me.tRemarquePharmacie.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tRemarquePharmacie.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.cmbVillePharmacie)
        Me.GroupBox3.Controls.Add(Me.tCodePostalPharmacie)
        Me.GroupBox3.Controls.Add(Me.tTelephonePharmacie)
        Me.GroupBox3.Controls.Add(Me.tFaxPharmacie)
        Me.GroupBox3.Controls.Add(Me.LRuePharmacie)
        Me.GroupBox3.Controls.Add(Me.LCodePostal)
        Me.GroupBox3.Controls.Add(Me.LFaxPharmacie)
        Me.GroupBox3.Controls.Add(Me.tRuePharmacie)
        Me.GroupBox3.Controls.Add(Me.LTelephonePharmacie)
        Me.GroupBox3.Controls.Add(Me.LVillePharmacie)
        Me.GroupBox3.Location = New System.Drawing.Point(18, 134)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(352, 132)
        Me.GroupBox3.TabIndex = 12
        Me.GroupBox3.TabStop = False
        '
        'cmbVillePharmacie
        '
        Me.cmbVillePharmacie.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbVillePharmacie.Caption = ""
        Me.cmbVillePharmacie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbVillePharmacie.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbVillePharmacie.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbVillePharmacie.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbVillePharmacie.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbVillePharmacie.Images.Add(CType(resources.GetObject("cmbVillePharmacie.Images"), System.Drawing.Image))
        Me.cmbVillePharmacie.Location = New System.Drawing.Point(138, 34)
        Me.cmbVillePharmacie.MatchEntryTimeout = CType(2000, Long)
        Me.cmbVillePharmacie.MaxDropDownItems = CType(5, Short)
        Me.cmbVillePharmacie.MaxLength = 32767
        Me.cmbVillePharmacie.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbVillePharmacie.Name = "cmbVillePharmacie"
        Me.cmbVillePharmacie.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbVillePharmacie.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbVillePharmacie.Size = New System.Drawing.Size(154, 21)
        Me.cmbVillePharmacie.TabIndex = 1
        Me.cmbVillePharmacie.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbVillePharmacie.PropBag = resources.GetString("cmbVillePharmacie.PropBag")
        '
        'tCodePostalPharmacie
        '
        Me.tCodePostalPharmacie.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodePostalPharmacie.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodePostalPharmacie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tCodePostalPharmacie.EditMask = "0000"
        Me.tCodePostalPharmacie.Location = New System.Drawing.Point(138, 102)
        Me.tCodePostalPharmacie.Name = "tCodePostalPharmacie"
        Me.tCodePostalPharmacie.Size = New System.Drawing.Size(100, 18)
        Me.tCodePostalPharmacie.TabIndex = 4
        Me.tCodePostalPharmacie.Tag = Nothing
        Me.tCodePostalPharmacie.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodePostalPharmacie.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tTelephonePharmacie
        '
        Me.tTelephonePharmacie.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tTelephonePharmacie.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTelephonePharmacie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tTelephonePharmacie.Location = New System.Drawing.Point(138, 59)
        Me.tTelephonePharmacie.Name = "tTelephonePharmacie"
        Me.tTelephonePharmacie.Size = New System.Drawing.Size(154, 18)
        Me.tTelephonePharmacie.TabIndex = 2
        Me.tTelephonePharmacie.Tag = Nothing
        Me.tTelephonePharmacie.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tTelephonePharmacie.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tFaxPharmacie
        '
        Me.tFaxPharmacie.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tFaxPharmacie.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tFaxPharmacie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tFaxPharmacie.Location = New System.Drawing.Point(138, 80)
        Me.tFaxPharmacie.Name = "tFaxPharmacie"
        Me.tFaxPharmacie.Size = New System.Drawing.Size(100, 18)
        Me.tFaxPharmacie.TabIndex = 3
        Me.tFaxPharmacie.Tag = Nothing
        Me.tFaxPharmacie.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tFaxPharmacie.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'LRuePharmacie
        '
        Me.LRuePharmacie.AutoSize = True
        Me.LRuePharmacie.Location = New System.Drawing.Point(97, 15)
        Me.LRuePharmacie.Name = "LRuePharmacie"
        Me.LRuePharmacie.Size = New System.Drawing.Size(27, 13)
        Me.LRuePharmacie.TabIndex = 2
        Me.LRuePharmacie.Text = "Rue"
        '
        'LCodePostal
        '
        Me.LCodePostal.AutoSize = True
        Me.LCodePostal.Location = New System.Drawing.Point(60, 104)
        Me.LCodePostal.Name = "LCodePostal"
        Me.LCodePostal.Size = New System.Drawing.Size(64, 13)
        Me.LCodePostal.TabIndex = 28
        Me.LCodePostal.Text = "Code Postal"
        '
        'LFaxPharmacie
        '
        Me.LFaxPharmacie.AutoSize = True
        Me.LFaxPharmacie.Location = New System.Drawing.Point(100, 83)
        Me.LFaxPharmacie.Name = "LFaxPharmacie"
        Me.LFaxPharmacie.Size = New System.Drawing.Size(24, 13)
        Me.LFaxPharmacie.TabIndex = 1
        Me.LFaxPharmacie.Text = "Fax"
        '
        'tRuePharmacie
        '
        Me.tRuePharmacie.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tRuePharmacie.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tRuePharmacie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tRuePharmacie.Location = New System.Drawing.Point(138, 13)
        Me.tRuePharmacie.Name = "tRuePharmacie"
        Me.tRuePharmacie.Size = New System.Drawing.Size(154, 18)
        Me.tRuePharmacie.TabIndex = 0
        Me.tRuePharmacie.Tag = Nothing
        Me.tRuePharmacie.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tRuePharmacie.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'LTelephonePharmacie
        '
        Me.LTelephonePharmacie.AutoSize = True
        Me.LTelephonePharmacie.Location = New System.Drawing.Point(66, 61)
        Me.LTelephonePharmacie.Name = "LTelephonePharmacie"
        Me.LTelephonePharmacie.Size = New System.Drawing.Size(58, 13)
        Me.LTelephonePharmacie.TabIndex = 4
        Me.LTelephonePharmacie.Text = "Téléphone"
        '
        'LVillePharmacie
        '
        Me.LVillePharmacie.AutoSize = True
        Me.LVillePharmacie.Location = New System.Drawing.Point(98, 38)
        Me.LVillePharmacie.Name = "LVillePharmacie"
        Me.LVillePharmacie.Size = New System.Drawing.Size(26, 13)
        Me.LVillePharmacie.TabIndex = 3
        Me.LVillePharmacie.Text = "Ville"
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler21
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(840, 67)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(109, 45)
        Me.bAnnuler.TabIndex = 3
        Me.bAnnuler.Text = "Annuler              F10"
        Me.bAnnuler.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.lTest)
        Me.GroupBox2.Controls.Add(Me.tNomPharmacie)
        Me.GroupBox2.Controls.Add(Me.tCodePharmacie)
        Me.GroupBox2.Controls.Add(Me.LNomPharmacie)
        Me.GroupBox2.Controls.Add(Me.LCodePharmacie)
        Me.GroupBox2.Location = New System.Drawing.Point(18, 52)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(352, 67)
        Me.GroupBox2.TabIndex = 11
        Me.GroupBox2.TabStop = False
        '
        'lTest
        '
        Me.lTest.AutoSize = True
        Me.lTest.BackColor = System.Drawing.Color.Transparent
        Me.lTest.Location = New System.Drawing.Point(245, 18)
        Me.lTest.Name = "lTest"
        Me.lTest.Size = New System.Drawing.Size(19, 13)
        Me.lTest.TabIndex = 2
        Me.lTest.Text = "55" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        Me.lTest.Visible = False
        '
        'tNomPharmacie
        '
        Me.tNomPharmacie.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNomPharmacie.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNomPharmacie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNomPharmacie.Location = New System.Drawing.Point(138, 38)
        Me.tNomPharmacie.Name = "tNomPharmacie"
        Me.tNomPharmacie.Size = New System.Drawing.Size(154, 18)
        Me.tNomPharmacie.TabIndex = 1
        Me.tNomPharmacie.Tag = Nothing
        Me.tNomPharmacie.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNomPharmacie.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tCodePharmacie
        '
        Me.tCodePharmacie.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodePharmacie.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodePharmacie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tCodePharmacie.Location = New System.Drawing.Point(138, 15)
        Me.tCodePharmacie.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tCodePharmacie.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tCodePharmacie.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tCodePharmacie.Name = "tCodePharmacie"
        Me.tCodePharmacie.Size = New System.Drawing.Size(100, 18)
        Me.tCodePharmacie.TabIndex = 0
        Me.tCodePharmacie.Tag = Nothing
        Me.tCodePharmacie.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodePharmacie.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'LNomPharmacie
        '
        Me.LNomPharmacie.AutoSize = True
        Me.LNomPharmacie.Location = New System.Drawing.Point(97, 40)
        Me.LNomPharmacie.Name = "LNomPharmacie"
        Me.LNomPharmacie.Size = New System.Drawing.Size(29, 13)
        Me.LNomPharmacie.TabIndex = 1
        Me.LNomPharmacie.Text = "Nom"
        '
        'LCodePharmacie
        '
        Me.LCodePharmacie.AutoSize = True
        Me.LCodePharmacie.Location = New System.Drawing.Point(97, 17)
        Me.LCodePharmacie.Name = "LCodePharmacie"
        Me.LCodePharmacie.Size = New System.Drawing.Size(35, 13)
        Me.LCodePharmacie.TabIndex = 0
        Me.LCodePharmacie.Text = "Code "
        '
        'bConfirmer
        '
        Me.bConfirmer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bConfirmer.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmer.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bConfirmer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmer.Location = New System.Drawing.Point(840, 16)
        Me.bConfirmer.Name = "bConfirmer"
        Me.bConfirmer.Size = New System.Drawing.Size(109, 45)
        Me.bConfirmer.TabIndex = 2
        Me.bConfirmer.Text = "Confirmer            F3"
        Me.bConfirmer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bConfirmer.UseVisualStyleBackColor = True
        Me.bConfirmer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage2
        '
        Me.C1DockingTabPage2.Controls.Add(Me.GroupBox17)
        Me.C1DockingTabPage2.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage2.Name = "C1DockingTabPage2"
        Me.C1DockingTabPage2.Size = New System.Drawing.Size(952, 529)
        Me.C1DockingTabPage2.TabIndex = 1
        Me.C1DockingTabPage2.Text = "Fiche pharmacien"
        '
        'GroupBox17
        '
        Me.GroupBox17.Controls.Add(Me.Label4)
        Me.GroupBox17.Controls.Add(Me.lTotalPrets)
        Me.GroupBox17.Controls.Add(Me.Label3)
        Me.GroupBox17.Controls.Add(Me.Label1)
        Me.GroupBox17.Controls.Add(Me.lTotalEmprunts)
        Me.GroupBox17.Controls.Add(Me.Label2)
        Me.GroupBox17.Controls.Add(Me.gDetailsEmprunts)
        Me.GroupBox17.Controls.Add(Me.gEmprunts)
        Me.GroupBox17.Controls.Add(Me.Label24)
        Me.GroupBox17.Controls.Add(Me.Label23)
        Me.GroupBox17.Controls.Add(Me.gDetailsPrets)
        Me.GroupBox17.Controls.Add(Me.gPrets)
        Me.GroupBox17.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox17.Location = New System.Drawing.Point(8, 2)
        Me.GroupBox17.Name = "GroupBox17"
        Me.GroupBox17.Size = New System.Drawing.Size(938, 464)
        Me.GroupBox17.TabIndex = 13
        Me.GroupBox17.TabStop = False
        '
        'Label4
        '
        Me.Label4.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.Location = New System.Drawing.Point(237, 420)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(77, 13)
        Me.Label4.TabIndex = 76
        Me.Label4.Text = "Total des prêts"
        '
        'lTotalPrets
        '
        Me.lTotalPrets.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lTotalPrets.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lTotalPrets.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotalPrets.ForeColor = System.Drawing.Color.Black
        Me.lTotalPrets.Location = New System.Drawing.Point(320, 417)
        Me.lTotalPrets.Name = "lTotalPrets"
        Me.lTotalPrets.Size = New System.Drawing.Size(93, 18)
        Me.lTotalPrets.TabIndex = 75
        Me.lTotalPrets.Text = "999999.999"
        Me.lTotalPrets.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label3
        '
        Me.Label3.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.Location = New System.Drawing.Point(736, 420)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(97, 13)
        Me.Label3.TabIndex = 52
        Me.Label3.Text = "Total des emprûnts"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(519, 209)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(86, 13)
        Me.Label1.TabIndex = 74
        Me.Label1.Text = "Détails Emprûnts"
        '
        'lTotalEmprunts
        '
        Me.lTotalEmprunts.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lTotalEmprunts.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lTotalEmprunts.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotalEmprunts.ForeColor = System.Drawing.Color.Black
        Me.lTotalEmprunts.Location = New System.Drawing.Point(839, 417)
        Me.lTotalEmprunts.Name = "lTotalEmprunts"
        Me.lTotalEmprunts.Size = New System.Drawing.Size(93, 18)
        Me.lTotalEmprunts.TabIndex = 51
        Me.lTotalEmprunts.Text = "999999.999"
        Me.lTotalEmprunts.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(519, 10)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(98, 13)
        Me.Label2.TabIndex = 71
        Me.Label2.Text = "Entrées / Emprûnts"
        '
        'gDetailsEmprunts
        '
        Me.gDetailsEmprunts.AlternatingRows = True
        Me.gDetailsEmprunts.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.gDetailsEmprunts.FetchRowStyles = True
        Me.gDetailsEmprunts.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gDetailsEmprunts.GroupByCaption = "Drag a column header here to group by that column"
        Me.gDetailsEmprunts.Images.Add(CType(resources.GetObject("gDetailsEmprunts.Images"), System.Drawing.Image))
        Me.gDetailsEmprunts.LinesPerRow = 2
        Me.gDetailsEmprunts.Location = New System.Drawing.Point(521, 225)
        Me.gDetailsEmprunts.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gDetailsEmprunts.Name = "gDetailsEmprunts"
        Me.gDetailsEmprunts.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gDetailsEmprunts.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gDetailsEmprunts.PreviewInfo.ZoomFactor = 75.0R
        Me.gDetailsEmprunts.PrintInfo.PageSettings = CType(resources.GetObject("gDetailsEmprunts.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gDetailsEmprunts.Size = New System.Drawing.Size(411, 179)
        Me.gDetailsEmprunts.TabIndex = 73
        Me.gDetailsEmprunts.Text = "C1TrueDBGrid1"
        Me.gDetailsEmprunts.PropBag = resources.GetString("gDetailsEmprunts.PropBag")
        '
        'gEmprunts
        '
        Me.gEmprunts.AlternatingRows = True
        Me.gEmprunts.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.gEmprunts.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gEmprunts.GroupByCaption = "Drag a column header here to group by that column"
        Me.gEmprunts.Images.Add(CType(resources.GetObject("gEmprunts.Images"), System.Drawing.Image))
        Me.gEmprunts.LinesPerRow = 2
        Me.gEmprunts.Location = New System.Drawing.Point(521, 27)
        Me.gEmprunts.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gEmprunts.Name = "gEmprunts"
        Me.gEmprunts.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gEmprunts.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gEmprunts.PreviewInfo.ZoomFactor = 75.0R
        Me.gEmprunts.PrintInfo.PageSettings = CType(resources.GetObject("gEmprunts.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gEmprunts.Size = New System.Drawing.Size(411, 179)
        Me.gEmprunts.TabIndex = 72
        Me.gEmprunts.Text = "C1TrueDBGrid1"
        Me.gEmprunts.PropBag = resources.GetString("gEmprunts.PropBag")
        '
        'Label24
        '
        Me.Label24.AutoSize = True
        Me.Label24.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label24.Location = New System.Drawing.Point(6, 209)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(66, 13)
        Me.Label24.TabIndex = 70
        Me.Label24.Text = "Détails Prêts"
        '
        'Label23
        '
        Me.Label23.AutoSize = True
        Me.Label23.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label23.Location = New System.Drawing.Point(6, 10)
        Me.Label23.Name = "Label23"
        Me.Label23.Size = New System.Drawing.Size(74, 13)
        Me.Label23.TabIndex = 14
        Me.Label23.Text = "Sorties / Prêts"
        '
        'gDetailsPrets
        '
        Me.gDetailsPrets.AllowUpdate = False
        Me.gDetailsPrets.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.gDetailsPrets.FetchRowStyles = True
        Me.gDetailsPrets.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gDetailsPrets.GroupByCaption = "Drag a column header here to group by that column"
        Me.gDetailsPrets.Images.Add(CType(resources.GetObject("gDetailsPrets.Images"), System.Drawing.Image))
        Me.gDetailsPrets.LinesPerRow = 2
        Me.gDetailsPrets.Location = New System.Drawing.Point(9, 225)
        Me.gDetailsPrets.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gDetailsPrets.Name = "gDetailsPrets"
        Me.gDetailsPrets.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gDetailsPrets.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gDetailsPrets.PreviewInfo.ZoomFactor = 75.0R
        Me.gDetailsPrets.PrintInfo.PageSettings = CType(resources.GetObject("gDetailsPrets.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gDetailsPrets.Size = New System.Drawing.Size(404, 179)
        Me.gDetailsPrets.TabIndex = 69
        Me.gDetailsPrets.Text = "C1TrueDBGrid1"
        Me.gDetailsPrets.PropBag = resources.GetString("gDetailsPrets.PropBag")
        '
        'gPrets
        '
        Me.gPrets.AllowUpdate = False
        Me.gPrets.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.gPrets.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gPrets.GroupByCaption = "Drag a column header here to group by that column"
        Me.gPrets.Images.Add(CType(resources.GetObject("gPrets.Images"), System.Drawing.Image))
        Me.gPrets.LinesPerRow = 2
        Me.gPrets.Location = New System.Drawing.Point(9, 27)
        Me.gPrets.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gPrets.Name = "gPrets"
        Me.gPrets.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gPrets.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gPrets.PreviewInfo.ZoomFactor = 75.0R
        Me.gPrets.PrintInfo.PageSettings = CType(resources.GetObject("gPrets.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gPrets.Size = New System.Drawing.Size(404, 179)
        Me.gPrets.TabIndex = 50
        Me.gPrets.Text = "C1TrueDBGrid1"
        Me.gPrets.PropBag = resources.GetString("gPrets.PropBag")
        '
        'C1DockingTabPage3
        '
        Me.C1DockingTabPage3.Controls.Add(Me.Label5)
        Me.C1DockingTabPage3.Controls.Add(Me.gReglements)
        Me.C1DockingTabPage3.Controls.Add(Me.bModifierReglement)
        Me.C1DockingTabPage3.Controls.Add(Me.bSupprimerReglement)
        Me.C1DockingTabPage3.Controls.Add(Me.bAjouterReglement)
        Me.C1DockingTabPage3.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage3.Name = "C1DockingTabPage3"
        Me.C1DockingTabPage3.Size = New System.Drawing.Size(952, 529)
        Me.C1DockingTabPage3.TabIndex = 2
        Me.C1DockingTabPage3.Text = "Réglement"
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.Location = New System.Drawing.Point(7, 12)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(63, 13)
        Me.Label5.TabIndex = 75
        Me.Label5.Text = "Règlements"
        '
        'gReglements
        '
        Me.gReglements.AlternatingRows = True
        Me.gReglements.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.gReglements.FetchRowStyles = True
        Me.gReglements.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gReglements.GroupByCaption = "Drag a column header here to group by that column"
        Me.gReglements.Images.Add(CType(resources.GetObject("gReglements.Images"), System.Drawing.Image))
        Me.gReglements.LinesPerRow = 2
        Me.gReglements.Location = New System.Drawing.Point(10, 28)
        Me.gReglements.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gReglements.Name = "gReglements"
        Me.gReglements.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gReglements.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gReglements.PreviewInfo.ZoomFactor = 75.0R
        Me.gReglements.PrintInfo.PageSettings = CType(resources.GetObject("gReglements.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gReglements.Size = New System.Drawing.Size(939, 441)
        Me.gReglements.TabIndex = 74
        Me.gReglements.Text = "C1TrueDBGrid1"
        Me.gReglements.PropBag = resources.GetString("gReglements.PropBag")
        '
        'bModifierReglement
        '
        Me.bModifierReglement.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.bModifierReglement.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bModifierReglement.Image = Global.Pharma2000Premium.My.Resources.Resources.voir_modifierfpharmacien
        Me.bModifierReglement.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bModifierReglement.Location = New System.Drawing.Point(847, 475)
        Me.bModifierReglement.Name = "bModifierReglement"
        Me.bModifierReglement.Size = New System.Drawing.Size(102, 45)
        Me.bModifierReglement.TabIndex = 73
        Me.bModifierReglement.Text = "Modifier            F8"
        Me.bModifierReglement.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bModifierReglement.UseVisualStyleBackColor = True
        Me.bModifierReglement.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bSupprimerReglement
        '
        Me.bSupprimerReglement.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.bSupprimerReglement.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimerReglement.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler2
        Me.bSupprimerReglement.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSupprimerReglement.Location = New System.Drawing.Point(742, 475)
        Me.bSupprimerReglement.Name = "bSupprimerReglement"
        Me.bSupprimerReglement.Size = New System.Drawing.Size(102, 45)
        Me.bSupprimerReglement.TabIndex = 72
        Me.bSupprimerReglement.Text = "Supprimer         F7"
        Me.bSupprimerReglement.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bSupprimerReglement.UseVisualStyleBackColor = True
        Me.bSupprimerReglement.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouterReglement
        '
        Me.bAjouterReglement.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.bAjouterReglement.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouterReglement.Image = Global.Pharma2000Premium.My.Resources.Resources.nouveau
        Me.bAjouterReglement.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouterReglement.Location = New System.Drawing.Point(636, 475)
        Me.bAjouterReglement.Name = "bAjouterReglement"
        Me.bAjouterReglement.Size = New System.Drawing.Size(102, 45)
        Me.bAjouterReglement.TabIndex = 71
        Me.bAjouterReglement.Text = "Ajouter                 F5"
        Me.bAjouterReglement.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bAjouterReglement.UseVisualStyleBackColor = True
        Me.bAjouterReglement.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'DataSet_EtatInventaireTemporaire1
        '
        Me.DataSet_EtatInventaireTemporaire1.DataSetName = "DataSet_EtatInventaireTemporaire"
        Me.DataSet_EtatInventaireTemporaire1.SchemaSerializationMode = System.Data.SchemaSerializationMode.IncludeSchema
        '
        'fFichePharmacie
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1028, 566)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fFichePharmacie"
        Me.Text = "fFichePharmacie"
        Me.Panel.ResumeLayout(False)
        CType(Me.Tab, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Tab.ResumeLayout(False)
        Me.C1DockingTabPage1.ResumeLayout(False)
        CType(Me.dtpDate, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox7.ResumeLayout(False)
        Me.GroupBox7.PerformLayout()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.tRemarquePharmacie, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        CType(Me.cmbVillePharmacie, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodePostalPharmacie, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tTelephonePharmacie, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tFaxPharmacie, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tRuePharmacie, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.tNomPharmacie, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodePharmacie, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage2.ResumeLayout(False)
        Me.GroupBox17.ResumeLayout(False)
        Me.GroupBox17.PerformLayout()
        CType(Me.gDetailsEmprunts, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gEmprunts, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gDetailsPrets, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gPrets, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage3.ResumeLayout(False)
        Me.C1DockingTabPage3.PerformLayout()
        CType(Me.gReglements, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DataSet_EtatInventaireTemporaire1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bConfirmer As C1.Win.C1Input.C1Button
    Friend WithEvents Tab As C1.Win.C1Command.C1DockingTab
    Friend WithEvents C1DockingTabPage1 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents C1DockingTabPage2 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents GroupBox17 As System.Windows.Forms.GroupBox
    Friend WithEvents Label24 As System.Windows.Forms.Label
    Friend WithEvents Label23 As System.Windows.Forms.Label
    Friend WithEvents gDetailsPrets As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents gPrets As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents GroupBox7 As System.Windows.Forms.GroupBox
    Friend WithEvents lSolde As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents LRemarque As System.Windows.Forms.Label
    Friend WithEvents tRemarquePharmacie As C1.Win.C1Input.C1TextBox
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents cmbVillePharmacie As C1.Win.C1List.C1Combo
    Friend WithEvents tCodePostalPharmacie As C1.Win.C1Input.C1TextBox
    Friend WithEvents tTelephonePharmacie As C1.Win.C1Input.C1TextBox
    Friend WithEvents tFaxPharmacie As C1.Win.C1Input.C1TextBox
    Friend WithEvents LRuePharmacie As System.Windows.Forms.Label
    Friend WithEvents LCodePostal As System.Windows.Forms.Label
    Friend WithEvents LFaxPharmacie As System.Windows.Forms.Label
    Friend WithEvents tRuePharmacie As C1.Win.C1Input.C1TextBox
    Friend WithEvents LTelephonePharmacie As System.Windows.Forms.Label
    Friend WithEvents LVillePharmacie As System.Windows.Forms.Label
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents lTest As System.Windows.Forms.Label
    Friend WithEvents tNomPharmacie As C1.Win.C1Input.C1TextBox
    Friend WithEvents tCodePharmacie As C1.Win.C1Input.C1TextBox
    Friend WithEvents LNomPharmacie As System.Windows.Forms.Label
    Friend WithEvents LCodePharmacie As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents gDetailsEmprunts As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents gEmprunts As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents lTotalPrets As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents lTotalEmprunts As System.Windows.Forms.Label
    Friend WithEvents dtpDate As C1.Win.C1Input.C1DateEdit
    Friend WithEvents bViderMouvements As C1.Win.C1Input.C1Button
    Friend WithEvents lSoldeInitial As System.Windows.Forms.Label
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents lDateInitiale As System.Windows.Forms.Label
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents C1DockingTabPage3 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents DataSet_EtatInventaireTemporaire1 As Pharma2000Premium.DataSet_EtatInventaireTemporaire
    Friend WithEvents gReglements As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bModifierReglement As C1.Win.C1Input.C1Button
    Friend WithEvents bSupprimerReglement As C1.Win.C1Input.C1Button
    Friend WithEvents bAjouterReglement As C1.Win.C1Input.C1Button
    Friend WithEvents Label5 As System.Windows.Forms.Label
End Class
