<UserControl x:Class="PharmaModerne.UI.Views.Dashboard.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="900" d:DesignWidth="1400">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- En-tête Dashboard -->
            <Border Grid.Row="0" 
                    Background="DarkBlue" 
                    CornerRadius="10" 
                    Padding="20" 
                    Margin="0,0,0,20">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <TextBlock Text="📊" FontSize="32" Foreground="White" VerticalAlignment="Center"/>
                        <StackPanel Margin="16,0,0,0" VerticalAlignment="Center">
                            <TextBlock Text="Dashboard PHARMA2000" 
                                     FontSize="24" 
                                     FontWeight="Bold"
                                     Foreground="White"/>
                            <TextBlock Text="Vue d'ensemble en temps réel" 
                                     FontSize="14" 
                                     Foreground="LightBlue"/>
                        </StackPanel>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <TextBlock Text="{Binding DateMiseAJour, StringFormat='Mis à jour: {0:HH:mm:ss}'}" 
                                 FontSize="14" 
                                 Foreground="LightGray"
                                 VerticalAlignment="Center"
                                 Margin="0,0,20,0"/>
                        
                        <Button Content="🔄 ACTUALISER"
                                Command="{Binding ActualiserCommand}"
                                Padding="15,8"
                                Background="Orange"
                                Foreground="White"
                                BorderThickness="0"
                                FontWeight="Bold"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- Indicateurs clés (KPI) -->
            <UniformGrid Grid.Row="1" Columns="4" Margin="0,0,0,20">
                
                <!-- Ventes du jour -->
                <Border Background="Green" 
                        CornerRadius="10" 
                        Padding="20" 
                        Margin="0,0,10,0">
                    <StackPanel>
                        <TextBlock Text="💰" FontSize="32" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="VENTES DU JOUR" 
                                 FontSize="14" 
                                 FontWeight="Bold"
                                 Foreground="White"
                                 HorizontalAlignment="Center"
                                 Margin="0,10,0,5"/>
                        <TextBlock Text="{Binding VentesDuJour, StringFormat=C}" 
                                 FontSize="24" 
                                 FontWeight="Bold"
                                 Foreground="Yellow"
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding NombreVentesDuJour, StringFormat='{}{0} ventes'}" 
                                 FontSize="12" 
                                 Foreground="LightGreen"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
                
                <!-- Clients -->
                <Border Background="Blue" 
                        CornerRadius="10" 
                        Padding="20" 
                        Margin="5,0">
                    <StackPanel>
                        <TextBlock Text="👥" FontSize="32" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="CLIENTS" 
                                 FontSize="14" 
                                 FontWeight="Bold"
                                 Foreground="White"
                                 HorizontalAlignment="Center"
                                 Margin="0,10,0,5"/>
                        <TextBlock Text="{Binding NombreClients}" 
                                 FontSize="24" 
                                 FontWeight="Bold"
                                 Foreground="Yellow"
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding NouveauxClientsJour, StringFormat='+{0} aujourd''hui'}"
                                 FontSize="12"
                                 Foreground="LightBlue"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
                
                <!-- Articles -->
                <Border Background="Purple" 
                        CornerRadius="10" 
                        Padding="20" 
                        Margin="5,0">
                    <StackPanel>
                        <TextBlock Text="💊" FontSize="32" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="ARTICLES" 
                                 FontSize="14" 
                                 FontWeight="Bold"
                                 Foreground="White"
                                 HorizontalAlignment="Center"
                                 Margin="0,10,0,5"/>
                        <TextBlock Text="{Binding NombreArticles}" 
                                 FontSize="24" 
                                 FontWeight="Bold"
                                 Foreground="Yellow"
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding ArticlesStockFaible, StringFormat='{}{0} stock faible'}" 
                                 FontSize="12" 
                                 Foreground="Pink"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
                
                <!-- Scanner -->
                <Border Background="Orange" 
                        CornerRadius="10" 
                        Padding="20" 
                        Margin="10,0,0,0">
                    <StackPanel>
                        <TextBlock Text="📱" FontSize="32" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="SCANNER" 
                                 FontSize="14" 
                                 FontWeight="Bold"
                                 Foreground="White"
                                 HorizontalAlignment="Center"
                                 Margin="0,10,0,5"/>
                        <TextBlock Text="{Binding ScansAujourdhui}" 
                                 FontSize="24" 
                                 FontWeight="Bold"
                                 Foreground="Yellow"
                                 HorizontalAlignment="Center"/>
                        <Border Background="Green"
                              CornerRadius="10"
                              Padding="5,2"
                              HorizontalAlignment="Center"
                              Visibility="Collapsed">
                            <TextBlock Text="ACTIF" 
                                     FontSize="10" 
                                     Foreground="White"
                                     FontWeight="Bold"/>
                        </Border>
                    </StackPanel>
                </Border>
            </UniformGrid>
            
            <!-- Graphiques et analyses -->
            <Grid Grid.Row="2" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Graphique des ventes -->
                <Border Grid.Column="0" 
                        Background="White" 
                        CornerRadius="10" 
                        Padding="20" 
                        Margin="0,0,10,0"
                        Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                    <StackPanel>
                        <TextBlock Text="📈 Évolution des Ventes (7 derniers jours)" 
                                 FontSize="16" 
                                 FontWeight="Bold"
                                 Margin="0,0,0,20"/>
                        
                        <!-- Simulation d'un graphique -->
                        <Grid Height="200">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <!-- Barres du graphique -->
                            <UniformGrid Grid.Row="0" Columns="7" Margin="0,0,0,10">
                                <Border Background="LightBlue" Height="50" VerticalAlignment="Bottom" Margin="2"/>
                                <Border Background="LightBlue" Height="80" VerticalAlignment="Bottom" Margin="2"/>
                                <Border Background="LightBlue" Height="60" VerticalAlignment="Bottom" Margin="2"/>
                                <Border Background="LightBlue" Height="90" VerticalAlignment="Bottom" Margin="2"/>
                                <Border Background="LightBlue" Height="70" VerticalAlignment="Bottom" Margin="2"/>
                                <Border Background="LightBlue" Height="100" VerticalAlignment="Bottom" Margin="2"/>
                                <Border Background="Green" Height="120" VerticalAlignment="Bottom" Margin="2"/>
                            </UniformGrid>
                            
                            <!-- Labels des jours -->
                            <UniformGrid Grid.Row="1" Columns="7">
                                <TextBlock Text="Lun" HorizontalAlignment="Center" FontSize="12"/>
                                <TextBlock Text="Mar" HorizontalAlignment="Center" FontSize="12"/>
                                <TextBlock Text="Mer" HorizontalAlignment="Center" FontSize="12"/>
                                <TextBlock Text="Jeu" HorizontalAlignment="Center" FontSize="12"/>
                                <TextBlock Text="Ven" HorizontalAlignment="Center" FontSize="12"/>
                                <TextBlock Text="Sam" HorizontalAlignment="Center" FontSize="12"/>
                                <TextBlock Text="Dim" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold"/>
                            </UniformGrid>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Top articles -->
                <Border Grid.Column="1" 
                        Background="White" 
                        CornerRadius="10" 
                        Padding="20" 
                        Margin="10,0,0,0"
                        Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                    <StackPanel>
                        <TextBlock Text="🏆 Top Articles" 
                                 FontSize="16" 
                                 FontWeight="Bold"
                                 Margin="0,0,0,15"/>
                        
                        <ListBox ItemsSource="{Binding TopArticles}"
                               BorderThickness="0"
                               Height="200">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" 
                                                 Text="{Binding Position}" 
                                                 FontWeight="Bold"
                                                 Foreground="Orange"
                                                 Margin="0,0,10,0"/>
                                        
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="{Binding Designation}" 
                                                     FontWeight="Bold"
                                                     FontSize="12"
                                                     TextWrapping="Wrap"/>
                                            <TextBlock Text="{Binding CodeArticle}" 
                                                     FontSize="10"
                                                     Foreground="Gray"/>
                                        </StackPanel>
                                        
                                        <TextBlock Grid.Column="2" 
                                                 Text="{Binding QuantiteVendue}" 
                                                 FontWeight="Bold"
                                                 Foreground="Green"/>
                                    </Grid>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </StackPanel>
                </Border>
            </Grid>
            
            <!-- Alertes et notifications -->
            <Border Grid.Row="3" 
                    Background="White" 
                    CornerRadius="10" 
                    Padding="20" 
                    Margin="0,0,0,20"
                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                <StackPanel>
                    <TextBlock Text="⚠️ Alertes et Notifications" 
                             FontSize="16" 
                             FontWeight="Bold"
                             Margin="0,0,0,15"/>
                    
                    <UniformGrid Columns="3">
                        <!-- Alertes stock -->
                        <Border Background="Red" 
                                CornerRadius="5" 
                                Padding="15" 
                                Margin="0,0,10,0">
                            <StackPanel>
                                <TextBlock Text="📦 STOCK FAIBLE" 
                                         FontWeight="Bold"
                                         Foreground="White"
                                         HorizontalAlignment="Center"/>
                                <TextBlock Text="{Binding AlertesStockFaible}" 
                                         FontSize="24"
                                         FontWeight="Bold"
                                         Foreground="Yellow"
                                         HorizontalAlignment="Center"/>
                                <Button Content="Voir détails"
                                      Command="{Binding VoirAlerteStockCommand}"
                                      Background="DarkRed"
                                      Foreground="White"
                                      BorderThickness="0"
                                      Padding="8,4"
                                      Margin="0,10,0,0"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Articles périmés -->
                        <Border Background="Orange" 
                                CornerRadius="5" 
                                Padding="15" 
                                Margin="5,0">
                            <StackPanel>
                                <TextBlock Text="⏰ PÉRIMÉS" 
                                         FontWeight="Bold"
                                         Foreground="White"
                                         HorizontalAlignment="Center"/>
                                <TextBlock Text="{Binding ArticlesPerimes}" 
                                         FontSize="24"
                                         FontWeight="Bold"
                                         Foreground="Yellow"
                                         HorizontalAlignment="Center"/>
                                <Button Content="Voir détails"
                                      Command="{Binding VoirPerimesCommand}"
                                      Background="DarkOrange"
                                      Foreground="White"
                                      BorderThickness="0"
                                      Padding="8,4"
                                      Margin="0,10,0,0"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Commandes en attente -->
                        <Border Background="Blue" 
                                CornerRadius="5" 
                                Padding="15" 
                                Margin="10,0,0,0">
                            <StackPanel>
                                <TextBlock Text="📋 COMMANDES" 
                                         FontWeight="Bold"
                                         Foreground="White"
                                         HorizontalAlignment="Center"/>
                                <TextBlock Text="{Binding CommandesEnAttente}" 
                                         FontSize="24"
                                         FontWeight="Bold"
                                         Foreground="Yellow"
                                         HorizontalAlignment="Center"/>
                                <Button Content="Voir détails"
                                      Command="{Binding VoirCommandesCommand}"
                                      Background="DarkBlue"
                                      Foreground="White"
                                      BorderThickness="0"
                                      Padding="8,4"
                                      Margin="0,10,0,0"/>
                            </StackPanel>
                        </Border>
                    </UniformGrid>
                </StackPanel>
            </Border>
            
            <!-- Actions rapides -->
            <Border Grid.Row="4" 
                    Background="White" 
                    CornerRadius="10" 
                    Padding="20"
                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                <StackPanel>
                    <TextBlock Text="🚀 Actions Rapides" 
                             FontSize="16" 
                             FontWeight="Bold"
                             Margin="0,0,0,15"/>
                    
                    <UniformGrid Columns="4">
                        <Button Content="🛒 NOUVELLE VENTE"
                                Command="{Binding NouvelleVenteCommand}"
                                Padding="15"
                                Margin="5"
                                Background="Green"
                                Foreground="White"
                                BorderThickness="0"
                                FontWeight="Bold"/>
                        
                        <Button Content="👤 NOUVEAU CLIENT"
                                Command="{Binding NouveauClientCommand}"
                                Padding="15"
                                Margin="5"
                                Background="Blue"
                                Foreground="White"
                                BorderThickness="0"
                                FontWeight="Bold"/>
                        
                        <Button Content="💊 NOUVEL ARTICLE"
                                Command="{Binding NouvelArticleCommand}"
                                Padding="15"
                                Margin="5"
                                Background="Purple"
                                Foreground="White"
                                BorderThickness="0"
                                FontWeight="Bold"/>
                        
                        <Button Content="📊 RAPPORTS"
                                Command="{Binding VoirRapportsCommand}"
                                Padding="15"
                                Margin="5"
                                Background="Orange"
                                Foreground="White"
                                BorderThickness="0"
                                FontWeight="Bold"/>
                    </UniformGrid>
                    
                    <Separator Margin="0,20"/>
                    
                    <UniformGrid Columns="3">
                        <Button Content="📱 ACTIVER SCANNER"
                                Command="{Binding ActiverScannerCommand}"
                                Padding="15"
                                Margin="5"
                                Background="Orange"
                                Foreground="White"
                                BorderThickness="0"
                                FontWeight="Bold"/>
                        
                        <Button Content="📤 SAUVEGARDER"
                                Command="{Binding SauvegarderCommand}"
                                Padding="15"
                                Margin="5"
                                Background="Gray"
                                Foreground="White"
                                BorderThickness="0"
                                FontWeight="Bold"/>
                        
                        <Button Content="⚙️ PARAMÈTRES"
                                Command="{Binding ParametresCommand}"
                                Padding="15"
                                Margin="5"
                                Background="DarkGray"
                                Foreground="White"
                                BorderThickness="0"
                                FontWeight="Bold"/>
                    </UniformGrid>
                </StackPanel>
            </Border>
        </Grid>
    </ScrollViewer>

</UserControl>
