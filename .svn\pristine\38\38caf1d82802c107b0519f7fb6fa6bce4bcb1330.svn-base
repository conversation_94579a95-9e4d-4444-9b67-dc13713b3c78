﻿'Créer Par MOMRI Le 23/10/2012
'MODULE: A pour rôle de tracer les erreus dans un fichier log
'avec un ensemble d'information pour nous aider à connaitre 
'les causes des bugs, et nous envoyer un email contenant 
'une imprime écran
'Le MODULE fait appler le fichier DLL TraceAPP.DLL

'UTILISATION: Ecrire dans fichier log

'Paramétres: 
'            1 -  Module en cours d'execution
'            2 -  Forme 'Code source' 
'            3 -  Nom de la fonction
'            4 -  Nom de l'exception (Si existe, si non on va écrire "NoException")
'            5 -  Numéro de message d'erreur (Si existe, si non on va écrire "NoError")
'            6 -  Observation
'            7 -  Nom Utilisateur 
'            8 -  Cede Utilisateur
'            9 -  Nom Machine Logique
'            10 -  Nom Machine Physique
'            11 -  Date Time
'            12 -  Envoyer Mail (True/False)
'            13-  Aciver Tracing (True/False)
'            14-  Aciver ImprimeEcran (True/False)



Imports System.Drawing
Imports TraceApp.logFile
Imports Microsoft.VisualBasic


Module TraceFichier

    Private Sub ecrireFichierLog(ByVal nomDuModule As String, ByVal nomDuFome As String, ByVal nomDuFonction As String, ByVal nomException As String, ByVal NumeroMessageErreur As String, ByVal observation As String, ByVal activerAfficheForme As String, ByVal activerTraceLog As Boolean, ByVal activerTraceEcran As Boolean)

        '----------------------------------------Declaration


        If activerAfficheForme = True Then

            'Le programme va ecrire dans le fichier Log
            'on affichant la Form d'exception

            'Gérer l'Exception
            fMessageException.Show(nomDuModule, nomDuFome, nomDuFonction, nomException, NumeroMessageErreur, observation, activerAfficheForme, activerTraceLog, activerTraceEcran)

        Else

            'Le programme va ecrire dans le fichier Log
            'sans afficher la Form d'exception
            'on l'utilise en cas de suivi des mvr effectuées par 
            'lutilisateur 


        End If


        'Affciher la forme fMessageException
        fMessageException.Show()


    End Sub

End Module
