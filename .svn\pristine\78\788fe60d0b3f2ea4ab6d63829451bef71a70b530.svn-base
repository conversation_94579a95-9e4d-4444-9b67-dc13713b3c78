﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fProjetCommande
    Dim cmdChargementCommande As New SqlCommand
    Dim cbChargementCommande As New SqlCommandBuilder
    Dim dsChargementCommande As New DataSet
    Dim daChargementCommande As New SqlDataAdapter

    Dim cmdCommande As New SqlCommand
    Dim cbCommande As New SqlCommandBuilder
    Public Shared dsCommande As New DataSet
    Dim daCommande As New SqlDataAdapter
    Dim daCommande1 As New SqlDataAdapter
    Dim mode As String = ""

    Public NumeroCommande As String = ""

    Public TotalTTCAchat As Double = 0.0
    Public TotalHTAchat As Double = 0.0
    Public TotalTVA As Double = 0.0

    Public TVA As Double = 0.0
    Public Timbre As Double = 0.3

    Dim StrSQL As String = ""
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNum As New SqlDataAdapter

    '****************************************************************************************
    '******************************* Critéres de selection Commande *************************
    '****************************************************************************************
    Public TypeCommande As String = ""
    Public NombreDeJour As Integer = 0
    Public SansManquantDepuis As String = ""
    Public Reference As String = ""
    Public mois As Integer = 0
    Public DebutPeriode As String = ""
    Public FinPeriode As String = ""
    Public Section As String = ""
    Public DebutIntervalle As String = ""
    Public FinIntervalle As String = ""
    Public Forme As Integer = 0
    Public Categorie As Integer = 0
    Public Labo As Integer = 0
    Public Rayon As String = ""
    Public RayonSelectionne As String = ""
    Public Trie As String = ""
    Public CommandeEnCours As Boolean = False

    '------------------------------------------------- les informations de paiement (retour de fenetre paiement)
    Public Operateur As Integer = 0
    '---------------------------------------- variable pour recuperer le code article à partir de la fenêtre recherchre multicritere
    Dim CodeArticleRechercheMC As String = ""

    Public NouvelleCommande As DataRow = Nothing 'datarow pour charger l'entête dans la datatable ACHAT
    Public NouvelArticle As DataRow = Nothing 'datarow pour charger les détails dans la datatable ACHAT_DETAIL
    Public dr As DataRow = Nothing 'datarow pour charger dans la datatable ARTICLE qui contient les articles recherchés pendant la saisie de la designation
    '---------------------------------------- datarow pour charger les fournisseurs de ce projet 
    Public NouveauFournisseur As DataRow = Nothing

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "116" And bAjouter.Enabled = True Then
            bAjouter_Click(sender, e)
        End If
        If argument = "114" And bConfirmer.Enabled = True Then
            bConfirmer_Click(sender, e)
        End If
        If argument = "121" And bAnnuler.Enabled = True Then
            bAnnuler_Click(sender, e)
        End If
       
        If argument = "113" And bFournisseur.Enabled = True Then
            bFournisseur_Click(sender, e)
        End If
        
        If argument = "118" And bSupprimer.Enabled = True Then
            bSupprimer_Click(sender, e)
        End If
        '--------------------- boutton close 
        'If argument = "123" Then
        '    Dim i As Integer
        '    For i = 0 To fMain.Tab.TabPages.Count - 1
        '        If fMain.Tab.TabPages(i).Text = "Vente" Then
        '            fMain.Tab.TabPages(i).Hide()
        '            Exit Sub
        '        End If
        '    Next
        'End If
    End Sub

    Public Sub Init()
        Timer1.Start()
        
        lTotHTAchat.Text = "0.000"
        lTotalTTCAchat.Text = "0.000"
        lTotalTVA.Text = "0.000"

        Dim StrSQL As String = ""
        Dim I As Integer

        With cmbType
            .HoldFields()
            .AddItem("JOURNALIERE")
            .AddItem("GROUPEE")
            .ColumnHeaders = False
        End With

        Try
            dsChargementCommande.Tables("COMMANDE_DETAILS").Clear()
        Catch ex As Exception
        End Try

        Try
            dsChargementCommande.Tables("COMMANDE").Clear()
        Catch ex As Exception
        End Try

        Try
            dsChargementCommande.Tables("FOURNISSEUR").Clear()
        Catch ex As Exception
        End Try

        'chargement des Fournisseurs
        StrSQL = "SELECT CodeFournisseur,NomFournisseur FROM FOURNISSEUR ORDER BY NomFournisseur ASC"
        cmdChargementCommande.Connection = ConnectionServeur
        cmdChargementCommande.CommandText = StrSQL
        daChargementCommande = New SqlDataAdapter(cmdChargementCommande)
        daChargementCommande.Fill(dsChargementCommande, "FOURNISSEUR")
        cmbFournisseur.DataSource = dsChargementCommande.Tables("FOURNISSEUR")
        cmbFournisseur.ValueMember = "CodeFournisseur"
        cmbFournisseur.DisplayMember = "NomFournisseur"
        cmbFournisseur.ColumnHeaders = False
        cmbFournisseur.Splits(0).DisplayColumns("CodeFournisseur").Visible = False
        cmbFournisseur.Splits(0).DisplayColumns("NomFournisseur").Width = 10
        cmbFournisseur.ExtendRightColumn = True


        'chargement des Entêtes des achats        
        StrSQL = "SELECT * FROM COMMANDE ORDER BY NumeroCommande ASC"
        cmdChargementCommande.Connection = ConnectionServeur
        cmdChargementCommande.CommandText = StrSQL
        daChargementCommande = New SqlDataAdapter(cmdChargementCommande)
        daChargementCommande.Fill(dsChargementCommande, "COMMANDE")
        If dsChargementCommande.Tables("COMMANDE").Rows.Count > 0 Then
            NumeroCommande = dsChargementCommande.Tables("COMMANDE").Rows(dsChargementCommande.Tables("COMMANDE").Rows.Count - 1).Item("NumeroCommande")

            lNumeroCommande.Text = dsChargementCommande.Tables("COMMANDE").Rows(dsChargementCommande.Tables("COMMANDE").Rows.Count - 1)("NumeroCommande")
            lDateCommande.Text = dsChargementCommande.Tables("COMMANDE").Rows(dsChargementCommande.Tables("COMMANDE").Rows.Count - 1)("Date")

            TotalTTCAchat = dsChargementCommande.Tables("COMMANDE").Rows(dsChargementCommande.Tables("COMMANDE").Rows.Count - 1)("TotalTTC")
            TotalHTAchat = dsChargementCommande.Tables("COMMANDE").Rows(dsChargementCommande.Tables("COMMANDE").Rows.Count - 1)("TotalHT")
            lTotalTTCAchat.Text = Math.Round(dsChargementCommande.Tables("COMMANDE").Rows(dsChargementCommande.Tables("COMMANDE").Rows.Count - 1)("TotalTTC"), 3)
            lTotHTAchat.Text = TotalHTAchat.ToString
            lTotalTVA.Text = dsChargementCommande.Tables("COMMANDE").Rows(dsChargementCommande.Tables("COMMANDE").Rows.Count - 1)("TotalTVA")

            lOperateur.Text = RecupererValeurExecuteScalaire("NomOperateur", "OPERATEUR", "CodeOperateur", dsChargementCommande.Tables("COMMANDE").Rows(dsChargementCommande.Tables("COMMANDE").Rows.Count - 1)("CodePersonnel"))

        End If
        'chargement des détails des achats 
        StrSQL = "SELECT '' as Fournisseur," + _
                 "NumeroCommande," + _
                 "CodeArticle," + _
                 "CodeABarre," + _
                 "Designation," + _
                 "LibelleForme," + _
                 "Qte," + _
                 "Stock," + _
                 "DatePeremption," + _
                 "PrixAchatHT," + _
                 "TVA," + _
                 "TotalTTCAchat," + _
                 "StockAlerte," + _
                 "EnCours," + _
                 "QteACommander," + _
                 "QteUnitaire " + _
                 "FROM COMMANDE_DETAILS,FORME_ARTICLE " + _
                 "WHERE COMMANDE_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
                 "AND NumeroCommande =" + Quote(NumeroCommande) + ""

        cmdChargementCommande.Connection = ConnectionServeur
        cmdChargementCommande.CommandText = StrSQL
        daChargementCommande = New SqlDataAdapter(cmdChargementCommande)
        daChargementCommande.Fill(dsChargementCommande, "COMMANDE_DETAILS")


        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsChargementCommande
            Catch ex As Exception
            End Try

            .DataMember = "COMMANDE_DETAILS"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qté"
            .Columns("Stock").Caption = "Stock"
            .Columns("DatePeremption").Caption = "Date de péremption"
            .Columns("PrixAchatHT").Caption = "Prix A HT "
            .Columns("TVA").Caption = "TVAinit"
            .Columns("TotalTTCAchat").Caption = "Total TTC "
            .Columns("StockAlerte").Caption = "S Alerte"
            .Columns("EnCours").Caption = "Encours "
            .Columns("QteACommander").Caption = "Qte/Jour"
            .Columns("QteUnitaire").Caption = "Q unitaire"

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns(2).Style.HorizontalAlignment = AlignHorzEnum.Near
            For I = 0 To .Columns.Count - 1
                '.Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTTCAchat").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("NumeroCommande").Width = 0
            .Splits(0).DisplayColumns("NumeroCommande").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 60
            .Splits(0).DisplayColumns("Designation").Width = 240
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("Stock").Width = 50
            .Splits(0).DisplayColumns("DatePeremption").Width = 100
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 90
            .Splits(0).DisplayColumns("TVA").Width = 80
            .Splits(0).DisplayColumns("TotalTTCAchat").Width = 90
            .Splits(0).DisplayColumns("StockAlerte").Width = 50
            .Splits(0).DisplayColumns("EnCours").Width = 90
            .Splits(0).DisplayColumns("QteACommander").Width = 50
            .Splits(0).DisplayColumns("QteUnitaire").Width = 50

            'coloriage de la liste 
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.BackColor = Color.FromArgb(250, 250, 200)
            Next

            .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(250, 250, 200)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(210, 240, 255)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(210, 240, 255)
            .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("PrixAchatHT").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("TVA").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("TotalTTCAchat").Style.BackColor = Color.FromArgb(210, 240, 255)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gArticles)
        End With

        Me.tdbdFournisseur.DataSource = dsChargementCommande
        Me.tdbdFournisseur.DataMember = "FOURNISSEUR"
        Me.gArticles.Columns("Fournisseur").DropDown = Me.tdbdFournisseur
        gArticles.Columns("Fournisseur").Value = tdbdFournisseur.Columns("NomFournisseur").Value 'PresentationEnum.ComboBox '
        Me.tdbdFournisseur.Columns("CodeFournisseur").Caption = "Code Fournisseur"
        Me.tdbdFournisseur.Columns("NomFournisseur").Caption = "Nom Fournisseur"

        'Me.tdbdFournisseur.ValueMember = "CodeFournisseur"
        Me.tdbdFournisseur.DisplayMember = "NomFournisseur"
        Me.tdbdFournisseur.DisplayColumns("CodeFournisseur").Width = 0
        Me.tdbdFournisseur.DisplayColumns("NomFournisseur").Width = 100


        '****************************************************************************************
        '******** initialisation de la datatable article qui est utilisé dans la liste de *******
        '*** recherche alimenté selon les entrés de l'utilisateur dans la colonne designation ***
        '****************************************************************************************

        StrSQL = "SELECT CodeArticle, " + _
                 "Designation, " + _
                 "LibelleForme, " + _
                 "PrixVenteTTC " + _
                 "FROM ARTICLE, " + _
                 "FORME_ARTICLE " + _
                 "WHERE ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                 "AND Designation LIKE '" + gArticles.Columns("Designation").Value + _
                 "'ORDER BY Designation"

        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande = New SqlDataAdapter(cmdCommande)
        daCommande.Fill(dsCommande, "ARTICLE")

        With gListeRecherche
            .Columns.Clear()
            .DataSource = dsCommande
            .DataMember = "ARTICLE"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code Article"
            .Columns("Designation").Caption = "Designation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("PrixVenteTTC").Caption = "Prix de vente"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centre tous les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Visible = False
            Next

            .Splits(0).DisplayColumns("CodeArticle").Visible = True
            .Splits(0).DisplayColumns("Designation").Visible = True
            .Splits(0).DisplayColumns("LibelleForme").Visible = True
            .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("Designation").Width = 300
            .Splits(0).DisplayColumns("LibelleForme").Width = 100
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 20
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gListeRecherche)
        End With

        cbCommande = New SqlCommandBuilder(daCommande)

        bConfirmer.Enabled = False
        bAnnuler.Enabled = False
        'bCommande.Enabled = False
        bFournisseur.Enabled = False
        'bRemise.Enabled = False
        bAjouter.Enabled = True


        GroupeFournisseur.Enabled = False
        Dim sender As New Object
        Dim e As New System.EventArgs

        AfficherStatistique("")

        bAjouter_Click(sender, e)

    End Sub

    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        Try
            Quote(ValeurCle)
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurCle)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click
        Dim StrSQL As String = ""
        'Dim STRSQL1 As String = ""
        Dim i As Integer = 0
        Dim Cond As String = ""
        Dim NumeroCommande As String = ""
        Dim unite As Double = 0.0

        mode = "Ajout"

        '------------------------------ préparation des datatable vides 

        Try
            dsCommande.Tables("COMMANDE_DETAILS_PROJET").Clear()
        Catch ex As Exception
        End Try

        Try
            dsCommande.Tables("COMMANDE_DETAILS").Clear()
        Catch ex As Exception
        End Try

        Try
            dsCommande.Tables("COMMANDE").Clear()
        Catch ex As Exception
        End Try

        Try
            dsCommande.Tables("ARTICLE_JOURNALIERE").Clear()
        Catch ex As Exception
        End Try

        Try
            dsCommande.Tables("ARTICLE_GROUPEE").Clear()
        Catch ex As Exception
        End Try

        '----------------------------- chargement des Entêtes des Commande 

        StrSQL = "SELECT top 0 * FROM COMMANDE ORDER BY NumeroCommande ASC"
        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande = New SqlDataAdapter(cmdCommande)
        daCommande.Fill(dsCommande, "COMMANDE")
        cbCommande = New SqlCommandBuilder(daCommande)

        '----------------------------- chargement des détails des Commande 

        StrSQL = "SELECT '' as Fournisseur," + _
                 "NumeroCommande," + _
                 "CodeArticle," + _
                 "CodeABarre," + _
                 "Designation," + _
                 "LibelleForme," + _
                 "Qte," + _
                 "Stock," + _
                 "DatePeremption," + _
                 "PrixAchatHT," + _
                 "TVA," + _
                 "TotalTTCAchat," + _
                 "StockAlerte," + _
                 "EnCours," + _
                 "QteACommander," + _
                 "QteUnitaire," + _
                 "COMMANDE_DETAILS_PROJET.CodeForme " + _
                 "FROM COMMANDE_DETAILS as COMMANDE_DETAILS_PROJET,FORME_ARTICLE " + _
                 "WHERE COMMANDE_DETAILS_PROJET.CodeForme=FORME_ARTICLE.CodeForme " + _
                 "AND NumeroCommande ='0'"

        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande.Fill(dsCommande, "COMMANDE_DETAILS_PROJET1")
        cbCommande = New SqlCommandBuilder(daCommande)


        With gArticles
            .Columns.Clear()
            .DataSource = dsCommande
            .DataMember = "COMMANDE_DETAILS_PROJET1"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qté"
            .Columns("Stock").Caption = "Stock"
            .Columns("DatePeremption").Caption = "Date.Pérem"
            .Columns("PrixAchatHT").Caption = "Prix A HT "
            .Columns("TVA").Caption = "TVA"
            .Columns("TotalTTCAchat").Caption = "Total TTC "
            .Columns("StockAlerte").Caption = "S Alerte"
            .Columns("EnCours").Caption = "Encours "
            .Columns("QteACommander").Caption = "Qte / Jour"
            .Columns("QteUnitaire").Caption = "Q unit"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTTCAchat").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("Fournisseur").Locked = False
            .Splits(0).DisplayColumns("CodeABarre").Locked = False
            .Splits(0).DisplayColumns("Designation").Locked = False
            .Splits(0).DisplayColumns("Qte").Locked = False

            .Splits(0).DisplayColumns("Fournisseur").Width = 80

            .Splits(0).DisplayColumns("NumeroCommande").Width = 0
            .Splits(0).DisplayColumns("NumeroCommande").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 50
            .Splits(0).DisplayColumns("Designation").Width = 240
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("Stock").Width = 50
            .Splits(0).DisplayColumns("DatePeremption").Width = 100
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 90
            .Splits(0).DisplayColumns("TVA").Width = 90
            .Splits(0).DisplayColumns("TotalTTCAchat").Width = 90
            .Splits(0).DisplayColumns("StockAlerte").Width = 50
            .Splits(0).DisplayColumns("EnCours").Width = 90
            .Splits(0).DisplayColumns("QteACommander").Width = 40
            .Splits(0).DisplayColumns("QteUnitaire").Width = 50
            .Splits(0).DisplayColumns("CodeForme").Visible = False
            .Splits(0).DisplayColumns("NumeroCommande").Visible = False

            'coloriage de la liste 
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.BackColor = Color.FromArgb(250, 250, 200)
            Next

            .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(250, 250, 200)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(210, 240, 255)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(210, 240, 255)
            .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("PrixAchatHT").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("TVA").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("TotalTTCAchat").Style.BackColor = Color.FromArgb(210, 240, 255)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True

        End With

        '****************************************************************************************
        '********************** initialisation des differents zones de textes *******************
        '*************************** initialisation des variables globaux************************
        '****************************************************************************************

        TotalTTCAchat = 0.0
        TotalHTAchat = 0.0
        TVA = 0.0
        Timbre = 0.3
        TotalTVA = 0.0

        lTotHTAchat.Text = "0.000"
        lTotalTTCAchat.Text = "0.000"

        lTotalTVA.Text = "0.000"

        lOperateur.Text = "-"

        lDateCommande.Text = System.DateTime.Now
        'cmbFournisseur.Text = ""
        lNumeroCommande.Text = "-------------"

        bAnnuler.Enabled = True
        bConfirmer.Enabled = True
        'bCommande.Enabled = True
        bFournisseur.Enabled = True
        'bRemise.Enabled = True

        'bSupprimer.Enabled = False

        'bAjouter.Enabled = False

        GroupeFournisseur.Enabled = True

        gArticles.Focus()
        gArticles.Col = 2
        gArticles.EditActive = True

        Dim MyCritereDeSelectionCommande As New fCritereDeSelectionCommande
        MyCritereDeSelectionCommande.FenetreAppelante = "ProjetCommande"
        MyCritereDeSelectionCommande.ShowDialog()

        TypeCommande = fCritereDeSelectionCommande.TypeCommande
        NombreDeJour = fCritereDeSelectionCommande.NombreDeJour
        SansManquantDepuis = fCritereDeSelectionCommande.SansManquantDepuis
        Reference = fCritereDeSelectionCommande.Reference
        mois = fCritereDeSelectionCommande.mois
        DebutPeriode = fCritereDeSelectionCommande.DebutPeriode
        FinPeriode = fCritereDeSelectionCommande.FinPeriode
        Section = fCritereDeSelectionCommande.Section
        DebutIntervalle = fCritereDeSelectionCommande.DebutIntervalle
        FinIntervalle = fCritereDeSelectionCommande.FinIntervalle
        Forme = fCritereDeSelectionCommande.Forme
        Categorie = fCritereDeSelectionCommande.Categorie
        Labo = fCritereDeSelectionCommande.Labo
        Rayon = fCritereDeSelectionCommande.Rayon
        RayonSelectionne = fCritereDeSelectionCommande.RayonSelectionne
        Trie = fCritereDeSelectionCommande.Trie
        CommandeEnCours = fCritereDeSelectionCommande.CommandeEnCours

        MyCritereDeSelectionCommande.Dispose()
        MyCritereDeSelectionCommande.Close()


        'laison de la gride avec une datatable ou on va charger les détails 
        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande.Fill(dsCommande, "COMMANDE_DETAILS_PROJET")
        cbCommande = New SqlCommandBuilder(daCommande)

        'ajout d'un nouvel enregistrement vide dans les datatables convenables
        NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS_PROJET").NewRow()
        NouvelArticle("Designation") = "" 'initialisation pour que le calcul des montants ne compare pas 
        NouvelArticle("CodeArticle") = "" ' des chaines de caractères avec des NULL
        NouvelArticle("Fournisseur") = ""
        NouvelArticle("CodeABarre") = ""
        dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Add(NouvelArticle)


        '***************************************************************************************************
        '************************************ Cas de Groupée ********************************************
        '***************************************************************************************************

        Dim DebutDeConge As Date = Convert.ToDateTime("01/07/2012")
        Dim FinDeConge As Date = Convert.ToDateTime("15/07/2012")
        Dim NombreDeDimanche As Integer = 0
        Dim NombreDeDimancheConge As Integer = 0
        Dim NombreDesJoursDeReference As Integer = 0
        If TypeCommande = "GROUPEE" Then

            ' crirères de selection
            If SansManquantDepuis <> "" Then
                Cond = Cond + " AND (ARTICLE.DateAlerte >'" + SansManquantDepuis + "' OR ARTICLE.DateAlerte is null)"
            End If
            If Section = "INTERVALLE" Then
                Cond = Cond + " AND Section > " + DebutIntervalle.ToString + " AND Section < " + FinIntervalle.ToString
            End If
            If Forme <> 0 Then
                Cond = Cond + " AND ARTICLE.CodeForme = " + Forme.ToString
            End If
            If Categorie <> 0 Then
                Cond = Cond + " AND CodeCategorie = " + Categorie.ToString
            End If
            If Labo <> 0 Then
                Cond = Cond + " AND CodeLabo = " + Labo.ToString
            End If
            If Rayon = "RAYON" Then
                Cond = Cond + " AND Rayon = " + RayonSelectionne
            End If

            '---------------------------- calcul des jours en éléminant les jours de congés

            If NombreDeJour <> 1 Then
                If DebutDeConge > System.DateTime.Now And FinDeConge < DateAdd(DateInterval.Day, NombreDeJour, System.DateTime.Now) Then
                    NombreDeDimancheConge = (FinDeConge - DebutDeConge).Days / 7
                    NombreDeDimanche = NombreDeJour / 7 - NombreDeDimancheConge
                    NombreDeJour = NombreDeJour - (FinDeConge - DebutDeConge).Days - NombreDeDimanche
                End If
                If DebutDeConge < System.DateTime.Now And FinDeConge < DateAdd(DateInterval.Day, NombreDeJour, System.DateTime.Now) And FinDeConge > System.DateTime.Now Then
                    NombreDeDimancheConge = (FinDeConge - System.DateTime.Now).Days / 7
                    NombreDeDimanche = NombreDeJour / 7 - NombreDeDimancheConge
                    NombreDeJour = NombreDeJour - (FinDeConge - DebutDeConge).Days - NombreDeDimanche
                End If
                If DebutDeConge > System.DateTime.Now And DebutDeConge < DateAdd(DateInterval.Day, NombreDeJour, System.DateTime.Now) And FinDeConge > DateAdd(DateInterval.Day, NombreDeJour, System.DateTime.Now) Then
                    NombreDeDimancheConge = (FinDeConge - System.DateTime.Now).Days / 7
                    NombreDeDimanche = NombreDeJour / 7 - NombreDeDimancheConge
                    NombreDeJour = NombreDeJour - (FinDeConge - DebutDeConge).Days - NombreDeDimanche
                End If
            End If
            '---------------------------- Cas ou on a une période
            If Reference = "PERIODE" Then
                Dim JourMoisDebut As String = ""
                Dim JourMoisFin As String = ""
                Dim AnneeDebut As String = ""
                Dim AnneeFin As String = ""

                NombreDesJoursDeReference = DateDiff(DateInterval.Day, Convert.ToDateTime(DebutPeriode), Convert.ToDateTime(FinPeriode)) + 20

                JourMoisDebut = DebutPeriode.Substring(0, 6)
                AnneeDebut = DebutPeriode.Substring(6, 4)
                JourMoisFin = FinPeriode.Substring(0, 6)
                AnneeFin = FinPeriode.Substring(6, 4)

                StrSQL = "SELECT distinct(VENTE_DETAILS.Codearticle)," + _
                         "VENTE_DETAILS.CodeABarre," + _
                         "VENTE_DETAILS.DESIGNATION," + _
                         "LibelleForme," + _
                         "sum(Qte) as QTE " + _
                         "FROM VENTE_DETAILS " + _
                         "LEFT OUTER JOIN VENTE ON VENTE.NumeroVente=VENTE_DETAILS.NumeroVente  " + _
                         "LEFT OUTER JOIN ARTICLE ON ARTICLE.CodeArticle=VENTE_DETAILS.CodeArticle " + _
                         "LEFT OUTER JOIN FORME_ARTICLE ON FORME_ARTICLE.CodeForme=VENTE_DETAILS.CodeForme " + _
                         "WHERE ARTICLE.CodeCategorie <> 9 " + _
                         "AND (VENTE.Date between '" + JourMoisDebut + _
                         (Convert.ToInt32(AnneeDebut) - 1).ToString + "' AND '" + JourMoisFin + _
                         (Convert.ToInt32(AnneeFin) - 1).ToString + "'" + _
                         " OR VENTE.Date between '" + _
                         DateAdd(DateInterval.Day, -20, System.DateTime.Now) + "' AND '" + System.DateTime.Now + "')" + _
                         Cond + _
                         " GROUP BY VENTE_DETAILS.Codearticle,VENTE_DETAILS.CodeABarre,VENTE_DETAILS.DESIGNATION,LibelleForme" + _
                         " ORDER BY " + Trie

                cmdCommande.Connection = ConnectionServeur
                cmdCommande.CommandText = StrSQL
                daCommande.Fill(dsCommande, "ARTICLE_GROUPEE")
                cbCommande = New SqlCommandBuilder(daCommande)

                '---------------------------- Cas ou on a un mois
            ElseIf Reference = "MOIS" Then

                NombreDesJoursDeReference = 50

                StrSQL = "SELECT distinct(VENTE_DETAILS.Codearticle)," + _
                         "VENTE_DETAILS.CodeABarre," + _
                         "VENTE_DETAILS.DESIGNATION," + _
                         "LibelleForme," + _
                         "sum(Qte) as QTE " + _
                         "FROM VENTE_DETAILS " + _
                         "LEFT OUTER JOIN VENTE ON VENTE.NumeroVente=VENTE_DETAILS.NumeroVente " + _
                         "LEFT OUTER JOIN ARTICLE ON ARTICLE.CodeArticle=VENTE_DETAILS.CodeArticle " + _
                         "LEFT OUTER JOIN FORME_ARTICLE ON FORME_ARTICLE.CodeForme=VENTE_DETAILS.CodeForme " + _
                         "WHERE ARTICLE.CodeCategorie <> 9 " + _
                         "AND (MONTH(VENTE.Date)=" + mois.ToString + _
                         " AND YEAR(VENTE.Date)=" + (Convert.ToInt32(System.DateTime.Now.Year) - 1).ToString + _
                         " OR VENTE.Date between '" + DateAdd(DateInterval.Day, -20, System.DateTime.Now) + _
                         "' AND '" + System.DateTime.Now + "')" + _
                         Cond + _
                         " GROUP BY VENTE_DETAILS.Codearticle,VENTE_DETAILS.CodeABarre,VENTE_DETAILS.DESIGNATION,LibelleForme" + _
                         " ORDER BY " + Trie

                cmdCommande.Connection = ConnectionServeur
                cmdCommande.CommandText = StrSQL
                daCommande.Fill(dsCommande, "ARTICLE_GROUPEE")
                cbCommande = New SqlCommandBuilder(daCommande)

            End If

            '-------------------------- chargement de la tables COMMANDE_DETAILS

            Dim J As Integer
            Dim DataRowRecherche As DataRow = Nothing
            Dim DatePeremption As Date

            ProgressBar.Visible = True
            GroupeJauge.Visible = True
            ProgressBar.Value = 0

            unite = 100 / (dsCommande.Tables("ARTICLE_GROUPEE").Rows.Count - 1)

            'Arrêter la capture d'evenement clavier sur le contrôle 
            RemoveHandler Me.bAjouter.Click, AddressOf Me.bAjouter_Click

            For J = 0 To dsCommande.Tables("ARTICLE_GROUPEE").Rows.Count - 1

                If unite * J < (dsCommande.Tables("ARTICLE_GROUPEE").Rows.Count - 1) Then
                    ProgressBar.Value += 1
                    If ProgressBar.Value >= 100 Then
                        ProgressBar.Value = 0
                    End If
                End If
                Application.DoEvents()

                DataRowRecherche = dsCommande.Tables("ARTICLE_GROUPEE").Rows(J)

                NouvelArticle("Fournisseur") = ""
                NouvelArticle("NumeroCommande") = NumeroCommande
                NouvelArticle("CodeArticle") = DataRowRecherche.Item("CodeArticle")
                NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

                lArticleEnCours.Text = NouvelArticle("Designation")

                Try
                    NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                Catch ex As Exception
                End Try

                NouvelArticle("Stock") = CalculeStock(NouvelArticle("CodeArticle"))
                NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

                Try
                    NouvelArticle("StockAlerte") = RecupererValeurExecuteScalaire("StockAlerte", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                Catch ex As Exception
                    NouvelArticle("StockAlerte") = 0
                End Try

                NouvelArticle("EnCours") = CalculerEnCours(NouvelArticle("CodeArticle"))
                NouvelArticle("QteACommander") = RecupererValeurExecuteScalaire("QteACommander", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("Qte") = Int((DataRowRecherche.Item("QTE") / NombreDesJoursDeReference) * NombreDeJour)

                If CommandeEnCours = True Then
                    NouvelArticle("Qte") = NouvelArticle("Qte") - CalculerEnCours(NouvelArticle("CodeArticle"))
                    If NouvelArticle("Qte") < 0 Then
                        NouvelArticle("Qte") = 0
                    End If
                End If

                NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
                NouvelArticle("QteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", "FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
                '------------------ récupération de la date de péremption

                StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
                System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
                NouvelArticle("CodeArticle") + "' Order by DatePeremptionArticle ASC "

                cmdCommande.Connection = ConnectionServeur
                cmdCommande.CommandText = StrSQL

                Try
                    DatePeremption = cmdCommande.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If DatePeremption = #12:00:00 AM# Then
                    'NouvelArticle("DatePeremption") = "01/01/1900"
                Else
                    NouvelArticle("DatePeremption") = DatePeremption
                End If

                NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS_PROJET").NewRow()
                dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Add(NouvelArticle)
                NouvelArticle("Designation") = "" 'initialisation pour que le calcul des montants ne compare pas 
                NouvelArticle("CodeArticle") = "" ' des chaines de caractères avec des NULL
                NouvelArticle("Fournisseur") = ""
                NouvelArticle("CodeABarre") = ""
                gArticles.Refresh()

            Next J

            'Reprendre la capture d'evenement clavier sur le contrôle 
            AddHandler Me.bAjouter.Click, AddressOf Me.bAjouter_Click


            ProgressBar.Value = 100
            ProgressBar.Visible = False
            GroupeJauge.Visible = False

            cmbType.Text = "GROUPEE"
            cmbType.Enabled = False
            CalculerMontants()

        End If

        With gArticles
            .Columns.Clear()
            .DataSource = dsCommande
            .DataMember = "COMMANDE_DETAILS_PROJET"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qté"
            .Columns("Stock").Caption = "Stock"
            .Columns("DatePeremption").Caption = "Date.Pérem"
            .Columns("PrixAchatHT").Caption = "Prix A HT "
            .Columns("TVA").Caption = "TVA"
            .Columns("TotalTTCAchat").Caption = "Total TTC "
            .Columns("StockAlerte").Caption = "S Alerte"
            .Columns("EnCours").Caption = "Encours "
            .Columns("QteACommander").Caption = "Qte / Jour"
            .Columns("QteUnitaire").Caption = "Q unit"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next
            .Splits(0).DisplayColumns("Fournisseur").Locked = False
            .Splits(0).DisplayColumns("CodeArticle").Locked = False
            .Splits(0).DisplayColumns("Designation").Locked = False
            .Splits(0).DisplayColumns("Qte").Locked = False

            .Splits(0).DisplayColumns("Fournisseur").Width = 80
            .Splits(0).DisplayColumns("NumeroCommande").Width = 0
            .Splits(0).DisplayColumns("NumeroCommande").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 50
            .Splits(0).DisplayColumns("Designation").Width = 240
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("Stock").Width = 50
            .Splits(0).DisplayColumns("DatePeremption").Width = 100
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 90
            .Splits(0).DisplayColumns("TVA").Width = 90
            .Splits(0).DisplayColumns("TotalTTCAchat").Width = 90
            .Splits(0).DisplayColumns("StockAlerte").Width = 50
            .Splits(0).DisplayColumns("EnCours").Width = 90
            .Splits(0).DisplayColumns("QteACommander").Width = 40
            .Splits(0).DisplayColumns("QteUnitaire").Width = 50
            .Splits(0).DisplayColumns("CodeForme").Visible = False
            .Splits(0).DisplayColumns("NumeroCommande").Visible = False

            'coloriage de la liste 
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.BackColor = Color.FromArgb(250, 250, 200)
            Next

            .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(250, 250, 200)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(210, 240, 255)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(210, 240, 255)
            .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("PrixAchatHT").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("TVA").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("TotalTTCAchat").Style.BackColor = Color.FromArgb(210, 240, 255)

            .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTTCAchat").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True

        End With

        Me.tdbdFournisseur.DataSource = dsChargementCommande
        Me.tdbdFournisseur.DataMember = "FOURNISSEUR"
        Me.gArticles.Columns("Fournisseur").DropDown = Me.tdbdFournisseur
        Me.tdbdFournisseur.Columns("CodeFournisseur").Caption = "Code Fournisseur"
        Me.tdbdFournisseur.Columns("NomFournisseur").Caption = "Nom Fournisseur"

        'Me.tdbdFournisseur.ValueMember = "CodeFournisseur"
        Me.tdbdFournisseur.DisplayMember = "NomFournisseur"
        Me.tdbdFournisseur.DisplayColumns("CodeFournisseur").Width = 0
        Me.tdbdFournisseur.DisplayColumns("NomFournisseur").Width = 100

        Me.gArticles.Splits(0).DisplayColumns(2).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(5).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(7).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(8).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(9).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(10).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(11).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(12).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(13).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(14).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(15).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(16).AllowFocus = False

        '---------------------------------------- élemination des lignes vide ou qte =0 
        If TypeCommande <> "RIEN" And TypeCommande <> "FRIGO" Then

            i = 0
            Do While i < dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count
                If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("CodeArticle") = "" Or dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("Qte").ToString <= "0" Then
                    dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Delete()
                    i = 0
                Else
                    i = i + 1
                End If
            Loop

            ' cas ou l'utilisateur a choisi un type de commande 
            NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS_PROJET").NewRow()
            NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
            NouvelArticle("CodeArticle") = ""
            dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Add(NouvelArticle)

        End If

        AfficherStatistique(gArticles(gArticles.Row, "CodeArticle"))
        CalculerMontants()

        gArticles.Focus()
        gArticles.Col = 3
        gArticles.Row = gArticles.RowCount - 1
        gArticles.EditActive = True

        '****************************************************************************************
        '********************** verouillage des lignes déja confirmées **************************
        '****************************************************************************************
        If gArticles.Row <> dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 Then
            gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        ElseIf gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 Then
            If gArticles(gArticles.Row, "CodeArticle") = "" Then
                gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = False
            End If
            If gArticles(gArticles.Row, "Designation") = "" Then
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
            End If
        End If

    End Sub
    Public Function CalculerEnCours(ByVal CodeArticle)

        Dim QteEnCours As Integer = 0

        StrSQL = " SELECT SUM(Qte) FROM COMMANDE_DETAILS,COMMANDE WHERE  CodeArticle ='" + _
                 CodeArticle + "' AND COMMANDE .NumeroCommande =COMMANDE_DETAILS .NumeroCommande " + _
                 "AND COMMANDE .NumeroFacture is null"

        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            QteEnCours = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return QteEnCours
    End Function

    Private Sub gArticles_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gArticles.Change
        If (gArticles.Col = 4 And gArticles.Columns("Designation").Value <> "") Or gArticles.Col = 3 Then
            Dim StrSQL1 As String = ""
            Dim I As Integer
            Dim NbLigne As Integer
            NbLigne = gArticles.RowCount

            With gListeRecherche
                .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
                .Top = Me.gArticles.Top + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight
            End With

            Try
                dsCommande.Tables("ARTICLE").Clear()
            Catch ex As Exception

            End Try
            If gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 And gArticles.Col = 4 Then
                gListeRecherche.Visible = True
            Else
                gListeRecherche.Visible = False
            End If

            'chargement des articles qui sont mis en jeu
            If gArticles.Col = 4 Then
                If gArticles.Columns("Designation").Value.ToString.Length > 1 Then
                    If gArticles.Columns("Designation").Value.ToString.IndexOf(".") > 0 And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1)) Then

                        StrSQL1 = "SELECT CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC" + _
                                  " FROM ARTICLE " + _
                                  "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  "WHERE " + _
                                  "ltrim(str(PrixVenteTTC,10,3)) LIKE '" + _
                                  gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + "%' AND Supprime=0 ORDER BY PrixVenteTTC"
                    Else
                        StrSQL1 = "SELECT CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC" + _
                                  " FROM ARTICLE " + _
                                  "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  "WHERE  " + _
                                  "Designation LIKE '" + gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + _
                                  "%' AND Supprime=0 ORDER BY Designation"
                    End If
                Else
                    StrSQL1 = "SELECT CodeArticle," + _
                              "Designation," + _
                              "LibelleForme," + _
                              "PrixVenteTTC " + _
                              "FROM ARTICLE " + _
                              "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                              "WHERE " + _
                              " Designation LIKE '" + gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + _
                              "%' AND Supprime=0 ORDER BY Designation"
                End If
            ElseIf gArticles.Col = 3 Then
                StrSQL1 = "SELECT CodeArticle," + _
                          "Designation," + _
                          "LibelleForme," + _
                          "PrixVenteTTC " + _
                          "FROM ARTICLE " + _
                          "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                          "WHERE " + _
                          "CodeArticle LIKE '" + gArticles.Columns("CodeArticle").Value + _
                          "' AND Supprime=0 ORDER BY Designation"

            End If
            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL1
            daCommande = New SqlDataAdapter(cmdCommande)
            daCommande.Fill(dsCommande, "ARTICLE")

            If dsCommande.Tables("ARTICLE").Rows.Count > 0 Then
                dr = dsCommande.Tables("ARTICLE").Rows(0)
            End If

            With gListeRecherche
                .Columns.Clear()
                .DataSource = dsCommande
                .DataMember = "ARTICLE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code Article"
                .Columns("Designation").Caption = "Designation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("PrixVenteTTC").Caption = "Prix de vente"

                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' CentreR tous les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Visible = False
                Next

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("CodeArticle").Visible = True
                .Splits(0).DisplayColumns("Designation").Visible = True
                .Splits(0).DisplayColumns("LibelleForme").Visible = True
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("Designation").Width = 300
                .Splits(0).DisplayColumns("LibelleForme").Width = 100
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120

                '.Splits(0).DisplayColumns("Designation").Style.BackColor = Color.Aqua
                '.Splits(0).DisplayColumns("LibelleForme").Style.BackColor = Color.Aqua
                '.Splits(0).DisplayColumns("PrixVenteTTC").Style.BackColor = Color.Aqua

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 20
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
            End With
        End If
    End Sub
    Public Function RecupereNumero()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max([NumeroCommande]) FROM [COMMANDE]"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If
        Return ValeurRetour
    End Function

    Private Sub gArticles_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyDown
        If gArticles.Columns("Fournisseur").Value.ToString = "" Then
            GroupeSituationFournisseur.Visible = False
        End If
    End Sub

    Private Sub gArticles_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyUp
        Dim i As Integer = 0
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand

        Dim QuantiteAAjouter As Integer = 0

        Dim CodeArticleExisteDansLaListe As String = ""

        '****************************************************************************************
        '*********************** test si on est en mode saisi ou non* ***************************
        '****************************************************************************************


        If mode <> "Ajout" Then
            Exit Sub
        End If

        '-------------------- si f1 c'est afficher la fiche article ou la fenêtre de recherche multicritère
        If e.KeyCode = Keys.F1 And mode = "Ajout" And gArticles.Columns("CodeArticle").Value <> "" Then
            AfficherFicheArticle(gArticles.Columns("CodeArticle").Value, gArticles.Columns("Stock").Value, gArticles.Columns("Designation").Value)
            Exit Sub
        End If

        If e.KeyCode = Keys.F1 And mode = "Ajout" And gArticles.Columns("CodeArticle").Value = "" Then
            Dim RechercheMulticritere As New fRechercheArticleMultiCritere

            RechercheMulticritere.ShowDialog()

            CodeArticleRechercheMC = RechercheMulticritere.CodeArticleRecherche

            gArticles.MoveLast()
            gArticles.Col = 2
            gArticles.Columns("CodeArticle").Value = CodeArticleRechercheMC
            ChargerDetailArticle(CodeArticleRechercheMC)
            gArticles.Col = 3

            RechercheMulticritere.Close()
            RechercheMulticritere.Dispose()

        End If

        '***************************************************************************************
        '******************* éliminer les fournisseurs des lignes sans articles ****************
        For i = 0 To dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1
            If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("Fournisseur").ToString <> "" And dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("CodeArticle") = "" Then
                dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("Fournisseur") = ""
            End If
        Next

        '***************************************************************************************
        '**************** parcourir par les flèches pour voir les statistique des ventes *******
        '***************************************************************************************
        If gListeRecherche.Visible = False And (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down) Then
            AfficherStatistique(gArticles(gArticles.Row, "CodeArticle"))
            'Exit Sub
        End If

        '****************************************************************************************
        '****** suppression du dernier ligne vide si on a deux lignes au mm temps vide **********
        '****************************** cas ou on supprime dernier ligne ************************
        '****************************************************************************************

        If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 = 1 And gArticles(0, "CodeArticle") = "" And gArticles(1, "CodeArticle") = "" Then
            gArticles.MoveLast()
            gArticles.MovePrevious()
            gArticles.Delete()
        End If

        '****************************************************************************************
        '******* test du type de la valeur d'entrée dans la colonne quantité (numéric) **********
        '**************** test du  valeur d'entrée dans la colonne quantité < 0 à éliminer *************
        '****************************************************************************************

        If gArticles.Col = 6 Then
            ' à éliminer la signe - pour les montants négatives 
            If e.KeyCode = Keys.D6 Or e.KeyCode = Keys.Subtract Then
                gArticles.Columns("Qte").Value = ""
                gArticles.Col = 5
                gArticles.EditActive = True
                Exit Sub
            End If

            If gArticles.Columns("Qte").Value.ToString <> "-" Then   ' pour les quantité négatives
                If IsNumeric(gArticles.Columns("Qte").Value) = False Then
                    gArticles.Columns("Qte").Value = ""
                    gArticles.Col = 6
                    gArticles.EditActive = True
                    Exit Sub
                End If
                If (gArticles.Columns("Qte").Value = 0) And e.KeyCode = Keys.Enter Then
                    gArticles.Columns("Qte").Value = ""
                    gArticles.Col = 6
                    gArticles.EditActive = True
                    Exit Sub
                End If
                If (gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999) And e.KeyCode = Keys.Enter Then
                    gArticles.Columns("Qte").Value = "1"
                    MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Col = 6
                    gArticles.EditActive = True
                    Exit Sub
                End If
            Else
                gArticles.Columns("Qte").Value = ""
                gArticles.Col = 6
                gArticles.EditActive = True

            End If

        End If

        '****************************************************************************************
        '************************************** recherche par code*******************************
        '****************************************************************************************

        If gArticles.Col = 3 And e.KeyCode = Keys.Enter And gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 Then
            ChargerDetailArticle(gArticles.Columns("CodeABarre").Value.ToString)
            Exit Sub
        ElseIf gArticles.Col = 3 And e.KeyCode = Keys.Enter And gArticles.Row < dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 Then
            gArticles.Col = 4
        End If

        '****************************************************************************************
        '********* masquer la liste de recherche si la designation est vide *********************
        '****************************************************************************************

        If e.KeyCode = Keys.Back And gArticles.Columns("Designation").Value = "" And gArticles.Col = 4 Then
            gListeRecherche.Visible = False
        End If

        '****************************************************************************************
        '********* pour passer à la navigation dans la petite liste de recherhce ****************
        '****************************************************************************************

        If (e.KeyCode = Keys.Down Or e.KeyCode = Keys.Up) And gListeRecherche.Visible = True Then
            gListeRecherche.Focus()
            gListeRecherche.Col = 2
            gListeRecherche.Row = 1
        End If

        '****************************************************************************************
        '******* si l'artcile n'existe pas dans la liste de recherhce sa quantité doit etre 0 ***
        '****************************************************************************************

        If dsCommande.Tables("ARTICLE").Rows.Count <= 0 And gListeRecherche.Visible = True Then
            gArticles.Columns("Qte").Value = 0
            gArticles.Col = 4
        End If

        '****************************************************************************************
        '*********************************** calcul des montants ********************************
        '****************************************************************************************

        If (gArticles.Col = 6) And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) Then
            CalculerMontants()
        End If

        '****************************************************************************************
        '********************** verouillage des lignes déja confirmées **************************
        '****************************************************************************************
        If (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) And gArticles.Row <> dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        ElseIf gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 Then
            If gArticles(gArticles.Row, "CodeArticle") = "" Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
            End If
            If gArticles(gArticles.Row, "Designation") = "" Then
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
            End If
        End If
        If gArticles.Col = 2 And gArticles.Columns("Designation").Value <> "" Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        End If
        If gArticles.Col = 3 And gArticles.Columns("CodeArticle").Value <> "" Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        End If

        '****************************************************************************************
        '*************** traitement du clique sur le bouton ENTREE selon la colonne *************
        '****************************************************************************************

        If e.KeyCode = Keys.Enter Then ' And (dsCommande.Tables("ARTICLE").Rows.Count > 0 Or gArticles.Row <> dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1) Then
            gListeRecherche_KeyUp(sender, e)     ' pour charger les informations de l'article a partir du fiche article

            If gArticles.Col = 4 Then
                If gArticles.Columns("CodeArticle").Value = "" Then    ' si l'article choisit n'existe pas dans la liste de recherche
                    gArticles.Col = 4
                    gArticles.Columns("Designation").Value = ""
                Else
                    gArticles.Col = 6
                End If

            ElseIf gArticles.Col = 6 And e.KeyCode = Keys.Enter Then
                ' si l'utilisateur a choisit un article on ajoute un nouvel enregistrement dans la datatable puis on passe à la ligne
                ' suivante dans la gride avec un test si cet article est déja choisi ou non si c'est le cas on ajoute seulement la quntité
                i = 0
                If gArticles.Columns("Designation").Value <> "" Then
                    QuantiteAAjouter = gArticles.Columns("Qte").Value
                    CodeArticleExisteDansLaListe = gArticles.Columns("CodeArticle").Value
                    If gArticles.RowCount >= 1 And gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 Then
                        Do While i < gArticles.RowCount - 1
                            If gArticles(i, "CodeArticle") = CodeArticleExisteDansLaListe Then
                                If MsgBox("Article déja saisi, voulez vous ajouter la quantité ou effacer l'article ? Oui pour ajouter la quantité", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                                    gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QuantiteAAjouter).ToString
                                    gArticles.MoveLast()
                                    gArticles.Delete()
                                Else
                                    gArticles.MoveLast()
                                    gArticles.Delete()
                                End If

                            End If
                            i = i + 1
                        Loop
                    End If
                End If


                '###########################################################################

                'Dim k As Integer = 0
                'Dim j As Integer = 0
                'Dim L As Integer = 0


                'Do While j < gArticles.RowCount

                '    For k = j To dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1

                '        If gArticles(j, "CodeArticle") = gArticles(k, "CodeArticle") And j <> k Then
                '            'gArticles(j, "Qte") = (Convert.ToInt32(gArticles(j, "Qte")) + (Convert.ToInt32(gArticles(k, "Qte")))).ToString
                '            gArticles(j, "Qte") = CDbl(Val(gArticles(j, "Qte").ToString)) + CDbl(Val(gArticles(k, "Qte").ToString))

                '            gArticles(k, "CodeArticle") = ""
                '            gArticles(k, "CodeABarre") = ""
                '            gArticles(k, "Designation") = ""
                '            gArticles(k, "LibelleForme") = ""
                '            gArticles(k, "Qte") = ""
                '            gArticles(k, "Stock") = ""
                '            gArticles(k, "DatePeremption") = ""
                '            gArticles(k, "PrixAchatHT") = ""
                '            gArticles(k, "TVA") = ""
                '            gArticles(k, "TotalTTCAchat") = ""
                '            gArticles(k, "StockAlerte") = ""
                '            gArticles(k, "EnCours") = ""
                '            gArticles(k, "QteACommander") = ""
                '            gArticles(k, "QteUnitaire") = ""

                '        End If
                '    Next

                '    i = 0
                '    Do While i < dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count

                '        If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).RowState <> DataRowState.Deleted Then

                '            If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("CodeArticle") = "" Then
                '                dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Delete()

                '            End If

                '        End If
                '        i = i + 1
                '    Loop

                '    j = j + 1
                'Loop

                '############################################################################

                If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                    NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS_PROJET").NewRow()
                    NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                    NouvelArticle("CodeArticle") = ""
                    NouvelArticle("CodeABarre") = ""
                    NouvelArticle("Fournisseur") = ""
                    dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Add(NouvelArticle)
                End If
                gArticles.MoveLast()
                Try
                    dsCommande.Tables("ARTICLE").Clear()
                Catch ex As Exception
                End Try
                gArticles.Col = 3
                'gArticles.EditActive = True

            End If
        End If

    End Sub

    'procedure pour grouper les lignes repeteés plusieurs fois
    Private Sub GrouperLigne()
        '------------------------------------------------------------------------------------------------------------------------------------------------
        Dim k As Integer = 0
        Dim j As Integer = 0
        Dim i As Integer = 0



        Do While j < gArticles.RowCount

            For k = j To dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1

                If gArticles(j, "CodeArticle") = gArticles(k, "CodeArticle") And j <> k Then
                    gArticles(j, "Qte") = CDbl(Val(gArticles(j, "Qte").ToString)) + CDbl(Val(gArticles(k, "Qte").ToString))

                    gArticles(k, "CodeArticle") = ""
                    gArticles(k, "CodeABarre") = ""
                    gArticles(k, "Designation") = ""
                    gArticles(k, "LibelleForme") = ""
                    gArticles(k, "Qte") = ""
                    gArticles(k, "Stock") = ""
                    gArticles(k, "DatePeremption") = ""
                    gArticles(k, "PrixAchatHT") = ""
                    gArticles(k, "TVA") = ""
                    gArticles(k, "TotalTTCAchat") = ""
                    gArticles(k, "StockAlerte") = ""
                    gArticles(k, "EnCours") = ""
                    gArticles(k, "QteACommander") = ""
                    gArticles(k, "QteUnitaire") = ""

                End If
            Next

            i = 0
            Do While i < dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count

                If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).RowState <> DataRowState.Deleted Then

                    If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("CodeArticle") = "" Then
                        dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Delete()

                    End If

                End If
                i = i + 1
            Loop

            j = j + 1
        Loop

    End Sub

    Private Sub gListeRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListeRecherche.KeyUp
        ' dans cet évènenment on teste si l évènenment est due à un clique sur le boutton entrée et si l'utilisateur a choisi 
        ' un article et si le curseur est dans la colonne de designation 

        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date

        Dim CategorieArticle As Integer = 0

        If gListeRecherche.Visible = False Then
            Exit Sub
        End If
        If e.KeyCode = Keys.Back Then
            gArticles.Focus()
            gArticles.Col = 4
            gArticles.MoveLast()
            gArticles.EditActive = True
        End If

        Dim j As Integer
        Dim NumeroLigne As Integer
        Dim DataRowRecherche As DataRow
        If e.KeyCode = Keys.Enter And (gArticles.Col = 6 Or gArticles.Col = 4) Then    'And gArticles.Columns("Designation").Value <> ""
            If dsCommande.Tables("ARTICLE").Rows.Count > 0 Then

                '--------------------- rehcerche de l'index de la ligne qui contient l'article recherché ---------------

                For j = 0 To dsCommande.Tables("ARTICLE").Rows.Count - 1
                    DataRowRecherche = dsCommande.Tables("ARTICLE").Rows(j)
                    If DataRowRecherche.Item("CodeArticle") = gListeRecherche.Columns("CodeArticle").Value Then
                        NumeroLigne = j
                    End If
                Next

                '------------------- chargement des données ---------------------------------------------- 

                dr = dsCommande.Tables("ARTICLE").Rows(NumeroLigne)
                NouvelArticle("NumeroCommande") = RecupereNumero()

                '---------------------- les préparations ne sont pas autorisées
                CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                If CategorieArticle = 9 Then
                    MsgBox("Saisie des préparations interdite !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Columns("CodeArticle").Value = ""
                    gArticles.Columns("CodeABarre").Value = ""
                    gArticles.Columns("Designation").Value = ""
                    gArticles.Col = 2
                    gArticles.EditActive = True
                    Exit Sub
                End If

                NouvelArticle("CodeArticle") = dr.Item("CodeArticle")
                NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                Try
                    NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
                Catch ex As Exception
                End Try
                NouvelArticle("Qte") = 1
                NouvelArticle("Stock") = CalculeStock(dr.Item("CodeArticle"))
                NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")

                NouvelArticle("StockAlerte") = CDbl(Val(RecupererValeurExecuteScalaire("StockAlerte", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))))
                'NouvelArticle("EnCours") = RecupererValeurExecuteScalaire("StockAlerte", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("QteACommander") = RecupererValeurExecuteScalaire("QteACommander", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("QteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))

                '------------------ récupération de la date de péremption

                StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
                System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
                dr.Item("CodeArticle") + "' Order by DatePeremptionArticle ASC "

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    DatePeremption = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If DatePeremption = #12:00:00 AM# Then
                    'NouvelArticle("DatePeremption") = "01/01/1900"
                Else
                    NouvelArticle("DatePeremption") = DatePeremption
                End If

                gArticles.Refresh()
            End If
            gListeRecherche.Visible = False
            gArticles.Focus()

            If NumeroLigne = 0 Then
                gArticles.Col = 4
            Else
                gArticles.Col = 6
            End If
        End If
    End Sub

    Public Sub ChargerDetailArticle(ByVal CodeABarre As String)
        Dim resultat As String = ""
        Dim Supprime As String = ""
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date
        Dim CodeArticle As String = ""

        Dim CategorieArticle As Integer = 0

        CodeArticle = RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "CodeABarre", CodeABarre)
        resultat = RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle)
        Supprime = RecupererValeurExecuteScalaire("Supprime", "ARTICLE", "CodeArticle", CodeArticle)

        If resultat <> "" And Supprime = "False" Then

            CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", CodeArticle)
            If CategorieArticle = 9 Then
                MsgBox("Saisie des préparations interdite !", MsgBoxStyle.Critical, "Erreur")
                gArticles.Columns("CodeArticle").Value = ""
                gArticles.Columns("CodeABarre").Value = ""
                gArticles.Col = 3
                Exit Sub
            End If

            NouvelArticle("NumeroCommande") = RecupereNumero()
            NouvelArticle("CodeArticle") = CodeArticle
            NouvelArticle("CodeABarre") = CodeABarre
            NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)
            Try
                NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
            Catch ex As Exception
            End Try

            NouvelArticle("Qte") = 1
            NouvelArticle("Stock") = CalculeStock(CodeArticle)
            NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", CodeArticle)
            NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", CodeArticle)
            NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
            NouvelArticle("StockAlerte") = RecupererValeurExecuteScalaire("StockAlerte", " ARTICLE", "CodeArticle", CodeArticle)
            'NouvelArticle("EnCours") = RecupererValeurExecuteScalaire("StockAlerte", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("QteACommander") = RecupererValeurExecuteScalaire("QteACommander", " ARTICLE", "CodeArticle", CodeArticle)
            NouvelArticle("QteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", CodeArticle)

            '----------------------- récupération de la date de péremption

            StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
            System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
            CodeArticle + "'  Order by DatePeremptionArticle ASC "

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                DatePeremption = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If DatePeremption = #12:00:00 AM# Then
                ' NouvelArticle("DatePeremption") = "01/01/1900"
            Else
                NouvelArticle("DatePeremption") = DatePeremption
            End If

            gArticles.Refresh()
            gArticles.Col = 6
            gArticles.EditActive = True
            gArticles.Focus()
        Else
            gArticles.Columns("CodeABarre").Value = ""

            gArticles.Col = 4
            gArticles.EditActive = True
        End If
    End Sub
    Public Sub CalculerMontants()
        Dim i As Integer = 0
        TotalTTCAchat = 0.0
        TotalHTAchat = 0.0
        TotalTVA = 0.0

        Do While i < gArticles.RowCount
            If gArticles(i, "Designation") <> "" And gArticles(i, "Qte").ToString <> "" Then
                gArticles(i, "TotalTTCAchat") = Math.Round(gArticles(i, "PrixAchatHT") * gArticles(i, "Qte") * (100 + gArticles(i, "TVA")) / 100, 3)
                TotalHTAchat = Math.Round(TotalHTAchat + gArticles(i, "PrixAchatHT") * gArticles(i, "Qte"), 3)
                TotalTTCAchat = TotalTTCAchat + gArticles(i, "TotalTTCAchat")
                TotalTVA = TotalTVA + Math.Round(gArticles(i, "PrixAchatHT") * gArticles(i, "TVA") / 100 * gArticles(i, "Qte"), 3)

            End If
            i = i + 1
        Loop

        lTotHTAchat.Text = TotalHTAchat
        lTotalTTCAchat.Text = TotalTTCAchat.ToString
        lTotalTVA.Text = TotalTVA.ToString

    End Sub
    'Public Function CalculeStock(ByVal CodeArticle)
    '    '--------------------------------------- pour calculer le stock 
    '    '--------------------------------------- qui sera affiché dans la liste 
    '    Dim StrSQLStock As String = ""
    '    Dim CmdCalcul As New SqlCommand
    '    Dim StockArticle1 As Double = 0.0
    '    Dim StockArticle2 As Double = 0.0

    '    'calcul du stock article dans les differents lots

    '    StrSQLStock = "SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE CodeArticle =" + Quote(CodeArticle) + " AND DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + "'"

    '    CmdCalcul.Connection = ConnectionServeur
    '    CmdCalcul.CommandText = StrSQLStock

    '    Try
    '        StockArticle1 = CmdCalcul.ExecuteScalar()
    '    Catch ex As Exception
    '        Console.WriteLine(ex.Message)
    '    End Try

    '    StrSQLStock = "SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE CodeArticle =" + Quote(CodeArticle) + " AND DatePeremptionArticle  ='01/01/1900'"
    '    CmdCalcul.Connection = ConnectionServeur
    '    CmdCalcul.CommandText = StrSQLStock
    '    Try
    '        StockArticle2 = CmdCalcul.ExecuteScalar()
    '    Catch ex As Exception
    '        Console.WriteLine(ex.Message)
    '    End Try

    '    Return (Convert.ToSingle(StockArticle1 + StockArticle2))
    'End Function

    Private Sub bFournisseur_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFournisseur.Click
        'cmbFournisseur.Focus()
    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        Dim I As Integer = 0
        Dim J As Integer = 0
        Dim k As Integer = 0

        Dim cmd As New SqlCommand
        Dim NumeroLot As Integer = 0
        Dim TestNumeroLot As String = ""
        Dim NouveauNumeroLot As String = ""

        Dim QuantiteLotSansNumero As Integer = 0
        Dim QuantiteLotAInsere As Integer = 0

        Dim StrSQL As String = ""

        Dim NouveauDetail As DataRow = Nothing
        Dim NouvelleEntete As DataRow = Nothing

        Dim TotalHTAchatCommande As Double = 0.0
        Dim TotalTTCAchatCommande As Double = 0.0
        Dim lTotalTVACommande As Double = 0.0

        ''-------------appel de la Procedure GrouperLigne pour regrouper les articles qui existe plusieurs fois
        'GrouperLigne()


        '---------------------------------------- contrôle si la liste est vide 

        If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle") = "" Then
            MsgBox("Commandes Vides !", MsgBoxStyle.Critical, "Erreur")
            If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 < 0 Then
                bAjouter_Click(sender, e)
            End If
            '----------------------
            gArticles.Col = 2
            gArticles.EditActive = True
            Exit Sub
        End If

        '****************************************************************************************
        '************************* Création des commandes selon le nombre des fournisseurs ******
        '****************************************************************************************
        Try
            dsCommande.Tables("FournisseurMisEnJeu").Clear()
        Catch ex As Exception
            Dim FournisseurMisEnJeu As New DataTable("FournisseurMisEnJeu")
            Dim NomFournisseurMisEnJeu As New DataColumn("NomFournisseurMisEnJeu")
            NomFournisseurMisEnJeu.DataType = GetType(System.String)
            FournisseurMisEnJeu.Columns.Add(NomFournisseurMisEnJeu)
            dsCommande.Tables.Add(FournisseurMisEnJeu)
        End Try

        NouveauFournisseur = dsCommande.Tables("FournisseurMisEnJeu").NewRow()
        dsCommande.Tables("FournisseurMisEnJeu").Rows.Add(NouveauFournisseur)
        NouveauFournisseur("NomFournisseurMisEnJeu") = ""

        '----------------------- controle si il ya un article sans fournisseur
        For J = 0 To dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1
            If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("Designation") <> "" Then
                If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("Fournisseur") = "" Then
                    MsgBox("Article " + dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("Designation") + " sans fournisseur " + Chr(13) + "Veuillez l'affecter à un fournisseur !", MsgBoxStyle.Critical, "Erreur")
                    Exit Sub
                End If
            End If
        Next

        '----------------------- récupération des fournisseurs 
        Dim Trouve As Boolean = False
        For J = 0 To dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1
            If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("Designation") <> "" Then
                Trouve = False
                For k = 0 To dsCommande.Tables("FournisseurMisEnJeu").Rows.Count - 1
                    If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("Fournisseur") = dsCommande.Tables("FournisseurMisEnJeu").Rows(k).Item("NomFournisseurMisEnJeu") Then
                        Trouve = True
                    End If
                Next
                If Trouve = False Then
                    NouveauFournisseur = dsCommande.Tables("FournisseurMisEnJeu").NewRow()
                    dsCommande.Tables("FournisseurMisEnJeu").Rows.Add(NouveauFournisseur)
                    NouveauFournisseur.Item("NomFournisseurMisEnJeu") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("Fournisseur")
                End If
            End If
        Next
        '----------------------- suppression des lignes vide dans la datatable 
        I = 0
        Do While I < dsCommande.Tables("FournisseurMisEnJeu").Rows.Count
            If dsCommande.Tables("FournisseurMisEnJeu").Rows(I).Item("NomFournisseurMisEnJeu") = "" Then
                dsCommande.Tables("FournisseurMisEnJeu").Rows(I).Delete()
            End If
            I = I + 1
        Loop

        I = 0
        Do While I < dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count
            If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(I).Item("CodeArticle") = "" Then
                dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(I).Delete()
            End If
            I = I + 1
        Loop

        '------------------------------ demande du mot de passe

        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        Dim myMotDePasse As New fMotDePasse
        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If

        '-------------appel de la Procedure GrouperLigne pour regrouper les articles qui existent plusieurs fois
        GrouperLigne()
        '----------------------- préparation des datatables entête et détail pour chaque commande 
        '----------------------- et enregistrement des table 

        I = 0
        J = 0
        For I = 0 To dsCommande.Tables("FournisseurMisEnJeu").Rows.Count - 1

            NumeroCommande = RecupereNumero()

            '----------------------------- chargement des Entêtes des Commandes 
            Try
                dsCommande.Tables("COMMANDE").Clear()
            Catch ex As Exception

            End Try

            StrSQL = "SELECT top (0) * FROM COMMANDE ORDER BY NumeroCommande ASC"
            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL
            daCommande = New SqlDataAdapter(cmdCommande)
            daCommande.Fill(dsCommande, "COMMANDE")
            cbCommande = New SqlCommandBuilder(daCommande)
            NouvelleEntete = dsCommande.Tables("COMMANDE").NewRow()
            '-------------- calcule des montants
            For J = 0 To dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1
                If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("Fournisseur") = dsCommande.Tables("FournisseurMisEnJeu").Rows(I).Item("NomFournisseurMisEnJeu") Then
                    TotalHTAchatCommande = Math.Round(TotalHTAchatCommande + CDbl(Val(dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("PrixAchatHT").ToString)) * CDbl(Val(dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("Qte").ToString)), 3)
                    TotalTTCAchatCommande = TotalTTCAchatCommande + CDbl(Val(dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("TotalTTCAchat").ToString))
                    lTotalTVACommande = lTotalTVACommande + CDbl(Val(dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("PrixAchatHT").ToString)) * CDbl(Val(dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("TVA").ToString)) / 100 * CDbl(Val(dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("Qte").ToString))
                End If
            Next
            '------------- chargement de l'entête 
            NouvelleEntete.Item("NumeroCommande") = NumeroCommande
            NouvelleEntete.Item("Date") = System.DateTime.Now
            NouvelleEntete.Item("TotalHT") = TotalHTAchatCommande
            NouvelleEntete.Item("TotalTTC") = TotalTTCAchatCommande
            NouvelleEntete.Item("TotalTVA") = lTotalTVACommande
            NouvelleEntete.Item("LibellePoste") = System.Environment.GetEnvironmentVariable("Poste")
            NouvelleEntete.Item("CodePersonnel") = CodeOperateur
            NouvelleEntete.Item("CodeFournisseur") = RecupererValeurExecuteScalaire("CodeFournisseur", "FOURNISSEUR", "NomFournisseur", dsCommande.Tables("FournisseurMisEnJeu").Rows(I).Item("NomFournisseurMisEnJeu"))
            NouvelleEntete.Item("Note") = "rien"

            dsCommande.Tables("COMMANDE").Rows.Add(NouvelleEntete)

            '------------------------------- enregistrement de l'entête 
            Try
                daCommande.Update(dsCommande, "COMMANDE")
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                dsCommande.Reset()
                Exit Sub
            End Try
            '----------------------------- chargement des détails des Commandes 

            StrSQL = "SELECT NumeroCommande," + _
                     "CodeArticle," + _
                     "CodeABarre," + _
                     "Designation," + _
                     "CodeForme," + _
                     "Qte," + _
                     "Stock," + _
                     "DatePeremption," + _
                     "PrixAchatHT," + _
                     "TVA," + _
                     "TotalTTCAchat," + _
                     "StockAlerte," + _
                     "EnCours," + _
                     "QteACommander," + _
                     "QteUnitaire " + _
                     "FROM COMMANDE_DETAILS " + _
                     "WHERE NumeroCommande ='0'"

            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL
            daCommande.Fill(dsCommande, "COMMANDE_DETAILS")
            cbCommande = New SqlCommandBuilder(daCommande)

            For J = 0 To dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1
                If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("Fournisseur") = dsCommande.Tables("FournisseurMisEnJeu").Rows(I).Item("NomFournisseurMisEnJeu") Then
                    NouveauDetail = dsCommande.Tables("COMMANDE_DETAILS").NewRow()

                    dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouveauDetail)

                    NouveauDetail.Item("NumeroCommande") = NumeroCommande
                    NouveauDetail.Item("CodeArticle") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("CodeArticle")
                    NouveauDetail.Item("CodeABarre") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("CodeABarre")
                    NouveauDetail.Item("Designation") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("Designation")
                    NouveauDetail.Item("CodeForme") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("CodeForme")
                    NouveauDetail.Item("Qte") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("Qte")
                    NouveauDetail.Item("Stock") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("Stock")
                    NouveauDetail.Item("DatePeremption") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("DatePeremption")
                    NouveauDetail.Item("PrixAchatHT") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("PrixAchatHT")
                    NouveauDetail.Item("TVA") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("TVA")
                    NouveauDetail.Item("TotalTTCAchat") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("TotalTTCAchat")
                    NouveauDetail.Item("StockAlerte") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("StockAlerte")
                    NouveauDetail.Item("EnCours") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("EnCours")
                    NouveauDetail.Item("QteACommander") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("QteACommander")
                    NouveauDetail.Item("QteUnitaire") = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(J).Item("QteUnitaire")

                    TotalHTAchatCommande = Math.Round(TotalHTAchatCommande + CDbl(Val(NouveauDetail.Item("PrixAchatHT").ToString)) * CDbl(Val(NouveauDetail.Item("Qte").ToString)), 3)
                    TotalTTCAchatCommande = TotalTTCAchatCommande + CDbl(Val(NouveauDetail.Item("TotalTTCAchat").ToString))
                    lTotalTVACommande = lTotalTVACommande + CDbl(Val(NouveauDetail.Item("PrixAchatHT").ToString)) * CDbl(Val(NouveauDetail.Item("TVA").ToString)) / 100 * CDbl(Val(NouveauDetail.Item("Qte").ToString))

                End If

            Next

            '------------------------------- enregistrement des détails 
            Try
                daCommande.Update(dsCommande, "COMMANDE_DETAILS")
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                dsCommande.Reset()
            End Try

        Next

        mode = "Consultation"
        bAnnuler.Enabled = False
        bConfirmer.Enabled = False
        bFournisseur.Enabled = False
        bSupprimer.Enabled = True
        bAjouter.Enabled = True
        fMain.Tab.SelectedTab.Dispose()

    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click

        If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 > 0 And gArticles(0, "CodeArticle") <> "" Then
            If MsgBox("Voulez vous vraiment annuler cette commande ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Annuler Commande") = MsgBoxResult.No Then
                Exit Sub
            End If
        End If

        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub lTotalTTC_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotalTTCAchat.TextChanged
        lTotalTTCAchat.Text = lTotalTTCAchat.Text
        If lTotalTTCAchat.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lTotalTTCAchat.Text, ".")
            If lTotalTTCAchat.Text.Length - x = 1 Then
                lTotalTTCAchat.Text = lTotalTTCAchat.Text + ("00")
            ElseIf lTotalTTCAchat.Text.Length - x = 2 Then
                lTotalTTCAchat.Text = lTotalTTCAchat.Text + ("0")
            End If
        Else
            lTotalTTCAchat.Text = lTotalTTCAchat.Text + ".000"
        End If
    End Sub

    Private Sub lTotHT_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotHTAchat.TextChanged
        lTotHTAchat.Text = lTotHTAchat.Text
        If lTotHTAchat.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lTotHTAchat.Text, ".")
            If lTotHTAchat.Text.Length - x = 1 Then
                lTotHTAchat.Text = lTotHTAchat.Text + ("00")
            ElseIf lTotHTAchat.Text.Length - x = 2 Then
                lTotHTAchat.Text = lTotHTAchat.Text + ("0")
            End If
        Else
            lTotHTAchat.Text = lTotHTAchat.Text + ".000"
        End If
    End Sub

    Private Sub lTotalTVA_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotalTVA.TextChanged
        lTotalTVA.Text = lTotalTVA.Text
        If lTotalTVA.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lTotalTVA.Text, ".")
            If lTotalTVA.Text.Length - x = 1 Then
                lTotalTVA.Text = lTotalTVA.Text + ("00")
            ElseIf lTotalTVA.Text.Length - x = 2 Then
                lTotalTVA.Text = lTotalTVA.Text + ("0")
            End If
        Else
            lTotalTVA.Text = lTotalTVA.Text + ".000"
        End If
    End Sub

    Private Sub fCommande_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        With cmbType
            .HoldFields()
            .AddItem("Journalière")
            .AddItem("Groupée")
        End With
    End Sub

    Private Sub Label5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Label5.Click
        MessageBox.Show(gArticles.Columns("Fournisseur").Value.ToString)
        MessageBox.Show(tdbdFournisseur.Columns("NomFournisseur").Value)
        gArticles.Columns("Fournisseur").Value = tdbdFournisseur.Columns("NomFournisseur").Value
        MessageBox.Show(gArticles.Columns("Fournisseur").Value.ToString)
    End Sub

    Private Sub tdbdFournisseur_SelChange(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.CancelEventArgs) Handles tdbdFournisseur.SelChange
        AfficherSituationFournisseur(tdbdFournisseur.Columns("NomFournisseur").Value)
    End Sub

    Private Sub tdbdFournisseur_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles tdbdFournisseur.TextChanged

    End Sub

    Private Sub gArticles_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gArticles.MouseClick
        Dim i As Integer = 0
        If gArticles.Columns("Fournisseur").Value.ToString = "" Then
            GroupeSituationFournisseur.Visible = False
        Else
            GroupeSituationFournisseur.Visible = True
            AfficherSituationFournisseur(gArticles.Columns("Fournisseur").Value.ToString)
        End If

        AfficherStatistique(gArticles(gArticles.Row, "CodeArticle"))

        '***************************************************************************************
        '******************* éliminer les fournisseurs des lignes sans articles ****************
        For i = 0 To dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1
            If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("Fournisseur").ToString <> "" And dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("CodeArticle") = "" Then
                dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("Fournisseur") = ""
            End If
        Next

        '****************************************************************************************
        '********************** verouillage des lignes déja confirmées **************************
        '****************************************************************************************
        If gArticles.Row <> dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        ElseIf gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 Then
            If gArticles(gArticles.Row, "CodeArticle") = "" Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
            End If
            If gArticles(gArticles.Row, "Designation") = "" Then
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
            End If
        End If

        If gArticles.Col = 2 And gArticles.Columns("Designation").Value <> "" Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        End If
        If gArticles.Col = 3 And gArticles.Columns("CodeArticle").Value <> "" Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        End If

    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click
        If gArticles.RowCount > 0 Then
            'Test si la lign est NEW ADDED et elle est vide
            If gArticles(gArticles.Row, ("CodeArticle")) <> "" Then
                gArticles.Delete()
                CalculerMontants()
            End If
        End If



        'If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 < 0 Then
        '    bAjouter_Click(sender, e)
        'End If


    End Sub
    Private Sub AfficherSituationFournisseur(ByVal ParametreNomFournisseur As String)
        If tdbdFournisseur.Columns("NomFournisseur").Value <> Nothing Then
            Dim StrSQLdernierAchat As String = ""
            Dim CmdCalcul As New SqlCommand
            Dim Dernier_Date_Achat As String = ""
            Dim Dernier_Date_Reglement As String = ""
            Dim StrSQLSolde As String = ""
            Dim Somme_Facture As Double = 0.0
            Dim Somme_Reglement As Double = 0.0
            Dim Somme_Echeance As Double = 0.0
            Dim difference As Double = 0.0
            Dim StrMatricule As String = ""
            Dim CodeFournisseur As String
            Dim i As Integer = 0
            Dim SommeDesMontantsDuProjet As Double = 0.0

            CodeFournisseur = RecupererValeurExecuteScalaire("CodeFournisseur", "FOURNISSEUR", "NomFournisseur", ParametreNomFournisseur)

            '---------- récupération de la dernière date d'achat pour le Fournisseur concerné 
            StrSQLdernierAchat = "SELECT MAX(Date) FROM ACHAT WHERE CodeFournisseur =" + Quote(CodeFournisseur)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLdernierAchat

            Try
                Dernier_Date_Achat = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            '----------- récupération de la dernière date de règlement pour le Fournisseur concerné 
            StrSQLdernierAchat = "SELECT MAX(Date) FROM REGLEMENT_FOURNISSEUR WHERE CodeFournisseur =" + Quote(CodeFournisseur)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLdernierAchat

            Try
                Dernier_Date_Reglement = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            '-------- calcul du solde Fournisseur en retranchant la somme des montants des règlements de la somme des montants des ventes 
            StrSQLSolde = "SELECT SUM(TotalTTC) FROM ACHAT WHERE CodeFournisseur =" + Quote(CodeFournisseur)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLSolde

            Try
                Somme_Facture = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQLSolde = "SELECT SUM(Montant) FROM REGLEMENT_FOURNISSEUR WHERE CodeFournisseur =" + Quote(CodeFournisseur)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLSolde

            Try
                Somme_Reglement = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            '----------- récupération des montants des règlements antidaté nn encaissé
            StrSQLSolde = "SELECT SUM(Montant) FROM REGLEMENT_FOURNISSEUR,NATURE_REGLEMENT WHERE CodeFournisseur =" + _
            Quote(CodeFournisseur) + " AND DateEcheance > '" + System.DateTime.Now.Date.ToString + _
            "' AND NATURE_REGLEMENT.LibelleNatureReglement='CHEQUE' AND REGLEMENT_FOURNISSEUR.CodeNatureReglement=" + _
            " NATURE_REGLEMENT.CodeNatureReglement"

            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLSolde

            Try
                Somme_Echeance = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            '---------------------- calcul de la somme des commandes de ce projet de commande 
            'gArticles.Row = 0
            'While gArticles.Row < dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1
            '    If gArticles.Columns("Fournisseur").Value = ParametreNomFournisseur Then
            '        SommeDesMontantsDuProjet = SommeDesMontantsDuProjet + gArticles.Columns("TotalTTCAchat").Value
            '    End If
            '    gArticles.MoveNext()
            'End While

            'If gArticles.Columns("Fournisseur").Value = "" Then ' And gArticles.Columns("CodeArticle").Value <> "" 
            '    SommeDesMontantsDuProjet = gArticles.Columns("TotalTTCAchat").Value
            'End If

            For i = 0 To dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1
                If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("CodeArticle") <> "" Then
                    If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("Fournisseur") = ParametreNomFournisseur Then
                        SommeDesMontantsDuProjet = SommeDesMontantsDuProjet + CDbl(Val(dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("TotalTTCAchat").ToString))
                    End If
                    'If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("Fournisseur") <> "" And dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("CodeArticle") = "" Then
                    '    dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(i).Item("Fournisseur") = ""
                    'End If
                End If

            Next

            '------------------------------------------------------------------------------
            '-------------------------------- afféctation des valeurs ---------------------

            If (Dernier_Date_Achat) <> "" Then
                lDDA.Text = Dernier_Date_Achat
            Else
                lDDA.Text = "-"
            End If

            If (Dernier_Date_Reglement) <> "" Then
                lDDR.Text = Dernier_Date_Reglement
            Else
                lDDR.Text = "-"
            End If

            difference = Somme_Facture - Somme_Reglement
            lSoldeFournisseur.Text = Convert.ToString(difference)
            lNouvSolde.Text = CDbl(lSoldeFournisseur.Text) + SommeDesMontantsDuProjet '+ CDbl(lTotalTTC.Text)).ToString
            lSoldeEnCours.Text = Somme_Echeance.ToString

            GroupeSituationFournisseur.Visible = True
        Else
            GroupeSituationFournisseur.Visible = False
        End If
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub cmbFournisseur_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles cmbFournisseur.MouseClick
        Dim j As Integer = 0
        For j = 0 To dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 2
            dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(j).Item("Fournisseur") = cmbFournisseur.Text
        Next
    End Sub


    Private Sub cmbFournisseur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbFournisseur.TextChanged
        Dim j As Integer = 0
        For j = 0 To dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 2
            dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(j).Item("Fournisseur") = cmbFournisseur.Text
        Next
    End Sub

    Private Sub cmbFournisseur_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbFournisseur.Validated
        Dim j As Integer = 0
        For j = 0 To dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 2
            dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(j).Item("Fournisseur") = cmbFournisseur.Text
        Next
    End Sub

    Public Sub AfficherFicheArticle(ByVal CodeArticle, ByVal StockArticle, ByVal Designation)
        Dim MyFicheArticle As New fFicheArticle

        MyFicheArticle.CodeArticle = CodeArticle
        MyFicheArticle.StockArticle = StockArticle
        MyFicheArticle.DesignationArticle = Designation
        MyFicheArticle.ajoutmodif = "M"

        MyFicheArticle.Init()
        MyFicheArticle.ShowDialog()
        MyFicheArticle.Close()
        MyFicheArticle.Dispose()
    End Sub
    Private Sub AfficherStatistique(ByVal CodeArticle As String)

        Dim i As Integer = 0
        Dim mois As String = ""

        If (dsCommande.Tables.IndexOf("STATISTIQUE_ARTICLE") > -1) Then
            dsCommande.Tables("STATISTIQUE_ARTICLE").Clear()
        End If
        If (dsCommande.Tables.IndexOf("STATISTIQUE_ARTICLE_PREC") > -1) Then
            dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Clear()
        End If

        InitialiserStatistique()

        lAnDernier.Text = System.DateTime.Now.Year
        lAnAvantDernier.Text = DateAdd(DateInterval.Year, -1, System.DateTime.Now).Year

        '********************** récupération des valeurs de vente de 12 mois pour les 2 ans 

        StrSQL = "  SELECT MONTH (date) AS MOIS,SUM(Qte) AS QTE " + _
                 "  FROM VENTE_DETAILS  " + _
                 " LEFT OUTER JOIN VENTE ON VENTE .NumeroVente =VENTE_DETAILS .NumeroVente " + _
                 " WHERE CodeArticle='" + CodeArticle + _
                 "' AND YEAR (date)=YEAR(getdate())  GROUP BY MONTH (date)"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        daRecupereNum = New SqlDataAdapter(cmdRecupereNum)
        daRecupereNum.Fill(dsCommande, "STATISTIQUE_ARTICLE")


        StrSQL = "  SELECT MONTH (date) AS MOIS,SUM(Qte) AS QTE " + _
                 "  FROM VENTE_DETAILS  " + _
                 " LEFT OUTER JOIN VENTE ON VENTE .NumeroVente =VENTE_DETAILS .NumeroVente " + _
                 " WHERE CodeArticle='" + CodeArticle + _
                 "' AND YEAR (date)=YEAR(getdate())-1  GROUP BY MONTH (date)"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        daRecupereNum = New SqlDataAdapter(cmdRecupereNum)
        daRecupereNum.Fill(dsCommande, "STATISTIQUE_ARTICLE_PREC")


        For i = 0 To dsCommande.Tables("STATISTIQUE_ARTICLE").Rows.Count - 1
            mois = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("Mois").ToString
            If mois <> "" Then
                If mois = "1" Then
                    lMois1.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "2" Then
                    lMois2.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "3" Then
                    lMois3.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "4" Then
                    lMois4.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "5" Then
                    lMois5.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "6" Then
                    lMois6.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "7" Then
                    lMois7.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "8" Then
                    lMois8.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "9" Then
                    lMois9.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "10" Then
                    lMois10.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "11" Then
                    lMois11.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "12" Then
                    lMois12.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If

            End If
        Next

        For i = 0 To dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows.Count - 1
            mois = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("Mois").ToString
            If mois <> "" Then
                If mois = "1" Then
                    lMoisPrec1.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "2" Then
                    lMoisPrec2.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "3" Then
                    lMoisPrec3.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "4" Then
                    lMoisPrec4.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "5" Then
                    lMoisPrec5.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "6" Then
                    lMoisPrec6.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "7" Then
                    lMoisPrec7.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "8" Then
                    lMoisPrec8.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "9" Then
                    lMoisPrec9.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "10" Then
                    lMoisPrec10.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "11" Then
                    lMoisPrec11.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "12" Then
                    lMoisPrec12.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If

            End If
        Next


    End Sub
    Private Sub InitialiserStatistique()
        lMois1.Text = "0"
        lMois2.Text = "0"
        lMois3.Text = "0"
        lMois4.Text = "0"
        lMois5.Text = "0"
        lMois6.Text = "0"
        lMois7.Text = "0"
        lMois8.Text = "0"
        lMois9.Text = "0"
        lMois10.Text = "0"
        lMois11.Text = "0"
        lMois12.Text = "0"

        lMoisPrec1.Text = "0"
        lMoisPrec2.Text = "0"
        lMoisPrec3.Text = "0"
        lMoisPrec4.Text = "0"
        lMoisPrec5.Text = "0"
        lMoisPrec6.Text = "0"
        lMoisPrec7.Text = "0"
        lMoisPrec8.Text = "0"
        lMoisPrec9.Text = "0"
        lMoisPrec10.Text = "0"
        lMoisPrec11.Text = "0"
        lMoisPrec12.Text = "0"
    End Sub



    Private Sub bListeDesManquants_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bListeDesManquants.Click
        Dim Cond As String = ""
        Dim StrSQL As String = ""
        Dim I As Integer = 0
        Dim ListeExiste As String = ""


        If SansManquantDepuis <> "" Then
            Cond = Cond + " AND ARTICLE.DateAlerte <='" + SansManquantDepuis + "'"
        End If
        If Section = "INTERVALLE" Then
            Cond = Cond + " AND Section > " + DebutIntervalle.ToString + " AND Section < " + FinIntervalle.ToString
        End If
        If Forme <> 0 Then
            Cond = Cond + " AND ARTICLE.CodeForme = " + Forme.ToString
        End If
        If Categorie <> 0 Then
            Cond = Cond + " AND CodeCategorie = " + Categorie.ToString
        End If
        If Labo <> 0 Then
            Cond = Cond + " AND CodeLabo = " + Labo.ToString
        End If
        If Rayon = "RAYON" Then
            Cond = Cond + " AND Rayon = " + RayonSelectionne
        End If
        'COMMANDE_DETAILS_PROJET
        If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count - 1 > 0 Then

            'For I = 0 To dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1
            '    ListeExiste += "'" + dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle") + "',"
            'Next
            'Cond += " AND CodeArticle NOT IN (" + ListeExiste.Substring(0, ListeExiste.Length - 1) + ")"
            Cond += "AND ARTICLE.DateAlerte <'" + SansManquantDepuis + "'"
        End If

        Cond = Cond + " ORDER BY " + Trie

        StrSQL = "SELECT CAST(0 as bit) Cocher," + _
                 " CodeArticle," + _
                 " CodeABarre," + _
                 " Designation," + _
                 " ARTICLE.CodeForme," + _
                 " LibelleForme," + _
                 " '-' as Qte," + _
                 " '-' as Stock," + _
                 " StockAlerte," + _
                 " QteACommander " + _
                 " FROM ARTICLE " + _
                 " LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                 " WHERE  " + _
                 " ((SELECT SUM(QteLotArticle) FROM LOT_ARTICLE " + _
                 " WHERE LOT_ARTICLE.CodeArticle =ARTICLE.CodeArticle " + _
                 " AND (LOT_ARTICLE.DatePeremptionArticle >' " + System.DateTime.Now + _
                 "' OR LOT_ARTICLE.DatePeremptionArticle is null))<ARTICLE.StockAlerte  " + _
                 " OR (SELECT SUM(QteLotArticle) FROM LOT_ARTICLE" + _
                 " WHERE LOT_ARTICLE.CodeArticle =ARTICLE.CodeArticle " + _
                 " AND (LOT_ARTICLE.DatePeremptionArticle >'" + System.DateTime.Now + _
                 "' OR LOT_ARTICLE.DatePeremptionArticle is null)) is null) " + Cond

        Dim listeManquant As New fListeDesManquants
        fListeDesManquants.Requette = StrSQL
        fListeDesManquants.FromCommandeOrProjetCommande = False
        fListeDesManquants.CommandeEnCours = CommandeEnCours

        listeManquant.ShowDialog()

        If fListeDesManquants.ConvertirEnCommande = True Then
            '------------------------------- élemination des lignes vide ou qte =0 
            I = 0
            Do While I < dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Count
                If dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(I).Item("CodeArticle") = "" Then
                    dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows(I).Delete()
                    I = 0
                Else
                    I = I + 1
                End If
            Loop


            Dim DatePeremption As Date

            For I = 0 To fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows.Count - 1
                Try
                    If fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("CodeArticle") <> "" Then

                        If fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Cocher") = True Then

                            NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS_PROJET").NewRow()
                            NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                            NouvelArticle("CodeArticle") = ""
                            NouvelArticle("CodeABarre") = ""
                            NouvelArticle("Fournisseur") = ""
                            dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Add(NouvelArticle)

                            NouvelArticle("CodeArticle") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("CodeArticle")
                            NouvelArticle("CodeABarre") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("CodeABarre")

                            NouvelArticle("NumeroCommande") = NumeroCommande
                            NouvelArticle("Designation") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Designation")

                            NouvelArticle("CodeForme") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("CodeForme")
                            NouvelArticle("Stock") = CalculeStock(NouvelArticle("CodeArticle"))

                            NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                            NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

                            NouvelArticle("StockAlerte") = RecupererValeurExecuteScalaire("StockAlerte", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                            NouvelArticle("EnCours") = 0

                            NouvelArticle("QteACommander") = RecupererValeurExecuteScalaire("QteACommander", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                            NouvelArticle("Qte") = (NouvelArticle("StockAlerte") - NouvelArticle("Stock") + NouvelArticle("QteACommander"))

                            NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
                            NouvelArticle("QteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

                            NouvelArticle("LibelleForme") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("LibelleForme")

                            '------------------ récupération de la date de péremption

                            StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
                            System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
                            NouvelArticle("CodeArticle") + "' Order by DatePeremptionArticle ASC "

                            cmdCommande.Connection = ConnectionServeur
                            cmdCommande.CommandText = StrSQL

                            Try
                                DatePeremption = cmdCommande.ExecuteScalar()
                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                            If DatePeremption = #12:00:00 AM# Then
                                'NouvelArticle("DatePeremption") = "01/01/1900"
                            Else
                                NouvelArticle("DatePeremption") = DatePeremption
                            End If

                            Dim J As Integer = 0

                            Do While J < gArticles.RowCount - 1
                                If gArticles(J, "CodeArticle") = NouvelArticle("CodeArticle") Then
                                    If MsgBox("Article déja saisi, voulez vous ajouter la quantité ou effacer l'article ? Oui pour ajouter la quantité", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                                        gArticles(J, "Qte") = (Convert.ToInt32(gArticles(J, "Qte")) + NouvelArticle("Qte")).ToString
                                        gArticles.MoveLast()
                                        gArticles.Delete()
                                    Else
                                        gArticles.MoveLast()
                                        gArticles.Delete()
                                    End If

                                End If
                                J = J + 1
                            Loop


                        End If

                    End If
                Catch ex As Exception

                End Try
            Next
            '------------------ ajout d une ligne vide 
            NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS_PROJET").NewRow()
            NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
            NouvelArticle("CodeArticle") = ""
            NouvelArticle("Fournisseur") = ""
            dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Add(NouvelArticle)
        End If
        gArticles.MoveLast()
        gArticles.Col = 1
        gArticles.EditActive = True
    End Sub
End Class