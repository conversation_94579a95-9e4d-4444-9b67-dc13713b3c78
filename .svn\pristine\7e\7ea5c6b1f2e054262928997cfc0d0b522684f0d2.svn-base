﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fFicheMutuelle
    Public ValidationCodeTest As Boolean = False

    Public ajoutmodif As String = ""
    Public CodeMutuelle As String = ""
    Public SoldeMutuelle As Double = 0.0

    Dim cmdMutuelle As New SqlCommand
    Dim cbMutuelle As New SqlCommandBuilder
    Dim dsMutuelle As New DataSet
    Dim daMutuelle As New SqlDataAdapter

    Dim cmdReglement As New SqlCommand
    Dim cbReglement As New SqlCommandBuilder
    Dim daReglement As New SqlDataAdapter

    Dim dsRecupereNum As New DataSet
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNumt As New SqlDataAdapter

    Dim NumeroReglementAModifier As String = ""
    Dim NumeroVenteAAfficher As String = ""

    Dim BlanchirListeReglement As Boolean = False

    Dim CmdCalcul As New SqlCommand
    Dim CodeExiste As Boolean = False

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "114" And bConfirmer.Enabled = True Then
            bConfirmer_Click(sender, e)
        End If

        If argument = "121" And bAnnuler.Enabled = True Then
            bAnnuler_Click(sender, e)
        End If

    End Sub

    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)
        If e.KeyData = Keys.F3 Then
            bConfirmer_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F7 Then
            bSupprimerReglement_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F8 Then
            bModifierReglement_Click(o, e)
            Exit Sub
        End If
    End Sub

    Public Sub Init()
        Dim StrSQL1 As String = ""
        'Dim StrSQL2 As String = ""
        'Dim StrSQL3 As String = ""

        Dim StrSQLdernierAchat As String = ""
        Dim Dernier_Date_Achat As String = ""
        Dim StrSQLSolde As String = ""
        Dim Somme_Facture As Double = 0.0
        Dim Somme_Reglement As Double = 0.0
        Dim difference As Double = 0.0

        'chargement des villes
        StrSQL1 = "SELECT DISTINCT CodeVille,NomVille FROM VILLE WHERE SupprimeVille=0 ORDER BY NomVille ASC"
        cmdMutuelle.Connection = ConnectionServeur
        cmdMutuelle.CommandText = StrSQL1
        daMutuelle = New SqlDataAdapter(cmdMutuelle)
        daMutuelle.Fill(dsMutuelle, "VILLE")
        cmbVilleMutuelle.DataSource = dsMutuelle.Tables("VILLE")
        cmbVilleMutuelle.ValueMember = "CodeVille"
        cmbVilleMutuelle.DisplayMember = "NomVille"
        cmbVilleMutuelle.ColumnWidth = 10
        cmbVilleMutuelle.ColumnHeaders = False
        cmbVilleMutuelle.Splits(0).DisplayColumns("NomVille").Width = 10
        cmbVilleMutuelle.Splits(0).DisplayColumns("CodeVille").Visible = False
        cmbVilleMutuelle.ExtendRightColumn = True

        If ajoutmodif = "A" Then
            StrSQL1 = " SELECT TOP 0 * FROM MUTUELLE"
        Else
            StrSQL1 = " SELECT * FROM MUTUELLE WHERE CodeMutuelle = " + Quote(CodeMutuelle)
        End If

        cmdMutuelle.Connection = ConnectionServeur
        cmdMutuelle.CommandText = StrSQL1
        daMutuelle = New SqlDataAdapter(cmdMutuelle)
        daMutuelle.Fill(dsMutuelle, "MUTUELLE")
        cbMutuelle = New SqlCommandBuilder(daMutuelle)

        If ajoutmodif = "M" Then

            LTitreMutuelle.Text = "Fiche MUTUELLE : " + dsMutuelle.Tables("MUTUELLE").Rows(0)("NomMutuelle")

            tCodeMutuelle.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("CodeMutuelle")
            tNomMutuelle.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("NomMutuelle")

            tAdresseMutuelle.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("Adresse")
            tCodeTVA.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("CodeTva")
            cmbVilleMutuelle.SelectedValue = dsMutuelle.Tables("MUTUELLE").Rows(0)("CodeVille")
            tTelephoneMutuelle.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("Tel")

            tFaxMutuelle.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("Fax")
            tCodePostalMutuelle.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("CodePostal")
            tPriseEnCharge.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("PriseEnCharge")
            chbAfficherLaTotalitreDuMontant.Checked = dsMutuelle.Tables("MUTUELLE").Rows(0)("AfficherLaTotaliteDuMontant")

            tLibelle1.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("Libelle1")
            tLibelle2.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("Libelle2")
            tLibelle3.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("Libelle3")
            tLibelle4.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("Libelle4")
            tLibelle5.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("Libelle5")

            tRemarqueMutuelle.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("Remarque")
            tCodePharmacien.Value = dsMutuelle.Tables("MUTUELLE").Rows(0)("CodePharmacien")
            lSolde.Text = SoldeMutuelle
            lSoldeInitial.Text = dsMutuelle.Tables("MUTUELLE").Rows(0)("SoldeInitial")
            tNomMutuelle.Focus()
            '------------------------------ verouillage du code pour interdire la modification --------
            tCodeMutuelle.Enabled = False

            AfficherSituationMutuelle()

        Else
            lSolde.Text = 0
            tPriseEnCharge.Value = 0
            tCodeMutuelle.Focus()
        End If
        TAB.TabPages(0).Show()
        tCodeMutuelle.Focus()
    End Sub

    Public Sub AfficherSituationMutuelle()

        Dim StrSQLSolde As String = ""
        Dim Somme_Facture As Double = 0.0
        Dim Somme_Reglement As Double = 0.0
        Dim difference As Double = 0.0
        Dim EnCours As Double = 0.0

        ''calcul du solde Mutuelle en retranchant la somme des montants des règlements de la somme des montants des ventes 
        'StrSQLSolde = "SELECT SUM(MontantMutuelle) FROM VENTE WHERE CodeMutuelle=" + CodeMutuelle.ToString
        'CmdCalcul.Connection = ConnectionServeur
        'CmdCalcul.CommandText = StrSQLSolde

        'Try
        '    Somme_Facture = CmdCalcul.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'StrSQLSolde = "SELECT SUM(Montant) FROM REGLEMENT_MUTUELLE WHERE Encaisse=1 AND CodeMutuelle=" + CodeMutuelle.ToString
        'CmdCalcul.Connection = ConnectionServeur
        'CmdCalcul.CommandText = StrSQLSolde

        'Try
        '    Somme_Reglement = CmdCalcul.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'difference = Somme_Facture - Somme_Reglement

        StrSQLSolde = "select ISNULL(SUM(Reste), 0) from RELEVE_MUTUELLE WHERE CodeMutuelle=" + Quote(CodeMutuelle.ToString)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQLSolde

        Try
            Somme_Reglement = CmdCalcul.ExecuteScalar()
        Catch ex As Exception
            Somme_Reglement = 0
        End Try
        lSolde.Text = Convert.ToString(Math.Round(Somme_Reglement, 3))

        ''calcul du solde Mutuelle en cours (somme des cheque qui ont ne sont pas encaissé) 
        'StrSQLSolde = "SELECT SUM(Montant) FROM REGLEMENT_MUTUELLE WHERE  " + _
        '              "(CodeNatureReglement= 2 OR CodeNatureReglement= 5) AND Encaisse=0 AND CodeMutuelle=" + CodeMutuelle.ToString
        'CmdCalcul.Connection = ConnectionServeur
        'CmdCalcul.CommandText = StrSQLSolde

        'Try
        '    EnCours = CmdCalcul.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        lSoldeEnCours.Text = "0.000"
        lResteAPayer.Text = Math.Round(Somme_Reglement, 3)
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        If dsMutuelle.HasChanges Then
            If MsgBox("Voulez vous vraiment annuler les modifications de ce Mutuelle ?", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                'fMain.Tab.SelectedTab.Dispose()
                Me.Hide()
            End If
        Else
            'fMain.Tab.SelectedTab.Dispose()
            Me.Hide()
        End If
    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        Dim dr As DataRow
        Dim StrSQL As String = ""

        If tCodeMutuelle.Text = "" Then
            MsgBox("Veuillez saisir le code du Mutuelle !", MsgBoxStyle.Critical, "Erreur")
            tCodeMutuelle.Focus()
            Exit Sub
        End If

        If tNomMutuelle.Text = "" Then
            MsgBox("Veuillez saisir le nom du Mutuelle !", MsgBoxStyle.Critical, "Erreur")
            tNomMutuelle.Focus()
            Exit Sub
        End If

        If tPriseEnCharge.Text = "" Then
            MsgBox("Veuillez saisir le pourcentage de la prise en charge !", MsgBoxStyle.Critical, "Erreur")
            tPriseEnCharge.Focus()
            Exit Sub
        End If

        '------------------------------ demande du mot de passe
        Dim myMotDePasse As New fMotDePasse
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If
        '------------------------------------------------------

        If CodeExiste = True Then
            MsgBox("Code Mutuelle existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
            tCodeMutuelle.Focus()
            Exit Sub
        End If

        If ajoutmodif = "A" Then
            With dsMutuelle
                dr = .Tables("MUTUELLE").NewRow

                dr.Item("CodeMutuelle") = tCodeMutuelle.Text
                dr.Item("NomMutuelle") = tNomMutuelle.Text

                dr.Item("Adresse") = tAdresseMutuelle.Text
                dr.Item("CodeTva") = tCodeTVA.Text

                If cmbVilleMutuelle.Text <> "" Then
                    dr.Item("CodeVille") = cmbVilleMutuelle.SelectedValue
                End If
                dr.Item("Tel") = tTelephoneMutuelle.Text
                dr.Item("Fax") = tFaxMutuelle.Text
                dr.Item("CodePostal") = tCodePostalMutuelle.Text

                dr.Item("PriseEnCharge") = tPriseEnCharge.Text

                dr.Item("AfficherLaTotaliteDuMontant") = chbAfficherLaTotalitreDuMontant.Checked

                dr.Item("Libelle1") = tLibelle1.Text
                dr.Item("Libelle2") = tLibelle2.Text
                dr.Item("Libelle3") = tLibelle3.Text
                dr.Item("Libelle4") = tLibelle4.Text
                dr.Item("Libelle5") = tLibelle5.Text

                dr.Item("Remarque") = tRemarqueMutuelle.Text

                dr.Item("CodePharmacien") = tCodePharmacien.Text

                .Tables("MUTUELLE").Rows.Add(dr)
            End With

        ElseIf ajoutmodif = "M" Then
            With dsMutuelle.Tables("MUTUELLE")
                dr = .Rows(0)
                dr.Item("CodeMutuelle") = tCodeMutuelle.Text
                dr.Item("NomMutuelle") = tNomMutuelle.Text

                dr.Item("Adresse") = tAdresseMutuelle.Text
                dr.Item("CodeTva") = tCodeTVA.Text

                If cmbVilleMutuelle.Text <> "" Then
                    dr.Item("CodeVille") = cmbVilleMutuelle.SelectedValue
                End If

                dr.Item("Tel") = tTelephoneMutuelle.Text
                dr.Item("Fax") = tFaxMutuelle.Text
                dr.Item("CodePostal") = tCodePostalMutuelle.Text

                dr.Item("PriseEnCharge") = tPriseEnCharge.Text

                dr.Item("AfficherLaTotaliteDuMontant") = chbAfficherLaTotalitreDuMontant.Checked

                dr.Item("Libelle1") = tLibelle1.Text
                dr.Item("Libelle2") = tLibelle2.Text
                dr.Item("Libelle3") = tLibelle3.Text
                dr.Item("Libelle4") = tLibelle4.Text
                dr.Item("Libelle5") = tLibelle5.Text

                dr.Item("CodePharmacien") = tCodePharmacien.Text

                dr.Item("Remarque") = tRemarqueMutuelle.Text
            End With

        End If
        Try
            daMutuelle.Update(dsMutuelle, "MUTUELLE")

            If ajoutmodif = "A" Then
                InsertionDansLog("AJOUT_MUTUELLE", "L ajout du mutuelle " + tNomMutuelle.Text, CodeOperateur, System.DateTime.Now, "MUTUELLE", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
            ElseIf ajoutmodif = "M" Then
                InsertionDansLog("MODIFICATION_MUTUELLE", "La modification de l'article " + tNomMutuelle.Text, CodeOperateur, System.DateTime.Now, "MUTUELLE", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
            End If

            Me.Hide()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsMutuelle.Reset()
            Me.Init()
        End Try

    End Sub

    Private Sub cmbVilleMutuelle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        'Recherche_Automatique_fiche(e, cmbVilleMutuelle, cmbVilleMutuelle.Columns("NomVille"))
        If e.KeyCode = Keys.Enter Then
            cmbVilleMutuelle.Text = cmbVilleMutuelle.WillChangeToText
        Else
            cmbVilleMutuelle.OpenCombo()
        End If
    End Sub

    Private Sub tCodeMutuelle_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tCodeMutuelle.KeyPress
        'If IsNumeric(e.KeyChar) = False And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back Then
        '    e.Handled = True
        'End If
    End Sub

    Private Sub tCodeMutuelle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeMutuelle.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNomMutuelle.Focus()
        End If
    End Sub


    Private Sub tCodeMutuelle_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeMutuelle.LostFocus
        lTest.Visible = False
    End Sub

    Private Sub tCodeMutuelle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeMutuelle.TextChanged
        Dim StrSQLtest As String = ""
        dsRecupereNum.Clear()
        If ajoutmodif = "A" Then
            StrSQLtest = " SELECT * FROM MUTUELLE WHERE CodeMutuelle=" + Quote(tCodeMutuelle.Text)
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQLtest
            daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
            daRecupereNumt.Fill(dsRecupereNum, "MUTUELLE")

            If dsRecupereNum.Tables("MUTUELLE").Rows.Count <> 0 Then
                lTest.Text = "Code non valide déja existe"
                lTest.ForeColor = Color.OrangeRed
                lTest.Visible = True
                ValidationCodeTest = False
                CodeExiste = True
            Else
                lTest.Text = "Code valide"
                lTest.ForeColor = Color.LimeGreen
                lTest.Visible = True
                ValidationCodeTest = True
                CodeExiste = False
            End If
        End If
        If tCodeMutuelle.Text = "" Then
            lTest.Visible = False
        End If
    End Sub

    Private Sub cmbVilleMutuelle_KeyUp1(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbVilleMutuelle.KeyUp
        'Recherche_Automatique_fiche(e, cmbVilleMutuelle, cmbVilleMutuelle.Columns("NomVille"))
        If e.KeyCode = Keys.Enter Then
            cmbVilleMutuelle.Text = cmbVilleMutuelle.WillChangeToText
            tTelephoneMutuelle.Focus()
        Else
            cmbVilleMutuelle.OpenCombo()
        End If

    End Sub

    Private Sub tPriseEnCharge_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tPriseEnCharge.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLibelle1.Focus()
        End If
    End Sub

    Private Sub tPriseEnCharge_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tPriseEnCharge.TextChanged
        If IsNumeric(tPriseEnCharge.Text) Then
            If CDbl(tPriseEnCharge.Text) > 100 Then
                MsgBox("Prise en charge ne doit pas dépasser 100 % !", MsgBoxStyle.Critical, "Erreur")
                tPriseEnCharge.Text = "100"
            End If
        End If
    End Sub

    Private Sub Panel_Paint(ByVal sender As System.Object, ByVal e As System.Windows.Forms.PaintEventArgs) Handles Panel.Paint

    End Sub

    Private Sub tNomMutuelle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNomMutuelle.KeyUp
        If e.KeyCode = Keys.Enter Then
            tAdresseMutuelle.Focus()
        End If
    End Sub

    Private Sub tNomMutuelle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNomMutuelle.TextChanged

    End Sub

    Private Sub tAdresseMutuelle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tAdresseMutuelle.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbVilleMutuelle.Focus()
        End If
    End Sub

    Private Sub tAdresseMutuelle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tAdresseMutuelle.TextChanged

    End Sub

    Private Sub cmbVilleMutuelle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbVilleMutuelle.TextChanged

    End Sub

    Private Sub tTelephoneMutuelle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTelephoneMutuelle.KeyUp
        If e.KeyCode = Keys.Enter Then
            tFaxMutuelle.Focus()
        End If
    End Sub

    Private Sub tTelephoneMutuelle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tTelephoneMutuelle.TextChanged

    End Sub

    Private Sub tFaxMutuelle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tFaxMutuelle.KeyUp
        If e.KeyCode = Keys.Enter Then
            tCodePostalMutuelle.Focus()
        End If
    End Sub

    Private Sub tFaxMutuelle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tFaxMutuelle.TextChanged

    End Sub

    Private Sub tCodePostalMutuelle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodePostalMutuelle.KeyUp
        If e.KeyCode = Keys.Enter Then
            tPriseEnCharge.Focus()
        End If
    End Sub

    Private Sub tCodePostalMutuelle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodePostalMutuelle.TextChanged

    End Sub

    Private Sub tLibelle1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tLibelle1.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLibelle2.Focus()
        End If
    End Sub

    Private Sub tLibelle1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tLibelle1.TextChanged

    End Sub

    Private Sub tLibelle2_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tLibelle2.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLibelle3.Focus()
        End If
    End Sub

    Private Sub tLibelle2_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tLibelle2.TextChanged

    End Sub

    Private Sub tLibelle3_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tLibelle3.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLibelle4.Focus()
        End If
    End Sub

    Private Sub tLibelle3_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tLibelle3.TextChanged

    End Sub

    Private Sub tLibelle4_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tLibelle4.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLibelle5.Focus()
        End If
    End Sub

    Private Sub tLibelle4_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tLibelle4.TextChanged

    End Sub

    Private Sub tLibelle5_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tLibelle5.KeyUp
        If e.KeyCode = Keys.Enter Then
            tRemarqueMutuelle.Focus()
        End If
    End Sub
    Private Sub C1DockingTab1_Enter(ByVal sender As Object, ByVal e As System.EventArgs) Handles TAB.Enter
        AfficherReglement(0)
    End Sub
    Public Sub AfficherReglement(ByVal NumVente As String)
        Dim StrSQL As String = ""
        Dim NumeroPremierReglement As Integer = 0
        Dim i As Integer = 0

        'chargement des règlements

        If (dsMutuelle.Tables.IndexOf("REGLEMENT_MUTUELLE_AFFICHAGE") > -1) Then
            dsMutuelle.Tables("REGLEMENT_MUTUELLE_AFFICHAGE").Clear()
        End If

        StrSQL = "SELECT REGLEMENT_MUTUELLE.NumeroReglementMutuelle," + _
                 "LibelleReglement AS libelle ," + _
                 "REGLEMENT_MUTUELLE_VENTE.NumeroReleve AS NumeroReleve ," + _
                 "NATURE_REGLEMENT.LibelleNatureReglement AS LibelleNatureReglement," + _
                 "REGLEMENT_MUTUELLE.CodeNatureReglement, " + _
                 "Date," + _
                 "DateEcheance," + _
                 "Montant," + _
                 "NumeroCheque," + _
                 "LibellePoste," + _
                 "NomInscritSurLeCheque," + _
                 "CodeMutuelle," + _
                 "BANQUE.NomBanque," + _
                 "REGLEMENT_MUTUELLE.CodeBanque," + _
                 "Encaisse " + _
                 "FROM " + _
                 "REGLEMENT_MUTUELLE " + _
                 "LEFT OUTER JOIN (SELECT NumeroReglementMutuelle, MAX(NumeroReleve) AS NumeroReleve FROM REGLEMENT_MUTUELLE_VENTE GROUP BY NumeroReglementMutuelle) AS REGLEMENT_MUTUELLE_VENTE ON REGLEMENT_MUTUELLE.NumeroReglementMutuelle=REGLEMENT_MUTUELLE_VENTE.NumeroReglementMutuelle " + _
                 "LEFT OUTER JOIN NATURE_REGLEMENT ON REGLEMENT_MUTUELLE.CodeNatureReglement=NATURE_REGLEMENT.CodeNatureReglement " + _
                 "LEFT OUTER JOIN BANQUE ON REGLEMENT_MUTUELLE.CodeBanque=BANQUE.CodeBanque " + _
                 "WHERE REGLEMENT_MUTUELLE.CodeMutuelle='" + tCodeMutuelle.Text + "'"


        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsMutuelle, "REGLEMENT_MUTUELLE_AFFICHAGE")
        cbReglement = New SqlCommandBuilder(daReglement)

        With gReglements
            .Columns.Clear()
            Try
                .DataSource = dsMutuelle
            Catch ex As Exception
            End Try
            .DataMember = "REGLEMENT_MUTUELLE_AFFICHAGE"
            .Rebind(False)
            .Columns("NumeroReglementMutuelle").Caption = "Numero reg"
            .Columns("libelle").Caption = "libellé"
            .Columns("LibelleNatureReglement").Caption = "Nature"
            .Columns("Date").Caption = "Date"
            .Columns("DateEcheance").Caption = "Date Echéance"
            .Columns("Montant").Caption = "Montant"
            .Columns("NumeroCheque").Caption = "Numero Cheque "
            .Columns("LibellePoste").Caption = "Numero Poste"
            .Columns("NomInscritSurLeCheque").Caption = "Nom inscrit"
            .Columns("CodeMutuelle").Caption = "Code Client"
            .Columns("NomBanque").Caption = "BANQUE"
            .Columns("Encaisse").Caption = "Encaissé"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns(2).Style.HorizontalAlignment = AlignHorzEnum.Near
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("NumeroReglementMutuelle").Width = 50
            .Splits(0).DisplayColumns("libelle").Width = 170
            .Splits(0).DisplayColumns("LibelleNatureReglement").Width = 80
            .Splits(0).DisplayColumns("CodeNatureReglement").Visible = False
            .Splits(0).DisplayColumns("Date").Width = 70
            .Splits(0).DisplayColumns("DateEcheance").Width = 70
            .Splits(0).DisplayColumns("Montant").Width = 50
            .Splits(0).DisplayColumns("NumeroCheque").Width = 70
            .Splits(0).DisplayColumns("LibellePoste").Width = 0
            .Splits(0).DisplayColumns("LibellePoste").Visible = False
            .Splits(0).DisplayColumns("NomInscritSurLeCheque").Width = 50
            .Splits(0).DisplayColumns("CodeMutuelle").Width = 50
            .Splits(0).DisplayColumns("NomBanque").Width = 60
            .Splits(0).DisplayColumns("CodeBanque").Visible = False
            .Splits(0).DisplayColumns("Encaisse").Width = 50

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

        End With
        'NumeroReglementAModifier = gReglements(0, "NumeroReglementFournisseur")

        StrSQL = "SELECT TOP(1) REGLEMENT_MUTUELLE.NumeroReglementMutuelle FROM REGLEMENT_MUTUELLE LEFT OUTER JOIN REGLEMENT_MUTUELLE_VENTE" + _
               " ON REGLEMENT_MUTUELLE.NumeroReglementMutuelle=REGLEMENT_MUTUELLE_VENTE.NumeroReglementMutuelle WHERE NumeroVente='" + _
               NumVente.ToString + "' ORDER BY REGLEMENT_MUTUELLE.Date"

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL

        Try
            NumeroPremierReglement = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        If NumeroPremierReglement <> 0 Then
            For i = 0 To dsMutuelle.Tables("REGLEMENT_MUTUELLE_AFFICHAGE").Rows.Count - 1
                If gReglements(i, "NumeroReglementMutuelle") = NumeroPremierReglement Then
                    gReglements.MoveRelative(i)
                End If
            Next
        End If

        If NumeroVenteAAfficher = "" And BlanchirListeReglement = False Then
            NumeroReglementAModifier = gReglements(0, "NumeroReglementMutuelle").ToString
            If NumeroReglementAModifier = "" Then
                AfficherVente(0)
            Else
                AfficherVente(NumeroReglementAModifier)
            End If

        End If
    End Sub

    Public Sub AfficherVente(ByVal NumReglement As Integer)

        Dim StrSQL As String = ""
        Dim i As Integer = 0
        Dim NumeroPremierAchat As String = ""
        Dim x As Integer = 0

        If (dsMutuelle.Tables.IndexOf("VENTE_REGLEMENT_MUTUELLE_AFFICHAGE") > -1) Then
            dsMutuelle.Tables("VENTE_REGLEMENT_MUTUELLE_AFFICHAGE").Clear()
        End If

        ' chargement des vente 

        StrSQL = "SELECT NumeroVente," + _
                 "Date," + _
                 "TotalHT," + _
                 "TotalTTC," + _
                 "TVA," + _
                 "TotalRemise AS Remise " + _
                 "FROM VENTE " + _
                 "WHERE CodeMutuelle='" + tCodeMutuelle.Text + _
                 "' AND NumeroVente IN(SELECT NumeroVente " + _
                 "FROM REGLEMENT_MUTUELLE_VENTE )" + _
                 " ORDER BY Date"

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsMutuelle, "VENTE_REGLEMENT_MUTUELLE_AFFICHAGE")
        cbReglement = New SqlCommandBuilder(daReglement)

        With gVentes
            .Columns.Clear()
            Try
                .DataSource = dsMutuelle
            Catch ex As Exception
            End Try
            .DataMember = "VENTE_REGLEMENT_MUTUELLE_AFFICHAGE"
            .Rebind(False)
            .Columns("NumeroVente").Caption = "Numero vente"
            .Columns("Date").Caption = "Date"
            .Columns("TotalHT").Caption = "Total HT"
            .Columns("TotalTTC").Caption = "Total TTC"
            .Columns("TVA").Caption = "TVA"
            .Columns("Remise").Caption = "Remise"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("NumeroVente").Width = 100
            .Splits(0).DisplayColumns("Date").Width = 250
            .Splits(0).DisplayColumns("TotalHT").Width = 100
            .Splits(0).DisplayColumns("TotalTTC").Width = 100
            .Splits(0).DisplayColumns("TVA").Width = 100
            .Splits(0).DisplayColumns("Remise").Width = 100

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

        End With

        StrSQL = "SELECT TOP(1) VENTE.NumeroVente FROM VENTE LEFT OUTER JOIN REGLEMENT_MUTUELLE_VENTE" + _
                 " ON VENTE.NumeroVente=REGLEMENT_MUTUELLE_VENTE.NumeroVente WHERE NumeroReglementMutuelle=" + _
                 NumReglement.ToString + " ORDER BY VENTE.Date"

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL

        Try
            NumeroPremierAchat = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        If NumeroPremierAchat <> "" Then
            For i = 0 To dsMutuelle.Tables("VENTE_REGLEMENT_MUTUELLE_AFFICHAGE").Rows.Count - 1
                If gVentes(i, "NumeroVente") = NumeroPremierAchat Then
                    gVentes.MoveRelative(i)
                End If
            Next
        Else
            gVentes.MoveFirst()
        End If

    End Sub

    Private Sub bModifierReglement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifierReglement.Click

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(4, "MODIFICATION_REGLEMENT_MUTUELLE") = "False" Then
            Exit Sub
        End If

        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = "SELECT DemandeMotDePasse FROM [POINTS_CONTROLE] WHERE CodePointControle LIKE 'MODIFICATION_REGLEMENT_MUTUELLE' AND CodeModule=4"

        'Try
        '    DemandePotDePasse = cmd.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'If DemandePotDePasse = True Then
        '    '------------------------------ demande du mot de passe
        '    Dim myMotDePasse As New fMotDePasse
        '    myMotDePasse.ShowDialog()

        '    ConfirmerEnregistrer = fMotDePasse.Confirmer
        '    CodeOperateur = fMotDePasse.CodeOperateur

        '    myMotDePasse.Dispose()
        '    myMotDePasse.Close()

        '    If ConfirmerEnregistrer = False Then
        '        Exit Sub
        '    End If

        '    cmd.Connection = ConnectionServeur
        '    cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'MODIFICATION_REGLEMENT_MUTUELLE' AND CodeUtilisateur='" + CodeOperateur + "'"

        '    Try
        '        If cmd.ExecuteScalar() < 1 Then
        '            MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
        '            Exit Sub
        '        End If
        '    Catch ex As Exception
        '        Console.WriteLine(ex.Message)
        '    End Try
        'End If

        If gReglements(gReglements.Row, "NumeroReglementMutuelle").ToString <> "" Then
            If gReglements(gReglements.Row, "Encaisse") = True Then
                MsgBox("Il est interdit de modifier un réglement encaissé !", MsgBoxStyle.OkOnly)
                Exit Sub
            End If
        Else : Exit Sub
        End If

        Dim MyReglement As New fReglementMutuelle
        MyReglement.CodeReglement = gReglements(gReglements.Row, "NumeroReglementMutuelle")
        MyReglement.CodeMutuelle = tCodeMutuelle.Text
        MyReglement.ajoutmodif = "M"
        MyReglement.init()
        MyReglement.ShowDialog()
        AfficherReglement(0)

    End Sub

    Private Sub gReglements_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gReglements.Click

    End Sub

    Private Sub gReglements_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gReglements.FetchRowStyle

        Dim NumeroReglement As Integer = gReglements.Columns("NumeroReglementMutuelle").CellText(e.Row).ToString
        Dim StrSQL As String = ""
        Dim TestRegleOuNon As Integer = 0

        StrSQL = "SELECT count(NumeroReglementMutuelle) FROM REGLEMENT_MUTUELLE_VENTE WHERE " + _
                 "NumeroReglementMutuelle=" + _
                 NumeroReglement.ToString + " AND NumeroVente='" + NumeroVenteAAfficher + "'"

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL

        Try
            TestRegleOuNon = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        If TestRegleOuNon > 0 Then
            ' le coloriage 
            e.CellStyle.BackColor = System.Drawing.Color.Blue
            e.CellStyle.ForeColor = Color.White
        End If
    End Sub

    Private Sub gVentes_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gVentes.Click

    End Sub

    Private Sub gVentes_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gVentes.FetchRowStyle
        Dim NumeroAchat As String = gVentes.Columns("NumeroVente").CellText(e.Row).ToString
        Dim StrSQL As String = ""
        Dim TestRegleOuNon As Integer = 0

        StrSQL = "SELECT count(NumeroReglementMutuelle) FROM REGLEMENT_MUTUELLE_VENTE WHERE NumeroReglementMutuelle=" + _
                    NumeroReglementAModifier.ToString + " AND NumeroVente='" + NumeroAchat + "'"

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL

        Try
            TestRegleOuNon = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        If TestRegleOuNon > 0 Then
            ' le coloriage 
            e.CellStyle.BackColor = System.Drawing.Color.Lime
            e.CellStyle.ForeColor = Color.Black
        End If
    End Sub

    Private Sub gReglements_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gReglements.MouseClick
        Dim I As Integer = 0
        Dim index As Integer = 0

        NumeroReglementAModifier = gReglements(gReglements.Row, "NumeroReglementMutuelle")

        NumeroVenteAAfficher = ""
        BlanchirListeReglement = True
        AfficherReglement("")
        BlanchirListeReglement = False
        For I = 0 To gReglements.RowCount - 1
            If gReglements(I, "NumeroReglementMutuelle") = NumeroReglementAModifier Then
                index = I
            End If
        Next

        gReglements.MoveRelative(index)
        gReglements.HighLightRowStyle.BackColor = Color.Blue
        If NumeroReglementAModifier <> "" Then
            AfficherVente(NumeroReglementAModifier)
        End If
    End Sub

    Private Sub gVentes_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gVentes.MouseClick
        Dim I As Integer = 0
        Dim index As Integer = 0

        NumeroVenteAAfficher = gVentes(gVentes.Row, "NumeroVente")
        NumeroReglementAModifier = 0
        AfficherVente(0)
        For I = 0 To gVentes.RowCount - 1
            If gVentes(I, "NumeroVente") = NumeroVenteAAfficher Then
                index = I
            End If
        Next

        gVentes.MoveRelative(index)
        gVentes.HighLightRowStyle.BackColor = Color.Lime
        AfficherReglement(NumeroVenteAAfficher)

    End Sub

    Private Sub bSupprimerReglement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(4, "SUPPRESSION_REGLEMENT_MUTUELLE") = "False" Then
            Exit Sub
        Else
            CodeOperateur = ControleDAcces(4, "SUPPRESSION_REGLEMENT_MUTUELLE")
        End If
        If CodeOperateur = "" Then
            CodeOperateur = CodeUtilisateur
        End If


        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = "SELECT DemandeMotDePasse FROM [POINTS_CONTROLE] WHERE CodePointControle LIKE 'SUPPRESSION_REGLEMENT_MUTUELLE' AND CodeModule=4"

        'Try
        '    DemandePotDePasse = cmd.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'If DemandePotDePasse = True Then
        '    '------------------------------ demande du mot de passe
        '    Dim myMotDePasse As New fMotDePasse
        '    myMotDePasse.ShowDialog()

        '    ConfirmerEnregistrer = fMotDePasse.Confirmer
        '    CodeOperateur = fMotDePasse.CodeOperateur

        '    myMotDePasse.Dispose()
        '    myMotDePasse.Close()

        '    If ConfirmerEnregistrer = False Then
        '        Exit Sub
        '    End If

        '    cmd.Connection = ConnectionServeur
        '    cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'SUPPRESSION_REGLEMENT_MUTUELLE' AND CodeUtilisateur='" + CodeOperateur + "'"

        '    Try
        '        If cmd.ExecuteScalar() < 1 Then
        '            MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
        '            Exit Sub
        '        End If
        '    Catch ex As Exception
        '        Console.WriteLine(ex.Message)
        '    End Try
        'End If

        Dim NumeroDesVentesRegle As Integer = 0
        Dim StrSQL As String = ""
        Dim i As Integer = 0
        Dim CumulARetrancherLorsDuneSuppression As Double = 0.0
        Dim NumeroReleve As String = ""

        Connect()
        If gReglements.RowCount = 0 Then
            MsgBox("Liste Vide", MsgBoxStyle.OkOnly)
        Else
            If NumeroReglementAModifier = "" Then
                MsgBox("Vous devez sélectionner un règlement", MsgBoxStyle.OkOnly)
                Exit Sub
            End If

            If gReglements(gReglements.Row, "NumeroReglementMutuelle").ToString <> "" Then
                If gReglements(gReglements.Row, "Encaisse") = True Then
                    MsgBox("Il est interdit de modifier un réglement encaissé !", MsgBoxStyle.OkOnly)
                    Exit Sub
                End If
            Else : Exit Sub
            End If


            If MsgBox("Voulez vous vraiment supprimer ce Règlement  ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then

                'suppression du détails 
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM REGLEMENT_MUTUELLE_VENTE WHERE NumeroReglementMutuelle = " + NumeroReglementAModifier.ToString
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try

                'suppression du reglement
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM REGLEMENT_MUTUELLE WHERE NumeroReglementMutuelle = " + NumeroReglementAModifier.ToString
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try

            End If
        End If
        AfficherReglement(0)
    End Sub

    Private Sub Label10_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Label10.Click

    End Sub

    Private Sub fFicheMutuelle_Activated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Activated
        If ajoutmodif = "A" Then
            tCodeMutuelle.Focus()
        Else
            tNomMutuelle.Focus()
        End If
    End Sub

    Private Sub fFicheMutuelle_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler
    End Sub

    Private Sub bReglement_Click(sender As Object, e As EventArgs) Handles bReglement.Click
        Dim I As Integer = 0
        Dim MontantDuReglement As Double = 0.0
        Dim cmdReleve As New SqlCommand
        Dim StrSQL As String = ""
        Dim NumeroReglement As Integer = 0

        Dim CodeNatureReglement As Integer = 0
        Dim DateEcheanceReglement As String = ""
        Dim CodeBanque As String = ""
        Dim NumeroCheque As String = ""
        Dim LibelleReglement As String = ""
        Dim NomInscritSurLeCheque As String = ""
        Dim Montant As Double = 0.0
        Dim Encaisse As Boolean = False
        Dim ConfirmerReglement As Boolean = False
        Dim CodeOperateur As String = ""

        Dim MontantRegle As Double = 0.0

        Dim ReglementNonVide As Boolean = False


        Dim MyReglemnt As New fInformationReglement
        MyReglemnt.Montant = MontantDuReglement
        MyReglemnt.tMontant.Enabled = True
        MyReglemnt.ShowDialog()

        CodeNatureReglement = MyReglemnt.CodeNatureReglement
        DateEcheanceReglement = MyReglemnt.DateEcheanceReglement
        CodeBanque = MyReglemnt.CodeBanque
        NumeroCheque = MyReglemnt.NumeroCheque
        LibelleReglement = MyReglemnt.LibelleReglement
        NomInscritSurLeCheque = MyReglemnt.NomInscritSurLeCheque
        Montant = MyReglemnt.Montant
        Encaisse = MyReglemnt.Encaisse
        CodeOperateur = MyReglemnt.CodeOperateurLocal

        ConfirmerReglement = MyReglemnt.ConfirmerReglement


        MyReglemnt.Dispose()
        MyReglemnt.Close()

      

        'initialisation d'un nouveau règlement 
        StrSQL = " SELECT max([NumeroReglementMutuelle]) FROM REGLEMENT_MUTUELLE"
        cmdReleve.Connection = ConnectionServeur
        cmdReleve.CommandText = StrSQL
        Try
            NumeroReglement = cmdReleve.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        NumeroReglement = NumeroReglement + 1

        StrSQL = "INSERT INTO REGLEMENT_MUTUELLE " + _
                "(""NumeroReglementMutuelle"",""LibelleReglement"",""CodeNatureReglement""" + _
                ",""Date"",""DateEcheance"",""Montant"",""NumeroCheque"",""LibellePoste""" + _
                ",""NomInscritSurLeCheque"",""CodeMutuelle"",""CodeBanque"",""Vider"",""Encaisse"",""CodePersonnel"") " + _
                " VALUES('" + NumeroReglement.ToString + _
                "','" + LibelleReglement + _
                "','" + CodeNatureReglement.ToString + _
                "','" + System.DateTime.Now.ToString + _
                "','" + DateEcheanceReglement.ToString + _
                "','" + Montant.ToString + _
                "','" + NumeroCheque + _
                "','" + System.Environment.GetEnvironmentVariable("Poste") + _
                "','" + NomInscritSurLeCheque + _
                "','" + tCodeMutuelle.Text + _
                "'," + CodeBanque.ToString + _
                ",'" + "False" + _
                "','" + "true" + _
                "','" + CodeOperateur.ToString + " ')"

        cmdReleve.Connection = ConnectionServeur
        cmdReleve.CommandText = StrSQL
        Try
            cmdReleve.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

    End Sub
End Class