﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using System.Data.Entity;

namespace Data.StockManagement
{
    public class RepositoryInventory 
    {

        StockManagementEntities _StockManagementEntities = new StockManagementEntities();

        public bool Add(Data.StockManagement.INVENTAIRE inventaire, List<Data.StockManagement.LOT_ARTICLE> lotArticle)
        {
            try
            {
                _StockManagementEntities.INVENTAIRE.AddObject(inventaire);

                object OriginalItem;
                System.Data.EntityKey key;
                //foreach (Data.StockManagement.LOT_ARTICLE _item in lotArticle)
                //{
                //    key = _StockManagementEntities.CreateEntityKey(_StockManagementEntities.LOT_ARTICLE.EntitySet.Name, _item);
                //    if (_StockManagementEntities.CreateObjectSet<Data.StockManagement.LOT_ARTICLE>().Context.TryGetObjectByKey(key, out OriginalItem))
                //    {
                //        _StockManagementEntities.CreateObjectSet<Data.StockManagement.LOT_ARTICLE>().ApplyCurrentValues(_item);
                //    }
                //    else
                //    {
                //        _StockManagementEntities.CreateObjectSet<Data.StockManagement.LOT_ARTICLE>().Attach(_item);
                //        _StockManagementEntities.CreateObjectSet<Data.StockManagement.LOT_ARTICLE>().ApplyCurrentValues(_item);
                //    }
                //}
                _StockManagementEntities.SaveChanges();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public IQueryable<Data.StockManagement.INVENTAIRE> GetAllInventory()
        {
            return _StockManagementEntities.INVENTAIRE;
        }

        public List<V_List_Inventaire> GetListInventory()
        {
            return _StockManagementEntities.P_List_Inventaire(true).ToList();
        }

        public List<V_List_Inventaire> GetListInventoryByNum(string numero)
        {
            return _StockManagementEntities.P_List_Inventaire(true).Where(item => item.NumeroInventaire == numero).ToList();
        }

        public List<V_List_ArticlesRecherche> GetListArticle()
        {
            return _StockManagementEntities.P_List_ArticlesRecherche().ToList();
        }

        public List<V_List_Inventaire> GetListArticleResearchInventory()
        {
            return _StockManagementEntities.P_List_Inventaire(false).ToList();
        }

        public void FillNewInventory(System.Data.Objects.DataClasses.EntityCollection<Data.StockManagement.V_List_Inventaire> vInventroy, string section, string debutIntervalle, string finIntervalle, string forme, string categorie, string laboratoire, string rayon)
        {
            foreach (Data.StockManagement.V_List_NouvelInventaire Item in _StockManagementEntities.P_List_NouvelInventaire(section, debutIntervalle, finIntervalle, forme, categorie, laboratoire, rayon).ToList())
            {
                vInventroy.Add(new V_List_Inventaire()
                {
                    NumeroInventaire = Item.NumeroInventaire,
                    CodeArticle = Item.CodeArticle,
                    CodeABarre = Item.CodeABarre,
                    Designation = Item.Designation,
                    StockInitial = Item.StockInitial,
                    StockActuel = Item.StockActuel,
                    PrixVenteTTC = Item.PrixVenteTTC,
                    PrixAchatTTC = Item.PrixAchatTTC,
                    TotalAchatTTC = Item.TotalAchatTTC,
                    QuantiteUnitaire = Item.QuantiteUnitaire
                });
            }
        }

        public List<V_List_LotArticleInventaire> GetListLotArticle(string codeArticle, string debutIntervalle, string finIntervalle, string forme, string categorie, string laboratoire, string rayon)
        {
            return _StockManagementEntities.P_List_LotArticleInventaire(codeArticle, debutIntervalle, finIntervalle, forme, categorie, laboratoire, rayon).ToList();
        }

        public void FillNewLotArticle(List<Data.StockManagement.LOT_ARTICLE> lotArticle, string codeArticle, string debutIntervalle, string finIntervalle, string forme, string categorie, string laboratoire, string rayon)
        {
            if (codeArticle == "")
            {
                foreach (Data.StockManagement.V_List_LotArticleInventaire Item in _StockManagementEntities.P_List_LotArticleInventaire(codeArticle, debutIntervalle, finIntervalle, forme, categorie, laboratoire, rayon).ToList())
                {
                    lotArticle.Add(new LOT_ARTICLE()
                    {
                        NumeroLotArticle = Item.NumeroLotArticle,
                        CodeArticle = Item.CodeArticle,
                        QteLotArticle = Convert.ToInt32(Item.QteLotArticle),
                        DatePeremptionArticle = Item.DatePeremptionArticle
                    });
                }
            }
            else
            {
                foreach (Data.StockManagement.LOT_ARTICLE Item in _StockManagementEntities.LOT_ARTICLE.Where(Item => Item.CodeArticle == codeArticle).ToList())
                {
                    lotArticle.Add(new LOT_ARTICLE()
                    {
                        NumeroLotArticle = Item.NumeroLotArticle,
                        CodeArticle = Item.CodeArticle,
                        QteLotArticle = Convert.ToInt32(Item.QteLotArticle),
                        DatePeremptionArticle = Item.DatePeremptionArticle
                    });
                }
            }
        }
    }
}
