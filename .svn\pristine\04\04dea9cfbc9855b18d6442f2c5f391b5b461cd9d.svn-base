﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fFicheArticle111
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fFicheArticle111))
        Me.Panel = New System.Windows.Forms.Panel
        Me.bAnnuler = New C1.Win.C1Input.C1Button
        Me.bConfirmer = New C1.Win.C1Input.C1Button
        Me.GroupBox1 = New System.Windows.Forms.GroupBox
        Me.GroupBox9 = New System.Windows.Forms.GroupBox
        Me.lOperateur = New System.Windows.Forms.Label
        Me.Label29 = New System.Windows.Forms.Label
        Me.cmbSituation = New C1.Win.C1List.C1Combo
        Me.Label13 = New System.Windows.Forms.Label
        Me.GroupBox8 = New System.Windows.Forms.GroupBox
        Me.tTarifReference = New C1.Win.C1Input.C1TextBox
        Me.Label22 = New System.Windows.Forms.Label
        Me.Label24 = New System.Windows.Forms.Label
        Me.chkPriseEnCharge = New System.Windows.Forms.CheckBox
        Me.tCodePCT = New C1.Win.C1Input.C1TextBox
        Me.Label9 = New System.Windows.Forms.Label
        Me.Label21 = New System.Windows.Forms.Label
        Me.chkAccordPrealable = New System.Windows.Forms.CheckBox
        Me.cmbCategorieCnam = New C1.Win.C1List.C1Combo
        Me.Label23 = New System.Windows.Forms.Label
        Me.GroupBox7 = New System.Windows.Forms.GroupBox
        Me.lQuantiteDernierCommande = New System.Windows.Forms.Label
        Me.lDateDernierCommande = New System.Windows.Forms.Label
        Me.Label28 = New System.Windows.Forms.Label
        Me.Label18 = New System.Windows.Forms.Label
        Me.tStockInitial = New C1.Win.C1Input.C1TextBox
        Me.tDateInitial = New C1.Win.C1Input.C1TextBox
        Me.Label19 = New System.Windows.Forms.Label
        Me.Label20 = New System.Windows.Forms.Label
        Me.GroupBox4 = New System.Windows.Forms.GroupBox
        Me.Label27 = New System.Windows.Forms.Label
        Me.chkExonere = New System.Windows.Forms.CheckBox
        Me.tPrixVenteTTC = New C1.Win.C1Input.C1TextBox
        Me.tHR = New C1.Win.C1Input.C1TextBox
        Me.tPrixVenteHT = New C1.Win.C1Input.C1TextBox
        Me.tMarge = New C1.Win.C1Input.C1TextBox
        Me.tPrixAchatTTC = New C1.Win.C1Input.C1TextBox
        Me.tTVA = New C1.Win.C1Input.C1TextBox
        Me.tPrixAchatHT = New C1.Win.C1Input.C1TextBox
        Me.Label7 = New System.Windows.Forms.Label
        Me.Label6 = New System.Windows.Forms.Label
        Me.Label5 = New System.Windows.Forms.Label
        Me.Label4 = New System.Windows.Forms.Label
        Me.Label3 = New System.Windows.Forms.Label
        Me.Label2 = New System.Windows.Forms.Label
        Me.Label1 = New System.Windows.Forms.Label
        Me.STOCK = New System.Windows.Forms.GroupBox
        Me.lStock = New System.Windows.Forms.Label
        Me.GroupBox5 = New System.Windows.Forms.GroupBox
        Me.Label25 = New System.Windows.Forms.Label
        Me.chkSansCodeAbarre = New System.Windows.Forms.CheckBox
        Me.Label17 = New System.Windows.Forms.Label
        Me.chkSansVignette = New System.Windows.Forms.CheckBox
        Me.GroupBox3 = New System.Windows.Forms.GroupBox
        Me.tSection = New C1.Win.C1Input.C1TextBox
        Me.Label10 = New System.Windows.Forms.Label
        Me.cmbRayon = New C1.Win.C1List.C1Combo
        Me.cmbLaboratoire = New C1.Win.C1List.C1Combo
        Me.Label12 = New System.Windows.Forms.Label
        Me.cmbCategorie = New C1.Win.C1List.C1Combo
        Me.tDosage = New C1.Win.C1Input.C1TextBox
        Me.Label11 = New System.Windows.Forms.Label
        Me.cmbTableau = New C1.Win.C1List.C1Combo
        Me.cmbForme = New C1.Win.C1List.C1Combo
        Me.LFormeArticle = New System.Windows.Forms.Label
        Me.Label8 = New System.Windows.Forms.Label
        Me.lLaboratoire = New System.Windows.Forms.Label
        Me.LCategorieArticle = New System.Windows.Forms.Label
        Me.GroupBox6 = New System.Windows.Forms.GroupBox
        Me.tContenance = New C1.Win.C1Input.C1TextBox
        Me.tQuantiteACommander = New C1.Win.C1Input.C1TextBox
        Me.Label26 = New System.Windows.Forms.Label
        Me.tQuantiteUnitaire = New C1.Win.C1Input.C1TextBox
        Me.Label14 = New System.Windows.Forms.Label
        Me.Label16 = New System.Windows.Forms.Label
        Me.tstockAlert = New C1.Win.C1Input.C1TextBox
        Me.Label15 = New System.Windows.Forms.Label
        Me.GroupBox2 = New System.Windows.Forms.GroupBox
        Me.lTest = New System.Windows.Forms.Label
        Me.tDesignation = New C1.Win.C1Input.C1TextBox
        Me.tCodeArticle = New C1.Win.C1Input.C1TextBox
        Me.LNomClient = New System.Windows.Forms.Label
        Me.LNumeroClient = New System.Windows.Forms.Label
        Me.tDateAlerte = New C1.Win.C1Input.C1TextBox
        Me.Label30 = New System.Windows.Forms.Label
        Me.Panel.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        Me.GroupBox9.SuspendLayout()
        CType(Me.cmbSituation, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox8.SuspendLayout()
        CType(Me.tTarifReference, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodePCT, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbCategorieCnam, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox7.SuspendLayout()
        CType(Me.tStockInitial, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDateInitial, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox4.SuspendLayout()
        CType(Me.tPrixVenteTTC, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tHR, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tPrixVenteHT, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tMarge, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tPrixAchatTTC, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tTVA, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tPrixAchatHT, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.STOCK.SuspendLayout()
        Me.GroupBox5.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        CType(Me.tSection, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbRayon, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbLaboratoire, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbCategorie, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDosage, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbTableau, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbForme, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox6.SuspendLayout()
        CType(Me.tContenance, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tQuantiteACommander, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tQuantiteUnitaire, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tstockAlert, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox2.SuspendLayout()
        CType(Me.tDesignation, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodeArticle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDateAlerte, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.bConfirmer)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1028, 566)
        Me.Panel.TabIndex = 1
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler21
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(913, 80)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(102, 51)
        Me.bAnnuler.TabIndex = 2
        Me.bAnnuler.Text = "Annuler"
        Me.bAnnuler.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bConfirmer
        '
        Me.bConfirmer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bConfirmer.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bConfirmer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmer.Location = New System.Drawing.Point(913, 19)
        Me.bConfirmer.Name = "bConfirmer"
        Me.bConfirmer.Size = New System.Drawing.Size(102, 51)
        Me.bConfirmer.TabIndex = 1
        Me.bConfirmer.Text = "Confirmer"
        Me.bConfirmer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bConfirmer.UseVisualStyleBackColor = True
        Me.bConfirmer.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.bConfirmer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.GroupBox9)
        Me.GroupBox1.Controls.Add(Me.GroupBox8)
        Me.GroupBox1.Controls.Add(Me.GroupBox7)
        Me.GroupBox1.Controls.Add(Me.GroupBox4)
        Me.GroupBox1.Controls.Add(Me.STOCK)
        Me.GroupBox1.Controls.Add(Me.GroupBox5)
        Me.GroupBox1.Controls.Add(Me.GroupBox3)
        Me.GroupBox1.Controls.Add(Me.GroupBox6)
        Me.GroupBox1.Controls.Add(Me.GroupBox2)
        Me.GroupBox1.Location = New System.Drawing.Point(12, 12)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(883, 452)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        '
        'GroupBox9
        '
        Me.GroupBox9.Controls.Add(Me.lOperateur)
        Me.GroupBox9.Controls.Add(Me.Label29)
        Me.GroupBox9.Controls.Add(Me.cmbSituation)
        Me.GroupBox9.Controls.Add(Me.Label13)
        Me.GroupBox9.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox9.Location = New System.Drawing.Point(6, 302)
        Me.GroupBox9.Name = "GroupBox9"
        Me.GroupBox9.Size = New System.Drawing.Size(515, 45)
        Me.GroupBox9.TabIndex = 3
        Me.GroupBox9.TabStop = False
        '
        'lOperateur
        '
        Me.lOperateur.AutoSize = True
        Me.lOperateur.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lOperateur.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lOperateur.Location = New System.Drawing.Point(379, 19)
        Me.lOperateur.Name = "lOperateur"
        Me.lOperateur.Size = New System.Drawing.Size(12, 15)
        Me.lOperateur.TabIndex = 57
        Me.lOperateur.Text = "-"
        '
        'Label29
        '
        Me.Label29.AutoSize = True
        Me.Label29.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label29.Location = New System.Drawing.Point(316, 19)
        Me.Label29.Name = "Label29"
        Me.Label29.Size = New System.Drawing.Size(54, 13)
        Me.Label29.TabIndex = 56
        Me.Label29.Text = "Opérateur"
        '
        'cmbSituation
        '
        Me.cmbSituation.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbSituation.Caption = ""
        Me.cmbSituation.CaptionHeight = 17
        Me.cmbSituation.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbSituation.ColumnCaptionHeight = 17
        Me.cmbSituation.ColumnFooterHeight = 17
        Me.cmbSituation.ContentHeight = 16
        Me.cmbSituation.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbSituation.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbSituation.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbSituation.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbSituation.EditorHeight = 16
        Me.cmbSituation.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbSituation.Images.Add(CType(resources.GetObject("cmbSituation.Images"), System.Drawing.Image))
        Me.cmbSituation.ItemHeight = 15
        Me.cmbSituation.Location = New System.Drawing.Point(74, 14)
        Me.cmbSituation.MatchEntryTimeout = CType(2000, Long)
        Me.cmbSituation.MaxDropDownItems = CType(5, Short)
        Me.cmbSituation.MaxLength = 32767
        Me.cmbSituation.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbSituation.Name = "cmbSituation"
        Me.cmbSituation.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbSituation.Size = New System.Drawing.Size(154, 22)
        Me.cmbSituation.TabIndex = 0
        Me.cmbSituation.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbSituation.PropBag = resources.GetString("cmbSituation.PropBag")
        '
        'Label13
        '
        Me.Label13.AutoSize = True
        Me.Label13.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label13.Location = New System.Drawing.Point(20, 18)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(48, 13)
        Me.Label13.TabIndex = 54
        Me.Label13.Text = "Situation"
        '
        'GroupBox8
        '
        Me.GroupBox8.Controls.Add(Me.tTarifReference)
        Me.GroupBox8.Controls.Add(Me.Label22)
        Me.GroupBox8.Controls.Add(Me.Label24)
        Me.GroupBox8.Controls.Add(Me.chkPriseEnCharge)
        Me.GroupBox8.Controls.Add(Me.tCodePCT)
        Me.GroupBox8.Controls.Add(Me.Label9)
        Me.GroupBox8.Controls.Add(Me.Label21)
        Me.GroupBox8.Controls.Add(Me.chkAccordPrealable)
        Me.GroupBox8.Controls.Add(Me.cmbCategorieCnam)
        Me.GroupBox8.Controls.Add(Me.Label23)
        Me.GroupBox8.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox8.Location = New System.Drawing.Point(6, 199)
        Me.GroupBox8.Name = "GroupBox8"
        Me.GroupBox8.Size = New System.Drawing.Size(515, 98)
        Me.GroupBox8.TabIndex = 2
        Me.GroupBox8.TabStop = False
        Me.GroupBox8.Text = "CNAM"
        '
        'tTarifReference
        '
        Me.tTarifReference.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTarifReference.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tTarifReference.Location = New System.Drawing.Point(208, 67)
        Me.tTarifReference.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tTarifReference.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tTarifReference.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tTarifReference.Name = "tTarifReference"
        Me.tTarifReference.Size = New System.Drawing.Size(154, 18)
        Me.tTarifReference.TabIndex = 4
        Me.tTarifReference.Tag = Nothing
        Me.tTarifReference.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tTarifReference.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label22
        '
        Me.Label22.AutoSize = True
        Me.Label22.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label22.Location = New System.Drawing.Point(85, 68)
        Me.Label22.Name = "Label22"
        Me.Label22.Size = New System.Drawing.Size(99, 13)
        Me.Label22.TabIndex = 50
        Me.Label22.Text = "Tarif de références "
        '
        'Label24
        '
        Me.Label24.AutoSize = True
        Me.Label24.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label24.Location = New System.Drawing.Point(305, 45)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(84, 13)
        Me.Label24.TabIndex = 59
        Me.Label24.Text = "Prise en charge "
        '
        'chkPriseEnCharge
        '
        Me.chkPriseEnCharge.AutoSize = True
        Me.chkPriseEnCharge.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chkPriseEnCharge.Location = New System.Drawing.Point(401, 47)
        Me.chkPriseEnCharge.Name = "chkPriseEnCharge"
        Me.chkPriseEnCharge.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkPriseEnCharge.Size = New System.Drawing.Size(15, 14)
        Me.chkPriseEnCharge.TabIndex = 3
        Me.chkPriseEnCharge.UseVisualStyleBackColor = True
        '
        'tCodePCT
        '
        Me.tCodePCT.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodePCT.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodePCT.Location = New System.Drawing.Point(88, 19)
        Me.tCodePCT.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tCodePCT.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tCodePCT.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tCodePCT.Name = "tCodePCT"
        Me.tCodePCT.Size = New System.Drawing.Size(99, 18)
        Me.tCodePCT.TabIndex = 0
        Me.tCodePCT.Tag = Nothing
        Me.tCodePCT.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodePCT.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.Location = New System.Drawing.Point(16, 22)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(56, 13)
        Me.Label9.TabIndex = 57
        Me.Label9.Text = "Code PCT"
        '
        'Label21
        '
        Me.Label21.AutoSize = True
        Me.Label21.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label21.Location = New System.Drawing.Point(34, 45)
        Me.Label21.Name = "Label21"
        Me.Label21.Size = New System.Drawing.Size(87, 13)
        Me.Label21.TabIndex = 55
        Me.Label21.Text = "Accord préalable"
        '
        'chkAccordPrealable
        '
        Me.chkAccordPrealable.AutoSize = True
        Me.chkAccordPrealable.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chkAccordPrealable.Location = New System.Drawing.Point(140, 46)
        Me.chkAccordPrealable.Name = "chkAccordPrealable"
        Me.chkAccordPrealable.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkAccordPrealable.Size = New System.Drawing.Size(15, 14)
        Me.chkAccordPrealable.TabIndex = 2
        Me.chkAccordPrealable.UseVisualStyleBackColor = True
        '
        'cmbCategorieCnam
        '
        Me.cmbCategorieCnam.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbCategorieCnam.Caption = ""
        Me.cmbCategorieCnam.CaptionHeight = 17
        Me.cmbCategorieCnam.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbCategorieCnam.ColumnCaptionHeight = 17
        Me.cmbCategorieCnam.ColumnFooterHeight = 17
        Me.cmbCategorieCnam.ColumnWidth = 100
        Me.cmbCategorieCnam.ContentHeight = 16
        Me.cmbCategorieCnam.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbCategorieCnam.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbCategorieCnam.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbCategorieCnam.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbCategorieCnam.EditorHeight = 16
        Me.cmbCategorieCnam.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbCategorieCnam.Images.Add(CType(resources.GetObject("cmbCategorieCnam.Images"), System.Drawing.Image))
        Me.cmbCategorieCnam.ItemHeight = 15
        Me.cmbCategorieCnam.Location = New System.Drawing.Point(325, 17)
        Me.cmbCategorieCnam.MatchEntryTimeout = CType(2000, Long)
        Me.cmbCategorieCnam.MaxDropDownItems = CType(5, Short)
        Me.cmbCategorieCnam.MaxLength = 32767
        Me.cmbCategorieCnam.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbCategorieCnam.Name = "cmbCategorieCnam"
        Me.cmbCategorieCnam.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbCategorieCnam.Size = New System.Drawing.Size(154, 22)
        Me.cmbCategorieCnam.TabIndex = 1
        Me.cmbCategorieCnam.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbCategorieCnam.PropBag = resources.GetString("cmbCategorieCnam.PropBag")
        '
        'Label23
        '
        Me.Label23.AutoSize = True
        Me.Label23.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label23.Location = New System.Drawing.Point(228, 22)
        Me.Label23.Name = "Label23"
        Me.Label23.Size = New System.Drawing.Size(86, 13)
        Me.Label23.TabIndex = 35
        Me.Label23.Text = "Catégorie CNAM"
        '
        'GroupBox7
        '
        Me.GroupBox7.Controls.Add(Me.lQuantiteDernierCommande)
        Me.GroupBox7.Controls.Add(Me.lDateDernierCommande)
        Me.GroupBox7.Controls.Add(Me.Label28)
        Me.GroupBox7.Controls.Add(Me.Label18)
        Me.GroupBox7.Controls.Add(Me.tStockInitial)
        Me.GroupBox7.Controls.Add(Me.tDateInitial)
        Me.GroupBox7.Controls.Add(Me.Label19)
        Me.GroupBox7.Controls.Add(Me.Label20)
        Me.GroupBox7.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox7.Location = New System.Drawing.Point(527, 249)
        Me.GroupBox7.Name = "GroupBox7"
        Me.GroupBox7.Size = New System.Drawing.Size(334, 99)
        Me.GroupBox7.TabIndex = 6
        Me.GroupBox7.TabStop = False
        Me.GroupBox7.Text = "STOCK"
        '
        'lQuantiteDernierCommande
        '
        Me.lQuantiteDernierCommande.AutoSize = True
        Me.lQuantiteDernierCommande.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lQuantiteDernierCommande.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lQuantiteDernierCommande.Location = New System.Drawing.Point(176, 75)
        Me.lQuantiteDernierCommande.Name = "lQuantiteDernierCommande"
        Me.lQuantiteDernierCommande.Size = New System.Drawing.Size(12, 15)
        Me.lQuantiteDernierCommande.TabIndex = 60
        Me.lQuantiteDernierCommande.Text = "-"
        '
        'lDateDernierCommande
        '
        Me.lDateDernierCommande.AutoSize = True
        Me.lDateDernierCommande.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lDateDernierCommande.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDateDernierCommande.Location = New System.Drawing.Point(176, 56)
        Me.lDateDernierCommande.Name = "lDateDernierCommande"
        Me.lDateDernierCommande.Size = New System.Drawing.Size(12, 15)
        Me.lDateDernierCommande.TabIndex = 58
        Me.lDateDernierCommande.Text = "-"
        '
        'Label28
        '
        Me.Label28.AutoSize = True
        Me.Label28.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label28.Location = New System.Drawing.Point(5, 75)
        Me.Label28.Name = "Label28"
        Me.Label28.Size = New System.Drawing.Size(169, 13)
        Me.Label28.TabIndex = 59
        Me.Label28.Text = "Quantité de la derniere commande"
        '
        'Label18
        '
        Me.Label18.AutoSize = True
        Me.Label18.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label18.Location = New System.Drawing.Point(5, 56)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(152, 13)
        Me.Label18.TabIndex = 57
        Me.Label18.Text = "Date de la derniere commande"
        '
        'tStockInitial
        '
        Me.tStockInitial.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tStockInitial.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tStockInitial.Location = New System.Drawing.Point(177, 12)
        Me.tStockInitial.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tStockInitial.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tStockInitial.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tStockInitial.Name = "tStockInitial"
        Me.tStockInitial.Size = New System.Drawing.Size(139, 18)
        Me.tStockInitial.TabIndex = 0
        Me.tStockInitial.Tag = Nothing
        Me.tStockInitial.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tStockInitial.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tDateInitial
        '
        Me.tDateInitial.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDateInitial.EditMask = "##/##/####"
        Me.tDateInitial.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDateInitial.Location = New System.Drawing.Point(177, 32)
        Me.tDateInitial.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tDateInitial.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tDateInitial.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tDateInitial.Name = "tDateInitial"
        Me.tDateInitial.Size = New System.Drawing.Size(139, 18)
        Me.tDateInitial.TabIndex = 1
        Me.tDateInitial.Tag = Nothing
        Me.tDateInitial.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDateInitial.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label19
        '
        Me.Label19.AutoSize = True
        Me.Label19.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label19.Location = New System.Drawing.Point(5, 36)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(54, 13)
        Me.Label19.TabIndex = 50
        Me.Label19.Text = "Date intial"
        '
        'Label20
        '
        Me.Label20.AutoSize = True
        Me.Label20.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label20.Location = New System.Drawing.Point(4, 18)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(61, 13)
        Me.Label20.TabIndex = 35
        Me.Label20.Text = "Stock initial"
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.Label27)
        Me.GroupBox4.Controls.Add(Me.chkExonere)
        Me.GroupBox4.Controls.Add(Me.tPrixVenteTTC)
        Me.GroupBox4.Controls.Add(Me.tHR)
        Me.GroupBox4.Controls.Add(Me.tPrixVenteHT)
        Me.GroupBox4.Controls.Add(Me.tMarge)
        Me.GroupBox4.Controls.Add(Me.tPrixAchatTTC)
        Me.GroupBox4.Controls.Add(Me.tTVA)
        Me.GroupBox4.Controls.Add(Me.tPrixAchatHT)
        Me.GroupBox4.Controls.Add(Me.Label7)
        Me.GroupBox4.Controls.Add(Me.Label6)
        Me.GroupBox4.Controls.Add(Me.Label5)
        Me.GroupBox4.Controls.Add(Me.Label4)
        Me.GroupBox4.Controls.Add(Me.Label3)
        Me.GroupBox4.Controls.Add(Me.Label2)
        Me.GroupBox4.Controls.Add(Me.Label1)
        Me.GroupBox4.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox4.Location = New System.Drawing.Point(6, 352)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(855, 90)
        Me.GroupBox4.TabIndex = 7
        Me.GroupBox4.TabStop = False
        Me.GroupBox4.Text = "PRIX"
        '
        'Label27
        '
        Me.Label27.AutoSize = True
        Me.Label27.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label27.Location = New System.Drawing.Point(340, 44)
        Me.Label27.Name = "Label27"
        Me.Label27.Size = New System.Drawing.Size(135, 13)
        Me.Label27.TabIndex = 4
        Me.Label27.Text = "Exonéré de TVA à la vente"
        '
        'chkExonere
        '
        Me.chkExonere.AutoSize = True
        Me.chkExonere.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chkExonere.Location = New System.Drawing.Point(488, 46)
        Me.chkExonere.Name = "chkExonere"
        Me.chkExonere.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkExonere.Size = New System.Drawing.Size(15, 14)
        Me.chkExonere.TabIndex = 60
        Me.chkExonere.UseVisualStyleBackColor = True
        '
        'tPrixVenteTTC
        '
        Me.tPrixVenteTTC.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tPrixVenteTTC.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tPrixVenteTTC.Location = New System.Drawing.Point(648, 64)
        Me.tPrixVenteTTC.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tPrixVenteTTC.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tPrixVenteTTC.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tPrixVenteTTC.Name = "tPrixVenteTTC"
        Me.tPrixVenteTTC.Size = New System.Drawing.Size(113, 18)
        Me.tPrixVenteTTC.TabIndex = 7
        Me.tPrixVenteTTC.Tag = Nothing
        Me.tPrixVenteTTC.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tPrixVenteTTC.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tHR
        '
        Me.tHR.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tHR.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tHR.Location = New System.Drawing.Point(381, 65)
        Me.tHR.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tHR.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tHR.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tHR.Name = "tHR"
        Me.tHR.Size = New System.Drawing.Size(113, 18)
        Me.tHR.TabIndex = 6
        Me.tHR.Tag = Nothing
        Me.tHR.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tHR.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tPrixVenteHT
        '
        Me.tPrixVenteHT.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tPrixVenteHT.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tPrixVenteHT.Location = New System.Drawing.Point(156, 65)
        Me.tPrixVenteHT.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tPrixVenteHT.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tPrixVenteHT.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tPrixVenteHT.Name = "tPrixVenteHT"
        Me.tPrixVenteHT.Size = New System.Drawing.Size(115, 18)
        Me.tPrixVenteHT.TabIndex = 5
        Me.tPrixVenteHT.Tag = Nothing
        Me.tPrixVenteHT.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tPrixVenteHT.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tMarge
        '
        Me.tMarge.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMarge.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tMarge.Location = New System.Drawing.Point(156, 42)
        Me.tMarge.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tMarge.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tMarge.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tMarge.Name = "tMarge"
        Me.tMarge.Size = New System.Drawing.Size(115, 18)
        Me.tMarge.TabIndex = 3
        Me.tMarge.Tag = Nothing
        Me.tMarge.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tMarge.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tPrixAchatTTC
        '
        Me.tPrixAchatTTC.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tPrixAchatTTC.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tPrixAchatTTC.Location = New System.Drawing.Point(648, 21)
        Me.tPrixAchatTTC.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tPrixAchatTTC.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tPrixAchatTTC.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tPrixAchatTTC.Name = "tPrixAchatTTC"
        Me.tPrixAchatTTC.Size = New System.Drawing.Size(113, 18)
        Me.tPrixAchatTTC.TabIndex = 2
        Me.tPrixAchatTTC.Tag = Nothing
        Me.tPrixAchatTTC.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tPrixAchatTTC.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tTVA
        '
        Me.tTVA.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTVA.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tTVA.Location = New System.Drawing.Point(381, 21)
        Me.tTVA.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tTVA.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tTVA.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tTVA.Name = "tTVA"
        Me.tTVA.Size = New System.Drawing.Size(113, 18)
        Me.tTVA.TabIndex = 1
        Me.tTVA.Tag = Nothing
        Me.tTVA.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tTVA.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tPrixAchatHT
        '
        Me.tPrixAchatHT.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tPrixAchatHT.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tPrixAchatHT.Location = New System.Drawing.Point(156, 21)
        Me.tPrixAchatHT.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tPrixAchatHT.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tPrixAchatHT.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tPrixAchatHT.Name = "tPrixAchatHT"
        Me.tPrixAchatHT.Size = New System.Drawing.Size(115, 18)
        Me.tPrixAchatHT.TabIndex = 0
        Me.tPrixAchatHT.Tag = Nothing
        Me.tPrixAchatHT.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tPrixAchatHT.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.Location = New System.Drawing.Point(337, 69)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(23, 13)
        Me.Label7.TabIndex = 40
        Me.Label7.Text = "HR"
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.Location = New System.Drawing.Point(22, 44)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(37, 13)
        Me.Label6.TabIndex = 38
        Me.Label6.Text = "Marge"
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.Location = New System.Drawing.Point(337, 23)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(28, 13)
        Me.Label5.TabIndex = 37
        Me.Label5.Text = "TVA"
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.Location = New System.Drawing.Point(563, 23)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(79, 13)
        Me.Label4.TabIndex = 36
        Me.Label4.Text = "Prix Achat TTC"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.Location = New System.Drawing.Point(563, 68)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(79, 13)
        Me.Label3.TabIndex = 35
        Me.Label3.Text = "Prix Vente TTC"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(21, 66)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(73, 13)
        Me.Label2.TabIndex = 34
        Me.Label2.Text = "Prix Vente HT"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(21, 23)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(73, 13)
        Me.Label1.TabIndex = 33
        Me.Label1.Text = "Prix Achat HT"
        '
        'STOCK
        '
        Me.STOCK.Controls.Add(Me.lStock)
        Me.STOCK.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.STOCK.Location = New System.Drawing.Point(533, 16)
        Me.STOCK.Name = "STOCK"
        Me.STOCK.Size = New System.Drawing.Size(324, 53)
        Me.STOCK.TabIndex = 59
        Me.STOCK.TabStop = False
        Me.STOCK.Text = "STOCK"
        '
        'lStock
        '
        Me.lStock.AutoSize = True
        Me.lStock.Font = New System.Drawing.Font("Microsoft Sans Serif", 24.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lStock.Location = New System.Drawing.Point(150, 10)
        Me.lStock.Name = "lStock"
        Me.lStock.Size = New System.Drawing.Size(28, 37)
        Me.lStock.TabIndex = 61
        Me.lStock.Text = "-"
        '
        'GroupBox5
        '
        Me.GroupBox5.Controls.Add(Me.Label25)
        Me.GroupBox5.Controls.Add(Me.chkSansCodeAbarre)
        Me.GroupBox5.Controls.Add(Me.Label17)
        Me.GroupBox5.Controls.Add(Me.chkSansVignette)
        Me.GroupBox5.Location = New System.Drawing.Point(527, 70)
        Me.GroupBox5.Name = "GroupBox5"
        Me.GroupBox5.Size = New System.Drawing.Size(334, 56)
        Me.GroupBox5.TabIndex = 4
        Me.GroupBox5.TabStop = False
        '
        'Label25
        '
        Me.Label25.AutoSize = True
        Me.Label25.Location = New System.Drawing.Point(34, 32)
        Me.Label25.Name = "Label25"
        Me.Label25.Size = New System.Drawing.Size(95, 13)
        Me.Label25.TabIndex = 57
        Me.Label25.Text = "Sans Code a barre"
        '
        'chkSansCodeAbarre
        '
        Me.chkSansCodeAbarre.AutoSize = True
        Me.chkSansCodeAbarre.Location = New System.Drawing.Point(221, 32)
        Me.chkSansCodeAbarre.Name = "chkSansCodeAbarre"
        Me.chkSansCodeAbarre.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkSansCodeAbarre.Size = New System.Drawing.Size(15, 14)
        Me.chkSansCodeAbarre.TabIndex = 1
        Me.chkSansCodeAbarre.UseVisualStyleBackColor = True
        '
        'Label17
        '
        Me.Label17.AutoSize = True
        Me.Label17.Location = New System.Drawing.Point(34, 15)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(73, 13)
        Me.Label17.TabIndex = 55
        Me.Label17.Text = "Sans Vignette"
        '
        'chkSansVignette
        '
        Me.chkSansVignette.AutoSize = True
        Me.chkSansVignette.Location = New System.Drawing.Point(221, 15)
        Me.chkSansVignette.Name = "chkSansVignette"
        Me.chkSansVignette.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkSansVignette.Size = New System.Drawing.Size(15, 14)
        Me.chkSansVignette.TabIndex = 0
        Me.chkSansVignette.UseVisualStyleBackColor = True
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.tSection)
        Me.GroupBox3.Controls.Add(Me.Label10)
        Me.GroupBox3.Controls.Add(Me.cmbRayon)
        Me.GroupBox3.Controls.Add(Me.cmbLaboratoire)
        Me.GroupBox3.Controls.Add(Me.Label12)
        Me.GroupBox3.Controls.Add(Me.cmbCategorie)
        Me.GroupBox3.Controls.Add(Me.tDosage)
        Me.GroupBox3.Controls.Add(Me.Label11)
        Me.GroupBox3.Controls.Add(Me.cmbTableau)
        Me.GroupBox3.Controls.Add(Me.cmbForme)
        Me.GroupBox3.Controls.Add(Me.LFormeArticle)
        Me.GroupBox3.Controls.Add(Me.Label8)
        Me.GroupBox3.Controls.Add(Me.lLaboratoire)
        Me.GroupBox3.Controls.Add(Me.LCategorieArticle)
        Me.GroupBox3.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox3.Location = New System.Drawing.Point(6, 77)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(515, 123)
        Me.GroupBox3.TabIndex = 1
        Me.GroupBox3.TabStop = False
        Me.GroupBox3.Text = "INFORMATIONS ARTICLE"
        '
        'tSection
        '
        Me.tSection.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tSection.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tSection.Location = New System.Drawing.Point(340, 76)
        Me.tSection.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tSection.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tSection.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tSection.Name = "tSection"
        Me.tSection.Size = New System.Drawing.Size(154, 18)
        Me.tSection.TabIndex = 53
        Me.tSection.Tag = Nothing
        Me.tSection.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tSection.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label10.Location = New System.Drawing.Point(287, 77)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(43, 13)
        Me.Label10.TabIndex = 54
        Me.Label10.Text = "Section"
        '
        'cmbRayon
        '
        Me.cmbRayon.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbRayon.Caption = ""
        Me.cmbRayon.CaptionHeight = 17
        Me.cmbRayon.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbRayon.ColumnCaptionHeight = 17
        Me.cmbRayon.ColumnFooterHeight = 17
        Me.cmbRayon.ContentHeight = 16
        Me.cmbRayon.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbRayon.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbRayon.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbRayon.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbRayon.EditorHeight = 16
        Me.cmbRayon.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbRayon.Images.Add(CType(resources.GetObject("cmbRayon.Images"), System.Drawing.Image))
        Me.cmbRayon.ItemHeight = 15
        Me.cmbRayon.Location = New System.Drawing.Point(340, 46)
        Me.cmbRayon.MatchEntryTimeout = CType(2000, Long)
        Me.cmbRayon.MaxDropDownItems = CType(5, Short)
        Me.cmbRayon.MaxLength = 32767
        Me.cmbRayon.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbRayon.Name = "cmbRayon"
        Me.cmbRayon.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbRayon.Size = New System.Drawing.Size(154, 22)
        Me.cmbRayon.TabIndex = 5
        Me.cmbRayon.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbRayon.PropBag = resources.GetString("cmbRayon.PropBag")
        '
        'cmbLaboratoire
        '
        Me.cmbLaboratoire.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbLaboratoire.Caption = ""
        Me.cmbLaboratoire.CaptionHeight = 17
        Me.cmbLaboratoire.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbLaboratoire.ColumnCaptionHeight = 17
        Me.cmbLaboratoire.ColumnFooterHeight = 17
        Me.cmbLaboratoire.ContentHeight = 16
        Me.cmbLaboratoire.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbLaboratoire.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbLaboratoire.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbLaboratoire.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbLaboratoire.EditorHeight = 16
        Me.cmbLaboratoire.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbLaboratoire.Images.Add(CType(resources.GetObject("cmbLaboratoire.Images"), System.Drawing.Image))
        Me.cmbLaboratoire.ItemHeight = 15
        Me.cmbLaboratoire.Location = New System.Drawing.Point(88, 71)
        Me.cmbLaboratoire.MatchEntryTimeout = CType(2000, Long)
        Me.cmbLaboratoire.MaxDropDownItems = CType(5, Short)
        Me.cmbLaboratoire.MaxLength = 32767
        Me.cmbLaboratoire.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbLaboratoire.Name = "cmbLaboratoire"
        Me.cmbLaboratoire.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbLaboratoire.Size = New System.Drawing.Size(154, 22)
        Me.cmbLaboratoire.TabIndex = 2
        Me.cmbLaboratoire.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbLaboratoire.PropBag = resources.GetString("cmbLaboratoire.PropBag")
        '
        'Label12
        '
        Me.Label12.AutoSize = True
        Me.Label12.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label12.Location = New System.Drawing.Point(286, 52)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(38, 13)
        Me.Label12.TabIndex = 52
        Me.Label12.Text = "Rayon"
        '
        'cmbCategorie
        '
        Me.cmbCategorie.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbCategorie.Caption = ""
        Me.cmbCategorie.CaptionHeight = 17
        Me.cmbCategorie.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbCategorie.ColumnCaptionHeight = 17
        Me.cmbCategorie.ColumnFooterHeight = 17
        Me.cmbCategorie.ContentHeight = 16
        Me.cmbCategorie.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbCategorie.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbCategorie.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbCategorie.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbCategorie.EditorHeight = 16
        Me.cmbCategorie.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbCategorie.Images.Add(CType(resources.GetObject("cmbCategorie.Images"), System.Drawing.Image))
        Me.cmbCategorie.ItemHeight = 15
        Me.cmbCategorie.Location = New System.Drawing.Point(88, 45)
        Me.cmbCategorie.MatchEntryTimeout = CType(2000, Long)
        Me.cmbCategorie.MaxDropDownItems = CType(5, Short)
        Me.cmbCategorie.MaxLength = 32767
        Me.cmbCategorie.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbCategorie.Name = "cmbCategorie"
        Me.cmbCategorie.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbCategorie.Size = New System.Drawing.Size(154, 22)
        Me.cmbCategorie.TabIndex = 1
        Me.cmbCategorie.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbCategorie.PropBag = resources.GetString("cmbCategorie.PropBag")
        '
        'tDosage
        '
        Me.tDosage.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDosage.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDosage.Location = New System.Drawing.Point(340, 23)
        Me.tDosage.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tDosage.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tDosage.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tDosage.Name = "tDosage"
        Me.tDosage.Size = New System.Drawing.Size(154, 18)
        Me.tDosage.TabIndex = 4
        Me.tDosage.Tag = Nothing
        Me.tDosage.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDosage.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label11.Location = New System.Drawing.Point(287, 27)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(44, 13)
        Me.Label11.TabIndex = 50
        Me.Label11.Text = "Dosage"
        '
        'cmbTableau
        '
        Me.cmbTableau.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbTableau.Caption = ""
        Me.cmbTableau.CaptionHeight = 17
        Me.cmbTableau.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbTableau.ColumnCaptionHeight = 17
        Me.cmbTableau.ColumnFooterHeight = 17
        Me.cmbTableau.ColumnWidth = 100
        Me.cmbTableau.ContentHeight = 16
        Me.cmbTableau.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbTableau.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbTableau.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbTableau.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbTableau.EditorHeight = 16
        Me.cmbTableau.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbTableau.Images.Add(CType(resources.GetObject("cmbTableau.Images"), System.Drawing.Image))
        Me.cmbTableau.ItemHeight = 15
        Me.cmbTableau.Location = New System.Drawing.Point(88, 95)
        Me.cmbTableau.MatchEntryTimeout = CType(2000, Long)
        Me.cmbTableau.MaxDropDownItems = CType(5, Short)
        Me.cmbTableau.MaxLength = 32767
        Me.cmbTableau.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbTableau.Name = "cmbTableau"
        Me.cmbTableau.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbTableau.Size = New System.Drawing.Size(154, 22)
        Me.cmbTableau.TabIndex = 3
        Me.cmbTableau.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbTableau.PropBag = resources.GetString("cmbTableau.PropBag")
        '
        'cmbForme
        '
        Me.cmbForme.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbForme.Caption = ""
        Me.cmbForme.CaptionHeight = 17
        Me.cmbForme.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbForme.ColumnCaptionHeight = 17
        Me.cmbForme.ColumnFooterHeight = 17
        Me.cmbForme.ContentHeight = 16
        Me.cmbForme.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbForme.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbForme.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbForme.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbForme.EditorHeight = 16
        Me.cmbForme.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbForme.Images.Add(CType(resources.GetObject("cmbForme.Images"), System.Drawing.Image))
        Me.cmbForme.ItemHeight = 15
        Me.cmbForme.Location = New System.Drawing.Point(88, 20)
        Me.cmbForme.MatchEntryTimeout = CType(2000, Long)
        Me.cmbForme.MaxDropDownItems = CType(5, Short)
        Me.cmbForme.MaxLength = 32767
        Me.cmbForme.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbForme.Name = "cmbForme"
        Me.cmbForme.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbForme.Size = New System.Drawing.Size(154, 22)
        Me.cmbForme.TabIndex = 0
        Me.cmbForme.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbForme.PropBag = resources.GetString("cmbForme.PropBag")
        '
        'LFormeArticle
        '
        Me.LFormeArticle.AutoSize = True
        Me.LFormeArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LFormeArticle.Location = New System.Drawing.Point(21, 27)
        Me.LFormeArticle.Name = "LFormeArticle"
        Me.LFormeArticle.Size = New System.Drawing.Size(36, 13)
        Me.LFormeArticle.TabIndex = 2
        Me.LFormeArticle.Text = "Forme"
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.Location = New System.Drawing.Point(19, 101)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(46, 13)
        Me.Label8.TabIndex = 35
        Me.Label8.Text = "Tableau"
        '
        'lLaboratoire
        '
        Me.lLaboratoire.AutoSize = True
        Me.lLaboratoire.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lLaboratoire.Location = New System.Drawing.Point(21, 78)
        Me.lLaboratoire.Name = "lLaboratoire"
        Me.lLaboratoire.Size = New System.Drawing.Size(60, 13)
        Me.lLaboratoire.TabIndex = 1
        Me.lLaboratoire.Text = "Laboratoire"
        '
        'LCategorieArticle
        '
        Me.LCategorieArticle.AutoSize = True
        Me.LCategorieArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LCategorieArticle.Location = New System.Drawing.Point(21, 51)
        Me.LCategorieArticle.Name = "LCategorieArticle"
        Me.LCategorieArticle.Size = New System.Drawing.Size(52, 13)
        Me.LCategorieArticle.TabIndex = 3
        Me.LCategorieArticle.Text = "Catégorie"
        '
        'GroupBox6
        '
        Me.GroupBox6.Controls.Add(Me.tDateAlerte)
        Me.GroupBox6.Controls.Add(Me.Label30)
        Me.GroupBox6.Controls.Add(Me.tContenance)
        Me.GroupBox6.Controls.Add(Me.tQuantiteACommander)
        Me.GroupBox6.Controls.Add(Me.Label26)
        Me.GroupBox6.Controls.Add(Me.tQuantiteUnitaire)
        Me.GroupBox6.Controls.Add(Me.Label14)
        Me.GroupBox6.Controls.Add(Me.Label16)
        Me.GroupBox6.Controls.Add(Me.tstockAlert)
        Me.GroupBox6.Controls.Add(Me.Label15)
        Me.GroupBox6.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox6.Location = New System.Drawing.Point(527, 125)
        Me.GroupBox6.Name = "GroupBox6"
        Me.GroupBox6.Size = New System.Drawing.Size(334, 122)
        Me.GroupBox6.TabIndex = 5
        Me.GroupBox6.TabStop = False
        Me.GroupBox6.Text = "QUANTITE"
        '
        'tContenance
        '
        Me.tContenance.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tContenance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tContenance.Location = New System.Drawing.Point(157, 34)
        Me.tContenance.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tContenance.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tContenance.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tContenance.Name = "tContenance"
        Me.tContenance.Size = New System.Drawing.Size(113, 18)
        Me.tContenance.TabIndex = 1
        Me.tContenance.Tag = Nothing
        Me.tContenance.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tContenance.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tQuantiteACommander
        '
        Me.tQuantiteACommander.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tQuantiteACommander.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tQuantiteACommander.Location = New System.Drawing.Point(157, 99)
        Me.tQuantiteACommander.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tQuantiteACommander.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tQuantiteACommander.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tQuantiteACommander.Name = "tQuantiteACommander"
        Me.tQuantiteACommander.Size = New System.Drawing.Size(113, 18)
        Me.tQuantiteACommander.TabIndex = 3
        Me.tQuantiteACommander.Tag = Nothing
        Me.tQuantiteACommander.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tQuantiteACommander.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label26
        '
        Me.Label26.AutoSize = True
        Me.Label26.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label26.Location = New System.Drawing.Point(32, 38)
        Me.Label26.Name = "Label26"
        Me.Label26.Size = New System.Drawing.Size(65, 13)
        Me.Label26.TabIndex = 63
        Me.Label26.Text = "Contenance"
        '
        'tQuantiteUnitaire
        '
        Me.tQuantiteUnitaire.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tQuantiteUnitaire.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tQuantiteUnitaire.Location = New System.Drawing.Point(157, 13)
        Me.tQuantiteUnitaire.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tQuantiteUnitaire.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tQuantiteUnitaire.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tQuantiteUnitaire.Name = "tQuantiteUnitaire"
        Me.tQuantiteUnitaire.Size = New System.Drawing.Size(113, 18)
        Me.tQuantiteUnitaire.TabIndex = 0
        Me.tQuantiteUnitaire.Tag = Nothing
        Me.tQuantiteUnitaire.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tQuantiteUnitaire.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label14
        '
        Me.Label14.AutoSize = True
        Me.Label14.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label14.Location = New System.Drawing.Point(32, 17)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(84, 13)
        Me.Label14.TabIndex = 55
        Me.Label14.Text = "Quantite unitaire"
        '
        'Label16
        '
        Me.Label16.AutoSize = True
        Me.Label16.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label16.Location = New System.Drawing.Point(33, 102)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(114, 13)
        Me.Label16.TabIndex = 61
        Me.Label16.Text = "Quantité à commander"
        '
        'tstockAlert
        '
        Me.tstockAlert.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tstockAlert.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tstockAlert.Location = New System.Drawing.Point(157, 56)
        Me.tstockAlert.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tstockAlert.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tstockAlert.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tstockAlert.Name = "tstockAlert"
        Me.tstockAlert.Size = New System.Drawing.Size(113, 18)
        Me.tstockAlert.TabIndex = 2
        Me.tstockAlert.Tag = Nothing
        Me.tstockAlert.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tstockAlert.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label15.Location = New System.Drawing.Point(32, 60)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(72, 13)
        Me.Label15.TabIndex = 59
        Me.Label15.Text = "Stock d'alerte"
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.lTest)
        Me.GroupBox2.Controls.Add(Me.tDesignation)
        Me.GroupBox2.Controls.Add(Me.tCodeArticle)
        Me.GroupBox2.Controls.Add(Me.LNomClient)
        Me.GroupBox2.Controls.Add(Me.LNumeroClient)
        Me.GroupBox2.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox2.Location = New System.Drawing.Point(6, 17)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(515, 60)
        Me.GroupBox2.TabIndex = 0
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "CODE - DESIGNATION"
        '
        'lTest
        '
        Me.lTest.AutoSize = True
        Me.lTest.BackColor = System.Drawing.Color.Transparent
        Me.lTest.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTest.ForeColor = System.Drawing.Color.Black
        Me.lTest.Location = New System.Drawing.Point(349, 16)
        Me.lTest.Name = "lTest"
        Me.lTest.Size = New System.Drawing.Size(19, 13)
        Me.lTest.TabIndex = 1
        Me.lTest.Text = "55"
        Me.lTest.Visible = False
        '
        'tDesignation
        '
        Me.tDesignation.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDesignation.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDesignation.Location = New System.Drawing.Point(228, 34)
        Me.tDesignation.Name = "tDesignation"
        Me.tDesignation.Size = New System.Drawing.Size(154, 18)
        Me.tDesignation.TabIndex = 1
        Me.tDesignation.Tag = Nothing
        Me.tDesignation.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDesignation.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tCodeArticle
        '
        Me.tCodeArticle.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodeArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodeArticle.Location = New System.Drawing.Point(228, 14)
        Me.tCodeArticle.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tCodeArticle.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tCodeArticle.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tCodeArticle.Name = "tCodeArticle"
        Me.tCodeArticle.Size = New System.Drawing.Size(115, 18)
        Me.tCodeArticle.TabIndex = 0
        Me.tCodeArticle.Tag = Nothing
        Me.tCodeArticle.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodeArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'LNomClient
        '
        Me.LNomClient.AutoSize = True
        Me.LNomClient.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LNomClient.Location = New System.Drawing.Point(93, 39)
        Me.LNomClient.Name = "LNomClient"
        Me.LNomClient.Size = New System.Drawing.Size(63, 13)
        Me.LNomClient.TabIndex = 1
        Me.LNomClient.Text = "Designation"
        '
        'LNumeroClient
        '
        Me.LNumeroClient.AutoSize = True
        Me.LNumeroClient.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LNumeroClient.Location = New System.Drawing.Point(93, 19)
        Me.LNumeroClient.Name = "LNumeroClient"
        Me.LNumeroClient.Size = New System.Drawing.Size(32, 13)
        Me.LNumeroClient.TabIndex = 0
        Me.LNumeroClient.Text = "Code"
        '
        'tDateAlerte
        '
        Me.tDateAlerte.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDateAlerte.Enabled = False
        Me.tDateAlerte.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDateAlerte.Location = New System.Drawing.Point(157, 78)
        Me.tDateAlerte.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tDateAlerte.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tDateAlerte.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tDateAlerte.Name = "tDateAlerte"
        Me.tDateAlerte.Size = New System.Drawing.Size(113, 18)
        Me.tDateAlerte.TabIndex = 64
        Me.tDateAlerte.Tag = Nothing
        Me.tDateAlerte.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDateAlerte.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label30
        '
        Me.Label30.AutoSize = True
        Me.Label30.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label30.Location = New System.Drawing.Point(33, 81)
        Me.Label30.Name = "Label30"
        Me.Label30.Size = New System.Drawing.Size(67, 13)
        Me.Label30.TabIndex = 65
        Me.Label30.Text = "Date d'alerte"
        '
        'fFicheArticle
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1028, 566)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fFicheArticle"
        Me.Text = "fFicheArticle"
        Me.Panel.ResumeLayout(False)
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox9.ResumeLayout(False)
        Me.GroupBox9.PerformLayout()
        CType(Me.cmbSituation, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox8.ResumeLayout(False)
        Me.GroupBox8.PerformLayout()
        CType(Me.tTarifReference, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodePCT, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbCategorieCnam, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox7.ResumeLayout(False)
        Me.GroupBox7.PerformLayout()
        CType(Me.tStockInitial, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDateInitial, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.tPrixVenteTTC, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tHR, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tPrixVenteHT, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tMarge, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tPrixAchatTTC, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tTVA, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tPrixAchatHT, System.ComponentModel.ISupportInitialize).EndInit()
        Me.STOCK.ResumeLayout(False)
        Me.STOCK.PerformLayout()
        Me.GroupBox5.ResumeLayout(False)
        Me.GroupBox5.PerformLayout()
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        CType(Me.tSection, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbRayon, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbLaboratoire, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbCategorie, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDosage, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbTableau, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbForme, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox6.ResumeLayout(False)
        Me.GroupBox6.PerformLayout()
        CType(Me.tContenance, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tQuantiteACommander, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tQuantiteUnitaire, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tstockAlert, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.tDesignation, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodeArticle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDateAlerte, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bConfirmer As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents STOCK As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox6 As System.Windows.Forms.GroupBox
    Friend WithEvents tQuantiteUnitaire As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents GroupBox5 As System.Windows.Forms.GroupBox
    Friend WithEvents chkSansVignette As System.Windows.Forms.CheckBox
    Friend WithEvents cmbTableau As C1.Win.C1List.C1Combo
    Friend WithEvents tDosage As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents tPrixVenteTTC As C1.Win.C1Input.C1TextBox
    Friend WithEvents tHR As C1.Win.C1Input.C1TextBox
    Friend WithEvents tPrixVenteHT As C1.Win.C1Input.C1TextBox
    Friend WithEvents tMarge As C1.Win.C1Input.C1TextBox
    Friend WithEvents tPrixAchatTTC As C1.Win.C1Input.C1TextBox
    Friend WithEvents tTVA As C1.Win.C1Input.C1TextBox
    Friend WithEvents tPrixAchatHT As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents cmbLaboratoire As C1.Win.C1List.C1Combo
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents cmbCategorie As C1.Win.C1List.C1Combo
    Friend WithEvents cmbForme As C1.Win.C1List.C1Combo
    Friend WithEvents LFormeArticle As System.Windows.Forms.Label
    Friend WithEvents lLaboratoire As System.Windows.Forms.Label
    Private WithEvents LCategorieArticle As System.Windows.Forms.Label
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents lTest As System.Windows.Forms.Label
    Friend WithEvents tDesignation As C1.Win.C1Input.C1TextBox
    Friend WithEvents tCodeArticle As C1.Win.C1Input.C1TextBox
    Friend WithEvents LNomClient As System.Windows.Forms.Label
    Friend WithEvents LNumeroClient As System.Windows.Forms.Label
    Friend WithEvents cmbRayon As C1.Win.C1List.C1Combo
    Friend WithEvents tstockAlert As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents tQuantiteACommander As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents Label17 As System.Windows.Forms.Label
    Friend WithEvents GroupBox7 As System.Windows.Forms.GroupBox
    Friend WithEvents tStockInitial As C1.Win.C1Input.C1TextBox
    Friend WithEvents tDateInitial As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label19 As System.Windows.Forms.Label
    Friend WithEvents Label20 As System.Windows.Forms.Label
    Friend WithEvents Label18 As System.Windows.Forms.Label
    Friend WithEvents GroupBox8 As System.Windows.Forms.GroupBox
    Friend WithEvents tCodePCT As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents Label21 As System.Windows.Forms.Label
    Friend WithEvents chkAccordPrealable As System.Windows.Forms.CheckBox
    Friend WithEvents cmbCategorieCnam As C1.Win.C1List.C1Combo
    Friend WithEvents tTarifReference As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label22 As System.Windows.Forms.Label
    Friend WithEvents Label23 As System.Windows.Forms.Label
    Friend WithEvents Label24 As System.Windows.Forms.Label
    Friend WithEvents chkPriseEnCharge As System.Windows.Forms.CheckBox
    Friend WithEvents Label25 As System.Windows.Forms.Label
    Friend WithEvents chkSansCodeAbarre As System.Windows.Forms.CheckBox
    Friend WithEvents tContenance As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label26 As System.Windows.Forms.Label
    Friend WithEvents GroupBox9 As System.Windows.Forms.GroupBox
    Friend WithEvents cmbSituation As C1.Win.C1List.C1Combo
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents Label27 As System.Windows.Forms.Label
    Friend WithEvents chkExonere As System.Windows.Forms.CheckBox
    Friend WithEvents Label28 As System.Windows.Forms.Label
    Friend WithEvents Label29 As System.Windows.Forms.Label
    Friend WithEvents lOperateur As System.Windows.Forms.Label
    Friend WithEvents lQuantiteDernierCommande As System.Windows.Forms.Label
    Friend WithEvents lDateDernierCommande As System.Windows.Forms.Label
    Friend WithEvents lStock As System.Windows.Forms.Label
    Friend WithEvents tSection As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents tDateAlerte As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label30 As System.Windows.Forms.Label
End Class
