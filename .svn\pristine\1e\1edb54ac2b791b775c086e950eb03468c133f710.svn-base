﻿Imports System.IO
Imports System.Data.SqlClient
Imports System.Windows.Forms

Public Class fAjouterBanque
    Dim cmd As New SqlCommand
    Public Enregistrer As Boolean = False
    Public Valeur As String = ""

    Private Sub bEnregistrer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bEnregistrer.Click
        Dim StrSQL As String = ""
        Dim StrMajLOT As String = ""
        Dim DernierCodeCategorie As Integer = 0

        StrSQL = " SELECT max(CodeBanque) FROM [BANQUE]"

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL

        Try
            DernierCodeCategorie = cmd.ExecuteScalar()
            If Trim(DernierCodeCategorie) <> "" Then
                DernierCodeCategorie = DernierCodeCategorie + 1
            Else
                DernierCodeCategorie = "0"
            End If
        Catch ex As Exception
            Console.WriteLine(ex.Message)
            DernierCodeCategorie = "0"
        End Try

        StrMajLOT = "INSERT INTO BANQUE (""CodeBanque"",""NomBanque"") " + _
                    " VALUES(" + DernierCodeCategorie.ToString + "," + Quote(tCategorie.Text) + ")"

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrMajLOT
        Try
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        Enregistrer = True
        Me.Hide()
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Enregistrer = False
        Me.Hide()
    End Sub

    Private Sub fAjouterForme_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        tCategorie.Value = Valeur
    End Sub

    Private Sub tCategorie_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCategorie.KeyUp
        If e.KeyCode = Keys.Enter Then
            bEnregistrer.Focus()
        End If
    End Sub
End Class