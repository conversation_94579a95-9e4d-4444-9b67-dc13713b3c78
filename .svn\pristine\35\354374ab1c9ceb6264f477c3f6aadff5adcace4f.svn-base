﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fAjouterMedecin
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fAjouterMedecin))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.tMedecin = New C1.Win.C1Input.C1TextBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.tIdCNAM = New C1.Win.C1Input.C1TextBox()
        Me.lNomArticle = New System.Windows.Forms.Label()
        Me.bEnregistrer = New C1.Win.C1Input.C1Button()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.cmbSpecialite = New C1.Win.C1List.C1Combo()
        Me.Panel.SuspendLayout()
        CType(Me.tMedecin, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tIdCNAM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbSpecialite, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.cmbSpecialite)
        Me.Panel.Controls.Add(Me.Label3)
        Me.Panel.Controls.Add(Me.Label2)
        Me.Panel.Controls.Add(Me.tMedecin)
        Me.Panel.Controls.Add(Me.Label1)
        Me.Panel.Controls.Add(Me.tIdCNAM)
        Me.Panel.Controls.Add(Me.lNomArticle)
        Me.Panel.Controls.Add(Me.bEnregistrer)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(238, 181)
        Me.Panel.TabIndex = 78
        '
        'Label2
        '
        Me.Label2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(4, 29)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(73, 24)
        Me.Label2.TabIndex = 77
        Me.Label2.Text = "Médecin :"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'tMedecin
        '
        Me.tMedecin.AutoSize = False
        Me.tMedecin.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tMedecin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMedecin.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tMedecin.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tMedecin.Location = New System.Drawing.Point(78, 32)
        Me.tMedecin.Name = "tMedecin"
        Me.tMedecin.Size = New System.Drawing.Size(146, 19)
        Me.tMedecin.TabIndex = 0
        Me.tMedecin.Tag = Nothing
        Me.tMedecin.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tMedecin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label1
        '
        Me.Label1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(3, 57)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(73, 24)
        Me.Label1.TabIndex = 75
        Me.Label1.Text = "Id CNAM :"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'tIdCNAM
        '
        Me.tIdCNAM.AutoSize = False
        Me.tIdCNAM.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tIdCNAM.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tIdCNAM.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tIdCNAM.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tIdCNAM.Location = New System.Drawing.Point(78, 60)
        Me.tIdCNAM.Name = "tIdCNAM"
        Me.tIdCNAM.Size = New System.Drawing.Size(146, 19)
        Me.tIdCNAM.TabIndex = 1
        Me.tIdCNAM.Tag = Nothing
        Me.tIdCNAM.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tIdCNAM.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lNomArticle
        '
        Me.lNomArticle.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lNomArticle.Font = New System.Drawing.Font("HandelGotDLig", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNomArticle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.lNomArticle.Location = New System.Drawing.Point(0, 3)
        Me.lNomArticle.Name = "lNomArticle"
        Me.lNomArticle.Size = New System.Drawing.Size(231, 23)
        Me.lNomArticle.TabIndex = 73
        Me.lNomArticle.Text = "Ajout d'un médecin"
        Me.lNomArticle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bEnregistrer
        '
        Me.bEnregistrer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bEnregistrer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bEnregistrer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bEnregistrer.Location = New System.Drawing.Point(15, 124)
        Me.bEnregistrer.Name = "bEnregistrer"
        Me.bEnregistrer.Size = New System.Drawing.Size(100, 45)
        Me.bEnregistrer.TabIndex = 2
        Me.bEnregistrer.Text = "Enregistrer            F3"
        Me.bEnregistrer.UseVisualStyleBackColor = True
        Me.bEnregistrer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(124, 124)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(100, 45)
        Me.bAnnuler.TabIndex = 3
        Me.bAnnuler.Text = "Annuler                F10"
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label3
        '
        Me.Label3.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.Location = New System.Drawing.Point(4, 82)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(73, 24)
        Me.Label3.TabIndex = 78
        Me.Label3.Text = "Spécialité :"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'cmbSpecialite
        '
        Me.cmbSpecialite.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbSpecialite.Caption = ""
        Me.cmbSpecialite.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbSpecialite.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbSpecialite.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbSpecialite.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbSpecialite.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbSpecialite.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbSpecialite.Images.Add(CType(resources.GetObject("cmbSpecialite.Images"), System.Drawing.Image))
        Me.cmbSpecialite.Location = New System.Drawing.Point(78, 85)
        Me.cmbSpecialite.MatchEntryTimeout = CType(2000, Long)
        Me.cmbSpecialite.MaxDropDownItems = CType(5, Short)
        Me.cmbSpecialite.MaxLength = 32767
        Me.cmbSpecialite.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbSpecialite.Name = "cmbSpecialite"
        Me.cmbSpecialite.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbSpecialite.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbSpecialite.Size = New System.Drawing.Size(146, 22)
        Me.cmbSpecialite.TabIndex = 79
        Me.cmbSpecialite.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbSpecialite.PropBag = resources.GetString("cmbSpecialite.PropBag")
        '
        'fAjouterMedecin
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(238, 181)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fAjouterMedecin"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        Me.Panel.PerformLayout()
        CType(Me.tMedecin, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tIdCNAM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbSpecialite, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents tIdCNAM As C1.Win.C1Input.C1TextBox
    Friend WithEvents lNomArticle As System.Windows.Forms.Label
    Friend WithEvents bEnregistrer As C1.Win.C1Input.C1Button
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents tMedecin As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents cmbSpecialite As C1.Win.C1List.C1Combo
End Class
