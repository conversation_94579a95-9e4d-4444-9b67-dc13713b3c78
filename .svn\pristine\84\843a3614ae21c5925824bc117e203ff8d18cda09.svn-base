﻿//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.ComponentModel;
using System.Data.Entity.Core.EntityClient;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Core.Objects.DataClasses;
using System.Linq;
using System.Runtime.Serialization;
using System.Xml.Serialization;

[assembly: EdmSchemaAttribute()]
#region Métadonnées de relation EDM

[assembly: EdmRelationshipAttribute("BusinessManagementModel", "VENTEVENTE_DETAILS", "VENTE", System.Data.Entity.Core.Metadata.Edm.RelationshipMultiplicity.One, typeof(Data.BusinessManagement.VENTE), "VENTE_DETAILS", System.Data.Entity.Core.Metadata.Edm.RelationshipMultiplicity.Many, typeof(Data.BusinessManagement.VENTE_DETAILS), true)]
[assembly: EdmRelationshipAttribute("BusinessManagementModel", "COMMANDECOMMANDE_DETAILS", "COMMANDE", System.Data.Entity.Core.Metadata.Edm.RelationshipMultiplicity.One, typeof(Data.BusinessManagement.COMMANDE), "COMMANDE_DETAILS", System.Data.Entity.Core.Metadata.Edm.RelationshipMultiplicity.Many, typeof(Data.BusinessManagement.COMMANDE_DETAILS), true)]
[assembly: EdmRelationshipAttribute("BusinessManagementModel", "SIMULATION_STOCKSIMULATION_STOCK_DETAILS", "SIMULATION_STOCK", System.Data.Entity.Core.Metadata.Edm.RelationshipMultiplicity.One, typeof(Data.BusinessManagement.SIMULATION_STOCK), "SIMULATION_STOCK_DETAILS", System.Data.Entity.Core.Metadata.Edm.RelationshipMultiplicity.Many, typeof(Data.BusinessManagement.SIMULATION_STOCK_DETAILS), true)]
[assembly: EdmRelationshipAttribute("BusinessManagementModel", "ACHATACHAT_DETAILS", "ACHAT", System.Data.Entity.Core.Metadata.Edm.RelationshipMultiplicity.One, typeof(Data.BusinessManagement.ACHAT), "ACHAT_DETAILS", System.Data.Entity.Core.Metadata.Edm.RelationshipMultiplicity.Many, typeof(Data.BusinessManagement.ACHAT_DETAILS), true)]

#endregion

namespace Data.BusinessManagement
{
    #region Contextes
    
    /// <summary>
    /// Aucune documentation sur les métadonnées n'est disponible.
    /// </summary>
    public partial class BusinessManagementEntities : ObjectContext
    {
        #region Constructeurs
    
        /// <summary>
        /// Initialise un nouvel objet BusinessManagementEntities à l'aide de la chaîne de connexion trouvée dans la section 'BusinessManagementEntities' du fichier de configuration d'application.
        /// </summary>
        public BusinessManagementEntities() : base("name=BusinessManagementEntities", "BusinessManagementEntities")
        {
            this.ContextOptions.LazyLoadingEnabled = true;
            OnContextCreated();
        }
    
        /// <summary>
        /// Initialisez un nouvel objet BusinessManagementEntities.
        /// </summary>
        public BusinessManagementEntities(string connectionString) : base(connectionString, "BusinessManagementEntities")
        {
            this.ContextOptions.LazyLoadingEnabled = true;
            OnContextCreated();
        }
    
        /// <summary>
        /// Initialisez un nouvel objet BusinessManagementEntities.
        /// </summary>
        public BusinessManagementEntities(EntityConnection connection) : base(connection, "BusinessManagementEntities")
        {
            this.ContextOptions.LazyLoadingEnabled = true;
            OnContextCreated();
        }
    
        #endregion
    
        #region Méthodes partielles
    
        partial void OnContextCreated();
    
        #endregion
    
        #region Propriétés ObjectSet
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        public ObjectSet<VENTE_NUMERO> VENTE_NUMERO
        {
            get
            {
                if ((_VENTE_NUMERO == null))
                {
                    _VENTE_NUMERO = base.CreateObjectSet<VENTE_NUMERO>("VENTE_NUMERO");
                }
                return _VENTE_NUMERO;
            }
        }
        private ObjectSet<VENTE_NUMERO> _VENTE_NUMERO;
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        public ObjectSet<VENTE> VENTE
        {
            get
            {
                if ((_VENTE == null))
                {
                    _VENTE = base.CreateObjectSet<VENTE>("VENTE");
                }
                return _VENTE;
            }
        }
        private ObjectSet<VENTE> _VENTE;
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        public ObjectSet<VENTE_DETAILS> VENTE_DETAILS
        {
            get
            {
                if ((_VENTE_DETAILS == null))
                {
                    _VENTE_DETAILS = base.CreateObjectSet<VENTE_DETAILS>("VENTE_DETAILS");
                }
                return _VENTE_DETAILS;
            }
        }
        private ObjectSet<VENTE_DETAILS> _VENTE_DETAILS;
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        public ObjectSet<COMMANDE> COMMANDE
        {
            get
            {
                if ((_COMMANDE == null))
                {
                    _COMMANDE = base.CreateObjectSet<COMMANDE>("COMMANDE");
                }
                return _COMMANDE;
            }
        }
        private ObjectSet<COMMANDE> _COMMANDE;
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        public ObjectSet<COMMANDE_DETAILS> COMMANDE_DETAILS
        {
            get
            {
                if ((_COMMANDE_DETAILS == null))
                {
                    _COMMANDE_DETAILS = base.CreateObjectSet<COMMANDE_DETAILS>("COMMANDE_DETAILS");
                }
                return _COMMANDE_DETAILS;
            }
        }
        private ObjectSet<COMMANDE_DETAILS> _COMMANDE_DETAILS;
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        public ObjectSet<SIMULATION_STOCK> SIMULATION_STOCK
        {
            get
            {
                if ((_SIMULATION_STOCK == null))
                {
                    _SIMULATION_STOCK = base.CreateObjectSet<SIMULATION_STOCK>("SIMULATION_STOCK");
                }
                return _SIMULATION_STOCK;
            }
        }
        private ObjectSet<SIMULATION_STOCK> _SIMULATION_STOCK;
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        public ObjectSet<SIMULATION_STOCK_DETAILS> SIMULATION_STOCK_DETAILS
        {
            get
            {
                if ((_SIMULATION_STOCK_DETAILS == null))
                {
                    _SIMULATION_STOCK_DETAILS = base.CreateObjectSet<SIMULATION_STOCK_DETAILS>("SIMULATION_STOCK_DETAILS");
                }
                return _SIMULATION_STOCK_DETAILS;
            }
        }
        private ObjectSet<SIMULATION_STOCK_DETAILS> _SIMULATION_STOCK_DETAILS;
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        public ObjectSet<ACHAT> ACHAT
        {
            get
            {
                if ((_ACHAT == null))
                {
                    _ACHAT = base.CreateObjectSet<ACHAT>("ACHAT");
                }
                return _ACHAT;
            }
        }
        private ObjectSet<ACHAT> _ACHAT;
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        public ObjectSet<ACHAT_DETAILS> ACHAT_DETAILS
        {
            get
            {
                if ((_ACHAT_DETAILS == null))
                {
                    _ACHAT_DETAILS = base.CreateObjectSet<ACHAT_DETAILS>("ACHAT_DETAILS");
                }
                return _ACHAT_DETAILS;
            }
        }
        private ObjectSet<ACHAT_DETAILS> _ACHAT_DETAILS;

        #endregion

        #region Méthodes AddTo
    
        /// <summary>
        /// Méthode déconseillée pour ajouter un nouvel objet à l'EntitySet VENTE_NUMERO. Utilisez la méthode .Add de la propriété ObjectSet&lt;T&gt; associée à la place.
        /// </summary>
        public void AddToVENTE_NUMERO(VENTE_NUMERO vENTE_NUMERO)
        {
            base.AddObject("VENTE_NUMERO", vENTE_NUMERO);
        }
    
        /// <summary>
        /// Méthode déconseillée pour ajouter un nouvel objet à l'EntitySet VENTE. Utilisez la méthode .Add de la propriété ObjectSet&lt;T&gt; associée à la place.
        /// </summary>
        public void AddToVENTE(VENTE vENTE)
        {
            base.AddObject("VENTE", vENTE);
        }
    
        /// <summary>
        /// Méthode déconseillée pour ajouter un nouvel objet à l'EntitySet VENTE_DETAILS. Utilisez la méthode .Add de la propriété ObjectSet&lt;T&gt; associée à la place.
        /// </summary>
        public void AddToVENTE_DETAILS(VENTE_DETAILS vENTE_DETAILS)
        {
            base.AddObject("VENTE_DETAILS", vENTE_DETAILS);
        }
    
        /// <summary>
        /// Méthode déconseillée pour ajouter un nouvel objet à l'EntitySet COMMANDE. Utilisez la méthode .Add de la propriété ObjectSet&lt;T&gt; associée à la place.
        /// </summary>
        public void AddToCOMMANDE(COMMANDE cOMMANDE)
        {
            base.AddObject("COMMANDE", cOMMANDE);
        }
    
        /// <summary>
        /// Méthode déconseillée pour ajouter un nouvel objet à l'EntitySet COMMANDE_DETAILS. Utilisez la méthode .Add de la propriété ObjectSet&lt;T&gt; associée à la place.
        /// </summary>
        public void AddToCOMMANDE_DETAILS(COMMANDE_DETAILS cOMMANDE_DETAILS)
        {
            base.AddObject("COMMANDE_DETAILS", cOMMANDE_DETAILS);
        }
    
        /// <summary>
        /// Méthode déconseillée pour ajouter un nouvel objet à l'EntitySet SIMULATION_STOCK. Utilisez la méthode .Add de la propriété ObjectSet&lt;T&gt; associée à la place.
        /// </summary>
        public void AddToSIMULATION_STOCK(SIMULATION_STOCK sIMULATION_STOCK)
        {
            base.AddObject("SIMULATION_STOCK", sIMULATION_STOCK);
        }
    
        /// <summary>
        /// Méthode déconseillée pour ajouter un nouvel objet à l'EntitySet SIMULATION_STOCK_DETAILS. Utilisez la méthode .Add de la propriété ObjectSet&lt;T&gt; associée à la place.
        /// </summary>
        public void AddToSIMULATION_STOCK_DETAILS(SIMULATION_STOCK_DETAILS sIMULATION_STOCK_DETAILS)
        {
            base.AddObject("SIMULATION_STOCK_DETAILS", sIMULATION_STOCK_DETAILS);
        }
    
        /// <summary>
        /// Méthode déconseillée pour ajouter un nouvel objet à l'EntitySet ACHAT. Utilisez la méthode .Add de la propriété ObjectSet&lt;T&gt; associée à la place.
        /// </summary>
        public void AddToACHAT(ACHAT aCHAT)
        {
            base.AddObject("ACHAT", aCHAT);
        }
    
        /// <summary>
        /// Méthode déconseillée pour ajouter un nouvel objet à l'EntitySet ACHAT_DETAILS. Utilisez la méthode .Add de la propriété ObjectSet&lt;T&gt; associée à la place.
        /// </summary>
        public void AddToACHAT_DETAILS(ACHAT_DETAILS aCHAT_DETAILS)
        {
            base.AddObject("ACHAT_DETAILS", aCHAT_DETAILS);
        }

        #endregion

    }

    #endregion

    #region Entités
    
    /// <summary>
    /// Aucune documentation sur les métadonnées n'est disponible.
    /// </summary>
    [EdmEntityTypeAttribute(NamespaceName="BusinessManagementModel", Name="ACHAT")]
    [Serializable()]
    [DataContractAttribute(IsReference=true)]
    public partial class ACHAT : EntityObject
    {
        #region Méthode de fabrique
    
        /// <summary>
        /// Créez un nouvel objet ACHAT.
        /// </summary>
        /// <param name="numeroAchat">Valeur initiale de la propriété NumeroAchat.</param>
        /// <param name="date">Valeur initiale de la propriété Date.</param>
        /// <param name="totalHT">Valeur initiale de la propriété TotalHT.</param>
        /// <param name="tVA">Valeur initiale de la propriété TVA.</param>
        /// <param name="totalTTC">Valeur initiale de la propriété TotalTTC.</param>
        /// <param name="totalRemise">Valeur initiale de la propriété TotalRemise.</param>
        /// <param name="timbre">Valeur initiale de la propriété Timbre.</param>
        /// <param name="codeFournisseur">Valeur initiale de la propriété CodeFournisseur.</param>
        /// <param name="codePersonnel">Valeur initiale de la propriété CodePersonnel.</param>
        /// <param name="note">Valeur initiale de la propriété Note.</param>
        /// <param name="libellePoste">Valeur initiale de la propriété LibellePoste.</param>
        /// <param name="dateBlFacture">Valeur initiale de la propriété DateBlFacture.</param>
        /// <param name="valeurVenteTTC">Valeur initiale de la propriété ValeurVenteTTC.</param>
        /// <param name="autre">Valeur initiale de la propriété Autre.</param>
        public static ACHAT CreateACHAT(global::System.String numeroAchat, global::System.DateTime date, global::System.Decimal totalHT, global::System.Decimal tVA, global::System.Decimal totalTTC, global::System.Decimal totalRemise, global::System.Decimal timbre, global::System.String codeFournisseur, global::System.String codePersonnel, global::System.String note, global::System.String libellePoste, global::System.DateTime dateBlFacture, global::System.Decimal valeurVenteTTC, global::System.Decimal autre)
        {
            ACHAT aCHAT = new ACHAT();
            aCHAT.NumeroAchat = numeroAchat;
            aCHAT.Date = date;
            aCHAT.TotalHT = totalHT;
            aCHAT.TVA = tVA;
            aCHAT.TotalTTC = totalTTC;
            aCHAT.TotalRemise = totalRemise;
            aCHAT.Timbre = timbre;
            aCHAT.CodeFournisseur = codeFournisseur;
            aCHAT.CodePersonnel = codePersonnel;
            aCHAT.Note = note;
            aCHAT.LibellePoste = libellePoste;
            aCHAT.DateBlFacture = dateBlFacture;
            aCHAT.ValeurVenteTTC = valeurVenteTTC;
            aCHAT.Autre = autre;
            return aCHAT;
        }

        #endregion

        #region Propriétés simples
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String NumeroAchat
        {
            get
            {
                return _NumeroAchat;
            }
            set
            {
                if (_NumeroAchat != value)
                {
                    OnNumeroAchatChanging(value);
                    ReportPropertyChanging("NumeroAchat");
                    _NumeroAchat = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("NumeroAchat");
                    OnNumeroAchatChanged();
                }
            }
        }
        private global::System.String _NumeroAchat;
        partial void OnNumeroAchatChanging(global::System.String value);
        partial void OnNumeroAchatChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.DateTime Date
        {
            get
            {
                return _Date;
            }
            set
            {
                OnDateChanging(value);
                ReportPropertyChanging("Date");
                _Date = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Date");
                OnDateChanged();
            }
        }
        private global::System.DateTime _Date;
        partial void OnDateChanging(global::System.DateTime value);
        partial void OnDateChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalHT
        {
            get
            {
                return _TotalHT;
            }
            set
            {
                OnTotalHTChanging(value);
                ReportPropertyChanging("TotalHT");
                _TotalHT = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalHT");
                OnTotalHTChanged();
            }
        }
        private global::System.Decimal _TotalHT;
        partial void OnTotalHTChanging(global::System.Decimal value);
        partial void OnTotalHTChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TVA
        {
            get
            {
                return _TVA;
            }
            set
            {
                OnTVAChanging(value);
                ReportPropertyChanging("TVA");
                _TVA = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TVA");
                OnTVAChanged();
            }
        }
        private global::System.Decimal _TVA;
        partial void OnTVAChanging(global::System.Decimal value);
        partial void OnTVAChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalTTC
        {
            get
            {
                return _TotalTTC;
            }
            set
            {
                OnTotalTTCChanging(value);
                ReportPropertyChanging("TotalTTC");
                _TotalTTC = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalTTC");
                OnTotalTTCChanged();
            }
        }
        private global::System.Decimal _TotalTTC;
        partial void OnTotalTTCChanging(global::System.Decimal value);
        partial void OnTotalTTCChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalRemise
        {
            get
            {
                return _TotalRemise;
            }
            set
            {
                OnTotalRemiseChanging(value);
                ReportPropertyChanging("TotalRemise");
                _TotalRemise = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalRemise");
                OnTotalRemiseChanged();
            }
        }
        private global::System.Decimal _TotalRemise;
        partial void OnTotalRemiseChanging(global::System.Decimal value);
        partial void OnTotalRemiseChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal Timbre
        {
            get
            {
                return _Timbre;
            }
            set
            {
                OnTimbreChanging(value);
                ReportPropertyChanging("Timbre");
                _Timbre = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Timbre");
                OnTimbreChanged();
            }
        }
        private global::System.Decimal _Timbre;
        partial void OnTimbreChanging(global::System.Decimal value);
        partial void OnTimbreChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String CodeFournisseur
        {
            get
            {
                return _CodeFournisseur;
            }
            set
            {
                OnCodeFournisseurChanging(value);
                ReportPropertyChanging("CodeFournisseur");
                _CodeFournisseur = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("CodeFournisseur");
                OnCodeFournisseurChanged();
            }
        }
        private global::System.String _CodeFournisseur;
        partial void OnCodeFournisseurChanging(global::System.String value);
        partial void OnCodeFournisseurChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String CodePersonnel
        {
            get
            {
                return _CodePersonnel;
            }
            set
            {
                OnCodePersonnelChanging(value);
                ReportPropertyChanging("CodePersonnel");
                _CodePersonnel = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("CodePersonnel");
                OnCodePersonnelChanged();
            }
        }
        private global::System.String _CodePersonnel;
        partial void OnCodePersonnelChanging(global::System.String value);
        partial void OnCodePersonnelChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String Note
        {
            get
            {
                return _Note;
            }
            set
            {
                OnNoteChanging(value);
                ReportPropertyChanging("Note");
                _Note = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("Note");
                OnNoteChanged();
            }
        }
        private global::System.String _Note;
        partial void OnNoteChanging(global::System.String value);
        partial void OnNoteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public global::System.String NumeroBL_Facture
        {
            get
            {
                return _NumeroBL_Facture;
            }
            set
            {
                OnNumeroBL_FactureChanging(value);
                ReportPropertyChanging("NumeroBL_Facture");
                _NumeroBL_Facture = StructuralObject.SetValidValue(value, true);
                ReportPropertyChanged("NumeroBL_Facture");
                OnNumeroBL_FactureChanged();
            }
        }
        private global::System.String _NumeroBL_Facture;
        partial void OnNumeroBL_FactureChanging(global::System.String value);
        partial void OnNumeroBL_FactureChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String LibellePoste
        {
            get
            {
                return _LibellePoste;
            }
            set
            {
                OnLibellePosteChanging(value);
                ReportPropertyChanging("LibellePoste");
                _LibellePoste = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("LibellePoste");
                OnLibellePosteChanged();
            }
        }
        private global::System.String _LibellePoste;
        partial void OnLibellePosteChanging(global::System.String value);
        partial void OnLibellePosteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.DateTime DateBlFacture
        {
            get
            {
                return _DateBlFacture;
            }
            set
            {
                OnDateBlFactureChanging(value);
                ReportPropertyChanging("DateBlFacture");
                _DateBlFacture = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("DateBlFacture");
                OnDateBlFactureChanged();
            }
        }
        private global::System.DateTime _DateBlFacture;
        partial void OnDateBlFactureChanging(global::System.DateTime value);
        partial void OnDateBlFactureChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal ValeurVenteTTC
        {
            get
            {
                return _ValeurVenteTTC;
            }
            set
            {
                OnValeurVenteTTCChanging(value);
                ReportPropertyChanging("ValeurVenteTTC");
                _ValeurVenteTTC = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("ValeurVenteTTC");
                OnValeurVenteTTCChanged();
            }
        }
        private global::System.Decimal _ValeurVenteTTC;
        partial void OnValeurVenteTTCChanging(global::System.Decimal value);
        partial void OnValeurVenteTTCChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal Autre
        {
            get
            {
                return _Autre;
            }
            set
            {
                OnAutreChanging(value);
                ReportPropertyChanging("Autre");
                _Autre = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Autre");
                OnAutreChanged();
            }
        }
        private global::System.Decimal _Autre;
        partial void OnAutreChanging(global::System.Decimal value);
        partial void OnAutreChanged();

        #endregion

        #region Propriétés de navigation
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [XmlIgnoreAttribute()]
        [SoapIgnoreAttribute()]
        [DataMemberAttribute()]
        [EdmRelationshipNavigationPropertyAttribute("BusinessManagementModel", "ACHATACHAT_DETAILS", "ACHAT_DETAILS")]
        public EntityCollection<ACHAT_DETAILS> ACHAT_DETAILS
        {
            get
            {
                return ((IEntityWithRelationships)this).RelationshipManager.GetRelatedCollection<ACHAT_DETAILS>("BusinessManagementModel.ACHATACHAT_DETAILS", "ACHAT_DETAILS");
            }
            set
            {
                if ((value != null))
                {
                    ((IEntityWithRelationships)this).RelationshipManager.InitializeRelatedCollection<ACHAT_DETAILS>("BusinessManagementModel.ACHATACHAT_DETAILS", "ACHAT_DETAILS", value);
                }
            }
        }

        #endregion

    }
    
    /// <summary>
    /// Aucune documentation sur les métadonnées n'est disponible.
    /// </summary>
    [EdmEntityTypeAttribute(NamespaceName="BusinessManagementModel", Name="ACHAT_DETAILS")]
    [Serializable()]
    [DataContractAttribute(IsReference=true)]
    public partial class ACHAT_DETAILS : EntityObject
    {
        #region Méthode de fabrique
    
        /// <summary>
        /// Créez un nouvel objet ACHAT_DETAILS.
        /// </summary>
        /// <param name="numeroAchat">Valeur initiale de la propriété NumeroAchat.</param>
        /// <param name="codeArticle">Valeur initiale de la propriété CodeArticle.</param>
        /// <param name="numeroLotArticle">Valeur initiale de la propriété NumeroLotArticle.</param>
        /// <param name="ordre">Valeur initiale de la propriété Ordre.</param>
        /// <param name="codeABarre">Valeur initiale de la propriété CodeABarre.</param>
        /// <param name="designation">Valeur initiale de la propriété Designation.</param>
        /// <param name="codeForme">Valeur initiale de la propriété CodeForme.</param>
        /// <param name="qte">Valeur initiale de la propriété Qte.</param>
        /// <param name="stock">Valeur initiale de la propriété Stock.</param>
        /// <param name="prixAchatHT">Valeur initiale de la propriété PrixAchatHT.</param>
        /// <param name="totalAchatHT">Valeur initiale de la propriété TotalAchatHT.</param>
        /// <param name="prixVenteTTC">Valeur initiale de la propriété PrixVenteTTC.</param>
        /// <param name="remise">Valeur initiale de la propriété Remise.</param>
        /// <param name="tVA">Valeur initiale de la propriété TVA.</param>
        /// <param name="quantiteUnitaire">Valeur initiale de la propriété QuantiteUnitaire.</param>
        public static ACHAT_DETAILS CreateACHAT_DETAILS(global::System.String numeroAchat, global::System.String codeArticle, global::System.String numeroLotArticle, global::System.Int64 ordre, global::System.String codeABarre, global::System.String designation, global::System.Int32 codeForme, global::System.Int32 qte, global::System.Int32 stock, global::System.Decimal prixAchatHT, global::System.Decimal totalAchatHT, global::System.Decimal prixVenteTTC, global::System.Decimal remise, global::System.Decimal tVA, global::System.Int32 quantiteUnitaire)
        {
            ACHAT_DETAILS aCHAT_DETAILS = new ACHAT_DETAILS();
            aCHAT_DETAILS.NumeroAchat = numeroAchat;
            aCHAT_DETAILS.CodeArticle = codeArticle;
            aCHAT_DETAILS.NumeroLotArticle = numeroLotArticle;
            aCHAT_DETAILS.Ordre = ordre;
            aCHAT_DETAILS.CodeABarre = codeABarre;
            aCHAT_DETAILS.Designation = designation;
            aCHAT_DETAILS.CodeForme = codeForme;
            aCHAT_DETAILS.Qte = qte;
            aCHAT_DETAILS.Stock = stock;
            aCHAT_DETAILS.PrixAchatHT = prixAchatHT;
            aCHAT_DETAILS.TotalAchatHT = totalAchatHT;
            aCHAT_DETAILS.PrixVenteTTC = prixVenteTTC;
            aCHAT_DETAILS.Remise = remise;
            aCHAT_DETAILS.TVA = tVA;
            aCHAT_DETAILS.QuantiteUnitaire = quantiteUnitaire;
            return aCHAT_DETAILS;
        }

        #endregion

        #region Propriétés simples
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String NumeroAchat
        {
            get
            {
                return _NumeroAchat;
            }
            set
            {
                if (_NumeroAchat != value)
                {
                    OnNumeroAchatChanging(value);
                    ReportPropertyChanging("NumeroAchat");
                    _NumeroAchat = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("NumeroAchat");
                    OnNumeroAchatChanged();
                }
            }
        }
        private global::System.String _NumeroAchat;
        partial void OnNumeroAchatChanging(global::System.String value);
        partial void OnNumeroAchatChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String CodeArticle
        {
            get
            {
                return _CodeArticle;
            }
            set
            {
                if (_CodeArticle != value)
                {
                    OnCodeArticleChanging(value);
                    ReportPropertyChanging("CodeArticle");
                    _CodeArticle = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("CodeArticle");
                    OnCodeArticleChanged();
                }
            }
        }
        private global::System.String _CodeArticle;
        partial void OnCodeArticleChanging(global::System.String value);
        partial void OnCodeArticleChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String NumeroLotArticle
        {
            get
            {
                return _NumeroLotArticle;
            }
            set
            {
                if (_NumeroLotArticle != value)
                {
                    OnNumeroLotArticleChanging(value);
                    ReportPropertyChanging("NumeroLotArticle");
                    _NumeroLotArticle = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("NumeroLotArticle");
                    OnNumeroLotArticleChanged();
                }
            }
        }
        private global::System.String _NumeroLotArticle;
        partial void OnNumeroLotArticleChanging(global::System.String value);
        partial void OnNumeroLotArticleChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int64 Ordre
        {
            get
            {
                return _Ordre;
            }
            set
            {
                OnOrdreChanging(value);
                ReportPropertyChanging("Ordre");
                _Ordre = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Ordre");
                OnOrdreChanged();
            }
        }
        private global::System.Int64 _Ordre;
        partial void OnOrdreChanging(global::System.Int64 value);
        partial void OnOrdreChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String CodeABarre
        {
            get
            {
                return _CodeABarre;
            }
            set
            {
                OnCodeABarreChanging(value);
                ReportPropertyChanging("CodeABarre");
                _CodeABarre = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("CodeABarre");
                OnCodeABarreChanged();
            }
        }
        private global::System.String _CodeABarre;
        partial void OnCodeABarreChanging(global::System.String value);
        partial void OnCodeABarreChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String Designation
        {
            get
            {
                return _Designation;
            }
            set
            {
                OnDesignationChanging(value);
                ReportPropertyChanging("Designation");
                _Designation = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("Designation");
                OnDesignationChanged();
            }
        }
        private global::System.String _Designation;
        partial void OnDesignationChanging(global::System.String value);
        partial void OnDesignationChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 CodeForme
        {
            get
            {
                return _CodeForme;
            }
            set
            {
                OnCodeFormeChanging(value);
                ReportPropertyChanging("CodeForme");
                _CodeForme = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("CodeForme");
                OnCodeFormeChanged();
            }
        }
        private global::System.Int32 _CodeForme;
        partial void OnCodeFormeChanging(global::System.Int32 value);
        partial void OnCodeFormeChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 Qte
        {
            get
            {
                return _Qte;
            }
            set
            {
                OnQteChanging(value);
                ReportPropertyChanging("Qte");
                _Qte = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Qte");
                OnQteChanged();
            }
        }
        private global::System.Int32 _Qte;
        partial void OnQteChanging(global::System.Int32 value);
        partial void OnQteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 Stock
        {
            get
            {
                return _Stock;
            }
            set
            {
                OnStockChanging(value);
                ReportPropertyChanging("Stock");
                _Stock = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Stock");
                OnStockChanged();
            }
        }
        private global::System.Int32 _Stock;
        partial void OnStockChanging(global::System.Int32 value);
        partial void OnStockChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal PrixAchatHT
        {
            get
            {
                return _PrixAchatHT;
            }
            set
            {
                OnPrixAchatHTChanging(value);
                ReportPropertyChanging("PrixAchatHT");
                _PrixAchatHT = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("PrixAchatHT");
                OnPrixAchatHTChanged();
            }
        }
        private global::System.Decimal _PrixAchatHT;
        partial void OnPrixAchatHTChanging(global::System.Decimal value);
        partial void OnPrixAchatHTChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalAchatHT
        {
            get
            {
                return _TotalAchatHT;
            }
            set
            {
                OnTotalAchatHTChanging(value);
                ReportPropertyChanging("TotalAchatHT");
                _TotalAchatHT = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalAchatHT");
                OnTotalAchatHTChanged();
            }
        }
        private global::System.Decimal _TotalAchatHT;
        partial void OnTotalAchatHTChanging(global::System.Decimal value);
        partial void OnTotalAchatHTChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal PrixVenteTTC
        {
            get
            {
                return _PrixVenteTTC;
            }
            set
            {
                OnPrixVenteTTCChanging(value);
                ReportPropertyChanging("PrixVenteTTC");
                _PrixVenteTTC = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("PrixVenteTTC");
                OnPrixVenteTTCChanged();
            }
        }
        private global::System.Decimal _PrixVenteTTC;
        partial void OnPrixVenteTTCChanging(global::System.Decimal value);
        partial void OnPrixVenteTTCChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal Remise
        {
            get
            {
                return _Remise;
            }
            set
            {
                OnRemiseChanging(value);
                ReportPropertyChanging("Remise");
                _Remise = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Remise");
                OnRemiseChanged();
            }
        }
        private global::System.Decimal _Remise;
        partial void OnRemiseChanging(global::System.Decimal value);
        partial void OnRemiseChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TVA
        {
            get
            {
                return _TVA;
            }
            set
            {
                OnTVAChanging(value);
                ReportPropertyChanging("TVA");
                _TVA = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TVA");
                OnTVAChanged();
            }
        }
        private global::System.Decimal _TVA;
        partial void OnTVAChanging(global::System.Decimal value);
        partial void OnTVAChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.DateTime> DatePeremption
        {
            get
            {
                return _DatePeremption;
            }
            set
            {
                OnDatePeremptionChanging(value);
                ReportPropertyChanging("DatePeremption");
                _DatePeremption = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("DatePeremption");
                OnDatePeremptionChanged();
            }
        }
        private Nullable<global::System.DateTime> _DatePeremption;
        partial void OnDatePeremptionChanging(Nullable<global::System.DateTime> value);
        partial void OnDatePeremptionChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 QuantiteUnitaire
        {
            get
            {
                return _QuantiteUnitaire;
            }
            set
            {
                OnQuantiteUnitaireChanging(value);
                ReportPropertyChanging("QuantiteUnitaire");
                _QuantiteUnitaire = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("QuantiteUnitaire");
                OnQuantiteUnitaireChanged();
            }
        }
        private global::System.Int32 _QuantiteUnitaire;
        partial void OnQuantiteUnitaireChanging(global::System.Int32 value);
        partial void OnQuantiteUnitaireChanged();

        #endregion

    }
    
    /// <summary>
    /// Aucune documentation sur les métadonnées n'est disponible.
    /// </summary>
    [EdmEntityTypeAttribute(NamespaceName="BusinessManagementModel", Name="COMMANDE")]
    [Serializable()]
    [DataContractAttribute(IsReference=true)]
    public partial class COMMANDE : EntityObject
    {
        #region Méthode de fabrique
    
        /// <summary>
        /// Créez un nouvel objet COMMANDE.
        /// </summary>
        /// <param name="numeroCommande">Valeur initiale de la propriété NumeroCommande.</param>
        /// <param name="date">Valeur initiale de la propriété Date.</param>
        /// <param name="totalHT">Valeur initiale de la propriété TotalHT.</param>
        /// <param name="totalTTC">Valeur initiale de la propriété TotalTTC.</param>
        /// <param name="totalTVA">Valeur initiale de la propriété TotalTVA.</param>
        /// <param name="libellePoste">Valeur initiale de la propriété LibellePoste.</param>
        /// <param name="codePersonnel">Valeur initiale de la propriété CodePersonnel.</param>
        /// <param name="codeFournisseur">Valeur initiale de la propriété CodeFournisseur.</param>
        /// <param name="note">Valeur initiale de la propriété Note.</param>
        /// <param name="typeCommande">Valeur initiale de la propriété TypeCommande.</param>
        public static COMMANDE CreateCOMMANDE(global::System.String numeroCommande, global::System.DateTime date, global::System.Decimal totalHT, global::System.Decimal totalTTC, global::System.Decimal totalTVA, global::System.String libellePoste, global::System.String codePersonnel, global::System.String codeFournisseur, global::System.String note, global::System.String typeCommande)
        {
            COMMANDE cOMMANDE = new COMMANDE();
            cOMMANDE.NumeroCommande = numeroCommande;
            cOMMANDE.Date = date;
            cOMMANDE.TotalHT = totalHT;
            cOMMANDE.TotalTTC = totalTTC;
            cOMMANDE.TotalTVA = totalTVA;
            cOMMANDE.LibellePoste = libellePoste;
            cOMMANDE.CodePersonnel = codePersonnel;
            cOMMANDE.CodeFournisseur = codeFournisseur;
            cOMMANDE.Note = note;
            cOMMANDE.TypeCommande = typeCommande;
            return cOMMANDE;
        }

        #endregion

        #region Propriétés simples
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String NumeroCommande
        {
            get
            {
                return _NumeroCommande;
            }
            set
            {
                if (_NumeroCommande != value)
                {
                    OnNumeroCommandeChanging(value);
                    ReportPropertyChanging("NumeroCommande");
                    _NumeroCommande = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("NumeroCommande");
                    OnNumeroCommandeChanged();
                }
            }
        }
        private global::System.String _NumeroCommande;
        partial void OnNumeroCommandeChanging(global::System.String value);
        partial void OnNumeroCommandeChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.DateTime Date
        {
            get
            {
                return _Date;
            }
            set
            {
                OnDateChanging(value);
                ReportPropertyChanging("Date");
                _Date = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Date");
                OnDateChanged();
            }
        }
        private global::System.DateTime _Date;
        partial void OnDateChanging(global::System.DateTime value);
        partial void OnDateChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalHT
        {
            get
            {
                return _TotalHT;
            }
            set
            {
                OnTotalHTChanging(value);
                ReportPropertyChanging("TotalHT");
                _TotalHT = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalHT");
                OnTotalHTChanged();
            }
        }
        private global::System.Decimal _TotalHT;
        partial void OnTotalHTChanging(global::System.Decimal value);
        partial void OnTotalHTChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalTTC
        {
            get
            {
                return _TotalTTC;
            }
            set
            {
                OnTotalTTCChanging(value);
                ReportPropertyChanging("TotalTTC");
                _TotalTTC = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalTTC");
                OnTotalTTCChanged();
            }
        }
        private global::System.Decimal _TotalTTC;
        partial void OnTotalTTCChanging(global::System.Decimal value);
        partial void OnTotalTTCChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalTVA
        {
            get
            {
                return _TotalTVA;
            }
            set
            {
                OnTotalTVAChanging(value);
                ReportPropertyChanging("TotalTVA");
                _TotalTVA = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalTVA");
                OnTotalTVAChanged();
            }
        }
        private global::System.Decimal _TotalTVA;
        partial void OnTotalTVAChanging(global::System.Decimal value);
        partial void OnTotalTVAChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String LibellePoste
        {
            get
            {
                return _LibellePoste;
            }
            set
            {
                OnLibellePosteChanging(value);
                ReportPropertyChanging("LibellePoste");
                _LibellePoste = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("LibellePoste");
                OnLibellePosteChanged();
            }
        }
        private global::System.String _LibellePoste;
        partial void OnLibellePosteChanging(global::System.String value);
        partial void OnLibellePosteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String CodePersonnel
        {
            get
            {
                return _CodePersonnel;
            }
            set
            {
                OnCodePersonnelChanging(value);
                ReportPropertyChanging("CodePersonnel");
                _CodePersonnel = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("CodePersonnel");
                OnCodePersonnelChanged();
            }
        }
        private global::System.String _CodePersonnel;
        partial void OnCodePersonnelChanging(global::System.String value);
        partial void OnCodePersonnelChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String CodeFournisseur
        {
            get
            {
                return _CodeFournisseur;
            }
            set
            {
                OnCodeFournisseurChanging(value);
                ReportPropertyChanging("CodeFournisseur");
                _CodeFournisseur = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("CodeFournisseur");
                OnCodeFournisseurChanged();
            }
        }
        private global::System.String _CodeFournisseur;
        partial void OnCodeFournisseurChanging(global::System.String value);
        partial void OnCodeFournisseurChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String Note
        {
            get
            {
                return _Note;
            }
            set
            {
                OnNoteChanging(value);
                ReportPropertyChanging("Note");
                _Note = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("Note");
                OnNoteChanged();
            }
        }
        private global::System.String _Note;
        partial void OnNoteChanging(global::System.String value);
        partial void OnNoteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public global::System.String NumeroFacture
        {
            get
            {
                return _NumeroFacture;
            }
            set
            {
                OnNumeroFactureChanging(value);
                ReportPropertyChanging("NumeroFacture");
                _NumeroFacture = StructuralObject.SetValidValue(value, true);
                ReportPropertyChanged("NumeroFacture");
                OnNumeroFactureChanged();
            }
        }
        private global::System.String _NumeroFacture;
        partial void OnNumeroFactureChanging(global::System.String value);
        partial void OnNumeroFactureChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String TypeCommande
        {
            get
            {
                return _TypeCommande;
            }
            set
            {
                OnTypeCommandeChanging(value);
                ReportPropertyChanging("TypeCommande");
                _TypeCommande = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("TypeCommande");
                OnTypeCommandeChanged();
            }
        }
        private global::System.String _TypeCommande;
        partial void OnTypeCommandeChanging(global::System.String value);
        partial void OnTypeCommandeChanged();

        #endregion

        #region Propriétés de navigation
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [XmlIgnoreAttribute()]
        [SoapIgnoreAttribute()]
        [DataMemberAttribute()]
        [EdmRelationshipNavigationPropertyAttribute("BusinessManagementModel", "COMMANDECOMMANDE_DETAILS", "COMMANDE_DETAILS")]
        public EntityCollection<COMMANDE_DETAILS> COMMANDE_DETAILS
        {
            get
            {
                return ((IEntityWithRelationships)this).RelationshipManager.GetRelatedCollection<COMMANDE_DETAILS>("BusinessManagementModel.COMMANDECOMMANDE_DETAILS", "COMMANDE_DETAILS");
            }
            set
            {
                if ((value != null))
                {
                    ((IEntityWithRelationships)this).RelationshipManager.InitializeRelatedCollection<COMMANDE_DETAILS>("BusinessManagementModel.COMMANDECOMMANDE_DETAILS", "COMMANDE_DETAILS", value);
                }
            }
        }

        #endregion

    }
    
    /// <summary>
    /// Aucune documentation sur les métadonnées n'est disponible.
    /// </summary>
    [EdmEntityTypeAttribute(NamespaceName="BusinessManagementModel", Name="COMMANDE_DETAILS")]
    [Serializable()]
    [DataContractAttribute(IsReference=true)]
    public partial class COMMANDE_DETAILS : EntityObject
    {
        #region Méthode de fabrique
    
        /// <summary>
        /// Créez un nouvel objet COMMANDE_DETAILS.
        /// </summary>
        /// <param name="numeroCommande">Valeur initiale de la propriété NumeroCommande.</param>
        /// <param name="codeArticle">Valeur initiale de la propriété CodeArticle.</param>
        /// <param name="codeABarre">Valeur initiale de la propriété CodeABarre.</param>
        /// <param name="designation">Valeur initiale de la propriété Designation.</param>
        /// <param name="codeForme">Valeur initiale de la propriété CodeForme.</param>
        /// <param name="qte">Valeur initiale de la propriété Qte.</param>
        /// <param name="stock">Valeur initiale de la propriété Stock.</param>
        /// <param name="prixAchatHT">Valeur initiale de la propriété PrixAchatHT.</param>
        /// <param name="tVA">Valeur initiale de la propriété TVA.</param>
        /// <param name="totalTTCAchat">Valeur initiale de la propriété TotalTTCAchat.</param>
        /// <param name="stockAlerte">Valeur initiale de la propriété StockAlerte.</param>
        /// <param name="enCours">Valeur initiale de la propriété EnCours.</param>
        /// <param name="qteACommander">Valeur initiale de la propriété QteACommander.</param>
        /// <param name="qteUnitaire">Valeur initiale de la propriété QteUnitaire.</param>
        public static COMMANDE_DETAILS CreateCOMMANDE_DETAILS(global::System.String numeroCommande, global::System.String codeArticle, global::System.String codeABarre, global::System.String designation, global::System.Int32 codeForme, global::System.Int32 qte, global::System.Int32 stock, global::System.Decimal prixAchatHT, global::System.Decimal tVA, global::System.Decimal totalTTCAchat, global::System.Decimal stockAlerte, global::System.Decimal enCours, global::System.Decimal qteACommander, global::System.Decimal qteUnitaire)
        {
            COMMANDE_DETAILS cOMMANDE_DETAILS = new COMMANDE_DETAILS();
            cOMMANDE_DETAILS.NumeroCommande = numeroCommande;
            cOMMANDE_DETAILS.CodeArticle = codeArticle;
            cOMMANDE_DETAILS.CodeABarre = codeABarre;
            cOMMANDE_DETAILS.Designation = designation;
            cOMMANDE_DETAILS.CodeForme = codeForme;
            cOMMANDE_DETAILS.Qte = qte;
            cOMMANDE_DETAILS.Stock = stock;
            cOMMANDE_DETAILS.PrixAchatHT = prixAchatHT;
            cOMMANDE_DETAILS.TVA = tVA;
            cOMMANDE_DETAILS.TotalTTCAchat = totalTTCAchat;
            cOMMANDE_DETAILS.StockAlerte = stockAlerte;
            cOMMANDE_DETAILS.EnCours = enCours;
            cOMMANDE_DETAILS.QteACommander = qteACommander;
            cOMMANDE_DETAILS.QteUnitaire = qteUnitaire;
            return cOMMANDE_DETAILS;
        }

        #endregion

        #region Propriétés simples
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String NumeroCommande
        {
            get
            {
                return _NumeroCommande;
            }
            set
            {
                if (_NumeroCommande != value)
                {
                    OnNumeroCommandeChanging(value);
                    ReportPropertyChanging("NumeroCommande");
                    _NumeroCommande = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("NumeroCommande");
                    OnNumeroCommandeChanged();
                }
            }
        }
        private global::System.String _NumeroCommande;
        partial void OnNumeroCommandeChanging(global::System.String value);
        partial void OnNumeroCommandeChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String CodeArticle
        {
            get
            {
                return _CodeArticle;
            }
            set
            {
                if (_CodeArticle != value)
                {
                    OnCodeArticleChanging(value);
                    ReportPropertyChanging("CodeArticle");
                    _CodeArticle = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("CodeArticle");
                    OnCodeArticleChanged();
                }
            }
        }
        private global::System.String _CodeArticle;
        partial void OnCodeArticleChanging(global::System.String value);
        partial void OnCodeArticleChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String CodeABarre
        {
            get
            {
                return _CodeABarre;
            }
            set
            {
                OnCodeABarreChanging(value);
                ReportPropertyChanging("CodeABarre");
                _CodeABarre = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("CodeABarre");
                OnCodeABarreChanged();
            }
        }
        private global::System.String _CodeABarre;
        partial void OnCodeABarreChanging(global::System.String value);
        partial void OnCodeABarreChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String Designation
        {
            get
            {
                return _Designation;
            }
            set
            {
                OnDesignationChanging(value);
                ReportPropertyChanging("Designation");
                _Designation = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("Designation");
                OnDesignationChanged();
            }
        }
        private global::System.String _Designation;
        partial void OnDesignationChanging(global::System.String value);
        partial void OnDesignationChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 CodeForme
        {
            get
            {
                return _CodeForme;
            }
            set
            {
                OnCodeFormeChanging(value);
                ReportPropertyChanging("CodeForme");
                _CodeForme = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("CodeForme");
                OnCodeFormeChanged();
            }
        }
        private global::System.Int32 _CodeForme;
        partial void OnCodeFormeChanging(global::System.Int32 value);
        partial void OnCodeFormeChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 Qte
        {
            get
            {
                return _Qte;
            }
            set
            {
                OnQteChanging(value);
                ReportPropertyChanging("Qte");
                _Qte = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Qte");
                OnQteChanged();
            }
        }
        private global::System.Int32 _Qte;
        partial void OnQteChanging(global::System.Int32 value);
        partial void OnQteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 Stock
        {
            get
            {
                return _Stock;
            }
            set
            {
                OnStockChanging(value);
                ReportPropertyChanging("Stock");
                _Stock = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Stock");
                OnStockChanged();
            }
        }
        private global::System.Int32 _Stock;
        partial void OnStockChanging(global::System.Int32 value);
        partial void OnStockChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.DateTime> DatePeremption
        {
            get
            {
                return _DatePeremption;
            }
            set
            {
                OnDatePeremptionChanging(value);
                ReportPropertyChanging("DatePeremption");
                _DatePeremption = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("DatePeremption");
                OnDatePeremptionChanged();
            }
        }
        private Nullable<global::System.DateTime> _DatePeremption;
        partial void OnDatePeremptionChanging(Nullable<global::System.DateTime> value);
        partial void OnDatePeremptionChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal PrixAchatHT
        {
            get
            {
                return _PrixAchatHT;
            }
            set
            {
                OnPrixAchatHTChanging(value);
                ReportPropertyChanging("PrixAchatHT");
                _PrixAchatHT = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("PrixAchatHT");
                OnPrixAchatHTChanged();
            }
        }
        private global::System.Decimal _PrixAchatHT;
        partial void OnPrixAchatHTChanging(global::System.Decimal value);
        partial void OnPrixAchatHTChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TVA
        {
            get
            {
                return _TVA;
            }
            set
            {
                OnTVAChanging(value);
                ReportPropertyChanging("TVA");
                _TVA = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TVA");
                OnTVAChanged();
            }
        }
        private global::System.Decimal _TVA;
        partial void OnTVAChanging(global::System.Decimal value);
        partial void OnTVAChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalTTCAchat
        {
            get
            {
                return _TotalTTCAchat;
            }
            set
            {
                OnTotalTTCAchatChanging(value);
                ReportPropertyChanging("TotalTTCAchat");
                _TotalTTCAchat = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalTTCAchat");
                OnTotalTTCAchatChanged();
            }
        }
        private global::System.Decimal _TotalTTCAchat;
        partial void OnTotalTTCAchatChanging(global::System.Decimal value);
        partial void OnTotalTTCAchatChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal StockAlerte
        {
            get
            {
                return _StockAlerte;
            }
            set
            {
                OnStockAlerteChanging(value);
                ReportPropertyChanging("StockAlerte");
                _StockAlerte = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("StockAlerte");
                OnStockAlerteChanged();
            }
        }
        private global::System.Decimal _StockAlerte;
        partial void OnStockAlerteChanging(global::System.Decimal value);
        partial void OnStockAlerteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal EnCours
        {
            get
            {
                return _EnCours;
            }
            set
            {
                OnEnCoursChanging(value);
                ReportPropertyChanging("EnCours");
                _EnCours = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("EnCours");
                OnEnCoursChanged();
            }
        }
        private global::System.Decimal _EnCours;
        partial void OnEnCoursChanging(global::System.Decimal value);
        partial void OnEnCoursChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal QteACommander
        {
            get
            {
                return _QteACommander;
            }
            set
            {
                OnQteACommanderChanging(value);
                ReportPropertyChanging("QteACommander");
                _QteACommander = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("QteACommander");
                OnQteACommanderChanged();
            }
        }
        private global::System.Decimal _QteACommander;
        partial void OnQteACommanderChanging(global::System.Decimal value);
        partial void OnQteACommanderChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal QteUnitaire
        {
            get
            {
                return _QteUnitaire;
            }
            set
            {
                OnQteUnitaireChanging(value);
                ReportPropertyChanging("QteUnitaire");
                _QteUnitaire = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("QteUnitaire");
                OnQteUnitaireChanged();
            }
        }
        private global::System.Decimal _QteUnitaire;
        partial void OnQteUnitaireChanging(global::System.Decimal value);
        partial void OnQteUnitaireChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Int32> Ordre
        {
            get
            {
                return _Ordre;
            }
            set
            {
                OnOrdreChanging(value);
                ReportPropertyChanging("Ordre");
                _Ordre = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Ordre");
                OnOrdreChanged();
            }
        }
        private Nullable<global::System.Int32> _Ordre;
        partial void OnOrdreChanging(Nullable<global::System.Int32> value);
        partial void OnOrdreChanged();

        #endregion

    }
    
    /// <summary>
    /// Aucune documentation sur les métadonnées n'est disponible.
    /// </summary>
    [EdmEntityTypeAttribute(NamespaceName="BusinessManagementModel", Name="SIMULATION_STOCK")]
    [Serializable()]
    [DataContractAttribute(IsReference=true)]
    public partial class SIMULATION_STOCK : EntityObject
    {
        #region Méthode de fabrique
    
        /// <summary>
        /// Créez un nouvel objet SIMULATION_STOCK.
        /// </summary>
        /// <param name="numeroSimulation">Valeur initiale de la propriété NumeroSimulation.</param>
        /// <param name="date">Valeur initiale de la propriété Date.</param>
        /// <param name="dateImpression">Valeur initiale de la propriété DateImpression.</param>
        /// <param name="totalAHT">Valeur initiale de la propriété TotalAHT.</param>
        /// <param name="totalATTC">Valeur initiale de la propriété TotalATTC.</param>
        /// <param name="totalVTTC">Valeur initiale de la propriété TotalVTTC.</param>
        /// <param name="codePersonnel">Valeur initiale de la propriété CodePersonnel.</param>
        public static SIMULATION_STOCK CreateSIMULATION_STOCK(global::System.String numeroSimulation, global::System.DateTime date, global::System.DateTime dateImpression, global::System.Decimal totalAHT, global::System.Decimal totalATTC, global::System.Decimal totalVTTC, global::System.Int32 codePersonnel)
        {
            SIMULATION_STOCK sIMULATION_STOCK = new SIMULATION_STOCK();
            sIMULATION_STOCK.NumeroSimulation = numeroSimulation;
            sIMULATION_STOCK.Date = date;
            sIMULATION_STOCK.DateImpression = dateImpression;
            sIMULATION_STOCK.TotalAHT = totalAHT;
            sIMULATION_STOCK.TotalATTC = totalATTC;
            sIMULATION_STOCK.TotalVTTC = totalVTTC;
            sIMULATION_STOCK.CodePersonnel = codePersonnel;
            return sIMULATION_STOCK;
        }

        #endregion

        #region Propriétés simples
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String NumeroSimulation
        {
            get
            {
                return _NumeroSimulation;
            }
            set
            {
                if (_NumeroSimulation != value)
                {
                    OnNumeroSimulationChanging(value);
                    ReportPropertyChanging("NumeroSimulation");
                    _NumeroSimulation = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("NumeroSimulation");
                    OnNumeroSimulationChanged();
                }
            }
        }
        private global::System.String _NumeroSimulation;
        partial void OnNumeroSimulationChanging(global::System.String value);
        partial void OnNumeroSimulationChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.DateTime Date
        {
            get
            {
                return _Date;
            }
            set
            {
                OnDateChanging(value);
                ReportPropertyChanging("Date");
                _Date = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Date");
                OnDateChanged();
            }
        }
        private global::System.DateTime _Date;
        partial void OnDateChanging(global::System.DateTime value);
        partial void OnDateChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.DateTime DateImpression
        {
            get
            {
                return _DateImpression;
            }
            set
            {
                OnDateImpressionChanging(value);
                ReportPropertyChanging("DateImpression");
                _DateImpression = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("DateImpression");
                OnDateImpressionChanged();
            }
        }
        private global::System.DateTime _DateImpression;
        partial void OnDateImpressionChanging(global::System.DateTime value);
        partial void OnDateImpressionChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalAHT
        {
            get
            {
                return _TotalAHT;
            }
            set
            {
                OnTotalAHTChanging(value);
                ReportPropertyChanging("TotalAHT");
                _TotalAHT = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalAHT");
                OnTotalAHTChanged();
            }
        }
        private global::System.Decimal _TotalAHT;
        partial void OnTotalAHTChanging(global::System.Decimal value);
        partial void OnTotalAHTChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalATTC
        {
            get
            {
                return _TotalATTC;
            }
            set
            {
                OnTotalATTCChanging(value);
                ReportPropertyChanging("TotalATTC");
                _TotalATTC = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalATTC");
                OnTotalATTCChanged();
            }
        }
        private global::System.Decimal _TotalATTC;
        partial void OnTotalATTCChanging(global::System.Decimal value);
        partial void OnTotalATTCChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalVTTC
        {
            get
            {
                return _TotalVTTC;
            }
            set
            {
                OnTotalVTTCChanging(value);
                ReportPropertyChanging("TotalVTTC");
                _TotalVTTC = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalVTTC");
                OnTotalVTTCChanged();
            }
        }
        private global::System.Decimal _TotalVTTC;
        partial void OnTotalVTTCChanging(global::System.Decimal value);
        partial void OnTotalVTTCChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Int32> CodeCategorie
        {
            get
            {
                return _CodeCategorie;
            }
            set
            {
                OnCodeCategorieChanging(value);
                ReportPropertyChanging("CodeCategorie");
                _CodeCategorie = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("CodeCategorie");
                OnCodeCategorieChanged();
            }
        }
        private Nullable<global::System.Int32> _CodeCategorie;
        partial void OnCodeCategorieChanging(Nullable<global::System.Int32> value);
        partial void OnCodeCategorieChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 CodePersonnel
        {
            get
            {
                return _CodePersonnel;
            }
            set
            {
                OnCodePersonnelChanging(value);
                ReportPropertyChanging("CodePersonnel");
                _CodePersonnel = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("CodePersonnel");
                OnCodePersonnelChanged();
            }
        }
        private global::System.Int32 _CodePersonnel;
        partial void OnCodePersonnelChanging(global::System.Int32 value);
        partial void OnCodePersonnelChanged();

        #endregion

        #region Propriétés de navigation
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [XmlIgnoreAttribute()]
        [SoapIgnoreAttribute()]
        [DataMemberAttribute()]
        [EdmRelationshipNavigationPropertyAttribute("BusinessManagementModel", "SIMULATION_STOCKSIMULATION_STOCK_DETAILS", "SIMULATION_STOCK_DETAILS")]
        public EntityCollection<SIMULATION_STOCK_DETAILS> SIMULATION_STOCK_DETAILS
        {
            get
            {
                return ((IEntityWithRelationships)this).RelationshipManager.GetRelatedCollection<SIMULATION_STOCK_DETAILS>("BusinessManagementModel.SIMULATION_STOCKSIMULATION_STOCK_DETAILS", "SIMULATION_STOCK_DETAILS");
            }
            set
            {
                if ((value != null))
                {
                    ((IEntityWithRelationships)this).RelationshipManager.InitializeRelatedCollection<SIMULATION_STOCK_DETAILS>("BusinessManagementModel.SIMULATION_STOCKSIMULATION_STOCK_DETAILS", "SIMULATION_STOCK_DETAILS", value);
                }
            }
        }

        #endregion

    }
    
    /// <summary>
    /// Aucune documentation sur les métadonnées n'est disponible.
    /// </summary>
    [EdmEntityTypeAttribute(NamespaceName="BusinessManagementModel", Name="SIMULATION_STOCK_DETAILS")]
    [Serializable()]
    [DataContractAttribute(IsReference=true)]
    public partial class SIMULATION_STOCK_DETAILS : EntityObject
    {
        #region Méthode de fabrique
    
        /// <summary>
        /// Créez un nouvel objet SIMULATION_STOCK_DETAILS.
        /// </summary>
        /// <param name="numeroSimulation">Valeur initiale de la propriété NumeroSimulation.</param>
        /// <param name="codeArticle">Valeur initiale de la propriété CodeArticle.</param>
        /// <param name="designation">Valeur initiale de la propriété Designation.</param>
        /// <param name="codeForme">Valeur initiale de la propriété CodeForme.</param>
        /// <param name="qte">Valeur initiale de la propriété Qte.</param>
        /// <param name="prixAchatHT">Valeur initiale de la propriété PrixAchatHT.</param>
        /// <param name="prixAchatTTC">Valeur initiale de la propriété PrixAchatTTC.</param>
        /// <param name="prixVenteTTC">Valeur initiale de la propriété PrixVenteTTC.</param>
        /// <param name="totalAchatHT">Valeur initiale de la propriété TotalAchatHT.</param>
        /// <param name="totalAchatTTC">Valeur initiale de la propriété TotalAchatTTC.</param>
        /// <param name="totalVenteTTC">Valeur initiale de la propriété TotalVenteTTC.</param>
        public static SIMULATION_STOCK_DETAILS CreateSIMULATION_STOCK_DETAILS(global::System.String numeroSimulation, global::System.String codeArticle, global::System.String designation, global::System.Int32 codeForme, global::System.Int32 qte, global::System.Decimal prixAchatHT, global::System.Decimal prixAchatTTC, global::System.Decimal prixVenteTTC, global::System.Decimal totalAchatHT, global::System.Decimal totalAchatTTC, global::System.Decimal totalVenteTTC)
        {
            SIMULATION_STOCK_DETAILS sIMULATION_STOCK_DETAILS = new SIMULATION_STOCK_DETAILS();
            sIMULATION_STOCK_DETAILS.NumeroSimulation = numeroSimulation;
            sIMULATION_STOCK_DETAILS.CodeArticle = codeArticle;
            sIMULATION_STOCK_DETAILS.Designation = designation;
            sIMULATION_STOCK_DETAILS.CodeForme = codeForme;
            sIMULATION_STOCK_DETAILS.Qte = qte;
            sIMULATION_STOCK_DETAILS.PrixAchatHT = prixAchatHT;
            sIMULATION_STOCK_DETAILS.PrixAchatTTC = prixAchatTTC;
            sIMULATION_STOCK_DETAILS.PrixVenteTTC = prixVenteTTC;
            sIMULATION_STOCK_DETAILS.TotalAchatHT = totalAchatHT;
            sIMULATION_STOCK_DETAILS.TotalAchatTTC = totalAchatTTC;
            sIMULATION_STOCK_DETAILS.TotalVenteTTC = totalVenteTTC;
            return sIMULATION_STOCK_DETAILS;
        }

        #endregion

        #region Propriétés simples
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String NumeroSimulation
        {
            get
            {
                return _NumeroSimulation;
            }
            set
            {
                if (_NumeroSimulation != value)
                {
                    OnNumeroSimulationChanging(value);
                    ReportPropertyChanging("NumeroSimulation");
                    _NumeroSimulation = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("NumeroSimulation");
                    OnNumeroSimulationChanged();
                }
            }
        }
        private global::System.String _NumeroSimulation;
        partial void OnNumeroSimulationChanging(global::System.String value);
        partial void OnNumeroSimulationChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String CodeArticle
        {
            get
            {
                return _CodeArticle;
            }
            set
            {
                if (_CodeArticle != value)
                {
                    OnCodeArticleChanging(value);
                    ReportPropertyChanging("CodeArticle");
                    _CodeArticle = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("CodeArticle");
                    OnCodeArticleChanged();
                }
            }
        }
        private global::System.String _CodeArticle;
        partial void OnCodeArticleChanging(global::System.String value);
        partial void OnCodeArticleChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String Designation
        {
            get
            {
                return _Designation;
            }
            set
            {
                OnDesignationChanging(value);
                ReportPropertyChanging("Designation");
                _Designation = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("Designation");
                OnDesignationChanged();
            }
        }
        private global::System.String _Designation;
        partial void OnDesignationChanging(global::System.String value);
        partial void OnDesignationChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 CodeForme
        {
            get
            {
                return _CodeForme;
            }
            set
            {
                OnCodeFormeChanging(value);
                ReportPropertyChanging("CodeForme");
                _CodeForme = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("CodeForme");
                OnCodeFormeChanged();
            }
        }
        private global::System.Int32 _CodeForme;
        partial void OnCodeFormeChanging(global::System.Int32 value);
        partial void OnCodeFormeChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 Qte
        {
            get
            {
                return _Qte;
            }
            set
            {
                OnQteChanging(value);
                ReportPropertyChanging("Qte");
                _Qte = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Qte");
                OnQteChanged();
            }
        }
        private global::System.Int32 _Qte;
        partial void OnQteChanging(global::System.Int32 value);
        partial void OnQteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal PrixAchatHT
        {
            get
            {
                return _PrixAchatHT;
            }
            set
            {
                OnPrixAchatHTChanging(value);
                ReportPropertyChanging("PrixAchatHT");
                _PrixAchatHT = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("PrixAchatHT");
                OnPrixAchatHTChanged();
            }
        }
        private global::System.Decimal _PrixAchatHT;
        partial void OnPrixAchatHTChanging(global::System.Decimal value);
        partial void OnPrixAchatHTChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal PrixAchatTTC
        {
            get
            {
                return _PrixAchatTTC;
            }
            set
            {
                OnPrixAchatTTCChanging(value);
                ReportPropertyChanging("PrixAchatTTC");
                _PrixAchatTTC = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("PrixAchatTTC");
                OnPrixAchatTTCChanged();
            }
        }
        private global::System.Decimal _PrixAchatTTC;
        partial void OnPrixAchatTTCChanging(global::System.Decimal value);
        partial void OnPrixAchatTTCChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal PrixVenteTTC
        {
            get
            {
                return _PrixVenteTTC;
            }
            set
            {
                OnPrixVenteTTCChanging(value);
                ReportPropertyChanging("PrixVenteTTC");
                _PrixVenteTTC = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("PrixVenteTTC");
                OnPrixVenteTTCChanged();
            }
        }
        private global::System.Decimal _PrixVenteTTC;
        partial void OnPrixVenteTTCChanging(global::System.Decimal value);
        partial void OnPrixVenteTTCChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalAchatHT
        {
            get
            {
                return _TotalAchatHT;
            }
            set
            {
                OnTotalAchatHTChanging(value);
                ReportPropertyChanging("TotalAchatHT");
                _TotalAchatHT = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalAchatHT");
                OnTotalAchatHTChanged();
            }
        }
        private global::System.Decimal _TotalAchatHT;
        partial void OnTotalAchatHTChanging(global::System.Decimal value);
        partial void OnTotalAchatHTChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalAchatTTC
        {
            get
            {
                return _TotalAchatTTC;
            }
            set
            {
                OnTotalAchatTTCChanging(value);
                ReportPropertyChanging("TotalAchatTTC");
                _TotalAchatTTC = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalAchatTTC");
                OnTotalAchatTTCChanged();
            }
        }
        private global::System.Decimal _TotalAchatTTC;
        partial void OnTotalAchatTTCChanging(global::System.Decimal value);
        partial void OnTotalAchatTTCChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalVenteTTC
        {
            get
            {
                return _TotalVenteTTC;
            }
            set
            {
                OnTotalVenteTTCChanging(value);
                ReportPropertyChanging("TotalVenteTTC");
                _TotalVenteTTC = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalVenteTTC");
                OnTotalVenteTTCChanged();
            }
        }
        private global::System.Decimal _TotalVenteTTC;
        partial void OnTotalVenteTTCChanging(global::System.Decimal value);
        partial void OnTotalVenteTTCChanged();

        #endregion

    }
    
    /// <summary>
    /// Aucune documentation sur les métadonnées n'est disponible.
    /// </summary>
    [EdmEntityTypeAttribute(NamespaceName="BusinessManagementModel", Name="VENTE")]
    [Serializable()]
    [DataContractAttribute(IsReference=true)]
    public partial class VENTE : EntityObject
    {
        #region Méthode de fabrique
    
        /// <summary>
        /// Créez un nouvel objet VENTE.
        /// </summary>
        /// <param name="numeroVente">Valeur initiale de la propriété NumeroVente.</param>
        /// <param name="date">Valeur initiale de la propriété Date.</param>
        /// <param name="totalHT">Valeur initiale de la propriété TotalHT.</param>
        /// <param name="totalTTC">Valeur initiale de la propriété TotalTTC.</param>
        /// <param name="tVA">Valeur initiale de la propriété TVA.</param>
        /// <param name="totalRemise">Valeur initiale de la propriété TotalRemise.</param>
        /// <param name="timbre">Valeur initiale de la propriété Timbre.</param>
        /// <param name="codeClient">Valeur initiale de la propriété CodeClient.</param>
        /// <param name="codePersonnel">Valeur initiale de la propriété CodePersonnel.</param>
        /// <param name="libellePoste">Valeur initiale de la propriété LibellePoste.</param>
        /// <param name="recu">Valeur initiale de la propriété Recu.</param>
        /// <param name="montantCnam">Valeur initiale de la propriété MontantCnam.</param>
        /// <param name="montantMutuelle">Valeur initiale de la propriété MontantMutuelle.</param>
        /// <param name="note">Valeur initiale de la propriété Note.</param>
        /// <param name="nomMalade">Valeur initiale de la propriété NomMalade.</param>
        /// <param name="libelleLienDeParente">Valeur initiale de la propriété LibelleLienDeParente.</param>
        /// <param name="tiersPayant">Valeur initiale de la propriété TiersPayant.</param>
        /// <param name="priseEnCharge">Valeur initiale de la propriété PriseEnCharge.</param>
        /// <param name="appareillage">Valeur initiale de la propriété Appareillage.</param>
        /// <param name="identifiantCNAMMedecin">Valeur initiale de la propriété IdentifiantCNAMMedecin.</param>
        /// <param name="vider">Valeur initiale de la propriété Vider.</param>
        /// <param name="numeroPriseEnCharge">Valeur initiale de la propriété NumeroPriseEnCharge.</param>
        /// <param name="numeroBonAchat">Valeur initiale de la propriété NumeroBonAchat.</param>
        /// <param name="dureeTraitement">Valeur initiale de la propriété DureeTraitement.</param>
        public static VENTE CreateVENTE(global::System.String numeroVente, global::System.DateTime date, global::System.Decimal totalHT, global::System.Decimal totalTTC, global::System.Decimal tVA, global::System.Decimal totalRemise, global::System.Decimal timbre, global::System.String codeClient, global::System.String codePersonnel, global::System.String libellePoste, global::System.Decimal recu, global::System.Decimal montantCnam, global::System.Decimal montantMutuelle, global::System.String note, global::System.String nomMalade, global::System.String libelleLienDeParente, global::System.Boolean tiersPayant, global::System.Boolean priseEnCharge, global::System.Boolean appareillage, global::System.String identifiantCNAMMedecin, global::System.Boolean vider, global::System.String numeroPriseEnCharge, global::System.String numeroBonAchat, global::System.Int32 dureeTraitement)
        {
            VENTE vENTE = new VENTE();
            vENTE.NumeroVente = numeroVente;
            vENTE.Date = date;
            vENTE.TotalHT = totalHT;
            vENTE.TotalTTC = totalTTC;
            vENTE.TVA = tVA;
            vENTE.TotalRemise = totalRemise;
            vENTE.Timbre = timbre;
            vENTE.CodeClient = codeClient;
            vENTE.CodePersonnel = codePersonnel;
            vENTE.LibellePoste = libellePoste;
            vENTE.Recu = recu;
            vENTE.MontantCnam = montantCnam;
            vENTE.MontantMutuelle = montantMutuelle;
            vENTE.Note = note;
            vENTE.NomMalade = nomMalade;
            vENTE.LibelleLienDeParente = libelleLienDeParente;
            vENTE.TiersPayant = tiersPayant;
            vENTE.PriseEnCharge = priseEnCharge;
            vENTE.Appareillage = appareillage;
            vENTE.IdentifiantCNAMMedecin = identifiantCNAMMedecin;
            vENTE.Vider = vider;
            vENTE.NumeroPriseEnCharge = numeroPriseEnCharge;
            vENTE.NumeroBonAchat = numeroBonAchat;
            vENTE.DureeTraitement = dureeTraitement;
            return vENTE;
        }

        #endregion

        #region Propriétés simples
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String NumeroVente
        {
            get
            {
                return _NumeroVente;
            }
            set
            {
                if (_NumeroVente != value)
                {
                    OnNumeroVenteChanging(value);
                    ReportPropertyChanging("NumeroVente");
                    _NumeroVente = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("NumeroVente");
                    OnNumeroVenteChanged();
                }
            }
        }
        private global::System.String _NumeroVente;
        partial void OnNumeroVenteChanging(global::System.String value);
        partial void OnNumeroVenteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.DateTime Date
        {
            get
            {
                return _Date;
            }
            set
            {
                OnDateChanging(value);
                ReportPropertyChanging("Date");
                _Date = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Date");
                OnDateChanged();
            }
        }
        private global::System.DateTime _Date;
        partial void OnDateChanging(global::System.DateTime value);
        partial void OnDateChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalHT
        {
            get
            {
                return _TotalHT;
            }
            set
            {
                OnTotalHTChanging(value);
                ReportPropertyChanging("TotalHT");
                _TotalHT = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalHT");
                OnTotalHTChanged();
            }
        }
        private global::System.Decimal _TotalHT;
        partial void OnTotalHTChanging(global::System.Decimal value);
        partial void OnTotalHTChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalTTC
        {
            get
            {
                return _TotalTTC;
            }
            set
            {
                OnTotalTTCChanging(value);
                ReportPropertyChanging("TotalTTC");
                _TotalTTC = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalTTC");
                OnTotalTTCChanged();
            }
        }
        private global::System.Decimal _TotalTTC;
        partial void OnTotalTTCChanging(global::System.Decimal value);
        partial void OnTotalTTCChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TVA
        {
            get
            {
                return _TVA;
            }
            set
            {
                OnTVAChanging(value);
                ReportPropertyChanging("TVA");
                _TVA = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TVA");
                OnTVAChanged();
            }
        }
        private global::System.Decimal _TVA;
        partial void OnTVAChanging(global::System.Decimal value);
        partial void OnTVAChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalRemise
        {
            get
            {
                return _TotalRemise;
            }
            set
            {
                OnTotalRemiseChanging(value);
                ReportPropertyChanging("TotalRemise");
                _TotalRemise = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalRemise");
                OnTotalRemiseChanged();
            }
        }
        private global::System.Decimal _TotalRemise;
        partial void OnTotalRemiseChanging(global::System.Decimal value);
        partial void OnTotalRemiseChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal Timbre
        {
            get
            {
                return _Timbre;
            }
            set
            {
                OnTimbreChanging(value);
                ReportPropertyChanging("Timbre");
                _Timbre = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Timbre");
                OnTimbreChanged();
            }
        }
        private global::System.Decimal _Timbre;
        partial void OnTimbreChanging(global::System.Decimal value);
        partial void OnTimbreChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String CodeClient
        {
            get
            {
                return _CodeClient;
            }
            set
            {
                OnCodeClientChanging(value);
                ReportPropertyChanging("CodeClient");
                _CodeClient = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("CodeClient");
                OnCodeClientChanged();
            }
        }
        private global::System.String _CodeClient;
        partial void OnCodeClientChanging(global::System.String value);
        partial void OnCodeClientChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String CodePersonnel
        {
            get
            {
                return _CodePersonnel;
            }
            set
            {
                OnCodePersonnelChanging(value);
                ReportPropertyChanging("CodePersonnel");
                _CodePersonnel = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("CodePersonnel");
                OnCodePersonnelChanged();
            }
        }
        private global::System.String _CodePersonnel;
        partial void OnCodePersonnelChanging(global::System.String value);
        partial void OnCodePersonnelChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Int32> CodeAPCI
        {
            get
            {
                return _CodeAPCI;
            }
            set
            {
                OnCodeAPCIChanging(value);
                ReportPropertyChanging("CodeAPCI");
                _CodeAPCI = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("CodeAPCI");
                OnCodeAPCIChanged();
            }
        }
        private Nullable<global::System.Int32> _CodeAPCI;
        partial void OnCodeAPCIChanging(Nullable<global::System.Int32> value);
        partial void OnCodeAPCIChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Int32> CodeDeFamille
        {
            get
            {
                return _CodeDeFamille;
            }
            set
            {
                OnCodeDeFamilleChanging(value);
                ReportPropertyChanging("CodeDeFamille");
                _CodeDeFamille = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("CodeDeFamille");
                OnCodeDeFamilleChanged();
            }
        }
        private Nullable<global::System.Int32> _CodeDeFamille;
        partial void OnCodeDeFamilleChanging(Nullable<global::System.Int32> value);
        partial void OnCodeDeFamilleChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public global::System.String CodeMedecinFamille
        {
            get
            {
                return _CodeMedecinFamille;
            }
            set
            {
                OnCodeMedecinFamilleChanging(value);
                ReportPropertyChanging("CodeMedecinFamille");
                _CodeMedecinFamille = StructuralObject.SetValidValue(value, true);
                ReportPropertyChanged("CodeMedecinFamille");
                OnCodeMedecinFamilleChanged();
            }
        }
        private global::System.String _CodeMedecinFamille;
        partial void OnCodeMedecinFamilleChanging(global::System.String value);
        partial void OnCodeMedecinFamilleChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public global::System.String CodeMedecinPrescripteur
        {
            get
            {
                return _CodeMedecinPrescripteur;
            }
            set
            {
                OnCodeMedecinPrescripteurChanging(value);
                ReportPropertyChanging("CodeMedecinPrescripteur");
                _CodeMedecinPrescripteur = StructuralObject.SetValidValue(value, true);
                ReportPropertyChanged("CodeMedecinPrescripteur");
                OnCodeMedecinPrescripteurChanged();
            }
        }
        private global::System.String _CodeMedecinPrescripteur;
        partial void OnCodeMedecinPrescripteurChanging(global::System.String value);
        partial void OnCodeMedecinPrescripteurChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String LibellePoste
        {
            get
            {
                return _LibellePoste;
            }
            set
            {
                OnLibellePosteChanging(value);
                ReportPropertyChanging("LibellePoste");
                _LibellePoste = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("LibellePoste");
                OnLibellePosteChanged();
            }
        }
        private global::System.String _LibellePoste;
        partial void OnLibellePosteChanging(global::System.String value);
        partial void OnLibellePosteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal Recu
        {
            get
            {
                return _Recu;
            }
            set
            {
                OnRecuChanging(value);
                ReportPropertyChanging("Recu");
                _Recu = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Recu");
                OnRecuChanged();
            }
        }
        private global::System.Decimal _Recu;
        partial void OnRecuChanging(global::System.Decimal value);
        partial void OnRecuChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.DateTime> DateOrdonnance
        {
            get
            {
                return _DateOrdonnance;
            }
            set
            {
                OnDateOrdonnanceChanging(value);
                ReportPropertyChanging("DateOrdonnance");
                _DateOrdonnance = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("DateOrdonnance");
                OnDateOrdonnanceChanged();
            }
        }
        private Nullable<global::System.DateTime> _DateOrdonnance;
        partial void OnDateOrdonnanceChanging(Nullable<global::System.DateTime> value);
        partial void OnDateOrdonnanceChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal MontantCnam
        {
            get
            {
                return _MontantCnam;
            }
            set
            {
                OnMontantCnamChanging(value);
                ReportPropertyChanging("MontantCnam");
                _MontantCnam = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("MontantCnam");
                OnMontantCnamChanged();
            }
        }
        private global::System.Decimal _MontantCnam;
        partial void OnMontantCnamChanging(global::System.Decimal value);
        partial void OnMontantCnamChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal MontantMutuelle
        {
            get
            {
                return _MontantMutuelle;
            }
            set
            {
                OnMontantMutuelleChanging(value);
                ReportPropertyChanging("MontantMutuelle");
                _MontantMutuelle = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("MontantMutuelle");
                OnMontantMutuelleChanged();
            }
        }
        private global::System.Decimal _MontantMutuelle;
        partial void OnMontantMutuelleChanging(global::System.Decimal value);
        partial void OnMontantMutuelleChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String Note
        {
            get
            {
                return _Note;
            }
            set
            {
                OnNoteChanging(value);
                ReportPropertyChanging("Note");
                _Note = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("Note");
                OnNoteChanged();
            }
        }
        private global::System.String _Note;
        partial void OnNoteChanging(global::System.String value);
        partial void OnNoteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Int32> CodeNatureReglement
        {
            get
            {
                return _CodeNatureReglement;
            }
            set
            {
                OnCodeNatureReglementChanging(value);
                ReportPropertyChanging("CodeNatureReglement");
                _CodeNatureReglement = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("CodeNatureReglement");
                OnCodeNatureReglementChanged();
            }
        }
        private Nullable<global::System.Int32> _CodeNatureReglement;
        partial void OnCodeNatureReglementChanging(Nullable<global::System.Int32> value);
        partial void OnCodeNatureReglementChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public global::System.String CodeMutuelle
        {
            get
            {
                return _CodeMutuelle;
            }
            set
            {
                OnCodeMutuelleChanging(value);
                ReportPropertyChanging("CodeMutuelle");
                _CodeMutuelle = StructuralObject.SetValidValue(value, true);
                ReportPropertyChanged("CodeMutuelle");
                OnCodeMutuelleChanged();
            }
        }
        private global::System.String _CodeMutuelle;
        partial void OnCodeMutuelleChanging(global::System.String value);
        partial void OnCodeMutuelleChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String NomMalade
        {
            get
            {
                return _NomMalade;
            }
            set
            {
                OnNomMaladeChanging(value);
                ReportPropertyChanging("NomMalade");
                _NomMalade = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("NomMalade");
                OnNomMaladeChanged();
            }
        }
        private global::System.String _NomMalade;
        partial void OnNomMaladeChanging(global::System.String value);
        partial void OnNomMaladeChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Int32> Rang
        {
            get
            {
                return _Rang;
            }
            set
            {
                OnRangChanging(value);
                ReportPropertyChanging("Rang");
                _Rang = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Rang");
                OnRangChanged();
            }
        }
        private Nullable<global::System.Int32> _Rang;
        partial void OnRangChanging(Nullable<global::System.Int32> value);
        partial void OnRangChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.DateTime> DateNaissance
        {
            get
            {
                return _DateNaissance;
            }
            set
            {
                OnDateNaissanceChanging(value);
                ReportPropertyChanging("DateNaissance");
                _DateNaissance = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("DateNaissance");
                OnDateNaissanceChanged();
            }
        }
        private Nullable<global::System.DateTime> _DateNaissance;
        partial void OnDateNaissanceChanging(Nullable<global::System.DateTime> value);
        partial void OnDateNaissanceChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Int32> CodeLienDeParente
        {
            get
            {
                return _CodeLienDeParente;
            }
            set
            {
                OnCodeLienDeParenteChanging(value);
                ReportPropertyChanging("CodeLienDeParente");
                _CodeLienDeParente = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("CodeLienDeParente");
                OnCodeLienDeParenteChanged();
            }
        }
        private Nullable<global::System.Int32> _CodeLienDeParente;
        partial void OnCodeLienDeParenteChanging(Nullable<global::System.Int32> value);
        partial void OnCodeLienDeParenteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String LibelleLienDeParente
        {
            get
            {
                return _LibelleLienDeParente;
            }
            set
            {
                OnLibelleLienDeParenteChanging(value);
                ReportPropertyChanging("LibelleLienDeParente");
                _LibelleLienDeParente = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("LibelleLienDeParente");
                OnLibelleLienDeParenteChanged();
            }
        }
        private global::System.String _LibelleLienDeParente;
        partial void OnLibelleLienDeParenteChanging(global::System.String value);
        partial void OnLibelleLienDeParenteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Boolean TiersPayant
        {
            get
            {
                return _TiersPayant;
            }
            set
            {
                OnTiersPayantChanging(value);
                ReportPropertyChanging("TiersPayant");
                _TiersPayant = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TiersPayant");
                OnTiersPayantChanged();
            }
        }
        private global::System.Boolean _TiersPayant;
        partial void OnTiersPayantChanging(global::System.Boolean value);
        partial void OnTiersPayantChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Boolean PriseEnCharge
        {
            get
            {
                return _PriseEnCharge;
            }
            set
            {
                OnPriseEnChargeChanging(value);
                ReportPropertyChanging("PriseEnCharge");
                _PriseEnCharge = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("PriseEnCharge");
                OnPriseEnChargeChanged();
            }
        }
        private global::System.Boolean _PriseEnCharge;
        partial void OnPriseEnChargeChanging(global::System.Boolean value);
        partial void OnPriseEnChargeChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Boolean Appareillage
        {
            get
            {
                return _Appareillage;
            }
            set
            {
                OnAppareillageChanging(value);
                ReportPropertyChanging("Appareillage");
                _Appareillage = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Appareillage");
                OnAppareillageChanged();
            }
        }
        private global::System.Boolean _Appareillage;
        partial void OnAppareillageChanging(global::System.Boolean value);
        partial void OnAppareillageChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String IdentifiantCNAMMedecin
        {
            get
            {
                return _IdentifiantCNAMMedecin;
            }
            set
            {
                OnIdentifiantCNAMMedecinChanging(value);
                ReportPropertyChanging("IdentifiantCNAMMedecin");
                _IdentifiantCNAMMedecin = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("IdentifiantCNAMMedecin");
                OnIdentifiantCNAMMedecinChanged();
            }
        }
        private global::System.String _IdentifiantCNAMMedecin;
        partial void OnIdentifiantCNAMMedecinChanging(global::System.String value);
        partial void OnIdentifiantCNAMMedecinChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public global::System.String Libelle1
        {
            get
            {
                return _Libelle1;
            }
            set
            {
                OnLibelle1Changing(value);
                ReportPropertyChanging("Libelle1");
                _Libelle1 = StructuralObject.SetValidValue(value, true);
                ReportPropertyChanged("Libelle1");
                OnLibelle1Changed();
            }
        }
        private global::System.String _Libelle1;
        partial void OnLibelle1Changing(global::System.String value);
        partial void OnLibelle1Changed();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public global::System.String Libelle2
        {
            get
            {
                return _Libelle2;
            }
            set
            {
                OnLibelle2Changing(value);
                ReportPropertyChanging("Libelle2");
                _Libelle2 = StructuralObject.SetValidValue(value, true);
                ReportPropertyChanged("Libelle2");
                OnLibelle2Changed();
            }
        }
        private global::System.String _Libelle2;
        partial void OnLibelle2Changing(global::System.String value);
        partial void OnLibelle2Changed();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public global::System.String Libelle3
        {
            get
            {
                return _Libelle3;
            }
            set
            {
                OnLibelle3Changing(value);
                ReportPropertyChanging("Libelle3");
                _Libelle3 = StructuralObject.SetValidValue(value, true);
                ReportPropertyChanged("Libelle3");
                OnLibelle3Changed();
            }
        }
        private global::System.String _Libelle3;
        partial void OnLibelle3Changing(global::System.String value);
        partial void OnLibelle3Changed();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public global::System.String Libelle4
        {
            get
            {
                return _Libelle4;
            }
            set
            {
                OnLibelle4Changing(value);
                ReportPropertyChanging("Libelle4");
                _Libelle4 = StructuralObject.SetValidValue(value, true);
                ReportPropertyChanged("Libelle4");
                OnLibelle4Changed();
            }
        }
        private global::System.String _Libelle4;
        partial void OnLibelle4Changing(global::System.String value);
        partial void OnLibelle4Changed();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public global::System.String Libelle5
        {
            get
            {
                return _Libelle5;
            }
            set
            {
                OnLibelle5Changing(value);
                ReportPropertyChanging("Libelle5");
                _Libelle5 = StructuralObject.SetValidValue(value, true);
                ReportPropertyChanged("Libelle5");
                OnLibelle5Changed();
            }
        }
        private global::System.String _Libelle5;
        partial void OnLibelle5Changing(global::System.String value);
        partial void OnLibelle5Changed();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public global::System.String NumeroFacture
        {
            get
            {
                return _NumeroFacture;
            }
            set
            {
                OnNumeroFactureChanging(value);
                ReportPropertyChanging("NumeroFacture");
                _NumeroFacture = StructuralObject.SetValidValue(value, true);
                ReportPropertyChanged("NumeroFacture");
                OnNumeroFactureChanged();
            }
        }
        private global::System.String _NumeroFacture;
        partial void OnNumeroFactureChanging(global::System.String value);
        partial void OnNumeroFactureChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Boolean> Totaliseur
        {
            get
            {
                return _Totaliseur;
            }
            set
            {
                OnTotaliseurChanging(value);
                ReportPropertyChanging("Totaliseur");
                _Totaliseur = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Totaliseur");
                OnTotaliseurChanged();
            }
        }
        private Nullable<global::System.Boolean> _Totaliseur;
        partial void OnTotaliseurChanging(Nullable<global::System.Boolean> value);
        partial void OnTotaliseurChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Boolean Vider
        {
            get
            {
                return _Vider;
            }
            set
            {
                OnViderChanging(value);
                ReportPropertyChanging("Vider");
                _Vider = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Vider");
                OnViderChanged();
            }
        }
        private global::System.Boolean _Vider;
        partial void OnViderChanging(global::System.Boolean value);
        partial void OnViderChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String NumeroPriseEnCharge
        {
            get
            {
                return _NumeroPriseEnCharge;
            }
            set
            {
                OnNumeroPriseEnChargeChanging(value);
                ReportPropertyChanging("NumeroPriseEnCharge");
                _NumeroPriseEnCharge = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("NumeroPriseEnCharge");
                OnNumeroPriseEnChargeChanged();
            }
        }
        private global::System.String _NumeroPriseEnCharge;
        partial void OnNumeroPriseEnChargeChanging(global::System.String value);
        partial void OnNumeroPriseEnChargeChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String NumeroBonAchat
        {
            get
            {
                return _NumeroBonAchat;
            }
            set
            {
                OnNumeroBonAchatChanging(value);
                ReportPropertyChanging("NumeroBonAchat");
                _NumeroBonAchat = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("NumeroBonAchat");
                OnNumeroBonAchatChanged();
            }
        }
        private global::System.String _NumeroBonAchat;
        partial void OnNumeroBonAchatChanging(global::System.String value);
        partial void OnNumeroBonAchatChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 DureeTraitement
        {
            get
            {
                return _DureeTraitement;
            }
            set
            {
                OnDureeTraitementChanging(value);
                ReportPropertyChanging("DureeTraitement");
                _DureeTraitement = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("DureeTraitement");
                OnDureeTraitementChanged();
            }
        }
        private global::System.Int32 _DureeTraitement;
        partial void OnDureeTraitementChanging(global::System.Int32 value);
        partial void OnDureeTraitementChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Boolean> OMF
        {
            get
            {
                return _OMF;
            }
            set
            {
                OnOMFChanging(value);
                ReportPropertyChanging("OMF");
                _OMF = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("OMF");
                OnOMFChanged();
            }
        }
        private Nullable<global::System.Boolean> _OMF;
        partial void OnOMFChanging(Nullable<global::System.Boolean> value);
        partial void OnOMFChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Boolean> APCI
        {
            get
            {
                return _APCI;
            }
            set
            {
                OnAPCIChanging(value);
                ReportPropertyChanging("APCI");
                _APCI = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("APCI");
                OnAPCIChanged();
            }
        }
        private Nullable<global::System.Boolean> _APCI;
        partial void OnAPCIChanging(Nullable<global::System.Boolean> value);
        partial void OnAPCIChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public global::System.String CodeAppareillage
        {
            get
            {
                return _CodeAppareillage;
            }
            set
            {
                OnCodeAppareillageChanging(value);
                ReportPropertyChanging("CodeAppareillage");
                _CodeAppareillage = StructuralObject.SetValidValue(value, true);
                ReportPropertyChanged("CodeAppareillage");
                OnCodeAppareillageChanged();
            }
        }
        private global::System.String _CodeAppareillage;
        partial void OnCodeAppareillageChanging(global::System.String value);
        partial void OnCodeAppareillageChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public global::System.String NomInscritSurLeCheque
        {
            get
            {
                return _NomInscritSurLeCheque;
            }
            set
            {
                OnNomInscritSurLeChequeChanging(value);
                ReportPropertyChanging("NomInscritSurLeCheque");
                _NomInscritSurLeCheque = StructuralObject.SetValidValue(value, true);
                ReportPropertyChanged("NomInscritSurLeCheque");
                OnNomInscritSurLeChequeChanged();
            }
        }
        private global::System.String _NomInscritSurLeCheque;
        partial void OnNomInscritSurLeChequeChanging(global::System.String value);
        partial void OnNomInscritSurLeChequeChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public global::System.String NumeroCheque
        {
            get
            {
                return _NumeroCheque;
            }
            set
            {
                OnNumeroChequeChanging(value);
                ReportPropertyChanging("NumeroCheque");
                _NumeroCheque = StructuralObject.SetValidValue(value, true);
                ReportPropertyChanged("NumeroCheque");
                OnNumeroChequeChanged();
            }
        }
        private global::System.String _NumeroCheque;
        partial void OnNumeroChequeChanging(global::System.String value);
        partial void OnNumeroChequeChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.DateTime> DateEcheance
        {
            get
            {
                return _DateEcheance;
            }
            set
            {
                OnDateEcheanceChanging(value);
                ReportPropertyChanging("DateEcheance");
                _DateEcheance = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("DateEcheance");
                OnDateEcheanceChanged();
            }
        }
        private Nullable<global::System.DateTime> _DateEcheance;
        partial void OnDateEcheanceChanging(Nullable<global::System.DateTime> value);
        partial void OnDateEcheanceChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Int32> IDFacturationClient
        {
            get
            {
                return _IDFacturationClient;
            }
            set
            {
                OnIDFacturationClientChanging(value);
                ReportPropertyChanging("IDFacturationClient");
                _IDFacturationClient = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("IDFacturationClient");
                OnIDFacturationClientChanged();
            }
        }
        private Nullable<global::System.Int32> _IDFacturationClient;
        partial void OnIDFacturationClientChanging(Nullable<global::System.Int32> value);
        partial void OnIDFacturationClientChanged();

        #endregion

        #region Propriétés de navigation
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [XmlIgnoreAttribute()]
        [SoapIgnoreAttribute()]
        [DataMemberAttribute()]
        [EdmRelationshipNavigationPropertyAttribute("BusinessManagementModel", "VENTEVENTE_DETAILS", "VENTE_DETAILS")]
        public EntityCollection<VENTE_DETAILS> VENTE_DETAILS
        {
            get
            {
                return ((IEntityWithRelationships)this).RelationshipManager.GetRelatedCollection<VENTE_DETAILS>("BusinessManagementModel.VENTEVENTE_DETAILS", "VENTE_DETAILS");
            }
            set
            {
                if ((value != null))
                {
                    ((IEntityWithRelationships)this).RelationshipManager.InitializeRelatedCollection<VENTE_DETAILS>("BusinessManagementModel.VENTEVENTE_DETAILS", "VENTE_DETAILS", value);
                }
            }
        }

        #endregion

    }
    
    /// <summary>
    /// Aucune documentation sur les métadonnées n'est disponible.
    /// </summary>
    [EdmEntityTypeAttribute(NamespaceName="BusinessManagementModel", Name="VENTE_DETAILS")]
    [Serializable()]
    [DataContractAttribute(IsReference=true)]
    public partial class VENTE_DETAILS : EntityObject
    {
        #region Méthode de fabrique
    
        /// <summary>
        /// Créez un nouvel objet VENTE_DETAILS.
        /// </summary>
        /// <param name="id">Valeur initiale de la propriété Id.</param>
        /// <param name="numeroVente">Valeur initiale de la propriété NumeroVente.</param>
        /// <param name="codeArticle">Valeur initiale de la propriété CodeArticle.</param>
        /// <param name="numeroLotArticle">Valeur initiale de la propriété NumeroLotArticle.</param>
        /// <param name="ordre">Valeur initiale de la propriété Ordre.</param>
        /// <param name="codeABarre">Valeur initiale de la propriété CodeABarre.</param>
        /// <param name="designation">Valeur initiale de la propriété Designation.</param>
        /// <param name="codeForme">Valeur initiale de la propriété CodeForme.</param>
        /// <param name="qte">Valeur initiale de la propriété Qte.</param>
        /// <param name="prixAchat">Valeur initiale de la propriété PrixAchat.</param>
        /// <param name="prixHT">Valeur initiale de la propriété PrixHT.</param>
        /// <param name="totalHT">Valeur initiale de la propriété TotalHT.</param>
        /// <param name="prixTTC">Valeur initiale de la propriété PrixTTC.</param>
        /// <param name="totalTTC">Valeur initiale de la propriété TotalTTC.</param>
        /// <param name="remise">Valeur initiale de la propriété Remise.</param>
        /// <param name="tVA">Valeur initiale de la propriété TVA.</param>
        /// <param name="totalTVA">Valeur initiale de la propriété TotalTVA.</param>
        /// <param name="honoraire">Valeur initiale de la propriété Honoraire.</param>
        /// <param name="stock">Valeur initiale de la propriété Stock.</param>
        /// <param name="dureeTraitement">Valeur initiale de la propriété DureeTraitement.</param>
        public static VENTE_DETAILS CreateVENTE_DETAILS(global::System.Int32 id, global::System.String numeroVente, global::System.String codeArticle, global::System.String numeroLotArticle, global::System.Int32 ordre, global::System.String codeABarre, global::System.String designation, global::System.Int32 codeForme, global::System.Int32 qte, global::System.Decimal prixAchat, global::System.Decimal prixHT, global::System.Decimal totalHT, global::System.Decimal prixTTC, global::System.Decimal totalTTC, global::System.Decimal remise, global::System.Decimal tVA, global::System.Decimal totalTVA, global::System.Decimal honoraire, global::System.Int32 stock, global::System.Int32 dureeTraitement)
        {
            VENTE_DETAILS vENTE_DETAILS = new VENTE_DETAILS();
            vENTE_DETAILS.Id = id;
            vENTE_DETAILS.NumeroVente = numeroVente;
            vENTE_DETAILS.CodeArticle = codeArticle;
            vENTE_DETAILS.NumeroLotArticle = numeroLotArticle;
            vENTE_DETAILS.Ordre = ordre;
            vENTE_DETAILS.CodeABarre = codeABarre;
            vENTE_DETAILS.Designation = designation;
            vENTE_DETAILS.CodeForme = codeForme;
            vENTE_DETAILS.Qte = qte;
            vENTE_DETAILS.PrixAchat = prixAchat;
            vENTE_DETAILS.PrixHT = prixHT;
            vENTE_DETAILS.TotalHT = totalHT;
            vENTE_DETAILS.PrixTTC = prixTTC;
            vENTE_DETAILS.TotalTTC = totalTTC;
            vENTE_DETAILS.Remise = remise;
            vENTE_DETAILS.TVA = tVA;
            vENTE_DETAILS.TotalTVA = totalTVA;
            vENTE_DETAILS.Honoraire = honoraire;
            vENTE_DETAILS.Stock = stock;
            vENTE_DETAILS.DureeTraitement = dureeTraitement;
            return vENTE_DETAILS;
        }

        #endregion

        #region Propriétés simples
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 Id
        {
            get
            {
                return _Id;
            }
            set
            {
                if (_Id != value)
                {
                    OnIdChanging(value);
                    ReportPropertyChanging("Id");
                    _Id = StructuralObject.SetValidValue(value);
                    ReportPropertyChanged("Id");
                    OnIdChanged();
                }
            }
        }
        private global::System.Int32 _Id;
        partial void OnIdChanging(global::System.Int32 value);
        partial void OnIdChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String NumeroVente
        {
            get
            {
                return _NumeroVente;
            }
            set
            {
                if (_NumeroVente != value)
                {
                    OnNumeroVenteChanging(value);
                    ReportPropertyChanging("NumeroVente");
                    _NumeroVente = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("NumeroVente");
                    OnNumeroVenteChanged();
                }
            }
        }
        private global::System.String _NumeroVente;
        partial void OnNumeroVenteChanging(global::System.String value);
        partial void OnNumeroVenteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String CodeArticle
        {
            get
            {
                return _CodeArticle;
            }
            set
            {
                if (_CodeArticle != value)
                {
                    OnCodeArticleChanging(value);
                    ReportPropertyChanging("CodeArticle");
                    _CodeArticle = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("CodeArticle");
                    OnCodeArticleChanged();
                }
            }
        }
        private global::System.String _CodeArticle;
        partial void OnCodeArticleChanging(global::System.String value);
        partial void OnCodeArticleChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String NumeroLotArticle
        {
            get
            {
                return _NumeroLotArticle;
            }
            set
            {
                if (_NumeroLotArticle != value)
                {
                    OnNumeroLotArticleChanging(value);
                    ReportPropertyChanging("NumeroLotArticle");
                    _NumeroLotArticle = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("NumeroLotArticle");
                    OnNumeroLotArticleChanged();
                }
            }
        }
        private global::System.String _NumeroLotArticle;
        partial void OnNumeroLotArticleChanging(global::System.String value);
        partial void OnNumeroLotArticleChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 Ordre
        {
            get
            {
                return _Ordre;
            }
            set
            {
                OnOrdreChanging(value);
                ReportPropertyChanging("Ordre");
                _Ordre = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Ordre");
                OnOrdreChanged();
            }
        }
        private global::System.Int32 _Ordre;
        partial void OnOrdreChanging(global::System.Int32 value);
        partial void OnOrdreChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String CodeABarre
        {
            get
            {
                return _CodeABarre;
            }
            set
            {
                OnCodeABarreChanging(value);
                ReportPropertyChanging("CodeABarre");
                _CodeABarre = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("CodeABarre");
                OnCodeABarreChanged();
            }
        }
        private global::System.String _CodeABarre;
        partial void OnCodeABarreChanging(global::System.String value);
        partial void OnCodeABarreChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String Designation
        {
            get
            {
                return _Designation;
            }
            set
            {
                OnDesignationChanging(value);
                ReportPropertyChanging("Designation");
                _Designation = StructuralObject.SetValidValue(value, false);
                ReportPropertyChanged("Designation");
                OnDesignationChanged();
            }
        }
        private global::System.String _Designation;
        partial void OnDesignationChanging(global::System.String value);
        partial void OnDesignationChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 CodeForme
        {
            get
            {
                return _CodeForme;
            }
            set
            {
                OnCodeFormeChanging(value);
                ReportPropertyChanging("CodeForme");
                _CodeForme = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("CodeForme");
                OnCodeFormeChanged();
            }
        }
        private global::System.Int32 _CodeForme;
        partial void OnCodeFormeChanging(global::System.Int32 value);
        partial void OnCodeFormeChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 Qte
        {
            get
            {
                return _Qte;
            }
            set
            {
                OnQteChanging(value);
                ReportPropertyChanging("Qte");
                _Qte = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Qte");
                OnQteChanged();
            }
        }
        private global::System.Int32 _Qte;
        partial void OnQteChanging(global::System.Int32 value);
        partial void OnQteChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal PrixAchat
        {
            get
            {
                return _PrixAchat;
            }
            set
            {
                OnPrixAchatChanging(value);
                ReportPropertyChanging("PrixAchat");
                _PrixAchat = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("PrixAchat");
                OnPrixAchatChanged();
            }
        }
        private global::System.Decimal _PrixAchat;
        partial void OnPrixAchatChanging(global::System.Decimal value);
        partial void OnPrixAchatChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal PrixHT
        {
            get
            {
                return _PrixHT;
            }
            set
            {
                OnPrixHTChanging(value);
                ReportPropertyChanging("PrixHT");
                _PrixHT = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("PrixHT");
                OnPrixHTChanged();
            }
        }
        private global::System.Decimal _PrixHT;
        partial void OnPrixHTChanging(global::System.Decimal value);
        partial void OnPrixHTChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalHT
        {
            get
            {
                return _TotalHT;
            }
            set
            {
                OnTotalHTChanging(value);
                ReportPropertyChanging("TotalHT");
                _TotalHT = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalHT");
                OnTotalHTChanged();
            }
        }
        private global::System.Decimal _TotalHT;
        partial void OnTotalHTChanging(global::System.Decimal value);
        partial void OnTotalHTChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal PrixTTC
        {
            get
            {
                return _PrixTTC;
            }
            set
            {
                OnPrixTTCChanging(value);
                ReportPropertyChanging("PrixTTC");
                _PrixTTC = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("PrixTTC");
                OnPrixTTCChanged();
            }
        }
        private global::System.Decimal _PrixTTC;
        partial void OnPrixTTCChanging(global::System.Decimal value);
        partial void OnPrixTTCChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalTTC
        {
            get
            {
                return _TotalTTC;
            }
            set
            {
                OnTotalTTCChanging(value);
                ReportPropertyChanging("TotalTTC");
                _TotalTTC = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalTTC");
                OnTotalTTCChanged();
            }
        }
        private global::System.Decimal _TotalTTC;
        partial void OnTotalTTCChanging(global::System.Decimal value);
        partial void OnTotalTTCChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal Remise
        {
            get
            {
                return _Remise;
            }
            set
            {
                OnRemiseChanging(value);
                ReportPropertyChanging("Remise");
                _Remise = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Remise");
                OnRemiseChanged();
            }
        }
        private global::System.Decimal _Remise;
        partial void OnRemiseChanging(global::System.Decimal value);
        partial void OnRemiseChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TVA
        {
            get
            {
                return _TVA;
            }
            set
            {
                OnTVAChanging(value);
                ReportPropertyChanging("TVA");
                _TVA = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TVA");
                OnTVAChanged();
            }
        }
        private global::System.Decimal _TVA;
        partial void OnTVAChanging(global::System.Decimal value);
        partial void OnTVAChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal TotalTVA
        {
            get
            {
                return _TotalTVA;
            }
            set
            {
                OnTotalTVAChanging(value);
                ReportPropertyChanging("TotalTVA");
                _TotalTVA = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TotalTVA");
                OnTotalTVAChanged();
            }
        }
        private global::System.Decimal _TotalTVA;
        partial void OnTotalTVAChanging(global::System.Decimal value);
        partial void OnTotalTVAChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Decimal Honoraire
        {
            get
            {
                return _Honoraire;
            }
            set
            {
                OnHonoraireChanging(value);
                ReportPropertyChanging("Honoraire");
                _Honoraire = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Honoraire");
                OnHonoraireChanged();
            }
        }
        private global::System.Decimal _Honoraire;
        partial void OnHonoraireChanging(global::System.Decimal value);
        partial void OnHonoraireChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 Stock
        {
            get
            {
                return _Stock;
            }
            set
            {
                OnStockChanging(value);
                ReportPropertyChanging("Stock");
                _Stock = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("Stock");
                OnStockChanged();
            }
        }
        private global::System.Int32 _Stock;
        partial void OnStockChanging(global::System.Int32 value);
        partial void OnStockChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.DateTime> DateDePeremption
        {
            get
            {
                return _DateDePeremption;
            }
            set
            {
                OnDateDePeremptionChanging(value);
                ReportPropertyChanging("DateDePeremption");
                _DateDePeremption = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("DateDePeremption");
                OnDateDePeremptionChanged();
            }
        }
        private Nullable<global::System.DateTime> _DateDePeremption;
        partial void OnDateDePeremptionChanging(Nullable<global::System.DateTime> value);
        partial void OnDateDePeremptionChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Boolean> PriseEnCharge
        {
            get
            {
                return _PriseEnCharge;
            }
            set
            {
                OnPriseEnChargeChanging(value);
                ReportPropertyChanging("PriseEnCharge");
                _PriseEnCharge = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("PriseEnCharge");
                OnPriseEnChargeChanged();
            }
        }
        private Nullable<global::System.Boolean> _PriseEnCharge;
        partial void OnPriseEnChargeChanging(Nullable<global::System.Boolean> value);
        partial void OnPriseEnChargeChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Boolean> AccordPrealable
        {
            get
            {
                return _AccordPrealable;
            }
            set
            {
                OnAccordPrealableChanging(value);
                ReportPropertyChanging("AccordPrealable");
                _AccordPrealable = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("AccordPrealable");
                OnAccordPrealableChanged();
            }
        }
        private Nullable<global::System.Boolean> _AccordPrealable;
        partial void OnAccordPrealableChanging(Nullable<global::System.Boolean> value);
        partial void OnAccordPrealableChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Decimal> TarifDeReference
        {
            get
            {
                return _TarifDeReference;
            }
            set
            {
                OnTarifDeReferenceChanging(value);
                ReportPropertyChanging("TarifDeReference");
                _TarifDeReference = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("TarifDeReference");
                OnTarifDeReferenceChanged();
            }
        }
        private Nullable<global::System.Decimal> _TarifDeReference;
        partial void OnTarifDeReferenceChanging(Nullable<global::System.Decimal> value);
        partial void OnTarifDeReferenceChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.Int32 DureeTraitement
        {
            get
            {
                return _DureeTraitement;
            }
            set
            {
                OnDureeTraitementChanging(value);
                ReportPropertyChanging("DureeTraitement");
                _DureeTraitement = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("DureeTraitement");
                OnDureeTraitementChanged();
            }
        }
        private global::System.Int32 _DureeTraitement;
        partial void OnDureeTraitementChanging(global::System.Int32 value);
        partial void OnDureeTraitementChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Decimal> MontantMutuelle
        {
            get
            {
                return _MontantMutuelle;
            }
            set
            {
                OnMontantMutuelleChanging(value);
                ReportPropertyChanging("MontantMutuelle");
                _MontantMutuelle = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("MontantMutuelle");
                OnMontantMutuelleChanged();
            }
        }
        private Nullable<global::System.Decimal> _MontantMutuelle;
        partial void OnMontantMutuelleChanging(Nullable<global::System.Decimal> value);
        partial void OnMontantMutuelleChanged();
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=false, IsNullable=true)]
        [DataMemberAttribute()]
        public Nullable<global::System.Decimal> MontantCNAM
        {
            get
            {
                return _MontantCNAM;
            }
            set
            {
                OnMontantCNAMChanging(value);
                ReportPropertyChanging("MontantCNAM");
                _MontantCNAM = StructuralObject.SetValidValue(value);
                ReportPropertyChanged("MontantCNAM");
                OnMontantCNAMChanged();
            }
        }
        private Nullable<global::System.Decimal> _MontantCNAM;
        partial void OnMontantCNAMChanging(Nullable<global::System.Decimal> value);
        partial void OnMontantCNAMChanged();

        #endregion

    }
    
    /// <summary>
    /// Aucune documentation sur les métadonnées n'est disponible.
    /// </summary>
    [EdmEntityTypeAttribute(NamespaceName="BusinessManagementModel", Name="VENTE_NUMERO")]
    [Serializable()]
    [DataContractAttribute(IsReference=true)]
    public partial class VENTE_NUMERO : EntityObject
    {
        #region Méthode de fabrique
    
        /// <summary>
        /// Créez un nouvel objet VENTE_NUMERO.
        /// </summary>
        /// <param name="numeroVente">Valeur initiale de la propriété NumeroVente.</param>
        public static VENTE_NUMERO CreateVENTE_NUMERO(global::System.String numeroVente)
        {
            VENTE_NUMERO vENTE_NUMERO = new VENTE_NUMERO();
            vENTE_NUMERO.NumeroVente = numeroVente;
            return vENTE_NUMERO;
        }

        #endregion

        #region Propriétés simples
    
        /// <summary>
        /// Aucune documentation sur les métadonnées n'est disponible.
        /// </summary>
        [EdmScalarPropertyAttribute(EntityKeyProperty=true, IsNullable=false)]
        [DataMemberAttribute()]
        public global::System.String NumeroVente
        {
            get
            {
                return _NumeroVente;
            }
            set
            {
                if (_NumeroVente != value)
                {
                    OnNumeroVenteChanging(value);
                    ReportPropertyChanging("NumeroVente");
                    _NumeroVente = StructuralObject.SetValidValue(value, false);
                    ReportPropertyChanged("NumeroVente");
                    OnNumeroVenteChanged();
                }
            }
        }
        private global::System.String _NumeroVente;
        partial void OnNumeroVenteChanging(global::System.String value);
        partial void OnNumeroVenteChanged();

        #endregion

    }

    #endregion

}
