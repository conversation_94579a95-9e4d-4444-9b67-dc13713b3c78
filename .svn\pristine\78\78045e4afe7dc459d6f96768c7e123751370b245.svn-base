﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Form1
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Form1))
        Dim Style1 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style2 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style3 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style4 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style5 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style6 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style7 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim Style8 As C1.Win.C1TrueDBGrid.Style = New C1.Win.C1TrueDBGrid.Style()
        Dim PrintStyle1 As C1.C1Schedule.Printing.PrintStyle = New C1.C1Schedule.Printing.PrintStyle()
        Dim PrintStyle2 As C1.C1Schedule.Printing.PrintStyle = New C1.C1Schedule.Printing.PrintStyle()
        Dim PrintStyle3 As C1.C1Schedule.Printing.PrintStyle = New C1.C1Schedule.Printing.PrintStyle()
        Dim PrintStyle4 As C1.C1Schedule.Printing.PrintStyle = New C1.C1Schedule.Printing.PrintStyle()
        Dim PrintStyle5 As C1.C1Schedule.Printing.PrintStyle = New C1.C1Schedule.Printing.PrintStyle()
        Me.C1CommandDock1 = New C1.Win.C1Command.C1CommandDock()
        Me.C1DockingTab1 = New C1.Win.C1Command.C1DockingTab()
        Me.C1DockingTabPage1 = New C1.Win.C1Command.C1DockingTabPage()
        Me.C1List1 = New C1.Win.C1List.C1List()
        Me.C1Label1 = New C1.Win.C1Input.C1Label()
        Me.C1Button1 = New C1.Win.C1Input.C1Button()
        Me.C1CheckBox1 = New C1.Win.C1Input.C1CheckBox()
        Me.C1NavBar1 = New C1.Win.C1Command.C1NavBar()
        Me.C1NavBarPanel1 = New C1.Win.C1Command.C1NavBarPanel()
        Me.C1StatusBar1 = New C1.Win.C1Ribbon.C1StatusBar()
        Me.C1PictureBox1 = New C1.Win.C1Input.C1PictureBox()
        Me.C1TextBox1 = New C1.Win.C1Input.C1TextBox()
        Me.C1MainMenu1 = New C1.Win.C1Command.C1MainMenu()
        Me.C1CommandHolder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.C1CommandControl1 = New C1.Win.C1Command.C1CommandControl()
        Me.C1InputPanel1 = New C1.Win.C1InputPanel.C1InputPanel()
        Me.C1CommandControl2 = New C1.Win.C1Command.C1CommandControl()
        Me.C1Combo1 = New C1.Win.C1List.C1Combo()
        Me.C1CommandControl3 = New C1.Win.C1Command.C1CommandControl()
        Me.C1Ribbon1 = New C1.Win.C1Ribbon.C1Ribbon()
        Me.RibbonApplicationMenu1 = New C1.Win.C1Ribbon.RibbonApplicationMenu()
        Me.RibbonConfigToolBar1 = New C1.Win.C1Ribbon.RibbonConfigToolBar()
        Me.RibbonQat1 = New C1.Win.C1Ribbon.RibbonQat()
        Me.RibbonTab1 = New C1.Win.C1Ribbon.RibbonTab()
        Me.RibbonGroup1 = New C1.Win.C1Ribbon.RibbonGroup()
        Me.C1CommandControl4 = New C1.Win.C1Command.C1CommandControl()
        Me.C1TrueDBGrid1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.C1CommandControl5 = New C1.Win.C1Command.C1CommandControl()
        Me.C1TrueDBDropdown1 = New C1.Win.C1TrueDBGrid.C1TrueDBDropdown()
        Me.C1CommandLink1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink2 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink3 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink4 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink5 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink6 = New C1.Win.C1Command.C1CommandLink()
        Me.C1Report1 = New C1.C1Report.C1Report()
        Me.C1Chart1 = New C1.Win.C1Chart.C1Chart()
        Me.C1DateEdit1 = New C1.Win.C1Input.C1DateEdit()
        Me.C1SplitButton1 = New C1.Win.C1Input.C1SplitButton()
        Me.C1NumericEdit1 = New C1.Win.C1Input.C1NumericEdit()
        Me.C1Schedule1 = New C1.Win.C1Schedule.C1Schedule()
        Me.C1Calendar1 = New C1.Win.C1Schedule.C1Calendar()
        Me.C1XLBook1 = New C1.C1Excel.C1XLBook()
        Me.C1BarCode1 = New C1.Win.C1BarCode.C1BarCode()
        Me.C1Sizer1 = New C1.Win.C1Sizer.C1Sizer()
        Me.C1XLBook2 = New C1.C1Excel.C1XLBook()
        Me.C1CommandDock2 = New C1.Win.C1Command.C1CommandDock()
        Me.C1Combo2 = New C1.Win.C1List.C1Combo()
        Me.C1List2 = New C1.Win.C1List.C1List()
        Me.C1List3 = New C1.Win.C1List.C1List()
        Me.C1XLBook3 = New C1.C1Excel.C1XLBook()
        Me.C1Chart2 = New C1.Win.C1Chart.C1Chart()
        CType(Me.C1CommandDock1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1CommandDock1.SuspendLayout()
        CType(Me.C1DockingTab1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTab1.SuspendLayout()
        Me.C1DockingTabPage1.SuspendLayout()
        CType(Me.C1List1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Label1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1NavBar1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1NavBar1.SuspendLayout()
        Me.C1NavBarPanel1.SuspendLayout()
        CType(Me.C1StatusBar1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1MainMenu1.SuspendLayout()
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1InputPanel1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Combo1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Ribbon1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TrueDBGrid1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TrueDBDropdown1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Report1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Chart1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1DateEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1NumericEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Schedule1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Schedule1.DataStorage.AppointmentStorage, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Schedule1.DataStorage.CategoryStorage, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Schedule1.DataStorage.ContactStorage, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Schedule1.DataStorage.LabelStorage, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Schedule1.DataStorage.OwnerStorage, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Schedule1.DataStorage.ResourceStorage, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Schedule1.DataStorage.StatusStorage, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Calendar1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Sizer1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1CommandDock2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Combo2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1List2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1List3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Chart2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'C1CommandDock1
        '
        Me.C1CommandDock1.Controls.Add(Me.C1DockingTab1)
        Me.C1CommandDock1.Dock = System.Windows.Forms.DockStyle.Left
        Me.C1CommandDock1.Id = 1
        Me.C1CommandDock1.Location = New System.Drawing.Point(0, 0)
        Me.C1CommandDock1.Name = "C1CommandDock1"
        Me.C1CommandDock1.Size = New System.Drawing.Size(300, 262)
        '
        'C1DockingTab1
        '
        Me.C1DockingTab1.Alignment = System.Windows.Forms.TabAlignment.Bottom
        Me.C1DockingTab1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1DockingTab1.CanAutoHide = True
        Me.C1DockingTab1.CanCloseTabs = True
        Me.C1DockingTab1.CanMoveTabs = True
        Me.C1DockingTab1.Controls.Add(Me.C1DockingTabPage1)
        Me.C1DockingTab1.Location = New System.Drawing.Point(0, 0)
        Me.C1DockingTab1.Name = "C1DockingTab1"
        Me.C1DockingTab1.ShowCaption = True
        Me.C1DockingTab1.Size = New System.Drawing.Size(300, 262)
        Me.C1DockingTab1.TabIndex = 0
        Me.C1DockingTab1.TabSizeMode = C1.Win.C1Command.TabSizeModeEnum.Fit
        Me.C1DockingTab1.TabsSpacing = 0
        Me.C1DockingTab1.VisualStyleBase = C1.Win.C1Command.VisualStyle.OfficeXP
        '
        'C1DockingTabPage1
        '
        Me.C1DockingTabPage1.CaptionVisible = True
        Me.C1DockingTabPage1.Controls.Add(Me.C1List1)
        Me.C1DockingTabPage1.Controls.Add(Me.C1Label1)
        Me.C1DockingTabPage1.Controls.Add(Me.C1Button1)
        Me.C1DockingTabPage1.Controls.Add(Me.C1CheckBox1)
        Me.C1DockingTabPage1.Controls.Add(Me.C1NavBar1)
        Me.C1DockingTabPage1.Controls.Add(Me.C1MainMenu1)
        Me.C1DockingTabPage1.Location = New System.Drawing.Point(0, 0)
        Me.C1DockingTabPage1.Name = "C1DockingTabPage1"
        Me.C1DockingTabPage1.Size = New System.Drawing.Size(297, 238)
        Me.C1DockingTabPage1.TabIndex = 0
        Me.C1DockingTabPage1.Text = "Page1"
        '
        'C1List1
        '
        Me.C1List1.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1List1.DeadAreaBackColor = System.Drawing.SystemColors.ControlDark
        Me.C1List1.Images.Add(CType(resources.GetObject("C1List1.Images"), System.Drawing.Image))
        Me.C1List1.Location = New System.Drawing.Point(31, 208)
        Me.C1List1.MatchEntryTimeout = CType(2000, Long)
        Me.C1List1.Name = "C1List1"
        Me.C1List1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1List1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1List1.PreviewInfo.ZoomFactor = 75.0R
        Me.C1List1.PrintInfo.PageSettings = CType(resources.GetObject("C1List1.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.C1List1.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1List1.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1List1.ShowHeaderCheckBox = False
        Me.C1List1.Size = New System.Drawing.Size(75, 23)
        Me.C1List1.TabIndex = 5
        Me.C1List1.Text = "C1List1"
        Me.C1List1.PropBag = resources.GetString("C1List1.PropBag")
        '
        'C1Label1
        '
        Me.C1Label1.AutoSize = True
        Me.C1Label1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Label1.Location = New System.Drawing.Point(52, 239)
        Me.C1Label1.Name = "C1Label1"
        Me.C1Label1.Size = New System.Drawing.Size(52, 13)
        Me.C1Label1.TabIndex = 4
        Me.C1Label1.Tag = Nothing
        '
        'C1Button1
        '
        Me.C1Button1.Location = New System.Drawing.Point(52, 107)
        Me.C1Button1.Name = "C1Button1"
        Me.C1Button1.Size = New System.Drawing.Size(75, 23)
        Me.C1Button1.TabIndex = 3
        Me.C1Button1.Text = "C1Button1"
        Me.C1Button1.UseVisualStyleBackColor = True
        '
        'C1CheckBox1
        '
        Me.C1CheckBox1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1CheckBox1.Location = New System.Drawing.Point(78, 149)
        Me.C1CheckBox1.Name = "C1CheckBox1"
        Me.C1CheckBox1.Size = New System.Drawing.Size(104, 24)
        Me.C1CheckBox1.TabIndex = 2
        Me.C1CheckBox1.Text = "C1CheckBox1"
        Me.C1CheckBox1.Value = Nothing
        '
        'C1NavBar1
        '
        Me.C1NavBar1.Collapsed = False
        Me.C1NavBar1.Controls.Add(Me.C1NavBarPanel1)
        Me.C1NavBar1.Location = New System.Drawing.Point(110, 98)
        Me.C1NavBar1.Name = "C1NavBar1"
        Me.C1NavBar1.Size = New System.Drawing.Size(200, 300)
        Me.C1NavBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.OfficeXP
        '
        'C1NavBarPanel1
        '
        Me.C1NavBarPanel1.Button.Image = CType(resources.GetObject("resource.Image"), System.Drawing.Image)
        Me.C1NavBarPanel1.Button.ImageTransparentColor = System.Drawing.Color.FromArgb(CType(CType(238, Byte), Integer), CType(CType(238, Byte), Integer), CType(CType(238, Byte), Integer))
        Me.C1NavBarPanel1.Button.SmallImage = CType(resources.GetObject("resource.SmallImage"), System.Drawing.Image)
        Me.C1NavBarPanel1.Button.Text = "Notes"
        Me.C1NavBarPanel1.Controls.Add(Me.C1StatusBar1)
        Me.C1NavBarPanel1.Controls.Add(Me.C1PictureBox1)
        Me.C1NavBarPanel1.Controls.Add(Me.C1TextBox1)
        Me.C1NavBarPanel1.ID = 1
        Me.C1NavBarPanel1.Name = "C1NavBarPanel1"
        Me.C1NavBarPanel1.Size = New System.Drawing.Size(198, 202)
        '
        'C1StatusBar1
        '
        Me.C1StatusBar1.Location = New System.Drawing.Point(0, 179)
        Me.C1StatusBar1.Name = "C1StatusBar1"
        Me.C1StatusBar1.Size = New System.Drawing.Size(198, 23)
        '
        'C1PictureBox1
        '
        Me.C1PictureBox1.Location = New System.Drawing.Point(78, 26)
        Me.C1PictureBox1.Name = "C1PictureBox1"
        Me.C1PictureBox1.Size = New System.Drawing.Size(100, 50)
        Me.C1PictureBox1.TabIndex = 1
        Me.C1PictureBox1.TabStop = False
        '
        'C1TextBox1
        '
        Me.C1TextBox1.Location = New System.Drawing.Point(148, 37)
        Me.C1TextBox1.Name = "C1TextBox1"
        Me.C1TextBox1.Size = New System.Drawing.Size(100, 20)
        Me.C1TextBox1.TabIndex = 0
        Me.C1TextBox1.Tag = Nothing
        '
        'C1MainMenu1
        '
        Me.C1MainMenu1.CommandHolder = Me.C1CommandHolder1
        Me.C1MainMenu1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.C1CommandLink1, Me.C1CommandLink2, Me.C1CommandLink3, Me.C1CommandLink4, Me.C1CommandLink5, Me.C1CommandLink6})
        Me.C1MainMenu1.Controls.Add(Me.C1TrueDBDropdown1)
        Me.C1MainMenu1.Controls.Add(Me.C1TrueDBGrid1)
        Me.C1MainMenu1.Controls.Add(Me.C1Ribbon1)
        Me.C1MainMenu1.Controls.Add(Me.C1Combo1)
        Me.C1MainMenu1.Controls.Add(Me.C1InputPanel1)
        Me.C1MainMenu1.Dock = System.Windows.Forms.DockStyle.Top
        Me.C1MainMenu1.Location = New System.Drawing.Point(0, 20)
        Me.C1MainMenu1.Name = "C1MainMenu1"
        Me.C1MainMenu1.Size = New System.Drawing.Size(297, 809)
        Me.C1MainMenu1.VisualStyleBase = C1.Win.C1Command.VisualStyle.OfficeXP
        '
        'C1CommandHolder1
        '
        Me.C1CommandHolder1.Commands.Add(Me.C1CommandControl1)
        Me.C1CommandHolder1.Commands.Add(Me.C1CommandControl2)
        Me.C1CommandHolder1.Commands.Add(Me.C1CommandControl3)
        Me.C1CommandHolder1.Commands.Add(Me.C1CommandControl4)
        Me.C1CommandHolder1.Commands.Add(Me.C1CommandControl5)
        Me.C1CommandHolder1.Owner = Me
        '
        'C1CommandControl1
        '
        Me.C1CommandControl1.Control = Me.C1InputPanel1
        Me.C1CommandControl1.Name = "C1CommandControl1"
        Me.C1CommandControl1.ShortcutText = ""
        '
        'C1InputPanel1
        '
        Me.C1InputPanel1.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.C1InputPanel1.Location = New System.Drawing.Point(66, 4)
        Me.C1InputPanel1.Name = "C1InputPanel1"
        Me.C1InputPanel1.Size = New System.Drawing.Size(240, 150)
        Me.C1InputPanel1.TabIndex = 0
        '
        'C1CommandControl2
        '
        Me.C1CommandControl2.Control = Me.C1Combo1
        Me.C1CommandControl2.Name = "C1CommandControl2"
        Me.C1CommandControl2.ShortcutText = ""
        '
        'C1Combo1
        '
        Me.C1Combo1.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1Combo1.Caption = ""
        Me.C1Combo1.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo1.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo1.EditorBackColor = System.Drawing.SystemColors.Window
        Me.C1Combo1.EditorFont = New System.Drawing.Font("Segoe UI", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.World)
        Me.C1Combo1.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.C1Combo1.Images.Add(CType(resources.GetObject("C1Combo1.Images"), System.Drawing.Image))
        Me.C1Combo1.Location = New System.Drawing.Point(13, 132)
        Me.C1Combo1.MatchEntryTimeout = CType(2000, Long)
        Me.C1Combo1.MaxDropDownItems = CType(5, Short)
        Me.C1Combo1.MaxLength = 32767
        Me.C1Combo1.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo1.Name = "C1Combo1"
        Me.C1Combo1.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1Combo1.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo1.Size = New System.Drawing.Size(121, 24)
        Me.C1Combo1.TabIndex = 1
        Me.C1Combo1.Text = "C1Combo1"
        Me.C1Combo1.PropBag = resources.GetString("C1Combo1.PropBag")
        '
        'C1CommandControl3
        '
        Me.C1CommandControl3.Control = Me.C1Ribbon1
        Me.C1CommandControl3.Name = "C1CommandControl3"
        Me.C1CommandControl3.ShortcutText = ""
        '
        'C1Ribbon1
        '
        Me.C1Ribbon1.ApplicationMenuHolder = Me.RibbonApplicationMenu1
        Me.C1Ribbon1.ConfigToolBarHolder = Me.RibbonConfigToolBar1
        Me.C1Ribbon1.Location = New System.Drawing.Point(0, 0)
        Me.C1Ribbon1.Name = "C1Ribbon1"
        Me.C1Ribbon1.QatHolder = Me.RibbonQat1
        Me.C1Ribbon1.Size = New System.Drawing.Size(297, 156)
        Me.C1Ribbon1.Tabs.Add(Me.RibbonTab1)
        '
        'RibbonApplicationMenu1
        '
        Me.RibbonApplicationMenu1.Name = "RibbonApplicationMenu1"
        '
        'RibbonConfigToolBar1
        '
        Me.RibbonConfigToolBar1.Name = "RibbonConfigToolBar1"
        '
        'RibbonQat1
        '
        Me.RibbonQat1.Name = "RibbonQat1"
        '
        'RibbonTab1
        '
        Me.RibbonTab1.Groups.Add(Me.RibbonGroup1)
        Me.RibbonTab1.Name = "RibbonTab1"
        Me.RibbonTab1.Text = "Tab"
        '
        'RibbonGroup1
        '
        Me.RibbonGroup1.Name = "RibbonGroup1"
        Me.RibbonGroup1.Text = "Group"
        '
        'C1CommandControl4
        '
        Me.C1CommandControl4.Control = Me.C1TrueDBGrid1
        Me.C1CommandControl4.Name = "C1CommandControl4"
        Me.C1CommandControl4.ShortcutText = ""
        '
        'C1TrueDBGrid1
        '
        Me.C1TrueDBGrid1.CaptionHeight = 17
        Me.C1TrueDBGrid1.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid1.Images.Add(CType(resources.GetObject("C1TrueDBGrid1.Images"), System.Drawing.Image))
        Me.C1TrueDBGrid1.Location = New System.Drawing.Point(189, 35)
        Me.C1TrueDBGrid1.Name = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.ZoomFactor = 75.0R
        Me.C1TrueDBGrid1.PrintInfo.PageSettings = CType(resources.GetObject("C1TrueDBGrid1.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.C1TrueDBGrid1.RowHeight = 15
        Me.C1TrueDBGrid1.Size = New System.Drawing.Size(240, 150)
        Me.C1TrueDBGrid1.TabIndex = 3
        Me.C1TrueDBGrid1.Text = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PropBag = resources.GetString("C1TrueDBGrid1.PropBag")
        '
        'C1CommandControl5
        '
        Me.C1CommandControl5.Control = Me.C1TrueDBDropdown1
        Me.C1CommandControl5.Name = "C1CommandControl5"
        Me.C1CommandControl5.ShortcutText = ""
        '
        'C1TrueDBDropdown1
        '
        Me.C1TrueDBDropdown1.AllowColMove = True
        Me.C1TrueDBDropdown1.AllowColSelect = True
        Me.C1TrueDBDropdown1.AllowRowSizing = C1.Win.C1TrueDBGrid.RowSizingEnum.AllRows
        Me.C1TrueDBDropdown1.AlternatingRows = False
        Me.C1TrueDBDropdown1.CaptionHeight = 17
        Me.C1TrueDBDropdown1.CaptionStyle = Style1
        Me.C1TrueDBDropdown1.ColumnCaptionHeight = 17
        Me.C1TrueDBDropdown1.ColumnFooterHeight = 17
        Me.C1TrueDBDropdown1.EvenRowStyle = Style2
        Me.C1TrueDBDropdown1.FetchRowStyles = False
        Me.C1TrueDBDropdown1.FooterStyle = Style3
        Me.C1TrueDBDropdown1.HeadingStyle = Style4
        Me.C1TrueDBDropdown1.HighLightRowStyle = Style5
        Me.C1TrueDBDropdown1.Images.Add(CType(resources.GetObject("C1TrueDBDropdown1.Images"), System.Drawing.Image))
        Me.C1TrueDBDropdown1.Location = New System.Drawing.Point(33, 45)
        Me.C1TrueDBDropdown1.Name = "C1TrueDBDropdown1"
        Me.C1TrueDBDropdown1.OddRowStyle = Style6
        Me.C1TrueDBDropdown1.RecordSelectorStyle = Style7
        Me.C1TrueDBDropdown1.RowDivider.Color = System.Drawing.Color.DarkGray
        Me.C1TrueDBDropdown1.RowDivider.Style = C1.Win.C1TrueDBGrid.LineStyleEnum.[Single]
        Me.C1TrueDBDropdown1.RowHeight = 15
        Me.C1TrueDBDropdown1.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1TrueDBDropdown1.ScrollTips = False
        Me.C1TrueDBDropdown1.Size = New System.Drawing.Size(100, 150)
        Me.C1TrueDBDropdown1.Style = Style8
        Me.C1TrueDBDropdown1.TabIndex = 4
        Me.C1TrueDBDropdown1.Text = "C1TrueDBDropdown1"
        Me.C1TrueDBDropdown1.Visible = False
        Me.C1TrueDBDropdown1.PropBag = resources.GetString("C1TrueDBDropdown1.PropBag")
        '
        'C1CommandLink1
        '
        Me.C1CommandLink1.Text = "New Command"
        '
        'C1CommandLink2
        '
        Me.C1CommandLink2.Command = Me.C1CommandControl1
        Me.C1CommandLink2.SortOrder = 1
        '
        'C1CommandLink3
        '
        Me.C1CommandLink3.Command = Me.C1CommandControl2
        Me.C1CommandLink3.SortOrder = 2
        '
        'C1CommandLink4
        '
        Me.C1CommandLink4.Command = Me.C1CommandControl3
        Me.C1CommandLink4.SortOrder = 3
        '
        'C1CommandLink5
        '
        Me.C1CommandLink5.Command = Me.C1CommandControl4
        Me.C1CommandLink5.SortOrder = 4
        '
        'C1CommandLink6
        '
        Me.C1CommandLink6.Command = Me.C1CommandControl5
        Me.C1CommandLink6.SortOrder = 5
        '
        'C1Report1
        '
        Me.C1Report1.ReportDefinition = resources.GetString("C1Report1.ReportDefinition")
        Me.C1Report1.ReportName = "C1Report1"
        '
        'C1Chart1
        '
        Me.C1Chart1.Location = New System.Drawing.Point(304, -58)
        Me.C1Chart1.Name = "C1Chart1"
        Me.C1Chart1.PropBag = resources.GetString("C1Chart1.PropBag")
        Me.C1Chart1.Size = New System.Drawing.Size(200, 150)
        Me.C1Chart1.TabIndex = 1
        '
        'C1DateEdit1
        '
        '
        '
        '
        Me.C1DateEdit1.Calendar.DayNameLength = 1
        Me.C1DateEdit1.Location = New System.Drawing.Point(358, 116)
        Me.C1DateEdit1.Name = "C1DateEdit1"
        Me.C1DateEdit1.Size = New System.Drawing.Size(200, 20)
        Me.C1DateEdit1.TabIndex = 2
        Me.C1DateEdit1.Tag = Nothing
        '
        'C1SplitButton1
        '
        Me.C1SplitButton1.Location = New System.Drawing.Point(317, 187)
        Me.C1SplitButton1.Name = "C1SplitButton1"
        Me.C1SplitButton1.Size = New System.Drawing.Size(115, 23)
        Me.C1SplitButton1.TabIndex = 3
        Me.C1SplitButton1.Text = "C1SplitButton1"
        Me.C1SplitButton1.UseVisualStyleBackColor = True
        '
        'C1NumericEdit1
        '
        Me.C1NumericEdit1.Location = New System.Drawing.Point(352, 107)
        Me.C1NumericEdit1.Name = "C1NumericEdit1"
        Me.C1NumericEdit1.Size = New System.Drawing.Size(200, 20)
        Me.C1NumericEdit1.TabIndex = 4
        Me.C1NumericEdit1.Tag = Nothing
        '
        'C1Schedule1
        '
        '
        '
        '
        Me.C1Schedule1.CalendarInfo.CultureInfo = New System.Globalization.CultureInfo("fr-FR")
        Me.C1Schedule1.CalendarInfo.DateFormatString = "dd/MM/yyyy"
        Me.C1Schedule1.CalendarInfo.EndDayTime = System.TimeSpan.Parse("19:00:00")
        Me.C1Schedule1.CalendarInfo.StartDayTime = System.TimeSpan.Parse("07:00:00")
        Me.C1Schedule1.CalendarInfo.TimeScale = System.TimeSpan.Parse("00:30:00")
        Me.C1Schedule1.CalendarInfo.WeekStart = System.DayOfWeek.Monday
        Me.C1Schedule1.CalendarInfo.WorkDays.AddRange(New System.DayOfWeek() {System.DayOfWeek.Monday, System.DayOfWeek.Tuesday, System.DayOfWeek.Wednesday, System.DayOfWeek.Thursday, System.DayOfWeek.Friday})
        '
        '
        '
        Me.C1Schedule1.GroupPageSize = 2
        Me.C1Schedule1.Location = New System.Drawing.Point(304, 149)
        Me.C1Schedule1.Name = "C1Schedule1"
        PrintStyle1.Description = "Daily Style"
        PrintStyle1.PreviewImage = CType(resources.GetObject("PrintStyle1.PreviewImage"), System.Drawing.Image)
        PrintStyle1.StyleName = "Daily"
        PrintStyle1.StyleSource = "day.c1d"
        PrintStyle2.Description = "Weekly Style"
        PrintStyle2.PreviewImage = CType(resources.GetObject("PrintStyle2.PreviewImage"), System.Drawing.Image)
        PrintStyle2.StyleName = "Week"
        PrintStyle2.StyleSource = "week.c1d"
        PrintStyle3.Description = "Monthly Style"
        PrintStyle3.PreviewImage = CType(resources.GetObject("PrintStyle3.PreviewImage"), System.Drawing.Image)
        PrintStyle3.StyleName = "Month"
        PrintStyle3.StyleSource = "month.c1d"
        PrintStyle4.Description = "Details Style"
        PrintStyle4.PreviewImage = CType(resources.GetObject("PrintStyle4.PreviewImage"), System.Drawing.Image)
        PrintStyle4.StyleName = "Details"
        PrintStyle4.StyleSource = "details.c1d"
        PrintStyle5.Context = C1.C1Schedule.Printing.PrintContextType.Appointment
        PrintStyle5.Description = "Memo Style"
        PrintStyle5.PreviewImage = CType(resources.GetObject("PrintStyle5.PreviewImage"), System.Drawing.Image)
        PrintStyle5.StyleName = "Memo"
        PrintStyle5.StyleSource = "memo.c1d"
        Me.C1Schedule1.PrintInfo.PrintStyles.AddRange(New C1.C1Schedule.Printing.PrintStyle() {PrintStyle1, PrintStyle2, PrintStyle3, PrintStyle4, PrintStyle5})
        Me.C1Schedule1.Size = New System.Drawing.Size(640, 480)
        Me.C1Schedule1.TabIndex = 5
        '
        'C1Calendar1
        '
        Me.C1Calendar1.BoldedDates = New Date(-1) {}
        '
        '
        '
        Me.C1Calendar1.CalendarInfo.CultureInfo = New System.Globalization.CultureInfo("fr-FR")
        Me.C1Calendar1.CalendarInfo.DateFormatString = "dd/MM/yyyy"
        Me.C1Calendar1.CalendarInfo.EndDayTime = System.TimeSpan.Parse("19:00:00")
        Me.C1Calendar1.CalendarInfo.StartDayTime = System.TimeSpan.Parse("07:00:00")
        Me.C1Calendar1.CalendarInfo.TimeScale = System.TimeSpan.Parse("00:30:00")
        Me.C1Calendar1.CalendarInfo.WeekStart = System.DayOfWeek.Monday
        Me.C1Calendar1.CalendarInfo.WorkDays.AddRange(New System.DayOfWeek() {System.DayOfWeek.Monday, System.DayOfWeek.Tuesday, System.DayOfWeek.Wednesday, System.DayOfWeek.Thursday, System.DayOfWeek.Friday})
        Me.C1Calendar1.Location = New System.Drawing.Point(351, 24)
        Me.C1Calendar1.Name = "C1Calendar1"
        Me.C1Calendar1.Size = New System.Drawing.Size(200, 180)
        Me.C1Calendar1.TabIndex = 6
        '
        'C1BarCode1
        '
        Me.C1BarCode1.Location = New System.Drawing.Point(475, 20)
        Me.C1BarCode1.Name = "C1BarCode1"
        Me.C1BarCode1.Size = New System.Drawing.Size(75, 23)
        Me.C1BarCode1.TabIndex = 7
        Me.C1BarCode1.Text = "C1BarCode1"
        '
        'C1Sizer1
        '
        Me.C1Sizer1.GridDefinition = "65.2173913043478:False:False;" & Global.Microsoft.VisualBasic.ChrW(9) & "89.3333333333333:False:False;"
        Me.C1Sizer1.Location = New System.Drawing.Point(590, 55)
        Me.C1Sizer1.Name = "C1Sizer1"
        Me.C1Sizer1.Size = New System.Drawing.Size(75, 23)
        Me.C1Sizer1.TabIndex = 8
        Me.C1Sizer1.Text = "C1Sizer1"
        '
        'C1CommandDock2
        '
        Me.C1CommandDock2.Dock = System.Windows.Forms.DockStyle.Right
        Me.C1CommandDock2.Id = 2
        Me.C1CommandDock2.Location = New System.Drawing.Point(603, 0)
        Me.C1CommandDock2.Name = "C1CommandDock2"
        Me.C1CommandDock2.Size = New System.Drawing.Size(32, 262)
        '
        'C1Combo2
        '
        Me.C1Combo2.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1Combo2.Caption = ""
        Me.C1Combo2.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo2.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo2.EditorBackColor = System.Drawing.SystemColors.Window
        Me.C1Combo2.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.C1Combo2.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.C1Combo2.Images.Add(CType(resources.GetObject("C1Combo2.Images"), System.Drawing.Image))
        Me.C1Combo2.Location = New System.Drawing.Point(463, 65)
        Me.C1Combo2.MatchEntryTimeout = CType(2000, Long)
        Me.C1Combo2.MaxDropDownItems = CType(5, Short)
        Me.C1Combo2.MaxLength = 32767
        Me.C1Combo2.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo2.Name = "C1Combo2"
        Me.C1Combo2.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1Combo2.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo2.Size = New System.Drawing.Size(121, 21)
        Me.C1Combo2.TabIndex = 11
        Me.C1Combo2.Text = "C1Combo2"
        Me.C1Combo2.PropBag = resources.GetString("C1Combo2.PropBag")
        '
        'C1List2
        '
        Me.C1List2.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1List2.DeadAreaBackColor = System.Drawing.SystemColors.ControlDark
        Me.C1List2.Images.Add(CType(resources.GetObject("C1List2.Images"), System.Drawing.Image))
        Me.C1List2.Location = New System.Drawing.Point(271, 98)
        Me.C1List2.MatchEntryTimeout = CType(2000, Long)
        Me.C1List2.Name = "C1List2"
        Me.C1List2.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1List2.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1List2.PreviewInfo.ZoomFactor = 75.0R
        Me.C1List2.PrintInfo.PageSettings = CType(resources.GetObject("C1List2.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.C1List2.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1List2.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1List2.ShowHeaderCheckBox = False
        Me.C1List2.Size = New System.Drawing.Size(75, 23)
        Me.C1List2.TabIndex = 12
        Me.C1List2.Text = "C1List2"
        Me.C1List2.PropBag = resources.GetString("C1List2.PropBag")
        '
        'C1List3
        '
        Me.C1List3.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1List3.DeadAreaBackColor = System.Drawing.SystemColors.ControlDark
        Me.C1List3.Images.Add(CType(resources.GetObject("C1List3.Images"), System.Drawing.Image))
        Me.C1List3.Location = New System.Drawing.Point(317, 128)
        Me.C1List3.MatchEntryTimeout = CType(2000, Long)
        Me.C1List3.Name = "C1List3"
        Me.C1List3.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1List3.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1List3.PreviewInfo.ZoomFactor = 75.0R
        Me.C1List3.PrintInfo.PageSettings = CType(resources.GetObject("C1List3.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.C1List3.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1List3.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1List3.ShowHeaderCheckBox = False
        Me.C1List3.Size = New System.Drawing.Size(75, 23)
        Me.C1List3.TabIndex = 15
        Me.C1List3.Text = "C1List3"
        Me.C1List3.PropBag = resources.GetString("C1List3.PropBag")
        '
        'C1Chart2
        '
        Me.C1Chart2.Location = New System.Drawing.Point(-23, -45)
        Me.C1Chart2.Name = "C1Chart2"
        Me.C1Chart2.PropBag = resources.GetString("C1Chart2.PropBag")
        Me.C1Chart2.Size = New System.Drawing.Size(200, 150)
        Me.C1Chart2.TabIndex = 18
        '
        'Form1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(635, 262)
        Me.Controls.Add(Me.C1Chart2)
        Me.Controls.Add(Me.C1List3)
        Me.Controls.Add(Me.C1List2)
        Me.Controls.Add(Me.C1Combo2)
        Me.Controls.Add(Me.C1CommandDock2)
        Me.Controls.Add(Me.C1Sizer1)
        Me.Controls.Add(Me.C1BarCode1)
        Me.Controls.Add(Me.C1Calendar1)
        Me.Controls.Add(Me.C1Schedule1)
        Me.Controls.Add(Me.C1NumericEdit1)
        Me.Controls.Add(Me.C1SplitButton1)
        Me.Controls.Add(Me.C1DateEdit1)
        Me.Controls.Add(Me.C1Chart1)
        Me.Controls.Add(Me.C1CommandDock1)
        Me.Name = "Form1"
        Me.Text = "Form1"
        CType(Me.C1CommandDock1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1CommandDock1.ResumeLayout(False)
        CType(Me.C1DockingTab1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTab1.ResumeLayout(False)
        Me.C1DockingTabPage1.ResumeLayout(False)
        Me.C1DockingTabPage1.PerformLayout()
        CType(Me.C1List1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Label1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1NavBar1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1NavBar1.ResumeLayout(False)
        Me.C1NavBarPanel1.ResumeLayout(False)
        Me.C1NavBarPanel1.PerformLayout()
        CType(Me.C1StatusBar1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1MainMenu1.ResumeLayout(False)
        Me.C1MainMenu1.PerformLayout()
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1InputPanel1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Combo1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Ribbon1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TrueDBGrid1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TrueDBDropdown1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Report1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Chart1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1DateEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1NumericEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Schedule1.DataStorage.AppointmentStorage, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Schedule1.DataStorage.CategoryStorage, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Schedule1.DataStorage.ContactStorage, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Schedule1.DataStorage.LabelStorage, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Schedule1.DataStorage.OwnerStorage, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Schedule1.DataStorage.ResourceStorage, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Schedule1.DataStorage.StatusStorage, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Schedule1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Calendar1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Sizer1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1CommandDock2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Combo2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1List2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1List3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Chart2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents C1CommandDock1 As C1.Win.C1Command.C1CommandDock
    Friend WithEvents C1DockingTab1 As C1.Win.C1Command.C1DockingTab
    Friend WithEvents C1DockingTabPage1 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents C1List1 As C1.Win.C1List.C1List
    Friend WithEvents C1Label1 As C1.Win.C1Input.C1Label
    Friend WithEvents C1Button1 As C1.Win.C1Input.C1Button
    Friend WithEvents C1CheckBox1 As C1.Win.C1Input.C1CheckBox
    Friend WithEvents C1NavBar1 As C1.Win.C1Command.C1NavBar
    Friend WithEvents C1NavBarPanel1 As C1.Win.C1Command.C1NavBarPanel
    Friend WithEvents C1StatusBar1 As C1.Win.C1Ribbon.C1StatusBar
    Friend WithEvents C1PictureBox1 As C1.Win.C1Input.C1PictureBox
    Friend WithEvents C1TextBox1 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1MainMenu1 As C1.Win.C1Command.C1MainMenu
    Friend WithEvents C1CommandHolder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents C1CommandControl1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents C1InputPanel1 As C1.Win.C1InputPanel.C1InputPanel
    Friend WithEvents C1CommandControl2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents C1Combo1 As C1.Win.C1List.C1Combo
    Friend WithEvents C1CommandControl3 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents C1Ribbon1 As C1.Win.C1Ribbon.C1Ribbon
    Friend WithEvents RibbonApplicationMenu1 As C1.Win.C1Ribbon.RibbonApplicationMenu
    Friend WithEvents RibbonConfigToolBar1 As C1.Win.C1Ribbon.RibbonConfigToolBar
    Friend WithEvents RibbonQat1 As C1.Win.C1Ribbon.RibbonQat
    Friend WithEvents RibbonTab1 As C1.Win.C1Ribbon.RibbonTab
    Friend WithEvents RibbonGroup1 As C1.Win.C1Ribbon.RibbonGroup
    Friend WithEvents C1CommandControl4 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents C1TrueDBGrid1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents C1CommandControl5 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents C1TrueDBDropdown1 As C1.Win.C1TrueDBGrid.C1TrueDBDropdown
    Friend WithEvents C1BarCode1 As C1.Win.C1BarCode.C1BarCode
    Friend WithEvents C1Calendar1 As C1.Win.C1Schedule.C1Calendar
    Friend WithEvents C1Schedule1 As C1.Win.C1Schedule.C1Schedule
    Friend WithEvents C1NumericEdit1 As C1.Win.C1Input.C1NumericEdit
    Friend WithEvents C1SplitButton1 As C1.Win.C1Input.C1SplitButton
    Friend WithEvents C1DateEdit1 As C1.Win.C1Input.C1DateEdit
    Friend WithEvents C1Chart1 As C1.Win.C1Chart.C1Chart
    Friend WithEvents C1CommandLink1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink5 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink6 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1Report1 As C1.C1Report.C1Report
    Friend WithEvents C1XLBook1 As C1.C1Excel.C1XLBook
    Friend WithEvents C1Sizer1 As C1.Win.C1Sizer.C1Sizer
    Friend WithEvents C1List2 As C1.Win.C1List.C1List
    Friend WithEvents C1Combo2 As C1.Win.C1List.C1Combo
    Friend WithEvents C1CommandDock2 As C1.Win.C1Command.C1CommandDock
    Friend WithEvents C1XLBook2 As C1.C1Excel.C1XLBook
    Friend WithEvents C1List3 As C1.Win.C1List.C1List
    Friend WithEvents C1XLBook3 As C1.C1Excel.C1XLBook
    Friend WithEvents C1Chart2 As C1.Win.C1Chart.C1Chart
End Class
