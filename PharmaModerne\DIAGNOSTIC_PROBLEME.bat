@echo off
echo ========================================
echo    DIAGNOSTIC PROBLEME - APPLICATION NE S'OUVRE PAS
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 DIAGNOSTIC COMPLET EN COURS...
echo.

REM Vérifier l'exécutable
echo 1. VERIFICATION DE L'EXECUTABLE :
if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe" (
    echo ✅ Fichier PharmaModerne.UI.exe existe
    echo 📄 Taille du fichier :
    dir "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe" | find "PharmaModerne.UI.exe"
) else (
    echo ❌ PROBLEME : Fichier PharmaModerne.UI.exe manquant !
    echo 🔧 SOLUTION : Recompilation necessaire
    goto :recompile
)
echo.

REM Vérifier .NET
echo 2. VERIFICATION .NET :
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET disponible - Version :
    dotnet --version
) else (
    echo ❌ PROBLEME : .NET non disponible !
    echo 🔧 SOLUTION : Installer .NET 9.0 Runtime
    echo 📥 Telecharger depuis : https://dotnet.microsoft.com/download
    pause
    exit /b 1
)
echo.

REM Vérifier les dépendances
echo 3. VERIFICATION DES DEPENDANCES :
cd "PharmaModerne.UI\bin\Debug\net9.0-windows"

if exist "PharmaModerne.Core.dll" (echo ✅ PharmaModerne.Core.dll) else (echo ❌ PharmaModerne.Core.dll MANQUANT)
if exist "PharmaModerne.Services.dll" (echo ✅ PharmaModerne.Services.dll) else (echo ❌ PharmaModerne.Services.dll MANQUANT)
if exist "PharmaModerne.Data.dll" (echo ✅ PharmaModerne.Data.dll) else (echo ❌ PharmaModerne.Data.dll MANQUANT)
if exist "PharmaModerne.Shared.dll" (echo ✅ PharmaModerne.Shared.dll) else (echo ❌ PharmaModerne.Shared.dll MANQUANT)

echo.

REM Test de lancement avec capture d'erreurs
echo 4. TEST DE LANCEMENT AVEC CAPTURE D'ERREURS :
echo.
echo 🚀 Tentative de lancement...

REM Créer un script PowerShell pour capturer les erreurs
echo try { > test_app.ps1
echo     Write-Host "Demarrage de l'application..." >> test_app.ps1
echo     $process = Start-Process -FilePath ".\PharmaModerne.UI.exe" -PassThru -WindowStyle Normal >> test_app.ps1
echo     Start-Sleep -Seconds 5 >> test_app.ps1
echo     if ($process.HasExited) { >> test_app.ps1
echo         Write-Host "PROBLEME: Application fermee immediatement" >> test_app.ps1
echo         Write-Host "Code de sortie: $($process.ExitCode)" >> test_app.ps1
echo     } else { >> test_app.ps1
echo         Write-Host "Application semble demarree (PID: $($process.Id))" >> test_app.ps1
echo         Write-Host "Verification si la fenetre est visible..." >> test_app.ps1
echo         $windows = Get-Process | Where-Object {$_.ProcessName -eq "PharmaModerne.UI"} >> test_app.ps1
echo         if ($windows) { >> test_app.ps1
echo             Write-Host "SUCCES: Processus trouve et actif" >> test_app.ps1
echo         } else { >> test_app.ps1
echo             Write-Host "PROBLEME: Processus non trouve" >> test_app.ps1
echo         } >> test_app.ps1
echo     } >> test_app.ps1
echo } catch { >> test_app.ps1
echo     Write-Host "ERREUR lors du lancement:" >> test_app.ps1
echo     Write-Host $_.Exception.Message >> test_app.ps1
echo } >> test_app.ps1

powershell -ExecutionPolicy Bypass -File test_app.ps1

echo.

REM Vérifier les processus
echo 5. VERIFICATION DES PROCESSUS :
tasklist | find "PharmaModerne" >nul
if %errorlevel% equ 0 (
    echo ✅ Processus PharmaModerne detecte :
    tasklist | find "PharmaModerne"
) else (
    echo ❌ PROBLEME : Aucun processus PharmaModerne trouve
    echo 💡 L'application ne demarre pas ou crash immediatement
)
echo.

REM Test avec dotnet run
echo 6. TEST AVEC DOTNET RUN :
cd ..\..\..\..\
echo 🔄 Test avec dotnet run...
timeout /t 2 /nobreak >nul
dotnet run --project PharmaModerne.UI\PharmaModerne.UI.csproj 2>&1 | head -20

echo.

REM Nettoyer
cd "PharmaModerne.UI\bin\Debug\net9.0-windows"
del test_app.ps1 2>nul
cd ..\..\..\..\

echo ========================================
echo    SOLUTIONS POSSIBLES
echo ========================================
echo.

echo 🔧 SOLUTIONS A ESSAYER :
echo.
echo 1. RECOMPILATION COMPLETE :
echo    dotnet clean
echo    dotnet restore
echo    dotnet build --configuration Debug
echo.
echo 2. LANCEMENT EN ADMINISTRATEUR :
echo    Clic droit sur l'executable ^> "Executer en tant qu'administrateur"
echo.
echo 3. VERIFIER ANTIVIRUS :
echo    Desactiver temporairement l'antivirus
echo    Ajouter le dossier en exception
echo.
echo 4. INSTALLER .NET RUNTIME :
echo    Telecharger .NET 9.0 Desktop Runtime
echo    https://dotnet.microsoft.com/download/dotnet/9.0
echo.
echo 5. VERIFIER DEPENDANCES WINDOWS :
echo    Installer Visual C++ Redistributable
echo    Mettre a jour Windows
echo.
echo 6. TEST EN MODE CONSOLE :
echo    Ouvrir cmd dans le dossier de l'executable
echo    Executer : PharmaModerne.UI.exe
echo    Voir les messages d'erreur
echo.

goto :end

:recompile
echo.
echo 🔧 RECOMPILATION AUTOMATIQUE...
dotnet clean
dotnet restore
dotnet build PharmaModerne.UI\PharmaModerne.UI.csproj --configuration Debug

if %errorlevel% equ 0 (
    echo ✅ Recompilation reussie !
    echo 🔄 Relancez ce diagnostic pour tester
) else (
    echo ❌ Erreur de compilation !
    echo 📋 Verifiez les erreurs ci-dessus
)

:end
echo.
echo ========================================
echo    INFORMATIONS SYSTEME
echo ========================================
echo.
echo 🖥️ Systeme : 
ver
echo.
echo 💾 Memoire disponible :
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /format:list | find "="
echo.
echo 📁 Espace disque :
dir | find "octets libres"
echo.

echo Appuyez sur une touche pour fermer...
pause >nul
