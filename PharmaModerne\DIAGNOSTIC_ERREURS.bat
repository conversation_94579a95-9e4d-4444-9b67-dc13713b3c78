@echo off
echo ========================================
echo    DIAGNOSTIC DES ERREURS PHARMA2000
echo    Detection et resolution des problemes
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 VERIFICATION DE L'ENVIRONNEMENT...
echo.

REM Vérifier .NET
echo 📋 Version .NET installée :
dotnet --version
if %errorlevel% neq 0 (
    echo ❌ .NET n'est pas installé ou accessible
    echo 💡 Installez .NET 9.0 SDK depuis https://dotnet.microsoft.com/
    pause
    exit /b 1
) else (
    echo ✅ .NET est disponible
)
echo.

REM Vérifier l'exécutable
echo 🔍 Vérification de l'exécutable...
if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe" (
    echo ✅ Exécutable trouvé
    echo 📄 Taille : 
    dir "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe" | find "PharmaModerne.UI.exe"
) else (
    echo ❌ Exécutable non trouvé
    echo 🔧 Recompilation nécessaire...
    dotnet build PharmaModerne.UI\PharmaModerne.UI.csproj --configuration Debug
    if %errorlevel% neq 0 (
        echo ❌ Erreur de compilation
        pause
        exit /b 1
    )
)
echo.

REM Vérifier les dépendances
echo 🔍 Vérification des dépendances...
if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.Core.dll" (
    echo ✅ PharmaModerne.Core.dll présent
) else (
    echo ❌ PharmaModerne.Core.dll manquant
)

if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.Services.dll" (
    echo ✅ PharmaModerne.Services.dll présent
) else (
    echo ❌ PharmaModerne.Services.dll manquant
)

if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.Data.dll" (
    echo ✅ PharmaModerne.Data.dll présent
) else (
    echo ❌ PharmaModerne.Data.dll manquant
)

if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.Shared.dll" (
    echo ✅ PharmaModerne.Shared.dll présent
) else (
    echo ❌ PharmaModerne.Shared.dll manquant
)
echo.

REM Tester le lancement avec capture d'erreurs
echo 🚀 TEST DE LANCEMENT AVEC DIAGNOSTIC...
echo.

cd "PharmaModerne.UI\bin\Debug\net9.0-windows"

echo 📱 Tentative de lancement...
echo ⏰ Lancement dans 3 secondes...
timeout /t 3 /nobreak >nul

REM Créer un script PowerShell pour capturer les erreurs
echo try { > test_launch.ps1
echo     Start-Process -FilePath ".\PharmaModerne.UI.exe" -Wait -NoNewWindow >> test_launch.ps1
echo     Write-Host "✅ Application lancée avec succès" >> test_launch.ps1
echo } catch { >> test_launch.ps1
echo     Write-Host "❌ Erreur lors du lancement: $_" >> test_launch.ps1
echo     Write-Host "Type d'erreur: $($_.Exception.GetType().Name)" >> test_launch.ps1
echo     Write-Host "Message: $($_.Exception.Message)" >> test_launch.ps1
echo } >> test_launch.ps1

powershell -ExecutionPolicy Bypass -File test_launch.ps1

if %errorlevel% equ 0 (
    echo.
    echo ✅ DIAGNOSTIC TERMINÉ - Application fonctionnelle
) else (
    echo.
    echo ❌ DIAGNOSTIC TERMINÉ - Erreurs détectées
    echo.
    echo 🔧 SOLUTIONS POSSIBLES :
    echo 1. Recompiler le projet : dotnet build --configuration Debug
    echo 2. Vérifier les dépendances manquantes
    echo 3. Installer .NET 9.0 Runtime
    echo 4. Vérifier les permissions d'exécution
    echo 5. Désactiver l'antivirus temporairement
)

REM Nettoyer
del test_launch.ps1 2>nul

cd ..\..\..\..\

echo.
echo ========================================
echo    INFORMATIONS SYSTÈME
echo ========================================
echo.
echo 🖥️ Système d'exploitation :
ver
echo.
echo 📁 Répertoire actuel :
echo %CD%
echo.
echo 🔧 Variables d'environnement importantes :
echo PATH (extrait) : %PATH:~0,200%...
echo.
echo 💾 Espace disque disponible :
dir | find "octets libres"
echo.

echo Appuyez sur une touche pour fermer...
pause >nul
