﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fReleveeMutuelle
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fReleveeMutuelle))
        Me.PAnel = New System.Windows.Forms.Panel()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.tNumero = New C1.Win.C1Input.C1TextBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.cmbMutuelle = New C1.Win.C1List.C1Combo()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.dpDateFin = New C1.Win.C1Input.C1DateEdit()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.dpDateDebut = New C1.Win.C1Input.C1DateEdit()
        Me.LDateDebut = New System.Windows.Forms.Label()
        Me.bRechercher = New C1.Win.C1Input.C1Button()
        Me.bSuprimerreleve = New C1.Win.C1Input.C1Button()
        Me.gReleves = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bModifierReleve = New C1.Win.C1Input.C1Button()
        Me.bAjouterReleve = New C1.Win.C1Input.C1Button()
        Me.LListereleves = New System.Windows.Forms.Label()
        Me.PAnel.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.tNumero, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbMutuelle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dpDateFin, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dpDateDebut, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gReleves, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PAnel
        '
        Me.PAnel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.PAnel.Controls.Add(Me.bQuitter)
        Me.PAnel.Controls.Add(Me.GroupBox2)
        Me.PAnel.Controls.Add(Me.bSuprimerreleve)
        Me.PAnel.Controls.Add(Me.gReleves)
        Me.PAnel.Controls.Add(Me.bModifierReleve)
        Me.PAnel.Controls.Add(Me.bAjouterReleve)
        Me.PAnel.Controls.Add(Me.LListereleves)
        Me.PAnel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.PAnel.Location = New System.Drawing.Point(0, 0)
        Me.PAnel.Name = "PAnel"
        Me.PAnel.Size = New System.Drawing.Size(1020, 742)
        Me.PAnel.TabIndex = 5
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(902, 20)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(105, 45)
        Me.bQuitter.TabIndex = 24
        Me.bQuitter.Text = "Quitter             F12   "
        Me.bQuitter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox2
        '
        Me.GroupBox2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox2.Controls.Add(Me.tNumero)
        Me.GroupBox2.Controls.Add(Me.Label3)
        Me.GroupBox2.Controls.Add(Me.cmbMutuelle)
        Me.GroupBox2.Controls.Add(Me.Label8)
        Me.GroupBox2.Controls.Add(Me.dpDateFin)
        Me.GroupBox2.Controls.Add(Me.Label6)
        Me.GroupBox2.Controls.Add(Me.dpDateDebut)
        Me.GroupBox2.Controls.Add(Me.LDateDebut)
        Me.GroupBox2.Controls.Add(Me.bRechercher)
        Me.GroupBox2.Location = New System.Drawing.Point(12, 12)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(634, 81)
        Me.GroupBox2.TabIndex = 16
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "Critères de recherche"
        '
        'tNumero
        '
        Me.tNumero.AutoSize = False
        Me.tNumero.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNumero.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNumero.Location = New System.Drawing.Point(72, 46)
        Me.tNumero.Name = "tNumero"
        Me.tNumero.Size = New System.Drawing.Size(134, 21)
        Me.tNumero.TabIndex = 26
        Me.tNumero.Tag = Nothing
        Me.tNumero.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNumero.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Location = New System.Drawing.Point(22, 50)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(44, 13)
        Me.Label3.TabIndex = 27
        Me.Label3.Text = "Numéro"
        '
        'cmbMutuelle
        '
        Me.cmbMutuelle.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbMutuelle.Caption = ""
        Me.cmbMutuelle.CaptionHeight = 17
        Me.cmbMutuelle.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbMutuelle.ColumnCaptionHeight = 17
        Me.cmbMutuelle.ColumnFooterHeight = 17
        Me.cmbMutuelle.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbMutuelle.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbMutuelle.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbMutuelle.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbMutuelle.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbMutuelle.Images.Add(CType(resources.GetObject("cmbMutuelle.Images"), System.Drawing.Image))
        Me.cmbMutuelle.ItemHeight = 15
        Me.cmbMutuelle.Location = New System.Drawing.Point(72, 21)
        Me.cmbMutuelle.MatchEntryTimeout = CType(2000, Long)
        Me.cmbMutuelle.MaxDropDownItems = CType(5, Short)
        Me.cmbMutuelle.MaxLength = 32767
        Me.cmbMutuelle.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbMutuelle.Name = "cmbMutuelle"
        Me.cmbMutuelle.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbMutuelle.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbMutuelle.Size = New System.Drawing.Size(134, 22)
        Me.cmbMutuelle.TabIndex = 69
        Me.cmbMutuelle.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbMutuelle.PropBag = resources.GetString("cmbMutuelle.PropBag")
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Location = New System.Drawing.Point(19, 25)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(47, 13)
        Me.Label8.TabIndex = 68
        Me.Label8.Text = "Mutuelle"
        '
        'dpDateFin
        '
        Me.dpDateFin.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dpDateFin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dpDateFin.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dpDateFin.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dpDateFin.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dpDateFin.Location = New System.Drawing.Point(295, 46)
        Me.dpDateFin.Name = "dpDateFin"
        Me.dpDateFin.Size = New System.Drawing.Size(156, 18)
        Me.dpDateFin.TabIndex = 15
        Me.dpDateFin.Tag = Nothing
        Me.dpDateFin.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dpDateFin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Location = New System.Drawing.Point(245, 48)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(44, 13)
        Me.Label6.TabIndex = 14
        Me.Label6.Text = "Date fin"
        '
        'dpDateDebut
        '
        Me.dpDateDebut.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dpDateDebut.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dpDateDebut.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dpDateDebut.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dpDateDebut.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dpDateDebut.Location = New System.Drawing.Point(295, 22)
        Me.dpDateDebut.Name = "dpDateDebut"
        Me.dpDateDebut.Size = New System.Drawing.Size(156, 18)
        Me.dpDateDebut.TabIndex = 13
        Me.dpDateDebut.Tag = Nothing
        Me.dpDateDebut.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dpDateDebut.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'LDateDebut
        '
        Me.LDateDebut.AutoSize = True
        Me.LDateDebut.Location = New System.Drawing.Point(229, 25)
        Me.LDateDebut.Name = "LDateDebut"
        Me.LDateDebut.Size = New System.Drawing.Size(60, 13)
        Me.LDateDebut.TabIndex = 10
        Me.LDateDebut.Text = "Date début"
        '
        'bRechercher
        '
        Me.bRechercher.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bRechercher.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bRechercher.Image = Global.Pharma2000Premium.My.Resources.Resources.recherche2
        Me.bRechercher.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bRechercher.Location = New System.Drawing.Point(462, 20)
        Me.bRechercher.Name = "bRechercher"
        Me.bRechercher.Size = New System.Drawing.Size(156, 44)
        Me.bRechercher.TabIndex = 4
        Me.bRechercher.Text = "Rechercher                                F6"
        Me.bRechercher.UseVisualStyleBackColor = True
        Me.bRechercher.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bSuprimerreleve
        '
        Me.bSuprimerreleve.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bSuprimerreleve.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSuprimerreleve.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler2
        Me.bSuprimerreleve.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSuprimerreleve.Location = New System.Drawing.Point(716, 685)
        Me.bSuprimerreleve.Name = "bSuprimerreleve"
        Me.bSuprimerreleve.Size = New System.Drawing.Size(100, 45)
        Me.bSuprimerreleve.TabIndex = 2
        Me.bSuprimerreleve.Text = "Suprimer                     F7"
        Me.bSuprimerreleve.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSuprimerreleve.UseVisualStyleBackColor = True
        Me.bSuprimerreleve.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gReleves
        '
        Me.gReleves.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gReleves.GroupByCaption = "Drag a column header here to group by that column"
        Me.gReleves.Images.Add(CType(resources.GetObject("gReleves.Images"), System.Drawing.Image))
        Me.gReleves.Location = New System.Drawing.Point(11, 112)
        Me.gReleves.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gReleves.Name = "gReleves"
        Me.gReleves.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gReleves.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gReleves.PreviewInfo.ZoomFactor = 75.0R
        Me.gReleves.PrintInfo.PageSettings = CType(resources.GetObject("gReleves.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gReleves.Size = New System.Drawing.Size(996, 559)
        Me.gReleves.TabIndex = 12
        Me.gReleves.Text = "C1TrueDBGrid1"
        Me.gReleves.PropBag = resources.GetString("gReleves.PropBag")
        '
        'bModifierReleve
        '
        Me.bModifierReleve.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bModifierReleve.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bModifierReleve.Image = Global.Pharma2000Premium.My.Resources.Resources.voir_modifierfpharmacien
        Me.bModifierReleve.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bModifierReleve.Location = New System.Drawing.Point(820, 685)
        Me.bModifierReleve.Name = "bModifierReleve"
        Me.bModifierReleve.Size = New System.Drawing.Size(187, 45)
        Me.bModifierReleve.TabIndex = 3
        Me.bModifierReleve.Text = "Modifier / Règler / Générer     F8"
        Me.bModifierReleve.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bModifierReleve.UseVisualStyleBackColor = True
        Me.bModifierReleve.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouterReleve
        '
        Me.bAjouterReleve.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouterReleve.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouterReleve.Image = Global.Pharma2000Premium.My.Resources.Resources.nouveau
        Me.bAjouterReleve.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouterReleve.Location = New System.Drawing.Point(611, 685)
        Me.bAjouterReleve.Name = "bAjouterReleve"
        Me.bAjouterReleve.Size = New System.Drawing.Size(100, 45)
        Me.bAjouterReleve.TabIndex = 1
        Me.bAjouterReleve.Text = "Ajouter               F5"
        Me.bAjouterReleve.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouterReleve.UseVisualStyleBackColor = True
        Me.bAjouterReleve.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'LListereleves
        '
        Me.LListereleves.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LListereleves.AutoSize = True
        Me.LListereleves.Location = New System.Drawing.Point(12, 96)
        Me.LListereleves.Name = "LListereleves"
        Me.LListereleves.Size = New System.Drawing.Size(86, 13)
        Me.LListereleves.TabIndex = 7
        Me.LListereleves.Text = "Liste des relevés"
        '
        'fReleveeMutuelle
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1020, 742)
        Me.Controls.Add(Me.PAnel)
        Me.Name = "fReleveeMutuelle"
        Me.Text = "fReleveeMutuelle"
        Me.PAnel.ResumeLayout(False)
        Me.PAnel.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.tNumero, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbMutuelle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dpDateFin, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dpDateDebut, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gReleves, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents PAnel As System.Windows.Forms.Panel
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents dpDateFin As C1.Win.C1Input.C1DateEdit
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents dpDateDebut As C1.Win.C1Input.C1DateEdit
    Friend WithEvents LDateDebut As System.Windows.Forms.Label
    Friend WithEvents bRechercher As C1.Win.C1Input.C1Button
    Friend WithEvents bSuprimerreleve As C1.Win.C1Input.C1Button
    Friend WithEvents gReleves As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bModifierReleve As C1.Win.C1Input.C1Button
    Friend WithEvents bAjouterReleve As C1.Win.C1Input.C1Button
    Friend WithEvents LListereleves As System.Windows.Forms.Label
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents cmbMutuelle As C1.Win.C1List.C1Combo
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents tNumero As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
End Class
