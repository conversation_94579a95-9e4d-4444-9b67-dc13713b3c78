﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports Microsoft.Reporting.WinForms
Imports System.Linq.Dynamic

Public Class fEtatDesVentesParJour
    Dim _SalesReportService As New Bll.Reporting.SalesReport
    Dim _VOrderBy As String = "Id"
    Dim _VAscDesc As String = "Asc"

    Dim StrSQL As String = ""

    Dim cmdVente As New SqlCommand
    Dim daVente As New SqlDataAdapter
    Dim dsVente As New DataSet

    Public Source As String = ""

    Dim TotalComptant As Double = 0.0
    Dim TotalCredit As Double = 0.0
    Dim TotalMutuelle As Double = 0.0
    Dim TotalCNAM As Double = 0.0
    Dim Total As Double = 0.0
    Dim TotalReglement As Double = 0.0
    Dim TotalCaisse As Double = 0.0
    Dim TotalCheque As Double = 0.0
    Dim TotalCarte As Double = 0.0

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub init()

        'chargement des Mois
        StrSQL = "SELECT NumeroMois,NomMois FROM MOIS_ANNEE AS MOIS ORDER BY NumeroMois ASC"
        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = StrSQL
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "MOIS")
        cmbMois.DataSource = dsVente.Tables("MOIS")
        cmbMois.ValueMember = "NumeroMois"
        cmbMois.DisplayMember = "NomMois"
        cmbMois.ColumnHeaders = False
        cmbMois.Splits(0).DisplayColumns("NumeroMois").Visible = False
        cmbMois.Splits(0).DisplayColumns("NomMois").Width = 10
        cmbMois.ExtendRightColumn = True

        cmbMois.SelectedValue = System.DateTime.Now.Month
        tAnnee.Text = System.DateTime.Now.Year

        AfficherVentes()
        cmbMois.Focus()

    End Sub

    Public Sub AfficherVentes()

        Dim List As New Library.SortableBindingList(Of Data.Reporting.P_Report_VentesQuotidienne_Result)(_SalesReportService.GetVentesQuotidiennes(cmbMois.SelectedValue, tAnnee.Text))
        With gVentes
            .Columns.Clear()
            .DataSource = List
            .Rebind(False)
            .Columns("NumeroJour").Caption = "Jour"
            .Columns("Espece").Caption = "Espece"
            .Columns("MontantMutuelle").Caption = "Mutuelle"
            .Columns("MontantCnam").Caption = "CNAM"
            .Columns("TotalTTC").Caption = "Total TTC"
            .Columns("CREDIT").Caption = "Crédit"
            .Columns("ReglementClient").Caption = "Reglement"
            .Columns("Caisse").Caption = "Caisse (Espece + Reglement)"

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("NumeroJour").Width = 100            '
            .Splits(0).DisplayColumns("Espece").Width = 111
            .Splits(0).DisplayColumns("CREDIT").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("MontantMutuelle").Width = 111
            .Splits(0).DisplayColumns("MontantMutuelle").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("MontantCnam").Width = 111
            .Splits(0).DisplayColumns("MontantCnam").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("TotalTTC").Width = 111
            .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("CREDIT").Width = 111
            .Splits(0).DisplayColumns("CREDIT").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("ReglementClient").Width = 111
            .Splits(0).DisplayColumns("ReglementClient").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("Caisse").Width = 200
            .Splits(0).DisplayColumns("Caisse").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("Id").Visible = False


            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gVentes)
        End With

        TotalComptant = 0.0
        TotalCredit = 0.0
        TotalMutuelle = 0.0
        TotalCNAM = 0.0
        Total = 0.0
        TotalReglement = 0.0
        TotalCaisse = 0.0

        For I = 0 To gVentes.RowCount - 1
            If gVentes(I, "Espece").ToString <> "" Then
                TotalComptant = TotalComptant + gVentes(I, "Espece")
            End If
            If gVentes(I, "MontantMutuelle").ToString <> "" Then
                TotalMutuelle = TotalMutuelle + gVentes(I, "MontantMutuelle")
            End If
            If gVentes(I, "MontantCnam").ToString <> "" Then
                TotalCNAM = TotalCNAM + gVentes(I, "MontantCnam")
            End If
            If gVentes(I, "TotalTTC").ToString <> "" Then
                Total = Total + gVentes(I, "TotalTTC")
            End If
            If gVentes(I, "CREDIT").ToString <> "" Then
                TotalCredit = TotalCredit + gVentes(I, "CREDIT")
            End If
            If gVentes(I, "ReglementClient").ToString <> "" Then
                TotalReglement = TotalReglement + gVentes(I, "ReglementClient")
            End If
            If gVentes(I, "Caisse").ToString <> "" Then
                TotalCaisse = TotalCaisse + gVentes(I, "Caisse")
            End If
            If gVentes(I, "Cheque").ToString <> "" Then
                TotalCheque = TotalCheque + gVentes(I, "Cheque")
            End If
            If gVentes(I, "Carte").ToString <> "" Then
                TotalCarte = TotalCarte + gVentes(I, "Carte")
            End If
        Next

        lComptant.Text = Math.Round(TotalComptant + TotalCarte + TotalCheque, 3).ToString("### ### ##0.000")
        lMutuelle.Text = Math.Round(TotalMutuelle, 3).ToString("### ### ##0.000")
        lCNAM.Text = Math.Round(TotalCNAM, 3).ToString("### ### ##0.000")
        lTotalTTC.Text = Math.Round(Total, 3).ToString("### ### ##0.000")
        lCredit.Text = Math.Round(TotalCredit, 3).ToString("### ### ##0.000")
        LReglement.Text = Math.Round(TotalReglement, 3).ToString("### ### ##0.000")
        lCaisse.Text = Math.Round(TotalCaisse, 3).ToString("### ### ##0.000")

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub tMois_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        AfficherVentes()
    End Sub

    Private Sub bQuitter_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bQuitter.KeyUp
        If e.KeyCode = Keys.Enter Then
            tAnnee.Focus()
        End If
    End Sub

    Private Sub tAnnee_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tAnnee.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherVentes()
        End If
    End Sub

    Private Sub tAnnee_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tAnnee.TextChanged

    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Reporting Ventes" Then
                num = I
            End If
        Next
        Dim dt

        Dim _Parameters As New List(Of ReportParameter)()

        Dim _Mois As New ReportParameter()
        _Mois.Name = "Mois"
        _Mois.Values.Add(cmbMois.SelectedValue)
        _Parameters.Add(_Mois)

        Dim _Annee As New ReportParameter()
        _Annee.Name = "Annee"
        _Annee.Values.Add(tAnnee.Text)
        _Parameters.Add(_Annee)

        Dim MyViewer As New fImpressionReportingVente

        dt = _SalesReportService.GetVentesQuotidiennes(cmbMois.SelectedValue, tAnnee.Text).OrderBy(_VOrderBy + " " + _VAscDesc)
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_VentesQuotidienne", dt))
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Pharmacie", _SalesReportService.GetPharmacie()))
        MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatVentesQuotidiennes.rdl"

        MyViewer.ReportViewer1.LocalReport.SetParameters(_Parameters)

        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel1)

        MyViewer.ReportViewer1.RefreshReport()

        fMain.Tab.SelectedTab.Text = "Impression Reporting Ventes"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    Private Sub gVentes_AfterSort(sender As Object, e As FilterEventArgs) Handles gVentes.AfterSort
        Try
            _VOrderBy = e.Condition.Substring(0, e.Condition.IndexOf(" "))
            _VAscDesc = e.Condition.Substring(e.Condition.IndexOf(" ") + 1, e.Condition.Length - e.Condition.IndexOf(" ") - 1)
        Catch ex As Exception
            _VOrderBy = e.Condition
            _VAscDesc = "Asc"
        End Try
    End Sub

    Private Sub gVentes_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gVentes.Click

    End Sub

    Private Sub cmbMois_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMois.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherVentes()
        End If
    End Sub

    Private Sub cmbMois_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbMois.TextChanged

    End Sub
End Class