﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fListeDesCommandePourAchat

    Dim cmdCommandeEnAchat As New SqlCommand
    Dim cbCommandeEnAchat As New SqlCommandBuilder
    Public Shared dsCommandeEnAchat As New DataSet
    Dim daCommandeEnAchat As New SqlDataAdapter
    Dim StrSQL As String = ""

    Public Shared ComfirmerChargementDesCommandes As Boolean = False
    Public CodeFournisseur As String = ""

    Dim NumeroCommande As String = ""

    'Public NumeroCommandeExiste As String = ""

    Public Sub init()
        Dim I As Integer
        Try
            dsCommandeEnAchat.Tables("COMMANDE").Clear()
        Catch ex As Exception

        End Try
        'intialisation de la gride      
        StrSQL = "SELECT 'supprimer' as supprimer, CAST(0 as bit) Cocher," + _
                 "NumeroCommande," + _
                 "NomFournisseur," + _
                 "Date," + _
                 "TotalHT," + _
                 "TotalTTC " + _
                 "" + _
                 "FROM " + _
                 "COMMANDE,FOURNISSEUR WHERE " + _
                 "COMMANDE.CodeFournisseur=FOURNISSEUR.CodeFournisseur" + _
                 " AND (NumeroFacture IS NULL OR NumeroFacture = '') "

        If CodeFournisseur <> "" Then
            StrSQL = StrSQL + " AND COMMANDE.CodeFournisseur ='" + CodeFournisseur + "'"
        End If
        StrSQL += " ORDER BY NumeroCommande DESC "

        cmdCommandeEnAchat.Connection = ConnectionServeur
        cmdCommandeEnAchat.CommandText = StrSQL
        daCommandeEnAchat = New SqlDataAdapter(cmdCommandeEnAchat)
        daCommandeEnAchat.Fill(dsCommandeEnAchat, "COMMANDE")

        ' ''Try
        ' ''    dsCommandeEnAchat.Tables("COMMANDE").Columns.Add("Supprimer", Type.GetType("C1.Win.C1TrueDBGrid.C1DataColumn"))
        ' ''Catch ex As Exception
        ' ''End Try

        With gCommandes
            .Columns.Clear()
            Try
                .DataSource = dsCommandeEnAchat
            Catch ex As Exception
            End Try
            .DataMember = "COMMANDE"
            .Rebind(False)
            .Columns("Cocher").Caption = "><"
            .Columns("NumeroCommande").Caption = "Numero Commande"
            .Columns("NomFournisseur").Caption = "Fournisseur"
            .Columns("Date").Caption = "Date"
            .Columns("TotalTTC").Caption = "Total TTC"
            .Columns("TotalHT").Caption = "Total HT"
            .Columns("supprimer").Caption = ""
            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("NomFournisseur").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns(0).Width = 100
            .Splits(0).DisplayColumns("Cocher").Width = 60 '100
            .Splits(0).DisplayColumns("NumeroCommande").Width = 100
            .Splits(0).DisplayColumns("NomFournisseur").Width = 120
            .Splits(0).DisplayColumns("Date").Width = 100
            .Splits(0).DisplayColumns("TotalTTC").Width = 70
            .Splits(0).DisplayColumns("TotalHT").Width = 70
            .Splits(0).DisplayColumns("Cocher").Locked = False
            .Splits(0).DisplayColumns("supprimer").Locked = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

            .VisualStyle = VisualStyle.Office2007Blue
            'Style du Caractere et du grid
            ParametreGrid(gCommandes)
        End With


        gCommandes.Splits(0).DisplayColumns("supprimer").ButtonText = True
        gCommandes.Splits(0).DisplayColumns("supprimer").ButtonAlways = True

        NumeroCommande = gCommandes(0, "NumeroCommande")
        AfficherDetailsCommande(NumeroCommande)
    End Sub
    Public Sub AfficherDetailsCommande(ByVal NumeroCommande)
        Dim I As Integer
        Try
            dsCommandeEnAchat.Tables("COMMANDE_DETAILS").Clear()
        Catch ex As Exception

        End Try
        'intialisation de la gride      
        StrSQL = "SELECT CodeArticle,CodeABarre,Designation,Qte,PrixAchatHT FROM COMMANDE_DETAILS " + _
        "WHERE NumeroCommande ='" + NumeroCommande + "'"

        cmdCommandeEnAchat.Connection = ConnectionServeur
        cmdCommandeEnAchat.CommandText = StrSQL
        daCommandeEnAchat = New SqlDataAdapter(cmdCommandeEnAchat)
        daCommandeEnAchat.Fill(dsCommandeEnAchat, "COMMANDE_DETAILS")

        With gDetailsCommande
            .Columns.Clear()
            Try
                .DataSource = dsCommandeEnAchat
            Catch ex As Exception
            End Try
            .DataMember = "COMMANDE_DETAILS"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code Article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("Qte").Caption = "Qte"
            .Columns("PrixAchatHT").Caption = "Prix Achat HT"

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
            .Splits(0).DisplayColumns("CodeABarre").Width = 100
            .Splits(0).DisplayColumns("Designation").Width = 300
            .Splits(0).DisplayColumns("Qte").Width = 100
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 100

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            .VisualStyle = VisualStyle.Office2007Blue
            'Style du Caractere et du grid
            ParametreGrid(gDetailsCommande)
        End With
    End Sub
    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        ComfirmerChargementDesCommandes = False
        Me.Hide()
    End Sub

    Private Sub gCommandes_ButtonClick(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gCommandes.ButtonClick

        'Suppression Commande et ses details
        If gCommandes(gCommandes.Row, "cocher") = False Then
            Exit Sub
        End If
        'Message de confirmation de suppression
        If MsgBox("Voulez vous vraiment supprimer cet achat " + gCommandes(gCommandes.Row, "NumeroCommande") + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
            cmdCommandeEnAchat.Connection = ConnectionServeur
            cmdCommandeEnAchat.CommandText = "DELETE FROM COMMANDE_DETAILS WHERE NumeroCommande = " + Quote(gCommandes(gCommandes.Row, "NumeroCommande"))
            Try
                cmdCommandeEnAchat.ExecuteNonQuery()
            Catch ex As Exception
            End Try
            cmdCommandeEnAchat.Connection = ConnectionServeur
            cmdCommandeEnAchat.CommandText = "DELETE FROM COMMANDE WHERE NumeroCommande = " + Quote(gCommandes(gCommandes.Row, "NumeroCommande"))
            Try
                cmdCommandeEnAchat.ExecuteNonQuery()
            Catch ex As Exception
            End Try
            init()
        End If
    End Sub

    Private Sub gCommandes_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gCommandes.Click


    End Sub

    Private Sub gCommandes_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles gCommandes.DoubleClick
        '' ''Suppression Commande et ses details

        '' ''Message de confirmation de suppression
        ' ''If MsgBox("Voulez vous vraiment supprimer cet achat " + gCommandes(gCommandes.Row, "NumeroCommande") + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
        ' ''    cmdCommandeEnAchat.Connection = ConnectionServeur
        ' ''    cmdCommandeEnAchat.CommandText = "DELETE FROM COMMANDE_DETAILS WHERE NumeroCommande = " + Quote(gCommandes(gCommandes.Row, "NumeroCommande"))
        ' ''    Try
        ' ''        cmdCommandeEnAchat.ExecuteNonQuery()
        ' ''    Catch ex As Exception
        ' ''    End Try
        ' ''    cmdCommandeEnAchat.Connection = ConnectionServeur
        ' ''    cmdCommandeEnAchat.CommandText = "DELETE FROM COMMANDE WHERE NumeroCommande = " + Quote(gCommandes(gCommandes.Row, "NumeroCommande"))
        ' ''    Try
        ' ''        cmdCommandeEnAchat.ExecuteNonQuery()
        ' ''    Catch ex As Exception
        ' ''    End Try
        ' ''    init()
        ' ''End If
    End Sub

    Private Sub gCommandes_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gCommandes.KeyUp
        If e.KeyCode = Keys.Down Or e.KeyCode = Keys.Up Then
            NumeroCommande = gCommandes(gCommandes.Row, "NumeroCommande")
            AfficherDetailsCommande(NumeroCommande)
        End If

        If e.KeyCode = Keys.F3 Or e.KeyCode = Keys.Enter Then
            bOK_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub gCommandes_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gCommandes.MouseClick
        NumeroCommande = gCommandes(gCommandes.Row, "NumeroCommande")
        AfficherDetailsCommande(NumeroCommande)
    End Sub

    Private Sub ListeDesCommandePourAchat_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        init()
    End Sub

    Private Sub bOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOK.Click
        Dim i As Integer = 0
        Dim j As Integer = 0

        For i = 0 To dsCommandeEnAchat.Tables("COMMANDE").Rows.Count - 1
            If dsCommandeEnAchat.Tables("COMMANDE").Rows(i).Item("Cocher") = True Then
                For j = i To dsCommandeEnAchat.Tables("COMMANDE").Rows.Count - 1
                    If dsCommandeEnAchat.Tables("COMMANDE").Rows(j).Item("Cocher") = True Then
                        If dsCommandeEnAchat.Tables("COMMANDE").Rows(j).Item("NomFournisseur") <> dsCommandeEnAchat.Tables("COMMANDE").Rows(i).Item("NomFournisseur") Then
                            MsgBox("Il faut choisir des commandes du même fournisseur !", MsgBoxStyle.Critical, "Erreur")
                            Exit Sub
                        End If
                    End If
                Next
            End If
        Next

        ComfirmerChargementDesCommandes = True
        Me.Hide()
    End Sub

    Private Sub gDetailsCommande_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gDetailsCommande.Click

    End Sub

    Private Sub gDetailsCommande_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gDetailsCommande.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub bOK_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bOK.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub bAnnuler_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bAnnuler.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub GroupBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GroupBox1.Enter

    End Sub

    Private Sub GroupBox1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles GroupBox1.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub GroupBox5_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GroupBox5.Enter

    End Sub

    Private Sub GroupBox5_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles GroupBox5.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub
End Class