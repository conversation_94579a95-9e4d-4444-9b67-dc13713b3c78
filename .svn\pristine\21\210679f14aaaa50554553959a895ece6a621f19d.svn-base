﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fFicheArticle
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fFicheArticle))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bChangeQuantiteUnitaire = New C1.Win.C1Input.C1Button()
        Me.bChangementPrix = New C1.Win.C1Input.C1Button()
        Me.bStatistiqueArticle = New C1.Win.C1Input.C1Button()
        Me.lNomArticle = New System.Windows.Forms.Label()
        Me.bNaviger = New C1.Win.C1Input.C1Button()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.bConfirmer = New C1.Win.C1Input.C1Button()
        Me.Tab = New C1.Win.C1Command.C1DockingTab()
        Me.C1DockingTabPage1 = New C1.Win.C1Command.C1DockingTabPage()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.tstockAlert = New C1.Win.C1Input.C1TextBox()
        Me.tQuantiteACommander = New C1.Win.C1Input.C1TextBox()
        Me.GroupBox9 = New System.Windows.Forms.GroupBox()
        Me.lOperateur = New System.Windows.Forms.Label()
        Me.Label29 = New System.Windows.Forms.Label()
        Me.cmbSituation = New C1.Win.C1List.C1Combo()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.GroupBox8 = New System.Windows.Forms.GroupBox()
        Me.lTestPCT = New System.Windows.Forms.Label()
        Me.tTarifReference = New C1.Win.C1Input.C1TextBox()
        Me.Label22 = New System.Windows.Forms.Label()
        Me.Label24 = New System.Windows.Forms.Label()
        Me.chkPriseEnCharge = New System.Windows.Forms.CheckBox()
        Me.tCodePCT = New C1.Win.C1Input.C1TextBox()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.Label21 = New System.Windows.Forms.Label()
        Me.chkAccordPrealable = New System.Windows.Forms.CheckBox()
        Me.cmbCategorieCnam = New C1.Win.C1List.C1Combo()
        Me.Label23 = New System.Windows.Forms.Label()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.Label38 = New System.Windows.Forms.Label()
        Me.CmbTVA = New C1.Win.C1List.C1Combo()
        Me.Label27 = New System.Windows.Forms.Label()
        Me.chkExonere = New System.Windows.Forms.CheckBox()
        Me.tPrixVenteTTC = New C1.Win.C1Input.C1TextBox()
        Me.tHR = New C1.Win.C1Input.C1TextBox()
        Me.tPrixVenteHT = New C1.Win.C1Input.C1TextBox()
        Me.tMarge = New C1.Win.C1Input.C1TextBox()
        Me.tPrixAchatTTC = New C1.Win.C1Input.C1TextBox()
        Me.tPrixAchatHT = New C1.Win.C1Input.C1TextBox()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.STOCK = New System.Windows.Forms.GroupBox()
        Me.lUnite = New System.Windows.Forms.Label()
        Me.lStock = New System.Windows.Forms.Label()
        Me.GroupBox6 = New System.Windows.Forms.GroupBox()
        Me.tQuantiteUnitaire = New C1.Win.C1Input.C1TextBox()
        Me.tFocus = New C1.Win.C1Input.C1TextBox()
        Me.dtpDerniereDateAchat = New C1.Win.C1Input.C1DateEdit()
        Me.lDerniereDateAchat = New System.Windows.Forms.Label()
        Me.tDernierPrixAchat = New C1.Win.C1Input.C1TextBox()
        Me.lQuantiteUnitaire = New System.Windows.Forms.Label()
        Me.tDateAlerte = New C1.Win.C1Input.C1TextBox()
        Me.Label30 = New System.Windows.Forms.Label()
        Me.tContenance = New C1.Win.C1Input.C1TextBox()
        Me.Label26 = New System.Windows.Forms.Label()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.tNumCirculaire = New C1.Win.C1Input.C1TextBox()
        Me.Label42 = New System.Windows.Forms.Label()
        Me.Label17 = New System.Windows.Forms.Label()
        Me.chkSansVignette = New System.Windows.Forms.CheckBox()
        Me.Label25 = New System.Windows.Forms.Label()
        Me.chkSansCodeAbarre = New System.Windows.Forms.CheckBox()
        Me.tRayon = New C1.Win.C1Input.C1TextBox()
        Me.lTest = New System.Windows.Forms.Label()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.tSection = New C1.Win.C1Input.C1TextBox()
        Me.cmbPreparation = New C1.Win.C1List.C1Combo()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.tDesignation = New C1.Win.C1Input.C1TextBox()
        Me.tCodeArticle = New C1.Win.C1Input.C1TextBox()
        Me.cmbTableau = New C1.Win.C1List.C1Combo()
        Me.LNomClient = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.cmbLaboratoire = New C1.Win.C1List.C1Combo()
        Me.tDosage = New C1.Win.C1Input.C1TextBox()
        Me.LNumeroClient = New System.Windows.Forms.Label()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.LCategorieArticle = New System.Windows.Forms.Label()
        Me.cmbCategorie = New C1.Win.C1List.C1Combo()
        Me.lLaboratoire = New System.Windows.Forms.Label()
        Me.LFormeArticle = New System.Windows.Forms.Label()
        Me.cmbForme = New C1.Win.C1List.C1Combo()
        Me.C1DockingTabPage2 = New C1.Win.C1Command.C1DockingTabPage()
        Me.GroupBox10 = New System.Windows.Forms.GroupBox()
        Me.chbFemmeEnceinte = New System.Windows.Forms.CheckBox()
        Me.cmbDCI3 = New C1.Win.C1List.C1Combo()
        Me.cmbDCI2 = New C1.Win.C1List.C1Combo()
        Me.cmbDCI1 = New C1.Win.C1List.C1Combo()
        Me.Label34 = New System.Windows.Forms.Label()
        Me.Label36 = New System.Windows.Forms.Label()
        Me.Label37 = New System.Windows.Forms.Label()
        Me.GroupBox7 = New System.Windows.Forms.GroupBox()
        Me.lDernierFournisseur = New System.Windows.Forms.Label()
        Me.lDateDernierAchat = New System.Windows.Forms.Label()
        Me.lDateInventaire = New System.Windows.Forms.Label()
        Me.lStockInventaire = New System.Windows.Forms.Label()
        Me.Label41 = New System.Windows.Forms.Label()
        Me.lQuantiteDernierCommande = New System.Windows.Forms.Label()
        Me.lDateDernierCommande = New System.Windows.Forms.Label()
        Me.Label40 = New System.Windows.Forms.Label()
        Me.Label28 = New System.Windows.Forms.Label()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.Label19 = New System.Windows.Forms.Label()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.C1DockingTabPage3 = New C1.Win.C1Command.C1DockingTabPage()
        Me.GroupBox11 = New System.Windows.Forms.GroupBox()
        Me.chbVoirLotsVides = New System.Windows.Forms.CheckBox()
        Me.grbAjoutLot = New System.Windows.Forms.GroupBox()
        Me.dtpDate = New C1.Win.C1Input.C1DateEdit()
        Me.bAjouterLot = New C1.Win.C1Input.C1Button()
        Me.tQuantiteLot = New C1.Win.C1Input.C1TextBox()
        Me.lQuantiteLot = New System.Windows.Forms.Label()
        Me.tNumeroLot = New C1.Win.C1Input.C1TextBox()
        Me.lNumeroLot = New System.Windows.Forms.Label()
        Me.lDatePeremption = New System.Windows.Forms.Label()
        Me.GroupBox12 = New System.Windows.Forms.GroupBox()
        Me.bValiderLots = New C1.Win.C1Input.C1Button()
        Me.tDesignationGL = New C1.Win.C1Input.C1TextBox()
        Me.tCodePCTGL = New C1.Win.C1Input.C1TextBox()
        Me.Label31 = New System.Windows.Forms.Label()
        Me.tCodeArticleGL = New C1.Win.C1Input.C1TextBox()
        Me.Label32 = New System.Windows.Forms.Label()
        Me.Label33 = New System.Windows.Forms.Label()
        Me.Label35 = New System.Windows.Forms.Label()
        Me.gLots = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.C1DockingTabPage4 = New C1.Win.C1Command.C1DockingTabPage()
        Me.Label39 = New System.Windows.Forms.Label()
        Me.gAchat = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Panel.SuspendLayout()
        CType(Me.Tab, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Tab.SuspendLayout()
        Me.C1DockingTabPage1.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        CType(Me.tstockAlert, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tQuantiteACommander, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox9.SuspendLayout()
        CType(Me.cmbSituation, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox8.SuspendLayout()
        CType(Me.tTarifReference, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodePCT, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbCategorieCnam, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox4.SuspendLayout()
        CType(Me.CmbTVA, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tPrixVenteTTC, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tHR, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tPrixVenteHT, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tMarge, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tPrixAchatTTC, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tPrixAchatHT, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.STOCK.SuspendLayout()
        Me.GroupBox6.SuspendLayout()
        CType(Me.tQuantiteUnitaire, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tFocus, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpDerniereDateAchat, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDernierPrixAchat, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDateAlerte, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tContenance, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox2.SuspendLayout()
        CType(Me.tNumCirculaire, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tRayon, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tSection, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbPreparation, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDesignation, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodeArticle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbTableau, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbLaboratoire, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDosage, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbCategorie, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbForme, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage2.SuspendLayout()
        Me.GroupBox10.SuspendLayout()
        CType(Me.cmbDCI3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbDCI2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbDCI1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox7.SuspendLayout()
        Me.C1DockingTabPage3.SuspendLayout()
        Me.GroupBox11.SuspendLayout()
        Me.grbAjoutLot.SuspendLayout()
        CType(Me.dtpDate, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tQuantiteLot, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNumeroLot, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox12.SuspendLayout()
        CType(Me.tDesignationGL, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodePCTGL, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodeArticleGL, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gLots, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage4.SuspendLayout()
        CType(Me.gAchat, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.bChangeQuantiteUnitaire)
        Me.Panel.Controls.Add(Me.bChangementPrix)
        Me.Panel.Controls.Add(Me.bStatistiqueArticle)
        Me.Panel.Controls.Add(Me.lNomArticle)
        Me.Panel.Controls.Add(Me.bNaviger)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.bConfirmer)
        Me.Panel.Controls.Add(Me.Tab)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1121, 590)
        Me.Panel.TabIndex = 2
        '
        'bChangeQuantiteUnitaire
        '
        Me.bChangeQuantiteUnitaire.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bChangeQuantiteUnitaire.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bChangeQuantiteUnitaire.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bChangeQuantiteUnitaire.Location = New System.Drawing.Point(124, 536)
        Me.bChangeQuantiteUnitaire.Name = "bChangeQuantiteUnitaire"
        Me.bChangeQuantiteUnitaire.Size = New System.Drawing.Size(114, 45)
        Me.bChangeQuantiteUnitaire.TabIndex = 50
        Me.bChangeQuantiteUnitaire.Text = "Changement de la Quantité unitaire"
        Me.bChangeQuantiteUnitaire.UseVisualStyleBackColor = True
        Me.bChangeQuantiteUnitaire.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bChangementPrix
        '
        Me.bChangementPrix.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bChangementPrix.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bChangementPrix.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bChangementPrix.Location = New System.Drawing.Point(244, 536)
        Me.bChangementPrix.Name = "bChangementPrix"
        Me.bChangementPrix.Size = New System.Drawing.Size(114, 45)
        Me.bChangementPrix.TabIndex = 49
        Me.bChangementPrix.Text = "Changement du prix"
        Me.bChangementPrix.UseVisualStyleBackColor = True
        Me.bChangementPrix.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bStatistiqueArticle
        '
        Me.bStatistiqueArticle.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bStatistiqueArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bStatistiqueArticle.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bStatistiqueArticle.Location = New System.Drawing.Point(364, 536)
        Me.bStatistiqueArticle.Name = "bStatistiqueArticle"
        Me.bStatistiqueArticle.Size = New System.Drawing.Size(114, 45)
        Me.bStatistiqueArticle.TabIndex = 48
        Me.bStatistiqueArticle.Text = "Statistique Article"
        Me.bStatistiqueArticle.UseVisualStyleBackColor = True
        Me.bStatistiqueArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lNomArticle
        '
        Me.lNomArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 26.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNomArticle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.lNomArticle.Location = New System.Drawing.Point(11, -1)
        Me.lNomArticle.Name = "lNomArticle"
        Me.lNomArticle.Size = New System.Drawing.Size(1109, 35)
        Me.lNomArticle.TabIndex = 47
        Me.lNomArticle.Text = "FICHE ARTICLE"
        Me.lNomArticle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bNaviger
        '
        Me.bNaviger.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bNaviger.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bNaviger.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bNaviger.Location = New System.Drawing.Point(4, 536)
        Me.bNaviger.Name = "bNaviger"
        Me.bNaviger.Size = New System.Drawing.Size(114, 45)
        Me.bNaviger.TabIndex = 10
        Me.bNaviger.Text = "Info / DCI / Lots        F2"
        Me.bNaviger.UseVisualStyleBackColor = True
        Me.bNaviger.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler21
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(994, 536)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(114, 45)
        Me.bAnnuler.TabIndex = 4
        Me.bAnnuler.Text = "Annuler              F10"
        Me.bAnnuler.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bConfirmer
        '
        Me.bConfirmer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bConfirmer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmer.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bConfirmer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmer.Location = New System.Drawing.Point(874, 536)
        Me.bConfirmer.Name = "bConfirmer"
        Me.bConfirmer.Size = New System.Drawing.Size(114, 45)
        Me.bConfirmer.TabIndex = 3
        Me.bConfirmer.Text = "Confirmer                 F3"
        Me.bConfirmer.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bConfirmer.UseVisualStyleBackColor = True
        Me.bConfirmer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Tab
        '
        Me.Tab.Controls.Add(Me.C1DockingTabPage1)
        Me.Tab.Controls.Add(Me.C1DockingTabPage2)
        Me.Tab.Controls.Add(Me.C1DockingTabPage3)
        Me.Tab.Controls.Add(Me.C1DockingTabPage4)
        Me.Tab.Location = New System.Drawing.Point(3, 37)
        Me.Tab.Name = "Tab"
        Me.Tab.SelectedIndex = 3
        Me.Tab.Size = New System.Drawing.Size(1105, 493)
        Me.Tab.TabIndex = 1
        Me.Tab.TabsSpacing = 5
        Me.Tab.TabStyle = C1.Win.C1Command.TabStyleEnum.Office2007
        Me.Tab.VisualStyle = C1.Win.C1Command.VisualStyle.Office2007Blue
        Me.Tab.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage1
        '
        Me.C1DockingTabPage1.Controls.Add(Me.GroupBox1)
        Me.C1DockingTabPage1.Controls.Add(Me.GroupBox9)
        Me.C1DockingTabPage1.Controls.Add(Me.GroupBox8)
        Me.C1DockingTabPage1.Controls.Add(Me.GroupBox4)
        Me.C1DockingTabPage1.Controls.Add(Me.STOCK)
        Me.C1DockingTabPage1.Controls.Add(Me.GroupBox6)
        Me.C1DockingTabPage1.Controls.Add(Me.GroupBox2)
        Me.C1DockingTabPage1.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage1.Name = "C1DockingTabPage1"
        Me.C1DockingTabPage1.Size = New System.Drawing.Size(1103, 468)
        Me.C1DockingTabPage1.TabIndex = 0
        Me.C1DockingTabPage1.Text = "Information Article"
        '
        'GroupBox1
        '
        Me.GroupBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GroupBox1.Controls.Add(Me.Label16)
        Me.GroupBox1.Controls.Add(Me.Label15)
        Me.GroupBox1.Controls.Add(Me.tstockAlert)
        Me.GroupBox1.Controls.Add(Me.tQuantiteACommander)
        Me.GroupBox1.Location = New System.Drawing.Point(690, 77)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(406, 95)
        Me.GroupBox1.TabIndex = 69
        Me.GroupBox1.TabStop = False
        '
        'Label16
        '
        Me.Label16.AutoSize = True
        Me.Label16.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label16.Location = New System.Drawing.Point(3, 54)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(171, 20)
        Me.Label16.TabIndex = 61
        Me.Label16.Text = "Quantité à commander"
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label15.Location = New System.Drawing.Point(3, 21)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(106, 20)
        Me.Label15.TabIndex = 59
        Me.Label15.Text = "Stock d'alerte"
        '
        'tstockAlert
        '
        Me.tstockAlert.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tstockAlert.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tstockAlert.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tstockAlert.Location = New System.Drawing.Point(199, 16)
        Me.tstockAlert.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tstockAlert.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tstockAlert.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tstockAlert.Name = "tstockAlert"
        Me.tstockAlert.Size = New System.Drawing.Size(195, 27)
        Me.tstockAlert.TabIndex = 3
        Me.tstockAlert.Tag = Nothing
        Me.tstockAlert.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tstockAlert.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tQuantiteACommander
        '
        Me.tQuantiteACommander.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tQuantiteACommander.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tQuantiteACommander.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tQuantiteACommander.Location = New System.Drawing.Point(199, 49)
        Me.tQuantiteACommander.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tQuantiteACommander.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tQuantiteACommander.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tQuantiteACommander.Name = "tQuantiteACommander"
        Me.tQuantiteACommander.Size = New System.Drawing.Size(195, 27)
        Me.tQuantiteACommander.TabIndex = 5
        Me.tQuantiteACommander.Tag = Nothing
        Me.tQuantiteACommander.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tQuantiteACommander.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox9
        '
        Me.GroupBox9.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GroupBox9.Controls.Add(Me.lOperateur)
        Me.GroupBox9.Controls.Add(Me.Label29)
        Me.GroupBox9.Controls.Add(Me.cmbSituation)
        Me.GroupBox9.Controls.Add(Me.Label13)
        Me.GroupBox9.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox9.Location = New System.Drawing.Point(690, 358)
        Me.GroupBox9.Name = "GroupBox9"
        Me.GroupBox9.Size = New System.Drawing.Size(406, 103)
        Me.GroupBox9.TabIndex = 63
        Me.GroupBox9.TabStop = False
        '
        'lOperateur
        '
        Me.lOperateur.AutoSize = True
        Me.lOperateur.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lOperateur.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lOperateur.Location = New System.Drawing.Point(202, 65)
        Me.lOperateur.Name = "lOperateur"
        Me.lOperateur.Size = New System.Drawing.Size(16, 22)
        Me.lOperateur.TabIndex = 57
        Me.lOperateur.Text = "-"
        '
        'Label29
        '
        Me.Label29.AutoSize = True
        Me.Label29.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label29.Location = New System.Drawing.Point(6, 68)
        Me.Label29.Name = "Label29"
        Me.Label29.Size = New System.Drawing.Size(81, 20)
        Me.Label29.TabIndex = 56
        Me.Label29.Text = "Opérateur"
        '
        'cmbSituation
        '
        Me.cmbSituation.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbSituation.Caption = ""
        Me.cmbSituation.CaptionHeight = 17
        Me.cmbSituation.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbSituation.ColumnCaptionHeight = 17
        Me.cmbSituation.ColumnFooterHeight = 17
        Me.cmbSituation.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.cmbSituation.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbSituation.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbSituation.EditorFont = New System.Drawing.Font("Verdana", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbSituation.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbSituation.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbSituation.Images.Add(CType(resources.GetObject("cmbSituation.Images"), System.Drawing.Image))
        Me.cmbSituation.ItemHeight = 22
        Me.cmbSituation.Location = New System.Drawing.Point(202, 14)
        Me.cmbSituation.MatchEntryTimeout = CType(2000, Long)
        Me.cmbSituation.MaxDropDownItems = CType(5, Short)
        Me.cmbSituation.MaxLength = 32767
        Me.cmbSituation.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbSituation.Name = "cmbSituation"
        Me.cmbSituation.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbSituation.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbSituation.Size = New System.Drawing.Size(195, 28)
        Me.cmbSituation.TabIndex = 0
        Me.cmbSituation.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbSituation.PropBag = resources.GetString("cmbSituation.PropBag")
        '
        'Label13
        '
        Me.Label13.AutoSize = True
        Me.Label13.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label13.Location = New System.Drawing.Point(6, 14)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(72, 20)
        Me.Label13.TabIndex = 54
        Me.Label13.Text = "Situation"
        '
        'GroupBox8
        '
        Me.GroupBox8.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.GroupBox8.Controls.Add(Me.lTestPCT)
        Me.GroupBox8.Controls.Add(Me.tTarifReference)
        Me.GroupBox8.Controls.Add(Me.Label22)
        Me.GroupBox8.Controls.Add(Me.Label24)
        Me.GroupBox8.Controls.Add(Me.chkPriseEnCharge)
        Me.GroupBox8.Controls.Add(Me.tCodePCT)
        Me.GroupBox8.Controls.Add(Me.Label9)
        Me.GroupBox8.Controls.Add(Me.Label21)
        Me.GroupBox8.Controls.Add(Me.chkAccordPrealable)
        Me.GroupBox8.Controls.Add(Me.cmbCategorieCnam)
        Me.GroupBox8.Controls.Add(Me.Label23)
        Me.GroupBox8.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox8.Location = New System.Drawing.Point(10, 208)
        Me.GroupBox8.Name = "GroupBox8"
        Me.GroupBox8.Size = New System.Drawing.Size(668, 122)
        Me.GroupBox8.TabIndex = 62
        Me.GroupBox8.TabStop = False
        Me.GroupBox8.Text = "CNAM"
        '
        'lTestPCT
        '
        Me.lTestPCT.BackColor = System.Drawing.Color.Transparent
        Me.lTestPCT.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTestPCT.ForeColor = System.Drawing.Color.Black
        Me.lTestPCT.Location = New System.Drawing.Point(391, 19)
        Me.lTestPCT.Name = "lTestPCT"
        Me.lTestPCT.Size = New System.Drawing.Size(38, 20)
        Me.lTestPCT.TabIndex = 2
        Me.lTestPCT.Text = "55"
        Me.lTestPCT.Visible = False
        '
        'tTarifReference
        '
        Me.tTarifReference.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tTarifReference.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTarifReference.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tTarifReference.Location = New System.Drawing.Point(190, 82)
        Me.tTarifReference.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tTarifReference.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tTarifReference.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tTarifReference.Name = "tTarifReference"
        Me.tTarifReference.Size = New System.Drawing.Size(197, 27)
        Me.tTarifReference.TabIndex = 4
        Me.tTarifReference.Tag = Nothing
        Me.tTarifReference.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tTarifReference.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label22
        '
        Me.Label22.AutoSize = True
        Me.Label22.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label22.Location = New System.Drawing.Point(9, 87)
        Me.Label22.Name = "Label22"
        Me.Label22.Size = New System.Drawing.Size(146, 20)
        Me.Label22.TabIndex = 50
        Me.Label22.Text = "Tarif de références "
        '
        'Label24
        '
        Me.Label24.AutoSize = True
        Me.Label24.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label24.Location = New System.Drawing.Point(416, 49)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(123, 20)
        Me.Label24.TabIndex = 59
        Me.Label24.Text = "Prise en charge "
        '
        'chkPriseEnCharge
        '
        Me.chkPriseEnCharge.AutoSize = True
        Me.chkPriseEnCharge.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chkPriseEnCharge.Location = New System.Drawing.Point(550, 54)
        Me.chkPriseEnCharge.Name = "chkPriseEnCharge"
        Me.chkPriseEnCharge.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkPriseEnCharge.Size = New System.Drawing.Size(15, 14)
        Me.chkPriseEnCharge.TabIndex = 3
        Me.chkPriseEnCharge.UseVisualStyleBackColor = True
        '
        'tCodePCT
        '
        Me.tCodePCT.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodePCT.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodePCT.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodePCT.Location = New System.Drawing.Point(190, 14)
        Me.tCodePCT.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tCodePCT.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tCodePCT.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tCodePCT.Name = "tCodePCT"
        Me.tCodePCT.Size = New System.Drawing.Size(195, 27)
        Me.tCodePCT.TabIndex = 0
        Me.tCodePCT.Tag = Nothing
        Me.tCodePCT.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodePCT.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.Location = New System.Drawing.Point(6, 19)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(81, 20)
        Me.Label9.TabIndex = 57
        Me.Label9.Text = "Code PCT"
        '
        'Label21
        '
        Me.Label21.AutoSize = True
        Me.Label21.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label21.Location = New System.Drawing.Point(416, 85)
        Me.Label21.Name = "Label21"
        Me.Label21.Size = New System.Drawing.Size(128, 20)
        Me.Label21.TabIndex = 55
        Me.Label21.Text = "Accord préalable"
        '
        'chkAccordPrealable
        '
        Me.chkAccordPrealable.AutoSize = True
        Me.chkAccordPrealable.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chkAccordPrealable.Location = New System.Drawing.Point(550, 90)
        Me.chkAccordPrealable.Name = "chkAccordPrealable"
        Me.chkAccordPrealable.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkAccordPrealable.Size = New System.Drawing.Size(15, 14)
        Me.chkAccordPrealable.TabIndex = 2
        Me.chkAccordPrealable.UseVisualStyleBackColor = True
        '
        'cmbCategorieCnam
        '
        Me.cmbCategorieCnam.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbCategorieCnam.Caption = ""
        Me.cmbCategorieCnam.CaptionHeight = 17
        Me.cmbCategorieCnam.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbCategorieCnam.ColumnCaptionHeight = 17
        Me.cmbCategorieCnam.ColumnFooterHeight = 17
        Me.cmbCategorieCnam.ColumnWidth = 100
        Me.cmbCategorieCnam.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.cmbCategorieCnam.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbCategorieCnam.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown
        Me.cmbCategorieCnam.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbCategorieCnam.EditorFont = New System.Drawing.Font("Verdana", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbCategorieCnam.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbCategorieCnam.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbCategorieCnam.Images.Add(CType(resources.GetObject("cmbCategorieCnam.Images"), System.Drawing.Image))
        Me.cmbCategorieCnam.ItemHeight = 15
        Me.cmbCategorieCnam.Location = New System.Drawing.Point(190, 49)
        Me.cmbCategorieCnam.MatchEntryTimeout = CType(2000, Long)
        Me.cmbCategorieCnam.MaxDropDownItems = CType(5, Short)
        Me.cmbCategorieCnam.MaxLength = 32767
        Me.cmbCategorieCnam.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbCategorieCnam.Name = "cmbCategorieCnam"
        Me.cmbCategorieCnam.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbCategorieCnam.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbCategorieCnam.Size = New System.Drawing.Size(197, 28)
        Me.cmbCategorieCnam.TabIndex = 1
        Me.cmbCategorieCnam.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbCategorieCnam.PropBag = resources.GetString("cmbCategorieCnam.PropBag")
        '
        'Label23
        '
        Me.Label23.AutoSize = True
        Me.Label23.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label23.Location = New System.Drawing.Point(9, 54)
        Me.Label23.Name = "Label23"
        Me.Label23.Size = New System.Drawing.Size(128, 20)
        Me.Label23.TabIndex = 35
        Me.Label23.Text = "Catégorie CNAM"
        '
        'GroupBox4
        '
        Me.GroupBox4.BackColor = System.Drawing.Color.FromArgb(CType(CType(192, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GroupBox4.Controls.Add(Me.Label38)
        Me.GroupBox4.Controls.Add(Me.CmbTVA)
        Me.GroupBox4.Controls.Add(Me.Label27)
        Me.GroupBox4.Controls.Add(Me.chkExonere)
        Me.GroupBox4.Controls.Add(Me.tPrixVenteTTC)
        Me.GroupBox4.Controls.Add(Me.tHR)
        Me.GroupBox4.Controls.Add(Me.tPrixVenteHT)
        Me.GroupBox4.Controls.Add(Me.tMarge)
        Me.GroupBox4.Controls.Add(Me.tPrixAchatTTC)
        Me.GroupBox4.Controls.Add(Me.tPrixAchatHT)
        Me.GroupBox4.Controls.Add(Me.Label7)
        Me.GroupBox4.Controls.Add(Me.Label6)
        Me.GroupBox4.Controls.Add(Me.Label5)
        Me.GroupBox4.Controls.Add(Me.Label4)
        Me.GroupBox4.Controls.Add(Me.Label3)
        Me.GroupBox4.Controls.Add(Me.Label2)
        Me.GroupBox4.Controls.Add(Me.Label1)
        Me.GroupBox4.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox4.Location = New System.Drawing.Point(10, 336)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(674, 127)
        Me.GroupBox4.TabIndex = 67
        Me.GroupBox4.TabStop = False
        Me.GroupBox4.Text = "Prix"
        '
        'Label38
        '
        Me.Label38.AutoSize = True
        Me.Label38.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label38.Location = New System.Drawing.Point(364, 21)
        Me.Label38.Name = "Label38"
        Me.Label38.Size = New System.Drawing.Size(23, 20)
        Me.Label38.TabIndex = 68
        Me.Label38.Text = "%"
        '
        'CmbTVA
        '
        Me.CmbTVA.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.CmbTVA.Caption = ""
        Me.CmbTVA.CaptionHeight = 17
        Me.CmbTVA.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.CmbTVA.ColumnCaptionHeight = 17
        Me.CmbTVA.ColumnFooterHeight = 17
        Me.CmbTVA.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.CmbTVA.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown
        Me.CmbTVA.EditorBackColor = System.Drawing.SystemColors.Window
        Me.CmbTVA.EditorFont = New System.Drawing.Font("Verdana", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CmbTVA.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.CmbTVA.Enabled = False
        Me.CmbTVA.FetchRowStyles = True
        Me.CmbTVA.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CmbTVA.Images.Add(CType(resources.GetObject("CmbTVA.Images"), System.Drawing.Image))
        Me.CmbTVA.ItemHeight = 20
        Me.CmbTVA.Location = New System.Drawing.Point(293, 16)
        Me.CmbTVA.MatchEntryTimeout = CType(2000, Long)
        Me.CmbTVA.MaxDropDownItems = CType(5, Short)
        Me.CmbTVA.MaxLength = 32767
        Me.CmbTVA.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.CmbTVA.Name = "CmbTVA"
        Me.CmbTVA.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.CmbTVA.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.CmbTVA.Size = New System.Drawing.Size(65, 28)
        Me.CmbTVA.TabIndex = 67
        Me.CmbTVA.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.CmbTVA.PropBag = resources.GetString("CmbTVA.PropBag")
        '
        'Label27
        '
        Me.Label27.AutoSize = True
        Me.Label27.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label27.Location = New System.Drawing.Point(248, 54)
        Me.Label27.Name = "Label27"
        Me.Label27.Size = New System.Drawing.Size(197, 20)
        Me.Label27.TabIndex = 4
        Me.Label27.Text = "Exonéré de TVA à la vente"
        '
        'chkExonere
        '
        Me.chkExonere.AutoSize = True
        Me.chkExonere.Enabled = False
        Me.chkExonere.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chkExonere.Location = New System.Drawing.Point(451, 59)
        Me.chkExonere.Name = "chkExonere"
        Me.chkExonere.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkExonere.Size = New System.Drawing.Size(15, 14)
        Me.chkExonere.TabIndex = 60
        Me.chkExonere.UseVisualStyleBackColor = True
        '
        'tPrixVenteTTC
        '
        Me.tPrixVenteTTC.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tPrixVenteTTC.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tPrixVenteTTC.Enabled = False
        Me.tPrixVenteTTC.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tPrixVenteTTC.Location = New System.Drawing.Point(535, 82)
        Me.tPrixVenteTTC.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tPrixVenteTTC.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tPrixVenteTTC.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tPrixVenteTTC.Name = "tPrixVenteTTC"
        Me.tPrixVenteTTC.Size = New System.Drawing.Size(113, 27)
        Me.tPrixVenteTTC.TabIndex = 7
        Me.tPrixVenteTTC.Tag = Nothing
        Me.tPrixVenteTTC.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tPrixVenteTTC.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tHR
        '
        Me.tHR.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tHR.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tHR.Enabled = False
        Me.tHR.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tHR.Location = New System.Drawing.Point(286, 82)
        Me.tHR.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tHR.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tHR.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tHR.Name = "tHR"
        Me.tHR.Size = New System.Drawing.Size(113, 27)
        Me.tHR.TabIndex = 6
        Me.tHR.Tag = Nothing
        Me.tHR.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tHR.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tPrixVenteHT
        '
        Me.tPrixVenteHT.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tPrixVenteHT.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tPrixVenteHT.Enabled = False
        Me.tPrixVenteHT.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tPrixVenteHT.Location = New System.Drawing.Point(120, 82)
        Me.tPrixVenteHT.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tPrixVenteHT.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tPrixVenteHT.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tPrixVenteHT.Name = "tPrixVenteHT"
        Me.tPrixVenteHT.Size = New System.Drawing.Size(115, 27)
        Me.tPrixVenteHT.TabIndex = 5
        Me.tPrixVenteHT.Tag = Nothing
        Me.tPrixVenteHT.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tPrixVenteHT.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tMarge
        '
        Me.tMarge.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tMarge.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMarge.Enabled = False
        Me.tMarge.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tMarge.Location = New System.Drawing.Point(120, 49)
        Me.tMarge.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tMarge.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tMarge.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tMarge.Name = "tMarge"
        Me.tMarge.Size = New System.Drawing.Size(115, 27)
        Me.tMarge.TabIndex = 3
        Me.tMarge.Tag = Nothing
        Me.tMarge.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tMarge.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tPrixAchatTTC
        '
        Me.tPrixAchatTTC.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tPrixAchatTTC.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tPrixAchatTTC.Enabled = False
        Me.tPrixAchatTTC.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tPrixAchatTTC.Location = New System.Drawing.Point(535, 16)
        Me.tPrixAchatTTC.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tPrixAchatTTC.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tPrixAchatTTC.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tPrixAchatTTC.Name = "tPrixAchatTTC"
        Me.tPrixAchatTTC.Size = New System.Drawing.Size(113, 27)
        Me.tPrixAchatTTC.TabIndex = 2
        Me.tPrixAchatTTC.Tag = Nothing
        Me.tPrixAchatTTC.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tPrixAchatTTC.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tPrixAchatHT
        '
        Me.tPrixAchatHT.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tPrixAchatHT.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tPrixAchatHT.Enabled = False
        Me.tPrixAchatHT.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tPrixAchatHT.Location = New System.Drawing.Point(120, 16)
        Me.tPrixAchatHT.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tPrixAchatHT.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tPrixAchatHT.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tPrixAchatHT.Name = "tPrixAchatHT"
        Me.tPrixAchatHT.Size = New System.Drawing.Size(115, 27)
        Me.tPrixAchatHT.TabIndex = 0
        Me.tPrixAchatHT.Tag = Nothing
        Me.tPrixAchatHT.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tPrixAchatHT.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.Location = New System.Drawing.Point(248, 87)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(33, 20)
        Me.Label7.TabIndex = 40
        Me.Label7.Text = "HR"
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.Location = New System.Drawing.Point(9, 54)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(80, 20)
        Me.Label6.TabIndex = 38
        Me.Label6.Text = "Marge   %"
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.Location = New System.Drawing.Point(248, 21)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(40, 20)
        Me.Label5.TabIndex = 37
        Me.Label5.Text = "TVA"
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.Location = New System.Drawing.Point(416, 21)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(113, 20)
        Me.Label4.TabIndex = 36
        Me.Label4.Text = "Prix Achat TTC"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.Location = New System.Drawing.Point(415, 87)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(114, 20)
        Me.Label3.TabIndex = 35
        Me.Label3.Text = "Prix Vente TTC"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(9, 87)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(106, 20)
        Me.Label2.TabIndex = 34
        Me.Label2.Text = "Prix Vente HT"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(9, 21)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(105, 20)
        Me.Label1.TabIndex = 33
        Me.Label1.Text = "Prix Achat HT"
        '
        'STOCK
        '
        Me.STOCK.Controls.Add(Me.lUnite)
        Me.STOCK.Controls.Add(Me.lStock)
        Me.STOCK.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.STOCK.Location = New System.Drawing.Point(687, 8)
        Me.STOCK.Name = "STOCK"
        Me.STOCK.Size = New System.Drawing.Size(409, 63)
        Me.STOCK.TabIndex = 68
        Me.STOCK.TabStop = False
        Me.STOCK.Text = "Stock"
        '
        'lUnite
        '
        Me.lUnite.BackColor = System.Drawing.Color.Transparent
        Me.lUnite.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lUnite.ForeColor = System.Drawing.Color.Black
        Me.lUnite.Location = New System.Drawing.Point(176, 22)
        Me.lUnite.Name = "lUnite"
        Me.lUnite.Size = New System.Drawing.Size(105, 28)
        Me.lUnite.TabIndex = 2
        Me.lUnite.Text = "Unités"
        Me.lUnite.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lUnite.Visible = False
        '
        'lStock
        '
        Me.lStock.Font = New System.Drawing.Font("Microsoft Sans Serif", 21.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lStock.ForeColor = System.Drawing.Color.Blue
        Me.lStock.Location = New System.Drawing.Point(8, 17)
        Me.lStock.Name = "lStock"
        Me.lStock.Size = New System.Drawing.Size(163, 36)
        Me.lStock.TabIndex = 61
        Me.lStock.Text = "-"
        Me.lStock.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'GroupBox6
        '
        Me.GroupBox6.Controls.Add(Me.tQuantiteUnitaire)
        Me.GroupBox6.Controls.Add(Me.tFocus)
        Me.GroupBox6.Controls.Add(Me.dtpDerniereDateAchat)
        Me.GroupBox6.Controls.Add(Me.lDerniereDateAchat)
        Me.GroupBox6.Controls.Add(Me.tDernierPrixAchat)
        Me.GroupBox6.Controls.Add(Me.lQuantiteUnitaire)
        Me.GroupBox6.Controls.Add(Me.tDateAlerte)
        Me.GroupBox6.Controls.Add(Me.Label30)
        Me.GroupBox6.Controls.Add(Me.tContenance)
        Me.GroupBox6.Controls.Add(Me.Label26)
        Me.GroupBox6.Controls.Add(Me.Label14)
        Me.GroupBox6.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox6.Location = New System.Drawing.Point(690, 173)
        Me.GroupBox6.Name = "GroupBox6"
        Me.GroupBox6.Size = New System.Drawing.Size(406, 181)
        Me.GroupBox6.TabIndex = 0
        Me.GroupBox6.TabStop = False
        Me.GroupBox6.Text = "Quantité"
        '
        'tQuantiteUnitaire
        '
        Me.tQuantiteUnitaire.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tQuantiteUnitaire.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tQuantiteUnitaire.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tQuantiteUnitaire.Location = New System.Drawing.Point(202, 16)
        Me.tQuantiteUnitaire.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tQuantiteUnitaire.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tQuantiteUnitaire.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tQuantiteUnitaire.Name = "tQuantiteUnitaire"
        Me.tQuantiteUnitaire.Size = New System.Drawing.Size(195, 27)
        Me.tQuantiteUnitaire.TabIndex = 1
        Me.tQuantiteUnitaire.Tag = Nothing
        Me.tQuantiteUnitaire.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tQuantiteUnitaire.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tFocus
        '
        Me.tFocus.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tFocus.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tFocus.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tFocus.Location = New System.Drawing.Point(210, 16)
        Me.tFocus.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tFocus.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tFocus.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tFocus.Name = "tFocus"
        Me.tFocus.Size = New System.Drawing.Size(40, 27)
        Me.tFocus.TabIndex = 0
        Me.tFocus.Tag = Nothing
        Me.tFocus.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tFocus.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'dtpDerniereDateAchat
        '
        Me.dtpDerniereDateAchat.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDerniereDateAchat.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDerniereDateAchat.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDerniereDateAchat.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDerniereDateAchat.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDerniereDateAchat.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.dtpDerniereDateAchat.Location = New System.Drawing.Point(202, 148)
        Me.dtpDerniereDateAchat.Name = "dtpDerniereDateAchat"
        Me.dtpDerniereDateAchat.Size = New System.Drawing.Size(195, 27)
        Me.dtpDerniereDateAchat.TabIndex = 7
        Me.dtpDerniereDateAchat.Tag = Nothing
        Me.dtpDerniereDateAchat.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDerniereDateAchat.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lDerniereDateAchat
        '
        Me.lDerniereDateAchat.AutoSize = True
        Me.lDerniereDateAchat.BackColor = System.Drawing.Color.Transparent
        Me.lDerniereDateAchat.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDerniereDateAchat.Location = New System.Drawing.Point(6, 153)
        Me.lDerniereDateAchat.Name = "lDerniereDateAchat"
        Me.lDerniereDateAchat.Size = New System.Drawing.Size(162, 20)
        Me.lDerniereDateAchat.TabIndex = 69
        Me.lDerniereDateAchat.Text = "Dernière date d'achat"
        Me.lDerniereDateAchat.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tDernierPrixAchat
        '
        Me.tDernierPrixAchat.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tDernierPrixAchat.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDernierPrixAchat.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDernierPrixAchat.Location = New System.Drawing.Point(202, 115)
        Me.tDernierPrixAchat.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tDernierPrixAchat.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tDernierPrixAchat.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tDernierPrixAchat.Name = "tDernierPrixAchat"
        Me.tDernierPrixAchat.Size = New System.Drawing.Size(195, 27)
        Me.tDernierPrixAchat.TabIndex = 6
        Me.tDernierPrixAchat.Tag = Nothing
        Me.tDernierPrixAchat.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDernierPrixAchat.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lQuantiteUnitaire
        '
        Me.lQuantiteUnitaire.AutoSize = True
        Me.lQuantiteUnitaire.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lQuantiteUnitaire.Location = New System.Drawing.Point(6, 120)
        Me.lQuantiteUnitaire.Name = "lQuantiteUnitaire"
        Me.lQuantiteUnitaire.Size = New System.Drawing.Size(145, 20)
        Me.lQuantiteUnitaire.TabIndex = 67
        Me.lQuantiteUnitaire.Text = "Dernier prix d'achat"
        '
        'tDateAlerte
        '
        Me.tDateAlerte.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tDateAlerte.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDateAlerte.Enabled = False
        Me.tDateAlerte.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDateAlerte.Location = New System.Drawing.Point(202, 82)
        Me.tDateAlerte.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tDateAlerte.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tDateAlerte.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tDateAlerte.Name = "tDateAlerte"
        Me.tDateAlerte.Size = New System.Drawing.Size(195, 27)
        Me.tDateAlerte.TabIndex = 4
        Me.tDateAlerte.Tag = Nothing
        Me.tDateAlerte.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDateAlerte.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label30
        '
        Me.Label30.AutoSize = True
        Me.Label30.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label30.Location = New System.Drawing.Point(6, 89)
        Me.Label30.Name = "Label30"
        Me.Label30.Size = New System.Drawing.Size(100, 20)
        Me.Label30.TabIndex = 65
        Me.Label30.Text = "Date d'alerte"
        '
        'tContenance
        '
        Me.tContenance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tContenance.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tContenance.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tContenance.Location = New System.Drawing.Point(202, 49)
        Me.tContenance.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tContenance.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tContenance.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tContenance.Name = "tContenance"
        Me.tContenance.Size = New System.Drawing.Size(195, 27)
        Me.tContenance.TabIndex = 2
        Me.tContenance.Tag = Nothing
        Me.tContenance.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tContenance.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label26
        '
        Me.Label26.AutoSize = True
        Me.Label26.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label26.Location = New System.Drawing.Point(6, 54)
        Me.Label26.Name = "Label26"
        Me.Label26.Size = New System.Drawing.Size(96, 20)
        Me.Label26.TabIndex = 63
        Me.Label26.Text = "Contenance"
        '
        'Label14
        '
        Me.Label14.AutoSize = True
        Me.Label14.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label14.Location = New System.Drawing.Point(6, 21)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(126, 20)
        Me.Label14.TabIndex = 55
        Me.Label14.Text = "Quantité unitaire"
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.tNumCirculaire)
        Me.GroupBox2.Controls.Add(Me.Label42)
        Me.GroupBox2.Controls.Add(Me.Label17)
        Me.GroupBox2.Controls.Add(Me.chkSansVignette)
        Me.GroupBox2.Controls.Add(Me.Label25)
        Me.GroupBox2.Controls.Add(Me.chkSansCodeAbarre)
        Me.GroupBox2.Controls.Add(Me.tRayon)
        Me.GroupBox2.Controls.Add(Me.lTest)
        Me.GroupBox2.Controls.Add(Me.Label12)
        Me.GroupBox2.Controls.Add(Me.tSection)
        Me.GroupBox2.Controls.Add(Me.cmbPreparation)
        Me.GroupBox2.Controls.Add(Me.Label10)
        Me.GroupBox2.Controls.Add(Me.tDesignation)
        Me.GroupBox2.Controls.Add(Me.tCodeArticle)
        Me.GroupBox2.Controls.Add(Me.cmbTableau)
        Me.GroupBox2.Controls.Add(Me.LNomClient)
        Me.GroupBox2.Controls.Add(Me.Label8)
        Me.GroupBox2.Controls.Add(Me.cmbLaboratoire)
        Me.GroupBox2.Controls.Add(Me.tDosage)
        Me.GroupBox2.Controls.Add(Me.LNumeroClient)
        Me.GroupBox2.Controls.Add(Me.Label11)
        Me.GroupBox2.Controls.Add(Me.LCategorieArticle)
        Me.GroupBox2.Controls.Add(Me.cmbCategorie)
        Me.GroupBox2.Controls.Add(Me.lLaboratoire)
        Me.GroupBox2.Controls.Add(Me.LFormeArticle)
        Me.GroupBox2.Controls.Add(Me.cmbForme)
        Me.GroupBox2.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox2.Location = New System.Drawing.Point(10, 8)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(674, 194)
        Me.GroupBox2.TabIndex = 60
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "Informations article"
        '
        'tNumCirculaire
        '
        Me.tNumCirculaire.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNumCirculaire.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNumCirculaire.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNumCirculaire.Location = New System.Drawing.Point(544, 161)
        Me.tNumCirculaire.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tNumCirculaire.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tNumCirculaire.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tNumCirculaire.Name = "tNumCirculaire"
        Me.tNumCirculaire.Size = New System.Drawing.Size(124, 27)
        Me.tNumCirculaire.TabIndex = 68
        Me.tNumCirculaire.Tag = Nothing
        Me.tNumCirculaire.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNumCirculaire.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label42
        '
        Me.Label42.AutoSize = True
        Me.Label42.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label42.Location = New System.Drawing.Point(419, 165)
        Me.Label42.Name = "Label42"
        Me.Label42.Size = New System.Drawing.Size(119, 20)
        Me.Label42.TabIndex = 67
        Me.Label42.Text = "Num. Circulaire "
        '
        'Label17
        '
        Me.Label17.AutoSize = True
        Me.Label17.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label17.Location = New System.Drawing.Point(261, 171)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(110, 20)
        Me.Label17.TabIndex = 55
        Me.Label17.Text = "Sans Vignette"
        '
        'chkSansVignette
        '
        Me.chkSansVignette.AutoSize = True
        Me.chkSansVignette.Location = New System.Drawing.Point(377, 175)
        Me.chkSansVignette.Name = "chkSansVignette"
        Me.chkSansVignette.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkSansVignette.Size = New System.Drawing.Size(15, 14)
        Me.chkSansVignette.TabIndex = 0
        Me.chkSansVignette.UseVisualStyleBackColor = True
        '
        'Label25
        '
        Me.Label25.AutoSize = True
        Me.Label25.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label25.Location = New System.Drawing.Point(6, 171)
        Me.Label25.Name = "Label25"
        Me.Label25.Size = New System.Drawing.Size(142, 20)
        Me.Label25.TabIndex = 57
        Me.Label25.Text = "Sans Code à barre"
        '
        'chkSansCodeAbarre
        '
        Me.chkSansCodeAbarre.AutoSize = True
        Me.chkSansCodeAbarre.Location = New System.Drawing.Point(154, 175)
        Me.chkSansCodeAbarre.Name = "chkSansCodeAbarre"
        Me.chkSansCodeAbarre.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkSansCodeAbarre.Size = New System.Drawing.Size(15, 14)
        Me.chkSansCodeAbarre.TabIndex = 1
        Me.chkSansCodeAbarre.UseVisualStyleBackColor = True
        '
        'tRayon
        '
        Me.tRayon.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tRayon.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tRayon.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tRayon.Location = New System.Drawing.Point(544, 131)
        Me.tRayon.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tRayon.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tRayon.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tRayon.Name = "tRayon"
        Me.tRayon.Size = New System.Drawing.Size(124, 27)
        Me.tRayon.TabIndex = 66
        Me.tRayon.Tag = Nothing
        Me.tRayon.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tRayon.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lTest
        '
        Me.lTest.AutoSize = True
        Me.lTest.BackColor = System.Drawing.Color.Transparent
        Me.lTest.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTest.ForeColor = System.Drawing.Color.Black
        Me.lTest.Location = New System.Drawing.Point(74, 50)
        Me.lTest.Name = "lTest"
        Me.lTest.Size = New System.Drawing.Size(19, 13)
        Me.lTest.TabIndex = 1
        Me.lTest.Text = "55"
        Me.lTest.Visible = False
        '
        'Label12
        '
        Me.Label12.AutoSize = True
        Me.Label12.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label12.Location = New System.Drawing.Point(483, 136)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(55, 20)
        Me.Label12.TabIndex = 52
        Me.Label12.Text = "Rayon"
        '
        'tSection
        '
        Me.tSection.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tSection.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tSection.DataType = GetType(Integer)
        Me.tSection.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tSection.Location = New System.Drawing.Point(544, 97)
        Me.tSection.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tSection.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tSection.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tSection.Name = "tSection"
        Me.tSection.Size = New System.Drawing.Size(124, 27)
        Me.tSection.TabIndex = 53
        Me.tSection.Tag = Nothing
        Me.tSection.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tSection.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbPreparation
        '
        Me.cmbPreparation.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbPreparation.Caption = ""
        Me.cmbPreparation.CaptionHeight = 17
        Me.cmbPreparation.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbPreparation.ColumnCaptionHeight = 17
        Me.cmbPreparation.ColumnFooterHeight = 17
        Me.cmbPreparation.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbPreparation.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown
        Me.cmbPreparation.DropDownWidth = 400
        Me.cmbPreparation.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbPreparation.EditorFont = New System.Drawing.Font("Verdana", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbPreparation.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbPreparation.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbPreparation.Images.Add(CType(resources.GetObject("cmbPreparation.Images"), System.Drawing.Image))
        Me.cmbPreparation.ItemHeight = 22
        Me.cmbPreparation.Location = New System.Drawing.Point(265, 94)
        Me.cmbPreparation.MatchEntryTimeout = CType(2000, Long)
        Me.cmbPreparation.MaxDropDownItems = CType(5, Short)
        Me.cmbPreparation.MaxLength = 32767
        Me.cmbPreparation.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbPreparation.Name = "cmbPreparation"
        Me.cmbPreparation.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbPreparation.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbPreparation.Size = New System.Drawing.Size(198, 32)
        Me.cmbPreparation.TabIndex = 65
        Me.cmbPreparation.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbPreparation.PropBag = resources.GetString("cmbPreparation.PropBag")
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label10.Location = New System.Drawing.Point(475, 102)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(63, 20)
        Me.Label10.TabIndex = 54
        Me.Label10.Text = "Section"
        '
        'tDesignation
        '
        Me.tDesignation.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tDesignation.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDesignation.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDesignation.Location = New System.Drawing.Point(265, 20)
        Me.tDesignation.Name = "tDesignation"
        Me.tDesignation.Size = New System.Drawing.Size(403, 27)
        Me.tDesignation.TabIndex = 1
        Me.tDesignation.Tag = Nothing
        Me.tDesignation.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDesignation.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tCodeArticle
        '
        Me.tCodeArticle.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodeArticle.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodeArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodeArticle.Location = New System.Drawing.Point(71, 20)
        Me.tCodeArticle.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tCodeArticle.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tCodeArticle.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tCodeArticle.MaxLength = 25
        Me.tCodeArticle.Name = "tCodeArticle"
        Me.tCodeArticle.Size = New System.Drawing.Size(188, 27)
        Me.tCodeArticle.TabIndex = 0
        Me.tCodeArticle.Tag = Nothing
        Me.tCodeArticle.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodeArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbTableau
        '
        Me.cmbTableau.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbTableau.Caption = ""
        Me.cmbTableau.CaptionHeight = 17
        Me.cmbTableau.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbTableau.ColumnCaptionHeight = 17
        Me.cmbTableau.ColumnFooterHeight = 17
        Me.cmbTableau.ColumnWidth = 100
        Me.cmbTableau.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.cmbTableau.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbTableau.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown
        Me.cmbTableau.DropDownWidth = 400
        Me.cmbTableau.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbTableau.EditorFont = New System.Drawing.Font("Verdana", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbTableau.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbTableau.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbTableau.Images.Add(CType(resources.GetObject("cmbTableau.Images"), System.Drawing.Image))
        Me.cmbTableau.ItemHeight = 22
        Me.cmbTableau.Location = New System.Drawing.Point(544, 58)
        Me.cmbTableau.MatchEntryTimeout = CType(2000, Long)
        Me.cmbTableau.MaxDropDownItems = CType(5, Short)
        Me.cmbTableau.MaxLength = 32767
        Me.cmbTableau.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbTableau.Name = "cmbTableau"
        Me.cmbTableau.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbTableau.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbTableau.Size = New System.Drawing.Size(124, 32)
        Me.cmbTableau.TabIndex = 3
        Me.cmbTableau.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbTableau.PropBag = resources.GetString("cmbTableau.PropBag")
        '
        'LNomClient
        '
        Me.LNomClient.AutoSize = True
        Me.LNomClient.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LNomClient.Location = New System.Drawing.Point(9, 25)
        Me.LNomClient.Name = "LNomClient"
        Me.LNomClient.Size = New System.Drawing.Size(53, 20)
        Me.LNomClient.TabIndex = 1
        Me.LNomClient.Text = "Article"
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.Location = New System.Drawing.Point(472, 68)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(66, 20)
        Me.Label8.TabIndex = 35
        Me.Label8.Text = "Tableau"
        '
        'cmbLaboratoire
        '
        Me.cmbLaboratoire.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbLaboratoire.Caption = ""
        Me.cmbLaboratoire.CaptionHeight = 17
        Me.cmbLaboratoire.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbLaboratoire.ColumnCaptionHeight = 17
        Me.cmbLaboratoire.ColumnFooterHeight = 17
        Me.cmbLaboratoire.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbLaboratoire.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown
        Me.cmbLaboratoire.DropDownWidth = 400
        Me.cmbLaboratoire.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbLaboratoire.EditorFont = New System.Drawing.Font("Verdana", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbLaboratoire.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbLaboratoire.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbLaboratoire.Images.Add(CType(resources.GetObject("cmbLaboratoire.Images"), System.Drawing.Image))
        Me.cmbLaboratoire.ItemHeight = 22
        Me.cmbLaboratoire.Location = New System.Drawing.Point(105, 132)
        Me.cmbLaboratoire.MatchEntryTimeout = CType(2000, Long)
        Me.cmbLaboratoire.MaxDropDownItems = CType(5, Short)
        Me.cmbLaboratoire.MaxLength = 32767
        Me.cmbLaboratoire.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbLaboratoire.Name = "cmbLaboratoire"
        Me.cmbLaboratoire.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbLaboratoire.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbLaboratoire.Size = New System.Drawing.Size(154, 32)
        Me.cmbLaboratoire.TabIndex = 2
        Me.cmbLaboratoire.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbLaboratoire.PropBag = resources.GetString("cmbLaboratoire.PropBag")
        '
        'tDosage
        '
        Me.tDosage.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tDosage.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDosage.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDosage.Location = New System.Drawing.Point(336, 63)
        Me.tDosage.MaskInfo.Inherit = CType((C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive Or C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage), C1.Win.C1Input.MaskInfoInheritFlags)
        Me.tDosage.MaskInfo.PromptChar = Global.Microsoft.VisualBasic.ChrW(32)
        Me.tDosage.MaskInfo.StoredEmptyChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.tDosage.Name = "tDosage"
        Me.tDosage.Size = New System.Drawing.Size(127, 27)
        Me.tDosage.TabIndex = 4
        Me.tDosage.Tag = Nothing
        Me.tDosage.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDosage.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'LNumeroClient
        '
        Me.LNumeroClient.AutoSize = True
        Me.LNumeroClient.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LNumeroClient.Location = New System.Drawing.Point(6, 23)
        Me.LNumeroClient.Name = "LNumeroClient"
        Me.LNumeroClient.Size = New System.Drawing.Size(0, 20)
        Me.LNumeroClient.TabIndex = 0
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label11.Location = New System.Drawing.Point(265, 68)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(65, 20)
        Me.Label11.TabIndex = 50
        Me.Label11.Text = "Dosage"
        '
        'LCategorieArticle
        '
        Me.LCategorieArticle.AutoSize = True
        Me.LCategorieArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LCategorieArticle.Location = New System.Drawing.Point(9, 106)
        Me.LCategorieArticle.Name = "LCategorieArticle"
        Me.LCategorieArticle.Size = New System.Drawing.Size(78, 20)
        Me.LCategorieArticle.TabIndex = 3
        Me.LCategorieArticle.Text = "Catégorie"
        '
        'cmbCategorie
        '
        Me.cmbCategorie.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbCategorie.Caption = ""
        Me.cmbCategorie.CaptionHeight = 17
        Me.cmbCategorie.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbCategorie.ColumnCaptionHeight = 17
        Me.cmbCategorie.ColumnFooterHeight = 17
        Me.cmbCategorie.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbCategorie.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown
        Me.cmbCategorie.DropDownWidth = 400
        Me.cmbCategorie.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbCategorie.EditorFont = New System.Drawing.Font("Verdana", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbCategorie.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbCategorie.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbCategorie.Images.Add(CType(resources.GetObject("cmbCategorie.Images"), System.Drawing.Image))
        Me.cmbCategorie.ItemHeight = 22
        Me.cmbCategorie.Location = New System.Drawing.Point(105, 94)
        Me.cmbCategorie.MatchEntryTimeout = CType(2000, Long)
        Me.cmbCategorie.MaxDropDownItems = CType(5, Short)
        Me.cmbCategorie.MaxLength = 32767
        Me.cmbCategorie.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbCategorie.Name = "cmbCategorie"
        Me.cmbCategorie.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbCategorie.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbCategorie.Size = New System.Drawing.Size(154, 32)
        Me.cmbCategorie.TabIndex = 1
        Me.cmbCategorie.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbCategorie.PropBag = resources.GetString("cmbCategorie.PropBag")
        '
        'lLaboratoire
        '
        Me.lLaboratoire.AutoSize = True
        Me.lLaboratoire.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lLaboratoire.Location = New System.Drawing.Point(9, 144)
        Me.lLaboratoire.Name = "lLaboratoire"
        Me.lLaboratoire.Size = New System.Drawing.Size(90, 20)
        Me.lLaboratoire.TabIndex = 1
        Me.lLaboratoire.Text = "Laboratoire"
        '
        'LFormeArticle
        '
        Me.LFormeArticle.AutoSize = True
        Me.LFormeArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LFormeArticle.Location = New System.Drawing.Point(9, 68)
        Me.LFormeArticle.Name = "LFormeArticle"
        Me.LFormeArticle.Size = New System.Drawing.Size(55, 20)
        Me.LFormeArticle.TabIndex = 2
        Me.LFormeArticle.Text = "Forme"
        '
        'cmbForme
        '
        Me.cmbForme.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbForme.Caption = ""
        Me.cmbForme.CaptionHeight = 17
        Me.cmbForme.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbForme.ColumnCaptionHeight = 17
        Me.cmbForme.ColumnFooterHeight = 17
        Me.cmbForme.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbForme.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown
        Me.cmbForme.DropDownWidth = 400
        Me.cmbForme.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbForme.EditorFont = New System.Drawing.Font("Verdana", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbForme.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbForme.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbForme.Images.Add(CType(resources.GetObject("cmbForme.Images"), System.Drawing.Image))
        Me.cmbForme.ItemHeight = 22
        Me.cmbForme.Location = New System.Drawing.Point(105, 56)
        Me.cmbForme.MatchEntryTimeout = CType(2000, Long)
        Me.cmbForme.MaxDropDownItems = CType(5, Short)
        Me.cmbForme.MaxLength = 32767
        Me.cmbForme.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbForme.Name = "cmbForme"
        Me.cmbForme.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbForme.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbForme.Size = New System.Drawing.Size(154, 32)
        Me.cmbForme.TabIndex = 0
        Me.cmbForme.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbForme.PropBag = resources.GetString("cmbForme.PropBag")
        '
        'C1DockingTabPage2
        '
        Me.C1DockingTabPage2.Controls.Add(Me.GroupBox10)
        Me.C1DockingTabPage2.Controls.Add(Me.GroupBox7)
        Me.C1DockingTabPage2.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage2.Name = "C1DockingTabPage2"
        Me.C1DockingTabPage2.Size = New System.Drawing.Size(1103, 468)
        Me.C1DockingTabPage2.TabIndex = 1
        Me.C1DockingTabPage2.Text = "DCI"
        '
        'GroupBox10
        '
        Me.GroupBox10.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.GroupBox10.Controls.Add(Me.chbFemmeEnceinte)
        Me.GroupBox10.Controls.Add(Me.cmbDCI3)
        Me.GroupBox10.Controls.Add(Me.cmbDCI2)
        Me.GroupBox10.Controls.Add(Me.cmbDCI1)
        Me.GroupBox10.Controls.Add(Me.Label34)
        Me.GroupBox10.Controls.Add(Me.Label36)
        Me.GroupBox10.Controls.Add(Me.Label37)
        Me.GroupBox10.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox10.Location = New System.Drawing.Point(10, 20)
        Me.GroupBox10.Name = "GroupBox10"
        Me.GroupBox10.Size = New System.Drawing.Size(672, 179)
        Me.GroupBox10.TabIndex = 2
        Me.GroupBox10.TabStop = False
        Me.GroupBox10.Text = "Informations article"
        '
        'chbFemmeEnceinte
        '
        Me.chbFemmeEnceinte.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chbFemmeEnceinte.Location = New System.Drawing.Point(8, 140)
        Me.chbFemmeEnceinte.Name = "chbFemmeEnceinte"
        Me.chbFemmeEnceinte.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chbFemmeEnceinte.Size = New System.Drawing.Size(283, 21)
        Me.chbFemmeEnceinte.TabIndex = 4
        Me.chbFemmeEnceinte.Text = "Déconseillé pour la femme enceinte"
        Me.chbFemmeEnceinte.UseVisualStyleBackColor = True
        '
        'cmbDCI3
        '
        Me.cmbDCI3.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbDCI3.Caption = ""
        Me.cmbDCI3.CaptionHeight = 17
        Me.cmbDCI3.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbDCI3.ColumnCaptionHeight = 17
        Me.cmbDCI3.ColumnFooterHeight = 17
        Me.cmbDCI3.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbDCI3.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown
        Me.cmbDCI3.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbDCI3.EditorFont = New System.Drawing.Font("Verdana", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbDCI3.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbDCI3.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbDCI3.Images.Add(CType(resources.GetObject("cmbDCI3.Images"), System.Drawing.Image))
        Me.cmbDCI3.ItemHeight = 22
        Me.cmbDCI3.Location = New System.Drawing.Point(110, 94)
        Me.cmbDCI3.MatchEntryTimeout = CType(2000, Long)
        Me.cmbDCI3.MaxDropDownItems = CType(5, Short)
        Me.cmbDCI3.MaxLength = 32767
        Me.cmbDCI3.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbDCI3.Name = "cmbDCI3"
        Me.cmbDCI3.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbDCI3.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbDCI3.Size = New System.Drawing.Size(536, 28)
        Me.cmbDCI3.TabIndex = 2
        Me.cmbDCI3.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbDCI3.PropBag = resources.GetString("cmbDCI3.PropBag")
        '
        'cmbDCI2
        '
        Me.cmbDCI2.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbDCI2.Caption = ""
        Me.cmbDCI2.CaptionHeight = 17
        Me.cmbDCI2.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbDCI2.ColumnCaptionHeight = 17
        Me.cmbDCI2.ColumnFooterHeight = 17
        Me.cmbDCI2.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbDCI2.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown
        Me.cmbDCI2.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbDCI2.EditorFont = New System.Drawing.Font("Verdana", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbDCI2.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbDCI2.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbDCI2.Images.Add(CType(resources.GetObject("cmbDCI2.Images"), System.Drawing.Image))
        Me.cmbDCI2.ItemHeight = 22
        Me.cmbDCI2.Location = New System.Drawing.Point(110, 60)
        Me.cmbDCI2.MatchEntryTimeout = CType(2000, Long)
        Me.cmbDCI2.MaxDropDownItems = CType(5, Short)
        Me.cmbDCI2.MaxLength = 32767
        Me.cmbDCI2.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbDCI2.Name = "cmbDCI2"
        Me.cmbDCI2.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbDCI2.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbDCI2.Size = New System.Drawing.Size(536, 28)
        Me.cmbDCI2.TabIndex = 1
        Me.cmbDCI2.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbDCI2.PropBag = resources.GetString("cmbDCI2.PropBag")
        '
        'cmbDCI1
        '
        Me.cmbDCI1.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbDCI1.Caption = ""
        Me.cmbDCI1.CaptionHeight = 17
        Me.cmbDCI1.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbDCI1.ColumnCaptionHeight = 17
        Me.cmbDCI1.ColumnFooterHeight = 17
        Me.cmbDCI1.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbDCI1.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown
        Me.cmbDCI1.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbDCI1.EditorFont = New System.Drawing.Font("Verdana", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbDCI1.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbDCI1.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbDCI1.Images.Add(CType(resources.GetObject("cmbDCI1.Images"), System.Drawing.Image))
        Me.cmbDCI1.ItemHeight = 22
        Me.cmbDCI1.Location = New System.Drawing.Point(110, 26)
        Me.cmbDCI1.MatchEntryTimeout = CType(2000, Long)
        Me.cmbDCI1.MaxDropDownItems = CType(5, Short)
        Me.cmbDCI1.MaxLength = 32767
        Me.cmbDCI1.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbDCI1.Name = "cmbDCI1"
        Me.cmbDCI1.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbDCI1.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbDCI1.Size = New System.Drawing.Size(536, 28)
        Me.cmbDCI1.TabIndex = 0
        Me.cmbDCI1.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbDCI1.PropBag = resources.GetString("cmbDCI1.PropBag")
        '
        'Label34
        '
        Me.Label34.AutoSize = True
        Me.Label34.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label34.Location = New System.Drawing.Point(14, 34)
        Me.Label34.Name = "Label34"
        Me.Label34.Size = New System.Drawing.Size(81, 20)
        Me.Label34.TabIndex = 2
        Me.Label34.Text = "Principal 1"
        '
        'Label36
        '
        Me.Label36.AutoSize = True
        Me.Label36.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label36.Location = New System.Drawing.Point(14, 102)
        Me.Label36.Name = "Label36"
        Me.Label36.Size = New System.Drawing.Size(81, 20)
        Me.Label36.TabIndex = 1
        Me.Label36.Text = "Principal 3"
        '
        'Label37
        '
        Me.Label37.AutoSize = True
        Me.Label37.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label37.Location = New System.Drawing.Point(14, 68)
        Me.Label37.Name = "Label37"
        Me.Label37.Size = New System.Drawing.Size(81, 20)
        Me.Label37.TabIndex = 3
        Me.Label37.Text = "Principal 2"
        '
        'GroupBox7
        '
        Me.GroupBox7.Controls.Add(Me.lDernierFournisseur)
        Me.GroupBox7.Controls.Add(Me.lDateDernierAchat)
        Me.GroupBox7.Controls.Add(Me.lDateInventaire)
        Me.GroupBox7.Controls.Add(Me.lStockInventaire)
        Me.GroupBox7.Controls.Add(Me.Label41)
        Me.GroupBox7.Controls.Add(Me.lQuantiteDernierCommande)
        Me.GroupBox7.Controls.Add(Me.lDateDernierCommande)
        Me.GroupBox7.Controls.Add(Me.Label40)
        Me.GroupBox7.Controls.Add(Me.Label28)
        Me.GroupBox7.Controls.Add(Me.Label18)
        Me.GroupBox7.Controls.Add(Me.Label19)
        Me.GroupBox7.Controls.Add(Me.Label20)
        Me.GroupBox7.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox7.Location = New System.Drawing.Point(10, 205)
        Me.GroupBox7.Name = "GroupBox7"
        Me.GroupBox7.Size = New System.Drawing.Size(672, 230)
        Me.GroupBox7.TabIndex = 66
        Me.GroupBox7.TabStop = False
        Me.GroupBox7.Text = "Stock"
        '
        'lDernierFournisseur
        '
        Me.lDernierFournisseur.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lDernierFournisseur.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDernierFournisseur.Location = New System.Drawing.Point(424, 151)
        Me.lDernierFournisseur.Name = "lDernierFournisseur"
        Me.lDernierFournisseur.Size = New System.Drawing.Size(222, 28)
        Me.lDernierFournisseur.TabIndex = 64
        Me.lDernierFournisseur.Text = "-"
        '
        'lDateDernierAchat
        '
        Me.lDateDernierAchat.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lDateDernierAchat.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDateDernierAchat.Location = New System.Drawing.Point(424, 187)
        Me.lDateDernierAchat.Name = "lDateDernierAchat"
        Me.lDateDernierAchat.Size = New System.Drawing.Size(222, 28)
        Me.lDateDernierAchat.TabIndex = 62
        Me.lDateDernierAchat.Text = "-"
        '
        'lDateInventaire
        '
        Me.lDateInventaire.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lDateInventaire.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDateInventaire.Location = New System.Drawing.Point(424, 46)
        Me.lDateInventaire.Name = "lDateInventaire"
        Me.lDateInventaire.Size = New System.Drawing.Size(222, 27)
        Me.lDateInventaire.TabIndex = 62
        Me.lDateInventaire.Text = "-"
        '
        'lStockInventaire
        '
        Me.lStockInventaire.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lStockInventaire.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lStockInventaire.Location = New System.Drawing.Point(424, 13)
        Me.lStockInventaire.Name = "lStockInventaire"
        Me.lStockInventaire.Size = New System.Drawing.Size(222, 27)
        Me.lStockInventaire.TabIndex = 61
        Me.lStockInventaire.Text = "-"
        '
        'Label41
        '
        Me.Label41.AutoSize = True
        Me.Label41.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label41.Location = New System.Drawing.Point(6, 188)
        Me.Label41.Name = "Label41"
        Me.Label41.Size = New System.Drawing.Size(171, 20)
        Me.Label41.TabIndex = 61
        Me.Label41.Text = "Date du dernier achat :"
        '
        'lQuantiteDernierCommande
        '
        Me.lQuantiteDernierCommande.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lQuantiteDernierCommande.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lQuantiteDernierCommande.Location = New System.Drawing.Point(424, 115)
        Me.lQuantiteDernierCommande.Name = "lQuantiteDernierCommande"
        Me.lQuantiteDernierCommande.Size = New System.Drawing.Size(222, 27)
        Me.lQuantiteDernierCommande.TabIndex = 60
        Me.lQuantiteDernierCommande.Text = "-"
        '
        'lDateDernierCommande
        '
        Me.lDateDernierCommande.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lDateDernierCommande.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDateDernierCommande.Location = New System.Drawing.Point(424, 79)
        Me.lDateDernierCommande.Name = "lDateDernierCommande"
        Me.lDateDernierCommande.Size = New System.Drawing.Size(222, 27)
        Me.lDateDernierCommande.TabIndex = 58
        Me.lDateDernierCommande.Text = "-"
        '
        'Label40
        '
        Me.Label40.AutoSize = True
        Me.Label40.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label40.Location = New System.Drawing.Point(1, 151)
        Me.Label40.Name = "Label40"
        Me.Label40.Size = New System.Drawing.Size(152, 20)
        Me.Label40.TabIndex = 63
        Me.Label40.Text = "Dernier fournisseur :"
        '
        'Label28
        '
        Me.Label28.AutoSize = True
        Me.Label28.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label28.Location = New System.Drawing.Point(1, 119)
        Me.Label28.Name = "Label28"
        Me.Label28.Size = New System.Drawing.Size(180, 20)
        Me.Label28.TabIndex = 59
        Me.Label28.Text = "Qte dernière commande"
        '
        'Label18
        '
        Me.Label18.AutoSize = True
        Me.Label18.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label18.Location = New System.Drawing.Point(1, 83)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(189, 20)
        Me.Label18.TabIndex = 57
        Me.Label18.Text = "Date dernière commande"
        '
        'Label19
        '
        Me.Label19.AutoSize = True
        Me.Label19.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label19.Location = New System.Drawing.Point(1, 50)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(116, 20)
        Me.Label19.TabIndex = 50
        Me.Label19.Text = "Date inventaire"
        '
        'Label20
        '
        Me.Label20.AutoSize = True
        Me.Label20.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label20.Location = New System.Drawing.Point(1, 20)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(122, 20)
        Me.Label20.TabIndex = 35
        Me.Label20.Text = "Stock inventaire"
        '
        'C1DockingTabPage3
        '
        Me.C1DockingTabPage3.Controls.Add(Me.GroupBox11)
        Me.C1DockingTabPage3.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage3.Name = "C1DockingTabPage3"
        Me.C1DockingTabPage3.Size = New System.Drawing.Size(1103, 468)
        Me.C1DockingTabPage3.TabIndex = 0
        Me.C1DockingTabPage3.Text = "Gestion des lots"
        '
        'GroupBox11
        '
        Me.GroupBox11.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.GroupBox11.Controls.Add(Me.chbVoirLotsVides)
        Me.GroupBox11.Controls.Add(Me.grbAjoutLot)
        Me.GroupBox11.Controls.Add(Me.GroupBox12)
        Me.GroupBox11.Controls.Add(Me.Label35)
        Me.GroupBox11.Controls.Add(Me.gLots)
        Me.GroupBox11.Location = New System.Drawing.Point(10, 20)
        Me.GroupBox11.Name = "GroupBox11"
        Me.GroupBox11.Size = New System.Drawing.Size(824, 449)
        Me.GroupBox11.TabIndex = 0
        Me.GroupBox11.TabStop = False
        '
        'chbVoirLotsVides
        '
        Me.chbVoirLotsVides.Location = New System.Drawing.Point(15, 73)
        Me.chbVoirLotsVides.Name = "chbVoirLotsVides"
        Me.chbVoirLotsVides.Size = New System.Drawing.Size(167, 24)
        Me.chbVoirLotsVides.TabIndex = 87
        Me.chbVoirLotsVides.Text = "Voir les lots vides (Historique)"
        Me.chbVoirLotsVides.UseVisualStyleBackColor = True
        '
        'grbAjoutLot
        '
        Me.grbAjoutLot.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.grbAjoutLot.BackColor = System.Drawing.Color.Transparent
        Me.grbAjoutLot.Controls.Add(Me.dtpDate)
        Me.grbAjoutLot.Controls.Add(Me.bAjouterLot)
        Me.grbAjoutLot.Controls.Add(Me.tQuantiteLot)
        Me.grbAjoutLot.Controls.Add(Me.lQuantiteLot)
        Me.grbAjoutLot.Controls.Add(Me.tNumeroLot)
        Me.grbAjoutLot.Controls.Add(Me.lNumeroLot)
        Me.grbAjoutLot.Controls.Add(Me.lDatePeremption)
        Me.grbAjoutLot.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.grbAjoutLot.ForeColor = System.Drawing.SystemColors.ControlText
        Me.grbAjoutLot.Location = New System.Drawing.Point(15, 366)
        Me.grbAjoutLot.Name = "grbAjoutLot"
        Me.grbAjoutLot.Size = New System.Drawing.Size(794, 65)
        Me.grbAjoutLot.TabIndex = 86
        Me.grbAjoutLot.TabStop = False
        Me.grbAjoutLot.Text = "Ajout du nouveau lot"
        '
        'dtpDate
        '
        Me.dtpDate.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDate.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDate.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDate.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDate.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDate.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.dtpDate.Location = New System.Drawing.Point(422, 28)
        Me.dtpDate.Name = "dtpDate"
        Me.dtpDate.Size = New System.Drawing.Size(128, 18)
        Me.dtpDate.TabIndex = 2
        Me.dtpDate.Tag = Nothing
        Me.dtpDate.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDate.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouterLot
        '
        Me.bAjouterLot.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouterLot.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouterLot.Image = Global.Pharma2000Premium.My.Resources.Resources.nouveau1
        Me.bAjouterLot.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouterLot.Location = New System.Drawing.Point(675, 13)
        Me.bAjouterLot.Name = "bAjouterLot"
        Me.bAjouterLot.Size = New System.Drawing.Size(103, 44)
        Me.bAjouterLot.TabIndex = 3
        Me.bAjouterLot.Text = "Ajouter"
        Me.bAjouterLot.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouterLot.UseVisualStyleBackColor = True
        Me.bAjouterLot.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tQuantiteLot
        '
        Me.tQuantiteLot.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tQuantiteLot.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tQuantiteLot.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tQuantiteLot.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tQuantiteLot.Location = New System.Drawing.Point(204, 28)
        Me.tQuantiteLot.Name = "tQuantiteLot"
        Me.tQuantiteLot.Size = New System.Drawing.Size(70, 18)
        Me.tQuantiteLot.TabIndex = 1
        Me.tQuantiteLot.Tag = Nothing
        '
        'lQuantiteLot
        '
        Me.lQuantiteLot.AutoSize = True
        Me.lQuantiteLot.BackColor = System.Drawing.Color.Transparent
        Me.lQuantiteLot.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lQuantiteLot.Location = New System.Drawing.Point(176, 30)
        Me.lQuantiteLot.Name = "lQuantiteLot"
        Me.lQuantiteLot.Size = New System.Drawing.Size(24, 13)
        Me.lQuantiteLot.TabIndex = 37
        Me.lQuantiteLot.Text = "Qté"
        '
        'tNumeroLot
        '
        Me.tNumeroLot.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNumeroLot.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNumeroLot.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNumeroLot.Location = New System.Drawing.Point(68, 28)
        Me.tNumeroLot.Name = "tNumeroLot"
        Me.tNumeroLot.Size = New System.Drawing.Size(70, 18)
        Me.tNumeroLot.TabIndex = 0
        Me.tNumeroLot.Tag = Nothing
        '
        'lNumeroLot
        '
        Me.lNumeroLot.AutoSize = True
        Me.lNumeroLot.BackColor = System.Drawing.Color.Transparent
        Me.lNumeroLot.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNumeroLot.Location = New System.Drawing.Point(6, 30)
        Me.lNumeroLot.Name = "lNumeroLot"
        Me.lNumeroLot.Size = New System.Drawing.Size(58, 13)
        Me.lNumeroLot.TabIndex = 36
        Me.lNumeroLot.Text = "Numéro lot"
        '
        'lDatePeremption
        '
        Me.lDatePeremption.AutoSize = True
        Me.lDatePeremption.BackColor = System.Drawing.Color.Transparent
        Me.lDatePeremption.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDatePeremption.Location = New System.Drawing.Point(318, 30)
        Me.lDatePeremption.Name = "lDatePeremption"
        Me.lDatePeremption.Size = New System.Drawing.Size(100, 13)
        Me.lDatePeremption.TabIndex = 8
        Me.lDatePeremption.Text = "Date de péremption"
        Me.lDatePeremption.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'GroupBox12
        '
        Me.GroupBox12.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox12.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox12.Controls.Add(Me.bValiderLots)
        Me.GroupBox12.Controls.Add(Me.tDesignationGL)
        Me.GroupBox12.Controls.Add(Me.tCodePCTGL)
        Me.GroupBox12.Controls.Add(Me.Label31)
        Me.GroupBox12.Controls.Add(Me.tCodeArticleGL)
        Me.GroupBox12.Controls.Add(Me.Label32)
        Me.GroupBox12.Controls.Add(Me.Label33)
        Me.GroupBox12.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox12.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox12.Location = New System.Drawing.Point(15, 18)
        Me.GroupBox12.Name = "GroupBox12"
        Me.GroupBox12.Size = New System.Drawing.Size(794, 49)
        Me.GroupBox12.TabIndex = 85
        Me.GroupBox12.TabStop = False
        Me.GroupBox12.Text = "Information du lot "
        '
        'bValiderLots
        '
        Me.bValiderLots.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bValiderLots.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__1
        Me.bValiderLots.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bValiderLots.Location = New System.Drawing.Point(653, 15)
        Me.bValiderLots.Name = "bValiderLots"
        Me.bValiderLots.Size = New System.Drawing.Size(125, 26)
        Me.bValiderLots.TabIndex = 82
        Me.bValiderLots.Text = "Valider lots "
        Me.bValiderLots.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bValiderLots.UseVisualStyleBackColor = True
        Me.bValiderLots.Visible = False
        Me.bValiderLots.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tDesignationGL
        '
        Me.tDesignationGL.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tDesignationGL.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDesignationGL.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tDesignationGL.Enabled = False
        Me.tDesignationGL.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDesignationGL.Location = New System.Drawing.Point(463, 21)
        Me.tDesignationGL.Name = "tDesignationGL"
        Me.tDesignationGL.Size = New System.Drawing.Size(121, 18)
        Me.tDesignationGL.TabIndex = 38
        Me.tDesignationGL.Tag = Nothing
        '
        'tCodePCTGL
        '
        Me.tCodePCTGL.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodePCTGL.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodePCTGL.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tCodePCTGL.Enabled = False
        Me.tCodePCTGL.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodePCTGL.Location = New System.Drawing.Point(279, 21)
        Me.tCodePCTGL.Name = "tCodePCTGL"
        Me.tCodePCTGL.Size = New System.Drawing.Size(89, 18)
        Me.tCodePCTGL.TabIndex = 35
        Me.tCodePCTGL.Tag = Nothing
        '
        'Label31
        '
        Me.Label31.AutoSize = True
        Me.Label31.BackColor = System.Drawing.Color.Transparent
        Me.Label31.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label31.Location = New System.Drawing.Point(219, 23)
        Me.Label31.Name = "Label31"
        Me.Label31.Size = New System.Drawing.Size(56, 13)
        Me.Label31.TabIndex = 37
        Me.Label31.Text = "Code PCT"
        '
        'tCodeArticleGL
        '
        Me.tCodeArticleGL.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodeArticleGL.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodeArticleGL.Enabled = False
        Me.tCodeArticleGL.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodeArticleGL.Location = New System.Drawing.Point(75, 21)
        Me.tCodeArticleGL.Name = "tCodeArticleGL"
        Me.tCodeArticleGL.Size = New System.Drawing.Size(120, 18)
        Me.tCodeArticleGL.TabIndex = 34
        Me.tCodeArticleGL.Tag = Nothing
        '
        'Label32
        '
        Me.Label32.AutoSize = True
        Me.Label32.BackColor = System.Drawing.Color.Transparent
        Me.Label32.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label32.Location = New System.Drawing.Point(6, 23)
        Me.Label32.Name = "Label32"
        Me.Label32.Size = New System.Drawing.Size(64, 13)
        Me.Label32.TabIndex = 36
        Me.Label32.Text = "Code Article"
        '
        'Label33
        '
        Me.Label33.AutoSize = True
        Me.Label33.BackColor = System.Drawing.Color.Transparent
        Me.Label33.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label33.Location = New System.Drawing.Point(395, 23)
        Me.Label33.Name = "Label33"
        Me.Label33.Size = New System.Drawing.Size(63, 13)
        Me.Label33.TabIndex = 8
        Me.Label33.Text = "Désignation"
        Me.Label33.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label35
        '
        Me.Label35.AutoSize = True
        Me.Label35.BackColor = System.Drawing.Color.Transparent
        Me.Label35.Location = New System.Drawing.Point(17, 109)
        Me.Label35.Name = "Label35"
        Me.Label35.Size = New System.Drawing.Size(68, 13)
        Me.Label35.TabIndex = 84
        Me.Label35.Text = "Liste des lots"
        '
        'gLots
        '
        Me.gLots.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gLots.GroupByCaption = "Drag a column header here to group by that column"
        Me.gLots.Images.Add(CType(resources.GetObject("gLots.Images"), System.Drawing.Image))
        Me.gLots.Location = New System.Drawing.Point(15, 125)
        Me.gLots.Name = "gLots"
        Me.gLots.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gLots.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gLots.PreviewInfo.ZoomFactor = 75.0R
        Me.gLots.PrintInfo.PageSettings = CType(resources.GetObject("gLots.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gLots.Size = New System.Drawing.Size(794, 235)
        Me.gLots.TabIndex = 81
        Me.gLots.Text = "C1TrueDBGrid4"
        Me.gLots.PropBag = resources.GetString("gLots.PropBag")
        '
        'C1DockingTabPage4
        '
        Me.C1DockingTabPage4.Controls.Add(Me.Label39)
        Me.C1DockingTabPage4.Controls.Add(Me.gAchat)
        Me.C1DockingTabPage4.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage4.Name = "C1DockingTabPage4"
        Me.C1DockingTabPage4.Size = New System.Drawing.Size(1103, 468)
        Me.C1DockingTabPage4.TabIndex = 2
        Me.C1DockingTabPage4.Text = "Historique achat"
        '
        'Label39
        '
        Me.Label39.AutoSize = True
        Me.Label39.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label39.Location = New System.Drawing.Point(7, 9)
        Me.Label39.Name = "Label39"
        Me.Label39.Size = New System.Drawing.Size(35, 13)
        Me.Label39.TabIndex = 76
        Me.Label39.Text = "Achat"
        '
        'gAchat
        '
        Me.gAchat.AllowColMove = False
        Me.gAchat.AllowColSelect = False
        Me.gAchat.AllowUpdate = False
        Me.gAchat.AllowUpdateOnBlur = False
        Me.gAchat.AlternatingRows = True
        Me.gAchat.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.gAchat.FetchRowStyles = True
        Me.gAchat.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gAchat.GroupByCaption = "Drag a column header here to group by that column"
        Me.gAchat.Images.Add(CType(resources.GetObject("gAchat.Images"), System.Drawing.Image))
        Me.gAchat.LinesPerRow = 2
        Me.gAchat.Location = New System.Drawing.Point(10, 11)
        Me.gAchat.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gAchat.Name = "gAchat"
        Me.gAchat.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gAchat.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gAchat.PreviewInfo.ZoomFactor = 75.0R
        Me.gAchat.PrintInfo.PageSettings = CType(resources.GetObject("gAchat.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gAchat.Size = New System.Drawing.Size(1090, 469)
        Me.gAchat.TabIndex = 75
        Me.gAchat.Text = "C1TrueDBGrid1"
        Me.gAchat.PropBag = resources.GetString("gAchat.PropBag")
        '
        'fFicheArticle
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1121, 590)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None
        Me.Name = "fFicheArticle"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "fFicheArticle"
        Me.Panel.ResumeLayout(False)
        CType(Me.Tab, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Tab.ResumeLayout(False)
        Me.C1DockingTabPage1.ResumeLayout(False)
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.tstockAlert, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tQuantiteACommander, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox9.ResumeLayout(False)
        Me.GroupBox9.PerformLayout()
        CType(Me.cmbSituation, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox8.ResumeLayout(False)
        Me.GroupBox8.PerformLayout()
        CType(Me.tTarifReference, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodePCT, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbCategorieCnam, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.CmbTVA, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tPrixVenteTTC, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tHR, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tPrixVenteHT, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tMarge, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tPrixAchatTTC, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tPrixAchatHT, System.ComponentModel.ISupportInitialize).EndInit()
        Me.STOCK.ResumeLayout(False)
        Me.GroupBox6.ResumeLayout(False)
        Me.GroupBox6.PerformLayout()
        CType(Me.tQuantiteUnitaire, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tFocus, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpDerniereDateAchat, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDernierPrixAchat, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDateAlerte, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tContenance, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.tNumCirculaire, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tRayon, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tSection, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbPreparation, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDesignation, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodeArticle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbTableau, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbLaboratoire, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDosage, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbCategorie, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbForme, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage2.ResumeLayout(False)
        Me.GroupBox10.ResumeLayout(False)
        Me.GroupBox10.PerformLayout()
        CType(Me.cmbDCI3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbDCI2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbDCI1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox7.ResumeLayout(False)
        Me.GroupBox7.PerformLayout()
        Me.C1DockingTabPage3.ResumeLayout(False)
        Me.GroupBox11.ResumeLayout(False)
        Me.GroupBox11.PerformLayout()
        Me.grbAjoutLot.ResumeLayout(False)
        Me.grbAjoutLot.PerformLayout()
        CType(Me.dtpDate, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tQuantiteLot, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNumeroLot, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox12.ResumeLayout(False)
        Me.GroupBox12.PerformLayout()
        CType(Me.tDesignationGL, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodePCTGL, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodeArticleGL, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gLots, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage4.ResumeLayout(False)
        Me.C1DockingTabPage4.PerformLayout()
        CType(Me.gAchat, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bConfirmer As C1.Win.C1Input.C1Button
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents Tab As C1.Win.C1Command.C1DockingTab
    Friend WithEvents C1DockingTabPage1 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents C1DockingTabPage2 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents GroupBox10 As System.Windows.Forms.GroupBox
    Friend WithEvents cmbDCI3 As C1.Win.C1List.C1Combo
    Friend WithEvents cmbDCI2 As C1.Win.C1List.C1Combo
    Friend WithEvents cmbDCI1 As C1.Win.C1List.C1Combo
    Friend WithEvents Label34 As System.Windows.Forms.Label
    Friend WithEvents Label36 As System.Windows.Forms.Label
    Private WithEvents Label37 As System.Windows.Forms.Label
    Friend WithEvents C1DockingTabPage3 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents GroupBox11 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox12 As System.Windows.Forms.GroupBox
    Friend WithEvents tCodePCTGL As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label31 As System.Windows.Forms.Label
    Friend WithEvents tCodeArticleGL As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label32 As System.Windows.Forms.Label
    Friend WithEvents Label33 As System.Windows.Forms.Label
    Friend WithEvents Label35 As System.Windows.Forms.Label
    Friend WithEvents bValiderLots As C1.Win.C1Input.C1Button
    Friend WithEvents gLots As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents tDesignationGL As C1.Win.C1Input.C1TextBox
    Friend WithEvents chbFemmeEnceinte As System.Windows.Forms.CheckBox
    Friend WithEvents bNaviger As C1.Win.C1Input.C1Button
    Friend WithEvents grbAjoutLot As System.Windows.Forms.GroupBox
    Friend WithEvents tQuantiteLot As C1.Win.C1Input.C1TextBox
    Friend WithEvents lQuantiteLot As System.Windows.Forms.Label
    Friend WithEvents tNumeroLot As C1.Win.C1Input.C1TextBox
    Friend WithEvents lNumeroLot As System.Windows.Forms.Label
    Friend WithEvents lDatePeremption As System.Windows.Forms.Label
    Friend WithEvents bAjouterLot As C1.Win.C1Input.C1Button
    Friend WithEvents dtpDate As C1.Win.C1Input.C1DateEdit
    Friend WithEvents chbVoirLotsVides As System.Windows.Forms.CheckBox
    Friend WithEvents lNomArticle As System.Windows.Forms.Label
    Friend WithEvents bStatistiqueArticle As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox9 As System.Windows.Forms.GroupBox
    Friend WithEvents lOperateur As System.Windows.Forms.Label
    Friend WithEvents Label29 As System.Windows.Forms.Label
    Friend WithEvents cmbSituation As C1.Win.C1List.C1Combo
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents GroupBox8 As System.Windows.Forms.GroupBox
    Friend WithEvents lTestPCT As System.Windows.Forms.Label
    Friend WithEvents tTarifReference As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label22 As System.Windows.Forms.Label
    Friend WithEvents Label24 As System.Windows.Forms.Label
    Friend WithEvents chkPriseEnCharge As System.Windows.Forms.CheckBox
    Friend WithEvents tCodePCT As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents Label21 As System.Windows.Forms.Label
    Friend WithEvents chkAccordPrealable As System.Windows.Forms.CheckBox
    Friend WithEvents cmbCategorieCnam As C1.Win.C1List.C1Combo
    Friend WithEvents Label23 As System.Windows.Forms.Label
    Friend WithEvents GroupBox7 As System.Windows.Forms.GroupBox
    Friend WithEvents lQuantiteDernierCommande As System.Windows.Forms.Label
    Friend WithEvents lDateDernierCommande As System.Windows.Forms.Label
    Friend WithEvents Label28 As System.Windows.Forms.Label
    Friend WithEvents Label18 As System.Windows.Forms.Label
    Friend WithEvents Label19 As System.Windows.Forms.Label
    Friend WithEvents Label20 As System.Windows.Forms.Label
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents Label27 As System.Windows.Forms.Label
    Friend WithEvents chkExonere As System.Windows.Forms.CheckBox
    Friend WithEvents tPrixVenteTTC As C1.Win.C1Input.C1TextBox
    Friend WithEvents tHR As C1.Win.C1Input.C1TextBox
    Friend WithEvents tPrixVenteHT As C1.Win.C1Input.C1TextBox
    Friend WithEvents tMarge As C1.Win.C1Input.C1TextBox
    Friend WithEvents tPrixAchatTTC As C1.Win.C1Input.C1TextBox
    Friend WithEvents tPrixAchatHT As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents STOCK As System.Windows.Forms.GroupBox
    Friend WithEvents lUnite As System.Windows.Forms.Label
    Friend WithEvents lStock As System.Windows.Forms.Label
    Friend WithEvents Label25 As System.Windows.Forms.Label
    Friend WithEvents chkSansCodeAbarre As System.Windows.Forms.CheckBox
    Friend WithEvents Label17 As System.Windows.Forms.Label
    Friend WithEvents chkSansVignette As System.Windows.Forms.CheckBox
    Friend WithEvents tRayon As C1.Win.C1Input.C1TextBox
    Friend WithEvents cmbPreparation As C1.Win.C1List.C1Combo
    Friend WithEvents tSection As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents cmbLaboratoire As C1.Win.C1List.C1Combo
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents cmbCategorie As C1.Win.C1List.C1Combo
    Friend WithEvents tDosage As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents cmbTableau As C1.Win.C1List.C1Combo
    Friend WithEvents cmbForme As C1.Win.C1List.C1Combo
    Friend WithEvents LFormeArticle As System.Windows.Forms.Label
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents lLaboratoire As System.Windows.Forms.Label
    Private WithEvents LCategorieArticle As System.Windows.Forms.Label
    Friend WithEvents GroupBox6 As System.Windows.Forms.GroupBox
    Friend WithEvents tDateAlerte As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label30 As System.Windows.Forms.Label
    Friend WithEvents tContenance As C1.Win.C1Input.C1TextBox
    Friend WithEvents tQuantiteACommander As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label26 As System.Windows.Forms.Label
    Friend WithEvents tQuantiteUnitaire As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents tstockAlert As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents lTest As System.Windows.Forms.Label
    Friend WithEvents tDesignation As C1.Win.C1Input.C1TextBox
    Friend WithEvents tCodeArticle As C1.Win.C1Input.C1TextBox
    Friend WithEvents LNomClient As System.Windows.Forms.Label
    Friend WithEvents LNumeroClient As System.Windows.Forms.Label
    Friend WithEvents bChangementPrix As C1.Win.C1Input.C1Button
    Friend WithEvents lDernierFournisseur As System.Windows.Forms.Label
    Friend WithEvents lDateDernierAchat As System.Windows.Forms.Label
    Friend WithEvents Label40 As System.Windows.Forms.Label
    Friend WithEvents Label41 As System.Windows.Forms.Label
    Friend WithEvents lDateInventaire As System.Windows.Forms.Label
    Friend WithEvents lStockInventaire As System.Windows.Forms.Label
    Friend WithEvents dtpDerniereDateAchat As C1.Win.C1Input.C1DateEdit
    Friend WithEvents lDerniereDateAchat As System.Windows.Forms.Label
    Friend WithEvents tDernierPrixAchat As C1.Win.C1Input.C1TextBox
    Friend WithEvents lQuantiteUnitaire As System.Windows.Forms.Label
    Friend WithEvents bChangeQuantiteUnitaire As C1.Win.C1Input.C1Button
    Friend WithEvents tFocus As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1DockingTabPage4 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents Label39 As System.Windows.Forms.Label
    Friend WithEvents gAchat As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents CmbTVA As C1.Win.C1List.C1Combo
    Friend WithEvents Label38 As System.Windows.Forms.Label
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents tNumCirculaire As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label42 As System.Windows.Forms.Label
End Class
