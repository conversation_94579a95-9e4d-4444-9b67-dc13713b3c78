﻿Imports System.ComponentModel
Imports System.Data.SqlClient
Imports System.Data.OleDb
Imports System.Text
Public Class fCubeVenteDetail
    Public Mode As String = ""
    Dim NomVue As String = ""
    Dim cmd As New OleDbCommand
    Dim da As New OleDbDataAdapter

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "123" And bCritere.Enabled = True Then
            bCritere_Click(sender, e)
        End If
    End Sub

    Public Sub Init()
       
        ConnectOleDB()
        Dim Cond As String = " 1=1 "
        Dim MyCritereCube As New fCritereCubeVenteDetail
        'If Mode = "Vente Detail" Or Mode = "Achat Detail" Then

        MyCritereCube.Mode = Mode
        MyCritereCube.ShowDialog()

        'End If
        ' get data

        If Mode = "Vente Detail" Then

            NomVue = "Vue_CubeVenteDetail"
            Cond += " AND [Période : Date de vente] BETWEEN " + Quote(MyCritereCube.yDateDebut) + " AND " + Quote(MyCritereCube.yDateFin)
            If MyCritereCube.yClient <> "" Then
                Cond += " AND  [Client : Nom du client] =" + Quote(MyCritereCube.yClient)
            End If
            If MyCritereCube.yOperateur <> "" Then
                Cond += " AND  [Vente : Personnel] =" + Quote(MyCritereCube.yOperateur)
            End If

        ElseIf Mode = "Vente" Then
            NomVue = "Vue_CubeVente"

        ElseIf Mode = "Achat Detail" Then

            NomVue = "Vue_CubeAchatDetail"
            Cond += " AND [Période : Date d'achat] BETWEEN " + Quote(MyCritereCube.yDateDebut) + " AND " + Quote(MyCritereCube.yDateFin)
            If MyCritereCube.yClient <> "" Then
                Cond += " AND  [Fournisseur : Nom du fournisseur] =" + Quote(MyCritereCube.yClient)
            End If
            If MyCritereCube.yOperateur <> "" Then
                Cond += " AND  [Achat : Personnel] =" + Quote(MyCritereCube.yOperateur)
            End If
        End If
        If MyCritereCube.yProduit <> "" Then
            Cond += " AND  [Article : Désignation] =" + Quote(MyCritereCube.yProduit)
        End If
        If MyCritereCube.yCategorie <> "" Then
            Cond += " AND  [Article : Catégorie] =" + Quote(MyCritereCube.yCategorie)
        End If
        If MyCritereCube.yLabo <> "" Then
            Cond += " AND  [Article : Laboratoire] =" + Quote(MyCritereCube.yLabo)
        End If


        If Mode = "Vente" Then
            Cond += " AND [Période : Date de vente] BETWEEN " + Quote(MyCritereCube.yDateDebut) + " AND " + Quote(MyCritereCube.yDateFin)
            If MyCritereCube.yClient <> "" Then
                Cond += " AND  [Client : Nom du client] =" + Quote(MyCritereCube.yClient)
            End If
            If MyCritereCube.yOperateur <> "" Then
                Cond += " AND  [Vente : Personnel] =" + Quote(MyCritereCube.yOperateur)
            End If
            cmd.CommandText = "SELECT * FROM Vue_CubeVente WHERE " + Cond
        Else
            cmd.CommandText = "SELECT * FROM " + NomVue + " WHERE " + Cond
        End If


        cmd.Connection = ConnectionServeurOLEDB
        Dim da = New OleDbDataAdapter(cmd)
        Dim dt = New DataTable()
        da.Fill(dt)

        ' bind to olap page
        Me.C1OlapPage1.DataSource = dt

        ' build view
        Dim olap = Me.C1OlapPage1.OlapEngine
        olap.ValueFields.MaxItems = 5

    End Sub
    Private Sub bCritere_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCritere.Click
        Dim Cond As String = " 1=1 "
        Dim MyCritereCube As New fCritereCubeVenteDetail
        MyCritereCube.Mode = Mode
        MyCritereCube.ShowDialog()
        ' get data
        If Mode = "Vente Detail" Then

            NomVue = "Vue_CubeVenteDetail"
            Cond += " AND [Période : Date de vente] BETWEEN " + Quote(MyCritereCube.yDateDebut) + " AND " + Quote(MyCritereCube.yDateFin)
            If MyCritereCube.yClient <> "" Then
                Cond += " AND  [Client : Nom du client] =" + Quote(MyCritereCube.yClient)
            End If
            If MyCritereCube.yOperateur <> "" Then
                Cond += " AND  [Vente : Personnel] =" + Quote(MyCritereCube.yOperateur)
            End If
        ElseIf Mode = "Vente" Then

            NomVue = "Vue_CubeVente"
            Cond += " AND [Période : Date de vente] BETWEEN " + Quote(MyCritereCube.yDateDebut) + " AND " + Quote(MyCritereCube.yDateFin)
            If MyCritereCube.yClient <> "" Then
                Cond += " AND  [Client : Nom du client] =" + Quote(MyCritereCube.yClient)
            End If
            If MyCritereCube.yOperateur <> "" Then
                Cond += " AND  [Vente : Personnel] =" + Quote(MyCritereCube.yOperateur)
            End If

        ElseIf Mode = "Achat Detail" Then

            NomVue = "Vue_CubeAchatDetail"
            Cond += " AND [Période : Date d'achat] BETWEEN " + Quote(MyCritereCube.yDateDebut) + " AND " + Quote(MyCritereCube.yDateFin)
            If MyCritereCube.yClient <> "" Then
                Cond += " AND  [Fournisseur : Nom du fournisseur] =" + Quote(MyCritereCube.yClient)
            End If
            If MyCritereCube.yOperateur <> "" Then
                Cond += " AND  [Achat : Personnel] =" + Quote(MyCritereCube.yOperateur)
            End If
        End If

        If MyCritereCube.yProduit <> "" Then
            Cond += " AND  [Article : Désignation] =" + Quote(MyCritereCube.yProduit)
        End If
        If MyCritereCube.yCategorie <> "" Then
            Cond += " AND  [Article : Catégorie] =" + Quote(MyCritereCube.yCategorie)
        End If
        If MyCritereCube.yLabo <> "" Then
            Cond += " AND  [Article : Laboratoire] =" + Quote(MyCritereCube.yLabo)
        End If

        cmd.CommandText = "SELECT * FROM " + NomVue + " WHERE " + Cond

        cmd.Connection = ConnectionServeurOLEDB
        Dim da = New OleDbDataAdapter(cmd)
        Dim dt = New DataTable()
        da.Fill(dt)

        ' bind to olap page
        Me.C1OlapPage1.DataSource = dt

        ' build view
        Dim olap = Me.C1OlapPage1.OlapEngine
        olap.ValueFields.MaxItems = 5

    End Sub

    Private Sub fCubeVenteDetail_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

    End Sub
End Class