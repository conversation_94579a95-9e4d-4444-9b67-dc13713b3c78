﻿Imports System.Data.SqlClient
Imports System.IO.Ports

Public Class fPaiement
    Public Shared cmdReglement As New SqlCommand
    Public Shared cbReglement As New SqlCommandBuilder
    Public Shared dsReglement As New DataSet
    Public Shared daReglement As New SqlDataAdapter
    Public StrSQL As String = ""
    Dim NumeroImage As Integer = 1

    Dim up As Boolean = False

    Public Shared ClientPassger As Boolean = False
    Public Shared VenteCnamMutuelle As Boolean = False
    Public Shared MontantVente As Double = 0.0
    Public Shared PremiereActivate As Boolean = True
    Public Shared CodeClient As String = ""
    Public Shared paiement As String = ""
    Public Shared utilistaeur As Integer = 0
    Public Shared CodeModePaiement As Integer = 0
    Public Shared TypePaiement As String = ""
    Public Shared MontantRecu As Double = 0.0
    Public Shared MontantRegle As Double = 0.0
    Public Shared DateEcheance As String = "01/01/1900"
    Public Shared TableauB As Boolean = False
    Public Shared InscriptionSurOrdonnancier As Boolean = False
    Public Shared Totaliseur As Boolean = False

    Public NouveauSoldeDuClient As Double = 0.0

    Public Ordonnancier As Boolean = False

    'Public Shared Montant As Double = 0.0
    Public Shared Nom As String = ""

    Public Shared NumeroCheque As String = ""

    Public Shared ImpressionApresVente As Boolean = False

    Public renduMnt As Double = 0.0

    Dim IsFirst As Boolean = True
    Dim conteur As Integer = 0


    Declare Function InitFRIUSBLibrary Lib "FIRIDLLU.dll" () As Integer
    Declare Function OpenFRIDoor Lib "FIRIDLLU.dll" () As Integer
    Declare Function CloseFRIUSBLibrary Lib "FIRIDLLU.dll" () As Integer

    Private Sub fPaiement_BackgroundImageLayoutChanged(sender As Object, e As System.EventArgs) Handles Me.BackgroundImageLayoutChanged

    End Sub

    Private Sub fPaiement_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Me.KeyDown
        If tRecu.Enabled = True Then
            If IsFirst = True And e.KeyCode <> Keys.Enter And tRecu.Focused = True Then
                tRecu.Text = ""
            End If

            IsFirst = False
        End If
        If e.KeyCode = Keys.F5 Then
            chbOrdonnancier.Checked = Not chbOrdonnancier.Checked
        End If
    End Sub

    Private Sub fPaiement_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles Me.KeyPress
        'Dim x As Char
        'x = e.KeyChar

        'If IsFirst = True And e.KeyChar = "/c" Then
        'Else
        '    tRecu.Text = ""
        'End If

        'IsFirst = False

    End Sub

    Private Sub fPaiement_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Dim Transaction1 As SqlTransaction = ConnectionServeur.BeginTransaction()
        'cmdReglement.Transaction = Transaction1

        'chargement des Clients
        Try
            dsReglement.Tables("NATURE_REGLEMENT").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT CodeNatureReglement,LibelleNatureReglement FROM NATURE_REGLEMENT WHERE " + _
                 "LibelleNatureReglement<>'MULTIPLE' AND LibelleNatureReglement<>'REMISE' AND " + _
                 "LibelleNatureReglement<>'VIREMENT' ORDER BY LibelleNatureReglement ASC"
        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsReglement, "NATURE_REGLEMENT")
        cmbModePaiement.DataSource = dsReglement.Tables("NATURE_REGLEMENT")
        cmbModePaiement.ValueMember = "CodeNatureReglement"
        cmbModePaiement.DisplayMember = "LibelleNatureReglement"
        cmbModePaiement.ColumnHeaders = False
        cmbModePaiement.ColumnFooterHeight = 150
        cmbModePaiement.Splits(0).DisplayColumns("CodeNatureReglement").Width = 0
        cmbModePaiement.Splits(0).DisplayColumns("LibelleNatureReglement").Width = 110

        cmbModePaiement.Text = "ESPECE"
        TypePaiement = "Simple"

        If 5 - MontantVente >= 0 Then
            lRetour5.Text = 5 - MontantVente
        Else
            lRetour5.Text = "."
        End If

        If 10 - MontantVente >= 0 Then
            lRetour10.Text = 10 - MontantVente
        Else
            lRetour10.Text = "."
        End If

        If 20 - MontantVente >= 0 Then
            lRetour20.Text = 20 - MontantVente
        Else
            lRetour20.Text = "."
        End If

        If 50 - MontantVente >= 0 Then
            lRetour50.Text = 50 - MontantVente
        Else
            lRetour50.Text = "."
        End If

        If Ordonnancier = True Then
            Timer1.Start()
        Else
            Timer1.Stop()
        End If

        If (AdditionOrdonnanceVente < 0) Then
            tRecu.Enabled = False
        End If


        tEcheance.Value = ""
        tNumero.Value = ""
        tNom.Value = ""
        tMotPasse.Value = ""
        lMontant.Text = MontantVente.ToString
        tRecu.Focus()
        'tRecu.SelectionLength = tRecu.Text.Length
        chbOrdonnancier.Checked = IIf(InscriptionSurOrdonnancierAutomatiqueTableauB, TableauB, InscriptionSurOrdonnancierAutomatique)
        chbOrdonnancier.Enabled = Not InscriptionSurOrdonnancierAutomatique
        tEcheance.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        tEcheance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        tEcheance.Value = Today
        tRecu.Focus()


        'En cas de retour en bloque le text recu
        If Not tRecu.Enabled Then
            grpMotDePasse.Focus()
            tMotPasse.Focus()
        End If

        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler

        'tRecu.SelectAll()



        IsFirst = True
        conteur = 0

        chbOrdonnancier.Enabled = Ordonnancier

        If (AdditionOrdonnanceVente < 0) Then
            tRecu.Enabled = False
        End If
    End Sub
    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)

        If e.KeyCode = Keys.F10 Then
            e.SuppressKeyPress = True
        Else
            e.SuppressKeyPress = False
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(o, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(o, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(o, e)
            Exit Sub
            'ElseIf e.KeyCode = Keys.F5 Then
            '    chbOrdonnancier.Checked = Not chbOrdonnancier.Checked
            'If conteur = 0 Then
            'If chbOrdonnancier.Checked = True Then
            '    chbOrdonnancier.Checked = False
            '    'conteur = 1
            'Else
            '    chbOrdonnancier.Checked = True
            '    'conteur = 2
            'End If
            'Else
            '    If conteur = 1 And chbOrdonnancier.Checked = False Then
            '        chbOrdonnancier.Checked = True
            '        conteur = 1
            '    ElseIf conteur = 2 And chbOrdonnancier.Checked = True Then
            '        chbOrdonnancier.Checked = False
            '        conteur = 2
            '        'ElseIf conteur = 3 And chbOrdonnancier.Checked = False Then
            '        '    chbOrdonnancier.Checked = True
            '        'ElseIf conteur = 3 And chbOrdonnancier.Checked = True Then
            '        '    chbOrdonnancier.Checked = False
            '    Else
            '        conteur = 3
            '    End If

            'End If

            Exit Sub
        End If

    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        paiement = "NO"
        utilistaeur = 0
        MontantRecu = 0.0
        CodeModePaiement = 0
        DateEcheance = " 01/01/1900"
        NumeroCheque = ""
        ImpressionApresVente = False

        Me.Hide()
    End Sub

    Private Sub fPaiement_Activated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Activated
        If PremiereActivate = True Then
            tRecu.Enabled = True
            If MontantVente < 0 Then
                If CodeClient <> "CLIPASS" Then
                    tRecu.Value = "0.000"
                    lRendu.Text = (-MontantVente).ToString
                    tRecu.Enabled = False
                Else
                    tRecu.Enabled = False
                    tRecu.Value = MontantVente.ToString '"0.000"
                    lRendu.Text = 0 '(-MontantVente).ToString
                End If

                lRenduAffiche.Text = "Rendu"
                lRenduAffiche.ForeColor = Color.Red
                lRendu.ForeColor = Color.Red
                'tRecu.Focus()

                'En cas de retour en bloque le text recu
                If Not tRecu.Enabled Then
                    grpMotDePasse.Focus()
                    Application.DoEvents()
                    tMotPasse.Focus()
                    tMotPasse.Select()
                End If
                Exit Sub
            End If

            If ClientPassger = True Then
                tRecu.Value = MontantVente.ToString
                lRendu.Text = "0.000"
                lRenduAffiche.ForeColor = Color.Black
                lRendu.ForeColor = Color.Black
            ElseIf VenteCnamMutuelle = True Then
                tRecu.Value = MontantVente.ToString
                lRendu.Text = "0.000"
                lRenduAffiche.ForeColor = Color.Black
                lRendu.ForeColor = Color.Black
            Else
                tRecu.Text = "0.000"
                lRendu.Text = MontantVente.ToString
                lRenduAffiche.Text = "Crédit"
                lRenduAffiche.ForeColor = Color.Red
                lRendu.ForeColor = Color.Red
            End If
            PremiereActivate = False
        End If
        tRecu.Focus()
        tRecu.SelectAll()

        'En cas de retour en bloque le text recu
        If Not tRecu.Enabled Then
            grpMotDePasse.Focus()
            tMotPasse.Focus()
        End If

        tRecu.Value = tRecu.Text
        If tRecu.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(tRecu.Text, ".")
            If tRecu.Text.Length - x = 1 Then
                tRecu.Value = tRecu.Text + ("00")
            ElseIf tRecu.Text.Length - x = 2 Then
                tRecu.Value = tRecu.Text + ("0")
            End If
        Else
            tRecu.Value = tRecu.Text + ".000"
        End If
    End Sub

    Private Sub bOk_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOk.Click
        Dim UtilisateurCode As Integer = 0
        Dim NumeroReglement As Integer = 0
        Dim TestMotDePasse As Decimal = 0.0

        '--------------------
        If (Convert.ToDecimal(lMontant.Text) > Convert.ToDecimal(tRecu.Text)) Then
            If ControleDAcces(9, "VENTE_CREDIT") = "False" Then
                Exit Sub
            End If
        End If

        '--------------------
        tRecu.ValidateText()
        Try
            Dim x As Double = 0.0
            x = CDbl(tRecu.Text)  'pour verifier qu'il s'agit d'un entier

            If IsNumeric(CDbl(tRecu.Text)) = True Then

                If ClientPassger = True And (CDbl(tRecu.Text) < CDbl(lMontant.Text)) Then
                    MsgBox("Pas de credit pour un client passager !", MsgBoxStyle.Critical, "Erreur")
                    tRecu.Text = x 'MontantVente.ToString
                    lRendu.Text = "0.000"
                    lRenduAffiche.Text = "Rendu"
                    lRenduAffiche.ForeColor = Color.Black
                    lRendu.ForeColor = Color.Black

                    tRecu.Focus()
                    Exit Sub

                End If
            End If

        Catch ex As Exception

            MsgBox("Merci de vérifier les données saisies !", MsgBoxStyle.Critical, "Erreur")
            tRecu.Focus()
            Exit Sub

        End Try

        tRecu.Value = tRecu.Text
        If tRecu.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(tRecu.Text, ".")
            If tRecu.Text.Length - x = 1 Then
                tRecu.Value = tRecu.Text + ("00")
            ElseIf tRecu.Text.Length - x = 2 Then
                tRecu.Value = tRecu.Text + ("0")
            ElseIf tRecu.Text.Length - x = 0 Then
                tRecu.Value = tRecu.Text + ("000")
            End If
        Else
            tRecu.Value = tRecu.Text + ".000"
        End If

        '--------------------

        If cmbModePaiement.Text = "" Then
            tMotPasse.Focus()
            MsgBox("Vous devez sélectionner le mode de paiement !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If
        Try
            TestMotDePasse = CDbl(tRecu.Text)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tRecu.Value = "0.000"
            tRecu.Focus()

            'En cas de retour en bloque le text recu
            If Not tRecu.Enabled Then
                grpMotDePasse.Focus()
                tMotPasse.Focus()
            End If

            tRecu.SelectionLength = tRecu.Text.Length
            Exit Sub
        End Try

        'controle si le montant de cheque est > au ttc 
        If cmbModePaiement.Text <> "ESPECE" And CDbl(tRecu.Text) > CDbl(lMontant.Text) Then
            tRecu.Focus()

            'En cas de retour en bloque le text recu
            If Not tRecu.Enabled Then
                grpMotDePasse.Focus()
                tMotPasse.Focus()
            End If

            MsgBox("Montant supérieur au Net à payer !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        '---------------------------------- contrôle si ce client est autorisé pour le credit ou nn 
        Dim PaimentComptant As Boolean = False
        Try
            PaimentComptant = RecupererValeurExecuteScalaire("PaiementComptant", "CLIENT", "CodeClient", CodeClient)
        Catch ex As Exception
        End Try

        If PaimentComptant = True And Convert.ToDecimal(lMontant.Text) > Convert.ToDecimal(tRecu.Text) Then
            MsgBox("Ce client n'est pas autorisé à avoir un crédit !", MsgBoxStyle.Critical, "Erreur")
            tRecu.Focus()

            'En cas de retour en bloque le text recu
            If Not tRecu.Enabled Then
                grpMotDePasse.Focus()
                tMotPasse.Focus()
            End If

            Exit Sub
        End If

        '---------------------------------- contrôle du solde du client qui ne doit pas depasser le credit max
        Dim CreditMax As Double = 0.0
        CreditMax = CDbl(RecupererValeurExecuteScalaire("CrediMax", "CLIENT", "CodeClient", CodeClient))
        If CreditMax <> 0 Then
            If CreditMax < NouveauSoldeDuClient - CDbl(tRecu.Text) Then
                MsgBox("Le Crédit maximum de ce client est dépassé (" + CreditMax.ToString + ")!", MsgBoxStyle.Critical, "Erreur")
                tRecu.Focus()

                'En cas de retour en bloque le text recu
                If Not tRecu.Enabled Then
                    grpMotDePasse.Focus()
                    tMotPasse.Focus()
                End If

                Exit Sub
            End If
        End If

        StrSQL = "SELECT COUNT([CodeUtilisateur]) FROM UTILISATEUR WHERE Supprime=0 AND [MotPass]='" + tMotPasse.Text + "'"
        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL
        Try
            UtilisateurCode = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        If UtilisateurCode = 0 Then
            tMotPasse.Text = ""
            MsgBox("Mot de passe erroné !", MsgBoxStyle.Critical, "Erreur")
            tMotPasse.Focus()
            Exit Sub
        End If

        StrSQL = "SELECT [CodeUtilisateur] FROM UTILISATEUR WHERE Supprime=0 AND [MotPass]='" + tMotPasse.Text + "'"
        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL
        Try
            UtilisateurCode = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        paiement = "OK"
        utilistaeur = UtilisateurCode

        If CDbl(tRecu.Text) = "0.000" Then
            CodeModePaiement = 3
        Else
            CodeModePaiement = cmbModePaiement.SelectedValue
        End If

        DateEcheance = tEcheance.Text.ToString
        NumeroCheque = tNumero.Text
        Nom = tNom.Text
        NumeroCheque = tNumero.Text

        If lMontant.Text >= 0 Then
            If CDbl(tRecu.Text) <= lMontant.Text Then
                MontantRecu = CDbl(tRecu.Text)
                MontantRegle = CDbl(tRecu.Text)
            Else
                MontantRecu = CDbl(tRecu.Text)
                MontantRegle = CDbl(tRecu.Text) - CDbl(lRendu.Text)
            End If
        Else
            MontantRecu = CDbl(tRecu.Text)
            MontantRegle = CDbl(tRecu.Text)
        End If



        InscriptionSurOrdonnancier = chbOrdonnancier.Checked
        ImpressionApresVente = False

        ' End If
        Dim myvente As New fVente
        myvente.AfficheArticle = False
        Me.Hide()
        'Me.Close()
        'Me.Dispose()

    End Sub
    Private Sub Timer1_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer1.Tick

        If NumeroImage = 1 Then
            Clignote1.Image = Pharma2000Premium.My.Resources.Resources._2
            NumeroImage = 2
        ElseIf NumeroImage = 2 Then
            Clignote1.Image = Pharma2000Premium.My.Resources.Resources._3
            NumeroImage = 3
        ElseIf NumeroImage = 3 Then
            Clignote1.Image = Pharma2000Premium.My.Resources.Resources._1
            NumeroImage = 1
        End If
    End Sub

    Private Sub tRecu_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tRecu.GotFocus
        'tRecu.SelectAll()
        ''tRecu.Text = tRecu.Text
        'If tRecu.Text.Contains(".") = True Then
        '    Dim x As Integer
        '    x = InStr(tRecu.Text, ".")
        '    If tRecu.Text.Length - x = 1 Then
        '        tRecu.Text = tRecu.Text + ("00")
        '    ElseIf tRecu.Text.Length - x = 2 Then
        '        tRecu.Text = tRecu.Text + ("0")
        '    End If
        'Else
        '    tRecu.Text = tRecu.Text + ".000"
        'End If

    End Sub

    Private Sub tRecu_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRecu.KeyDown




        If e.KeyCode = Keys.Enter Then
            tMotPasse.Focus()
        End If






        'If e.KeyCode = Keys.F5 Then
        '    If chbOrdonnancier.Checked = True Then
        '        chbOrdonnancier.Checked = False
        '    Else
        '        chbOrdonnancier.Checked = True
        '    End If

        'End If
    End Sub

    Private Sub tRecu_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tRecu.KeyPress
        If IsNumeric(e.KeyChar) = False And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "," And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "." Then
            e.Handled = True
        End If
    End Sub

    Private Sub tRecu_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRecu.KeyUp
        Dim position As Integer = 0

        If e.KeyCode = Keys.Enter Then
            tRecu.Text = Format(CDec(tRecu.Value), "# ##0.000")
            tRecu.SelectAll()

            ''tMotPasse.Focus()

        End If

        '------------ pour le probleme du double points 
        If e.KeyCode = Keys.Decimal Then
            position = InStr(tRecu.Text, ".")
            tRecu.Text = tRecu.Text.Substring(0, position)
            tRecu.Select(tRecu.Text.Length, 0)
            Exit Sub
        End If
        If tRecu.Text.Contains(".") Then
            position = InStr(tRecu.Text, ".")
            If tRecu.Text.Length - position > 3 Then
                tRecu.Text = tRecu.Text.Substring(0, position + 3)
                tRecu.Select(tRecu.Text.Length, 0)
            End If
        End If
        '------------ pour le probleme d'une lettre
        If IsNumeric(e.KeyValue) = False Then
            If e.KeyValue <> "." Or e.KeyValue <> "," Then
                tRecu.Text = tRecu.Text.Substring(0, tRecu.Text.Length - 1)
            End If

        Else
            If tRecu.Text.Contains(".") Then
                If tRecu.Text.Length - InStr(tRecu.Text, ".") > 3 Then
                    tRecu.Text = tRecu.Text.Substring(0, InStr(tRecu.Text, ".") + 3)
                    tRecu.Select(tRecu.Text.Length, 0)
                    tMotPasse.Focus()
                End If
            End If
        End If
        '------------ pour afficher le label rendu 
        Dim TestConversion As Double = 0.0

        If tRecu.Text <> "" Then
            Try   ' test si un valeur numerique ou non
                TestConversion = Math.Round(CDbl(tRecu.Text), 3)
            Catch ex As Exception
                MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
                tRecu.Text = "0.000"
                tRecu.Focus()
                tRecu.SelectionLength = tRecu.Text.Length

                'En cas de retour en bloque le text recu
                If Not tRecu.Enabled Then
                    grpMotDePasse.Focus()
                    tMotPasse.Focus()
                End If

                Exit Sub
            End Try

            If CDbl(tRecu.Text) >= MontantVente Then
                lRendu.Text = Math.Round((CDbl(tRecu.Text) - MontantVente), 3).ToString
                lRenduAffiche.ForeColor = Color.Green
                lRenduAffiche.Text = "Rendu"
                lRendu.ForeColor = Color.Green

                renduMnt = lRendu.Text

            ElseIf (tRecu.Text) < MontantVente Then
                lRendu.Text = Math.Round((MontantVente - CDbl(tRecu.Text)), 3).ToString
                lRenduAffiche.Text = "Crédit"
                lRenduAffiche.ForeColor = Color.Red
                lRendu.ForeColor = Color.Red

                renduMnt = 0.0

            Else
                lRendu.Text = "0.000"
                lRenduAffiche.Text = "Crédit"
                lRenduAffiche.ForeColor = Color.Black
                lRendu.ForeColor = Color.Black

                renduMnt = 0.0

            End If
        Else
            lRendu.Text = "0.000"
            lRenduAffiche.Text = "Crédit"
            lRendu.ForeColor = Color.Red

            renduMnt = 0.0

        End If


    End Sub

    Private Sub tRecu_Layout(ByVal sender As Object, ByVal e As System.Windows.Forms.LayoutEventArgs) Handles tRecu.Layout

    End Sub

    Private Sub tRecu_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tRecu.LostFocus

        tRecu.Value = tRecu.Text
        If tRecu.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(tRecu.Text, ".")
            If tRecu.Text.Length - x = 1 Then
                tRecu.Text = tRecu.Text + ("00")
            ElseIf tRecu.Text.Length - x = 2 Then
                tRecu.Text = tRecu.Text + ("0")
            ElseIf tRecu.Text.Length - x = 0 Then
                tRecu.Text = tRecu.Text + ("000")
            End If
        Else
            '''''''''tRecu.Text = tRecu.Text + ".000"
            tRecu.Value = tRecu.Text + ".000"
        End If



    End Sub

    Private Sub tRecu_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tRecu.TextChanged
        'Dim TestConversion As Double = 0.0

        'If tRecu.Text <> "" Then
        '    Try   ' test si un valeur numerique ou non
        '        TestConversion = Math.Round(CDbl(tRecu.Text), 3)
        '    Catch ex As Exception
        '        MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
        '        tRecu.Text = "0.000"
        '        tRecu.Focus()
        '        tRecu.SelectionLength = tRecu.Text.Length
        '        Exit Sub
        '    End Try

        '    If CDbl(tRecu.Text) > MontantVente Then
        '        lRendu.Text = Math.Round((CDbl(tRecu.Text) - MontantVente), 3).ToString
        '        lRenduAffiche.ForeColor = Color.Green
        '        lRenduAffiche.Text = "Rendu"
        '        lRendu.ForeColor = Color.Green
        '    ElseIf (tRecu.Text) < MontantVente Then
        '        lRendu.Text = Math.Round((MontantVente - CDbl(tRecu.Text)), 3).ToString
        '        lRenduAffiche.Text = "Crédit"
        '        lRenduAffiche.ForeColor = Color.Red
        '        lRendu.ForeColor = Color.Red
        '    End If
        'Else
        '    lRendu.Text = "0.000"
        '    lRenduAffiche.Text = "Crédit"
        '    lRendu.ForeColor = Color.Red
        'End If

        Dim position As Integer = 0




        If tRecu.Text <> "" Then
            If tRecu.Text.Contains(".") Then
                position = InStr(tRecu.Text, ".")
                If tRecu.Text.Length - position > 3 Then
                    tRecu.Text = tRecu.Text.Substring(0, position + 3)
                    tRecu.Select(tRecu.Text.Length, 0)
                End If
            End If

            Try

                If CDbl(tRecu.Text) > MontantVente Then
                    lRendu.Text = Math.Round((CDbl(tRecu.Text) - MontantVente), 3).ToString
                    lRenduAffiche.ForeColor = Color.Green
                    lRenduAffiche.Text = "Rendu"
                    lRendu.ForeColor = Color.Green

                    renduMnt = lRendu.Text

                ElseIf (tRecu.Text) < MontantVente Then
                    lRendu.Text = Math.Round((MontantVente - CDbl(tRecu.Text)), 3).ToString
                    lRenduAffiche.Text = "Crédit"
                    lRenduAffiche.ForeColor = Color.Red
                    lRendu.ForeColor = Color.Red

                    renduMnt = 0.0

                Else
                    lRendu.Text = "0.000"
                    lRenduAffiche.Text = "Crédit"
                    lRenduAffiche.ForeColor = Color.Black
                    lRendu.ForeColor = Color.Black

                    renduMnt = 0.0

                End If
            Catch ex As Exception
            End Try
        Else
            lRendu.Text = "0.000"
            lRenduAffiche.Text = "Crédit"
            lRendu.ForeColor = Color.Red
        End If
    End Sub
    Private Sub tMotPasse_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tMotPasse.KeyDown
        If e.KeyCode = Keys.Enter Then
            bOk.Focus()
        End If
    End Sub

    Private Sub tMotPasse_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tMotPasse.KeyUp

        If e.KeyCode = Keys.F3 Then
            'bOk_Click(sender, e)
            bOk.Focus()
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F5 Then
            'If chbOrdonnancier.Checked = True Then
            '    chbOrdonnancier.Checked = False
            'Else
            '    chbOrdonnancier.Checked = True
            'End If

            'Exit Sub
        End If
    End Sub


    Private Sub tMotPasse_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tMotPasse.TextChanged

    End Sub

    Private Sub lRendu_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lRendu.Click

    End Sub

    Private Sub lRendu_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lRendu.TextChanged
        lRendu.Text = lRendu.Text
        If lRendu.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lRendu.Text, ".")
            If lRendu.Text.Length - x = 1 Then
                lRendu.Text = lRendu.Text + ("00")
            ElseIf lRendu.Text.Length - x = 2 Then
                lRendu.Text = lRendu.Text + ("0")
            End If
        Else
            lRendu.Text = lRendu.Text + ".000"
        End If
    End Sub

    Private Sub lMontant_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lMontant.Click

    End Sub

    Private Sub lMontant_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lMontant.TextChanged
        lMontant.Text = lMontant.Text
        If lMontant.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lMontant.Text, ".")
            If lMontant.Text.Length - x = 1 Then
                lMontant.Text = lMontant.Text + ("00")
            ElseIf lMontant.Text.Length - x = 2 Then
                lMontant.Text = lMontant.Text + ("0")
            End If
        Else
            lMontant.Text = lMontant.Text + ".000"
        End If
    End Sub

    Private Sub bPaiementMultiple_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If ControleDAcces(5, "PAIEMENT_MULTIPLE") = "False" Then
            Exit Sub
        End If

        TypePaiement = "Multiple"

        paiement = "NO"
        utilistaeur = 0
        MontantRecu = 0.0
        CodeModePaiement = 0
        DateEcheance = " 01/01/1900"
        NumeroCheque = ""
        Me.Hide()
    End Sub

    Private Sub bTotaliseur_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim UtilisateurCode As Integer = 0
        Dim NumeroReglement As Integer = 0
        Dim DateEcheance As String = "01/01/1900"

        'If tMotPasse.Text = "" Then
        '    tMotPasse.Focus()
        '    MsgBox("Vous devez insérer votre mot de passe !", MsgBoxStyle.Critical, "Erreur")
        '    Exit Sub
        'End If
        If cmbModePaiement.Text = "" Then
            tMotPasse.Focus()
            MsgBox("Vous devez sélectionner le mode de paiement !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        StrSQL = "SELECT COUNT([CodeUtilisateur]) FROM UTILISATEUR WHERE Supprime=0 AND [MotPass]='" + tMotPasse.Text + "'"
        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL
        Try
            UtilisateurCode = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        If UtilisateurCode = 0 Then
            tMotPasse.Text = ""
            MsgBox("Mot de passe erroné !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If
        paiement = "OK"
        utilistaeur = UtilisateurCode
        MontantRecu = CDbl(tRecu.Text)
        CodeModePaiement = cmbModePaiement.SelectedValue
        DateEcheance = tEcheance.Text.ToString
        NumeroCheque = tNumero.Text
        Nom = tNom.Text
        TypePaiement = "Totaliseur"


        Me.Hide()
    End Sub

    Private Sub cmbModePaiement_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbModePaiement.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F5 Then
            'If chbOrdonnancier.Checked = True Then
            '    chbOrdonnancier.Checked = False
            'Else
            '    chbOrdonnancier.Checked = True
            'End If
            Exit Sub
        End If

        If e.KeyCode = Keys.Enter And (cmbModePaiement.Text = "ESPECE" Or cmbModePaiement.Text = "CREDIT") Then
            tRecu.Focus()

            'En cas de retour en bloque le text recu
            If Not tRecu.Enabled Then
                grpMotDePasse.Focus()
                tMotPasse.Focus()
            End If

        Else
            tEcheance.Focus()
        End If
    End Sub

    Private Sub cmbModePaiement_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbModePaiement.TextChanged
        'If cmbModePaiement.Text = "CREDIT" And CodeClient = "CLIPASS" Then
        '    MsgBox("Pas de crédit pour un Client Passger !", MsgBoxStyle.Critical, "Erreur")
        '    cmbModePaiement.Text = "ESPECE"
        '    Exit Sub
        'End If

        If cmbModePaiement.Text = "CHEQUE" Then
            tEcheance.Enabled = True
            tNumero.Enabled = True
            tNom.Enabled = True
            tEcheance.Value = Date.Now.Day.ToString + "/" + Date.Now.Month.ToString + "/" + Date.Now.Year.ToString
            'ElseIf cmbModePaiement.Text = "CARTE" Then
            '    tEcheance.Enabled = True
            '    tNumero.Enabled = True
            '    tNom.Enabled = True
            '    tEcheance.Value = Date.Now.Day.ToString + "/" + Date.Now.Month.ToString + "/" + Date.Now.Year.ToString
        ElseIf cmbModePaiement.Text = "TRAITE" Then
            tEcheance.Enabled = True
            tNumero.Enabled = True
            tNom.Enabled = True
            tEcheance.Value = Date.Now.Day.ToString + "/" + Date.Now.Month.ToString + "/" + Date.Now.Year.ToString
        ElseIf cmbModePaiement.Text = "CARTE" Then
            tEcheance.Enabled = False
            tNumero.Enabled = True
            tNom.Enabled = True
        Else
            tEcheance.Enabled = False
            tNumero.Enabled = False
            tNom.Enabled = False

        End If

        If cmbModePaiement.Text = "CREDIT" And tRecu.Enabled Then
            tRecu.Value = 0
            tRecu.Text = "0"
        End If
    End Sub

    Private Sub cmbModePaiement_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbModePaiement.Validated
        If cmbModePaiement.Text = "CHEQUE" Then
            tEcheance.Enabled = True
            tNumero.Enabled = True
            tNom.Enabled = True

            tEcheance.Value = Date.Now.Day.ToString + "/" + Date.Now.Month.ToString + "/" + Date.Now.Year.ToString
        ElseIf cmbModePaiement.Text = "CARTE" Then
            tEcheance.Enabled = False 'True
            tNumero.Enabled = True
            tEcheance.Value = Date.Now.Day.ToString + "/" + Date.Now.Month.ToString + "/" + Date.Now.Year.ToString
        ElseIf cmbModePaiement.Text = "TRAITE" Then
            tEcheance.Enabled = True
            tNumero.Enabled = True
            tNom.Enabled = True
        Else
            tEcheance.Enabled = False
            tNumero.Enabled = False
            tNom.Enabled = False

        End If
    End Sub

    Private Sub tEcheance_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            tNumero.Focus()
        End If
    End Sub

    Private Sub tEcheance_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tNumero_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNumero.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F5 Then
            'If chbOrdonnancier.Checked = True Then
            '    chbOrdonnancier.Checked = False
            'Else
            '    chbOrdonnancier.Checked = True
            'End If
            Exit Sub
        End If

        If e.KeyCode = Keys.Enter Then
            tNom.Focus()
        End If
    End Sub

    Private Sub tNumero_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNumero.TextChanged

    End Sub

    Private Sub tBanque_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            tRecu.Focus()

            'En cas de retour en bloque le text recu
            If Not tRecu.Enabled Then
                grpMotDePasse.Focus()
                tMotPasse.Focus()
            End If

        End If
    End Sub

    Private Sub tBanque_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tNom_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNom.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F5 Then
            'If chbOrdonnancier.Checked = True Then
            '    chbOrdonnancier.Checked = False
            'Else
            '    chbOrdonnancier.Checked = True
            'End If
            Exit Sub
        End If

        If e.KeyCode = Keys.Enter Then
            tRecu.Focus()

            'En cas de retour en bloque le text recu
            If Not tRecu.Enabled Then
                grpMotDePasse.Focus()
                tMotPasse.Focus()
            End If

        End If
    End Sub

    Private Sub tNom_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNom.TextChanged

    End Sub

    Private Sub lRetour5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lRetour5.Click

    End Sub

    Private Sub lRetour5_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lRetour5.TextChanged
        lRetour5.Text = lRetour5.Text
        If lRetour5.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lRetour5.Text, ".")
            If lRetour5.Text.Length - x = 1 Then
                lRetour5.Text = lRetour5.Text + ("00")
            ElseIf lRetour5.Text.Length - x = 2 Then
                lRetour5.Text = lRetour5.Text + ("0")
            End If
        Else
            lRetour5.Text = lRetour5.Text + ".000"
        End If
    End Sub

    Private Sub lRetour10_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lRetour10.Click

    End Sub

    Private Sub lRetour10_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lRetour10.TextChanged
        lRetour10.Text = lRetour10.Text
        If lRetour10.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lRetour10.Text, ".")
            If lRetour10.Text.Length - x = 1 Then
                lRetour10.Text = lRetour10.Text + ("00")
            ElseIf lRetour10.Text.Length - x = 2 Then
                lRetour10.Text = lRetour10.Text + ("0")
            End If
        Else
            lRetour10.Text = lRetour10.Text + ".000"
        End If
    End Sub

    Private Sub lRetour20_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lRetour20.Click

    End Sub

    Private Sub lRetour20_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lRetour20.TextChanged
        lRetour20.Text = lRetour20.Text
        If lRetour20.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lRetour20.Text, ".")
            If lRetour20.Text.Length - x = 1 Then
                lRetour20.Text = lRetour20.Text + ("00")
            ElseIf lRetour20.Text.Length - x = 2 Then
                lRetour20.Text = lRetour20.Text + ("0")
            End If
        Else
            lRetour20.Text = lRetour20.Text + ".000"
        End If
    End Sub

    Private Sub lRetour50_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lRetour50.TextChanged
        lRetour50.Text = lRetour50.Text
        If lRetour50.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lRetour50.Text, ".")
            If lRetour50.Text.Length - x = 1 Then
                lRetour50.Text = lRetour50.Text + ("00")
            ElseIf lRetour50.Text.Length - x = 2 Then
                lRetour50.Text = lRetour50.Text + ("0")
            End If
        Else
            lRetour50.Text = lRetour50.Text + ".000"
        End If
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click

        bOk_Click(sender, e)
        ImpressionApresVente = True
    End Sub


    Private Sub tEcheance_KeyUp1(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tEcheance.KeyUp

        If e.KeyCode = Keys.F10 Then
            e.SuppressKeyPress = True
        Else
            e.SuppressKeyPress = False
        End If


        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F5 Then
            'If chbOrdonnancier.Checked = True Then
            '    chbOrdonnancier.Checked = False
            'Else
            '    chbOrdonnancier.Checked = True
            'End If
            Exit Sub
        End If

        If e.KeyCode = Keys.Enter And cmbModePaiement.Text <> "ESPECE" And cmbModePaiement.Text <> "CREDIT" Then
            tNumero.Focus()
        End If

    End Sub


    Private Sub bTotaliseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)

        If e.KeyCode = Keys.F10 Then
            e.SuppressKeyPress = True
        Else
            e.SuppressKeyPress = False
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F5 Then
            'If chbOrdonnancier.Checked = True Then
            '    chbOrdonnancier.Checked = False
            'Else
            '    chbOrdonnancier.Checked = True
            'End If
            Exit Sub
        End If
    End Sub

    Private Sub bPaiementMultiple_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)

        If e.KeyCode = Keys.F10 Then
            e.SuppressKeyPress = True
        Else
            e.SuppressKeyPress = False
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F5 Then
            'If chbOrdonnancier.Checked = True Then
            '    chbOrdonnancier.Checked = False
            'Else
            '    chbOrdonnancier.Checked = True
            'End If
            Exit Sub
        End If
    End Sub

    Private Sub bOk_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bOk.KeyUp

        If e.KeyCode = Keys.F10 Then
            e.SuppressKeyPress = True
        Else
            e.SuppressKeyPress = False
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F5 Then
            'If chbOrdonnancier.Checked = True Then
            '    chbOrdonnancier.Checked = False
            'Else
            '    chbOrdonnancier.Checked = True
            'End If
            Exit Sub
        End If
    End Sub

    Private Sub bAnnuler_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bAnnuler.KeyUp

    End Sub

    Private Sub bImprimer_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bImprimer.KeyUp

        If e.KeyCode = Keys.F10 Then
            e.SuppressKeyPress = True
        Else
            e.SuppressKeyPress = False
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F5 Then
            'If chbOrdonnancier.Checked = True Then
            '    chbOrdonnancier.Checked = False
            'Else
            '    chbOrdonnancier.Checked = True
            'End If
            Exit Sub
        End If
    End Sub

    Private Sub GroupBox4_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GroupBox4.Enter

    End Sub

    Private Sub GroupBox4_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles GroupBox4.KeyUp

        If e.KeyCode = Keys.F10 Then
            e.SuppressKeyPress = True
        Else
            e.SuppressKeyPress = False
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F5 Then
            'If chbOrdonnancier.Checked = True Then
            '    chbOrdonnancier.Checked = False
            'Else
            '    chbOrdonnancier.Checked = True
            'End If
            Exit Sub
        End If
    End Sub


    Private Sub bPlus_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        AdditionOrdonnanceVente = AdditionOrdonnanceVente + lMontant.Text

    End Sub

    Private Sub bMoins_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        AdditionOrdonnanceVente = AdditionOrdonnanceVente - lMontant.Text

    End Sub

    Private Sub bEffacer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        AdditionOrdonnanceVente = 0.0

    End Sub

    Private Sub GroupBox2_Enter(sender As Object, e As EventArgs) Handles GroupBox2.Enter

    End Sub

    Private Sub Label9_Click(sender As Object, e As EventArgs) Handles Label9.Click

    End Sub

    Private Sub Label3_Click(sender As Object, e As EventArgs) Handles Label3.Click

    End Sub

    Private Sub grpMotDePasse_Enter(sender As Object, e As EventArgs) Handles grpMotDePasse.Enter

    End Sub

    Private Sub Clignote1_Click(sender As Object, e As EventArgs) Handles Clignote1.Click

    End Sub

    Private Sub chbOrdonnancier_CheckedChanged(sender As Object, e As EventArgs) Handles chbOrdonnancier.CheckedChanged

    End Sub
End Class