﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="ErrorManagementModel.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2008" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
  <EntityContainer Name="ErrorManagementModelStoreContainer">
    <EntitySet Name="MESSAGE_ERROR" EntityType="ErrorManagementModel.Store.MESSAGE_ERROR" store:Type="Tables" Schema="dbo" />
  </EntityContainer>
  <EntityType Name="MESSAGE_ERROR">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
    <Property Name="Module" Type="varchar" MaxLength="500" />
    <Property Name="Date" Type="date" />
    <Property Name="LibellePoste" Type="varchar" MaxLength="500" />
    <Property Name="CodePersonnel" Type="varchar" MaxLength="500" />
    <Property Name="Message" Type="varchar(max)" />
  </EntityType>
</Schema>