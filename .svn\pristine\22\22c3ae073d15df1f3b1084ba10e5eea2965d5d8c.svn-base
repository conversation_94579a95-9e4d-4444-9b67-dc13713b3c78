﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fReleveeMutuelle
    Dim x As Integer
    Dim cmdReleve As New SqlCommand
    Dim daReleve As New SqlDataAdapter
    Dim dsReleve As New DataSet
    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "116" And bAjouterReleve.Enabled = True Then
            bAjouterReleve_Click(sender, e)
        End If
        If argument = "117" And bRechercher.Enabled = True Then
            bRechercher_Click(sender, e)
        End If
        If argument = "118" And bSuprimerreleve.Enabled = True Then
            bSuprimerreleve_Click(sender, e)
        End If
        If argument = "119" And bModifierReleve.Enabled = True Then
            bModifierReleve_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If

    End Sub
    Public Sub AfficherReleve()

        Dim I As Integer
        Dim Cond As String = "1=1"

        If (dsReleve.Tables.IndexOf("RELEVE") > -1) Then
            dsReleve.Tables("RELEVE").Clear()
        End If

        'Composer la condition de la requête 
        If cmbMutuelle.Text <> "" And cmbMutuelle.SelectedValue <> Nothing Then
            Cond += " AND RELEVE_MUTUELLE.CodeMutuelle='" + cmbMutuelle.SelectedValue.ToString + "'"
        End If
        If tNumero.Text <> "" Then
            Cond += " AND RELEVE_MUTUELLE.NumeroReleve = '" + tNumero.Text + "'"
        End If

        If dpDateDebut.Text <> "" And dpDateDebut.Text.Length = 10 Then
            Cond += " AND RELEVE_MUTUELLE.Date > '" + dpDateDebut.Text + "'"
        End If
        If dpDateFin.Text <> "" And dpDateFin.Text.Length = 10 Then
            Cond += "AND RELEVE_MUTUELLE.Date < '" + dpDateFin.Text + " 23:59:59'"
        End If

        'cmdReleve.CommandText = " SELECT  RELEVE_MUTUELLE.NumeroReleve," + _
        '                        " NomMutuelle," + _
        '                        " Date, " + _
        '                        " DateDebut,  " + _
        '                        " DateFin, " + _
        '                        " Total, " + _
        '                        " Montant, " + _
        '                        " Montant-Reste AS regle," + _
        '                        " Reste " + _
        '                        " FROM RELEVE_MUTUELLE" + _
        '                        " LEFT OUTER JOIN MUTUELLE ON MUTUELLE.CodeMutuelle=RELEVE_MUTUELLE.CodeMutuelle " + _
        '                        " WHERE " + Cond + _
        '                        " AND NomMutuelle<>'COMPTOIR' ORDER BY RELEVE_MUTUELLE.Date DESC"

        cmdReleve.CommandText = " SELECT  " + _
                                "	RELEVE_MUTUELLE.NumeroReleve,  " + _
                                "	NomMutuelle,  " + _
                                "	Date,  " + _
                                "	DateDebut,  " + _
                                "	DateFin,  " + _
                                "	Total,  " + _
                                "	RELEVE_MUTUELLE.Montant,  " + _
                                "	(RELEVE_MUTUELLE.Montant - Reste - ISNULL(REGLEMENT_MUTUELLE1.MontantRetenueSource,0) ) AS regle, " + _
                                "	ISNULL(REGLEMENT_MUTUELLE1.MontantRetenueSource,0) AS MontantRetenueSource,  " + _
                                "	(Reste) AS Reste" + _
                                " FROM  " + _
                                "	RELEVE_MUTUELLE " + _
                                "	LEFT OUTER JOIN MUTUELLE ON MUTUELLE.CodeMutuelle=RELEVE_MUTUELLE.CodeMutuelle  " + _
                                "	LEFT JOIN (SELECT NumeroReleve, MAX(Montant) as Montant, SUM(MontantRegle) as MontantRegle, MAX(MontantRetenueSource) as MontantRetenueSource " + _
                                "			FROM REGLEMENT_MUTUELLE  " + _
                                "			LEFT JOIN REGLEMENT_MUTUELLE_VENTE ON REGLEMENT_MUTUELLE_VENTE.NumeroReglementMutuelle = REGLEMENT_MUTUELLE.NumeroReglementMutuelle " + _
                                "			GROUP BY NumeroReleve  " + _
                                "			) AS REGLEMENT_MUTUELLE1 ON REGLEMENT_MUTUELLE1.NumeroReleve = RELEVE_MUTUELLE.NumeroReleve  " + _
                                " WHERE " + Cond + _
                                " AND NomMutuelle<>'COMPTOIR' ORDER BY RELEVE_MUTUELLE.Date DESC"

        cmdReleve.Connection = ConnectionServeur
        daReleve = New SqlDataAdapter(cmdReleve)
        daReleve.Fill(dsReleve, "RELEVE")

        With gReleves
            .Columns.Clear()
            .DataSource = dsReleve
            .DataMember = "RELEVE"
            .Rebind(False)
            .Columns("NumeroReleve").Caption = "Num relevé"
            .Columns("NomMutuelle").Caption = "Mutuelle"
            .Columns("Date").Caption = "Date"
            .Columns("DateDebut").Caption = "Date début"
            .Columns("DateFin").Caption = "Date fin"
            .Columns("Montant").Caption = "Montant à rembourser"
            .Columns("Total").Caption = "Total"
            .Columns("regle").Caption = "Déjà réglé"
            .Columns("Reste").Caption = "Reste"
            .Columns("MontantRetenueSource").Caption = "Retenue à la Source"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Montant").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("regle").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Reste").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("NumeroReleve").Width = 140
            .Splits(0).DisplayColumns("Date").Width = 100
            .Splits(0).DisplayColumns("NomMutuelle").Width = 150
            .Splits(0).DisplayColumns("DateDebut").Width = 100
            .Splits(0).DisplayColumns("DateFin").Width = 100
            .Splits(0).DisplayColumns("Montant").Width = 140
            .Splits(0).DisplayColumns("Total").Width = 140
            .Splits(0).DisplayColumns("regle").Width = 140
            .Splits(0).DisplayColumns("Reste").Width = 100

            .Splits(0).DisplayColumns("MontantRetenueSource").Width = 130
            .Splits(0).DisplayColumns("MontantRetenueSource").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gReleves)
        End With

        gReleves.MoveRelative(x)

    End Sub

    Private Sub bAjouterReleve_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterReleve.Click
        Dim fNouveauRelevee As New fCritereDuReleveMutuelle
        fNouveauRelevee.ShowDialog()
        AfficherReleve()
    End Sub
    Public Sub Init()
        Dim StrSQL As String = ""
        x = 0

        'chargement des Mutuelle
        StrSQL = "SELECT CodeMutuelle,NomMutuelle FROM MUTUELLE ORDER BY NomMutuelle ASC"
        cmdReleve.Connection = ConnectionServeur
        cmdReleve.CommandText = StrSQL
        daReleve = New SqlDataAdapter(cmdReleve)
        daReleve.Fill(dsReleve, "MUTUELLE")
        cmbMutuelle.DataSource = dsReleve.Tables("MUTUELLE")
        cmbMutuelle.ValueMember = "CodeMutuelle"
        cmbMutuelle.DisplayMember = "NomMutuelle"
        cmbMutuelle.ColumnHeaders = False
        cmbMutuelle.Splits(0).DisplayColumns("CodeMutuelle").Visible = False
        cmbMutuelle.Splits(0).DisplayColumns("NomMutuelle").Width = 10
        cmbMutuelle.ExtendRightColumn = True

        dpDateDebut.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dpDateDebut.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dpDateFin.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dpDateFin.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        AfficherReleve()
    End Sub

    Private Sub bModifierReleve_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifierReleve.Click
        If gReleves.RowCount = 0 Then
            MsgBox("Liste Vide", MsgBoxStyle.OkOnly)
        Else

            Dim MyFicheReleveMutuelle As New fFicheReleveeMutuelle
            MyFicheReleveMutuelle.NuemroReleve = gReleves(gReleves.Row, "NumeroReleve")
            'MyFicheReleveMutuelle.Init()
            MyFicheReleveMutuelle.ShowDialog()
            MyFicheReleveMutuelle.Close()
            MyFicheReleveMutuelle.Dispose()
            x = gReleves.Row
            AfficherReleve()

        End If
    End Sub

    Private Sub bSuprimerreleve_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSuprimerreleve.Click
        Dim cmd As New SqlCommand
        Dim NumeroReleve As String = ""
        Dim StrSQL As String = ""
        Dim NombreDesVenteRegle As Integer = 0

        NumeroReleve = Quote(gReleves(gReleves.Row, "NumeroReleve"))
        Connect()
        If gReleves.RowCount = 0 Then
            MsgBox("Liste Vide", MsgBoxStyle.OkOnly)
        Else
            'test si ce relevé est reglé ou nn si c est le cas il faut interdir sa suppression

            StrSQL = " SELECT COUNT(NumeroVente) FROM RELEVE_MUTUELLE_DETAILS WHERE NumeroReleve=" _
                      + NumeroReleve + " AND Regle <> 0 "
            cmdReleve.Connection = ConnectionServeur
            cmdReleve.CommandText = StrSQL
            Try
                NombreDesVenteRegle = cmdReleve.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If NombreDesVenteRegle <> 0 Then
                MsgBox("Impossible de supprimer un relevé reglé !", MsgBoxStyle.Critical, "Erreur")
                Exit Sub
            End If



            If MsgBox("Voulez vous vraiment supprimer ce relevé  ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM RELEVE_MUTUELLE_DETAILS WHERE NumeroReleve = " + NumeroReleve
                    cmd.ExecuteNonQuery()
                    AfficherReleve()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM RELEVE_MUTUELLE WHERE NumeroReleve = " + NumeroReleve
                    cmd.ExecuteNonQuery()
                    AfficherReleve()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub

    Private Sub fReleveeMutuelle_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub bRechercher_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRechercher.Click
        cmbMutuelle.Focus()
    End Sub

    Private Sub tNumero_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tNumero.GotFocus
        tNumero.Text = System.DateTime.Now.Year.ToString + "/"
        tNumero.Focus()
        tNumero.Select(tNumero.Text.Length, 0)
    End Sub

    Private Sub tNumero_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNumero.KeyUp
        If e.KeyCode = Keys.Enter Then
            If tNumero.Text.Length < 11 And tNumero.Text <> System.DateTime.Now.Year.ToString + "/" Then
                tNumero.Text = tNumero.Text.Substring(0, 5) + tNumero.Text.Substring(5, tNumero.Text.Length - 5).PadLeft(6, "0")
                AfficherReleve()
            Else
                tNumero.Text = ""
            End If
            dpDateDebut.Focus()
        End If
    End Sub

    Private Sub tNumero_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNumero.TextChanged

    End Sub

    Private Sub cmbMutuelle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMutuelle.KeyUp
        If e.KeyCode = Keys.Enter Then
            If cmbMutuelle.WillChangeToText <> "" Then
                cmbMutuelle.Text = cmbMutuelle.WillChangeToText
            End If
            tNumero.Focus()
        Else
            cmbMutuelle.OpenCombo()
        End If
    End Sub

    Private Sub cmbMutuelle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbMutuelle.TextChanged
        AfficherReleve()
    End Sub

    Private Sub dpDateDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dpDateDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            dpDateFin.Focus()
        End If
    End Sub

    Private Sub dpDateDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dpDateDebut.TextChanged

    End Sub

    Private Sub dpDateFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dpDateFin.KeyUp
        AfficherReleve()
    End Sub

    Private Sub dpDateFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dpDateFin.TextChanged

    End Sub
End Class