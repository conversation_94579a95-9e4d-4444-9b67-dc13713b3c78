//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class EMPRUNT
    {
        public EMPRUNT()
        {
            this.EMPRUNT_DETAILS = new HashSet<EMPRUNT_DETAILS>();
        }
    
        public string NumeroEmprunt { get; set; }
        public string CodePharmacie { get; set; }
        public string CodePersonnel { get; set; }
        public System.DateTime Date { get; set; }
        public decimal TotalHT { get; set; }
        public decimal TotalTVA { get; set; }
        public decimal TotalTTC { get; set; }
        public bool Vider { get; set; }
    
        public virtual ICollection<EMPRUNT_DETAILS> EMPRUNT_DETAILS { get; set; }
        public virtual PHARMACIE PHARMACIE { get; set; }
        public virtual UTILISATEUR UTILISATEUR { get; set; }
    }
}
