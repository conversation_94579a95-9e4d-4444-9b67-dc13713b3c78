<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
 <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <!-- Diagram content (shape and connector positions) -->
    <edmx:Diagrams>
      <Diagram DiagramId="1f6952de35274ae583990275b771e572" Name="Diagramme1">
        <EntityTypeShape EntityType="StockManagementModel.ARTICLE" PointX="3.5" PointY="5.625" Width="1.5" IsExpanded="false" />
        <EntityTypeShape EntityType="StockManagementModel.INVENTAIRE" Width="1.5" PointX="1" PointY="3.75" IsExpanded="true" />
        <EntityTypeShape EntityType="StockManagementModel.MOUVEMENT_ARTICLE" Width="2.125" PointX="0.5" PointY="1.625" IsExpanded="false" />
        <EntityTypeShape EntityType="StockManagementModel.MOUVEMENT_LOT_ARTICLE" Width="2.25" PointX="3" PointY="1.625" IsExpanded="false" />
        <EntityTypeShape EntityType="StockManagementModel.V_List_ArticlesRecherche" Width="2.125" PointX="3" PointY="2.125" IsExpanded="false" />
        <EntityTypeShape EntityType="StockManagementModel.V_List_Inventaire" Width="1.75" PointX="3.375" PointY="3.75" IsExpanded="false" />
        <AssociationConnector Association="StockManagementModel.INVENTAIREV_List_Inventaire" />
        <EntityTypeShape EntityType="StockManagementModel.V_List_NouvelInventaire" Width="2.125" PointX="3.375" PointY="4.625" IsExpanded="false" />
        <EntityTypeShape EntityType="StockManagementModel.LOT_ARTICLE" PointX="1" PointY="5.625" Width="1.875" IsExpanded="false" />
        <AssociationConnector Association="StockManagementModel.FK_LOTS_ARTICLE_ARTICLE" />
        <EntityTypeShape EntityType="StockManagementModel.V_List_LotArticleInventaire" Width="2.25" PointX="5.625" PointY="5.5" IsExpanded="false" />
        <EntityTypeShape EntityType="StockManagementModel.INVENTAIRE_DETAILS" Width="1.5" PointX="3.5" PointY="11.25" />
        <AssociationConnector Association="StockManagementModel.FK_INVENTAIRE_DETAILS_LOT_ARTICLE" />
        </Diagram>
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>