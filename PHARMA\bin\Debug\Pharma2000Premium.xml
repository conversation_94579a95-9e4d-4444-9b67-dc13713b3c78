﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
Pharma2000Premium
</name>
</assembly>
<members>
<member name="P:Pharma2000Premium.ServiceBCB.bcbSecurity.codeEditeur">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSecurity.idPS">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSecurity.secretEditeur">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbSecurity">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getEtablissementResponse.result">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getEtablissementResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEtablissement.adresse1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEtablissement.adresse2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEtablissement.code">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEtablissement.codePostal">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEtablissement.dateFin">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEtablissement.fax">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEtablissement.libelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEtablissement.lstIp">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEtablissement.mail">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEtablissement.tel">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEtablissement.ville">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getEtablissement.idEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getEtablissement.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.addEtablissementResponse.result">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.addEtablissementResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.addEtablissement.etablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.addEtablissement.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.addEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.anmConc">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.anmDapp">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.anmDencom">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.anmForma">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.anmNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.anmPrescr">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.biologic">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.cgNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.clsinsulin">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.codComerc">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.codifatc">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.compNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.concentrat">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.dataOmo">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.denIntern">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.denumire">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.forma">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.fractionab">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.gratNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.hasbioech">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.isbrand">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.listaNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.listeNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.localCodeForSearch1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.localCodeForSearch2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.localCodeForSearch3">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.lspeNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.msfNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.pens600">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.prescri">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.pretAm">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.pretC2Ne">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.pretRid">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.prezentare">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.procNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.producator">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.refCgNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.refgNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.socialNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.speciallaw">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.sumacNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.tabelNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.tara">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.um">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.ut">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.valabNew">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.versiune">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.etrInfoProduitsRo">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduit_ro.idProduits">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduit_ro.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getInformationProduit_ro">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduitEtrResponse.produitResult">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getInformationProduitEtrResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitEtr.infosEtr">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbProduitEtr">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.agrementCollectivites">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.attributCasher">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.baseRemboursementSS">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.cip13">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.cip7">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.cip7fictif">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.cipProduitRemplacant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.code13Referent">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.codeForme">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.codeGroupeGenerique">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.codeIdentifiantSpecialite">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.codePoidsVolume">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.codeStatut">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.codeStockage">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.compositionCommentaire">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.compositionDateAMM">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.compositionIdExprimePar">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.compositionLibelleExprimePar">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.contenance">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.dateAMM">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.dateResponsabiliteUcd">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.dateSupprime">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.dispensationCodeUnitePrise">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.dispensationLibelleUnitePrise">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.dispensationNombre">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.distributionCodeUnitePrise">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.distributionLibelleUnitePrise">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.distributionNombre">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.dopant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.dosage">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.dureePeremption">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.ean13">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.forme">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.generique">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.hauteur">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.hospitalier">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.idCategorie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.idDC">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.idDCMolecule">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.idProduitRemplacant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.idType">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.idTypeCategorie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.laboratoireExploitant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.laboratoireTitulaire">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.largeur">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libelleAbrege">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libelleActeNormeB2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libelleCasher">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libelleConditionnement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libelleCourt">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libelleDC">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libelleDCMolecule">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libelleDivers">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libelleGroupeGenerique">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libelleGroupeGeneriqueCourt">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libellePresentation">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libelleProduitRemplacant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libelleSpecialite">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.libelleStockage">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lienNoticeANSM">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lienRCP">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.liste">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.longueur">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstAsmr">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstClassesATC">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstClassesTherapeutiques">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstContresIndications">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstDetailsLPP">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstEan13">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstEffetsIndesirables">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstExcipients">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstFamilles">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstIndications">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstLiensCrat">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstPrecautionsEmplois">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstPrescription">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstPrincipesActifs">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstSmr">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstUnitesPrise">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.lstVoies">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.medicamentDC">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.medicamentException">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.medicamentT2A">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.monographieHTML">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.monographiePDF">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.numeroAMM">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.otc">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.parapharmacie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.pictogramme">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.poidsVolume">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.prescriptionRestreinte">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.prixAchatHTFabricant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.prixAchatHTGrossiste">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.prixVente">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.refHTML">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.referenceLPPR">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.referenceTIPS">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.referent">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.statut">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.stupefiant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.TFR">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.tarifResponsabiliteUcd">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.tauxSS">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.tauxTva">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.typeConditionnement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.ucd13">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.ucd7">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduit.version">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbLaboratoire.adresse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbLaboratoire.codePostal">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbLaboratoire.commentaires">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbLaboratoire.email">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbLaboratoire.id">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbLaboratoire.informations_medicales">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbLaboratoire.nom">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbLaboratoire.telecopie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbLaboratoire.telephone">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbLaboratoire.ville">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbLaboratoire.webSiteUrl">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbLaboratoire">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSmrAsmr.libelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSmrAsmr.libelleAmm">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbSmrAsmr">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbClasse.code">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbClasse.libelle">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbClasse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbMotClef.code">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbMotClef.libelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbMotClef.niveau_ci">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbMotClef.type_ci_pe">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbMotClef">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblpp.ageMaxi">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblpp.ageMini">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblpp.code">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblpp.coefficient">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblpp.dateLimite">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblpp.indications">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblpp.libelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblpp.lstHistorique">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblpp.typePrestation">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcblpp">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblppHistorique.dateArreteMinisteriel">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblppHistorique.dateDebut">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblppHistorique.dateFin">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblppHistorique.datePublicationJo">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblppHistorique.ententePrealable">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblppHistorique.naturePrestation">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblppHistorique.plv">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblppHistorique.tarifLPP">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcblppHistorique">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbExcipient.attribut">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbExcipient.commentaire">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbExcipient.effetNotoire">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbExcipient.iam">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbExcipient.id">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbExcipient.libelle">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbExcipient">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbFamille.code">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbFamille.libelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbFamille.libellePere">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbFamille.lstFils">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbFamille.niveau">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbFamille.noeudFils">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbFamille">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbLienCrat.codeComposant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbLienCrat.lienHTML">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbLienCrat.texte">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbLienCrat">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrescript.codePrescriptionParticuliere">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrescript.codeSpecialitePrescripteur">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrescript.libellePrescriptionParticuliere">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrescript.libelleSpecialitePrescripteur">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbPrescript">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrincipeActif.attribut">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrincipeActif.codeUnite">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrincipeActif.commentaire">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrincipeActif.iam">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrincipeActif.id">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrincipeActif.libelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrincipeActif.libelleUnite">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrincipeActif.quantite">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbPrincipeActif">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUnitePrise.arrondi">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUnitePrise.code">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUnitePrise.jeterLeReste">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUnitePrise.libelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUnitePrise.libelleAbrege">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUnitePrise.libellePluriel">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUnitePrise.libelleSingulier">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUnitePrise.nbDbl">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUnitePrise.nbStr">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbUnitePrise">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbVoie.code">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbVoie.codeAncien">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbVoie.codeStandard">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbVoie.libelle">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbVoie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbHtml.lstResource">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbHtml.monographie">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbHtml">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbResource.chemin">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbResource.data">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbResource.nom">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbResource">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrescriptionRestreinte.code">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrescriptionRestreinte.libelleDecret">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPrescriptionRestreinte.libelleTitre">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbPrescriptionRestreinte">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduitEtr.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduitEtr.mode">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduitEtr.pays">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduitEtr.includeRessource">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduitEtr.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getInformationProduitEtr">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getListeEtablissements.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getListeEtablissements">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbVersion.databaseVersion">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbVersion.idVersionBase">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbVersion.minimumRequiredDatabaseVersion">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbVersion.revisionBase">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbVersion.synchro">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbVersion.webServiceVersion">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbVersion">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getVersionResponse.result">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getVersionResponse">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getVersion">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsDCResponse.searchResult">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getProduitsDCResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSearchResult.listeAccessoiresGeneriques">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSearchResult.listeClasseChimique">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSearchResult.listeClassesATC">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSearchResult.listeComposants">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSearchResult.listeDC">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSearchResult.listeFamilles">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSearchResult.listeIdentProduits">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSearchResult.listeIndications">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSearchResult.listeLPP">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSearchResult.listeLaboratoires">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSearchResult.listePathologies">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSearchResult.listeProduits">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSearchResult.query">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbSearchResult">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbAccessoireGenerique.code">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbAccessoireGenerique.id">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbAccessoireGenerique.libelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbAccessoireGenerique.libelleCourt">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbAccessoireGenerique">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbClasseChimique.id">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbClasseChimique.libelle">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbClasseChimique">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbClasseATC.code">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbClasseATC.libelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbClasseATC.lstFils">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbClasseATC.niveau">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbClasseATC.noeudFils">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbClasseATC">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbComposant.afficher">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbComposant.id">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbComposant.libelle">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbComposant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbdc.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbdc.libelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbdc.supprime">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbdc">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbIdentProduit.libelle">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbIdentProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbIndication.code">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbIndication.libelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbIndication.type">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbIndication">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblppMini.code">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcblppMini.libelle">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcblppMini">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPathologie.codeMotClef">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPathologie.glossaire">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPathologie.libelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbPathologie.typeIndication">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbPathologie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.cip13">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.cip7">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.cip7Fictif">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.codeReferent">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.documentsOfficiels">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.dopant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.generique">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.homeopathie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.hospitalier">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.idCategorie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.idTypeCategorie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.libelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.libelleCourt">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.livretTherapeutique">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.medicamentDC">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.medicamentException">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.parapharmacie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.prixVenteTtc">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.referent">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.reserveHopital">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.supprime">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.ucd13">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMini.ucd7">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbProduitMini">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsDC.idDC">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsDC.baseLocation">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsDC.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getProduitsDC">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getMonographieUrlResponse.result">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getMonographieUrlResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getMonographieUrl.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getMonographieUrl.key">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getMonographieUrl.optionalMode">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getMonographieUrl">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getEffetsIndesirablesProduit.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getEffetsIndesirablesProduit.modeTri">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getEffetsIndesirablesProduit.descMode">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getEffetsIndesirablesProduit.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getEffetsIndesirablesProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.accordPrealable">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.clientCliniquePrivee">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.clientHopitalPublic">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.clientOfficinePrivee">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.codePct">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.commercialise">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.conservation">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.dateSuppression">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.designationProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.distributeur">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.dureeDeVie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.fabricant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.fournisseur">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.hospitalier">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.liste">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.localCodeForSearch1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.localCodeForSearch2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.localCodeForSearch3">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.ppt">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.prixHopital">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.prixPharmacien">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.prixPublic">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.secteur">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.tarifReference">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.tauxTva">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.veicCategorie">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.etrInfoProduitsTn">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduit_tn.idProduits">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduit_tn.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getInformationProduit_tn">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.addUserResponse.result">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.addUserResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.addUser.user">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.addUser.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.addUser">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.abonne">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.bypass">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.codePromo">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.dateCreation">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.dateDernierAcces">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.dateFin">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.droitAccesOutils">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.droitGestionEtablissements">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.droitGestionLivret">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.droitGestionUtilisateurs">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.droitModifierPass">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.firstname">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.fonction">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.guid">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.id">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.idBaseContrat">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.idEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.libelleEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.light">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.login">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.name">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaAdeli">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaAdresse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaCategorie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaCivilite">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaCiviliteLibelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaCodePays">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaCodePostal">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaDateNaissance">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaEmail">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaProfession">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaProfessionLibelle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaProfessionOneKey">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaRpps">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaTelMobile">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaTelProfessionnel">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaTitre">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaTl">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaUserType">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.owaVille">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.pass">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbUser.profilDemarrage">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbUser">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.deleteAllUsersEtablissementResponse.result">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.deleteAllUsersEtablissementResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.deleteAllUsersEtablissement.idEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.deleteAllUsersEtablissement.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.deleteAllUsersEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduitResponse.produitResult">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getInformationProduitResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduit.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduit.mode">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduit.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getInformationProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.updateUserResponse.result">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.updateUserResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.updateUser.user">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.updateUser.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.updateUser">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.isLoginExistResponse.result">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.isLoginExistResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.isLoginExist.login">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.isLoginExist.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.isLoginExist">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.deleteUserResponse.result">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.deleteUserResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.deleteUser.id">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.deleteUser.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.deleteUser">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.updateEtablissementResponse.result">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.updateEtablissementResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.updateEtablissement.etablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.updateEtablissement.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.updateEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getUsersEtablissement.idEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getUsersEtablissement.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getUsersEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.checkLoginResponse.result">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.checkLoginResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.checkLogin.login">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.checkLogin.password">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.checkLogin.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.checkLogin">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheParCodePCT.codePCT">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheParCodePCT.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.rechercheParCodePCT">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsClasseATCResponse.searchResult">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getProduitsClasseATCResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsClasseATC.codeClasseATC">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsClasseATC.baseLocation">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsClasseATC.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getProduitsClasseATC">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.controleOrdonnanceResponse.controleResult">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.controleOrdonnanceResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbControle.lstAllergies">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbControle.lstAllergiesNonTraitees">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbControle.lstContresIndications">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbControle.lstIPC">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbControle.lstInteractions">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbControle.lstPathologiesAMMNonTraitees">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbControle.lstPathologiesCIM10NonTraitees">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbControle.lstPrecautionsEmploi">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbControle.lstProduitsBcbNonTraites">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbControle.lstProduitsNonTraitesInteractions">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbControle.lstSurdosage">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbControle.lstSurdosageNS">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbControle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbAllergie.alerteMsg">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbAllergie.allergieHTML">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbAllergie.idComposant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbAllergie.idComposantProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbAllergie.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbAllergie.libelleComposant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbAllergie.libelleComposantProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbAllergie.libelleProduit">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbAllergie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbContreIndication.AMMOrigine">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbContreIndication.CIHTML">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbContreIndication.codePathologie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbContreIndication.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbContreIndication.libelleAMMOrigine">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbContreIndication.libelleNiveau">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbContreIndication.libellePathologie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbContreIndication.libelleProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbContreIndication.niveau">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbContreIndication.source">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbContreIndication">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbipc.codeVoie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbipc.IPCHTML">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbipc.idPrincipeActif1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbipc.idPrincipeActif2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbipc.idProduit1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbipc.idProduit2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbipc.libellePrincipeActif1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbipc.libellePrincipeActif2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbipc.libelleProduit1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbipc.libelleProduit2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbipc.libelleVoie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbipc.texte">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbipc">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.classe1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.classe2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.codeClasse1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.codeClasse2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.composantDeclencheur1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.composantDeclencheur2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.conduite">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.gravite">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.idProduit1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.idProduit2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.interactionHTML">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.libelleProduit1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.libelleProduit2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.lngType">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.mecanisme">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.message">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.niveau">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.texte">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.type">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.voie1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInteraction.voie2">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbInteraction">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosage.idClasse1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosage.idClasse2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosage.idComposant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosage.idProduit1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosage.idProduit2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosage.libelleClasse1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosage.libelleClasse2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosage.libelleComposant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosage.libelleProduit1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosage.libelleProduit2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosage.niveau">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbSurdosage">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosageNS.description">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosageNS.lstProduits">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosageNS.redondanceHTML">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosageNS.tableauResultat">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosageNS.titre1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbSurdosageNS.titre2">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbSurdosageNS">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbObj.id">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbObj.value">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbObj">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.controleOrdonnance.lstIdProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.controleOrdonnance.profilPatient">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.controleOrdonnance.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.controleOrdonnance">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProfilPatient.age">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProfilPatient.allaitement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProfilPatient.clairanceCreatinine">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProfilPatient.grossesse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProfilPatient.insuffisanceHepatique">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProfilPatient.lstIdComposantAllergie">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProfilPatient.lstPathologiesAMM">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProfilPatient.lstPathologiesCIM10">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProfilPatient.poids">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProfilPatient.sexe">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProfilPatient.taille">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbProfilPatient">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInformation.codeEditeur">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInformation.dateBase">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInformation.dateFinAbonnement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInformation.dateFinAbonnementPS">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInformation.dateFinBase">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInformation.IP">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInformation.idPS">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInformation.infoAbonnement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInformation.libelleEditeur">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInformation.nomLogiciel">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInformation.statutConnexion">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbInformation.statutConnexionLibelle">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbInformation">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.testConnexionResponse.result">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.testConnexionResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.testConnexion.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.testConnexion">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheBCBHopitalResponse.searchResult">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.rechercheBCBHopitalResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheBCBHopital.query">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheBCBHopital.type">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheBCBHopital.baseLocation">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheBCBHopital.codeEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheBCBHopital.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.rechercheBCBHopital">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.deleteEtablissementResponse.result">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.deleteEtablissementResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.deleteEtablissement.idEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.deleteEtablissement.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.deleteEtablissement">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsMa.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsMa.localCodeForSearch1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsMa.localCodeForSearch2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsMa.localCodeForSearch3">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.etrInfoProduitsMa">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduit_ma.idProduits">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduit_ma.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getInformationProduit_ma">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheBCBResponse.searchResult">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.rechercheBCBResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheBCB.query">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheBCB.type">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheBCB.baseLocation">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheBCB.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.rechercheBCB">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsComposantResponse.searchResult">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getProduitsComposantResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsComposant.idComposant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsComposant.baseLocation">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsComposant.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getProduitsComposant">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.controleOrdonnanceParamResponse.controleResult">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.controleOrdonnanceParamResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.controleOrdonnanceParam.lstIdProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.controleOrdonnanceParam.profilPatient">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.controleOrdonnanceParam.typeControle">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.controleOrdonnanceParam.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.controleOrdonnanceParam">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsBefr.firnm">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsBefr.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsBefr.ircv">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsBefr.localCodeForSearch1">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsBefr.localCodeForSearch2">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsBefr.localCodeForSearch3">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.etrInfoProduitsBefr.mpcv">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.etrInfoProduitsBefr">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduit_befr.idProduits">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getInformationProduit_befr.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getInformationProduit_befr">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getUserResponse.result">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getUserResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getUser.id">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getUser.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getUser">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsIndicationResponse.searchResult">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getProduitsIndicationResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsIndication.typeIndication">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsIndication.codeIndication">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsIndication.baseLocation">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.getProduitsIndication.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.getProduitsIndication">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheEquivalentsResponse.searchResult">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.rechercheEquivalentsResponse">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEquivalents.generique">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEquivalents.groupeGenerique">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEquivalents.idProduitOrigine">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEquivalents.idProduitReferent">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEquivalents.libelleProduitOrigine">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEquivalents.lstClassesTherapeutiques">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEquivalents.lstEquivalentsAutres">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEquivalents.lstEquivalentsProches">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEquivalents.lstEquivalentsStricts">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEquivalents.lstProduitsGroupeGen">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEquivalents.lstProduitsGroupeGenUP">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEquivalents.produitReferent">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEquivalents.referent">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbEquivalents">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMiniUP.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbProduitMiniUP.lstUnitesPrise">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbProduitMiniUP">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheEquivalents.idProduit">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.rechercheEquivalents.key">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.rechercheEquivalents">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEffetIndesirable.appareil">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEffetIndesirable.codeFrequence">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEffetIndesirable.codeMotClef">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEffetIndesirable.libelleFrequence">
	<remarks/>
</member><member name="P:Pharma2000Premium.ServiceBCB.bcbEffetIndesirable.libelleMotClef">
	<remarks/>
</member><member name="T:Pharma2000Premium.ServiceBCB.bcbEffetIndesirable">
	<remarks/>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.ResourceManager">
	<summary>
  Retourne l'instance ResourceManager mise en cache utilisée par cette classe.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Culture">
	<summary>
  Remplace la propriété CurrentUICulture du thread actuel pour toutes
  les recherches de ressources à l'aide de cette classe de ressource fortement typée.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources._1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources._2">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources._21">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources._3">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources._next">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.aachat">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.aajouter">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.aannuler">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.achat">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.aclient">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.aCommande">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.aequi">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.afermer">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.afournisseur">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.afrigo">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.aimprimer">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Ajouter">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.ajouterIconBureau">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.aliste">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.amodifier">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Annuler">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Annuler1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.annuler2">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.annuler21">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.annuler3">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.annuler31">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.archive1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.arecherche">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.areglement">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.aremise">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.arrierePlanBureau">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.article2">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.article3">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.asupprimer">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.avalider">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Banque">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.BarreDexther">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.baseSQL1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.bin">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.caisse">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.calculatrice">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.catalogue">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.cataloque_article">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.categorie">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.changer_">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.cle">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.cle1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.cle11">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.cle12">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.cle2">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.clean1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.client">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.client1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.cnam">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.CNAM1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.cnam2">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.cnam222">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.cnam3">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.cnam4">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.CodeABarre">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.commande">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.dexter">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.echancier">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.egal">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.enregistrer">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.entrée">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.EntreSortie">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.equi">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.etat">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.etat1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.etat2">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.etat3">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.etat31">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.etat4">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.etat5">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.etat6">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Excel_2010">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Exemple_Info_Channel">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.facebook">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.femme">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.femme_enceinte1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.femme_enceinte3">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.femmeR">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Fermer">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.first">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.first_1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.first_dounloaded">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.forme">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.forme1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.forme2">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.fournisseur">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.frigo111">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.frigo112">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.google">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.google1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.homme_femme">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.hommeR">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.icon_mail">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.iden">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.identification">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Image2">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Imprimante">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Imprimante1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Imprimante2">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.imprimer">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.imprmante1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.info1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.laboratoire">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.laboratoire1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.last">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.last_1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.last_dounloaded">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.libelle">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.LinkedInAudit">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.loading">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.loading_transparent">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.loading_transparent_4">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Login">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Login_2">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Login_21">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Login_pharma">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Login_pharma1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Login_Pharma2000_rouge">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.LOGO_NEXT_PLUS">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.LogoPharma2000Premium">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Logp_BCB_Dexther">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.marker">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.medecin">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Medicament">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.mouvement_fournisseur">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.mouvement_fournisseur1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.mouvement_fournisseur11">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.MS_Word">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Mutuelle">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Nature">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.natures">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.next_1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.next_downloaded">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.nouveau">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.nouveau1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.onekey">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.people">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.pharmacie">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.pin">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.preloader">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.preloader_transparent">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.previous">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.previous_1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.previous_downloaded">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.previous_downloaded1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.production">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.quitter">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.RAZ">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.RAZ1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.rech11">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.recherche">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.recherche2">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.regelement1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.reglementt">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.releve_mutuelle">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.remise11">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.saisie">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Sauvegarde">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Sortie">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Sortir">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.spb">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.statistique">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.supprimer">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Supprimer1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.tourner">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.TVA">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.update">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.utilisateur">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.utilisateur1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.valider">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.valider__">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.valider__1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.vente">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.vente1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Vider">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Vider1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.ville">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.ville1">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.voir_modifierfpharmacien">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.webcam">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Windows_Media_Player">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.WZEND2">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="P:Pharma2000Premium.My.Resources.Resources.Youtube">
	<summary>
  Recherche une ressource localisée de type System.Drawing.Bitmap.
</summary>
</member><member name="T:Pharma2000Premium.My.Resources.Resources">
	<summary>
  Une classe de ressource fortement typée destinée, entre autres, à la consultation des chaînes localisées.
</summary>
</member><member name="T:Pharma2000Premium.DataSet_ETAT_Exception.DataTable_EtatDataTable">
	<summary>
Represents the strongly named DataTable class.
</summary>
</member><member name="T:Pharma2000Premium.DataSet_ETAT_Exception.DataTable_EtatRow">
	<summary>
Represents strongly named DataRow class.
</summary>
</member><member name="T:Pharma2000Premium.DataSet_ETAT_Exception.DataTable_EtatRowChangeEvent">
	<summary>
Row event argument class
</summary>
</member><member name="T:Pharma2000Premium.DataSet_ETAT_Exception">
	<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member><member name="T:Pharma2000Premium.DataSet_ETAT_HITPARAD_ARTICLE.DataTableEtatHitParadArticlesDataTable">
	<summary>
Represents the strongly named DataTable class.
</summary>
</member><member name="T:Pharma2000Premium.DataSet_ETAT_HITPARAD_ARTICLE.DataTableEtatHitParadArticlesRow">
	<summary>
Represents strongly named DataRow class.
</summary>
</member><member name="T:Pharma2000Premium.DataSet_ETAT_HITPARAD_ARTICLE.DataTableEtatHitParadArticlesRowChangeEvent">
	<summary>
Row event argument class
</summary>
</member><member name="T:Pharma2000Premium.DataSet_ETAT_HITPARAD_ARTICLE">
	<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member><member name="T:Pharma2000Premium.DataSet_ETAT_FICHE_DE_CONTACT.FicheDeContactDataTable">
	<summary>
Represents the strongly named DataTable class.
</summary>
</member><member name="T:Pharma2000Premium.DataSet_ETAT_FICHE_DE_CONTACT.FicheDeContactRow">
	<summary>
Represents strongly named DataRow class.
</summary>
</member><member name="T:Pharma2000Premium.DataSet_ETAT_FICHE_DE_CONTACT.FicheDeContactRowChangeEvent">
	<summary>
Row event argument class
</summary>
</member><member name="T:Pharma2000Premium.DataSet_ETAT_FICHE_DE_CONTACT">
	<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member><member name="T:Pharma2000Premium.DataSet_EtatInventaireTemporaire.dtINVENTAIRE_DETAILDataTable">
	<summary>
Represents the strongly named DataTable class.
</summary>
</member><member name="T:Pharma2000Premium.DataSet_EtatInventaireTemporaire.dtINVENTAIRE_DETAILRow">
	<summary>
Represents strongly named DataRow class.
</summary>
</member><member name="T:Pharma2000Premium.DataSet_EtatInventaireTemporaire.dtINVENTAIRE_DETAILRowChangeEvent">
	<summary>
Row event argument class
</summary>
</member><member name="T:Pharma2000Premium.DataSet_EtatInventaireTemporaire">
	<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
</members>
</doc>