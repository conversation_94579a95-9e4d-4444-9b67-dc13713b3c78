﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fCopieBD

    Dim cmdReleveSearch As New SqlCommand
    Dim daReleveSearch As New SqlDataAdapter
    Dim dsReleveSearch As New DataSet
    Dim cbReleveSearch As New SqlCommandBuilder

    Dim NbrVente As Integer = 0
    Dim TotalDesVentes As Double = 0.0
    Dim TotalARembourser As Double = 0.0

    Public dr As DataRow = Nothing
    Public StrSQL As String

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        Dim StrConnectionCopy As String = "Password=" + GetSetting("PHARMA", "PHARMA", "MotDePasseSQL", "") + ";Persist security info=true;User ID=" + GetSetting("PHARMA", "PHARMA", "NomUtilisateurSQL", "") + ";Initial catalog=" + GetSetting("PHARMA", "PHARMA", "NomBaseCopie", "") + ";Data source=" + GetSetting("PHARMA", "PHARMA", "NomServeurCopie", "")
        Dim ConnectionServeurCopy = New SqlConnection
        ConnectionServeurCopy.ConnectionString = StrConnection
        ConnectionServeurCopy.Open()
        Dim cmd As New SqlCommand()
        cmd.Connection = ConnectionServeurCopy
        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "ActiverDeclencheurs"
        cmd.CommandTimeout = 72000
        cmd.ExecuteNonQuery()
        Me.Dispose()
    End Sub

    Private Sub fCritereDuReleveCNAM_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

    End Sub

    Private Sub dtDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            dtFin.Focus()
        End If
    End Sub

    Private Sub dtFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            bValider.Focus()
        End If
    End Sub

    Private Sub bValider_Click(sender As Object, e As System.EventArgs) Handles bValider.Click
        Dim StrConnectionCopy As String = "Password=" + GetSetting("PHARMA", "PHARMA", "MotDePasseSQLCopie", "") + ";Persist security info=true;User ID=" + GetSetting("PHARMA", "PHARMA", "NomUtilisateurSQLCopie", "") + ";Initial catalog=" + GetSetting("PHARMA", "PHARMA", "NomBaseCopie", "") + ";Data source=" + GetSetting("PHARMA", "PHARMA", "NomServeurCopie", "")
        Dim ConnectionServeurCopy = New SqlConnection
        ConnectionServeurCopy.ConnectionString = StrConnectionCopy
        ConnectionServeurCopy.Open()



        If dtDebut.Text = "" Or dtFin.Text = "" Then
            MsgBox("Veuiller choisir la période")
            Exit Sub
        End If

        Dim Base As String

        Base = "[" + GetSetting("PHARMA", "PHARMA", "NomServeurCopie", "") + "]"
        If "[" + NomServeur + "]" = Base Then
            Base = "[" + GetSetting("PHARMA", "PHARMA", "NomBaseCopie", "") + "]"
        Else
            Base = Base + ".[" + GetSetting("PHARMA", "PHARMA", "NomBaseCopie", "") + "]"
        End If

        If MsgBox("Voulez vous vraiment copier la base  ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Copie Pharama") = MsgBoxResult.Yes Then
            Try
                Dim cmd As New SqlCommand()



                cmd.Connection = ConnectionServeurCopy
                cmd.CommandType = CommandType.StoredProcedure
                cmd.CommandText = "DeactiverDeclencheurs"
                cmd.CommandTimeout = 72000
                cmd.ExecuteNonQuery()




                cmd.Connection = ConnectionServeur
                cmd.CommandType = CommandType.StoredProcedure
                cmd.Parameters.Add("@pDU", SqlDbType.VarChar).Value = dtDebut.Text
                cmd.Parameters.Add("@pAU", SqlDbType.VarChar).Value = dtFin.Text
                cmd.Parameters.Add("@pBD", SqlDbType.VarChar).Value = Base
                cmd.CommandText = "[Proc_CopierBasePharma]"
                cmd.CommandTimeout = 72000
                cmd.ExecuteNonQuery()



                cmd.Connection = ConnectionServeurCopy
                cmd.CommandType = CommandType.StoredProcedure
                cmd.CommandText = "ActiverDeclencheurs"
                cmd.CommandTimeout = 72000
                cmd.ExecuteNonQuery()


                MsgBox("opération terminé avec succès !")
            Catch ex As Exception
                MsgBox(ex.Message)
            End Try
        End If
    End Sub
End Class