﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fListeDesEquivalents
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fListeDesEquivalents))
        Me.PAnel = New System.Windows.Forms.Panel()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.bOk = New C1.Win.C1Input.C1Button()
        Me.lNbreDesArticles = New System.Windows.Forms.Label()
        Me.gArticles = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.rdbPrix = New System.Windows.Forms.RadioButton()
        Me.rdbDesignation = New System.Windows.Forms.RadioButton()
        Me.chbDisponibleEnStock = New System.Windows.Forms.CheckBox()
        Me.chbMemeDosage = New System.Windows.Forms.CheckBox()
        Me.chbMemeForme = New System.Windows.Forms.CheckBox()
        Me.PAnel.SuspendLayout()
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        Me.SuspendLayout()
        '
        'PAnel
        '
        Me.PAnel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.PAnel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.PAnel.Controls.Add(Me.bAnnuler)
        Me.PAnel.Controls.Add(Me.bOk)
        Me.PAnel.Controls.Add(Me.lNbreDesArticles)
        Me.PAnel.Controls.Add(Me.gArticles)
        Me.PAnel.Controls.Add(Me.GroupBox1)
        Me.PAnel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.PAnel.Location = New System.Drawing.Point(0, 0)
        Me.PAnel.Name = "PAnel"
        Me.PAnel.Size = New System.Drawing.Size(533, 474)
        Me.PAnel.TabIndex = 2
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bAnnuler.Location = New System.Drawing.Point(409, 419)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(107, 45)
        Me.bAnnuler.TabIndex = 16
        Me.bAnnuler.Text = "Annuler                 F10"
        Me.bAnnuler.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bOk
        '
        Me.bOk.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bOk.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bOk.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bOk.Location = New System.Drawing.Point(298, 419)
        Me.bOk.Name = "bOk"
        Me.bOk.Size = New System.Drawing.Size(107, 45)
        Me.bOk.TabIndex = 15
        Me.bOk.Text = "OK                                  F3"
        Me.bOk.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bOk.UseVisualStyleBackColor = True
        Me.bOk.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lNbreDesArticles
        '
        Me.lNbreDesArticles.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lNbreDesArticles.AutoSize = True
        Me.lNbreDesArticles.Location = New System.Drawing.Point(404, 531)
        Me.lNbreDesArticles.Name = "lNbreDesArticles"
        Me.lNbreDesArticles.Size = New System.Drawing.Size(10, 13)
        Me.lNbreDesArticles.TabIndex = 14
        Me.lNbreDesArticles.Text = "-"
        '
        'gArticles
        '
        Me.gArticles.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.gArticles.GroupByCaption = "Drag a column header here to group by that column"
        Me.gArticles.Images.Add(CType(resources.GetObject("gArticles.Images"), System.Drawing.Image))
        Me.gArticles.Location = New System.Drawing.Point(13, 72)
        Me.gArticles.Name = "gArticles"
        Me.gArticles.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gArticles.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gArticles.PreviewInfo.ZoomFactor = 75.0R
        Me.gArticles.PrintInfo.PageSettings = CType(resources.GetObject("gArticles.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gArticles.Size = New System.Drawing.Size(503, 339)
        Me.gArticles.TabIndex = 12
        Me.gArticles.Text = "C1TrueDBGrid1"
        Me.gArticles.PropBag = resources.GetString("gArticles.PropBag")
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Controls.Add(Me.rdbPrix)
        Me.GroupBox1.Controls.Add(Me.rdbDesignation)
        Me.GroupBox1.Controls.Add(Me.chbDisponibleEnStock)
        Me.GroupBox1.Controls.Add(Me.chbMemeDosage)
        Me.GroupBox1.Controls.Add(Me.chbMemeForme)
        Me.GroupBox1.Location = New System.Drawing.Point(13, 12)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(503, 54)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(332, 13)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(49, 13)
        Me.Label2.TabIndex = 46
        Me.Label2.Text = "Trie par :"
        '
        'rdbPrix
        '
        Me.rdbPrix.AutoSize = True
        Me.rdbPrix.Location = New System.Drawing.Point(393, 31)
        Me.rdbPrix.Name = "rdbPrix"
        Me.rdbPrix.Size = New System.Drawing.Size(42, 17)
        Me.rdbPrix.TabIndex = 44
        Me.rdbPrix.TabStop = True
        Me.rdbPrix.Text = "Prix"
        Me.rdbPrix.UseVisualStyleBackColor = True
        '
        'rdbDesignation
        '
        Me.rdbDesignation.AutoSize = True
        Me.rdbDesignation.Location = New System.Drawing.Point(393, 12)
        Me.rdbDesignation.Name = "rdbDesignation"
        Me.rdbDesignation.Size = New System.Drawing.Size(81, 17)
        Me.rdbDesignation.TabIndex = 43
        Me.rdbDesignation.TabStop = True
        Me.rdbDesignation.Text = "Désignation"
        Me.rdbDesignation.UseVisualStyleBackColor = True
        '
        'chbDisponibleEnStock
        '
        Me.chbDisponibleEnStock.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.chbDisponibleEnStock.AutoSize = True
        Me.chbDisponibleEnStock.Location = New System.Drawing.Point(197, 21)
        Me.chbDisponibleEnStock.Name = "chbDisponibleEnStock"
        Me.chbDisponibleEnStock.Size = New System.Drawing.Size(119, 17)
        Me.chbDisponibleEnStock.TabIndex = 42
        Me.chbDisponibleEnStock.Text = "Disponible en stock"
        Me.chbDisponibleEnStock.UseVisualStyleBackColor = True
        '
        'chbMemeDosage
        '
        Me.chbMemeDosage.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.chbMemeDosage.AutoSize = True
        Me.chbMemeDosage.Location = New System.Drawing.Point(99, 21)
        Me.chbMemeDosage.Name = "chbMemeDosage"
        Me.chbMemeDosage.Size = New System.Drawing.Size(93, 17)
        Me.chbMemeDosage.TabIndex = 41
        Me.chbMemeDosage.Text = "Même dosage"
        Me.chbMemeDosage.UseVisualStyleBackColor = True
        '
        'chbMemeForme
        '
        Me.chbMemeForme.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.chbMemeForme.AutoSize = True
        Me.chbMemeForme.Location = New System.Drawing.Point(10, 21)
        Me.chbMemeForme.Name = "chbMemeForme"
        Me.chbMemeForme.Size = New System.Drawing.Size(84, 17)
        Me.chbMemeForme.TabIndex = 40
        Me.chbMemeForme.Text = "Même forme"
        Me.chbMemeForme.UseVisualStyleBackColor = True
        '
        'fListeDesEquivalents
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(533, 474)
        Me.Controls.Add(Me.PAnel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fListeDesEquivalents"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.PAnel.ResumeLayout(False)
        Me.PAnel.PerformLayout()
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents PAnel As System.Windows.Forms.Panel
    Friend WithEvents lNbreDesArticles As System.Windows.Forms.Label
    Friend WithEvents gArticles As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents chbMemeForme As System.Windows.Forms.CheckBox
    Friend WithEvents chbDisponibleEnStock As System.Windows.Forms.CheckBox
    Friend WithEvents chbMemeDosage As System.Windows.Forms.CheckBox
    Friend WithEvents rdbPrix As System.Windows.Forms.RadioButton
    Friend WithEvents rdbDesignation As System.Windows.Forms.RadioButton
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bOk As C1.Win.C1Input.C1Button
    Friend WithEvents Label2 As System.Windows.Forms.Label
End Class
