﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fMouvementArticle
    Dim StrSQL As String = ""

    Dim cmdMouvement As New SqlCommand
    Dim daMouvement As New SqlDataAdapter
    Dim dsMouvement As New DataSet
    Dim cmd As New SqlCommand

    Dim StrSQLINJ As String = ""

    Dim cmdInjection As New SqlCommand
    Dim daInjection As New SqlDataAdapter

    Dim vCodeTypePre As String = ""
    Dim vArticlefractionneMere As Integer = 0

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub init()

        ' liste des articles
        initArticle()

        'pour initialiser la date
        initDate()

        'Initialiser la table Mouvement Article
        initMouvementArticle()

        'pour initialiser la grid
        initgArticle()

        dtpDebut.Focus()

    End Sub

    Dim DateReference As String = ""
    Dim StockReference As String = ""

    Private Sub initDate()
        dtpDebut.Text = Date.Today
        dtpFin.Text = Date.Today
        System.DateTime.Now.Date.ToString()
    End Sub

    'Initialiser les table Muvement Article
    Private Sub initMouvementArticle()

        cmdMouvement.CommandText = " SELECT TOP(0) Type," + _
                                   " Numero," + _
                                   " DateMouvement," + _
                                   " Stock," + _
                                   " Entree," + _
                                   " Sortie," + _
                                   " (Stock + Entree - Sortie) as StockResiduel, " + _
                                   " Tiers, " + _
                                   " TIME " + _
                                   " FROM Vue_MouvementArticle "

        cmdMouvement.Connection = ConnectionServeur
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "MouvementArticle")
    End Sub

    '-----------pour tester si l'article est de type injection
    Private Sub Verification_Injection()
        Dim inj As Integer = 0
        Try
        StrSQLINJ = " SELECT CodePreparation FROM  FORMULE_PREPARATION WHERE CodeTypePreparation=2 "

        cmdInjection.Connection = ConnectionServeur
        cmdInjection.CommandText = StrSQLINJ
        daInjection = New SqlDataAdapter(cmdInjection)
        daInjection.Fill(dsMouvement, "INJECTION")

        For I = 0 To dsMouvement.Tables("INJECTION").Rows.Count - 1
            If dsMouvement.Tables("INJECTION").Rows(I).Item("CodePreparation") = cmbArticle.SelectedValue Then
                inj = inj + 1
            End If
        Next
        If inj > 0 Then
            For I = 0 To gDetails.RowCount - 1
                gDetails(I, "StockResiduel") = ""
                gDetails(I, "Stock") = ""
            Next
            End If
        Catch ex As Exception
            WriteLine(ex.Message)
        End Try
    End Sub

    Public Sub AfficherMouvement()

        Dim I As Integer

        Dim TotalEntree As Decimal = 0
        Dim TotalSortie As Decimal = 0

        Dim totalAchats As Decimal = 0
        Dim TotalVentes As Decimal = 0
        Dim TotalEnrees As Decimal = 0
        Dim TotalSorties As Decimal = 0
        Dim TotalPret As Decimal = 0
        Dim TotalEmprun As Decimal = 0

        Dim Entree As Double = 0.0
        Dim Sortie As Double = 0.0
        Dim Pret As Double = 0.0
        Dim Emprunt As Double = 0.0
        Dim Vente As Double = 0.0
        Dim Achat As Double = 0.0
        Dim PreparationPharmaceutique As Double = 0.0
        Dim ProductionPharmaceutique As Double = 0.0
        Dim TotalInjection As Double = 0.0


        If (dsMouvement.Tables.IndexOf("MouvementArticle") > -1) Then
            dsMouvement.Tables("MouvementArticle").Clear()
        End If

        If (dsMouvement.Tables.IndexOf("INVENTAIRE") > -1) Then
            dsMouvement.Tables("INVENTAIRE").Clear()
        End If


        'Composer la condition de la requête    
        If cmbArticle.Text = "" Then
            Exit Sub
        End If

        If Len(dtpDebut.Text) < 10 Or Len(dtpFin.Text) < 10 Then
            Exit Sub
        End If
        DateAdd(DateInterval.Year, -3, dtpDebut.Value)

        ' Date et stock au dernier inventaire 
        cmdMouvement.CommandText = " select DISTINCT(INVENTAIRE_DETAILS.NumeroInventaire)," + _
                                   " Date " + _
                                   " FROM INVENTAIRE LEFT OUTER JOIN INVENTAIRE_DETAILS ON INVENTAIRE.NumeroInventaire=INVENTAIRE_DETAILS.NumeroInventaire " + _
                                   " WHERE INVENTAIRE_DETAILS.CodeArticle ='" + cmbArticle.SelectedValue + _
                                   "' ORDER BY Date DESC "
        cmdMouvement.Connection = ConnectionServeur
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "INVENTAIRE")

        If dsMouvement.Tables("INVENTAIRE").Rows.Count <> 0 Then
            DateReference = dsMouvement.Tables("INVENTAIRE").Rows(0).Item("Date")
            cmdMouvement.CommandText = "SELECT SUM(stockactuel) FROM INVENTAIRE_DETAILS where NumeroInventaire ='" + dsMouvement.Tables("INVENTAIRE").Rows(0).Item("NumeroInventaire") + "' AND CodeArticle='" + cmbArticle.SelectedValue + "'"
            cmdMouvement.Connection = ConnectionServeur
            Try
                StockReference = cmdMouvement.ExecuteScalar().ToString
            Catch ex As Exception
                Console.WriteLine(ex.Message)
                StockReference = 0
            End Try

        Else
            DateReference = ""
            StockReference = 0
        End If

        If DateReference = "" Then
            DateReference = RecupererValeurExecuteScalaire("DateInitiale", "ARTICLE", "CodeArticle", cmbArticle.SelectedValue)
            StockReference = RecupererValeurExecuteScalaire("StockInitial", "ARTICLE", "CodeArticle", cmbArticle.SelectedValue)
            If DateReference = "" Then
                DateReference = "01/01/1900"
                StockReference = 0
            End If
        End If
        lDateReference.Text = DateReference
        lStockReference.Text = StockReference


        ' Somme des achats
        cmdMouvement.CommandText = "select SUM(Qte) FROM ACHAT_DETAILS LEFT OUTER JOIN ACHAT ON ACHAT_DETAILS.NumeroAchat=ACHAT.NumeroAchat " + _
                                   "WHERE CodeArticle like '" + _
                                   cmbArticle.SelectedValue + "' AND Date BETWEEN '" + DateReference + "' AND '" + dtpDebut.Value + "'"
        cmdMouvement.Connection = ConnectionServeur
        Try
            Achat = cmdMouvement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
            Achat = 0.0
        End Try

        ' Somme des ventes
        cmdMouvement.CommandText = "select SUM(Qte) FROM VENTE_DETAILS LEFT OUTER JOIN VENTE ON VENTE_DETAILS.NumeroVente=VENTE.NumeroVente " + _
                                   " WHERE CodeArticle like '" + _
                                   cmbArticle.SelectedValue + "' AND Date BETWEEN '" + DateReference + "' AND '" + dtpDebut.Value + "'"
        cmdMouvement.Connection = ConnectionServeur
        Try
            Vente = cmdMouvement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
            Vente = 0.0
        End Try

        ' Somme des entrées
        cmdMouvement.CommandText = "select SUM(Qte) FROM ENTREE_DETAILS LEFT OUTER JOIN ENTREE ON ENTREE_DETAILS.NumeroEntree=ENTREE.NumeroEntree " + _
                                   " WHERE CodeArticle like '" + _
                                   cmbArticle.SelectedValue + "' AND Date BETWEEN '" + DateReference + "' AND '" + dtpDebut.Value + "'"
        cmdMouvement.Connection = ConnectionServeur
        Try
            Entree = cmdMouvement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
            Entree = 0.0
        End Try

        ' Somme des sorties
        cmdMouvement.CommandText = "select SUM(Qte) FROM SORTIE_DETAILS LEFT OUTER JOIN SORTIE ON SORTIE_DETAILS.NumeroSortie=SORTIE.NumeroSortie " + _
                                   " WHERE CodeArticle like '" + _
                                   cmbArticle.SelectedValue + "' AND Date BETWEEN '" + DateReference + "' AND '" + dtpDebut.Value + "'"
        cmdMouvement.Connection = ConnectionServeur
        Try
            Sortie = cmdMouvement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
            Sortie = 0.0
        End Try

        ' Somme des emprunts
        cmdMouvement.CommandText = "select SUM(Qte) FROM EMPRUNT_DETAILS LEFT OUTER JOIN EMPRUNT ON EMPRUNT_DETAILS.NumeroEmprunt=EMPRUNT.NumeroEmprunt " + _
                                   " WHERE CodeArticle like '" + _
                                   cmbArticle.SelectedValue + "' AND Date BETWEEN '" + DateReference + "' AND '" + dtpDebut.Value + "'"
        cmdMouvement.Connection = ConnectionServeur
        Try
            Emprunt = cmdMouvement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
            Emprunt = 0.0
        End Try

        ' Somme des pret
        cmdMouvement.CommandText = "select SUM(Qte) FROM PRET_DETAILS LEFT OUTER JOIN PRET ON PRET_DETAILS.NumeroPret=PRET.NumeroPret " + _
                                  " WHERE CodeArticle like '" + _
                                  cmbArticle.SelectedValue + "' AND Date BETWEEN '" + DateReference + "' AND '" + dtpDebut.Value + "'"
        cmdMouvement.Connection = ConnectionServeur
        Try
            Pret = cmdMouvement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
            Pret = 0.0
        End Try

        lEntree.Text = Achat + Entree + Emprunt + ProductionPharmaceutique
        lSortie.Text = Vente + Sortie + Pret + PreparationPharmaceutique
        lStockAvant.Text = CDbl(lEntree.Text) - CDbl(lSortie.Text) + CDbl(lStockReference.Text)


        Try
            vCodeTypePre = RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", cmbArticle.SelectedValue)
        Catch ex As Exception
            vCodeTypePre = ""
        End Try

        Try
            cmd.CommandText = " select COUNT(*) from ARTICLE where CodeArticle in (select distinct CodeArticleMere from FRACTIONNEMENT) and CodeArticle = " + Quote(cmbArticle.SelectedValue)
            vArticlefractionneMere = cmd.ExecuteScalar()
        Catch ex As Exception
            vArticlefractionneMere = 0
        End Try

        If vCodeTypePre = "4" Or vArticlefractionneMere > 0 Then
            cmdMouvement.CommandText = " SELECT Type," + _
                                      " Numero," + _
                                      " DateMouvement," + _
                                      " case when TYPE='Vente' then (select STOCK from VENTE_DETAILS where NumeroVente = Numero and CodeArticle = " + Quote(cmbArticle.SelectedValue) + ") " + _
                                     " else case when TYPE='Achat' then (select STOCK from ACHAT_DETAILS where NumeroAchat = Numero and CodeArticle = " + Quote(cmbArticle.SelectedValue) + ") " + _
                                     " else case when TYPE='Entrée' then (select STOCK from ENTREE_DETAILS where NumeroEntree = Numero and CodeArticle = " + Quote(cmbArticle.SelectedValue) + ") " + _
                                     " else case when TYPE='Sortie' then (select STOCK from SORTIE_DETAILS where NumeroSortie = Numero and CodeArticle = " + Quote(cmbArticle.SelectedValue) + ") " + _
                                     " else case when TYPE='Prêt' then (select STOCK from PRET_DETAILS where NumeroPret = Numero and CodeArticle = " + Quote(cmbArticle.SelectedValue) + ") " + _
                                     " else case when TYPE='Emprunt' then (select STOCK from EMPRUNT_DETAILS where NumeroEmprunt = Numero and CodeArticle = " + Quote(cmbArticle.SelectedValue) + ") " + _
                                     " else 0 end end end end end end AS Stock, " + _
                                      " Entree," + _
                                      " Sortie," + _
                                      " 0 as StockResiduel, " + _
                                      " Tiers, " + _
                                      " TIME " + _
                                      " FROM Vue_MouvementArticle " + _
                                      " WHERE CodeArticle = '" & cmbArticle.SelectedValue & "' " + _
                                      " AND DateMouvement Between '" & DateAdd(DateInterval.Second, -1, dtpDebut.Value) & "' AND '" & DateAdd(DateInterval.Day, 1, dtpFin.Value).ToString & "' " + _
                                      " ORDER BY DateMouvement"
        Else

            cmdMouvement.CommandText = " SELECT Type," + _
                                       " Numero," + _
                                       " DateMouvement," + _
                                       " 0 AS Stock," + _
                                       " Entree," + _
                                       " Sortie," + _
                                       " 0 as StockResiduel, " + _
                                       " Tiers, " + _
                                       " TIME " + _
                                       " FROM Vue_MouvementArticle " + _
                                       " WHERE CodeArticle = '" & cmbArticle.SelectedValue & "' " + _
                                       " AND DateMouvement Between '" & DateAdd(DateInterval.Second, -1, dtpDebut.Value) & "' AND '" & DateAdd(DateInterval.Day, 1, dtpFin.Value).ToString & "' " + _
                                       " ORDER BY DateMouvement"
            '" AND convert(date,DateMouvement) Between '" & dtpDebut.Text & "' AND '" & dtpFin.Text & "' "
        End If


        cmdMouvement.Connection = ConnectionServeur
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "MouvementArticle")

        ' 3 cas se présentent :
        ' 1/ La date de référence est avan la date de début de visualisation
        cmd.Connection = ConnectionServeur
        If DateReference < dtpDebut.Value Then
            ' Calcul du stock au début de la période de visualisation et l'affecter au stock de la première line
            cmd.CommandText = " SELECT SUM(Entree)-SUM(Sortie) AS TotalMouvement " + _
                              " FROM Vue_MouvementArticle " + _
                              " WHERE CodeArticle = '" & cmbArticle.SelectedValue & "' " + _
                              " AND DateMouvement Between '" & DateReference.ToString & "' AND '" & DateAdd(DateInterval.Second, -1, dtpDebut.Value).ToString & "' "
            If Not IsNothing(cmd.ExecuteScalar) Then
                If IsDBNull(cmd.ExecuteScalar) Then
                    StockReference = 0
                Else
                    StockReference = CInt(cmd.ExecuteScalar)
                End If
                If gDetails.RowCount > 0 Then
                    gDetails(0, "Stock") = StockReference
                    gDetails(0, "StockResiduel") = gDetails(0, "Stock") + gDetails(0, "Entree") - gDetails(0, "Sortie")
                End If
            End If
        End If

        If DateReference >= dtpDebut.Value And DateReference <= dtpFin.Value Then
            ' Chercher le mouvement d'inventaire ou de stock initial du produit et mettre à jour le champ stock par le stock de référence
            ' Commencer par la fin et aller vers le début
            For I = gDetails.RowCount - 1 To 0 Step -1
                If CDate(gDetails(I, "DateMouvement").ToString) = DateReference Then
                    If gDetails(I, "Type") = "Inventaire" Or gDetails(I, "Type") = "Stock Initial" Then
                        gDetails(I, "StockResiduel") = StockReference
                        Exit For
                    End If
                End If
            Next I
        End If

        If DateReference > dtpFin.Value Then
            ' ne rien faire sur le champ stock
        End If


        If vCodeTypePre = "4" Or vArticlefractionneMere > 0 Then
        Else
            ' Calcul du Stock résiduel par ligne après chaqe mouvement
            For I = 1 To gDetails.RowCount - 1
                If gDetails(I, "DateMouvement") >= DateReference Then
                    gDetails(I, "Stock") = gDetails(I - 1, "StockResiduel")
                    If gDetails(I, "Type") <> "Inventaire" Then
                        gDetails(I, "StockResiduel") = gDetails(I, "Stock") - gDetails(I, "Sortie") + gDetails(I, "Entree")
                    End If
                Else
                    gDetails(I, "StockResiduel") = 0
                End If
            Next
        End If


        lStock.Text = CalculeStock(cmbArticle.SelectedValue) 'Val(lStockAvant.Text) + Val(lDifference.Text)

        For I = 0 To gDetails.RowCount - 1
            TotalEntree += gDetails(I, "Entree")
            TotalSortie += gDetails(I, "Sortie")

            Select Case gDetails(I, "Type")
                Case "Vente"
                    TotalVentes += gDetails(I, "Sortie")
                Case "Sortie"
                    TotalSorties += gDetails(I, "Sortie")
                Case "Prêt"
                    TotalPret += gDetails(I, "Sortie")
                Case "Achat"
                    totalAchats += gDetails(I, "Entree")
                Case "Entrée"
                    TotalEnrees += gDetails(I, "Entree")
                Case "Emprunt"
                    TotalEmprun += gDetails(I, "Entree")
                Case "Production "
                    ProductionPharmaceutique += gDetails(I, "Entree")
                Case "Preparation"
                    PreparationPharmaceutique += gDetails(I, "Sortie")
                Case "Injection"
                    TotalInjection += gDetails(I, "Sortie")
            End Select
        Next

        lTotalEntree.Text = TotalEntree.ToString("### ### ##0.000")
        lTotalSortie.Text = TotalSortie.ToString("### ### ##0.000")

        lTotAchat.Text = totalAchats.ToString("### ### ##0.000")
        lTotEmprunt.Text = TotalEmprun.ToString("### ### ##0.000")
        lTotentre.Text = TotalEnrees.ToString("### ### ##0.000")
        lTotPret.Text = TotalPret.ToString("### ### ##0.000")
        lTotSortie.Text = TotalSorties.ToString("### ### ##0.000")
        lTotProduction.Text = ProductionPharmaceutique.ToString("### ### ##0.000")
        lTotVente.Text = TotalVentes.ToString("### ### ##0.000")
        lTotPreparation.Text = PreparationPharmaceutique.ToString("### ### ##0.000")
        lTotInjecton.Text = TotalInjection.ToString("### ### ##0.000")

        'pour initialiser la grid
        initgArticle()

        'si l'article est de tpe injection alors les colonnes stock et stock résiduel 
        Verification_Injection()

    End Sub
    Private Sub initgArticle()
        With gDetails
            .Columns.Clear()
            .DataSource = dsMouvement
            .DataMember = "MouvementArticle"
            .Rebind(False)
            .Columns("Type").Caption = "Type"
            .Columns("Numero").Caption = "Numéro"
            .Columns("DateMouvement").Caption = "Date"
            .Columns("Stock").Caption = "Stock"
            .Columns("Entree").Caption = "Entrée"
            .Columns("Sortie").Caption = "Sortie"
            .Columns("StockResiduel").Caption = "Stock Résiduel"
            .Columns("TIME").Caption = "Heure"

            .Columns("DateMouvement").NumberFormat = "dd/MM/yyyy"
            .Columns("Entree").NumberFormat = "#"
            .Columns("Sortie").NumberFormat = "#"
            .Columns("Stock").NumberFormat = "#"
            .Columns("StockResiduel").NumberFormat = "#"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("Type").Width = 100
            .Splits(0).DisplayColumns("Type").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Numero").Width = 130
            .Splits(0).DisplayColumns("DateMouvement").Width = 100
            .Splits(0).DisplayColumns("Stock").Width = 100
            .Splits(0).DisplayColumns("Entree").Width = 100
            .Splits(0).DisplayColumns("Sortie").Width = 100
            .Splits(0).DisplayColumns("StockResiduel").Width = 100
            .Splits(0).DisplayColumns("Tiers").Width = 300
            .Splits(0).DisplayColumns("Tiers").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("TIME").Width = 80


            If vCodeTypePre = "4" Or vArticlefractionneMere > 0 Then
                .Splits(0).DisplayColumns("StockResiduel").Visible = False
            Else
                .Splits(0).DisplayColumns("StockResiduel").Visible = True
            End If


            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .AllowSort = False
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gDetails)
        End With
    End Sub

    Private Sub initArticle()
        'Vider la dataset 
        Try
            dsMouvement.Tables("Article").Clear()

        Catch ex As Exception

        End Try


        'chargement des noms Articles
        StrSQL = " SELECT ARTICLE.CodeArticle," + _
                 " Designation," + _
                 " LibelleForme," + _
                 " StockInitial," + _
                 " DateInitiale," + _
                 " Sum(QteLotArticle) as Stock " + _
                 " FROM  ARTICLE " + _
                 " LEFT OUTER JOIN LOT_ARTICLE ON LOT_ARTICLE.CodeArticle = ARTICLE.CodeArticle " + _
                 " LEFT OUTER JOIN FORME_ARTICLE ON FORME_ARTICLE.CodeForme = ARTICLE.CodeForme " + _
                 " where  ARTICLE.Supprime=0 GROUP BY ARTICLE.CodeArticle, Designation, LibelleForme ,StockInitial , DateInitiale" + _
                 " ORDER BY Designation ASC"
        cmdMouvement.Connection = ConnectionServeur
        cmdMouvement.CommandText = StrSQL
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "Article")


        cmbArticle.DataSource = dsMouvement.Tables("Article")
        cmbArticle.ValueMember = "CodeArticle"
        cmbArticle.DisplayMember = "Designation"
        cmbArticle.ColumnHeaders = False
        cmbArticle.Splits(0).DisplayColumns("CodeArticle").Visible = False
        cmbArticle.Splits(0).DisplayColumns("Designation").Width = 220
        cmbArticle.Splits(0).DisplayColumns("LibelleForme").Width = 90
        cmbArticle.Splits(0).DisplayColumns("StockInitial").Visible = False
        cmbArticle.Splits(0).DisplayColumns("DateInitiale").Visible = False
        cmbArticle.Splits(0).DisplayColumns("Stock").Width = 40
        cmbArticle.ExtendRightColumn = True
    End Sub
    Private Sub dtpDateDebut_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyDown
        If e.KeyCode = Keys.Enter Then
            dtpFin.Focus()
            AfficherMouvement()
        End If

    End Sub

    Private Sub dtpDateFin_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyDown
        If e.KeyCode = Keys.Enter Then
            cmbArticle.Focus()
        End If
        AfficherMouvement()
    End Sub

    Private Sub cmbArticle_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbArticle.KeyDown

        cmbArticle.OpenCombo()
        If e.KeyCode = Keys.Enter Then
            cmbArticle.CloseCombo()
            If cmbArticle.Text <> "" Then
                cmbArticle.Text = cmbArticle.Columns("Designation").Value
                AfficherMouvement()
            End If
            ''''dtpDebut.Focus()
            gDetails.Focus()
        End If

    End Sub

    Private Sub cmbArticle_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbArticle.Validated

        'AfficherMouvement()

    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        If cmbArticle.Text = "" Then
            Exit Sub
        End If

        'If lDateReference.Text > dtpDebut.Text + " 00:00:00" Then
        '    dtpDebut.Value = lDateReference.Text
        'End If

        Dim CondCrystal As String = ""
        CondCrystal = " 1=1 AND {Vue_MouvementArticle.CodeArticle} = '" & cmbArticle.SelectedValue & "' " + _
                      " AND date({Vue_MouvementArticle.DateMouvement}) >= date('" & dtpDebut.Text & "') " + _
                      " AND date({Vue_MouvementArticle.DateMouvement}) <= date('" & dtpFin.Text & "') "
        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression de liste de mouvement d'article" Then
                num = I
            End If
        Next
        CR.FileName = Application.StartupPath + "\EtatMouvementArticle.rpt"

        CR.SetParameterValue("Debut", dtpDebut.Text)
        CR.SetParameterValue("Fin", dtpFin.Text)
        CR.SetParameterValue("Article", cmbArticle.Text)
        CR.SetParameterValue("Stock", lStock.Text)
        CR.SetParameterValue("StockInitial", StockReference)

        CR.SetParameterValue("pPharmacie", Pharmacie)

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo

        Try
            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent
        Catch ex As Exception
            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent
        End Try

        CR.RecordSelectionFormula = CondCrystal
        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression de liste de mouvement d'article"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherMouvement()
        End If
    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherMouvement()
        End If

    End Sub

    Private Sub gDetails_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gDetails.KeyUp

        If e.KeyCode = Keys.F1 And gDetails(gDetails.Row, "Type") = "Vente" Then
            Dim MyVenteAffiche As New fVenteJusteAffichage
            MyVenteAffiche.NumeroVente = gDetails(gDetails.Row, "Numero")
            MyVenteAffiche.ShowDialog()
            MyVenteAffiche.Close()
            MyVenteAffiche.Dispose()

        End If

        If e.KeyCode = Keys.F1 And gDetails(gDetails.Row, "Type") = "Achat" Then
            Dim MyAchatAffiche As New fAchatJusteAffichage
            MyAchatAffiche.NumeroAchat = gDetails(gDetails.Row, "Numero")
            MyAchatAffiche.ShowDialog()
            MyAchatAffiche.Close()
            MyAchatAffiche.Dispose()

        End If
    End Sub

    Private Sub cmbArticle_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbArticle.KeyUp
        If e.KeyCode = Keys.Enter Then

            AfficherMouvement()
        End If

        cmbArticle.Focus()

    End Sub


    Private Sub bAfficherDetails_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAfficherDetails.Click
        If dsMouvement.Tables("MouvementArticle").Rows.Count > 0 Then
            If gDetails(gDetails.Row, "Type") = "Vente" Then
                Dim MyVenteAffiche As New fVenteJusteAffichage
                MyVenteAffiche.NumeroVente = gDetails(gDetails.Row, "Numero")
                MyVenteAffiche.ShowDialog()
                MyVenteAffiche.Close()
                MyVenteAffiche.Dispose()

            End If

            If gDetails(gDetails.Row, "Type") = "Achat" Then
                Dim MyAchatAffiche As New fAchatJusteAffichage
                MyAchatAffiche.NumeroAchat = gDetails(gDetails.Row, "Numero")
                MyAchatAffiche.ShowDialog()
                MyAchatAffiche.Close()
                MyAchatAffiche.Dispose()

            End If

            If gDetails(gDetails.Row, "Type") = "Inventaire" Then
                Dim MyAchatAffiche As New fInventaire
                MyAchatAffiche.NumeroInventaire = gDetails(gDetails.Row, "Numero")
                MyAchatAffiche.Init()

                '--------------------
                MyAchatAffiche.bQuitter.Visible = False
                MyAchatAffiche.bAnnuler.Visible = False
                MyAchatAffiche.bConfirmer.Visible = False
                MyAchatAffiche.bAjouter.Visible = False
                MyAchatAffiche.bListe.Visible = False
                MyAchatAffiche.bSupprimer.Visible = False
                MyAchatAffiche.bRecherche.Visible = False
                MyAchatAffiche.bImprimer.Visible = False
                MyAchatAffiche.bTerminal.Visible = False
                MyAchatAffiche.lNombreDesArticles.Visible = False
                'MyAchatAffiche.bLast.Visible = False
                'MyAchatAffiche.bNext.Visible = False
                'MyAchatAffiche.bPrevious.Visible = False
                'MyAchatAffiche.bFirst.Visible = False
                MyAchatAffiche.bAfficherLot.Visible = False
                MyAchatAffiche.gArticles.Enabled = False
                '---------------------
                MyAchatAffiche.ShowDialog()
                MyAchatAffiche.Close()
                MyAchatAffiche.Dispose()

            End If
        End If

    End Sub


End Class