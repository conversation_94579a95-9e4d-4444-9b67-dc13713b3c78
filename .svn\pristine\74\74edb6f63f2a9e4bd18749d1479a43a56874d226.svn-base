﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid


Public Class fFicheReleveeCNAM

    Public NuemroReleve As String = ""

    Dim x As Integer
    Dim cmdReleve As New SqlCommand
    Dim daReleve As New SqlDataAdapter
    Dim dsReleve As New DataSet
    Dim cbReleve As SqlCommandBuilder

    Dim tailleCodeCNAM As Integer
    Dim tailleTypeCode As Integer = 4
    Dim tailleCodePCT As Integer = 6

    Public ModificationReglement As String = ""
    Public _fReleveeCNAM As New fReleveeCNAM

    Public ListNumeroVents As List(Of String) = New List(Of String)()

    Dim MontantTotalTTC As Decimal = 0
    Dim MontantR As Decimal = 0

    Public Sub Init(ByVal fReleveeCNAM As fReleveeCNAM)

        Dim StrSQL As String = ""

        x = 0
        'chargement des APCI
        StrSQL = "SELECT CodeAPCI,NomAPCI FROM APCI ORDER BY NomAPCI ASC"
        cmdReleve.Connection = ConnectionServeur
        cmdReleve.CommandText = StrSQL
        daReleve = New SqlDataAdapter(cmdReleve)
        daReleve.Fill(dsReleve, "APCI")

        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler

        AfficherReleve()
        CalculerMontants()

        'pour recupere les information de la CNAM
        recupererInformationCNAM()

        _fReleveeCNAM = fReleveeCNAM
    End Sub

    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)
        If e.KeyCode = Keys.F3 Then
            bValiderReleve_Click(o, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F12 Then
            bAnnuler_Click(o, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(o, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F8 Then
            bReglement_Click(o, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F1 Then
            Dim MyVenteAffiche As New fVenteJusteAffichage
            MyVenteAffiche.NumeroVente = gReleves(gReleves.Row, "NumeroVente")
            MyVenteAffiche.ShowDialog()
            MyVenteAffiche.Close()
            MyVenteAffiche.Dispose()
            Exit Sub
        End If
    End Sub

    Public Sub AfficherReleve()

        Dim I As Integer
        Dim Cond As String = "1=1"
        Dim StrSQL As String = ""

        Dim LienDeParentePremiereLigne As String = ""
        Dim CodeApciPremiereLigne As String = ""

        If (dsReleve.Tables.IndexOf("RELEVE") > -1) Then
            dsReleve.Tables("RELEVE").Clear()
        End If
        If (dsReleve.Tables.IndexOf("RELEVE_CNAM_DETAILS") > -1) Then
            dsReleve.Tables("RELEVE_CNAM_DETAILS").Clear()
        End If
        If (dsReleve.Tables.IndexOf("APCI") > -1) Then
            dsReleve.Tables("APCI").Clear()
        End If
        If (dsReleve.Tables.IndexOf("LIEN_PARENTE") > -1) Then
            dsReleve.Tables("LIEN_PARENTE").Clear()
        End If

        'chargement des APCI
        StrSQL = "SELECT CodeAPCI,NomAPCI FROM APCI ORDER BY NomAPCI ASC"
        cmdReleve.Connection = ConnectionServeur
        cmdReleve.CommandText = StrSQL
        daReleve = New SqlDataAdapter(cmdReleve)
        daReleve.Fill(dsReleve, "APCI")

        'chargement des LIEN DE PARENTE
        StrSQL = "SELECT CodeLienDeParente,LibelleLienDeParente FROM LIEN_PARENTE ORDER BY LibelleLienDeParente ASC"
        cmdReleve.Connection = ConnectionServeur
        cmdReleve.CommandText = StrSQL
        daReleve = New SqlDataAdapter(cmdReleve)
        daReleve.Fill(dsReleve, "LIEN_PARENTE")

        cmdReleve.CommandText = " SELECT * FROM RELEVE_CNAM WHERE NumeroReleve = " + Quote(NuemroReleve) + " ORDER BY Date DESC "

        cmdReleve.Connection = ConnectionServeur
        daReleve = New SqlDataAdapter(cmdReleve)
        daReleve.Fill(dsReleve, "RELEVE")

        lNumero.Text = dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve")
        lDate.Text = dsReleve.Tables("RELEVE").Rows(0).Item("Date")
        lDu.Text = dsReleve.Tables("RELEVE").Rows(0).Item("DateDebut")
        lAu.Text = dsReleve.Tables("RELEVE").Rows(0).Item("DateFin")
        lTotal.Text = dsReleve.Tables("RELEVE").Rows(0).Item("Total")
        lMontant.Text = dsReleve.Tables("RELEVE").Rows(0).Item("Montant")
        lReste.Text = dsReleve.Tables("RELEVE").Rows(0).Item("Reste")

        If dsReleve.Tables("RELEVE").Rows(0).Item("TiersPayant") = True Then
            lType.Text = "TIERS PAYANT"
        End If
        If dsReleve.Tables("RELEVE").Rows(0).Item("PriseEnCharge") = True Then
            lType.Text = "PRISE EN CHARGE"
        End If
        If dsReleve.Tables("RELEVE").Rows(0).Item("Appareillage") = True Then
            lType.Text = "APPAREILLAGE"
        End If
        If dsReleve.Tables("RELEVE").Rows(0).Item("MaladieOrdinaire") = True Then
            lType.Text = "MALADIE ORDINAIRE"
        End If
        If dsReleve.Tables("RELEVE").Rows(0).Item("TiersPayant") = True And dsReleve.Tables("RELEVE").Rows(0).Item("MaladieOrdinaire") = True Then
            lType.Text = "TIERS PAYANT + MALADIE ORDINAIRE"
        End If
        If dsReleve.Tables("RELEVE").Rows(0).Item("TiersPayant") = True And dsReleve.Tables("RELEVE").Rows(0).Item("Appareillage") = True And dsReleve.Tables("RELEVE").Rows(0).Item("PriseEnCharge") = True Then
            lType.Text = "TOUS"
        End If

        cmdReleve.CommandText = "SELECT ROW_NUMBER() OVER(ORDER BY NumeroReleve ASC) AS RowNumber ,NumeroReleve, " + _
                                    " RELEVE_CNAM_DETAILS.NumeroVente, " + _
                                    " CONVERT(DATE,RELEVE_CNAM_DETAILS.Date) AS DATE, " + _
                                    " RELEVE_CNAM_DETAILS.CodeClient, " + _
                                    " CLIENT.Nom, " + _
                                    " VENTE.NomMalade," + _
                                    " VENTE.DateNaissance, " + _
                                    " VENTE.LibelleLienDeParente, " + _
                                    " VENTE.Rang, " + _
                                    " CLIENT.IdentifiantCnam AS IdentifiantClient, " + _
                                    " RELEVE_CNAM_DETAILS.TotalTTC, " + _
                                    " RELEVE_CNAM_DETAILS.MontantCnam, " + _
                                    " RELEVE_CNAM_DETAILS.Regle, " + _
                                    " ISNULL(MEDECIN.IdentifiantCNAM, VENTE.IdentifiantCNAMMedecin) as IdentifiantMedecin, " + _
                                    " MEDECIN.NomMedecin, " + _
                                    " MEDECIN.CodeMedecin, " + _
                                    " RELEVE_CNAM_DETAILS.DateOrdonnance, " + _
                                    " VENTE.CodeLienDeParente, " + _
                                    " VENTE.CodeAPCI, " + _
                                    " APCI.NomAPCI, " + _
                                    " VENTE.DureeTraitement," + _
                                    " RELEVE_CNAM_DETAILS.MontantCnam-RELEVE_CNAM_DETAILS.Regle AS Reste" + _
                                    " ,ISNULL(CLIENT.CodeMedecin,0) as CodeMedecinFamilleFicheClient, VENTE.CodeMedecinFamille " + _
                                    " FROM RELEVE_CNAM_DETAILS" + _
                                    " LEFT OUTER JOIN CLIENT ON RELEVE_CNAM_DETAILS.CodeClient =CLIENT.CodeClient  " + _
                                    " LEFT OUTER JOIN MEDECIN ON RELEVE_CNAM_DETAILS.CodeMedecin =MEDECIN.CodeMedecin " + _
                                    " AND MEDECIN.IdentifiantCNAM<>'' " + _
                                    " LEFT OUTER JOIN VENTE ON RELEVE_CNAM_DETAILS.NumeroVente =VENTE.NumeroVente " + _
                                    " LEFT OUTER JOIN APCI ON VENTE.CodeAPCI =APCI.CodeAPCI " + _
                                    " WHERE NumeroReleve=" + Quote(NuemroReleve) + _
                                    " ORDER BY NumeroLigne "

        'VENTE.MontantCnam-(SELECT SUM(MontantRegle) FROM REGLEMENT_CNAM_VENTE WHERE RELEVE_CNAM_DETAILS.NumeroVente=NumeroVente )

        daReleve = New SqlDataAdapter(cmdReleve)
        daReleve.Fill(dsReleve, "RELEVE_CNAM_DETAILS")
        cbReleve = New SqlCommandBuilder(daReleve)

        If dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows.Count > 0 Then
            LienDeParentePremiereLigne = dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(0).Item("LibelleLienDeParente")
            CodeApciPremiereLigne = dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(0).Item("NomAPCI").ToString
        End If

        With gReleves
            .Columns.Clear()
            .DataSource = dsReleve
            .DataMember = "RELEVE_CNAM_DETAILS"
            .Rebind(False)
            .Columns("RowNumber").Caption = "N°"
            .Columns("NumeroVente").Caption = "Num Vente"
            .Columns("Date").Caption = "Date"
            .Columns("Nom").Caption = "Client"
            .Columns("IdentifiantClient").Caption = "N°CNAM Client"
            .Columns("TotalTTC").Caption = "M.Total"
            .Columns("MontantCnam").Caption = "Montant A Rem"
            .Columns("Regle").Caption = "Déja Reglé"
            .Columns("Reste").Caption = "Reste"
            .Columns("NomMedecin").Caption = "Nom Med"
            .Columns("IdentifiantMedecin").Caption = "Id CNAM Med"
            .Columns("DateOrdonnance").Caption = "Date Ord"
            .Columns("NomMalade").Caption = "Nom Malade"
            .Columns("Rang").Caption = "Rang"
            .Columns("DateNaissance").Caption = "Date Nais"
            .Columns("LibelleLienDeParente").Caption = "Lien"
            .Columns("NomAPCI").Caption = "APCI"
            .Columns("DureeTraitement").Caption = "Durée"
            .Columns("CodeLienDeParente").Caption = "Lien Code"
            .Columns("CodeAPCI").Caption = "APCI Code"

            ' Centrer tous les entêtes SPLIT 1 
            For I = 0 To .Columns.Count - 1
                .Splits(1).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(1).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(1).DisplayColumns(I).Locked = True
            Next

            .Splits(1).DisplayColumns("IdentifiantClient").Locked = False
            .Splits(1).DisplayColumns("NomMedecin").Locked = False
            .Splits(1).DisplayColumns("IdentifiantMedecin").Locked = False
            .Splits(1).DisplayColumns("NomMalade").Locked = False
            .Splits(1).DisplayColumns("NomMedecin").Locked = False
            .Splits(1).DisplayColumns("Rang").Locked = False
            .Splits(1).DisplayColumns("DateNaissance").Locked = False
            .Splits(1).DisplayColumns("LibelleLienDeParente").Locked = False
            .Splits(1).DisplayColumns("NomAPCI").Locked = False
            .Splits(1).DisplayColumns("DureeTraitement").Locked = False
            .Splits(1).DisplayColumns("DateOrdonnance").Locked = False

            .Splits(1).DisplayColumns("RowNumber").Visible = False
            .Splits(0).DisplayColumns("RowNumber").Width = 40

            .Splits(1).DisplayColumns("NumeroReleve").Width = 0
            .Splits(1).DisplayColumns("NumeroReleve").Visible = False
            .Splits(1).DisplayColumns("NumeroReleve").AllowSizing = False

            .Splits(1).DisplayColumns("NumeroVente").Width = 200
            .Splits(1).DisplayColumns("NumeroVente").Visible = False
            .Splits(1).DisplayColumns("Date").Width = 100
            .Splits(1).DisplayColumns("Date").Visible = False
            .Splits(1).DisplayColumns("CodeClient").Visible = False
            .Splits(1).DisplayColumns("Nom").Width = 160
            .Splits(1).DisplayColumns("Nom").Visible = False
            .Splits(1).DisplayColumns("Nom").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("IdentifiantClient").Width = 120 '80
            .Splits(1).DisplayColumns("TotalTTC").Width = 70
            .Splits(1).DisplayColumns("TotalTTC").Visible = False
            .Splits(1).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("MontantCnam").Width = 70
            .Splits(1).DisplayColumns("MontantCnam").Visible = False
            .Splits(1).DisplayColumns("MontantCnam").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("Regle").Width = 70
            .Splits(1).DisplayColumns("Regle").Visible = False
            .Splits(1).DisplayColumns("Regle").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("Reste").Width = 70
            .Splits(1).DisplayColumns("Reste").Visible = False
            .Splits(1).DisplayColumns("Reste").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("NomMedecin").Width = 100
            .Splits(1).DisplayColumns("NomMedecin").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("CodeMedecin").Width = 0
            .Splits(1).DisplayColumns("CodeMedecin").Visible = False
            .Splits(1).DisplayColumns("IdentifiantMedecin").Width = 160 '100
            .Splits(1).DisplayColumns("DateOrdonnance").Width = 100 '70
            .Splits(1).DisplayColumns("NomMalade").Width = 100
            .Splits(1).DisplayColumns("NomMalade").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("Rang").Width = 40
            .Splits(1).DisplayColumns("DateNaissance").Width = 100 '70
            .Splits(1).DisplayColumns("LibelleLienDeParente").Width = 80
            .Splits(1).DisplayColumns("NomAPCI").Width = 40
            .Splits(1).DisplayColumns("DureeTraitement").Width = 50
            .Splits(1).DisplayColumns("CodeLienDeParente").Visible = False
            .Splits(1).DisplayColumns("CodeAPCI").Visible = False
            '.Splits(1).DisplayColumns("Reste").Width = 70

            .Splits(0).DisplayColumns("CodeMedecinFamille").Visible = False
            .Splits(1).DisplayColumns("CodeMedecinFamille").Visible = False
            .Splits(0).DisplayColumns("CodeMedecinFamilleFicheClient").Visible = False
            .Splits(1).DisplayColumns("CodeMedecinFamilleFicheClient").Visible = False

            '.Splits(1).SplitSize = 100
            '.Splits(1).SplitSizeMode = SizeModeEnum.NumberOfColumns
            .Splits(1).ColumnCaptionHeight = 40
            .Splits(1).RecordSelectors = False

            'le deuxieme split est coloré en bleu
            'For I = 0 To .Columns.Count - 1
            '    .Splits(1).DisplayColumns(I).Style.BackColor = Color.FromArgb(255, 230, 252, 251)
            'Next
            Try
                gReleves.Splits(1).DisplayColumns("IdentifiantClient").Style.BackColor = Color.FromArgb(192, 255, 255)
                gReleves.Splits(1).DisplayColumns("NomMalade").Style.BackColor = Color.FromArgb(192, 255, 255)
                gReleves.Splits(1).DisplayColumns("DateNaissance").Style.BackColor = Color.FromArgb(192, 255, 255)
                gReleves.Splits(1).DisplayColumns("LibelleLienDeParente").Style.BackColor = Color.FromArgb(192, 255, 255)
                gReleves.Splits(1).DisplayColumns("Rang").Style.BackColor = Color.FromArgb(192, 255, 255)

                ' 
                '' ''gReleves.Splits(1).DisplayColumns("IdentifiantMedecin").Style.BackColor = Color.FromArgb(255, 192, 255)
                '' ''gReleves.Splits(1).DisplayColumns("NomMedecin").Style.BackColor = Color.FromArgb(255, 192, 255)

                ' 
                gReleves.Splits(1).DisplayColumns("MontantCnam").Style.BackColor = Color.FromArgb(192, 255, 192)
                gReleves.Splits(1).DisplayColumns("TotalTTC").Style.BackColor = Color.FromArgb(192, 255, 192)

                ' 
                gReleves.Splits(1).DisplayColumns("DateOrdonnance").Style.BackColor = Color.FromArgb(255, 224, 192)
                gReleves.Splits(1).DisplayColumns("NomAPCI").Style.BackColor = Color.FromArgb(255, 224, 192)
                gReleves.Splits(1).DisplayColumns("DureeTraitement").Style.BackColor = Color.FromArgb(255, 224, 192)
            Catch
            End Try
            ' Centrer tous les entêtes SPLIT 0 
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("IdentifiantClient").Locked = False
            .Splits(0).DisplayColumns("NomMedecin").Locked = False
            .Splits(0).DisplayColumns("IdentifiantMedecin").Locked = False
            .Splits(0).DisplayColumns("NomMalade").Locked = False
            .Splits(0).DisplayColumns("Rang").Locked = False
            .Splits(0).DisplayColumns("DateNaissance").Locked = False
            .Splits(0).DisplayColumns("LibelleLienDeParente").Locked = False
            .Splits(0).DisplayColumns("NomAPCI").Locked = False
            .Splits(0).DisplayColumns("DureeTraitement").Locked = False

            .Splits(1).DisplayColumns("NumeroReleve").Width = 0
            .Splits(1).DisplayColumns("NumeroReleve").Visible = False
            .Splits(1).DisplayColumns("NumeroReleve").AllowSizing = False

            .Splits(0).DisplayColumns("NumeroVente").Width = 120
            .Splits(0).DisplayColumns("Date").Width = 100
            .Splits(0).DisplayColumns("CodeClient").Visible = False
            .Splits(0).DisplayColumns("Nom").Width = 160
            .Splits(0).DisplayColumns("Nom").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("IdentifiantClient").Width = 80
            .Splits(0).DisplayColumns("IdentifiantClient").Visible = False
            .Splits(0).DisplayColumns("TotalTTC").Width = 70
            .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("MontantCnam").Width = 70
            .Splits(0).DisplayColumns("MontantCnam").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Regle").Width = 70
            .Splits(0).DisplayColumns("Regle").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Reste").Width = 70
            .Splits(0).DisplayColumns("Reste").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("NomMedecin").Width = 100
            .Splits(0).DisplayColumns("NomMedecin").Visible = False
            .Splits(0).DisplayColumns("NomMedecin").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("CodeMedecin").Width = 0
            .Splits(0).DisplayColumns("CodeMedecin").Visible = False
            .Splits(0).DisplayColumns("IdentifiantMedecin").Width = 80
            .Splits(0).DisplayColumns("IdentifiantMedecin").Visible = False
            .Splits(0).DisplayColumns("DateOrdonnance").Width = 70
            .Splits(0).DisplayColumns("DateOrdonnance").Visible = False
            .Splits(0).DisplayColumns("NomMalade").Width = 200
            .Splits(0).DisplayColumns("NomMalade").Visible = False
            .Splits(0).DisplayColumns("NomMalade").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Rang").Width = 50
            .Splits(0).DisplayColumns("Rang").Visible = False
            .Splits(0).DisplayColumns("DateNaissance").Width = 70
            .Splits(0).DisplayColumns("DateNaissance").Visible = False
            .Splits(0).DisplayColumns("LibelleLienDeParente").Width = 80
            .Splits(0).DisplayColumns("LibelleLienDeParente").Visible = False
            .Splits(0).DisplayColumns("NomAPCI").Width = 40
            .Splits(0).DisplayColumns("NomAPCI").Visible = False
            .Splits(0).DisplayColumns("DureeTraitement").Width = 50
            .Splits(0).DisplayColumns("DureeTraitement").Visible = False
            .Splits(0).DisplayColumns("CodeLienDeParente").Visible = False
            .Splits(0).DisplayColumns("CodeAPCI").Visible = False
            .Splits(0).DisplayColumns("Reste").Width = 70


            .Splits(0).SplitSize = 400
            .Splits(0).SplitSizeMode = SizeModeEnum.NumberOfColumns
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gReleves)
        End With

        Me.tdbdAPCI.DataSource = dsReleve
        Me.tdbdAPCI.DataMember = "APCI"
        Me.gReleves.Columns("NomAPCI").DropDown = Me.tdbdAPCI
        gReleves.Columns("NomAPCI").Value = tdbdAPCI.Columns("NomAPCI").Value
        Me.tdbdAPCI.Columns("CodeAPCI").Caption = "Code APCI"
        Me.tdbdAPCI.Columns("NomAPCI").Caption = "Nom APCI"
        Me.tdbdAPCI.DisplayMember = "NomAPCI"
        Me.tdbdAPCI.DisplayColumns("CodeAPCI").Width = 0
        Me.tdbdAPCI.DisplayColumns("NomAPCI").Width = 100

        Me.tdbdLienDeParente.DataSource = dsReleve
        Me.tdbdLienDeParente.DataMember = "LIEN_PARENTE"
        Me.gReleves.Columns("LibelleLienDeParente").DropDown = Me.tdbdLienDeParente
        gReleves.Columns("LibelleLienDeParente").Value = tdbdLienDeParente.Columns("LibelleLienDeParente").Value
        Me.tdbdLienDeParente.Columns("CodeLienDeParente").Caption = "Code Lien"
        Me.tdbdLienDeParente.Columns("LibelleLienDeParente").Caption = "Nom Lien"
        Me.tdbdLienDeParente.DisplayMember = "LibelleLienDeParente"
        Me.tdbdLienDeParente.DisplayColumns("CodeLienDeParente").Width = 0
        Me.tdbdLienDeParente.DisplayColumns("LibelleLienDeParente").Width = 100

        If dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows.Count > 0 Then
            gReleves(0, "LibelleLienDeParente") = LienDeParentePremiereLigne
            gReleves(0, "NomAPCI") = CodeApciPremiereLigne
        End If

    End Sub

    Private Sub bValiderReleve_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bValiderReleve.Click
        Dim StrMajLOT As String
        Dim I As Integer = 0
        Dim MontantDuReglement As Double = 0.0

        Dim StrSQL As String = ""
        Dim NumeroReglement As Integer = 0

        Dim CodeNatureReglement As Integer = 0
        Dim DateEcheanceReglement As String = ""
        Dim CodeBanque As String = ""
        Dim NumeroCheque As String = ""
        Dim LibelleReglement As String = ""
        Dim NomInscritSurLeCheque As String = ""
        Dim Montant As Double = 0.0
        Dim Encaisse As Boolean = False
        Dim CodeOperateur As String = ""
        Dim ReglementNonVide As Boolean = False
        Dim ConfirmerReglement As Boolean = False

        Dim MontantRetenueSource As Double = 0.0
        Dim AppliquerRetenueSource As Boolean = False

        If ModificationReglement = "M" Then

            For Each item As String In ListNumeroVents
                Try
                    cmdReleve.Connection = ConnectionServeur
                    cmdReleve.CommandText = " DELETE FROM RELEVE_CNAM_DETAILS WHERE NumeroVente = " & Quote(item)
                    cmdReleve.ExecuteNonQuery()
                Catch
                End Try
            Next

            Try
                cmdReleve.Connection = ConnectionServeur
                cmdReleve.CommandText = " UPDATE RELEVE_CNAM SET Total = Total - " & MontantTotalTTC & ", Montant = Montant - " & MontantR & ", Reste = Reste - " & MontantR & "  WHERE NumeroReleve = " & Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroReleve"))
                cmdReleve.ExecuteNonQuery()
            Catch
            End Try

            '---------------------- enregistrement des modifications du relévé 
            For I = 0 To dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows.Count - 1
                ' mise a jour du fichier vente 
                If dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("CodeAPCI").ToString = "" Then
                    StrMajLOT = "UPDATE VENTE SET NomMalade = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NomMalade")) + _
                            " , Rang = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Rang").ToString) + _
                            " , DateNaissance = " + _
                            IIf(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateNaissance").ToString = "", "NULL", Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateNaissance").ToString)) + _
                            " , DateOrdonnance = " + _
                            IIf(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateOrdonnance").ToString = "", "NULL", Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateOrdonnance").ToString)) + _
                            " , CodeLienDeParente = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("CodeLienDeParente").ToString) + _
                            " , LibelleLienDeParente = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("LibelleLienDeParente")) + _
                            " , CodeAPCI=NULL" + _
                            " , DureeTraitement = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DureeTraitement").ToString) + _
                            " , IdentifiantCNAMMedecin = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantMedecin")) + _
                            " , CodeMedecinPrescripteur = " + _
                            Quote(RecupererValeurExecuteScalaire("CodeMedecin", "MEDECIN", "IdentifiantCNAM", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantMedecin").ToString)) + _
                            " WHERE NumeroVente = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente"))
                Else
                    StrMajLOT = "UPDATE VENTE SET NomMalade = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NomMalade").ToString) + _
                            ", Rang = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Rang").ToString) + _
                            " , DateNaissance = " + _
                            IIf(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateNaissance").ToString = "", "NULL", Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateNaissance").ToString)) + _
                            " , DateOrdonnance = " + _
                            IIf(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateOrdonnance").ToString = "", "NULL", Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateOrdonnance").ToString)) + _
                            " , CodeLienDeParente = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("CodeLienDeParente").ToString) + _
                            " , LibelleLienDeParente = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("LibelleLienDeParente").ToString) + _
                            " , CodeAPCI = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("CodeAPCI").ToString) + _
                            " , DureeTraitement=" + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DureeTraitement").ToString) + _
                            " , IdentifiantCNAMMedecin = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantMedecin").ToString) + _
                            " , CodeMedecinPrescripteur = " + _
                            Quote(RecupererValeurExecuteScalaire("CodeMedecin", "MEDECIN", "IdentifiantCNAM", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantMedecin").ToString)) + _
                            " WHERE NumeroVente = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente").ToString)

                End If
                ' RecupererValeurExecuteScalaire("CodeMedecin", "MEDECIN", "IdentifiantCNAM", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantMedecin"))

                cmdReleve.Connection = ConnectionServeur
                cmdReleve.CommandText = StrMajLOT
                Try
                    cmdReleve.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                Try
                    cmdReleve.Connection = ConnectionServeur
                    cmdReleve.CommandText = "UPDATE RELEVE_CNAM_DETAILS SET DateOrdonnance = " & IIf(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateOrdonnance").ToString = "", "NULL", Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateOrdonnance").ToString)) & " WHERE NumeroReleve = " & Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroReleve")) & " AND NumeroVente = " & Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente"))
                    cmdReleve.ExecuteNonQuery()
                Catch
                End Try

                If (lType.Text <> "PRISE EN CHARGE" And lType.Text <> "APPAREILLAGE") Then
                    Try
                        If (RecupererValeurExecuteScalaire("CodeMedecin", "MEDECIN", "IdentifiantCNAM", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantMedecin")) <> "") Then
                            cmdReleve.Connection = ConnectionServeur
                            cmdReleve.CommandText = "UPDATE RELEVE_CNAM_DETAILS SET RELEVE_CNAM_DETAILS.CodeMedecin = " & Quote(RecupererValeurExecuteScalaire("CodeMedecin", "MEDECIN", "IdentifiantCNAM", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantMedecin"))) & "WHERE NumeroReleve = " & Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroReleve")) & " AND NumeroVente = " & Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente"))
                            cmdReleve.ExecuteNonQuery()
                        Else
                            MsgBox("Identification medecin CNAM " & dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantMedecin") & "introvable.", MsgBoxStyle.Critical)
                        End If

                    Catch
                    End Try
                End If


                Try
                    cmdReleve.Connection = ConnectionServeur
                    cmdReleve.CommandText = "UPDATE VENTE_DETAILS SET VENTE_DETAILS.DureeTraitement = " + Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DureeTraitement").ToString) + " WHERE NumeroVente = " + Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente").ToString())

                    cmdReleve.ExecuteNonQuery()

                Catch
                End Try

                '' mise a jour du fichier Client (identifiant CNAM)
                StrMajLOT = "UPDATE CLIENT SET IdentifiantCnam = " + _
                            Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantClient")) + _
                             " WHERE CodeClient = " + _
                             Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("CodeClient"))

                cmdReleve.Connection = ConnectionServeur
                cmdReleve.CommandText = StrMajLOT
                Try
                    cmdReleve.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                Try
                    StrMajLOT = "UPDATE " + _
                                "   VENTE " + _
                                "SET " + _
                                "   ,CodeAPCI = " & Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("CodeAPCI")) + _
                                "WHERE " + _
                                "   NumeroVente = " & Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente"))

                    cmdReleve.Connection = ConnectionServeur
                    cmdReleve.CommandText = StrMajLOT
                    Try
                        cmdReleve.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try
                Catch
                End Try

                '' mise a jour du fichier MEDECIN (Nom)
                'Try
                '    StrMajLOT = "UPDATE MEDECIN SET NomMedecin = " + _
                '                               Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NomMedecin")) + _
                '                               " WHERE CodeMedecin=" + _
                '                               Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("CodeMedecin"))
                '
                '    cmdReleve.Connection = ConnectionServeur
                '    cmdReleve.CommandText = StrMajLOT
                '    Try
                '        cmdReleve.ExecuteNonQuery()
                '    Catch ex As Exception
                '        Console.WriteLine(ex.Message)
                '    End Try
                'Catch
                'End Try

                ' '' mise a jour du fichier RELEVE_CNAM_DETAILS (Date d'ordonnance)
                'StrMajLOT = "UPDATE RELEVE_CNAM_DETAILS SET DateOrdonnance='" + _
                '             dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateOrdonnance") + _
                '             "' WHERE NumeroReleve='" + _
                '             dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroReleve") + _
                '             "' AND NumeroVente='" + dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente") + "'"

                'cmdReleve.Connection = ConnectionServeur
                'cmdReleve.CommandText = StrMajLOT
                'Try
                '    cmdReleve.ExecuteNonQuery()
                'Catch ex As Exception
                '    Console.WriteLine(ex.Message)
                'End Try

            Next

            MsgBox("Modification terminée avec succès !", MsgBoxStyle.Information, "Information")
            Me.Close()

        ElseIf ModificationReglement = "R" And bReglement.Text = "Annuler                 F8" Then
            '------------------ enregistrement des règlements de chauqe vente et du règlement global du CNAM

            For I = 0 To gReleves.RowCount - 1
                If gReleves(I, "Reste") <> 0 Then
                    ReglementNonVide = True
                End If
            Next

            If ReglementNonVide = False Then
                MsgBox("Règlement vide !", MsgBoxStyle.Information, "Information")
                Exit Sub
            End If


            For I = 0 To gReleves.RowCount - 1
                MontantDuReglement += gReleves(I, "Reste")
            Next
            If MontantDuReglement <> 0 Then
                Dim MyReglemnt As New fInformationReglement
                MyReglemnt.Montant = MontantDuReglement
                MyReglemnt.ShowDialog()

                CodeNatureReglement = MyReglemnt.CodeNatureReglement
                DateEcheanceReglement = MyReglemnt.DateEcheanceReglement
                CodeBanque = MyReglemnt.CodeBanque
                NumeroCheque = MyReglemnt.NumeroCheque
                LibelleReglement = MyReglemnt.LibelleReglement
                NomInscritSurLeCheque = MyReglemnt.NomInscritSurLeCheque
                Montant = MyReglemnt.Montant
                Encaisse = MyReglemnt.Encaisse
                CodeOperateur = MyReglemnt.CodeOperateurLocal
                ConfirmerReglement = MyReglemnt.ConfirmerReglement

                AppliquerRetenueSource = MyReglemnt.AppliquerRetenueSource
                MontantRetenueSource = MyReglemnt.MontantRetenueSource

                MyReglemnt.Dispose()
                MyReglemnt.Close()

                If ConfirmerReglement = False Then
                    Exit Sub
                End If

            End If








            'initialisation d'un nouveau règlement 
            StrSQL = " SELECT max([NumeroReglementCnam]) FROM REGLEMENT_CNAM"
            cmdReleve.Connection = ConnectionServeur
            cmdReleve.CommandText = StrSQL
            Try
                NumeroReglement = cmdReleve.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            NumeroReglement = NumeroReglement + 1

            StrSQL = "INSERT INTO REGLEMENT_CNAM " + _
                    "(""NumeroReglementCnam"",""LibelleReglement"",""CodeNatureReglement""" + _
                    ",""Date"",""DateEcheance"",""Montant"",""NumeroCheque"",""LibellePoste""" + _
                    ",""NomInscritSurLeCheque"",""CodeBanque"",""Vider"",""Encaisse"",""CodePersonnel"", AppliquerRetenueSource, MontantRetenueSource) " + _
                    " VALUES( " + Quote(NumeroReglement.ToString) + _
                    "," + Quote(LibelleReglement) + _
                    "," + Quote(CodeNatureReglement.ToString) + _
                    "," + Quote(System.DateTime.Now.ToString) + _
                    "," + Quote(DateEcheanceReglement.ToString) + _
                    "," + Quote(Montant.ToString) + _
                    "," + Quote(NumeroCheque) + _
                    "," + Quote(System.Environment.GetEnvironmentVariable("Poste")) + _
                    "," + Quote(NomInscritSurLeCheque) + _
                    "," + CodeBanque.ToString + _
                    "," + "'False'" + _
                    "," + "'False'" + _
                    "," + Quote(CodeOperateur.ToString) + _
                    "," + Quote(AppliquerRetenueSource.ToString) + _
                    "," + Quote(MontantRetenueSource.ToString) + _
                    " )"

            cmdReleve.Connection = ConnectionServeur
            cmdReleve.CommandText = StrSQL
            Try
                cmdReleve.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            'afféctation des règlements aux vente dans la table REGLEMENT_CNAM_VENTE
            For I = 0 To dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows.Count - 1
                'seulement les ventes qui ont des reglements <> 0

                If dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Reste") <> 0 Then
                    StrSQL = "INSERT INTO REGLEMENT_CNAM_VENTE " + _
                    "(""NumeroReglementCnam"",""NumeroReleve"",""NumeroVente""" + _
                    ",""MontantRegle"") " + _
                    " VALUES(" + Quote(NumeroReglement.ToString) + _
                    "," + Quote(NuemroReleve) + _
                    "," + Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente")) + _
                    "," + Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Reste").ToString) + _
                     " )"

                    cmdReleve.Connection = ConnectionServeur
                    cmdReleve.CommandText = StrSQL
                    Try
                        cmdReleve.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try
                    'changement des valeur reglé dans le fichier RELEVE_CNAM_DETAILS
                    StrSQL = "UPDATE RELEVE_CNAM_DETAILS SET Regle = Regle + " + _
                       dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Reste").ToString + _
                       " WHERE NumeroReleve = " + Quote(NuemroReleve) + _
                       " AND NumeroVente = " + Quote(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente"))

                    cmdReleve.Connection = ConnectionServeur
                    cmdReleve.CommandText = StrSQL
                    Try
                        cmdReleve.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try
                End If
            Next

            'changement de valeur RESTE dans le fichier RELEVE_CNAM
            StrSQL = "UPDATE RELEVE_CNAM SET Reste=Reste-" + _
               Montant.ToString + _
               " WHERE NumeroReleve='" + NuemroReleve + "'"

            cmdReleve.Connection = ConnectionServeur
            cmdReleve.CommandText = StrSQL
            Try
                cmdReleve.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            InsertionDansLog("REGLEMENT_RELEVE_CNAM", "Reglement du relevé  " + NuemroReleve, CodeOperateur, System.DateTime.Now, "CNAM", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
            MsgBox("Règlement terminé avec succès !", MsgBoxStyle.Information, "Information")

            _fReleveeCNAM.VerifierMontant()
            Me.Close()

        ElseIf ModificationReglement = "R" And bReglement.Text = "Règlement                 F8" Then
            MsgBox("Modification terminé avec succès !", MsgBoxStyle.Information, "Information")
            Me.Close()
        End If
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        ' Me.Close()
        Me.Close()
    End Sub

    Private Sub tdbdAPCI_SelChange(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.CancelEventArgs) Handles tdbdAPCI.SelChange
        gReleves.Columns("CodeAPCI").Value = tdbdAPCI.Columns("CodeAPCI").Value

    End Sub

    Private Sub tdbdLienDeParente_SelChange(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.CancelEventArgs) Handles tdbdLienDeParente.SelChange
        gReleves.Columns("CodeLienDeParente").Value = tdbdLienDeParente.Columns("CodeLienDeParente").Value
        'Try
        '    gReleves.Columns("NomMalade").Value = RecupererValeurExecuteScalaire("NomMalade", "CLIENT_FAMILLE", "CodeClient", "1")
        '    gReleves.Columns("Rang").Value = RecupererValeurExecuteScalaire("Rang", "CLIENT_FAMILLE", "CodeClient", "1")
        '    gReleves.Columns("DateNaissance").Value = RecupererValeurExecuteScalaire("DateNaissance", "CLIENT_FAMILLE", "CodeClient", "'" + dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(gReleves.Row).Item("CodeClient").ToString + "' AND ")
        'Catch
        'End Try
    End Sub

    Private Sub bReglement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bReglement.Click
        ModificationReglement = "R"
        Dim I As Integer = 0
        Dim Reste As Double = 0

        If bReglement.Text = "Règlement                 F8" Then
            Dim AfficherLaTotaliteDuMontant As Boolean = False

            Try
                AfficherLaTotaliteDuMontant = RecupererValeurExecuteScalaire("AfficherLaTotaliteDuMontant", "CNAM", "CodeCNAM", "1")
            Catch ex As Exception
                Try
                    AfficherLaTotaliteDuMontant = RecupererValeurExecuteScalaire("AfficherLaTotaliteDuMontant", "CNAM", "CodeCNAM", "0")
                Catch ex1 As Exception
                    AfficherLaTotaliteDuMontant = False
                End Try

            End Try




            Reste = RecupererValeurExecuteScalaire("Reste", "RELEVE_CNAM", "NumeroReleve", lNumero.Text)
            If Reste = 0 Then
                MsgBox("Relevé déja réglé !", MsgBoxStyle.Information, "Information")
                Exit Sub
            End If

            With gReleves

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Locked = True
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(1).DisplayColumns(I).Locked = True
                Next

                .Splits(0).DisplayColumns("Reste").Locked = False
                .Columns("Reste").Caption = "A Régler"

                For I = 0 To gReleves.RowCount - 1
                    If AfficherLaTotaliteDuMontant = True Then
                        gReleves(I, "Reste") = gReleves(I, "MontantCnam") - gReleves(I, "Regle")
                    Else
                        gReleves(I, "Reste") = 0
                    End If
                Next

            End With
            Me.gReleves.Columns("NomAPCI").DropDown = Nothing
            Me.gReleves.Columns("LibelleLienDeParente").DropDown = Nothing

            'For I = 0 To gReleves.Columns.Count - 1
            '    gReleves.Splits(1).DisplayColumns(I).Style.BackColor = Color.White 'Color.FromArgb(255, 230, 252, 251)
            'Next

            Try
                gReleves.Splits(1).DisplayColumns("IdentifiantClient").Style.BackColor = Color.FromArgb(192, 255, 255)
                gReleves.Splits(1).DisplayColumns("NomMalade").Style.BackColor = Color.FromArgb(192, 255, 255)
                gReleves.Splits(1).DisplayColumns("DateNaissance").Style.BackColor = Color.FromArgb(192, 255, 255)
                gReleves.Splits(1).DisplayColumns("LibelleLienDeParente").Style.BackColor = Color.FromArgb(192, 255, 255)
                gReleves.Splits(1).DisplayColumns("Rang").Style.BackColor = Color.FromArgb(192, 255, 255)

                ' 
                gReleves.Splits(1).DisplayColumns("IdentifiantMedecin").Style.BackColor = Color.FromArgb(255, 192, 255)
                gReleves.Splits(1).DisplayColumns("NomMedecin").Style.BackColor = Color.FromArgb(255, 192, 255)

                ' 
                gReleves.Splits(1).DisplayColumns("MontantCnam").Style.BackColor = Color.FromArgb(192, 255, 192)
                gReleves.Splits(1).DisplayColumns("TotalTTC").Style.BackColor = Color.FromArgb(192, 255, 192)

                ' 
                gReleves.Splits(1).DisplayColumns("DateOrdonnance").Style.BackColor = Color.FromArgb(255, 224, 192)
                gReleves.Splits(1).DisplayColumns("NomAPCI").Style.BackColor = Color.FromArgb(255, 224, 192)
                gReleves.Splits(1).DisplayColumns("DureeTraitement").Style.BackColor = Color.FromArgb(255, 224, 192)
            Catch
            End Try


            gReleves.Splits(0).DisplayColumns("Reste").Style.BackColor = Color.FromArgb(255, 200, 252, 251)
            With gReleves
                .Splits(1).DisplayColumns("Rang").Visible = False
                .Splits(1).DisplayColumns("DateNaissance").Visible = False
                .Splits(1).DisplayColumns("LibelleLienDeParente").Visible = False
                .Splits(1).DisplayColumns("NomAPCI").Visible = False
                .Splits(1).DisplayColumns("DureeTraitement").Visible = False
                .Splits(1).DisplayColumns("NomMalade").Width = 80
            End With

            'CalculerMontants()
            gReleves.Focus()

            gReleves.Col = 20

            gReleves.Splits(0).DisplayColumns("Reste").Locked = False
            gReleves.EditActive = True
        Else
            AfficherReleve()
        End If



        If bReglement.Text = "Règlement                 F8" Then
            bReglement.Text = "Annuler                 F8"
        Else
            bReglement.Text = "Règlement                 F8"
        End If

    End Sub

    Private Sub lMontant_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lMontant.Click

    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim TypeReleve As String = ""

        If dsReleve.Tables("RELEVE").Rows.Count = 0 Then
            MsgBox("Liste vide !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        Dim MyImpressionReleve As New fImpressionDesRelevesCNAM
        MyImpressionReleve.ShowDialog()

        TypeReleve = fImpressionDesRelevesCNAM.Confirmer

        MyImpressionReleve.Close()
        MyImpressionReleve.Dispose()

        Dim CondCrystal As String = ""

        CondCrystal = "1=1 AND {Vue_EtatReleveCNAM.NumeroReleve} = '" + lNumero.Text + "'"

        If TypeReleve = "Regles" Then
            CondCrystal += " AND {Vue_EtatReleveCNAM.Reste} = 0"
        ElseIf TypeReleve = "Non Regles" Then
            CondCrystal += " AND {Vue_EtatReleveCNAM.Reste} <> 0"
        ElseIf TypeReleve = "Rien" Then
            Exit Sub
        End If

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression de relevé Cnam" Then
                num = I
            End If
        Next
        CR.FileName = Application.StartupPath + "\EtatDeReleveCNAM.rpt"

        CR.SetParameterValue("DateDebut", lDu.Text)
        CR.SetParameterValue("DateFin", lAu.Text)

        CR.SetParameterValue("ChiffreEnLettre", ChiffresEnLettres(CDbl(lMontantARegle.Text)))

        If dsReleve.Tables("RELEVE").Rows(0).Item("PriseEnCharge") = True Then
            CR.SetParameterValue("Titre", "DECOMPTE DES MEDICAMENTS DELIVRES PAR LE PHARMACIEN " & Chr(10) & " CONVENTIONNE " & Chr(10) & " DANS LE CADRE DU ACCORD PREALABLE")

            CR.SetParameterValue("NCNSS", "")
        ElseIf dsReleve.Tables("RELEVE").Rows(0).Item("Appareillage") = True Then

            CR.SetParameterValue("Titre", "DECOMPTE DES APPAREILLAGES DELIVRES PAR LE PHARMACIEN " & Chr(10) & " CONVENTIONNE ")
            Try
                CR.SetParameterValue("NCNSS", "N°CNSS : " + RecupererValeurExecuteScalaire("Affiliation1", "PARAMETRE_PHARMACIE", "Code", "1") + "/" + RecupererValeurExecuteScalaire("Affiliation2", "PARAMETRE_PHARMACIE", "Code", "1"))
            Catch ex As Exception
                CR.SetParameterValue("NCNSS", "")
            End Try

        Else
            CR.SetParameterValue("Titre", "DECOMPTE DES MEDICAMENTS DELIVRES PAR LE PHARMACIEN " & Chr(10) & " CONVENTIONNE " & Chr(10) & "DANS LE CADRE DU TIERS PAYANT")

            CR.SetParameterValue("NCNSS", "")
        End If

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent
        CR.RecordSelectionFormula = CondCrystal

        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression de relevé Cnam"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
        'Me.Close()
        Me.Close()
    End Sub

    Private Sub CalculerMontants()
        Dim MontantARegler As Double = 0.0
        Dim MontantDejaRegler As Double = 0.0
        Dim MontantEnCoursReglement As Double = 0.0
        Dim I As Integer = 0

        For I = 0 To gReleves.RowCount - 1
            MontantARegler += gReleves(I, "MontantCnam")
            MontantDejaRegler += gReleves(I, "Regle")
            If gReleves(I, "Reste").ToString <> "" Then
                MontantEnCoursReglement += gReleves(I, "Reste")
            End If
        Next

        lMontantARegle.Text = Math.Round(MontantARegler, 3)
        lMontantDejaRegle.Text = Math.Round(MontantDejaRegler, 3)

        lMontantEnCoursReglementt.Text = Math.Round(MontantEnCoursReglement, 3)

    End Sub

    Private Sub etatDeReleveCNAM1_InitReport(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CR.InitReport

    End Sub

    Private Sub gReleves_AfterColUpdate(sender As Object, e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gReleves.AfterColUpdate
        Try
            If (lType.Text <> "PRISE EN CHARGE" And lType.Text <> "APPAREILLAGE") Then
                gReleves.Columns("NomMedecin").Value = RecupererValeurExecuteScalaire("NomMedecin", "MEDECIN", "IdentifiantCNAM", gReleves(gReleves.Row, "IdentifiantMedecin"))
            End If
        Catch
        End Try
    End Sub

    Private Sub gReleves_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles gReleves.Click

    End Sub

    Private Sub gReleves_DoubleClick(sender As Object, e As System.EventArgs) Handles gReleves.DoubleClick
        Dim MyVenteAffiche As New fVenteJusteAffichage
        MyVenteAffiche.NumeroVente = gReleves(gReleves.Row, "NumeroVente")
        MyVenteAffiche.ShowDialog()
        MyVenteAffiche.Close()
        MyVenteAffiche.Dispose()
    End Sub

    Private Sub gReleves_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gReleves.KeyUp
        If e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right Then
            CalculerMontants()
        End If
    End Sub

    Private Sub bAPCI_MO_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAPCI_MO.Click

        If dsReleve.Tables("RELEVE").Rows(0).Item("TiersPayant") = False And dsReleve.Tables("RELEVE").Rows(0).Item("MaladieOrdinaire") = False Then
            MsgBox("Ce n'est pas un relevé APCI Maladie ordinaire !", MsgBoxStyle.Information, "Information")
            Exit Sub
        End If

        Dim StrEnteteReleve As String = ""
        Dim StrEnteteVente As String = ""
        Dim StrDetailVente As String = ""
        Dim NouveauEnregistrement As DataRow = Nothing

        Dim StrSQL As String = ""
        Dim Cmd As New SqlCommand
        Dim Valeur As String = ""

        Dim Chaine As String = ""

        Dim I As Integer = 0
        Dim J As Integer = 0
        Dim k As Integer = 0
        Dim CNSS As String = ""
        Dim CNRPS As String = ""
        Dim CNAM As String = ""
        Dim Caisse As String = ""
        Dim LienDeParente As String = ""
        Dim NomClient As String = ""
        Dim IsPriseEncharge As String = ""
        Dim DateNaissanceMalade As String = ""
        Dim CodeMedecin As String = ""
        Dim APCI As String = ""
        Dim CodeCnamClient As String = ""

        Dim NumeroCnamPharmacien As String = ""

        Dim cmdLisetVente As New SqlCommand
        Dim daListeVente As New SqlDataAdapter

        Dim NomClientCnam As String = ""

        If (dsReleve.Tables.IndexOf("ResultatTable") > -1) Then
            dsReleve.Tables("ResultatTable").Clear()
        Else
            Dim ResultatTable As DataTable = dsReleve.Tables.Add("ResultatTable")
            Dim MyColumn As DataColumn = New DataColumn()
            MyColumn.DataType = System.Type.GetType("System.String")
            MyColumn.AllowDBNull = False
            MyColumn.Caption = "Resultat"
            MyColumn.ColumnName = "Resultat"
            ResultatTable.Columns.Add(MyColumn)
        End If

        NumeroCnamPharmacien = Trim(RecupererValeurExecuteScalaire("NCnam", "PARAMETRE_PHARMACIE", "Code", 1))
        If NumeroCnamPharmacien = "" Then
            MsgBox("Code Cnam Pharmacien manquant " + CodePharmacien, MsgBoxStyle.Information, "Information")
        End If
        NumeroCnamPharmacien = FormatterCodePharmacien(NumeroCnamPharmacien, "Pharmacien")
        If NumeroCnamPharmacien = "False" Then
            Exit Sub
        End If

        'NumeroCnamPharmacien = Chaine.PadLeft(12 - Len(Trim(NumeroCnamPharmacien)), "0") + Trim(NumeroCnamPharmacien)
        Try
            dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve") = dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString.Substring(0, 4) + dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString.Substring(5, 6)
        Catch ex As Exception
        End Try

        StrEnteteReleve = tailleTypeCode.ToString + NumeroCnamPharmacien + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("Date").ToString.Substring(6, 4) + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("Date").ToString.Substring(3, 2) + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("Date").ToString.Substring(0, 2) + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("DateDebut").ToString.Substring(6, 4) + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("DateDebut").ToString.Substring(3, 2) + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("DateDebut").ToString.Substring(0, 2) + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("DateFin").ToString.Substring(6, 4) + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("DateFin").ToString.Substring(3, 2) + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("DateFin").ToString.Substring(0, 2) + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString + Space(20 - Len(dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString)) + _
                          Chaine.PadLeft(5 - Len(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows.Count.ToString), "0") + _
                          dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows.Count.ToString + _
                          Chaine.PadLeft(10 - Len((dsReleve.Tables("RELEVE").Rows(0).Item("Total") * 1000).ToString.Substring(0, Len((dsReleve.Tables("RELEVE").Rows(0).Item("Total") * 1000).ToString) - 4)), "0") + _
                          (dsReleve.Tables("RELEVE").Rows(0).Item("Total") * 1000).ToString.Substring(0, Len((dsReleve.Tables("RELEVE").Rows(0).Item("Total") * 1000).ToString) - 4) + _
                          Chaine.PadLeft(10 - Len(((dsReleve.Tables("RELEVE").Rows(0).Item("Total") - dsReleve.Tables("RELEVE").Rows(0).Item("Montant")) * 1000).ToString.Substring(0, Len(((dsReleve.Tables("RELEVE").Rows(0).Item("Total") - dsReleve.Tables("RELEVE").Rows(0).Item("Montant")) * 1000).ToString) - 4)), "0") + _
                          ((dsReleve.Tables("RELEVE").Rows(0).Item("Total") - dsReleve.Tables("RELEVE").Rows(0).Item("Montant")) * 1000).ToString.Substring(0, Len(((dsReleve.Tables("RELEVE").Rows(0).Item("Total") - dsReleve.Tables("RELEVE").Rows(0).Item("Montant")) * 1000).ToString) - 4) + _
                          Chaine.PadLeft(10 - Len((dsReleve.Tables("RELEVE").Rows(0).Item("Montant") * 1000).ToString.Substring(0, Len((dsReleve.Tables("RELEVE").Rows(0).Item("Montant") * 1000).ToString) - 4)), "0") + _
                          (dsReleve.Tables("RELEVE").Rows(0).Item("Montant") * 1000).ToString.Substring(0, Len((dsReleve.Tables("RELEVE").Rows(0).Item("Montant") * 1000).ToString) - 4)

        NouveauEnregistrement = dsReleve.Tables("ResultatTable").NewRow()
        NouveauEnregistrement("Resultat") = StrEnteteReleve
        dsReleve.Tables("ResultatTable").Rows.Add(NouveauEnregistrement)

        For I = 0 To dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows.Count - 1

            CNSS = RecupererValeurExecuteScalaire("CNSS", "CLIENT", "CodeClient", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("CodeClient"))
            CNRPS = RecupererValeurExecuteScalaire("CNRPS", "CLIENT", "CodeClient", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("CodeClient"))
            CNAM = RecupererValeurExecuteScalaire("AUTRE", "CLIENT", "CodeClient", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("CodeClient"))
            If CNSS = "True" Then
                Caisse = 1
            ElseIf CNRPS = "True" Then
                Caisse = "2"
            ElseIf CNAM = "True" Then
                Caisse = "3"
            End If

            Select Case dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("LibelleLienDeParente")
                Case "ASSURE"
                    LienDeParente = "0"
                Case "CONJOINT"
                    LienDeParente = "1"
                Case "ENFANT"
                    LienDeParente = "2"
                Case "PERE"
                    LienDeParente = "3"
                Case "MERE"
                    LienDeParente = "4"
            End Select

            'StrSQL = "SELECT DateNaissance FROM CLIENT_FAMILLE WHERE CodeClient='" + _
            '          dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("CodeClient") + _
            '          "' AND Nom='" + dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NomMalade") + "'"

            'Cmd.Connection = ConnectionServeur
            'Cmd.CommandText = StrSQL
            'Try
            '    DateNaissanceMalade = Cmd.ExecuteScalar().ToString
            'Catch ex As Exception
            '    Console.WriteLine(ex.Message)
            'End Try
            Try
                DateNaissanceMalade = dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateNaissance")
            Catch ex As Exception
                DateNaissanceMalade = ""
            End Try

            If DateNaissanceMalade = "00:00:00" Then
                DateNaissanceMalade = "01/01/1990"
            End If


            If DateNaissanceMalade = "" Then
                DateNaissanceMalade = "          "
            End If

            IsPriseEncharge = RecupererValeurExecuteScalaire("PriseEnCharge", "VENTE", "NumeroVente", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente"))

            'If IsPriseEncharge = "True" Then
            '    CodeMedecin = Chaine.PadLeft(12 - Len(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantMedecin").ToString), "0") + _
            '                  dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantMedecin").ToString
            'Else
            '    CodeMedecin = "000000000000"
            'End If

            CodeMedecin = Trim(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantMedecin").ToString)
            If CodeMedecin = "" Then
                MsgBox("Code Cnam Medecin manquant " + CodeMedecin, MsgBoxStyle.Information, "Information")
                Exit Sub
            End If
            CodeMedecin = FormatterCodePharmacien(CodeMedecin, "Medecin")
            If CodeMedecin = "False" Then
                Exit Sub
            End If

            If dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NomAPCI").ToString = "MO" Then
                APCI = "0000"
            Else
                APCI = dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NomAPCI").ToString
            End If

            CodeCnamClient = ""
            For l = 0 To Len(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantClient").ToString) - 1
                If IsNumeric(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantClient").ToString.Substring(l, 1)) Then
                    CodeCnamClient += dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantClient").ToString.Substring(l, 1)
                End If
            Next
            If Len(CodeCnamClient) > tailleCodeCNAM Then
                MsgBox("Code Cnam Client " + dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NomMalade").ToString + " est supérieur à " & tailleCodeCNAM & "  : (" + CodeCnamClient + ")", MsgBoxStyle.Information, "Information")
                Exit Sub
            End If

            'dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NomMalade").ToString.Substring(0, 1).IndexOfAny("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ ")

            NomClientCnam = dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NomMalade").ToString.Replace("-", " ").Replace(".", " ").Replace("_", " ").ToString

            J = Len(NomClientCnam)
            k = 0
            While k < J
                'If InStr("0123456789", CodePharmacien.Substring(I, 1)) = 0 Then
                If NomClientCnam.Substring(k, 1).IndexOfAny("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ ") = -1 Then
                    If k > 1 Then
                        NomClientCnam = NomClientCnam.Substring(0, k) + NomClientCnam.Substring(k + 1, J - k - 1)
                    Else
                        NomClientCnam = NomClientCnam.Substring(2, J - 1)
                    End If
                End If
                k += 1
                J = NomClientCnam.Length
            End While

            'dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows.Count
            StrEnteteVente = "2" + Chaine.PadLeft(5 - Len((I + 1).ToString), "0") + _
                             (I + 1).ToString + _
                             Caisse + _
                             Chaine.PadLeft(tailleCodeCNAM - Len(CodeCnamClient), "0") + _
                             CodeCnamClient + _
                             LienDeParente + _
                             NomClientCnam + _
                             Space(60 - Len(NomClientCnam)) + _
                             Chaine.PadLeft(2 - Len(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Rang").ToString), "0") + _
                             dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Rang").ToString + _
                             Chaine.PadLeft(4 - Len(Trim(DateNaissanceMalade.Substring(6, 4))), "0") + _
                             Trim(DateNaissanceMalade.Substring(6, 4)) + _
                             Chaine.PadLeft(2 - Len(Trim(DateNaissanceMalade.Substring(3, 2))), "0") + _
                             Trim(DateNaissanceMalade.Substring(3, 2)) + _
                             Chaine.PadLeft(2 - Len(Trim(DateNaissanceMalade.Substring(0, 2))), "0") + _
                             Trim(DateNaissanceMalade.Substring(0, 2)) + _
                             CodeMedecin + _
                             dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateOrdonnance").ToString.Substring(6, 4) + _
                             dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateOrdonnance").ToString.Substring(3, 2) + _
                             dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("DateOrdonnance").ToString.Substring(0, 2) + _
                             dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Date").ToString.Substring(6, 4) + _
                             dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Date").ToString.Substring(3, 2) + _
                             dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Date").ToString.Substring(0, 2) + _
                             Chaine.PadLeft(10 - Len((dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("TotalTTC") * 1000).ToString.Substring(0, Len((dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("TotalTTC") * 1000).ToString) - 4)), "0") + _
                             (dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("TotalTTC") * 1000).ToString.Substring(0, Len((dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("TotalTTC") * 1000).ToString) - 4) + _
                             Chaine.PadLeft(10 - Len(((dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("TotalTTC") - dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("MontantCnam")) * 1000).ToString.Substring(0, Len(((dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("TotalTTC") - dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("MontantCnam")) * 1000).ToString) - 4)), "0") + _
                             ((dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("TotalTTC") - dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("MontantCnam")) * 1000).ToString.Substring(0, Len(((dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("TotalTTC") - dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("MontantCnam")) * 1000).ToString) - 4) + _
                             Chaine.PadLeft(10 - Len((dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("MontantCnam") * 1000).ToString.Substring(0, Len((dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("MontantCnam") * 1000).ToString) - 4)), "0") + _
                             (dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("MontantCnam") * 1000).ToString.Substring(0, Len((dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("MontantCnam") * 1000).ToString) - 4) + _
                             Chaine.PadLeft(4 - Len(APCI), "0") + _
                             APCI

            NouveauEnregistrement = dsReleve.Tables("ResultatTable").NewRow()
            NouveauEnregistrement("Resultat") = StrEnteteVente
            dsReleve.Tables("ResultatTable").Rows.Add(NouveauEnregistrement)


            If (dsReleve.Tables.IndexOf("LISTE_ORDONNANCES") > -1) Then
                dsReleve.Tables("LISTE_ORDONNANCES").Clear()
            End If

            cmdLisetVente.CommandText = "SELECT CodePCT," + _
                                    "SUM(Qte) AS Qte," + _
                                    "SUM(VENTE_DETAILS.TotalTTC) AS TotalTTC," + _
                                    "VENTE_DETAILS.PriseEnCharge," + _
                                    "VENTE_DETAILS.AccordPrealable," + _
                                    "VENTE_DETAILS.TarifDeReference," + _
                                    "VENTE_DETAILS.DureeTraitement " + _
                                    "FROM VENTE_DETAILS LEFT OUTER JOIN ARTICLE ON " + _
                                    "VENTE_DETAILS.CodeArticle=ARTICLE.CodeArticle " + _
                                    "LEFT OUTER JOIN VENTE ON VENTE_DETAILS.NumeroVente=VENTE.NumeroVente " + _
                                    "WHERE VENTE_DETAILS.NumeroVente='" + _
                                    dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente").ToString + "'" + _
                                    "GROUP BY CodePCT, VENTE_DETAILS.PriseEnCharge,VENTE_DETAILS.AccordPrealable, " + _
                                    "VENTE_DETAILS.TarifDeReference,VENTE_DETAILS.DureeTraitement  "

            cmdLisetVente.Connection = ConnectionServeur
            daListeVente = New SqlDataAdapter(cmdLisetVente)
            daListeVente.Fill(dsReleve, "LISTE_ORDONNANCES")

            For J = 0 To dsReleve.Tables("LISTE_ORDONNANCES").Rows.Count - 1
                Dim MontantCnam As Decimal = 0.0
                Dim AccordPrealable As String = ""

                If dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("PriseEnCharge").ToString = "True" And dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("AccordPrealable").ToString = "False" Then
                    MontantCnam = Math.Round((dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("TotalTTC") / 100 * 70), 3)
                End If

                If dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("AccordPrealable").ToString = "True" Then
                    AccordPrealable = "O"
                Else
                    AccordPrealable = "N"
                End If

                If Len(dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("CodePCT").ToString) > tailleCodePCT Then
                    MsgBox("La taille du Code PCT " + dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("CodePCT").ToString + " est supérieur à " & tailleCodePCT, MsgBoxStyle.Information, "Information")
                    Exit Sub
                End If

                If Len(dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("DureeTraitement").ToString) > 3 Then
                    MsgBox("La taille du Durée du Traitement ( " + dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("DureeTraitement").ToString + ") est supérieur à 3, VenteNuméro : " + dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente").ToString)
                    Exit Sub
                End If

                StrDetailVente = "3" + Chaine.PadLeft(5 - Len((I + 1).ToString), "0") + _
                                (I + 1).ToString + _
                                 Chaine.PadLeft(8 - Len(dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("CodePCT").ToString), "0") + _
                                 dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("CodePCT") + _
                                 Chaine.PadLeft(4 - Len(dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("Qte").ToString), "0") + _
                                 dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("Qte").ToString + _
                                 Chaine.PadLeft(10 - Len((dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("TotalTTC") * 1000).ToString.Substring(0, Len((dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("TotalTTC") * 1000).ToString) - 4)), "0") + _
                                 (dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("TotalTTC") * 1000).ToString.Substring(0, Len((dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("TotalTTC") * 1000).ToString) - 4) + _
                                 Chaine.PadLeft(10 - Len(((dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("TotalTTC") - MontantCnam) * 1000).ToString.Substring(0, Len(((dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("TotalTTC") - MontantCnam) * 1000).ToString) - 4)), "0") + _
                                 ((dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("TotalTTC") - MontantCnam) * 1000).ToString.Substring(0, Len(((dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("TotalTTC") - MontantCnam) * 1000).ToString) - 4) + _
                                 Chaine.PadLeft(3 - Len(dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("DureeTraitement").ToString), "0") + _
                                 dsReleve.Tables("LISTE_ORDONNANCES").Rows(J).Item("DureeTraitement").ToString + _
                                 AccordPrealable + _
                                 "000000000000"




                ' des zéros car on est dans une APCI ou Maladie ordinaire
                NouveauEnregistrement = dsReleve.Tables("ResultatTable").NewRow()
                NouveauEnregistrement("Resultat") = StrDetailVente
                dsReleve.Tables("ResultatTable").Rows.Add(NouveauEnregistrement)

            Next

        Next

        Dim theFolderBrowser As New FolderBrowserDialog
        Dim Shemin As String = ""

        Shemin = GetSetting("PHARMA", "PHARMA", "SheminSauveFichierCANM", "")

        theFolderBrowser.Description = "Please select a folder for the download."
        theFolderBrowser.ShowNewFolderButton = False
        theFolderBrowser.RootFolder = System.Environment.SpecialFolder.Desktop

        If Shemin <> "" Then
            theFolderBrowser.SelectedPath = Shemin
        Else
            theFolderBrowser.SelectedPath = "C:\"
        End If

        If theFolderBrowser.ShowDialog = Windows.Forms.DialogResult.OK Then
            If theFolderBrowser.SelectedPath.Substring(theFolderBrowser.SelectedPath.Length - 1, 1) <> "\" Then
                Shemin = theFolderBrowser.SelectedPath + "\"
            Else
                Shemin = theFolderBrowser.SelectedPath
            End If
        End If

        SaveSetting("PHARMA", "PHARMA", "SheminSauveFichierCANM", Shemin)
        Dim FichierCNAM_APCI As New IO.StreamWriter(Shemin & "RLV" & Trim(Replace(dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString, "/", "-")) & ".txt")

        For I = 0 To dsReleve.Tables("ResultatTable").Rows.Count - 1
            FichierCNAM_APCI.WriteLine(dsReleve.Tables("ResultatTable").Rows(I).Item("Resultat"))
        Next

        FichierCNAM_APCI.Close()

        MsgBox("Fichier : " + Shemin + "RLV" + Trim(Replace(dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString, "/", "-")) + ".txt  -  Généré avec succès", MsgBoxStyle.Information, "Information")
        InsertionDansLog("GENERATION_FICHIER_APCI_MO", "Génération du fichier APCI MO" + NuemroReleve, "-", System.DateTime.Now, "CNAM", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)

    End Sub

    Private Sub bAppareillage_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAppareillage.Click

        If dsReleve.Tables("RELEVE").Rows(0).Item("Appareillage") = False Then
            MsgBox("Ce n'est pas un relevé APPAREILLAGE !", MsgBoxStyle.Information, "Information")
            Exit Sub
        End If

        Dim StrEnteteReleve As String = ""
        Dim StrEnteteVente As String = ""
        Dim NouveauEnregistrement As DataRow = Nothing

        Dim StrSQL As String = ""
        Dim Cmd As New SqlCommand
        Dim Valeur As String = ""

        Dim Chaine As String = ""

        Dim I As Integer = 0
        Dim J As Integer = 0
        Dim K As Integer = 0
        Dim L As Integer = 0
        Dim CNSS As String = ""
        Dim CNRPS As String = ""
        Dim CNAM As String = ""
        Dim Caisse As String = ""
        Dim LienDeParente As String = ""
        Dim NomClient As String = ""
        Dim IsPriseEncharge As String = ""
        Dim DateNaissanceMalade As String = ""
        Dim CodeMedecin As String = ""
        Dim APCI As String = ""

        Dim NumeroCnamPharmacien As String = ""

        Dim cmdLisetVente As New SqlCommand
        Dim daListeVente As New SqlDataAdapter

        '----------------------
        If (dsReleve.Tables.IndexOf("ResultatTable") > -1) Then
            dsReleve.Tables("ResultatTable").Clear()
        Else
            Dim ResultatTable As DataTable = dsReleve.Tables.Add("ResultatTable")
            Dim MyColumn As DataColumn = New DataColumn()
            MyColumn.DataType = System.Type.GetType("System.String")
            MyColumn.AllowDBNull = False
            MyColumn.Caption = "Resultat"
            MyColumn.ColumnName = "Resultat"
            ResultatTable.Columns.Add(MyColumn)
        End If

        NumeroCnamPharmacien = Trim(RecupererValeurExecuteScalaire("NCnam", "PARAMETRE_PHARMACIE", "Code", 1))
        If NumeroCnamPharmacien = "" Then
            MsgBox("Code CNAM Pharmacien manquant " + CodePharmacien, MsgBoxStyle.Information, "Information")
        End If
        NumeroCnamPharmacien = FormatterCodePharmacien(NumeroCnamPharmacien, "Pharmacien")
        If NumeroCnamPharmacien = "False" Then
            Exit Sub
        End If

        'NumeroCnamPharmacien = Chaine.PadLeft(12 - Len(Trim(NumeroCnamPharmacien)), "0") + Trim(NumeroCnamPharmacien)

        StrEnteteReleve = "1" + dsReleve.Tables("RELEVE").Rows(0).Item("Date").ToString.Substring(6, 4) + _
                          Chaine.PadLeft(3 - Len(dsReleve.Tables("RELEVE").Rows(0).Item("NumeroAppareillage").ToString), "0") + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("NumeroAppareillage").ToString + _
                          Chaine.PadLeft(5 - Len(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows.Count.ToString), "0") + _
                          dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows.Count.ToString + _
                          Chaine.PadLeft(4, "0") + _
                          Space(15) + _
                          Chaine.PadLeft(2, "0") + _
                          Chaine.PadLeft(4, "0") + _
                          Chaine.PadLeft(6, "0") + _
                          Chaine.PadLeft(10 - Len(RecupererValeurExecuteScalaire("Affiliation1", "PARAMETRE_PHARMACIE", "Code", 1)), "0") + _
                          RecupererValeurExecuteScalaire("Affiliation1", "PARAMETRE_PHARMACIE", "Code", 1) + _
                          Chaine.PadLeft(2 - Len(RecupererValeurExecuteScalaire("Affiliation2", "PARAMETRE_PHARMACIE", "Code", 1)), "0") + _
                          RecupererValeurExecuteScalaire("Affiliation2", "PARAMETRE_PHARMACIE", "Code", 1) + _
                          Chaine.PadLeft(10 - Len((dsReleve.Tables("RELEVE").Rows(0).Item("Montant") * 1000).ToString.Substring(0, Len((dsReleve.Tables("RELEVE").Rows(0).Item("Montant") * 1000).ToString) - 4)), "0") + _
                          (dsReleve.Tables("RELEVE").Rows(0).Item("Montant") * 1000).ToString.Substring(0, Len((dsReleve.Tables("RELEVE").Rows(0).Item("Montant") * 1000).ToString) - 4) + _
                          Chaine.PadLeft(8, "0") + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("DateDebut").ToString.Substring(6, 4) + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("DateDebut").ToString.Substring(3, 2) + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("DateDebut").ToString.Substring(0, 2) + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("DateFin").ToString.Substring(6, 4) + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("DateFin").ToString.Substring(3, 2) + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("DateFin").ToString.Substring(0, 2) + _
                          Chaine.PadLeft(8, "0") + _
                          Chaine.PadLeft(10, "0") + _
                          Chaine.PadLeft(10, "0") + _
                          Chaine.PadLeft(3, "0") + _
                          Chaine.PadLeft(4, "0")

        NouveauEnregistrement = dsReleve.Tables("ResultatTable").NewRow()
        NouveauEnregistrement("Resultat") = StrEnteteReleve
        dsReleve.Tables("ResultatTable").Rows.Add(NouveauEnregistrement)

        For I = 0 To dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows.Count - 1
            Dim NumeroFacture As String = ""
            Dim ReferenceDecision As String = ""
            Dim CodeBureau As String = ""
            Dim AnneePriseEnCharge As String = ""
            Dim NumeroOrdre As String = ""
            Dim NumeroAssureSocial As String = ""
            Dim Racine As String = ""
            Dim Cle As String = ""
            Dim DateOrdonnance As String = ""
            Dim PrixHorsTaxe As String = ""

            NumeroFacture = RecupererValeurExecuteScalaire("NumeroFacture", "VENTE", "NumeroVente", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente").ToString)
            ReferenceDecision = RecupererValeurExecuteScalaire("IdentifiantCNAMMedecin", "VENTE", "NumeroVente", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente").ToString)
            CodeBureau = ReferenceDecision.Substring(0, 2)
            AnneePriseEnCharge = ReferenceDecision.Substring(3, 4)
            NumeroOrdre = ReferenceDecision.Substring(8, 6)
            NumeroAssureSocial = dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantClient")

            '******************************* suppression des caractères non numériques 
            L = Len(NumeroAssureSocial)
            K = 0
            While K < L
                If InStr("0123456789", NumeroAssureSocial.Substring(K, 1)) = 0 Then
                    If K > 1 Then
                        NumeroAssureSocial = NumeroAssureSocial.Substring(0, K) + NumeroAssureSocial.Substring(K + 1, L - K - 1)
                    Else
                        NumeroAssureSocial = NumeroAssureSocial.Substring(2, L - 1)
                    End If
                End If
                K += 1
                L = NumeroAssureSocial.Length
            End While
            '*************************************************************************

            Racine = Chaine.PadLeft((tailleCodeCNAM - 2) - Len(NumeroAssureSocial.Substring(0, Len(NumeroAssureSocial) - 2)), "0") + NumeroAssureSocial.Substring(0, Len(NumeroAssureSocial) - 2)
            Cle = NumeroAssureSocial.Substring(Len(NumeroAssureSocial) - 2, 2)
            DateOrdonnance = RecupererValeurExecuteScalaire("DateOrdonnance", "VENTE", "NumeroVente", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente").ToString)

            PrixHorsTaxe = (RecupererValeurExecuteScalaire("TotalHT", "VENTE", "NumeroVente", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente").ToString) * 1000).ToString
            PrixHorsTaxe = PrixHorsTaxe.PadLeft(10, "0") 'PrixHorsTaxe.Substring(0, Len(PrixHorsTaxe) - 4)

            Dim Timbre As String
            Timbre = RecupererValeurExecuteScalaire("replace(replace(convert(varchar,Timbre),'.',''),',','')", "PARAMETRE_PHARMACIE", "Code", 1)
            If Len(Timbre) > 4 Then
                MsgBox("Vérifier le Timbre dans le menu Paramètres généreaux")
                Exit Sub
            End If

            StrEnteteVente = "2" + dsReleve.Tables("RELEVE").Rows(0).Item("Date").ToString.Substring(6, 4) + _
                             Chaine.PadLeft(3 - Len(dsReleve.Tables("RELEVE").Rows(0).Item("NumeroAppareillage").ToString), "0") + _
                             dsReleve.Tables("RELEVE").Rows(0).Item("NumeroAppareillage").ToString + _
                             Chaine.PadLeft(5, "0") + _
                             dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Date").ToString.Substring(6, 4) + _
                             NumeroFacture + _
                             Space(15 - Len(NumeroFacture)) + _
                             CodeBureau + _
                             AnneePriseEnCharge + _
                             NumeroOrdre + _
                             Racine + _
                             Cle + _
                             Chaine.PadLeft(10 - Len((dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("MontantCnam") * 1000).ToString.Substring(0, Len((dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(0).Item("MontantCnam") * 1000).ToString) - 4)), "0") + _
                             (dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("MontantCnam") * 1000).ToString.Substring(0, Len((dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(0).Item("MontantCnam") * 1000).ToString) - 4) + _
                             dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(0).Item("Date").ToString.Substring(6, 4) + _
                             dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(0).Item("Date").ToString.Substring(3, 2) + _
                             dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(0).Item("Date").ToString.Substring(0, 2) + _
                             Chaine.PadLeft(8, "0") + _
                             Chaine.PadLeft(8, "0") + _
                             DateOrdonnance.Substring(6, 4) + _
                             DateOrdonnance.ToString.Substring(3, 2) + _
                             DateOrdonnance.ToString.Substring(0, 2) + _
                             Chaine.PadLeft(10 - Len(PrixHorsTaxe.ToString), "0") + _
                             PrixHorsTaxe + _
                             "0000000000" + _
                             "000" + _
                             Timbre '"0300"

            NouveauEnregistrement = dsReleve.Tables("ResultatTable").NewRow()
            NouveauEnregistrement("Resultat") = StrEnteteVente
            dsReleve.Tables("ResultatTable").Rows.Add(NouveauEnregistrement)

        Next

        Dim theFolderBrowser As New FolderBrowserDialog
        Dim Shemin As String = ""

        Shemin = GetSetting("PHARMA", "PHARMA", "SheminSauveFichierCANM", "")
        theFolderBrowser.Description = "Please select a folder for the download."
        theFolderBrowser.ShowNewFolderButton = False
        theFolderBrowser.RootFolder = System.Environment.SpecialFolder.Desktop

        If Shemin <> "" Then
            theFolderBrowser.SelectedPath = Shemin
        Else
            theFolderBrowser.SelectedPath = "C:\"
        End If

        If theFolderBrowser.ShowDialog = Windows.Forms.DialogResult.OK Then
            If theFolderBrowser.SelectedPath.Substring(theFolderBrowser.SelectedPath.Length - 1, 1) <> "\" Then
                Shemin = theFolderBrowser.SelectedPath + "\"
            Else
                Shemin = theFolderBrowser.SelectedPath
            End If
        End If

        SaveSetting("PHARMA", "PHARMA", "SheminSauveFichierCANM", Shemin)

        Dim FichierCNAM_APCI As New IO.StreamWriter(Shemin + "RLVAPP" + Trim(Replace(dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString, "/", "-")) + ".txt")

        For I = 0 To dsReleve.Tables("ResultatTable").Rows.Count - 1
            FichierCNAM_APCI.WriteLine(dsReleve.Tables("ResultatTable").Rows(I).Item("Resultat"))
        Next

        FichierCNAM_APCI.Close()

        MsgBox("Fichier : " + Shemin + "RLVAPP" + Trim(Replace(dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString, "/", "-")) + ".txt  -  Généré avec succès", MsgBoxStyle.Information, "Information")
        InsertionDansLog("GENERATION_FICHIER_APPAREILLAGE", "Génération du fichier appareillage" + NuemroReleve, "-", System.DateTime.Now, "CNAM", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)

    End Sub

    Private Sub bPriseEnCharge_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPriseEnCharge.Click

        If dsReleve.Tables("RELEVE").Rows(0).Item("PriseEnCharge") = False Then
            MsgBox("Ce n'est pas un relevé PRISE EN CHARGE !", MsgBoxStyle.Information, "Information")
            Exit Sub
        End If

        Dim StrEnteteReleve As String = ""
        Dim StrEnteteVente As String = ""
        Dim NouveauEnregistrement As DataRow = Nothing

        Dim StrSQL As String = ""
        Dim Cmd As New SqlCommand
        Dim Valeur As String = ""

        Dim Chaine As String = ""

        Dim I As Integer = 0
        Dim J As Integer = 0
        Dim K As Integer = 0
        Dim L As Integer = 0
        Dim CNSS As String = ""
        Dim CNRPS As String = ""
        Dim CNAM As String = ""
        Dim Caisse As String = ""
        Dim LienDeParente As String = ""
        Dim NomClient As String = ""
        Dim IsPriseEncharge As String = ""
        Dim DateNaissanceMalade As String = ""
        Dim CodeMedecin As String = ""
        Dim APCI As String = ""

        Dim NumeroCnamPharmacien As String = ""

        Dim cmdLisetVente As New SqlCommand
        Dim daListeVente As New SqlDataAdapter

        If (dsReleve.Tables.IndexOf("ResultatTable") > -1) Then
            dsReleve.Tables("ResultatTable").Clear()
        Else
            Dim ResultatTable As DataTable = dsReleve.Tables.Add("ResultatTable")
            Dim MyColumn As DataColumn = New DataColumn()
            MyColumn.DataType = System.Type.GetType("System.String")
            MyColumn.AllowDBNull = False
            MyColumn.Caption = "Resultat"
            MyColumn.ColumnName = "Resultat"
            ResultatTable.Columns.Add(MyColumn)
        End If

        NumeroCnamPharmacien = Trim(RecupererValeurExecuteScalaire("NCnam", "PARAMETRE_PHARMACIE", "Code", 1))
        If NumeroCnamPharmacien = "" Then
            MsgBox("Code CNAM Pharmacien manquant :" + CodePharmacien, MsgBoxStyle.Information, "Information")
        End If
        NumeroCnamPharmacien = FormatterCodePharmacien(NumeroCnamPharmacien, "Pharmacien")
        If NumeroCnamPharmacien = "False" Then
            Exit Sub
        End If

        'NumeroCnamPharmacien = Chaine.PadLeft(12 - Len(Trim(NumeroCnamPharmacien)), "0") + Trim(NumeroCnamPharmacien)
        Try
            dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve") = dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString.Substring(0, 4) + dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString.Substring(5, 6)
        Catch ex As Exception

        End Try


        StrEnteteReleve = "1" + dsReleve.Tables("RELEVE").Rows(0).Item("Date").ToString.Substring(6, 4) + _
                          Chaine.PadLeft(15 - Len(dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString), "0") + _
                          dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString + _
                          Chaine.PadLeft(5 - Len(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows.Count.ToString), "0") + _
                          dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows.Count.ToString + _
                          NumeroCnamPharmacien + _
                          Chaine.PadLeft(4, "0") + _
                          Space(15) + _
                          Chaine.PadLeft(2, "0") + _
                          Chaine.PadLeft(4, "0") + _
                          Chaine.PadLeft(6, "0") + _
                          Chaine.PadLeft(2, "0") + _
                          Chaine.PadLeft(10 - Len(RecupererValeurExecuteScalaire("Affiliation1", "PARAMETRE_PHARMACIE", "Code", 1)), "0") + _
                          RecupererValeurExecuteScalaire("Affiliation1", "PARAMETRE_PHARMACIE", "Code", 1) + _
                        Chaine.PadLeft(2 - Len(RecupererValeurExecuteScalaire("Affiliation2", "PARAMETRE_PHARMACIE", "Code", 1)), "0") + _
                        RecupererValeurExecuteScalaire("Affiliation2", "PARAMETRE_PHARMACIE", "Code", 1) + _
                        Chaine.PadLeft(8, "0") + _
                        Chaine.PadLeft(8, "0") + _
                        Chaine.PadLeft(6, "0") + _
                        Chaine.PadLeft(3, "0") + _
                        Chaine.PadLeft(3, "0") + _
                        Chaine.PadLeft(10 - Len((dsReleve.Tables("RELEVE").Rows(0).Item("Montant") * 1000).ToString.Substring(0, Len((dsReleve.Tables("RELEVE").Rows(0).Item("Montant") * 1000).ToString) - 4)), "0") + _
                        (dsReleve.Tables("RELEVE").Rows(0).Item("Montant") * 1000).ToString.Substring(0, Len((dsReleve.Tables("RELEVE").Rows(0).Item("Montant") * 1000).ToString) - 4)

        NouveauEnregistrement = dsReleve.Tables("ResultatTable").NewRow()
        NouveauEnregistrement("Resultat") = StrEnteteReleve
        dsReleve.Tables("ResultatTable").Rows.Add(NouveauEnregistrement)

        For I = 0 To dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows.Count - 1
            Dim NumeroFacture As String = ""
            Dim ReferenceDecision As String = ""
            Dim CodeBureau As String = ""
            Dim AnneePriseEnCharge As String = ""
            Dim NumeroOrdre As String = ""
            Dim NumeroTranche As String = ""
            Dim NumeroAssureSocial As String = ""
            Dim Racine As String = ""
            Dim Cle As String = ""
            Dim DateOrdonnance As String = ""
            Dim PrixHorsTaxe As String = ""


            NumeroAssureSocial = dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantClient")
            NumeroFacture = RecupererValeurExecuteScalaire("NumeroFacture", "VENTE", "NumeroVente", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente").ToString)
            Try
                ReferenceDecision = dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantMedecin")
                'ReferenceDecision = RecupererValeurExecuteScalaire("IdentifiantCNAMMedecin", "VENTE", "NumeroVente", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente").ToString)
                CodeBureau = ReferenceDecision.Substring(0, 2)
                AnneePriseEnCharge = ReferenceDecision.Substring(3, 4)
                NumeroOrdre = ReferenceDecision.Substring(8, 6)
                NumeroTranche = ReferenceDecision.Substring(14, 2)

            Catch ex As Exception
                MsgBox("Id Cnam Medecin du Client " + dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NomMalade").ToString + " est éronné " + ReferenceDecision, MsgBoxStyle.Information, "Information")
                Exit Sub

                'If CodeBureau = "" Then CodeBureau = "" '"00"
                'If AnneePriseEnCharge = "" Then AnneePriseEnCharge = "" '"0000"
                'If NumeroOrdre = "" Then NumeroOrdre = "" '"000000"
                'If NumeroTranche = "" Then NumeroTranche = "" '"00"
            End Try





            ''''''
            NumeroAssureSocial = ""
            For L = 0 To Len(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantClient").ToString) - 1
                If IsNumeric(dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantClient").ToString.Substring(L, 1)) Then
                    NumeroAssureSocial += dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("IdentifiantClient").ToString.Substring(L, 1)
                End If
            Next
            '''''


            '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            '******************************* suppression des caractères non numériques 
            'L = Len(NumeroAssureSocial)
            'K = 0
            'While K < L
            '    If InStr("0123456789", NumeroAssureSocial.Substring(K, 1)) = 0 Then
            '        If K > 1 Then
            '            NumeroAssureSocial = NumeroAssureSocial.Substring(0, K) + NumeroAssureSocial.Substring(K + 1, L - K - 1)
            '        Else
            '            NumeroAssureSocial = NumeroAssureSocial.Substring(1, L - 1)
            '        End If
            '    End If
            '    K += 1
            '    L = NumeroAssureSocial.Length
            'End While
            '*************************************************************************
            '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''' 




            'Racine = Chaine.PadLeft(tailleCodeCNAM - Len(NumeroAssureSocial.Substring(0, Len(NumeroAssureSocial) - 2)), "0") + NumeroAssureSocial.Substring(0, Len(NumeroAssureSocial) - 2)


            '''''''''''''''
            'Racine = Replace(PADL(NumeroAssureSocial, tailleCodeCNAM), " ", "0")
            '''''''''''''''

            If Len(NumeroAssureSocial) > tailleCodeCNAM Then
                MsgBox("Code Cnam Client " + dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NomMalade").ToString + " est supérieur à " & tailleCodeCNAM & "  : (" + NumeroAssureSocial + ")", MsgBoxStyle.Information, "Information")
                Exit Sub
            Else
                Racine = Chaine.PadLeft(tailleCodeCNAM - Len(NumeroAssureSocial), "0") + NumeroAssureSocial
            End If


            'Cle = NumeroAssureSocial.Substring(Len(NumeroAssureSocial) - 2, 2)
            DateOrdonnance = RecupererValeurExecuteScalaire("DateOrdonnance", "VENTE", "NumeroVente", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente").ToString)

            PrixHorsTaxe = (RecupererValeurExecuteScalaire("TotalHT", "VENTE", "NumeroVente", dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente").ToString) * 1000).ToString
            PrixHorsTaxe = PrixHorsTaxe.Substring(0, Len(PrixHorsTaxe) - 4)

            If (dsReleve.Tables.IndexOf("LISTE_ARTICLES") > -1) Then
                dsReleve.Tables("LISTE_ARTICLES").Clear()
            End If

            cmdLisetVente.CommandText = "SELECT CodePCT," + _
                                        "SUM(Qte) AS Qte," + _
                                        "SUM(VENTE_DETAILS.TotalTTC) AS TotalTTC," + _
                                        "VENTE_DETAILS.PriseEnCharge," + _
                                        "VENTE_DETAILS.AccordPrealable," + _
                                        "VENTE_DETAILS.TarifDeReference," + _
                                        "VENTE_DETAILS.DureeTraitement " + _
                                        "FROM VENTE_DETAILS LEFT OUTER JOIN ARTICLE ON " + _
                                        "VENTE_DETAILS.CodeArticle=ARTICLE.CodeArticle " + _
                                        "LEFT OUTER JOIN VENTE ON VENTE_DETAILS.NumeroVente=VENTE.NumeroVente " + _
                                        "WHERE VENTE_DETAILS.NumeroVente='" + _
                                         dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("NumeroVente").ToString + "'" + _
                                        "GROUP BY CodePCT, VENTE_DETAILS.PriseEnCharge,VENTE_DETAILS.AccordPrealable, " + _
                                        "VENTE_DETAILS.TarifDeReference, VENTE_DETAILS.DureeTraitement  "

            cmdLisetVente.Connection = ConnectionServeur
            daListeVente = New SqlDataAdapter(cmdLisetVente)
            daListeVente.Fill(dsReleve, "LISTE_ARTICLES")

            For J = 0 To dsReleve.Tables("LISTE_ARTICLES").Rows.Count - 1

                If IsDBNull(dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("TarifDeReference")) = True Then
                    dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("TarifDeReference") = 0
                End If

                Dim MontantParArticle As Double = 0.0
                If dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("TarifDeReference") <> 0 Then
                    MontantParArticle = dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("TarifDeReference")
                Else
                    MontantParArticle = Math.Round(dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("TotalTTC") / 100 * 70, 3)
                End If

                'Dim wassim As String = ""
                'wassim = Chaine.PadLeft(10 - Len((dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("TarifDeReference") * 1000).ToString.Substring(0, Len((dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("TarifDeReference") * 1000).ToString) - 4)), "0")

                If Len(dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("CodePCT").ToString) > tailleCodePCT Then
                    MsgBox("La taille du Code PCT " + dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("CodePCT").ToString + " est supérieur à " & tailleCodePCT, MsgBoxStyle.Information, "Information")
                    Exit Sub
                End If

                Try
                    StrEnteteVente = "2" + dsReleve.Tables("RELEVE").Rows(0).Item("Date").ToString.Substring(6, 4) + _
                    Chaine.PadLeft(15 - Len(dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString), "0") + _
                    dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString + _
                    Chaine.PadLeft(5, "0") + _
                    NumeroCnamPharmacien + _
                    dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Date").ToString.Substring(6, 4) + _
                    Space(15 - Len(NumeroFacture)) + _
                    NumeroFacture + _
                    CodeBureau + _
                    AnneePriseEnCharge + _
                    NumeroOrdre + _
                    NumeroTranche + _
                    Racine + _
                    dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Date").ToString.Substring(6, 4) + _
                    dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Date").ToString.Substring(3, 2) + _
                    dsReleve.Tables("RELEVE_CNAM_DETAILS").Rows(I).Item("Date").ToString.Substring(0, 2) + _
                    DateOrdonnance.Substring(6, 4) + _
                    DateOrdonnance.ToString.Substring(3, 2) + _
                    DateOrdonnance.ToString.Substring(0, 2) + _
                    Chaine.PadLeft(tailleCodePCT - Len(dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("CodePCT").ToString), "0") + _
                    dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("CodePCT").ToString + _
                    Chaine.PadLeft(3 - Len(dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("Qte").ToString), "0") + _
                    dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("Qte").ToString + _
                    Chaine.PadLeft(3 - Len(dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("DureeTraitement").ToString), "0") + _
                    dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("DureeTraitement").ToString + _
                    Chaine.PadLeft(10 - Len((MontantParArticle * dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("Qte") * 1000).ToString.Substring(0, Len((MontantParArticle * dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("Qte") * 1000).ToString))), "0") + _
                    (MontantParArticle * dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("Qte") * 1000).ToString.Substring(0, Len((MontantParArticle * dsReleve.Tables("LISTE_ARTICLES").Rows(J).Item("Qte") * 1000).ToString))

                Catch ex As Exception
                    MsgBox(ex.Message)
                End Try

                NouveauEnregistrement = dsReleve.Tables("ResultatTable").NewRow()
                NouveauEnregistrement("Resultat") = StrEnteteVente
                dsReleve.Tables("ResultatTable").Rows.Add(NouveauEnregistrement)

            Next
        Next

        Dim theFolderBrowser As New FolderBrowserDialog
        Dim Shemin As String = ""

        Shemin = GetSetting("PHARMA", "PHARMA", "SheminSauveFichierCANM", "")

        theFolderBrowser.Description = "Please select a folder for the download."
        theFolderBrowser.ShowNewFolderButton = False
        theFolderBrowser.RootFolder = System.Environment.SpecialFolder.Desktop

        If Shemin <> "" Then
            theFolderBrowser.SelectedPath = Shemin
        Else
            theFolderBrowser.SelectedPath = "C:\"
        End If

        If theFolderBrowser.ShowDialog = Windows.Forms.DialogResult.OK Then
            If theFolderBrowser.SelectedPath.Substring(theFolderBrowser.SelectedPath.Length - 1, 1) <> "\" Then
                Shemin = theFolderBrowser.SelectedPath + "\"
            Else
                Shemin = theFolderBrowser.SelectedPath
            End If
        Else
            Exit Sub
        End If

        SaveSetting("PHARMA", "PHARMA", "SheminSauveFichierCANM", Shemin)

        Dim FichierCNAM_APCI As New IO.StreamWriter(Shemin + "RLVPC" + Trim(Replace(dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString, "/", "-")) + ".txt")

        For I = 0 To dsReleve.Tables("ResultatTable").Rows.Count - 1
            FichierCNAM_APCI.WriteLine(dsReleve.Tables("ResultatTable").Rows(I).Item("Resultat"))
        Next

        FichierCNAM_APCI.Close()

        MsgBox("Fichier : " + Shemin + "RLVPC" + Trim(Replace(dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve").ToString, "/", "-")) + ".txt  -  Généré avec succès", MsgBoxStyle.Information, "Information")
        InsertionDansLog("GENERATION_FICHIER_PRISE_EN_CHARGE", "Génération du fichier prise en charge" + NuemroReleve, "-", System.DateTime.Now, "CNAM", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)

    End Sub

    Public Function FormatterCodePharmacien(ByVal CodePharmacien As String, ByVal Type As String)
        Dim CodeCorrige As String = ""
        Dim CodeCorrigeIntermediaire As String = ""
        Dim i As Integer = 0
        Dim j As Integer = 0
        Dim Prefix As String = ""
        Dim Cle As String = ""
        Dim Cle1 As String = ""
        Dim Nom As String = ""

        If Type = "Medecin" Then
            Nom = RecupererValeurExecuteScalaire("NomMedecin", "MEDECIN", "IdentifiantCNAM", CodePharmacien)
        End If

        If CodePharmacien.Length > 1 Then
            If CodePharmacien.Substring(0, 1) <> "0" Then
                CodePharmacien = "0" & CodePharmacien
            End If
        End If


        If CodePharmacien.Length < 3 Then
            MsgBox("Code Cnam " + Type + " " + Nom + " éronné " + CodePharmacien, MsgBoxStyle.Information, "Information")
            Return ("False")
            Exit Function
        End If

        If Len(CodePharmacien) > 14 Then
            MsgBox("Code Cnam " + Type + " " + Nom + " éronné " + CodePharmacien, MsgBoxStyle.Information, "Information")
            Return ("False")
            Exit Function
        End If

        j = Len(CodePharmacien)
        i = 0
        Try
            While i < j
                If InStr("0123456789", CodePharmacien.Substring(i, 1)) = 0 Then
                    If i > 1 Then
                        CodePharmacien = CodePharmacien.Substring(0, i) + CodePharmacien.Substring(i + 1, j - i - 1)
                    Else
                        CodePharmacien = CodePharmacien.Substring(2, j - 1)
                    End If
                End If
                i += 1
                j = CodePharmacien.Length
            End While
        Catch ex As Exception
            MsgBox("Code Cnam " + Type + " " + Nom + " éronné " + CodePharmacien, MsgBoxStyle.Information, "Information")
            Return ("False")
            Exit Function
        End Try


        Prefix = CodePharmacien.Substring(0, 2)
        Cle = CodePharmacien.Substring(CodePharmacien.Length - 1, 1)
        CodeCorrige = CodePharmacien.Substring(2, CodePharmacien.Length - 3)

        If CodeCorrige.Length < 1 Then
            MsgBox("Code Cnam " + Type + Nom + " éronné " + CodePharmacien, MsgBoxStyle.Information, "Information")
            Return ("False")
            Exit Function
        End If

        If CDbl(CodeCorrige) Mod 97 = CDbl(Cle) Then
            Cle1 = CodeCorrige.Substring(CodeCorrige.Length - 1, 1) + Cle
            Cle = "0" & Cle
            CodeCorrigeIntermediaire = CodeCorrige.Substring(0, CodeCorrige.Length - 1)
            If CDbl(CodeCorrigeIntermediaire) Mod 97 = CDbl(Cle1) Then
                Cle = Cle1
                CodeCorrige = CodeCorrigeIntermediaire
            End If
        Else
            Cle = CodeCorrige.Substring(CodeCorrige.Length - 1, 1) + Cle
            CodeCorrige = CodeCorrige.Substring(0, CodeCorrige.Length - 1)
        End If

        If CodeCorrige.Length < 8 Then
            CodeCorrige = CodeCorrige.PadLeft(8, "0")
        End If

        CodeCorrige = Prefix + CodeCorrige + Cle

        Return (CodeCorrige)
    End Function

    Private Sub recupererInformationCNAM()

        Dim Cmd As New SqlCommand
        Dim StrSQL As String

        'le nbre de chiffre du code CNAM
        StrSQL = "SELECT tailleCodeCNAM FROM PARAMETRE_PHARMACIE "

        Cmd.Connection = ConnectionServeur
        Cmd.CommandText = StrSQL
        Try
            tailleCodeCNAM = Cmd.ExecuteScalar().ToString
        Catch ex As Exception
            MsgBox("Vous devez aller sur [Paramètres Généreaux/Taille Code CNAM] pour remplir les paramètres")
            Exit Sub
            tailleCodeCNAM = 12
            'Console.WriteLine(ex.Message)
        End Try
        '----------------------
    End Sub

    Private Sub gReleves_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gReleves.MouseClick
        Try
            CalculerMontants()
        Catch ex As Exception
        End Try
    End Sub

    Private Sub bSuprimerreleve_Click(sender As Object, e As EventArgs) Handles bSuprimerreleve.Click

        MontantTotalTTC += gReleves.Columns("TotalTTC").Value
        MontantR += gReleves.Columns("MontantCNAM").Value

        ListNumeroVents.Add(gReleves.Columns("NumeroVente").Value)
        gReleves.Delete()
        dsReleve.Tables("RELEVE_CNAM_DETAILS").AcceptChanges()
        CalculerMontants()
    End Sub

    Private Sub gReleves_FetchRowStyle(sender As Object, e As FetchRowStyleEventArgs) Handles gReleves.FetchRowStyle
        If gReleves.Columns("CodeMedecinFamille").CellText(e.Row) <> gReleves.Columns("CodeMedecinFamilleFicheClient").CellText(e.Row) Then
            e.CellStyle.BackColor = Color.FromArgb(255, 255, 192, 192)
        End If
    End Sub
End Class
