@echo off
echo ========================================
echo    TEST SIMPLE - PHARMA2000
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 Test de l'application simple...
echo.

if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe" (
    echo ✅ Executable trouve
    echo.
    
    echo 🚀 Lancement de l'application...
    echo.
    echo 💡 ATTENTION : Regardez votre ecran !
    echo    Une fenetre verte avec "PHARMA2000 FONCTIONNE !" devrait apparaitre
    echo.
    
    cd "PharmaModerne.UI\bin\Debug\net9.0-windows"
    start "" "PharmaModerne.UI.exe"
    cd ..\..\..
    
    echo ✅ Application lancee !
    echo.
    echo 📋 VERIFICATIONS :
    echo.
    echo 1. Voyez-vous une fenetre verte ?
    echo    ✅ OUI = L'application fonctionne !
    echo    ❌ NON = Probleme d'affichage
    echo.
    echo 2. Si aucune fenetre :
    echo    - Verifiez la barre des taches
    echo    - Regardez si l'icone clignote
    echo    - Appuyez sur Alt+Tab
    echo.
    echo 3. Si fenetre visible :
    echo    - Cliquez sur "FERMER"
    echo    - L'application fonctionne correctement !
    echo.
    
    timeout /t 10 /nobreak >nul
    
    echo 🔍 Verification des processus...
    tasklist | find "PharmaModerne" >nul
    if %errorlevel% equ 0 (
        echo ✅ Processus detecte - Application active
        tasklist | find "PharmaModerne"
    ) else (
        echo ❌ Aucun processus trouve - Application fermee ou crash
    )
    
) else (
    echo ❌ Executable non trouve !
    echo Recompilation necessaire...
    dotnet build PharmaModerne.UI\PharmaModerne.UI.csproj --configuration Debug
)

echo.
echo ========================================
echo    RESULTATS DU TEST
echo ========================================
echo.

echo 📊 SI VOUS AVEZ VU LA FENETRE VERTE :
echo    🎉 SUCCES ! L'application fonctionne
echo    ✅ WPF est operationnel
echo    ✅ .NET 9 fonctionne
echo    ✅ Interface s'affiche correctement
echo.

echo 📊 SI AUCUNE FENETRE :
echo    ❌ Probleme d'affichage
echo    🔧 Solutions possibles :
echo    1. Redemarrer en administrateur
echo    2. Verifier .NET Desktop Runtime
echo    3. Desactiver antivirus temporairement
echo    4. Verifier pilotes graphiques
echo.

echo 📊 SI CRASH IMMEDIAT :
echo    ❌ Probleme de dependances
echo    🔧 Solutions :
echo    1. Installer .NET 9.0 Desktop Runtime
echo    2. Installer Visual C++ Redistributable
echo    3. Mettre a jour Windows
echo.

pause
