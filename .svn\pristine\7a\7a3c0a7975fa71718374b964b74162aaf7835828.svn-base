//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class PARAMETRE_PHARMACIE
    {
        public string Code { get; set; }
        public string CodePharmacie { get; set; }
        public string Pharmacie { get; set; }
        public string NCnam { get; set; }
        public string Affiliation1 { get; set; }
        public string Affiliation2 { get; set; }
        public string Adresse { get; set; }
        public string Telephone { get; set; }
        public string Fax { get; set; }
        public string CodeTVA { get; set; }
        public string Rib { get; set; }
        public string Messagederoulant1 { get; set; }
        public string Messagederoulant2 { get; set; }
        public Nullable<decimal> Timbre { get; set; }
        public Nullable<bool> DemandeMotDePasse { get; set; }
        public Nullable<System.DateTime> DateMigration { get; set; }
        public string SmtpMail { get; set; }
        public string PortMail { get; set; }
        public string AdresseMailDestinateur { get; set; }
        public string SujetMail { get; set; }
        public string TexteMail { get; set; }
        public string MotDePasseDestinateur { get; set; }
        public bool AutoriserEnvoiMail { get; set; }
        public Nullable<int> NbreJourValiditeParDefaut { get; set; }
        public string NumeroLotProduction { get; set; }
        public string Latitude_Longitude { get; set; }
        public Nullable<int> TailleCodeCNAM { get; set; }
        public Nullable<int> TailleListe { get; set; }
        public Nullable<int> TailleCaractere { get; set; }
        public string PoliceCaractere { get; set; }
        public string Texte { get; set; }
        public short TauxRemise { get; set; }
        public string CodeGSU { get; set; }
        public byte[] ImageCodeABarre { get; set; }
        public bool ActiverOMFAPCI { get; set; }
        public bool ActiverBCB { get; set; }
        public Nullable<System.DateTime> ActiverBCBDateFin { get; set; }
        public string Version { get; set; }
        public Nullable<bool> GererBon { get; set; }
        public Nullable<int> NombreJoursValiditerOrdonnance { get; set; }
        public Nullable<int> NombreJoursValiditerPriseEnCharge { get; set; }
        public Nullable<int> NombreJoursValiditerAppareillage { get; set; }
        public int QuantiteMultipleDeCinq { get; set; }
        public Nullable<bool> ImpressionDirectApresVente { get; set; }
        public Nullable<bool> HistoriqueMouvementArticle { get; set; }
        public Nullable<int> NbrCommandePourClasserManquant { get; set; }
        public Nullable<int> TypeClassementManquant { get; set; }
        public Nullable<System.DateTime> DateDerniereMiseAJour { get; set; }
        public Nullable<int> NombreCopieImpBon { get; set; }
        public Nullable<bool> AutoriserModificationPrixDansAchat { get; set; }
        public Nullable<bool> MettreAJourPrixFrigo { get; set; }
        public Nullable<bool> AfficherReglementsSupprimes { get; set; }
        public Nullable<bool> AutoriserSaisieNonMembeFamille { get; set; }
        public Nullable<bool> ImprimerUnEtiquette { get; set; }
        public Nullable<bool> TenirCompteStockAlerte { get; set; }
        public string hostname { get; set; }
        public string username { get; set; }
        public string password { get; set; }
    }
}
