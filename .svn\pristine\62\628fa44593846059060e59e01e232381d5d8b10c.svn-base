﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports Microsoft.Reporting.WinForms
Imports System.Linq.Dynamic

Public Class fStatistiqueDesFournisseurs
    Dim _SalesReportService As New Bll.Reporting.SalesReport
    Dim _VOrderBy As String = "Id"
    Dim _VAscDesc As String = "Asc"

    Dim StrSQL As String = ""

    Dim cmdSTAT As New SqlCommand
    Dim daSTAT As New SqlDataAdapter
    Dim dsSTAT As New DataSet

    Dim TotalHT As Double = 0.0
    Dim TotalTVA As Double = 0.0
    Dim TotalTTC As Double = 0.0
    Dim TotalRemise As Double = 0.0
    Dim TotalVenteTTC As Double = 0.0
    Dim TotalResteAPayer As Double = 0.0

    Public Sub init()
        


        dpDateDebut.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dpDateDebut.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dpDateFin.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dpDateFin.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate


        If ModeADMIN = "ADMIN" Then
            gbTotal.Visible = True
        Else
            gbTotal.Visible = False
        End If

        rdbdateFacture.Checked = True
        'AfficherStatistique()

        dpDateDebut.Focus()



    End Sub

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If

    End Sub

    Public Sub AfficherStatistique()
        Dim I As Integer
        Dim Mois As String = ""
        Dim Annee As String = ""
        Dim Cond As String = "1=1"

        TotalHT = 0.0
        TotalTVA = 0.0
        TotalVenteTTC = 0.0
        TotalResteAPayer = 0.0

        Dim TotalTTC As Double = 0.0
        Dim PartCNAM As Double = 0.0
        Dim PartMutuelle As Double = 0.0
        Dim TotalRemise As Double = 0.0
        Dim TotalMarge As Double = 0.0

        Dim NombreVente As Integer = 0

        Dim Espece As Double = 0.0
        Dim Cheque As Double = 0.0
        Dim Carte As Double = 0.0
        Dim Credit As Double = 0.0

        If dpDateDebut.Text = "" Then
            dpDateDebut.Value = Date.Now
        End If
        If dpDateFin.Text = "" Then
            dpDateFin.Value = Date.Now
        End If

        Dim List As New Library.SortableBindingList(Of Data.Reporting.P_Report_EtatStatistiqueFournisseurs_Result)(_SalesReportService.GetEtatStatistiqueFournisseurs(
                                                                                                                                                                        dpDateDebut.Text,
                                                                                                                                                                        dpDateFin.Text,
                                                                                                                                                                        IIf(rdbDateDAchat.Checked, "Achat", "Facture")
                                                                                                                                                                        ))

        With gVentes
            .Columns.Clear()
            .DataSource = List
            .Rebind(False)
            .Columns("NomFournisseur").Caption = "Fournisseur"
            .Columns("HT").Caption = "HT"
            .Columns("HT").NumberFormat = "### ### ##0.000"
            .Columns("TVA").Caption = "TVA"
            .Columns("TVA").NumberFormat = "### ### ##0.000"
            .Columns("TTC").Caption = "TTC"
            .Columns("TTC").NumberFormat = "### ### ##0.000"
            .Columns("Remise").Caption = "Remise"
            .Columns("Remise").NumberFormat = "### ### ##0.000"
            .Columns("VenteTTC").Caption = "Prix Vente TTC"
            .Columns("VenteTTC").NumberFormat = "### ### ##0.000"
            .Columns("ResteAPayer").Caption = "Reste A Payer"
            .Columns("ResteAPayer").NumberFormat = "### ### ##0.000"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("HT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Remise").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("VenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("ResteAPayer").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("Id").Visible = False             '
            .Splits(0).DisplayColumns("NomFournisseur").Width = 320            '
            .Splits(0).DisplayColumns("HT").Width = 140
            .Splits(0).DisplayColumns("TVA").Width = 140
            .Splits(0).DisplayColumns("TTC").Width = 140
            .Splits(0).DisplayColumns("Remise").Width = 120
            .Splits(0).DisplayColumns("VenteTTC").Width = 200
            .Splits(0).DisplayColumns("ResteAPayer").Width = 111

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True

            'Style du Caractere et du grid
            ParametreGrid(gVentes)
        End With

        For I = 0 To gVentes.RowCount - 1
            If gVentes(I, "HT").ToString <> "" Then
                TotalHT = TotalHT + gVentes(I, "HT")
            End If
            If gVentes(I, "TVA").ToString <> "" Then
                TotalTVA = TotalTVA + gVentes(I, "TVA")
            End If
            If gVentes(I, "TTC").ToString <> "" Then
                TotalTTC = TotalTTC + gVentes(I, "TTC")
            End If
            If gVentes(I, "Remise").ToString <> "" Then
                TotalRemise = TotalRemise + gVentes(I, "Remise")
            End If
            If gVentes(I, "VenteTTC").ToString <> "" Then
                TotalVenteTTC = TotalVenteTTC + gVentes(I, "VenteTTC")
            End If
            If gVentes(I, "ResteAPayer").ToString <> "" Then
                TotalResteAPayer = TotalResteAPayer + gVentes(I, "ResteAPayer")
            End If

        Next

        lHT.Text = Math.Round(TotalHT, 3).ToString("### ### ##0.000")
        lTVA.Text = Math.Round(TotalTVA, 3).ToString("### ### ##0.000")
        lTTC.Text = Math.Round(TotalTTC, 3).ToString("### ### ##0.000")
        lRemise.Text = Math.Round(TotalRemise, 3).ToString("### ### ##0.000")
        lValeurVenteTTC.Text = Math.Round(TotalVenteTTC, 3).ToString("### ### ##0.000")
        lResteAPayer.Text = Math.Round(TotalResteAPayer, 3).ToString("### ### ##0.000")

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Reporting Ventes" Then
                num = I
            End If
        Next
        Dim dt

        Dim _Parameters As New List(Of ReportParameter)()

        Dim _DateDebut As New ReportParameter()
        _DateDebut.Name = "DateDebut"
        _DateDebut.Values.Add(dpDateDebut.Value)
        _Parameters.Add(_DateDebut)

        Dim _DateFin As New ReportParameter()
        _DateFin.Name = "DateFin"
        _DateFin.Values.Add(dpDateFin.Value)
        _Parameters.Add(_DateFin)

        Dim _TypeDate As New ReportParameter()
        _TypeDate.Name = "TypeDate"
        _TypeDate.Values.Add(IIf(rdbDateDAchat.Checked, "Achat", "Facture"))
        _Parameters.Add(_TypeDate)

        Dim MyViewer As New fImpressionReportingVente

        dt = _SalesReportService.GetEtatStatistiqueFournisseurs(
                                                                dpDateDebut.Value,
                                                                dpDateFin.Value,
                                                                IIf(rdbDateDAchat.Checked, "Achat", "Facture")
                                                                ).OrderBy(_VOrderBy + " " + _VAscDesc)

        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_EtatStatistiqueFournisseurs", dt))
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Pharmacie", _SalesReportService.GetPharmacie()))
        MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatStatistiquesFournisseurs.rdl"

        MyViewer.ReportViewer1.LocalReport.SetParameters(_Parameters)

        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel1)

        MyViewer.ReportViewer1.RefreshReport()

        fMain.Tab.SelectedTab.Text = "Impression Reporting Ventes"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    Private Sub dpDateDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dpDateDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherStatistique()
            dpDateFin.Focus()
        End If
    End Sub

    Private Sub dpDateDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dpDateDebut.TextChanged

    End Sub

    Private Sub dpDateDebut_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles dpDateDebut.Validated
        AfficherStatistique()
    End Sub

    Private Sub dpDateFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dpDateFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherStatistique()
        End If
    End Sub

    Private Sub dpDateFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dpDateFin.TextChanged

    End Sub

    Private Sub dpDateFin_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles dpDateFin.Validated
        AfficherStatistique()
    End Sub

    Private Sub Label1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Label1.Click

    End Sub

    Private Sub rdbDateDAchat_CheckedChanged(sender As Object, e As EventArgs) Handles rdbDateDAchat.CheckedChanged
        AfficherStatistique()
    End Sub

    Private Sub rdbdateFacture_CheckedChanged(sender As Object, e As EventArgs) Handles rdbdateFacture.CheckedChanged
        AfficherStatistique()
    End Sub

    Private Sub gVentes_AfterSort(sender As Object, e As FilterEventArgs) Handles gVentes.AfterSort
        Try
            _VOrderBy = e.Condition.Substring(0, e.Condition.IndexOf(" "))
            _VAscDesc = e.Condition.Substring(e.Condition.IndexOf(" ") + 1, e.Condition.Length - e.Condition.IndexOf(" ") - 1)
        Catch ex As Exception
            _VOrderBy = e.Condition
            _VAscDesc = "Asc"
        End Try
    End Sub
End Class