﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{082B5048-4D66-49C5-A8C7-2C9CC2B977E5}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>Pharma2000Premium.My.MyApplication</StartupObject>
    <RootNamespace>Pharma2000Premium</RootNamespace>
    <AssemblyName>Pharma2000Premium</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <OptionExplicit>On</OptionExplicit>
    <OptionCompare>Binary</OptionCompare>
    <OptionStrict>Off</OptionStrict>
    <OptionInfer>On</OptionInfer>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <ApplicationIcon>App.ico</ApplicationIcon>
    <TargetZone>LocalIntranet</TargetZone>
    <GenerateManifests>false</GenerateManifests>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UpgradeBackupLocation />
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>1</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>Pharma2000Premium.xml</DocumentationFile>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036,42353,42354,42355</NoWarn>
    <WarningsAsErrors>
    </WarningsAsErrors>
    <PlatformTarget>x86</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>Pharma2000Premium.xml</DocumentationFile>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036,42353,42354,42355</NoWarn>
    <WarningsAsErrors>
    </WarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>My Project\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BCBV3_TN">
      <HintPath>W:\Cegedim.Next Products\Pharma 2000\V 2.5.0.2\dll\BCBV3_TN.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.Win.2, Version=2.0.20203.457, Culture=neutral, PublicKeyToken=944ae1ea0e47ca04, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.Win.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.C1Excel.2, Version=2.0.20153.110, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.C1Excel.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.C1Flash.2, Version=2.0.20113.87, Culture=neutral, PublicKeyToken=45fc79e7d82d90d7, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.C1Flash.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.C1Pdf.2, Version=2.1.20132.246, Culture=neutral, PublicKeyToken=79882d576c6336da">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.C1Pdf.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.C1Report.2, Version=2.6.20132.54638, Culture=neutral, PublicKeyToken=594a0605db190bb9, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.C1Report.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.Win.C1BarCode.2, Version=2.0.20133.67, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.Win.C1BarCode.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.Win.C1Chart.2, Version=2.0.20133.23316, Culture=neutral, PublicKeyToken=a22e16972c085838, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.Win.C1Chart.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.Win.C1Command.2, Version=2.0.20113.19499, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.Win.C1Command.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.Win.C1FlexGrid.2, Version=2.6.20133.797, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.Win.C1FlexGrid.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.Win.C1Gauge.2, Version=2.0.20132.93, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.Win.C1Gauge.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.Win.C1Input.2, Version=2.0.20122.33281, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.Win.C1Input.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.Win.C1InputPanel.2, Version=2.0.20133.168, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.Win.C1InputPanel.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.Win.C1List.2, Version=2.1.20132.231, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.Win.C1List.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.Win.C1Ribbon.2, Version=2.0.20132.470, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.Win.C1Ribbon.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.Win.C1Schedule.2, Version=2.0.20132.243, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.Win.C1Schedule.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.Win.C1Sizer.2, Version=2.1.20132.77, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.Win.C1Sizer.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.Win.C1TrueDBGrid.2, Version=2.0.20113.61237, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.Win.C1TrueDBGrid.2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.Win.Olap, Version=2.0.20132.134, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\ComponentOne\Apps\v2.0\C1.Win.Olap.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="CrystalDecisions.CrystalReports.Engine, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\dll\CrystalReports\CrystalDecisions.CrystalReports.Engine.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="CrystalDecisions.ReportSource, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\dll\CrystalReports\CrystalDecisions.ReportSource.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="CrystalDecisions.Shared, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\dll\CrystalReports\CrystalDecisions.Shared.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="CrystalDecisions.Windows.Forms, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\dll\CrystalReports\CrystalDecisions.Windows.Forms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DirectX.Capture, Version=1.0.1313.24984, Culture=neutral">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>.\DirectX.Capture.dll</HintPath>
    </Reference>
    <Reference Include="DShowNET, Version=1.0.0.1, Culture=neutral">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>.\DShowNET.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\dll\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="GMap.NET.Core, Version=1.6.0.0, Culture=neutral, PublicKeyToken=b85b9027b614afef, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>W:\Cegedim.Next Products\Pharma 2000\V 2.5.0.2\dll\GMap.NET.Core.dll</HintPath>
    </Reference>
    <Reference Include="GMap.NET.WindowsForms, Version=1.6.0.0, Culture=neutral, PublicKeyToken=b85b9027b614afef, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>W:\Cegedim.Next Products\Pharma 2000\V 2.5.0.2\dll\GMap.NET.WindowsForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Excel, Version=14.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.Common, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
      <HintPath>bin\Debug\Microsoft.ReportViewer.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.ProcessingObjectModel, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
      <HintPath>bin\Debug\Microsoft.ReportViewer.ProcessingObjectModel.DLL</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WinForms, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
      <HintPath>bin\Debug\Microsoft.ReportViewer.WinForms.DLL</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Types, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Reporting.WinForms.v11.1.0.0.0\lib\Microsoft.SqlServer.Types.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic.PowerPacks.Vs, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="Neodynamic.WinControls.BarcodeProfessional">
      <HintPath>..\Nouveau dossier\Neodynamic.WinControls.BarcodeProfessional.8.0.2016.127\lib\Neodynamic.WinControls.BarcodeProfessional.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Entity" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Drawing.Design" />
    <Reference Include="System.Linq.Dynamic">
      <HintPath>..\packages\System.Linq.Dynamic.1.0.6\lib\net40\System.Linq.Dynamic.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Windows.Forms.DataVisualization, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="TraceApp, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>.\TraceApp.dll</HintPath>
    </Reference>
    <Reference Include="WebKitBrowser">
      <HintPath>C:\Users\<USER>\Desktop\WebKit.NET-0.5-bin-cairo\bin\WebKitBrowser.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ApplicationEvents.vb" />
    <Compile Include="clsCircle.vb" />
    <Compile Include="FAvoirAchat.Designer.vb">
      <DependentUpon>FAvoirAchat.vb</DependentUpon>
    </Compile>
    <Compile Include="FAvoirAchat.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="EtatArticlePerimeSaisie.vb">
      <DependentUpon>EtatArticlePerimeSaisie.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="fHistoriquesMouvements.Designer.vb">
      <DependentUpon>fHistoriquesMouvements.vb</DependentUpon>
    </Compile>
    <Compile Include="fHistoriquesMouvements.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeArticleSansInventaire.designer.vb">
      <DependentUpon>fListeArticleSansInventaire.vb</DependentUpon>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="fListeArticleSansInventaire.vb">
      <SubType>Form</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="fReglemntSupprime.designer.vb">
      <DependentUpon>fReglemntSupprime.vb</DependentUpon>
    </Compile>
    <Compile Include="fReglemntSupprime.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fSaisiePerimes.designer.vb">
      <DependentUpon>fSaisiePerimes.vb</DependentUpon>
    </Compile>
    <Compile Include="fSaisiePerimes.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fStockParCategorieIntervalleMarge.designer.vb">
      <DependentUpon>fStockParCategorieIntervalleMarge.vb</DependentUpon>
    </Compile>
    <Compile Include="fStockParCategorieIntervalleMarge.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fVenteNonEnregistree.Designer.vb">
      <DependentUpon>fVenteNonEnregistree.vb</DependentUpon>
    </Compile>
    <Compile Include="fVenteNonEnregistree.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAchatLectureTerminal.designer.vb">
      <DependentUpon>fAchatLectureTerminal.vb</DependentUpon>
    </Compile>
    <Compile Include="fAchatLectureTerminal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fImpressionReportingVente.Designer.vb">
      <DependentUpon>fImpressionReportingVente.vb</DependentUpon>
    </Compile>
    <Compile Include="fImpressionReportingVente.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListesPourSpot.Designer.vb">
      <DependentUpon>fListesPourSpot.vb</DependentUpon>
    </Compile>
    <Compile Include="fListesPourSpot.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fMessageBoxPourQuitter.Designer.vb">
      <DependentUpon>fMessageBoxPourQuitter.vb</DependentUpon>
    </Compile>
    <Compile Include="fMessageBoxPourQuitter.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fVenteAnnuler.Designer.vb">
      <DependentUpon>fVenteAnnuler.vb</DependentUpon>
    </Compile>
    <Compile Include="fVenteAnnuler.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatFacturationClient.Designer.vb">
      <DependentUpon>fEtatFacturationClient.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatFacturationClient.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fNomPourBon.Designer.vb">
      <DependentUpon>fNomPourBon.vb</DependentUpon>
    </Compile>
    <Compile Include="fNomPourBon.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="EtatJournalDesVenteHR.vb">
      <DependentUpon>EtatJournalDesVenteHR.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="EtatNMouvementArticleGlobal.vb">
      <DependentUpon>EtatNMouvementArticleGlobal.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="EtatNMouvementArticle.vb">
      <DependentUpon>EtatNMouvementArticle.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="fCopieBD.Designer.vb">
      <DependentUpon>fCopieBD.vb</DependentUpon>
    </Compile>
    <Compile Include="fCopieBD.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fDureeTraitement.Designer.vb">
      <DependentUpon>fDureeTraitement.vb</DependentUpon>
    </Compile>
    <Compile Include="fDureeTraitement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fInformationClient.Designer.vb">
      <DependentUpon>fInformationClient.vb</DependentUpon>
    </Compile>
    <Compile Include="fInformationClient.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeReglement.Designer.vb">
      <DependentUpon>fListeReglement.vb</DependentUpon>
    </Compile>
    <Compile Include="fListeReglement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fNMouvementArticle.designer.vb">
      <DependentUpon>fNMouvementArticle.vb</DependentUpon>
    </Compile>
    <Compile Include="fNMouvementArticle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CustomMarkers.vb" />
    <Compile Include="DataSet_EtatInventaireTemporaire.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSet_EtatInventaireTemporaire.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSet_EtatInventaireTemporaire.vb">
      <DependentUpon>DataSet_EtatInventaireTemporaire.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSet_ETAT_FICHE_DE_CONTACT.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSet_ETAT_FICHE_DE_CONTACT.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSet_ETAT_FICHE_DE_CONTACT.vb">
      <DependentUpon>DataSet_ETAT_FICHE_DE_CONTACT.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSet_ETAT_HITPARAD_ARTICLE.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSet_ETAT_HITPARAD_ARTICLE.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSet_ETAT_HITPARAD_ARTICLE.vb">
      <DependentUpon>DataSet_ETAT_HITPARAD_ARTICLE.xsd</DependentUpon>
    </Compile>
    <Compile Include="EcranDeDemarrage.Designer.vb">
      <DependentUpon>EcranDeDemarrage.vb</DependentUpon>
    </Compile>
    <Compile Include="EcranDeDemarrage.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="EtatCarnetCNAM.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatCarnetCNAM.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDesArticlesDateMin.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDesArticlesDateMin.rpt</DependentUpon>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="EtatDetailsDesVentesSimple.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDetailsDesVentesSimple.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDifferenceCaisseCalculeReel.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDifferenceCaisseCalculeReel.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatHistoriqueDesAchats.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatHistoriqueDesAchats.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatHistoriqueDesActions.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatHistoriqueDesActions.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatHitParadeArticleNew.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatHitParadeArticleNew.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatImpressionCodeABarre.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatImpressionCodeABarre.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatImpressionCodeABarre2.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatImpressionCodeABarre2.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatImpressionCodeABarreA4.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatImpressionCodeABarreA4.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatInventaireTemporaire.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatInventaireTemporaire.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatListeInventaire.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatListeInventaire.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatOrdonnanceTemporaire.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatOrdonnanceTemporaire.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatPhotoClient.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatPhotoClient.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatPhotoOrdonnance.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatPhotoOrdonnance.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="fAfficheurEtat.Designer.vb">
      <DependentUpon>fAfficheurEtat.vb</DependentUpon>
    </Compile>
    <Compile Include="fAfficheurEtat.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAjouterBanque.Designer.vb">
      <DependentUpon>fAjouterBanque.vb</DependentUpon>
    </Compile>
    <Compile Include="fAjouterBanque.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAjouterFiche.designer.vb">
      <DependentUpon>fAjouterFiche.vb</DependentUpon>
    </Compile>
    <Compile Include="fAjouterFiche.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAjoutIconBurau.Designer.vb">
      <DependentUpon>fAjoutIconBurau.vb</DependentUpon>
    </Compile>
    <Compile Include="fAjoutIconBurau.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fCameraClient.Designer.vb">
      <DependentUpon>fCameraClient.vb</DependentUpon>
    </Compile>
    <Compile Include="fCameraClient.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fCameraOrdonnance.Designer.vb">
      <DependentUpon>fCameraOrdonnance.vb</DependentUpon>
    </Compile>
    <Compile Include="fCameraOrdonnance.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="EtatEntree.vb">
      <DependentUpon>EtatEntree.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="DataConnection.vb" />
    <Compile Include="DataSet_ETAT_Exception.vb">
      <DependentUpon>DataSet_ETAT_Exception.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSet_ETAT_Exception1.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSet_ETAT_Exception.xsd</DependentUpon>
    </Compile>
    <Compile Include="EtatArticlePerime.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatArticlePerime.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatBons.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatBons.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatCAParCategorie.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatCAParCategorie.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatCommande.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatCommande.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDeReleveCNAM.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDeReleveCNAM.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDeReleveCNAMTemporaire.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDeReleveCNAMTemporaire.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDeReleveMutuelle.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDeReleveMutuelle.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDeReleveMutuelleTemporaire.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDeReleveMutuelleTemporaire.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDesFactures.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDesFactures.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDesFournisseurs.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDesFournisseurs.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDesPreparationAProduire.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDesPreparationAProduire.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDesStatistiquesFournisseur.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDesStatistiquesFournisseur.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDesVentesParAnnee.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDesVentesParAnnee.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDesVentesParJour.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDesVentesParJour.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDesVentesParMois.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDesVentesParMois.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDetailsDesVentes.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDetailsDesVentes.rpt</DependentUpon>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="EtatDevisBLVenteCNAM.vb">
      <DependentUpon>EtatDevisBLVenteCNAM.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="EtatDevisBLVenteTemporaire.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDevisBLVenteTemporaire.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatEcheanceDesFournisseurs.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatEcheanceDesFournisseurs.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatEmprunt.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatEmprunt.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatEntreeArticle.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatEntreeArticle.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatFactureVenteCNAM.vb">
      <DependentUpon>EtatFactureVenteCNAM.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="EtatDevisBLVente.vb">
      <DependentUpon>EtatDevisBLVente.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="EtatDesVentesNonVidees1.vb">
      <DependentUpon>EtatDesVentesNonVidees.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="EtatDesVentes.vb">
      <DependentUpon>EtatDesVentes.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="EtatEcheancesDesClients.vb">
      <DependentUpon>EtatEcheancesDesClients.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="EtatHitParadeArticle.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatHitParadeArticle.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatInventaire.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatInventaire.rpt</DependentUpon>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="EtatJournalDesAchats.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatJournalDesAchats.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatJournalDesVentes.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatJournalDesVentes.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatJournalReleveCNAM.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatJournalReleveCNAM.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatJournalReleveMutuelle.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatJournalReleveMutuelle.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatJournalTVAAchat.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatJournalTVAAchat.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatJournalTVADetail.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatJournalTVADetail.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatJournalTVAVente.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatJournalTVAVente.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatJournalTVAVenteMois.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatJournalTVAVenteMois.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatListeDesManquants.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatListeDesManquants.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatMouvementArticle.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatMouvementArticle.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatMouvementDuClient.vb">
      <DependentUpon>EtatMouvementDuClient.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="EtatDesClients.vb">
      <DependentUpon>EtatDesClients.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDesArticlesSansImpressionStock.vb">
      <DependentUpon>EtatDesArticlesSansImpressionStock.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="EtatAchat.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatAchat.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatDesArticles.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatDesArticles.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatFactureVente.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatFactureVente.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatMouvementDuFournisseur.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatMouvementDuFournisseur.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatMouvementPharmacie.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatMouvementPharmacie.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatOrdonnance.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatOrdonnance.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatOrdonnanceCNAM.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatOrdonnanceCNAM.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatOrdonnanceVente.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatOrdonnanceVente.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatOrdonnancier1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatOrdonnancier.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatOrdonnancierNonRegleCNAM.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatOrdonnancierNonRegleCNAM.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatOrdonnancierNonRegleMutuelle.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatOrdonnancierNonRegleMutuelle.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatPharma2000PremiumUpdate.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatPharma2000PremiumUpdate.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatPret1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatPret1.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatProduction.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatProduction.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatRecapCaisse.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatRecapCaisse.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatRecuReglement.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatRecuReglement.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatReglement.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatReglement.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatReleveMouvementCNAM.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatReleveMouvementCNAM.rpt</DependentUpon>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="EtatReleveMouvementMutuelle.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatReleveMouvementMutuelle.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatSortie1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatSortie1.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatSortieArticle.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatSortieArticle.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatStockParCategorie.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatStockParCategorie.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatStrategieDeStockage.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatStrategieDeStockage.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatTicketFacture.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatTicketFacture.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatTicketFactureCNAM.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatTicketFactureCNAM.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="EtatTicketFactureSupprime.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EtatTicketFactureSupprime.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="Etat_Exception.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Etat_Exception.rpt</DependentUpon>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="fAchatJusteAffichage.Designer.vb">
      <DependentUpon>fAchatJusteAffichage.vb</DependentUpon>
    </Compile>
    <Compile Include="fAchatJusteAffichage.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAchatSupprime.Designer.vb">
      <DependentUpon>fAchatSupprime.vb</DependentUpon>
    </Compile>
    <Compile Include="fAchatSupprime.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAffectationDesAutorisations.Designer.vb">
      <DependentUpon>fAffectationDesAutorisations.vb</DependentUpon>
    </Compile>
    <Compile Include="fAffectationDesAutorisations.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAffectationDesAutorisationsProfil.Designer.vb">
      <DependentUpon>fAffectationDesAutorisationsProfil.vb</DependentUpon>
    </Compile>
    <Compile Include="fAffectationDesAutorisationsProfil.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAjouterCategorie.Designer.vb">
      <DependentUpon>fAjouterCategorie.vb</DependentUpon>
    </Compile>
    <Compile Include="fAjouterCategorie.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAjouterDCI.Designer.vb">
      <DependentUpon>fAjouterDCI.vb</DependentUpon>
    </Compile>
    <Compile Include="fAjouterDCI.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAjouterForme.Designer.vb">
      <DependentUpon>fAjouterForme.vb</DependentUpon>
    </Compile>
    <Compile Include="fAjouterForme.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAjouterLaboratoire.Designer.vb">
      <DependentUpon>fAjouterLaboratoire.vb</DependentUpon>
    </Compile>
    <Compile Include="fAjouterLaboratoire.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAlimentationCaisse.Designer.vb">
      <DependentUpon>fAlimentationCaisse.vb</DependentUpon>
    </Compile>
    <Compile Include="fAlimentationCaisse.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAPCI.Designer.vb">
      <DependentUpon>fAPCI.vb</DependentUpon>
    </Compile>
    <Compile Include="fAPCI.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fArticleNonRemboursable.Designer.vb">
      <DependentUpon>fArticleNonRemboursable.vb</DependentUpon>
    </Compile>
    <Compile Include="fArticleNonRemboursable.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fArticleRemboursable.Designer.vb">
      <DependentUpon>fArticleRemboursable.vb</DependentUpon>
    </Compile>
    <Compile Include="fArticleRemboursable.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fCamera.Designer.vb">
      <DependentUpon>fCamera.vb</DependentUpon>
    </Compile>
    <Compile Include="fCamera.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fChangemantDuFichierArticle.Designer.vb">
      <DependentUpon>fChangemantDuFichierArticle.vb</DependentUpon>
    </Compile>
    <Compile Include="fChangemantDuFichierArticle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fChangerLoginMotDePasse.Designer.vb">
      <DependentUpon>fChangerLoginMotDePasse.vb</DependentUpon>
    </Compile>
    <Compile Include="fChangerLoginMotDePasse.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fCleRegistre.designer.vb">
      <DependentUpon>fCleRegistre.vb</DependentUpon>
    </Compile>
    <Compile Include="fCleRegistre.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fCNAM.Designer.vb">
      <DependentUpon>fCNAM.vb</DependentUpon>
    </Compile>
    <Compile Include="fCNAM.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fCommandeDesManquants.Designer.vb">
      <DependentUpon>fCommandeDesManquants.vb</DependentUpon>
    </Compile>
    <Compile Include="fCommandeDesManquants.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fCorrectionDesLots.Designer.vb">
      <DependentUpon>fCorrectionDesLots.vb</DependentUpon>
    </Compile>
    <Compile Include="fCorrectionDesLots.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fCritereDeRechercheInventaire.Designer.vb">
      <DependentUpon>fCritereDeRechercheInventaire.vb</DependentUpon>
    </Compile>
    <Compile Include="fCritereDeRechercheInventaire.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fCritereDeSelectionCommande.Designer.vb">
      <DependentUpon>fCritereDeSelectionCommande.vb</DependentUpon>
    </Compile>
    <Compile Include="fCritereDeSelectionCommande.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAchat.Designer.vb">
      <DependentUpon>fAchat.vb</DependentUpon>
    </Compile>
    <Compile Include="fAchat.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fArticle.Designer.vb">
      <DependentUpon>fArticle.vb</DependentUpon>
    </Compile>
    <Compile Include="fArticle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fBanque.Designer.vb">
      <DependentUpon>fBanque.vb</DependentUpon>
    </Compile>
    <Compile Include="fBanque.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fBaseSQL.designer.vb">
      <DependentUpon>fBaseSQL.vb</DependentUpon>
    </Compile>
    <Compile Include="fBaseSQL.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fCategorie.Designer.vb">
      <DependentUpon>fCategorie.vb</DependentUpon>
    </Compile>
    <Compile Include="fCategorie.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fClient.Designer.vb">
      <DependentUpon>fClient.vb</DependentUpon>
    </Compile>
    <Compile Include="fClient.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fCommande.Designer.vb">
      <DependentUpon>fCommande.vb</DependentUpon>
    </Compile>
    <Compile Include="fCommande.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fCritereDuReleveCNAM.Designer.vb">
      <DependentUpon>fCritereDuReleveCNAM.vb</DependentUpon>
    </Compile>
    <Compile Include="fCritereDuReleveCNAM.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fCritereDuReleveMutuelle.Designer.vb">
      <DependentUpon>fCritereDuReleveMutuelle.vb</DependentUpon>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="fCritereDuReleveMutuelle.vb">
      <SubType>Form</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="fCubeVenteDetail.Designer.vb">
      <DependentUpon>fCubeVenteDetail.vb</DependentUpon>
    </Compile>
    <Compile Include="fCubeVenteDetail.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fDCI.Designer.vb">
      <DependentUpon>fDCI.vb</DependentUpon>
    </Compile>
    <Compile Include="fDCI.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fDetailTVA.designer.vb">
      <DependentUpon>fDetailTVA.vb</DependentUpon>
    </Compile>
    <Compile Include="fDetailTVA.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEcheancesDesClients.Designer.vb">
      <DependentUpon>fEcheancesDesClients.vb</DependentUpon>
    </Compile>
    <Compile Include="fEcheancesDesClients.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEcheancesDesFournisseurs.Designer.vb">
      <DependentUpon>fEcheancesDesFournisseurs.vb</DependentUpon>
    </Compile>
    <Compile Include="fEcheancesDesFournisseurs.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEmprunt.Designer.vb">
      <DependentUpon>fEmprunt.vb</DependentUpon>
    </Compile>
    <Compile Include="fEmprunt.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEntree.Designer.vb">
      <DependentUpon>fEntree.vb</DependentUpon>
    </Compile>
    <Compile Include="fEntree.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEquivalentOuVenteEnNegative.Designer.vb">
      <DependentUpon>fEquivalentOuVenteEnNegative.vb</DependentUpon>
    </Compile>
    <Compile Include="fEquivalentOuVenteEnNegative.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="EtatDeBonDeReglement.vb">
      <DependentUpon>EtatDeBonDeReglement.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="fEtatChiffreAffaireCategorie.designer.vb">
      <DependentUpon>fEtatChiffreAffaireCategorie.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatChiffreAffaireCategorie.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatDesFactures.Designer.vb">
      <DependentUpon>fEtatDesFactures.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatDesFactures.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatDesVentes.Designer.vb">
      <DependentUpon>fEtatDesVentes.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatDesVentes.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatDesVentesParAnnee.Designer.vb">
      <DependentUpon>fEtatDesVentesParAnnee.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatDesVentesParAnnee.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatDesVentesParMois.Designer.vb">
      <DependentUpon>fEtatDesVentesParMois.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatDesVentesParMois.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatDesVentesParJour.Designer.vb">
      <DependentUpon>fEtatDesVentesParJour.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatDesVentesParJour.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatDetailsDesVentes.Designer.vb">
      <DependentUpon>fEtatDetailsDesVentes.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatDetailsDesVentes.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatEntreeArticle.designer.vb">
      <DependentUpon>fEtatEntreeArticle.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatEntreeArticle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatInventaire.designer.vb">
      <DependentUpon>fEtatInventaire.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatInventaire.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatJournalDesAchats.Designer.vb">
      <DependentUpon>fEtatJournalDesAchats.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatJournalDesAchats.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatJournalDesVentes.Designer.vb">
      <DependentUpon>fEtatJournalDesVentes.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatJournalDesVentes.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatJournalReleveCNAM.designer.vb">
      <DependentUpon>fEtatJournalReleveCNAM.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatJournalReleveCNAM.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatJournalReleveMutuelle.designer.vb">
      <DependentUpon>fEtatJournalReleveMutuelle.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatJournalReleveMutuelle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatOrdonnanceNonRegleCNAM.designer.vb">
      <DependentUpon>fEtatOrdonnanceNonRegleCNAM.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatOrdonnanceNonRegleCNAM.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatOrdonnanceNonRegleMutuelle.designer.vb">
      <DependentUpon>fEtatOrdonnanceNonRegleMutuelle.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatOrdonnanceNonRegleMutuelle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatOrdonnancier.Designer.vb">
      <DependentUpon>fEtatOrdonnancier.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatOrdonnancier.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatReglement.designer.vb">
      <DependentUpon>fEtatReglement.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatReglement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatReleveMouvementCNAM.Designer.vb">
      <DependentUpon>fEtatReleveMouvementCNAM.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatReleveMouvementCNAM.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatReleveMouvementMutuelle.designer.vb">
      <DependentUpon>fEtatReleveMouvementMutuelle.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatReleveMouvementMutuelle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fEtatSortiArticle.Designer.vb">
      <DependentUpon>fEtatSortiArticle.vb</DependentUpon>
    </Compile>
    <Compile Include="fEtatSortiArticle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fException.designer.vb">
      <DependentUpon>fException.vb</DependentUpon>
    </Compile>
    <Compile Include="fException.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fFamilleNonRemboursable.Designer.vb">
      <DependentUpon>fFamilleNonRemboursable.vb</DependentUpon>
    </Compile>
    <Compile Include="fFamilleNonRemboursable.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fFamilleRemboursable.Designer.vb">
      <DependentUpon>fFamilleRemboursable.vb</DependentUpon>
    </Compile>
    <Compile Include="fFamilleRemboursable.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fFicheArticle.Designer.vb">
      <DependentUpon>fFicheArticle.vb</DependentUpon>
    </Compile>
    <Compile Include="fFicheArticle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fFicheArticle111.Designer.vb">
      <DependentUpon>fFicheArticle111.vb</DependentUpon>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="fFicheArticle111.vb">
      <SubType>Form</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="fFicheClient.Designer.vb">
      <DependentUpon>fFicheClient.vb</DependentUpon>
    </Compile>
    <Compile Include="fFicheClient.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fFicheClient111.Designer.vb">
      <DependentUpon>fFicheClient111.vb</DependentUpon>
    </Compile>
    <Compile Include="fFicheClient111.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fFicheDeContact.designer.vb">
      <DependentUpon>fFicheDeContact.vb</DependentUpon>
    </Compile>
    <Compile Include="fFicheDeContact.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fFicheFournisseur.designer.vb">
      <DependentUpon>fFicheFournisseur.vb</DependentUpon>
    </Compile>
    <Compile Include="fFicheFournisseur.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fFicheMutuelle.designer.vb">
      <DependentUpon>fFicheMutuelle.vb</DependentUpon>
    </Compile>
    <Compile Include="fFicheMutuelle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fFichePharmacie.Designer.vb">
      <DependentUpon>fFichePharmacie.vb</DependentUpon>
    </Compile>
    <Compile Include="fFichePharmacie.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fFicheReleveeCNAM.Designer.vb">
      <DependentUpon>fFicheReleveeCNAM.vb</DependentUpon>
    </Compile>
    <Compile Include="fFicheReleveeCNAM.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fFicheReleveeMutuelle.Designer.vb">
      <DependentUpon>fFicheReleveeMutuelle.vb</DependentUpon>
    </Compile>
    <Compile Include="fFicheReleveeMutuelle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fForme.Designer.vb">
      <DependentUpon>fForme.vb</DependentUpon>
    </Compile>
    <Compile Include="fForme.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fFournisseur.Designer.vb">
      <DependentUpon>fFournisseur.vb</DependentUpon>
    </Compile>
    <Compile Include="fFournisseur.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fFractionnement.Designer.vb">
      <DependentUpon>fFractionnement.vb</DependentUpon>
    </Compile>
    <Compile Include="fFractionnement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fGestionPointsControle.Designer.vb">
      <DependentUpon>fGestionPointsControle.vb</DependentUpon>
    </Compile>
    <Compile Include="fGestionPointsControle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fHistoriqueDesAchats.designer.vb">
      <DependentUpon>fHistoriqueDesAchats.vb</DependentUpon>
    </Compile>
    <Compile Include="fHistoriqueDesAchats.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fHistoriqueDesActions.Designer.vb">
      <DependentUpon>fHistoriqueDesActions.vb</DependentUpon>
    </Compile>
    <Compile Include="fHistoriqueDesActions.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fHistoriqueDesChangementsDesPrix.Designer.vb">
      <DependentUpon>fHistoriqueDesChangementsDesPrix.vb</DependentUpon>
    </Compile>
    <Compile Include="fHistoriqueDesChangementsDesPrix.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fHitParadeArticle.designer.vb">
      <DependentUpon>fHitParadeArticle.vb</DependentUpon>
    </Compile>
    <Compile Include="fHitParadeArticle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fHistoriqueDAcces.Designer.vb">
      <DependentUpon>fHistoriqueDAcces.vb</DependentUpon>
    </Compile>
    <Compile Include="fHistoriqueDAcces.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FileItem.vb" />
    <Compile Include="fImpressionCodeABarre.Designer.vb">
      <DependentUpon>fImpressionCodeABarre.vb</DependentUpon>
    </Compile>
    <Compile Include="fImpressionCodeABarre.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fImpressionDesRelevesCNAM.Designer.vb">
      <DependentUpon>fImpressionDesRelevesCNAM.vb</DependentUpon>
    </Compile>
    <Compile Include="fImpressionDesRelevesCNAM.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fImpressionEtiquette.Designer.vb">
      <DependentUpon>fImpressionEtiquette.vb</DependentUpon>
    </Compile>
    <Compile Include="fImpressionEtiquette.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fImpressionVente.Designer.vb">
      <DependentUpon>fImpressionVente.vb</DependentUpon>
    </Compile>
    <Compile Include="fImpressionVente.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fIndemnite.Designer.vb">
      <DependentUpon>fIndemnite.vb</DependentUpon>
    </Compile>
    <Compile Include="fIndemnite.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fInformationReglement.Designer.vb">
      <DependentUpon>fInformationReglement.vb</DependentUpon>
    </Compile>
    <Compile Include="fInformationReglement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fInteraction.Designer.vb">
      <DependentUpon>fInteraction.vb</DependentUpon>
    </Compile>
    <Compile Include="fInteraction.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fInteractionsCreation.Designer.vb">
      <DependentUpon>fInteractionsCreation.vb</DependentUpon>
    </Compile>
    <Compile Include="fInteractionsCreation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fInventaire.designer.vb">
      <DependentUpon>fInventaire.vb</DependentUpon>
    </Compile>
    <Compile Include="fInventaire.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fInventaireLectureTerminal.designer.vb">
      <DependentUpon>fInventaireLectureTerminal.vb</DependentUpon>
    </Compile>
    <Compile Include="fInventaireLectureTerminal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fLaboratoire.designer.vb">
      <DependentUpon>fLaboratoire.vb</DependentUpon>
    </Compile>
    <Compile Include="fLaboratoire.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fLibellesMutuelle.Designer.vb">
      <DependentUpon>fLibellesMutuelle.vb</DependentUpon>
    </Compile>
    <Compile Include="fLibellesMutuelle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeArticlePerime.Designer.vb">
      <DependentUpon>fListeArticlePerime.vb</DependentUpon>
    </Compile>
    <Compile Include="fListeArticlePerime.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeDeRecalculDeStock.Designer.vb">
      <DependentUpon>fListeDeRecalculDeStock.vb</DependentUpon>
    </Compile>
    <Compile Include="fListeDeRecalculDeStock.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeDesAchats.Designer.vb">
      <DependentUpon>fListeDesAchats.vb</DependentUpon>
    </Compile>
    <Compile Include="fListeDesAchats.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeDesBons.Designer.vb">
      <DependentUpon>fListeDesBons.vb</DependentUpon>
    </Compile>
    <Compile Include="fListeDesBons.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeDesCommandes.Designer.vb">
      <DependentUpon>fListeDesCommandes.vb</DependentUpon>
    </Compile>
    <Compile Include="fListeDesCommandes.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeDesEquivalents.Designer.vb">
      <DependentUpon>fListeDesEquivalents.vb</DependentUpon>
    </Compile>
    <Compile Include="fListeDesEquivalents.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeDesInventaires.Designer.vb">
      <DependentUpon>fListeDesInventaires.vb</DependentUpon>
    </Compile>
    <Compile Include="fListeDesInventaires.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeDesPreparationAProduire.Designer.vb">
      <DependentUpon>fListeDesPreparationAProduire.vb</DependentUpon>
    </Compile>
    <Compile Include="fListeDesPreparationAProduire.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListePreparation.Designer.vb">
      <DependentUpon>fListePreparation.vb</DependentUpon>
    </Compile>
    <Compile Include="fListePreparation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListesPourRecapCaisse.Designer.vb">
      <DependentUpon>fListesPourRecapCaisse.vb</DependentUpon>
    </Compile>
    <Compile Include="fListesPourRecapCaisse.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fMedecin.Designer.vb">
      <DependentUpon>fMedecin.vb</DependentUpon>
    </Compile>
    <Compile Include="fMedecin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fAjouterMedecin.Designer.vb">
      <DependentUpon>fAjouterMedecin.vb</DependentUpon>
    </Compile>
    <Compile Include="fAjouterMedecin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fMessageException.Designer.vb">
      <DependentUpon>fMessageException.vb</DependentUpon>
    </Compile>
    <Compile Include="fMessageException.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fMesVoisinage.designer.vb">
      <DependentUpon>fMesVoisinage.vb</DependentUpon>
    </Compile>
    <Compile Include="fMesVoisinage.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fMotDePasse.Designer.vb">
      <DependentUpon>fMotDePasse.vb</DependentUpon>
    </Compile>
    <Compile Include="fMotDePasse.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fMotDePasseOuvertureTiroir.Designer.vb">
      <DependentUpon>fMotDePasseOuvertureTiroir.vb</DependentUpon>
    </Compile>
    <Compile Include="fMotDePasseOuvertureTiroir.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fMouvementArticle.designer.vb">
      <DependentUpon>fMouvementArticle.vb</DependentUpon>
    </Compile>
    <Compile Include="fMouvementArticle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fMouvementDesClients.Designer.vb">
      <DependentUpon>fMouvementDesClients.vb</DependentUpon>
    </Compile>
    <Compile Include="fMouvementDesClients.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fMouvementDesFournisseurs.Designer.vb">
      <DependentUpon>fMouvementDesFournisseurs.vb</DependentUpon>
    </Compile>
    <Compile Include="fMouvementDesFournisseurs.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fMouvementPharmacien.designer.vb">
      <DependentUpon>fMouvementPharmacien.vb</DependentUpon>
    </Compile>
    <Compile Include="fMouvementPharmacien.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fNatureEntreeSortie.Designer.vb">
      <DependentUpon>fNatureEntreeSortie.vb</DependentUpon>
    </Compile>
    <Compile Include="fNatureEntreeSortie.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeDesAchatsEnInstance.Designer.vb">
      <DependentUpon>fListeDesAchatsEnInstance.vb</DependentUpon>
    </Compile>
    <Compile Include="fListeDesAchatsEnInstance.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeDesCommandesEnInstance.Designer.vb">
      <DependentUpon>fListeDesCommandesEnInstance.vb</DependentUpon>
    </Compile>
    <Compile Include="fListeDesCommandesEnInstance.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeDesManquants.designer.vb">
      <DependentUpon>fListeDesManquants.vb</DependentUpon>
    </Compile>
    <Compile Include="fListeDesManquants.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeDesVentesEnInstance.Designer.vb">
      <DependentUpon>fListeDesVentesEnInstance.vb</DependentUpon>
    </Compile>
    <Compile Include="fListeDesVentesEnInstance.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fLogin.Designer.vb">
      <DependentUpon>fLogin.vb</DependentUpon>
    </Compile>
    <Compile Include="fLogin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fMain.designer.vb">
      <DependentUpon>fMain.vb</DependentUpon>
    </Compile>
    <Compile Include="fMain.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fMaquetteChangerPrix.Designer.vb">
      <DependentUpon>fMaquetteChangerPrix.vb</DependentUpon>
    </Compile>
    <Compile Include="fMaquetteChangerPrix.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fMutuelle.designer.vb">
      <DependentUpon>fMutuelle.vb</DependentUpon>
    </Compile>
    <Compile Include="fMutuelle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fMaquetteCnam.Designer.vb">
      <DependentUpon>fMaquetteCnam.vb</DependentUpon>
    </Compile>
    <Compile Include="fMaquetteCnam.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fNomBoutonLien.Designer.vb">
      <DependentUpon>fNomBoutonLien.vb</DependentUpon>
    </Compile>
    <Compile Include="fNomBoutonLien.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fNombreDeCopieImpression.Designer.vb">
      <DependentUpon>fNombreDeCopieImpression.vb</DependentUpon>
    </Compile>
    <Compile Include="fNombreDeCopieImpression.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fNombreQuantiteControle.Designer.vb">
      <DependentUpon>fNombreQuantiteControle.vb</DependentUpon>
    </Compile>
    <Compile Include="fNombreQuantiteControle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fNomInstance.Designer.vb">
      <DependentUpon>fNomInstance.vb</DependentUpon>
    </Compile>
    <Compile Include="fNomInstance.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fOrdonnancier.Designer.vb">
      <DependentUpon>fOrdonnancier.vb</DependentUpon>
    </Compile>
    <Compile Include="fOrdonnancier.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fCritereCubeVenteDetail.Designer.vb">
      <DependentUpon>fCritereCubeVenteDetail.vb</DependentUpon>
    </Compile>
    <Compile Include="fCritereCubeVenteDetail.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.vb">
      <DependentUpon>Form1.vb</DependentUpon>
    </Compile>
    <Compile Include="Form1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fPaiement.Designer.vb">
      <DependentUpon>fPaiement.vb</DependentUpon>
    </Compile>
    <Compile Include="fPaiement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fPaiementMultiple.designer.vb">
      <DependentUpon>fPaiementMultiple.vb</DependentUpon>
    </Compile>
    <Compile Include="fPaiementMultiple.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fPaiementTiroir.Designer.vb">
      <DependentUpon>fPaiementTiroir.vb</DependentUpon>
    </Compile>
    <Compile Include="fPaiementTiroir.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fParametres.Designer.vb">
      <DependentUpon>fParametres.vb</DependentUpon>
    </Compile>
    <Compile Include="fParametres.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fParametresGeneraux.designer.vb">
      <DependentUpon>fParametresGeneraux.vb</DependentUpon>
    </Compile>
    <Compile Include="fParametresGeneraux.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fParametresPoste.Designer.vb">
      <DependentUpon>fParametresPoste.vb</DependentUpon>
    </Compile>
    <Compile Include="fParametresPoste.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fParametresUtilisateur.Designer.vb">
      <DependentUpon>fParametresUtilisateur.vb</DependentUpon>
    </Compile>
    <Compile Include="fParametresUtilisateur.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fPharmacie.Designer.vb">
      <DependentUpon>fPharmacie.vb</DependentUpon>
    </Compile>
    <Compile Include="fPharmacie.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fPreparation.Designer.vb">
      <DependentUpon>fPreparation.vb</DependentUpon>
    </Compile>
    <Compile Include="fPreparation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fPret.Designer.vb">
      <DependentUpon>fPret.vb</DependentUpon>
    </Compile>
    <Compile Include="fPret.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fProduction.Designer.vb">
      <DependentUpon>fProduction.vb</DependentUpon>
    </Compile>
    <Compile Include="fProduction.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fProductionMagistrale.Designer.vb">
      <DependentUpon>fProductionMagistrale.vb</DependentUpon>
    </Compile>
    <Compile Include="fProductionMagistrale.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fProfilUtilisateur.Designer.vb">
      <DependentUpon>fProfilUtilisateur.vb</DependentUpon>
    </Compile>
    <Compile Include="fProfilUtilisateur.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fProjetCommande.Designer.vb">
      <DependentUpon>fProjetCommande.vb</DependentUpon>
    </Compile>
    <Compile Include="fProjetCommande.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fProjetCommandeResteCommandes.Designer.vb">
      <DependentUpon>fProjetCommandeResteCommandes.vb</DependentUpon>
    </Compile>
    <Compile Include="fProjetCommandeResteCommandes.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fQuantiteADelivrer.Designer.vb">
      <DependentUpon>fQuantiteADelivrer.vb</DependentUpon>
    </Compile>
    <Compile Include="fQuantiteADelivrer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fRecapitulatifCaisse.Designer.vb">
      <DependentUpon>fRecapitulatifCaisse.vb</DependentUpon>
    </Compile>
    <Compile Include="fRecapitulatifCaisse.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fReceptionFichierTerminal.Designer.vb">
      <DependentUpon>fReceptionFichierTerminal.vb</DependentUpon>
    </Compile>
    <Compile Include="fReceptionFichierTerminal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fRechercheArticleMultiCritere.Designer.vb">
      <DependentUpon>fRechercheArticleMultiCritere.vb</DependentUpon>
    </Compile>
    <Compile Include="fRechercheArticleMultiCritere.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fReglementClient.Designer.vb">
      <DependentUpon>fReglementClient.vb</DependentUpon>
    </Compile>
    <Compile Include="fReglementClient.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fReglementCNAM.Designer.vb">
      <DependentUpon>fReglementCNAM.vb</DependentUpon>
    </Compile>
    <Compile Include="fReglementCNAM.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fReglementFactureClient.Designer.vb">
      <DependentUpon>fReglementFactureClient.vb</DependentUpon>
    </Compile>
    <Compile Include="fReglementFactureClient.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fReglementFournisseur.Designer.vb">
      <DependentUpon>fReglementFournisseur.vb</DependentUpon>
    </Compile>
    <Compile Include="fReglementFournisseur.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fReglementMutuelle.Designer.vb">
      <DependentUpon>fReglementMutuelle.vb</DependentUpon>
    </Compile>
    <Compile Include="fReglementMutuelle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fReglementPharmacie.Designer.vb">
      <DependentUpon>fReglementPharmacie.vb</DependentUpon>
    </Compile>
    <Compile Include="fReglementPharmacie.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fReleve.Designer.vb">
      <DependentUpon>fReleve.vb</DependentUpon>
    </Compile>
    <Compile Include="fReleve.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fReleveeCNAM.Designer.vb">
      <DependentUpon>fReleveeCNAM.vb</DependentUpon>
    </Compile>
    <Compile Include="fReleveeCNAM.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fReleveeMutuelle.Designer.vb">
      <DependentUpon>fReleveeMutuelle.vb</DependentUpon>
    </Compile>
    <Compile Include="fReleveeMutuelle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fSituationArticle.Designer.vb">
      <DependentUpon>fSituationArticle.vb</DependentUpon>
    </Compile>
    <Compile Include="fSituationArticle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fSituationClient.designer.vb">
      <DependentUpon>fSituationClient.vb</DependentUpon>
    </Compile>
    <Compile Include="fSituationClient.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fSortie.Designer.vb">
      <DependentUpon>fSortie.vb</DependentUpon>
    </Compile>
    <Compile Include="fSortie.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fSpecialiteMedecin.Designer.vb">
      <DependentUpon>fSpecialiteMedecin.vb</DependentUpon>
    </Compile>
    <Compile Include="fSpecialiteMedecin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fStatistiqueDesFournisseurs.Designer.vb">
      <DependentUpon>fStatistiqueDesFournisseurs.vb</DependentUpon>
    </Compile>
    <Compile Include="fStatistiqueDesFournisseurs.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fStatistiqueVente.designer.vb">
      <DependentUpon>fStatistiqueVente.vb</DependentUpon>
    </Compile>
    <Compile Include="fStatistiqueVente.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fStockParCategorie.designer.vb">
      <DependentUpon>fStockParCategorie.vb</DependentUpon>
    </Compile>
    <Compile Include="fStockParCategorie.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fStrategieDeStockage.Designer.vb">
      <DependentUpon>fStrategieDeStockage.vb</DependentUpon>
    </Compile>
    <Compile Include="fStrategieDeStockage.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fSuppressionDesVentes.Designer.vb">
      <DependentUpon>fSuppressionDesVentes.vb</DependentUpon>
    </Compile>
    <Compile Include="fSuppressionDesVentes.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fTestStatisqtique.Designer.vb">
      <DependentUpon>fTestStatisqtique.vb</DependentUpon>
    </Compile>
    <Compile Include="fTestStatisqtique.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FTP.vb" />
    <Compile Include="FtpConnection.vb" />
    <Compile Include="fUpdateArticle.Designer.vb">
      <DependentUpon>fUpdateArticle.vb</DependentUpon>
    </Compile>
    <Compile Include="fUpdateArticle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fUtilisateur.Designer.vb">
      <DependentUpon>fUtilisateur.vb</DependentUpon>
    </Compile>
    <Compile Include="fUtilisateur.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fValeurSimulation.Designer.vb">
      <DependentUpon>fValeurSimulation.vb</DependentUpon>
    </Compile>
    <Compile Include="fValeurSimulation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fValeurTVAAchat.designer.vb">
      <DependentUpon>fValeurTVAAchat.vb</DependentUpon>
    </Compile>
    <Compile Include="fValeurTVAAchat.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fValeurTVAVente.designer.vb">
      <DependentUpon>fValeurTVAVente.vb</DependentUpon>
    </Compile>
    <Compile Include="fValeurTVAVente.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fValeurTVAVenteMois.Designer.vb">
      <DependentUpon>fValeurTVAVenteMois.vb</DependentUpon>
    </Compile>
    <Compile Include="fValeurTVAVenteMois.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fVente.Designer.vb">
      <DependentUpon>fVente.vb</DependentUpon>
    </Compile>
    <Compile Include="fVente.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fVenteDetailImage.Designer.vb">
      <DependentUpon>fVenteDetailImage.vb</DependentUpon>
    </Compile>
    <Compile Include="fVenteDetailImage.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fVenteJusteAffichage.Designer.vb">
      <DependentUpon>fVenteJusteAffichage.vb</DependentUpon>
    </Compile>
    <Compile Include="fVenteJusteAffichage.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fVenteSupprime.Designer.vb">
      <DependentUpon>fVenteSupprime.vb</DependentUpon>
    </Compile>
    <Compile Include="fVenteSupprime.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fVerifierPrixPreparations.Designer.vb">
      <DependentUpon>fVerifierPrixPreparations.vb</DependentUpon>
    </Compile>
    <Compile Include="fVerifierPrixPreparations.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fViewer.Designer.vb">
      <DependentUpon>fViewer.vb</DependentUpon>
    </Compile>
    <Compile Include="fViewer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fVille.Designer.vb">
      <DependentUpon>fVille.vb</DependentUpon>
    </Compile>
    <Compile Include="fVille.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fListeDesCommandePourAchat.Designer.vb">
      <DependentUpon>fListeDesCommandePourAchat.vb</DependentUpon>
    </Compile>
    <Compile Include="fListeDesCommandePourAchat.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fVisionneurCodeABarre.Designer.vb">
      <DependentUpon>fVisionneurCodeABarre.vb</DependentUpon>
    </Compile>
    <Compile Include="fVisionneurCodeABarre.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fVisionneurHTML.Designer.vb">
      <DependentUpon>fVisionneurHTML.vb</DependentUpon>
    </Compile>
    <Compile Include="fVisionneurHTML.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fVisionneurInteraction.Designer.vb">
      <DependentUpon>fVisionneurInteraction.vb</DependentUpon>
    </Compile>
    <Compile Include="fVisionneurInteraction.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="fZoomImage.Designer.vb">
      <DependentUpon>fZoomImage.vb</DependentUpon>
    </Compile>
    <Compile Include="fZoomImage.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ModCap.vb" />
    <Compile Include="ModuleGeneral.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="PrintFile.vb" />
    <Compile Include="Service References\ServiceBCB\Reference.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\ServiceOneKey\Reference.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="TestCodeABarre.Designer.vb">
      <DependentUpon>TestCodeABarre.vb</DependentUpon>
    </Compile>
    <Compile Include="TestCodeABarre.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TraceFichier.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="FAvoirAchat.resx">
      <DependentUpon>FAvoirAchat.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="fHistoriquesMouvements.resx">
      <DependentUpon>fHistoriquesMouvements.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeArticleSansInventaire.resx">
      <DependentUpon>fListeArticleSansInventaire.vb</DependentUpon>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="fReglemntSupprime.resx">
      <DependentUpon>fReglemntSupprime.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fSaisiePerimes.resx">
      <DependentUpon>fSaisiePerimes.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fStockParCategorieIntervalleMarge.resx">
      <DependentUpon>fStockParCategorieIntervalleMarge.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fVenteNonEnregistree.resx">
      <DependentUpon>fVenteNonEnregistree.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fAchatLectureTerminal.resx">
      <DependentUpon>fAchatLectureTerminal.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListesPourSpot.resx">
      <DependentUpon>fListesPourSpot.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fMessageBoxPourQuitter.resx">
      <DependentUpon>fMessageBoxPourQuitter.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fVenteAnnuler.resx">
      <DependentUpon>fVenteAnnuler.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatFacturationClient.resx">
      <DependentUpon>fEtatFacturationClient.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fNomPourBon.resx">
      <DependentUpon>fNomPourBon.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatJournalDesVenteHR.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatJournalDesVenteHR.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatNMouvementArticleGlobal.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatNMouvementArticleGlobal.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatNMouvementArticle.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatNMouvementArticle.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="fCopieBD.resx">
      <DependentUpon>fCopieBD.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fDureeTraitement.resx">
      <DependentUpon>fDureeTraitement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fInformationClient.resx">
      <DependentUpon>fInformationClient.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeReglement.resx">
      <DependentUpon>fListeReglement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fNMouvementArticle.resx">
      <DependentUpon>fNMouvementArticle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="EcranDeDemarrage.resx">
      <DependentUpon>EcranDeDemarrage.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatCarnetCNAM.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatCarnetCNAM.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDesArticlesDateMin.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDesArticlesDateMin.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDetailsDesVentesSimple.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDetailsDesVentesSimple.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDifferenceCaisseCalculeReel.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDifferenceCaisseCalculeReel.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatHistoriqueDesAchats.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatHistoriqueDesAchats.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatHistoriqueDesActions.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatHistoriqueDesActions.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatHitParadeArticleNew.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatHitParadeArticleNew.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatImpressionCodeABarre.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatImpressionCodeABarre.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatImpressionCodeABarre2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatImpressionCodeABarre2.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatImpressionCodeABarreA4.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatImpressionCodeABarreA4.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatInventaireTemporaire.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatInventaireTemporaire.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatListeInventaire.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatListeInventaire.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatOrdonnanceTemporaire.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatOrdonnanceTemporaire.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatPhotoClient.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatPhotoClient.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatPhotoOrdonnance.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatPhotoOrdonnance.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="fAfficheurEtat.resx">
      <DependentUpon>fAfficheurEtat.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fAjouterBanque.resx">
      <DependentUpon>fAjouterBanque.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fAjouterFiche.resx">
      <DependentUpon>fAjouterFiche.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="fAjoutIconBurau.resx">
      <DependentUpon>fAjoutIconBurau.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fCameraClient.resx">
      <DependentUpon>fCameraClient.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fCameraOrdonnance.resx">
      <DependentUpon>fCameraOrdonnance.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatEntree.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatEntree.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatArticlePerime.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatArticlePerime.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatBons.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatBons.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatCAParCategorie.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatCAParCategorie.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatCommande.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatCommande.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDeReleveCNAM.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDeReleveCNAM.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDeReleveCNAMTemporaire.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDeReleveCNAMTemporaire.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDeReleveMutuelle.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDeReleveMutuelle.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDeReleveMutuelleTemporaire.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDeReleveMutuelleTemporaire.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDesFactures.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDesFactures.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDesFournisseurs.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDesFournisseurs.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDesPreparationAProduire.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDesPreparationAProduire.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDesStatistiquesFournisseur.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDesStatistiquesFournisseur.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDesVentesParAnnee.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDesVentesParAnnee.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDesVentesParJour.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDesVentesParJour.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDesVentesParMois.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDesVentesParMois.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDetailsDesVentes.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDetailsDesVentes.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDevisBLVenteCNAM.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDevisBLVenteCNAM.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDevisBLVenteTemporaire.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDevisBLVenteTemporaire.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatEcheanceDesFournisseurs.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatEcheanceDesFournisseurs.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatEmprunt.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatEmprunt.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatEntreeArticle.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatEntreeArticle.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatFactureVenteCNAM.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatFactureVenteCNAM.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDevisBLVente.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDevisBLVente.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDesVentesNonVidees.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDesVentesNonVidees1.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDesVentes.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDesVentes.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatEcheancesDesClients.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatEcheancesDesClients.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatHitParadeArticle.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatHitParadeArticle.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatInventaire.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatInventaire.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatJournalDesAchats.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatJournalDesAchats.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatJournalDesVentes.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatJournalDesVentes.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatJournalReleveCNAM.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatJournalReleveCNAM.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatJournalReleveMutuelle.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatJournalReleveMutuelle.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatJournalTVAAchat.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatJournalTVAAchat.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatJournalTVADetail.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatJournalTVADetail.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatJournalTVAVente.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatJournalTVAVente.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatJournalTVAVenteMois.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatJournalTVAVenteMois.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatListeDesManquants.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatListeDesManquants.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatMouvementArticle.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatMouvementArticle.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatMouvementDuClient.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatMouvementDuClient.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDesClients.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDesClients.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDesArticlesSansImpressionStock.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDesArticlesSansImpressionStock.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatAchat.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatAchat.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDesArticles.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDesArticles.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatFactureVente.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatFactureVente.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatMouvementDuFournisseur.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatMouvementDuFournisseur.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatMouvementPharmacie.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatMouvementPharmacie.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatOrdonnance.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatOrdonnance.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatOrdonnanceCNAM.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatOrdonnanceCNAM.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatOrdonnanceVente.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatOrdonnanceVente.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatOrdonnancier.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatOrdonnancier1.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatOrdonnancierNonRegleCNAM.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatOrdonnancierNonRegleCNAM.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatOrdonnancierNonRegleMutuelle.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatOrdonnancierNonRegleMutuelle.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatPharma2000PremiumUpdate.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatPharma2000PremiumUpdate.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatPret1.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatPret1.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatProduction.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatProduction.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatRecapCaisse.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatRecapCaisse.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatRecuReglement.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatRecuReglement.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatReglement.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatReglement.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatReleveMouvementCNAM.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatReleveMouvementCNAM.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatReleveMouvementMutuelle.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatReleveMouvementMutuelle.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatSortie1.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatSortie1.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatSortieArticle.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatSortieArticle.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatStockParCategorie.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatStockParCategorie.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatStrategieDeStockage.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatStrategieDeStockage.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatTicketFacture.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatTicketFacture.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatTicketFactureCNAM.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatTicketFactureCNAM.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatTicketFactureSupprime.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatTicketFactureSupprime.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Etat_Exception.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Etat_Exception.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="fAchatJusteAffichage.resx">
      <DependentUpon>fAchatJusteAffichage.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fAchatSupprime.resx">
      <DependentUpon>fAchatSupprime.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="fAffectationDesAutorisations.resx">
      <DependentUpon>fAffectationDesAutorisations.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fAffectationDesAutorisationsProfil.resx">
      <DependentUpon>fAffectationDesAutorisationsProfil.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="fAjouterCategorie.resx">
      <DependentUpon>fAjouterCategorie.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fAjouterDCI.resx">
      <DependentUpon>fAjouterDCI.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fAjouterForme.resx">
      <DependentUpon>fAjouterForme.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fAjouterLaboratoire.resx">
      <DependentUpon>fAjouterLaboratoire.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fAlimentationCaisse.resx">
      <DependentUpon>fAlimentationCaisse.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fAPCI.resx">
      <DependentUpon>fAPCI.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fArticleNonRemboursable.resx">
      <DependentUpon>fArticleNonRemboursable.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fArticleRemboursable.resx">
      <DependentUpon>fArticleRemboursable.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fCamera.resx">
      <DependentUpon>fCamera.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fChangemantDuFichierArticle.resx">
      <DependentUpon>fChangemantDuFichierArticle.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="fChangerLoginMotDePasse.resx">
      <DependentUpon>fChangerLoginMotDePasse.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fCleRegistre.resx">
      <DependentUpon>fCleRegistre.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fCNAM.resx">
      <DependentUpon>fCNAM.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fCommandeDesManquants.resx">
      <DependentUpon>fCommandeDesManquants.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fCorrectionDesLots.resx">
      <DependentUpon>fCorrectionDesLots.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fCritereCubeVenteDetail.resx">
      <DependentUpon>fCritereCubeVenteDetail.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fCritereDeRechercheInventaire.resx">
      <DependentUpon>fCritereDeRechercheInventaire.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fCritereDeSelectionCommande.resx">
      <DependentUpon>fCritereDeSelectionCommande.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fAchat.resx">
      <DependentUpon>fAchat.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="fArticle.resx">
      <DependentUpon>fArticle.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="fBanque.resx">
      <DependentUpon>fBanque.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fBaseSQL.resx">
      <DependentUpon>fBaseSQL.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="fCategorie.resx">
      <DependentUpon>fCategorie.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fClient.resx">
      <DependentUpon>fClient.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fCommande.resx">
      <DependentUpon>fCommande.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fCritereDuReleveCNAM.resx">
      <DependentUpon>fCritereDuReleveCNAM.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fCritereDuReleveMutuelle.resx">
      <DependentUpon>fCritereDuReleveMutuelle.vb</DependentUpon>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="fCubeVenteDetail.resx">
      <DependentUpon>fCubeVenteDetail.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fDCI.resx">
      <DependentUpon>fDCI.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fDetailTVA.resx">
      <DependentUpon>fDetailTVA.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEcheancesDesClients.resx">
      <DependentUpon>fEcheancesDesClients.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEcheancesDesFournisseurs.resx">
      <DependentUpon>fEcheancesDesFournisseurs.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEmprunt.resx">
      <DependentUpon>fEmprunt.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEntree.resx">
      <DependentUpon>fEntree.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEquivalentOuVenteEnNegative.resx">
      <DependentUpon>fEquivalentOuVenteEnNegative.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="EtatDeBonDeReglement.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatDeBonDeReglement.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatChiffreAffaireCategorie.resx">
      <DependentUpon>fEtatChiffreAffaireCategorie.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatDesFactures.resx">
      <DependentUpon>fEtatDesFactures.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatDesVentes.resx">
      <DependentUpon>fEtatDesVentes.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatDesVentesParAnnee.resx">
      <DependentUpon>fEtatDesVentesParAnnee.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatDesVentesParMois.resx">
      <DependentUpon>fEtatDesVentesParMois.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatDesVentesParJour.resx">
      <DependentUpon>fEtatDesVentesParJour.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatDetailsDesVentes.resx">
      <DependentUpon>fEtatDetailsDesVentes.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatEntreeArticle.resx">
      <DependentUpon>fEtatEntreeArticle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatInventaire.resx">
      <DependentUpon>fEtatInventaire.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatJournalDesAchats.resx">
      <DependentUpon>fEtatJournalDesAchats.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatJournalDesVentes.resx">
      <DependentUpon>fEtatJournalDesVentes.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatJournalReleveCNAM.resx">
      <DependentUpon>fEtatJournalReleveCNAM.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatJournalReleveMutuelle.resx">
      <DependentUpon>fEtatJournalReleveMutuelle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatOrdonnanceNonRegleCNAM.resx">
      <DependentUpon>fEtatOrdonnanceNonRegleCNAM.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatOrdonnanceNonRegleMutuelle.resx">
      <DependentUpon>fEtatOrdonnanceNonRegleMutuelle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatOrdonnancier.resx">
      <DependentUpon>fEtatOrdonnancier.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatReglement.resx">
      <DependentUpon>fEtatReglement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatReleveMouvementCNAM.resx">
      <DependentUpon>fEtatReleveMouvementCNAM.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatReleveMouvementMutuelle.resx">
      <DependentUpon>fEtatReleveMouvementMutuelle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fEtatSortiArticle.resx">
      <DependentUpon>fEtatSortiArticle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fException.resx">
      <DependentUpon>fException.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fFamilleNonRemboursable.resx">
      <DependentUpon>fFamilleNonRemboursable.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fFamilleRemboursable.resx">
      <DependentUpon>fFamilleRemboursable.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fFicheArticle.resx">
      <DependentUpon>fFicheArticle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fFicheArticle111.resx">
      <DependentUpon>fFicheArticle111.vb</DependentUpon>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="fFicheClient.resx">
      <DependentUpon>fFicheClient.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fFicheClient111.resx">
      <DependentUpon>fFicheClient111.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fFicheDeContact.resx">
      <DependentUpon>fFicheDeContact.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fFicheFournisseur.resx">
      <DependentUpon>fFicheFournisseur.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fFicheMutuelle.resx">
      <DependentUpon>fFicheMutuelle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fFichePharmacie.resx">
      <DependentUpon>fFichePharmacie.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fFicheReleveeCNAM.resx">
      <DependentUpon>fFicheReleveeCNAM.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fFicheReleveeMutuelle.resx">
      <DependentUpon>fFicheReleveeMutuelle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fForme.resx">
      <DependentUpon>fForme.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fFournisseur.resx">
      <DependentUpon>fFournisseur.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fFractionnement.resx">
      <DependentUpon>fFractionnement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fGestionPointsControle.resx">
      <DependentUpon>fGestionPointsControle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fHistoriqueDesAchats.resx">
      <DependentUpon>fHistoriqueDesAchats.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fHistoriqueDesActions.resx">
      <DependentUpon>fHistoriqueDesActions.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fHistoriqueDesChangementsDesPrix.resx">
      <DependentUpon>fHistoriqueDesChangementsDesPrix.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fHitParadeArticle.resx">
      <DependentUpon>fHitParadeArticle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fHistoriqueDAcces.resx">
      <DependentUpon>fHistoriqueDAcces.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fImpressionCodeABarre.resx">
      <DependentUpon>fImpressionCodeABarre.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fImpressionDesRelevesCNAM.resx">
      <DependentUpon>fImpressionDesRelevesCNAM.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fImpressionEtiquette.resx">
      <DependentUpon>fImpressionEtiquette.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="fImpressionVente.resx">
      <DependentUpon>fImpressionVente.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fIndemnite.resx">
      <DependentUpon>fIndemnite.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fInformationReglement.resx">
      <DependentUpon>fInformationReglement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fInteraction.resx">
      <DependentUpon>fInteraction.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fInteractionsCreation.resx">
      <DependentUpon>fInteractionsCreation.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fInventaire.resx">
      <DependentUpon>fInventaire.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fInventaireLectureTerminal.resx">
      <DependentUpon>fInventaireLectureTerminal.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fLaboratoire.resx">
      <DependentUpon>fLaboratoire.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fLibellesMutuelle.resx">
      <DependentUpon>fLibellesMutuelle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeArticlePerime.resx">
      <DependentUpon>fListeArticlePerime.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeDeRecalculDeStock.resx">
      <DependentUpon>fListeDeRecalculDeStock.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeDesAchats.resx">
      <DependentUpon>fListeDesAchats.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeDesBons.resx">
      <DependentUpon>fListeDesBons.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeDesCommandes.resx">
      <DependentUpon>fListeDesCommandes.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeDesEquivalents.resx">
      <DependentUpon>fListeDesEquivalents.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeDesInventaires.resx">
      <DependentUpon>fListeDesInventaires.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeDesPreparationAProduire.resx">
      <DependentUpon>fListeDesPreparationAProduire.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListePreparation.resx">
      <DependentUpon>fListePreparation.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListesPourRecapCaisse.resx">
      <DependentUpon>fListesPourRecapCaisse.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fMedecin.resx">
      <DependentUpon>fMedecin.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fAjouterMedecin.resx">
      <DependentUpon>fAjouterMedecin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="fMessageException.resx">
      <DependentUpon>fMessageException.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fMesVoisinage.resx">
      <DependentUpon>fMesVoisinage.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fMotDePasse.resx">
      <DependentUpon>fMotDePasse.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fMotDePasseOuvertureTiroir.resx">
      <DependentUpon>fMotDePasseOuvertureTiroir.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fMouvementArticle.resx">
      <DependentUpon>fMouvementArticle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fMouvementDesClients.resx">
      <DependentUpon>fMouvementDesClients.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fMouvementDesFournisseurs.resx">
      <DependentUpon>fMouvementDesFournisseurs.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fMouvementPharmacien.resx">
      <DependentUpon>fMouvementPharmacien.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fNatureEntreeSortie.resx">
      <DependentUpon>fNatureEntreeSortie.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeDesAchatsEnInstance.resx">
      <DependentUpon>fListeDesAchatsEnInstance.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeDesCommandesEnInstance.resx">
      <DependentUpon>fListeDesCommandesEnInstance.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeDesManquants.resx">
      <DependentUpon>fListeDesManquants.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeDesVentesEnInstance.resx">
      <DependentUpon>fListeDesVentesEnInstance.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fLogin.resx">
      <DependentUpon>fLogin.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fMain.resx">
      <DependentUpon>fMain.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fMaquetteChangerPrix.resx">
      <DependentUpon>fMaquetteChangerPrix.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fMaquetteCnam.resx">
      <DependentUpon>fMaquetteCnam.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="fMutuelle.resx">
      <DependentUpon>fMutuelle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fNomBoutonLien.resx">
      <DependentUpon>fNomBoutonLien.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fNombreDeCopieImpression.resx">
      <DependentUpon>fNombreDeCopieImpression.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fNombreQuantiteControle.resx">
      <DependentUpon>fNombreQuantiteControle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fNomInstance.resx">
      <DependentUpon>fNomInstance.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fOrdonnancier.resx">
      <DependentUpon>fOrdonnancier.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fPaiement.resx">
      <DependentUpon>fPaiement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fPaiementMultiple.resx">
      <DependentUpon>fPaiementMultiple.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fPaiementTiroir.resx">
      <DependentUpon>fPaiementTiroir.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fParametres.resx">
      <DependentUpon>fParametres.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fParametresGeneraux.resx">
      <DependentUpon>fParametresGeneraux.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fParametresPoste.resx">
      <DependentUpon>fParametresPoste.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fParametresUtilisateur.resx">
      <DependentUpon>fParametresUtilisateur.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fPharmacie.resx">
      <DependentUpon>fPharmacie.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fPreparation.resx">
      <DependentUpon>fPreparation.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fPret.resx">
      <DependentUpon>fPret.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fProduction.resx">
      <DependentUpon>fProduction.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fProductionMagistrale.resx">
      <DependentUpon>fProductionMagistrale.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fProfilUtilisateur.resx">
      <DependentUpon>fProfilUtilisateur.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fProjetCommande.resx">
      <DependentUpon>fProjetCommande.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fProjetCommandeResteCommandes.resx">
      <DependentUpon>fProjetCommandeResteCommandes.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fQuantiteADelivrer.resx">
      <DependentUpon>fQuantiteADelivrer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fRecapitulatifCaisse.resx">
      <DependentUpon>fRecapitulatifCaisse.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fReceptionFichierTerminal.resx">
      <DependentUpon>fReceptionFichierTerminal.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fRechercheArticleMultiCritere.resx">
      <DependentUpon>fRechercheArticleMultiCritere.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fReglementClient.resx">
      <DependentUpon>fReglementClient.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fReglementCNAM.resx">
      <DependentUpon>fReglementCNAM.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fReglementFactureClient.resx">
      <DependentUpon>fReglementFactureClient.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fReglementFournisseur.resx">
      <DependentUpon>fReglementFournisseur.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fReglementMutuelle.resx">
      <DependentUpon>fReglementMutuelle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fReglementPharmacie.resx">
      <DependentUpon>fReglementPharmacie.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fReleve.resx">
      <DependentUpon>fReleve.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fReleveeCNAM.resx">
      <DependentUpon>fReleveeCNAM.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fReleveeMutuelle.resx">
      <DependentUpon>fReleveeMutuelle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fSituationArticle.resx">
      <DependentUpon>fSituationArticle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fSituationClient.resx">
      <DependentUpon>fSituationClient.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fSortie.resx">
      <DependentUpon>fSortie.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fSpecialiteMedecin.resx">
      <DependentUpon>fSpecialiteMedecin.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fStatistiqueDesFournisseurs.resx">
      <DependentUpon>fStatistiqueDesFournisseurs.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fStatistiqueVente.resx">
      <DependentUpon>fStatistiqueVente.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fStockParCategorie.resx">
      <DependentUpon>fStockParCategorie.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fStrategieDeStockage.resx">
      <DependentUpon>fStrategieDeStockage.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fSuppressionDesVentes.resx">
      <DependentUpon>fSuppressionDesVentes.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fTestStatisqtique.resx">
      <DependentUpon>fTestStatisqtique.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fUpdateArticle.resx">
      <DependentUpon>fUpdateArticle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fUtilisateur.resx">
      <DependentUpon>fUtilisateur.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fValeurSimulation.resx">
      <DependentUpon>fValeurSimulation.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fValeurTVAAchat.resx">
      <DependentUpon>fValeurTVAAchat.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fValeurTVAVente.resx">
      <DependentUpon>fValeurTVAVente.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fValeurTVAVenteMois.resx">
      <DependentUpon>fValeurTVAVenteMois.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fVente.resx">
      <DependentUpon>fVente.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fVenteDetailImage.resx">
      <DependentUpon>fVenteDetailImage.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fVenteJusteAffichage.resx">
      <DependentUpon>fVenteJusteAffichage.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fVenteSupprime.resx">
      <DependentUpon>fVenteSupprime.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fVerifierPrixPreparations.resx">
      <DependentUpon>fVerifierPrixPreparations.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fViewer.resx">
      <DependentUpon>fViewer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fVille.resx">
      <DependentUpon>fVille.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fListeDesCommandePourAchat.resx">
      <DependentUpon>fListeDesCommandePourAchat.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fVisionneurCodeABarre.resx">
      <DependentUpon>fVisionneurCodeABarre.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fVisionneurHTML.resx">
      <DependentUpon>fVisionneurHTML.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="fVisionneurInteraction.resx">
      <DependentUpon>fVisionneurInteraction.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="fZoomImage.resx">
      <DependentUpon>fZoomImage.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report1.rdlc" />
    <EmbeddedResource Include="TestCodeABarre.resx">
      <DependentUpon>TestCodeABarre.vb</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="ClassDiagram1.cd" />
    <None Include="ClassDiagram3.cd" />
    <EmbeddedResource Include="EtatArticlePerimeSaisie.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EtatArticlePerimeSaisie.vb</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <None Include="DataSet_EtatInventaireTemporaire.xsc">
      <DependentUpon>DataSet_EtatInventaireTemporaire.xsd</DependentUpon>
    </None>
    <None Include="DataSet_EtatInventaireTemporaire.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSet_EtatInventaireTemporaire.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSet_EtatInventaireTemporaire.xss">
      <DependentUpon>DataSet_EtatInventaireTemporaire.xsd</DependentUpon>
    </None>
    <None Include="DataSet_ETAT_Exception.xsc">
      <DependentUpon>DataSet_ETAT_Exception.xsd</DependentUpon>
    </None>
    <None Include="DataSet_ETAT_Exception.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSet_ETAT_Exception1.Designer.vb</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Include="DataSet_ETAT_Exception.xss">
      <DependentUpon>DataSet_ETAT_Exception.xsd</DependentUpon>
    </None>
    <None Include="DataSet_ETAT_FICHE_DE_CONTACT.xsc">
      <DependentUpon>DataSet_ETAT_FICHE_DE_CONTACT.xsd</DependentUpon>
    </None>
    <None Include="DataSet_ETAT_FICHE_DE_CONTACT.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSet_ETAT_FICHE_DE_CONTACT.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSet_ETAT_FICHE_DE_CONTACT.xss">
      <DependentUpon>DataSet_ETAT_FICHE_DE_CONTACT.xsd</DependentUpon>
    </None>
    <None Include="DataSet_ETAT_HITPARAD_ARTICLE.xsc">
      <DependentUpon>DataSet_ETAT_HITPARAD_ARTICLE.xsd</DependentUpon>
    </None>
    <None Include="DataSet_ETAT_HITPARAD_ARTICLE.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSet_ETAT_HITPARAD_ARTICLE.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSet_ETAT_HITPARAD_ARTICLE.xss">
      <DependentUpon>DataSet_ETAT_HITPARAD_ARTICLE.xsd</DependentUpon>
    </None>
    <None Include="EtatArticleSansInventaire.rpt" />
    <None Include="EtatAvoirAchat.rpt" />
    <None Include="EtatHistoriqueMouvementArticle.rpt" />
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\DataSources\Bll.Reporting.ClassReportsVentes.datasource" />
    <None Include="My Project\DataSources\Data.Reporting.V_Report_EtatDetailsCaisse.datasource" />
    <None Include="My Project\DataSources\System.Data.DataSet.datasource" />
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="packages.config" />
    <None Include="Service References\ServiceBCB\BCBDextherEtr.wsdl" />
    <None Include="Service References\ServiceBCB\BCBDextherEtr.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.bcbControle.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.bcbEffetIndesirable.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.bcbEquivalents.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.bcbEtablissement.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.bcbInformation.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.bcbProduit.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.bcbProduitEtr.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.bcbProduitMini.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.bcbSearchResult.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.bcbUser.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.bcbVersion.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.etrInfoProduitsBefr.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.etrInfoProduitsMa.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.etrInfoProduitsRo.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceBCB\Pharma2000Premium.ServiceBCB.etrInfoProduitsTn.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServiceOneKey\ServiceOne1.wsdl" />
    <None Include="Service References\ServiceOneKey\ServiceOne3.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\ServiceOneKey\ServiceOne31.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\ServiceOneKey\ServiceOne32.xsd">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 (x86)</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 (x86)</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\nouveau.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\annuler2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\voir_modifierfpharmacien.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\recherche2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\clean1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\baseSQL1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\valider__.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\last_dounloaded.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\next_downloaded.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\previous_downloaded.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\first_dounloaded.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\client.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\article2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\fournisseur.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ville.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\categorie.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\laboratoire.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\forme.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Banque.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\archive1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\article3.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\quitter.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\catalogue.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\mouvement_fournisseur.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\echancier.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\commande.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\achat.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cataloque_article.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\saisie.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\etat.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\utilisateur.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cle.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\caisse.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Mutuelle.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cnam.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\CNAM1.PNG" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\vente.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\etat1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\etat2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\releve_mutuelle1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\TVA.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\bin.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\statistique.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\RAZ.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\pharmacie.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\EntreSortie.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\RAZ1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\etat3.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\etat31.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\etat5.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\etat4.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\etat6.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cle1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cle2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\utilisateur1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cle11.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\WZEND2.BMP" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\1.BMP" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\2.BMP" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\3.BMP" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{967B4E0D-AD0C-4609-AB67-0FA40C0206D8}" />
    <Service Include="{C0C07587-41A7-46C8-8FBD-3F9C8EBE2DDC}" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Sortie.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\entrée.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\libelle.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\mouvement_fournisseur1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\changer_.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Annuler.bmp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\medecin.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ville1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\forme1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Imprimante.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\last1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\next1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\previous1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\first1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\supprimer.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Login pharma.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Login pharma1.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\annuler3.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Sortir.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\enregistrer.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Vider1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Vider.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\imprmante1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Imprimante1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Imprimante2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\last 1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\next 1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\previous 1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\first 1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="My Project\app.manifest" />
    <None Include="Resources\annuler31.png" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App.ico" />
    <Content Include="DirectX.Capture.dll" />
    <Content Include="DShowNET.dll" />
    <Content Include="EZTW32.DLL" />
    <Content Include="FIRIDLLU.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Include="Resources\LogoPharma.bmp" />
    <None Include="Resources\LogoPharma2000Premium.png" />
    <None Include="Resources\Exemple Info Channel.gif" />
    <None Include="Resources\Logp BCB Dexther.jpg" />
    <None Include="Service References\ServiceOneKey\configuration91.svcinfo" />
    <None Include="Service References\ServiceOneKey\configuration.svcinfo" />
    <None Include="Service References\ServiceOneKey\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.vb</LastGenOutput>
    </None>
    <None Include="Resources\BarreDexther.PNG" />
    <None Include="Resources\dexter.jpg" />
    <None Include="Resources\LOGO NEXT PLUS.png" />
    <None Include="Resources\Login.png" />
    <None Include="Service References\ServiceOneKey\ServiceOne.disco" />
    <None Include="Resources\CodeABarre.jpg" />
    <None Include="Service References\ServiceBCB\configuration91.svcinfo" />
    <None Include="Service References\ServiceBCB\configuration.svcinfo" />
    <None Include="Service References\ServiceBCB\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.vb</LastGenOutput>
    </None>
    <None Include="Resources\Login 2.jpg" />
    <Content Include="TraceApp.dll" />
    <None Include="Resources\Windows-Media-Player.png" />
    <None Include="Resources\arrierePlanBureau.png" />
    <None Include="Resources\Excel-2010.png" />
    <None Include="Resources\Youtube.png" />
    <None Include="Resources\ajouterIconBureau.png" />
    <None Include="Resources\MS Word.jpg" />
    <None Include="Resources\calculatrice.jpg" />
    <None Include="Resources\google1.jpg" />
    <None Include="Resources\google.jpg" />
    <None Include="Resources\LinkedInAudit.jpeg" />
    <None Include="Resources\facebook.jpg" />
    <None Include="Resources\marker.png" />
    <None Include="Resources\pin.png" />
    <None Include="Resources\onekey.png" />
    <None Include="Resources\hommeR.png" />
    <None Include="Resources\homme-femme.png" />
    <None Include="Resources\femmeR.png" />
    <None Include="Resources\webcam.png" />
    <None Include="Resources\ZOOM.png" />
    <None Include="Resources\tourner.png" />
    <None Include="Resources\webcam.tif" />
    <None Include="Resources\production.png" />
    <None Include="Resources\preloader_transparent.gif" />
    <None Include="Resources\loading_transparent_4.gif" />
    <None Include="Resources\loading_transparent.gif" />
    <None Include="Resources\loading.gif" />
    <None Include="Resources\Image2.png" />
    <None Include="Resources\natures.png" />
    <None Include="Resources\Nature.tif" />
    <None Include="Resources\first 11.png" />
    <None Include="Resources\ajouter-ecommerce-panier-boutique-en-ligne-icone-6203-128.png" />
    <None Include="Resources\aliste.tif" />
    <None Include="Resources\aCommande.tif" />
    <None Include="Resources\afournisseur.tif" />
    <None Include="Resources\Medicament.bmp" />
    <None Include="Resources\drugstore_93955.jpg" />
    <None Include="Resources\amodifier.tif" />
    <None Include="Resources\areglement.tif" />
    <None Include="Resources\aremise.tif" />
    <None Include="Resources\aclient.tif" />
    <None Include="Resources\arecherche.tif" />
    <None Include="Resources\aimprimer.tif" />
    <None Include="Resources\afrigo.tif" />
    <None Include="Resources\afermer.tif" />
    <None Include="Resources\aequi.tif" />
    <None Include="Resources\aannuler.tif" />
    <None Include="Resources\aajouter.tif" />
    <None Include="Resources\avalider.tif" />
    <None Include="Resources\asupprimer.tif" />
    <None Include="Resources\equi.tif" />
    <None Include="Resources\identification.png" />
    <None Include="Resources\icone-membres-grand.png" />
    <None Include="Resources\cnam.gif" />
    <None Include="Resources\vente1.png" />
    <None Include="Resources\info-icone-7967-96.png" />
    <None Include="Resources\Image1.png" />
    <None Include="Resources\2873-53893.png" />
    <None Include="Resources\egal.tif" />
    <None Include="Resources\egale2123.tif" />
    <None Include="Resources\egale23.tif" />
    <None Include="Resources\egale2.tif" />
    <None Include="Resources\egalee.tif" />
    <None Include="Resources\egale123.tif" />
    <None Include="Resources\egale222.tif" />
    <None Include="Resources\remise11.tif" />
    <None Include="Resources\remise12.tif" />
    <None Include="Resources\egale.tif" />
    <None Include="Resources\cnam222.tif" />
    <None Include="Resources\femme enceinte3.tif" />
    <None Include="Resources\femme enceinte1.tif" />
    <None Include="Resources\femme enceinte.tif" />
    <None Include="Resources\femme enceinte4.tif" />
    <None Include="Resources\femme.tif" />
    <None Include="Resources\reglementt.tif" />
    <None Include="Resources\frigo112.tif" />
    <None Include="Resources\frigo 25.tif" />
    <None Include="Resources\frigo111.tif" />
    <None Include="Resources\reglement.tif" />
    <None Include="Resources\frigo1.tif" />
    <None Include="Resources\frigo.tif" />
    <None Include="Resources\remise1.tif" />
    <None Include="Resources\remise.tif" />
    <None Include="Resources\rech11.tif" />
    <None Include="Resources\rech.tif" />
    <None Include="Resources\rech1.tif" />
    <None Include="Resources\recherche.tif" />
    <None Include="Resources\cnam3.tif" />
    <None Include="Resources\cnam.tif" />
    <None Include="Resources\Annuler.tif" />
    <None Include="Resources\imprimer.tif" />
    <None Include="Resources\client.tif" />
    <None Include="Resources\valider.tif" />
    <None Include="Resources\Supprimer.tif" />
    <None Include="Resources\Fermer.tif" />
    <None Include="Resources\Ajouter.tif" />
    <None Include="Resources\preloader.gif" />
    <None Include="Resources\icon_mail.jpg" />
    <None Include="Resources\Login Pharma2000 rouge.jpg" />
    <None Include="Resources\Sauvegarde.png" />
    <None Include="Resources\people.png" />
    <None Include="Resources\spb.jpg" />
    <None Include="Resources\update.png" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadataStorage Include="Service References\ServiceBCB\" />
    <WCFMetadataStorage Include="Service References\ServiceOneKey\" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="AxShockwaveFlashObjects">
      <Guid>{D27CDB6B-AE6D-11CF-96B8-************}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>aximp</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
    <COMReference Include="ShockwaveFlashObjects">
      <Guid>{D27CDB6B-AE6D-11CF-96B8-************}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BLL\BusinessManagement\BusinessManagement.csproj">
      <Project>{f97f5909-3958-4dcc-ad2d-4d414e9a6bbc}</Project>
      <Name>BusinessManagement (BLL\BusinessManagement)</Name>
    </ProjectReference>
    <ProjectReference Include="..\BLL\ErrorManagement\ErrorManagement.csproj">
      <Project>{ba0be93e-8550-48f3-a1de-d93207956c41}</Project>
      <Name>ErrorManagement (BLL\ErrorManagement)</Name>
    </ProjectReference>
    <ProjectReference Include="..\BLL\Reporting\ReportingManagement.csproj">
      <Project>{f194144e-3099-4ce6-8f15-96a29367d910}</Project>
      <Name>ReportingManagement (BLL\ReportingManagement)</Name>
    </ProjectReference>
    <ProjectReference Include="..\BLL\StockManagement\StockManagement.csproj">
      <Project>{c5ecc9ac-fcc5-4012-86e6-da40e9c07ebd}</Project>
      <Name>StockManagement (BLL\StockManagement)</Name>
    </ProjectReference>
    <ProjectReference Include="..\DATA\BusinessManagement\BusinessManagement.csproj">
      <Project>{6005c983-e7e7-4e2f-8cf2-a3d87812621b}</Project>
      <Name>BusinessManagement (DATA\BusinessManagement)</Name>
    </ProjectReference>
    <ProjectReference Include="..\DATA\Reporting\ReportingManagement.csproj">
      <Project>{0c74776f-83eb-4547-98fb-36fc71c93cdf}</Project>
      <Name>ReportingManagement (DATA\ReportingManagement)</Name>
    </ProjectReference>
    <ProjectReference Include="..\DATA\StockManagement\StockManagement.csproj">
      <Project>{cb1323a3-4388-40d6-aa25-95f4a55681a7}</Project>
      <Name>StockManagement (DATA\StockManagement)</Name>
    </ProjectReference>
    <ProjectReference Include="..\Library\Library.csproj">
      <Project>{66d12d6f-433d-429c-98e7-ee10ebc64f6e}</Project>
      <Name>Library (Library\Library)</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>