using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PharmaModerne.Core.Interfaces;
using PharmaModerne.Shared.DTOs;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace PharmaModerne.UI.ViewModels.Clients
{
    /// <summary>
    /// ViewModel pour la liste des clients avec fonctionnalités complètes
    /// </summary>
    public partial class ClientListViewModel : ObservableObject
    {
        private readonly IClientService _clientService;
        private readonly IScannerService _scannerService;

        public ClientListViewModel(IClientService clientService, IScannerService scannerService)
        {
            _clientService = clientService;
            _scannerService = scannerService;
            
            // Initialisation des collections
            Clients = new ObservableCollection<ClientDto>();
            
            // Configuration initiale
            PageSize = 50;
            CurrentPage = 1;
            
            // Abonnement aux événements du scanner
            _scannerService.CodeScanned += OnCodeScanned;
            
            // Chargement initial
            _ = LoadClientsAsync();
        }

        #region Propriétés observables

        [ObservableProperty]
        private ObservableCollection<ClientDto> clients = new();

        [ObservableProperty]
        private ClientDto? selectedClient;

        [ObservableProperty]
        private string searchText = "";

        [ObservableProperty]
        private bool showActiveOnly = true;

        [ObservableProperty]
        private bool showWithDebtOnly = false;

        [ObservableProperty]
        private bool showRecentOnly = false;

        [ObservableProperty]
        private bool isScannerActive = false;

        [ObservableProperty]
        private string statusMessage = "Chargement...";

        [ObservableProperty]
        private int totalClients = 0;

        [ObservableProperty]
        private int currentPage = 1;

        [ObservableProperty]
        private int totalPages = 1;

        [ObservableProperty]
        private int pageSize = 50;

        [ObservableProperty]
        private int currentPageStart = 0;

        [ObservableProperty]
        private int currentPageEnd = 0;

        [ObservableProperty]
        private bool canGoToPreviousPage = false;

        [ObservableProperty]
        private bool canGoToNextPage = false;

        [ObservableProperty]
        private string? triPar = "Nom";

        [ObservableProperty]
        private bool triDescendant = false;

        #endregion

        #region Commandes

        [RelayCommand]
        private async Task LoadClients()
        {
            await LoadClientsAsync();
        }

        [RelayCommand]
        private async Task Search()
        {
            CurrentPage = 1;
            await LoadClientsAsync();
        }

        [RelayCommand]
        private async Task ActivateScanner()
        {
            try
            {
                if (IsScannerActive)
                {
                    await _scannerService.DeactivateAsync();
                }
                else
                {
                    await _scannerService.ActivateAsync();
                }
                IsScannerActive = _scannerService.IsActive;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur scanner : {ex.Message}", "Erreur", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task AdvancedSearch()
        {
            // TODO: Ouvrir la fenêtre de recherche avancée
            MessageBox.Show("Recherche avancée - À implémenter", "Fonctionnalité", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task Refresh()
        {
            await LoadClientsAsync();
        }

        [RelayCommand]
        private async Task AddClient()
        {
            // TODO: Ouvrir la fenêtre d'ajout de client
            MessageBox.Show("Nouveau client - À implémenter", "Fonctionnalité", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task ImportClients()
        {
            // TODO: Implémenter l'import
            MessageBox.Show("Import clients - À implémenter", "Fonctionnalité", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task ExportClients()
        {
            // TODO: Implémenter l'export
            MessageBox.Show("Export clients - À implémenter", "Fonctionnalité", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task ViewClient(ClientDto client)
        {
            if (client != null)
            {
                MessageBox.Show($"Voir client : {client.NomComplet}", "Client", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        [RelayCommand]
        private async Task EditClient(ClientDto client)
        {
            if (client != null)
            {
                MessageBox.Show($"Modifier client : {client.NomComplet}", "Client", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        [RelayCommand]
        private async Task ViewHistory(ClientDto client)
        {
            if (client != null)
            {
                MessageBox.Show($"Historique de : {client.NomComplet}", "Historique", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        [RelayCommand]
        private async Task ScanClient(ClientDto client)
        {
            if (client != null)
            {
                MessageBox.Show($"Scanner client : {client.CodeClient}", "Scanner", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        #endregion

        #region Pagination

        [RelayCommand]
        private async Task FirstPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage = 1;
                await LoadClientsAsync();
            }
        }

        [RelayCommand]
        private async Task PreviousPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                await LoadClientsAsync();
            }
        }

        [RelayCommand]
        private async Task NextPage()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
                await LoadClientsAsync();
            }
        }

        [RelayCommand]
        private async Task LastPage()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage = TotalPages;
                await LoadClientsAsync();
            }
        }

        #endregion

        #region Méthodes privées

        private async Task LoadClientsAsync()
        {
            try
            {
                StatusMessage = "Chargement des clients...";

                var searchDto = new ClientSearchDto
                {
                    CodeClient = SearchText.Contains("CLI") ? SearchText : null,
                    Nom = !SearchText.Contains("CLI") && !string.IsNullOrEmpty(SearchText) ? SearchText : null,
                    EstActif = ShowActiveOnly ? true : null,
                    Page = CurrentPage,
                    TailleePage = PageSize,
                    TriPar = TriPar,
                    TriDescendant = TriDescendant
                };

                var (clients, totalCount) = await _clientService.SearchClientsAsync(searchDto);

                Clients.Clear();
                foreach (var client in clients)
                {
                    Clients.Add(client);
                }

                TotalClients = totalCount;
                TotalPages = (int)Math.Ceiling((double)totalCount / PageSize);
                
                CurrentPageStart = (CurrentPage - 1) * PageSize + 1;
                CurrentPageEnd = Math.Min(CurrentPage * PageSize, TotalClients);
                
                CanGoToPreviousPage = CurrentPage > 1;
                CanGoToNextPage = CurrentPage < TotalPages;

                StatusMessage = $"{TotalClients} client(s) trouvé(s)";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Erreur : {ex.Message}";
                MessageBox.Show($"Erreur lors du chargement : {ex.Message}", "Erreur", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OnCodeScanned(object? sender, string code)
        {
            Application.Current?.Dispatcher?.Invoke(async () =>
            {
                try
                {
                    SearchText = code;
                    await Search();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Erreur lors du scan : {ex.Message}", "Erreur Scanner", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            });
        }

        #endregion

        #region Filtres

        partial void OnShowActiveOnlyChanged(bool value)
        {
            _ = LoadClientsAsync();
        }

        partial void OnShowWithDebtOnlyChanged(bool value)
        {
            _ = LoadClientsAsync();
        }

        partial void OnShowRecentOnlyChanged(bool value)
        {
            _ = LoadClientsAsync();
        }

        partial void OnPageSizeChanged(int value)
        {
            CurrentPage = 1;
            _ = LoadClientsAsync();
        }

        #endregion

        #region Nettoyage

        public void Dispose()
        {
            if (_scannerService != null)
            {
                _scannerService.CodeScanned -= OnCodeScanned;
            }
        }

        #endregion
    }
}
