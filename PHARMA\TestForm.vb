Public Class TestForm
    ' Variables pour la gestion du scanner de code à barres
    Private lastKeyPressTime As DateTime = DateTime.Now
    Private barcodeBuffer As String = ""
    Private Const BARCODE_TIMEOUT_MS As Integer = 100 ' Timeout pour détecter la fin de saisie du scanner

    Private Sub TestForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Test Scanner Code à Barres - Module Client"
        Me.Size = New Size(600, 400)
        Me.StartPosition = FormStartPosition.CenterScreen
        
        ' Ajouter des instructions
        lblInstructions.Text = "Testez votre scanner de code à barres dans le champ Code Client ci-dessous." & vbCrLf & _
                              "Le système détectera automatiquement les saisies rapides (scanner) vs manuelles." & vbCrLf & _
                              "Appuyez sur Enter après le scan pour valider."
        
        ' Configurer le champ de test
        tCodeClient.Focus()
    End Sub

    Private Sub tCodeClient_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tCodeClient.KeyPress
        Dim currentTime As DateTime = DateTime.Now
        
        ' Détecter si c'est une saisie rapide (probablement un scanner)
        If (currentTime - lastKeyPressTime).TotalMilliseconds < BARCODE_TIMEOUT_MS Then
            ' Saisie rapide détectée - probablement un scanner
            barcodeBuffer += e.KeyChar.ToString()
            lblStatus.Text = "Scanner détecté - Saisie rapide en cours..."
            lblStatus.ForeColor = Color.Blue
        Else
            ' Nouvelle saisie ou saisie manuelle
            barcodeBuffer = e.KeyChar.ToString()
            lblStatus.Text = "Saisie manuelle détectée"
            lblStatus.ForeColor = Color.Green
        End If
        
        lastKeyPressTime = currentTime
        
        ' Permettre uniquement les caractères alphanumériques et certains caractères spéciaux pour les codes à barres
        If Not (Char.IsLetterOrDigit(e.KeyChar) Or e.KeyChar = Chr(8) Or e.KeyChar = Chr(13) Or e.KeyChar = "-" Or e.KeyChar = "_" Or e.KeyChar = " ") Then
            e.Handled = True
            lblStatus.Text = "Caractère non autorisé pour code à barres"
            lblStatus.ForeColor = Color.Red
        End If
        
        ' Convertir en majuscules automatiquement
        If Char.IsLetter(e.KeyChar) Then
            e.KeyChar = Char.ToUpper(e.KeyChar)
        End If
        
        ' Si c'est Enter et qu'on a un buffer, traiter comme code à barres
        If e.KeyChar = Chr(13) And barcodeBuffer.Length > 3 Then
            ProcessBarcodeInput()
        End If
    End Sub

    Private Sub tCodeClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeClient.KeyUp
        If e.KeyCode = Keys.Enter And tCodeClient.Text <> "" Then
            ' Traitement spécial pour scanner de code à barres
            ProcessBarcodeInput()
            Exit Sub
        End If
    End Sub

    ' Méthode pour traiter la saisie du scanner de code à barres
    Private Sub ProcessBarcodeInput()
        Try
            ' Nettoyer le code scanné (supprimer les espaces en début/fin)
            tCodeClient.Text = tCodeClient.Text.Trim().ToUpper()
            
            ' Afficher les résultats
            lblResult.Text = "Code traité: " & tCodeClient.Text
            lblResult.ForeColor = Color.DarkGreen
            
            ' Déterminer le type de saisie
            If barcodeBuffer.Length > 5 And (DateTime.Now - lastKeyPressTime).TotalMilliseconds < 200 Then
                lblScanType.Text = "Type: Scanner de code à barres (saisie rapide)"
                lblScanType.ForeColor = Color.Blue
            Else
                lblScanType.Text = "Type: Saisie manuelle"
                lblScanType.ForeColor = Color.Green
            End If
            
            ' Afficher les détails
            lblDetails.Text = "Longueur: " & tCodeClient.Text.Length & " caractères" & vbCrLf & _
                             "Buffer: " & barcodeBuffer.Replace(Chr(13), "[ENTER]") & vbCrLf & _
                             "Heure: " & DateTime.Now.ToString("HH:mm:ss.fff")
            
            ' Log de l'utilisation du scanner
            Console.WriteLine("Code à barres traité: " + tCodeClient.Text)
            
            ' Ajouter à l'historique
            If lstHistory.Items.Count > 10 Then
                lstHistory.Items.RemoveAt(0)
            End If
            lstHistory.Items.Add(DateTime.Now.ToString("HH:mm:ss") & " - " & tCodeClient.Text)
            
            ' Sélectionner tout le texte pour la prochaine saisie
            tCodeClient.SelectAll()
            
        Catch ex As Exception
            lblResult.Text = "Erreur lors du traitement: " + ex.Message
            lblResult.ForeColor = Color.Red
        End Try
    End Sub

    Private Sub btnClear_Click(sender As Object, e As EventArgs) Handles btnClear.Click
        tCodeClient.Clear()
        lblResult.Text = ""
        lblStatus.Text = "Prêt pour nouveau scan"
        lblStatus.ForeColor = Color.Black
        lblScanType.Text = ""
        lblDetails.Text = ""
        barcodeBuffer = ""
        tCodeClient.Focus()
    End Sub

    Private Sub btnTest_Click(sender As Object, e As EventArgs) Handles btnTest.Click
        ' Simuler un code à barres
        tCodeClient.Text = "TEST123456789"
        ProcessBarcodeInput()
    End Sub
End Class
