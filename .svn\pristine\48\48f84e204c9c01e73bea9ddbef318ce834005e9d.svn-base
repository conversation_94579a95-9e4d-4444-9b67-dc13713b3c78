﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fProduction
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fProduction))
        Dim Margin1 As Neodynamic.WinControls.BarcodeProfessional.Margin = New Neodynamic.WinControls.BarcodeProfessional.Margin()
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bComposonts = New C1.Win.C1Input.C1Button()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.tRecherche = New C1.Win.C1Input.C1TextBox()
        Me.pModepaiment = New System.Windows.Forms.PictureBox()
        Me.LNumero = New System.Windows.Forms.Label()
        Me.lDateProduction = New System.Windows.Forms.Label()
        Me.LDate = New System.Windows.Forms.Label()
        Me.lNumeroProduction = New System.Windows.Forms.Label()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.tRemarque = New C1.Win.C1Input.C1TextBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.lOperateur = New System.Windows.Forms.Label()
        Me.bRecherche = New C1.Win.C1Input.C1Button()
        Me.bModifier = New C1.Win.C1Input.C1Button()
        Me.bImprimer = New C1.Win.C1Input.C1Button()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.gListeRecherche = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bSupprimer = New C1.Win.C1Input.C1Button()
        Me.bLast = New C1.Win.C1Input.C1Button()
        Me.bNext = New C1.Win.C1Input.C1Button()
        Me.bFirst = New C1.Win.C1Input.C1Button()
        Me.bPrevious = New C1.Win.C1Input.C1Button()
        Me.bAjouter = New C1.Win.C1Input.C1Button()
        Me.bConfirmer = New C1.Win.C1Input.C1Button()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.gArticles = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.CR = New Pharma2000Premium.EtatProduction()
        Me.bCodeABarre = New C1.Win.C1Input.C1Button()
        Me.BarcodeProfessional = New Neodynamic.WinControls.BarcodeProfessional.BarcodeProfessional()
        Me.Panel.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        CType(Me.tRecherche, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.pModepaiment, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox2.SuspendLayout()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tRemarque, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gListeRecherche, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.bCodeABarre)
        Me.Panel.Controls.Add(Me.bComposonts)
        Me.Panel.Controls.Add(Me.GroupBox3)
        Me.Panel.Controls.Add(Me.GroupBox2)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Controls.Add(Me.bRecherche)
        Me.Panel.Controls.Add(Me.bModifier)
        Me.Panel.Controls.Add(Me.bImprimer)
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.gListeRecherche)
        Me.Panel.Controls.Add(Me.bSupprimer)
        Me.Panel.Controls.Add(Me.bLast)
        Me.Panel.Controls.Add(Me.bNext)
        Me.Panel.Controls.Add(Me.bFirst)
        Me.Panel.Controls.Add(Me.bPrevious)
        Me.Panel.Controls.Add(Me.bAjouter)
        Me.Panel.Controls.Add(Me.bConfirmer)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.gArticles)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1230, 576)
        Me.Panel.TabIndex = 4
        '
        'bComposonts
        '
        Me.bComposonts.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bComposonts.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bComposonts.Image = Global.Pharma2000Premium.My.Resources.Resources.aCommande
        Me.bComposonts.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bComposonts.Location = New System.Drawing.Point(529, 519)
        Me.bComposonts.Name = "bComposonts"
        Me.bComposonts.Size = New System.Drawing.Size(91, 37)
        Me.bComposonts.TabIndex = 85
        Me.bComposonts.Text = "Composants " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "         F1"
        Me.bComposonts.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bComposonts.UseVisualStyleBackColor = True
        Me.bComposonts.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.tRecherche)
        Me.GroupBox3.Controls.Add(Me.pModepaiment)
        Me.GroupBox3.Controls.Add(Me.LNumero)
        Me.GroupBox3.Controls.Add(Me.lDateProduction)
        Me.GroupBox3.Controls.Add(Me.LDate)
        Me.GroupBox3.Controls.Add(Me.lNumeroProduction)
        Me.GroupBox3.Location = New System.Drawing.Point(237, 8)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(215, 103)
        Me.GroupBox3.TabIndex = 84
        Me.GroupBox3.TabStop = False
        Me.GroupBox3.Text = "        Identification"
        '
        'tRecherche
        '
        Me.tRecherche.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tRecherche.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tRecherche.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tRecherche.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tRecherche.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tRecherche.Location = New System.Drawing.Point(80, 32)
        Me.tRecherche.Name = "tRecherche"
        Me.tRecherche.Size = New System.Drawing.Size(113, 18)
        Me.tRecherche.TabIndex = 92
        Me.tRecherche.Tag = Nothing
        Me.tRecherche.Visible = False
        Me.tRecherche.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tRecherche.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'pModepaiment
        '
        Me.pModepaiment.Image = Global.Pharma2000Premium.My.Resources.Resources.identification
        Me.pModepaiment.Location = New System.Drawing.Point(6, 0)
        Me.pModepaiment.Name = "pModepaiment"
        Me.pModepaiment.Size = New System.Drawing.Size(23, 20)
        Me.pModepaiment.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.pModepaiment.TabIndex = 91
        Me.pModepaiment.TabStop = False
        '
        'LNumero
        '
        Me.LNumero.AutoSize = True
        Me.LNumero.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LNumero.Location = New System.Drawing.Point(23, 33)
        Me.LNumero.Name = "LNumero"
        Me.LNumero.Size = New System.Drawing.Size(50, 13)
        Me.LNumero.TabIndex = 10
        Me.LNumero.Text = "Numéro :"
        '
        'lDateProduction
        '
        Me.lDateProduction.BackColor = System.Drawing.Color.Transparent
        Me.lDateProduction.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDateProduction.Location = New System.Drawing.Point(77, 63)
        Me.lDateProduction.Name = "lDateProduction"
        Me.lDateProduction.Size = New System.Drawing.Size(128, 19)
        Me.lDateProduction.TabIndex = 34
        Me.lDateProduction.Text = "Date"
        '
        'LDate
        '
        Me.LDate.AutoSize = True
        Me.LDate.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LDate.Location = New System.Drawing.Point(37, 63)
        Me.LDate.Name = "LDate"
        Me.LDate.Size = New System.Drawing.Size(36, 13)
        Me.LDate.TabIndex = 11
        Me.LDate.Text = "Date :"
        '
        'lNumeroProduction
        '
        Me.lNumeroProduction.AutoSize = True
        Me.lNumeroProduction.BackColor = System.Drawing.Color.Transparent
        Me.lNumeroProduction.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNumeroProduction.Location = New System.Drawing.Point(77, 33)
        Me.lNumeroProduction.Name = "lNumeroProduction"
        Me.lNumeroProduction.Size = New System.Drawing.Size(59, 13)
        Me.lNumeroProduction.TabIndex = 33
        Me.lNumeroProduction.Text = "-------------"
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.PictureBox2)
        Me.GroupBox2.Controls.Add(Me.Label3)
        Me.GroupBox2.Location = New System.Drawing.Point(12, 8)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(219, 103)
        Me.GroupBox2.TabIndex = 83
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "        Production"
        '
        'PictureBox2
        '
        Me.PictureBox2.Location = New System.Drawing.Point(7, 0)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(23, 20)
        Me.PictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox2.TabIndex = 93
        Me.PictureBox2.TabStop = False
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Cursor = System.Windows.Forms.Cursors.Default
        Me.Label3.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label3.Location = New System.Drawing.Point(6, 29)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(197, 42)
        Me.Label3.TabIndex = 11
        Me.Label3.Text = "Production"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.PictureBox1)
        Me.GroupBox1.Controls.Add(Me.tRemarque)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Controls.Add(Me.lOperateur)
        Me.GroupBox1.Location = New System.Drawing.Point(458, 8)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(529, 103)
        Me.GroupBox1.TabIndex = 82
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "        Préparateur"
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Pharma2000Premium.My.Resources.Resources.medecin
        Me.PictureBox1.Location = New System.Drawing.Point(6, 0)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(23, 20)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox1.TabIndex = 92
        Me.PictureBox1.TabStop = False
        '
        'tRemarque
        '
        Me.tRemarque.AutoSize = False
        Me.tRemarque.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tRemarque.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tRemarque.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tRemarque.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tRemarque.Location = New System.Drawing.Point(106, 61)
        Me.tRemarque.Name = "tRemarque"
        Me.tRemarque.Size = New System.Drawing.Size(417, 19)
        Me.tRemarque.TabIndex = 39
        Me.tRemarque.Tag = Nothing
        Me.tRemarque.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tRemarque.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(31, 64)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(62, 13)
        Me.Label2.TabIndex = 38
        Me.Label2.Text = "Remarque :"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(25, 29)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(68, 13)
        Me.Label1.TabIndex = 37
        Me.Label1.Text = "Préparateur :"
        '
        'lOperateur
        '
        Me.lOperateur.AutoSize = True
        Me.lOperateur.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lOperateur.Location = New System.Drawing.Point(109, 31)
        Me.lOperateur.Name = "lOperateur"
        Me.lOperateur.Size = New System.Drawing.Size(10, 13)
        Me.lOperateur.TabIndex = 36
        Me.lOperateur.Text = "-"
        '
        'bRecherche
        '
        Me.bRecherche.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bRecherche.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bRecherche.Image = Global.Pharma2000Premium.My.Resources.Resources.arecherche
        Me.bRecherche.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bRecherche.Location = New System.Drawing.Point(626, 519)
        Me.bRecherche.Name = "bRecherche"
        Me.bRecherche.Size = New System.Drawing.Size(80, 37)
        Me.bRecherche.TabIndex = 81
        Me.bRecherche.Text = "Recherche " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "     F4"
        Me.bRecherche.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bRecherche.UseVisualStyleBackColor = True
        Me.bRecherche.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.bRecherche.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bModifier
        '
        Me.bModifier.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bModifier.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bModifier.Image = Global.Pharma2000Premium.My.Resources.Resources.amodifier
        Me.bModifier.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bModifier.Location = New System.Drawing.Point(712, 519)
        Me.bModifier.Name = "bModifier"
        Me.bModifier.Size = New System.Drawing.Size(80, 37)
        Me.bModifier.TabIndex = 80
        Me.bModifier.Text = "Modifier " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "     F8"
        Me.bModifier.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bModifier.UseVisualStyleBackColor = True
        Me.bModifier.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.bModifier.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bImprimer
        '
        Me.bImprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.aimprimer
        Me.bImprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bImprimer.Location = New System.Drawing.Point(797, 519)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(80, 37)
        Me.bImprimer.TabIndex = 79
        Me.bImprimer.Text = "Imprimer " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "    F11"
        Me.bImprimer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.afermer
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(1139, 519)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(80, 37)
        Me.bQuitter.TabIndex = 72
        Me.bQuitter.Text = "Fermer        " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "  F12"
        Me.bQuitter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gListeRecherche
        '
        Me.gListeRecherche.AllowUpdate = False
        Me.gListeRecherche.AlternatingRows = True
        Me.gListeRecherche.CaptionHeight = 17
        Me.gListeRecherche.GroupByCaption = "Drag a column header here to group by that column"
        Me.gListeRecherche.Images.Add(CType(resources.GetObject("gListeRecherche.Images"), System.Drawing.Image))
        Me.gListeRecherche.LinesPerRow = 2
        Me.gListeRecherche.Location = New System.Drawing.Point(114, 162)
        Me.gListeRecherche.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gListeRecherche.Name = "gListeRecherche"
        Me.gListeRecherche.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gListeRecherche.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gListeRecherche.PreviewInfo.ZoomFactor = 75.0R
        Me.gListeRecherche.PrintInfo.PageSettings = CType(resources.GetObject("gListeRecherche.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gListeRecherche.RowHeight = 15
        Me.gListeRecherche.Size = New System.Drawing.Size(610, 209)
        Me.gListeRecherche.TabIndex = 49
        Me.gListeRecherche.Text = "C1TrueDBGrid1"
        Me.gListeRecherche.Visible = False
        Me.gListeRecherche.PropBag = resources.GetString("gListeRecherche.PropBag")
        '
        'bSupprimer
        '
        Me.bSupprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bSupprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.asupprimer
        Me.bSupprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSupprimer.Location = New System.Drawing.Point(443, 519)
        Me.bSupprimer.Name = "bSupprimer"
        Me.bSupprimer.Size = New System.Drawing.Size(80, 37)
        Me.bSupprimer.TabIndex = 61
        Me.bSupprimer.Text = "Supprimer       " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "     F7"
        Me.bSupprimer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSupprimer.UseVisualStyleBackColor = True
        Me.bSupprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bLast
        '
        Me.bLast.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bLast.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bLast.Image = Global.Pharma2000Premium.My.Resources.Resources.last_dounloaded
        Me.bLast.Location = New System.Drawing.Point(197, 532)
        Me.bLast.Name = "bLast"
        Me.bLast.Size = New System.Drawing.Size(52, 24)
        Me.bLast.TabIndex = 11
        Me.bLast.UseVisualStyleBackColor = True
        Me.bLast.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bNext
        '
        Me.bNext.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bNext.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bNext.Image = Global.Pharma2000Premium.My.Resources.Resources.next_downloaded
        Me.bNext.Location = New System.Drawing.Point(142, 532)
        Me.bNext.Name = "bNext"
        Me.bNext.Size = New System.Drawing.Size(52, 24)
        Me.bNext.TabIndex = 10
        Me.bNext.UseVisualStyleBackColor = True
        Me.bNext.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bFirst
        '
        Me.bFirst.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bFirst.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bFirst.Image = Global.Pharma2000Premium.My.Resources.Resources.first_dounloaded
        Me.bFirst.Location = New System.Drawing.Point(19, 532)
        Me.bFirst.Name = "bFirst"
        Me.bFirst.Size = New System.Drawing.Size(52, 24)
        Me.bFirst.TabIndex = 8
        Me.bFirst.UseVisualStyleBackColor = True
        Me.bFirst.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bPrevious
        '
        Me.bPrevious.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bPrevious.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bPrevious.Image = Global.Pharma2000Premium.My.Resources.Resources.previous_downloaded
        Me.bPrevious.Location = New System.Drawing.Point(74, 532)
        Me.bPrevious.Name = "bPrevious"
        Me.bPrevious.Size = New System.Drawing.Size(52, 24)
        Me.bPrevious.TabIndex = 9
        Me.bPrevious.UseVisualStyleBackColor = True
        Me.bPrevious.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouter
        '
        Me.bAjouter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouter.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouter.Image = Global.Pharma2000Premium.My.Resources.Resources.aajouter
        Me.bAjouter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouter.Location = New System.Drawing.Point(883, 519)
        Me.bAjouter.Name = "bAjouter"
        Me.bAjouter.Size = New System.Drawing.Size(80, 37)
        Me.bAjouter.TabIndex = 5
        Me.bAjouter.Text = "Ajouter        " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "     F5"
        Me.bAjouter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouter.UseVisualStyleBackColor = True
        Me.bAjouter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bConfirmer
        '
        Me.bConfirmer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bConfirmer.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmer.Image = Global.Pharma2000Premium.My.Resources.Resources.avalider
        Me.bConfirmer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmer.Location = New System.Drawing.Point(969, 519)
        Me.bConfirmer.Name = "bConfirmer"
        Me.bConfirmer.Size = New System.Drawing.Size(80, 37)
        Me.bConfirmer.TabIndex = 6
        Me.bConfirmer.Text = "Confirmer" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "      F3"
        Me.bConfirmer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bConfirmer.UseVisualStyleBackColor = True
        Me.bConfirmer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.aannuler
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(1054, 519)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(80, 37)
        Me.bAnnuler.TabIndex = 7
        Me.bAnnuler.Text = "Annuler  " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "    F10"
        Me.bAnnuler.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gArticles
        '
        Me.gArticles.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gArticles.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gArticles.GroupByCaption = "Drag a column header here to group by that column"
        Me.gArticles.Images.Add(CType(resources.GetObject("gArticles.Images"), System.Drawing.Image))
        Me.gArticles.LinesPerRow = 2
        Me.gArticles.Location = New System.Drawing.Point(12, 117)
        Me.gArticles.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
        Me.gArticles.Name = "gArticles"
        Me.gArticles.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gArticles.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gArticles.PreviewInfo.ZoomFactor = 75.0R
        Me.gArticles.PrintInfo.PageSettings = CType(resources.GetObject("gArticles.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gArticles.Size = New System.Drawing.Size(1206, 361)
        Me.gArticles.TabIndex = 2
        Me.gArticles.Text = "C1TrueDBGrid1"
        Me.gArticles.PropBag = resources.GetString("gArticles.PropBag")
        '
        'bCodeABarre
        '
        Me.bCodeABarre.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bCodeABarre.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bCodeABarre.Image = Global.Pharma2000Premium.My.Resources.Resources.CodeABarre
        Me.bCodeABarre.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bCodeABarre.Location = New System.Drawing.Point(324, 518)
        Me.bCodeABarre.Name = "bCodeABarre"
        Me.bCodeABarre.Size = New System.Drawing.Size(113, 38)
        Me.bCodeABarre.TabIndex = 100
        Me.bCodeABarre.Text = "Impression" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "Etiquette    " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        Me.bCodeABarre.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bCodeABarre.UseVisualStyleBackColor = True
        Me.bCodeABarre.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'BarcodeProfessional
        '
        Me.BarcodeProfessional.BackColor = System.Drawing.Color.White
        Margin1.Bottom = 0.0R
        Margin1.Left = 0.0R
        Margin1.Right = 0.0R
        Margin1.Top = 0.0R
        Me.BarcodeProfessional.BarcodePadding = Margin1
        Me.BarcodeProfessional.BarHeight = 0.2R
        Me.BarcodeProfessional.BarRatio = 2.0R
        Me.BarcodeProfessional.BarWidth = 0.010416666666666666R
        Me.BarcodeProfessional.BarWidthAdjustment = 0.0R
        Me.BarcodeProfessional.BorderWidth = 0.0R
        Me.BarcodeProfessional.Code = "1234567890"
        Me.BarcodeProfessional.GuardBarHeight = 0.25R
        Me.BarcodeProfessional.HibcUseIsoIec15434Encoding = False
        Me.BarcodeProfessional.IsbnSupplementCode = "0"
        Me.BarcodeProfessional.Location = New System.Drawing.Point(76, 6)
        Me.BarcodeProfessional.MICRCharHeight = 0.0R
        Me.BarcodeProfessional.MICRCharSpacing = ""
        Me.BarcodeProfessional.MICRCharWidths = ""
        Me.BarcodeProfessional.Name = "BarcodeProfessional"
        Me.BarcodeProfessional.Pdf417AspectRatio = 0.0R
        Me.BarcodeProfessional.PlanetHeightShortBar = 0.1R
        Me.BarcodeProfessional.PlanetHeightTallBar = 0.2R
        Me.BarcodeProfessional.PostnetHeightShortBar = 0.1R
        Me.BarcodeProfessional.PostnetHeightTallBar = 0.2R
        Me.BarcodeProfessional.QuietZoneWidth = 0.1R
        Me.BarcodeProfessional.SnapsToDevicePixels = False
        Me.BarcodeProfessional.TabIndex = 0
        Me.BarcodeProfessional.TextFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.BarcodeProfessional.TextForeColor = System.Drawing.Color.Black
        Me.BarcodeProfessional.TiffCompression = Neodynamic.WinControls.BarcodeProfessional.TiffCompression.LZW
        '
        'fProduction
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1230, 576)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fProduction"
        Me.Text = "fProduction"
        Me.Panel.ResumeLayout(False)
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        CType(Me.tRecherche, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.pModepaiment, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tRemarque, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gListeRecherche, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents gListeRecherche As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bSupprimer As C1.Win.C1Input.C1Button
    Friend WithEvents bLast As C1.Win.C1Input.C1Button
    Friend WithEvents bNext As C1.Win.C1Input.C1Button
    Friend WithEvents lOperateur As System.Windows.Forms.Label
    Friend WithEvents lDateProduction As System.Windows.Forms.Label
    Friend WithEvents LNumero As System.Windows.Forms.Label
    Friend WithEvents LDate As System.Windows.Forms.Label
    Friend WithEvents lNumeroProduction As System.Windows.Forms.Label
    Friend WithEvents bFirst As C1.Win.C1Input.C1Button
    Friend WithEvents bPrevious As C1.Win.C1Input.C1Button
    Friend WithEvents bAjouter As C1.Win.C1Input.C1Button
    Friend WithEvents bConfirmer As C1.Win.C1Input.C1Button
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents gArticles As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents tRemarque As C1.Win.C1Input.C1TextBox
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
    Friend WithEvents bModifier As C1.Win.C1Input.C1Button
    Friend WithEvents bRecherche As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents pModepaiment As System.Windows.Forms.PictureBox
    Friend WithEvents PictureBox2 As System.Windows.Forms.PictureBox
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents tRecherche As C1.Win.C1Input.C1TextBox
    Friend WithEvents CR As Pharma2000Premium.EtatProduction
    Friend WithEvents bComposonts As C1.Win.C1Input.C1Button
    Friend WithEvents bCodeABarre As C1.Win.C1Input.C1Button
    Private WithEvents BarcodeProfessional As Neodynamic.WinControls.BarcodeProfessional.BarcodeProfessional
End Class
