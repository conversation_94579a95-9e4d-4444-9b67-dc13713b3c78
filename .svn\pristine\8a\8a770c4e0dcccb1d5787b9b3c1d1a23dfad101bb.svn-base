﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fMouvementDesFournisseurs
    Dim StrSQL As String = ""

    Dim cmdMouvement As New SqlCommand
    Dim daMouvement As New SqlDataAdapter
    Dim dsMouvement As New DataSet

    Dim CmdCalcul As New SqlCommand   ' juste pour calculer le solde du fournisseur

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub init()
        'chargement des noms Clients
        StrSQL = "SELECT DISTINCT CodeFournisseur,NomFournisseur FROM FOURNISSEUR WHERE Supprimer = 0 ORDER BY NomFournisseur ASC"
        cmdMouvement.Connection = ConnectionServeur
        cmdMouvement.CommandText = StrSQL
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "FOURNISSEUR")
        cmbFournisseur.DataSource = dsMouvement.Tables("FOURNISSEUR")
        cmbFournisseur.ValueMember = "CodeFournisseur"
        cmbFournisseur.DisplayMember = "NomFournisseur"
        cmbFournisseur.ColumnHeaders = False
        cmbFournisseur.Splits(0).DisplayColumns("CodeFournisseur").Visible = False
        cmbFournisseur.Splits(0).DisplayColumns("NomFournisseur").Width = 10
        cmbFournisseur.ExtendRightColumn = True

        'dtpDebut.Text = "01/" + Date.Today.Month.ToString.PadLeft(2, "0") + "/" + Date.Now.Year.ToString

        If DateMigration = Nothing Then
            dtpDebut.Text = "01/" + Date.Today.Month.ToString.PadLeft(2, "0") + "/" + Date.Now.Year.ToString
        Else
            dtpDebut.Text = DateMigration
        End If

        dtpFin.Text = NombreDesJoursDuMois(Date.Today.Month, Date.Today.Year).ToString + "/" + Date.Today.Month.ToString.PadLeft(2, "0") + "/" + Date.Now.Year.ToString

        'If Date.Today.Month <> 2 Then
        '    dtpFin.Text = "30/" + Date.Today.Month.ToString + "/" + Date.Now.Year.ToString
        'Else
        '    dtpFin.Text = "28/" + Date.Today.Month.ToString + "/" + Date.Now.Year.ToString
        'End If

        dtpDebut.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDebut.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpFin.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpFin.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        If ModeADMIN = "ADMIN" Then
            gbTotal.Visible = True
        Else
            gbTotal.Visible = False
        End If

        AfficherMouvement()

    End Sub

    Public Sub AfficherMouvement()
        Dim I As Integer
        Dim Cond As String = "1=1"
        Dim TotalDebit As Double = 0.0
        Dim TotalCredit As Double = 0.0
        Dim TotalRemise As Double = 0.0

        Dim StrSQLSolde As String = ""
        Dim Somme_Facture As Double = 0.0
        Dim Somme_Reglement As Double = 0.0
        Dim difference As Double = 0.0


        If (dsMouvement.Tables.IndexOf("MOUVEMENT_FOURNISSEUR") > -1) Then
            dsMouvement.Tables("MOUVEMENT_FOURNISSEUR").Clear()
        End If

        'Composer la condition de la requête    
        If cmbFournisseur.Text <> "" Then
            Cond += " AND codefournisseur ='" + cmbFournisseur.SelectedValue + "'"
        End If

        If dtpDebut.Text <> "" And dtpDebut.Text.Length = 10 Then
            Cond += " AND (CONVERT(DATE, [date], 103) >'" + dtpDebut.Text + "' OR CONVERT(DATE, [date], 103)='" + dtpDebut.Text + "')"
        End If

        If dtpFin.Text <> "" And dtpFin.Text.Length = 10 Then
            Cond += " AND (CONVERT(DATE, [date], 103) <'" + dtpFin.Text + "' OR CONVERT(DATE, [date], 103)='" + dtpFin.Text + "')"
        End If

        If cmbFournisseur.Text = "" Then
            cmdMouvement.CommandText = "SELECT TOP(0) * FROM Vue_MouvementsDesFournisseurs WHERE " + Cond + _
            "Order by Date"
        Else
            cmdMouvement.CommandText = "SELECT * FROM Vue_MouvementsDesFournisseurs WHERE " + Cond + _
            "Order by Date"
        End If

        cmdMouvement.Connection = ConnectionServeur
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "MOUVEMENT_FOURNISSEUR")

        With gFournisseur
            .Columns.Clear()
            .DataSource = dsMouvement
            .DataMember = "MOUVEMENT_FOURNISSEUR"
            .Rebind(False)
            .Columns("Type").Caption = "Type"
            .Columns("NumeroFacture").Caption = "Numéro Facture"
            .Columns("Date").Caption = "Date"
            .Columns("Debit").Caption = "Débit"
            .Columns("Credit").Caption = "Crédit"
            .Columns("MP").Caption = "MP"
            .Columns("DateEcheance").Caption = "Date Echéance"
            .Columns("Libelle").Caption = "Libellé"
            .Columns("Numero").Caption = "Numéro"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Type").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Libelle").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Debit").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Credit").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("Type").Width = 100
            .Splits(0).DisplayColumns("NumeroFacture").Width = 100
            .Splits(0).DisplayColumns("Date").Width = 100
            .Splits(0).DisplayColumns("Debit").Width = 100
            .Splits(0).DisplayColumns("Credit").Width = 100
            .Splits(0).DisplayColumns("MP").Width = 100
            .Splits(0).DisplayColumns("DateEcheance").Width = 140
            .Splits(0).DisplayColumns("Libelle").Width = 150
            .Splits(0).DisplayColumns("CodeFournisseur").Visible = False
            .Splits(0).DisplayColumns("Numero").Width = 100

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gFournisseur)
        End With


        For I = 0 To dsMouvement.Tables("MOUVEMENT_FOURNISSEUR").Rows.Count - 1
            TotalCredit = TotalCredit + dsMouvement.Tables("MOUVEMENT_FOURNISSEUR").Rows(I).Item("Credit")
            TotalDebit = TotalDebit + dsMouvement.Tables("MOUVEMENT_FOURNISSEUR").Rows(I).Item("Debit")
            If dsMouvement.Tables("MOUVEMENT_FOURNISSEUR").Rows(I).Item("MP").ToString = "REMISE" Then
                TotalRemise = TotalRemise + dsMouvement.Tables("MOUVEMENT_FOURNISSEUR").Rows(I).Item("Credit")
            End If
        Next
        lTotalCredit.Text = Math.Round(TotalCredit, 3).ToString("### ### ##0.000")
        lTotalDebit.Text = Math.Round(TotalDebit, 3).ToString("### ### ##0.000")
        lTotalRemise.Text = Math.Round(TotalRemise, 3).ToString("### ### ##0.000")

        lDifference.Text = Math.Round(TotalDebit - TotalCredit, 3).ToString("### ### ##0.000")

        If RecupererValeurExecuteScalaire("SoldeInitial", "FOURNISSEUR", "CodeFournisseur", cmbFournisseur.SelectedValue) <> "" Then
            lSoldeInitial.Text = Format(CDbl(RecupererValeurExecuteScalaire("SoldeInitial", "FOURNISSEUR", "CodeFournisseur", cmbFournisseur.SelectedValue)), "### ### ##0.000")
        Else
            lSoldeInitial.Text = "0"
        End If
        lDateInitial.Text = RecupererValeurExecuteScalaire("DateInitiale", "FOURNISSEUR", "CodeFournisseur", cmbFournisseur.SelectedValue)

        'calcul du solde client en retranchant la somme des montants des règlements de la somme des montants des ventes 
        StrSQLSolde = "SELECT SUM(TotalTTC) FROM ACHAT WHERE CodeFournisseur =" + Quote(cmbFournisseur.SelectedValue)
        ' " AND CONVERT(DATE, [date], 103) >= " + Quote(dtpDebut.Text) + " AND CONVERT(DATE, [date], 103) <= " + Quote(dtpFin.Text)

        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQLSolde

        Try
            Somme_Facture = CmdCalcul.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        StrSQLSolde = "SELECT SUM(Montant) FROM REGLEMENT_FOURNISSEUR WHERE CodeFournisseur =" + Quote(cmbFournisseur.SelectedValue)
        ' " AND CONVERT(DATE, [date], 103) >= " + Quote(dtpDebut.Text) + " AND CONVERT(DATE, [date], 103) <= " + Quote(dtpFin.Text)

        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQLSolde

        Try
            Somme_Reglement = CmdCalcul.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try


        difference = Somme_Facture - Somme_Reglement + CType(lSoldeInitial.Text, Decimal)
        lSolde.Text = difference.ToString("### ### ##0.000")


        'cmbFournisseur.Focus()
    End Sub

    Private Sub cmbFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbFournisseur.KeyUp
        If e.KeyCode = Keys.Enter Then
            If cmbFournisseur.Text = "" Then
                MsgBox("Veillez choisir un Fournisseur !", MsgBoxStyle.Critical, "Erreur")
                cmbFournisseur.Focus()
                Exit Sub
            Else
                cmbFournisseur.Text = cmbFournisseur.WillChangeToText
                AfficherMouvement()
                dtpDebut.Focus()
            End If
        Else
            cmbFournisseur.OpenCombo()
        End If
    End Sub

    Private Sub cmbFournisseur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbFournisseur.TextChanged

    End Sub

    Private Sub dtpFin_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles dtpFin.KeyPress

    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherMouvement()
            cmbFournisseur.Focus()
        End If
    End Sub

    Private Sub dtpFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpFin.TextChanged
       
    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherMouvement()
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDebut.TextChanged
        
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim CondCrystal As String = ""
        Dim Somme_Echeance As Double = 0.0
        Dim StrSQLSolde As String = ""

        CondCrystal = "1=1 "

        If cmbFournisseur.Text = "" Then
            MsgBox("Veillez choisir un Fournisseur !", MsgBoxStyle.Critical, "Erreur")
            cmbFournisseur.Focus()
            Exit Sub
        End If

        If dsMouvement.Tables("MOUVEMENT_FOURNISSEUR").Rows.Count = 0 Then
            MsgBox("Liste vide !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        If cmbFournisseur.Text <> "" Then
            CondCrystal = CondCrystal + " AND {Vue_MouvementsDesFournisseurs.CodeFournisseur} = '" + cmbFournisseur.SelectedValue + "'"
        End If

        If dtpDebut.Text <> "" Then
            CondCrystal = CondCrystal + " AND (Date({Vue_MouvementsDesFournisseurs.Date}) > Date('" + dtpDebut.Text + "') OR date({Vue_MouvementsDesFournisseurs.Date}) = Date('" + dtpDebut.Text + "') )"
        End If

        If dtpFin.Text <> "" Then
            CondCrystal = CondCrystal + " AND (Date({Vue_MouvementsDesFournisseurs.Date}) < Date('" + dtpFin.Text + "') OR Date({Vue_MouvementsDesFournisseurs.Date}) = Date('" + dtpFin.Text + "'))"
        End If


        ' récupération des montants des règlement antidaté nn encaissé SOLDE EN COURS
        StrSQLSolde = "SELECT SUM(Montant) " + _
                      "FROM REGLEMENT_FOURNISSEUR LEFT OUTER JOIN NATURE_REGLEMENT ON " + _
                      " REGLEMENT_FOURNISSEUR.CodeNatureReglement=NATURE_REGLEMENT.CodeNatureReglement" + _
                      "WHERE CodeClient =" + _
                      Quote(cmbFournisseur.SelectedValue) + _
                      " AND DateEcheance > '" + System.DateTime.Now.Date.ToString + _
                      "' AND NATURE_REGLEMENT.LibelleNatureReglement='CHEQUE' "

        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQLSolde

        Try
            Somme_Echeance = CmdCalcul.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try



        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression du Mouvement du fournisseur" Then
                num = I
            End If
        Next

        CR.FileName = Application.StartupPath + "\EtatMouvementDuFournisseur.rpt"
        CR.SetParameterValue("Fournisseur", cmbFournisseur.Text)
        CR.SetParameterValue("Debut", dtpDebut.Text)
        CR.SetParameterValue("Fin", dtpFin.Text)
        CR.SetParameterValue("SoldeActuel", lSolde.Text)
        CR.SetParameterValue("Encours", Somme_Echeance)


        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent
        CR.RecordSelectionFormula = CondCrystal
        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression du Mouvement du fournisseur"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub gFournisseur_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gFournisseur.Click

    End Sub

    Private Sub gFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gFournisseur.KeyUp
        If e.KeyCode = Keys.F1 And gFournisseur(gFournisseur.Row, "Type") = "Achat" Then
            Dim MyAchatAffiche As New fAchatJusteAffichage
            MyAchatAffiche.NumeroAchat = gFournisseur(gFournisseur.Row, "Numero")
            MyAchatAffiche.ShowDialog()
            MyAchatAffiche.Close()
            MyAchatAffiche.Dispose()

        End If
    End Sub
End Class