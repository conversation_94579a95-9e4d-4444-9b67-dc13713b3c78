//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class EMPRUNT_DETAILS
    {
        public string NumeroEmprunt { get; set; }
        public string CodeArticle { get; set; }
        public string NumeroLotArticle { get; set; }
        public string CodeABarre { get; set; }
        public string Designation { get; set; }
        public int CodeForme { get; set; }
        public int Qte { get; set; }
        public decimal PrixAchatHT { get; set; }
        public decimal TotalAchatHT { get; set; }
        public decimal PrixAchatTTC { get; set; }
        public decimal TotalAchatTTC { get; set; }
        public decimal TVA { get; set; }
        public decimal TotalTVA { get; set; }
        public Nullable<System.DateTime> DatePeremption { get; set; }
        public int Stock { get; set; }
    
        public virtual EMPRUNT EMPRUNT { get; set; }
        public virtual FORME_ARTICLE FORME_ARTICLE { get; set; }
        public virtual LOT_ARTICLE LOT_ARTICLE { get; set; }
    }
}
