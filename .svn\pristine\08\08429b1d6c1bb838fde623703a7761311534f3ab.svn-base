﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fListePreparation

    Dim cmdArticle As New SqlCommand
    Dim daArticle As New SqlDataAdapter
    Dim dsArticle As New DataSet

    Dim x As Integer

    
    Dim TotalVenteHT As Double = 0.0
    Dim TotalVenteTTC As Double = 0.0

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        Try
            If argument = "117" And bRechercher.Enabled = True Then
                bRechercher_Click(sender, e)
                Exit Sub
            End If
            If argument = "118" And bSuprimerArticle.Enabled = True Then
                bSuprimerArticle_Click(sender, e)
                Exit Sub
            End If
            If argument = "119" And bModifierArticle.Enabled = True Then
                bModifierArticle_Click(sender, e)
                Exit Sub
            End If
            'If argument = "120" And bImprimer.Enabled = True Then
            '    bImprimer_Click(sender, e)
            'End If
            If argument = "123" And bFermer.Enabled = True Then
                bQuitter_Click(sender, e)
                Exit Sub
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("ListePreparation", "fListePreparation", " fonctionsF", ex.Message, "0000323", "Erreur d'exécution de fonctionsF ", True, True, True)

        End Try
    End Sub

    Public Sub Init()
        Dim StrSQL As String = ""
        Try
            If (dsArticle.Tables.IndexOf("ARTICLE_DESIGNATION_LISTE") > -1) Then
                dsArticle.Tables("ARTICLE_DESIGNATION_LISTE").Clear()
            End If
            If (dsArticle.Tables.IndexOf("TYPE_PREPARATION") > -1) Then
                dsArticle.Tables("TYPE_PREPARATION").Clear()
            End If

            'chargement des noms Articles
            StrSQL = "SELECT DISTINCT Designation FROM ARTICLE as ARTICLE_DESIGNATION_LISTE where Supprime=0 AND CodeCategorie=9 ORDER BY Designation ASC"
            cmdArticle.Connection = ConnectionServeur
            cmdArticle.CommandText = StrSQL
            daArticle = New SqlDataAdapter(cmdArticle)
            daArticle.Fill(dsArticle, "ARTICLE_DESIGNATION_LISTE")
            cmbDesignation.DataSource = dsArticle.Tables("ARTICLE_DESIGNATION_LISTE")
            cmbDesignation.DisplayMember = "Designation"
            cmbDesignation.ColumnHeaders = False
            cmbDesignation.Splits(0).DisplayColumns("Designation").Width = 10
            cmbDesignation.ExtendRightColumn = True

            'chargement des Préparations
            StrSQL = "SELECT DISTINCT CodeTypePreparation,LibelleTypePreparation FROM TYPE_PREPARATION ORDER BY LibelleTypePreparation ASC"
            cmdArticle.Connection = ConnectionServeur
            cmdArticle.CommandText = StrSQL
            daArticle = New SqlDataAdapter(cmdArticle)
            daArticle.Fill(dsArticle, "TYPE_PREPARATION")
            cmbType.DataSource = dsArticle.Tables("TYPE_PREPARATION")
            cmbType.ValueMember = "CodeTypePreparation"
            cmbType.DisplayMember = "LibelleTypePreparation"
            cmbType.ColumnHeaders = False
            cmbType.Splits(0).DisplayColumns("CodeTypePreparation").Visible = False
            cmbType.Splits(0).DisplayColumns("LibelleTypePreparation").Width = 10
            cmbType.ExtendRightColumn = True

            'chargement des noms Composants
            StrSQL = "SELECT DISTINCT Designation FROM FORMULE_PREPARATION_DETAILS ORDER BY Designation ASC"
            cmdArticle.Connection = ConnectionServeur
            cmdArticle.CommandText = StrSQL
            daArticle = New SqlDataAdapter(cmdArticle)
            daArticle.Fill(dsArticle, "FORMULE_PREPARATION_DETAILS")
            cmbComposante.DataSource = dsArticle.Tables("FORMULE_PREPARATION_DETAILS")
            cmbComposante.DisplayMember = "Designation"
            cmbComposante.ColumnHeaders = False
            cmbComposante.Splits(0).DisplayColumns("Designation").Width = 10
            cmbComposante.ExtendRightColumn = True

            dtpDebut.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
            dtpDebut.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

            dtpFin.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
            dtpFin.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

            AfficherArticle()
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("ListePreparation", "fListePreparation", " Init", ex.Message, "0000324", "Erreur d'exécution de Init ", True, True, True)

        End Try
    End Sub

    Public Sub AfficherArticle()

        Dim I As Integer
        Dim Cond As String = "1=1"
        Dim StockArticle As Integer = 0
        Dim QteUnitaireArticle As Integer = 1
        TotalVenteHT = 0.0
        TotalVenteTTC = 0.0

        Try
            If (dsArticle.Tables.IndexOf("Article") > -1) Then
                dsArticle.Tables("Article").Clear()
            End If

            'Composer la condition de la requête  
            If tCode.Text <> "" Then
                Cond += " AND ARTICLE.CodeABarre LIKE " + Quote(tCode.Text)
            End If

            If cmbDesignation.Text <> "" Then
                Cond += " AND  ARTICLE.Designation LIKE " + Quote(cmbDesignation.Text + "%")
            End If

            Try

                If cmbType.Text <> "" Then
                    Cond += " AND ARTICLE.CodeTypePreparation = " + cmbType.SelectedValue.ToString
                End If

            Catch ex As Exception

            End Try


            If CBdate.Checked = True Then
                If dtpDebut.Text <> "" And dtpDebut.Text.Length = 10 Then
                    Cond += " AND CAST(FORMULE_PREPARATION.date as date) >= " + Quote(dtpDebut.Text)
                End If

                If dtpFin.Text <> "" And dtpFin.Text.Length = 10 Then
                    Cond += " AND CAST(FORMULE_PREPARATION.date as date) <= " + Quote(dtpFin.Text)
                End If
            End If

            If tPrix.Text <> "" And IsNumeric(tPrix.Text) = True Then
                If tPrix.Text = CInt(tPrix.Text) Then
                    Cond += " AND CAST (ARTICLE.PrixVenteTTC AS int) LIKE " + Quote(tPrix.Text)
                Else
                    Cond += " AND ARTICLE.PrixVenteTTC LIKE " + Quote(tPrix.Text + "%")
                End If
            Else
                Cond += " AND ARTICLE.PrixVenteTTC LIKE " + Quote(tPrix.Text + "%")
            End If

            If cmbComposante.Text <> "" Then
                Cond += " AND  FORMULE_PREPARATION_DETAILS.Designation = " + Quote(cmbComposante.Text)
            End If

            cmdArticle.CommandText = "SELECT distinct( ARTICLE.CodeArticle)," + _
                                            " ARTICLE.CodeABarre," + _
                                            " ARTICLE.Designation ," + _
                                            " ARTICLE.CodeTypePreparation," + _
                                            " LibelleTypePreparation," + _
                                            " TVA,  " + _
                                            " ARTICLE.PrixVenteTTC, " + _
                                            " CASE WHEN StockArticle.Stock IS NULL THEN 0 ELSE StockArticle.Stock END as Stock, " + _
                                            " Section, " + _
                                            " RAYON, " + _
                                            " SITUATION_ARTICLE.LibelleSituationArticle," + _
                                            " PrixAchatTTC," + _
                                            " QuantiteUnitaire, " + _
                                            " FORMULE_PREPARATION.date, " + _
                                            " NombreJourValidite, " + _
                                            "'' AS Vide " + _
                                            " FROM ARTICLE" + _
                                            " LEFT OUTER JOIN FORMULE_PREPARATION ON ARTICLE.CodeArticle=FORMULE_PREPARATION.CodePreparation " + _
                                            " LEFT OUTER JOIN SITUATION_ARTICLE ON SITUATION_ARTICLE.CodeSituationArticle=ARTICLE.CodeSituation " + _
                                            " LEFT OUTER JOIN TYPE_PREPARATION ON ARTICLE.CodeTypePreparation=TYPE_PREPARATION.CodeTypePreparation " + _
                                            " LEFT OUTER JOIN FORMULE_PREPARATION_DETAILS ON FORMULE_PREPARATION.CodePreparation= FORMULE_PREPARATION_DETAILS.CodePreparation " + _
                                            " LEFT OUTER JOIN " + _
                                            " (SELECT  CodeArticle, SUM(QteLotArticle) AS Stock " + _
                                            " FROM dbo.LOT_ARTICLE " + _
                                            " GROUP BY CodeArticle) AS StockArticle ON dbo.ARTICLE.CodeArticle = StockArticle.CodeArticle " + _
                                            " WHERE " + Cond + " AND Supprime=0 AND CodeCategorie=9" + _
                                            " ORDER BY FORMULE_PREPARATION.date desc"

            cmdArticle.Connection = ConnectionServeur
            daArticle = New SqlDataAdapter(cmdArticle)
            daArticle.Fill(dsArticle, "ARTICLE")


            With gArticle
                .Columns.Clear()
                .DataSource = dsArticle
                .DataMember = "ARTICLE"
                .Rebind(False)
                .Columns("CodeABarre").Caption = "Code Article"
                .Columns("Designation").Caption = "Designation"
                .Columns("Rayon").Caption = "Rayon"
                .Columns("TVA").Caption = "TVA"
                .Columns("PrixVenteTTC").Caption = "Prix Vente TTC"
                .Columns("LibelleTypePreparation").Caption = "Type préparation"
                .Columns("LibelleSituationArticle").Caption = "Situation"
                .Columns("Section").Caption = "Section"
                .Columns("Stock").Caption = "Stock"
                .Columns("QuantiteUnitaire").Caption = "Quantité Unitaire"
                .Columns("PrixAchatTTC").Caption = "Prix Achat TTC"
                .Columns("date").Caption = "Date Préparation"
                .Columns("NombreJourValidite").Caption = "Nombre Jour Validité"
                .Columns("Vide").Caption = ""

                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    .Splits(0).DisplayColumns(I).Locked = True
                Next

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("CodeABarre").Width = 90
                .Splits(0).DisplayColumns("Designation").Width = 300
                .Splits(0).DisplayColumns("Rayon").Width = 50
                .Splits(0).DisplayColumns("Rayon").Style.HorizontalAlignment = AlignHorzEnum.Center
                .Splits(0).DisplayColumns("TVA").Width = 60
                .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 80
                .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

                .Splits(0).DisplayColumns("NombreJourValidite").Width = 120
                .Splits(0).DisplayColumns("NombreJourValidite").Style.HorizontalAlignment = AlignHorzEnum.Center

                .Splits(0).DisplayColumns("CodeTypePreparation").Visible = False
                .Splits(0).DisplayColumns("CodeTypePreparation").AllowSizing = False

                'colonne vide ajouter
                .Splits(0).DisplayColumns("date").Width = 100
                .Splits(0).DisplayColumns("date").Visible = True
                .Splits(0).DisplayColumns("date").Style.HorizontalAlignment = AlignHorzEnum.Center

                'colonne vide ajouter
                .Splits(0).DisplayColumns("Vide").Width = 50
                .Splits(0).DisplayColumns("Vide").Visible = False

                .Splits(0).DisplayColumns("Stock").Width = 50
                .Splits(0).DisplayColumns("Stock").Style.HorizontalAlignment = AlignHorzEnum.Center

                .Splits(0).DisplayColumns("QuantiteUnitaire").Width = 120
                .Splits(0).DisplayColumns("QuantiteUnitaire").Style.HorizontalAlignment = AlignHorzEnum.Center

                .Splits(0).DisplayColumns("PrixAchatTTC").Width = 80
                .Splits(0).DisplayColumns("PrixAchatTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

                .Splits(0).DisplayColumns("LibelleSituationArticle").Width = 60
                .Splits(0).DisplayColumns("LibelleSituationArticle").Style.HorizontalAlignment = AlignHorzEnum.Center

                .Splits(0).DisplayColumns("Section").Width = 60
                .Splits(0).DisplayColumns("Section").Style.HorizontalAlignment = AlignHorzEnum.Center

                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

                .Splits(0).SplitSize = 370
                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                'Style du Caractere et du grid
                ParametreGrid(gArticle)
            End With

            'gArticle.MoveRelative(x)

            For I = 0 To gArticle.RowCount - 1

                TotalVenteTTC += dsArticle.Tables("ARTICLE").Rows(I).Item("PrixVenteTTC") * dsArticle.Tables("ARTICLE").Rows(I).Item("Stock") / dsArticle.Tables("ARTICLE").Rows(I).Item("QuantiteUnitaire")
            Next

            lTotalVenteTTC.Text = Math.Round(TotalVenteTTC, 3).ToString("### ### ##0.000")

            lNbreDesArticles.Text = dsArticle.Tables("ARTICLE").Rows.Count.ToString + " Articles"
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("ListePreparation", "fListePreparation", " AfficherArticle", ex.Message, "0000325", "Erreur d'exécution de AfficherArticle ", True, True, True)

        End Try
    End Sub

    Private Sub bModifierArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifierArticle.Click
        'Suivi du scénario
        fMessageException.Show("ListePreparation", "fListePreparation", "bModifierArticle_Click", "NoException", "NoError", "Clic sur le bouton ModifierArticle", False, True, False)
        Try
            If gArticle.Columns("CodeTypePreparation").Value.ToString = "" Then
                Exit Sub
            End If

            Dim rowArticle As Double = gArticle(gArticle.Row, "CodeArticle") 'gArticle.Row

            If gArticle.Columns("CodeTypePreparation").Value = 4 Then

                Dim InstanceFractionnement As New fFractionnement
                fFractionnement.CodeArticleFractionne = gArticle.Columns("CodeArticle").Value
                fFractionnement.CodeABarre = gArticle.Columns("CodeABarre").Value
                fFractionnement.DesignationArticleFract = gArticle.Columns("Designation").Value
                fFractionnement.PrixTTCArticle = gArticle.Columns("PrixVenteTTC").Value
                InstanceFractionnement.AjoutModif = "M"

                'fFractionnement.init()

                InstanceFractionnement.ShowDialog()

                InstanceFractionnement.Dispose()
                InstanceFractionnement.Close()

            End If

            If gArticle.Columns("CodeTypePreparation").Value = 1 Then

                Dim InstancePreparation As New fPreparation
                fPreparation.CodePreparation = gArticle.Columns("CodeArticle").Value
                fPreparation.CodeABarre = gArticle.Columns("CodeABarre").Value
                fPreparation.TypePreparation = 1
                fPreparation.DesignationPreparation = gArticle.Columns("Designation").Value
                InstancePreparation.mode = "M"

                InstancePreparation.init()
                InstancePreparation.ShowDialog()

                InstancePreparation.Dispose()
                InstancePreparation.Close()
            End If

            If gArticle.Columns("CodeTypePreparation").Value = 3 Then

                Dim InstancePreparation As New fPreparation
                fPreparation.CodePreparation = gArticle.Columns("CodeArticle").Value
                fPreparation.CodeABarre = gArticle.Columns("CodeABarre").Value
                fPreparation.TypePreparation = 3
                fPreparation.DesignationPreparation = gArticle.Columns("Designation").Value
                InstancePreparation.mode = "M"
                fPreparation.init()

                InstancePreparation.ShowDialog()

                InstancePreparation.Dispose()
                InstancePreparation.Close()
            End If

            If gArticle.Columns("CodeTypePreparation").Value = 2 Then

                Dim InstancePreparation As New fPreparation
                fPreparation.CodePreparation = gArticle.Columns("CodeArticle").Value
                fPreparation.CodeABarre = gArticle.Columns("CodeABarre").Value
                fPreparation.TypePreparation = 2
                fPreparation.DesignationPreparation = gArticle.Columns("Designation").Value
                InstancePreparation.mode = "M"
                fPreparation.init()

                InstancePreparation.ShowDialog()

                InstancePreparation.Dispose()
                InstancePreparation.Close()
                Init()
            End If

            AfficherArticle()

            For i = 0 To gArticle.RowCount - 1
                If gArticle(i, "CodeArticle") = rowArticle Then
                    rowArticle = i
                    Exit For
                End If
            Next
            gArticle.Row = rowArticle

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("ListePreparation", "fListePreparation", " bModifierArticle_Click", ex.Message, "0000326", "Erreur d'exécution de bModifierArticle_Click ", True, True, True)

        End Try
    End Sub

    Private Sub bSuprimerArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSuprimerArticle.Click
        'Suivi du scénario
        fMessageException.Show("ListePreparation", "fListePreparation", "bSuprimerArticle_Click", "NoException", "NoError", "Clic sur le bouton SuprimerArticle", False, True, False)

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""
        Try
            Connect()

            Dim CodeArticleASupprimer As String = ""
            Dim DesignationArticleASupprimer As String = ""

            CodeArticleASupprimer = gArticle(gArticle.Row, "CodeArticle")
            DesignationArticleASupprimer = gArticle(gArticle.Row, "Designation")

            If gArticle.RowCount <> 0 Then
                If gArticle(gArticle.Row, "Stock") <> 0 Then 'And dsArticle.Tables("ARTICLE").Rows.Count - 1 <> 0 Then
                    MsgBox("Stock différent de zéro! Suppression impossible", MsgBoxStyle.OkOnly)
                    Exit Sub
                End If
            Else
                Exit Sub
            End If

            If gArticle.RowCount = 0 Then
                MsgBox("Liste Vide", MsgBoxStyle.OkOnly)
            Else
                If MsgBox("Voulez vous vraiment supprimer l'article " + gArticle(gArticle.Row, "Designation") + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then

                    If ControleDAcces(2, "SUPPRESSION_ARTICLE") = "False" Then
                        Exit Sub
                    Else
                        CodeOperateur = ControleDAcces(2, "SUPPRESSION_ARTICLE")
                    End If
                    If CodeOperateur = "" Then
                        CodeOperateur = CodeUtilisateur
                    End If

                    Dim rowArticle As Integer = gArticle.Row

                    Try
                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = "DELETE FROM FRACTIONNEMENT WHERE CodeArticleMere = " + Quote(CodeArticleASupprimer)
                        cmd.ExecuteNonQuery()
                        InsertionDansLog("SUPPRESSION_FRACTIONNEMENT", "La Suppression de fractionnement de l article " + DesignationArticleASupprimer, CodeOperateur, System.DateTime.Now, "ARTICLE", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
                        AfficherArticle()
                    Catch ex As Exception
                        MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                    End Try

                    Try
                        cmd.Connection = ConnectionServeur
                        'cmd.CommandText = "DELETE FROM article WHERE Codearticle = " + Quote(gArticle(gArticle.Row, "CodeArticle"))
                        cmd.CommandText = "UPDATE ARTICLE SET Supprime=1 WHERE Codearticle = " + Quote(CodeArticleASupprimer)
                        cmd.ExecuteNonQuery()
                        InsertionDansLog("SUPPRESSION_ARTICLE", "La Suppression de l article " + DesignationArticleASupprimer, CodeOperateur, System.DateTime.Now, "ARTICLE", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
                        AfficherArticle()

                        gArticle.Row = rowArticle

                    Catch ex As Exception
                        MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                    End Try
                End If
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("ListePreparation", "fListePreparation", " bSuprimerArticle_Click", ex.Message, "0000327", "Erreur d'exécution de bSuprimerArticle_Click ", True, True, True)

        End Try
    End Sub

    Private Sub bRechercher_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRechercher.Click
        'Suivi du scénario
        fMessageException.Show("ListePreparation", "fListePreparation", "bRechercher_Click", "NoException", "NoError", "Clic sur le bouton Recherche", False, True, False)
        tCode.Focus()
    End Sub

    Private Sub bViderLesChamps_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bViderLesChamps.Click
        'Suivi du scénario
        fMessageException.Show("ListePreparation", "fListePreparation", "bViderLesChamps_Click", "NoException", "NoError", "Clic sur le bouton ViderLesChamps", False, True, False)
        Try
            cmbType.Text = ""
            cmbDesignation.Text = ""
            tCode.Text = ""
            cmbComposante.Text = ""
            tPrix.Text = ""
            CBdate.Checked = False

            bFractionnement.Enabled = True
            bMagistrale.Enabled = True
            bPharmaceutique.Enabled = True
            bInjection.Enabled = True
            AfficherArticle()
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("ListePreparation", "fListePreparation", " bViderLesChamps_Click", ex.Message, "0000328", "Erreur d'exécution de bViderLesChamps_Click ", True, True, True)

        End Try
    End Sub

    Private Sub cmbDesignation_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbDesignation.KeyUp
        Try
            If e.KeyCode = Keys.Enter Then
                cmbDesignation.Text = cmbDesignation.WillChangeToText
                AfficherArticle()
                cmbType.Focus()
            Else
                cmbDesignation.OpenCombo()
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("ListePreparation", "fListePreparation", " cmbDesignation_KeyUp", ex.Message, "0000329", "Erreur d'exécution de cmbDesignation_KeyUp ", True, True, True)

        End Try
    End Sub

    Private Sub gArticle_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gArticle.FetchRowStyle
        Dim cmd As New SqlCommand
        Dim StrSQL As String = ""
        Dim TestEnCoursOuNN As Integer = 0
        Try
            StrSQL = "SELECT count(CodeArticle) FROM COMMANDE_DETAILS LEFT OUTER JOIN COMMANDE ON COMMANDE_DETAILS.NumeroCommande=COMMANDE.NumeroCommande" + _
                     " WHERE NumeroFacture is NULL " + " AND CodeArticle = " + Quote(gArticle.Columns("CodeArticle").CellText(e.Row))

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                TestEnCoursOuNN = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If TestEnCoursOuNN > 0 Then
                e.CellStyle.BackColor = Color.FromArgb(255, 255, 255, 128)
                TestEnCoursOuNN = 0
                Exit Sub
            End If

            If gArticle.Columns("Stock").CellText(e.Row) <= 0 Then
                e.CellStyle.BackColor = Color.FromArgb(255, 255, 192, 192)
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("ListePreparation", "fListePreparation", " gArticle_FetchRowStyle", ex.Message, "0000330", "Erreur d'exécution de gArticle_FetchRowStyle ", True, True, True)

        End Try
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub cmbType_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbType.KeyUp
        Try
            If e.KeyCode = Keys.Enter Then
                cmbType.Text = cmbType.WillChangeToText
                AfficherArticle()
                'cmbType.Focus()
            Else
                cmbType.OpenCombo()
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("ListePreparation", "fListePreparation", " cmbType_KeyUp", ex.Message, "0000331", "Erreur d'exécution de cmbType_KeyUp ", True, True, True)

        End Try
    End Sub

    Private Sub cmbType_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbType.TextChanged
        Try
            'If cmbType.SelectedValue = 1 Then
            '    bPharmaceutique.Enabled = True
            '    bFractionnement.Enabled = False
            '    bMagistrale.Enabled = False
            '    bInjection.Enabled = False
            'ElseIf cmbType.SelectedValue = 2 Then
            '    bPharmaceutique.Enabled = False
            '    bFractionnement.Enabled = False
            '    bMagistrale.Enabled = False
            '    bInjection.Enabled = True
            'ElseIf cmbType.SelectedValue = 3 Then
            '    bPharmaceutique.Enabled = False
            '    bFractionnement.Enabled = False
            '    bMagistrale.Enabled = True
            '    bInjection.Enabled = False
            'ElseIf cmbType.SelectedValue = 4 Then
            '    bPharmaceutique.Enabled = False
            '    bFractionnement.Enabled = True
            '    bMagistrale.Enabled = False
            '    bInjection.Enabled = False
            'End If

            AfficherArticle()

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("ListePreparation", "fListePreparation", " cmbType_TextChanged", ex.Message, "0000332", "Erreur d'exécution de cmbType_TextChanged ", True, True, True)

        End Try
    End Sub

    Private Sub tCode_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCode.KeyUp
        Try
            If e.KeyCode = Keys.Enter Then
                AfficherArticle()
                cmbDesignation.Focus()
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("ListePreparation", "fListePreparation", " tCode_KeyUp", ex.Message, "0000333", "Erreur d'exécution de tCode_KeyUp ", True, True, True)

        End Try
    End Sub



    Private Sub bFractionnement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFractionnement.Click
        'Suivi du scénario
        fMessageException.Show("ListePreparation", "fListePreparation", "bFractionnement_Click", "NoException", "NoError", "Clic sur le bouton Fractionnement", False, True, False)

        Dim StrMajLOT As String = ""
        Dim cmdMiseAJoursPrix As New SqlCommand
        Dim StrSQL As String = ""
        Try
            Dim InstanceFractionnement As New fFractionnement
            fFractionnement.CodeArticleFractionne = ""
            fFractionnement.CodeABarre = ""
            fFractionnement.DesignationArticleFract = ""
            fFractionnement.PrixTTCArticle = "0.000"
            InstanceFractionnement.AjoutModif = "A"

            'fFractionnement.init()

            InstanceFractionnement.ShowDialog()

            InstanceFractionnement.Dispose()
            InstanceFractionnement.Close()

            Init()

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("ListePreparation", "fListePreparation", " bFractionnement_Click", ex.Message, "0000334", "Erreur d'exécution de bFractionnement_Click ", True, True, True)

        End Try

    End Sub

    Private Sub bPharmaceutique_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPharmaceutique.Click
        'Suivi du scénario
        fMessageException.Show("ListePreparation", "fListePreparation", "bPharmaceutique_Click", "NoException", "NoError", "Clic sur le bouton Pharmaceutique", False, True, False)


        Try
            Dim InstancePreparation As New fPreparation
            fPreparation.CodePreparation = ""
            fPreparation.CodeABarre = ""
            fPreparation.TypePreparation = 1
            fPreparation.DesignationPreparation = ""
            InstancePreparation.mode = "A"
            fPreparation.init()

            InstancePreparation.ShowDialog()

            InstancePreparation.Dispose()
            InstancePreparation.Close()
            Init()
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("ListePreparation", "fListePreparation", " bPharmaceutique_Click", ex.Message, "0000335", "Erreur d'exécution de bPharmaceutique_Click ", True, True, True)

        End Try
    End Sub

    Private Sub bMagistrale_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bMagistrale.Click
        'Suivi du scénario
        fMessageException.Show("ListePreparation", "fListePreparation", "bMagistrale_Click", "NoException", "NoError", "Clic sur le bouton Magistrale", False, True, False)
        Dim i As Integer = 0

        Try
            Dim InstancePreparation As New fPreparation
            fPreparation.CodePreparation = ""
            fPreparation.CodeABarre = ""
            fPreparation.TypePreparation = 3
            fPreparation.DesignationPreparation = ""
            InstancePreparation.mode = "A"

            fPreparation.init()

            InstancePreparation.ShowDialog()

            InstancePreparation.Dispose()
            InstancePreparation.Close()
            Init()
        Catch ex As Exception
            'Gérer l'Exception
            'fMessageException.Show("ListePreparation", "fListePreparation", " bMagistrale_Click", ex.Message, "0000336", "Erreur d'exécution de bMagistrale_Click ", True, True, True)

        End Try
    End Sub

    Private Sub bInjection_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bInjection.Click
        'Suivi du scénario
        fMessageException.Show("ListePreparation", "fListePreparation", "bInjection_Click", "NoException", "NoError", "Clic sur le bouton Injection", False, True, False)

        Try
            Dim InstancePreparation As New fPreparation
            fPreparation.CodePreparation = ""
            fPreparation.TypePreparation = 2
            fPreparation.DesignationPreparation = ""
            InstancePreparation.mode = "A"
            fPreparation.init()

            InstancePreparation.ShowDialog()

            InstancePreparation.Dispose()
            InstancePreparation.Close()
            Init()
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("ListePreparation", "fListePreparation", " bInjection_Click", ex.Message, "0000337", "Erreur d'exécution de bInjection_Click ", True, True, True)

        End Try
    End Sub

    Private Sub bFermer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFermer.Click
        'Suivi du scénario
        fMessageException.Show("ListePreparation", "fListePreparation", "bFermer_Click", "NoException", "NoError", "Clic sur le bouton Fermer", False, True, False)
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherArticle()
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherArticle()
            cmbComposante.Focus()
        End If
    End Sub

    Private Sub CBdate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CBdate.CheckedChanged
        If CBdate.Checked = False Then
            dtpDebut.Enabled = False
            dtpFin.Enabled = False
        Else
            dtpDebut.Enabled = True
            dtpFin.Enabled = True
            dtpDebut.Text = Date.Today
            dtpFin.Text = Date.Today
            dtpDebut.Focus()
        End If
        AfficherArticle()
    End Sub

    Private Sub cmbComposante_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbComposante.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbComposante.Text = cmbComposante.WillChangeToText
            AfficherArticle()
            tPrix.Focus()
        Else
            cmbComposante.OpenCombo()
        End If
    End Sub

    Private Sub tPrix_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tPrix.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherArticle()
        End If
    End Sub

  
    Private Sub tPrix_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tPrix.TextChanged
        AfficherArticle()
    End Sub

    Private Sub cmbComposante_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbComposante.TextChanged
        AfficherArticle()
    End Sub

    Private Sub tCode_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCode.TextChanged
        AfficherArticle()
    End Sub

    Private Sub cmbDesignation_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbDesignation.TextChanged
        AfficherArticle()
    End Sub

End Class