﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fListeDesPreparationAProduire
    Dim cmdListe As New SqlCommand
    Dim cbListe As New SqlCommandBuilder
    Dim dsListe As New DataSet
    Dim daListe As New SqlDataAdapter

    Public StrSQL As String = ""
    Public ListeDesVentes As Boolean = False

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub
    Public Sub init()
        Dim i As Integer = 0

        StrSQL = "select ARTICLE.CodeArticle," + _
                 "Designation," + _
                 "dbo.ARTICLE.QteACommander - (CASE WHEN qte IS NULL THEN 0 ELSE Qte END)+ dbo.ARTICLE.StockAlerte AS AProduire," + _
                 "ARTICLE.StockAlerte," + _
                 "QteACommander," + _
                 "CASE WHEN qte IS NULL THEN 0 ELSE Qte END AS QTE " + _
                 "FROM ARTICLE LEFT OUTER JOIN " + _
                 "(SELECT SUM(QteLotArticle) as Qte,CodeArticle FROM LOT_ARTICLE  " + _
                 "where LOT_ARTICLE.CodeArticle = CodeArticle GROUP BY  CodeArticle) " + _
                 "AS StockArticle ON ARTICLE.CodeArticle =StockArticle.CodeArticle " + _
                 "WHERE CodeCategorie =9 AND CodeTypePreparation =1 " + _
                 "AND (Qte <StockAlerte OR Qte=0 or Qte is null) "

        cmdListe.Connection = ConnectionServeur
        cmdListe.CommandText = StrSQL
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "LISTE")

        With gListe
            .Columns.Clear()
            Try
                .DataSource = dsListe
            Catch ex As Exception
            End Try
            .DataMember = "LISTE"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code"
            .Columns("Designation").Caption = "Désignation"
            .Columns("AProduire").Caption = "A Produire"
            .Columns("StockAlerte").Caption = "Stock Alerte"
            .Columns("QteACommander").Caption = "Qté à Commander"
            .Columns("QTE").Caption = "Qté"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next
            .Splits(0).DisplayColumns("CodeArticle").Width = 150
            .Splits(0).DisplayColumns("Designation").Width = 400
            .Splits(0).DisplayColumns("AProduire").Width = 100
            .Splits(0).DisplayColumns("StockAlerte").Width = 100
            .Splits(0).DisplayColumns("QteACommander").Width = 100
            .Splits(0).DisplayColumns("QTE").Width = 100

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gListe)
        End With
    End Sub
    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub fListeDesPreparationAProduire_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler
    End Sub
    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)
        If e.KeyCode = Keys.F12 Then
            bQuitter_Click(o, e)
            Exit Sub
        End If
    End Sub
    
    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim CondCrystal As String = ""
        CondCrystal = "1=1 "
        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression de la liste des préparations à produire" Then
                num = I
            End If
        Next
        CR.FileName = Application.StartupPath + "\EtatDesPreparationAProduire.rpt"
        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent
        CR.RecordSelectionFormula = CondCrystal

        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression de la liste des préparations à produire"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If

    End Sub
End Class