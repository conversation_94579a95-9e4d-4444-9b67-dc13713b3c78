@echo off
echo ========================================
echo    DIAGNOSTIC PHARMA2000 MODERNE
echo    Analyse des problemes de lancement
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 DIAGNOSTIC EN COURS...
echo.

REM Vérifier l'existence de l'exécutable
echo 1. VERIFICATION DE L'EXECUTABLE :
if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe" (
    echo ✅ Executable trouve
    
    REM Afficher les détails du fichier
    echo.
    echo 📄 DETAILS DU FICHIER :
    dir "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe"
    echo.
    
    REM Vérifier les dépendances
    echo 📦 VERIFICATION DES DEPENDANCES :
    if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.deps.json" (
        echo ✅ Fichier deps.json present
    ) else (
        echo ❌ Fichier deps.json manquant
    )
    
    if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.runtimeconfig.json" (
        echo ✅ Fichier runtimeconfig.json present
    ) else (
        echo ❌ Fichier runtimeconfig.json manquant
    )
    
    if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.dll" (
        echo ✅ DLL principale presente
    ) else (
        echo ❌ DLL principale manquante
    )
    
) else (
    echo ❌ Executable non trouve !
    echo.
    echo 🔧 SOLUTION : Executez RECOMPILER_COMPLET.bat
    pause
    exit /b 1
)

echo.
echo 2. VERIFICATION DE .NET RUNTIME :
dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App"
if %errorlevel% neq 0 (
    echo ❌ .NET Desktop Runtime non trouve !
    echo.
    echo 🔧 SOLUTION :
    echo Installez .NET 9 Desktop Runtime depuis :
    echo https://dotnet.microsoft.com/download/dotnet/9.0
    echo.
    pause
    exit /b 1
) else (
    echo ✅ .NET Desktop Runtime detecte
)

echo.
echo 3. TEST DE LANCEMENT AVEC DIAGNOSTIC :
echo.
echo 🚀 Tentative de lancement avec affichage des erreurs...
echo.

REM Essayer de lancer avec capture d'erreur
echo Lancement en cours...
cd "PharmaModerne.UI\bin\Debug\net9.0-windows"
PharmaModerne.UI.exe 2>&1
set launch_result=%errorlevel%
cd ..\..\..\..\

echo.
echo 📊 RESULTAT DU LANCEMENT :
if %launch_result% equ 0 (
    echo ✅ Application lancee avec succes !
) else (
    echo ❌ Erreur lors du lancement (Code: %launch_result%)
    echo.
    echo 🔍 CAUSES POSSIBLES :
    echo 1. Dependances manquantes
    echo 2. .NET Runtime incorrect
    echo 3. Erreur dans le code de l'application
    echo 4. Antivirus bloquant l'execution
    echo 5. Permissions insuffisantes
)

echo.
echo 4. VERIFICATION DES LOGS D'EVENEMENTS :
echo.
echo Verification des erreurs Windows recentes...
powershell -Command "Get-EventLog -LogName Application -Newest 5 -EntryType Error | Where-Object {$_.Source -like '*dotnet*' -or $_.Source -like '*PharmaModerne*'} | Format-Table TimeGenerated, Source, Message -Wrap"

echo.
echo 5. TEST DE COMPATIBILITE :
echo.
echo 🔧 VERIFICATION DE LA COMPATIBILITE :

REM Vérifier la version de Windows
echo Version de Windows :
ver

echo.
echo Architecture du système :
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo ✅ 64-bit detecte
) else if "%PROCESSOR_ARCHITECTURE%"=="x86" (
    echo ⚠️ 32-bit detecte - peut causer des problemes
) else (
    echo ❓ Architecture inconnue : %PROCESSOR_ARCHITECTURE%
)

echo.
echo 6. TENTATIVE DE LANCEMENT ALTERNATIF :
echo.
echo 🔄 Essai avec dotnet run...
cd PharmaModerne.UI
dotnet run --configuration Debug 2>&1
set dotnet_result=%errorlevel%
cd ..

echo.
if %dotnet_result% equ 0 (
    echo ✅ Lancement avec dotnet run reussi !
) else (
    echo ❌ Echec avec dotnet run aussi (Code: %dotnet_result%)
)

echo.
echo ========================================
echo    SOLUTIONS RECOMMANDEES
echo ========================================
echo.

if %launch_result% neq 0 (
    echo 🔧 SOLUTIONS A ESSAYER :
    echo.
    echo 1. REINSTALLER .NET :
    echo    - Desinstallez .NET 9
    echo    - Reinstallez .NET 9 SDK + Desktop Runtime
    echo    - Redemarrez l'ordinateur
    echo.
    echo 2. VERIFIER L'ANTIVIRUS :
    echo    - Ajoutez le dossier PharmaModerne aux exceptions
    echo    - Desactivez temporairement l'antivirus
    echo.
    echo 3. EXECUTER EN TANT QU'ADMINISTRATEUR :
    echo    - Clic droit sur l'executable
    echo    - "Executer en tant qu'administrateur"
    echo.
    echo 4. RECOMPILER COMPLETEMENT :
    echo    - Executez RECOMPILER_COMPLET.bat
    echo    - Ou utilisez Visual Studio
    echo.
    echo 5. VERIFIER LES DEPENDANCES :
    echo    - Installez Visual C++ Redistributable
    echo    - Mettez a jour Windows
    echo.
    echo 6. MODE COMPATIBILITE :
    echo    - Clic droit sur l'executable
    echo    - Proprietes → Compatibilite
    echo    - Essayez Windows 10/11
    echo.
) else (
    echo ✅ L'application semble fonctionner correctement !
    echo.
    echo Si elle ne s'affiche pas :
    echo - Verifiez la barre des taches
    echo - Appuyez sur Alt+Tab
    echo - Verifiez les ecrans multiples
)

echo.
echo 7. CREATION D'UN LANCEUR SIMPLIFIE :
echo.
echo Creation d'un fichier de lancement simplifie...

echo @echo off > LANCER_SIMPLE.bat
echo cd /d "%%~dp0" >> LANCER_SIMPLE.bat
echo echo Lancement de PHARMA2000 Moderne... >> LANCER_SIMPLE.bat
echo cd "PharmaModerne.UI\bin\Debug\net9.0-windows" >> LANCER_SIMPLE.bat
echo start "" "PharmaModerne.UI.exe" >> LANCER_SIMPLE.bat
echo if %%errorlevel%% neq 0 ( >> LANCER_SIMPLE.bat
echo     echo Erreur lors du lancement >> LANCER_SIMPLE.bat
echo     pause >> LANCER_SIMPLE.bat
echo ^) >> LANCER_SIMPLE.bat

echo ✅ Fichier LANCER_SIMPLE.bat cree
echo.

echo ========================================
echo    INFORMATIONS SYSTEME
echo ========================================
echo.
echo 💻 ENVIRONNEMENT :
echo Repertoire actuel : %CD%
echo Utilisateur : %USERNAME%
echo Ordinateur : %COMPUTERNAME%
echo.

echo 🔧 VERSIONS :
dotnet --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ .NET SDK non detecte
) else (
    echo ✅ .NET SDK detecte
)

echo.
echo 📞 SUPPORT :
echo Si le probleme persiste :
echo 1. Copiez ce diagnostic complet
echo 2. Contactez le support technique
echo 3. Ou essayez de compiler avec Visual Studio
echo.

echo Appuyez sur une touche pour fermer...
pause >nul
