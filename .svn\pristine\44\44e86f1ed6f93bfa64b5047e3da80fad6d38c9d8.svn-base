﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fIndemnite
    Dim cmdIndemnite As New SqlCommand
    Dim daIndemnite As New SqlDataAdapter
    Dim daIndemniteTestCode As New SqlDataAdapter
    Dim cbIndemnite As New SqlCommandBuilder
    Dim dsIndemnite As New DataSet

    Dim xIndemnite As Integer
    Dim ModeIndemnite As String
    Dim NomIndemnite As String

    Dim CodeExiste As Boolean = False
    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub
    Public Sub init()
        afficherIndemnite()
    End Sub

    Public Sub afficherIndemnite()
        Dim I As Integer

        dsIndemnite.Clear()
        cmdIndemnite.CommandText = " SELECT " + _
                                   " CodeIndemnite, " + _
                                   " LibelleIndemnite, " + _
                                   " Prix " + _
                                   " FROM INDEMNITE " + _
                                   " ORDER BY CodeIndemnite"

        cmdIndemnite.Connection = ConnectionServeur
        daIndemnite = New SqlDataAdapter(cmdIndemnite)
        daIndemnite.Fill(dsIndemnite, "INDEMNITE")

        With gIndemnite
            .Columns.Clear()
            .DataSource = dsIndemnite
            .DataMember = "INDEMNITE"
            .Rebind(False)
            .Columns("CodeIndemnite").Caption = "Code Indemnité"
            .Columns("LibelleIndemnite").Caption = "Libelle Indemnité"
            .Columns("Prix").Caption = "Prix Indemnité"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("CodeIndemnite").Width = 120
            .Splits(0).DisplayColumns("CodeIndemnite").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("LibelleIndemnite").Width = 300
            .Splits(0).DisplayColumns("LibelleIndemnite").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Prix").Width = 80
            .Splits(0).DisplayColumns("Prix").Style.HorizontalAlignment = AlignHorzEnum.Center

            .Splits(0).DisplayColumns("CodeIndemnite").Locked = True
            .Splits(0).DisplayColumns("LibelleIndemnite").Locked = False
            .Splits(0).DisplayColumns("Prix").Locked = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gIndemnite)
        End With
        gIndemnite.MoveRelative(xIndemnite)
        cbIndemnite = New SqlCommandBuilder(daIndemnite)

    End Sub

    Private Sub bAjouterIndemnite_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterIndemnite.Click
        Dim dr As DataRow
        Dim StrSQL As String = ""

        If tCodeIndemnite.Text = "" Then
            MsgBox("Veuillez saisir le code de l'indemnité !", MsgBoxStyle.Critical, "Erreur")
            tCodeIndemnite.Focus()
            Exit Sub
        End If

        If tLibelleIndemnite.Text = "" Then
            MsgBox("Veuillez saisir le libelle de l'indemnité !", MsgBoxStyle.Critical, "Erreur")
            tLibelleIndemnite.Focus()
            Exit Sub
        End If

        If tPrixIndemnite.Text = "" Then
            MsgBox("Veuillez saisir le prix de l'indemnité !", MsgBoxStyle.Critical, "Erreur")
            tPrixIndemnite.Focus()
            Exit Sub
        End If

        If CodeExiste = True Then
            MsgBox("Code indemnité existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
            tCodeIndemnite.Focus()
            Exit Sub
        End If

        With dsIndemnite
            dr = .Tables("INDEMNITE").NewRow
            dr.Item("LibelleIndemnite") = tLibelleIndemnite.Text
            dr.Item("CodeIndemnite") = tCodeIndemnite.Text
            dr.Item("Prix") = tPrixIndemnite.Text
            .Tables("INDEMNITE").Rows.Add(dr)
        End With

        Try
            daIndemnite.Update(dsIndemnite, "INDEMNITE")
            afficherIndemnite()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsIndemnite.Reset()
        End Try

        tCodeIndemnite.Text = ""
        tLibelleIndemnite.Text = ""
        tPrixIndemnite.Text = ""
    End Sub

    Private Sub tSupprimerIndemnite_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tSupprimerIndemnite.Click
        Dim cmd As New SqlCommand
        If gIndemnite.RowCount > 0 Then
            If MsgBox("Voulez vous vraiment supprimer cette indemnité " + Quote(gIndemnite(gIndemnite.Row, "LibelleIndemnite")) + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM INDEMNITE WHERE CodeIndemnite =" + Quote(gIndemnite(gIndemnite.Row, "CodeIndemnite"))
                    cmd.ExecuteNonQuery()
                    afficherIndemnite()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub

    Private Sub gIndemnite_AfterColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gIndemnite.AfterColEdit
        Dim dr As DataRow
        Dim StrSQL As String = ""

        If gIndemnite(gIndemnite.Row, "LibelleIndemnite").ToString() = "" Then
            MsgBox("Veuillez saisir le libellé")
            gIndemnite.Col = 0
            Exit Sub
        End If

        If gIndemnite(gIndemnite.Row, "Prix").ToString() = "" Then
            gIndemnite.Col = 1
            MsgBox("Veuillez saisir le prix")
            Exit Sub
        End If

        With dsIndemnite.Tables("INDEMNITE_MAJ")
            dr = .Rows(0)
            dr.Item("LibelleIndemnite") = gIndemnite(gIndemnite.Row, "LibelleIndemnite")
            dr.Item("Prix") = gIndemnite(gIndemnite.Row, "Prix")
        End With
        Try
            daIndemnite.Update(dsIndemnite, "INDEMNITE_MAJ")

        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            afficherIndemnite()
        End Try
    End Sub

    Private Sub gIndemnite_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gIndemnite.Click
        Dim StrSQL As String = ""
        NomIndemnite = Quote(gIndemnite(gIndemnite.Row, "LibelleIndemnite"))
        If NomIndemnite = "" Then
            MsgBox("Veuillez sélectionner une indemnité !", MsgBoxStyle.Critical, "Erreur")
            gIndemnite.Focus()
            Exit Sub
        End If

        If (dsIndemnite.Tables.IndexOf("INDEMNITE_MAJ") > -1) Then
            dsIndemnite.Tables("INDEMNITE_MAJ").Clear()
        End If

        StrSQL = " SELECT * FROM INDEMNITE WHERE LibelleIndemnite = " + NomIndemnite

        cmdIndemnite.Connection = ConnectionServeur
        cmdIndemnite.CommandText = StrSQL
        daIndemnite = New SqlDataAdapter(cmdIndemnite)
        daIndemnite.Fill(dsIndemnite, "INDEMNITE_MAJ")
        cbIndemnite = New SqlCommandBuilder(daIndemnite)
    End Sub

    Private Sub tCodeIndemnite_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeIndemnite.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLibelleIndemnite.Focus()
        End If
    End Sub

    Private Sub tCodeIndemnite_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeIndemnite.LostFocus
        lTest.Visible = False
    End Sub

    Private Sub tCodeIndemnite_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeIndemnite.TextChanged
        If tCodeIndemnite.Text <> "" Then
            If IsNumeric(tCodeIndemnite.Text.Substring(Len(tCodeIndemnite.Text) - 1, 1)) = False Then
                MsgBox("Veuillez introduire des chiffres !", MsgBoxStyle.Critical, "Erreur")
                tCodeIndemnite.Text = tCodeIndemnite.Text.Substring(0, Len(tCodeIndemnite.Text) - 1)
                tCodeIndemnite.Select(Len(tCodeIndemnite.Text), 0)
            End If
        End If

        Dim StrSQLtest As String = ""
        If (dsIndemnite.Tables.IndexOf("INDEMNITE_TEST") > -1) Then
            dsIndemnite.Tables("INDEMNITE_TEST").Clear()
        End If

        StrSQLtest = " SELECT * FROM INDEMNITE as INDEMNITE_TEST WHERE CodeIndemnite=" + Quote(tCodeIndemnite.Text)
        cmdIndemnite.Connection = ConnectionServeur
        cmdIndemnite.CommandText = StrSQLtest
        daIndemniteTestCode = New SqlDataAdapter(cmdIndemnite)
        daIndemniteTestCode.Fill(dsIndemnite, "INDEMNITE_TEST")

        If dsIndemnite.Tables("INDEMNITE_TEST").Rows.Count <> 0 Then
            lTest.Text = "Code non valide déja existe"
            lTest.ForeColor = Color.OrangeRed
            lTest.Visible = True
            CodeExiste = True
        Else
            lTest.Text = "Code valide"
            lTest.ForeColor = Color.LawnGreen
            lTest.Visible = True
            CodeExiste = False
        End If

        If tCodeIndemnite.Text = "" Then
            lTest.Visible = False
        End If
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub gIndemnite_KeyDown(sender As Object, e As KeyEventArgs) Handles gIndemnite.KeyDown
       
    End Sub
End Class