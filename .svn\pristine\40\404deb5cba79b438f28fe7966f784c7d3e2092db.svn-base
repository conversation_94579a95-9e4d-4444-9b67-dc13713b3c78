﻿Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Public Class fSortie
   
    Dim cmdSortie As New SqlCommand
    Dim cbSortie As New SqlCommandBuilder
    Dim dsSortie As New DataSet
    Dim daSortie As New SqlDataAdapter

    Dim cmdSortieEntete As New SqlCommand
    Dim daSortieEntete As New SqlDataAdapter
    Dim cbSortieEntete As New SqlCommandBuilder

    Dim cmdSortieDetails As New SqlCommand
    Dim daSortieDetails As New SqlDataAdapter
    Dim cbSortieDetails As New SqlCommandBuilder

    Dim StrSQL As String

    Public NumeroligneSortie As Integer = 0

    Dim mode As String = ""

    Public NumeroSortie As String = ""

    Public TotalTTCVente As Double = 0.0
    Public TotalHTVente As Double = 0.0
    Public TotalTTCAchat As Double = 0.0
    Public TotalHTAchat As Double = 0.0
    Public TVA As Double = 0.0
    Public Timbre As Double = 0.3

    '------------------------------------------------- les informations de paiement (retour de fenetre paiement)
    Public PaiementOkNo As String = ""
    Public Operateur As Integer = 0

    Public NouvelleAchat As DataRow = Nothing 'datarow pour charger l'entête dans la datatable ACHAT
    Public NouvelArticle As DataRow = Nothing 'datarow pour charger les détails dans la datatable ACHAT_DETAIL
    Public dr As DataRow = Nothing 'datarow pour charger dans la datatable ARTICLE qui contient les articles recherchés pendant la saisie de la designation

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        Try
            If argument = "114" And bConfirmer.Enabled = True Then
                bConfirmer_Click(sender, e)
            End If
            If argument = "115" And bRecherche.Enabled = True Then
                bRecherche_Click(sender, e)
            End If

            If argument = "116" And bAjouter.Enabled = True Then
                bAjouter_Click(sender, e)
            End If

            If argument = "118" And bSupprimer.Enabled = True Then
                bSupprimer_Click(sender, e)
            End If

            If argument = "119" And bModifier.Enabled = True Then
                'bModifier_Click(sender, e)
                bModifier.PerformClick()
            End If

            If argument = "121" And bAnnuler.Enabled = True Then
                bAnnuler_Click(sender, e)
            End If

            If argument = "122" And bImprimer.Enabled = True Then
                bImprimer_Click(sender, e)
            End If
            '--------------------- boutton close 
            If argument = "123" And bQuitter.Enabled = True Then
                bQuitter_Click(sender, e)
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " fonctionsF", ex.Message, "0000135", "Erreur d'exécution de fonctionsF ", True, True, True)

        End Try

    End Sub

    Public Sub Init()

        'Initialiser les controles
        initLoadControl()

        'Init Nature Sortie
        initNatureSortie()

        'Appel Pour selectionner le dernier ligne 
        NumeroligneSortie = selectionDernierLigneSortie()

        'Init Sortie Entete
        initSortieEntete()

        'Init Sortie Details
        initSortieDetails()

        'Appel pour charger les information de l'Etete en question
        ChargerSortie(NumeroligneSortie)

        'Init Grid
        initgArticle()

        'Init Article 
        initArticle()

    End Sub

    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)

        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""
        Try
            Try
                Quote(ValeurCle)
            Catch ex As Exception
                Return Nothing
                Exit Function
            End Try

            StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurCle)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                Valeur = CmdCalcul.ExecuteScalar().ToString
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " RecupererValeurExecuteScalaire", ex.Message, "0000136", "Erreur d'exécution de RecupererValeurExecuteScalaire ", True, True, True)

        End Try
        Return (Valeur)
    End Function

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click

        Try
            'Changer le MODE en Ajout
            mode = "Ajout"

            'Appel ChargerSortie: Pour Récuperer la 
            'structure des DS SORTIE et SORTIE_DEAILS
            'La valeur 0 est inexistant

            ChargerSortie("0") '0 au lieu de 88ij

            'Ajout d'un nouvel enregistrement vide dans la table SORTIE_DETAIL
            NouvelArticle = dsSortie.Tables("SORTIE_DETAILS").NewRow()
            NouvelArticle("Designation") = ""
            NouvelArticle("CodeArticle") = ""
            NouvelArticle("CodeABarre") = ""
            dsSortie.Tables("SORTIE_DETAILS").Rows.Add(NouvelArticle)

            Me.gArticles.Splits(0).DisplayColumns(1).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(4).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(5).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(9).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(10).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(11).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(12).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(13).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(14).AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns(15).AllowFocus = False

            'Initialiser controles
            initControlSortie()
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " bAjouter_Click", ex.Message, "0000137", "Erreur d'exécution de bAjouter_Click ", True, True, True)

        End Try
    End Sub

    '' ''Private Sub gArticles_BeforeColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColEditEventArgs) Handles gArticles.BeforeColEdit
    '' ''    If e.Column.Name = "Date péremption" Then 'gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then ' 
    '' ''        Dim det As New C1.Win.C1Input.C1DateEdit
    '' ''        det.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
    '' ''        gArticles.Columns("DatePeremption").Editor = det
    '' ''    End If
    '' ''End Sub

    Private Sub gArticles_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gArticles.Change
        Try
            If (gArticles.Columns(gArticles.Col).DataField() = "Designation" And gArticles.Columns("Designation").Value <> "") Or gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" Then
                Dim StrSQL1 As String = ""
                Dim I As Integer
                Dim NbLigne As Integer
                NbLigne = gArticles.RowCount

                With gListeRecherche
                    .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
                    .Top = Me.gArticles.Top + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight
                End With

                Try
                    dsSortie.Tables("ARTICLE").Clear()
                Catch ex As Exception

                End Try
                If gArticles.Row = dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1 And gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                    gListeRecherche.Visible = True
                Else
                    gListeRecherche.Visible = False
                End If

                'chargement des articles qui sont mis en jeu
                If gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                    If gArticles.Columns("Designation").Value.ToString.Length > 1 Then
                        If gArticles.Columns("Designation").Value.ToString.IndexOf(".") > 0 And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1)) Then   'gArticles.Columns("Designation").Value.ToString.Substring(gArticles.Columns("Designation").Value.ToString.Length - 1, 1) Like "." And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1))

                            StrSQL1 = "SELECT CodeArticle," + _
                                      "Designation," + _
                                      "LibelleForme," + _
                                      "PrixVenteTTC" + _
                                      " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                      " WHERE  " + _
                                      " ltrim(str(PrixVenteTTC,10,3)) LIKE '" + _
                                      gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + "%' AND Supprime=0 ORDER BY PrixVenteTTC"
                        Else
                            StrSQL1 = "SELECT CodeArticle," + _
                                      "Designation," + _
                                      "LibelleForme," + _
                                      "PrixVenteTTC" + _
                                      " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                      " WHERE  " + _
                                      " Designation LIKE '" + gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + _
                                      "%' AND Supprime=0 ORDER BY Designation"
                        End If
                    Else
                        StrSQL1 = "SELECT CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC" + _
                                  " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  " WHERE  " + _
                                  " Designation LIKE '" + gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + _
                                  "%' AND Supprime=0 ORDER BY Designation"
                    End If
                ElseIf gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" Then
                    StrSQL1 = "SELECT CodeArticle," + _
                              "Designation," + _
                              "LibelleForme," + _
                              "PrixVenteTTC" + _
                              " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                              " WHERE " + _
                              "CodeABarre LIKE '" + gArticles.Columns("CodeArticle").Value + _
                              "' AND Supprime=0 ORDER BY Designation"

                End If
                cmdSortie.Connection = ConnectionServeur
                cmdSortie.CommandText = StrSQL1
                daSortie = New SqlDataAdapter(cmdSortie)
                daSortie.Fill(dsSortie, "ARTICLE")

                If dsSortie.Tables("ARTICLE").Rows.Count > 0 Then
                    dr = dsSortie.Tables("ARTICLE").Rows(0)
                End If

                With gListeRecherche
                    .Columns.Clear()
                    .DataSource = dsSortie
                    .DataMember = "ARTICLE"
                    .Rebind(False)
                    .Columns("CodeArticle").Caption = "Code Article"
                    .Columns("Designation").Caption = "Designation"
                    .Columns("LibelleForme").Caption = "Forme"
                    .Columns("PrixVenteTTC").Caption = "Prix de vente"

                    ' Centrer tous les entêtes
                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    Next
                    ' CentreR tous les valeurs
                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    Next

                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).Visible = False
                    Next

                    .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                    .Splits(0).DisplayColumns("CodeArticle").Visible = False
                    .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
                    .Splits(0).DisplayColumns("Designation").Visible = True
                    .Splits(0).DisplayColumns("LibelleForme").Visible = True
                    .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

                    .Splits(0).DisplayColumns("CodeArticle").Width = 0
                    .Splits(0).DisplayColumns("Designation").Width = 260
                    .Splits(0).DisplayColumns("LibelleForme").Width = 90
                    .Splits(0).DisplayColumns("PrixVenteTTC").Width = 90

                    .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(210, 240, 255)
                    .Splits(0).DisplayColumns("LibelleForme").Style.BackColor = Color.FromArgb(250, 250, 200)
                    .Splits(0).DisplayColumns("PrixVenteTTC").Style.BackColor = Color.FromArgb(210, 255, 230)

                    .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                    .Splits(0).ColumnCaptionHeight = 20
                    .Splits(0).RecordSelectors = False
                    .ExtendRightColumn = True
                    .EmptyRows = True
                    .FetchRowStyles = True
                End With

                Dim Col As New C1.Win.C1TrueDBGrid.C1DataColumn
                Dim dc As C1.Win.C1TrueDBGrid.C1DisplayColumn

                With gListeRecherche
                    .Columns.Insert(0, Col)
                    Col.Caption = "Stock"
                    dc = .Splits(0).DisplayColumns.Item("Stock")
                    dc.Width = 80
                    .Splits(0).DisplayColumns(4).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    .Splits(0).DisplayColumns(4).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(210, 255, 230)
                    dc.Visible = True
                    .Rebind(True)
                End With

            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " gArticles_Change", ex.Message, "0000138", "Erreur d'exécution de gArticles_Change ", True, True, True)

        End Try
    End Sub
    Public Function RecupereNumero()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer
        Try
            StrSQL = " SELECT max([NumeroSortie]) FROM [SORTIE]"
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQL
            Try
                ValeurActuel = cmdRecupereNum.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
            If ValeurActuel.Length > 4 Then
                If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                    Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                    Numero = Numero + 1
                    numeroConvertit = Numero.ToString
                    For i = 0 To 5 - numeroConvertit.Length
                        numeroConvertit = "0" + numeroConvertit
                    Next
                    ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
                Else
                    ValeurRetour = Year(Today).ToString + "/" + "000001"
                End If
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " RecupereNumero", ex.Message, "0000139", "Erreur d'exécution de RecupereNumero ", True, True, True)

        End Try
        Return ValeurRetour
    End Function

    Private Sub gArticles_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyUp
        Dim i As Integer = 0
        Dim M As Integer = 0
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        'Dim TestNumeroLot As Integer
        Dim QuantiteAAjouter As Integer = 0
        Dim CodeArticleExisteDansLaListe As String = ""


        Try
            '---------------------------------- test si on est en mode saisi ou non ---------------------------
            If mode <> "Ajout" And mode <> "Modif" Then
                Exit Sub
            End If

            If gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                gListeRecherche.Visible = True
            End If
            '---------------------------------- suppression du dernier ligne vide si on a deux lignes au mm temps vide
            '---------------------------------- cas ou on supprime dernier ligne
            If dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1 = 1 And gArticles(0, "CodeArticle") = "" And gArticles(1, "CodeArticle") = "" Then
                gArticles.MoveLast()
                gArticles.MovePrevious()
                gArticles.Delete()
            End If
            '---------------------------------- test du type de la valeur d'entrée dans la colonne quantité (numéric) ------------
            '---------------------------------- test du  valeur d'entrée dans la colonne quantité < 99999 ------------

            If gArticles.Columns(gArticles.Col).DataField() = "Qte" Then

                If gArticles.Columns("Qte").Value.ToString <> "-" Then   ' pour les quantité négatives
                    If IsNumeric(gArticles.Columns("Qte").Value) = False Then
                        gArticles.Columns("Qte").Value = "1"
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                        gArticles.EditActive = True
                        Exit Sub
                    End If
                    If (gArticles.Columns("Qte").Value = 0) And e.KeyCode = Keys.Enter Then
                        gArticles.Columns("Qte").Value = ""
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                        gArticles.EditActive = True
                        Exit Sub
                    End If
                    If (gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999) And e.KeyCode = Keys.Enter Then
                        gArticles.Columns("Qte").Value = "1"
                        MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                        gArticles.EditActive = True
                        Exit Sub
                    End If
                End If

            End If


            ''********************************** Contrôles des numéros de lot et des dates de péremption ************

            ''-------- afféctation du num de lot pour la date de péremption si elle existe deja DANS LA BASE ------
            'Try
            '    If e.KeyCode = Keys.Enter And gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then
            '        If gArticles.Columns("DatePeremption").Value.ToString <> "" Then
            '            StrSQL = " SELECT count(NumeroLotArticle) FROM [LOT_ARTICLE] WHERE DatePeremptionArticle='" + _
            '            gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
            '            gArticles.Columns("CodeArticle").Value + "'"
            '            cmd.Connection = ConnectionServeur
            '            cmd.CommandText = StrSQL

            '            If cmd.ExecuteScalar <> 0 Then

            '                StrSQL = " SELECT NumeroLotArticle FROM [LOT_ARTICLE] WHERE DatePeremptionArticle='" + _
            '                         gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
            '                         gArticles.Columns("CodeArticle").Value + "'"
            '                cmd.Connection = ConnectionServeur
            '                cmd.CommandText = StrSQL
            '                gArticles.Columns("NumeroLotArticle").Value = cmd.ExecuteScalar
            '            Else
            '                gArticles.Columns("NumeroLotArticle").Value = ""
            '            End If
            '        End If
            '    End If
            'Catch ex As Exception

            '    'Gérer l'Exception
            '    fMessageException.Show("Sortie", "fSortie", " gArticles_KeyUp", ex.Message, "0000141", "Erreur d'exécution de la requete ", True, True, True)

            'End Try
            ''-------- afféctation du num de lot pour la date de péremption si elle existe deja DANS LA LISTE -----
            'If gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            '    Dim CodeNewArticle As String = ""
            '    Dim DateNewArticle As Date

            '    CodeNewArticle = gArticles.Columns("CodeArticle").Value
            '    DateNewArticle = gArticles.Columns("DatePeremption").Value

            '    If gArticles.Columns("NumeroLotArticle").Value = "" Then
            '        i = 0
            '        Do While i < gArticles.RowCount - 1
            '            If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '                If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") = DateNewArticle Then
            '                    gArticles.Columns("NumeroLotArticle").Value = gArticles(i, "NumeroLotArticle")
            '                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '                    gArticles.EditActive = True
            '                End If
            '            End If
            '            i = i + 1
            '        Loop
            '    End If
            'End If
            ''--------------------------- test de l'existance d'un ancien lot avec cette date de péremption -----
            'Try
            '    If e.KeyCode = Keys.Enter And gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle" Then
            '        If gArticles.Columns("DatePeremption").Value.ToString <> "" And gArticles.Columns("NumeroLotArticle").Value.ToString <> "" Then
            '            StrSQL = " SELECT count(NumeroLotArticle) FROM [LOT_ARTICLE]WHERE DatePeremptionArticle='" + _
            '                     gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
            '                     gArticles.Columns("CodeArticle").Value + "'"
            '            cmd.Connection = ConnectionServeur
            '            cmd.CommandText = StrSQL

            '            If cmd.ExecuteScalar <> 0 Then
            '                StrSQL = " SELECT NumeroLotArticle FROM [LOT_ARTICLE]WHERE DatePeremptionArticle='" + _
            '                         gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
            '                         gArticles.Columns("CodeArticle").Value + "'"
            '                cmd.Connection = ConnectionServeur
            '                cmd.CommandText = StrSQL

            '                If gArticles.Columns("NumeroLotArticle").Value <> cmd.ExecuteScalar Then
            '                    MsgBox("Date de péremption existe pour un autre lot !", MsgBoxStyle.Critical, "Erreur")
            '                    gArticles.Columns("DatePeremption").Value = ""
            '                    gArticles.Columns("NumeroLotArticle").Value = ""
            '                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '                    gArticles.EditActive = False
            '                    Exit Sub
            '                End If
            '            End If
            '        End If
            '    End If
            'Catch ex As Exception

            '    'Gérer l'Exception
            '    fMessageException.Show("Sortie", "fSortie", " gArticles_KeyUp", ex.Message, "0000142", "Erreur d'exécution de la requete ", True, True, True)

            'End Try
            '---------------------------------- test de l'existance du numero de lot pour une autre date --------
            'Try
            '    If (gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle") And e.KeyCode = Keys.Enter Then
            '        StrSQL = " SELECT COUNT(NumeroLotArticle) FROM [LOT_ARTICLE] WHERE NumeroLotArticle='" + _
            '            gArticles.Columns("NumeroLotArticle").Value.ToString + "' AND CodeArticle='" + _
            '            gArticles.Columns("CodeArticle").Value + "' AND DatePeremptionArticle <> '" + _
            '            gArticles.Columns("DatePeremption").Value + "'"
            '        cmd.Connection = ConnectionServeur
            '        cmd.CommandText = StrSQL

            '        If cmd.ExecuteScalar() <> 0 Then
            '            MsgBox("Numero de lot existe déja !", MsgBoxStyle.Critical, "Erreur")
            '            gArticles.Columns("NumeroLotArticle").Value = ""
            '            gArticles.Columns("DatePeremption").Value = ""
            '            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '            gArticles.EditActive = False
            '            Exit Sub
            '        End If
            '    End If
            'Catch ex As Exception

            '    'Gérer l'Exception
            '    fMessageException.Show("Sortie", "fSortie", " gArticles_KeyUp", ex.Message, "0000143", "Erreur d'exécution de la requete ", True, True, True)

            'End Try
            '--------------- test si le mm numero du lot existe dans la liste au dessus pour le mm article mais 
            '--------------- avec une date de péremption differente et vise versa--------
            'If (gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle") And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            '    Dim CodeNewArticle As String = ""
            '    Dim DateNewArticle As Date
            '    Dim NumeroLotNewArticle As String = ""

            '    CodeNewArticle = gArticles.Columns("CodeArticle").Value
            '    DateNewArticle = gArticles.Columns("DatePeremption").Value
            '    NumeroLotNewArticle = gArticles.Columns("NumeroLotArticle").Value
            '    Dim QteNewArticle As Integer = 0
            '    '-------------------------- mm code mm numero de lot mais date different
            '    i = 0
            '    Do While i < gArticles.RowCount - 1
            '        If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '            If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") <> DateNewArticle And gArticles(i, "NumeroLotArticle") = NumeroLotNewArticle Then
            '                MsgBox("Numero de lot existe dans la liste pour le mm article mais avec une autre date de péremption!", MsgBoxStyle.Critical, "Erreur")
            '                gArticles.Columns("NumeroLotArticle").Value = ""
            '                gArticles.Columns("DatePeremption").Value = ""
            '                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '                gArticles.EditActive = False
            '                Exit Sub
            '            End If
            '        End If
            '        i = i + 1
            '    Loop
            '-------------------------- mm code mm date de lot mais numero different
            'i = 0
            'Do While i < gArticles.RowCount - 1
            '    If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '        If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") = DateNewArticle And gArticles(i, "NumeroLotArticle") <> NumeroLotNewArticle Then
            '            MsgBox("Date de péremption existe dans la liste pour le mm article mais avec un autre numéro de lot!", MsgBoxStyle.Critical, "Erreur")
            '            gArticles.Columns("DatePeremption").Value = ""
            '            gArticles.Columns("NumeroLotArticle").Value = ""
            '            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            '            gArticles.EditActive = False
            '            Exit Sub
            '        End If
            '    End If
            '    i = i + 1
            'Loop
            'End If

            '--------------- test de l'existance du mm article avec la mm date au dessus dans la 
            '--------------- liste (cas ou on a une date non null)

            'If (gArticles.Columns(gArticles.Col).DataField() = "DatePeremption") And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            '    Dim CodeNewArticle As String = ""
            '    Dim DateNewArticle As Date
            '    Dim NumeroLotNewArticle As String = ""
            '    Dim QteNewArticle As Integer = 0

            '    CodeNewArticle = gArticles.Columns("CodeArticle").Value
            '    DateNewArticle = gArticles.Columns("DatePeremption").Value
            '    NumeroLotNewArticle = gArticles.Columns("NumeroLotArticle").Value
            '    QteNewArticle = gArticles.Columns("Qte").Value

            '    i = 0
            '    Do While i < gArticles.RowCount - 1
            '        If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
            '            If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") = DateNewArticle And gArticles(i, "NumeroLotArticle") = NumeroLotNewArticle And i <> gArticles.Row Then
            '                gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QteNewArticle).ToString
            '                gArticles.MoveLast()
            '                gArticles.Delete()
            '                If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
            '                    NouvelArticle = dsSortie.Tables("SORTIE_DETAILS").NewRow()
            '                    NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
            '                    NouvelArticle("CodeArticle") = ""
            '                    NouvelArticle("CodeABarre") = ""
            '                    dsSortie.Tables("SORTIE_DETAILS").Rows.Add(NouvelArticle)
            '                End If
            '            End If
            '        End If
            '        i = i + 1
            '    Loop
            'End If

            '--------------- test de l'existance du mm article sans date aussi au dessus dans la 
            '--------------- liste (cas ou on a une date null)
            'Test BADR
            'If (gArticles.Columns(gArticles.Col).DataField() = "DatePeremption") And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = True Then
            'If (gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Or gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle") And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = True Then

            '    Dim CodeNewArticle As String = ""
            '    Dim QteNewArticle As Integer = 0

            '    CodeNewArticle = gArticles.Columns("CodeArticle").Value
            '    QteNewArticle = gArticles.Columns("Qte").Value

            '    i = 0
            '    Do While i < gArticles.RowCount - 1
            '        If IsDBNull(gArticles(i, "DatePeremption")) = True And gArticles(i, "NumeroLotArticle").ToString = "" Then
            '            If gArticles(i, "CodeArticle") = CodeNewArticle And i <> gArticles.Row Then
            '                gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QteNewArticle).ToString
            '                gArticles.MoveLast()
            '                gArticles.Delete()
            '                If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
            '                    NouvelArticle = dsSortie.Tables("SORTIE_DETAILS").NewRow()
            '                    NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
            '                    NouvelArticle("CodeArticle") = ""
            '                    NouvelArticle("CodeABarre") = ""
            '                    dsSortie.Tables("SORTIE_DETAILS").Rows.Add(NouvelArticle)
            '                End If
            '            End If
            '        End If
            '        i = i + 1
            '    Loop
            'End If
            '*******************************************************************************************************
            '####################################################################################################################
            '--------------- test de l'existance du mm article sans date aussi au dessus dans la 
            '--------------- liste (cas ou on a une date null)

            If gArticles.Columns(gArticles.Col).DataField() = "Qte" Then
                Dim CodeNewArticle As String = ""
                Dim QteNewArticle As Integer = 0

                CodeNewArticle = gArticles.Columns("CodeArticle").Value
                QteNewArticle = Val(gArticles.Columns("Qte").Value)

                i = 0
                Do While i < gArticles.RowCount - 1

                    If gArticles(i, "CodeArticle") = CodeNewArticle And i <> gArticles.Row Then
                        gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QteNewArticle).ToString
                        gArticles.MoveLast()
                        gArticles.Delete()
                        If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                            NouvelArticle = dsSortie.Tables("SORTIE_DETAILS").NewRow()
                            NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                            NouvelArticle("CodeArticle") = ""
                            NouvelArticle("CodeABarre") = ""
                            dsSortie.Tables("SORTIE_DETAILS").Rows.Add(NouvelArticle)


                        End If
                    End If
                    i = i + 1
                Loop
            End If
            '#####################################################################################################################

            '------------------------------ recherche par code ----------------------------------------------
            If gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" And e.KeyCode = Keys.Enter And gArticles.Row = dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1 Then
                ChargerDetailArticle(gArticles.Columns("CodeABarre").Value.ToString)
                Exit Sub
            ElseIf gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" And e.KeyCode = Keys.Enter Then
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
            End If
            '---------------------------------- masquer la liste de recherche si la designation est vide -----------
            If e.KeyCode = Keys.Back And gArticles.Columns("Designation").Value = "" And gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                gListeRecherche.Visible = False
            End If
            '---------------------------- pour passer à la navigation dans la petite liste de recherhce ----------------
            If (e.KeyCode = Keys.Down Or e.KeyCode = Keys.Up) And gListeRecherche.Visible = True Then
                gListeRecherche.Focus()
                gListeRecherche.Col = 2
                gListeRecherche.Row = 1
            End If
            '---------------------------- si l'artcile n'existe pas dans la liste de recherhce sa quantité doit etre 0  ----
            If dsSortie.Tables("ARTICLE").Rows.Count <= 0 And gListeRecherche.Visible = True Then '
                gArticles.Columns("Qte").Value = 0
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
            End If
            '---------------------------- calcul des montants --------------------------------------------------------
            If (gArticles.Columns(gArticles.Col).DataField() = "Qte") And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) Then
                CalculerMontants()
            End If

            '---------------------------------- verouillage des lignes déja confirmées -------------------------
            If (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) And gArticles.Row <> dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1 Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
                gArticles.Splits(0).DisplayColumns("Designation").Locked = True
            ElseIf gArticles.Row = dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1 Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
            End If

            '------------------------------ suppression d'une date de péremption
            If e.KeyCode = Keys.Delete And gArticles.Row <= dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1 And gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then
                gArticles.EditActive = False
                gArticles.Columns("DatePeremption").Value = ""
            End If
            '------------------------------ suppression de numéro de lot
            If e.KeyCode = Keys.Delete And gArticles.Row <= dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1 And gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle" Then
                gArticles.EditActive = False
                gArticles.Columns("NumeroLotArticle").Value = ""
                gArticles.EditActive = True
            End If

            '--------------------- traitement du clique sur le bouton ENTREE selon la colonne ---------------------------
            If e.KeyCode = Keys.Enter Then 'And (dsSortie.Tables("ARTICLE").Rows.Count > 0 Or gArticles.Row <> dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1) Then
                ChargerGride()     ' pour charger les informations de l'article a partir du fiche article

                If gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                    If gArticles.Columns("CodeArticle").Value = "" Then    ' si l'article choisit n'existe pas dans la liste de recherche
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
                        gArticles.Columns("Designation").Value = ""
                    Else
                        gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                    End If

                ElseIf gArticles.Columns(gArticles.Col).DataField() = "Qte" And e.KeyCode = Keys.Enter Then
                    '    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
                    'ElseIf gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then
                    '    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("NumeroLotArticle"))
                    'ElseIf gArticles.Columns(gArticles.Col).DataField() = "NumeroLotArticle" Then   ' si on est dans la colonne date de péremption on passe au nouveau ligne

                    If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                        NouvelArticle = dsSortie.Tables("SORTIE_DETAILS").NewRow()
                        NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                        NouvelArticle("CodeArticle") = ""
                        NouvelArticle("CodeABarre") = ""
                        dsSortie.Tables("SORTIE_DETAILS").Rows.Add(NouvelArticle)
                    End If
                    gArticles.MoveLast()
                    Try
                        dsSortie.Tables("ARTICLE").Clear()
                    Catch ex As Exception
                    End Try
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))

                End If
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " gArticles_KeyUp", ex.Message, "0000140", "Erreur d'exécution de gArticles_KeyUp ", True, True, True)

        End Try
    End Sub

    Public Sub ChargerGride()
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date
        Dim NumeroLot As String = ""
        Dim NumeroLotArticle As String = ""

        If gListeRecherche.Visible = False Then
            Exit Sub
        End If


        Dim j As Integer
        Dim NumeroLigne As Integer
        Dim DataRowRecherche As DataRow
        'And gArticles.Columns("Designation").Value <> ""
        If dsSortie.Tables("ARTICLE").Rows.Count > 0 Then
            '--------------------- rehcerche de l'index de la ligne qui contient l'article recherché ---------------
            For j = 0 To dsSortie.Tables("ARTICLE").Rows.Count - 1
                DataRowRecherche = dsSortie.Tables("ARTICLE").Rows(j)
                If DataRowRecherche.Item("CodeArticle") = gListeRecherche.Columns("CodeArticle").Value Then
                    NumeroLigne = j
                End If
            Next

            '------------------- chargement des données ---------------------------------------------- 
            dr = dsSortie.Tables("ARTICLE").Rows(NumeroLigne)
            NouvelArticle("NumeroSortie") = RecupereNumero()
            NouvelArticle("CodeArticle") = dr.Item("CodeArticle")
            NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            Try
                NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            Catch ex As Exception
            End Try

            NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

            ''''
            NouvelArticle("QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            If NouvelArticle("QuantiteUnitaire") = 0 Then NouvelArticle("QuantiteUnitaire") = 1
            ''''

            NouvelArticle("Qte") = NouvelArticle("QuantiteUnitaire") '1

            NouvelArticle("Stock") = CalculeStock(dr.Item("CodeArticle"))

            NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("TotalAchatTTC") = Math.Round(NouvelArticle("PrixAchatTTC") / NouvelArticle("QuantiteUnitaire"), 3)
            NouvelArticle("PrixVenteHT") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("TotalVenteTTC") = Math.Round(NouvelArticle("PrixVenteTTC") / NouvelArticle("QuantiteUnitaire"), 3)

            '----------------------- récupération de la date de péremption

            StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] " + _
                     "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                     "' AND CodeArticle='" + dr.Item("CodeArticle") + _
                     "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                DatePeremption = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If DatePeremption = #12:00:00 AM# Then
                'NouvelArticle("DatePeremption") = "01/01/1900" 'System.DateTime.Today.Day.ToString + "/" + System.DateTime.Today.Month.ToString + "/" + System.DateTime.Today.Year.ToString  
            Else
                NouvelArticle("DatePeremption") = DatePeremption
            End If


            ' '' ''----------------------- récupération du numéro de lot
            StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
                     " WHERE DatePeremptionArticle ='" + NouvelArticle("DatePeremption") + _
                     "' AND CodeArticle='" + dr.Item("CodeArticle") + _
                     "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                NumeroLotArticle = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            If NumeroLotArticle = "" Then
                NouvelArticle("NumeroLotArticle") = ""
            Else
                NouvelArticle("NumeroLotArticle") = NumeroLotArticle
            End If


            gArticles.Refresh()
        End If
        gListeRecherche.Visible = False
        gArticles.Focus()

    End Sub

    Private Sub gListeRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListeRecherche.KeyUp

        ' dans cet évènenment on teste si l évènenment est due à un clique sur le boutton entrée et si l'utilisateur a choisi 
        ' un article et si le curseur est dans la colonne de designation 
        Try
            Dim StrSQL As String = ""
            Dim cmd As New SqlCommand
            Dim DatePeremption As Date
            Dim NumeroLotArticle As String = ""

            If gListeRecherche.Visible = False Then
                Exit Sub
            End If
            If e.KeyCode = Keys.Back Then
                gArticles.Focus()
                gArticles.Col = 3
                gArticles.MoveLast()
                gArticles.EditActive = True
            End If

            Dim j As Integer
            Dim NumeroLigne As Integer
            Dim DataRowRecherche As DataRow
            If e.KeyCode = Keys.Enter And (gArticles.Col = 5 Or gArticles.Col = 3) Then    'And gArticles.Columns("Designation").Value <> ""
                If dsSortie.Tables("ARTICLE").Rows.Count > 0 Then
                    '--------------------- rehcerche de l'index de la ligne qui contient l'article recherché ---------------
                    For j = 0 To dsSortie.Tables("ARTICLE").Rows.Count - 1
                        DataRowRecherche = dsSortie.Tables("ARTICLE").Rows(j)
                        If DataRowRecherche.Item("CodeArticle") = gListeRecherche.Columns("CodeArticle").Value Then
                            NumeroLigne = j
                        End If
                    Next

                    '------------------- chargement des données ---------------------------------------------- 
                    dr = dsSortie.Tables("ARTICLE").Rows(NumeroLigne)
                    NouvelArticle("NumeroSortie") = RecupereNumero()
                    NouvelArticle("CodeArticle") = dr.Item("CodeArticle")
                    NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    Try
                        NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    Catch ex As Exception
                    End Try

                    NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                    ''''
                    NouvelArticle("QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    If NouvelArticle("QuantiteUnitaire") = 0 Then NouvelArticle("QuantiteUnitaire") = 1
                    ''''

                    NouvelArticle("Qte") = NouvelArticle("QuantiteUnitaire") '1

                    NouvelArticle("Stock") = CalculeStock(dr.Item("CodeArticle"))

                    NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("TotalAchatTTC") = Math.Round(NouvelArticle("PrixAchatTTC") / NouvelArticle("QuantiteUnitaire"), 3)
                    NouvelArticle("PrixVenteHT") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("TotalVenteTTC") = Math.Round(NouvelArticle("PrixVenteTTC") / NouvelArticle("QuantiteUnitaire"), 3)

                    '----------------------- récupération de la date de péremption

                    StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] " + _
                             "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                             "' AND CodeArticle='" + dr.Item("CodeArticle") + _
                             "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        DatePeremption = cmd.ExecuteScalar()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                    If DatePeremption = #12:00:00 AM# Then
                        'NouvelArticle("DatePeremption") = "01/01/1900" 'System.DateTime.Today.Day.ToString + "/" + System.DateTime.Today.Month.ToString + "/" + System.DateTime.Today.Year.ToString  
                    Else
                        NouvelArticle("DatePeremption") = DatePeremption
                    End If


                    ' '' ''----------------------- récupération du numéro de lot
                    StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
                             " WHERE DatePeremptionArticle ='" + NouvelArticle("DatePeremption") + _
                             "' AND CodeArticle='" + dr.Item("CodeArticle") + _
                             "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        NumeroLotArticle = cmd.ExecuteScalar()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try
                    If NumeroLotArticle = "" Then
                        NouvelArticle("NumeroLotArticle") = ""
                    Else
                        NouvelArticle("NumeroLotArticle") = NumeroLotArticle
                    End If


                    gArticles.Refresh()
                End If
                gListeRecherche.Visible = False
                gArticles.Focus()
                gArticles.Col = 6
            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " gListeRecherche_KeyUp", ex.Message, "0000144", "Erreur d'exécution de gListeRecherche_KeyUp ", True, True, True)

        End Try
    End Sub


    Public Sub ChargerDetailArticle(ByVal CodeABarre As String)
        Dim resultat As String
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date '= "1 / 1 / 1900"
        Dim NumeroLotArticle As String = ""
        Dim CodeArticle As String = ""
        Dim Supprime As String = ""
        Dim QteUnitaireArticle As Integer = 0
        Dim CategorieArticle As Integer = 0
        Dim PreparationArticle As Integer = 0
        Dim CodeArticleMereFractionnement As Integer = 0
        Dim QteUnitaireArticleMere As Integer = 0
        Dim StockArticleMere As Integer = 0
        Try
            CodeArticle = RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "CodeABarre", CodeABarre)
            resultat = RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle)
            Supprime = RecupererValeurExecuteScalaire("Supprime", "ARTICLE", "CodeArticle", CodeArticle)

            If resultat <> "" And Supprime = "False" Then

                NouvelArticle("NumeroSortie") = RecupereNumero()
                NouvelArticle("CodeArticle") = CodeArticle
                NouvelArticle("CodeABarre") = CodeABarre
                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)

                'Try
                '    NouvelArticle("CodeForme") = CInt(Val(RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))))
                'Catch ex As Exception

                '    ''Gérer l'Exception
                '    'fMessageException.Show("Sortie", "fSortie", "ChargerDetailArticle", ex.Message, "0000144", "Erreur d'excution de ChargerDetailArticle", True, True, True)
                'End Try

                NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))


                CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", CodeArticle)
                If CategorieArticle = 9 Then
                    PreparationArticle = RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", CodeArticle)
                End If
                If CategorieArticle = 8 Or (CategorieArticle = 9 And PreparationArticle = 1) Then
                    QteUnitaireArticle = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticle)
                Else
                    QteUnitaireArticle = 1
                End If

                If CategorieArticle = 9 And PreparationArticle = 4 Then
                    Try
                        CodeArticleMereFractionnement = RecupererValeurExecuteScalaire("CodeArticleMere", "FRACTIONNEMENT", "CodeArticleFractionne", CodeArticle)
                        QteUnitaireArticleMere = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticleMereFractionnement)
                        StockArticleMere = CalculeStock(CodeArticleMereFractionnement) * QteUnitaireArticleMere
                    Catch ex As Exception
                    End Try
                End If

                ' ''''
                'NouvelArticle("QuantiteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", CodeArticle)
                'If NouvelArticle("QuantiteUnitaire") = 0 Then NouvelArticle("QuantiteUnitaire") = 1
                ' ''''

                NouvelArticle("Qte") = QteUnitaireArticle
                NouvelArticle("QuantiteUnitaire") = QteUnitaireArticle
                NouvelArticle("Stock") = CalculeStock(CodeArticle)

                NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("TotalAchatTTC") = Math.Round(NouvelArticle("PrixAchatTTC") / NouvelArticle("QuantiteUnitaire"), 3)
                NouvelArticle("PrixVenteHT") = RecupererValeurExecuteScalaire("PrixVenteHT", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("TotalVenteTTC") = Math.Round(NouvelArticle("PrixVenteTTC") / NouvelArticle("QuantiteUnitaire"), 3)

                '----------------------- récupération de la date de péremption
                StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] " + _
                         "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                         "' AND CodeArticle='" + CodeArticle + _
                         "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    DatePeremption = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If DatePeremption = #12:00:00 AM# Then
                    'NouvelArticle("DatePeremption") = "01/01/1900"
                Else
                    NouvelArticle("DatePeremption") = DatePeremption
                End If

                '----------------------- récupération du numéro de lot
                StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
                         " WHERE DatePeremptionArticle ='" + NouvelArticle("DatePeremption") + _
                         "' AND CodeArticle='" + CodeArticle + _
                         "' AND QteLotArticle>0 Order by DatePeremptionArticle ASC "

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    NumeroLotArticle = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
                If NumeroLotArticle = "" Then
                    NouvelArticle("NumeroLotArticle") = ""
                Else
                    NouvelArticle("NumeroLotArticle") = NumeroLotArticle
                End If


                gArticles.Refresh()
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
            Else
                gArticles.Columns("CodeABarre").Value = ""
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " ChargerDetailArticle", ex.Message, "0000145", "Erreur d'exécution de ChargerDetailArticle ", True, True, True)

        End Try
    End Sub

    Public Sub CalculerMontants()
        Dim i As Integer = 0

        Dim QteUnitaireArticle As Integer = 1
        Dim CategorieArticle As Integer = 0
        Dim PreparationArticle As Integer = 0

        TotalTTCVente = 0.0
        TotalHTVente = 0.0
        TotalTTCAchat = 0.0
        TotalHTAchat = 0.0
        Try
            Do While i < gArticles.RowCount
                If gArticles(i, "Designation") <> "" Then
                    gArticles(i, "TotalAchatTTC") = Math.Round(gArticles(i, "PrixAchatTTC") * gArticles(i, "Qte"), 3)
                    gArticles(i, "TotalVenteTTC") = Math.Round(gArticles(i, "PrixVenteTTC") * gArticles(i, "Qte"), 3)

                    CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))
                    If CategorieArticle = 9 Then
                        If RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle")) <> "" Then
                            PreparationArticle = RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))
                        End If
                    End If

                    If CategorieArticle = 8 Or (CategorieArticle = 9 And PreparationArticle = 1) Then
                        QteUnitaireArticle = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))
                    Else
                        QteUnitaireArticle = 1
                    End If
                    gArticles(i, "TotalAchatTTC") = Math.Round(gArticles(i, "TotalAchatTTC") / QteUnitaireArticle, 3)
                    gArticles(i, "TotalVenteTTC") = Math.Round(gArticles(i, "TotalVenteTTC") / QteUnitaireArticle, 3)

                    TotalTTCVente += gArticles(i, "TotalVenteTTC")
                    TotalHTVente += Math.Round(gArticles(i, "PrixVenteHT") * gArticles(i, "Qte") / QteUnitaireArticle, 3)
                    TotalTTCAchat += gArticles(i, "TotalAchatTTC")
                    TotalHTAchat += Math.Round(gArticles(i, "PrixAchatHT") * gArticles(i, "Qte") / QteUnitaireArticle, 3)
                End If
                i = i + 1
            Loop

            lValeurVenteTTC.Text = TotalTTCVente
            lValeurAchatTTC.Text = TotalTTCAchat

            lValeurVenteHT.Text = Math.Round(TotalHTVente, 3)
            lValeurAchatHT.Text = Math.Round(TotalHTAchat, 3)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " CalculerMontants", ex.Message, "0000146", "Erreur d'exécution de CalculerMontants ", True, True, True)

        End Try

    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        Try
            'Suivi du scénario 
            fMessageException.Show("Sortie", "fSortie", "bConfirmer_Click", "NoException", "NoError", "Clic sur le bouton Confirmer", False, True, False)

            Dim I As Integer = 0
            Dim cmd As New SqlCommand
            Dim NumeroLot As String = ""
            Dim TestNumeroLot As String = ""
            Dim NouveauNumeroLot As String = ""
            Dim QuantiteLotSansNumero As Integer = 0
            Dim QuantiteLotAInsere As Integer = 0

            Dim StrMajLOT As String = ""
            Dim NumeroDeLotAEnregistrer As String = ""

            Dim ConfirmerEnregistrer As Boolean = False
            Dim CodeOperateur As String = ""
            Dim NombreDesLotsDisponibles As Integer = 0

            'Mode Modif
            If mode = "Modif" Then
                If gArticles.RowCount - 1 = 0 And gArticles(0, "CodeArticle") = "" Then
                    If MsgBox("La liste des détails est vide, Voulez-vous supprimer le Sortie N° : " + lNumeroSortie.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Enregistrer") = MsgBoxResult.Yes Then
                        'Pour appeler le Bouton Annuler
                        bAnnuler.PerformClick()
                        'Pour appeler le bouton de Suppression
                        supprimerSortie(True)
                        Exit Sub
                    Else
                        'Pour appeler le Bouton Annuler
                        bAnnuler.PerformClick()
                        'Quitter la procedure apres faire annuler
                        Exit Sub
                    End If
                End If
            Else 'Mode Ajout
                If dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle") = "" Then
                    MsgBox("Entree Vide !", MsgBoxStyle.Critical, "Erreur")
                    If dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1 < 0 Then
                        bAjouter_Click(sender, e)
                    End If
                    '----------------------
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
                    gArticles.EditActive = True
                    Exit Sub
                End If
            End If

            '-------------------------pour verifier le calcul : si l'utilisateur ne clique pas entree
            '-------------------------sur la cellule qte du dernier ligne la somme TTC sera fausse
            CalculerMontants()
            '-----------------------------------------------------------------------
            '-----------------------------------------------------------------------

            If cmbNature.Text = "" Then
                MsgBox("Veuillez choisir une Nature!", MsgBoxStyle.Critical, "Erreur")
                cmbNature.Focus()
                Exit Sub
            End If

            '------------------------------ demande du mot de passe
            Dim myMotDePasse As New fMotDePasse
            myMotDePasse.ShowDialog()

            ConfirmerEnregistrer = fMotDePasse.Confirmer
            CodeOperateur = fMotDePasse.CodeOperateur

            myMotDePasse.Dispose()
            myMotDePasse.Close()

            If ConfirmerEnregistrer = False Then
                Exit Sub
            End If

            '-------------------------- élémination des lignes vides 
            I = 0
            Do While I < dsSortie.Tables("SORTIE_DETAILS").Rows.Count
                If dsSortie.Tables("SORTIE_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then
                    If dsSortie.Tables("SORTIE_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                        dsSortie.Tables("SORTIE_DETAILS").Rows(I).Delete()
                    End If
                End If
                I = I + 1
            Loop

            '----------------------- controle des Numeros des lots, codes articles : insertion des doublons
            '----------------------- (Violation du clé primaire dans la table achat details)
            'Dim p As Integer = 0
            'Dim q As Integer = 0
            'For p = 0 To dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1
            '    For q = p To dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1
            '        If dsSortie.Tables("SORTIE_DETAILS").Rows(p).RowState <> DataRowState.Deleted Then
            '            If dsSortie.Tables("SORTIE_DETAILS").Rows(p).Item("CodeArticle") = dsSortie.Tables("SORTIE_DETAILS").Rows(q).Item("CodeArticle") And p <> q Then
            '                If dsSortie.Tables("SORTIE_DETAILS").Rows(p).Item("NumeroLotArticle") = dsSortie.Tables("SORTIE_DETAILS").Rows(q).Item("NumeroLotArticle") Then
            '                    MsgBox("l'article " + dsSortie.Tables("SORTIE_DETAILS").Rows(p).Item("Designation") + " apparaît deux fois avec le même numero de lot ! veuillez supprimer et ajouter sa quantité dans l'autre", MsgBoxStyle.Critical, "Erreur")
            '                    Exit Sub
            '                End If
            '            End If
            '        End If
            '    Next
            'Next

            Dim p As Integer = 0
            Dim q As Integer = 0
            For p = 0 To dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1
                For q = p To dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1
                    If gArticles(p, "CodeArticle") = gArticles(q, "CodeArticle") And p <> q And gArticles(p, "CodeArticle") <> "" And gArticles(q, "CodeArticle") <> "" Then
                        If gArticles(p, "DatePeremption").ToString = gArticles(q, "DatePeremption").ToString Then
                            MsgBox("l'article " + gArticles(p, "Designation") + " apparaît deux fois avec le même numero de lot ! veuillez supprimer un et ajouter sa quantité dans l'autre", MsgBoxStyle.Critical, "Erreur")
                            Exit Sub
                        End If
                    End If
                Next
            Next

            '----------------------- contrôle des dates de péremption si il y a un qui est périmé
            For p = 0 To dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1
                If dsSortie.Tables("SORTIE_DETAILS").Rows(p).RowState <> DataRowState.Deleted Then
                    If dsSortie.Tables("SORTIE_DETAILS").Rows(p).Item("DatePeremption").ToString <> "" Then
                        If dsSortie.Tables("SORTIE_DETAILS").Rows(p).Item("DatePeremption") < Date.Today Then
                            MsgBox("l'article " + dsSortie.Tables("SORTIE_DETAILS").Rows(p).Item("Designation") + " admet une date de péremption deja dépassé ! veuillez corriger sa date de péremption", MsgBoxStyle.Critical, "Erreur")
                            Exit Sub
                        End If
                    End If
                End If
            Next
            '------------------------------ enregistrement de SORTIE -------------------------
            '-----------------------------------------------------------------------------------------------
            '-----------------------------------------------------------------------------------------------

            '----------------------------------------
            If mode = "Ajout" Then
                NumeroSortie = RecupereNumero()
            End If

            If mode = "Ajout" Then
                dr = dsSortie.Tables("SORTIE").NewRow()
                With dsSortie
                    dr.Item("NumeroSortie") = NumeroSortie
                    dr.Item("Date") = System.DateTime.Now
                    dr.Item("CodeNature") = cmbNature.SelectedValue
                    dr.Item("ValeurAchatHT") = lValeurAchatHT.Text
                    dr.Item("ValeurAchatTTC") = lValeurAchatTTC.Text
                    dr.Item("ValeurVenteHT") = lValeurVenteHT.Text
                    dr.Item("ValeurVenteTTC") = lValeurVenteTTC.Text
                    dr.Item("CodePersonnel") = CodeOperateur
                    dsSortie.Tables("SORTIE").Rows.Add(dr)
                End With
                Try
                    daSortieEntete.Update(dsSortie, "SORTIE")
                Catch ex As Exception
                    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                    dsSortie.Reset()
                End Try
            Else 'mod modif
                With dsSortie
                    .Tables("SORTIE").Rows(0)("NumeroSortie") = NumeroSortie
                    .Tables("SORTIE").Rows(0)("Date") = System.DateTime.Now
                    .Tables("SORTIE").Rows(0)("CodeNature") = cmbNature.SelectedValue
                    .Tables("SORTIE").Rows(0)("ValeurAchatHT") = lValeurAchatHT.Text
                    .Tables("SORTIE").Rows(0)("ValeurAchatTTC") = lValeurAchatTTC.Text
                    .Tables("SORTIE").Rows(0)("ValeurVenteHT") = lValeurVenteHT.Text
                    .Tables("SORTIE").Rows(0)("ValeurVenteTTC") = lValeurVenteTTC.Text
                    .Tables("SORTIE").Rows(0)("CodePersonnel") = CodeOperateur
                End With
                Try
                    daSortieEntete.Update(dsSortie, "SORTIE")
                Catch ex As Exception
                    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If


            'I = 0
            'Do While I < dsSortie.Tables("SORTIE_DETAILS").Rows.Count

            '    If dsSortie.Tables("SORTIE_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then
            '        If dsSortie.Tables("SORTIE_DETAILS").Rows(I).Item("CodeArticle") = "" Then
            '            dsSortie.Tables("SORTIE_DETAILS").Rows(I).Delete()
            '        Else

            '            If mode = "Modif" Then
            '                dsSortie.Tables("SORTIE_DETAILS").Rows(I).Item("NumeroSortie") = lNumeroSortie.Text
            '            Else
            '                dsSortie.Tables("SORTIE_DETAILS").Rows(I).Item("NumeroSortie") = NumeroSortie
            '            End If

            '        End If

            '    End If
            '    I = I + 1
            'Loop

            'cmdSortie.Connection = ConnectionServeur
            'cmdSortie.CommandText = "Select top(0) * FROM SORTIE_DETAILS"
            'daSortie = New SqlDataAdapter(cmdSortie)
            'daSortie.Fill(dsSortie, "SORTIE_DETAILS")
            'cbSortie = New SqlCommandBuilder(daSortie)


            'Try
            '    daSortie.Update(dsSortie, "SORTIE_DETAILS")

            'Catch ex As Exception

            '    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            '    dsSortie.Reset()
            'End Try

            'Gestion du numero de lot

            '#############################################################################################################################
            '#############################################################################################################################
            '#############################################################################################################################

            Dim J As Integer = 0
            Dim DatePeremption As Date
            Dim NumeroLotArticle As String
            Dim QteDemande As Integer
            Dim QteParLot As Integer
            Dim StrSQL1 As String = ""
            Dim SommeQteLotArticle As Integer = 0

            If mode = "Ajout" Then

                Do While J < gArticles.RowCount

                    Try
                        'Clacule la somme de la Qte d'un  Article
                        StrSQL1 = "SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE CodeArticle= " & Quote(gArticles(J, "CodeArticle"))

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL1
                        SommeQteLotArticle = Val(cmd.ExecuteScalar)

                    Catch ex As Exception

                    End Try


                    StrSQL = " IF NOT EXISTS " + _
                                " (SELECT * FROM LOT_ARTICLE WHERE CodeArticle=" + Quote(gArticles(J, "CodeArticle")) + _
                                " AND NumeroLotArticle= '' )" + _
                                " INSERT INTO LOT_ARTICLE (CodeArticle,NumeroLotArticle,DatePeremptionArticle)" + _
                                " VALUES (" + Quote(gArticles(J, "CodeArticle")) + _
                                " ,'', NULL)"
                    cmd.CommandText = StrSQL
                    Try
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        MsgBox(ex.Message)
                    End Try



                    'Inialiser la Qte a demander
                    QteDemande = gArticles(J, "Qte")


                    '------------------ gestion des numeros de lot
                    If dsSortie.Tables("LOT_ARTICLE") IsNot Nothing Then
                        dsSortie.Tables("LOT_ARTICLE").Clear()
                    End If

                    StrSQL = " SELECT  NumeroLotArticle, DatePeremptionArticle, QteLotArticle, CodeArticle , 1 as OrdreTri FROM LOT_ARTICLE " + _
                    " WHERE DatePeremptionArticle >'" + _
                    System.DateTime.Now.Date.ToString & "'" + _
                    " AND CodeArticle= " & Quote(gArticles(J, "CodeArticle")) & "" + _
                    " AND QteLotArticle > 0   " & "" + _
                    " UNION  SELECT  NumeroLotArticle, DatePeremptionArticle, QteLotArticle, CodeArticle, 0 as OrdreTri  FROM LOT_ARTICLE " + _
                    " WHERE DatePeremptionArticle is null " + _
                    " AND CodeArticle= " & Quote(gArticles(J, "CodeArticle")) & "" + _
                    " AND QteLotArticle > 0   " + _
                    " ORDER BY OrdreTri desc, DatePeremptionArticle "


                    cmdSortie.Connection = ConnectionServeur
                    cmdSortie.CommandText = StrSQL
                    daSortie = New SqlDataAdapter(cmdSortie)
                    daSortie.Fill(dsSortie, "LOT_ARTICLE")
                    cbSortie = New SqlCommandBuilder(daSortie)



                    ' '' ''If dsSortie.Tables("LOT_ARTICLE").Rows.Count = 0 Then

                    ' '' ''End If

                    For k = 0 To dsSortie.Tables("LOT_ARTICLE").Rows.Count - 1

                        QteParLot = dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("QteLotArticle")
                        
                        If (dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString = "" And
                                dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "") Then

                            If QteDemande <= QteParLot Then

                                NumeroLotArticle = ""

                                DatePeremption = #12:00:00 AM#

                                StrSQL = "INSERT INTO SORTIE_DETAILS "
                                StrSQL = StrSQL & "(""NumeroSortie"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""CodeForme"",""Designation"",""Qte"",""PrixAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""PrixVenteHT"",""PrixVenteTTC"",""TotalVenteTTC"",""DatePeremption"",""Stock"") "
                                StrSQL = StrSQL & " VALUES('"
                                StrSQL = StrSQL & NumeroSortie
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & NumeroLotArticle
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeABarre").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                                StrSQL = StrSQL & "',"
                                StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & QteDemande.ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixVenteHT").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixVenteTTC").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixVenteTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "',"
                                If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                    StrSQL = StrSQL & "'" & DatePeremption & "'"
                                Else
                                    StrSQL = StrSQL & "NULL"
                                End If
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & gArticles(J, "Stock").ToString
                                StrSQL = StrSQL & "')"

                                QteDemande = 0

                                cmd.Connection = ConnectionServeur
                                cmd.CommandText = StrSQL
                                Try
                                    cmd.ExecuteNonQuery()

                                Catch ex As Exception
                                    Console.WriteLine(ex.Message)
                                End Try

                            Else

                                NumeroLotArticle = ""

                                DatePeremption = #12:00:00 AM#


                                StrSQL = "INSERT INTO SORTIE_DETAILS "
                                StrSQL = StrSQL & "(""NumeroSortie"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""CodeForme"",""Designation"",""Qte"",""PrixAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""PrixVenteHT"",""PrixVenteTTC"",""TotalVenteTTC"",""DatePeremption"",""Stock"") "
                                StrSQL = StrSQL & " VALUES('"
                                StrSQL = StrSQL & NumeroSortie
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & NumeroLotArticle
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeABarre").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                                StrSQL = StrSQL & "',"
                                StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & QteParLot
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixVenteHT").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixVenteTTC").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixVenteTTC") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "',"
                                If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                    StrSQL = StrSQL & "'" & DatePeremption & "'"
                                Else
                                    StrSQL = StrSQL & "NULL"
                                End If
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & gArticles(J, "Stock").ToString
                                StrSQL = StrSQL & "')"

                                cmd.Connection = ConnectionServeur
                                cmd.CommandText = StrSQL
                                Try
                                    cmd.ExecuteNonQuery()

                                Catch ex As Exception
                                    Console.WriteLine(ex.Message)
                                End Try

                                QteDemande = QteDemande - QteParLot

                            End If

                            Exit For

                        End If

                        If QteDemande <= QteParLot Then


                            NumeroLotArticle = dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString

                            If dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "" Then
                                DatePeremption = #12:00:00 AM#

                            Else
                                DatePeremption = dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString
                            End If


                            StrSQL = "INSERT INTO SORTIE_DETAILS "
                            StrSQL = StrSQL & "(""NumeroSortie"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""CodeForme"",""Designation"",""Qte"",""PrixAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""PrixVenteHT"",""PrixVenteTTC"",""TotalVenteTTC"",""DatePeremption"",""Stock"") "
                            StrSQL = StrSQL & " VALUES('"
                            StrSQL = StrSQL & NumeroSortie
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & NumeroLotArticle
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeABarre").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                            StrSQL = StrSQL & "',"
                            StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & QteDemande
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixVenteHT").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixVenteTTC").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixVenteTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "',"
                            If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                StrSQL = StrSQL & "'" & DatePeremption & "'"
                            Else
                                StrSQL = StrSQL & "NULL"
                            End If
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & gArticles(J, "Stock").ToString
                            StrSQL = StrSQL & "')"

                            QteDemande = 0

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try
                                cmd.ExecuteNonQuery()

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                            Exit For

                        Else

                            QteDemande = QteDemande - QteParLot

                            NumeroLotArticle = dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString

                            If dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "" Then
                                DatePeremption = #12:00:00 AM#

                            Else
                                DatePeremption = dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString
                            End If

                            StrSQL = "INSERT INTO SORTIE_DETAILS "
                            StrSQL = StrSQL & "(""NumeroSortie"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""CodeForme"",""Designation"",""Qte"",""PrixAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""PrixVenteHT"",""PrixVenteTTC"",""TotalVenteTTC"",""DatePeremption"",""Stock"") "
                            StrSQL = StrSQL & " VALUES('"
                            StrSQL = StrSQL & NumeroSortie
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & NumeroLotArticle
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeABarre").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                            StrSQL = StrSQL & "',"
                            StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & QteParLot
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixVenteHT").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixVenteTTC").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixVenteTTC") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "',"
                            If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                StrSQL = StrSQL & "'" & DatePeremption & "'"
                            Else
                                StrSQL = StrSQL & "NULL"
                            End If
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & gArticles(J, "Stock").ToString
                            StrSQL = StrSQL & "')"

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try
                                cmd.ExecuteNonQuery()

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                        End If

                    Next

                    If QteDemande > 0 Then

                        NumeroLotArticle = ""

                        DatePeremption = #12:00:00 AM#



                        StrSQL = "INSERT INTO SORTIE_DETAILS "
                        StrSQL = StrSQL & "(""NumeroSortie"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""CodeForme"",""Designation"",""Qte"",""PrixAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""PrixVenteHT"",""PrixVenteTTC"",""TotalVenteTTC"",""DatePeremption"",""Stock"") "
                        StrSQL = StrSQL & " VALUES('"
                        StrSQL = StrSQL & NumeroSortie
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & NumeroLotArticle
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeABarre").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                        StrSQL = StrSQL & "',"
                        StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & QteDemande
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixVenteHT").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixVenteTTC").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixVenteTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "',"
                        If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                            StrSQL = StrSQL & "'" & DatePeremption & "'"
                        Else
                            StrSQL = StrSQL & "NULL"
                        End If
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & gArticles(J, "Stock").ToString
                        StrSQL = StrSQL & "')"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()

                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try


                    ElseIf QteDemande < 0 And SommeQteLotArticle < 0 Then

                        NumeroLotArticle = ""

                        DatePeremption = #12:00:00 AM#



                        StrSQL = "INSERT INTO SORTIE_DETAILS "
                        StrSQL = StrSQL & "(""NumeroSortie"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""CodeForme"",""Designation"",""Qte"",""PrixAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""PrixVenteHT"",""PrixVenteTTC"",""TotalVenteTTC"",""DatePeremption"",""Stock"") "
                        StrSQL = StrSQL & " VALUES('"
                        StrSQL = StrSQL & NumeroSortie
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & NumeroLotArticle
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeABarre").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                        StrSQL = StrSQL & "',"
                        StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & QteDemande
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixVenteHT").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixVenteTTC").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixVenteTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "',"
                        If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                            StrSQL = StrSQL & "'" & DatePeremption & "'"
                        Else
                            StrSQL = StrSQL & "NULL"
                        End If
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & gArticles(J, "Stock").ToString
                        StrSQL = StrSQL & "')"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()

                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try
                    End If
                    J = J + 1
                Loop
            ElseIf mode = "Modif" Then
                cmdSortie.Connection = ConnectionServeur
                cmdSortie.CommandText = "DELETE FROM SORTIE_DETAILS WHERE NumeroSortie ='" + lNumeroSortie.Text + "'"
                cmdSortie.ExecuteNonQuery()

                '------------------ gestion des numeros de lot
                Do While J < gArticles.RowCount
                    Try
                        'Clacule la somme de la Qte d'un  Article
                        StrSQL1 = "SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE CodeArticle= " & Quote(gArticles(J, "CodeArticle"))

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL1
                        SommeQteLotArticle = Val(cmd.ExecuteScalar)

                    Catch ex As Exception
                    End Try

                    If dsSortie.Tables("LOT_ARTICLE") IsNot Nothing Then
                        dsSortie.Tables("LOT_ARTICLE").Clear()
                    End If
                    StrSQL = " SELECT  NumeroLotArticle, DatePeremptionArticle, QteLotArticle, CodeArticle , 1 as OrdreTri FROM LOT_ARTICLE " + _
                         " WHERE DatePeremptionArticle >'" + _
                         System.DateTime.Now.Date.ToString & "'" + _
                         " AND CodeArticle= " & Quote(gArticles(J, "CodeArticle")) & "" + _
                         " AND QteLotArticle > 0   " & "" + _
                         " UNION  SELECT  NumeroLotArticle, DatePeremptionArticle, QteLotArticle, CodeArticle, 0 as OrdreTri  FROM LOT_ARTICLE " + _
                         " WHERE DatePeremptionArticle is null " + _
                         " AND CodeArticle= " & Quote(gArticles(J, "CodeArticle")) & "" + _
                         " AND QteLotArticle > 0   " + _
                         " ORDER BY OrdreTri desc, DatePeremptionArticle "


                    cmdSortie.Connection = ConnectionServeur
                    cmdSortie.CommandText = StrSQL
                    daSortie = New SqlDataAdapter(cmdSortie)
                    daSortie.Fill(dsSortie, "LOT_ARTICLE")
                    cbSortie = New SqlCommandBuilder(daSortie)

                    'Inialiser la Qte a demander
                    QteDemande = gArticles(J, "Qte")

                    For k = 0 To dsSortie.Tables("LOT_ARTICLE").Rows.Count - 1

                        QteParLot = dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("QteLotArticle")

                        If (dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString = "" And
                                dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "") Then

                            If QteDemande <= QteParLot Then

                                NumeroLotArticle = ""

                                DatePeremption = #12:00:00 AM#

                                StrSQL = "INSERT INTO SORTIE_DETAILS "
                                StrSQL = StrSQL & "(""NumeroSortie"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""CodeForme"",""Designation"",""Qte"",""PrixAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""PrixVenteHT"",""PrixVenteTTC"",""TotalVenteTTC"",""DatePeremption"",""Stock"") "
                                StrSQL = StrSQL & " VALUES('"
                                StrSQL = StrSQL & lNumeroSortie.Text
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & NumeroLotArticle
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeABarre").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                                StrSQL = StrSQL & "',"
                                StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & QteDemande.ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixVenteHT").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixVenteTTC").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixVenteTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "',"
                                If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                    StrSQL = StrSQL & "'" & DatePeremption & "'"
                                Else
                                    StrSQL = StrSQL & "NULL"
                                End If
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & gArticles(J, "Stock").ToString
                                StrSQL = StrSQL & "')"

                                QteDemande = 0

                                cmd.Connection = ConnectionServeur
                                cmd.CommandText = StrSQL
                                Try
                                    cmd.ExecuteNonQuery()

                                Catch ex As Exception
                                    Console.WriteLine(ex.Message)
                                End Try

                            Else

                                NumeroLotArticle = ""

                                DatePeremption = #12:00:00 AM#


                                StrSQL = "INSERT INTO SORTIE_DETAILS "
                                StrSQL = StrSQL & "(""NumeroSortie"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""CodeForme"",""Designation"",""Qte"",""PrixAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""PrixVenteHT"",""PrixVenteTTC"",""TotalVenteTTC"",""DatePeremption"",""Stock"") "
                                StrSQL = StrSQL & " VALUES('"
                                StrSQL = StrSQL & lNumeroSortie.Text
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & NumeroLotArticle
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeABarre").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                                StrSQL = StrSQL & "',"
                                StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & QteParLot
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixVenteHT").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & gArticles(J, "PrixVenteTTC").ToString
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixVenteTTC") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                                StrSQL = StrSQL & "',"
                                If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                    StrSQL = StrSQL & "'" & DatePeremption & "'"
                                Else
                                    StrSQL = StrSQL & "NULL"
                                End If
                                StrSQL = StrSQL & ",'"
                                StrSQL = StrSQL & gArticles(J, "Stock").ToString
                                StrSQL = StrSQL & "')"

                                cmd.Connection = ConnectionServeur
                                cmd.CommandText = StrSQL
                                Try
                                    cmd.ExecuteNonQuery()

                                Catch ex As Exception
                                    Console.WriteLine(ex.Message)
                                End Try

                                QteDemande = QteDemande - QteParLot

                            End If

                            Exit For

                        End If

                        If QteDemande <= QteParLot Then


                            NumeroLotArticle = dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString

                            If dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "" Then
                                DatePeremption = #12:00:00 AM#

                            Else
                                DatePeremption = dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString
                            End If


                            StrSQL = "INSERT INTO SORTIE_DETAILS "
                            StrSQL = StrSQL & "(""NumeroSortie"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""CodeForme"",""Designation"",""Qte"",""PrixAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""PrixVenteHT"",""PrixVenteTTC"",""TotalVenteTTC"",""DatePeremption"",""Stock"") "
                            StrSQL = StrSQL & " VALUES('"
                            StrSQL = StrSQL & lNumeroSortie.Text
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & NumeroLotArticle
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeABarre").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                            StrSQL = StrSQL & "',"
                            StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & QteDemande
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixVenteHT").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixVenteTTC").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixVenteTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "',"
                            If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                StrSQL = StrSQL & "'" & DatePeremption & "'"
                            Else
                                StrSQL = StrSQL & "NULL"
                            End If
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & gArticles(J, "Stock").ToString
                            StrSQL = StrSQL & "')"

                            QteDemande = 0

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try
                                cmd.ExecuteNonQuery()

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                            Exit For

                        Else

                            QteDemande = QteDemande - QteParLot

                            NumeroLotArticle = dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString

                            If dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "" Then
                                DatePeremption = #12:00:00 AM#

                            Else
                                DatePeremption = dsSortie.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString
                            End If

                            StrSQL = "INSERT INTO SORTIE_DETAILS "
                            StrSQL = StrSQL & "(""NumeroSortie"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""CodeForme"",""Designation"",""Qte"",""PrixAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""PrixVenteHT"",""PrixVenteTTC"",""TotalVenteTTC"",""DatePeremption"",""Stock"") "
                            StrSQL = StrSQL & " VALUES('"
                            StrSQL = StrSQL & lNumeroSortie.Text
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & NumeroLotArticle
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeABarre").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                            StrSQL = StrSQL & "',"
                            StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & QteParLot
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixVenteHT").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & gArticles(J, "PrixVenteTTC").ToString
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixVenteTTC") * QteParLot / gArticles(J, "QuantiteUnitaire"), 3))
                            StrSQL = StrSQL & "',"
                            If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                                StrSQL = StrSQL & "'" & DatePeremption & "'"
                            Else
                                StrSQL = StrSQL & "NULL"
                            End If
                            StrSQL = StrSQL & ",'"
                            StrSQL = StrSQL & gArticles(J, "Stock").ToString
                            StrSQL = StrSQL & "')"

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try
                                cmd.ExecuteNonQuery()

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                        End If

                    Next

                    If QteDemande > 0 Then

                        NumeroLotArticle = ""

                        DatePeremption = #12:00:00 AM#



                        StrSQL = "INSERT INTO SORTIE_DETAILS "
                        StrSQL = StrSQL & "(""NumeroSortie"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""CodeForme"",""Designation"",""Qte"",""PrixAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""PrixVenteHT"",""PrixVenteTTC"",""TotalVenteTTC"",""DatePeremption"",""Stock"") "
                        StrSQL = StrSQL & " VALUES('"
                        StrSQL = StrSQL & lNumeroSortie.Text
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & NumeroLotArticle
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeABarre").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                        StrSQL = StrSQL & "',"
                        StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & QteDemande
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixVenteHT").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixVenteTTC").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixVenteTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "',"
                        If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                            StrSQL = StrSQL & "'" & DatePeremption & "'"
                        Else
                            StrSQL = StrSQL & "NULL"
                        End If
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & gArticles(J, "Stock").ToString
                        StrSQL = StrSQL & "')"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()

                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try

                    ElseIf QteDemande < 0 And SommeQteLotArticle < 0 Then

                        NumeroLotArticle = ""

                        DatePeremption = #12:00:00 AM#



                        StrSQL = "INSERT INTO SORTIE_DETAILS "
                        StrSQL = StrSQL & "(""NumeroSortie"",""CodeArticle"",""NumeroLotArticle"",""CodeABarre"",""CodeForme"",""Designation"",""Qte"",""PrixAchatHT"",""PrixAchatTTC"",""TotalAchatTTC"",""PrixVenteHT"",""PrixVenteTTC"",""TotalVenteTTC"",""DatePeremption"",""Stock"") "
                        StrSQL = StrSQL & " VALUES('"
                        StrSQL = StrSQL & NumeroSortie
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeArticle").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & NumeroLotArticle
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeABarre").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "CodeForme").ToString
                        StrSQL = StrSQL & "',"
                        StrSQL = StrSQL & Quote(gArticles(J, "Designation").ToString)
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & QteDemande
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatHT").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixAchatTTC").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixAchatTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixVenteHT").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & gArticles(J, "PrixVenteTTC").ToString
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & (Math.Round(gArticles(J, "PrixVenteTTC") * QteDemande / gArticles(J, "QuantiteUnitaire"), 3))
                        StrSQL = StrSQL & "',"
                        If (DatePeremption.ToString() <> "01/01/0001 00:00:00") Then
                            StrSQL = StrSQL & "'" & DatePeremption & "'"
                        Else
                            StrSQL = StrSQL & "NULL"
                        End If
                        StrSQL = StrSQL & ",'"
                        StrSQL = StrSQL & gArticles(J, "Stock").ToString
                        StrSQL = StrSQL & "')"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()

                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try


                    End If

                    J = J + 1
                Loop


            End If
            '#############################################################################################################################
            '#############################################################################################################################
            '#############################################################################################################################




            'si le mode Ajout
            If mode = "Ajout" Then

                'Appel Pour selectionner le dernier ligne 
                NumeroligneSortie = selectionDernierLigneSortie()

            End If

            'changer le mode en consultation
            mode = "Consultation"

            'Appel pour charger les information en question
            ChargerSortie(NumeroligneSortie)


            'initialisation des btns
            initBoutons()

            gListeRecherche.Visible = False
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " bConfirmer_Click", ex.Message, "0000147", "Erreur d'exécution de bConfirmer_Click ", True, True, True)

        End Try
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Try
            'Suivi du scénario
            fMessageException.Show("Sortie", "fSortie", "bAnnuler_Click", "NoException", "NoError", "Clic sur le bouton Annuler", False, True, False)

            If dsSortie.Tables("SORTIE_DETAILS").Rows.Count > 0 And gArticles(0, "CodeArticle") <> "" Then
                If MsgBox("Voulez vous vraiment Annuler cette Sortie ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Annuler Sortie") = MsgBoxResult.No Then
                    Exit Sub
                End If
            End If

            '--------changer le mode en concultation

            mode = "Consultation"


            'Refreche liste Sortie

            ChargerSortie(NumeroligneSortie)

            'Pour initialiser les btns suivant le mode et l'etat du table dans la BD
            initBoutons()

            'pour rendre invisible la liste de recherche
            gListeRecherche.Visible = False

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " bAnnuler_Click", ex.Message, "0000148", "Erreur d'exécution de bAnnuler_Click ", True, True, True)

        End Try
    End Sub

    Private Sub cmbNature_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbNature.KeyDown
        Try
            If e.KeyCode = Keys.Enter And cmbNature.WillChangeToText = "" And cmbNature.Text <> "" Then

                If MsgBox("Nature  ' " + cmbNature.Text + " '  inexistant, voulez-vous le créer ?", MsgBoxStyle.OkCancel, "Erreur") = MsgBoxResult.Ok Then
                    Dim CodeNatureSortie As String = cmbNature.Text

                    cmdSortie.Connection = ConnectionServeur
                    cmdSortie.CommandText = "INSERT INTO NATURE_SORTIE VALUES ((SELECT MAX(CodeNatureSortie) FROM NATURE_SORTIE) + 1, " + Quote(cmbNature.Text) + ", 0) "
                    cmdSortie.ExecuteNonQuery()

                    initNatureSortie()
                    cmbNature.Text = CodeNatureSortie


                End If

            End If

            If e.KeyCode = Keys.Enter Then
                cmbNature.Text = cmbNature.WillChangeToText


                If cmbNature.Text.ToUpper() = "PERIMES" Or cmbNature.Text.ToUpper() = "PERIME" Or cmbNature.Text.ToUpper() = "PERIMEE" Or cmbNature.Text.ToUpper() = "PERIMEES" Then
                    If MsgBox("Recherche Automatique des articles Périmés? ", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Message") = MsgBoxResult.Yes Then
                        rechercheArticlesPerimes()
                    End If
                    dtpDebut.Visible = True
                    lDu.Visible = True
                    dtpFin.Visible = True
                    Label7.Visible = True
                Else
                    dtpDebut.Visible = False
                    lDu.Visible = False
                    dtpFin.Visible = False
                    Label7.Visible = False
                End If

                gArticles.Focus()
                gArticles.Col = 2
                gArticles.EditActive = True
            Else
                cmbNature.OpenCombo()
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " cmbNature_KeyDown", ex.Message, "0000149", "Erreur d'exécution de cmbNature_KeyDown ", True, True, True)

        End Try
    End Sub

    Private Sub rechercheArticlesPerimes()
        Dim strSql As String

        Try
            dsSortie.Tables("SORTIE_DETAILS").Clear()
        Catch ex As Exception
        End Try

        Dim Condition As String = "DatePeremptionArticle < GETDATE()"

        If dtpDebut.Text <> "" And dtpFin.Text <> "" Then
            Condition = "CONVERT(Date, DatePeremptionArticle) BETWEEN " & Quote(dtpDebut.Text) & " AND " & Quote(dtpFin.Text)
        End If

        strSql = "SELECT '' as NumeroSortie," + _
                     "ARTICLE.CodeArticle," + _
                     "CodeABarre," + _
                     "Designation," + _
                     "ARTICLE.CodeForme," + _
                     "FORME_ARTICLE.LibelleForme," + _
                     "(SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE LOT_ARTICLE.CodeArticle = ARTICLE.CodeArticle AND (CAST(DatePeremptionArticle AS Date) <> '01/01/1900') AND (DatePeremptionArticle IS NOT NULL) AND " & Condition & ") as Qte," + _
                     "NULL as DatePeremption," + _
                     "'' as NumeroLotArticle," + _
                     "PrixAchatHT," + _
                     "PrixAchatTTC," + _
                     "PrixAchatTTC as TotalAchatTTC," + _
                     "PrixVenteHT," + _
                     "PrixVenteTTC," + _
                     "PrixVenteTTC as TotalVenteTTC," + _
                     "(SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE LOT_ARTICLE.CodeArticle = ARTICLE.CodeArticle ) as Stock, " + _
                     "case when QuantiteUnitaire = 0 then 1 else QuantiteUnitaire end as QuantiteUnitaire , " + _
                     "'' AS Vide " + _
                     " FROM ARTICLE " + _
                     " JOIN FORME_ARTICLE ON FORME_ARTICLE.CodeForme = ARTICLE.CodeForme  " + _
                     " INNER JOIN (SELECT CodeArticle, SUM(QteLotArticle) AS QuantitePerime, " + _
                     "			   MAX(CAST(DatePeremptionArticle AS Date)) AS DatePeremptionArticle " + _
                     "             FROM dbo.LOT_ARTICLE " + _
                     "             WHERE (CAST(DatePeremptionArticle AS Date) <> '01/01/1900') AND (DatePeremptionArticle IS NOT NULL) AND " & Condition & " " + _
                     "             GROUP BY CodeArticle " + _
                     "            ) AS StockPerime ON dbo.ARTICLE.CodeArticle = StockPerime.CodeArticle " + _
                     " WHERE  Supprime = 0 AND " + _
                     "(SELECT SUM(QteLotArticle) FROM LOT_ARTICLE WHERE LOT_ARTICLE.CodeArticle = ARTICLE.CodeArticle AND (CAST(DatePeremptionArticle AS Date) <> '01/01/1900') AND (DatePeremptionArticle IS NOT NULL) AND " & Condition & " )> 0"

        'and DatePeremptionArticle < GETDATE() 

        '"FROM Vue_ListeArticlePerime " + _
        '"WHERE DatePeremptionArticle < GETDATE()"

       

        cmdSortieDetails.Connection = ConnectionServeur
        cmdSortieDetails.CommandText = strSql '"SELECT '' as NumeroSortie, ARTICLE.CodeArticle, ARTICLE.CodeABarre, Designation, ARTICLE.CodeForme, FORME_ARTICLE.LibelleForme,  FROM ARTICLE JOIN LOT_ARTICLE ON LOT_ARTICLE.CodeArticle = ARTICLE.CodeArticle WHERE   "
        daSortieDetails = New SqlDataAdapter(cmdSortieDetails)
        daSortieDetails.Fill(dsSortie, "SORTIE_DETAILS")
        cbSortieDetails = New SqlCommandBuilder(daSortieDetails)

        CalculerMontants()
        gArticles.Col = 3
    End Sub

    Private Sub bLast_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLast.Click

        Try
            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner la derniere ligne 
            NumeroligneSortie = selectionDernierLigneSortie()

            'Appel pour charger les information de l'Achat en question
            ChargerSortie(NumeroligneSortie)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " bLast_Click", ex.Message, "0000150", "Erreur d'exécution de bLast_Click ", True, True, True)

        End Try

    End Sub


    Private Sub bFirst_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFirst.Click
        Try
            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner le dernier ligne 
            selectionPrmierLigneSortie()

            'Appel pour charger les information de Sortie en question
            ChargerSortie(NumeroligneSortie)
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " bFirst_Click", ex.Message, "0000151", "Erreur d'exécution de bFirst_Click ", True, True, True)

        End Try
    End Sub

    Private Sub bNext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bNext.Click

        Try
            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner l'element suivant 
            selectionLigneSortieSuivant()

            'Appel pour charger les information de Sortie en question
            ChargerSortie(NumeroligneSortie)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " bNext_Click", ex.Message, "0000152", "Erreur d'exécution de bNext_Click ", True, True, True)

        End Try
    End Sub

    Private Sub bPrevious_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPrevious.Click
        Try
            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner l'element precedent 
            selectionLigneSortiePrecedent()

            'Appel pour charger les information de Sortie en question
            ChargerSortie(NumeroligneSortie)
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " bPrevious_Click", ex.Message, "0000153", "Erreur d'exécution de bPrevious_Click ", True, True, True)

        End Try
    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click
        supprimerSortie(False)
    End Sub
    Private Sub supprimerSortie(ByVal msgShow As Boolean)
        Try
            'Suivi du scénario 
            fMessageException.Show("Sortie", "fSortie", "bSupprimer_Click", "NoException", "NoError", "Clic sur le bouton Supprimer", False, True, False)

            Dim NumeroSortie As String
            Dim ConfirmerEnregistrer As Boolean = False
            Dim CodeOperateur As String = ""
            NumeroSortie = lNumeroSortie.Text

            'Si le mode est Ajout ou Modif
            If mode = "Ajout" Or mode = "Modif" Then

                'Si  la liste est vide, quitter la procedure
                If gArticles.RowCount = 0 Then
                    Exit Sub
                End If


                If gArticles.RowCount > 0 Then

                    'Test si la lign est NEW ADDED et elle est vide
                    If gArticles(gArticles.Row, ("CodeArticle")) <> "" Then
                        gArticles.Delete()
                        CalculerMontants()
                    End If

                    If gArticles.RowCount <= 0 Then
                        bAjouter.PerformClick()

                    End If

                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))

                    Exit Sub
                Else

                    CalculerMontants()

                End If

            Else 'mode  consultation
                If NumeroSortie = "" Then
                    MsgBox("Aucun Sortie à supprimer !", MsgBoxStyle.Critical, "Information")
                    Exit Sub
                Else

                    If msgShow = False Then

                        If MsgBox("Voulez vous vraiment supprimer cet Sortie " + lNumeroSortie.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then

                            '-------- demande du mot de passe

                            Dim myMotDePasse As New fMotDePasse
                            myMotDePasse.ShowDialog()
                            ConfirmerEnregistrer = fMotDePasse.Confirmer
                            CodeOperateur = fMotDePasse.CodeOperateur
                            myMotDePasse.Dispose()
                            myMotDePasse.Close()
                            If ConfirmerEnregistrer = False Then
                                Exit Sub

                            End If

                            Try
                                'delete de la table Sortie details
                                cmdSortie.Connection = ConnectionServeur
                                cmdSortie.CommandText = "DELETE FROM SORTIE_DETAILS WHERE NumeroSortie ='" + NumeroSortie + "'"
                                cmdSortie.ExecuteNonQuery()
                                'delete de la table Sortie
                                cmdSortie.Connection = ConnectionServeur
                                cmdSortie.CommandText = "DELETE FROM SORTIE WHERE NumeroSortie ='" + NumeroSortie + "'"
                                cmdSortie.ExecuteNonQuery()

                                'Ma nouvelle position

                                If NumeroligneSortie > 1 Then
                                    NumeroligneSortie = NumeroligneSortie - 1
                                ElseIf NumeroligneSortie = 1 Then
                                    initLoadControl()

                                End If
                                'charger la nouvelle position
                                ChargerSortie(NumeroligneSortie)

                            Catch ex As Exception

                                'Gérer l'Exception
                                fMessageException.Show("Sortie", "fSortie", "supprimerSortie", ex.Message, "0000156", "Erreur lors d'executer la requette", True, True, True)

                            End Try

                            MsgBox("Sortie supprimé !", MsgBoxStyle.Information, "Information")

                        Else

                            Exit Sub

                        End If

                    Else

                        '-------- demande du mot de passe

                        Dim myMotDePasse As New fMotDePasse
                        myMotDePasse.ShowDialog()
                        ConfirmerEnregistrer = fMotDePasse.Confirmer
                        CodeOperateur = fMotDePasse.CodeOperateur
                        myMotDePasse.Dispose()
                        myMotDePasse.Close()
                        If ConfirmerEnregistrer = False Then
                            Exit Sub

                        End If

                        Try
                            'delete de la table Sortie details
                            cmdSortie.Connection = ConnectionServeur
                            cmdSortie.CommandText = "DELETE FROM SORTIE_DETAILS WHERE NumeroSortie ='" + NumeroSortie + "'"
                            cmdSortie.ExecuteNonQuery()
                            'delete de la table Sortie
                            cmdSortie.Connection = ConnectionServeur
                            cmdSortie.CommandText = "DELETE FROM SORTIE WHERE NumeroSortie ='" + NumeroSortie + "'"
                            cmdSortie.ExecuteNonQuery()

                            'Ma nouvelle position

                            If NumeroligneSortie > 1 Then
                                NumeroligneSortie = NumeroligneSortie - 1
                            ElseIf NumeroligneSortie = 1 Then
                                initLoadControl()

                            End If
                            'charger la nouvelle position
                            ChargerSortie(NumeroligneSortie)

                        Catch ex As Exception

                            'Gérer l'Exception
                            fMessageException.Show("Sortie", "fSortie", "supprimerSortie", ex.Message, "0000155", "Erreur lors d'executer la requette", True, True, True)

                        End Try

                        MsgBox("Sortie supprimé !", MsgBoxStyle.Information, "Information")

                        Exit Sub

                    End If

                End If

            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " supprimerSortie", ex.Message, "0000154", "Erreur d'exécution de supprimerSortie ", True, True, True)

        End Try
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        Try

            If mode = "Consultation" Then
                fMain.Tab.SelectedTab.Dispose()
                Exit Sub
            End If

            If dsSortie.Tables("SORTIE_DETAILS").Rows.Count > 0 And gArticles(0, "CodeArticle") <> "" And mode <> "Consultation" Then
                If MsgBox("Voulez vous vraiment Fermer cette Sortie ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Fermer Sortie") = MsgBoxResult.No Then
                    Exit Sub
                End If
            End If

            fMain.Tab.SelectedTab.Dispose()
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " bQuitter_Click", ex.Message, "0000157", "Erreur d'exécution de bQuitter_Click ", True, True, True)

        End Try
    End Sub


    Private Sub lValeurVenteTTC_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lValeurVenteTTC.TextChanged
        Try
            lValeurVenteTTC.Text = lValeurVenteTTC.Text
            If lValeurVenteTTC.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lValeurVenteTTC.Text, ".")
                If lValeurVenteTTC.Text.Length - x = 1 Then
                    lValeurVenteTTC.Text = lValeurVenteTTC.Text + ("00")
                ElseIf lValeurVenteTTC.Text.Length - x = 2 Then
                    lValeurVenteTTC.Text = lValeurVenteTTC.Text + ("0")
                End If
            Else
                lValeurVenteTTC.Text = lValeurVenteTTC.Text + ".000"
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " lValeurVenteTTC_TextChanged", ex.Message, "0000158", "Erreur d'exécution de lValeurVenteTTC_TextChanged ", True, True, True)

        End Try
    End Sub


    Private Sub lValeurAchatTTC_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lValeurAchatTTC.TextChanged
        Try
            lValeurAchatTTC.Text = lValeurAchatTTC.Text
            If lValeurAchatTTC.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lValeurAchatTTC.Text, ".")
                If lValeurAchatTTC.Text.Length - x = 1 Then
                    lValeurAchatTTC.Text = lValeurAchatTTC.Text + ("00")
                ElseIf lValeurAchatTTC.Text.Length - x = 2 Then
                    lValeurAchatTTC.Text = lValeurAchatTTC.Text + ("0")
                End If
            Else
                lValeurAchatTTC.Text = lValeurAchatTTC.Text + ".000"
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " lValeurAchatTTC_TextChanged", ex.Message, "0000159", "Erreur d'exécution de lValeurAchatTTC_TextChanged ", True, True, True)

        End Try
    End Sub


    Private Sub lValeurVenteHT_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lValeurVenteHT.TextChanged
        Try
            lValeurVenteHT.Text = lValeurVenteHT.Text
            If lValeurVenteHT.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lValeurVenteHT.Text, ".")
                If lValeurVenteHT.Text.Length - x = 1 Then
                    lValeurVenteHT.Text = lValeurVenteHT.Text + ("00")
                ElseIf lValeurVenteHT.Text.Length - x = 2 Then
                    lValeurVenteHT.Text = lValeurVenteHT.Text + ("0")
                End If
            Else
                lValeurVenteHT.Text = lValeurVenteHT.Text + ".000"
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " lValeurVenteHT_TextChanged", ex.Message, "0000160", "Erreur d'exécution de lValeurVenteHT_TextChanged ", True, True, True)

        End Try
    End Sub

    Private Sub lValeurAchatHT_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lValeurAchatHT.TextChanged
        Try
            lValeurAchatHT.Text = lValeurAchatHT.Text
            If lValeurAchatHT.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lValeurAchatHT.Text, ".")
                If lValeurAchatHT.Text.Length - x = 1 Then
                    lValeurAchatHT.Text = lValeurAchatHT.Text + ("00")
                ElseIf lValeurAchatHT.Text.Length - x = 2 Then
                    lValeurAchatHT.Text = lValeurAchatHT.Text + ("0")
                End If
            Else
                lValeurAchatHT.Text = lValeurAchatHT.Text + ".000"
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " lValeurAchatHT_TextChanged", ex.Message, "0000161", "Erreur d'exécution de lValeurAchatHT_TextChanged ", True, True, True)

        End Try
    End Sub

    Private Sub gArticles_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gArticles.MouseClick
        '---------------------------------- verouillage des lignes déja confirmées -------------------------
        Try
            If mode = "Ajout" Then
                If gArticles.Row <> dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1 Then
                    gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
                    gArticles.Splits(0).DisplayColumns("Designation").Locked = True
                ElseIf gArticles.Row = dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1 Then
                    gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
                    gArticles.Splits(0).DisplayColumns("Designation").Locked = False
                End If
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " gArticles_MouseClick", ex.Message, "0000162", "Erreur d'exécution de gArticles_MouseClick ", True, True, True)

        End Try
    End Sub

    Private Sub gListeRecherche_UnboundColumnFetch(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gListeRecherche.UnboundColumnFetch
        Dim y As String
        Dim CategorieArticle As Integer = 0
        Dim PreparationArticle As Integer = 0
        Dim CodeArticleMereFractionnement As Integer = 0
        Dim QteUnitaireArticleMere As Integer = 0
        Dim StockArticleMere As Integer = 0
        Try
            y = gListeRecherche(e.Row, ("CodeArticle"))

            CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", y)
            If CategorieArticle = 9 Then
                If RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", y) <> "" Then
                    PreparationArticle = RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", y)
                End If
            End If

            If CategorieArticle = 9 And PreparationArticle = 4 Then
                Try
                    CodeArticleMereFractionnement = RecupererValeurExecuteScalaire("CodeArticleMere", "FRACTIONNEMENT", "CodeFractionnement", y)
                    QteUnitaireArticleMere = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticleMereFractionnement)
                    StockArticleMere = CalculeStock(CodeArticleMereFractionnement) * QteUnitaireArticleMere
                Catch ex As Exception
                End Try
            End If

            If CategorieArticle = 9 And PreparationArticle = 4 Then
                e.Value = CalculeStock(y) + StockArticleMere
            Else
                e.Value = CalculeStock(y)
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " gListeRecherche_UnboundColumnFetch", ex.Message, "0000163", "Erreur d'exécution de gListeRecherche_UnboundColumnFetch ", True, True, True)

        End Try
    End Sub

    Private Sub bRecherche_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRecherche.Click
        Try
            'Suivi du scénario 
            fMessageException.Show("Sortie", "fSortie", "bRecherche_Click", "NoException", "NoError", "Clic sur le bouton Recherche", False, True, False)

            mode = "Consultation"
            tRecherche.Visible = True
            tRecherche.Text = System.DateTime.Now.Year.ToString + "/"
            tRecherche.Focus()
            tRecherche.Select(tRecherche.Text.Length, 0)
            bAnnuler.Enabled = True

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " bRecherche_Click", ex.Message, "0000164", "Erreur d'exécution de bRecherche_Click ", True, True, True)

        End Try
    End Sub

    Private Sub bModifier_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifier.Click

        Dim I As Integer = 0

        Try
            'Suivi du scénario 
            fMessageException.Show("Sortie", "fSortie", "bModifier_Click", "NoException", "NoError", "Clic sur le bouton Modifier", False, True, False)

            'Changer le mode en Modif
            mode = "Modif"

            'ajout(d) 'un nouvel enregistrement vide dans les datatables convenables
            NouvelArticle = dsSortie.Tables("SORTIE_DETAILS").NewRow()
            NouvelArticle("Designation") = ""
            NouvelArticle("CodeArticle") = ""
            dsSortie.Tables("SORTIE_DETAILS").Rows.Add(NouvelArticle)

            For I = 0 To dsSortie.Tables("SORTIE_DETAILS").Columns.Count - 1
                Me.gArticles.Splits(0).DisplayColumns(I).AllowFocus = False
            Next

            With gArticles

                .Splits(0).DisplayColumns("CodeABarre").Locked = False
                .Splits(0).DisplayColumns("Designation").Locked = False
                .Splits(0).DisplayColumns("Qte").Locked = False
                '.Splits(0).DisplayColumns("Remise").Locked = False
                .Splits(0).DisplayColumns("NumeroLotArticle").Locked = False
                .Splits(0).DisplayColumns("DatePeremption").Locked = False
                .Splits(0).DisplayColumns("PrixAchatHT").Locked = False

                '.Splits(0).DisplayColumns("CodeArticle").AllowFocus = True
                .Splits(0).DisplayColumns("CodeABarre").AllowFocus = True
                .Splits(0).DisplayColumns("Designation").AllowFocus = True
                .Splits(0).DisplayColumns("Qte").AllowFocus = True
                '.Splits(0).DisplayColumns("Remise").AllowFocus = True
                .Splits(0).DisplayColumns("NumeroLotArticle").AllowFocus = True
                .Splits(0).DisplayColumns("DatePeremption").AllowFocus = True
                .Splits(0).DisplayColumns("PrixAchatHT").AllowFocus = True

                If mode = "Modif" Then
                    .Splits(0).DisplayColumns("DatePeremption").Visible = False
                    .Splits(0).DisplayColumns("NumeroLotArticle").Visible = False
                End If

            End With

            'Verouiller les controles 
            initBoutons()

            cmbNature.Focus()

            '#########################################################################################################
            '------------------------------------------------------------------------------------------------------------------------------------------------
            Dim k As Integer = 0
            Dim j As Integer = 0
            Dim L As Integer = 0
            Dim H As Integer = 0
            Dim M As Integer = 0


            Do While j < gArticles.RowCount

                For k = j To dsSortie.Tables("SORTIE_DETAILS").Rows.Count - 1

                    If gArticles(j, "CodeArticle") = gArticles(k, "CodeArticle") And j <> k Then
                        gArticles(j, "Qte") = (Convert.ToInt32(gArticles(j, "Qte")) + (Convert.ToInt32(gArticles(k, "Qte")))).ToString

                        gArticles(k, "CodeArticle") = ""
                        gArticles(k, "CodeABarre") = ""
                        gArticles(k, "Designation") = ""
                        gArticles(k, "Forme") = ""
                        gArticles(k, "Qte") = ""
                        gArticles(k, "DatePeremption") = ""
                        gArticles(k, "NumeroLotArticle") = ""
                        gArticles(k, "PrixAchatTTC") = ""
                        gArticles(k, "TotalAchatTTC") = ""
                        gArticles(k, "PrixVenteTTC") = ""
                        gArticles(k, "TotalVenteTTC") = ""
                        gArticles(k, "Stock") = ""
                        gArticles(k, "QuantiteUnitaire") = ""

                        gArticles(j, "DatePeremption") = ""
                        gArticles(j, "NumeroLotArticle") = ""


                    End If
                Next
                '#########################################################################################################
                I = 0
                Do While I < dsSortie.Tables("SORTIE_DETAILS").Rows.Count

                    If dsSortie.Tables("SORTIE_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then

                        If dsSortie.Tables("SORTIE_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                            dsSortie.Tables("SORTIE_DETAILS").Rows(I).Delete()

                        End If

                    End If
                    I = I + 1
                Loop




                j = j + 1
            Loop

            Do While H < gArticles.RowCount

                gArticles(H, "DatePeremption") = ""
                gArticles(H, "NumeroLotArticle") = ""

                H = H + 1
            Loop

            CalculerMontants()
            '#########################################################################################################

            '--------------------------------------------------------------------------------------------------------------------------------------------------




        Catch ex As Exception

            'Gérer l'Exception
            'fMessageException.Show("Sortie", "fSortie", " bModifier_Click", ex.Message, "0000165", "Erreur d'exécution de bModifier_Click ", True, True, True)

        End Try
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Try
            'Suivi du scénario 
            fMessageException.Show("Sortie", "fSortie", "bImprimer_Click", "NoException", "NoError", "Clic sur le bouton Imprimer", False, True, False)

            If lNumeroSortie.Text = "" Then
                Exit Sub
            End If

            Dim CondCrystal As String = ""
            'CondCrystal = " AND {SORTIE_DETAILS.NumeroSortie} = '" & lNumeroSortie.Text & "' "
            CondCrystal = "1=1 AND {Vue_EtatSortie.NumeroSortie} = '" & lNumeroSortie.Text & "' "

            Dim I As Integer
            Dim num As Integer = 999
            For I = 0 To fMain.Tab.TabPages.Count - 1
                If fMain.Tab.TabPages(I).Text = "Impression Sortie" Then
                    num = I
                End If
            Next
            'CR.FileName = Application.StartupPath + "\EtatSortie.rpt"
            CR.FileName = Application.StartupPath + "\EtatSortie1.rpt"
            CR.SetParameterValue("pNumeroSortie", lNumeroSortie.Text)

            Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo

            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent

            CR.RecordSelectionFormula = CondCrystal
            Dim MyViewer As New fViewer
            MyViewer.CRViewer.ReportSource = CR
            fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
            fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
            fMain.Tab.SelectedTab.Text = "Impression Sortie"
            If num <> 999 Then
                fMain.Tab.TabPages(num).Dispose()
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " bImprimer_Click", ex.Message, "0000165", "Erreur d'exécution de bImprimer_Click ", True, True, True)

        End Try
    End Sub

    'Paramétre: pNumeroLigneSortie
    '           C'est l'élément actuel, on va faire 
    '           seulement une selection de la ligne en question
    Private Sub ChargerSortie(ByVal pNumeroLigneSortie As String)

        '--------------------------Declaration 
        Dim StrSQL As String
        Dim DataRowAffichageEntete As DataRow

        Try
            '------------------------------ préparation des datatable vides 
            Try
                dsSortie.Tables("SORTIE_DETAILS").Clear()

                dsSortie.Tables("SORTIE").Clear()

            Catch ex As Exception

            End Try

            Try

                'chargement des Entêtes des Sortie

                StrSQL = " SELECT * FROM (  " + _
                " SELECT *, ROW_NUMBER() OVER (ORDER BY NumeroSortie) as row FROM SORTIE " + _
                "              ) a WHERE row > " & pNumeroLigneSortie - 1 & " AND  row <= " & pNumeroLigneSortie + ""

                cmdSortieEntete.Connection = ConnectionServeur
                cmdSortieEntete.CommandText = StrSQL
                daSortieEntete = New SqlDataAdapter(cmdSortieEntete)
                daSortieEntete.Fill(dsSortie, "SORTIE")
                cbSortieEntete = New SqlCommandBuilder(daSortieEntete)

            Catch ex As Exception

                'Gérer l'Exception
                fMessageException.Show("Sortie", "fSortie", "ChargerSortie", ex.Message, "00000--20", "Erreur lors d'executer la requette", True, True, True)


            End Try

            'Lire le numéro Sortie
            If dsSortie.Tables("SORTIE").Rows.Count > 0 Then

                NumeroSortie = dsSortie.Tables("SORTIE").Rows(0).Item("NumeroSortie")

            Else

                NumeroSortie = "0"

            End If

            initBoutons()


            Try
                'chargement des détails des SORTIE_DETAILS 
                StrSQL = "SELECT NumeroSortie," + _
                         "CodeArticle," + _
                         "CodeABarre," + _
                         "Designation," + _
                         "SORTIE_DETAILS.CodeForme," + _
                         "'' AS LibelleForme," + _
                         "Qte," + _
                         "DatePeremption," + _
                         "NumeroLotArticle," + _
                         "PrixAchatHT," + _
                         "PrixAchatTTC," + _
                         "TotalAchatTTC," + _
                         "PrixVenteHT," + _
                         "PrixVenteTTC," + _
                         "TotalVenteTTC," + _
                         "Stock, " + _
                         "(SELECT CASE WHEN QuantiteUnitaire = 0 then 1 else QuantiteUnitaire end From ARTICLE WHERE ARTICLE.CodeArticle = SORTIE_DETAILS.CodeArticle) as QuantiteUnitaire ," + _
                         "'' AS Vide " + _
                         "FROM " + _
                         "SORTIE_DETAILS " + _
                         "WHERE " + _
                         "NumeroSortie =" + Quote(NumeroSortie) + " ORDER BY Id"

                cmdSortieDetails.Connection = ConnectionServeur
                cmdSortieDetails.CommandText = StrSQL
                daSortieDetails = New SqlDataAdapter(cmdSortieDetails)
                daSortieDetails.Fill(dsSortie, "SORTIE_DETAILS")
                cbSortieDetails = New SqlCommandBuilder(daSortieDetails)

            Catch ex As Exception

                'Gérer l'Exception
                fMessageException.Show("Sortie", "fSortie", "ChargerSortie", ex.Message, "00000--19", "Erreur lors d'executer la requette", True, True, True)

            End Try

            'initialisation de gArticle
            initgArticle()

            '-----chargement des Natures de Sortie
            initNatureSortie()

            ' Affichage ds informations de Sortie

            'Si le  mode est modification
            If mode = "Modif" Or mode = "Consultation" Then

                If dsSortie.Tables("SORTIE").Rows.Count > 0 Then

                    DataRowAffichageEntete = dsSortie.Tables("SORTIE").Select("NumeroSortie=" + Quote(NumeroSortie))(0)

                    lNumeroSortie.Text = dsSortie.Tables("SORTIE").Rows(dsSortie.Tables("SORTIE").Rows.Count - 1)("NumeroSortie")
                    lDateSortie.Text = dsSortie.Tables("SORTIE").Rows(dsSortie.Tables("SORTIE").Rows.Count - 1)("Date")
                    cmbNature.SelectedValue = dsSortie.Tables("SORTIE").Rows(dsSortie.Tables("SORTIE").Rows.Count - 1)("CodeNature")

                    lValeurVenteHT.Text = dsSortie.Tables("SORTIE").Rows(dsSortie.Tables("SORTIE").Rows.Count - 1)("ValeurVenteHT")
                    lValeurVenteTTC.Text = dsSortie.Tables("SORTIE").Rows(dsSortie.Tables("SORTIE").Rows.Count - 1)("ValeurVenteTTC")
                    lValeurAchatHT.Text = Math.Round(dsSortie.Tables("SORTIE").Rows(dsSortie.Tables("SORTIE").Rows.Count - 1)("ValeurAchatHT"), 3)
                    lValeurAchatTTC.Text = Math.Round(dsSortie.Tables("SORTIE").Rows(dsSortie.Tables("SORTIE").Rows.Count - 1)("ValeurAchatTTC"), 3)

                    lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsSortie.Tables("SORTIE").Rows(dsSortie.Tables("SORTIE").Rows.Count - 1)("CodePersonnel"))


                End If

            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " ChargerSortie", ex.Message, "0000166", "Erreur d'exécution de ChargerSortie ", True, True, True)

        End Try
    End Sub

    Private Sub gArticles_UnboundColumnFetch(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gArticles.UnboundColumnFetch
        'Récuperer la valeur Désignation FORME ARTICLE 
        Try

            StrSQL = " SELECT LibelleForme FROM FORME_ARTICLE AS F JOIN ARTICLE AS A ON  " + _
                     " F.CodeForme = A.CodeForme" + _
                     " WHERE CodeArticle = " + Quote(gArticles(e.Row, "CodeArticle"))

            cmdSortie.Connection = ConnectionServeur
            cmdSortie.CommandText = StrSQL

            e.Value = cmdSortie.ExecuteScalar()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " gArticles_UnboundColumnFetch", ex.Message, "0000167", "Erreur d'exécution de gArticles_UnboundColumnFetch ", True, True, True)

        End Try
    End Sub

    Private Sub initBoutons()
        Try
            'Tester si on atteint la premiere ligne
            'pour desactiver les BTN Précedent et Premier élément        
            If NumeroligneSortie = 1 Then

                bPrevious.Enabled = False
                bFirst.Enabled = False

            Else

                bPrevious.Enabled = True
                bFirst.Enabled = True

            End If

            'Tester si on atteint la derniere ligne
            'pour desactiver les BTN Siuvant et Dernier élément
            If NumeroligneSortie = selectionDernierLigneSortie() Then

                bNext.Enabled = False
                bLast.Enabled = False

            Else

                bNext.Enabled = True
                bLast.Enabled = True

            End If

            'Le cas ou la table est vide
            If NumeroligneSortie = 0 Then

                bNext.Enabled = False
                bLast.Enabled = False
                bPrevious.Enabled = False
                bFirst.Enabled = False

            End If

            'Tester si la table est vide
            'pour desactiver les BTN Siuvant et Dernier élément
            If selectionDernierLigneSortie() = 0 Then

                'Bloque navigation
                bNext.Enabled = False
                bLast.Enabled = False
                bNext.Enabled = False
                bLast.Enabled = False

                'Bouton de ctrl
                bAnnuler.Enabled = False
                bConfirmer.Enabled = False
                bImprimer.Enabled = False
                bModifier.Enabled = False
                bRecherche.Enabled = False
                bSupprimer.Enabled = False
                bQuitter.Enabled = True

            End If   ' le cas on a ajouté un element

            'le mode en Cosultation et on a des enregistrements
            If selectionDernierLigneSortie() <> 0 And mode = "Consultation" Then

                'Bouton de ctrl
                bAnnuler.Enabled = False
                bConfirmer.Enabled = False
                bImprimer.Enabled = True
                bModifier.Enabled = True
                bRecherche.Enabled = True
                bSupprimer.Enabled = True
                bAjouter.Enabled = True
                bQuitter.Enabled = True

                bFirst.Visible = True
                bPrevious.Visible = True
                bNext.Visible = True
                bLast.Visible = True

                GroupeNature.Enabled = False

                'le mode est modif/Ajout et on a des enregistrements
            ElseIf selectionDernierLigneSortie() <> 0 And mode <> "Consultation" Then

                bAnnuler.Enabled = True
                bConfirmer.Enabled = True
                bImprimer.Enabled = False
                bModifier.Enabled = False
                bAjouter.Enabled = False
                bRecherche.Enabled = False
                bSupprimer.Enabled = True
                bQuitter.Enabled = True

                'Rendre Invisible le menu de navigation
                bNext.Visible = False
                bLast.Visible = False
                bPrevious.Visible = False
                bFirst.Visible = False

                'lPharmacie.Enabled = True
                GroupeNature.Enabled = True

                'le mode en Cosultation et on  a pas des enregistrements
            ElseIf selectionDernierLigneSortie() = 0 And mode = "Consultation" Then

                bAjouter.Enabled = True
                bAnnuler.Enabled = False
                bConfirmer.Enabled = False
                bQuitter.Enabled = True

                bFirst.Visible = True
                bPrevious.Visible = True
                bNext.Visible = True
                bLast.Visible = True

                'En cas d'ajout un element puis annulé sans faire valider
                GroupeNature.Enabled = False
                cmbNature.SelectedValue = -1


            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " initBoutons", ex.Message, "0000168", "Erreur d'exécution de initBoutons ", True, True, True)

        End Try
    End Sub

    Private Sub initControlSortie()
        Try
            '---------------------------------Debloquer le saisie
            Dim I As Integer

            With gArticles

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Locked = True
                Next

                .Splits(0).DisplayColumns("CodeABarre").Locked = False
                .Splits(0).DisplayColumns("Designation").Locked = False
                .Splits(0).DisplayColumns("CodeForme").Locked = False
                .Splits(0).DisplayColumns("Qte").Locked = False
                .Splits(0).DisplayColumns("DatePeremption").Locked = False
                .Splits(0).DisplayColumns("NumeroLotArticle").Locked = False

            End With

            '------------------------------ initialisation des differents zones de textes 
            '------------------------------ initialisation des variables globaux 

            TotalHTAchat = 0.0
            TVA = 0.0
            Timbre = 0.3

            lValeurVenteTTC.Text = "0.000"
            lValeurVenteHT.Text = "0.000"
            lValeurAchatTTC.Text = "0.000"
            lValeurAchatHT.Text = "0.000"

            lOperateur.Text = "-"

            lDateSortie.Text = System.DateTime.Now
            cmbNature.Text = ""
            lNumeroSortie.Text = "-------------"

            bAnnuler.Enabled = True
            bConfirmer.Enabled = True

            bSupprimer.Enabled = True

            bFirst.Visible = False
            bPrevious.Visible = False
            bNext.Visible = False
            bLast.Visible = False
            bAjouter.Enabled = False

            GroupeNature.Enabled = True

            cmbNature.Focus()
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " initControlSortie", ex.Message, "0000169", "Erreur d'exécution de initControlSortie ", True, True, True)

        End Try
    End Sub

    Private Function selectionDernierLigneSortie()


        Dim StrSQL As String

        Try

            'Affécter le nombre de ligne au variable global  NumeroligneSortie
            StrSQL = " SELECT COUNT(*) FROM SORTIE "

            cmdSortie.Connection = ConnectionServeur
            cmdSortie.CommandText = StrSQL

            selectionDernierLigneSortie = cmdSortie.ExecuteScalar()

            Return selectionDernierLigneSortie

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " selectionDernierLigneSortie", ex.Message, "0000170", "Erreur d'exécution de selectionDernierLigneSortie ", True, True, True)
            Return 0
        End Try

    End Function

    Private Sub selectionPrmierLigneSortie()

        'Affécter le numéro 1 au variable global  NumeroligneSortie
        NumeroligneSortie = 1

    End Sub

    Private Sub selectionLigneSortieSuivant()

        'Affécter le numéro 1 au variable global  NumeroligneSortie
        NumeroligneSortie = NumeroligneSortie + 1

    End Sub

    Private Sub selectionLigneSortiePrecedent()

        'Affécter le numéro 1 au variable global  NumeroligneSortie
        NumeroligneSortie = NumeroligneSortie - 1

    End Sub

    Private Sub initLoadControl()
        Try
            mode = "Consultation"

            lValeurVenteTTC.Text = "0.000"
            lValeurVenteHT.Text = "0.000"
            lValeurAchatHT.Text = "0.000"
            lValeurAchatTTC.Text = "0.000"

            bConfirmer.Enabled = False
            bAnnuler.Enabled = False
            bAjouter.Enabled = True
            bFirst.Enabled = True
            bPrevious.Enabled = True
            bSupprimer.Enabled = True

            GroupeNature.Enabled = False

            'Rendre la liste en mode read only
            For I = 0 To gArticles.Columns.Count - 1
                gArticles.Splits(0).DisplayColumns(I).Locked = True
            Next
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " initLoadControl", ex.Message, "0000171", "Erreur d'exécution de initLoadControl ", True, True, True)

        End Try

    End Sub

    Private Sub initNatureSortie()

        Dim StrSQL As String
        Try
            Try
                dsSortie.Tables("NATURE_SORTIE").Clear()
            Catch ex As Exception

            End Try


            'chargement des NATURE_Sortie
            StrSQL = "SELECT CodeNatureSortie,LibelleNatureSortie FROM NATURE_SORTIE WHERE SupprimeNatureSortie = 0 ORDER BY LibelleNatureSortie ASC"
            cmdSortie.Connection = ConnectionServeur
            cmdSortie.CommandText = StrSQL
            daSortie = New SqlDataAdapter(cmdSortie)
            daSortie.Fill(dsSortie, "NATURE_SORTIE")
            cmbNature.DataSource = dsSortie.Tables("NATURE_SORTIE")
            cmbNature.ValueMember = "CodeNatureSortie"
            cmbNature.DisplayMember = "LibelleNatureSortie"
            cmbNature.ColumnHeaders = False

            cmbNature.Splits(0).DisplayColumns("CodeNatureSortie").Visible = False
            cmbNature.Splits(0).DisplayColumns("LibelleNatureSortie").Width = 10
            cmbNature.ExtendRightColumn = True

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " initNatureSortie", ex.Message, "0000172", "Erreur d'exécution de initNatureSortie ", True, True, True)

        End Try

    End Sub

    Private Sub initSortieEntete()
        Try


            Dim StrSQL As String

            'chargement des Entêtes des SORTIE        
            StrSQL = "SELECT Top(0) * FROM SORTIE ORDER BY NumeroSortie ASC"
            cmdSortieEntete.Connection = ConnectionServeur
            cmdSortieEntete.CommandText = StrSQL
            daSortieEntete = New SqlDataAdapter(cmdSortieEntete)
            daSortieEntete.Fill(dsSortie, "SORTIE")
            cbSortieEntete = New SqlCommandBuilder(daSortieEntete)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " initSortieEntete", ex.Message, "0000173", "Erreur d'exécution de initSortieEntete ", True, True, True)

        End Try

    End Sub

    Private Sub initSortieDetails()
        Try
            Dim StrSQL As String

            'chargement des détails des SORTIE_DETAILS 
            StrSQL = "SELECT Top(0) NumeroSortie," + _
                     "CodeArticle," + _
                     "CodeABarre," + _
                     "Designation," + _
                     "SORTIE_DETAILS.CodeForme," + _
                     "'' AS LibelleForme," + _
                     "Qte," + _
                     "DatePeremption," + _
                     "NumeroLotArticle," + _
                     "PrixAchatHT," + _
                     "PrixAchatTTC," + _
                     "TotalAchatTTC," + _
                     "PrixVenteHT," + _
                     "PrixVenteTTC," + _
                     "TotalVenteTTC," + _
                     "Stock, " + _
                     "1 as QuantiteUnitaire, " + _
                     "'' AS Vide " + _
                     "FROM " + _
                     "SORTIE_DETAILS " + _
                     "WHERE  " + _
                     "NumeroSortie =" + Quote(NumeroSortie) + ""

            cmdSortieDetails.Connection = ConnectionServeur
            cmdSortieDetails.CommandText = StrSQL
            daSortieDetails = New SqlDataAdapter(cmdSortieDetails)
            daSortieDetails.Fill(dsSortie, "SORTIE_DETAILS")
            cbSortieDetails = New SqlCommandBuilder(daSortieDetails)
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " initSortieDetails", ex.Message, "0000174", "Erreur d'exécution de initSortieDetails ", True, True, True)

        End Try
    End Sub

    Private Sub initgArticle()
        Try
            With gArticles
                .Columns.Clear()
                Try
                    .DataSource = dsSortie
                Catch ex As Exception
                End Try
                .DataMember = "SORTIE_DETAILS"
                .Rebind(False)
                .Columns("CodeABarre").Caption = "Code article"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Qte").Caption = "Qte"
                .Columns("NumeroLotArticle").Caption = "Numero Lot"
                .Columns("PrixAchatTTC").Caption = "Prix A TTC "
                .Columns("PrixVenteTTC").Caption = "Prix V TTC "
                .Columns("TotalAchatTTC").Caption = "Total A TTC"
                .Columns("TotalVenteTTC").Caption = "Total V TTC"
                .Columns("DatePeremption").Caption = "Date péremption"
                .Columns("Stock").Caption = "Stock"
                'colonne vide
                .Columns("Vide").Caption = ""

                ' Colonne non liée : LibelleForme
                .Columns("LibelleForme").DataField = ""

                ' Centrer toutes les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Locked = True
                Next

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("PrixAchatTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalAchatTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

                .Splits(0).DisplayColumns("NumeroSortie").Width = 0
                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

                .Splits(0).DisplayColumns("CodeABarre").Width = 150 '60
                .Splits(0).DisplayColumns("NumeroLotArticle").Width = 70
                .Splits(0).DisplayColumns("Designation").Width = 440 '260
                '.Splits(0).DisplayColumns("CodeForme").Width = 0
                .Splits(0).DisplayColumns("Forme").Width = 100
                .Splits(0).DisplayColumns("Qte").Width = 50
                .Splits(0).DisplayColumns("PrixAchatTTC").Width = 90
                .Splits(0).DisplayColumns("TotalAchatTTC").Width = 90
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 90
                .Splits(0).DisplayColumns("TotalVenteTTC").Width = 90
                .Splits(0).DisplayColumns("DatePeremption").Width = 130 '150
                .Splits(0).DisplayColumns("Stock").Width = 70

                'colonne vide ajouter
                .Splits(0).DisplayColumns("Vide").Width = 50
                .Splits(0).DisplayColumns("Vide").Visible = False

                .Splits(0).DisplayColumns("NumeroSortie").Visible = False
                .Splits(0).DisplayColumns("CodeForme").Visible = False
                .Splits(0).DisplayColumns("PrixAchatHT").Visible = False
                .Splits(0).DisplayColumns("PrixVenteHT").Visible = False

                .Splits(0).DisplayColumns("QuantiteUnitaire").Visible = False

                .Splits(0).DisplayColumns("NumeroLotArticle").Visible = False
                If mode = "Ajout" Then
                    .Splits(0).DisplayColumns("DatePeremption").Visible = False
                    '.Splits(0).DisplayColumns("NumeroLotArticle").Visible = False
                Else
                    .Splits(0).DisplayColumns("DatePeremption").Visible = True
                    '.Splits(0).DisplayColumns("NumeroLotArticle").Visible = True
                End If

                '' ''If mode = "Consultation" Then
                '' ''    .Splits(0).DisplayColumns("DatePeremption").Visible = True
                '' ''    .Splits(0).DisplayColumns("NumeroLotArticle").Visible = True
                '' ''End If


                'couleur de la grid
                .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(250, 250, 200)
                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(210, 240, 255)
                .Splits(0).DisplayColumns("Forme").Style.BackColor = Color.FromArgb(250, 250, 200)
                .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(210, 240, 255)
                .Splits(0).DisplayColumns("TotalAchatTTC").Style.BackColor = Color.FromArgb(210, 240, 255)
                .Splits(0).DisplayColumns("NumeroLotArticle").Style.BackColor = Color.FromArgb(250, 250, 200)
                .Splits(0).DisplayColumns("PrixAchatHT").Style.BackColor = Color.FromArgb(210, 255, 230)
                .Splits(0).DisplayColumns("PrixAchatTTC").Style.BackColor = Color.FromArgb(210, 255, 230)
                .Splits(0).DisplayColumns("PrixVenteTTC").Style.BackColor = Color.FromArgb(210, 255, 230)
                .Splits(0).DisplayColumns("TotalVenteTTC").Style.BackColor = Color.FromArgb(210, 240, 255)
                .Splits(0).DisplayColumns("PrixVenteHT").Style.BackColor = Color.FromArgb(210, 255, 230)
                .Splits(0).DisplayColumns("DatePeremption").Style.BackColor = Color.FromArgb(250, 250, 200)
                .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(210, 255, 230)
                .Splits(0).DisplayColumns("Vide").Style.BackColor = Color.FromArgb(250, 250, 200)

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                .AllowSort = False
                .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
                'Style du Caractere et du grid
                ParametreGrid(gArticles)
            End With
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " initgArticle", ex.Message, "0000175", "Erreur d'exécution de initgArticle ", True, True, True)

        End Try
    End Sub


    '--------------------initialisation de la datatable article qui est utilisé dans la liste de 
    '--------------------recherche alimenté selon les entrés de l'utilisateur dans la colonne designation
    Private Sub initArticle()


        Dim StrSQL As String
        Try
            StrSQL = "SELECT CodeArticle," + _
                     "Designation," + _
                     "LibelleForme," + _
                     "PrixVenteTTC" + _
                     " FROM ARTICLE,FORME_ARTICLE " + _
                     "WHERE ARTICLE.CodeForme=FORME_ARTICLE.CodeForme and " + _
                     "Designation LIKE " + Quote(gArticles.Columns("Designation").Value) + " ORDER BY Designation"

            cmdSortie.Connection = ConnectionServeur
            cmdSortie.CommandText = StrSQL
            daSortie = New SqlDataAdapter(cmdSortie)
            daSortie.Fill(dsSortie, "ARTICLE")

            With gListeRecherche
                .Columns.Clear()
                .DataSource = dsSortie
                .DataMember = "ARTICLE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code Article"
                .Columns("Designation").Caption = "Designation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("PrixVenteTTC").Caption = "Prix de vente"

                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centre tous les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Visible = False
                Next

                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("Designation").Visible = True
                .Splits(0).DisplayColumns("LibelleForme").Visible = True
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
                .Splits(0).DisplayColumns("Designation").Width = 300
                .Splits(0).DisplayColumns("LibelleForme").Width = 100
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 20
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True

                'Style du Caractere et du grid
                ParametreGrid(gListeRecherche)

            End With

            cbSortie = New SqlCommandBuilder(daSortie)
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " initArticle", ex.Message, "0000176", "Erreur d'exécution de initArticle ", True, True, True)

        End Try
    End Sub

    Private Sub tRecherche_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRecherche.KeyUp
        Try
            If e.KeyCode = Keys.Enter And tRecherche.Text <> "" Then

                'Recuprére le Row de l'element sellectioné

                'Lors du press Enter, on va appler la procedure rechercheSortie

                rechercheSortie(tRecherche.Text)

            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " tRecherche_KeyUp", ex.Message, "0000177", "Erreur d'exécution de tRecherche_KeyUp ", True, True, True)

        End Try
    End Sub

    Private Sub tRecherche_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tRecherche.LostFocus

        tRecherche.Visible = False

    End Sub

    Private Sub rechercheSortie(ByVal pNumeroSortie As String)

        '----------------------------------Traitement
        Try
            If tRecherche.Text.Length < 11 Then
                tRecherche.Text = tRecherche.Text.Substring(0, 5) + tRecherche.Text.Substring(5, tRecherche.Text.Length - 5).PadLeft(6, "0")
            End If

            'Recuperer la valeur de la row
            recupererNumRowRechrche()


            If NumeroligneSortie <> 0 Then
                ChargerSortie(NumeroligneSortie)
            End If

            tRecherche.Value = ""
            tRecherche.Visible = False
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " rechercheSortie", ex.Message, "0000178", "Erreur d'exécution de rechercheSortie ", True, True, True)

        End Try
    End Sub

    Private Sub recupererNumRowRechrche()

        Try
            '------------------------- affichage du nombre de Sortie en instance 
            StrSQL = " SELECT RowNumber " + _
                     " FROM (select ROW_NUMBER() OVER(ORDER BY NumeroSortie) " + _
                     " AS 'RowNumber' , NumeroSortie  from SORTIE) AS SORTIELISTE " + _
                     " where SORTIELISTE.NumeroSortie =  " & Quote(tRecherche.Text)

            cmdSortie.Connection = ConnectionServeur
            cmdSortie.CommandText = StrSQL

            NumeroligneSortie = cmdSortie.ExecuteScalar()

            If NumeroligneSortie = 0 Then
                MsgBox("Sortie inéxistant", MsgBoxStyle.Exclamation, "Recherche")
                NumeroligneSortie = selectionDernierLigneSortie()
            End If

            'Tester si on atteint la premiere ligne
            'pour desactiver les BTN Précedent et Premier élément        
            If NumeroligneSortie = 1 Or NumeroligneSortie = 0 Then

                bPrevious.Enabled = False
                bFirst.Enabled = False

            Else

                bPrevious.Enabled = True
                bFirst.Enabled = True

            End If

            'Tester si on atteint la derniere ligne
            'pour desactiver les BTN Siuvant et Dernier élément
            If NumeroligneSortie = selectionDernierLigneSortie() Then

                bNext.Enabled = False
                bLast.Enabled = False

            Else

                bNext.Enabled = True
                bLast.Enabled = True

            End If


        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " recupererNumRowRechrche", ex.Message, "0000179", "Erreur d'exécution de recupererNumRowRechrche ", True, True, True)

        End Try

    End Sub

    Private Sub gArticles_AfterColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gArticles.AfterColUpdate
        Try
            If e.ColIndex = gArticles.Columns.IndexOf(gArticles.Columns("Qte")) Then

                If gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999 Then

                    MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Columns("Qte").Value = "1"

                End If

            End If

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Sortie", "fSortie", " gArticles_AfterColUpdate", ex.Message, "0000180", "Erreur d'exécution de gArticles_AfterColUpdate ", True, True, True)

        End Try
    End Sub

    Private Sub gArticles_BeforeColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColUpdateEventArgs) Handles gArticles.BeforeColUpdate

        If e.ColIndex = gArticles.Columns.IndexOf(gArticles.Columns("Qte")) Then

            If gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999 Then

                MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")

                gArticles.Columns("Qte").Value = e.OldValue

            End If

        End If

    End Sub

    Private Sub cmbNature_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbNature.TextChanged

    End Sub

    Private Sub gArticles_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyDown
        If e.KeyCode = Keys.F1 And gArticles.Columns("CodeArticle").Value.ToString <> "" Then
            Dim stock As Integer
            stock = CalculeStock(gArticles.Columns("CodeArticle").Value)
            AfficherFicheArticle(gArticles.Columns("CodeArticle").Value, stock, gArticles.Columns("Designation").Value)
            Exit Sub
        End If
    End Sub

    Public Sub AfficherFicheArticle(ByVal CodeArticle, ByVal StockArticle, ByVal Designation)

        Try

            Dim MyFicheArticle As New fFicheArticle
            MyFicheArticle.CodeArticle = CodeArticle
            MyFicheArticle.StockArticle = StockArticle
            MyFicheArticle.DesignationArticle = Designation
            MyFicheArticle.ajoutmodif = "M"

            MyFicheArticle.Init()
            MyFicheArticle.ShowDialog()
            MyFicheArticle.Close()
            MyFicheArticle.Dispose()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Achat", "fAchat", "AfficherFicheArticle", ex.Message, "0000721", "Erreur d'excution de AfficherFicheArticle", True, True, True)
            Return

        End Try

    End Sub

    Private Sub dtpDebut_KeyUp(sender As Object, e As KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            dtpFin.Focus()
        End If

        If e.KeyCode = Keys.Back Then
            dtpDebut.Value = Nothing
        End If
    End Sub

    Private Sub dtpFin_KeyUp(sender As Object, e As KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            dtpDebut.Focus()
            rechercheArticlesPerimes()
        End If

        If e.KeyCode = Keys.Back Then
            dtpFin.Value = Nothing
        End If
    End Sub
End Class