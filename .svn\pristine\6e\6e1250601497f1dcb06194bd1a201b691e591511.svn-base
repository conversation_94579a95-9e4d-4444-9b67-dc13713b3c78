﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports Microsoft.Reporting.WinForms
Imports System.Linq.Dynamic

Public Class fHitParadeArticle
    Dim _SalesReportService As New Bll.Reporting.SalesReport
    Dim _VOrderBy As String = "Designation"
    Dim _VAscDesc As String = "Asc"
    Dim _List As New DataSet

    Dim dsListe As New DataSet
    Dim cmdListe As New SqlCommand
    Dim daListe As New SqlDataAdapter
    Dim cbListe As New SqlCommandBuilder
    Dim Categorie As String
    Dim Rqt As String

    Private Sub AfficherFicheArticle(ByVal CodeArticle, ByVal StockArticle)
        Dim MyFicheArticle As New fFicheArticle
        MyFicheArticle.CodeArticle = CodeArticle
        MyFicheArticle.StockArticle = StockArticle
        MyFicheArticle.ajoutmodif = "M"
        MyFicheArticle.Init()
        MyFicheArticle.ShowDialog()
        MyFicheArticle.Close()
        MyFicheArticle.Dispose()
    End Sub

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitte.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub Init()

        'Vider dataset
        dsListe.Clear()

        'charger les vendeurs
        cmdListe.CommandText = "SELECT CodeUtilisateur,Nom FROM UTILISATEUR ORDER BY Nom ASC"
        cmdListe.Connection = ConnectionServeur
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "OPERATEUR")
        cmbVendeur.DataSource = dsListe.Tables("OPERATEUR")
        cmbVendeur.ValueMember = "CodeUtilisateur"
        cmbVendeur.DisplayMember = "Nom"
        cmbVendeur.ColumnHeaders = False
        cmbVendeur.ExtendRightColumn = True
        cmbVendeur.ColumnHeaders = False
        cmbVendeur.Splits(0).DisplayColumns("CodeUtilisateur").Visible = False
        cmbVendeur.Splits(0).DisplayColumns("Nom").Width = cmbVendeur.Width - 20
        cmbVendeur.ExtendRightColumn = True

        'chargement des Categories
        cmdListe.Connection = ConnectionServeur
        cmdListe.CommandText = "SELECT DISTINCT LibelleCategorie,CodeCategorie FROM CATEGORIE WHERE SupprimeCategorie=0 ORDER BY LibelleCategorie ASC"
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "CATEGORIE")
        cmbCategorie.DataSource = dsListe.Tables("CATEGORIE")
        cmbCategorie.ValueMember = "CodeCategorie"
        cmbCategorie.DisplayMember = "LibelleCategorie"
        cmbCategorie.ColumnHeaders = False
        cmbCategorie.Splits(0).DisplayColumns("CodeCategorie").Visible = False
        cmbCategorie.Splits(0).DisplayColumns("LibelleCategorie").Width = 10
        cmbCategorie.ExtendRightColumn = True

        'chargement des Labo
        cmdListe.Connection = ConnectionServeur
        cmdListe.CommandText = "SELECT DISTINCT NomLabo, CodeLabo FROM LABORATOIRE WHERE SupprimeLabo=0 ORDER BY NomLabo ASC"
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "LABO")
        cmbLabo.DataSource = dsListe.Tables("LABO")
        cmbLabo.ValueMember = "CodeLabo"
        cmbLabo.DisplayMember = "NomLabo"
        cmbLabo.ColumnHeaders = False
        cmbLabo.Splits(0).DisplayColumns("CodeLabo").Width = 0
        cmbLabo.Splits(0).DisplayColumns("CodeLabo").Visible = False
        cmbLabo.Splits(0).DisplayColumns("NomLabo").Width = 10
        cmbLabo.ExtendRightColumn = True

        'chargement des Formes
        cmdListe.Connection = ConnectionServeur
        cmdListe.CommandText = "SELECT DISTINCT LibelleForme,CodeForme FROM FORME_ARTICLE where SupprimeForme = 0 ORDER BY LibelleForme ASC"
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "FORME")
        cmbForme.DataSource = dsListe.Tables("FORME")
        cmbForme.ValueMember = "CodeForme"
        cmbForme.DisplayMember = "LibelleForme"
        cmbForme.ColumnHeaders = False
        cmbForme.Splits(0).DisplayColumns("CodeForme").Width = 0
        cmbForme.Splits(0).DisplayColumns("CodeForme").Visible = False
        cmbForme.Splits(0).DisplayColumns("LibelleForme").Width = 10
        cmbForme.ExtendRightColumn = True

        'chargement des Rayons
        cmdListe.Connection = ConnectionServeur
        cmdListe.CommandText = "SELECT DISTINCT Rayon FROM ARTICLE ORDER BY Rayon ASC"
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "RAYON")
        cmbRayon.DataSource = dsListe.Tables("RAYON")
        cmbRayon.ValueMember = "Rayon"
        cmbRayon.DisplayMember = "Rayon"
        cmbRayon.ColumnHeaders = False
        cmbRayon.Splits(0).DisplayColumns("Rayon").Width = 10
        cmbRayon.ExtendRightColumn = True

        'Initialiser les h
        dtpDebut.Text = Date.Today
        dtpDebut.Text = dtpDebut.Text.Substring(0, 10) + " 00:00:01"
        dtpFin.Text = Date.Today
        dtpFin.Text = dtpFin.Text.Substring(0, 10) + " 23:59:59"

        dtpDebut.Focus()

        If ModeADMIN = "ADMIN" Then
            Label2.Visible = True
            tTotTTC.Visible = True
        Else
            Label2.Visible = False
            tTotTTC.Visible = False
        End If

        AfficherListe()
    End Sub

    Public Sub AfficherListe()
        Dim Total As Decimal = 0
        Dim Cond As String = ""
        Dim OrdreBy As String = ""
        Dim OrdreTrie As String = ""
        Dim I As Integer

        Try

            Dim List As New Library.SortableBindingList(Of Data.Reporting.V_Report_EtatHitParade)(_SalesReportService.GetEtatHitParade(dtpDebut.Value, _
                                                                    dtpFin.Value, _
                                                                    IIf(IsNothing(cmbCategorie.SelectedValue), 0, cmbCategorie.SelectedValue), _
                                                                    IIf(IsNothing(cmbForme.SelectedValue), 0, cmbForme.SelectedValue), _
                                                                    IIf(IsNothing(cmbLabo.SelectedValue), 0, cmbLabo.SelectedValue), _
                                                                    IIf(IsNothing(cmbRayon.SelectedValue), "", cmbRayon.SelectedValue), _
                                                                    IIf(IsNothing(cmbVendeur.SelectedValue), -1, Convert.ToInt32(cmbVendeur.SelectedValue))).OrderBy(_VOrderBy + " " + _VAscDesc + ",Designation"))

            With gListe
                .Columns.Clear()
                .DataSource = List
                '.DataMember = "Resultat"
                .Rebind(False)
                .Columns("CodeABarre").Caption = "Code Article"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("LibelleCategorie").Caption = "Catégorie"
                .Columns("Quantite").Caption = "Qté"
                .Columns("Stock").Caption = "Stock"
                .Columns("TotalVenteTTC").Caption = "Montant"
                .Columns("Rayon").Caption = "Rayon"

                '.Columns("DatePeremptionArticle").Caption = "Derniere Date Peremption"

                'Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                'Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
                Next

                .Splits(0).DisplayColumns("CodeABarre").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("TotalVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

                .Splits(0).DisplayColumns("CodePersonnel").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeLabo").Visible = False
                .Splits(0).DisplayColumns("CodeForme").Visible = False
                .Splits(0).DisplayColumns("CodeCategorie").Visible = False
                .Splits(0).DisplayColumns("Date").Visible = False

                .Splits(0).DisplayColumns("CodeABarre").Width = 90


                .Splits(0).DisplayColumns("Designation").Width = 340 '400
                'Else

                '    .Splits(0).DisplayColumns("Designation").Width = Screen.PrimaryScreen.Bounds.Width - 870
                'End If


                .Splits(0).DisplayColumns("LibelleForme").Width = 100
                .Splits(0).DisplayColumns("LibelleCategorie").Width = 100
                .Splits(0).DisplayColumns("Quantite").Width = 90
                .Splits(0).DisplayColumns("Stock").Width = 90
                .Splits(0).DisplayColumns("TotalVenteTTC").Width = 140
                .Splits(0).DisplayColumns("Rayon").Width = 100
                .Columns("DatePeremption").NumberFormat = "dd/MM/yyyy"
                .Splits(0).DisplayColumns("Id").Visible = False
                ''.Splits(0).DisplayColumns("TotalVenteTTC").
                .Columns("TotalVenteTTC").NumberFormat = "#,###0.000"

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                '.Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                'Style du Caractere et du grid
                .AllowSort = True
                ParametreGrid(gListe)
            End With

            ''''''''Calcule du total
            For I = 0 To gListe.RowCount - 1
                Total += gListe(I, "TotalVenteTTC")
            Next

            tTotTTC.Text = Math.Round(Total, 3).ToString("### ### ##0.000")

        Catch
        End Try

        Try
            lNbrArticle.Text = gListe.RowCount.ToString()

        Catch
            lNbrArticle.Text = "0"
        End Try

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub dtpDateDebut_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyDown
        If e.KeyCode = Keys.Enter Then
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpFin_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyDown
        If e.KeyCode = Keys.Enter Then
            cmbCategorie.Focus()
        End If
    End Sub

    Private Sub cmbCategorie_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCategorie.KeyDown
        cmbCategorie.OpenCombo()
        If e.KeyCode = Keys.Enter Then
            cmbCategorie.CloseCombo()
            If cmbCategorie.Text <> "" Then
                cmbCategorie.Text = cmbCategorie.Columns("LibelleCategorie").Value
            End If
            cmbLabo.Focus()
            'AfficherListe()
        End If
    End Sub

    Private Sub gListe_AfterSort(sender As Object, e As FilterEventArgs) Handles gListe.AfterSort
        Try
            _VOrderBy = e.Condition.Substring(0, e.Condition.IndexOf(" "))
            _VAscDesc = e.Condition.Substring(e.Condition.IndexOf(" ") + 1, e.Condition.Length - e.Condition.IndexOf(" ") - 1)
        Catch ex As Exception
            _VOrderBy = e.Condition
            _VAscDesc = IIf(_VAscDesc = "Asc", "Desc", "Asc")
        End Try
        AfficherListe()
    End Sub

    Private Sub gListe_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListe.KeyDown
        If e.KeyCode = Keys.F1 Then
            AfficherFicheArticle(gListe(gListe.Row, "Codearticle"), gListe(gListe.Row, "Stock"))
        End If
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Reporting Ventes" Then
                num = I
            End If
        Next
        Dim dt

        Dim _Parameters As New List(Of ReportParameter)()

        Dim _DateDebut As New ReportParameter()
        _DateDebut.Name = "DateDebut"
        _DateDebut.Values.Add(dtpDebut.Value)
        _Parameters.Add(_DateDebut)

        Dim _DateFin As New ReportParameter()
        _DateFin.Name = "DateFin"
        _DateFin.Values.Add(dtpFin.Value)
        _Parameters.Add(_DateFin)

        Dim _CodeLabo As New ReportParameter()
        _CodeLabo.Name = "CodeLabo"
        _CodeLabo.Values.Add(cmbLabo.SelectedValue)
        _Parameters.Add(_CodeLabo)

        Dim _CodeCategorie As New ReportParameter()
        _CodeCategorie.Name = "CodeCategorie"
        _CodeCategorie.Values.Add(cmbCategorie.SelectedValue)
        _Parameters.Add(_CodeCategorie)

        Dim _CodeForme As New ReportParameter()
        _CodeForme.Name = "CodeForme"
        _CodeForme.Values.Add(cmbForme.SelectedValue)
        _Parameters.Add(_CodeForme)

        Dim _Rayon As New ReportParameter()
        _Rayon.Name = "Rayon"
        _Rayon.Values.Add(cmbRayon.SelectedValue)
        _Parameters.Add(_Rayon)

        Dim MyViewer As New fImpressionReportingVente


        dt = _SalesReportService.GetEtatHitParade(dtpDebut.Value, _
                                                    dtpFin.Value, _
                                                    IIf(IsNothing(cmbCategorie.SelectedValue), 0, cmbCategorie.SelectedValue), _
                                                    IIf(IsNothing(cmbForme.SelectedValue), 0, cmbForme.SelectedValue), _
                                                    IIf(IsNothing(cmbLabo.SelectedValue), 0, cmbLabo.SelectedValue), _
                                                    IIf(IsNothing(cmbRayon.SelectedValue), "", cmbRayon.SelectedValue), _
                                                    IIf(IsNothing(cmbVendeur.SelectedValue), -1, Convert.ToInt32(cmbVendeur.SelectedValue))).OrderBy(_VOrderBy + " " + _VAscDesc + ",Designation")




        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_EtatHitParade", dt))
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Pharmacie", _SalesReportService.GetPharmacie()))
        MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatHitParade.rdl"


        MyViewer.ReportViewer1.LocalReport.SetParameters(_Parameters)

        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel1)

        MyViewer.ReportViewer1.RefreshReport()

        fMain.Tab.SelectedTab.Text = "Impression Reporting Ventes"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If

    End Sub

    Private Sub bQuitter_Click_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitte.Click

        fMain.Tab.SelectedTab.Dispose()

    End Sub

    Private Sub cmbCategorie_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCategorie.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbCategorie.Text = cmbCategorie.WillChangeToText
            cmbCategorie.OpenCombo()
        End If
    End Sub

    Private Sub cmbLabo_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbLabo.KeyDown
        cmbLabo.OpenCombo()
        If e.KeyCode = Keys.Enter Then
            cmbLabo.CloseCombo()
            If cmbLabo.Text <> "" Then
                cmbLabo.Text = cmbLabo.WillChangeToText
            End If
            cmbForme.Focus()
            AfficherListe()
        End If
    End Sub

    Private Sub cmbLabo_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbLabo.KeyUp

    End Sub

    Private Sub cmbForme_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbForme.KeyDown
        cmbForme.OpenCombo()
        If e.KeyCode = Keys.Enter Then
            cmbForme.CloseCombo()
            If cmbForme.Text <> "" Then
                cmbForme.Text = cmbForme.WillChangeToText
            End If
            cmbVendeur.Focus()
            AfficherListe()
        End If
    End Sub

    Private Sub cmbRayon_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbRayon.KeyDown
        cmbRayon.OpenCombo()
        If e.KeyCode = Keys.Enter Then
            cmbRayon.CloseCombo()
            If cmbForme.Text <> "" Then
                cmbRayon.Text = cmbRayon.WillChangeToText
            End If
            dtpDebut.Focus()
            AfficherListe()
        End If
    End Sub

    Private Sub bConvert_Click(sender As System.Object, e As System.EventArgs) Handles bConvert.Click
        
        If ControleDAcces(8, "SAISIE_DINVENTAIRE") = "False" Then
            Exit Sub
        End If


        Dim I As Integer
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Inventaire" Then
                fMain.Tab.TabPages(I).Show()
                Exit Sub
            End If
        Next
        fMain.MyInventaire = New fInventaire
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(fMain.MyInventaire.Panel)
        fMain.Tab.SelectedTab.Text = "Inventaire"
        fMain.MyInventaire.Init()

        fMain.MyInventaire.HitParade(_SalesReportService.GetEtatHitParade(dtpDebut.Value, _
                                                    dtpFin.Value, _
                                                    IIf(IsNothing(cmbCategorie.SelectedValue), 0, cmbCategorie.SelectedValue), _
                                                    IIf(IsNothing(cmbForme.SelectedValue), 0, cmbForme.SelectedValue), _
                                                    IIf(IsNothing(cmbLabo.SelectedValue), 0, cmbLabo.SelectedValue), _
                                                    IIf(IsNothing(cmbRayon.SelectedValue), "", cmbRayon.SelectedValue), _
                                                    IIf(IsNothing(cmbVendeur.SelectedValue), -1, Convert.ToInt32(cmbVendeur.SelectedValue))).OrderBy(_VOrderBy + " " + _VAscDesc + ",Designation").ToList())

        
        

    End Sub

    Private Sub cmbVendeur_KeyDown(sender As Object, e As KeyEventArgs) Handles cmbVendeur.KeyDown
        cmbVendeur.OpenCombo()
        If e.KeyCode = Keys.Enter Then
            cmbVendeur.CloseCombo()
            If cmbVendeur.Text <> "" Then
                cmbVendeur.Text = cmbVendeur.WillChangeToText
            End If
            cmbRayon.Focus()
            AfficherListe()
        End If
    End Sub
End Class