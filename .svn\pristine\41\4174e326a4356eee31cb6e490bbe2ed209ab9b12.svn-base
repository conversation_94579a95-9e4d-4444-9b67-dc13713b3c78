//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class CATEGORIE
    {
        public CATEGORIE()
        {
            this.ARTICLE = new HashSet<ARTICLE>();
            this.MUTUELLE = new HashSet<MUTUELLE>();
            this.MUTUELLE1 = new HashSet<MUTUELLE>();
            this.MUTUELLE2 = new HashSet<MUTUELLE>();
        }
    
        public int CodeCategorie { get; set; }
        public string LibelleCategorie { get; set; }
        public bool SupprimeCategorie { get; set; }
    
        public virtual ICollection<ARTICLE> ARTICLE { get; set; }
        public virtual ICollection<MUTUELLE> MUTUELLE { get; set; }
        public virtual ICollection<MUTUELLE> MUTUELLE1 { get; set; }
        public virtual ICollection<MUTUELLE> MUTUELLE2 { get; set; }
    }
}
