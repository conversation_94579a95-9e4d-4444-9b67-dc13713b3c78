﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fCritereDuReleveCNAM

    Dim cmdReleveSearch As New SqlCommand
    Dim daReleveSearch As New SqlDataAdapter
    Dim dsReleveSearch As New DataSet
    Dim cbReleveSearch As New SqlCommandBuilder

    Dim NbrVente As Integer = 0
    Dim TotalDesVentes As Double = 0.0
    Dim TotalARembourser As Double = 0.0

    Public dr As DataRow = Nothing
    Public NouvelLigne As DataRow = Nothing

    Public Sub AfficherReleve()
        Dim I As Integer
        Dim Cond As String = "1=1"
        Dim Trie As String = ""

        If (dsReleveSearch.Tables.IndexOf("RELEVE_CNAM_DETAILS_AFFICHE") > -1) Then
            dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Clear()
        End If

        'Composer la condition de la requête        

        If dtDebut.Text <> "" Then
            Cond += " AND (Date >= '" + dtDebut.Text + " 00:00:00' OR Date='" + dtDebut.Text + "')"
        End If

        If dtFin.Text <> "" Then
            Cond += " AND (Date <= '" + dtFin.Text + " 23:59:59' OR Date='" + dtFin.Text + "')"
        End If

        If rdbTiersPayant.Checked Then
            Cond += " AND (PriseEnCharge <> 1 OR PriseEnCharge IS NULL) AND " + _
            "(Appareillage <> 1 OR Appareillage IS NULL) AND (APCI.NomAPCI <> 'MO' OR APCI.NomAPCI IS NULL) "
        End If
        If rdbPriseEnCharge.Checked Then
            Cond += " AND PriseEnCharge=1 AND (APCI.NomAPCI <> 'MO' OR APCI.NomAPCI IS NULL) "
        End If
        If rdbMaladiesOrdinaires.Checked Then
            Cond += " AND APCI.NomAPCI = 'MO' AND (PriseEnCharge <> 1 OR PriseEnCharge IS NULL)"
        End If
        If rdbTiersPayantMaladieOrdinaire.Checked Then
            Cond += " AND (PriseEnCharge <> 1 OR PriseEnCharge IS NULL) AND (Appareillage <> 1 OR Appareillage IS NULL ) "
        End If
        If rdbAppareillage.Checked And chbTous.Checked = False Then
            Cond += " AND Appareillage=1 AND VENTE.CodeAppareillage = '" + cmbCodeApp.Text + "' "
        End If
        If rdbAppareillage.Checked And chbTous.Checked = True Then
            Cond += " AND Appareillage=1 "
        End If
        If rdbDate.Checked = True Then
            Trie = " ORDER BY Date "
        Else
            Trie = " ORDER BY IdentifiantCnam "
        End If

        cmdReleveSearch.CommandText = "SELECT " + _
                                        " VENTE.NumeroVente, " + _
                                        " VENTE.Date, " + _
                                        " CLIENT.IdentifiantCnam, " + _
                                        " CLIENT.Nom, " + _
                                        " VENTE.NomMalade," + _
                                        " VENTE.DateNaissance," + _
                                        " LIEN_PARENTE.LibelleLienDeParente," + _
                                        " VENTE.Rang," + _
                                        " VENTE.TotalTTC, " + _
                                        " VENTE.MontantCnam, " + _
                                        " CASE WHEN VENTE.CodeMedecinPrescripteur IN ('PRISE EN CHARGE', 'APPREILLAGE') THEN IdentifiantCNAMMedecin ELSE VENTE.CodeMedecinPrescripteur END AS CodeMedecin, " + _
                                        " CASE WHEN (PriseEnCharge=1) THEN 'PRISE EN CHARGE' ELSE CASE WHEN Appareillage=1 THEN 'APPREILLAGE' ELSE MEDECIN.NomMedecin END END AS NomMedecin, " + _
                                        " VENTE.DateOrdonnance, " + _
                                        " APCI.NomAPCI, " + _
                                        " VENTE.CodeAPCI, " + _
                                        " VENTE.DureeTraitement," + _
                                        " VENTE.CodeClient," + _
                                        " VENTE.CodeDeFamille " + _
                                        " FROM VENTE" + _
                                        " LEFT OUTER JOIN CLIENT ON VENTE.CodeClient =CLIENT.CodeClient  " + _
                                        " LEFT OUTER JOIN MEDECIN ON VENTE.CodeMedecinPrescripteur =MEDECIN.CodeMedecin " + _
                                        " LEFT OUTER JOIN APCI ON VENTE.CodeAPCI =APCI.CodeAPCI " + _
                                        " LEFT OUTER JOIN LIEN_PARENTE ON VENTE.CodeLienDeParente=LIEN_PARENTE.CodeLienDeParente" + _
                                        " WHERE " + Cond + " AND MontantCnam <> '0'" + _
                                        " AND VENTE.NumeroVente NOT IN (SELECT NumeroVente FROM RELEVE_CNAM_DETAILS) " + _
                                        Trie

        cmdReleveSearch.Connection = ConnectionServeur
        daReleveSearch = New SqlDataAdapter(cmdReleveSearch)
        daReleveSearch.Fill(dsReleveSearch, "RELEVE_CNAM_DETAILS_AFFICHE")

        With gReleves
            .Columns.Clear()
            .DataSource = dsReleveSearch
            .DataMember = "RELEVE_CNAM_DETAILS_AFFICHE"
            .Rebind(False)
            .Columns("NumeroVente").Caption = "Num vente"
            .Columns("Date").Caption = "Date"
            .Columns("Nom").Caption = "Nom Client"
            .Columns("IdentifiantCnam").Caption = "Id cnam"
            .Columns("TotalTTC").Caption = "Total"
            .Columns("MontantCnam").Caption = "Montant à rembourser"
            .Columns("NomMedecin").Caption = "Nom Médecin"
            .Columns("CodeMedecin").Caption = "Code Med"
            .Columns("DateOrdonnance").Caption = "Date ordonnance"
            .Columns("NomMalade").Caption = "Nom Malade"
            .Columns("Rang").Caption = "Rang"
            .Columns("DateNaissance").Caption = "Date Nais"
            .Columns("LibelleLienDeParente").Caption = "Lien Parenté"
            .Columns("NomAPCI").Caption = "APCI"
            .Columns("DureeTraitement").Caption = "Durée"

            'Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Nom").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("MontantCnam").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("NomMalade").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(0).DisplayColumns("NumeroVente").Width = 180 '80
            .Splits(0).DisplayColumns("Date").Width = 100
            .Splits(0).DisplayColumns("Nom").Width = 200
            .Splits(0).DisplayColumns("Nom").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("IdentifiantCnam").Width = 100
            .Splits(0).DisplayColumns("IdentifiantCnam").Visible = False
            .Splits(0).DisplayColumns("TotalTTC").Width = 60
            .Splits(0).DisplayColumns("TotalTTC").Visible = False
            .Splits(0).DisplayColumns("MontantCnam").Width = 65
            .Splits(0).DisplayColumns("MontantCnam").Visible = False
            .Splits(0).DisplayColumns("NomMedecin").Width = 80
            .Splits(0).DisplayColumns("NomMedecin").Visible = False
            .Splits(0).DisplayColumns("CodeMedecin").Width = 80
            .Splits(0).DisplayColumns("CodeMedecin").Visible = False
            .Splits(0).DisplayColumns("DateOrdonnance").Width = 80
            .Splits(0).DisplayColumns("DateOrdonnance").Visible = False
            .Splits(0).DisplayColumns("NomMalade").Width = 60
            .Splits(0).DisplayColumns("NomMalade").Visible = False
            .Splits(0).DisplayColumns("Rang").Width = 50
            .Splits(0).DisplayColumns("Rang").Visible = False
            .Splits(0).DisplayColumns("DateNaissance").Width = 50
            .Splits(0).DisplayColumns("DateNaissance").Visible = False
            .Splits(0).DisplayColumns("LibelleLienDeParente").Width = 50
            .Splits(0).DisplayColumns("LibelleLienDeParente").Visible = False
            .Splits(0).DisplayColumns("NomAPCI").Width = 50
            .Splits(0).DisplayColumns("NomAPCI").Visible = False
            .Splits(0).DisplayColumns("DureeTraitement").Width = 50
            .Splits(0).DisplayColumns("DureeTraitement").Visible = False
            .Splits(0).DisplayColumns("CodeClient").Width = 0
            .Splits(0).DisplayColumns("CodeClient").Visible = False
            .Splits(0).DisplayColumns("CodeDeFamille").Width = 0
            .Splits(0).DisplayColumns("CodeDeFamille").Visible = False
            .Splits(0).DisplayColumns("CodeAPCI").Width = 0
            .Splits(0).DisplayColumns("CodeAPCI").Visible = False
            '.Splits(0).DisplayColumns("LibelleSituationArticle").Style.HorizontalAlignment = AlignHorzEnum.Center


            .Splits(0).SplitSize = 360
            .Splits(0).SplitSizeMode = SizeModeEnum.NumberOfColumns
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True



            'deuxieme splits

            'Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(1).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(1).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            .Splits(1).DisplayColumns("Nom").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("MontantCnam").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("NomMalade").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(1).DisplayColumns("NumeroVente").Width = 110
            .Splits(1).DisplayColumns("NumeroVente").Visible = False
            .Splits(1).DisplayColumns("Date").Width = 100
            .Splits(1).DisplayColumns("Date").Visible = False
            .Splits(1).DisplayColumns("Nom").Width = 200
            .Splits(1).DisplayColumns("Nom").Visible = False
            .Splits(1).DisplayColumns("Nom").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("IdentifiantCnam").Width = 140 '100
            .Splits(1).DisplayColumns("TotalTTC").Width = 90
            .Splits(1).DisplayColumns("MontantCnam").Width = 90
            .Splits(1).DisplayColumns("NomMedecin").Width = 80
            .Splits(1).DisplayColumns("CodeMedecin").Width = 100 '80
            .Splits(1).DisplayColumns("DateOrdonnance").Width = 100
            .Splits(1).DisplayColumns("NomMalade").Width = 60
            .Splits(1).DisplayColumns("Rang").Width = 50
            .Splits(1).DisplayColumns("DateNaissance").Width = 100
            .Splits(1).DisplayColumns("LibelleLienDeParente").Width = 80 '50
            .Splits(1).DisplayColumns("NomAPCI").Width = 50
            .Splits(1).DisplayColumns("DureeTraitement").Width = 50
            .Splits(1).DisplayColumns("CodeClient").Width = 0
            .Splits(1).DisplayColumns("CodeClient").Visible = False
            .Splits(1).DisplayColumns("CodeDeFamille").Width = 0
            .Splits(1).DisplayColumns("CodeDeFamille").Visible = False
            .Splits(1).DisplayColumns("CodeAPCI").Width = 0
            .Splits(1).DisplayColumns("CodeAPCI").Visible = False

            .Splits(1).DisplayColumns("IdentifiantCnam").Style.BackColor = Color.FromArgb(192, 255, 255)
            .Splits(1).DisplayColumns("NomMalade").Style.BackColor = Color.FromArgb(192, 255, 255)
            .Splits(1).DisplayColumns("DateNaissance").Style.BackColor = Color.FromArgb(192, 255, 255)
            .Splits(1).DisplayColumns("LibelleLienDeParente").Style.BackColor = Color.FromArgb(192, 255, 255)
            .Splits(1).DisplayColumns("Rang").Style.BackColor = Color.FromArgb(192, 255, 255)

            ' 
            .Splits(1).DisplayColumns("CodeMedecin").Style.BackColor = Color.FromArgb(255, 192, 255)
            .Splits(1).DisplayColumns("NomMedecin").Style.BackColor = Color.FromArgb(255, 192, 255)

            ' 
            .Splits(1).DisplayColumns("MontantCnam").Style.BackColor = Color.FromArgb(192, 255, 192)
            .Splits(1).DisplayColumns("TotalTTC").Style.BackColor = Color.FromArgb(192, 255, 192)

            ' 
            .Splits(1).DisplayColumns("DateOrdonnance").Style.BackColor = Color.FromArgb(255, 224, 192)
            .Splits(1).DisplayColumns("NomAPCI").Style.BackColor = Color.FromArgb(255, 224, 192)
            .Splits(1).DisplayColumns("DureeTraitement").Style.BackColor = Color.FromArgb(255, 224, 192)

            '.Splits(1).SplitSizeMode = SizeModeEnum.NumberOfColumns
            .Splits(1).ColumnCaptionHeight = 40
            .Splits(1).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gReleves)
        End With

        'gReleves.MoveRelative(x)
        CalculerMontants()
    End Sub
    Public Sub CalculerMontants()
        NbrVente = 0
        TotalDesVentes = 0.0
        TotalARembourser = 0.0
        Dim i As Integer = 0

        Do While i < gReleves.RowCount
            If gReleves(i, "NumeroVente") <> "" Then
                TotalDesVentes = TotalDesVentes + gReleves(i, "TotalTTC")
                TotalARembourser = TotalARembourser + gReleves(i, "MontantCnam")
                NbrVente += 1
            End If
            i = i + 1
        Loop

        lNbrVente.Text = NbrVente
        lTotalMontant.Text = Math.Round(TotalDesVentes, 3)

        Dim x As String = ""
        Try
            x = lTotalMontant.Text.Substring(lTotalMontant.Text.LastIndexOf("."))
        Catch ex As Exception
            Try
                x = lTotalMontant.Text.Substring(lTotalMontant.Text.LastIndexOf(","))
            Catch ex1 As Exception
                x = 0
            End Try
        End Try

        If x <> "" Then
            x = Replace(x, ".", "")
            x = Replace(x, ",", "")
            If x.Length = 2 Then lTotalMontant.Text = lTotalMontant.Text + "0"
            If x.Length = 1 Then lTotalMontant.Text = lTotalMontant.Text + "00"
        End If

        'String.Format("# ###.###", lTotalMontant.Text)
        lTotalRembourser.Text = Math.Round(TotalARembourser, 3)
        Try
            x = lTotalRembourser.Text.Substring(lTotalRembourser.Text.LastIndexOf("."))
        Catch ex As Exception
            Try
                x = lTotalRembourser.Text.Substring(lTotalRembourser.Text.LastIndexOf(","))
            Catch ex1 As Exception
                x = 0
            End Try
        End Try

        If x <> "" Then
            x = Replace(x, ".", "")
            x = Replace(x, ",", "")
            If x.Length = 2 Then lTotalRembourser.Text = lTotalRembourser.Text + "0"
            If x.Length = 1 Then lTotalRembourser.Text = lTotalRembourser.Text + "00"
        End If
        'String.Format("# ###.###", lTotalRembourser.Text)

    End Sub

    Private Sub tAu_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs)
        AfficherReleve()
    End Sub

    Private Sub tAu_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tAu_Validated(ByVal sender As Object, ByVal e As System.EventArgs)
        AfficherReleve()
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        Me.Dispose()
    End Sub

    Private Sub bValider_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bValider.Click

        Dim StrSQL As String = ""
        Dim NumeroReleve As String = ""
        Dim I As Integer = 0

        Dim StrSQLAppareillage As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""

        If dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows.Count = 0 Then
            MsgBox("Liste vide !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        If (dsReleveSearch.Tables.IndexOf("RELEVE_CNAM") > -1) Then
            dsReleveSearch.Tables("RELEVE_CNAM").Clear()
        End If

        '---------------------- chargement des données entête

        NumeroReleve = RecupereNumero()

        StrSQL = "SELECT * FROM RELEVE_CNAM ORDER BY NumeroReleve ASC"
        cmdReleveSearch.Connection = ConnectionServeur
        cmdReleveSearch.CommandText = StrSQL
        daReleveSearch = New SqlDataAdapter(cmdReleveSearch)
        daReleveSearch.Fill(dsReleveSearch, "RELEVE_CNAM")
        cbReleveSearch = New SqlCommandBuilder(daReleveSearch)
        dr = dsReleveSearch.Tables("RELEVE_CNAM").NewRow()

        With dsReleveSearch
            dr.Item("NumeroReleve") = NumeroReleve
            dr.Item("Date") = System.DateTime.Now
            dr.Item("DateDebut") = dtDebut.Text
            dr.Item("DateFin") = dtFin.Text
            dr.Item("Total") = TotalDesVentes
            dr.Item("Montant") = TotalARembourser
            dr.Item("Reste") = TotalARembourser
            If rdbTous.Checked Then
                dr.Item("TiersPayant") = True
                dr.Item("PriseEnCharge") = True
                dr.Item("Appareillage") = True
                dr.Item("MaladieOrdinaire") = True
            End If
            If rdbTiersPayant.Checked Then
                dr.Item("TiersPayant") = True
                dr.Item("PriseEnCharge") = False
                dr.Item("Appareillage") = False
                dr.Item("MaladieOrdinaire") = False
            End If
            If rdbPriseEnCharge.Checked Then
                dr.Item("TiersPayant") = False
                dr.Item("PriseEnCharge") = True
                dr.Item("Appareillage") = False
                dr.Item("MaladieOrdinaire") = False
            End If
            If rdbMaladiesOrdinaires.Checked Then
                dr.Item("TiersPayant") = False
                dr.Item("PriseEnCharge") = False
                dr.Item("Appareillage") = False
                dr.Item("MaladieOrdinaire") = True
            End If
            If rdbTiersPayantMaladieOrdinaire.Checked Then
                dr.Item("TiersPayant") = True
                dr.Item("PriseEnCharge") = False
                dr.Item("Appareillage") = False
                dr.Item("MaladieOrdinaire") = True
            End If
            If rdbAppareillage.Checked Then
                dr.Item("TiersPayant") = False
                dr.Item("PriseEnCharge") = False
                dr.Item("Appareillage") = True
                dr.Item("MaladieOrdinaire") = False


                StrSQLAppareillage = " SELECT max([NumeroAppareillage]) FROM [RELEVE_CNAM]"
                cmdRecupereNum.Connection = ConnectionServeur
                cmdRecupereNum.CommandText = StrSQLAppareillage
                Try
                    ValeurActuel = cmdRecupereNum.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If ValeurActuel = "" Then
                    dr.Item("NumeroAppareillage") = 1
                Else
                    dr.Item("NumeroAppareillage") = (ValeurActuel + 1).ToString
                End If


            End If


            dsReleveSearch.Tables("RELEVE_CNAM").Rows.Add(dr)
        End With

        '***************************************** enregistrement de l'entête du relevé *************

        Try
            daReleveSearch.Update(dsReleveSearch, "RELEVE_CNAM")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsReleveSearch.Reset()
        End Try

        '********************************************************************************************
        Try
            Dim StrMajLOT As String
            For I = 0 To dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows.Count - 1
                ' mise a jour du fichier vente 
                If dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("CodeAPCI").ToString = "" Then
                    StrMajLOT = "UPDATE VENTE SET DureeTraitement = " + _
                            Quote(dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("DureeTraitement").ToString) + _
                            " WHERE NumeroVente = " + _
                            Quote(dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("NumeroVente"))
                Else
                    StrMajLOT = "UPDATE VENTE SET DureeTraitement=" + _
                            Quote(dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("DureeTraitement").ToString) + _
                            " WHERE NumeroVente = " + _
                            Quote(dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("NumeroVente").ToString)

                End If
                ' RecupererValeurExecuteScalaire("CodeMedecin", "MEDECIN", "IdentifiantCNAM", dsReleve.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("IdentifiantMedecin"))

                cmdReleveSearch.Connection = ConnectionServeur
                cmdReleveSearch.CommandText = StrMajLOT
                Try
                    cmdReleveSearch.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
            Next
        Catch ex As Exception

        End Try

        '***************************************** enregistrement des détails du relevé *************

        cmdReleveSearch.Connection = ConnectionServeur
        cmdReleveSearch.CommandText = "Select top(0) * FROM RELEVE_CNAM_DETAILS"
        daReleveSearch = New SqlDataAdapter(cmdReleveSearch)
        daReleveSearch.Fill(dsReleveSearch, "RELEVE_CNAM_DETAILS")
        cbReleveSearch = New SqlCommandBuilder(daReleveSearch)

        I = 0
        '---------------------- Chargement des détails  
        Do While I < dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows.Count

            If dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("NumeroVente") <> "" Then

                dr = dsReleveSearch.Tables("RELEVE_CNAM_DETAILS").NewRow()
                dsReleveSearch.Tables("RELEVE_CNAM_DETAILS").Rows.Add(dr)

                dr.Item("NumeroReleve") = NumeroReleve
                dr.Item("NumeroVente") = dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("NumeroVente")
                dr.Item("Date") = dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("Date")
                dr.Item("CodeClient") = dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("CodeClient")
                dr.Item("CodeMedecin") = dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("CodeMedecin")
                dr.Item("DateOrdonnance") = dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("DateOrdonnance")
                'dr.Item("CodeDeFamille") = dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("CodeDeFamille")
                dr.Item("CodeAPCI") = dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("CodeAPCI")
                dr.Item("TotalTTC") = dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("TotalTTC")
                dr.Item("MontantCnam") = dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("MontantCnam")
                'dr.Item("DureeTraitement") = dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("DureeTraitement")
                dr.Item("NumeroLigne") = I
            End If
            I = I + 1
        Loop

        Try

            daReleveSearch.Update(dsReleveSearch, "RELEVE_CNAM_DETAILS")

            If MsgBox("Voulez vous imprimer ce relevé  ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                '''''''''''''bImprimer_Click(sender, e)

                ''''''''''''''''
                Dim TypeReleve As String = ""
                Dim CondCrystal As String = ""

                CondCrystal = "1=1 AND {Vue_EtatReleveCNAM.NumeroReleve} = '" + NumeroReleve + "'"

                ' '' ''If TypeReleve = "Regles" Then
                ' '' ''    CondCrystal += " AND {Vue_EtatReleveCNAM.Reste} = 0"
                ' '' ''ElseIf TypeReleve = "Non Regles" Then
                ' '' ''    CondCrystal += " AND {Vue_EtatReleveCNAM.Reste} <> 0"
                ' '' ''ElseIf TypeReleve = "Rien" Then
                ' '' ''    Exit Sub
                ' '' ''End If

                Dim Ii As Integer
                Dim num As Integer = 999
                For Ii = 0 To fMain.Tab.TabPages.Count - 1
                    If fMain.Tab.TabPages(Ii).Text = "Impression de relevé Cnam" Then
                        num = Ii
                    End If
                Next
                CR.FileName = Application.StartupPath + "\EtatDeReleveCNAM.rpt"

                CR.SetParameterValue("DateDebut", dtDebut.Text)
                CR.SetParameterValue("DateFin", dtFin.Text)

                CR.SetParameterValue("ChiffreEnLettre", ChiffresEnLettres(CDbl(lTotalRembourser.Text)))
                dsReleveSearch.Tables("RELEVE_CNAM").Rows(0).Item("PriseEnCharge") = rdbPriseEnCharge.Checked
                dsReleveSearch.Tables("RELEVE_CNAM").Rows(0).Item("Appareillage") = rdbAppareillage.Checked

                If dsReleveSearch.Tables("RELEVE_CNAM").Rows(0).Item("PriseEnCharge") = True Then
                    CR.SetParameterValue("Titre", "DANS LE CADRE DU ACCORD PREALABLE")
                    CR.SetParameterValue("NCNSS", "")
                ElseIf dsReleveSearch.Tables("RELEVE_CNAM").Rows(0).Item("Appareillage") = True Then
                    CR.SetParameterValue("Titre", "")
                    Try
                        CR.SetParameterValue("NCNSS", "N°CNSS : " + RecupererValeurExecuteScalaire("Affiliation1", "PARAMETRE_PHARMACIE", "Code", "1") + "/" + RecupererValeurExecuteScalaire("Affiliation2", "PARAMETRE_PHARMACIE", "Code", "1"))
                    Catch ex As Exception
                        CR.SetParameterValue("NCNSS", "")
                    End Try
                Else
                    CR.SetParameterValue("Titre", "DANS LE CADRE DU TIERS PAYANT")
                    CR.SetParameterValue("NCNSS", "")
                End If

                Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
                Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
                For Each tbCurrent In CR.Database.Tables
                    tliCurrent = tbCurrent.LogOnInfo
                    With tliCurrent.ConnectionInfo
                        .ServerName = NomServeur
                        .UserID = NomUtilisateurSQL
                        .Password = MotDePasseSQL
                        .DatabaseName = NomBase
                    End With
                    tbCurrent.ApplyLogOnInfo(tliCurrent)
                Next tbCurrent
                CR.RecordSelectionFormula = CondCrystal

                Dim MyViewer As New fViewer
                MyViewer.CRViewer.ReportSource = CR
                fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
                fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
                fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
                fMain.Tab.SelectedTab.Text = "Impression de relevé Cnam"
                If num <> 999 Then
                    fMain.Tab.TabPages(num).Dispose()
                End If
                Me.Hide()
                ''''''''''''''''




            End If

            Me.Dispose()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données (Enregistrement des détails vente) !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsReleveSearch.Reset()
        End Try

    End Sub
    Public Function RecupereNumero()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max([NumeroReleve]) FROM [RELEVE_CNAM]"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero sequenciel de l année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If

        Return ValeurRetour

    End Function

    Private Sub rdbTous_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbTous.CheckedChanged

        '' ''Dim StrSQL As String = ""
        '' ''Dim cmd As New SqlCommand
        '' ''Dim DerniereDate As String = ""

        '' ''If rdbTiersPayant.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE TiersPayant=1"
        '' ''ElseIf rdbPriseEnCharge.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE PriseEnCharge=1"
        '' ''ElseIf rdbMaladiesOrdinaires.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE MaladieOrdinaire=1"
        '' ''ElseIf rdbAppareillage.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE Appareillage=1"
        '' ''Else
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM]"
        '' ''End If

        '' ''cmd.Connection = ConnectionServeur
        '' ''cmd.CommandText = StrSQL
        '' ''Try
        '' ''    DerniereDate = DateAdd(DateInterval.Day, 1, Convert.ToDateTime(cmd.ExecuteScalar()))
        '' ''Catch ex As Exception
        '' ''    Console.WriteLine(ex.Message)
        '' ''    DerniereDate = Now.Date
        '' ''End Try

        '' ''dtDebut.Value = Now.Date 'DerniereDate
        '' ''dtFin.Value = Now.Date

        AfficherReleve()
    End Sub

    Private Sub rdbTiersPayant_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbTiersPayant.CheckedChanged

        '' ''Dim StrSQL As String = ""
        '' ''Dim cmd As New SqlCommand
        '' ''Dim DerniereDate As String = ""

        '' ''If rdbTiersPayant.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE TiersPayant=1"
        '' ''ElseIf rdbPriseEnCharge.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE PriseEnCharge=1"
        '' ''ElseIf rdbMaladiesOrdinaires.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE MaladieOrdinaire=1"
        '' ''ElseIf rdbAppareillage.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE Appareillage=1"
        '' ''Else
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM]"
        '' ''End If

        '' ''cmd.Connection = ConnectionServeur
        '' ''cmd.CommandText = StrSQL
        '' ''Try
        '' ''    DerniereDate = DateAdd(DateInterval.Day, 1, Convert.ToDateTime(cmd.ExecuteScalar()))
        '' ''Catch ex As Exception
        '' ''    Console.WriteLine(ex.Message)
        '' ''    DerniereDate = Now.Date
        '' ''End Try

        '' ''dtDebut.Value = Now.Date ' DerniereDate
        '' ''dtFin.Value = Now.Date

        AfficherReleve()
    End Sub

    Private Sub rdbPriseEnCharge_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbPriseEnCharge.CheckedChanged
        '' ''Dim StrSQL As String = ""
        '' ''Dim cmd As New SqlCommand
        '' ''Dim DerniereDate As String = ""

        '' ''If rdbTiersPayant.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE TiersPayant=1"
        '' ''ElseIf rdbPriseEnCharge.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE PriseEnCharge=1"
        '' ''ElseIf rdbMaladiesOrdinaires.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE MaladieOrdinaire=1"
        '' ''ElseIf rdbAppareillage.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE Appareillage=1"
        '' ''Else
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM]"
        '' ''End If

        '' ''cmd.Connection = ConnectionServeur
        '' ''cmd.CommandText = StrSQL
        '' ''Try
        '' ''    DerniereDate = DateAdd(DateInterval.Day, 1, Convert.ToDateTime(cmd.ExecuteScalar()))
        '' ''Catch ex As Exception
        '' ''    Console.WriteLine(ex.Message)
        '' ''    DerniereDate = Now.Date
        '' ''End Try

        '' ''dtDebut.Value = Now.Date ' DerniereDate
        '' ''dtFin.Value = Now.Date

        AfficherReleve()
    End Sub

    Private Sub rdbMaladiesOrdinaires_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbMaladiesOrdinaires.CheckedChanged
        '' ''Dim StrSQL As String = ""
        '' ''Dim cmd As New SqlCommand
        '' ''Dim DerniereDate As String = ""

        '' ''If rdbTiersPayant.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE TiersPayant=1"
        '' ''ElseIf rdbPriseEnCharge.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE PriseEnCharge=1"
        '' ''ElseIf rdbMaladiesOrdinaires.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE MaladieOrdinaire=1"
        '' ''ElseIf rdbAppareillage.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE Appareillage=1"
        '' ''Else
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM]"
        '' ''End If

        '' ''cmd.Connection = ConnectionServeur
        '' ''cmd.CommandText = StrSQL
        '' ''Try
        '' ''    DerniereDate = DateAdd(DateInterval.Day, 1, Convert.ToDateTime(cmd.ExecuteScalar()))
        '' ''Catch ex As Exception
        '' ''    Console.WriteLine(ex.Message)
        '' ''    DerniereDate = Now.Date
        '' ''End Try

        '' ''dtDebut.Value = Now.Date 'DerniereDate
        '' ''dtFin.Value = Now.Date

        AfficherReleve()
    End Sub

    Private Sub rdbTiersPayantMaladieOrdinaire_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbTiersPayantMaladieOrdinaire.CheckedChanged
        '' ''Dim StrSQL As String = ""
        '' ''Dim cmd As New SqlCommand
        '' ''Dim DerniereDate As String = ""

        '' ''If rdbTiersPayant.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE TiersPayant=1"
        '' ''ElseIf rdbPriseEnCharge.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE PriseEnCharge=1"
        '' ''ElseIf rdbMaladiesOrdinaires.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE MaladieOrdinaire=1"
        '' ''ElseIf rdbAppareillage.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE Appareillage=1"
        '' ''ElseIf rdbTiersPayantMaladieOrdinaire.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE TiersPayant=1 AND MaladieOrdinaire=1"
        '' ''Else
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM]"
        '' ''End If

        '' ''cmd.Connection = ConnectionServeur
        '' ''cmd.CommandText = StrSQL
        '' ''Try
        '' ''    DerniereDate = DateAdd(DateInterval.Day, 1, Convert.ToDateTime(cmd.ExecuteScalar()))
        '' ''Catch ex As Exception
        '' ''    Console.WriteLine(ex.Message)
        '' ''    DerniereDate = Now.Date
        '' ''End Try

        '' ''dtDebut.Value = Now.Date 'DerniereDate
        '' ''dtFin.Value = Now.Date

        AfficherReleve()
    End Sub

    Private Sub rdbAppareillage_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbAppareillage.CheckedChanged

        '' ''Dim StrSQL As String = ""
        '' ''Dim cmd As New SqlCommand
        '' ''Dim DerniereDate As String = ""

        '' ''If rdbTiersPayant.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE TiersPayant=1"
        '' ''ElseIf rdbPriseEnCharge.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE PriseEnCharge=1"
        '' ''ElseIf rdbMaladiesOrdinaires.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE MaladieOrdinaire=1"
        '' ''ElseIf rdbAppareillage.Checked Then
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE Appareillage=1"
        '' ''Else
        '' ''    StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM]"
        '' ''End If

        '' ''cmd.Connection = ConnectionServeur
        '' ''cmd.CommandText = StrSQL
        '' ''Try
        '' ''    DerniereDate = DateAdd(DateInterval.Day, 1, Convert.ToDateTime(cmd.ExecuteScalar()))
        '' ''Catch ex As Exception
        '' ''    Console.WriteLine(ex.Message)
        '' ''    DerniereDate = Now.Date
        '' ''End Try

        '' ''dtDebut.Value = Now.Date 'DerniereDate
        '' ''dtFin.Value = Now.Date

        If rdbAppareillage.Checked Then
            lCodeApp.Visible = True
            cmbCodeApp.Visible = True
            chbTous.Visible = True
        Else
            lCodeApp.Visible = False
            cmbCodeApp.Visible = False
            chbTous.Visible = False
        End If

        AfficherReleve()
    End Sub

    Private Sub fCritereDuReleveCNAM_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DerniereDate As String = ""

        dtDebut.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtDebut.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtFin.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtFin.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate



        If rdbTiersPayant.Checked Then
            StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE TiersPayant=1"
        ElseIf rdbPriseEnCharge.Checked Then
            StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE PriseEnCharge=1"
        ElseIf rdbMaladiesOrdinaires.Checked Then
            StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE MaladieOrdinaire=1"
        ElseIf rdbAppareillage.Checked Then
            StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM] WHERE Appareillage=1"
        Else
            StrSQL = " SELECT max([DateFin]) FROM [RELEVE_CNAM]"
        End If

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL
        Try
            DerniereDate = DateAdd(DateInterval.Day, 1, Convert.ToDateTime(cmd.ExecuteScalar()))
        Catch ex As Exception
            Console.WriteLine(ex.Message)
            DerniereDate = Now.Date
        End Try

        dtDebut.Value = Now.Date 'DerniereDate
        dtFin.Value = Now.Date
        Try
            rdbTiersPayantMaladieOrdinaire.Checked = True
        Catch ex As Exception

        End Try
        rdbDate.Checked = True
        AfficherReleve()

    End Sub

    Private Sub PAnel_Paint(ByVal sender As System.Object, ByVal e As System.Windows.Forms.PaintEventArgs) Handles PAnel.Paint

    End Sub

    Private Sub dtDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            dtFin.Focus()
        End If

        If e.KeyCode = Keys.F3 Then
            bValider_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F12 Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Private Sub dtDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtDebut.TextChanged

    End Sub

    Private Sub dtFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherReleve()
        End If

        If e.KeyCode = Keys.F3 Then
            bValider_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F12 Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Private Sub dtFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtFin.TextChanged

    End Sub

    Private Sub rdbDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbDate.CheckedChanged
        AfficherReleve()
    End Sub

    Private Sub rdbNCNAM_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbNCNAM.CheckedChanged
        AfficherReleve()
    End Sub

    Private Sub rdbDate_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbDate.KeyUp
        If e.KeyCode = Keys.F3 Then
            bValider_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F12 Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Private Sub rdbNCNAM_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbNCNAM.KeyUp
        If e.KeyCode = Keys.F3 Then
            bValider_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F12 Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim I As Integer = 0
        Dim StrSQL As String = ""
        Dim CodeMedecin As String = ""
        Dim MontantCnamAPayer As Double = 0.0

        Dim FieldDef As CrystalDecisions.CrystalReports.Engine.FieldDefinition
        Dim OrdreBy As String = ""

        'remplissage de la table temporaire 

        Try
            cmdReleveSearch.Connection = ConnectionServeur
            cmdReleveSearch.CommandText = "DELETE FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_RELEVE_CNAM "
            cmdReleveSearch.ExecuteNonQuery()
        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
        End Try


        For I = 0 To dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows.Count - 1

            If rdbPriseEnCharge.Checked Then
                CodeMedecin = RecupererValeurExecuteScalaire("IdentifiantCNAMMedecin", "VENTE", "NumeroVente", dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("NumeroVente").ToString)
            Else
                CodeMedecin = RecupererValeurExecuteScalaire("IdentifiantCNAM", "MEDECIN", "CodeMedecin", dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("CodeMedecin").ToString)
            End If

            MontantCnamAPayer = MontantCnamAPayer + dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("MontantCnam")

            StrSQL = "INSERT INTO TABLE_TEMPORAIRE_POUR_IMPRESSION_RELEVE_CNAM " + _
                    "(""NumeroVente""" + _
                    ",""Nom""" + _
                    ",""LibelleLienDeParente""" + _
                    ",""CodeAPCI""" + _
                    ",""Date""" + _
                    ",""TotalTTC""" + _
                    ",""MontantRegleParClient""" + _
                    ",""MontantCnam""" + _
                    ",""CodeCNAMMedecinPrescripteur""" + _
                    ",""IdentifiantCnam""" + _
                    ",""Reste"") " + _
                    " VALUES('" + _
                    dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("NumeroVente").ToString + _
                    "','" + dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("NomMalade").ToString + _
                    "','" + dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("LibelleLienDeParente").ToString + _
                     "','" + RecupererValeurExecuteScalaire("NomAPCI", "APCI", "CodeAPCI", dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("CodeAPCI").ToString) + _
                    "','" + dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("Date").ToString.Substring(0, 10) + _
                    "','" + dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("TotalTTC").ToString + _
                    "','" + (dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("TotalTTC") - dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("MontantCnam")).ToString + _
                    "','" + dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("MontantCnam").ToString + _
                    "','" + IIf(CodeMedecin = "", dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("CodeMedecin").ToString, CodeMedecin) + _
                    "','" + dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("IdentifiantCnam").ToString + _
                    "','" + dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(I).Item("MontantCnam").ToString + " ')"

            cmdReleveSearch.Connection = ConnectionServeur
            cmdReleveSearch.CommandText = StrSQL
            Try
                cmdReleveSearch.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
        Next

        Dim CondCrystal As String = ""
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression de relevé Cnam" Then
                num = I
            End If
        Next
        CR.FileName = Application.StartupPath + "\EtatDeReleveCNAMTemporaire.rpt"

        CR.SetParameterValue("DateDebut", dtDebut.Text)
        CR.SetParameterValue("DateFin", dtFin.Text)
        CR.SetParameterValue("TotalAPayer", MontantCnamAPayer)
        CR.SetParameterValue("ChiffreEnLettre", ChiffresEnLettres(CDbl(MontantCnamAPayer)))

        If rdbPriseEnCharge.Checked Then
            CR.SetParameterValue("Titre", "DANS LE CADRE DU ACCORD PREALABLE")
            CR.SetParameterValue("NCNSS", "")
        ElseIf rdbAppareillage.Checked Then
            CR.SetParameterValue("Titre", "")
            Try
                CR.SetParameterValue("NCNSS", "N°CNSS : " + RecupererValeurExecuteScalaire("Affiliation1", "PARAMETRE_PHARMACIE", "Code", "1") + "/" + RecupererValeurExecuteScalaire("Affiliation2", "PARAMETRE_PHARMACIE", "Code", "1"))
            Catch ex As Exception
                CR.SetParameterValue("NCNSS", "")
            End Try
        Else
            CR.SetParameterValue("Titre", "DANS LE CADRE DU TIERS PAYANT")
            CR.SetParameterValue("NCNSS", "")
        End If

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent

        'Critére de Trie
        If rdbDate.Checked = True Then
            OrdreBy = "Date"
        ElseIf rdbNCNAM.Checked = True Then
            OrdreBy = "IdentifiantCnam"
        End If

        FieldDef = CR.Database.Tables("TABLE_TEMPORAIRE_POUR_IMPRESSION_RELEVE_CNAM").Fields(OrdreBy)
        CR.DataDefinition.SortFields(0).Field = FieldDef

        FieldDef = CR.Database.Tables("TABLE_TEMPORAIRE_POUR_IMPRESSION_RELEVE_CNAM").Fields("NumeroVente")
        CR.DataDefinition.SortFields(1).Field = FieldDef

        CR.RecordSelectionFormula = CondCrystal

        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression de relevé Cnam"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
        Me.Hide()
    End Sub

    Private Sub rdbTous_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbTous.KeyUp
        If e.KeyCode = Keys.F3 Then
            bValider_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F12 Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Private Sub rdbTiersPayant_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbTiersPayant.KeyUp
        If e.KeyCode = Keys.F3 Then
            bValider_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F12 Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Private Sub rdbPriseEnCharge_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbPriseEnCharge.KeyUp
        If e.KeyCode = Keys.F3 Then
            bValider_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F12 Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Private Sub rdbMaladiesOrdinaires_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbMaladiesOrdinaires.KeyUp
        If e.KeyCode = Keys.F3 Then
            bValider_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F12 Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Private Sub rdbTiersPayantMaladieOrdinaire_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbTiersPayantMaladieOrdinaire.KeyUp
        If e.KeyCode = Keys.F3 Then
            bValider_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F12 Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Private Sub rdbAppareillage_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbAppareillage.KeyUp
        If e.KeyCode = Keys.F3 Then
            bValider_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F12 Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Private Sub gReleves_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gReleves.Click

    End Sub

    Private Sub gReleves_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gReleves.KeyUp
        If e.KeyCode = Keys.F3 Then
            bValider_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F12 Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Private Sub bValider_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bValider.KeyUp
        If e.KeyCode = Keys.F3 Then
            bValider_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F12 Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Private Sub bImprimer_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bImprimer.KeyUp
        If e.KeyCode = Keys.F3 Then
            bValider_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F12 Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Private Sub bQuitter_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bQuitter.KeyUp
        If e.KeyCode = Keys.F3 Then
            bValider_Click(sender, e)
        ElseIf e.KeyCode = Keys.F9 Then
            bImprimer_Click(sender, e)
        ElseIf e.KeyCode = Keys.F12 Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Private Sub bSupprimer_Click(sender As System.Object, e As System.EventArgs) Handles bSupprimer.Click
        Try
            'Suivi du scénario
            fMessageException.Show("Releve CNAM", "fCritereDuReleveCNAM", "bSupprimer_Click", "NoException", "NoError", "Clic sur le bouton bSupprimer", False, True, False)
            dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows(gReleves.Row).Delete()
            dsReleveSearch.AcceptChanges()

            If gReleves.Row <= dsReleveSearch.Tables("RELEVE_CNAM_DETAILS_AFFICHE").Rows.Count - 1 Then
                'gReleves.Delete()
                CalculerMontants()
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Vente", "fVente", " bSupprimer_Click", ex.Message, "0000241", "Erreur d'exécution de bSupprimer_Click ", True, True, True)
        End Try
    End Sub

    Public Sub New()

        ' Cet appel est requis par le concepteur.
        InitializeComponent()
        initAppareillage()
        ' Ajoutez une initialisation quelconque après l'appel InitializeComponent().

    End Sub

    Private Sub initAppareillage()

        Dim StrSQL As String

        'Rechargement des Clients
        Try

            'vider DS
            If (dsReleveSearch.Tables.IndexOf("Appareillage") > -1) Then
                dsReleveSearch.Tables("Appareillage").Clear()
            End If


            StrSQL = "  SELECT DISTINCT " + _
                     "      CodeAppareillage " + _
                     "  FROM " + _
                     "      Vente " + _
                     "  WHERE " + _
                     "      VENTE.NumeroVente NOT IN (SELECT NumeroVente FROM RELEVE_CNAM_DETAILS)"
            cmdReleveSearch.Connection = ConnectionServeur
            cmdReleveSearch.CommandText = StrSQL
            daReleveSearch = New SqlDataAdapter(cmdReleveSearch)
            daReleveSearch.Fill(dsReleveSearch, "Appareillage")
            cmbCodeApp.DataSource = dsReleveSearch.Tables("Appareillage")
            cmbCodeApp.ValueMember = "CodeAppareillage"
            cmbCodeApp.DisplayMember = "CodeAppareillage"
            cmbCodeApp.ColumnHeaders = False
            cmbCodeApp.Splits(0).DisplayColumns("CodeAppareillage").Width = 60
            cmbCodeApp.Splits(0).ExtendRightColumn = True

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Vente", "fVene", " initClient", ex.Message, "0000259", "Erreur d'exécution de initClient ", True, True, True)

        End Try

    End Sub

    Private Sub cmbCodeApp_Change(sender As Object, e As System.EventArgs) Handles cmbCodeApp.Change
        AfficherReleve()
    End Sub

    Private Sub chbCnam1_CheckedChanged(sender As System.Object, e As System.EventArgs) Handles chbTous.CheckedChanged
        AfficherReleve()
    End Sub
End Class