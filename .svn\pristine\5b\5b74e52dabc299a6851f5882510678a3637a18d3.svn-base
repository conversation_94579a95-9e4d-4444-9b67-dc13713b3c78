﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSet_ETAT_FICHE_DE_CONTACT" targetNamespace="http://tempuri.org/DataSet_ETAT_FICHE_DE_CONTACT.xsd" xmlns:mstns="http://tempuri.org/DataSet_ETAT_FICHE_DE_CONTACT.xsd" xmlns="http://tempuri.org/DataSet_ETAT_FICHE_DE_CONTACT.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSet_ETAT_FICHE_DE_CONTACT" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DataSet_ETAT_FICHE_DE_CONTACT" msprop:Generator_UserDSName="DataSet_ETAT_FICHE_DE_CONTACT">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="FicheDeContact" msprop:Generator_TableClassName="FicheDeContactDataTable" msprop:Generator_TableVarName="tableFicheDeContact" msprop:Generator_TablePropName="FicheDeContact" msprop:Generator_RowDeletingName="FicheDeContactRowDeleting" msprop:Generator_UserTableName="FicheDeContact" msprop:Generator_RowChangingName="FicheDeContactRowChanging" msprop:Generator_RowEvHandlerName="FicheDeContactRowChangeEventHandler" msprop:Generator_RowDeletedName="FicheDeContactRowDeleted" msprop:Generator_RowEvArgName="FicheDeContactRowChangeEvent" msprop:Generator_RowChangedName="FicheDeContactRowChanged" msprop:Generator_RowClassName="FicheDeContactRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Nom" msprop:Generator_ColumnVarNameInTable="columnNom" msprop:Generator_ColumnPropNameInRow="Nom" msprop:Generator_ColumnPropNameInTable="NomColumn" msprop:Generator_UserColumnName="Nom" type="xs:string" minOccurs="0" />
              <xs:element name="Specialite" msprop:Generator_ColumnVarNameInTable="columnSpecialite" msprop:Generator_ColumnPropNameInRow="Specialite" msprop:Generator_ColumnPropNameInTable="SpecialiteColumn" msprop:Generator_UserColumnName="Specialite" type="xs:string" minOccurs="0" />
              <xs:element name="TypeEtablissement" msprop:Generator_ColumnVarNameInTable="columnTypeEtablissement" msprop:Generator_ColumnPropNameInRow="TypeEtablissement" msprop:Generator_ColumnPropNameInTable="TypeEtablissementColumn" msprop:Generator_UserColumnName="TypeEtablissement" type="xs:string" minOccurs="0" />
              <xs:element name="Gouvernorat" msprop:Generator_ColumnVarNameInTable="columnGouvernorat" msprop:Generator_ColumnPropNameInRow="Gouvernorat" msprop:Generator_ColumnPropNameInTable="GouvernoratColumn" msprop:Generator_UserColumnName="Gouvernorat" type="xs:string" minOccurs="0" />
              <xs:element name="Ville" msprop:Generator_ColumnVarNameInTable="columnVille" msprop:Generator_ColumnPropNameInRow="Ville" msprop:Generator_ColumnPropNameInTable="VilleColumn" msprop:Generator_UserColumnName="Ville" type="xs:string" minOccurs="0" />
              <xs:element name="Telephone" msprop:Generator_ColumnVarNameInTable="columnTelephone" msprop:Generator_ColumnPropNameInRow="Telephone" msprop:Generator_ColumnPropNameInTable="TelephoneColumn" msprop:Generator_UserColumnName="Telephone" type="xs:string" minOccurs="0" />
              <xs:element name="Fax" msprop:Generator_ColumnVarNameInTable="columnFax" msprop:Generator_ColumnPropNameInRow="Fax" msprop:Generator_ColumnPropNameInTable="FaxColumn" msprop:Generator_UserColumnName="Fax" type="xs:string" minOccurs="0" />
              <xs:element name="Sexe" msprop:Generator_ColumnVarNameInTable="columnSexe" msprop:Generator_ColumnPropNameInRow="Sexe" msprop:Generator_ColumnPropNameInTable="SexeColumn" msprop:Generator_UserColumnName="Sexe" type="xs:string" minOccurs="0" />
              <xs:element name="Pharmacie" msprop:Generator_ColumnVarNameInTable="columnPharmacie" msprop:Generator_ColumnPropNameInRow="Pharmacie" msprop:Generator_ColumnPropNameInTable="PharmacieColumn" msprop:Generator_UserColumnName="Pharmacie" type="xs:string" minOccurs="0" />
              <xs:element name="AdressePharmacie" msprop:Generator_ColumnVarNameInTable="columnAdressePharmacie" msprop:Generator_ColumnPropNameInRow="AdressePharmacie" msprop:Generator_ColumnPropNameInTable="AdressePharmacieColumn" msprop:Generator_UserColumnName="AdressePharmacie" type="xs:string" minOccurs="0" />
              <xs:element name="Map" msprop:Generator_ColumnVarNameInTable="columnMap" msprop:Generator_ColumnPropNameInRow="Map" msprop:Generator_ColumnPropNameInTable="MapColumn" msprop:Generator_UserColumnName="Map" type="xs:base64Binary" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>