﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fEtatInventaire
    Dim cmdNature As New SqlCommand
    Dim daNature As New SqlDataAdapter
    Dim dsNature As New DataSet

    Dim cmdSortie As New SqlCommand
    Dim daSortie As New SqlDataAdapter
    Dim dsSortie As New DataSet

    Dim ValeurAchatInitial As Double = 0
    Dim ValeurVenteInitial As Double = 0
    Dim ValeurAchatFinal As Double = 0
    Dim ValeurVenteFinal As Double = 0
    Dim DifferenceAchat As Double = 0
    Dim DifferenceVente As Double = 0

    Dim CondCrystalReport As String = ""

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub Init()

        dtpDebut.Text = Today
        dtpFin.Text = Today
        dtpDebut.Focus()

        If ModeADMIN = "ADMIN" Then
            Label4.Visible = True
            Label6.Visible = True
            Label9.Visible = True
            Label7.Visible = True
            Label10.Visible = True
            Label5.Visible = True
            tValeurAchatInitial.Visible = True
            tValeurVenteInitial.Visible = True
            tValeurAchatFinal.Visible = True
            tValeurVenteFinal.Visible = True
            tDifferenceAchat.Visible = True
            tDifferenceVente.Visible = True
        Else
            Label4.Visible = False
            Label6.Visible = False
            Label9.Visible = False
            Label7.Visible = False
            Label10.Visible = False
            Label5.Visible = False
            tValeurAchatInitial.Visible = False
            tValeurVenteInitial.Visible = False
            tValeurAchatFinal.Visible = False
            tValeurVenteFinal.Visible = False
            tDifferenceAchat.Visible = False
            tDifferenceVente.Visible = False
        End If

        AfficherSortie()
    End Sub

    Public Sub AfficherSortie()
        Dim I As Integer
        Dim Cond As String = "1=1"
        CondCrystalReport = "1=1"
        dsSortie.Clear()

        If dtpDebut.Text <> "" And dtpDebut.Text.Length = 10 And dtpFin.Text <> "" And dtpFin.Text.Length = 10 Then
            Cond += " AND Date BETWEEN " + Quote(dtpDebut.Text + " 00:00:00") + " AND " + Quote(dtpFin.Text + " 23:59:59")
            CondCrystalReport += " AND {INVENTAIRE.Date} >= DateTime('" + dtpDebut.Text + " 00:00:00')"
            CondCrystalReport += " AND {INVENTAIRE.Date} <= DateTime('" + dtpFin.Text + " 23:59:59')"
        End If

        cmdSortie.CommandText = "SELECT [NumeroInventaire] " + _
                                "      ,[Date] " + _
                                "      ,[ValeurAchatInitial] " + _
                                "      ,[ValeurVenteInitial] " + _
                                "      ,[ValeurAchatActuelle] " + _
                                "      ,[ValeurVenteActuelle] " + _
                                "      ,[ValeurAchatDifference] " + _
                                "      ,[ValeurVenteDifference] " + _
                                "      ,[Remarque] " + _
                                "      ,[CodePersonnel] " + _
                                "      ,Nom " + _
                                "      ,[Valide] " + _
                                " FROM [VUE_INVENTAIRE] " + _
                                " JOIN UTILISATEUR ON UTILISATEUR.CodeUtilisateur = VUE_INVENTAIRE.CodePersonnel " + _
                                " WHERE " + Cond + " ORDER BY Date ASC"

        cmdSortie.Connection = ConnectionServeur
        daSortie = New SqlDataAdapter(cmdSortie)
        daSortie.Fill(dsSortie, "SORTIE")

        With gSortie
            .Columns.Clear()
            .DataSource = dsSortie
            .DataMember = "SORTIE"
            .Rebind(False)
            .Columns("NumeroInventaire").Caption = "Numéro"
            .Columns("Date").Caption = "Date"
            .Columns("ValeurAchatInitial").Caption = "Valeur Achat Initial"
            .Columns("ValeurVenteInitial").Caption = "Valeur Vente Initial"

            .Columns("ValeurAchatActuelle").Caption = "Valeur Achat Actuelle"
            .Columns("ValeurVenteActuelle").Caption = "Valeur Vente Actuelle"

            .Columns("ValeurAchatDifference").Caption = "Difference Achat"
            .Columns("ValeurVenteDifference").Caption = "Difference Vente"

            .Columns("Date").NumberFormat = "dd/MM/yyyy"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("CodePersonnel").Visible = False
            .Splits(0).DisplayColumns("Valide").Visible = False
            .Splits(0).DisplayColumns("Remarque").Visible = False

            .Splits(0).DisplayColumns("NumeroInventaire").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("NumeroInventaire").Width = 100
            .Splits(0).DisplayColumns("Date").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Date").Width = 100

            .Splits(0).DisplayColumns("ValeurAchatInitial").Width = 140
            .Splits(0).DisplayColumns("ValeurVenteInitial").Width = 140
            .Splits(0).DisplayColumns("ValeurAchatActuelle").Width = 140
            .Splits(0).DisplayColumns("ValeurVenteActuelle").Width = 140
            .Splits(0).DisplayColumns("ValeurAchatDifference").Width = 140
            .Splits(0).DisplayColumns("ValeurVenteDifference").Width = 140


            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gSortie)
        End With

        CalculValeur()
    End Sub

    Private Sub gSortie_DoubleClick(sender As Object, e As System.EventArgs) Handles gSortie.DoubleClick
        bAfficherDetails_Click(sender, e)
    End Sub

    Private Sub gSortie_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gSortie.FetchRowStyle
        e.CellStyle.Font = New System.Drawing.Font("Calibri", 9, FontStyle.Regular)
    End Sub
    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherSortie()
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDebut.TextChanged

    End Sub


    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherSortie()
        End If
    End Sub

    Private Sub dtpFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpFin.TextChanged

    End Sub

    Private Sub cmbNature_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub CalculValeur()

        ValeurAchatInitial = 0.0
        ValeurVenteInitial = 0.0
        ValeurAchatFinal = 0.0
        ValeurVenteFinal = 0.0
        DifferenceAchat = 0.0
        DifferenceVente = 0.0

        If gSortie.RowCount <> 0 Then
            For I As Integer = 0 To gSortie.RowCount - 1
                ValeurAchatInitial += gSortie(I, "ValeurAchatInitial")
                ValeurVenteInitial += gSortie(I, "ValeurVenteInitial")

                ValeurAchatFinal += gSortie(I, "ValeurAchatActuelle")
                ValeurVenteFinal += gSortie(I, "ValeurVenteActuelle")

            Next
        End If

        DifferenceAchat = ValeurAchatFinal - ValeurAchatInitial
        DifferenceVente = ValeurVenteFinal - ValeurVenteInitial

        tValeurAchatInitial.Text = Format(ValeurAchatInitial, "0.000")
        tValeurVenteInitial.Text = Format(ValeurVenteInitial, "0.000")
        tValeurAchatFinal.Text = Format(ValeurAchatFinal, "0.000")
        tValeurVenteFinal.Text = Format(ValeurVenteFinal, "0.000")
        tDifferenceAchat.Text = Format(DifferenceAchat, "0.000")
        tDifferenceVente.Text = Format(DifferenceVente, "0.000")

    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click

        If gSortie.RowCount > 0 Then
            Dim I As Integer
            Dim CondCrystal As String = " 1=1 "

            For I = 0 To fMain.Tab.TabPages.Count - 1
                If fMain.Tab.TabPages(I).Text = "Imprimer Etat des inventaires" Then
                    fMain.Tab.TabPages(I).Show()
                    Exit Sub
                End If
            Next

            If dtpDebut.Text <> "" Then
                CondCrystal = CondCrystal + " AND {INVENTAIRE.Date} >= Date('" & dtpDebut.Text & "') "

            End If

            If dtpFin.Text <> "" Then
                CondCrystal = CondCrystal + " AND {INVENTAIRE.Date} <= Date('" & dtpFin.Text & "')"


            End If

            CR.FileName = Application.StartupPath + "\EtatListeInventaire.rpt"
            CR.SetParameterValue("debut", dtpDebut.Text)
            CR.SetParameterValue("fin", dtpFin.Text)

            'CR.SetParameterValue("ValeurAchat18", ValeurAchat18)
            'CR.SetParameterValue("ValeurVente18", ValeurVente18)
            'CR.SetParameterValue("ValeurAchat0", ValeurAchat0)
            'CR.SetParameterValue("ValeurVente0", ValeurVente0)

            Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent
            CR.RecordSelectionFormula = CondCrystalReport
            Dim MyViewer As New fViewer
            MyViewer.CRViewer.ReportSource = CR
            fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
            fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
            fMain.Tab.SelectedTab.Text = "Imprimer Etat des inventaires"
        End If
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub


    Private Sub bAfficherDetails_Click(sender As System.Object, e As System.EventArgs) Handles bAfficherDetails.Click
        Dim MyAchatAffiche As New fInventaire
        MyAchatAffiche.NumeroInventaire = gSortie(gSortie.Row, "NumeroInventaire")
        MyAchatAffiche.Init()

        '--------------------
        MyAchatAffiche.bQuitter.Visible = False
        MyAchatAffiche.bAnnuler.Visible = False
        MyAchatAffiche.bConfirmer.Visible = False
        MyAchatAffiche.bAjouter.Visible = False
        MyAchatAffiche.bListe.Visible = False
        MyAchatAffiche.bSupprimer.Visible = False
        MyAchatAffiche.bRecherche.Visible = False
        MyAchatAffiche.bImprimer.Visible = False
        MyAchatAffiche.bTerminal.Visible = False
        MyAchatAffiche.lNombreDesArticles.Visible = False
        'MyAchatAffiche.bLast.Visible = False
        'MyAchatAffiche.bNext.Visible = False
        'MyAchatAffiche.bPrevious.Visible = False
        'MyAchatAffiche.bFirst.Visible = False
        MyAchatAffiche.bAfficherLot.Visible = False

        '---------------------
        MyAchatAffiche.ShowDialog()
        MyAchatAffiche.Close()
        MyAchatAffiche.Dispose()
    End Sub
End Class