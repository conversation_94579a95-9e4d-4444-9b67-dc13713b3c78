@echo off
echo ========================================
echo    TEST SCANNER CODE A BARRES
echo    PHARMA2000 - Module Client
echo ========================================
echo.
echo Lancement de l'application de test...
echo.

cd /d "%~dp0"

if exist "bin\Debug\TestScannerClient.exe" (
    echo Application trouvee : bin\Debug\TestScannerClient.exe
    echo Demarrage...
    echo.
    start "" "bin\Debug\TestScannerClient.exe"
    echo.
    echo L'application de test du scanner est maintenant ouverte.
    echo.
    echo INSTRUCTIONS :
    echo - Utilisez votre scanner dans le champ "Code Client"
    echo - L'application detectera automatiquement les scans
    echo - Appuyez sur Enter apres chaque scan
    echo - Consultez l'historique des scans
    echo.
) else (
    echo ERREUR : Application non trouvee !
    echo Veuillez compiler le projet TestScannerClient.vbproj d'abord.
    echo.
)

echo Appuyez sur une touche pour fermer cette fenetre...
pause >nul
