﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports Microsoft.Reporting.WinForms
Imports System.Linq.Dynamic

Public Class fEtatDesVentes
    Dim _SalesReportService As New Bll.Reporting.SalesReport
    Dim _VOrderBy As String = "Id"
    Dim _VAscDesc As String = "Asc"

    Dim StrSQL As String = ""

    Dim cmdVente As New SqlCommand
    Dim daVente As New SqlDataAdapter
    Dim dsVente As New DataSet

    Public Initialisation As Boolean = False

    Public Source As String = ""

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If

    End Sub

    Public Sub init()
        'chargement des noms Mode paiements
        StrSQL = "SELECT DISTINCT CodeNatureReglement,LibelleNatureReglement FROM NATURE_REGLEMENT ORDER BY LibelleNatureReglement ASC"
        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = StrSQL
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "NATURE_REGLEMENT")
        cmbModePaiment.DataSource = dsVente.Tables("NATURE_REGLEMENT")
        cmbModePaiment.ValueMember = "CodeNatureReglement"
        cmbModePaiment.DisplayMember = "LibelleNatureReglement"
        cmbModePaiment.ColumnHeaders = False
        cmbModePaiment.Splits(0).DisplayColumns("CodeNatureReglement").Visible = False
        cmbModePaiment.Splits(0).DisplayColumns("LibelleNatureReglement").Width = 10
        cmbModePaiment.ExtendRightColumn = True

        'chargement des Clients
        StrSQL = "SELECT DISTINCT CodeClient,Nom FROM CLIENT WHERE Supprime=0 ORDER BY Nom ASC"
        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = StrSQL
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "CLIENT")
        cmbClient.DataSource = dsVente.Tables("CLIENT")
        cmbClient.ValueMember = "CodeClient"
        cmbClient.DisplayMember = "Nom"
        cmbClient.ColumnHeaders = False
        cmbClient.Splits(0).DisplayColumns("CodeClient").Visible = False
        cmbClient.Splits(0).DisplayColumns("Nom").Width = 10
        cmbClient.ExtendRightColumn = True

        'chargement des postes
        StrSQL = "SELECT DISTINCT LibellePoste FROM POSTE ORDER BY LibellePoste ASC"
        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = StrSQL
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "POSTE")
        cmbPoste.DataSource = dsVente.Tables("POSTE")
        cmbPoste.ValueMember = "LibellePoste"
        cmbPoste.DisplayMember = "LibellePoste"
        cmbPoste.ColumnHeaders = False
        'cmbPoste.Splits(0).DisplayColumns("LibellePoste").Visible = False
        cmbPoste.Splits(0).DisplayColumns("LibellePoste").Width = 10
        cmbPoste.ExtendRightColumn = True



        ''''''''''''''''''''''''''''
        ''StrSQL = "SELECT CodeCategorie ,LibelleCategorie FROM CATEGORIE order by LibelleCategorie ASC"
        ''cmdVente.Connection = ConnectionServeur
        ''cmdVente.CommandText = StrSQL
        ''daVente = New SqlDataAdapter(cmdVente)
        ''daVente.Fill(dsVente, "CATEGORIE")
        ''cmbCategorie.DataSource = dsVente.Tables("CATEGORIE")
        ''cmbCategorie.ValueMember = "CodeCategorie"
        ''cmbCategorie.DisplayMember = "LibelleCategorie"
        ''cmbCategorie.ColumnHeaders = False
        ''cmbCategorie.Splits(0).DisplayColumns("CodeCategorie").Visible = False
        ''cmbCategorie.Splits(0).DisplayColumns("LibelleCategorie").Width = 10
        ''cmbCategorie.ExtendRightColumn = True

        'chargement des Clients
        StrSQL = "SELECT DISTINCT CodeUtilisateur,Nom FROM UTILISATEUR WHERE supprime = 0 ORDER BY Nom ASC"
        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = StrSQL
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "VENDEUR")
        cmbvendeur.DataSource = dsVente.Tables("VENDEUR")
        cmbvendeur.ValueMember = "CodeUtilisateur"
        cmbvendeur.DisplayMember = "Nom"
        cmbvendeur.ColumnHeaders = False
        cmbvendeur.Splits(0).DisplayColumns("CodeUtilisateur").Visible = False
        cmbvendeur.Splits(0).DisplayColumns("Nom").Width = 10
        cmbvendeur.ExtendRightColumn = True
        ''''''''''''''''''''''''''''


        With cmbType
            .HoldFields()
            .AddItem("COMPTOIR")
            .AddItem("CNAM")
            .AddItem("MUTUELLE")
            .ColumnHeaders = False
        End With

        lClient.Visible = True
        cmbClient.Visible = True

        dtpDebut.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDebut.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpFin.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpFin.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        Initialisation = True
        rdbTous.Checked = True
        Initialisation = True
        Initialisation = True
        chbNonVidees.Checked = True
        Initialisation = True


        If ModeADMIN = "ADMIN" Then
            gbTotal.Visible = True
        Else
            gbTotal.Visible = False
        End If

        'AfficherVentes()

        cmbModePaiment.Focus()

    End Sub

    Public Sub AfficherVentes()

        Dim I As Integer
        Dim Cond As String = "1=1"
        Dim CondTotal As String = "1=1"

        Dim TotalTTC As Double = 0.0
        Dim PartCNAM As Double = 0.0
        Dim PartMutuelle As Double = 0.0
        Dim TotalRemise As Double = 0.0
        Dim TotalMarge As Double = 0.0

        Dim NombreVente As Integer = 0

        Dim Espece As Double = 0.0
        Dim Cheque As Double = 0.0
        Dim Carte As Double = 0.0
        Dim Credit As Double = 0.0

        If dtpDebut.Text = "" Then
            dtpDebut.Value = Date.Now
        End If
        If dtpFin.Text = "" Then
            dtpFin.Value = Date.Now
        End If

        Dim List As New Library.SortableBindingList(Of Data.Reporting.P_Report_EtatDesVentes_Result)(_SalesReportService.GetEtatDesVents(chbNonVidees.Checked, _
                                                                                                                                        dtpDebut.Value, _
                                                                                                                                        dtpFin.Value, _
                                                                                                                                        IIf(IsNothing(cmbClient.SelectedValue), "", cmbClient.SelectedValue), _
                                                                                                                                        cmbModePaiment.SelectedValue, _
                                                                                                                                        cmbType.Text, _
                                                                                                                                        IIf(IsNothing(cmbvendeur.SelectedValue), -1, Convert.ToInt32(cmbvendeur.SelectedValue)), _
                                                                                                                                        IIf(IsNothing(cmbPoste.SelectedValue), Nothing, Convert.ToInt32(cmbPoste.SelectedValue)),
                                                                                                                                        IIf(rdbTous.Checked, 2, IIf(rdbFacturees.Checked, 1, 0))))

        With gVentes
            .Columns.Clear()
            .DataSource = List
            .Rebind(False)
            .Columns("NumeroOperation").Caption = "Numéro"
            .Columns("Date").Caption = "Date"
            .Columns("Type").Caption = "Type"
            .Columns("Nom").Caption = "Nom"
            .Columns("MP").Caption = "M P"
            .Columns("Mutuelle").Caption = "Mutuelle"
            .Columns("TotalRemise").Caption = "Remise"
            .Columns("TotalTTC").Caption = "TTC"
            .Columns("LibellePoste").Caption = "Poste"
            .Columns("NomUtilisateur").Caption = "Vendeur"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("NumeroOperation").Width = 120            '
            .Splits(0).DisplayColumns("Date").Width = 200
            .Splits(0).DisplayColumns("Type").Width = 120
            .Splits(0).DisplayColumns("Nom").Width = 200
            .Splits(0).DisplayColumns("Nom").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("MP").Width = 100
            .Splits(0).DisplayColumns("TotalRemise").Width = 70
            .Splits(0).DisplayColumns("TotalTTC").Width = 100
            .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("LibellePoste").Width = 100
            .Splits(0).DisplayColumns("NomUtilisateur").Width = 80

            .Splits(0).DisplayColumns("MontantCnam").Visible = False
            .Splits(0).DisplayColumns("MontantMutuelle").Visible = False
            .Splits(0).DisplayColumns("Id").Visible = False
            .Splits(0).DisplayColumns("CodeClient").Visible = False
            .Splits(0).DisplayColumns("TypeOperation").Visible = False
            .Splits(0).DisplayColumns("NumeroFacture").Visible = False
            .Splits(0).DisplayColumns("CodeNatureReglement").Visible = False
            .Splits(0).DisplayColumns("CodeMutuelle").Visible = False
            .Splits(0).DisplayColumns("CodePersonnel").Visible = False
            .Splits(0).DisplayColumns("Credit").Visible = False
            .Splits(0).DisplayColumns("Debit").Visible = False
            .Splits(0).DisplayColumns("Vider").Visible = False
            .Splits(0).DisplayColumns("Mutuelle").Visible = False
            .Splits(0).DisplayColumns("Marge").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gVentes)
        End With

        For I = 0 To gVentes.RowCount - 1
            Try
                If gVentes(I, "TotalTTC").ToString <> "" Then
                    TotalTTC = TotalTTC + gVentes(I, "TotalTTC")
                End If
                If gVentes(I, "MontantCnam").ToString <> "" Then
                    PartCNAM = PartCNAM + gVentes(I, "MontantCnam")
                End If
                If gVentes(I, "MontantMutuelle").ToString Then
                    PartMutuelle = PartMutuelle + gVentes(I, "MontantMutuelle")
                End If
                If gVentes(I, "TotalRemise").ToString <> "" Then
                    TotalRemise = TotalRemise + gVentes(I, "TotalRemise")
                End If

                If gVentes(I, "NumeroOperation").ToString <> "" Then
                    NombreVente = NombreVente + 1
                End If

                'If gVentes(I, "MARGE").ToString <> "" Then
                '    TotalMarge = TotalMarge + gVentes(I, "MARGE")
                'End If



                Credit = Credit + (gVentes(I, "Debit") - gVentes(I, "Credit"))

                If gVentes(I, "MP").ToString = "CARTE" Then
                    Carte = Carte + gVentes(I, "Credit")
                End If

                If gVentes(I, "MP").ToString = "CHEQUE" Then
                    Cheque = Cheque + gVentes(I, "Credit")
                End If

                If gVentes(I, "MP").ToString = "ESPECE" Then
                    Espece = Espece + gVentes(I, "Credit")
                End If

                TotalMarge = TotalMarge + gVentes(I, "Marge")

            Catch
            End Try
        Next


        lTotalTTC.Text = TotalTTC.ToString("### ### ##0.000")
        lPartCNAM.Text = PartCNAM.ToString("### ### ##0.000")
        lPartMutuelle.Text = PartMutuelle.ToString("### ### ##0.000")
        lTotalRemise.Text = TotalRemise.ToString("### ### ##0.000")
        lTotMarge.Text = TotalMarge.ToString("### ### ##0.000")

        lNombreDesVentes.Text = NombreVente

        'lTotalMarge.Text = TotalMarge.ToString("### ### ##0.000")

        lEspece.Text = Espece.ToString("### ### ##0.000")
        lCheque.Text = Cheque.ToString("### ### ##0.000")
        lCarte.Text = Carte.ToString("### ### ##0.000")
        lCredit.Text = Credit.ToString("### ### ##0.000")

    End Sub

    Private Sub chbNonVidees_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbNonVidees.CheckedChanged
        If chbNonVidees.Checked = True Then
            ' dtpDebut.Clear()
            ' dtpFin.Clear()
            dtpDebut.Enabled = False
            dtpFin.Enabled = False
        Else
            dtpDebut.Enabled = True
            dtpFin.Enabled = True
            dtpDebut.Text = Date.Today
            dtpFin.Text = Date.Today
            dtpDebut.Focus()
        End If
        AfficherVentes()
    End Sub

    Private Sub cmbModePaiment_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbModePaiment.KeyUp

        If e.KeyCode = Keys.Enter Then
            cmbModePaiment.Text = cmbModePaiment.WillChangeToText
            AfficherVentes()
            cmbType.Focus()
        Else
            cmbModePaiment.OpenCombo()
        End If
    End Sub

    Private Sub cmbType_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbType.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbType.Text = cmbType.WillChangeToText
            AfficherVentes()
            cmbPoste.Focus()
        Else
            cmbType.OpenCombo()
        End If
    End Sub

    Private Sub cmbPoste_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbPoste.KeyUp

        If e.KeyCode = Keys.Enter Then
            cmbPoste.Text = cmbPoste.WillChangeToText
            AfficherVentes()
            cmbClient.Focus()
        Else
            cmbPoste.OpenCombo()
        End If

    End Sub
    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherVentes()
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            dtpFin.ValidateText()
            AfficherVentes()
            cmbModePaiment.Focus()
        End If
    End Sub

    Private Sub rdbTous_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbTous.CheckedChanged
        AfficherVentes()
    End Sub

    Private Sub rdbFacturees_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbFacturees.CheckedChanged
        AfficherVentes()
    End Sub

    Private Sub rdbNonFacturees_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbNonFacturees.CheckedChanged
        AfficherVentes()
    End Sub

    Private Sub rdbNumero_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        AfficherVentes()
    End Sub

    Private Sub rdbMontant_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        AfficherVentes()
    End Sub

    Private Sub rdbHeure_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        AfficherVentes()
    End Sub

    Private Sub rdbVendeur_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        AfficherVentes()
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Reporting Ventes" Then
                num = I
            End If
        Next
        Dim dt

        Dim _Parameters As New List(Of ReportParameter)()

        Dim _Vide As New ReportParameter()
        _Vide.Name = "Vide"
        _Vide.Values.Add(chbNonVidees.Checked)
        _Parameters.Add(_Vide)

        Dim _DateDebut As New ReportParameter()
        _DateDebut.Name = "DateDebut"
        _DateDebut.Values.Add(dtpDebut.Value)
        _Parameters.Add(_DateDebut)

        Dim _DateFin As New ReportParameter()
        _DateFin.Name = "DateFin"
        _DateFin.Values.Add(dtpFin.Value)
        _Parameters.Add(_DateFin)

        Dim _CodeClient As New ReportParameter()
        _CodeClient.Name = "CodeClient"
        _CodeClient.Values.Add(cmbClient.SelectedValue)
        _Parameters.Add(_CodeClient)

        Dim _ModePaiement As New ReportParameter()
        _ModePaiement.Name = "ModePaiement"
        _ModePaiement.Values.Add(cmbModePaiment.SelectedValue)
        _Parameters.Add(_ModePaiement)

        Dim _CodePersonnel As New ReportParameter()
        _CodePersonnel.Name = "CodePersonnel"
        _CodePersonnel.Values.Add(cmbvendeur.SelectedValue)
        _Parameters.Add(_CodeClient)

        Dim _Poste As New ReportParameter()
        _Poste.Name = "Poste"
        _Poste.Values.Add(cmbPoste.SelectedValue)
        _Parameters.Add(_Poste)

        Dim _Facture As New ReportParameter()
        _Facture.Name = "Facturer"
        _Facture.Values.Add(IIf(rdbFacturees.Checked, 2, IIf(rdbFacturees.Checked, 1, 0)))
        _Parameters.Add(_Facture)

        Dim _Type As New ReportParameter()
        _Type.Name = "Type"
        _Type.Values.Add(cmbType.Text)
        _Parameters.Add(_Type)

        Dim MyViewer As New fImpressionReportingVente

        dt = _SalesReportService.GetEtatDesVents(chbNonVidees.Checked, _
                                                dtpDebut.Value, _
                                                dtpFin.Value, _
                                                IIf(IsNothing(cmbClient.SelectedValue), "", cmbClient.SelectedValue), _
                                                cmbModePaiment.SelectedValue, _
                                                cmbType.Text, _
                                                IIf(IsNothing(cmbvendeur.SelectedValue), -1, Convert.ToInt32(cmbvendeur.SelectedValue)), _
                                                IIf(IsNothing(cmbPoste.SelectedValue), Nothing, Convert.ToInt32(cmbPoste.SelectedValue)),
                                                IIf(rdbTous.Checked, 2, IIf(rdbFacturees.Checked, 1, 0))).OrderBy(_VOrderBy + " " + _VAscDesc)

        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Etatdesventes", dt))
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Pharmacie", _SalesReportService.GetPharmacie()))
        MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatDesVentes.rdl"

        MyViewer.ReportViewer1.LocalReport.SetParameters(_Parameters)

        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel1)

        MyViewer.ReportViewer1.RefreshReport()

        fMain.Tab.SelectedTab.Text = "Impression Reporting Ventes"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    Private Sub lEspece_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lEspece.Click

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub cmbClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbClient.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbClient.Text = cmbClient.WillChangeToText
            cmbvendeur.Focus()
            AfficherVentes()
        Else
            cmbClient.OpenCombo()
        End If
    End Sub

    Private Sub cmbClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbClient.TextChanged

    End Sub

    Private Sub gVentes_AfterSort(sender As Object, e As FilterEventArgs) Handles gVentes.AfterSort
        Try
            _VOrderBy = e.Condition.Substring(0, e.Condition.IndexOf(" "))
            _VAscDesc = e.Condition.Substring(e.Condition.IndexOf(" ") + 1, e.Condition.Length - e.Condition.IndexOf(" ") - 1)
        Catch ex As Exception
            _VOrderBy = e.Condition
            _VAscDesc = "Asc"
        End Try
    End Sub

    Private Sub gVentes_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gVentes.Click

    End Sub

    Private Sub gVentes_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gVentes.KeyUp
        If e.KeyCode = Keys.F1 Then
            Dim MyVenteAffiche As New fVenteJusteAffichage
            MyVenteAffiche.NumeroVente = gVentes(gVentes.Row, "NumeroOperation")
            MyVenteAffiche.ShowDialog()
            MyVenteAffiche.Close()
            MyVenteAffiche.Dispose()

        End If
    End Sub

    Private Sub cmbvendeur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbvendeur.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbvendeur.Text = cmbvendeur.WillChangeToText
            AfficherVentes()
            'cmbvendeur.Focus()
        Else
            cmbvendeur.OpenCombo()
        End If
    End Sub

    Private Sub bAfficherDetails_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAfficherDetails.Click
        Try
            If gVentes.RowCount > 0 Then
                Dim MyVenteAffiche As New fVenteJusteAffichage
                MyVenteAffiche.NumeroVente = gVentes(gVentes.Row, "NumeroOperation")
                MyVenteAffiche.ShowDialog()
                MyVenteAffiche.Close()
                MyVenteAffiche.Dispose()
            End If
        Catch ex As Exception

        End Try

    End Sub
End Class