﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms
Imports System.Byte
Imports System.Drawing
Imports DirectX.Capture
Imports System.Drawing.Imaging
Imports System.IO.Ports
Imports System.Data.SqlTypes
Imports C1.Win.C1Input

Public Class fFicheClient

    Private Property DernierCodeFamille As Integer

    Declare Function TWAIN_AcquireToClipboard Lib "EZTW32.DLL" (ByVal hwndApp&, ByVal wPixTypes&) As Integer
    Declare Function TWAIN_SetHideUI Lib "Eztw32.dll" Alias "TWAIN_SetHideUI" (ByVal ui As Long) As Long
    Declare Function TWAIN_OpenDefaultSource Lib "Eztw32.DLL" Alias "TWAIN_OpenDefaultSource" (ByVal hwnd As Long) As Integer
    Declare Function TWAIN_SetCurrentResolution Lib "Eztw32.dll" Alias "TWAIN_SetCurrentResolution" (ByVal neufdix As Double) As Long
    Declare Function TWAIN_SetCurrentPixelType Lib "Eztw32.dll" Alias "TWAIN_SetCurrentPixelType" (ByVal deux As Long) As Long
    Declare Function TWAIN_LoadSourceManager Lib "Eztw32.dll" Alias "TWAIN_LoadSourceManager" () As Long
    Declare Function TWAIN_SetCurrentUnits Lib "Eztw32.dll" Alias "TWAIN_SetCurrentUnits" (ByVal zero As Long) As Long
    Declare Function TWAIN_SetBitDepth Lib "Eztw32.dll" Alias "TWAIN_SetBitDepth" (ByVal zero As Long) As Long
    Declare Function TWAIN_OpenSourceManager Lib "EZTW32.DLL" Alias "TWAIN_OpenSourceManager" (ByVal hwnd As Long) As Long
    Declare Function TWAIN_CloseSource Lib "EZTW32.DLL" Alias "TWAIN_CloseSource" () As Long
    Declare Function TWAIN_SelectImageSource Lib "EZTW32.DLL" Alias "TWAIN_SelectImageSource" (ByVal hwnd As Long) As Integer
    Declare Function TWAIN_State Lib "EZTW32.DLL" Alias "TWAIN_State" () As Integer

    Declare Function TWAIN_AcquireToFilename Lib "Eztwain3.dll" (ByVal hwndApp As Long, ByVal sFile As String) As Long
    Declare Function TWAIN_SelectFeeder Lib "EZTW32.DLL" (ByVal fYes As Long) As Long
    Declare Function TWAIN_SetCurrentResolution Lib "EZTW32.DLL" (ByVal nRes As Long) As Long

    Declare Function TWAIN_LogFile Lib "EZTW32.DLL" (ByVal fLog As Long) As Long
    Declare Function TWAIN_SetAutoScan Lib "EZTW32.DLL" (ByVal fYes As Long) As Long
    Declare Function TWAIN_SetRegion Lib "EZTW32.DLL" (ByVal L As Double, ByVal T As Double, ByVal R As Double, ByVal B As Double) As Long

    Declare Function TWAIN_AcquireMultipageFile Lib "EZTW32.DLL" (ByVal hwndApp As Long, ByVal FileName As String) As Long
    Declare Function TWAIN_LastErrorCode Lib "EZTW32.DLL" () As Long
    Declare Function TWAIN_ReportLastError Lib "EZTW32.DLL" (ByVal pzMsg As String) As Long

    Declare Function TWAIN_GetCurrentResolution Lib "EZTW32.DLL" () As Long

    Public ajoutmodif As String = ""
    Public AjoutModifLien As String = ""

    Public AjoutModifMedecinApci As String = ""

    Public AJoutModifReglement As String = ""
    Public CodeClient As String = ""
    Public NomClient As String = ""
    Public NomMutuelle As String = ""

    Dim cmdClient As New SqlCommand
    Dim cbClient As New SqlCommandBuilder
    Dim dsClient As New DataSet
    Dim daClient As New SqlDataAdapter

    Dim cmdReglement As New SqlCommand
    Dim cbReglement As New SqlCommandBuilder
    Dim daReglement As New SqlDataAdapter

    Dim cmdFacture As New SqlCommand
    Dim dsFacture As New DataSet
    Dim daFacture As New SqlDataAdapter

    Dim cmdFactureDetail As New SqlCommand
    Dim dsFactureDetail As New DataSet
    Dim daFactureDetail As New SqlDataAdapter

    Dim cmdLienFamille As New SqlCommand
    Dim cbLienFamille As New SqlCommandBuilder
    Dim dsLienFamille As New DataSet
    Dim daLienFamille As New SqlDataAdapter

    Dim dsRecupereNum As New DataSet
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNumt As New SqlDataAdapter
    Dim CodeExiste As Boolean = False

    Dim NumeroReglementAModifier As Integer = 0
    Dim NumeroVenteAAfficher As String = ""

    'Dim BlanchirListeVente As Boolean = False
    Dim BlanchirListeReglement As Boolean = False

    Dim CmdCalcul As New SqlCommand

    Private m_barrImg As Byte()
    Private iBytesRead As Integer
    Private ImageCarnet As Image

    Private BtnChoisir As String = ""

    'variable pour confirmer la validation ou l'annulation de l'enregistrement du nouveau client 
    'pour les clients crées depuis une vente

    Public ConfirmerEnregistrementDepuisVente As Boolean = False

    Private X_initial As Integer
    Private Y_initial As Integer
    Public FocusCodeCNAM As String = ""

    Dim CodeMedecinAjouter As String = ""
    Dim CodeFamilleModifMedecin As String

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "114" And bConfirmer.Enabled = True Then
            bConfirmer_Click(sender, e)
        End If
        If argument = "121" And bAnnuler.Enabled = True Then
            bAnnuler_Click(sender, e)
        End If
        If argument = "116" And bAjouterReglement.Enabled = True Then
            bAjouterReglement_Click(sender, e)
        End If
        If argument = "118" And bSupprimerReglement.Enabled = True Then
            bSupprimerReglement_Click(sender, e)
        End If
        If argument = "117" And bModifierReglement.Enabled = True Then
            bModifierReglement_Click(sender, e)
        End If
    End Sub

    Public Sub Init()

        Dim StrSQL As String = ""
        Dim StrSQL1 As String = ""

        dsClient.Clear()

        'chargement des villes
        StrSQL1 = "SELECT DISTINCT CodeVille,NomVille FROM VILLE WHERE SupprimeVille=0 ORDER BY NomVille ASC"
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "VILLE")
        cmbVilleClient.DataSource = dsClient.Tables("VILLE")
        cmbVilleClient.ValueMember = "CodeVille"
        cmbVilleClient.DisplayMember = "NomVille"
        cmbVilleClient.ColumnHeaders = False
        cmbVilleClient.Splits(0).DisplayColumns("CodeVille").Visible = False
        cmbVilleClient.Splits(0).DisplayColumns("NomVille").Width = 10
        cmbVilleClient.ExtendRightColumn = True

        'chargement des situations
        StrSQL1 = "SELECT DISTINCT CodeSituationClient,LibelleSituationClient FROM SITUATION_CLIENT ORDER BY LibelleSituationClient ASC"
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "SITUATION_CLIENT")
        cmbSituationClient.DataSource = dsClient.Tables("SITUATION_CLIENT")
        cmbSituationClient.ValueMember = "CodeSituationClient"
        cmbSituationClient.DisplayMember = "LibelleSituationClient"
        cmbSituationClient.ColumnHeaders = False
        cmbSituationClient.Splits(0).DisplayColumns("CodeSituationClient").Visible = False
        cmbSituationClient.Splits(0).DisplayColumns("LibelleSituationClient").Width = 10
        cmbSituationClient.ExtendRightColumn = True

        'chargement des Mutuelle
        StrSQL1 = "SELECT DISTINCT CodeMutuelle,NomMutuelle FROM MUTUELLE WHERE NomMutuelle<>'COMPTOIR' ORDER BY NomMutuelle ASC"
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "MUTUELLE")
        cmbMutuelle.DataSource = dsClient.Tables("MUTUELLE")
        cmbMutuelle.ValueMember = "CodeMutuelle"
        cmbMutuelle.DisplayMember = "NomMutuelle"
        cmbMutuelle.ColumnHeaders = False
        cmbMutuelle.Splits(0).DisplayColumns("CodeMutuelle").Visible = False
        cmbMutuelle.Splits(0).DisplayColumns("NomMutuelle").Width = 10
        cmbMutuelle.ExtendRightColumn = True

        'chargement des Medecins
        StrSQL1 = "SELECT DISTINCT CodeMedecin,NomMedecin FROM MEDECIN WHERE  Supprimer = 'False' ORDER BY NomMedecin ASC"
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "MEDECIN")
        cmbMedecin.DataSource = dsClient.Tables("MEDECIN")
        cmbMedecin.ValueMember = "CodeMedecin"
        cmbMedecin.DisplayMember = "NomMedecin"
        cmbMedecin.ColumnHeaders = False
        cmbMedecin.Splits(0).DisplayColumns("CodeMedecin").Visible = False
        cmbMedecin.Splits(0).DisplayColumns("NomMedecin").Width = 10
        cmbMedecin.ExtendRightColumn = True

        'chargement des Liens de parenté
        StrSQL1 = "SELECT DISTINCT CodeLienDeParente,LibelleLienDeParente FROM LIEN_PARENTE WHERE CodeLienDeParente <>'1' ORDER BY LibelleLienDeParente ASC"
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "LIEN_PARENTE")
        cmbLienDeParente.DataSource = dsClient.Tables("LIEN_PARENTE")
        cmbLienDeParente.ValueMember = "CodeLienDeParente"
        cmbLienDeParente.DisplayMember = "LibelleLienDeParente"
        cmbLienDeParente.ColumnHeaders = False
        cmbLienDeParente.Splits(0).DisplayColumns("CodeLienDeParente").Visible = False
        cmbLienDeParente.Splits(0).DisplayColumns("LibelleLienDeParente").Width = 10
        cmbLienDeParente.ExtendRightColumn = True




        ''''''''''''''''''''''''''''''''''

        'chargement des famille apci
        StrSQL1 = "select CodeDeFamille, Nom from CLIENT_FAMILLE where CodeClient = " + Quote(CodeClient) + _
                    "union " + _
                    "select 9999 as CodeDeFamille, 'ASSURE' as Nom " + _
                    "order by Nom asc "
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "FAMILLE_APCI")
        cmbFamille.DataSource = dsClient.Tables("FAMILLE_APCI")
        cmbFamille.ValueMember = "CodeDeFamille"
        cmbFamille.DisplayMember = "Nom"
        cmbFamille.ColumnHeaders = False
        cmbFamille.Splits(0).DisplayColumns("CodeDeFamille").Visible = False
        cmbFamille.Splits(0).DisplayColumns("Nom").Width = 10
        cmbFamille.ExtendRightColumn = True


        'chargement des Medecins apci
        StrSQL1 = "SELECT DISTINCT CodeMedecin,NomMedecin FROM MEDECIN WHERE  Supprimer = 'False'  ORDER BY NomMedecin ASC"
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "MEDECIN_APCI1")
        cmbMedecinApci1.DataSource = dsClient.Tables("MEDECIN_APCI1")
        cmbMedecinApci1.ValueMember = "CodeMedecin"
        cmbMedecinApci1.DisplayMember = "NomMedecin"
        cmbMedecinApci1.ColumnHeaders = False
        cmbMedecinApci1.Splits(0).DisplayColumns("CodeMedecin").Visible = False
        cmbMedecinApci1.Splits(0).DisplayColumns("NomMedecin").Width = 10
        cmbMedecinApci1.ExtendRightColumn = True

        StrSQL1 = "SELECT DISTINCT CodeMedecin,NomMedecin FROM MEDECIN WHERE  Supprimer = 'False'   ORDER BY NomMedecin ASC"
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "MEDECIN_APCI2")
        cmbMedecinApci2.DataSource = dsClient.Tables("MEDECIN_APCI2")
        cmbMedecinApci2.ValueMember = "CodeMedecin"
        cmbMedecinApci2.DisplayMember = "NomMedecin"
        cmbMedecinApci2.ColumnHeaders = False
        cmbMedecinApci2.Splits(0).DisplayColumns("CodeMedecin").Visible = False
        cmbMedecinApci2.Splits(0).DisplayColumns("NomMedecin").Width = 10
        cmbMedecinApci2.ExtendRightColumn = True

        StrSQL1 = "SELECT DISTINCT CodeMedecin,NomMedecin FROM MEDECIN WHERE  Supprimer = 'False' ORDER BY NomMedecin ASC"
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "MEDECIN_APCI3")
        cmbMedecinApci3.DataSource = dsClient.Tables("MEDECIN_APCI3")
        cmbMedecinApci3.ValueMember = "CodeMedecin"
        cmbMedecinApci3.DisplayMember = "NomMedecin"
        cmbMedecinApci3.ColumnHeaders = False
        cmbMedecinApci3.Splits(0).DisplayColumns("CodeMedecin").Visible = False
        cmbMedecinApci3.Splits(0).DisplayColumns("NomMedecin").Width = 10
        cmbMedecinApci3.ExtendRightColumn = True


        'chargement des apci
        StrSQL1 = "SELECT CodeAPCI, NomAPCI FROM APCI ORDER BY CodeAPCI ASC"
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "APCI1")
        cmbApci1.DataSource = dsClient.Tables("APCI1")
        cmbApci1.ValueMember = "CodeAPCI"
        cmbApci1.DisplayMember = "NomAPCI"
        cmbApci1.ColumnHeaders = False
        cmbApci1.Splits(0).DisplayColumns("CodeAPCI").Visible = False
        cmbApci1.Splits(0).DisplayColumns("NomAPCI").Width = 10
        cmbApci1.ExtendRightColumn = True

        StrSQL1 = "SELECT CodeAPCI, NomAPCI FROM APCI ORDER BY CodeAPCI ASC"
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "APCI2")
        cmbApci2.DataSource = dsClient.Tables("APCI2")
        cmbApci2.ValueMember = "CodeAPCI"
        cmbApci2.DisplayMember = "NomAPCI"
        cmbApci2.ColumnHeaders = False
        cmbApci2.Splits(0).DisplayColumns("CodeAPCI").Visible = False
        cmbApci2.Splits(0).DisplayColumns("NomAPCI").Width = 10
        cmbApci2.ExtendRightColumn = True

        StrSQL1 = "SELECT CodeAPCI, NomAPCI FROM APCI ORDER BY CodeAPCI ASC"
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL1
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "APCI3")
        cmbApci3.DataSource = dsClient.Tables("APCI3")
        cmbApci3.ValueMember = "CodeAPCI"
        cmbApci3.DisplayMember = "NomAPCI"
        cmbApci3.ColumnHeaders = False
        cmbApci3.Splits(0).DisplayColumns("CodeAPCI").Visible = False
        cmbApci3.Splits(0).DisplayColumns("NomAPCI").Width = 10
        cmbApci3.ExtendRightColumn = True

        ''''''''''''''''''''''''''''''''''

        If ajoutmodif = "A" Then
            StrSQL = " SELECT TOP 0 * FROM CLIENT"
        Else
            StrSQL = " SELECT * FROM CLIENT WHERE CodeClient = " + Quote(CodeClient)
        End If



        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = StrSQL
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "CLIENT")
        cbClient = New SqlCommandBuilder(daClient)

        If ajoutmodif = "A" And NomClient <> "" Then
            tNomClient.Value = NomClient
            cmbMutuelle.Text = NomMutuelle
        End If

        If ajoutmodif = "M" Then

            If dsClient.Tables("CLIENT").Rows.Count > 0 Then
                AfficherFacture()
                AfficherFactureDetail()
                AfficherFactures()

                LtitreClient.Text = "Fiche CLIENT : " + dsClient.Tables("CLIENT").Rows(0)("Nom")

                tCodeClient.Value = dsClient.Tables("CLIENT").Rows(0)("CodeClient")
                tNomClient.Value = dsClient.Tables("CLIENT").Rows(0)("Nom")

                tDateNaissance.Value = dsClient.Tables("CLIENT").Rows(0)("DateNaissance")

                tAdresseClient.Value = dsClient.Tables("CLIENT").Rows(0)("Adresse")
                cmbVilleClient.SelectedValue = dsClient.Tables("CLIENT").Rows(0)("Codeville")
                tTelephoneClient.Value = dsClient.Tables("CLIENT").Rows(0)("Telephone")
                tFaxClient.Value = dsClient.Tables("CLIENT").Rows(0)("Fax")
                tCodePostalClient.Value = dsClient.Tables("CLIENT").Rows(0)("CodePostal")

                Try
                    Dim IdCnam() As String = dsClient.Tables("CLIENT").Rows(0)("IdentifiantCnam").Trim().Split(" ")
                    If IdCnam.Length = 1 Then
                        IdCnam = dsClient.Tables("CLIENT").Rows(0)("IdentifiantCnam").Trim().Split("-")
                        If IdCnam.Length = 1 Then
                            IdCnam = dsClient.Tables("CLIENT").Rows(0)("IdentifiantCnam").Trim().Split("/")
                        End If
                    End If
                    Try
                        tIdCNAM.Value = IdCnam(0).ToString
                        tCleIdCNAM.Value = IdCnam(1).ToString
                    Catch
                    End Try
                Catch
                End Try

                btnCNSS.Checked = dsClient.Tables("CLIENT").Rows(0)("CNSS")
                btnCNRPS.Checked = dsClient.Tables("CLIENT").Rows(0)("CNRPS")
                btnBilateral.Checked = dsClient.Tables("CLIENT").Rows(0)("AUTRE")
                cmbMedecin.SelectedValue = dsClient.Tables("CLIENT").Rows(0)("CodeMedecin")
                dpDateValidite.Value = dsClient.Tables("CLIENT").Rows(0)("DateValidite")

                cmbSituationClient.SelectedValue = dsClient.Tables("CLIENT").Rows(0)("CodeSituation")

                chbPaimentComptantClient.Checked = dsClient.Tables("CLIENT").Rows(0)("PaiementComptant")
                tCrediMaxClient.Value = dsClient.Tables("CLIENT").Rows(0)("CrediMax")

                tSoldeInitial.Value = dsClient.Tables("CLIENT").Rows(0)("SoldeInitial")
                tDateInitial.Value = dsClient.Tables("CLIENT").Rows(0)("DateInitial")

                tRemarqueClient.Value = dsClient.Tables("CLIENT").Rows(0)("Remarque")
                tMatriculeFiscale.Value = dsClient.Tables("CLIENT").Rows(0)("MatriculeFiscale")

                cmbMutuelle.SelectedValue = dsClient.Tables("CLIENT").Rows(0)("CodeMutuelle")
                tMatricule.Value = dsClient.Tables("CLIENT").Rows(0)("MatriculeMutuelle")
                tRemPCharge.Value = dsClient.Tables("CLIENT").Rows(0)("RemisePriseEnCharge")
                tNumeroCIN.Value = dsClient.Tables("CLIENT").Rows(0)("NumeroCIN")

                'affichage image du carnet CNAM
                If IsDBNull(dsClient.Tables("CLIENT").Rows(0)("CarnetCNAM")) = True Then
                    Pic_Carnet_CNAM.Image = Nothing
                Else
                    ImageCarnet = ByteArrayToImage(dsClient.Tables("CLIENT").Rows(0)("CarnetCNAM"))
                    Pic_Carnet_CNAM.Image = ImageCarnet
                End If

                dtpDateRappel.Value = dsClient.Tables("CLIENT").Rows(0)("DateRappel")
                tInformation.Value = dsClient.Tables("CLIENT").Rows(0)("InformationRappel").ToString()
                lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsClient.Tables("CLIENT").Rows(0)("CodeOperateur"))

                AfficherSituationClient(dsClient.Tables("CLIENT").Rows(0)("SoldeInitial"))

                '---------------------- verouillage du champs Code client pour interdire la modification ------------
                tCodeClient.Enabled = False

                If ajoutmodif = "M" And CodeClient = "CLIPASS" Then
                    tCodeClient.Enabled = False
                    tNomClient.Enabled = False
                    tAdresseClient.Enabled = False
                    cmbVilleClient.Enabled = False
                    tTelephoneClient.Enabled = False
                    tFaxClient.Enabled = False
                    tCodePostalClient.Enabled = False
                    tIdCNAM.Enabled = False
                    cmbMedecin.Enabled = False
                    dpDateValidite.Enabled = False
                    cmbSituationClient.Enabled = False
                    cmbMutuelle.Enabled = False
                    tMatricule.Enabled = False
                    tRemPCharge.Enabled = False
                    tDateDernierAchatClient.Enabled = False
                    tCrediMaxClient.Enabled = False
                    tSoldeClient.Enabled = False
                    tEnCours.Enabled = False
                    tResteAPayer.Enabled = False
                    tSoldeInitial.Enabled = False
                    tDateInitial.Enabled = False
                    tRemarqueClient.Enabled = False
                    tMatriculeFiscale.Enabled = False
                    Pic_Carnet_CNAM.Enabled = False
                    chbPaimentComptantClient.Enabled = False
                    tDateNaissance.Enabled = False
                    tNumeroCIN.Enabled = False
                End If
            Else
                Exit Sub
            End If
        End If


        tCrediMaxClient.Enabled = ControleDAccesClient(1, "CREDIT_MAX_CLIENT")
        chbPaimentComptantClient.Enabled = ControleDAccesClient(1, "MODIFICATION_PAIEMENT_COMPTANT")


        '---------------------- verouillage des champs indisponibles ------------
        If ajoutmodif = "A" Then
            'tabFamille.Enabled = False
            cmbSituationClient.Text = "ACTIVITE"

            '' ''If ModeADMIN <> "ADMIN" Then
            '' ''    chbPaimentComptantClient.Checked = True
            '' ''    chbPaimentComptantClient.Enabled = False
            '' ''Else
            '' ''    chbPaimentComptantClient.Enabled = True
            '' ''End If

        End If
        tDateDernierAchatClient.Enabled = False
        tSoldeClient.Enabled = False
        tEnCours.Enabled = False
        tResteAPayer.Enabled = False
        tSoldeInitial.Enabled = False

        dtpDatePrise.Value = Date.Now

        tDateNaissance.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        tDateNaissance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpDateNaissance.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDateNaissance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpDateValidite.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDateValidite.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        tDateInitial.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        tDateInitial.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dpDateValidite.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dpDateValidite.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        Tab.TabPages(0).Show()

        'If FocusCodeCNAM = "FocusCodeCNAM" Then
        '    tIdCNAM.Focus()
        'Else
        tCodeClient.Focus()
        'End If

        AfficherMedecinAPCI()

        AfficherInformationPatient()

        'cmdClient.Connection = ConnectionServeur
        'cmdClient.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'CREDIT_MAX_CLIENT' AND CodeUtilisateur='" & CodeUtilisateur & "'"
        'Try
        '    If cmdClient.ExecuteScalar() >= 1 Or ModeADMIN = "ADMIN" Then
        '        tCrediMaxClient.Enabled = True
        '    Else
        '        tCrediMaxClient.Enabled = False
        '    End If
        'Catch ex As Exception
        '    tCrediMaxClient.Enabled = False
        'End Try

    End Sub

    Private Sub AfficherFacture()
        Dim I As Integer
        dsFacture.Clear()
        cmdFacture.CommandText = " SELECT " + _
                                 " NumeroVente, " + _
                                 " Date,  " + _
                                 " NATURE_REGLEMENT.LibelleNatureReglement AS Paiement, " + _
                                 " TotalHT, " + _
                                 " TotalRemise, " + _
                                 " TVA, " + _
                                 " TotalTTC " + _
                                 " FROM VENTE " + _
                                 " LEFT JOIN NATURE_REGLEMENT ON NATURE_REGLEMENT.CodeNatureReglement = VENTE.CodeNatureReglement " + _
                                 " WHERE CodeClient = " + Quote(CodeClient) + _
                                 " ORDER BY Date DESC"

        cmdFacture.Connection = ConnectionServeur
        daFacture = New SqlDataAdapter(cmdFacture)
        daFacture.Fill(dsFacture, "FACTURE")

        With gVente
            .Columns.Clear()
            .DataSource = dsFacture
            .DataMember = "FACTURE"
            .Rebind(False)
            .Columns("NumeroVente").Caption = "Numéro"
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            '.Splits(0).DisplayColumns("CodeForme").Width = 120
            '.Splits(0).DisplayColumns("CodeForme").Style.HorizontalAlignment = AlignHorzEnum.Center
            '.Splits(0).DisplayColumns("LibelleForme").Width = 80
            '.Splits(0).DisplayColumns("LibelleForme").Style.HorizontalAlignment = AlignHorzEnum.Near

            '.Splits(0).DisplayColumns("CodeForme").Locked = True
            '.Splits(0).DisplayColumns("LibelleForme").Locked = True

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gVente)
        End With

    End Sub

    Private Sub AfficherFactures()
        Dim I As Integer
        Try
            dsFacture.Tables("FACTURATION_CLIENT").Clear()
        Catch
        End Try
        cmdFacture.CommandText = "  SELECT " + _
                                 "      FACTURATION_CLIENT.Id AS Numero, " + _
                                 "      FACTURATION_CLIENT.Date,  " + _
                                 "      FACTURATION_CLIENT.DateDebut, " + _
                                 "      FACTURATION_CLIENT.DateFin, " + _
                                 "      FACTURATION_CLIENT.TotalTTC - ISNULL(FACTURATION_CLIENT.TotalCNAM, 0) - ISNULL(FACTURATION_CLIENT.TotalMutuelle, 0) AS TotalTTC, " + _
                                 "      ISNULL(FACTURATION_CLIENT.TotalTVA, 0) AS TotalTVA, " + _
                                 "      ISNULL(FACTURATION_CLIENT.TotalRemise, 0) AS TotalRemise, " + _
                                 "      FACTURATION_CLIENT.TotalTTC - ISNULL(FACTURATION_CLIENT.TotalRemise, 0) - ISNULL(FACTURATION_CLIENT.TotalCNAM, 0) - ISNULL(FACTURATION_CLIENT.TotalMutuelle, 0) AS TotalNet, " + _
                                 "      ISNULL(FACTURATION_CLIENT.TotalCNAM, 0) AS TotalCNAM, " + _
                                 "      ISNULL(FACTURATION_CLIENT.TotalMutuelle, 0) AS TotalMutuelle " + _
                                 "  FROM FACTURATION_CLIENT " + _
                                 "  WHERE FACTURATION_CLIENT.CodeClient = " + Quote(CodeClient) + _
                                 "  ORDER BY ID DESC"

        cmdFacture.Connection = ConnectionServeur
        daFacture = New SqlDataAdapter(cmdFacture)
        daFacture.Fill(dsFacture, "FACTURATION_CLIENT")

        With gFacture
            .Columns.Clear()
            .DataSource = dsFacture
            .DataMember = "FACTURATION_CLIENT"
            .Rebind(False)
            .Columns("Numero").Caption = "Numéro"
            .Columns("TotalTTC").Caption = "Total Facture"
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next


            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gFacture)
        End With

    End Sub

    Private Sub AfficherFactureDetail()

        If gVente.RowCount <> 0 Then
            Dim I As Integer
            dsFactureDetail.Clear()


            cmdFactureDetail.CommandText = " SELECT " + _
                                           " CodeABarre AS Code " + _
                                           " ,Designation AS Désignation  " + _
                                           " ,FORME_ARTICLE.LibelleForme AS Forme " + _
                                           " ,SUM(Qte) AS Qté" + _
                                           " ,PrixHT " + _
                                           " ,SUM(TotalTTC) AS TotalTTC " + _
                                           " FROM VENTE_DETAILS " + _
                                           " LEFT JOIN FORME_ARTICLE ON VENTE_DETAILS.CodeForme = FORME_ARTICLE.CodeForme  " + _
                                           " WHERE NumeroVente = " + Quote(gVente(gVente.Row, "NumeroVente")) + _
                                           " GROUP BY CodeABarre, Designation, FORME_ARTICLE.LibelleForme, PrixHT" + _
                                           " ORDER BY Designation"

            cmdFactureDetail.Connection = ConnectionServeur
            daFactureDetail = New SqlDataAdapter(cmdFactureDetail)
            daFactureDetail.Fill(dsFactureDetail, "FACTURE_DETAIL")

            With gDetailVente
                .Columns.Clear()
                .DataSource = dsFactureDetail
                .DataMember = "FACTURE_DETAIL"
                .Rebind(False)
                '.Columns("NumeroVente").Caption = "Numéro"
                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                .Splits(0).DisplayColumns("Code").Width = 120
                .Splits(0).DisplayColumns("Forme").Width = 80
                .Splits(0).DisplayColumns("Désignation").Width = 320
                .Splits(0).DisplayColumns("Forme").Style.HorizontalAlignment = AlignHorzEnum.Center
                .Splits(0).DisplayColumns("Qté").Style.HorizontalAlignment = AlignHorzEnum.Center
                '.Splits(0).DisplayColumns("LibelleForme").Width = 80
                '.Splits(0).DisplayColumns("LibelleForme").Style.HorizontalAlignment = AlignHorzEnum.Near

                '.Splits(0).DisplayColumns("CodeForme").Locked = True
                '.Splits(0).DisplayColumns("LibelleForme").Locked = True

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                'Style du Caractere et du grid
                ParametreGrid(gDetailVente)
            End With
        End If
    End Sub

    Public Sub AfficherSituationClient(ByVal SoldeInitial As Decimal)

        Dim StrSQLdernierAchat As String = ""
        Dim Dernier_Date_Achat As String = ""
        Dim StrSQLSolde As String = ""
        Dim Somme_Facture As Double = 0.0
        Dim Somme_Reglement As Double = 0.0
        Dim difference As Double = 0.0
        Dim EnCours As Double = 0.0

        ' récupération de la dernière date d'achat pour le client concerné 
        StrSQLdernierAchat = "SELECT MAX(Date) FROM VENTE WHERE CodeClient =" + Quote(CodeClient)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQLdernierAchat

        Try
            Dernier_Date_Achat = CmdCalcul.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        'calcul du solde client en retranchant la somme des montants des règlements de la somme des montants des ventes 
        StrSQLSolde = "SELECT SUM(TotalTTC - MontantCnam -MontantMutuelle) FROM VENTE LEFT OUTER JOIN CLIENT ON VENTE.CodeClient=CLIENT.CodeClient WHERE VENTE.Date>Client.DateInitial AND VENTE.CodeClient =" + Quote(CodeClient)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQLSolde

        Try
            Somme_Facture = CmdCalcul.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        StrSQLSolde = "SELECT SUM(MontantRegle) FROM REGLEMENT_CLIENT LEFT OUTER JOIN CLIENT ON REGLEMENT_CLIENT.CodeClient=CLIENT.CodeClient WHERE REGLEMENT_CLIENT.Date>Client.DateInitial AND Encaisse=1 AND  REGLEMENT_CLIENT.CodeClient =" + Quote(CodeClient)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQLSolde

        Try
            Somme_Reglement = CmdCalcul.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        If (Dernier_Date_Achat) <> "" Then
            tDateDernierAchatClient.Value = Dernier_Date_Achat
        End If

        difference = Somme_Facture - Somme_Reglement
        tSoldeClient.Value = Math.Round(difference + SoldeInitial, 3) '.ToString("### ### ##0.000")

        'calcul du solde client en cours (somme des cheque qui ont ne sont pas encaissé) 
        StrSQLSolde = "SELECT SUM(MontantRegle) FROM REGLEMENT_CLIENT LEFT OUTER JOIN CLIENT ON REGLEMENT_CLIENT.CodeClient=CLIENT.CodeClient " + _
                      " WHERE REGLEMENT_CLIENT.CodeClient =" + Quote(CodeClient) + _
                      " AND (CodeNatureReglement= 2 OR CodeNatureReglement= 5) AND Encaisse=0 AND REGLEMENT_CLIENT.Date>Client.DateInitial"
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQLSolde

        Try
            EnCours = CmdCalcul.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        tEnCours.Value = EnCours.ToString
        tResteAPayer.Value = Math.Round(difference + SoldeInitial - EnCours, 3)

        'Test si le client est passager
        If CodeClient = "CLIPASS" Then
            '          grbSoldeClient.Enabled = False
            tSoldeClient.Value = "0.000"
            'tEnCours.Value = "0.000"
            tResteAPayer.Value = "0.000"
            tSoldeInitial.Value = "0.000"

            tabCarnetCNAM.Enabled = False
            tabFamille.Enabled = False
            'tabReglementClient.Enabled = False

        End If



    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        If dsClient.HasChanges Then
            If MsgBox("Voulez vous vraiment annuler les modifications de ce client ?", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                Me.Hide()
            End If
        Else
            If NomClient = "" Then   ' création depuis la liste des clients
                'fMain.Tab.SelectedTab.Dispose()
                Me.Hide()
            Else   'création depuis vente
                Me.Hide()
                ConfirmerEnregistrementDepuisVente = False
            End If

        End If
    End Sub

    Public Function ImageToByteArray(ByVal img As Image) As Byte()
        Dim stream As New MemoryStream
        img.Save(stream, System.Drawing.Imaging.ImageFormat.Jpeg)
        Return stream.ToArray
    End Function

    Public Function ByteArrayToImage(ByVal ByteArray As Byte()) As Image
        Dim stream As New MemoryStream(ByteArray, 0, ByteArray.Length)
        Return Image.FromStream(stream, True)
    End Function

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        Dim DernierCode As String = ""
        Dim dr As DataRow
        Dim StrSQLNouveauClient As String = ""
        Dim cmdNouveauClient As New SqlCommand

        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""


        '-----------------------------

        If Pic_Carnet_CNAM.Image Is Nothing Then
            Pic_Carnet_CNAM.Image = Nothing
        Else

            If BtnChoisir = "bParcourir" Then

                Pic_Carnet_CNAM.Image = New Bitmap(OpenFileDialog1.FileName)
                Dim fiImage As New FileInfo(OpenFileDialog1.FileName)
                Dim fs As New FileStream(OpenFileDialog1.FileName, FileMode.Open, FileAccess.Read, FileShare.Read)

                m_barrImg = New Byte(Convert.ToInt32(fiImage.Length)) {}

                Dim iBytesRead As Integer = fs.Read(m_barrImg, 0, Convert.ToInt32(fiImage.Length))

                fs.Close()

            End If


        End If

        '-----------------------------

        If tCodeClient.Text = "" Then

            StrSQLNouveauClient = " SELECT max(CodeClient) FROM CLIENT WHERE CodeClient LIKE 'Customer%'"
            cmdNouveauClient.Connection = ConnectionServeur
            cmdNouveauClient.CommandText = StrSQLNouveauClient
            Try
                DernierCode = cmdNouveauClient.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            If DernierCode = "" Then
                tCodeClient.Value = "Customer1"
            Else
                DernierCode = (Convert.ToInt32(DernierCode.Substring(8, DernierCode.Length - 8)) + 1).ToString
                tCodeClient.Value = "Customer" + DernierCode
            End If
            lTest.Visible = False
        End If

        If tIdCNAM.Text <> "" Or tCleIdCNAM.Text <> "" Then
            If IsNumeric(tIdCNAM.Text) = False Or IsNumeric(tCleIdCNAM.Text) = False Or tIdCNAM.Text = "" Or tCleIdCNAM.Text = "" Then
                MsgBox("Identifiant CNAM erroné !", MsgBoxStyle.Critical, "Erreur")
                tIdCNAM.Focus()
                Exit Sub
            End If
        End If

        If btnCNSS.Checked = False And btnCNRPS.Checked = False And btnBilateral.Checked = False And tIdCNAM.Text <> "" Then
            MsgBox("Choisissez CNSS, CNRPS ou BILATERALE !", MsgBoxStyle.Critical, "Erreur")
            tIdCNAM.Focus()
            Exit Sub
        End If

        If tNomClient.Text = "" Then
            MsgBox("Veuillez saisir le nom du client !", MsgBoxStyle.Critical, "Erreur")
            tNomClient.Focus()
            Exit Sub
        End If

        If cmbMutuelle.Text <> "" And tMatricule.Text = "" Then
            MsgBox("Matricule mutuelle obligatoire !", MsgBoxStyle.Critical, "Erreur")
            tMatricule.Focus()
            Exit Sub
        End If

        If cmbMutuelle.Text <> "" And tMatricule.Text <> "" Then
            cmdNouveauClient.CommandText = "SELECT COUNT(*) FROM CLIENT WHERE CodeClient NOT IN (" + Quote(CodeClient) + ") AND Supprime=0 AND CodeMutuelle=" + Quote(cmbMutuelle.SelectedValue) + " AND MatriculeMutuelle = " + Quote(tMatricule.Text)
            cmdNouveauClient.Connection = ConnectionServeur
            If cmdNouveauClient.ExecuteScalar <> 0 Then
                MsgBox("Matricule mutuelle existe déja pour un autre client !", MsgBoxStyle.Critical, "Erreur")
                tMatricule.Focus()
                tMatricule.SelectAll()
                Exit Sub
            End If
        End If


        If tIdCNAM.Text <> "" Then
            Dim Test As String = ""
            Test = RecupererValeurExecuteScalaire("CodeClient", "CLIENT", "IdentifiantCnam", tIdCNAM.Text + "/" + tCleIdCNAM.Text)
            If Test <> "" And Test <> tCodeClient.Text Then
                MsgBox("Code CNAM existe déja !", MsgBoxStyle.Critical, "Erreur")
                tIdCNAM.Value = ""
                tIdCNAM.Focus()
                Exit Sub
            End If
        End If

        'tDateInitial.Value = System.DateTime.Today

        '------------------------------ demande du mot de passe
        Dim myMotDePasse As New fMotDePasse

        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If
        '------------------------------------------------------

        If CodeExiste = True Then
            MsgBox("Code client existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
            tCodeClient.Focus()
            Exit Sub
        End If

        If ajoutmodif = "A" Then
            With dsClient
                dr = .Tables("CLIENT").NewRow

                dr.Item("CodeClient") = tCodeClient.Text
                dr.Item("Nom") = tNomClient.Text

                dr.Item("Adresse") = tAdresseClient.Text

                If cmbVilleClient.Text <> "" Then
                    dr.Item("Codeville") = cmbVilleClient.SelectedValue
                Else
                    dr.Item("Codeville") = DBNull.Value
                End If

                dr.Item("Telephone") = tTelephoneClient.Text

                dr.Item("Fax") = tFaxClient.Text
                dr.Item("CodePostal") = tCodePostalClient.Text

                If tIdCNAM.Text + "/" + tCleIdCNAM.Text <> "/" Then
                    dr.Item("IdentifiantCnam") = tIdCNAM.Text + "/" + tCleIdCNAM.Text
                End If


                dr.Item("CNSS") = btnCNSS.Checked
                dr.Item("CNRPS") = btnCNRPS.Checked
                dr.Item("AUTRE") = btnBilateral.Checked



                '''''''''''''''''
                If cmbMedecin.Text <> "" Then
                    dr.Item("CodeMedecin") = cmbMedecin.SelectedValue
                Else
                    dr.Item("CodeMedecin") = DBNull.Value
                End If
                '''''''''''


                If dpDateValidite.Text <> "" Then
                    dr.Item("DateValidite") = Convert.ToDateTime(dpDateValidite.Text)
                End If

                If cmbSituationClient.Text <> "" Then
                    dr.Item("CodeSituation") = cmbSituationClient.SelectedValue
                End If

                dr.Item("PaiementComptant") = chbPaimentComptantClient.Checked
                dr.Item("CrediMax") = tCrediMaxClient.Text.Replace(",", ".")


                dr.Item("SoldeInitial") = tSoldeInitial.Text.Replace(",", ".")
                If tDateInitial.Text <> "" Then
                    dr.Item("DateInitial") = tDateInitial.Text
                Else
                    dr.Item("DateInitial") = System.DateTime.Now.AddDays(-1)
                End If

                If tDateNaissance.Text <> "" Then
                    dr.Item("DateNaissance") = tDateNaissance.Text
                End If


                dr.Item("Remarque") = tRemarqueClient.Text
                dr.Item("MatriculeFiscale") = tMatriculeFiscale.Text

                If cmbMutuelle.Text <> "" Then
                    dr.Item("CodeMutuelle") = cmbMutuelle.SelectedValue
                Else
                    dr.Item("CodeMutuelle") = 0 'DBNull.Value
                End If

                If tMatricule.Text <> "" Then
                    dr.Item("MatriculeMutuelle") = tMatricule.Text
                End If

                If tRemPCharge.Text <> "" Then
                    dr.Item("RemisePriseEnCharge") = tRemPCharge.Text
                Else
                    dr.Item("RemisePriseEnCharge") = DBNull.Value
                End If
                dr.Item("CodeOperateur") = CodeOperateur
                dr.Item("NumeroCIN") = tNumeroCIN.Value
                dr.Item("InformationRappel") = tInformation.Text
                If dtpDateRappel.Text <> "" Then
                    dr.Item("DateRappel") = dtpDateRappel.Text
                End If
                '-------------------------Ajouter une image de la carnet CNAM
                If Pic_Carnet_CNAM.Image Is Nothing Then
                    dr.Item("CarnetCNAM") = Nothing
                Else
                    dr.Item("CarnetCNAM") = ImageToByteArray(Pic_Carnet_CNAM.Image)
                End If
                dr.Item("CodePersonnel") = CodeOperateur
                dr.Item("Supprime") = 0

                .Tables("CLIENT").Rows.Add(dr)
                dr.Item("Assurance") = ""

                ''''''''''''.Tables("CLIENT").AcceptChanges()

            End With

        ElseIf ajoutmodif = "M" Then
            With dsClient.Tables("CLIENT")
                dr = .Rows(0)
                dr.Item("CodeClient") = tCodeClient.Text
                dr.Item("Nom") = tNomClient.Text

                dr.Item("Adresse") = tAdresseClient.Text

                If cmbVilleClient.Text <> "" Then
                    dr.Item("Codeville") = cmbVilleClient.SelectedValue
                End If

                dr.Item("Telephone") = tTelephoneClient.Text

                dr.Item("Fax") = tFaxClient.Text
                dr.Item("CodePostal") = tCodePostalClient.Text

                If tIdCNAM.Text + "/" + tCleIdCNAM.Text <> "/" Then
                    dr.Item("IdentifiantCnam") = tIdCNAM.Text + "/" + tCleIdCNAM.Text
                End If

                dr.Item("CNSS") = btnCNSS.Checked
                dr.Item("CNRPS") = btnCNRPS.Checked
                dr.Item("AUTRE") = btnBilateral.Checked


                ''''''''''
                If cmbMedecin.Text <> "" Then
                    dr.Item("CodeMedecin") = cmbMedecin.SelectedValue.ToString
                Else
                    dr.Item("CodeMedecin") = 0
                End If
                '''''''



                If dpDateValidite.Text <> "" Then
                    dr.Item("DateValidite") = Convert.ToDateTime(dpDateValidite.Text)
                End If


                If cmbSituationClient.Text <> "" Then
                    dr.Item("CodeSituation") = cmbSituationClient.SelectedValue
                End If

                dr.Item("PaiementComptant") = chbPaimentComptantClient.Checked
                Try
                    dr.Item("CrediMax") = tCrediMaxClient.Text.Replace(",", ".")
                Catch ex As Exception
                    dr.Item("CrediMax") = 0
                End Try


                dr.Item("SoldeInitial") = tSoldeInitial.Text.Replace(",", ".")

                If tDateInitial.Text <> "" Then
                    dr.Item("DateInitial") = tDateInitial.Text
                Else
                    dr.Item("DateInitial") = System.DateTime.Now
                End If

                If tDateNaissance.Text <> "" Then
                    dr.Item("DateNaissance") = tDateNaissance.Text
                End If

                dr.Item("Remarque") = tRemarqueClient.Text

                dr.Item("MatriculeFiscale") = tMatriculeFiscale.Text

                If cmbMutuelle.Text <> "" Then
                    dr.Item("CodeMutuelle") = cmbMutuelle.SelectedValue
                Else
                    dr.Item("CodeMutuelle") = 0
                End If

                dr.Item("MatriculeMutuelle") = tMatricule.Text
                If tRemPCharge.Text <> "" Then
                    dr.Item("RemisePriseEnCharge") = tRemPCharge.Text
                End If
                dr.Item("NumeroCIN") = tNumeroCIN.Value
                dr.Item("CodeOperateur") = CodeOperateur

                '-------------------------Ajouter une image de la carnet CNAM
                If Pic_Carnet_CNAM.Image Is Nothing Then
                    dr.Item("CarnetCNAM") = Nothing
                Else
                    dr.Item("CarnetCNAM") = ImageToByteArray(Pic_Carnet_CNAM.Image)
                End If
                dr.Item("CodePersonnel") = CodeOperateur
                dr.Item("Assurance") = ""
                dr.Item("Supprime") = 0
                If dtpDateRappel.Text <> "" Then
                    dr.Item("DateRappel") = dtpDateRappel.Text
                End If
                dr.Item("InformationRappel") = tInformation.Text
                ''''''''''''.AcceptChanges()
            End With

        End If
        Try

            cmdClient.Connection = ConnectionServeur
            cmdClient.CommandText = " SELECT TOP 0 * FROM CLIENT"
            daClient = New SqlDataAdapter(cmdClient)
            daClient.Fill(dsClient, "CLIENT")
            cbClient = New SqlCommandBuilder(daClient)
            daClient.Update(dsClient, "CLIENT")

            Try

                Dim i As Integer
                Dim trouve As Boolean = False
                For i = 0 To dsLienFamille.Tables("CLIENT_FAMILLE").Rows().Count - 1
                    cmdLienFamille.Connection = ConnectionServeur
                    cmdLienFamille.CommandText = "UPDATE " & _
                                                "   CLIENT_FAMILLE " & _
                                                "SET " & _
                                                "    Nom = " & Quote(dsLienFamille.Tables("CLIENT_FAMILLE").Rows(i).Item("Nom").ToString()) & _
                                                "   ,DateNaissance = " & Quote(dsLienFamille.Tables("CLIENT_FAMILLE").Rows(i).Item("DateNaissance").ToString()) & _
                                                "   ,DateValidite = " & Quote(dsLienFamille.Tables("CLIENT_FAMILLE").Rows(i).Item("DateValidite").ToString()) & _
                                                "   ,Rang = " & Quote(dsLienFamille.Tables("CLIENT_FAMILLE").Rows(i).Item("Rang").ToString()) & _
                                                "WHERE " & _
                                                "   CodeClient = " & Quote(dsLienFamille.Tables("CLIENT_FAMILLE").Rows(i).Item("CodeClient").ToString()) & _
                                                "   AND CodeDeFamille = " & Quote(dsLienFamille.Tables("CLIENT_FAMILLE").Rows(i).Item("CodeDeFamille").ToString())
                    cmdLienFamille.ExecuteNonQuery()
                Next

            Catch
            End Try

            Try

                Dim i As Integer
                Dim trouve As Boolean = False
                dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").AcceptChanges()
                For i = 0 To dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows().Count - 1
                    cmdLienFamille.Connection = ConnectionServeur
                    If dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeDeFamille") = "9999" Then
                        cmdLienFamille.CommandText = "UPDATE " & _
                                                    "   CLIENT " & _
                                                    " SET " & _
                                                    "   CodeMedecinAPCI1 = " & Quote(RecupererValeurExecuteScalaire("CodeMedecin", "MEDECIN", "NomMedecin", dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("MedecinAPCI1").ToString())) & _
                                                    "   ,CodeMedecinAPCI2 = " & Quote(RecupererValeurExecuteScalaire("CodeMedecin", "MEDECIN", "NomMedecin", dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("MedecinAPCI2").ToString())) & _
                                                    "   ,CodeMedecinAPCI3 = " & Quote(RecupererValeurExecuteScalaire("CodeMedecin", "MEDECIN", "NomMedecin", dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("MedecinAPCI3").ToString())) & _
                                                    "   ,CodeAPCI1 = " & IIf(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeAPCI1").ToString() = "", " NULL ", Quote(RecupererValeurExecuteScalaire("CodeAPCI", "APCI", "NomAPCI", dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeAPCI1").ToString()))) & _
                                                    "   ,CodeAPCI2 = " & IIf(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeAPCI2").ToString() = "", " NULL ", Quote(RecupererValeurExecuteScalaire("CodeAPCI", "APCI", "NomAPCI", dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeAPCI2").ToString()))) & _
                                                    "   ,CodeAPCI3 = " & IIf(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeAPCI3").ToString() = "", " NULL ", Quote(RecupererValeurExecuteScalaire("CodeAPCI", "APCI", "NomAPCI", dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeAPCI3").ToString()))) & _
                                                    "   ,DateValiditeAPCI1 = " & IIf(Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("DateValiditeAPCI1").ToString()) = "''", " NULL", Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("DateValiditeAPCI1").ToString())) & _
                                                    "   ,DateValiditeAPCI2 = " & IIf(Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("DateValiditeAPCI2").ToString()) = "''", " NULL", Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("DateValiditeAPCI2").ToString())) & _
                                                    "   ,DateValiditeAPCI3 = " & IIf(Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("DateValiditeAPCI3").ToString()) = "''", " NULL", Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("DateValiditeAPCI3").ToString())) & _
                                                    " WHERE " & _
                                                    "   CodeClient = " & Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeClient"))
                    Else
                        cmdLienFamille.CommandText = "UPDATE " & _
                                                    "   CLIENT_FAMILLE " & _
                                                    " SET " & _
                                                    "   CodeMedecinAPCI1 = " & Quote(RecupererValeurExecuteScalaire("CodeMedecin", "MEDECIN", "NomMedecin", dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("MedecinAPCI1").ToString())) & _
                                                    "   ,CodeMedecinAPCI2 = " & Quote(RecupererValeurExecuteScalaire("CodeMedecin", "MEDECIN", "NomMedecin", dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("MedecinAPCI2").ToString())) & _
                                                    "   ,CodeMedecinAPCI3 = " & Quote(RecupererValeurExecuteScalaire("CodeMedecin", "MEDECIN", "NomMedecin", dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("MedecinAPCI3").ToString())) & _
                                                    "   ,CodeAPCI1 = " & IIf(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeAPCI1").ToString() = "", " NULL ", Quote(RecupererValeurExecuteScalaire("CodeAPCI", "APCI", "NomAPCI", dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeAPCI1").ToString()))) & _
                                                    "   ,CodeAPCI2 = " & IIf(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeAPCI2").ToString() = "", " NULL ", Quote(RecupererValeurExecuteScalaire("CodeAPCI", "APCI", "NomAPCI", dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeAPCI2").ToString()))) & _
                                                    "   ,CodeAPCI3 = " & IIf(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeAPCI3").ToString() = "", " NULL ", Quote(RecupererValeurExecuteScalaire("CodeAPCI", "APCI", "NomAPCI", dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeAPCI3").ToString()))) & _
                                                    "   ,DateValiditeAPCI1 = " & IIf(Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("DateValiditeAPCI1").ToString()) = "''", " NULL", Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("DateValiditeAPCI1").ToString())) & _
                                                    "   ,DateValiditeAPCI2 = " & IIf(Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("DateValiditeAPCI2").ToString()) = "''", " NULL", Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("DateValiditeAPCI2").ToString())) & _
                                                    "   ,DateValiditeAPCI3 = " & IIf(Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("DateValiditeAPCI3").ToString()) = "''", " NULL", Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("DateValiditeAPCI3").ToString())) & _
                                                    " WHERE " & _
                                                    "   CodeClient = " & Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeClient")) & _
                                                    "   AND CodeDeFamille = " & Quote(dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Rows(i).Item("CodeDeFamille"))
                    End If

                    cmdLienFamille.ExecuteNonQuery()
                Next

            Catch
            End Try


            ' ''''''''
            'If cmbMedecin.Text <> "" Then
            '    cmdClient.Connection = ConnectionServeur
            '    If CodeMedecinAjouter = "" Then
            '        cmdClient.CommandText = " UPDATE CLIENT SET CodeMedecin = " + Quote(cmbMedecin.SelectedValue) + " WHERE CodeClient = " + Quote(tCodeClient.Text)
            '    Else
            '        cmdClient.CommandText = " UPDATE CLIENT SET CodeMedecin = " + Quote(CodeMedecinAjouter) + " WHERE CodeClient = " + Quote(tCodeClient.Text)
            '    End If

            '    Try
            '        cmdClient.ExecuteNonQuery()
            '    Catch ex As Exception
            '    End Try
            'End If

            ' '''''''''

            If ajoutmodif = "A" Then
                ModuleSurveillance(6, "L'utilisateur " & NomUtilisateur & " a ajouté le client " & tNomClient.Text)
            Else
                ModuleSurveillance(7, "L'utilisateur " & NomUtilisateur & " a modifié le client " & tNomClient.Text)
            End If

            '""""""""""""""""""""""""""""""""""""""""""""" enregistrement du log
            If ajoutmodif = "A" Then
                InsertionDansLog("AJOUT_CLIENT", "L ajout du client " + tNomClient.Text, CodeOperateur, System.DateTime.Now, "CLIENT", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
            ElseIf ajoutmodif = "M" Then
                InsertionDansLog("MODIFICATION_CLIENT", "La modification du client " + tNomClient.Text, CodeOperateur, System.DateTime.Now, "CLIENT", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
            End If

            If NomClient = "" Then ' création depuis la liste des clients
                'fMain.Tab.SelectedTab.Dispose()
                Me.Hide()
            Else  ' création depuis vente
                Me.Hide()
                ConfirmerEnregistrementDepuisVente = True
            End If



        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsClient.Reset()
            Me.Init()
        End Try
    End Sub

    Private Sub tCodeClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeClient.KeyUp
        If e.KeyCode = Keys.Enter And tCodeClient.Text <> "" Then
            tNomClient.Focus()
            Exit Sub
        End If
    End Sub

    Private Sub tCodeClient_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeClient.LostFocus
        lTest.Visible = False
    End Sub

    Private Sub tCodeClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeClient.TextChanged
        Dim StrSQLtest As String = ""
        dsRecupereNum.Clear()
        If ajoutmodif = "A" Then
            StrSQLtest = " SELECT * FROM CLIENT WHERE CodeClient=" + Quote(tCodeClient.Text)
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQLtest
            daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
            daRecupereNumt.Fill(dsRecupereNum, "CLIENT")

            If dsRecupereNum.Tables("CLIENT").Rows.Count <> 0 Or tCodeClient.Text = "" Then
                lTest.Text = "Code non valide déja existe"
                lTest.ForeColor = Color.OrangeRed
                lTest.Visible = True
                CodeExiste = True
            Else
                lTest.Text = "Code valide"
                lTest.ForeColor = Color.LimeGreen
                lTest.Visible = True
                CodeExiste = False
            End If
        End If
        If tCodeClient.Text = "" Then
            lTest.Visible = False
        End If
    End Sub

    Private Sub cmbVilleClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbVilleClient.KeyUp
        'Recherche_Automatique_fiche(e, cmbVilleClient, cmbVilleClient.Columns("NomVille"))
        If e.KeyCode = Keys.Enter Then
            cmbVilleClient.Text = cmbVilleClient.WillChangeToText
            tTelephoneClient.Focus()
            Exit Sub
        Else
            cmbVilleClient.OpenCombo()
            Exit Sub
        End If

        'If e.KeyCode = Keys.F3 Then
        '    bConfirmer_Click(sender, e)
        '    Exit Sub
        'End If
        'If e.KeyCode = Keys.F10 Then
        '    bAnnuler_Click(sender, e)
        '    Exit Sub
        'End If

    End Sub

    Private Sub cmbVilleClient_Layout(ByVal sender As Object, ByVal e As System.Windows.Forms.LayoutEventArgs)

    End Sub

    Private Sub cmbVilleClient_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbVilleClient.LostFocus
        Dim i As Integer
        Dim trouve As Boolean = False
        For i = 0 To cmbVilleClient.ListCount - 1
            If cmbVilleClient.Columns("NomVille").CellValue(i) Like cmbVilleClient.Text Then
                trouve = True
            End If
        Next
        If trouve = False Then
            cmbVilleClient.Text = ""
        End If
    End Sub

    Private Sub cmbVilleClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub cmbSituationClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbSituationClient.KeyUp
        'Recherche_Automatique_fiche(e, cmbSituationClient, cmbSituationClient.Columns("LibelleSituationClient"))
        If e.KeyCode = Keys.Enter Then
            cmbSituationClient.Text = cmbSituationClient.WillChangeToText
            cmbMutuelle.Focus()
        Else
            cmbSituationClient.OpenCombo()
        End If
    End Sub

    Private Sub cmbSituationClient_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbSituationClient.LostFocus
        Dim i As Integer
        Dim trouve As Boolean = False
        For i = 0 To cmbSituationClient.ListCount - 1
            If cmbSituationClient.Columns("LibelleSituationClient").CellValue(i) Like cmbSituationClient.Text Then
                trouve = True
            End If
        Next
        If trouve = False Then
            cmbSituationClient.Text = ""
        End If
    End Sub

    Private Sub cmbSituationClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub C1DockingTabPage2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub C1DockingTabPage2_Enter(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1DockingTabPage2.Enter
        AfficherReglement(0)
    End Sub
    Public Sub AfficherReglement(ByVal NumVente As String)
        Dim StrSQL As String = ""
        Dim NumeroPremierReglement As Integer = 0
        Dim i As Integer = 0

        If (dsClient.Tables.IndexOf("REGLEMENT_CLIENT_AFFICHAGE") > -1) Then
            dsClient.Tables("REGLEMENT_CLIENT_AFFICHAGE").Clear()
        End If

        'chargement des règlements

        StrSQL = "SELECT NumeroReglementClient," + _
                 "LibelleReglement AS libelle ," + _
                 "NATURE_REGLEMENT.LibelleNatureReglement AS LibelleNatureReglement," + _
                 "REGLEMENT_CLIENT.CodeNatureReglement, " + _
                 "Date," + _
                 "DateEcheance," + _
                 "MontantRegle," + _
                 "NumeroCheque," + _
                 "LibellePoste," + _
                 "NomInscritSurLeCheque," + _
                 "CodeClient," + _
                 "BANQUE.NomBanque," + _
                 "REGLEMENT_CLIENT.CodeBanque," + _
                 "Encaisse " + _
                 "FROM " + _
                 "REGLEMENT_CLIENT " + _
                 "LEFT OUTER JOIN NATURE_REGLEMENT ON REGLEMENT_CLIENT.CodeNatureReglement=NATURE_REGLEMENT.CodeNatureReglement " + _
                 "LEFT OUTER JOIN BANQUE ON REGLEMENT_CLIENT.CodeBanque=BANQUE.CodeBanque " + _
                 "WHERE REGLEMENT_CLIENT.CodeClient='" + tCodeClient.Text + "'" + _
                 "ORDER BY Date DESC"


        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsClient, "REGLEMENT_CLIENT_AFFICHAGE")
        cbReglement = New SqlCommandBuilder(daReglement)

        With gReglements
            .Columns.Clear()
            Try
                .DataSource = dsClient
            Catch ex As Exception
            End Try
            .DataMember = "REGLEMENT_CLIENT_AFFICHAGE"
            .Rebind(False)
            .Columns("NumeroReglementClient").Caption = "Numero reg"
            .Columns("libelle").Caption = "libellé"
            .Columns("LibelleNatureReglement").Caption = "Nature"
            .Columns("Date").Caption = "Date"
            .Columns("DateEcheance").Caption = "Date Echéance"
            .Columns("MontantRegle").Caption = "Montant"
            .Columns("NumeroCheque").Caption = "Numero Cheque "
            .Columns("LibellePoste").Caption = "Numero Poste"
            .Columns("NomInscritSurLeCheque").Caption = "Nom inscrit"
            .Columns("CodeClient").Caption = "Code Client"
            .Columns("NomBanque").Caption = "BANQUE"
            .Columns("Encaisse").Caption = "Encaissé"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns(2).Style.HorizontalAlignment = AlignHorzEnum.Near
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("NumeroReglementClient").Width = 80 ' 50
            .Splits(0).DisplayColumns("libelle").Width = 170
            .Splits(0).DisplayColumns("LibelleNatureReglement").Width = 80
            .Splits(0).DisplayColumns("CodeNatureReglement").Visible = False
            .Splits(0).DisplayColumns("Date").Width = 100
            .Splits(0).DisplayColumns("DateEcheance").Width = 100
            .Splits(0).DisplayColumns("Montant").Width = 80
            .Splits(0).DisplayColumns("NumeroCheque").Width = 70
            .Splits(0).DisplayColumns("LibellePoste").Width = 0
            .Splits(0).DisplayColumns("LibellePoste").Visible = False
            .Splits(0).DisplayColumns("NomInscritSurLeCheque").Width = 50
            .Splits(0).DisplayColumns("CodeClient").Width = 50
            .Splits(0).DisplayColumns("NomBanque").Width = 60
            .Splits(0).DisplayColumns("CodeBanque").Visible = False
            .Splits(0).DisplayColumns("Encaisse").Width = 50

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            ParametreGrid(gReglements)
        End With

        StrSQL = "SELECT TOP(1) REGLEMENT_CLIENT.NumeroReglementClient FROM REGLEMENT_CLIENT LEFT OUTER JOIN REGLEMENT_CLIENT_VENTE" + _
                " ON REGLEMENT_CLIENT.NumeroReglementClient=REGLEMENT_CLIENT_VENTE.NumeroReglementClient WHERE NumeroVente='" + _
                NumVente.ToString + "' ORDER BY REGLEMENT_CLIENT.Date"

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL

        Try
            NumeroPremierReglement = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        If NumeroPremierReglement <> 0 Then
            For i = 0 To dsClient.Tables("REGLEMENT_CLIENT_AFFICHAGE").Rows.Count - 1
                If gReglements(i, "NumeroReglementClient") = NumeroPremierReglement Then
                    gReglements.MoveRelative(i)
                End If
            Next
        End If

        If NumeroVenteAAfficher = "" And BlanchirListeReglement = False Then
            Try
                NumeroReglementAModifier = gReglements(0, "NumeroReglementClient")
            Catch ex As Exception
                NumeroReglementAModifier = 0
            End Try

            If NumeroReglementAModifier.ToString = "" Then
                AfficherVentes(0)
            Else
                AfficherVentes(NumeroReglementAModifier)
            End If

        End If
    End Sub
    Public Sub AfficherVentes(ByVal NumReglement As Integer)

        Dim StrSQL As String = ""
        Dim i As Integer = 0
        Dim NumeroPremiereVente As String = ""
        Dim x As Integer = 0

        If (dsClient.Tables.IndexOf("VENTE_REGLEMENT_CLIENT_AFFICHAGE") > -1) Then
            dsClient.Tables("VENTE_REGLEMENT_CLIENT_AFFICHAGE").Clear()
        End If

        ' chargement des ventes 

        StrSQL = "SELECT NumeroVente," + _
                 "Date," + _
                 "TotalHT," + _
                 "TotalTTC," + _
                 "TVA," + _
                 "TotalRemise AS Remise, " + _
                 "MontantCnam," + _
                 "MontantMutuelle " + _
                 "FROM VENTE " + _
                 "WHERE CodeClient='" + tCodeClient.Text + _
                 "' ORDER BY Date"

        '"' AND NumeroVente IN(SELECT NumeroVente " + _
        '        "FROM REGLEMENT_CLIENT_VENTE )" + _

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsClient, "VENTE_REGLEMENT_CLIENT_AFFICHAGE")
        cbReglement = New SqlCommandBuilder(daReglement)

        With gVentes
            .Columns.Clear()
            Try
                .DataSource = dsClient
            Catch ex As Exception
            End Try
            .DataMember = "VENTE_REGLEMENT_CLIENT_AFFICHAGE"
            .Rebind(False)
            .Columns("NumeroVente").Caption = "Numero Vente"
            .Columns("Date").Caption = "Date"
            .Columns("TotalHT").Caption = "Total HT"
            .Columns("TotalTTC").Caption = "Total TTC"
            .Columns("TVA").Caption = "TVA"
            .Columns("Remise").Caption = "Remise"
            .Columns("MontantCnam").Caption = "Montant Cnam"
            .Columns("MontantMutuelle").Caption = "Montant Mutuelle"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("NumeroVente").Width = 110
            .Splits(0).DisplayColumns("Date").Width = 110
            .Splits(0).DisplayColumns("TotalHT").Width = 110
            .Splits(0).DisplayColumns("TotalTTC").Width = 110
            .Splits(0).DisplayColumns("TVA").Width = 150
            .Splits(0).DisplayColumns("Remise").Width = 110
            .Splits(0).DisplayColumns("MontantCnam").Width = 110
            .Splits(0).DisplayColumns("MontantMutuelle").Width = 100

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            ParametreGrid(gVentes)
        End With

        'pour atteindre le premier enregistrement dans les ventes deja reglé par ce réglement oki fhemet 
        StrSQL = "SELECT TOP(1) VENTE.NumeroVente FROM VENTE LEFT OUTER JOIN REGLEMENT_CLIENT_VENTE" + _
                 " ON VENTE.NumeroVente=REGLEMENT_CLIENT_VENTE.NumeroVente WHERE NumeroReglementClient=" + _
                 NumReglement.ToString + " ORDER BY VENTE.Date"

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL

        Try
            NumeroPremiereVente = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        If NumeroPremiereVente <> "" Then
            For i = 0 To dsClient.Tables("VENTE_REGLEMENT_CLIENT_AFFICHAGE").Rows.Count - 1
                If gVentes(i, "NumeroVente") = NumeroPremiereVente Then
                    gVentes.MoveRelative(i)
                End If
            Next
        Else
            gVentes.MoveFirst()
        End If

    End Sub

    Private Sub bAjouterReglement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterReglement.Click
        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(1, "AJOUT_REGLEMENT_CLIENT") = "False" Then
            Exit Sub
        End If

        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = "SELECT DemandeMotDePasse FROM [POINTS_CONTROLE] WHERE CodePointControle LIKE 'AJOUT_REGLEMENT_CLIENT' AND CodeModule=1"

        'Try
        '    DemandePotDePasse = cmd.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'If DemandePotDePasse = True Then
        '    '------------------------------ demande du mot de passe
        '    Dim myMotDePasse As New fMotDePasse
        '    myMotDePasse.ShowDialog()

        '    ConfirmerEnregistrer = fMotDePasse.Confirmer
        '    CodeOperateur = fMotDePasse.CodeOperateur

        '    myMotDePasse.Dispose()
        '    myMotDePasse.Close()

        '    If ConfirmerEnregistrer = False Then
        '        Exit Sub
        '    End If

        '    cmd.Connection = ConnectionServeur
        '    cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE'AJOUT_REGLEMENT_CLIENT' AND CodeUtilisateur='" + CodeOperateur + "'"

        '    Try
        '        If cmd.ExecuteScalar() < 1 Then
        '            MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
        '            Exit Sub
        '        End If
        '    Catch ex As Exception
        '        Console.WriteLine(ex.Message)
        '    End Try

        'End If

        Dim MyReglement As New fReglementClient
        MyReglement.SoldeClient = tSoldeClient.Text
        MyReglement.ajoutmodif = "A"
        MyReglement.CodeClient = tCodeClient.Text
        MyReglement.init()
        MyReglement.ShowDialog()

        MyReglement.Dispose()
        MyReglement.Close()

        'AfficherReglement(0)

        AfficherSituationClient(tSoldeInitial.Text)

    End Sub

    Private Sub bModifierReglement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifierReglement.Click

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(1, "MODIFICATION_REGLEMENT_CLIENT") = "False" Then
            Exit Sub
        End If

        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = "SELECT DemandeMotDePasse FROM [POINTS_CONTROLE] WHERE CodePointControle LIKE 'MODIFICATION_REGLEMENT_CLIENT' AND CodeModule=1"

        'Try
        '    DemandePotDePasse = cmd.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'If DemandePotDePasse = True Then
        '    '------------------------------ demande du mot de passe
        '    Dim myMotDePasse As New fMotDePasse
        '    myMotDePasse.ShowDialog()

        '    ConfirmerEnregistrer = fMotDePasse.Confirmer
        '    CodeOperateur = fMotDePasse.CodeOperateur

        '    myMotDePasse.Dispose()
        '    myMotDePasse.Close()

        '    If ConfirmerEnregistrer = False Then
        '        Exit Sub
        '    End If

        '    cmd.Connection = ConnectionServeur
        '    cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'MODIFICATION_REGLEMENT_CLIENT' AND CodeUtilisateur='" + CodeOperateur + "'"

        '    Try
        '        If cmd.ExecuteScalar() < 1 Then
        '            MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
        '            Exit Sub
        '        End If
        '    Catch ex As Exception
        '        Console.WriteLine(ex.Message)
        '    End Try

        'End If
        If gReglements.RowCount = 0 Then
            MsgBox("Liste vide !", MsgBoxStyle.OkOnly)
            Exit Sub
        End If

        If gReglements(gReglements.Row, "Encaisse") = True Then
            MsgBox("Il est interdit de modifier un réglement encaissé !", MsgBoxStyle.OkOnly)
            Exit Sub
        End If

        Dim MyReglement As New fReglementClient
        MyReglement.CodeReglement = gReglements(gReglements.Row, "NumeroReglementClient")
        MyReglement.CodeClient = tCodeClient.Text
        MyReglement.ajoutmodif = "M"
        MyReglement.init()
        MyReglement.tMontant.Enabled = True
        MyReglement.tMontant.Visible = True
        MyReglement.tMontant.Value = gReglements.Columns("Montant").Value
        MyReglement.cmbBanque.Enabled = False
        MyReglement.cmbNature.Enabled = False
        MyReglement.dtEcheance.Enabled = False
        MyReglement.tNumeroCheque.Enabled = False
        MyReglement.tNomInscrit.Enabled = False
        MyReglement.tMontant.Enabled = False

        MyReglement.ShowDialog()

        MyReglement.Dispose()
        MyReglement.Close()

        AfficherReglement(0)

        AfficherSituationClient(tSoldeInitial.Text)

    End Sub

    Private Sub gReglements_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gReglements.FetchRowStyle
        Dim NumeroReglement As Integer = gReglements.Columns("NumeroReglementClient").CellText(e.Row).ToString
        Dim StrSQL As String = ""
        Dim TestRegleOuNon As Integer = 0

        StrSQL = "SELECT count(NumeroReglementClient) FROM REGLEMENT_CLIENT_VENTE WHERE NumeroReglementClient=" + _
                    NumeroReglement.ToString + " AND NumeroVente='" + NumeroVenteAAfficher + "'"

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL

        Try
            TestRegleOuNon = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        If TestRegleOuNon > 0 Then
            ' le coloriage 
            e.CellStyle.BackColor = System.Drawing.Color.Blue
            e.CellStyle.ForeColor = Color.White
        End If
    End Sub
    Private Sub gVentes_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gVente.FetchRowStyle

        Dim NumeroVente As String = gVentes.Columns("NumeroVente").CellText(e.Row).ToString
        Dim StrSQL As String = ""
        Dim TestRegleOuNon As Integer = 0

        StrSQL = "SELECT count(NumeroReglementClient) FROM REGLEMENT_CLIENT_VENTE WHERE NumeroReglementClient=" + _
                    NumeroReglementAModifier.ToString + " AND NumeroVente='" + NumeroVente + "'"

        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL

        Try
            TestRegleOuNon = cmdReglement.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        If TestRegleOuNon > 0 Then
            ' le coloriage 
            e.CellStyle.BackColor = System.Drawing.Color.Lime
            e.CellStyle.ForeColor = Color.Black
        End If
    End Sub

    Private Sub gReglements_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gReglements.Click

        Dim I As Integer = 0
        Dim index As Integer = 0
        Try
            NumeroReglementAModifier = gReglements(gReglements.Row, "NumeroReglementClient")
        Catch ex As Exception
            NumeroReglementAModifier = 0
        End Try


        NumeroVenteAAfficher = ""
        BlanchirListeReglement = True
        AfficherReglement("")
        BlanchirListeReglement = False
        For I = 0 To gReglements.RowCount - 1
            If gReglements(I, "NumeroReglementClient") = NumeroReglementAModifier Then
                index = I
            End If
        Next

        gReglements.MoveRelative(index)
        gReglements.HighLightRowStyle.BackColor = Color.Blue

        AfficherVentes(NumeroReglementAModifier)

    End Sub
    Private Sub gVentes_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gVentes.MouseClick
        Dim I As Integer = 0
        Dim index As Integer = 0

        NumeroVenteAAfficher = gVentes(gVentes.Row, "NumeroVente")
        NumeroReglementAModifier = 0
        AfficherVentes(0)
        For I = 0 To gVentes.RowCount - 1
            If gVentes(I, "NumeroVente") = NumeroVenteAAfficher Then
                index = I
            End If
        Next

        gVentes.MoveRelative(index)
        gVentes.HighLightRowStyle.BackColor = Color.Lime
        AfficherReglement(NumeroVenteAAfficher)

    End Sub

    Private Sub bSupprimerReglement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerReglement.Click

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(1, "SUPPRESSION_REGLEMENT_CLIENT") = "False" Then
            Exit Sub
        End If

        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = "SELECT DemandeMotDePasse FROM [POINTS_CONTROLE] WHERE CodePointControle LIKE 'SUPPRESSION_REGLEMENT_CLIENT' AND CodeModule=1"

        'Try
        '    DemandePotDePasse = cmd.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'If DemandePotDePasse = True Then
        '    '------------------------------ demande du mot de passe
        '    Dim myMotDePasse As New fMotDePasse
        '    myMotDePasse.ShowDialog()

        '    ConfirmerEnregistrer = fMotDePasse.Confirmer
        '    CodeOperateur = fMotDePasse.CodeOperateur

        '    myMotDePasse.Dispose()
        '    myMotDePasse.Close()

        '    If ConfirmerEnregistrer = False Then
        '        Exit Sub
        '    End If

        '    cmd.Connection = ConnectionServeur
        '    cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'SUPPRESSION_REGLEMENT_CLIENT' AND CodeUtilisateur='" + CodeOperateur + "'"

        '    Try
        '        If cmd.ExecuteScalar() < 1 Then
        '            MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
        '            Exit Sub
        '        End If
        '    Catch ex As Exception
        '        Console.WriteLine(ex.Message)
        '    End Try

        'End If

        Dim NumeroDesVentesRegle As Integer = 0
        Dim StrSQL As String = ""

        Connect()
        If gReglements.RowCount = 0 Then
            MsgBox("Liste Vide", MsgBoxStyle.OkOnly)
        Else


            If gReglements(gReglements.Row, "libelle") = "Reglement Lors d une Vente" Then
                MsgBox("Il est interdit de supprimer un réglement suivi d une vente !", MsgBoxStyle.OkOnly)
                Exit Sub
            End If

            If gReglements(gReglements.Row, "libelle") = "Suppression vente" Then
                MsgBox("Il est interdit de supprimer ce réglement !", MsgBoxStyle.OkOnly)
                Exit Sub
            End If

            If ModeADMIN <> "ADMIN" Then
                If gReglements(gReglements.Row, "Encaisse") = True Then
                    MsgBox("Il est interdit de supprimer un réglement encaissé !", MsgBoxStyle.OkOnly)
                    Exit Sub
                End If
            End If

            Try
                NumeroReglementAModifier = gReglements(gReglements.Row, "NumeroReglementClient")
            Catch ex As Exception
                NumeroReglementAModifier = 0
            End Try


            If NumeroReglementAModifier = 0 Then
                MsgBox("Vous devez sélectionner un règlement", MsgBoxStyle.OkOnly)
                Exit Sub
            End If
            If MsgBox("Voulez vous vraiment supprimer ce Règlement  ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then



                '------------------------------ demande du mot de passe
                Dim myMotDePasse As New fMotDePasse
                myMotDePasse.ShowDialog()

                ConfirmerEnregistrer = fMotDePasse.Confirmer
                CodeOperateur = fMotDePasse.CodeOperateur

                myMotDePasse.Dispose()
                myMotDePasse.Close()

                If ConfirmerEnregistrer = False Then
                    Exit Sub
                End If
                '''''''''''''''''''''''''''
                cmd.Connection = ConnectionServeur
                cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'SUPPRESSION_REGLEMENT_CLIENT' AND CodeUtilisateur='" + CodeOperateur + "'"

                Try
                    If cmd.ExecuteScalar() < 1 Then
                        MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
                        Exit Sub
                    Else
                        'CodeOperateur = "True"
                    End If
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                '''''''''''''''''''''''''''''

                'StrSQL = " SELECT count([NumeroReglementClient]) FROM REGLEMENT_CLIENT_VENTE WHERE " + _
                '         "NumeroReglementClient='" + NumeroReglementAModifier.ToString + "'"
                'cmdClient.Connection = ConnectionServeur
                'cmdClient.CommandText = StrSQL

                'Try
                '    NumeroDesVentesRegle = cmdClient.ExecuteScalar()
                'Catch ex As Exception
                '    Console.WriteLine(ex.Message)
                'End Try

                'If NumeroDesVentesRegle <> 0 Then
                '    MsgBox("Règlement déja affecté à une ou plusieurs ventes, impossible de le supprimer", MsgBoxStyle.OkOnly)
                '    Exit Sub
                'End If


                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "UPDATE REGLEMENT_CLIENT SET CodePersonnel = " + Quote(CodeOperateur) + " WHERE NumeroReglementClient = " + NumeroReglementAModifier.ToString
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try

                'traiter ds le trigger
                'Try
                '    cmd.Connection = ConnectionServeur
                '    cmd.CommandText = "UPDATE MOUVEMENT_ETATS SET Supprimer = 1 WHERE TypeOperation = 'Reglement client' and NumeroReglement = " + NumeroReglementAModifier.ToString
                '    cmd.ExecuteNonQuery()
                'Catch ex As Exception
                '    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                'End Try

                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM REGLEMENT_CLIENT_VENTE WHERE NumeroReglementClient = " + NumeroReglementAModifier.ToString
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try

                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM REGLEMENT_CLIENT WHERE NumeroReglementClient = " + NumeroReglementAModifier.ToString
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try

            End If
            End If
            AfficherReglement(0)
    End Sub

    Private Sub C1DockingTabPage3_Enter(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1DockingTabPage3.Enter
        AfficherFamille()
        'tRang.Enabled = False
    End Sub

    Public Sub AfficherFamille()
        Dim StrSQL As String = ""
        Dim NumeroPremierReglement As Integer = 0
        Dim i As Integer = 0

        If (dsLienFamille.Tables.IndexOf("CLIENT_FAMILLE") > -1) Then
            dsLienFamille.Tables("CLIENT_FAMILLE").Clear()
        End If

        'chargement des liens familiales

        StrSQL = " SELECT CodeClient," + _
                 " CodeDeFamille," + _
                 " Nom," + _
                 " LibelleLienDeParente," + _
                 " DateNaissance," + _
                 " DateValidite," + _
                 " Rang " + _
                 " FROM CLIENT_FAMILLE LEFT OUTER JOIN LIEN_PARENTE ON CLIENT_FAMILLE.CodeLienDeParente=LIEN_PARENTE.CodeLienDeParente " + _
                 " WHERE CLIENT_FAMILLE.CodeClient='" + tCodeClient.Text + "'"

        cmdLienFamille.Connection = ConnectionServeur
        cmdLienFamille.CommandText = StrSQL
        daLienFamille = New SqlDataAdapter(cmdLienFamille)
        daLienFamille.Fill(dsLienFamille, "CLIENT_FAMILLE")
        cbLienFamille = New SqlCommandBuilder(daLienFamille)

        With gFamille
            .Columns.Clear()
            Try
                .DataSource = dsLienFamille
            Catch ex As Exception
            End Try
            .DataMember = "CLIENT_FAMILLE"
            .Rebind(False)
            .Columns("CodeDeFamille").Caption = "Code Famille   "
            .Columns("Nom").Caption = "Nom"
            .Columns("DateNaissance").Caption = "Date Naissance"
            .Columns("LibelleLienDeParente").Caption = "Lien"
            .Columns("DateValidite").Caption = "Date Validité"
            .Columns("Rang").Caption = "Rang"

            Dim DateNaissanceC1DateEdit As C1DateEdit = New C1DateEdit()
            .Columns("DateNaissance").Editor = DateNaissanceC1DateEdit

            Dim DateValiditeC1DateEdit As C1DateEdit = New C1DateEdit()
            .Columns("DateValidite").Editor = DateValiditeC1DateEdit

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns(2).Style.HorizontalAlignment = AlignHorzEnum.Near
            'For i = 0 To .Columns.Count - 1
            '    .Splits(0).DisplayColumns(i).Locked = True
            'Next
            .Splits(0).DisplayColumns("LibelleLienDeParente").Locked = True
            .Splits(0).DisplayColumns("Rang").Locked = True

            .Splits(0).DisplayColumns("CodeClient").Width = 0
            .Splits(0).DisplayColumns("CodeClient").Visible = False
            .Splits(0).DisplayColumns("CodeDeFamille").Width = 0
            .Splits(0).DisplayColumns("CodeDeFamille").Visible = False
            .Splits(0).DisplayColumns("Nom").Width = 200
            .Splits(0).DisplayColumns("LibelleLienDeParente").Width = 50
            .Splits(0).DisplayColumns("DateNaissance").Width = 170
            .Splits(0).DisplayColumns("DateValidite").Width = 170
            .Splits(0).DisplayColumns("Rang").Width = 120

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            ParametreGrid(gFamille)

            Dim Col As New C1.Win.C1TrueDBGrid.C1DataColumn
            Dim dc As C1.Win.C1TrueDBGrid.C1DisplayColumn

            With gFamille
                .Columns.Insert(0, Col)
                Col.Caption = "Lien"
                dc = .Splits(0).DisplayColumns.Item("Lien")
                dc.Width = 120
                .Splits(0).DisplayColumns(7).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                .Splits(0).DisplayColumns(7).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                dc.Visible = True
                .Rebind(True)
            End With

        End With
    End Sub

    Private Sub bAjouterLien_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterLien.Click
        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(1, "AJOUT_MEMBRE_FAMILLE_CLIENT") = "False" Then
            Exit Sub
        End If

        dtpDateValidite.Value = dpDateValidite.Value
        dtpDateNaissance.Value = Date.Now.AddYears(-10)

        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = "SELECT DemandeMotDePasse FROM [POINTS_CONTROLE] WHERE CodePointControle LIKE 'AJOUT_MEMBRE_FAMILLE_CLIENT' AND CodeModule=1"

        'Try
        '    DemandePotDePasse = cmd.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'If DemandePotDePasse = True Then
        '    '------------------------------ demande du mot de passe
        '    Dim myMotDePasse As New fMotDePasse
        '    myMotDePasse.ShowDialog()

        '    ConfirmerEnregistrer = fMotDePasse.Confirmer
        '    CodeOperateur = fMotDePasse.CodeOperateur

        '    myMotDePasse.Dispose()
        '    myMotDePasse.Close()

        '    If ConfirmerEnregistrer = False Then
        '        Exit Sub
        '    End If

        '    cmd.Connection = ConnectionServeur
        '    cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'AJOUT_MEMBRE_FAMILLE_CLIENT' AND CodeUtilisateur='" + CodeOperateur + "'"

        '    Try
        '        If cmd.ExecuteScalar() < 1 Then
        '            MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
        '            Exit Sub
        '        End If
        '    Catch ex As Exception
        '        Console.WriteLine(ex.Message)
        '    End Try
        'End If

        If ajoutmodif = "A" Then
            If MsgBox("Vous devez valider l'ajout du client ! Voulez-vous confirmer ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Valider le client") = MsgBoxResult.Yes Then

                bConfirmer_Click(sender, e)

                cmd.CommandText = "SELECT COUNT(*) FROM CLIENT WHERE CodeClient=" + Quote(tCodeClient.Text)
                cmd.Connection = ConnectionServeur
                If cmd.ExecuteScalar = 0 Then
                    Exit Sub
                Else
                    Me.Show()
                    ajoutmodif = "M"
                    CodeClient = tCodeClient.Text
                    Init()
                    tabFamille.Show()
                End If
            Else
                Exit Sub
            End If
        End If

        tNomFamille.Value = ""
        cmbLienDeParente.Text = ""
        'dtpDateNaissance.Value = ""
        'dtpDateValidite.Value = ""
        tRang.Value = ""
        AjoutModifLien = "A"

        bAjouterLien.Visible = False
        'bModifierLien.Visible = False
        bConfirmerLien.Visible = True
        bAnnulerLien.Visible = True
        grpInfoLien.Visible = True

    End Sub

    Private Sub bModifierLien_Click_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifierLien.Click

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(1, "MODIFICATION_MEMBRE_FAMILLE_CLIENT") = "False" Then
            Exit Sub
        End If

        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = "SELECT DemandeMotDePasse FROM [POINTS_CONTROLE] WHERE CodePointControle LIKE 'MODIFICATION_MEMBRE_FAMILLE_CLIENT' AND CodeModule=1"

        'Try
        '    DemandePotDePasse = cmd.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'If DemandePotDePasse = True Then
        '    '------------------------------ demande du mot de passe
        '    Dim myMotDePasse As New fMotDePasse
        '    myMotDePasse.ShowDialog()

        '    ConfirmerEnregistrer = fMotDePasse.Confirmer
        '    CodeOperateur = fMotDePasse.CodeOperateur

        '    myMotDePasse.Dispose()
        '    myMotDePasse.Close()

        '    If ConfirmerEnregistrer = False Then
        '        Exit Sub
        '    End If

        '    cmd.Connection = ConnectionServeur
        '    cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'MODIFICATION_MEMBRE_FAMILLE_CLIENT' AND CodeUtilisateur='" + CodeOperateur + "'"

        '    Try
        '        If cmd.ExecuteScalar() < 1 Then
        '            MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
        '            Exit Sub
        '        End If
        '    Catch ex As Exception
        '        Console.WriteLine(ex.Message)
        '    End Try
        'End If

        If gFamille.RowCount = 0 Then
            MsgBox("Liste vide !", MsgBoxStyle.OkOnly)
            Exit Sub
        End If

        tNomFamille.Value = gFamille(gFamille.Row, "Nom")
        cmbLienDeParente.Text = gFamille(gFamille.Row, "LibelleLienDeParente")
        dtpDateNaissance.Value = gFamille(gFamille.Row, "DateNaissance")
        dtpDateValidite.Value = gFamille(gFamille.Row, "DateValidite")
        tRang.Value = gFamille(gFamille.Row, "Rang")
        tCodeFamille.Value = gFamille(gFamille.Row, "CodeDeFamille")
        AjoutModifLien = "M"

        bAjouterLien.Visible = False
        'bModifierLien.Visible = False
        bConfirmerLien.Visible = True
        bAnnulerLien.Visible = True
        grpInfoLien.Visible = True

    End Sub

    Private Sub bConfirmerLien_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmerLien.Click

        Dim DernierCode As Integer = 0
        Dim SQL As New SqlCommand
        If tCodeFamille.Text = "" Then

            SQL.Connection = ConnectionServeur
            SQL.CommandText = " SELECT MAX(CodeDeFamille) FROM CLIENT_FAMILLE WHERE codeclient='" + tCodeClient.Text + "'"
            Try
                DernierCode = SQL.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
                DernierCode = 0
            End Try
            DernierCode = DernierCode + 1
            tCodeFamille.Value = DernierCode
        End If

        If tNomFamille.Text = "" Then
            MsgBox("Veuillez saisir le Nom !", MsgBoxStyle.Critical, "Erreur")
            tNomFamille.Focus()
            Exit Sub
        End If

        If cmbLienDeParente.Text = "" Then
            MsgBox("Veuillez saisir le lien de parenté !", MsgBoxStyle.Critical, "Erreur")
            cmbLienDeParente.Focus()
            Exit Sub
        End If

        If dtpDateNaissance.Text = "" Or dtpDateNaissance.Text = "01/01/0001" Or dtpDateNaissance.Text = "01/01/1900" Then
            MsgBox("Veuillez saisir la date de naissance !", MsgBoxStyle.Critical, "Erreur")
            dtpDateNaissance.Focus()
            Exit Sub
        End If

        If dtpDateValidite.Text = "" Or dtpDateValidite.Text = "01/01/0001" Or dtpDateValidite.Text = "01/01/1900" Then
            MsgBox("Veuillez saisir la date de validité !", MsgBoxStyle.Critical, "Erreur")
            dtpDateValidite.Focus()
            Exit Sub
        End If

        If cmbLienDeParente.Text = "ENFANT" Then
            'If AjoutModifLien = "A" Then
            '    SQL.Connection = ConnectionServeur
            '    SQL.CommandText = " SELECT COUNT(*) FROM CLIENT_FAMILLE WHERE CodeClient='" + tCodeClient.Text + "' AND CodeLienDeParente=3"
            '    Try
            '        tRang.Value = SQL.ExecuteScalar() + 1
            '    Catch ex As Exception
            '        Console.WriteLine(ex.Message)
            '        DernierCode = 0
            '    End Try
            'End If

            SQL.CommandText = "SELECT COUNT(*) FROM CLIENT_FAMILLE WHERE CodeClient='" + tCodeClient.Text + "' AND CodeLienDeParente=3 AND rang = '" + tRang.Value + "'"
            SQL.Connection = ConnectionServeur
            If (SQL.ExecuteScalar > 0 And AjoutModifLien = "A") Then
                MsgBox("Rang existe déja dans la famille pour une autre enfant !", MsgBoxStyle.Critical, "Erreur")
                tRang.Focus()
                Exit Sub
            End If

        Else
            'tRang.Value = 0

            If cmbLienDeParente.Text = "CONJOINT" Then
                SQL.CommandText = "SELECT COUNT(*) FROM CLIENT_FAMILLE WHERE CodeClient='" + tCodeClient.Text + "' AND CodeLienDeParente=2"
                SQL.Connection = ConnectionServeur
                If (SQL.ExecuteScalar > 0 And AjoutModifLien = "A") Then
                    MsgBox("Conjoint existe déja dans la famille !", MsgBoxStyle.Critical, "Erreur")
                    Exit Sub
                End If
            End If
            If cmbLienDeParente.Text = "PERE" Then
                SQL.CommandText = "SELECT COUNT(*) FROM CLIENT_FAMILLE WHERE CodeClient='" + tCodeClient.Text + "' AND CodeLienDeParente=4"
                SQL.Connection = ConnectionServeur
                If SQL.ExecuteScalar > 0 And AjoutModifLien = "A" Then
                    MsgBox("Le père existe déja dans la famille !", MsgBoxStyle.Critical, "Erreur")
                    Exit Sub
                End If
            End If
            If cmbLienDeParente.Text = "MERE" Then
                SQL.CommandText = "SELECT COUNT(*) FROM CLIENT_FAMILLE WHERE CodeClient='" + tCodeClient.Text + "' AND CodeLienDeParente=5"
                SQL.Connection = ConnectionServeur
                If SQL.ExecuteScalar > 0 And AjoutModifLien = "A" Then
                    MsgBox("La mère existe déja dans la famille !", MsgBoxStyle.Critical, "Erreur")
                    Exit Sub
                End If
            End If
        End If

        Dim StrSQL As String = ""
        If AjoutModifLien = "A" Then
            StrSQL = "INSERT INTO CLIENT_FAMILLE (""CodeClient"",""CodeDeFamille""" + _
                        ",""Nom"",""CodeLienDeParente"",""DateNaissance"",""DateValidite"",""Rang"") VALUES(" + _
                        Quote(tCodeClient.Text) + _
                        "," + _
                        Quote(DernierCode.ToString) + _
                        "," + _
                        Quote(tNomFamille.Text) + _
                        "," + _
                        Quote(cmbLienDeParente.SelectedValue.ToString) + _
                        "," + _
                        Quote(dtpDateNaissance.Text) + _
                        "," + _
                        Quote(dtpDateValidite.Text) + _
                        "," + _
                        Quote(tRang.Text) + _
                        ")"

            SQL.Connection = ConnectionServeur
            SQL.CommandText = StrSQL

            Try
                SQL.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            'With dsLienFamille
            '    dr = .Tables("CLIENT_FAMILLE").NewRow

            '    dr.Item("CodeClient") = tCodeClient.Text
            '    dr.Item("CodeDeFamille") = DernierCode
            '    dr.Item("Nom") = tNomFamille.Text
            '    dr.Item("LibelleLienDeParente") = cmbLienDeParente.SelectedValue
            '    dr.Item("DateNaissance") = dtpDateNaissance.Text
            '    dr.Item("DateValidite") = dtpDateValidite.Text
            '    dr.Item("Rang") = tRang.Text

            '    .Tables("CLIENT_FAMILLE").Rows.Add(dr)
            'End With

        ElseIf AjoutModifLien = "M" Then

            StrSQL = "UPDATE CLIENT_FAMILLE SET Nom=" + Quote(tNomFamille.Text) + _
                            ",CodeLienDeParente=" + cmbLienDeParente.SelectedValue.ToString + _
                            ",DateNaissance=" + Quote(dtpDateNaissance.Text) + _
                            ",DateValidite=" + Quote(dtpDateValidite.Text) + _
                            ",Rang=" + tRang.Text + _
                            " WHERE CodeClient=" + Quote(tCodeClient.Text) + _
                            " AND CodeDeFamille=" + tCodeFamille.Text


            SQL.Connection = ConnectionServeur
            SQL.CommandText = StrSQL
            Try
                SQL.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            'Dim i As Integer = 0
            'Dim index As Integer = 0
            'For i = 0 To dsLienFamille.Tables("CLIENT_FAMILLE").Rows.Count - 1
            '    If dsLienFamille.Tables("CLIENT_FAMILLE").Rows(i).Item("CodeClient") = tCodeClient.Text And dsLienFamille.Tables("CLIENT_FAMILLE").Rows(i).Item("CodeDeFamille") = tCodeFamille.Text Then
            '        index = i
            '    End If
            'Next

            'With dsLienFamille.Tables("CLIENT_FAMILLE")
            '    dr = .Rows(index)

            '    dr.Item("CodeClient") = tCodeClient.Text
            '    dr.Item("CodeDeFamille") = DernierCode
            '    dr.Item("Nom") = tNomFamille.Text
            '    dr.Item("CodeLienDeParente") = cmbLienDeParente.SelectedValue
            '    dr.Item("DateNaissance") = dtpDateNaissance.Text
            '    dr.Item("DateValidite") = dtpDateValidite.Text
            '    dr.Item("Rang") = tRang.Text

            'End With

        End If

        'Try
        'daLienFamille.Update(dsLienFamille, "CLIENT_FAMILLE")
        bAjouterLien.Visible = True
        'bModifierLien.Visible = True
        bConfirmerLien.Visible = False
        bAnnulerLien.Visible = False
        grpInfoLien.Visible = False
        tCodeFamille.Value = ""
        dtpDateNaissance.Value = ""
        dtpDateValidite.Value = ""

        'Catch ex As Exception
        '    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
        '    dsLienFamille.Reset()
        '    Me.Init()
        'End Try
        AfficherFamille()

        bAjouterLien_Click(sender, e)
        tNomFamille.Focus()

    End Sub

    Private Sub bAnnulerLien_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnulerLien.Click
        If dsLienFamille.HasChanges Then
            If MsgBox("Voulez vous vraiment annuler les modifications de ce client ?", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                bAjouterLien.Visible = True
                'bModifierLien.Visible = True
                bConfirmerLien.Visible = False
                bAnnulerLien.Visible = False
                grpInfoLien.Visible = False

            End If
        Else
            If MsgBox("Voulez vous vraiment annuler les modifications de ce client ?", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                bAjouterLien.Visible = True
                'bModifierLien.Visible = True
                bConfirmerLien.Visible = False
                bAnnulerLien.Visible = False
                grpInfoLien.Visible = False

            End If
        End If

    End Sub

    Private Sub bSupprimerLien_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerLien.Click

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(1, "SUPPRESSION_MEMBRE_FAMILLE_CLIENT") = "False" Then
            Exit Sub
        End If

        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = "SELECT DemandeMotDePasse FROM [POINTS_CONTROLE] WHERE CodePointControle LIKE 'SUPPRESSION_MEMBRE_FAMILLE_CLIENT' AND CodeModule=1"

        'Try
        '    DemandePotDePasse = cmd.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'If DemandePotDePasse = True Then
        '    '------------------------------ demande du mot de passe
        '    Dim myMotDePasse As New fMotDePasse
        '    myMotDePasse.ShowDialog()

        '    ConfirmerEnregistrer = fMotDePasse.Confirmer
        '    CodeOperateur = fMotDePasse.CodeOperateur

        '    myMotDePasse.Dispose()
        '    myMotDePasse.Close()

        '    If ConfirmerEnregistrer = False Then
        '        Exit Sub
        '    End If

        '    cmd.Connection = ConnectionServeur
        '    cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'SUPPRESSION_MEMBRE_FAMILLE_CLIENT' AND CodeUtilisateur='" + CodeOperateur + "'"

        '    Try
        '        If cmd.ExecuteScalar() < 1 Then
        '            MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
        '            Exit Sub
        '        End If
        '    Catch ex As Exception
        '        Console.WriteLine(ex.Message)
        '    End Try
        'End If


        Dim CodeClient As String = ""
        Dim CodeFamille As String = ""
        Dim StrSQL As String = ""

        CodeClient = gFamille(gFamille.Row, "CodeClient")
        CodeFamille = gFamille(gFamille.Row, "CodeDeFamille")

        Connect()
        If CodeClient = "" Then
            MsgBox("Veuillez sélectionner un Client", MsgBoxStyle.OkOnly)
        Else
            If MsgBox("Voulez vous vraiment supprimer ce Client  ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then

                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM CLIENT_FAMILLE WHERE CodeClient = " + Quote(CodeClient) + _
                                      " AND CodeDeFamille =" + CodeFamille.ToString
                    cmd.ExecuteNonQuery()
                    AfficherFamille()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try

            End If
        End If
    End Sub

    Private Sub gFamille_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub gFamille_UnboundColumnFetch(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gFamille.UnboundColumnFetch
        e.Value = RecupererValeurExecuteScalaire("LibelleLienDeParente", "LIEN_PARENTE", "CodeLienDeParente", gFamille(e.Row, ("CodeLienDeParente")))
    End Sub

    Private Sub tRemPCharge_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRemPCharge.KeyUp
        If e.KeyCode = Keys.Enter Then
            tCrediMaxClient.Focus()
        End If
    End Sub

    Private Sub tRemPCharge_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tRemPCharge.TextChanged

        Dim TestConversion As Double = 0.0

        If tRemPCharge.Text <> "" Then
            Try   ' test si un valeur numerique ou non
                TestConversion = Math.Round(CDbl(tRemPCharge.Text), 3)
            Catch ex As Exception
                MsgBox("Remise invalide !", MsgBoxStyle.Critical, "Erreur")
                tRemPCharge.Text = "0.000"
                tRemPCharge.Focus()
                tRemPCharge.SelectionLength = tRemPCharge.Text.Length
                Exit Sub
            End Try
        End If
    End Sub

    Private Sub cmbMedecin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMedecin.KeyUp
        Dim cmd As New SqlCommand
        If e.KeyCode = Keys.Enter Then
            cmbMedecin.Text = cmbMedecin.WillChangeToText
            cmbMutuelle.Focus()

            Try
                cmd.Connection = ConnectionServeur
                cmd.CommandText = "SELECT TOP(1) IdentifiantCNAM FROM MEDECIN WHERE CodeMedecin =" & Quote(cmbMedecin.SelectedValue)
                tIdentificationMedecin.Text = cmd.ExecuteScalar
            Catch
            End Try


        Else
            cmbMedecin.OpenCombo()
        End If


        'Dim StrSQL As String
        'cmbMedecin.OpenCombo()
        'If e.KeyCode = Keys.Enter Then
        '    If cmbMedecin.Text <> "" Then
        '        If cmbMedecin.Columns("NomMedecin").Value.ToString.ToUpper Like cmbMedecin.Text.ToUpper & "*" Then
        '            cmbMedecin.Text = cmbMedecin.Columns("NomMedecin").Value
        '            dpDateValidite.Focus()
        '        Else
        '            fAjouterFiche.Text = "Ajouter Medecin"
        '            fAjouterFiche.tLibelle.Value = cmbMedecin.Text
        '            fAjouterFiche.ShowDialog()

        '            If fAjouterFiche.Annuler = True Then
        '                cmbMedecin.Text = ""
        '            Else
        '                'chargement des Formes
        '                dsClient.Tables("MEDECIN").Clear()
        '                StrSQL = "SELECT DISTINCT CodeMedecin,NomMedecin FROM MEDECIN ORDER BY NomMedecin ASC"
        '                cmdClient.Connection = ConnectionServeur
        '                cmdClient.CommandText = StrSQL
        '                daClient = New SqlDataAdapter(cmdClient)
        '                daClient.Fill(dsClient, "MEDECIN")

        '                cmbMedecin.Text = fAjouterFiche.tLibelle.Value
        '                cmbMedecin.SelectedValue = fAjouterFiche.tCodeMedecin.Value
        '            End If
        '        End If
        '    End If
        '    fAjouterFiche.Close()
        '    cmbMedecin.CloseCombo()
        '    tCrediMaxClient.Focus()
        'End If


    End Sub

    Private Sub cmbMedecin_LostFocus(sender As Object, e As System.EventArgs) Handles cmbMedecin.LostFocus
        Dim cmd As New SqlCommand
        Try
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "SELECT TOP(1) IdentifiantCNAM FROM MEDECIN WHERE CodeMedecin =" & Quote(cmbMedecin.SelectedValue)
            tIdentificationMedecin.Text = cmd.ExecuteScalar
        Catch
        End Try
    End Sub

    Private Sub cmbMedecin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbMedecin.TextChanged
        Dim cmd As New SqlCommand
        Try
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "SELECT TOP(1) IdentifiantCNAM FROM MEDECIN WHERE CodeMedecin =" & Quote(cmbMedecin.SelectedValue)
            tIdentificationMedecin.Text = cmd.ExecuteScalar
        Catch
        End Try
    End Sub

    Private Sub cmbMutuelle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMutuelle.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbMutuelle.Text = cmbMutuelle.WillChangeToText
            tMatricule.Focus()
        Else
            cmbMutuelle.OpenCombo()
        End If
    End Sub

    Private Sub cmbMutuelle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub C1DockingTabPage3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub cmbLienDeParente_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbLienDeParente.KeyDown
        If e.KeyCode = Keys.Enter Then
            dtpDateNaissance.Focus()
        End If
    End Sub

    Private Sub cmbLienDeParente_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)

    End Sub

    Private Sub cmbLienDeParente_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbLienDeParente.TextChanged

        If cmbLienDeParente.Text = "ENFANT" Then
            tRang.Enabled = True
        ElseIf cmbLienDeParente.Text = "CONJOINT" Then
            tRang.Enabled = False
        Else
            tRang.Enabled = False
            'tRang.Value = ""
        End If

        Dim SQL As New SqlCommand

        If cmbLienDeParente.Text = "ENFANT" Then
            'If AjoutModifLien = "A" Then
            SQL.Connection = ConnectionServeur
            SQL.CommandText = " SELECT COUNT(*) FROM CLIENT_FAMILLE WHERE CodeClient='" + tCodeClient.Text + "' AND CodeLienDeParente=3"
            Try
                tRang.Value = SQL.ExecuteScalar() + 1
            Catch ex As Exception
                Console.WriteLine(ex.Message)
                'DernierCode = 0
            End Try
            'End If
        ElseIf cmbLienDeParente.Text = "CONJOINT" Then

            tRang.Value = 1
        Else
            tRang.Value = 0
        End If



    End Sub

    Private Sub tNomFamille_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles tNomFamille.KeyDown
        If e.KeyCode = Keys.Enter Then
            cmbLienDeParente.Focus()
        End If
    End Sub

    Private Sub tNomFamille_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)

    End Sub

    Private Sub tNomFamille_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub dtpDateNaissance_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles dtpDateNaissance.KeyDown
        If e.KeyCode = Keys.Enter Then
            dtpDateValidite.Focus()
        End If
    End Sub

    Private Sub dtpDateNaissance_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)

    End Sub

    Private Sub dtpDateNaissance_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub dtpDateValidite_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles dtpDateValidite.KeyDown
        If e.KeyCode = Keys.Enter Then
            If e.KeyCode = Keys.Enter And Not tRang.Enabled Then
                bConfirmerLien_Click(sender, e)
            Else
                tRang.Focus()
            End If
        End If
    End Sub

    Private Sub dtpDateValidite_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)

    End Sub

    Private Sub dtpDateValidite_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub C1DockingTabPage1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub C1DockingTabPage1_Enter(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1DockingTabPage1.Enter
        tCodeClient.Focus()
    End Sub

    Private Sub tAdresseClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tAdresseClient.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbVilleClient.Focus()
            Exit Sub
        End If

        'If e.KeyCode = Keys.F3 Then
        '    bConfirmer_Click(sender, e)
        '    Exit Sub
        'End If
        'If e.KeyCode = Keys.F10 Then
        '    bAnnuler_Click(sender, e)
        '    Exit Sub
        'End If
    End Sub

    Private Sub tAdresseClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tNomClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNomClient.KeyUp
        If e.KeyCode = Keys.Enter Then
            tAdresseClient.Focus()
            Exit Sub
        End If

        'If e.KeyCode = Keys.F3 Then
        '    bConfirmer_Click(sender, e)
        '    Exit Sub
        'End If
        'If e.KeyCode = Keys.F10 Then
        '    bAnnuler_Click(sender, e)
        '    Exit Sub
        'End If
    End Sub

    Private Sub tNomClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tTelephoneClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTelephoneClient.KeyUp
        If e.KeyCode = Keys.Enter Then
            tFaxClient.Focus()
        End If
    End Sub

    Private Sub tTelephoneClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tFaxClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tFaxClient.KeyUp
        If e.KeyCode = Keys.Enter Then
            tCodePostalClient.Focus()
        End If
    End Sub

    Private Sub tFaxClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tCodePostalClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodePostalClient.KeyUp
        If e.KeyCode = Keys.Enter Then
            tIdCNAM.Focus()
        End If

    End Sub

    Private Sub tCodePostalClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tIdCNAM_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tIdCNAM.KeyUp
        If e.KeyCode = Keys.Enter Then
            tCleIdCNAM.Focus()
        End If
    End Sub

    Private Sub tIdCNAM_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tIdCNAM.LostFocus
        If tIdCNAM.Text = "" Then
            dpDateValidite.Enabled = False
        Else
            dpDateValidite.Enabled = True
            'tIdCNAM.Text = tIdCNAM.Text.PadLeft(10, "0")
        End If
    End Sub

    Private Sub tIdCNAM_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tDateValidite_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            cmbSituationClient.Focus()
        End If
    End Sub

    Private Sub tDateValidite_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tMatricule_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tMatricule.KeyUp
        If e.KeyCode = Keys.Enter Then
            tRemPCharge.Focus()
        End If
    End Sub

    Private Sub tMatricule_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tDateDernierAchatClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tDateDernierAchatClient.KeyUp
        If e.KeyCode = Keys.Enter Then
            tCrediMaxClient.Focus()
        End If
    End Sub

    Private Sub tDateDernierAchatClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tCrediMaxClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCrediMaxClient.KeyUp
        If e.KeyCode = Keys.Enter Then
            tSoldeInitial.Focus()
        End If
    End Sub

    Private Sub tCrediMaxClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tSoldeClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tSoldeClient.KeyUp
        If e.KeyCode = Keys.Enter Then
            tEnCours.Focus()
        End If
    End Sub

    Private Sub tSoldeClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tEnCours_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tEnCours.KeyUp
        If e.KeyCode = Keys.Enter Then
            tResteAPayer.Focus()
        End If
    End Sub

    Private Sub tEnCours_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tResteAPayer_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tResteAPayer.KeyUp
        If e.KeyCode = Keys.Enter Then
            tSoldeInitial.Focus()
        End If
    End Sub

    Private Sub tResteAPayer_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tSoldeInitial_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        'If e.KeyCode = Keys.Enter Then
        '    tDateInitial.Focus()
        'End If
    End Sub

    Private Sub tSoldeInitial_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tDateInitial_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tDateInitial.KeyUp
        If e.KeyCode = Keys.Enter Then
            tMatriculeFiscale.Focus()
        End If
    End Sub

    Private Sub tDateInitial_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tRemarqueClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        'If e.KeyCode = Keys.Tab Then
        '    bConfirmer.Focus()
        'End If
    End Sub

    Private Sub tRemarqueClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub tDateNaissance_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tDateNaissance.KeyUp
        If e.KeyCode = Keys.Enter Then
            dpDateValidite.Focus()
            Exit Sub
        End If

        'If e.KeyCode = Keys.F3 Then
        '    bConfirmer_Click(sender, e)
        '    Exit Sub
        'End If
        'If e.KeyCode = Keys.F10 Then
        '    bAnnuler_Click(sender, e)
        '    Exit Sub
        'End If
    End Sub

    Private Sub tDateNaissance_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub fFicheClient_Activated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Activated
        If ajoutmodif = "A" Then
            tCodeClient.Focus()
        Else
            tNomClient.Focus()
        End If

    End Sub

    Private Sub fFicheClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        'If e.KeyCode = Keys.F3 Then
        '    bConfirmer_Click(sender, e)
        '    Exit Sub
        'End If
        'If e.KeyCode = Keys.F10 Then
        '    bAnnuler_Click(sender, e)
        '    Exit Sub
        'End If
    End Sub
    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)


        If e.KeyCode = Keys.F10 Then
            e.SuppressKeyPress = True
        Else
            e.SuppressKeyPress = False
        End If

        If e.KeyData = Keys.F3 Then
            bConfirmer_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F5 Then
            bAjouterReglement_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F7 Then
            bSupprimerReglement_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F8 Then
            bModifierReglement_Click(o, e)
            Exit Sub
        End If

    End Sub

    Private Sub fFicheClient_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        X_initial = Pic_Carnet_CNAM.Width
        Y_initial = Pic_Carnet_CNAM.Height

        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler

    End Sub

    Private Sub GroupBox9_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub gReglements_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click
        If ControleDAcces(1, "MODIFIER_SOLDE_INITIAL") = "False" Then
            Exit Sub
        End If

        tSoldeInitial.Enabled = True
        tSoldeInitial.Focus()
    End Sub

    Private Sub dpDateValidite_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles dpDateValidite.GotFocus
        If tIdCNAM.Text = "" Then
            dpDateValidite.Enabled = False
        Else
            dpDateValidite.Enabled = True
        End If
    End Sub

    Private Sub dpDateValidite_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dpDateValidite.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbMedecin.Focus()
            Exit Sub
        End If
        If tIdCNAM.Text = "" Then
            dpDateValidite.Enabled = False
        End If
    End Sub

    Private Sub gVentes_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub gVentes_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gVentes.KeyUp
        If e.KeyCode = Keys.F1 Then
            Dim MyVenteAffiche As New fVenteJusteAffichage
            MyVenteAffiche.NumeroVente = Trim(gVentes(gVentes.Row, "NumeroVente"))
            MyVenteAffiche.ShowDialog()
            MyVenteAffiche.Close()
            MyVenteAffiche.Dispose()

        End If
    End Sub

    Private Sub bParcourir_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bParcourir.Click
        'le btn 
        BtnChoisir = "bParcourir"
        OpenFileDialog1.Filter = "All Pictures|*.bmp;*.gif;*.jpg;*.png|Bitmap|*.bmp|GIF|*.gif|JPEG|*.jpg|PNG|*.png"

        If (OpenFileDialog1.ShowDialog() = DialogResult.OK) Then

            Pic_Carnet_CNAM.Image = New Bitmap(OpenFileDialog1.FileName)

        End If

    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click
        Pic_Carnet_CNAM.Image = Nothing
    End Sub

    Private Sub Scan()
        Dim iRetour As Long = 0
        Dim ChemainSauv As String = ""

        Try

            TWAIN_CloseSource()
            TWAIN_LoadSourceManager()
            TWAIN_OpenSourceManager(Me.Handle.ToInt32)
            iRetour = TWAIN_OpenDefaultSource(Me.Handle.ToInt32)
            If iRetour = 0 Then
                iRetour = TWAIN_SelectImageSource(Me.Handle.ToInt32)

            End If
            If iRetour = 1 Then
                iRetour = TWAIN_OpenDefaultSource(Me.Handle.ToInt32)
            End If
            If iRetour = 0 Then
                'Exit Sub
            End If

            If TWAIN_State() < 4 Then
                MsgBox("Impossible de paramêtrer le scanner")
                Exit Sub
            Else
                'parametres du scanner
                TWAIN_SetCurrentUnits(0) 'DPI
                TWAIN_SetCurrentResolution(150) 'en points par pouces
                TWAIN_SetCurrentPixelType(2) ' Scan format 0 = B&W, 1 Grey, 2 RGB
                TWAIN_SetBitDepth(8) ' Bit Depth 1, 2, 4, 8 but depends on Pixeltype

                TWAIN_SetHideUI(True)
            End If

            'scan du document
            'iRetour = TWAIN_AcquireToClipboard(Me.Handle.ToInt32, 32)

            For i = 0 To 1
                iRetour = TWAIN_AcquireToClipboard(Me.Handle.ToInt32, 32)
            Next

            'If iRetour = 0 Then
            iRetour = TWAIN_AcquireToClipboard(Me.Handle.ToInt32, 32)
            'MsgBox("Le scan du document a échoué.", MsgBoxStyle.Critical, "Scanner")
            'Exit Sub
            'End If

            TWAIN_CloseSource()
        Catch ex As Exception
            Exit Sub
        End Try

        Try
            'If Not IO.Directory.Exists("C:\DocumentEmploye") Then
            '    IO.Directory.CreateDirectory("C:\DocumentEmploye")
            'End If
            'If File.Exists("C:\DocumentEmploye\Document.jpeg") Then
            '    File.Delete("C:\DocumentEmploye\Doucment.jpeg")
            'End If

            'ChemainSauv = "C:\DocumentEmploye\Document.jpeg"
            Pic_Carnet_CNAM.Image = System.Windows.Forms.Clipboard.GetDataObject.GetData(System.Windows.Forms.DataFormats.Bitmap) 'on la met dans une picture box
            'Pic_Carnet_CNAM.Image.Save(ChemainSauv, System.Drawing.Imaging.ImageFormat.Jpeg)
            Pic_Carnet_CNAM.SizeMode = PictureBoxSizeMode.Zoom

        Catch ex As Exception
            MsgBox("Le document a bien été scanné," & Chr(13) & "mais une erreur s'est produite lors de la sauvegarde dans le dossier du candidat.", MsgBoxStyle.Critical, "Scanner")
        End Try
    End Sub

    Private Sub bScanner_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bScanner.Click
        'Scan()
        '####################
        ChoixScan()
        Scanner()

        '#####################
    End Sub

    Private Sub bCapturer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCapturer.Click
        Dim MyCamera As New fCamera
        MyCamera.ShowDialog()
        If MyCamera.OK = True Then
            Pic_Carnet_CNAM.Image = MyCamera.Image
            Pic_Carnet_CNAM.SizeMode = PictureBoxSizeMode.Zoom 'CenterImage

        End If

    End Sub

    '------------- scan
    Dim THdl As Integer = 0

    Public Sub Scanner()


        Dim Img As Drawing.Image = Nothing
        Try

            Dim Ret As Integer = 0

            ''Paramètre du Scan
            If TWAIN_State() < 4 Then
                'MsgBox("Impossible de paramêtrer le scanner", MsgBoxStyle.Exclamation)
            Else
                'parametres du scanner
                TWAIN_SetCurrentUnits(0) 'DPI
                TWAIN_SetCurrentResolution(600) 'en points par pouces
                TWAIN_SetCurrentPixelType(2) ' Scan format 0 = B&W, 1 Grey, 2 RGB
                TWAIN_SetBitDepth(8) ' Bit Depth 1, 2, 4, 8 but depends on Pixeltype
                TWAIN_SetHideUI(1)
            End If

            'scan du document
            Ret = TWAIN_AcquireToClipboard(THdl, 32)
            Img = CType(Clipboard.GetDataObject.GetData(System.Windows.Forms.DataFormats.Bitmap), Bitmap)

            Pic_Carnet_CNAM.Image = Img 'on la met dans une picture box
            'Pic_Carnet_CNAM.Image.Save(ChemainSauv, System.Drawing.Imaging.ImageFormat.Jpeg)
            Pic_Carnet_CNAM.SizeMode = PictureBoxSizeMode.Zoom
        Catch ex As Exception
            Img = Nothing
        End Try

        'Return Img

    End Sub

    Public Sub ChoixScan()

        Try

            Dim Ret As Integer = 0
            'Fermeture de la source du scan
            TWAIN_CloseSource()
            TWAIN_LoadSourceManager()
            TWAIN_OpenSourceManager(THdl)



            Ret = TWAIN_SelectImageSource(THdl)
            If Ret = 1 Then
                Ret = TWAIN_OpenDefaultSource(THdl)
            End If

            If TWAIN_State() < 4 Then
                'MsgBox("Impossible de paramêtrer le scanner", MsgBoxStyle.Exclamation)
            Else
                'parametres du scanner
                TWAIN_SetCurrentUnits(0) 'DPI
                TWAIN_SetCurrentResolution(150) 'en points par pouces
                TWAIN_SetCurrentPixelType(2) ' Scan format 0 = B&W, 1 Grey, 2 RGB
                TWAIN_SetBitDepth(8) ' Bit Depth 1, 2, 4, 8 but depends on Pixeltype
                TWAIN_SetHideUI(1)
            End If

        Catch ex As Exception
            MsgBox(ex.Message)
        End Try

    End Sub


    Private Sub TrackBar_Scroll(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TrackBar.Scroll
        LabelZoom.Text = "Zoom : " + TrackBar.Value.ToString + " X"
        Pic_Carnet_CNAM.Height = Y_initial + (100 * TrackBar.Value)
        Pic_Carnet_CNAM.Width = X_initial + (100 * TrackBar.Value)
    End Sub


    Private Sub bZoom_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bZoom.Click
        Dim myfZoomImage As New fZoomImage
        myfZoomImage.PictureBox_ZOOM.Image = Pic_Carnet_CNAM.Image
        myfZoomImage.ShowDialog()
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click

        Dim CondCrystal As String = ""

        CondCrystal = " 1=1 AND {CLIENT.CodeClient} = '" + tCodeClient.Text + "' "



        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Carnet CNAM" Then
                num = I
            End If
        Next
        CR.FileName = Application.StartupPath + "\EtatCarnetCNAM.rpt"


        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo


        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent

        CR.RecordSelectionFormula = CondCrystal
        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression Carnet CNAM"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If

        Me.Close()
    End Sub
    Public Sub CLOSEFICHECLIENT()
        Me.Close()
        Me.Dispose()
    End Sub

    Private Sub gVente_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles gVente.Click
        ''AfficherFactureDetail()
    End Sub

    Private Sub gVente_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gVente.KeyUp
        If e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Then
            AfficherFactureDetail()
        End If
    End Sub

    Private Sub bCreerMedecin_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCreerMedecin.Click
        Dim StrSQL As String
        cmbMedecin.OpenCombo()

        fAjouterFiche.Text = "Ajouter Medecin"
        'fAjouterFiche.tLibelle.Value = cmbMedecin.Text
        fAjouterFiche.ShowDialog()

        If fAjouterFiche.Annuler = True Then
            'cmbMedecin.Text = ""
        Else

            '''''''''''''
            dsClient.Tables("MEDECIN").Clear()
            StrSQL = "SELECT DISTINCT CodeMedecin,NomMedecin FROM MEDECIN WHERE Supprimer = 'False' ORDER BY NomMedecin ASC"
            cmdClient.Connection = ConnectionServeur
            cmdClient.CommandText = StrSQL
            daClient = New SqlDataAdapter(cmdClient)
            daClient.Fill(dsClient, "MEDECIN")
            ''''''''''''


            cmbMedecin.Text = fAjouterFiche.tLibelle.Value
            CodeMedecinAjouter = fAjouterFiche.tCodeMedecin.Value
            cmbMedecin.SelectedValue = fAjouterFiche.tCodeMedecin.Value

        End If
        fAjouterFiche.Close()

    End Sub

    Private Sub tRang_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles tRang.KeyDown
        If e.KeyCode = Keys.Enter Then
            Dim SQL As New SqlCommand
            SQL.CommandText = "SELECT COUNT(*) FROM CLIENT_FAMILLE WHERE CodeClient='" + tCodeClient.Text + "' AND CodeLienDeParente=3 AND rang = '" + tRang.Text + "' AND CodeDeFamille <> " + DernierCodeFamille.ToString
            SQL.Connection = ConnectionServeur
            If (SQL.ExecuteScalar > 0) Then
                MsgBox("Rang existe déja dans la famille pour une autre enfant !", MsgBoxStyle.Critical, "Erreur")
                tRang.Focus()
                Exit Sub
            End If
            bConfirmerLien_Click(sender, e)
        End If
    End Sub

    Private Sub tRang_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles tRang.KeyUp
        If e.KeyCode = Keys.Enter Then

        End If
    End Sub

    Private Sub tRang_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tRang.LostFocus

        Dim SQL As New SqlCommand
        If cmbLienDeParente.Text = "ENFANT" Then

            If AjoutModifLien = "A" Then
                SQL.Connection = ConnectionServeur
                SQL.CommandText = " SELECT MAX(CodeDeFamille) FROM CLIENT_FAMILLE WHERE codeclient='" + tCodeClient.Text + "'"
                Try
                    DernierCodeFamille = SQL.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                    DernierCodeFamille = 0
                End Try
                DernierCodeFamille = DernierCodeFamille + 1
            Else
                DernierCodeFamille = tCodeFamille.Value
            End If


        End If

    End Sub

    Private Sub gVente_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gVente.MouseClick
        AfficherFactureDetail()
    End Sub

    Private Sub bAjouterMedecinAPCI_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(1, "AJOUT_MEDECIN_APCI_FAMILLE_CLIENT") = "False" Then
            Exit Sub
        End If

        If ajoutmodif = "A" Then
            If MsgBox("Vous devez valider l'ajout du client ! Voulez-vous confirmer ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Valider le client") = MsgBoxResult.Yes Then

                bConfirmer_Click(sender, e)

                cmd.CommandText = "SELECT COUNT(*) FROM CLIENT WHERE CodeClient=" + Quote(tCodeClient.Text)
                cmd.Connection = ConnectionServeur
                If cmd.ExecuteScalar = 0 Then
                    Exit Sub
                Else
                    Me.Show()
                    ajoutmodif = "M"
                    CodeClient = tCodeClient.Text
                    Init()
                    tabMedecinAPCI.Show()
                End If
            Else
                Exit Sub
            End If
        End If

        cmbFamille.Text = ""

        cmbApci1.Text = ""
        cmbApci2.Text = ""
        cmbApci3.Text = ""

        cmbMedecinApci1.Text = ""
        cmbMedecinApci2.Text = ""
        cmbMedecinApci3.Text = ""

        AjoutModifMedecinApci = "A"

        bModifierMedecinAPCI.Visible = False
        bConfirmerMedecinAPCI.Visible = True
        bAnnulerMedecinAPCI.Visible = True

        grpMedecinApci.Visible = True
    End Sub

    Private Sub tabMedecinAPCI_Enter(ByVal sender As Object, ByVal e As System.EventArgs) Handles tabMedecinAPCI.Enter
        'AfficherMedecinAPCI()
    End Sub

    Private Sub AfficherMedecinAPCI()
        Dim StrSQL As String = ""
        Dim NumeroPremierReglement As Integer = 0
        Dim i As Integer = 0

        If (dsLienFamille.Tables.IndexOf("MEDECIN_APCI_CLIENT_FAMILLE") > -1) Then
            dsLienFamille.Tables("MEDECIN_APCI_CLIENT_FAMILLE").Clear()
        End If

        'chargement des liens familiales

        StrSQL = " SELECT CodeClient," + _
                 " CodeDeFamille," + _
                 " Nom," + _
                 " LibelleLienDeParente," + _
                 " m1.NomMedecin as MedecinApci1,CodeAPCI1,DateValiditeAPCI1," + _
                 " m2.NomMedecin as MedecinApci2,CodeAPCI2,DateValiditeAPCI2," + _
                 " m3.NomMedecin as MedecinApci3,CodeAPCI3,DateValiditeAPCI3" + _
                  " FROM CLIENT_FAMILLE " + _
                 " LEFT OUTER JOIN LIEN_PARENTE ON CLIENT_FAMILLE.CodeLienDeParente=LIEN_PARENTE.CodeLienDeParente " + _
                 " LEFT JOIN MEDECIN M1 ON CLIENT_FAMILLE.CodeMedecinAPCI1 = M1.CodeMedecin " + _
                 " LEFT JOIN MEDECIN M2 ON CLIENT_FAMILLE.CodeMedecinAPCI2 = M2.CodeMedecin " + _
                 " LEFT JOIN MEDECIN M3 ON CLIENT_FAMILLE.CodeMedecinAPCI3 = M3.CodeMedecin " + _
                 " WHERE CLIENT_FAMILLE.CodeClient = " + Quote(tCodeClient.Text) + _
                 " UNION " + _
                 " SELECT CodeClient," + _
                 " 9999 as CodeDeFamille," + _
                 " 'ASSURE' as Nom," + _
                 " 'ASSURE' as LibelleLienDeParente," + _
                 " m1.NomMedecin as MedecinApci1,CodeAPCI1,DateValiditeAPCI1," + _
                 " m2.NomMedecin as MedecinApci2,CodeAPCI2,DateValiditeAPCI2," + _
                 " m3.NomMedecin as MedecinApci3,CodeAPCI3,DateValiditeAPCI3" + _
                  " FROM CLIENT " + _
                 " LEFT JOIN MEDECIN M1 ON CLIENT.CodeMedecinAPCI1 = M1.CodeMedecin " + _
                 " LEFT JOIN MEDECIN M2 ON CLIENT.CodeMedecinAPCI2 = M2.CodeMedecin " + _
                 " LEFT JOIN MEDECIN M3 ON CLIENT.CodeMedecinAPCI3 = M3.CodeMedecin " + _
                 " WHERE CLIENT.CodeClient = " + Quote(tCodeClient.Text)


        cmdLienFamille.Connection = ConnectionServeur
        cmdLienFamille.CommandText = StrSQL
        daLienFamille = New SqlDataAdapter(cmdLienFamille)
        daLienFamille.Fill(dsLienFamille, "MEDECIN_APCI_CLIENT_FAMILLE")
        cbLienFamille = New SqlCommandBuilder(daLienFamille)

        With gMedecinAPCI
            .Columns.Clear()
            Try
                .DataSource = dsLienFamille
            Catch ex As Exception
            End Try
            .DataMember = "MEDECIN_APCI_CLIENT_FAMILLE"
            .Rebind(False)
            .Columns("CodeDeFamille").Caption = "Code Famille   "
            .Columns("Nom").Caption = "Nom"
            .Columns("DateValiditeAPCI1").Caption = "Date Validite APCI (1)"
            .Columns("DateValiditeAPCI2").Caption = "Date Validite APCI (2)"
            .Columns("DateValiditeAPCI3").Caption = "Date Validite APCI (3)"

            .Columns("CodeAPCI1").Caption = "APCI (1)"
            .Columns("CodeAPCI2").Caption = "APCI (2)"
            .Columns("CodeAPCI3").Caption = "APCI (3)"

            .Columns("MedecinApci1").Caption = "Médecin (1)"
            .Columns("MedecinApci2").Caption = "Médecin (2)"
            .Columns("MedecinApci3").Caption = "Médecin (3)"

            .Columns("LibelleLienDeParente").Caption = "Lien"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns(2).Style.HorizontalAlignment = AlignHorzEnum.Near
            'For i = 0 To .Columns.Count - 1
            '    .Splits(0).DisplayColumns(i).Locked = True
            'Next

            .Splits(0).DisplayColumns("Nom").Locked = True
            .Splits(0).DisplayColumns("LibelleLienDeParente").Locked = True

            .Splits(0).DisplayColumns("CodeClient").Width = 0
            .Splits(0).DisplayColumns("CodeClient").Visible = False
            .Splits(0).DisplayColumns("CodeDeFamille").Width = 0
            .Splits(0).DisplayColumns("CodeDeFamille").Visible = False
            .Splits(0).DisplayColumns("Nom").Width = 150
            .Splits(0).DisplayColumns("LibelleLienDeParente").Width = 100

            .Splits(0).DisplayColumns("CodeAPCI1").Width = 50
            .Splits(0).DisplayColumns("CodeAPCI2").Width = 50
            .Splits(0).DisplayColumns("CodeAPCI3").Width = 50

            '.Splits(0).DisplayColumns("DateNaissance").Width = 170
            '.Splits(0).DisplayColumns("DateValidite").Width = 170
            '.Splits(0).DisplayColumns("Rang").Width = 120

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            ParametreGrid(gMedecinAPCI)

            'chargement des APCI
            StrSQL = "SELECT CodeAPCI,NomAPCI FROM APCI ORDER BY CodeAPCI ASC"
            cmdClient.Connection = ConnectionServeur
            cmdClient.CommandText = StrSQL
            daClient = New SqlDataAdapter(cmdClient)
            daClient.Fill(dsClient, "APCI")

            StrSQL = "SELECT CodeMedecin,NomMedecin FROM MEDECIN WHERE supprimer = '0' ORDER BY NomMedecin ASC"
            cmdClient.Connection = ConnectionServeur
            cmdClient.CommandText = StrSQL
            daClient = New SqlDataAdapter(cmdClient)
            daClient.Fill(dsClient, "MEDECIN")

            Me.tdbdMedecin.DataSource = dsClient
            Me.tdbdMedecin.DataMember = "MEDECIN"
            Me.gMedecinAPCI.Columns("MedecinApci1").DropDown = Me.tdbdMedecin
            'gMedecinAPCI.Columns("MedecinApci1").Value = tdbdMedecin.Columns("NomMedecin").Value
            Me.tdbdMedecin.Columns("CodeMedecin").Caption = "Code Medecin"
            Me.tdbdMedecin.Columns("NomMedecin").Caption = "Nom Medecin"
            Me.tdbdMedecin.DisplayMember = "NomMedecin"
            Me.tdbdMedecin.DisplayColumns("CodeMedecin").Width = 0
            Me.tdbdMedecin.DisplayColumns("NomMedecin").Width = 100

            Me.tdbdMedecin.DataSource = dsClient
            Me.tdbdMedecin.DataMember = "MEDECIN"
            Me.gMedecinAPCI.Columns("MedecinApci2").DropDown = Me.tdbdMedecin
            'gMedecinAPCI.Columns("MedecinApci2").Value = tdbdMedecin.Columns("NomMedecin").Value
            Me.tdbdMedecin.Columns("CodeMedecin").Caption = "Code Medecin"
            Me.tdbdMedecin.Columns("NomMedecin").Caption = "Nom Medecin"
            Me.tdbdMedecin.DisplayMember = "NomMedecin"
            Me.tdbdMedecin.DisplayColumns("CodeMedecin").Width = 0
            Me.tdbdMedecin.DisplayColumns("NomMedecin").Width = 100

            Me.tdbdMedecin.DataSource = dsClient
            Me.tdbdMedecin.DataMember = "MEDECIN"
            Me.gMedecinAPCI.Columns("MedecinApci3").DropDown = Me.tdbdMedecin
            'gMedecinAPCI.Columns("MedecinApci3").Value = tdbdMedecin.Columns("NomMedecin").Value
            Me.tdbdMedecin.Columns("CodeMedecin").Caption = "Code Medecin"
            Me.tdbdMedecin.Columns("NomMedecin").Caption = "Nom Medecin"
            Me.tdbdMedecin.DisplayMember = "NomMedecin"
            Me.tdbdMedecin.DisplayColumns("CodeMedecin").Width = 0
            Me.tdbdMedecin.DisplayColumns("NomMedecin").Width = 100

            Me.tdbdAPCI.DataSource = dsClient
            Me.tdbdAPCI.DataMember = "APCI"
            Me.gMedecinAPCI.Columns("CodeAPCI1").DropDown = Me.tdbdAPCI
            'gMedecinAPCI.Columns("CodeAPCI1").Value = tdbdAPCI.Columns("NomAPCI").Value
            Me.tdbdAPCI.Columns("CodeAPCI").Caption = "Code APCI"
            Me.tdbdAPCI.Columns("NomAPCI").Caption = "Nom APCI"
            Me.tdbdAPCI.DisplayMember = "NomAPCI"
            Me.tdbdAPCI.DisplayColumns("CodeAPCI").Width = 0
            Me.tdbdAPCI.DisplayColumns("NomAPCI").Width = 100

            Me.tdbdAPCI.DataSource = dsClient
            Me.tdbdAPCI.DataMember = "APCI"
            Me.gMedecinAPCI.Columns("CodeAPCI2").DropDown = Me.tdbdAPCI
            'gMedecinAPCI.Columns("CodeAPCI2").Value = tdbdAPCI.Columns("NomAPCI").Value
            Me.tdbdAPCI.Columns("CodeAPCI").Caption = "Code APCI"
            Me.tdbdAPCI.Columns("NomAPCI").Caption = "Nom APCI"
            Me.tdbdAPCI.DisplayMember = "NomAPCI"
            Me.tdbdAPCI.DisplayColumns("CodeAPCI").Width = 0
            Me.tdbdAPCI.DisplayColumns("NomAPCI").Width = 100

            Me.tdbdAPCI.DataSource = dsClient
            Me.tdbdAPCI.DataMember = "APCI"
            Me.gMedecinAPCI.Columns("CodeAPCI3").DropDown = Me.tdbdAPCI
            'gMedecinAPCI.Columns("CodeAPCI3").Value = tdbdAPCI.Columns("NomAPCI").Value
            Me.tdbdAPCI.Columns("CodeAPCI").Caption = "Code APCI"
            Me.tdbdAPCI.Columns("NomAPCI").Caption = "Nom APCI"
            Me.tdbdAPCI.DisplayMember = "NomAPCI"
            Me.tdbdAPCI.DisplayColumns("CodeAPCI").Width = 0
            Me.tdbdAPCI.DisplayColumns("NomAPCI").Width = 100
            'Dim Col As New C1.Win.C1TrueDBGrid.C1DataColumn
            'Dim dc As C1.Win.C1TrueDBGrid.C1DisplayColumn

            'With gFamille
            '    .Columns.Insert(0, Col)
            '    Col.Caption = "Lien"
            '    dc = .Splits(0).DisplayColumns.Item("Lien")
            '    dc.Width = 120
            '    .Splits(0).DisplayColumns(7).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            '    .Splits(0).DisplayColumns(7).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            '    dc.Visible = True
            '    .Rebind(True)
            'End With

        End With
    End Sub

    Private Sub bModifierMedecinAPCI_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifierMedecinAPCI.Click
        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(1, "MODIFICATION_MEDECIN_APCI_FAMILLE_CLIENT") = "False" Then
            Exit Sub
        End If

        If gMedecinAPCI.RowCount = 0 Then
            MsgBox("Liste vide !", MsgBoxStyle.OkOnly)
            Exit Sub
        End If

        cmbFamille.Text = gMedecinAPCI(gMedecinAPCI.Row, "Nom")
        CodeFamilleModifMedecin = gMedecinAPCI(gMedecinAPCI.Row, "CodeDeFamille")

        cmbMedecinApci1.Text = gMedecinAPCI(gMedecinAPCI.Row, "MedecinApci1").ToString
        cmbMedecinApci2.Text = gMedecinAPCI(gMedecinAPCI.Row, "MedecinApci2").ToString
        cmbMedecinApci3.Text = gMedecinAPCI(gMedecinAPCI.Row, "MedecinApci3").ToString

        cmbApci1.SelectedValue = gMedecinAPCI(gMedecinAPCI.Row, "CodeAPCI1")
        cmbApci2.SelectedValue = gMedecinAPCI(gMedecinAPCI.Row, "CodeAPCI2")
        cmbApci3.SelectedValue = gMedecinAPCI(gMedecinAPCI.Row, "CodeAPCI3")

        cmbDateValiApci1.Value = gMedecinAPCI(gMedecinAPCI.Row, "DateValiditeAPCI1")
        cmbDateValiApci2.Value = gMedecinAPCI(gMedecinAPCI.Row, "DateValiditeAPCI2")
        cmbDateValiApci3.Value = gMedecinAPCI(gMedecinAPCI.Row, "DateValiditeAPCI3")

        AjoutModifMedecinApci = "M"

        grpMedecinApci.Visible = True
        cmbMedecinApci1.Focus()

    End Sub

    Private Sub bConfirmerMedecinAPCI_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmerMedecinAPCI.Click
        Dim StrSQL As String = ""
        Dim Sql As New SqlCommand

        If grpMedecinApci.Visible = False Then Exit Sub

        Dim M1, M2, M3, A1, A2, A3, D1, D2, D3 As String
        If cmbMedecinApci1.Text = "" Then
            M1 = "NULL"
        Else
            M1 = cmbMedecinApci1.SelectedValue
        End If
        If cmbMedecinApci2.Text = "" Then
            M2 = "NULL"
        Else
            M2 = cmbMedecinApci2.SelectedValue
        End If
        If cmbMedecinApci3.Text = "" Then
            M3 = "NULL"
        Else
            M3 = cmbMedecinApci3.SelectedValue
        End If
        If cmbApci1.Text = "" Then
            A1 = "NULL"
        Else
            A1 = cmbApci1.SelectedValue
        End If
        If cmbApci2.Text = "" Then
            A2 = "NULL"
        Else
            A2 = cmbApci2.SelectedValue
        End If
        If cmbApci3.Text = "" Then
            A3 = "NULL"
        Else
            A3 = cmbApci3.SelectedValue
        End If

        If cmbDateValiApci1.Text = "" Then
            D1 = "NULL"
        Else
            D1 = Quote(cmbDateValiApci1.Text)
        End If

        If cmbDateValiApci2.Text = "" Then
            D2 = "NULL"
        Else
            D2 = Quote(cmbDateValiApci2.Text)
        End If

        If cmbDateValiApci3.Text = "" Then
            D3 = "NULL"
        Else
            D3 = Quote(cmbDateValiApci3.Text)
        End If

        If cmbFamille.SelectedValue = 9999 Then
            StrSQL = "UPDATE CLIENT SET " + _
                        " CodeMedecinAPCI1 = " + M1.ToString + _
                        ",CodeMedecinAPCI2 = " + M2.ToString + _
                        ",CodeMedecinAPCI3 = " + M3.ToString + _
                        ",CodeAPCI1 = " + A1.ToString + _
                        ",CodeAPCI2 = " + A2.ToString + _
                        ",CodeAPCI3 = " + A3.ToString + _
                        ",DateValiditeAPCI1 = " + D1 + _
                        ",DateValiditeAPCI2 = " + D2 + _
                        ",DateValiditeAPCI3 = " + D3 + _
                        " WHERE CodeClient = " + Quote(tCodeClient.Text)
        Else

            StrSQL = "UPDATE CLIENT_FAMILLE SET " + _
                        " CodeMedecinAPCI1 = " + M1.ToString + _
                        ",CodeMedecinAPCI2 = " + M2.ToString + _
                        ",CodeMedecinAPCI3 = " + M3.ToString + _
                        ",CodeAPCI1 = " + A1.ToString + _
                        ",CodeAPCI2 = " + A2.ToString + _
                        ",CodeAPCI3 = " + A3.ToString + _
                        ",DateValiditeAPCI1 = " + D1 + _
                        ",DateValiditeAPCI2 = " + D2 + _
                        ",DateValiditeAPCI3 = " + D3 + _
                        " WHERE CodeClient = " + Quote(tCodeClient.Text) + " AND CodeDeFamille = " + CodeFamilleModifMedecin

        End If
        Sql.Connection = ConnectionServeur
        Sql.CommandText = StrSQL

        Try
            Sql.ExecuteNonQuery()
            AfficherMedecinAPCI()
            grpMedecinApci.Visible = False
        Catch ex As Exception
            'Console.WriteLine(ex.Message)
            MsgBox("Erreur de modification", MsgBoxStyle.Critical)
        End Try

    End Sub

    Private Sub bAnnulerMedecinAPCI_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bAnnulerMedecinAPCI.Click
        grpMedecinApci.Visible = False
    End Sub

    Private Sub gMedecinAPCI_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gMedecinAPCI.KeyUp
        If e.KeyCode = Keys.Up Or e.KeyCode = Keys.PageDown Then
            If grpMedecinApci.Visible = True Then
                If MsgBox("Voulez vous enregistrer les modifications effectuées ?", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                    bConfirmerMedecinAPCI_Click(sender, e)
                End If
                grpMedecinApci.Visible = False
            End If
        End If
    End Sub

    Private Sub cmbApci1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbApci1.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbApci1.Text = cmbApci1.WillChangeToText
            cmbDateValiApci1.Focus()
        Else
            cmbApci1.OpenCombo()
        End If
    End Sub

    Private Sub cmbApci2_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbApci2.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbApci2.Text = cmbApci2.WillChangeToText
            cmbDateValiApci2.Focus()
        Else
            cmbApci2.OpenCombo()
        End If
    End Sub

    Private Sub cmbApci3_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbApci3.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbApci3.Text = cmbApci3.WillChangeToText
            cmbDateValiApci3.Focus()
        Else
            cmbApci3.OpenCombo()
        End If
    End Sub

    Private Sub cmbDateValiApci1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbDateValiApci1.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbMedecinApci2.Focus()
        End If
    End Sub

    Private Sub cmbDateValiApci2_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbDateValiApci2.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbMedecinApci3.Focus()
        End If
    End Sub

    Private Sub cmbDateValiApci3_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbDateValiApci3.KeyUp
        If e.KeyCode = Keys.Enter Then
            bConfirmerMedecinAPCI.Focus()
        End If
    End Sub

    Private Sub gMedecinAPCI_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gMedecinAPCI.MouseClick
        If grpMedecinApci.Visible = True Then
            If MsgBox("Voulez vous enregistrer les modifications effectuées ?", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                bConfirmerMedecinAPCI_Click(sender, e)
            End If
            grpMedecinApci.Visible = False
        End If
    End Sub

    Private Sub cmbMedecinApci1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMedecinApci1.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbMedecinApci1.Text = cmbMedecinApci1.WillChangeToText
            cmbApci1.Focus()
        Else
            cmbMedecinApci1.OpenCombo()
        End If
    End Sub

    Private Sub cmbMedecinApci2_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMedecinApci2.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbMedecinApci2.Text = cmbMedecinApci2.WillChangeToText
            cmbApci2.Focus()
        Else
            cmbMedecinApci2.OpenCombo()
        End If
    End Sub

    Private Sub cmbMedecinApci3_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMedecinApci3.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbMedecinApci3.Text = cmbMedecinApci3.WillChangeToText
            cmbApci3.Focus()
        Else
            cmbMedecinApci3.OpenCombo()
        End If
    End Sub

    Private Sub gVentes_DoubleClick(sender As System.Object, e As System.EventArgs) Handles gVentes.DoubleClick
        Dim MyVenteAffiche As New fVenteJusteAffichage
        MyVenteAffiche.NumeroVente = gVentes(gVentes.Row, "NumeroVente")
        MyVenteAffiche.ShowDialog()
        MyVenteAffiche.Close()
        MyVenteAffiche.Dispose()
    End Sub

    Private Sub bFamille_Click(sender As System.Object, e As System.EventArgs) Handles bFamille.Click
        AfficherFamille()
        tabFamille.Show()
    End Sub

    Private Sub grpMedecinApci_Enter(sender As System.Object, e As System.EventArgs)

    End Sub

    Private Sub bAjouterFacture_Click(sender As System.Object, e As System.EventArgs) Handles bAjouterFacture.Click
        Dim EtatFacturationClient As New fEtatFacturationClient
        EtatFacturationClient.AjoutModif = "A"
        EtatFacturationClient.CodeClient = tCodeClient.Text

        EtatFacturationClient.lRemise.Text = "0.000"
        EtatFacturationClient.NomClient = tNomClient.Text

        EtatFacturationClient.init()
        EtatFacturationClient.bImprimer.Visible = False
        EtatFacturationClient.ShowDialog()

        EtatFacturationClient.Dispose()
        EtatFacturationClient.Close()

        AfficherFactures()
        AfficherReglement(0)

        AfficherSituationClient(tSoldeInitial.Text)
    End Sub

    Private Sub bVisualiserFacture_Click(sender As System.Object, e As System.EventArgs) Handles bVisualiserFacture.Click
        Dim EtatFacturationClient As New fEtatFacturationClient
        EtatFacturationClient.AjoutModif = "M"
        If gFacture.Columns("DateDebut").Value.ToString = "" Then
            Exit Sub
        End If
        EtatFacturationClient.DateDebut = gFacture.Columns("DateDebut").Value
        EtatFacturationClient.DateFin = gFacture.Columns("DateFin").Value
        EtatFacturationClient.CodeClient = tCodeClient.Text
        EtatFacturationClient.lRemise.Text = gFacture.Columns("TotalRemise").Value.ToString()
        EtatFacturationClient.NomClient = tNomClient.Text
        EtatFacturationClient.MatriculeFiscale = tMatriculeFiscale.Text
        EtatFacturationClient.NumeroFacture = gFacture.Columns("Numero").Value.ToString()

        EtatFacturationClient.dtpDebut.Enabled = False
        EtatFacturationClient.dtpFin.Enabled = False
        EtatFacturationClient.chbRetourUniquement.Enabled = False
        EtatFacturationClient.bImprimer.Visible = True

        EtatFacturationClient.init()
        EtatFacturationClient.ShowDialog()

        EtatFacturationClient.Dispose()
        EtatFacturationClient.Close()

        AfficherReglement(0)

        AfficherSituationClient(tSoldeInitial.Text)
    End Sub

    Private Sub bSupprimerFacture_Click(sender As System.Object, e As System.EventArgs) Handles bSupprimerFacture.Click
        If ModeADMIN <> "ADMIN" Then
            MsgBox("Vous n'avez pas le droit d'executer cette fonction")
            Exit Sub
        End If

        Dim Sql As New SqlCommand

        Sql.Connection = ConnectionServeur
        Sql.CommandText = "DELETE FROM REGLEMENT_CLIENT WHERE NumeroFacture = " & Quote(gFacture(gFacture.Row, "Numero").ToString())
        Try
            Sql.ExecuteNonQuery()
        Catch
        End Try

        Sql.Connection = ConnectionServeur
        Sql.CommandText = "UPDATE VENTE SET IDFacturationClient = '' WHERE IDFacturationClient = " & Quote(gFacture(gFacture.Row, "Numero").ToString())
        Try
            Sql.ExecuteNonQuery()
        Catch
        End Try

        Sql.Connection = ConnectionServeur
        Sql.CommandText = "DELETE FROM FACTURATION_CLIENT WHERE Id = " & Quote(gFacture(gFacture.Row, "Numero").ToString())
        Try
            Sql.ExecuteNonQuery()
        Catch
        End Try

        MsgBox("Facture supprimé !", MsgBoxStyle.Information, "Information")
        AfficherFactures()

    End Sub




    Private Sub C1DockingTabPage3_Click_1(sender As System.Object, e As System.EventArgs) Handles C1DockingTabPage3.Click

    End Sub

    Private Sub bReglement_Click(sender As System.Object, e As System.EventArgs) Handles bReglement.Click
        Dim MyReglement As New fReglementFactureClient
        'MyReglement.SoldeClient = CDbl(Val(lSoldeClient.Text))
        MyReglement.ajoutmodif = "A"
        MyReglement.CodeClient = CodeClient
        MyReglement.init()
        MyReglement.ShowDialog()
        AfficherSituationClient(tSoldeInitial.Text)
        MyReglement.Dispose()
        MyReglement.Close()
    End Sub


    Private Sub Tab_Click(sender As System.Object, e As System.EventArgs) Handles Tab.Click
        AfficherReglement(0)
    End Sub

    Private Sub AfficherInformationPatient()
        Dim I As Integer
        Try
            dsFacture.Tables("INFORMATION_PATIENT").Clear()
        Catch
        End Try
        cmdFacture.CommandText = "  SELECT " + _
                                 "      FICHE_PATIENT.Id, " + _
                                 "      FICHE_PATIENT.CodeClient, " + _
                                 "      FICHE_PATIENT.Date,  " + _
                                 "      FICHE_PATIENT.Poid, " + _
                                 "      FICHE_PATIENT.Taille, " + _
                                 "      FICHE_PATIENT.Tention AS Tension, " + _
                                 "      FICHE_PATIENT.Glycemie, " + _
                                 "      FICHE_PATIENT.Temperature, " + _
                                 "      FICHE_PATIENT.Pathologie, " + _
                                 "      FICHE_PATIENT.Allergie " + _
                                 "  FROM FICHE_PATIENT " + _
                                 "  WHERE FICHE_PATIENT.CodeClient = " + Quote(CodeClient) + _
                                 "  ORDER BY FICHE_PATIENT.Id DESC"

        cmdFacture.Connection = ConnectionServeur
        daFacture = New SqlDataAdapter(cmdFacture)
        daFacture.Fill(dsFacture, "INFORMATION_PATIENT")

        With gInformationPatient
            .Columns.Clear()
            .DataSource = dsFacture
            .DataMember = "INFORMATION_PATIENT"
            .Rebind(False)
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Id").Visible = False
            .Splits(0).DisplayColumns("CodeClient").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gFacture)
        End With

    End Sub

    Private Sub tabInformationPatient_Click(sender As System.Object, e As System.EventArgs) Handles tabInformationPatient.Click
        AfficherInformationPatient()
    End Sub


    Private Sub bAjouterInformation_Click(sender As System.Object, e As System.EventArgs) Handles bAjouterInformation.Click

        Try
            cmdFacture.Connection = ConnectionServeur
            cmdFacture.CommandText = "INSERT INTO FICHE_PATIENT (CodeClient ,Date ,Poid ,Taille ,Tention ,Glycemie ,Temperature ,Pathologie ,Allergie) " & _
                                    "VALUES " & _
                                    "   (" & Quote(tCodeClient.Text) & ", " & _
                                    "   " & Quote(dtpDatePrise.Text) & "," & _
                                    "   " & Quote(tPoid.Text) & "," & _
                                    "   " & Quote(tTaille.Text) & "," & _
                                    "   " & Quote(tTention.Text) & "," & _
                                    "   " & Quote(tGlycemie.Text) & "," & _
                                    "   " & Quote(tTemperature.Text) & "," & _
                                    "   " & Quote(tPathologies.Text) & "," & _
                                    "   " & Quote(tAllergies.Text) & ")"

            cmdFacture.ExecuteNonQuery()

        Catch
        End Try

        AfficherInformationPatient()
    End Sub

    Private Sub tSupprimerInformation_Click(sender As System.Object, e As System.EventArgs) Handles tSupprimerInformation.Click
        Try

            cmdFacture.Connection = ConnectionServeur
            cmdFacture.CommandText = "DELETE FROM FICHE_PATIENT WHERE Id = " & Quote(gInformationPatient(gInformationPatient.Row, "Id"))
            cmdFacture.ExecuteNonQuery()

        Catch
        End Try

        AfficherInformationPatient()
    End Sub

    Private Sub bAjouterPathologie_Click(sender As System.Object, e As System.EventArgs) Handles bAjouterPathologie.Click
        tPathologies.Text += cmbPathologie.Text + ", "
    End Sub

    Private Sub tPathologie_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles tPathologie.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbPathologie.Focus()
        End If
    End Sub

    Private Sub tPathologie_Validated(sender As Object, e As System.EventArgs) Handles tPathologie.Validated
        Dim listProduit As New List(Of Integer)

        Dim BCBDextherEtrClient As New ServiceBCB.BCBDextherEtrClient()
        Dim Cle As String = generateKey("NEXT", "")

        Dim bcbSecurity As ServiceBCB.bcbSecurity = New ServiceBCB.bcbSecurity
        bcbSecurity.codeEditeur = "NEXT"
        bcbSecurity.idPS = ""
        bcbSecurity.secretEditeur = Cle

        Dim PathologieList = BCBDextherEtrClient.rechercheBCB(tPathologie.Text, 16384, 0, bcbSecurity)

        Dim Index As Integer = 0
        cmbPathologie.Items.Clear()

        If PathologieList.listePathologies IsNot Nothing Then
            For Each item In PathologieList.listePathologies
                cmbPathologie.Items.Insert(Index, item.libelle)
                Index += 1
            Next
        End If
    End Sub

    Private Sub tAllergie_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles tAllergie.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbAllergie.Focus()
        End If
    End Sub

    Private Sub tAllergie_MouseUp(sender As Object, e As System.Windows.Forms.MouseEventArgs) Handles tAllergie.MouseUp

    End Sub

    Private Sub tAllergie_Validated(sender As Object, e As System.EventArgs) Handles tAllergie.Validated
        Dim listProduit As New List(Of Integer)

        Dim BCBDextherEtrClient As New ServiceBCB.BCBDextherEtrClient()
        Dim Cle As String = generateKey("NEXT", "")

        Dim bcbSecurity As ServiceBCB.bcbSecurity = New ServiceBCB.bcbSecurity
        bcbSecurity.codeEditeur = "NEXT"
        bcbSecurity.idPS = ""
        bcbSecurity.secretEditeur = Cle

        Dim AllergieList = BCBDextherEtrClient.rechercheBCB(tAllergie.Text, 4096, 0, bcbSecurity)

        Dim Index As Integer = 0
        cmbAllergie.Items.Clear()

        If AllergieList.listeComposants IsNot Nothing Then
            For Each item In AllergieList.listeComposants
                cmbAllergie.Items.Insert(Index, item.libelle)
                Index += 1
            Next
        End If
    End Sub

    Private Sub bAjouterAllergie_Click(sender As System.Object, e As System.EventArgs) Handles bAjouterAllergie.Click
        tAllergies.Text += cmbAllergie.Text + ", "
    End Sub

    Private Sub tabFamille_Enter(sender As System.Object, e As System.EventArgs) Handles tabFamille.Enter
        AfficherFamille()
    End Sub

    Private Sub tCleIdCNAM_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles tCleIdCNAM.KeyDown

    End Sub

    Private Sub tCleIdCNAM_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles tCleIdCNAM.KeyUp
        If e.KeyCode = Keys.Enter Then
            tDateNaissance.Focus()
        End If
    End Sub

    Private Sub tCleIdCNAM_LostFocus(sender As Object, e As System.EventArgs) Handles tCleIdCNAM.LostFocus
        If tIdCNAM.Text <> "" And tCleIdCNAM.Text = "" Then
            MsgBox("Code erroné.", MsgBoxStyle.Information, "")
            tCleIdCNAM.Focus()
        Else
            'tCleIdCNAM.Text = tCleIdCNAM.Text.PadLeft(2, "0")
        End If
    End Sub

    Private Sub bCodeABarre_Click(sender As Object, e As EventArgs) Handles bCodeABarre.Click
        Dim cmdCodeABarre As New SqlCommand
        Dim daCodeABarre As New SqlDataAdapter
        Dim dsCodeABarre As New DataSet
        Dim cbCodeABarre As New SqlCommandBuilder

        Dim LineWrite As String = ""
        Dim TexteEtiquette As String = ""
        Dim NomSysteme As String = ""

        Dim J As Integer = 1
        Dim nbCol As Integer = 0
        Dim dr As DataRow

        cmdCodeABarre.Connection = ConnectionServeur
        cmdCodeABarre.CommandText = "UPDATE PARAMETRE_PHARMACIE SET ImageCodeABarre=NULL"
        cmdCodeABarre.ExecuteNonQuery()

        cmdCodeABarre.CommandText = "SELECT * FROM PARAMETRE_PHARMACIE"
        daCodeABarre = New SqlDataAdapter(cmdCodeABarre)
        daCodeABarre.Fill(dsCodeABarre, "IMAGE")
        cbCodeABarre = New SqlCommandBuilder(daCodeABarre)

        BarcodeProfessional.Code = tCodeClient.Text
        BarcodeProfessional.Text = ""
        BarcodeProfessional.Symbology = Neodynamic.WinControls.BarcodeProfessional.Symbology.Code128
        BarcodeProfessional.BarHeight = 1.0F
        BarcodeProfessional.BarWidth = 0.04F

        With dsCodeABarre.Tables("IMAGE")
            dr = .Rows(0)
            dr.Item("ImageCodeABarre") = ImageToByteArray(BarcodeProfessional.GetBarcodeImage())
        End With

        Try
            daCodeABarre.Update(dsCodeABarre, "IMAGE")

        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End Try

        If NomOrdinateurImpressionCodeABarre <> "" Then

            CR.FileName = Application.StartupPath + "\EtatImpressionCodeABarre.rpt"

            CR.SetParameterValue("Designation", "")
            CR.SetParameterValue("PrixVenteTTC", "")

            Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent

            CR.PrintOptions.PrinterName = NomOrdinateurImpressionCodeABarre
            CR.PrintToPrinter(1, False, 1, 1)
        End If
    End Sub

    Private Sub cmbSituationClient_TextChanged_1(sender As Object, e As EventArgs) Handles cmbSituationClient.TextChanged

    End Sub

    Private Sub Label10_Click(sender As Object, e As EventArgs) Handles Label10.Click

    End Sub

    Private Sub gReglements_MouseClick(sender As Object, e As EventArgs) Handles gReglements.Click

    End Sub



    Private Function ControleDAccesClient(ByVal CodeModule, ByVal OptionATester)
        '********************************* Contrôle de l accée *******************************************
        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ModeADMIN = "ADMIN" Then
            Return "True"
            Exit Function
        End If

        cmd.Connection = ConnectionServeur
        cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE '" + OptionATester + "' AND CodeUtilisateur='" + CodeUtilisateur + "'"

        Try
            If cmd.ExecuteScalar() < 1 Then
                Return "False"
                Exit Function
            Else
                Return "True"
                Exit Function
            End If
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
    End Function

    Private Sub tMatriculeFiscale_KeyUp(sender As Object, e As KeyEventArgs) Handles tMatriculeFiscale.KeyUp
        If e.KeyCode = Keys.Enter Then
            tRemarqueClient.Focus()
        End If
    End Sub
End Class