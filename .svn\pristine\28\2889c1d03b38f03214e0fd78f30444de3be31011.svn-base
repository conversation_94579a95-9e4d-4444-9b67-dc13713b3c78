﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fListeDesInventaires
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fListeDesInventaires))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.bOK = New C1.Win.C1Input.C1Button()
        Me.GroupBox5 = New System.Windows.Forms.GroupBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.gDetailsInventaires = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.gInventaires = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Panel.SuspendLayout()
        Me.GroupBox5.SuspendLayout()
        CType(Me.gDetailsInventaires, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gInventaires, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.bOK)
        Me.Panel.Controls.Add(Me.GroupBox5)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(889, 537)
        Me.Panel.TabIndex = 6
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(769, 479)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(107, 45)
        Me.bAnnuler.TabIndex = 65
        Me.bAnnuler.Text = "Annuler"
        Me.bAnnuler.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bOK
        '
        Me.bOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bOK.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bOK.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bOK.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bOK.Location = New System.Drawing.Point(653, 479)
        Me.bOK.Name = "bOK"
        Me.bOK.Size = New System.Drawing.Size(107, 45)
        Me.bOK.TabIndex = 64
        Me.bOK.Text = "OK"
        Me.bOK.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bOK.UseVisualStyleBackColor = True
        Me.bOK.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox5
        '
        Me.GroupBox5.Controls.Add(Me.Label1)
        Me.GroupBox5.Controls.Add(Me.gDetailsInventaires)
        Me.GroupBox5.Controls.Add(Me.gInventaires)
        Me.GroupBox5.Location = New System.Drawing.Point(7, 11)
        Me.GroupBox5.Name = "GroupBox5"
        Me.GroupBox5.Size = New System.Drawing.Size(869, 462)
        Me.GroupBox5.TabIndex = 1
        Me.GroupBox5.TabStop = False
        Me.GroupBox5.Text = "Liste des Inventaires"
        '
        'Label1
        '
        Me.Label1.Location = New System.Drawing.Point(6, 204)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(100, 13)
        Me.Label1.TabIndex = 3
        Me.Label1.Text = "Détails"
        '
        'gDetailsInventaires
        '
        Me.gDetailsInventaires.GroupByCaption = "Drag a column header here to group by that column"
        Me.gDetailsInventaires.Images.Add(CType(resources.GetObject("gDetailsInventaires.Images"), System.Drawing.Image))
        Me.gDetailsInventaires.Location = New System.Drawing.Point(7, 220)
        Me.gDetailsInventaires.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gDetailsInventaires.Name = "gDetailsInventaires"
        Me.gDetailsInventaires.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gDetailsInventaires.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gDetailsInventaires.PreviewInfo.ZoomFactor = 75.0R
        Me.gDetailsInventaires.PrintInfo.PageSettings = CType(resources.GetObject("gDetailsInventaires.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gDetailsInventaires.Size = New System.Drawing.Size(856, 236)
        Me.gDetailsInventaires.TabIndex = 2
        Me.gDetailsInventaires.Text = "C1TrueDBGrid1"
        Me.gDetailsInventaires.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gDetailsInventaires.PropBag = resources.GetString("gDetailsInventaires.PropBag")
        '
        'gInventaires
        '
        Me.gInventaires.GroupByCaption = "Drag a column header here to group by that column"
        Me.gInventaires.Images.Add(CType(resources.GetObject("gInventaires.Images"), System.Drawing.Image))
        Me.gInventaires.Location = New System.Drawing.Point(7, 18)
        Me.gInventaires.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gInventaires.Name = "gInventaires"
        Me.gInventaires.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gInventaires.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gInventaires.PreviewInfo.ZoomFactor = 75.0R
        Me.gInventaires.PrintInfo.PageSettings = CType(resources.GetObject("gInventaires.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gInventaires.Size = New System.Drawing.Size(856, 183)
        Me.gInventaires.TabIndex = 0
        Me.gInventaires.Text = "C1TrueDBGrid1"
        Me.gInventaires.PropBag = resources.GetString("gInventaires.PropBag")
        '
        'fListeDesInventaires
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(889, 537)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fListeDesInventaires"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        Me.GroupBox5.ResumeLayout(False)
        CType(Me.gDetailsInventaires, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gInventaires, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bOK As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox5 As System.Windows.Forms.GroupBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents gDetailsInventaires As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents gInventaires As C1.Win.C1TrueDBGrid.C1TrueDBGrid
End Class
