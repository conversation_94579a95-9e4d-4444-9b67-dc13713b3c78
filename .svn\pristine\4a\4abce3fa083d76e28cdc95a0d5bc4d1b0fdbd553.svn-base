﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Public Class fCorrectionDesLots

    Dim cmdLotsArticle As New SqlCommand
    Dim daLotsArticle As New SqlDataAdapter
    Dim cbLotsArticle As New SqlCommandBuilder
    Dim dsLotsArticle As New DataSet

    Dim xLotsArticle As Integer
    Dim StrSQL As String = ""

    Dim ValeurDuStock As Integer = 0

    Public Sub init()
        'chargement des Articles
        StrSQL = "SELECT CodeArticle,Designation FROM ARTICLE ORDER BY Designation ASC"
        cmdLotsArticle.Connection = ConnectionServeur
        cmdLotsArticle.CommandText = StrSQL
        daLotsArticle = New SqlDataAdapter(cmdLotsArticle)
        daLotsArticle.Fill(dsLotsArticle, "ARTICLE")
        cmbDesignation.DataSource = dsLotsArticle.Tables("ARTICLE")
        cmbDesignation.ValueMember = "CodeArticle"
        cmbDesignation.DisplayMember = "Designation"
        cmbDesignation.ColumnHeaders = False
        cmbDesignation.Splits(0).DisplayColumns("CodeArticle").Visible = False
        cmbDesignation.Splits(0).DisplayColumns("Designation").Width = 10
        cmbDesignation.ExtendRightColumn = True

        AfficherLotsArticle()

    End Sub

    Public Sub AfficherLotsArticle()
        ValeurDuStock = 0
        Dim I As Integer
        Dim Cond As String = " 1=1 "

        If cmbDesignation.Text <> "" Then
            Cond = Cond + " AND ARTICLE.CodeArticle='" + cmbDesignation.SelectedValue + "'"
        End If
        If tCodeArticle.Text <> "" Then
            Cond = Cond + " AND ARTICLE.CodeArticle='" + tCodeArticle.Text + "'"
        End If
        If tCodePCT.Text <> "" Then
            Cond = Cond + " AND ARTICLE.CODEPCT=" + tCodePCT.Text
        End If

        Try
            dsLotsArticle.Tables("LOT_ARTICLE").Clear()
        Catch ex As Exception
        End Try

        cmdLotsArticle.CommandText = " SELECT " + _
                                    " NumeroLotArticle, " + _
                                    " LOT_ARTICLE.CodeArticle, " + _
                                    " CodePCT, " + _
                                    " Designation, " + _
                                    " QteLotArticle, " + _
                                    " DatePeremptionArticle " + _
                                    " FROM LOT_ARTICLE,ARTICLE where " + _
                                    " LOT_ARTICLE.CodeArticle=ARTICLE.CodeArticle AND " + Cond + _
                                    " ORDER BY CodeForme"

        cmdLotsArticle.Connection = ConnectionServeur
        daLotsArticle = New SqlDataAdapter(cmdLotsArticle)
        daLotsArticle.Fill(dsLotsArticle, "LOT_ARTICLE")
        cbLotsArticle = New SqlCommandBuilder(daLotsArticle)

        With gLots
            .Columns.Clear()
            .DataSource = dsLotsArticle
            .DataMember = "LOT_ARTICLE"
            .Rebind(False)
            .Columns("NumeroLotArticle").Caption = "Num lot"
            .Columns("Designation").Caption = "Désignation"
            .Columns("QteLotArticle").Caption = "Quantité"
            .Columns("DatePeremptionArticle").Caption = "Date Péremption"

            ' Centrer tous 
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
                .Splits(0).DisplayColumns(I).Style.Locked = True
            Next

            If cmbDesignation.Text <> "" Or tCodeArticle.Text <> "" Or tCodePCT.Text <> "" Then
                .Splits(0).DisplayColumns("QteLotArticle").Style.Locked = False
            Else
                .Splits(0).DisplayColumns("QteLotArticle").Style.Locked = True
            End If

            .Splits(0).DisplayColumns("NumeroLotArticle").Width = 50
            .Splits(0).DisplayColumns("Designation").Width = 200
            .Splits(0).DisplayColumns("QteLotArticle").Width = 50
            .Splits(0).DisplayColumns("DatePeremptionArticle").Width = 80

            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodePCT").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodePCT").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gLots)
        End With

        For I = 0 To dsLotsArticle.Tables("LOT_ARTICLE").Rows.Count - 1
            ValeurDuStock = ValeurDuStock + dsLotsArticle.Tables("LOT_ARTICLE").Rows(I).Item("QteLotArticle")
        Next

        gLots.MoveRelative(xLotsArticle)
        cbLotsArticle = New SqlCommandBuilder(daLotsArticle)

    End Sub

    Private Sub cmbDesignation_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbDesignation.LostFocus
        AfficherLotsArticle()
    End Sub

    Private Sub cmbDesignation_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbDesignation.TextChanged
        AfficherLotsArticle()
    End Sub

    Private Sub cmbDesignation_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbDesignation.Validated
        AfficherLotsArticle()
    End Sub

    Private Sub tCodePCT_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodePCT.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherLotsArticle()
        End If
    End Sub

    Private Sub tCodePCT_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodePCT.LostFocus
        AfficherLotsArticle()
    End Sub

    Private Sub tCodePCT_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodePCT.Validated
        AfficherLotsArticle()
    End Sub

    Private Sub tCodeArticle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeArticle.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherLotsArticle()
        End If
    End Sub

    Private Sub tCodeArticle_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeArticle.LostFocus
        AfficherLotsArticle()
    End Sub

    Private Sub tCodeArticle_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeArticle.Validated
        AfficherLotsArticle()
    End Sub

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click
        Dim I As Integer = 0
        Dim ValeurFinal As Integer = 0

        'Dim dr As DataRow
        Dim StrSQL As String = ""

        For I = 0 To dsLotsArticle.Tables("LOT_ARTICLE").Rows.Count - 1
            ValeurFinal = ValeurFinal + dsLotsArticle.Tables("LOT_ARTICLE").Rows(I).Item("QteLotArticle")
        Next

        If ValeurDuStock <> ValeurFinal Then
            MsgBox("La somme des quantité des stocks n'est pas valide !  Valeur initial est : " + ValeurDuStock.ToString, MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If


        StrSQL = " SELECT * FROM LOT_ARTICLE WHERE CodeArticle = " + gLots.Columns("CodeArticle").Value

        cmdLotsArticle.Connection = ConnectionServeur
        cmdLotsArticle.CommandText = StrSQL
        daLotsArticle = New SqlDataAdapter(cmdLotsArticle)
        daLotsArticle.Fill(dsLotsArticle, "LOT_ARTICLE")
        cbLotsArticle = New SqlCommandBuilder(daLotsArticle)

        Try
            daLotsArticle.Update(dsLotsArticle, "LOT_ARTICLE")

        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            AfficherLotsArticle()
            Exit Sub
        End Try

        tCodeArticle.Text = ""
        tCodePCT.Text = ""
        cmbDesignation.Text = ""
        tCodeArticle.Focus()

        AfficherLotsArticle()

    End Sub

    Private Sub tSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tSupprimer.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub
End Class