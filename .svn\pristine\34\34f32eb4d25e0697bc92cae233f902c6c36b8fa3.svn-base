﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="ErrorManagementModel.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2008" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityContainer Name="ErrorManagementModelStoreContainer">
          <EntitySet Name="MESSAGE_ERROR" EntityType="ErrorManagementModel.Store.MESSAGE_ERROR" store:Type="Tables" Schema="dbo" />
        </EntityContainer>
        <EntityType Name="MESSAGE_ERROR">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Module" Type="varchar" MaxLength="500" />
          <Property Name="Date" Type="date" />
          <Property Name="LibellePoste" Type="varchar" MaxLength="500" />
          <Property Name="CodePersonnel" Type="varchar" MaxLength="500" />
          <Property Name="Message" Type="varchar(max)" />
        </EntityType>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="ErrorManagementModel" Alias="Self" p1:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:p1="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="ErrorManagementEntities" p1:LazyLoadingEnabled="true">
          <EntitySet Name="MESSAGE_ERROR" EntityType="ErrorManagementModel.MESSAGE_ERROR" />
        </EntityContainer>
        <EntityType Name="MESSAGE_ERROR">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Module" Type="String" MaxLength="500" Unicode="false" FixedLength="false" />
          <Property Name="Date" Type="DateTime" Precision="0" />
          <Property Name="LibellePoste" Type="String" MaxLength="500" Unicode="false" FixedLength="false" />
          <Property Name="CodePersonnel" Type="String" MaxLength="500" Unicode="false" FixedLength="false" />
          <Property Name="Message" Type="String" MaxLength="Max" Unicode="false" FixedLength="false" />
        </EntityType>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="ErrorManagementModelStoreContainer" CdmEntityContainer="ErrorManagementEntities">
          <EntitySetMapping Name="MESSAGE_ERROR">
            <EntityTypeMapping TypeName="ErrorManagementModel.MESSAGE_ERROR">
              <MappingFragment StoreEntitySet="MESSAGE_ERROR">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Module" ColumnName="Module" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
                <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
                <ScalarProperty Name="Message" ColumnName="Message" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="False" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="True" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="Aucun" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>