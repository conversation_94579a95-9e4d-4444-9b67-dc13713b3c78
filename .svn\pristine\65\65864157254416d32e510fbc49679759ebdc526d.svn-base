﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fReglementPharmacie

    Dim cmdReglement As New SqlCommand
    Dim cbReglement As New SqlCommandBuilder
    Dim dsReglement As New DataSet
    Dim daReglement As New SqlDataAdapter

    Public ajoutmodif As String = ""
    Public CodePharmacie As String = ""
    Public CodeReglement As Integer = 0
    Public Solde As String = ""

    Dim StrSQL As String = ""
    Dim StrSQL1 As String = ""
    Dim i As Integer = 0
    Dim NumeroPremierAchat As String = ""

    Dim x As Integer = 0

    Public Sub init()

        Dim i As Integer = 0
        'chargement des Banque
        StrSQL1 = "SELECT DISTINCT CodeBanque,NomBanque FROM BANQUE ORDER BY NomBanque ASC"
        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL1
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsReglement, "BANQUE")
        cmbBanque.DataSource = dsReglement.Tables("BANQUE")
        cmbBanque.ValueMember = "CodeBanque"
        cmbBanque.DisplayMember = "NomBanque"
        cmbBanque.ColumnHeaders = False
        cmbBanque.Splits(0).DisplayColumns("CodeBanque").Visible = False
        cmbBanque.Splits(0).DisplayColumns("NomBanque").Width = 10
        cmbBanque.ExtendRightColumn = True

        'chargement des Nature reglements
        StrSQL1 = "SELECT DISTINCT CodeNatureReglement,LibelleNatureReglement FROM NATURE_REGLEMENT ORDER BY LibelleNatureReglement ASC"
        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL1
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsReglement, "NATURE_REGLEMENT")
        cmbNature.DataSource = dsReglement.Tables("NATURE_REGLEMENT")
        cmbNature.ValueMember = "CodeNatureReglement"
        cmbNature.DisplayMember = "LibelleNatureReglement"
        cmbNature.ColumnHeaders = False
        cmbNature.Splits(0).DisplayColumns("CodeNatureReglement").Visible = False
        cmbNature.Splits(0).DisplayColumns("LibelleNatureReglement").Width = 10
        cmbNature.ExtendRightColumn = True

        If (dsReglement.Tables.IndexOf("ACHAT_PHARMACIE") > -1) Then
            dsReglement.Tables("ACHAT_PHARMACIE").Clear()
        End If

        lSolde.Text = Solde
       

        tNomInscrit.Enabled = True
        tMontant.Enabled = True
        'chbEncaisse.Enabled = True
        dtEcheance.Enabled = True
        tNumeroCheque.Enabled = True
        tLibelle.Enabled = True
        cmbNature.Enabled = True
        cmbBanque.Enabled = True

        If ajoutmodif = "A" Then

            tNomInscrit.Value = ""
            tMontant.Value = "0.000"
            chbEncaisse.Checked = False
            dtEcheance.Value = System.DateTime.Today
            tNumeroCheque.Value = ""
            tLibelle.Value = ""
            cmbNature.Text = ""
            cmbBanque.Text = ""
            cmbNature.Focus()

        ElseIf ajoutmodif = "M" Then

            tNomInscrit.Value = RecupererValeurExecuteScalaire("NomInscritSurLeCheque", "REGLEMENT_PHARMACIE", "NumeroReglementPharmacie", CodeReglement)
            tMontant.Value = RecupererValeurExecuteScalaire("Montant", "REGLEMENT_PHARMACIE", "NumeroReglementPharmacie", CodeReglement)
            chbEncaisse.Checked = False
            dtEcheance.Value = RecupererValeurExecuteScalaire("DateEcheance", "REGLEMENT_PHARMACIE", "NumeroReglementPharmacie", CodeReglement)
            tNumeroCheque.Value = RecupererValeurExecuteScalaire("NumeroCheque", "REGLEMENT_PHARMACIE", "NumeroReglementPharmacie", CodeReglement)
            tLibelle.Value = RecupererValeurExecuteScalaire("LibelleReglement", "REGLEMENT_PHARMACIE", "NumeroReglementPharmacie", CodeReglement)
            cmbNature.SelectedValue = RecupererValeurExecuteScalaire("CodeNatureReglement", "REGLEMENT_PHARMACIE", "NumeroReglementPharmacie", CodeReglement)
            lNumeroReglement.Text = CodeReglement

            If (RecupererValeurExecuteScalaire("CodeBanque", "REGLEMENT_PHARMACIE", "NumeroReglementPharmacie", CodeReglement)) = "" Then
                cmbBanque.Text = ""
            Else
                cmbBanque.SelectedValue = RecupererValeurExecuteScalaire("CodeBanque", "REGLEMENT_PHARMACIE", "NumeroReglementPharmacie", CodeReglement)
            End If

        End If

        dtEcheance.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtEcheance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        If ajoutmodif = "A" Then
            cmbNature.Text = "ESPECE"
            chbEncaisse.Checked = True
        Else
            chbEncaisse.Checked = False
            chbEncaisse.Enabled = True
        End If
    End Sub

    Private Sub bconfirmerReglement_Click(sender As System.Object, e As System.EventArgs) Handles bconfirmerReglement.Click
        Dim StrSQL As String = ""
        Dim NumeroReglement As Integer = 0
        Dim i As Integer = 0
        Dim Reste As Double = 0.0
        Dim MontantAEnregistrer As Double = 0.0
        Dim CodeNatureReglement As Integer = 0
        Dim DateEcheance As String = ""
        Dim Montant As Double = 0.0
        Dim CodeBanque As String = ""

        If cmbNature.Text = "" Then
            MsgBox("Vous devez sélectionner une nature", MsgBoxStyle.OkOnly)
            Exit Sub
        End If
        If CDbl(tMontant.Text = 0) Then
            MsgBox("Montant null", MsgBoxStyle.OkOnly)
            tMontant.Focus()
            Exit Sub
        End If

        '------------------------------ demande du mot de passe
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        Dim myMotDePasse As New fMotDePasse

        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If

        If ajoutmodif = "A" Then

            '------------- ajout du nouveau règlement client

            StrSQL = " SELECT max(NumeroReglementPharmacie) FROM REGLEMENT_PHARMACIE"
            cmdReglement.Connection = ConnectionServeur
            cmdReglement.CommandText = StrSQL
            Try
                NumeroReglement = cmdReglement.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            NumeroReglement = NumeroReglement + 1

            CodeNatureReglement = cmbNature.SelectedValue


            If dtEcheance.Text <> "" Then
                DateEcheance = dtEcheance.Value
            Else
                DateEcheance = "Null"
            End If

            If tMontant.Text = "" Then
                Montant = "0"
            Else
                Montant = tMontant.Text
            End If

            If cmbBanque.Text <> "" Then
                CodeBanque = cmbBanque.SelectedValue
            Else
                CodeBanque = "Null"
            End If
            If CodeBanque = Nothing Then
                CodeBanque = "Null"
            End If

            StrSQL = "INSERT INTO REGLEMENT_PHARMACIE " + _
                     "(""NumeroReglementPharmacie"",""LibelleReglement"",""CodeNatureReglement""" + _
                     ",""Date"",""DateEcheance"",""MontantRegle"",""Montant"",""NumeroCheque"",""LibellePoste""" + _
                     ",""NomInscritSurLeCheque"",""CodePharmacie"",""CodeBanque"",""Vider"",""Encaisse""," + _
                     """CodePersonnel"") VALUES(" + Quote(NumeroReglement.ToString) + _
                     "," + Quote(tLibelle.Text) + _
                     "," + Quote(CodeNatureReglement.ToString) + _
                     "," + Quote(System.DateTime.Now.ToString) + _
                     "," + Quote(DateEcheance) + _
                     "," + Quote(Montant.ToString) + _
                     "," + Quote(Montant.ToString) + _
                     "," + Quote(tNumeroCheque.Text) + _
                     "," + Quote(Environment.MachineName.ToString) + _
                     "," + Quote(tNomInscrit.Text) + _
                     "," + Quote(CodePharmacie) + _
                     "," + CodeBanque + _
                     "," + "'False'" + _
                     "," + Quote(chbEncaisse.Checked.ToString) + _
                     "," + Quote(CodeOperateur.ToString) + ")"

            cmdReglement.Connection = ConnectionServeur
            cmdReglement.CommandText = StrSQL
            Try
                cmdReglement.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

        Else

            '---------------------------------------------------------------------------------------

            If dtEcheance.Text <> "" Then
                DateEcheance = dtEcheance.Text
            Else
                DateEcheance = "Null"
            End If

            If tMontant.Text = "" Then
                Montant = "0"
            Else
                Montant = tMontant.Text
            End If

            If cmbBanque.Text <> "" Then
                CodeBanque = cmbBanque.SelectedValue
            Else
                CodeBanque = "Null"
            End If

            StrSQL = " UPDATE REGLEMENT_PHARMACIE SET LibelleReglement= " + Quote(tLibelle.Text) + _
                     ",CodeNatureReglement=" + cmbNature.SelectedValue.ToString + _
                     ",Date='" + System.DateTime.Today + _
                     "',DateEcheance='" + DateEcheance + _
                     "',Montant=" + Montant.ToString + _
                     ",NumeroCheque='" + tNumeroCheque.Text + _
                     "',LibellePoste='" + Environment.MachineName.ToString + _
                     "',NomInscritSurLeCheque=" + Quote(tNomInscrit.Text) + _
                     ",CodePharmacie=" + Quote(CodePharmacie) + _
                     ",CodeBanque= " + CodeBanque + _
                     ",CodePersonnel=" + CodeOperateur + _
                     ",Encaisse =" + IIf(chbEncaisse.Checked = True, "1", "0") + _
                     " WHERE NumeroReglementPharmacie='" + CodeReglement.ToString + "'"

            cmdReglement.Connection = ConnectionServeur
            cmdReglement.CommandText = StrSQL
            Try
                cmdReglement.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            NumeroReglement = CodeReglement
        End If

        '------------------------------------------------------------------------------------------
        tNomInscrit.Enabled = False
        tMontant.Enabled = False
        'chbEncaisse.Enabled = False
        dtEcheance.Enabled = False
        tNumeroCheque.Enabled = False
        tLibelle.Enabled = False
        cmbNature.Enabled = False
        cmbBanque.Enabled = False

        bannulerReglement.Enabled = False
        bconfirmerReglement.Enabled = False
        Me.Hide()
    End Sub

    Private Sub bannulerReglement_Click(sender As System.Object, e As System.EventArgs) Handles bannulerReglement.Click
        tNomInscrit.Enabled = False
        tMontant.Enabled = False
        'chbEncaisse.Enabled = False
        dtEcheance.Enabled = False
        tNumeroCheque.Enabled = False
        tLibelle.Enabled = False
        cmbNature.Enabled = False
        cmbBanque.Enabled = False

        bannulerReglement.Enabled = False
        bconfirmerReglement.Enabled = False
        Me.Hide()
    End Sub


    Private Sub tMontant_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles tMontant.KeyUp
        Dim TestConversion As Double = 0.0
        If tMontant.Text <> "-" Then
            Try   ' test si un valeur numerique ou non
                TestConversion = Math.Round(CDbl(tMontant.Text), 3)
            Catch ex As Exception
                MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
                tMontant.Text = "0.000"
                tMontant.Focus()
                tMontant.SelectionLength = tMontant.Text.Length
                Exit Sub
            End Try
        End If

        If e.KeyCode = Keys.Enter Then
            If cmbNature.Text = "ESPECE" Or cmbNature.Text = "CARTE" Then
                tLibelle.Focus()
            Else
                chbEncaisse.Focus()
            End If
        End If

        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub


    Private Sub cmbNature_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbNature.KeyUp
        If e.KeyCode = Keys.Enter Then
            If cmbNature.Text = "ESPECE" Or cmbNature.Text = "CARTE" Then
                tMontant.Focus()
            Else
                dtEcheance.Focus()
            End If

        End If
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub

    Private Sub cmbNature_TextChanged(sender As Object, e As System.EventArgs) Handles cmbNature.TextChanged
        If cmbNature.Text = "ESPECE" Or cmbNature.Text = "CARTE" Then
            cmbBanque.Enabled = False
            dtEcheance.Enabled = False
            tNumeroCheque.Enabled = False
            tNomInscrit.Enabled = False
        Else
            cmbBanque.Enabled = True
            dtEcheance.Enabled = True
            tNumeroCheque.Enabled = True
            tNomInscrit.Enabled = True
        End If

        dtEcheance.Value = System.DateTime.Today
    End Sub

    Private Sub dtEcheance_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles dtEcheance.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbBanque.Focus()
        End If
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub

    Private Sub dtEcheance_TextChanged(sender As Object, e As System.EventArgs) Handles dtEcheance.TextChanged
        If dtEcheance.Text > System.DateTime.Today Then
            chbEncaisse.Checked = False
        Else
            chbEncaisse.Checked = True
        End If
    End Sub

    Private Sub cmbBanque_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbBanque.KeyUp
        If e.KeyCode = Keys.Enter Then
            If cmbBanque.Text = "" Then
                cmbBanque.Focus()
            ElseIf cmbBanque.WillChangeToText = "" And cmbBanque.Text <> "" Then

                '----------------- nouveau fournisseur 
                Dim reponse As MsgBoxResult
                reponse = MsgBox("Banque " + cmbBanque.Text + " inéxistant, voulez vous le créer  ", MsgBoxStyle.OkCancel, "Erreur")
                If reponse = MsgBoxResult.Ok Then

                    Dim MyBanque As New fAjouterBanque
                    MyBanque.Valeur = cmbBanque.Text

                    MyBanque.StartPosition = FormStartPosition.CenterScreen
                    MyBanque.tCategorie.Focus()
                    MyBanque.FormBorderStyle = Windows.Forms.FormBorderStyle.None
                    MyBanque.ShowDialog()

                    If MyBanque.Enregistrer = False Then
                        cmbBanque.Text = ""
                    End If

                    MyBanque.Close()
                    MyBanque.Dispose()

                    dsReglement.Tables("BANQUE").Clear()
                    StrSQL = "SELECT DISTINCT CodeBanque,NomBanque FROM BANQUE ORDER BY NomBanque ASC"
                    cmdReglement.Connection = ConnectionServeur
                    cmdReglement.CommandText = StrSQL
                    daReglement = New SqlDataAdapter(cmdReglement)
                    daReglement.Fill(dsReglement, "BANQUE")
                    cmbBanque.DataSource = dsReglement.Tables("BANQUE")
                    cmbBanque.ValueMember = "CodeBanque"
                    cmbBanque.DisplayMember = "NomBanque"
                    cmbBanque.ColumnHeaders = False
                    cmbBanque.Splits(0).DisplayColumns("CodeBanque").Width = 0
                    cmbBanque.Splits(0).DisplayColumns("NomBanque").Width = 10
                    cmbBanque.Text = cmbBanque.Text

                    tNumeroCheque.Focus()
                Else
                    cmbBanque.Text = ""
                End If
            Else
                cmbBanque.Text = cmbBanque.WillChangeToText
                tNumeroCheque.Focus()
            End If
        Else
            cmbBanque.OpenCombo()
        End If

        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub

    Private Sub calculerMonatant()
        Dim i As Integer = 0
        Dim TotalMontant As Decimal = 0.0
        Dim TotalHT As Decimal = 0.0
        Dim totalTVA As Decimal = 0.0

        tMontant.Text = TotalMontant
    End Sub

    Private Sub fReglementPharmacie_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles Me.KeyUp
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub
End Class