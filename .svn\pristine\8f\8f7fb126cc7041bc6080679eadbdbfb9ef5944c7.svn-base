﻿Imports System.IO
Public Class fValeurSimulation
    Public ValeurActuelle As Double = 0.0
    Public ValeurFixee As Double = 0.0

    Private Sub tValeurFixee_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tValeurFixee.KeyUp
        If e.KeyCode = Keys.Enter Then
            If tValeurFixee.Text <> "" And IsNumeric(tValeurFixee.Text) Then
                ValeurFixee = tValeurFixee.Text
            Else
                ValeurFixee = ValeurActuelle
            End If
            Me.Hide()
        End If
    End Sub

    Private Sub tValeurFixee_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tValeurFixee.TextChanged

    End Sub

    Private Sub fValeurSimulation_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        lValeurActuelle.Text = ValeurActuelle.ToString
    End Sub

    Private Sub bQuitter_Click(sender As System.Object, e As System.EventArgs) Handles bQuitter.Click
        Me.Close()
    End Sub
End Class