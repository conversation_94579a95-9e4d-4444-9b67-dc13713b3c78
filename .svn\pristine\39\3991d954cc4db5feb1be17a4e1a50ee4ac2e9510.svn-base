<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
 <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <!-- Diagram content (shape and connector positions) -->
    <edmx:Diagrams>
      <Diagram DiagramId="a2c414b7bd314f10ae447273d17b35ce" Name="Diagramme1" ZoomLevel="96">
        <EntityTypeShape EntityType="BusinessManagementModel.VENTE_NUMERO" Width="2.375" PointX="1.25" PointY="0.75" IsExpanded="false" />
        <EntityTypeShape EntityType="BusinessManagementModel.VENTE" Width="2.375" PointX="1.25" PointY="2" IsExpanded="false" />
        <EntityTypeShape EntityType="BusinessManagementModel.VENTE_DETAILS" Width="2.375" PointX="7.625" PointY="0.875" IsExpanded="false" />
        <AssociationConnector Association="BusinessManagementModel.VENTEVENTE_DETAILS" />
        <EntityTypeShape EntityType="BusinessManagementModel.COMMANDE" Width="2.375" PointX="1.25" PointY="1.375" IsExpanded="false" />
        <EntityTypeShape EntityType="BusinessManagementModel.COMMANDE_DETAILS" Width="2.375" PointX="4.75" PointY="1.375" IsExpanded="false" />
        <AssociationConnector Association="BusinessManagementModel.COMMANDECOMMANDE_DETAILS" />
        <EntityTypeShape EntityType="BusinessManagementModel.SIMULATION_STOCK" Width="2.375" PointX="1.25" PointY="2.625" IsExpanded="false" />
        <EntityTypeShape EntityType="BusinessManagementModel.SIMULATION_STOCK_DETAILS" Width="2.375" PointX="1.25" PointY="4.125" IsExpanded="false" />
        <AssociationConnector Association="BusinessManagementModel.SIMULATION_STOCKSIMULATION_STOCK_DETAILS" />
        <EntityTypeShape EntityType="BusinessManagementModel.ACHAT" Width="2.375" PointX="1.25" PointY="3.25" IsExpanded="false" />
        <EntityTypeShape EntityType="BusinessManagementModel.ACHAT_DETAILS" Width="2.375" PointX="4.75" PointY="3.25" IsExpanded="false" />
        <AssociationConnector Association="BusinessManagementModel.ACHATACHAT_DETAILS" />
      </Diagram>
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>