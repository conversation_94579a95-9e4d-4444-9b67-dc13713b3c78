﻿Imports System.Data.SqlClient
Imports System.IO
Imports System.IO.Ports

Public Class fMotDePasseOuvertureTiroir
    Declare Function InitFRIUSBLibrary Lib "FIRIDLLU.dll" () As Integer
    Declare Function OpenFRIDoor Lib "FIRIDLLU.dll" () As Integer
    Declare Function CloseFRIUSBLibrary Lib "FIRIDLLU.dll" () As Integer

    Private Sub bOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOk.Click

        Dim StrSQL As String = ""
        Dim cmdMotDePasse As New SqlCommand
        Dim MotDePasseExiste As Integer = 0
        Dim CodeOperateur As String = ""

        StrSQL = "SELECT COUNT([CodeUtilisateur]) FROM UTILISATEUR WHERE Supprime=0 AND [MotPass]='" + tMotDePasse.Text + "'"
        cmdMotDePasse.Connection = ConnectionServeur
        cmdMotDePasse.CommandText = StrSQL
        Try
            MotDePasseExiste = cmdMotDePasse.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        If MotDePasseExiste = 0 Then
            tMotDePasse.Text = ""
            tMotDePasse.Focus()
            MsgBox("Mot de passe erroné !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        StrSQL = "SELECT [CodeUtilisateur] FROM UTILISATEUR WHERE Supprime=0 AND [MotPass]='" + tMotDePasse.Text + "'"
        cmdMotDePasse.Connection = ConnectionServeur
        cmdMotDePasse.CommandText = StrSQL
        Try
            CodeOperateur = cmdMotDePasse.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try


        cmdMotDePasse.Connection = ConnectionServeur
        cmdMotDePasse.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE '" + "OUVRIR_CAISSE" + "' AND CodeUtilisateur='" + CodeOperateur + "'"

        Try
            If cmdMotDePasse.ExecuteScalar() < 1 Then
                MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
                Exit Sub
            End If
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try


        If Tiroir Then
            If TiroirUSB Then
                Try
                    InitFRIUSBLibrary()
                    OpenFRIDoor()
                    CloseFRIUSBLibrary()
                Catch ex As Exception
                End Try
            End If
            If TiroirCOM Then

                Dim Adaptateur As Boolean = False
                Try
                    Adaptateur = RecupererValeurExecuteScalaire("Adaptateur", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
                Catch
                    Adaptateur = False
                End Try

                Dim TypeTiroir As Integer = 0
                Try
                    TypeTiroir = RecupererValeurExecuteScalaire("TypeTiroir", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
                Catch
                    TypeTiroir = 0
                End Try

                Dim Port As String = "0"
                Try
                    Port = RecupererValeurExecuteScalaire("COM", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
                Catch
                    Port = "0"
                End Try

                Dim VitesseDeTransmission As Integer = 9600
                Try
                    VitesseDeTransmission = RecupererValeurExecuteScalaire("VitesseDeTransmission", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
                Catch
                    VitesseDeTransmission = 9600
                End Try

                Try

                    Select Case TypeTiroir

                        Case 0
                            If Not (SerialPortTiroir.IsOpen = True) Then
                                SerialPortTiroir.Open()
                            End If
                            SerialPortTiroir.BaudRate = VitesseDeTransmission
                            SerialPortTiroir.Write("g")
                            SerialPortTiroir.Close()

                        Case 1
                            Using com As IO.Ports.SerialPort = My.Computer.Ports.OpenSerialPort("COM" & Port)
                                com.WriteLine("g")
                            End Using

                        Case 2
                            Dim mPort As New System.IO.Ports.SerialPort()
                            If mPort.IsOpen Then
                                mPort.Close()
                            End If
                            mPort.PortName = "COM" + Port
                            mPort.BaudRate = VitesseDeTransmission
                            mPort.Parity = Parity.None
                            mPort.DataBits = 8
                            mPort.StopBits = StopBits.One
                            mPort.Handshake = Handshake.RequestToSend
                            ' Handshake.None;
                            mPort.Open()
                            mPort.Write("d")
                            mPort.Close()

                        Case Else
                            Shell("cmd.exe /K dir /P > COM" & Port, AppWinStyle.Hide, True, 2000)

                    End Select

                Catch
                End Try

            End If
        End If

        InsertionDansLog("OUVRIR_CAISSE", "Ouverture de la caisse pour la raison :" + tDescription.Text, CodeOperateur, System.DateTime.Now, "VENTE", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
        Me.Hide()
    End Sub


    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Me.Hide()
    End Sub
End Class