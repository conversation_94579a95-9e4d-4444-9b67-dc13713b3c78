﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fFamilleNonRemboursable
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fFamilleNonRemboursable))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.bConfirmer = New C1.Win.C1Input.C1Button()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.gFamille = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.cmbFamille = New C1.Win.C1List.C1Combo()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.gFamilleNonAttribue = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bSupprimerUn = New C1.Win.C1Input.C1Button()
        Me.bSupprimerTous = New C1.Win.C1Input.C1Button()
        Me.bAjoutertous = New C1.Win.C1Input.C1Button()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.cmbMutuelle = New C1.Win.C1List.C1Combo()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.bAjouterUn = New C1.Win.C1Input.C1Button()
        Me.Panel.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.gFamille, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.cmbFamille, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gFamilleNonAttribue, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox4.SuspendLayout()
        CType(Me.cmbMutuelle, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.Label5)
        Me.Panel.Controls.Add(Me.bConfirmer)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.GroupBox2)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Controls.Add(Me.bSupprimerUn)
        Me.Panel.Controls.Add(Me.bSupprimerTous)
        Me.Panel.Controls.Add(Me.bAjoutertous)
        Me.Panel.Controls.Add(Me.GroupBox4)
        Me.Panel.Controls.Add(Me.bAjouterUn)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1020, 539)
        Me.Panel.TabIndex = 1
        '
        'Label5
        '
        Me.Label5.Font = New System.Drawing.Font("HandelGotDLig", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label5.Location = New System.Drawing.Point(12, 18)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(745, 44)
        Me.Label5.TabIndex = 49
        Me.Label5.Text = "CATEGORIES NON REMBOURSABLES"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bConfirmer
        '
        Me.bConfirmer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bConfirmer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmer.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__1
        Me.bConfirmer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmer.Location = New System.Drawing.Point(776, 15)
        Me.bConfirmer.Name = "bConfirmer"
        Me.bConfirmer.Size = New System.Drawing.Size(112, 45)
        Me.bConfirmer.TabIndex = 16
        Me.bConfirmer.Text = "Confirmer                 F3"
        Me.bConfirmer.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bConfirmer.UseVisualStyleBackColor = True
        Me.bConfirmer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(896, 15)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(112, 45)
        Me.bAnnuler.TabIndex = 17
        Me.bAnnuler.Text = "Fermer              F12"
        Me.bAnnuler.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox2
        '
        Me.GroupBox2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox2.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox2.Controls.Add(Me.gFamille)
        Me.GroupBox2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox2.Location = New System.Drawing.Point(656, 125)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(350, 402)
        Me.GroupBox2.TabIndex = 15
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "Liste des Catégories non remboursables"
        '
        'gFamille
        '
        Me.gFamille.AllowUpdate = False
        Me.gFamille.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gFamille.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.gFamille.GroupByCaption = "Drag a column header here to group by that column"
        Me.gFamille.Images.Add(CType(resources.GetObject("gFamille.Images"), System.Drawing.Image))
        Me.gFamille.Location = New System.Drawing.Point(17, 33)
        Me.gFamille.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gFamille.Name = "gFamille"
        Me.gFamille.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gFamille.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gFamille.PreviewInfo.ZoomFactor = 75.0R
        Me.gFamille.PrintInfo.PageSettings = CType(resources.GetObject("gFamille.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gFamille.Size = New System.Drawing.Size(314, 348)
        Me.gFamille.TabIndex = 2
        Me.gFamille.Text = "C1TrueDBGrid4"
        Me.gFamille.PropBag = resources.GetString("gFamille.PropBag")
        '
        'GroupBox1
        '
        Me.GroupBox1.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox1.Controls.Add(Me.cmbFamille)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Controls.Add(Me.gFamilleNonAttribue)
        Me.GroupBox1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox1.Location = New System.Drawing.Point(12, 125)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(350, 402)
        Me.GroupBox1.TabIndex = 14
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Liste de tous les Catégories"
        '
        'cmbFamille
        '
        Me.cmbFamille.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbFamille.Caption = ""
        Me.cmbFamille.CaptionHeight = 17
        Me.cmbFamille.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbFamille.ColumnCaptionHeight = 17
        Me.cmbFamille.ColumnFooterHeight = 17
        Me.cmbFamille.ContentHeight = 15
        Me.cmbFamille.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbFamille.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbFamille.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbFamille.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbFamille.EditorHeight = 15
        Me.cmbFamille.Images.Add(CType(resources.GetObject("cmbFamille.Images"), System.Drawing.Image))
        Me.cmbFamille.ItemHeight = 15
        Me.cmbFamille.Location = New System.Drawing.Point(69, 33)
        Me.cmbFamille.MatchEntryTimeout = CType(2000, Long)
        Me.cmbFamille.MaxDropDownItems = CType(5, Short)
        Me.cmbFamille.MaxLength = 32767
        Me.cmbFamille.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbFamille.Name = "cmbFamille"
        Me.cmbFamille.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbFamille.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbFamille.Size = New System.Drawing.Size(261, 21)
        Me.cmbFamille.TabIndex = 15
        Me.cmbFamille.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbFamille.PropBag = resources.GetString("cmbFamille.PropBag")
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.Location = New System.Drawing.Point(13, 37)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(50, 13)
        Me.Label1.TabIndex = 14
        Me.Label1.Text = "Chercher"
        '
        'gFamilleNonAttribue
        '
        Me.gFamilleNonAttribue.AllowUpdate = False
        Me.gFamilleNonAttribue.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gFamilleNonAttribue.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.gFamilleNonAttribue.GroupByCaption = "Drag a column header here to group by that column"
        Me.gFamilleNonAttribue.Images.Add(CType(resources.GetObject("gFamilleNonAttribue.Images"), System.Drawing.Image))
        Me.gFamilleNonAttribue.Location = New System.Drawing.Point(16, 73)
        Me.gFamilleNonAttribue.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gFamilleNonAttribue.Name = "gFamilleNonAttribue"
        Me.gFamilleNonAttribue.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gFamilleNonAttribue.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gFamilleNonAttribue.PreviewInfo.ZoomFactor = 75.0R
        Me.gFamilleNonAttribue.PrintInfo.PageSettings = CType(resources.GetObject("gFamilleNonAttribue.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gFamilleNonAttribue.Size = New System.Drawing.Size(314, 308)
        Me.gFamilleNonAttribue.TabIndex = 4
        Me.gFamilleNonAttribue.Text = "C1TrueDBGrid4"
        Me.gFamilleNonAttribue.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gFamilleNonAttribue.PropBag = resources.GetString("gFamilleNonAttribue.PropBag")
        '
        'bSupprimerUn
        '
        Me.bSupprimerUn.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.bSupprimerUn.Image = Global.Pharma2000Premium.My.Resources.Resources.previous_1
        Me.bSupprimerUn.Location = New System.Drawing.Point(485, 342)
        Me.bSupprimerUn.Name = "bSupprimerUn"
        Me.bSupprimerUn.Size = New System.Drawing.Size(45, 45)
        Me.bSupprimerUn.TabIndex = 6
        Me.bSupprimerUn.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSupprimerUn.UseVisualStyleBackColor = True
        Me.bSupprimerUn.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bSupprimerTous
        '
        Me.bSupprimerTous.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.bSupprimerTous.Image = Global.Pharma2000Premium.My.Resources.Resources.first_1
        Me.bSupprimerTous.Location = New System.Drawing.Point(485, 404)
        Me.bSupprimerTous.Name = "bSupprimerTous"
        Me.bSupprimerTous.Size = New System.Drawing.Size(45, 45)
        Me.bSupprimerTous.TabIndex = 7
        Me.bSupprimerTous.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSupprimerTous.UseVisualStyleBackColor = True
        Me.bSupprimerTous.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjoutertous
        '
        Me.bAjoutertous.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.bAjoutertous.Image = Global.Pharma2000Premium.My.Resources.Resources.last_1
        Me.bAjoutertous.Location = New System.Drawing.Point(485, 215)
        Me.bAjoutertous.Name = "bAjoutertous"
        Me.bAjoutertous.Size = New System.Drawing.Size(45, 45)
        Me.bAjoutertous.TabIndex = 1
        Me.bAjoutertous.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjoutertous.UseVisualStyleBackColor = True
        Me.bAjoutertous.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox4
        '
        Me.GroupBox4.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox4.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox4.Controls.Add(Me.cmbMutuelle)
        Me.GroupBox4.Controls.Add(Me.Label15)
        Me.GroupBox4.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox4.Location = New System.Drawing.Point(12, 66)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(996, 53)
        Me.GroupBox4.TabIndex = 0
        Me.GroupBox4.TabStop = False
        '
        'cmbMutuelle
        '
        Me.cmbMutuelle.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbMutuelle.Caption = ""
        Me.cmbMutuelle.CaptionHeight = 17
        Me.cmbMutuelle.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbMutuelle.ColumnCaptionHeight = 17
        Me.cmbMutuelle.ColumnFooterHeight = 17
        Me.cmbMutuelle.ContentHeight = 15
        Me.cmbMutuelle.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbMutuelle.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbMutuelle.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbMutuelle.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbMutuelle.EditorHeight = 15
        Me.cmbMutuelle.Images.Add(CType(resources.GetObject("cmbMutuelle.Images"), System.Drawing.Image))
        Me.cmbMutuelle.ItemHeight = 15
        Me.cmbMutuelle.Location = New System.Drawing.Point(145, 18)
        Me.cmbMutuelle.MatchEntryTimeout = CType(2000, Long)
        Me.cmbMutuelle.MaxDropDownItems = CType(5, Short)
        Me.cmbMutuelle.MaxLength = 32767
        Me.cmbMutuelle.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbMutuelle.Name = "cmbMutuelle"
        Me.cmbMutuelle.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbMutuelle.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbMutuelle.Size = New System.Drawing.Size(279, 21)
        Me.cmbMutuelle.TabIndex = 13
        Me.cmbMutuelle.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbMutuelle.PropBag = resources.GetString("cmbMutuelle.PropBag")
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.BackColor = System.Drawing.Color.Transparent
        Me.Label15.Location = New System.Drawing.Point(13, 21)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(97, 13)
        Me.Label15.TabIndex = 8
        Me.Label15.Text = "Liste des Mutuelles"
        '
        'bAjouterUn
        '
        Me.bAjouterUn.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.bAjouterUn.Image = Global.Pharma2000Premium.My.Resources.Resources.next_1
        Me.bAjouterUn.Location = New System.Drawing.Point(485, 281)
        Me.bAjouterUn.Name = "bAjouterUn"
        Me.bAjouterUn.Size = New System.Drawing.Size(45, 45)
        Me.bAjouterUn.TabIndex = 2
        Me.bAjouterUn.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouterUn.UseVisualStyleBackColor = True
        Me.bAjouterUn.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fFamilleNonRemboursable
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1020, 539)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fFamilleNonRemboursable"
        Me.Text = "fFamilleNonRemboursable"
        Me.Panel.ResumeLayout(False)
        Me.GroupBox2.ResumeLayout(False)
        CType(Me.gFamille, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.cmbFamille, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gFamilleNonAttribue, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.cmbMutuelle, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents bAjoutertous As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents bAjouterUn As C1.Win.C1Input.C1Button
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents gFamille As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents cmbMutuelle As C1.Win.C1List.C1Combo
    Friend WithEvents bSupprimerUn As C1.Win.C1Input.C1Button
    Friend WithEvents bSupprimerTous As C1.Win.C1Input.C1Button
    Friend WithEvents gFamilleNonAttribue As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents cmbFamille As C1.Win.C1List.C1Combo
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents bConfirmer As C1.Win.C1Input.C1Button
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents Label5 As System.Windows.Forms.Label
End Class
