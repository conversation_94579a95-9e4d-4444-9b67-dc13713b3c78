﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fReglementFactureClient

    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fReglementClient))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bCocherTous = New C1.Win.C1Input.C1Button()
        Me.bImprimer = New C1.Win.C1Input.C1Button()
        Me.gVentes = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bconfirmerReglement = New C1.Win.C1Input.C1Button()
        Me.bannulerReglement = New C1.Win.C1Input.C1Button()
        Me.GroupBox13 = New System.Windows.Forms.GroupBox()
        Me.chbTaux = New System.Windows.Forms.CheckBox()
        Me.tTaux = New C1.Win.C1Input.C1TextBox()
        Me.ltaux = New System.Windows.Forms.Label()
        Me.lSoldeClient = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.lNumeroReglement = New System.Windows.Forms.Label()
        Me.dtEcheance = New C1.Win.C1Input.C1DateEdit()
        Me.tNomInscrit = New C1.Win.C1Input.C1TextBox()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.cmbBanque = New C1.Win.C1List.C1Combo()
        Me.Label19 = New System.Windows.Forms.Label()
        Me.tLibelle = New C1.Win.C1Input.C1TextBox()
        Me.chbEncaisse = New System.Windows.Forms.CheckBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.tMontant = New C1.Win.C1Input.C1TextBox()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.tNumeroCheque = New C1.Win.C1Input.C1TextBox()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.cmbNature = New C1.Win.C1List.C1Combo()
        Me.Label24 = New System.Windows.Forms.Label()
        Me.CR = New Pharma2000Premium.EtatDeBonDeReglement()
        Me.CR1 = New Pharma2000Premium.EtatRecuReglement()
        Me.SerialPortTiroir = New System.IO.Ports.SerialPort(Me.components)
        Me.Panel.SuspendLayout()
        CType(Me.gVentes, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox13.SuspendLayout()
        CType(Me.tTaux, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtEcheance, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNomInscrit, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbBanque, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tLibelle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tMontant, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNumeroCheque, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbNature, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.bCocherTous)
        Me.Panel.Controls.Add(Me.bImprimer)
        Me.Panel.Controls.Add(Me.gVentes)
        Me.Panel.Controls.Add(Me.bconfirmerReglement)
        Me.Panel.Controls.Add(Me.bannulerReglement)
        Me.Panel.Controls.Add(Me.GroupBox13)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(835, 431)
        Me.Panel.TabIndex = 0
        '
        'bCocherTous
        '
        Me.bCocherTous.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bCocherTous.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bCocherTous.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bCocherTous.Location = New System.Drawing.Point(3, 142)
        Me.bCocherTous.Name = "bCocherTous"
        Me.bCocherTous.Size = New System.Drawing.Size(111, 26)
        Me.bCocherTous.TabIndex = 72
        Me.bCocherTous.Text = "Cocher tous"
        Me.bCocherTous.UseVisualStyleBackColor = True
        Me.bCocherTous.Visible = False
        Me.bCocherTous.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bImprimer
        '
        Me.bImprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.imprmante1
        Me.bImprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bImprimer.Location = New System.Drawing.Point(485, 373)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(113, 45)
        Me.bImprimer.TabIndex = 71
        Me.bImprimer.Text = "Imprimer            F9"
        Me.bImprimer.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gVentes
        '
        Me.gVentes.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.gVentes.FetchRowStyles = True
        Me.gVentes.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gVentes.GroupByCaption = "Drag a column header here to group by that column"
        Me.gVentes.Images.Add(CType(resources.GetObject("gVentes.Images"), System.Drawing.Image))
        Me.gVentes.LinesPerRow = 2
        Me.gVentes.Location = New System.Drawing.Point(3, 172)
        Me.gVentes.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gVentes.Name = "gVentes"
        Me.gVentes.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gVentes.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gVentes.PreviewInfo.ZoomFactor = 75.0R
        Me.gVentes.PrintInfo.PageSettings = CType(resources.GetObject("gVentes.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gVentes.Size = New System.Drawing.Size(828, 194)
        Me.gVentes.TabIndex = 70
        Me.gVentes.Text = "C1TrueDBGrid1"
        Me.gVentes.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gVentes.PropBag = resources.GetString("gVentes.PropBag")
        '
        'bconfirmerReglement
        '
        Me.bconfirmerReglement.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bconfirmerReglement.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bconfirmerReglement.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bconfirmerReglement.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bconfirmerReglement.Location = New System.Drawing.Point(602, 373)
        Me.bconfirmerReglement.Name = "bconfirmerReglement"
        Me.bconfirmerReglement.Size = New System.Drawing.Size(115, 45)
        Me.bconfirmerReglement.TabIndex = 67
        Me.bconfirmerReglement.Text = "Confirmer               F3"
        Me.bconfirmerReglement.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bconfirmerReglement.UseVisualStyleBackColor = True
        Me.bconfirmerReglement.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bannulerReglement
        '
        Me.bannulerReglement.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bannulerReglement.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bannulerReglement.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bannulerReglement.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bannulerReglement.Location = New System.Drawing.Point(721, 373)
        Me.bannulerReglement.Name = "bannulerReglement"
        Me.bannulerReglement.Size = New System.Drawing.Size(110, 45)
        Me.bannulerReglement.TabIndex = 68
        Me.bannulerReglement.Text = "Annuler          F10"
        Me.bannulerReglement.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bannulerReglement.UseVisualStyleBackColor = True
        Me.bannulerReglement.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox13
        '
        Me.GroupBox13.Controls.Add(Me.chbTaux)
        Me.GroupBox13.Controls.Add(Me.tTaux)
        Me.GroupBox13.Controls.Add(Me.ltaux)
        Me.GroupBox13.Controls.Add(Me.lSoldeClient)
        Me.GroupBox13.Controls.Add(Me.Label1)
        Me.GroupBox13.Controls.Add(Me.lNumeroReglement)
        Me.GroupBox13.Controls.Add(Me.dtEcheance)
        Me.GroupBox13.Controls.Add(Me.tNomInscrit)
        Me.GroupBox13.Controls.Add(Me.Label20)
        Me.GroupBox13.Controls.Add(Me.cmbBanque)
        Me.GroupBox13.Controls.Add(Me.Label19)
        Me.GroupBox13.Controls.Add(Me.tLibelle)
        Me.GroupBox13.Controls.Add(Me.chbEncaisse)
        Me.GroupBox13.Controls.Add(Me.Label2)
        Me.GroupBox13.Controls.Add(Me.tMontant)
        Me.GroupBox13.Controls.Add(Me.Label18)
        Me.GroupBox13.Controls.Add(Me.tNumeroCheque)
        Me.GroupBox13.Controls.Add(Me.Label15)
        Me.GroupBox13.Controls.Add(Me.Label13)
        Me.GroupBox13.Controls.Add(Me.cmbNature)
        Me.GroupBox13.Controls.Add(Me.Label24)
        Me.GroupBox13.Location = New System.Drawing.Point(3, 12)
        Me.GroupBox13.Name = "GroupBox13"
        Me.GroupBox13.Size = New System.Drawing.Size(828, 126)
        Me.GroupBox13.TabIndex = 50
        Me.GroupBox13.TabStop = False
        '
        'chbTaux
        '
        Me.chbTaux.AutoSize = True
        Me.chbTaux.Location = New System.Drawing.Point(483, 77)
        Me.chbTaux.Name = "chbTaux"
        Me.chbTaux.Size = New System.Drawing.Size(15, 14)
        Me.chbTaux.TabIndex = 56
        Me.chbTaux.UseVisualStyleBackColor = True
        '
        'tTaux
        '
        Me.tTaux.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tTaux.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tTaux.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTaux.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tTaux.DataType = GetType(Decimal)
        Me.tTaux.Enabled = False
        Me.tTaux.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tTaux.Location = New System.Drawing.Point(552, 71)
        Me.tTaux.Name = "tTaux"
        Me.tTaux.Size = New System.Drawing.Size(43, 22)
        Me.tTaux.TabIndex = 54
        Me.tTaux.Tag = Nothing
        Me.tTaux.Value = New Decimal(New Integer() {100, 0, 0, 0})
        Me.tTaux.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tTaux.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'ltaux
        '
        Me.ltaux.AutoSize = True
        Me.ltaux.Enabled = False
        Me.ltaux.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ltaux.Location = New System.Drawing.Point(504, 77)
        Me.ltaux.Name = "ltaux"
        Me.ltaux.Size = New System.Drawing.Size(42, 13)
        Me.ltaux.TabIndex = 55
        Me.ltaux.Text = "Taux %"
        '
        'lSoldeClient
        '
        Me.lSoldeClient.AutoSize = True
        Me.lSoldeClient.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lSoldeClient.Location = New System.Drawing.Point(657, 50)
        Me.lSoldeClient.Name = "lSoldeClient"
        Me.lSoldeClient.Size = New System.Drawing.Size(14, 13)
        Me.lSoldeClient.TabIndex = 53
        Me.lSoldeClient.Text = "0"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(583, 50)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(63, 13)
        Me.Label1.TabIndex = 52
        Me.Label1.Text = "Solde Client"
        '
        'lNumeroReglement
        '
        Me.lNumeroReglement.AutoSize = True
        Me.lNumeroReglement.Font = New System.Drawing.Font("Calibri", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNumeroReglement.Location = New System.Drawing.Point(4, 9)
        Me.lNumeroReglement.Name = "lNumeroReglement"
        Me.lNumeroReglement.Size = New System.Drawing.Size(10, 13)
        Me.lNumeroReglement.TabIndex = 51
        Me.lNumeroReglement.Text = "-"
        '
        'dtEcheance
        '
        Me.dtEcheance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtEcheance.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtEcheance.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtEcheance.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtEcheance.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtEcheance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.dtEcheance.Location = New System.Drawing.Point(304, 26)
        Me.dtEcheance.Name = "dtEcheance"
        Me.dtEcheance.Size = New System.Drawing.Size(115, 18)
        Me.dtEcheance.TabIndex = 1
        Me.dtEcheance.Tag = Nothing
        Me.dtEcheance.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtEcheance.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tNomInscrit
        '
        Me.tNomInscrit.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNomInscrit.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNomInscrit.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNomInscrit.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNomInscrit.Location = New System.Drawing.Point(657, 23)
        Me.tNomInscrit.Name = "tNomInscrit"
        Me.tNomInscrit.Size = New System.Drawing.Size(166, 18)
        Me.tNomInscrit.TabIndex = 5
        Me.tNomInscrit.Tag = Nothing
        Me.tNomInscrit.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNomInscrit.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label20
        '
        Me.Label20.AutoSize = True
        Me.Label20.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label20.Location = New System.Drawing.Point(525, 25)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(126, 13)
        Me.Label20.TabIndex = 49
        Me.Label20.Text = "Nom inscrit sur le chèque"
        '
        'cmbBanque
        '
        Me.cmbBanque.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbBanque.Caption = ""
        Me.cmbBanque.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbBanque.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.cmbBanque.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbBanque.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbBanque.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbBanque.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbBanque.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbBanque.Images.Add(CType(resources.GetObject("cmbBanque.Images"), System.Drawing.Image))
        Me.cmbBanque.Location = New System.Drawing.Point(64, 59)
        Me.cmbBanque.MatchEntryTimeout = CType(2000, Long)
        Me.cmbBanque.MaxDropDownItems = CType(5, Short)
        Me.cmbBanque.MaxLength = 32767
        Me.cmbBanque.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbBanque.Name = "cmbBanque"
        Me.cmbBanque.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbBanque.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbBanque.Size = New System.Drawing.Size(106, 22)
        Me.cmbBanque.TabIndex = 2
        Me.cmbBanque.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbBanque.PropBag = resources.GetString("cmbBanque.PropBag")
        '
        'Label19
        '
        Me.Label19.AutoSize = True
        Me.Label19.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label19.Location = New System.Drawing.Point(9, 63)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(44, 13)
        Me.Label19.TabIndex = 48
        Me.Label19.Text = "Banque"
        '
        'tLibelle
        '
        Me.tLibelle.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tLibelle.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tLibelle.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tLibelle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tLibelle.Location = New System.Drawing.Point(64, 95)
        Me.tLibelle.Name = "tLibelle"
        Me.tLibelle.Size = New System.Drawing.Size(410, 18)
        Me.tLibelle.TabIndex = 4
        Me.tLibelle.Tag = Nothing
        Me.tLibelle.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tLibelle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'chbEncaisse
        '
        Me.chbEncaisse.AutoSize = True
        Me.chbEncaisse.Enabled = False
        Me.chbEncaisse.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chbEncaisse.Location = New System.Drawing.Point(692, 99)
        Me.chbEncaisse.Name = "chbEncaisse"
        Me.chbEncaisse.Size = New System.Drawing.Size(69, 17)
        Me.chbEncaisse.TabIndex = 7
        Me.chbEncaisse.Text = "Encaissé"
        Me.chbEncaisse.UseVisualStyleBackColor = True
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(16, 97)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(37, 13)
        Me.Label2.TabIndex = 2
        Me.Label2.Text = "Libellé"
        '
        'tMontant
        '
        Me.tMontant.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tMontant.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMontant.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tMontant.DataType = GetType(Decimal)
        Me.tMontant.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tMontant.Location = New System.Drawing.Point(657, 71)
        Me.tMontant.Name = "tMontant"
        Me.tMontant.Size = New System.Drawing.Size(166, 22)
        Me.tMontant.TabIndex = 6
        Me.tMontant.Tag = Nothing
        Me.tMontant.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tMontant.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label18
        '
        Me.Label18.AutoSize = True
        Me.Label18.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label18.Location = New System.Drawing.Point(605, 76)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(46, 13)
        Me.Label18.TabIndex = 44
        Me.Label18.Text = "Montant"
        '
        'tNumeroCheque
        '
        Me.tNumeroCheque.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNumeroCheque.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNumeroCheque.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNumeroCheque.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNumeroCheque.Location = New System.Drawing.Point(304, 59)
        Me.tNumeroCheque.Name = "tNumeroCheque"
        Me.tNumeroCheque.Size = New System.Drawing.Size(115, 18)
        Me.tNumeroCheque.TabIndex = 3
        Me.tNumeroCheque.Tag = Nothing
        Me.tNumeroCheque.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNumeroCheque.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label15.Location = New System.Drawing.Point(215, 61)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(83, 13)
        Me.Label15.TabIndex = 42
        Me.Label15.Text = "Numéro chèque"
        '
        'Label13
        '
        Me.Label13.AutoSize = True
        Me.Label13.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label13.Location = New System.Drawing.Point(242, 30)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(56, 13)
        Me.Label13.TabIndex = 40
        Me.Label13.Text = "Echéance"
        '
        'cmbNature
        '
        Me.cmbNature.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbNature.Caption = ""
        Me.cmbNature.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbNature.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.cmbNature.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbNature.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbNature.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbNature.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbNature.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbNature.Images.Add(CType(resources.GetObject("cmbNature.Images"), System.Drawing.Image))
        Me.cmbNature.Location = New System.Drawing.Point(64, 23)
        Me.cmbNature.MatchEntryTimeout = CType(2000, Long)
        Me.cmbNature.MaxDropDownItems = CType(5, Short)
        Me.cmbNature.MaxLength = 32767
        Me.cmbNature.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbNature.Name = "cmbNature"
        Me.cmbNature.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbNature.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbNature.Size = New System.Drawing.Size(106, 22)
        Me.cmbNature.TabIndex = 0
        Me.cmbNature.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbNature.PropBag = resources.GetString("cmbNature.PropBag")
        '
        'Label24
        '
        Me.Label24.AutoSize = True
        Me.Label24.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label24.Location = New System.Drawing.Point(14, 27)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(39, 13)
        Me.Label24.TabIndex = 39
        Me.Label24.Text = "Nature"
        '
        'fReglementClient
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(835, 431)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fReglementClient"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        CType(Me.gVentes, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox13.ResumeLayout(False)
        Me.GroupBox13.PerformLayout()
        CType(Me.tTaux, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtEcheance, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNomInscrit, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbBanque, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tLibelle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tMontant, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNumeroCheque, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbNature, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents GroupBox13 As System.Windows.Forms.GroupBox
    Friend WithEvents lNumeroReglement As System.Windows.Forms.Label
    Friend WithEvents dtEcheance As C1.Win.C1Input.C1DateEdit
    Friend WithEvents tNomInscrit As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label20 As System.Windows.Forms.Label
    Friend WithEvents cmbBanque As C1.Win.C1List.C1Combo
    Friend WithEvents Label19 As System.Windows.Forms.Label
    Friend WithEvents tLibelle As C1.Win.C1Input.C1TextBox
    Friend WithEvents chbEncaisse As System.Windows.Forms.CheckBox
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents tMontant As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label18 As System.Windows.Forms.Label
    Friend WithEvents tNumeroCheque As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents cmbNature As C1.Win.C1List.C1Combo
    Friend WithEvents Label24 As System.Windows.Forms.Label
    Friend WithEvents bconfirmerReglement As C1.Win.C1Input.C1Button
    Friend WithEvents bannulerReglement As C1.Win.C1Input.C1Button
    Friend WithEvents gVentes As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
    Friend WithEvents CR As Pharma2000Premium.EtatDeBonDeReglement
    Friend WithEvents CR1 As Pharma2000Premium.EtatRecuReglement
    Friend WithEvents bCocherTous As C1.Win.C1Input.C1Button
    Friend WithEvents SerialPortTiroir As System.IO.Ports.SerialPort
    Friend WithEvents lSoldeClient As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents tTaux As C1.Win.C1Input.C1TextBox
    Friend WithEvents ltaux As System.Windows.Forms.Label
    Friend WithEvents chbTaux As System.Windows.Forms.CheckBox
End Class
