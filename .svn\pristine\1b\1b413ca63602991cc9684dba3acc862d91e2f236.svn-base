﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fReglementCNAM
    Dim cmdReglement As New SqlCommand
    Dim cbReglement As New SqlCommandBuilder
    Dim dsReglement As New DataSet
    Dim daReglement As New SqlDataAdapter

    Public ajoutmodif As String = ""
    Public CodeReglement As Integer = 0

    Dim StrSQL As String = ""
    Dim StrSQL1 As String = ""
    Dim i As Integer = 0
    Dim NumeroPremierVente As String = ""

    Dim x As Integer = 0

    Public Sub init()

        Dim i As Integer = 0
        'chargement des Banque
        StrSQL1 = "SELECT DISTINCT CodeBanque,NomBanque FROM BANQUE ORDER BY NomBanque ASC"
        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL1
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsReglement, "BANQUE")
        cmbBanque.DataSource = dsReglement.Tables("BANQUE")
        cmbBanque.ValueMember = "CodeBanque"
        cmbBanque.DisplayMember = "NomBanque"
        cmbBanque.ColumnHeaders = False
        cmbBanque.Splits(0).DisplayColumns("CodeBanque").Visible = False
        cmbBanque.Splits(0).DisplayColumns("NomBanque").Width = 10
        cmbBanque.ExtendRightColumn = True

        'chargement des Nature reglements
        StrSQL1 = "SELECT DISTINCT CodeNatureReglement,LibelleNatureReglement FROM NATURE_REGLEMENT ORDER BY LibelleNatureReglement ASC"
        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL1
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsReglement, "NATURE_REGLEMENT")
        cmbNature.DataSource = dsReglement.Tables("NATURE_REGLEMENT")
        cmbNature.ValueMember = "CodeNatureReglement"
        cmbNature.DisplayMember = "LibelleNatureReglement"
        cmbNature.ColumnHeaders = False
        cmbNature.Splits(0).DisplayColumns("CodeNatureReglement").Visible = False
        cmbNature.Splits(0).DisplayColumns("LibelleNatureReglement").Width = 10
        cmbNature.ExtendRightColumn = True

        If (dsReglement.Tables.IndexOf("VENTE_CNAM") > -1) Then
            dsReglement.Tables("VENTE_CNAM").Clear()
        End If

        ' chargement des ventes 
        If ajoutmodif = "A" Then
            StrSQL = "SELECT CAST(0 as bit) Cocher," + _
                "NumeroVente," + _
                "Date," + _
                "TotalHT," + _
                "TotalTTC," + _
                "TVA," + _
                "TotalRemise AS Remise " + _
                "FROM VENTE " + _
                "WHERE MontantCnam<>0" + _
                " AND NumeroVente NOT IN (SELECT NumeroVente FROM REGLEMENT_CNAM_VENTE " + _
                " WHERE REGLEMENT_CNAM_VENTE.NumeroVente=NumeroVente)" + _
                " ORDER BY Date"
        ElseIf ajoutmodif = "M" Then
            StrSQL = "SELECT CAST(0 as bit) Cocher," + _
                "NumeroVente," + _
                "Date," + _
                "TotalHT," + _
                "TotalTTC," + _
                "TVA," + _
                "TotalRemise AS Remise " + _
                "FROM VENTE " + _
                "WHERE MontantCnam<>0" + _
                " AND (NumeroVente IN (SELECT NumeroVente FROM REGLEMENT_CNAM_VENTE " + _
                " WHERE NumeroReglementCnam=" + CodeReglement.ToString + _
                " ) OR NumeroVente NOT IN (SELECT NumeroVente FROM REGLEMENT_CNAM_VENTE)) ORDER BY Date"
        End If


        cmdReglement.Connection = ConnectionServeur
        cmdReglement.CommandText = StrSQL
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsReglement, "VENTE_CNAM")
        cbReglement = New SqlCommandBuilder(daReglement)

        With gAchats
            .Columns.Clear()
            Try
                .DataSource = dsReglement
            Catch ex As Exception
            End Try
            .DataMember = "VENTE_CNAM"
            .Rebind(False)
            .Columns("Cocher").Caption = "Cocher"
            .Columns("NumeroVente").Caption = "Numero Vente"
            .Columns("Date").Caption = "Date"
            .Columns("TotalHT").Caption = "Total HT"
            .Columns("TotalTTC").Caption = "Total TTC"
            .Columns("TVA").Caption = "TVA"
            .Columns("Remise").Caption = "Remise"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("TotalHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Remise").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("Cocher").Locked = False

            .Splits(0).DisplayColumns("Cocher").Width = 45
            .Splits(0).DisplayColumns("NumeroVente").Width = 100
            .Splits(0).DisplayColumns("Date").Width = 70
            .Splits(0).DisplayColumns("TotalHT").Width = 100
            .Splits(0).DisplayColumns("TotalTTC").Width = 100
            .Splits(0).DisplayColumns("TVA").Width = 100
            .Splits(0).DisplayColumns("Remise").Width = 100

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gAchats)
        End With
        gAchats.HighLightRowStyle.ForeColor = Color.Black

        tNomInscrit.Enabled = True
        tMontant.Enabled = True
        'chbEncaisse.Enabled = True
        dtEcheance.Enabled = True
        tNumeroCheque.Enabled = True
        tLibelle.Enabled = True
        cmbNature.Enabled = True
        cmbBanque.Enabled = True

        If ajoutmodif = "A" Then

            tNomInscrit.Value = ""
            tMontant.Value = "0.000"
            chbEncaisse.Checked = False
            dtEcheance.Value = System.DateTime.Today
            tNumeroCheque.Value = ""
            tLibelle.Value = ""
            cmbNature.Text = ""
            cmbBanque.Text = ""
            cmbNature.Focus()

        ElseIf ajoutmodif = "M" Then

            tNomInscrit.Value = RecupererValeurExecuteScalaire("NomInscritSurLeCheque", "REGLEMENT_CNAM", "NumeroReglementCnam", CodeReglement)
            tMontant.Value = Format(RecupererValeurExecuteScalaire("Montant", "REGLEMENT_CNAM", "NumeroReglementCnam", CodeReglement), "# ###.000")
            chbEncaisse.Checked = False
            dtEcheance.Value = RecupererValeurExecuteScalaire("DateEcheance", "REGLEMENT_CNAM", "NumeroReglementCnam", CodeReglement)
            tNumeroCheque.Value = RecupererValeurExecuteScalaire("NumeroCheque", "REGLEMENT_CNAM", "NumeroReglementCnam", CodeReglement)
            tLibelle.Value = RecupererValeurExecuteScalaire("LibelleReglement", "REGLEMENT_CNAM", "NumeroReglementCnam", CodeReglement)
            cmbNature.SelectedValue = RecupererValeurExecuteScalaire("CodeNatureReglement", "REGLEMENT_CNAM", "NumeroReglementCnam", CodeReglement)
            lNumeroReglement.Text = CodeReglement

            If (RecupererValeurExecuteScalaire("CodeBanque", "REGLEMENT_CNAM", "NumeroReglementCnam", CodeReglement)) = "" Then
                cmbBanque.Text = ""
            Else
                cmbBanque.SelectedValue = RecupererValeurExecuteScalaire("CodeBanque", "REGLEMENT_CNAM", "NumeroReglementCnam", CodeReglement)
            End If

            Dim Existe As Integer = 0
            For i = 0 To gAchats.RowCount - 1
                StrSQL = " SELECT COUNT(NumeroVente) FROM REGLEMENT_CNAM_VENTE WHERE " + _
                         "NumeroReglementCnam=" + CodeReglement.ToString + " AND NumeroVente='" + _
                         gAchats(i, "NumeroVente") + "'"

                cmdReglement.Connection = ConnectionServeur
                cmdReglement.CommandText = StrSQL

                Try
                    Existe = cmdReglement.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If Existe <> 0 Then
                    gAchats(i, "Cocher") = True
                    Existe = 0
                End If
            Next

        End If

        dtEcheance.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtEcheance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        If ajoutmodif = "A" Then
            cmbNature.Text = "ESPECE"
            chbEncaisse.Checked = True
        Else
            chbEncaisse.Checked = False
            chbEncaisse.Enabled = True
        End If
    End Sub


    Private Sub bconfirmerReglement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bconfirmerReglement.Click
        Dim StrSQL As String = ""
        Dim NumeroReglement As Integer = 0
        Dim i As Integer = 0
        Dim Reste As Double = 0.0
        Dim MontantAEnregistrer As Double = 0.0
        Dim CodeNatureReglement As Integer = 0
        Dim DateEcheance As String = ""
        Dim Montant As Double = 0.0
        Dim CodeBanque As String = ""

        If cmbNature.Text = "" Then
            MsgBox("Vous devez sélectionner une nature", MsgBoxStyle.OkOnly)
            Exit Sub
        End If
        If CDbl(tMontant.Text = 0) Then
            MsgBox("Montant null", MsgBoxStyle.OkOnly)
            tMontant.Focus()
            Exit Sub
        End If

        '------------------------------ demande du mot de passe
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        Dim myMotDePasse As New fMotDePasse

        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If

        If ajoutmodif = "A" Then
            Exit Sub
            '------------- ajout du nouveau reglement client

            StrSQL = " SELECT max(NumeroReglementCnam) FROM REGLEMENT_CNAM"
            cmdReglement.Connection = ConnectionServeur
            cmdReglement.CommandText = StrSQL
            Try
                NumeroReglement = cmdReglement.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            NumeroReglement = NumeroReglement + 1

            CodeNatureReglement = cmbNature.SelectedValue

            If dtEcheance.Text <> "" Then
                DateEcheance = dtEcheance.Text
            Else
                DateEcheance = "Null"
            End If

            If tMontant.Text = "" Then
                Montant = "0"
            Else
                Montant = tMontant.Text
            End If

            If cmbBanque.Text <> "" Then
                CodeBanque = cmbBanque.SelectedValue
            Else
                CodeBanque = "Null"
            End If

            StrSQL = "INSERT INTO REGLEMENT_CNAM " + _
                     "(""NumeroReglementCnam"",""LibelleReglement"",""CodeNatureReglement""" + _
                     ",""Date"",""DateEcheance"",""Montant"",""NumeroCheque"",""NumeroPoste""" + _
                     ",""NomInscritSurLeCheque"",""CodeBanque"",""Vider"",""Encaisse""," + _
                     """CodePersonnel"") VALUES('" + NumeroReglement.ToString + _
                     "','" + tLibelle.Text + _
                     "','" + CodeNatureReglement.ToString + _
                     "','" + System.DateTime.Now.ToString + _
                     "','" + DateEcheance + _
                     "','" + Montant.ToString + _
                     "','" + tNumeroCheque.Text + _
                     "','" + Environment.MachineName.ToString + _
                     "','" + tNomInscrit.Text + _
                     "'," + CodeBanque + _
                     ",'" + "False" + _
                     "','" + chbEncaisse.Checked.ToString + _
                      "','" + CodeOperateur.ToString + " ')"

            cmdReglement.Connection = ConnectionServeur
            cmdReglement.CommandText = StrSQL
            Try
                cmdReglement.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

        Else

            '--------------------- suppression des afféctations des anciens achats à ce règlement
            Try
                cmdReglement.Connection = ConnectionServeur
                cmdReglement.CommandText = "DELETE FROM REGLEMENT_CNAM_VENTE WHERE NumeroReglementCnam = " + CodeReglement.ToString
                cmdReglement.ExecuteNonQuery()
            Catch ex As Exception
                MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
            End Try

            '---------------------------------------------------------------------------------------

            If dtEcheance.Text <> "" Then
                DateEcheance = dtEcheance.Text
            Else
                DateEcheance = "Null"
            End If

            If tMontant.Text = "" Then
                Montant = "0"
            Else
                Montant = tMontant.Text
            End If

            If cmbBanque.Text <> "" Then
                CodeBanque = cmbBanque.SelectedValue
            Else
                CodeBanque = "Null"
            End If

            StrSQL = " UPDATE REGLEMENT_CNAM SET LibelleReglement='" + tLibelle.Text + _
                     "',CodeNatureReglement=" + cmbNature.SelectedValue.ToString + _
                     ",Date='" + System.DateTime.Today + _
                     "',DateEcheance='" + DateEcheance + _
                     "',Montant=" + Montant.ToString + _
                     ",NumeroCheque='" + tNumeroCheque.Text + _
                     "',NumeroPoste='" + Environment.MachineName.ToString + _
                     "',NomInscritSurLeCheque='" + tNomInscrit.Text + _
                     "',CodeBanque=" + CodeBanque + _
                     ",CodePersonnel=" + CodeOperateur + _
                     ",Encaisse='" + chbEncaisse.Checked.ToString + _
                     "' WHERE NumeroReglementCnam='" + CodeReglement.ToString + "'"

            cmdReglement.Connection = ConnectionServeur
            cmdReglement.CommandText = StrSQL
            Try
                cmdReglement.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            NumeroReglement = CodeReglement
        End If

        '----------- ajout des ventes règlées par ce règlement dans la table règlement_cnam_vente

        Reste = Convert.ToDouble(tMontant.Text)
        For i = 0 To dsReglement.Tables("VENTE_CNAM").Rows.Count - 1
            If dsReglement.Tables("VENTE_CNAM").Rows(i).Item("Cocher") = True Then

                If Reste > dsReglement.Tables("VENTE_CNAM").Rows(i).Item("TotalTTC") Then
                    MontantAEnregistrer = dsReglement.Tables("VENTE_CNAM").Rows(i).Item("TotalTTC")
                    Reste = Reste - dsReglement.Tables("VENTE_CNAM").Rows(i).Item("TotalTTC")
                Else
                    MontantAEnregistrer = Reste
                    Reste = 0
                End If

                StrSQL = "INSERT INTO REGLEMENT_CNAM_VENTE " + _
                     "(""NumeroReglementCnam"",""NumeroVente"",""MontantRegle"") " + _
                     " VALUES('" + NumeroReglement.ToString + _
                     "','" + dsReglement.Tables("VENTE_CNAM").Rows(i).Item("NumeroVente") + _
                     "','" + MontantAEnregistrer.ToString + "')"

                cmdReglement.Connection = ConnectionServeur
                cmdReglement.CommandText = StrSQL
                Try
                    cmdReglement.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

            End If
        Next
        '------------------------------------------------------------------------------------------
        tNomInscrit.Enabled = False
        tMontant.Enabled = False
        'chbEncaisse.Enabled = False
        dtEcheance.Enabled = False
        tNumeroCheque.Enabled = False
        tLibelle.Enabled = False
        cmbNature.Enabled = False
        cmbBanque.Enabled = False

        bannulerReglement.Enabled = False
        bconfirmerReglement.Enabled = False
        Me.Hide()
    End Sub

    Private Sub bannulerReglement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bannulerReglement.Click
        tNomInscrit.Enabled = False
        tMontant.Enabled = False
        'chbEncaisse.Enabled = False
        dtEcheance.Enabled = False
        tNumeroCheque.Enabled = False
        tLibelle.Enabled = False
        cmbNature.Enabled = False
        cmbBanque.Enabled = False

        bannulerReglement.Enabled = False
        bconfirmerReglement.Enabled = False
        Me.Hide()
    End Sub

    Private Sub tMontant_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tMontant.KeyUp
        Dim TestConversion As Double = 0.0
        Try   ' test si un valeur numerique ou non
            TestConversion = Math.Round(CDbl(tMontant.Text), 3)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tMontant.Text = "0.000"
            tMontant.Focus()
            tMontant.SelectionLength = tMontant.Text.Length
            Exit Sub
        End Try
        If e.KeyCode = Keys.Enter Then
            chbEncaisse.Focus()
        End If

        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub

    Private Sub tMontant_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tMontant.TextChanged

    End Sub

    Private Sub bannulerReglement_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bannulerReglement.KeyUp
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub

    Private Sub cmbNature_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbNature.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLibelle.Focus()
        End If
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub

    Private Sub cmbNature_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbNature.TextChanged
        If cmbNature.Text = "ESPECE" Or cmbNature.Text = "CARTE" Then
            cmbBanque.Enabled = False
            dtEcheance.Enabled = False
            tNumeroCheque.Enabled = False
            tNomInscrit.Enabled = False
        Else
            cmbBanque.Enabled = True
            dtEcheance.Enabled = True
            tNumeroCheque.Enabled = True
            tNomInscrit.Enabled = True
        End If

        dtEcheance.Value = System.DateTime.Today
    End Sub

    Private Sub dtEcheance_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtEcheance.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbBanque.Focus()
        End If
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub

    Private Sub dtEcheance_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtEcheance.TextChanged
        If dtEcheance.Value > System.DateTime.Today Then
            chbEncaisse.Checked = False
        Else
            chbEncaisse.Checked = True
        End If
    End Sub

    Private Sub cmbBanque_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbBanque.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNumeroCheque.Focus()
        End If
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub

    Private Sub cmbBanque_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbBanque.TextChanged

    End Sub

    Private Sub tLibelle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tLibelle.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNomInscrit.Focus()
            Exit Sub
        End If
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub

    Private Sub tLibelle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tLibelle.TextChanged

    End Sub

    Private Sub tNomInscrit_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNomInscrit.KeyUp
        If e.KeyCode = Keys.Enter Then
            tMontant.Focus()
        End If
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub

    Private Sub tNomInscrit_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNomInscrit.TextChanged

    End Sub

    Private Sub gAchats_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gAchats.Click

    End Sub

    Private Sub gAchats_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gAchats.KeyUp
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub

    Private Sub chbEncaisse_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbEncaisse.CheckedChanged

    End Sub

    Private Sub chbEncaisse_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles chbEncaisse.KeyUp
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub

    Private Sub bconfirmerReglement_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bconfirmerReglement.KeyUp
        If e.KeyCode = Keys.F3 Then
            bconfirmerReglement_Click(sender, e)
        ElseIf e.KeyCode = Keys.F10 Then
            bannulerReglement_Click(sender, e)
        End If
    End Sub
End Class