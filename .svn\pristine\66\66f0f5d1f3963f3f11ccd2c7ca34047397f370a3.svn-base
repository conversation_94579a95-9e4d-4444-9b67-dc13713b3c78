﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <connectionStrings>
    <add name="SaleReportEntities" connectionString="metadata=res://*/SaleReport.csdl|res://*/SaleReport.ssdl|res://*/SaleReport.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=MARWEN\SQLSRV;initial catalog=Pharma;persist security info=True;user id=sa;password=********;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="ReportEntities" connectionString="metadata=res://*/Reports.csdl|res://*/Reports.ssdl|res://*/Reports.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=MARWEN\SQLSRV;initial catalog=Pharma;persist security info=True;user id=sa;password=********;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  </connectionStrings>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework" />
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
</configuration>