﻿Public Class fQuantiteADelivrer

    Public NomArticle As String = " "
    Public Quantite As Integer = 0

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Me.Hide()
    End Sub

    Private Sub fQuantiteADelivrer_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ltitre.Text = "Calcul de la quantité à délivrer pour:  " + NomArticle
        rdbJour.Checked = True
        rdbGoutte.Checked = True
        With cmbForme
            .HoldFields()
            .AddItem("SIROP")
            .AddItem("GOUTTE")
            .AddItem("AUTRE")
            .ColumnHeaders = False
        End With
        tDureDeTraitement.Focus()
    End Sub

    Private Sub bOk_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bCalculer.Click

        If tDureDeTraitement.Text = "" Or tQuantitePrescrire.Text = "" Or tNombreDePrise.Text = "" Or tContenanceDuProduit.Text = "" Or cmbForme.Text = "" Then
            MsgBox("Données incomplètes !!", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        Dim DureDeTrairement As Integer = 0
        Dim QuantiteTraite As Double = 0.0

        If rdbJour.Checked = True Then
            DureDeTrairement = tDureDeTraitement.Text
        ElseIf rdbSemaine.Checked = True Then
            DureDeTrairement = Convert.ToInt32(tDureDeTraitement.Text) / 7
        ElseIf rdbMois.Checked = True Then
            DureDeTrairement = Convert.ToInt32(tDureDeTraitement.Text) / 30
        ElseIf rdb1JourSur2.Checked = True Then
            DureDeTrairement = Convert.ToInt32(tDureDeTraitement.Text) / 2
        ElseIf rdb5jourSemaine.Checked = True Then
            'DureDeTrairement = Convert.ToInt32(tDureDeTraitement.Text) * 5 / 30
            DureDeTrairement = Int(Convert.ToInt32(tDureDeTraitement.Text) / 7) * 5
        End If

        If rdbGoutte.Checked = True Then
            QuantiteTraite = tQuantitePrescrire.Text * 0.05
        ElseIf rdbCCafe.Checked = True Then
            QuantiteTraite = tQuantitePrescrire.Text * 5
        ElseIf rdbCDessert.Checked = True Then
            QuantiteTraite = tQuantitePrescrire.Text * 10
        ElseIf rdbCSouppe.Checked = True Then
            QuantiteTraite = tQuantitePrescrire.Text * 15
        ElseIf rdbCpInj.Checked = True Then
            QuantiteTraite = tQuantitePrescrire.Text
        End If

        lQuantite.Text = (QuantiteTraite * DureDeTrairement * Convert.ToInt32(tNombreDePrise.Text)) / Convert.ToInt32(tContenanceDuProduit.Text)
        If lQuantite.Text > Int(lQuantite.Text) Then
            lQuantite.Text = Int(lQuantite.Text + 1)
        End If

        Quantite = lQuantite.Text
        'Me.Hide()
    End Sub

    Private Sub tDureDeTraitement_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tDureDeTraitement.KeyUp
        If e.KeyData = Keys.Enter Then
            tQuantitePrescrire.Focus()
        End If
    End Sub

    Private Sub tDureDeTraitement_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tDureDeTraitement.TextChanged

    End Sub

    Private Sub tQuantitePrescrire_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tQuantitePrescrire.KeyUp
        If e.KeyData = Keys.Enter Then
            tNombreDePrise.Focus()
        End If
    End Sub

    Private Sub tQuantitePrescrire_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tQuantitePrescrire.TextChanged

    End Sub

    Private Sub tNombreDePrise_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNombreDePrise.KeyUp
        If e.KeyData = Keys.Enter Then
            tContenanceDuProduit.Focus()
        End If
    End Sub

    Private Sub tNombreDePrise_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNombreDePrise.TextChanged

    End Sub

    Private Sub tContenanceDuProduit_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tContenanceDuProduit.KeyUp
        If e.KeyData = Keys.Enter Then
            cmbForme.Focus()
        End If
    End Sub

    Private Sub tContenanceDuProduit_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tContenanceDuProduit.TextChanged

    End Sub

    Private Sub cmbForme_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyData = Keys.Enter Then
            rdbGoutte.Focus()
        End If
    End Sub

    Private Sub cmbForme_KeyUp1(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbForme.KeyUp
        If e.KeyCode = Keys.Enter Then
            bCalculer.Focus()
        End If
    End Sub

  

    Private Sub cmbForme_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbForme.TextChanged

        If cmbForme.Text = "SIROP" Then
            rdbGoutte.Enabled = False
            rdbCCafe.Enabled = True
            rdbCDessert.Enabled = True
            rdbCSouppe.Enabled = True
            rdbCpInj.Enabled = False

            rdbCCafe.Checked = True
        ElseIf cmbForme.Text = "GOUTTE" Then
            rdbGoutte.Enabled = True
            rdbCCafe.Enabled = False
            rdbCDessert.Enabled = False
            rdbCSouppe.Enabled = False
            rdbCpInj.Enabled = False

            rdbGoutte.Checked = True

        ElseIf cmbForme.Text = "AUTRE" Then
            rdbGoutte.Enabled = False
            rdbCCafe.Enabled = False
            rdbCDessert.Enabled = False
            rdbCSouppe.Enabled = False
            rdbCpInj.Enabled = True

            rdbCpInj.Checked = True
        End If

    End Sub
End Class