﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fVille
    Dim cmdVille As New SqlCommand
    Dim daVille As New SqlDataAdapter
    Dim cbVille As New SqlCommandBuilder
    Dim dsVille As New DataSet

    Dim xVille As Integer
    Dim ModeVille As String
    Dim CodeVille As String

    Dim dsRecupereNum As New DataSet
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNumt As New SqlDataAdapter

    Dim CodeExiste As Boolean = False
    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub
    Public Sub afficherville()
        Dim I As Integer
        Dim Cond As String = "1=1"
        dsVille.Clear()
        cmdVille.CommandText = " SELECT * " + _
                                   " FROM VILLE as VILLE_LISTE where SupprimeVille = 0 AND " + Cond + _
                                   " ORDER BY CodeVille"

        cmdVille.Connection = ConnectionServeur
        daVille = New SqlDataAdapter(cmdVille)
        daVille.Fill(dsVille, "VILLE_LISTE")

        With gVille
            .Columns.Clear()
            .DataSource = dsVille
            .DataMember = "VILLE_LISTE"
            .Rebind(False)
            .Columns("CodeVille").Caption = "Code de la ville"
            .Columns("NomVille").Caption = "Nom de la Ville"
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("CodeVille").Width = 100
            .Splits(0).DisplayColumns("CodeVille").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("NomVille").Width = 80
            .Splits(0).DisplayColumns("NomVille").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("CodeVille").Locked = True
            .Splits(0).DisplayColumns("NomVille").Locked = True
            .Splits(0).DisplayColumns("SupprimeVille").Visible = False
            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True

            'Style du Caractere et du grid
            ParametreGrid(gVille)
        End With
        gVille.MoveRelative(xVille)
        cbVille = New SqlCommandBuilder(daVille)
    End Sub

    Private Sub bAjouterVille_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterVille.Click
        Dim dr As DataRow
        Dim StrSQL As String = ""

        If tCodeVille.Text = "" Then
            MsgBox("Veuillez saisir le code de la ville !", MsgBoxStyle.Critical, "Erreur")
            tCodeVille.Focus()
            Exit Sub
        End If
        If tNomVille.Text = "" Then
            MsgBox("Veuillez saisir le nom de la ville !", MsgBoxStyle.Critical, "Erreur")
            tNomVille.Focus()
            Exit Sub
        End If

        If CodeExiste = True Then
            MsgBox("Code Ville existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
            tCodeVille.Focus()
            Exit Sub
        End If

        With dsVille
            dr = .Tables("VILLE_LISTE").NewRow
            dr.Item("NomVille") = tNomVille.Text
            dr.Item("CodeVille") = tCodeVille.Text
            .Tables("VILLE_LISTE").Rows.Add(dr)
        End With

        Try
            daVille.Update(dsVille, "VILLE_LISTE")
            afficherville()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsVille.Reset()
        End Try

        tCodeVille.Text = ""
        tNomVille.Text = ""
        lTest.Visible = False
    End Sub

    Private Sub bSupprimerVille_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerVille.Click
        Dim cmd As New SqlCommand
        If gVille.RowCount > 0 Then
            If MsgBox("Voulez vous vraiment supprimer cette ville " + Quote(gVille(gVille.Row, "NomVille")) + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "UPDATE VILLE SET SupprimeVille = 1 WHERE CodeVille =" + Quote(gVille(gVille.Row, "CodeVille"))
                    cmd.ExecuteNonQuery()
                    afficherville()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub

    Private Sub gVille_AfterColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gVille.AfterColEdit
        Dim dr As DataRow
        Dim StrSQL As String = ""

        With dsVille.Tables("VILLE")
            dr = .Rows(0)
            dr.Item("CodeVille") = gVille(gVille.Row, "CodeVille")
            dr.Item("NomVille") = gVille(gVille.Row, "NomVille")
        End With

        Try
            daVille.Update(dsVille, "VILLE")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
        End Try

        afficherville()
    End Sub

    Private Sub gVille_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles gVille.Click
        Dim StrSQL As String = ""
        CodeVille = Quote(gVille(gVille.Row, "CodeVille"))
        If CodeVille = "" Then
            MsgBox("Veuillez sélectionner une ville !", MsgBoxStyle.Critical, "Erreur")
            gVille.Focus()
            Exit Sub
        End If

        If (dsVille.Tables.IndexOf("VILLE") > -1) Then
            dsVille.Tables("VILLE").Clear()
        End If

        StrSQL = " SELECT * FROM VILLE WHERE CodeVille = " + CodeVille

        cmdVille.Connection = ConnectionServeur
        cmdVille.CommandText = StrSQL
        daVille = New SqlDataAdapter(cmdVille)
        daVille.Fill(dsVille, "VILLE")
        cbVille = New SqlCommandBuilder(daVille)
    End Sub
    Public Sub Init()
        afficherville()
    End Sub

    Private Sub tCodeVille_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeVille.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNomVille.Focus()
        End If
    End Sub

    Private Sub tCodeVille_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeVille.LostFocus
        lTest.Visible = False
    End Sub

    Private Sub tCodeVille_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeVille.TextChanged

        If tCodeVille.Text <> "" Then
            If IsNumeric(tCodeVille.Text.Substring(Len(tCodeVille.Text) - 1, 1)) = False Then
                MsgBox("Veuillez introduisez des chiffres !", MsgBoxStyle.Critical, "Erreur")
                tCodeVille.Text = tCodeVille.Text.Substring(0, Len(tCodeVille.Text) - 1)
                tCodeVille.Select(Len(tCodeVille.Text), 0)
            End If
        End If

        Dim StrSQLtest As String = ""
        dsRecupereNum.Clear()

        StrSQLtest = " SELECT * FROM VILLE WHERE CodeVille=" + Quote(tCodeVille.Text)
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQLtest
        daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
        daRecupereNumt.Fill(dsRecupereNum, "CLIENT")

        If dsRecupereNum.Tables("CLIENT").Rows.Count <> 0 Then
            lTest.Text = "Code non valide déja existe"
            lTest.ForeColor = Color.OrangeRed
            lTest.Visible = True
            CodeExiste = True
        Else
            lTest.Text = "Code valide"
            lTest.ForeColor = Color.LawnGreen
            lTest.Visible = True
            CodeExiste = False
        End If
    End Sub

    Private Sub tNomVille_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNomVille.KeyUp
        If e.KeyCode = Keys.Enter Then
            bAjouterVille.Focus()
        End If
    End Sub

    Private Sub tNomVille_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNomVille.TextChanged
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub
End Class