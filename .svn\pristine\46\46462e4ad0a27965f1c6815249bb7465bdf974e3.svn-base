﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fInventaire
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fInventaire))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bFicheArticle = New C1.Win.C1Input.C1Button()
        Me.bSupprimer = New C1.Win.C1Input.C1Button()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.lTypeInventaire = New System.Windows.Forms.Label()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.bAfficherLot = New C1.Win.C1Input.C1Button()
        Me.GroupeJauge = New System.Windows.Forms.GroupBox()
        Me.lTitreLabelle = New System.Windows.Forms.Label()
        Me.ProgressBar = New System.Windows.Forms.ProgressBar()
        Me.lArticle = New System.Windows.Forms.Label()
        Me.lArticleEnCours = New System.Windows.Forms.Label()
        Me.PanelAfficherLots = New System.Windows.Forms.Panel()
        Me.bAjouterLot = New C1.Win.C1Input.C1Button()
        Me.bOkLot = New C1.Win.C1Input.C1Button()
        Me.bAnnulerLot = New C1.Win.C1Input.C1Button()
        Me.lSommeQte = New System.Windows.Forms.Label()
        Me.gListeLotArticle = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.gListeRecherche = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.lNombreDesArticles = New System.Windows.Forms.Label()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.lDifferenceAchatTTCPourcentage = New System.Windows.Forms.Label()
        Me.lDifferenceVenteTTCPourcentage = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.lDifferenceAchatTTC = New System.Windows.Forms.Label()
        Me.lDifferenceVenteTTC = New System.Windows.Forms.Label()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.lInitialeAchatTTC = New System.Windows.Forms.Label()
        Me.lInitialeVenteTTC = New System.Windows.Forms.Label()
        Me.GroupeRemarque = New System.Windows.Forms.GroupBox()
        Me.tRemarqueInventaire = New C1.Win.C1Input.C1TextBox()
        Me.bLast = New C1.Win.C1Input.C1Button()
        Me.bNext = New C1.Win.C1Input.C1Button()
        Me.GroupeNumero = New System.Windows.Forms.GroupBox()
        Me.tRecherche = New C1.Win.C1Input.C1TextBox()
        Me.lOperateur = New System.Windows.Forms.Label()
        Me.lDateInventaire = New System.Windows.Forms.Label()
        Me.LNumero = New System.Windows.Forms.Label()
        Me.LVille = New System.Windows.Forms.Label()
        Me.lNumeroInventaire = New System.Windows.Forms.Label()
        Me.bFirst = New C1.Win.C1Input.C1Button()
        Me.bPrevious = New C1.Win.C1Input.C1Button()
        Me.bAjouter = New C1.Win.C1Input.C1Button()
        Me.bConfirmer = New C1.Win.C1Input.C1Button()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.lActuelleAchatTTC = New System.Windows.Forms.Label()
        Me.lActuelleVenteTTC = New System.Windows.Forms.Label()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.gArticles = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bTerminal = New C1.Win.C1Input.C1Button()
        Me.bRecherche = New C1.Win.C1Input.C1Button()
        Me.bListe = New C1.Win.C1Input.C1Button()
        Me.bImprimer = New C1.Win.C1Input.C1Button()
        Me.CR = New Pharma2000Premium.EtatInventaire()
        Me.CRtemp = New Pharma2000Premium.EtatInventaireTemporaire()
        Me.Panel.SuspendLayout()
        Me.GroupBox4.SuspendLayout()
        Me.GroupeJauge.SuspendLayout()
        Me.PanelAfficherLots.SuspendLayout()
        CType(Me.gListeLotArticle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gListeRecherche, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox3.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        Me.GroupeRemarque.SuspendLayout()
        CType(Me.tRemarqueInventaire, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupeNumero.SuspendLayout()
        CType(Me.tRecherche, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox2.SuspendLayout()
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.bFicheArticle)
        Me.Panel.Controls.Add(Me.bSupprimer)
        Me.Panel.Controls.Add(Me.GroupBox4)
        Me.Panel.Controls.Add(Me.bAfficherLot)
        Me.Panel.Controls.Add(Me.GroupeJauge)
        Me.Panel.Controls.Add(Me.PanelAfficherLots)
        Me.Panel.Controls.Add(Me.gListeRecherche)
        Me.Panel.Controls.Add(Me.lNombreDesArticles)
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.GroupBox3)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Controls.Add(Me.GroupeRemarque)
        Me.Panel.Controls.Add(Me.bLast)
        Me.Panel.Controls.Add(Me.bNext)
        Me.Panel.Controls.Add(Me.GroupeNumero)
        Me.Panel.Controls.Add(Me.bFirst)
        Me.Panel.Controls.Add(Me.bPrevious)
        Me.Panel.Controls.Add(Me.bAjouter)
        Me.Panel.Controls.Add(Me.bConfirmer)
        Me.Panel.Controls.Add(Me.GroupBox2)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.gArticles)
        Me.Panel.Controls.Add(Me.bTerminal)
        Me.Panel.Controls.Add(Me.bRecherche)
        Me.Panel.Controls.Add(Me.bListe)
        Me.Panel.Controls.Add(Me.bImprimer)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1077, 566)
        Me.Panel.TabIndex = 3
        '
        'bFicheArticle
        '
        Me.bFicheArticle.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bFicheArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.bFicheArticle.Location = New System.Drawing.Point(12, 425)
        Me.bFicheArticle.Name = "bFicheArticle"
        Me.bFicheArticle.Size = New System.Drawing.Size(105, 33)
        Me.bFicheArticle.TabIndex = 99
        Me.bFicheArticle.Text = "Fiche article F1"
        Me.bFicheArticle.UseVisualStyleBackColor = True
        Me.bFicheArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bSupprimer
        '
        Me.bSupprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bSupprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.asupprimer
        Me.bSupprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSupprimer.Location = New System.Drawing.Point(475, 518)
        Me.bSupprimer.Name = "bSupprimer"
        Me.bSupprimer.Size = New System.Drawing.Size(80, 39)
        Me.bSupprimer.TabIndex = 98
        Me.bSupprimer.Text = "Supprimer" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "       F7"
        Me.bSupprimer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSupprimer.UseVisualStyleBackColor = True
        Me.bSupprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.lTypeInventaire)
        Me.GroupBox4.Controls.Add(Me.Label9)
        Me.GroupBox4.Location = New System.Drawing.Point(12, 8)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(200, 103)
        Me.GroupBox4.TabIndex = 82
        Me.GroupBox4.TabStop = False
        '
        'lTypeInventaire
        '
        Me.lTypeInventaire.AutoSize = True
        Me.lTypeInventaire.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTypeInventaire.Location = New System.Drawing.Point(29, 80)
        Me.lTypeInventaire.Name = "lTypeInventaire"
        Me.lTypeInventaire.Size = New System.Drawing.Size(11, 13)
        Me.lTypeInventaire.TabIndex = 12
        Me.lTypeInventaire.Text = "-"
        Me.lTypeInventaire.Visible = False
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Cursor = System.Windows.Forms.Cursors.Default
        Me.Label9.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Label9.Font = New System.Drawing.Font("Microsoft Sans Serif", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label9.Location = New System.Drawing.Point(9, 27)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(181, 42)
        Me.Label9.TabIndex = 10
        Me.Label9.Text = "Inventaire"
        Me.Label9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bAfficherLot
        '
        Me.bAfficherLot.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bAfficherLot.Enabled = False
        Me.bAfficherLot.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.bAfficherLot.Location = New System.Drawing.Point(12, 425)
        Me.bAfficherLot.Name = "bAfficherLot"
        Me.bAfficherLot.Size = New System.Drawing.Size(105, 33)
        Me.bAfficherLot.TabIndex = 77
        Me.bAfficherLot.Text = "Afficher les lots F2"
        Me.bAfficherLot.UseVisualStyleBackColor = True
        Me.bAfficherLot.Visible = False
        Me.bAfficherLot.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupeJauge
        '
        Me.GroupeJauge.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupeJauge.Controls.Add(Me.lTitreLabelle)
        Me.GroupeJauge.Controls.Add(Me.ProgressBar)
        Me.GroupeJauge.Controls.Add(Me.lArticle)
        Me.GroupeJauge.Controls.Add(Me.lArticleEnCours)
        Me.GroupeJauge.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeJauge.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupeJauge.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.GroupeJauge.Location = New System.Drawing.Point(276, 223)
        Me.GroupeJauge.Name = "GroupeJauge"
        Me.GroupeJauge.Size = New System.Drawing.Size(536, 94)
        Me.GroupeJauge.TabIndex = 51
        Me.GroupeJauge.TabStop = False
        Me.GroupeJauge.Visible = False
        '
        'lTitreLabelle
        '
        Me.lTitreLabelle.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTitreLabelle.Location = New System.Drawing.Point(8, 12)
        Me.lTitreLabelle.Name = "lTitreLabelle"
        Me.lTitreLabelle.Size = New System.Drawing.Size(469, 25)
        Me.lTitreLabelle.TabIndex = 73
        Me.lTitreLabelle.Text = "Analyse en cours "
        Me.lTitreLabelle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'ProgressBar
        '
        Me.ProgressBar.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ProgressBar.Location = New System.Drawing.Point(23, 65)
        Me.ProgressBar.Name = "ProgressBar"
        Me.ProgressBar.Size = New System.Drawing.Size(486, 17)
        Me.ProgressBar.TabIndex = 72
        Me.ProgressBar.Value = 50
        Me.ProgressBar.Visible = False
        '
        'lArticle
        '
        Me.lArticle.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lArticle.AutoSize = True
        Me.lArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lArticle.Location = New System.Drawing.Point(23, 42)
        Me.lArticle.Name = "lArticle"
        Me.lArticle.Size = New System.Drawing.Size(59, 13)
        Me.lArticle.TabIndex = 10
        Me.lArticle.Text = "Article :  "
        '
        'lArticleEnCours
        '
        Me.lArticleEnCours.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lArticleEnCours.BackColor = System.Drawing.Color.Transparent
        Me.lArticleEnCours.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lArticleEnCours.Location = New System.Drawing.Point(86, 42)
        Me.lArticleEnCours.Name = "lArticleEnCours"
        Me.lArticleEnCours.Size = New System.Drawing.Size(359, 15)
        Me.lArticleEnCours.TabIndex = 33
        Me.lArticleEnCours.Text = "-------------"
        '
        'PanelAfficherLots
        '
        Me.PanelAfficherLots.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.PanelAfficherLots.Controls.Add(Me.bAjouterLot)
        Me.PanelAfficherLots.Controls.Add(Me.bOkLot)
        Me.PanelAfficherLots.Controls.Add(Me.bAnnulerLot)
        Me.PanelAfficherLots.Controls.Add(Me.lSommeQte)
        Me.PanelAfficherLots.Controls.Add(Me.gListeLotArticle)
        Me.PanelAfficherLots.Location = New System.Drawing.Point(358, 197)
        Me.PanelAfficherLots.Name = "PanelAfficherLots"
        Me.PanelAfficherLots.Size = New System.Drawing.Size(559, 158)
        Me.PanelAfficherLots.TabIndex = 76
        Me.PanelAfficherLots.Visible = False
        '
        'bAjouterLot
        '
        Me.bAjouterLot.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bAjouterLot.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.bAjouterLot.Location = New System.Drawing.Point(257, 130)
        Me.bAjouterLot.Name = "bAjouterLot"
        Me.bAjouterLot.Size = New System.Drawing.Size(90, 23)
        Me.bAjouterLot.TabIndex = 80
        Me.bAjouterLot.Text = "Ajouter un lot"
        Me.bAjouterLot.UseVisualStyleBackColor = True
        Me.bAjouterLot.Visible = False
        Me.bAjouterLot.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bOkLot
        '
        Me.bOkLot.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bOkLot.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.bOkLot.Location = New System.Drawing.Point(440, 130)
        Me.bOkLot.Name = "bOkLot"
        Me.bOkLot.Size = New System.Drawing.Size(51, 23)
        Me.bOkLot.TabIndex = 79
        Me.bOkLot.Text = "OK"
        Me.bOkLot.UseVisualStyleBackColor = True
        Me.bOkLot.Visible = False
        Me.bOkLot.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAnnulerLot
        '
        Me.bAnnulerLot.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bAnnulerLot.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.bAnnulerLot.Location = New System.Drawing.Point(497, 130)
        Me.bAnnulerLot.Name = "bAnnulerLot"
        Me.bAnnulerLot.Size = New System.Drawing.Size(51, 23)
        Me.bAnnulerLot.TabIndex = 78
        Me.bAnnulerLot.Text = "Annuler"
        Me.bAnnulerLot.UseVisualStyleBackColor = True
        Me.bAnnulerLot.Visible = False
        Me.bAnnulerLot.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lSommeQte
        '
        Me.lSommeQte.BackColor = System.Drawing.Color.Transparent
        Me.lSommeQte.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lSommeQte.ForeColor = System.Drawing.Color.Red
        Me.lSommeQte.Location = New System.Drawing.Point(6, 134)
        Me.lSommeQte.Name = "lSommeQte"
        Me.lSommeQte.Size = New System.Drawing.Size(246, 15)
        Me.lSommeQte.TabIndex = 76
        Me.lSommeQte.Text = "-"
        Me.lSommeQte.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lSommeQte.Visible = False
        '
        'gListeLotArticle
        '
        Me.gListeLotArticle.AlternatingRows = True
        Me.gListeLotArticle.CaptionHeight = 17
        Me.gListeLotArticle.GroupByCaption = "Drag a column header here to group by that column"
        Me.gListeLotArticle.Images.Add(CType(resources.GetObject("gListeLotArticle.Images"), System.Drawing.Image))
        Me.gListeLotArticle.LinesPerRow = 2
        Me.gListeLotArticle.Location = New System.Drawing.Point(1, 1)
        Me.gListeLotArticle.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
        Me.gListeLotArticle.Name = "gListeLotArticle"
        Me.gListeLotArticle.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gListeLotArticle.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gListeLotArticle.PreviewInfo.ZoomFactor = 75.0R
        Me.gListeLotArticle.PrintInfo.PageSettings = CType(resources.GetObject("gListeLotArticle.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gListeLotArticle.RowHeight = 15
        Me.gListeLotArticle.Size = New System.Drawing.Size(554, 126)
        Me.gListeLotArticle.TabIndex = 50
        Me.gListeLotArticle.Text = "C1TrueDBGrid1"
        Me.gListeLotArticle.Visible = False
        Me.gListeLotArticle.PropBag = resources.GetString("gListeLotArticle.PropBag")
        '
        'gListeRecherche
        '
        Me.gListeRecherche.AllowUpdate = False
        Me.gListeRecherche.AlternatingRows = True
        Me.gListeRecherche.CaptionHeight = 17
        Me.gListeRecherche.GroupByCaption = "Drag a column header here to group by that column"
        Me.gListeRecherche.Images.Add(CType(resources.GetObject("gListeRecherche.Images"), System.Drawing.Image))
        Me.gListeRecherche.LinesPerRow = 2
        Me.gListeRecherche.Location = New System.Drawing.Point(114, 162)
        Me.gListeRecherche.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gListeRecherche.Name = "gListeRecherche"
        Me.gListeRecherche.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gListeRecherche.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gListeRecherche.PreviewInfo.ZoomFactor = 75.0R
        Me.gListeRecherche.PrintInfo.PageSettings = CType(resources.GetObject("gListeRecherche.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gListeRecherche.RowHeight = 15
        Me.gListeRecherche.Size = New System.Drawing.Size(610, 209)
        Me.gListeRecherche.TabIndex = 49
        Me.gListeRecherche.Text = "C1TrueDBGrid1"
        Me.gListeRecherche.Visible = False
        Me.gListeRecherche.PropBag = resources.GetString("gListeRecherche.PropBag")
        '
        'lNombreDesArticles
        '
        Me.lNombreDesArticles.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.lNombreDesArticles.AutoSize = True
        Me.lNombreDesArticles.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNombreDesArticles.Location = New System.Drawing.Point(301, 535)
        Me.lNombreDesArticles.Name = "lNombreDesArticles"
        Me.lNombreDesArticles.Size = New System.Drawing.Size(60, 15)
        Me.lNombreDesArticles.TabIndex = 51
        Me.lNombreDesArticles.Text = "Achat TTC"
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(1000, 518)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(65, 39)
        Me.bQuitter.TabIndex = 72
        Me.bQuitter.Text = "Quitter F12"
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox3
        '
        Me.GroupBox3.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox3.Controls.Add(Me.lDifferenceAchatTTCPourcentage)
        Me.GroupBox3.Controls.Add(Me.lDifferenceVenteTTCPourcentage)
        Me.GroupBox3.Controls.Add(Me.Label7)
        Me.GroupBox3.Controls.Add(Me.Label8)
        Me.GroupBox3.Controls.Add(Me.lDifferenceAchatTTC)
        Me.GroupBox3.Controls.Add(Me.lDifferenceVenteTTC)
        Me.GroupBox3.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupBox3.Location = New System.Drawing.Point(392, 425)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(282, 68)
        Me.GroupBox3.TabIndex = 49
        Me.GroupBox3.TabStop = False
        Me.GroupBox3.Text = "Différences"
        '
        'lDifferenceAchatTTCPourcentage
        '
        Me.lDifferenceAchatTTCPourcentage.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lDifferenceAchatTTCPourcentage.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDifferenceAchatTTCPourcentage.ForeColor = System.Drawing.Color.Black
        Me.lDifferenceAchatTTCPourcentage.Location = New System.Drawing.Point(163, 15)
        Me.lDifferenceAchatTTCPourcentage.Name = "lDifferenceAchatTTCPourcentage"
        Me.lDifferenceAchatTTCPourcentage.Size = New System.Drawing.Size(93, 18)
        Me.lDifferenceAchatTTCPourcentage.TabIndex = 50
        Me.lDifferenceAchatTTCPourcentage.Text = "999999.999"
        Me.lDifferenceAchatTTCPourcentage.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lDifferenceVenteTTCPourcentage
        '
        Me.lDifferenceVenteTTCPourcentage.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lDifferenceVenteTTCPourcentage.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDifferenceVenteTTCPourcentage.ForeColor = System.Drawing.Color.Red
        Me.lDifferenceVenteTTCPourcentage.Location = New System.Drawing.Point(163, 40)
        Me.lDifferenceVenteTTCPourcentage.Name = "lDifferenceVenteTTCPourcentage"
        Me.lDifferenceVenteTTCPourcentage.Size = New System.Drawing.Size(94, 19)
        Me.lDifferenceVenteTTCPourcentage.TabIndex = 49
        Me.lDifferenceVenteTTCPourcentage.Text = "999999.999"
        Me.lDifferenceVenteTTCPourcentage.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label7
        '
        Me.Label7.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.Location = New System.Drawing.Point(1, 40)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(63, 15)
        Me.Label7.TabIndex = 48
        Me.Label7.Text = "Vente TTC"
        '
        'Label8
        '
        Me.Label8.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.Location = New System.Drawing.Point(1, 15)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(62, 15)
        Me.Label8.TabIndex = 47
        Me.Label8.Text = "Achat TTC"
        '
        'lDifferenceAchatTTC
        '
        Me.lDifferenceAchatTTC.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lDifferenceAchatTTC.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDifferenceAchatTTC.ForeColor = System.Drawing.Color.Black
        Me.lDifferenceAchatTTC.Location = New System.Drawing.Point(64, 15)
        Me.lDifferenceAchatTTC.Name = "lDifferenceAchatTTC"
        Me.lDifferenceAchatTTC.Size = New System.Drawing.Size(93, 18)
        Me.lDifferenceAchatTTC.TabIndex = 46
        Me.lDifferenceAchatTTC.Text = "999999.999"
        Me.lDifferenceAchatTTC.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lDifferenceVenteTTC
        '
        Me.lDifferenceVenteTTC.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lDifferenceVenteTTC.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDifferenceVenteTTC.ForeColor = System.Drawing.Color.Red
        Me.lDifferenceVenteTTC.Location = New System.Drawing.Point(64, 39)
        Me.lDifferenceVenteTTC.Name = "lDifferenceVenteTTC"
        Me.lDifferenceVenteTTC.Size = New System.Drawing.Size(94, 19)
        Me.lDifferenceVenteTTC.TabIndex = 35
        Me.lDifferenceVenteTTC.Text = "999999.999"
        Me.lDifferenceVenteTTC.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Controls.Add(Me.lInitialeAchatTTC)
        Me.GroupBox1.Controls.Add(Me.lInitialeVenteTTC)
        Me.GroupBox1.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupBox1.Location = New System.Drawing.Point(684, 425)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(186, 68)
        Me.GroupBox1.TabIndex = 49
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Valeurs Initiales"
        '
        'Label1
        '
        Me.Label1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(5, 40)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(63, 15)
        Me.Label1.TabIndex = 48
        Me.Label1.Text = "Vente TTC"
        '
        'Label2
        '
        Me.Label2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(5, 15)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(62, 15)
        Me.Label2.TabIndex = 47
        Me.Label2.Text = "Achat TTC"
        '
        'lInitialeAchatTTC
        '
        Me.lInitialeAchatTTC.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lInitialeAchatTTC.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lInitialeAchatTTC.ForeColor = System.Drawing.Color.Black
        Me.lInitialeAchatTTC.Location = New System.Drawing.Point(85, 14)
        Me.lInitialeAchatTTC.Name = "lInitialeAchatTTC"
        Me.lInitialeAchatTTC.Size = New System.Drawing.Size(93, 18)
        Me.lInitialeAchatTTC.TabIndex = 46
        Me.lInitialeAchatTTC.Text = "999999.999"
        Me.lInitialeAchatTTC.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lInitialeVenteTTC
        '
        Me.lInitialeVenteTTC.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lInitialeVenteTTC.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lInitialeVenteTTC.ForeColor = System.Drawing.Color.Red
        Me.lInitialeVenteTTC.Location = New System.Drawing.Point(85, 39)
        Me.lInitialeVenteTTC.Name = "lInitialeVenteTTC"
        Me.lInitialeVenteTTC.Size = New System.Drawing.Size(94, 19)
        Me.lInitialeVenteTTC.TabIndex = 35
        Me.lInitialeVenteTTC.Text = "999999.999"
        Me.lInitialeVenteTTC.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'GroupeRemarque
        '
        Me.GroupeRemarque.Controls.Add(Me.tRemarqueInventaire)
        Me.GroupeRemarque.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeRemarque.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupeRemarque.Location = New System.Drawing.Point(432, 8)
        Me.GroupeRemarque.Name = "GroupeRemarque"
        Me.GroupeRemarque.Size = New System.Drawing.Size(634, 103)
        Me.GroupeRemarque.TabIndex = 3
        Me.GroupeRemarque.TabStop = False
        Me.GroupeRemarque.Text = "Remarque"
        '
        'tRemarqueInventaire
        '
        Me.tRemarqueInventaire.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tRemarqueInventaire.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tRemarqueInventaire.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tRemarqueInventaire.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tRemarqueInventaire.Location = New System.Drawing.Point(15, 16)
        Me.tRemarqueInventaire.Multiline = True
        Me.tRemarqueInventaire.Name = "tRemarqueInventaire"
        Me.tRemarqueInventaire.Size = New System.Drawing.Size(547, 78)
        Me.tRemarqueInventaire.TabIndex = 36
        Me.tRemarqueInventaire.Tag = Nothing
        Me.tRemarqueInventaire.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tRemarqueInventaire.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bLast
        '
        Me.bLast.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bLast.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bLast.Image = Global.Pharma2000Premium.My.Resources.Resources.last_dounloaded
        Me.bLast.Location = New System.Drawing.Point(195, 524)
        Me.bLast.Name = "bLast"
        Me.bLast.Size = New System.Drawing.Size(59, 33)
        Me.bLast.TabIndex = 11
        Me.bLast.UseVisualStyleBackColor = True
        Me.bLast.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bNext
        '
        Me.bNext.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bNext.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bNext.Image = Global.Pharma2000Premium.My.Resources.Resources.next_downloaded
        Me.bNext.Location = New System.Drawing.Point(134, 524)
        Me.bNext.Name = "bNext"
        Me.bNext.Size = New System.Drawing.Size(59, 33)
        Me.bNext.TabIndex = 10
        Me.bNext.UseVisualStyleBackColor = True
        Me.bNext.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupeNumero
        '
        Me.GroupeNumero.Controls.Add(Me.tRecherche)
        Me.GroupeNumero.Controls.Add(Me.lOperateur)
        Me.GroupeNumero.Controls.Add(Me.lDateInventaire)
        Me.GroupeNumero.Controls.Add(Me.LNumero)
        Me.GroupeNumero.Controls.Add(Me.LVille)
        Me.GroupeNumero.Controls.Add(Me.lNumeroInventaire)
        Me.GroupeNumero.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeNumero.Location = New System.Drawing.Point(217, 8)
        Me.GroupeNumero.Name = "GroupeNumero"
        Me.GroupeNumero.Size = New System.Drawing.Size(209, 103)
        Me.GroupeNumero.TabIndex = 13
        Me.GroupeNumero.TabStop = False
        Me.GroupeNumero.Text = "Identification"
        '
        'tRecherche
        '
        Me.tRecherche.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tRecherche.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tRecherche.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tRecherche.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tRecherche.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tRecherche.Location = New System.Drawing.Point(54, 20)
        Me.tRecherche.Name = "tRecherche"
        Me.tRecherche.Size = New System.Drawing.Size(149, 18)
        Me.tRecherche.TabIndex = 49
        Me.tRecherche.Tag = Nothing
        Me.tRecherche.Visible = False
        Me.tRecherche.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tRecherche.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lOperateur
        '
        Me.lOperateur.AutoSize = True
        Me.lOperateur.Font = New System.Drawing.Font("Calibri", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lOperateur.Location = New System.Drawing.Point(9, 82)
        Me.lOperateur.Name = "lOperateur"
        Me.lOperateur.Size = New System.Drawing.Size(10, 13)
        Me.lOperateur.TabIndex = 36
        Me.lOperateur.Text = "-"
        '
        'lDateInventaire
        '
        Me.lDateInventaire.BackColor = System.Drawing.Color.Transparent
        Me.lDateInventaire.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDateInventaire.Location = New System.Drawing.Point(53, 39)
        Me.lDateInventaire.Name = "lDateInventaire"
        Me.lDateInventaire.Size = New System.Drawing.Size(75, 32)
        Me.lDateInventaire.TabIndex = 34
        Me.lDateInventaire.Text = "Date"
        '
        'LNumero
        '
        Me.LNumero.AutoSize = True
        Me.LNumero.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LNumero.Location = New System.Drawing.Point(4, 20)
        Me.LNumero.Name = "LNumero"
        Me.LNumero.Size = New System.Drawing.Size(44, 13)
        Me.LNumero.TabIndex = 10
        Me.LNumero.Text = "Numéro"
        '
        'LVille
        '
        Me.LVille.AutoSize = True
        Me.LVille.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LVille.Location = New System.Drawing.Point(4, 38)
        Me.LVille.Name = "LVille"
        Me.LVille.Size = New System.Drawing.Size(30, 13)
        Me.LVille.TabIndex = 11
        Me.LVille.Text = "Date"
        '
        'lNumeroInventaire
        '
        Me.lNumeroInventaire.AutoSize = True
        Me.lNumeroInventaire.BackColor = System.Drawing.Color.Transparent
        Me.lNumeroInventaire.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNumeroInventaire.Location = New System.Drawing.Point(53, 20)
        Me.lNumeroInventaire.Name = "lNumeroInventaire"
        Me.lNumeroInventaire.Size = New System.Drawing.Size(59, 13)
        Me.lNumeroInventaire.TabIndex = 33
        Me.lNumeroInventaire.Text = "-------------"
        '
        'bFirst
        '
        Me.bFirst.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bFirst.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bFirst.Image = Global.Pharma2000Premium.My.Resources.Resources.first_dounloaded
        Me.bFirst.Location = New System.Drawing.Point(12, 524)
        Me.bFirst.Name = "bFirst"
        Me.bFirst.Size = New System.Drawing.Size(59, 34)
        Me.bFirst.TabIndex = 8
        Me.bFirst.UseVisualStyleBackColor = True
        Me.bFirst.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bPrevious
        '
        Me.bPrevious.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bPrevious.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bPrevious.Image = Global.Pharma2000Premium.My.Resources.Resources.previous_downloaded
        Me.bPrevious.Location = New System.Drawing.Point(73, 524)
        Me.bPrevious.Name = "bPrevious"
        Me.bPrevious.Size = New System.Drawing.Size(59, 33)
        Me.bPrevious.TabIndex = 9
        Me.bPrevious.UseVisualStyleBackColor = True
        Me.bPrevious.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouter
        '
        Me.bAjouter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouter.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouter.Location = New System.Drawing.Point(784, 518)
        Me.bAjouter.Name = "bAjouter"
        Me.bAjouter.Size = New System.Drawing.Size(71, 39)
        Me.bAjouter.TabIndex = 5
        Me.bAjouter.Text = "Ajouter          F5"
        Me.bAjouter.UseVisualStyleBackColor = True
        Me.bAjouter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bConfirmer
        '
        Me.bConfirmer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bConfirmer.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmer.Location = New System.Drawing.Point(856, 518)
        Me.bConfirmer.Name = "bConfirmer"
        Me.bConfirmer.Size = New System.Drawing.Size(71, 39)
        Me.bConfirmer.TabIndex = 6
        Me.bConfirmer.Text = "Confirmer F3"
        Me.bConfirmer.UseVisualStyleBackColor = True
        Me.bConfirmer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox2
        '
        Me.GroupBox2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox2.Controls.Add(Me.Label6)
        Me.GroupBox2.Controls.Add(Me.Label5)
        Me.GroupBox2.Controls.Add(Me.lActuelleAchatTTC)
        Me.GroupBox2.Controls.Add(Me.lActuelleVenteTTC)
        Me.GroupBox2.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupBox2.Location = New System.Drawing.Point(880, 425)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(186, 68)
        Me.GroupBox2.TabIndex = 12
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "Valeurs Actuelles"
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.Location = New System.Drawing.Point(9, 40)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(63, 15)
        Me.Label6.TabIndex = 48
        Me.Label6.Text = "Vente TTC"
        '
        'Label5
        '
        Me.Label5.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.Location = New System.Drawing.Point(9, 15)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(62, 15)
        Me.Label5.TabIndex = 47
        Me.Label5.Text = "Achat TTC"
        '
        'lActuelleAchatTTC
        '
        Me.lActuelleAchatTTC.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lActuelleAchatTTC.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lActuelleAchatTTC.ForeColor = System.Drawing.Color.Black
        Me.lActuelleAchatTTC.Location = New System.Drawing.Point(85, 14)
        Me.lActuelleAchatTTC.Name = "lActuelleAchatTTC"
        Me.lActuelleAchatTTC.Size = New System.Drawing.Size(93, 18)
        Me.lActuelleAchatTTC.TabIndex = 46
        Me.lActuelleAchatTTC.Text = "999999.999"
        Me.lActuelleAchatTTC.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lActuelleVenteTTC
        '
        Me.lActuelleVenteTTC.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lActuelleVenteTTC.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lActuelleVenteTTC.ForeColor = System.Drawing.Color.Red
        Me.lActuelleVenteTTC.Location = New System.Drawing.Point(85, 39)
        Me.lActuelleVenteTTC.Name = "lActuelleVenteTTC"
        Me.lActuelleVenteTTC.Size = New System.Drawing.Size(94, 19)
        Me.lActuelleVenteTTC.TabIndex = 35
        Me.lActuelleVenteTTC.Text = "999999.999"
        Me.lActuelleVenteTTC.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(928, 518)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(71, 39)
        Me.bAnnuler.TabIndex = 7
        Me.bAnnuler.Text = "Annuler F10"
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gArticles
        '
        Me.gArticles.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gArticles.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gArticles.GroupByCaption = "Drag a column header here to group by that column"
        Me.gArticles.Images.Add(CType(resources.GetObject("gArticles.Images"), System.Drawing.Image))
        Me.gArticles.LinesPerRow = 2
        Me.gArticles.Location = New System.Drawing.Point(12, 117)
        Me.gArticles.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
        Me.gArticles.Name = "gArticles"
        Me.gArticles.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gArticles.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gArticles.PreviewInfo.ZoomFactor = 75.0R
        Me.gArticles.PrintInfo.PageSettings = CType(resources.GetObject("gArticles.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gArticles.Size = New System.Drawing.Size(1053, 303)
        Me.gArticles.TabIndex = 2
        Me.gArticles.Text = "C1TrueDBGrid1"
        Me.gArticles.PropBag = resources.GetString("gArticles.PropBag")
        '
        'bTerminal
        '
        Me.bTerminal.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bTerminal.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bTerminal.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bTerminal.Location = New System.Drawing.Point(368, 518)
        Me.bTerminal.Name = "bTerminal"
        Me.bTerminal.Size = New System.Drawing.Size(106, 39)
        Me.bTerminal.TabIndex = 97
        Me.bTerminal.Text = "Lecture du Terminal"
        Me.bTerminal.UseVisualStyleBackColor = True
        Me.bTerminal.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bRecherche
        '
        Me.bRecherche.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bRecherche.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bRecherche.Image = Global.Pharma2000Premium.My.Resources.Resources.arecherche
        Me.bRecherche.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bRecherche.Location = New System.Drawing.Point(556, 518)
        Me.bRecherche.Name = "bRecherche"
        Me.bRecherche.Size = New System.Drawing.Size(80, 39)
        Me.bRecherche.TabIndex = 84
        Me.bRecherche.Text = "Recherche " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "      F4"
        Me.bRecherche.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bRecherche.UseVisualStyleBackColor = True
        Me.bRecherche.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bListe
        '
        Me.bListe.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bListe.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bListe.Image = Global.Pharma2000Premium.My.Resources.Resources.aliste
        Me.bListe.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bListe.Location = New System.Drawing.Point(637, 518)
        Me.bListe.Name = "bListe"
        Me.bListe.Size = New System.Drawing.Size(74, 39)
        Me.bListe.TabIndex = 83
        Me.bListe.Text = "Liste"
        Me.bListe.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bListe.UseVisualStyleBackColor = True
        Me.bListe.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bImprimer
        '
        Me.bImprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bImprimer.Location = New System.Drawing.Point(712, 518)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(71, 39)
        Me.bImprimer.TabIndex = 4
        Me.bImprimer.Text = "Imprimer F6"
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fInventaire
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1077, 566)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fInventaire"
        Me.Text = "fInventaire"
        Me.Panel.ResumeLayout(False)
        Me.Panel.PerformLayout()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        Me.GroupeJauge.ResumeLayout(False)
        Me.GroupeJauge.PerformLayout()
        Me.PanelAfficherLots.ResumeLayout(False)
        CType(Me.gListeLotArticle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gListeRecherche, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.GroupeRemarque.ResumeLayout(False)
        CType(Me.tRemarqueInventaire, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupeNumero.ResumeLayout(False)
        Me.GroupeNumero.PerformLayout()
        CType(Me.tRecherche, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents gListeRecherche As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents GroupeRemarque As System.Windows.Forms.GroupBox
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
    Friend WithEvents bLast As C1.Win.C1Input.C1Button
    Friend WithEvents bNext As C1.Win.C1Input.C1Button
    Friend WithEvents GroupeNumero As System.Windows.Forms.GroupBox
    Friend WithEvents lOperateur As System.Windows.Forms.Label
    Friend WithEvents lDateInventaire As System.Windows.Forms.Label
    Friend WithEvents LNumero As System.Windows.Forms.Label
    Friend WithEvents LVille As System.Windows.Forms.Label
    Friend WithEvents lNumeroInventaire As System.Windows.Forms.Label
    Friend WithEvents bFirst As C1.Win.C1Input.C1Button
    Friend WithEvents bPrevious As C1.Win.C1Input.C1Button
    Friend WithEvents bAjouter As C1.Win.C1Input.C1Button
    Friend WithEvents bConfirmer As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents lActuelleAchatTTC As System.Windows.Forms.Label
    Friend WithEvents lActuelleVenteTTC As System.Windows.Forms.Label
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents gArticles As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents lDifferenceAchatTTCPourcentage As System.Windows.Forms.Label
    Friend WithEvents lDifferenceVenteTTCPourcentage As System.Windows.Forms.Label
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents lDifferenceAchatTTC As System.Windows.Forms.Label
    Friend WithEvents lDifferenceVenteTTC As System.Windows.Forms.Label
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents lInitialeAchatTTC As System.Windows.Forms.Label
    Friend WithEvents lInitialeVenteTTC As System.Windows.Forms.Label
    Friend WithEvents tRemarqueInventaire As C1.Win.C1Input.C1TextBox
    Friend WithEvents gListeLotArticle As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents GroupeJauge As System.Windows.Forms.GroupBox
    Friend WithEvents lTitreLabelle As System.Windows.Forms.Label
    Friend WithEvents lArticle As System.Windows.Forms.Label
    Friend WithEvents ProgressBar As System.Windows.Forms.ProgressBar
    Friend WithEvents lArticleEnCours As System.Windows.Forms.Label
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents PanelAfficherLots As System.Windows.Forms.Panel
    Friend WithEvents lNombreDesArticles As System.Windows.Forms.Label
    Friend WithEvents lSommeQte As System.Windows.Forms.Label
    Friend WithEvents bAfficherLot As C1.Win.C1Input.C1Button
    Friend WithEvents bOkLot As C1.Win.C1Input.C1Button
    Friend WithEvents bAnnulerLot As C1.Win.C1Input.C1Button
    Friend WithEvents bAjouterLot As C1.Win.C1Input.C1Button
    Friend WithEvents CR As Pharma2000Premium.EtatInventaire
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents tRecherche As C1.Win.C1Input.C1TextBox
    Friend WithEvents lTypeInventaire As System.Windows.Forms.Label
    Friend WithEvents bListe As C1.Win.C1Input.C1Button
    Friend WithEvents bRecherche As C1.Win.C1Input.C1Button
    Friend WithEvents bTerminal As C1.Win.C1Input.C1Button
    Friend WithEvents bSupprimer As C1.Win.C1Input.C1Button
    Friend WithEvents CRtemp As Pharma2000Premium.EtatInventaireTemporaire
    Friend WithEvents bFicheArticle As C1.Win.C1Input.C1Button
End Class
