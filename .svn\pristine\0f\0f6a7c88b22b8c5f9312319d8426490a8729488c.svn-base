﻿Imports System.Data.SqlClient
Public Class fCritereCubeVenteDetail
    Dim dsProduit As New DataSet
    Dim cmdProduit As New SqlCommand
    Dim daProduit As New SqlDataAdapter

    Dim dsCategorie As New DataSet
    Dim cmdCategorie As New SqlCommand
    Dim daCategorie As New SqlDataAdapter

    Dim dsLabo As New DataSet
    Dim cmdLabo As New SqlCommand
    Dim daLabo As New SqlDataAdapter

    Dim dsClient As New DataSet
    Dim cmdClient As New SqlCommand
    Dim daClient As New SqlDataAdapter

    Dim dsOperateur As New DataSet
    Dim cmdOperateur As New SqlCommand
    Dim daOperateur As New SqlDataAdapter

    Public yDateDebut As Date
    Public yDateFin As Date
    Public yProduit As String = ""
    Public yCategorie As String = ""
    Public yLabo As String = ""
    Public yClient As String = ""
    Public yOperateur As String = ""
    Public Mode As String = ""
    Dim NomVue As String = ""

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        'If argument = "123" And bAnnuler.Enabled = True Then
        'bAnnuler_Click(sender, e)
        'End If
    End Sub
    Private Sub fCritereCubeVenteDetail_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        If Mode = "Vente Detail" Then
            NomVue = "Vue_CubeVenteDetail"
            lTitre.Text = "ANALYSE DES VENTES DES ARTICLES"
            lClientFournisseur.Text = "Client"
        ElseIf Mode = "Achat Detail" Then
            NomVue = "Vue_CubeAchatDetail"
            lTitre.Text = "ANALYSE DES ACHATS"
            lClientFournisseur.Text = "Fournisseur"
        ElseIf Mode = "Vente" Then
            NomVue = "Vue_CubeVente"
            lTitre.Text = "ANALYSE DES VENTES"
            lClientFournisseur.Text = "Client"
            cmbProduit.Visible = False
            lProduit.Visible = False
            lCategorie.Visible = False
            cmbCategorie.Visible = False
            lLaboratoire.Visible = False
            cmbLabo.Visible = False
        End If
        If Mode = "Vente Detail" Or Mode = "Achat Detail" Then
            'chargement des produits
            cmdProduit.Connection = ConnectionServeur
            cmdProduit.CommandText = "SELECT DISTINCT [Article : Désignation] AS Designation FROM " + NomVue + " ORDER BY [Article : Désignation]"
            daProduit = New SqlDataAdapter(cmdProduit)
            daProduit.Fill(dsProduit, "PRODUIT")
            cmbProduit.DataSource = dsProduit.Tables("PRODUIT")
            cmbProduit.DisplayMember = "Designation"
            cmbProduit.ColumnHeaders = False
            cmbProduit.Splits(0).DisplayColumns("Designation").Width = 30
            cmbProduit.ExtendRightColumn = True

            'chargement des catégorie
            cmdCategorie.Connection = ConnectionServeur
            cmdCategorie.CommandText = "SELECT DISTINCT [Article : Catégorie] AS Categorie FROM " + NomVue + " ORDER BY [Article : Catégorie]"
            daCategorie = New SqlDataAdapter(cmdCategorie)
            daCategorie.Fill(dsCategorie, "CATEGORIE")
            cmbCategorie.DataSource = dsCategorie.Tables("CATEGORIE")
            cmbCategorie.DisplayMember = "Categorie"
            cmbCategorie.ColumnHeaders = False
            cmbCategorie.Splits(0).DisplayColumns("Categorie").Width = 30
            cmbCategorie.ExtendRightColumn = True

            'chargement des labo
            cmdLabo.Connection = ConnectionServeur
            cmdLabo.CommandText = "SELECT DISTINCT [Article : Laboratoire] AS Labo FROM " + NomVue + " ORDER BY [Article : Laboratoire]"
            daLabo = New SqlDataAdapter(cmdLabo)
            daLabo.Fill(dsLabo, "LABO")
            cmbLabo.DataSource = dsLabo.Tables("LABO")
            cmbLabo.DisplayMember = "Labo"
            cmbLabo.ColumnHeaders = False
            cmbLabo.Splits(0).DisplayColumns("Labo").Width = 30
            cmbLabo.ExtendRightColumn = True
        End If

        'chargement des clients / fournisseur
        cmdClient.Connection = ConnectionServeur
        If Mode = "Vente Detail" Or Mode = "Vente" Then
            cmdClient.CommandText = "SELECT DISTINCT [Client : Nom du client] AS Client FROM " + NomVue + " ORDER BY [Client : Nom du client]"
        ElseIf Mode = "Achat Detail" Then
            cmdClient.CommandText = "SELECT DISTINCT [Fournisseur : Nom du fournisseur] AS Client FROM Vue_CubeAchatDetail ORDER BY [Fournisseur : Nom du fournisseur]"
        End If
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "CLIENT")
        cmbClient.DataSource = dsClient.Tables("CLIENT")
        cmbClient.DisplayMember = "Client"
        cmbClient.ColumnHeaders = False
        cmbClient.Splits(0).DisplayColumns("Client").Width = 30
        cmbClient.ExtendRightColumn = True

        'chargement des opérateurs
        cmdOperateur.Connection = ConnectionServeur
        If Mode = "Vente Detail" Or Mode = "Vente" Then
            cmdOperateur.CommandText = "SELECT DISTINCT [Vente : Personnel] AS Personnel FROM " + NomVue + " ORDER BY [Vente : Personnel]"
        ElseIf Mode = "Achat Detail" Then
            cmdOperateur.CommandText = "SELECT DISTINCT [Achat : Personnel] AS Personnel FROM Vue_CubeAchatDetail ORDER BY [Achat : Personnel]"
        End If
        daOperateur = New SqlDataAdapter(cmdOperateur)
        daOperateur.Fill(dsOperateur, "OPERATEUR")
        cmbOperateur.DataSource = dsOperateur.Tables("OPERATEUR")
        cmbOperateur.DisplayMember = "Personnel"
        cmbOperateur.ColumnHeaders = False
        cmbOperateur.Splits(0).DisplayColumns("Personnel").Width = 30
        cmbOperateur.ExtendRightColumn = True

        dtpDebut.Text = "01/01/" + (Year(Date.Today) - 1).ToString
        dtpDebut.Text = dtpDebut.Text.Substring(0, 10)
        dtpFin.Text = Date.Today
        dtpFin.Text = dtpFin.Text.Substring(0, 10)

        dtpDebut.Focus()

    End Sub

    Private Sub dtpDebut_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyDown
        If e.KeyCode = Keys.Enter Then
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpFin_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyDown
        If e.KeyCode = Keys.Enter Then
            cmbProduit.Focus()
        End If
    End Sub

    Private Sub bOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOK.Click
        yDateDebut = dtpDebut.Value
        yDateFin = dtpFin.Value
        yProduit = cmbProduit.Text
        yCategorie = cmbCategorie.Text
        yLabo = cmbLabo.Text
        yClient = cmbClient.Text
        yOperateur = cmbOperateur.Text
        Me.Dispose()
    End Sub
End Class