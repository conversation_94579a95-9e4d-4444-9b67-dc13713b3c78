@echo off
echo ========================================
echo    PHARMA2000 MODERNE - DEMARRAGE
echo    Clone complet avec interface moderne
echo ========================================
echo.

cd /d "%~dp0"

echo Verification des prerequis...
echo.

REM Vérifier Visual Studio
where devenv >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Visual Studio 2022 non trouve !
    echo    Veuillez installer Visual Studio 2022
    echo    https://visualstudio.microsoft.com/fr/downloads/
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Visual Studio 2022 detecte
)

REM Vérifier .NET 8
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET SDK non trouve !
    echo    Veuillez installer .NET 8 SDK
    echo    https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
) else (
    echo ✅ .NET SDK detecte
    dotnet --version
)

echo.
echo ========================================
echo    CREATION DU PROJET MODERNE
echo ========================================
echo.

echo Voulez-vous creer la structure du projet maintenant ?
echo.
echo 1. Oui - Creer la structure complete
echo 2. Non - Juste ouvrir les guides
echo.
set /p choice="Votre choix (1 ou 2) : "

if "%choice%"=="1" (
    echo.
    echo Creation de la structure...
    call CREER_PROJET_MODERNE.bat
    
    echo.
    echo ========================================
    echo    OUVERTURE DES GUIDES
    echo ========================================
    echo.
    
    echo Ouverture des fichiers de reference...
    
    REM Ouvrir les guides dans l'éditeur par défaut
    start "" "PHARMA_MODERNE_PLAN.md"
    timeout /t 2 >nul
    start "" "GUIDE_DEVELOPPEMENT_MODERNE.md"
    timeout /t 2 >nul
    start "" "EXEMPLE_MODULE_CLIENT_MODERNE.md"
    
    echo.
    echo ========================================
    echo    PROCHAINES ETAPES
    echo ========================================
    echo.
    echo 1. ✅ Structure creee dans le dossier PharmaModerne/
    echo 2. ✅ Guides ouverts pour reference
    echo.
    echo MAINTENANT :
    echo 3. 📂 Ouvrez Visual Studio 2022
    echo 4. 📁 Creez les projets dans chaque dossier
    echo 5. 📦 Installez les packages NuGet necessaires
    echo 6. 🎨 Commencez par le module Client
    echo.
    echo RESSOURCES DISPONIBLES :
    echo - 📋 PHARMA_MODERNE_PLAN.md - Plan complet
    echo - 🔧 GUIDE_DEVELOPPEMENT_MODERNE.md - Guide technique
    echo - 👥 EXEMPLE_MODULE_CLIENT_MODERNE.md - Exemple concret
    echo.
    
) else (
    echo.
    echo Ouverture des guides uniquement...
    
    start "" "PHARMA_MODERNE_PLAN.md"
    timeout /t 1 >nul
    start "" "GUIDE_DEVELOPPEMENT_MODERNE.md"
    timeout /t 1 >nul
    start "" "EXEMPLE_MODULE_CLIENT_MODERNE.md"
    
    echo.
    echo ✅ Guides ouverts !
    echo.
    echo Quand vous serez pret, relancez ce script
    echo et choisissez l'option 1 pour creer la structure.
    echo.
)

echo ========================================
echo    RESUME DU PROJET
echo ========================================
echo.
echo 🎯 OBJECTIF : Clone complet de PHARMA2000 moderne
echo.
echo 📊 MODULES A IMPLEMENTER :
echo   ✅ Gestion Ventes (avec scanner integre)
echo   ✅ Gestion Clients (interface moderne)
echo   ✅ Gestion Articles/Stock (temps reel)
echo   ✅ Gestion Fournisseurs (complete)
echo   ✅ Gestion Financiere (caisse moderne)
echo   ✅ Rapports et Analyses (dashboard)
echo   ✅ Administration (securite avancee)
echo   ✅ Donnees Medicales (base complete)
echo.
echo 🎨 TECHNOLOGIES :
echo   ✅ WPF + Material Design (interface)
echo   ✅ .NET 8 (performance)
echo   ✅ Entity Framework Core (donnees)
echo   ✅ MVVM + Prism (architecture)
echo.
echo 🚀 AVANTAGES :
echo   ✅ Interface moderne et elegante
echo   ✅ Scanner integre partout
echo   ✅ Performance optimale
echo   ✅ Maintenance facilitee
echo   ✅ Extensibilite future
echo.

echo ========================================
echo    SUPPORT ET AIDE
echo ========================================
echo.
echo 📚 DOCUMENTATION :
echo   - Tous les guides sont dans le dossier PHARMA/
echo   - Exemples de code inclus
echo   - Architecture detaillee
echo.
echo 🔧 OUTILS RECOMMANDES :
echo   - Visual Studio 2022 (IDE principal)
echo   - SQL Server Management Studio (BDD)
echo   - Git (controle de version)
echo.
echo 💡 CONSEILS :
echo   - Commencez par le module Client
echo   - Testez chaque module independamment
echo   - Utilisez les exemples fournis
echo   - N'hesitez pas a adapter selon vos besoins
echo.

echo Appuyez sur une touche pour terminer...
pause >nul

echo.
echo 🎉 Bon developpement !
echo    Votre PHARMA2000 moderne vous attend !
echo.
