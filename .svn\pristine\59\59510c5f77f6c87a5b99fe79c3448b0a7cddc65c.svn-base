//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class FORME_ARTICLE
    {
        public FORME_ARTICLE()
        {
            this.ACHAT_INSTANCE_DETAILS = new HashSet<ACHAT_INSTANCE_DETAILS>();
            this.ARTICLE = new HashSet<ARTICLE>();
            this.COMMANDE_INSTANCE_DETAILS = new HashSet<COMMANDE_INSTANCE_DETAILS>();
            this.EMPRUNT_DETAILS = new HashSet<EMPRUNT_DETAILS>();
            this.ENTREE_DETAILS = new HashSet<ENTREE_DETAILS>();
            this.FORMULE_PREPARATION_DETAILS = new HashSet<FORMULE_PREPARATION_DETAILS>();
            this.PERIME_DETAILS = new HashSet<PERIME_DETAILS>();
            this.SORTIE_DETAILS = new HashSet<SORTIE_DETAILS>();
        }
    
        public int CodeForme { get; set; }
        public string LibelleForme { get; set; }
        public bool SupprimeForme { get; set; }
    
        public virtual ICollection<ACHAT_INSTANCE_DETAILS> ACHAT_INSTANCE_DETAILS { get; set; }
        public virtual ICollection<ARTICLE> ARTICLE { get; set; }
        public virtual ICollection<COMMANDE_INSTANCE_DETAILS> COMMANDE_INSTANCE_DETAILS { get; set; }
        public virtual ICollection<EMPRUNT_DETAILS> EMPRUNT_DETAILS { get; set; }
        public virtual ICollection<ENTREE_DETAILS> ENTREE_DETAILS { get; set; }
        public virtual ICollection<FORMULE_PREPARATION_DETAILS> FORMULE_PREPARATION_DETAILS { get; set; }
        public virtual ICollection<PERIME_DETAILS> PERIME_DETAILS { get; set; }
        public virtual ICollection<SORTIE_DETAILS> SORTIE_DETAILS { get; set; }
    }
}
