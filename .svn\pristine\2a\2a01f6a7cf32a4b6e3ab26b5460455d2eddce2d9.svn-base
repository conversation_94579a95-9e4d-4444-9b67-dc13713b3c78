﻿<?xml version="1.0" encoding="utf-8"?>
<Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
  <EntityContainerMapping StorageEntityContainer="StockManagementModelStoreContainer" CdmEntityContainer="StockManagementEntities">
    <EntitySetMapping Name="ARTICLE">
      <EntityTypeMapping TypeName="StockManagementModel.ARTICLE">
        <MappingFragment StoreEntitySet="ARTICLE">
          <ScalarProperty Name="NombreCommande" ColumnName="NombreCommande" />
          <ScalarProperty Name="CodeFournisseur" ColumnName="CodeFournisseur" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="Dosage" ColumnName="Dosage" />
          <ScalarProperty Name="LibelleTableau" ColumnName="LibelleTableau" />
          <ScalarProperty Name="QuantiteUnitaire" ColumnName="QuantiteUnitaire" />
          <ScalarProperty Name="ContenanceArticle" ColumnName="ContenanceArticle" />
          <ScalarProperty Name="PrixAchatHT" ColumnName="PrixAchatHT" />
          <ScalarProperty Name="PrixAchatTTC" ColumnName="PrixAchatTTC" />
          <ScalarProperty Name="PrixVenteHT" ColumnName="PrixVenteHT" />
          <ScalarProperty Name="PrixVenteTTC" ColumnName="PrixVenteTTC" />
          <ScalarProperty Name="TVA" ColumnName="TVA" />
          <ScalarProperty Name="Marge" ColumnName="Marge" />
          <ScalarProperty Name="Exonorertva" ColumnName="Exonorertva" />
          <ScalarProperty Name="HR" ColumnName="HR" />
          <ScalarProperty Name="CodePCT" ColumnName="CodePCT" />
          <ScalarProperty Name="CodeCategorieCNAM" ColumnName="CodeCategorieCNAM" />
          <ScalarProperty Name="TarifDeReference" ColumnName="TarifDeReference" />
          <ScalarProperty Name="AccordPrealable" ColumnName="AccordPrealable" />
          <ScalarProperty Name="PriseEnCharge" ColumnName="PriseEnCharge" />
          <ScalarProperty Name="SansCodeBarre" ColumnName="SansCodeBarre" />
          <ScalarProperty Name="SansVignette" ColumnName="SansVignette" />
          <ScalarProperty Name="StockAlerte" ColumnName="StockAlerte" />
          <ScalarProperty Name="QteACommander" ColumnName="QteACommander" />
          <ScalarProperty Name="DateAlerte" ColumnName="DateAlerte" />
          <ScalarProperty Name="DateDerniereCommande" ColumnName="DateDerniereCommande" />
          <ScalarProperty Name="QuantiteDernierCommande" ColumnName="QuantiteDernierCommande" />
          <ScalarProperty Name="DateInitiale" ColumnName="DateInitiale" />
          <ScalarProperty Name="StockInitial" ColumnName="StockInitial" />
          <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
          <ScalarProperty Name="CodeCategorie" ColumnName="CodeCategorie" />
          <ScalarProperty Name="CodeLabo" ColumnName="CodeLabo" />
          <ScalarProperty Name="Rayon" ColumnName="Rayon" />
          <ScalarProperty Name="CodeSituation" ColumnName="CodeSituation" />
          <ScalarProperty Name="CodeOperateur" ColumnName="CodeOperateur" />
          <ScalarProperty Name="CodeGroupement" ColumnName="CodeGroupement" />
          <ScalarProperty Name="CodeTypePreparation" ColumnName="CodeTypePreparation" />
          <ScalarProperty Name="Section" ColumnName="Section" />
          <ScalarProperty Name="DCI1" ColumnName="DCI1" />
          <ScalarProperty Name="DCI2" ColumnName="DCI2" />
          <ScalarProperty Name="DCI3" ColumnName="DCI3" />
          <ScalarProperty Name="Supprime" ColumnName="Supprime" />
          <ScalarProperty Name="FemmeEnceinte" ColumnName="FemmeEnceinte" />
          <ScalarProperty Name="StockArticle" ColumnName="StockArticle" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="INVENTAIRE">
      <EntityTypeMapping TypeName="StockManagementModel.INVENTAIRE">
        <MappingFragment StoreEntitySet="INVENTAIRE">
          <ScalarProperty Name="Poste" ColumnName="Poste" />
          <ScalarProperty Name="EnCours" ColumnName="EnCours" />
          <ScalarProperty Name="NumeroInventaire" ColumnName="NumeroInventaire" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="ValeurAchatInitial" ColumnName="ValeurAchatInitial" />
          <ScalarProperty Name="ValeurVenteInitial" ColumnName="ValeurVenteInitial" />
          <ScalarProperty Name="ValeurAchatActuelle" ColumnName="ValeurAchatActuelle" />
          <ScalarProperty Name="ValeurVenteActuelle" ColumnName="ValeurVenteActuelle" />
          <ScalarProperty Name="ValeurAchatDifference" ColumnName="ValeurAchatDifference" />
          <ScalarProperty Name="ValeurVenteDifference" ColumnName="ValeurVenteDifference" />
          <ScalarProperty Name="Remarque" ColumnName="Remarque" />
          <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
          <ScalarProperty Name="Valide" ColumnName="Valide" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="MOUVEMENT_ARTICLE">
      <EntityTypeMapping TypeName="StockManagementModel.MOUVEMENT_ARTICLE">
        <MappingFragment StoreEntitySet="MOUVEMENT_ARTICLE">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="AncienStock" ColumnName="AncienStock" />
          <ScalarProperty Name="Qte" ColumnName="Qte" />
          <ScalarProperty Name="NouveauStock" ColumnName="NouveauStock" />
          <ScalarProperty Name="TypeOperation" ColumnName="TypeOperation" />
          <ScalarProperty Name="TypeMouvement" ColumnName="TypeMouvement" />
          <ScalarProperty Name="NumOperation" ColumnName="NumOperation" />
          <ScalarProperty Name="DateOperation" ColumnName="DateOperation" />
          <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="MOUVEMENT_LOT_ARTICLE">
      <EntityTypeMapping TypeName="StockManagementModel.MOUVEMENT_LOT_ARTICLE">
        <MappingFragment StoreEntitySet="MOUVEMENT_LOT_ARTICLE">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="NumeroLotArticle" ColumnName="NumeroLotArticle" />
          <ScalarProperty Name="DatePeremptionArticle" ColumnName="DatePeremptionArticle" />
          <ScalarProperty Name="AncienStock" ColumnName="AncienStock" />
          <ScalarProperty Name="Qte" ColumnName="Qte" />
          <ScalarProperty Name="NouveauStock" ColumnName="NouveauStock" />
          <ScalarProperty Name="TypeOperation" ColumnName="TypeOperation" />
          <ScalarProperty Name="TypeMouvement" ColumnName="TypeMouvement" />
          <ScalarProperty Name="NumOperation" ColumnName="NumOperation" />
          <ScalarProperty Name="DateOperation" ColumnName="DateOperation" />
          <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
          <ScalarProperty Name="PrixAchatHT" ColumnName="PrixAchatHT" />
          <ScalarProperty Name="PrixAchatTTC" ColumnName="PrixAchatTTC" />
          <ScalarProperty Name="PrixVenteHT" ColumnName="PrixVenteHT" />
          <ScalarProperty Name="PrixVenteTTC" ColumnName="PrixVenteTTC" />
          <ScalarProperty Name="TVA" ColumnName="TVA" />
          <ScalarProperty Name="Marge" ColumnName="Marge" />
          <ScalarProperty Name="HR" ColumnName="HR" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <FunctionImportMapping FunctionImportName="P_List_ArticlesRecherche" FunctionName="StockManagementModel.Store.P_List_ArticlesRecherche" />
    <FunctionImportMapping FunctionImportName="P_List_Inventaire" FunctionName="StockManagementModel.Store.P_List_Inventaire" />
    <EntitySetMapping Name="V_List_ArticlesRecherche">
      <EntityTypeMapping TypeName="StockManagementModel.V_List_ArticlesRecherche">
        <MappingFragment StoreEntitySet="V_List_ArticlesRecherche">
          <ScalarProperty Name="LibelleCategorie" ColumnName="LibelleCategorie" />
          <ScalarProperty Name="LibelleForme" ColumnName="LibelleForme" />
          <ScalarProperty Name="Rayon" ColumnName="Rayon" />
          <ScalarProperty Name="Stock" ColumnName="Stock" />
          <ScalarProperty Name="TVA" ColumnName="TVA" />
          <ScalarProperty Name="PrixAchatTTC" ColumnName="PrixAchatTTC" />
          <ScalarProperty Name="PrixAchatHT" ColumnName="PrixAchatHT" />
          <ScalarProperty Name="PrixVenteTTC" ColumnName="PrixVenteTTC" />
          <ScalarProperty Name="PrixVenteHT" ColumnName="PrixVenteHT" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="V_List_Inventaire">
      <EntityTypeMapping TypeName="StockManagementModel.V_List_Inventaire">
        <MappingFragment StoreEntitySet="V_List_Inventaire">
          <ScalarProperty Name="QuantiteUnitaire" ColumnName="QuantiteUnitaire" />
          <ScalarProperty Name="TotalAchatTTC" ColumnName="TotalAchatTTC" />
          <ScalarProperty Name="PrixAchatTTC" ColumnName="PrixAchatTTC" />
          <ScalarProperty Name="PrixVenteTTC" ColumnName="PrixVenteTTC" />
          <ScalarProperty Name="StockActuel" ColumnName="StockActuel" />
          <ScalarProperty Name="StockInitial" ColumnName="StockInitial" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="NumeroInventaire" ColumnName="NumeroInventaire" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <FunctionImportMapping FunctionImportName="P_List_NouvelInventaire" FunctionName="StockManagementModel.Store.P_List_NouvelInventaire" />
    <EntitySetMapping Name="V_List_NouvelInventaire">
      <EntityTypeMapping TypeName="StockManagementModel.V_List_NouvelInventaire">
        <MappingFragment StoreEntitySet="V_List_NouvelInventaire">
          <ScalarProperty Name="QuantiteUnitaire" ColumnName="QuantiteUnitaire" />
          <ScalarProperty Name="TotalAchatTTC" ColumnName="TotalAchatTTC" />
          <ScalarProperty Name="PrixAchatTTC" ColumnName="PrixAchatTTC" />
          <ScalarProperty Name="PrixVenteTTC" ColumnName="PrixVenteTTC" />
          <ScalarProperty Name="StockActuel" ColumnName="StockActuel" />
          <ScalarProperty Name="StockInitial" ColumnName="StockInitial" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="NumeroInventaire" ColumnName="NumeroInventaire" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="LOT_ARTICLE">
      <EntityTypeMapping TypeName="StockManagementModel.LOT_ARTICLE">
        <MappingFragment StoreEntitySet="LOT_ARTICLE">
          <ScalarProperty Name="DatePeremptionArticle" ColumnName="DatePeremptionArticle" />
          <ScalarProperty Name="QteLotArticle" ColumnName="QteLotArticle" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="NumeroLotArticle" ColumnName="NumeroLotArticle" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <FunctionImportMapping FunctionImportName="P_List_LotArticleInventaire" FunctionName="StockManagementModel.Store.P_List_LotArticleInventaire" />
    <EntitySetMapping Name="V_List_LotArticleInventaire">
      <EntityTypeMapping TypeName="StockManagementModel.V_List_LotArticleInventaire">
        <MappingFragment StoreEntitySet="V_List_LotArticleInventaire">
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="QteLotArticle" ColumnName="QteLotArticle" />
          <ScalarProperty Name="DatePeremptionArticle" ColumnName="DatePeremptionArticle" />
          <ScalarProperty Name="NumeroLotArticle" ColumnName="NumeroLotArticle" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="INVENTAIRE_DETAILS">
      <EntityTypeMapping TypeName="StockManagementModel.INVENTAIRE_DETAILS">
        <MappingFragment StoreEntitySet="INVENTAIRE_DETAILS">
          <ScalarProperty Name="DatePeremption" ColumnName="DatePeremption" />
          <ScalarProperty Name="NumLigne" ColumnName="NumLigne" />
          <ScalarProperty Name="PrixVenteTTC" ColumnName="PrixVenteTTC" />
          <ScalarProperty Name="TotalAchatTTC" ColumnName="TotalAchatTTC" />
          <ScalarProperty Name="PrixAchatTTC" ColumnName="PrixAchatTTC" />
          <ScalarProperty Name="StockActuel" ColumnName="StockActuel" />
          <ScalarProperty Name="StockInitial" ColumnName="StockInitial" />
          <ScalarProperty Name="Rayon" ColumnName="Rayon" />
          <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
          <ScalarProperty Name="NumeroLot" ColumnName="NumeroLot" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="NumeroInventaire" ColumnName="NumeroInventaire" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
  </EntityContainerMapping>
</Mapping>