﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fParametres
    Dim cmdParametres As New SqlCommand
    Dim daParametres As New SqlDataAdapter
    Dim cbParametres As New SqlCommandBuilder
    Dim dsParametres As New DataSet

    Dim cmdParametresPharmacien As New SqlCommand
    Dim daParametresPharmacien As New SqlDataAdapter
    Dim cbParametresPharmacien As New SqlCommandBuilder
    Dim dsParametresPharmacien As New DataSet

    Public Sub afficherParametres()

        'Paramètres généreaux 

        dsParametres.Clear()
        cmdParametres.CommandText = " SELECT * FROM PARAMETRES WHERE POSTE='" + Environment.MachineName.ToString + "'"

        cmdParametres.Connection = ConnectionServeur
        daParametres = New SqlDataAdapter(cmdParametres)
        daParametres.Fill(dsParametres, "PARAMETRES")

        tHonoraireTableauA.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("HonoraireDeResponsabiliteTA").ToString
        tHonoraireTableauB.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("HonoraireDeResponsabiliteTB").ToString
        tHonoraireTableauC.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("HonoraireDeResponsabiliteTC").ToString
        tMinimumdePerception.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("MinimumDePerception").ToString

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("RechercheArticleForme").ToString <> "" Then
            rdbForme.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("RechercheArticleForme").ToString
        Else
            rdbForme.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("RechercheArticleDCI").ToString <> "" Then
            rdbDCI.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("RechercheArticleDCI")
        Else
            rdbDCI.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("RechercheArticlePrixTTC").ToString <> "" Then
            rdbPrixTTC.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("RechercheArticlePrixTTC")
        Else
            rdbPrixTTC.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("AutoriserLesMiseAjourEnLigne").ToString <> "" Then
            chbAutoriserMiseAjoursEnLigneArticles.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("AutoriserLesMiseAjourEnLigne")
        Else
            chbAutoriserMiseAjoursEnLigneArticles.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("AutoriserLesChangementsAutomatiquesDesPrixPreparation").ToString <> "" Then
            chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("AutoriserLesChangementsAutomatiquesDesPrixPreparation")
        Else
            chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("ImprimanteATicket").ToString <> "" Then
            chbImprimanteATicket.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("ImprimanteATicket")
        Else
            chbImprimanteATicket.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("Tiroir").ToString <> "" Then
            chbTiroir.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("Tiroir")
        Else
            chbTiroir.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("USB").ToString <> "" Then
            chbUSB.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("USB")
        Else
            chbUSB.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("COM").ToString <> "" Then
            tCom.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("COM")
        Else
            tCom.Value = ""
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("ImprimerLesBons").ToString <> "" Then
            chbImprimerBon.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("ImprimerLesBons")
        Else
            chbImprimerBon.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("ValiderQteEgalA1").ToString <> "" Then
            chbValiderQteEgalA1.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("ValiderQteEgalA1")
        Else
            chbValiderQteEgalA1.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("ControlerLeNombredesUnitesvendues").ToString <> "" Then
            chbControleNombreUnites.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("ControlerLeNombredesUnitesvendues")
        Else
            chbControleNombreUnites.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("NombreDesTentativesPourControlerLeNombreDesArticles").ToString <> "" Then
            tTentatives.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("NombreDesTentativesPourControlerLeNombreDesArticles")
        Else
            tTentatives.Value = 1
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("CreationDesClientsCreditAuNiveauPreparateur").ToString <> "" Then
            chbCreationDesClientsCreditDansNiveauPreparateur.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("CreationDesClientsCreditAuNiveauPreparateur")
        Else
            chbCreationDesClientsCreditDansNiveauPreparateur.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("InscriptionAutomatiqueSurOrdonnacier").ToString <> "" Then
            chbInscriptionSurOrdonnancierAutomatique.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("InscriptionAutomatiqueSurOrdonnacier")
        Else
            chbInscriptionSurOrdonnancierAutomatique.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("InterdirelaVenteDesProduitsPerimes").ToString <> "" Then
            chbInterdireLaVenteDesPerimes.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("InterdirelaVenteDesProduitsPerimes")
        Else
            chbInterdireLaVenteDesPerimes.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("VerifierSiProduitPrisEnChargeParLaCNAM").ToString <> "" Then
            chbVerifierProduitPrisEnChargeParCNAM.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("VerifierSiProduitPrisEnChargeParLaCNAM")
        Else
            chbVerifierProduitPrisEnChargeParCNAM.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("PermettreUtiliserLesFrigosEnVente").ToString <> "" Then
            chbPermettreUtiliserFrigosEnVente.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("PermettreUtiliserLesFrigosEnVente")
        Else
            chbPermettreUtiliserFrigosEnVente.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("PermettreLaSuppressionsDesVentesAuPreparateurs").ToString <> "" Then
            chbPermettrePreparateursSupprimerVente.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("PermettreLaSuppressionsDesVentesAuPreparateurs")
        Else
            chbPermettrePreparateursSupprimerVente.Checked = False
        End If
        tDureeAffichageAlerte.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("DureeAffichageDesAlertes")
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("InterdireDeChoisirLesArticlesParDesignation").ToString <> "" Then
            chbInterdireChoisirParDesignation.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("InterdireDeChoisirLesArticlesParDesignation")
        Else
            chbInterdireChoisirParDesignation.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("RetrancherDuStockLorsDuneVenteenInstance").ToString <> "" Then
            chbRetarancheDuStockLorsDeVenteInstance.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("RetrancherDuStockLorsDuneVenteenInstance")
        Else
            chbRetarancheDuStockLorsDeVenteInstance.Checked = False
        End If
        tCommandeGroupeJ.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("CmdGroupeJournaliere")
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("QteMultipleDe5").ToString <> "" Then
            chbQteMultiple5.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("QteMultipleDe5")
        Else
            chbQteMultiple5.Checked = False
        End If

        tNePasSortirManquantsDepuis.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("NePAsSortirLesManquantsDepuis")

        dtpDebutAnneeCourant.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("CongeDuAnneeCourant")
        dtpFinAnneeCourant.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("CongeAuAnneeCourant")
        dtpDebutAnneeProchaine.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("CongeDuAnneeProchaine")
        dtpfinAnneeProchaine.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("CongeAuAnneeProchaine")

        tNomOrdinateurImpressionCodeABarre.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("NomDeLordinateurDImpressionCodeABarre")
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("NePAsMettreAJourLaFicheArticleSiLePrixChange").ToString <> "" Then
            rdbNePasMettreAJourLaFicheArticleSiLePrixChange.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("NePAsMettreAJourLaFicheArticleSiLePrixChange")
        Else
            rdbNePasMettreAJourLaFicheArticleSiLePrixChange.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("InterventionAvecUnAssistantSiLePrixChange").ToString <> "" Then
            rdbInterventionAvecUnAssistant.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("InterventionAvecUnAssistantSiLePrixChange")
        Else
            rdbInterventionAvecUnAssistant.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("AutoriserLaModificationdesAchatsAuPreparateur").ToString <> "" Then
            chbAutoriserModificationsDesAchatsPréparateurs.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("AutoriserLaModificationdesAchatsAuPreparateur")
        Else
            chbAutoriserModificationsDesAchatsPréparateurs.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("AutoriserLaSuppressiondesAchatsAuPreparateur").ToString <> "" Then
            chbAutoriserSuppressionsDesAchatsAuxPréparateurs.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("AutoriserLaSuppressiondesAchatsAuPreparateur")
        Else
            chbAutoriserSuppressionsDesAchatsAuxPréparateurs.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("AfficherLesDernieresDatesDePeremptionDansLesNouveauxAchats").ToString <> "" Then
            chbAfficherLesDerniereDDPeremptionDansNouveauAchat.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("AfficherLesDernieresDatesDePeremptionDansLesNouveauxAchats")
        Else
            chbAfficherLesDerniereDDPeremptionDansNouveauAchat.Checked = False
        End If
        tPoste.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("POSTE").ToString

        cbParametres = New SqlCommandBuilder(daParametres)

        'paramètres Pharmacien

        dsParametresPharmacien.Clear()
        cmdParametresPharmacien.CommandText = " SELECT * FROM PARAMETRE_PHARMACIE "

        cmdParametresPharmacien.Connection = ConnectionServeur
        daParametresPharmacien = New SqlDataAdapter(cmdParametresPharmacien)
        daParametresPharmacien.Fill(dsParametresPharmacien, "PARAMETRE_PHARMACIE")

        tCodePharmacien.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("CodePharmacie")
        tLecteurUpdate.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("LecteurUpdate")
        tPharmacie.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Pharmacie")
        tCNAM.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("NCnam")
        tNumeroAffiliation1.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Affiliation1")
        tNumeroAffiliation2.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Affiliation2")
        tAdresse.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Adresse")
        tTelephone.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Telephone")
        tFax.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Fax")
        tCodeTva.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("CodeTVA")
        tNCNSS.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("NCNSS")
        tMatriculeFiscale.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("MatriculeFiscale")
        tMessagederoulant.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Messagederoulant")
        tTimbre.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Timbre")
        If dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("DemandeMotDePasse").ToString <> "" Then
            chbAutoriserLesMotsDePasse.Checked = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("DemandeMotDePasse")
        Else
            chbAutoriserLesMotsDePasse.Checked = False
        End If

        cbParametresPharmacien = New SqlCommandBuilder(daParametresPharmacien)

        Tab.TabPages(0).Show()

    End Sub


    Public Sub init()
        lAnneeCourant.Text = System.DateTime.Now.Year
        lAnneeProchaine.Text = System.DateTime.Now.Year + 1

        dtpDebutAnneeCourant.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDebutAnneeCourant.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpFinAnneeCourant.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpFinAnneeCourant.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpDebutAnneeProchaine.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDebutAnneeProchaine.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpfinAnneeProchaine.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpfinAnneeProchaine.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        afficherParametres()
    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        Dim dr As DataRow
        Dim drPharmacien As DataRow

        Dim StrSQL As String = ""

        With dsParametres.Tables("PARAMETRES")
            dr = .Rows(0)

            If tHonoraireTableauA.Text <> "" Then
                dr.Item("HonoraireDeResponsabiliteTA") = tHonoraireTableauA.Text
            Else
                dr.Item("HonoraireDeResponsabiliteTA") = 0
            End If
            If tHonoraireTableauB.Text <> "" Then
                dr.Item("HonoraireDeResponsabiliteTB") = tHonoraireTableauB.Text
            Else
                dr.Item("HonoraireDeResponsabiliteTB") = 0
            End If
            If tHonoraireTableauC.Text <> "" Then
                dr.Item("HonoraireDeResponsabiliteTC") = tHonoraireTableauC.Text
            Else
                dr.Item("HonoraireDeResponsabiliteTC") = 0
            End If
            If tMinimumdePerception.Text <> "" Then
                dr.Item("MinimumDePerception") = tMinimumdePerception.Text
            Else
                dr.Item("MinimumDePerception") = 0
            End If

            dr.Item("RechercheArticleForme") = rdbForme.Checked
            dr.Item("RechercheArticleDCI") = rdbDCI.Checked
            dr.Item("RechercheArticlePrixTTC") = rdbPrixTTC.Checked
            dr.Item("AutoriserLesMiseAjourEnLigne") = chbAutoriserMiseAjoursEnLigneArticles.Checked

            dr.Item("AutoriserLesChangementsAutomatiquesDesPrixPreparation") = chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.Checked
            dr.Item("ImprimanteATicket") = chbImprimanteATicket.Checked
            dr.Item("Tiroir") = chbTiroir.Checked
            dr.Item("USB") = chbUSB.Checked

            dr.Item("COM") = tCom.Text
            dr.Item("ImprimerLesBons") = chbImprimerBon.Checked
            dr.Item("ValiderQteEgalA1") = chbValiderQteEgalA1.Checked
            dr.Item("ControlerLeNombredesUnitesvendues") = chbControleNombreUnites.Checked

            If tTentatives.Text <> "" Then
                dr.Item("NombreDesTentativesPourControlerLeNombreDesArticles") = tTentatives.Text
            Else
                dr.Item("NombreDesTentativesPourControlerLeNombreDesArticles") = 1
            End If

            dr.Item("CreationDesClientsCreditAuNiveauPreparateur") = chbCreationDesClientsCreditDansNiveauPreparateur.Checked
            dr.Item("InscriptionAutomatiqueSurOrdonnacier") = chbInscriptionSurOrdonnancierAutomatique.Checked
            dr.Item("InterdirelaVenteDesProduitsPerimes") = chbInterdireLaVenteDesPerimes.Checked
            dr.Item("VerifierSiProduitPrisEnChargeParLaCNAM") = chbVerifierProduitPrisEnChargeParCNAM.Checked

            dr.Item("PermettreUtiliserLesFrigosEnVente") = chbPermettreUtiliserFrigosEnVente.Checked
            dr.Item("PermettreLaSuppressionsDesVentesAuPreparateurs") = chbPermettrePreparateursSupprimerVente.Checked
            dr.Item("DureeAffichageDesAlertes") = tDureeAffichageAlerte.Text
            dr.Item("InterdireDeChoisirLesArticlesParDesignation") = chbInterdireChoisirParDesignation.Checked

            dr.Item("RetrancherDuStockLorsDuneVenteenInstance") = chbRetarancheDuStockLorsDeVenteInstance.Checked
            dr.Item("CmdGroupeJournaliere") = tCommandeGroupeJ.Text
            dr.Item("QteMultipleDe5") = chbQteMultiple5.Checked

            dr.Item("NePAsSortirLesManquantsDepuis") = tNePasSortirManquantsDepuis.Text

            dr.Item("CongeDuAnneeCourant") = dtpDebutAnneeCourant.Value
            dr.Item("CongeAuAnneeCourant") = dtpFinAnneeCourant.Value
            dr.Item("CongeDuAnneeProchaine") = dtpDebutAnneeProchaine.Value
            dr.Item("CongeAuAnneeProchaine") = dtpfinAnneeProchaine.Value

            dr.Item("NomDeLordinateurDImpressionCodeABarre") = tNomOrdinateurImpressionCodeABarre.Text

            dr.Item("NePAsMettreAJourLaFicheArticleSiLePrixChange") = rdbNePasMettreAJourLaFicheArticleSiLePrixChange.Checked
            dr.Item("InterventionAvecUnAssistantSiLePrixChange") = rdbInterventionAvecUnAssistant.Checked
            dr.Item("AutoriserLaModificationdesAchatsAuPreparateur") = chbAutoriserModificationsDesAchatsPréparateurs.Checked
            dr.Item("AutoriserLaSuppressiondesAchatsAuPreparateur") = chbAutoriserSuppressionsDesAchatsAuxPréparateurs.Checked

            dr.Item("AfficherLesDernieresDatesDePeremptionDansLesNouveauxAchats") = chbAfficherLesDerniereDDPeremptionDansNouveauAchat.Checked

        End With

        Try
            daParametres.Update(dsParametres, "PARAMETRES")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsParametres.Reset()
            Me.init()
            Exit Sub
        End Try

        With dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE")
            drPharmacien = .Rows(0)

            drPharmacien.Item("CodePharmacie") = tCodePharmacien.Text
            drPharmacien.Item("LecteurUpdate") = tLecteurUpdate.Text
            drPharmacien.Item("Pharmacie") = tPharmacie.Text
            drPharmacien.Item("NCnam") = tCNAM.Text

            drPharmacien.Item("Affiliation1") = tNumeroAffiliation1.Text
            drPharmacien.Item("Affiliation2") = tNumeroAffiliation2.Text
            drPharmacien.Item("Adresse") = tAdresse.Text
            drPharmacien.Item("Telephone") = tTelephone.Text

            drPharmacien.Item("Fax") = tFax.Text
            drPharmacien.Item("CodeTVA") = tCodeTva.Text
            drPharmacien.Item("Messagederoulant") = tMessagederoulant.Text
            If tTimbre.Text <> "" Then
                drPharmacien.Item("Timbre") = tTimbre.Text
            Else
                drPharmacien.Item("Timbre") = 0
            End If

            drPharmacien.Item("NCNSS") = tNCNSS.Text
            drPharmacien.Item("MatriculeFiscale") = tMatriculeFiscale.Text
            drPharmacien.Item("DemandeMotDePasse") = chbAutoriserLesMotsDePasse.Checked
        End With

        Try
            daParametresPharmacien.Update(dsParametresPharmacien, "PARAMETRE_PHARMACIE")
            Me.Hide()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsParametresPharmacien.Reset()
            Me.init()
        End Try


    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        If dsParametres.HasChanges Then
            If MsgBox("Voulez vous vraiment annuler les modifications de ce Fournisseur ?", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                Me.Hide()
            End If
        Else
            Me.Hide()
        End If
    End Sub

    Private Sub chbValiderQteEgalA1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbValiderQteEgalA1.CheckedChanged
        'If chbValiderQteEgalA1.Checked = True Then
        '    ValiderQteEgalA1 = True
        'Else
        '    ValiderQteEgalA1 = False
        'End If
    End Sub

    Private Sub GroupBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GroupBox1.Enter

    End Sub

    Private Sub tPharmacie_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tPharmacie.TextChanged

    End Sub

    Private Sub tTimbre_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTimbre.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tTimbre.Text), 3)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tTimbre.Text = "0.000"
            tTimbre.Focus()
            tTimbre.SelectionLength = tTimbre.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tTimbre_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tTimbre.TextChanged

    End Sub

    Private Sub tHonoraireTableauA_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tHonoraireTableauA.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tHonoraireTableauA.Text), 3)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tHonoraireTableauA.Text = "0.000"
            tHonoraireTableauA.Focus()
            tHonoraireTableauA.SelectionLength = tHonoraireTableauA.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tHonoraireTableauA_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tHonoraireTableauA.TextChanged

    End Sub

    Private Sub tHonoraireTableauB_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tHonoraireTableauB.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tHonoraireTableauB.Text), 3)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tHonoraireTableauB.Text = "0.000"
            tHonoraireTableauB.Focus()
            tHonoraireTableauB.SelectionLength = tHonoraireTableauB.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tHonoraireTableauB_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tHonoraireTableauB.TextChanged

    End Sub

    Private Sub tHonoraireTableauC_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tHonoraireTableauC.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tHonoraireTableauC.Text), 3)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tHonoraireTableauC.Text = "0.000"
            tHonoraireTableauC.Focus()
            tHonoraireTableauC.SelectionLength = tHonoraireTableauC.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tHonoraireTableauC_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tHonoraireTableauC.TextChanged

    End Sub

    Private Sub tMinimumdePerception_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tMinimumdePerception.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tMinimumdePerception.Text), 3)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tMinimumdePerception.Text = "0.000"
            tMinimumdePerception.Focus()
            tMinimumdePerception.SelectionLength = tMinimumdePerception.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tMinimumdePerception_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tMinimumdePerception.TextChanged

    End Sub

    Private Sub tCommandeGroupeJ_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCommandeGroupeJ.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tCommandeGroupeJ.Text), 3)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tCommandeGroupeJ.Text = "0.000"
            tCommandeGroupeJ.Focus()
            tCommandeGroupeJ.SelectionLength = tCommandeGroupeJ.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tCommandeGroupeJ_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCommandeGroupeJ.TextChanged

    End Sub

    Private Sub tNePasSortirManquantsDepuis_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNePasSortirManquantsDepuis.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tNePasSortirManquantsDepuis.Text), 3)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tNePasSortirManquantsDepuis.Text = "0.000"
            tNePasSortirManquantsDepuis.Focus()
            tNePasSortirManquantsDepuis.SelectionLength = tNePasSortirManquantsDepuis.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tNePasSortirManquantsDepuis_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNePasSortirManquantsDepuis.TextChanged

    End Sub

    Private Sub chbControleNombreUnites_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbControleNombreUnites.CheckedChanged
        'If chbControleNombreUnites.Checked = True Then
        '    ControleNombreUnites = True
        'Else
        '    ControleNombreUnites = False
        'End If
    End Sub

    Private Sub tTentatives_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTentatives.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tTentatives.Text), 3)
        Catch ex As Exception
            MsgBox("Valeur invalide !", MsgBoxStyle.Critical, "Erreur")
            tTentatives.Text = "0.000"
            tTentatives.Focus()
            tTentatives.SelectionLength = tTentatives.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tTentatives_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tTentatives.TextChanged
        'If tTentatives.Text <> "" Then
        '    NbrDesTentatives = CDbl(tTentatives.Text)
        'Else
        '    NbrDesTentatives = 1
        'End If
    End Sub

    Private Sub chbPermettreUtiliserFrigosEnVente_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbPermettreUtiliserFrigosEnVente.CheckedChanged
        'If chbPermettreUtiliserFrigosEnVente.Checked = True Then
        '    PermettreUtiliserFrigosEuVente = True
        'Else
        '    PermettreUtiliserFrigosEuVente = False
        'End If
    End Sub
End Class