﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="VentesReportsModel.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2008" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
  <EntityContainer Name="VentesReportsModelStoreContainer">
    <EntitySet Name="PARAMETRE_PHARMACIE" EntityType="VentesReportsModel.Store.PARAMETRE_PHARMACIE" store:Type="Tables" Schema="dbo" />
    <EntitySet Name="V_Report_EtatDesFactures" EntityType="VentesReportsModel.Store.V_Report_EtatDesFactures" store:Type="Views" store:Schema="dbo" store:Name="V_Report_EtatDesFactures">
      <DefiningQuery>SELECT 
      [V_Report_EtatDesFactures].[Id] AS [Id], 
      [V_Report_EtatDesFactures].[CodeClient] AS [CodeClient], 
      [V_Report_EtatDesFactures].[Nom] AS [Nom], 
      [V_Report_EtatDesFactures].[NumeroOperation] AS [NumeroOperation], 
      [V_Report_EtatDesFactures].[Date] AS [Date], 
      [V_Report_EtatDesFactures].[Honoraire] AS [Honoraire], 
      [V_Report_EtatDesFactures].[TotalVenteHT] AS [TotalVenteHT], 
      [V_Report_EtatDesFactures].[Tva] AS [Tva], 
      [V_Report_EtatDesFactures].[TotalVenteTTC] AS [TotalVenteTTC], 
      [V_Report_EtatDesFactures].[NumeroFacture] AS [NumeroFacture]
      FROM [dbo].[V_Report_EtatDesFactures] AS [V_Report_EtatDesFactures]</DefiningQuery>
    </EntitySet>
    <EntitySet Name="V_Report_EtatDetailDesVentes" EntityType="VentesReportsModel.Store.V_Report_EtatDetailDesVentes" store:Type="Views" store:Schema="dbo" store:Name="V_Report_EtatDetailDesVentes">
      <DefiningQuery>SELECT 
      [V_Report_EtatDetailDesVentes].[Id] AS [Id], 
      [V_Report_EtatDetailDesVentes].[NumeroOperation] AS [NumeroOperation], 
      [V_Report_EtatDetailDesVentes].[Date] AS [Date], 
      [V_Report_EtatDetailDesVentes].[CodeArticle] AS [CodeArticle], 
      [V_Report_EtatDetailDesVentes].[CodeABarre] AS [CodeABarre], 
      [V_Report_EtatDetailDesVentes].[Designation] AS [Designation], 
      [V_Report_EtatDetailDesVentes].[LibelleForme] AS [LibelleForme], 
      [V_Report_EtatDetailDesVentes].[LibelleCategorie] AS [LibelleCategorie], 
      [V_Report_EtatDetailDesVentes].[CodeClient] AS [CodeClient], 
      [V_Report_EtatDetailDesVentes].[Nom] AS [Nom], 
      [V_Report_EtatDetailDesVentes].[Quantite] AS [Quantite], 
      [V_Report_EtatDetailDesVentes].[Tva] AS [Tva], 
      [V_Report_EtatDetailDesVentes].[Remise] AS [Remise], 
      [V_Report_EtatDetailDesVentes].[TotalVenteTTC] AS [TotalVenteTTC], 
      [V_Report_EtatDetailDesVentes].[NumeroFacture] AS [NumeroFacture], 
      [V_Report_EtatDetailDesVentes].[CodeCategorie] AS [CodeCategorie], 
      [V_Report_EtatDetailDesVentes].[CodeForme] AS [CodeForme]
      FROM [dbo].[V_Report_EtatDetailDesVentes] AS [V_Report_EtatDetailDesVentes]</DefiningQuery>
    </EntitySet>
    <EntitySet Name="V_Report_EtatDetailsCaisse" EntityType="VentesReportsModel.Store.V_Report_EtatDetailsCaisse" store:Type="Views" store:Schema="dbo" store:Name="V_Report_EtatDetailsCaisse">
      <DefiningQuery>SELECT 
      [V_Report_EtatDetailsCaisse].[Id] AS [Id], 
      [V_Report_EtatDetailsCaisse].[CodeNatureReglement] AS [CodeNatureReglement], 
      [V_Report_EtatDetailsCaisse].[Type] AS [Type], 
      [V_Report_EtatDetailsCaisse].[Date] AS [Date], 
      [V_Report_EtatDetailsCaisse].[CodeClient] AS [CodeClient], 
      [V_Report_EtatDetailsCaisse].[Nom] AS [Nom], 
      [V_Report_EtatDetailsCaisse].[TotalVenteTTC] AS [TotalVenteTTC], 
      [V_Report_EtatDetailsCaisse].[Credit] AS [Credit], 
      [V_Report_EtatDetailsCaisse].[Reglement] AS [Reglement], 
      [V_Report_EtatDetailsCaisse].[DateEcheance] AS [DateEcheance], 
      [V_Report_EtatDetailsCaisse].[CodePersonnel] AS [CodePersonnel], 
      [V_Report_EtatDetailsCaisse].[Vendeur] AS [Vendeur], 
      [V_Report_EtatDetailsCaisse].[Poste] AS [Poste], 
      [V_Report_EtatDetailsCaisse].[NumeroOperation] AS [NumeroOperation]
      FROM [dbo].[V_Report_EtatDetailsCaisse] AS [V_Report_EtatDetailsCaisse]</DefiningQuery>
    </EntitySet>
    <EntitySet Name="V_Report_EtatJournalDesVentes" EntityType="VentesReportsModel.Store.V_Report_EtatJournalDesVentes" store:Type="Views" store:Schema="dbo" store:Name="V_Report_EtatJournalDesVentes">
      <DefiningQuery>SELECT 
      [V_Report_EtatJournalDesVentes].[Id] AS [Id], 
      [V_Report_EtatJournalDesVentes].[Date] AS [Date], 
      [V_Report_EtatJournalDesVentes].[Exonore] AS [Exonore], 
      [V_Report_EtatJournalDesVentes].[BaseTVA6] AS [BaseTVA6], 
      [V_Report_EtatJournalDesVentes].[TVA6] AS [TVA6], 
      [V_Report_EtatJournalDesVentes].[BaseTVA12] AS [BaseTVA12], 
      [V_Report_EtatJournalDesVentes].[TVA12] AS [TVA12], 
      [V_Report_EtatJournalDesVentes].[BaseTVA18] AS [BaseTVA18], 
      [V_Report_EtatJournalDesVentes].[TVA18] AS [TVA18], 
      [V_Report_EtatJournalDesVentes].[TotalHT] AS [TotalHT], 
      [V_Report_EtatJournalDesVentes].[TotalTVA] AS [TotalTVA], 
      [V_Report_EtatJournalDesVentes].[TotalTTC] AS [TotalTTC], 
      [V_Report_EtatJournalDesVentes].[HR] AS [HR], 
      [V_Report_EtatJournalDesVentes].[NumeroFacture] AS [NumeroFacture]
      FROM [dbo].[V_Report_EtatJournalDesVentes] AS [V_Report_EtatJournalDesVentes]</DefiningQuery>
    </EntitySet>
    <EntitySet Name="V_Report_VentesAnnuelles" EntityType="VentesReportsModel.Store.V_Report_VentesAnnuelles" store:Type="Views" store:Schema="dbo" store:Name="V_Report_VentesAnnuelles">
      <DefiningQuery>SELECT 
      [V_Report_VentesAnnuelles].[Id] AS [Id], 
      [V_Report_VentesAnnuelles].[Annee] AS [Annee], 
      [V_Report_VentesAnnuelles].[Comptant] AS [Comptant], 
      [V_Report_VentesAnnuelles].[Credit] AS [Credit], 
      [V_Report_VentesAnnuelles].[Mutuelle] AS [Mutuelle], 
      [V_Report_VentesAnnuelles].[Cnam] AS [Cnam], 
      [V_Report_VentesAnnuelles].[Remise] AS [Remise], 
      [V_Report_VentesAnnuelles].[Caisse] AS [Caisse], 
      [V_Report_VentesAnnuelles].[TotalTTC] AS [TotalTTC]
      FROM [dbo].[V_Report_VentesAnnuelles] AS [V_Report_VentesAnnuelles]</DefiningQuery>
    </EntitySet>
    <EntitySet Name="V_Report_VentesMensuelles" EntityType="VentesReportsModel.Store.V_Report_VentesMensuelles" store:Type="Views" store:Schema="dbo" store:Name="V_Report_VentesMensuelles">
      <DefiningQuery>SELECT 
      [V_Report_VentesMensuelles].[Id] AS [Id], 
      [V_Report_VentesMensuelles].[NumeroMois] AS [NumeroMois], 
      [V_Report_VentesMensuelles].[NomMois] AS [NomMois], 
      [V_Report_VentesMensuelles].[Comptant] AS [Comptant], 
      [V_Report_VentesMensuelles].[Credit] AS [Credit], 
      [V_Report_VentesMensuelles].[Mutuelle] AS [Mutuelle], 
      [V_Report_VentesMensuelles].[Cnam] AS [Cnam], 
      [V_Report_VentesMensuelles].[Remise] AS [Remise], 
      [V_Report_VentesMensuelles].[TotalTTC] AS [TotalTTC], 
      [V_Report_VentesMensuelles].[Caisse] AS [Caisse], 
      [V_Report_VentesMensuelles].[NumMois] AS [NumMois], 
      [V_Report_VentesMensuelles].[NumYear] AS [NumYear]
      FROM [dbo].[V_Report_VentesMensuelles] AS [V_Report_VentesMensuelles]</DefiningQuery>
    </EntitySet>
    <EntitySet Name="V_Report_VentesQuotidiennes" EntityType="VentesReportsModel.Store.V_Report_VentesQuotidiennes" store:Type="Views" store:Schema="dbo" store:Name="V_Report_VentesQuotidiennes">
      <DefiningQuery>SELECT 
      [V_Report_VentesQuotidiennes].[Id] AS [Id], 
      [V_Report_VentesQuotidiennes].[NumeroJour] AS [NumeroJour], 
      [V_Report_VentesQuotidiennes].[Comptant] AS [Comptant], 
      [V_Report_VentesQuotidiennes].[Mutuelle] AS [Mutuelle], 
      [V_Report_VentesQuotidiennes].[Cnam] AS [Cnam], 
      [V_Report_VentesQuotidiennes].[TotalTTC] AS [TotalTTC], 
      [V_Report_VentesQuotidiennes].[Reglement] AS [Reglement], 
      [V_Report_VentesQuotidiennes].[Caisse] AS [Caisse], 
      [V_Report_VentesQuotidiennes].[NumJour] AS [NumJour], 
      [V_Report_VentesQuotidiennes].[NumMois] AS [NumMois], 
      [V_Report_VentesQuotidiennes].[NumYear] AS [NumYear]
      FROM [dbo].[V_Report_VentesQuotidiennes] AS [V_Report_VentesQuotidiennes]</DefiningQuery>
    </EntitySet>
  </EntityContainer>
  <EntityType Name="PARAMETRE_PHARMACIE">
    <Key>
      <PropertyRef Name="Code" />
    </Key>
    <Property Name="Code" Type="nchar" Nullable="false" MaxLength="10" />
    <Property Name="CodePharmacie" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Pharmacie" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="NCnam" Type="varchar" MaxLength="255" />
    <Property Name="Affiliation1" Type="varchar" MaxLength="255" />
    <Property Name="Affiliation2" Type="varchar" MaxLength="255" />
    <Property Name="Adresse" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Telephone" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Fax" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="CodeTVA" Type="varchar" MaxLength="255" />
    <Property Name="Rib" Type="varchar" Nullable="false" MaxLength="255" />
    <Property Name="Messagederoulant1" Type="varchar" MaxLength="255" />
    <Property Name="Messagederoulant2" Type="varchar" MaxLength="255" />
    <Property Name="Timbre" Type="decimal" Scale="3" />
    <Property Name="DemandeMotDePasse" Type="bit" />
    <Property Name="DateMigration" Type="date" />
    <Property Name="SmtpMail" Type="varchar" MaxLength="255" />
    <Property Name="PortMail" Type="varchar" MaxLength="50" />
    <Property Name="AdresseMailDestinateur" Type="varchar" MaxLength="255" />
    <Property Name="SujetMail" Type="varchar" MaxLength="255" />
    <Property Name="TexteMail" Type="varchar" MaxLength="550" />
    <Property Name="MotDePasseDestinateur" Type="varchar" MaxLength="255" />
    <Property Name="AutoriserEnvoiMail" Type="bit" Nullable="false" />
    <Property Name="NbreJourValiditeParDefaut" Type="int" />
    <Property Name="NumeroLotProduction" Type="nvarchar" Nullable="false" MaxLength="50" />
    <Property Name="Latitude_Longitude" Type="varchar" MaxLength="255" />
    <Property Name="TailleCodeCNAM" Type="int" />
    <Property Name="TailleListe" Type="int" />
    <Property Name="TailleCaractere" Type="int" />
    <Property Name="PoliceCaractere" Type="varchar" MaxLength="50" />
    <Property Name="Texte" Type="varchar" MaxLength="255" />
    <Property Name="TauxRemise" Type="smallint" Nullable="false" />
    <Property Name="CodeGSU" Type="varchar" MaxLength="5" />
    <Property Name="ImageCodeABarre" Type="image" />
    <Property Name="ActiverOMFAPCI" Type="bit" Nullable="false" />
  </EntityType>
  <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « Pharma_Copie.dbo.V_Report_EtatDesFactures » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
  <EntityType Name="V_Report_EtatDesFactures">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" />
    <Property Name="CodeClient" Type="varchar" MaxLength="255" />
    <Property Name="Nom" Type="varchar" MaxLength="255" />
    <Property Name="NumeroOperation" Type="varchar" MaxLength="255" />
    <Property Name="Date" Type="date" />
    <Property Name="Honoraire" Type="decimal" Scale="3" />
    <Property Name="TotalVenteHT" Type="decimal" Scale="3" />
    <Property Name="Tva" Type="decimal" Scale="3" />
    <Property Name="TotalVenteTTC" Type="decimal" Scale="3" />
    <Property Name="NumeroFacture" Type="varchar" MaxLength="255" />
  </EntityType>
  <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « Pharma_Copie.dbo.V_Report_EtatDesVentes » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
  <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « Pharma_Copie.dbo.V_Report_EtatDetailDesVentes » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
  <EntityType Name="V_Report_EtatDetailDesVentes">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" />
    <Property Name="NumeroOperation" Type="varchar" MaxLength="255" />
    <Property Name="Date" Type="date" />
    <Property Name="CodeArticle" Type="varchar" MaxLength="255" />
    <Property Name="CodeABarre" Type="varchar" MaxLength="255" />
    <Property Name="Designation" Type="varchar" MaxLength="255" />
    <Property Name="LibelleForme" Type="varchar" MaxLength="255" />
    <Property Name="LibelleCategorie" Type="varchar" MaxLength="255" />
    <Property Name="CodeClient" Type="varchar" MaxLength="255" />
    <Property Name="Nom" Type="varchar" MaxLength="255" />
    <Property Name="Quantite" Type="int" />
    <Property Name="Tva" Type="decimal" Scale="3" />
    <Property Name="Remise" Type="decimal" Scale="3" />
    <Property Name="TotalVenteTTC" Type="decimal" Scale="3" />
    <Property Name="NumeroFacture" Type="varchar" MaxLength="255" />
    <Property Name="CodeCategorie" Type="int" />
    <Property Name="CodeForme" Type="int" />
  </EntityType>
  <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « Pharma_Copie.dbo.V_Report_EtatDetailsCaisse » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
  <EntityType Name="V_Report_EtatDetailsCaisse">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" />
    <Property Name="CodeNatureReglement" Type="int" />
    <Property Name="Type" Type="varchar" MaxLength="255" />
    <Property Name="Date" Type="date" />
    <Property Name="CodeClient" Type="varchar" MaxLength="255" />
    <Property Name="Nom" Type="varchar" MaxLength="255" />
    <Property Name="TotalVenteTTC" Type="decimal" Scale="3" />
    <Property Name="Credit" Type="decimal" Scale="3" />
    <Property Name="Reglement" Type="decimal" Scale="3" />
    <Property Name="DateEcheance" Type="date" />
    <Property Name="CodePersonnel" Type="varchar" MaxLength="255" />
    <Property Name="Vendeur" Type="varchar" MaxLength="255" />
    <Property Name="Poste" Type="varchar" MaxLength="255" />
    <Property Name="NumeroOperation" Type="varchar" MaxLength="255" />
  </EntityType>
  <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « Pharma_Copie.dbo.V_Report_EtatJournalDesVentes » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
  <EntityType Name="V_Report_EtatJournalDesVentes">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" />
    <Property Name="Date" Type="date" />
    <Property Name="Exonore" Type="decimal" Scale="3" />
    <Property Name="BaseTVA6" Type="decimal" Scale="3" />
    <Property Name="TVA6" Type="decimal" Scale="3" />
    <Property Name="BaseTVA12" Type="decimal" Scale="3" />
    <Property Name="TVA12" Type="decimal" Scale="3" />
    <Property Name="BaseTVA18" Type="decimal" Scale="3" />
    <Property Name="TVA18" Type="decimal" Scale="3" />
    <Property Name="TotalHT" Type="decimal" Scale="3" />
    <Property Name="TotalTVA" Type="decimal" Scale="3" />
    <Property Name="TotalTTC" Type="decimal" Scale="3" />
    <Property Name="HR" Type="decimal" Scale="3" />
    <Property Name="NumeroFacture" Type="varchar" MaxLength="255" />
  </EntityType>
  <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « Pharma_Copie.dbo.V_Report_VentesAnnuelles » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
  <EntityType Name="V_Report_VentesAnnuelles">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" />
    <Property Name="Annee" Type="int" />
    <Property Name="Comptant" Type="decimal" Scale="3" />
    <Property Name="Credit" Type="decimal" Scale="3" />
    <Property Name="Mutuelle" Type="decimal" Scale="3" />
    <Property Name="Cnam" Type="decimal" Scale="3" />
    <Property Name="Remise" Type="decimal" Scale="3" />
    <Property Name="Caisse" Type="decimal" Scale="3" />
    <Property Name="TotalTTC" Type="decimal" Scale="3" />
  </EntityType>
  <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « Pharma_Copie.dbo.V_Report_VentesMensuelles » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
  <EntityType Name="V_Report_VentesMensuelles">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" />
    <Property Name="NumeroMois" Type="int" />
    <Property Name="NomMois" Type="varchar(max)" />
    <Property Name="Comptant" Type="decimal" Scale="3" />
    <Property Name="Credit" Type="decimal" Scale="3" />
    <Property Name="Mutuelle" Type="decimal" Scale="3" />
    <Property Name="Cnam" Type="decimal" Scale="3" />
    <Property Name="Remise" Type="decimal" Scale="3" />
    <Property Name="TotalTTC" Type="decimal" Scale="3" />
    <Property Name="Caisse" Type="decimal" Scale="3" />
    <Property Name="NumMois" Type="int" />
    <Property Name="NumYear" Type="int" />
  </EntityType>
  <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « Pharma_Copie.dbo.V_Report_VentesQuotidiennes » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
  <EntityType Name="V_Report_VentesQuotidiennes">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="int" Nullable="false" />
    <Property Name="NumeroJour" Type="int" />
    <Property Name="Comptant" Type="decimal" Scale="3" />
    <Property Name="Mutuelle" Type="decimal" Scale="3" />
    <Property Name="Cnam" Type="decimal" Scale="3" />
    <Property Name="TotalTTC" Type="decimal" Scale="3" />
    <Property Name="Reglement" Type="decimal" Scale="3" />
    <Property Name="Caisse" Type="decimal" Scale="3" />
    <Property Name="NumJour" Type="int" />
    <Property Name="NumMois" Type="int" />
    <Property Name="NumYear" Type="int" />
  </EntityType>
  <Function Name="P_Report_EtatDesFactures" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="DateDebut" Type="varchar(max)" Mode="In" />
    <Parameter Name="DateFin" Type="varchar(max)" Mode="In" />
  </Function>
  <Function Name="P_Report_EtatDesVentes" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="DateDebut" Type="date" Mode="In" />
    <Parameter Name="DateFin" Type="date" Mode="In" />
    <Parameter Name="CodeClient" Type="varchar(max)" Mode="In" />
    <Parameter Name="ModePaiement" Type="int" Mode="In" />
    <Parameter Name="TypeMutuelle" Type="varchar(max)" Mode="In" />
    <Parameter Name="CodePersonnel" Type="int" Mode="In" />
    <Parameter Name="Poste" Type="int" Mode="In" />
  </Function>
  <Function Name="P_Report_EtatDetailDesVentes" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="DateDebut" Type="varchar(max)" Mode="In" />
    <Parameter Name="DateFin" Type="varchar(max)" Mode="In" />
    <Parameter Name="CodeArticle" Type="varchar(max)" Mode="In" />
    <Parameter Name="CodeCategorie" Type="int" Mode="In" />
    <Parameter Name="CodeClient" Type="varchar(max)" Mode="In" />
  </Function>
  <Function Name="P_Report_EtatDetailsCaisse" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="DateDebut" Type="date" Mode="In" />
    <Parameter Name="DateFin" Type="date" Mode="In" />
    <Parameter Name="ModePaiement" Type="int" Mode="In" />
    <Parameter Name="Type" Type="varchar(max)" Mode="In" />
    <Parameter Name="CodePersonnel" Type="int" Mode="In" />
    <Parameter Name="Poste" Type="int" Mode="In" />
  </Function>
  <Function Name="P_Report_EtatJournalDesVentes" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="Exonorertva" Type="varchar(max)" Mode="In" />
    <Parameter Name="DateDebut" Type="varchar(max)" Mode="In" />
    <Parameter Name="DateFin" Type="varchar(max)" Mode="In" />
    <Parameter Name="TenirCompteHonoraire" Type="bit" Mode="In" />
  </Function>
  <Function Name="P_Report_VentesAnnuelles" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" />
  <Function Name="P_Report_VentesMensuelles" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="Annee" Type="varchar(max)" Mode="In" />
  </Function>
  <Function Name="P_Report_VentesQuotidienne" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="Mois" Type="int" Mode="In" />
    <Parameter Name="Annee" Type="varchar(max)" Mode="In" />
  </Function>
</Schema>