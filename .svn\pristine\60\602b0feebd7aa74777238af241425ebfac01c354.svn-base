﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="SaleReportModel" Alias="Self" p1:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:p1="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
  <EntityContainer Name="SaleReportEntities" p1:LazyLoadingEnabled="true">
    <EntitySet Name="PARAMETRE_PHARMACIE" EntityType="SaleReportModel.PARAMETRE_PHARMACIE" />
    <EntitySet Name="MOUVEMENT_ETATS" EntityType="SaleReportModel.MOUVEMENT_ETATS" />
    <EntitySet Name="CAISSE" EntityType="SaleReportModel.CAISSE" />
    <FunctionImport Name="P_Report_EtatDesFactures">
      <Parameter Name="Exonorertva" Mode="In" Type="Int32" />
      <Parameter Name="DateDebut" Mode="In" Type="String" />
      <Parameter Name="DateFin" Mode="In" Type="String" />
      <Parameter Name="TenirCompteHonoraire" Mode="In" Type="Boolean" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatDetailsTVA" ReturnType="Collection(SaleReportModel.P_Report_EtatDetailsTVA_Result)">
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="Tva" Mode="In" Type="Decimal" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatValeurTVAVente" ReturnType="Collection(SaleReportModel.P_Report_EtatValeurTVAVente_Result)">
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="Exonere" Mode="In" Type="Boolean" />
    </FunctionImport>
    <FunctionImport Name="P_Report_VentesAnnuelles" ReturnType="Collection(SaleReportModel.P_Report_VentesAnnuelles_Result)" />
    <FunctionImport Name="P_Report_VentesMensuelles" ReturnType="Collection(SaleReportModel.P_Report_VentesMensuelles_Result)">
      <Parameter Name="Annee" Mode="In" Type="Int32" />
    </FunctionImport>
    <FunctionImport Name="P_Report_VentesQuotidienne" ReturnType="Collection(SaleReportModel.P_Report_VentesQuotidienne_Result)">
      <Parameter Name="Mois" Mode="In" Type="Int32" />
      <Parameter Name="Annee" Mode="In" Type="Int32" />
    </FunctionImport>
    <FunctionImport Name="P_Relever_CNAM_MaladieOrdinaire" ReturnType="Collection(SaleReportModel.P_Relever_CNAM_MaladieOrdinaire_Result)">
      <Parameter Name="NumeroReleve" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_Report_DetailsTVA" ReturnType="Collection(SaleReportModel.P_Report_DetailsTVA_Result)">
      <Parameter Name="DateDebut" Mode="In" Type="String" />
      <Parameter Name="DateFin" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_RecapCaisse_Mutuelle" ReturnType="Collection(SaleReportModel.P_RecapCaisse_Mutuelle_Result)">
      <Parameter Name="Vide" Mode="In" Type="Boolean" />
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="Poste" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_RecapCaisse_ReglementCreditMutuelle" ReturnType="Collection(SaleReportModel.P_RecapCaisse_ReglementCreditMutuelle_Result)">
      <Parameter Name="Vide" Mode="In" Type="Boolean" />
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="Poste" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_RecapCaisse_RemiseRglement" ReturnType="Collection(SaleReportModel.P_RecapCaisse_RemiseRglement_Result)">
      <Parameter Name="Vide" Mode="In" Type="Boolean" />
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="Poste" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_RecapCaisse_RemiseVente" ReturnType="Collection(SaleReportModel.P_RecapCaisse_RemiseVente_Result)">
      <Parameter Name="Vide" Mode="In" Type="Boolean" />
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="Poste" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_RecapCaisse_RetourVente" ReturnType="Collection(SaleReportModel.P_RecapCaisse_RetourVente_Result)">
      <Parameter Name="Vide" Mode="In" Type="Boolean" />
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="Poste" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_RecapCaisse_CNAM" ReturnType="Collection(SaleReportModel.P_RecapCaisse_CNAM_Result)">
      <Parameter Name="Vide" Mode="In" Type="Boolean" />
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="Poste" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_RecapCaisse_VenteAuComptant" ReturnType="Collection(SaleReportModel.P_RecapCaisse_VenteAuComptant_Result)">
      <Parameter Name="Vide" Mode="In" Type="Boolean" />
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="Poste" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatRecapCaisse" ReturnType="Collection(SaleReportModel.P_Report_EtatRecapCaisse_Result)">
      <Parameter Name="Vide" Mode="In" Type="Boolean" />
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="Poste" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatHitParade" ReturnType="Collection(SaleReportModel.P_Report_EtatHitParade_Result)">
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="CodeCategorie" Mode="In" Type="Int32" />
      <Parameter Name="CodeForme" Mode="In" Type="Int32" />
      <Parameter Name="CodeLabo" Mode="In" Type="Int32" />
      <Parameter Name="Rayon" Mode="In" Type="String" />
      <Parameter Name="CodePersonnel" Mode="In" Type="Int32" />
    </FunctionImport>
    <FunctionImport Name="P_Etat_ListeDesReleveCNAM" ReturnType="Collection(SaleReportModel.P_Etat_ListeDesReleveCNAM_Result)">
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="Type" Mode="In" Type="String" />
      <Parameter Name="Numero" Mode="In" Type="String" />
    </FunctionImport>
    <EntitySet Name="V_Report_EtatDetailDesVentes" EntityType="SaleReportModel.V_Report_EtatDetailDesVentes" />
    <EntitySet Name="V_Report_EtatDesVentes" EntityType="SaleReportModel.V_Report_EtatDesVentes" />
    <EntitySet Name="V_Report_EtatHitParade" EntityType="SaleReportModel.V_Report_EtatHitParade" />
    <FunctionImport Name="P_Report_EtatJournalDesVentes" ReturnType="Collection(SaleReportModel.P_Report_EtatJournalDesVentes_Result)">
      <Parameter Name="Exonorertva" Mode="In" Type="Int32" />
      <Parameter Name="DateDebut" Mode="In" Type="String" />
      <Parameter Name="DateFin" Mode="In" Type="String" />
      <Parameter Name="TenirCompteHonoraire" Mode="In" Type="Boolean" />
      <Parameter Name="Facturer" Mode="In" Type="Int32" />
      <Parameter Name="Detail" Mode="In" Type="Boolean" />
    </FunctionImport>
    <EntitySet Name="V_Report_EtatJournalDesVentes" EntityType="SaleReportModel.V_Report_EtatJournalDesVentes" />
    <EntitySet Name="V_Report_EtatJournalDesVentesDetaillee" EntityType="SaleReportModel.V_Report_EtatJournalDesVentesDetaillee" />
    <FunctionImport Name="P_Report_EtatStatistiqueFournisseurs" ReturnType="Collection(SaleReportModel.P_Report_EtatStatistiqueFournisseurs_Result)">
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="TypeDate" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatJournalDesAchats" ReturnType="Collection(SaleReportModel.P_Report_EtatJournalDesAchats_Result)">
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="CodeFournisseur" Mode="In" Type="String" />
      <Parameter Name="TypeDate" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatDesVentes" ReturnType="Collection(SaleReportModel.P_Report_EtatDesVentes_Result)">
      <Parameter Name="Vide" Mode="In" Type="Boolean" />
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="CodeClient" Mode="In" Type="String" />
      <Parameter Name="ModePaiement" Mode="In" Type="Int32" />
      <Parameter Name="Type" Mode="In" Type="String" />
      <Parameter Name="CodePersonnel" Mode="In" Type="Int32" />
      <Parameter Name="Poste" Mode="In" Type="Int32" />
      <Parameter Name="Facturer" Mode="In" Type="Int32" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatDetailDesVentes" ReturnType="Collection(SaleReportModel.P_Report_EtatDetailDesVentes_Result)">
      <Parameter Name="DateDebut" Mode="In" Type="String" />
      <Parameter Name="DateFin" Mode="In" Type="String" />
      <Parameter Name="CodeArticle" Mode="In" Type="String" />
      <Parameter Name="CodeCategorie" Mode="In" Type="Int32" />
      <Parameter Name="CodeForme" Mode="In" Type="Int32" />
      <Parameter Name="CodeClient" Mode="In" Type="String" />
      <Parameter Name="RetourUniquement" Mode="In" Type="Boolean" />
      <Parameter Name="CodePersonnel" Mode="In" Type="Int32" />
      <Parameter Name="Facturer" Mode="In" Type="Int32" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatJournalReleveMutuelle" ReturnType="Collection(SaleReportModel.P_Report_EtatJournalReleveMutuelle_Result)">
      <Parameter Name="CodeMutuelle" Mode="In" Type="String" />
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatFacureJournaliere" ReturnType="Collection(SaleReportModel.P_Report_EtatFacureJournaliere_Result)">
      <Parameter Name="NumeroFacture" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatStockParCategorieIntervalleMarge" ReturnType="Collection(SaleReportModel.P_Report_EtatStockParCategorieIntervalleMarge_Result)">
      <Parameter Name="LibelleCategorie" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatListeDesMedecins" ReturnType="Collection(SaleReportModel.P_Report_EtatListeDesMedecins_Result)">
      <Parameter Name="Code" Mode="In" Type="String" />
      <Parameter Name="IdCnam" Mode="In" Type="String" />
      <Parameter Name="NomMedecin" Mode="In" Type="String" />
      <Parameter Name="Specialite" Mode="In" Type="String" />
      <Parameter Name="Ville" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatDetailsCaisse" ReturnType="Collection(SaleReportModel.P_Report_EtatDetailsCaisse_Result)">
      <Parameter Name="Vide" Mode="In" Type="Boolean" />
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="ModePaiement" Mode="In" Type="Int32" />
      <Parameter Name="Type" Mode="In" Type="String" />
      <Parameter Name="CodePersonnel" Mode="In" Type="Int32" />
      <Parameter Name="Poste" Mode="In" Type="Int32" />
      <Parameter Name="CodeClient" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_Report_EtatOrdonnancier" ReturnType="Collection(SaleReportModel.P_Report_EtatOrdonnancier_Result)">
      <Parameter Name="DateDebut" Mode="In" Type="String" />
      <Parameter Name="DateFin" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="P_RecapCaisse_Credit" ReturnType="Collection(SaleReportModel.P_RecapCaisse_Credit_Result)">
      <Parameter Name="Vide" Mode="In" Type="Boolean" />
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="Poste" Mode="In" Type="String" />
    </FunctionImport>
  </EntityContainer>
  <EntityType Name="PARAMETRE_PHARMACIE">
    <Key>
      <PropertyRef Name="Code" />
    </Key>
    <Property Type="String" Name="Code" Nullable="false" MaxLength="10" FixedLength="true" Unicode="true" />
    <Property Type="String" Name="CodePharmacie" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Pharmacie" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NCnam" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Affiliation1" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Affiliation2" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Adresse" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Telephone" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Fax" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeTVA" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Rib" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Messagederoulant1" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Messagederoulant2" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Decimal" Name="Timbre" Precision="18" Scale="3" />
    <Property Type="Boolean" Name="DemandeMotDePasse" />
    <Property Type="DateTime" Name="DateMigration" Precision="0" />
    <Property Type="String" Name="SmtpMail" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="PortMail" MaxLength="50" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="AdresseMailDestinateur" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="SujetMail" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="TexteMail" MaxLength="550" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="MotDePasseDestinateur" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Boolean" Name="AutoriserEnvoiMail" Nullable="false" />
    <Property Type="Int32" Name="NbreJourValiditeParDefaut" />
    <Property Type="String" Name="NumeroLotProduction" Nullable="false" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Type="String" Name="Latitude_Longitude" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="TailleCodeCNAM" />
    <Property Type="Int32" Name="TailleListe" />
    <Property Type="Int32" Name="TailleCaractere" />
    <Property Type="String" Name="PoliceCaractere" MaxLength="50" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Texte" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int16" Name="TauxRemise" Nullable="false" />
    <Property Type="String" Name="CodeGSU" MaxLength="5" FixedLength="false" Unicode="false" />
    <Property Type="Binary" Name="ImageCodeABarre" MaxLength="Max" FixedLength="false" />
    <Property Type="Boolean" Name="ActiverOMFAPCI" Nullable="false" />
    <Property Type="Boolean" Name="ActiverBCB" Nullable="false" />
    <Property Type="DateTime" Name="ActiverBCBDateFin" Precision="3" />
    <Property Type="String" Name="Version" MaxLength="50" FixedLength="false" Unicode="false" />
    <Property Type="Boolean" Name="GererBon" />
    <Property Type="Int32" Name="NombreJoursValiditerOrdonnance" />
    <Property Type="Int32" Name="NombreJoursValiditerPriseEnCharge" />
    <Property Type="Int32" Name="NombreJoursValiditerAppareillage" />
    <Property Type="Int32" Name="QuantiteMultipleDeCinq" Nullable="false" />
    <Property Type="Boolean" Name="ImpressionDirectApresVente" />
    <Property Type="Boolean" Name="HistoriqueMouvementArticle" />
    <Property Type="Int32" Name="NbrCommandePourClasserManquant" />
    <Property Type="Int32" Name="TypeClassementManquant" />
    <Property Type="DateTime" Name="DateDerniereMiseAJour" Precision="0" />
    <Property Type="Int32" Name="NombreCopieImpBon" />
    <Property Type="Boolean" Name="AutoriserModificationPrixDansAchat" />
    <Property Type="Boolean" Name="MettreAJourPrixFrigo" />
    <Property Type="Boolean" Name="AfficherReglementsSupprimes" />
    <Property Type="Boolean" Name="AutoriserSaisieNonMembeFamille" />
    <Property Type="Boolean" Name="ImprimerUnEtiquette" />
  </EntityType>
  <EntityType Name="MOUVEMENT_ETATS">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Type="String" Name="TypeOperation" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="Date" Precision="3" />
    <Property Type="String" Name="NumeroOperation" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Decimal" Name="Credit" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Debit" Precision="18" Scale="3" />
    <Property Type="String" Name="CodeClient" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeMutuelle" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Decimal" Name="TotalVenteHT" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalVenteTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantCnam" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantMutuelle" Precision="18" Scale="3" />
    <Property Type="String" Name="CodeFournisseur" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Decimal" Name="TotalAchatHT" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalAchatTTC" Precision="18" Scale="3" />
    <Property Type="String" Name="CodeArticle" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Designation" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="CodeCategorie" />
    <Property Type="Int32" Name="CodeForme" />
    <Property Type="Int32" Name="Quantite" />
    <Property Type="Int32" Name="QuantiteUnitaire" />
    <Property Type="Decimal" Name="PrixVenteHT" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="PrixVenteTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Honoraire" Precision="18" Scale="3" />
    <Property Type="Boolean" Name="Exonorertva" />
    <Property Type="Decimal" Name="Tva" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTVA" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="PrixAchatHT" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="PrixAchatTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Remise" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalRemise" Precision="18" Scale="3" />
    <Property Type="String" Name="NumeroReglement" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="CodeNatureReglement" />
    <Property Type="Decimal" Name="MontantRegle" Precision="18" Scale="3" />
    <Property Type="String" Name="NumeroFacture" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="DateEcheance" Precision="0" />
    <Property Type="String" Name="CodePersonnel" MaxLength="50" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="LibellePoste" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Boolean" Name="Vider" />
    <Property Type="Decimal" Name="Recu" Precision="18" Scale="3" />
    <Property Type="Boolean" Name="Supprimer" />
    <Property Type="String" Name="NumeroLot" MaxLength="255" FixedLength="false" Unicode="false" />
  </EntityType>
  <EntityType Name="CAISSE">
    <Key>
      <PropertyRef Name="Poste" />
    </Key>
    <Property Type="String" Name="Poste" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Decimal" Name="MontantCaisse" Nullable="false" Precision="18" Scale="3" />
  </EntityType>
  <ComplexType Name="P_Report_EtatDetailsTVA_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="String" Name="Designation" Nullable="true" />
    <Property Type="Int32" Name="Quantite" Nullable="true" />
    <Property Type="Decimal" Name="PrixVenteHT" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalVenteHT" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTVA" Nullable="true" Precision="18" Scale="3" />
  </ComplexType>
  <ComplexType Name="P_Report_EtatValeurTVAVente_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="Decimal" Name="TVA" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Base" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantTVA" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Honoraire" Nullable="true" Precision="18" Scale="3" />
  </ComplexType>
  <ComplexType Name="P_Report_VentesAnnuelles_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="Int32" Name="Annee" Nullable="true" />
    <Property Type="Decimal" Name="Espece" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Cheque" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Carte" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Remise" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Autre" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="ReglementClient" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Reglementcnammutuelle" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Caisse" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
  </ComplexType>
  <ComplexType Name="P_Report_VentesMensuelles_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="Int32" Name="Mois" Nullable="true" />
    <Property Type="Decimal" Name="Espece" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Cheque" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Carte" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Remise" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Autre" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="ReglementClient" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Reglementcnammutuelle" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Caisse" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
  </ComplexType>
  <ComplexType Name="P_Report_VentesQuotidienne_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="Int32" Name="NumeroJour" Nullable="true" />
    <Property Type="Decimal" Name="Espece" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Cheque" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Carte" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Remise" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Autre" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="ReglementClient" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Reglementcnammutuelle" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Caisse" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
  </ComplexType>
  <ComplexType Name="P_Relever_CNAM_MaladieOrdinaire_Result">
    <Property Type="String" Name="NumeroVente" Nullable="false" MaxLength="255" />
    <Property Type="String" Name="Ligne" Nullable="true" />
  </ComplexType>
  <ComplexType Name="P_Report_DetailsTVA_Result">
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="String" Name="Designation" Nullable="true" MaxLength="255" />
    <Property Type="Int32" Name="Quantite" Nullable="true" />
    <Property Type="Decimal" Name="PrixVenteHT" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalVenteHT" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTVA" Nullable="true" Precision="18" Scale="3" />
  </ComplexType>
  <ComplexType Name="P_RecapCaisse_Mutuelle_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="Numero" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="String" Name="Client" Nullable="true" />
    <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="MP" Nullable="true" />
    <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
    <Property Type="String" Name="Poste" Nullable="true" />
    <Property Type="String" Name="Utilisateur" Nullable="true" />
  </ComplexType>
  <ComplexType Name="P_RecapCaisse_ReglementCreditMutuelle_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="Numero" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="String" Name="Client" Nullable="true" />
    <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="MP" Nullable="true" />
    <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
    <Property Type="String" Name="Libelle" Nullable="true" />
    <Property Type="Decimal" Name="Solde" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="Poste" Nullable="true" />
    <Property Type="String" Name="Utilisateur" Nullable="true" />
  </ComplexType>
  <ComplexType Name="P_RecapCaisse_RemiseRglement_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="Numero" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="String" Name="Client" Nullable="true" />
    <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="MP" Nullable="true" />
    <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
    <Property Type="String" Name="Libelle" Nullable="true" />
    <Property Type="String" Name="Poste" Nullable="true" />
    <Property Type="String" Name="Utilisateur" Nullable="true" />
  </ComplexType>
  <ComplexType Name="P_RecapCaisse_RemiseVente_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="Numero" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="String" Name="Client" Nullable="true" />
    <Property Type="Decimal" Name="Remise" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="MP" Nullable="true" />
    <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
    <Property Type="String" Name="Poste" Nullable="true" />
    <Property Type="String" Name="Utilisateur" Nullable="true" />
  </ComplexType>
  <ComplexType Name="P_RecapCaisse_RetourVente_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="Numero" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="String" Name="Client" Nullable="true" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="MP" Nullable="true" />
    <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
    <Property Type="String" Name="Poste" Nullable="true" />
    <Property Type="String" Name="Utilisateur" Nullable="true" />
  </ComplexType>
  <ComplexType Name="P_RecapCaisse_CNAM_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="Numero" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="String" Name="Client" Nullable="true" />
    <Property Type="Decimal" Name="MontantCNAM" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="MP" Nullable="true" />
    <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
    <Property Type="String" Name="Poste" Nullable="true" />
    <Property Type="String" Name="Utilisateur" Nullable="true" />
  </ComplexType>
  <ComplexType Name="P_RecapCaisse_VenteAuComptant_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="Numero" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="String" Name="Client" Nullable="true" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="MP" Nullable="true" />
    <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
    <Property Type="String" Name="Type" Nullable="true" />
    <Property Type="String" Name="Poste" Nullable="true" />
    <Property Type="String" Name="Utilisateur" Nullable="true" />
  </ComplexType>
  <ComplexType Name="P_Report_EtatRecapCaisse_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="NumeroOperation" Nullable="true" />
    <Property Type="String" Name="TypeOperation" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="String" Name="Designation" Nullable="true" />
    <Property Type="Int32" Name="Quantite" Nullable="true" />
    <Property Type="Decimal" Name="Tva" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTVA" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Remise" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalVenteTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Debit" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalVenteHT" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Int32" Name="CodeNatureReglement" Nullable="true" />
  </ComplexType>
  <ComplexType Name="P_Report_EtatHitParade_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="CodeABarre" Nullable="true" />
    <Property Type="String" Name="Designation" Nullable="true" />
    <Property Type="String" Name="LibelleForme" Nullable="true" />
    <Property Type="String" Name="LibelleCategorie" Nullable="true" />
    <Property Type="Int32" Name="Quantite" Nullable="true" />
    <Property Type="Int32" Name="Stock" Nullable="true" />
    <Property Type="Decimal" Name="TotalVenteTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="Rayon" Nullable="true" />
    <Property Type="String" Name="nom" Nullable="false" MaxLength="1" />
    <Property Type="DateTime" Name="DatePeremption" Nullable="true" />
  </ComplexType>
  <ComplexType Name="P_Etat_ListeDesReleveCNAM_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="NumeroReleve" Nullable="true" />
    <Property Type="String" Name="Type" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="DateTime" Name="DateDebut" Nullable="true" Precision="23" />
    <Property Type="DateTime" Name="DateFin" Nullable="true" Precision="23" />
    <Property Type="Decimal" Name="MontantARembourser" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Reste" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantTotal" Nullable="true" Precision="18" Scale="3" />
  </ComplexType>
  <EntityType Name="V_Report_EtatDetailDesVentes">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="String" Name="NumeroOperation" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="Date" Precision="0" />
    <Property Type="String" Name="Designation" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="LibelleForme" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="LibelleCategorie" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Nom" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="Quantite" />
    <Property Type="Decimal" Name="Tva" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTVA" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Remise" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalVenteTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalMutuelle" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalCNAM" Precision="18" Scale="3" />
    <Property Type="String" Name="CodeArticle" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeABarre" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeClient" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="Decimal" Name="TotalVenteHT" Precision="18" Scale="3" />
    <Property Type="String" Name="NumeroFacture" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="CodeCategorie" />
    <Property Type="Int32" Name="CodeForme" />
  </EntityType>
  <EntityType Name="V_Report_EtatDesVentes">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int64" Name="Id" Nullable="false" />
    <Property Type="String" Name="NumeroOperation" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="Date" Precision="0" />
    <Property Type="String" Name="Type" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Nom" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="MP" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="Decimal" Name="TotalRemise" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Precision="18" Scale="3" />
    <Property Type="String" Name="LibellePoste" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NomUtilisateur" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeClient" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="TypeOperation" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NumeroFacture" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="CodeNatureReglement" />
    <Property Type="String" Name="CodePersonnel" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeMutuelle" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Mutuelle" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="Decimal" Name="MontantCnam" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantMutuelle" Precision="18" Scale="3" />
    <Property Type="Boolean" Name="Vider" />
    <Property Type="Decimal" Name="Credit" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Debit" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Marge" Precision="18" Scale="3" />
  </EntityType>
  <EntityType Name="V_Report_EtatHitParade">
    <Key>
      <PropertyRef Name="nom" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="String" Name="CodeABarre" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Designation" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="LibelleForme" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="LibelleCategorie" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="Quantite" />
    <Property Type="Int32" Name="Stock" />
    <Property Type="Decimal" Name="TotalVenteTTC" Precision="18" Scale="3" />
    <Property Type="String" Name="Rayon" MaxLength="Max" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="nom" Nullable="false" MaxLength="1" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="DatePeremption" Precision="0" />
    <Property Type="DateTime" Name="Date" Precision="3" />
    <Property Type="Int32" Name="CodeArticle" />
    <Property Type="Int32" Name="CodeLabo" />
    <Property Type="Int32" Name="CodeForme" />
    <Property Type="Int32" Name="CodeCategorie" />
    <Property Type="String" Name="CodePersonnel" MaxLength="Max" FixedLength="false" Unicode="false" />
  </EntityType>
  <ComplexType Name="P_Report_EtatJournalDesVentes_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" />
    <Property Type="Decimal" Name="Exonore" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="BaseTVA6" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA6" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="BaseTVA12" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA12" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="BaseTVA18" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA18" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalHT" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTVA" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="38" Scale="3" />
    <Property Type="Decimal" Name="HR" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="Nom" Nullable="true" MaxLength="255" />
    <Property Type="String" Name="NumeroFacture" Nullable="true" MaxLength="255" />
  </ComplexType>
  <EntityType Name="V_Report_EtatJournalDesVentes">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="DateTime" Name="Date" Precision="0" />
    <Property Type="Decimal" Name="Exonore" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="BaseTVA6" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA6" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="BaseTVA12" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA12" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="BaseTVA18" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA18" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalHT" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTVA" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="HR" Precision="18" Scale="3" />
    <Property Type="String" Name="Nom" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NumeroFacture" MaxLength="255" FixedLength="false" Unicode="false" />
  </EntityType>
  <EntityType Name="V_Report_EtatJournalDesVentesDetaillee">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="DateTime" Name="Date" Precision="0" />
    <Property Type="Decimal" Name="Exonore" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="BaseTVA6" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA6" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="BaseTVA12" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA12" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="BaseTVA18" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA18" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalHT" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTVA" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="HR" Precision="18" Scale="3" />
    <Property Type="String" Name="Nom" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NumeroFacture" MaxLength="255" FixedLength="false" Unicode="false" />
  </EntityType>
  <ComplexType Name="P_Report_EtatStatistiqueFournisseurs_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="NomFournisseur" Nullable="true" MaxLength="255" />
    <Property Type="Decimal" Name="HT" Nullable="true" Precision="38" Scale="3" />
    <Property Type="Decimal" Name="TVA" Nullable="true" Precision="38" Scale="3" />
    <Property Type="Decimal" Name="TTC" Nullable="true" Precision="38" Scale="3" />
    <Property Type="Decimal" Name="Remise" Nullable="true" Precision="38" Scale="3" />
    <Property Type="Decimal" Name="VenteTTC" Nullable="true" Precision="38" Scale="3" />
    <Property Type="Decimal" Name="ResteAPayer" Nullable="true" Precision="38" Scale="3" />
  </ComplexType>
  <ComplexType Name="P_Report_EtatJournalDesAchats_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="NumeroAchat" Nullable="false" MaxLength="255" />
    <Property Type="DateTime" Name="Date" Nullable="false" Precision="23" />
    <Property Type="DateTime" Name="DateBlFacture" Nullable="false" />
    <Property Type="String" Name="NomFournisseur" Nullable="true" MaxLength="255" />
    <Property Type="String" Name="NumeroBL_Facture" Nullable="true" MaxLength="255" />
    <Property Type="Decimal" Name="TotalHT" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalRemise" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="String" Name="CodeFournisseur" Nullable="true" MaxLength="255" />
    <Property Type="Decimal" Name="ValeurVenteTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTVAVente" Nullable="true" Precision="38" Scale="6" />
  </ComplexType>
  <ComplexType Name="P_Report_EtatDesVentes_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="NumeroOperation" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="String" Name="Type" Nullable="true" />
    <Property Type="String" Name="Nom" Nullable="true" />
    <Property Type="String" Name="MP" Nullable="true" />
    <Property Type="Decimal" Name="TotalRemise" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="LibellePoste" Nullable="true" />
    <Property Type="String" Name="NomUtilisateur" Nullable="true" />
    <Property Type="String" Name="CodeClient" Nullable="true" />
    <Property Type="String" Name="TypeOperation" Nullable="true" />
    <Property Type="String" Name="NumeroFacture" Nullable="true" />
    <Property Type="Int32" Name="CodeNatureReglement" Nullable="true" />
    <Property Type="String" Name="CodePersonnel" Nullable="true" />
    <Property Type="String" Name="CodeMutuelle" Nullable="true" />
    <Property Type="String" Name="Mutuelle" Nullable="true" />
    <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Boolean" Name="Vider" Nullable="true" />
    <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Debit" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Marge" Nullable="true" Precision="18" Scale="3" />
  </ComplexType>
  <ComplexType Name="P_Report_EtatDetailDesVentes_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="NumeroOperation" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="String" Name="Designation" Nullable="true" />
    <Property Type="String" Name="LibelleForme" Nullable="true" />
    <Property Type="String" Name="LibelleCategorie" Nullable="true" />
    <Property Type="String" Name="Nom" Nullable="true" />
    <Property Type="Int32" Name="Quantite" Nullable="true" />
    <Property Type="Decimal" Name="Tva" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTVA" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Remise" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalVenteTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalMutuelle" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalCNAM" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="CodeArticle" Nullable="true" />
    <Property Type="String" Name="CodeABarre" Nullable="true" />
    <Property Type="String" Name="CodeClient" Nullable="true" />
    <Property Type="Decimal" Name="TotalVenteHT" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="NumeroFacture" Nullable="true" />
    <Property Type="Int32" Name="CodeCategorie" Nullable="true" />
    <Property Type="Int32" Name="CodeForme" Nullable="true" />
    <Property Type="Decimal" Name="PrixVenteHT" Nullable="true" Precision="18" Scale="3" />
  </ComplexType>
  <ComplexType Name="P_Report_EtatJournalReleveMutuelle_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="NumeroReleve" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="DateTime" Name="DateDebut" Nullable="true" Precision="23" />
    <Property Type="DateTime" Name="DateFin" Nullable="true" Precision="23" />
    <Property Type="Decimal" Name="MontantTotal" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantRegle" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Reste" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="CodeMutuelle" Nullable="true" />
    <Property Type="String" Name="NomMutuelle" Nullable="true" />
  </ComplexType>
  <ComplexType Name="P_Report_EtatFacureJournaliere_Result">
    <Property Type="DateTime" Name="Date" Nullable="true" />
    <Property Type="Decimal" Name="TotalHT" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTVA" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Caisse" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
  </ComplexType>
  <ComplexType Name="P_Report_EtatStockParCategorieIntervalleMarge_Result">
    <Property Type="Decimal" Name="Marge" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="ValeurAchatHT" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="ValeurAchatHTP" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="ValeurVenteHT" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="ValeurVenteHTP" Nullable="true" Precision="18" Scale="3" />
  </ComplexType>
  <ComplexType Name="P_Report_EtatListeDesMedecins_Result">
    <Property Type="String" Name="CodeMedecin" Nullable="false" MaxLength="255" />
    <Property Type="String" Name="NomMedecin" Nullable="false" MaxLength="255" />
    <Property Type="String" Name="LibelleSpecialite" Nullable="true" MaxLength="255" />
    <Property Type="Int32" Name="CodeSpecialite" Nullable="true" />
    <Property Type="String" Name="Adresse" Nullable="false" MaxLength="255" />
    <Property Type="String" Name="Tel" Nullable="false" MaxLength="255" />
    <Property Type="String" Name="Fax" Nullable="false" MaxLength="255" />
    <Property Type="String" Name="Email" Nullable="false" MaxLength="255" />
    <Property Type="String" Name="IdentifiantCNAM" Nullable="false" MaxLength="255" />
    <Property Type="String" Name="NomVille" Nullable="true" MaxLength="255" />
    <Property Type="Int32" Name="CodeVille" Nullable="true" />
    <Property Type="Boolean" Name="bloquer" Nullable="false" />
  </ComplexType>
  <ComplexType Name="P_Report_EtatDetailsCaisse_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="Operation" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="String" Name="Nom" Nullable="true" />
    <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="NatureReglement" Nullable="true" />
    <Property Type="DateTime" Name="DateEcheance" Nullable="true" />
    <Property Type="String" Name="Vendeur" Nullable="true" />
    <Property Type="String" Name="Poste" Nullable="true" />
    <Property Type="Int32" Name="CodeNatureReglement" Nullable="true" />
    <Property Type="String" Name="Type" Nullable="true" />
    <Property Type="String" Name="TypeOperation" Nullable="true" />
    <Property Type="String" Name="CodeClient" Nullable="true" />
    <Property Type="Decimal" Name="TotalVenteTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Remise" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="CodePersonnel" Nullable="true" />
    <Property Type="String" Name="NumeroOperation" Nullable="true" />
    <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Debit" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
  </ComplexType>
  <ComplexType Name="P_Report_EtatOrdonnancier_Result">
    <Property Type="DateTime" Name="DateVente" Nullable="false" Precision="23" />
    <Property Type="DateTime" Name="DateOrdonnancier" Nullable="false" Precision="23" />
    <Property Type="String" Name="NomMedecin" Nullable="true" MaxLength="255" />
    <Property Type="Int32" Name="Numero" Nullable="false" />
    <Property Type="Int32" Name="NumeroOrdonnacier" Nullable="false" />
    <Property Type="String" Name="LibelleForme" Nullable="true" MaxLength="255" />
    <Property Type="String" Name="Designation" Nullable="true" MaxLength="255" />
    <Property Type="Int32" Name="Quantite" Nullable="true" />
    <Property Type="String" Name="Malade" Nullable="false" MaxLength="100" />
    <Property Type="String" Name="CIN" Nullable="false" MaxLength="50" />
    <Property Type="String" Name="Adresse" Nullable="false" MaxLength="255" />
    <Property Type="String" Name="TypeOrdonnance" Nullable="true" MaxLength="50" />
    <Property Type="String" Name="NumeroVente" Nullable="true" MaxLength="50" />
    <Property Type="Boolean" Name="Supprimer" Nullable="false" />
    <Property Type="Int64" Name="Row" Nullable="true" />
  </ComplexType>
  <ComplexType Name="P_RecapCaisse_Credit_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="Numero" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="String" Name="Client" Nullable="true" />
    <Property Type="Decimal" Name="Solde" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantCNAM" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="MP" Nullable="true" />
    <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
    <Property Type="String" Name="Poste" Nullable="true" />
    <Property Type="String" Name="Utilisateur" Nullable="true" />
  </ComplexType>
</Schema>