﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fHistoriqueDesChangementsDesPrix
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fHistoriqueDesChangementsDesPrix))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bSupprimerCocher = New C1.Win.C1Input.C1Button()
        Me.cmbUser = New C1.Win.C1List.C1Combo()
        Me.bSupprimerUser = New C1.Win.C1Input.C1Button()
        Me.dtpDate = New C1.Win.C1Input.C1DateEdit()
        Me.bSupprimerDate = New C1.Win.C1Input.C1Button()
        Me.lTitre = New System.Windows.Forms.Label()
        Me.gListe = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.Panel.SuspendLayout()
        CType(Me.cmbUser, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpDate, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gListe, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.bSupprimerCocher)
        Me.Panel.Controls.Add(Me.cmbUser)
        Me.Panel.Controls.Add(Me.bSupprimerUser)
        Me.Panel.Controls.Add(Me.dtpDate)
        Me.Panel.Controls.Add(Me.bSupprimerDate)
        Me.Panel.Controls.Add(Me.lTitre)
        Me.Panel.Controls.Add(Me.gListe)
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(773, 566)
        Me.Panel.TabIndex = 12
        '
        'bSupprimerCocher
        '
        Me.bSupprimerCocher.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bSupprimerCocher.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimerCocher.Location = New System.Drawing.Point(612, 483)
        Me.bSupprimerCocher.Name = "bSupprimerCocher"
        Me.bSupprimerCocher.Size = New System.Drawing.Size(137, 45)
        Me.bSupprimerCocher.TabIndex = 55
        Me.bSupprimerCocher.Text = "Cacher les changements Cochés"
        Me.bSupprimerCocher.UseVisualStyleBackColor = True
        Me.bSupprimerCocher.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbUser
        '
        Me.cmbUser.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbUser.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.cmbUser.Caption = ""
        Me.cmbUser.CaptionHeight = 17
        Me.cmbUser.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbUser.ColumnCaptionHeight = 17
        Me.cmbUser.ColumnFooterHeight = 17
        Me.cmbUser.ContentHeight = 16
        Me.cmbUser.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbUser.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbUser.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbUser.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbUser.EditorHeight = 16
        Me.cmbUser.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbUser.Images.Add(CType(resources.GetObject("cmbUser.Images"), System.Drawing.Image))
        Me.cmbUser.ItemHeight = 15
        Me.cmbUser.Location = New System.Drawing.Point(311, 534)
        Me.cmbUser.MatchEntryTimeout = CType(2000, Long)
        Me.cmbUser.MaxDropDownItems = CType(5, Short)
        Me.cmbUser.MaxLength = 32767
        Me.cmbUser.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbUser.Name = "cmbUser"
        Me.cmbUser.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbUser.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbUser.Size = New System.Drawing.Size(138, 22)
        Me.cmbUser.TabIndex = 54
        Me.cmbUser.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbUser.PropBag = resources.GetString("cmbUser.PropBag")
        '
        'bSupprimerUser
        '
        Me.bSupprimerUser.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bSupprimerUser.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimerUser.Location = New System.Drawing.Point(312, 483)
        Me.bSupprimerUser.Name = "bSupprimerUser"
        Me.bSupprimerUser.Size = New System.Drawing.Size(137, 45)
        Me.bSupprimerUser.TabIndex = 53
        Me.bSupprimerUser.Text = "Cacher les changements de"
        Me.bSupprimerUser.UseVisualStyleBackColor = True
        Me.bSupprimerUser.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'dtpDate
        '
        Me.dtpDate.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.dtpDate.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDate.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDate.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDate.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDate.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDate.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.dtpDate.Location = New System.Drawing.Point(8, 531)
        Me.dtpDate.Name = "dtpDate"
        Me.dtpDate.Size = New System.Drawing.Size(175, 18)
        Me.dtpDate.TabIndex = 52
        Me.dtpDate.Tag = Nothing
        Me.dtpDate.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDate.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bSupprimerDate
        '
        Me.bSupprimerDate.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bSupprimerDate.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimerDate.Location = New System.Drawing.Point(24, 483)
        Me.bSupprimerDate.Name = "bSupprimerDate"
        Me.bSupprimerDate.Size = New System.Drawing.Size(137, 45)
        Me.bSupprimerDate.TabIndex = 33
        Me.bSupprimerDate.Text = "Cacher les changements avant"
        Me.bSupprimerDate.UseVisualStyleBackColor = True
        Me.bSupprimerDate.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lTitre
        '
        Me.lTitre.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lTitre.Font = New System.Drawing.Font("HandelGotDLig", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTitre.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.lTitre.Location = New System.Drawing.Point(11, 7)
        Me.lTitre.Name = "lTitre"
        Me.lTitre.Size = New System.Drawing.Size(627, 80)
        Me.lTitre.TabIndex = 31
        Me.lTitre.Text = "HISTORIQUE DES CHANGEMENTS DES PRIX "
        Me.lTitre.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'gListe
        '
        Me.gListe.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gListe.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gListe.GroupByCaption = "Drag a column header here to group by that column"
        Me.gListe.Images.Add(CType(resources.GetObject("gListe.Images"), System.Drawing.Image))
        Me.gListe.LinesPerRow = 2
        Me.gListe.Location = New System.Drawing.Point(11, 90)
        Me.gListe.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gListe.Name = "gListe"
        Me.gListe.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gListe.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gListe.PreviewInfo.ZoomFactor = 75.0R
        Me.gListe.PrintInfo.PageSettings = CType(resources.GetObject("gListe.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gListe.Size = New System.Drawing.Size(749, 387)
        Me.gListe.TabIndex = 4
        Me.gListe.Text = "C1TrueDBGrid1"
        Me.gListe.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gListe.PropBag = resources.GetString("gListe.PropBag")
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.Location = New System.Drawing.Point(644, 9)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(116, 45)
        Me.bQuitter.TabIndex = 3
        Me.bQuitter.Text = "Fermer            F12"
        Me.bQuitter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fHistoriqueDesChangementsDesPrix
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(773, 566)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fHistoriqueDesChangementsDesPrix"
        Me.Text = "fHistoriqueDesChangementsDesPrix"
        Me.Panel.ResumeLayout(False)
        Me.Panel.PerformLayout()
        CType(Me.cmbUser, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpDate, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gListe, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents lTitre As System.Windows.Forms.Label
    Friend WithEvents gListe As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents dtpDate As C1.Win.C1Input.C1DateEdit
    Friend WithEvents bSupprimerDate As C1.Win.C1Input.C1Button
    Friend WithEvents bSupprimerUser As C1.Win.C1Input.C1Button
    Friend WithEvents cmbUser As C1.Win.C1List.C1Combo
    Friend WithEvents bSupprimerCocher As C1.Win.C1Input.C1Button
End Class
