﻿Imports System.Data.SqlClient
Imports System.IO

Public Class fMotDePasse

    Public Shared CodeOperateur As String = ""
    Public Shared Confirmer As Boolean = False

    Dim UtilisateurCode As String = ""
    Dim StrSQL As String = ""

    Public Shared cmdMotDePasse As New SqlCommand

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Confirmer = False
        CodeOperateur = ""
        Me.Hide()
    End Sub

    Private Sub bOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOK.Click

        'If tMotDePasse.Text = "" Then
        '    tMotDePasse.Focus()
        '    MsgBox("Vous devez insérer votre mot de passe !", MsgBoxStyle.Critical, "Erreur")
        '    Exit Sub
        'End If

        StrSQL = "SELECT COUNT([CodeUtilisateur]) FROM UTILISATEUR WHERE Supprime=0 AND [MotPass]='" + tMotDePasse.Text + "'"
        cmdMotDePasse.Connection = ConnectionServeur
        cmdMotDePasse.CommandText = StrSQL

        Try
            UtilisateurCode = cmdMotDePasse.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
            UtilisateurCode = 0
        End Try

        If UtilisateurCode = 0 Then
            tMotDePasse.Text = ""
            tMotDePasse.Focus()
            MsgBox("Mot de passe erroné !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        StrSQL = "SELECT [CodeUtilisateur] FROM UTILISATEUR WHERE Supprime=0 AND [MotPass]='" + tMotDePasse.Text + "'"
        cmdMotDePasse.Connection = ConnectionServeur

        cmdMotDePasse.CommandText = StrSQL

        Try
            UtilisateurCode = cmdMotDePasse.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

       
        CodeOperateur = UtilisateurCode
        Confirmer = True
        Me.Hide()
    End Sub

    Private Sub fMotDePasse_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        'Confirmer = False
    End Sub

    Private Sub fMotDePasse_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        Confirmer = False

        tMotDePasse.Focus()
    End Sub

    Private Sub tMotDePasse_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tMotDePasse.KeyUp
        'If e.KeyCode = Keys.Enter Then
        '    'bOK_Click(sender, e)
        '    bOK.Focus()
        'End If
    End Sub

    Private Sub tMotDePasse_PreviewKeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.PreviewKeyDownEventArgs) Handles tMotDePasse.PreviewKeyDown
        If e.KeyCode = Keys.Enter Then
            'bOK_Click(sender, e)
            bOK.Focus()
        End If
    End Sub
End Class