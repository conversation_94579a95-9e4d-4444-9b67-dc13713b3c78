﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fEtatReleveMouvementCNAM
    Dim cmdCNAM As New SqlCommand
    Dim daCNAM As New SqlDataAdapter
    Dim dsCNAM As New DataSet

    Dim TotalDebit As Double = 0
    Dim TotalCredit As Double = 0
    Dim Reste As Double = 0
    Dim CondCrystalReport As String = ""

    Dim Initialisation As Boolean = False

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub Init()
        'dtpDebut.Text = "01/" + Date.Today.Month.ToString.PadLeft(2, "0") + "/" + Date.Now.Year.ToString
        If DateMigration = Nothing Then
            dtpDebut.Text = "01/" + Date.Today.Month.ToString.PadLeft(2, "0") + "/" + Date.Now.Year.ToString
        Else
            dtpDebut.Text = DateMigration
        End If
        dtpFin.Text = Today
        dtpDebut.Focus()

        Initialisation = True
        AfficherCNAM()
    End Sub
    Public Sub AfficherCNAM()
        Dim I As Integer
        Dim Cond As String = "1=1"
        CondCrystalReport = "1=1"
        dsCNAM.Clear()

        If dtpDebut.Text <> "" And dtpFin.Text <> "" And dtpDebut.Text.Length = 10 And dtpFin.Text.Length = 10 Then
            Cond += " AND CAST(DateMouvement as date) BETWEEN " + Quote(dtpDebut.Text) + " AND " + Quote(dtpFin.Text) 'Quote(DateAdd(DateInterval.Day, 1, dtpFin.Value).ToString)
            CondCrystalReport += " AND {Vue_ReleveMouvementCNAM.DateMouvement} >= DateTime('" + dtpDebut.Text + " 00:00:00')"
            CondCrystalReport += " AND {Vue_ReleveMouvementCNAM.DateMouvement} <= DateTime('" + dtpFin.Text + " 23:59:59')"
        End If

        If Initialisation = True Then
            cmdCNAM.CommandText = "SELECT TOP(0) "
            Initialisation = False
        Else
            cmdCNAM.CommandText = "SELECT "
        End If

        cmdCNAM.CommandText += "  " + _
                                " Type, " + _
                                " Numero, " + _
                                " DateMouvement, " + _
                                " Débit, " + _
                                " Crédit, " + _
                                " ModePaiement, " + _
                                " Libellé " + _
                                " FROM Vue_ReleveMouvementCNAM " + _
                                " WHERE " + Cond + " ORDER BY DateMouvement ASC"

        cmdCNAM.Connection = ConnectionServeur
        daCNAM = New SqlDataAdapter(cmdCNAM)
        daCNAM.Fill(dsCNAM, "CNAM")

        With gCNAM
            .Columns.Clear()
            .DataSource = dsCNAM
            .DataMember = "CNAM"
            .Rebind(False)
            .Columns("Type").Caption = "Type"
            .Columns("Numero").Caption = "Numéro"
            .Columns("DateMouvement").Caption = "Date"
            .Columns("Débit").Caption = "Débit"
            .Columns("Crédit").Caption = "Crédit"
            .Columns("ModePaiement").Caption = "Paiement"
            .Columns("Libellé").Caption = "Libellé"
            .Columns("DateMouvement").NumberFormat = "dd/MM/yyyy"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("DateMouvement").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("DateMouvement").Width = 100
            .Splits(0).DisplayColumns("Numero").Width = 100
            .Splits(0).DisplayColumns("Numero").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Débit").Width = 150
            .Splits(0).DisplayColumns("Débit").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Crédit").Width = 150
            .Splits(0).DisplayColumns("Crédit").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("ModePaiement").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("ModePaiement").Width = 120

            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gCNAM)
        End With
        CalculValeur()
    End Sub

    Private Sub gCNAM_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gCNAM.FetchRowStyle
        'e.CellStyle.Font = New System.Drawing.Font("Calibri", 9, FontStyle.Regular)
    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter And dtpDebut.Text <> "" Then
            AfficherCNAM()
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDebut.TextChanged

    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter And dtpFin.Text <> "" Then
            AfficherCNAM()
            gCNAM.Focus()
        End If
    End Sub

    Private Sub dtpFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpFin.TextChanged

    End Sub

    Private Sub CalculValeur()
        TotalDebit = 0.0
        TotalCredit = 0.0

        If gCNAM.RowCount <> 0 Then
            For I As Integer = 0 To gCNAM.RowCount - 1
                TotalDebit += gCNAM(I, "Débit")
                TotalCredit += gCNAM(I, "Crédit")
            Next
        End If
        tTotalDebit.Text = TotalDebit.ToString("### ### ##0.000") 'Format(TotalDebit, "0.000")
        tTotalCredit.Text = TotalCredit.ToString("### ### ##0.000")  'Format(TotalCredit, "0.000")
        tReste.Text = (TotalDebit - TotalCredit).ToString("### ### ##0.000") 'Format(TotalDebit - TotalCredit, "0.000")
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        If gCNAM.RowCount > 0 Then
            Dim I As Integer
            For I = 0 To fMain.Tab.TabPages.Count - 1
                If fMain.Tab.TabPages(I).Text = "Imprimer Relevé des mouvements CNAM" Then
                    fMain.Tab.TabPages(I).Show()
                    Exit Sub
                End If
            Next
            CR.FileName = Application.StartupPath + "\EtatReleveMouvementCNAM.rpt"
            CR.SetParameterValue("debut", dtpDebut.Text)
            CR.SetParameterValue("fin", dtpFin.Text)
            CR.SetParameterValue("Solde", tReste.Text)

            Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent
            CR.RecordSelectionFormula = CondCrystalReport
            Dim MyViewer As New fViewer
            MyViewer.CRViewer.ReportSource = CR
            fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
            fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
            fMain.Tab.SelectedTab.Text = "Imprimer Relevé des mouvements CNAM"
        Else
            MsgBox("Liste vide !", MsgBoxStyle.Critical, "Erreur")
            dtpDebut.Focus()
        End If
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub fEtatJournalReleveCNAM_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

    End Sub
End Class