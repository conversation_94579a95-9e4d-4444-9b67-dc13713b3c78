﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms
Imports Microsoft.Office.Interop
Imports C1.C1Excel
Imports System.Diagnostics

Public Class fCommande

    Dim cmdChargementCommande As New SqlCommand
    Dim cbChargementCommande As New SqlCommandBuilder
    Dim dsChargementCommande As New DataSet
    Dim daChargementCommande As New SqlDataAdapter


    Dim cmdEnteteCommande As New SqlCommand
    Dim cbEnteteCommande As New SqlCommandBuilder
    Dim dsEnteteCommande As New DataSet
    Dim daEnteteCommande As New SqlDataAdapter

    Dim cmdDetailCommande As New SqlCommand
    Dim cbDetailCommande As New SqlCommandBuilder
    Dim dsDetailCommande As New DataSet
    Dim daDetailCommande As New SqlDataAdapter

    Dim cmdCommande As New SqlCommand
    Dim cbCommande As New SqlCommandBuilder
    Public Shared dsCommande As New DataSet
    Dim daCommande As New SqlDataAdapter

    Dim mode As String = ""
    Public NumeroligneCommande As Integer = 0

    Public NumeroCommande As String = ""

    Public TotalTTCCommande As Double = 0.0
    Public TotalHTCommande As Double = 0.0
    Public TotalTVACommande As Double = 0.0

    Dim DataRowRecherche As DataRow

    Public TVA As Double = 0.0
    Public Timbre As Double = 0.3
    Dim unite As Double = 0.0
    Dim StrSQL As String = ""
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNum As New SqlDataAdapter

    '****************************************************************************************
    '******************************* Critéres de selection Commande *************************
    '****************************************************************************************
    Public TypeCommande As String = ""                'journalière ou groupé
    Public NombreDeJour As Integer = 0
    Public SansManquantDepuis As String = ""
    Public Reference As String = ""
    Public mois As Integer = 0
    Public DebutPeriode As String = ""
    Public FinPeriode As String = ""
    Public Section As String = ""
    Public DebutIntervalle As String = ""
    Public FinIntervalle As String = ""
    Public Forme As Integer = 0
    Public Categorie As Integer = 0
    Public Labo As Integer = 0
    Public Rayon As String = ""
    Public RayonSelectionne As String = ""
    Public Trie As String = ""
    Public CommandeEnCours As Boolean = False
    Public TenirCompteStockAlerte As Boolean
    Public TenirCompteStock As Boolean
    Public TenirCompteStockAlerteCJ As Boolean

    Public StatistiqueAnneePrecidente As Boolean = False

    '------------------------------------------------- les informations de paiement (retour de fenetre paiement)
    Public Operateur As Integer = 0

    Public NouvelleCommande As DataRow = Nothing 'datarow pour charger l'entête dans la datatable ACHAT
    Public NouvelArticle As DataRow = Nothing 'datarow pour charger les détails dans la datatable ACHAT_DETAIL
    Public dr As DataRow = Nothing 'datarow pour charger dans la datatable ARTICLE qui contient les articles recherchés pendant la saisie de la designation

    '---------------------------------------- variable pour recuperer le code article à partir de la fenêtre recherchre multicritere
    Dim CodeArticleRechercheMC As String = ""

    '---------------------------------------- variables pour confirmer la mise en instance d'une Commande
    Dim ComfirmerMettreEnINstance As Boolean = False
    Dim ConfirmerInstance As Boolean = False
    Dim NomInstance As String = ""
    Dim CodeOperateurInstance As String = ""

    Dim NombreCommandeInstance As Integer = 0

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "113" And bFournisseur.Enabled = True Then
            bFournisseur_Click(sender, e)
        End If
        If argument = "114" And bConfirmer.Enabled = True Then
            bConfirmer_Click(sender, e)
        End If
        If argument = "116" And bAjouter.Enabled = True Then
            bAjouter_Click(sender, e)
        End If
        If argument = "117" And bInstance.Enabled = True Then
            bInstance_Click(sender, e)
        End If
        If argument = "118" And bSupprimer.Enabled = True Then
            bSupprimer_Click(sender, e)
        End If
        If argument = "119" And bModifier.Enabled = True Then
            bModifier_Click(sender, e)
        End If
        If argument = "121" And bAnnuler.Enabled = True Then
            bAnnuler_Click(sender, e)
        End If
        If argument = "122" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        '--------------------- boutton close 
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub Init()

        'Initialiser les controles
        initLoadControl()

        'pour initialiser le type de commande
        initTypeCommande()

        'chargement des Fournisseurs
        initFournisseur()

        'Appel Pour selectionner le dernier ligne 
        NumeroligneCommande = selectionDernierLigneCommand()

        'chargement des Entêtes des Commandes        
        initCommandeEntete()

        'chargement des détails des Commandes 
        initCommandeDetail()

        'Appel pour charger les information de Commande en question
        ChargerCommande(NumeroligneCommande)

        'initialisation la grid
        initgArticle()

        'initialisation de la datatable article qui est utilisé dans la liste de 
        initAticle()

        'affichage du nombre des commandes en instance 
        NombredeCommandeEnInstance()

        'pour initialiser les btns
        initBoutons()

    End Sub




    'pour initialiser le type de commande
    Private Sub initTypeCommande()
        With cmbType
            .HoldFields()
            .AddItem("JOURNALIERE")
            .AddItem("GROUPEE")
            .ColumnHeaders = False
        End With
    End Sub

    'Initialiser les controles
    Private Sub initLoadControl()
        'mode en consultation

        mode = "Consultation"

        lTotHTAchat.Text = "0.000"
        lTotalTTCAchat.Text = "0.000"
        lTotalTVA.Text = "0.000"

        bConfirmer.Enabled = False

        bAnnuler.Enabled = False
        bFournisseur.Enabled = False
        bAjouter.Enabled = True
        bInstance.Enabled = False

        bModifier.Enabled = True
        bFirst.Enabled = True
        bPrevious.Enabled = True
        bNext.Enabled = True
        bLast.Enabled = True
        GroupeFournisseur.Enabled = False
        bExportExcel.Enabled = True

        MasqauerZoneStatiqtique()

    End Sub
    '----------------------------- chargement des Entêtes des Commandes
    Private Sub ChargerCommande(ByVal pNumeroLigneCommande As String)

        'clear les ds
        If (dsCommande.Tables.IndexOf("COMMANDE_DETAILS") > -1) Then
            dsCommande.Tables("COMMANDE_DETAILS").Clear()
        End If
        If (dsCommande.Tables.IndexOf("COMMANDE") > -1) Then
            dsCommande.Tables("COMMANDE").Clear()
        End If

        ''----------------------------- chargement des Entetes des Commandes 
        Try

            StrSQL = " SELECT * FROM (  " + _
            " SELECT *, ROW_NUMBER() OVER (ORDER BY NumeroCommande) as row FROM COMMANDE " + _
            "              ) a WHERE row > " & pNumeroLigneCommande - 1 & " AND  row <= " & pNumeroLigneCommande

            cmdEnteteCommande.Connection = ConnectionServeur
            cmdEnteteCommande.CommandText = StrSQL
            daEnteteCommande = New SqlDataAdapter(cmdEnteteCommande)
            daEnteteCommande.Fill(dsCommande, "COMMANDE")
            cbEnteteCommande = New SqlCommandBuilder(daEnteteCommande)

        Catch ex As Exception
            WriteLine(ex.Message)
        End Try

        'Lire le numéro Commande
        If dsCommande.Tables("COMMANDE").Rows.Count > 0 Then

            NumeroCommande = dsCommande.Tables("COMMANDE").Rows(0).Item("NumeroCommande")

        Else

            NumeroCommande = "0"

        End If

        initBoutons()

        ''------chargement des détails des Commandes 
        Try


            StrSQL = "SELECT NumeroCommande," + _
                     "CodeArticle," + _
                     "CodeABarre," + _
                     "Designation," + _
                     "'' AS LibelleForme," + _
                     "Qte," + _
                     "Stock," + _
                     "DatePeremption," + _
                     "PrixAchatHT," + _
                     "TVA," + _
                     "TotalTTCAchat," + _
                     "EnCours," + _
                     "StockAlerte," + _
                     "QteACommander," + _
                     "QteUnitaire," + _
                     "CONVERT(bit, 0) AS RUPTURE," + _
                       "'' As Vide  " + _
                     "FROM COMMANDE_DETAILS " + _
                     "WHERE  NumeroCommande =" + Quote(NumeroCommande) + " ORDER BY Ordre "

            cmdDetailCommande.Connection = ConnectionServeur
            cmdDetailCommande.CommandText = StrSQL
            daDetailCommande = New SqlDataAdapter(cmdDetailCommande)
            daDetailCommande.Fill(dsCommande, "COMMANDE_DETAILS")
            cbDetailCommande = New SqlCommandBuilder(daDetailCommande)
        Catch ex As Exception
            WriteLine(ex.Message)
        End Try

        initgArticle()

        ' Affichage ds informations de Commande

        'Si le  mode est consultation

        If mode = "Modif" Or mode = "Consultation" Then
            If dsCommande.Tables("COMMANDE").Rows.Count > 0 Then

                DataRowRecherche = dsCommande.Tables("COMMANDE").Select("NumeroCommande=" + Quote(NumeroCommande))(0)

                NumeroCommande = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1).Item("NumeroCommande")
                lNumeroCommande.Text = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("NumeroCommande")
                lDateCommande.Text = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("Date")
                cmbFournisseur.SelectedValue = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("CodeFournisseur")

                Try
                    tNumeroBlFact.Value = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("NumeroFacture")
                Catch ex As Exception
                    tNumeroBlFact.Value = ""
                End Try

                TotalTTCCommande = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("TotalTTC")
                TotalHTCommande = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("TotalHT")
                lTotalTTCAchat.Text = Math.Round(dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("TotalTTC"), 3)
                lTotHTAchat.Text = TotalHTCommande.ToString
                lTotalTVA.Text = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("TotalTVA")

                cmbType.Text = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("TypeCommande")

                lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("CodePersonnel"))

                NumeroCommande = DataRowRecherche.Item("NumeroCommande")

            End If
        End If

        If mode = "Consultation" Then
            cmbFournisseur.Enabled = False
            bInstance.Enabled = False

        Else
            cmbFournisseur.Enabled = True
            bInstance.Enabled = True

        End If

    End Sub

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click

        Dim StrSQL As String = ""
        Dim i As Integer
        Dim Cond As String = ""
        Dim NumeroCommande As String = ""
        Dim QteDejaCommande As Integer = 0

        mode = "Ajout"

        'ChargerCommande: Pour Récuperer la 
        'structure des DS Commande et Commande detail
        'La valeur 0 est inexistant
        ChargerCommande("0")

        ' initialisation des variables globaux 
        initControl()

        Dim MyCritereDeSelectionCommande As New fCritereDeSelectionCommande
        MyCritereDeSelectionCommande.FenetreAppelante = "SimpleCommande"
        MyCritereDeSelectionCommande.ShowDialog()

        TypeCommande = fCritereDeSelectionCommande.TypeCommande
        NombreDeJour = fCritereDeSelectionCommande.NombreDeJour
        SansManquantDepuis = fCritereDeSelectionCommande.SansManquantDepuis
        Reference = fCritereDeSelectionCommande.Reference
        mois = fCritereDeSelectionCommande.mois
        DebutPeriode = fCritereDeSelectionCommande.DebutPeriode
        FinPeriode = fCritereDeSelectionCommande.FinPeriode
        Section = fCritereDeSelectionCommande.Section
        DebutIntervalle = fCritereDeSelectionCommande.DebutIntervalle
        FinIntervalle = fCritereDeSelectionCommande.FinIntervalle
        Forme = fCritereDeSelectionCommande.Forme
        Categorie = fCritereDeSelectionCommande.Categorie
        Labo = fCritereDeSelectionCommande.Labo
        Rayon = fCritereDeSelectionCommande.Rayon
        RayonSelectionne = fCritereDeSelectionCommande.RayonSelectionne
        Trie = fCritereDeSelectionCommande.Trie
        CommandeEnCours = fCritereDeSelectionCommande.CommandeEnCours
        TenirCompteStockAlerte = fCritereDeSelectionCommande.TenirCompteStockAlerte
        TenirCompteStock = fCritereDeSelectionCommande.TenirCompteStock
        TenirCompteStockAlerteCJ = fCritereDeSelectionCommande.TenirCompteStockAlerteCJ

        StatistiqueAnneePrecidente = fCritereDeSelectionCommande.StatistiqueAnneePrecidente

        MyCritereDeSelectionCommande.Dispose()
        MyCritereDeSelectionCommande.Close()

        NumeroCommande = RecupereNumero()

        If SansManquantDepuis <> "" Then
            bListeDesManquants.Enabled = True
        Else
            bListeDesManquants.Enabled = False
        End If

        'ajout d'un nouvel enregistrement vide dans les datatables convenables
        NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
        NouvelArticle("Designation") = "" 'initialisation pour que le calcul des montants ne compare pas 
        NouvelArticle("CodeArticle") = "" ' des chaines de caractères avec des NULL
        'NouvelArticle("Qte") = 0
        dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)

        NouvelleCommande = dsCommande.Tables("COMMANDE").NewRow()
        dsCommande.Tables("COMMANDE").Rows.Add(NouvelleCommande)

        '***************************************************************************************************
        '************************************ Cas de HIT PARADE ********************************************
        '***************************************************************************************************

        If TypeCommande = "HIT PARADE" Then
            CommandeHitParade(TenirCompteStockAlerte, Trie, TenirCompteStock)
        End If

        '***************************************************************************************************
        '************************************ Cas de Journalière *******************************************
        '***************************************************************************************************

        If TypeCommande = "JOURNALIERE" Then
            CommandeJournalier(Trie)
        End If

        '***************************************************************************************************
        '************************************ Cas de Groupée ********************************************
        '***************************************************************************************************

        If TypeCommande = "GROUPEE" Then
            CommandeGroupee(Trie)
        End If

        '***************************************************************************************************
        '************************************ Cas de Groupée ********************************************
        '***************************************************************************************************

        If TypeCommande = "COMMANDE_INSTANTANEE" Then
            CommandeInstantanee(Trie)
            Try
                cmdCommande.Connection = ConnectionServeur
                cmdCommande.CommandText = "DELETE FROM COMMANDE_INSTANTANEE"
                cmdCommande.ExecuteNonQuery()
            Catch
            End Try
        End If

        ' liaison avec la gride pour l affichage
        initgArticle()

        'Me.gArticles.Splits(0).DisplayColumns(0).AllowFocus = False
        'Me.gArticles.Splits(0).DisplayColumns(4).AllowFocus = False
        'Me.gArticles.Splits(0).DisplayColumns(6).AllowFocus = False
        'Me.gArticles.Splits(0).DisplayColumns(7).AllowFocus = False
        'Me.gArticles.Splits(0).DisplayColumns(8).AllowFocus = False
        'Me.gArticles.Splits(0).DisplayColumns(9).AllowFocus = False
        'Me.gArticles.Splits(0).DisplayColumns(10).AllowFocus = False
        'Me.gArticles.Splits(0).DisplayColumns(11).AllowFocus = False
        'Me.gArticles.Splits(0).DisplayColumns(12).AllowFocus = False
        'Me.gArticles.Splits(0).DisplayColumns(13).AllowFocus = False
        'Me.gArticles.Splits(0).DisplayColumns(14).AllowFocus = False
        'Me.gArticles.Splits(0).DisplayColumns(15).AllowFocus = False

        If TypeCommande = "FRIGO" Then
            bInstance_Click(sender, e)
            Exit Sub
        End If

        '---------------------------------------- élemination des lignes vide ou qte =0 
        If TypeCommande <> "RIEN" And TypeCommande <> "FRIGO" Then

            i = 0
            Do While i < dsCommande.Tables("COMMANDE_DETAILS").Rows.Count
                If dsCommande.Tables("COMMANDE_DETAILS").Rows(i).Item("CodeArticle").ToString = "" Or CDbl(Val(dsCommande.Tables("COMMANDE_DETAILS").Rows(i).Item("Qte").ToString)) <= 0 Then
                    dsCommande.Tables("COMMANDE_DETAILS").Rows(i).Delete()
                    dsCommande.Tables("COMMANDE_DETAILS").AcceptChanges()

                    i = 0
                Else
                    i = i + 1
                End If
            Loop

            ' cas ou l'utilisateur a choisi un type de commande 
            NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
            NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
            NouvelArticle("CodeArticle") = ""
            dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)

        End If

        bListeDesManquants.Visible = True

        lNombreDesArticles.Text = (dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1).ToString + " articles"
        lNombreDesArticles.Visible = True

        AfficherZoneStatiqtique()
        AfficherStatistique(gArticles(gArticles.Row, "CodeArticle"))

        CalculerMontants()

        gArticles.Focus()
        gArticles.Col = 2
        gArticles.Row = gArticles.RowCount - 1
        gArticles.EditActive = True

        '****************************************************************************************
        '********************** verouillage des lignes déja confirmées **************************
        '****************************************************************************************
        If gArticles.Row <> dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 Then
            gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        ElseIf gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 Then
            If gArticles(gArticles.Row, "CodeArticle") = "" Then
                gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = False
            End If
            If gArticles(gArticles.Row, "Designation") = "" Then
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
            End If
        End If

        '##############################################################################
        For i = 0 To dsCommande.Tables("COMMANDE_DETAILS").Columns.Count - 1
            Me.gArticles.Splits(0).DisplayColumns(i).AllowFocus = False
        Next


        With gArticles

            '.Splits(0).DisplayColumns("CodeArticle").Locked = False
            .Splits(0).DisplayColumns("Designation").Locked = False
            .Splits(0).DisplayColumns("Qte").Locked = False
            .Splits(0).DisplayColumns("DatePeremption").Locked = False
            .Splits(0).DisplayColumns("CodeABarre").Locked = False
            .Splits(0).DisplayColumns("QteACommander").Locked = False
            .Splits(0).DisplayColumns("StockAlerte").Locked = False
            .Splits(0).DisplayColumns("RUPTURE").Locked = False

            '.Splits(0).DisplayColumns("CodeArticle").AllowFocus = True
            .Splits(0).DisplayColumns("Designation").AllowFocus = True
            .Splits(0).DisplayColumns("Qte").AllowFocus = True
            .Splits(0).DisplayColumns("DatePeremption").AllowFocus = True
            .Splits(0).DisplayColumns("CodeABarre").AllowFocus = True
            .Splits(0).DisplayColumns("CodeABarre").AllowFocus = True
            .Splits(0).DisplayColumns("QteACommander").AllowFocus = True
            .Splits(0).DisplayColumns("RUPTURE").AllowFocus = True
            .Splits(0).DisplayColumns("StockAlerte").AllowFocus = True

        End With

    End Sub

    Private Sub gArticles_BeforeColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColEditEventArgs) Handles gArticles.BeforeColEdit
        If e.Column.Name = "Date de péremption" Then
            Dim det As New C1.Win.C1Input.C1DateEdit
            det.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
            gArticles.Columns("DatePeremption").Editor = det
        End If
    End Sub

    Private Sub gArticles_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gArticles.Change
        Dim cmd As New SqlCommand
        If gArticles.Col = 15 Then
            Try
                cmd.Connection = ConnectionServeur
                cmd.CommandText = "UPDATE ARTICLE SET CodeSituation = " & Quote(IIf(gArticles.Columns("RUPTURE").Value.ToString() = "False", "1", "2")) & " WHERE CodeArticle = " & Quote(gArticles.Columns("CodeArticle").Value.ToString())
                cmd.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
        End If

        If (gArticles.Col = 3 And gArticles.Columns("Designation").Value <> "") Or gArticles.Col = 2 Then
            Dim StrSQL1 As String = ""
            Dim I As Integer
            Dim NbLigne As Integer
            NbLigne = gArticles.RowCount

            ''With gListeRecherche
            ''    .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
            ''    .Top = Me.gArticles.Top + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight

            ''End With
            'If gArticles.Row <= 8 Then
            '    gListeRecherche.Location = New Point(109, ((gArticles.Row) * 17) + 161)
            '    gListeRecherche.Visible = True
            'Else
            '    'gListeRecherche.Location = New Point(109, 101)
            '    gListeRecherche.Location = New Point(109, 170)
            '    gListeRecherche.Visible = True
            'End If

            With gListeRecherche
                If gArticles.Row <= 8 Then
                    .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
                    .Top = Me.gArticles.Top + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight + 10
                Else
                    .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
                    .Top = Me.gArticles.Top
                End If
            End With





            Try
                dsCommande.Tables("ARTICLE").Clear()
            Catch ex As Exception

            End Try
            If gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 And gArticles.Col = 3 Then
                gListeRecherche.Visible = True
            Else
                gListeRecherche.Visible = False
            End If

            'chargement des articles qui sont mis en jeu
            If gArticles.Col = 3 Then
                If gArticles.Columns("Designation").Value.ToString.Length > 1 Then
                    If gArticles.Columns("Designation").Value.ToString.IndexOf(".") > 0 And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1)) Then

                        StrSQL1 = "SELECT ARTICLE.CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC, " + _
                                  "PrixAchatTTC, " + _
                                  "StockArticle.stock " + _
                                  "FROM ARTICLE " + _
                                  "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  "INNER JOIN (SELECT CodeArticle, SUM(QteLotArticle) AS Stock FROM LOT_ARTICLE GROUP BY CodeArticle) StockArticle ON ARTICLE.CodeArticle=StockArticle.CodeArticle " + _
                                  " WHERE " + _
                                  "ltrim(str(PrixVenteTTC,10,3)) LIKE '" + _
                                  gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + "%' AND Supprime=0 ORDER BY PrixVenteTTC"
                    Else

                        StrSQL1 = "SELECT ARTICLE.CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC, " + _
                                  "PrixAchatTTC, " + _
                                  "StockArticle.stock " + _
                                  "FROM ARTICLE " + _
                                  "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  "INNER JOIN (SELECT CodeArticle, SUM(QteLotArticle) AS Stock FROM LOT_ARTICLE GROUP BY CodeArticle) StockArticle ON ARTICLE.CodeArticle=StockArticle.CodeArticle " + _
                                  " WHERE " + _
                                  " Designation LIKE '" + _
                                  gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + "%' AND Supprime=0 ORDER BY Designation"
                    End If
                Else

                    StrSQL1 = "SELECT ARTICLE.CodeArticle," + _
                                "Designation," + _
                                "LibelleForme," + _
                                "PrixVenteTTC, " + _
                                "PrixAchatTTC, " + _
                                "StockArticle.stock " + _
                                "FROM ARTICLE " + _
                                "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                "INNER JOIN (SELECT CodeArticle, SUM(QteLotArticle) AS Stock FROM LOT_ARTICLE GROUP BY CodeArticle) StockArticle ON ARTICLE.CodeArticle=StockArticle.CodeArticle " + _
                                "WHERE  " + _
                                "Designation LIKE '" + gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + _
                                "%' AND Supprime=0 ORDER BY Designation"
                End If
            ElseIf gArticles.Col = 2 Then

                StrSQL1 = "SELECT ARTICLE.CodeArticle," + _
                            "Designation," + _
                            "LibelleForme," + _
                            "PrixVenteTTC, " + _
                            "PrixAchatTTC, " + _
                            "StockArticle.stock " + _
                            "FROM ARTICLE " + _
                            "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                            "INNER JOIN (SELECT CodeArticle, SUM(QteLotArticle) AS Stock FROM LOT_ARTICLE GROUP BY CodeArticle) StockArticle ON ARTICLE.CodeArticle=StockArticle.CodeArticle " + _
                            "WHERE " + _
                            " CodeABarre LIKE '" + gArticles.Columns("CodeArticle").Value + _
                            "' AND Supprime=0 ORDER BY Designation"

            End If
            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL1
            daCommande = New SqlDataAdapter(cmdCommande)
            daCommande.Fill(dsCommande, "ARTICLE")

            If dsCommande.Tables("ARTICLE").Rows.Count > 0 Then
                dr = dsCommande.Tables("ARTICLE").Rows(0)
            End If

            With gListeRecherche
                .Columns.Clear()
                .DataSource = dsCommande
                .DataMember = "ARTICLE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code Article"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("PrixVenteTTC").Caption = "Prix de vente"

                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' CentreR tous les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Visible = False
                Next

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("CodeArticle").Visible = True
                .Splits(0).DisplayColumns("Designation").Visible = True
                .Splits(0).DisplayColumns("LibelleForme").Visible = True
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True
                .Splits(0).DisplayColumns("PrixAchatTTC").Visible = True
                .Splits(0).DisplayColumns("stock").Visible = True

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("Designation").Width = 300
                .Splits(0).DisplayColumns("LibelleForme").Width = 100
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 80
                .Splits(0).DisplayColumns("PrixAchatTTC").Width = 80
                .Splits(0).DisplayColumns("stock").Width = 80

                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.Aqua
                .Splits(0).DisplayColumns("LibelleForme").Style.BackColor = Color.Aqua
                .Splits(0).DisplayColumns("PrixVenteTTC").Style.BackColor = Color.Aqua
                .Splits(0).DisplayColumns("PrixAchatTTC").Style.BackColor = Color.Aqua
                Try
                    .Splits(0).DisplayColumns("CodeForme").Visible = False
                    .Splits(0).DisplayColumns("Ordre").Visible = False
                Catch ex As Exception
                End Try

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 20
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
            End With
        End If
    End Sub
    Public Function RecupereNumero()

        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max([NumeroCommande]) FROM [COMMANDE] WHERE SUBSTRING (NumeroCommande,0,5)=YEAR(getdate())"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numuutero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If
        Return ValeurRetour
    End Function
    Public Function CalculerEnCours(ByVal CodeArticle)

        Dim QteEnCours As Integer = 0

        StrSQL = " SELECT SUM(Qte) FROM COMMANDE_DETAILS,COMMANDE WHERE  CodeArticle ='" + _
                 CodeArticle + "' AND COMMANDE .NumeroCommande =COMMANDE_DETAILS .NumeroCommande " + _
                 "AND (COMMANDE .NumeroFacture is null OR COMMANDE .NumeroFacture = '')"

        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            QteEnCours = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return QteEnCours
    End Function

    Private Sub gArticles_DoubleClick(sender As Object, e As System.EventArgs) Handles gArticles.DoubleClick
        If (Not String.IsNullOrEmpty(gArticles.Columns("CodeArticle").Value)) Then
            AfficherFicheArticle(gArticles.Columns("CodeArticle").Value, gArticles.Columns("Stock").Value, gArticles.Columns("Designation").Value)
        End If
    End Sub

    Private Sub gArticles_FetchRowStyle(sender As Object, e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gArticles.FetchRowStyle
        If e.Row = gArticles.Row Then
            e.CellStyle.BackColor = System.Drawing.Color.Aqua
        End If
    End Sub

    Private Sub gArticles_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyDown
        Dim cmd As New SqlCommand
        If (gArticles.Col = 12 Or gArticles.Col = 13) And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) Then
            If (gArticles.Columns("StockAlerte").Value.ToString <> "") Then
                If (IsNumeric(gArticles.Columns("StockAlerte").Value.ToString)) Then
                    If (gArticles.Columns("StockAlerte").Value >= 0) Then
                        'gArticles.Col = 13
                        'gArticles.EditActive = True


                        If gArticles.Col = 12 Then
                            Try
                                cmd.Connection = ConnectionServeur
                                cmd.CommandText = "UPDATE ARTICLE SET StockAlerte = " & Quote(gArticles.Columns("StockAlerte").Value.ToString()) & " WHERE CodeArticle = " & Quote(gArticles.Columns("CodeArticle").Value.ToString())
                                cmd.ExecuteNonQuery()
                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try
                        End If

                        If gArticles.Col = 13 Then
                            Try
                                cmd.Connection = ConnectionServeur
                                cmd.CommandText = "UPDATE ARTICLE SET QteACommander = " & Quote(gArticles.Columns("QteACommander").Value.ToString()) & " WHERE CodeArticle = " & Quote(gArticles.Columns("CodeArticle").Value.ToString())
                                cmd.ExecuteNonQuery()
                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try
                        End If

                        Exit Sub
                    Else
                        gArticles.Columns("StockAlerte").Value = ""
                        'gArticles.Col = 12
                        'gArticles.EditActive = True
                        Exit Sub
                    End If
                Else
                    gArticles.Columns("StockAlerte").Value = ""
                    'gArticles.Col = 12
                    'gArticles.EditActive = True
                    Exit Sub
                End If
            Else
                gArticles.Columns("StockAlerte").Value = ""
                gArticles.Col = 12
                gArticles.EditActive = True
                Exit Sub
            End If
        End If
    End Sub

    Private Sub gArticles_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyUp


        Dim i As Integer = 0
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        ' Dim TestNumeroLot As Integer
        Dim QuantiteAAjouter As Integer = 0
        Dim CodeArticleExisteDansLaListe As String = ""



        '****************************************************************************************
        '*********************** test si on est en mode saisi ou non* ***************************
        '****************************************************************************************
        'gArticles.EditActive = True    StockAlerte
        'gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
        'gArticles.Splits(0).DisplayColumns("Designation").Locked = False


        If gArticles.Col = 13 And e.KeyCode = Keys.Enter Then
            If (gArticles.Columns("QteACommander").Value.ToString <> "") Then
                If (IsNumeric(gArticles.Columns("QteACommander").Value.ToString)) Then
                    If (gArticles.Columns("QteACommander").Value >= 0) Then
                        gArticles.Col = 1
                        gArticles.EditActive = True

                        Try
                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = "UPDATE ARTICLE SET QteACommander = " & Quote(gArticles.Columns("QteACommander").Value.ToString()) & " WHERE CodeArticle = " & Quote(gArticles.Columns("CodeArticle").Value.ToString())
                            cmd.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try

                        gArticles.Row = gArticles.Row + 1

                        Exit Sub
                    Else
                        gArticles.Columns("QteACommander").Value = ""
                        gArticles.Col = 13
                        gArticles.EditActive = True
                        Exit Sub
                    End If
                Else
                    gArticles.Columns("QteACommander").Value = ""
                    gArticles.Col = 13
                    gArticles.EditActive = True
                    Exit Sub
                End If
            Else
                gArticles.Columns("QteACommander").Value = ""
                gArticles.Col = 13
                gArticles.EditActive = True
                Exit Sub
            End If
        End If

        If mode <> "Ajout" And mode <> "Modif" Then
            Exit Sub
        End If

        '-------------------- si f1 c'est afficher la fiche article ou la fenêtre de recherche multicritère
        If e.KeyCode = Keys.F1 And mode = "Ajout" And gArticles.Columns("CodeArticle").Value <> "" Then
            AfficherFicheArticle(gArticles.Columns("CodeArticle").Value, gArticles.Columns("Stock").Value, gArticles.Columns("Designation").Value)
            Exit Sub
        End If

        If e.KeyCode = Keys.F1 And mode = "Ajout" And gArticles.Columns("CodeArticle").Value = "" Then
            Dim RechercheMulticritere As New fRechercheArticleMultiCritere

            RechercheMulticritere.ShowDialog()

            CodeArticleRechercheMC = RechercheMulticritere.CodeArticleRecherche

            gArticles.MoveLast()
            gArticles.Col = 2
            gArticles.Columns("CodeArticle").Value = CodeArticleRechercheMC
            ChargerDetailArticle(CodeArticleRechercheMC)
            gArticles.Col = 3

            RechercheMulticritere.Close()
            RechercheMulticritere.Dispose()

        End If

        '***************************************************************************************
        '**************** parcourir par les flèches pour voir les statistiques des ventes ******
        '***************************************************************************************

        If gListeRecherche.Visible = False And (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down) Then
            AfficherStatistique(gArticles(gArticles.Row, "CodeArticle"))
            'Exit Sub
        End If

        '****************************************************************************************
        '****** suppression du dernier ligne vide si on a deux lignes au mm temps vide **********
        '****************************** cas ou on supprime dernier ligne ************************
        '****************************************************************************************

        If dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 = 1 And gArticles(0, "CodeArticle") = "" And gArticles(1, "CodeArticle") = "" Then
            gArticles.MoveLast()
            gArticles.MovePrevious()
            gArticles.Delete()
        End If

        '****************************************************************************************
        '******* test du type de la valeur d'entrée dans la colonne quantité (numéric) **********
        '**************** test du  valeur d'entrée dans la colonne quantité < 99999 *************
        '****************************************************************************************

        If gArticles.Col = 5 Then
            If e.KeyCode = Keys.D6 Or e.KeyCode = Keys.Subtract Then
                gArticles.Columns("Qte").Value = ""
                gArticles.Col = 5
                gArticles.EditActive = True
                Exit Sub
            End If

            If gArticles.Columns("Qte").Value.ToString <> "-" Then   ' pour les quantité négatives
                If IsNumeric(gArticles.Columns("Qte").Value) = False Then
                    gArticles.Columns("Qte").Value = ""
                    gArticles.Col = 5
                    gArticles.EditActive = True
                    Exit Sub
                End If
                If (gArticles.Columns("Qte").Value = 0) And e.KeyCode = Keys.Enter Then
                    gArticles.Columns("Qte").Value = ""
                    gArticles.Col = 5
                    gArticles.EditActive = True
                    Exit Sub
                End If
                If (gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999) And e.KeyCode = Keys.Enter Then
                    gArticles.Columns("Qte").Value = "1"
                    MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Col = 5
                    gArticles.EditActive = True
                    Exit Sub
                End If
            Else
                gArticles.Columns("Qte").Value = ""
                gArticles.Col = 5
                gArticles.EditActive = True
            End If

        End If

        '****************************************************************************************
        '************************************** recherche par code***** *************************
        '****************************************************************************************

        If gArticles.Col = 2 And e.KeyCode = Keys.Enter And gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 Then
            ChargerDetailArticle(gArticles.Columns("CodeABarre").Value.ToString)
            AfficherStatistique(gArticles(gArticles.Row, "CodeArticle"))
            Exit Sub
        ElseIf gArticles.Col = 2 And e.KeyCode = Keys.Enter And gArticles.Row < dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 Then
            gArticles.Col = 3
        End If

        '****************************************************************************************
        '********* masquer la liste de recherche si la designation est vide *********************
        '****************************************************************************************

        If e.KeyCode = Keys.Back And gArticles.Columns("Designation").Value = "" And gArticles.Col = 3 Then
            gListeRecherche.Visible = False
        End If

        '****************************************************************************************
        '********* pour passer à la navigation dans la petite liste de recherhce ****************
        '****************************************************************************************

        If (e.KeyCode = Keys.Down Or e.KeyCode = Keys.Up) And gListeRecherche.Visible = True Then
            gListeRecherche.Focus()
            gListeRecherche.Col = 1
            gListeRecherche.Row = 1
        End If

        '****************************************************************************************
        '******* si l'artcile n'existe pas dans la liste de recherhce sa quantité doit etre 0 ***
        '****************************************************************************************

        If dsCommande.Tables("ARTICLE").Rows.Count <= 0 And gListeRecherche.Visible = True Then
            gArticles.Columns("Qte").Value = 0
            gArticles.Col = 3
        End If

        '****************************************************************************************
        '*********************************** calcul des montants ********************************
        '****************************************************************************************

        If (gArticles.Col = 5) And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) Then
            CalculerMontants()
        End If

        '****************************************************************************************
        '********************** verouillage des lignes déja confirmées **************************
        '****************************************************************************************
        If (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) And gArticles.Row <> dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 Then
            gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        ElseIf gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 Then
            If gArticles(gArticles.Row, "CodeArticle") = "" Then
                gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = False
                gArticles.Splits(0).DisplayColumns("QteACommander").Locked = False
                gArticles.Splits(0).DisplayColumns("StockAlerte").Locked = False
                gArticles.Splits(0).DisplayColumns("RUPTURE").Locked = False
            End If
            If gArticles(gArticles.Row, "Designation") = "" Then
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
                gArticles.Splits(0).DisplayColumns("QteACommander").Locked = False
                gArticles.Splits(0).DisplayColumns("StockAlerte").Locked = False
                gArticles.Splits(0).DisplayColumns("RUPTURE").Locked = False
            End If
        End If

        If gArticles.Col = 2 And gArticles.Columns("Designation").Value <> "" Then
            gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        End If
        If gArticles.Col = 3 And gArticles.Columns("CodeArticle").Value <> "" Then
            gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        End If



        '****************************************************************************************
        '*************** traitement du clique sur le bouton ENTREE selon la colonne *************
        '****************************************************************************************

        If e.KeyCode = Keys.Enter Then 'And (dsCommande.Tables("ARTICLE").Rows.Count > 0 Or gArticles.Row <> dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1) Then

            gListeRecherche_KeyUp(sender, e)     ' pour charger les informations de l'article a partir du fiche article
            AfficherStatistique(gArticles(gArticles.Row, "CodeArticle"))

            If gArticles.Col = 3 Then
                If gArticles.Columns("CodeArticle").Value = "" Then    ' si l'article choisit n'existe pas dans la liste de recherche
                    gArticles.Col = 3
                    gArticles.Columns("Designation").Value = ""
                Else
                    gArticles.Col = 5
                End If

            ElseIf gArticles.Col = 5 And e.KeyCode = Keys.Enter Then
                ' si l'utilisateur a choisit un article on ajoute un nouvel enregistrement dans la datatable puis on passe à la ligne
                ' suivant dans la gride avec un test si cet article est déja choisi ou non si c'est le cas on ajoute seulement la quntité

                If gArticles.Columns("Designation").Value <> "" Then
                    QuantiteAAjouter = gArticles.Columns("Qte").Value
                    CodeArticleExisteDansLaListe = gArticles.Columns("CodeArticle").Value
                    If gArticles.RowCount >= 1 And gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 Then
                        Do While i < gArticles.RowCount - 1
                            If gArticles(i, "CodeArticle") = CodeArticleExisteDansLaListe Then 'dr.Item("CodeArticle") Then
                                If MsgBox("Article déja saisi, voulez vous ajouter la quantité ou effacer l'article ? Oui pour ajouter la quantité", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                                    gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QuantiteAAjouter).ToString
                                    gArticles.MoveLast()
                                    gArticles.Delete()
                                Else
                                    gArticles.MoveLast()
                                    gArticles.Delete()
                                End If
                            End If
                            i = i + 1
                        Loop
                    End If
                End If
                If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                    NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
                    NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                    NouvelArticle("CodeArticle") = ""
                    dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)
                End If
                'gArticles.MoveLast()
                gArticles.Row = gArticles.Row + 1
                'gArticles.FirstRow = gArticles.RowCount - 1

                Try
                    dsCommande.Tables("ARTICLE").Clear()
                Catch ex As Exception
                End Try
                gArticles.Col = 2

            End If
        End If
        lNombreDesArticles.Text = (dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1).ToString + " articles"
        lNombreDesArticles.Visible = True

        If e.KeyCode = Keys.F4 Then
            gArticles.Splits(0).DisplayColumns("QteACommander").Locked = False
            gArticles.Splits(0).DisplayColumns("StockAlerte").Locked = False
            gArticles.Splits(0).DisplayColumns("RUPTURE").Locked = False
            gArticles.Col = 12
            gArticles.EditActive = True
            Exit Sub
        End If

        Try
            'dsCommande.Tables("COMMANDE_DETAILS").AcceptChanges()
        Catch
        End Try
    End Sub

    Private Sub gListeRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListeRecherche.KeyUp
        ' dans cet évènenment on teste si l évènenment est due à un clique sur le boutton entrée et si l'utilisateur a choisi 
        ' un article et si le curseur est dans la colonne de designation 

        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date

        Dim CategorieArticle As Integer = 0

        If gListeRecherche.Visible = False Then
            Exit Sub
        End If
        If e.KeyCode = Keys.Back Then
            gArticles.Focus()
            gArticles.Col = 3
            gArticles.MoveLast()
            gArticles.EditActive = True
        End If

        Dim j As Integer
        Dim NumeroLigne As Integer
        Dim DataRowRecherche As DataRow
        If e.KeyCode = Keys.Enter And (gArticles.Col = 5 Or gArticles.Col = 3) Then    'And gArticles.Columns("Designation").Value <> ""
            If dsCommande.Tables("ARTICLE").Rows.Count > 0 Then

                '--------------------- rehcerche de l'index de la ligne qui contient l'article recherché ---------------

                For j = 0 To dsCommande.Tables("ARTICLE").Rows.Count - 1
                    DataRowRecherche = dsCommande.Tables("ARTICLE").Rows(j)
                    If DataRowRecherche.Item("CodeArticle") = gListeRecherche.Columns("CodeArticle").Value Then
                        NumeroLigne = j
                    End If
                Next

                '------------------- chargement des données ---------------------------------------------- 

                dr = dsCommande.Tables("ARTICLE").Rows(NumeroLigne)
                If mode = "Modif" Then
                    NouvelArticle("NumeroCommande") = NumeroCommande
                Else
                    NouvelArticle("NumeroCommande") = RecupereNumero()
                End If

                '---------------------- les préparations ne sont pas autorisées
                CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                If CategorieArticle = 9 Then
                    MsgBox("Saisie des préparations interdite !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Columns("CodeArticle").Value = ""
                    gArticles.Columns("CodeABarre").Value = ""
                    gArticles.Columns("Designation").Value = ""
                    gArticles.Col = 1
                    gArticles.EditActive = True
                    Exit Sub
                End If

                NouvelArticle("CodeArticle") = dr.Item("CodeArticle")
                NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                Try
                    'NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    'NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
                Catch ex As Exception
                End Try

                NouvelArticle("Qte") = 1
                NouvelArticle("Stock") = CalculeStock(dr.Item("CodeArticle"))
                NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
                NouvelArticle("StockAlerte") = CDbl(Val(RecupererValeurExecuteScalaire("StockAlerte", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))))
                'NouvelArticle("EnCours") = RecupererValeurExecuteScalaire("StockAlerte", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("QteACommander") = RecupererValeurExecuteScalaire("QteACommander", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("QteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))

                '------------------ récupération de la date de péremption

                StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
                System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
                dr.Item("CodeArticle") + "' AND QteLotArticle>0 Order by DatePeremptionArticle DESC "

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    DatePeremption = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If DatePeremption = #12:00:00 AM# Then
                    'NouvelArticle("DatePeremption") = "01/01/1900"
                Else
                    NouvelArticle("DatePeremption") = DatePeremption
                End If

                gArticles.Refresh()
            End If
            gListeRecherche.Visible = False
            gArticles.Focus()
            If NumeroLigne = 0 Then
                gArticles.Col = 3
            Else
                gArticles.Col = 5
            End If

            ''''''''''
            CalculerMontants()
            ''''''''''
        End If
    End Sub

    Public Sub ChargerDetailArticle(ByVal CodeABarre As String)
        Dim resultat As String = ""
        Dim Supprime As String = ""
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date
        Dim CodeArticle As String = ""

        Dim CategorieArticle As Integer = 0

        If CodeABarre = "" Then
            gArticles.Columns("CodeArticle").Value = ""
            gArticles.Columns("CodeABarre").Value = ""
            gArticles.Col = 3
            Exit Sub
        End If

        CodeArticle = RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "CodeABarre", CodeABarre)
        resultat = RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle)
        Supprime = RecupererValeurExecuteScalaire("Supprime", "ARTICLE", "CodeArticle", CodeArticle)

        If resultat <> "" And Supprime = "False" Then

            CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", CodeArticle)
            If CategorieArticle = 9 Then
                MsgBox("Saisie des préparations interdite !", MsgBoxStyle.Critical, "Erreur")
                gArticles.Columns("CodeArticle").Value = ""
                gArticles.Col = 3
                Exit Sub
            End If

            If mode = "Modif" Then
                NouvelArticle("NumeroCommande") = NumeroCommande
            Else
                NouvelArticle("NumeroCommande") = RecupereNumero()
            End If

            NouvelArticle("CodeArticle") = CodeArticle
            NouvelArticle("CodeABarre") = CodeABarre
            NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)
            Try
                'NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", CodeArticle)
                'NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", CodeArticle)
            Catch ex As Exception

            End Try

            NouvelArticle("Qte") = 1
            NouvelArticle("Stock") = CalculeStock(CodeArticle)
            NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", CodeArticle)
            NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", CodeArticle)
            NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
            NouvelArticle("StockAlerte") = RecupererValeurExecuteScalaire("StockAlerte", " ARTICLE", "CodeArticle", CodeArticle)
            'NouvelArticle("EnCours") = RecupererValeurExecuteScalaire("StockAlerte", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
            NouvelArticle("QteACommander") = RecupererValeurExecuteScalaire("QteACommander", " ARTICLE", "CodeArticle", CodeArticle)
            NouvelArticle("QteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", CodeArticle)

            '----------------------- récupération de la date de péremption

            StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
            System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
            CodeArticle + "' AND QteLotArticle>0 Order by DatePeremptionArticle DESC "

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                DatePeremption = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If DatePeremption = #12:00:00 AM# Then
                'NouvelArticle("DatePeremption") = "01/01/1900"
            Else
                NouvelArticle("DatePeremption") = DatePeremption
            End If

            gArticles.Refresh()
            gArticles.Col = 5
        Else
            gArticles.Columns("CodeABarre").Value = ""
            gArticles.Col = 3
        End If
    End Sub
    Public Sub CalculerMontants()
        Dim i As Integer = 0
        TotalTTCCommande = 0.0
        TotalHTCommande = 0.0
        TotalTVACommande = 0.0

        Do While i < gArticles.RowCount
            If gArticles(i, "Designation") <> "" And gArticles(i, "PrixAchatHT").ToString <> "" Then
                If gArticles(i, "Qte").ToString <> "" Then

                    gArticles(i, "TotalTTCAchat") = Math.Round(gArticles(i, "PrixAchatHT") * gArticles(i, "Qte") * (100 + gArticles(i, "TVA")) / 100, 3)
                    TotalHTCommande = Math.Round(TotalHTCommande + gArticles(i, "PrixAchatHT") * gArticles(i, "Qte"), 3)
                    TotalTTCCommande = TotalTTCCommande + gArticles(i, "TotalTTCAchat")
                    TotalTVACommande = TotalTVACommande + gArticles(i, "PrixAchatHT") * gArticles(i, "TVA") / 100 * gArticles(i, "Qte")

                Else
                    MsgBox("Quantité invalide !", MsgBoxStyle.Critical, "Erreur")
                End If

            End If

            i = i + 1
        Loop

        lTotHTAchat.Text = TotalHTCommande
        lTotalTTCAchat.Text = TotalTTCCommande.ToString
        lTotalTVA.Text = TotalTVACommande.ToString

    End Sub

    Private Sub bFournisseur_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFournisseur.Click
        cmbFournisseur.Focus()
    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        Dim _BusinessManagementEntities As Data.BusinessManagement.BusinessManagementEntities = New Data.BusinessManagement.BusinessManagementEntities()
        Dim _Commande As Data.BusinessManagement.COMMANDE = New Data.BusinessManagement.COMMANDE()
        Dim _CommandeDetails As Data.BusinessManagement.COMMANDE_DETAILS = New Data.BusinessManagement.COMMANDE_DETAILS()

        Dim I As Integer = 0
        Dim J As Integer = 0
        Dim cmd As New SqlCommand
        Dim NumeroLot As Integer = 0
        Dim TestNumeroLot As String = ""
        Dim NouveauNumeroLot As String = ""
        Dim QuantiteLotSansNumero As Integer = 0
        Dim QuantiteLotAInsere As Integer = 0
        Dim Ordre As Integer = 1
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        NumeroCommande = RecupereNumero()
        '------------------------------------------------------------------------------------------------------
        '------------------------------------- contrôle si la liste est vide ou nn
        '------------------------------------------------------------------------------------------------------
        If dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle") = "" Then
            MsgBox("Commande Vide !", MsgBoxStyle.Critical, "Erreur")
            If dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 < 0 Then
                bAjouter_Click(sender, e)
            End If
            '----------------------
            gArticles.Col = 2
            gArticles.EditActive = True
            Exit Sub
        End If

        If cmbFournisseur.Text = "" Then
            MsgBox("Veuillez choisir un Fournisseur !", MsgBoxStyle.Critical, "Erreur")
            cmbFournisseur.Focus()
            Exit Sub
        End If

        '------------------------------------------------------------------------------------------------------
        '------------------------------ demande du mot de passe
        '------------------------------------------------------------------------------------------------------
        Dim myMotDePasse As New fMotDePasse
        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If


        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = "SELECT TOP(0) * FROM COMMANDE_DETAILS"
        daCommande = New SqlDataAdapter(cmdCommande)
        daCommande.Fill(dsCommande, "COMMANDE_DETAILSS")

        Try
            dsCommande.Tables("COMMANDE_DETAILSS").Clear()
        Catch ex As Exception
        End Try

        I = 0
        Do While I < dsCommande.Tables("COMMANDE_DETAILS").Rows.Count
            Try
                If dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle") <> "" Then
                    If (Not String.IsNullOrEmpty(dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("Qte").ToString)) Then
                        If (dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("Qte").ToString > 0) Then
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows.Add()

                            If mode = "Modif" Then
                                dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("NumeroCommande") = lNumeroCommande.Text
                            Else
                                dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("NumeroCommande") = NumeroCommande
                            End If

                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("CodeForme") = 0
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("Ordre") = I
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("CodeArticle") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle")
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("CodeABarre") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeABarre")
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("Designation") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("Designation")
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("CodeForme") = 0
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("Qte") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("Qte")
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("Stock") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("Stock")
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("DatePeremption") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("DatePeremption")
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("PrixAchatHT") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("PrixAchatHT")
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("TVA") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("TVA")
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("TotalTTCAchat") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("TotalTTCAchat")
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("StockAlerte") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("StockAlerte")
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("EnCours") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("EnCours")
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("QteACommander") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("QteACommander")
                            dsCommande.Tables("COMMANDE_DETAILSS").Rows(J).Item("QteUnitaire") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("QteUnitaire")
                            J = J + 1
                        End If
                    End If
                End If
            Catch
            End Try
            I = I + 1
        Loop

        If mode = "Ajout" Then
            _Commande = New Data.BusinessManagement.COMMANDE()
            _Commande.NumeroCommande = NumeroCommande
        Else
            _Commande = _BusinessManagementEntities.COMMANDE.Include("COMMANDE_DETAILS").FirstOrDefault(Function(item As Data.BusinessManagement.COMMANDE) item.NumeroCommande = lNumeroCommande.Text)
            _Commande.COMMANDE_DETAILS.Clear()
        End If

        _Commande.Date = System.DateTime.Now
        _Commande.TotalHT = TotalHTCommande
        _Commande.TotalTTC = TotalTTCCommande
        _Commande.TotalTVA = lTotalTVA.Text
        _Commande.LibellePoste = System.Environment.GetEnvironmentVariable("Poste")
        _Commande.CodePersonnel = CodeOperateur
        _Commande.CodeFournisseur = cmbFournisseur.SelectedValue
        _Commande.Note = "rien"
        _Commande.NumeroFacture = ""
        _Commande.TypeCommande = cmbType.Text


        dsCommande.Tables("COMMANDE_DETAILSS").AcceptChanges()
        For I = 0 To dsCommande.Tables("COMMANDE_DETAILSS").Rows.Count - 1
            If _Commande.COMMANDE_DETAILS.Where(Function(Item) Item.CodeArticle = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("CodeArticle")).Count > 0 Then
                Try
                    _Commande.COMMANDE_DETAILS.FirstOrDefault(Function(Item) Item.CodeArticle = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("CodeArticle")).Qte += dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("Qte")
                Catch
                End Try
            Else
                _CommandeDetails = New Data.BusinessManagement.COMMANDE_DETAILS()
                _CommandeDetails.NumeroCommande = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("NumeroCommande")
                _CommandeDetails.CodeArticle = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("CodeArticle")
                _CommandeDetails.CodeABarre = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("CodeABarre")
                _CommandeDetails.Designation = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("Designation")
                _CommandeDetails.CodeForme = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("CodeForme")
                _CommandeDetails.Qte = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("Qte")
                _CommandeDetails.Stock = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("Stock")
                If dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("DatePeremption").ToString = "" Then
                    _CommandeDetails.DatePeremption = Nothing
                Else
                    _CommandeDetails.DatePeremption = Convert.ToDateTime(dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("DatePeremption").ToString)
                End If
                _CommandeDetails.PrixAchatHT = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("PrixAchatHT")
                _CommandeDetails.TVA = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("TVA")
                _CommandeDetails.TotalTTCAchat = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("TotalTTCAchat")
                _CommandeDetails.StockAlerte = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("StockAlerte")
                If dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("EnCours").ToString = "" Then
                    _CommandeDetails.EnCours = Nothing
                Else
                    _CommandeDetails.EnCours = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("EnCours")
                End If
                _CommandeDetails.QteACommander = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("QteACommander")
                _CommandeDetails.QteUnitaire = dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("QteUnitaire")
                If dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("CodeArticle") <> "" Then

                    Try
                        cmdCommande.Connection = ConnectionServeur
                        cmdCommande.CommandText = "UPDATE " + _
                                                "	ARTICLE " + _
                                                "SET " + _
                                                "	ARTICLE.DateDerniereCommande = GetDate() " + _
                                                "WHERE " + _
                                                " ARTICLE.CodeArticle =" + dsCommande.Tables("COMMANDE_DETAILSS").Rows(I).Item("CodeArticle")

                        cmdCommande.ExecuteNonQuery()

                    Catch
                    End Try
                End If
                _CommandeDetails.Ordre = I
                _Commande.COMMANDE_DETAILS.Add(_CommandeDetails)
            End If
        Next

        Try
            If mode = "Ajout" Then
                _BusinessManagementEntities.COMMANDE.AddObject(_Commande)
            End If
            _BusinessManagementEntities.SaveChanges()
        Catch ex As Exception
            BLL.ErrorManagement.ErrorService.AddMessage("Commande", Date.Now, System.Environment.GetEnvironmentVariable("Poste"), Operateur, ex.InnerException.Message)
            MessageBox.Show("Problème d'enregistrement." & vbCrLf & "Veuillez contacter votre administrateur système", "", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End Try

        '------------------------------------------------------------------------------------------------------
        '------------------------------------------------------------------------------------------------------
        '------------------------------------------------------------------------------------------------------
        '------------------------------------------------------------------------------------------------------

        Try
            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = "UPDATE " + _
                                    "	ARTICLE " + _
                                    "SET " + _
                                    "	ARTICLE.NombreCommande = ISNULL(ARTICLE.NombreCommande, 0) + 1 " + _
                                    "FROM " + _
                                    "	COMMANDE_DETAILS " + _
                                    "WHERE " + _
                                    "	COMMANDE_DETAILS.CodeArticle = ARTICLE.CodeArticle " + _
                                    "	AND COMMANDE_DETAILS.NumeroCommande =  " + Quote(IIf(mode = "Modif", lNumeroCommande.Text, NumeroCommande))
            cmdCommande.ExecuteNonQuery()

        Catch
        End Try

        '' ''         POUR AMINE SALAH
        Try
            If RecupererValeurExecuteScalaire("NbrCommandePourClasserManquant", "PARAMETRE_PHARMACIE", "Code", "1") >= 1 Then

                cmdCommande.Connection = ConnectionServeur
                cmdCommande.CommandText = "UPDATE " + _
                                        "	ARTICLE " + _
                                        "SET " + _
                                        "	CodeSituation = 2 " + _
                                        "WHERE " + _
                                        "	NombreCommande > (SELECT TOP(1) NbrCommandePourClasserManquant FROM PARAMETRE_PHARMACIE ) "
                cmdCommande.ExecuteNonQuery()
            End If

        Catch
        End Try
        '' '' Fin POUR AMIN


        'si le mode Ajout
        If mode = "Ajout" Then

            'Appel Pour selectionner le dernier ligne 
            NumeroligneCommande = selectionDernierLigneCommand()

        End If

        Try
            With gArticles
                .Splits(0).DisplayColumns("Codeforme").Visible = False
                .Splits(0).DisplayColumns("Ordre").Visible = False
            End With
        Catch ex As Exception

        End Try



        '---------------------------------------------------------------------------------------------------------------------------------------------------------------------
        '---------------------------------------------------------------------------------------------------------------------------------------------------------------------
        '---------------------------------------------------------------------------------------------------------------------------------------------------------------------
        '---------------------------------------------------------------------------------------------------------------------------------------------------------------------




        'changer le mode en consultation
        mode = "Consultation"

        bAnnuler.Enabled = False
        bConfirmer.Enabled = False
        bFournisseur.Enabled = False

        bQuitter.Enabled = True

        bModifier.Enabled = True
        bFirst.Visible = True
        bPrevious.Visible = True
        bNext.Visible = True
        bLast.Visible = True
        bAjouter.Enabled = True
        bImprimer.Enabled = True
        bListeDesManquants.Visible = False
        bListe.Enabled = True
        bExportExcel.Enabled = True

        lNombreDesArticles.Visible = False

        MasqauerZoneStatiqtique()

        'Appel pour charger les information  en question
        ChargerCommande(NumeroligneCommande)

        'pour initialiser la grid
        initgArticle()

        'initialisation des btns
        initBoutons()

        'pour iniialiser le nbre de commande en frigo
        NombredeCommandeEnInstance()
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click

        If dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 > 0 And gArticles(0, "CodeArticle") <> "" Then
            If MsgBox("Voulez vous vraiment annuler cette commande ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Annuler Commande") = MsgBoxResult.No Then
                Exit Sub
            End If
        End If

        mode = "Consultation"

        initBtnSupprimer()

        ChargerCommande(NumeroligneCommande)

        initBoutons()

    End Sub

    Private Sub initBtnSupprimer()
        bAnnuler.Enabled = False
        bConfirmer.Enabled = False
        'bCommande.Enabled = False
        bFournisseur.Enabled = False


        bQuitter.Enabled = True

        bModifier.Enabled = True
        bFirst.Visible = True
        bPrevious.Visible = True
        bNext.Visible = True
        bLast.Visible = True
        bAjouter.Enabled = True
        bImprimer.Enabled = True
        bListeDesManquants.Visible = False
        bListe.Enabled = True
        bExportExcel.Enabled = True
        gListeRecherche.Visible = False

        lNombreDesArticles.Visible = False

        MasqauerZoneStatiqtique()
    End Sub
    Private Sub bLast_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLast.Click
        Try
            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner la derniere ligne 
            NumeroligneCommande = selectionDernierLigneCommand()

            'Appel pour charger les information de l'Commande en question
            ChargerCommande(NumeroligneCommande)

        Catch ex As Exception
            WriteLine(ex.Message)
        End Try
    End Sub

    Private Sub ChargerDetails()
        Dim StrSQL As String
        Dim I As Integer
        dsCommande.Tables("COMMANDE_DETAILS").Clear()

        '------------------------------------ chargement des détails des Achats 

        StrSQL = "SELECT NumeroCommande," + _
                 "CodeArticle," + _
                 "CodeABarre," + _
                 "Designation," + _
                 "'' AS LibelleForme," + _
                 "Qte," + _
                 "Stock," + _
                 "DatePeremption," + _
                 "PrixAchatHT," + _
                 "TVA," + _
                 "TotalTTCAchat," + _
                 "EnCours," + _
                 "StockAlerte," + _
                 "QteACommander," + _
                 "QteUnitaire, " + _
                 "CONVERT(bit, 0) AS RUPTURE," + _
                 "'' AS Vide " + _
                 "FROM COMMANDE_DETAILS WHERE NumeroCommande =" + Quote(NumeroCommande) + " ORDER BY Designation"

        cmdChargementCommande.Connection = ConnectionServeur
        cmdChargementCommande.CommandText = StrSQL
        daChargementCommande = New SqlDataAdapter(cmdChargementCommande)
        daChargementCommande.Fill(dsCommande, "COMMANDE_DETAILS")

        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsCommande
            Catch ex As Exception
            End Try
            .DataMember = "COMMANDE_DETAILS"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qté"
            .Columns("Stock").Caption = "Stock"
            .Columns("DatePeremption").Caption = "Date de péremption"
            .Columns("PrixAchatHT").Caption = "Prix A HT "
            .Columns("TVA").Caption = "TVA"
            .Columns("TotalTTCAchat").Caption = "Total TTC "
            .Columns("StockAlerte").Caption = "S Alerte"
            .Columns("EnCours").Caption = "Encours "
            .Columns("QteACommander").Caption = "Qte Cmd"
            .Columns("QteUnitaire").Caption = "Q unitaire"
            .Columns("Vide").Caption = ""

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTTCAchat").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("NumeroCommande").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("NumeroCommande").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").Visible = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 120
            .Splits(0).DisplayColumns("Designation").Width = 320
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("Stock").Width = 50
            .Splits(0).DisplayColumns("DatePeremption").Width = 100
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 80
            .Splits(0).DisplayColumns("TVA").Width = 70
            .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTTCAchat").Width = 80
            .Splits(0).DisplayColumns("StockAlerte").Width = 80
            .Splits(0).DisplayColumns("EnCours").Width = 60
            .Splits(0).DisplayColumns("QteACommander").Width = 60
            .Splits(0).DisplayColumns("QteUnitaire").Width = 50

            .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(250, 250, 200)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(210, 240, 255)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(210, 240, 255)
            .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("PrixAchatHT").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("TVA").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("TotalTTCAchat").Style.BackColor = Color.FromArgb(210, 240, 255)
            .Splits(0).DisplayColumns("StockAlerte").Style.BackColor = Color.FromArgb(255, 224, 192)
            .Splits(0).DisplayColumns("QteACommander").Style.BackColor = Color.FromArgb(255, 224, 192)
            .Splits(0).DisplayColumns("RUPTURE").Style.BackColor = Color.FromArgb(255, 224, 192)
            Try
                .Splits(0).DisplayColumns("Vide").Visible = False
                .Splits(0).DisplayColumns("CodeForme").Visible = False
                .Splits(0).DisplayColumns("Ordre").Visible = False
            Catch ex As Exception
            End Try

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

        End With

    End Sub

    Private Sub bFirst_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFirst.Click
        Try

            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner le dernier ligne 
            selectionPremierLigneCommande()

            'Appel pour charger les information de l'Commande en question
            ChargerCommande(NumeroligneCommande)

        Catch ex As Exception
            WriteLine(ex.Message)
        End Try
    End Sub

    Private Sub bNext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bNext.Click
        Try
            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner l'element suivant 
            selectionLigneCommandeSuivante()

            'Appel pour charger les information de l'Commande en question
            ChargerCommande(NumeroligneCommande)

        Catch ex As Exception

            WriteLine(ex.Message)
        End Try
    End Sub

    Private Sub bPrevious_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPrevious.Click
        Try
            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner l'element precedent 
            selectionLigneCommandePrecedent()

            'Appel pour charger les information de l'Commande en question
            ChargerCommande(NumeroligneCommande)

        Catch ex As Exception
            WriteLine(ex.Message)
        End Try

    End Sub

    Private Sub lTotalTTC_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        lTotalTTCAchat.Text = lTotalTTCAchat.Text
        If lTotalTTCAchat.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lTotalTTCAchat.Text, ".")
            If lTotalTTCAchat.Text.Length - x = 1 Then
                lTotalTTCAchat.Text = lTotalTTCAchat.Text + ("00")
            ElseIf lTotalTTCAchat.Text.Length - x = 2 Then
                lTotalTTCAchat.Text = lTotalTTCAchat.Text + ("0")
            End If
        Else
            lTotalTTCAchat.Text = lTotalTTCAchat.Text + ".000"
        End If
    End Sub

    Private Sub lTotHT_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        lTotHTAchat.Text = lTotHTAchat.Text
        If lTotHTAchat.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lTotHTAchat.Text, ".")
            If lTotHTAchat.Text.Length - x = 1 Then
                lTotHTAchat.Text = lTotHTAchat.Text + ("00")
            ElseIf lTotHTAchat.Text.Length - x = 2 Then
                lTotHTAchat.Text = lTotHTAchat.Text + ("0")
            End If
        Else
            lTotHTAchat.Text = lTotHTAchat.Text + ".000"
        End If
    End Sub

    Private Sub lTotalTVA_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        lTotalTVA.Text = lTotalTVA.Text
        If lTotalTVA.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lTotalTVA.Text, ".")
            If lTotalTVA.Text.Length - x = 1 Then
                lTotalTVA.Text = lTotalTVA.Text + ("00")
            ElseIf lTotalTVA.Text.Length - x = 2 Then
                lTotalTVA.Text = lTotalTVA.Text + ("0")
            End If
        Else
            lTotalTVA.Text = lTotalTVA.Text + ".000"
        End If
    End Sub

    Private Sub fCommande_Load(ByVal sender As Object, ByVal e As System.EventArgs)
        With cmbType
            .HoldFields()
            .AddItem("Journalière")
            .AddItem("Groupée")
        End With
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim CondCrystal As String = ""
        CondCrystal = "1=1 AND {Vue_EtatCommande.NumeroCommande} = '" + lNumeroCommande.Text + "'"
        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression de commande" Then
                'fMain.Tab.TabPages(I).Show()
                'Exit Sub
                num = I
            End If
        Next
        CR.FileName = Application.StartupPath + "\EtatCommande.rpt"
        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent
        CR.RecordSelectionFormula = CondCrystal

        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression de commande"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    Private Sub bListeDesManquants_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bListeDesManquants.Click
        Dim Cond As String = ""
        Dim CodeArticles As String = ""
        Dim StrSQL As String = ""
        Dim I As Integer = 0
        Dim ListeExiste As String = ""
        Dim CondCrystal As String = ""

        dsCommande.Tables("COMMANDE_DETAILS").AcceptChanges()
        If TypeCommande = "JOURNALIERE" Then

            For I = 0 To dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1
                If dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle") <> "" Then
                    CodeArticles = CodeArticles + ", " + Quote(dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle"))
                End If
            Next
            Cond = "AND ARTICLE.CodeArticle NOT IN ( '0' " + CodeArticles + " ) "

            If Section = "INTERVALLE" Then
                Cond = Cond + " AND Section >= " + DebutIntervalle.ToString + " AND Section <= " + FinIntervalle.ToString
            End If
            If Forme <> 0 Then
                Cond = Cond + " AND ARTICLE.CodeForme = " + Forme.ToString
            End If
            If Categorie <> 0 Then
                Cond = Cond + " AND ARTICLE.CodeCategorie = " + Categorie.ToString
            End If
            If Labo <> 0 Then
                Cond = Cond + " AND CodeLabo = " + Labo.ToString
            End If
            If Rayon = "RAYON" Then
                Cond = Cond + " AND Rayon = '" + RayonSelectionne + "'"
            End If

            If (dsCommande.Tables.IndexOf("ARTICLE_GROUPEE_PAR_QTE") > -1) Then
                dsCommande.Tables("ARTICLE_GROUPEE_PAR_QTE").Clear()
            End If

            If (dsCommande.Tables.IndexOf("ARTICLE_GROUPEE_PAR_PRIX") > -1) Then
                dsCommande.Tables("ARTICLE_GROUPEE_PAR_PRIX").Clear()
            End If

            If (dsCommande.Tables.IndexOf("ARTICLE_GROUPEE") > -1) Then
                dsCommande.Tables("ARTICLE_GROUPEE").Clear()
            End If

            'Cond = Cond + " ORDER BY " + Trie
            dsCommande.Tables("COMMANDE_DETAILS").AcceptChanges()
            CodeArticles = "'0'"
            For I = 0 To dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1
                If (dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle") <> "") Then
                    CodeArticles += ", " + Quote(dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle").ToString)
                End If
            Next



            'StrSQL = "SELECT " + _
            '        "   CAST(0 as bit) Cocher, " + _
            '        "   ARTICLE.CodeArticle, " + _
            '        "   ARTICLE.CodeaBarre AS CodeaBarre, " + _
            '        "   ARTICLE.Designation AS Designation, " + _
            '        "   FORME_ARTICLE.LibelleForme AS LibelleForme, " + _
            '        "   ARTICLE.QteACommander + ARTICLE.StockAlerte - LOT_ARTICLES.Stock as Qte, " + _
            '        "   LOT_ARTICLES.DatePeremptionArticle AS DatePeremption, " + _
            '        "   LOT_ARTICLES.Stock as Stock, " + _
            '        "   ARTICLE.StockAlerte as StockAlerte, " + _
            '        "   ARTICLE.QteACommander as QteACommander " + _
            '        "FROM " + _
            '        "	ARTICLE " + _
            '        "    INNER JOIN (SELECT CodeArticle, SUM(LOT_ARTICLE.QteLotArticle ) AS Stock, MAX(DatePeremptionArticle) AS DatePeremptionArticle FROM LOT_ARTICLE GROUP BY CodeArticle ) AS LOT_ARTICLES ON LOT_ARTICLES.CodeArticle = ARTICLE.CodeArticle " + _
            '        "    LEFT OUTER join CATEGORIE on ARTICLE.CodeCategorie =CATEGORIE.CodeCategorie " + _
            '        "    LEFT OUTER join FORME_ARTICLE on ARTICLE.CodeForme =FORME_ARTICLE.CodeForme " + _
            '        "    LEFT OUTER JOIN SITUATION_ARTICLE on ARTICLE .CodeSituation =SITUATION_ARTICLE .CodeSituationArticle " + _
            '        "WHERE " + _
            '        "	1 = 1 " + Cond + _
            '        "   AND ARTICLE.Supprime = 0 " + _
            '        "	AND LOT_ARTICLES.Stock  <= ARTICLE.StockAlerte " + _
            '        "   AND StockAlerte - Stock + QteACommander > 0 " + _
            '        "	AND ARTICLE.CodeCategorie  <> 9 " + _
            '        "	AND DATEDIFF ( DAY  , ARTICLE.DateAlerte   , GETDATE() ) > (SELECT TOP(1) ISNULL(NePAsSortirLesManquantsDepuis, 5) FROM PARAMETRES ) " + _
            '        "	AND LibelleSituationArticle <>'SUSPENDU'"

            '' AmineSalah
            StrSQL = "EXEC P_CommandeJournaliere_ArticleManquant @SansManquantDepuis = " + Quote(SansManquantDepuis) + ", @Section = " + Quote(Section) + ", @DebutIntervalle = " + Quote(DebutIntervalle) + ", @FinIntervalle = " + Quote(FinIntervalle) + ", @Forme = " + Quote(Forme) + ", @Categorie = " + Quote(Categorie) + ", @Labo = " + Quote(Labo) + ", @Rayon = " + Quote(Rayon) + ", @RayonSelectionne = " + Quote(RayonSelectionne) + ", @OrderBy = " + Quote(Trie)
            '' Fin amineSalah
        Else
            If Section = "INTERVALLE" Then
                Cond = Cond + " AND Section >= " + DebutIntervalle.ToString + " AND Section <= " + FinIntervalle.ToString
            End If
            If Forme <> 0 Then
                Cond = Cond + " AND ARTICLE.CodeForme = " + Forme.ToString
            End If
            If Categorie <> 0 Then
                Cond = Cond + " AND ARTICLE.CodeCategorie = " + Categorie.ToString
            End If
            If Labo <> 0 Then
                Cond = Cond + " AND CodeLabo = " + Labo.ToString
            End If
            If Rayon = "RAYON" Then
                Cond = Cond + " AND Rayon = '" + RayonSelectionne + "'"
            End If

            If (dsCommande.Tables.IndexOf("ARTICLE_GROUPEE_PAR_QTE") > -1) Then
                dsCommande.Tables("ARTICLE_GROUPEE_PAR_QTE").Clear()
            End If

            If (dsCommande.Tables.IndexOf("ARTICLE_GROUPEE_PAR_PRIX") > -1) Then
                dsCommande.Tables("ARTICLE_GROUPEE_PAR_PRIX").Clear()
            End If

            If (dsCommande.Tables.IndexOf("ARTICLE_GROUPEE") > -1) Then
                dsCommande.Tables("ARTICLE_GROUPEE").Clear()
            End If

            'Cond = Cond + " ORDER BY " + Trie
            dsCommande.Tables("COMMANDE_DETAILS").AcceptChanges()
            CodeArticles = "'0'"
            For I = 0 To dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1
                If (dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle") <> "") Then
                    CodeArticles += ", " + Quote(dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle").ToString)
                End If
            Next



            StrSQL = "" + _
                        "SELECT " + _
                        "   CAST(0 as bit) Cocher, " + _
                        "   ARTICLE.CodeArticle, " + _
                        "   MAX(article.CodeaBarre) AS CodeaBarre, " + _
                        "   MAX(article.Designation) AS Designation, " + _
                        "   MAX(FORME_ARTICLE.LibelleForme) AS LibelleForme, " + _
                        "   SUM(Qte) as Qte, " + _
                        "   MAX(DatePeremptionArticles.DatePeremptionArticle) AS DatePeremption, " + _
                        "   MAX(LOT_ARTICLES.Stock) as Stock,  " + _
                        "   MAX(ARTICLE.StockAlerte) as StockAlerte,  " + _
                        "   MAX(ARTICLE.QteACommander) as QteACommander  " + _
                        "FROM   ARTICLE " + _
                        "       INNER join [dbo].[ArticleCommandeRoupee](" + NombreDeJour.ToString + ", " + Quote(DebutPeriode.ToString) + ", " + Quote(FinPeriode.ToString) + ", " + Quote(CommandeEnCours) + ") ListeArticle ON ListeArticle.CodeArticle = ARTICLE.CodeArticle " + _
                        "       INNER JOIN (SELECT CodeArticle, SUM(LOT_ARTICLE.QteLotArticle ) AS Stock FROM LOT_ARTICLE GROUP BY CodeArticle ) AS LOT_ARTICLES ON LOT_ARTICLES.CodeArticle = ARTICLE.CodeArticle " + _
                        "       LEFT OUTER join CATEGORIE on ARTICLE.CodeCategorie =CATEGORIE.CodeCategorie " + _
                        "       LEFT OUTER join FORME_ARTICLE on ARTICLE.CodeForme =FORME_ARTICLE.CodeForme " + _
                        "       LEFT OUTER JOIN SITUATION_ARTICLE on ARTICLE .CodeSituation =SITUATION_ARTICLE .CodeSituationArticle  " + _
                        "       LEFT OUTER JOIN (Select CodeArticle, MAX(DatePeremptionArticle) AS DatePeremptionArticle from LOT_ARTICLE GROUP BY CodeArticle) AS DatePeremptionArticles ON DatePeremptionArticles.CodeArticle = Article.codearticle " + _
                        "WHERE 1 = 1 AND " + _
                        "ARTICLE.CodeArticle NOT IN (" + CodeArticles + ")" + _
                        "   AND ARTICLE.Supprime = 0 " + _
                        Cond + _
                        "AND LibelleSituationArticle <>'SUSPENDU' group by ARTICLE.CodeArticle   " + _
                        "ORDER BY SUM(Qte) desc  "
        End If




        Dim listeManquant As New fListeDesManquants
        fListeDesManquants.Requette = StrSQL
        fListeDesManquants.CondCrystal = CondCrystal
        fListeDesManquants.FromCommandeOrProjetCommande = True
        fListeDesManquants.CommandeEnCours = CommandeEnCours

        listeManquant.ShowDialog()

        If fListeDesManquants.ConvertirEnCommande = True Then
            '------------------------------- élemination des lignes vide ou qte =0 
            I = 0
            dsCommande.Tables("COMMANDE_DETAILS").AcceptChanges()

            Do While I < dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1
                If dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                    dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Delete()
                    I = 0
                Else
                    I = I + 1
                End If
            Loop


            Dim DatePeremption As Date

            For I = 0 To fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows.Count - 1
                Try
                    If fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("CodeArticle") <> "" Then

                        If fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Cocher") = True Then

                            NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
                            dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)

                            NouvelArticle("CodeArticle") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("CodeArticle")
                            NouvelArticle("CodeABarre") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("CodeABarre")
                            NouvelArticle("Qte") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Qte")
                            NouvelArticle("DatePeremption") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("DatePeremption")

                            NouvelArticle("NumeroCommande") = NumeroCommande
                            NouvelArticle("Designation") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("Designation")

                            'NouvelArticle("CodeForme") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("CodeForme")
                            NouvelArticle("Stock") = CalculeStock(NouvelArticle("CodeArticle"))

                            NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                            NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

                            NouvelArticle("StockAlerte") = RecupererValeurExecuteScalaire("StockAlerte", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                            NouvelArticle("EnCours") = 0

                            NouvelArticle("QteACommander") = RecupererValeurExecuteScalaire("QteACommander", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                            'NouvelArticle("Qte") = (NouvelArticle("StockAlerte") - NouvelArticle("Stock") + NouvelArticle("QteACommander"))

                            NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
                            NouvelArticle("QteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

                            NouvelArticle("LibelleForme") = fListeDesManquants.dsManquant.Tables("MANQUANT_DETAILS").Rows(I).Item("LibelleForme")

                            ''------------------ récupération de la date de péremption

                            'StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
                            'System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
                            'NouvelArticle("CodeArticle") + "' Order by DatePeremptionArticle DESC "

                            'cmdCommande.Connection = ConnectionServeur
                            'cmdCommande.CommandText = StrSQL

                            'Try
                            '    DatePeremption = cmdCommande.ExecuteScalar()
                            'Catch ex As Exception
                            '    Console.WriteLine(ex.Message)
                            'End Try

                            'If DatePeremption = #12:00:00 AM# Then
                            '    'NouvelArticle("DatePeremption") = "01/01/1900"
                            'Else
                            '    NouvelArticle("DatePeremption") = DatePeremption
                            'End If

                        End If

                    End If
                Catch ex As Exception

                End Try
            Next
            '------------------ ajout d une ligne vide 
            NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
            NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
            NouvelArticle("CodeArticle") = ""
            NouvelArticle("CodeABarre") = ""
            dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)
        End If
        gArticles.MoveLast()
        gArticles.Col = 2
        gArticles.EditActive = True

        '-------------------------- élémination des lignes vides 
        I = 0
        Do While I < dsCommande.Tables("COMMANDE_DETAILS").Rows.Count
            'dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("NumeroCommande") = NumeroCommande
            Try
                If dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                    dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Delete()
                End If
            Catch ex As Exception
            End Try

            I = I + 1
        Loop

        CalculerMontants()
    End Sub

    Private Sub bInstance_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bInstance.Click

        Dim cmd As New SqlCommand
        Dim NumeroCommandeInstance As String = ""
        Dim StrMajLOT As String = ""
        Dim StrMajReglement As String = ""
        Dim I As Integer = 0
        Dim NumeroLot As String = "RIEN"
        Dim NouveauNumeroLot As String = ""
        Dim unite As Double = 0.0
        Dim Ordre As Integer = 1


        Dim StrSQL As String = ""

        '**************************************************************************************************
        '********* Test du cas ou la liste est vide : chargement des anciens ventes en instance ***********
        '**************************************************************************************************

        If dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle") = "" Then

            Dim MyCommandeInstance As New fListeDesCommandesEnInstance

            MyCommandeInstance.ShowDialog()
            NumeroCommandeInstance = fListeDesCommandesEnInstance.NumeroCommande
            ComfirmerMettreEnINstance = fListeDesCommandesEnInstance.ComfirmerMettreEnINstance

            MyCommandeInstance.Dispose()
            MyCommandeInstance.Close()

            If ComfirmerMettreEnINstance = False Then
                gArticles.Col = 1
                gArticles.EditActive = True
                Exit Sub
            End If

            '----------------------- chargement des datatables 

            '------------------- chargement de l'Entêtes de la commandes  
            Try
                dsCommande.Tables("COMMANDE_INSTANCE").Clear()
            Catch ex As Exception

            End Try
            Try
                dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Clear()
            Catch ex As Exception

            End Try

            StrSQL = "SELECT * FROM COMMANDE_INSTANCE WHERE NumeroCommandeInstance ='" + NumeroCommandeInstance + "'"
            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL
            daCommande = New SqlDataAdapter(cmdCommande)
            daCommande.Fill(dsCommande, "COMMANDE_INSTANCE")
            If dsCommande.Tables("COMMANDE_INSTANCE").Rows.Count > 0 Then
                lDateCommande.Text = dsCommande.Tables("COMMANDE_INSTANCE").Rows(dsCommande.Tables("COMMANDE_INSTANCE").Rows.Count - 1)("Date")
                TotalTTCCommande = dsCommande.Tables("COMMANDE_INSTANCE").Rows(dsCommande.Tables("COMMANDE_INSTANCE").Rows.Count - 1)("TotalTTC")
                TotalHTCommande = dsCommande.Tables("COMMANDE_INSTANCE").Rows(dsCommande.Tables("COMMANDE_INSTANCE").Rows.Count - 1)("TotalHT")
                TVA = dsCommande.Tables("COMMANDE_INSTANCE").Rows(dsCommande.Tables("COMMANDE_INSTANCE").Rows.Count - 1)("TotalTVA")
                lTotalTTCAchat.Text = Math.Round(dsCommande.Tables("COMMANDE_INSTANCE").Rows(dsCommande.Tables("COMMANDE_INSTANCE").Rows.Count - 1)("TotalTTC"), 3)
                lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsCommande.Tables("COMMANDE_INSTANCE").Rows(dsCommande.Tables("COMMANDE_INSTANCE").Rows.Count - 1)("CodeOperateur"))
                cmbFournisseur.SelectedValue = dsCommande.Tables("COMMANDE_INSTANCE").Rows(dsCommande.Tables("COMMANDE_INSTANCE").Rows.Count - 1)("CodeFournisseur")
                cmbType.Text = dsCommande.Tables("COMMANDE_INSTANCE").Rows(dsCommande.Tables("COMMANDE_INSTANCE").Rows.Count - 1)("TypeCommande")
            End If

            '----------------------chargement des détails des Commandes 
            StrSQL = "SELECT NumeroCommandeInstance," + _
                     "COMMANDE_INSTANCE_DETAILS.CodeArticle," + _
                     "ARTICLE.CodeABarre," + _
                     "ARTICLE.Designation," + _
                     "'' AS LibelleForme," + _
                     "Qte," + _
                     "COMMANDE_INSTANCE_DETAILS.DatePeremption," + _
                     "COMMANDE_INSTANCE_DETAILS.PrixAchatHT," + _
                     "COMMANDE_INSTANCE_DETAILS.TVA," + _
                     "COMMANDE_INSTANCE_DETAILS.TotalTTCAchat," + _
                     "COMMANDE_INSTANCE_DETAILS.EnCours," + _
                     "ARTICLE.StockAlerte," + _
                     "ARTICLE.QteACommander," + _
                     "COMMANDE_INSTANCE_DETAILS.QteJour," + _
                     "COMMANDE_INSTANCE_DETAILS.QteUnitaire," + _
                     "CASE WHEN Stockcalcule IS NULL THEN 0 ELSE Stockcalcule END AS StockArticle " + _
                     "FROM  COMMANDE_INSTANCE_DETAILS " + _
                     "INNER JOIN ARTICLE ON ARTICLE.CodeArticle = COMMANDE_INSTANCE_DETAILS.CodeArticle " + _
                     "LEFT OUTER JOIN (SELECT CodeArticle, SUM(QteLotArticle) AS Stockcalcule " + _
                     "FROM dbo.LOT_ARTICLE  GROUP BY CodeArticle) AS StockArticle ON dbo.COMMANDE_INSTANCE_DETAILS.CodeArticle = StockArticle.CodeArticle " + _
                     "WHERE NumeroCommandeInstance =" + Quote(NumeroCommandeInstance) + " "

            '************************* pour lier la gride par une autre datatable differente a celle 
            '************************* de l affichage final du resultat (juste pour eliminer le lenteur de l affichage 

            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL
            daCommande = New SqlDataAdapter(cmdCommande)
            daCommande.Fill(dsCommande, "COMMANDE_INSTANCE_DETAILS1")

            With gArticles
                .Columns.Clear()
                .DataSource = dsCommande
                .DataMember = "COMMANDE_INSTANCE_DETAILS1"
                .Rebind(False)
                .Columns("CodeABarre").Caption = "Code article"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Qte").Caption = "Qté"
                .Columns("StockArticle").Caption = "Stock"
                .Columns("DatePeremption").Caption = "Date de péremption"
                .Columns("PrixAchatHT").Caption = "Prix A HT "
                .Columns("TVA").Caption = "TVA"
                .Columns("TotalTTCAchat").Caption = "Total TTC "
                .Columns("StockAlerte").Caption = "S Alerte"
                .Columns("EnCours").Caption = "Encours "
                .Columns("QteJour").Caption = "Qte Cmd"
                .Columns("QteUnitaire").Caption = "Q unitaire"

                ' Centrer toutes les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Locked = True
                Next

                .Splits(0).DisplayColumns("CodeABarre").Locked = False
                .Splits(0).DisplayColumns("Designation").Locked = False
                .Splits(0).DisplayColumns("Qte").Locked = False
                .Splits(0).DisplayColumns("QteACommander").Locked = False
                .Splits(0).DisplayColumns("StockAlerte").Locked = False
                '.Splits(0).DisplayColumns("RUPTURE").Locked = False

                .Splits(0).DisplayColumns("NumeroCommandeInstance").Width = 0
                .Splits(0).DisplayColumns("NumeroCommandeInstance").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

                .Splits(0).DisplayColumns("CodeABarre").Width = 120
                .Splits(0).DisplayColumns("Designation").Width = 320
                .Splits(0).DisplayColumns("LibelleForme").Width = 80
                .Splits(0).DisplayColumns("Qte").Width = 50
                .Splits(0).DisplayColumns("StockArticle").Width = 50
                .Splits(0).DisplayColumns("DatePeremption").Width = 100
                .Splits(0).DisplayColumns("PrixAchatHT").Width = 80
                .Splits(0).DisplayColumns("TVA").Width = 70
                .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalTTCAchat").Width = 80
                .Splits(0).DisplayColumns("StockAlerte").Width = 80
                .Splits(0).DisplayColumns("EnCours").Width = 60
                .Splits(0).DisplayColumns("QteJour").Width = 50
                .Splits(0).DisplayColumns("QteUnitaire").Width = 50
                '.Splits(0).DisplayColumns("CodeForme").Visible = False
                Try
                    .Splits(0).DisplayColumns("CodeForme").Visible = False
                    .Splits(0).DisplayColumns("Ordre").Visible = False
                Catch ex As Exception
                End Try
                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near

                .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far

                .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(250, 250, 200)
                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(210, 240, 255)
                .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(210, 240, 255)
                .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(210, 255, 230)
                .Splits(0).DisplayColumns("PrixAchatHT").Style.BackColor = Color.FromArgb(210, 255, 230)
                .Splits(0).DisplayColumns("TVA").Style.BackColor = Color.FromArgb(210, 255, 230)
                .Splits(0).DisplayColumns("TotalTTCAchat").Style.BackColor = Color.FromArgb(210, 240, 255)

                .Splits(0).DisplayColumns("StockAlerte").Style.BackColor = Color.FromArgb(255, 224, 192)
                .Splits(0).DisplayColumns("QteACommander").Style.BackColor = Color.FromArgb(255, 224, 192)
                ' .Splits(0).DisplayColumns("RUPTURE").Style.BackColor = Color.FromArgb(255, 224, 192)

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True

            End With

            '*************************** il faut maintenant déclarer la table ou on va afficher le resultat
            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL
            daCommande = New SqlDataAdapter(cmdCommande)
            daCommande.Fill(dsCommande, "COMMANDE_INSTANCE_DETAILS")

            Dim DatePeremption As Date

            Dim J As Integer
            ProgressBar.Visible = True
            GroupeJauge.Visible = True
            ProgressBar.Value = 0
            unite = 100 / (dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows.Count - 1)

            'Arrêter la capture d'evenement clavier sur le contrôle 
            RemoveHandler Me.bAjouter.Click, AddressOf Me.bAjouter_Click

            For I = 0 To dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows.Count - 1

                If unite * J < (dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows.Count - 1) Then
                    If (unite * J) <= 100 Then
                        ProgressBar.Value = unite * J
                    Else
                        ProgressBar.Value = 100
                    End If
                End If

                If dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("CodeArticle") <> "" Then
                    dr = dsCommande.Tables("COMMANDE_DETAILS").NewRow

                    dr.Item("CodeArticle") = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("CodeArticle")
                    dr.Item("CodeABarre") = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("CodeABarre")
                    dr.Item("Designation") = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("Designation")

                    lArticleEnCours.Text = dr.Item("Designation")

                    dr.Item("LibelleForme") = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("LibelleForme")
                    'dr.Item("CodeForme") = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("CodeForme")
                    dr.Item("Qte") = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("Qte")
                    dr.Item("Stock") = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("StockArticle") 'CalculeStock(dr.Item("CodeArticle"))
                    dr.Item("PrixAchatHT") = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("PrixAchatHT")
                    dr.Item("TVA") = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("TVA")
                    dr.Item("TotalTTCAchat") = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("TotalTTCAchat")
                    dr.Item("StockAlerte") = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("StockAlerte")
                    dr.Item("EnCours") = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("EnCours")
                    dr.Item("QteACommander") = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("QteJour")
                    dr.Item("QteUnitaire") = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows(I).Item("QteUnitaire")

                    '---------------------- ajout des informations manquantes dans l'instance 

                    '----------------------- récupération de la date de péremption

                    StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
                    System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
                    dr.Item("CodeArticle") + "' Order by DatePeremptionArticle DESC "

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        DatePeremption = cmd.ExecuteScalar()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                    If DatePeremption = #12:00:00 AM# Then
                    Else
                        dr.Item("DatePeremption") = DatePeremption
                    End If
                    '----------------------- calcul du stock

                    dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(dr)
                End If

            Next

            'Reprendre la capture d'evenement clavier sur le contrôle 
            AddHandler Me.bAjouter.Click, AddressOf Me.bAjouter_Click
            ProgressBar.Value = 100
            ProgressBar.Visible = False
            GroupeJauge.Visible = False

            With gArticles
                .Columns.Clear()
                .DataSource = dsCommande
                .DataMember = "COMMANDE_DETAILS"
                .Rebind(False)
                .Columns("CodeABarre").Caption = "Code article"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Qte").Caption = "Qté"
                .Columns("Stock").Caption = "Stock"
                .Columns("DatePeremption").Caption = "Date de péremption"
                .Columns("PrixAchatHT").Caption = "Prix A HT "
                .Columns("TVA").Caption = "TVA"
                .Columns("TotalTTCAchat").Caption = "Total TTC "
                .Columns("StockAlerte").Caption = "S Alerte"
                .Columns("EnCours").Caption = "Encours "
                .Columns("QteACommander").Caption = "Qte Cmd"
                .Columns("QteUnitaire").Caption = "Q unitaire"

                ' Centrer toutes les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Locked = True
                Next

                .Splits(0).DisplayColumns("CodeABarre").Locked = False
                .Splits(0).DisplayColumns("Designation").Locked = False
                .Splits(0).DisplayColumns("Qte").Locked = False
                .Splits(0).DisplayColumns("QteACommander").Locked = False
                .Splits(0).DisplayColumns("StockAlerte").Locked = False
                .Splits(0).DisplayColumns("RUPTURE").Locked = False

                .Splits(0).DisplayColumns("NumeroCommande").Width = 0
                .Splits(0).DisplayColumns("NumeroCommande").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("CodeArticle").Visible = False

                .Splits(0).DisplayColumns("CodeABarre").Width = 120
                .Splits(0).DisplayColumns("Designation").Width = 320
                .Splits(0).DisplayColumns("LibelleForme").Width = 80
                .Splits(0).DisplayColumns("Qte").Width = 50
                .Splits(0).DisplayColumns("Stock").Width = 50
                .Splits(0).DisplayColumns("DatePeremption").Width = 100
                .Splits(0).DisplayColumns("PrixAchatHT").Width = 80
                .Splits(0).DisplayColumns("TVA").Width = 70
                .Splits(0).DisplayColumns("TotalTTCAchat").Width = 80
                .Splits(0).DisplayColumns("StockAlerte").Width = 80
                .Splits(0).DisplayColumns("EnCours").Width = 60
                .Splits(0).DisplayColumns("QteACommander").Width = 60
                .Splits(0).DisplayColumns("QteUnitaire").Width = 50
                .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
                '.Splits(0).DisplayColumns("CodeForme").Visible = False

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near

                .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(250, 250, 200)
                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(210, 240, 255)
                .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(210, 240, 255)
                .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(210, 255, 230)
                .Splits(0).DisplayColumns("PrixAchatHT").Style.BackColor = Color.FromArgb(210, 255, 230)
                .Splits(0).DisplayColumns("TVA").Style.BackColor = Color.FromArgb(210, 255, 230)
                .Splits(0).DisplayColumns("TotalTTCAchat").Style.BackColor = Color.FromArgb(210, 240, 255)
                .Splits(0).DisplayColumns("StockAlerte").Style.BackColor = Color.FromArgb(255, 224, 192)
                .Splits(0).DisplayColumns("QteACommander").Style.BackColor = Color.FromArgb(255, 224, 192)
                .Splits(0).DisplayColumns("RUPTURE").Style.BackColor = Color.FromArgb(255, 224, 192)
                Try
                    .Splits(0).DisplayColumns("Vide").Visible = False
                    .Splits(0).DisplayColumns("CodeForme").Visible = False
                    .Splits(0).DisplayColumns("Ordre").Visible = False
                Catch ex As Exception
                End Try
                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True

            End With

            I = 0
            '---------------------- Suppression des ligne vides 
            Do While I < dsCommande.Tables("COMMANDE_DETAILS").Rows.Count
                If dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                    dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Delete()
                End If
                I = I + 1
            Loop

            '---------------------- Suppression de l'instance 
            'details
            StrSQL = "DELETE FROM COMMANDE_INSTANCE_DETAILS WHERE NumeroCommandeInstance='" + NumeroCommandeInstance + "'"
            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            Try
                cmd.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            'entête
            StrSQL = "DELETE FROM COMMANDE_INSTANCE WHERE NumeroCommandeInstance='" + NumeroCommandeInstance + "'"
            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            Try
                cmd.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            gArticles.Col = 2
            gArticles.EditActive = True
            Exit Sub
        End If

        If cmbFournisseur.Text = "" Then
            MsgBox("Veuillez choisir un Fournisseur !", MsgBoxStyle.Critical, "Erreur")
            cmbFournisseur.Focus()
            Exit Sub
        End If

        '**************************************************************************************************
        '***************** Enregistrement d'une nouvelle commande en instance  *******************************
        '**************************************************************************************************

        '--------------------------- pour verifier le calcul : si l'utilisateur ne clique pas entree
        '--------------------------- sur la cellule qte du dernier ligne la somme TTC sera fausse
        CalculerMontants()
        '---------------------------------- récupération du dernier numero de vente --------------------
        NumeroCommandeInstance = RecupereNumeroInstance()
        '------------------------------ enregistrement de l'entête de la vente -------------------------
        '-----------------------------------------------------------------------------------------------

        'Dim MyVenteInstance As New fListeDesVentesEnInstance
        'MyVenteInstance.ShowDialog()
        'NumeroVenteInstance = fListeDesVentesEnInstance.NumeroVente
        'ComfirmerMettreEnINstance = fListeDesVentesEnInstance.ComfirmerMettreEnINstance

        'MyVenteInstance.Dispose()
        'MyVenteInstance.Close()
        '-----------------------------------------------------------------------------------------------
        Try
            dsCommande.Tables("COMMANDE_INSTANCE").Clear()
        Catch ex As Exception
        End Try

        Try
            dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Clear()
        Catch ex As Exception
        End Try

        Dim myNomInstance As New fNomInstance
        fNomInstance.NomTable = "COMMANDE_INSTANCE"
        myNomInstance.ShowDialog()

        NomInstance = fNomInstance.NomInstance
        ConfirmerInstance = fNomInstance.Confirmer

        CodeOperateurInstance = fNomInstance.CodeOperateur

        myNomInstance.Dispose()
        myNomInstance.Close()

        If ConfirmerInstance = False Then
            Exit Sub
        End If

        StrSQL = "SELECT * FROM COMMANDE_INSTANCE ORDER BY NumeroCommandeInstance ASC"
        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande = New SqlDataAdapter(cmdCommande)
        daCommande.Fill(dsCommande, "COMMANDE_INSTANCE")
        cbCommande = New SqlCommandBuilder(daCommande)
        dr = dsCommande.Tables("COMMANDE_INSTANCE").NewRow()

        With dsCommande
            dr.Item("NumeroCommandeInstance") = NumeroCommandeInstance
            dr.Item("Date") = System.DateTime.Now
            dr.Item("TotalHT") = TotalHTCommande
            dr.Item("TotalTTC") = TotalTTCCommande
            dr.Item("TotalTVA") = TVA
            dr.Item("LibellePoste") = System.Environment.GetEnvironmentVariable("Poste")
            dr.Item("CodeOperateur") = CodeOperateurInstance
            dr.Item("CodeFournisseur") = cmbFournisseur.SelectedValue
            dr.Item("Note") = "rien"
            dr.Item("NomCommandeInstance") = NomInstance
            dr.Item("TypeCommande") = cmbType.Text

            dsCommande.Tables("COMMANDE_INSTANCE").Rows.Add(dr)
        End With

        '***************************************** enregistrement de l'entête de la vente *************

        Try
            daCommande.Update(dsCommande, "COMMANDE_INSTANCE")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsCommande.Reset()
            Exit Sub
        End Try

        '-------------------- préparation des détails de la vente pour enregistrement ------------------
        '-----------------------------------------------------------------------------------------------
        '-----------------------------------------------------------------------------------------------

        StrSQL = "SELECT TOP (0) * FROM COMMANDE_INSTANCE_DETAILS ORDER BY NumeroCommandeInstance ASC"
        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande = New SqlDataAdapter(cmdCommande)
        daCommande.Fill(dsCommande, "COMMANDE_INSTANCE_DETAILS")
        cbCommande = New SqlCommandBuilder(daCommande)

        For I = 0 To dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1
            If dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle") <> "" Then

                dr = dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").NewRow()

                dr.Item("NumeroCommandeInstance") = NumeroCommandeInstance
                dr.Item("CodeArticle") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeArticle")
                dr.Item("CodeABarre") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeABarre")
                dr.Item("Designation") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("Designation")
                'dr.Item("CodeForme") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("CodeForme")
                dr.Item("Qte") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("Qte")
                dr.Item("Stock") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("Stock")

                dr.Item("PrixAchatHT") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("PrixAchatHT")
                dr.Item("TVA") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("TVA")
                dr.Item("TotalTTCAchat") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("TotalTTCAchat")

                dr.Item("StockAlerte") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("StockAlerte")
                dr.Item("EnCours") = 0
                dr.Item("QteJour") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("QteACommander")
                dr.Item("QteUnitaire") = dsCommande.Tables("COMMANDE_DETAILS").Rows(I).Item("QteUnitaire")
                dr.Item("Ordre") = Ordre
                Ordre += 1

                dsCommande.Tables("COMMANDE_INSTANCE_DETAILS").Rows.Add(dr)

            End If
        Next

        '***************************************** enregistrement des détails de la commande *************

        Try
            daCommande.Update(dsCommande, "COMMANDE_INSTANCE_DETAILS")
            Init()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données (Enregistrement des détails Commandes) !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsCommande.Reset()
            Exit Sub
        End Try

        mode = "Consultation"

        Init()
        bAnnuler.Enabled = False
        bConfirmer.Enabled = False

        bModifier.Enabled = True
        bFirst.Visible = True
        bPrevious.Visible = True
        bNext.Visible = True
        bLast.Visible = True
        bAjouter.Enabled = True
        bExportExcel.Enabled = True

    End Sub
    Public Function RecupereNumeroInstance()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max(NumeroCommandeInstance) FROM COMMANDE_INSTANCE"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero séquenciel de l année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If

        Return ValeurRetour

    End Function

    Private Sub bCommande_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click



        Dim cmd As New SqlCommand
        Dim NumeroCommande As String = ""


        If mode = "Ajout" Or mode = "Modif" Then
            If gArticles.RowCount > 0 Then
                'Test si la lign est NEW ADDED et elle est vide
                If gArticles(gArticles.Row, ("CodeArticle")) <> "" Then
                    gArticles.Delete()
                    gArticles.Focus()
                    CalculerMontants()

                    AfficherStatistique(gArticles(gArticles.Row, "CodeArticle"))
                    gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
                    gArticles.Splits(0).DisplayColumns("Designation").Locked = False
                    gArticles.Splits(0).DisplayColumns("QteACommander").Locked = False
                    gArticles.Splits(0).DisplayColumns("RUPTURE").Locked = False
                    gArticles.Splits(0).DisplayColumns("StockAlerte").Locked = False
                    gArticles.EditActive = True
                    dsCommande.Tables("COMMANDE_DETAILS").AcceptChanges()
                    AfficherStatistique(gArticles(gArticles.Row, "CodeArticle"))
                    'Try
                    '    gArticles_MouseClick(sender, e)
                    'Catch ex As Exception
                    'End Try

                End If
            End If
            Exit Sub
        Else

            If ControleDAcces(7, "SUPPRESSION_COMMANDE") = "False" Then
                Exit Sub
            End If

            If MsgBox("Voulez vous vraiment supprimer cette commande " + lNumeroCommande.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                '--------------------------- suppression des details de la comandes
                NumeroCommande = lNumeroCommande.Text
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM COMMANDE_DETAILS WHERE NumeroCommande ='" + NumeroCommande + "'"
                    cmd.ExecuteNonQuery()

                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
                '--------------------------- suppression de la commande
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM COMMANDE WHERE NumeroCommande ='" + NumeroCommande + "'"
                    cmd.ExecuteNonQuery()

                    'Ma nouvelle position

                    If NumeroligneCommande > 1 Then
                        NumeroligneCommande = NumeroligneCommande - 1
                    ElseIf NumeroligneCommande = 1 Then

                    End If
                    'charger la nouvelle position
                    ChargerCommande(NumeroligneCommande)

                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub

    Private Sub cmbType_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbType.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbType.Text = cmbType.WillChangeToText
        Else
            cmbType.OpenCombo()
        End If
    End Sub



    Private Sub bModifier_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifier.Click
        AfficherZoneStatiqtique()
        If ControleDAcces(7, "MODIFICATION_COMMANDE") = "False" Then
            Exit Sub
        End If

        mode = "Modif"


        'ajout d'un nouvel enregistrement vide dans les datatables convenables
        NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
        NouvelArticle("Designation") = "" 'initialisation pour que le calcul des montants ne compare pas 
        NouvelArticle("CodeArticle") = "" ' des chaines de caractères avec des NULL
        NouvelArticle("CodeABarre") = ""
        'NouvelArticle("Qte") = 0
        dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)

        For I = 0 To dsCommande.Tables("COMMANDE_DETAILS").Columns.Count - 1
            Me.gArticles.Splits(0).DisplayColumns(I).AllowFocus = False
        Next


        With gArticles

            '.Splits(0).DisplayColumns("CodeArticle").Locked = False
            .Splits(0).DisplayColumns("Designation").Locked = False
            .Splits(0).DisplayColumns("Qte").Locked = False
            .Splits(0).DisplayColumns("DatePeremption").Locked = False
            .Splits(0).DisplayColumns("CodeABarre").Locked = False
            .Splits(0).DisplayColumns("QteACommander").Locked = False
            .Splits(0).DisplayColumns("StockAlerte").Locked = False
            .Splits(0).DisplayColumns("RUPTURE").Locked = False

            '.Splits(0).DisplayColumns("CodeArticle").AllowFocus = True
            .Splits(0).DisplayColumns("Designation").AllowFocus = True
            .Splits(0).DisplayColumns("Qte").AllowFocus = True
            .Splits(0).DisplayColumns("DatePeremption").AllowFocus = True
            .Splits(0).DisplayColumns("CodeABarre").AllowFocus = True
            .Splits(0).DisplayColumns("QteACommander").AllowFocus = True
            .Splits(0).DisplayColumns("StockAlerte").AllowFocus = True
            .Splits(0).DisplayColumns("RUPTURE").AllowFocus = True

        End With

        initBoutons()

        cmbFournisseur.Enabled = True
        'lNumeroCommande.Text = "-------------"

        bAnnuler.Enabled = True
        bConfirmer.Enabled = True

        'bFournisseur.Enabled = True

        tNumeroBlFact.Value = ""

        bModifier.Enabled = False
        bFirst.Visible = False
        bPrevious.Visible = False
        bNext.Visible = False
        bLast.Visible = False
        bAjouter.Enabled = False
        bInstance.Enabled = True
        bImprimer.Enabled = False
        bListe.Enabled = False
        bExportExcel.Enabled = False
        bQuitter.Enabled = False

        'GroupeFournisseur.Enabled = True

        gArticles.Focus()
        gArticles.Col = 1
        gArticles.EditActive = True


    End Sub

    Private Sub bListe_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bListe.Click

        Dim fInstanceListeDesCommandes As New fListeDesCommandes
        fInstanceListeDesCommandes.ShowDialog()

        NumeroCommande = fListeDesCommandes.NumeroCommande

        fInstanceListeDesCommandes.Dispose()
        fInstanceListeDesCommandes.Close()

        If NumeroCommande <> "" Then
            Dim j As Integer
            Dim DataRowRecherche As DataRow
            Dim NumeroLigne As Integer
            For j = 0 To dsCommande.Tables("COMMANDE").Rows.Count - 1
                DataRowRecherche = dsCommande.Tables("COMMANDE").Rows(j)
                If DataRowRecherche.Item("NumeroCommande") = NumeroCommande Then
                    NumeroLigne = j
                End If
            Next

            If NumeroLigne <= dsCommande.Tables("COMMANDE").Rows.Count - 1 Then
                DataRowRecherche = dsCommande.Tables("COMMANDE").Rows(NumeroLigne)

                '------------------------------------ chargement des informations entête

                lNumeroCommande.Text = dsCommande.Tables("COMMANDE").Rows(NumeroLigne)("NumeroCommande")
                lDateCommande.Text = dsCommande.Tables("COMMANDE").Rows(NumeroLigne)("Date")
                cmbFournisseur.SelectedValue = dsCommande.Tables("COMMANDE").Rows(NumeroLigne)("CodeFournisseur")
                tNumeroBlFact.Value = dsCommande.Tables("COMMANDE").Rows(NumeroLigne)("NumeroFacture")

                TotalTTCCommande = dsCommande.Tables("COMMANDE").Rows(NumeroLigne)("TotalTTC")
                TotalHTCommande = dsCommande.Tables("COMMANDE").Rows(NumeroLigne)("TotalHT")
                lTotalTTCAchat.Text = Math.Round(TotalTTCCommande, 3)
                lTotHTAchat.Text = TotalHTCommande.ToString

                lTotalTVA.Text = dsCommande.Tables("COMMANDE").Rows(NumeroLigne)("TotalTVA")

                lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsCommande.Tables("COMMANDE").Rows(NumeroLigne)("CodePersonnel"))

                NumeroCommande = DataRowRecherche.Item("NumeroCommande")
                ChargerDetails()

            End If

        End If

    End Sub

    Private Sub cmbFournisseur_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbFournisseur.KeyDown
        If e.KeyCode = Keys.Enter Then
            cmbFournisseur.Text = cmbFournisseur.WillChangeToText
            gArticles.Focus()
        Else
            cmbFournisseur.OpenCombo()
        End If
    End Sub

    Private Sub cmbFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbFournisseur.KeyUp

    End Sub

    Public Sub AfficherFicheArticle(ByVal CodeArticle, ByVal StockArticle, ByVal Designation)
        Dim MyFicheArticle As New fFicheArticle

        MyFicheArticle.CodeArticle = CodeArticle
        MyFicheArticle.StockArticle = StockArticle
        MyFicheArticle.DesignationArticle = Designation
        MyFicheArticle.ajoutmodif = "M"

        MyFicheArticle.Init()
        MyFicheArticle.ShowDialog()
        MyFicheArticle.Close()
        MyFicheArticle.Dispose()
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub AfficherStatistique(ByVal CodeArticle As String)

        Dim i As Integer = 0
        Dim mois As String = ""

        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim CodeAbarre As String = ""

        CodeAbarre = RecupererValeurExecuteScalaire("CodeABarre", "ARTICLE", "CodeArticle", CodeArticle)

        If (dsCommande.Tables.IndexOf("STATISTIQUE_ARTICLE") > -1) Then
            dsCommande.Tables("STATISTIQUE_ARTICLE").Clear()
        End If
        If (dsCommande.Tables.IndexOf("STATISTIQUE_ARTICLE_PREC") > -1) Then
            dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Clear()
        End If

        InitialiserStatistique()

        lAnDernier.Text = System.DateTime.Now.Year
        lAnAvantDernier.Text = DateAdd(DateInterval.Year, -1, System.DateTime.Now).Year

        '********************** récupération des valeurs de vente de 12 mois pour les 2 ans 

        StrSQL = "SELECT " + _
                    "	MOIS, " + _
                    "	CONVERT(INT, SUM(Qte)) AS Qte " + _
                    "FROM " + _
                    "	(SELECT  " + _
                    "		MONTH (date) AS MOIS, " + _
                    "		SUM(Qte) AS QTE " + _
                    "	FROM  " + _
                    "		VENTE_DETAILS  " + _
                    "		LEFT OUTER JOIN VENTE ON VENTE .NumeroVente =VENTE_DETAILS .NumeroVente " + _
                    "	WHERE " + _
                    "       CodeArticle=" + Quote(CodeArticle) + _
                    "		AND YEAR (date)=YEAR(getdate())   " + _
                    "	GROUP BY MONTH (date) " + _
                    " " + _
                    "	UNION " + _
                    " " + _
                    "	SELECT  " + _
                    "		Mois AS MOIS, " + _
                    "		SUM(Qte) AS QTE " + _
                    "	FROM  " + _
                    "		STATISTIQUE_ARTICLE " + _
                    "	WHERE " + _
                    "       CodeArticle=" + Quote(CodeArticle) + _
                    "		AND Annee = YEAR(getdate())  " + _
                    "	GROUP BY Mois) AS Stat " + _
                    "GROUP BY " + _
                    "	MOIS "
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        daRecupereNum = New SqlDataAdapter(cmdRecupereNum)
        daRecupereNum.Fill(dsCommande, "STATISTIQUE_ARTICLE")


        StrSQL = "SELECT " + _
                    "	MOIS, " + _
                    "	CONVERT(INT, SUM(Qte)) AS Qte " + _
                    "FROM " + _
                    "	(SELECT  " + _
                    "		MONTH (date) AS MOIS, " + _
                    "		SUM(Qte) AS QTE " + _
                    "	FROM  " + _
                    "		VENTE_DETAILS  " + _
                    "		LEFT OUTER JOIN VENTE ON VENTE .NumeroVente =VENTE_DETAILS .NumeroVente " + _
                    "	WHERE " + _
                    "       CodeArticle=" + Quote(CodeArticle) + _
                    "		AND YEAR (date)=YEAR(getdate()) - 1   " + _
                    "	GROUP BY MONTH (date) " + _
                    " " + _
                    "	UNION " + _
                    " " + _
                    "	SELECT  " + _
                    "		Mois AS MOIS, " + _
                    "		SUM(Qte) AS QTE " + _
                    "	FROM  " + _
                    "		STATISTIQUE_ARTICLE " + _
                    "	WHERE " + _
                    "       CodeArticle=" + Quote(CodeArticle) + _
                    "		AND Annee = YEAR(getdate()) - 1  " + _
                    "	GROUP BY Mois) AS Stat " + _
                    "GROUP BY " + _
                    "	MOIS "
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        daRecupereNum = New SqlDataAdapter(cmdRecupereNum)
        daRecupereNum.Fill(dsCommande, "STATISTIQUE_ARTICLE_PREC")


        For i = 0 To dsCommande.Tables("STATISTIQUE_ARTICLE").Rows.Count - 1
            mois = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("Mois").ToString
            If mois <> "" Then
                If mois = "1" Then
                    lMois1.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "2" Then
                    lMois2.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "3" Then
                    lMois3.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "4" Then
                    lMois4.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "5" Then
                    lMois5.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "6" Then
                    lMois6.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "7" Then
                    lMois7.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "8" Then
                    lMois8.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "9" Then
                    lMois9.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "10" Then
                    lMois10.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "11" Then
                    lMois11.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If
                If mois = "12" Then
                    lMois12.Text = dsCommande.Tables("STATISTIQUE_ARTICLE").Rows(i).Item("QTE").ToString
                End If

            End If
        Next

        For i = 0 To dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows.Count - 1
            mois = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("Mois").ToString
            If mois <> "" Then
                If mois = "1" Then
                    lMoisPrec1.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "2" Then
                    lMoisPrec2.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "3" Then
                    lMoisPrec3.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "4" Then
                    lMoisPrec4.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "5" Then
                    lMoisPrec5.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "6" Then
                    lMoisPrec6.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "7" Then
                    lMoisPrec7.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "8" Then
                    lMoisPrec8.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "9" Then
                    lMoisPrec9.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "10" Then
                    lMoisPrec10.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "11" Then
                    lMoisPrec11.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If
                If mois = "12" Then
                    lMoisPrec12.Text = dsCommande.Tables("STATISTIQUE_ARTICLE_PREC").Rows(i).Item("QTE").ToString
                End If

            End If
        Next

        If lAnAvantDernier.Text = "2011" Then

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=1 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec1.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=2 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec2.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=3 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec3.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=4 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec4.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=5 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec5.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=6 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec6.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=7 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec7.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=8 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec8.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=9 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec9.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=10 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec10.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=11 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec11.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQL = "SELECT Qte FROM STATISTIQUE_ARTICLE2011 WHERE CodeArticle='" + CodeAbarre + "' AND Annee=2011 and Mois=12 "
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                lMoisPrec12.Text = CInt(CmdCalcul.ExecuteScalar().ToString)
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

        End If

        LSum1.Text = Convert.ToInt32(lMois1.Text) + lMois2.Text + lMois3.Text + lMois4.Text + lMois5.Text + lMois6.Text + lMois7.Text + lMois8.Text + lMois9.Text + lMois10.Text + lMois11.Text + lMois12.Text
        LSum2.Text = Convert.ToInt32(lMoisPrec1.Text) + lMoisPrec2.Text + lMoisPrec3.Text + lMoisPrec4.Text + lMoisPrec5.Text + lMoisPrec6.Text + lMoisPrec7.Text + lMoisPrec8.Text + lMoisPrec9.Text + lMoisPrec10.Text + lMoisPrec11.Text + lMoisPrec12.Text

    End Sub

    Private Sub gArticles_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gArticles.MouseClick
        AfficherStatistique(gArticles(gArticles.Row, "CodeArticle"))

        If mode <> "Ajout" Then
            Exit Sub
        End If

        Try
            dsCommande.Tables("COMMANDE_DETAILS").AcceptChanges()
        Catch
        End Try

        '****************************************************************************************
        '********************** verouillage des lignes déja confirmées **************************
        '****************************************************************************************
        If gArticles.Row <> dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        ElseIf gArticles.Row = dsCommande.Tables("COMMANDE_DETAILS").Rows.Count - 1 Then
            If gArticles(gArticles.Row, "CodeArticle") = "" Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
            End If
            If gArticles(gArticles.Row, "Designation") = "" Then
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
            End If
        End If

        If gArticles.Col = 1 And gArticles.Columns("Designation").Value <> "" Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        End If
        If gArticles.Col = 2 And gArticles.Columns("CodeArticle").Value <> "" Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        End If
    End Sub

    Private Sub AfficherZoneStatiqtique()
        lAnAvantDernier.Visible = True
        lAnDernier.Visible = True

        lJanvier.Visible = True
        lMois1.Visible = True
        lMoisPrec1.Visible = True

        lFevrier.Visible = True
        lMois2.Visible = True
        lMoisPrec2.Visible = True

        lMars.Visible = True
        lMois3.Visible = True
        lMoisPrec3.Visible = True

        lAvril.Visible = True
        lMois4.Visible = True
        lMoisPrec4.Visible = True

        lMai.Visible = True
        lMois5.Visible = True
        lMoisPrec5.Visible = True

        lJuin.Visible = True
        lMois6.Visible = True
        lMoisPrec6.Visible = True

        lJuillet.Visible = True
        lMois7.Visible = True
        lMoisPrec7.Visible = True

        lAout.Visible = True
        lMois8.Visible = True
        lMoisPrec8.Visible = True

        lMois9.Visible = True
        lMoisPrec9.Visible = True
        lSeptembre.Visible = True

        lOctobre.Visible = True
        lMois10.Visible = True
        lMoisPrec10.Visible = True

        lNovembre.Visible = True
        lMois11.Visible = True
        lMoisPrec11.Visible = True

        lDecembre.Visible = True
        lMois12.Visible = True
        lMoisPrec12.Visible = True

        LSomme.Visible = True
        LSum1.Visible = True
        LSum2.Visible = True


    End Sub

    Private Sub MasqauerZoneStatiqtique()
        lAnAvantDernier.Visible = False
        lAnDernier.Visible = False

        lJanvier.Visible = False
        lMois1.Visible = False
        lMoisPrec1.Visible = False

        lFevrier.Visible = False
        lMois2.Visible = False
        lMoisPrec2.Visible = False

        lMars.Visible = False
        lMois3.Visible = False
        lMoisPrec3.Visible = False

        lAvril.Visible = False
        lMois4.Visible = False
        lMoisPrec4.Visible = False

        lMai.Visible = False
        lMois5.Visible = False
        lMoisPrec5.Visible = False

        lJuin.Visible = False
        lMois6.Visible = False
        lMoisPrec6.Visible = False

        lJuillet.Visible = False
        lMois7.Visible = False
        lMoisPrec7.Visible = False

        lAout.Visible = False
        lMois8.Visible = False
        lMoisPrec8.Visible = False

        lMois9.Visible = False
        lMoisPrec9.Visible = False
        lSeptembre.Visible = False

        lOctobre.Visible = False
        lMois10.Visible = False
        lMoisPrec10.Visible = False

        lNovembre.Visible = False
        lMois11.Visible = False
        lMoisPrec11.Visible = False

        lDecembre.Visible = False
        lMois12.Visible = False
        lMoisPrec12.Visible = False

        LSomme.Visible = False
        LSum1.Visible = False
        LSum2.Visible = False
    End Sub

    Private Sub InitialiserStatistique()
        lMois1.Text = "0"
        lMois2.Text = "0"
        lMois3.Text = "0"
        lMois4.Text = "0"
        lMois5.Text = "0"
        lMois6.Text = "0"
        lMois7.Text = "0"
        lMois8.Text = "0"
        lMois9.Text = "0"
        lMois10.Text = "0"
        lMois11.Text = "0"
        lMois12.Text = "0"
        LSum1.Text = "0"

        lMoisPrec1.Text = "0"
        lMoisPrec2.Text = "0"
        lMoisPrec3.Text = "0"
        lMoisPrec4.Text = "0"
        lMoisPrec5.Text = "0"
        lMoisPrec6.Text = "0"
        lMoisPrec7.Text = "0"
        lMoisPrec8.Text = "0"
        lMoisPrec9.Text = "0"
        lMoisPrec10.Text = "0"
        lMoisPrec11.Text = "0"
        lMoisPrec12.Text = "0"
        LSum2.Text = "0"
    End Sub

    '***************************************************************************************************
    '************************************ Cas de GROUPEE ********************************************
    '' ''    ''***************************************************************************************************
    Private Sub CommandeGroupee(Trie As String)
        'Dim DebutDeConge As Date = Convert.ToDateTime("01/07/2012")
        'Dim FinDeConge As Date = Convert.ToDateTime("15/07/2012")
        Dim NombreDeDimanche As Integer = 0
        Dim NombreDeDimancheConge As Integer = 0
        Dim NombreDesJoursDeReference As Integer = 0
        Dim cond As String = ""
        Dim i As Integer = 0

        ' crirères de selection
        'If SansManquantDepuis <> "" Then
        '    cond = cond + " AND (ARTICLE.DateAlerte >='" + SansManquantDepuis + "' OR ARTICLE.DateAlerte is null)"
        'End If
        If Section = "INTERVALLE" Then
            cond = cond + " AND Section >= " + DebutIntervalle.ToString + " AND Section <= " + FinIntervalle.ToString
        End If
        If Forme <> 0 Then
            cond = cond + " AND ARTICLE.CodeForme = " + Forme.ToString
        End If
        If Categorie <> 0 Then
            cond = cond + " AND ARTICLE.CodeCategorie = " + Categorie.ToString
        End If
        If Labo <> 0 Then
            cond = cond + " AND CodeLabo = " + Labo.ToString
        End If
        If Rayon = "RAYON" Then
            cond = cond + " AND Rayon = '" + RayonSelectionne + "'"
        End If

        If (dsCommande.Tables.IndexOf("ARTICLE_GROUPEE_PAR_QTE") > -1) Then
            dsCommande.Tables("ARTICLE_GROUPEE_PAR_QTE").Clear()
        End If

        If (dsCommande.Tables.IndexOf("ARTICLE_GROUPEE_PAR_PRIX") > -1) Then
            dsCommande.Tables("ARTICLE_GROUPEE_PAR_PRIX").Clear()
        End If

        If (dsCommande.Tables.IndexOf("ARTICLE_GROUPEE") > -1) Then
            dsCommande.Tables("ARTICLE_GROUPEE").Clear()
        End If

        If TypeCommande = "GROUPEE" Then

            Dim NombreDesArticlesSelectionneQte As Integer = 0
            Dim NombreDesArticlesSelectionnePrix As Integer = 0


            'trie du résultat final dans la table COMMANDE_DETAILS dans une autre table pour que l'affichage devient par ordre alphabétique 

            Dim view As DataView = dsCommande.Tables("COMMANDE_DETAILS").DefaultView
            'view.Sort = "Qte"

            Dim newTable As DataTable = view.ToTable '("UniqueLastNames", True, "FirstName", "LastName")
            'Chargement de la tables COMMANDE_DETAILS depuis la table newTable trié
            dsCommande.Tables("COMMANDE_DETAILS").Clear()


            StrSQL = "DECLARE @NbrArticel int = (ISNULL((SELECT count(*)  FROM   ARTICLE        LEFT OUTER join CATEGORIE on ARTICLE.CodeCategorie =CATEGORIE.CodeCategorie        LEFT OUTER join FORME_ARTICLE on ARTICLE.CodeForme =FORME_ARTICLE.CodeForme        LEFT OUTER JOIN SITUATION_ARTICLE on ARTICLE .CodeSituation =SITUATION_ARTICLE .CodeSituationArticle  WHERE 1 = 1 AND LibelleSituationArticle <>'SUSPENDU'  " + cond + " ), 0) * 20 / 100) " + _
                    "SELECT * " + _
                    "FROM (" + _
                    "   SELECT TOP(@NbrArticel) " + _
                    "       article.CodeArticle, " + _
                    "       MAX(article.CodeaBarre) AS CodeaBarre, " + _
                    "       MAX(article.Designation) AS Designation, " + _
                    "       SUM(Qte) as Qte, " + _
                    "       MAX(DatePeremptionArticles.DatePeremptionArticle) AS DatePeremption, " + _
                    "       SUM(Qte*ARTICLE.PrixVenteTTC ) as Valeur,  " + _
                    "       0 as EnCours,  " + _
                    "       MAX(LOT_ARTICLES.Stock) as Stock,  " + _
                    "       MAX(ARTICLE.QuantiteUnitaire) as QteUnitaire,  " + _
                    "       MAX(FORME_ARTICLE.LibelleForme) as LibelleForme,  " + _
                    "       MAX(ARTICLE.PrixAchatHT) as PrixAchatHT,  " + _
                    "       MAX(ARTICLE.TVA) as TVA,  " + _
                    "       MAX(ARTICLE.StockAlerte) as StockAlerte,  " + _
                    "       MAX(ARTICLE.QteACommander) as QteACommander,  " + _
                    "       CONVERT(BIT, CASE WHEN MAX(ARTICLE.CodeSituation) = '2' THEN '1' ELSE '0' END) as RUPTURE  " + _
                    "   FROM   ARTICLE " + _
                    "           INNER join [dbo].[ArticleCommandeRoupee](" + NombreDeJour.ToString + ", " + Quote(DebutPeriode.ToString) + ", " + Quote(FinPeriode.ToString) + ", " + Quote(CommandeEnCours) + ") ListeArticle ON ListeArticle.CodeArticle = ARTICLE.CodeArticle " + _
                    "           INNER JOIN (SELECT CodeArticle, SUM(LOT_ARTICLE.QteLotArticle ) AS Stock FROM LOT_ARTICLE GROUP BY CodeArticle ) AS LOT_ARTICLES ON LOT_ARTICLES.CodeArticle = ARTICLE.CodeArticle " + _
                    "           LEFT OUTER join CATEGORIE on ARTICLE.CodeCategorie =CATEGORIE.CodeCategorie " + _
                    "           LEFT OUTER join FORME_ARTICLE on ARTICLE.CodeForme =FORME_ARTICLE.CodeForme " + _
                    "           LEFT OUTER JOIN SITUATION_ARTICLE on ARTICLE .CodeSituation =SITUATION_ARTICLE .CodeSituationArticle  " + _
                    "           LEFT OUTER JOIN (Select CodeArticle, MAX(DatePeremptionArticle) AS DatePeremptionArticle from LOT_ARTICLE GROUP BY CodeArticle) AS DatePeremptionArticles ON DatePeremptionArticles.CodeArticle = Article.codearticle " + _
                    "   WHERE 1 = 1 " + _
                    cond + _
                    "   AND LibelleSituationArticle <>'SUSPENDU' " + _
                    "   GROUP BY " + _
                    "       ARTICLE.CodeArticle   " + _
                    "ORDER BY SUM(Qte) desc) AS ARTICLES ORDER BY  " + Trie


            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL
            cmdCommande.CommandTimeout = 900
            daCommande.Fill(dsCommande, "COMMANDE_DETAILS")
            cbCommande = New SqlCommandBuilder(daCommande)

            'For J = 0 To newTable.Rows.Count - 1

            '    If unite * J < (newTable.Rows.Count - 1) Then
            '        If (unite * J) <= 100 Then
            '            ProgressBar.Value = unite * J
            '        Else
            '            ProgressBar.Value = 100
            '        End If
            '    End If

            '    Application.DoEvents()

            '    DataRowRecherche = newTable.Rows(J)

            '    NouvelArticle("NumeroCommande") = NumeroCommande
            '    NouvelArticle("CodeArticle") = DataRowRecherche.Item("CodeArticle")
            '    NouvelArticle("CodeABarre") = DataRowRecherche.Item("CodeABarre")
            '    NouvelArticle("Designation") = DataRowRecherche.Item("Designation")

            '    lArticleEnCours.Text = "   " + NouvelArticle("Designation")

            '    Try
            '        'NouvelArticle("CodeForme") = DataRowRecherche.Item("CodeForme")
            '    Catch ex As Exception
            '    End Try

            '    NouvelArticle("Stock") = DataRowRecherche.Item("Stock")
            '    NouvelArticle("PrixAchatHT") = DataRowRecherche.Item("PrixAchatHT")
            '    NouvelArticle("TVA") = DataRowRecherche.Item("TVA")

            '    Try
            '        NouvelArticle("StockAlerte") = DataRowRecherche.Item("StockAlerte")
            '    Catch ex As Exception
            '        NouvelArticle("StockAlerte") = 0
            '    End Try

            '    NouvelArticle("EnCours") = DataRowRecherche.Item("EnCours")
            '    NouvelArticle("QteACommander") = DataRowRecherche.Item("QteACommander")
            '    NouvelArticle("Qte") = DataRowRecherche.Item("Qte")

            '    NouvelArticle("Qte") = DataRowRecherche.Item("Qte")

            '    NouvelArticle("TotalTTCAchat") = DataRowRecherche.Item("TotalTTCAchat")
            '    NouvelArticle("QteUnitaire") = DataRowRecherche.Item("QteUnitaire")
            '    NouvelArticle("LibelleForme") = DataRowRecherche.Item("LibelleForme")

            '    NouvelArticle("DatePeremption") = DataRowRecherche.Item("DatePeremption")
            '    NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
            '    dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)
            '    NouvelArticle("Designation") = "" 'initialisation pour que le calcul des montants ne compare pas 
            '    NouvelArticle("CodeArticle") = "" ' des chaines de caractères avec des NULL
            '    NouvelArticle("CodeABarre") = ""
            '    NouvelArticle("Qte") = 0

            'Next J



        End If



    End Sub


    '' ''    ''Commande Groupe BINZARTI
    '' ''    Private Sub CommandeGroupee()

    '' ''        Dim DebutDeConge As Date = Convert.ToDateTime("01/07/2012")
    '' ''        Dim FinDeConge As Date = Convert.ToDateTime("15/07/2012")
    '' ''        Dim NombreDeDimanche As Integer = 0
    '' ''        Dim NombreDeDimancheConge As Integer = 0
    '' ''        Dim NombreDesJoursDeReference As Integer = 0
    '' ''        Dim cond As String = ""
    '' ''        Dim i As Integer = 0

    '' ''        ' crirères de selection
    '' ''        If SansManquantDepuis <> "" Then
    '' ''            cond = cond + " AND (ARTICLE.DateAlerte >='" + SansManquantDepuis + "' OR ARTICLE.DateAlerte is null)"
    '' ''        End If
    '' ''        If Section = "INTERVALLE" Then
    '' ''            cond = cond + " AND Section >= " + DebutIntervalle.ToString + " AND Section <= " + FinIntervalle.ToString
    '' ''        End If
    '' ''        If Forme <> 0 Then
    '' ''            cond = cond + " AND ARTICLE.CodeForme = " + Forme.ToString
    '' ''        End If
    '' ''        If Categorie <> 0 Then
    '' ''            cond = cond + " AND ARTICLE.CodeCategorie = " + Categorie.ToString
    '' ''        End If
    '' ''        If Labo <> 0 Then
    '' ''            cond = cond + " AND CodeLabo = " + Labo.ToString
    '' ''        End If
    '' ''        If Rayon = "RAYON" Then
    '' ''            cond = cond + " AND Rayon = '" + RayonSelectionne + "'"
    '' ''        End If

    '' ''        If (dsCommande.Tables.IndexOf("ARTICLE_GROUPEE_PAR_QTE") > -1) Then
    '' ''            dsCommande.Tables("ARTICLE_GROUPEE_PAR_QTE").Clear()
    '' ''        End If

    '' ''        If (dsCommande.Tables.IndexOf("ARTICLE_GROUPEE_PAR_PRIX") > -1) Then
    '' ''            dsCommande.Tables("ARTICLE_GROUPEE_PAR_PRIX").Clear()
    '' ''        End If

    '' ''        If (dsCommande.Tables.IndexOf("ARTICLE_GROUPEE") > -1) Then
    '' ''            dsCommande.Tables("ARTICLE_GROUPEE").Clear()
    '' ''        End If

    '' ''        If TypeCommande = "GROUPEE" Then

    '' ''            Dim NombreDesArticlesSelectionneQte As Integer = 0
    '' ''            Dim NombreDesArticlesSelectionnePrix As Integer = 0
    '' ''            ' les articles les plus vendues(qte)
    '' ''            StrSQL = "SELECT article.CodeArticle,MAX(article.CodeaBarre) AS CodeaBarre,MAX(article.Designation) AS Designation,SUM(Qte) as Qte " + _
    '' ''                         "FROM ARTICLE " + _
    '' ''                         "left outer join VENTE_DETAILS on ARTICLE.CodeArticle=VENTE_DETAILS.CodeArticle " + _
    '' ''                         "left outer join VENTE on VENTE.NumeroVente=VENTE_DETAILS.NumeroVente " + _
    '' ''                         "left outer join CATEGORIE on ARTICLE.CodeCategorie =CATEGORIE.CodeCategorie " + _
    '' ''                         "LEFT OUTER JOIN SITUATION_ARTICLE on ARTICLE .CodeSituation =SITUATION_ARTICLE .CodeSituationArticle  " + _
    '' ''                         "where  ARTICLE.Supprime = 0 AND " + _
    '' ''                         "(select SUM(qte) from VENTE_DETAILS left outer join VENTE on VENTE_DETAILS.NumeroVente=VENTE.NumeroVente " + _
    '' ''                         "where VENTE.Date between DATEADD(day,-90,GETDATE()) and GETDATE() and VENTE_DETAILS.CodeArticle =article.CodeArticle )> 0  " + _
    '' ''                         "and vente.Date between '" + DateAdd(DateInterval.Year, -2, System.DateTime.Now) + "' and '" + System.DateTime.Now + "' " + _
    '' ''                         cond + _
    '' ''                         " AND LibelleSituationArticle <>'SUSPENDU' group by ARTICLE.CodeArticle   " + _
    '' ''                         "ORDER BY Qte DESC  "

    '' ''            cmdCommande.Connection = ConnectionServeur
    '' ''            cmdCommande.CommandText = StrSQL
    '' ''            daCommande.Fill(dsCommande, "ARTICLE_GROUPEE_PAR_QTE")
    '' ''            cbCommande = New SqlCommandBuilder(daCommande)

    '' ''            NombreDesArticlesSelectionneQte = Math.Round(dsCommande.Tables("ARTICLE_GROUPEE_PAR_QTE").Rows.Count / 1, 0)

    '' ''            ' les articles les plus chères vendues(valeur)
    '' ''            StrSQL = "SELECT article.CodeArticle,MAX(article.CodeaBarre) AS CodeaBarre,MAX(article.Designation) AS Designation,SUM(Qte) as Qte " + _
    '' ''                         ",SUM(Qte*ARTICLE.PrixVenteTTC ) as Valeur " + _
    '' ''                         "FROM ARTICLE " + _
    '' ''                         "left outer join VENTE_DETAILS on ARTICLE.CodeArticle=VENTE_DETAILS.CodeArticle " + _
    '' ''                         "left outer join VENTE on VENTE.NumeroVente=VENTE_DETAILS.NumeroVente " + _
    '' ''                         "left outer join CATEGORIE on ARTICLE.CodeCategorie =CATEGORIE.CodeCategorie " + _
    '' ''                         "LEFT OUTER JOIN SITUATION_ARTICLE on ARTICLE .CodeSituation =SITUATION_ARTICLE .CodeSituationArticle  " + _
    '' ''                         "where  ARTICLE.Supprime = 0 AND " + _
    '' ''                         "(select SUM(qte) from VENTE_DETAILS left outer join VENTE on VENTE_DETAILS.NumeroVente=VENTE.NumeroVente " + _
    '' ''                         "where VENTE.Date between DATEADD(day,-90,GETDATE()) and GETDATE() and VENTE_DETAILS.CodeArticle =article.CodeArticle )> 0  " + _
    '' ''                         "AND vente.Date between '" + DateAdd(DateInterval.Year, -2, System.DateTime.Now) + "' and '" + System.DateTime.Now + "' " + _
    '' ''                         cond + _
    '' ''                         "AND LibelleSituationArticle <>'SUSPENDU' group by ARTICLE.CodeArticle   " + _
    '' ''                         "ORDER BY Valeur DESC  "

    '' ''            cmdCommande.Connection = ConnectionServeur
    '' ''            cmdCommande.CommandText = StrSQL
    '' ''            daCommande.Fill(dsCommande, "ARTICLE_GROUPEE_PAR_PRIX")
    '' ''            cbCommande = New SqlCommandBuilder(daCommande)

    '' ''            NombreDesArticlesSelectionnePrix = Math.Round(dsCommande.Tables("ARTICLE_GROUPEE_PAR_PRIX").Rows.Count / 1, 0)

    '' ''            'Création de la table résultat vide
    '' ''            StrSQL = "SELECT TOP (0) article.CodeArticle,MAX(article.CodeaBarre) AS CodeaBarre,MAX(article.Designation) AS Designation,SUM(Qte) as Qte " + _
    '' ''                         "FROM ARTICLE " + _
    '' ''                         "left outer join VENTE_DETAILS on ARTICLE.CodeArticle=VENTE_DETAILS.CodeArticle " + _
    '' ''                         "left outer join VENTE on VENTE.NumeroVente=VENTE_DETAILS.NumeroVente " + _
    '' ''                         "where  ARTICLE.Supprime = 0 AND  vente.Date between '" + DateAdd(DateInterval.Year, -2, System.DateTime.Now) + "' and '" + System.DateTime.Now + "' " + _
    '' ''                         "group by ARTICLE.CodeArticle   " + _
    '' ''                         "ORDER BY Qte DESC  "

    '' ''            cmdCommande.Connection = ConnectionServeur
    '' ''            cmdCommande.CommandText = StrSQL
    '' ''            daCommande.Fill(dsCommande, "ARTICLE_GROUPEE")
    '' ''            cbCommande = New SqlCommandBuilder(daCommande)

    '' ''            i = 0
    '' ''            Dim drResultat As DataRow = Nothing
    '' ''            'Dim QteVendu90JoursAvant As Integer = 0
    '' ''            drResultat = dsCommande.Tables("ARTICLE_GROUPEE").NewRow()

    '' ''            'ajout de la liste trie par qte dans la liste résultat 
    '' ''            For i = 0 To NombreDesArticlesSelectionneQte - 1

    '' ''                drResultat("CodeArticle") = dsCommande.Tables("ARTICLE_GROUPEE_PAR_QTE").Rows(i).Item("CodeArticle")
    '' ''                drResultat("CodeaBarre") = dsCommande.Tables("ARTICLE_GROUPEE_PAR_QTE").Rows(i).Item("CodeaBarre")
    '' ''                drResultat("Designation") = dsCommande.Tables("ARTICLE_GROUPEE_PAR_QTE").Rows(i).Item("Designation")
    '' ''                drResultat("Qte") = dsCommande.Tables("ARTICLE_GROUPEE_PAR_QTE").Rows(i).Item("Qte")

    '' ''                dsCommande.Tables("ARTICLE_GROUPEE").Rows.Add(drResultat)
    '' ''                drResultat = dsCommande.Tables("ARTICLE_GROUPEE").NewRow()

    '' ''            Next

    '' ''            'ajout de la liste trie par valeur dans la liste resultat 
    '' ''            Dim k As Integer = 0
    '' ''            Dim Existe As Boolean = False
    '' ''            For i = 0 To NombreDesArticlesSelectionneQte - 1

    '' ''                For k = 0 To dsCommande.Tables("ARTICLE_GROUPEE").Rows.Count - 1
    '' ''                    If dsCommande.Tables("ARTICLE_GROUPEE").Rows(k).Item("CodeArticle") = dsCommande.Tables("ARTICLE_GROUPEE_PAR_PRIX").Rows(i).Item("CodeArticle") Then
    '' ''                        Existe = True
    '' ''                        GoTo Terminer
    '' ''                    End If
    '' ''                Next

    '' ''                If Existe = False Then     ' AND dsCommande.Tables("ARTICLE_GROUPEE_PAR_PRIX").Rows(i).Item("Qte") > 20 

    '' ''                    drResultat("CodeArticle") = dsCommande.Tables("ARTICLE_GROUPEE_PAR_PRIX").Rows(i).Item("CodeArticle")
    '' ''                    drResultat("CodeaBarre") = dsCommande.Tables("ARTICLE_GROUPEE_PAR_PRIX").Rows(i).Item("CodeaBarre")
    '' ''                    drResultat("Designation") = dsCommande.Tables("ARTICLE_GROUPEE_PAR_PRIX").Rows(i).Item("Designation")
    '' ''                    drResultat("Qte") = dsCommande.Tables("ARTICLE_GROUPEE_PAR_PRIX").Rows(i).Item("Qte")

    '' ''                    dsCommande.Tables("ARTICLE_GROUPEE").Rows.Add(drResultat)
    '' ''                    drResultat = dsCommande.Tables("ARTICLE_GROUPEE").NewRow()

    '' ''                End If
    '' ''terminer:
    '' ''                Existe = False
    '' ''            Next

    '' ''            If dsCommande.Tables("ARTICLE_GROUPEE").Columns.Count < 5 Then
    '' ''                Dim QtaniteDecimal As DataColumn = dsCommande.Tables("ARTICLE_GROUPEE").Columns.Add("QteDecimale", Type.GetType("System.Decimal"))
    '' ''            End If

    '' ''            'division de la quantite de chaque article par 730 : le nombre des jours de deux années 
    '' ''            For i = 0 To dsCommande.Tables("ARTICLE_GROUPEE").Rows.Count - 1
    '' ''                Try
    '' ''                    dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") = dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") / 730
    '' ''                Catch ex As Exception
    '' ''                End Try
    '' ''            Next
    '' ''INITIALE:
    '' ''            For i = 0 To dsCommande.Tables("ARTICLE_GROUPEE").Rows.Count - 1
    '' ''                'élimination des quantités négatives ou zéro et les articles qui ont une moyenne de vente inferieur a 1 par mois cad 0.032 par jour 
    '' ''                If dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") <= 0.032 Or IsDBNull(dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale")) = True Or Trim(dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale").ToString) = "" Then
    '' ''                    dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Delete()
    '' ''                    GoTo INITIALE
    '' ''                End If
    '' ''            Next

    '' ''            'calcul de la quantité vendu pendant la période de l'an dernier pour voir si on a un pic de vente ou nn

    '' ''            Dim JourMoisDebut As String = ""
    '' ''            Dim JourMoisFin As String = ""
    '' ''            Dim AnneeDebut As String = ""
    '' ''            Dim AnneeFin As String = ""
    '' ''            Dim QteVenduePeriode As Decimal = 0.0
    '' ''            Dim QteVendue30DernierJour As Decimal = 0.0
    '' ''            Dim NombreDeJourPeriode As Integer = 1
    '' ''            Dim QteEnStock As Integer = 0

    '' ''            Dim moismoins1 As Integer
    '' ''            Dim yearmoin1 As Integer

    '' ''            If Reference = "PERIODE" Then
    '' ''                '---------------------------- Cas ou on a une période
    '' ''                JourMoisDebut = DebutPeriode.Substring(0, 6)
    '' ''                AnneeDebut = DebutPeriode.Substring(6, 4)
    '' ''                JourMoisFin = FinPeriode.Substring(0, 6)
    '' ''                AnneeFin = FinPeriode.Substring(6, 4)

    '' ''                NombreDeJourPeriode = DateDiff(DateInterval.Day, Convert.ToDateTime(DebutPeriode), Convert.ToDateTime(FinPeriode))

    '' ''                If NombreDeJourPeriode = 0 Then
    '' ''                    NombreDeJourPeriode = 1
    '' ''                End If
    '' ''                For i = 0 To dsCommande.Tables("ARTICLE_GROUPEE").Rows.Count - 1
    '' ''                    ' calcul de la quantite vendu pendant la periode de l'an dernier 
    '' ''                    StrSQL = "SELECT SUM(Qte) " + _
    '' ''                         "FROM VENTE_DETAILS " + _
    '' ''                         "left outer join VENTE on VENTE.NumeroVente=VENTE_DETAILS.NumeroVente " + _
    '' ''                         "where vente.Date between '" + JourMoisDebut + (Convert.ToInt32(AnneeDebut) - 1).ToString + _
    '' ''                         "' and '" + JourMoisFin + (Convert.ToInt32(AnneeFin) - 1).ToString + "' " + _
    '' ''                         " AND CodeArticle='" + dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("CodeArticle") + "'"

    '' ''                    cmdCommande.Connection = ConnectionServeur
    '' ''                    cmdCommande.CommandText = StrSQL
    '' ''                    Try
    '' ''                        QteVenduePeriode = cmdCommande.ExecuteScalar
    '' ''                    Catch ex As Exception
    '' ''                        QteVenduePeriode = 0
    '' ''                    End Try

    '' ''                    If QteVenduePeriode > dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") * NombreDeJourPeriode Then
    '' ''                        dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = Math.Round((((dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") * NombreDeJourPeriode) + QteVenduePeriode) / 2) / NombreDeJourPeriode * NombreDeJour)
    '' ''                    ElseIf QteVenduePeriode / dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") * NombreDeJourPeriode > 0.75 Then
    '' ''                        dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = Math.Round((((dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") * NombreDeJourPeriode) + QteVenduePeriode) / 2) / NombreDeJourPeriode * NombreDeJour)
    '' ''                    Else
    '' ''                        ' calcul de la quantite vendu pendant les 30 jours derniers
    '' ''                        StrSQL = "SELECT SUM(Qte) " + _
    '' ''                             "FROM VENTE_DETAILS " + _
    '' ''                             "left outer join VENTE on VENTE.NumeroVente=VENTE_DETAILS.NumeroVente " + _
    '' ''                             "where vente.Date between '" + DateAdd(DateInterval.Day, -30, System.DateTime.Now).ToString + _
    '' ''                             "' and '" + System.DateTime.Now + "' " + _
    '' ''                             " AND CodeArticle='" + dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("CodeArticle") + "'"

    '' ''                        cmdCommande.Connection = ConnectionServeur
    '' ''                        cmdCommande.CommandText = StrSQL
    '' ''                        Try
    '' ''                            QteVendue30DernierJour = cmdCommande.ExecuteScalar
    '' ''                        Catch ex As Exception
    '' ''                            QteVenduePeriode = 0
    '' ''                        End Try

    '' ''                        If (QteVendue30DernierJour / 30) * 2 < dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") Then
    '' ''                            ' ici je récupère la moyenne vendu pour la même période demandé mais pour l'an dernier 
    '' ''                            'l avenir dans l'année -1  qui est  celui la quantité demandé 
    '' ''                            StrSQL = "SELECT SUM(Qte) " + _
    '' ''                             "FROM VENTE_DETAILS " + _
    '' ''                             "left outer join VENTE on VENTE.NumeroVente=VENTE_DETAILS.NumeroVente " + _
    '' ''                             "where vente.Date between '" + DateAdd(DateInterval.Year, -1, System.DateTime.Now).ToString + _
    '' ''                             "' and '" + DateAdd(DateInterval.Day, NombreDeJour, DateAdd(DateInterval.Year, -1, System.DateTime.Now)).ToString + "' " + _
    '' ''                             " AND CodeArticle='" + dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("CodeArticle") + "'"

    '' ''                            cmdCommande.Connection = ConnectionServeur
    '' ''                            cmdCommande.CommandText = StrSQL
    '' ''                            Try
    '' ''                                dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = cmdCommande.ExecuteScalar '/ NombreDeJour
    '' ''                            Catch ex As Exception
    '' ''                                dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = 0
    '' ''                            End Try
    '' ''                        End If

    '' ''                        If (QteVendue30DernierJour / 30) > 2 * dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") Then
    '' ''                            dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") * NombreDeJour
    '' ''                        Else
    '' ''                            'dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = Math.Round((((dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") * NombreDeJourPeriode) + QteVendue30DernierJour / 30 * NombreDeJourPeriode) / 2) / NombreDeJourPeriode * NombreDeJour)
    '' ''                            dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = Math.Round(((dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") + QteVendue30DernierJour / 30) / 2) * NombreDeJour)
    '' ''                        End If

    '' ''                    End If
    '' ''                    'on retranche la quantité existante en stock 
    '' ''                    StrSQL = "SELECT SUM(QteLotArticle) " + _
    '' ''                        "FROM LOT_ARTICLE " + _
    '' ''                        "where LOT_ARTICLE.CodeArticle = '" + dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("CodeArticle") + _
    '' ''                        "' and DatePeremptionArticle>'" + System.DateTime.Now + "' "

    '' ''                    cmdCommande.Connection = ConnectionServeur
    '' ''                    cmdCommande.CommandText = StrSQL
    '' ''                    Try
    '' ''                        QteEnStock = cmdCommande.ExecuteScalar
    '' ''                    Catch ex As Exception
    '' ''                        QteEnStock = 0
    '' ''                    End Try

    '' ''                    dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") - QteEnStock
    '' ''                    If dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") < 0 Then
    '' ''                        dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = 0
    '' ''                    End If
    '' ''                Next

    '' ''            ElseIf Reference = "MOIS" Then

    '' ''                NombreDeJourPeriode = NombreDesJoursDuMois(mois, (Convert.ToInt32(System.DateTime.Now.Year) - 1))

    '' ''                For i = 0 To dsCommande.Tables("ARTICLE_GROUPEE").Rows.Count - 1
    '' ''                    ' calcul de la quantite vendu pendant la periode de l'an dernier 
    '' ''                    ' ''StrSQL = "SELECT SUM(Qte) " + _
    '' ''                    ' ''     "FROM VENTE_DETAILS " + _
    '' ''                    ' ''     "LEFT OUTER JOIN VENTE on VENTE.NumeroVente=VENTE_DETAILS.NumeroVente " + _
    '' ''                    ' ''     "WHERE MONTH(VENTE.Date)=" + mois.ToString + _
    '' ''                    ' ''     " AND YEAR(VENTE.Date)= " + (Convert.ToInt32(System.DateTime.Now.Year) - 1).ToString + " " + _
    '' ''                    ' ''     " AND CodeArticle='" + dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("CodeArticle") + "'"

    '' ''                    If mois = 1 Then
    '' ''                        moismoins1 = 12
    '' ''                        yearmoin1 = Year(Date.Now) - 1
    '' ''                    Else
    '' ''                        moismoins1 = mois - 1
    '' ''                        yearmoin1 = Year(Date.Now)
    '' ''                    End If

    '' ''                    StrSQL = "SELECT SUM(Qte) " + _
    '' ''                        "FROM VENTE_DETAILS " + _
    '' ''                        "LEFT OUTER JOIN VENTE on VENTE.NumeroVente=VENTE_DETAILS.NumeroVente " + _
    '' ''                        "WHERE MONTH(VENTE.Date)= " + moismoins1.ToString + _
    '' ''                        " AND YEAR(VENTE.Date)= " + yearmoin1.ToString + " " + _
    '' ''                        " AND CodeArticle='" + dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("CodeArticle") + "'"

    '' ''                    cmdCommande.Connection = ConnectionServeur
    '' ''                    cmdCommande.CommandText = StrSQL
    '' ''                    Try
    '' ''                        QteVenduePeriode = cmdCommande.ExecuteScalar
    '' ''                    Catch ex As Exception
    '' ''                        QteVenduePeriode = 0
    '' ''                    End Try

    '' ''                    '' ''If QteVenduePeriode > dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") * NombreDeJourPeriode Then
    '' ''                    '' ''    dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = Math.Round((((dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") * NombreDeJourPeriode) + QteVenduePeriode) / 2) / NombreDeJourPeriode * NombreDeJour)
    '' ''                    '' ''ElseIf QteVenduePeriode / (dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") * NombreDeJourPeriode) > 0.75 Then
    '' ''                    '' ''    dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = Math.Round((((dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") * NombreDeJourPeriode) + QteVenduePeriode) / 2) / NombreDeJourPeriode * NombreDeJour)
    '' ''                    '' ''Else
    '' ''                    '' ''    ' calcul de la quantite vendu pendant les 30 jours derniers
    '' ''                    '' ''    StrSQL = "SELECT SUM(Qte) " + _
    '' ''                    '' ''         "FROM VENTE_DETAILS " + _
    '' ''                    '' ''         "left outer join VENTE on VENTE.NumeroVente=VENTE_DETAILS.NumeroVente " + _
    '' ''                    '' ''         "where vente.Date between '" + DateAdd(DateInterval.Day, -30, System.DateTime.Now).ToString + _
    '' ''                    '' ''         "' and '" + System.DateTime.Now + "' " + _
    '' ''                    '' ''         " AND CodeArticle='" + dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("CodeArticle") + "'"

    '' ''                    '' ''    cmdCommande.Connection = ConnectionServeur
    '' ''                    '' ''    cmdCommande.CommandText = StrSQL
    '' ''                    '' ''    Try
    '' ''                    '' ''        QteVendue30DernierJour = cmdCommande.ExecuteScalar
    '' ''                    '' ''    Catch ex As Exception
    '' ''                    '' ''        QteVendue30DernierJour = 0
    '' ''                    '' ''    End Try

    '' ''                    '' ''    If (QteVendue30DernierJour / 30) * 2 < dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") Then
    '' ''                    '' ''        ' ici je récupère la moyenne vendu pour la même période demandé mais pour l an dernier 
    '' ''                    '' ''        'l avenir dans l'année -1  qui est  celui la quantité demandé 
    '' ''                    '' ''        StrSQL = "SELECT SUM(Qte) " + _
    '' ''                    '' ''         "FROM VENTE_DETAILS " + _
    '' ''                    '' ''         "left outer join VENTE on VENTE.NumeroVente=VENTE_DETAILS.NumeroVente " + _
    '' ''                    '' ''         "where vente.Date between '" + DateAdd(DateInterval.Year, -1, System.DateTime.Now).ToString + _
    '' ''                    '' ''         "' and '" + DateAdd(DateInterval.Day, NombreDeJour, DateAdd(DateInterval.Year, -1, System.DateTime.Now)).ToString + "' " + _
    '' ''                    '' ''         " AND CodeArticle='" + dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("CodeArticle") + "'"

    '' ''                    '' ''        cmdCommande.Connection = ConnectionServeur
    '' ''                    '' ''        cmdCommande.CommandText = StrSQL
    '' ''                    '' ''        Try
    '' ''                    '' ''            dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = cmdCommande.ExecuteScalar '/ NombreDeJour
    '' ''                    '' ''        Catch ex As Exception
    '' ''                    '' ''            dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = 0
    '' ''                    '' ''        End Try
    '' ''                    '' ''    End If

    '' ''                    '' ''    If (QteVendue30DernierJour / 30) > 2 * dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") Then
    '' ''                    '' ''        dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") * NombreDeJour
    '' ''                    '' ''    Else
    '' ''                    '' ''        'dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = Math.Round((((dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") * NombreDeJourPeriode) + QteVendue30DernierJour / 30 * NombreDeJourPeriode) / 2) / NombreDeJourPeriode * NombreDeJour)
    '' ''                    '' ''        dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = Math.Round(((dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("QteDecimale") + QteVendue30DernierJour / 30) / 2) * NombreDeJour)
    '' ''                    '' ''    End If

    '' ''                    '' ''End If

    '' ''                    dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = QteVenduePeriode

    '' ''                    'on retranche la quentité existante en stock 
    '' ''                    StrSQL = "SELECT SUM(QteLotArticle) " + _
    '' ''                        "FROM LOT_ARTICLE " + _
    '' ''                        "where LOT_ARTICLE.CodeArticle = '" + dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("CodeArticle") + "'"
    '' ''                    '"' and DatePeremptionArticle>'" + System.DateTime.Now + "' "

    '' ''                    cmdCommande.Connection = ConnectionServeur
    '' ''                    cmdCommande.CommandText = StrSQL
    '' ''                    Try
    '' ''                        QteEnStock = cmdCommande.ExecuteScalar
    '' ''                    Catch ex As Exception
    '' ''                        QteEnStock = 0
    '' ''                    End Try


    '' ''                    dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") - QteEnStock
    '' ''                    If dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") < 0 Then
    '' ''                        dsCommande.Tables("ARTICLE_GROUPEE").Rows(i).Item("Qte") = 0
    '' ''                    End If
    '' ''                Next
    '' ''            End If

    '' ''            '-------------------------- chargement de la tables COMMANDE_DETAILS 
    '' ''            '---------------------------------------------------------------------------

    '' ''            Dim J As Integer
    '' ''            Dim DataRowRecherche As DataRow = Nothing
    '' ''            Dim DatePeremption As Date

    '' ''            ProgressBar.Visible = True
    '' ''            GroupeJauge.Visible = True
    '' ''            ProgressBar.Value = 0

    '' ''            unite = 100 / (dsCommande.Tables("ARTICLE_GROUPEE").Rows.Count - 1)

    '' ''            'Arrêter la capture d'evenement clavier sur le contrôle 
    '' ''            RemoveHandler Me.bAjouter.Click, AddressOf Me.bAjouter_Click
    '' ''            ' pour empecher l'annulation : elle cause des problemes il faut 
    '' ''            ' attendre la fin de l'execusion
    '' ''            bAnnuler.Enabled = False

    '' ''            For J = 0 To dsCommande.Tables("ARTICLE_GROUPEE").Rows.Count - 1

    '' ''                If unite * J < (dsCommande.Tables("ARTICLE_GROUPEE").Rows.Count - 1) Then
    '' ''                    If (unite * J) <= 100 Then
    '' ''                        ProgressBar.Value = unite * J
    '' ''                    Else
    '' ''                        ProgressBar.Value = 100
    '' ''                    End If
    '' ''                End If

    '' ''                Application.DoEvents()

    '' ''                DataRowRecherche = dsCommande.Tables("ARTICLE_GROUPEE").Rows(J)

    '' ''                NouvelArticle("NumeroCommande") = NumeroCommande
    '' ''                NouvelArticle("CodeArticle") = DataRowRecherche.Item("CodeArticle")
    '' ''                NouvelArticle("CodeABarre") = DataRowRecherche.Item("CodeABarre")
    '' ''                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

    '' ''                lArticleEnCours.Text = "   " + NouvelArticle("Designation")

    '' ''                Try
    '' ''                    'NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
    '' ''                Catch ex As Exception
    '' ''                End Try

    '' ''                NouvelArticle("Stock") = CalculeStock(NouvelArticle("CodeArticle"))
    '' ''                NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
    '' ''                NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

    '' ''                Try
    '' ''                    NouvelArticle("StockAlerte") = RecupererValeurExecuteScalaire("StockAlerte", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
    '' ''                Catch ex As Exception
    '' ''                    NouvelArticle("StockAlerte") = 0
    '' ''                End Try

    '' ''                NouvelArticle("EnCours") = CalculerEnCours(NouvelArticle("CodeArticle"))
    '' ''                NouvelArticle("QteACommander") = RecupererValeurExecuteScalaire("QteACommander", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
    '' ''                NouvelArticle("Qte") = DataRowRecherche.Item("Qte") 'Int((DataRowRecherche.Item("QTE") / NombreDesJoursDeReference) * NombreDeJour)

    '' ''                If CommandeEnCours = True Then
    '' ''                    NouvelArticle("Qte") = NouvelArticle("Qte") - CalculerEnCours(NouvelArticle("CodeArticle"))
    '' ''                    If NouvelArticle("Qte") < 0 Then
    '' ''                        NouvelArticle("Qte") = 0
    '' ''                    End If
    '' ''                End If

    '' ''                NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
    '' ''                NouvelArticle("QteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
    '' ''                'NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", "FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

    '' ''                '------------------ récupération de la date de péremption

    '' ''                StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
    '' ''                System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
    '' ''                NouvelArticle("CodeArticle") + "' Order by DatePeremptionArticle DESC "

    '' ''                cmdCommande.Connection = ConnectionServeur
    '' ''                cmdCommande.CommandText = StrSQL

    '' ''                Try
    '' ''                    DatePeremption = cmdCommande.ExecuteScalar()
    '' ''                Catch ex As Exception
    '' ''                    Console.WriteLine(ex.Message)
    '' ''                End Try

    '' ''                If DatePeremption = #12:00:00 AM# Then
    '' ''                    'NouvelArticle("DatePeremption") = "01/01/1900"
    '' ''                Else
    '' ''                    NouvelArticle("DatePeremption") = DatePeremption
    '' ''                End If

    '' ''                NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
    '' ''                dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)
    '' ''                NouvelArticle("Designation") = "" 'initialisation pour que le calcul des montants ne compare pas 
    '' ''                NouvelArticle("CodeArticle") = "" ' des chaines de caractères avec des NULL
    '' ''                NouvelArticle("CodeABarre") = ""
    '' ''                NouvelArticle("Qte") = 0

    '' ''            Next J

    '' ''            bAnnuler.Enabled = True
    '' ''            'Reprendre la capture d'evenement clavier sur le contrôle 
    '' ''            AddHandler Me.bAjouter.Click, AddressOf Me.bAjouter_Click


    '' ''            ProgressBar.Value = 100
    '' ''            ProgressBar.Visible = False
    '' ''            GroupeJauge.Visible = False

    '' ''            cmbType.Text = "GROUPEE"
    '' ''            cmbType.Enabled = False
    '' ''            CalculerMontants()

    '' ''            'trie du résultat final dans la table COMMANDE_DETAILS dans une autre table pour que l'affichage devient par ordre alphabétique 

    '' ''            Dim view As DataView = dsCommande.Tables("COMMANDE_DETAILS").DefaultView
    '' ''            view.Sort = "Designation"

    '' ''            Dim newTable As DataTable = view.ToTable '("UniqueLastNames", True, "FirstName", "LastName")
    '' ''            'Chargement de la tables COMMANDE_DETAILS depuis la table newTable trié
    '' ''            dsCommande.Tables("COMMANDE_DETAILS").Clear()
    '' ''            'Dim J As Integer
    '' ''            'Dim DataRowRecherche As DataRow = Nothing

    '' ''            For J = 0 To newTable.Rows.Count - 1

    '' ''                If unite * J < (newTable.Rows.Count - 1) Then
    '' ''                    If (unite * J) <= 100 Then
    '' ''                        ProgressBar.Value = unite * J
    '' ''                    Else
    '' ''                        ProgressBar.Value = 100
    '' ''                    End If
    '' ''                End If

    '' ''                Application.DoEvents()

    '' ''                DataRowRecherche = newTable.Rows(J)

    '' ''                NouvelArticle("NumeroCommande") = NumeroCommande
    '' ''                NouvelArticle("CodeArticle") = DataRowRecherche.Item("CodeArticle")
    '' ''                NouvelArticle("CodeABarre") = DataRowRecherche.Item("CodeABarre")
    '' ''                NouvelArticle("Designation") = DataRowRecherche.Item("Designation")

    '' ''                lArticleEnCours.Text = "   " + NouvelArticle("Designation")

    '' ''                Try
    '' ''                    'NouvelArticle("CodeForme") = DataRowRecherche.Item("CodeForme")
    '' ''                Catch ex As Exception
    '' ''                End Try

    '' ''                NouvelArticle("Stock") = DataRowRecherche.Item("Stock")
    '' ''                NouvelArticle("PrixAchatHT") = DataRowRecherche.Item("PrixAchatHT")
    '' ''                NouvelArticle("TVA") = DataRowRecherche.Item("TVA")

    '' ''                Try
    '' ''                    NouvelArticle("StockAlerte") = DataRowRecherche.Item("StockAlerte")
    '' ''                Catch ex As Exception
    '' ''                    NouvelArticle("StockAlerte") = 0
    '' ''                End Try

    '' ''                NouvelArticle("EnCours") = DataRowRecherche.Item("EnCours")
    '' ''                NouvelArticle("QteACommander") = DataRowRecherche.Item("QteACommander")
    '' ''                NouvelArticle("Qte") = DataRowRecherche.Item("Qte")

    '' ''                NouvelArticle("Qte") = DataRowRecherche.Item("Qte")

    '' ''                NouvelArticle("TotalTTCAchat") = DataRowRecherche.Item("TotalTTCAchat")
    '' ''                NouvelArticle("QteUnitaire") = DataRowRecherche.Item("QteUnitaire")
    '' ''                NouvelArticle("LibelleForme") = DataRowRecherche.Item("LibelleForme")

    '' ''                NouvelArticle("DatePeremption") = DataRowRecherche.Item("DatePeremption")
    '' ''                NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
    '' ''                dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)
    '' ''                NouvelArticle("Designation") = "" 'initialisation pour que le calcul des montants ne compare pas 
    '' ''                NouvelArticle("CodeArticle") = "" ' des chaines de caractères avec des NULL
    '' ''                NouvelArticle("CodeABarre") = ""
    '' ''                NouvelArticle("Qte") = 0

    '' ''            Next J



    '' ''        End If

    '' ''    End Sub

    '***************************************************************************************************
    '************************************ Cas de HIT PARADE ********************************************
    '***************************************************************************************************

    Private Sub CommandeInstantanee(Trie As String)

        If (dsCommande.Tables.IndexOf("ARTICLE_JOURNALIERE") > -1) Then
            dsCommande.Tables("ARTICLE_JOURNALIERE").Clear()
        End If

        StrSQL = "SELECT " + _
                 "  ARTICLE.CodeArticle," + _
                 "  CodeABarre," + _
                 "  Designation," + _
                 "  PrixAchatHT," + _
                 "  TVA," + _
                 "  StockAlerte," + _
                 "  QteACommander," + _
                 "  QuantiteUnitaire," + _
                 "  FORME_ARTICLE.LibelleForme AS LibelleForme," + _
                 "  CASE WHEN Stock IS NULL THEN 0 ELSE Stock END AS StockArticle," + _
                 "  CASE WHEN QteEnCours IS NULL THEN 0 ELSE QteEnCours END AS QteEnCoursArticle " + _
                 "FROM ARTICLE " + _
                 "INNER JOIN  (SELECT DISTINCT CodeArticle FROM COMMANDE_INSTANTANEE) AS COMMANDE_INSTANTANEE ON COMMANDE_INSTANTANEE.CodeArticle = ARTICLE.CodeArticle " + _
                "LEFT OUTER JOIN (SELECT CodeArticle,SUM(Qte) AS QteEnCours " + _
                 "       FROM COMMANDE_DETAILS " + _
                 "       LEFT OUTER JOIN COMMANDE " + _
                 "       ON COMMANDE .NumeroCommande =COMMANDE_DETAILS .NumeroCommande " + _
                 "       WHERE  COMMANDE .NumeroFacture is null " + _
                 "       GROUP BY CodeArticle) " + _
                 "       AS QteEnCoursArticle " + _
                 "       ON dbo.ARTICLE.CodeArticle = QteEnCoursArticle.CodeArticle " + _
                "LEFT OUTER JOIN (SELECT  CodeArticle, SUM(QteLotArticle) AS Stock " + _
                 "       FROM dbo.LOT_ARTICLE " + _
                 "       GROUP BY CodeArticle) AS StockArticle ON dbo.ARTICLE.CodeArticle = StockArticle.CodeArticle " + _
                 "LEFT OUTER JOIN FORME_ARTICLE ON FORME_ARTICLE.CodeForme = ARTICLE.CodeForme " + _
                 "WHERE ARTICLE.Supprime = 0 AND (Stock<=ARTICLE.StockAlerte OR Stock IS NULL) " + _
                 " AND StockAlerte - Stock + QteACommander <> 0 " + _
                 "AND ARTICLE.CodeCategorie <> 9 AND ARTICLE .CodeSituation <>'3' "

        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande.Fill(dsCommande, "ARTICLE_JOURNALIERE")
        cbCommande = New SqlCommandBuilder(daCommande)

        Dim J As Integer

        Dim DataRowRecherche As DataRow = Nothing
        Dim DatePeremption As Date
        ProgressBar.Visible = True
        GroupeJauge.Visible = True
        ProgressBar.Value = 0

        unite = 100 / (dsCommande.Tables("ARTICLE_JOURNALIERE").Rows.Count - 1)

        'Arrêter la capture d'evenement clavier sur le contrôle 
        RemoveHandler Me.bAjouter.Click, AddressOf Me.bAjouter_Click

        ' pour empecher l'annulation : elle cause des problemes il faut 
        ' attendre la fin de l'execusion
        bAnnuler.Enabled = False

        For J = 0 To dsCommande.Tables("ARTICLE_JOURNALIERE").Rows.Count - 1

            If unite * J < (dsCommande.Tables("ARTICLE_JOURNALIERE").Rows.Count - 1) Then
                If (unite * J) <= 100 Then
                    ProgressBar.Value = unite * J
                Else
                    ProgressBar.Value = 100
                End If
            End If

            Application.DoEvents()

            DataRowRecherche = dsCommande.Tables("ARTICLE_JOURNALIERE").Rows(J)
            lArticleEnCours.Text = "   " + DataRowRecherche.Item("Designation")
            NouvelArticle("NumeroCommande") = NumeroCommande
            NouvelArticle("CodeArticle") = DataRowRecherche.Item("CodeArticle")
            NouvelArticle("CodeABarre") = DataRowRecherche.Item("CodeABarre")
            NouvelArticle("Designation") = DataRowRecherche.Item("Designation")
            NouvelArticle("LibelleForme") = DataRowRecherche.Item("LibelleForme")
            NouvelArticle("Stock") = DataRowRecherche.Item("StockArticle") 'CalculeStock(NouvelArticle("CodeArticle"))
            NouvelArticle("PrixAchatHT") = DataRowRecherche.Item("PrixAchatHT")
            NouvelArticle("TVA") = DataRowRecherche.Item("TVA")
            Try
                NouvelArticle("StockAlerte") = DataRowRecherche.Item("StockAlerte")
            Catch ex As Exception
                NouvelArticle("StockAlerte") = 0
            End Try
            If NouvelArticle("StockAlerte").ToString = "" Then
                NouvelArticle("StockAlerte") = 0
            End If

            NouvelArticle("EnCours") = DataRowRecherche.Item("QteEnCoursArticle") 'CalculerEnCours(NouvelArticle("CodeArticle"))
            NouvelArticle("QteACommander") = DataRowRecherche.Item("QteACommander")
            NouvelArticle("Qte") = (NouvelArticle("StockAlerte") - NouvelArticle("Stock") + NouvelArticle("QteACommander"))

            If CommandeEnCours = True Then
                NouvelArticle("Qte") = NouvelArticle("Qte") - CalculerEnCours(NouvelArticle("CodeArticle"))
                If NouvelArticle("Qte") < 0 Then
                    NouvelArticle("Qte") = 0
                End If
            End If

            NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
            NouvelArticle("QteUnitaire") = DataRowRecherche.Item("QuantiteUnitaire")
            NouvelArticle("LibelleForme") = DataRowRecherche.Item("LibelleForme")
            '------------------ récupération de la date de péremption

            StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
            System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
            NouvelArticle("CodeArticle") + "' Order by DatePeremptionArticle DESC "

            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL

            Try
                DatePeremption = cmdCommande.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If DatePeremption = #12:00:00 AM# Then
                'NouvelArticle("DatePeremption") = "01/01/1900"
            Else
                NouvelArticle("DatePeremption") = DatePeremption
            End If

            NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
            dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)
            NouvelArticle("Designation") = "" 'initialisation pour que le calcul des montants ne compare pas 
            NouvelArticle("CodeArticle") = "" ' des chaines de caractères avec des NULL
            NouvelArticle("CodeABarre") = ""
            NouvelArticle("Qte") = 0

        Next J

        bAnnuler.Enabled = True

        'Reprendre la capture d'evenement clavier sur le contrôle 
        AddHandler Me.bAjouter.Click, AddressOf Me.bAjouter_Click


        ProgressBar.Value = 100
        ProgressBar.Visible = False
        GroupeJauge.Visible = False

        cmbType.Text = "COMMANDE_INSTANTANEE"
        cmbType.Enabled = False
        CalculerMontants()

    End Sub

    '***************************************************************************************************
    '************************************ Cas de HIT PARADE ********************************************
    '***************************************************************************************************

    Private Sub CommandeHitParade(TenirCompteStockAlerte As Boolean, Trie As String, TenirCompteStock As Boolean)

        Dim SumQte As Integer
        '------------------------------ préparation des datatable vides 
        Trie = Trie.Replace("DESIGNATION", "VENTE_DETAILS.DESIGNATION")
        If (dsCommande.Tables.IndexOf("ARTICLE_HIT_PARADE") > -1) Then
            dsCommande.Tables("ARTICLE_HIT_PARADE").Clear()
        End If



        Dim cond As String = ""
        If Section = "INTERVALLE" Then
            cond = cond + " AND ARTICLE.Section >= " + DebutIntervalle.ToString + " AND ARTICLE.Section <= " + FinIntervalle.ToString
        End If
        If Rayon = "RAYON" Then
            cond = cond + " AND ARTICLE.Rayon = '" + RayonSelectionne + "'"
        End If
        If Forme <> 0 Then
            cond = cond + " AND ARTICLE.CodeForme = " + Forme.ToString
        End If
        If Categorie <> 0 Then
            cond = cond + " AND ARTICLE.CodeCategorie = " + Categorie.ToString
        End If
        If Labo <> 0 Then
            cond = cond + " AND ARTICLE.CodeLabo = " + Labo.ToString
        End If

        If TenirCompteStockAlerte = True Then
            StrSQL = "SELECT DISTINCT ARTICLE.CodeArticle," + _
                     "ARTICLE.CodeABarre," + _
                     "VENTE_DETAILS.Designation," + _
                     "ARTICLE.CodeForme," + _
                     "MAX(ARTICLE.PrixAchatHT) AS PrixAchat," + _
                    "MAX(ARTICLE.TVA) AS TVA," + _
                     "sum(qte) as qte," + _
                     "StockArticle.Stock, " + _
                     "CONVERT(BIT, CASE WHEN MAX(ARTICLE.CodeSituation) = '2' THEN '1' ELSE '0' END) as RUPTURE  " + _
                     " FROM VENTE_DETAILS " + _
                     " LEFT JOIN VENTE ON VENTE.NumeroVente =VENTE_DETAILS.NumeroVente " + _
                     " LEFT JOIN ARTICLE ON VENTE_DETAILS.CodeArticle=ARTICLE.CodeArticle AND ARTICLE.Supprime = 0" + _
                     " LEFT OUTER JOIN dbo.FORME_ARTICLE ON dbo.ARTICLE.CodeForme = dbo.FORME_ARTICLE.CodeForme  " + _
                     " LEFT OUTER JOIN (SELECT     CodeArticle, SUM(QteLotArticle) AS Stock " + _
                     " FROM  dbo.LOT_ARTICLE " + _
                     " WHERE (DatePeremptionArticle > GETDATE()) OR " + _
                     " (DatePeremptionArticle = '01/01/1900') OR " + _
                     " (DatePeremptionArticle IS NULL) " + _
                     " GROUP BY CodeArticle) AS StockArticle " + _
                     " ON dbo.ARTICLE.CodeArticle = StockArticle.CodeArticle " + _
                     " WHERE ARTICLE.Supprime = 0 AND ARTICLE.CodeCategorie <> 9 AND ARTICLE.CodeSituation <>'3' AND VENTE.Date BETWEEN ' " + _
                     DebutPeriode + "' AND '" + FinPeriode + "'" + _
                     " AND StockArticle.Stock <= ARTICLE .StockAlerte  " + _
                     cond + _
                     " group by ARTICLE.CodeForme, ARTICLE.CodeArticle,ARTICLE.CodeABarre,VENTE_DETAILS.designation,StockArticle.Stock ORDER BY " & Trie
        Else
            StrSQL = "SELECT DISTINCT ARTICLE.CodeArticle," + _
                    "ARTICLE.CodeABarre," + _
                    "VENTE_DETAILS.Designation," + _
                    "ARTICLE.CodeForme," + _
                    "MAX(ARTICLE.PrixAchatHT) AS PrixAchat," + _
                    "MAX(ARTICLE.TVA) AS TVA," + _
                    "sum(qte) as qte," + _
                    "StockArticle.Stock, " + _
                     "CONVERT(BIT, CASE WHEN MAX(ARTICLE.CodeSituation) = '2' THEN '1' ELSE '0' END) as RUPTURE  " + _
                    " FROM VENTE_DETAILS " + _
                    " LEFT JOIN VENTE ON VENTE.NumeroVente =VENTE_DETAILS.NumeroVente " + _
                    " LEFT JOIN ARTICLE ON VENTE_DETAILS.CodeArticle=ARTICLE.CodeArticle AND ARTICLE.Supprime = 0 " + _
                    " LEFT OUTER JOIN dbo.FORME_ARTICLE ON dbo.ARTICLE.CodeForme = dbo.FORME_ARTICLE.CodeForme  " + _
                    " LEFT OUTER JOIN (SELECT     CodeArticle, SUM(QteLotArticle) AS Stock " + _
                    " FROM  dbo.LOT_ARTICLE " + _
                    " WHERE (DatePeremptionArticle > GETDATE()) OR " + _
                    " (DatePeremptionArticle = '01/01/1900') OR " + _
                    " (DatePeremptionArticle IS NULL) " + _
                    " GROUP BY CodeArticle) AS StockArticle " + _
                    " ON dbo.ARTICLE.CodeArticle = StockArticle.CodeArticle " + _
                    " WHERE ARTICLE.Supprime = 0 AND ARTICLE.CodeCategorie <> 9 AND ARTICLE.CodeSituation <>'3' AND VENTE.Date BETWEEN ' " + _
                    DebutPeriode + "' AND '" + FinPeriode + "'" + _
                    cond + _
                    " group by ARTICLE.CodeForme, ARTICLE.CodeArticle,ARTICLE.CodeABarre,VENTE_DETAILS.designation,StockArticle.Stock ORDER BY " & Trie
        End If
        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande.Fill(dsCommande, "ARTICLE_HIT_PARADE")
        cbCommande = New SqlCommandBuilder(daCommande)

        Dim J As Integer
        Dim DataRowRecherche As DataRow = Nothing
        Dim DatePeremption As Date

        ProgressBar.Visible = True
        GroupeJauge.Visible = True
        ProgressBar.Value = 0

        unite = 100 / (dsCommande.Tables("ARTICLE_HIT_PARADE").Rows.Count - 1)

        For J = 0 To dsCommande.Tables("ARTICLE_HIT_PARADE").Rows.Count - 1
            If ProgressBar.Value + unite <= 100 Then
                ProgressBar.Value += unite
            End If

            Application.DoEvents()

            DataRowRecherche = dsCommande.Tables("ARTICLE_HIT_PARADE").Rows(J)

            lArticleEnCours.Text = DataRowRecherche.Item("Designation")

            NouvelArticle("NumeroCommande") = NumeroCommande
            NouvelArticle("CodeArticle") = DataRowRecherche.Item("CodeArticle")
            NouvelArticle("CodeABarre") = DataRowRecherche.Item("CodeABarre")
            NouvelArticle("Designation") = DataRowRecherche.Item("Designation")
            'NouvelArticle("CodeForme") = DataRowRecherche.Item("CodeForme")
            NouvelArticle("Stock") = CalculeStock(NouvelArticle("CodeArticle"))
            NouvelArticle("PrixAchatHT") = DataRowRecherche.Item("PrixAchat")
            NouvelArticle("TVA") = DataRowRecherche.Item("TVA")
            NouvelArticle("RUPTURE") = DataRowRecherche.Item("RUPTURE")
            Try
                NouvelArticle("StockAlerte") = RecupererValeurExecuteScalaire("StockAlerte", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
            Catch ex As Exception
                NouvelArticle("StockAlerte") = 0
            End Try

            NouvelArticle("EnCours") = 0
            NouvelArticle("QteACommander") = RecupererValeurExecuteScalaire("QteACommander", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
            If TenirCompteStockAlerte = True Then
                NouvelArticle("Qte") = NouvelArticle("StockAlerte") - NouvelArticle("Stock") + NouvelArticle("QteACommander")
            ElseIf TenirCompteStock Then
                cmdDetailCommande.Connection = ConnectionServeur
                cmdDetailCommande.CommandText = "SELECT ISNULL(SUM(Qte), 0) FROM VENTE_DETAILS INNER JOIN VENTE ON VENTE.NumeroVente = VENTE_DETAILS.NumeroVente WHERE CodeArticle = " & Quote(DataRowRecherche.Item("CodeArticle")) & " AND CONVERT(DATE, DATE) BETWEEN " & Quote(DebutPeriode) & " AND " & Quote(FinPeriode)
                Try
                    SumQte = cmdDetailCommande.ExecuteScalar
                    NouvelArticle("Qte") = SumQte - NouvelArticle("Stock")
                Catch
                    NouvelArticle("Qte") = DataRowRecherche.Item("qte")
                End Try
            Else
                NouvelArticle("Qte") = DataRowRecherche.Item("qte")
            End If
            If CommandeEnCours = True Then
                NouvelArticle("Qte") = NouvelArticle("Qte") - CalculerEnCours(NouvelArticle("CodeArticle"))
                If NouvelArticle("Qte") < 0 Then
                    NouvelArticle("Qte") = 0
                End If
            End If

            NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
            NouvelArticle("QteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
            'NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", "FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

            '------------------ récupération de la date de péremption

            StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
            System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
            NouvelArticle("CodeArticle") + "' Order by DatePeremptionArticle DESC "

            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL

            Try
                DatePeremption = cmdCommande.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If DatePeremption = #12:00:00 AM# Then
                'NouvelArticle("DatePeremption") = "01/01/1900"
            Else
                NouvelArticle("DatePeremption") = DatePeremption
            End If

            NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
            dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)
            NouvelArticle("Designation") = "" 'initialisation pour que le calcul des montants ne compare pas 
            NouvelArticle("CodeArticle") = "" ' des chaines de caractères avec des NULL
            NouvelArticle("CodeABarre") = ""
            NouvelArticle("Qte") = 0

            gArticles.Refresh()

        Next J

        ProgressBar.Value = 100
        ProgressBar.Visible = False
        GroupeJauge.Visible = False

        cmbType.Text = "HIT PARADE"
        cmbType.Enabled = False
        CalculerMontants()
    End Sub
    '***************************************************************************************************
    '************************************ Cas de JOURNALIER ********************************************
    '***************************************************************************************************
    Private Sub CommandeJournalier(Trie As String)
        '------------------------------ préparation des datatable vides 
        Trie = Trie.Replace("Qte", "QteACommander")
        If (dsCommande.Tables.IndexOf("ARTICLE_JOURNALIERE") > -1) Then
            dsCommande.Tables("ARTICLE_JOURNALIERE").Clear()
        End If

        Dim Cond As String = ""
        If SansManquantDepuis <> "" Then
            Cond = Cond + " AND DATEDIFF ( DAY  , ARTICLE.DateAlerte   , GETDATE() ) <= (SELECT TOP(1) ISNULL(NePAsSortirLesManquantsDepuis, 5) FROM PARAMETRES WHERE NePAsSortirLesManquantsDepuis <> '' )"
        End If
        If Section = "INTERVALLE" And DebutIntervalle.ToString <> "" And FinIntervalle.ToString <> "" Then
            Cond = Cond + " AND Section >= " + DebutIntervalle.ToString + " AND Section <= " + FinIntervalle.ToString
        End If
        If Forme <> 0 Then
            Cond = Cond + " AND ARTICLE.CodeForme = " + Forme.ToString
        End If
        If Categorie <> 0 Then
            Cond = Cond + " AND CodeCategorie = " + Categorie.ToString
        End If
        If Labo <> 0 Then
            Cond = Cond + " AND CodeLabo = " + Labo.ToString
        End If
        If Rayon = "RAYON" Then
            Cond = Cond + " AND Rayon = '" + RayonSelectionne + "'"
        End If
        Cond = Cond + " ORDER BY " + Trie

        'StrSQL = "SELECT distinct  ARTICLE.CodeArticle," + _
        '         "CodeABarre," + _
        '         "Designation," + _
        '         "PrixAchatHT," + _
        '         "TVA," + _
        '         "StockAlerte," + _
        '         "QteACommander," + _
        '         "QuantiteUnitaire," + _
        '         "FORME_ARTICLE.LibelleForme AS LibelleForme," + _
        '         "CASE WHEN Stock IS NULL THEN 0 ELSE Stock END AS StockArticle," + _
        '         "CASE WHEN QteEnCours IS NULL THEN 0 ELSE QteEnCours END AS QteEnCoursArticle, " + _
        '         "CONVERT(BIT, CASE WHEN ARTICLE.CodeSituation = '2' THEN '1' ELSE '0' END) as RUPTURE  " + _
        '         "FROM ARTICLE " + _
        '         "LEFT OUTER JOIN (SELECT  CodeArticle, SUM(QteLotArticle) AS Stock " + _
        '         "       FROM dbo.LOT_ARTICLE " + _
        '         "       GROUP BY CodeArticle) AS StockArticle ON dbo.ARTICLE.CodeArticle = StockArticle.CodeArticle " + _
        '         "LEFT OUTER JOIN (SELECT CodeArticle,SUM(Qte) AS QteEnCours " + _
        '         "       FROM COMMANDE_DETAILS " + _
        '         "       LEFT OUTER JOIN COMMANDE " + _
        '         "       ON COMMANDE .NumeroCommande =COMMANDE_DETAILS .NumeroCommande " + _
        '         "       WHERE  COMMANDE .NumeroFacture is null " + _
        '         "       GROUP BY CodeArticle) " + _
        '         "       AS QteEnCoursArticle " + _
        '         "       ON dbo.ARTICLE.CodeArticle = QteEnCoursArticle.CodeArticle " + _
        '         "LEFT OUTER JOIN FORME_ARTICLE ON FORME_ARTICLE.CodeForme = ARTICLE.CodeForme " + _
        '         "WHERE ARTICLE.Supprime = 0 AND (Stock<=ARTICLE.StockAlerte OR Stock IS NULL) " + _
        '         " AND StockAlerte - Stock + QteACommander <> 0 " + _
        '         "AND ARTICLE.CodeCategorie <> 9 AND ARTICLE.CodeSituation <>'4' AND ARTICLE.CodeSituation in ('1', '2', '3') " + Cond

        '' POUR AMINE SALAHH
        StrSQL = "EXEC P_CommandeJournaliere @SansManquantDepuis = " + Quote(SansManquantDepuis) + ", @Section = " + Quote(Section) + ", @DebutIntervalle = " + Quote(DebutIntervalle) + ", @FinIntervalle = " + Quote(FinIntervalle) + ", @Forme = " + Quote(Forme) + ", @Categorie = " + Quote(Categorie) + ", @Labo = " + Quote(Labo) + ", @Rayon = " + Quote(Rayon) + ", @RayonSelectionne = " + Quote(RayonSelectionne) + ", @OrderBy = " + Quote(Trie)
        '' Fin

        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande.Fill(dsCommande, "ARTICLE_JOURNALIERE")
        cbCommande = New SqlCommandBuilder(daCommande)

        Dim J As Integer

        Dim DataRowRecherche As DataRow = Nothing
        Dim DatePeremption As Date
        ProgressBar.Visible = True
        GroupeJauge.Visible = True
        ProgressBar.Value = 0

        unite = 100 / (dsCommande.Tables("ARTICLE_JOURNALIERE").Rows.Count - 1)

        'Arrêter la capture d'evenement clavier sur le contrôle 
        RemoveHandler Me.bAjouter.Click, AddressOf Me.bAjouter_Click

        ' pour empecher l'annulation : elle cause des problemes il faut 
        ' attendre la fin de l'execusion
        bAnnuler.Enabled = False

        For J = 0 To dsCommande.Tables("ARTICLE_JOURNALIERE").Rows.Count - 1

            If unite * J < (dsCommande.Tables("ARTICLE_JOURNALIERE").Rows.Count - 1) Then
                If (unite * J) <= 100 Then
                    ProgressBar.Value = unite * J
                Else
                    ProgressBar.Value = 100
                End If
            End If

            Application.DoEvents()

            DataRowRecherche = dsCommande.Tables("ARTICLE_JOURNALIERE").Rows(J)
            lArticleEnCours.Text = "   " + DataRowRecherche.Item("Designation")
            NouvelArticle("NumeroCommande") = NumeroCommande
            NouvelArticle("CodeArticle") = DataRowRecherche.Item("CodeArticle")
            NouvelArticle("CodeABarre") = DataRowRecherche.Item("CodeABarre")
            NouvelArticle("Designation") = DataRowRecherche.Item("Designation")
            NouvelArticle("LibelleForme") = DataRowRecherche.Item("LibelleForme")
            NouvelArticle("Stock") = DataRowRecherche.Item("StockArticle") 'CalculeStock(NouvelArticle("CodeArticle"))
            NouvelArticle("PrixAchatHT") = DataRowRecherche.Item("PrixAchatHT")
            NouvelArticle("TVA") = DataRowRecherche.Item("TVA")
            Try
                NouvelArticle("StockAlerte") = DataRowRecherche.Item("StockAlerte")
            Catch ex As Exception
                NouvelArticle("StockAlerte") = 0
            End Try
            If NouvelArticle("StockAlerte").ToString = "" Then
                NouvelArticle("StockAlerte") = 0
            End If
            NouvelArticle("EnCours") = DataRowRecherche.Item("QteEnCoursArticle") 'CalculerEnCours(NouvelArticle("CodeArticle"))
            NouvelArticle("QteACommander") = DataRowRecherche.Item("QteACommander")
            If TenirCompteStockAlerteCJ = True Then
                NouvelArticle("Qte") = (NouvelArticle("StockAlerte") - NouvelArticle("Stock") + NouvelArticle("QteACommander")) * NombreDeJour
            Else
                NouvelArticle("Qte") = (NouvelArticle("Stock") + NouvelArticle("QteACommander")) * NombreDeJour
            End If

            If CommandeEnCours = True Then
                NouvelArticle("Qte") = NouvelArticle("Qte") - CalculerEnCours(NouvelArticle("CodeArticle"))
                If NouvelArticle("Qte") < 0 Then
                    NouvelArticle("Qte") = 0
                End If
            End If
            NouvelArticle("TotalTTCAchat") = (NouvelArticle("PrixAchatHT") / 100) * (100 + NouvelArticle("TVA")) * NouvelArticle("Qte")
            NouvelArticle("QteUnitaire") = DataRowRecherche.Item("QuantiteUnitaire")
            NouvelArticle("LibelleForme") = DataRowRecherche.Item("LibelleForme")
            NouvelArticle("RUPTURE") = DataRowRecherche.Item("RUPTURE")

            '------------------ récupération de la date de péremption
            StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
            System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
            NouvelArticle("CodeArticle") + "' Order by DatePeremptionArticle DESC "

            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL

            Try
                DatePeremption = cmdCommande.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If DatePeremption = #12:00:00 AM# Then
                'NouvelArticle("DatePeremption") = "01/01/1900"
            Else
                NouvelArticle("DatePeremption") = DatePeremption
            End If

            NouvelArticle = dsCommande.Tables("COMMANDE_DETAILS").NewRow()
            dsCommande.Tables("COMMANDE_DETAILS").Rows.Add(NouvelArticle)
            NouvelArticle("Designation") = "" 'initialisation pour que le calcul des montants ne compare pas 
            NouvelArticle("CodeArticle") = "" ' des chaines de caractères avec des NULL
            NouvelArticle("CodeABarre") = ""
            NouvelArticle("Qte") = 0

        Next J

        bAnnuler.Enabled = True

        'Reprendre la capture d'evenement clavier sur le contrôle 
        AddHandler Me.bAjouter.Click, AddressOf Me.bAjouter_Click


        ProgressBar.Value = 100
        ProgressBar.Visible = False
        GroupeJauge.Visible = False

        cmbType.Text = "JOURNALIERE"
        cmbType.Enabled = False
        CalculerMontants()
    End Sub

    '---initialisation des fournisseurs
    Private Sub initFournisseur()
        Try
            dsCommande.Tables("FOURNISSEUR").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT CodeFournisseur,NomFournisseur FROM FOURNISSEUR ORDER BY NomFournisseur ASC"
        cmdChargementCommande.Connection = ConnectionServeur
        cmdChargementCommande.CommandText = StrSQL
        daChargementCommande = New SqlDataAdapter(cmdChargementCommande)
        daChargementCommande.Fill(dsCommande, "FOURNISSEUR")
        cmbFournisseur.DataSource = dsCommande.Tables("FOURNISSEUR")
        cmbFournisseur.ValueMember = "CodeFournisseur"
        cmbFournisseur.DisplayMember = "NomFournisseur"
        cmbFournisseur.ColumnHeaders = False
        cmbFournisseur.Splits(0).DisplayColumns("CodeFournisseur").Visible = False
        cmbFournisseur.Splits(0).DisplayColumns("NomFournisseur").Width = 10
        cmbFournisseur.ExtendRightColumn = True
    End Sub

    Private Sub initCommandeDetail()
        Try
            dsCommande.Tables("COMMANDE_DETAILS").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT TOP(0) NumeroCommande," + _
               "CodeArticle," + _
               "CodeABarre," + _
               "Designation," + _
               "'' AS LibelleForme," + _
               "Qte," + _
               "Stock," + _
               "DatePeremption," + _
               "PrixAchatHT," + _
               "TVA," + _
               "TotalTTCAchat," + _
               "EnCours," + _
               "StockAlerte," + _
               "QteACommander," + _
               "QteUnitaire, " + _
                "CONVERT(bit, 0) AS RUPTURE," + _
                 "'' AS Vide " + _
               "FROM " + _
               "COMMANDE_DETAILS " + _
               "WHERE  " + _
               "NumeroCommande =" + Quote(NumeroCommande) + " ORDER BY Designation"

        cmdChargementCommande.Connection = ConnectionServeur
        cmdChargementCommande.CommandText = StrSQL
        daChargementCommande = New SqlDataAdapter(cmdChargementCommande)
        daChargementCommande.Fill(dsCommande, "COMMANDE_DETAILS")
    End Sub

    'initialisation la grid article
    Private Sub initgArticle()
        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsCommande
            Catch ex As Exception
            End Try
            .DataMember = "COMMANDE_DETAILS"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qté"
            .Columns("Stock").Caption = "Stock"
            .Columns("DatePeremption").Caption = "Date de péremption"
            .Columns("PrixAchatHT").Caption = "Prix A HT "
            .Columns("TVA").Caption = "TVA"
            .Columns("TotalTTCAchat").Caption = "Total TTC "
            .Columns("StockAlerte").Caption = "S Alerte"
            .Columns("EnCours").Caption = "Encours "
            .Columns("QteACommander").Caption = "Qte Cmd"
            .Columns("QteUnitaire").Caption = "Q unitaire"
            .Columns("Vide").Caption = ""

            ' Colonne non liée : LibelleForme
            .Columns("LibelleForme").DataField = ""

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTTCAchat").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("NumeroCommande").Width = 0
            .Splits(0).DisplayColumns("NumeroCommande").Visible = False
            .Splits(0).DisplayColumns("NumeroCommande").AllowSizing = False
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 120
            .Splits(0).DisplayColumns("Designation").Width = 340
            '.Splits(0).DisplayColumns("LibelleForme").Width = 100
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("Stock").Width = 50
            .Splits(0).DisplayColumns("DatePeremption").Width = 100
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 80
            .Splits(0).DisplayColumns("TVA").Width = 70
            .Splits(0).DisplayColumns("TotalTTCAchat").Width = 80
            .Splits(0).DisplayColumns("StockAlerte").Width = 80
            .Splits(0).DisplayColumns("EnCours").Width = 60
            .Splits(0).DisplayColumns("QteACommander").Width = 60
            .Splits(0).DisplayColumns("QteUnitaire").Width = 50
            .Splits(0).DisplayColumns("RUPTURE").Width = 50
            .Splits(0).DisplayColumns("Vide").Width = 50

            Try
                .Splits(0).DisplayColumns("Vide").Visible = False
                .Splits(0).DisplayColumns("CodeForme").Visible = False
                .Splits(0).DisplayColumns("Ordre").Visible = False
            Catch ex As Exception
            End Try


            'coloriage de la liste 
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.BackColor = Color.FromArgb(250, 250, 200)
            Next

            .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(250, 250, 200)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(210, 240, 255)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(210, 240, 255)
            .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("PrixAchatHT").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("TVA").Style.BackColor = Color.FromArgb(210, 255, 230)
            .Splits(0).DisplayColumns("TotalTTCAchat").Style.BackColor = Color.FromArgb(210, 240, 255)

            .Splits(0).DisplayColumns("StockAlerte").Style.BackColor = Color.FromArgb(255, 224, 192)
            .Splits(0).DisplayColumns("QteACommander").Style.BackColor = Color.FromArgb(255, 224, 192)
            .Splits(0).DisplayColumns("RUPTURE").Style.BackColor = Color.FromArgb(255, 224, 192)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

            'Style du Caractere et du grid
            ParametreGrid(gArticles)
        End With
    End Sub

    Private Sub initCommandeEntete()
        Try
            dsCommande.Tables("COMMANDE").Clear()
        Catch ex As Exception
        End Try

        Try

            StrSQL = " SELECT TOP (0 )* FROM COMMANDE "
            cmdEnteteCommande.Connection = ConnectionServeur
            cmdEnteteCommande.CommandText = StrSQL
            daEnteteCommande = New SqlDataAdapter(cmdEnteteCommande)
            daEnteteCommande.Fill(dsCommande, "COMMANDE")
            cbEnteteCommande = New SqlCommandBuilder(daEnteteCommande)

        Catch ex As Exception
            WriteLine(ex.Message)
        End Try

    End Sub

    'pour initaliser l'entete de la commande
    Private Sub ChargementEnteteCommnade()
        StrSQL = "SELECT * FROM COMMANDE WHERE SUBSTRING(NumeroCommande,0,5)<=YEAR(getdate()) ORDER BY NumeroCommande ASC"
        cmdChargementCommande.Connection = ConnectionServeur
        cmdChargementCommande.CommandText = StrSQL
        daChargementCommande = New SqlDataAdapter(cmdChargementCommande)
        daChargementCommande.Fill(dsCommande, "COMMANDE")

        If dsCommande.Tables("COMMANDE").Rows.Count > 0 Then
            NumeroCommande = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1).Item("NumeroCommande")
            lNumeroCommande.Text = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("NumeroCommande")
            lDateCommande.Text = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("Date")
            cmbFournisseur.SelectedValue = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("CodeFournisseur")
            Try
                tNumeroBlFact.Value = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("NumeroFacture")
            Catch ex As Exception
                tNumeroBlFact.Value = ""
            End Try
            TotalTTCCommande = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("TotalTTC")
            TotalHTCommande = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("TotalHT")
            lTotalTTCAchat.Text = Math.Round(dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("TotalTTC"), 3)
            lTotHTAchat.Text = TotalHTCommande.ToString
            lTotalTVA.Text = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("TotalTVA")
            cmbType.Text = dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("TypeCommande")
            lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsCommande.Tables("COMMANDE").Rows(dsCommande.Tables("COMMANDE").Rows.Count - 1)("CodePersonnel"))
        End If
    End Sub

    'pour afficher le nbre de commande en instance
    Private Sub NombredeCommandeEnInstance()
        StrSQL = " SELECT COUNT(*) FROM COMMANDE_INSTANCE "

        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL

        Try
            NombreCommandeInstance = cmdCommande.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        lNbreInstance.Text = "Commandes en frigo : " + NombreCommandeInstance.ToString
    End Sub

    'pour initaliser la liste des article
    Private Sub initAticle()

        StrSQL = "SELECT CodeArticle," + _
                 "Designation," + _
                 "LibelleForme," + _
                 "PrixVenteTTC" + _
                 " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE " + _
                 " ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme WHERE Supprime = 0 and " + _
                 "Designation LIKE '" + gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + "' ORDER BY Designation"

        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = StrSQL
        daCommande = New SqlDataAdapter(cmdCommande)
        daCommande.Fill(dsCommande, "ARTICLE")

        With gListeRecherche
            .Columns.Clear()
            .DataSource = dsCommande
            .DataMember = "ARTICLE"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code Article"
            .Columns("Designation").Caption = "Designation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("PrixVenteTTC").Caption = "Prix de vente"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centre tous les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Visible = False
            Next

            .Splits(0).DisplayColumns("CodeArticle").Visible = True
            .Splits(0).DisplayColumns("Designation").Visible = True
            .Splits(0).DisplayColumns("LibelleForme").Visible = True
            .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("Designation").Width = 300
            .Splits(0).DisplayColumns("LibelleForme").Width = 100
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 20
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
        End With
    End Sub

    Private Sub initControl()
        TotalTTCCommande = 0.0
        TotalHTCommande = 0.0
        TVA = 0.0
        Timbre = 0.3
        TotalTVACommande = 0.0

        lTotHTAchat.Text = "0.000"
        lTotalTTCAchat.Text = "0.000"

        lTotalTVA.Text = "0.000"

        lOperateur.Text = "-"

        lDateCommande.Text = System.DateTime.Now
        cmbFournisseur.Text = ""
        lNumeroCommande.Text = "-------------"

        bAnnuler.Enabled = True
        bConfirmer.Enabled = True

        bFournisseur.Enabled = True

        tNumeroBlFact.Value = ""

        bModifier.Enabled = False
        bFirst.Visible = False
        bPrevious.Visible = False
        bNext.Visible = False
        bLast.Visible = False
        bAjouter.Enabled = False
        bInstance.Enabled = True
        bImprimer.Enabled = False
        bListe.Enabled = False
        bExportExcel.Enabled = False
        bQuitter.Enabled = False

        GroupeFournisseur.Enabled = True

        gArticles.Focus()
        gArticles.Col = 1
        gArticles.EditActive = True





    End Sub

    '------------------------------------les procédure de navigation avec NumeroligneCommande---------------------------------------------------------

    Private Function selectionDernierLigneCommand()
        Try
            Dim StrSQL As String
            'Affécter le nombre de ligne au variable global  NumeroligneCommande
            StrSQL = " SELECT COUNT(*) FROM COMMANDE "

            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL

            selectionDernierLigneCommand = cmdCommande.ExecuteScalar()

            Return selectionDernierLigneCommand

        Catch ex As Exception

            Return 0
        End Try

    End Function
    Private Sub selectionLigneCommandeSuivante()
        Try
            ' incrémenter le numéro 1 au variable global NumeroligneCommande 
            NumeroligneCommande = NumeroligneCommande + 1

        Catch ex As Exception
            WriteLine(ex.Message)
        End Try
    End Sub


    Private Sub selectionLigneCommandePrecedent()

        Try
            'décrémenter le numéro 1 au variable global  NumeroligneCommande
            NumeroligneCommande = NumeroligneCommande - 1

        Catch ex As Exception
            WriteLine(ex.Message)
        End Try

    End Sub


    Private Sub selectionPremierLigneCommande()

        Try

            'Affécter le numéro 1 au variable global  NumeroligneCommande
            NumeroligneCommande = 1

        Catch ex As Exception

            WriteLine(ex.Message)
        End Try

    End Sub

    'Pour initialiser les btns suivant le mode et l'etat du table dans la BD

    Private Sub initBoutons()

        Try
            'Tester si on atteint la premiere ligne
            'pour desactiver les BTN Précedent et Premier élément        
            If NumeroligneCommande = 1 Then

                bPrevious.Enabled = False
                bFirst.Enabled = False

            Else

                bPrevious.Enabled = True
                bFirst.Enabled = True

            End If

            'Tester si on atteint la derniere ligne
            'pour desactiver les BTN Siuvant et Dernier élément
            If NumeroligneCommande = selectionDernierLigneCommand() Then

                bNext.Enabled = False
                bLast.Enabled = False


            Else

                bNext.Enabled = True
                bLast.Enabled = True

            End If

            'Le cas ou la table est vide
            If NumeroligneCommande = 0 Then

                bNext.Enabled = False
                bLast.Enabled = False
                bPrevious.Enabled = False
                bFirst.Enabled = False

            End If

            'Tester si la table est vide
            'pour desactiver les BTN Siuvant et Dernier élément
            If selectionDernierLigneCommand() = 0 Then

                'Bloque navigation
                bNext.Enabled = False
                bLast.Enabled = False
                bNext.Enabled = False
                bLast.Enabled = False

                'Bouton de ctrl
                bAnnuler.Enabled = False
                bConfirmer.Enabled = False
                bImprimer.Enabled = False
                bModifier.Enabled = False

                'bSupprimer.Enabled = False
                bQuitter.Enabled = True

            End If   ' le cas on a ajouté un element

            'le mode en Cosultation et on a des enregistrements
            If selectionDernierLigneCommand() <> 0 And mode = "Consultation" Then

                'Bouton de ctrl
                bAnnuler.Enabled = False
                bConfirmer.Enabled = False
                bImprimer.Enabled = True
                bModifier.Enabled = True

                'bSupprimer.Enabled = True
                bAjouter.Enabled = True
                bQuitter.Enabled = True

                bFirst.Visible = True
                bPrevious.Visible = True
                bNext.Visible = True
                bLast.Visible = True



                'le mode est modif/Ajout et on a des enregistrements
            ElseIf selectionDernierLigneCommand() <> 0 And mode <> "Consultation" Then

                bAnnuler.Enabled = True
                bConfirmer.Enabled = True
                bImprimer.Enabled = False
                bModifier.Enabled = False
                bAjouter.Enabled = False

                'bSupprimer.Enabled = True
                bQuitter.Enabled = True

                'pour rendre visible si le mode est Modif ou Ajout
                bLast.Visible = False
                bNext.Visible = False
                bPrevious.Visible = False
                bFirst.Visible = False



                'le mode en Cosultation et on  a pas des enregistrements
            ElseIf selectionDernierLigneCommand() = 0 And mode = "Consultation" Then

                bAjouter.Enabled = True
                bAnnuler.Enabled = False
                bConfirmer.Enabled = False
                bQuitter.Enabled = True

                'pour rendre invisible si le mode est Consultation
                bFirst.Visible = True
                bPrevious.Visible = True
                bNext.Visible = True
                bLast.Visible = True

            End If

        Catch ex As Exception

        End Try
    End Sub

    Private Sub gArticles_UnboundColumnFetch(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gArticles.UnboundColumnFetch
        'Récuperer la valeur Désignation FORME ARTICLE 
        Try

            StrSQL = " SELECT DISTINCT(LibelleForme) FROM FORME_ARTICLE AS F JOIN ARTICLE AS A ON  " + _
                     " F.CodeForme = A.CodeForme" + _
                     " WHERE CodeArticle = " + Quote(gArticles(e.Row, "CodeArticle")) + _
                        "ORDER BY  LibelleForme desc"

            cmdCommande.Connection = ConnectionServeur
            cmdCommande.CommandText = StrSQL

            e.Value = cmdCommande.ExecuteScalar()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Commande", "fCommande", " gArticles_UnboundColumnFetch", ex.Message, "0000---", "Erreur d'exécution de gArticles_UnboundColumnFetch ", True, True, True)

        End Try
    End Sub

    Private Sub bExportExcel_Click(sender As System.Object, e As System.EventArgs) Handles bExportExcel.Click

        Try
            dsCommande.Tables("ListeCommandeExport").Clear()
        Catch ex As Exception
        End Try
        Dim strSQLExcel As String = "SELECT [Ordre]+1 AS Seq " + _
                                "      ,C.[CodeArticle] AS [Code Interne Produit]" + _
                                "	  ,A.[CodePCT] AS [Code Pct]  " + _
                                "      ,C.[CodeABarre] AS [Code Barre] " + _
                                "      ,Replace(C.[Designation], ';', '') AS [Libelle]" + _
                                "      ,C.[Qte] " + _
                                "      ,C.[PrixAchatHT] AS [Prix achat ]" + _
                                "FROM [COMMANDE_DETAILS] C " + _
                                "JOIN [ARTICLE] A ON A.CodeArticle = C.CodeArticle " + _
                                "WHERE [NumeroCommande] = " + Quote(lNumeroCommande.Text) + _
                                "ORDER By [Ordre] "

        cmdCommande.Connection = ConnectionServeur
        cmdCommande.CommandText = strSQLExcel
        daCommande = New SqlDataAdapter(cmdCommande)
        daCommande.Fill(dsCommande, "ListeCommandeExport")

        With gExport
            .Columns.Clear()
            .DataSource = dsCommande
            .DataMember = "ListeCommandeExport"
            .Rebind(False)
            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
        End With

        ExportExcel()
    End Sub

    Private Sub ExporterVersExcel()

        Dim XlApp As Excel.Application
        Dim Cond As String = ""
        Dim Champ As String = ""
        Dim AnneeDebut As Integer = 0
        Dim AnneeFin As Integer = 0
        Dim MoisDebut As Integer = 0
        Dim MoisFin As Integer = 0
        Dim I As Integer = 0
        Dim chemin As String = ""

        SaveFileDialog1.ShowDialog()

        chemin = SaveFileDialog1.FileName
        Try
            XlApp = GetObject(, "excel.application")
        Catch
            XlApp = New Excel.Application
        End Try

        'Ajout d'une page et sélection 
        Dim xsTransfert As Excel.Worksheet = XlApp.Workbooks.Add.ActiveSheet
        XlApp.Cells(1, 1).value = "COMMANDE N°"
        XlApp.Cells(2, 1).value = lNumeroCommande.Text

        XlApp.Cells(1, 2).value = "Code Pharmacie"
        XlApp.Cells(2, 2).value = cmbFournisseur.SelectedValue

        XlApp.Cells(1, 3).value = "Date"
        XlApp.Cells(2, 3).value = lDateCommande.Text

        XlApp.Cells(1, 4).value = "Nbre de Lignes"
        XlApp.Cells(2, 4).value = ""


        Try
            'On crée la chaine de connexion
            Dim StrCon As String = ""
            StrCon = "ODBC;DRIVER={SQL Server}; SERVER=" + NomServeur + "; " & _
                     "DATABASE=" + NomBase + "; UID=" + NomUtilisateurSQL + "; PWD=" + MotDePasseSQL

            With xsTransfert.QueryTables.Add(Connection:=StrCon, Destination:=xsTransfert.Range("A3"))
                .CommandText = "SELECT [Ordre]+1 as Seq " + _
                                "      ,C.[CodeArticle] as codeInterneProduit" + _
                                "	  ,A.[CodePCT]  " + _
                                "      ,C.[CodeABarre] " + _
                                "      ,C.[Designation] as Libelle" + _
                                "      ,C.[Qte] " + _
                                "      ,C.[PrixAchatHT] " + _
                                "FROM [COMMANDE_DETAILS] C " + _
                                "JOIN [ARTICLE] A ON A.CodeArticle = C.CodeArticle " + _
                                "WHERE [NumeroCommande] = " + Quote(lNumeroCommande.Text) + _
                                "ORDER By [Ordre] "

                '"      ,C.[TVA] " + _
                '               "      ,C.[TotalTTCAchat] " + _

                .FieldNames = True
                .FillAdjacentFormulas = False
                .PreserveFormatting = True
                .RefreshOnFileOpen = False
                .BackgroundQuery = True
                .RefreshStyle = Excel.XlCellInsertionMode.xlOverwriteCells
                .SavePassword = False
                .SaveData = False
                .AdjustColumnWidth = True
                .RefreshPeriod = 0
                .PreserveColumnInfo = True
                .Refresh(BackgroundQuery:=False)
                .SaveAsODC(chemin)
            End With
            'XlApp.Visible = True

            'XlApp.SaveWorkspace(chemin)
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub ExportExcel()

        Dim xls As New C1XLBook
        Dim i As Integer = 0, j As Integer = 0, k As Integer = 0
        Dim Sheet As XLSheet = xls.Sheets(0)
        Dim Style As New XLStyle(xls)
        Dim BackStyle As New XLStyle(xls)
        Dim yFontconverter As New FontConverter
        Dim Col As C1.Win.C1TrueDBGrid.C1DataColumn
        Dim OKBackStyle As Boolean = False
        Dim BaseTableau As Integer = 0
        Dim cmd As New SqlCommand

        If gExport.RowCount = 0 Then Exit Sub

        Dim chemin As String = ""

        SaveFileDialog1.Filter = "*.csv|"
        SaveFileDialog1.ShowDialog()
        chemin = SaveFileDialog1.FileName

        If chemin = "" Then Exit Sub

        cmd.Connection = ConnectionServeur

        BaseTableau = 0
        Sheet(BaseTableau, 0).Value = "N° Transaction"
        Sheet(BaseTableau, 1).Value = "Code Pharmacie"
        Sheet(BaseTableau, 2).Value = "Date"
        Sheet(BaseTableau, 3).Value = "Nbre de lignes"

        BaseTableau += 1
        Sheet(BaseTableau, 0).Value = lNumeroCommande.Text
        Sheet(BaseTableau, 1).Value = cmbFournisseur.SelectedValue
        Sheet(BaseTableau, 2).Value = Convert.ToDateTime(lDateCommande.Text).Date
        Sheet(BaseTableau, 3).Value = RecupererValeurExecuteScalaire("count(*)", "COMMANDE_DETAILS", "NumeroCommande", lNumeroCommande.Text)

        BaseTableau += 1

        For Each Col In gExport.Columns
            Sheet(BaseTableau, j).Value = Col.Caption
            Sheet(BaseTableau, j).Style = Style
            j += 1
        Next
        Sheet.Rows(BaseTableau).Height *= 3
        BaseTableau += 1

        For i = 0 To gExport.RowCount - 1
            Application.DoEvents()
            j = 0
            For Each Col In gExport.Columns

                If Col.DataType.Name = "String" Then
                    If Not IsDBNull(gExport(i, j)) Then
                        Sheet(i + BaseTableau, j).Value = gExport(i, j)
                    Else
                        Sheet(i + BaseTableau, j).Value = ""
                    End If
                    Style.AlignHorz = XLAlignHorzEnum.Left
                    Sheet(i + BaseTableau, j).Style = Style
                ElseIf Col.DataType.Name = "Integer" Then
                    If Not IsDBNull(gExport(i, j)) Then
                        Sheet(i + BaseTableau, j).Value = CLng(gExport(i, j))
                    Else
                        Sheet(i + BaseTableau, j).Value = ""
                    End If
                    Style.AlignHorz = XLAlignHorzEnum.Right
                    Sheet(i + BaseTableau, j).Style = Style
                ElseIf Col.DataType.Name = "Decimal" Then
                    If Not IsDBNull(gExport(i, j)) Then
                        Sheet(i + BaseTableau, j).Value = CDbl(gExport(i, j))
                    Else
                        Sheet(i + BaseTableau, j).Value = ""
                    End If
                    Style.AlignHorz = XLAlignHorzEnum.Right
                    Sheet(i + BaseTableau, j).Style = Style
                ElseIf Col.DataType.Name = "DateTime" Then
                    If Not IsDBNull(gExport(i, j)) Then
                        Sheet(i + BaseTableau, j).Value = Format(gExport(i, j), "dd/MM/yyyy")
                    Else
                        Sheet(i + BaseTableau, j).Value = ""
                    End If
                    Style.AlignHorz = XLAlignHorzEnum.Center
                    Sheet(i + BaseTableau, j).Style = Style
                ElseIf Col.DataType.Name = "Int32" Then
                    If Not IsDBNull(gExport(i, j)) Then
                        Sheet(i + BaseTableau, j).Value = CLng(gExport(i, j))
                    Else
                        Sheet(i + BaseTableau, j).Value = ""
                    End If
                    Style.AlignHorz = XLAlignHorzEnum.Right
                    Sheet(i + BaseTableau, j).Style = Style
                Else
                    Sheet(i + BaseTableau, j).Value = CStr(gExport(i, j))
                    Style.AlignHorz = XLAlignHorzEnum.Left
                    Sheet(i + BaseTableau, j).Style = Style
                End If
                j += 1
            Next
        Next
        Application.DoEvents()
        xls.Sheets(0).Columns(0).Width = 1000
        Try
            chemin = Replace(chemin, ".CSV", "")
            chemin = Replace(chemin, ".csv", "")
            chemin = chemin + ".CSV"

            xls.Save(chemin, FileFormat.Csv)

            'xls.Save(chemin, FileFormat.OpenXml)
            'System.Diagnostics.Process.Start(chemin)
        Catch ex As Exception
            MsgBox(ex.Message) ', MsgBoxStyle.Critical, "Erreur")
        End Try

        Dim fileContents = System.IO.File.ReadAllText(chemin)
        fileContents = fileContents.Replace(";;;", ";")
        fileContents = fileContents.Replace("�", "°")
        fileContents = fileContents.Replace(".", " ")
        fileContents = fileContents.Replace(" 00:00:00", "")
        System.IO.File.WriteAllText(chemin, fileContents)


        If MessageBox.Show("Voulez-vous envoyer le fichier par Mail ?", "Envoi de commande", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.Yes Then
            Dim Outl As Object
            Outl = CreateObject("Outlook.Application")
            If Outl IsNot Nothing Then
                Dim omsg As Object
                omsg = Outl.CreateItem(0)
                omsg.To = ""
                omsg.bcc = ""
                omsg.subject = "Commande"
                omsg.body = ""
                omsg.Attachments.Add(chemin)
                omsg.Display(True)
            End If
        End If


    End Sub

    Private Sub cmbFournisseur_LostFocus(sender As Object, e As System.EventArgs) Handles cmbFournisseur.LostFocus
        Try
            cmbFournisseur.Text = cmbFournisseur.WillChangeToText
        Catch
        End Try
    End Sub
End Class