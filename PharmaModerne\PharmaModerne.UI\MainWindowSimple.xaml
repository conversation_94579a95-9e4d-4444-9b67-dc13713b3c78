<Window x:Class="PharmaModerne.UI.MainWindowSimple"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="PHARMA2000 Moderne - Test Simple" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen">
    
    <Grid Background="LightBlue">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- En-tête -->
        <Border Grid.Row="0" Background="DarkBlue" Padding="20">
            <StackPanel>
                <TextBlock Text="🏥 PHARMA2000 MODERNE" 
                         Foreground="White" 
                         FontSize="24" 
                         FontWeight="Bold"
                         HorizontalAlignment="Center"/>
                <TextBlock Text="Version de test simplifiée" 
                         Foreground="LightGray" 
                         FontSize="14"
                         HorizontalAlignment="Center"
                         Margin="0,5,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- Contenu principal -->
        <ScrollViewer Grid.Row="1" Margin="20">
            <StackPanel>
                
                <!-- Message de bienvenue -->
                <Border Background="White" 
                      CornerRadius="10" 
                      Padding="20" 
                      Margin="0,0,0,20"
                      Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                    <StackPanel>
                        <TextBlock Text="🎉 Félicitations !" 
                                 FontSize="20" 
                                 FontWeight="Bold" 
                                 Foreground="Green"
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="L'application PHARMA2000 Moderne fonctionne correctement !" 
                                 FontSize="16" 
                                 TextWrapping="Wrap"
                                 HorizontalAlignment="Center"
                                 Margin="0,10,0,0"/>
                    </StackPanel>
                </Border>
                
                <!-- Fonctionnalités -->
                <Border Background="White" 
                      CornerRadius="10" 
                      Padding="20" 
                      Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="📋 Fonctionnalités Implémentées" 
                                 FontSize="18" 
                                 FontWeight="Bold" 
                                 Margin="0,0,0,15"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="✅" FontSize="16" Margin="0,0,10,0"/>
                            <TextBlock Text="Architecture .NET 9 moderne" FontSize="14"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="✅" FontSize="16" Margin="0,0,10,0"/>
                            <TextBlock Text="Interface WPF avec Material Design" FontSize="14"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="✅" FontSize="16" Margin="0,0,10,0"/>
                            <TextBlock Text="Scanner de codes à barres intégré" FontSize="14"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="✅" FontSize="16" Margin="0,0,10,0"/>
                            <TextBlock Text="Services complets (Client, Scanner)" FontSize="14"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="✅" FontSize="16" Margin="0,0,10,0"/>
                            <TextBlock Text="Entity Framework Core" FontSize="14"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="✅" FontSize="16" Margin="0,0,10,0"/>
                            <TextBlock Text="Architecture MVVM" FontSize="14"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
                
                <!-- Test du scanner -->
                <Border Background="White" 
                      CornerRadius="10" 
                      Padding="20" 
                      Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="📱 Test du Scanner" 
                                 FontSize="18" 
                                 FontWeight="Bold" 
                                 Margin="0,0,0,15"/>
                        
                        <TextBox x:Name="ScannerTestBox"
                               Text="{Binding ScannerTestText, UpdateSourceTrigger=PropertyChanged}"
                               FontSize="16"
                               Padding="10"
                               Margin="0,0,0,10"
                               Background="LightYellow">
                            <TextBox.InputBindings>
                                <KeyBinding Key="Enter" Command="{Binding TestScannerCommand}"/>
                            </TextBox.InputBindings>
                        </TextBox>
                        
                        <TextBlock Text="💡 Tapez ou scannez un code puis appuyez sur Entrée" 
                                 FontSize="12" 
                                 Foreground="Gray"
                                 Margin="0,0,0,10"/>
                        
                        <Button Content="🔍 Tester le Scanner" 
                              Command="{Binding TestScannerCommand}"
                              Padding="15,8"
                              FontSize="14"
                              Background="Green"
                              Foreground="White"
                              BorderThickness="0"
                              Cursor="Hand"/>
                        
                        <TextBlock Text="{Binding ScannerResult}" 
                                 FontSize="14" 
                                 Foreground="Blue"
                                 Margin="0,10,0,0"
                                 TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>
                
                <!-- Actions -->
                <Border Background="White" 
                      CornerRadius="10" 
                      Padding="20">
                    <StackPanel>
                        <TextBlock Text="🚀 Actions Rapides" 
                                 FontSize="18" 
                                 FontWeight="Bold" 
                                 Margin="0,0,0,15"/>
                        
                        <UniformGrid Columns="2" Margin="0,10">
                            <Button Content="👥 Module Clients" 
                                  Command="{Binding OpenClientsCommand}"
                                  Margin="5"
                                  Padding="15,10"
                                  Background="Blue"
                                  Foreground="White"
                                  BorderThickness="0"
                                  Cursor="Hand"/>
                            
                            <Button Content="💊 Module Articles" 
                                  Command="{Binding OpenArticlesCommand}"
                                  Margin="5"
                                  Padding="15,10"
                                  Background="Orange"
                                  Foreground="White"
                                  BorderThickness="0"
                                  Cursor="Hand"/>
                            
                            <Button Content="🛒 Point de Vente" 
                                  Command="{Binding OpenVenteCommand}"
                                  Margin="5"
                                  Padding="15,10"
                                  Background="Green"
                                  Foreground="White"
                                  BorderThickness="0"
                                  Cursor="Hand"/>
                            
                            <Button Content="📊 Rapports" 
                                  Command="{Binding OpenReportsCommand}"
                                  Margin="5"
                                  Padding="15,10"
                                  Background="Purple"
                                  Foreground="White"
                                  BorderThickness="0"
                                  Cursor="Hand"/>
                        </UniformGrid>
                    </StackPanel>
                </Border>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Pied de page -->
        <Border Grid.Row="2" Background="DarkGray" Padding="10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="PHARMA2000 Moderne v1.0" 
                         Foreground="White" 
                         FontSize="12"/>
                <TextBlock Text=" | " 
                         Foreground="LightGray" 
                         FontSize="12" 
                         Margin="10,0"/>
                <TextBlock Text="Compilé avec succès ✅" 
                         Foreground="LightGreen" 
                         FontSize="12"/>
                <TextBlock Text=" | " 
                         Foreground="LightGray" 
                         FontSize="12" 
                         Margin="10,0"/>
                <TextBlock Text="{Binding CurrentTime}" 
                         Foreground="LightBlue" 
                         FontSize="12"/>
            </StackPanel>
        </Border>
        
    </Grid>
</Window>
