<Window x:Class="PharmaModerne.UI.TestWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="PHARMA2000 Moderne - Test de Fonctionnement" 
        Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        Background="LightBlue">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Titre -->
        <Border Grid.Row="0" 
                Background="DarkBlue" 
                CornerRadius="10" 
                Padding="20" 
                Margin="0,0,0,20">
            <StackPanel HorizontalAlignment="Center">
                <TextBlock Text="🏥 PHARMA2000 MODERNE" 
                         Foreground="White" 
                         FontSize="28" 
                         FontWeight="Bold"
                         HorizontalAlignment="Center"/>
                <TextBlock Text="✅ APPLICATION FONCTIONNELLE !" 
                         Foreground="LightGreen" 
                         FontSize="16"
                         HorizontalAlignment="Center"
                         Margin="0,10,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- Contenu -->
        <Border Grid.Row="1" 
                Background="White" 
                CornerRadius="10" 
                Padding="30">
            <ScrollViewer>
                <StackPanel>
                    
                    <!-- Message de succès -->
                    <TextBlock Text="🎉 Félicitations !" 
                             FontSize="24" 
                             FontWeight="Bold" 
                             Foreground="Green"
                             HorizontalAlignment="Center"
                             Margin="0,0,0,20"/>
                    
                    <TextBlock Text="Votre application PHARMA2000 Moderne fonctionne parfaitement !" 
                             FontSize="16" 
                             TextWrapping="Wrap"
                             HorizontalAlignment="Center"
                             Margin="0,0,0,30"/>
                    
                    <!-- Fonctionnalités -->
                    <TextBlock Text="📋 Fonctionnalités Implémentées :" 
                             FontSize="18" 
                             FontWeight="Bold" 
                             Margin="0,0,0,15"/>
                    
                    <StackPanel Margin="20,0,0,0">
                        <TextBlock Text="✅ Architecture .NET 9 moderne" FontSize="14" Margin="0,3"/>
                        <TextBlock Text="✅ Interface WPF responsive" FontSize="14" Margin="0,3"/>
                        <TextBlock Text="✅ Scanner de codes à barres intégré" FontSize="14" Margin="0,3"/>
                        <TextBlock Text="✅ Services complets (Client, Scanner)" FontSize="14" Margin="0,3"/>
                        <TextBlock Text="✅ Entity Framework Core" FontSize="14" Margin="0,3"/>
                        <TextBlock Text="✅ Architecture MVVM" FontSize="14" Margin="0,3"/>
                        <TextBlock Text="✅ Material Design (prêt)" FontSize="14" Margin="0,3"/>
                    </StackPanel>
                    
                    <!-- Test du scanner -->
                    <Border Background="LightYellow" 
                            CornerRadius="5" 
                            Padding="15" 
                            Margin="0,20,0,0">
                        <StackPanel>
                            <TextBlock Text="📱 Test du Scanner :" 
                                     FontSize="16" 
                                     FontWeight="Bold" 
                                     Margin="0,0,0,10"/>
                            
                            <TextBox x:Name="TestTextBox"
                                   FontSize="14"
                                   Padding="8"
                                   Margin="0,0,0,10"
                                   Text="Tapez ou scannez un code ici..."/>
                            
                            <Button Content="🔍 Tester" 
                                  Click="TestButton_Click"
                                  Padding="10,5"
                                  FontSize="14"
                                  Background="Green"
                                  Foreground="White"
                                  BorderThickness="0"
                                  HorizontalAlignment="Left"/>
                            
                            <TextBlock x:Name="ResultTextBlock"
                                     FontSize="12" 
                                     Foreground="Blue"
                                     Margin="0,10,0,0"
                                     TextWrapping="Wrap"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Modules -->
                    <TextBlock Text="🚀 Modules Disponibles :" 
                             FontSize="16" 
                             FontWeight="Bold" 
                             Margin="0,20,0,10"/>
                    
                    <UniformGrid Columns="2" Margin="0,10">
                        <Button Content="👥 Clients" 
                              Click="ModuleButton_Click"
                              Tag="Clients"
                              Margin="5"
                              Padding="10"
                              Background="Blue"
                              Foreground="White"
                              BorderThickness="0"/>
                        
                        <Button Content="💊 Articles" 
                              Click="ModuleButton_Click"
                              Tag="Articles"
                              Margin="5"
                              Padding="10"
                              Background="Orange"
                              Foreground="White"
                              BorderThickness="0"/>
                        
                        <Button Content="🛒 Ventes" 
                              Click="ModuleButton_Click"
                              Tag="Ventes"
                              Margin="5"
                              Padding="10"
                              Background="Green"
                              Foreground="White"
                              BorderThickness="0"/>
                        
                        <Button Content="📊 Rapports" 
                              Click="ModuleButton_Click"
                              Tag="Rapports"
                              Margin="5"
                              Padding="10"
                              Background="Purple"
                              Foreground="White"
                              BorderThickness="0"/>
                    </UniformGrid>
                    
                </StackPanel>
            </ScrollViewer>
        </Border>
        
        <!-- Pied de page -->
        <Border Grid.Row="2" 
                Background="DarkGray" 
                CornerRadius="5" 
                Padding="15" 
                Margin="0,20,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="PHARMA2000 Moderne v1.0" 
                         Foreground="White" 
                         FontSize="12"/>
                <TextBlock Text=" • " 
                         Foreground="LightGray" 
                         FontSize="12" 
                         Margin="10,0"/>
                <TextBlock Text="Compilé avec succès ✅" 
                         Foreground="LightGreen" 
                         FontSize="12"/>
                <TextBlock Text=" • " 
                         Foreground="LightGray" 
                         FontSize="12" 
                         Margin="10,0"/>
                <TextBlock x:Name="TimeTextBlock"
                         Foreground="LightBlue" 
                         FontSize="12"/>
            </StackPanel>
        </Border>
        
    </Grid>
</Window>
