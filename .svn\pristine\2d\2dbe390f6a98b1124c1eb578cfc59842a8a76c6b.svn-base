﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSet_EtatInventaireTemporaire" targetNamespace="http://tempuri.org/DataSet_EtatInventaireTemporaire.xsd" xmlns:mstns="http://tempuri.org/DataSet_EtatInventaireTemporaire.xsd" xmlns="http://tempuri.org/DataSet_EtatInventaireTemporaire.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSet_EtatInventaireTemporaire" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DataSet_EtatInventaireTemporaire" msprop:Generator_UserDSName="DataSet_EtatInventaireTemporaire">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="dtINVENTAIRE_DETAIL" msprop:Generator_TableClassName="dtINVENTAIRE_DETAILDataTable" msprop:Generator_TableVarName="tabledtINVENTAIRE_DETAIL" msprop:Generator_TablePropName="dtINVENTAIRE_DETAIL" msprop:Generator_RowDeletingName="dtINVENTAIRE_DETAILRowDeleting" msprop:Generator_UserTableName="dtINVENTAIRE_DETAIL" msprop:Generator_RowChangingName="dtINVENTAIRE_DETAILRowChanging" msprop:Generator_RowEvHandlerName="dtINVENTAIRE_DETAILRowChangeEventHandler" msprop:Generator_RowDeletedName="dtINVENTAIRE_DETAILRowDeleted" msprop:Generator_RowEvArgName="dtINVENTAIRE_DETAILRowChangeEvent" msprop:Generator_RowChangedName="dtINVENTAIRE_DETAILRowChanged" msprop:Generator_RowClassName="dtINVENTAIRE_DETAILRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="NumeroInventaire" msprop:Generator_ColumnVarNameInTable="columnNumeroInventaire" msprop:Generator_ColumnPropNameInRow="NumeroInventaire" msprop:Generator_ColumnPropNameInTable="NumeroInventaireColumn" msprop:Generator_UserColumnName="NumeroInventaire" type="xs:string" minOccurs="0" />
              <xs:element name="CodeArticle" msprop:Generator_ColumnVarNameInTable="columnCodeArticle" msprop:Generator_ColumnPropNameInRow="CodeArticle" msprop:Generator_ColumnPropNameInTable="CodeArticleColumn" msprop:Generator_UserColumnName="CodeArticle" type="xs:string" minOccurs="0" />
              <xs:element name="CodeABarre" msprop:Generator_ColumnVarNameInTable="columnCodeABarre" msprop:Generator_ColumnPropNameInRow="CodeABarre" msprop:Generator_ColumnPropNameInTable="CodeABarreColumn" msprop:Generator_UserColumnName="CodeABarre" type="xs:string" minOccurs="0" />
              <xs:element name="Designation" msprop:Generator_ColumnVarNameInTable="columnDesignation" msprop:Generator_ColumnPropNameInRow="Designation" msprop:Generator_ColumnPropNameInTable="DesignationColumn" msprop:Generator_UserColumnName="Designation" type="xs:string" minOccurs="0" />
              <xs:element name="LibelleForme" msprop:Generator_ColumnVarNameInTable="columnLibelleForme" msprop:Generator_ColumnPropNameInRow="LibelleForme" msprop:Generator_ColumnPropNameInTable="LibelleFormeColumn" msprop:Generator_UserColumnName="LibelleForme" type="xs:string" minOccurs="0" />
              <xs:element name="CodeForme" msprop:Generator_ColumnVarNameInTable="columnCodeForme" msprop:Generator_ColumnPropNameInRow="CodeForme" msprop:Generator_ColumnPropNameInTable="CodeFormeColumn" msprop:Generator_UserColumnName="CodeForme" type="xs:string" minOccurs="0" />
              <xs:element name="Rayon" msprop:Generator_ColumnVarNameInTable="columnRayon" msprop:Generator_ColumnPropNameInRow="Rayon" msprop:Generator_ColumnPropNameInTable="RayonColumn" msprop:Generator_UserColumnName="Rayon" type="xs:string" minOccurs="0" />
              <xs:element name="StockInitial" msprop:Generator_ColumnVarNameInTable="columnStockInitial" msprop:Generator_ColumnPropNameInRow="StockInitial" msprop:Generator_ColumnPropNameInTable="StockInitialColumn" msprop:Generator_UserColumnName="StockInitial" type="xs:string" minOccurs="0" />
              <xs:element name="StockActuel" msprop:Generator_ColumnVarNameInTable="columnStockActuel" msprop:Generator_ColumnPropNameInRow="StockActuel" msprop:Generator_ColumnPropNameInTable="StockActuelColumn" msprop:Generator_UserColumnName="StockActuel" type="xs:string" minOccurs="0" />
              <xs:element name="PrixAchatTTC" msprop:Generator_ColumnVarNameInTable="columnPrixAchatTTC" msprop:Generator_ColumnPropNameInRow="PrixAchatTTC" msprop:Generator_ColumnPropNameInTable="PrixAchatTTCColumn" msprop:Generator_UserColumnName="PrixAchatTTC" type="xs:string" minOccurs="0" />
              <xs:element name="TotalAchatTTC" msprop:Generator_ColumnVarNameInTable="columnTotalAchatTTC" msprop:Generator_ColumnPropNameInRow="TotalAchatTTC" msprop:Generator_ColumnPropNameInTable="TotalAchatTTCColumn" msprop:Generator_UserColumnName="TotalAchatTTC" type="xs:string" minOccurs="0" />
              <xs:element name="PrixVenteTTC" msprop:Generator_ColumnVarNameInTable="columnPrixVenteTTC" msprop:Generator_ColumnPropNameInRow="PrixVenteTTC" msprop:Generator_ColumnPropNameInTable="PrixVenteTTCColumn" msprop:Generator_UserColumnName="PrixVenteTTC" type="xs:string" minOccurs="0" />
              <xs:element name="QteChange" msprop:Generator_ColumnVarNameInTable="columnQteChange" msprop:Generator_ColumnPropNameInRow="QteChange" msprop:Generator_ColumnPropNameInTable="QteChangeColumn" msprop:Generator_UserColumnName="QteChange" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>