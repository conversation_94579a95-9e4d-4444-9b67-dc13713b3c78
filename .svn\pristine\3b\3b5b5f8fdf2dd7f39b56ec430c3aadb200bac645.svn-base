﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Public Class fHistoriqueDesChangementsDesPrix

    Dim cmdListe As New SqlCommand
    Dim cbListe As New SqlCommandBuilder
    Dim dsListe As New DataSet
    Dim daListe As New SqlDataAdapter

    Dim cmdChargementUser As New SqlCommand
    Dim cbChargementUser As New SqlCommandBuilder
    Dim dsChargementUser As New DataSet
    Dim daChargementUser As New SqlDataAdapter

    Public StrSQL As String = ""

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub
    Public Sub init()

        dtpDate.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDate.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        Try
            dsChargementUser.Tables("UTILISATEUR").Clear()
        Catch ex As Exception
        End Try

        Dim StrSQL As String = ""
        'chargement des utilisateurs
        StrSQL = "SELECT CodeUtilisateur,Nom FROM UTILISATEUR ORDER BY Login ASC"
        cmdChargementUser.Connection = ConnectionServeur
        cmdChargementUser.CommandText = StrSQL
        daChargementUser = New SqlDataAdapter(cmdChargementUser)
        daChargementUser.Fill(dsChargementUser, "UTILISATEUR")
        cmbUser.DataSource = dsChargementUser.Tables("UTILISATEUR")
        cmbUser.ValueMember = "CodeUtilisateur"
        cmbUser.DisplayMember = "Nom"
        cmbUser.ColumnHeaders = False
        cmbUser.Splits(0).DisplayColumns("CodeUtilisateur").Visible = False
        cmbUser.Splits(0).DisplayColumns("Nom").Width = 10
        cmbUser.ExtendRightColumn = True
        cmbUser.Focus()

        AfficherListeChangements()

    End Sub

    Public Sub AfficherListeChangements()

        Dim i As Integer = 0

        If (dsListe.Tables.IndexOf("CHANGEMANT_DE_PRIX") > -1) Then
            dsListe.Tables("CHANGEMANT_DE_PRIX").Clear()
        End If

        StrSQL = "select CAST(0 as bit) Cocher," + _
                 "Date, " + _
                 "CHANGEMANT_DE_PRIX.CodeArticle," + _
                 "Designation," + _
                 "AncienPrixAchatHT," + _
                 "AncienTVA," + _
                 "AncienPrixVenteTTC," + _
                 "AncienMarge," + _
                 "NouveauPrixAchatHT," + _
                 "NouveauTVA," + _
                 "NouveauPrixVenteTTC," + _
                 "NouveauMarge," + _
                 "Nom " + _
                 "FROM CHANGEMANT_DE_PRIX " + _
                 "LEFT OUTER JOIN UTILISATEUR ON CHANGEMANT_DE_PRIX.CodePersonnel=UTILISATEUR.CodeUtilisateur " + _
                 "LEFT OUTER JOIN ARTICLE ON CHANGEMANT_DE_PRIX.CodeArticle=ARTICLE.CodeArticle " + _
                 "WHERE Cacher=0 ORDER BY Date DESC"

        cmdListe.Connection = ConnectionServeur
        cmdListe.CommandText = StrSQL
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "CHANGEMANT_DE_PRIX")
        cbListe = New SqlCommandBuilder(daListe)

        With gListe
            .Columns.Clear()
            Try
                .DataSource = dsListe
            Catch ex As Exception
            End Try
            .DataMember = "CHANGEMANT_DE_PRIX"
            .Rebind(False)
            .Columns("Date").Caption = "Date"
            .Columns("CodeArticle").Caption = "Code Article"
            .Columns("Designation").Caption = "Designation"

            .Columns("AncienPrixAchatHT").Caption = "Ancien Prix Achat HT"
            .Columns("AncienTVA").Caption = "Ancien TVA"
            .Columns("AncienPrixVenteTTC").Caption = "Ancien Prix Vente TTC"
            .Columns("AncienMarge").Caption = "Ancien Marge"

            .Columns("NouveauPrixAchatHT").Caption = "Nouveau Prix Achat HT"
            .Columns("NouveauTVA").Caption = "Nouveau TVA"
            .Columns("NouveauPrixVenteTTC").Caption = "Nouveau Prix Vente TTC"
            .Columns("NouveauMarge").Caption = "Nouveau Marge"

            .Columns("Nom").Caption = "Personnel"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("Cocher").Width = 40
            .Splits(0).DisplayColumns("Date").Width = 80
            .Splits(0).DisplayColumns("CodeArticle").Width = 80
            .Splits(0).DisplayColumns("Designation").Width = 180

            .Splits(0).DisplayColumns("AncienPrixAchatHT").Width = 70
            .Splits(0).DisplayColumns("AncienTVA").Width = 70
            .Splits(0).DisplayColumns("AncienPrixVenteTTC").Width = 70
            .Splits(0).DisplayColumns("AncienMarge").Width = 70

            .Splits(0).DisplayColumns("NouveauPrixAchatHT").Width = 70
            .Splits(0).DisplayColumns("NouveauTVA").Width = 70
            .Splits(0).DisplayColumns("NouveauPrixVenteTTC").Width = 70
            .Splits(0).DisplayColumns("NouveauMarge").Width = 70

            .Splits(0).DisplayColumns("Nom").Width = 80

            .Splits(0).DisplayColumns(0).Locked = False

            .Splits(0).DisplayColumns("AncienMarge").Style.BackColor = Color.FromArgb(234, 220, 251, 251)
            .Splits(0).DisplayColumns("NouveauMarge").Style.BackColor = Color.FromArgb(234, 220, 251, 251)



            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            '.AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gListe)
        End With
    End Sub
    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerDate.Click
        Dim cmd As New SqlCommand
        If dtpDate.Text = "" Then
            MsgBox("Veillez donnez une date ", MsgBoxStyle.Information, "Information")
            dtpDate.Focus()
            Exit Sub
        End If
        If MsgBox("Voulez vous vraiment cacher les changements avant le " + dtpDate.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
            '--------------- suppression des acces

            Try
                cmd.Connection = ConnectionServeur
                cmd.CommandText = "UPDATE  CHANGEMANT_DE_PRIX SET Cacher=1 WHERE Date<='" + dtpDate.Text + "'"
                cmd.ExecuteNonQuery()

            Catch ex As Exception
                MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
            End Try
            init()
        End If
    End Sub

    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerUser.Click
        Dim cmd As New SqlCommand
        If cmbUser.Text = "" Then
            MsgBox("Veillez seléctionner un utilisateur ", MsgBoxStyle.Information, "Information")
            cmbUser.Focus()
            Exit Sub
        End If
        If MsgBox("Voulez vous vraiment cacher les changements de " + cmbUser.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
            '--------------- suppression des acces

            Try
                cmd.Connection = ConnectionServeur
                cmd.CommandText = "UPDATE  CHANGEMANT_DE_PRIX SET Cacher=1 WHERE CodePersonnel=" + cmbUser.SelectedValue
                cmd.ExecuteNonQuery()

            Catch ex As Exception
                MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
            End Try
            init()
        End If
    End Sub

    Private Sub Panel_Paint(ByVal sender As System.Object, ByVal e As System.Windows.Forms.PaintEventArgs) Handles Panel.Paint

    End Sub

    Private Sub bSupprimerCocher_Click_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerCocher.Click
        Dim cmd As New SqlCommand
        If MsgBox("Voulez vous vraiment cacher les changements cochés ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
            For i = 0 To dsListe.Tables("CHANGEMANT_DE_PRIX").Rows.Count - 1
                If dsListe.Tables("CHANGEMANT_DE_PRIX").Rows(i).Item("Cocher") = True Then
                    Try
                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = "UPDATE  CHANGEMANT_DE_PRIX SET Cacher=1 WHERE CodeArticle='" + dsListe.Tables("CHANGEMANT_DE_PRIX").Rows(i).Item("CodeArticle") + _
                        "' AND CodePersonnel=" + RecupererValeurExecuteScalaire("CodeUtilisateur", "UTILISATEUR", "Nom", dsListe.Tables("CHANGEMANT_DE_PRIX").Rows(i).Item("Nom")) + _
                        " AND Date='" + dsListe.Tables("CHANGEMANT_DE_PRIX").Rows(i).Item("Date").ToString + _
                        "' AND AncienPrixAchatHT=" + dsListe.Tables("CHANGEMANT_DE_PRIX").Rows(i).Item("AncienPrixAchatHT").ToString
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                    End Try
                End If
            Next
            init()
        End If
    End Sub
End Class