﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fEtatOrdonnanceNonRegleCNAM
    Dim cmdCNAM As New SqlCommand
    Dim daCNAM As New SqlDataAdapter
    Dim dsCNAM As New DataSet

    Dim MontantARegler As Double = 0
    Dim CondCrystalReport As String = ""

    Dim Initialisation As Boolean = False

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub Init()
        dtpDebut.Text = "01/" + Date.Today.Month.ToString.PadLeft(2, "0") + "/" + Date.Now.Year.ToString
        dtpFin.Text = Today
        dtpDebut.Focus()

        Initialisation = True
        AfficherCNAM()
    End Sub
    Public Sub AfficherCNAM()
        Dim I As Integer
        Dim Cond As String = "1=1"
        CondCrystalReport = "1=1"
        dsCNAM.Clear()

        If dtpDebut.Text <> "" And dtpFin.Text <> "" And dtpDebut.Text.Length = 10 And dtpFin.Text.Length = 10 Then
            Cond += " AND CAST(DateVente as date) BETWEEN " + Quote(dtpDebut.Text) + " AND " + Quote(dtpFin.Text)
            CondCrystalReport += " AND {Vue_OrdonnacierNonRegleCNAM.DateVente} >= DateTime('" + dtpDebut.Text + " 00:00:00')"
            CondCrystalReport += " AND {Vue_OrdonnacierNonRegleCNAM.DateVente} <= DateTime('" + dtpFin.Text + " 23:59:59')"
        End If

        If Initialisation = True Then
            cmdCNAM.CommandText = "SELECT TOP(0) "
            Initialisation = False
        Else
            cmdCNAM.CommandText = "SELECT "
        End If

        cmdCNAM.CommandText += "  " + _
                                " NomClient, " + _
                                " MatriculeCNAM, " + _
                                " NumeroVente, " + _
                                " DateVente, " + _
                                " NumeroReleve, " + _
                                " ARegler " + _
                                " FROM Vue_OrdonnacierNonRegleCNAM " + _
                                " WHERE " + Cond + " ORDER BY DateVente ASC"

        cmdCNAM.Connection = ConnectionServeur
        daCNAM = New SqlDataAdapter(cmdCNAM)
        daCNAM.Fill(dsCNAM, "CNAM")

        With gCNAM
            .Columns.Clear()
            .DataSource = dsCNAM
            .DataMember = "CNAM"
            .Rebind(False)
            .Columns("DateVente").Caption = "Date"
            .Columns("NomClient").Caption = "Client"
            .Columns("MatriculeCNAM").Caption = "Matricule"
            .Columns("NumeroVente").Caption = "Numéro Vente"
            .Columns("NumeroReleve").Caption = "Relevé"
            .Columns("ARegler").Caption = "A régler"
            .Columns("DateVente").NumberFormat = "dd/MM/yyyy"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("DateVente").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("DateVente").Width = 100
            .Splits(0).DisplayColumns("MatriculeCNAM").Width = 150
            .Splits(0).DisplayColumns("MatriculeCNAM").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("NumeroVente").Width = 150
            .Splits(0).DisplayColumns("NumeroVente").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("NumeroReleve").Width = 150
            .Splits(0).DisplayColumns("NumeroReleve").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("NomClient").Width = 200

            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gCNAM)
        End With
    End Sub

    Private Sub gCNAM_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gCNAM.FetchRowStyle
        'e.CellStyle.Font = New System.Drawing.Font("Calibri", 9, FontStyle.Regular)
    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherCNAM()
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDebut.TextChanged

    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            gCNAM.Focus()
            AfficherCNAM()
            CalculValeur()
        End If
    End Sub

    Private Sub dtpFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpFin.TextChanged

    End Sub

    Private Sub CalculValeur()
        MontantARegler = 0.0
        If gCNAM.RowCount <> 0 Then
            For I As Integer = 0 To gCNAM.RowCount - 1
                MontantARegler += gCNAM(I, "ARegler")
            Next
            tMontantARegler.Text = MontantARegler.ToString("### ### ##0.000") 'Format(MontantARegler, "0.000")
        End If
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        If gCNAM.RowCount > 0 Then
            Dim I As Integer
            For I = 0 To fMain.Tab.TabPages.Count - 1
                If fMain.Tab.TabPages(I).Text = "Imprimer les ordonnances non réglés par la CNAM" Then
                    fMain.Tab.TabPages(I).Show()
                    Exit Sub
                End If
            Next
            CR.FileName = Application.StartupPath + "\EtatOrdonnancierNonRegleCNAM.rpt"
            CR.SetParameterValue("debut", dtpDebut.Text)
            CR.SetParameterValue("fin", dtpFin.Text)

            Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent
            CR.RecordSelectionFormula = CondCrystalReport
            Dim MyViewer As New fViewer
            MyViewer.CRViewer.ReportSource = CR
            fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
            fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
            fMain.Tab.SelectedTab.Text = "Imprimer les ordonnances non réglés par la CNAM"
        Else
            MsgBox("Liste vide !", MsgBoxStyle.Critical, "Erreur")
            dtpDebut.Focus()
        End If
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub fEtatJournalReleveCNAM_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

    End Sub
End Class