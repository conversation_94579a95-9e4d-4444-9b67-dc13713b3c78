@echo off
echo ========================================
echo    DEBUG PHARMA2000 - DIAGNOSTIC
echo ========================================
echo.

cd /d "%~dp0\bin\Debug"

echo Verification des fichiers necessaires...
echo.

echo 1. Executable principal :
if exist "Pharma2000Premium.exe" (
    echo    ✓ Pharma2000Premium.exe trouve
    dir "Pharma2000Premium.exe" | findstr "Pharma2000Premium.exe"
) else (
    echo    ✗ Pharma2000Premium.exe MANQUANT
)
echo.

echo 2. DLL ComponentOne :
if exist "C1.Win.C1Ribbon.2.dll" (
    echo    ✓ C1.Win.C1Ribbon.2.dll trouve
) else (
    echo    ✗ C1.Win.C1Ribbon.2.dll MANQUANT
)

if exist "C1.Win.2.dll" (
    echo    ✓ C1.Win.2.dll trouve
) else (
    echo    ✗ C1.Win.2.dll MANQUANT
)
echo.

echo 3. Autres DLL importantes :
if exist "Data.BusinessManagement.dll" (
    echo    ✓ Data.BusinessManagement.dll trouve
) else (
    echo    ✗ Data.BusinessManagement.dll MANQUANT
)

if exist "BLL.BusinessManagement.dll" (
    echo    ✓ BLL.BusinessManagement.dll trouve
) else (
    echo    ✗ BLL.BusinessManagement.dll MANQUANT
)
echo.

echo 4. Fichiers de configuration :
if exist "Pharma2000Premium.exe.config" (
    echo    ✓ Fichier de configuration trouve
) else (
    echo    ✗ Fichier de configuration MANQUANT
)
echo.

echo ========================================
echo    TENTATIVE DE LANCEMENT AVEC DEBUG
echo ========================================
echo.

echo Lancement avec capture d'erreurs...
echo (L'application va se lancer - observez les erreurs)
echo.

echo Appuyez sur une touche pour lancer...
pause >nul

echo Demarrage...
start /wait "" "Pharma2000Premium.exe"

echo.
echo L'application s'est fermee.
echo.
echo Si l'application s'est fermee immediatement, les causes possibles sont :
echo 1. Probleme de connexion base de donnees
echo 2. DLL ComponentOne manquantes
echo 3. Erreur dans le formulaire de login
echo 4. Probleme de permissions
echo.

echo Appuyez sur une touche pour fermer...
pause >nul
