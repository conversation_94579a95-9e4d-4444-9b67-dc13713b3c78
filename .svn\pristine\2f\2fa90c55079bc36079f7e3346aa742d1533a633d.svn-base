﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports Microsoft.Reporting.WinForms
Imports System.Linq.Dynamic


Public Class fEtatJournalDesVentes
    Dim _SalesReportService As New Bll.Reporting.SalesReport
    Dim _VOrderBy As String = "Id"
    Dim _VAscDesc As String = "Asc"

    Dim Initialisation As Boolean = False
    Dim StrSQL As String = ""
    Dim cmdVente As New SqlCommand
    Dim daVente As New SqlDataAdapter
    Dim dsVente As New DataSet
    Public Source As String = ""
    Dim TotalExonore As Double = 0.0
    Dim TotalBaseTVA6 As Double = 0.0
    Dim TotalBaseTVA12 As Double = 0.0
    Dim TotalBaseTVA18 As Double = 0.0
    Dim TotalHT As Double = 0.0
    Dim TotalTVA6 As Double = 0.0
    Dim TotalTVA12 As Double = 0.0
    Dim TotalTVA18 As Double = 0.0
    Dim TotalTVA As Double = 0.0
    Dim TotalTTC As Double = 0.0
    Dim TotalHR As Double = 0.0

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub init()
        Try
            dsVente.Clear()
            gVentes.ClearFields()
        Catch
        End Try

        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = "SELECT 2 AS Code, 'Tous' AS Libelle UNION SELECT 1 AS Code, 'TVA exonéré' AS Libelle UNION SELECT 0 AS Code, 'TVA non exonéré' AS Libelle "
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "Exonerer")
        cmbExonerer.DataSource = dsVente.Tables("Exonerer")
        cmbExonerer.ValueMember = "Code"
        cmbExonerer.DisplayMember = "Libelle"
        cmbExonerer.ColumnHeaders = False
        cmbExonerer.Splits(0).DisplayColumns("Code").Width = 0
        cmbExonerer.Splits(0).DisplayColumns("Code").Visible = False
        cmbExonerer.Splits(0).DisplayColumns("Libelle").Width = 10
        cmbExonerer.ExtendRightColumn = True
        cmbExonerer.SelectedValue = 2

        dtpDebut.Text = "01/" + Date.Today.Month.ToString.PadLeft(2, "0") + "/" + Date.Now.Year.ToString
        dtpFin.Text = NombreDesJoursDuMois(Date.Today.Month, Date.Today.Year).ToString + "/" + Date.Today.Month.ToString.PadLeft(2, "0") + "/" + Date.Now.Year.ToString
        dtpDebut.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDebut.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpFin.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpFin.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDebut.Focus()
        Initialisation = True

        rdbTous.Checked = True
        chbDetail.Checked = True
        If ModeADMIN = "ADMIN" Then
            gbTotal.Visible = True
        Else
            gbTotal.Visible = False
        End If

    End Sub

    Public Sub AfficherVentes()
        Dim I As Integer
        Dim Cond As String = "1=1"

        Dim NombreVente As Integer = 0
        Dim List

        If dtpDebut.Text = "" Then
            dtpDebut.Value = Date.Now
        End If

        If dtpFin.Text = "" Then
            dtpFin.Value = Date.Now
        End If



        List = New Library.SortableBindingList(Of Data.Reporting.P_Report_EtatJournalDesVentes_Result)(_SalesReportService.GetEtatJournalDesVentes(
                                                                                                        cmbExonerer.SelectedValue(),
                                                                                                        dtpDebut.Value, _
                                                                                                        dtpFin.Value, _
                                                                                                        chHR.Checked, _
                                                                                                        IIf(rdbTous.Checked, 2, IIf(rdbFacturees.Checked, 1, 0)), _
                                                                                                        chbDetail.Checked))





        Try


            With gVentes
                .Columns.Clear()
                .DataSource = List
                .Rebind(False)
                .Columns("Date").Caption = "Date"
                .Columns("Exonore").Caption = "Exonore"
                .Columns("BaseTVA18").Caption = "Base TVA 19"
                .Columns("BaseTVA12").Caption = "Base TVA 13"
                .Columns("BaseTVA6").Caption = "Base TVA 7"
                .Columns("TotalHT").Caption = "Total HT"
                .Columns("TVA18").Caption = "TVA 19"
                .Columns("TVA12").Caption = "TVA 13"
                .Columns("TVA6").Caption = "TVA 7"

                .Columns("TotalTTC").Caption = "Total TTC"
                '.Columns("Nom").Caption = "Client"
                .Columns("NumeroFacture").Caption = "Numero Facture"
                ' .Columns("Vide").Caption = ""

                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
                Next

                .Splits(0).DisplayColumns("Date").Width = 110        '
                .Splits(0).DisplayColumns("Id").Visible = False
                '.Splits(0).DisplayColumns("BaseTVA18").Visible = False
                '.Splits(0).DisplayColumns("TotalHT").Visible = False
                '.Splits(0).DisplayColumns("TVA").Visible = False
                '.Splits(0).DisplayColumns("TotalTTC").Visible = False
                '.Splits(0).DisplayColumns("TotalTVA").Visible = False
                '.Splits(0).DisplayColumns("Nom").Visible = False
                '.Splits(0).DisplayColumns("NumeroFacture").Visible = False
                '.Splits(0).DisplayColumns("Vide").Visible = False
                '.Splits(0).DisplayColumns("HR").Visible = False

                '.Splits(0).SplitSize = 1
                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True


                .Splits(0).DisplayColumns("Date").Visible = True
                .Splits(0).DisplayColumns("Nom").Width = 200
                .Splits(0).DisplayColumns("Exonore").Style.HorizontalAlignment = AlignHorzEnum.Far
                ' .Splits(0).DisplayColumns("BaseTVA18").Width = 120
                '.Splits(0).DisplayColumns("BaseTVA18").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalHT").Width = 120
                .Splits(0).DisplayColumns("TotalHT").Style.HorizontalAlignment = AlignHorzEnum.Far
                '.Splits(0).DisplayColumns("TVA").Width = 120
                '.Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalTTC").Width = 120
                .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalTVA").Width = 100
                .Splits(0).DisplayColumns("TotalTVA").Style.HorizontalAlignment = AlignHorzEnum.Far
                '.Splits(0).DisplayColumns("Nom").Width = 180
                '.Splits(0).DisplayColumns("Nom").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("NumeroFacture").Width = 120


                .Splits(0).DisplayColumns("HR").Width = 100
                .Splits(0).DisplayColumns("HR").Visible = chHR.Checked

                '.Splits(0).DisplayColumns("HR").Style.HorizontalAlignment = AlignHorzEnum.Far
                '.Splits(0).DisplayColumns("Vide").Visible = False

                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True

                'Style du Caractere et du grid
                ParametreGrid(gVentes)
            End With

            gVentes.Splits(0).DisplayColumns("HR").Visible = chHR.Checked

            TotalExonore = 0
            TotalHT = 0
            TotalTTC = 0
            TotalTVA = 0
            TotalTVA6 = 0
            TotalBaseTVA6 = 0
            TotalTVA12 = 0
            TotalBaseTVA12 = 0
            TotalTVA18 = 0
            TotalBaseTVA18 = 0
            TotalHR = 0

            For I = 0 To gVentes.RowCount - 1
                If gVentes(I, "Exonore").ToString <> "" Then
                    TotalExonore = TotalExonore + gVentes(I, "Exonore")
                End If
                If gVentes(I, "TotalHT").ToString <> "" Then
                    TotalHT = TotalHT + gVentes(I, "TotalHT")
                End If
                If gVentes(I, "TotalTTC").ToString <> "" Then
                    TotalTTC = TotalTTC + gVentes(I, "TotalTTC")
                End If

                If gVentes(I, "TotalTVA").ToString <> "" Then
                    TotalTVA = TotalTVA + gVentes(I, "TotalTVA")
                End If

                If chHR.Checked Then
                    If gVentes(I, "HR").ToString <> "" Then
                        TotalHR = TotalHR + gVentes(I, "HR")
                    End If
                End If
            Next

            Try
                For I = 0 To gVentes.RowCount - 1
                    If gVentes(I, "BaseTVA18").ToString <> "" Then
                        TotalBaseTVA18 = TotalBaseTVA18 + gVentes(I, "BaseTVA18")
                    End If
                Next
            Catch
            End Try
            Try
                For I = 0 To gVentes.RowCount - 1
                    If gVentes(I, "TVA18").ToString <> "" Then
                        TotalTVA18 = TotalTVA18 + gVentes(I, "TVA18")
                    End If
                Next
            Catch
            End Try
            Try
                For I = 0 To gVentes.RowCount - 1
                    If gVentes(I, "BaseTVA12").ToString <> "" Then
                        TotalBaseTVA12 = TotalBaseTVA12 + gVentes(I, "BaseTVA12")
                    End If
                Next
            Catch
            End Try
            Try
                For I = 0 To gVentes.RowCount - 1
                    If gVentes(I, "TVA12").ToString <> "" Then
                        TotalTVA12 = TotalTVA12 + gVentes(I, "TVA12")
                    End If
                Next
            Catch
            End Try
            Try
                For I = 0 To gVentes.RowCount - 1
                    If gVentes(I, "BaseTVA6").ToString <> "" Then
                        TotalBaseTVA6 = TotalBaseTVA6 + gVentes(I, "BaseTVA6")
                    End If
                Next
            Catch
            End Try
            Try
                For I = 0 To gVentes.RowCount - 1
                    If gVentes(I, "TVA6").ToString <> "" Then
                        TotalTVA6 = TotalTVA6 + gVentes(I, "TVA6")
                    End If
                Next
            Catch
            End Try


            lExonore.Text = TotalExonore.ToString("### ### ##0.000")
            lTotalTVA.Text = TotalTVA.ToString("### ### ##0.000")
            lTotalHT.Text = TotalHT.ToString("### ### ##0.000")
            lTotalTTC.Text = TotalTTC.ToString("### ### ##0.000")
            lHR.Text = TotalHR.ToString("### ### ##0.000")



            lTVA18.Text = TotalTVA18.ToString("### ### ##0.000")
            lBaseTVA18.Text = TotalBaseTVA18.ToString("### ### ##0.000")
            lBaseTVA6.Text = TotalBaseTVA6.ToString("### ### ##0.000")
            lTVA6.Text = TotalTVA6.ToString("### ### ##0.000")
            lBaseTVA12.Text = TotalBaseTVA12.ToString("### ### ##0.000")
            lTVA12.Text = TotalTVA12.ToString("### ### ##0.000")
            'lNombreDesVentes.Text = NombreVente
        Catch
        End Try
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherVentes()
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDebut.TextChanged
        'If dtpDebut.Text.Length = 10 Then
        '    AfficherVentes()
        'End If
    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            Try
                dtpFin.ValidateText()
            Catch
            End Try
            AfficherVentes()
        End If
    End Sub

    Private Sub dtpFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpFin.TextChanged

    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim _Parameters As New List(Of ReportParameter)()

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Reporting Ventes" Then
                num = I
            End If
        Next
        Dim dt

        Dim MyViewer As New fImpressionReportingVente

        Dim _Exonerer As New ReportParameter()
        _Exonerer.Name = "Exonorertva"
        _Exonerer.Values.Add(cmbExonerer.SelectedValue)
        _Parameters.Add(_Exonerer)

        Dim _DateDebut As New ReportParameter()
        _DateDebut.Name = "DateDebut"
        _DateDebut.Values.Add(dtpDebut.Value)
        _Parameters.Add(_DateDebut)

        Dim _DateFin As New ReportParameter()
        _DateFin.Name = "DateFin"
        _DateFin.Values.Add(dtpFin.Value)
        _Parameters.Add(_DateFin)

        Dim _TenirCompteHonoraire As New ReportParameter()
        _TenirCompteHonoraire.Name = "TenirCompteHonoraire"
        _TenirCompteHonoraire.Values.Add(chHR.Checked)
        _Parameters.Add(_TenirCompteHonoraire)

        Dim _Facturer As New ReportParameter()
        _Facturer.Name = "Facturer"
        _Facturer.Values.Add(IIf(rdbTous.Checked, 2, IIf(rdbFacturees.Checked, 1, 0)))
        _Parameters.Add(_Facturer)

        Dim _Detail As New ReportParameter()
        _Detail.Name = "Detail"
        _Detail.Values.Add(chbDetail.Checked)
        _Parameters.Add(_Detail)


        dt = _SalesReportService.GetEtatJournalDesVentes(
                                                        cmbExonerer.SelectedValue(),
                                                        dtpDebut.Value, _
                                                        dtpFin.Value, _
                                                        chHR.Checked, _
                                                        IIf(rdbTous.Checked, 2, IIf(rdbFacturees.Checked, 1, 0)), _
                                                        chbDetail.Checked).OrderBy(_VOrderBy + " " + _VAscDesc)





        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Journaldesventes", dt))
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Pharmacie", _SalesReportService.GetPharmacie()))
        MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatJournalDesVentes.rdl"



        MyViewer.ReportViewer1.LocalReport.SetParameters(_Parameters)

        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel1)

        MyViewer.ReportViewer1.RefreshReport()

        fMain.Tab.SelectedTab.Text = "Impression Reporting Ventes"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    Private Sub chHR_CheckedChanged(sender As System.Object, e As System.EventArgs) Handles chHR.CheckedChanged
        AfficherVentes()
        lHR.Visible = chHR.Checked
        Label7.Visible = chHR.Checked
    End Sub

    Private Sub bExportExcel_Click(sender As System.Object, e As System.EventArgs) Handles bExportExcel.Click
        Dim tab(1) As String
        Dim tabIndice(2), tabValeur(2) As String
        Dim x As String


        'tabIndice(0) = "0"
        'tabIndice(1) = "1"
        'tabIndice(2) = "2"

        'tabValeur(0) = "Total"
        'tabValeur(1) = lBaseTVA6.Text
        'tabValeur(2) = lTVA6.Text

        'x = dtpDebut.Text
        'tab(0) = "Période DU : " + x + "  AU :  "
        'x = dtpFin.Text
        'tab(0) = tab(0) + x


        GeneralExportExcel(gVentes, "", "", tab, tabIndice, tabValeur)
    End Sub

    Private Sub cmbExonerer_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbExonerer.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbExonerer.Text = cmbExonerer.WillChangeToText
            AfficherVentes()
            dtpDebut.Focus()
        Else
            cmbExonerer.OpenCombo()
        End If
    End Sub

    Private Sub rdbTous_CheckedChanged(sender As Object, e As EventArgs) Handles rdbTous.CheckedChanged
        chbDetail.Visible = rdbTous.Checked
        chbDetail.Checked = True
        If rdbTous.Checked Then
            AfficherVentes()
        End If
    End Sub

    Private Sub rdbFacturees_CheckedChanged(sender As Object, e As EventArgs) Handles rdbFacturees.CheckedChanged
        If rdbFacturees.Checked Then
            AfficherVentes()
        End If
    End Sub

    Private Sub rdbNonFacturees_CheckedChanged(sender As Object, e As EventArgs) Handles rdbNonFacturees.CheckedChanged
        If rdbNonFacturees.Checked Then
            AfficherVentes()
        End If
    End Sub

    Private Sub gVentes_AfterSort(sender As Object, e As FilterEventArgs) Handles gVentes.AfterSort
        Try
            _VOrderBy = e.Condition.Substring(0, e.Condition.IndexOf(" "))
            _VAscDesc = e.Condition.Substring(e.Condition.IndexOf(" ") + 1, e.Condition.Length - e.Condition.IndexOf(" ") - 1)
        Catch ex As Exception
            _VOrderBy = e.Condition
            _VAscDesc = "Asc"
        End Try
    End Sub

    Private Sub chbDetail_CheckedChanged(sender As Object, e As EventArgs) Handles chbDetail.CheckedChanged
        AfficherVentes()

        bFacturerJournee.Visible = chbDetail.Checked
        bImrimerFacture.Visible = chbDetail.Checked
    End Sub

    Private Sub bFacturerJournee_Click(sender As Object, e As EventArgs) Handles bFacturerJournee.Click

        Dim NumeroFacture As String
        Dim FacturerJournee As Boolean = False
        Dim cmd As New SqlCommand

        Try
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "SELECT COUNT(*) FROM FACTURATION_JOURNALIERE WHERE CONVERT(Date, Date) = CONVERT(Date, " + Quote(gVentes.Columns("Date").Value.ToString) + ") "
            FacturerJournee = Not (cmd.ExecuteScalar > 0)
        Catch ex As Exception
            FacturerJournee = False
        End Try

        If FacturerJournee Then


            NumeroFacture = fVente.RecupereNumeroSequentiel("NumeroSequentielFacture")
            '''''''''''''''''''''''''''''num facture sequentiel
            Try
                Dim numeroConvertit As String = ""
                For I = 0 To 5 - NumeroFacture.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                NumeroFacture = Year(gVentes.Columns("Date").Value.ToString).ToString.Substring(2, 2) + numeroConvertit + NumeroFacture
            Catch ex As Exception
            End Try
            '''''''''''''''''''''''''''''''

            cmd.Connection = ConnectionServeur
            cmd.CommandText = "INSERT INTO FACTURATION_JOURNALIERE VALUES (" + Quote(gVentes.Columns("Date").Value.ToString) + ", " + NumeroFacture + ")"
            cmd.ExecuteNonQuery()

            cmd.Connection = ConnectionServeur
            cmd.CommandText = "UPDATE MOUVEMENT_ETATS SET NumeroFacture = " + Quote(NumeroFacture) + " WHERE CONVERT(Date, Date) = CONVERT(Date, " + Quote(gVentes.Columns("Date").Value.ToString) + ") AND (NumeroFacture IS NULL OR NumeroFacture = '') "
            cmd.ExecuteNonQuery()

            MsgBox("opération terminé avec succès")

            AfficherVentes()

        Else

            MsgBox("journée déja facturée")

        End If

    End Sub


    Private Sub bImrimerFacture_Click(sender As Object, e As EventArgs) Handles bImrimerFacture.Click
        Dim _Parameters As New List(Of ReportParameter)()

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Reporting Ventes" Then
                num = I
            End If
        Next
        Dim dt

        Dim MyViewer As New fImpressionReportingVente

       

        Dim _NumeroFacture As New ReportParameter()
        _NumeroFacture.Name = "NumeroFacture"
        _NumeroFacture.Values.Add(gVentes.Columns("NumeroFacture").Value.ToString)
        _Parameters.Add(_NumeroFacture)


        dt = _SalesReportService.GetEtatFacureJournalierer(gVentes.Columns("NumeroFacture").Value.ToString)

        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_EtatFacureJournaliere", dt))
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Pharmacie", _SalesReportService.GetPharmacie()))
        MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatFacureJournaliere.rdl"



        MyViewer.ReportViewer1.LocalReport.SetParameters(_Parameters)

        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel1)

        MyViewer.ReportViewer1.RefreshReport()

        fMain.Tab.SelectedTab.Text = "Impression Reporting Ventes"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub
End Class