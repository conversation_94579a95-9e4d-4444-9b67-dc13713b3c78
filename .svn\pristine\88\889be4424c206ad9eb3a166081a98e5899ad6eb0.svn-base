﻿Public Class ImageViewer

    Dim currentImage As Image = Nothing
    Dim iCurrPage As Integer = 1
    Dim iPages As Integer = 1

    Public Sub LoadImage(ByVal imgStream As System.IO.Stream)
        picLabel.Image = Nothing
        If Not currentImage Is Nothing Then
            currentImage.Dispose()
            currentImage = Nothing
        End If
            

        currentImage = Image.FromStream(imgStream)
        iCurrPage = 1
        Me.RefreshImage()

    End Sub

    Private Sub RefreshImage()
        iPages = currentImage.GetFrameCount(System.Drawing.Imaging.FrameDimension.Page)
        lblNumOfLabels.Text = "Label " + iCurrPage.ToString() + " of " + iPages.ToString()
        currentImage.SelectActiveFrame(System.Drawing.Imaging.FrameDimension.Page, iCurrPage - 1)
        picLabel.Image = New Bitmap(currentImage)
        Me.SetImageLocation()
    End Sub

    Private Sub SetImageLocation()
        Dim x As Integer = 0
        Dim y As Integer = 0

        If picLabel.Width > pnlContainer.ClientRectangle.Width Then
            x = 0
        Else
            x = (pnlContainer.ClientRectangle.Width - picLabel.Width) / 2
        End If

        If picLabel.Height > pnlContainer.ClientRectangle.Height Then
            y = 0
        Else

            y = (pnlContainer.ClientRectangle.Height - picLabel.Height) / 2
        End If

        picLabel.Location = New Point(x, y)

    End Sub

    Private Sub ImageViewer_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Resize

        SetImageLocation()

    End Sub

    Private Sub btnPrev_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrev.Click
        If iCurrPage > 1 Then
            iCurrPage -= 1
            Me.RefreshImage()
        End If
    End Sub

    Private Sub btnNext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNext.Click
        If iCurrPage < iPages Then
            iCurrPage += 1
            Me.RefreshImage()
        End If
    End Sub
End Class
