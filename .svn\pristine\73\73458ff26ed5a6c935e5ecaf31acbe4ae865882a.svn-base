//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class MOUVEMENT_ETATS
    {
        public int Id { get; set; }
        public string TypeOperation { get; set; }
        public Nullable<System.DateTime> Date { get; set; }
        public string NumeroOperation { get; set; }
        public Nullable<decimal> Credit { get; set; }
        public Nullable<decimal> Debit { get; set; }
        public string CodeClient { get; set; }
        public string CodeMutuelle { get; set; }
        public Nullable<decimal> TotalVenteHT { get; set; }
        public Nullable<decimal> TotalVenteTTC { get; set; }
        public Nullable<decimal> MontantCnam { get; set; }
        public Nullable<decimal> MontantMutuelle { get; set; }
        public string CodeFournisseur { get; set; }
        public Nullable<decimal> TotalAchatHT { get; set; }
        public Nullable<decimal> TotalAchatTTC { get; set; }
        public string CodeArticle { get; set; }
        public string Designation { get; set; }
        public Nullable<int> CodeCategorie { get; set; }
        public Nullable<int> CodeForme { get; set; }
        public Nullable<int> Quantite { get; set; }
        public Nullable<int> QuantiteUnitaire { get; set; }
        public Nullable<decimal> PrixVenteHT { get; set; }
        public Nullable<decimal> PrixVenteTTC { get; set; }
        public Nullable<decimal> Honoraire { get; set; }
        public Nullable<bool> Exonorertva { get; set; }
        public Nullable<decimal> Tva { get; set; }
        public Nullable<decimal> TotalTVA { get; set; }
        public Nullable<decimal> PrixAchatHT { get; set; }
        public Nullable<decimal> PrixAchatTTC { get; set; }
        public Nullable<decimal> Remise { get; set; }
        public Nullable<decimal> TotalRemise { get; set; }
        public string NumeroReglement { get; set; }
        public Nullable<int> CodeNatureReglement { get; set; }
        public Nullable<decimal> MontantRegle { get; set; }
        public string NumeroFacture { get; set; }
        public Nullable<System.DateTime> DateEcheance { get; set; }
        public string CodePersonnel { get; set; }
        public string LibellePoste { get; set; }
        public Nullable<bool> Vider { get; set; }
        public Nullable<decimal> Recu { get; set; }
        public Nullable<bool> Supprimer { get; set; }
        public string NumeroLot { get; set; }
    }
}
