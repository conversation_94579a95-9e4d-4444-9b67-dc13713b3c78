//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class REGLEMENT_MUTUELLE
    {
        public REGLEMENT_MUTUELLE()
        {
            this.REGLEMENT_MUTUELLE_VENTE = new HashSet<REGLEMENT_MUTUELLE_VENTE>();
        }
    
        public int NumeroReglementMutuelle { get; set; }
        public string LibelleReglement { get; set; }
        public int CodeNatureReglement { get; set; }
        public System.DateTime Date { get; set; }
        public Nullable<System.DateTime> DateEcheance { get; set; }
        public decimal Montant { get; set; }
        public string NumeroCheque { get; set; }
        public string LibellePoste { get; set; }
        public string NomInscritSurLeCheque { get; set; }
        public string CodeMutuelle { get; set; }
        public Nullable<int> CodeBanque { get; set; }
        public bool Vider { get; set; }
        public bool Encaisse { get; set; }
        public int CodePersonnel { get; set; }
        public bool AppliquerRetenueSource { get; set; }
        public decimal MontantRetenueSource { get; set; }
    
        public virtual BANQUE BANQUE { get; set; }
        public virtual MUTUELLE MUTUELLE { get; set; }
        public virtual ICollection<REGLEMENT_MUTUELLE_VENTE> REGLEMENT_MUTUELLE_VENTE { get; set; }
    }
}
