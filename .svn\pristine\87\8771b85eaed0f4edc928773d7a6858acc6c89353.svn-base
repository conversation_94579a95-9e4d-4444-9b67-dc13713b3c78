//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class REGLEMENT_FOURNISSEUR
    {
        public REGLEMENT_FOURNISSEUR()
        {
            this.REGLEMENT_FOURNISSEUR_ACHAT = new HashSet<REGLEMENT_FOURNISSEUR_ACHAT>();
        }
    
        public int NumeroReglementFournisseur { get; set; }
        public string LibelleReglement { get; set; }
        public int CodeNatureReglement { get; set; }
        public System.DateTime Date { get; set; }
        public Nullable<System.DateTime> DateEcheance { get; set; }
        public decimal Montant { get; set; }
        public string NumeroCheque { get; set; }
        public string NumeroPoste { get; set; }
        public string NomInscritSurLeCheque { get; set; }
        public string CodeFournisseur { get; set; }
        public Nullable<int> CodeBanque { get; set; }
        public bool Vider { get; set; }
        public bool Encaisse { get; set; }
        public string CodePersonnel { get; set; }
    
        public virtual BANQUE BANQUE { get; set; }
        public virtual FOURNISSEUR FOURNISSEUR { get; set; }
        public virtual NATURE_REGLEMENT NATURE_REGLEMENT { get; set; }
        public virtual ICollection<REGLEMENT_FOURNISSEUR_ACHAT> REGLEMENT_FOURNISSEUR_ACHAT { get; set; }
        public virtual UTILISATEUR UTILISATEUR { get; set; }
    }
}
