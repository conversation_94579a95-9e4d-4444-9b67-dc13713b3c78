﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fVenteJusteAffichage

    Dim cmdChargement As New SqlCommand
    Dim cbChargement As New SqlCommandBuilder
    Dim dsChargement As New DataSet
    Dim daChargement As New SqlDataAdapter

    Public NumeroVente As String = ""

    Public NouvelleVente As DataRow = Nothing 'datarow pour charger l'entête dans la datatable FACTURE_CLIENT
    Public NouvelArticle As DataRow = Nothing 'datarow pour charger les détails dans la datatable FACTURE_CLIENT_DETAIL
    Public dr As DataRow = Nothing 'datarow pour charger dans la datatable ARTICLE qui contient les articles recherchés pendant la saisie de la designation
    Public LigneCourantEnregistrement As DataRow = Nothing  'datarow pour parcourir la datatable detail pour l'enregistrement

    Public TotalTTC As Double = 0.0
    Public TotalHT As Double = 0.0
    Public TotalRemise As Double = 0.0
    Public TVA As Double = 0.0
    Public Timbre As Double = 0.3
    Public MontantCNAM As Double = 0.0
    Public MontantMutuelle As Double = 0.0
    Public TotalNetSansRemise As Double = 0.0

    '------------------------------------------------- les informations de paiement (retour de fenetre paiement)
    Public PaiementOkNo As String = ""
    Public Operateur As Integer = 0
    Public MonatantRecu As Double = 0.0
    Public ModePaiement As Integer = 0
    Public NumeroCheque As String = "-"
    Public MontantCheque As Double = 0.0
    '------------------------------------------------- les information CNAM (retour de maquette CNAM)
    Public CodeMalade As Integer = 0
    Public CodeAPCI As Integer = 0
    Public DureeTraitement As Integer = 0
    Public ConfirmerOkNo As Boolean = False
    Public NoteVente As String = ""
    Public MontantPrixEnChargeParLaCNAMAppareillage As Double = 0.0

    Public NomMalade As String = ""
    Public Rang As Integer = 0
    Public DateNaissance As Date
    Public CodeLienDeParente As Integer = 0
    Public LibelleLienDeParente As String = ""

    '---------------------------------------- variable pour attribuer a partir d'une vente un code cnam
    '----------------------------------------  à un client cnamiste et manque un code 
    Dim NouveuxCodeCnam As Boolean = False
    '---------------------------------------- variable pour attribuer a partir d'une vente un code cnam
    '---------------------------------------- à un Medecin cnamiste et manque un code 
    Dim NouveuxCodeCnamMedecin As Boolean = False
    '---------------------------------------- variable de retour de la liste des ventes en instance
    Dim NumeroventeInstance As String = ""
    '---------------------------------------- variables pour confirmer la mise en instance d'une vente
    Dim ComfirmerMettreEnINstance As Boolean = False
    Dim ConfirmerInstance As Boolean = False
    Dim NomInstance As String = ""
    Dim CodeOperateurInstance As String = ""
    '---------------------------------------- variable pour recuperer le code article à partir de la fenêtre recherchre multicritere
    Dim CodeArticleRechercheMC As String = ""
    '---------------------------------------- variable pour que l'apuit sur la touche ok d'un message d'information ne sera pas traité sur la liste
    Dim OKMessageInfo As Boolean = False
    '---------------------------------------- variable pour suuprimer le dernier lettre qui rend la designation introuvable lors d'un saisi dans la liste 
    Public NouveauValeur As String = ""

    Dim NombreVenteInstance As Integer = 0

    '---------------------------------------- variable de parametrage PHARMA
    Dim ValiderQteDe1 As Boolean = False

    Dim ValeurEntree As String = ""

    Dim NomTableVente As String = "" ' le variable se change selon 
    Public NumeroVenteS As Boolean = False  ' verifier l'existance de S vente supprimée

    Dim StrSQL As String = ""

    Private Sub affciherInformationSuppression()

        Try

            'chargement des Medecins
            StrSQL = "SELECT NomPersonnelSupprime, DateSuppression FROM VENTE_SUPPRIME WHERE NumeroVente = '" & NumeroVente.Substring(2, NumeroVente.Length - 2) & "'"
            cmdChargement.Connection = ConnectionServeur
            cmdChargement.CommandText = StrSQL
            daChargement = New SqlDataAdapter(cmdChargement)
            daChargement.Fill(dsChargement, "INFO_SUPRESSION")

            lSupprimePar.Visible = True
            lSupprimeLe.Visible = True

            lSupprimePar.Text = "Supprimée par: " & dsChargement.Tables("INFO_SUPRESSION").Rows(0)("NomPersonnelSupprime").ToString
            lSupprimeLe.Text = "Supprimée le: " & dsChargement.Tables("INFO_SUPRESSION").Rows(0)("DateSuppression").ToString

        Catch ex As Exception

        End Try

    End Sub

    Public Sub init()

        Dim i As Integer = 0

        lSupprimePar.Visible = False
        lSupprimeLe.Visible = False

        If NumeroVente.Substring(0, 1) = "S" Then
            lTitre.Text = "AFFICHAGE DE LA VENTE SUPPRIMEE: " + NumeroVente
            affciherInformationSuppression()
        Else
            lTitre.Text = "AFFICHAGE DE LA VENTE: " + NumeroVente
        End If

        If (dsChargement.Tables.IndexOf("VENTE_DETAILS") > -1) Then
            dsChargement.Tables("VENTE_DETAILS").Clear()
        End If
        If (dsChargement.Tables.IndexOf("VENTE") > -1) Then
            dsChargement.Tables("VENTE").Clear()
        End If

        'chargement des Clients
        StrSQL = "SELECT CodeClient,Nom FROM CLIENT WHERE CodeSituation <> 3 ORDER BY Nom ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "CLIENT")
        cmbClient.DataSource = dsChargement.Tables("CLIENT")
        cmbClient.ValueMember = "CodeClient"
        cmbClient.DisplayMember = "Nom"
        cmbClient.ColumnHeaders = False
        cmbClient.Splits(0).DisplayColumns("CodeClient").Width = 0
        cmbClient.Splits(0).DisplayColumns("Nom").Width = 160

        'chargement des Medecins
        StrSQL = "SELECT CodeMedecin,NomMedecin FROM MEDECIN ORDER BY NomMedecin ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "MEDECIN")
        cmbMedecin.DataSource = dsChargement.Tables("MEDECIN")
        cmbMedecin.ValueMember = "CodeMedecin"
        cmbMedecin.DisplayMember = "NomMedecin"
        cmbMedecin.ColumnHeaders = False
        cmbMedecin.Splits(0).DisplayColumns("CodeMedecin").Width = 0
        cmbMedecin.Splits(0).DisplayColumns("NomMedecin").Width = 160

        'chargement des Mutuelles
        StrSQL = "SELECT CodeMutuelle,NomMutuelle FROM MUTUELLE ORDER BY NomMutuelle ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "MUTUELLE")
        cmbMutuelle.DataSource = dsChargement.Tables("MUTUELLE")
        cmbMutuelle.ValueMember = "CodeMutuelle"
        cmbMutuelle.DisplayMember = "NomMutuelle"
        cmbMutuelle.ColumnHeaders = False
        cmbMutuelle.Splits(0).DisplayColumns("CodeMutuelle").Width = 0
        cmbMutuelle.Splits(0).DisplayColumns("NomMutuelle").Width = 160

        'chargement des tranches
        StrSQL = "SELECT NumeroTranche FROM TRANCHE ORDER BY NumeroTranche ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "TRANCHE")
        cmbTranche.DataSource = dsChargement.Tables("TRANCHE")
        cmbTranche.DisplayMember = "NumeroTranche"
        cmbTranche.ColumnHeaders = False
        cmbTranche.Splits(0).DisplayColumns("NumeroTranche").Width = 160


        'chargement des Entêtes des ventes  
        If NumeroVente.Substring(0, 1) = "S" Then
            NomTableVente = "VENTE_SUPPRIME"
            NumeroVente = NumeroVente.Substring(2, NumeroVente.Length - 2)
            NumeroVenteS = True
        Else
            NomTableVente = "VENTE"
            NumeroVenteS = False
        End If

        StrSQL = "SELECT * FROM " & NomTableVente & " WHERE NumeroVente='" + NumeroVente + "'"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "VENTE")
        If dsChargement.Tables("VENTE").Rows.Count > 0 Then
            NumeroVente = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1).Item("NumeroVente")

            cmbMutuelle.SelectedValue = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("CodeMutuelle")
            lNumeroVente.Text = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("NumeroVente")
            lDateFacture.Text = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("Date")
            cmbClient.SelectedValue = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("CodeClient")
            cmbClient.Text = RecupererValeurExecuteScalaire("Nom", "Client", "CodeClient", dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("CodeClient"))
            lNumerofacture.Text = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("NumeroFacture").ToString

            TotalTTC = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("TotalTTC")
            TotalHT = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("TotalHT")
            TVA = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("TVA")

            lRemise.Text = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("TotalRemise")

            lTotalTTC.Text = Math.Round(dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("TotalTTC"), 3)
            lMontantCNAM.Text = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("MontantCnam")
            lMontantMutuelle.Text = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("MontantMutuelle")
            lRecu.Text = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("Recu")

            lNetAPayerAfficher.Visible = False
            tNetAPayer.Visible = False
            tRemisePourcentage.Visible = False
            lPourcent.Visible = False
            lRemisePourcentAfficher.Visible = False

            lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("CodePersonnel"))
            tCodeMedecin.Value = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("CodeMedecinPrescripteur")

            cmbMedecin.SelectedValue = RecupererValeurExecuteScalaire("NomMedecin", "MEDECIN", "CodeMedecin", tCodeMedecin.Text)
            dtpDateOrdonnance.Value = dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("DateOrdonnance")
            lModePaiement.Text = RecupererValeurExecuteScalaire("LibelleNatureReglement", "NATURE_REGLEMENT", "CodeNatureReglement", dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("CodeNatureReglement"))

            If dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("TiersPayant") = True Then
                rdbTiersPayant.Checked = True
            End If
            If dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("PriseEnCharge") = True Then
                rdbPriseEnCharge.Checked = True
            End If
            If dsChargement.Tables("VENTE").Rows(dsChargement.Tables("VENTE").Rows.Count - 1)("Appareillage") = True Then
                rdbAppareillage.Checked = True
            End If

            If lRemise.Text = "0.000" Then
                lRemise.Visible = False
                lRemiseAfficher.Visible = False
            Else
                lRemise.Visible = True
                lRemiseAfficher.Visible = True
            End If
            If lMontantCNAM.Text <> "0.000" Then
                chbCnam1.Checked = True
                chbCnam1.Visible = True
                GroupeMedecin.Visible = True
            Else
                chbCnam1.Visible = False
                chbCnam1.Checked = False
                GroupeMedecin.Visible = False
            End If

            If cmbClient.SelectedValue = "CLIPASS" Or (lMontantCNAM.Text = "0.000" And lMontantMutuelle.Text = "0.000") Then
                GroupeCnamMutuelle.Visible = False
            Else
                If chbCNAM.Checked = True Or Trim(cmbMutuelle.Text.ToUpper) <> "COMPTOIR" Then
                    GroupeCnamMutuelle.Visible = True
                Else
                    GroupeCnamMutuelle.Visible = False
                End If
            End If

        End If

        'chargement des détails des ventes 
        If NumeroVenteS = True Then
            NomTableVente = "VENTE_SUPPRIME_DETAILS"            
        Else
            NomTableVente = "VENTE_DETAILS"
        End If

        StrSQL = "SELECT " + _
                "   NumeroVente," + _
                "   (SELECT CodeABarre FROM ARTICLE WHERE CodeArticle = " & NomTableVente & ".CodeArticle) AS CodeArticle," + _
                "   MAX(NumeroLotArticle) AS NumeroLotArticle," + _
                "   Designation," + _
                "   LibelleForme," + _
                "   SUM(Qte) AS Qte," + _
                "   Stock," + _
                "   MAX(DateDePeremption) AS DateDePeremption, " + _
                "   PrixTTC," + _
                "   SUM(TotalTTC) AS TotalTTC," + _
                "   TVA," + _
                "   Remise," + _
                "   PrixHT," + _
                "   SUM(TotalHT) AS TotalHT," + _
                "   SUM(TotalTVA) AS TotalTVA," + _
                "   PrixAchat," + _
                "   Honoraire " + _
                "FROM " & NomTableVente & " " + _
                "LEFT OUTER JOIN FORME_ARTICLE ON " & NomTableVente & ".CodeForme=FORME_ARTICLE.CodeForme " + _
                "WHERE NumeroVente =" + Quote(NumeroVente) + " " + _
                "GROUP BY NumeroVente, " + _
                "" & NomTableVente & ".CodeArticle," + _
                "Designation, " + _
                "LibelleForme, " + _
                "Stock, " + _
                "PrixTTC, " + _
                "TVA, " + _
                "Remise, " + _
                "PrixHT, " + _
                "PrixAchat, " + _
                "PrixAchat, " + _
                "Honoraire"
        If NomTableVente = "VENTE_DETAILS" Then
            StrSQL += " ORDER BY MAX(ordre)"
        End If
        '                  " Group by NumeroVente, CodeArticle" + _

        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "VENTE_DETAILS")

        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsChargement
            Catch ex As Exception
            End Try
            .DataMember = "VENTE_DETAILS"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qte"
            .Columns("DateDePeremption").Caption = "Date de péremption"
            .Columns("PrixTTC").Caption = "Prix TTC "
            .Columns("TotalTTC").Caption = "Total TTC "
            .Columns("TVA").Caption = "TVA"
            .Columns("Remise").Caption = "Remise"
            .Columns("Stock").Caption = "Stock"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns(2).Style.HorizontalAlignment = AlignHorzEnum.Near
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Remise").Style.HorizontalAlignment = AlignHorzEnum.Far


            .Splits(0).DisplayColumns("NumeroVente").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Width = 80
            .Splits(0).DisplayColumns("NumeroLotArticle").Width = 0
            .Splits(0).DisplayColumns("Designation").Width = 300
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("Stock").Width = 50
            .Splits(0).DisplayColumns("DateDePeremption").Width = 70
            .Splits(0).DisplayColumns("PrixTTC").Width = 60
            .Splits(0).DisplayColumns("TotalTTC").Width = 60
            .Splits(0).DisplayColumns("TVA").Width = 50
            .Splits(0).DisplayColumns("Remise").Width = 50

            .Splits(0).DisplayColumns("NumeroVente").Visible = False
            .Splits(0).DisplayColumns("NumeroLotArticle").Width = 0
            .Splits(0).DisplayColumns("NumeroLotArticle").Visible = False
            .Splits(0).DisplayColumns("PrixHT").Width = 0
            .Splits(0).DisplayColumns("PrixHT").Visible = False
            .Splits(0).DisplayColumns("TotalHT").Width = 0
            .Splits(0).DisplayColumns("TotalHT").Visible = False
            .Splits(0).DisplayColumns("TotalTVA").Width = 50
            .Splits(0).DisplayColumns("TotalTVA").Visible = False
            .Splits(0).DisplayColumns("PrixAchat").Width = 0
            .Splits(0).DisplayColumns("PrixAchat").Visible = False
            .Splits(0).DisplayColumns("Honoraire").Width = 0
            .Splits(0).DisplayColumns("Honoraire").Visible = False

            .Splits(0).DisplayColumns("CodeArticle").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Remise").Style.BackColor = Color.FromArgb(255, 244, 252, 251)


            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gArticles)
        End With

        GroupeCNAM.Enabled = False
        GroupeMedecin.Enabled = False

        If lRemise.Text = "0.000" Then
            lRemise.Visible = False
            lRemiseAfficher.Visible = False
        Else
            lRemise.Visible = True
            lRemiseAfficher.Visible = True
        End If

        tMatricule.Visible = False
        lMatricule.Visible = False

        lInfoBulle.Text = ""

        dtpDateOrdonnance.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDateOrdonnance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler

    End Sub

    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)
       
        If e.KeyCode = Keys.F12 Then
            bQuitter_Click(o, e)
            Exit Sub
        End If

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        Me.Hide()
    End Sub

    Private Sub fVenteJusteAffichage_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        init()
    End Sub

    Private Sub bImprimer_Click(sender As Object, e As EventArgs) Handles bImprimer.Click
        Try
            Dim Apercu = False
            Dim I As Integer = 0
            Dim StrSQL As String = ""

            Dim Confirmer As Boolean = False
            Dim Ordonnance As Boolean = False
            Dim Facture As Boolean = False
            Dim BL As Boolean = False
            Dim Devis As Boolean = False
            Dim DateImpression As Date
            Dim NbreCopie As String = "1"

            Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo

            Dim pClient As String

            Dim pchbCnamAjout As CheckBox

            Dim NomAder, MatAder As String

            'Suivi du scénario
            fMessageException.Show("Vente", "fVente", "bImprimer_Click", "NoException", "NoError", "Clic sur le bouton Imprimer", False, True, False)

            Dim myImprime As New fImpressionVente

            TestPremierCliqueToucheF = 0

                myImprime.pNumeroVente = lNumeroVente.Text



            myImprime.pNumeroVente = lNumeroVente.Text    'lNumeroVente.Text

            myImprime.plTotalTTC = RecupererValeurExecuteScalaire("TotalTTC", "VENTE", "NumeroVente", lNumeroVente.Text)
            myImprime.plMontantCNAM = RecupererValeurExecuteScalaire("MontantCnam", "VENTE", "NumeroVente", lNumeroVente.Text)
            myImprime.plMontantMutuelle = RecupererValeurExecuteScalaire("MontantMutuelle", "VENTE", "NumeroVente", lNumeroVente.Text)
            myImprime.pTotalTTC = RecupererValeurExecuteScalaire("TotalTTC", "VENTE", "NumeroVente", lNumeroVente.Text)

            fImpressionVente.DateImpression = RecupererValeurExecuteScalaire("Date", "VENTE", "NumeroVente", lNumeroVente.Text)
            myImprime.plNumeroFacture = RecupererValeurExecuteScalaire("NumeroFacture", "VENTE", "NumeroVente", lNumeroVente.Text)

            pchbCnamAjout = New CheckBox

            If myImprime.plMontantCNAM > 0 Then
                pchbCnamAjout.Checked = True
            Else
                pchbCnamAjout.Checked = False
            End If
            myImprime.pchbCnam1 = pchbCnamAjout
            myImprime.pClientCode = RecupererValeurExecuteScalaire("CodeClient", "VENTE", "NumeroVente", lNumeroVente.Text)
                myImprime.pClientText = RecupererValeurExecuteScalaire("Nom", "CLIENT", "CodeClient", myImprime.pClientCode)


                ''''' New
                myImprime.pMatriculeAdherent = RecupererValeurExecuteScalaire("MatriculeMutuelle", "CLIENT", "CodeClient", myImprime.pClientCode)
                myImprime.pNomAdherent = myImprime.pClientText
                ''''''
         


            '''' 
            ' '' ''myImprime.pNumeroVente = lNumeroVente.Text
            ' '' ''myImprime.plTotalTTC = lTotalTTC.Text
            ' '' ''myImprime.plMontantCNAM = lMontantCNAM.Text
            ' '' ''myImprime.plMontantMutuelle = lMontantMutuelle.Text
            ' '' ''myImprime.pTotalTTC = TotalTTC
            ' '' ''fImpressionVente.DateImpression = lDateFacture.Text
            ' '' ''myImprime.plNumeroFacture = lNumerofacture.Text
            ' '' ''myImprime.pchbCnam1 = chbCnam1
            ' '' ''myImprime.pClientCode = cmbClient.SelectedValue
            ' '' ''myImprime.pClientText = cmbClient.Text
            ''''

            myImprime.ShowDialog()

            Confirmer = fImpressionVente.Confirmer
            pClient = fImpressionVente.pClientBL
            Apercu = myImprime.Apercu
            Ordonnance = fImpressionVente.Ordonnance
            Facture = fImpressionVente.Facture
            BL = fImpressionVente.BL
            Devis = fImpressionVente.Devis

            DateImpression = fImpressionVente.DateImpression

            NbreCopie = fImpressionVente.pNbreCopie

            'NomAder = fImpressionVente.pNomAdherent
            'MatAder = fImpressionVente.pMatriculeAdherent

            myImprime.Dispose()
            myImprime.Close()

        Catch
        End Try

    End Sub
End Class