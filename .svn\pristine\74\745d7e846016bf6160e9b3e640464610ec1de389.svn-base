//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class VENTE_INSTANCE
    {
        public VENTE_INSTANCE()
        {
            this.VENTE_INSTANCE_DETAILS = new HashSet<VENTE_INSTANCE_DETAILS>();
        }
    
        public string NumeroVenteInstance { get; set; }
        public System.DateTime Date { get; set; }
        public decimal TotalHT { get; set; }
        public decimal TotalTTC { get; set; }
        public decimal TVA { get; set; }
        public decimal TotalRemise { get; set; }
        public decimal Timbre { get; set; }
        public string CodeClient { get; set; }
        public string CodeOperateur { get; set; }
        public Nullable<int> CodeAPCI { get; set; }
        public Nullable<int> CodeDeFamille { get; set; }
        public string CodeMedecinFamille { get; set; }
        public string CodeMedecinPrescripteur { get; set; }
        public string LibellePoste { get; set; }
        public System.DateTime DateOrdonnance { get; set; }
        public decimal MontantCnam { get; set; }
        public decimal MontantMutuelle { get; set; }
        public string Note { get; set; }
        public string CodeMutuelle { get; set; }
        public string NomVenteInstance { get; set; }
    
        public virtual APCI APCI { get; set; }
        public virtual MEDECIN MEDECIN { get; set; }
        public virtual MEDECIN MEDECIN1 { get; set; }
        public virtual MUTUELLE MUTUELLE { get; set; }
        public virtual UTILISATEUR UTILISATEUR { get; set; }
        public virtual ICollection<VENTE_INSTANCE_DETAILS> VENTE_INSTANCE_DETAILS { get; set; }
    }
}
