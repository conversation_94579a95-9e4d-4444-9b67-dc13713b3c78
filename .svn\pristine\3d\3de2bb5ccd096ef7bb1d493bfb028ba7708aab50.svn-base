
/*http://www.fontsquirrel.com/fontface/generator*/
/*http://www.typo3facile.fr/2010/06/css-integrer-une-police-perso-a-son-site-web/*/

@font-face {
    font-family: 'myriad';
	src: url('myriadpro-regular-webfont.eot?') format('eot'),url('myriadpro-regular-webfont.woff') format('woff'),url('myriadpro-regular-webfont.ttf') format('truetype'),url('myriadpro-regular-webfont.svg#webfontlvblEI6b') format('svg');
    font-weight: normal;
    font-style: normal;
}

body
{
	font-family:"myriad",tahoma,arial;
	font-size:12px;
}

html,body { overflow:hidden;overflow-y: hidden;border: none;border: 0 none; }

.tr_signet
{
	height:20px;
}

#div_datas_monographie{
	scrollbar-face-color: #B1C800;
	scrollbar-arrow-color: white;
	scrollbar-track-color: white;
	scrollbar-highlight-color: #cae30b;
	scrollbar-darkshadow-color: white;
	scrollbar-shadow-color: #859603;
	scrollbar-3dlight-color: #white;
}


#conteneur
{
	/*position:absolute;
	width:100%;
	top:0px;
	left: 0px;
	padding:10px;*/
	width:100%;
}

#conteneur_hg
{
	width:7px;
	background:url(../images/coin_hg.png) top left no-repeat;
}

#conteneur_h
{
	background:url(../images/coin_h.png) top left repeat-x;
}

#conteneur_hd
{
	width:7px;
	background:url(../images/coin_hd.png) top left no-repeat;
}

#conteneur_mg
{
	background-color:#054380;
}

#conteneur_md
{
	background:url(../images/coin_md.png) top left repeat-y;
}

#conteneur_bg
{
	width:7px;
	background:url(../images/coin_bg.png) top left no-repeat;
}

#conteneur_b
{
	background:url(../images/coin_b.png) top left repeat-x;
}

#conteneur_bd
{
	width:7px;
	background:url(../images/coin_bd.png) top left no-repeat;
}

#monographie
{
	width:100%;
	background:url(../images/logoFond.png) top right no-repeat;
	background-color:#054380;
	/*background-color:#00FF00;*/
}

#conteneur_signet
{
	width:200px;
	/*background-color:transparent;*/
}

#signet
{
	width:100%;
}

.cadreblanc
{
	background:url(../images/cadre_3.png) top left repeat-y;
}

#signet_titre
{
	background:url(../images/cadre_1bis.png) top left no-repeat;
	text-align:center;
	color:#FFFFFF;
	font-size:18px;
	font-weight:500;
	background-color: transparent;
	border-right:2px solid #054380;
}

#signet_datas_bas
{
	background:url(../images/cadre_4.png) top left no-repeat;
	
}

.header_monographie_petit
{
	background:url(../images/degradeBleuPetit.png) top left repeat-x;
	
}

.header_monographie_grand
{
	background:url(../images/degradeBleuGrand.png) top left repeat-x;
	
}

.degrademilieu
{
	background:url(../images/degradeBleuGrand.png) top left repeat-x;
}

.degrademilieuDroit
{
	background:url(../images/degradeBleuDroit.png) top left repeat-x;
	background-color:#FFFFFF;
}


#monographie_titre
{
	font-family:"myriad",tahoma,arial;
	font-size:24px;
	color:#FFFFFF;
	padding-left:10px;
	padding-right:10px;
	
}

#data_monographie
{
	padding-left:10px;
	padding-right:10px;
}

#signet_div{
	overflow:auto;
	overflow-x:hidden;
	overflow-y:auto;
	height:300px;
	width:190px;
	text-align:left;
	padding-right:5px;
}

.signet_tr{
	height:23px;
	vertical-align:top;
}

.signet_td{
	color:#626262;;
	font-weight:normal;
	text-align:left;
	padding-top:3px;
	padding-bottom:3px;
}

.signet_td_puce{
	width:20px;
	text-align:center;
}

.signet_td_active{
	/*background:url(../images/test.png) top left repeat;*/
	background-color:#054380;
	color:#FFFFFF;
	font-weight:bold;
	/*background-color:#1365AB;*/
}

#div_datas_monographie{
	overflow:auto;
	/*overflow-x:hidden;*/
	overflow-y:scroll;
	height:100px;
	/*width:100%;*/
	text-align:left;
	padding-right:10px;
	/*position:relative;*/
}

.chapitre_titre{
	font-size:20px;
	color:#FFFFFF;
	font-family:"myriad",tahoma,arial;
}

.chapitre_table{
	font-family:"myriad",tahoma,arial;
	width:100%;
	background-color:#FFF;
	border-left: 1px solid #626262;
	border-top: 1px solid #626262;
	border-bottom: 1px solid #FFF;
	border-right: 1px solid #FFF;
	cellpadding:0px;
	border-collapse:collapse;
	text-align:left;
}

.chapitre_table td{
	border: 1px solid #CCC;
	color:#626262;
	padding: 8px 5px 8px 5px;
	font-size:13px;
}

.chapitre_table tr{
	vertical-align:middle;	
}

.chapitre_table_noborder{
	font-family:"myriad",tahoma,arial;
	width:100%;
	background-color:#FFF;
	border:none;
	cellpadding:0px;
	border-collapse:collapse;
	text-align:left;
}

.chapitre_table_ocp{
	font-family:"myriad",tahoma,arial;
	background-color:#FFF;
	border:none;
	border-collapse:collapse;
	text-align:left;
}

.chapitre_table_noborder td{
	border: none;
	color:#626262;
	padding: 8px 5px 8px 5px;
	font-size:13px;
}

.chapitre_table_noborder tr{
	vertical-align:middle;	
}
#chapitre_header_titre{
	font-size:15px;
	color:#c3d500;
	font-weight:bold;
	padding:5px;
}