<UserControl x:Class="PharmaModerne.UI.Views.Articles.ArticleListView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- En-tête Articles -->
        <Border Grid.Row="0" 
                Background="White" 
                CornerRadius="10" 
                Padding="20" 
                Margin="0,0,0,16"
                Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="💊" FontSize="32" VerticalAlignment="Center"/>
                    <StackPanel Margin="16,0,0,0" VerticalAlignment="Center">
                        <TextBlock Text="Gestion des Articles" 
                                 FontSize="24" 
                                 FontWeight="Bold"
                                 Foreground="DarkBlue"/>
                        <TextBlock Text="Catalogue complet avec codes-barres et scanner" 
                                 FontSize="14" 
                                 Foreground="Gray"/>
                    </StackPanel>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="➕ NOUVEL ARTICLE"
                            Command="{Binding AddArticleCommand}"
                            Padding="15,8"
                            Margin="5"
                            Background="Green"
                            Foreground="White"
                            BorderThickness="0"
                            FontWeight="Bold"/>
                    
                    <Button Content="📤 IMPORTER"
                            Command="{Binding ImportArticlesCommand}"
                            Padding="15,8"
                            Margin="5"
                            Background="Blue"
                            Foreground="White"
                            BorderThickness="0"/>
                    
                    <Button Content="📥 EXPORTER"
                            Command="{Binding ExportArticlesCommand}"
                            Padding="15,8"
                            Margin="5"
                            Background="Orange"
                            Foreground="White"
                            BorderThickness="0"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Barre de recherche avec scanner -->
        <Border Grid.Row="1" 
                Background="White" 
                CornerRadius="10" 
                Padding="20" 
                Margin="0,0,0,16"
                Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- Recherche principale -->
                <Grid Grid.Row="0" Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox Grid.Column="0"
                             Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                             FontSize="16"
                             Padding="12"
                             Margin="0,0,8,0"
                             Background="LightYellow">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>
                    <TextBlock Text="🔍 Rechercher par nom, code article, code-barres ou scanner..."
                             IsHitTestVisible="False"
                             VerticalAlignment="Center"
                             HorizontalAlignment="Left"
                             Margin="15,0,0,0"
                             Foreground="Gray"
                             FontSize="14">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <Setter Property="Visibility" Value="Collapsed"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding SearchText}" Value="">
                                        <Setter Property="Visibility" Value="Visible"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                    
                    <Button Grid.Column="1"
                            Content="📱 SCANNER"
                            Command="{Binding ActivateScannerCommand}"
                            Padding="15,8"
                            Margin="8,0"
                            Background="Orange"
                            Foreground="White"
                            BorderThickness="0"
                            FontWeight="Bold"/>
                    
                    <Button Grid.Column="2"
                            Content="🔍"
                            Command="{Binding AdvancedSearchCommand}"
                            Padding="12"
                            Margin="4,0"
                            Background="Purple"
                            Foreground="White"
                            BorderThickness="0"
                            ToolTip="Recherche avancée"/>
                    
                    <Button Grid.Column="3"
                            Content="🔄"
                            Command="{Binding RefreshCommand}"
                            Padding="12"
                            Margin="4,0"
                            Background="Gray"
                            Foreground="White"
                            BorderThickness="0"
                            ToolTip="Actualiser"/>
                </Grid>
                
                <!-- Filtres rapides -->
                <StackPanel Grid.Row="1" Orientation="Horizontal">
                    <TextBlock Text="Filtres :" 
                             VerticalAlignment="Center"
                             Margin="0,0,16,0"
                             FontWeight="Bold"/>
                    
                    <CheckBox Content="Actifs uniquement"
                            IsChecked="{Binding ShowActiveOnly}"
                            Margin="8,0"
                            FontSize="14"/>
                    
                    <CheckBox Content="Stock faible"
                            IsChecked="{Binding ShowLowStockOnly}"
                            Margin="8,0"
                            FontSize="14"/>
                    
                    <CheckBox Content="Périmés"
                            IsChecked="{Binding ShowExpiredOnly}"
                            Margin="8,0"
                            FontSize="14"/>
                    
                    <CheckBox Content="Avec code-barres"
                            IsChecked="{Binding ShowWithBarcodeOnly}"
                            Margin="8,0"
                            FontSize="14"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Liste des articles -->
        <Border Grid.Row="2" 
                Background="White" 
                CornerRadius="10" 
                Padding="0" 
                Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- En-tête de la liste -->
                <Border Grid.Row="0" 
                      Background="LightBlue"
                      Padding="16,12">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0"
                                 Text="{Binding StatusMessage}"
                                 VerticalAlignment="Center"
                                 FontWeight="Bold"
                                 FontSize="14"/>
                        
                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <TextBlock Text="{Binding TotalArticles, StringFormat='{}{0} articles'}"
                                     VerticalAlignment="Center"
                                     Margin="0,0,16,0"
                                     FontWeight="Bold"/>
                            
                            <!-- Indicateur scanner -->
                            <Border Background="Green"
                                  CornerRadius="12"
                                  Padding="8,4"
                                  Visibility="Collapsed">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📡" Foreground="White" FontSize="12"/>
                                    <TextBlock Text="Scanner Actif" 
                                             Foreground="White"
                                             Margin="4,0,0,0"
                                             FontSize="12"
                                             FontWeight="Bold"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Grid>
                </Border>
                
                <!-- DataGrid des articles -->
                <DataGrid Grid.Row="1"
                        ItemsSource="{Binding Articles}"
                        SelectedItem="{Binding SelectedArticle}"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        GridLinesVisibility="Horizontal"
                        HeadersVisibility="Column"
                        SelectionMode="Single"
                        AlternatingRowBackground="LightGray"
                        RowHeight="50"
                        FontSize="14"
                        Margin="16">
                    
                    <DataGrid.Columns>
                        <!-- Code Article -->
                        <DataGridTemplateColumn Header="Code Article" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="🏷️" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <TextBlock Text="{Binding CodeArticle}" 
                                                 VerticalAlignment="Center"
                                                 FontWeight="Bold"
                                                 FontFamily="Consolas"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <!-- Code-barres -->
                        <DataGridTemplateColumn Header="Code-barres" Width="140">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📱" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <TextBlock Text="{Binding CodeBarre}" 
                                                 VerticalAlignment="Center"
                                                 FontFamily="Consolas"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <!-- Désignation -->
                        <DataGridTemplateColumn Header="Désignation" Width="300">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock Text="{Binding Designation}" 
                                                 FontWeight="Bold"
                                                 TextWrapping="Wrap"/>
                                        <TextBlock Text="{Binding Description}" 
                                                 FontSize="12"
                                                 Foreground="Gray"
                                                 TextWrapping="Wrap"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <!-- Prix -->
                        <DataGridTemplateColumn Header="Prix" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock Text="{Binding PrixVenteHT, StringFormat=C}" 
                                                 FontWeight="Bold"/>
                                        <TextBlock Text="{Binding PrixVenteTTC, StringFormat='TTC: {0:C}'}" 
                                                 FontSize="12"
                                                 Foreground="Gray"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <!-- Stock -->
                        <DataGridTemplateColumn Header="Stock" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding QuantiteStock}" 
                                             VerticalAlignment="Center"
                                             HorizontalAlignment="Center"
                                             FontWeight="Bold">
                                        <TextBlock.Foreground>
                                            <SolidColorBrush Color="Red"/>
                                        </TextBlock.Foreground>
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <!-- Fournisseur -->
                        <DataGridTextColumn Header="Fournisseur" 
                                          Binding="{Binding FournisseurNom}" 
                                          Width="120"/>
                        
                        <!-- Statut -->
                        <DataGridTemplateColumn Header="Statut" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="Green"
                                          CornerRadius="12"
                                          Padding="8,4">
                                        <TextBlock Text="Actif"
                                                 Foreground="White"
                                                 FontSize="12"
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <!-- Actions -->
                        <DataGridTemplateColumn Header="Actions" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="👁️"
                                              Command="{Binding DataContext.ViewArticleCommand, 
                                                      RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                              CommandParameter="{Binding}"
                                              Padding="8"
                                              Margin="2"
                                              Background="Blue"
                                              Foreground="White"
                                              BorderThickness="0"
                                              ToolTip="Voir détail"/>
                                        
                                        <Button Content="✏️"
                                              Command="{Binding DataContext.EditArticleCommand, 
                                                      RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                              CommandParameter="{Binding}"
                                              Padding="8"
                                              Margin="2"
                                              Background="Orange"
                                              Foreground="White"
                                              BorderThickness="0"
                                              ToolTip="Modifier"/>
                                        
                                        <Button Content="📦"
                                              Command="{Binding DataContext.ViewStockCommand, 
                                                      RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                              CommandParameter="{Binding}"
                                              Padding="8"
                                              Margin="2"
                                              Background="Purple"
                                              Foreground="White"
                                              BorderThickness="0"
                                              ToolTip="Gestion stock"/>
                                        
                                        <Button Content="📱"
                                              Command="{Binding DataContext.ScanArticleCommand, 
                                                      RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                              CommandParameter="{Binding}"
                                              Padding="8"
                                              Margin="2"
                                              Background="Green"
                                              Foreground="White"
                                              BorderThickness="0"
                                              ToolTip="Scanner"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
        
        <!-- Pagination -->
        <Border Grid.Row="3" 
                Background="White" 
                CornerRadius="10" 
                Padding="16" 
                Margin="0,16,0,0"
                Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" 
                          Orientation="Horizontal"
                          VerticalAlignment="Center">
                    <TextBlock Text="Affichage :" Margin="0,0,8,0"/>
                    <TextBlock Text="{Binding CurrentPageStart}"/>
                    <TextBlock Text=" - " />
                    <TextBlock Text="{Binding CurrentPageEnd}"/>
                    <TextBlock Text=" sur " />
                    <TextBlock Text="{Binding TotalArticles}" FontWeight="Bold"/>
                    <TextBlock Text=" articles" Margin="0,0,16,0"/>
                    
                    <TextBlock Text="Par page :" Margin="16,0,8,0"/>
                    <ComboBox SelectedValue="{Binding PageSize}" Width="80">
                        <ComboBoxItem Content="25"/>
                        <ComboBoxItem Content="50"/>
                        <ComboBoxItem Content="100"/>
                        <ComboBoxItem Content="200"/>
                    </ComboBox>
                </StackPanel>
                
                <StackPanel Grid.Column="1" 
                          Orientation="Horizontal"
                          VerticalAlignment="Center">
                    <Button Content="⏮️"
                          Command="{Binding FirstPageCommand}"
                          IsEnabled="{Binding CanGoToPreviousPage}"
                          Padding="8"
                          Margin="2"/>
                    
                    <Button Content="◀️"
                          Command="{Binding PreviousPageCommand}"
                          IsEnabled="{Binding CanGoToPreviousPage}"
                          Padding="8"
                          Margin="2"/>
                    
                    <TextBlock Text="{Binding CurrentPage}" 
                             VerticalAlignment="Center"
                             Margin="16,0"
                             FontWeight="Bold"/>
                    <TextBlock Text="/" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalPages}" 
                             VerticalAlignment="Center"
                             Margin="0,0,16,0"/>
                    
                    <Button Content="▶️"
                          Command="{Binding NextPageCommand}"
                          IsEnabled="{Binding CanGoToNextPage}"
                          Padding="8"
                          Margin="2"/>
                    
                    <Button Content="⏭️"
                          Command="{Binding LastPageCommand}"
                          IsEnabled="{Binding CanGoToNextPage}"
                          Padding="8"
                          Margin="2"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>

</UserControl>
