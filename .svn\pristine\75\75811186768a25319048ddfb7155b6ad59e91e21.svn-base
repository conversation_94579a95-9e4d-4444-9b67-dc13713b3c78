﻿Public Class fVisionneurInteraction
    Public Chemin As String = ""
    Public Retour As Boolean = False

    Public Sub Init()
        WebBrowser.Navigate(Chemin)
    End Sub

    Private Sub fVisionneurInteraction_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Me.KeyUp
        If e.KeyCode = Keys.F3 Then
            bIgnorer_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bEffacer_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub fVisionneurInteraction_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

    End Sub

    Private Sub bIgnorer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bIgnorer.Click
        Retour = False
        Me.Hide()
    End Sub

    Private Sub bEffacer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bEffacer.Click
        Retour = True
        Me.Hide()
    End Sub

    Private Sub bIgnorer_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bIgnorer.KeyUp
        If e.KeyCode = Keys.F3 Then
            bIgnorer_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bEffacer_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub bEffacer_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bEffacer.KeyUp
        If e.KeyCode = Keys.F3 Then
            bIgnorer_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bEffacer_Click(sender, e)
            Exit Sub
        End If
    End Sub
End Class