﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fCritereDeSelectionCommande
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fCritereDeSelectionCommande))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bCommandeInstantannee = New C1.Win.C1Input.C1Button()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.bFirgo = New C1.Win.C1Input.C1Button()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.bOk = New C1.Win.C1Input.C1Button()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.chkStatistique = New System.Windows.Forms.CheckBox()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.rdbToutesSection = New System.Windows.Forms.RadioButton()
        Me.rdbIntervalleSection = New System.Windows.Forms.RadioButton()
        Me.tFinIntervalle = New C1.Win.C1Input.C1TextBox()
        Me.tDebutIntervalle = New C1.Win.C1Input.C1TextBox()
        Me.GroupeProduit = New System.Windows.Forms.GroupBox()
        Me.cmbCategorie = New C1.Win.C1List.C1Combo()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.cmbForme = New C1.Win.C1List.C1Combo()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.cmbLabo = New C1.Win.C1List.C1Combo()
        Me.GroupeTrie = New System.Windows.Forms.GroupBox()
        Me.rdbStock = New System.Windows.Forms.RadioButton()
        Me.rdbQte = New System.Windows.Forms.RadioButton()
        Me.rdbForme = New System.Windows.Forms.RadioButton()
        Me.rdbDesignation = New System.Windows.Forms.RadioButton()
        Me.GroupeReference = New System.Windows.Forms.GroupBox()
        Me.dtpDebutPeriode = New C1.Win.C1Input.C1DateEdit()
        Me.rdbPeriode = New System.Windows.Forms.RadioButton()
        Me.rdbMois = New System.Windows.Forms.RadioButton()
        Me.cmbMois = New C1.Win.C1List.C1Combo()
        Me.dtpFinPeriode = New C1.Win.C1Input.C1DateEdit()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.chbTenirCompte = New System.Windows.Forms.CheckBox()
        Me.GroupeType = New System.Windows.Forms.GroupBox()
        Me.chbTenirCompteStock = New System.Windows.Forms.CheckBox()
        Me.chbTenirCompteStockAlerte = New System.Windows.Forms.CheckBox()
        Me.rdbHitParade = New System.Windows.Forms.RadioButton()
        Me.rdbGroupee = New System.Windows.Forms.RadioButton()
        Me.rdbJournaliere = New System.Windows.Forms.RadioButton()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.tNombreJour = New C1.Win.C1Input.C1TextBox()
        Me.GroupeRayon = New System.Windows.Forms.GroupBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.dtpSansManquant = New C1.Win.C1Input.C1DateEdit()
        Me.GroupeSection = New System.Windows.Forms.GroupBox()
        Me.cmbRayon = New C1.Win.C1List.C1Combo()
        Me.rdbRayonRayon = New System.Windows.Forms.RadioButton()
        Me.rdbTousRayon = New System.Windows.Forms.RadioButton()
        Me.CbTenirCompteStockAlerteCJ = New System.Windows.Forms.CheckBox()
        Me.Panel.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.tFinIntervalle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDebutIntervalle, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupeProduit.SuspendLayout()
        CType(Me.cmbCategorie, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbForme, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbLabo, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupeTrie.SuspendLayout()
        Me.GroupeReference.SuspendLayout()
        CType(Me.dtpDebutPeriode, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbMois, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpFinPeriode, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupeType.SuspendLayout()
        CType(Me.tNombreJour, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupeRayon.SuspendLayout()
        CType(Me.dtpSansManquant, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupeSection.SuspendLayout()
        CType(Me.cmbRayon, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.bCommandeInstantannee)
        Me.Panel.Controls.Add(Me.Label5)
        Me.Panel.Controls.Add(Me.bFirgo)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.bOk)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(564, 591)
        Me.Panel.TabIndex = 7
        '
        'bCommandeInstantannee
        '
        Me.bCommandeInstantannee.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bCommandeInstantannee.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bCommandeInstantannee.Location = New System.Drawing.Point(117, 534)
        Me.bCommandeInstantannee.Name = "bCommandeInstantannee"
        Me.bCommandeInstantannee.Size = New System.Drawing.Size(100, 45)
        Me.bCommandeInstantannee.TabIndex = 47
        Me.bCommandeInstantannee.Text = "Commande instantanée"
        Me.bCommandeInstantannee.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bCommandeInstantannee.UseVisualStyleBackColor = True
        Me.bCommandeInstantannee.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label5
        '
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 18.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label5.Location = New System.Drawing.Point(11, 4)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(540, 33)
        Me.Label5.TabIndex = 46
        Me.Label5.Text = "CRITERES DE COMMANDE"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bFirgo
        '
        Me.bFirgo.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bFirgo.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bFirgo.Image = Global.Pharma2000Premium.My.Resources.Resources.enregistrer
        Me.bFirgo.Location = New System.Drawing.Point(451, 534)
        Me.bFirgo.Name = "bFirgo"
        Me.bFirgo.Size = New System.Drawing.Size(100, 45)
        Me.bFirgo.TabIndex = 18
        Me.bFirgo.Text = "Frigo              F6"
        Me.bFirgo.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bFirgo.UseVisualStyleBackColor = True
        Me.bFirgo.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bAnnuler.Location = New System.Drawing.Point(325, 534)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(121, 45)
        Me.bAnnuler.TabIndex = 17
        Me.bAnnuler.Text = "Annuler           F10"
        Me.bAnnuler.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bOk
        '
        Me.bOk.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bOk.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bOk.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bOk.Location = New System.Drawing.Point(221, 534)
        Me.bOk.Name = "bOk"
        Me.bOk.Size = New System.Drawing.Size(100, 45)
        Me.bOk.TabIndex = 16
        Me.bOk.Text = "OK                  F3"
        Me.bOk.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bOk.UseVisualStyleBackColor = True
        Me.bOk.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.CbTenirCompteStockAlerteCJ)
        Me.GroupBox1.Controls.Add(Me.chkStatistique)
        Me.GroupBox1.Controls.Add(Me.GroupBox2)
        Me.GroupBox1.Controls.Add(Me.GroupeProduit)
        Me.GroupBox1.Controls.Add(Me.GroupeTrie)
        Me.GroupBox1.Controls.Add(Me.GroupeReference)
        Me.GroupBox1.Controls.Add(Me.chbTenirCompte)
        Me.GroupBox1.Controls.Add(Me.GroupeType)
        Me.GroupBox1.Controls.Add(Me.GroupeRayon)
        Me.GroupBox1.Controls.Add(Me.GroupeSection)
        Me.GroupBox1.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupBox1.Location = New System.Drawing.Point(11, 36)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(540, 492)
        Me.GroupBox1.TabIndex = 3
        Me.GroupBox1.TabStop = False
        '
        'chkStatistique
        '
        Me.chkStatistique.AutoSize = True
        Me.chkStatistique.Location = New System.Drawing.Point(17, 469)
        Me.chkStatistique.Name = "chkStatistique"
        Me.chkStatistique.Size = New System.Drawing.Size(318, 17)
        Me.chkStatistique.TabIndex = 45
        Me.chkStatistique.Text = "Tenir compte des statistiques de l'année précidente (Groupée)"
        Me.chkStatistique.UseVisualStyleBackColor = True
        Me.chkStatistique.Visible = False
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.rdbToutesSection)
        Me.GroupBox2.Controls.Add(Me.rdbIntervalleSection)
        Me.GroupBox2.Controls.Add(Me.tFinIntervalle)
        Me.GroupBox2.Controls.Add(Me.tDebutIntervalle)
        Me.GroupBox2.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupBox2.Location = New System.Drawing.Point(224, 228)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(299, 70)
        Me.GroupBox2.TabIndex = 44
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "Paramètres de section"
        '
        'rdbToutesSection
        '
        Me.rdbToutesSection.AutoSize = True
        Me.rdbToutesSection.Location = New System.Drawing.Point(6, 19)
        Me.rdbToutesSection.Name = "rdbToutesSection"
        Me.rdbToutesSection.Size = New System.Drawing.Size(116, 17)
        Me.rdbToutesSection.TabIndex = 16
        Me.rdbToutesSection.TabStop = True
        Me.rdbToutesSection.Text = "Toutes les sections"
        Me.rdbToutesSection.UseVisualStyleBackColor = True
        '
        'rdbIntervalleSection
        '
        Me.rdbIntervalleSection.AutoSize = True
        Me.rdbIntervalleSection.Location = New System.Drawing.Point(130, 19)
        Me.rdbIntervalleSection.Name = "rdbIntervalleSection"
        Me.rdbIntervalleSection.Size = New System.Drawing.Size(125, 17)
        Me.rdbIntervalleSection.TabIndex = 17
        Me.rdbIntervalleSection.TabStop = True
        Me.rdbIntervalleSection.Text = "Intervalle de sections"
        Me.rdbIntervalleSection.UseVisualStyleBackColor = True
        '
        'tFinIntervalle
        '
        Me.tFinIntervalle.AutoSize = False
        Me.tFinIntervalle.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tFinIntervalle.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tFinIntervalle.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tFinIntervalle.Location = New System.Drawing.Point(168, 42)
        Me.tFinIntervalle.Name = "tFinIntervalle"
        Me.tFinIntervalle.Size = New System.Drawing.Size(29, 20)
        Me.tFinIntervalle.TabIndex = 19
        Me.tFinIntervalle.Tag = Nothing
        Me.tFinIntervalle.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tFinIntervalle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tDebutIntervalle
        '
        Me.tDebutIntervalle.AutoSize = False
        Me.tDebutIntervalle.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tDebutIntervalle.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDebutIntervalle.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDebutIntervalle.Location = New System.Drawing.Point(130, 42)
        Me.tDebutIntervalle.Name = "tDebutIntervalle"
        Me.tDebutIntervalle.Size = New System.Drawing.Size(32, 20)
        Me.tDebutIntervalle.TabIndex = 18
        Me.tDebutIntervalle.Tag = Nothing
        Me.tDebutIntervalle.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDebutIntervalle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupeProduit
        '
        Me.GroupeProduit.Controls.Add(Me.cmbCategorie)
        Me.GroupeProduit.Controls.Add(Me.Label6)
        Me.GroupeProduit.Controls.Add(Me.cmbForme)
        Me.GroupeProduit.Controls.Add(Me.Label7)
        Me.GroupeProduit.Controls.Add(Me.Label8)
        Me.GroupeProduit.Controls.Add(Me.cmbLabo)
        Me.GroupeProduit.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeProduit.Location = New System.Drawing.Point(17, 304)
        Me.GroupeProduit.Name = "GroupeProduit"
        Me.GroupeProduit.Size = New System.Drawing.Size(506, 118)
        Me.GroupeProduit.TabIndex = 15
        Me.GroupeProduit.TabStop = False
        Me.GroupeProduit.Text = "Paramètres de produit"
        '
        'cmbCategorie
        '
        Me.cmbCategorie.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbCategorie.Caption = ""
        Me.cmbCategorie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbCategorie.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbCategorie.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbCategorie.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbCategorie.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbCategorie.Images.Add(CType(resources.GetObject("cmbCategorie.Images"), System.Drawing.Image))
        Me.cmbCategorie.Location = New System.Drawing.Point(10, 83)
        Me.cmbCategorie.MatchEntryTimeout = CType(2000, Long)
        Me.cmbCategorie.MaxDropDownItems = CType(5, Short)
        Me.cmbCategorie.MaxLength = 32767
        Me.cmbCategorie.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbCategorie.Name = "cmbCategorie"
        Me.cmbCategorie.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbCategorie.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbCategorie.Size = New System.Drawing.Size(159, 21)
        Me.cmbCategorie.TabIndex = 11
        Me.cmbCategorie.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbCategorie.PropBag = resources.GetString("cmbCategorie.PropBag")
        '
        'Label6
        '
        Me.Label6.Location = New System.Drawing.Point(7, 21)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(74, 15)
        Me.Label6.TabIndex = 40
        Me.Label6.Text = "Forme"
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'cmbForme
        '
        Me.cmbForme.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbForme.Caption = ""
        Me.cmbForme.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbForme.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbForme.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbForme.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbForme.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbForme.Images.Add(CType(resources.GetObject("cmbForme.Images"), System.Drawing.Image))
        Me.cmbForme.Location = New System.Drawing.Point(10, 39)
        Me.cmbForme.MatchEntryTimeout = CType(2000, Long)
        Me.cmbForme.MaxDropDownItems = CType(5, Short)
        Me.cmbForme.MaxLength = 32767
        Me.cmbForme.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbForme.Name = "cmbForme"
        Me.cmbForme.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbForme.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbForme.Size = New System.Drawing.Size(159, 21)
        Me.cmbForme.TabIndex = 10
        Me.cmbForme.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbForme.PropBag = resources.GetString("cmbForme.PropBag")
        '
        'Label7
        '
        Me.Label7.Location = New System.Drawing.Point(191, 21)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(74, 15)
        Me.Label7.TabIndex = 44
        Me.Label7.Text = "Labo"
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label8
        '
        Me.Label8.Location = New System.Drawing.Point(7, 63)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(74, 15)
        Me.Label8.TabIndex = 42
        Me.Label8.Text = "Catégorie"
        Me.Label8.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'cmbLabo
        '
        Me.cmbLabo.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbLabo.Caption = ""
        Me.cmbLabo.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbLabo.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbLabo.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbLabo.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbLabo.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbLabo.Images.Add(CType(resources.GetObject("cmbLabo.Images"), System.Drawing.Image))
        Me.cmbLabo.Location = New System.Drawing.Point(194, 38)
        Me.cmbLabo.MatchEntryTimeout = CType(2000, Long)
        Me.cmbLabo.MaxDropDownItems = CType(5, Short)
        Me.cmbLabo.MaxLength = 32767
        Me.cmbLabo.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbLabo.Name = "cmbLabo"
        Me.cmbLabo.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbLabo.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbLabo.Size = New System.Drawing.Size(298, 21)
        Me.cmbLabo.TabIndex = 12
        Me.cmbLabo.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbLabo.PropBag = resources.GetString("cmbLabo.PropBag")
        '
        'GroupeTrie
        '
        Me.GroupeTrie.Controls.Add(Me.rdbStock)
        Me.GroupeTrie.Controls.Add(Me.rdbQte)
        Me.GroupeTrie.Controls.Add(Me.rdbForme)
        Me.GroupeTrie.Controls.Add(Me.rdbDesignation)
        Me.GroupeTrie.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeTrie.Location = New System.Drawing.Point(238, 423)
        Me.GroupeTrie.Name = "GroupeTrie"
        Me.GroupeTrie.Size = New System.Drawing.Size(285, 46)
        Me.GroupeTrie.TabIndex = 15
        Me.GroupeTrie.TabStop = False
        Me.GroupeTrie.Text = "Tri"
        '
        'rdbStock
        '
        Me.rdbStock.AutoSize = True
        Me.rdbStock.Location = New System.Drawing.Point(201, 17)
        Me.rdbStock.Name = "rdbStock"
        Me.rdbStock.Size = New System.Drawing.Size(53, 17)
        Me.rdbStock.TabIndex = 3
        Me.rdbStock.Text = "Stock"
        Me.rdbStock.UseVisualStyleBackColor = True
        '
        'rdbQte
        '
        Me.rdbQte.AutoSize = True
        Me.rdbQte.Location = New System.Drawing.Point(153, 17)
        Me.rdbQte.Name = "rdbQte"
        Me.rdbQte.Size = New System.Drawing.Size(42, 17)
        Me.rdbQte.TabIndex = 2
        Me.rdbQte.Text = "Qte"
        Me.rdbQte.UseVisualStyleBackColor = True
        '
        'rdbForme
        '
        Me.rdbForme.AutoSize = True
        Me.rdbForme.Location = New System.Drawing.Point(93, 17)
        Me.rdbForme.Name = "rdbForme"
        Me.rdbForme.Size = New System.Drawing.Size(54, 17)
        Me.rdbForme.TabIndex = 1
        Me.rdbForme.TabStop = True
        Me.rdbForme.Text = "Forme"
        Me.rdbForme.UseVisualStyleBackColor = True
        '
        'rdbDesignation
        '
        Me.rdbDesignation.AutoSize = True
        Me.rdbDesignation.Location = New System.Drawing.Point(6, 17)
        Me.rdbDesignation.Name = "rdbDesignation"
        Me.rdbDesignation.Size = New System.Drawing.Size(81, 17)
        Me.rdbDesignation.TabIndex = 0
        Me.rdbDesignation.TabStop = True
        Me.rdbDesignation.Text = "Désignation"
        Me.rdbDesignation.UseVisualStyleBackColor = True
        '
        'GroupeReference
        '
        Me.GroupeReference.Controls.Add(Me.dtpDebutPeriode)
        Me.GroupeReference.Controls.Add(Me.rdbPeriode)
        Me.GroupeReference.Controls.Add(Me.rdbMois)
        Me.GroupeReference.Controls.Add(Me.cmbMois)
        Me.GroupeReference.Controls.Add(Me.dtpFinPeriode)
        Me.GroupeReference.Controls.Add(Me.Label4)
        Me.GroupeReference.Controls.Add(Me.Label3)
        Me.GroupeReference.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeReference.Location = New System.Drawing.Point(17, 93)
        Me.GroupeReference.Name = "GroupeReference"
        Me.GroupeReference.Size = New System.Drawing.Size(507, 76)
        Me.GroupeReference.TabIndex = 3
        Me.GroupeReference.TabStop = False
        Me.GroupeReference.Text = "Paramètres de la période"
        '
        'dtpDebutPeriode
        '
        Me.dtpDebutPeriode.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDebutPeriode.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDebutPeriode.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDebutPeriode.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutPeriode.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutPeriode.Location = New System.Drawing.Point(156, 47)
        Me.dtpDebutPeriode.Name = "dtpDebutPeriode"
        Me.dtpDebutPeriode.Size = New System.Drawing.Size(155, 18)
        Me.dtpDebutPeriode.TabIndex = 35
        Me.dtpDebutPeriode.Tag = Nothing
        Me.dtpDebutPeriode.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutPeriode.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'rdbPeriode
        '
        Me.rdbPeriode.AutoSize = True
        Me.rdbPeriode.Location = New System.Drawing.Point(140, 24)
        Me.rdbPeriode.Name = "rdbPeriode"
        Me.rdbPeriode.Size = New System.Drawing.Size(61, 17)
        Me.rdbPeriode.TabIndex = 1
        Me.rdbPeriode.TabStop = True
        Me.rdbPeriode.Text = "Période"
        Me.rdbPeriode.UseVisualStyleBackColor = True
        '
        'rdbMois
        '
        Me.rdbMois.AutoSize = True
        Me.rdbMois.Location = New System.Drawing.Point(6, 24)
        Me.rdbMois.Name = "rdbMois"
        Me.rdbMois.Size = New System.Drawing.Size(47, 17)
        Me.rdbMois.TabIndex = 0
        Me.rdbMois.TabStop = True
        Me.rdbMois.Text = "Mois"
        Me.rdbMois.UseVisualStyleBackColor = True
        '
        'cmbMois
        '
        Me.cmbMois.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbMois.Caption = ""
        Me.cmbMois.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbMois.ColumnWidth = 100
        Me.cmbMois.DataMode = C1.Win.C1List.DataModeEnum.AddItem
        Me.cmbMois.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbMois.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbMois.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbMois.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbMois.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbMois.Images.Add(CType(resources.GetObject("cmbMois.Images"), System.Drawing.Image))
        Me.cmbMois.Location = New System.Drawing.Point(6, 47)
        Me.cmbMois.MatchEntryTimeout = CType(2000, Long)
        Me.cmbMois.MaxDropDownItems = CType(5, Short)
        Me.cmbMois.MaxLength = 32767
        Me.cmbMois.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbMois.Name = "cmbMois"
        Me.cmbMois.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbMois.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbMois.Size = New System.Drawing.Size(94, 21)
        Me.cmbMois.TabIndex = 4
        Me.cmbMois.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbMois.PropBag = resources.GetString("cmbMois.PropBag")
        '
        'dtpFinPeriode
        '
        Me.dtpFinPeriode.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpFinPeriode.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpFinPeriode.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpFinPeriode.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFinPeriode.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFinPeriode.Location = New System.Drawing.Point(337, 47)
        Me.dtpFinPeriode.Name = "dtpFinPeriode"
        Me.dtpFinPeriode.Size = New System.Drawing.Size(155, 18)
        Me.dtpFinPeriode.TabIndex = 8
        Me.dtpFinPeriode.Tag = Nothing
        Me.dtpFinPeriode.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFinPeriode.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label4
        '
        Me.Label4.Location = New System.Drawing.Point(317, 50)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(21, 11)
        Me.Label4.TabIndex = 34
        Me.Label4.Text = "Au"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label3
        '
        Me.Label3.Location = New System.Drawing.Point(137, 50)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(21, 11)
        Me.Label3.TabIndex = 32
        Me.Label3.Text = "Du"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'chbTenirCompte
        '
        Me.chbTenirCompte.AutoSize = True
        Me.chbTenirCompte.Location = New System.Drawing.Point(17, 446)
        Me.chbTenirCompte.Name = "chbTenirCompte"
        Me.chbTenirCompte.Size = New System.Drawing.Size(212, 17)
        Me.chbTenirCompte.TabIndex = 0
        Me.chbTenirCompte.Text = "Tenir compte des commandes en cours"
        Me.chbTenirCompte.UseVisualStyleBackColor = True
        '
        'GroupeType
        '
        Me.GroupeType.Controls.Add(Me.chbTenirCompteStock)
        Me.GroupeType.Controls.Add(Me.chbTenirCompteStockAlerte)
        Me.GroupeType.Controls.Add(Me.rdbHitParade)
        Me.GroupeType.Controls.Add(Me.rdbGroupee)
        Me.GroupeType.Controls.Add(Me.rdbJournaliere)
        Me.GroupeType.Controls.Add(Me.Label1)
        Me.GroupeType.Controls.Add(Me.tNombreJour)
        Me.GroupeType.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeType.Location = New System.Drawing.Point(17, 12)
        Me.GroupeType.Name = "GroupeType"
        Me.GroupeType.Size = New System.Drawing.Size(507, 77)
        Me.GroupeType.TabIndex = 0
        Me.GroupeType.TabStop = False
        Me.GroupeType.Text = "Type de la commande"
        '
        'chbTenirCompteStock
        '
        Me.chbTenirCompteStock.AutoSize = True
        Me.chbTenirCompteStock.Location = New System.Drawing.Point(195, 58)
        Me.chbTenirCompteStock.Name = "chbTenirCompteStock"
        Me.chbTenirCompteStock.Size = New System.Drawing.Size(117, 17)
        Me.chbTenirCompteStock.TabIndex = 47
        Me.chbTenirCompteStock.Text = "Tenir compte stock"
        Me.chbTenirCompteStock.UseVisualStyleBackColor = True
        Me.chbTenirCompteStock.Visible = False
        '
        'chbTenirCompteStockAlerte
        '
        Me.chbTenirCompteStockAlerte.AutoSize = True
        Me.chbTenirCompteStockAlerte.Location = New System.Drawing.Point(195, 40)
        Me.chbTenirCompteStockAlerte.Name = "chbTenirCompteStockAlerte"
        Me.chbTenirCompteStockAlerte.Size = New System.Drawing.Size(154, 17)
        Me.chbTenirCompteStockAlerte.TabIndex = 46
        Me.chbTenirCompteStockAlerte.Text = "Tenir compte stock d'alerte"
        Me.chbTenirCompteStockAlerte.UseVisualStyleBackColor = True
        Me.chbTenirCompteStockAlerte.Visible = False
        '
        'rdbHitParade
        '
        Me.rdbHitParade.AutoSize = True
        Me.rdbHitParade.Location = New System.Drawing.Point(195, 20)
        Me.rdbHitParade.Name = "rdbHitParade"
        Me.rdbHitParade.Size = New System.Drawing.Size(75, 17)
        Me.rdbHitParade.TabIndex = 2
        Me.rdbHitParade.TabStop = True
        Me.rdbHitParade.Text = "Hit Parade"
        Me.rdbHitParade.UseVisualStyleBackColor = True
        '
        'rdbGroupee
        '
        Me.rdbGroupee.AutoSize = True
        Me.rdbGroupee.Location = New System.Drawing.Point(104, 20)
        Me.rdbGroupee.Name = "rdbGroupee"
        Me.rdbGroupee.Size = New System.Drawing.Size(66, 17)
        Me.rdbGroupee.TabIndex = 1
        Me.rdbGroupee.TabStop = True
        Me.rdbGroupee.Text = "Groupée"
        Me.rdbGroupee.UseVisualStyleBackColor = True
        '
        'rdbJournaliere
        '
        Me.rdbJournaliere.AutoSize = True
        Me.rdbJournaliere.Location = New System.Drawing.Point(6, 19)
        Me.rdbJournaliere.Name = "rdbJournaliere"
        Me.rdbJournaliere.Size = New System.Drawing.Size(76, 17)
        Me.rdbJournaliere.TabIndex = 0
        Me.rdbJournaliere.TabStop = True
        Me.rdbJournaliere.Text = "Journalière"
        Me.rdbJournaliere.UseVisualStyleBackColor = True
        '
        'Label1
        '
        Me.Label1.Location = New System.Drawing.Point(292, 21)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(91, 15)
        Me.Label1.TabIndex = 30
        Me.Label1.Text = "Nombre de jours"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'tNombreJour
        '
        Me.tNombreJour.AutoSize = False
        Me.tNombreJour.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNombreJour.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNombreJour.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNombreJour.Location = New System.Drawing.Point(383, 18)
        Me.tNombreJour.Name = "tNombreJour"
        Me.tNombreJour.Size = New System.Drawing.Size(63, 20)
        Me.tNombreJour.TabIndex = 1
        Me.tNombreJour.Tag = Nothing
        Me.tNombreJour.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNombreJour.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupeRayon
        '
        Me.GroupeRayon.Controls.Add(Me.Label2)
        Me.GroupeRayon.Controls.Add(Me.dtpSansManquant)
        Me.GroupeRayon.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeRayon.Location = New System.Drawing.Point(17, 175)
        Me.GroupeRayon.Name = "GroupeRayon"
        Me.GroupeRayon.Size = New System.Drawing.Size(507, 47)
        Me.GroupeRayon.TabIndex = 13
        Me.GroupeRayon.TabStop = False
        Me.GroupeRayon.Text = "Produits manquants"
        Me.GroupeRayon.Visible = False
        '
        'Label2
        '
        Me.Label2.Location = New System.Drawing.Point(6, 16)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(183, 20)
        Me.Label2.TabIndex = 30
        Me.Label2.Text = "Ne pas ajouter les manquants depuis"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'dtpSansManquant
        '
        Me.dtpSansManquant.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpSansManquant.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpSansManquant.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpSansManquant.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpSansManquant.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpSansManquant.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        Me.dtpSansManquant.EditFormat.Inherit = CType(((((C1.Win.C1Input.FormatInfoInheritFlags.CustomFormat Or C1.Win.C1Input.FormatInfoInheritFlags.NullText) _
            Or C1.Win.C1Input.FormatInfoInheritFlags.EmptyAsNull) _
            Or C1.Win.C1Input.FormatInfoInheritFlags.TrimStart) _
            Or C1.Win.C1Input.FormatInfoInheritFlags.TrimEnd), C1.Win.C1Input.FormatInfoInheritFlags)
        Me.dtpSansManquant.FormatType = C1.Win.C1Input.FormatTypeEnum.LongDate
        Me.dtpSansManquant.Location = New System.Drawing.Point(195, 18)
        Me.dtpSansManquant.Name = "dtpSansManquant"
        Me.dtpSansManquant.Size = New System.Drawing.Size(107, 18)
        Me.dtpSansManquant.TabIndex = 35
        Me.dtpSansManquant.Tag = Nothing
        Me.dtpSansManquant.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpSansManquant.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupeSection
        '
        Me.GroupeSection.Controls.Add(Me.cmbRayon)
        Me.GroupeSection.Controls.Add(Me.rdbRayonRayon)
        Me.GroupeSection.Controls.Add(Me.rdbTousRayon)
        Me.GroupeSection.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeSection.Location = New System.Drawing.Point(17, 228)
        Me.GroupeSection.Name = "GroupeSection"
        Me.GroupeSection.Size = New System.Drawing.Size(201, 70)
        Me.GroupeSection.TabIndex = 7
        Me.GroupeSection.TabStop = False
        Me.GroupeSection.Text = "Paramètres de rayon"
        '
        'cmbRayon
        '
        Me.cmbRayon.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbRayon.Caption = ""
        Me.cmbRayon.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbRayon.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbRayon.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbRayon.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbRayon.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbRayon.Images.Add(CType(resources.GetObject("cmbRayon.Images"), System.Drawing.Image))
        Me.cmbRayon.Location = New System.Drawing.Point(114, 40)
        Me.cmbRayon.MatchEntryTimeout = CType(2000, Long)
        Me.cmbRayon.MaxDropDownItems = CType(5, Short)
        Me.cmbRayon.MaxLength = 32767
        Me.cmbRayon.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbRayon.Name = "cmbRayon"
        Me.cmbRayon.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbRayon.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbRayon.Size = New System.Drawing.Size(52, 22)
        Me.cmbRayon.TabIndex = 43
        Me.cmbRayon.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbRayon.PropBag = resources.GetString("cmbRayon.PropBag")
        '
        'rdbRayonRayon
        '
        Me.rdbRayonRayon.AutoSize = True
        Me.rdbRayonRayon.Location = New System.Drawing.Point(114, 19)
        Me.rdbRayonRayon.Name = "rdbRayonRayon"
        Me.rdbRayonRayon.Size = New System.Drawing.Size(56, 17)
        Me.rdbRayonRayon.TabIndex = 1
        Me.rdbRayonRayon.TabStop = True
        Me.rdbRayonRayon.Text = "Rayon"
        Me.rdbRayonRayon.UseVisualStyleBackColor = True
        '
        'rdbTousRayon
        '
        Me.rdbTousRayon.AutoSize = True
        Me.rdbTousRayon.Location = New System.Drawing.Point(6, 19)
        Me.rdbTousRayon.Name = "rdbTousRayon"
        Me.rdbTousRayon.Size = New System.Drawing.Size(99, 17)
        Me.rdbTousRayon.TabIndex = 0
        Me.rdbTousRayon.TabStop = True
        Me.rdbTousRayon.Text = "Tous les rayons"
        Me.rdbTousRayon.UseVisualStyleBackColor = True
        '
        'CbTenirCompteStockAlerteCJ
        '
        Me.CbTenirCompteStockAlerteCJ.AutoSize = True
        Me.CbTenirCompteStockAlerteCJ.Location = New System.Drawing.Point(17, 423)
        Me.CbTenirCompteStockAlerteCJ.Name = "CbTenirCompteStockAlerteCJ"
        Me.CbTenirCompteStockAlerteCJ.Size = New System.Drawing.Size(172, 17)
        Me.CbTenirCompteStockAlerteCJ.TabIndex = 89
        Me.CbTenirCompteStockAlerteCJ.Text = "Tenir En compte Stock d'alerte"
        Me.CbTenirCompteStockAlerteCJ.UseVisualStyleBackColor = True
        '
        'fCritereDeSelectionCommande
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(564, 591)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fCritereDeSelectionCommande"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.tFinIntervalle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDebutIntervalle, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupeProduit.ResumeLayout(False)
        Me.GroupeProduit.PerformLayout()
        CType(Me.cmbCategorie, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbForme, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbLabo, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupeTrie.ResumeLayout(False)
        Me.GroupeTrie.PerformLayout()
        Me.GroupeReference.ResumeLayout(False)
        Me.GroupeReference.PerformLayout()
        CType(Me.dtpDebutPeriode, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbMois, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpFinPeriode, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupeType.ResumeLayout(False)
        Me.GroupeType.PerformLayout()
        CType(Me.tNombreJour, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupeRayon.ResumeLayout(False)
        CType(Me.dtpSansManquant, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupeSection.ResumeLayout(False)
        Me.GroupeSection.PerformLayout()
        CType(Me.cmbRayon, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents GroupeType As System.Windows.Forms.GroupBox
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bOk As C1.Win.C1Input.C1Button
    Friend WithEvents rdbGroupee As System.Windows.Forms.RadioButton
    Friend WithEvents rdbJournaliere As System.Windows.Forms.RadioButton
    Friend WithEvents rdbHitParade As System.Windows.Forms.RadioButton
    Friend WithEvents GroupeReference As System.Windows.Forms.GroupBox
    Friend WithEvents rdbPeriode As System.Windows.Forms.RadioButton
    Friend WithEvents rdbMois As System.Windows.Forms.RadioButton
    Friend WithEvents tNombreJour As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents cmbMois As C1.Win.C1List.C1Combo
    Friend WithEvents GroupeSection As System.Windows.Forms.GroupBox
    Friend WithEvents bFirgo As C1.Win.C1Input.C1Button
    Friend WithEvents GroupeTrie As System.Windows.Forms.GroupBox
    Friend WithEvents rdbForme As System.Windows.Forms.RadioButton
    Friend WithEvents rdbDesignation As System.Windows.Forms.RadioButton
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents GroupeRayon As System.Windows.Forms.GroupBox
    Friend WithEvents rdbRayonRayon As System.Windows.Forms.RadioButton
    Friend WithEvents rdbTousRayon As System.Windows.Forms.RadioButton
    Friend WithEvents cmbLabo As C1.Win.C1List.C1Combo
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents cmbCategorie As C1.Win.C1List.C1Combo
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents cmbForme As C1.Win.C1List.C1Combo
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents dtpFinPeriode As C1.Win.C1Input.C1DateEdit
    Friend WithEvents dtpSansManquant As C1.Win.C1Input.C1DateEdit
    Friend WithEvents cmbRayon As C1.Win.C1List.C1Combo
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents GroupeProduit As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents rdbToutesSection As System.Windows.Forms.RadioButton
    Friend WithEvents rdbIntervalleSection As System.Windows.Forms.RadioButton
    Friend WithEvents tFinIntervalle As C1.Win.C1Input.C1TextBox
    Friend WithEvents tDebutIntervalle As C1.Win.C1Input.C1TextBox
    Friend WithEvents chkStatistique As System.Windows.Forms.CheckBox
    Friend WithEvents chbTenirCompte As System.Windows.Forms.CheckBox
    Friend WithEvents chbTenirCompteStockAlerte As System.Windows.Forms.CheckBox
    Friend WithEvents bCommandeInstantannee As C1.Win.C1Input.C1Button
    Friend WithEvents rdbQte As System.Windows.Forms.RadioButton
    Friend WithEvents rdbStock As System.Windows.Forms.RadioButton
    Friend WithEvents chbTenirCompteStock As System.Windows.Forms.CheckBox
    Friend WithEvents dtpDebutPeriode As C1.Win.C1Input.C1DateEdit
    Friend WithEvents CbTenirCompteStockAlerteCJ As System.Windows.Forms.CheckBox
End Class
