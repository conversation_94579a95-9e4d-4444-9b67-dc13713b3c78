﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fListeDesCommandePourAchat
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fListeDesCommandePourAchat))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.bOK = New C1.Win.C1Input.C1Button()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.gDetailsCommande = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.GroupBox5 = New System.Windows.Forms.GroupBox()
        Me.gCommandes = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Panel.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        CType(Me.gDetailsCommande, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox5.SuspendLayout()
        CType(Me.gCommandes, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.bOK)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Controls.Add(Me.GroupBox5)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(713, 526)
        Me.Panel.TabIndex = 4
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(593, 470)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(107, 45)
        Me.bAnnuler.TabIndex = 65
        Me.bAnnuler.Text = "Annuler                 F10"
        Me.bAnnuler.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bOK
        '
        Me.bOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bOK.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bOK.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bOK.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bOK.Location = New System.Drawing.Point(481, 470)
        Me.bOK.Name = "bOK"
        Me.bOK.Size = New System.Drawing.Size(107, 45)
        Me.bOK.TabIndex = 64
        Me.bOK.Text = "OK                    F3"
        Me.bOK.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bOK.UseVisualStyleBackColor = True
        Me.bOK.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.gDetailsCommande)
        Me.GroupBox1.Location = New System.Drawing.Point(7, 238)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(693, 226)
        Me.GroupBox1.TabIndex = 2
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Détails"
        '
        'gDetailsCommande
        '
        Me.gDetailsCommande.GroupByCaption = "Drag a column header here to group by that column"
        Me.gDetailsCommande.Images.Add(CType(resources.GetObject("gDetailsCommande.Images"), System.Drawing.Image))
        Me.gDetailsCommande.Location = New System.Drawing.Point(7, 18)
        Me.gDetailsCommande.Name = "gDetailsCommande"
        Me.gDetailsCommande.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gDetailsCommande.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gDetailsCommande.PreviewInfo.ZoomFactor = 75.0R
        Me.gDetailsCommande.PrintInfo.PageSettings = CType(resources.GetObject("gDetailsCommande.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gDetailsCommande.Size = New System.Drawing.Size(680, 196)
        Me.gDetailsCommande.TabIndex = 0
        Me.gDetailsCommande.Text = "C1TrueDBGrid1"
        Me.gDetailsCommande.PropBag = resources.GetString("gDetailsCommande.PropBag")
        '
        'GroupBox5
        '
        Me.GroupBox5.Controls.Add(Me.gCommandes)
        Me.GroupBox5.Location = New System.Drawing.Point(7, 11)
        Me.GroupBox5.Name = "GroupBox5"
        Me.GroupBox5.Size = New System.Drawing.Size(693, 208)
        Me.GroupBox5.TabIndex = 1
        Me.GroupBox5.TabStop = False
        Me.GroupBox5.Text = "Liste des Commandes"
        '
        'gCommandes
        '
        Me.gCommandes.GroupByCaption = "Drag a column header here to group by that column"
        Me.gCommandes.Images.Add(CType(resources.GetObject("gCommandes.Images"), System.Drawing.Image))
        Me.gCommandes.Location = New System.Drawing.Point(7, 18)
        Me.gCommandes.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gCommandes.Name = "gCommandes"
        Me.gCommandes.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gCommandes.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gCommandes.PreviewInfo.ZoomFactor = 75.0R
        Me.gCommandes.PrintInfo.PageSettings = CType(resources.GetObject("gCommandes.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gCommandes.Size = New System.Drawing.Size(680, 177)
        Me.gCommandes.TabIndex = 0
        Me.gCommandes.Text = "C1TrueDBGrid1"
        Me.gCommandes.PropBag = resources.GetString("gCommandes.PropBag")
        '
        'fListeDesCommandePourAchat
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(713, 526)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fListeDesCommandePourAchat"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        Me.GroupBox1.ResumeLayout(False)
        CType(Me.gDetailsCommande, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox5.ResumeLayout(False)
        CType(Me.gCommandes, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bOK As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents gDetailsCommande As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents GroupBox5 As System.Windows.Forms.GroupBox
    Friend WithEvents gCommandes As C1.Win.C1TrueDBGrid.C1TrueDBGrid
End Class
