﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fCubeVenteDetail
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.bCritere = New C1.Win.C1Input.C1Button()
        Me.C1OlapPage1 = New C1.Win.Olap.C1OlapPage()
        Me.Panel = New System.Windows.Forms.Panel()
        CType(Me.C1OlapPage1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel.SuspendLayout()
        Me.SuspendLayout()
        '
        'bCritere
        '
        Me.bCritere.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bCritere.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bCritere.Image = Global.Pharma2000Premium.My.Resources.Resources.recherche
        Me.bCritere.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bCritere.Location = New System.Drawing.Point(962, 4)
        Me.bCritere.Name = "bCritere"
        Me.bCritere.Size = New System.Drawing.Size(101, 39)
        Me.bCritere.TabIndex = 83
        Me.bCritere.Text = "Critères"
        Me.bCritere.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bCritere.UseVisualStyleBackColor = True
        Me.bCritere.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1OlapPage1
        '
        Me.C1OlapPage1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.C1OlapPage1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.C1OlapPage1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.C1OlapPage1.Location = New System.Drawing.Point(0, 0)
        Me.C1OlapPage1.Margin = New System.Windows.Forms.Padding(2)
        Me.C1OlapPage1.Name = "C1OlapPage1"
        Me.C1OlapPage1.ShowZeros = False
        Me.C1OlapPage1.Size = New System.Drawing.Size(1075, 475)
        Me.C1OlapPage1.Stacked = False
        Me.C1OlapPage1.TabIndex = 84
        Me.C1OlapPage1.VisualStyle = C1.Win.C1FlexGrid.VisualStyle.Office2010Blue
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.bCritere)
        Me.Panel.Controls.Add(Me.C1OlapPage1)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1075, 475)
        Me.Panel.TabIndex = 8
        '
        'fCubeVenteDetail
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1075, 475)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fCubeVenteDetail"
        Me.Text = "fCubeVenteDetail"
        CType(Me.C1OlapPage1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    'Friend WithEvents etatHitParadeArticle As PHARMA.EtatHitParadeArticle
    Friend WithEvents bCritere As C1.Win.C1Input.C1Button
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents C1OlapPage1 As C1.Win.Olap.C1OlapPage
End Class
