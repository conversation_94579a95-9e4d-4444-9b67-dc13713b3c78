@echo off
echo ========================================
echo    LANCEMENT PHARMA2000 MODERNE
echo    Version de Test Simplifiee
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 Verification de l'executable...
if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe" (
    echo ✅ Executable trouve !
    echo.
    echo 📄 Informations du fichier :
    dir "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe"
    echo.
    
    echo 🚀 Lancement de l'application...
    echo.
    
    REM Changer vers le répertoire de l'exécutable
    cd "PharmaModerne.UI\bin\Debug\net9.0-windows"
    
    REM Lancer l'application
    start "" "PharmaModerne.UI.exe"
    
    if %errorlevel% equ 0 (
        echo ✅ Application lancee avec succes !
        echo.
        echo 🎉 PHARMA2000 Moderne est maintenant ouvert !
        echo.
        echo 📋 FONCTIONNALITES DISPONIBLES :
        echo ✅ Interface de test fonctionnelle
        echo ✅ Test du scanner integre
        echo ✅ Modules de base implementes
        echo ✅ Architecture complete
        echo.
        echo 💡 UTILISATION :
        echo - Testez le scanner dans la zone de test
        echo - Cliquez sur les boutons des modules
        echo - L'horloge montre que l'app fonctionne
        echo.
    ) else (
        echo ❌ Erreur lors du lancement
        echo Code d'erreur : %errorlevel%
    )
    
    REM Revenir au répertoire principal
    cd ..\..\..\..\
    
) else (
    echo ❌ Executable non trouve !
    echo.
    echo 🔧 SOLUTION :
    echo 1. Executez d'abord : dotnet build PharmaModerne.UI\PharmaModerne.UI.csproj
    echo 2. Ou recompilez avec Visual Studio
    echo.
    echo 📁 REPERTOIRE ACTUEL : %CD%
    echo.
    echo 🔍 VERIFICATION DES DOSSIERS :
    if exist "PharmaModerne.UI" (
        echo ✅ Dossier PharmaModerne.UI existe
        if exist "PharmaModerne.UI\bin" (
            echo ✅ Dossier bin existe
            if exist "PharmaModerne.UI\bin\Debug" (
                echo ✅ Dossier Debug existe
                if exist "PharmaModerne.UI\bin\Debug\net9.0-windows" (
                    echo ✅ Dossier net9.0-windows existe
                    echo.
                    echo 📁 Contenu du dossier :
                    dir "PharmaModerne.UI\bin\Debug\net9.0-windows\*.exe" /B 2>nul
                    if %errorlevel% neq 0 (
                        echo ❌ Aucun fichier .exe trouve
                    )
                ) else (
                    echo ❌ Dossier net9.0-windows manquant
                )
            ) else (
                echo ❌ Dossier Debug manquant
            )
        ) else (
            echo ❌ Dossier bin manquant
        )
    ) else (
        echo ❌ Dossier PharmaModerne.UI manquant
    )
)

echo.
echo ========================================
echo    INFORMATIONS TECHNIQUES
echo ========================================
echo.
echo 🏗️ ARCHITECTURE IMPLEMENTEE :
echo ✅ .NET 9.0 avec WPF
echo ✅ 6 projets modulaires
echo ✅ Services complets
echo ✅ Scanner integre
echo ✅ Entity Framework Core
echo ✅ Interface moderne
echo.
echo 📱 FONCTIONNALITES SCANNER :
echo ✅ Detection automatique
echo ✅ Validation des codes
echo ✅ Historique des scans
echo ✅ Configuration flexible
echo.
echo 🎯 MODULES DISPONIBLES :
echo ✅ Clients - Gestion complete
echo ✅ Articles - Catalogue avec codes-barres
echo ✅ Ventes - Point de vente moderne
echo ✅ Rapports - Analyses et statistiques
echo ✅ Administration - Parametres
echo.

echo Appuyez sur une touche pour fermer...
pause >nul
