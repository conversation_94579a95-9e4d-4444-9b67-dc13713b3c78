﻿Public Class fNombreQuantiteControle
    Public NombreDesArticles As Integer = 0
    Public resultat As Boolean = False
    Dim NbrTentative As Integer = 0
    Dim Confirmation As Boolean = False ' variable pour que la deuxieme interpretation de la touche OK lors d'une inf manquante apres confirmation

    Private Sub bOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOK.Click


        If tNombreArticle.Text = "" Then
            MsgBox("Veuillez saisir une valeur.. !", MsgBoxStyle.OkOnly, "Erreur")
            Confirmation = True
            Exit Sub
        End If
        NbrTentative += 1

        If NbrTentative <= NbrDesTentatives Then
            If CDbl(tNombreArticle.Text) <> NombreDesArticles Then
                MsgBox("Veuillez vérifier le nombre d'articles .. !", MsgBoxStyle.OkOnly + MessageBoxIcon.Exclamation, "Erreur")
                Confirmation = True
                tNombreArticle.Text = 0
                tNombreArticle.Focus()
                tNombreArticle.SelectionLength = tNombreArticle.Text.Length
                If CDbl(NbrTentative = NbrDesTentatives) Then
                    resultat = False
                    'Me.Hide()
                    Me.Close()
                End If
            Else
                resultat = True
                'Me.Hide()
                Me.Close()
            End If
        End If
    End Sub

    Private Sub tNombreArticle_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tNombreArticle.KeyPress

    End Sub

    Private Sub tNombreArticle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNombreArticle.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tNombreArticle.Text), 3)
        Catch ex As Exception
            MsgBox("Valeur invalide !", MsgBoxStyle.Critical, "Erreur")
            tNombreArticle.Text = "0"
            tNombreArticle.Focus()
            tNombreArticle.SelectionLength = tNombreArticle.Text.Length
            Exit Sub
        End Try

        If e.KeyCode = Keys.Enter And Confirmation = True Then
            Confirmation = False
            Exit Sub
        End If

        If e.KeyCode = Keys.Enter And Confirmation = False Then
            bOK_Click(sender, e)
        End If
    End Sub
    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)
        If e.KeyData = Keys.F3 Then
            bOK_Click(o, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            resultat = False
            Me.Hide()

            Exit Sub
        End If

    End Sub

    Private Sub tNombreArticle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNombreArticle.TextChanged

    End Sub

    Private Sub fNombreQuantiteControle_Disposed(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Disposed
        'fMain.Tab.SelectedTab.Focus()
        'Application.DoEvents()
    End Sub

    Private Sub fNombreQuantiteControle_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        tNombreArticle.Value = 0
        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Me.Dispose()
    End Sub
End Class