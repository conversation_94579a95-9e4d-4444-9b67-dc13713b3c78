﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSet_ETAT_HITPARAD_ARTICLE" targetNamespace="http://tempuri.org/DataSet_ETAT_HITPARAD_ARTICLE.xsd" xmlns:mstns="http://tempuri.org/DataSet_ETAT_HITPARAD_ARTICLE.xsd" xmlns="http://tempuri.org/DataSet_ETAT_HITPARAD_ARTICLE.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSet_ETAT_HITPARAD_ARTICLE" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DataSet_ETAT_HITPARAD_ARTICLE" msprop:Generator_UserDSName="DataSet_ETAT_HITPARAD_ARTICLE">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTableEtatHitParadArticles" msprop:Generator_TableClassName="DataTableEtatHitParadArticlesDataTable" msprop:Generator_TableVarName="tableDataTableEtatHitParadArticles" msprop:Generator_RowChangedName="DataTableEtatHitParadArticlesRowChanged" msprop:Generator_TablePropName="DataTableEtatHitParadArticles" msprop:Generator_RowDeletingName="DataTableEtatHitParadArticlesRowDeleting" msprop:Generator_RowChangingName="DataTableEtatHitParadArticlesRowChanging" msprop:Generator_RowEvHandlerName="DataTableEtatHitParadArticlesRowChangeEventHandler" msprop:Generator_RowDeletedName="DataTableEtatHitParadArticlesRowDeleted" msprop:Generator_RowClassName="DataTableEtatHitParadArticlesRow" msprop:Generator_UserTableName="DataTableEtatHitParadArticles" msprop:Generator_RowEvArgName="DataTableEtatHitParadArticlesRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CodeArticle" msprop:Generator_ColumnVarNameInTable="columnCodeArticle" msprop:Generator_ColumnPropNameInRow="CodeArticle" msprop:Generator_ColumnPropNameInTable="CodeArticleColumn" msprop:Generator_UserColumnName="CodeArticle" type="xs:string" minOccurs="0" />
              <xs:element name="CodeABarre" msprop:Generator_ColumnVarNameInTable="columnCodeABarre" msprop:Generator_ColumnPropNameInRow="CodeABarre" msprop:Generator_ColumnPropNameInTable="CodeABarreColumn" msprop:Generator_UserColumnName="CodeABarre" type="xs:string" minOccurs="0" />
              <xs:element name="Designation" msprop:Generator_ColumnVarNameInTable="columnDesignation" msprop:Generator_ColumnPropNameInRow="Designation" msprop:Generator_ColumnPropNameInTable="DesignationColumn" msprop:Generator_UserColumnName="Designation" type="xs:string" minOccurs="0" />
              <xs:element name="LibelleForme" msprop:Generator_ColumnVarNameInTable="columnLibelleForme" msprop:Generator_ColumnPropNameInRow="LibelleForme" msprop:Generator_ColumnPropNameInTable="LibelleFormeColumn" msprop:Generator_UserColumnName="LibelleForme" type="xs:string" minOccurs="0" />
              <xs:element name="LibelleCategorie" msprop:Generator_ColumnVarNameInTable="columnLibelleCategorie" msprop:Generator_ColumnPropNameInRow="LibelleCategorie" msprop:Generator_ColumnPropNameInTable="LibelleCategorieColumn" msprop:Generator_UserColumnName="LibelleCategorie" type="xs:string" minOccurs="0" />
              <xs:element name="QuantiteVendu" msprop:Generator_ColumnVarNameInTable="columnQuantiteVendu" msprop:Generator_ColumnPropNameInRow="QuantiteVendu" msprop:Generator_ColumnPropNameInTable="QuantiteVenduColumn" msprop:Generator_UserColumnName="QuantiteVendu" type="xs:string" minOccurs="0" />
              <xs:element name="Stock" msprop:Generator_ColumnVarNameInTable="columnStock" msprop:Generator_ColumnPropNameInRow="Stock" msprop:Generator_ColumnPropNameInTable="StockColumn" msprop:Generator_UserColumnName="Stock" type="xs:string" minOccurs="0" />
              <xs:element name="TotalTTC" msprop:Generator_ColumnVarNameInTable="columnTotalTTC" msprop:Generator_ColumnPropNameInRow="TotalTTC" msprop:Generator_ColumnPropNameInTable="TotalTTCColumn" msprop:Generator_UserColumnName="TotalTTC" type="xs:decimal" minOccurs="0" />
              <xs:element name="Rayon" msprop:Generator_ColumnVarNameInTable="columnRayon" msprop:Generator_ColumnPropNameInRow="Rayon" msprop:Generator_ColumnPropNameInTable="RayonColumn" msprop:Generator_UserColumnName="Rayon" type="xs:string" minOccurs="0" />
              <xs:element name="Pharmacie" msprop:Generator_ColumnVarNameInTable="columnPharmacie" msprop:Generator_ColumnPropNameInRow="Pharmacie" msprop:Generator_ColumnPropNameInTable="PharmacieColumn" msprop:Generator_UserColumnName="Pharmacie" type="xs:string" minOccurs="0" />
              <xs:element name="debut" msprop:Generator_ColumnVarNameInTable="columndebut" msprop:Generator_ColumnPropNameInRow="debut" msprop:Generator_ColumnPropNameInTable="debutColumn" msprop:Generator_UserColumnName="debut" type="xs:string" minOccurs="0" />
              <xs:element name="fin" msprop:Generator_ColumnVarNameInTable="columnfin" msprop:Generator_ColumnPropNameInRow="fin" msprop:Generator_ColumnPropNameInTable="finColumn" msprop:Generator_UserColumnName="fin" type="xs:string" minOccurs="0" />
              <xs:element name="Categorie" msprop:Generator_ColumnVarNameInTable="columnCategorie" msprop:Generator_ColumnPropNameInRow="Categorie" msprop:Generator_ColumnPropNameInTable="CategorieColumn" msprop:Generator_UserColumnName="Categorie" type="xs:string" minOccurs="0" />
              <xs:element name="DatePeremption" msprop:Generator_ColumnVarNameInTable="columnDatePeremption" msprop:Generator_ColumnPropNameInRow="DatePeremption" msprop:Generator_ColumnPropNameInTable="DatePeremptionColumn" msprop:Generator_UserColumnName="DatePeremption" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>