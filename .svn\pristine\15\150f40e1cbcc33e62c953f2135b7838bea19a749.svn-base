﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fHistoriquesMouvements
    Public Class MouvementArticle
        Public TypeOperation As String
        Public NumOperation As String
        Public TypeMouvement As String
        Public AncienStock As Integer
        Public Qte As Integer
        Public NouveauStock As Integer
        Public DateOperation As Date
    End Class
    Dim MouvementArticles As List(Of MouvementArticle) = New List(Of MouvementArticle)
    Dim StrSQL As String = ""

    Dim cmdMouvement As New SqlCommand
    Dim daMouvement As New SqlDataAdapter
    Dim dsMouvement As New DataSet
    Dim cmd As New SqlCommand
    Dim ds As New DataSet
    Dim da As New SqlDataAdapter
    Dim StrSQLINJ As String = ""

    Dim cmdInjection As New SqlCommand
    Dim daInjection As New SqlDataAdapter

    Dim vCodeTypePre As String = ""
    Dim vArticlefractionneMere As Integer = 0

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub init()

        'liste des types operations articles
        ' initTypeOperation()

        'liste des types mouvements articles
        initTypeMouvement()

        ' liste des articles
        initArticle()

        'liste fournisseurs
        intiFournisseur()

        'liste LAbo
        initLabo()

        'pour initialiser la date
        initDate()

        'Initialiser la table Mouvement Article
        initMouvementArticle()

        'Initialiser la table Mouvement Article
        initMouvementArticleGlobal()

        'pour initialiser la grid
        initgArticle()

        initgArticleGlobal()


        dtpDebut.Focus()

    End Sub

    Dim DateReference As String = ""
    Dim StockReference As String = ""

    Private Sub initDate()
        dtpDebut.Text = Date.Today
        dtpFin.Text = Date.Today
        System.DateTime.Now.Date.ToString()
    End Sub

    Private Sub initLabo()
        'chargement des Labo
        StrSQL = "SELECT DISTINCT NomLabo, CodeLabo FROM LABORATOIRE WHERE SupprimeLabo = 0 ORDER BY NomLabo ASC"
        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL
        da = New SqlDataAdapter(cmd)
        da.Fill(ds, "LABO")
        cbLabo.DataSource = ds.Tables("LABO")
        cbLabo.ValueMember = "CodeLabo"
        cbLabo.DisplayMember = "NomLabo"
        cbLabo.ColumnHeaders = False
        cbLabo.Splits(0).DisplayColumns("CodeLabo").Width = 0
        cbLabo.Splits(0).DisplayColumns("CodeLabo").Visible = False
        cbLabo.Splits(0).DisplayColumns("NomLabo").Width = 10
        cbLabo.ExtendRightColumn = True

    End Sub

    'Private Sub initTypeOperation()
    '    'chargement des Labo
    '    StrSQL = "SELECT DISTINCT TypeOperation FROM MOUVEMENT_ARTICLE  ORDER BY TypeOperation ASC"
    '    cmd.Connection = ConnectionServeur
    '    cmd.CommandText = StrSQL
    '    da = New SqlDataAdapter(cmd)
    '    da.Fill(ds, "MOUVEMENT_ARTICLE_TypeOperation")
    '    cbTypeOp.DataSource = ds.Tables("MOUVEMENT_ARTICLE")
    '    cbTypeOp.ValueMember = "TypeOperation"
    '    cbTypeOp.DisplayMember = "TypeOperation"
    '    cbTypeOp.ColumnHeaders = False
    '    cbTypeOp.Splits(0).DisplayColumns("TypeOperation").Width = 10
    '    cbTypeOp.ExtendRightColumn = True

    'End Sub


    Private Sub initTypeMouvement()
        'chargement des Labo
        StrSQL = "SELECT DISTINCT TypeMouvement FROM MOUVEMENT_ARTICLE  ORDER BY TypeMouvement ASC"
        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL
        da = New SqlDataAdapter(cmd)
        da.Fill(ds, "MOUVEMENT_ARTICLE")
        cbTypeMvt.DataSource = ds.Tables("MOUVEMENT_ARTICLE")
        cbTypeMvt.ValueMember = "TypeMouvement"
        cbTypeMvt.DisplayMember = "TypeMouvement"
        cbTypeMvt.ColumnHeaders = False
        cbTypeMvt.Splits(0).DisplayColumns("TypeMouvement").Width = 10
        cbTypeMvt.ExtendRightColumn = True

    End Sub

    Private Sub intiFournisseur()

        'Chargement des fournisseurs
        StrSQL = "SELECT CodeFournisseur, NomFournisseur FROM FOURNISSEUR WHERE Supprimer = 0 ORDER BY NomFournisseur ASC"
        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL
        da = New SqlDataAdapter(cmd)
        da.Fill(ds, "FOURNISSEUR")
        cmbFournisseur.DataSource = ds.Tables("FOURNISSEUR")
        cmbFournisseur.ValueMember = "CodeFournisseur"
        cmbFournisseur.DisplayMember = "NomFournisseur"
        cmbFournisseur.ColumnHeaders = False
        cmbFournisseur.Splits(0).DisplayColumns("CodeFournisseur").Visible = False
        cmbFournisseur.Splits(0).DisplayColumns("NomFournisseur").Width = 10
        cmbFournisseur.ExtendRightColumn = True
    End Sub



    'Initialiser les table Muvement Article
    Private Sub initMouvementArticle()

        cmdMouvement.CommandText = " SELECT TOP(0) " + _
                                   "    TypeOperation AS TypeOperation," + _
                                   "    NumOperation AS NumOperation," + _
                                   "    DateOperation AS DateOperation," + _
                                   "    CONVERT(VARCHAR(MAX), '00:00:00') AS TIME, " + _
                                   "    TypeMouvement AS TypeMouvement, " + _
                                    "    A.Designation AS Designation, " + _
                                   "    F.NomFournisseur AS Fournisseur ," + _
                                    "    L.NomLabo AS Labo, " + _
                                   "    AncienStock AS Stock," + _
                                   "    Qte AS Qte," + _
                                   "     0 AS Entree," + _
                                   "     0 AS Sortie," + _
                                   "    NouveauStock AS NouveauStock " + _
                                   " FROM " + _
                                   "    Mouvement_Article as MA ,Article as A,Fournisseur as F,LABORATOIRE as L" + _
                                     "   where MA.CodeArticle=A.CodeArticle and A.CodeFournisseur=F.CodeFournisseur and A.CodeLabo=L.CodeLabo"


        cmdMouvement.Connection = ConnectionServeur
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "MouvementArticle")
    End Sub

    Private Sub initMouvementArticleGlobal()

        cmdMouvement.CommandText = " SELECT TOP(0) " + _
                                   "    '' AS Article," + _
                                   "    AncienStock AS AncienStock," + _
                                   "    0 AS QteEntree," + _
                                   "    0 AS QteSortie," + _
                                   "    0 AS QteModification," + _
                                   "    NouveauStock AS NouveauStock " + _
                                   " FROM " + _
                                   "    Mouvement_Article "

        cmdMouvement.Connection = ConnectionServeur
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "MouvementArticleGlobal")
    End Sub

    '-----------pour tester si l'article est de type injection
    Private Sub Verification_Injection()
        Dim inj As Integer = 0
        Try
            StrSQLINJ = " SELECT CodePreparation FROM  FORMULE_PREPARATION WHERE CodeTypePreparation=2 "

            cmdInjection.Connection = ConnectionServeur
            cmdInjection.CommandText = StrSQLINJ
            daInjection = New SqlDataAdapter(cmdInjection)
            daInjection.Fill(dsMouvement, "INJECTION")

            For I = 0 To dsMouvement.Tables("INJECTION").Rows.Count - 1
                If dsMouvement.Tables("INJECTION").Rows(I).Item("CodePreparation") = cmbArticle.SelectedValue Then
                    inj = inj + 1
                End If
            Next
            If inj > 0 Then
                For I = 0 To gDetails.RowCount - 1
                    gDetails(I, "NouveauStock") = ""
                    gDetails(I, "Stock") = ""
                Next
            End If
        Catch ex As Exception
            WriteLine(ex.Message)
        End Try
    End Sub

    Public Sub AfficherMouvement()
        Dim Condition As String = "1=1"
        If (dsMouvement.Tables.IndexOf("MouvementArticle") > -1) Then
            dsMouvement.Tables("MouvementArticle").Clear()
        End If

        'Composer la condition de la requête    
        If cmbArticle.Text <> "" Then
            Condition += " And   A.CodeArticle = '" & cmbArticle.SelectedValue & "' "
        End If


        If cmbFournisseur.Text <> "" Then
            Condition += " And   A.CodeFournisseur = '" & cmbFournisseur.SelectedValue & "' "
        End If

        If cbLabo.Text <> "" Then
            Condition += " And   A.CodeLabo = '" & cbLabo.SelectedValue & "' "
        End If

        If cbTypeMvt.Text <> "" Then
            Condition += " And TypeMouvement = '" & cbTypeMvt.SelectedValue & "' "
        End If


        If Len(dtpDebut.Text) < 10 Or Len(dtpFin.Text) < 10 Then
            Exit Sub
        End If



        cmdMouvement.CommandText = " SELECT " + _
                                   "    TypeOperation AS TypeOperation," + _
                                   "    CASE WHEN TypeOperation = 'Suppression vente' THEN  'S ' + NumOperation ELSE NumOperation END AS NumOperation," + _
                                   "    DateOperation AS DateOperation," + _
                                   "    CONVERT(VARCHAR(MAX), CONVERT(TIME(0), DateOperation)) AS TIME, " + _
                                   "    TypeMouvement AS TypeMouvement, " + _
                                   "    AncienStock AS Stock," + _
                                   "    Qte AS Qte," + _
                                   "    CASE WHEN TypeMouvement = 'Entrée' OR (TypeOperation = 'Modification achat' AND Qte > 0) THEN CASE when TypeOperation = 'Modification achat' then  ABS(Qte) else Qte end else 0 end AS Entree," + _
                                   "    CASE WHEN TypeMouvement = 'Sortie' OR (TypeOperation = 'Modification achat' AND Qte < 0) THEN CASE WHEN TypeOperation = 'Modification achat' THEN  ABS(Qte) ELSE Qte END ELSE 0 END AS Sortie," + _
                                   "    NouveauStock AS NouveauStock ," + _
                                   "    A.Designation AS Designation , " + _
                                   "     F.NomFournisseur AS Fournisseur ," + _
                                    "    L.NomLabo AS Labo " + _
                                   " FROM " + _
                                   "    Mouvement_Article AS MA ,Article as A,Fournisseur as F,LABORATOIRE as L" + _
                                   " where MA.CodeArticle=A.CodeArticle and A.CodeFournisseur=F.CodeFournisseur and A.CodeLabo=L.CodeLabo" + _
                                    " AND " & Condition &
                                   " AND CONVERT(DATE, DateOperation) Between '" & dtpDebut.Value & "' AND '" & dtpFin.Value & "' " + _
                                   " ORDER BY " + _
                                   "    DateOperation"

        cmdMouvement.Connection = ConnectionServeur
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "MouvementArticle")

        MouvementArticles.Clear()
        For I = dsMouvement.Tables("MouvementArticle").Rows.Count - 1 To 0 Step -1
            Dim MouvementArticle As MouvementArticle = New MouvementArticle With
                                                       {
                                                        .TypeOperation = dsMouvement.Tables("MouvementArticle").Rows(I).Item(0),
                                                        .NumOperation = dsMouvement.Tables("MouvementArticle").Rows(I).Item(1),
                                                        .TypeMouvement = dsMouvement.Tables("MouvementArticle").Rows(I).Item(4),
                                                        .AncienStock = dsMouvement.Tables("MouvementArticle").Rows(I).Item(8),
                                                        .Qte = dsMouvement.Tables("MouvementArticle").Rows(I).Item(9),
                                                        .NouveauStock = dsMouvement.Tables("MouvementArticle").Rows(I).Item(10),
                                                        .DateOperation = dsMouvement.Tables("MouvementArticle").Rows(I).Item(2)
                                                    }
            MouvementArticles.Add(MouvementArticle)
        Next

        ' Somme des achats
        Try
            lTotAchat.Text = MouvementArticles.Where(Function(Item) Item.TypeOperation = "Création achat").Sum(Function(Item) Item.Qte)
            lTotVente.Text = MouvementArticles.Where(Function(Item) Item.TypeOperation = "Création vente").Sum(Function(Item) Item.Qte)
            lTotentre.Text = MouvementArticles.Where(Function(Item) Item.TypeOperation = "Création entrée").Sum(Function(Item) Item.Qte)
            lTotSortie.Text = MouvementArticles.Where(Function(Item) Item.TypeOperation = "Création sortie").Sum(Function(Item) Item.Qte)
            lTotPret.Text = MouvementArticles.Where(Function(Item) Item.TypeOperation = "Création prêt").Sum(Function(Item) Item.Qte)
            lTotEmprunt.Text = MouvementArticles.Where(Function(Item) Item.TypeOperation = "Création emprunt").Sum(Function(Item) Item.Qte)
            lTotalEntree.Text = MouvementArticles.Where(Function(Item) Item.TypeMouvement = "Entrée").Sum(Function(Item) Item.Qte)
            lTotalSortie.Text = MouvementArticles.Where(Function(Item) Item.TypeMouvement = "Sortie").Sum(Function(Item) Item.Qte)
        Catch ex As Exception
        End Try

        ' Somme des achats
        cmdMouvement.CommandText = "SELECT " + _
                                   "    dbo.stock('" + cmbArticle.SelectedValue + "')"
        cmdMouvement.Connection = ConnectionServeur


        'pour initialiser la grid
        initgArticle()

        'si l'article est de tpe injection alors les colonnes stock et stock résiduel 
        Verification_Injection()

    End Sub

    Public Sub AfficherMouvementGlobal()

        If (dsMouvement.Tables.IndexOf("MouvementArticleGlobal") > -1) Then
            dsMouvement.Tables("MouvementArticleGlobal").Clear()
        End If

        'Composer la condition de la requête    
        If Len(dtpDebut.Text) < 10 Or Len(dtpFin.Text) < 10 Then
            Exit Sub
        End If


        cmdMouvement.CommandText = "SELECT DISTINCT " + _
                                    "	ARTICLE.Designation AS Article, " + _
                                    "   SUM(CASE WHEN MA.TypeMouvement = 'Sortie' THEN MA.Qte END) AS QteSortie, " + _
                                    "   SUM(CASE WHEN MA.TypeMouvement = 'Entrée' THEN MA.Qte  END) AS QteEntree, " + _
                                    "   SUM(CASE WHEN MA.TypeMouvement = 'Modification' THEN MA.Qte END) AS QteModification, " + _
                                    "	(SELECT TOP(1) MOUVEMENT_ARTICLE.AncienStock FROM MOUVEMENT_ARTICLE WHERE MOUVEMENT_ARTICLE.CodeArticle = MA.CodeArticle AND CONVERT(DATE, DateOperation) Between '" & dtpDebut.Value & "' AND '" & dtpFin.Value & "'  ORDER BY MOUVEMENT_ARTICLE.DateOperation) AS AncienStock, " + _
                                    "	(SELECT TOP(1) MOUVEMENT_ARTICLE.NouveauStock FROM MOUVEMENT_ARTICLE WHERE MOUVEMENT_ARTICLE.CodeArticle = MA.CodeArticle AND CONVERT(DATE, DateOperation) Between '" & dtpDebut.Value & "' AND '" & dtpFin.Value & "'  ORDER BY MOUVEMENT_ARTICLE.DateOperation DESC) AS NouveauStock " + _
                                    "FROM " + _
                                    "	MOUVEMENT_ARTICLE AS MA " + _
                                    "	INNER JOIN ARTICLE ON MA.CodeArticle = ARTICLE.CodeArticle " + _
                                    "WHERE " + _
                                    "   CONVERT(DATE, DateOperation) Between '" & dtpDebut.Value & "' AND '" & dtpFin.Value & "' " + _
                                    "GROUP BY " + _
                                    "   ARTICLE.Designation," + _
                                    "   MA.CodeArticle " + _
                                    "ORDER BY " + _
                                    "   ARTICLE.Designation"

        cmdMouvement.Connection = ConnectionServeur
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "MouvementArticleGlobal")

        initgArticleGlobal()


    End Sub

    Private Sub initgArticle()
        With gDetails
            .Columns.Clear()
            .DataSource = dsMouvement
            .DataMember = "MouvementArticle"
            .Rebind(False)
            .Columns("TypeOperation").Caption = "Type d'opération"
            .Columns("NumOperation").Caption = "Numéro"
            .Columns("DateOperation").Caption = "Date"
            .Columns("Designation").Caption = "Désignation"
            .Columns("Fournisseur").Caption = "Fournisseur"
            .Columns("Labo").Caption = "Labo"
            .Columns("Stock").Caption = "Ancien stock"
            .Columns("Qte").Caption = "Qte"
            .Columns("Sortie").Caption = "Sortie"
            .Columns("NouveauStock").Caption = "Nouveau stock"
            .Columns("TIME").Caption = "Heure"
            .Columns("TypeMouvement").Caption = "Type de mouvement"

            .Columns("DateOperation").NumberFormat = "dd/MM/yyyy"
            .Columns("TIME").NumberFormat = "hh:mm:ss"
            .Columns("Entree").NumberFormat = "#"
            .Columns("Sortie").NumberFormat = "#"
            '.Columns("Stock").NumberFormat = "#"
            '.Columns("NouveauStock").NumberFormat = "#"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.C1Preview.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("TypeOperation").Width = 200
            '  .Splits(0).DisplayColumns("TypeOperation").Style.HorizontalAlignment = C1.C1Preview.AlignHorzEnum.Near
            .Splits(0).DisplayColumns("NumOperation").Width = 130
            .Splits(0).DisplayColumns("DateOperation").Width = 120
            .Splits(0).DisplayColumns("Stock").Width = 110
            .Splits(0).DisplayColumns("Entree").Width = 110
            .Splits(0).DisplayColumns("Sortie").Width = 110
            .Splits(0).DisplayColumns("Qte").Visible = False
            .Splits(0).DisplayColumns("TypeMouvement").Visible = False
            .Splits(0).DisplayColumns("NouveauStock").Width = 110
            .Splits(0).DisplayColumns("TypeMouvement").Width = 200
            .Splits(0).DisplayColumns("TIME").Width = 100
            .Splits(0).DisplayColumns("Designation").Width = 300
            .Splits(0).DisplayColumns("Fournisseur").Width = 250
            .Splits(0).DisplayColumns("Labo").Width = 150


            'If vCodeTypePre = "4" Or vArticlefractionneMere > 0 Then
            '    .Splits(0).DisplayColumns("NouveauStock").Visible = False
            'Else
            '    .Splits(0).DisplayColumns("NouveauStock").Visible = True
            'End If


            ''''''''''''
            ''''''''''''
            '.Splits(0).DisplayColumns("NumOperation").Visible = False
            '''''''''''

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .AllowSort = False
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gDetails)
        End With
    End Sub

    Private Sub initgArticleGlobal()
        With gGlobal

            .Columns.Clear()
            .DataSource = dsMouvement
            .DataMember = "MouvementArticleGlobal"
            .Rebind(False)
            .Columns("Article").Caption = "Article"
            .Columns("AncienStock").Caption = "Ancien stock"
            .Columns("NouveauStock").Caption = "Nouveau stock"
            .Columns("QteSortie").Caption = "Sortie"
            .Columns("QteEntree").Caption = "Entrée"
            .Columns("QteModification").Caption = "Inventaire"



            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.C1Preview.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("Article").Width = 600
            '  .Splits(0).DisplayColumns("Article").Style.HorizontalAlignment = C1.C1Preview.AlignHorzEnum.Near
            .Splits(0).DisplayColumns("AncienStock").Width = 200
            .Splits(0).DisplayColumns("QteSortie").Width = 200
            .Splits(0).DisplayColumns("QteEntree").Width = 200
            .Splits(0).DisplayColumns("QteModification").Width = 200
            .Splits(0).DisplayColumns("NouveauStock").Width = 200


            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .AllowSort = False
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gGlobal)
        End With

    End Sub

    Private Sub initArticle()
        'Vider la dataset 
        Try
            dsMouvement.Tables("Article").Clear()

        Catch ex As Exception

        End Try


        'chargement des noms Articles
        StrSQL = " SELECT ARTICLE.CodeArticle," + _
                 " Designation," + _
                 " LibelleForme," + _
                 " StockInitial," + _
                 " DateInitiale," + _
                 " Sum(QteLotArticle) as Stock " + _
                 " FROM  ARTICLE " + _
                 " LEFT OUTER JOIN LOT_ARTICLE ON LOT_ARTICLE.CodeArticle = ARTICLE.CodeArticle " + _
                 " LEFT OUTER JOIN FORME_ARTICLE ON FORME_ARTICLE.CodeForme = ARTICLE.CodeForme " + _
                 " where  ARTICLE.Supprime=0 GROUP BY ARTICLE.CodeArticle, Designation, LibelleForme ,StockInitial , DateInitiale" + _
                 " ORDER BY Designation ASC"
        cmdMouvement.Connection = ConnectionServeur
        cmdMouvement.CommandText = StrSQL
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "Article")


        cmbArticle.DataSource = dsMouvement.Tables("Article")
        cmbArticle.ValueMember = "CodeArticle"
        cmbArticle.DisplayMember = "Designation"
        cmbArticle.ColumnHeaders = False
        cmbArticle.Splits(0).DisplayColumns("CodeArticle").Visible = False
        cmbArticle.Splits(0).DisplayColumns("Designation").Width = 220
        cmbArticle.Splits(0).DisplayColumns("LibelleForme").Width = 90
        cmbArticle.Splits(0).DisplayColumns("StockInitial").Visible = False
        cmbArticle.Splits(0).DisplayColumns("DateInitiale").Visible = False
        cmbArticle.Splits(0).DisplayColumns("Stock").Width = 40
        cmbArticle.ExtendRightColumn = True
    End Sub
    Private Sub dtpDateDebut_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyDown
        If e.KeyCode = Keys.Enter Then
            dtpFin.Focus()
            If chbGlobal.Checked Then
                AfficherMouvementGlobal()
            Else
                AfficherMouvement()
            End If
        End If

    End Sub

    Private Sub dtpDateFin_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyDown
        If e.KeyCode = Keys.Enter Then
            If Not chbGlobal.Checked Then
                cmbArticle.Focus()
            End If
        End If
        If chbGlobal.Checked Then
            AfficherMouvementGlobal()
        Else
            AfficherMouvement()
        End If
    End Sub

    Private Sub cmbArticle_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbArticle.KeyDown

        cmbArticle.OpenCombo()
        If e.KeyCode = Keys.Enter Then
            cmbArticle.CloseCombo()
            If cmbArticle.Text <> "" Then
                cmbArticle.Text = cmbArticle.Columns("Designation").Value
                AfficherMouvement()
            End If
            ''''dtpDebut.Focus()
            'gDetails.Focus()
        End If

    End Sub

    Private Sub cmbArticle_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbArticle.Validated

        'AfficherMouvement()

    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click

   

        'If lDateReference.Text > dtpDebut.Text + " 00:00:00" Then
        '    dtpDebut.Value = lDateReference.Text
        'End If

        Dim CondCrystal As String = ""
        CondCrystal = " 1=1 AND date({vue_HistoriqueMouvementArticle.DateOperation}) >= date('" & dtpDebut.Text & "') " + _
                      " AND date({vue_HistoriqueMouvementArticle.DateOperation}) <= date('" & dtpFin.Text & "') "

        If (cmbArticle.Text) <> "" Then
            CondCrystal += " AND {vue_HistoriqueMouvementArticle.CodeArticle} = '" & cmbArticle.SelectedValue & "' "
        End If
        If (cmbFournisseur.Text) <> "" Then
        CondCrystal += " AND {vue_HistoriqueMouvementArticle.CodeFournisseur} = '" & cmbFournisseur.SelectedValue & "' "
        End If
        If (cbLabo.Text) <> "" Then
            CondCrystal += " AND {vue_HistoriqueMouvementArticle.CodeLabo} = '" & cbLabo.SelectedValue & "' "
        End If
        'AND {CodeArticle} = '" & cmbArticle.SelectedValue & "' "
        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression de Historique de mouvement d'articles" Then
                num = I
            End If
        Next
        CR.FileName = Application.StartupPath + "\EtatHistoriqueMouvementArticle.rpt"

        CR.SetParameterValue("debut", dtpDebut.Text)
        CR.SetParameterValue("fin", dtpFin.Text)
        CR.SetParameterValue("Article", cmbArticle.Text)
        CR.SetParameterValue("Fournisseur", cmbFournisseur.Text)
        CR.SetParameterValue("Labo", cbLabo.Text)

        CR.SetParameterValue("pPharmacie", Pharmacie)

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo

        Try
            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent
        Catch ex As Exception
            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent
        End Try

        CR.RecordSelectionFormula = CondCrystal
        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression de Historique de mouvement d'articles"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If



    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            If chbGlobal.Checked Then
                AfficherMouvementGlobal()
            Else
                AfficherMouvement()
            End If
        End If
    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            If chbGlobal.Checked Then
                AfficherMouvementGlobal()
            Else
                AfficherMouvement()
            End If
        End If

    End Sub



    Private Sub gDetails_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gDetails.KeyUp

        If (e.KeyCode = Keys.F1 Or e.KeyCode = Keys.Enter) And gDetails(gDetails.Row, "TypeOperation") = "Création vente" Then
            Dim MyVenteAffiche As New fVenteJusteAffichage
            MyVenteAffiche.NumeroVente = gDetails(gDetails.Row, "NumOperation")
            MyVenteAffiche.ShowDialog()
            MyVenteAffiche.Close()
            MyVenteAffiche.Dispose()

        End If

        If (e.KeyCode = Keys.F1 Or e.KeyCode = Keys.Enter) And (gDetails(gDetails.Row, "TypeOperation") = "Création achat" Or gDetails(gDetails.Row, "TypeOperation") = "Modification achat") Then
            Dim MyAchatAffiche As New fAchatJusteAffichage
            MyAchatAffiche.NumeroAchat = gDetails(gDetails.Row, "NumOperation")
            MyAchatAffiche.ShowDialog()
            MyAchatAffiche.Close()
            MyAchatAffiche.Dispose()

        End If
    End Sub

    Private Sub cmbArticle_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbArticle.KeyUp
        If e.KeyCode = Keys.Enter Then

            AfficherMouvement()
        End If

        cmbArticle.Focus()

    End Sub




    Private Sub gDetails_FetchRowStyle(sender As System.Object, e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gDetails.FetchRowStyle
        'If gDetails.Columns("TypeOperation").CellText(e.Row).ToString().Contains("vente") Then
        '    e.CellStyle.BackColor = System.Drawing.Color.White
        'End If
        'If gDetails.Columns("TypeOperation").CellText(e.Row).ToString().Contains("achat") Then
        '    e.CellStyle.BackColor = System.Drawing.Color.LightGoldenrodYellow
        'End If
        'If gDetails.Columns("TypeOperation").CellText(e.Row).ToString().Contains("entrée") Then
        '    e.CellStyle.BackColor = System.Drawing.Color.Pink
        'End If
        'If gDetails.Columns("TypeOperation").CellText(e.Row).ToString().Contains("sortie") Then
        '    e.CellStyle.BackColor = System.Drawing.Color.Azure
        'End If
        If gDetails.Columns("TypeOperation").CellText(e.Row).ToString().Contains("inventaire") Then
            e.CellStyle.BackColor = System.Drawing.Color.Pink
        End If
    End Sub

    Private Sub chbGlobal_CheckedChanged(sender As System.Object, e As System.EventArgs) Handles chbGlobal.CheckedChanged
        gGlobal.Visible = chbGlobal.Checked
        gDetails.Visible = Not chbGlobal.Checked
        cmbArticle.Visible = Not chbGlobal.Checked
        lArtcile.Visible = Not chbGlobal.Checked
        gbTotal.Visible = Not chbGlobal.Checked
        gbEnSo.Visible = Not chbGlobal.Checked
    End Sub

    Private Sub cmbFournisseur_KeyDown(sender As Object, e As KeyEventArgs) Handles cmbFournisseur.KeyDown

        cmbFournisseur.OpenCombo()
        If e.KeyCode = Keys.Enter Then
            cmbFournisseur.CloseCombo()
            If cmbFournisseur.Text <> "" Then
                cmbFournisseur.Text = cmbFournisseur.Columns("NomFournisseur").Value
                AfficherMouvement()
            End If
        End If
    End Sub

    Private Sub cmbFournisseur_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbFournisseur.KeyUp
        If e.KeyCode = Keys.Enter Then

            AfficherMouvement()
        End If

        cmbFournisseur.Focus()

    End Sub

    Private Sub cbTypeMvt_KeyUp(sender As Object, e As KeyEventArgs) Handles cbTypeMvt.KeyUp
        If e.KeyCode = Keys.Enter Then
            If chbGlobal.Checked Then
                AfficherMouvementGlobal()
            Else
                AfficherMouvement()
            End If
        End If
    End Sub

    Private Sub cbTypeMvt_KeyDown(sender As Object, e As KeyEventArgs) Handles cbTypeMvt.KeyDown
        If e.KeyCode = Keys.Enter Then
            If chbGlobal.Checked Then
                AfficherMouvementGlobal()
            Else
                AfficherMouvement()
            End If
        End If
    End Sub

    Private Sub cbTypeOp_KeyUp(sender As Object, e As KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            If chbGlobal.Checked Then
                AfficherMouvementGlobal()
            Else
                AfficherMouvement()
            End If
        End If
    End Sub

    Private Sub cbTypeOp_KeyDown(sender As Object, e As KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            If chbGlobal.Checked Then
                AfficherMouvementGlobal()
            Else
                AfficherMouvement()
            End If
        End If
    End Sub
End Class