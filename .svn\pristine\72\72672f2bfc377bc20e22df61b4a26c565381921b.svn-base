﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms
Public Class fListeDesAchats

    Dim cmdListeAchat As New SqlCommand
    Dim cbListeAchat As New SqlCommandBuilder
    Dim dsListeAchat As New DataSet
    Dim daListeAchat As New SqlDataAdapter
    Dim StrSQL As String = ""

    Public Shared NumeroAchat As String = ""

    Public Sub init()
        Dim I As Integer
        Try
            dsListeAchat.Tables("ACHAT").Clear()
        Catch ex As Exception
        End Try

        'intialisation de la gride      
        StrSQL = "SELECT NumeroAchat," + _
                 "Date," + _
                 "NomFournisseur," + _
                 "TotalTTC," + _
                 "[NumeroBL/Facture]," + _
                 "DateBlFacture," + _
                 "Nom " + _
                 "FROM ACHAT " + _
                 "LEFT OUTER JOIN FOURNISSEUR ON ACHAT.CodeFournisseur=FOURNISSEUR.CodeFournisseur " + _
                 "LEFT OUTER JOIN UTILISATEUR ON ACHAT.CodePersonnel=UTILISATEUR.CodeUtilisateur "

        cmdListeAchat.Connection = ConnectionServeur
        cmdListeAchat.CommandText = StrSQL
        daListeAchat = New SqlDataAdapter(cmdListeAchat)
        daListeAchat.Fill(dsListeAchat, "ACHAT")
        With gAchats
            .Columns.Clear()
            Try
                .DataSource = dsListeAchat
            Catch ex As Exception
            End Try
            .DataMember = "ACHAT"
            .Rebind(False)
            .Columns("NumeroAchat").Caption = "Numéro"
            .Columns("Date").Caption = "Date"
            .Columns("NomFournisseur").Caption = "Fournisseur"
            .Columns("TotalTTC").Caption = "Total TTC"
            .Columns("NumeroBL/Facture").Caption = "BL/Facture"
            .Columns("DateBlFacture").Caption = "Date Bl/Facture"
            .Columns("Nom").Caption = "Saisie par"

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("NomFournisseur").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("NumeroAchat").Width = 100
            .Splits(0).DisplayColumns("Date").Width = 100
            .Splits(0).DisplayColumns("NomFournisseur").Width = 120
            .Splits(0).DisplayColumns("TotalTTC").Width = 100
            .Splits(0).DisplayColumns("NumeroBL/Facture").Width = 70
            .Splits(0).DisplayColumns("DateBlFacture").Width = 70
            .Splits(0).DisplayColumns("Nom").Width = 70

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            '.AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

            .VisualStyle = VisualStyle.Office2007Blue
            'Style du Caractere et du grid
            ParametreGrid(gAchats)
        End With
        AfficherDetails(gAchats(0, "NumeroAchat"))

    End Sub

    Private Sub fListeDesAchats_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        init()
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        NumeroAchat = ""
        Me.Hide()
    End Sub

    Private Sub bOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOK.Click
        NumeroAchat = gAchats(gAchats.Row, "NumeroAchat")
        Me.Hide()
    End Sub

    Private Sub gAchats_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gAchats.Click

    End Sub

    Private Sub gAchats_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gAchats.KeyUp
        If e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Then
            AfficherDetails(gAchats(gAchats.Row, "NumeroAchat"))
        End If
    End Sub

    Private Sub gAchats_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gAchats.MouseClick
        AfficherDetails(gAchats(gAchats.Row, "NumeroAchat"))
    End Sub

    Private Sub gAchats_MouseDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gAchats.MouseDoubleClick
        bOK_Click(sender, e)
    End Sub

    Public Sub AfficherDetails(ByVal NumAchat As String)

        Dim StrSQL As String = ""
        Dim i As Integer = 0
        Dim NumeroPremiereVente As String = ""
        Dim x As Integer = 0

        If (dsListeAchat.Tables.IndexOf("ACHAT_DETAILS") > -1) Then
            dsListeAchat.Tables("ACHAT_DETAILS").Clear()
        End If

        ' chargement des détails 

        StrSQL = "SELECT NumeroAchat," + _
                 "CodeArticle," + _
                 "CodeABarre," + _
                 "Designation," + _
                 "Qte," + _
                 "PrixAchatHT," + _
                 "TotalAchatHT," + _
                 "PrixVenteTTC " + _
                 "FROM ACHAT_DETAILS " + _
                 "WHERE NumeroAchat='" + NumAchat + _
                 "' ORDER BY CodeArticle "

        cmdListeAchat.Connection = ConnectionServeur
        cmdListeAchat.CommandText = StrSQL
        daListeAchat = New SqlDataAdapter(cmdListeAchat)
        daListeAchat.Fill(dsListeAchat, "ACHAT_DETAILS")
        cbListeAchat = New SqlCommandBuilder(daListeAchat)

        With gDetailsCommandes
            .Columns.Clear()
            Try
                .DataSource = dsListeAchat
            Catch ex As Exception
            End Try
            .DataMember = "ACHAT_DETAILS"
            .Rebind(False)
            .Columns("NumeroAchat").Caption = "Numero Achat"
            .Columns("CodeABarre").Caption = "Code"
            .Columns("Designation").Caption = "Designation"
            .Columns("Qte").Caption = "Qte"
            .Columns("PrixAchatHT").Caption = "PrixAchat HT"
            .Columns("TotalAchatHT").Caption = "Total TTC Achat"
            .Columns("PrixVenteTTC").Caption = "Total TTC Achat"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("NumeroAchat").Width = 0
            .Splits(0).DisplayColumns("NumeroAchat").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
            .Splits(0).DisplayColumns("CodeABarre").Width = 100
            .Splits(0).DisplayColumns("Designation").Width = 250
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 100
            .Splits(0).DisplayColumns("TotalAchatHT").Width = 80
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 80

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gDetailsCommandes)
        End With

    End Sub

End Class