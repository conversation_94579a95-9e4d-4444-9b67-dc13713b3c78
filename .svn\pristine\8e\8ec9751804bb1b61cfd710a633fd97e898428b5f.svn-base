﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fProfilUtilisateur
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fProfilUtilisateur))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.gbox = New System.Windows.Forms.GroupBox()
        Me.bAffecterAutorisations = New C1.Win.C1Input.C1Button()
        Me.tCodeProfil = New C1.Win.C1Input.C1TextBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.tRemarque = New C1.Win.C1Input.C1TextBox()
        Me.bAjouter = New C1.Win.C1Input.C1Button()
        Me.tNomProfil = New C1.Win.C1Input.C1TextBox()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.bSupprimer = New C1.Win.C1Input.C1Button()
        Me.gProfil = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Panel.SuspendLayout()
        Me.gbox.SuspendLayout()
        CType(Me.tCodeProfil, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tRemarque, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNomProfil, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gProfil, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.gbox)
        Me.Panel.Controls.Add(Me.bSupprimer)
        Me.Panel.Controls.Add(Me.gProfil)
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.Label1)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1028, 566)
        Me.Panel.TabIndex = 4
        '
        'gbox
        '
        Me.gbox.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gbox.Controls.Add(Me.bAffecterAutorisations)
        Me.gbox.Controls.Add(Me.tCodeProfil)
        Me.gbox.Controls.Add(Me.Label2)
        Me.gbox.Controls.Add(Me.tRemarque)
        Me.gbox.Controls.Add(Me.bAjouter)
        Me.gbox.Controls.Add(Me.tNomProfil)
        Me.gbox.Controls.Add(Me.Label5)
        Me.gbox.Controls.Add(Me.Label3)
        Me.gbox.Location = New System.Drawing.Point(12, 415)
        Me.gbox.Name = "gbox"
        Me.gbox.Size = New System.Drawing.Size(900, 140)
        Me.gbox.TabIndex = 124
        Me.gbox.TabStop = False
        '
        'bAffecterAutorisations
        '
        Me.bAffecterAutorisations.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAffecterAutorisations.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAffecterAutorisations.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAffecterAutorisations.Location = New System.Drawing.Point(769, 77)
        Me.bAffecterAutorisations.Name = "bAffecterAutorisations"
        Me.bAffecterAutorisations.Size = New System.Drawing.Size(125, 45)
        Me.bAffecterAutorisations.TabIndex = 5
        Me.bAffecterAutorisations.Text = "Affecter des Autorisations"
        Me.bAffecterAutorisations.UseVisualStyleBackColor = True
        Me.bAffecterAutorisations.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tCodeProfil
        '
        Me.tCodeProfil.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodeProfil.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodeProfil.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodeProfil.Location = New System.Drawing.Point(110, 17)
        Me.tCodeProfil.Name = "tCodeProfil"
        Me.tCodeProfil.Size = New System.Drawing.Size(193, 19)
        Me.tCodeProfil.TabIndex = 0
        Me.tCodeProfil.Tag = Nothing
        Me.tCodeProfil.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodeProfil.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label2.Location = New System.Drawing.Point(32, 62)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(72, 15)
        Me.Label2.TabIndex = 118
        Me.Label2.Text = "Remarque :"
        '
        'tRemarque
        '
        Me.tRemarque.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tRemarque.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tRemarque.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tRemarque.Location = New System.Drawing.Point(110, 63)
        Me.tRemarque.Multiline = True
        Me.tRemarque.Name = "tRemarque"
        Me.tRemarque.Size = New System.Drawing.Size(418, 66)
        Me.tRemarque.TabIndex = 2
        Me.tRemarque.Tag = Nothing
        Me.tRemarque.Value = ""
        Me.tRemarque.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tRemarque.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouter
        '
        Me.bAjouter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouter.Image = Global.Pharma2000Premium.My.Resources.Resources.nouveau
        Me.bAjouter.Location = New System.Drawing.Point(769, 24)
        Me.bAjouter.Name = "bAjouter"
        Me.bAjouter.Size = New System.Drawing.Size(125, 45)
        Me.bAjouter.TabIndex = 3
        Me.bAjouter.Text = "Ajouter   F5"
        Me.bAjouter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bAjouter.UseVisualStyleBackColor = True
        Me.bAjouter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tNomProfil
        '
        Me.tNomProfil.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNomProfil.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNomProfil.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNomProfil.Location = New System.Drawing.Point(110, 40)
        Me.tNomProfil.Name = "tNomProfil"
        Me.tNomProfil.Size = New System.Drawing.Size(193, 19)
        Me.tNomProfil.TabIndex = 1
        Me.tNomProfil.Tag = Nothing
        Me.tNomProfil.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNomProfil.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label5.Location = New System.Drawing.Point(17, 41)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(87, 15)
        Me.Label5.TabIndex = 115
        Me.Label5.Text = "Nom du profil :"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label3.Location = New System.Drawing.Point(15, 19)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(89, 15)
        Me.Label3.TabIndex = 111
        Me.Label3.Text = "Code du profil :"
        '
        'bSupprimer
        '
        Me.bSupprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bSupprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler2
        Me.bSupprimer.Location = New System.Drawing.Point(918, 465)
        Me.bSupprimer.Name = "bSupprimer"
        Me.bSupprimer.Size = New System.Drawing.Size(100, 45)
        Me.bSupprimer.TabIndex = 4
        Me.bSupprimer.Text = "Supprimer  F7"
        Me.bSupprimer.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bSupprimer.UseVisualStyleBackColor = True
        Me.bSupprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gProfil
        '
        Me.gProfil.AllowUpdate = False
        Me.gProfil.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gProfil.EmptyRows = True
        Me.gProfil.GroupByCaption = "Drag a column header here to group by that column"
        Me.gProfil.Images.Add(CType(resources.GetObject("gProfil.Images"), System.Drawing.Image))
        Me.gProfil.Location = New System.Drawing.Point(12, 74)
        Me.gProfil.Name = "gProfil"
        Me.gProfil.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gProfil.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gProfil.PreviewInfo.ZoomFactor = 75.0R
        Me.gProfil.PrintInfo.PageSettings = CType(resources.GetObject("gProfil.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gProfil.Size = New System.Drawing.Size(1004, 335)
        Me.gProfil.TabIndex = 125
        Me.gProfil.Text = "C1TrueDBGrid1"
        Me.gProfil.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gProfil.PropBag = resources.GetString("gProfil.PropBag")
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(912, 12)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(104, 45)
        Me.bQuitter.TabIndex = 87
        Me.bQuitter.Text = "Fermer             F12   "
        Me.bQuitter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("HandelGotDLig", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(12, 20)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(894, 33)
        Me.Label1.TabIndex = 86
        Me.Label1.Text = "LISTE DES PROFILS"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'fProfilUtilisateur
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1028, 566)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fProfilUtilisateur"
        Me.Text = "fProfilUtilisateur"
        Me.Panel.ResumeLayout(False)
        Me.gbox.ResumeLayout(False)
        Me.gbox.PerformLayout()
        CType(Me.tCodeProfil, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tRemarque, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNomProfil, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gProfil, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents gbox As System.Windows.Forms.GroupBox
    Friend WithEvents bAffecterAutorisations As C1.Win.C1Input.C1Button
    Friend WithEvents tCodeProfil As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents tRemarque As C1.Win.C1Input.C1TextBox
    Friend WithEvents bAjouter As C1.Win.C1Input.C1Button
    Friend WithEvents tNomProfil As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents bSupprimer As C1.Win.C1Input.C1Button
    Friend WithEvents gProfil As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents Label1 As System.Windows.Forms.Label
End Class
