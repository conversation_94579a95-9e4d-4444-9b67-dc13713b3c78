﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="StockManagementModel" Alias="Self" p1:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:p1="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
  <EntityContainer Name="StockManagementEntities" p1:LazyLoadingEnabled="true">
    <EntitySet Name="ARTICLE" EntityType="StockManagementModel.ARTICLE" />
    <EntitySet Name="INVENTAIRE" EntityType="StockManagementModel.INVENTAIRE" />
    <EntitySet Name="MOUVEMENT_ARTICLE" EntityType="StockManagementModel.MOUVEMENT_ARTICLE" />
    <EntitySet Name="MOUVEMENT_LOT_ARTICLE" EntityType="StockManagementModel.MOUVEMENT_LOT_ARTICLE" />
    <FunctionImport Name="P_List_ArticlesRecherche" EntitySet="V_List_ArticlesRecherche" ReturnType="Collection(StockManagementModel.V_List_ArticlesRecherche)" />
    <FunctionImport Name="P_List_Inventaire" EntitySet="V_List_Inventaire" ReturnType="Collection(StockManagementModel.V_List_Inventaire)">
      <Parameter Name="Tous" Mode="In" Type="Boolean" />
    </FunctionImport>
    <EntitySet Name="V_List_ArticlesRecherche" EntityType="StockManagementModel.V_List_ArticlesRecherche" />
    <EntitySet Name="V_List_Inventaire" EntityType="StockManagementModel.V_List_Inventaire" />
    <AssociationSet Name="INVENTAIREV_List_Inventaire" Association="StockManagementModel.INVENTAIREV_List_Inventaire">
      <End EntitySet="INVENTAIRE" Role="INVENTAIRE" />
      <End EntitySet="V_List_Inventaire" Role="V_List_Inventaire" />
    </AssociationSet>
    <FunctionImport Name="P_List_NouvelInventaire" EntitySet="V_List_NouvelInventaire" ReturnType="Collection(StockManagementModel.V_List_NouvelInventaire)">
      <Parameter Name="Section" Mode="In" Type="String" />
      <Parameter Name="DebutIntervalle" Mode="In" Type="String" />
      <Parameter Name="FinIntervalle" Mode="In" Type="String" />
      <Parameter Name="Forme" Mode="In" Type="String" />
      <Parameter Name="Categorie" Mode="In" Type="String" />
      <Parameter Name="Laboratoire" Mode="In" Type="String" />
      <Parameter Name="Rayon" Mode="In" Type="String" />
    </FunctionImport>
    <EntitySet Name="V_List_NouvelInventaire" EntityType="StockManagementModel.V_List_NouvelInventaire" />
    <EntitySet Name="LOT_ARTICLE" EntityType="StockManagementModel.LOT_ARTICLE" />
    <AssociationSet Name="FK_LOTS_ARTICLE_ARTICLE" Association="StockManagementModel.FK_LOTS_ARTICLE_ARTICLE">
      <End EntitySet="ARTICLE" Role="ARTICLE" />
      <End EntitySet="LOT_ARTICLE" Role="LOT_ARTICLE" />
    </AssociationSet>
    <FunctionImport Name="P_List_LotArticleInventaire" EntitySet="V_List_LotArticleInventaire" ReturnType="Collection(StockManagementModel.V_List_LotArticleInventaire)">
      <Parameter Name="CodeArticle" Mode="In" Type="String" />
      <Parameter Name="DebutIntervalle" Mode="In" Type="String" />
      <Parameter Name="FinIntervalle" Mode="In" Type="String" />
      <Parameter Name="Forme" Mode="In" Type="String" />
      <Parameter Name="Categorie" Mode="In" Type="String" />
      <Parameter Name="Laboratoire" Mode="In" Type="String" />
      <Parameter Name="Rayon" Mode="In" Type="String" />
    </FunctionImport>
    <EntitySet Name="V_List_LotArticleInventaire" EntityType="StockManagementModel.V_List_LotArticleInventaire" />
    <EntitySet Name="INVENTAIRE_DETAILS" EntityType="StockManagementModel.INVENTAIRE_DETAILS" />
    <AssociationSet Name="FK_INVENTAIRE_DETAILS_LOT_ARTICLE" Association="StockManagementModel.FK_INVENTAIRE_DETAILS_LOT_ARTICLE">
      <End Role="LOT_ARTICLE" EntitySet="LOT_ARTICLE" />
      <End Role="INVENTAIRE_DETAILS" EntitySet="INVENTAIRE_DETAILS" />
    </AssociationSet>
  </EntityContainer>
  <EntityType Name="ARTICLE">
    <Key>
      <PropertyRef Name="CodeArticle" />
    </Key>
    <Property Name="CodeArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" Type="String" />
    <Property Name="CodeABarre" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" Type="String" />
    <Property Name="Designation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" Type="String" />
    <Property Name="Dosage" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" Type="String" />
    <Property Name="LibelleTableau" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" Type="String" />
    <Property Name="QuantiteUnitaire" Nullable="false" Precision="6" Scale="0" Type="Decimal" />
    <Property Name="ContenanceArticle" Nullable="false" Type="Int32" />
    <Property Name="PrixAchatHT" Nullable="false" Precision="18" Scale="3" Type="Decimal" />
    <Property Name="PrixAchatTTC" Nullable="false" Precision="18" Scale="3" Type="Decimal" />
    <Property Name="PrixVenteHT" Nullable="false" Precision="18" Scale="3" Type="Decimal" />
    <Property Name="PrixVenteTTC" Nullable="false" Precision="18" Scale="3" Type="Decimal" />
    <Property Name="TVA" Nullable="false" Precision="18" Scale="3" Type="Decimal" />
    <Property Name="Marge" Nullable="false" Precision="18" Scale="3" Type="Decimal" />
    <Property Name="Exonorertva" Nullable="false" Type="Boolean" />
    <Property Name="HR" Nullable="false" Precision="18" Scale="3" Type="Decimal" />
    <Property Name="CodePCT" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" Type="String" />
    <Property Name="CodeCategorieCNAM" Type="Int32" />
    <Property Name="TarifDeReference" Nullable="false" Precision="18" Scale="3" Type="Decimal" />
    <Property Name="AccordPrealable" Nullable="false" Type="Boolean" />
    <Property Name="PriseEnCharge" Nullable="false" Type="Boolean" />
    <Property Name="SansCodeBarre" Nullable="false" Type="Boolean" />
    <Property Name="SansVignette" Nullable="false" Type="Boolean" />
    <Property Name="StockAlerte" Precision="6" Scale="0" Type="Decimal" />
    <Property Name="QteACommander" Nullable="false" Precision="6" Scale="0" Type="Decimal" />
    <Property Name="DateAlerte" Precision="0" Type="DateTime" />
    <Property Name="DateDerniereCommande" Precision="0" Type="DateTime" />
    <Property Name="QuantiteDernierCommande" Precision="6" Scale="0" Type="Decimal" />
    <Property Name="DateInitiale" Precision="0" Type="DateTime" />
    <Property Name="StockInitial" Nullable="false" Type="Int32" />
    <Property Name="CodeForme" Type="Int32" Nullable="false" />
    <Property Name="CodeCategorie" Nullable="false" Type="Int32" />
    <Property Name="CodeLabo" Type="Int32" />
    <Property Name="Rayon" MaxLength="50" FixedLength="false" Unicode="false" Type="String" Nullable="false" />
    <Property Name="CodeSituation" Type="Int32" />
    <Property Name="CodeOperateur" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" Type="String" />
    <Property Name="CodeGroupement" Type="Int32" />
    <Property Name="CodeTypePreparation" Type="Int32" />
    <Property Name="Section" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" Type="String" />
    <Property Name="DCI1" Type="Int32" />
    <Property Name="DCI2" Type="Int32" />
    <Property Name="DCI3" Type="Int32" />
    <Property Name="Supprime" Nullable="false" Type="Boolean" />
    <Property Name="FemmeEnceinte" Nullable="false" Type="Boolean" />
    <Property Name="StockArticle" Nullable="false" Precision="6" Scale="0" Type="Decimal" />
    <NavigationProperty Name="LOT_ARTICLE" Relationship="StockManagementModel.FK_LOTS_ARTICLE_ARTICLE" FromRole="ARTICLE" ToRole="LOT_ARTICLE" />
    <Property Type="String" Name="CodeFournisseur" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="NombreCommande" />
  </EntityType>
  <EntityType Name="INVENTAIRE">
    <Key>
      <PropertyRef Name="NumeroInventaire" />
    </Key>
    <Property Name="NumeroInventaire" Type="String" Nullable="false" MaxLength="255" Unicode="false" FixedLength="false" />
    <Property Name="Date" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="ValeurAchatInitial" Type="Decimal" Nullable="false" Precision="18" Scale="3" />
    <Property Name="ValeurVenteInitial" Type="Decimal" Nullable="false" Precision="18" Scale="3" />
    <Property Name="ValeurAchatActuelle" Type="Decimal" Nullable="false" Precision="18" Scale="3" />
    <Property Name="ValeurVenteActuelle" Type="Decimal" Nullable="false" Precision="18" Scale="3" />
    <Property Name="ValeurAchatDifference" Type="Decimal" Nullable="false" Precision="18" Scale="3" />
    <Property Name="ValeurVenteDifference" Type="Decimal" Nullable="false" Precision="18" Scale="3" />
    <Property Name="Remarque" Type="String" MaxLength="255" Unicode="false" FixedLength="false" Nullable="false" />
    <Property Name="CodePersonnel" Type="String" MaxLength="50" Unicode="false" FixedLength="false" Nullable="false" />
    <Property Name="Valide" Type="Boolean" Nullable="false" />
    <NavigationProperty Name="V_List_Inventaire" Relationship="StockManagementModel.INVENTAIREV_List_Inventaire" FromRole="INVENTAIRE" ToRole="V_List_Inventaire" />
    <Property Type="Boolean" Name="EnCours" />
    <Property Type="Int32" Name="Poste" />
  </EntityType>
  <EntityType Name="MOUVEMENT_ARTICLE">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
    <Property Name="CodeArticle" Type="String" Nullable="false" MaxLength="50" Unicode="true" FixedLength="false" />
    <Property Name="AncienStock" Type="Int32" Nullable="false" />
    <Property Name="Qte" Type="Int32" Nullable="false" />
    <Property Name="NouveauStock" Type="Int32" Nullable="false" />
    <Property Name="TypeOperation" Type="String" Nullable="false" MaxLength="300" Unicode="false" FixedLength="false" />
    <Property Name="TypeMouvement" Type="String" Nullable="false" MaxLength="300" Unicode="false" FixedLength="false" />
    <Property Name="NumOperation" Type="String" Nullable="false" MaxLength="300" Unicode="false" FixedLength="false" />
    <Property Name="DateOperation" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="Utilisateur" Type="String" Nullable="false" MaxLength="50" Unicode="false" FixedLength="false" />
  </EntityType>
  <EntityType Name="MOUVEMENT_LOT_ARTICLE">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
    <Property Name="CodeArticle" Type="String" Nullable="false" MaxLength="50" Unicode="false" FixedLength="false" />
    <Property Name="NumeroLotArticle" Type="String" Nullable="false" MaxLength="255" Unicode="false" FixedLength="false" />
    <Property Name="DatePeremptionArticle" Type="DateTime" Precision="0" />
    <Property Name="AncienStock" Type="Int32" Nullable="false" />
    <Property Name="Qte" Type="Int32" Nullable="false" />
    <Property Name="NouveauStock" Type="Int32" Nullable="false" />
    <Property Name="TypeOperation" Type="String" Nullable="false" MaxLength="300" Unicode="false" FixedLength="false" />
    <Property Name="TypeMouvement" Type="String" Nullable="false" MaxLength="300" Unicode="false" FixedLength="false" />
    <Property Name="NumOperation" Type="String" Nullable="false" MaxLength="300" Unicode="false" FixedLength="false" />
    <Property Name="DateOperation" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="Utilisateur" Type="String" Nullable="false" MaxLength="50" Unicode="false" FixedLength="false" />
    <Property Name="PrixAchatHT" Type="Decimal" Nullable="false" Precision="18" Scale="3" />
    <Property Name="PrixAchatTTC" Type="Decimal" Nullable="false" Precision="18" Scale="3" />
    <Property Name="PrixVenteHT" Type="Decimal" Nullable="false" Precision="18" Scale="3" />
    <Property Name="PrixVenteTTC" Type="Decimal" Nullable="false" Precision="18" Scale="3" />
    <Property Name="TVA" Type="Decimal" Nullable="false" Precision="18" Scale="3" />
    <Property Name="Marge" Type="Decimal" Nullable="false" Precision="18" Scale="3" />
    <Property Name="HR" Type="Decimal" Nullable="false" Precision="18" Scale="3" />
    <Property Name="Remise" Type="Decimal" Nullable="false" Precision="18" Scale="3" />
  </EntityType>
  <EntityType Name="V_List_ArticlesRecherche">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="String" Name="CodeArticle" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeABarre" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Designation" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Decimal" Name="PrixVenteHT" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="PrixVenteTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="PrixAchatHT" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="PrixAchatTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TVA" Precision="18" Scale="3" />
    <Property Type="Int32" Name="Stock" />
    <Property Type="String" Name="Rayon" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="LibelleForme" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="LibelleCategorie" MaxLength="255" FixedLength="false" Unicode="false" />
  </EntityType>
  <EntityType Name="V_List_Inventaire">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="String" Name="NumeroInventaire" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeArticle" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeABarre" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Designation" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="StockInitial" />
    <Property Type="Int32" Name="StockActuel" />
    <Property Type="Decimal" Name="PrixVenteTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="PrixAchatTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalAchatTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="QuantiteUnitaire" Precision="6" Scale="0" />
  </EntityType>
  <Association Name="INVENTAIREV_List_Inventaire">
    <End Type="StockManagementModel.INVENTAIRE" Multiplicity="0..1" Role="INVENTAIRE" />
    <End Type="StockManagementModel.V_List_Inventaire" Multiplicity="*" Role="V_List_Inventaire" />
    <ReferentialConstraint>
      <Principal Role="INVENTAIRE">
        <PropertyRef Name="NumeroInventaire" />
      </Principal>
      <Dependent Role="V_List_Inventaire">
        <PropertyRef Name="NumeroInventaire" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <EntityType Name="V_List_NouvelInventaire">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="String" Name="NumeroInventaire" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeArticle" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeABarre" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Designation" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="StockInitial" />
    <Property Type="Int32" Name="StockActuel" />
    <Property Type="Decimal" Name="PrixVenteTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="PrixAchatTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalAchatTTC" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="QuantiteUnitaire" Precision="6" Scale="0" />
  </EntityType>
  <EntityType Name="LOT_ARTICLE">
    <Key>
      <PropertyRef Name="NumeroLotArticle" />
      <PropertyRef Name="CodeArticle" />
    </Key>
    <Property Name="NumeroLotArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" Type="String" />
    <Property Name="CodeArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" Type="String" />
    <Property Name="QteLotArticle" Nullable="false" Type="Int32" />
    <Property Name="DatePeremptionArticle" Precision="0" Type="DateTime" />
    <NavigationProperty Name="INVENTAIRE_DETAILS" Relationship="StockManagementModel.FK_INVENTAIRE_DETAILS_LOT_ARTICLE" FromRole="LOT_ARTICLE" ToRole="INVENTAIRE_DETAILS" />
  </EntityType>
  <Association Name="FK_LOTS_ARTICLE_ARTICLE">
    <End Type="StockManagementModel.ARTICLE" Multiplicity="1" Role="ARTICLE" />
    <End Type="StockManagementModel.LOT_ARTICLE" Multiplicity="*" Role="LOT_ARTICLE" />
    <ReferentialConstraint>
      <Principal Role="ARTICLE">
        <PropertyRef Name="CodeArticle" />
      </Principal>
      <Dependent Role="LOT_ARTICLE">
        <PropertyRef Name="CodeArticle" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <EntityType Name="V_List_LotArticleInventaire">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Type="Int32" Name="Id" Nullable="false" />
    <Property Type="String" Name="CodeArticle" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NumeroLotArticle" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="DateTime" Name="DatePeremptionArticle" Precision="0" />
    <Property Type="Int32" Name="QteLotArticle" />
    <Property Type="String" Name="Designation" MaxLength="255" FixedLength="false" Unicode="false" />
  </EntityType>
  <EntityType Name="INVENTAIRE_DETAILS">
    <Key>
      <PropertyRef Name="NumeroInventaire" />
      <PropertyRef Name="CodeArticle" />
      <PropertyRef Name="NumeroLot" />
    </Key>
    <Property Type="String" Name="NumeroInventaire" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="NumeroLot" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="CodeABarre" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="String" Name="Designation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="CodeForme" Nullable="false" />
    <Property Type="String" Name="Rayon" MaxLength="50" FixedLength="false" Unicode="false" />
    <Property Type="Int32" Name="StockInitial" Nullable="false" />
    <Property Type="Int32" Name="StockActuel" Nullable="false" />
    <Property Type="Decimal" Name="PrixAchatTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalAchatTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="PrixVenteTTC" Nullable="false" Precision="18" Scale="3" />
    <Property Type="Int32" Name="NumLigne" />
    <Property Type="DateTime" Name="DatePeremption" Precision="0" />
    <NavigationProperty Name="LOT_ARTICLE" Relationship="StockManagementModel.FK_INVENTAIRE_DETAILS_LOT_ARTICLE" FromRole="INVENTAIRE_DETAILS" ToRole="LOT_ARTICLE" />
  </EntityType>
  <Association Name="FK_INVENTAIRE_DETAILS_LOT_ARTICLE">
    <End Type="StockManagementModel.LOT_ARTICLE" Role="LOT_ARTICLE" Multiplicity="1" />
    <End Type="StockManagementModel.INVENTAIRE_DETAILS" Role="INVENTAIRE_DETAILS" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="LOT_ARTICLE">
        <PropertyRef Name="NumeroLotArticle" />
        <PropertyRef Name="CodeArticle" />
      </Principal>
      <Dependent Role="INVENTAIRE_DETAILS">
        <PropertyRef Name="NumeroLot" />
        <PropertyRef Name="CodeArticle" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
</Schema>