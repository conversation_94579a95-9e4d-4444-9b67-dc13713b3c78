﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="StockManagementEntities" connectionString="metadata=res://*/StockManagement.csdl|res://*/StockManagement.ssdl|res://*/StockManagement.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=MARWEN\SQLSRV;initial catalog=Pharma;persist security info=True;Integrated Security=True;Connection Timeout=60;user id=s;password=********;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="SaleReportEntities" connectionString="metadata=res://*/SaleReport.csdl|res://*/SaleReport.ssdl|res://*/SaleReport.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=MARWEN\SQLSRV;initial catalog=Pharma;persist security info=True;user id=s;password=********;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="BusinessManagementEntities" connectionString="metadata=res://*/BusinessManagement.csdl|res://*/BusinessManagement.ssdl|res://*/BusinessManagement.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=MARWEN\SQLSRV;initial catalog=pharma;persist security info=True;user id=sa;password=********;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="ErrorManagementEntities" connectionString="metadata=res://*/ErrorManagement.csdl|res://*/ErrorManagement.ssdl|res://*/ErrorManagement.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=MARWEN\SQLSRV;initial catalog=pharma;persist security info=True;user id=sa;password=********;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  </connectionStrings>
  <system.diagnostics>
    <sources>
      <!-- Cette section définit la configuration de l'enregistrement dans le fichier journal de My.Application.Log -->
      <source name="DefaultSource" switchName="DefaultSwitch">
        <listeners>
          <add name="FileLog" />
          <!-- Supprimez les marques de commentaire dans la section suivante pour écrire dans le journal des événements de l'application -->
          <!--<add name="EventLog"/>-->
        </listeners>
      </source>
    </sources>
    <switches>
      <add name="DefaultSwitch" value="Information" />
    </switches>
    <sharedListeners>
      <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter" />
      <!-- Supprimez les marques de commentaire dans la section suivante et remplacez APPLICATION_NAME par le nom de votre application à écrire dans le journal des événements de l'application -->
      <!--<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> -->
    </sharedListeners>
  </system.diagnostics>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="C1.Win.C1Chart.2" publicKeyToken="A22E16972C085838" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.20132.23192" newVersion="2.0.20132.23192" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="C1.Win.C1Chart.2" publicKeyToken="a22e16972c085838" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.20132.23192" newVersion="2.0.20132.23192" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_IServiceOne" />
        <binding name="BCBDextherEtrPortBinding" maxReceivedMessageSize="1000000">
          <security mode="Transport" />
        </binding>
        <binding name="BCBDextherEtrPortBinding1" />
        <binding name="BCBDextherEtrPortBinding2">
          <security mode="Transport" />
        </binding>
        <binding name="BCBDextherEtrPortBinding3" />
      </basicHttpBinding>
    </bindings>
    <client>
      <endpoint address="http://www.nextsoftware.com.tn:8080/ServiceOne.svc/ServiceOneKey" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IServiceOne" contract="ServiceOneKey.IServiceOne" name="BasicHttpBinding_IServiceOne" />
      <endpoint address="https://bcbdexther.com/tn/BCBDexther/BCBDextherEtr" binding="basicHttpBinding" bindingConfiguration="BCBDextherEtrPortBinding" contract="ServiceBCB.BCBDextherEtr" name="BCBDextherEtrPort" />
    </client>
  </system.serviceModel>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" />
  </startup>
</configuration>
<!--maxReceivedMessageSize = "1000000"-->