﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Public Class fListeDeRecalculDeStock

    Dim cmdListe As New SqlCommand
    Dim Cmd As New SqlCommand
    Dim cbListe As New SqlCommandBuilder
    Dim dsListe As New DataSet
    Dim daListe As New SqlDataAdapter

    Dim cmdListeTable As New SqlCommand
    Dim dsListeTable As New DataSet
    Dim daListeTable As New SqlDataAdapter

    Dim cmdListeIndex As New SqlCommand
    Dim dsListeIndex As New DataSet
    Dim daListeIndex As New SqlDataAdapter

    Public Requette As String = ""
    Public Titre As String = ""
    Public ListeDesVentes As Boolean = False

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        Me.Hide()
    End Sub

    Private Sub fListeDeRecalculDeStock_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        'Appel chargement de la liste
        chargerListe()
        grbPatientez.Visible = False

    End Sub

    Private Sub chargerListe()

        Dim i As Integer = 0
        dsListe.Clear()
        'lTitre.Text = Titre

        'chargement de la liste  
        '---------------------------- si la liste est celle des ventes au comptant

        Requette = "SELECT [CodeArticle] " + _
                                      ",[CodeABarre]" + _
                                      ",[Designation]" + _
                                      ",[Forme]" + _
                                      ",[Categorie]" + _
                                      ",[DateDernierInventaire]" + _
                                      ",[TotalEntree]" + _
                                      ",[TotalSortie]" + _
                                      ",[StockCalcule]" + _
                                      ",[StockAcuel] " + _
                                      "FROM [Vue_RecalculDeStock] " + _
                                      "WHERE [StockAcuel]<>[StockCalcule] " + _
                                      "ORDER BY Designation"

        cmdListe.Connection = ConnectionServeur
        cmdListe.CommandText = Requette
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "LISTE")

        With gListe
            .Columns.Clear()
            Try
                .DataSource = dsListe
            Catch ex As Exception
            End Try
            .DataMember = "LISTE"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("Forme").Caption = "Forme"
            .Columns("Categorie").Caption = "Catégorie"
            .Columns("TotalEntree").Caption = "Total Entrée"
            .Columns("TotalSortie").Caption = "Total Sortie"
            .Columns("StockCalcule").Caption = "Stock Calculé"
            .Columns("StockAcuel").Caption = "Stock Acuel"
            .Columns("DateDernierInventaire").Caption = "Date du dernier inventaire"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("CodeABarre").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Forme").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Categorie").Style.HorizontalAlignment = AlignHorzEnum.Near
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
            .Splits(0).DisplayColumns("CodeABarre").Width = 100
            .Splits(0).DisplayColumns("Designation").Width = 200
            .Splits(0).DisplayColumns("Forme").Width = 70
            .Splits(0).DisplayColumns("Categorie").Width = 80
            .Splits(0).DisplayColumns("TotalEntree").Width = 50
            .Splits(0).DisplayColumns("TotalSortie").Width = 50
            .Splits(0).DisplayColumns("StockCalcule").Width = 50
            .Splits(0).DisplayColumns("StockAcuel").Width = 50
            .Splits(0).DisplayColumns("DateDernierInventaire").Width = 100

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            .AllowSort = True
            .AllowFilter = True
            .FilterActive = True
            .FilterBar = True
            'Style du Caractere et du grid
            ParametreGrid(gListe)
        End With
        lNombreLigne.Text = dsListe.Tables("LISTE").Rows.Count.ToString + " Articles"
        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler

    End Sub

    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)
        If e.KeyCode = Keys.F12 Then
            bQuitter_Click(o, e)
            Exit Sub
        End If
    End Sub

    Private Sub bRecalculer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRecalculer.Click

        'Code recalcule 
        Dim cmdRecalcule As New SqlCommand
        Dim StrSQL As String
        Dim msgRes As MsgBoxResult
        ProgressBar.Visible = False


        Try

            'Test s'il y a un inventaire non valide -> Informer linfomaticien 
            StrSQL = "SELECT count(*) FROM INVENTAIRE WHERE Valide = 1"

            Cmd.Connection = ConnectionServeur
            Cmd.CommandText = StrSQL

            If Cmd.ExecuteScalar <> 0 Then
                msgRes = MsgBox("Il existe un inventaire Invalide, Voulez-vous continuer ?", MessageBoxButtons.YesNo + MessageBoxIcon.Question)
                If msgRes = vbNo Then Exit Sub
            End If

            msgRes = MsgBox("L'oppération de Recalcule des stocks peut prendre quelleques secondes, meci de patienter", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "Recalcule des stocks")

            If msgRes = MsgBoxResult.Ok Then

                Application.DoEvents()
                grbPatientez.Visible = True
                Application.DoEvents()
                'Excecuter la Procedure 
                RecalculeDeStock()
                Application.DoEvents()
                grbPatientez.Visible = False
                Application.DoEvents()
                MsgBox("L'opération de Recalcule est terminée avec succés", MsgBoxStyle.Information, "Recalcule des stocks")

                'Vider la liste
                dsListe.Tables("LISTE").Rows.Clear()

                'Recharger  la liste 
                chargerListe()

            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Recalcule des stocks", "fListeDeRecalculDeStock", "bRecalculer_Click", ex.Message, "0000617", "Erreur lors d'executer la requette", True, True, True)
            grbPatientez.Visible = False

        End Try


    End Sub

    Private Sub bMaintenanceBase_Click(sender As System.Object, e As System.EventArgs) Handles bMaintenanceBase.Click

        Try

            grbPatientez.Visible = True
            ProgressBar.Visible = True
            bMaintenanceBase.Enabled = False
            bRecalculer.Enabled = False
            bQuitter.Enabled = False

            dsListeTable.Clear()

            Cmd.Connection = ConnectionServeur
            Cmd.CommandText = "DBCC SHRINKDATABASE(N" + Quote(NomBase) + ", 10, TRUNCATEONLY)"
            Cmd.ExecuteNonQuery()

            'lister les tables de la base
            Requette = "SELECT Name AS TableName FROM " + NomBase + ".sys.objects WHERE Type = 'U' ORDER BY Name"

            cmdListeTable.Connection = ConnectionServeur
            cmdListeTable.CommandText = Requette
            daListeTable = New SqlDataAdapter(cmdListeTable)
            daListeTable.Fill(dsListeTable, "LISTE_TABLE")

            For K As Integer = 0 To dsListeTable.Tables("LISTE_TABLE").Rows.Count - 1

                ProgressBar.Minimum = 0
                ProgressBar.Maximum = dsListeTable.Tables("LISTE_TABLE").Rows.Count - 1
                ProgressBar.Value = K
                Application.DoEvents()

                dsListeIndex.Clear()

                'lister les index de la table
                Requette = " SELECT " + _
                           " DISTINCT I.NAME NomIndex " + _
                           " FROM SYSOBJECTS T " + _
                           " INNER JOIN SYSINDEXES I ON T.ID = I.ID " + _
                           " INNER JOIN SYSINDEXKEYS K " + _
                           " ON I.ID = K.ID AND I.INDID = K.INDID " + _
                           " INNER JOIN SYSCOLUMNS C ON K.ID = C.ID AND K.COLID = C.COLID " + _
                           " WHERE T.NAME = " + Quote(dsListeTable.Tables("LISTE_TABLE").Rows(K)("TableName"))

                cmdListeIndex.Connection = ConnectionServeur
                cmdListeIndex.CommandText = Requette
                daListeIndex = New SqlDataAdapter(cmdListeIndex)
                daListeIndex.Fill(dsListeIndex, "LISTE_INDEX")

                For J As Integer = 0 To dsListeIndex.Tables("LISTE_INDEX").Rows.Count - 1
                    Application.DoEvents()

                    Cmd.CommandText = " ALTER INDEX " + dsListeIndex.Tables("LISTE_INDEX").Rows(J)("NomIndex") + _
                                      " ON [" + dsListeTable.Tables("LISTE_TABLE").Rows(K)("TableName") + "]" + _
                                      " REBUILD PARTITION = ALL " + _
                                      " WITH " + _
                                      " ( " + _
                                      "     FILLFACTOR = 90, " + _
                                      "     PAD_INDEX  = OFF, " + _
                                      "     STATISTICS_NORECOMPUTE  = OFF, " + _
                                      "     ALLOW_ROW_LOCKS  = ON, " + _
                                      "     ALLOW_PAGE_LOCKS  = ON, " + _
                                      "     ONLINE = OFF, " + _
                                      "     SORT_IN_TEMPDB = OFF" + _
                                      " )"
                    Cmd.ExecuteNonQuery()
                Next
            Next
            Application.DoEvents()
            MsgBox("L'opération de maintenance est terminée avec succés", MsgBoxStyle.Information, "Maintenance de la base de données")
            grbPatientez.Visible = False
            bMaintenanceBase.Enabled = True
            bRecalculer.Enabled = True
            bQuitter.Enabled = True

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Maintenance de la base", "fListeDeRecalculDeStock", "bMaintenanceBase_Click", ex.Message, "0000???", "Erreur lors d'executer la requette", True, True, True)
            grbPatientez.Visible = False
            bMaintenanceBase.Enabled = True
            bRecalculer.Enabled = True
            bQuitter.Enabled = True
        End Try
    End Sub
End Class