﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fEcheancesDesFournisseurs
    Dim StrSQL As String = ""

    Dim cmdMouvement As New SqlCommand
    Dim daMouvement As New SqlDataAdapter
    Dim dsMouvement As New DataSet

    Dim CmdCalcul As New SqlCommand   ' juste pour calculer le solde du client

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub init()

        dtpDebut.Text = "01/" + Date.Today.Month.ToString.PadLeft(2, CChar("0")) + "/" + Date.Now.Year.ToString
        dtpFin.Text = NombreDesJoursDuMois(Date.Today.Month, Date.Today.Year).ToString + "/" + Date.Today.Month.ToString.PadLeft(2, CChar("0")) + "/" + Date.Now.Year.ToString

        'If Date.Today.Month <> 2 Then
        '    dtpFin.Text = "30/" + Date.Today.Month.ToString + "/" + Date.Now.Year.ToString
        'Else
        '    dtpFin.Text = "29/" + Date.Today.Month.ToString + "/" + Date.Now.Year.ToString
        'End If

        dtpDebut.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDebut.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpFin.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpFin.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        AfficherEcheances()
        dtpDebut.Focus()

    End Sub

    Public Sub AfficherEcheances()
        Dim I As Integer
        Dim Cond As String = "1=1"
        Dim TotalMontant As Double = 0.0

        If (dsMouvement.Tables.IndexOf("ECHEANCES_FOURNISSEUR") > -1) Then
            dsMouvement.Tables("ECHEANCES_FOURNISSEUR").Clear()
        End If

        'Composer la condition de la requête    

        If dtpDebut.Text <> "" And dtpDebut.Text.Length = 10 Then
            Cond += " AND DateEcheance >='" + dtpDebut.Text.Substring(0, 10) + " 00:00:00'"
        End If

        If dtpFin.Text <> "" And dtpFin.Text.Length = 10 Then
            Cond += " AND DateEcheance <='" + dtpFin.Text.Substring(0, 10) + " 23:59:59'"
        End If

        If chbEncaisse.Checked = False Then
            Cond += " AND Encaisse =0 "
        End If

        cmdMouvement.CommandText = "SELECT REGLEMENT_FOURNISSEUR.CodeFournisseur ," + _
                                   " NomFournisseur ," + _
                                   "LibelleNatureReglement ," + _
                                   "DATE," + _
                                   " DateEcheance ," + _
                                   "LibelleReglement ," + _
                                   "NumeroCheque ," + _
                                   "Montant, " + _
                                   "Encaisse " + _
                                   "FROM REGLEMENT_FOURNISSEUR " + _
                                   "LEFT OUTER JOIN FOURNISSEUR ON REGLEMENT_FOURNISSEUR .CodeFournisseur =FOURNISSEUR.CodeFournisseur  " + _
                                   "LEFT OUTER JOIN NATURE_REGLEMENT ON REGLEMENT_FOURNISSEUR .CodeNatureReglement =NATURE_REGLEMENT .CodeNatureReglement  " + _
                                   "WHERE (LibelleNatureReglement='CHEQUE' OR LibelleNatureReglement='TRAITE') AND " + _
                                   Cond
        'AND convert(char(10) ,DATE,103)<>convert(char(10) ,DateEcheance,103) AND
        cmdMouvement.Connection = ConnectionServeur
        daMouvement = New SqlDataAdapter(cmdMouvement)
        daMouvement.Fill(dsMouvement, "ECHEANCES_FOURNISSEUR")

        With gFournisseur
            .Columns.Clear()
            .DataSource = dsMouvement
            .DataMember = "ECHEANCES_FOURNISSEUR"
            .Rebind(False)
            .Columns("CodeFournisseur").Caption = "Code Fournisseur"
            .Columns("NomFournisseur").Caption = "Nom"
            .Columns("LibelleNatureReglement").Caption = "Mode Paiement"
            .Columns("DATE").Caption = "Date"
            .Columns("DateEcheance").Caption = "Echéance"
            .Columns("LibelleReglement").Caption = "Libellé"
            .Columns("NumeroCheque").Caption = "Num chèque"
            .Columns("Montant").Caption = "Montant"


            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("CodeFournisseur").Width = 100
            .Splits(0).DisplayColumns("NomFournisseur").Width = 150
            .Splits(0).DisplayColumns("LibelleNatureReglement").Width = 120
            .Splits(0).DisplayColumns("LibelleNatureReglement").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("DATE").Width = 100
            .Splits(0).DisplayColumns("DateEcheance").Width = 100
            .Splits(0).DisplayColumns("LibelleReglement").Width = 140
            .Splits(0).DisplayColumns("NumeroCheque").Width = 140
            .Splits(0).DisplayColumns("Montant").Width = 100
            .Splits(0).DisplayColumns("Montant").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Encaisse").Style.HorizontalAlignment = AlignHorzEnum.Far
            '.Splits(0).DisplayColumns("Encaisse").Visible = False



            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gFournisseur)
        End With

        For I = 0 To dsMouvement.Tables("ECHEANCES_FOURNISSEUR").Rows.Count - 1
            TotalMontant = TotalMontant + dsMouvement.Tables("ECHEANCES_FOURNISSEUR").Rows(I).Item("Montant")
        Next

        lTotalMontant.Text = TotalMontant.ToString("### ### ##0.000")

    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherEcheances()
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDebut.TextChanged

    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherEcheances()
        End If
    End Sub

    Private Sub dtpFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpFin.TextChanged

    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim CondCrystal As String = ""
        Dim Somme_Echeance As Double = 0.0
        Dim StrSQLSolde As String = ""

        CondCrystal = "1=1 "

        If dsMouvement.Tables("ECHEANCES_FOURNISSEUR").Rows.Count = 0 Then
            MsgBox("Liset vide !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        If dtpDebut.Text <> "" Then
            CondCrystal = CondCrystal + " AND {Vue_EcheancesDesFournisseurs.DateEcheance} > date ('" + dtpDebut.Text + "')"
        End If

        If dtpFin.Text <> "" Then
            CondCrystal = CondCrystal + " AND {Vue_EcheancesDesFournisseurs.DateEcheance} < date ('" + dtpFin.Text + "')"
        End If

        If chbEncaisse.Checked = False Then
            CondCrystal = CondCrystal + " AND {Vue_EcheancesDesFournisseurs.Encaisse} = false"
        End If

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Echéances des fournisseur" Then
                num = I
            End If
        Next

        CR.FileName = Application.StartupPath + "\EtatEcheanceDesFournisseurs.rpt"

        CR.SetParameterValue("Debut", dtpDebut.Text)
        CR.SetParameterValue("Fin", dtpFin.Text)
        CR.SetParameterValue("Total", lTotalMontant.Text)

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent
        CR.RecordSelectionFormula = CondCrystal
        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression Echéances des fournisseur"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    Private Sub chbEncaisse_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbEncaisse.CheckedChanged
        AfficherEcheances()
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub
End Class