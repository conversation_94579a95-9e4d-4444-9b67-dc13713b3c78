using System;
using System.Threading.Tasks;

namespace PharmaModerne.Core.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion du scanner de codes à barres
    /// </summary>
    public interface IScannerService
    {
        // Événements
        event EventHandler<string> CodeScanned;
        event EventHandler<object> ScannerActivated;
        event EventHandler<object> ScannerDeactivated;
        event EventHandler<object> ScannerError;
        
        // Propriétés
        bool IsActive { get; }
        bool IsConnected { get; }
        string LastScannedCode { get; }
        DateTime? LastScanTime { get; }
        int ScanCount { get; }
        
        // Activation/Désactivation
        Task<bool> ActivateAsync();
        Task<bool> DeactivateAsync();
        Task<bool> ToggleAsync();
        
        // Configuration
        Task<bool> ConfigureAsync(ScannerConfig config);
        Task<ScannerConfig> GetConfigurationAsync();
        Task<bool> TestConnectionAsync();
        
        // Détection et traitement
        bool DetectScannerInput(string input, TimeSpan inputDuration);
        Task<bool> ProcessInputAsync(string input, TimeSpan inputDuration);
        Task<string> CleanScannedCodeAsync(string rawCode);
        Task<bool> ValidateScannedCodeAsync(string code);
        
        // Gestion des codes
        Task<ScanResult> ProcessScannedCodeAsync(string code);
        Task<bool> IsValidBarcodeAsync(string code);
        Task<string> FormatCodeAsync(string code, CodeFormat format);
        
        // Statistiques et historique
        Task<int> GetTodayScanCountAsync();
        Task<int> GetTotalScanCountAsync();
        Task<DateTime?> GetLastScanTimeAsync();
        Task<IEnumerable<ScanHistoryItem>> GetScanHistoryAsync(int count = 100);
        Task<bool> ClearHistoryAsync();
        
        // Paramètres avancés
        Task<bool> SetScanDelayAsync(int milliseconds);
        Task<bool> SetAutoProcessingAsync(bool enabled);
        Task<bool> SetValidationRulesAsync(ValidationRules rules);
        
        // Diagnostic
        Task<ScannerStatus> GetStatusAsync();
        Task<string> GetDiagnosticInfoAsync();
        Task<bool> ResetAsync();
    }
    
    /// <summary>
    /// Configuration du scanner
    /// </summary>
    public class ScannerConfig
    {
        public int ScanDelay { get; set; } = 100; // Délai en ms pour détecter un scan
        public bool AutoProcessing { get; set; } = true;
        public bool AutoCleanup { get; set; } = true;
        public bool AutoValidation { get; set; } = true;
        public CodeFormat DefaultFormat { get; set; } = CodeFormat.UpperCase;
        public ValidationRules ValidationRules { get; set; } = new();
    }
    
    /// <summary>
    /// Règles de validation des codes
    /// </summary>
    public class ValidationRules
    {
        public int MinLength { get; set; } = 1;
        public int MaxLength { get; set; } = 50;
        public string AllowedCharacters { get; set; } = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_";
        public bool RequireAlphaNumeric { get; set; } = true;
        public bool AllowSpaces { get; set; } = false;
    }
    
    /// <summary>
    /// Format des codes
    /// </summary>
    public enum CodeFormat
    {
        None,
        UpperCase,
        LowerCase,
        TitleCase,
        Trimmed,
        AlphaNumericOnly
    }
    
    /// <summary>
    /// Résultat d'un scan
    /// </summary>
    public class ScanResult
    {
        public bool Success { get; set; }
        public string Code { get; set; } = string.Empty;
        public string CleanedCode { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public DateTime ScanTime { get; set; } = DateTime.Now;
        public TimeSpan ProcessingTime { get; set; }
        public ScanType Type { get; set; }
    }
    
    /// <summary>
    /// Type de scan détecté
    /// </summary>
    public enum ScanType
    {
        Unknown,
        ClientCode,
        ArticleCode,
        Barcode,
        Manual
    }
    
    /// <summary>
    /// Statut du scanner
    /// </summary>
    public class ScannerStatus
    {
        public bool IsActive { get; set; }
        public bool IsConnected { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime LastActivity { get; set; }
        public int TotalScans { get; set; }
        public int ErrorCount { get; set; }
        public string Version { get; set; } = string.Empty;
    }
    
    /// <summary>
    /// Élément de l'historique des scans
    /// </summary>
    public class ScanHistoryItem
    {
        public DateTime ScanTime { get; set; }
        public string Code { get; set; } = string.Empty;
        public ScanType Type { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }
    
    /// <summary>
    /// Arguments d'événement du scanner
    /// </summary>
    public class ScannerEventArgs : EventArgs
    {
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public string Message { get; set; } = string.Empty;
    }
    
    /// <summary>
    /// Arguments d'événement d'erreur du scanner
    /// </summary>
    public class ScannerErrorEventArgs : ScannerEventArgs
    {
        public Exception? Exception { get; set; }
        public string ErrorCode { get; set; } = string.Empty;
    }
}
