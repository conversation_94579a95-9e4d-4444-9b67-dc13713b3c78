﻿Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared

Public Class fProduction
    Dim cmdChargement As New SqlCommand
    Dim cbChargement As New SqlCommandBuilder
    Dim dsChargement As New DataSet
    Dim daChargement As New SqlDataAdapter

    Dim cmdProduction As New SqlCommand
    Dim cbProduction As New SqlCommandBuilder
    Dim dsProduction As New DataSet
    Dim daProduction As New SqlDataAdapter
    Dim daProduction1 As New SqlDataAdapter

    Dim cmdProductionEntete As New SqlCommand
    Dim daProductionEntete As New SqlDataAdapter
    Dim cbProductionEntete As New SqlCommandBuilder

    Dim cmdProductionDetail As New SqlCommand
    Dim daProductionDetails As New SqlDataAdapter
    Dim cbProductionDetails As New SqlCommandBuilder


    Dim DataRowRecherche As DataRow

    Dim mode As String = ""
    Dim StrSQL As String = ""

    Public NumeroProduction As String = ""
    Public NumeroligneProduction As Integer = 0

    Public Operateur As Integer = 0

    Public NouvelleEntree As DataRow = Nothing 'datarow pour charger l'entête dans la datatable PRODUCTION
    Public NouvelArticle As DataRow = Nothing 'datarow pour charger les détails dans la datatable PRODUCTION_DETAIL
    Public dr As DataRow = Nothing 'datarow pour charger dans la datatable ARTICLE qui contient les articles recherchés pendant la saisie de la designation

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "114" And bConfirmer.Enabled = True Then
            bConfirmer_Click(sender, e)
        End If
        If argument = "115" And bRecherche.Enabled = True Then
            bRecherche_Click(sender, e)
        End If

        If argument = "116" And bAjouter.Enabled = True Then
            bAjouter_Click(sender, e)
        End If

        If argument = "118" And bSupprimer.Enabled = True Then
            bSupprimer_Click(sender, e)
        End If

        If argument = "119" And bModifier.Enabled = True Then
            'bModifier_Click(sender, e)
            bModifier.PerformClick()
        End If

        If argument = "121" And bAnnuler.Enabled = True Then
            bAnnuler_Click(sender, e)
        End If

        If argument = "122" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If

        If argument = "112" Then
            bComposonts_Click(sender, e)
        End If
        '--------------------- boutton close 
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If

    End Sub

    Public Sub Init()

        'mode en consultation
        mode = "Consultation"

        'Appel Pour selectionner le dernier ligne 
        NumeroligneProduction = selectionDernierLigneProduction()

        'chargement des Entêtes des productions        
        initProductionEntete()

        'chargement des détails des productions 
        initProductionDetails()

        'Appel pour charger les information de le productions en question
        ChargerProduction(NumeroligneProduction)

        'Mise en forme de la Grid gArticles
        initgArticles()

        'Charger Liste Article
        initArticle()

        ' ''Initialiser les controles
        'initLoadControl()

    End Sub

    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        Try
            Quote(ValeurCle)
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurCle)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function

    Public Function RecupererValeurPreparationDetails(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurClePrimaire, ByVal CleSecondaire, ByVal ValeurCleSecondaire)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        Try
            Quote(ValeurClePrimaire)
            Quote(ValeurCleSecondaire)
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurClePrimaire) + "And" + " " + CleSecondaire + "=" + Quote(ValeurCleSecondaire)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click

        'Changer le mode en Ajout
        mode = "Ajout"

        'Appel ChargerProduction: Pour Récuperer la 
        'structure des DS PRODUCTION et PRODUCTION_DETAILS
        'La valeur 0 est inexistant

        ChargerProduction("0")

        'ajout d'un nouvel enregistrement vide dans les datatables convenables
        NouvelArticle = dsProduction.Tables("PRODUCTION_DETAILS").NewRow()
        NouvelArticle("Designation") = ""
        NouvelArticle("CodeArticle") = ""
        NouvelArticle("CodeABarre") = ""
        dsProduction.Tables("PRODUCTION_DETAILS").Rows.Add(NouvelArticle)

        NouvelleEntree = dsProduction.Tables("PRODUCTION").NewRow()
        dsProduction.Tables("PRODUCTION").Rows.Add(NouvelleEntree)

        Me.gArticles.Splits(0).DisplayColumns(1).AllowFocus = False

        Me.gArticles.Splits(0).DisplayColumns(4).AllowFocus = False

        'Me.gArticles.Splits(0).DisplayColumns(6).AllowFocus = True


        'Initialiser les Controls utilisés lors de l'opération de le Production
        initControlProduction()

    End Sub

    Private Sub gArticles_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gArticles.Change
        If (gArticles.Columns(gArticles.Col).DataField() = "Designation" And gArticles.Columns("Designation").Value <> "") Or gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" Then
            Dim StrSQL1 As String = ""
            Dim I As Integer
            Dim NbLigne As Integer
            NbLigne = gArticles.RowCount

            With gListeRecherche
                .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
                .Top = Me.gArticles.Top + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight
            End With

            Try
                dsProduction.Tables("ARTICLE").Clear()
            Catch ex As Exception

            End Try
            If gArticles.Row = dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1 And gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                gListeRecherche.Visible = True
            Else
                gListeRecherche.Visible = False
            End If

            'chargement des articles qui sont mis en jeu
            If gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                If gArticles.Columns("Designation").Value.ToString.Length > 1 Then
                    If gArticles.Columns("Designation").Value.ToString.IndexOf(".") > 0 Then   'gArticles.Columns("Designation").Value.ToString.Substring(gArticles.Columns("Designation").Value.ToString.Length - 1, 1) Like "." And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1))

                        StrSQL1 = "SELECT CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC " + _
                                  "FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE " + _
                                  "ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  "WHERE CodeCategorie =9 AND CodeTypePreparation =1 " + _
                                  " and ltrim(str(PrixVenteTTC,10,3)) LIKE " + _
                                  Quote(gArticles.Columns("Designation").Value + "%") + " AND Supprime=0 ORDER BY PrixVenteTTC"
                    Else
                        StrSQL1 = "SELECT CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC " + _
                                  "FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE " + _
                                  "ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  "WHERE CodeCategorie =9 AND CodeTypePreparation =1 " + _
                                  " AND Designation LIKE " + Quote(gArticles.Columns("Designation").Value + "%") + _
                                  " AND Supprime=0 ORDER BY Designation"
                    End If
                Else
                    StrSQL1 = "SELECT CodeArticle," + _
                              "Designation," + _
                              "LibelleForme," + _
                              "PrixVenteTTC " + _
                              "FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE " + _
                              "ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                              "WHERE CodeCategorie =9 AND CodeTypePreparation =1 " + _
                              " and Designation LIKE " + Quote(gArticles.Columns("Designation").Value + "%") + _
                              " AND Supprime=0 ORDER BY Designation"
                End If
            ElseIf gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" Then
                StrSQL1 = "SELECT CodeArticle," + _
                          "Designation," + _
                          "LibelleForme," + _
                          "PrixVenteTTC " + _
                          "FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE " + _
                          "ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                          "WHERE CodeCategorie =9 AND CodeTypePreparation =1 " + _
                          "AND CodeABarre LIKE " + Quote(gArticles.Columns("CodeArticle").Value) + _
                          " AND Supprime=0 ORDER BY Designation"

            End If
            cmdProduction.Connection = ConnectionServeur
            cmdProduction.CommandText = StrSQL1
            daProduction = New SqlDataAdapter(cmdProduction)
            daProduction.Fill(dsProduction, "ARTICLE")

            If dsProduction.Tables("ARTICLE").Rows.Count > 0 Then
                dr = dsProduction.Tables("ARTICLE").Rows(0)
            End If

            With gListeRecherche
                .Columns.Clear()
                .DataSource = dsProduction
                .DataMember = "ARTICLE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code Article"
                .Columns("Designation").Caption = "Designation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("PrixVenteTTC").Caption = "Prix de vente"

                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' CentreR tous les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Visible = False
                Next

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("CodeArticle").Visible = True
                .Splits(0).DisplayColumns("Designation").Visible = True
                .Splits(0).DisplayColumns("LibelleForme").Visible = True
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("Designation").Width = 300
                .Splits(0).DisplayColumns("LibelleForme").Width = 100
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120


                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.Aqua
                .Splits(0).DisplayColumns("LibelleForme").Style.BackColor = Color.Aqua
                .Splits(0).DisplayColumns("PrixVenteTTC").Style.BackColor = Color.Aqua

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 20
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
            End With
            Dim Col As New C1.Win.C1TrueDBGrid.C1DataColumn
            Dim dc As C1.Win.C1TrueDBGrid.C1DisplayColumn

            With gListeRecherche
                .Columns.Insert(0, Col)
                Col.Caption = "Stock"
                dc = .Splits(0).DisplayColumns.Item("Stock")
                dc.Width = 40
                .Splits(0).DisplayColumns(4).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                .Splits(0).DisplayColumns(4).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.Aqua
                dc.Visible = True
                .Rebind(True)
            End With


        End If
    End Sub

    Public Function RecupereNumero()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max([NumeroProduction]) FROM [PRODUCTION]"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If
        Return ValeurRetour
    End Function

    Private Sub gArticles_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyUp
        Dim i As Integer = 0
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim QuantiteAAjouter As Integer = 0

        '---------------------------------- test si on est en mode saisi ou non ---------------------------
        If mode <> "Ajout" And mode <> "Modif" Then
            Exit Sub
        End If


        '---------------------------------- suppression du dernier ligne vide si on a deux lignes au mm temps vide
        '---------------------------------- cas ou on supprime dernier ligne
        If dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1 = 1 And gArticles(0, "CodeArticle") = "" And gArticles(1, "CodeArticle") = "" Then
            gArticles.MoveLast()
            gArticles.MovePrevious()
            gArticles.Delete()
        End If

        If gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" And e.KeyCode = Keys.Enter Then

            If gArticles.Columns("CodeABarre").Value <> "" Then

                Dim NbreJourValidite As Integer = 0
                Dim CodeABarre As String
                Dim CodeArticle As String = ""

                CodeABarre = gArticles.Columns("CodeABarre").Value

                StrSQL = " select CodeArticle from ARTICLE where CodeABarre='" + CodeABarre + "'"
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL
                Try
                    CodeArticle = cmd.ExecuteScalar
                Catch ex As Exception
                    WriteLine(ex.Message)
                End Try


                StrSQL = " select NombreJourValidite from FORMULE_PREPARATION where CodePreparation='" + CodeArticle + "'"
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL
                Try
                    NbreJourValidite = cmd.ExecuteScalar
                Catch ex As Exception
                    'WriteLine(ex.Message)
                    NbreJourValidite = 0
                End Try

                If NbreJourValidite = 0 Then

                    StrSQL = "SELECT TOP(1) NbreJourValiditeParDefaut FROM PARAMETRE_PHARMACIE "
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL
                    Try
                        NbreJourValidite = cmd.ExecuteScalar
                    Catch ex As Exception
                        'WriteLine(ex.Message)
                        NbreJourValidite = 0
                    End Try

                End If

                If CodeArticle = "" Then
                    Exit Sub
                End If

                'If NbreJourValidite = 0 Then
                '    Exit Sub
                'End If

                gArticles.Columns("DatePeremption").Value = System.DateTime.Now.AddDays(NbreJourValidite)

            End If
        End If

        If gArticles.Columns(gArticles.Col).DataField() = "Designation" And e.KeyCode = Keys.Enter Then

            If gArticles.Columns("Designation").Value <> "" Then

                Dim NbreJourValidite As Integer = 0
                Dim CodeABarre As String
                Dim CodeArticle As String = ""

                CodeABarre = gArticles.Columns("CodeABarre").Value

                StrSQL = " select CodeArticle from ARTICLE where CodeABarre='" + CodeABarre + "'"
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL
                Try
                    CodeArticle = cmd.ExecuteScalar
                Catch ex As Exception
                    WriteLine(ex.Message)
                End Try

                StrSQL = " select NombreJourValidite from FORMULE_PREPARATION where CodePreparation='" + CodeArticle + "'"
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL
                Try
                    NbreJourValidite = cmd.ExecuteScalar

                Catch ex As Exception
                    'WriteLine(ex.Message)
                    NbreJourValidite = 0
                End Try

                If NbreJourValidite = 0 Then

                    StrSQL = "SELECT TOP(1) NbreJourValiditeParDefaut FROM PARAMETRE_PHARMACIE "
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL
                    Try
                        NbreJourValidite = cmd.ExecuteScalar
                    Catch ex As Exception
                        'WriteLine(ex.Message)
                        NbreJourValidite = 0
                    End Try

                End If

                gArticles.Columns("DatePeremption").Value = System.DateTime.Now.AddDays(NbreJourValidite)

            End If
        End If

        '---------------------------------- test du type de la valeur d'entrée dans la colonne quantité (numéric) ------------
        '---------------------------------- test du  valeur d'entrée dans la colonne quantité < 99999 ------------

        If gArticles.Columns(gArticles.Col).DataField() = "Qte" Then
            If gArticles.Columns("Qte").Value.ToString <> "-" Then   ' pour les quantité négatives
                If IsNumeric(gArticles.Columns("Qte").Value) = False Then
                    gArticles.Columns("Qte").Value = ""
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                    gArticles.EditActive = True
                    Exit Sub
                End If
                If (gArticles.Columns("Qte").Value = 0) And e.KeyCode = Keys.Enter Then
                    gArticles.Columns("Qte").Value = ""
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                    gArticles.EditActive = True
                    Exit Sub
                End If
                If (gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999) And e.KeyCode = Keys.Enter Then
                    gArticles.Columns("Qte").Value = "1"
                    MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                    gArticles.EditActive = True
                    Exit Sub
                End If
            End If

        End If

        '--------------- test de l'existance du mm article avec la mm date au dessus dans la 
        '--------------- liste (cas ou on a une date non null)

        If (gArticles.Columns(gArticles.Col).DataField() = "Qte") And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            Dim CodeNewArticle As String = ""
            Dim DateNewArticle As Date
            Dim NumeroLotNewArticle As String = ""
            Dim QteNewArticle As Integer = 0
            Dim DateOldArticle As Date

            CodeNewArticle = gArticles.Columns("CodeArticle").Value.ToString
            DateNewArticle = gArticles.Columns("DatePeremption").Value
            DateNewArticle = FormatDateTime(DateNewArticle, DateFormat.ShortDate)

            QteNewArticle = gArticles.Columns("Qte").Value
            DateOldArticle = gArticles(i, "DatePeremption")

            DateOldArticle = FormatDateTime(DateOldArticle, DateFormat.ShortDate)

            i = 0
            Do While i < gArticles.RowCount - 1
                If IsDBNull(gArticles(i, "DatePeremption")) = False Then
                    DateOldArticle = gArticles(i, "DatePeremption")
                    DateOldArticle = FormatDateTime(DateOldArticle, DateFormat.ShortDate)
                    If gArticles(i, "CodeArticle") = CodeNewArticle And DateOldArticle = DateNewArticle And i <> gArticles.Row Then
                        gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QteNewArticle).ToString
                        gArticles.MoveLast()
                        gArticles.Delete()
                        If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                            NouvelArticle = dsProduction.Tables("PRODUCTION_DETAILS").NewRow()
                            NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                            NouvelArticle("CodeArticle") = ""
                            NouvelArticle("CodeABarre") = ""
                            dsProduction.Tables("PRODUCTION_DETAILS").Rows.Add(NouvelArticle)
                        End If
                    End If
                End If
                i = i + 1
            Loop
        End If

        '--------------- test de l'existance du mm article sans date aussi au dessus dans la 
        '--------------- liste (cas ou on a une date null)

        If gArticles.Columns(gArticles.Col).DataField() = "Qte" And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = True Then
            Dim CodeNewArticle As String = ""
            Dim QteNewArticle As Integer = 0

            CodeNewArticle = gArticles.Columns("CodeArticle").Value
            QteNewArticle = gArticles.Columns("Qte").Value

            i = 0
            Do While i < gArticles.RowCount - 1
                If IsDBNull(gArticles(i, "DatePeremption")) = True Then
                    If gArticles(i, "CodeArticle") = CodeNewArticle And i <> gArticles.Row Then
                        gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QteNewArticle).ToString
                        gArticles.MoveLast()
                        gArticles.Delete()
                        If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                            NouvelArticle = dsProduction.Tables("PRODUCTION_DETAILS").NewRow()
                            NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                            NouvelArticle("CodeArticle") = ""
                            NouvelArticle("CodeABarre") = ""
                            dsProduction.Tables("PRODUCTION_DETAILS").Rows.Add(NouvelArticle)

                        End If
                    End If
                End If
                i = i + 1
            Loop
        End If

        '------------------------------ recherche par code ----------------------------------------------
        If gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" And e.KeyCode = Keys.Enter And gArticles.Row = dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1 Then
            ChargerDetailArticle(gArticles.Columns("CodeABarre").Value)
            Exit Sub
        ElseIf gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" And e.KeyCode = Keys.Enter And gArticles.Row < dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1 Then
            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
        End If
        '---------------------------------- masquer la liste de recherche si la designation est vide -----------
        If e.KeyCode = Keys.Back And gArticles.Columns("Designation").Value = "" And gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
            gListeRecherche.Visible = False
        End If
        '---------------------------- pour passer à la navigation dans la petite liste de recherhce ----------------
        If (e.KeyCode = Keys.Down Or e.KeyCode = Keys.Up) And gListeRecherche.Visible = True Then
            gListeRecherche.Focus()
            gListeRecherche.Col = 2
            gListeRecherche.Row = 1
        End If
        '---------------------------- si l'artcile n'existe pas dans la liste de recherhce sa quantité doit etre 0  ----
        If dsProduction.Tables("ARTICLE").Rows.Count <= 0 And gListeRecherche.Visible = True Then '
            gArticles.Columns("Qte").Value = 0
            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
        End If
        '---------------------------------- verouillage des lignes déja confirmées -------------------------
        If (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) And gArticles.Row <> dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1 Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        ElseIf gArticles.Row = dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1 Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
            gArticles.Splits(0).DisplayColumns("Designation").Locked = False
        End If

        '------------------------------ suppression d'une date de péremption
        If e.KeyCode = Keys.Delete And gArticles.Row <= dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1 And gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then
            gArticles.EditActive = False
            gArticles.Columns("DatePeremption").Value = ""
        End If

        '--------------------- traitement du clique sur le bouton ENTREE selon la colonne ---------------------------
        If e.KeyCode = Keys.Enter Then ' And (dsEmprunt.Tables("ARTICLE").Rows.Count > 0 Or gArticles.Row <> dsEmprunt.Tables("EMPRUNT_DETAILS").Rows.Count - 1) Then
            gListeRecherche_KeyUp(sender, e)     ' pour charger les informations de l'article a partir du fiche article

            If gArticles.Columns(gArticles.Col).DataField() = "Designation" Then
                If gArticles.Columns("CodeArticle").Value = "" Then    ' si l'article choisit n'existe pas dans la liste de recherche
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
                    gArticles.Columns("Designation").Value = ""
                Else
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
                End If

            ElseIf gArticles.Columns(gArticles.Col).DataField() = "CodeABarre" Then
                If gArticles.Columns("CodeABarre").Value <> "" Then
                    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))
                End If

            ElseIf gArticles.Columns(gArticles.Col).DataField() = "DatePeremption" Then
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Qte"))

            ElseIf gArticles.Columns(gArticles.Col).DataField() = "Qte" Then   ' si on est dans la colonne date de péremption on passe au nouveau ligne



                If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                    NouvelArticle = dsProduction.Tables("PRODUCTION_DETAILS").NewRow()
                    NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                    NouvelArticle("CodeArticle") = ""
                    NouvelArticle("CodeABarre") = ""
                    dsProduction.Tables("PRODUCTION_DETAILS").Rows.Add(NouvelArticle)
                End If

                gArticles.MoveLast()

                dsProduction.Tables("ARTICLE").Clear()

                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))

            End If

        End If

        If e.KeyCode = Keys.F1 Then

            Dim InstancePreparation As New fPreparation
            fPreparation.CodePreparation = gArticles.Columns("CodeArticle").Value
            fPreparation.CodeABarre = gArticles.Columns("CodeABarre").Value
            fPreparation.TypePreparation = 1
            fPreparation.DesignationPreparation = gArticles.Columns("Designation").Value
            InstancePreparation.mode = "M"

            InstancePreparation.init()
            InstancePreparation.ShowDialog()

            InstancePreparation.Dispose()
            InstancePreparation.Close()

        End If


    End Sub


    Private Sub gListeRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListeRecherche.KeyUp
        ' dans cet évènenment on teste si l évènenment est due à un clique sur le boutton entrée et si l'utilisateur a choisi 
        ' un article et si le curseur est dans la colonne de designation 

        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date

        Dim NbreJourValidite As Integer = 0
        Dim CodeABarre As String = ""
        Dim CodeArticle As String = ""

        If gListeRecherche.Visible = False Then
            Exit Sub
        End If
        If e.KeyCode = Keys.Back Then
            gArticles.Focus()
            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
            gArticles.MoveLast()
            gArticles.EditActive = True
        End If

        Dim j As Integer
        Dim NumeroLigne As Integer
        Dim DataRowRecherche As DataRow
        If e.KeyCode = Keys.Enter And (gArticles.Columns(gArticles.Col).DataField() = "LibelleForme" Or gArticles.Columns(gArticles.Col).DataField() = "Designation") Then    'And gArticles.Columns("Designation").Value <> ""
            If dsProduction.Tables("ARTICLE").Rows.Count > 0 Then
                '--------------------- rehcerche de l'index de la ligne qui contient l'article recherché ---------------
                For j = 0 To dsProduction.Tables("ARTICLE").Rows.Count - 1
                    DataRowRecherche = dsProduction.Tables("ARTICLE").Rows(j)
                    If DataRowRecherche.Item("CodeArticle") = gListeRecherche.Columns("CodeArticle").Value Then
                        NumeroLigne = j
                    End If
                Next

                '------------------- chargement des données ---------------------------------------------- 
                dr = dsProduction.Tables("ARTICLE").Rows(NumeroLigne)
                NouvelArticle("NumeroProduction") = RecupereNumero()
                NouvelArticle("CodeArticle") = dr.Item("CodeArticle")
                NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                Try
                    NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                Catch ex As Exception
                End Try

                NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
                NouvelArticle("Qte") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))

                '----------------------- récupération de la date de péremption

                CodeArticle = dr.Item("CodeArticle")

                StrSQL = " select NombreJourValidite from FORMULE_PREPARATION where CodePreparation='" + CodeArticle + "'"
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL
                Try
                    NbreJourValidite = cmd.ExecuteScalar
                Catch ex As Exception

                End Try
                If NbreJourValidite = 0 Then

                    StrSQL = "SELECT TOP(1) NbreJourValiditeParDefaut FROM PARAMETRE_PHARMACIE "
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL
                    Try
                        NbreJourValidite = cmd.ExecuteScalar
                    Catch ex As Exception
                        'WriteLine(ex.Message)
                        NbreJourValidite = 0
                    End Try

                End If
                If DatePeremption = #12:00:00 AM# Then
                    ' NouvelArticle("DatePeremption") = "01/01/1900"  
                Else
                    NouvelArticle("DatePeremption") = System.DateTime.Now.AddDays(NbreJourValidite)
                End If

                gArticles.Refresh()
            End If
            gListeRecherche.Visible = False
            gArticles.Focus()
            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
            gArticles.Columns("DatePeremption").Value = System.DateTime.Now.AddDays(NbreJourValidite)
        End If

    End Sub

    Public Sub ChargerDetailArticle(ByVal CodeABarre As String)
        Dim resultat As String
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date
        Dim NumeroLot As String = ""
        Dim CodeCategorie As String = ""
        Dim CodeTypePreparation As String = ""
        Dim CodeArticle As String = ""

        CodeArticle = RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "CodeABarre", CodeABarre)
        resultat = RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle)
        CodeCategorie = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", CodeArticle).ToString
        CodeTypePreparation = RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", CodeArticle)
        If resultat <> "" And CodeCategorie = "9" And CodeTypePreparation = "1" Then

            NouvelArticle("NumeroProduction") = RecupereNumero()
            NouvelArticle("CodeArticle") = CodeArticle
            NouvelArticle("CodeABarre") = CodeABarre
            NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)
            Try
                NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", CodeArticle)
            Catch ex As Exception
            End Try
            NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
            NouvelArticle("Qte") = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", CodeArticle)


            '----------------------- récupération de la date de péremption
            StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] " + _
                     "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                     "' AND CodeArticle='" + CodeABarre + _
                     "' Order by DatePeremptionArticle ASC "

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                DatePeremption = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If DatePeremption = #12:00:00 AM# Then
                'NouvelArticle("DatePeremption") = "01/01/1900"
            Else
                NouvelArticle("DatePeremption") = DatePeremption
            End If

            StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
                    "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                    "' AND CodeArticle='" + CodeArticle + _
                    "' Order by DatePeremptionArticle ASC "

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                NumeroLot = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If NumeroLot = "" Then
            Else
                NouvelArticle("NumeroLotArticle") = NumeroLot
            End If


            gArticles.Refresh()
            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("DatePeremption"))
        Else
            gArticles.Columns("CodeABarre").Value = ""
            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("Designation"))
        End If
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click

        '--------changer le mode en concultation
        mode = "Consultation"

        '----------Refreche liste Production
        ChargerProduction(NumeroligneProduction)

        '--------Pour initialiser les btns suivant le mode et l'etat du table dans la BD
        initBoutons()

        '-------pour rendre invisible la grid de recherche
        gListeRecherche.Visible = False
        bComposonts.Enabled = True

    End Sub

    Private Sub bLast_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLast.Click

        'Changer le mode en Mode Consultation
        mode = "Consultation"

        'Appel Pour selectionner la derniere ligne 
        NumeroligneProduction = selectionDernierLigneProduction()

        'Appel pour charger les information de  en question
        ChargerProduction(NumeroligneProduction)

    End Sub

    Private Sub bFirst_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFirst.Click
        'Changer le mode en Mode Consultation
        mode = "Consultation"

        'Appel Pour selectionner le dernier ligne 
        selectionPremierLigneProduction()

        'Appel pour charger les information de le Production en question
        ChargerProduction(NumeroligneProduction)

    End Sub


    Private Sub bNext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bNext.Click

        'Changer le mode en Mode Consultation
        mode = "Consultation"

        'Appel Pour selectionner l'element suivant 
        selectionLigneProductionSuivante()

        'Appel pour charger les information de le Production en question
        ChargerProduction(NumeroligneProduction)

    End Sub

    Private Sub bPrevious_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPrevious.Click
        'Changer le mode en Mode Consultation
        mode = "Consultation"

        'Appel Pour selectionner l'element precedent 
        selectionLigneProductionPrecedent()

        'Appel pour charger les information de le Production en question
        ChargerProduction(NumeroligneProduction)

    End Sub


    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click

        supprimerProduction(False)

    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        Dim I As Integer = 0
        Dim J As Integer = 0
        Dim cmd As New SqlCommand
        Dim NumeroLot As String = "RIEN"
        Dim TestNumeroLot As String = ""
        Dim NouveauNumeroLot As String = ""
        Dim QuantiteLotSansNumero As Integer = 0
        Dim QuantiteLotAInsere As Integer = 0

        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        Dim CodeArticle As String = ""
        Dim CodeArticleComposant As String = ""
        Dim NumeroLotArticleComposant As String = ""
        Dim DatePeremptionComposant As Date
        Dim QteComposant As Double
        Dim NumeroLotIncrementer As String = ""
        Dim PrixTTCPourVerifier As Double = 0.0
        Dim Qte, qteUnitaire As Integer

        Dim nbreLotArticle As Integer = 0
        Dim QteLotArticle As Integer = 0
        Dim k As Integer = 0
        Dim resteQte As Integer = 0
        Dim QteParLot As Integer = 0
        Dim PemiereLigne As Boolean = True
        Dim QteDemande As Integer = 0


        'Mode Modif
        If mode = "Modif" Then

            If gArticles.RowCount - 1 = 0 And gArticles(0, "CodeArticle") = "" Then

                If MsgBox("La liste des détails est vide, Voulez-vous supprimer le Production N° : " + lNumeroProduction.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Enregistrer") = MsgBoxResult.Yes Then

                    'Pour appeler le Bouton Annuler
                    bAnnuler.PerformClick()

                    'Pour appeler le bouton de Suppression
                    supprimerProduction(True)

                    Exit Sub

                Else

                    'Pour appeler le Bouton Annuler
                    bAnnuler.PerformClick()

                    'Quitter la procedure apres faire annuler
                    Exit Sub

                End If
            End If

        Else
            'Mode Ajout
            If dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle") = "" Then
                MsgBox("Entree Vide !", MsgBoxStyle.Critical, "Erreur")
                If dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1 < 0 Then
                    bAjouter_Click(sender, e)
                End If
                '----------------------
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
                gArticles.EditActive = True
                Exit Sub

            End If

        End If


        If mode = "Ajout" Then
            NumeroProduction = RecupereNumero()
        End If


        If dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle") = "" Then
            MsgBox("Entree Vide !", MsgBoxStyle.Critical, "Erreur")
            If dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1 < 0 Then
                bAjouter_Click(sender, e)
            End If
            '----------------------
            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
            gArticles.EditActive = True
            Exit Sub
        End If

        '----------------------- contrôle Quantité 

        For J = 0 To dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1
            If dsProduction.Tables("PRODUCTION_DETAILS").Rows(J).RowState <> DataRowState.Deleted Then
                If dsProduction.Tables("PRODUCTION_DETAILS").Rows(J).Item("Qte").ToString <> "" Then
                    If dsProduction.Tables("PRODUCTION_DETAILS").Rows(J).Item("Qte") < 0 Then
                        MsgBox("l'Article " + dsProduction.Tables("PRODUCTION_DETAILS").Rows(J).Item("Designation") + " admet une quantité égative  ! veuillez corriger sa valeur", MsgBoxStyle.Critical, "Erreur")
                        Exit Sub
                    End If
                    If dsProduction.Tables("PRODUCTION_DETAILS").Rows(J).Item("Qte") = 0 Then
                        MsgBox("l'Article " + dsProduction.Tables("PRODUCTION_DETAILS").Rows(J).Item("Designation") + " admet une quantité  égale à zéro  ! veuillez corriger sa valeur", MsgBoxStyle.Critical, "Erreur")
                        Exit Sub
                    End If
                End If
            End If
        Next

        For J = 0 To dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1
            If dsProduction.Tables("PRODUCTION_DETAILS").Rows(J).RowState <> DataRowState.Deleted Then
                If dsProduction.Tables("PRODUCTION_DETAILS").Rows(J).RowState <> DataRowState.Added Then
                    If IsNumeric(dsProduction.Tables("PRODUCTION_DETAILS").Rows(J).Item("Qte")) = False Then
                        MsgBox("l'Article " + dsProduction.Tables("PRODUCTION_DETAILS").Rows(J).Item("Designation") + ": la quantité doit être un nombre entier sans virgule ! veuillez corriger sa valeur", MsgBoxStyle.Critical, "Erreur")
                        Exit Sub
                    End If
                End If
            End If
        Next

        '------------------------------ demande du mot de passe
        Dim myMotDePasse As New fMotDePasse
        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If

        '-------------------------- élémination des lignes vides 

        I = 0
        Do While I < dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count()
            If dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then
                If dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                    dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Delete()
                End If
            End If
            I = I + 1
        Loop

        '----------------------- contrôle des dates de péremption si il y a un qui est périmé
        Dim p As Integer = 0
        For p = 0 To dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1
            If dsProduction.Tables("PRODUCTION_DETAILS").Rows(p).RowState <> DataRowState.Deleted Then
                If dsProduction.Tables("PRODUCTION_DETAILS").Rows(p).Item("DatePeremption").ToString <> "" Then
                    If dsProduction.Tables("PRODUCTION_DETAILS").Rows(p).Item("DatePeremption") < Date.Today Then
                        MsgBox("l'article " + dsProduction.Tables("PRODUCTION_DETAILS").Rows(p).Item("Designation") + " admet une date de péremption deja dépassé ! veuillez corriger sa date de péremption", MsgBoxStyle.Critical, "Erreur")
                        Exit Sub
                    End If
                End If
            End If
        Next

        '------------------------------ enregistrement l'entête de la Production -------------------------
        If mode = "Ajout" Then

            dsProduction.Tables("PRODUCTION").Clear()
            Dim StrSQL As String = ""
            StrSQL = "SELECT top(0) * FROM PRODUCTION ORDER BY NumeroProduction ASC"
            cmdProduction.Connection = ConnectionServeur
            cmdProduction.CommandText = StrSQL
            daProduction = New SqlDataAdapter(cmdProduction)
            daProduction.Fill(dsProduction, "PRODUCTION")
            cbProduction = New SqlCommandBuilder(daProduction)
            dr = dsProduction.Tables("PRODUCTION").NewRow()

            With dsProduction
                dr.Item("NumeroProduction") = NumeroProduction
                dr.Item("Date") = System.DateTime.Now
                dr.Item("Remarque") = tRemarque.Text
                dr.Item("CodePersonnel") = CodeOperateur

                dsProduction.Tables("PRODUCTION").Rows.Add(dr)
            End With
            Try
                daProduction.Update(dsProduction, "PRODUCTION")
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                dsProduction.Reset()
            End Try
            'mode = modif
        Else

            With dsProduction

                .Tables("PRODUCTION").Rows(0)("NumeroProduction") = lNumeroProduction.Text
                .Tables("PRODUCTION").Rows(0)("Date") = lDateProduction.Text
                .Tables("PRODUCTION").Rows(0)("Remarque") = tRemarque.Text
                .Tables("PRODUCTION").Rows(0)("CodePersonnel") = CodeOperateur

            End With
            Try
                daProductionEntete.Update(dsProduction, "PRODUCTION")
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            End Try

        End If

        '------------------------------ enregistrement le détails de la Production -------------------------

        I = 0
        Do While I < dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count
            If dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).RowState <> DataRowState.Deleted Then
                If dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                    dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Delete()
                Else
                    If mode = "Modif" Then

                        dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("NumeroProduction") = lNumeroProduction.Text

                        If dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("DatePeremption").ToString = "" Then

                            dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("NumeroLotArticle") = ""

                        Else

                            If dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).RowState = DataRowState.Added And dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("DatePeremption").ToString <> "" Then

                                dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("NumeroLotArticle") = IncrementerNumeroLotArticle(dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("CodeArticle")) 'fonction d'incrémentation du numéro de lot 

                            End If

                            If dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).RowState = DataRowState.Modified And dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("DatePeremption").ToString <> "" Then

                                dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("NumeroLotArticle") = IncrementerNumeroLotArticle(dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("CodeArticle")) 'fonction d'incrémentation du numéro de lot 

                            End If
                        End If

                    Else 'mode Ajout

                        dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("NumeroProduction") = NumeroProduction

                        If dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("DatePeremption").ToString = "" Then

                            dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("NumeroLotArticle") = ""
                        Else
                            dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("NumeroLotArticle") = IncrementerNumeroLotArticle(dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("CodeArticle")) 'fonction d'incrémentation du numéro de lot 

                        End If
                    End If
                End If
            End If
            I = I + 1
        Loop


        cmdProduction.Connection = ConnectionServeur
        cmdProduction.CommandText = "Select top(0) * FROM PRODUCTION_DETAILS"
        daProduction = New SqlDataAdapter(cmdProduction)
        daProduction.Fill(dsProduction, "PRODUCTION_DETAILS")
        cbProduction = New SqlCommandBuilder(daProduction)

        Try
            daProduction.Update(dsProduction, "PRODUCTION_DETAILS")
        Catch ex As Exception
            'Gérer l'Exception
            MsgBox(ex.Message)
            dsProduction.Reset()
        End Try
        '-----------------------------------------------------------------------------------------------------------------------
        '-----------------------------------------------------------------------------------------------------------------------
        '----------------insertion dans la table Poduction détails formule
        If mode = "Ajout" Then
            I = 0

            '------------pour parcourir les article dans la table production détails
            Do While I < dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count

                CodeArticle = RecupererValeurPreparationDetails("CodeArticle", " PRODUCTION_DETAILS", "NumeroProduction", dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("NumeroProduction"), "CodeArticle", dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("CodeArticle"))
                Qte = RecupererValeurPreparationDetails("Qte", " PRODUCTION_DETAILS", "NumeroProduction", dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("NumeroProduction"), "CodeArticle", dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("CodeArticle"))


                '''''''
                Try
                    qteUnitaire = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticle)
                Catch ex As Exception
                    qteUnitaire = 0
                End Try
                '''''''''

                '------------pour parcourir les composants de l'article dans la table formule préparation détails
                If dsProduction.Tables("FORMULE_PREPARATION_DETAILS") IsNot Nothing Then
                    dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Clear()
                End If

                cmdProduction.Connection = ConnectionServeur
                cmdProduction.CommandText = "Select  * FROM FORMULE_PREPARATION_DETAILS where CodePreparation='" + CodeArticle + "'"
                daProduction = New SqlDataAdapter(cmdProduction)
                daProduction.Fill(dsProduction, "FORMULE_PREPARATION_DETAILS")
                cbProduction = New SqlCommandBuilder(daProduction)

                For J = 0 To dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1

                    CodeArticleComposant = RecupererValeurPreparationDetails("CodeArticle", "FORMULE_PREPARATION_DETAILS", "CodeArticle", dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Rows(J).Item("CodeArticle"), "CodePreparation", CodeArticle)

                    QteComposant = RecupererValeurPreparationDetails("Qte", "FORMULE_PREPARATION_DETAILS", "CodeArticle", dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Rows(J).Item("CodeArticle"), "CodePreparation", CodeArticle)

                    'QteComposant = QteComposant * Qte

                    QteComposant = QteComposant * Qte / qteUnitaire

                    '------------------ gestion des numeros de lot
                    If dsProduction.Tables("LOT_ARTICLE") IsNot Nothing Then
                        dsProduction.Tables("LOT_ARTICLE").Clear()
                    End If

                    StrSQL = " SELECT  NumeroLotArticle, DatePeremptionArticle, QteLotArticle, CodeArticle , 1 as OrdreTri FROM LOT_ARTICLE " + _
                    " WHERE DatePeremptionArticle >'" + _
                    System.DateTime.Now.Date.ToString & "'" + _
                    " AND CodeArticle= " & Quote(CodeArticleComposant) & "" + _
                    " AND QteLotArticle > 0   " & "" + _
                    " UNION  SELECT  NumeroLotArticle, DatePeremptionArticle, QteLotArticle, CodeArticle, 0 as OrdreTri  FROM LOT_ARTICLE " + _
                    " WHERE DatePeremptionArticle is null " + _
                    " AND CodeArticle= " & Quote(CodeArticleComposant) & "" + _
                    " AND QteLotArticle > 0   " + _
                    " ORDER BY OrdreTri desc, DatePeremptionArticle "

                    cmdProduction.Connection = ConnectionServeur
                    cmdProduction.CommandText = StrSQL
                    daProduction = New SqlDataAdapter(cmdProduction)
                    daProduction.Fill(dsProduction, "LOT_ARTICLE")
                    cbProduction = New SqlCommandBuilder(daProduction)

                    For k = 0 To dsProduction.Tables("LOT_ARTICLE").Rows.Count - 1

                        QteParLot = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("QteLotArticle")

                        If (dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString = "" And
                                dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "") Then

                            If QteComposant <= QteParLot Then

                                NumeroLotArticleComposant = ""

                                DatePeremptionComposant = #12:00:00 AM#

                                StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                                StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                                StrSQL = StrSQL & " VALUES('"
                                StrSQL = StrSQL & NumeroProduction
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & CodeArticle
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & CodeArticleComposant
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & NumeroLotArticleComposant
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & DatePeremptionComposant
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & QteComposant
                                StrSQL = StrSQL & "')"

                                QteComposant = 0

                                cmd.Connection = ConnectionServeur
                                cmd.CommandText = StrSQL
                                Try
                                    cmd.ExecuteNonQuery()

                                Catch ex As Exception
                                    Console.WriteLine(ex.Message)
                                End Try

                            Else

                                NumeroLotArticleComposant = ""

                                DatePeremptionComposant = #12:00:00 AM#

                                StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                                StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                                StrSQL = StrSQL & " VALUES('"
                                StrSQL = StrSQL & NumeroProduction
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & CodeArticle
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & CodeArticleComposant
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & NumeroLotArticleComposant
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & DatePeremptionComposant
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & QteComposant
                                StrSQL = StrSQL & "')"

                                cmd.Connection = ConnectionServeur
                                cmd.CommandText = StrSQL
                                Try
                                    cmd.ExecuteNonQuery()

                                Catch ex As Exception
                                    Console.WriteLine(ex.Message)
                                End Try

                            End If

                            Exit For

                        End If

                        If QteComposant <= QteParLot Then


                            NumeroLotArticleComposant = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString

                            If dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "" Then
                                DatePeremptionComposant = #12:00:00 AM#

                            Else
                                DatePeremptionComposant = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString
                            End If

                            StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                            StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                            StrSQL = StrSQL & " VALUES('"
                            StrSQL = StrSQL & NumeroProduction
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & CodeArticle
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & CodeArticleComposant
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & NumeroLotArticleComposant
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & DatePeremptionComposant
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & QteComposant
                            StrSQL = StrSQL & "')"

                            QteComposant = 0

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try
                                cmd.ExecuteNonQuery()

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                            Exit For

                        Else

                            QteComposant = QteComposant - QteParLot

                            NumeroLotArticleComposant = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString

                            If dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "" Then
                                DatePeremptionComposant = #12:00:00 AM#

                            Else
                                DatePeremptionComposant = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString
                            End If

                            StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                            StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                            StrSQL = StrSQL & " VALUES('"
                            StrSQL = StrSQL & NumeroProduction
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & CodeArticle
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & CodeArticleComposant
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & NumeroLotArticleComposant
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & DatePeremptionComposant
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & QteParLot
                            StrSQL = StrSQL & "')"

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try
                                cmd.ExecuteNonQuery()

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                        End If

                    Next

                    If QteComposant > 0 Then

                        NumeroLotArticleComposant = ""

                        DatePeremptionComposant = #12:00:00 AM#


                        StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                        StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                        StrSQL = StrSQL & " VALUES('"
                        StrSQL = StrSQL & NumeroProduction
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & CodeArticle
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & CodeArticleComposant
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & NumeroLotArticleComposant
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & DatePeremptionComposant
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & QteComposant
                        StrSQL = StrSQL & "')"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()

                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try

                    End If

                Next
                I = I + 1
            Loop
        End If



        'test si on a dans le mode Modif

        If mode = "Modif" Then

            'delete  la table Production details formule
            cmdProduction.Connection = ConnectionServeur
            cmdProduction.CommandText = "DELETE FROM PRODUCTION_DETAILS_FORMULE WHERE NumeroProduction ='" + lNumeroProduction.Text + "'"
            cmdProduction.ExecuteNonQuery()

            I = 0

            '------------pour parcourir les article dans la table production détails
            Do While I < dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count

                CodeArticle = RecupererValeurPreparationDetails("CodeArticle", " PRODUCTION_DETAILS", "NumeroProduction", dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("NumeroProduction"), "CodeArticle", dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("CodeArticle"))
                Qte = RecupererValeurPreparationDetails("Qte", " PRODUCTION_DETAILS", "NumeroProduction", dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("NumeroProduction"), "CodeArticle", dsProduction.Tables("PRODUCTION_DETAILS").Rows(I).Item("CodeArticle"))

                '------------pour parcourir les composants de l'article dans la table formule préparation détails
                If dsProduction.Tables("FORMULE_PREPARATION_DETAILS") IsNot Nothing Then
                    dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Clear()
                End If

                cmdProduction.Connection = ConnectionServeur
                cmdProduction.CommandText = "Select  * FROM FORMULE_PREPARATION_DETAILS where CodePreparation='" + CodeArticle + "'"
                daProduction = New SqlDataAdapter(cmdProduction)
                daProduction.Fill(dsProduction, "FORMULE_PREPARATION_DETAILS")
                cbProduction = New SqlCommandBuilder(daProduction)

                For J = 0 To dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1

                    CodeArticleComposant = RecupererValeurPreparationDetails("CodeArticle", "FORMULE_PREPARATION_DETAILS", "CodeArticle", dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Rows(J).Item("CodeArticle"), "CodePreparation", CodeArticle)

                    QteComposant = RecupererValeurPreparationDetails("Qte", "FORMULE_PREPARATION_DETAILS", "CodeArticle", dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Rows(J).Item("CodeArticle"), "CodePreparation", CodeArticle)

                    QteComposant = QteComposant * Qte


                    '------------------ gestion des numeros de lot
                    If dsProduction.Tables("LOT_ARTICLE") IsNot Nothing Then
                        dsProduction.Tables("LOT_ARTICLE").Clear()
                    End If

                    StrSQL = " SELECT  NumeroLotArticle, DatePeremptionArticle, QteLotArticle, CodeArticle , 1 as OrdreTri FROM LOT_ARTICLE " + _
                    " WHERE DatePeremptionArticle >'" + _
                    System.DateTime.Now.Date.ToString & "'" + _
                    " AND CodeArticle= " & CodeArticleComposant & "" + _
                    " AND QteLotArticle > 0   " & "" + _
                    " UNION  SELECT  NumeroLotArticle, DatePeremptionArticle, QteLotArticle, CodeArticle, 0 as OrdreTri  FROM LOT_ARTICLE " + _
                    " WHERE DatePeremptionArticle is null " + _
                    " AND CodeArticle= " & CodeArticleComposant & "" + _
                    " AND QteLotArticle > 0   " + _
                    " ORDER BY OrdreTri desc, DatePeremptionArticle "

                    cmdProduction.Connection = ConnectionServeur
                    cmdProduction.CommandText = StrSQL
                    daProduction = New SqlDataAdapter(cmdProduction)
                    daProduction.Fill(dsProduction, "LOT_ARTICLE")
                    cbProduction = New SqlCommandBuilder(daProduction)

                    For k = 0 To dsProduction.Tables("LOT_ARTICLE").Rows.Count - 1

                        QteParLot = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("QteLotArticle")

                        If (dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString = "" And
                                dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "") Then

                            If QteComposant <= QteParLot Then

                                NumeroLotArticleComposant = ""

                                DatePeremptionComposant = #12:00:00 AM#

                                StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                                StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                                StrSQL = StrSQL & " VALUES('"
                                StrSQL = StrSQL & NumeroProduction
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & CodeArticle
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & CodeArticleComposant
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & NumeroLotArticleComposant
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & DatePeremptionComposant
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & QteComposant
                                StrSQL = StrSQL & "')"

                                QteComposant = 0

                                cmd.Connection = ConnectionServeur
                                cmd.CommandText = StrSQL
                                Try
                                    cmd.ExecuteNonQuery()

                                Catch ex As Exception
                                    Console.WriteLine(ex.Message)
                                End Try

                            Else

                                NumeroLotArticleComposant = ""

                                DatePeremptionComposant = #12:00:00 AM#

                                StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                                StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                                StrSQL = StrSQL & " VALUES('"
                                StrSQL = StrSQL & NumeroProduction
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & CodeArticle
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & CodeArticleComposant
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & NumeroLotArticleComposant
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & DatePeremptionComposant
                                StrSQL = StrSQL & "','"
                                StrSQL = StrSQL & QteComposant
                                StrSQL = StrSQL & "')"

                                cmd.Connection = ConnectionServeur
                                cmd.CommandText = StrSQL
                                Try
                                    cmd.ExecuteNonQuery()

                                Catch ex As Exception
                                    Console.WriteLine(ex.Message)
                                End Try

                            End If

                            Exit For

                        End If

                        If QteComposant <= QteParLot Then


                            NumeroLotArticleComposant = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString

                            If dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "" Then
                                DatePeremptionComposant = #12:00:00 AM#

                            Else
                                DatePeremptionComposant = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString
                            End If

                            StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                            StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                            StrSQL = StrSQL & " VALUES('"
                            StrSQL = StrSQL & NumeroProduction
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & CodeArticle
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & CodeArticleComposant
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & NumeroLotArticleComposant
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & DatePeremptionComposant
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & QteComposant
                            StrSQL = StrSQL & "')"

                            QteComposant = 0

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try
                                cmd.ExecuteNonQuery()

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                            Exit For

                        Else

                            QteComposant = QteComposant - QteParLot

                            NumeroLotArticleComposant = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString

                            If dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "" Then
                                DatePeremptionComposant = #12:00:00 AM#

                            Else
                                DatePeremptionComposant = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString
                            End If

                            StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                            StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                            StrSQL = StrSQL & " VALUES('"
                            StrSQL = StrSQL & NumeroProduction
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & CodeArticle
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & CodeArticleComposant
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & NumeroLotArticleComposant
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & DatePeremptionComposant
                            StrSQL = StrSQL & "','"
                            StrSQL = StrSQL & QteParLot
                            StrSQL = StrSQL & "')"

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try
                                cmd.ExecuteNonQuery()

                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                        End If

                    Next

                    If QteComposant > 0 Then

                        NumeroLotArticleComposant = ""

                        DatePeremptionComposant = #12:00:00 AM#


                        StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                        StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                        StrSQL = StrSQL & " VALUES('"
                        StrSQL = StrSQL & NumeroProduction
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & CodeArticle
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & CodeArticleComposant
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & NumeroLotArticleComposant
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & DatePeremptionComposant
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & QteComposant
                        StrSQL = StrSQL & "')"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()

                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try

                    End If

                Next
                I = I + 1
            Loop
        End If

        '-----------------comparaison des prix des composants juste avant la production
        For I = 0 To dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1
            If dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("CodeArticle").ToString <> "" Then
                PrixTTCPourVerifier = RecupererValeurExecuteScalaire("PrixVenteTTC", "ARTICLE", "CodeArticle", dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("CodeArticle"))
                If PrixTTCPourVerifier <> dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("PrixVenteTTC") Then
                    Dim InstanceVerifierPrix As New fVerifierPrixPreparations
                    InstanceVerifierPrix.NumeroPreparation = dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Rows(0)("CodePreparation")
                    InstanceVerifierPrix.ShowDialog()

                    InstanceVerifierPrix.Dispose()
                    InstanceVerifierPrix.Close()
                End If
            End If

        Next


        'si le mode Ajout
        If mode = "Ajout" Then

            'Appel Pour selectionner le dernier ligne 
            NumeroligneProduction = selectionDernierLigneProduction()

        End If

        'changer le mode en consultation
        mode = "Consultation"

        'Appel pour charger les information  en question
        ChargerProduction(NumeroligneProduction)

        'initialisation des btns
        initBoutons()

        gListeRecherche.Visible = False

        bComposonts.Enabled = True


    End Sub

    Public Function IncrementerNumeroLotArticle(CodeArticle As String)
        Dim cmd As New SqlCommand
        Dim NumeroLot As String = ""
        Dim NumeroLotIncrementer As String = ""

        Try
            StrSQL = "SELECT ISNULL((SELECT MAX(Convert(INT, NumeroLotArticle)) + 1 FROM LOT_ARTICLE WHERE NumeroLotArticle <> '' AND ISNUMERIC(NumeroLotArticle) = 1 AND CodeArticle =" & Quote(CodeArticle) & "), '1')"
            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            NumeroLotIncrementer = cmd.ExecuteScalar

        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
        Return NumeroLotIncrementer
    End Function

    'Public Function RecupereNumeroLotArticle()
    '    Dim StrSQL As String = ""
    '    Dim cmdRecupereNum As New SqlCommand
    '    Dim ValeurActuel As String = ""
    '    Dim Numero As Integer = 0
    '    Dim numeroConvertit As String = ""
    '    Dim ValeurRetour As String = ""
    '    Dim i As Integer

    '    StrSQL = " SELECT NumeroLotProduction FROM PARAMETRE_PHARMACIE"
    '    cmdRecupereNum.Connection = ConnectionServeur
    '    cmdRecupereNum.CommandText = StrSQL
    '    Try
    '        ValeurActuel = CInt(Val(cmdRecupereNum.ExecuteScalar()))
    '    Catch ex As Exception
    '        'Console.WriteLine(ex.Message)
    '    End Try

    '    If ValeurActuel = "" Then
    '        ValeurActuel = "000000"
    '    End If
    '    '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d'un nouveau compteur  ----
    '    Numero = CInt(Val(ValeurActuel))
    '    Numero = Numero + 1
    '    numeroConvertit = Numero.ToString
    '    For i = 0 To 5 - numeroConvertit.Length
    '        numeroConvertit = "0" + numeroConvertit
    '    Next
    '    ValeurRetour = numeroConvertit

    '    Return ValeurRetour
    'End Function

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub tRemarque_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRemarque.KeyUp
        If e.KeyCode = Keys.Enter Then
            gArticles.Focus()
            gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))
        End If

    End Sub
    Private Sub gListeRecherche_UnboundColumnFetch(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gListeRecherche.UnboundColumnFetch
        Dim y As String
        y = gListeRecherche(e.Row, ("CodeArticle"))
        e.Value = CalculeStock(y)
    End Sub

    'Pour charger les Entêtes des productions  
    Sub initProductionEntete()
        StrSQL = "SELECT * FROM PRODUCTION ORDER BY NumeroProduction ASC"
        cmdProduction.Connection = ConnectionServeur
        cmdProduction.CommandText = StrSQL
        daProduction = New SqlDataAdapter(cmdProduction)
        daProduction.Fill(dsProduction, "PRODUCTION")
        If dsProduction.Tables("PRODUCTION").Rows.Count > 0 Then
            NumeroProduction = dsProduction.Tables("PRODUCTION").Rows(dsProduction.Tables("PRODUCTION").Rows.Count - 1).Item("NumeroProduction")

            lNumeroProduction.Text = dsProduction.Tables("PRODUCTION").Rows(dsProduction.Tables("PRODUCTION").Rows.Count - 1)("NumeroProduction")
            lDateProduction.Text = dsProduction.Tables("PRODUCTION").Rows(dsProduction.Tables("PRODUCTION").Rows.Count - 1)("Date")
            lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsProduction.Tables("PRODUCTION").Rows(dsProduction.Tables("PRODUCTION").Rows.Count - 1)("CodePersonnel"))
            tRemarque.Value = dsProduction.Tables("PRODUCTION").Rows(dsProduction.Tables("PRODUCTION").Rows.Count - 1)("Remarque")
        End If
    End Sub

    'chargement des détails des productions 
    Sub initProductionDetails()
        StrSQL = "SELECT TOP(0)  NumeroProduction," + _
                 "CodeArticle," + _
                 "CodeABarre," + _
                 "Designation," + _
                 "PRODUCTION_DETAILS.CodeForme," + _
                 "LibelleForme," + _
                 "DatePeremption," + _
                  "NumeroLotArticle," + _
                 "Qte " + _
                 "FROM " + _
                 "PRODUCTION_DETAILS LEFT OUTER JOIN FORME_ARTICLE " + _
                 "ON PRODUCTION_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
                 "WHERE NumeroProduction =" + Quote(NumeroProduction) + ""

        cmdProductionDetail.Connection = ConnectionServeur
        cmdProductionDetail.CommandText = StrSQL
        daProductionDetails = New SqlDataAdapter(cmdProductionDetail)
        daProductionDetails.Fill(dsProduction, "PRODUCTION_DETAILS")
        cbProductionDetails = New SqlCommandBuilder(daProductionDetails)

    End Sub
    'pour initialiser gArticles
    Private Sub initgArticles()

        Dim I As Integer
        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsProduction
            Catch ex As Exception
            End Try
            .DataMember = "PRODUCTION_DETAILS"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qté"
            .Columns("DatePeremption").Caption = "Date péremption"
            .Columns("NumeroLotArticle").Caption = "Numéro Lot Article"


            'colonne vide
            .Columns("Vide").Caption = ""

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near


            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(0).DisplayColumns("NumeroProduction").Width = 0
            .Splits(0).DisplayColumns("NumeroProduction").Visible = False
            .Splits(0).DisplayColumns("NumeroProduction").AllowSizing = False
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 200
            .Splits(0).DisplayColumns("Designation").Width = 650
            .Splits(0).DisplayColumns("CodeForme").Width = 0
            .Splits(0).DisplayColumns("LibelleForme").Width = 120
            .Splits(0).DisplayColumns("Qte").Width = 70
            .Splits(0).DisplayColumns("DatePeremption").Width = 120

            'colonne vide ajouter
            .Splits(0).DisplayColumns("Vide").Width = 50
            .Splits(0).DisplayColumns("Vide").Visible = False

            .Splits(0).DisplayColumns("NumeroLotArticle").Width = 120

            'pour rendre invisible le colonne numero de  colonne dans le mode ajoue et modif
            'et visible dans le mode consultation

            If mode = "Ajout" Or mode = "Modif" Then
                .Splits(0).DisplayColumns("NumeroLotArticle").Visible = False
            End If

            If mode = "Consultation" Then
                .Splits(0).DisplayColumns("NumeroLotArticle").Visible = True
            End If


            .Splits(0).DisplayColumns("CodeForme").Visible = False

            'Couleur de la Grid
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.BackColor = Color.FromArgb(250, 250, 200)
            Next

            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(210, 240, 255)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(210, 240, 255)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gArticles)
        End With

        ParametreGrid(gListeRecherche)

    End Sub

    '--------------------initialisation de la datatable article qui est utilisé dans la liste de 
    '--------------------recherche alimenté selon les entrés de l'utilisateur dans la colonne designation
    Private Sub initArticle()
        Dim I As Integer

        StrSQL = "SELECT CodeArticle," + _
                 "Designation," + _
                 "LibelleForme," + _
                 "PrixVenteTTC" + _
                 " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                 "WHERE " + _
                 "Designation LIKE " + Quote(gArticles.Columns("Designation").Value) + " ORDER BY Designation"

        cmdProduction.Connection = ConnectionServeur
        cmdProduction.CommandText = StrSQL
        daProduction = New SqlDataAdapter(cmdProduction)
        daProduction.Fill(dsProduction, "ARTICLE")

        With gListeRecherche
            .Columns.Clear()
            .DataSource = dsProduction
            .DataMember = "ARTICLE"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code Article"
            .Columns("Designation").Caption = "Designation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("PrixVenteTTC").Caption = "Prix de vente"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centre tous les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Visible = False
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(0).DisplayColumns("CodeArticle").Visible = True
            .Splits(0).DisplayColumns("Designation").Visible = True
            .Splits(0).DisplayColumns("LibelleForme").Visible = True
            .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("Designation").Width = 300
            .Splits(0).DisplayColumns("LibelleForme").Width = 100
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 20
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
        End With

        cbProduction = New SqlCommandBuilder(daProduction)
    End Sub

    Private Sub initLoadControl()

        bConfirmer.Enabled = False
        'bAnnuler.Enabled = False

        bAjouter.Enabled = True

        bFirst.Enabled = True
        bPrevious.Enabled = True
        bNext.Enabled = True
        bLast.Enabled = True

        tRemarque.Enabled = False

        'bSupprimer.Enabled = False

    End Sub

    Private Sub ChargerProduction(ByVal pNumeroLigneProduction As String)

        '----------Vider la DS Production_DETAILS


        If dsProduction.Tables("PRODUCTION_DETAILS") IsNot Nothing Then
            dsProduction.Tables("PRODUCTION_DETAILS").Clear()
        End If

        '----------Vider la DS Production

        If dsProduction.Tables("PRODUCTION") IsNot Nothing Then
            dsProduction.Tables("PRODUCTION").Clear()
        End If

        '-----------------------------chargement des Entêtes des production

        StrSQL = " SELECT * FROM (  " + _
        " SELECT *, ROW_NUMBER() OVER (ORDER BY NumeroProduction) as row FROM PRODUCTION " + _
        "              ) a WHERE row > " & pNumeroLigneProduction - 1 & " AND  row <= " & pNumeroLigneProduction

        cmdProductionEntete.Connection = ConnectionServeur
        cmdProductionEntete.CommandText = StrSQL
        daProductionEntete = New SqlDataAdapter(cmdProductionEntete)
        daProductionEntete.Fill(dsProduction, "PRODUCTION")
        cbProductionEntete = New SqlCommandBuilder(daProductionEntete)


        'Lire le numéro Production

        If dsProduction.Tables("PRODUCTION").Rows.Count > 0 Then

            NumeroProduction = dsProduction.Tables("PRODUCTION").Rows(0).Item("NumeroProduction")

        Else

            NumeroProduction = "0"

        End If

        'Pour initialiser les btns suivant le mode et l'etat du table dans la BD
        initBoutons()


        'chargement des détails des productions

        '**************************************************************

        StrSQL = "SELECT  NumeroProduction," + _
            "CodeArticle," + _
            "CodeABarre," + _
            "Designation," + _
            "PRODUCTION_DETAILS.CodeForme," + _
            "LibelleForme," + _
            "NumeroLotArticle," + _
             "DatePeremption," + _
            "Qte, " + _
             "'' AS Vide " + _
            "FROM " + _
            "PRODUCTION_DETAILS LEFT OUTER JOIN FORME_ARTICLE " + _
            "ON PRODUCTION_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
            "WHERE NumeroProduction =" + Quote(NumeroProduction) + ""

        cmdProductionDetail.Connection = ConnectionServeur
        cmdProductionDetail.CommandText = StrSQL
        daProductionDetails = New SqlDataAdapter(cmdProductionDetail)
        daProductionDetails.Fill(dsProduction, "PRODUCTION_DETAILS")
        cbProductionDetails = New SqlCommandBuilder(daProductionDetails)

        'pour initialiser la grid
        initgArticles()


        ' Affichage ds informations de le production

        'Si le  mode est consultation

        If mode = "Modif" Or mode = "Consultation" Then

            If dsProduction.Tables("PRODUCTION").Rows.Count > 0 Then

                DataRowRecherche = dsProduction.Tables("PRODUCTION").Select("NumeroProduction=" + Quote(NumeroProduction))(0)

                'chargement des informations entête
                lNumeroProduction.Text = dsProduction.Tables("PRODUCTION").Rows(dsProduction.Tables("PRODUCTION").Rows.Count - 1)("NumeroProduction")
                lDateProduction.Text = dsProduction.Tables("PRODUCTION").Rows(dsProduction.Tables("PRODUCTION").Rows.Count - 1)("Date")
                lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsProduction.Tables("PRODUCTION").Rows(dsProduction.Tables("PRODUCTION").Rows.Count - 1)("CodePersonnel"))
                tRemarque.Value = dsProduction.Tables("PRODUCTION").Rows(dsProduction.Tables("PRODUCTION").Rows.Count - 1)("Remarque")
                NumeroProduction = DataRowRecherche.Item("NumeroProduction")

            End If

            tRemarque.Enabled = False

        End If

    End Sub

    'Initialiser les Controls utilisés lors de l'opération de le Production
    Private Sub initControlProduction()

        '---------------------------------Debloquer le saisie
        Dim I As Integer

        With gArticles

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("CodeABarre").Locked = False
            .Splits(0).DisplayColumns("Designation").Locked = False
            .Splits(0).DisplayColumns("CodeForme").Locked = False
            .Splits(0).DisplayColumns("NumeroLotArticle").Locked = False
            .Splits(0).DisplayColumns("DatePeremption").Locked = False
            .Splits(0).DisplayColumns("Qte").Locked = False

        End With

        lOperateur.Text = "-"

        lDateProduction.Text = System.DateTime.Now
        lNumeroProduction.Text = "-------------"

        'bAnnuler.Enabled = True
        bConfirmer.Enabled = True

        'bSupprimer.Enabled = True

        bFirst.Visible = False
        bPrevious.Visible = False
        bNext.Visible = False
        bLast.Visible = False
        bAjouter.Enabled = False

        bComposonts.Enabled = True ' False

        tRemarque.Enabled = True
        tRemarque.Text = ""

        'gArticles.Col = 1
        gArticles.Focus()

    End Sub
    '------------------------------------les procédure de navigation avec NumeroligneProduction---------------------------------------------------------

    Private Function selectionDernierLigneProduction()

        Dim StrSQL As String
        'Affécter le nombre de ligne au variable global  NumeroligneProduction
        StrSQL = " SELECT COUNT(*) FROM PRODUCTION "

        cmdProduction.Connection = ConnectionServeur
        cmdProduction.CommandText = StrSQL

        selectionDernierLigneProduction = cmdProduction.ExecuteScalar()

        Return selectionDernierLigneProduction


    End Function

    Private Sub selectionPremierLigneProduction()

        'Affécter le numéro 1 au variable global  NumeroligneProduction
        NumeroligneProduction = 1

    End Sub

    Private Sub selectionLigneProductionPrecedent()

        'décrémenter le numéro 1 au variable global  NumeroligneProduction
        NumeroligneProduction = NumeroligneProduction - 1

    End Sub

    Private Sub selectionLigneProductionSuivante()

        ' incrémenter le numéro 1 au variable global NumeroligneProduction  
        NumeroligneProduction = NumeroligneProduction + 1

    End Sub

    Private Sub initBoutons()

        'Tester si on atteint la premiere ligne
        'pour desactiver les BTN Précedent et Premier élément        
        If NumeroligneProduction = 1 Then

            bPrevious.Enabled = False
            bFirst.Enabled = False

        Else

            bPrevious.Enabled = True
            bFirst.Enabled = True

        End If

        'Tester si on atteint la derniere ligne
        'pour desactiver les BTN Siuvant et Dernier élément
        If NumeroligneProduction = selectionDernierLigneProduction() Then

            bNext.Enabled = False
            bLast.Enabled = False


        Else

            bNext.Enabled = True
            bLast.Enabled = True

        End If

        'Le cas ou la table est vide
        If NumeroligneProduction = 0 Then

            bNext.Enabled = False
            bLast.Enabled = False
            bPrevious.Enabled = False
            bFirst.Enabled = False

        End If

        'Tester si la table est vide
        'pour desactiver les BTN Siuvant et Dernier élément
        If selectionDernierLigneProduction() = 0 Then

            'Bloque navigation
            bNext.Enabled = False
            bLast.Enabled = False
            bNext.Enabled = False
            bLast.Enabled = False

            'Bouton de ctrl
            'bAnnuler.Enabled = False
            bConfirmer.Enabled = False
            bImprimer.Enabled = False
            bModifier.Enabled = False
            bRecherche.Enabled = False
            'bSupprimer.Enabled = False
            bQuitter.Enabled = True

        End If   ' le cas on a ajouté un element

        'le mode en Cosultation et on a des enregistrements
        If selectionDernierLigneProduction() <> 0 And mode = "Consultation" Then

            'Bouton de ctrl
            'bAnnuler.Enabled = False
            bConfirmer.Enabled = False
            bImprimer.Enabled = True
            bModifier.Enabled = True
            bRecherche.Enabled = True
            'bSupprimer.Enabled = False
            bAjouter.Enabled = True
            bQuitter.Enabled = True

            bFirst.Visible = True
            bPrevious.Visible = True
            bNext.Visible = True
            bLast.Visible = True



            'le mode est modif/Ajout et on a des enregistrements
        ElseIf selectionDernierLigneProduction() <> 0 And mode <> "Consultation" Then

            'bAnnuler.Enabled = True
            bConfirmer.Enabled = True
            bImprimer.Enabled = False
            bModifier.Enabled = False
            bAjouter.Enabled = False
            bRecherche.Enabled = False
            'bSupprimer.Enabled = False
            bQuitter.Enabled = True

            'pour rendre visible si le mode est Modif ou Ajout
            bLast.Visible = False
            bNext.Visible = False
            bPrevious.Visible = False
            bFirst.Visible = False




            'le mode en Cosultation et on  a pas des enregistrements
        ElseIf selectionDernierLigneProduction() = 0 And mode = "Consultation" Then

            bAjouter.Enabled = True
            'bAnnuler.Enabled = False
            bConfirmer.Enabled = False
            bQuitter.Enabled = True

            'pour rendre invisible si le mode est Consultation
            bFirst.Visible = True
            bPrevious.Visible = True
            bNext.Visible = True
            bLast.Visible = True


        End If
    End Sub

    Private Sub bRecherche_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRecherche.Click
        mode = "Consultation"
        tRecherche.Visible = True
        tRecherche.Text = System.DateTime.Now.Year.ToString + "/"
        tRecherche.Focus()
        tRecherche.Select(tRecherche.Text.Length, 0)
        'bAnnuler.Enabled = True
    End Sub

    Private Sub tRecherche_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRecherche.KeyUp

        If e.KeyCode = Keys.Enter And tRecherche.Text <> "" Then

            'Recuprére le Row de l'element sellectioné

            'Lors du press Enter, on va appler la procedure rechercheProduction

            rechercheProduction(tRecherche.Text)

        End If

    End Sub

    Private Sub rechercheProduction(ByVal pNumeroProduction As String)
        '----------------------------------Traitement

        If tRecherche.Text.Length < 11 Then
            tRecherche.Text = tRecherche.Text.Substring(0, 5) + tRecherche.Text.Substring(5, tRecherche.Text.Length - 5).PadLeft(6, "0")
        End If



        'Recuperer la valeur de la row
        recupererNumRowRechrche()


        If NumeroligneProduction <> 0 Then
            ChargerProduction(NumeroligneProduction)
        End If

        tRecherche.Value = ""
        tRecherche.Visible = False

    End Sub

    Private Sub recupererNumRowRechrche()

        '------------------------- affichage du nombre de Production en instance 
        StrSQL = " SELECT RowNumber " + _
                 " FROM (select ROW_NUMBER() OVER(ORDER BY NumeroProduction) " + _
                 " AS 'RowNumber' , NumeroProduction  from PRODUCTION) AS PRODUCTIONLISTE " + _
                 " where PRODUCTIONLISTE.NumeroProduction =  " & Quote(tRecherche.Text)

        cmdProduction.Connection = ConnectionServeur
        cmdProduction.CommandText = StrSQL

        NumeroligneProduction = cmdProduction.ExecuteScalar()

        If NumeroligneProduction = 0 Then
            MsgBox("Production inéxistant", MsgBoxStyle.Exclamation, "Recherche")
            NumeroligneProduction = selectionDernierLigneProduction()
        End If

        'Tester si on atteint la premiere ligne
        'pour desactiver les BTN Précedent et Premier élément        
        If NumeroligneProduction = 1 Or NumeroligneProduction = 0 Then

            bPrevious.Enabled = False
            bFirst.Enabled = False

        Else

            bPrevious.Enabled = True
            bFirst.Enabled = True

        End If

        'Tester si on atteint la derniere ligne
        'pour desactiver les BTN Siuvant et Dernier élément
        If NumeroligneProduction = selectionDernierLigneProduction() Then

            bNext.Enabled = False
            bLast.Enabled = False

        Else

            bNext.Enabled = True
            bLast.Enabled = True

        End If

    End Sub


    Private Sub tRecherche_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tRecherche.LostFocus

        tRecherche.Visible = False

    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click

        If lNumeroProduction.Text = "" Then
            Exit Sub
        End If

        Dim CondCrystal As String = ""
        CondCrystal = " 1=1 AND {Vue_EtatProduction.NumeroProduction} = '" & lNumeroProduction.Text & "' "

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Production" Then
                num = I
            End If
        Next
        CR.FileName = Application.StartupPath + "\EtatProduction.rpt"

        CR.SetParameterValue("pNumeroProduction", lNumeroProduction.Text)

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo

        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent

        CR.RecordSelectionFormula = CondCrystal
        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression Production"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If

    End Sub

    Private Sub bModifier_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifier.Click
        'mode en modification
        mode = "Modif"
        initgArticles()
        Dim I As Integer
        NouvelArticle = dsProduction.Tables("PRODUCTION_DETAILS").NewRow()
        NouvelArticle("Designation") = ""
        NouvelArticle("CodeArticle") = ""
        NouvelArticle("CodeABarre") = ""
        dsProduction.Tables("PRODUCTION_DETAILS").Rows.Add(NouvelArticle)



        'NouvelArticle = dsProduction.Tables("PRODUCTION").NewRow()
        'dsProduction.Tables("PRODUCTION").Rows.Add(NouvelArticle)

        For I = 0 To dsProduction.Tables("PRODUCTION_DETAILS").Columns.Count - 1
            Me.gArticles.Splits(0).DisplayColumns(I).AllowFocus = False
        Next


        With gArticles

            '.Splits(0).DisplayColumns("CodeArticle").Locked = False
            .Splits(0).DisplayColumns("Designation").Locked = False
            .Splits(0).DisplayColumns("Qte").Locked = False
            .Splits(0).DisplayColumns("DatePeremption").Locked = False
            .Splits(0).DisplayColumns("NumeroLotArticle").Locked = False
            .Splits(0).DisplayColumns("CodeABarre").Locked = False


            '.Splits(0).DisplayColumns("CodeArticle").AllowFocus = True
            .Splits(0).DisplayColumns("Designation").AllowFocus = True
            .Splits(0).DisplayColumns("Qte").AllowFocus = True
            .Splits(0).DisplayColumns("DatePeremption").AllowFocus = True
            .Splits(0).DisplayColumns("NumeroLotArticle").AllowFocus = True
            .Splits(0).DisplayColumns("CodeABarre").AllowFocus = True

        End With

        tRemarque.Enabled = True
        bComposonts.Enabled = True ' False

        'Pour initialiser les btns suivant le mode et l'etat du table dans la BD
        initBoutons()

    End Sub

    Private Sub supprimerProduction(ByVal msgShow As Boolean)
        Dim NumeroProduction As String
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""
        NumeroProduction = lNumeroProduction.Text

        'Si le mode est Ajout ou Modif
        If mode = "Ajout" Or mode = "Modif" Then

            'Si  la liste est vide, quitter la procedure
            If gArticles.RowCount = 0 Then
                Exit Sub
            End If


            If gArticles.RowCount > 0 Then

                'Test si la lign est NEW ADDED et elle est vide
                If gArticles(gArticles.Row, ("CodeArticle")) <> "" Then
                    gArticles.Delete()
                End If


                'If gArticles.RowCount <= 0 Then
                '    bAjouter.PerformClick()
                'End If
                If dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1 < 0 Then

                    'ajout d'un nouvel enregistrement vide dans les datatables convenables
                    NouvelArticle = dsProduction.Tables("PRODUCTION_DETAILS").NewRow()
                    NouvelArticle("Designation") = ""
                    NouvelArticle("CodeArticle") = ""
                    NouvelArticle("CodeABarre") = ""
                    dsProduction.Tables("PRODUCTION_DETAILS").Rows.Add(NouvelArticle)

                End If
                gArticles.Focus()
                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeArticle"))
                gArticles.EditActive = True


                gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeABarre"))

                Exit Sub

            End If


        Else 'mode  consultation
            If NumeroProduction = "" Then
                MsgBox("Aucun Production à supprimer !", MsgBoxStyle.Critical, "Information")
                Exit Sub
            Else

                If msgShow = False Then

                    If MsgBox("Voulez vous vraiment supprimer cet Production " + lNumeroProduction.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then

                        '-------- demande du mot de passe

                        Dim myMotDePasse As New fMotDePasse
                        myMotDePasse.ShowDialog()
                        ConfirmerEnregistrer = fMotDePasse.Confirmer
                        CodeOperateur = fMotDePasse.CodeOperateur
                        myMotDePasse.Dispose()
                        myMotDePasse.Close()
                        If ConfirmerEnregistrer = False Then
                            Exit Sub

                        End If

                        'delete de la table Production details formule
                        cmdProduction.Connection = ConnectionServeur
                        cmdProduction.CommandText = "DELETE FROM PRODUCTION_DETAILS_FORMULE WHERE NumeroProduction ='" + NumeroProduction + "'"
                        cmdProduction.ExecuteNonQuery()

                        'delete de la table Production details
                        cmdProduction.Connection = ConnectionServeur
                        cmdProduction.CommandText = "DELETE FROM PRODUCTION_DETAILS WHERE NumeroProduction ='" + NumeroProduction + "'"
                        cmdProduction.ExecuteNonQuery()

                        'delete de la table Production
                        cmdProduction.Connection = ConnectionServeur
                        cmdProduction.CommandText = "DELETE FROM PRODUCTION WHERE NumeroProduction ='" + NumeroProduction + "'"
                        cmdProduction.ExecuteNonQuery()

                        'Ma nouvelle position

                        If NumeroligneProduction > 1 Then
                            NumeroligneProduction = NumeroligneProduction - 1
                        ElseIf NumeroligneProduction = 1 Then
                            initLoadControl()

                        End If
                        'charger la nouvelle position
                        ChargerProduction(NumeroligneProduction)



                        MsgBox("Production supprimé !", MsgBoxStyle.Information, "Information")


                        Exit Sub

                    End If

                Else

                    '-------- demande du mot de passe

                    Dim myMotDePasse As New fMotDePasse
                    myMotDePasse.ShowDialog()
                    ConfirmerEnregistrer = fMotDePasse.Confirmer
                    CodeOperateur = fMotDePasse.CodeOperateur
                    myMotDePasse.Dispose()
                    myMotDePasse.Close()
                    If ConfirmerEnregistrer = False Then
                        Exit Sub

                    End If

                    'delete de la table Production details
                    cmdProduction.Connection = ConnectionServeur
                    cmdProduction.CommandText = "DELETE FROM PRODUCTION_DETAILS WHERE NumeroProduction ='" + NumeroProduction + "'"
                    cmdProduction.ExecuteNonQuery()

                    'delete de la table Production
                    cmdProduction.Connection = ConnectionServeur
                    cmdProduction.CommandText = "DELETE FROM PRODUCTION WHERE NumeroProduction ='" + NumeroProduction + "'"
                    cmdProduction.ExecuteNonQuery()

                    'Ma nouvelle position

                    If NumeroligneProduction > 1 Then
                        NumeroligneProduction = NumeroligneProduction - 1
                    ElseIf NumeroligneProduction = 1 Then
                        initLoadControl()

                    End If
                    'charger la nouvelle position
                    ChargerProduction(NumeroligneProduction)


                    MsgBox("Production supprimé !", MsgBoxStyle.Information, "Information")

                    Exit Sub

                End If

            End If

        End If
    End Sub

    Private Sub bComposonts_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bComposonts.Click

        Dim CodeArticle As String
        Dim TypePreparation As Integer
        Dim TotalTTC As Double = 0.0

        Try
            cmdProduction.Connection = ConnectionServeur
            cmdProduction.CommandText = "Select top(0) * FROM PRODUCTION_DETAILS"
            daProduction = New SqlDataAdapter(cmdProduction)
            daProduction.Fill(dsProduction, "PRODUCTION_DETAILS")
            cbProduction = New SqlCommandBuilder(daProduction)

            If gArticles.Row <= dsProduction.Tables("PRODUCTION_DETAILS").Rows.Count - 1 Then

                CodeArticle = gArticles.Columns("CodeArticle").Value

                TypePreparation = RecupererValeurExecuteScalaire("CodeTypePreparation", " FORMULE_PREPARATION", "CodePreparation", CodeArticle)

                Dim InstancePreparation As New fPreparation

                fPreparation.CodePreparation = gArticles.Columns("CodeArticle").Value

                fPreparation.CodeABarre = gArticles.Columns("CodeABarre").Value

                fPreparation.TypePreparation = TypePreparation

                fPreparation.DesignationPreparation = gArticles.Columns("Designation").Value

                fPreparation.init()

                InstancePreparation.Label1.Text = "Les Composants de la Préparation"

                'pour désactiver tout les chmaps de la forme
                InstancePreparation.GroupeFournisseur.Enabled = False
                InstancePreparation.gArticles.Enabled = False
                InstancePreparation.gIndemnites.Enabled = False
                InstancePreparation.bSupprimer.Enabled = False
                InstancePreparation.bIndemnites.Enabled = False
                InstancePreparation.bConfirmer.Enabled = False
                InstancePreparation.bVerifierPrix.Enabled = False
                InstancePreparation.bProduire.Enabled = False
                InstancePreparation.bAnnuler.Enabled = False

                InstancePreparation.tTotTTC.Enabled = False

                InstancePreparation.ShowDialog()

                InstancePreparation.Dispose()

                InstancePreparation.Close()

            End If
        Catch ex As Exception
            MsgBox("Cette préparation n'admet pas des composants ", MsgBoxStyle.Critical, "Information") 'message si l'article n'est pas pahramceutique
        End Try
    End Sub

    Public Sub ImprimerCodeBarre(Code As String, Designation As String, Qte As String, PrixVenteTTC As String)
        Dim cmdCodeABarre As New SqlCommand
        Dim daCodeABarre As New SqlDataAdapter
        Dim dsCodeABarre As New DataSet
        Dim cbCodeABarre As New SqlCommandBuilder

        Dim LineWrite As String = ""
        Dim TexteEtiquette As String = ""
        Dim NomSysteme As String = ""

Imprimme:
        Dim J As Integer = 0
        Dim nbCol As Integer = 0
        Dim dr As DataRow

        cmdCodeABarre.Connection = ConnectionServeur
        cmdCodeABarre.CommandText = "UPDATE PARAMETRE_PHARMACIE SET ImageCodeABarre=NULL"
        cmdCodeABarre.ExecuteNonQuery()

        cmdCodeABarre.CommandText = "SELECT * FROM PARAMETRE_PHARMACIE"
        daCodeABarre = New SqlDataAdapter(cmdCodeABarre)
        daCodeABarre.Fill(dsCodeABarre, "IMAGE")
        cbCodeABarre = New SqlCommandBuilder(daCodeABarre)

        BarcodeProfessional.Code = Code
        BarcodeProfessional.Text = ""
        BarcodeProfessional.Symbology = Neodynamic.WinControls.BarcodeProfessional.Symbology.Code128
        BarcodeProfessional.BarHeight = 1.0F
        BarcodeProfessional.BarWidth = 0.04F

        With dsCodeABarre.Tables("IMAGE")
            dr = .Rows(0)
            dr.Item("ImageCodeABarre") = ImageToByteArray(BarcodeProfessional.GetBarcodeImage())
        End With

        Try
            daCodeABarre.Update(dsCodeABarre, "IMAGE")

        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End Try

        If NomOrdinateurImpressionCodeABarre = "" Then

            cmdCodeABarre.Connection = ConnectionServeur
            cmdCodeABarre.CommandText = "DELETE FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE"
            cmdCodeABarre.ExecuteNonQuery()

            cmdCodeABarre.CommandText = "SELECT * FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE"
            daCodeABarre = New SqlDataAdapter(cmdCodeABarre)
            daCodeABarre.Fill(dsCodeABarre, "IMAGE")
            cbCodeABarre = New SqlCommandBuilder(daCodeABarre)



            If CInt(Qte) > 0 And CInt(Qte) <= 5 Then
                J = 1
                nbCol = CInt(Qte)
            ElseIf CInt(Qte) > 5 And CInt(Qte) <= 10 Then
                J = 2
                nbCol = CInt(Qte) - 5
            ElseIf CInt(Qte) > 10 And CInt(Qte) <= 15 Then
                J = 3
                nbCol = CInt(Qte) - 10
            ElseIf CInt(Qte) > 15 And CInt(Qte) <= 20 Then
                J = 4
                nbCol = CInt(Qte) - 15
            ElseIf CInt(Qte) > 20 And CInt(Qte) <= 25 Then
                J = 5
                nbCol = CInt(Qte) - 20
            ElseIf CInt(Qte) > 25 And CInt(Qte) <= 30 Then
                J = 6
                nbCol = CInt(Qte) - 25
            End If



            For I As Integer = 0 To J - 1
                With dsCodeABarre
                    dr = .Tables("IMAGE").NewRow

                    dr.Item("Col1") = " "
                    dr.Item("Col2") = " "
                    dr.Item("Col3") = " "
                    dr.Item("Col4") = " "
                    dr.Item("Col5") = " "
                    dr.Item("Ordre") = I
                    .Tables("IMAGE").Rows.Add(dr)
                End With
            Next
            Try
                daCodeABarre.Update(dsCodeABarre, "IMAGE")
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                Exit Sub
            End Try

            If nbCol = 1 Then
                cmdCodeABarre.CommandText = " UPDATE TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE SET Col2=NULL,COL3=NULL,Col4=NULL,COL5=NULL " + _
                                            " WHERE Ordre IN" + _
                                            " ( SELECT TOP(1) Ordre FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE ORDER BY Ordre DESC ) "
            ElseIf nbCol = 2 Then
                cmdCodeABarre.CommandText = " UPDATE TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE SET COL3=NULL,Col4=NULL,COL5=NULL " + _
                                            " WHERE Ordre IN" + _
                                            " ( SELECT TOP(1) Ordre FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE ORDER BY Ordre DESC ) "
            ElseIf nbCol = 3 Then
                cmdCodeABarre.CommandText = " UPDATE TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE SET Col4=NULL,COL5=NULL " + _
                                            " WHERE Ordre IN" + _
                                            " ( SELECT TOP(1) Ordre FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE ORDER BY Ordre DESC ) "
            ElseIf nbCol = 4 Then
                cmdCodeABarre.CommandText = " UPDATE TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE SET COL5=NULL " + _
                                            " WHERE Ordre IN" + _
                                            " ( SELECT TOP(1) Ordre FROM TABLE_TEMPORAIRE_POUR_IMPRESSION_CODEABARRE ORDER BY Ordre DESC ) "
            End If

            cmdCodeABarre.ExecuteNonQuery()

            Me.Dispose()
            Dim MyVisionneurCodeABarre As New fVisionneurCodeABarre
            MyVisionneurCodeABarre.ShowDialog()

        Else

            CR.FileName = Application.StartupPath + "\EtatImpressionCodeABarre.rpt"

            CR.SetParameterValue("Designation", Designation)
            CR.SetParameterValue("PrixVenteTTC", PrixVenteTTC)

            Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent

            CR.PrintOptions.PrinterName = NomOrdinateurImpressionCodeABarre
            CR.PrintToPrinter(Qte, False, 1, 1)
        End If
    End Sub

    Private Sub bCodeABarre_Click(sender As Object, e As EventArgs) Handles bCodeABarre.Click
        If ControleDAcces(6, "IMPRESSION_CODEABARRE") = "False" Then
            Exit Sub
        End If

        Dim cmdSansCodeABarre As New SqlCommand
        cmdSansCodeABarre.Connection = ConnectionServeur
        Dim AucunProduitSansCodeABarre As Boolean = True

        For I As Integer = 0 To gArticles.RowCount - 1

            cmdSansCodeABarre.CommandText = "SELECT SansCodeBarre FROM ARTICLE WHERE CodeArticle=" + Quote(gArticles(I, "CodeArticle"))

            If cmdSansCodeABarre.ExecuteScalar = True Then
                Dim Qte As String = "0"
                If Convert.ToInt32(gArticles(I, "Qte")) Mod 2 = 0 Then
                    Qte = (Convert.ToInt32(gArticles(I, "Qte")) / 2).ToString()
                Else
                    Qte = (Convert.ToInt32((Convert.ToInt32(gArticles(I, "Qte") + 1) / 2))).ToString()
                End If


                ImprimerCodeBarre(gArticles(I, "CodeABarre"), gArticles(I, "Designation"), Qte, RecupererValeurExecuteScalaire("PrixVenteTTC", "ARTICLE", "CodeArticle", gArticles(I, "CodeArticle")))
                AucunProduitSansCodeABarre = False
            End If
        Next

        If AucunProduitSansCodeABarre = True Then
            MsgBox("Aucun article dans la liste sans code à barre !", MsgBoxStyle.Information, "Information")
        End If
    End Sub
End Class
