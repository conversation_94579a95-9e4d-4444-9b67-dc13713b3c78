﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="pharmaModel.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2008" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityContainer Name="pharmaModelStoreContainer">
          <EntitySet Name="ACHAT" EntityType="pharmaModel.Store.ACHAT" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="ACHAT_DETAILS" EntityType="pharmaModel.Store.ACHAT_DETAILS" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="AVOIRACHAT" EntityType="pharmaModel.Store.AVOIRACHAT" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="AVOIRACHAT_DETAILS" EntityType="pharmaModel.Store.AVOIRACHAT_DETAILS" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="COMMANDE" EntityType="pharmaModel.Store.COMMANDE" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="COMMANDE_DETAILS" EntityType="pharmaModel.Store.COMMANDE_DETAILS" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="SIMULATION_STOCK" EntityType="pharmaModel.Store.SIMULATION_STOCK" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="SIMULATION_STOCK_DETAILS" EntityType="pharmaModel.Store.SIMULATION_STOCK_DETAILS" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="VENTE" EntityType="pharmaModel.Store.VENTE" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="VENTE_DETAILS" EntityType="pharmaModel.Store.VENTE_DETAILS" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="VENTE_NUMERO" EntityType="pharmaModel.Store.VENTE_NUMERO" store:Type="Tables" store:Schema="dbo" store:Name="VENTE_NUMERO">
            <DefiningQuery>SELECT 
      [VENTE_NUMERO].[NumeroVente] AS [NumeroVente]
      FROM [dbo].[VENTE_NUMERO] AS [VENTE_NUMERO]</DefiningQuery>
          </EntitySet>
          <AssociationSet Name="FK_SIMULATION_STOCK_DETAILS_SIMULATION_STOCK" Association="pharmaModel.Store.FK_SIMULATION_STOCK_DETAILS_SIMULATION_STOCK">
            <End Role="SIMULATION_STOCK" EntitySet="SIMULATION_STOCK" />
            <End Role="SIMULATION_STOCK_DETAILS" EntitySet="SIMULATION_STOCK_DETAILS" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="ACHAT">
          <Key>
            <PropertyRef Name="NumeroAchat" />
          </Key>
          <Property Name="NumeroAchat" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Date" Type="datetime" Nullable="false" />
          <Property Name="TotalHT" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TVA" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalTTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalRemise" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="Timbre" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="CodeFournisseur" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodePersonnel" Type="varchar" Nullable="false" MaxLength="50" />
          <Property Name="Note" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="NumeroBL/Facture" Type="varchar" MaxLength="255" />
          <Property Name="LibellePoste" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="DateBlFacture" Type="date" Nullable="false" />
          <Property Name="ValeurVenteTTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="Autre" Type="decimal" Nullable="false" Scale="3" />
        </EntityType>
        <EntityType Name="ACHAT_DETAILS">
          <Key>
            <PropertyRef Name="NumeroAchat" />
            <PropertyRef Name="CodeArticle" />
            <PropertyRef Name="NumeroLotArticle" />
          </Key>
          <Property Name="NumeroAchat" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodeArticle" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="NumeroLotArticle" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Ordre" Type="bigint" Nullable="false" />
          <Property Name="CodeABarre" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Designation" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodeForme" Type="int" Nullable="false" />
          <Property Name="Qte" Type="int" Nullable="false" />
          <Property Name="Stock" Type="int" Nullable="false" />
          <Property Name="PrixAchatHT" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalAchatHT" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="PrixVenteTTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="Remise" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TVA" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="DatePeremption" Type="date" />
          <Property Name="QuantiteUnitaire" Type="int" Nullable="false" />
          <Property Name="CodePCT" Type="nvarchar" MaxLength="50" />
          <Property Name="QteGratuite" Type="int" />
        </EntityType>
        <EntityType Name="AVOIRACHAT">
          <Key>
            <PropertyRef Name="NumeroAvoirAchat" />
          </Key>
          <Property Name="NumeroAvoirAchat" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Date" Type="datetime" Nullable="false" />
          <Property Name="TotalHT" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TVA" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalTTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalRemise" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="Timbre" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="CodeFournisseur" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodePersonnel" Type="varchar" Nullable="false" MaxLength="50" />
          <Property Name="Note" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="NumeroBL/Facture" Type="varchar" MaxLength="255" />
          <Property Name="LibellePoste" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="DateBlFacture" Type="date" Nullable="false" />
          <Property Name="ValeurVenteTTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="Autre" Type="decimal" Nullable="false" Scale="3" />
        </EntityType>
        <EntityType Name="AVOIRACHAT_DETAILS">
          <Key>
            <PropertyRef Name="NumeroAvoirAchat" />
            <PropertyRef Name="CodeArticle" />
            <PropertyRef Name="NumeroLotArticle" />
          </Key>
          <Property Name="NumeroAvoirAchat" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodeArticle" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="NumeroLotArticle" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Ordre" Type="bigint" Nullable="false" />
          <Property Name="CodeABarre" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Designation" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodeForme" Type="int" Nullable="false" />
          <Property Name="Qte" Type="int" Nullable="false" />
          <Property Name="Stock" Type="int" Nullable="false" />
          <Property Name="PrixAchatHT" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalAchatHT" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="PrixVenteTTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="Remise" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TVA" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="DatePeremption" Type="date" />
          <Property Name="QuantiteUnitaire" Type="int" Nullable="false" />
          <Property Name="CodePCT" Type="nvarchar" MaxLength="50" />
          <Property Name="QteGratuite" Type="int" />
        </EntityType>
        <EntityType Name="COMMANDE">
          <Key>
            <PropertyRef Name="NumeroCommande" />
          </Key>
          <Property Name="NumeroCommande" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Date" Type="datetime" Nullable="false" />
          <Property Name="TotalHT" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalTTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalTVA" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="LibellePoste" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodePersonnel" Type="varchar" Nullable="false" MaxLength="50" />
          <Property Name="CodeFournisseur" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Note" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="NumeroFacture" Type="varchar" MaxLength="255" />
          <Property Name="TypeCommande" Type="varchar" Nullable="false" MaxLength="255" />
        </EntityType>
        <EntityType Name="COMMANDE_DETAILS">
          <Key>
            <PropertyRef Name="NumeroCommande" />
            <PropertyRef Name="CodeArticle" />
          </Key>
          <Property Name="NumeroCommande" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodeArticle" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodeABarre" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Designation" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodeForme" Type="int" Nullable="false" />
          <Property Name="Qte" Type="int" Nullable="false" />
          <Property Name="Stock" Type="int" Nullable="false" />
          <Property Name="DatePeremption" Type="datetime" />
          <Property Name="PrixAchatHT" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TVA" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalTTCAchat" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="StockAlerte" Type="numeric" Nullable="false" Precision="6" />
          <Property Name="EnCours" Type="numeric" Nullable="false" Precision="6" />
          <Property Name="QteACommander" Type="numeric" Nullable="false" Precision="6" />
          <Property Name="QteUnitaire" Type="numeric" Nullable="false" Precision="6" />
          <Property Name="Ordre" Type="int" />
        </EntityType>
        <EntityType Name="SIMULATION_STOCK">
          <Key>
            <PropertyRef Name="NumeroSimulation" />
          </Key>
          <Property Name="NumeroSimulation" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Date" Type="datetime" Nullable="false" />
          <Property Name="DateImpression" Type="date" Nullable="false" />
          <Property Name="TotalAHT" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalATTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalVTTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="CodeCategorie" Type="int" />
          <Property Name="CodePersonnel" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="SIMULATION_STOCK_DETAILS">
          <Key>
            <PropertyRef Name="NumeroSimulation" />
            <PropertyRef Name="CodeArticle" />
          </Key>
          <Property Name="NumeroSimulation" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodeArticle" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Designation" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodeForme" Type="int" Nullable="false" />
          <Property Name="Qte" Type="int" Nullable="false" />
          <Property Name="PrixAchatHT" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="PrixAchatTTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="PrixVenteTTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalAchatHT" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalAchatTTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalVenteTTC" Type="decimal" Nullable="false" Scale="3" />
        </EntityType>
        <EntityType Name="VENTE">
          <Key>
            <PropertyRef Name="NumeroVente" />
          </Key>
          <Property Name="NumeroVente" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Date" Type="datetime" Nullable="false" />
          <Property Name="TotalHT" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalTTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TVA" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalRemise" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="Timbre" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="CodeClient" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodePersonnel" Type="varchar" Nullable="false" MaxLength="50" />
          <Property Name="CodeAPCI" Type="int" />
          <Property Name="CodeDeFamille" Type="int" />
          <Property Name="CodeMedecinFamille" Type="varchar" MaxLength="255" />
          <Property Name="CodeMedecinPrescripteur" Type="varchar" MaxLength="255" />
          <Property Name="LibellePoste" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Recu" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="DateOrdonnance" Type="date" />
          <Property Name="MontantCnam" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="MontantMutuelle" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="DureeTraitement" Type="int" Nullable="false" />
          <Property Name="Note" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodeNatureReglement" Type="int" />
          <Property Name="CodeMutuelle" Type="varchar" MaxLength="255" />
          <Property Name="NomMalade" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Rang" Type="int" />
          <Property Name="DateNaissance" Type="date" />
          <Property Name="CodeLienDeParente" Type="int" />
          <Property Name="LibelleLienDeParente" Type="varchar" Nullable="false" MaxLength="50" />
          <Property Name="TiersPayant" Type="bit" Nullable="false" />
          <Property Name="PriseEnCharge" Type="bit" Nullable="false" />
          <Property Name="Appareillage" Type="bit" Nullable="false" />
          <Property Name="IdentifiantCNAMMedecin" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Libelle1" Type="varchar" MaxLength="255" />
          <Property Name="Libelle2" Type="varchar" MaxLength="255" />
          <Property Name="Libelle3" Type="varchar" MaxLength="255" />
          <Property Name="Libelle4" Type="varchar" MaxLength="255" />
          <Property Name="Libelle5" Type="varchar" MaxLength="255" />
          <Property Name="NumeroFacture" Type="varchar" MaxLength="255" />
          <Property Name="Totaliseur" Type="bit" />
          <Property Name="Vider" Type="bit" Nullable="false" />
          <Property Name="NumeroPriseEnCharge" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="NumeroBonAchat" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="OMF" Type="bit" />
          <Property Name="APCI" Type="bit" />
          <Property Name="CodeAppareillage" Type="varchar" MaxLength="10" />
          <Property Name="NomInscritSurLeCheque" Type="varchar" MaxLength="255" />
          <Property Name="NumeroCheque" Type="varchar" MaxLength="255" />
          <Property Name="DateEcheance" Type="date" />
          <Property Name="IDFacturationClient" Type="int" />
        </EntityType>
        <EntityType Name="VENTE_DETAILS">
          <Key>
            <PropertyRef Name="NumeroVente" />
            <PropertyRef Name="CodeArticle" />
            <PropertyRef Name="NumeroLotArticle" />
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="NumeroVente" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodeArticle" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="NumeroLotArticle" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodeABarre" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Designation" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodeForme" Type="int" Nullable="false" />
          <Property Name="Qte" Type="int" Nullable="false" />
          <Property Name="PrixAchat" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="PrixHT" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalHT" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="PrixTTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalTTC" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="Remise" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TVA" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="TotalTVA" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="Honoraire" Type="decimal" Nullable="false" Scale="3" />
          <Property Name="Stock" Type="int" Nullable="false" />
          <Property Name="DateDePeremption" Type="date" />
          <Property Name="PriseEnCharge" Type="bit" />
          <Property Name="AccordPrealable" Type="bit" />
          <Property Name="TarifDeReference" Type="decimal" Scale="3" />
          <Property Name="DureeTraitement" Type="int" Nullable="false" />
          <Property Name="Ordre" Type="int" Nullable="false" />
          <Property Name="Id" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="MontantMutuelle" Type="decimal" Scale="3" />
          <Property Name="MontantCNAM" Type="decimal" Scale="3" />
        </EntityType>
        <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « pharma.dbo.VENTE_NUMERO » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
        <EntityType Name="VENTE_NUMERO">
          <Key>
            <PropertyRef Name="NumeroVente" />
          </Key>
          <Property Name="NumeroVente" Type="varchar" Nullable="false" MaxLength="50" />
        </EntityType>
        <Association Name="FK_SIMULATION_STOCK_DETAILS_SIMULATION_STOCK">
          <End Role="SIMULATION_STOCK" Type="pharmaModel.Store.SIMULATION_STOCK" Multiplicity="1" />
          <End Role="SIMULATION_STOCK_DETAILS" Type="pharmaModel.Store.SIMULATION_STOCK_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="SIMULATION_STOCK">
              <PropertyRef Name="NumeroSimulation" />
            </Principal>
            <Dependent Role="SIMULATION_STOCK_DETAILS">
              <PropertyRef Name="NumeroSimulation" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="BusinessManagementModel" Alias="Self" p1:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:p1="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="BusinessManagementEntities" p1:LazyLoadingEnabled="true">
          <EntitySet Name="VENTE_NUMERO" EntityType="BusinessManagementModel.VENTE_NUMERO" />
          <EntitySet Name="VENTE" EntityType="BusinessManagementModel.VENTE" />
          <EntitySet Name="VENTE_DETAILS" EntityType="BusinessManagementModel.VENTE_DETAILS" />
          <AssociationSet Name="VENTEVENTE_DETAILS" Association="BusinessManagementModel.VENTEVENTE_DETAILS">
            <End Role="VENTE" EntitySet="VENTE" />
            <End Role="VENTE_DETAILS" EntitySet="VENTE_DETAILS" />
          </AssociationSet>
          <EntitySet Name="COMMANDE" EntityType="BusinessManagementModel.COMMANDE" />
          <EntitySet Name="COMMANDE_DETAILS" EntityType="BusinessManagementModel.COMMANDE_DETAILS" />
          <AssociationSet Name="COMMANDECOMMANDE_DETAILS" Association="BusinessManagementModel.COMMANDECOMMANDE_DETAILS">
            <End Role="COMMANDE" EntitySet="COMMANDE" />
            <End Role="COMMANDE_DETAILS" EntitySet="COMMANDE_DETAILS" />
          </AssociationSet>
          <EntitySet Name="SIMULATION_STOCK" EntityType="BusinessManagementModel.SIMULATION_STOCK" />
          <EntitySet Name="SIMULATION_STOCK_DETAILS" EntityType="BusinessManagementModel.SIMULATION_STOCK_DETAILS" />
          <AssociationSet Name="SIMULATION_STOCKSIMULATION_STOCK_DETAILS" Association="BusinessManagementModel.SIMULATION_STOCKSIMULATION_STOCK_DETAILS">
            <End Role="SIMULATION_STOCK" EntitySet="SIMULATION_STOCK" />
            <End Role="SIMULATION_STOCK_DETAILS" EntitySet="SIMULATION_STOCK_DETAILS" />
          </AssociationSet>
          <EntitySet Name="ACHAT" EntityType="BusinessManagementModel.ACHAT" />
          <EntitySet Name="ACHAT_DETAILS" EntityType="BusinessManagementModel.ACHAT_DETAILS" />
          <AssociationSet Name="ACHATACHAT_DETAILS" Association="BusinessManagementModel.ACHATACHAT_DETAILS">
            <End Role="ACHAT" EntitySet="ACHAT" />
            <End Role="ACHAT_DETAILS" EntitySet="ACHAT_DETAILS" />
          </AssociationSet>
          <EntitySet Name="AVOIRACHATs" EntityType="BusinessManagementModel.AVOIRACHAT" />
          <EntitySet Name="AVOIRACHAT_DETAILS" EntityType="BusinessManagementModel.AVOIRACHAT_DETAILS" />
          <AssociationSet Name="AVOIRACHATAVOIRACHAT_DETAILS" Association="BusinessManagementModel.AVOIRACHATAVOIRACHAT_DETAILS">
            <End Role="AVOIRACHAT" EntitySet="AVOIRACHATs" />
            <End Role="AVOIRACHAT_DETAILS" EntitySet="AVOIRACHAT_DETAILS" />
          </AssociationSet>
          </EntityContainer>
        <EntityType Name="VENTE_NUMERO">
          <Key>
            <PropertyRef Name="NumeroVente" />
          </Key>
          <Property Type="String" Name="NumeroVente" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="VENTE">
          <Key>
            <PropertyRef Name="NumeroVente" />
          </Key>
          <Property Type="String" Name="NumeroVente" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="DateTime" Name="Date" Nullable="false" Precision="3" />
          <Property Type="Decimal" Name="TotalHT" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalRemise" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Timbre" Nullable="false" Precision="18" Scale="3" />
          <Property Type="String" Name="CodeClient" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodePersonnel" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="CodeAPCI" />
          <Property Type="Int32" Name="CodeDeFamille" />
          <Property Type="String" Name="CodeMedecinFamille" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodeMedecinPrescripteur" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="LibellePoste" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Decimal" Name="Recu" Nullable="false" Precision="18" Scale="3" />
          <Property Type="DateTime" Name="DateOrdonnance" Precision="0" />
          <Property Type="Decimal" Name="MontantCnam" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantMutuelle" Nullable="false" Precision="18" Scale="3" />
          <Property Type="String" Name="Note" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="CodeNatureReglement" />
          <Property Type="String" Name="CodeMutuelle" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NomMalade" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="Rang" />
          <Property Type="DateTime" Name="DateNaissance" Precision="0" />
          <Property Type="Int32" Name="CodeLienDeParente" />
          <Property Type="String" Name="LibelleLienDeParente" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="Boolean" Name="TiersPayant" Nullable="false" />
          <Property Type="Boolean" Name="PriseEnCharge" Nullable="false" />
          <Property Type="Boolean" Name="Appareillage" Nullable="false" />
          <Property Type="String" Name="IdentifiantCNAMMedecin" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Libelle1" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Libelle2" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Libelle3" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Libelle4" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Libelle5" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NumeroFacture" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Boolean" Name="Totaliseur" />
          <Property Type="Boolean" Name="Vider" Nullable="false" />
          <Property Type="String" Name="NumeroPriseEnCharge" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NumeroBonAchat" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="DureeTraitement" Nullable="false" />
          <Property Type="Boolean" Name="OMF" />
          <Property Type="Boolean" Name="APCI" />
          <Property Type="String" Name="CodeAppareillage" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NomInscritSurLeCheque" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NumeroCheque" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="DateTime" Name="DateEcheance" Precision="0" />
          <Property Type="Int32" Name="IDFacturationClient" />
          <NavigationProperty Name="VENTE_DETAILS" Relationship="BusinessManagementModel.VENTEVENTE_DETAILS" FromRole="VENTE" ToRole="VENTE_DETAILS" />
        </EntityType>
        <EntityType Name="VENTE_DETAILS">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="NumeroVente" />
            <PropertyRef Name="CodeArticle" />
            <PropertyRef Name="NumeroLotArticle" />
          </Key>
          <Property Type="Int32" Name="Id" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Type="String" Name="NumeroVente" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodeArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NumeroLotArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="Ordre" Nullable="false" />
          <Property Type="String" Name="CodeABarre" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Designation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="CodeForme" Nullable="false" />
          <Property Type="Int32" Name="Qte" Nullable="false" />
          <Property Type="Decimal" Name="PrixAchat" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="PrixHT" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalHT" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="PrixTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Remise" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTVA" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Honoraire" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Int32" Name="Stock" Nullable="false" />
          <Property Type="DateTime" Name="DateDePeremption" Precision="0" />
          <Property Type="Boolean" Name="PriseEnCharge" />
          <Property Type="Boolean" Name="AccordPrealable" />
          <Property Type="Decimal" Name="TarifDeReference" Precision="18" Scale="3" />
          <Property Type="Int32" Name="DureeTraitement" Nullable="false" />
          <Property Type="Decimal" Name="MontantMutuelle" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantCNAM" Precision="18" Scale="3" />
        </EntityType>
        <Association Name="VENTEVENTE_DETAILS">
          <End Type="BusinessManagementModel.VENTE" Role="VENTE" Multiplicity="1" />
          <End Type="BusinessManagementModel.VENTE_DETAILS" Role="VENTE_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="VENTE">
              <PropertyRef Name="NumeroVente" />
            </Principal>
            <Dependent Role="VENTE_DETAILS">
              <PropertyRef Name="NumeroVente" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="COMMANDE">
          <Key>
            <PropertyRef Name="NumeroCommande" />
          </Key>
          <Property Type="String" Name="NumeroCommande" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="DateTime" Name="Date" Nullable="false" Precision="3" />
          <Property Type="Decimal" Name="TotalHT" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTVA" Nullable="false" Precision="18" Scale="3" />
          <Property Type="String" Name="LibellePoste" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodePersonnel" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodeFournisseur" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Note" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NumeroFacture" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="TypeCommande" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <NavigationProperty Name="COMMANDE_DETAILS" Relationship="BusinessManagementModel.COMMANDECOMMANDE_DETAILS" FromRole="COMMANDE" ToRole="COMMANDE_DETAILS" />
        </EntityType>
        <EntityType Name="COMMANDE_DETAILS">
          <Key>
            <PropertyRef Name="NumeroCommande" />
            <PropertyRef Name="CodeArticle" />
          </Key>
          <Property Type="String" Name="NumeroCommande" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodeArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodeABarre" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Designation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="CodeForme" Nullable="false" />
          <Property Type="Int32" Name="Qte" Nullable="false" />
          <Property Type="Int32" Name="Stock" Nullable="false" />
          <Property Type="DateTime" Name="DatePeremption" Precision="3" />
          <Property Type="Decimal" Name="PrixAchatHT" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTCAchat" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="StockAlerte" Nullable="false" Precision="6" Scale="0" />
          <Property Type="Decimal" Name="EnCours" Nullable="false" Precision="6" Scale="0" />
          <Property Type="Decimal" Name="QteACommander" Nullable="false" Precision="6" Scale="0" />
          <Property Type="Decimal" Name="QteUnitaire" Nullable="false" Precision="6" Scale="0" />
          <Property Type="Int32" Name="Ordre" />
        </EntityType>
        <Association Name="COMMANDECOMMANDE_DETAILS">
          <End Type="BusinessManagementModel.COMMANDE" Role="COMMANDE" Multiplicity="1" />
          <End Type="BusinessManagementModel.COMMANDE_DETAILS" Role="COMMANDE_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="COMMANDE">
              <PropertyRef Name="NumeroCommande" />
            </Principal>
            <Dependent Role="COMMANDE_DETAILS">
              <PropertyRef Name="NumeroCommande" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="SIMULATION_STOCK">
          <Key>
            <PropertyRef Name="NumeroSimulation" />
          </Key>
          <Property Type="String" Name="NumeroSimulation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="DateTime" Name="Date" Nullable="false" Precision="3" />
          <Property Type="DateTime" Name="DateImpression" Nullable="false" Precision="0" />
          <Property Type="Decimal" Name="TotalAHT" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalATTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalVTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Int32" Name="CodeCategorie" />
          <Property Type="Int32" Name="CodePersonnel" Nullable="false" />
          <NavigationProperty Name="SIMULATION_STOCK_DETAILS" Relationship="BusinessManagementModel.SIMULATION_STOCKSIMULATION_STOCK_DETAILS" FromRole="SIMULATION_STOCK" ToRole="SIMULATION_STOCK_DETAILS" />
        </EntityType>
        <EntityType Name="SIMULATION_STOCK_DETAILS">
          <Key>
            <PropertyRef Name="NumeroSimulation" />
            <PropertyRef Name="CodeArticle" />
          </Key>
          <Property Type="String" Name="NumeroSimulation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodeArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Designation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="CodeForme" Nullable="false" />
          <Property Type="Int32" Name="Qte" Nullable="false" />
          <Property Type="Decimal" Name="PrixAchatHT" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="PrixAchatTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="PrixVenteTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalAchatHT" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalAchatTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalVenteTTC" Nullable="false" Precision="18" Scale="3" />
        </EntityType>
        <Association Name="SIMULATION_STOCKSIMULATION_STOCK_DETAILS">
          <End Type="BusinessManagementModel.SIMULATION_STOCK" Role="SIMULATION_STOCK" Multiplicity="1" />
          <End Type="BusinessManagementModel.SIMULATION_STOCK_DETAILS" Role="SIMULATION_STOCK_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="SIMULATION_STOCK">
              <PropertyRef Name="NumeroSimulation" />
            </Principal>
            <Dependent Role="SIMULATION_STOCK_DETAILS">
              <PropertyRef Name="NumeroSimulation" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="ACHAT">
          <Key>
            <PropertyRef Name="NumeroAchat" />
          </Key>
          <Property Type="String" Name="NumeroAchat" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="DateTime" Name="Date" Nullable="false" Precision="3" />
          <Property Type="Decimal" Name="TotalHT" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalRemise" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Timbre" Nullable="false" Precision="18" Scale="3" />
          <Property Type="String" Name="CodeFournisseur" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodePersonnel" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Note" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NumeroBL_Facture" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="LibellePoste" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="DateTime" Name="DateBlFacture" Nullable="false" Precision="0" />
          <Property Type="Decimal" Name="ValeurVenteTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Autre" Nullable="false" Precision="18" Scale="3" />
          <NavigationProperty Name="ACHAT_DETAILS" Relationship="BusinessManagementModel.ACHATACHAT_DETAILS" FromRole="ACHAT" ToRole="ACHAT_DETAILS" />
        </EntityType>
        <EntityType Name="ACHAT_DETAILS">
          <Key>
            <PropertyRef Name="NumeroAchat" />
            <PropertyRef Name="CodeArticle" />
            <PropertyRef Name="NumeroLotArticle" />
          </Key>
          <Property Type="String" Name="NumeroAchat" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodeArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NumeroLotArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int64" Name="Ordre" Nullable="false" />
          <Property Type="String" Name="CodeABarre" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Designation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="CodeForme" Nullable="false" />
          <Property Type="Int32" Name="Qte" Nullable="false" />
          <Property Type="Int32" Name="Stock" Nullable="false" />
          <Property Type="Decimal" Name="PrixAchatHT" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalAchatHT" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="PrixVenteTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Remise" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA" Nullable="false" Precision="18" Scale="3" />
          <Property Type="DateTime" Name="DatePeremption" Precision="0" />
          <Property Type="Int32" Name="QuantiteUnitaire" Nullable="false" />
          <Property Type="String" Name="CodePCT" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Type="Int32" Name="QteGratuite" />
        </EntityType>
        <Association Name="ACHATACHAT_DETAILS">
          <End Type="BusinessManagementModel.ACHAT" Role="ACHAT" Multiplicity="1" />
          <End Type="BusinessManagementModel.ACHAT_DETAILS" Role="ACHAT_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ACHAT">
              <PropertyRef Name="NumeroAchat" />
            </Principal>
            <Dependent Role="ACHAT_DETAILS">
              <PropertyRef Name="NumeroAchat" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="AVOIRACHAT">
          <Key>
            <PropertyRef Name="NumeroAvoirAchat" />
          </Key>
          <Property Type="String" Name="NumeroAvoirAchat" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="DateTime" Name="Date" Nullable="false" Precision="3" />
          <Property Type="Decimal" Name="TotalHT" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalRemise" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Timbre" Nullable="false" Precision="18" Scale="3" />
          <Property Type="String" Name="CodeFournisseur" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodePersonnel" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Note" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NumeroBL_Facture" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="LibellePoste" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="DateTime" Name="DateBlFacture" Nullable="false" Precision="0" />
          <Property Type="Decimal" Name="ValeurVenteTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Autre" Nullable="false" Precision="18" Scale="3" />
          <NavigationProperty Name="AVOIRACHAT_DETAILS" Relationship="BusinessManagementModel.AVOIRACHATAVOIRACHAT_DETAILS" FromRole="AVOIRACHAT" ToRole="AVOIRACHAT_DETAILS" />
        </EntityType>
        <EntityType Name="AVOIRACHAT_DETAILS">
          <Key>
            <PropertyRef Name="NumeroAvoirAchat" />
            <PropertyRef Name="CodeArticle" />
            <PropertyRef Name="NumeroLotArticle" />
          </Key>
          <Property Type="String" Name="NumeroAvoirAchat" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodeArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NumeroLotArticle" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int64" Name="Ordre" Nullable="false" />
          <Property Type="String" Name="CodeABarre" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Designation" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="CodeForme" Nullable="false" />
          <Property Type="Int32" Name="Qte" Nullable="false" />
          <Property Type="Int32" Name="Stock" Nullable="false" />
          <Property Type="Decimal" Name="PrixVenteTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Remise" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA" Nullable="false" Precision="18" Scale="3" />
          <Property Type="DateTime" Name="DatePeremption" Precision="0" />
          <Property Type="Int32" Name="QuantiteUnitaire" Nullable="false" />
          <Property Type="String" Name="CodePCT" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Type="Int32" Name="QteGratuite" />
          <Property Type="Decimal" Name="PrixAchatHT" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalAchatHT" Nullable="false" Precision="18" Scale="3" />
        </EntityType>
        <Association Name="AVOIRACHATAVOIRACHAT_DETAILS">
          <End Type="BusinessManagementModel.AVOIRACHAT" Role="AVOIRACHAT" Multiplicity="1" />
          <End Type="BusinessManagementModel.AVOIRACHAT_DETAILS" Role="AVOIRACHAT_DETAILS" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="AVOIRACHAT">
              <PropertyRef Name="NumeroAvoirAchat" />
            </Principal>
            <Dependent Role="AVOIRACHAT_DETAILS">
              <PropertyRef Name="NumeroAvoirAchat" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="pharmaModelStoreContainer" CdmEntityContainer="BusinessManagementEntities">
          <EntitySetMapping Name="VENTE_NUMERO">
            <EntityTypeMapping TypeName="BusinessManagementModel.VENTE_NUMERO">
              <MappingFragment StoreEntitySet="VENTE_NUMERO">
                <ScalarProperty Name="NumeroVente" ColumnName="NumeroVente" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="VENTE">
            <EntityTypeMapping TypeName="BusinessManagementModel.VENTE">
              <MappingFragment StoreEntitySet="VENTE">
                <ScalarProperty Name="IDFacturationClient" ColumnName="IDFacturationClient" />
                <ScalarProperty Name="DateEcheance" ColumnName="DateEcheance" />
                <ScalarProperty Name="NumeroCheque" ColumnName="NumeroCheque" />
                <ScalarProperty Name="NomInscritSurLeCheque" ColumnName="NomInscritSurLeCheque" />
                <ScalarProperty Name="CodeAppareillage" ColumnName="CodeAppareillage" />
                <ScalarProperty Name="APCI" ColumnName="APCI" />
                <ScalarProperty Name="OMF" ColumnName="OMF" />
                <ScalarProperty Name="DureeTraitement" ColumnName="DureeTraitement" />
                <ScalarProperty Name="NumeroBonAchat" ColumnName="NumeroBonAchat" />
                <ScalarProperty Name="NumeroPriseEnCharge" ColumnName="NumeroPriseEnCharge" />
                <ScalarProperty Name="Vider" ColumnName="Vider" />
                <ScalarProperty Name="Totaliseur" ColumnName="Totaliseur" />
                <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
                <ScalarProperty Name="Libelle5" ColumnName="Libelle5" />
                <ScalarProperty Name="Libelle4" ColumnName="Libelle4" />
                <ScalarProperty Name="Libelle3" ColumnName="Libelle3" />
                <ScalarProperty Name="Libelle2" ColumnName="Libelle2" />
                <ScalarProperty Name="Libelle1" ColumnName="Libelle1" />
                <ScalarProperty Name="IdentifiantCNAMMedecin" ColumnName="IdentifiantCNAMMedecin" />
                <ScalarProperty Name="Appareillage" ColumnName="Appareillage" />
                <ScalarProperty Name="PriseEnCharge" ColumnName="PriseEnCharge" />
                <ScalarProperty Name="TiersPayant" ColumnName="TiersPayant" />
                <ScalarProperty Name="LibelleLienDeParente" ColumnName="LibelleLienDeParente" />
                <ScalarProperty Name="CodeLienDeParente" ColumnName="CodeLienDeParente" />
                <ScalarProperty Name="DateNaissance" ColumnName="DateNaissance" />
                <ScalarProperty Name="Rang" ColumnName="Rang" />
                <ScalarProperty Name="NomMalade" ColumnName="NomMalade" />
                <ScalarProperty Name="CodeMutuelle" ColumnName="CodeMutuelle" />
                <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
                <ScalarProperty Name="Note" ColumnName="Note" />
                <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
                <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
                <ScalarProperty Name="DateOrdonnance" ColumnName="DateOrdonnance" />
                <ScalarProperty Name="Recu" ColumnName="Recu" />
                <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
                <ScalarProperty Name="CodeMedecinPrescripteur" ColumnName="CodeMedecinPrescripteur" />
                <ScalarProperty Name="CodeMedecinFamille" ColumnName="CodeMedecinFamille" />
                <ScalarProperty Name="CodeDeFamille" ColumnName="CodeDeFamille" />
                <ScalarProperty Name="CodeAPCI" ColumnName="CodeAPCI" />
                <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
                <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
                <ScalarProperty Name="Timbre" ColumnName="Timbre" />
                <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
                <ScalarProperty Name="TVA" ColumnName="TVA" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="NumeroVente" ColumnName="NumeroVente" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="VENTE_DETAILS">
            <EntityTypeMapping TypeName="BusinessManagementModel.VENTE_DETAILS">
              <MappingFragment StoreEntitySet="VENTE_DETAILS">
                <ScalarProperty Name="MontantCNAM" ColumnName="MontantCNAM" />
                <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
                <ScalarProperty Name="DureeTraitement" ColumnName="DureeTraitement" />
                <ScalarProperty Name="TarifDeReference" ColumnName="TarifDeReference" />
                <ScalarProperty Name="AccordPrealable" ColumnName="AccordPrealable" />
                <ScalarProperty Name="PriseEnCharge" ColumnName="PriseEnCharge" />
                <ScalarProperty Name="DateDePeremption" ColumnName="DateDePeremption" />
                <ScalarProperty Name="Stock" ColumnName="Stock" />
                <ScalarProperty Name="Honoraire" ColumnName="Honoraire" />
                <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
                <ScalarProperty Name="TVA" ColumnName="TVA" />
                <ScalarProperty Name="Remise" ColumnName="Remise" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="PrixTTC" ColumnName="PrixTTC" />
                <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
                <ScalarProperty Name="PrixHT" ColumnName="PrixHT" />
                <ScalarProperty Name="PrixAchat" ColumnName="PrixAchat" />
                <ScalarProperty Name="Qte" ColumnName="Qte" />
                <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
                <ScalarProperty Name="Ordre" ColumnName="Ordre" />
                <ScalarProperty Name="NumeroLotArticle" ColumnName="NumeroLotArticle" />
                <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
                <ScalarProperty Name="NumeroVente" ColumnName="NumeroVente" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="COMMANDE">
            <EntityTypeMapping TypeName="BusinessManagementModel.COMMANDE">
              <MappingFragment StoreEntitySet="COMMANDE">
                <ScalarProperty Name="TypeCommande" ColumnName="TypeCommande" />
                <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
                <ScalarProperty Name="Note" ColumnName="Note" />
                <ScalarProperty Name="CodeFournisseur" ColumnName="CodeFournisseur" />
                <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
                <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
                <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="NumeroCommande" ColumnName="NumeroCommande" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="COMMANDE_DETAILS">
            <EntityTypeMapping TypeName="BusinessManagementModel.COMMANDE_DETAILS">
              <MappingFragment StoreEntitySet="COMMANDE_DETAILS">
                <ScalarProperty Name="Ordre" ColumnName="Ordre" />
                <ScalarProperty Name="QteUnitaire" ColumnName="QteUnitaire" />
                <ScalarProperty Name="QteACommander" ColumnName="QteACommander" />
                <ScalarProperty Name="EnCours" ColumnName="EnCours" />
                <ScalarProperty Name="StockAlerte" ColumnName="StockAlerte" />
                <ScalarProperty Name="TotalTTCAchat" ColumnName="TotalTTCAchat" />
                <ScalarProperty Name="TVA" ColumnName="TVA" />
                <ScalarProperty Name="PrixAchatHT" ColumnName="PrixAchatHT" />
                <ScalarProperty Name="DatePeremption" ColumnName="DatePeremption" />
                <ScalarProperty Name="Stock" ColumnName="Stock" />
                <ScalarProperty Name="Qte" ColumnName="Qte" />
                <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
                <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
                <ScalarProperty Name="NumeroCommande" ColumnName="NumeroCommande" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="SIMULATION_STOCK">
            <EntityTypeMapping TypeName="BusinessManagementModel.SIMULATION_STOCK">
              <MappingFragment StoreEntitySet="SIMULATION_STOCK">
                <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
                <ScalarProperty Name="CodeCategorie" ColumnName="CodeCategorie" />
                <ScalarProperty Name="TotalVTTC" ColumnName="TotalVTTC" />
                <ScalarProperty Name="TotalATTC" ColumnName="TotalATTC" />
                <ScalarProperty Name="TotalAHT" ColumnName="TotalAHT" />
                <ScalarProperty Name="DateImpression" ColumnName="DateImpression" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="NumeroSimulation" ColumnName="NumeroSimulation" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="SIMULATION_STOCK_DETAILS">
            <EntityTypeMapping TypeName="BusinessManagementModel.SIMULATION_STOCK_DETAILS">
              <MappingFragment StoreEntitySet="SIMULATION_STOCK_DETAILS">
                <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
                <ScalarProperty Name="TotalAchatTTC" ColumnName="TotalAchatTTC" />
                <ScalarProperty Name="TotalAchatHT" ColumnName="TotalAchatHT" />
                <ScalarProperty Name="PrixVenteTTC" ColumnName="PrixVenteTTC" />
                <ScalarProperty Name="PrixAchatTTC" ColumnName="PrixAchatTTC" />
                <ScalarProperty Name="PrixAchatHT" ColumnName="PrixAchatHT" />
                <ScalarProperty Name="Qte" ColumnName="Qte" />
                <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
                <ScalarProperty Name="NumeroSimulation" ColumnName="NumeroSimulation" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ACHAT">
            <EntityTypeMapping TypeName="IsTypeOf(BusinessManagementModel.ACHAT)">
              <MappingFragment StoreEntitySet="ACHAT">
                <ScalarProperty Name="NumeroAchat" ColumnName="NumeroAchat" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
                <ScalarProperty Name="TVA" ColumnName="TVA" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
                <ScalarProperty Name="Timbre" ColumnName="Timbre" />
                <ScalarProperty Name="CodeFournisseur" ColumnName="CodeFournisseur" />
                <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
                <ScalarProperty Name="Note" ColumnName="Note" />
                <ScalarProperty Name="NumeroBL_Facture" ColumnName="NumeroBL/Facture" />
                <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
                <ScalarProperty Name="DateBlFacture" ColumnName="DateBlFacture" />
                <ScalarProperty Name="ValeurVenteTTC" ColumnName="ValeurVenteTTC" />
                <ScalarProperty Name="Autre" ColumnName="Autre" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ACHAT_DETAILS">
            <EntityTypeMapping TypeName="BusinessManagementModel.ACHAT_DETAILS">
              <MappingFragment StoreEntitySet="ACHAT_DETAILS">
                <ScalarProperty Name="QteGratuite" ColumnName="QteGratuite" />
                <ScalarProperty Name="CodePCT" ColumnName="CodePCT" />
                <ScalarProperty Name="QuantiteUnitaire" ColumnName="QuantiteUnitaire" />
                <ScalarProperty Name="DatePeremption" ColumnName="DatePeremption" />
                <ScalarProperty Name="TVA" ColumnName="TVA" />
                <ScalarProperty Name="Remise" ColumnName="Remise" />
                <ScalarProperty Name="PrixVenteTTC" ColumnName="PrixVenteTTC" />
                <ScalarProperty Name="TotalAchatHT" ColumnName="TotalAchatHT" />
                <ScalarProperty Name="PrixAchatHT" ColumnName="PrixAchatHT" />
                <ScalarProperty Name="Stock" ColumnName="Stock" />
                <ScalarProperty Name="Qte" ColumnName="Qte" />
                <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
                <ScalarProperty Name="Ordre" ColumnName="Ordre" />
                <ScalarProperty Name="NumeroLotArticle" ColumnName="NumeroLotArticle" />
                <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
                <ScalarProperty Name="NumeroAchat" ColumnName="NumeroAchat" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="AVOIRACHATs">
            <EntityTypeMapping TypeName="BusinessManagementModel.AVOIRACHAT">
              <MappingFragment StoreEntitySet="AVOIRACHAT">
                <ScalarProperty Name="Autre" ColumnName="Autre" />
                <ScalarProperty Name="ValeurVenteTTC" ColumnName="ValeurVenteTTC" />
                <ScalarProperty Name="DateBlFacture" ColumnName="DateBlFacture" />
                <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
                <ScalarProperty Name="NumeroBL_Facture" ColumnName="NumeroBL/Facture" />
                <ScalarProperty Name="Note" ColumnName="Note" />
                <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
                <ScalarProperty Name="CodeFournisseur" ColumnName="CodeFournisseur" />
                <ScalarProperty Name="Timbre" ColumnName="Timbre" />
                <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="TVA" ColumnName="TVA" />
                <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="NumeroAvoirAchat" ColumnName="NumeroAvoirAchat" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="AVOIRACHAT_DETAILS">
            <EntityTypeMapping TypeName="BusinessManagementModel.AVOIRACHAT_DETAILS">
              <MappingFragment StoreEntitySet="AVOIRACHAT_DETAILS">
                <ScalarProperty Name="TotalAchatHT" ColumnName="TotalAchatHT" />
                <ScalarProperty Name="PrixAchatHT" ColumnName="PrixAchatHT" />
                <ScalarProperty Name="QteGratuite" ColumnName="QteGratuite" />
                <ScalarProperty Name="CodePCT" ColumnName="CodePCT" />
                <ScalarProperty Name="QuantiteUnitaire" ColumnName="QuantiteUnitaire" />
                <ScalarProperty Name="DatePeremption" ColumnName="DatePeremption" />
                <ScalarProperty Name="TVA" ColumnName="TVA" />
                <ScalarProperty Name="Remise" ColumnName="Remise" />
                <ScalarProperty Name="PrixVenteTTC" ColumnName="PrixVenteTTC" />
                <ScalarProperty Name="Stock" ColumnName="Stock" />
                <ScalarProperty Name="Qte" ColumnName="Qte" />
                <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
                <ScalarProperty Name="Ordre" ColumnName="Ordre" />
                <ScalarProperty Name="NumeroLotArticle" ColumnName="NumeroLotArticle" />
                <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
                <ScalarProperty Name="NumeroAvoirAchat" ColumnName="NumeroAvoirAchat" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="True" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="True" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="Aucun" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>