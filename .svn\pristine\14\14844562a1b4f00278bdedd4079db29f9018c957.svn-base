﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Public Class fAlimentationCaisse

    Dim cmdListe As New SqlCommand
    Dim cbListe As New SqlCommandBuilder
    Dim dsListe As New DataSet
    Dim daListe As New SqlDataAdapter

    Dim cmd As New SqlCommand

    Public StrSQL As String = ""

    Dim i As Integer = 0

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub
    Public Sub init()
        Dim i As Integer = 0
        'verifier les nouveaux postes 

        If (dsListe.Tables.IndexOf("POSTE_ABSENT") > -1) Then
            dsListe.Tables("POSTE_ABSENT").Clear()
        End If

        StrSQL = "select LibellePoste " + _
                 "FROM POSTE " + _
                 "WHERE LibellePoste NOT IN (SELECT POSTE FROM CAISSE ) "

        cmdListe.Connection = ConnectionServeur
        cmdListe.CommandText = StrSQL
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "POSTE_ABSENT")
        cbListe = New SqlCommandBuilder(daListe)

        If dsListe.Tables("POSTE_ABSENT").Rows.Count - 1 > 0 Then
            For i = 0 To dsListe.Tables("POSTE_ABSENT").Rows.Count - 1
                cmd.Connection = ConnectionServeur
                cmd.CommandText = "INSERT INTO CAISSE (""Poste"",""MontantCaisse"") VALUES ('" + _
                                  dsListe.Tables("POSTE_ABSENT").Rows(i).Item("LibellePoste") + "',0)"
                Try
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
            Next
        End If

        AfficherCaisse()

    End Sub

    Public Sub AfficherCaisse()
        Dim i As Integer = 0
        If (dsListe.Tables.IndexOf("CAISSE") > -1) Then
            dsListe.Tables("CAISSE").Clear()
        End If

        StrSQL = "select Poste," + _
                 "MontantCaisse " + _
                 "FROM CAISSE "

        cmdListe.Connection = ConnectionServeur
        cmdListe.CommandText = StrSQL
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "CAISSE")
        cbListe = New SqlCommandBuilder(daListe)

        With gListe
            .Columns.Clear()
            Try
                .DataSource = dsListe
            Catch ex As Exception
            End Try
            .DataMember = "CAISSE"
            .Rebind(False)
            .Columns("Poste").Caption = "Poste"
            .Columns("MontantCaisse").Caption = "Montant Caisse"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("Poste").Width = 300
            .Splits(0).DisplayColumns("MontantCaisse").Width = 80

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gListe)
        End With
    End Sub
    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub bAlimenter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAlimenter.Click
        gListe.Splits(0).DisplayColumns("MontantCaisse").Locked = False
        gListe.MarqueeStyle = MarqueeEnum.DottedCellBorder
        bAlimenter.Enabled = False
    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click

        '----------------------------------- enregistrement des Caisses 

        Try
            daListe.Update(dsListe, "CAISSE")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsListe.Reset()
            Me.init()
        End Try
        gListe.Splits(0).DisplayColumns("MontantCaisse").Locked = True
        gListe.MarqueeStyle = MarqueeEnum.HighlightRow
        bAlimenter.Enabled = True
    End Sub
End Class