﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fParametresPoste
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fParametresPoste))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bAjouterPoste = New C1.Win.C1Input.C1Button()
        Me.cmbPoste = New C1.Win.C1List.C1Combo()
        Me.Label30 = New System.Windows.Forms.Label()
        Me.Label27 = New System.Windows.Forms.Label()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.bConfirmer = New C1.Win.C1Input.C1Button()
        Me.Tab = New C1.Win.C1Command.C1DockingTab()
        Me.C1DockingTabPage2 = New C1.Win.C1Command.C1DockingTabPage()
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations = New System.Windows.Forms.CheckBox()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.tLecteurUpdate = New C1.Win.C1Input.C1TextBox()
        Me.C1DockingTabPage3 = New C1.Win.C1Command.C1DockingTabPage()
        Me.chbTableauB = New System.Windows.Forms.CheckBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.tComAfficheurClient = New C1.Win.C1Input.C1TextBox()
        Me.chbAfficheurClient = New System.Windows.Forms.CheckBox()
        Me.chbScanner = New System.Windows.Forms.CheckBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.tNomDeLImprimante = New C1.Win.C1Input.C1TextBox()
        Me.chbInterdireChoisirParDesignation = New System.Windows.Forms.CheckBox()
        Me.chbInterdireLaVenteDesPerimes = New System.Windows.Forms.CheckBox()
        Me.chbInscriptionSurOrdonnancierAutomatique = New System.Windows.Forms.CheckBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.tTentatives = New C1.Win.C1Input.C1TextBox()
        Me.lMatricule = New System.Windows.Forms.Label()
        Me.tCom = New C1.Win.C1Input.C1TextBox()
        Me.chbUSB = New System.Windows.Forms.CheckBox()
        Me.chbImprimerBon = New System.Windows.Forms.CheckBox()
        Me.chbTiroir = New System.Windows.Forms.CheckBox()
        Me.chbImprimanteATicket = New System.Windows.Forms.CheckBox()
        Me.chbControleNombreUnites = New System.Windows.Forms.CheckBox()
        Me.chbValiderQteEgalA1 = New System.Windows.Forms.CheckBox()
        Me.C1DockingTabPage1 = New C1.Win.C1Command.C1DockingTabPage()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.tLecteurSauvegardeDistant = New C1.Win.C1Input.C1TextBox()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.tDelaiSauvegarde = New C1.Win.C1Input.C1TextBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.tLecteurSauvegarde = New C1.Win.C1Input.C1TextBox()
        Me.Panel.SuspendLayout()
        CType(Me.cmbPoste, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Tab, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Tab.SuspendLayout()
        Me.C1DockingTabPage2.SuspendLayout()
        CType(Me.tLecteurUpdate, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage3.SuspendLayout()
        CType(Me.tComAfficheurClient, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNomDeLImprimante, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tTentatives, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCom, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage1.SuspendLayout()
        CType(Me.tLecteurSauvegardeDistant, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDelaiSauvegarde, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tLecteurSauvegarde, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.bAjouterPoste)
        Me.Panel.Controls.Add(Me.cmbPoste)
        Me.Panel.Controls.Add(Me.Label30)
        Me.Panel.Controls.Add(Me.Label27)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.bConfirmer)
        Me.Panel.Controls.Add(Me.Tab)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(745, 489)
        Me.Panel.TabIndex = 17
        '
        'bAjouterPoste
        '
        Me.bAjouterPoste.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouterPoste.Location = New System.Drawing.Point(620, 442)
        Me.bAjouterPoste.Name = "bAjouterPoste"
        Me.bAjouterPoste.Size = New System.Drawing.Size(112, 33)
        Me.bAjouterPoste.TabIndex = 4
        Me.bAjouterPoste.Text = "Ajouter poste"
        Me.bAjouterPoste.UseVisualStyleBackColor = True
        Me.bAjouterPoste.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbPoste
        '
        Me.cmbPoste.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbPoste.Caption = ""
        Me.cmbPoste.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbPoste.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbPoste.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbPoste.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbPoste.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbPoste.Images.Add(CType(resources.GetObject("cmbPoste.Images"), System.Drawing.Image))
        Me.cmbPoste.Location = New System.Drawing.Point(615, 294)
        Me.cmbPoste.MatchEntryTimeout = CType(2000, Long)
        Me.cmbPoste.MaxDropDownItems = CType(5, Short)
        Me.cmbPoste.MaxLength = 32767
        Me.cmbPoste.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbPoste.Name = "cmbPoste"
        Me.cmbPoste.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbPoste.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbPoste.Size = New System.Drawing.Size(117, 22)
        Me.cmbPoste.TabIndex = 3
        Me.cmbPoste.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbPoste.PropBag = resources.GetString("cmbPoste.PropBag")
        '
        'Label30
        '
        Me.Label30.Font = New System.Drawing.Font("Microsoft Sans Serif", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label30.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label30.Location = New System.Drawing.Point(4, 8)
        Me.Label30.Name = "Label30"
        Me.Label30.Size = New System.Drawing.Size(732, 56)
        Me.Label30.TabIndex = 87
        Me.Label30.Text = "Paramètres du poste"
        Me.Label30.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label27
        '
        Me.Label27.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label27.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label27.Location = New System.Drawing.Point(653, 264)
        Me.Label27.Name = "Label27"
        Me.Label27.Size = New System.Drawing.Size(42, 13)
        Me.Label27.TabIndex = 2
        Me.Label27.Text = "Poste"
        Me.Label27.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bAnnuler
        '
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler21
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(624, 132)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(112, 45)
        Me.bAnnuler.TabIndex = 1
        Me.bAnnuler.Text = "Annuler"
        Me.bAnnuler.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bConfirmer
        '
        Me.bConfirmer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmer.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bConfirmer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmer.Location = New System.Drawing.Point(624, 80)
        Me.bConfirmer.Name = "bConfirmer"
        Me.bConfirmer.Size = New System.Drawing.Size(112, 45)
        Me.bConfirmer.TabIndex = 0
        Me.bConfirmer.Text = "Confirmer"
        Me.bConfirmer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bConfirmer.UseVisualStyleBackColor = True
        Me.bConfirmer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Tab
        '
        Me.Tab.Controls.Add(Me.C1DockingTabPage2)
        Me.Tab.Controls.Add(Me.C1DockingTabPage3)
        Me.Tab.Controls.Add(Me.C1DockingTabPage1)
        Me.Tab.Location = New System.Drawing.Point(3, 81)
        Me.Tab.Name = "Tab"
        Me.Tab.SelectedIndex = 2
        Me.Tab.Size = New System.Drawing.Size(604, 395)
        Me.Tab.TabIndex = 0
        Me.Tab.TabsSpacing = 5
        Me.Tab.TabStyle = C1.Win.C1Command.TabStyleEnum.Office2007
        Me.Tab.VisualStyle = C1.Win.C1Command.VisualStyle.Office2007Blue
        Me.Tab.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage2
        '
        Me.C1DockingTabPage2.Controls.Add(Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations)
        Me.C1DockingTabPage2.Controls.Add(Me.Label5)
        Me.C1DockingTabPage2.Controls.Add(Me.tLecteurUpdate)
        Me.C1DockingTabPage2.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage2.Name = "C1DockingTabPage2"
        Me.C1DockingTabPage2.Size = New System.Drawing.Size(602, 370)
        Me.C1DockingTabPage2.TabIndex = 1
        Me.C1DockingTabPage2.Text = "Article"
        '
        'chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations
        '
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.BackColor = System.Drawing.Color.Transparent
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.Location = New System.Drawing.Point(11, 45)
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.Name = "chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations"
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.Size = New System.Drawing.Size(406, 19)
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.TabIndex = 1
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.Text = "Autoriser le changement automatique des prix des articles des préparations"
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.UseVisualStyleBackColor = False
        '
        'Label5
        '
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label5.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label5.Location = New System.Drawing.Point(8, 19)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(87, 18)
        Me.Label5.TabIndex = 66
        Me.Label5.Text = "Lecteur Update :"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tLecteurUpdate
        '
        Me.tLecteurUpdate.AutoSize = False
        Me.tLecteurUpdate.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tLecteurUpdate.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tLecteurUpdate.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tLecteurUpdate.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tLecteurUpdate.Location = New System.Drawing.Point(98, 20)
        Me.tLecteurUpdate.Name = "tLecteurUpdate"
        Me.tLecteurUpdate.Size = New System.Drawing.Size(492, 19)
        Me.tLecteurUpdate.TabIndex = 2
        Me.tLecteurUpdate.Tag = Nothing
        Me.tLecteurUpdate.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage3
        '
        Me.C1DockingTabPage3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.C1DockingTabPage3.Controls.Add(Me.chbTableauB)
        Me.C1DockingTabPage3.Controls.Add(Me.Label4)
        Me.C1DockingTabPage3.Controls.Add(Me.tComAfficheurClient)
        Me.C1DockingTabPage3.Controls.Add(Me.chbAfficheurClient)
        Me.C1DockingTabPage3.Controls.Add(Me.chbScanner)
        Me.C1DockingTabPage3.Controls.Add(Me.Label2)
        Me.C1DockingTabPage3.Controls.Add(Me.tNomDeLImprimante)
        Me.C1DockingTabPage3.Controls.Add(Me.chbInterdireChoisirParDesignation)
        Me.C1DockingTabPage3.Controls.Add(Me.chbInterdireLaVenteDesPerimes)
        Me.C1DockingTabPage3.Controls.Add(Me.chbInscriptionSurOrdonnancierAutomatique)
        Me.C1DockingTabPage3.Controls.Add(Me.Label1)
        Me.C1DockingTabPage3.Controls.Add(Me.tTentatives)
        Me.C1DockingTabPage3.Controls.Add(Me.lMatricule)
        Me.C1DockingTabPage3.Controls.Add(Me.tCom)
        Me.C1DockingTabPage3.Controls.Add(Me.chbUSB)
        Me.C1DockingTabPage3.Controls.Add(Me.chbImprimerBon)
        Me.C1DockingTabPage3.Controls.Add(Me.chbTiroir)
        Me.C1DockingTabPage3.Controls.Add(Me.chbImprimanteATicket)
        Me.C1DockingTabPage3.Controls.Add(Me.chbControleNombreUnites)
        Me.C1DockingTabPage3.Controls.Add(Me.chbValiderQteEgalA1)
        Me.C1DockingTabPage3.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage3.Name = "C1DockingTabPage3"
        Me.C1DockingTabPage3.Size = New System.Drawing.Size(602, 370)
        Me.C1DockingTabPage3.TabIndex = 2
        Me.C1DockingTabPage3.Text = "Vente"
        '
        'chbTableauB
        '
        Me.chbTableauB.BackColor = System.Drawing.Color.Transparent
        Me.chbTableauB.ForeColor = System.Drawing.Color.Black
        Me.chbTableauB.Location = New System.Drawing.Point(276, 136)
        Me.chbTableauB.Name = "chbTableauB"
        Me.chbTableauB.Size = New System.Drawing.Size(75, 19)
        Me.chbTableauB.TabIndex = 19
        Me.chbTableauB.Text = "Tableau B"
        Me.chbTableauB.UseVisualStyleBackColor = False
        '
        'Label4
        '
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label4.ForeColor = System.Drawing.Color.Black
        Me.Label4.Location = New System.Drawing.Point(131, 233)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(43, 13)
        Me.Label4.TabIndex = 9
        Me.Label4.Text = "COM :"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tComAfficheurClient
        '
        Me.tComAfficheurClient.AutoSize = False
        Me.tComAfficheurClient.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tComAfficheurClient.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tComAfficheurClient.Font = New System.Drawing.Font("Calibri", 8.25!)
        Me.tComAfficheurClient.Location = New System.Drawing.Point(175, 230)
        Me.tComAfficheurClient.Name = "tComAfficheurClient"
        Me.tComAfficheurClient.Size = New System.Drawing.Size(50, 19)
        Me.tComAfficheurClient.TabIndex = 10
        Me.tComAfficheurClient.Tag = Nothing
        Me.tComAfficheurClient.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'chbAfficheurClient
        '
        Me.chbAfficheurClient.BackColor = System.Drawing.Color.Transparent
        Me.chbAfficheurClient.ForeColor = System.Drawing.Color.Black
        Me.chbAfficheurClient.Location = New System.Drawing.Point(8, 231)
        Me.chbAfficheurClient.Name = "chbAfficheurClient"
        Me.chbAfficheurClient.Size = New System.Drawing.Size(117, 19)
        Me.chbAfficheurClient.TabIndex = 8
        Me.chbAfficheurClient.Text = "Afficheur Client"
        Me.chbAfficheurClient.UseVisualStyleBackColor = False
        '
        'chbScanner
        '
        Me.chbScanner.BackColor = System.Drawing.Color.Transparent
        Me.chbScanner.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbScanner.Location = New System.Drawing.Point(8, 207)
        Me.chbScanner.Name = "chbScanner"
        Me.chbScanner.Size = New System.Drawing.Size(406, 19)
        Me.chbScanner.TabIndex = 7
        Me.chbScanner.Text = "Scanner l'ordonnance médicale et capturer la photo client lors d'une vente"
        Me.chbScanner.UseVisualStyleBackColor = False
        '
        'Label2
        '
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label2.Location = New System.Drawing.Point(248, 23)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(143, 13)
        Me.Label2.TabIndex = 12
        Me.Label2.Text = "Nom de l'imprimante  :"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tNomDeLImprimante
        '
        Me.tNomDeLImprimante.AutoSize = False
        Me.tNomDeLImprimante.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNomDeLImprimante.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNomDeLImprimante.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNomDeLImprimante.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tNomDeLImprimante.Location = New System.Drawing.Point(397, 21)
        Me.tNomDeLImprimante.Name = "tNomDeLImprimante"
        Me.tNomDeLImprimante.Size = New System.Drawing.Size(109, 19)
        Me.tNomDeLImprimante.TabIndex = 13
        Me.tNomDeLImprimante.Tag = Nothing
        Me.tNomDeLImprimante.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'chbInterdireChoisirParDesignation
        '
        Me.chbInterdireChoisirParDesignation.BackColor = System.Drawing.Color.Transparent
        Me.chbInterdireChoisirParDesignation.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbInterdireChoisirParDesignation.Location = New System.Drawing.Point(8, 182)
        Me.chbInterdireChoisirParDesignation.Name = "chbInterdireChoisirParDesignation"
        Me.chbInterdireChoisirParDesignation.Size = New System.Drawing.Size(406, 19)
        Me.chbInterdireChoisirParDesignation.TabIndex = 6
        Me.chbInterdireChoisirParDesignation.Text = "Interdire de choisir l'article par désignation lors d'une vente"
        Me.chbInterdireChoisirParDesignation.UseVisualStyleBackColor = False
        '
        'chbInterdireLaVenteDesPerimes
        '
        Me.chbInterdireLaVenteDesPerimes.BackColor = System.Drawing.Color.Transparent
        Me.chbInterdireLaVenteDesPerimes.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbInterdireLaVenteDesPerimes.Location = New System.Drawing.Point(8, 158)
        Me.chbInterdireLaVenteDesPerimes.Name = "chbInterdireLaVenteDesPerimes"
        Me.chbInterdireLaVenteDesPerimes.Size = New System.Drawing.Size(406, 19)
        Me.chbInterdireLaVenteDesPerimes.TabIndex = 5
        Me.chbInterdireLaVenteDesPerimes.Text = "Interdire la vente des produits périmés"
        Me.chbInterdireLaVenteDesPerimes.UseVisualStyleBackColor = False
        '
        'chbInscriptionSurOrdonnancierAutomatique
        '
        Me.chbInscriptionSurOrdonnancierAutomatique.BackColor = System.Drawing.Color.Transparent
        Me.chbInscriptionSurOrdonnancierAutomatique.ForeColor = System.Drawing.Color.Black
        Me.chbInscriptionSurOrdonnancierAutomatique.Location = New System.Drawing.Point(8, 136)
        Me.chbInscriptionSurOrdonnancierAutomatique.Name = "chbInscriptionSurOrdonnancierAutomatique"
        Me.chbInscriptionSurOrdonnancierAutomatique.Size = New System.Drawing.Size(406, 19)
        Me.chbInscriptionSurOrdonnancierAutomatique.TabIndex = 4
        Me.chbInscriptionSurOrdonnancierAutomatique.Text = "Inscription sur ordonnancier Automatique"
        Me.chbInscriptionSurOrdonnancierAutomatique.UseVisualStyleBackColor = False
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label1.Location = New System.Drawing.Point(273, 116)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(63, 13)
        Me.Label1.TabIndex = 16
        Me.Label1.Text = "Tentatives :"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tTentatives
        '
        Me.tTentatives.AutoSize = False
        Me.tTentatives.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTentatives.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tTentatives.Font = New System.Drawing.Font("Calibri", 8.25!)
        Me.tTentatives.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tTentatives.Location = New System.Drawing.Point(341, 113)
        Me.tTentatives.Name = "tTentatives"
        Me.tTentatives.Size = New System.Drawing.Size(50, 19)
        Me.tTentatives.TabIndex = 17
        Me.tTentatives.Tag = Nothing
        Me.tTentatives.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lMatricule
        '
        Me.lMatricule.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.lMatricule.ForeColor = System.Drawing.Color.Black
        Me.lMatricule.Location = New System.Drawing.Point(283, 50)
        Me.lMatricule.Name = "lMatricule"
        Me.lMatricule.Size = New System.Drawing.Size(43, 13)
        Me.lMatricule.TabIndex = 14
        Me.lMatricule.Text = "COM :"
        Me.lMatricule.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tCom
        '
        Me.tCom.AutoSize = False
        Me.tCom.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCom.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tCom.Font = New System.Drawing.Font("Calibri", 8.25!)
        Me.tCom.Location = New System.Drawing.Point(327, 47)
        Me.tCom.Name = "tCom"
        Me.tCom.Size = New System.Drawing.Size(50, 19)
        Me.tCom.TabIndex = 15
        Me.tCom.Tag = Nothing
        Me.tCom.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'chbUSB
        '
        Me.chbUSB.BackColor = System.Drawing.Color.Transparent
        Me.chbUSB.ForeColor = System.Drawing.Color.Black
        Me.chbUSB.Location = New System.Drawing.Point(174, 46)
        Me.chbUSB.Name = "chbUSB"
        Me.chbUSB.Size = New System.Drawing.Size(88, 19)
        Me.chbUSB.TabIndex = 11
        Me.chbUSB.Text = "USB"
        Me.chbUSB.UseVisualStyleBackColor = False
        '
        'chbImprimerBon
        '
        Me.chbImprimerBon.BackColor = System.Drawing.Color.Transparent
        Me.chbImprimerBon.ForeColor = System.Drawing.Color.Black
        Me.chbImprimerBon.Location = New System.Drawing.Point(8, 64)
        Me.chbImprimerBon.Name = "chbImprimerBon"
        Me.chbImprimerBon.Size = New System.Drawing.Size(210, 24)
        Me.chbImprimerBon.TabIndex = 1
        Me.chbImprimerBon.Text = "Imprimer les bons"
        Me.chbImprimerBon.UseVisualStyleBackColor = False
        '
        'chbTiroir
        '
        Me.chbTiroir.BackColor = System.Drawing.Color.Transparent
        Me.chbTiroir.ForeColor = System.Drawing.Color.Black
        Me.chbTiroir.Location = New System.Drawing.Point(8, 46)
        Me.chbTiroir.Name = "chbTiroir"
        Me.chbTiroir.Size = New System.Drawing.Size(88, 19)
        Me.chbTiroir.TabIndex = 0
        Me.chbTiroir.Text = "Tiroir"
        Me.chbTiroir.UseVisualStyleBackColor = False
        '
        'chbImprimanteATicket
        '
        Me.chbImprimanteATicket.BackColor = System.Drawing.Color.Transparent
        Me.chbImprimanteATicket.ForeColor = System.Drawing.Color.Black
        Me.chbImprimanteATicket.Location = New System.Drawing.Point(8, 21)
        Me.chbImprimanteATicket.Name = "chbImprimanteATicket"
        Me.chbImprimanteATicket.Size = New System.Drawing.Size(428, 24)
        Me.chbImprimanteATicket.TabIndex = 18
        Me.chbImprimanteATicket.Text = "Imprimante à ticket"
        Me.chbImprimanteATicket.UseVisualStyleBackColor = False
        '
        'chbControleNombreUnites
        '
        Me.chbControleNombreUnites.BackColor = System.Drawing.Color.Transparent
        Me.chbControleNombreUnites.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbControleNombreUnites.Location = New System.Drawing.Point(8, 113)
        Me.chbControleNombreUnites.Name = "chbControleNombreUnites"
        Me.chbControleNombreUnites.Size = New System.Drawing.Size(232, 19)
        Me.chbControleNombreUnites.TabIndex = 3
        Me.chbControleNombreUnites.Text = "Contrôle du nombre des unités vendues"
        Me.chbControleNombreUnites.UseVisualStyleBackColor = False
        '
        'chbValiderQteEgalA1
        '
        Me.chbValiderQteEgalA1.BackColor = System.Drawing.Color.Transparent
        Me.chbValiderQteEgalA1.Location = New System.Drawing.Point(8, 87)
        Me.chbValiderQteEgalA1.Name = "chbValiderQteEgalA1"
        Me.chbValiderQteEgalA1.Size = New System.Drawing.Size(232, 24)
        Me.chbValiderQteEgalA1.TabIndex = 2
        Me.chbValiderQteEgalA1.Text = "Validation automatique d'une quantité de 1"
        Me.chbValiderQteEgalA1.UseVisualStyleBackColor = False
        '
        'C1DockingTabPage1
        '
        Me.C1DockingTabPage1.Controls.Add(Me.Label7)
        Me.C1DockingTabPage1.Controls.Add(Me.tLecteurSauvegardeDistant)
        Me.C1DockingTabPage1.Controls.Add(Me.Label6)
        Me.C1DockingTabPage1.Controls.Add(Me.tDelaiSauvegarde)
        Me.C1DockingTabPage1.Controls.Add(Me.Label3)
        Me.C1DockingTabPage1.Controls.Add(Me.tLecteurSauvegarde)
        Me.C1DockingTabPage1.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage1.Name = "C1DockingTabPage1"
        Me.C1DockingTabPage1.Size = New System.Drawing.Size(602, 370)
        Me.C1DockingTabPage1.TabIndex = 3
        Me.C1DockingTabPage1.Text = "Sauvegarde"
        '
        'Label7
        '
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label7.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label7.Location = New System.Drawing.Point(8, 45)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(223, 15)
        Me.Label7.TabIndex = 80
        Me.Label7.Text = "Lecteur de sauvegarde distant :"
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tLecteurSauvegardeDistant
        '
        Me.tLecteurSauvegardeDistant.AutoSize = False
        Me.tLecteurSauvegardeDistant.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tLecteurSauvegardeDistant.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tLecteurSauvegardeDistant.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tLecteurSauvegardeDistant.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tLecteurSauvegardeDistant.Location = New System.Drawing.Point(248, 45)
        Me.tLecteurSauvegardeDistant.Name = "tLecteurSauvegardeDistant"
        Me.tLecteurSauvegardeDistant.Size = New System.Drawing.Size(341, 19)
        Me.tLecteurSauvegardeDistant.TabIndex = 79
        Me.tLecteurSauvegardeDistant.Tag = Nothing
        Me.tLecteurSauvegardeDistant.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label6
        '
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label6.ForeColor = System.Drawing.Color.Black
        Me.Label6.Location = New System.Drawing.Point(8, 71)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(238, 20)
        Me.Label6.TabIndex = 78
        Me.Label6.Text = "Délai de sauvegarde automatique (en minutes) :"
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tDelaiSauvegarde
        '
        Me.tDelaiSauvegarde.AutoSize = False
        Me.tDelaiSauvegarde.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDelaiSauvegarde.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tDelaiSauvegarde.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tDelaiSauvegarde.Location = New System.Drawing.Point(248, 73)
        Me.tDelaiSauvegarde.Name = "tDelaiSauvegarde"
        Me.tDelaiSauvegarde.Size = New System.Drawing.Size(91, 19)
        Me.tDelaiSauvegarde.TabIndex = 0
        Me.tDelaiSauvegarde.Tag = Nothing
        Me.tDelaiSauvegarde.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label3
        '
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label3.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label3.Location = New System.Drawing.Point(8, 22)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(123, 13)
        Me.Label3.TabIndex = 76
        Me.Label3.Text = "Lecteur de sauvegarde :"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tLecteurSauvegarde
        '
        Me.tLecteurSauvegarde.AutoSize = False
        Me.tLecteurSauvegarde.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tLecteurSauvegarde.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tLecteurSauvegarde.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tLecteurSauvegarde.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tLecteurSauvegarde.Location = New System.Drawing.Point(248, 20)
        Me.tLecteurSauvegarde.Name = "tLecteurSauvegarde"
        Me.tLecteurSauvegarde.Size = New System.Drawing.Size(341, 19)
        Me.tLecteurSauvegarde.TabIndex = 75
        Me.tLecteurSauvegarde.Tag = Nothing
        Me.tLecteurSauvegarde.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fParametresPoste
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(745, 489)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fParametresPoste"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        Me.Panel.PerformLayout()
        CType(Me.cmbPoste, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Tab, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Tab.ResumeLayout(False)
        Me.C1DockingTabPage2.ResumeLayout(False)
        CType(Me.tLecteurUpdate, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage3.ResumeLayout(False)
        CType(Me.tComAfficheurClient, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNomDeLImprimante, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tTentatives, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCom, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage1.ResumeLayout(False)
        CType(Me.tLecteurSauvegardeDistant, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDelaiSauvegarde, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tLecteurSauvegarde, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents Label30 As System.Windows.Forms.Label
    Friend WithEvents Label27 As System.Windows.Forms.Label
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bConfirmer As C1.Win.C1Input.C1Button
    Friend WithEvents Tab As C1.Win.C1Command.C1DockingTab
    Friend WithEvents C1DockingTabPage2 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents C1DockingTabPage3 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents chbInterdireChoisirParDesignation As System.Windows.Forms.CheckBox
    Friend WithEvents chbInterdireLaVenteDesPerimes As System.Windows.Forms.CheckBox
    Friend WithEvents chbInscriptionSurOrdonnancierAutomatique As System.Windows.Forms.CheckBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents tTentatives As C1.Win.C1Input.C1TextBox
    Friend WithEvents lMatricule As System.Windows.Forms.Label
    Friend WithEvents tCom As C1.Win.C1Input.C1TextBox
    Friend WithEvents chbUSB As System.Windows.Forms.CheckBox
    Friend WithEvents chbImprimerBon As System.Windows.Forms.CheckBox
    Friend WithEvents chbTiroir As System.Windows.Forms.CheckBox
    Friend WithEvents chbImprimanteATicket As System.Windows.Forms.CheckBox
    Friend WithEvents chbControleNombreUnites As System.Windows.Forms.CheckBox
    Friend WithEvents chbValiderQteEgalA1 As System.Windows.Forms.CheckBox
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents tLecteurUpdate As C1.Win.C1Input.C1TextBox
    Friend WithEvents chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations As System.Windows.Forms.CheckBox
    Friend WithEvents cmbPoste As C1.Win.C1List.C1Combo
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents tNomDeLImprimante As C1.Win.C1Input.C1TextBox
    Friend WithEvents bAjouterPoste As C1.Win.C1Input.C1Button
    Friend WithEvents chbScanner As System.Windows.Forms.CheckBox
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents tComAfficheurClient As C1.Win.C1Input.C1TextBox
    Friend WithEvents chbAfficheurClient As System.Windows.Forms.CheckBox
    Friend WithEvents C1DockingTabPage1 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents tLecteurSauvegarde As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents tDelaiSauvegarde As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents tLecteurSauvegardeDistant As C1.Win.C1Input.C1TextBox
    Friend WithEvents chbTableauB As System.Windows.Forms.CheckBox
End Class
