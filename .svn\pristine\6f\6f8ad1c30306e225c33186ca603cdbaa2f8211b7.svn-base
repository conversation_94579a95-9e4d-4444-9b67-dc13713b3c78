﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.IO
Public Class fHistoriqueDesActions
    Dim cmdListe As New SqlCommand
    Dim cbListe As New SqlCommandBuilder
    Dim dsListe As New DataSet
    Dim daListe As New SqlDataAdapter

    Dim cmdChargement As New SqlCommand
    Dim cbChargement As New SqlCommandBuilder
    Dim daChargement As New SqlDataAdapter

    Dim cmdOrdinateur As New SqlCommand
    Dim dsOrdinateur As New DataSet
    Dim daOrdinateur As New SqlDataAdapter

    Public StrSQL As String = ""

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub


    Private Sub initDate()
        dtpDebut.Text = Date.Today + " 00:00:00"
        dtpFin.Text = Date.Today + " 23:59:59"
        System.DateTime.Now.Date.ToString()
    End Sub

    Public Sub init()

        'chargement des opérateurs
        StrSQL = "SELECT DISTINCT CodeUtilisateur,Nom FROM UTILISATEUR ORDER BY Nom ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsListe, "OPERATEUR")
        cmbOperateur.DataSource = dsListe.Tables("OPERATEUR")
        cmbOperateur.ValueMember = "CodeUtilisateur"
        cmbOperateur.DisplayMember = "Nom"
        cmbOperateur.ColumnHeaders = False
        cmbOperateur.Splits(0).DisplayColumns("CodeUtilisateur").Visible = False
        cmbOperateur.Splits(0).DisplayColumns("Nom").Width = 10
        cmbOperateur.ExtendRightColumn = True

        'chargement des modules
        StrSQL = "SELECT DISTINCT Module FROM LOG ORDER BY Module ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsListe, "MODULE")
        cmbModule.DataSource = dsListe.Tables("MODULE")
        cmbModule.ValueMember = "Module"
        cmbModule.DisplayMember = "Module"
        cmbModule.ColumnHeaders = False
        cmbModule.Splits(0).DisplayColumns("Module").Width = 10
        cmbModule.ExtendRightColumn = True

        'chargement des modules
        StrSQL = "SELECT DISTINCT NomPhysique FROM LOG ORDER BY NomPhysique ASC"
        cmdOrdinateur.Connection = ConnectionServeur
        cmdOrdinateur.CommandText = StrSQL
        daOrdinateur = New SqlDataAdapter(cmdOrdinateur)
        daOrdinateur.Fill(dsOrdinateur, "NomPhysique")
        cmbOrdinateur.DataSource = dsOrdinateur.Tables("NomPhysique")
        cmbOrdinateur.ValueMember = "NomPhysique"
        cmbOrdinateur.DisplayMember = "NomPhysique"
        cmbOrdinateur.ColumnHeaders = False
        cmbOrdinateur.Splits(0).DisplayColumns("NomPhysique").Width = 15
        cmbOrdinateur.ExtendRightColumn = True

        initDate()
        AfficherHistorique()

    End Sub

    Public Sub AfficherHistorique()

        Dim i As Integer = 0
        Dim Cond As String = " [DateTime] BETWEEN '" + dtpDebut.Value + "' AND '" + dtpFin.Value + "'"

        If (dsListe.Tables.IndexOf("LISTE_HISTORIQUE") > -1) Then
            dsListe.Tables("LISTE_HISTORIQUE").Clear()
        End If

        If cmbOperateur.Text <> "" And cmbOperateur.SelectedValue <> Nothing Then
            Cond += " AND [LOG].CodePersonnel='" + cmbOperateur.SelectedValue.ToString + "' "
        End If

        If cmbModule.Text <> "" Then
            Cond += " AND [LOG].Module='" + cmbModule.Text + "' "
        End If

        If cmbOrdinateur.Text <> "" Then
            Cond += " AND [LOG].NomPhysique='" + cmbOrdinateur.Text + "' "
        End If

        If tMotCle.Text <> "" Then
            Cond += " AND LOG.Libelle like '%" + tMotCle.Text + "%'"
        End If
       

        StrSQL = "select Libelle," + _
                 "Nom," + _
                 "[DateTime], " + _
                 "Module," + _
                 "NomLogique, NomPhysique " + _
                 "FROM [LOG] LEFT OUTER JOIN UTILISATEUR ON " + _
                 "[LOG].CodePersonnel=UTILISATEUR.CodeUtilisateur " + _
                 "WHERE " + Cond + "  ORDER BY [DateTime]"


            cmdListe.Connection = ConnectionServeur
            cmdListe.CommandText = StrSQL
            daListe = New SqlDataAdapter(cmdListe)
            daListe.Fill(dsListe, "LISTE_HISTORIQUE")
            cbListe = New SqlCommandBuilder(daListe)
       
        With gListe
            .Columns.Clear()
            Try
                .DataSource = dsListe
            Catch ex As Exception
            End Try
            .DataMember = "LISTE_HISTORIQUE"
            .Rebind(False)
            .Columns("Libelle").Caption = "Action"
            .Columns("Nom").Caption = "Nom Utilisateur"
            .Columns("DateTime").Caption = "Date/Heure"
            .Columns("DateTime").NumberFormat = "dd/MM/yyyy  HH:mm:ss"
            .Columns("Module").Caption = "Module"
            .Columns("NomLogique").Caption = "Poste"
            .Columns("NomPhysique").Caption = "Ordinateur"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Libelle").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Nom").Style.HorizontalAlignment = AlignHorzEnum.Near

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("Libelle").Width = 400
            .Splits(0).DisplayColumns("Nom").Width = 200
            .Splits(0).DisplayColumns("DateTime").Width = 150
            .Splits(0).DisplayColumns("Module").Width = 150
            .Splits(0).DisplayColumns("NomLogique").Width = 100

            .Splits(0).DisplayColumns("DateTime").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            '.AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gListe)
        End With
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSauvegarde.Click

        If gListe.RowCount < 0 Then
            Exit Sub
        End If

        Dim CondCrystal As String = "1=1"

        If cmbOperateur.Text <> "" And cmbOperateur.SelectedValue <> Nothing Then
            CondCrystal += " AND {Vue_HistoriqueDesActions.Nom} = '" & cmbOperateur.Text & "' "
        End If

        If cmbModule.Text <> "" And cmbModule.SelectedValue <> Nothing Then
            CondCrystal += " AND {Vue_HistoriqueDesActions.Module} = '" & cmbModule.Text & "' "
        End If

        If tMotCle.Text <> "" Then
            CondCrystal += " AND  {Vue_HistoriqueDesActions.Libelle}  like '%" + tMotCle.Text + "% ' "
        End If

        If dtpDate.Text <> "" Then
            CondCrystal += " AND  {Vue_HistoriqueDesActions.DateTime}  <= Date('" & dtpDate.Text & "')"
        End If


        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Historique Des Actions" Then
                num = I
            End If
        Next
        CR.FileName = Application.StartupPath + "\EtatHistoriqueDesActions.rpt"

        CR.SetParameterValue("pPharmacie", Pharmacie)
        CR.SetParameterValue("pHistoriqueAvant", dtpDate.Text)

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo


        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent

        CR.RecordSelectionFormula = CondCrystal
        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression Historique Des Actions"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If


        '----------------------------------------------------------------------------------------------------------
        'Dim I As Integer = 0

        'Dim cmd As New SqlCommand
        'If dtpDate.Text = "" Then
        '    MsgBox("Veuillez donnez une date ", MsgBoxStyle.Information, "Information")
        '    dtpDate.Focus()
        '    Exit Sub
        'End If

        'Dim FichierCNAM_APCI As New IO.StreamWriter("Cazerty.txt")

        'For I = 0 To dsListe.Tables("LISTE_HISTORIQUE").Rows.Count - 1
        '    If dsListe.Tables("LISTE_HISTORIQUE").Rows(I).Item("DateTime") < dtpDate.Text Then
        '        FichierCNAM_APCI.WriteLine(dsListe.Tables("LISTE_HISTORIQUE").Rows(I).Item("Libelle") + "  " + dsListe.Tables("LISTE_HISTORIQUE").Rows(I).Item("Nom") + "  " + dsListe.Tables("LISTE_HISTORIQUE").Rows(I).Item("DateTime") + "  " + dsListe.Tables("LISTE_HISTORIQUE").Rows(I).Item("Module") + "  " + dsListe.Tables("LISTE_HISTORIQUE").Rows(I).Item("NomLogique"))
        '    End If
        'Next

        'FichierCNAM_APCI.Close()

    End Sub
    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click

        Dim cmd As New SqlCommand

        If dtpDate.Text = "" Then
            MsgBox("Veuillez donnez une date ", MsgBoxStyle.Information, "Information")
            dtpDate.Focus()
            Exit Sub
        End If

        If MsgBox("Voulez vous vraiment supprimer l'historique enregistré avant le " + dtpDate.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
            '--------------- suppression 
            Try
                cmd.Connection = ConnectionServeur
                cmd.CommandText = "DELETE FROM LOG WHERE DateTime <='" + dtpDate.Text + "'"
                cmd.ExecuteNonQuery()
              
            Catch ex As Exception
                MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
            End Try
            'enregistrement dans le fichier LOG 
            InsertionDansLog("SAUVEGARDE_SUPPRESSION_LOG", "suppression des actions avant " + dtpDate.Text, CodeUtilisateur, System.DateTime.Now, "LOG", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
            'Else
            '    'enregistrement dans le fichier LOG 
            '    InsertionDansLog("SAUVEGARDE_LOG", "Sauvegarde des actions avant " + dtpDate.Text, CodeUtilisateur, System.DateTime.Now, "LOG", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
        End If
        init()

    End Sub

    Private Sub cmbOperateur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbOperateur.TextChanged
        AfficherHistorique()
    End Sub

    Private Sub cmbModule_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbModule.TextChanged
        AfficherHistorique()
    End Sub

    Private Sub tMotCle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tMotCle.TextChanged
        AfficherHistorique()
    End Sub

    Private Sub GroupBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GroupBox1.Enter

    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherHistorique()
        End If
    End Sub

    Private Sub cmbOrdinateur_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbOrdinateur.TextChanged
        AfficherHistorique()
    End Sub
End Class
