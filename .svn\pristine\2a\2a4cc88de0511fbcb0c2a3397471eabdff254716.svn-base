﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fChangemantDuFichierArticle
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fChangemantDuFichierArticle))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.gArticles = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.bOk = New C1.Win.C1Input.C1Button()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.cmbLaboNouveau = New C1.Win.C1List.C1Combo()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.tVenteHT = New C1.Win.C1Input.C1TextBox()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.tAchatHt = New C1.Win.C1Input.C1TextBox()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.cmbRayonNouveau = New C1.Win.C1List.C1Combo()
        Me.GroupBox5 = New System.Windows.Forms.GroupBox()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.tSection = New C1.Win.C1Input.C1TextBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.cmbCategorieNouveau = New C1.Win.C1List.C1Combo()
        Me.cmbFormeNouveau = New C1.Win.C1List.C1Combo()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.cmbLaboAncien = New C1.Win.C1List.C1Combo()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.GroupBox6 = New System.Windows.Forms.GroupBox()
        Me.rdbTousCodePCT = New System.Windows.Forms.RadioButton()
        Me.tCodePCT2 = New C1.Win.C1Input.C1TextBox()
        Me.tCodePCT1 = New C1.Win.C1Input.C1TextBox()
        Me.rdbIntervalleCodePCT = New System.Windows.Forms.RadioButton()
        Me.tDesignationCommencePar = New C1.Win.C1Input.C1TextBox()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.GroupeSection = New System.Windows.Forms.GroupBox()
        Me.cmbRayonAncien = New C1.Win.C1List.C1Combo()
        Me.rdbRayonRayonAncien = New System.Windows.Forms.RadioButton()
        Me.rdbTousRayonAncien = New System.Windows.Forms.RadioButton()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.rdbToutesSectionAncien = New System.Windows.Forms.RadioButton()
        Me.rdbIntervalleSectionAncien = New System.Windows.Forms.RadioButton()
        Me.tFinIntervalleAncien = New C1.Win.C1Input.C1TextBox()
        Me.tDebutIntervalleAncien = New C1.Win.C1Input.C1TextBox()
        Me.cmbCategorieAncien = New C1.Win.C1List.C1Combo()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.cmbFormeAncien = New C1.Win.C1List.C1Combo()
        Me.LVilleFournisseur = New System.Windows.Forms.Label()
        Me.Panel.SuspendLayout()
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.cmbLaboNouveau, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tVenteHT, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tAchatHt, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox4.SuspendLayout()
        CType(Me.cmbRayonNouveau, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox5.SuspendLayout()
        CType(Me.tSection, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbCategorieNouveau, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbFormeNouveau, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox2.SuspendLayout()
        CType(Me.cmbLaboAncien, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox6.SuspendLayout()
        CType(Me.tCodePCT2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodePCT1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDesignationCommencePar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupeSection.SuspendLayout()
        CType(Me.cmbRayonAncien, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox3.SuspendLayout()
        CType(Me.tFinIntervalleAncien, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tDebutIntervalleAncien, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbCategorieAncien, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbFormeAncien, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Panel.Controls.Add(Me.gArticles)
        Me.Panel.Controls.Add(Me.Label4)
        Me.Panel.Controls.Add(Me.Label5)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.bOk)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Controls.Add(Me.GroupBox2)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(874, 471)
        Me.Panel.TabIndex = 2
        '
        'gArticles
        '
        Me.gArticles.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gArticles.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gArticles.GroupByCaption = "Drag a column header here to group by that column"
        Me.gArticles.Images.Add(CType(resources.GetObject("gArticles.Images"), System.Drawing.Image))
        Me.gArticles.LinesPerRow = 2
        Me.gArticles.Location = New System.Drawing.Point(12, 304)
        Me.gArticles.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
        Me.gArticles.Name = "gArticles"
        Me.gArticles.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gArticles.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gArticles.PreviewInfo.ZoomFactor = 75.0R
        Me.gArticles.PrintInfo.PageSettings = CType(resources.GetObject("gArticles.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gArticles.Size = New System.Drawing.Size(850, 109)
        Me.gArticles.TabIndex = 49
        Me.gArticles.Text = "C1TrueDBGrid1"
        Me.gArticles.PropBag = resources.GetString("gArticles.PropBag")
        '
        'Label4
        '
        Me.Label4.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label4.Location = New System.Drawing.Point(519, 11)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(343, 22)
        Me.Label4.TabIndex = 48
        Me.Label4.Text = "NOUVELLES VALEURS"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label5
        '
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label5.Location = New System.Drawing.Point(12, 11)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(332, 22)
        Me.Label5.TabIndex = 47
        Me.Label5.Text = "ANCIENNES VALEURS"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bAnnuler.Location = New System.Drawing.Point(741, 419)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(121, 45)
        Me.bAnnuler.TabIndex = 19
        Me.bAnnuler.Text = "Annuler           F10"
        Me.bAnnuler.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bOk
        '
        Me.bOk.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bOk.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bOk.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bOk.Location = New System.Drawing.Point(637, 419)
        Me.bOk.Name = "bOk"
        Me.bOk.Size = New System.Drawing.Size(100, 45)
        Me.bOk.TabIndex = 18
        Me.bOk.Text = "OK                  F3"
        Me.bOk.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bOk.UseVisualStyleBackColor = True
        Me.bOk.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.Controls.Add(Me.cmbLaboNouveau)
        Me.GroupBox1.Controls.Add(Me.Label13)
        Me.GroupBox1.Controls.Add(Me.tVenteHT)
        Me.GroupBox1.Controls.Add(Me.Label8)
        Me.GroupBox1.Controls.Add(Me.tAchatHt)
        Me.GroupBox1.Controls.Add(Me.Label7)
        Me.GroupBox1.Controls.Add(Me.GroupBox4)
        Me.GroupBox1.Controls.Add(Me.GroupBox5)
        Me.GroupBox1.Controls.Add(Me.Label3)
        Me.GroupBox1.Controls.Add(Me.cmbCategorieNouveau)
        Me.GroupBox1.Controls.Add(Me.cmbFormeNouveau)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Location = New System.Drawing.Point(453, 36)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(409, 262)
        Me.GroupBox1.TabIndex = 13
        Me.GroupBox1.TabStop = False
        '
        'cmbLaboNouveau
        '
        Me.cmbLaboNouveau.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbLaboNouveau.Caption = ""
        Me.cmbLaboNouveau.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbLaboNouveau.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbLaboNouveau.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbLaboNouveau.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbLaboNouveau.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbLaboNouveau.Images.Add(CType(resources.GetObject("cmbLaboNouveau.Images"), System.Drawing.Image))
        Me.cmbLaboNouveau.Location = New System.Drawing.Point(63, 39)
        Me.cmbLaboNouveau.MatchEntryTimeout = CType(2000, Long)
        Me.cmbLaboNouveau.MaxDropDownItems = CType(5, Short)
        Me.cmbLaboNouveau.MaxLength = 32767
        Me.cmbLaboNouveau.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbLaboNouveau.Name = "cmbLaboNouveau"
        Me.cmbLaboNouveau.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbLaboNouveau.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbLaboNouveau.Size = New System.Drawing.Size(134, 22)
        Me.cmbLaboNouveau.TabIndex = 50
        Me.cmbLaboNouveau.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbLaboNouveau.PropBag = resources.GetString("cmbLaboNouveau.PropBag")
        '
        'Label13
        '
        Me.Label13.AutoSize = True
        Me.Label13.Location = New System.Drawing.Point(17, 44)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(37, 13)
        Me.Label13.TabIndex = 51
        Me.Label13.Text = "Labo :"
        Me.Label13.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tVenteHT
        '
        Me.tVenteHT.AutoSize = False
        Me.tVenteHT.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tVenteHT.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tVenteHT.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tVenteHT.Location = New System.Drawing.Point(99, 231)
        Me.tVenteHT.Name = "tVenteHT"
        Me.tVenteHT.Size = New System.Drawing.Size(217, 20)
        Me.tVenteHT.TabIndex = 53
        Me.tVenteHT.Tag = Nothing
        Me.tVenteHT.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tVenteHT.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Location = New System.Drawing.Point(15, 235)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(79, 13)
        Me.Label8.TabIndex = 52
        Me.Label8.Text = "Prix Vente HT :"
        Me.Label8.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tAchatHt
        '
        Me.tAchatHt.AutoSize = False
        Me.tAchatHt.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tAchatHt.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tAchatHt.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tAchatHt.Location = New System.Drawing.Point(99, 208)
        Me.tAchatHt.Name = "tAchatHt"
        Me.tAchatHt.Size = New System.Drawing.Size(217, 20)
        Me.tAchatHt.TabIndex = 49
        Me.tAchatHt.Tag = Nothing
        Me.tAchatHt.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tAchatHt.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Location = New System.Drawing.Point(15, 212)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(79, 13)
        Me.Label7.TabIndex = 48
        Me.Label7.Text = "Prix Achat HT :"
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.Label10)
        Me.GroupBox4.Controls.Add(Me.cmbRayonNouveau)
        Me.GroupBox4.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupBox4.Location = New System.Drawing.Point(17, 132)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(299, 70)
        Me.GroupBox4.TabIndex = 50
        Me.GroupBox4.TabStop = False
        Me.GroupBox4.Text = "Paramètres de rayon"
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Location = New System.Drawing.Point(19, 36)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(44, 13)
        Me.Label10.TabIndex = 49
        Me.Label10.Text = "Rayon :"
        Me.Label10.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'cmbRayonNouveau
        '
        Me.cmbRayonNouveau.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbRayonNouveau.Caption = ""
        Me.cmbRayonNouveau.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbRayonNouveau.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbRayonNouveau.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbRayonNouveau.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbRayonNouveau.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbRayonNouveau.Images.Add(CType(resources.GetObject("cmbRayonNouveau.Images"), System.Drawing.Image))
        Me.cmbRayonNouveau.Location = New System.Drawing.Point(68, 31)
        Me.cmbRayonNouveau.MatchEntryTimeout = CType(2000, Long)
        Me.cmbRayonNouveau.MaxDropDownItems = CType(5, Short)
        Me.cmbRayonNouveau.MaxLength = 32767
        Me.cmbRayonNouveau.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbRayonNouveau.Name = "cmbRayonNouveau"
        Me.cmbRayonNouveau.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbRayonNouveau.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbRayonNouveau.Size = New System.Drawing.Size(222, 22)
        Me.cmbRayonNouveau.TabIndex = 43
        Me.cmbRayonNouveau.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbRayonNouveau.PropBag = resources.GetString("cmbRayonNouveau.PropBag")
        '
        'GroupBox5
        '
        Me.GroupBox5.Controls.Add(Me.Label9)
        Me.GroupBox5.Controls.Add(Me.tSection)
        Me.GroupBox5.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupBox5.Location = New System.Drawing.Point(17, 62)
        Me.GroupBox5.Name = "GroupBox5"
        Me.GroupBox5.Size = New System.Drawing.Size(299, 70)
        Me.GroupBox5.TabIndex = 51
        Me.GroupBox5.TabStop = False
        Me.GroupBox5.Text = "Paramètres de section"
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Location = New System.Drawing.Point(13, 33)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(49, 13)
        Me.Label9.TabIndex = 48
        Me.Label9.Text = "Section :"
        Me.Label9.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tSection
        '
        Me.tSection.AutoSize = False
        Me.tSection.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tSection.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tSection.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tSection.Location = New System.Drawing.Point(68, 30)
        Me.tSection.Name = "tSection"
        Me.tSection.Size = New System.Drawing.Size(222, 20)
        Me.tSection.TabIndex = 19
        Me.tSection.Tag = Nothing
        Me.tSection.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tSection.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Location = New System.Drawing.Point(17, 15)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(42, 13)
        Me.Label3.TabIndex = 47
        Me.Label3.Text = "Forme :"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'cmbCategorieNouveau
        '
        Me.cmbCategorieNouveau.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbCategorieNouveau.Caption = ""
        Me.cmbCategorieNouveau.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbCategorieNouveau.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbCategorieNouveau.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbCategorieNouveau.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbCategorieNouveau.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbCategorieNouveau.Images.Add(CType(resources.GetObject("cmbCategorieNouveau.Images"), System.Drawing.Image))
        Me.cmbCategorieNouveau.Location = New System.Drawing.Point(265, 11)
        Me.cmbCategorieNouveau.MatchEntryTimeout = CType(2000, Long)
        Me.cmbCategorieNouveau.MaxDropDownItems = CType(5, Short)
        Me.cmbCategorieNouveau.MaxLength = 32767
        Me.cmbCategorieNouveau.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbCategorieNouveau.Name = "cmbCategorieNouveau"
        Me.cmbCategorieNouveau.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbCategorieNouveau.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbCategorieNouveau.Size = New System.Drawing.Size(134, 22)
        Me.cmbCategorieNouveau.TabIndex = 48
        Me.cmbCategorieNouveau.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbCategorieNouveau.PropBag = resources.GetString("cmbCategorieNouveau.PropBag")
        '
        'cmbFormeNouveau
        '
        Me.cmbFormeNouveau.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbFormeNouveau.Caption = ""
        Me.cmbFormeNouveau.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbFormeNouveau.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbFormeNouveau.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbFormeNouveau.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbFormeNouveau.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbFormeNouveau.Images.Add(CType(resources.GetObject("cmbFormeNouveau.Images"), System.Drawing.Image))
        Me.cmbFormeNouveau.Location = New System.Drawing.Point(63, 11)
        Me.cmbFormeNouveau.MatchEntryTimeout = CType(2000, Long)
        Me.cmbFormeNouveau.MaxDropDownItems = CType(5, Short)
        Me.cmbFormeNouveau.MaxLength = 32767
        Me.cmbFormeNouveau.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbFormeNouveau.Name = "cmbFormeNouveau"
        Me.cmbFormeNouveau.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbFormeNouveau.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbFormeNouveau.Size = New System.Drawing.Size(134, 22)
        Me.cmbFormeNouveau.TabIndex = 46
        Me.cmbFormeNouveau.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbFormeNouveau.PropBag = resources.GetString("cmbFormeNouveau.PropBag")
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(203, 16)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(58, 13)
        Me.Label1.TabIndex = 49
        Me.Label1.Text = "Catégorie :"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.cmbLaboAncien)
        Me.GroupBox2.Controls.Add(Me.Label11)
        Me.GroupBox2.Controls.Add(Me.GroupBox6)
        Me.GroupBox2.Controls.Add(Me.tDesignationCommencePar)
        Me.GroupBox2.Controls.Add(Me.Label6)
        Me.GroupBox2.Controls.Add(Me.GroupeSection)
        Me.GroupBox2.Controls.Add(Me.GroupBox3)
        Me.GroupBox2.Controls.Add(Me.cmbCategorieAncien)
        Me.GroupBox2.Controls.Add(Me.Label2)
        Me.GroupBox2.Controls.Add(Me.cmbFormeAncien)
        Me.GroupBox2.Controls.Add(Me.LVilleFournisseur)
        Me.GroupBox2.Location = New System.Drawing.Point(12, 36)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(435, 262)
        Me.GroupBox2.TabIndex = 12
        Me.GroupBox2.TabStop = False
        '
        'cmbLaboAncien
        '
        Me.cmbLaboAncien.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbLaboAncien.Caption = ""
        Me.cmbLaboAncien.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbLaboAncien.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbLaboAncien.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbLaboAncien.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbLaboAncien.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbLaboAncien.Images.Add(CType(resources.GetObject("cmbLaboAncien.Images"), System.Drawing.Image))
        Me.cmbLaboAncien.Location = New System.Drawing.Point(58, 44)
        Me.cmbLaboAncien.MatchEntryTimeout = CType(2000, Long)
        Me.cmbLaboAncien.MaxDropDownItems = CType(5, Short)
        Me.cmbLaboAncien.MaxLength = 32767
        Me.cmbLaboAncien.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbLaboAncien.Name = "cmbLaboAncien"
        Me.cmbLaboAncien.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbLaboAncien.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbLaboAncien.Size = New System.Drawing.Size(145, 22)
        Me.cmbLaboAncien.TabIndex = 48
        Me.cmbLaboAncien.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbLaboAncien.PropBag = resources.GetString("cmbLaboAncien.PropBag")
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.Location = New System.Drawing.Point(12, 49)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(37, 13)
        Me.Label11.TabIndex = 49
        Me.Label11.Text = "Labo :"
        Me.Label11.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'GroupBox6
        '
        Me.GroupBox6.Controls.Add(Me.rdbTousCodePCT)
        Me.GroupBox6.Controls.Add(Me.tCodePCT2)
        Me.GroupBox6.Controls.Add(Me.tCodePCT1)
        Me.GroupBox6.Controls.Add(Me.rdbIntervalleCodePCT)
        Me.GroupBox6.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupBox6.Location = New System.Drawing.Point(6, 148)
        Me.GroupBox6.Name = "GroupBox6"
        Me.GroupBox6.Size = New System.Drawing.Size(299, 70)
        Me.GroupBox6.TabIndex = 46
        Me.GroupBox6.TabStop = False
        Me.GroupBox6.Text = "Paramètres de CodePCT"
        '
        'rdbTousCodePCT
        '
        Me.rdbTousCodePCT.AutoSize = True
        Me.rdbTousCodePCT.Checked = True
        Me.rdbTousCodePCT.Location = New System.Drawing.Point(6, 19)
        Me.rdbTousCodePCT.Name = "rdbTousCodePCT"
        Me.rdbTousCodePCT.Size = New System.Drawing.Size(58, 17)
        Me.rdbTousCodePCT.TabIndex = 16
        Me.rdbTousCodePCT.TabStop = True
        Me.rdbTousCodePCT.Text = "Toutes"
        Me.rdbTousCodePCT.UseVisualStyleBackColor = True
        '
        'tCodePCT2
        '
        Me.tCodePCT2.AutoSize = False
        Me.tCodePCT2.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodePCT2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodePCT2.DataType = GetType(Integer)
        Me.tCodePCT2.Enabled = False
        Me.tCodePCT2.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodePCT2.Location = New System.Drawing.Point(202, 38)
        Me.tCodePCT2.Name = "tCodePCT2"
        Me.tCodePCT2.Size = New System.Drawing.Size(91, 20)
        Me.tCodePCT2.TabIndex = 50
        Me.tCodePCT2.Tag = Nothing
        Me.tCodePCT2.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodePCT2.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tCodePCT1
        '
        Me.tCodePCT1.AutoSize = False
        Me.tCodePCT1.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodePCT1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodePCT1.DataType = GetType(Integer)
        Me.tCodePCT1.Enabled = False
        Me.tCodePCT1.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodePCT1.Location = New System.Drawing.Point(93, 38)
        Me.tCodePCT1.Name = "tCodePCT1"
        Me.tCodePCT1.Size = New System.Drawing.Size(87, 20)
        Me.tCodePCT1.TabIndex = 49
        Me.tCodePCT1.Tag = Nothing
        Me.tCodePCT1.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodePCT1.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'rdbIntervalleCodePCT
        '
        Me.rdbIntervalleCodePCT.AutoSize = True
        Me.rdbIntervalleCodePCT.Location = New System.Drawing.Point(6, 41)
        Me.rdbIntervalleCodePCT.Name = "rdbIntervalleCodePCT"
        Me.rdbIntervalleCodePCT.Size = New System.Drawing.Size(68, 17)
        Me.rdbIntervalleCodePCT.TabIndex = 17
        Me.rdbIntervalleCodePCT.Text = "Intervalle"
        Me.rdbIntervalleCodePCT.UseVisualStyleBackColor = True
        '
        'tDesignationCommencePar
        '
        Me.tDesignationCommencePar.AutoSize = False
        Me.tDesignationCommencePar.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tDesignationCommencePar.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDesignationCommencePar.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDesignationCommencePar.Location = New System.Drawing.Point(160, 218)
        Me.tDesignationCommencePar.Name = "tDesignationCommencePar"
        Me.tDesignationCommencePar.Size = New System.Drawing.Size(150, 20)
        Me.tDesignationCommencePar.TabIndex = 47
        Me.tDesignationCommencePar.Tag = Nothing
        Me.tDesignationCommencePar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDesignationCommencePar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Location = New System.Drawing.Point(9, 221)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(145, 13)
        Me.Label6.TabIndex = 46
        Me.Label6.Text = "Désignation commence par  :"
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'GroupeSection
        '
        Me.GroupeSection.Controls.Add(Me.cmbRayonAncien)
        Me.GroupeSection.Controls.Add(Me.rdbRayonRayonAncien)
        Me.GroupeSection.Controls.Add(Me.rdbTousRayonAncien)
        Me.GroupeSection.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeSection.Location = New System.Drawing.Point(221, 72)
        Me.GroupeSection.Name = "GroupeSection"
        Me.GroupeSection.Size = New System.Drawing.Size(206, 70)
        Me.GroupeSection.TabIndex = 20
        Me.GroupeSection.TabStop = False
        Me.GroupeSection.Text = "Paramètres de rayon"
        '
        'cmbRayonAncien
        '
        Me.cmbRayonAncien.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbRayonAncien.Caption = ""
        Me.cmbRayonAncien.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbRayonAncien.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbRayonAncien.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbRayonAncien.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbRayonAncien.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbRayonAncien.Images.Add(CType(resources.GetObject("cmbRayonAncien.Images"), System.Drawing.Image))
        Me.cmbRayonAncien.Location = New System.Drawing.Point(69, 42)
        Me.cmbRayonAncien.MatchEntryTimeout = CType(2000, Long)
        Me.cmbRayonAncien.MaxDropDownItems = CType(5, Short)
        Me.cmbRayonAncien.MaxLength = 32767
        Me.cmbRayonAncien.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbRayonAncien.Name = "cmbRayonAncien"
        Me.cmbRayonAncien.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbRayonAncien.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbRayonAncien.Size = New System.Drawing.Size(52, 22)
        Me.cmbRayonAncien.TabIndex = 43
        Me.cmbRayonAncien.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbRayonAncien.PropBag = resources.GetString("cmbRayonAncien.PropBag")
        '
        'rdbRayonRayonAncien
        '
        Me.rdbRayonRayonAncien.AutoSize = True
        Me.rdbRayonRayonAncien.Location = New System.Drawing.Point(7, 43)
        Me.rdbRayonRayonAncien.Name = "rdbRayonRayonAncien"
        Me.rdbRayonRayonAncien.Size = New System.Drawing.Size(56, 17)
        Me.rdbRayonRayonAncien.TabIndex = 1
        Me.rdbRayonRayonAncien.TabStop = True
        Me.rdbRayonRayonAncien.Text = "Rayon"
        Me.rdbRayonRayonAncien.UseVisualStyleBackColor = True
        '
        'rdbTousRayonAncien
        '
        Me.rdbTousRayonAncien.AutoSize = True
        Me.rdbTousRayonAncien.Location = New System.Drawing.Point(6, 19)
        Me.rdbTousRayonAncien.Name = "rdbTousRayonAncien"
        Me.rdbTousRayonAncien.Size = New System.Drawing.Size(99, 17)
        Me.rdbTousRayonAncien.TabIndex = 0
        Me.rdbTousRayonAncien.TabStop = True
        Me.rdbTousRayonAncien.Text = "Tous les rayons"
        Me.rdbTousRayonAncien.UseVisualStyleBackColor = True
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.rdbToutesSectionAncien)
        Me.GroupBox3.Controls.Add(Me.rdbIntervalleSectionAncien)
        Me.GroupBox3.Controls.Add(Me.tFinIntervalleAncien)
        Me.GroupBox3.Controls.Add(Me.tDebutIntervalleAncien)
        Me.GroupBox3.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupBox3.Location = New System.Drawing.Point(6, 72)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(209, 70)
        Me.GroupBox3.TabIndex = 45
        Me.GroupBox3.TabStop = False
        Me.GroupBox3.Text = "Paramètres de section"
        '
        'rdbToutesSectionAncien
        '
        Me.rdbToutesSectionAncien.AutoSize = True
        Me.rdbToutesSectionAncien.Location = New System.Drawing.Point(6, 19)
        Me.rdbToutesSectionAncien.Name = "rdbToutesSectionAncien"
        Me.rdbToutesSectionAncien.Size = New System.Drawing.Size(116, 17)
        Me.rdbToutesSectionAncien.TabIndex = 16
        Me.rdbToutesSectionAncien.TabStop = True
        Me.rdbToutesSectionAncien.Text = "Toutes les sections"
        Me.rdbToutesSectionAncien.UseVisualStyleBackColor = True
        '
        'rdbIntervalleSectionAncien
        '
        Me.rdbIntervalleSectionAncien.AutoSize = True
        Me.rdbIntervalleSectionAncien.Location = New System.Drawing.Point(6, 42)
        Me.rdbIntervalleSectionAncien.Name = "rdbIntervalleSectionAncien"
        Me.rdbIntervalleSectionAncien.Size = New System.Drawing.Size(125, 17)
        Me.rdbIntervalleSectionAncien.TabIndex = 17
        Me.rdbIntervalleSectionAncien.TabStop = True
        Me.rdbIntervalleSectionAncien.Text = "Intervalle de sections"
        Me.rdbIntervalleSectionAncien.UseVisualStyleBackColor = True
        '
        'tFinIntervalleAncien
        '
        Me.tFinIntervalleAncien.AutoSize = False
        Me.tFinIntervalleAncien.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tFinIntervalleAncien.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tFinIntervalleAncien.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tFinIntervalleAncien.Location = New System.Drawing.Point(175, 42)
        Me.tFinIntervalleAncien.Name = "tFinIntervalleAncien"
        Me.tFinIntervalleAncien.Size = New System.Drawing.Size(29, 20)
        Me.tFinIntervalleAncien.TabIndex = 19
        Me.tFinIntervalleAncien.Tag = Nothing
        Me.tFinIntervalleAncien.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tFinIntervalleAncien.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tDebutIntervalleAncien
        '
        Me.tDebutIntervalleAncien.AutoSize = False
        Me.tDebutIntervalleAncien.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tDebutIntervalleAncien.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDebutIntervalleAncien.Font = New System.Drawing.Font("Calibri", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tDebutIntervalleAncien.Location = New System.Drawing.Point(137, 42)
        Me.tDebutIntervalleAncien.Name = "tDebutIntervalleAncien"
        Me.tDebutIntervalleAncien.Size = New System.Drawing.Size(32, 20)
        Me.tDebutIntervalleAncien.TabIndex = 18
        Me.tDebutIntervalleAncien.Tag = Nothing
        Me.tDebutIntervalleAncien.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tDebutIntervalleAncien.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbCategorieAncien
        '
        Me.cmbCategorieAncien.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbCategorieAncien.Caption = ""
        Me.cmbCategorieAncien.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbCategorieAncien.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbCategorieAncien.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbCategorieAncien.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbCategorieAncien.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbCategorieAncien.Images.Add(CType(resources.GetObject("cmbCategorieAncien.Images"), System.Drawing.Image))
        Me.cmbCategorieAncien.Location = New System.Drawing.Point(268, 15)
        Me.cmbCategorieAncien.MatchEntryTimeout = CType(2000, Long)
        Me.cmbCategorieAncien.MaxDropDownItems = CType(5, Short)
        Me.cmbCategorieAncien.MaxLength = 32767
        Me.cmbCategorieAncien.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbCategorieAncien.Name = "cmbCategorieAncien"
        Me.cmbCategorieAncien.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbCategorieAncien.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbCategorieAncien.Size = New System.Drawing.Size(159, 22)
        Me.cmbCategorieAncien.TabIndex = 16
        Me.cmbCategorieAncien.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbCategorieAncien.PropBag = resources.GetString("cmbCategorieAncien.PropBag")
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(209, 20)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(58, 13)
        Me.Label2.TabIndex = 17
        Me.Label2.Text = "Catégorie :"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'cmbFormeAncien
        '
        Me.cmbFormeAncien.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbFormeAncien.Caption = ""
        Me.cmbFormeAncien.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbFormeAncien.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbFormeAncien.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbFormeAncien.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbFormeAncien.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbFormeAncien.Images.Add(CType(resources.GetObject("cmbFormeAncien.Images"), System.Drawing.Image))
        Me.cmbFormeAncien.Location = New System.Drawing.Point(58, 16)
        Me.cmbFormeAncien.MatchEntryTimeout = CType(2000, Long)
        Me.cmbFormeAncien.MaxDropDownItems = CType(5, Short)
        Me.cmbFormeAncien.MaxLength = 32767
        Me.cmbFormeAncien.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbFormeAncien.Name = "cmbFormeAncien"
        Me.cmbFormeAncien.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbFormeAncien.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbFormeAncien.Size = New System.Drawing.Size(145, 22)
        Me.cmbFormeAncien.TabIndex = 14
        Me.cmbFormeAncien.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbFormeAncien.PropBag = resources.GetString("cmbFormeAncien.PropBag")
        '
        'LVilleFournisseur
        '
        Me.LVilleFournisseur.AutoSize = True
        Me.LVilleFournisseur.Location = New System.Drawing.Point(12, 20)
        Me.LVilleFournisseur.Name = "LVilleFournisseur"
        Me.LVilleFournisseur.Size = New System.Drawing.Size(42, 13)
        Me.LVilleFournisseur.TabIndex = 15
        Me.LVilleFournisseur.Text = "Forme :"
        Me.LVilleFournisseur.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'fChangemantDuFichierArticle
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(874, 471)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fChangemantDuFichierArticle"
        Me.Text = "fChangemantDuFichierArticle"
        Me.Panel.ResumeLayout(False)
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.cmbLaboNouveau, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tVenteHT, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tAchatHt, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.cmbRayonNouveau, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox5.ResumeLayout(False)
        Me.GroupBox5.PerformLayout()
        CType(Me.tSection, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbCategorieNouveau, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbFormeNouveau, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.cmbLaboAncien, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox6.ResumeLayout(False)
        Me.GroupBox6.PerformLayout()
        CType(Me.tCodePCT2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodePCT1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDesignationCommencePar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupeSection.ResumeLayout(False)
        Me.GroupeSection.PerformLayout()
        CType(Me.cmbRayonAncien, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        CType(Me.tFinIntervalleAncien, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tDebutIntervalleAncien, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbCategorieAncien, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbFormeAncien, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents cmbCategorieAncien As C1.Win.C1List.C1Combo
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents cmbFormeAncien As C1.Win.C1List.C1Combo
    Friend WithEvents LVilleFournisseur As System.Windows.Forms.Label
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents rdbToutesSectionAncien As System.Windows.Forms.RadioButton
    Friend WithEvents rdbIntervalleSectionAncien As System.Windows.Forms.RadioButton
    Friend WithEvents tFinIntervalleAncien As C1.Win.C1Input.C1TextBox
    Friend WithEvents tDebutIntervalleAncien As C1.Win.C1Input.C1TextBox
    Friend WithEvents GroupeSection As System.Windows.Forms.GroupBox
    Friend WithEvents cmbRayonAncien As C1.Win.C1List.C1Combo
    Friend WithEvents rdbRayonRayonAncien As System.Windows.Forms.RadioButton
    Friend WithEvents rdbTousRayonAncien As System.Windows.Forms.RadioButton
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox5 As System.Windows.Forms.GroupBox
    Friend WithEvents tSection As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents cmbCategorieNouveau As C1.Win.C1List.C1Combo
    Friend WithEvents cmbFormeNouveau As C1.Win.C1List.C1Combo
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bOk As C1.Win.C1Input.C1Button
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents tVenteHT As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents tAchatHt As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents tDesignationCommencePar As C1.Win.C1Input.C1TextBox
    Friend WithEvents gArticles As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents cmbRayonNouveau As C1.Win.C1List.C1Combo
    Friend WithEvents tCodePCT2 As C1.Win.C1Input.C1TextBox
    Friend WithEvents tCodePCT1 As C1.Win.C1Input.C1TextBox
    Friend WithEvents GroupBox6 As System.Windows.Forms.GroupBox
    Friend WithEvents rdbTousCodePCT As System.Windows.Forms.RadioButton
    Friend WithEvents rdbIntervalleCodePCT As System.Windows.Forms.RadioButton
    Friend WithEvents cmbLaboNouveau As C1.Win.C1List.C1Combo
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents cmbLaboAncien As C1.Win.C1List.C1Combo
    Friend WithEvents Label11 As System.Windows.Forms.Label
End Class
