﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fArticleRemboursable
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fArticleRemboursable))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.bConfirmer = New C1.Win.C1Input.C1Button()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.gArticle = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.cmbArticle = New C1.Win.C1List.C1Combo()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.gArticlesNonAttribue = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bSupprimerUn = New C1.Win.C1Input.C1Button()
        Me.bSupprimerTous = New C1.Win.C1Input.C1Button()
        Me.bAjoutertous = New C1.Win.C1Input.C1Button()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.cmbMutuelle = New C1.Win.C1List.C1Combo()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.bAjouterUn = New C1.Win.C1Input.C1Button()
        Me.cmbCategorieCnam = New C1.Win.C1List.C1Combo()
        Me.Label23 = New System.Windows.Forms.Label()
        Me.Panel.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.gArticle, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.cmbArticle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gArticlesNonAttribue, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox4.SuspendLayout()
        CType(Me.cmbMutuelle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbCategorieCnam, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.Label5)
        Me.Panel.Controls.Add(Me.bConfirmer)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.GroupBox2)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Controls.Add(Me.bSupprimerUn)
        Me.Panel.Controls.Add(Me.bSupprimerTous)
        Me.Panel.Controls.Add(Me.bAjoutertous)
        Me.Panel.Controls.Add(Me.GroupBox4)
        Me.Panel.Controls.Add(Me.bAjouterUn)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1020, 539)
        Me.Panel.TabIndex = 3
        '
        'Label5
        '
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label5.Location = New System.Drawing.Point(12, 15)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(757, 44)
        Me.Label5.TabIndex = 48
        Me.Label5.Text = "ARTICLES REMBOURSABLES"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bConfirmer
        '
        Me.bConfirmer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bConfirmer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmer.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__1
        Me.bConfirmer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmer.Location = New System.Drawing.Point(775, 15)
        Me.bConfirmer.Name = "bConfirmer"
        Me.bConfirmer.Size = New System.Drawing.Size(112, 45)
        Me.bConfirmer.TabIndex = 16
        Me.bConfirmer.Text = "Confirmer                 F3"
        Me.bConfirmer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bConfirmer.UseVisualStyleBackColor = True
        Me.bConfirmer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(894, 15)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(112, 45)
        Me.bAnnuler.TabIndex = 17
        Me.bAnnuler.Text = "Fermer              F12"
        Me.bAnnuler.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox2
        '
        Me.GroupBox2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox2.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox2.Controls.Add(Me.gArticle)
        Me.GroupBox2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox2.Location = New System.Drawing.Point(656, 125)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(350, 402)
        Me.GroupBox2.TabIndex = 15
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "Liste des Articles remboursables"
        '
        'gArticle
        '
        Me.gArticle.AllowUpdate = False
        Me.gArticle.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gArticle.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.gArticle.GroupByCaption = "Drag a column header here to group by that column"
        Me.gArticle.Images.Add(CType(resources.GetObject("gArticle.Images"), System.Drawing.Image))
        Me.gArticle.Location = New System.Drawing.Point(17, 33)
        Me.gArticle.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gArticle.Name = "gArticle"
        Me.gArticle.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gArticle.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gArticle.PreviewInfo.ZoomFactor = 75.0R
        Me.gArticle.PrintInfo.PageSettings = CType(resources.GetObject("gArticle.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gArticle.Size = New System.Drawing.Size(314, 348)
        Me.gArticle.TabIndex = 2
        Me.gArticle.Text = "C1TrueDBGrid4"
        Me.gArticle.PropBag = resources.GetString("gArticle.PropBag")
        '
        'GroupBox1
        '
        Me.GroupBox1.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox1.Controls.Add(Me.cmbCategorieCnam)
        Me.GroupBox1.Controls.Add(Me.Label23)
        Me.GroupBox1.Controls.Add(Me.cmbArticle)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Controls.Add(Me.gArticlesNonAttribue)
        Me.GroupBox1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox1.Location = New System.Drawing.Point(12, 125)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(350, 402)
        Me.GroupBox1.TabIndex = 14
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Liste de tous les Articles "
        '
        'cmbArticle
        '
        Me.cmbArticle.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbArticle.Caption = ""
        Me.cmbArticle.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbArticle.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbArticle.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbArticle.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbArticle.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbArticle.Images.Add(CType(resources.GetObject("cmbArticle.Images"), System.Drawing.Image))
        Me.cmbArticle.Location = New System.Drawing.Point(106, 33)
        Me.cmbArticle.MatchEntryTimeout = CType(2000, Long)
        Me.cmbArticle.MaxDropDownItems = CType(5, Short)
        Me.cmbArticle.MaxLength = 32767
        Me.cmbArticle.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbArticle.Name = "cmbArticle"
        Me.cmbArticle.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbArticle.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbArticle.Size = New System.Drawing.Size(224, 21)
        Me.cmbArticle.TabIndex = 15
        Me.cmbArticle.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbArticle.PropBag = resources.GetString("cmbArticle.PropBag")
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.Location = New System.Drawing.Point(13, 37)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(50, 13)
        Me.Label1.TabIndex = 14
        Me.Label1.Text = "Chercher"
        '
        'gArticlesNonAttribue
        '
        Me.gArticlesNonAttribue.AllowUpdate = False
        Me.gArticlesNonAttribue.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gArticlesNonAttribue.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.gArticlesNonAttribue.GroupByCaption = "Drag a column header here to group by that column"
        Me.gArticlesNonAttribue.Images.Add(CType(resources.GetObject("gArticlesNonAttribue.Images"), System.Drawing.Image))
        Me.gArticlesNonAttribue.Location = New System.Drawing.Point(16, 95)
        Me.gArticlesNonAttribue.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gArticlesNonAttribue.Name = "gArticlesNonAttribue"
        Me.gArticlesNonAttribue.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gArticlesNonAttribue.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gArticlesNonAttribue.PreviewInfo.ZoomFactor = 75.0R
        Me.gArticlesNonAttribue.PrintInfo.PageSettings = CType(resources.GetObject("gArticlesNonAttribue.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gArticlesNonAttribue.Size = New System.Drawing.Size(314, 287)
        Me.gArticlesNonAttribue.TabIndex = 4
        Me.gArticlesNonAttribue.Text = "C1TrueDBGrid4"
        Me.gArticlesNonAttribue.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gArticlesNonAttribue.PropBag = resources.GetString("gArticlesNonAttribue.PropBag")
        '
        'bSupprimerUn
        '
        Me.bSupprimerUn.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.bSupprimerUn.Image = Global.Pharma2000Premium.My.Resources.Resources.previous_1
        Me.bSupprimerUn.Location = New System.Drawing.Point(488, 347)
        Me.bSupprimerUn.Name = "bSupprimerUn"
        Me.bSupprimerUn.Size = New System.Drawing.Size(45, 45)
        Me.bSupprimerUn.TabIndex = 6
        Me.bSupprimerUn.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSupprimerUn.UseVisualStyleBackColor = True
        Me.bSupprimerUn.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bSupprimerTous
        '
        Me.bSupprimerTous.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.bSupprimerTous.Image = Global.Pharma2000Premium.My.Resources.Resources.first_1
        Me.bSupprimerTous.Location = New System.Drawing.Point(488, 409)
        Me.bSupprimerTous.Name = "bSupprimerTous"
        Me.bSupprimerTous.Size = New System.Drawing.Size(45, 45)
        Me.bSupprimerTous.TabIndex = 7
        Me.bSupprimerTous.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSupprimerTous.UseVisualStyleBackColor = True
        Me.bSupprimerTous.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjoutertous
        '
        Me.bAjoutertous.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.bAjoutertous.Image = Global.Pharma2000Premium.My.Resources.Resources.last_1
        Me.bAjoutertous.Location = New System.Drawing.Point(488, 220)
        Me.bAjoutertous.Name = "bAjoutertous"
        Me.bAjoutertous.Size = New System.Drawing.Size(45, 45)
        Me.bAjoutertous.TabIndex = 1
        Me.bAjoutertous.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjoutertous.UseVisualStyleBackColor = True
        Me.bAjoutertous.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox4
        '
        Me.GroupBox4.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox4.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox4.Controls.Add(Me.cmbMutuelle)
        Me.GroupBox4.Controls.Add(Me.Label15)
        Me.GroupBox4.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox4.Location = New System.Drawing.Point(12, 66)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(994, 53)
        Me.GroupBox4.TabIndex = 0
        Me.GroupBox4.TabStop = False
        '
        'cmbMutuelle
        '
        Me.cmbMutuelle.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbMutuelle.Caption = ""
        Me.cmbMutuelle.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbMutuelle.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbMutuelle.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbMutuelle.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbMutuelle.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbMutuelle.Images.Add(CType(resources.GetObject("cmbMutuelle.Images"), System.Drawing.Image))
        Me.cmbMutuelle.Location = New System.Drawing.Point(145, 18)
        Me.cmbMutuelle.MatchEntryTimeout = CType(2000, Long)
        Me.cmbMutuelle.MaxDropDownItems = CType(5, Short)
        Me.cmbMutuelle.MaxLength = 32767
        Me.cmbMutuelle.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbMutuelle.Name = "cmbMutuelle"
        Me.cmbMutuelle.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbMutuelle.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbMutuelle.Size = New System.Drawing.Size(279, 21)
        Me.cmbMutuelle.TabIndex = 13
        Me.cmbMutuelle.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbMutuelle.PropBag = resources.GetString("cmbMutuelle.PropBag")
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.BackColor = System.Drawing.Color.Transparent
        Me.Label15.Location = New System.Drawing.Point(42, 22)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(97, 13)
        Me.Label15.TabIndex = 8
        Me.Label15.Text = "Liste des Mutuelles"
        '
        'bAjouterUn
        '
        Me.bAjouterUn.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.bAjouterUn.Image = Global.Pharma2000Premium.My.Resources.Resources.next_1
        Me.bAjouterUn.Location = New System.Drawing.Point(488, 286)
        Me.bAjouterUn.Name = "bAjouterUn"
        Me.bAjouterUn.Size = New System.Drawing.Size(45, 45)
        Me.bAjouterUn.TabIndex = 2
        Me.bAjouterUn.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouterUn.UseVisualStyleBackColor = True
        Me.bAjouterUn.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbCategorieCnam
        '
        Me.cmbCategorieCnam.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbCategorieCnam.Caption = ""
        Me.cmbCategorieCnam.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbCategorieCnam.ColumnWidth = 100
        Me.cmbCategorieCnam.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.cmbCategorieCnam.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbCategorieCnam.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown
        Me.cmbCategorieCnam.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbCategorieCnam.EditorFont = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbCategorieCnam.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbCategorieCnam.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbCategorieCnam.Images.Add(CType(resources.GetObject("cmbCategorieCnam.Images"), System.Drawing.Image))
        Me.cmbCategorieCnam.Location = New System.Drawing.Point(106, 60)
        Me.cmbCategorieCnam.MatchEntryTimeout = CType(2000, Long)
        Me.cmbCategorieCnam.MaxDropDownItems = CType(5, Short)
        Me.cmbCategorieCnam.MaxLength = 32767
        Me.cmbCategorieCnam.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbCategorieCnam.Name = "cmbCategorieCnam"
        Me.cmbCategorieCnam.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbCategorieCnam.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbCategorieCnam.Size = New System.Drawing.Size(224, 22)
        Me.cmbCategorieCnam.TabIndex = 38
        Me.cmbCategorieCnam.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbCategorieCnam.PropBag = resources.GetString("cmbCategorieCnam.PropBag")
        '
        'Label23
        '
        Me.Label23.AutoSize = True
        Me.Label23.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label23.Location = New System.Drawing.Point(14, 65)
        Me.Label23.Name = "Label23"
        Me.Label23.Size = New System.Drawing.Size(86, 13)
        Me.Label23.TabIndex = 39
        Me.Label23.Text = "Catégorie CNAM"
        '
        'fArticleRemboursable
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1020, 539)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fArticleRemboursable"
        Me.Text = "fArticleRemboursable"
        Me.Panel.ResumeLayout(False)
        Me.GroupBox2.ResumeLayout(False)
        CType(Me.gArticle, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.cmbArticle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gArticlesNonAttribue, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.cmbMutuelle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbCategorieCnam, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents bConfirmer As C1.Win.C1Input.C1Button
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents gArticle As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents cmbArticle As C1.Win.C1List.C1Combo
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents gArticlesNonAttribue As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bSupprimerUn As C1.Win.C1Input.C1Button
    Friend WithEvents bSupprimerTous As C1.Win.C1Input.C1Button
    Friend WithEvents bAjoutertous As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents cmbMutuelle As C1.Win.C1List.C1Combo
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents bAjouterUn As C1.Win.C1Input.C1Button
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents cmbCategorieCnam As C1.Win.C1List.C1Combo
    Friend WithEvents Label23 As System.Windows.Forms.Label
End Class
