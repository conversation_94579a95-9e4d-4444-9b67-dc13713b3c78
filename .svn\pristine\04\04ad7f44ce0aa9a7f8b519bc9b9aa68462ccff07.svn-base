﻿Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Public Class fAchatJusteAffichage
    Dim cmdChargement As New SqlCommand
    Dim cbChargement As New SqlCommandBuilder
    Dim dsChargement As New DataSet
    Dim daChargement As New SqlDataAdapter

    Dim cmdAchat As New SqlCommand
    Dim cbAchat As New SqlCommandBuilder
    Public dsAchat As New DataSet
    Dim daAchat As New SqlDataAdapter
    Dim daAchat1 As New SqlDataAdapter
    Dim mode As String = ""

    Public NumeroAchat As String = ""

    Public TotalTTCAchat As Double = 0.0
    Public TotalHTNETAchat As Double = 0.0
    Public TotalRemiseAchat As Double = 0.0
    Public TotalTVAAchat As Double = 0.0
    Public TotalVenteTTCAchat As Double = 0.0
    Public TotalQte As Integer = 0

    Public TVA As Double = 0.0
    Public Timbre As Double = 0.3

    '------------------------------------------------- les informations de paiement (retour de fenetre paiement)
    Public PaiementOkNo As String = ""
    Public Operateur As Integer = 0

    Public NouvelleAchat As DataRow = Nothing 'datarow pour charger l'entête dans la datatable ACHAT
    Public NouvelArticle As DataRow = Nothing 'datarow pour charger les détails dans la datatable ACHAT_DETAIL
    Public dr As DataRow = Nothing 'datarow pour charger dans la datatable ARTICLE qui contient les articles recherchés pendant la saisie de la designation

    '---------------------------------------- variables pour confirmer la mise en instance d'une Commande
    Dim ComfirmerMettreEnINstance As Boolean = False
    Dim ConfirmerInstance As Boolean = False
    Dim NomInstance As String = ""
    Dim CodeOperateurInstance As String = ""
    '---------------------------------------- Tableau pour enregistrer les comandes convertis en achat
    Dim TableauCommande(10) As String
    '---------------------------------------- variable pour recuperer le code article à partir de la fenêtre recherchre multicritere
    Dim CodeArticleRechercheMC As String = ""
    '---------------------------------------- variable pour savoir si l achat est depuis commande ou nn
    Dim DepuisCommande As Boolean = False

    Dim NombreAchatInstance As Integer = 0

    Public TransactionAchat As SqlTransaction


    Public Sub Init()

        lTotHT.Text = "0.000"
        lTotalTTC.Text = "0.000"
        lTotalTVA.Text = "0.000"
        lTotalQte.Text = "0"
        tRemisePourcentage.Value = "0"
        lRemise.Text = "0.000"
        lValeurVenteTTC.Text = "0.000"
        lHTNet.Text = "0.000"
        tAutre.Value = "0.000"

        tRemisePourcentage.Visible = False
        lRemisePourcentAfficher.Visible = False
        lTotalQteAffiche.Visible = False
        lTotalQte.Visible = False

        DepuisCommande = False

        '-------------------------------- initialisation du tableau des commandes convertis en achat
        Dim k As Integer = 0

        For k = 0 To TableauCommande.Length - 1
            TableauCommande(k) = ""
        Next

        Dim StrSQL As String = ""
        Dim I As Integer

        Try
            dsChargement.Tables("ACHAT_DETAILS").Clear()
        Catch ex As Exception
        End Try

        Try
            dsChargement.Tables("ACHAT").Clear()
        Catch ex As Exception
        End Try

        Try
            dsChargement.Tables("FOURNISSEUR").Clear()
        Catch ex As Exception
        End Try

        'chargement des fournisseurs
        StrSQL = "SELECT CodeFournisseur,NomFournisseur FROM FOURNISSEUR ORDER BY NomFournisseur ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "FOURNISSEUR")
        cmbFournisseur.DataSource = dsChargement.Tables("FOURNISSEUR")
        cmbFournisseur.ValueMember = "CodeFournisseur"
        cmbFournisseur.DisplayMember = "NomFournisseur"
        cmbFournisseur.ColumnHeaders = False
        cmbFournisseur.Splits(0).DisplayColumns("CodeFournisseur").Visible = False
        cmbFournisseur.Splits(0).DisplayColumns("NomFournisseur").Width = 10
        cmbFournisseur.ExtendRightColumn = True

        'chargement des Entêtes des achats        
        StrSQL = "SELECT * FROM ACHAT WHERE NumeroAchat='" + NumeroAchat + "'"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "ACHAT")
        If dsChargement.Tables("ACHAT").Rows.Count > 0 Then
            NumeroAchat = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1).Item("NumeroAchat")

            lNumeroAchat.Text = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("NumeroAchat")
            lDateAchat.Text = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("Date")
            cmbFournisseur.SelectedValue = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("CodeFournisseur")

            cmbDateBlFacture.Value = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("DateBlFacture")
            tTotalHT.Value = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("TotalHT")

            tNumeroBlFact.Value = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("NumeroBL/Facture").ToString

            TotalTTCAchat = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("TotalTTC")
            TotalHTNETAchat = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("TotalHT")
            lTotHT.Text = TotalHTNETAchat.ToString
            lTotalTVA.Text = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("TVA")
            lValeurVenteTTC.Text = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("ValeurVenteTTC")

            lRemise.Text = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("TotalRemise")
            tAutre.Value = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("Autre")

            lTotalTTC.Text = Math.Round(dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("TotalTTC"), 3)
            lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("CodePersonnel"))

            lHTNet.Text = (CDbl(lTotHT.Text) - CDbl(lRemise.Text)).ToString

        End If
        'chargement des détails des achats 
        StrSQL = "SELECT NumeroAchat," + _
                 "CodeABarre," + _
                 "Designation," + _
                 "LibelleForme," + _
                 "Qte," + _
                 " '-' AS QteCommander," + _
                 "DatePeremption," + _
                 "NumeroLotArticle," + _
                 "Stock," + _
                 "Remise," + _
                 "PrixAchatHT," + _
                 "PrixVenteTTC," + _
                 "TotalAchatHT," + _
                 "TVA " + _
                 "FROM " + _
                 "ACHAT_DETAILS LEFT OUTER JOIN FORME_ARTICLE ON " + _
                 "ACHAT_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
                 "WHERE NumeroAchat =" + Quote(NumeroAchat) + " ORDER BY Ordre"

        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "ACHAT_DETAILS")

        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsChargement
            Catch ex As Exception
            End Try
            .DataMember = "ACHAT_DETAILS"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qte"
            .Columns("QteCommander").Caption = " Qte Cmd"
            .Columns("NumeroLotArticle").Caption = "Numero Lot"
            .Columns("DatePeremption").Caption = "Date de péremption"
            .Columns("Stock").Caption = "Stock"
            .Columns("Remise").Caption = "Remise"
            .Columns("PrixAchatHT").Caption = "Prix A HT "
            .Columns("PrixVenteTTC").Caption = "Prix V TTC "
            .Columns("TotalAchatHT").Caption = "Total A HT"
            .Columns("TVA").Caption = "TVA"

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("NumeroAchat").Width = 0
            .Splits(0).DisplayColumns("NumeroAchat").Visible = False
            .Splits(0).DisplayColumns("NumeroAchat").AllowSizing = False
            .Splits(0).DisplayColumns("CodeABarre").Width = 60
            .Splits(0).DisplayColumns("Designation").Width = 230
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("QteCommander").Width = 50
            .Splits(0).DisplayColumns("NumeroLotArticle").Width = 70
            .Splits(0).DisplayColumns("DatePeremption").Width = 70
            .Splits(0).DisplayColumns("Stock").Width = 50
            .Splits(0).DisplayColumns("Remise").Width = 50
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 60
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 60
            .Splits(0).DisplayColumns("TotalAchatHT").Width = 60
            .Splits(0).DisplayColumns("TVA").Width = 50

            .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Remise").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            '.FetchRowStyles = True
            '.SpringMode = True
            '.AlternatingRows = True
            'Style du Caractere et du grid
            ParametreGrid(gArticles)
        End With

        '--------------------initialisation de la datatable article qui est utilisé dans la liste de 
        '--------------------recherche alimenté selon les entrés de l utilisateur dans la colonne designation
        StrSQL = "SELECT CodeArticle," + _
                 "Designation," + _
                 "LibelleForme," + _
                 "PrixVenteTTC" + _
                 " FROM ARTICLE,FORME_ARTICLE " + _
                 "WHERE ARTICLE.CodeForme=FORME_ARTICLE.CodeForme and " + _
                 "Designation LIKE '" + gArticles.Columns("Designation").Value + "'ORDER BY Designation"

        cmdAchat.Connection = ConnectionServeur
        cmdAchat.CommandText = StrSQL
        daAchat = New SqlDataAdapter(cmdAchat)
        daAchat.Fill(dsAchat, "ARTICLE")

        With gListeRecherche
            .Columns.Clear()
            .DataSource = dsAchat
            .DataMember = "ARTICLE"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code Article"
            .Columns("Designation").Caption = "Designation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("PrixVenteTTC").Caption = "Prix de vente"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centre tous les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Visible = False
            Next

            .Splits(0).DisplayColumns("CodeArticle").Visible = True
            .Splits(0).DisplayColumns("Designation").Visible = True
            .Splits(0).DisplayColumns("LibelleForme").Visible = True
            .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("Designation").Width = 300
            .Splits(0).DisplayColumns("LibelleForme").Width = 100
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 20
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
        End With

        cbAchat = New SqlCommandBuilder(daAchat)


        GroupeFournisseur.Enabled = False


        cmbDateBlFacture.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        cmbDateBlFacture.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        '------------------------- affichage du nombre des achat en instance 
        StrSQL = " SELECT COUNT(*) FROM ACHAT_INSTANCE "

        cmdAchat.Connection = ConnectionServeur
        cmdAchat.CommandText = StrSQL

        Try
            NombreAchatInstance = cmdAchat.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        lNbreInstance.Text = "Achat en frigo : " + NombreAchatInstance.ToString

    End Sub

    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        CmdCalcul.Transaction = TransactionAchat

        Try
            Quote(ValeurCle)
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurCle)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim StrSQL As String = ""
        Dim i As Integer

        mode = "Ajout"

        '------------------------------ préparation des datatable vides 

        Try
            dsAchat.Tables("ACHAT_DETAILS").Clear()
        Catch ex As Exception
        End Try

        Try
            dsAchat.Tables("ACHAT").Clear()
        Catch ex As Exception
        End Try

        'chargement des Entêtes des achat 
        StrSQL = "SELECT top 0 * FROM ACHAT ORDER BY NumeroAchat ASC"
        cmdAchat.Connection = ConnectionServeur
        cmdAchat.CommandText = StrSQL
        daAchat = New SqlDataAdapter(cmdAchat)
        daAchat.Fill(dsAchat, "ACHAT")
        cbAchat = New SqlCommandBuilder(daAchat)

        'chargement des détails des achat 
        StrSQL = "SELECT NumeroAchat," + _
                 "CodeArticle," + _
                 "Designation," + _
                 "LibelleForme," + _
                 "Qte," + _
                 " '-' AS QteCommander," + _
                 "DatePeremption," + _
                 "NumeroLotArticle," + _
                 "Stock," + _
                 "Remise," + _
                 "PrixAchatHT," + _
                 "PrixVenteTTC," + _
                 "TotalAchatHT," + _
                 "TVA," + _
                 "ACHAT_DETAILS.CodeForme " + _
                 "FROM " + _
                 "ACHAT_DETAILS,FORME_ARTICLE " + _
                 "WHERE ACHAT_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
                 "AND NumeroAchat ='0' ORDER BY Ordre"

        cmdAchat.Connection = ConnectionServeur
        cmdAchat.CommandText = StrSQL
        daAchat.Fill(dsAchat, "ACHAT_DETAILS")
        cbAchat = New SqlCommandBuilder(daAchat)

        With gArticles
            .Columns.Clear()
            .DataSource = dsAchat
            .DataMember = "ACHAT_DETAILS"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qte"
            .Columns("QteCommander").Caption = "Qte Cmd"
            .Columns("NumeroLotArticle").Caption = "Numero Lot"
            .Columns("DatePeremption").Caption = "Date de péremption"
            .Columns("Stock").Caption = "Stock"
            .Columns("Remise").Caption = "Remise"
            .Columns("PrixAchatHT").Caption = "Prix A HT "
            .Columns("PrixVenteTTC").Caption = "Prix V TTC "
            .Columns("TotalAchatHT").Caption = "Total A HT "
            .Columns("TVA").Caption = "TVA"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("CodeArticle").Locked = False
            .Splits(0).DisplayColumns("Designation").Locked = False
            .Splits(0).DisplayColumns("Qte").Locked = False
            .Splits(0).DisplayColumns("Remise").Locked = False
            .Splits(0).DisplayColumns("NumeroLotArticle").Locked = False
            .Splits(0).DisplayColumns("DatePeremption").Locked = False
            .Splits(0).DisplayColumns("PrixAchatHT").Locked = False

            .Splits(0).DisplayColumns("NumeroAchat").Width = 0
            .Splits(0).DisplayColumns("NumeroAchat").AllowSizing = False
            .Splits(0).DisplayColumns("NumeroAchat").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").Width = 80
            .Splits(0).DisplayColumns("Designation").Width = 230
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("QteCommander").Width = 50
            .Splits(0).DisplayColumns("NumeroLotArticle").Width = 70
            .Splits(0).DisplayColumns("DatePeremption").Width = 70
            .Splits(0).DisplayColumns("Stock").Width = 50
            .Splits(0).DisplayColumns("Remise").Width = 50
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 60
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 60
            .Splits(0).DisplayColumns("TotalAchatHT").Width = 60
            .Splits(0).DisplayColumns("TVA").Width = 50
            .Splits(0).DisplayColumns("CodeForme").Width = 50

            .Splits(0).DisplayColumns("NumeroAchat").Visible = False
            .Splits(0).DisplayColumns("CodeForme").Visible = False


            .Splits(0).DisplayColumns("CodeArticle").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Remise").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True

        End With
        'ajout d'un nouvel enregistrement vide dans les datatables convenables
        NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
        NouvelArticle("Designation") = ""
        NouvelArticle("CodeArticle") = ""
        dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)

        NouvelleAchat = dsAchat.Tables("ACHAT").NewRow()
        dsAchat.Tables("ACHAT").Rows.Add(NouvelleAchat)

        Me.gArticles.Splits(0).DisplayColumns(0).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(3).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(11).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(12).AllowFocus = False

        '------------------------------ initialisation des differents zones de textes 
        '------------------------------ initialisation des variables globaux 
        lRemise.Visible = True
        lHTNet.Visible = True
        tRemisePourcentage.Visible = True
        tAutre.Enabled = True

        lRemisePourcentAfficher.Visible = True
        lRemiseAfficher.Visible = True
        lHTNetAfficher.Visible = True

        TotalTTCAchat = 0.0
        TotalHTNETAchat = 0.0
        TVA = 0.0
        Timbre = 0.3
        TotalTVAAchat = 0.0
        TotalVenteTTCAchat = 0.0

        lHTNet.Text = "0.000"
        lRemise.Text = "0.000"
        tRemisePourcentage.Value = "0.000"
        lTotHT.Text = "0.000"
        lTotalTTC.Text = "0.000"
        lTotalQte.Text = "0"
        lValeurVenteTTC.Text = "0.000"
        lTotalTVA.Text = "0.000"
        tAutre.Value = "0.000"
        tTotalHT.Value = "0.000"

        lTotalQteAffiche.Visible = True
        lTotalQte.Visible = True

        lOperateur.Text = "-"

        lDateAchat.Text = System.DateTime.Now
        cmbFournisseur.Text = ""
        lNumeroAchat.Text = "-------------"

        tNumeroBlFact.Value = ""
        cmbDateBlFacture.Value = System.DateTime.Now


        GroupeFournisseur.Enabled = True

        cmbFournisseur.Focus()

    End Sub

    Private Sub gArticles_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gArticles.Change
        If (gArticles.Col = 2 And gArticles.Columns("Designation").Value <> "") Or gArticles.Col = 1 Then
            Dim StrSQL1 As String = ""
            Dim I As Integer
            Dim NbLigne As Integer
            NbLigne = gArticles.RowCount

            With gListeRecherche
                .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(1).Width
                .Top = Me.gArticles.Top + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight
            End With

            Try
                dsAchat.Tables("ARTICLE").Clear()
            Catch ex As Exception

            End Try
            If gArticles.Row = dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 And gArticles.Col = 2 Then
                gListeRecherche.Visible = True
            Else
                gListeRecherche.Visible = False
            End If

            'chargement des articles qui sont mis en jeu
            If gArticles.Col = 2 Then
                If gArticles.Columns("Designation").Value.ToString.Length > 1 Then
                    If gArticles.Columns("Designation").Value.ToString.IndexOf(".") > 0 And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1)) Then   'gArticles.Columns("Designation").Value.ToString.Substring(gArticles.Columns("Designation").Value.ToString.Length - 1, 1) Like "." And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1))

                        StrSQL1 = "SELECT CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC" + _
                                  " FROM ARTICLE " + _
                                  "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  "WHERE " + _
                                  "ltrim(str(PrixVenteTTC,10,3)) LIKE '" + _
                                  gArticles.Columns("Designation").Value + "%' AND Supprime=0 ORDER BY PrixVenteTTC"
                    Else
                        StrSQL1 = "SELECT CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC" + _
                                  " FROM ARTICLE " + _
                                  "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  "WHERE " + _
                                  " Designation LIKE '" + gArticles.Columns("Designation").Value + _
                                  "%' AND Supprime=0 ORDER BY Designation"
                    End If
                Else
                    StrSQL1 = "SELECT CodeArticle," + _
                              "Designation," + _
                              "LibelleForme," + _
                              "PrixVenteTTC" + _
                              " FROM ARTICLE " + _
                              "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                              "WHERE " + _
                              " Designation LIKE '" + gArticles.Columns("Designation").Value + _
                              "%' AND Supprime=0 ORDER BY Designation"
                End If
            ElseIf gArticles.Col = 1 Then
                StrSQL1 = "SELECT CodeArticle," + _
                          "Designation," + _
                          "LibelleForme," + _
                          "PrixVenteTTC " + _
                          " FROM ARTICLE " + _
                          "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                          "WHERE " + _
                          "CodeArticle LIKE '" + gArticles.Columns("CodeArticle").Value + _
                          "' AND Supprime=0 ORDER BY Designation"

            End If
            cmdAchat.Connection = ConnectionServeur
            cmdAchat.CommandText = StrSQL1
            daAchat = New SqlDataAdapter(cmdAchat)
            daAchat.Fill(dsAchat, "ARTICLE")

            If dsAchat.Tables("ARTICLE").Rows.Count > 0 Then
                dr = dsAchat.Tables("ARTICLE").Rows(0)
            End If

            With gListeRecherche
                .Columns.Clear()
                .DataSource = dsAchat
                .DataMember = "ARTICLE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code Article"
                .Columns("Designation").Caption = "Designation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("PrixVenteTTC").Caption = "Prix de vente"

                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' CentreR tous les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Visible = False
                Next

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("CodeArticle").Visible = True
                .Splits(0).DisplayColumns("Designation").Visible = True
                .Splits(0).DisplayColumns("LibelleForme").Visible = True
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("Designation").Width = 300
                .Splits(0).DisplayColumns("LibelleForme").Width = 100
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120


                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.Aqua
                .Splits(0).DisplayColumns("LibelleForme").Style.BackColor = Color.Aqua
                .Splits(0).DisplayColumns("PrixVenteTTC").Style.BackColor = Color.Aqua

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 20
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
            End With
        End If
    End Sub
    Public Function RecupereNumero()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max([NumeroACHAT]) FROM [ACHAT] WHERE SUBSTRING (NumeroACHAT,0,5)=YEAR(getdate())"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If
        Return ValeurRetour
    End Function

    Private Sub gArticles_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyUp
        Dim i As Integer = 0
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim reponse As New MsgBoxResult

        'Dim TestNumeroLot As Integer
        Dim QuantiteAAjouter As Integer = 0
        '---------------------------------- test si on est en mode saisi ou non ---------------------------
        If mode <> "Ajout" And mode <> "Modif" Then
            Exit Sub
        End If

        If e.KeyCode = Keys.F2 Then
            cmbFournisseur.Focus()
            Exit Sub
        End If

        If gArticles.Columns("CodeArticle").Value = "" And (e.KeyCode = Keys.Right Or e.KeyCode = Keys.Left) Then
            gArticles.Col = 1
            gArticles.EditActive = True
            Exit Sub
        End If

        '--------------------- si entrer dans la colonne designation dans une ligne vide retour au code
        If e.KeyCode = Keys.Enter And gArticles.Columns("Designation").Value = "" And gArticles.Col <> 1 Then
            gArticles.Col = 1
            gArticles.EditActive = True
            Exit Sub
        End If

        '-------------------- si f1 c'est afficher la fiche article ou la fenêtre de recherche multicritère
        If e.KeyCode = Keys.F1 And mode = "Ajout" And gArticles.Columns("CodeArticle").Value <> "" Then
            AfficherFicheArticle(gArticles.Columns("CodeArticle").Value, gArticles.Columns("Stock").Value, gArticles.Columns("Designation").Value)
            Exit Sub
        End If

        If e.KeyCode = Keys.F1 And mode = "Ajout" And gArticles.Columns("CodeArticle").Value = "" Then
            Dim RechercheMulticritere As New fRechercheArticleMultiCritere

            RechercheMulticritere.ShowDialog()

            CodeArticleRechercheMC = RechercheMulticritere.CodeArticleRecherche

            gArticles.MoveLast()
            gArticles.Col = 1
            gArticles.Columns("CodeArticle").Value = CodeArticleRechercheMC
            ChargerDetailArticle(CodeArticleRechercheMC)
            gArticles.Col = 2

            RechercheMulticritere.Close()
            RechercheMulticritere.Dispose()
            gArticles.EditActive = True
        End If

        '---------------------------------- suppression du dernier ligne vide si on a deux lignes au mm temps vide
        '---------------------------------- cas ou on supprime dernier ligne
        If dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 = 1 And gArticles(0, "CodeArticle") = "" And gArticles(1, "CodeArticle") = "" Then
            gArticles.MoveLast()
            gArticles.MovePrevious()
            gArticles.Delete()
        End If

        '---------------------------------- test du type de la valeur d'entrée dans la colonne quantité (numéric) ------------
        '---------------------------------- test du  valeur d'entrée dans la colonne quantité < 0 à éliminer ------------
        If gArticles.Col = 4 Then
            If gArticles.Columns("Qte").Value.ToString <> "-" Then   ' pour les quantité négatives
                If IsNumeric(gArticles.Columns("Qte").Value) = False Then
                    gArticles.Columns("Qte").Value = "1"
                    gArticles.Col = 4
                    gArticles.EditActive = False
                    Exit Sub
                End If
                If (gArticles.Columns("Qte").Value = 0) And e.KeyCode = Keys.Enter Then
                    gArticles.Columns("Qte").Value = ""
                    gArticles.Col = 4
                    gArticles.EditActive = True
                    Exit Sub
                End If
                If (gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999) And e.KeyCode = Keys.Enter Then
                    gArticles.Columns("Qte").Value = "1"
                    MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Col = 4
                    gArticles.EditActive = True
                    Exit Sub
                End If
            End If
        End If
        '---------------------------------- interdiction de l'insertion de double point dans le prix achat 
        '---------------------------------- et l affichage de 3 chiffres apres la virgule
        If gArticles.Col = 10 Then

            '*******************************************************************************
            Dim Position As Integer = 0
            If e.KeyCode = Keys.Decimal Or e.KeyCode = Keys.OemPeriod Then
                Position = InStr(gArticles.Columns("PrixAchatHT").Value.Substring(0, gArticles.Columns("PrixAchatHT").Value.ToString.Length - 1), ".")
                If Position <> 0 Then
                    gArticles.Columns("PrixAchatHT").Value = ""
                    gArticles.EditActive = True
                    gArticles.Col = 10
                End If
                Exit Sub
            End If
        End If

        If e.KeyCode = Keys.Enter And gArticles.Col = 6 And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            If gArticles.Columns("DatePeremption").Value < System.DateTime.Now Then
                MsgBox("Date de péremption périmé !", MsgBoxStyle.Critical, "Erreur")
                gArticles.EditActive = False
                gArticles.Columns("DatePeremption").Value = ""
                gArticles.Col = 6
                gArticles.EditActive = False
                Exit Sub
            End If
        End If
        '------- interdire l insertion d un lot avec un numero et sans une date de peremp ------------
        If gArticles.Col = 10 And e.KeyCode = Keys.Enter Then
            If gArticles.Columns("DatePeremption").Value.ToString() = "" And gArticles.Columns("NumeroLotArticle").Value.ToString() <> "" Then
                MsgBox("Date de péremption manquant !", MsgBoxStyle.Critical, "Erreur")
                gArticles.Col = 6
                Exit Sub
            End If

        End If
        '-------- afféctation du num de lot pour la date de péremption si elle existe deja DANS LA BASE ------
        If e.KeyCode = Keys.Enter And gArticles.Col = 6 Then
            If gArticles.Columns("DatePeremption").Value.ToString <> "" Then
                StrSQL = " SELECT count(NumeroLotArticle) FROM [LOT_ARTICLE] WHERE DatePeremptionArticle='" + _
                gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
                gArticles.Columns("CodeArticle").Value + "'"
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                If cmd.ExecuteScalar <> 0 Then

                    StrSQL = " SELECT NumeroLotArticle FROM [LOT_ARTICLE] WHERE DatePeremptionArticle='" + _
                             gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
                             gArticles.Columns("CodeArticle").Value + "'"
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL
                    gArticles.Columns("NumeroLotArticle").Value = cmd.ExecuteScalar
                Else
                    gArticles.Columns("NumeroLotArticle").Value = ""
                End If
            End If
        End If
        '-------- afféctation du num de lot pour la date de péremption si elle existe deja DANS LA LISTE -----
        If gArticles.Col = 6 And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            Dim CodeNewArticle As String = ""
            Dim DateNewArticle As Date

            CodeNewArticle = gArticles.Columns("CodeArticle").Value
            DateNewArticle = gArticles.Columns("DatePeremption").Value

            If gArticles.Columns("NumeroLotArticle").Value = "" Then
                i = 0
                Do While i < gArticles.RowCount - 1
                    If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
                        If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") = DateNewArticle Then
                            gArticles.Columns("NumeroLotArticle").Value = gArticles(i, "NumeroLotArticle")
                            gArticles.Col = 6
                            gArticles.EditActive = True
                        End If
                    End If
                    i = i + 1
                Loop
            End If
        End If
        '--------------------------- test de l'existance d'un lot avec cette date de péremption -----
        If e.KeyCode = Keys.Enter And gArticles.Col = 7 Then
            If gArticles.Columns("DatePeremption").Value.ToString <> "" And gArticles.Columns("NumeroLotArticle").Value.ToString <> "" Then
                StrSQL = " SELECT count(NumeroLotArticle) FROM [LOT_ARTICLE]WHERE DatePeremptionArticle='" + _
                         gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
                         gArticles.Columns("CodeArticle").Value + "'"
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                If cmd.ExecuteScalar <> 0 Then
                    StrSQL = " SELECT NumeroLotArticle FROM [LOT_ARTICLE]WHERE DatePeremptionArticle='" + _
                             gArticles.Columns("DatePeremption").Value.ToString + "' AND CodeArticle='" + _
                             gArticles.Columns("CodeArticle").Value + "'"
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    If gArticles.Columns("NumeroLotArticle").Value <> cmd.ExecuteScalar Then
                        MsgBox("Date de péremption existe pour un autre lot !", MsgBoxStyle.Critical, "Erreur")
                        gArticles.Columns("DatePeremption").Value = ""
                        gArticles.Columns("NumeroLotArticle").Value = ""
                        gArticles.Col = 6
                        gArticles.EditActive = False
                        Exit Sub
                    End If
                End If
            End If
        End If

        '---------------------------------- test de l'existance du numero de lot pour une autre date --------
        If (gArticles.Col = 5 Or gArticles.Col = 7) And e.KeyCode = Keys.Enter Then
            StrSQL = " SELECT COUNT(NumeroLotArticle) FROM [LOT_ARTICLE] WHERE NumeroLotArticle='" + _
                gArticles.Columns("NumeroLotArticle").Value.ToString + "' AND CodeArticle='" + _
                gArticles.Columns("CodeArticle").Value + "' AND DatePeremptionArticle <> '" + _
                gArticles.Columns("DatePeremption").Value + "'"
            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            If cmd.ExecuteScalar() <> 0 Then
                MsgBox("Numero de lot existe déja !", MsgBoxStyle.Critical, "Erreur")
                gArticles.Columns("NumeroLotArticle").Value = ""
                gArticles.Columns("DatePeremption").Value = ""
                gArticles.Col = 6
                gArticles.EditActive = False
                Exit Sub
            End If
        End If

        '--------------- test si le mm numero du lot existe dans la liste au dessus pour le mm article mais 
        '--------------- avec une date de péremption differente et vise versa--------
        If (gArticles.Col = 7 Or gArticles.Col = 10) And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            Dim CodeNewArticle As String = ""
            Dim DateNewArticle As Date
            Dim NumeroLotNewArticle As String = ""

            CodeNewArticle = gArticles.Columns("CodeArticle").Value
            DateNewArticle = gArticles.Columns("DatePeremption").Value
            NumeroLotNewArticle = gArticles.Columns("NumeroLotArticle").Value.ToString
            Dim QteNewArticle As Integer = 0
            '-------------------------- mm code mm numero de lot mais date different
            i = 0
            Do While i < gArticles.RowCount - 1
                If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
                    If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") <> DateNewArticle And gArticles(i, "NumeroLotArticle") = NumeroLotNewArticle Then
                        MsgBox("Numero de lot existe dans la liste pour le mm article mais avec une autre date de péremption!", MsgBoxStyle.Critical, "Erreur")
                        gArticles.Columns("NumeroLotArticle").Value = ""
                        gArticles.Columns("DatePeremption").Value = ""
                        gArticles.Col = 6
                        gArticles.EditActive = False
                        Exit Sub
                    End If
                End If
                i = i + 1
            Loop
            '-------------------------- mm code mm date de lot mais numero different
            i = 0
            Do While i < gArticles.RowCount - 1
                If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
                    If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") = DateNewArticle And gArticles(i, "NumeroLotArticle") <> NumeroLotNewArticle Then
                        MsgBox("Date de péremption existe dans la liste pour le mm article mais avec un autre numéro de lot!", MsgBoxStyle.Critical, "Erreur")
                        gArticles.Columns("DatePeremption").Value = ""
                        gArticles.Columns("NumeroLotArticle").Value = ""
                        gArticles.Col = 6
                        gArticles.EditActive = False
                        Exit Sub
                    End If
                End If
                i = i + 1
            Loop
        End If

        '--------------- test de l'existance du mm article avec la mm date au dessus dans la 
        '--------------- liste (cas ou on a une date non null)
        If (gArticles.Col = 7 Or gArticles.Col = 10) And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = False Then
            Dim CodeNewArticle As String = ""
            Dim DateNewArticle As Date
            Dim NumeroLotNewArticle As String = ""
            Dim QteNewArticle As Integer = 0

            CodeNewArticle = gArticles.Columns("CodeArticle").Value
            DateNewArticle = gArticles.Columns("DatePeremption").Value
            NumeroLotNewArticle = gArticles.Columns("NumeroLotArticle").Value.ToString
            QteNewArticle = gArticles.Columns("Qte").Value

            i = 0
            Do While i < gArticles.RowCount - 1
                If IsDBNull(gArticles(i, "DatePeremption")) = False And IsDBNull(gArticles(i, "NumeroLotArticle")) = False Then
                    If gArticles(i, "CodeArticle") = CodeNewArticle And gArticles(i, "DatePeremption") = DateNewArticle And gArticles(i, "NumeroLotArticle") = NumeroLotNewArticle And i <> gArticles.Row Then
                        gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QteNewArticle).ToString
                        gArticles.MoveLast()
                        gArticles.Delete()
                        If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                            NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
                            NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                            NouvelArticle("CodeArticle") = ""
                            dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)
                        End If
                    End If
                End If
                i = i + 1
            Loop
        End If

        '--------------- test de l'existance du mm article sans date aussi au dessus dans la 
        '--------------- liste (cas ou on a une date null)

        If (gArticles.Col = 7 Or gArticles.Col = 10) And e.KeyCode = Keys.Enter And IsDBNull(gArticles.Columns("DatePeremption").Value) = True Then
            Dim CodeNewArticle As String = ""
            Dim QteNewArticle As Integer = 0

            CodeNewArticle = gArticles.Columns("CodeArticle").Value
            QteNewArticle = gArticles.Columns("Qte").Value

            i = 0
            Do While i < gArticles.RowCount - 1
                If IsDBNull(gArticles(i, "DatePeremption")) = True And IsDBNull(gArticles(i, "NumeroLotArticle")) = True Then
                    If gArticles(i, "CodeArticle") = CodeNewArticle And i <> gArticles.Row Then
                        gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QteNewArticle).ToString
                        gArticles.MoveLast()
                        gArticles.Delete()
                        If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                            NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
                            NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                            NouvelArticle("CodeArticle") = ""
                            dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)
                        End If
                    End If
                End If
                i = i + 1
            Loop
        End If

        '---------------------------------- test du type de la valeur d'entrée dans la colonne Remise (decimal) ------------

        If gArticles.Col = 10 Then
            If IsNumeric(gArticles.Columns("Remise").Value) = False And gArticles.Columns("Remise").Value.ToString.Contains(".") = False Then
                gArticles.Columns("Remise").Value = ""
                gArticles.Col = 10
                gArticles.EditActive = True
                Exit Sub
            End If
        End If

        '---------------------------------- verouillage des lignes déja confirmées -------------------------


        If (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) And gArticles.Row <> dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 Then
            gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        ElseIf gArticles.Row = dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 Then
            gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = False
            gArticles.Splits(0).DisplayColumns("Designation").Locked = False
        End If

        If gArticles.Col = 1 And gArticles.Columns("Designation").Value <> "" Then
            gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        End If
        If gArticles.Col = 2 And gArticles.Columns("CodeArticle").Value <> "" Then
            gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        End If

        '------------------------------ suppression d'une date de péremption
        If e.KeyCode = Keys.Delete And gArticles.Row <= dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 And gArticles.Col = 6 Then
            gArticles.EditActive = False
            gArticles.Columns("DatePeremption").Value = ""
            'gArticles.EditActive = True
        End If
        '------------------------------ suppression de numéro de lot
        If e.KeyCode = Keys.Delete And gArticles.Row <= dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 And gArticles.Col = 7 Then
            gArticles.EditActive = False
            gArticles.Columns("NumeroLotArticle").Value = ""
            gArticles.EditActive = True
        End If
        '------------------------------ recherche par code ----------------------------------------------
        If gArticles.Col = 1 And e.KeyCode = Keys.Enter And gArticles.Row = dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 Then
            ChargerDetailArticle(gArticles.Columns("CodeArticle").Value)
            VerifierSiDejaSaisi(gArticles.Columns("CodeArticle").Value)
            gArticles.EditActive = True
            Exit Sub
        ElseIf gArticles.Col = 1 And e.KeyCode = Keys.Enter And gArticles.Row < dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 Then
            gArticles.Col = 2
            gArticles.EditActive = True
        End If
        '---------------------------------- masquer la liste de recherche si la designation est vide -----------
        If e.KeyCode = Keys.Back And gArticles.Columns("Designation").Value = "" And gArticles.Col = 2 Then
            gListeRecherche.Visible = False
        End If
        '---------------------------- pour passer à la navigation dans la petite liste de recherhce ----------------
        If (e.KeyCode = Keys.Down Or e.KeyCode = Keys.Up) And gListeRecherche.Visible = True Then
            gListeRecherche.Focus()
            gListeRecherche.Col = 1
            gListeRecherche.Row = 1
        End If
        '---------------------------- si l'artcile n'existe pas dans la liste de recherhce sa quantité doit etre 0  ----
        If dsAchat.Tables("ARTICLE").Rows.Count <= 0 And gListeRecherche.Visible = True Then '
            gArticles.Columns("Qte").Value = 0
            gArticles.Col = 2
            gArticles.EditActive = True
        End If
        '---------------------------- calcul des montants --------------------------------------------------------
        If (gArticles.Col = 4 Or gArticles.Col = 9 Or gArticles.Col = 10) And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) Then
            CalculerMontants()
        End If
        '------------------------------ cas ou on a un changement de prix ---------------------------------------
        If e.KeyCode = Keys.Enter And gArticles.Col = 10 Then
            Dim AncienPrix As Double = 0.0
            Dim ConfirmerChangemant As Boolean = False

            AncienPrix = RecupererValeurExecuteScalaire("PrixAchatHT", "ARTICLE", "CodeArticle", gArticles.Columns("CodeArticle").Value)
            If AncienPrix <> gArticles.Columns("PrixAchatHT").Value Then  ' And InterventionAvecUnAssistant = True


                '********************************* Contrôle de l accée *******************************************
                Dim cmdAssistant As New SqlCommand
                Dim DemandePotDePasse As Boolean = False

                cmdAssistant.Connection = ConnectionServeur
                cmdAssistant.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'INTERVENTION_AVEC_ASSISTANT_DE_CHANGEMANT_DE_PRIX' AND CodeUtilisateur='" + CodeUtilisateur + "'"

                Try
                    If cmdAssistant.ExecuteScalar() >= 1 Then

                        Dim InstanceMaquetteChangerPrix As New fMaquetteChangerPrix
                        fMaquetteChangerPrix.CodeArticle = gArticles.Columns("CodeArticle").Value
                        fMaquetteChangerPrix.NouveauPrix = gArticles.Columns("PrixAchatHT").Value

                        InstanceMaquetteChangerPrix.ShowDialog()

                        ConfirmerChangemant = InstanceMaquetteChangerPrix.ConfirmerChangemant

                        InstanceMaquetteChangerPrix.Dispose()
                        InstanceMaquetteChangerPrix.Close()

                        If ConfirmerChangemant = False Then
                            Exit Sub
                        End If

                    End If
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
                '*********************************************************************

            End If
        End If

        '--------------------- traitement du clique sur le bouton ENTREE selon la colonne ---------------------------

        If e.KeyCode = Keys.Enter And (dsAchat.Tables("ARTICLE").Rows.Count > 0 Or gArticles.Row <> dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1) Then

            If gArticles.Col = 2 Then
                gListeRecherche_KeyUp(sender, e)     ' pour charger les informations de l'article a partir du fiche article
                VerifierSiDejaSaisi(gArticles.Columns("CodeArticle").Value)

            End If

            If gArticles.Col = 2 Then
                If gArticles.Columns("CodeArticle").Value = "" Then    ' si l'article choisit n'existe pas dans la liste de recherche
                    gArticles.Col = 2
                    gArticles.Columns("Designation").Value = ""
                    gArticles.EditActive = True
                Else
                    gArticles.Col = 4
                    gArticles.EditActive = True
                End If

            ElseIf gArticles.Col = 4 And e.KeyCode = Keys.Enter Then

                gArticles.Col = 6
            ElseIf gArticles.Col = 5 Then
                gArticles.Col = 6
            ElseIf gArticles.Col = 6 Then
                gArticles.Col = 7
            ElseIf gArticles.Col = 7 Then
                gArticles.Col = 10
            ElseIf gArticles.Col = 8 Then
                gArticles.Col = 10
            ElseIf gArticles.Col = 9 Then
                gArticles.Col = 10
            ElseIf gArticles.Col = 13 Then
                gArticles.Col = 10
            ElseIf gArticles.Col = 10 Then   ' si on est dans la colonne prix d'achat on passe au nouveau ligne

                If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                    NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
                    NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                    NouvelArticle("CodeArticle") = ""
                    NouvelArticle("NumeroLotArticle") = ""
                    dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)
                End If
                gArticles.MoveLast()
                Try
                    dsAchat.Tables("ARTICLE").Clear()
                Catch ex As Exception
                End Try
                gArticles.Col = 1

            End If
            'If gArticles.Col <> 6 Then
            '    gArticles.EditActive = True
            'End If

        End If
    End Sub

    Public Sub VerifierSiDejaSaisi(ByVal CodeNewArticle As String)

        Dim i As Integer = 0
        Dim reponse As MsgBoxResult
        '---------------------------------- test si l article existe deja dans la liste ou nn
        i = 0
        Do While i < gArticles.RowCount - 1
            If gArticles(i, "CodeArticle") = CodeNewArticle And i <> gArticles.Row Then
                reponse = MsgBox("Article : " + gArticles(i, "Designation") + " existe déja, " + Chr(13) + " OK : pour continuer " + Chr(13) + " ANNULER : pour effacer l article", MsgBoxStyle.OkCancel, "Erreur")
                If reponse = MsgBoxResult.Cancel Then
                    gArticles.MoveLast()
                    gArticles.Delete()
                    If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                        NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
                        NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                        NouvelArticle("CodeArticle") = ""
                        dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)
                    End If
                    Exit Sub
                Else
                    gArticles.Columns("NumeroLotArticle").Value = ""
                    gArticles.EditActive = False
                    gArticles.Columns("DatePeremption").Value = ""
                    gListeRecherche.Visible = False
                End If

            End If
            i = i + 1
        Loop
    End Sub

    Private Sub gListeRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListeRecherche.KeyUp
        ' dans cet évènenment on teste si l évènenment est due à un clique sur le boutton entrée et si l'utilisateur a choisi 
        ' un article et si le curseur est dans la colonne de designation 

        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim DatePeremption As Date
        Dim NumeroLot As String = ""

        Dim CategorieArticle As Integer = 0

        If gListeRecherche.Visible = False Then
            Exit Sub
        End If
        If e.KeyCode = Keys.Back Then
            gArticles.Focus()
            gArticles.Col = 2
            gArticles.MoveLast()
            gArticles.EditActive = True
        End If

        Dim j As Integer
        Dim NumeroLigne As Integer
        Dim DataRowRecherche As DataRow
        If e.KeyCode = Keys.Enter And (gArticles.Col = 4 Or gArticles.Col = 2) Then    'And gArticles.Columns("Designation").Value <> ""
            If dsAchat.Tables("ARTICLE").Rows.Count > 0 Then
                '--------------------- rehcerche de l'index de la ligne qui contient l'article recherché ---------------
                For j = 0 To dsAchat.Tables("ARTICLE").Rows.Count - 1
                    DataRowRecherche = dsAchat.Tables("ARTICLE").Rows(j)
                    If DataRowRecherche.Item("CodeArticle") = gListeRecherche.Columns("CodeArticle").Value Then
                        NumeroLigne = j
                    End If
                Next
                '------------------- chargement des données ---------------------------------------------- 
                dr = dsAchat.Tables("ARTICLE").Rows(NumeroLigne)
                NouvelArticle("NumeroAchat") = RecupereNumero()

                '---------------------- les préparations ne sont pas autorisées
                CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                If CategorieArticle = 9 Then
                    MsgBox("Saisie des préparations interdite !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Columns("CodeArticle").Value = ""
                    gArticles.Columns("Designation").Value = ""
                    gArticles.Col = 1
                    gArticles.EditActive = True
                    Exit Sub
                End If

                NouvelArticle("CodeArticle") = dr.Item("CodeArticle")
                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                Try
                    NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
                Catch ex As Exception

                End Try

                NouvelArticle("Qte") = 1
                NouvelArticle("QteCommander") = 0
                ' NouvelArticle("DateDePeremption") = System.DateTime.Now
                NouvelArticle("Stock") = CalculeStock(dr.Item("CodeArticle"))
                NouvelArticle("Remise") = 0
                NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("TotalAchatHT") = NouvelArticle("PrixAchatHT") '* dr.Item("PrixVenteTTC")
                NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))

                '----------------------- récupération de la date de péremption et le numéro de lot

                StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] " + _
                         "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                         "' AND CodeArticle='" + dr.Item("CodeArticle") + _
                         "' Order by DatePeremptionArticle DESC "

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    DatePeremption = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If DatePeremption = #12:00:00 AM# Then
                Else
                    NouvelArticle("DatePeremption") = DatePeremption
                End If


                StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
                         "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                         "' AND CodeArticle='" + dr.Item("CodeArticle") + _
                         "' Order by DatePeremptionArticle DESC "

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    NumeroLot = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If NumeroLot = "" Then
                Else
                    NouvelArticle("NumeroLotArticle") = NumeroLot
                End If


                gArticles.Refresh()
            End If
            gListeRecherche.Visible = False
            gArticles.Focus()
            If NumeroLigne = 0 Then
                gArticles.Col = 2
            Else
                gArticles.Col = 4
            End If

        End If
    End Sub

    Public Sub ChargerDetailArticle(ByVal CodeArticle As String)
        Dim resultat As String = ""
        Dim Supprime As String = ""
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand

        Dim DatePeremption As Date
        Dim NumeroLot As String = ""

        Dim CategorieArticle As Integer = 0

        If CodeArticle = "" Then
            gArticles.Columns("CodeArticle").Value = ""
            gArticles.Col = 2
            Exit Sub
        End If

        resultat = RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle)
        Supprime = RecupererValeurExecuteScalaire("Supprime", "ARTICLE", "CodeArticle", CodeArticle)

        If resultat <> "" And Supprime = "False" Then

            CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", CodeArticle)
            If CategorieArticle = 9 Then
                MsgBox("Saisie des préparations interdite !", MsgBoxStyle.Critical, "Erreur")
                gArticles.Columns("CodeArticle").Value = ""
                gArticles.Col = 2
                Exit Sub
            End If

            NouvelArticle("NumeroAchat") = RecupereNumero()
            NouvelArticle("CodeArticle") = CodeArticle
            NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)

            Try
                NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
            Catch ex As Exception
            End Try

            NouvelArticle("Qte") = 1
            NouvelArticle("QteCommander") = 0

            'NouvelArticle("DatePeremption") = System.DateTime.Now
            NouvelArticle("Stock") = CalculeStock(CodeArticle)
            NouvelArticle("Remise") = 0

            NouvelArticle("PrixAchatHT") = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", CodeArticle)
            NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", CodeArticle)
            NouvelArticle("TotalAchatHT") = NouvelArticle("PrixAchatHT")
            NouvelArticle("TVA") = RecupererValeurExecuteScalaire("TVA", " ARTICLE", "CodeArticle", CodeArticle)

            If AfficherLesDerniereDDPeremptionDansNouveauAvoirAchat = True Then
                '----------------------- récupération de la date de péremption et du numéro de lot
                StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] " + _
                         "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                         "' AND CodeArticle='" + CodeArticle + _
                         "' Order by DatePeremptionArticle DESC "

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    DatePeremption = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If DatePeremption = #12:00:00 AM# Then
                Else
                    NouvelArticle("DatePeremption") = DatePeremption
                End If



                StrSQL = " SELECT top(1) NumeroLotArticle FROM [LOT_ARTICLE] " + _
                        "WHERE DatePeremptionArticle >'" + System.DateTime.Now.Date.ToString + _
                        "' AND CodeArticle='" + CodeArticle + _
                        "' Order by DatePeremptionArticle DESC "

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    NumeroLot = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If NumeroLot = "" Then
                Else
                    NouvelArticle("NumeroLotArticle") = NumeroLot
                End If
            End If

            gArticles.Refresh()
            gArticles.Col = 4
        Else
            gArticles.Columns("CodeArticle").Value = ""
            gArticles.Col = 2
        End If
    End Sub
    Public Sub CalculerMontants()
        Dim i As Integer = 0
        TotalTTCAchat = 0.0
        TotalHTNETAchat = 0.0
        TotalRemiseAchat = 0.0
        TotalTVAAchat = 0.0
        TotalVenteTTCAchat = 0.0
        TotalQte = 0
        TotalRemiseAchat = 0.0

        Dim MontantHTLigne As Double = 0.0
        Dim TVALigne As Double = 0.0
        Dim RemiseLigne As Double = 0.0

        Dim TotalArticleCourant As Double = 0.0

        Do While i < gArticles.RowCount
            If gArticles(i, "Designation") <> "" Then
                If gArticles(i, "PrixAchatHT").ToString = "" Then
                    gArticles(i, "PrixAchatHT") = 0
                End If
                gArticles(i, "TotalAchatHT") = Math.Round((gArticles(i, "PrixAchatHT") * gArticles(i, "Qte")) - ((gArticles(i, "PrixAchatHT") / 100) * gArticles(i, "Remise") * gArticles(i, "Qte")), 3)

                TotalArticleCourant = gArticles(i, "TotalAchatHT")  'Math.Round((gArticles(i, "PrixAchatHT") * gArticles(i, "Qte")) - ((gArticles(i, "PrixAchatHT") / 100) * gArticles(i, "Remise") * gArticles(i, "Qte")), 3)

                TotalRemiseAchat = TotalRemiseAchat + Math.Round(((gArticles(i, "PrixAchatHT") / 100) * gArticles(i, "Remise") * gArticles(i, "Qte")), 3)

                TotalHTNETAchat = Math.Round(TotalHTNETAchat + TotalArticleCourant, 3)

                MontantHTLigne = (gArticles(i, "PrixAchatHT") * gArticles(i, "Qte"))
                TVALigne = gArticles(i, "TVA")
                RemiseLigne = Math.Round((gArticles(i, "Remise") / 100) * gArticles(i, "Qte"), 3)
                TotalTTCAchat = TotalTTCAchat + Math.Round(((MontantHTLigne * (100 - gArticles(i, "Remise")) / 100) * ((100 + TVALigne) / 100)), 3)

                TotalTVAAchat = TotalTVAAchat + Math.Round(((MontantHTLigne * (100 - RemiseLigne) / 100) * ((TVALigne) / 100)), 3)
                TotalVenteTTCAchat = TotalVenteTTCAchat + (gArticles(i, "PrixVenteTTC") * gArticles(i, "Qte"))
                TotalQte = TotalQte + gArticles(i, "Qte")
            End If
            i = i + 1
        Loop

        lHTNet.Text = TotalHTNETAchat.ToString
        lTotHT.Text = (TotalHTNETAchat + TotalRemiseAchat).ToString
        lTotalTTC.Text = (TotalTTCAchat + CDbl(tAutre.Text)).ToString
        lTotalQte.Text = TotalTVAAchat.ToString
        lValeurVenteTTC.Text = TotalVenteTTCAchat.ToString
        lTotalQte.Text = TotalQte.ToString
        lTotalTVA.Text = TotalTVAAchat.ToString

        lRemise.Text = TotalRemiseAchat.ToString

    End Sub

    Private Sub bFournisseur_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        cmbFournisseur.Focus()
    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim I As Integer = 0
        Dim cmd As New SqlCommand
        Dim DaTestFractionnement As New SqlDataAdapter
        'Dim DsTestFractionnement As New DataSet

        Dim QteUnitaireArticle As Integer = 0
        Dim CategorieArticle As Integer = 0
        Dim PreparationArticle As Integer = 0

        Dim NumeroLot As Integer = 0
        Dim NumeroLot1 As Integer = 0
        Dim TestNumeroLot As String = ""
        Dim NouveauNumeroLot As String = ""

        Dim QuantiteLotSansNumero As Integer = 0
        Dim QuantiteLotAInsere As Integer = 0
        Dim NouvelleQuantiteDuLotNegative As Integer = 0

        Dim StrSQL As String = ""
        Dim DatePeremptionAEnregistrer As String = ""
        Dim AchatAvecLeMemeNumeroBl As Integer = 0

        Dim NumeroLotExistePourLeMmArticle As Integer = 0
        Dim NumeroLotExistePourLaDateIntroduite As String = ""

        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        ''Debut du Transactionnel
        'TransactionAchat = ConnectionServeur.BeginTransaction("BuilderTransaction")
        'cmdAchat.Transaction = TransactionAchat
        'cmd.Transaction = TransactionAchat

        If mode = "Modif" Then

            SupprimerTransactionArticlesPourModification()
            CalculerMontants()

            With gArticles
                .Columns.Clear()
                Try
                    .DataSource = dsAchat
                Catch ex As Exception
                End Try
                .DataMember = "ACHAT_DETAILS"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code article"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Qte").Caption = "Qte"
                .Columns("QteCommander").Caption = " Qte Cmd"
                .Columns("NumeroLotArticle").Caption = "Numero Lot"
                .Columns("DatePeremption").Caption = "Date de péremption"
                .Columns("Stock").Caption = "Stock"
                .Columns("Remise").Caption = "Remise"
                .Columns("PrixAchatHT").Caption = "Prix A HT "
                .Columns("PrixVenteTTC").Caption = "Prix V TTC "
                .Columns("TotalAchatHT").Caption = "Total A HT"
                .Columns("TVA").Caption = "TVA"

                ' Centrer toutes les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Locked = True
                Next

                .Splits(0).DisplayColumns("CodeArticle").Locked = False
                .Splits(0).DisplayColumns("Designation").Locked = False
                .Splits(0).DisplayColumns("Qte").Locked = False
                .Splits(0).DisplayColumns("Remise").Locked = False
                .Splits(0).DisplayColumns("NumeroLotArticle").Locked = False
                .Splits(0).DisplayColumns("DatePeremption").Locked = False
                .Splits(0).DisplayColumns("PrixAchatHT").Locked = False

                .Splits(0).DisplayColumns("NumeroAchat").Width = 0
                .Splits(0).DisplayColumns("NumeroAchat").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").Width = 80
                .Splits(0).DisplayColumns("Designation").Width = 230
                .Splits(0).DisplayColumns("LibelleForme").Width = 80
                .Splits(0).DisplayColumns("Qte").Width = 50
                .Splits(0).DisplayColumns("QteCommander").Width = 50
                .Splits(0).DisplayColumns("NumeroLotArticle").Width = 70
                .Splits(0).DisplayColumns("DatePeremption").Width = 70
                .Splits(0).DisplayColumns("Stock").Width = 50
                .Splits(0).DisplayColumns("Remise").Width = 50
                .Splits(0).DisplayColumns("PrixAchatHT").Width = 60
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 60
                .Splits(0).DisplayColumns("TotalAchatHT").Width = 60
                .Splits(0).DisplayColumns("TVA").Width = 50
                .Splits(0).DisplayColumns("CodeForme").Width = 50
                .Splits(0).DisplayColumns("CodeForme").Visible = False

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near

                .Splits(0).DisplayColumns("CodeArticle").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("Remise").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
            End With

        End If

        NumeroAchat = RecupereNumero()

        If tTotalHT.Text = "" Then
            tTotalHT.Text = "0.000"
        End If

        'test si achat vide
        If dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle") = "" Then
            MsgBox("Achat Vide !", MsgBoxStyle.Critical, "Erreur")
            If dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 < 0 Then
                bAjouter_Click(sender, e)
            End If
            '----------------------
            gArticles.Col = 1
            gArticles.EditActive = True
            Exit Sub
        End If

        '--------------------------- pour verifier le calcul : si l'utilisateur ne clique pas entree
        '--------------------------- sur la cellule qte du dernier ligne la somme TTC sera fausse
        CalculerMontants()
        '--------------------------------------------------------------------------------------

        If cmbFournisseur.Text = "" Then
            MsgBox("Veuillez choisir un Fournisseur !", MsgBoxStyle.Critical, "Erreur")
            cmbFournisseur.Focus()
            Exit Sub
        End If
        If tNumeroBlFact.Text = "" Then
            MsgBox("Veuillez saisir un numero de Facture !", MsgBoxStyle.Critical, "Erreur")
            tNumeroBlFact.Focus()
            Exit Sub
        End If
        If cmbDateBlFacture.Text = "" Then
            MsgBox("Veuillez saisir la date de la Facture !", MsgBoxStyle.Critical, "Erreur")
            cmbDateBlFacture.Focus()
            Exit Sub
        End If
        If CDbl(tTotalHT.Text) <> CDbl(lHTNet.Text) Then
            MsgBox("Total Hors Taxe saisi est différent du Total Hors Taxe Calculé !", MsgBoxStyle.Critical, "Erreur")
            tTotalHT.Focus()
            Exit Sub
        End If

        '------------------------------ verification si le fournisseur admet le même numero du BL 
        StrSQL = " SELECT COUNT(NumeroAchat) FROM [ACHAT] " + _
                 "WHERE CodeFournisseur ='" + cmbFournisseur.SelectedValue + _
                 "' AND [NumeroBL/Facture]='" + tNumeroBlFact.Text + "'"

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL

        Try
            AchatAvecLeMemeNumeroBl = cmd.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        If AchatAvecLeMemeNumeroBl > 0 And mode = "Ajout" Then
            MsgBox("Numéro du BL déja utilisé par le même fournisseur !", MsgBoxStyle.Critical, "Erreur")
            tNumeroBlFact.Value = ""
            tNumeroBlFact.Focus()
            Exit Sub
        End If

        '----------------------- contrôle des Numeros des lots, codes articles : insertion des doublons
        '----------------------- (Violation du clé primaire dans la table achat details)

        Dim p As Integer = 0
        Dim q As Integer = 0
        For p = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
            For q = p To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
                If gArticles(p, "CodeArticle") = gArticles(q, "CodeArticle") And p <> q And gArticles(p, "CodeArticle") <> "" And gArticles(q, "CodeArticle") <> "" Then
                    If gArticles(p, "DatePeremption").ToString = gArticles(q, "DatePeremption").ToString Then
                        MsgBox("l'article " + gArticles(p, "Designation") + " apparaît deux fois avec le même numero de lot ! veuillez supprimer un et ajouter sa quantité dans l'autre", MsgBoxStyle.Critical, "Erreur")
                        Exit Sub
                    End If
                End If
            Next
        Next

        '-------------------- contrôle si on a un lot avec un numéro et sans date de péremption 
        For p = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
            If IsDBNull(gArticles(p, "NumeroLotArticle")) = False Then
                If gArticles(p, "NumeroLotArticle") <> "" And gArticles(q, "DatePeremption") = "" Then
                    gArticles(p, "NumeroLotArticle") = ""
                End If
            End If
        Next

        '----------------------- contrôle des dates de péremption si il y a un qui est périmé

        For p = 0 To gArticles.RowCount - 1
            If gArticles(p, "CodeArticle") <> "" And gArticles(p, "DatePeremption").ToString <> "" Then
                If gArticles(p, "DatePeremption") < Date.Today Then
                    MsgBox("l'article " + dsAchat.Tables("ACHAT_DETAILS").Rows(p).Item("Designation") + " admet une date de péremption deja dépassé ! veuillez corriger sa date de péremption", MsgBoxStyle.Critical, "Erreur")
                    Exit Sub
                End If
            End If
        Next

        '----------------------- ajout des numéros des lots pour ceux qui ont des dates de péremption sans numlot

        For p = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1

            If gArticles(p, "CodeArticle") <> "" And gArticles(p, "DatePeremption").ToString <> "" Then
                If dsAchat.Tables("ACHAT_DETAILS").Rows(p).Item("NumeroLotArticle").ToString = "" Then

                    '------------------ recupération du dernier numéro de lot pour cet article puis incrémentation
                    StrSQL = " SELECT MAX(NumeroLotArticle) FROM [LOT_ARTICLE] WHERE CodeArticle ='" + _
                             dsAchat.Tables("ACHAT_DETAILS").Rows(p).Item("CodeArticle") + "'"
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL
                    Try
                        NouveauNumeroLot = cmd.ExecuteScalar().ToString
                        If NouveauNumeroLot <> "" Then
                            NouveauNumeroLot = (Convert.ToInt32(NouveauNumeroLot) + 1).ToString
                        Else
                            NouveauNumeroLot = 1
                        End If

                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                    For q = p To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
                        If dsAchat.Tables("ACHAT_DETAILS").Rows(p).Item("CodeArticle") = gArticles(q, "CodeArticle") And gArticles(q, "NumeroLotArticle").ToString = "" Then
                            ' ----------- affectation des numero pour ceux qui n'ont pas de numero de lots (incrémentation)
                            dsAchat.Tables("ACHAT_DETAILS").Rows(q).Item("NumeroLotArticle") = NouveauNumeroLot
                            NouveauNumeroLot = NouveauNumeroLot + 1
                        End If

                    Next

                End If

            End If
        Next

        '------------------------------ demande du mot de passe
        Dim myMotDePasse As New fMotDePasse
        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If

        '------------------------------ enregistrement de l'entête de l'achat -------------------------
        '-----------------------------------------------------------------------------------------------
        '-----------------------------------------------------------------------------------------------
        If (dsAchat.Tables.IndexOf("ACHAT") > -1) Then
            dsAchat.Tables("ACHAT").Clear()
        End If

        'Debut du Transactionnel
        TransactionAchat = ConnectionServeur.BeginTransaction("BuilderTransaction")
        cmdAchat.Transaction = TransactionAchat
        cmd.Transaction = TransactionAchat

        If mode = "Modif" Then

            ' cas de modification 
            StrSQL = "SELECT * FROM ACHAT WHERE NumeroAchat='" + lNumeroAchat.Text + "'"
            cmdAchat.Connection = ConnectionServeur
            cmdAchat.CommandText = StrSQL
            daAchat = New SqlDataAdapter(cmdAchat)
            daAchat.Fill(dsAchat, "ACHAT")
            cbAchat = New SqlCommandBuilder(daAchat)
            dr = dsAchat.Tables("ACHAT").NewRow()

            With dsAchat
                .Tables("ACHAT").Rows(0)("NumeroAchat") = lNumeroAchat.Text
                .Tables("ACHAT").Rows(0)("Date") = System.DateTime.Now
                .Tables("ACHAT").Rows(0)("DateBlFacture") = cmbDateBlFacture.Text
                .Tables("ACHAT").Rows(0)("TotalHT") = TotalHTNETAchat + CDbl(lRemise.Text)
                .Tables("ACHAT").Rows(0)("TVA") = lTotalTVA.Text
                .Tables("ACHAT").Rows(0)("TotalTTC") = TotalTTCAchat
                .Tables("ACHAT").Rows(0)("TotalRemise") = lRemise.Text
                .Tables("ACHAT").Rows(0)("Timbre") = Timbre
                .Tables("ACHAT").Rows(0)("CodeFournisseur") = cmbFournisseur.SelectedValue
                .Tables("ACHAT").Rows(0)("CodePersonnel") = CodeOperateur
                .Tables("ACHAT").Rows(0)("Note") = "rien"
                .Tables("ACHAT").Rows(0)("NumeroBL/Facture") = tNumeroBlFact.Text
                .Tables("ACHAT").Rows(0)("LibellePoste") = System.Environment.GetEnvironmentVariable("Poste")
                .Tables("ACHAT").Rows(0)("ValeurVenteTTC") = lValeurVenteTTC.Text
                .Tables("ACHAT").Rows(0)("Autre") = tAutre.Text
            End With
            Try
                daAchat.Update(dsAchat, "ACHAT")
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                dsAchat.Reset()
            End Try

        Else

            ' cas d'un nouveau achat 
            StrSQL = "SELECT top(0) * FROM ACHAT ORDER BY NumeroAchat ASC"
            cmdAchat.Connection = ConnectionServeur
            cmdAchat.CommandText = StrSQL
            daAchat = New SqlDataAdapter(cmdAchat)
            daAchat.Fill(dsAchat, "ACHAT")
            cbAchat = New SqlCommandBuilder(daAchat)
            dr = dsAchat.Tables("ACHAT").NewRow()

            With dsAchat
                dr.Item("NumeroAchat") = NumeroAchat
                dr.Item("Date") = System.DateTime.Now
                dr.Item("DateBlFacture") = cmbDateBlFacture.Text
                dr.Item("TotalHT") = TotalHTNETAchat + CDbl(lRemise.Text)
                dr.Item("TVA") = lTotalTVA.Text
                dr.Item("TotalTTC") = TotalTTCAchat
                dr.Item("TotalRemise") = lRemise.Text
                dr.Item("Timbre") = Timbre
                dr.Item("CodeFournisseur") = cmbFournisseur.SelectedValue
                dr.Item("CodePersonnel") = CodeOperateur
                dr.Item("Note") = "rien"
                dr.Item("NumeroBL/Facture") = tNumeroBlFact.Text
                dr.Item("LibellePoste") = System.Environment.GetEnvironmentVariable("Poste")
                dr.Item("ValeurVenteTTC") = lValeurVenteTTC.Text
                dr.Item("Autre") = tAutre.Text
                dsAchat.Tables("ACHAT").Rows.Add(dr)
            End With
            Try
                daAchat.Update(dsAchat, "ACHAT")
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                dsAchat.Reset()
            End Try
        End If

        '------------------------------ enregistrement des détails de l'achat -------------------------
        '-----------------------------------------------------------------------------------------------
        '-----------------------------------------------------------------------------------------------

        cmdAchat.Connection = ConnectionServeur
        cmdAchat.CommandText = "Select top(0) * FROM ACHAT_DETAILS"
        daAchat = New SqlDataAdapter(cmdAchat)
        daAchat.Fill(dsAchat, "ACHAT_DETAILS")
        cbAchat = New SqlCommandBuilder(daAchat)

        '----------------------------- gestion des lots : si on a un lot qui n a pas de numero de lot et 
        '----------------------------- sans date de péremption on le met à jour sinon création d'un nouveau lot
        I = 0
        Do While I < dsAchat.Tables("ACHAT_DETAILS").Rows.Count And gArticles(I, "CodeArticle") <> ""

            CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", gArticles(I, "CodeArticle"))
            If CategorieArticle = 9 Then
                PreparationArticle = RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", gArticles(I, "CodeArticle"))
            End If

            If CategorieArticle = 8 Or (CategorieArticle = 9 And PreparationArticle = 1) Then
                QteUnitaireArticle = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", gArticles(I, "CodeArticle"))
            Else
                QteUnitaireArticle = 1
            End If

            '------------- correction des lots des articles fractionnés depuis l'article en cours

            Dim StrSQLFractionnement As String = ""
            StrSQLFractionnement = "SELECT CodeFractionnement FROM FRACTIONNEMENT " + _
                                   " WHERE CodeArticleMere='" + gArticles(I, "CodeArticle") + "'"
            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQLFractionnement
            DaTestFractionnement = New SqlDataAdapter(cmd)
            DaTestFractionnement.Fill(dsAchat, "FRACTIONNEMENT")
            If dsAchat.Tables("FRACTIONNEMENT").Rows.Count <> 0 Then
                Dim QuantiteARetrancher As Integer = 0
                QuantiteARetrancher = ReglerArticlesFractionneesNegatives(gArticles(I, "CodeArticle"))
                gArticles(I, "Qte") = gArticles(I, "Qte") - QuantiteARetrancher
            End If
            '-------------------------------------------------------------------------------------

            StrSQL = " SELECT count(NumeroLotArticle) FROM [LOT_ARTICLE] " + _
                     "WHERE NumeroLotArticle ='' AND CodeArticle='" + _
                     gArticles(I, "CodeArticle") + "'"

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                NumeroLot = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            QuantiteLotAInsere = gArticles(I, "Qte") * QteUnitaireArticle
            If NumeroLot <> 0 Then  '-------- il y a un enregistrement déja sans numero de lot 

                '-------------------------- on verifie si la quantité est négative
                StrSQL = " SELECT QteLotArticle FROM [LOT_ARTICLE] where NumeroLotArticle =''" + _
                " AND CodeArticle='" + gArticles(I, "CodeArticle") + "'"
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL
                Try
                    QuantiteLotSansNumero = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If QuantiteLotSansNumero < 0 Then
                    If QuantiteLotAInsere > (-QuantiteLotSansNumero) Then
                        QuantiteLotAInsere = QuantiteLotAInsere + QuantiteLotSansNumero
                        NouvelleQuantiteDuLotNegative = 0
                    Else
                        NouvelleQuantiteDuLotNegative = QuantiteLotAInsere + QuantiteLotSansNumero
                        QuantiteLotAInsere = 0
                    End If
                End If

                If gArticles(I, "NumeroLotArticle").ToString <> "" Then

                    '---------------- verification s'il ya un lot avec le même numero lot 

                    StrSQL = " SELECT count(NumeroLotArticle) FROM [LOT_ARTICLE] " + _
                        "WHERE NumeroLotArticle ='" + _
                        dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle") + _
                        "' AND CodeArticle='" + _
                        dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") + "'"

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        NumeroLot1 = cmd.ExecuteScalar()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                    If NumeroLot1 <> 0 Then
                        StrSQL = "Update LOT_ARTICLE set QteLotArticle=QteLotArticle + " + _
                             QuantiteLotAInsere.ToString + _
                             " ,DatePeremptionArticle ='" + _
                             dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremption") + _
                             "' WHERE NumeroLotArticle = '" + _
                             dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle") + _
                             "' AND CodeArticle ='" + _
                             dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") + "'"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL

                        Try
                            cmd.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try
                    Else
                        StrSQL = "INSERT INTO LOT_ARTICLE " + _
                             "(""NumeroLotArticle"",""CodeArticle"",""QteLotArticle"",""DatePeremptionArticle"") " + _
                             " VALUES('" + _
                             dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle") + _
                             "','" + _
                             dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") + _
                             "'," + _
                             QuantiteLotAInsere.ToString + ",'" + _
                             dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremption") + "')"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try
                    End If

                    If QuantiteLotSansNumero < 0 Then   'si la qte dans le lot sans date est negative
                        '-------------------------- on élimine la quantité déja vendue en négative pour un lot
                        '-------------------------- sans numero
                        StrSQL = "Update LOT_ARTICLE SET QteLotArticle= " + _
                                 (NouvelleQuantiteDuLotNegative * QteUnitaireArticle).ToString + _
                                 " WHERE NumeroLotArticle ='' AND CodeArticle ='" + _
                                 dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") + "'"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL

                        Try
                            cmd.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try

                    End If

                Else
                    '---------------- Mise a jour de l'enregistrement qui a un numero de lot vide 
                    StrSQL = "Update LOT_ARTICLE set QteLotArticle=QteLotArticle + " + _
                             QuantiteLotAInsere.ToString + _
                             " WHERE NumeroLotArticle = '' AND CodeArticle ='" + _
                             gArticles(I, "CodeArticle") + "'"

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try
                End If

            Else
                '---------------- verification s'il y a un lot avec le même numero lot 

                StrSQL = " SELECT count(NumeroLotArticle) FROM [LOT_ARTICLE] " + _
                    "WHERE NumeroLotArticle ='" + _
                    dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle") + _
                    "' AND CodeArticle='" + _
                    dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") + "'"

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    NumeroLot1 = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If NumeroLot1 <> 0 Then
                    StrSQL = "Update LOT_ARTICLE set QteLotArticle=QteLotArticle + " + _
                             (dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Qte") * QteUnitaireArticle).ToString + _
                             " ,DatePeremptionArticle ='" + _
                             dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremption") + _
                             "' WHERE NumeroLotArticle = '" + _
                             dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle") + _
                             "' AND CodeArticle ='" + _
                             dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") + "'"

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                Else
                    If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremption").ToString = "" Then
                        DatePeremptionAEnregistrer = "Null"
                    Else
                        DatePeremptionAEnregistrer = "'" + dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremption").ToString + "'"
                    End If

                    Dim NumeroLotNouveau As String = ""

                    If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle").ToString = "" Then
                        NumeroLotNouveau = ""
                    Else
                        NumeroLotNouveau = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroLotArticle")
                    End If

                    '---------------- un nouvel enregistrement 
                    StrSQL = "INSERT INTO LOT_ARTICLE " + _
                             "(""NumeroLotArticle"",""CodeArticle"",""QteLotArticle"",""DatePeremptionArticle"") " + _
                             " VALUES('" + _
                             NumeroLotNouveau + "','" + _
                             dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") + _
                             "'," + (dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Qte") * QteUnitaireArticle).ToString + "," + _
                              DatePeremptionAEnregistrer + ")"

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                End If

            End If

            '---------------- Changement des situations des articles acheté en ACTIVITE
            StrSQL = "Update ARTICLE set CodeSituation=1 " + _
                     " WHERE CodeArticle = '" + _
                     gArticles(I, "CodeArticle") + "'"

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                cmd.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            I = I + 1
        Loop

        '-------------------------- élémination des lignes vides et ajout des numero achat pour les achat 
        '-------------------------- Depuis commandes 
        I = 0
        Do While I < dsAchat.Tables("ACHAT_DETAILS").Rows.Count
            If gArticles(I, "CodeArticle") = "" Then
                dsAchat.Tables("ACHAT_DETAILS").Rows(I).Delete()
            End If
            Try
                If mode = "Modif" Then
                    dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroAchat") = lNumeroAchat.Text
                Else
                    dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("NumeroAchat") = NumeroAchat
                End If

            Catch ex As Exception
            End Try
            I = I + 1
        Loop

        Try
            daAchat.Update(dsAchat, "ACHAT_DETAILS")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsAchat.Reset()
        End Try

        '-------------------------------- Affectation des numeros de facture au commandes convertis 
        Dim k As Integer = 0
        For k = 0 To TableauCommande.Length - 1
            If TableauCommande(k) <> "" Then
                StrSQL = "UPDATE COMMANDE SET NumeroFacture='" + NumeroAchat + _
                         "' WHERE NumeroCommande = '" + TableauCommande(k) + "'"

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
            End If
        Next

        TransactionAchat.Commit()

        '------------------------ si l'achat est depuis des comandes on effectue les contrôles nécessaires 
        If DepuisCommande = True Then
            DepuisCommande = False
            '------------------------------- recherche s il y a des différences entre les qtes commandées et les 
            '------------------------------- qtes livrées 
            StrSQL = "SELECT NumeroCommande," + _
                     "CodeArticle," + _
                     "Designation," + _
                     "LibelleForme," + _
                     "Qte," + _
                     "Stock," + _
                     "DatePeremption," + _
                     "PrixAchatHT," + _
                     "TVA," + _
                     "TotalTTCAchat," + _
                     "StockAlerte," + _
                     "EnCours," + _
                     "QteACommander," + _
                     "QteUnitaire," + _
                     "PROJET_COMMANDE_DETAILS.CodeForme " + _
                     "FROM " + _
                     "COMMANDE_DETAILS AS PROJET_COMMANDE_DETAILS,FORME_ARTICLE " + _
                     "WHERE PROJET_COMMANDE_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
                     "AND NumeroCommande ='0'"

            cmdAchat.Connection = ConnectionServeur
            cmdAchat.CommandText = StrSQL
            daAchat.Fill(dsAchat, "PROJET_COMMANDE_DETAILS")
            cbAchat = New SqlCommandBuilder(daAchat)

            For I = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
                If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Qte") < dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("QteCommander") Then

                    NouvelArticle = dsAchat.Tables("PROJET_COMMANDE_DETAILS").NewRow()
                    NouvelArticle("Designation") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Designation")
                    NouvelArticle("CodeArticle") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle")
                    NouvelArticle("CodeForme") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeForme")

                    NouvelArticle("Qte") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Qte")
                    NouvelArticle("Stock") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Stock")
                    NouvelArticle("DatePeremption") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremption")
                    NouvelArticle("PrixAchatHT") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("PrixAchatHT")
                    NouvelArticle("TVA") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("TVA")
                    NouvelArticle("TotalTTCAchat") = NouvelArticle("PrixAchatHT") * (1 + NouvelArticle("TVA")) * NouvelArticle("Qte")
                    NouvelArticle("StockAlerte") = RecupererValeurExecuteScalaire("StockAlerte", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                    NouvelArticle("QteACommander") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("QteCommander") 'RecupererValeurExecuteScalaire("QteACommander", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                    NouvelArticle("QteUnitaire") = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

                    dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows.Add(NouvelArticle)
                End If
            Next

            If dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows.Count > 0 Then
                Dim reponse As MsgBoxResult
                reponse = MsgBox("Vous avez des articles manquants dans ces commandes," + Chr(13) + " voulez vous les convertir en projet de commande  !" + Chr(13), MsgBoxStyle.YesNo, "Erreur")
                If reponse = MsgBoxResult.Yes Then
                    Dim J As Integer
                    For J = 0 To fMain.Tab.TabPages.Count - 1
                        If fMain.Tab.TabPages(J).Text = "Projet de Commande" Then
                            fMain.Tab.TabPages(J).Show()
                            Exit Sub
                        End If
                    Next
                    Dim MyProjetComandeReste As New fProjetCommandeResteCommandes
                    fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
                    fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
                    fMain.Tab.SelectedTab.Controls.Add(MyProjetComandeReste.Panel)
                    fMain.Tab.SelectedTab.Text = "Projet de Commande"
                    MyProjetComandeReste.Init()

                    For I = 0 To dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows.Count - 1
                        dr = MyProjetComandeReste.dsCommande.Tables("COMMANDE_DETAILS_PROJET").NewRow

                        'initialisation pour que le calcul des montants ne compare pas 
                        ' des chaines de caractères avec des NULL
                        dr.Item("Fournisseur") = ""

                        dr.Item("CodeArticle") = dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows(I).Item("CodeArticle")
                        dr.Item("Designation") = dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows(I).Item("Designation")
                        dr.Item("Qte") = dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows(I).Item("QteACommander") - dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows(I).Item("Qte")
                        dr.Item("CodeForme") = dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows(I).Item("CodeForme")
                        dr.Item("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", "FORME_ARTICLE", "CodeForme", dr.Item("CodeForme"))
                        dr.Item("Stock") = dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows(I).Item("Stock")
                        dr.Item("DatePeremption") = dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows(I).Item("DatePeremption")
                        dr.Item("PrixAchatHT") = dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows(I).Item("PrixAchatHT")
                        dr.Item("TVA") = dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows(I).Item("TVA")
                        dr.Item("TotalTTCAchat") = dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows(I).Item("TotalTTCAchat")
                        dr.Item("StockAlerte") = dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows(I).Item("StockAlerte")
                        'dr.Item("EnCours") = dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows(I).Item("")
                        dr.Item("QteACommander") = dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows(I).Item("QteACommander")
                        dr.Item("QteUnitaire") = dsAchat.Tables("PROJET_COMMANDE_DETAILS").Rows(I).Item("QteUnitaire")

                        MyProjetComandeReste.dsCommande.Tables("COMMANDE_DETAILS_PROJET").Rows.Add(dr)
                    Next
                    MyProjetComandeReste.gArticles.Refresh()
                End If

            End If
        End If

        Init()
        mode = "Consultation"

        tRemisePourcentage.Visible = False
        lRemisePourcentAfficher.Visible = False
        lTotalQteAffiche.Visible = False
        lTotalQte.Visible = False
        tAutre.Enabled = False

    End Sub
    Private Function ReglerArticlesFractionneesNegatives(ByVal CodeArticle)
        Dim StrSQL As String = ""
        Dim CmdRegler As New SqlCommand
        Dim DaRegler As New SqlDataAdapter
        Dim Dsregler As New DataSet
        Dim i As Integer = 0
        Dim QteLot As Integer = 0
        Dim QteFinal1 As Double = 0.0
        Dim QteFinal As Integer = 0
        Dim QuantiteARetrancher As Integer = 0

        CmdRegler.Transaction = TransactionAchat

        StrSQL = "SELECT CodeFractionnement,QuantiteUnitaireMere,QuantiteUnitaire " + _
                 "FROM FRACTIONNEMENT WHERE CodeArticleMere='" + CodeArticle + "'"
        CmdRegler.Connection = ConnectionServeur
        CmdRegler.CommandText = StrSQL
        DaRegler = New SqlDataAdapter(CmdRegler)
        DaRegler.Fill(Dsregler, "FRACTIONNEMENT")

        For i = 0 To Dsregler.Tables("FRACTIONNEMENT").Rows.Count - 1

            StrSQL = " SELECT QteLotArticle FROM [LOT_ARTICLE] " + _
                     "WHERE CodeArticle='" + _
                     Dsregler.Tables("FRACTIONNEMENT").Rows(i).Item("CodeFractionnement") + "' AND " + _
                     "QteLotArticle<0 "

            CmdRegler.Connection = ConnectionServeur
            CmdRegler.CommandText = StrSQL

            Try
                QteLot = CmdRegler.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If QteLot < 0 Then
                QteFinal1 = Math.Abs(QteLot * Dsregler.Tables("FRACTIONNEMENT").Rows(i).Item("QuantiteUnitaire")) / Dsregler.Tables("FRACTIONNEMENT").Rows(i).Item("QuantiteUnitaireMere")
                QteFinal = Math.Round(QteFinal1, 0, MidpointRounding.AwayFromZero) + 1

                StrSQL = "UPDATE LOT_ARTICLE SET QteLotArticle=QteLotArticle + " + (QteFinal * Dsregler.Tables("FRACTIONNEMENT").Rows(i).Item("QuantiteUnitaireMere") / Dsregler.Tables("FRACTIONNEMENT").Rows(i).Item("QuantiteUnitaire")).ToString + _
                     " WHERE CodeArticle = '" + _
                     Dsregler.Tables("FRACTIONNEMENT").Rows(i).Item("CodeFractionnement") + "' AND " + _
                     "QteLotArticle<0 "

                CmdRegler.Connection = ConnectionServeur
                CmdRegler.CommandText = StrSQL

                Try
                    CmdRegler.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
                QuantiteARetrancher += QteFinal
            End If


        Next
        Return QuantiteARetrancher
    End Function



    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

        If dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 > 0 And gArticles(0, "CodeArticle") <> "" Then
            If MsgBox("Voulez vous vraiment annuler cet achat ?", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "Annuler Achat") = MsgBoxResult.No Then
                Exit Sub
            End If
        End If

        Init()
        mode = "Consultation"

        tRemisePourcentage.Visible = False
        lRemisePourcentAfficher.Visible = False
        lTotalQteAffiche.Visible = False
        lTotalQte.Visible = False
        tAutre.Enabled = False

        gListeRecherche.Visible = False

    End Sub

    Private Sub bRemise_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        tRemisePourcentage.Focus()
    End Sub

    Private Sub bRecherche_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        tRecherche.Visible = True
        tRecherche.Text = System.DateTime.Now.Year.ToString + "/"
        tRecherche.Focus()
        tRecherche.Select(tRecherche.Text.Length, 0)
    End Sub
    Public Sub AfficherFicheFournisseur(ByVal CodeFournisseur)
        Dim j As Integer
        For j = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(j).Text = "Modification du Fournisseur" Then
                fMain.Tab.TabPages(j).Show()
                Exit Sub
            End If
        Next
        Dim MyFournisseur As New fFicheFournisseur

        MyFournisseur.CodeFournisseur = CodeFournisseur
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyFournisseur.Panel)
        fMain.Tab.SelectedTab.Text = "Modification du Fournisseur"
        MyFournisseur.ajoutmodif = "M"
        MyFournisseur.Init()
    End Sub

    Private Sub cmbFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbFournisseur.KeyUp

        If e.KeyCode = Keys.F1 And mode = "Ajout" And cmbFournisseur.Text <> "" Then
            AfficherFicheFournisseur(cmbFournisseur.SelectedValue)
            Exit Sub
        End If

        If e.KeyCode = Keys.Enter Then
            If cmbFournisseur.Text = "" Then
                cmbFournisseur.Focus()
            ElseIf cmbFournisseur.WillChangeToText = "" And cmbFournisseur.Text <> "" Then

                '----------------- nouveau fournisseur 
                Dim reponse As MsgBoxResult
                reponse = MsgBox("Fournisseur " + cmbFournisseur.Text + " inéxistant, voulez vous le créer  ", MsgBoxStyle.OkCancel, "Erreur")
                If reponse = MsgBoxResult.Ok Then

                    Dim MyFournisseur As New fFicheFournisseur
                    MyFournisseur.ajoutmodif = "A"
                    MyFournisseur.NomFournisseur = cmbFournisseur.Text

                    MyFournisseur.Init()
                    MyFournisseur.StartPosition = FormStartPosition.CenterScreen
                    MyFournisseur.tCodeFournisseur.Focus()
                    MyFournisseur.FormBorderStyle = Windows.Forms.FormBorderStyle.None
                    MyFournisseur.ShowDialog()
                    If MyFournisseur.ConfirmerEnregistrementDepuisAchat = False Then
                        cmbFournisseur.Text = ""
                    End If
                    MyFournisseur.Close()
                    MyFournisseur.Dispose()



                    'chargement des fournisseurs
                    Dim StrSQL As String = ""
                    StrSQL = "SELECT CodeFournisseur,NomFournisseur FROM FOURNISSEUR ORDER BY NomFournisseur ASC"
                    cmdChargement.Connection = ConnectionServeur
                    cmdChargement.CommandText = StrSQL
                    daChargement = New SqlDataAdapter(cmdChargement)
                    daChargement.Fill(dsChargement, "FOURNISSEUR")
                    cmbFournisseur.DataSource = dsChargement.Tables("FOURNISSEUR")
                    cmbFournisseur.ValueMember = "CodeFournisseur"
                    cmbFournisseur.DisplayMember = "NomFournisseur"
                    cmbFournisseur.ColumnHeaders = False
                    cmbFournisseur.Splits(0).DisplayColumns("CodeFournisseur").Width = 0
                    cmbFournisseur.Splits(0).DisplayColumns("NomFournisseur").Width = 160
                Else
                    cmbFournisseur.Text = ""
                End If

            Else
                cmbFournisseur.Text = cmbFournisseur.WillChangeToText

                tNumeroBlFact.Focus()


            End If
        Else
            cmbFournisseur.OpenCombo()
        End If




        If e.KeyCode = Keys.Enter Then
            tNumeroBlFact.Focus()
        End If
    End Sub


    Private Sub cmbFournisseur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbFournisseur.TextChanged
        If cmbFournisseur.SelectedValue <> Nothing Then
            Dim StrSQLdernierAchat As String = ""
            Dim CmdCalcul As New SqlCommand
            Dim Dernier_Date_Achat As String = ""
            Dim Dernier_Date_Reglement As String = ""
            Dim StrSQLSolde As String = ""
            Dim Somme_Facture As Double = 0.0
            Dim Somme_Reglement As Double = 0.0
            Dim Somme_Echeance As Double = 0.0
            Dim difference As Double = 0.0
            Dim StrMatricule As String = ""


            ' récupération de la dernière date d'achat pour le client concerné 
            StrSQLdernierAchat = "SELECT MAX(Date) FROM ACHAT WHERE CodeFournisseur =" + Quote(cmbFournisseur.SelectedValue)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLdernierAchat

            Try
                Dernier_Date_Achat = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            ' récupération de la dernière date de règlement pour le client concerné 
            StrSQLdernierAchat = "SELECT MAX(Date) FROM REGLEMENT_FOURNISSEUR WHERE CodeFournisseur =" + Quote(cmbFournisseur.SelectedValue)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLdernierAchat

            Try
                Dernier_Date_Reglement = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            'calcul du solde client en retranchant la somme des montants des règlements de la somme des montants des ventes 
            StrSQLSolde = "SELECT SUM(TotalTTC) FROM ACHAT WHERE CodeFournisseur =" + Quote(cmbFournisseur.SelectedValue)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLSolde

            Try
                Somme_Facture = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            StrSQLSolde = "SELECT SUM(Montant) FROM REGLEMENT_FOURNISSEUR WHERE CodeFournisseur =" + Quote(cmbFournisseur.SelectedValue)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLSolde

            Try
                Somme_Reglement = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            ' récupération des montants des règlements antidaté nn encaissé
            StrSQLSolde = "SELECT SUM(Montant) " + _
                          "FROM REGLEMENT_FOURNISSEUR LEFT OUTER JOIN NATURE_REGLEMENT ON " + _
                          " REGLEMENT_FOURNISSEUR.CodeNatureReglement=NATURE_REGLEMENT.CodeNatureReglement" + _
                          "WHERE CodeFournisseur =" + _
                          Quote(cmbFournisseur.SelectedValue) + " AND DateEcheance > '" + _
                          System.DateTime.Now.Date.ToString + _
                          "' AND NATURE_REGLEMENT.LibelleNatureReglement='CHEQUE' "

            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLSolde

            Try
                Somme_Echeance = CmdCalcul.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            '------------------------------------------------------------------------------
            '-------------------------------- afféctation des valeurs ---------------------


            difference = Somme_Facture - Somme_Reglement

        End If
    End Sub

    Private Sub bLast_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim j As Integer
        Dim DataRowRecherche As DataRow
        Dim NumeroLigne As Integer
        For j = 0 To dsChargement.Tables("ACHAT").Rows.Count - 1
            DataRowRecherche = dsChargement.Tables("ACHAT").Rows(j)
            If DataRowRecherche.Item("NumeroAchat") = NumeroAchat Then
                NumeroLigne = j
            End If
        Next

        If dsChargement.Tables("ACHAT").Rows.Count - 1 > 0 Then
            DataRowRecherche = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)

            'chargement des informations entête
            lNumeroAchat.Text = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("NumeroAchat")
            lDateAchat.Text = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("Date")
            cmbFournisseur.SelectedValue = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("CodeFournisseur")
            cmbDateBlFacture.Value = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("DateBlFacture")

            tNumeroBlFact.Value = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("NumeroBL/Facture").ToString

            TotalTTCAchat = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("TotalTTC")
            TotalHTNETAchat = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("TotalHT")
            lTotHT.Text = TotalHTNETAchat.ToString

            lTotalTVA.Text = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("TVA")
            lValeurVenteTTC.Text = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("ValeurVenteTTC")

            lRemise.Text = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("TotalRemise")
            tAutre.Value = dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("Autre")

            lTotalTTC.Text = Math.Round(TotalTTCAchat, 3)

            lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsChargement.Tables("ACHAT").Rows(dsChargement.Tables("ACHAT").Rows.Count - 1)("CodePersonnel"))
            lHTNet.Text = (CDbl(lTotHT.Text) - CDbl(lRemise.Text)).ToString
            tTotalHT.Value = lHTNet.Text

            NumeroAchat = DataRowRecherche.Item("NumeroAchat")

            ChargerDetails()
        End If
    End Sub
    Private Sub ChargerDetails()
        Dim StrSQL As String
        Dim I As Integer
        dsChargement.Tables("ACHAT_DETAILS").Clear()

        'chargement des détails des Achats 

        StrSQL = "SELECT NumeroAchat," + _
                 "CodeArticle," + _
                 "Designation," + _
                 "LibelleForme," + _
                 "Qte," + _
                 "NumeroLotArticle," + _
                 "DatePeremption," + _
                 "Stock," + _
                 "Remise," + _
                 "PrixAchatHT," + _
                 "PrixVenteTTC," + _
                 "TotalAchatHT," + _
                 "TVA " + _
                 "FROM " + _
                 "ACHAT_DETAILS,FORME_ARTICLE " + _
                 "WHERE ACHAT_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
                 "AND NumeroAchat =" + Quote(NumeroAchat) + ""

        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "ACHAT_DETAILS")

        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsChargement
            Catch ex As Exception
            End Try
            .DataMember = "ACHAT_DETAILS"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qte"
            .Columns("NumeroLotArticle").Caption = "Numero Lot"
            .Columns("DatePeremption").Caption = "Date de péremption"
            .Columns("Stock").Caption = "Stock"
            .Columns("Remise").Caption = "Remise"
            .Columns("PrixAchatHT").Caption = "Prix A HT "
            .Columns("PrixVenteTTC").Caption = "Prix V TTC "
            .Columns("TotalAchatHT").Caption = "Total A HT"
            .Columns("TVA").Caption = "TVA"

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalAchatHT").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("NumeroAchat").Width = 0
            .Splits(0).DisplayColumns("NumeroAchat").Visible = False
            .Splits(0).DisplayColumns("NumeroAchat").AllowSizing = False
            .Splits(0).DisplayColumns("CodeArticle").Width = 80
            .Splits(0).DisplayColumns("Designation").Width = 250
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("QteCommander").Width = 50
            .Splits(0).DisplayColumns("NumeroLotArticle").Width = 70
            .Splits(0).DisplayColumns("DatePeremption").Width = 70
            .Splits(0).DisplayColumns("Stock").Width = 50
            .Splits(0).DisplayColumns("Remise").Width = 50
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 60
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 60
            .Splits(0).DisplayColumns("TotalAchatHT").Width = 60
            .Splits(0).DisplayColumns("TVA").Width = 50

            .Splits(0).DisplayColumns("CodeArticle").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Remise").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

        End With

    End Sub

    Private Sub bFirst_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim j As Integer
        Dim DataRowRecherche As DataRow
        Dim NumeroLigne As Integer
        For j = 0 To dsChargement.Tables("ACHAT").Rows.Count - 1
            DataRowRecherche = dsChargement.Tables("ACHAT").Rows(j)
            If DataRowRecherche.Item("NumeroAchat") = NumeroAchat Then
                NumeroLigne = j
            End If
        Next

        If dsChargement.Tables("ACHAT").Rows.Count - 1 > 0 Then
            DataRowRecherche = dsChargement.Tables("ACHAT").Rows(0)
            'chargement des informations entête
            lNumeroAchat.Text = dsChargement.Tables("ACHAT").Rows(0)("NumeroAchat")
            lDateAchat.Text = dsChargement.Tables("ACHAT").Rows(0)("Date")
            cmbFournisseur.SelectedValue = dsChargement.Tables("ACHAT").Rows(0)("CodeFournisseur")
            cmbDateBlFacture.Value = dsChargement.Tables("ACHAT").Rows(0)("DateBlFacture")

            tNumeroBlFact.Value = dsChargement.Tables("ACHAT").Rows(0)("NumeroBL/Facture").ToString

            TotalTTCAchat = dsChargement.Tables("ACHAT").Rows(0)("TotalTTC")
            TotalHTNETAchat = dsChargement.Tables("ACHAT").Rows(0)("TotalHT")
            lTotHT.Text = TotalHTNETAchat.ToString

            lTotalTVA.Text = dsChargement.Tables("ACHAT").Rows(0)("TVA")
            lValeurVenteTTC.Text = dsChargement.Tables("ACHAT").Rows(0)("ValeurVenteTTC")

            lRemise.Text = dsChargement.Tables("ACHAT").Rows(0)("TotalRemise")
            tAutre.Value = dsChargement.Tables("ACHAT").Rows(0)("Autre")
            lTotalTTC.Text = Math.Round(TotalTTCAchat, 3)

            lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsChargement.Tables("ACHAT").Rows(0)("CodePersonnel"))

            NumeroAchat = DataRowRecherche.Item("NumeroAchat")
            lHTNet.Text = (CDbl(lTotHT.Text) - CDbl(lRemise.Text)).ToString
            tTotalHT.Value = lHTNet.Text

            ChargerDetails()

        End If
    End Sub

    Private Sub bNext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim j As Integer
        Dim DataRowRecherche As DataRow
        Dim NumeroLigne As Integer
        For j = 0 To dsChargement.Tables("ACHAT").Rows.Count - 1
            DataRowRecherche = dsChargement.Tables("ACHAT").Rows(j)
            If DataRowRecherche.Item("NumeroAchat") = NumeroAchat Then
                NumeroLigne = j
            End If
        Next

        If NumeroLigne + 1 <= dsChargement.Tables("ACHAT").Rows.Count - 1 Then
            DataRowRecherche = dsChargement.Tables("ACHAT").Rows(NumeroLigne + 1)
            'chargement des informations entête
            lNumeroAchat.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne + 1)("NumeroAchat")
            lDateAchat.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne + 1)("Date")
            cmbFournisseur.SelectedValue = dsChargement.Tables("ACHAT").Rows(NumeroLigne + 1)("CodeFournisseur")
            cmbDateBlFacture.Value = dsChargement.Tables("ACHAT").Rows(NumeroLigne + 1)("DateBlFacture")

            tNumeroBlFact.Value = dsChargement.Tables("ACHAT").Rows(NumeroLigne + 1)("NumeroBL/Facture").ToString

            TotalTTCAchat = dsChargement.Tables("ACHAT").Rows(NumeroLigne + 1)("TotalTTC")
            TotalHTNETAchat = dsChargement.Tables("ACHAT").Rows(NumeroLigne + 1)("TotalHT")
            lTotHT.Text = TotalHTNETAchat.ToString

            lTotalTVA.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne + 1)("TVA")
            lValeurVenteTTC.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne + 1)("ValeurVenteTTC")

            lRemise.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne + 1)("TotalRemise")
            tAutre.Value = dsChargement.Tables("ACHAT").Rows(NumeroLigne + 1)("Autre")

            lTotalTTC.Text = Math.Round(TotalTTCAchat, 3)

            lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsChargement.Tables("ACHAT").Rows(NumeroLigne + 1)("CodePersonnel"))
            lHTNet.Text = (CDbl(lTotHT.Text) - CDbl(lRemise.Text)).ToString
            tTotalHT.Value = lHTNet.Text

            NumeroAchat = DataRowRecherche.Item("NumeroAchat")
            ChargerDetails()
        End If
    End Sub

    Private Sub bPrevious_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim j As Integer
        Dim DataRowRecherche As DataRow
        Dim NumeroLigne As Integer
        For j = 0 To dsChargement.Tables("ACHAT").Rows.Count - 1
            DataRowRecherche = dsChargement.Tables("ACHAT").Rows(j)
            If DataRowRecherche.Item("NumeroAchat") = NumeroAchat Then
                NumeroLigne = j
            End If
        Next

        If NumeroLigne - 1 >= 0 Then
            DataRowRecherche = dsChargement.Tables("ACHAT").Rows(NumeroLigne - 1)
            'chargement des informations entête
            lNumeroAchat.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne - 1)("NumeroAchat")
            lDateAchat.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne - 1)("Date")
            cmbFournisseur.SelectedValue = dsChargement.Tables("ACHAT").Rows(NumeroLigne - 1)("CodeFournisseur")
            cmbDateBlFacture.Value = dsChargement.Tables("ACHAT").Rows(NumeroLigne - 1)("DateBlFacture")

            tNumeroBlFact.Value = dsChargement.Tables("ACHAT").Rows(NumeroLigne - 1)("NumeroBL/Facture").ToString

            TotalTTCAchat = dsChargement.Tables("ACHAT").Rows(NumeroLigne - 1)("TotalTTC")
            TotalHTNETAchat = dsChargement.Tables("ACHAT").Rows(NumeroLigne - 1)("TotalHT")
            lTotHT.Text = TotalHTNETAchat.ToString

            lTotalTVA.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne - 1)("TVA")
            lValeurVenteTTC.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne - 1)("ValeurVenteTTC")

            lRemise.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne - 1)("TotalRemise")
            tAutre.Value = dsChargement.Tables("ACHAT").Rows(NumeroLigne - 1)("Autre")

            lTotalTTC.Text = Math.Round(TotalTTCAchat, 3)

            lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsChargement.Tables("ACHAT").Rows(NumeroLigne - 1)("CodePersonnel"))

            NumeroAchat = DataRowRecherche.Item("NumeroAchat")
            lHTNet.Text = (CDbl(lTotHT.Text) - CDbl(lRemise.Text)).ToString
            tTotalHT.Value = lHTNet.Text

            ChargerDetails()

        End If

    End Sub

    Private Sub tRemisePourcentage_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRemisePourcentage.KeyUp
        Dim I As Integer
        If e.KeyCode = Keys.Enter Then
            If tRemisePourcentage.Text = "" Then
                tRemisePourcentage.Text = "0.000"
            End If
            '---------------------- rendre le remis dans toutes les lignes
            For I = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
                If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") <> "" Then
                    dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Remise") = tRemisePourcentage.Text
                End If
            Next

            CalculerMontants()
            '---------------------- calculer le nouveau net à payer
            tRemisePourcentage.Text = (Math.Round(CDbl(tRemisePourcentage.Text), 3)).ToString
            '------------ déja calculé dans CalculerMonatant
            'tNetAPayer.Text = (CDbl(lTotalTTC.Text) / 100) * (100 - CDbl(tRemisePourcentage.Text))
            'lRemise.Text = Math.Round((CDbl(lTotalTTC.Text) / 100) * CDbl(tRemisePourcentage.Text), 3)
        End If

        '------------------------ controle du point et de 3 chiffres aprés la virgule 


        Dim Position As Integer = 0

        If e.KeyCode = Keys.Decimal Then
            Position = InStr(tRemisePourcentage.Text, ".")
            tRemisePourcentage.Text = tRemisePourcentage.Text.Substring(0, Position)
            tRemisePourcentage.Select(tRemisePourcentage.Text.Length, 0)
            Exit Sub
        End If

        If tRemisePourcentage.Text.Contains(".") Then
            Position = InStr(tRemisePourcentage.Text, ".")
            If tRemisePourcentage.Text.Length - Position > 3 Then
                tRemisePourcentage.Text = tRemisePourcentage.Text.Substring(0, Position + 3)
                tRemisePourcentage.Select(tRemisePourcentage.Text.Length, 0)
            End If
        End If

    End Sub

    Private Sub tRemisePourcentage_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tRemisePourcentage.LostFocus
        tRemisePourcentage.Text = tRemisePourcentage.Text
        If tRemisePourcentage.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(tRemisePourcentage.Text, ".")
            If tRemisePourcentage.Text.Length - x = 1 Then
                tRemisePourcentage.Text = tRemisePourcentage.Text + ("00")
            ElseIf tRemisePourcentage.Text.Length - x = 2 Then
                tRemisePourcentage.Text = tRemisePourcentage.Text + ("0")
            End If
        Else
            tRemisePourcentage.Text = tRemisePourcentage.Text + ".000"
        End If

    End Sub

    Private Sub tRemisePourcentage_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tRemisePourcentage.TextChanged

    End Sub

    Private Sub tNetAPayer_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            tRemisePourcentage.Text = 100 - (100 / CDbl(lTotalTTC.Text) * CDbl(lHTNet.Text))
            lRemise.Text = (CDbl(lTotalTTC.Text) - CDbl(lHTNet.Text)).ToString
        End If
    End Sub

    Private Sub tNetAPayer_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

        If ControleDAcces(6, "SUPPRESSION_ACHAT") = False Then
            Exit Sub
        End If

        Dim cmd As New SqlCommand
        'Dim NumeroAchat As String = ""
        Dim i As Integer = 0
        Dim StrSQL As String = ""
        Dim NbreDesLots As Integer = 0

        Dim QteRestante As Integer = 0

        If mode = "Ajout" Or mode = "Modif" Then
            If gArticles.Row <= dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 Then
                gArticles.Delete()
                CalculerMontants()
                If dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 < 0 Then
                    bAjouter_Click(sender, e)
                End If
                gArticles.Col = 1
                Exit Sub
            End If
        Else
            If MsgBox("Voulez vous vraiment supprimer cet achat " + lNumeroAchat.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then

                If NumeroAchat = "" Then
                    MsgBox("Aucun achat à supprimer !", MsgBoxStyle.Critical, "Information")
                    Exit Sub
                End If

                Dim ConfirmerEnregistrer As Boolean = False
                Dim CodeOperateur As String = ""

                '------------------------------ demande du mot de passe
                Dim myMotDePasse As New fMotDePasse
                myMotDePasse.ShowDialog()

                ConfirmerEnregistrer = fMotDePasse.Confirmer
                CodeOperateur = fMotDePasse.CodeOperateur

                myMotDePasse.Dispose()
                myMotDePasse.Close()

                If ConfirmerEnregistrer = False Then
                    Exit Sub
                End If

                If (dsChargement.Tables.IndexOf("ACHAT_A_SUPPRIME") > -1) Then
                    dsChargement.Tables("ACHAT_A_SUPPRIME").Clear()
                End If
                If (dsChargement.Tables.IndexOf("ACHAT_SUPPRIME") > -1) Then
                    dsChargement.Tables("ACHAT_SUPPRIME").Clear()
                End If
                If (dsChargement.Tables.IndexOf("ACHAT_DETAILS_A_SUPPRIME") > -1) Then
                    dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Clear()
                End If
                If (dsChargement.Tables.IndexOf("ACHAT_SUPPRIME_DETAILS") > -1) Then
                    dsChargement.Tables("ACHAT_SUPPRIME_DETAILS").Clear()
                End If

                Dim DataRowAchatSupprime As DataRow = Nothing
                Dim NumeroAchatSupprime As String = ""

                NumeroAchatSupprime = RecupereNumeroAchatSupprime()

                'chargement des Entêtes de la achat          
                StrSQL = "SELECT * FROM ACHAT WHERE NumeroAchat='" + NumeroAchat + "'"
                cmdChargement.Connection = ConnectionServeur
                cmdChargement.CommandText = StrSQL
                daChargement = New SqlDataAdapter(cmdChargement)
                daChargement.Fill(dsChargement, "ACHAT_A_SUPPRIME")

                'chargement de l'entête dans la datatble qui correspond à la table ACHAT_SUPPRIME de l achat 
                StrSQL = "SELECT TOP(0) * FROM ACHAT_SUPPRIME "
                cmdChargement.Connection = ConnectionServeur
                cmdChargement.CommandText = StrSQL
                daChargement = New SqlDataAdapter(cmdChargement)
                daChargement.Fill(dsChargement, "ACHAT_SUPPRIME")
                cbChargement = New SqlCommandBuilder(daChargement)

                'ajout d'un nouvel enregistrement vide dans les datatables convenables
                DataRowAchatSupprime = dsChargement.Tables("ACHAT_SUPPRIME").NewRow()

                DataRowAchatSupprime("NumeroAchatSupprime") = NumeroAchatSupprime

                DataRowAchatSupprime("NumeroAchat") = dsChargement.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("NumeroAchat")
                DataRowAchatSupprime("Date") = dsChargement.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("Date")
                DataRowAchatSupprime("TotalHT") = dsChargement.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("TotalHT")
                DataRowAchatSupprime("TVA") = dsChargement.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("TVA")
                DataRowAchatSupprime("TotalTTC") = dsChargement.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("TotalTTC")
                DataRowAchatSupprime("TotalRemise") = dsChargement.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("TotalRemise")
                DataRowAchatSupprime("Timbre") = dsChargement.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("Timbre")
                DataRowAchatSupprime("CodeFournisseur") = dsChargement.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("CodeFournisseur")
                DataRowAchatSupprime("CodePersonnel") = dsChargement.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("CodePersonnel")
                DataRowAchatSupprime("NumeroBL/Facture") = dsChargement.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("NumeroBL/Facture")
                DataRowAchatSupprime("LibellePoste") = dsChargement.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("LibellePoste")
                DataRowAchatSupprime("DateBlFacture") = dsChargement.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("DateBlFacture")
                DataRowAchatSupprime("ValeurVenteTTC") = dsChargement.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("ValeurVenteTTC")

                DataRowAchatSupprime("Autre") = dsChargement.Tables("ACHAT_A_SUPPRIME").Rows(0).Item("Autre")

                DataRowAchatSupprime("DateSuppression") = System.DateTime.Now
                DataRowAchatSupprime("CodePersonnelSupprime") = CodeOperateur
                DataRowAchatSupprime("NomPersonnelSupprime") = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", CodeOperateur)

                dsChargement.Tables("ACHAT_SUPPRIME").Rows.Add(DataRowAchatSupprime)

                Try
                    daChargement.Update(dsChargement, "ACHAT_SUPPRIME")
                Catch ex As Exception
                    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                    dsChargement.Reset()
                End Try


                'chargement des Details de la vente 
                StrSQL = "SELECT * FROM ACHAT_DETAILS WHERE NumeroAchat='" + NumeroAchat + "'"
                cmdChargement.Connection = ConnectionServeur
                cmdChargement.CommandText = StrSQL
                daChargement = New SqlDataAdapter(cmdChargement)
                daChargement.Fill(dsChargement, "ACHAT_DETAILS_A_SUPPRIME")

                'chargement des Details de la vente dans une datatable qui correspond a la tabel VENTE_SUPPRIME_DETAILS
                StrSQL = "SELECT * FROM ACHAT_SUPPRIME_DETAILS "
                cmdChargement.Connection = ConnectionServeur
                cmdChargement.CommandText = StrSQL
                daChargement = New SqlDataAdapter(cmdChargement)
                daChargement.Fill(dsChargement, "ACHAT_SUPPRIME_DETAILS")
                cbChargement = New SqlCommandBuilder(daChargement)

                For i = 0 To dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows.Count - 1

                    DataRowAchatSupprime = dsChargement.Tables("ACHAT_SUPPRIME_DETAILS").NewRow()

                    DataRowAchatSupprime("NumeroAchatSupprime") = NumeroAchatSupprime

                    DataRowAchatSupprime("NumeroAchat") = dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("NumeroAchat")
                    DataRowAchatSupprime("CodeArticle") = dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("CodeArticle")
                    DataRowAchatSupprime("NumeroLotArticle") = dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("NumeroLotArticle")
                    DataRowAchatSupprime("Designation") = dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("Designation")

                    DataRowAchatSupprime("CodeForme") = dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("CodeForme")
                    DataRowAchatSupprime("Qte") = dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("Qte")
                    DataRowAchatSupprime("Stock") = dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("Stock")

                    DataRowAchatSupprime("PrixAchatHT") = dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("PrixAchatHT")
                    DataRowAchatSupprime("TotalAchatHT") = dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("TotalAchatHT")
                    DataRowAchatSupprime("PrixVenteTTC") = dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("PrixVenteTTC")

                    DataRowAchatSupprime("Remise") = dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("Remise")
                    DataRowAchatSupprime("TVA") = dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("TVA")
                    DataRowAchatSupprime("DatePeremption") = dsChargement.Tables("ACHAT_DETAILS_A_SUPPRIME").Rows(i).Item("DatePeremption")

                    dsChargement.Tables("ACHAT_SUPPRIME_DETAILS").Rows.Add(DataRowAchatSupprime)

                Next

                Try
                    daChargement.Update(dsChargement, "ACHAT_SUPPRIME_DETAILS")
                Catch ex As Exception
                    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                    dsChargement.Reset()
                End Try




                '***************************************************************************************
                '***************************************************************************************
                '***************************************************************************************
                '***************************************************************************************

                '--------------- suppression des quantités des lots introduits a partir de cet achat
                For i = 0 To dsChargement.Tables("ACHAT_DETAILS").Rows.Count - 1

                    StrSQL = "SELECT QteLotArticle FROM [LOT_ARTICLE] " + _
                             "WHERE NumeroLotArticle='" + _
                             dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("NumeroLotArticle") + _
                             "' AND CodeArticle='" + _
                             dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + "'"

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        QteRestante = cmd.ExecuteScalar()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                    If QteRestante < dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("Qte") Then  'And dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("NumeroLotArticle") <> ""

                        '----------- s il y a des ventes qui ont diminuer du stock intoduit par cet achat 
                        '----------- le reste est inférieur à la quantité 

                        StrSQL = "Update LOT_ARTICLE SET QteLotArticle=0" + _
                                 " WHERE NumeroLotArticle ='" + _
                                 dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("NumeroLotArticle") + "'" + _
                                 " AND CodeArticle ='" + _
                                 dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + "'"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL

                        Try
                            cmd.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try

                        '------------ on cherche si on a un lot qui n'admet pas de date de peremption 
                        StrSQL = "SELECT COUNT(NumeroLotArticle) FROM [LOT_ARTICLE] " + _
                                 "WHERE CodeArticle='" + _
                                 dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + _
                                 "' AND DatePeremptionArticle IS NULL"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL

                        Try
                            NbreDesLots = cmd.ExecuteScalar()
                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try

                        If NbreDesLots > 0 Then
                            StrSQL = "Update LOT_ARTICLE SET QteLotArticle=QteLotArticle-" + _
                            (dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("Qte") - QteRestante).ToString + _
                            " WHERE NumeroLotArticle='' AND CodeArticle ='" + _
                            dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + "'"

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL

                            Try
                                cmd.ExecuteNonQuery()
                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try

                        Else
                            StrSQL = "INSERT INTO LOT_ARTICLE " + _
                                     "(""NumeroLotArticle"",""CodeArticle"",""QteLotArticle"",""DatePeremptionArticle"") " + _
                                     " VALUES(''" + _
                                     ",'" + dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + _
                                     "',-" + (dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("Qte") - QteRestante).ToString + _
                                     ",NULL)"

                            cmd.Connection = ConnectionServeur
                            cmd.CommandText = StrSQL
                            Try
                                cmd.ExecuteNonQuery()
                            Catch ex As Exception
                                Console.WriteLine(ex.Message)
                            End Try
                        End If

                    Else
                        StrSQL = "Update LOT_ARTICLE SET QteLotArticle=QteLotArticle-" + _
                                 dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("Qte").ToString + _
                                 " WHERE NumeroLotArticle ='" + _
                                 dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("NumeroLotArticle") + "'" + _
                                 " AND CodeArticle ='" + _
                                 dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + "'"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL

                        Try
                            cmd.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try

                    End If

                Next
                '--------------- suppression des affectation des commandes qui existe 

                StrSQL = "Update COMMANDE SET NumeroFacture=NULL WHERE NumeroFacture ='" + lNumeroAchat.Text + "'"

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                '--------------- suppression des details de la achats
                NumeroAchat = lNumeroAchat.Text
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM ACHAT_DETAILS WHERE NumeroAchat ='" + NumeroAchat + "'"
                    cmd.ExecuteNonQuery()

                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
                '--------------- suppression de la achats
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM ACHAT WHERE NumeroAchat ='" + NumeroAchat + "'"
                    cmd.ExecuteNonQuery()
                    Init()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try

                MsgBox("Achat supprimé !", MsgBoxStyle.Information, "Information")

            End If

        End If

    End Sub
    Public Function RecupereNumeroAchatSupprime()

        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max([NumeroAchatSupprime]) FROM [ACHAT_SUPPRIME] WHERE SUBSTRING (NumeroAchatSupprime,0,5)=YEAR(getdate())"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero sequenciel de l année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If

        Return ValeurRetour
    End Function

    Private Sub SupprimerTransactionArticlesPourModification()
        Dim cmd As New SqlCommand
        Dim NumeroAchat As String = ""
        Dim i As Integer = 0
        Dim StrSQL As String = ""
        Dim NbreDesLots As Integer = 0

        Dim QteRestante As Integer = 0

        '--------------- suppression des quantités des lots introduits a partir de cet achat
        For i = 0 To dsChargement.Tables("ACHAT_DETAILS").Rows.Count - 1

            StrSQL = "SELECT QteLotArticle FROM [LOT_ARTICLE] " + _
                     "WHERE NumeroLotArticle='" + _
                     dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("NumeroLotArticle") + _
                     "' AND CodeArticle='" + _
                     dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + "'"

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            Try
                QteRestante = cmd.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If QteRestante < dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("Qte") Then  'And dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("NumeroLotArticle") <> ""
                '----------- s il y a des ventes qui ont diminuer du stock intoduit par cet achat 

                StrSQL = "Update LOT_ARTICLE SET QteLotArticle=0" + _
                         " WHERE NumeroLotArticle ='" + _
                         dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("NumeroLotArticle") + "'" + _
                         " AND CodeArticle ='" + _
                         dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + "'"

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                '------------ on cherche si on a un lot qui n'admet pas de date de peremption 
                StrSQL = "SELECT COUNT(NumeroLotArticle) FROM [LOT_ARTICLE] " + _
                         "WHERE CodeArticle='" + _
                         dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + _
                         "' AND DatePeremptionArticle IS NULL"

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    NbreDesLots = cmd.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If NbreDesLots > 0 Then
                    StrSQL = "Update LOT_ARTICLE SET QteLotArticle=QteLotArticle-" + _
                    (dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("Qte") - QteRestante).ToString + _
                    " WHERE NumeroLotArticle='' AND CodeArticle ='" + _
                    dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + "'"

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                Else
                    StrSQL = "INSERT INTO LOT_ARTICLE " + _
                             "(""NumeroLotArticle"",""CodeArticle"",""QteLotArticle"",""DatePeremptionArticle"") " + _
                             " VALUES(''" + _
                             ",'" + dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + _
                             "',-" + (dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("Qte") - QteRestante).ToString + _
                             ",NULL)"

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL
                    Try
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try
                End If

            Else
                StrSQL = "Update LOT_ARTICLE SET QteLotArticle=QteLotArticle-" + _
                         dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("Qte").ToString + _
                         " WHERE NumeroLotArticle ='" + _
                         dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("NumeroLotArticle") + "'" + _
                         " AND CodeArticle ='" + _
                         dsChargement.Tables("ACHAT_DETAILS").Rows(i).Item("CodeArticle") + "'"

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL

                Try
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
            End If
        Next
    End Sub

    Private Sub tRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRecherche.KeyUp
        If e.KeyCode = Keys.Enter And tRecherche.Text <> "" Then
            Dim j As Integer
            Dim DataRowRecherche As DataRow
            Dim NumeroLigne As Integer = 0

            If tRecherche.Text.Length < 11 Then
                tRecherche.Text = tRecherche.Text.Substring(0, 5) + tRecherche.Text.Substring(5, tRecherche.Text.Length - 5).PadLeft(6, "0")
            End If

            For j = 0 To dsChargement.Tables("ACHAT").Rows.Count - 1
                DataRowRecherche = dsChargement.Tables("ACHAT").Rows(j)
                If DataRowRecherche.Item("NumeroAchat") = Trim(tRecherche.Text) Then
                    NumeroLigne = j
                End If
            Next
            If NumeroLigne >= 0 Then
                If NumeroLigne <= dsChargement.Tables("ACHAT").Rows.Count - 1 Then
                    DataRowRecherche = dsChargement.Tables("ACHAT").Rows(NumeroLigne)
                    'chargement des informations entête
                    lNumeroAchat.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("NumeroAchat")
                    lDateAchat.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("Date")
                    cmbFournisseur.SelectedValue = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("CodeFournisseur")

                    TotalTTCAchat = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("TotalTTC")
                    TotalHTNETAchat = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("TotalHT")

                    TVA = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("TVA")
                    lRemise.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("TotalRemise")
                    lTotalTTC.Text = Math.Round(TotalTTCAchat, 3)

                    lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsChargement.Tables("ACHAT").Rows(NumeroLigne)("CodePersonnel"))

                    NumeroAchat = DataRowRecherche.Item("NumeroAchat")
                    ChargerDetails()
                    'If lRemise.Text = "0.000" Then
                    '    lRemise.Visible = False
                    '    lRemiseAfficher.Visible = False
                    'Else
                    '    lRemise.Visible = True
                    '    lRemiseAfficher.Visible = True
                    'End If
                End If
            Else
                MsgBox("Vente inéxistant !", MsgBoxStyle.Critical, "Erreur")
            End If
            tRecherche.Value = ""
            tRecherche.Visible = False
        End If
    End Sub

    Private Sub tRecherche_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tRecherche.LostFocus
        tRecherche.Visible = False
    End Sub

    Private Sub tRecherche_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tRecherche.TextChanged

    End Sub

    Private Sub lValeurVenteTTC_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lValeurVenteTTC.Click

    End Sub

    Private Sub lValeurVenteTTC_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lValeurVenteTTC.TextChanged
        lValeurVenteTTC.Text = lValeurVenteTTC.Text
        If lValeurVenteTTC.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lValeurVenteTTC.Text, ".")
            If lValeurVenteTTC.Text.Length - x = 1 Then
                lValeurVenteTTC.Text = lValeurVenteTTC.Text + ("00")
            ElseIf lValeurVenteTTC.Text.Length - x = 2 Then
                lValeurVenteTTC.Text = lValeurVenteTTC.Text + ("0")
            End If
        Else
            lValeurVenteTTC.Text = lValeurVenteTTC.Text + ".000"
        End If
    End Sub

    Private Sub lTotalTTC_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotalTTC.TextChanged
        lTotalTTC.Text = lTotalTTC.Text
        If lTotalTTC.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lTotalTTC.Text, ".")
            If lTotalTTC.Text.Length - x = 1 Then
                lTotalTTC.Text = lTotalTTC.Text + ("00")
            ElseIf lTotalTTC.Text.Length - x = 2 Then
                lTotalTTC.Text = lTotalTTC.Text + ("0")
            End If
        Else
            lTotalTTC.Text = lTotalTTC.Text + ".000"
        End If
    End Sub

    Private Sub lTotHT_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotHT.TextChanged
        lTotHT.Text = lTotHT.Text
        If lTotHT.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lTotHT.Text, ".")
            If lTotHT.Text.Length - x = 1 Then
                lTotHT.Text = lTotHT.Text + ("00")
            ElseIf lTotHT.Text.Length - x = 2 Then
                lTotHT.Text = lTotHT.Text + ("0")
            End If
        Else
            lTotHT.Text = lTotHT.Text + ".000"
        End If
    End Sub

    Private Sub lTotalTVA_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotalTVA.TextChanged
        lTotalTVA.Text = lTotalTVA.Text
        If lTotalTVA.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lTotalTVA.Text, ".")
            If lTotalTVA.Text.Length - x = 1 Then
                lTotalTVA.Text = lTotalTVA.Text + ("00")
            ElseIf lTotalTVA.Text.Length - x = 2 Then
                lTotalTVA.Text = lTotalTVA.Text + ("0")
            End If
        Else
            lTotalTVA.Text = lTotalTVA.Text + ".000"
        End If
    End Sub

    Private Sub tAutre_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tAutre.KeyUp
        Dim Position As Integer = 0

        If e.KeyCode = Keys.Decimal Then
            Position = InStr(tAutre.Text, ".")
            tAutre.Text = tAutre.Text.Substring(0, Position)
            tAutre.Select(tAutre.Text.Length, 0)
            Exit Sub
        End If

        If tAutre.Text.Contains(".") Then
            Position = InStr(tAutre.Text, ".")
            If tAutre.Text.Length - Position > 3 Then
                tAutre.Text = tAutre.Text.Substring(0, Position + 3)
                tAutre.Select(tAutre.Text.Length, 0)
            End If
        End If

        If e.KeyCode = Keys.Enter And IsNumeric(tAutre.Text) = True Then
            lTotalTTC.Text = (CDbl(lTotalTTC.Text) + CDbl(tAutre.Text)).ToString
            gArticles.Focus()
            gArticles.Col = 1
        End If

    End Sub

    Private Sub tAutre_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tAutre.LostFocus
        tAutre.Value = tAutre.Text
        If tAutre.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(tAutre.Text, ".")
            If tAutre.Text.Length - x = 1 Then
                tAutre.Text = tAutre.Text + ("00")
            ElseIf tAutre.Text.Length - x = 2 Then
                tAutre.Text = tAutre.Text + ("0")
            End If
        Else
            tAutre.Text = tAutre.Text + ".000"
        End If
    End Sub


    Private Sub lRemise_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lRemise.TextChanged
        lRemise.Text = lRemise.Text
        If lRemise.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lRemise.Text, ".")
            If lRemise.Text.Length - x = 1 Then
                lRemise.Text = lRemise.Text + ("00")
            ElseIf lRemise.Text.Length - x = 2 Then
                lRemise.Text = lRemise.Text + ("0")
            End If
        Else
            lRemise.Text = lRemise.Text + ".000"
        End If
    End Sub

    Private Sub gArticles_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gArticles.Click

    End Sub

    Private Sub fAchat_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Init()
    End Sub

    Private Sub bInstance_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

        Dim cmd As New SqlCommand
        Dim NumeroAchatInstance As String = ""
        Dim StrMajLOT As String = ""
        Dim StrMajReglement As String = ""
        Dim I As Integer = 0
        Dim NumeroLot As String = "RIEN"
        Dim NouveauNumeroLot As String = ""

        Dim StrSQL As String = ""

        '**************************************************************************************************
        '********* Test du cas ou la liste est vide : chargement des anciens ventes en instance ***********
        '**************************************************************************************************

        If dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle") = "" Then

            Dim MyAchatInstance As New fListeDesAchatsEnInstance

            MyAchatInstance.ShowDialog()
            NumeroAchatInstance = fListeDesAchatsEnInstance.NumeroAchat
            ComfirmerMettreEnINstance = fListeDesAchatsEnInstance.ComfirmerMettreEnINstance

            MyAchatInstance.Dispose()
            MyAchatInstance.Close()

            If ComfirmerMettreEnINstance = False Then
                gArticles.Col = 1
                gArticles.EditActive = True
                Exit Sub
            End If

            '----------------------- chargement des datatables 

            '------------------- chargement de l'Entêtes de la commandes  
            Try
                dsAchat.Tables("ACHAT_INSTANCE").Clear()
            Catch ex As Exception
            End Try
            Try
                dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Clear()
            Catch ex As Exception
            End Try

            StrSQL = "SELECT * FROM ACHAT_INSTANCE WHERE NumeroAchatInstance ='" + NumeroAchatInstance + "'"
            cmdAchat.Connection = ConnectionServeur
            cmdAchat.CommandText = StrSQL
            daAchat = New SqlDataAdapter(cmdAchat)
            daAchat.Fill(dsAchat, "ACHAT_INSTANCE")
            If dsAchat.Tables("ACHAT_INSTANCE").Rows.Count > 0 Then

                lDateAchat.Text = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("Date")
                TotalTTCAchat = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("TotalTTC")

                TotalHTNETAchat = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("TotalHT")
                lTotHT.Text = TotalHTNETAchat.ToString

                TVA = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("TotalTVA")
                lTotalTVA.Text = TVA

                lTotalTTC.Text = Math.Round(dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("TotalTTC"), 3)
                lValeurVenteTTC.Text = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("ValeurVenteTTC")

                lRemise.Text = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("TotalRemise")
                tAutre.Value = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("Autre")

                lHTNet.Text = (CDbl(lTotHT.Text) - CDbl(lRemise.Text)).ToString
                lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("CodeOperateur"))

                tNumeroBlFact.Text = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("NumeroBL/Facture")
                cmbDateBlFacture.Text = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("DateBlFacture")

                cmbFournisseur.SelectedValue = dsAchat.Tables("ACHAT_INSTANCE").Rows(dsAchat.Tables("ACHAT_INSTANCE").Rows.Count - 1)("CodeFournisseur")
            End If

            '----------------------chargement des détails des Commandes 

            StrSQL = "SELECT NumeroAchatInstance," + _
                     "CodeArticle," + _
                     "Designation," + _
                     "LibelleForme," + _
                     "Qte," + _
                     "DatePeremption," + _
                     "Stock," + _
                     "Remise," + _
                     "PrixAchatHT," + _
                     "PrixVenteTTC," + _
                     "TotalAchatHT," + _
                     "TVA," + _
                     "ACHAT_INSTANCE_DETAILS.CodeForme " + _
                     "FROM ACHAT_INSTANCE_DETAILS,FORME_ARTICLE " + _
                     " WHERE ACHAT_INSTANCE_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
                     "AND NumeroAchatInstance =" + _
                     Quote(NumeroAchatInstance) + ""

            cmdAchat.Connection = ConnectionServeur
            cmdAchat.CommandText = StrSQL
            daAchat = New SqlDataAdapter(cmdAchat)
            daAchat.Fill(dsAchat, "ACHAT_INSTANCE_DETAILS")

            'Dim DatePeremption As Date

            For I = 0 To dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows.Count - 1
                If dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("CodeArticle") <> "" Then
                    dr = dsAchat.Tables("ACHAT_DETAILS").NewRow

                    dr.Item("CodeArticle") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("CodeArticle")
                    dr.Item("Designation") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("Designation")
                    dr.Item("LibelleForme") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("LibelleForme")
                    dr.Item("CodeForme") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("CodeForme")
                    dr.Item("Qte") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("Qte")
                    'dr.Item("NumeroLotArticle") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("NumeroLotArticle")
                    dr.Item("DatePeremption") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("DatePeremption")
                    dr.Item("Stock") = CalculeStock(dr.Item("CodeArticle"))
                    dr.Item("Remise") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("Remise")
                    dr.Item("PrixAchatHT") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("PrixAchatHT")
                    dr.Item("PrixVenteTTC") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("PrixVenteTTC")
                    dr.Item("TotalAchatHT") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("TotalAchatHT")
                    dr.Item("TVA") = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows(I).Item("TVA")

                    ''---------------------- ajout des informations manquantes dans l'instance 

                    ''----------------------- récupération de la date de péremption

                    'StrSQL = " SELECT top(1) DatePeremptionArticle FROM [LOT_ARTICLE] where DatePeremptionArticle >'" + _
                    'System.DateTime.Now.Date.ToString + "' AND CodeArticle='" + _
                    'dr.Item("CodeArticle") + "' Order by DatePeremptionArticle ASC "
                    'cmd.Connection = ConnectionServeur
                    'cmd.CommandText = StrSQL
                    'Try
                    '    DatePeremption = cmd.ExecuteScalar()
                    'Catch ex As Exception
                    '    Console.WriteLine(ex.Message)
                    'End Try
                    'If DatePeremption = #12:00:00 AM# Then
                    'Else
                    '    dr.Item("DatePeremption") = DatePeremption
                    'End If
                    '----------------------- calcul du stock

                    dsAchat.Tables("ACHAT_DETAILS").Rows.Add(dr)
                End If

            Next

            I = 0
            '---------------------- Suppression des ligne vides 
            Do While I < dsAchat.Tables("ACHAT_DETAILS").Rows.Count
                If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                    dsAchat.Tables("ACHAT_DETAILS").Rows(I).Delete()
                End If
                I = I + 1
            Loop

            '---------------------- Suppression de l'instance 
            'details
            StrSQL = "DELETE FROM ACHAT_INSTANCE_DETAILS WHERE NumeroAchatInstance='" + NumeroAchatInstance + "'"
            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            Try
                cmd.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            'entête
            StrSQL = "DELETE FROM ACHAT_INSTANCE WHERE NumeroAchatInstance='" + NumeroAchatInstance + "'"
            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            Try
                cmd.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            gArticles.Col = 1
            gArticles.EditActive = True
            Exit Sub
        End If

        If cmbFournisseur.Text = "" Then
            MsgBox("Veuillez choisir un Fournisseur !", MsgBoxStyle.Critical, "Erreur")
            cmbFournisseur.Focus()
            Exit Sub
        End If

        '**************************************************************************************************
        '***************** Enregistrement d'une nouvelle vente en instance  *******************************
        '**************************************************************************************************

        '--------------------------- pour verifier le calcul : si l'utilisateur ne clique pas entree
        '--------------------------- sur la cellule qte du dernier ligne la somme TTC sera fausse
        CalculerMontants()
        '---------------------------------- récupération du dernier numero de vente --------------------
        NumeroAchatInstance = RecupereNumeroInstance()
        '------------------------------ enregistrement de l'entête de la vente -------------------------
        '-----------------------------------------------------------------------------------------------
        '-----------------------------------------------------------------------------------------------
        Try
            dsAchat.Tables("ACHAT_INSTANCE").Clear()
        Catch ex As Exception
        End Try

        Try
            dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Clear()
        Catch ex As Exception
        End Try

        Dim myNomInstance As New fNomInstance
        fNomInstance.NomTable = "ACHAT_INSTANCE"
        myNomInstance.ShowDialog()

        ConfirmerInstance = fNomInstance.Confirmer
        NomInstance = fNomInstance.NomInstance
        CodeOperateurInstance = fNomInstance.CodeOperateur

        myNomInstance.Dispose()
        myNomInstance.Close()

        If ConfirmerInstance = False Then
            Exit Sub
        End If

        StrSQL = "SELECT * FROM ACHAT_INSTANCE ORDER BY NumeroAchatInstance ASC"
        cmdAchat.Connection = ConnectionServeur
        cmdAchat.CommandText = StrSQL
        daAchat = New SqlDataAdapter(cmdAchat)
        daAchat.Fill(dsAchat, "ACHAT_INSTANCE")
        cbAchat = New SqlCommandBuilder(daAchat)
        dr = dsAchat.Tables("ACHAT_INSTANCE").NewRow()

        With dsAchat
            dr.Item("NumeroAchatInstance") = NumeroAchatInstance
            dr.Item("Date") = System.DateTime.Now

            dr.Item("TotalHT") = TotalHTNETAchat
            dr.Item("TotalTTC") = TotalTTCAchat
            dr.Item("TotalTVA") = TVA
            dr.Item("Timbre") = 0.3

            dr.Item("NumeroBL/Facture") = tNumeroBlFact.Text
            dr.Item("TotalRemise") = TotalRemiseAchat

            dr.Item("LibellePoste") = System.Environment.GetEnvironmentVariable("Poste")
            dr.Item("CodeOperateur") = CodeOperateurInstance
            dr.Item("DateBlFacture") = cmbDateBlFacture.Text
            dr.Item("CodeFournisseur") = cmbFournisseur.SelectedValue
            dr.Item("Note") = "rien"
            dr.Item("ValeurVenteTTC") = lValeurVenteTTC.Text
            dr.Item("NomAchatInstance") = NomInstance

            dsAchat.Tables("ACHAT_INSTANCE").Rows.Add(dr)
        End With

        '***************************************** enregistrement de l'entête de la vente *************

        Try
            daAchat.Update(dsAchat, "ACHAT_INSTANCE")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsAchat.Reset()
            Exit Sub
        End Try

        '-------------------- préparation des détails de la vente pour enregistrement ------------------
        '-----------------------------------------------------------------------------------------------
        '-----------------------------------------------------------------------------------------------

        StrSQL = "SELECT TOP (0) * FROM ACHAT_INSTANCE_DETAILS ORDER BY NumeroAchatInstance ASC"
        cmdAchat.Connection = ConnectionServeur
        cmdAchat.CommandText = StrSQL
        daAchat = New SqlDataAdapter(cmdAchat)
        daAchat.Fill(dsAchat, "ACHAT_INSTANCE_DETAILS")
        cbAchat = New SqlCommandBuilder(daAchat)

        For I = 0 To dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1
            If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") <> "" Then

                dr = dsAchat.Tables("ACHAT_INSTANCE_DETAILS").NewRow()

                dr.Item("NumeroAchatInstance") = NumeroAchatInstance
                dr.Item("CodeArticle") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle")
                dr.Item("Designation") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Designation")
                dr.Item("CodeForme") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeForme")
                dr.Item("Qte") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Qte")
                dr.Item("Stock") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Stock")
                dr.Item("PrixAchatHT") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("PrixAchatHT")
                dr.Item("TotalAchatHT") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("TotalAchatHT")
                dr.Item("PrixVenteTTC") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("PrixVenteTTC")
                dr.Item("Remise") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("Remise")
                dr.Item("TVA") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("TVA")
                dr.Item("DatePeremption") = dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("DatePeremption")

                dsAchat.Tables("ACHAT_INSTANCE_DETAILS").Rows.Add(dr)

            End If
        Next

        '***************************************** enregistrement des détails de la vente *************

        Try
            daAchat.Update(dsAchat, "ACHAT_INSTANCE_DETAILS")
            Init()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données (Enregistrement des détails vente) !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsAchat.Reset()
            Exit Sub
        End Try

        mode = "Consultation"

        Init()

    End Sub
    Public Function RecupereNumeroInstance()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max(NumeroAchatInstance) FROM ACHAT_INSTANCE"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero séquenciel de l année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If

        Return ValeurRetour

    End Function

    Private Sub bCommande_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim I As Integer = 0
        Dim J As Integer = 0
        Dim k As Integer = 0
        Dim StrSQL As String = ""
        Dim NumeroAchat As String = ""

        For k = 0 To TableauCommande.Length - 1
            TableauCommande(k) = ""
        Next

        Dim MyCommandeListe As New fListeDesCommandePourAchat
        If cmbFournisseur.Text <> "" Then
            MyCommandeListe.CodeFournisseur = cmbFournisseur.SelectedValue
        End If
        MyCommandeListe.ShowDialog()

        If fListeDesCommandePourAchat.ComfirmerChargementDesCommandes = False Then
            Exit Sub
        End If

        NumeroAchat = RecupereNumero()

        For I = 0 To fListeDesCommandePourAchat.dsCommandeEnAchat.Tables("COMMANDE").Rows.Count - 1
            If fListeDesCommandePourAchat.dsCommandeEnAchat.Tables("COMMANDE").Rows(I).Item("Cocher") = True Then
                '------------------ pour savoir si l'achat est depuis des commandes ou nn
                DepuisCommande = True
                For k = 0 To TableauCommande.Length - 1
                    If TableauCommande(k) = "" Then  ' pour atteindre la fin de la liste des numero 
                        Exit For                     ' des commande enregistré dans le tableau
                    End If
                Next
                ' ajout de la commande dans l achat
                TableauCommande(k) = fListeDesCommandePourAchat.dsCommandeEnAchat.Tables("COMMANDE").Rows(I).Item("NumeroCommande")

                Try
                    dsAchat.Tables("COMMANDE_DETAILS").Clear()
                Catch ex As Exception
                End Try
                ' ajout des détails de commande dans détails achat
                StrSQL = "SELECT CodeArticle," + _
                         "Designation," + _
                         "CodeForme," + _
                         "Qte," + _
                         "QteACommander," + _
                         "Stock," + _
                         "PrixAchatHT," + _
                         "TotalTTCAchat," + _
                         "TVA," + _
                         "DatePeremption " + _
                         " FROM COMMANDE_DETAILS " + _
                         "WHERE NumeroCommande='" + _
                         fListeDesCommandePourAchat.dsCommandeEnAchat.Tables("COMMANDE").Rows(I).Item("NumeroCommande") + _
                         "'"

                cmdAchat.Connection = ConnectionServeur
                cmdAchat.CommandText = StrSQL
                daAchat = New SqlDataAdapter(cmdAchat)
                daAchat.Fill(dsAchat, "COMMANDE_DETAILS")

                For J = 0 To dsAchat.Tables("COMMANDE_DETAILS").Rows.Count - 1

                    NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
                    dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)

                    NouvelArticle("NumeroAchat") = NumeroAchat
                    NouvelArticle("CodeArticle") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("CodeArticle")
                    NouvelArticle("NumeroLotArticle") = ""
                    NouvelArticle("Designation") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("Designation")
                    NouvelArticle("CodeForme") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("CodeForme")
                    NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", "FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
                    NouvelArticle("Qte") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("Qte")
                    NouvelArticle("QteCommander") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("Qte")
                    NouvelArticle("Stock") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("Stock")
                    NouvelArticle("PrixAchatHT") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("PrixAchatHT")
                    NouvelArticle("TotalAchatHT") = NouvelArticle("PrixAchatHT") * NouvelArticle("Qte")
                    NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", "ARTICLE", "CodeArticle", NouvelArticle("CodeArticle")) * NouvelArticle("Qte")
                    NouvelArticle("Remise") = 0
                    NouvelArticle("TVA") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("TVA")
                    NouvelArticle("DatePeremption") = dsAchat.Tables("COMMANDE_DETAILS").Rows(J).Item("DatePeremption")

                Next

            End If
        Next

        '------------------ pour savoir si l'achat est depuis des commandes ou nn
        'If dsAchat.Tables("COMMANDE_DETAILS").Rows.Count <> 0 Then

        'End If

        I = 0
        '---------------------- Suppression des ligne vides 
        Do While I < dsAchat.Tables("ACHAT_DETAILS").Rows.Count
            If dsAchat.Tables("ACHAT_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                dsAchat.Tables("ACHAT_DETAILS").Rows(I).Delete()
            End If
            I = I + 1
        Loop
        CalculerMontants()

    End Sub

    Private Sub bListe_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

        Dim fInstanceListeDesAchats As New fListeDesAchats
        fInstanceListeDesAchats.ShowDialog()

        NumeroAchat = fListeDesAchats.NumeroAchat

        fInstanceListeDesAchats.Dispose()
        fInstanceListeDesAchats.Close()

        If NumeroAchat <> "" Then
            Dim j As Integer
            Dim DataRowRecherche As DataRow
            Dim NumeroLigne As Integer
            For j = 0 To dsChargement.Tables("ACHAT").Rows.Count - 1
                DataRowRecherche = dsChargement.Tables("ACHAT").Rows(j)
                If DataRowRecherche.Item("NumeroAchat") = NumeroAchat Then
                    NumeroLigne = j
                End If
            Next

            If NumeroLigne <= dsChargement.Tables("ACHAT").Rows.Count - 1 Then
                DataRowRecherche = dsChargement.Tables("ACHAT").Rows(NumeroLigne)
                'chargement des informations entête
                lNumeroAchat.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("NumeroAchat")
                lDateAchat.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("Date")
                cmbFournisseur.SelectedValue = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("CodeFournisseur")

                TotalTTCAchat = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("TotalTTC")
                TotalHTNETAchat = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("TotalHT")
                lTotHT.Text = TotalHTNETAchat.ToString

                lTotalTVA.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("TVA")
                lValeurVenteTTC.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("ValeurVenteTTC")

                lRemise.Text = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("TotalRemise")
                tAutre.Value = dsChargement.Tables("ACHAT").Rows(NumeroLigne)("Autre")

                lTotalTTC.Text = Math.Round(TotalTTCAchat, 3)

                lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsChargement.Tables("ACHAT").Rows(NumeroLigne)("CodePersonnel"))
                lHTNet.Text = (CDbl(lTotHT.Text) - CDbl(lRemise.Text)).ToString

                NumeroAchat = DataRowRecherche.Item("NumeroAchat")
                ChargerDetails()

            End If

        End If

    End Sub

    Private Sub tTotalHT_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tTotalHT.GotFocus
        tTotalHT.SelectAll()
    End Sub

    Private Sub tTotalHT_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTotalHT.KeyUp
        Dim Position As Integer = 0

        If e.KeyCode = Keys.Decimal Then
            Position = InStr(tTotalHT.Text, ".")
            tTotalHT.Text = tTotalHT.Text.Substring(0, Position)
            tTotalHT.Select(tTotalHT.Text.Length, 0)
            Exit Sub
        End If

        If tTotalHT.Text.Contains(".") Then
            Position = InStr(tTotalHT.Text, ".")
            If tTotalHT.Text.Length - Position > 3 Then
                tTotalHT.Text = tTotalHT.Text.Substring(0, Position + 3)
                tTotalHT.Select(tTotalHT.Text.Length, 0)
            End If
        End If

        If e.KeyCode = Keys.Enter Then
            gArticles.Focus()
            gArticles.Col = 1
            gArticles.EditActive = True
        End If

    End Sub

    Private Sub tTotalHT_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tTotalHT.LostFocus
        tTotalHT.Value = tTotalHT.Text
        If tTotalHT.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(tTotalHT.Text, ".")
            If tTotalHT.Text.Length - x = 1 Then
                tTotalHT.Text = tTotalHT.Text + ("00")
            ElseIf tTotalHT.Text.Length - x = 2 Then
                tTotalHT.Text = tTotalHT.Text + ("0")
            End If
        Else
            tTotalHT.Text = tTotalHT.Text + ".000"
        End If
    End Sub

    Private Sub tTotalHT_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles tTotalHT.MouseClick
        tTotalHT.SelectAll()
    End Sub

    Private Sub tTotalHT_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tTotalHT.TextChanged

        'Dim x As Integer = 0

        'x = InStr(tTotalHT.Text, ".")
        'If tTotalHT.Text.Length - x = 3 And x <> 0 Then
        '    bConfirmer.Focus()
        'End If

    End Sub

    Private Sub lTotHT_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lTotHT.Click

    End Sub

    Private Sub lHTNet_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lHTNet.Click

    End Sub

    Private Sub lHTNet_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lHTNet.TextChanged
        lHTNet.Text = lHTNet.Text
        If lHTNet.Text.Contains(".") = True Then
            Dim x As Integer
            x = InStr(lHTNet.Text, ".")
            If lHTNet.Text.Length - x = 1 Then
                lHTNet.Text = lHTNet.Text + ("00")
            ElseIf lHTNet.Text.Length - x = 2 Then
                lHTNet.Text = lHTNet.Text + ("0")
            End If
        Else
            lHTNet.Text = lHTNet.Text + ".000"
        End If
    End Sub

    Private Sub tNumeroBlFact_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNumeroBlFact.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbDateBlFacture.Focus()
        End If
    End Sub

    Private Sub tNumeroBlFact_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNumeroBlFact.TextChanged

    End Sub

    Private Sub cmbDateBlFacture_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbDateBlFacture.KeyUp
        If e.KeyCode = Keys.Enter Then
            tTotalHT.Focus()
        End If
    End Sub
    Public Sub AfficherFicheArticle(ByVal CodeArticle, ByVal StockArticle, ByVal Designation)
        Dim MyFicheArticle As New fFicheArticle

        MyFicheArticle.CodeArticle = CodeArticle
        MyFicheArticle.StockArticle = StockArticle
        MyFicheArticle.DesignationArticle = Designation
        MyFicheArticle.ajoutmodif = "M"

        MyFicheArticle.Init()
        MyFicheArticle.ShowDialog()
        MyFicheArticle.Close()
        MyFicheArticle.Dispose()
    End Sub



    Private Sub gArticles_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles gArticles.KeyPress
        If e.KeyChar = "" Or e.KeyChar = Chr(13) Or e.KeyChar = "." Or (Char.IsDigit(e.KeyChar) And (gArticles.Columns("Designation").Value = "" Or Char.IsDigit(gArticles.Columns("Designation").Value))) Then   '
            Exit Sub
        End If

        Dim StrSQL As String = ""
        Dim ValeurAChercher As String = ""
        ValeurAChercher = gArticles.Columns("Designation").Value + e.KeyChar
        Try
            dsAchat.Tables("ARTICLE1").Clear()
        Catch ex As Exception

        End Try

        StrSQL = "SELECT CodeArticle," + _
                             "Designation," + _
                             "LibelleForme," + _
                             "PrixVenteTTC " + _
                             "FROM ARTICLE " + _
                             "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                             "WHERE " + _
                             "Designation LIKE '" + ValeurAChercher + _
                             "%' AND Supprime=0 ORDER BY Designation"

        cmdAchat.Connection = ConnectionServeur
        cmdAchat.CommandText = StrSQL
        daAchat = New SqlDataAdapter(cmdAchat)
        daAchat.Fill(dsAchat, "ARTICLE1")

        If dsAchat.Tables("ARTICLE1").Rows.Count = 0 And gArticles.Col = 2 Then
            e.Handled = True
        End If
    End Sub

    Private Sub gArticles_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gArticles.MouseClick
        If mode = "Ajout" Then
            If gArticles.Row <> dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 Then
                gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = True
                gArticles.Splits(0).DisplayColumns("Designation").Locked = True
            ElseIf gArticles.Row = dsAchat.Tables("ACHAT_DETAILS").Rows.Count - 1 Then
                gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = False
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
            End If
        End If
    End Sub

    Private Sub bModifier_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

        If ControleDAcces(6, "MODIFICATION_ACHAT") = False Then
            Exit Sub
        End If

        Dim StrSQL As String = ""
        Dim i As Integer = 0

        If (dsAchat.Tables.IndexOf("ACHAT_DETAILS") > -1) Then
            dsAchat.Tables("ACHAT_DETAILS").Clear()
        End If

        'chargement des détails des achat 
        StrSQL = "SELECT NumeroAchat," + _
                 "CodeArticle," + _
                 "Designation," + _
                 "LibelleForme," + _
                 "Qte," + _
                 " '-' AS QteCommander," + _
                 "DatePeremption," + _
                 "NumeroLotArticle," + _
                 "Stock," + _
                 "Remise," + _
                 "PrixAchatHT," + _
                 "PrixVenteTTC," + _
                 "TotalAchatHT," + _
                 "TVA," + _
                 "ACHAT_DETAILS.CodeForme " + _
                 "FROM " + _
                 "ACHAT_DETAILS LEFT OUTER JOIN FORME_ARTICLE " + _
                 "ON ACHAT_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
                 "WHERE NumeroAchat =" + Quote(NumeroAchat) + ""

        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsAchat, "ACHAT_DETAILS")
        cbAchat = New SqlCommandBuilder(daAchat)

        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsAchat
            Catch ex As Exception
            End Try
            .DataMember = "ACHAT_DETAILS"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Qte"
            .Columns("QteCommander").Caption = " Qte Cmd"
            .Columns("NumeroLotArticle").Caption = "Numero Lot"
            .Columns("DatePeremption").Caption = "Date de péremption"
            .Columns("Stock").Caption = "Stock"
            .Columns("Remise").Caption = "Remise"
            .Columns("PrixAchatHT").Caption = "Prix A HT "
            .Columns("PrixVenteTTC").Caption = "Prix V TTC "
            .Columns("TotalAchatHT").Caption = "Total A HT"
            .Columns("TVA").Caption = "TVA"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("CodeArticle").Locked = False
            .Splits(0).DisplayColumns("Designation").Locked = False
            .Splits(0).DisplayColumns("Qte").Locked = False
            .Splits(0).DisplayColumns("Remise").Locked = False
            .Splits(0).DisplayColumns("NumeroLotArticle").Locked = False
            .Splits(0).DisplayColumns("DatePeremption").Locked = False
            .Splits(0).DisplayColumns("PrixAchatHT").Locked = False

            .Splits(0).DisplayColumns("NumeroAchat").Width = 0
            .Splits(0).DisplayColumns("NumeroAchat").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").Width = 80
            .Splits(0).DisplayColumns("Designation").Width = 230
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Qte").Width = 50
            .Splits(0).DisplayColumns("QteCommander").Width = 50
            .Splits(0).DisplayColumns("NumeroLotArticle").Width = 70
            .Splits(0).DisplayColumns("DatePeremption").Width = 70
            .Splits(0).DisplayColumns("Stock").Width = 50
            .Splits(0).DisplayColumns("Remise").Width = 50
            .Splits(0).DisplayColumns("PrixAchatHT").Width = 60
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 60
            .Splits(0).DisplayColumns("TotalAchatHT").Width = 60
            .Splits(0).DisplayColumns("TVA").Width = 50
            .Splits(0).DisplayColumns("CodeForme").Width = 50
            .Splits(0).DisplayColumns("CodeForme").Visible = False

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(0).DisplayColumns("CodeArticle").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Remise").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
        End With
        'ajout d'un nouvel enregistrement vide dans les datatables convenables
        NouvelArticle = dsAchat.Tables("ACHAT_DETAILS").NewRow()
        NouvelArticle("Designation") = ""
        NouvelArticle("CodeArticle") = ""
        dsAchat.Tables("ACHAT_DETAILS").Rows.Add(NouvelArticle)

        For i = 0 To dsAchat.Tables("ACHAT_DETAILS").Columns.Count - 1
            Me.gArticles.Splits(0).DisplayColumns(i).AllowFocus = False
        Next

        With gArticles
            .Splits(0).DisplayColumns("CodeArticle").AllowFocus = True
            .Splits(0).DisplayColumns("Designation").AllowFocus = True
            .Splits(0).DisplayColumns("Qte").AllowFocus = True
            .Splits(0).DisplayColumns("Remise").AllowFocus = True
            .Splits(0).DisplayColumns("NumeroLotArticle").AllowFocus = True
            .Splits(0).DisplayColumns("DatePeremption").AllowFocus = True
            .Splits(0).DisplayColumns("PrixAchatHT").AllowFocus = True
        End With

        GroupeFournisseur.Enabled = True
        mode = "Modif"

        lTotalQte.Visible = True
        lTotalQteAffiche.Visible = True
        tRemisePourcentage.Visible = True
        lRemisePourcentAfficher.Visible = True


    End Sub

    Private Sub bQuitter_Click_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        Me.Hide()
    End Sub
    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub
End Class