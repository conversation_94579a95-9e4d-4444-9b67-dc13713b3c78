﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms
Public Class fSuppressionDesVentes

    Public Source As String = ""

    Dim cmdChargement As New SqlCommand
    Dim cbChargement As New SqlCommandBuilder
    Dim daChargement As New SqlDataAdapter
    Dim dsChargement As New DataSet

    Dim cmdVente As New SqlCommand
    Dim cbVente As New SqlCommandBuilder
    Dim dsVente As New DataSet
    Dim daVente As New SqlDataAdapter

    Dim cmdVenteEntete As New SqlCommand
    Dim cbVenteEntete As New SqlCommandBuilder
    Dim daVenteEntete As New SqlDataAdapter

    Dim cmdVenteDetails As New SqlCommand
    Dim cbVenteDetails As New SqlCommandBuilder
    Dim daVenteDetails As New SqlDataAdapter

    Public NumeroligneVente As Integer = 0

    Dim mode As String = ""

    Public NumeroVente As String = ""

    Public NouvelleVente As DataRow = Nothing 'datarow pour charger l'entête dans la datatable FACTURE_CLIENT
    Public NouvelArticle As DataRow = Nothing 'datarow pour charger les détails dans la datatable FACTURE_CLIENT_DETAIL
    Public dr As DataRow = Nothing 'datarow pour charger dans la datatable ARTICLE qui contient les articles recherchés pendant la saisie de la designation
    Public LigneCourantEnregistrement As DataRow = Nothing  'datarow pour parcourir la datatable detail pour l'enregistrement

    Public TotalTTC As Double = 0.0
    Public TotalHT As Double = 0.0
    Public TotalRemise As Double = 0.0
    Public TVA As Double = 0.0
    Public Timbre As Double = 0.3
    Public MontantCNAM As Double = 0.0
    Public MontantMutuelle As Double = 0.0
    Public TotalNetSansRemise As Double = 0.0

    '------------------------------------------------- les informations de paiement (retour de fenetre paiement)
    Public PaiementOkNo As String = ""
    Public Operateur As Integer = 0
    Public MonatantRecu As Double = 0.0
    Public ModePaiement As Integer = 0
    Public NumeroCheque As String = "-"
    Public MontantCheque As Double = 0.0
    '------------------------------------------------- les information CNAM (retour de maquette CNAM)
    Public CodeMalade As Integer = 0
    Public CodeAPCI As Integer = 0
    Public DureeTraitement As Integer = 0
    Public ConfirmerOkNo As Boolean = False
    Public NoteVente As String = ""
    Public MontantPrixEnChargeParLaCNAMAppareillage As Double = 0.0

    Public NomMalade As String = ""
    Public Rang As Integer = 0
    Public DateNaissance As Date
    Public CodeLienDeParente As Integer = 0
    Public LibelleLienDeParente As String = ""

    '---------------------------------------- variable pour attribuer a partir d'une vente un code cnam
    '----------------------------------------  à un client cnamiste et manque un code 
    Dim NouveuxCodeCnam As Boolean = False
    '---------------------------------------- variable pour attribuer a partir d'une vente un code cnam
    '---------------------------------------- à un Medecin cnamiste et manque un code 
    Dim NouveuxCodeCnamMedecin As Boolean = False
    '---------------------------------------- variable de retour de la liste des ventes en instance
    Dim NumeroventeInstance As String = ""
    '---------------------------------------- variables pour confirmer la mise en instance d'une vente
    Dim ComfirmerMettreEnINstance As Boolean = False
    Dim ConfirmerInstance As Boolean = False
    Dim NomInstance As String = ""
    Dim CodeOperateurInstance As String = ""
    '---------------------------------------- variable pour recuperer le code article à partir de la fenêtre recherchre multicritere
    Dim CodeArticleRechercheMC As String = ""
    '---------------------------------------- variable pour que l'apuit sur la touche ok d'un message d'information ne sera pas traité sur la liste
    Dim OKMessageInfo As Boolean = False
    '---------------------------------------- variable pour suuprimer le dernier lettre qui rend la designation introuvable lors d'un saisi dans la liste 
    Public NouveauValeur As String = ""

    Dim NombreVenteInstance As Integer = 0

    '---------------------------------------- variable de parametrage PHARMA
    Dim ValiderQteDe1 As Boolean = False

    Dim ValeurEntree As String = ""


    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If Source = "SUPRESSION" Then
            If argument = "117" And bRecherche.Visible = True Then
                bRecherche_Click(sender, e)
            End If
            If argument = "118" And bSupprimer.Visible = True Then
                bSupprimer_Click(sender, e)
            End If
            '--------------------- boutton close 
            If argument = "123" And bQuitter.Enabled = True Then
                bQuitter_Click(sender, e)
            End If
            Exit Sub
        End If


        If argument = "117" And bRecherche.Visible = True Then
            bRecherche_Click(sender, e)
        End If

        '--------------------- boutton close 
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub
    Public Sub Init()


        'chargement des Clients
        initClient()

        'chargement des Medecins
        initMedecin()

        'chargement des Mutuelles
        initMutuelle()

        'chargement des tranches
        initTranche()

        'chargement des numéros de ventes          
        initNumeroVente()

        'Appel Pour selectionner le dernier ligne 
        NumeroligneVente = selectionDernierLigneVente()

        'Init Vente Entete
        initVenteEntete()

        'chargement des détails des ventes 
        initVenteDetails()

        'Appel pour charger les information de l'Etete en question
        ChargerVente(NumeroligneVente)

        'pour initaliser la grid
        initgArticlesInit()

        'refrech 
        gArticles.Refresh()

        'pour initialiser la grid des articles
        initgArticles()

        'pour initialiser les listes des articles
        initArticle()

        'affichage du nombre des ventes en instance 
        affichenombreVenteInstance()

        ' affichage du dernier numéro d'ordonnancier 
        affichedernierNumeroOrdonnancier()

    End Sub
    'pur afficher le dernier numéro ordonnancier

    Private Sub affichedernierNumeroOrdonnancier()
        Dim StrSQL As String = ""

        Try
            StrSQL = " SELECT MAX(Numero) FROM ORDONNANCIER "

            cmdVente.Connection = ConnectionServeur
            cmdVente.CommandText = StrSQL

            lNumeroOrdonnancier.Text = "Num Ordonnancier :" + cmdVente.ExecuteScalar().ToString
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " affichedernierNumeroOrdonnancier", ex.Message, "0000287", "Erreur d'exécution de affichedernierNumeroOrdonnancier ", True, True, True)

        End Try
    End Sub
    'pour affichier le nombre de vente en instance

    Private Sub affichenombreVenteInstance()
        Dim StrSQL As String = ""

        Try

            StrSQL = " SELECT COUNT(*) FROM VENTE_INSTANCE "

            cmdVente.Connection = ConnectionServeur
            cmdVente.CommandText = StrSQL


            NombreVenteInstance = cmdVente.ExecuteScalar()





            lInfoBulle.Text = ""

            dtpDateOrdonnance.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
            dtpDateOrdonnance.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
            cmbTranche.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList

            ValiderQteDe1 = True
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " affichenombreVenteInstance", ex.Message, "0000288", "Erreur d'exécution de affichenombreVenteInstance ", True, True, True)

        End Try

    End Sub
    Private Sub initArticle()
        Dim StrSQL As String = ""


        '--------------------initialisation de la datatable article qui est utilisé dans la liste de 
        '--------------------recherche alimenté selon les entrées de l'utilisateur dans la colonne designation
        Try


            StrSQL = "SELECT CodeArticle," + _
                     "Designation," + _
                     "LibelleForme," + _
                     "PrixVenteTTC " + _
                     "FROM ARTICLE,FORME_ARTICLE " + _
                     "WHERE ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                     "AND Designation LIKE '" + gArticles.Columns("Designation").Value.ToString.Replace("'", "''") + _
                     "'ORDER BY Designation"

            cmdVente.Connection = ConnectionServeur
            cmdVente.CommandText = StrSQL
            daVente = New SqlDataAdapter(cmdVente)
            daVente.Fill(dsVente, "ARTICLE")

            With gListeRecherche
                .Columns.Clear()
                .DataSource = dsVente
                .DataMember = "ARTICLE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code Article"
                .Columns("Designation").Caption = "Designation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("PrixVenteTTC").Caption = "Prix de vente"

                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centre tous les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Visible = False
                Next

                .Splits(0).DisplayColumns("CodeArticle").Visible = True
                .Splits(0).DisplayColumns("Designation").Visible = True
                '.Splits(0).DisplayColumns("LibelleForme").Visible = True
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
                .Splits(0).DisplayColumns("Designation").Width = 300
                '.Splits(0).DisplayColumns("LibelleForme").Width = 100
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 20
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
            End With

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " initArticle", ex.Message, "0000289", "Erreur d'exécution de initArticle ", True, True, True)

        End Try
    End Sub
    'pour initialiser la grid dans Init()
    Private Sub initgArticlesInit()
        Try


            With gArticles
                .Columns.Clear()
                Try
                    .DataSource = dsVente
                Catch ex As Exception
                End Try
                .DataMember = "VENTE_DETAILS"
                .Rebind(False)
                .Columns("CodeABarre").Caption = "Code article"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Qte").Caption = "Qte"
                .Columns("DateDePeremption").Caption = "Date de péremption"
                .Columns("PrixTTC").Caption = "Prix TTC "
                .Columns("TotalTTC").Caption = "Total TTC "
                .Columns("TVA").Caption = "TVA"
                .Columns("Remise").Caption = "Remise"
                .Columns("Stock").Caption = "Stock"

                ' Centrer toutes les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                .Splits(0).DisplayColumns(2).Style.HorizontalAlignment = AlignHorzEnum.Near
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Locked = True
                Next

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("PrixTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("Remise").Style.HorizontalAlignment = AlignHorzEnum.Far

                .Splits(0).DisplayColumns("NumeroVente").Width = 0
                .Splits(0).DisplayColumns("CodeABarre").Width = 60
                .Splits(0).DisplayColumns("NumeroLotArticle").Width = 0
                .Splits(0).DisplayColumns("Designation").Width = 300
                '.Splits(0).DisplayColumns("LibelleForme").Width = 80
                .Splits(0).DisplayColumns("Qte").Width = 50
                .Splits(0).DisplayColumns("Stock").Width = 50
                .Splits(0).DisplayColumns("DateDePeremption").Width = 70
                .Splits(0).DisplayColumns("PrixTTC").Width = 60
                .Splits(0).DisplayColumns("TotalTTC").Width = 60
                .Splits(0).DisplayColumns("TVA").Width = 50
                .Splits(0).DisplayColumns("Remise").Width = 50

                .Splits(0).DisplayColumns("NumeroVente").Visible = False
                .Splits(0).DisplayColumns("NumeroVente").AllowSizing = False
                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
                .Splits(0).DisplayColumns("NumeroLotArticle").Width = 0
                .Splits(0).DisplayColumns("NumeroLotArticle").Visible = False
                .Splits(0).DisplayColumns("PrixHT").Width = 0
                .Splits(0).DisplayColumns("PrixHT").Visible = False
                .Splits(0).DisplayColumns("TotalHT").Width = 0
                .Splits(0).DisplayColumns("TotalHT").Visible = False
                .Splits(0).DisplayColumns("TotalTVA").Width = 50
                .Splits(0).DisplayColumns("TotalTVA").Visible = False
                .Splits(0).DisplayColumns("PrixAchat").Width = 0
                .Splits(0).DisplayColumns("PrixAchat").Visible = False
                .Splits(0).DisplayColumns("Honoraire").Width = 0
                .Splits(0).DisplayColumns("Honoraire").Visible = False

                .Splits(0).DisplayColumns("CodeArticle").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("Remise").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.BackColor = Color.FromArgb(255, 225, 240, 255)
                Next


                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                .AllowSort = False
                .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

            End With
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " initgArticlesInit", ex.Message, "0000290", "Erreur d'exécution de initgArticlesInit ", True, True, True)

        End Try
    End Sub
    Private Sub initVenteDetails()

        Dim StrSQL As String

        'chargement des détails des ventes 
        Try
            StrSQL = "SELECT NumeroVente," + _
                     "CodeArticle," + _
                     "CodeABarre," + _
                     "Designation," + _
                     "'' AS LibelleForme," + _
                     "Qte," + _
                     "Stock," + _
                     "DateDePeremption, " + _
                     "PrixTTC," + _
                     "TotalTTC," + _
                     "TVA," + _
                     "Remise," + _
                     "PrixHT," + _
                     "TotalHT," + _
                     "TotalTVA," + _
                     "PrixAchat," + _
                     "Honoraire," + _
                     "VENTE_DETAILS.CodeForme," + _
                     "NumeroLotArticle," + _
                     "PriseEnCharge," + _
                     "AccordPrealable," + _
                     "TarifDeReference, " + _
                      "'' AS Vide " + _
                     "FROM VENTE_DETAILS " + _
                      "WHERE  " + _
                     "NumeroVente =" + Quote(NumeroVente) + ""

            cmdVenteDetails.Connection = ConnectionServeur
            cmdVenteDetails.CommandText = StrSQL
            daVenteDetails = New SqlDataAdapter(cmdVenteDetails)
            daVenteDetails.Fill(dsVente, "VENTE_DETAILS")
            cbVenteDetails = New SqlCommandBuilder(daVenteDetails)

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " initVenteDetails", ex.Message, "0000291", "Erreur d'exécution de initVenteDetails ", True, True, True)

        End Try

    End Sub
    Public Sub initVenteEntete()
        Dim StrSQL As String
        Try
            'chargement des Entêtes des Ventes        
            StrSQL = "SELECT Top(0) * FROM VENTE ORDER BY NumeroVente ASC"
            cmdVenteEntete.Connection = ConnectionServeur
            cmdVenteEntete.CommandText = StrSQL
            daVenteEntete = New SqlDataAdapter(cmdVenteEntete)
            daVenteEntete.Fill(dsVente, "VENTE")
            cbVenteEntete = New SqlCommandBuilder(daVenteEntete)

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " initVenteEntete", ex.Message, "0000292", "Erreur d'exécution de initVenteEntete ", True, True, True)

        End Try

    End Sub
    Private Sub initNumeroVente()
        Dim StrSQL As String = ""

        'chargement des numéros de vente
        Try


            StrSQL = "SELECT  NumeroVente FROM VENTE ORDER BY NumeroVente ASC"
            cmdChargement.Connection = ConnectionServeur
            cmdChargement.CommandText = StrSQL
            daChargement = New SqlDataAdapter(cmdChargement)
            daChargement.Fill(dsVente, "NUMEROS_VENTE")

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " initNumeroVente", ex.Message, "0000293", "Erreur d'exécution de initNumeroVente ", True, True, True)

        End Try

    End Sub

    Private Sub initTranche()
        Dim StrSQL As String = ""

        'chargement des Tranche
        Try


            StrSQL = "SELECT NumeroTranche FROM TRANCHE ORDER BY NumeroTranche ASC"
            cmdChargement.Connection = ConnectionServeur
            cmdChargement.CommandText = StrSQL
            daChargement = New SqlDataAdapter(cmdChargement)
            daChargement.Fill(dsVente, "TRANCHE")
            cmbTranche.DataSource = dsVente.Tables("TRANCHE")
            cmbTranche.DisplayMember = "NumeroTranche"
            cmbTranche.ColumnHeaders = False
            cmbTranche.Splits(0).DisplayColumns("NumeroTranche").Width = 160
            cmbTranche.Splits(0).ExtendRightColumn = True

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " initTranche", ex.Message, "0000294", "Erreur d'exécution de initTranche ", True, True, True)

        End Try

    End Sub
    Private Sub initMutuelle()
        Dim StrSQL As String = ""

        'chargement des Mutuelles
        Try


            StrSQL = "SELECT CodeMutuelle,NomMutuelle FROM MUTUELLE ORDER BY NomMutuelle ASC"
            cmdChargement.Connection = ConnectionServeur
            cmdChargement.CommandText = StrSQL
            daChargement = New SqlDataAdapter(cmdChargement)
            daChargement.Fill(dsVente, "MUTUELLE")
            cmbMutuelle.DataSource = dsVente.Tables("MUTUELLE")
            cmbMutuelle.ValueMember = "CodeMutuelle"
            cmbMutuelle.DisplayMember = "NomMutuelle"
            cmbMutuelle.ColumnHeaders = False
            cmbMutuelle.Splits(0).DisplayColumns("CodeMutuelle").Width = 0
            cmbMutuelle.Splits(0).DisplayColumns("NomMutuelle").Width = 160
            cmbMutuelle.Splits(0).ExtendRightColumn = True


        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " initMutuelle", ex.Message, "0000295", "Erreur d'exécution de initMutuelle ", True, True, True)

        End Try
    End Sub

    Private Sub initMedecin()
        Dim StrSQL As String = ""

        'chargement des Medecins
        Try


            StrSQL = "SELECT CodeMedecin,NomMedecin FROM MEDECIN ORDER BY NomMedecin ASC"
            cmdChargement.Connection = ConnectionServeur
            cmdChargement.CommandText = StrSQL
            daChargement = New SqlDataAdapter(cmdChargement)
            daChargement.Fill(dsVente, "MEDECIN")
            cmbMedecin.DataSource = dsVente.Tables("MEDECIN")
            cmbMedecin.ValueMember = "CodeMedecin"
            cmbMedecin.DisplayMember = "NomMedecin"
            cmbMedecin.ColumnHeaders = False
            cmbMedecin.Splits(0).DisplayColumns("CodeMedecin").Width = 0
            cmbMedecin.Splits(0).DisplayColumns("NomMedecin").Width = 160
            cmbMedecin.Splits(0).ExtendRightColumn = True
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " initMedecin", ex.Message, "0000296", "Erreur d'exécution de initMedecin ", True, True, True)

        End Try

    End Sub
    Public Function RecupereNumero()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max([NumeroVente]) FROM [VENTE] WHERE SUBSTRING (NumeroVente,0,5)=YEAR(getdate())"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()

            '----------------------- récupération du dernier numero sequenciel de l année en cours ou initialisation d un nouveau compteur  ----
            If ValeurActuel.Length > 4 Then
                If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                    Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                    Numero = Numero + 1
                    numeroConvertit = Numero.ToString
                    For i = 0 To 5 - numeroConvertit.Length
                        numeroConvertit = "0" + numeroConvertit
                    Next
                    ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
                Else
                    ValeurRetour = Year(Today).ToString + "/" + "000001"
                End If
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " RecupereNumero", ex.Message, "0000297", "Erreur d'exécution de RecupereNumero ", True, True, True)

        End Try
        Return ValeurRetour

    End Function

    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        Try
            Quote(ValeurCle)
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurCle)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " RecupererValeurExecuteScalaire", ex.Message, "0000298", "Erreur d'exécution de RecupererValeurExecuteScalaire ", True, True, True)

        End Try
        Return (Valeur)
    End Function


    Private Sub bPrevious_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPrevious.Click
        Try
            'Suivi du scénario
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", "bPrevious_Click", "NoException", "NoError", "Clic sur le bouton bPrevious", False, True, False)

            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner l'element precedent 
            selectionLigneVentePrecedent()

            'Appel pour charger les information de Vente en question
            ChargerVente(NumeroligneVente)


        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " bPrevious_Click", ex.Message, "0000299 ", "Erreur d'exécution de bPrevious_Click ", True, True, True)

        End Try

    End Sub
    Private Sub selectionLigneVentePrecedent()
        Try
            'Affécter le numéro 1 au variable global  NumeroligneVente
            NumeroligneVente = NumeroligneVente - 1

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " selectionLigneVentePrecedent", ex.Message, "0000300", "Erreur d'exécution de selectionLigneVentePrecedent ", True, True, True)

        End Try

    End Sub
    Private Sub bFirst_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFirst.Click
        Try
            'Suivi du scénario
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", "bFirst_Click", "NoException", "NoError", "Clic sur le bouton bFirst", False, True, False)

            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner le dernier ligne 
            selectionPrmierLigneVente()

            'Appel pour charger les information de Vente en question
            ChargerVente(NumeroligneVente)

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " bFirst_Click", ex.Message, "0000301", "Erreur d'exécution de bFirst_Click ", True, True, True)

        End Try

    End Sub

    Private Sub selectionPrmierLigneVente()
        Try
            'Affécter le numéro 1 au variable global  NumeroligneVente
            NumeroligneVente = 1

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " selectionPrmierLigneVente", ex.Message, "0000302", "Erreur d'exécution de selectionPrmierLigneVente ", True, True, True)

        End Try

    End Sub
    Private Sub bNext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bNext.Click

        Try
            'Suivi du scénario
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", "bNext_Click", "NoException", "NoError", "Clic sur le bouton bNext", False, True, False)

            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner l'element suivant 
            selectionLigneVenteSuivant()

            'Appel pour charger les information de Vente en question
            ChargerVente(NumeroligneVente)

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " bNext_Click", ex.Message, "0000303", "Erreur d'exécution de bNext_Click ", True, True, True)

        End Try

    End Sub
    Private Sub bLast_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLast.Click

        Try
            'Suivi du scénario
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", "bLast_Click", "NoException", "NoError", "Clic sur le bouton bLast", False, True, False)

            'Changer le mode en Mode Consultation
            mode = "Consultation"

            'Appel Pour selectionner la derniere ligne 
            NumeroligneVente = selectionDernierLigneVente()

            'Appel pour charger les information de le Vente en question
            ChargerVente(NumeroligneVente)


        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " bLast_Click", ex.Message, "0000304", "Erreur d'exécution de bLast_Click ", True, True, True)

        End Try

    End Sub
    Private Sub selectionLigneVenteSuivant()
        Try
            'Affécter le numéro 1 au variable global  NumeroligneVente
            NumeroligneVente = NumeroligneVente + 1
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " selectionLigneVenteSuivant", ex.Message, "0000305", "Erreur d'exécution de selectionLigneVenteSuivant ", True, True, True)

        End Try
    End Sub



    Private Sub tNetAPayer_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tNetAPayer.LostFocus
        Try
            tNetAPayer.Text = Format(CDbl(tNetAPayer.Text), "0.000")

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " tNetAPayer_LostFocus", ex.Message, "0000306", "Erreur d'exécution de tNetAPayer_LostFocus ", True, True, True)

        End Try
    End Sub
    Private Sub bRecherche_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRecherche.Click
        Try
            'Suivi du scénario
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", "bRecherche_Click", "NoException", "NoError", "Clic sur le bouton bRecherche", False, True, False)

            tRecherche.Visible = True
            tRecherche.Text = System.DateTime.Now.Year.ToString + "/"
            tRecherche.Focus()
            tRecherche.Select(tRecherche.Text.Length, 0)
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " bRecherche_Click", ex.Message, "0000307", "Erreur d'exécution de bRecherche_Click ", True, True, True)

        End Try
    End Sub

    Private Sub tRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRecherche.KeyUp

        Try
            If e.KeyCode = Keys.Enter And tRecherche.Text <> "" Then

                'Recuprére le Row de l'element sellectioné

                'Lors du press Enter, on va appler la procedure rechercheVente

                rechercheVente(tRecherche.Text)


            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " tRecherche_KeyUp", ex.Message, "0000308", "Erreur d'exécution de tRecherche_KeyUp ", True, True, True)

        End Try
    End Sub
    Private Sub tRecherche_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tRecherche.LostFocus
        tRecherche.Visible = False
    End Sub
    Private Sub rechercheVente(ByVal pNumeroVente As String)

        '----------------------------------Traitement
        Try
            If tRecherche.Text.Length < 11 Then
                tRecherche.Text = tRecherche.Text.Substring(0, 5) + tRecherche.Text.Substring(5, tRecherche.Text.Length - 5).PadLeft(6, "0")
            End If

            'Recuperer la valeur de la row
            recupererNumRowRechrche()


            If NumeroligneVente <> 0 Then
                ChargerVente(NumeroligneVente)
            End If

            tRecherche.Value = ""
            tRecherche.Visible = False
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " rechercheVente", ex.Message, "0000309", "Erreur d'exécution de rechercheVente ", True, True, True)

        End Try
    End Sub
    Private Sub recupererNumRowRechrche()
        Dim StrSQL As String = ""
        Try
            '------------------------- affichage du nombre de Vente en instance 
            StrSQL = " SELECT RowNumber " + _
                     " FROM (select ROW_NUMBER() OVER(ORDER BY NumeroVente) " + _
                     " AS 'RowNumber' , NumeroVente  from VENTE) AS VENTELISTE " + _
                     " where VENTELISTE.NumeroVente =  " & Quote(tRecherche.Text)

            cmdVente.Connection = ConnectionServeur
            cmdVente.CommandText = StrSQL

            NumeroligneVente = cmdVente.ExecuteScalar()

            If NumeroligneVente = 0 Then
                MsgBox("Vente inéxistant", MsgBoxStyle.Exclamation, "Recherche")
                NumeroligneVente = selectionDernierLigneVente()
            End If

            'Tester si on atteint la premiere ligne
            'pour desactiver les BTN Précedent et Premier élément        
            If NumeroligneVente = 1 Or NumeroligneVente = 0 Then

                bPrevious.Enabled = False
                bFirst.Enabled = False

            Else

                bPrevious.Enabled = True
                bFirst.Enabled = True

            End If

            'Tester si on atteint la derniere ligne
            'pour desactiver les BTN Siuvant et Dernier élément
            If NumeroligneVente = selectionDernierLigneVente() Then

                bNext.Enabled = False
                bLast.Enabled = False

            Else

                bNext.Enabled = True
                bLast.Enabled = True

            End If


        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " recupererNumRowRechrche", ex.Message, "0000310", "Erreur d'exécution de recupererNumRowRechrche ", True, True, True)

        End Try

    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click
        'Suivi du scénario
        fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", "bSupprimer_Click", "NoException", "NoError", "Clic sur le bouton bSupprimer", False, True, False)

        Try
            If MsgBox("Voulez vous vraiment supprimer cette vente " + lNumeroVente.Text + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then

                If NumeroVente = "" Then
                    MsgBox("Aucune vente à supprimer !", MsgBoxStyle.Critical, "Information")
                    Exit Sub
                End If

                Dim ConfirmerEnregistrer As Boolean = False
                Dim CodeOperateur As String = ""

                Try
                    cmdVente.Connection = ConnectionServeur
                    cmdVente.CommandText = "SELECT COUNT(*) FROM RELEVE_CNAM_DETAILS WHERE NumeroVente = " & Quote(lNumeroVente.Text)
                    If cmdVente.ExecuteScalar() > 0 Then
                        MsgBox("Relevé déja généré !", MsgBoxStyle.Critical, "Information")
                        Exit Sub
                    End If
                Catch
                End Try

                Try
                    cmdVente.Connection = ConnectionServeur
                    cmdVente.CommandText = "SELECT COUNT(*) FROM RELEVE_MUTUELLE_DETAILS WHERE NumeroVente = " & Quote(lNumeroVente.Text)
                    If cmdVente.ExecuteScalar() > 0 Then
                        MsgBox("Relevé déja généré !", MsgBoxStyle.Critical, "Information")
                        Exit Sub
                    End If
                Catch
                End Try


                '------------------------------ demande du mot de passe
                Dim myMotDePasse As New fMotDePasse
                myMotDePasse.ShowDialog()

                ConfirmerEnregistrer = fMotDePasse.Confirmer
                CodeOperateur = fMotDePasse.CodeOperateur

                myMotDePasse.Dispose()
                myMotDePasse.Close()

                If ConfirmerEnregistrer = False Then
                    Exit Sub
                End If

                If (dsChargement.Tables.IndexOf("VENTE_A_SUPPRIME") > -1) Then
                    dsChargement.Tables("VENTE_A_SUPPRIME").Clear()
                End If
                If (dsChargement.Tables.IndexOf("VENTE_SUPPRIME") > -1) Then
                    dsChargement.Tables("VENTE_SUPPRIME").Clear()
                End If
                If (dsChargement.Tables.IndexOf("VENTE_DETAILS_A_SUPPRIME") > -1) Then
                    dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Clear()
                End If
                If (dsChargement.Tables.IndexOf("VENTE_SUPPRIME_DETAILS") > -1) Then
                    dsChargement.Tables("VENTE_SUPPRIME_DETAILS").Clear()
                End If

                Dim StrSQL As String = ""
                Dim DataRowVenteSupprime As DataRow = Nothing
                'Dim DateRowVenteDetail As DataRow = Nothing
                Dim i As Integer = 0
                Dim cmd As New SqlCommand
                Dim StrMajLOT As String = ""
                Dim NumeroLot As String = "RIEN"
                Dim NouveauNumeroLot As String = ""
                Dim NumeroVenteSupprime As String = ""
                Dim NumeroReglementClient As String = ""

                NumeroVenteSupprime = RecupereNumeroVenteSupprime()

                'chargement des Entêtes de la vente          
                StrSQL = "SELECT * FROM VENTE WHERE NumeroVente='" + NumeroVente + "'"
                cmdChargement.Connection = ConnectionServeur
                cmdChargement.CommandText = StrSQL
                daChargement = New SqlDataAdapter(cmdChargement)
                daChargement.Fill(dsChargement, "VENTE_A_SUPPRIME")

                'chargement de l'entête dans la datatble qui correspond à la table VENTE_SUPPRIME de la vente 
                StrSQL = "SELECT TOP(0) * FROM VENTE_SUPPRIME "
                cmdChargement.Connection = ConnectionServeur
                cmdChargement.CommandText = StrSQL
                daChargement = New SqlDataAdapter(cmdChargement)
                daChargement.Fill(dsChargement, "VENTE_SUPPRIME")
                cbChargement = New SqlCommandBuilder(daChargement)

                'ajout d'un nouvel enregistrement vide dans les datatables convenables
                DataRowVenteSupprime = dsChargement.Tables("VENTE_SUPPRIME").NewRow()

                DataRowVenteSupprime("NumeroVenteSupprime") = NumeroVenteSupprime

                DataRowVenteSupprime("NumeroVente") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("NumeroVente")
                DataRowVenteSupprime("Date") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("Date")
                DataRowVenteSupprime("TotalHT") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("TotalHT")
                DataRowVenteSupprime("TotalTTC") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("TotalTTC")

                DataRowVenteSupprime("TVA") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("TVA")
                DataRowVenteSupprime("TotalRemise") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("TotalRemise")
                DataRowVenteSupprime("Timbre") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("Timbre")
                DataRowVenteSupprime("CodeClient") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("CodeClient")

                DataRowVenteSupprime("CodePersonnel") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("CodePersonnel")
                DataRowVenteSupprime("CodeAPCI") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("CodeAPCI")
                DataRowVenteSupprime("CodeDeFamille") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("CodeDeFamille")
                DataRowVenteSupprime("CodeMedecinFamille") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("CodeMedecinFamille")

                DataRowVenteSupprime("CodeMedecinPrescripteur") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("CodeMedecinPrescripteur")
                DataRowVenteSupprime("LibellePoste") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("LibellePoste")
                DataRowVenteSupprime("Recu") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("Recu")
                DataRowVenteSupprime("DateOrdonnance") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("DateOrdonnance")

                DataRowVenteSupprime("MontantCnam") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("MontantCnam")
                DataRowVenteSupprime("MontantMutuelle") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("MontantMutuelle")
                DataRowVenteSupprime("DureeTraitement") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("DureeTraitement")
                DataRowVenteSupprime("Note") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("Note")

                DataRowVenteSupprime("CodeNatureReglement") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("CodeNatureReglement")
                DataRowVenteSupprime("CodeMutuelle") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("CodeMutuelle")
                DataRowVenteSupprime("NomMalade") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("NomMalade")
                DataRowVenteSupprime("Rang") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("Rang")

                DataRowVenteSupprime("DateNaissance") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("DateNaissance")
                DataRowVenteSupprime("CodeLienDeParente") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("CodeLienDeParente")
                DataRowVenteSupprime("LibelleLienDeParente") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("LibelleLienDeParente")
                DataRowVenteSupprime("TiersPayant") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("TiersPayant")

                DataRowVenteSupprime("PriseEnCharge") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("PriseEnCharge")
                DataRowVenteSupprime("Appareillage") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("Appareillage")
                DataRowVenteSupprime("IdentifiantCNAMMedecin") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("IdentifiantCNAMMedecin")
                DataRowVenteSupprime("Libelle1") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("Libelle1")

                DataRowVenteSupprime("Libelle2") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("Libelle2")
                DataRowVenteSupprime("Libelle3") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("Libelle3")
                DataRowVenteSupprime("Libelle4") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("Libelle4")
                DataRowVenteSupprime("Libelle5") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("Libelle5")

                DataRowVenteSupprime("NumeroFacture") = dsChargement.Tables("VENTE_A_SUPPRIME").Rows(0).Item("NumeroFacture")
                DataRowVenteSupprime("DateSuppression") = System.DateTime.Now
                DataRowVenteSupprime("CodePersonnelSupprime") = CodeOperateur
                DataRowVenteSupprime("NomPersonnelSupprime") = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", CodeOperateur)
                DataRowVenteSupprime("Vider") = False

                dsChargement.Tables("VENTE_SUPPRIME").Rows.Add(DataRowVenteSupprime)


                Try
                    daChargement.Update(dsChargement, "VENTE_SUPPRIME")
                Catch ex As Exception
                    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                    dsChargement.Reset()
                End Try


                'chargement des Details de la vente 
                StrSQL = "SELECT * FROM VENTE_DETAILS WHERE NumeroVente='" + NumeroVente + "'"
                cmdChargement.Connection = ConnectionServeur
                cmdChargement.CommandText = StrSQL
                daChargement = New SqlDataAdapter(cmdChargement)
                daChargement.Fill(dsChargement, "VENTE_DETAILS_A_SUPPRIME")

                'chargement des Details de la vente dans une datatable qui correspond a la tabel VENTE_SUPPRIME_DETAILS
                StrSQL = "SELECT * FROM VENTE_SUPPRIME_DETAILS "
                cmdChargement.Connection = ConnectionServeur
                cmdChargement.CommandText = StrSQL
                daChargement = New SqlDataAdapter(cmdChargement)
                daChargement.Fill(dsChargement, "VENTE_SUPPRIME_DETAILS")
                cbChargement = New SqlCommandBuilder(daChargement)

                For i = 0 To dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows.Count - 1

                    DataRowVenteSupprime = dsChargement.Tables("VENTE_SUPPRIME_DETAILS").NewRow()

                    DataRowVenteSupprime("NumeroVenteSupprime") = NumeroVenteSupprime

                    DataRowVenteSupprime("NumeroVente") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("NumeroVente")
                    DataRowVenteSupprime("CodeArticle") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("CodeArticle")
                    DataRowVenteSupprime("NumeroLotArticle") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("NumeroLotArticle")
                    DataRowVenteSupprime("Designation") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("Designation")

                    DataRowVenteSupprime("CodeForme") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("CodeForme")
                    DataRowVenteSupprime("Qte") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("Qte")
                    DataRowVenteSupprime("PrixAchat") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("PrixAchat")
                    DataRowVenteSupprime("PrixHT") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("PrixHT")

                    DataRowVenteSupprime("TotalHT") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("TotalHT")
                    DataRowVenteSupprime("PrixTTC") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("PrixTTC")
                    DataRowVenteSupprime("TotalTTC") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("TotalTTC")
                    DataRowVenteSupprime("Remise") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("Remise")

                    DataRowVenteSupprime("TVA") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("TVA")
                    DataRowVenteSupprime("TotalTVA") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("TotalTVA")
                    DataRowVenteSupprime("Honoraire") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("Honoraire")
                    DataRowVenteSupprime("Stock") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("Stock")
                    DataRowVenteSupprime("DateDePeremption") = dsChargement.Tables("VENTE_DETAILS_A_SUPPRIME").Rows(i).Item("DateDePeremption")

                    dsChargement.Tables("VENTE_SUPPRIME_DETAILS").Rows.Add(DataRowVenteSupprime)

                Next

                Try
                    daChargement.Update(dsChargement, "VENTE_SUPPRIME_DETAILS")
                Catch ex As Exception
                    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                    dsChargement.Reset()
                End Try

                ''------------- ajout des quantités des articles dans les lots convenables

                'For i = 0 To dsChargement.Tables("VENTE_SUPPRIME_DETAILS").Rows.Count - 1

                '    StrSQL = " SELECT NumeroLotArticle FROM [LOT_ARTICLE] where NumeroLotArticle ='" + _
                '                            dsChargement.Tables("VENTE_SUPPRIME_DETAILS").Rows(i).Item("NumeroLotArticle") + _
                '                            "' AND CodeArticle='" + _
                '                            dsChargement.Tables("VENTE_SUPPRIME_DETAILS").Rows(i).Item("CodeArticle") + _
                '                            "'"

                '    cmd.Connection = ConnectionServeur
                '    cmd.CommandText = StrSQL
                '    Try
                '        NumeroLot = cmd.ExecuteScalar()
                '    Catch ex As Exception
                '        Console.WriteLine(ex.Message)
                '    End Try

                '    If NumeroLot <> Nothing Then
                '        StrMajLOT = "Update LOT_ARTICLE set QteLotArticle=QteLotArticle + " + _
                '                    dsChargement.Tables("VENTE_SUPPRIME_DETAILS").Rows(i).Item("Qte").ToString + _
                '                    " where NumeroLotArticle = '" + _
                '                    NumeroLot.ToString + "' and CodeArticle ='" + _
                '                    dsChargement.Tables("VENTE_SUPPRIME_DETAILS").Rows(i).Item("CodeArticle") + "'"

                '        cmd.Connection = ConnectionServeur
                '        cmd.CommandText = StrMajLOT
                '        Try
                '            cmd.ExecuteNonQuery()
                '        Catch ex As Exception
                '            Console.WriteLine(ex.Message)
                '        End Try

                '    Else
                '        '------------ n'y a pas de lot avec cette date de péremption : création d'un nouveau lot
                '        'récuperation du dernier numero de lot
                '        StrSQL = " SELECT max(NumeroLotArticle) FROM [LOT_ARTICLE] WHERE CodeArticle='" + _
                '                 dsChargement.Tables("VENTE_SUPPRIME_DETAILS").Rows(i).Item("CodeArticle") + "'"
                '        cmd.Connection = ConnectionServeur
                '        cmd.CommandText = StrSQL
                '        Try
                '            NouveauNumeroLot = cmd.ExecuteScalar()
                '            If Trim(NouveauNumeroLot) <> "" Then
                '                NouveauNumeroLot = (Convert.ToInt32(NouveauNumeroLot) + 1).ToString
                '            Else
                '                NouveauNumeroLot = "0"
                '            End If

                '        Catch ex As Exception
                '            Console.WriteLine(ex.Message)
                '            NouveauNumeroLot = "0"
                '        End Try

                '        StrMajLOT = "INSERT INTO LOT_ARTICLE " + _
                '                    "(""NumeroLotArticle"",""CodeArticle"",""QteLotArticle"",""DatePeremptionArticle"") " + _
                '                    " VALUES('" + _
                '                    NouveauNumeroLot + "','" + _
                '                    dsChargement.Tables("VENTE_SUPPRIME_DETAILS").Rows(i).Item("CodeArticle") + _
                '                    "'," + _
                '                    dsChargement.Tables("VENTE_SUPPRIME_DETAILS").Rows(i).Item("Qte").ToString + ",'" + _
                '                    dsChargement.Tables("VENTE_SUPPRIME_DETAILS").Rows(i).Item("DateDePeremption") + _
                '                    "')"

                '        cmd.Connection = ConnectionServeur
                '        cmd.CommandText = StrMajLOT
                '        Try
                '            cmd.ExecuteNonQuery()
                '        Catch ex As Exception
                '            Console.WriteLine(ex.Message)
                '        End Try
                '    End If
                'Next


                '---------------- récuperation du NumeroReglementClient
                StrSQL = " SELECT NumeroReglementClient FROM REGLEMENT_CLIENT_VENTE  WHERE NumeroVente ='" + NumeroVente + "'"
                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL
                Try
                    NumeroReglementClient = cmd.ExecuteScalar()

                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try

                '--------------- suppression des details de la vente
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM VENTE_DETAILS WHERE NumeroVente ='" + NumeroVente + "'"
                    cmd.ExecuteNonQuery()

                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
                '--------------- suppression des REGLEMENTS de la vente
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM REGLEMENT_CLIENT_VENTE WHERE NumeroVente ='" + NumeroVente + "'"
                    cmd.ExecuteNonQuery()

                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
                '--------------- suppression de la vente
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM VENTE WHERE NumeroVente ='" + NumeroVente + "'"
                    cmd.ExecuteNonQuery()

                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
                ''--------------- suppression des REGLEMENTS CLIENT de la vente
                'Try
                '    cmd.Connection = ConnectionServeur
                '    cmd.CommandText = " DELETE FROM REGLEMENT_CLIENT WHERE NumeroReglementClient = '" & NumeroReglementClient & "'"

                '    cmd.ExecuteNonQuery()

                'Catch ex As Exception
                '    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                'End Try
                'Try
                '    cmd.Connection = ConnectionServeur
                '    cmd.CommandText = "INSERT INTO REGLEMENT_CLIENT " + _
                '                    "SELECT (SELECT MAX(NumeroReglementClient) FROM REGLEMENT_CLIENT) + 1 " + _
                '                    "      ,'Suppression vente' " + _
                '                    "      ,CodeNatureReglement " + _
                '                    "      ,GETDATE() " + _
                '                    "      ,GETDATE() " + _
                '                    "      ,MontantRegle * -1 " + _
                '                    "      ,Montant * -1 " + _
                '                    "      ,NumeroCheque " + _
                '                    "      ,LibellePoste " + _
                '                    "      ,NomInscritSurLeCheque " + _
                '                    "      ,CodeClient " + _
                '                    "      ,CodeBanque " + _
                '                    "      ,0 " + _
                '                    "      ,Encaisse " + _
                '                    "      ," & Quote(CodeOperateur) & " " + _
                '                    "FROM REGLEMENT_CLIENT " + _
                '                    "WHERE NumeroReglementClient = '" & NumeroReglementClient & "'"

                '    cmd.ExecuteNonQuery()

                'Catch ex As Exception
                '    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                'End Try
                '--------------- suppression des BON de la vente
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = " DELETE FROM BON WHERE Livre = 0 and NumeroVente = '" & NumeroVente & "'"

                    cmd.ExecuteNonQuery()

                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try

                '--------------- suppression des ordonnanciers
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = " UPDATE ORDONNANCIER SET Supprimer = 'True' WHERE NumeroVente = '" & NumeroVente & "'"

                    cmd.ExecuteNonQuery()

                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try

                If ImprimanteATicket = True Then
                    ImprimerTicket(NumeroVente)
                End If
                '*********************************

                'Ma nouvelle position

                If NumeroligneVente > 1 Then
                    NumeroligneVente = NumeroligneVente - 1
                ElseIf NumeroligneVente = 1 Then
                    initBoutons()
                End If
                'charger la nouvelle position
                ChargerVente(NumeroligneVente)

                '*********************************
                MsgBox("Vente supprimée !", MsgBoxStyle.Information, "Information")

                '*********************************
                ModuleSurveillance(13, "L'utilisateur " + dsChargement.Tables("VENTE_SUPPRIME").Rows(0).Item("NomPersonnelSupprime").ToString() + " a supprimé la vente N° " + dsChargement.Tables("VENTE_SUPPRIME").Rows(0).Item("NumeroVente").ToString())

            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " bSupprimer_Click", ex.Message, "0000311", "Erreur d'exécution de bSupprimer_Click ", True, True, True)

        End Try
    End Sub
    Public Function RecupereNumeroVenteSupprime()

        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer
        Try
            StrSQL = " SELECT max([NumeroVenteSupprime]) FROM [VENTE_SUPPRIME] WHERE SUBSTRING (NumeroVenteSupprime,0,5)=YEAR(getdate())"
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQL
            Try
                ValeurActuel = cmdRecupereNum.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            '----------------------- récupération du dernier numero sequenciel de l année en cours ou initialisation d un nouveau compteur  ----
            If ValeurActuel.Length > 4 Then
                If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                    Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                    Numero = Numero + 1
                    numeroConvertit = Numero.ToString
                    For i = 0 To 5 - numeroConvertit.Length
                        numeroConvertit = "0" + numeroConvertit
                    Next
                    ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
                Else
                    ValeurRetour = Year(Today).ToString + "/" + "000001"
                End If
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " RecupereNumeroVenteSupprime", ex.Message, "0000321", "Erreur d'exécution de RecupereNumeroVenteSupprime ", True, True, True)

        End Try
        Return ValeurRetour
    End Function


    Private Sub gListeRecherche_UnboundColumnFetch(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gListeRecherche.UnboundColumnFetch
        Dim y As String
        Try
            y = gListeRecherche(e.Row, ("CodeArticle"))
            e.Value = CalculeStock(y)
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " gListeRecherche_UnboundColumnFetch", ex.Message, "0000312", "Erreur d'exécution de gListeRecherche_UnboundColumnFetch ", True, True, True)

        End Try
    End Sub


    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        'Suivi du scénario
        fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", "bQuitter_Click", "NoException", "NoError", "Clic sur le bouton bQuitter", False, True, False)

        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Public Sub ImprimerTicket(ByVal NumeroVente As String)
        Try

            Dim CondCrystal As String = ""


            CondCrystal = " {VENTE_SUPPRIME.NumeroVente} = '" + NumeroVente + "'"
            CR.FileName = Application.StartupPath + "\EtatTicketFactureSupprime.rpt"
            If cmbClient.SelectedValue = "CLIPASS" Then
                CR.SetParameterValue("APayer", (TotalTTC.ToString("### ### ##0.000")))
            Else
                If TotalTTC - MontantMutuelle <= MonatantRecu And MonatantRecu <> 0 Then
                    CR.SetParameterValue("APayer", (Math.Round(TotalTTC - MontantMutuelle, 3).ToString("### ### ##0.000")))
                Else
                    CR.SetParameterValue("APayer", (MonatantRecu.ToString("### ### ##0.000")))
                End If

            End If
            'If cmbClient.SelectedValue = "CLIPASS" Then
            '    CR.SetParameterValue("SoldeClient", "0")
            'Else
            '    CR.SetParameterValue("SoldeClient", lNouvSolde.Text)
            'End If

            Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent
            CR.RecordSelectionFormula = CondCrystal
            Dim MyViewer As New fViewer
            MyViewer.CRViewer.ReportSource = CR

            Try
                CR.PrintOptions.PrinterName = NomDeLImprimante  '"EPSON LX-300+ /II"
                CR.PrintToPrinter(1, False, 1, 100)
            Catch ex As Exception
                MsgBox("Nom de l'imprimante ticket invalide !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            End Try

            'fermer Viewr
            MyViewer.Dispose()
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Vente", "fVente", " ImprimerTicket", ex.Message, "0000254", "Erreur d'exécution de ImprimerTicket ", True, True, True)

        End Try

    End Sub

    Public Sub ChargerVente(ByVal pNumeroLigneVent As String)

        'Declaration 
        Dim StrSQL As String = ""
        Dim DataRowAffichageEntete As DataRow
        Try

            'Préparation des DS vides

            If (dsVente.Tables.IndexOf("VENTE_DETAILS") > -1) Then
                dsVente.Tables("VENTE_DETAILS").Clear()
            End If


            If (dsVente.Tables.IndexOf("VENTE") > -1) Then
                dsVente.Tables("VENTE").Clear()
            End If

            'chargement des Entêtes des ventes 

            StrSQL = " SELECT * FROM (  " + _
         " SELECT *, ROW_NUMBER() OVER (ORDER BY NumeroVente) as row FROM VENTE " + _
         " ) a WHERE row > " & pNumeroLigneVent - 1 & " AND  row <= " & pNumeroLigneVent + ""

            cmdVenteEntete.Connection = ConnectionServeur
            cmdVenteEntete.CommandText = StrSQL
            daVenteEntete = New SqlDataAdapter(cmdVenteEntete)
            daVenteEntete.Fill(dsVente, "VENTE")
            cbVenteEntete = New SqlCommandBuilder(daVenteEntete)


            'Lire le numéro Vente
            If dsVente.Tables("VENTE").Rows.Count > 0 Then

                NumeroVente = dsVente.Tables("VENTE").Rows(0).Item("NumeroVente")

            Else

                NumeroVente = "0"

            End If

            initBoutons()

            'chargement des détails des ventes 



            StrSQL = "SELECT NumeroVente," + _
                     "CodeArticle," + _
                     "CodeABarre," + _
                     "Designation," + _
                     "'' AS LibelleForme," + _
                     "Qte," + _
                     "Stock," + _
                     "DateDePeremption, " + _
                     "PrixTTC," + _
                     "TotalTTC," + _
                     "TVA," + _
                     "Remise," + _
                     "PrixHT," + _
                     "TotalHT," + _
                     "TotalTVA," + _
                     "PrixAchat," + _
                     "Honoraire," + _
                     "VENTE_DETAILS.CodeForme," + _
                     "NumeroLotArticle," + _
                     "PriseEnCharge," + _
                     "AccordPrealable," + _
                     "TarifDeReference, " + _
                          "'' AS Vide " + _
                     "FROM VENTE_DETAILS " + _
                       "WHERE " + _
                         "NumeroVente =" + Quote(NumeroVente) + " ORDER BY NumeroVente"

            cmdVenteDetails.Connection = ConnectionServeur
            cmdVenteDetails.CommandText = StrSQL
            daVenteDetails = New SqlDataAdapter(cmdVenteDetails)
            daVenteDetails.Fill(dsVente, "VENTE_DETAILS")
            cbVenteDetails = New SqlCommandBuilder(daVenteDetails)



            'initialisation de gArticles
            initgArticles()

            'chargement des clients
            initClient()

            If dsVente.Tables("VENTE").Rows.Count > 0 Then

                DataRowAffichageEntete = dsVente.Tables("VENTE").Select("NumeroVente=" + Quote(NumeroVente))(0)

                cmbMutuelle.SelectedValue = dsVente.Tables("VENTE").Rows(0)("CodeMutuelle")

                lNumeroVente.Text = dsVente.Tables("VENTE").Rows(0)("NumeroVente")
                lDateFacture.Text = dsVente.Tables("VENTE").Rows(0)("Date")
                cmbClient.Text = RecupererValeurExecuteScalaire("Nom", "CLIENT", "CodeClient", dsVente.Tables("VENTE").Rows(0)("CodeClient"))
                lNumerofacture.Text = dsVente.Tables("VENTE").Rows(0)("NumeroFacture").ToString

                TotalTTC = dsVente.Tables("VENTE").Rows(0)("TotalTTC")
                TotalHT = dsVente.Tables("VENTE").Rows(0)("TotalHT")
                MontantCNAM = dsVente.Tables("VENTE").Rows(0)("MontantCnam")
                MontantMutuelle = dsVente.Tables("VENTE").Rows(0)("MontantMutuelle")

                TVA = dsVente.Tables("VENTE").Rows(0)("TVA")
                lRemise.Text = dsVente.Tables("VENTE").Rows(0)("TotalRemise")
                lTotalTTC.Text = Math.Round(TotalTTC, 3)
                lMontantCNAM.Text = Math.Round(dsVente.Tables("VENTE").Rows(0)("MontantCnam"), 3)
                lMontantMutuelle.Text = Math.Round(dsVente.Tables("VENTE").Rows(0)("MontantMutuelle"), 3)
                lRecu.Text = Math.Round(dsVente.Tables("VENTE").Rows(0)("Recu"), 3)

                lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", dsVente.Tables("VENTE").Rows(0)("CodePersonnel"))


                cmbMedecin.SelectedValue = dsVente.Tables("VENTE").Rows(0)("CodeMedecinPrescripteur")
                dtpDateOrdonnance.Value = dsVente.Tables("VENTE").Rows(0)("DateOrdonnance")

                lModePaiement.Text = RecupererValeurExecuteScalaire("LibelleNatureReglement", "NATURE_REGLEMENT", "CodeNatureReglement", dsVente.Tables("VENTE").Rows(0)("CodeNatureReglement"))

                If dsVente.Tables("VENTE").Rows(0)("TiersPayant") = True Then
                    rdbTiersPayant.Checked = True
                ElseIf dsVente.Tables("VENTE").Rows(0)("PriseEnCharge") = True Then
                    rdbPriseEnCharge.Checked = True
                ElseIf dsVente.Tables("VENTE").Rows(0)("Appareillage") = True Then
                    rdbAppareillage.Checked = True
                End If
                tCodeMedecin.Value = dsVente.Tables("VENTE").Rows(0)("IdentifiantCNAMMedecin")

                NumeroVente = DataRowAffichageEntete.Item("NumeroVente")

                'pour afficher les informations liées au vente
                initafficheInformationVente()

                'pour bloquer le saisie

                For i = 0 To gArticles.Columns.Count - 1
                    gArticles.Splits(0).DisplayColumns(i).Locked = True

                Next

                'pour rendre invisible les champs suivants si le mode est consultation

                lNetAPayerAfficher.Visible = False
                tNetAPayer.Visible = False
                tRemisePourcentage.Visible = False
                lPourcent.Visible = False
                lRemisePourcentAfficher.Visible = False

            End If


            'pour afficher la situation des clients et ces valeurs
            AfficherSituationClient()

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " ChargerVente", ex.Message, "0000313", "Erreur d'exécution de ChargerVente ", True, True, True)

        End Try
    End Sub
    Public Sub AfficherSituationClient()
        Try


            Dim StrSQLdernierAchat As String = ""
            Dim Dernier_Date_Achat As String = ""
            Dim Dernier_Date_Reglement As String = ""
            Dim StrSQLSolde As String = ""
            Dim Somme_Facture As Decimal = 0.0
            Dim Somme_Reglement As Decimal = 0.0
            Dim Somme_Echeance As Decimal = 0.0
            Dim difference As Double = 0.0
            Dim SoldeInitial As Decimal = 0.0

            Dim CmdCalcul As New SqlCommand

            If cmbClient.SelectedValue <> Nothing Then

                ' récupération de la dernière date d'achat pour le client concerné 
                StrSQLdernierAchat = "SELECT MAX(Date) FROM VENTE WHERE CodeClient =" + Quote(cmbClient.SelectedValue)
                CmdCalcul.Connection = ConnectionServeur
                CmdCalcul.CommandText = StrSQLdernierAchat

                Try
                    Dernier_Date_Achat = CmdCalcul.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                ' récupération de la dernière date de règlement pour le client concerné 
                StrSQLdernierAchat = "SELECT MAX(Date) FROM REGLEMENT_CLIENT WHERE CodeClient =" + Quote(cmbClient.SelectedValue)
                CmdCalcul.Connection = ConnectionServeur
                CmdCalcul.CommandText = StrSQLdernierAchat

                Try
                    Dernier_Date_Reglement = CmdCalcul.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                'calcul du solde client en retranchant la somme des montants des règlements de la somme des montants des ventes 
                StrSQLSolde = "SELECT SUM(TotalTTC-MontantCnam-MontantMutuelle) FROM VENTE LEFT OUTER JOIN CLIENT ON VENTE.CodeClient=CLIENT.CodeClient WHERE VENTE.Date>Client.DateInitial AND VENTE.CodeClient =" + Quote(cmbClient.SelectedValue)
                CmdCalcul.Connection = ConnectionServeur
                CmdCalcul.CommandText = StrSQLSolde

                Try
                    Somme_Facture = CmdCalcul.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                StrSQLSolde = "SELECT SUM(Montant) FROM REGLEMENT_CLIENT LEFT OUTER JOIN CLIENT ON REGLEMENT_CLIENT.CodeClient=CLIENT.CodeClient WHERE REGLEMENT_CLIENT.Date>Client.DateInitial AND Encaisse=1 AND  REGLEMENT_CLIENT.CodeClient =" + Quote(cmbClient.SelectedValue)
                CmdCalcul.Connection = ConnectionServeur
                CmdCalcul.CommandText = StrSQLSolde

                Try
                    Somme_Reglement = CmdCalcul.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                ' récupération des montants des règlement antidaté nn encaissé
                StrSQLSolde = "SELECT SUM(Montant) FROM REGLEMENT_CLIENT WHERE " + _
                "(CodeNatureReglement = 2 OR CodeNatureReglement = 5 ) AND Encaisse=0  AND  CodeClient =" + Quote(cmbClient.SelectedValue)
                CmdCalcul.Connection = ConnectionServeur
                CmdCalcul.CommandText = StrSQLSolde

                Try
                    Somme_Echeance = CmdCalcul.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                Try
                    SoldeInitial = RecupererValeurExecuteScalaire("SoldeInitial", "CLIENT", "CodeClient", cmbClient.SelectedValue)
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                '------------------------------------------------------------------------------
                '-------------------------------- afféctation des valeurs ---------------------

                If (Dernier_Date_Achat) <> "" Then
                    lDDA.Text = Dernier_Date_Achat
                Else
                    lDDA.Text = "-"
                End If

                If (Dernier_Date_Reglement) <> "" Then
                    lDDR.Text = Dernier_Date_Reglement
                Else
                    lDDR.Text = "-"
                End If

                difference = (Somme_Facture - Somme_Reglement + SoldeInitial) '.ToString("### ### ##0.000")
                lSoldeClient.Text = difference.ToString("### ### ##0.000")
                lNouvSolde.Text = (Val(lSoldeClient.Text) + Val(lTotalTTC.Text)).ToString("### ### ##0.000")
                lSoldeEnCours.Text = Somme_Echeance.ToString("### ### ##0.000")

                If cmbClient.SelectedValue = "CLIPASS" Or cmbClient.Text = "" Then
                    GroupeSituationClient.Visible = False
                Else
                    GroupeSituationClient.Visible = True
                End If

            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " AfficherSituationClient", ex.Message, "0000314", "Erreur d'exécution de AfficherSituationClient ", True, True, True)

        End Try
    End Sub

    Private Sub initBoutons()
        Try
            'Tester si on atteint la premiere ligne
            'pour desactiver les BTN Précedent et Premier élément        
            If NumeroligneVente = 1 Then

                bPrevious.Enabled = False
                bFirst.Enabled = False

            Else

                bPrevious.Enabled = True
                bFirst.Enabled = True

            End If

            'Tester si on atteint la derniere ligne
            'pour desactiver les BTN Siuvant et Dernier élément
            If NumeroligneVente = selectionDernierLigneVente() Then

                bNext.Enabled = False
                bLast.Enabled = False

            Else

                bNext.Enabled = True
                bLast.Enabled = True

            End If

            'Le cas ou la table est vide
            If NumeroligneVente = 0 Then

                bNext.Enabled = False
                bLast.Enabled = False
                bPrevious.Enabled = False
                bFirst.Enabled = False

            End If

            'Tester si la table est vide
            'pour desactiver les BTN Siuvant et Dernier élément
            If selectionDernierLigneVente() = 0 Then

                'Bloque navigation
                bNext.Enabled = False
                bLast.Enabled = False
                bNext.Enabled = False
                bLast.Enabled = False




            End If   ' le cas on a ajouté un element

            'le mode en Cosultation et on a des enregistrements
            If selectionDernierLigneVente() <> 0 Then




                bFirst.Visible = True
                bPrevious.Visible = True
                bNext.Visible = True
                bLast.Visible = True


                'le mode est modif/Ajout et on a des enregistrements
            ElseIf selectionDernierLigneVente() <> 0 Then



                'Rendre Invisible le menu de navigation
                bNext.Visible = False
                bLast.Visible = False
                bPrevious.Visible = False
                bFirst.Visible = False


                'le mode en Cosultation et on  a pas des enregistrements
            ElseIf selectionDernierLigneVente() = 0 And mode = "Consultation" Then

                bQuitter.Enabled = True

                bFirst.Visible = True
                bPrevious.Visible = True
                bNext.Visible = True
                bLast.Visible = True

            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " initBoutons", ex.Message, "0000315", "Erreur d'exécution de initBoutons ", True, True, True)

        End Try
    End Sub
    Private Sub initgArticles()

        'Pour initialiser la grid des articles
        Try

            With gArticles
                .Columns.Clear()
                .DataSource = dsVente
                .DataMember = "VENTE_DETAILS"
                .Rebind(False)
                .Columns("CodeABarre").Caption = "Code article"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Qte").Caption = "Qte"
                .Columns("Stock").Caption = "Stock"
                .Columns("PrixHT").Caption = "Prix HT"
                .Columns("DateDePeremption").Caption = "Date de péremption"
                .Columns("PrixTTC").Caption = "Prix TTC "
                .Columns("TotalTTC").Caption = "Total TTC "
                .Columns("TVA").Caption = "TVA"
                .Columns("Remise").Caption = "Remise"
                'colonne vide
                .Columns("Vide").Caption = ""

                'Colonne non liée : LibelleForme
                .Columns("LibelleForme").DataField = ""

                ' Centrer toutes les entêtes
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Locked = True
                Next

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("PrixTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("Remise").Style.HorizontalAlignment = AlignHorzEnum.Far

                .Splits(0).DisplayColumns("CodeABarre").Locked = False
                .Splits(0).DisplayColumns("Designation").Locked = False
                .Splits(0).DisplayColumns("Qte").Locked = False
                .Splits(0).DisplayColumns("Remise").Locked = False

                .Splits(0).DisplayColumns("NumeroVente").Width = 0
                .Splits(0).DisplayColumns("NumeroVente").Visible = False
                .Splits(0).DisplayColumns("NumeroVente").AllowSizing = False
                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

                If Screen.PrimaryScreen.Bounds.Width < 1350 Then
                    .Splits(0).DisplayColumns("CodeABarre").Width = 170 '140
                    .Splits(0).DisplayColumns("NumeroLotArticle").Width = 0
                    .Splits(0).DisplayColumns("Designation").Width = 430 '400
                    '.Splits(0).DisplayColumns("LibelleForme").Width = 100
                    .Splits(0).DisplayColumns("Qte").Width = 60
                    .Splits(0).DisplayColumns("Stock").Width = 60
                    .Splits(0).DisplayColumns("DateDePeremption").Width = 150
                    .Splits(0).DisplayColumns("PrixTTC").Width = 80
                    .Splits(0).DisplayColumns("TotalTTC").Width = 80
                    .Splits(0).DisplayColumns("TVA").Width = 80
                    .Splits(0).DisplayColumns("Remise").Width = 80
                Else
                    .Splits(0).DisplayColumns("CodeABarre").Width = 170 '140
                    .Splits(0).DisplayColumns("NumeroLotArticle").Width = 0
                    .Splits(0).DisplayColumns("Designation").Width = Screen.PrimaryScreen.Bounds.Width - 920
                    '.Splits(0).DisplayColumns("LibelleForme").Width = 100
                    .Splits(0).DisplayColumns("Qte").Width = 60
                    .Splits(0).DisplayColumns("Stock").Width = 60
                    .Splits(0).DisplayColumns("DateDePeremption").Width = 150
                    .Splits(0).DisplayColumns("PrixTTC").Width = 80
                    .Splits(0).DisplayColumns("TotalTTC").Width = 80
                    .Splits(0).DisplayColumns("TVA").Width = 80
                    .Splits(0).DisplayColumns("Remise").Width = 80
                End If

              

                'colonne vide ajouter
                .Splits(0).DisplayColumns("Vide").Width = 50
                .Splits(0).DisplayColumns("Vide").Visible = False

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("NumeroVente").Visible = False
                .Splits(0).DisplayColumns("NumeroLotArticle").Width = 0
                .Splits(0).DisplayColumns("NumeroLotArticle").Visible = False
                .Splits(0).DisplayColumns("PrixHT").Width = 0
                .Splits(0).DisplayColumns("PrixHT").Visible = False
                .Splits(0).DisplayColumns("TotalHT").Width = 0
                .Splits(0).DisplayColumns("TotalHT").Visible = False
                .Splits(0).DisplayColumns("TotalTVA").Width = 0
                .Splits(0).DisplayColumns("TotalTVA").Visible = False
                .Splits(0).DisplayColumns("PrixAchat").Width = 0
                .Splits(0).DisplayColumns("PrixAchat").Visible = False
                .Splits(0).DisplayColumns("Honoraire").Width = 0
                .Splits(0).DisplayColumns("Honoraire").Visible = False
                .Splits(0).DisplayColumns("CodeForme").Width = 0
                .Splits(0).DisplayColumns("CodeForme").Visible = False
                .Splits(0).DisplayColumns("PriseEnCharge").Visible = False
                .Splits(0).DisplayColumns("AccordPrealable").Visible = False
                .Splits(0).DisplayColumns("TarifDeReference").Visible = False

                .Splits(0).DisplayColumns("CodeArticle").Style.BackColor = Color.MistyRose
                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.MistyRose
                .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.MistyRose
                .Splits(0).DisplayColumns("Remise").Style.BackColor = Color.MistyRose

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                'Style du Caractere et du grid
                ParametreGrid(gArticles)
            End With
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " initgArticles", ex.Message, "0000316", "Erreur d'exécution de initgArticles ", True, True, True)

        End Try

    End Sub


    Private Sub initClient()

        Dim StrSQL As String

        'Rechargement des Clients
        Try

            'vider DS
            If (dsVente.Tables.IndexOf("CLIENT") > -1) Then
                dsVente.Tables("CLIENT").Clear()
            End If


            StrSQL = "SELECT CodeClient,Nom FROM CLIENT WHERE Supprime=0 ORDER BY Nom ASC"
            cmdChargement.Connection = ConnectionServeur
            cmdChargement.CommandText = StrSQL
            daChargement = New SqlDataAdapter(cmdChargement)
            daChargement.Fill(dsVente, "CLIENT")
            cmbClient.DataSource = dsVente.Tables("CLIENT")
            cmbClient.ValueMember = "CodeClient"
            cmbClient.DisplayMember = "Nom"
            cmbClient.ColumnHeaders = False
            cmbClient.Splits(0).DisplayColumns("CodeClient").Width = 0
            cmbClient.Splits(0).DisplayColumns("Nom").Width = 160
            cmbClient.Splits(0).ExtendRightColumn = True
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " initClient", ex.Message, "0000317", "Erreur d'exécution de initClient ", True, True, True)

        End Try

    End Sub
    'pour afficher les informations liées au vente
    Private Sub initafficheInformationVente()
        Try
            If lMontantCNAM.Text <> "0.000" Then
                chbCnam1.Checked = True
                chbCnam1.Visible = True
                GroupeMedecin.Visible = True
            Else
                chbCnam1.Visible = False
                chbCnam1.Checked = False
                GroupeMedecin.Visible = False
            End If

            If lRemise.Text = "0.000" Then
                lRemise.Visible = False
                lRemiseAfficher.Visible = False
            Else
                lRemise.Visible = True
                lRemiseAfficher.Visible = True
            End If
            GroupeSituationClient.Visible = False

            If cmbClient.SelectedValue = "CLIPASS" Or (lMontantCNAM.Text = "0.000" And lMontantMutuelle.Text = "0.000") Then
                GroupeCnamMutuelle.Visible = False
                GroupeSituationClient.Visible = False
            Else
                GroupeSituationClient.Visible = True

                If chbCnam1.Checked = True Or Trim(cmbMutuelle.Text.ToUpper) <> "COMPTOIR" Then
                    GroupeCnamMutuelle.Visible = True
                Else
                    GroupeCnamMutuelle.Visible = False
                End If
            End If

            tMatricule.Visible = False
            lMatricule.Visible = False

            GroupeCNAM.Enabled = False
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " initafficheInformationVente", ex.Message, "0000318", "Erreur d'exécution de initafficheInformationVente ", True, True, True)

        End Try
    End Sub

    Private Function selectionDernierLigneVente()

        Dim StrSQL As String

        Try

            'Affécter le nombre de ligne au variable global  NumeroligneVente
            StrSQL = " SELECT COUNT(*) FROM VENTE "

            cmdVente.Connection = ConnectionServeur
            cmdVente.CommandText = StrSQL

            selectionDernierLigneVente = cmdVente.ExecuteScalar()

            Return selectionDernierLigneVente

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " selectionDernierLigneVente", ex.Message, "0000319", "Erreur d'exécution de selectionDernierLigneVente ", True, True, True)
            Return 0
        End Try

    End Function

    Private Sub gArticles_UnboundColumnFetch(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gArticles.UnboundColumnFetch

        'Récuperer la valeur Désignation FORME ARTICLE 

        Dim StrSQL As String = ""

        Try

            StrSQL = " SELECT LibelleForme FROM FORME_ARTICLE AS F JOIN VENTE_DETAILS AS A ON  " + _
                     " F.CodeForme = A.CodeForme" + _
                     " WHERE CodeArticle = " + Quote(gArticles(e.Row, "CodeArticle"))


            cmdVente.Connection = ConnectionServeur
            cmdVente.CommandText = StrSQL

            e.Value = cmdVente.ExecuteScalar()

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " gArticles_UnboundColumnFetch", ex.Message, "0000320", "Erreur d'exécution de gArticles_UnboundColumnFetch ", True, True, True)

        End Try

    End Sub
    Private Sub lTotalTTC_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lTotalTTC.TextChanged
        Try


            lTotalTTC.Text = lTotalTTC.Text
            If lTotalTTC.Text.Contains(".") = True Then
                Dim x As Integer
                x = InStr(lTotalTTC.Text, ".")
                If lTotalTTC.Text.Length - x = 1 Then
                    lTotalTTC.Text = lTotalTTC.Text + ("00")
                ElseIf lTotalTTC.Text.Length - x = 2 Then
                    lTotalTTC.Text = lTotalTTC.Text + ("0")
                End If
            Else
                lTotalTTC.Text = lTotalTTC.Text + ".000"
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Suppression Des Ventes", "fSuppressionDesVentes", " lTotalTTC_TextChanged", ex.Message, "0000322", "Erreur d'exécution de lTotalTTC_TextChanged ", True, True, True)

        End Try
    End Sub

End Class