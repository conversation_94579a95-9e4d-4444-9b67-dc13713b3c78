﻿#pragma checksum "..\..\..\MainWindowComplete.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "38C7DBC373AC8AC7CAFAA7803F137D08C9CBC605"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PharmaModerne.UI {
    
    
    /// <summary>
    /// MainWindowComplete
    /// </summary>
    public partial class MainWindowComplete : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 208 "..\..\..\MainWindowComplete.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ScannerIndicator;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\MainWindowComplete.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserInfo;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\MainWindowComplete.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimeInfo;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\MainWindowComplete.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ModuleTitle;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\MainWindowComplete.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox GlobalSearchBox;
        
        #line default
        #line hidden
        
        
        #line 301 "..\..\..\MainWindowComplete.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ScannerToggle;
        
        #line default
        #line hidden
        
        
        #line 342 "..\..\..\MainWindowComplete.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ModuleContent;
        
        #line default
        #line hidden
        
        
        #line 348 "..\..\..\MainWindowComplete.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DefaultDashboard;
        
        #line default
        #line hidden
        
        
        #line 498 "..\..\..\MainWindowComplete.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusMessage;
        
        #line default
        #line hidden
        
        
        #line 507 "..\..\..\MainWindowComplete.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ModuleStatus;
        
        #line default
        #line hidden
        
        
        #line 519 "..\..\..\MainWindowComplete.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentTimeStatus;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PharmaModerne.UI;component/mainwindowcomplete.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindowComplete.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 51 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 65 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 70 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 75 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 88 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 93 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 98 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 111 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 116 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 121 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 126 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 139 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 144 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 149 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 162 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 167 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 172 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 185 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 190 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 195 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToModule);
            
            #line default
            #line hidden
            return;
            case 21:
            this.ScannerIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 22:
            this.UserInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.TimeInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.ModuleTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.GlobalSearchBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 26:
            this.ScannerToggle = ((System.Windows.Controls.Button)(target));
            
            #line 303 "..\..\..\MainWindowComplete.xaml"
            this.ScannerToggle.Click += new System.Windows.RoutedEventHandler(this.ToggleScanner);
            
            #line default
            #line hidden
            return;
            case 27:
            
            #line 312 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowNotifications);
            
            #line default
            #line hidden
            return;
            case 28:
            
            #line 321 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowSettings);
            
            #line default
            #line hidden
            return;
            case 29:
            
            #line 330 "..\..\..\MainWindowComplete.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Logout);
            
            #line default
            #line hidden
            return;
            case 30:
            this.ModuleContent = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 31:
            this.DefaultDashboard = ((System.Windows.Controls.Grid)(target));
            return;
            case 32:
            this.StatusMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.ModuleStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.CurrentTimeStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

