﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fBanque
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fBanque))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.gBanque = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.cSupprimerBanque = New C1.Win.C1Input.C1Button()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.lTest = New System.Windows.Forms.Label()
        Me.tAdresseBanque = New C1.Win.C1Input.C1TextBox()
        Me.tFaxBanque = New C1.Win.C1Input.C1TextBox()
        Me.tTelBanque = New C1.Win.C1Input.C1TextBox()
        Me.bAjouterBanque = New C1.Win.C1Input.C1Button()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.tNomBanque = New C1.Win.C1Input.C1TextBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.tCodeBanque = New C1.Win.C1Input.C1TextBox()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Panel.SuspendLayout()
        CType(Me.gBanque, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox2.SuspendLayout()
        CType(Me.tAdresseBanque, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tFaxBanque, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tTelBanque, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNomBanque, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodeBanque, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.Label1)
        Me.Panel.Controls.Add(Me.gBanque)
        Me.Panel.Controls.Add(Me.cSupprimerBanque)
        Me.Panel.Controls.Add(Me.GroupBox2)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1028, 566)
        Me.Panel.TabIndex = 0
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(911, 9)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(105, 45)
        Me.bQuitter.TabIndex = 85
        Me.bQuitter.Text = "Fermer             F12   "
        Me.bQuitter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("HandelGotDLig", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(13, 11)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(897, 40)
        Me.Label1.TabIndex = 84
        Me.Label1.Text = "LISTE DES BANQUES"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'gBanque
        '
        Me.gBanque.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gBanque.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gBanque.GroupByCaption = "Drag a column header here to group by that column"
        Me.gBanque.Images.Add(CType(resources.GetObject("gBanque.Images"), System.Drawing.Image))
        Me.gBanque.Location = New System.Drawing.Point(13, 62)
        Me.gBanque.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gBanque.Name = "gBanque"
        Me.gBanque.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gBanque.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gBanque.PreviewInfo.ZoomFactor = 75.0R
        Me.gBanque.PrintInfo.PageSettings = CType(resources.GetObject("gBanque.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gBanque.Size = New System.Drawing.Size(1003, 361)
        Me.gBanque.TabIndex = 83
        Me.gBanque.Text = "C1TrueDBGrid4"
        Me.gBanque.PropBag = resources.GetString("gBanque.PropBag")
        '
        'cSupprimerBanque
        '
        Me.cSupprimerBanque.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cSupprimerBanque.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cSupprimerBanque.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler21
        Me.cSupprimerBanque.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.cSupprimerBanque.Location = New System.Drawing.Point(920, 472)
        Me.cSupprimerBanque.Name = "cSupprimerBanque"
        Me.cSupprimerBanque.Size = New System.Drawing.Size(100, 45)
        Me.cSupprimerBanque.TabIndex = 2
        Me.cSupprimerBanque.Text = "Supprimer"
        Me.cSupprimerBanque.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cSupprimerBanque.UseVisualStyleBackColor = True
        Me.cSupprimerBanque.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox2
        '
        Me.GroupBox2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox2.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox2.Controls.Add(Me.lTest)
        Me.GroupBox2.Controls.Add(Me.tAdresseBanque)
        Me.GroupBox2.Controls.Add(Me.tFaxBanque)
        Me.GroupBox2.Controls.Add(Me.tTelBanque)
        Me.GroupBox2.Controls.Add(Me.bAjouterBanque)
        Me.GroupBox2.Controls.Add(Me.Label8)
        Me.GroupBox2.Controls.Add(Me.Label7)
        Me.GroupBox2.Controls.Add(Me.Label6)
        Me.GroupBox2.Controls.Add(Me.tNomBanque)
        Me.GroupBox2.Controls.Add(Me.Label4)
        Me.GroupBox2.Controls.Add(Me.tCodeBanque)
        Me.GroupBox2.Controls.Add(Me.Label5)
        Me.GroupBox2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox2.Location = New System.Drawing.Point(12, 429)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(901, 125)
        Me.GroupBox2.TabIndex = 1
        Me.GroupBox2.TabStop = False
        '
        'lTest
        '
        Me.lTest.AutoSize = True
        Me.lTest.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTest.Location = New System.Drawing.Point(139, 46)
        Me.lTest.Name = "lTest"
        Me.lTest.Size = New System.Drawing.Size(10, 13)
        Me.lTest.TabIndex = 84
        Me.lTest.Text = "-"
        '
        'tAdresseBanque
        '
        Me.tAdresseBanque.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tAdresseBanque.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tAdresseBanque.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tAdresseBanque.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tAdresseBanque.Location = New System.Drawing.Point(388, 56)
        Me.tAdresseBanque.Multiline = True
        Me.tAdresseBanque.Name = "tAdresseBanque"
        Me.tAdresseBanque.Size = New System.Drawing.Size(291, 53)
        Me.tAdresseBanque.TabIndex = 4
        Me.tAdresseBanque.Tag = Nothing
        Me.tAdresseBanque.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tAdresseBanque.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tFaxBanque
        '
        Me.tFaxBanque.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tFaxBanque.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tFaxBanque.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tFaxBanque.Location = New System.Drawing.Point(388, 20)
        Me.tFaxBanque.Name = "tFaxBanque"
        Me.tFaxBanque.Size = New System.Drawing.Size(165, 19)
        Me.tFaxBanque.TabIndex = 3
        Me.tFaxBanque.Tag = Nothing
        Me.tFaxBanque.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tFaxBanque.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tTelBanque
        '
        Me.tTelBanque.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tTelBanque.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTelBanque.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tTelBanque.Location = New System.Drawing.Point(142, 90)
        Me.tTelBanque.Name = "tTelBanque"
        Me.tTelBanque.Size = New System.Drawing.Size(165, 19)
        Me.tTelBanque.TabIndex = 2
        Me.tTelBanque.Tag = Nothing
        Me.tTelBanque.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tTelBanque.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouterBanque
        '
        Me.bAjouterBanque.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouterBanque.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouterBanque.Image = Global.Pharma2000Premium.My.Resources.Resources.nouveau1
        Me.bAjouterBanque.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouterBanque.Location = New System.Drawing.Point(794, 43)
        Me.bAjouterBanque.Name = "bAjouterBanque"
        Me.bAjouterBanque.Size = New System.Drawing.Size(100, 45)
        Me.bAjouterBanque.TabIndex = 5
        Me.bAjouterBanque.Text = "Ajouter"
        Me.bAjouterBanque.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouterBanque.UseVisualStyleBackColor = True
        Me.bAjouterBanque.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.BackColor = System.Drawing.Color.Transparent
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.Location = New System.Drawing.Point(337, 59)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(45, 13)
        Me.Label8.TabIndex = 15
        Me.Label8.Text = "Adresse"
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.BackColor = System.Drawing.Color.Transparent
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.Location = New System.Drawing.Point(358, 22)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(24, 13)
        Me.Label7.TabIndex = 14
        Me.Label7.Text = "Fax"
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.BackColor = System.Drawing.Color.Transparent
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.Location = New System.Drawing.Point(111, 93)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(25, 13)
        Me.Label6.TabIndex = 13
        Me.Label6.Text = "Tel "
        '
        'tNomBanque
        '
        Me.tNomBanque.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNomBanque.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNomBanque.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNomBanque.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNomBanque.Location = New System.Drawing.Point(142, 65)
        Me.tNomBanque.Name = "tNomBanque"
        Me.tNomBanque.Size = New System.Drawing.Size(165, 19)
        Me.tNomBanque.TabIndex = 1
        Me.tNomBanque.Tag = Nothing
        Me.tNomBanque.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNomBanque.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.BackColor = System.Drawing.Color.Transparent
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.Location = New System.Drawing.Point(41, 68)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(95, 13)
        Me.Label4.TabIndex = 12
        Me.Label4.Text = "Nom de la Banque"
        '
        'tCodeBanque
        '
        Me.tCodeBanque.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodeBanque.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodeBanque.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodeBanque.Location = New System.Drawing.Point(142, 20)
        Me.tCodeBanque.Name = "tCodeBanque"
        Me.tCodeBanque.Size = New System.Drawing.Size(165, 19)
        Me.tCodeBanque.TabIndex = 0
        Me.tCodeBanque.Tag = Nothing
        Me.tCodeBanque.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodeBanque.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.BackColor = System.Drawing.Color.Transparent
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.Location = New System.Drawing.Point(38, 23)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(98, 13)
        Me.Label5.TabIndex = 8
        Me.Label5.Text = "Code de la Banque"
        '
        'fBanque
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1028, 566)
        Me.Controls.Add(Me.Panel)
        Me.Font = New System.Drawing.Font("Calibri", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Name = "fBanque"
        Me.Text = "fBanque"
        Me.Panel.ResumeLayout(False)
        CType(Me.gBanque, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.tAdresseBanque, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tFaxBanque, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tTelBanque, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNomBanque, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodeBanque, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents cSupprimerBanque As C1.Win.C1Input.C1Button
    Friend WithEvents tAdresseBanque As C1.Win.C1Input.C1TextBox
    Friend WithEvents tFaxBanque As C1.Win.C1Input.C1TextBox
    Friend WithEvents tTelBanque As C1.Win.C1Input.C1TextBox
    Friend WithEvents bAjouterBanque As C1.Win.C1Input.C1Button
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents tNomBanque As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents tCodeBanque As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents lTest As System.Windows.Forms.Label
    Friend WithEvents gBanque As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents Label1 As System.Windows.Forms.Label
End Class
