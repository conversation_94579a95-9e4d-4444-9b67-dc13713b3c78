<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://services.resip.fr/" targetNamespace="http://services.resip.fr/" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="addEtablissement" type="tns:addEtablissement" />
  <xs:element name="addEtablissementResponse" type="tns:addEtablissementResponse" />
  <xs:element name="addUser" type="tns:addUser" />
  <xs:element name="addUserResponse" type="tns:addUserResponse" />
  <xs:element name="checkLogin" type="tns:checkLogin" />
  <xs:element name="checkLoginResponse" type="tns:checkLoginResponse" />
  <xs:element name="controleOrdonnance" type="tns:controleOrdonnance" />
  <xs:element name="controleOrdonnanceParam" type="tns:controleOrdonnanceParam" />
  <xs:element name="controleOrdonnanceParamResponse" type="tns:controleOrdonnanceParamResponse" />
  <xs:element name="controleOrdonnanceResponse" type="tns:controleOrdonnanceResponse" />
  <xs:element name="deleteAllUsersEtablissement" type="tns:deleteAllUsersEtablissement" />
  <xs:element name="deleteAllUsersEtablissementResponse" type="tns:deleteAllUsersEtablissementResponse" />
  <xs:element name="deleteEtablissement" type="tns:deleteEtablissement" />
  <xs:element name="deleteEtablissementResponse" type="tns:deleteEtablissementResponse" />
  <xs:element name="deleteUser" type="tns:deleteUser" />
  <xs:element name="deleteUserResponse" type="tns:deleteUserResponse" />
  <xs:element name="getEffetsIndesirablesProduit" type="tns:getEffetsIndesirablesProduit" />
  <xs:element name="getEffetsIndesirablesProduitResponse" type="tns:getEffetsIndesirablesProduitResponse" />
  <xs:element name="getEtablissement" type="tns:getEtablissement" />
  <xs:element name="getEtablissementResponse" type="tns:getEtablissementResponse" />
  <xs:element name="getInformationProduit" type="tns:getInformationProduit" />
  <xs:element name="getInformationProduitEtr" type="tns:getInformationProduitEtr" />
  <xs:element name="getInformationProduitEtrResponse" type="tns:getInformationProduitEtrResponse" />
  <xs:element name="getInformationProduitResponse" type="tns:getInformationProduitResponse" />
  <xs:element name="getInformationProduit_befr" type="tns:getInformationProduit_befr" />
  <xs:element name="getInformationProduit_befrResponse" type="tns:getInformationProduit_befrResponse" />
  <xs:element name="getInformationProduit_ma" type="tns:getInformationProduit_ma" />
  <xs:element name="getInformationProduit_maResponse" type="tns:getInformationProduit_maResponse" />
  <xs:element name="getInformationProduit_ro" type="tns:getInformationProduit_ro" />
  <xs:element name="getInformationProduit_roResponse" type="tns:getInformationProduit_roResponse" />
  <xs:element name="getInformationProduit_tn" type="tns:getInformationProduit_tn" />
  <xs:element name="getInformationProduit_tnResponse" type="tns:getInformationProduit_tnResponse" />
  <xs:element name="getListeEtablissements" type="tns:getListeEtablissements" />
  <xs:element name="getListeEtablissementsResponse" type="tns:getListeEtablissementsResponse" />
  <xs:element name="getMonographieUrl" type="tns:getMonographieUrl" />
  <xs:element name="getMonographieUrlResponse" type="tns:getMonographieUrlResponse" />
  <xs:element name="getProduitsClasseATC" type="tns:getProduitsClasseATC" />
  <xs:element name="getProduitsClasseATCResponse" type="tns:getProduitsClasseATCResponse" />
  <xs:element name="getProduitsComposant" type="tns:getProduitsComposant" />
  <xs:element name="getProduitsComposantResponse" type="tns:getProduitsComposantResponse" />
  <xs:element name="getProduitsDC" type="tns:getProduitsDC" />
  <xs:element name="getProduitsDCResponse" type="tns:getProduitsDCResponse" />
  <xs:element name="getProduitsIndication" type="tns:getProduitsIndication" />
  <xs:element name="getProduitsIndicationResponse" type="tns:getProduitsIndicationResponse" />
  <xs:element name="getUser" type="tns:getUser" />
  <xs:element name="getUserResponse" type="tns:getUserResponse" />
  <xs:element name="getUsersEtablissement" type="tns:getUsersEtablissement" />
  <xs:element name="getUsersEtablissementResponse" type="tns:getUsersEtablissementResponse" />
  <xs:element name="getVersion" type="tns:getVersion" />
  <xs:element name="getVersionResponse" type="tns:getVersionResponse" />
  <xs:element name="isLoginExist" type="tns:isLoginExist" />
  <xs:element name="isLoginExistResponse" type="tns:isLoginExistResponse" />
  <xs:element name="rechercheBCB" type="tns:rechercheBCB" />
  <xs:element name="rechercheBCBHopital" type="tns:rechercheBCBHopital" />
  <xs:element name="rechercheBCBHopitalResponse" type="tns:rechercheBCBHopitalResponse" />
  <xs:element name="rechercheBCBResponse" type="tns:rechercheBCBResponse" />
  <xs:element name="rechercheEquivalents" type="tns:rechercheEquivalents" />
  <xs:element name="rechercheEquivalentsResponse" type="tns:rechercheEquivalentsResponse" />
  <xs:element name="rechercheParCodePCT" type="tns:rechercheParCodePCT" />
  <xs:element name="rechercheParCodePCTResponse" type="tns:rechercheParCodePCTResponse" />
  <xs:element name="testConnexion" type="tns:testConnexion" />
  <xs:element name="testConnexionResponse" type="tns:testConnexionResponse" />
  <xs:element name="updateEtablissement" type="tns:updateEtablissement" />
  <xs:element name="updateEtablissementResponse" type="tns:updateEtablissementResponse" />
  <xs:element name="updateUser" type="tns:updateUser" />
  <xs:element name="updateUserResponse" type="tns:updateUserResponse" />
  <xs:complexType name="rechercheEquivalents">
    <xs:sequence>
      <xs:element name="idProduit" type="xs:int" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbSecurity">
    <xs:sequence>
      <xs:element minOccurs="0" name="codeEditeur" type="xs:string" />
      <xs:element minOccurs="0" name="idPS" type="xs:string" />
      <xs:element minOccurs="0" name="secretEditeur" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="rechercheEquivalentsResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="searchResult" type="tns:bcbEquivalents" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbEquivalents">
    <xs:sequence>
      <xs:element name="generique" type="xs:boolean" />
      <xs:element minOccurs="0" name="groupeGenerique" type="xs:string" />
      <xs:element name="idProduitOrigine" type="xs:int" />
      <xs:element name="idProduitReferent" type="xs:int" />
      <xs:element minOccurs="0" name="libelleProduitOrigine" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstClassesTherapeutiques" nillable="true" type="tns:bcbClasse" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstEquivalentsAutres" nillable="true" type="tns:bcbProduitMini" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstEquivalentsProches" nillable="true" type="tns:bcbProduitMini" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstEquivalentsStricts" nillable="true" type="tns:bcbProduitMini" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstProduitsGroupeGen" nillable="true" type="tns:bcbProduitMini" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstProduitsGroupeGenUP" nillable="true" type="tns:bcbProduitMiniUP" />
      <xs:element minOccurs="0" name="produitReferent" type="xs:string" />
      <xs:element name="referent" type="xs:boolean" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbClasse">
    <xs:sequence>
      <xs:element minOccurs="0" name="code" type="xs:string" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbProduitMini">
    <xs:sequence>
      <xs:element minOccurs="0" name="cip13" type="xs:string" />
      <xs:element minOccurs="0" name="cip7" type="xs:string" />
      <xs:element name="cip7Fictif" type="xs:boolean" />
      <xs:element minOccurs="0" name="codeReferent" type="xs:string" />
      <xs:element name="documentsOfficiels" type="xs:boolean" />
      <xs:element name="dopant" type="xs:boolean" />
      <xs:element name="generique" type="xs:boolean" />
      <xs:element name="homeopathie" type="xs:boolean" />
      <xs:element name="hospitalier" type="xs:boolean" />
      <xs:element name="idCategorie" type="xs:long" />
      <xs:element name="idProduit" type="xs:long" />
      <xs:element name="idTypeCategorie" type="xs:long" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
      <xs:element minOccurs="0" name="libelleCourt" type="xs:string" />
      <xs:element name="livretTherapeutique" type="xs:boolean" />
      <xs:element name="medicamentDC" type="xs:boolean" />
      <xs:element name="medicamentException" type="xs:boolean" />
      <xs:element name="parapharmacie" type="xs:boolean" />
      <xs:element minOccurs="0" name="prixVenteTtc" type="xs:string" />
      <xs:element name="referent" type="xs:boolean" />
      <xs:element name="reserveHopital" type="xs:boolean" />
      <xs:element name="supprime" type="xs:boolean" />
      <xs:element minOccurs="0" name="ucd13" type="xs:string" />
      <xs:element minOccurs="0" name="ucd7" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbProduitMiniUP">
    <xs:sequence>
      <xs:element name="idProduit" type="xs:long" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstUnitesPrise" nillable="true" type="tns:bcbUnitePrise" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbUnitePrise">
    <xs:sequence>
      <xs:element name="arrondi" type="xs:int" />
      <xs:element name="code" type="xs:int" />
      <xs:element name="jeterLeReste" type="xs:int" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
      <xs:element minOccurs="0" name="libelleAbrege" type="xs:string" />
      <xs:element minOccurs="0" name="libellePluriel" type="xs:string" />
      <xs:element minOccurs="0" name="libelleSingulier" type="xs:string" />
      <xs:element name="nbDbl" type="xs:double" />
      <xs:element minOccurs="0" name="nbStr" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getProduitsIndication">
    <xs:sequence>
      <xs:element name="typeIndication" type="xs:int" />
      <xs:element minOccurs="0" name="codeIndication" type="xs:string" />
      <xs:element name="baseLocation" type="xs:int" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getProduitsIndicationResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="searchResult" type="tns:bcbSearchResult" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbSearchResult">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="listeAccessoiresGeneriques" nillable="true" type="tns:bcbAccessoireGenerique" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="listeClasseChimique" nillable="true" type="tns:bcbClasseChimique" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="listeClassesATC" nillable="true" type="tns:bcbClasseATC" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="listeComposants" nillable="true" type="tns:bcbComposant" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="listeDC" nillable="true" type="tns:bcbdc" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="listeFamilles" nillable="true" type="tns:bcbFamille" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="listeIdentProduits" nillable="true" type="tns:bcbIdentProduit" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="listeIndications" nillable="true" type="tns:bcbIndication" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="listeLPP" nillable="true" type="tns:bcblppMini" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="listeLaboratoires" nillable="true" type="tns:bcbLaboratoire" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="listePathologies" nillable="true" type="tns:bcbPathologie" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="listeProduits" nillable="true" type="tns:bcbProduitMini" />
      <xs:element minOccurs="0" name="query" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbAccessoireGenerique">
    <xs:sequence>
      <xs:element minOccurs="0" name="code" type="xs:string" />
      <xs:element name="id" type="xs:long" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
      <xs:element minOccurs="0" name="libelleCourt" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbClasseChimique">
    <xs:sequence>
      <xs:element name="id" type="xs:long" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbClasseATC">
    <xs:sequence>
      <xs:element minOccurs="0" name="code" type="xs:string" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstFils" nillable="true" type="tns:bcbClasseATC" />
      <xs:element name="niveau" type="xs:int" />
      <xs:element name="noeudFils" type="xs:boolean" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbComposant">
    <xs:sequence>
      <xs:element name="afficher" type="xs:boolean" />
      <xs:element name="id" type="xs:long" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbdc">
    <xs:sequence>
      <xs:element name="idProduit" type="xs:long" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
      <xs:element name="supprime" type="xs:boolean" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbFamille">
    <xs:sequence>
      <xs:element minOccurs="0" name="code" type="xs:string" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
      <xs:element minOccurs="0" name="libellePere" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstFils" nillable="true" type="tns:bcbFamille" />
      <xs:element name="niveau" type="xs:int" />
      <xs:element name="noeudFils" type="xs:boolean" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbIdentProduit">
    <xs:sequence>
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbIndication">
    <xs:sequence>
      <xs:element minOccurs="0" name="code" type="xs:string" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
      <xs:element name="type" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcblppMini">
    <xs:sequence>
      <xs:element minOccurs="0" name="code" type="xs:string" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbLaboratoire">
    <xs:sequence>
      <xs:element minOccurs="0" name="adresse" type="xs:string" />
      <xs:element minOccurs="0" name="codePostal" type="xs:string" />
      <xs:element minOccurs="0" name="commentaires" type="xs:string" />
      <xs:element minOccurs="0" name="email" type="xs:string" />
      <xs:element minOccurs="0" name="id" type="xs:string" />
      <xs:element minOccurs="0" name="informations_medicales" type="xs:string" />
      <xs:element minOccurs="0" name="nom" type="xs:string" />
      <xs:element minOccurs="0" name="telecopie" type="xs:string" />
      <xs:element minOccurs="0" name="telephone" type="xs:string" />
      <xs:element minOccurs="0" name="ville" type="xs:string" />
      <xs:element minOccurs="0" name="webSiteUrl" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbPathologie">
    <xs:sequence>
      <xs:element minOccurs="0" name="codeMotClef" type="xs:string" />
      <xs:element name="glossaire" type="xs:boolean" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
      <xs:element name="typeIndication" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getUser">
    <xs:sequence>
      <xs:element name="id" type="xs:long" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getUserResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="result" type="tns:bcbUser" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbUser">
    <xs:sequence>
      <xs:element name="abonne" type="xs:int" />
      <xs:element name="bypass" type="xs:int" />
      <xs:element minOccurs="0" name="codePromo" type="xs:string" />
      <xs:element minOccurs="0" name="dateCreation" type="xs:string" />
      <xs:element minOccurs="0" name="dateDernierAcces" type="xs:string" />
      <xs:element minOccurs="0" name="dateFin" type="xs:string" />
      <xs:element name="droitAccesOutils" type="xs:int" />
      <xs:element name="droitGestionEtablissements" type="xs:int" />
      <xs:element name="droitGestionLivret" type="xs:int" />
      <xs:element name="droitGestionUtilisateurs" type="xs:int" />
      <xs:element name="droitModifierPass" type="xs:int" />
      <xs:element minOccurs="0" name="firstname" type="xs:string" />
      <xs:element minOccurs="0" name="fonction" type="xs:string" />
      <xs:element minOccurs="0" name="guid" type="xs:string" />
      <xs:element name="id" type="xs:long" />
      <xs:element name="idBaseContrat" type="xs:int" />
      <xs:element name="idEtablissement" type="xs:long" />
      <xs:element minOccurs="0" name="libelleEtablissement" type="xs:string" />
      <xs:element name="light" type="xs:int" />
      <xs:element minOccurs="0" name="login" type="xs:string" />
      <xs:element minOccurs="0" name="name" type="xs:string" />
      <xs:element minOccurs="0" name="owaAdeli" type="xs:string" />
      <xs:element minOccurs="0" name="owaAdresse" type="xs:string" />
      <xs:element minOccurs="0" name="owaCategorie" type="xs:string" />
      <xs:element name="owaCivilite" type="xs:int" />
      <xs:element minOccurs="0" name="owaCiviliteLibelle" type="xs:string" />
      <xs:element minOccurs="0" name="owaCodePays" type="xs:string" />
      <xs:element minOccurs="0" name="owaCodePostal" type="xs:string" />
      <xs:element minOccurs="0" name="owaDateNaissance" type="xs:string" />
      <xs:element minOccurs="0" name="owaEmail" type="xs:string" />
      <xs:element minOccurs="0" name="owaEtablissement" type="xs:string" />
      <xs:element minOccurs="0" name="owaProfession" type="xs:string" />
      <xs:element minOccurs="0" name="owaProfessionLibelle" type="xs:string" />
      <xs:element minOccurs="0" name="owaProfessionOneKey" type="xs:string" />
      <xs:element minOccurs="0" name="owaRpps" type="xs:string" />
      <xs:element minOccurs="0" name="owaTelMobile" type="xs:string" />
      <xs:element minOccurs="0" name="owaTelProfessionnel" type="xs:string" />
      <xs:element minOccurs="0" name="owaTitre" type="xs:string" />
      <xs:element name="owaTl" type="xs:int" />
      <xs:element minOccurs="0" name="owaUserType" type="xs:string" />
      <xs:element minOccurs="0" name="owaVille" type="xs:string" />
      <xs:element minOccurs="0" name="pass" type="xs:string" />
      <xs:element name="profilDemarrage" type="xs:long" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getInformationProduit_befr">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="idProduits" type="xs:int" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getInformationProduit_befrResponse">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="produitResult" type="tns:etrInfoProduitsBefr" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="etrInfoProduitsBefr">
    <xs:sequence>
      <xs:element minOccurs="0" name="firnm" type="xs:string" />
      <xs:element name="idProduit" type="xs:int" />
      <xs:element minOccurs="0" name="ircv" type="xs:string" />
      <xs:element minOccurs="0" name="localCodeForSearch1" type="xs:string" />
      <xs:element minOccurs="0" name="localCodeForSearch2" type="xs:string" />
      <xs:element minOccurs="0" name="localCodeForSearch3" type="xs:string" />
      <xs:element minOccurs="0" name="mpcv" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="controleOrdonnanceParam">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstIdProduit" type="xs:int" />
      <xs:element minOccurs="0" name="profilPatient" type="tns:bcbProfilPatient" />
      <xs:element name="typeControle" type="xs:int" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbProfilPatient">
    <xs:sequence>
      <xs:element name="age" type="xs:int" />
      <xs:element name="allaitement" type="xs:int" />
      <xs:element name="clairanceCreatinine" type="xs:int" />
      <xs:element name="grossesse" type="xs:int" />
      <xs:element minOccurs="0" name="insuffisanceHepatique" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstIdComposantAllergie" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstPathologiesAMM" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstPathologiesCIM10" nillable="true" type="xs:string" />
      <xs:element name="poids" type="xs:int" />
      <xs:element minOccurs="0" name="sexe" type="xs:string" />
      <xs:element name="taille" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="controleOrdonnanceParamResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="controleResult" type="tns:bcbControle" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbControle">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstAllergies" nillable="true" type="tns:bcbAllergie" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstAllergiesNonTraitees" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstContresIndications" nillable="true" type="tns:bcbContreIndication" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstIPC" nillable="true" type="tns:bcbipc" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstInteractions" nillable="true" type="tns:bcbInteraction" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstPathologiesAMMNonTraitees" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstPathologiesCIM10NonTraitees" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstPrecautionsEmploi" nillable="true" type="tns:bcbContreIndication" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstProduitsBcbNonTraites" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstProduitsNonTraitesInteractions" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstSurdosage" nillable="true" type="tns:bcbSurdosage" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstSurdosageNS" nillable="true" type="tns:bcbSurdosageNS" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbAllergie">
    <xs:sequence>
      <xs:element minOccurs="0" name="alerteMsg" type="xs:string" />
      <xs:element minOccurs="0" name="allergieHTML" type="xs:string" />
      <xs:element name="idComposant" type="xs:int" />
      <xs:element name="idComposantProduit" type="xs:int" />
      <xs:element name="idProduit" type="xs:int" />
      <xs:element minOccurs="0" name="libelleComposant" type="xs:string" />
      <xs:element minOccurs="0" name="libelleComposantProduit" type="xs:string" />
      <xs:element minOccurs="0" name="libelleProduit" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbContreIndication">
    <xs:sequence>
      <xs:element minOccurs="0" name="AMMOrigine" type="xs:string" />
      <xs:element minOccurs="0" name="CIHTML" type="xs:string" />
      <xs:element minOccurs="0" name="codePathologie" type="xs:string" />
      <xs:element name="idProduit" type="xs:int" />
      <xs:element minOccurs="0" name="libelleAMMOrigine" type="xs:string" />
      <xs:element minOccurs="0" name="libelleNiveau" type="xs:string" />
      <xs:element minOccurs="0" name="libellePathologie" type="xs:string" />
      <xs:element minOccurs="0" name="libelleProduit" type="xs:string" />
      <xs:element name="niveau" type="xs:int" />
      <xs:element minOccurs="0" name="source" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbipc">
    <xs:sequence>
      <xs:element name="codeVoie" type="xs:int" />
      <xs:element minOccurs="0" name="IPCHTML" type="xs:string" />
      <xs:element name="idPrincipeActif1" type="xs:int" />
      <xs:element name="idPrincipeActif2" type="xs:int" />
      <xs:element name="idProduit1" type="xs:int" />
      <xs:element name="idProduit2" type="xs:int" />
      <xs:element minOccurs="0" name="libellePrincipeActif1" type="xs:string" />
      <xs:element minOccurs="0" name="libellePrincipeActif2" type="xs:string" />
      <xs:element minOccurs="0" name="libelleProduit1" type="xs:string" />
      <xs:element minOccurs="0" name="libelleProduit2" type="xs:string" />
      <xs:element minOccurs="0" name="libelleVoie" type="xs:string" />
      <xs:element minOccurs="0" name="texte" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbInteraction">
    <xs:sequence>
      <xs:element minOccurs="0" name="classe1" type="xs:string" />
      <xs:element minOccurs="0" name="classe2" type="xs:string" />
      <xs:element minOccurs="0" name="codeClasse1" type="xs:string" />
      <xs:element minOccurs="0" name="codeClasse2" type="xs:string" />
      <xs:element minOccurs="0" name="composantDeclencheur1" type="xs:string" />
      <xs:element minOccurs="0" name="composantDeclencheur2" type="xs:string" />
      <xs:element minOccurs="0" name="conduite" type="xs:string" />
      <xs:element minOccurs="0" name="gravite" type="xs:string" />
      <xs:element name="idProduit1" type="xs:int" />
      <xs:element name="idProduit2" type="xs:int" />
      <xs:element minOccurs="0" name="interactionHTML" type="xs:string" />
      <xs:element minOccurs="0" name="libelleProduit1" type="xs:string" />
      <xs:element minOccurs="0" name="libelleProduit2" type="xs:string" />
      <xs:element minOccurs="0" name="lngType" type="xs:string" />
      <xs:element minOccurs="0" name="mecanisme" type="xs:string" />
      <xs:element minOccurs="0" name="message" type="xs:string" />
      <xs:element name="niveau" type="xs:int" />
      <xs:element minOccurs="0" name="texte" type="xs:string" />
      <xs:element minOccurs="0" name="type" type="xs:string" />
      <xs:element minOccurs="0" name="voie1" type="xs:string" />
      <xs:element minOccurs="0" name="voie2" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbSurdosage">
    <xs:sequence>
      <xs:element minOccurs="0" name="idClasse1" type="xs:string" />
      <xs:element minOccurs="0" name="idClasse2" type="xs:string" />
      <xs:element minOccurs="0" name="idComposant" type="xs:string" />
      <xs:element name="idProduit1" type="xs:int" />
      <xs:element name="idProduit2" type="xs:int" />
      <xs:element minOccurs="0" name="libelleClasse1" type="xs:string" />
      <xs:element minOccurs="0" name="libelleClasse2" type="xs:string" />
      <xs:element minOccurs="0" name="libelleComposant" type="xs:string" />
      <xs:element minOccurs="0" name="libelleProduit1" type="xs:string" />
      <xs:element minOccurs="0" name="libelleProduit2" type="xs:string" />
      <xs:element name="niveau" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbSurdosageNS">
    <xs:sequence>
      <xs:element minOccurs="0" name="description" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstProduits" nillable="true" type="tns:bcbObj" />
      <xs:element minOccurs="0" name="redondanceHTML" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="tableauResultat" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="titre1" type="xs:string" />
      <xs:element minOccurs="0" name="titre2" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbObj">
    <xs:sequence>
      <xs:element name="id" type="xs:int" />
      <xs:element minOccurs="0" name="value" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getProduitsComposant">
    <xs:sequence>
      <xs:element name="idComposant" type="xs:int" />
      <xs:element name="baseLocation" type="xs:int" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getProduitsComposantResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="searchResult" type="tns:bcbSearchResult" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="rechercheBCB">
    <xs:sequence>
      <xs:element minOccurs="0" name="query" type="xs:string" />
      <xs:element name="type" type="xs:int" />
      <xs:element name="baseLocation" type="xs:int" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="rechercheBCBResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="searchResult" type="tns:bcbSearchResult" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getInformationProduit_ma">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="idProduits" type="xs:int" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getInformationProduit_maResponse">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="produitResult" type="tns:etrInfoProduitsMa" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="etrInfoProduitsMa">
    <xs:sequence>
      <xs:element name="idProduit" type="xs:int" />
      <xs:element minOccurs="0" name="localCodeForSearch1" type="xs:string" />
      <xs:element minOccurs="0" name="localCodeForSearch2" type="xs:string" />
      <xs:element minOccurs="0" name="localCodeForSearch3" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="deleteEtablissement">
    <xs:sequence>
      <xs:element name="idEtablissement" type="xs:long" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="deleteEtablissementResponse">
    <xs:sequence>
      <xs:element name="result" type="xs:boolean" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="rechercheBCBHopital">
    <xs:sequence>
      <xs:element minOccurs="0" name="query" type="xs:string" />
      <xs:element name="type" type="xs:int" />
      <xs:element name="baseLocation" type="xs:int" />
      <xs:element name="codeEtablissement" type="xs:long" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="rechercheBCBHopitalResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="searchResult" type="tns:bcbSearchResult" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="testConnexion">
    <xs:sequence>
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="testConnexionResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="result" type="tns:bcbInformation" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbInformation">
    <xs:sequence>
      <xs:element minOccurs="0" name="codeEditeur" type="xs:string" />
      <xs:element minOccurs="0" name="dateBase" type="xs:string" />
      <xs:element minOccurs="0" name="dateFinAbonnement" type="xs:string" />
      <xs:element minOccurs="0" name="dateFinAbonnementPS" type="xs:string" />
      <xs:element minOccurs="0" name="dateFinBase" type="xs:string" />
      <xs:element minOccurs="0" name="IP" type="xs:string" />
      <xs:element minOccurs="0" name="idPS" type="xs:string" />
      <xs:element minOccurs="0" name="infoAbonnement" type="xs:string" />
      <xs:element minOccurs="0" name="libelleEditeur" type="xs:string" />
      <xs:element minOccurs="0" name="nomLogiciel" type="xs:string" />
      <xs:element name="statutConnexion" type="xs:int" />
      <xs:element minOccurs="0" name="statutConnexionLibelle" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="controleOrdonnance">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstIdProduit" type="xs:int" />
      <xs:element minOccurs="0" name="profilPatient" type="tns:bcbProfilPatient" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="controleOrdonnanceResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="controleResult" type="tns:bcbControle" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getProduitsClasseATC">
    <xs:sequence>
      <xs:element minOccurs="0" name="codeClasseATC" type="xs:string" />
      <xs:element name="baseLocation" type="xs:int" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getProduitsClasseATCResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="searchResult" type="tns:bcbSearchResult" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="rechercheParCodePCT">
    <xs:sequence>
      <xs:element minOccurs="0" name="codePCT" type="xs:string" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="rechercheParCodePCTResponse">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="searchResult" type="tns:bcbProduitMini" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="checkLogin">
    <xs:sequence>
      <xs:element minOccurs="0" name="login" type="xs:string" />
      <xs:element minOccurs="0" name="password" type="xs:string" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="checkLoginResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="result" type="tns:bcbUser" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getUsersEtablissement">
    <xs:sequence>
      <xs:element name="idEtablissement" type="xs:long" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getUsersEtablissementResponse">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="result" type="tns:bcbUser" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="updateEtablissement">
    <xs:sequence>
      <xs:element minOccurs="0" name="etablissement" type="tns:bcbEtablissement" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbEtablissement">
    <xs:sequence>
      <xs:element minOccurs="0" name="adresse1" type="xs:string" />
      <xs:element minOccurs="0" name="adresse2" type="xs:string" />
      <xs:element name="code" type="xs:long" />
      <xs:element minOccurs="0" name="codePostal" type="xs:string" />
      <xs:element minOccurs="0" name="dateFin" type="xs:string" />
      <xs:element minOccurs="0" name="fax" type="xs:string" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstIp" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="mail" type="xs:string" />
      <xs:element minOccurs="0" name="tel" type="xs:string" />
      <xs:element minOccurs="0" name="ville" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="updateEtablissementResponse">
    <xs:sequence>
      <xs:element name="result" type="xs:long" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="deleteUser">
    <xs:sequence>
      <xs:element name="id" type="xs:long" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="deleteUserResponse">
    <xs:sequence>
      <xs:element name="result" type="xs:boolean" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="isLoginExist">
    <xs:sequence>
      <xs:element minOccurs="0" name="login" type="xs:string" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="isLoginExistResponse">
    <xs:sequence>
      <xs:element name="result" type="xs:boolean" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="updateUser">
    <xs:sequence>
      <xs:element minOccurs="0" name="user" type="tns:bcbUser" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="updateUserResponse">
    <xs:sequence>
      <xs:element name="result" type="xs:long" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getInformationProduit">
    <xs:sequence>
      <xs:element name="idProduit" type="xs:int" />
      <xs:element name="mode" type="xs:int" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getInformationProduitResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="produitResult" type="tns:bcbProduit" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbProduit">
    <xs:sequence>
      <xs:element name="agrementCollectivites" type="xs:boolean" />
      <xs:element name="attributCasher" type="xs:int" />
      <xs:element minOccurs="0" name="baseRemboursementSS" type="xs:string" />
      <xs:element minOccurs="0" name="cip13" type="xs:string" />
      <xs:element minOccurs="0" name="cip7" type="xs:string" />
      <xs:element name="cip7fictif" type="xs:boolean" />
      <xs:element minOccurs="0" name="cipProduitRemplacant" type="xs:string" />
      <xs:element minOccurs="0" name="code13Referent" type="xs:string" />
      <xs:element minOccurs="0" name="codeForme" type="xs:string" />
      <xs:element name="codeGroupeGenerique" type="xs:int" />
      <xs:element minOccurs="0" name="codeIdentifiantSpecialite" type="xs:string" />
      <xs:element minOccurs="0" name="codePoidsVolume" type="xs:string" />
      <xs:element name="codeStatut" type="xs:int" />
      <xs:element name="codeStockage" type="xs:int" />
      <xs:element minOccurs="0" name="compositionCommentaire" type="xs:string" />
      <xs:element minOccurs="0" name="compositionDateAMM" type="xs:string" />
      <xs:element name="compositionIdExprimePar" type="xs:int" />
      <xs:element minOccurs="0" name="compositionLibelleExprimePar" type="xs:string" />
      <xs:element minOccurs="0" name="contenance" type="xs:string" />
      <xs:element minOccurs="0" name="dateAMM" type="xs:string" />
      <xs:element minOccurs="0" name="dateResponsabiliteUcd" type="xs:string" />
      <xs:element minOccurs="0" name="dateSupprime" type="xs:string" />
      <xs:element name="dispensationCodeUnitePrise" type="xs:int" />
      <xs:element minOccurs="0" name="dispensationLibelleUnitePrise" type="xs:string" />
      <xs:element name="dispensationNombre" type="xs:int" />
      <xs:element name="distributionCodeUnitePrise" type="xs:int" />
      <xs:element minOccurs="0" name="distributionLibelleUnitePrise" type="xs:string" />
      <xs:element name="distributionNombre" type="xs:double" />
      <xs:element name="dopant" type="xs:boolean" />
      <xs:element minOccurs="0" name="dosage" type="xs:string" />
      <xs:element name="dureePeremption" type="xs:int" />
      <xs:element minOccurs="0" name="ean13" type="xs:string" />
      <xs:element minOccurs="0" name="forme" type="xs:string" />
      <xs:element name="generique" type="xs:boolean" />
      <xs:element name="hauteur" type="xs:int" />
      <xs:element name="hospitalier" type="xs:boolean" />
      <xs:element name="idCategorie" type="xs:int" />
      <xs:element name="idDC" type="xs:int" />
      <xs:element name="idDCMolecule" type="xs:int" />
      <xs:element name="idProduit" type="xs:long" />
      <xs:element name="idProduitRemplacant" type="xs:long" />
      <xs:element name="idType" type="xs:int" />
      <xs:element name="idTypeCategorie" type="xs:int" />
      <xs:element minOccurs="0" name="laboratoireExploitant" type="tns:bcbLaboratoire" />
      <xs:element minOccurs="0" name="laboratoireTitulaire" type="tns:bcbLaboratoire" />
      <xs:element name="largeur" type="xs:int" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
      <xs:element minOccurs="0" name="libelleAbrege" type="xs:string" />
      <xs:element minOccurs="0" name="libelleActeNormeB2" type="xs:string" />
      <xs:element minOccurs="0" name="libelleCasher" type="xs:string" />
      <xs:element minOccurs="0" name="libelleConditionnement" type="xs:string" />
      <xs:element minOccurs="0" name="libelleCourt" type="xs:string" />
      <xs:element minOccurs="0" name="libelleDC" type="xs:string" />
      <xs:element minOccurs="0" name="libelleDCMolecule" type="xs:string" />
      <xs:element minOccurs="0" name="libelleDivers" type="xs:string" />
      <xs:element minOccurs="0" name="libelleGroupeGenerique" type="xs:string" />
      <xs:element minOccurs="0" name="libelleGroupeGeneriqueCourt" type="xs:string" />
      <xs:element minOccurs="0" name="libellePresentation" type="xs:string" />
      <xs:element minOccurs="0" name="libelleProduitRemplacant" type="xs:string" />
      <xs:element minOccurs="0" name="libelleSpecialite" type="xs:string" />
      <xs:element minOccurs="0" name="libelleStockage" type="xs:string" />
      <xs:element minOccurs="0" name="lienNoticeANSM" type="xs:string" />
      <xs:element minOccurs="0" name="lienRCP" type="xs:string" />
      <xs:element minOccurs="0" name="liste" type="xs:string" />
      <xs:element name="longueur" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstAsmr" nillable="true" type="tns:bcbSmrAsmr" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstClassesATC" nillable="true" type="tns:bcbClasse" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstClassesTherapeutiques" nillable="true" type="tns:bcbClasse" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstContresIndications" nillable="true" type="tns:bcbMotClef" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstDetailsLPP" nillable="true" type="tns:bcblpp" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstEan13" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstEffetsIndesirables" nillable="true" type="tns:bcbMotClef" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstExcipients" nillable="true" type="tns:bcbExcipient" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstFamilles" nillable="true" type="tns:bcbFamille" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstIndications" nillable="true" type="tns:bcbMotClef" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstLiensCrat" nillable="true" type="tns:bcbLienCrat" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstPrecautionsEmplois" nillable="true" type="tns:bcbMotClef" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstPrescription" nillable="true" type="tns:bcbPrescript" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstPrincipesActifs" nillable="true" type="tns:bcbPrincipeActif" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstSmr" nillable="true" type="tns:bcbSmrAsmr" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstUnitesPrise" nillable="true" type="tns:bcbUnitePrise" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstVoies" nillable="true" type="tns:bcbVoie" />
      <xs:element name="medicamentDC" type="xs:boolean" />
      <xs:element name="medicamentException" type="xs:boolean" />
      <xs:element name="medicamentT2A" type="xs:boolean" />
      <xs:element minOccurs="0" name="monographieHTML" type="tns:bcbHtml" />
      <xs:element minOccurs="0" name="monographiePDF" type="xs:base64Binary" />
      <xs:element minOccurs="0" name="numeroAMM" type="xs:string" />
      <xs:element name="otc" type="xs:boolean" />
      <xs:element name="parapharmacie" type="xs:boolean" />
      <xs:element name="pictogramme" type="xs:int" />
      <xs:element minOccurs="0" name="poidsVolume" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="prescriptionRestreinte" nillable="true" type="tns:bcbPrescriptionRestreinte" />
      <xs:element minOccurs="0" name="prixAchatHTFabricant" type="xs:string" />
      <xs:element minOccurs="0" name="prixAchatHTGrossiste" type="xs:string" />
      <xs:element minOccurs="0" name="prixVente" type="xs:string" />
      <xs:element minOccurs="0" name="refHTML" type="xs:string" />
      <xs:element minOccurs="0" name="referenceLPPR" type="xs:string" />
      <xs:element minOccurs="0" name="referenceTIPS" type="xs:string" />
      <xs:element name="referent" type="xs:boolean" />
      <xs:element minOccurs="0" name="statut" type="xs:string" />
      <xs:element name="stupefiant" type="xs:boolean" />
      <xs:element name="TFR" type="xs:boolean" />
      <xs:element minOccurs="0" name="tarifResponsabiliteUcd" type="xs:string" />
      <xs:element name="tauxSS" type="xs:int" />
      <xs:element name="tauxTva" type="xs:float" />
      <xs:element name="typeConditionnement" type="xs:int" />
      <xs:element minOccurs="0" name="ucd13" type="xs:string" />
      <xs:element minOccurs="0" name="ucd7" type="xs:string" />
      <xs:element minOccurs="0" name="version" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbSmrAsmr">
    <xs:sequence>
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
      <xs:element minOccurs="0" name="libelleAmm" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbMotClef">
    <xs:sequence>
      <xs:element minOccurs="0" name="code" type="xs:string" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
      <xs:element name="niveau_ci" type="xs:int" />
      <xs:element name="type_ci_pe" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcblpp">
    <xs:sequence>
      <xs:element name="ageMaxi" type="xs:int" />
      <xs:element name="ageMini" type="xs:int" />
      <xs:element minOccurs="0" name="code" type="xs:string" />
      <xs:element name="coefficient" type="xs:double" />
      <xs:element minOccurs="0" name="dateLimite" type="xs:string" />
      <xs:element name="indications" type="xs:boolean" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstHistorique" nillable="true" type="tns:bcblppHistorique" />
      <xs:element minOccurs="0" name="typePrestation" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcblppHistorique">
    <xs:sequence>
      <xs:element minOccurs="0" name="dateArreteMinisteriel" type="xs:string" />
      <xs:element minOccurs="0" name="dateDebut" type="xs:string" />
      <xs:element minOccurs="0" name="dateFin" type="xs:string" />
      <xs:element minOccurs="0" name="datePublicationJo" type="xs:string" />
      <xs:element name="ententePrealable" type="xs:boolean" />
      <xs:element minOccurs="0" name="naturePrestation" type="xs:string" />
      <xs:element name="plv" type="xs:double" />
      <xs:element name="tarifLPP" type="xs:double" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbExcipient">
    <xs:sequence>
      <xs:element name="attribut" type="xs:boolean" />
      <xs:element minOccurs="0" name="commentaire" type="xs:string" />
      <xs:element name="effetNotoire" type="xs:boolean" />
      <xs:element name="iam" type="xs:boolean" />
      <xs:element name="id" type="xs:long" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbLienCrat">
    <xs:sequence>
      <xs:element name="codeComposant" type="xs:int" />
      <xs:element minOccurs="0" name="lienHTML" type="xs:string" />
      <xs:element minOccurs="0" name="texte" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbPrescript">
    <xs:sequence>
      <xs:element minOccurs="0" name="codePrescriptionParticuliere" type="xs:string" />
      <xs:element name="codeSpecialitePrescripteur" type="xs:int" />
      <xs:element minOccurs="0" name="libellePrescriptionParticuliere" type="xs:string" />
      <xs:element minOccurs="0" name="libelleSpecialitePrescripteur" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbPrincipeActif">
    <xs:sequence>
      <xs:element name="attribut" type="xs:boolean" />
      <xs:element name="codeUnite" type="xs:long" />
      <xs:element minOccurs="0" name="commentaire" type="xs:string" />
      <xs:element name="iam" type="xs:boolean" />
      <xs:element name="id" type="xs:long" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
      <xs:element minOccurs="0" name="libelleUnite" type="xs:string" />
      <xs:element name="quantite" type="xs:double" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbVoie">
    <xs:sequence>
      <xs:element name="code" type="xs:int" />
      <xs:element minOccurs="0" name="codeAncien" type="xs:string" />
      <xs:element minOccurs="0" name="codeStandard" type="xs:string" />
      <xs:element minOccurs="0" name="libelle" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbHtml">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="lstResource" nillable="true" type="tns:bcbResource" />
      <xs:element minOccurs="0" name="monographie" type="xs:base64Binary" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbResource">
    <xs:sequence>
      <xs:element minOccurs="0" name="chemin" type="xs:string" />
      <xs:element minOccurs="0" name="data" type="xs:base64Binary" />
      <xs:element minOccurs="0" name="nom" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbPrescriptionRestreinte">
    <xs:sequence>
      <xs:element name="code" type="xs:int" />
      <xs:element minOccurs="0" name="libelleDecret" type="xs:string" />
      <xs:element minOccurs="0" name="libelleTitre" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="deleteAllUsersEtablissement">
    <xs:sequence>
      <xs:element name="idEtablissement" type="xs:long" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="deleteAllUsersEtablissementResponse">
    <xs:sequence>
      <xs:element name="result" type="xs:boolean" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="addUser">
    <xs:sequence>
      <xs:element minOccurs="0" name="user" type="tns:bcbUser" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="addUserResponse">
    <xs:sequence>
      <xs:element name="result" type="xs:long" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getInformationProduit_tn">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="idProduits" type="xs:int" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getInformationProduit_tnResponse">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="produitResult" type="tns:etrInfoProduitsTn" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="etrInfoProduitsTn">
    <xs:sequence>
      <xs:element minOccurs="0" name="accordPrealable" type="xs:string" />
      <xs:element name="clientCliniquePrivee" type="xs:int" />
      <xs:element name="clientHopitalPublic" type="xs:int" />
      <xs:element name="clientOfficinePrivee" type="xs:int" />
      <xs:element minOccurs="0" name="codePct" type="xs:string" />
      <xs:element minOccurs="0" name="commercialise" type="xs:string" />
      <xs:element minOccurs="0" name="conservation" type="xs:string" />
      <xs:element minOccurs="0" name="dateSuppression" type="xs:string" />
      <xs:element minOccurs="0" name="designationProduit" type="xs:string" />
      <xs:element minOccurs="0" name="distributeur" type="xs:string" />
      <xs:element name="dureeDeVie" type="xs:int" />
      <xs:element minOccurs="0" name="fabricant" type="xs:string" />
      <xs:element minOccurs="0" name="fournisseur" type="xs:string" />
      <xs:element minOccurs="0" name="hospitalier" type="xs:string" />
      <xs:element name="idProduit" type="xs:int" />
      <xs:element name="liste" type="xs:int" />
      <xs:element minOccurs="0" name="localCodeForSearch1" type="xs:string" />
      <xs:element minOccurs="0" name="localCodeForSearch2" type="xs:string" />
      <xs:element minOccurs="0" name="localCodeForSearch3" type="xs:string" />
      <xs:element name="ppt" type="xs:float" />
      <xs:element name="prixHopital" type="xs:float" />
      <xs:element name="prixPharmacien" type="xs:float" />
      <xs:element name="prixPublic" type="xs:float" />
      <xs:element minOccurs="0" name="secteur" type="xs:string" />
      <xs:element name="tarifReference" type="xs:float" />
      <xs:element name="tauxTva" type="xs:float" />
      <xs:element minOccurs="0" name="veicCategorie" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getEffetsIndesirablesProduit">
    <xs:sequence>
      <xs:element name="idProduit" type="xs:int" />
      <xs:element name="modeTri" type="xs:int" />
      <xs:element name="descMode" type="xs:boolean" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getEffetsIndesirablesProduitResponse">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="result" type="tns:bcbEffetIndesirable" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbEffetIndesirable">
    <xs:sequence>
      <xs:element minOccurs="0" name="appareil" type="xs:string" />
      <xs:element name="codeFrequence" type="xs:int" />
      <xs:element minOccurs="0" name="codeMotClef" type="xs:string" />
      <xs:element minOccurs="0" name="libelleFrequence" type="xs:string" />
      <xs:element minOccurs="0" name="libelleMotClef" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getMonographieUrl">
    <xs:sequence>
      <xs:element name="idProduit" type="xs:int" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
      <xs:element name="optionalMode" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getMonographieUrlResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="result" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getProduitsDC">
    <xs:sequence>
      <xs:element name="idDC" type="xs:int" />
      <xs:element name="baseLocation" type="xs:int" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getProduitsDCResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="searchResult" type="tns:bcbSearchResult" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getVersion">
    <xs:sequence />
  </xs:complexType>
  <xs:complexType name="getVersionResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="result" type="tns:bcbVersion" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbVersion">
    <xs:sequence>
      <xs:element minOccurs="0" name="databaseVersion" type="xs:string" />
      <xs:element name="idVersionBase" type="xs:int" />
      <xs:element minOccurs="0" name="minimumRequiredDatabaseVersion" type="xs:string" />
      <xs:element name="revisionBase" type="xs:int" />
      <xs:element name="synchro" type="xs:boolean" />
      <xs:element minOccurs="0" name="webServiceVersion" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getListeEtablissements">
    <xs:sequence>
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getListeEtablissementsResponse">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="result" type="tns:bcbEtablissement" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getInformationProduitEtr">
    <xs:sequence>
      <xs:element name="idProduit" type="xs:int" />
      <xs:element name="mode" type="xs:int" />
      <xs:element name="pays" type="xs:int" />
      <xs:element name="includeRessource" type="xs:boolean" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getInformationProduitEtrResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="produitResult" type="tns:bcbProduitEtr" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="bcbProduitEtr">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:bcbProduit">
        <xs:sequence>
          <xs:element minOccurs="0" name="infosEtr" type="xs:anyType" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="getInformationProduit_ro">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="idProduits" type="xs:int" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getInformationProduit_roResponse">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="produitResult" type="tns:etrInfoProduitsRo" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="etrInfoProduitsRo">
    <xs:sequence>
      <xs:element minOccurs="0" name="anmConc" type="xs:string" />
      <xs:element minOccurs="0" name="anmDapp" type="xs:string" />
      <xs:element minOccurs="0" name="anmDencom" type="xs:string" />
      <xs:element minOccurs="0" name="anmForma" type="xs:string" />
      <xs:element minOccurs="0" name="anmNew" type="xs:string" />
      <xs:element minOccurs="0" name="anmPrescr" type="xs:string" />
      <xs:element name="biologic" type="xs:int" />
      <xs:element minOccurs="0" name="cgNew" type="xs:string" />
      <xs:element minOccurs="0" name="clsinsulin" type="xs:string" />
      <xs:element minOccurs="0" name="codComerc" type="xs:string" />
      <xs:element minOccurs="0" name="codifatc" type="xs:string" />
      <xs:element minOccurs="0" name="compNew" type="xs:string" />
      <xs:element minOccurs="0" name="concentrat" type="xs:string" />
      <xs:element minOccurs="0" name="dataOmo" type="xs:string" />
      <xs:element minOccurs="0" name="denIntern" type="xs:string" />
      <xs:element minOccurs="0" name="denumire" type="xs:string" />
      <xs:element minOccurs="0" name="forma" type="xs:string" />
      <xs:element minOccurs="0" name="fractionab" type="xs:string" />
      <xs:element minOccurs="0" name="gratNew" type="xs:string" />
      <xs:element name="hasbioech" type="xs:int" />
      <xs:element name="idProduit" type="xs:int" />
      <xs:element name="isbrand" type="xs:int" />
      <xs:element minOccurs="0" name="listaNew" type="xs:string" />
      <xs:element minOccurs="0" name="listeNew" type="xs:string" />
      <xs:element minOccurs="0" name="localCodeForSearch1" type="xs:string" />
      <xs:element minOccurs="0" name="localCodeForSearch2" type="xs:string" />
      <xs:element minOccurs="0" name="localCodeForSearch3" type="xs:string" />
      <xs:element name="lspeNew" type="xs:float" />
      <xs:element name="msfNew" type="xs:float" />
      <xs:element name="pens600" type="xs:float" />
      <xs:element minOccurs="0" name="prescri" type="xs:string" />
      <xs:element name="pretAm" type="xs:float" />
      <xs:element name="pretC2Ne" type="xs:float" />
      <xs:element name="pretRid" type="xs:float" />
      <xs:element minOccurs="0" name="prezentare" type="xs:string" />
      <xs:element name="procNew" type="xs:int" />
      <xs:element minOccurs="0" name="producator" type="xs:string" />
      <xs:element name="refCgNew" type="xs:float" />
      <xs:element name="refgNew" type="xs:float" />
      <xs:element minOccurs="0" name="socialNew" type="xs:string" />
      <xs:element name="speciallaw" type="xs:int" />
      <xs:element name="sumacNew" type="xs:float" />
      <xs:element minOccurs="0" name="tabelNew" type="xs:string" />
      <xs:element minOccurs="0" name="tara" type="xs:string" />
      <xs:element minOccurs="0" name="um" type="xs:string" />
      <xs:element name="ut" type="xs:int" />
      <xs:element minOccurs="0" name="valabNew" type="xs:string" />
      <xs:element name="versiune" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="addEtablissement">
    <xs:sequence>
      <xs:element minOccurs="0" name="etablissement" type="tns:bcbEtablissement" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="addEtablissementResponse">
    <xs:sequence>
      <xs:element name="result" type="xs:long" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getEtablissement">
    <xs:sequence>
      <xs:element name="idEtablissement" type="xs:long" />
      <xs:element minOccurs="0" name="key" type="tns:bcbSecurity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="getEtablissementResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="result" type="tns:bcbEtablissement" />
    </xs:sequence>
  </xs:complexType>
</xs:schema>