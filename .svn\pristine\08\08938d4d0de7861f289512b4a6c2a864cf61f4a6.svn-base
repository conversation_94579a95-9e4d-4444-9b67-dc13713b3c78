﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports Microsoft.Reporting.WinForms
Imports System.Linq.Dynamic

Public Class fEtatDetailsDesVentes
    Dim _SalesReportService As New Bll.Reporting.SalesReport
    Dim _VOrderBy As String = "Designation"
    Dim _VAscDesc As String = "Asc"

    Dim StrSQL As String = ""

    Dim cmdVente As New SqlCommand
    Dim daVente As New SqlDataAdapter
    Dim dsVente As New DataSet

    Public Initialisation As Boolean = False

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub init()

        'chargement des Formes
        StrSQL = "SELECT DISTINCT CodeForme,LibelleForme FROM FORME_ARTICLE Where SupprimeForme=0 ORDER BY LibelleForme ASC"
        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = StrSQL
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "FORME_ARTICLE")
        cmbForme.DataSource = dsVente.Tables("FORME_ARTICLE")
        cmbForme.ValueMember = "CodeForme"
        cmbForme.DisplayMember = "LibelleForme"
        cmbForme.ColumnHeaders = False
        cmbForme.Splits(0).DisplayColumns("CodeForme").Visible = False
        cmbForme.Splits(0).DisplayColumns("LibelleForme").Width = 10
        cmbForme.ExtendRightColumn = True

        'chargement des articles
        StrSQL = "SELECT DISTINCT CodeArticle,Designation FROM ARTICLE ORDER BY Designation ASC"
        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = StrSQL
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "ARTICLE")
        cmbArticle.DataSource = dsVente.Tables("ARTICLE")
        cmbArticle.ValueMember = "CodeArticle"
        cmbArticle.DisplayMember = "Designation"
        cmbArticle.ColumnHeaders = False
        cmbArticle.Splits(0).DisplayColumns("CodeArticle").Visible = False
        cmbArticle.Splits(0).DisplayColumns("Designation").Width = 10
        cmbArticle.ExtendRightColumn = True

        'chargement des Clients
        StrSQL = "SELECT DISTINCT CodeClient,Nom FROM CLIENT WHERE Supprime=0 ORDER BY Nom ASC"
        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = StrSQL
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "CLIENT")
        cmbClient.DataSource = dsVente.Tables("CLIENT")
        cmbClient.ValueMember = "CodeClient"
        cmbClient.DisplayMember = "Nom"
        cmbClient.ColumnHeaders = False
        cmbClient.Splits(0).DisplayColumns("CodeClient").Visible = False
        cmbClient.Splits(0).DisplayColumns("Nom").Width = 10
        cmbClient.ExtendRightColumn = True

        ''''''''''''''
        StrSQL = "SELECT CodeCategorie ,LibelleCategorie FROM CATEGORIE WHERE SupprimeCategorie=0 order by LibelleCategorie ASC"
        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = StrSQL
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "CATEGORIE")
        cmbCategorie.DataSource = dsVente.Tables("CATEGORIE")
        cmbCategorie.ValueMember = "CodeCategorie"
        cmbCategorie.DisplayMember = "LibelleCategorie"
        cmbCategorie.ColumnHeaders = False
        cmbCategorie.Splits(0).DisplayColumns("CodeCategorie").Visible = False
        cmbCategorie.Splits(0).DisplayColumns("LibelleCategorie").Width = 10
        cmbCategorie.ExtendRightColumn = True
        ''''''''''''''
        'chargement des Clients
        StrSQL = "SELECT DISTINCT CodeUtilisateur,Nom FROM UTILISATEUR WHERE supprime = 0 ORDER BY Nom ASC"
        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = StrSQL
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "VENDEUR")
        cmbvendeur.DataSource = dsVente.Tables("VENDEUR")
        cmbvendeur.ValueMember = "CodeUtilisateur"
        cmbvendeur.DisplayMember = "Nom"
        cmbvendeur.ColumnHeaders = False
        cmbvendeur.Splits(0).DisplayColumns("CodeUtilisateur").Visible = False
        cmbvendeur.Splits(0).DisplayColumns("Nom").Width = 10
        cmbvendeur.ExtendRightColumn = True

        dtpDebut.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDebut.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpFin.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpFin.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        Initialisation = True
        rdbTous.Checked = True
        Initialisation = True
        rdbNumero.Checked = True
        Initialisation = True

        If ModeADMIN = "ADMIN" Then
            gbTotal.Visible = True
        Else
            gbTotal.Visible = False
        End If

        AfficherDetailsVentes()

        dtpDebut.Focus()
        dtpFin.Text = System.DateTime.Today
        dtpDebut.Value = System.DateTime.Today 'DateAdd(DateInterval.Day, -30, System.DateTime.Today)

    End Sub


    Public Sub AfficherDetailsVentes()
        Dim TotalTTC As Decimal = 0.0
        Dim TotalTVA As Decimal = 0.0
        Dim NumeroVente As String = ""

        Dim NombreVente As Integer = 0
        Dim Qte As Integer = 0

        If dtpDebut.Text = "" Then
            dtpDebut.Value = Date.Now
        End If

        If dtpFin.Text = "" Then
            dtpFin.Value = Date.Now
        End If

        Dim List As New Library.SortableBindingList(Of Data.Reporting.P_Report_EtatDetailDesVentes_Result)(_SalesReportService.GetEtatDetailDesVents(dtpDebut.Value, _
                                                                                                    dtpFin.Value, _
                                                                                                    IIf(IsNothing(cmbArticle.SelectedValue), "", cmbArticle.SelectedValue), _
                                                                                                    IIf(IsNothing(cmbCategorie.SelectedValue), 0, cmbCategorie.SelectedValue), _
                                                                                                    IIf(IsNothing(cmbForme.SelectedValue), 0, cmbForme.SelectedValue), _
                                                                                                    IIf(IsNothing(cmbClient.SelectedValue), "", cmbClient.SelectedValue),
                                                                                                    chbRetourUniquement.Checked,
                                                                                                    IIf(IsNothing(cmbvendeur.SelectedValue), -1, Convert.ToInt32(cmbvendeur.SelectedValue)), _
                                                                                                    IIf(rdbTous.Checked, 2, IIf(rdbVentesFacturees.Checked, 1, 0))))
        With gDetailsVente
            .Columns.Clear()
            .DataSource = List
            .Rebind(False)
            .Columns("NumeroOperation").Caption = "Numéro"
            .Columns("Date").Caption = "Date"
            .Columns("CodeABarre").Caption = "Code"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Quantite").Caption = "Qté"
            .Columns("TVA").Caption = "TVA"
            .Columns("Remise").Caption = "Remise"
            .Columns("TotalVenteTTC").Caption = "Total TTC"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("NumeroOperation").Width = 120
            .Splits(0).DisplayColumns("Date").Width = 110
            .Splits(0).DisplayColumns("CodeABarre").Width = 100
            .Splits(0).DisplayColumns("Designation").Width = 200
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Quantite").Width = 40
            .Splits(0).DisplayColumns("TVA").Width = 70
            .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Remise").Width = 70
            .Splits(0).DisplayColumns("Remise").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalVenteTTC").Width = 100
            .Splits(0).DisplayColumns("TotalVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("Id").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("Code").Visible = False
            .Splits(0).DisplayColumns("CodeClient").Visible = False
            .Splits(0).DisplayColumns("NumeroFacture").Visible = False
            .Splits(0).DisplayColumns("CodeCategorie").Visible = False
            .Splits(0).DisplayColumns("CodeForme").Visible = False
            .Splits(0).DisplayColumns("PrixVenteHT").Visible = False


            '''''''''''''''''''
            ''''''''''''''''''''
            '.Splits(0).DisplayColumns("NumeroOperation").Visible = False
            '''''''''''''''''''


            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gDetailsVente)
        End With

        For Each item As Data.Reporting.P_Report_EtatDetailDesVentes_Result In List
            TotalTTC = TotalTTC + IIf(IsNothing(item.TotalVenteTTC), 0, item.TotalVenteTTC)
            TotalTVA = TotalTVA + IIf(IsNothing(item.TotalTVA), 0, item.TotalTVA)
            NombreVente = NombreVente + 1
            NumeroVente = item.NumeroOperation
            Qte = Qte + IIf(IsNothing(item.Quantite), 0, item.Quantite)
        Next

        'For I = 0 To gDetailsVente.RowCount - 1
        '    If gDetailsVente(I, "TotalVenteTTC").ToString <> "" Then
        '        TotalTTC = TotalTTC + gDetailsVente(I, "TotalVenteTTC")
        '    End If
        '    If gDetailsVente(I, "TVA").ToString <> "" Then
        '        TotalTVA = TotalTVA + gDetailsVente(I, "TVA")
        '    End If

        '    If gDetailsVente(I, "NumeroOperation").ToString <> NumeroVente Then
        '        NombreVente = NombreVente + 1
        '        NumeroVente = gDetailsVente(I, "NumeroOperation").ToString
        '    End If
        '    If gDetailsVente(I, "Quantite").ToString <> "" Then
        '        Qte = Qte + gDetailsVente(I, "Quantite")
        '    End If
        'Next

        lTotalTTC.Text = TotalTTC.ToString("### ### ##0.000")
        lTotalTVA.Text = TotalTVA.ToString("### ### ##0.000")
        lTotalQte.Text = Qte

        lNombreDesVentes.Text = NombreVente

    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherDetailsVentes()
            dtpFin.Focus()
        End If
    End Sub



    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherDetailsVentes()
            cmbArticle.Focus()
        End If
    End Sub



    Private Sub cmbArticle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbArticle.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbArticle.Text = cmbArticle.WillChangeToText
            AfficherDetailsVentes()
            cmbClient.Focus()
        Else
            cmbArticle.OpenCombo()
        End If
    End Sub

    Private Sub cmbClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbClient.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbClient.Text = cmbClient.WillChangeToText
            AfficherDetailsVentes()

            cmbCategorie.Focus()
        Else
            cmbClient.OpenCombo()
        End If
    End Sub

    Private Sub rdbTous_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbTous.CheckedChanged
        AfficherDetailsVentes()
    End Sub

    Private Sub rdbVentesFacturees_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbVentesFacturees.CheckedChanged
        AfficherDetailsVentes()
    End Sub

    Private Sub rdbVentesNonFacturees_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbVentesNonFacturees.CheckedChanged
        AfficherDetailsVentes()
    End Sub

    Private Sub rdbNumero_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbNumero.CheckedChanged
        AfficherDetailsVentes()
    End Sub

    Private Sub rdbMontant_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbMontant.CheckedChanged
        AfficherDetailsVentes()
    End Sub

    Private Sub rdbForme_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        AfficherDetailsVentes()
    End Sub

    'Private Sub rdbEtat_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbEtat.CheckedChanged
    '    AfficherDetailsVentes()
    'End Sub

    'Private Sub rdbFacture_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbFacture.CheckedChanged
    '    AfficherDetailsVentes()
    'End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Reporting Ventes" Then
                num = I
            End If
        Next
        Dim dt As EnumerableQuery(Of Data.Reporting.P_Report_EtatDetailDesVentes_Result)

        Dim _Parameters As New List(Of ReportParameter)()

        Dim _DateDebut As New ReportParameter()
        _DateDebut.Name = "DateDebut"
        _DateDebut.Values.Add(dtpDebut.Value)
        _Parameters.Add(_DateDebut)

        Dim _DateFin As New ReportParameter()
        _DateFin.Name = "DateFin"
        _DateFin.Values.Add(dtpFin.Value)
        _Parameters.Add(_DateFin)

        Dim _CodeArticle As New ReportParameter()
        _CodeArticle.Name = "CodeArticle"
        _CodeArticle.Values.Add(cmbArticle.SelectedValue)
        _Parameters.Add(_CodeArticle)

        Dim _CodeCategorie As New ReportParameter()
        _CodeCategorie.Name = "CodeCategorie"
        _CodeCategorie.Values.Add(cmbCategorie.SelectedValue)
        _Parameters.Add(_CodeCategorie)

        Dim _CodeForme As New ReportParameter()
        _CodeForme.Name = "CodeForme"
        _CodeForme.Values.Add(cmbForme.SelectedValue)
        _Parameters.Add(_CodeForme)

        Dim _CodeClient As New ReportParameter()
        _CodeClient.Name = "CodeClient"
        _CodeClient.Values.Add(cmbClient.SelectedValue)
        _Parameters.Add(_CodeClient)

        Dim _RetourUniquement As New ReportParameter()
        _RetourUniquement.Name = "RetourUniquement"
        _RetourUniquement.Values.Add(chbRetourUniquement.Checked)
        _Parameters.Add(_RetourUniquement)

        Dim _Facturer As New ReportParameter()
        _Facturer.Name = "Facturer"
        _Facturer.Values.Add(IIf(rdbTous.Checked, 2, IIf(rdbVentesFacturees.Checked, 1, 0)))
        _Parameters.Add(_Facturer)
        

        Dim MyViewer As New fImpressionReportingVente

        dt = _SalesReportService.GetEtatDetailDesVents(dtpDebut.Value, _
                                                        dtpFin.Value, _
                                                        IIf(IsNothing(cmbArticle.SelectedValue), "", cmbArticle.SelectedValue), _
                                                        IIf(IsNothing(cmbCategorie.SelectedValue), 0, cmbCategorie.SelectedValue), _
                                                        IIf(IsNothing(cmbForme.SelectedValue), 0, cmbForme.SelectedValue), _
                                                        IIf(IsNothing(cmbClient.SelectedValue), "", cmbClient.SelectedValue),
                                                        chbRetourUniquement.Checked,
                                                        IIf(IsNothing(cmbvendeur.SelectedValue), -1, Convert.ToInt32(cmbvendeur.SelectedValue)), _
                                                        IIf(rdbTous.Checked, 2, IIf(rdbVentesFacturees.Checked, 1, 0))).OrderBy(_VOrderBy + " " + _VAscDesc)

        Dim _ChiffresEnLettres As New ReportParameter()
        _ChiffresEnLettres.Name = "ChiffresEnLettres"
        _ChiffresEnLettres.Values.Add(ChiffresEnLettres(dt.ToList().Sum(Function(item) item.TotalVenteTTC - item.TotalMutuelle - item.TotalCNAM)))
        _Parameters.Add(_ChiffresEnLettres)

        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Etatdetaildesventes", dt))
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Pharmacie", _SalesReportService.GetPharmacie()))

        If chbEtatDetaile.Checked Then
            MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatDetailDesVentesDetaillee.rdl"
        Else
            MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatDetailDesVentes.rdl"
        End If

        MyViewer.ReportViewer1.LocalReport.SetParameters(_Parameters)

        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel1)

        MyViewer.ReportViewer1.RefreshReport()

        fMain.Tab.SelectedTab.Text = "Impression Reporting Ventes"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If

    End Sub

    Private Sub chbInjectionUniquement_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        AfficherDetailsVentes()
    End Sub

    Private Sub chbRetourUniquement_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbRetourUniquement.CheckedChanged
        AfficherDetailsVentes()
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub gDetailsVente_AfterSort(sender As Object, e As FilterEventArgs) Handles gDetailsVente.AfterSort
        Try
            _VOrderBy = e.Condition.Substring(0, e.Condition.IndexOf(" "))
            _VAscDesc = e.Condition.Substring(e.Condition.IndexOf(" ") + 1, e.Condition.Length - e.Condition.IndexOf(" ") - 1)
        Catch ex As Exception
            _VOrderBy = e.Condition
            _VAscDesc = "Asc"
        End Try
    End Sub


    Private Sub gFournisseur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gDetailsVente.KeyUp
        If e.KeyCode = Keys.F1 Then
            Dim MyVenteAffiche As New fVenteJusteAffichage
            MyVenteAffiche.NumeroVente = gDetailsVente(gDetailsVente.Row, "NumeroOperation")
            MyVenteAffiche.ShowDialog()
            MyVenteAffiche.Close()
            MyVenteAffiche.Dispose()

        End If
    End Sub

    Private Sub cmbCategorie_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCategorie.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbCategorie.Text = cmbCategorie.WillChangeToText
            AfficherDetailsVentes()
            rdbTous.Focus()
        Else
            cmbCategorie.OpenCombo()
        End If
    End Sub

    Private Sub bAfficherDetails_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAfficherDetails.Click
        If gDetailsVente.RowCount > 0 Then
            Dim MyVenteAffiche As New fVenteJusteAffichage
            MyVenteAffiche.NumeroVente = gDetailsVente(gDetailsVente.Row, "NumeroOperation").ToString.Replace("S -", "")
            MyVenteAffiche.ShowDialog()
            MyVenteAffiche.Close()
            MyVenteAffiche.Dispose()
        End If

    End Sub

    Private Sub cmbForme_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbForme.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbForme.Text = cmbForme.WillChangeToText
            AfficherDetailsVentes()
            cmbCategorie.Focus()
        Else
            cmbForme.OpenCombo()
        End If
    End Sub

    Private Sub cmbvendeur_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbvendeur.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbvendeur.Text = cmbvendeur.WillChangeToText
            AfficherDetailsVentes()
            rdbTous.Focus()
        Else
            cmbvendeur.OpenCombo()
        End If
    End Sub
End Class