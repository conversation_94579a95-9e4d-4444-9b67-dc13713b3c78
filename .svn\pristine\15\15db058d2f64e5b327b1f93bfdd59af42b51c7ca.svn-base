﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fCopieBD
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.PAnel = New System.Windows.Forms.Panel()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.dtFin = New C1.Win.C1Input.C1DateEdit()
        Me.dtDebut = New C1.Win.C1Input.C1DateEdit()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.bValider = New C1.Win.C1Input.C1Button()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.CR = New Pharma2000Premium.EtatDeReleveCNAMTemporaire()
        Me.ProgressBar = New System.Windows.Forms.ProgressBar()
        Me.PAnel.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        CType(Me.dtFin, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtDebut, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PAnel
        '
        Me.PAnel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.PAnel.Controls.Add(Me.ProgressBar)
        Me.PAnel.Controls.Add(Me.GroupBox1)
        Me.PAnel.Controls.Add(Me.bQuitter)
        Me.PAnel.Controls.Add(Me.Label1)
        Me.PAnel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.PAnel.Location = New System.Drawing.Point(0, 0)
        Me.PAnel.Name = "PAnel"
        Me.PAnel.Size = New System.Drawing.Size(624, 261)
        Me.PAnel.TabIndex = 4
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.Controls.Add(Me.dtFin)
        Me.GroupBox1.Controls.Add(Me.dtDebut)
        Me.GroupBox1.Controls.Add(Me.Label4)
        Me.GroupBox1.Controls.Add(Me.bValider)
        Me.GroupBox1.Controls.Add(Me.Label3)
        Me.GroupBox1.Location = New System.Drawing.Point(12, 68)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(603, 99)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        '
        'dtFin
        '
        Me.dtFin.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtFin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtFin.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtFin.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtFin.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtFin.Location = New System.Drawing.Point(52, 66)
        Me.dtFin.Name = "dtFin"
        Me.dtFin.Size = New System.Drawing.Size(211, 18)
        Me.dtFin.TabIndex = 1
        Me.dtFin.Tag = Nothing
        Me.dtFin.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtFin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'dtDebut
        '
        Me.dtDebut.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtDebut.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtDebut.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtDebut.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtDebut.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtDebut.Location = New System.Drawing.Point(52, 31)
        Me.dtDebut.Name = "dtDebut"
        Me.dtDebut.Size = New System.Drawing.Size(211, 18)
        Me.dtDebut.TabIndex = 0
        Me.dtDebut.Tag = Nothing
        Me.dtDebut.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtDebut.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Location = New System.Drawing.Point(23, 66)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(20, 13)
        Me.Label4.TabIndex = 32
        Me.Label4.Text = "Au"
        '
        'bValider
        '
        Me.bValider.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bValider.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bValider.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bValider.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bValider.Location = New System.Drawing.Point(450, 39)
        Me.bValider.Name = "bValider"
        Me.bValider.Size = New System.Drawing.Size(122, 45)
        Me.bValider.TabIndex = 1
        Me.bValider.Text = "Valider"
        Me.bValider.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bValider.UseVisualStyleBackColor = True
        Me.bValider.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Location = New System.Drawing.Point(23, 34)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(21, 13)
        Me.Label3.TabIndex = 3
        Me.Label3.Text = "Du"
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(462, 186)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(122, 45)
        Me.bQuitter.TabIndex = 3
        Me.bQuitter.Text = "Fermer                         F12"
        Me.bQuitter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label1
        '
        Me.Label1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label1.Font = New System.Drawing.Font("HandelGotDLig", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(12, 11)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(600, 35)
        Me.Label1.TabIndex = 4
        Me.Label1.Text = "COPIE BASE DE DONNEES"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'ProgressBar
        '
        Me.ProgressBar.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ProgressBar.Location = New System.Drawing.Point(12, 204)
        Me.ProgressBar.Name = "ProgressBar"
        Me.ProgressBar.Size = New System.Drawing.Size(418, 27)
        Me.ProgressBar.TabIndex = 73
        Me.ProgressBar.Value = 50
        Me.ProgressBar.Visible = False
        '
        'fCopieBD
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(624, 261)
        Me.Controls.Add(Me.PAnel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.SizableToolWindow
        Me.Name = "fCopieBD"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.PAnel.ResumeLayout(False)
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.dtFin, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtDebut, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents PAnel As System.Windows.Forms.Panel
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents bValider As C1.Win.C1Input.C1Button
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents dtFin As C1.Win.C1Input.C1DateEdit
    Friend WithEvents dtDebut As C1.Win.C1Input.C1DateEdit
    Friend WithEvents CR As Pharma2000Premium.EtatDeReleveCNAMTemporaire
    Friend WithEvents ProgressBar As System.Windows.Forms.ProgressBar
End Class
