﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Public Class fFamilleNonRemboursable
    Dim cmdFamilleNonRemb As New SqlCommand
    Dim daFamilleNonRemb As New SqlDataAdapter
    Dim dsFamilleNonRemb As New DataSet
    Dim cbFamilleNonRemb As New SqlCommandBuilder

    Dim CodeMutuelle As Integer = 0
    Dim CodeFamille As Integer = 0

    Public DataRow As DataRow = Nothing

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "114" And bConfirmer.Enabled = True Then
            bConfirmer_Click(sender, e)
        End If

        If argument = "123" And bAnnuler.Enabled = True Then
            bAnnuler_Click(sender, e)
        End If

    End Sub

    Public Sub init()
        Dim StrSQL As String = ""

        'charger les mutuelles

        StrSQL = "SELECT CodeMutuelle,NomMutuelle FROM MUTUELLE WHERE CodeMutuelle <> '0' ORDER BY NomMutuelle ASC"

        cmdFamilleNonRemb.Connection = ConnectionServeur
        cmdFamilleNonRemb.CommandText = StrSQL
        daFamilleNonRemb = New SqlDataAdapter(cmdFamilleNonRemb)
        daFamilleNonRemb.Fill(dsFamilleNonRemb, "MUTUELLE")
        cmbMutuelle.DataSource = dsFamilleNonRemb.Tables("MUTUELLE")
        cmbMutuelle.ValueMember = "CodeMutuelle"
        cmbMutuelle.DisplayMember = "NomMutuelle"
        cmbMutuelle.ColumnHeaders = False
        cmbMutuelle.Splits(0).DisplayColumns("CodeMutuelle").Visible = False
        cmbMutuelle.Splits(0).DisplayColumns("NomMutuelle").Width = 10
        cmbMutuelle.ExtendRightColumn = True

        'charger les Familles

        StrSQL = "SELECT CodeCategorie,LibelleCategorie FROM CATEGORIE WHERE SupprimeCategorie=0 ORDER BY LibelleCategorie ASC"

        cmdFamilleNonRemb.Connection = ConnectionServeur
        cmdFamilleNonRemb.CommandText = StrSQL
        daFamilleNonRemb = New SqlDataAdapter(cmdFamilleNonRemb)
        daFamilleNonRemb.Fill(dsFamilleNonRemb, "CATEGORIE")
        cmbFamille.DataSource = dsFamilleNonRemb.Tables("CATEGORIE")
        cmbFamille.ValueMember = "CodeCategorie"
        cmbFamille.DisplayMember = "LibelleCategorie"
        cmbFamille.ColumnHeaders = False
        cmbFamille.Splits(0).DisplayColumns("CodeCategorie").Visible = False
        cmbFamille.Splits(0).DisplayColumns("LibelleCategorie").Width = 10
        cmbFamille.ExtendRightColumn = True

        AfficherFamille(cmbMutuelle.SelectedValue)

    End Sub
    Public Sub AfficherFamille(ByVal CodeMutuelle As String)
        Dim I As Integer = 0
        Dim StrSQL As String = ""

        If (dsFamilleNonRemb.Tables.IndexOf("CATEGORIE_NON_REMBOURSABLE") > -1) Then
            dsFamilleNonRemb.Tables("CATEGORIE_NON_REMBOURSABLE").Clear()
        End If
        If (dsFamilleNonRemb.Tables.IndexOf("CATEGORIE_NON_ATTRIBUE") > -1) Then
            dsFamilleNonRemb.Tables("CATEGORIE_NON_ATTRIBUE").Clear()
        End If
        If (dsFamilleNonRemb.Tables.IndexOf("CATEGORIE") > -1) Then
            dsFamilleNonRemb.Tables("CATEGORIE").Clear()
        End If

        'charger les Familles qui ne sont pas attribuées à ce mutuelle dans le combo du recherche 
        If cmbMutuelle.Text <> "" Then
            StrSQL = "SELECT CodeCategorie,LibelleCategorie FROM CATEGORIE " + _
                " WHERE SupprimeCategorie=0 AND CodeCategorie NOT IN (SELECT CodeCategorie FROM CATEGORIE_NON_REMBOURSABLE " + _
                " WHERE CodeMutuelle ='" + cmbMutuelle.SelectedValue.ToString + "')" + _
                "ORDER BY LibelleCategorie ASC"
        Else
            StrSQL = "SELECT TOP (0) CodeCategorie,LibelleCategorie FROM CATEGORIE "
        End If
        cmdFamilleNonRemb.Connection = ConnectionServeur
        cmdFamilleNonRemb.CommandText = StrSQL
        daFamilleNonRemb = New SqlDataAdapter(cmdFamilleNonRemb)
        daFamilleNonRemb.Fill(dsFamilleNonRemb, "CATEGORIE")
        cmbFamille.DataSource = dsFamilleNonRemb.Tables("CATEGORIE")
        cmbFamille.ValueMember = "CodeCategorie"
        cmbFamille.DisplayMember = "LibelleCategorie"
        cmbFamille.ColumnHeaders = False
        cmbFamille.Splits(0).DisplayColumns("CodeCategorie").Width = 0
        cmbFamille.Splits(0).DisplayColumns("LibelleCategorie").Width = 250
        cmbFamille.Refresh()

        'charger les Familles qui ne sont pas attribuées à ce mutuelle dans la liste 

        If cmbMutuelle.Text <> "" Then
            StrSQL = "SELECT CodeCategorie,LibelleCategorie FROM CATEGORIE " + _
               " WHERE SupprimeCategorie=0 AND CodeCategorie NOT IN (SELECT CodeCategorie FROM CATEGORIE_NON_REMBOURSABLE " + _
               " WHERE CodeMutuelle ='" + cmbMutuelle.SelectedValue.ToString + "')" + _
               "ORDER BY LibelleCategorie ASC"
        Else
            StrSQL = "SELECT TOP (0) CodeCategorie,LibelleCategorie FROM CATEGORIE "
        End If
        cmdFamilleNonRemb.Connection = ConnectionServeur
        cmdFamilleNonRemb.CommandText = StrSQL
        daFamilleNonRemb = New SqlDataAdapter(cmdFamilleNonRemb)
        daFamilleNonRemb.Fill(dsFamilleNonRemb, "CATEGORIE_NON_ATTRIBUE")

        With gFamilleNonAttribue
            .Columns.Clear()
            .DataSource = dsFamilleNonRemb
            .DataMember = "CATEGORIE_NON_ATTRIBUE"
            .Rebind(False)
            .Columns("LibelleCategorie").Caption = "Nom de Famille"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("LibelleCategorie").Width = 50
            .Splits(0).DisplayColumns("LibelleCategorie").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("CodeCategorie").Visible = False
            .Splits(0).DisplayColumns("LibelleCategorie").Locked = True

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True

            .Style.Font = New Font("Microsoft Sans Serif", 8, FontStyle.Regular)
            'Style du Caractere et du grid
            ParametreGrid(gFamilleNonAttribue)
        End With

        '******************************************************************************************
        '******************************************************************************************

        'charger les Familles attribuées à ce mutuelle dans la 2 eme liste 

        cmdFamilleNonRemb.CommandText = " SELECT " + _
                                "CodeMutuelle," + _
                                "CATEGORIE_NON_REMBOURSABLE.CodeCategorie," + _
                                " CATEGORIE.LibelleCategorie " + _
                                " FROM CATEGORIE_NON_REMBOURSABLE LEFT OUTER JOIN CATEGORIE " + _
                                " ON CATEGORIE_NON_REMBOURSABLE.CodeCategorie=CATEGORIE.CodeCategorie " + _
                                " WHERE CATEGORIE_NON_REMBOURSABLE.CodeMutuelle <> '0' " + _
                                " AND CATEGORIE_NON_REMBOURSABLE.CodeMutuelle='"

        If cmbMutuelle.Text <> "" Then
            cmdFamilleNonRemb.CommandText = cmdFamilleNonRemb.CommandText + cmbMutuelle.SelectedValue.ToString + _
           "' ORDER BY LibelleCategorie"
        Else
            cmdFamilleNonRemb.CommandText = cmdFamilleNonRemb.CommandText + "' ORDER BY LibelleCategorie"
        End If

        cmdFamilleNonRemb.Connection = ConnectionServeur
        daFamilleNonRemb = New SqlDataAdapter(cmdFamilleNonRemb)
        daFamilleNonRemb.Fill(dsFamilleNonRemb, "CATEGORIE_NON_REMBOURSABLE")

        With gFamille
            .Columns.Clear()
            .DataSource = dsFamilleNonRemb
            .DataMember = "CATEGORIE_NON_REMBOURSABLE"
            .Rebind(False)
            .Columns("LibelleCategorie").Caption = "Nom de Famille"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("LibelleCategorie").Width = 50
            .Splits(0).DisplayColumns("LibelleCategorie").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("CodeMutuelle").Visible = False
            .Splits(0).DisplayColumns("CodeCategorie").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True

            .Style.Font = New Font("Microsoft Sans Serif", 8, FontStyle.Regular)
            'Style du Caractere et du grid
            ParametreGrid(gFamille)
        End With
        cbFamilleNonRemb = New SqlCommandBuilder(daFamilleNonRemb)



    End Sub

    Private Sub bAjoutertous_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjoutertous.Click

        Dim CodeCategorie As Integer = 0
        Dim LibelleCategorie As String = ""
        Dim i As Integer = 0

        For i = 0 To gFamilleNonAttribue.RowCount - 1

            If gFamilleNonAttribue(i, "CodeCategorie").ToString <> "" Then

                CodeCategorie = gFamilleNonAttribue(i, "CodeCategorie")
                LibelleCategorie = gFamilleNonAttribue(i, "LibelleCategorie")

                DataRow = dsFamilleNonRemb.Tables("CATEGORIE_NON_REMBOURSABLE").NewRow()
                DataRow("CodeMutuelle") = cmbMutuelle.SelectedValue
                DataRow("CodeCategorie") = CodeCategorie
                DataRow("LibelleCategorie") = LibelleCategorie
                dsFamilleNonRemb.Tables("CATEGORIE_NON_REMBOURSABLE").Rows.Add(DataRow)

            End If
        Next

        For i = 0 To gFamilleNonAttribue.RowCount - 1
            gFamilleNonAttribue.Delete()
        Next

    End Sub


    Private Sub bAjouterUn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterUn.Click

        Dim CodeCategorie As Integer = 0
        Dim LibelleCategorie As String = ""
        Dim i As Integer = 0


        If gFamilleNonAttribue(gFamilleNonAttribue.Row, "CodeCategorie").ToString = "" Then
            Exit Sub
        End If

        CodeCategorie = gFamilleNonAttribue(gFamilleNonAttribue.Row, "CodeCategorie")
        LibelleCategorie = gFamilleNonAttribue(gFamilleNonAttribue.Row, "LibelleCategorie")

        DataRow = dsFamilleNonRemb.Tables("CATEGORIE_NON_REMBOURSABLE").NewRow()
        DataRow("CodeMutuelle") = cmbMutuelle.SelectedValue
        DataRow("CodeCategorie") = CodeCategorie
        DataRow("LibelleCategorie") = LibelleCategorie
        dsFamilleNonRemb.Tables("CATEGORIE_NON_REMBOURSABLE").Rows.Add(DataRow)

        For i = 0 To gFamilleNonAttribue.RowCount - 1
            If gFamilleNonAttribue(gFamilleNonAttribue.Row, "CodeCategorie") = CodeCategorie Then
                gFamilleNonAttribue.Delete()
            End If
        Next

    End Sub

    Private Sub cmbMutuelle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbMutuelle.TextChanged
        AfficherFamille(cmbMutuelle.SelectedValue)
    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click

        Dim StrSQL As String = ""
        Dim StrSQLtest As String = ""
        Dim i As Integer = 0

        If cmbMutuelle.Text = "" Then
            MsgBox("Veuillez sélectionner un Mutuelle !", MsgBoxStyle.Critical, "Erreur")
            cmbMutuelle.Focus()
            Exit Sub
        End If

        cmdFamilleNonRemb.CommandText = " SELECT TOP (0) * FROM CATEGORIE_NON_REMBOURSABLE "

        cmdFamilleNonRemb.Connection = ConnectionServeur
        daFamilleNonRemb = New SqlDataAdapter(cmdFamilleNonRemb)
        daFamilleNonRemb.Fill(dsFamilleNonRemb, "CATEGORIE_NON_REMBOURSABLE")
        cbFamilleNonRemb = New SqlCommandBuilder(daFamilleNonRemb)

        Try
            daFamilleNonRemb.Update(dsFamilleNonRemb, "CATEGORIE_NON_REMBOURSABLE")
            cmbFamille.Text = ""
            AfficherFamille(cmbMutuelle.SelectedValue)
            MsgBox("Modification terminée avec succés !", MsgBoxStyle.Information, "Information")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsFamilleNonRemb.Reset()
        End Try
    End Sub

    Private Sub bSupprimerUn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerUn.Click
        Dim CodeCategorie As Integer = 0
        Dim LibelleCategorie As String = ""
        Dim i As Integer = 0

        If gFamille(gFamille.Row, "CodeCategorie").ToString = "" Then
            Exit Sub
        End If

        CodeCategorie = gFamille(gFamille.Row, "CodeCategorie")
        LibelleCategorie = gFamille(gFamille.Row, "LibelleCategorie")

        DataRow = dsFamilleNonRemb.Tables("CATEGORIE_NON_ATTRIBUE").NewRow()
        DataRow("CodeCategorie") = CodeCategorie
        DataRow("LibelleCategorie") = LibelleCategorie
        dsFamilleNonRemb.Tables("CATEGORIE_NON_ATTRIBUE").Rows.Add(DataRow)

        For i = 0 To gFamille.RowCount - 1
            If gFamille(gFamille.Row, "CodeCategorie") = CodeCategorie Then
                gFamille.Delete()
            End If
        Next
    End Sub

    Private Sub bSupprimerTous_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerTous.Click
        Dim CodeCategorie As Integer = 0
        Dim LibelleCategorie As String = ""
        Dim i As Integer = 0

        For i = 0 To gFamille.RowCount - 1

            If gFamille(i, "CodeCategorie").ToString <> "" Then

                CodeCategorie = gFamille(i, "CodeCategorie")
                LibelleCategorie = gFamille(i, "LibelleCategorie")

                DataRow = dsFamilleNonRemb.Tables("CATEGORIE_NON_ATTRIBUE").NewRow()
                DataRow("CodeCategorie") = CodeCategorie
                DataRow("LibelleCategorie") = LibelleCategorie
                dsFamilleNonRemb.Tables("CATEGORIE_NON_ATTRIBUE").Rows.Add(DataRow)

            End If
        Next

        For i = 0 To gFamille.RowCount - 1
            gFamille.Delete()
        Next
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub cmbFamille_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbFamille.KeyUp
        Dim Index As Integer = 0
        Dim i As Integer = 0

        If e.KeyCode = Keys.Enter Then
            cmbFamille.Text = cmbFamille.WillChangeToText
            For i = 0 To gFamilleNonAttribue.RowCount - 1
                If gFamilleNonAttribue(i, "LibelleCategorie") = cmbFamille.Text Then
                    Index = i
                    Exit For
                End If
            Next

            If Index <> 0 Then
                'gFamilleNonAttribue.MoveRelative(9)
                gFamilleNonAttribue.Row = Index
            End If
        Else
            cmbFamille.OpenCombo()
        End If
    End Sub

    Private Sub cmbFamille_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbFamille.TextChanged
        
    End Sub
End Class