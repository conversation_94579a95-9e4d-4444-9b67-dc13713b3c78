<?xml version="1.0"?>
<doc>
  <assembly>
    <name>C1.Win.C1Gauge.2</name>
  </assembly>
  <members>
    <member name="T:C1.Win.C1Gauge.C1Gauge">
      <summary>
            Container control for ComponentOne Gauges.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1Gauge.C1Gauge" /> control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.Dispose(System.Boolean)">
      <summary>
            Clean up any resources being used.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.Load(System.String)">
      <summary>
            Loads the contents of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> from an XML file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.Load(System.IO.Stream)">
      <summary>
            Loads the contents of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> from a <see cref="T:System.IO.Stream" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.Load(System.Xml.XmlDocument)">
      <summary>
            Loads the contents of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> from an <see cref="T:System.Xml.XmlDocument" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.Save(System.String)">
      <summary>
            Saves the contents of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> into an XML file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.Save(System.String,System.Text.Encoding)">
      <summary>
            Saves the contents of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> into an XML file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.Save(System.IO.Stream)">
      <summary>
            Saves the contents of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> into a <see cref="T:System.IO.Stream" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.Save(System.IO.Stream,System.Text.Encoding)">
      <summary>
            Saves the contents of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> into a <see cref="T:System.IO.Stream" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.Save(System.Xml.XmlWriter)">
      <summary>
            Saves the contents of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> into an <see cref="T:System.Xml.XmlWriter" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.LoadView(System.String)">
      <summary>
            Loads a View of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> from an XML file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.LoadView(System.IO.Stream)">
      <summary>
            Loads a View of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> from a <see cref="T:System.IO.Stream" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.LoadView(System.Xml.XmlDocument)">
      <summary>
            Loads a View of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> from an <see cref="T:System.Xml.XmlDocument" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.SaveView(System.String)">
      <summary>
            Saves a View of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> into an XML file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.SaveView(System.String,System.Text.Encoding)">
      <summary>
            Saves a View of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> into an XML file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.SaveView(System.IO.Stream)">
      <summary>
            Saves a View of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> into a <see cref="T:System.IO.Stream" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.SaveView(System.IO.Stream,System.Text.Encoding)">
      <summary>
            Saves a View of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> into a <see cref="T:System.IO.Stream" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.SaveView(System.Xml.XmlWriter)">
      <summary>
            Saves a View of <see cref="T:C1.Win.C1Gauge.C1Gauge" /> into an <see cref="T:System.Xml.XmlWriter" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.ShouldSerializeHotBrush">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.ShouldSerializeGauges">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.ShouldSerializeFaceShapes">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.ShouldSerializeCoverShapes">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.ShouldSerializeViewport">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.ShouldSerializeShadow">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.ShouldSerializeCommonBorders">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.ShouldSerializeCommonFillings">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.ShouldSerializeCommonFonts">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.ShouldSerializeCommonGradients">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.ShouldSerializeCommonImages">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.ShouldSerializeCommonShadows">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.ShouldSerializeCommonShapes">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnDrawFocus(C1.Win.C1Gauge.DrawFocusEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1Gauge.DrawFocus" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnItemClick(C1.Win.C1Gauge.ItemEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1Gauge.ItemClick" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnItemDoubleClick(C1.Win.C1Gauge.ItemEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1Gauge.ItemDoubleClick" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnItemMouseEnter(C1.Win.C1Gauge.ItemEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1Gauge.ItemMouseEnter" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnItemMouseLeave(C1.Win.C1Gauge.ItemEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1Gauge.ItemMouseLeave" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnItemMouseMove(C1.Win.C1Gauge.ItemMouseEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1Gauge.ItemMouseMove" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnItemMouseDown(C1.Win.C1Gauge.ItemMouseEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1Gauge.ItemMouseDown" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnItemMouseUp(C1.Win.C1Gauge.ItemMouseEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1Gauge.ItemMouseUp" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnItemStateChanged(C1.Win.C1Gauge.ItemEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1Gauge.ItemStateChanged" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.BeginUpdate">
      <summary>
            Maintains performance while multiple settings are being changed.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.EndUpdate">
      <summary>
            Resumes processing after it has been suspended by a call to <see cref="M:C1.Win.C1Gauge.C1Gauge.BeginUpdate" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.EndUpdate(System.Double)">
      <summary>
            Resumes processing after it has been suspended by a call to <see cref="M:C1.Win.C1Gauge.C1Gauge.BeginUpdate" />.
            </summary>
      <param name="duration">
            Specifies the time interval (in milliseconds) that is taken for the visual transition
            effect that hides the previous state of the control and shows the updated state.
            </param>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.InvalidateCache">
      <summary>
            Invalidates the internal cache, then redraws the <see cref="T:C1.Win.C1Gauge.C1Gauge" /> control completely.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.GetImage">
      <summary>
            Returns a "screenshot" of the <see cref="T:C1.Win.C1Gauge.C1Gauge" /> control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.GetImage(System.Int32,System.Int32)">
      <summary>
            Returns a "screenshot" of the <see cref="T:C1.Win.C1Gauge.C1Gauge" /> control as an image
            with given width and height.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.GetImage(System.Int32,System.Int32,System.Drawing.Imaging.PixelFormat)">
      <summary>
            Returns a "screenshot" of the <see cref="T:C1.Win.C1Gauge.C1Gauge" /> control as an image
            with given width, height, and pixel format.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.Reset">
      <summary>
            Removes all gauges and shapes from a <see cref="T:C1.Win.C1Gauge.C1Gauge" /> and resets
            its properties to their default values.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.GetItemAt(System.Int32,System.Int32)">
      <summary>
            Returns the topmost hit-testable Gauge item, such as a decorator
            or pointer, at the given location.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnBindingContextChanged(System.EventArgs)">
      <summary>
            Notifies Gauges about changing the binding context.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
            Raises the Paint event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnPaintBackground(System.Windows.Forms.PaintEventArgs)">
      <summary>
            Paints the background of the control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnBackgroundImageChanged(System.EventArgs)">
      <summary>
            Raises the BackgroundImageChanged event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnBackgroundImageLayoutChanged(System.EventArgs)">
      <summary>
            Raises the BackgroundImageLayoutChanged event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnMove(System.EventArgs)">
      <summary>
            Raises the Move event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnResize(System.EventArgs)">
      <summary>
            Raises the Resize event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnFontChanged(System.EventArgs)">
      <summary>
            Raises the FontChanged event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnForeColorChanged(System.EventArgs)">
      <summary>
            Raises the ForeColorChanged event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnBackColorChanged(System.EventArgs)">
      <summary>
            Raises the BackColorChanged event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnEnabledChanged(System.EventArgs)">
      <summary>
            Raises the EnabledChanged event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnVisibleChanged(System.EventArgs)">
      <summary>
            Raises the VisibleChanged event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnMouseClick(System.Windows.Forms.MouseEventArgs)">
      <summary>
            Raises the MouseClick event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnMouseDoubleClick(System.Windows.Forms.MouseEventArgs)">
      <summary>
            Raises the MouseDoubleClick event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnMouseLeave(System.EventArgs)">
      <summary>
            Raises the MouseLeave event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
      <summary>
            Raises the MouseMove event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>
            Raises the MouseDown event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
      <summary>
            Raises the MouseUp event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.IsInputKey(System.Windows.Forms.Keys)">
      <summary>
            Determines whether the specified key requires processing.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>
            Raises the KeyDown event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnGotFocus(System.EventArgs)">
      <summary>
            Raises the GotFocus event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1Gauge.OnLostFocus(System.EventArgs)">
      <summary>
            Raises the LostFocus event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.HotBrush">
      <summary>
            Specifies the properties of a brush to paint selection on the hot item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.ViewTag">
      <summary>
            Gets or sets a digital tag identifying this element's views.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.Gauges">
      <summary>
            Gets the collection of Gauges.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.FaceShapes">
      <summary>
            Gets the collection of shapes in the bottom layer (between the background and Gauges).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.CoverShapes">
      <summary>
            Gets the collection of shapes in the top layer (over everything).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.TextRenderingHint">
      <summary>
            Gets or sets the quality of text rendering.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.UseAntiAliasing">
      <summary>
            Gets or sets whether antialiasing is used when drawing Gauge elements.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.UsePixelOffset">
      <summary>
            Gets or sets whether pixels are offset during rendering.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.CacheBackground">
      <summary>
            Gets or sets whether the cache buffer is used for fixed background elements.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.CacheForeground">
      <summary>
            Gets or sets whether the cache buffer is used for fixed foreground elements.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.FramesPerSecond">
      <summary>
            Gets or sets how many times per second the <see cref="T:C1.Win.C1Gauge.C1Gauge" /> control can be repainted.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.Selectable">
      <summary>
            Gets or sets whether the <see cref="T:C1.Win.C1Gauge.C1Gauge" /> control can receive focus.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.SupportsTransitionEffect">
      <summary>
            Gets or sets whether the transition effect after a call to 'EndUpdate(duration)' is enabled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.Viewport">
      <summary>
            Specifies the bounds of the Gauge container working area.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.Shadow">
      <summary>
            Encapsulates properties of the shadow.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.CommonBorders">
      <summary>
            Gets the collection of templates for the <see cref="T:C1.Win.C1Gauge.C1GaugeBorder" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.CommonFillings">
      <summary>
            Gets the collection of templates for the <see cref="T:C1.Win.C1Gauge.C1GaugeFilling" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.CommonFonts">
      <summary>
            Gets the collection of templates for text font and color.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.CommonGradients">
      <summary>
            Gets the collection of templates for the <see cref="T:C1.Win.C1Gauge.C1GaugeGradient" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.CommonImages">
      <summary>
            Gets the collection of templates for the <see cref="T:C1.Win.C1Gauge.C1GaugeCustomImage" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.CommonShadows">
      <summary>
            Gets the collection of templates for the <see cref="T:C1.Win.C1Gauge.C1GaugeShadow" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.CommonShapes">
      <summary>
            Gets the collection of templates for the <see cref="T:C1.Win.C1Gauge.C1GaugeCustomShape" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.ImeMode">
      <summary>
            This property has no effect on C1Gauge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.Padding">
      <summary>
            This property has no effect on C1Gauge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.RightToLeft">
      <summary>
            This property has no effect on C1Gauge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1Gauge.Text">
      <summary>
            This property has no effect on C1Gauge.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1Gauge.DrawFocus">
      <summary>
            Occurs when the focus rectangle should be drawn.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1Gauge.ItemClick">
      <summary>
            Occurs when a Gauge item is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1Gauge.ItemDoubleClick">
      <summary>
            Occurs when a Gauge item is double-clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1Gauge.ItemMouseEnter">
      <summary>
            Occurs when the mouse pointer enters a Gauge item.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1Gauge.ItemMouseLeave">
      <summary>
            Occurs when the mouse pointer leaves a Gauge item.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1Gauge.ItemMouseMove">
      <summary>
            Occurs when the mouse pointer is moved over a Gauge item.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1Gauge.ItemMouseDown">
      <summary>
            Occurs when the mouse pointer is over a Gauge item and a mouse button is pressed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1Gauge.ItemMouseUp">
      <summary>
            Occurs when the mouse pointer is over a Gauge item and a mouse button is released.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1Gauge.ItemStateChanged">
      <summary>
            Occurs when the state of a Gauge item is changed.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeBase">
      <summary>
            The base class for other Gauges.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.Dispose(System.Boolean)">
      <summary>
            Clean up any resources being used.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.Load(System.String)">
      <summary>
            Loads a Gauge definition from an XML file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.Load(System.IO.Stream)">
      <summary>
            Loads a Gauge definition from a <see cref="T:System.IO.Stream" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.Save(System.String)">
      <summary>
            Saves a Gauge definition into an XML file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.Save(System.String,System.Text.Encoding)">
      <summary>
            Saves a Gauge definition into an XML file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.Save(System.IO.Stream)">
      <summary>
            Saves a Gauge definition into a <see cref="T:System.IO.Stream" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.Save(System.IO.Stream,System.Text.Encoding)">
      <summary>
            Saves a Gauge definition into a <see cref="T:System.IO.Stream" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.Load(System.Xml.XmlDocument)">
      <summary>
            Loads a Gauge from an <see cref="T:System.Xml.XmlDocument" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.Save(System.Xml.XmlWriter)">
      <summary>
            Saves a Gauge definition into an <see cref="T:System.Xml.XmlWriter" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.LoadView(System.String)">
      <summary>
            Loads a Gauge view from an XML file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.LoadView(System.IO.Stream)">
      <summary>
            Loads a Gauge view from a <see cref="T:System.IO.Stream" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.SaveView(System.String)">
      <summary>
            Saves a Gauge view into an XML file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.SaveView(System.String,System.Text.Encoding)">
      <summary>
            Saves a Gauge view into an XML file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.SaveView(System.IO.Stream)">
      <summary>
            Saves a Gauge view into a <see cref="T:System.IO.Stream" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.SaveView(System.IO.Stream,System.Text.Encoding)">
      <summary>
            Saves a Gauge view into a <see cref="T:System.IO.Stream" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.LoadView(System.Xml.XmlDocument)">
      <summary>
            Loads a Gauge view from an <see cref="T:System.Xml.XmlDocument" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.SaveView(System.Xml.XmlWriter)">
      <summary>
            Saves a Gauge view into an <see cref="T:System.Xml.XmlWriter" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializeDecorators">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializeFaceShapes">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializeCoverShapes">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializeCommonBorders">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializeCommonFillings">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializeCommonFonts">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializeCommonGradients">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializeCommonImages">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializeCommonShadows">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializeCommonShapes">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializeColorMaps">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializeImageMaps">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializeViewport">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializeMorePointers">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ShouldSerializePointer">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnPointerValueChanged(C1.Win.C1Gauge.PointerValueChangedEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.PointerValueChanged" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnValueChanged(System.EventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.ValueChanged" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnFormatLabel(C1.Win.C1Gauge.FormatLabelEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.FormatLabel" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnMouseClick(System.Windows.Forms.MouseEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.MouseClick" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnMouseDoubleClick(System.Windows.Forms.MouseEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.MouseDoubleClick" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnItemClick(C1.Win.C1Gauge.ItemEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.ItemClick" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnItemDoubleClick(C1.Win.C1Gauge.ItemEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.ItemDoubleClick" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnItemMouseEnter(C1.Win.C1Gauge.ItemEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.ItemMouseEnter" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnItemMouseLeave(C1.Win.C1Gauge.ItemEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.ItemMouseLeave" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnItemMouseMove(C1.Win.C1Gauge.ItemMouseEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.ItemMouseMove" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnItemMouseDown(C1.Win.C1Gauge.ItemMouseEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.ItemMouseDown" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnItemMouseUp(C1.Win.C1Gauge.ItemMouseEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.ItemMouseUp" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnItemStateChanged(C1.Win.C1Gauge.ItemEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.ItemStateChanged" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnPointerDragBegin(C1.Win.C1Gauge.PointerDragEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.PointerDragBegin" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnPointerDragEnd(C1.Win.C1Gauge.PointerDragEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.PointerDragEnd" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnPointerDragMove(C1.Win.C1Gauge.PointerDragEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.PointerDragMove" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.OnPointerDragCancel(C1.Win.C1Gauge.PointerDragEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Gauge.C1GaugeBase.PointerDragCancel" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.Reset">
      <summary>
            Clears all collections and resets the properties to their default values.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.GetValueAt(System.Int32,System.Int32)">
      <summary>
            Returns the Value that corresponds to the specified mouse position:
            (x, y), relative to the parent control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBase.ToString">
      <summary>
            Returns a string that represents the current object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.DataBindings">
      <summary>
            Gets a reference to the collection of data bindings for the Gauge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.BoundValue">
      <summary>
            Gets or sets the bound data value associated with the main pointer.
            </summary>
      <remarks>
            Use this property to bind the Gauge to values that can be null.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.MorePointersValue_0">
      <summary>
            Gets or sets the bound data value associated with pointer #0 from the <see cref="P:C1.Win.C1Gauge.C1GaugeBase.MorePointers" /> collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.MorePointersValue_1">
      <summary>
            Gets or sets the bound data value associated with pointer #1 from the <see cref="P:C1.Win.C1Gauge.C1GaugeBase.MorePointers" /> collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.MorePointersValue_2">
      <summary>
            Gets or sets the bound data value associated with pointer #2 from the <see cref="P:C1.Win.C1Gauge.C1GaugeBase.MorePointers" /> collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.MorePointersValue_3">
      <summary>
            Gets or sets the bound data value associated with pointer #3 from the <see cref="P:C1.Win.C1Gauge.C1GaugeBase.MorePointers" /> collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.DataSource">
      <summary>
            Gets or sets the data source object for the main pointer.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.DataField">
      <summary>
            Gets or sets the field of the data source to which the main pointer is bound.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.Owner">
      <summary>
            Gets the owner control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.IsDisposed">
      <summary>
            Gets a value indicating whether the Gauge has already been disposed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.Name">
      <summary>
            Gets or sets the unique name for this <see cref="T:C1.Win.C1Gauge.C1GaugeBase" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.ViewTag">
      <summary>
            Gets or sets a digital tag identifying this element's views.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.Bounds">
      <summary>
            Gets the size and location of the Gauge, in pixels, relative to the parent control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.Decorators">
      <summary>
            Gets the collection of the Gauge decorators.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.FaceShapes">
      <summary>
            Gets the collection of shapes in the bottom layer (between the background and the decorators).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.FaceAhead">
      <summary>
            Gets or sets whether the <see cref="P:C1.Win.C1Gauge.C1GaugeBase.FaceShapes" /> should appear in front of the other Gauges.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.CoverShapes">
      <summary>
            Gets the collection of shapes in the top layer (over everything).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.CommonBorders">
      <summary>
            Gets the collection of templates for the <see cref="T:C1.Win.C1Gauge.C1GaugeBorder" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.CommonFillings">
      <summary>
            Gets the collection of templates for the <see cref="T:C1.Win.C1Gauge.C1GaugeFilling" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.CommonFonts">
      <summary>
            Gets the collection of templates for text font and color.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.CommonGradients">
      <summary>
            Gets the collection of templates for the <see cref="T:C1.Win.C1Gauge.C1GaugeGradient" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.CommonImages">
      <summary>
            Gets the collection of templates for the <see cref="T:C1.Win.C1Gauge.C1GaugeCustomImage" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.CommonShadows">
      <summary>
            Gets the collection of templates for the <see cref="T:C1.Win.C1Gauge.C1GaugeShadow" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.CommonShapes">
      <summary>
            Gets the collection of templates for the <see cref="T:C1.Win.C1Gauge.C1GaugeCustomShape" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.ColorMaps">
      <summary>
            Gets the collection of templates for value colors.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.ImageMaps">
      <summary>
            Gets the collection of templates for value images.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.Viewport">
      <summary>
            Specifies the bounds of the Gauge working area.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.MorePointers">
      <summary>
            Gets the collection of the other pointers, except the main pointer.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.Pointer">
      <summary>
            Gets the main Gauge pointer.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.Value">
      <summary>
            Gets or sets the current value of the Gauge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.Minimum">
      <summary>
            Minimum value for the Gauge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.Maximum">
      <summary>
            Maximum value for the Gauge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.SlidingScale">
      <summary>
            Optimizes painting for the case if the <see cref="P:C1.Win.C1Gauge.C1GaugeBase.Minimum" /> and <see cref="P:C1.Win.C1Gauge.C1GaugeBase.Maximum" /> properties are variable.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.IsLogarithmic">
      <summary>
            Gets or sets if it uses a logarithmic scale. When true, uses the LogarithmicBase.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.LogarithmicBase">
      <summary>
            Gets or sets the log base used when the IsLogarithmic is set to True.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.IsReversed">
      <summary>
            Gets or sets whether scale values should appear in reversed order (from Maximum to Minimum).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.Visible">
      <summary>
            Gets or sets whether the Gauge is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBase.Enabled">
      <summary>
            Gets or sets whether the Gauge is enabled.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.PointerValueChanged">
      <summary>
            Occurs when the <see cref="P:C1.Win.C1Gauge.C1GaugePointer.Value" /> property changes for some Gauge pointer.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.ValueChanged">
      <summary>
            Occurs when the <see cref="P:C1.Win.C1Gauge.C1GaugeBase.Value" /> property changes for the main pointer.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.FormatLabel">
      <summary>
            Occurs when <see cref="T:C1.Win.C1Gauge.C1GaugeLabels" /> or <see cref="T:C1.Win.C1Gauge.C1GaugeSingleLabel" /> converts a value to the string representation.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.MouseClick">
      <summary>
            Occurs when the Gauge is clicked by the mouse.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.MouseDoubleClick">
      <summary>
            Occurs when the Gauge is double clicked by the mouse.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.ItemClick">
      <summary>
            Occurs when a Gauge item is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.ItemDoubleClick">
      <summary>
            Occurs when a Gauge item is double-clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.ItemMouseEnter">
      <summary>
            Occurs when the mouse pointer enters a Gauge item.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.ItemMouseLeave">
      <summary>
            Occurs when the mouse pointer leaves a Gauge item.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.ItemMouseMove">
      <summary>
            Occurs when the mouse pointer is moved over a Gauge item.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.ItemMouseDown">
      <summary>
            Occurs when the mouse pointer is over a Gauge item and a mouse button is pressed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.ItemMouseUp">
      <summary>
            Occurs when the mouse pointer is over a Gauge item and a mouse button is released.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.ItemStateChanged">
      <summary>
            Occurs when the state of a Gauge item is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.PointerDragBegin">
      <summary>
            Occurs when the user starts dragging a Gauge pointer.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.PointerDragEnd">
      <summary>
            Occurs when the user ends dragging a Gauge pointer.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.PointerDragMove">
      <summary>
            Occurs when a Gauge pointer is dragged with the mouse.
            </summary>
    </member>
    <member name="E:C1.Win.C1Gauge.C1GaugeBase.PointerDragCancel">
      <summary>
            Occurs if the user cancels dragging a Gauge pointer by pressing the Escape key.
            </summary>
      <remarks>
            This event is supported if the <see cref="P:C1.Win.C1Gauge.C1Gauge.Selectable" /> property is True.
            </remarks>
    </member>
    <member name="T:C1.Win.C1Gauge.C1LinearGauge">
      <summary>
            Control that shows a linear Gauge.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1LinearGauge.#ctor">
      <summary>
            Initializes a new instance of a <see cref="T:C1.Win.C1Gauge.C1LinearGauge" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1LinearGauge.AxisStartCoordinate">
      <summary>
            Gets the x-coordinate (for horizontal Gauge) or y-coordinate
            (for vertical Gauge) of the position where the longitudinal axis
            starts, in pixels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1LinearGauge.AxisLengthInPixels">
      <summary>
            Gets the length of the longitudinal axis, in pixels.
            </summary>
      <remarks>
            Negative value corresponds to the inverted direction of the longitudinal axis.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1LinearGauge.BaseOriginCoordinate">
      <summary>
            Gets the y-coordinate (for horizontal Gauge) or x-coordinate
            (for vertical Gauge) of the location where the transversal axis
            starts, in pixels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1LinearGauge.BaseFactorInPixels">
      <summary>
            Gets the length of the transversal axis, in pixels.
            </summary>
      <remarks>
            Negative value corresponds to the inverted direction of the transversal axis.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1LinearGauge.Orientation">
      <summary>
            Orientation of a <see cref="T:C1.Win.C1Gauge.C1LinearGauge" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1LinearGauge.AxisStart">
      <summary>
            Gets or sets the fraction-based position where the longitudinal axis starts.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1LinearGauge.AxisLength">
      <summary>
            Gets or sets the fraction-based length of the longitudinal axis.
            </summary>
      <remarks>
            0 represents 0, and 1 represents the length of the <see cref="T:C1.Win.C1Gauge.C1LinearGauge" />.
            Negative value inverts the direction of the longitudinal axis.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1LinearGauge.OffPosition">
      <summary>
            Gets or sets the fraction-based position used when the <see cref="P:C1.Win.C1Gauge.C1GaugeBase.Value" /> is set to Double.NaN.
            </summary>
      <remarks>
            Set to Double.NaN to hide the pointer.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1LinearGauge.BaseOrigin">
      <summary>
            Gets or sets the fraction-based location where the transversal axis starts.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1LinearGauge.BaseFactor">
      <summary>
            Gets or sets the fraction-based length of the transversal axis.
            </summary>
      <remarks>
            1 represents the whole height (for a horizontal Gauge) or width
            (for a vertical Gauge). 0 represents 0.
            Negative value inverts the direction of the transversal axis.
            </remarks>
    </member>
    <member name="T:C1.Win.C1Gauge.C1RadialGauge">
      <summary>
            Control that shows a radial Gauge.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1RadialGauge.#ctor">
      <summary>
            Initializes a new instance of a <see cref="T:C1.Win.C1Gauge.C1RadialGauge" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1RadialGauge.Dispose(System.Boolean)">
      <summary>
            Clean up any resources being used.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1RadialGauge.ShouldSerializeCap">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1RadialGauge.RadiusInPixels">
      <summary>
            Gets the radius of a <see cref="T:C1.Win.C1Gauge.C1RadialGauge" />, in pixels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1RadialGauge.PointerOriginXCoordinate">
      <summary>
            Gets the x-coordinate of the pointer origin, in pixels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1RadialGauge.PointerOriginYCoordinate">
      <summary>
            Gets the y-coordinate of the pointer origin, in pixels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1RadialGauge.Radius">
      <summary>
            Gets or sets the fraction-based radius of a <see cref="T:C1.Win.C1Gauge.C1RadialGauge" />.
            </summary>
      <remarks>
            1 represents the Min(Width, Height) of the Gauge. 0 represents 0.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1RadialGauge.PointerOriginX">
      <summary>
            Fraction-based X coordinate of the pointer origin of a <see cref="T:C1.Win.C1Gauge.C1RadialGauge" />.
            </summary>
      <remarks>
            The Point (0, 0) corresponds to the top-left corner of the Gauge's viewport
            and the Point (0.5, 0.5) is the center of the viewport.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1RadialGauge.PointerOriginY">
      <summary>
            Fraction-based Y coordinate of the pointer origin of a <see cref="T:C1.Win.C1Gauge.C1RadialGauge" />.
            </summary>
      <remarks>
            The Point (0, 0) corresponds to the top-left corner of the Gauge's viewport
            and the Point (0.5, 0.5) is the center of the viewport.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1RadialGauge.Cap">
      <summary>
            Gets the Gauge pointer cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1RadialGauge.StartAngle">
      <summary>
            Start angle for the <see cref="T:C1.Win.C1Gauge.C1RadialGauge" /> axis. 0 is the topmost point of the circumference.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1RadialGauge.SweepAngle">
      <summary>
            Sweep angle for the <see cref="T:C1.Win.C1Gauge.C1RadialGauge" /> values. The Maximum value will be placed at StartAngle + SweepAngle
            unless the scale is reversed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1RadialGauge.OffAngle">
      <summary>
            Gets or sets the angle of the pointer when the <see cref="P:C1.Win.C1Gauge.C1GaugeBase.Value" /> value is set to Double.NaN.
            </summary>
      <remarks>
            Set to Double.NaN to hide the pointer.
            </remarks>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeAlignment">
      <summary>
            Aligment possibilities for a decorator (relative to the Gauge's axis).
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeAlignment.In">
      <summary>
            The decorator is located inside the axis.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeAlignment.Out">
      <summary>
            The decorator is located outside the axis.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeAlignment.Center">
      <summary>
            The decorator is located centered in a the axis.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeBorderStyle">
      <summary>
            Specifies the style of lines drawn with a <see cref="T:C1.Win.C1Gauge.C1GaugeBorder" />.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeBorderStyle.None">
      <summary>
            Hides the border.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeBorderStyle.Solid">
      <summary>
            Specifies a solid line.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeBorderStyle.Dash">
      <summary>
            Specifies a line consisting of dashes.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeBorderStyle.Dot">
      <summary>
            Specifies a line consisting of dots.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeBorderStyle.DashDot">
      <summary>
            Specifies a line consisting of a repeating pattern of dash-dot.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeBorderStyle.DashDotDot">
      <summary>
            Specifies a line consisting of a repeating pattern of dash-dot-dot.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeBrushType">
      <summary>
            The type of brush that is used to fill a Gauge element. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeBrushType.None">
      <summary>
            Don't fill the Gauge element.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeBrushType.SolidColor">
      <summary>
            Use a brush of a single color.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeBrushType.Gradient">
      <summary>
            Use a brush with color gradient.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeBrushType.Hatch">
      <summary>
            Use a brush with a hatch style.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeBrushType.Texture">
      <summary>
            Use an image to fill interior of a Gauge element.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeClipOperation">
      <summary>
            Specifies how the source and given clipping regions can be combined.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeClipOperation.None">
      <summary>
            The source region remains unchanged.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeClipOperation.Replace">
      <summary>
            The source clipping region is replaced by the given region.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeClipOperation.Intersect">
      <summary>
            Two clipping regions are combined by taking their intersection.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeClipOperation.Union">
      <summary>
            Two clipping regions are combined by taking the union of both.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeClipOperation.Xor">
      <summary>
            Two clipping regions are combined by taking only the areas enclosed by one or the other region, but not both.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeClipOperation.Exclude">
      <summary>
            Specifies that the source region is replaced by the result of the given region being removed from the source region.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeClipOperation.Complement">
      <summary>
            Specifies that the source region is replaced by the result of the source region being removed from the given region.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeRangeAntiAliasing">
      <summary>
            Specifies the anti-aliasing mode for the internal filling of a <see cref="T:C1.Win.C1Gauge.C1GaugeRange" />.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRangeAntiAliasing.None">
      <summary>
            The special anti-aliasing is not applied to the range's filling.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRangeAntiAliasing.LowQuality">
      <summary>
            Applies the fast low-quality anti-aliasing to the range's filling.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRangeAntiAliasing.HighQuality">
      <summary>
            Applies the slow but high-quality anti-aliasing to the range's filling.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeFlipType">
      <summary>
            Specifies the axis used to flip the image.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeFlipType.FlipNone">
      <summary>
            Specifies no flipping.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeFlipType.FlipX">
      <summary>
            Specifies a horizontal flip.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeFlipType.FlipY">
      <summary>
            Specifies a vertical flip.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeFlipType.FlipXY">
      <summary>
            Specifies a horizontal and vertical flip.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeGradientDirection">
      <summary>
            The set of possible gradient directions.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeGradientDirection.Horizontal">
      <summary>
            Specifies a gradient from left to right.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeGradientDirection.Vertical">
      <summary>
            Specifies a gradient from top to bottom.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeGradientDirection.ForwardDiagonal">
      <summary>
            Specifies a gradient from upper left to lower right.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeGradientDirection.BackwardDiagonal">
      <summary>
            Specifies a gradient from upper right to lower left.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeGradientDirection.RadialInner">
      <summary>
            Specifies a gradient from center radially (inscribed ellipse).
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeGradientDirection.RadialOuter">
      <summary>
            Specifies a gradient from center radially (circumellipse).
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeGradientDirection.CornerRay">
      <summary>
            Specifies a gradient from center to four corners.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeGradientFalloff">
      <summary>
            Defines the type of a gradient falloff.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeGradientFalloff.Plain">
      <summary>
            Creates an even linear blend from the starting color to the ending color.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeGradientFalloff.Triangular">
      <summary>
            Creates a gradient with a center color and a linear falloff to a single color on both ends.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeGradientFalloff.SigmaBell">
      <summary>
            Creates a gradient falloff based on a bell-shaped curve.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeHatchStyle">
      <summary>
            Specifies the different patterns available for the hatch brush.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Horizontal">
      <summary>
            A pattern of horizontal lines.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Vertical">
      <summary>
            A pattern of vertical lines.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.ForwardDiagonal">
      <summary>
            A pattern of lines on a diagonal from upper left to lower right.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.BackwardDiagonal">
      <summary>
            A pattern of lines on a diagonal from upper right to lower left.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Cross">
      <summary>
            Specifies horizontal and vertical lines that cross.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.DiagonalCross">
      <summary>
            A pattern of crisscross diagonal lines.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Percent05">
      <summary>
            Specifies a 5-percent hatch. The ratio of foreground color to background
            color is 5:100.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Percent10">
      <summary>
            Specifies a 10-percent hatch. The ratio of foreground color to background
            color is 10:100.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Percent20">
      <summary>
            Specifies a 20-percent hatch. The ratio of foreground color to background
            color is 20:100.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Percent25">
      <summary>
            Specifies a 25-percent hatch. The ratio of foreground color to background
            color is 25:100.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Percent30">
      <summary>
            Specifies a 30-percent hatch. The ratio of foreground color to background
            color is 30:100.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Percent40">
      <summary>
            Specifies a 40-percent hatch. The ratio of foreground color to background
            color is 40:100.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Percent50">
      <summary>
            Specifies a 50-percent hatch. The ratio of foreground color to background
            color is 50:100.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Percent60">
      <summary>
            Specifies a 60-percent hatch. The ratio of foreground color to background
            color is 60:100.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Percent70">
      <summary>
            Specifies a 70-percent hatch. The ratio of foreground color to background
            color is 70:100.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Percent75">
      <summary>
            Specifies a 75-percent hatch. The ratio of foreground color to background
            color is 75:100.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Percent80">
      <summary>
            Specifies a 80-percent hatch. The ratio of foreground color to background
            color is 80:100.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Percent90">
      <summary>
            Specifies a 90-percent hatch. The ratio of foreground color to background
            color is 90:100.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.LightDownwardDiagonal">
      <summary>
            Specifies diagonal lines that slant to the right from top points to bottom
            points and are spaced 50 percent closer together than ForwardDiagonal,
            but are not antialiased.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.LightUpwardDiagonal">
      <summary>
            Specifies diagonal lines that slant to the left from top points to bottom
            points and are spaced 50 percent closer together than BackwardDiagonal,
            but they are not antialiased.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.DarkDownwardDiagonal">
      <summary>
            Specifies diagonal lines that slant to the right from top points to bottom
            points, are spaced 50 percent closer together than, and are twice the width
            of ForwardDiagonal. This hatch pattern is not antialiased.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.DarkUpwardDiagonal">
      <summary>
            Specifies diagonal lines that slant to the left from top points to bottom
            points, are spaced 50 percent closer together than BackwardDiagonal,
            and are twice its width, but the lines are not antialiased.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.WideDownwardDiagonal">
      <summary>
            Specifies diagonal lines that slant to the right from top points to bottom
            points, have the same spacing as hatch style ForwardDiagonal,
            and are triple its width, but are not antialiased.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.WideUpwardDiagonal">
      <summary>
            Specifies diagonal lines that slant to the left from top points to bottom
            points, have the same spacing as hatch style BackwardDiagonal,
            and are triple its width, but are not antialiased.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.LightVertical">
      <summary>
            Specifies vertical lines that are spaced 50 percent closer together than Vertical.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.LightHorizontal">
      <summary>
            Specifies horizontal lines that are spaced 50 percent closer together than Horizontal.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.NarrowVertical">
      <summary>
            Specifies vertical lines that are spaced 75 percent closer together than
            hatch style Vertical (or 25 percent closer together than LightVertical).
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.NarrowHorizontal">
      <summary>
            Specifies horizontal lines that are spaced 75 percent closer together than
            hatch style Horizontal (or 25 percent closer together than LightHorizontal).
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.DarkVertical">
      <summary>
            Specifies vertical lines that are spaced 50 percent closer together than
            Vertical and are twice its width.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.DarkHorizontal">
      <summary>
            Specifies horizontal lines that are spaced 50 percent closer together than
            Horizontal and are twice the width of Horizontal.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.DashedDownwardDiagonal">
      <summary>
            Specifies dashed diagonal lines, that slant to the right from top points
            to bottom points.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.DashedUpwardDiagonal">
      <summary>
            Specifies dashed diagonal lines, that slant to the left from top points to
            bottom points.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.DashedHorizontal">
      <summary>
            Specifies dashed horizontal lines.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.DashedVertical">
      <summary>
            Specifies dashed vertical lines.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.SmallConfetti">
      <summary>
            Specifies a hatch that has the appearance of confetti.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.LargeConfetti">
      <summary>
            Specifies a hatch that has the appearance of confetti, and is composed of
            larger pieces than SmallConfetti.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.ZigZag">
      <summary>
            Specifies horizontal lines that are composed of zigzags.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Wave">
      <summary>
            Specifies horizontal lines that are composed of tildes.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.DiagonalBrick">
      <summary>
            Specifies a hatch that has the appearance of layered bricks that slant to
            the left from top points to bottom points.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.HorizontalBrick">
      <summary>
            Specifies a hatch that has the appearance of horizontally layered bricks.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Weave">
      <summary>
            Specifies a hatch that has the appearance of a woven material.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Plaid">
      <summary>
            Specifies a hatch that has the appearance of a plaid material.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Divot">
      <summary>
            Specifies a hatch that has the appearance of divots.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.DottedGrid">
      <summary>
            Specifies horizontal and vertical lines, each of which is composed of dots,
            that cross.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.DottedDiamond">
      <summary>
            Specifies forward diagonal and backward diagonal lines, each of which is
            composed of dots, that cross.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Shingle">
      <summary>
            Specifies a hatch that has the appearance of diagonally layered shingles
            that slant to the right from top points to bottom points.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Trellis">
      <summary>
            Specifies a hatch that has the appearance of a trellis.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.Sphere">
      <summary>
            Specifies a hatch that has the appearance of spheres laid adjacent to one
            another.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.SmallGrid">
      <summary>
            Specifies horizontal and vertical lines that cross and are spaced 50 percent
            closer together than hatch style Cross.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.SmallCheckerBoard">
      <summary>
            Specifies a hatch that has the appearance of a checkerboard.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.LargeCheckerBoard">
      <summary>
            Specifies a hatch that has the appearance of a checkerboard with squares
            that are twice the size of SmallCheckerBoard.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.OutlinedDiamond">
      <summary>
            Specifies forward diagonal and backward diagonal lines that cross but are
            not antialiased.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeHatchStyle.SolidDiamond">
      <summary>
            Specifies a hatch that has the appearance of a checkerboard placed diagonally.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeMarkShape">
      <summary>
            The set of standard tick mark shapes.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeMarkShape.Custom">
      <summary>
            Use the custom shape for tick marks.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeMarkShape.Rectangle">
      <summary>
            Display a tick mark as rectangle.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeMarkShape.Round">
      <summary>
            Display a tick mark as circle or round rectangle.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeMarkShape.Triangle">
      <summary>
            Display a tick mark as triangle.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeOrientation">
      <summary>
            Specifies the orientation of a <see cref="T:C1.Win.C1Gauge.C1LinearGauge" />.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeOrientation.Horizontal">
      <summary>
            The gauge is oriented horizontally.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeOrientation.Vertical">
      <summary>
            The gauge is oriented vertically.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugePointerShape">
      <summary>
            The set of standard pointer shapes.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugePointerShape.Custom">
      <summary>
            Use the custom shape for the pointer.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugePointerShape.Arrow1">
      <summary>
            Display pointer as arrow.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugePointerShape.Arrow2">
      <summary>
            Display pointer as arrow.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugePointerShape.Arrow3">
      <summary>
            Display pointer as arrow.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugePointerShape.Rectangle">
      <summary>
            Display pointer as rectangle.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugePointerShape.Round">
      <summary>
            Display pointer as circle or round rectangle.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugePointerShape.Thumb">
      <summary>
            Display a thumb for the linear pointer.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugePointerShape.Triangle">
      <summary>
            Display pointer as triangle.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeRotateFlipType">
      <summary>
            Specifies the direction of an image's rotation and the axis used to flip the image.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.RotateNoneFlipNone">
      <summary>
            Specifies no rotation and no flipping.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.Rotate90FlipNone">
      <summary>
            Specifies a 90-degree rotation without flipping.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.Rotate180FlipNone">
      <summary>
            Specifies a 180-degree rotation without flipping.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.Rotate270FlipNone">
      <summary>
            Specifies a 270-degree rotation without flipping.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.RotateNoneFlipX">
      <summary>
            Specifies no rotation followed by a horizontal flip.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.Rotate90FlipX">
      <summary>
            Specifies a 90-degree rotation followed by a horizontal flip.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.Rotate180FlipX">
      <summary>
            Specifies a 180-degree rotation followed by a horizontal flip.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.Rotate270FlipX">
      <summary>
            Specifies a 270-degree rotation followed by a horizontal flip.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.RotateNoneFlipY">
      <summary>
            Specifies no rotation followed by a vertical flip.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.Rotate90FlipY">
      <summary>
            Specifies a 90-degree rotation followed by a vertical flip.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.Rotate180FlipY">
      <summary>
            Specifies a 180-degree rotation followed by a vertical flip.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.Rotate270FlipY">
      <summary>
            Specifies a 270-degree rotation followed by a vertical flip.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.RotateNoneFlipXY">
      <summary>
            Specifies no rotation followed by a horizontal and vertical flip.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.Rotate90FlipXY">
      <summary>
            Specifies a 90-degree rotation followed by a horizontal and vertical flip.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.Rotate180FlipXY">
      <summary>
            Specifies a 180-degree rotation followed by a horizontal and vertical flip.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeRotateFlipType.Rotate270FlipXY">
      <summary>
            Specifies a 270-degree rotation followed by a horizontal and vertical flip.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeValueColorFalloff">
      <summary>
            Specifies the blending mode for value Colors on a <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" />.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeValueColorFalloff.None">
      <summary>
            Display the same color till the next color position starts.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeValueColorFalloff.Linear">
      <summary>
            Display a linear falloff between the previous and the next color positions.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeValueColorFalloff.Smooth">
      <summary>
            Display a smooth falloff between the previous and the next color positions.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeValueColorFalloff.Sharp">
      <summary>
            Display a sharp falloff between the previous and the next color positions.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeImageLayout">
      <summary>
            Specifies the position of the background image.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeImageLayout.None">
      <summary>
            The image is left-aligned at the top across the control's client rectangle.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeImageLayout.Tile">
      <summary>
            The image is tiled across the control's client rectangle.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeImageLayout.Center">
      <summary>
            The image is centered within the control's client rectangle.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeImageLayout.Stretch">
      <summary>
            The image is streched across the control's client rectangle.
            </summary>
    </member>
    <member name="F:C1.Win.C1Gauge.C1GaugeImageLayout.Zoom">
      <summary>
            The image is enlarged within the control's client rectangle.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.FormatLabelEventHandler">
      <summary>
            Represents the method that handles the FormatLabel event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A FormatLabelEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Gauge.FormatLabelEventArgs">
      <summary>
            Provides data for the FormatLabel event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.FormatLabelEventArgs.Decorator">
      <summary>
            Gets the <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.FormatLabelEventArgs.Value">
      <summary>
            Gets the source value displayed in the label.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.FormatLabelEventArgs.ScaledValue">
      <summary>
            Gets the source value scaled by the ValueFactor and ValueOffset.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.FormatLabelEventArgs.Text">
      <summary>
            Gets or sets the text displayed in the label.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.PointerValueChangedEventHandler">
      <summary>
            Represents the method that handles the PointerValueChanged event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A PointerValueChangedEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Gauge.PointerValueChangedEventArgs">
      <summary>
            Provides data for the PointerValueChanged event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.PointerValueChangedEventArgs.Pointer">
      <summary>
            Gets the Gauge pointer which value has been changed.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.DrawFocusEventHandler">
      <summary>
            Represents a method that handles the DrawFocus event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A DrawFocusEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Gauge.DrawFocusEventArgs">
      <summary>
            Provides data for the DrawFocus event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.DrawFocusEventArgs.Graphics">
      <summary>
            Gets the graphics used to paint the focus rectangle.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.DrawFocusEventArgs.FocusRectangle">
      <summary>
            Gets or sets the bounds of the focus rectangle.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.DrawFocusEventArgs.Handled">
      <summary>
            Gets or sets whether the focus rectangle was drawn from user code.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.ItemEventHandler">
      <summary>
            Represents a method that handles events occuring for the Gauge items.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">An ItemEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Gauge.ItemEventArgs">
      <summary>
            Provides data for events occuring for the Gauge items, such as <see cref="T:C1.Win.C1Gauge.C1GaugePointer" />,
            <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" />, and others.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ItemEventArgs.Gauge">
      <summary>
            Gets the owner <see cref="T:C1.Win.C1Gauge.C1GaugeBase" /> for the item causing the event.
            </summary>
      <remarks>
            It is null (Nothing in VB) for shapes which belong to the main <see cref="T:C1.Win.C1Gauge.C1Gauge" /> control.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.ItemEventArgs.Container">
      <summary>
            Gets the owner <see cref="T:C1.Win.C1Gauge.C1Gauge" /> control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ItemEventArgs.Item">
      <summary>
            Gets the item causing the event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ItemEventArgs.ItemEnabled">
      <summary>
            Indicates whether the item can be hit-tested.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ItemEventArgs.ItemPressed">
      <summary>
            Indicates whether the item is in 'pressed' state.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ItemEventArgs.ItemHot">
      <summary>
            Indicates whether the mouse pointer stays over the item.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.ItemMouseEventHandler">
      <summary>
            Represents a method that handles mouse events occuring for the Gauge items.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">An ItemMouseEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Gauge.ItemMouseEventArgs">
      <summary>
            Provides data for mouse events occuring for the Gauge items.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ItemMouseEventArgs.Button">
      <summary>
            Gets which mouse button was pressed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ItemMouseEventArgs.Clicks">
      <summary>
            Gets the number of times the mouse button was pressed or released.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ItemMouseEventArgs.Location">
      <summary>
            Gets the location of the mouse during the generating mouse event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ItemMouseEventArgs.X">
      <summary>
            Gets the x-coordinate of the mouse, in pixels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ItemMouseEventArgs.Y">
      <summary>
            Gets the y-coordinate of the mouse, in pixels.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.PointerDragEventHandler">
      <summary>
            Represents a method that handles events while the Gauge pointer is dragged.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A PointerDragEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Gauge.PointerDragEventArgs">
      <summary>
            Provides data for events occuring while the Gauge pointer is dragged.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.PointerDragEventArgs.Pointer">
      <summary>
            Gets the Gauge pointer being dragged.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.PointerDragEventArgs.NewValue">
      <summary>
            Gets the suggested new value for the pointer.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeBorder">
      <summary>
            Encapsulates properties of a pen used to draw borders of Gauge elements.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBorder.ShouldSerializeColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBorder.ResetColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBorder.CommonBorderName">
      <summary>
            Gets or sets the name of the border template.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBorder.LineStyle">
      <summary>
            Gets or sets the style of lines drawn with a border.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBorder.Thickness">
      <summary>
            Gets or sets the logical thickness of the border. Negative value
            specifies the width of the border in pixels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBorder.Color">
      <summary>
            Gets or sets the Color of the border.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeCap">
      <summary>
            Defines properties of the Gauge pointer cap.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCap.ShouldSerializeBorder">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCap.ResetBorder">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCap.ShouldSerializeClippings">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCap.ShouldSerializeFilling">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCap.ResetFilling">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCap.ShouldSerializeGradient">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCap.ResetGradient">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCap.ShouldSerializeMoreCircles">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCap.ShouldSerializeCustomImage">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCap.ResetCustomImage">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCap.ShouldSerializeShadow">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCap.ResetShadow">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCap.ToString">
      <summary>
            Returns a string that represents the current object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCap.Owner">
      <summary>
            Gets the owner Gauge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCap.HitTestable">
      <summary>
            Gets or sets whether the pointer cap can be hit-tested.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCap.Radius">
      <summary>
            Gets or sets the logical radius of the pointer cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCap.Border">
      <summary>
            Gets the properties of the pointer cap border.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCap.Clippings">
      <summary>
            Gets the collection of shapes that form the clipping region.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCap.Filling">
      <summary>
            Adjusts the interior filling of the pointer cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCap.Gradient">
      <summary>
            Encapsulates properties of a color gradient.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCap.MoreCircles">
      <summary>
            Gets the collection of circles for the pointer cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCap.CustomImage">
      <summary>
            Gets or sets the custom image to draw on the pointer cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCap.Shadow">
      <summary>
            Encapsulates properties of the pointer cap shadow.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCap.BehindPointers">
      <summary>
            Gets or sets whether the pointer cap should appear behind the Gauge pointers.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCap.Visible">
      <summary>
            Gets or sets whether the pointer cap is visible.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeCapCircle">
      <summary>
            Interior filling for the pointer cap.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCapCircle.#ctor">
      <summary>
            Initializes a new instance of the circle.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCapCircle.ShouldSerializeFilling">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCapCircle.ResetFilling">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCapCircle.ShouldSerializeGradient">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCapCircle.ResetGradient">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCapCircle.Owner">
      <summary>
            Gets the owner <see cref="T:C1.Win.C1Gauge.C1GaugeCap" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCapCircle.Radius">
      <summary>
            Gets or sets the circle radius in logical coordinates.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCapCircle.Filling">
      <summary>
            Adjusts the interior filling of the circle.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCapCircle.Gradient">
      <summary>
            Encapsulates properties of a color gradient.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeClipping">
      <summary>
            Associates a clipping region with a Gauge element.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeClipping.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1Gauge.C1GaugeClipping" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeClipping.#ctor(System.String,C1.Win.C1Gauge.C1GaugeClipOperation,System.Double)">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1Gauge.C1GaugeClipping" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeClipping.ToString">
      <summary>
            Returns a String that represents the current <see cref="T:C1.Win.C1Gauge.C1GaugeClipping" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeClipping.Owner">
      <summary>
            Gets the owner Gauge element.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeClipping.ShapeName">
      <summary>
            Gets or sets the name of a Shape that is used for clipping.
            </summary>
      <remarks>
            An empty string here makes reference to the owner element.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeClipping.Operation">
      <summary>
            Specifies how the source and given clipping regions are combined.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeClipping.ScaleFactor">
      <summary>
            Gets or sets the scale factor for the given Shape when it is used for clipping.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeCustomImage">
      <summary>
            Properties of the user-defined image displayed on a Gauge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomImage.CommonImageName">
      <summary>
            Gets or sets the name of the image template.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomImage.Width">
      <summary>
            Gets or sets the logical value representing the width of the image.
            </summary>
      <remarks>
            Can be set to double.NaN (default value) to avoid scaling the image in the X-axis direction.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomImage.Height">
      <summary>
            Gets or sets the logical value representing the height of the image.
            </summary>
      <remarks>
            Can be set to double.NaN (default value) to avoid scaling the image in the Y-axis direction.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomImage.Image">
      <summary>
            Gets or sets the original image.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomImage.KeepAspectRatio">
      <summary>
            Gets or sets whether the image aspect ratio should be maintained.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomImage.KeepSize">
      <summary>
            Gets or sets whether the predefined size of the image should not changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomImage.RotateFlipType">
      <summary>
            Gets or sets the direction of an image's rotation and the axis used to flip the image.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomImage.Hue">
      <summary>
            Allows to modify the image hue by the specified amount (between -180 and 180).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomImage.Saturation">
      <summary>
            Allows to modify the image saturation by the specified amount (between -100 and 100).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomImage.Lightness">
      <summary>
            Allows to modify the image lightness by the specified amount (between -100 and 100).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomImage.Opacity">
      <summary>
            Gets or sets the opacity of the image between 0.0 (completely invisible) and 1.0 (opaque).
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeCustomShape">
      <summary>
            Defines properties of a shape used for Gauge marks and pointers.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ShouldSerializeScaleStartRadius">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ResetScaleStartRadius">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ShouldSerializeScaleEndRadius">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ResetScaleEndRadius">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ShouldSerializeStartAngle">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ResetStartAngle">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ShouldSerializeStartRadius">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ResetStartRadius">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ShouldSerializeStartWidth">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ResetStartWidth">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ShouldSerializeEndAngle">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ResetEndAngle">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ShouldSerializeEndRadius">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ResetEndRadius">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ShouldSerializeEndWidth">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ResetEndWidth">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ShouldSerializeStartSwellAngle">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ResetStartSwellAngle">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ShouldSerializeEndSwellAngle">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ResetEndSwellAngle">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ShouldSerializeStartSwellLength">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ResetStartSwellLength">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ShouldSerializeEndSwellLength">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ResetEndSwellLength">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ShouldSerializeStartSwellWidth">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ResetStartSwellWidth">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ShouldSerializeEndSwellWidth">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCustomShape.ResetEndSwellWidth">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.CommonShapeName">
      <summary>
            Gets or sets the name of the shape template.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.ScaleStartRadius">
      <summary>
            Gets or sets whether the value of the <see cref="P:C1.Win.C1Gauge.C1GaugeCustomShape.StartRadius" /> property should be scaled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.ScaleEndRadius">
      <summary>
            Gets or sets whether the value of the <see cref="P:C1.Win.C1Gauge.C1GaugeCustomShape.EndRadius" /> property should be scaled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.StartAngle">
      <summary>
            Gets or sets the angle of the start cap (between -90 and 90).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.StartRadius">
      <summary>
            Gets or sets the logical radius of the start cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.StartWidth">
      <summary>
            Gets or sets the logical width of the start cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.EndAngle">
      <summary>
            Gets or sets the angle of the end cap (between -90 and 90).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.EndRadius">
      <summary>
            Gets or sets the logical radius of the end cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.EndWidth">
      <summary>
            Gets or sets the logical width of the end cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.StartSwellAngle">
      <summary>
            Gets or sets the angle of the swell on the start cap (between -90 and 90).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.EndSwellAngle">
      <summary>
            Gets or sets the angle of the swell on the end cap (between -90 and 90).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.StartSwellLength">
      <summary>
            Gets or sets the logical length of the swell on the start cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.EndSwellLength">
      <summary>
            Gets or sets the logical length of the swell on the end cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.StartSwellWidth">
      <summary>
            Gets or sets the logical width of the swell on the start cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCustomShape.EndSwellWidth">
      <summary>
            Gets or sets the logical width of the swell on the end cap.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeGradient">
      <summary>
            Encapsulates properties of a color gradient.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeGradient.CommonGradientName">
      <summary>
            Gets or sets the name of the gradient template.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeGradient.Direction">
      <summary>
            Gets or sets the direction of a gradient brush.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeGradient.Falloff">
      <summary>
            Gets or sets the type of a gradient falloff.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeGradient.CenterPointX">
      <summary>
            Fraction-based X coordinate of the radial gradient center.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeGradient.CenterPointY">
      <summary>
            Fraction-based Y coordinate of the radial gradient center.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeGradient.Focus">
      <summary>
            A value from 0 through 1 that specifies the point where the gradient is composed of only the ending color.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeGradient.FocusScaleX">
      <summary>
            Specifies the focus point X coordinate (between 0 and 1) for the radial gradient falloff.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeGradient.FocusScaleY">
      <summary>
            Specifies the focus point Y coordinate (between 0 and 1) for the radial gradient falloff.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeGradient.TranslateX">
      <summary>
            Moves the gradient area by the specified relative amount along the X-axis (before scaling).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeGradient.TranslateY">
      <summary>
            Moves the gradient area by the specified relative amount along the Y-axis (before scaling).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeGradient.ScaleX">
      <summary>
            Scales the width of the gradient area by the specified amount (after translation).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeGradient.ScaleY">
      <summary>
            Scales the height of the gradient area by the specified amount (after translation).
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeHotBrush">
      <summary>
            Specifies the properties of a brush to paint selection on the hot item.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeHotBrush.ShouldSerializeColor1">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeHotBrush.ResetColor1">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeHotBrush.ShouldSerializeColor2">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeHotBrush.ResetColor2">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeHotBrush.Color1">
      <summary>
            Gets or sets the first Color of a hatch brush to paint selection on the hot item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeHotBrush.Color2">
      <summary>
            Gets or sets the second Color of a hatch brush to paint selection on the hot item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeHotBrush.HatchStyle">
      <summary>
            Gets or sets the style of a hatch brush to paint selection on the hot item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeHotBrush.Opacity">
      <summary>
            Gets or sets the opacity of the hot selection between 0.0 (completely transparent) and 1.0 (opaque).
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeFilling">
      <summary>
            Encapsulates properties of a brush used to fill Gauge elements.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeFilling.ShouldSerializeColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeFilling.ResetColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeFilling.ShouldSerializeColor2">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeFilling.ResetColor2">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeFilling.CommonFillingName">
      <summary>
            Gets or sets the name of the filling template.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeFilling.BrushType">
      <summary>
            Gets or sets the type of brush that is used to fill the element.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeFilling.Color">
      <summary>
            Gets or sets the Color used to fill the element.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeFilling.Opacity">
      <summary>
            Gets or sets the opacity of the first color between 0.0 (completely transparent) and 1.0 (opaque).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeFilling.Color2">
      <summary>
            Gets or sets the second Color that can be used in a gradient or hatch brush.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeFilling.Opacity2">
      <summary>
            Gets or sets the opacity of the second color between 0.0 (completely transparent) and 1.0 (opaque).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeFilling.SwapColors">
      <summary>
            Gets or sets whether the <see cref="P:C1.Win.C1Gauge.C1GaugeFilling.Color" /> should be used instead of <see cref="P:C1.Win.C1Gauge.C1GaugeFilling.Color2" /> and vice versa.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeFilling.HatchStyle">
      <summary>
            Gets or sets the style of a hatch brush.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeFilling.TextureImage">
      <summary>
            Gets or sets the Image in texture brush.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeFilling.WrapMode">
      <summary>
            Gets or sets the wrap mode for the texture brush.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugePointer">
      <summary>
            Defines properties of the Gauge pointer.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1Gauge.C1GaugePointer" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ShouldSerializeViewTag">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ShouldSerializeOffset">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ResetOffset">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ShouldSerializeLength">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ResetLength">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ShouldSerializeWidth">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ResetWidth">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ShouldSerializeCustomShape">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ResetCustomShape">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ShouldSerializeBorder">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ResetBorder">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ShouldSerializeClippings">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ShouldSerializeFilling">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ResetFilling">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ShouldSerializeGradient">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ResetGradient">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ShouldSerializeCustomImage">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ResetCustomImage">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ShouldSerializeShadow">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ResetShadow">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.GetValueAt(System.Int32,System.Int32)">
      <summary>
            Returns the Value that corresponds to the specified mouse position
            (x, y), relative to the parent control.
            </summary>
      <remarks>
            The result is scaled to the pointer's admitted region.
            </remarks>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.GetValueAt(System.Int32,System.Int32,System.Boolean)">
      <summary>
            Returns the Value that corresponds to the specified mouse position
            (x, y), relative to the parent control. If the 'dragging' parameter
            is True the returned value is the result of dragging the pointer
            from its current position to (x, y).
            </summary>
      <remarks>
            The result is scaled to the pointer's admitted region.
            </remarks>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.UpdateValue(System.Double,System.Double)">
      <summary>
            Updates the pointer's <see cref="P:C1.Win.C1Gauge.C1GaugePointer.Value" /> with the number nearest 'newValue'
            that is the multiple of the snapping interval ('snapInterval').
            </summary>
      <remarks>
            If the Gauge uses logarithmic scale the snapping interval is expected
            to be in logarithmic coordinates. If not, the 'snapInterval' parameter
            must be in the same scale as the pointer's <see cref="P:C1.Win.C1Gauge.C1GaugePointer.Value" />.
            </remarks>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.UpdateValue(System.Double,System.Double,System.Double)">
      <summary>
            Updates the pointer's <see cref="P:C1.Win.C1Gauge.C1GaugePointer.Value" /> with the number nearest 'newValue'
            that is the multiple of the snapping interval ('snapInterval') counting
            from the given origin ('snapOrigin').
            </summary>
      <remarks>
            If the Gauge uses logarithmic scale the snapInterval and snapOrigin values
            are expected to be in logarithmic coordinates. If not, these values must be
            in the same scale as the pointer's <see cref="P:C1.Win.C1Gauge.C1GaugePointer.Value" />.
            </remarks>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.IncValue(System.Int32,System.Double)">
      <summary>
            Increments the pointer's <see cref="P:C1.Win.C1Gauge.C1GaugePointer.Value" /> to the next intervalsCount'th (first, second, etc.)
            multiple of the snapping interval ('snapInterval').
            </summary>
      <remarks>
            If the Gauge uses logarithmic scale the snapping interval is expected
            to be in logarithmic coordinates. If not, the 'snapInterval' parameter
            must be in the same scale as the pointer's <see cref="P:C1.Win.C1Gauge.C1GaugePointer.Value" />.
            </remarks>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.IncValue(System.Int32,System.Double,System.Double)">
      <summary>
            Increments the pointer's <see cref="P:C1.Win.C1Gauge.C1GaugePointer.Value" /> to the next intervalsCount'th (first, second, etc.)
            multiple of the snapping interval ('snapInterval'). The snapOrigin parameter
            specifies the point of origin for snapping.
            </summary>
      <remarks>
            If the Gauge uses logarithmic scale the snapInterval and snapOrigin values
            are expected to be in logarithmic coordinates. If not, these values must be
            in the same scale as the pointer's <see cref="P:C1.Win.C1Gauge.C1GaugePointer.Value" />.
            </remarks>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugePointer.ToString">
      <summary>
            Returns a string that represents the current object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Owner">
      <summary>
            Gets the owner Gauge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.IsMainPointer">
      <summary>
            Returns True if this pointer is the main in the owner Gauge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.HitTestable">
      <summary>
            Gets or sets whether the pointer can be hit-tested.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Name">
      <summary>
            Gets or sets a name associated with the <see cref="T:C1.Win.C1Gauge.C1GaugePointer" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.ViewTag">
      <summary>
            Gets or sets a digital tag identifying this element's views.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.ScaledValue">
      <summary>
            Gets the current value scaled by the ValueFactor and ValueOffset.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Value">
      <summary>
            Gets or sets the current value of the <see cref="T:C1.Win.C1Gauge.C1GaugePointer" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.ValueFactor">
      <summary>
            Gets or sets the value multiplier to coerce the source scale of the value to the Gauge's scale.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.ValueOffset">
      <summary>
            Gets or sets the value offset to coerce the source scale of the value to the Gauge's scale.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.SweepTime">
      <summary>
            Gets or sets the time amount (in seconds) that is taken to move the pointer from Minimum to Maximum.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Offset">
      <summary>
            Gets or sets the logical offset of the pointer.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Offset2">
      <summary>
            Gets or sets the end offset of the pointer (<see cref="P:C1.Win.C1Gauge.C1GaugePointer.Offset" /> is the start offset).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Length">
      <summary>
            Gets or sets the logical length of the pointer.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Length2">
      <summary>
            Gets or sets the end length of the pointer (<see cref="P:C1.Win.C1Gauge.C1GaugePointer.Length" /> is the start length).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Alignment">
      <summary>
            Alignment of the pointer.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.ShapeAngle">
      <summary>
            Gets or sets the fixed angle (in degrees) to rotate this pointer.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.OrthogonalOffset">
      <summary>
            Gets or sets the logical orthogonal offset of the pointer.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.FlipShape">
      <summary>
            Gets or sets whether the shape of the pointer is inverted.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Shape">
      <summary>
            Selects a shape from the set of predefined pointer shapes.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Width">
      <summary>
            Gets or sets the logical width of the pointer.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.CustomShape">
      <summary>
            Gets the custom shape of the pointer.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Border">
      <summary>
            Gets the properties of the pointer border.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Clippings">
      <summary>
            Gets the collection of shapes that form the clipping region.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Filling">
      <summary>
            Adjusts the interior filling of the pointer.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Gradient">
      <summary>
            Encapsulates properties of a color gradient.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.CustomImage">
      <summary>
            Gets or sets the custom image used to draw the pointer.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Shadow">
      <summary>
            Encapsulates properties of the pointer shadow.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugePointer.Visible">
      <summary>
            Gets or sets whether the pointer is visible.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeShadow">
      <summary>
            Encapsulates properties of the shadow.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeShadow.ShouldSerializeOpacity">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeShadow.ResetOpacity">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeShadow.ShouldSerializeColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeShadow.ResetColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeShadow.ShouldSerializeOffsetX">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeShadow.ResetOffsetX">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeShadow.ShouldSerializeOffsetY">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeShadow.ResetOffsetY">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeShadow.ShouldSerializeVisible">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeShadow.ResetVisible">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeShadow.CommonShadowName">
      <summary>
            Gets or sets the name of the shadow template.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeShadow.Opacity">
      <summary>
            Gets or sets the opacity of the shadow between 0.0 (transparent) and 1.0 (dark).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeShadow.Color">
      <summary>
            Gets or sets the color of the shadow.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeShadow.OffsetX">
      <summary>
            Gets or sets the logical X offset of the shadow.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeShadow.OffsetY">
      <summary>
            Gets or sets the logical Y offset of the shadow.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeShadow.Visible">
      <summary>
            Gets or sets whether the shadow is visible.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeValueColor">
      <summary>
            Associates a value with the Color on a <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeValueColor.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeValueColor.#ctor(System.Double,System.Drawing.Color,System.Double)">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeValueColor.#ctor(System.Double,System.Int32,System.Drawing.Color,System.Double)">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeValueColor.#ctor(System.Double,System.Int32,System.Drawing.Color,System.Double,System.Int64)">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeValueColor.ShouldSerializeColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeValueColor.ResetColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeValueColor.ToString">
      <summary>
            Returns a String that represents the current <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeValueColor.Owner">
      <summary>
            Gets the owner Gauge element.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeValueColor.ViewTag">
      <summary>
            Gets or sets a digital tag identifying this element's views.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeValueColor.Value">
      <summary>
            Gets or sets the value position where the associated Color appears.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeValueColor.PointerIndex">
      <summary>
            Gets or sets the index of a Pointer that gives the value associated with this item.
            </summary>
      <remarks>
            Set to a large value to bind to the main Pointer.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeValueColor.Color">
      <summary>
            Gets or sets the Color associated with the value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeValueColor.Opacity">
      <summary>
            Gets or sets the opacity of the color between 0.0 (completely transparent) and 1.0 (opaque).
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeValueImage">
      <summary>
            Associates a value with the custom image on a <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeValueImage.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1Gauge.C1GaugeValueImage" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeValueImage.ShouldSerializeCustomImage">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeValueImage.ResetCustomImage">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeValueImage.ToString">
      <summary>
            Returns a String that represents the current <see cref="T:C1.Win.C1Gauge.C1GaugeValueImage" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeValueImage.Owner">
      <summary>
            Gets the owner Gauge element.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeValueImage.ViewTag">
      <summary>
            Gets or sets a digital tag identifying this element's views.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeValueImage.Value">
      <summary>
            Gets or sets the value position where the associated image appears.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeValueImage.PointerIndex">
      <summary>
            Gets or sets the index of a Pointer that gives the value associated with this item.
            </summary>
      <remarks>
            Set to a large value to bind to the main Pointer.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeValueImage.CustomImage">
      <summary>
            Gets or sets the custom image associated with this item.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeViewport">
      <summary>
            Specifies the bounds of a Gauge element.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeViewport.Bounds">
      <summary>
            Gets the size and location of the viewport, in pixels, relative to the parent control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeViewport.MarginX">
      <summary>
            Gets or sets the left and right margins (in pixels).
            </summary>
      <remarks>
            Negative value expands the width of viewport.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeViewport.MarginY">
      <summary>
            Gets or sets the top and bottom margins (in pixels).
            </summary>
      <remarks>
            Negative value expands the height of viewport.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeViewport.X">
      <summary>
            Gets or sets the X-offset of the viewport (in pixels).
            </summary>
      <remarks>
            Negative offset is count from the right edge of the owner element.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeViewport.Y">
      <summary>
            Gets or sets the Y-offset of the viewport (in pixels).
            </summary>
      <remarks>
            Negative offset is count from the bottom edge of the owner element.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeViewport.Width">
      <summary>
            Gets or sets the width of the viewport (in pixels).
            </summary>
      <remarks>
            Zero width extends the viewport till the opposite edge of the owner element.
            Negative width extends the viewport backwards.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeViewport.Height">
      <summary>
            Gets or sets the height of the viewport (in pixels).
            </summary>
      <remarks>
            Zero height extends the viewport till the opposite edge of the owner element.
            Negative height extends the viewport backwards.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeViewport.TranslateX">
      <summary>
            Moves the viewport by the specified relative amount along the X-axis (before scaling).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeViewport.TranslateY">
      <summary>
            Moves the viewport by the specified relative amount along the Y-axis (before scaling).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeViewport.ScaleX">
      <summary>
            Scales the width of the viewport by the specified relative amount (after translation).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeViewport.ScaleY">
      <summary>
            Scales the height of the viewport by the specified relative amount (after translation).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeViewport.AspectRatio">
      <summary>
            Gets or sets the fixed ratio of width to height of the viewport.
            </summary>
      <remarks>
            Set to Double.NaN to avoid restriction of the aspect ratio.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeViewport.AspectPinX">
      <summary>
            Gets or sets the relative X position that remains fixed when the viewport
            moves in order to maintain the aspect ration.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeViewport.AspectPinY">
      <summary>
            Gets or sets the relative Y position that remains fixed when the viewport
            moves in order to maintain the aspect ratio.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CapCircleCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.C1GaugeCapCircle" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CapCircleCollection.Contains(C1.Win.C1Gauge.C1GaugeCapCircle)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.C1GaugeCapCircle" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CapCircleCollection.IndexOf(C1.Win.C1Gauge.C1GaugeCapCircle)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.C1GaugeCapCircle" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CapCircleCollection.Add(C1.Win.C1Gauge.C1GaugeCapCircle)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.C1GaugeCapCircle" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CapCircleCollection.AddRange(C1.Win.C1Gauge.C1GaugeCapCircle[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.C1GaugeCapCircle" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CapCircleCollection.Insert(System.Int32,C1.Win.C1Gauge.C1GaugeCapCircle)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.C1GaugeCapCircle" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CapCircleCollection.Remove(C1.Win.C1Gauge.C1GaugeCapCircle)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.C1GaugeCapCircle" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CapCircleCollection.Move(System.Int32,System.Int32)">
      <summary>
            Moves the item with given index (fromIndex) to the new position (toIndex).
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CapCircleCollection.OnInsertComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after inserting a <see cref="T:C1.Win.C1Gauge.C1GaugeCapCircle" />
            into the <see cref="T:C1.Win.C1Gauge.CapCircleCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CapCircleCollection.OnRemoveComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after removing a <see cref="T:C1.Win.C1Gauge.C1GaugeCapCircle" />
            from the <see cref="T:C1.Win.C1Gauge.CapCircleCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CapCircleCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
      <summary>
            Performs additional custom processes after setting a <see cref="T:C1.Win.C1Gauge.C1GaugeCapCircle" />
            in the <see cref="T:C1.Win.C1Gauge.CapCircleCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CapCircleCollection.OnClear">
      <summary>
            Performs additional custom processes when clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.CapCircleCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CapCircleCollection.OnClearComplete">
      <summary>
            Performs additional custom processes after clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.CapCircleCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CapCircleCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CapCircleCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.C1GaugeCapCircle" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.ClippingCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.C1GaugeClipping" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ClippingCollection.Contains(C1.Win.C1Gauge.C1GaugeClipping)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.C1GaugeClipping" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ClippingCollection.IndexOf(C1.Win.C1Gauge.C1GaugeClipping)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.C1GaugeClipping" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ClippingCollection.Add(C1.Win.C1Gauge.C1GaugeClipping)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.C1GaugeClipping" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ClippingCollection.AddRange(C1.Win.C1Gauge.C1GaugeClipping[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.C1GaugeClipping" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ClippingCollection.Insert(System.Int32,C1.Win.C1Gauge.C1GaugeClipping)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.C1GaugeClipping" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ClippingCollection.Remove(C1.Win.C1Gauge.C1GaugeClipping)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.C1GaugeClipping" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ClippingCollection.Move(System.Int32,System.Int32)">
      <summary>
            Moves the item with given index (fromIndex) to the new position (toIndex).
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ClippingCollection.OnInsertComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after inserting a <see cref="T:C1.Win.C1Gauge.C1GaugeClipping" />
            into the <see cref="T:C1.Win.C1Gauge.ClippingCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ClippingCollection.OnRemoveComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after removing a <see cref="T:C1.Win.C1Gauge.C1GaugeClipping" />
            from the <see cref="T:C1.Win.C1Gauge.ClippingCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ClippingCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
      <summary>
            Performs additional custom processes after setting a <see cref="T:C1.Win.C1Gauge.C1GaugeClipping" />
            in the <see cref="T:C1.Win.C1Gauge.ClippingCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ClippingCollection.OnClear">
      <summary>
            Performs additional custom processes when clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.ClippingCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ClippingCollection.OnClearComplete">
      <summary>
            Performs additional custom processes after clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.ClippingCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ClippingCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ClippingCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.C1GaugeClipping" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonItemCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.CommonItem" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonItemCollection.Contains(System.String)">
      <summary>
            Determines whether this collection contains a <see cref="T:C1.Win.C1Gauge.CommonItem" /> with the specified name.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonItemCollection.IndexOf(System.String)">
      <summary>
            Determines index of the first <see cref="T:C1.Win.C1Gauge.CommonItem" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonItemCollection.ClearAndDispose">
      <summary>
            Removes all common items from the collection, then disposes the items.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonItemCollection.Move(System.Int32,System.Int32)">
      <summary>
            Moves the item with given index (fromIndex) to the new position (toIndex).
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonItemCollection.OnInsertComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after inserting a <see cref="T:C1.Win.C1Gauge.CommonItem" />
            into the <see cref="T:C1.Win.C1Gauge.CommonItemCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonItemCollection.OnRemoveComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after removing a <see cref="T:C1.Win.C1Gauge.CommonItem" />
            from the <see cref="T:C1.Win.C1Gauge.CommonItemCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonItemCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
      <summary>
            Performs additional custom processes after setting a <see cref="T:C1.Win.C1Gauge.CommonItem" />
            in the <see cref="T:C1.Win.C1Gauge.CommonItemCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonItemCollection.OnClear">
      <summary>
            Performs additional custom processes when clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.CommonItemCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonItemCollection.OnClearComplete">
      <summary>
            Performs additional custom processes after clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.CommonItemCollection" /> instance.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonBorderCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.CommonBorder" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonBorderCollection.Contains(C1.Win.C1Gauge.CommonBorder)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.CommonBorder" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonBorderCollection.IndexOf(C1.Win.C1Gauge.CommonBorder)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.CommonBorder" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonBorderCollection.Add(C1.Win.C1Gauge.CommonBorder)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.CommonBorder" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonBorderCollection.AddRange(C1.Win.C1Gauge.CommonBorder[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.CommonBorder" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonBorderCollection.Insert(System.Int32,C1.Win.C1Gauge.CommonBorder)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.CommonBorder" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonBorderCollection.Remove(C1.Win.C1Gauge.CommonBorder)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.CommonBorder" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonBorderCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonBorderCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.CommonBorder" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonBorderCollection.Item(System.String)">
      <summary>
            Gets the first <see cref="T:C1.Win.C1Gauge.CommonBorder" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonFillingCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.CommonFilling" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFillingCollection.Contains(C1.Win.C1Gauge.CommonFilling)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.CommonFilling" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFillingCollection.IndexOf(C1.Win.C1Gauge.CommonFilling)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.CommonFilling" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFillingCollection.Add(C1.Win.C1Gauge.CommonFilling)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.CommonFilling" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFillingCollection.AddRange(C1.Win.C1Gauge.CommonFilling[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.CommonFilling" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFillingCollection.Insert(System.Int32,C1.Win.C1Gauge.CommonFilling)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.CommonFilling" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFillingCollection.Remove(C1.Win.C1Gauge.CommonFilling)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.CommonFilling" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFillingCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFillingCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.CommonFilling" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFillingCollection.Item(System.String)">
      <summary>
            Gets the first <see cref="T:C1.Win.C1Gauge.CommonFilling" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonFontCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.CommonFont" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFontCollection.Contains(C1.Win.C1Gauge.CommonFont)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.CommonFont" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFontCollection.IndexOf(C1.Win.C1Gauge.CommonFont)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.CommonFont" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFontCollection.Add(C1.Win.C1Gauge.CommonFont)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.CommonFont" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFontCollection.AddRange(C1.Win.C1Gauge.CommonFont[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.CommonFont" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFontCollection.Insert(System.Int32,C1.Win.C1Gauge.CommonFont)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.CommonFont" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFontCollection.Remove(C1.Win.C1Gauge.CommonFont)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.CommonFont" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFontCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFontCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.CommonFont" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFontCollection.Item(System.String)">
      <summary>
            Gets the first <see cref="T:C1.Win.C1Gauge.CommonFont" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonGradientCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.CommonGradient" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonGradientCollection.Contains(C1.Win.C1Gauge.CommonGradient)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.CommonGradient" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonGradientCollection.IndexOf(C1.Win.C1Gauge.CommonGradient)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.CommonGradient" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonGradientCollection.Add(C1.Win.C1Gauge.CommonGradient)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.CommonGradient" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonGradientCollection.AddRange(C1.Win.C1Gauge.CommonGradient[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.CommonGradient" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonGradientCollection.Insert(System.Int32,C1.Win.C1Gauge.CommonGradient)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.CommonGradient" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonGradientCollection.Remove(C1.Win.C1Gauge.CommonGradient)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.CommonGradient" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonGradientCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonGradientCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.CommonGradient" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonGradientCollection.Item(System.String)">
      <summary>
            Gets the first <see cref="T:C1.Win.C1Gauge.CommonGradient" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonImageCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.CommonImage" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageCollection.Contains(C1.Win.C1Gauge.CommonImage)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.CommonImage" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageCollection.IndexOf(C1.Win.C1Gauge.CommonImage)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.CommonImage" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageCollection.Add(C1.Win.C1Gauge.CommonImage)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.CommonImage" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageCollection.AddRange(C1.Win.C1Gauge.CommonImage[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.CommonImage" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageCollection.Insert(System.Int32,C1.Win.C1Gauge.CommonImage)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.CommonImage" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageCollection.Remove(C1.Win.C1Gauge.CommonImage)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.CommonImage" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImageCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.CommonImage" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImageCollection.Item(System.String)">
      <summary>
            Gets the first <see cref="T:C1.Win.C1Gauge.CommonImage" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonShadowCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.CommonShadow" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShadowCollection.Contains(C1.Win.C1Gauge.CommonShadow)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.CommonShadow" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShadowCollection.IndexOf(C1.Win.C1Gauge.CommonShadow)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.CommonShadow" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShadowCollection.Add(C1.Win.C1Gauge.CommonShadow)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.CommonShadow" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShadowCollection.AddRange(C1.Win.C1Gauge.CommonShadow[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.CommonShadow" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShadowCollection.Insert(System.Int32,C1.Win.C1Gauge.CommonShadow)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.CommonShadow" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShadowCollection.Remove(C1.Win.C1Gauge.CommonShadow)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.CommonShadow" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShadowCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShadowCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.CommonShadow" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShadowCollection.Item(System.String)">
      <summary>
            Gets the first <see cref="T:C1.Win.C1Gauge.CommonShadow" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonShapeCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.CommonShape" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShapeCollection.Contains(C1.Win.C1Gauge.CommonShape)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.CommonShape" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShapeCollection.IndexOf(C1.Win.C1Gauge.CommonShape)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.CommonShape" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShapeCollection.Add(C1.Win.C1Gauge.CommonShape)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.CommonShape" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShapeCollection.AddRange(C1.Win.C1Gauge.CommonShape[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.CommonShape" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShapeCollection.Insert(System.Int32,C1.Win.C1Gauge.CommonShape)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.CommonShape" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShapeCollection.Remove(C1.Win.C1Gauge.CommonShape)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.CommonShape" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShapeCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShapeCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.CommonShape" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShapeCollection.Item(System.String)">
      <summary>
            Gets the first <see cref="T:C1.Win.C1Gauge.CommonShape" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonColorMapCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.CommonColorMap" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonColorMapCollection.Contains(C1.Win.C1Gauge.CommonColorMap)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.CommonColorMap" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonColorMapCollection.IndexOf(C1.Win.C1Gauge.CommonColorMap)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.CommonColorMap" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonColorMapCollection.Add(C1.Win.C1Gauge.CommonColorMap)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.CommonColorMap" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonColorMapCollection.AddRange(C1.Win.C1Gauge.CommonColorMap[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.CommonColorMap" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonColorMapCollection.Insert(System.Int32,C1.Win.C1Gauge.CommonColorMap)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.CommonColorMap" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonColorMapCollection.Remove(C1.Win.C1Gauge.CommonColorMap)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.CommonColorMap" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonColorMapCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonColorMapCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.CommonColorMap" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonColorMapCollection.Item(System.String)">
      <summary>
            Gets the first <see cref="T:C1.Win.C1Gauge.CommonColorMap" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonImageMapCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.CommonImageMap" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageMapCollection.Contains(C1.Win.C1Gauge.CommonImageMap)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.CommonImageMap" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageMapCollection.IndexOf(C1.Win.C1Gauge.CommonImageMap)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.CommonImageMap" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageMapCollection.Add(C1.Win.C1Gauge.CommonImageMap)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.CommonImageMap" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageMapCollection.AddRange(C1.Win.C1Gauge.CommonImageMap[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.CommonImageMap" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageMapCollection.Insert(System.Int32,C1.Win.C1Gauge.CommonImageMap)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.CommonImageMap" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageMapCollection.Remove(C1.Win.C1Gauge.CommonImageMap)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.CommonImageMap" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageMapCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImageMapCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.CommonImageMap" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImageMapCollection.Item(System.String)">
      <summary>
            Gets the first <see cref="T:C1.Win.C1Gauge.CommonImageMap" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.DecoratorCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.Contains(C1.Win.C1Gauge.C1GaugeDecorator)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.Contains(System.String)">
      <summary>
            Determines whether this collection contains a <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> with the specified name.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.IndexOf(C1.Win.C1Gauge.C1GaugeDecorator)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.IndexOf(System.String)">
      <summary>
            Determines index of the first <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.Add(C1.Win.C1Gauge.C1GaugeDecorator)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.AddRange(C1.Win.C1Gauge.C1GaugeDecorator[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.Insert(System.Int32,C1.Win.C1Gauge.C1GaugeDecorator)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.Remove(C1.Win.C1Gauge.C1GaugeDecorator)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.ClearAndDispose">
      <summary>
            Removes all decorators from the collection, then disposes the decorators.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.Move(System.Int32,System.Int32)">
      <summary>
            Moves the item with given index (fromIndex) to the new position (toIndex).
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.OnInsertComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after inserting a <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" />
            into the <see cref="T:C1.Win.C1Gauge.DecoratorCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.OnRemoveComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after removing a <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" />
            from the <see cref="T:C1.Win.C1Gauge.DecoratorCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
      <summary>
            Performs additional custom processes after setting a <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" />
            in the <see cref="T:C1.Win.C1Gauge.DecoratorCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.OnClear">
      <summary>
            Performs additional custom processes when clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.DecoratorCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.OnClearComplete">
      <summary>
            Performs additional custom processes after clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.DecoratorCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.DecoratorCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.DecoratorCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.DecoratorCollection.Item(System.String)">
      <summary>
            Gets the first <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.GaugeCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.C1GaugeBase" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.Contains(C1.Win.C1Gauge.C1GaugeBase)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.C1GaugeBase" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.Contains(System.String)">
      <summary>
            Determines whether this collection contains a <see cref="T:C1.Win.C1Gauge.C1GaugeBase" /> with the specified name.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.IndexOf(C1.Win.C1Gauge.C1GaugeBase)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.C1GaugeBase" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.IndexOf(System.String)">
      <summary>
            Determines index of the first <see cref="T:C1.Win.C1Gauge.C1GaugeBase" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.Add(C1.Win.C1Gauge.C1GaugeBase)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.C1GaugeBase" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.AddRange(C1.Win.C1Gauge.C1GaugeBase[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.C1GaugeBase" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.Insert(System.Int32,C1.Win.C1Gauge.C1GaugeBase)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.C1GaugeBase" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.Remove(C1.Win.C1Gauge.C1GaugeBase)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.C1GaugeBase" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.ClearAndDispose">
      <summary>
            Removes all Gauges from the collection, then disposes the Gauges.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.Move(System.Int32,System.Int32)">
      <summary>
            Moves the item with given index (fromIndex) to the new position (toIndex).
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.OnInsertComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after inserting a <see cref="T:C1.Win.C1Gauge.C1GaugeBase" />
            into the <see cref="T:C1.Win.C1Gauge.GaugeCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.OnRemoveComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after removing a <see cref="T:C1.Win.C1Gauge.C1GaugeBase" />
            from the <see cref="T:C1.Win.C1Gauge.GaugeCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
      <summary>
            Performs additional custom processes after setting a <see cref="T:C1.Win.C1Gauge.C1GaugeBase" />
            in the <see cref="T:C1.Win.C1Gauge.GaugeCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.OnClear">
      <summary>
            Performs additional custom processes when clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.GaugeCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.OnClearComplete">
      <summary>
            Performs additional custom processes after clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.GaugeCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.GaugeCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.GaugeCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.C1GaugeBase" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.GaugeCollection.Item(System.String)">
      <summary>
            Gets the first <see cref="T:C1.Win.C1Gauge.C1GaugeBase" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.PointerCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.C1GaugePointer" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.Contains(C1.Win.C1Gauge.C1GaugePointer)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.C1GaugePointer" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.Contains(System.String)">
      <summary>
            Determines whether this collection contains a <see cref="T:C1.Win.C1Gauge.C1GaugePointer" /> with the specified name.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.IndexOf(C1.Win.C1Gauge.C1GaugePointer)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.C1GaugePointer" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.IndexOf(System.String)">
      <summary>
            Determines index of the first <see cref="T:C1.Win.C1Gauge.C1GaugePointer" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.Add(C1.Win.C1Gauge.C1GaugePointer)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.C1GaugePointer" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.AddRange(C1.Win.C1Gauge.C1GaugePointer[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.C1GaugePointer" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.Insert(System.Int32,C1.Win.C1Gauge.C1GaugePointer)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.C1GaugePointer" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.Remove(C1.Win.C1Gauge.C1GaugePointer)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.C1GaugePointer" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.ClearAndDispose">
      <summary>
            Removes all pointers from the collection, then disposes the pointers.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.Move(System.Int32,System.Int32)">
      <summary>
            Moves the item with given index (fromIndex) to the new position (toIndex).
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.OnInsertComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after inserting a <see cref="T:C1.Win.C1Gauge.C1GaugePointer" />
            into the <see cref="T:C1.Win.C1Gauge.PointerCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.OnRemoveComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after removing a <see cref="T:C1.Win.C1Gauge.C1GaugePointer" />
            from the <see cref="T:C1.Win.C1Gauge.PointerCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
      <summary>
            Performs additional custom processes after setting a <see cref="T:C1.Win.C1Gauge.C1GaugePointer" />
            in the <see cref="T:C1.Win.C1Gauge.PointerCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.OnClear">
      <summary>
            Performs additional custom processes when clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.PointerCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.OnClearComplete">
      <summary>
            Performs additional custom processes after clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.PointerCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.PointerCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.PointerCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.C1GaugePointer" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.PointerCollection.Item(System.String)">
      <summary>
            Gets the first <see cref="T:C1.Win.C1Gauge.C1GaugePointer" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.ShapeCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.C1GaugeBaseShape" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.Contains(C1.Win.C1Gauge.C1GaugeBaseShape)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.C1GaugeBaseShape" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.Contains(System.String)">
      <summary>
            Determines whether this collection contains a <see cref="T:C1.Win.C1Gauge.C1GaugeBaseShape" /> with the specified name.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.IndexOf(C1.Win.C1Gauge.C1GaugeBaseShape)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.C1GaugeBaseShape" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.IndexOf(System.String)">
      <summary>
            Determines index of the first <see cref="T:C1.Win.C1Gauge.C1GaugeBaseShape" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.Add(C1.Win.C1Gauge.C1GaugeBaseShape)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.C1GaugeBaseShape" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.AddRange(C1.Win.C1Gauge.C1GaugeBaseShape[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.C1GaugeBaseShape" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.Insert(System.Int32,C1.Win.C1Gauge.C1GaugeBaseShape)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.C1GaugeBaseShape" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.Remove(C1.Win.C1Gauge.C1GaugeBaseShape)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.C1GaugeBaseShape" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.ClearAndDispose">
      <summary>
            Removes all shapes from the collection, then disposes the shapes.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.Move(System.Int32,System.Int32)">
      <summary>
            Moves the item with given index (fromIndex) to the new position (toIndex).
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.OnInsertComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after inserting a <see cref="T:C1.Win.C1Gauge.C1GaugeBaseShape" />
            into the <see cref="T:C1.Win.C1Gauge.ShapeCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.OnRemoveComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after removing a <see cref="T:C1.Win.C1Gauge.C1GaugeBaseShape" />
            from the <see cref="T:C1.Win.C1Gauge.ShapeCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
      <summary>
            Performs additional custom processes after setting a <see cref="T:C1.Win.C1Gauge.C1GaugeBaseShape" />
            in the <see cref="T:C1.Win.C1Gauge.ShapeCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.OnClear">
      <summary>
            Performs additional custom processes when clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.ShapeCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.OnClearComplete">
      <summary>
            Performs additional custom processes after clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.ShapeCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ShapeCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ShapeCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.C1GaugeBaseShape" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ShapeCollection.Item(System.String)">
      <summary>
            Gets the first <see cref="T:C1.Win.C1Gauge.C1GaugeBaseShape" /> with the specified name in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.ValueColorCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueColorCollection.Contains(C1.Win.C1Gauge.C1GaugeValueColor)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueColorCollection.IndexOf(C1.Win.C1Gauge.C1GaugeValueColor)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueColorCollection.Add(C1.Win.C1Gauge.C1GaugeValueColor)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueColorCollection.AddRange(C1.Win.C1Gauge.C1GaugeValueColor[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueColorCollection.Insert(System.Int32,C1.Win.C1Gauge.C1GaugeValueColor)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueColorCollection.Remove(C1.Win.C1Gauge.C1GaugeValueColor)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueColorCollection.Move(System.Int32,System.Int32)">
      <summary>
            Moves the item with given index (fromIndex) to the new position (toIndex).
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueColorCollection.OnInsertComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after inserting a <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" />
            into the <see cref="T:C1.Win.C1Gauge.ValueColorCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueColorCollection.OnRemoveComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after removing a <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" />
            from the <see cref="T:C1.Win.C1Gauge.ValueColorCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueColorCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
      <summary>
            Performs additional custom processes after setting a <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" />
            in the <see cref="T:C1.Win.C1Gauge.ValueColorCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueColorCollection.OnClear">
      <summary>
            Performs additional custom processes when clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.ValueColorCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueColorCollection.OnClearComplete">
      <summary>
            Performs additional custom processes after clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.ValueColorCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueColorCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ValueColorCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.C1GaugeValueColor" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.ValueImageCollection">
      <summary>
            Represents a collection of <see cref="T:C1.Win.C1Gauge.C1GaugeValueImage" /> objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueImageCollection.Contains(C1.Win.C1Gauge.C1GaugeValueImage)">
      <summary>
            Determines whether this collection contains the specified <see cref="T:C1.Win.C1Gauge.C1GaugeValueImage" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueImageCollection.IndexOf(C1.Win.C1Gauge.C1GaugeValueImage)">
      <summary>
            Determines the index of a specific <see cref="T:C1.Win.C1Gauge.C1GaugeValueImage" /> in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueImageCollection.Add(C1.Win.C1Gauge.C1GaugeValueImage)">
      <summary>
            Adds the specified <see cref="T:C1.Win.C1Gauge.C1GaugeValueImage" /> to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueImageCollection.AddRange(C1.Win.C1Gauge.C1GaugeValueImage[])">
      <summary>
            Adds a group of <see cref="T:C1.Win.C1Gauge.C1GaugeValueImage" /> objects to the end of the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueImageCollection.Insert(System.Int32,C1.Win.C1Gauge.C1GaugeValueImage)">
      <summary>
            Inserts the specified <see cref="T:C1.Win.C1Gauge.C1GaugeValueImage" /> into this collection at the given index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueImageCollection.Remove(C1.Win.C1Gauge.C1GaugeValueImage)">
      <summary>
            Removes the specified <see cref="T:C1.Win.C1Gauge.C1GaugeValueImage" /> from the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueImageCollection.ClearAndDispose">
      <summary>
            Removes all items from the collection, then disposes the items.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueImageCollection.Move(System.Int32,System.Int32)">
      <summary>
            Moves the item with given index (fromIndex) to the new position (toIndex).
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueImageCollection.OnInsertComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after inserting a <see cref="T:C1.Win.C1Gauge.C1GaugeValueImage" />
            into the <see cref="T:C1.Win.C1Gauge.ValueImageCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueImageCollection.OnRemoveComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after removing a <see cref="T:C1.Win.C1Gauge.C1GaugeValueImage" />
            from the <see cref="T:C1.Win.C1Gauge.ValueImageCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueImageCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
      <summary>
            Performs additional custom processes after setting a <see cref="T:C1.Win.C1Gauge.C1GaugeValueImage" />
            in the <see cref="T:C1.Win.C1Gauge.ValueImageCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueImageCollection.OnClear">
      <summary>
            Performs additional custom processes when clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.ValueImageCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueImageCollection.OnClearComplete">
      <summary>
            Performs additional custom processes after clearing the contents of
            the <see cref="T:C1.Win.C1Gauge.ValueImageCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.ValueImageCollection.OnValidate(System.Object)">
      <summary>
            Checks whather the type of the value argument is allowable for this collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.ValueImageCollection.Item(System.Int32)">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Gauge.C1GaugeValueImage" /> at the specified position in the collection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonBorder">
      <summary>
            Defines a template for <see cref="T:C1.Win.C1Gauge.C1GaugeBorder" /> to be used in multiple elements.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonItem">
      <summary>
            Defines a common template for various items to be used in multiple elements.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonItem.Owner">
      <summary>
            Gets the owner Gauge element.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonItem.Name">
      <summary>
            Gets or sets a name associated with the common item.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonBorder.ShouldSerializeColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonBorder.ResetColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonBorder.LineStyle">
      <summary>
            Gets or sets the style of lines drawn with a border.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonBorder.Thickness">
      <summary>
            Gets or sets the logical thickness of the border. Negative value
            specifies the width of the border in pixels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonBorder.Color">
      <summary>
            Gets or sets the Color of the border.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonColorMap">
      <summary>
            Defines a template for <see cref="T:C1.Win.C1Gauge.ValueColorCollection" /> to be used in multiple elements.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonColorMap.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1Gauge.CommonColorMap" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonColorMap.ShouldSerializeValueColors">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonColorMap.ViewTag">
      <summary>
            Gets or sets a digital tag identifying this element's views.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonColorMap.ValueColorFalloff">
      <summary>
            Gets or sets the blending mode for value colors.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonColorMap.ValueColors">
      <summary>
            Gets the collection of values with their associated colors.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonFilling">
      <summary>
            Defines a template for <see cref="T:C1.Win.C1Gauge.C1GaugeFilling" /> to be used in multiple elements.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFilling.ShouldSerializeColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFilling.ResetColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFilling.ShouldSerializeColor2">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFilling.ResetColor2">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFilling.BrushType">
      <summary>
            Gets or sets the type of brush that is used to fill the element.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFilling.Color">
      <summary>
            Gets or sets the Color used to fill the element.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFilling.Opacity">
      <summary>
            Gets or sets the opacity of the first color between 0.0 (completely transparent) and 1.0 (opaque).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFilling.Color2">
      <summary>
            Gets or sets the second Color that can be used in a gradient or hatch brush.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFilling.Opacity2">
      <summary>
            Gets or sets the opacity of the second color between 0.0 (completely transparent) and 1.0 (opaque).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFilling.SwapColors">
      <summary>
            Gets or sets whether the <see cref="P:C1.Win.C1Gauge.CommonFilling.Color" /> should be used instead of <see cref="P:C1.Win.C1Gauge.CommonFilling.Color2" /> and vice versa.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFilling.HatchStyle">
      <summary>
            Gets or sets the style of a hatch brush.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFilling.TextureImage">
      <summary>
            Gets or sets the Image in texture brush.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFilling.WrapMode">
      <summary>
            Gets or sets the wrap mode for the texture brush.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonFont">
      <summary>
            Defines a template for text font and color to be used in multiple elements.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFont.ShouldSerializeColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonFont.ResetColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFont.Color">
      <summary>
            Gets or sets the Color used to display text label.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFont.Font">
      <summary>
            Gets or sets the font used to display text label.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonFont.FontSize">
      <summary>
            Logical value representing the scalable font size.
            </summary>
      <remarks>
            Set to Double.NaN to use the fixed font size.
            </remarks>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonGradient">
      <summary>
            Defines a template for <see cref="T:C1.Win.C1Gauge.C1GaugeGradient" /> to be used in multiple elements.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonGradient.Direction">
      <summary>
            Gets or sets the direction of a gradient brush.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonGradient.Falloff">
      <summary>
            Gets or sets the type of a gradient falloff.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonGradient.CenterPointX">
      <summary>
            Fraction-based X coordinate of the radial gradient center.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonGradient.CenterPointY">
      <summary>
            Fraction-based Y coordinate of the radial gradient center.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonGradient.Focus">
      <summary>
            A value from 0 through 1 that specifies the point where the gradient is composed of only the ending color.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonGradient.FocusScaleX">
      <summary>
            Specifies the focus point X coordinate (between 0 and 1) for the radial gradient falloff.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonGradient.FocusScaleY">
      <summary>
            Specifies the focus point Y coordinate (between 0 and 1) for the radial gradient falloff.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonGradient.TranslateX">
      <summary>
            Moves the gradient area by the specified relative amount along the X-axis (before scaling).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonGradient.TranslateY">
      <summary>
            Moves the gradient area by the specified relative amount along the Y-axis (before scaling).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonGradient.ScaleX">
      <summary>
            Scales the width of the gradient area by the specified amount (after translation).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonGradient.ScaleY">
      <summary>
            Scales the height of the gradient area by the specified amount (after translation).
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonImage">
      <summary>
            Defines a template for <see cref="T:C1.Win.C1Gauge.C1GaugeCustomImage" /> to be used in multiple elements.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImage.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1Gauge.CommonImage" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImage.Width">
      <summary>
            Gets or sets the logical value representing the width of the image.
            </summary>
      <remarks>
            Can be set to double.NaN (default value) to avoid scaling the image in the X-axis direction.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImage.Height">
      <summary>
            Gets or sets the logical value representing the height of the image.
            </summary>
      <remarks>
            Can be set to double.NaN (default value) to avoid scaling the image in the Y-axis direction.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImage.Image">
      <summary>
            Gets or sets the original image.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImage.KeepAspectRatio">
      <summary>
            Gets or sets whether the image aspect ratio should be maintained.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImage.KeepSize">
      <summary>
            Gets or sets whether the predefined size of the image should not changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImage.RotateFlipType">
      <summary>
            Gets or sets the direction of an image's rotation and the axis used to flip the image.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImage.Hue">
      <summary>
            Allows to modify the image hue by the specified amount (between -180 and 180).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImage.Saturation">
      <summary>
            Allows to modify the image saturation by the specified amount (between -100 and 100).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImage.Lightness">
      <summary>
            Allows to modify the image lightness by the specified amount (between -100 and 100).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImage.Opacity">
      <summary>
            Gets or sets the opacity of the image between 0.0 (completely invisible) and 1.0 (opaque).
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonImageMap">
      <summary>
            Defines a template for <see cref="T:C1.Win.C1Gauge.ValueImageCollection" /> to be used in multiple elements.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageMap.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1Gauge.CommonImageMap" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonImageMap.ShouldSerializeValueImages">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImageMap.ViewTag">
      <summary>
            Gets or sets a digital tag identifying this element's views.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonImageMap.ValueImages">
      <summary>
            Gets the collection of values with their associated images.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonShadow">
      <summary>
            Defines a template for <see cref="T:C1.Win.C1Gauge.C1GaugeShadow" /> to be used in multiple elements.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShadow.ShouldSerializeColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.CommonShadow.ResetColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShadow.Opacity">
      <summary>
            Gets or sets the opacity of the shadow between 0.0 (transparent) and 1.0 (dark).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShadow.Color">
      <summary>
            Gets or sets the color of the shadow.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShadow.OffsetX">
      <summary>
            Gets or sets the logical X offset of the shadow.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShadow.OffsetY">
      <summary>
            Gets or sets the logical Y offset of the shadow.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShadow.Visible">
      <summary>
            Gets or sets whether the shadow is visible.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.CommonShape">
      <summary>
            Defines a template for <see cref="T:C1.Win.C1Gauge.C1GaugeCustomShape" /> to be used in multiple elements.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShape.ScaleStartRadius">
      <summary>
            Gets or sets whether the value of the <see cref="P:C1.Win.C1Gauge.CommonShape.StartRadius" /> property should be scaled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShape.ScaleEndRadius">
      <summary>
            Gets or sets whether the value of the <see cref="P:C1.Win.C1Gauge.CommonShape.EndRadius" /> property should be scaled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShape.StartAngle">
      <summary>
            Gets or sets the angle of the start cap (between -90 and 90).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShape.StartRadius">
      <summary>
            Gets or sets the logical radius of the start cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShape.StartWidth">
      <summary>
            Gets or sets the logical width of the start cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShape.EndAngle">
      <summary>
            Gets or sets the angle of the end cap (between -90 and 90).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShape.EndRadius">
      <summary>
            Gets or sets the logical radius of the end cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShape.EndWidth">
      <summary>
            Gets or sets the logical width of the end cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShape.StartSwellAngle">
      <summary>
            Gets or sets the angle of the swell on the start cap (between -90 and 90).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShape.EndSwellAngle">
      <summary>
            Gets or sets the angle of the swell on the end cap (between -90 and 90).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShape.StartSwellLength">
      <summary>
            Gets or sets the logical length of the swell on the start cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShape.EndSwellLength">
      <summary>
            Gets or sets the logical length of the swell on the end cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShape.StartSwellWidth">
      <summary>
            Gets or sets the logical width of the swell on the start cap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.CommonShape.EndSwellWidth">
      <summary>
            Gets or sets the logical width of the swell on the end cap.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeDecorator">
      <summary>
            Base class for all the Gauge decorators.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeDecorator.#ctor">
      <summary>
            Initializes a new instance of the decorator.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeDecorator.ShouldSerializeLocation">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeDecorator.ResetLocation">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeDecorator.ShouldSerializeClippings">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeDecorator.ShouldSerializeShadow">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeDecorator.ResetShadow">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeDecorator.ShouldSerializeValueColors">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeDecorator.ToString">
      <summary>
            Returns a string that represents the current object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeDecorator.Owner">
      <summary>
            Gets the owner Gauge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeDecorator.HitTestable">
      <summary>
            Gets or sets whether the decorator can be hit-tested.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeDecorator.Name">
      <summary>
            Gets or sets a name associated with the decorator.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeDecorator.ViewTag">
      <summary>
            Gets or sets a digital tag identifying this element's views.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeDecorator.Location">
      <summary>
            Logical value representing the start location of the decorator.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeDecorator.Alignment">
      <summary>
            Alignment of the decorator.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeDecorator.AlignmentOffset">
      <summary>
            Gets or sets the alignment offset of the decorator (in pixels).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeDecorator.Clippings">
      <summary>
            Gets the collection of shapes that form the clipping region.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeDecorator.Shadow">
      <summary>
            Encapsulates properties of the decorator shadow.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeDecorator.ColorMapName">
      <summary>
            Gets or sets the name of template for the <see cref="P:C1.Win.C1Gauge.C1GaugeDecorator.ValueColors" /> collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeDecorator.ValueColorFalloff">
      <summary>
            Gets or sets the blending mode for value colors.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeDecorator.ValueColors">
      <summary>
            Gets the collection of values with their associated colors.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeDecorator.Visible">
      <summary>
            Gets or sets whether the decorator is visible.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeLabels">
      <summary>
        <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> used to display a sequence of labels on the Gauge.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeScaleDecorator">
      <summary>
            Base class for the Gauge decorators showing a scale, such as marks or labels.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeMultivalueDecorator">
      <summary>
            Base class for the Gauge decorators that spread over a set of values.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMultivalueDecorator.From">
      <summary>
            Gets or sets the value where the decorator starts. Set to Double.NaN to bind to Gauge's minimum.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMultivalueDecorator.To">
      <summary>
            Gets or sets the value where the decorator ends. Set to Double.NaN to bind to Gauge's maximum.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMultivalueDecorator.FromPointerIndex">
      <summary>
            Gets or sets the index of a Pointer that specifies where the decorator starts.
            </summary>
      <remarks>
            Set to a large value to bind to the main Pointer.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMultivalueDecorator.ToPointerIndex">
      <summary>
            Gets or sets the index of a Pointer that specifies where the decorator ends.
            </summary>
      <remarks>
            Set to a large value to bind to the main Pointer.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMultivalueDecorator.ScaleFrom">
      <summary>
            Gets or sets the value where the scale graduation starts and the start location and width are defined.
            Set to Double.NaN to bind to the <see cref="P:C1.Win.C1Gauge.C1GaugeMultivalueDecorator.From" /> value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMultivalueDecorator.ScaleTo">
      <summary>
            Gets or sets the value where the scale graduation ends and the end location and width are defined.
            Set to Double.NaN to bind to the <see cref="P:C1.Win.C1Gauge.C1GaugeMultivalueDecorator.To" /> value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMultivalueDecorator.Location2">
      <summary>
            Gets or sets the end location of the <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> (<see cref="P:C1.Win.C1Gauge.C1GaugeDecorator.Location" /> is the start location).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeScaleDecorator.Interval">
      <summary>
            Value interval to draw each mark or label.
            </summary>
      <remarks>
            If the Gauge uses logarithmic scale this interval must be specified
            in logarithmic coordinates, such as (Log(Value2) - Log(Value1)).
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeScaleDecorator.IntervalCoeff">
      <summary>
            Gets or sets the multiplier for the value interval (or the number of subintervals for logarithmic scale).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeScaleDecorator.IntervalWidth">
      <summary>
            Gets or sets the logical distance between tick marks or labels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeScaleDecorator.Resolution">
      <summary>
            Gets or sets the minimum displayed value interval if the scale is nonuniform.
            </summary>
      <remarks>
            If the Gauge uses logarithmic scale the value resolution must be specified
            in logarithmic coordinates.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeScaleDecorator.ShowIrregularFrom">
      <summary>
            Gets or sets whether the From value should appear even if it doesn't conform with the specified interval.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeScaleDecorator.ShowIrregularTo">
      <summary>
            Gets or sets whether the To value should appear even if it doesn't conform with the specified interval.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeScaleDecorator.SequenceNo">
      <summary>
            The sequence number for this marks (or labels) decorator. Displayed values will not appear
            on the subsequent marks (or labels) with the same sequence number.
            </summary>
      <remarks>
            Set to -1 to ignore the order of this decorator.
            </remarks>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeLabels.ShouldSerializeColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeLabels.ResetColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeLabels.ShouldSerializeIsRotated">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeLabels.ResetIsRotated">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeLabels.ShouldSerializeAllowFlip">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeLabels.ResetAllowFlip">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeLabels.ValueFactor">
      <summary>
            Gets or sets the value multiplier when showing labels.
            </summary>
      <remarks>
            Allows to increase or decrease the displayed value by the specified multiplier.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeLabels.ValueOffset">
      <summary>
            Gets or sets the value offset when showing labels.
            </summary>
      <remarks>
            Allows to increase or decrease the displayed value by adding the specified amount.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeLabels.CommonFontName">
      <summary>
            Gets or sets the name of the font and color template.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeLabels.Color">
      <summary>
            Gets or sets the Color used to display text on labels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeLabels.Font">
      <summary>
            Gets or sets the font used to display text in labels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeLabels.FontSize">
      <summary>
            Logical value representing the scalable font size.
            </summary>
      <remarks>
            Set to Double.NaN to use the fixed font size.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeLabels.FontSize2">
      <summary>
            Gets or sets the end font size of the labels (<see cref="P:C1.Win.C1Gauge.C1GaugeLabels.FontSize" /> is the start font size).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeLabels.Format">
      <summary>
            Standard or custom numeric format string for the labels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeLabels.TextAngle">
      <summary>
            Gets or sets the angle (in degrees) to rotate the label text.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeLabels.OrthogonalAlignment">
      <summary>
            Gets or sets the orthogonal alignment of the text labels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeLabels.OrthogonalOffset">
      <summary>
            Gets or sets the logical orthogonal offset of the text labels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeLabels.IsRotated">
      <summary>
            Gets or sets if the labels should be rotated depending on their values (for radial Gauges only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeLabels.AllowFlip">
      <summary>
            Gets or sets whether the labels should be flipped if they appear inverted (for radial Gauges only).
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeMarks">
      <summary>
        <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> used to display a sequence of tick marks on the Gauge.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.#ctor">
      <summary>
            Initializes a new instance of the decorator.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ShouldSerializeIsRotated">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ResetIsRotated">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ShouldSerializeWidth">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ResetWidth">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ShouldSerializeCustomShape">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ResetCustomShape">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ShouldSerializeBorder">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ResetBorder">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ShouldSerializeFilling">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ResetFilling">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ShouldSerializeGradient">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ResetGradient">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ShouldSerializeCustomImage">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ResetCustomImage">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeMarks.ShouldSerializeValueImages">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.Length">
      <summary>
            Logical value representing the length of each tick mark.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.Length2">
      <summary>
            Gets or sets the end length of the tick marks (<see cref="P:C1.Win.C1Gauge.C1GaugeMarks.Length" /> is the start length).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.ShapeAngle">
      <summary>
            Gets or sets the fixed angle (in degrees) to rotate the tick marks.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.OrthogonalAlignment">
      <summary>
            Gets or sets the orthogonal alignment of the tick marks.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.OrthogonalOffset">
      <summary>
            Gets or sets the logical orthogonal offset of the tick marks.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.IsRotated">
      <summary>
            Gets or sets if the tick marks should be rotated depending on their values (for radial Gauges only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.FlipShape">
      <summary>
            Gets or sets whether the shape of a tick mark is inverted.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.Shape">
      <summary>
            Selects a shape from the set of predefined mark shapes.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.Width">
      <summary>
            Gets or sets the logical width of a tick mark.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.CustomShape">
      <summary>
            Gets the custom shape for the tick marks.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.Border">
      <summary>
            Gets the properties of the tick marks border.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.Filling">
      <summary>
            Adjusts the interior filling of tick marks.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.Gradient">
      <summary>
            Encapsulates properties of a color gradient.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.CustomImage">
      <summary>
            Gets or sets the custom image for the tick marks.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.ImageMapName">
      <summary>
            Gets or sets the name of template for the <see cref="P:C1.Win.C1Gauge.C1GaugeMarks.ValueImages" /> collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeMarks.ValueImages">
      <summary>
            Gets the collection of values with their associated images.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeRange">
      <summary>
        <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> used to display a range on the Gauge.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeRange.#ctor">
      <summary>
            Initializes a new instance of the decorator.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeRange.ShouldSerializeBorder">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeRange.ResetBorder">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeRange.ShouldSerializeFilling">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeRange.ResetFilling">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeRange.ShouldSerializeGradient">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeRange.ResetGradient">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRange.AdjustAngle">
      <summary>
            Gets or sets whether the angle of a linear range should depend on the location difference.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRange.AntiAliasing">
      <summary>
            Gets or sets the anti-aliasing method for the range filling.
            </summary>
      <remarks>
            Applying anti-aliasing to a bound range has significant performance impact.
            Instead, you may draw a border of the same color as the background.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRange.Width">
      <summary>
            Gets or sets the range's logical width.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRange.Width2">
      <summary>
            Gets or sets the end width of the <see cref="T:C1.Win.C1Gauge.C1GaugeRange" /> (<see cref="P:C1.Win.C1Gauge.C1GaugeRange.Width" /> is the start width).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRange.Border">
      <summary>
            Gets the properties of the border of this <see cref="T:C1.Win.C1Gauge.C1GaugeRange" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRange.Filling">
      <summary>
            Adjusts the interior filling of this <see cref="T:C1.Win.C1Gauge.C1GaugeRange" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRange.Gradient">
      <summary>
            Encapsulates properties of a color gradient.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeSingleLabel">
      <summary>
        <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> used to display a single label on the Gauge.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleLabel.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Win.C1Gauge.C1GaugeSingleLabel" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleLabel.ShouldSerializeAngle">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleLabel.ResetAngle">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleLabel.ShouldSerializePosition">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleLabel.ResetPosition">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleLabel.ShouldSerializeColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleLabel.ResetColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleLabel.ShouldSerializeIsRotated">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleLabel.ResetIsRotated">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleLabel.ShouldSerializeAllowFlip">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleLabel.ResetAllowFlip">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.Angle">
      <summary>
            Gets or sets the angle relative to the Gauge pointer origin where the label should appear (for radial Gauges only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.Position">
      <summary>
            Gets or sets the fraction-based position where this label should appear (for linear Gauges only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.Value">
      <summary>
            Gets or sets the value associated with this label.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.PointerIndex">
      <summary>
            Gets or sets the index of a Pointer that gives the value associated with this label.
            </summary>
      <remarks>
            Set to a large value to bind to the main Pointer.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.ImmediateUpdate">
      <summary>
            Gets or sets whether the bound label should ignore the Pointer's sweep time.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.ValueFactor">
      <summary>
            Gets or sets the value multiplier when showing the label.
            </summary>
      <remarks>
            Allows to increase or decrease the displayed value by the specified multiplier.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.ValueOffset">
      <summary>
            Gets or sets the value offset when showing the label.
            </summary>
      <remarks>
            Allows to increase or decrease the displayed value by adding the specified amount.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.SequenceNo">
      <summary>
            The sequence number for this label. Displayed value will not appear
            on the subsequent labels with the same sequence number.
            </summary>
      <remarks>
            Set to -1 to ignore the order of this label.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.Format">
      <summary>
            Standard or custom numeric format string for the label.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.Text">
      <summary>
            Gets or sets the text displayed in this label.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.CommonFontName">
      <summary>
            Gets or sets the name of the font and color template.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.Color">
      <summary>
            Gets or sets the Color used to display text label.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.Font">
      <summary>
            Gets or sets the font used to display text label.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.FontSize">
      <summary>
            Logical value representing the scalable font size.
            </summary>
      <remarks>
            Set to Double.NaN to use the fixed font size.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.TextAngle">
      <summary>
            Gets or sets the angle (in degrees) to rotate the label text.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.OrthogonalAlignment">
      <summary>
            Gets or sets the orthogonal alignment of the text label.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.OrthogonalOffset">
      <summary>
            Gets or sets the logical orthogonal offset of the text label.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.IsRotated">
      <summary>
            Gets or sets if the label should be rotated depending on its angle (for radial Gauges only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleLabel.AllowFlip">
      <summary>
            Gets or sets whether the label should be flipped if it appears inverted (for radial Gauges only).
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeSingleMark">
      <summary>
        <see cref="T:C1.Win.C1Gauge.C1GaugeDecorator" /> used to display a single mark on the Gauge.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.#ctor">
      <summary>
            Initializes a new instance of the decorator.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ShouldSerializeAngle">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ResetAngle">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ShouldSerializePosition">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ResetPosition">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ShouldSerializeIsRotated">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ResetIsRotated">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ShouldSerializeWidth">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ResetWidth">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ShouldSerializeCustomShape">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ResetCustomShape">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ShouldSerializeBorder">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ResetBorder">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ShouldSerializeFilling">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ResetFilling">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ShouldSerializeGradient">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ResetGradient">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ShouldSerializeCustomImage">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ResetCustomImage">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSingleMark.ShouldSerializeValueImages">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.Angle">
      <summary>
            Gets or sets the angle relative to the Gauge pointer origin where the tick mark should appear (for radial Gauges only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.Position">
      <summary>
            Gets or sets the fraction-based position where the tick mark should appear (for linear Gauges only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.Value">
      <summary>
            Gets or sets the value associated with this tick mark.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.PointerIndex">
      <summary>
            Gets or sets the index of a Pointer that gives the value associated with this tick mark.
            </summary>
      <remarks>
            Set to a large value to bind to the main Pointer.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.ImmediateUpdate">
      <summary>
            Gets or sets whether the bound tick mark should ignore the Pointer's sweep time.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.SequenceNo">
      <summary>
            The sequence number for this tick mark. Displayed value will not appear
            on the subsequent marks with the same sequence number.
            </summary>
      <remarks>
            Set to -1 to ignore the order of this tick mark.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.Length">
      <summary>
            Logical value representing the length of this tick mark.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.ShapeAngle">
      <summary>
            Gets or sets the fixed angle (in degrees) to rotate this tick mark.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.OrthogonalAlignment">
      <summary>
            Gets or sets the orthogonal alignment of the tick mark.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.OrthogonalOffset">
      <summary>
            Gets or sets the logical orthogonal offset of the tick mark.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.IsRotated">
      <summary>
            Gets or sets if the tick mark should be rotated depending on its angle (for radial Gauges only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.FlipShape">
      <summary>
            Gets or sets whether the shape of a tick mark is inverted.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.Shape">
      <summary>
            Selects a shape from the set of predefined mark shapes.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.Width">
      <summary>
            Gets or sets the logical width of a tick mark.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.CustomShape">
      <summary>
            Gets the custom shape for this tick mark.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.Border">
      <summary>
            Gets the properties of the tick mark border.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.Filling">
      <summary>
            Adjusts the interior filling of a tick mark.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.Gradient">
      <summary>
            Encapsulates properties of a color gradient.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.CustomImage">
      <summary>
            Gets or sets the custom image for the tick mark.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.ImageMapName">
      <summary>
            Gets or sets the name of template for the <see cref="P:C1.Win.C1Gauge.C1GaugeSingleMark.ValueImages" /> collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSingleMark.ValueImages">
      <summary>
            Gets the collection of values with their associated images.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeBaseShape">
      <summary>
            The base class for shapes, such as <see cref="T:C1.Win.C1Gauge.C1GaugeRectangle" /> or <see cref="T:C1.Win.C1Gauge.C1GaugeSector" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBaseShape.#ctor">
      <summary>
            Initializes a new instance of the shape.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBaseShape.ShouldSerializeViewport">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBaseShape.ShouldSerializeClippings">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeBaseShape.ToString">
      <summary>
            Returns a string that represents the current object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBaseShape.Owner">
      <summary>
            Gets the owner Gauge element.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBaseShape.HitTestable">
      <summary>
            Gets or sets whether the shape can be hit-tested.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBaseShape.Name">
      <summary>
            Gets or sets a name associated with the shape.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBaseShape.CenterPointX">
      <summary>
            Gets or sets the fraction-based X position of the shape center.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBaseShape.CenterPointY">
      <summary>
            Gets or sets the fraction-based Y position of the shape center.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBaseShape.Viewport">
      <summary>
            Specifies the outer bounds for the shape.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBaseShape.Clippings">
      <summary>
            Gets the collection of shapes that form the clipping region.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeBaseShape.Visible">
      <summary>
            Gets or sets whether the shape is visible.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeCaption">
      <summary>
            Draws a text caption.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCaption.#ctor">
      <summary>
            Initializes a new instance of the shape.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCaption.ShouldSerializeColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeCaption.ResetColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.Text">
      <summary>
            Gets or sets the text displayed in this caption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.CommonFontName">
      <summary>
            Gets or sets the name of the font and color template.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.Color">
      <summary>
            Gets or sets the Color used to display text caption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.Font">
      <summary>
            Gets or sets the font used to display text caption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.FontSize">
      <summary>
            Logical value representing the scalable font size.
            </summary>
      <remarks>
            Set to Double.NaN to use the fixed font size.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.NoClip">
      <summary>
            If set to True, unwrapped text reaching outside the formatting rectangle is allowed to show.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.NoWrap">
      <summary>
            If set to True, text wrapping between lines when formatting within a rectangle is disabled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.Trimming">
      <summary>
            Indicates how text is trimmed when it exceeds the edges of the layout rectangle.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.DirectionRightToLeft">
      <summary>
            Gets or sets whether the Text is displayed from right to left.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.DirectionVertical">
      <summary>
            Gets or sets whether the Text is displayed in vertical direction.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.Alignment">
      <summary>
            Gets or sets text alignment information on the vertical plane.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.LineAlignment">
      <summary>
            Gets or sets the line alignment on the horizontal plane.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.RotateAngle">
      <summary>
            Gets or sets the rotation angle (pivot point is at the center of the caption).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.Width">
      <summary>
            Gets or sets the logical (positive) or fraction-based (negative) width of the caption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeCaption.Height">
      <summary>
            Gets or sets the logical (positive) or fraction-based (negative) height of the caption.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeEllipse">
      <summary>
            Draws a filled ellipse.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeFilledShape">
      <summary>
            The base class for shapes that draw the border and fill the interior.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeFilledShape.#ctor">
      <summary>
            Initializes a new instance of the shape.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeFilledShape.ShouldSerializeBorder">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeFilledShape.ResetBorder">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeFilledShape.ShouldSerializeFilling">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeFilledShape.ResetFilling">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeFilledShape.ShouldSerializeGradient">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeFilledShape.ResetGradient">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeFilledShape.Border">
      <summary>
            Gets the properties of the shape border.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeFilledShape.Filling">
      <summary>
            Adjusts the interior filling of the shape.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeFilledShape.Gradient">
      <summary>
            Encapsulates properties of a color gradient.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeEllipse.#ctor">
      <summary>
            Initializes a new instance of the shape.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeEllipse.Width">
      <summary>
            Gets or sets the logical (positive) or fraction-based (negative) width of the ellipse.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeEllipse.Height">
      <summary>
            Gets or sets the logical (positive) or fraction-based (negative) height of the ellipse.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeEllipse.RotateAngle">
      <summary>
            Gets or sets the rotation angle (pivot point is at the center of the ellipse).
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeImage">
      <summary>
            Draws an image.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeImage.#ctor">
      <summary>
            Initializes a new instance of the shape.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeImage.Width">
      <summary>
            Gets or sets the logical (positive) or fraction-based (negative) width of the image.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeImage.Height">
      <summary>
            Gets or sets the logical (positive) or fraction-based (negative) height of the image.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeImage.RotateAngle">
      <summary>
            Gets or sets the rotation angle (pivot point is at the center of the image).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeImage.CommonImageName">
      <summary>
            Gets or sets the name of the image template.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeImage.FlipType">
      <summary>
            Gets or sets the axis used to flip the image.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeImage.Image">
      <summary>
            Gets or sets the image.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeImage.KeepAspectRatio">
      <summary>
            Gets or sets whether the image aspect ratio should be maintained.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeImage.KeepSize">
      <summary>
            Gets or sets whether the predefined size of the image should not changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeImage.Hue">
      <summary>
            Allows to modify the image hue by the specified amount (between -180 and 180).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeImage.Saturation">
      <summary>
            Allows to modify the image saturation by the specified amount (between -100 and 100).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeImage.Lightness">
      <summary>
            Allows to modify the image lightness by the specified amount (between -100 and 100).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeImage.Opacity">
      <summary>
            Gets or sets the opacity of the image between 0.0 (completely invisible) and 1.0 (opaque).
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeRectangle">
      <summary>
            Draws a filled rectangle.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeRectangle.#ctor">
      <summary>
            Initializes a new instance of the shape.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRectangle.Width">
      <summary>
            Gets or sets the logical (positive) or fraction-based (negative) width of the rectangle.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRectangle.Height">
      <summary>
            Gets or sets the logical (positive) or fraction-based (negative) height of the rectangle.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRectangle.SlantAngle">
      <summary>
            Gets or sets the slant angle of the left side (or both sides).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRectangle.SlantAngle2">
      <summary>
            Gets or sets the slant angle of the right side.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRectangle.CornerRadius">
      <summary>
            Gets or sets the default radius for the rectangle corners.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRectangle.RotateAngle">
      <summary>
            Gets or sets the rotation angle (pivot point is at the center of the rectangle).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRectangle.LeftTop">
      <summary>
            Gets or sets the radius for the left top corner.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRectangle.LeftBottom">
      <summary>
            Gets or sets the radius for the left bottom corner.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRectangle.RightTop">
      <summary>
            Gets or sets the radius for the right top corner.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeRectangle.RightBottom">
      <summary>
            Gets or sets the radius for the right bottom corner.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeSector">
      <summary>
            Draws a filled circular sector.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSector.#ctor">
      <summary>
            Initializes a new instance of the shape.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSector.OuterRadius">
      <summary>
            Gets or sets the logical outer radius of the sector.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSector.CenterRadius">
      <summary>
            Gets or sets the logical radius of the sector center.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSector.InnerOffset">
      <summary>
            Gets or sets the logical offset of the inner circle (if the <see cref="P:C1.Win.C1Gauge.C1GaugeSector.SweepAngle" /> is less than 180 degrees).
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSector.InnerRadius">
      <summary>
            Gets or sets the logical inner radius of the sector.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSector.CornerRadius">
      <summary>
            Gets or sets the radius for the sector corners.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSector.StartAngle">
      <summary>
            Start angle for the <see cref="T:C1.Win.C1Gauge.C1GaugeSector" />. 0 is the topmost point of the circumference.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSector.SweepAngle">
      <summary>
            Sweep angle for the <see cref="T:C1.Win.C1Gauge.C1GaugeSector" />.
            </summary>
    </member>
    <member name="T:C1.Win.C1Gauge.C1GaugeSegment">
      <summary>
            Draws a filled circular segment.
            </summary>
    </member>
    <member name="M:C1.Win.C1Gauge.C1GaugeSegment.#ctor">
      <summary>
            Initializes a new instance of the shape.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSegment.OuterRadius">
      <summary>
            Gets or sets the logical radius of the circular segment.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSegment.InnerRadius">
      <summary>
            Gets or sets the radius of the 'chord' line.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSegment.CornerRadius">
      <summary>
            Gets or sets the radius for the segment corners.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSegment.StartAngle">
      <summary>
            Start angle for the <see cref="T:C1.Win.C1Gauge.C1GaugeSegment" />. 0 is the topmost point of the circumference.
            </summary>
    </member>
    <member name="P:C1.Win.C1Gauge.C1GaugeSegment.SweepAngle">
      <summary>
            Sweep angle for the <see cref="T:C1.Win.C1Gauge.C1GaugeSegment" />.
            </summary>
    </member>
  </members>
</doc>