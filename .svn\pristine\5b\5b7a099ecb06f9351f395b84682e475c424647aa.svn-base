﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Imports C1.C1Excel
Imports Microsoft.Office.Interop

Public Class fFicheReleveeMutuelle
    Public NuemroReleve As String = ""

    Dim x As Integer
    Dim cmdReleve As New SqlCommand
    Dim daReleve As New SqlDataAdapter
    Dim dsReleve As New DataSet
    Dim cbReleve As SqlCommandBuilder

    Dim ValeurLibelle1 As String = ""
    Dim ValeurLibelle2 As String = ""
    Dim ValeurLibelle3 As String = ""
    Dim ValeurLibelle4 As String = ""
    Dim ValeurLibelle5 As String = ""

    Dim strExcel As String


    Public Sub Init()
        Dim StrSQL As String = ""
        x = 0

        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler

        AfficherReleve()

        CalculerMontants()

        If cmbMutuelle.Text = "LEONI" Then
            GroupBoxGenerer.Visible = True
        End If
    End Sub

    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)
        If e.KeyCode = Keys.F3 Then
            bValiderReleve_Click(o, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F12 Then
            bAnnuler_Click(o, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F9 Then
            bimprimer_Click(o, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F8 Then
            bReglement_Click(o, e)
            Exit Sub
        End If

    End Sub
    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "114" And bValiderReleve.Enabled = True Then
            bValiderReleve_Click(sender, e)
        End If
        If argument = "119" And bReglement.Enabled = True Then
            bReglement_Click(sender, e)
        End If
        If argument = "123" And bAnnuler.Enabled = True Then
            bAnnuler_Click(sender, e)
        End If

    End Sub
    Public Sub AfficherReleve()

        Dim I As Integer
        Dim Cond As String = "1=1"
        Dim StrSQL As String = ""

        Dim LienDeParentePremiereLigne As String = ""
        Dim CodeApciPremiereLigne As String = ""



        If (dsReleve.Tables.IndexOf("RELEVE") > -1) Then
            dsReleve.Tables("RELEVE").Clear()
        End If
        If (dsReleve.Tables.IndexOf("RELEVE_MUTUELLE_DETAILS") > -1) Then
            dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Clear()
        End If

        If (dsReleve.Tables.IndexOf("RELEVE_MUTUELLE_DETAILS_Excel") > -1) Then
            dsReleve.Tables("RELEVE_MUTUELLE_DETAILS_Excel").Clear()
        End If

        If (dsReleve.Tables.IndexOf("LIEN_PARENTE") > -1) Then
            dsReleve.Tables("LIEN_PARENTE").Clear()
        End If

        'chargement des LIEN DE PARENTE
        StrSQL = "SELECT CodeLienDeParente,LibelleLienDeParente FROM LIEN_PARENTE ORDER BY LibelleLienDeParente ASC"
        cmdReleve.Connection = ConnectionServeur
        cmdReleve.CommandText = StrSQL
        daReleve = New SqlDataAdapter(cmdReleve)
        daReleve.Fill(dsReleve, "LIEN_PARENTE")

        'chargement des Mutuelle
        StrSQL = "SELECT CodeMutuelle,NomMutuelle FROM MUTUELLE ORDER BY NomMutuelle ASC"
        cmdReleve.Connection = ConnectionServeur
        cmdReleve.CommandText = StrSQL
        daReleve = New SqlDataAdapter(cmdReleve)
        daReleve.Fill(dsReleve, "MUTUELLE")
        cmbMutuelle.DataSource = dsReleve.Tables("MUTUELLE")
        cmbMutuelle.ValueMember = "CodeMutuelle"
        cmbMutuelle.DisplayMember = "NomMutuelle"
        cmbMutuelle.ColumnHeaders = False
        cmbMutuelle.Splits(0).DisplayColumns("CodeMutuelle").Visible = False
        cmbMutuelle.Splits(0).DisplayColumns("NomMutuelle").Width = 10
        cmbMutuelle.ExtendRightColumn = True

        cmdReleve.CommandText = " SELECT * FROM RELEVE_MUTUELLE WHERE NumeroReleve = " + Quote(NuemroReleve)

        cmdReleve.Connection = ConnectionServeur
        daReleve = New SqlDataAdapter(cmdReleve)
        daReleve.Fill(dsReleve, "RELEVE")

        lNumero.Text = dsReleve.Tables("RELEVE").Rows(0).Item("NumeroReleve")
        lDate.Text = dsReleve.Tables("RELEVE").Rows(0).Item("Date")
        lDu.Text = dsReleve.Tables("RELEVE").Rows(0).Item("DateDebut")
        lAu.Text = dsReleve.Tables("RELEVE").Rows(0).Item("DateFin")
        lTotal.Text = dsReleve.Tables("RELEVE").Rows(0).Item("Total")
        lMontant.Text = dsReleve.Tables("RELEVE").Rows(0).Item("Montant")
        lReste.Text = dsReleve.Tables("RELEVE").Rows(0).Item("Reste")
        cmbMutuelle.SelectedValue = Trim(dsReleve.Tables("RELEVE").Rows(0).Item("CodeMutuelle"))

        cmdReleve.CommandText = " SELECT " + _
                                " RELEVE_MUTUELLE_DETAILS.NumeroVente, " + _
                                " RELEVE_MUTUELLE_DETAILS.Date, " + _
                                " CLIENT.Nom, " + _
                                " CLIENT.MatriculeMutuelle, " + _
                                " RELEVE_MUTUELLE_DETAILS.TotalTTC, " + _
                                " RELEVE_MUTUELLE_DETAILS.MontantMutuelle, " + _
                                " RELEVE_MUTUELLE_DETAILS.Regle, " + _
                                " VENTE.MontantMutuelle- ISNULL((SELECT SUM(MontantRegle) FROM REGLEMENT_MUTUELLE_VENTE WHERE RELEVE_MUTUELLE_DETAILS.NumeroVente=NumeroVente), 0) AS Reste, " + _
                                " VENTE.Libelle1," + _
                                " VENTE.Libelle2," + _
                                " VENTE.Libelle3," + _
                                " VENTE.Libelle4," + _
                                " VENTE.Libelle5 " + _
                                " FROM RELEVE_MUTUELLE_DETAILS" + _
                                " LEFT OUTER JOIN CLIENT ON RELEVE_MUTUELLE_DETAILS.CodeClient =CLIENT.CodeClient  " + _
                                " LEFT OUTER JOIN VENTE ON RELEVE_MUTUELLE_DETAILS.NumeroVente =VENTE.NumeroVente " + _
                                " WHERE NumeroReleve=" + Quote(NuemroReleve) + _
                                " ORDER BY NumeroVente"


        cmdReleve.Connection = ConnectionServeur
        daReleve = New SqlDataAdapter(cmdReleve)
        daReleve.Fill(dsReleve, "RELEVE_MUTUELLE_DETAILS")
        cbReleve = New SqlCommandBuilder(daReleve)

        With gReleves
            .Columns.Clear()
            .DataSource = dsReleve
            .DataMember = "RELEVE_MUTUELLE_DETAILS"
            .Rebind(False)
            .Columns("NumeroVente").Caption = "Num Vente"
            .Columns("Date").Caption = "Date"
            .Columns("Nom").Caption = "Client"
            .Columns("MatriculeMutuelle").Caption = "Matricule"
            .Columns("TotalTTC").Caption = "Total"
            .Columns("MontantMutuelle").Caption = "M A Rem"
            .Columns("Regle").Caption = "Déjà Reglé"
            .Columns("Reste").Caption = "Reste"
            .Columns("Libelle1").Caption = "Libelle1"
            .Columns("Libelle2").Caption = "Libelle2"
            .Columns("Libelle3").Caption = "Libelle3"
            .Columns("Libelle4").Caption = "Libelle4"
            .Columns("Libelle5").Caption = "Libelle5"

            ' Centrer tous les entêtes SPLIT 0 
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("NumeroVente").Width = 120
            .Splits(0).DisplayColumns("Date").Width = 100
            .Splits(0).DisplayColumns("Nom").Width = 200
            .Splits(0).DisplayColumns("Nom").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("MatriculeMutuelle").Width = 80
            .Splits(0).DisplayColumns("MatriculeMutuelle").Visible = False
            .Splits(0).DisplayColumns("TotalTTC").Width = 70
            .Splits(0).DisplayColumns("TotalTTC").Visible = False
            .Splits(0).DisplayColumns("Regle").Width = 70
            .Splits(0).DisplayColumns("Regle").Visible = False
            .Splits(0).DisplayColumns("MontantMutuelle").Width = 70
            .Splits(0).DisplayColumns("MontantMutuelle").Visible = False
            .Splits(0).DisplayColumns("Reste").Width = 70
            .Splits(0).DisplayColumns("Reste").Visible = False
            .Splits(0).DisplayColumns("Libelle1").Width = 60
            .Splits(0).DisplayColumns("Libelle1").Visible = False
            .Splits(0).DisplayColumns("Libelle2").Width = 60
            .Splits(0).DisplayColumns("Libelle2").Visible = False
            .Splits(0).DisplayColumns("Libelle3").Width = 60
            .Splits(0).DisplayColumns("Libelle3").Visible = False
            .Splits(0).DisplayColumns("Libelle4").Width = 60
            .Splits(0).DisplayColumns("Libelle4").Visible = False
            .Splits(0).DisplayColumns("Libelle5").Width = 60
            .Splits(0).DisplayColumns("Libelle5").Visible = False

            .Splits(0).SplitSize = 360
            .Splits(0).SplitSizeMode = SizeModeEnum.NumberOfColumns
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True


            'deuxieme splits 

            ' Centrer tous les entêtes SPLIT 0 
            For I = 0 To .Columns.Count - 1
                .Splits(1).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                .Splits(1).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
                .Splits(1).DisplayColumns(I).Locked = True
            Next

            .Splits(1).DisplayColumns("NumeroVente").Width = 100
            .Splits(1).DisplayColumns("NumeroVente").Visible = False
            .Splits(1).DisplayColumns("Date").Width = 100
            .Splits(1).DisplayColumns("Date").Visible = False
            .Splits(1).DisplayColumns("Nom").Width = 200
            .Splits(1).DisplayColumns("Nom").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(1).DisplayColumns("Nom").Visible = False
            .Splits(1).DisplayColumns("MatriculeMutuelle").Width = 80
            .Splits(1).DisplayColumns("TotalTTC").Width = 80
            .Splits(1).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("Regle").Width = 80
            .Splits(1).DisplayColumns("Regle").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("MontantMutuelle").Width = 80
            .Splits(1).DisplayColumns("MontantMutuelle").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("Reste").Width = 80
            .Splits(1).DisplayColumns("Reste").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(1).DisplayColumns("Libelle1").Width = 60
            .Splits(1).DisplayColumns("Libelle2").Width = 60
            .Splits(1).DisplayColumns("Libelle3").Width = 60
            .Splits(1).DisplayColumns("Libelle4").Width = 60
            .Splits(1).DisplayColumns("Libelle5").Width = 60

            '.Splits(1).SplitSizeMode = SizeModeEnum.NumberOfColumns
            .Splits(1).ColumnCaptionHeight = 40
            .Splits(1).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gReleves)
        End With

        If cmbMutuelle.Text <> "" Then
            ValeurLibelle1 = RecupererValeurExecuteScalaire("Libelle1", "MUTUELLE", "CodeMutuelle", cmbMutuelle.SelectedValue)
            ValeurLibelle2 = RecupererValeurExecuteScalaire("Libelle2", "MUTUELLE", "CodeMutuelle", cmbMutuelle.SelectedValue)
            ValeurLibelle3 = RecupererValeurExecuteScalaire("Libelle3", "MUTUELLE", "CodeMutuelle", cmbMutuelle.SelectedValue)
            ValeurLibelle4 = RecupererValeurExecuteScalaire("Libelle4", "MUTUELLE", "CodeMutuelle", cmbMutuelle.SelectedValue)
            ValeurLibelle5 = RecupererValeurExecuteScalaire("Libelle5", "MUTUELLE", "CodeMutuelle", cmbMutuelle.SelectedValue)

            With gReleves
                .Columns("Libelle1").Caption = ValeurLibelle1
                .Columns("Libelle2").Caption = ValeurLibelle2
                .Columns("Libelle3").Caption = ValeurLibelle3
                .Columns("Libelle4").Caption = ValeurLibelle4
                .Columns("Libelle5").Caption = ValeurLibelle5
            End With

            With gReleves
                .Splits(1).DisplayColumns("Libelle1").Locked = False
                .Splits(1).DisplayColumns("Libelle2").Locked = False
                .Splits(1).DisplayColumns("Libelle3").Locked = False
                .Splits(1).DisplayColumns("Libelle4").Locked = False
                .Splits(1).DisplayColumns("Libelle5").Locked = False
                gReleves.Splits(1).DisplayColumns("Libelle1").Style.BackColor = Color.FromArgb(255, 200, 252, 251)
                gReleves.Splits(1).DisplayColumns("Libelle2").Style.BackColor = Color.FromArgb(255, 200, 252, 251)
                gReleves.Splits(1).DisplayColumns("Libelle3").Style.BackColor = Color.FromArgb(255, 200, 252, 251)
                gReleves.Splits(1).DisplayColumns("Libelle4").Style.BackColor = Color.FromArgb(255, 200, 252, 251)
                gReleves.Splits(1).DisplayColumns("Libelle5").Style.BackColor = Color.FromArgb(255, 200, 252, 251)
            End With

        End If


        strExcel = " SELECT " + _
                               " CLIENT.MatriculeMutuelle as Matricule, " + _
                               " CLIENT.Nom, " + _
                               " RELEVE_MUTUELLE_DETAILS.TotalTTC as Total, " + _
                               " RELEVE_MUTUELLE_DETAILS.MontantMutuelle as Montant, " + _
                               " VENTE.Libelle1," + _
                               " VENTE.Libelle2," + _
                               " VENTE.Libelle3," + _
                               " VENTE.Libelle4," + _
                               " VENTE.Libelle5 " + _
                               " FROM RELEVE_MUTUELLE_DETAILS" + _
                               " LEFT OUTER JOIN CLIENT ON RELEVE_MUTUELLE_DETAILS.CodeClient =CLIENT.CodeClient  " + _
                               " LEFT OUTER JOIN VENTE ON RELEVE_MUTUELLE_DETAILS.NumeroVente =VENTE.NumeroVente " + _
                               " WHERE NumeroReleve=" + Quote(NuemroReleve)
        '" ORDER BY NumeroVente"
        cmdReleve.Connection = ConnectionServeur
        cmdReleve.CommandText = strExcel
        daReleve = New SqlDataAdapter(cmdReleve)
        daReleve.Fill(dsReleve, "RELEVE_MUTUELLE_DETAILS_Excel")
        cbReleve = New SqlCommandBuilder(daReleve)

        With gReleveExcel
            .Columns.Clear()
            .DataSource = dsReleve
            .DataMember = "RELEVE_MUTUELLE_DETAILS_Excel"
        End With


    End Sub

    Private Sub PAnel_Paint(ByVal sender As Object, ByVal e As System.Windows.Forms.PaintEventArgs) Handles PAnel.Paint
        'AfficherReleve()
    End Sub


    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Me.Hide()
        'fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub bReglement_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bReglement.Click
        Dim I As Integer = 0
        Dim AfficherLaTotaliteDuMontant As Boolean = False
        Dim Reste As Double = 0

        AfficherLaTotaliteDuMontant = RecupererValeurExecuteScalaire("AfficherLaTotaliteDuMontant", "MUTUELLE", "CodeMutuelle", cmbMutuelle.SelectedValue)
        Reste = RecupererValeurExecuteScalaire("Reste", "RELEVE_MUTUELLE", "NumeroReleve", lNumero.Text)
        If Reste = 0 Then
            MsgBox("Relevé déja réglé !", MsgBoxStyle.Information, "Information")
            Exit Sub
        End If

        With gReleves

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(1).DisplayColumns(I).Locked = True
            Next

            .Splits(1).DisplayColumns("Reste").Locked = False
            .Columns("Reste").Caption = "à Régler"

            For I = 0 To gReleves.RowCount - 1
                If AfficherLaTotaliteDuMontant = True Then
                    gReleves(I, "Reste") = gReleves(I, "MontantMutuelle") - gReleves(I, "Regle")
                Else
                    gReleves(I, "Reste") = 0
                End If
            Next

        End With

        For I = 0 To gReleves.Columns.Count - 1
            gReleves.Splits(1).DisplayColumns(I).Style.BackColor = Color.White
        Next
        gReleves.Splits(1).DisplayColumns("Reste").Style.BackColor = Color.FromArgb(255, 200, 252, 251)

        CalculerMontants()

        bReglement.Enabled = False
    End Sub

    Private Sub bValiderReleve_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bValiderReleve.Click
        Dim I As Integer = 0
        Dim MontantDuReglement As Double = 0.0

        Dim StrSQL As String = ""
        Dim NumeroReglement As Integer = 0

        Dim CodeNatureReglement As Integer = 0
        Dim DateEcheanceReglement As String = ""
        Dim CodeBanque As String = ""
        Dim NumeroCheque As String = ""
        Dim LibelleReglement As String = ""
        Dim NomInscritSurLeCheque As String = ""
        Dim Montant As Double = 0.0
        Dim Encaisse As Boolean = False
        Dim ConfirmerReglement As Boolean = False
        Dim CodeOperateur As String = ""

        Dim MontantRegle As Double = 0.0
        Dim ReglementNonVide As Boolean = False

        Dim MontantRetenueSource As Double = 0.0
        Dim AppliquerRetenueSource As Boolean = False

        '---------------------------------------------------------------------------------------------------
        Dim NumeroVente As String = ""
        For I = 0 To gReleves.RowCount - 1
            NumeroVente = NumeroVente + "," & Quote(gReleves(I, "NumeroVente"))
        Next

        StrSQL = "DELETE FROM RELEVE_MUTUELLE_DETAILS WHERE NumeroVente NOT IN ('1' " & NumeroVente & " ) AND NumeroReleve = " & Quote(lNumero.Text)
        cmdReleve.Connection = ConnectionServeur
        cmdReleve.CommandText = StrSQL
        Try
            cmdReleve.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
            Exit Sub
        End Try


        StrSQL = "UPDATE RELEVE_MUTUELLE SET Total = (SELECT SUM(TotalTTC) FROM RELEVE_MUTUELLE_DETAILS WHERE NumeroReleve = " & Quote(lNumero.Text) & ") WHERE NumeroReleve = " & Quote(lNumero.Text)
        cmdReleve.Connection = ConnectionServeur
        cmdReleve.CommandText = StrSQL
        Try
            cmdReleve.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        StrSQL = "UPDATE RELEVE_MUTUELLE SET Montant = (SELECT SUM(MontantMutuelle) FROM RELEVE_MUTUELLE_DETAILS WHERE NumeroReleve = " & Quote(lNumero.Text) & ") WHERE NumeroReleve = " & Quote(lNumero.Text)
        cmdReleve.Connection = ConnectionServeur
        cmdReleve.CommandText = StrSQL
        Try
            cmdReleve.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        StrSQL = "UPDATE RELEVE_MUTUELLE SET Reste = (SELECT SUM(MontantMutuelle - Regle) FROM RELEVE_MUTUELLE_DETAILS WHERE NumeroReleve = " & Quote(lNumero.Text) & ") WHERE NumeroReleve = " & Quote(lNumero.Text)
        cmdReleve.Connection = ConnectionServeur
        cmdReleve.CommandText = StrSQL
        Try
            cmdReleve.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '****************************************************************************************************
        '*********************************** CAS OU ON A UN REGLEMENT ***************************************
        '****************************************************************************************************
        If gReleves.Splits(1).DisplayColumns("Reste").Locked = False Then

            For I = 0 To gReleves.RowCount - 1
                If gReleves(I, "Reste") <> 0 Then
                    ReglementNonVide = True
                End If
            Next

            If ReglementNonVide = False Then
                MsgBox("Règlement vide !", MsgBoxStyle.Information, "Information")
                Exit Sub
            End If


            For I = 0 To gReleves.RowCount - 1
                If IsDBNull(gReleves(I, "reste")) = False Then
                    MontantDuReglement += gReleves(I, "Reste")
                End If
            Next

            If MontantDuReglement = 0 Then
                Exit Sub
            End If

            '------------------ enregistrement des règlements de chaque vente et du règlement global du CNAM

            If MontantDuReglement <> 0 Then
                Dim MyReglemnt As New fInformationReglement
                MyReglemnt.Montant = MontantDuReglement
                MyReglemnt.ShowDialog()

                CodeNatureReglement = MyReglemnt.CodeNatureReglement
                DateEcheanceReglement = MyReglemnt.DateEcheanceReglement
                CodeBanque = MyReglemnt.CodeBanque
                NumeroCheque = MyReglemnt.NumeroCheque
                LibelleReglement = MyReglemnt.LibelleReglement
                NomInscritSurLeCheque = MyReglemnt.NomInscritSurLeCheque
                Montant = MyReglemnt.Montant
                Encaisse = MyReglemnt.Encaisse
                CodeOperateur = MyReglemnt.CodeOperateurLocal

                AppliquerRetenueSource = MyReglemnt.AppliquerRetenueSource
                MontantRetenueSource = MyReglemnt.MontantRetenueSource

                ConfirmerReglement = MyReglemnt.ConfirmerReglement

                MyReglemnt.Dispose()
                MyReglemnt.Close()

            End If

            If ConfirmerReglement = False Then
                Exit Sub
            End If

            'initialisation d'un nouveau règlement 
            StrSQL = " SELECT max([NumeroReglementMutuelle]) FROM REGLEMENT_MUTUELLE"
            cmdReleve.Connection = ConnectionServeur
            cmdReleve.CommandText = StrSQL
            Try
                NumeroReglement = cmdReleve.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            NumeroReglement = NumeroReglement + 1

            StrSQL = "INSERT INTO REGLEMENT_MUTUELLE " + _
                    "(""NumeroReglementMutuelle"",""LibelleReglement"",""CodeNatureReglement""" + _
                    ",""Date"",""DateEcheance"",""Montant"",""NumeroCheque"",""LibellePoste""" + _
                    ",""NomInscritSurLeCheque"",""CodeMutuelle"",""CodeBanque"",""Vider"",""Encaisse"",""CodePersonnel"", AppliquerRetenueSource, MontantRetenueSource) " + _
                    " VALUES('" + NumeroReglement.ToString + _
                    "','" + LibelleReglement + _
                    "','" + CodeNatureReglement.ToString + _
                    "','" + System.DateTime.Now.ToString + _
                    "','" + DateEcheanceReglement.ToString + _
                    "','" + Montant.ToString + _
                    "','" + NumeroCheque + _
                    "','" + System.Environment.GetEnvironmentVariable("Poste") + _
                    "','" + NomInscritSurLeCheque + _
                    "','" + cmbMutuelle.SelectedValue.ToString + _
                    "'," + CodeBanque.ToString + _
                    ",'" + "False" + _
                    "','" + "False" + _
                    "'," + Quote(CodeOperateur.ToString) + _
                    "," + Quote(AppliquerRetenueSource.ToString) + _
                    "," + Quote(MontantRetenueSource.ToString) + _
                    " )"

            cmdReleve.Connection = ConnectionServeur
            cmdReleve.CommandText = StrSQL
            Try
                cmdReleve.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            For I = 0 To dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Rows.Count - 1

                If dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Rows(I).Item("Reste") > 0 Then
                    'afféctation des règlements aux vente dans la table REGLEMENT_CNAM_VENTE
                    StrSQL = "INSERT INTO REGLEMENT_MUTUELLE_VENTE " + _
                        "(""NumeroReglementMutuelle"",""NumeroReleve"",""NumeroVente""" + _
                        ",""MontantRegle"") " + _
                        " VALUES('" + NumeroReglement.ToString + _
                        "','" + NuemroReleve + _
                        "','" + dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Rows(I).Item("NumeroVente") + _
                        "','" + dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Rows(I).Item("Reste").ToString + _
                        "' )"

                    cmdReleve.Connection = ConnectionServeur
                    cmdReleve.CommandText = StrSQL
                    Try
                        cmdReleve.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                    'changement des valeur reglé dans le fichier RELEVE_CNAM_DETAILS
                    StrSQL = "UPDATE RELEVE_MUTUELLE_DETAILS SET Regle=Regle+ " + _
                       dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Rows(I).Item("Reste").ToString + _
                       " WHERE NumeroReleve='" + NuemroReleve + _
                       "' AND NumeroVente='" + dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Rows(I).Item("NumeroVente") + "'"

                    cmdReleve.Connection = ConnectionServeur
                    cmdReleve.CommandText = StrSQL
                    Try
                        cmdReleve.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                    MontantRegle += dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Rows(I).Item("Reste").ToString
                End If
            Next
            'mise à jour du reste du releve 
            StrSQL = "UPDATE RELEVE_MUTUELLE SET Reste=Reste- " + _
                     MontantRegle.ToString + _
                     " WHERE NumeroReleve='" + NuemroReleve + "'"

            cmdReleve.Connection = ConnectionServeur
            cmdReleve.CommandText = StrSQL
            Try
                cmdReleve.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            MsgBox("Règlement terminée avec succés !", MsgBoxStyle.Information, "Information")
            InsertionDansLog("REGLEMENT_RELEVE_MUTUELLE", "Reglement du relevé  " + NuemroReleve, CodeOperateur, System.DateTime.Now, "MUTUELLE", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
            Me.Hide()

        Else
            '****************************************************************************************************
            '*********************************** CAS OU ON A UNE MODIFICATION ***********************************
            '****************************************************************************************************
            For I = 0 To dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Rows.Count - 1

                'changement des valeur des libellés dans la table vente  
                StrSQL = "UPDATE VENTE SET Libelle1='" + _
                   dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Rows(I).Item("Libelle1").ToString + _
                   "',Libelle2='" + _
                   dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Rows(I).Item("Libelle2").ToString + _
                   "',Libelle3='" + _
                   dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Rows(I).Item("Libelle3").ToString + _
                   "',Libelle4='" + _
                   dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Rows(I).Item("Libelle4").ToString + _
                   "',Libelle5='" + _
                   dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Rows(I).Item("Libelle5").ToString + _
                   "' WHERE " + _
                   "  NumeroVente='" + dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").Rows(I).Item("NumeroVente") + "'"

                cmdReleve.Connection = ConnectionServeur
                cmdReleve.CommandText = StrSQL
                Try
                    cmdReleve.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
            Next

            MsgBox("Modification terminée avec succés !", MsgBoxStyle.Information, "Information")
            'fMain.Tab.SelectedTab.Dispose()
            Me.Hide()

        End If

    End Sub

    Private Sub gReleves_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gReleves.Click

    End Sub

    Private Sub gReleves_DoubleClick(sender As Object, e As System.EventArgs) Handles gReleves.DoubleClick
        Dim MyVenteAffiche As New fVenteJusteAffichage
        MyVenteAffiche.NumeroVente = gReleves(gReleves.Row, "NumeroVente")
        MyVenteAffiche.ShowDialog()
        MyVenteAffiche.Close()
        MyVenteAffiche.Dispose()
    End Sub

    Private Sub gReleves_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gReleves.KeyUp

        If e.KeyCode = Keys.F1 Then
            Dim MyVenteAffiche As New fVenteJusteAffichage
            MyVenteAffiche.NumeroVente = gReleves(gReleves.Row, "NumeroVente")
            MyVenteAffiche.ShowDialog()
            MyVenteAffiche.Close()
            MyVenteAffiche.Dispose()
        End If


        If e.KeyCode = Keys.Enter Then
            CalculerMontants()
        End If

        If gReleves.Splits(0).DisplayColumns("Reste").Locked = False Then
            If IsNumeric(gReleves.Columns("Reste").Value) = False Then
                gReleves.Columns("Reste").Value = 0
            Else
                If gReleves.Columns("Reste").Value.ToString <> "" And IsDBNull(gReleves.Columns("Reste").Value) = False Then
                    If (gReleves.Columns("Reste").Value > gReleves.Columns("MontantMutuelle").Value) Then
                        MsgBox("Réglement > Montant à Remboursé !", MsgBoxStyle.Critical, "Erreur")
                        gReleves.Columns("Reste").Value = gReleves.Columns("MontantMutuelle").Value - gReleves.Columns("Regle").Value
                        gReleves.EditActive = False

                    End If
                End If
            End If
        End If

    End Sub

    Private Sub fFicheReleveeMutuelle_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Init()
    End Sub

    Private Sub bimprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bimprimer.Click

        Dim CondCrystal As String = ""

        Dim FieldDef As CrystalDecisions.CrystalReports.Engine.FieldDefinition
        Dim OrdreBy As String = ""


        If dsReleve.Tables("RELEVE").Rows.Count = 0 Then
            MsgBox("Liste vide !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If


        CondCrystal = "1=1 AND {Vue_EtatReleveMutuelle.NumeroReleve} = '" + lNumero.Text + "'"

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression de relevé Mutuelle" Then
                num = I
            End If
        Next
        CR.FileName = Application.StartupPath + "\EtatDeReleveMutuelle.rpt"

        CR.SetParameterValue("DateDebut", lDu.Text)
        CR.SetParameterValue("DateFin", lAu.Text)
        CR.SetParameterValue("Numerofacture", lNumero.Text)
        CR.SetParameterValue("Mutuelle", cmbMutuelle.Text)
        CR.SetParameterValue("TitreColonneLibelle1", RecupererValeurExecuteScalaire("Libelle1", "MUTUELLE", "CodeMutuelle", cmbMutuelle.SelectedValue))
        CR.SetParameterValue("TitreColonneLibelle2", RecupererValeurExecuteScalaire("Libelle2", "MUTUELLE", "CodeMutuelle", cmbMutuelle.SelectedValue))
        CR.SetParameterValue("TitreColonneLibelle3", RecupererValeurExecuteScalaire("Libelle3", "MUTUELLE", "CodeMutuelle", cmbMutuelle.SelectedValue))

        CR.SetParameterValue("TitreColonneLibelle4", RecupererValeurExecuteScalaire("Libelle4", "MUTUELLE", "CodeMutuelle", cmbMutuelle.SelectedValue))
        CR.SetParameterValue("TitreColonneLibelle5", RecupererValeurExecuteScalaire("Libelle5", "MUTUELLE", "CodeMutuelle", cmbMutuelle.SelectedValue))


        CR.SetParameterValue("ChiffreEnLettre", ChiffresEnLettres(CDbl(lMontantARegle.Text)))

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent


        If rdbDate.Checked = True Then
            'OrdreBy = " ORDER BY Date "
            FieldDef = CR.Database.Tables("Vue_EtatReleveMutuelle").Fields("Date")
        Else
            'OrdreBy = " ORDER BY MatriculeMutuelle "
            FieldDef = CR.Database.Tables("Vue_EtatReleveMutuelle").Fields("MatriculeMutuelle")
        End If
        CR.DataDefinition.SortFields(0).Field = FieldDef


        CR.RecordSelectionFormula = CondCrystal

        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression de relevé Mutuelle"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
        Me.Hide()
    End Sub

    Private Sub CalculerMontants()
        Dim MontantARegler As Double = 0.0
        Dim MontantDejaRegler As Double = 0.0
        Dim MontantEnCoursReglement As Double = 0.0
        Dim I As Integer = 0

        For I = 0 To gReleves.RowCount - 1
            MontantARegler += gReleves(I, "MontantMutuelle")
            MontantDejaRegler += gReleves(I, "Regle")
            If gReleves(I, "Reste").ToString <> "" Then
                MontantEnCoursReglement += gReleves(I, "Reste")
            End If
        Next

        lMontantARegle.Text = Math.Round(MontantARegler, 3)
        lMontantDejaRegle.Text = Math.Round(MontantDejaRegler, 3)

        lMontantEnCoursReglementt.Text = Math.Round(MontantEnCoursReglement, 3)

    End Sub

    Private Sub bGenerer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bGenerer.Click
        'Dim Chemin As String = RecupererValeurExecuteScalaire("CheminReleve", "MUTUELLE", "NomMutuelle", "LEONI")
        'If Chemin <> "" Then
        '    SaveSetting("PHARMA", "PHARMA", "NuemroReleveMutuelle", NuemroReleve)
        '    Try
        '        Process.Start(Chemin)
        '    Catch ex As Exception
        '        MsgBox(ex.Message)
        '    End Try
        'Else
        '    MsgBox("Vous devez mentionner le chemin du générateur !", MsgBoxStyle.Critical, "Erreur")
        'End If

        Dim MyReleveLeoni As New fReleve
        MyReleveLeoni.NumeroReleveLeoni = lNumero.Text  ' gReleves(gReleves.Row, "NumeroReleve")
        MyReleveLeoni.ShowDialog()
        MyReleveLeoni.Close()
        MyReleveLeoni.Dispose()

    End Sub

    Private Sub bGenererExcel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bGenererExcel.Click
        Dim tab(1) As String
        Dim tabIndice(2), tabValeur(2) As String
        'Dim x As String


        'tabIndice(0) = "0"
        'tabIndice(1) = "1"
        'tabIndice(2) = "2"

        'tabValeur(0) = "Total"
        'tabValeur(1) = lTotBase.Text
        'tabValeur(2) = lTotMontant.Text


        'x = dtpDebut.Text
        'tab(0) = "Période DU : " + x + "  AU :  "
        'x = dtpFin.Text
        'tab(0) = tab(0) + x

        'GeneralExportExcel(gReleves, "", "ReleveeMutuelleN" + Replace(NuemroReleve, "/", ""), tab, tabIndice, tabValeur)

        GenererExcel()

    End Sub


    Private Sub ExporterVersExcel()

        Dim XlApp As Excel.Application
        Dim Cond As String = ""
        Dim Champ As String = ""
        Dim AnneeDebut As Integer = 0
        Dim AnneeFin As Integer = 0
        Dim MoisDebut As Integer = 0
        Dim MoisFin As Integer = 0
        Dim I As Integer = 0
        Try
            XlApp = GetObject(, "excel.application")
        Catch
            XlApp = New Excel.Application
        End Try
        'Ajout d'une page et sélection 
        Dim xsTransfert As Excel.Worksheet = XlApp.Workbooks.Add.ActiveSheet

        Try
            'On crée la chaine de connexion
            Dim StrCon As String = ""
            StrCon = "ODBC;DRIVER={SQL Server}; SERVER=" + NomServeur + "; " & _
                     "DATABASE=" + NomBase + "; UID=" + NomUtilisateurSQL + "; PWD=" + MotDePasseSQL
            With xsTransfert.QueryTables.Add(Connection:=StrCon, Destination:=xsTransfert.Range("A8"))
                .CommandText = strExcel
                .FieldNames = True
                .FillAdjacentFormulas = False
                .PreserveFormatting = True
                .RefreshOnFileOpen = False
                .BackgroundQuery = True
                .RefreshStyle = Excel.XlCellInsertionMode.xlOverwriteCells
                .SavePassword = False
                .SaveData = False
                .AdjustColumnWidth = True
                .RefreshPeriod = 0
                .PreserveColumnInfo = True
                .Refresh(BackgroundQuery:=False)
            End With
            XlApp.Visible = True
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub GenererExcel()
        Dim xls As New C1XLBook
        Dim i As Integer = 0, j As Integer = 0, k As Integer = 0
        Dim Sheet As XLSheet = xls.Sheets(0)
        Dim Style As New XLStyle(xls)
        Dim BackStyle As New XLStyle(xls)
        Dim yFontconverter As New FontConverter
        Dim Col As C1.Win.C1TrueDBGrid.C1DataColumn
        Dim OKBackStyle As Boolean = False
        Dim BaseTableau As Integer = 0
        Dim cmd As New SqlCommand
        Dim strSQL As String

        If gReleveExcel.RowCount = 0 Then Exit Sub

        cmd.Connection = ConnectionServeur


        Style = New XLStyle(xls)
        Style.Font = New Font("Arial", 15, FontStyle.Bold)
        Style.ForeColor = Color.Black
        Sheet(1, 0).Style = Style


        Style = New XLStyle(xls)
        Style.Font = New Font("Arial", 10, FontStyle.Bold)
        Style.ForeColor = Color.Black
        Sheet(5, 0).Style = Style
        BaseTableau += 1


        ' Entêtes des colonnes
        j = 0
        Style = New XLStyle(xls)
        Style.Font = New Font("Arial", 10, FontStyle.Bold)
        Style.ForeColor = Color.Black
        Style.BackColor = Color.LightGray
        Style.WordWrap = True
        Style.BorderBottom = XLLineStyleEnum.Thick
        Style.BorderLeft = XLLineStyleEnum.Thick
        Style.BorderRight = XLLineStyleEnum.Thick
        Style.BorderTop = XLLineStyleEnum.Thick
        Style.AlignVert = XLAlignVertEnum.Center
        Style.AlignHorz = XLAlignHorzEnum.Center
        For Each Col In gReleveExcel.Columns
            Sheet(BaseTableau, j).Value = Col.Caption
            Sheet(BaseTableau, j).Style = Style
            j += 1
        Next
        Sheet.Rows(BaseTableau).Height *= 3
        BaseTableau += 1

        ' Lignes de données
        For i = 0 To gReleveExcel.RowCount - 1
            'ProgressBarExport.Value = i
            Application.DoEvents()
            ' Ajout des données de la ligne

            j = 0
            For Each Col In gReleveExcel.Columns

                ' Calcul de la couleur de la cellule et son style
                Style = New XLStyle(xls)
                Style.ForeColor = Color.Black
                Style.BorderBottom = XLLineStyleEnum.Thin
                Style.BorderLeft = XLLineStyleEnum.Thin
                Style.BorderRight = XLLineStyleEnum.Thin
                Style.BorderTop = XLLineStyleEnum.Thin
                Style.AlignVert = XLAlignVertEnum.Center

                If Col.DataType.Name = "String" Then
                    If Not IsDBNull(gReleveExcel(i, j)) Then
                        Sheet(i + BaseTableau, j).Value = gReleveExcel(i, j)
                    Else
                        Sheet(i + BaseTableau, j).Value = ""
                    End If
                    Style.AlignHorz = XLAlignHorzEnum.Left
                    Sheet(i + BaseTableau, j).Style = Style
                ElseIf Col.DataType.Name = "Integer" Then
                    If Not IsDBNull(gReleveExcel(i, j)) Then
                        Sheet(i + BaseTableau, j).Value = CLng(gReleveExcel(i, j))
                    Else
                        Sheet(i + BaseTableau, j).Value = ""
                    End If
                    Style.AlignHorz = XLAlignHorzEnum.Right
                    Sheet(i + BaseTableau, j).Style = Style
                ElseIf Col.DataType.Name = "Decimal" Then
                    If Not IsDBNull(gReleveExcel(i, j)) Then
                        Sheet(i + BaseTableau, j).Value = CDbl(gReleveExcel(i, j))
                    Else
                        Sheet(i + BaseTableau, j).Value = ""
                    End If
                    Style.AlignHorz = XLAlignHorzEnum.Right
                    Sheet(i + BaseTableau, j).Style = Style
                ElseIf Col.DataType.Name = "DateTime" Then
                    If Not IsDBNull(gReleveExcel(i, j)) Then
                        Sheet(i + BaseTableau, j).Value = Format(gReleveExcel(i, j), "dd/MM/yyyy")
                    Else
                        Sheet(i + BaseTableau, j).Value = ""
                    End If
                    Style.AlignHorz = XLAlignHorzEnum.Center
                    Sheet(i + BaseTableau, j).Style = Style
                ElseIf Col.DataType.Name = "Int32" Then
                    If Not IsDBNull(gReleveExcel(i, j)) Then
                        Sheet(i + BaseTableau, j).Value = CLng(gReleveExcel(i, j))
                    Else
                        Sheet(i + BaseTableau, j).Value = ""
                    End If
                    Style.AlignHorz = XLAlignHorzEnum.Right
                    Sheet(i + BaseTableau, j).Style = Style
                Else
                    Sheet(i + BaseTableau, j).Value = CStr(gReleveExcel(i, j))
                    Style.AlignHorz = XLAlignHorzEnum.Left
                    Sheet(i + BaseTableau, j).Style = Style
                End If
                j += 1
            Next
        Next

        Application.DoEvents()

        BaseTableau = i + BaseTableau

        ' Règler la taille des colonnes sur le contenu
        AutoSizeColumns(Sheet)

        xls.Sheets(0).Columns(0).Width = 1000


        ' Lecture de l'option Format du fichier Excel'
        FormatExcel = GetSetting("PHARMA", "PHARMA", "FormatExcel", "Excel")
        ' Lecture de l'option Dossier du fichier Excel'
        DossierExcel = GetSetting("PHARMA", "PHARMA", "DossierExcel", "")
        If DossierExcel = "" Then
            'DossierExcel = "C:\ExportAlliance"
            MsgBox("Veuiller Saisir le Dossier d'export Excel dans le menu Paramètres Généraux")
            Exit Sub
        End If

        If System.IO.Directory.Exists(DossierExcel) = False Then
            System.IO.Directory.CreateDirectory(DossierExcel)
        End If

        Dim yNomFichierXLS As String = ""
        yNomFichierXLS = DossierExcel + "\" + "ReleveMutuelleN" + Replace(NuemroReleve, "/", "") + Format(Date.Now, "ddMMyyyy-hhmmss") + ".XLS"

        Try
            If FormatExcel = "EXCEL2007" Then
                yNomFichierXLS = Replace(yNomFichierXLS, ".XLS", ".XLSX")
                xls.Save(yNomFichierXLS, FileFormat.OpenXml)
            Else
                xls.Save(yNomFichierXLS, FileFormat.Biff8)
            End If
            System.Diagnostics.Process.Start(yNomFichierXLS)
        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
        End Try


    End Sub

    Private Sub bSuprimerreleve_Click(sender As Object, e As EventArgs) Handles bSuprimerreleve.Click

        'MontantTotalTTC += gReleves.Columns("TotalTTC").Value
        'MontantR += gReleves.Columns("MontantCNAM").Value

        'ListNumeroVents.Add(gReleves.Columns("NumeroVente").Value)
        gReleves.Delete()
        dsReleve.Tables("RELEVE_MUTUELLE_DETAILS").AcceptChanges()
        CalculerMontants()
    End Sub
End Class