﻿Imports System.Data.SqlClient

Public Class fCritereDeSelectionCommande

    '*******************************************************************************************************
    '**************************** déclaration des variables de retour vers la commande *********************
    '*******************************************************************************************************

    Public Shared TypeCommande As String = ""
    Public Shared NombreDeJour As Integer = 0
    Public Shared NombreDeJourManquantDepuis As Integer = 0
    Public Shared SansManquantDepuis As String = ""
    Public Shared Reference As String = ""
    Public Shared mois As Integer = 0
    Public Shared DebutPeriode As String = ""
    Public Shared FinPeriode As String = ""
    Public Shared Section As String = ""
    Public Shared DebutIntervalle As String = ""
    Public Shared FinIntervalle As String = ""
    Public Shared Forme As Integer = 0
    Public Shared Categorie As Integer = 0
    Public Shared Labo As Integer = 0
    Public Shared Rayon As String = ""
    Public Shared RayonSelectionne As String = ""
    Public Shared Trie As String = ""
    Public Shared CommandeEnCours As Boolean = False
    Public Shared TenirCompteStockAlerte As Boolean = False
    Public Shared TenirCompteStock As Boolean = False
    Public Shared TenirCompteStockAlerteCJ As Boolean = False

    Public Shared StatistiqueAnneePrecidente As Boolean = False

    Dim cmdChargement As New SqlCommand
    Dim cbChargement As New SqlCommandBuilder
    Dim dsChargement As New DataSet
    Dim daChargement As New SqlDataAdapter
    Dim StrSQL As String = ""

    '************************************** variable pour savoir la fenêtre appelante de cette fenêtre 
    '************************************** si elle est une simple commande ou un projet de comande
    Public FenetreAppelante As String = ""

    Private Sub rdbJournaliere_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbJournaliere.CheckedChanged
        If rdbJournaliere.Checked = True Then

            GroupeReference.Enabled = False
            cmbMois.Enabled = False
            dtpDebutPeriode.Enabled = False
            dtpFinPeriode.Enabled = False

            tNombreJour.Enabled = True
            dtpSansManquant.Enabled = True
            GroupeSection.Enabled = True
            cmbForme.Enabled = True
            cmbCategorie.Enabled = True
            cmbLabo.Enabled = True
            GroupeRayon.Enabled = True
            chbTenirCompte.Enabled = True
            cmbRayon.Enabled = True
            GroupeTrie.Enabled = True

            tNombreJour.Text = "1"


        End If
    End Sub

    Private Sub rdbToutesSection_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If rdbToutesSection.Checked = True Then
            tDebutIntervalle.Enabled = False
            tFinIntervalle.Enabled = False
        End If
    End Sub

    Private Sub rdbIntervalleSection_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If rdbIntervalleSection.Checked = True Then
            tDebutIntervalle.Enabled = True
            tFinIntervalle.Enabled = True
        End If
    End Sub

    Private Sub rdbTousRayon_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbTousRayon.CheckedChanged
        If rdbTousRayon.Checked = True Then
            cmbRayon.Enabled = False
        End If
    End Sub

    Private Sub rdbRayonRayon_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbRayonRayon.CheckedChanged
        If rdbRayonRayon.Checked = True Then
            cmbRayon.Enabled = True
        End If
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        TypeCommande = "RIEN"
        Me.Hide()
    End Sub

    Private Sub rdbGroupee_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbGroupee.CheckedChanged
        If rdbGroupee.Checked = True Then
            GroupeReference.Enabled = True
            cmbMois.Enabled = True
            dtpDebutPeriode.Enabled = True
            dtpFinPeriode.Enabled = True
            dtpSansManquant.Enabled = False

            tNombreJour.Enabled = True
            dtpSansManquant.Enabled = True
            GroupeSection.Enabled = True
            cmbForme.Enabled = True
            cmbCategorie.Enabled = True
            cmbLabo.Enabled = True
            GroupeRayon.Enabled = True
            chbTenirCompte.Enabled = True
            cmbRayon.Enabled = True
            GroupeTrie.Enabled = True

            tNombreJour.Text = CommandeGroupeJ
            If System.DateTime.Now.Month = 12 Then
                cmbMois.SelectedIndex = 0
            Else
                cmbMois.SelectedIndex = System.DateTime.Now.Month
            End If

            rdbMois.Checked = True
            cmbMois.Enabled = True
            dtpDebutPeriode.Enabled = False
            dtpFinPeriode.Enabled = False

            rdbMois.Enabled = False
            rdbPeriode.Checked = True
            dtpDebutPeriode.Text = "01/01/" + (Date.Now.Year - 1).ToString
            dtpFinPeriode.Value = Date.Now

        End If
    End Sub

    Private Sub bOk_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOk.Click

        If rdbJournaliere.Checked = True Then
            TypeCommande = "JOURNALIERE"
        ElseIf rdbGroupee.Checked = True Then
            TypeCommande = "GROUPEE"
        ElseIf rdbHitParade.Checked = True Then
            TypeCommande = "HIT PARADE"
        End If

        If tNombreJour.Value = "" Then
            NombreDeJour = 1
        Else
            NombreDeJour = tNombreJour.Value
        End If

        SansManquantDepuis = dtpSansManquant.Value

        If rdbMois.Checked = True Then
            Reference = "MOIS"
        ElseIf rdbPeriode.Checked = True Then
            Reference = "PERIODE"
        End If

        mois = cmbMois.SelectedIndex + 1
        DebutPeriode = dtpDebutPeriode.Value
        FinPeriode = dtpFinPeriode.Value

        If rdbToutesSection.Checked = True Then
            Section = "TOUTES"
        ElseIf rdbIntervalleSection.Checked = True Then
            Section = "INTERVALLE"
        End If

        DebutIntervalle = tDebutIntervalle.Value
        FinIntervalle = tFinIntervalle.Value
        Forme = cmbForme.SelectedValue
        Categorie = cmbCategorie.SelectedValue
        Labo = cmbLabo.SelectedValue

        If rdbTousRayon.Checked = True Then
            Rayon = "TOUS"
        ElseIf rdbRayonRayon.Checked = True Then
            Rayon = "RAYON"
        End If

        RayonSelectionne = cmbRayon.Text

        If rdbDesignation.Checked = True Then
            Trie = "DESIGNATION"
        ElseIf rdbForme.Checked = True Then
            Trie = "LibelleForme,DESIGNATION"
            'ElseIf rdbCategorie.Checked = True Then
            '    Trie = "CodeCategorie"
            'ElseIf rdbLabo.Checked = True Then
            '    Trie = "CodeLabo"
        ElseIf rdbStock.Checked = True Then
            Trie = "Stock"
        ElseIf rdbQte.Checked = True Then
            Trie = "Qte DESC"
        End If

        CommandeEnCours = chbTenirCompte.Checked
        TenirCompteStockAlerte = chbTenirCompteStockAlerte.Checked
        TenirCompteStock = chbTenirCompteStock.Checked
        StatistiqueAnneePrecidente = chkStatistique.Checked
        TenirCompteStockAlerteCJ = CbTenirCompteStockAlerteCJ.Checked

        Me.Hide()
    End Sub

    Private Sub rdbMois_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbMois.CheckedChanged
        If rdbMois.Checked = True Then
            dtpDebutPeriode.Enabled = False
            dtpFinPeriode.Enabled = False
            cmbMois.Enabled = True
        Else
            dtpDebutPeriode.Enabled = True
            dtpFinPeriode.Enabled = True
            cmbMois.Enabled = False
        End If
    End Sub

    Private Sub rdbPeriode_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbPeriode.CheckedChanged
        If rdbPeriode.Checked = True Then
            dtpDebutPeriode.Enabled = True
            dtpFinPeriode.Enabled = True
            cmbMois.Enabled = False
            dtpDebutPeriode.Value = Today
            If tNombreJour.Text <> "" Then
                dtpFinPeriode.Value = DateAdd(DateInterval.Day, CDbl(tNombreJour.Text), Today)
            End If

        Else
            dtpDebutPeriode.Enabled = False
            dtpFinPeriode.Enabled = False
            cmbMois.Enabled = True
        End If
    End Sub

    Private Sub fCritereDeSelectionCommande_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        '*************************************************************************************************
        '********************************* initialisation des controles **********************************
        '*************************************************************************************************

        With cmbMois
            .ColumnHeaders = False
            .HoldFields()
            .AddItem("JANVIER")
            .AddItem("FEVRIER")
            .AddItem("MARS")
            .AddItem("AVRIL")
            .AddItem("MAI")
            .AddItem("JUIN")
            .AddItem("JUILLET")
            .AddItem("AOUT")
            .AddItem("SEPTEMBER")
            .AddItem("OCTOBRE")
            .AddItem("NOVEMBRE")
            .AddItem("DECEMBRE")
        End With

        'chargement des Formes
        Try
            dsChargement.Tables("FORME_ARTICLE").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT CodeForme,LibelleForme FROM FORME_ARTICLE Where SupprimeForme=0 ORDER BY LibelleForme ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "FORME_ARTICLE")
        cmbForme.DataSource = dsChargement.Tables("FORME_ARTICLE")
        cmbForme.ValueMember = "CodeForme"
        cmbForme.DisplayMember = "LibelleForme"
        cmbForme.ColumnHeaders = False
        cmbForme.Splits(0).DisplayColumns("CodeForme").Visible = False
        cmbForme.Splits(0).DisplayColumns("LibelleForme").Width = 10
        cmbForme.ExtendRightColumn = True

        'chargement des Categories
        Try
            dsChargement.Tables("CATEGORIE").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT CodeCategorie,LibelleCategorie FROM CATEGORIE WHERE SupprimeCategorie=0 ORDER BY LibelleCategorie ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "CATEGORIE")
        cmbCategorie.DataSource = dsChargement.Tables("CATEGORIE")
        cmbCategorie.ValueMember = "CodeCategorie"
        cmbCategorie.DisplayMember = "LibelleCategorie"
        cmbCategorie.ColumnHeaders = False
        cmbCategorie.Splits(0).DisplayColumns("CodeCategorie").Visible = False
        cmbCategorie.Splits(0).DisplayColumns("LibelleCategorie").Width = 10
        cmbCategorie.ExtendRightColumn = True

        'chargement des Labo
        Try
            dsChargement.Tables("dbo.LABORATOIRE").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT CodeLabo,NomLabo FROM dbo.LABORATOIRE WHERE SupprimeLabo=0 ORDER BY NomLabo ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "dbo.LABORATOIRE")
        cmbLabo.DataSource = dsChargement.Tables("dbo.LABORATOIRE")
        cmbLabo.ValueMember = "CodeLabo"
        cmbLabo.DisplayMember = "NomLabo"
        cmbLabo.ColumnHeaders = False
        cmbLabo.Splits(0).DisplayColumns("CodeLabo").Visible = False
        cmbLabo.Splits(0).DisplayColumns("NomLabo").Width = 10
        cmbLabo.ExtendRightColumn = True

        'chargement des Rayons
        Try
            dsChargement.Tables("dbo.RAYON").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT DISTINCT Rayon FROM ARTICLE ORDER BY Rayon ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "RAYON")
        cmbRayon.DataSource = dsChargement.Tables("RAYON")
        cmbRayon.ValueMember = "Rayon"
        cmbRayon.DisplayMember = "Rayon"
        cmbRayon.ColumnHeaders = False
        cmbRayon.Splits(0).DisplayColumns("Rayon").Width = 10
        cmbRayon.ExtendRightColumn = True

        rdbMois.Checked = True
        rdbToutesSection.Checked = True
        rdbTousRayon.Checked = True
        rdbDesignation.Checked = True
        rdbJournaliere.Checked = True

        dtpDebutPeriode.Enabled = False
        dtpFinPeriode.Enabled = False
        cmbMois.Enabled = True
        cmbRayon.Enabled = False
        tDebutIntervalle.Enabled = False
        tFinIntervalle.Enabled = False

        tNombreJour.Value = ""
        dtpSansManquant.Value = ""
        dtpDebutPeriode.Value = ""
        dtpFinPeriode.Value = ""
        tDebutIntervalle.Value = ""
        tFinIntervalle.Value = ""
        cmbForme.Text = ""
        cmbCategorie.Text = ""
        cmbLabo.Text = ""
        cmbMois.Text = ""
        cmbRayon.Text = ""
        chbTenirCompte.Checked = False

        'dtpDebutPeriode.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        'dtpDebutPeriode.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        'dtpFinPeriode.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        'dtpFinPeriode.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpSansManquant.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpSansManquant.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        Try
            NombreDeJourManquantDepuis = RecupererValeurExecuteScalaire("NePAsSortirLesManquantsDepuis", "PARAMETRES", "POSTE", System.Environment.GetEnvironmentVariable("Poste"))
        Catch ex As Exception
            NombreDeJourManquantDepuis = 0
        End Try

        dtpSansManquant.Value = Date.Now.AddYears(-2).Date  'DateAdd(DateInterval.Day, -NombreDeJourManquantDepuis, System.DateTime.Now)

        dtpDebutPeriode.Value = Today
        dtpFinPeriode.Value = Today
        'dtpSansManquant.Value = DateAdd(DateInterval.Year, -1, Today)

        If FenetreAppelante = "ProjetCommande" Then
            rdbHitParade.Enabled = False
            rdbJournaliere.Enabled = False
            rdbGroupee.Checked = True
            tNombreJour.Text = "30"
        Else
            tNombreJour.Text = "1"
        End If

    End Sub

    Private Sub dtpSansManquant_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpSansManquant.KeyUp

        If e.KeyCode = Keys.Enter Then
            rdbTousRayon.Focus()
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbHitParade_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbHitParade.CheckedChanged
        chbTenirCompteStockAlerte.Visible = rdbHitParade.Checked
        chbTenirCompteStock.Visible = rdbHitParade.Checked
        rdbQte.Checked = True
        If rdbHitParade.Checked = True Then
            dtpDebutPeriode.Enabled = True
            dtpDebutPeriode.Value = Today
            dtpFinPeriode.Enabled = True
            dtpFinPeriode.Value = Today

            tNombreJour.Enabled = False
            dtpSansManquant.Enabled = False
            GroupeReference.Enabled = True
            cmbMois.Enabled = False
            GroupeSection.Enabled = False
            tDebutIntervalle.Enabled = False
            tFinIntervalle.Enabled = False
            cmbForme.Enabled = True
            cmbCategorie.Enabled = True
            cmbLabo.Enabled = True
            GroupeRayon.Enabled = False
            chbTenirCompte.Enabled = False
            cmbRayon.Enabled = False
            GroupeTrie.Enabled = True
            rdbMois.Enabled = False
            chbTenirCompte.Enabled = True
            dtpDebutPeriode.Focus()

        End If
    End Sub

    Private Sub bFirgo_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFirgo.Click
        TypeCommande = "FRIGO"
        Me.Hide()
    End Sub

    Private Sub dtpDebutPeriodeKeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebutPeriode.KeyUp
        If e.KeyCode = Keys.Enter Then
            dtpFinPeriode.Focus()
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub dtpFinPeriode_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFinPeriode.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbForme.Focus()
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub cmbForme_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbForme.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbCategorie.Focus()
            Exit Sub
        Else
            cmbForme.OpenCombo()
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If

    End Sub

    Private Sub cmbCategorie_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCategorie.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbLabo.Focus()
            Exit Sub
        Else
            cmbCategorie.OpenCombo()
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub cmbLabo_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbLabo.KeyUp
        If e.KeyCode = Keys.Enter Then
            If rdbHitParade.CheckAlign Then
                bOk.Focus()
                Exit Sub
            End If
        Else
            cmbLabo.OpenCombo()
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub GroupBox1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles GroupBox1.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbHitParade_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbHitParade.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbGroupee_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbGroupee.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbJournaliere_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbJournaliere.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbMois_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbMois.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbPeriode_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbPeriode.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub tNombreJour_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNombreJour.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub cmbMois_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMois.KeyUp
        If e.KeyCode = Keys.Enter Then
            dtpSansManquant.Focus()
            Exit Sub
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbToutesSection_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbIntervalleSection_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub tDebutIntervalle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub


    Private Sub tFinIntervalle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbTousRayon_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbTousRayon.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbRayonRayon_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbRayonRayon.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub cmbRayon_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbRayon.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub chbTenirCompte_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles chbTenirCompte.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbDesignation_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbDesignation.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbForme_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles rdbForme.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub bOk_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bOk.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub bAnnuler_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bAnnuler.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub bFirgo_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bFirgo.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F6 Then
            bFirgo_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbToutesSection_CheckedChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbToutesSection.CheckedChanged
        If rdbToutesSection.Checked = True Then
            tDebutIntervalle.Enabled = False
            tFinIntervalle.Enabled = False
        End If
    End Sub

    Private Sub rdbIntervalleSection_CheckedChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbIntervalleSection.CheckedChanged
        If rdbIntervalleSection.Checked = True Then
            tDebutIntervalle.Enabled = True
            tFinIntervalle.Enabled = True
        End If
    End Sub

    Private Sub tDebutIntervalle_KeyUp1(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tDebutIntervalle.KeyUp
        If e.KeyCode = Keys.Enter Then
            tFinIntervalle.Focus()
            Exit Sub
        End If
    End Sub

    Private Sub tFinIntervalle_KeyUp1(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tFinIntervalle.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbForme.Focus()
            Exit Sub
        End If
    End Sub

    Private Sub bCommandeInstantannee_Click(sender As System.Object, e As System.EventArgs) Handles bCommandeInstantannee.Click
        TypeCommande = "COMMANDE_INSTANTANEE"
        Me.Hide()
    End Sub
End Class