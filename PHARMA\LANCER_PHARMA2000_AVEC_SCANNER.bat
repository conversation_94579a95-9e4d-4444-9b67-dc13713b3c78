@echo off
echo ========================================
echo    PHARMA2000 PREMIUM
echo    AVEC SCANNER CODE A BARRES INTEGRE
echo ========================================
echo.
echo Lancement de l'application principale...
echo.

cd /d "%~dp0"

if exist "bin\Debug\Pharma2000Premium.exe" (
    echo Application trouvee : bin\Debug\Pharma2000Premium.exe
    echo Taille : 14.6 MB
    echo Date : 29/06/2025 15:54:31
    echo.
    echo Demarrage de PHARMA2000 avec scanner integre...
    echo.
    start "" "bin\Debug\Pharma2000Premium.exe"
    echo.
    echo ========================================
    echo    SCANNER CODE A BARRES INTEGRE !
    echo ========================================
    echo.
    echo INSTRUCTIONS POUR UTILISER LE SCANNER :
    echo.
    echo 1. Ouvrez le module "Fiche Client"
    echo 2. Le champ "Code Client" est maintenant modifiable
    echo 3. Utilisez votre scanner dans ce champ
    echo 4. L'application detectera automatiquement :
    echo    - Saisie rapide (scanner)
    echo    - Saisie manuelle
    echo 5. Appuyez sur Enter apres chaque scan
    echo 6. Le code sera automatiquement :
    echo    - Nettoye (espaces supprimes)
    echo    - Converti en majuscules
    echo    - Verifie pour unicite
    echo.
    echo FONCTIONNALITES IMPLEMENTEES :
    echo - Detection automatique scanner vs manuel
    echo - Validation en temps reel
    echo - Gestion d'erreurs complete
    echo - Verification d'unicite en mode ajout
    echo - Conversion automatique majuscules
    echo.
    echo ========================================
    echo.
) else (
    echo ERREUR : Application non trouvee !
    echo Veuillez compiler le projet Pharma2000Premium.vbproj d'abord.
    echo.
)

echo Appuyez sur une touche pour fermer cette fenetre...
pause >nul
