﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports Microsoft.Reporting.WinForms

Public Class fReleveeCNAM
    Dim _SalesReportService As New Bll.Reporting.SalesReport

    Dim x As Integer
    Dim cmdReleve As New SqlCommand
    Dim daReleve As New SqlDataAdapter
    Dim dsReleve As New DataSet

    Dim MontantTotal As Double = 0
    Dim MontantARemp As Double = 0
    Dim Reste As Double = 0

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "116" And bAjouterReleve.Enabled = True Then
            bAjouterReleve_Click(sender, e)
        End If
        If argument = "117" And bRechercher.Enabled = True Then
            bRechercher_Click(sender, e)
        End If
        If argument = "118" And bSuprimerreleve.Enabled = True Then
            bSuprimerreleve_Click(sender, e)
        End If
        If argument = "119" And bModifierReleve.Enabled = True Then
            bModifierReleve_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub Init()
        Dim StrSQL As String = ""
        x = 0

        With cmbType
            .HoldFields()
            .AddItem("TOUS")
            .AddItem("APCI TIERS PAYANT")
            .AddItem("APCI PRISE EN CHARGE")
            .AddItem("MALADIES ORDINAIRES")
            .AddItem("APPAREILLAGE")
            .AddItem("APCI TIERS PAYANT + MALADIES ORDINAIRES")
            .ColumnHeaders = False
            .ColumnWidth = "250"
        End With

        dpDateDebut.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dpDateDebut.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dpDateFin.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dpDateFin.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        AfficherReleve()
        CalculValeur()

    End Sub

    Public Sub AfficherReleve()

        Dim I As Integer
        Dim Cond As String = "1=1"

        If (dsReleve.Tables.IndexOf("RELEVE") > -1) Then
            dsReleve.Tables("RELEVE").Clear()
        End If

        'Composer la condition de la requête 
        If cmbType.Text <> "" Then

            If cmbType.Text = "TOUS" Then
            ElseIf cmbType.Text = "APCI TIERS PAYANT" Then
                Cond += " AND TiersPayant=1 AND PriseEnCharge=0 AND Appareillage=0 AND MaladieOrdinaire=0 "
            ElseIf cmbType.Text = "APCI PRISE EN CHARGE" Then
                Cond += " AND TiersPayant=0 AND PriseEnCharge=1 AND Appareillage=0 AND MaladieOrdinaire=0 "
            ElseIf cmbType.Text = "MALADIES ORDINAIRES" Then
                Cond += " AND TiersPayant=0 AND PriseEnCharge=0 AND Appareillage=0 AND MaladieOrdinaire=1 "
            ElseIf cmbType.Text = "APPAREILLAGE" Then
                Cond += " AND TiersPayant=0 AND PriseEnCharge=0 AND Appareillage=1 AND MaladieOrdinaire=0 "
            ElseIf cmbType.Text = "APCI TIERS PAYANT + MALADIES ORDINAIRES" Then
                Cond += " AND TiersPayant=1 AND PriseEnCharge=0 AND Appareillage=0 AND MaladieOrdinaire=1 "
            End If

        End If
        If tNumero.Text <> "" Then
            Cond += " AND RELEVE_CNAM.NumeroReleve = '" + tNumero.Text + "'"
        End If
        If dpDateDebut.Text <> "" Then
            Cond += " AND Convert(date,RELEVE_CNAM.Date) >= '" + dpDateDebut.Text + "'"
        End If
        If dpDateFin.Text <> "" Then
            Cond += "AND Convert(Date,RELEVE_CNAM.Date) <= '" + dpDateFin.Text + "'"
        End If

        'cmdReleve.CommandText = "SELECT  NumeroReleve," + _
        '                        " Date, " + _
        '                        "'' AS Type, " + _
        '                        " DateDebut,  " + _
        '                        " DateFin, " + _
        '                        " Total,  " + _
        '                        " Montant, " + _
        '                        " Reste, " + _
        '                        " TiersPayant, " + _
        '                        " PriseEnCharge, " + _
        '                        " Appareillage, " + _
        '                        " MaladieOrdinaire " + _
        '                        " FROM RELEVE_CNAM" + _
        '                        " WHERE " + Cond + _
        '                        " ORDER BY Date DESC"

        cmdReleve.CommandText = "SELECT DISTINCT  " + _
                                 "	RELEVE_CNAM.NumeroReleve,  " + _
                                 "	RELEVE_CNAM.Date,  " + _
                                 "	'' AS Type,  " + _
                                 "	RELEVE_CNAM.DateDebut,  " + _
                                 "	RELEVE_CNAM.DateFin,  " + _
                                 "	RELEVE_CNAM.Total,  " + _
                                 "	RELEVE_CNAM.Montant,  " + _
                                 "	ISNULL(REGLEMENT_CNAM1.MontantRetenueSource,0) AS MontantRetenueSource,  " + _
                                 "	(Reste - ISNULL(REGLEMENT_CNAM1.MontantRetenueSource,0)) AS Reste,   " + _
                                 "	RELEVE_CNAM.TiersPayant,  " + _
                                 "	RELEVE_CNAM.PriseEnCharge,  " + _
                                 "	RELEVE_CNAM.Appareillage,  " + _
                                 "	MaladieOrdinaire,   " + _
                                 "	VENTE.OMF   " + _
                                 "FROM  " + _
                                 "	dbo.RELEVE_CNAM INNER JOIN " +
                                 "	   dbo.RELEVE_CNAM_DETAILS ON dbo.RELEVE_CNAM.NumeroReleve = dbo.RELEVE_CNAM_DETAILS.NumeroReleve INNER JOIN" + _
                                 "	dbo.VENTE ON dbo.RELEVE_CNAM_DETAILS.NumeroVente = dbo.VENTE.NumeroVente  " + _
                                 "	LEFT JOIN (SELECT NumeroReleve, MAX(Montant) as Montant, SUM(MontantRegle) as MontantRegle, MAX(MontantRetenueSource) as MontantRetenueSource " + _
                                 "			   FROM REGLEMENT_CNAM  " + _
                                 "			   LEFT JOIN REGLEMENT_CNAM_VENTE ON REGLEMENT_CNAM_VENTE.NumeroReglementCnam = REGLEMENT_CNAM.NumeroReglementCnam " + _
                                 "			   GROUP BY NumeroReleve  " + _
                                 "			   ) AS REGLEMENT_CNAM1 ON REGLEMENT_CNAM1.NumeroReleve = RELEVE_CNAM.NumeroReleve " + _
                                 " WHERE " + Cond + _
                                 " ORDER BY Date DESC"

        cmdReleve.Connection = ConnectionServeur
        daReleve = New SqlDataAdapter(cmdReleve)
        daReleve.Fill(dsReleve, "RELEVE")

        With gReleves
            .Columns.Clear()
            .DataSource = dsReleve
            .DataMember = "RELEVE"
            .Rebind(False)
            .Columns("NumeroReleve").Caption = "Num relevé"
            .Columns("Date").Caption = "Date"
            .Columns("DateDebut").Caption = "Date début"
            .Columns("DateFin").Caption = "Date fin"
            .Columns("Total").Caption = "Montant Total"
            .Columns("Montant").Caption = "M.A.Rem"
            .Columns("Reste").Caption = "Reste"
            .Columns("MontantRetenueSource").Caption = "Retenue à la Source"
            .Columns("OMF").Caption = "OMF"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("NumeroReleve").Width = 160
            .Splits(0).DisplayColumns("NumeroReleve").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Date").Width = 100
            .Splits(0).DisplayColumns("Date").Style.HorizontalAlignment = AlignHorzEnum.Center

            .Splits(0).DisplayColumns("Type").Width = 350
            .Splits(0).DisplayColumns("Type").Style.HorizontalAlignment = AlignHorzEnum.Center

            .Splits(0).DisplayColumns("DateDebut").Width = 100
            .Splits(0).DisplayColumns("DateDebut").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("DateFin").Width = 100
            .Splits(0).DisplayColumns("DateFin").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Total").Width = 140
            .Splits(0).DisplayColumns("Total").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Montant").Width = 140
            .Splits(0).DisplayColumns("Montant").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Reste").Width = 130
            .Splits(0).DisplayColumns("Reste").Style.HorizontalAlignment = AlignHorzEnum.Far


            .Splits(0).DisplayColumns("MontantRetenueSource").Width = 130
            .Splits(0).DisplayColumns("MontantRetenueSource").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("TiersPayant").Visible = False
            .Splits(0).DisplayColumns("PriseEnCharge").Visible = False
            .Splits(0).DisplayColumns("Appareillage").Visible = False
            .Splits(0).DisplayColumns("MaladieOrdinaire").Visible = False
            .Splits(0).DisplayColumns("OMF").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gReleves)
        End With

        For I = 0 To dsReleve.Tables("RELEVE").Rows.Count - 1
            If dsReleve.Tables("RELEVE").Rows(I).Item("TiersPayant") = True Then
                dsReleve.Tables("RELEVE").Rows(I).Item("Type") = "TIERS PAYANT"
            End If
            If dsReleve.Tables("RELEVE").Rows(I).Item("PriseEnCharge") = True Then
                dsReleve.Tables("RELEVE").Rows(I).Item("Type") = "PRISE EN CHARGE"
            End If
            If dsReleve.Tables("RELEVE").Rows(I).Item("Appareillage") = True Then
                dsReleve.Tables("RELEVE").Rows(I).Item("Type") = "APPAREILLAGE"
            End If
            If dsReleve.Tables("RELEVE").Rows(I).Item("MaladieOrdinaire") = True Then
                dsReleve.Tables("RELEVE").Rows(I).Item("Type") = "MALADIE ORDINAIRE"
            End If
            If dsReleve.Tables("RELEVE").Rows(I).Item("TiersPayant") = True And dsReleve.Tables("RELEVE").Rows(I).Item("MaladieOrdinaire") = True Then
                dsReleve.Tables("RELEVE").Rows(I).Item("Type") = "TIERS PAYANT + MALADIE ORDINAIRE"
            End If
            If dsReleve.Tables("RELEVE").Rows(I).Item("TiersPayant") = True And dsReleve.Tables("RELEVE").Rows(I).Item("Appareillage") = True And dsReleve.Tables("RELEVE").Rows(I).Item("PriseEnCharge") = True Then
                dsReleve.Tables("RELEVE").Rows(I).Item("Type") = "TOUS"
            End If
        Next

        gReleves.MoveRelative(x)
        CalculValeur()
    End Sub
    Private Sub bAjouterReleve_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterReleve.Click
        Dim fNouveauRelevee As New fCritereDuReleveCNAM
        fNouveauRelevee.ShowDialog()
        fNouveauRelevee.Close()
        fNouveauRelevee.Dispose()
        AfficherReleve()
    End Sub


    Private Sub bModifierReleve_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifierReleve.Click
        If gReleves.RowCount = 0 Then
            MsgBox("Liste Vide", MsgBoxStyle.OkOnly)
        Else
            Dim MyFicheReleveCNAM As New fFicheReleveeCNAM
            MyFicheReleveCNAM.NuemroReleve = gReleves(gReleves.Row, "NumeroReleve")
            MyFicheReleveCNAM.ModificationReglement = "M"
            MyFicheReleveCNAM.Init(Me)
            MyFicheReleveCNAM.Show()

            'MyFicheReleveCNAM.Close()
            'MyFicheReleveCNAM.Dispose()
            'AfficherReleve()
        End If
    End Sub

    Public Sub VerifierMontant()
        Try
            gReleves(gReleves.Row, "Reste") = RecupererValeurExecuteScalaire("Reste", "RELEVE_CNAM", "NumeroReleve", gReleves(gReleves.Row, "NumeroReleve"))
        Catch
        End Try
    End Sub

    Private Sub bSuprimerreleve_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSuprimerreleve.Click
        Dim cmd As New SqlCommand
        Dim NumeroReleve As String = ""
        Dim StrSQL As String = ""
        Dim NombreDesVenteRegle As Integer = 0

        NumeroReleve = Quote(gReleves(gReleves.Row, "NumeroReleve"))
        Connect()
        If gReleves.RowCount = 0 Then
            MsgBox("Liste Vide", MsgBoxStyle.OkOnly)
        Else
            'test si ce relevé est reglé ou nn si c est le cas il faut interdir sa suppression

            StrSQL = " SELECT COUNT(NumeroVente) FROM RELEVE_CNAM_DETAILS WHERE NumeroReleve=" _
                      + NumeroReleve + " AND Regle <> 0 "
            cmdReleve.Connection = ConnectionServeur
            cmdReleve.CommandText = StrSQL
            Try
                NombreDesVenteRegle = cmdReleve.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If NombreDesVenteRegle <> 0 Then
                MsgBox("Impossible de supprimer un relevé reglé !", MsgBoxStyle.Critical, "Erreur")
                Exit Sub
            End If


            If MsgBox("Voulez vous vraiment supprimer ce relevé  ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM RELEVE_CNAM_DETAILS WHERE NumeroReleve = " + NumeroReleve
                    cmd.ExecuteNonQuery()
                    AfficherReleve()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM RELEVE_CNAM WHERE NumeroReleve = " + NumeroReleve
                    cmd.ExecuteNonQuery()
                    AfficherReleve()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub

    Private Sub cmbType_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbType.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNumero.Focus()
        End If
    End Sub

    Private Sub cmbType_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbType.TextChanged
        AfficherReleve()
    End Sub

    Private Sub bRechercher_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRechercher.Click
        cmbType.Focus()
    End Sub

    Private Sub tNumero_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tNumero.GotFocus
        tNumero.Text = System.DateTime.Now.Year.ToString + "/"
        tNumero.Focus()
        tNumero.Select(tNumero.Text.Length, 0)
    End Sub

    Private Sub tNumero_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNumero.KeyUp
        If e.KeyCode = Keys.Enter Then

            If tNumero.Text.Length < 11 And tNumero.Text <> System.DateTime.Now.Year.ToString + "/" Then
                tNumero.Text = tNumero.Text.Substring(0, 5) + tNumero.Text.Substring(5, tNumero.Text.Length - 5).PadLeft(6, "0")
                AfficherReleve()
            Else
                tNumero.Text = ""
            End If

            dpDateDebut.Focus()
        End If
    End Sub

    Private Sub tNumero_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNumero.TextChanged

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub dpDateDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dpDateDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherReleve()
            dpDateFin.Focus()
        End If
    End Sub

    Private Sub fReleveeCNAM_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

    End Sub

    Private Sub C1Button1_Click(sender As System.Object, e As System.EventArgs) Handles C1Button1.Click
        If gReleves.RowCount = 0 Then
            MsgBox("Liste Vide", MsgBoxStyle.OkOnly)
        Else
            Dim ListeReglement As New fListeReglement
            ListeReglement.NuemroReleve = gReleves(gReleves.Row, "NumeroReleve")
            ListeReglement.init()
            ListeReglement.ShowDialog()
            ListeReglement.Close()
            ListeReglement.Dispose()
        End If
    End Sub

    Private Sub CalculValeur()
        MontantARemp = 0.0
        MontantTotal = 0.0
        Reste = 0.0

        tMontantARembourser.Text = MontantARemp.ToString("### ### ##0.000")
        tMontantRegle.Text = (MontantARemp - Reste).ToString("### ### ##0.000")
        tMontantTotal.Text = MontantTotal.ToString("### ### ##0.000")
        tReste.Text = Reste.ToString("### ### ##0.000")

        If gReleves.RowCount <> 0 Then
            For I As Integer = 0 To gReleves.RowCount - 1
                MontantTotal += gReleves(I, "Total")
                MontantARemp += gReleves(I, "Montant")
                Reste += gReleves(I, "Reste")
            Next
            tMontantARembourser.Text = MontantARemp.ToString("### ### ##0.000")
            tMontantRegle.Text = (MontantARemp - Reste).ToString("### ### ##0.000")
            tMontantTotal.Text = MontantTotal.ToString("### ### ##0.000")
            tReste.Text = Reste.ToString("### ### ##0.000")
        End If
    End Sub

    Private Sub bImprimer_Click(sender As Object, e As EventArgs) Handles bImprimer.Click
        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Reporting Ventes" Then
                num = I
            End If
        Next
        Dim dt

        Dim _Parameters As New List(Of ReportParameter)()

        Dim _Type As New ReportParameter()
        _Type.Name = "Type"
        _Type.Values.Add(cmbType.Text)
        _Parameters.Add(_Type)

        Dim _DateDebut As New ReportParameter()
        _DateDebut.Name = "DateDebut"
        _DateDebut.Values.Add(IIf(dpDateDebut.Text = "", New Date(2000, 1, 1), dpDateDebut.Value))
        _Parameters.Add(_DateDebut)

        Dim _DateFin As New ReportParameter()
        _DateFin.Name = "DateFin"
        _DateFin.Values.Add(IIf(dpDateFin.Text = "", Date.Now, dpDateFin.Value).ToString())
        _Parameters.Add(_DateFin)

        Dim _Numero As New ReportParameter()
        _Numero.Name = "Numero"
        _Numero.Values.Add(tNumero.Text)
        _Parameters.Add(_Numero)


        Dim MyViewer As New fImpressionReportingVente

        dt = _SalesReportService.GetEtatListeDesReleveCNAM(IIf(dpDateDebut.Text = "", New Date(2000, 1, 1), dpDateDebut.Value), _
                                                            IIf(dpDateFin.Text = "", Date.Now, dpDateFin.Value), _
                                                            cmbType.Text, _
                                                            tNumero.Text)

        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_EtatListeDesReleveCNAM", dt))
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Pharmacie", _SalesReportService.GetPharmacie()))
        MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatListeDesReleveCNAM.rdl"

        MyViewer.ReportViewer1.LocalReport.SetParameters(_Parameters)

        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel1)

        MyViewer.ReportViewer1.RefreshReport()

        fMain.Tab.SelectedTab.Text = "Impression Reporting Ventes"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    Private Sub dpDateFin_KeyUp(sender As Object, e As KeyEventArgs) Handles dpDateFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherReleve()
        End If
    End Sub

    Private Sub gReleves_FetchRowStyle(sender As Object, e As FetchRowStyleEventArgs) Handles gReleves.FetchRowStyle
        'Afficher en rouge les lignes ou les ventes sont OMF
        If (gReleves.Columns("OMF").CellText(e.Row)) = True Then
            e.CellStyle.BackColor = Color.FromArgb(255, 187, 119)
            Exit Sub
        End If
    End Sub
End Class