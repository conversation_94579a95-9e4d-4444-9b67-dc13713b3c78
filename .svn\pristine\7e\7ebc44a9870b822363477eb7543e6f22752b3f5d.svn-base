﻿Imports System.Data.SqlClient
Imports C1.Win.C1Chart
Imports C1.Win.C1List
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fStatistiqueVente
    Dim dsListe As New DataSet
    Dim cmdListe As New SqlCommand
    Dim daListe As New SqlDataAdapter
    Public CodeArticle As String = ""
    Public Designation As String = ""

    Public Sub Init()
        Dim StrSQL As String
        Dim I As Integer
        Dim J As Integer

        If dsListe.Tables.IndexOf("Statistique") > -1 Then
            dsListe.Tables("Statistique").Clear()
        End If

        StrSQL = "select TOP(0)" + _
                         "case MONTH(Date) when 1 then SUM(qte) end AS Janvier , " + _
                         "case MONTH(Date) when 2 then SUM(qte) end AS Février , " + _
                         "case MONTH(Date) when 3 then SUM(qte) end AS Mars , " + _
                         "case MONTH(Date) when 4 then SUM(qte) end AS Avril , " + _
                         "case MONTH(Date) when 5 then <PERSON><PERSON>(qte) end AS Mai , " + _
                         "case MONTH(Date) when 6 then <PERSON>UM(qte) end AS Juin , " + _
                         "case MONTH(Date) when 7 then SUM(qte) end AS Juillet , " + _
                         "case MONTH(Date) when 8 then SUM(qte) end AS Août , " + _
                         "case MONTH(Date) when 9 then SUM(qte) end AS Septembre , " + _
                         "case MONTH(Date) when 10 then SUM(qte) end AS Octobre , " + _
                         "case MONTH(Date) when 11 then SUM(qte) end AS Novembre , " + _
                         "case MONTH(Date) when 12 then SUM(qte) end AS Decembre  " + _
                         "FROM VENTE " + _
                         "INNER JOIN VENTE_DETAILS ON VENTE_DETAILS.NumeroVente = VENTE.NumeroVente " + _
                         "GROUP BY MONTH(Date)"
        cmdListe.Connection = ConnectionServeur
        cmdListe.CommandText = StrSQL
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "Statistique")

        Try
            dsListe.Tables("Statistique").Columns.Add("Année", Type.GetType("System.String"))
            dsListe.Tables("Statistique").Columns("Année").SetOrdinal(0)
        Catch
        End Try

        With gListe
            .DataSource = dsListe
            .DataMember = "Statistique"
            .Rebind(False)
            .Splits(0).DisplayColumns("Année").Width = 90
            .Splits(0).DisplayColumns("Année").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Janvier").Width = 65
            .Splits(0).DisplayColumns("Janvier").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Février").Width = 65
            .Splits(0).DisplayColumns("Février").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Mars").Width = 65
            .Splits(0).DisplayColumns("Mars").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Avril").Width = 65
            .Splits(0).DisplayColumns("Avril").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Mai").Width = 65
            .Splits(0).DisplayColumns("Mai").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Juin").Width = 65
            .Splits(0).DisplayColumns("Juin").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Juillet").Width = 65
            .Splits(0).DisplayColumns("Juillet").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Août").Width = 65
            .Splits(0).DisplayColumns("Août").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Septembre").Width = 65
            .Splits(0).DisplayColumns("Septembre").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Octobre").Width = 65
            .Splits(0).DisplayColumns("Octobre").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Novembre").Width = 65
            .Splits(0).DisplayColumns("Novembre").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Decembre").Width = 65
            .Splits(0).DisplayColumns("Decembre").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Année").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
        End With

        dsListe.Tables("Statistique").Rows.Add()
        dsListe.Tables("Statistique").Rows(0)("Année") = Year(Date.Now) - 1
        dsListe.Tables("Statistique").Rows.Add()
        dsListe.Tables("Statistique").Rows(1)("Année") = Year(Date.Now)

        Try
            dsListe.Tables("StatistiqueInter").Clear()
        Catch ex As Exception
        End Try

        'StrSQL = "select " + _
        '         "YEAR(date) as Année, MONTH(date) AS Mois, " + _
        '         "Case when Sum(Qte) is null then 0 " + _
        '         "     else Sum(Qte) end AS Qte " + _
        '         "FROM VENTE " + _
        '         "INNER JOIN VENTE_DETAILS ON VENTE_DETAILS.NumeroVente = VENTE.NumeroVente " + _
        '         "WHERE CodeABarre like " & Quote(CodeArticle) & " " + _
        '         "AND (YEAR(Date) = YEAR(CURRENT_TIMESTAMP) or YEAR(Date) = YEAR(CURRENT_TIMESTAMP)-1) " + _
        '         "GROUP BY MONTH(Date), YEAR(date) "

        StrSQL = "select " + _
                "YEAR(date) as Année, MONTH(date) AS Mois, " + _
                "Case when Sum(Qte) is null then 0 " + _
                "     else Sum(Qte) end AS Qte " + _
                "FROM VENTE " + _
                "INNER JOIN VENTE_DETAILS ON VENTE_DETAILS.NumeroVente = VENTE.NumeroVente " + _
                "WHERE CodeArticle = " & Quote(CodeArticle) & " " + _
                "AND (YEAR(Date) = YEAR(CURRENT_TIMESTAMP) or YEAR(Date) = YEAR(CURRENT_TIMESTAMP)-1) " + _
                "GROUP BY MONTH(Date), YEAR(date) "

        cmdListe.Connection = ConnectionServeur
        cmdListe.CommandText = StrSQL
        daListe = New SqlDataAdapter(cmdListe)
        daListe.Fill(dsListe, "StatistiqueInter")

        For I = 0 To 1
            For J = 1 To 12
                dsListe.Tables("Statistique").Rows(I)(J) = "0"
            Next
        Next

        For I = 0 To dsListe.Tables("StatistiqueInter").Rows.Count - 1
            If dsListe.Tables("Statistique").Rows(0)(0).ToString = dsListe.Tables("StatistiqueInter").Rows(I)("Année").ToString Then
                dsListe.Tables("Statistique").Rows(0)(dsListe.Tables("StatistiqueInter").Rows(I)("Mois")) = dsListe.Tables("StatistiqueInter").Rows(I)("Qte")
            End If
            If dsListe.Tables("Statistique").Rows(1)(0).ToString = dsListe.Tables("StatistiqueInter").Rows(I)("Année").ToString Then
                dsListe.Tables("Statistique").Rows(1)(dsListe.Tables("StatistiqueInter").Rows(I)("Mois")) = dsListe.Tables("StatistiqueInter").Rows(I)("Qte")
            End If
        Next

        Dim cgroup As ChartGroup = c1Chart1.ChartGroups.Group0

        cgroup.ChartType = Chart2DTypeEnum.Bar

        'input the data through the series collection

        Dim cdsc As ChartDataSeriesCollection = cgroup.ChartData.SeriesList

        cdsc.Clear()

        'remove default data

        'create the series object from the collection and add data

        'Initialiser la courbe
        Dim cds As ChartDataSeries = cdsc.AddNewSeries()
        Dim cds1 As ChartDataSeries = cdsc.AddNewSeries()
        Dim MonthNames As String() = {"Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"}

        '' Initialiser les valeur
        Dim VenteDate As Double() = {dsListe.Tables("Statistique").Rows(0)(1), dsListe.Tables("Statistique").Rows(0)(2), _
                                 dsListe.Tables("Statistique").Rows(0)(3), dsListe.Tables("Statistique").Rows(0)(4), _
                                 dsListe.Tables("Statistique").Rows(0)(5), dsListe.Tables("Statistique").Rows(0)(6), _
                                 dsListe.Tables("Statistique").Rows(0)(7), dsListe.Tables("Statistique").Rows(0)(8), _
                                 dsListe.Tables("Statistique").Rows(0)(9), dsListe.Tables("Statistique").Rows(0)(10), _
                                 dsListe.Tables("Statistique").Rows(0)(11), dsListe.Tables("Statistique").Rows(0)(12)}
        Dim VenteDateapre As Double() = {dsListe.Tables("Statistique").Rows(1)(1), dsListe.Tables("Statistique").Rows(1)(2), _
                                 dsListe.Tables("Statistique").Rows(1)(3), dsListe.Tables("Statistique").Rows(1)(4), _
                                 dsListe.Tables("Statistique").Rows(1)(5), dsListe.Tables("Statistique").Rows(1)(6), _
                                 dsListe.Tables("Statistique").Rows(1)(7), dsListe.Tables("Statistique").Rows(1)(8), _
                                 dsListe.Tables("Statistique").Rows(1)(9), dsListe.Tables("Statistique").Rows(1)(10), _
                                 dsListe.Tables("Statistique").Rows(1)(11), dsListe.Tables("Statistique").Rows(1)(12)}

        ' Désigner les barre
        cds.X.CopyDataIn(MonthNames)
        cds.Y.CopyDataIn(VenteDate)
        cds1.X.CopyDataIn(MonthNames)
        cds1.Y.CopyDataIn(VenteDateapre)

        ''''''''''''''''
        Dim carea As C1.Win.C1Chart.Area = c1Chart1.ChartArea

        carea.AxisX.Text = "Month"
        carea.AxisY.Text = "Qte"
        'carea.AxisX.ForeColor = Color.Gainsboro
        '''''''''''''Ajouter Legende

        c1Chart1.Legend.Visible = True
        cds1.Label = "Vente" + Date.Today.Year.ToString
        cds.Label = "Vente" + (Date.Today.Year - 1).ToString
        '''''''''''''''''''''''''''''''Ajouter Nom

        c1Chart1.Header.Visible = True
        c1Chart1.Header.Text = "Statistiques des ventes de l'article " & Designation & " : " & (Date.Today.Year - 1) & " et des ventes " & Date.Today.Year & ""


        'add visual effects

        Dim s As C1.Win.C1Chart.Style = carea.Style

        s.ForeColor = Color.White

        s.BackColor = Color.LightBlue

        s.BackColor2 = Color.Azure

        s.GradientStyle = GradientStyleEnum.Radial

        c1Chart1.ColorGeneration = ColorGeneration.Flow

    End Sub
    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        Me.Close()
    End Sub

    Private Sub fStatistiqueVente_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler
    End Sub
    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)
        If e.KeyCode = Keys.F12 Then
            bQuitter_Click(o, e)
            Exit Sub
        End If
    End Sub
End Class