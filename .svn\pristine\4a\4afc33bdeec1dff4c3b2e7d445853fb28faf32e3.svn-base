﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fArticle
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fArticle))
        Me.PAnel = New System.Windows.Forms.Panel()
        Me.bModifierCNAM = New C1.Win.C1Input.C1Button()
        Me.bCodeABarre = New C1.Win.C1Input.C1Button()
        Me.bMonographie = New C1.Win.C1Input.C1Button()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.lNbreDesArticles = New System.Windows.Forms.Label()
        Me.bDupliquerArticle = New C1.Win.C1Input.C1Button()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.lTotalVenteTTC = New System.Windows.Forms.Label()
        Me.lTotalAchatTTC = New System.Windows.Forms.Label()
        Me.lTotalVenteHT = New System.Windows.Forms.Label()
        Me.lTotalAchatHT = New System.Windows.Forms.Label()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.bImprimer = New C1.Win.C1Input.C1Button()
        Me.bSuprimerArticle = New C1.Win.C1Input.C1Button()
        Me.gArticle = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bModifierArticle = New C1.Win.C1Input.C1Button()
        Me.bAjouterArticle = New C1.Win.C1Input.C1Button()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.cmbDCI = New C1.Win.C1List.C1Combo()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.lDCi = New System.Windows.Forms.Label()
        Me.cmbFournisseur = New C1.Win.C1List.C1Combo()
        Me.cmbQteUnit = New C1.Win.C1List.C1Combo()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.cmbLabo = New C1.Win.C1List.C1Combo()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.rdbTousArticlesSuspendus = New System.Windows.Forms.RadioButton()
        Me.rdbSuspendus = New System.Windows.Forms.RadioButton()
        Me.rdbNonSuspendus = New System.Windows.Forms.RadioButton()
        Me.tCode = New C1.Win.C1Input.C1TextBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.cmbSection = New C1.Win.C1List.C1Combo()
        Me.cmbRayon = New C1.Win.C1List.C1Combo()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.chbNonMouvementees = New System.Windows.Forms.CheckBox()
        Me.chbParLot = New System.Windows.Forms.CheckBox()
        Me.chMasquerStock = New System.Windows.Forms.CheckBox()
        Me.tNonMouvementees = New C1.Win.C1Input.C1DateEdit()
        Me.GroupStock = New System.Windows.Forms.GroupBox()
        Me.rdbStockInf0 = New System.Windows.Forms.RadioButton()
        Me.rdbStockEgal0 = New System.Windows.Forms.RadioButton()
        Me.rdbStockSup0 = New System.Windows.Forms.RadioButton()
        Me.rdbTousLesArticles = New System.Windows.Forms.RadioButton()
        Me.cmbDesignation = New C1.Win.C1List.C1Combo()
        Me.cmbCategorie = New C1.Win.C1List.C1Combo()
        Me.cmbCodePCT = New C1.Win.C1List.C1Combo()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.cmbForme = New C1.Win.C1List.C1Combo()
        Me.bViderLesChamps = New C1.Win.C1Input.C1Button()
        Me.LCategorie = New System.Windows.Forms.Label()
        Me.LVille = New System.Windows.Forms.Label()
        Me.LNom = New System.Windows.Forms.Label()
        Me.bRechercher = New C1.Win.C1Input.C1Button()
        Me.CR = New Pharma2000Premium.EtatDesArticles()
        Me.PAnel.SuspendLayout()
        Me.GroupBox4.SuspendLayout()
        CType(Me.gArticle, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.cmbDCI, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbFournisseur, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbQteUnit, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbLabo, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox3.SuspendLayout()
        CType(Me.tCode, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbSection, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbRayon, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNonMouvementees, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupStock.SuspendLayout()
        CType(Me.cmbDesignation, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbCategorie, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbCodePCT, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbForme, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PAnel
        '
        Me.PAnel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.PAnel.Controls.Add(Me.bModifierCNAM)
        Me.PAnel.Controls.Add(Me.bCodeABarre)
        Me.PAnel.Controls.Add(Me.bMonographie)
        Me.PAnel.Controls.Add(Me.GroupBox4)
        Me.PAnel.Controls.Add(Me.bDupliquerArticle)
        Me.PAnel.Controls.Add(Me.Label7)
        Me.PAnel.Controls.Add(Me.Label10)
        Me.PAnel.Controls.Add(Me.Label11)
        Me.PAnel.Controls.Add(Me.Label12)
        Me.PAnel.Controls.Add(Me.bQuitter)
        Me.PAnel.Controls.Add(Me.lTotalVenteTTC)
        Me.PAnel.Controls.Add(Me.lTotalAchatTTC)
        Me.PAnel.Controls.Add(Me.lTotalVenteHT)
        Me.PAnel.Controls.Add(Me.lTotalAchatHT)
        Me.PAnel.Controls.Add(Me.Label9)
        Me.PAnel.Controls.Add(Me.Label8)
        Me.PAnel.Controls.Add(Me.Label6)
        Me.PAnel.Controls.Add(Me.Label5)
        Me.PAnel.Controls.Add(Me.bImprimer)
        Me.PAnel.Controls.Add(Me.bSuprimerArticle)
        Me.PAnel.Controls.Add(Me.gArticle)
        Me.PAnel.Controls.Add(Me.bModifierArticle)
        Me.PAnel.Controls.Add(Me.bAjouterArticle)
        Me.PAnel.Controls.Add(Me.GroupBox1)
        Me.PAnel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.PAnel.Location = New System.Drawing.Point(0, 0)
        Me.PAnel.Name = "PAnel"
        Me.PAnel.Size = New System.Drawing.Size(1362, 566)
        Me.PAnel.TabIndex = 1
        '
        'bModifierCNAM
        '
        Me.bModifierCNAM.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bModifierCNAM.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bModifierCNAM.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bModifierCNAM.Location = New System.Drawing.Point(619, 511)
        Me.bModifierCNAM.Name = "bModifierCNAM"
        Me.bModifierCNAM.Size = New System.Drawing.Size(74, 45)
        Me.bModifierCNAM.TabIndex = 86
        Me.bModifierCNAM.Text = "Modifier CNAM"
        Me.bModifierCNAM.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bModifierCNAM.UseVisualStyleBackColor = True
        Me.bModifierCNAM.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bCodeABarre
        '
        Me.bCodeABarre.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bCodeABarre.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bCodeABarre.Image = Global.Pharma2000Premium.My.Resources.Resources.CodeABarre
        Me.bCodeABarre.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bCodeABarre.Location = New System.Drawing.Point(696, 511)
        Me.bCodeABarre.Name = "bCodeABarre"
        Me.bCodeABarre.Size = New System.Drawing.Size(110, 45)
        Me.bCodeABarre.TabIndex = 85
        Me.bCodeABarre.Text = "Impression" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "Etiquette    " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        Me.bCodeABarre.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bCodeABarre.UseVisualStyleBackColor = True
        Me.bCodeABarre.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bMonographie
        '
        Me.bMonographie.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bMonographie.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bMonographie.Image = Global.Pharma2000Premium.My.Resources.Resources.dexter
        Me.bMonographie.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bMonographie.Location = New System.Drawing.Point(809, 511)
        Me.bMonographie.Name = "bMonographie"
        Me.bMonographie.Size = New System.Drawing.Size(89, 45)
        Me.bMonographie.TabIndex = 84
        Me.bMonographie.Text = "Monographie                    " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        Me.bMonographie.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bMonographie.UseVisualStyleBackColor = True
        Me.bMonographie.Visible = False
        Me.bMonographie.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.Label13)
        Me.GroupBox4.Controls.Add(Me.lNbreDesArticles)
        Me.GroupBox4.Location = New System.Drawing.Point(12, 7)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(176, 113)
        Me.GroupBox4.TabIndex = 0
        Me.GroupBox4.TabStop = False
        Me.GroupBox4.Text = "Article"
        '
        'Label13
        '
        Me.Label13.AutoSize = True
        Me.Label13.Cursor = System.Windows.Forms.Cursors.Default
        Me.Label13.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Label13.Font = New System.Drawing.Font("Microsoft Sans Serif", 21.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label13.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label13.Location = New System.Drawing.Point(6, 23)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(164, 33)
        Me.Label13.TabIndex = 10
        Me.Label13.Text = "ARTICLES"
        Me.Label13.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lNbreDesArticles
        '
        Me.lNbreDesArticles.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lNbreDesArticles.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNbreDesArticles.ForeColor = System.Drawing.Color.Green
        Me.lNbreDesArticles.Location = New System.Drawing.Point(10, 63)
        Me.lNbreDesArticles.Name = "lNbreDesArticles"
        Me.lNbreDesArticles.Size = New System.Drawing.Size(160, 45)
        Me.lNbreDesArticles.TabIndex = 14
        Me.lNbreDesArticles.Text = "-"
        '
        'bDupliquerArticle
        '
        Me.bDupliquerArticle.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bDupliquerArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bDupliquerArticle.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bDupliquerArticle.Location = New System.Drawing.Point(471, 539)
        Me.bDupliquerArticle.Name = "bDupliquerArticle"
        Me.bDupliquerArticle.Size = New System.Drawing.Size(142, 20)
        Me.bDupliquerArticle.TabIndex = 3
        Me.bDupliquerArticle.Text = "Dupliquer"
        Me.bDupliquerArticle.UseVisualStyleBackColor = True
        Me.bDupliquerArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label7
        '
        Me.Label7.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label7.AutoSize = True
        Me.Label7.Location = New System.Drawing.Point(515, 523)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(75, 13)
        Me.Label7.TabIndex = 82
        Me.Label7.Text = "En commande"
        '
        'Label10
        '
        Me.Label10.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label10.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(250, Byte), Integer), CType(CType(200, Byte), Integer))
        Me.Label10.Location = New System.Drawing.Point(470, 523)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(39, 13)
        Me.Label10.TabIndex = 83
        '
        'Label11
        '
        Me.Label11.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label11.AutoSize = True
        Me.Label11.Location = New System.Drawing.Point(515, 507)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(59, 13)
        Me.Label11.TabIndex = 80
        Me.Label11.Text = "En rupture "
        '
        'Label12
        '
        Me.Label12.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label12.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(220, Byte), Integer), CType(CType(220, Byte), Integer))
        Me.Label12.Location = New System.Drawing.Point(470, 507)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(39, 13)
        Me.Label12.TabIndex = 81
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(1261, 511)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(92, 45)
        Me.bQuitter.TabIndex = 8
        Me.bQuitter.Text = "Fermer             F12   "
        Me.bQuitter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lTotalVenteTTC
        '
        Me.lTotalVenteTTC.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lTotalVenteTTC.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lTotalVenteTTC.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotalVenteTTC.ForeColor = System.Drawing.Color.Green
        Me.lTotalVenteTTC.Location = New System.Drawing.Point(349, 536)
        Me.lTotalVenteTTC.Name = "lTotalVenteTTC"
        Me.lTotalVenteTTC.Size = New System.Drawing.Size(110, 22)
        Me.lTotalVenteTTC.TabIndex = 22
        Me.lTotalVenteTTC.Text = "0.000"
        Me.lTotalVenteTTC.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lTotalAchatTTC
        '
        Me.lTotalAchatTTC.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lTotalAchatTTC.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lTotalAchatTTC.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotalAchatTTC.ForeColor = System.Drawing.Color.Green
        Me.lTotalAchatTTC.Location = New System.Drawing.Point(349, 507)
        Me.lTotalAchatTTC.Name = "lTotalAchatTTC"
        Me.lTotalAchatTTC.Size = New System.Drawing.Size(110, 22)
        Me.lTotalAchatTTC.TabIndex = 21
        Me.lTotalAchatTTC.Text = "0.000"
        Me.lTotalAchatTTC.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lTotalVenteHT
        '
        Me.lTotalVenteHT.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lTotalVenteHT.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lTotalVenteHT.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotalVenteHT.ForeColor = System.Drawing.Color.Green
        Me.lTotalVenteHT.Location = New System.Drawing.Point(117, 537)
        Me.lTotalVenteHT.Name = "lTotalVenteHT"
        Me.lTotalVenteHT.Size = New System.Drawing.Size(110, 22)
        Me.lTotalVenteHT.TabIndex = 20
        Me.lTotalVenteHT.Text = "0.000"
        Me.lTotalVenteHT.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lTotalAchatHT
        '
        Me.lTotalAchatHT.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lTotalAchatHT.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lTotalAchatHT.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotalAchatHT.ForeColor = System.Drawing.Color.Green
        Me.lTotalAchatHT.Location = New System.Drawing.Point(117, 509)
        Me.lTotalAchatHT.Name = "lTotalAchatHT"
        Me.lTotalAchatHT.Size = New System.Drawing.Size(110, 22)
        Me.lTotalAchatHT.TabIndex = 19
        Me.lTotalAchatHT.Text = "0.000"
        Me.lTotalAchatHT.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label9
        '
        Me.Label9.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label9.AutoSize = True
        Me.Label9.Location = New System.Drawing.Point(240, 542)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(106, 13)
        Me.Label9.TabIndex = 18
        Me.Label9.Text = "Valeur de vente TTC"
        '
        'Label8
        '
        Me.Label8.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label8.AutoSize = True
        Me.Label8.Location = New System.Drawing.Point(247, 513)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(99, 13)
        Me.Label8.TabIndex = 18
        Me.Label8.Text = "Valeur d'achat TTC"
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label6.AutoSize = True
        Me.Label6.Location = New System.Drawing.Point(11, 542)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(100, 13)
        Me.Label6.TabIndex = 16
        Me.Label6.Text = "Valeur de vente HT"
        '
        'Label5
        '
        Me.Label5.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label5.AutoSize = True
        Me.Label5.Location = New System.Drawing.Point(18, 513)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(93, 13)
        Me.Label5.TabIndex = 15
        Me.Label5.Text = "Valeur d'achat HT"
        '
        'bImprimer
        '
        Me.bImprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.imprmante1
        Me.bImprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bImprimer.Location = New System.Drawing.Point(1072, 511)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(95, 45)
        Me.bImprimer.TabIndex = 6
        Me.bImprimer.Text = "Imprimer             F9"
        Me.bImprimer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bSuprimerArticle
        '
        Me.bSuprimerArticle.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bSuprimerArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSuprimerArticle.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler2
        Me.bSuprimerArticle.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSuprimerArticle.Location = New System.Drawing.Point(901, 511)
        Me.bSuprimerArticle.Name = "bSuprimerArticle"
        Me.bSuprimerArticle.Size = New System.Drawing.Size(84, 45)
        Me.bSuprimerArticle.TabIndex = 4
        Me.bSuprimerArticle.Text = "Supprimer              F7"
        Me.bSuprimerArticle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSuprimerArticle.UseVisualStyleBackColor = True
        Me.bSuprimerArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gArticle
        '
        Me.gArticle.AllowUpdate = False
        Me.gArticle.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gArticle.GroupByCaption = "Drag a column header here to group by that column"
        Me.gArticle.Images.Add(CType(resources.GetObject("gArticle.Images"), System.Drawing.Image))
        Me.gArticle.Location = New System.Drawing.Point(12, 126)
        Me.gArticle.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gArticle.Name = "gArticle"
        Me.gArticle.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gArticle.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gArticle.PreviewInfo.ZoomFactor = 75.0R
        Me.gArticle.PrintInfo.PageSettings = CType(resources.GetObject("gArticle.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gArticle.Size = New System.Drawing.Size(1338, 372)
        Me.gArticle.TabIndex = 2
        Me.gArticle.Text = "C1TrueDBGrid1"
        Me.gArticle.PropBag = resources.GetString("gArticle.PropBag")
        '
        'bModifierArticle
        '
        Me.bModifierArticle.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bModifierArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bModifierArticle.Image = Global.Pharma2000Premium.My.Resources.Resources.voir_modifierfpharmacien
        Me.bModifierArticle.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bModifierArticle.Location = New System.Drawing.Point(1171, 511)
        Me.bModifierArticle.Name = "bModifierArticle"
        Me.bModifierArticle.Size = New System.Drawing.Size(87, 45)
        Me.bModifierArticle.TabIndex = 7
        Me.bModifierArticle.Text = "Modifier             F8   "
        Me.bModifierArticle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bModifierArticle.UseVisualStyleBackColor = True
        Me.bModifierArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouterArticle
        '
        Me.bAjouterArticle.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouterArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouterArticle.Image = Global.Pharma2000Premium.My.Resources.Resources.nouveau
        Me.bAjouterArticle.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouterArticle.Location = New System.Drawing.Point(988, 511)
        Me.bAjouterArticle.Name = "bAjouterArticle"
        Me.bAjouterArticle.Size = New System.Drawing.Size(81, 45)
        Me.bAjouterArticle.TabIndex = 5
        Me.bAjouterArticle.Text = "Ajouter                   F5"
        Me.bAjouterArticle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouterArticle.UseVisualStyleBackColor = True
        Me.bAjouterArticle.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.Controls.Add(Me.cmbDCI)
        Me.GroupBox1.Controls.Add(Me.Label16)
        Me.GroupBox1.Controls.Add(Me.lDCi)
        Me.GroupBox1.Controls.Add(Me.cmbFournisseur)
        Me.GroupBox1.Controls.Add(Me.cmbQteUnit)
        Me.GroupBox1.Controls.Add(Me.Label15)
        Me.GroupBox1.Controls.Add(Me.cmbLabo)
        Me.GroupBox1.Controls.Add(Me.Label14)
        Me.GroupBox1.Controls.Add(Me.GroupBox3)
        Me.GroupBox1.Controls.Add(Me.tCode)
        Me.GroupBox1.Controls.Add(Me.Label4)
        Me.GroupBox1.Controls.Add(Me.cmbSection)
        Me.GroupBox1.Controls.Add(Me.cmbRayon)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Controls.Add(Me.Label3)
        Me.GroupBox1.Controls.Add(Me.chbNonMouvementees)
        Me.GroupBox1.Controls.Add(Me.chbParLot)
        Me.GroupBox1.Controls.Add(Me.chMasquerStock)
        Me.GroupBox1.Controls.Add(Me.tNonMouvementees)
        Me.GroupBox1.Controls.Add(Me.GroupStock)
        Me.GroupBox1.Controls.Add(Me.cmbDesignation)
        Me.GroupBox1.Controls.Add(Me.cmbCategorie)
        Me.GroupBox1.Controls.Add(Me.cmbCodePCT)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Controls.Add(Me.cmbForme)
        Me.GroupBox1.Controls.Add(Me.bViderLesChamps)
        Me.GroupBox1.Controls.Add(Me.LCategorie)
        Me.GroupBox1.Controls.Add(Me.LVille)
        Me.GroupBox1.Controls.Add(Me.LNom)
        Me.GroupBox1.Controls.Add(Me.bRechercher)
        Me.GroupBox1.Location = New System.Drawing.Point(194, 7)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(1156, 113)
        Me.GroupBox1.TabIndex = 1
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Critères de recherche"
        '
        'cmbDCI
        '
        Me.cmbDCI.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbDCI.Caption = ""
        Me.cmbDCI.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbDCI.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbDCI.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbDCI.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbDCI.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbDCI.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbDCI.Images.Add(CType(resources.GetObject("cmbDCI.Images"), System.Drawing.Image))
        Me.cmbDCI.Location = New System.Drawing.Point(360, 50)
        Me.cmbDCI.MatchEntryTimeout = CType(2000, Long)
        Me.cmbDCI.MaxDropDownItems = CType(5, Short)
        Me.cmbDCI.MaxLength = 32767
        Me.cmbDCI.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbDCI.Name = "cmbDCI"
        Me.cmbDCI.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbDCI.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbDCI.Size = New System.Drawing.Size(99, 22)
        Me.cmbDCI.TabIndex = 19
        Me.cmbDCI.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbDCI.PropBag = resources.GetString("cmbDCI.PropBag")
        '
        'Label16
        '
        Me.Label16.AutoSize = True
        Me.Label16.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label16.Location = New System.Drawing.Point(495, 85)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(61, 13)
        Me.Label16.TabIndex = 52
        Me.Label16.Text = "Fournisseur"
        '
        'lDCi
        '
        Me.lDCi.AutoSize = True
        Me.lDCi.Location = New System.Drawing.Point(329, 53)
        Me.lDCi.Name = "lDCi"
        Me.lDCi.Size = New System.Drawing.Size(25, 13)
        Me.lDCi.TabIndex = 20
        Me.lDCi.Text = "DCI"
        '
        'cmbFournisseur
        '
        Me.cmbFournisseur.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbFournisseur.Caption = ""
        Me.cmbFournisseur.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbFournisseur.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbFournisseur.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbFournisseur.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbFournisseur.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbFournisseur.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbFournisseur.Images.Add(CType(resources.GetObject("cmbFournisseur.Images"), System.Drawing.Image))
        Me.cmbFournisseur.Location = New System.Drawing.Point(562, 78)
        Me.cmbFournisseur.MatchEntryTimeout = CType(2000, Long)
        Me.cmbFournisseur.MaxDropDownItems = CType(5, Short)
        Me.cmbFournisseur.MaxLength = 32767
        Me.cmbFournisseur.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbFournisseur.Name = "cmbFournisseur"
        Me.cmbFournisseur.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbFournisseur.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbFournisseur.Size = New System.Drawing.Size(139, 22)
        Me.cmbFournisseur.TabIndex = 51
        Me.cmbFournisseur.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbFournisseur.PropBag = resources.GetString("cmbFournisseur.PropBag")
        '
        'cmbQteUnit
        '
        Me.cmbQteUnit.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbQteUnit.Caption = ""
        Me.cmbQteUnit.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbQteUnit.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbQteUnit.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbQteUnit.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbQteUnit.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbQteUnit.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbQteUnit.Images.Add(CType(resources.GetObject("cmbQteUnit.Images"), System.Drawing.Image))
        Me.cmbQteUnit.Location = New System.Drawing.Point(760, 78)
        Me.cmbQteUnit.MatchEntryTimeout = CType(2000, Long)
        Me.cmbQteUnit.MaxDropDownItems = CType(5, Short)
        Me.cmbQteUnit.MaxLength = 32767
        Me.cmbQteUnit.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbQteUnit.Name = "cmbQteUnit"
        Me.cmbQteUnit.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbQteUnit.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbQteUnit.Size = New System.Drawing.Size(92, 22)
        Me.cmbQteUnit.TabIndex = 49
        Me.cmbQteUnit.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbQteUnit.PropBag = resources.GetString("cmbQteUnit.PropBag")
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label15.Location = New System.Drawing.Point(710, 85)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(44, 13)
        Me.Label15.TabIndex = 50
        Me.Label15.Text = "Qte unit"
        '
        'cmbLabo
        '
        Me.cmbLabo.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbLabo.Caption = ""
        Me.cmbLabo.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbLabo.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbLabo.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbLabo.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbLabo.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbLabo.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbLabo.Images.Add(CType(resources.GetObject("cmbLabo.Images"), System.Drawing.Image))
        Me.cmbLabo.Location = New System.Drawing.Point(502, 50)
        Me.cmbLabo.MatchEntryTimeout = CType(2000, Long)
        Me.cmbLabo.MaxDropDownItems = CType(5, Short)
        Me.cmbLabo.MaxLength = 32767
        Me.cmbLabo.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbLabo.Name = "cmbLabo"
        Me.cmbLabo.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbLabo.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbLabo.Size = New System.Drawing.Size(85, 22)
        Me.cmbLabo.TabIndex = 47
        Me.cmbLabo.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbLabo.PropBag = resources.GetString("cmbLabo.PropBag")
        '
        'Label14
        '
        Me.Label14.AutoSize = True
        Me.Label14.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label14.Location = New System.Drawing.Point(465, 54)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(31, 13)
        Me.Label14.TabIndex = 48
        Me.Label14.Text = "Labo"
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.rdbTousArticlesSuspendus)
        Me.GroupBox3.Controls.Add(Me.rdbSuspendus)
        Me.GroupBox3.Controls.Add(Me.rdbNonSuspendus)
        Me.GroupBox3.Location = New System.Drawing.Point(871, 60)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(158, 48)
        Me.GroupBox3.TabIndex = 11
        Me.GroupBox3.TabStop = False
        '
        'rdbTousArticlesSuspendus
        '
        Me.rdbTousArticlesSuspendus.AutoSize = True
        Me.rdbTousArticlesSuspendus.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbTousArticlesSuspendus.Location = New System.Drawing.Point(6, 7)
        Me.rdbTousArticlesSuspendus.Name = "rdbTousArticlesSuspendus"
        Me.rdbTousArticlesSuspendus.Size = New System.Drawing.Size(49, 17)
        Me.rdbTousArticlesSuspendus.TabIndex = 2
        Me.rdbTousArticlesSuspendus.TabStop = True
        Me.rdbTousArticlesSuspendus.Text = "Tous"
        Me.rdbTousArticlesSuspendus.UseVisualStyleBackColor = True
        '
        'rdbSuspendus
        '
        Me.rdbSuspendus.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbSuspendus.Location = New System.Drawing.Point(77, 7)
        Me.rdbSuspendus.Name = "rdbSuspendus"
        Me.rdbSuspendus.Size = New System.Drawing.Size(78, 16)
        Me.rdbSuspendus.TabIndex = 0
        Me.rdbSuspendus.TabStop = True
        Me.rdbSuspendus.Text = "Suspendus"
        Me.rdbSuspendus.UseVisualStyleBackColor = True
        '
        'rdbNonSuspendus
        '
        Me.rdbNonSuspendus.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbNonSuspendus.Location = New System.Drawing.Point(6, 25)
        Me.rdbNonSuspendus.Name = "rdbNonSuspendus"
        Me.rdbNonSuspendus.Size = New System.Drawing.Size(102, 17)
        Me.rdbNonSuspendus.TabIndex = 1
        Me.rdbNonSuspendus.TabStop = True
        Me.rdbNonSuspendus.Text = "Non suspendus"
        Me.rdbNonSuspendus.UseVisualStyleBackColor = True
        '
        'tCode
        '
        Me.tCode.AutoSize = False
        Me.tCode.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCode.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCode.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCode.Location = New System.Drawing.Point(48, 16)
        Me.tCode.Name = "tCode"
        Me.tCode.Size = New System.Drawing.Size(94, 22)
        Me.tCode.TabIndex = 0
        Me.tCode.Tag = Nothing
        Me.tCode.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCode.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.Location = New System.Drawing.Point(11, 20)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(32, 13)
        Me.Label4.TabIndex = 46
        Me.Label4.Text = "Code"
        '
        'cmbSection
        '
        Me.cmbSection.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbSection.Caption = ""
        Me.cmbSection.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbSection.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbSection.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbSection.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbSection.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbSection.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbSection.Images.Add(CType(resources.GetObject("cmbSection.Images"), System.Drawing.Image))
        Me.cmbSection.Location = New System.Drawing.Point(641, 50)
        Me.cmbSection.MatchEntryTimeout = CType(2000, Long)
        Me.cmbSection.MaxDropDownItems = CType(5, Short)
        Me.cmbSection.MaxLength = 32767
        Me.cmbSection.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbSection.Name = "cmbSection"
        Me.cmbSection.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbSection.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbSection.Size = New System.Drawing.Size(60, 22)
        Me.cmbSection.TabIndex = 8
        Me.cmbSection.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbSection.PropBag = resources.GetString("cmbSection.PropBag")
        '
        'cmbRayon
        '
        Me.cmbRayon.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbRayon.Caption = ""
        Me.cmbRayon.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbRayon.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbRayon.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbRayon.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbRayon.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbRayon.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbRayon.Images.Add(CType(resources.GetObject("cmbRayon.Images"), System.Drawing.Image))
        Me.cmbRayon.Location = New System.Drawing.Point(760, 50)
        Me.cmbRayon.MatchEntryTimeout = CType(2000, Long)
        Me.cmbRayon.MaxDropDownItems = CType(5, Short)
        Me.cmbRayon.MaxLength = 32767
        Me.cmbRayon.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbRayon.Name = "cmbRayon"
        Me.cmbRayon.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbRayon.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbRayon.Size = New System.Drawing.Size(92, 22)
        Me.cmbRayon.TabIndex = 10
        Me.cmbRayon.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbRayon.PropBag = resources.GetString("cmbRayon.PropBag")
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(718, 54)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(38, 13)
        Me.Label2.TabIndex = 45
        Me.Label2.Text = "Rayon"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.Location = New System.Drawing.Point(594, 54)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(43, 13)
        Me.Label3.TabIndex = 44
        Me.Label3.Text = "Section"
        '
        'chbNonMouvementees
        '
        Me.chbNonMouvementees.AutoSize = True
        Me.chbNonMouvementees.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chbNonMouvementees.Location = New System.Drawing.Point(14, 84)
        Me.chbNonMouvementees.Name = "chbNonMouvementees"
        Me.chbNonMouvementees.Size = New System.Drawing.Size(129, 17)
        Me.chbNonMouvementees.TabIndex = 2
        Me.chbNonMouvementees.Text = "Non vendus depuis le"
        Me.chbNonMouvementees.UseVisualStyleBackColor = True
        '
        'chbParLot
        '
        Me.chbParLot.AutoSize = True
        Me.chbParLot.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chbParLot.Location = New System.Drawing.Point(373, 84)
        Me.chbParLot.Name = "chbParLot"
        Me.chbParLot.Size = New System.Drawing.Size(112, 17)
        Me.chbParLot.TabIndex = 7
        Me.chbParLot.Text = "Impression détaillé"
        Me.chbParLot.UseVisualStyleBackColor = True
        '
        'chMasquerStock
        '
        Me.chMasquerStock.AutoSize = True
        Me.chMasquerStock.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chMasquerStock.Location = New System.Drawing.Point(260, 84)
        Me.chMasquerStock.Name = "chMasquerStock"
        Me.chMasquerStock.Size = New System.Drawing.Size(107, 17)
        Me.chMasquerStock.TabIndex = 6
        Me.chMasquerStock.Text = "Masquer le stock"
        Me.chMasquerStock.UseVisualStyleBackColor = True
        '
        'tNonMouvementees
        '
        Me.tNonMouvementees.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNonMouvementees.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.tNonMouvementees.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.tNonMouvementees.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNonMouvementees.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNonMouvementees.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        Me.tNonMouvementees.EditFormat.Inherit = CType(((((C1.Win.C1Input.FormatInfoInheritFlags.CustomFormat Or C1.Win.C1Input.FormatInfoInheritFlags.NullText) _
            Or C1.Win.C1Input.FormatInfoInheritFlags.EmptyAsNull) _
            Or C1.Win.C1Input.FormatInfoInheritFlags.TrimStart) _
            Or C1.Win.C1Input.FormatInfoInheritFlags.TrimEnd), C1.Win.C1Input.FormatInfoInheritFlags)
        Me.tNonMouvementees.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNonMouvementees.FormatType = C1.Win.C1Input.FormatTypeEnum.LongDate
        Me.tNonMouvementees.Location = New System.Drawing.Point(147, 83)
        Me.tNonMouvementees.Name = "tNonMouvementees"
        Me.tNonMouvementees.Size = New System.Drawing.Size(107, 18)
        Me.tNonMouvementees.TabIndex = 3
        Me.tNonMouvementees.Tag = Nothing
        Me.tNonMouvementees.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNonMouvementees.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupStock
        '
        Me.GroupStock.Controls.Add(Me.rdbStockInf0)
        Me.GroupStock.Controls.Add(Me.rdbStockEgal0)
        Me.GroupStock.Controls.Add(Me.rdbStockSup0)
        Me.GroupStock.Controls.Add(Me.rdbTousLesArticles)
        Me.GroupStock.Location = New System.Drawing.Point(871, 4)
        Me.GroupStock.Name = "GroupStock"
        Me.GroupStock.Size = New System.Drawing.Size(156, 58)
        Me.GroupStock.TabIndex = 12
        Me.GroupStock.TabStop = False
        '
        'rdbStockInf0
        '
        Me.rdbStockInf0.AutoSize = True
        Me.rdbStockInf0.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbStockInf0.Location = New System.Drawing.Point(77, 12)
        Me.rdbStockInf0.Name = "rdbStockInf0"
        Me.rdbStockInf0.Size = New System.Drawing.Size(71, 17)
        Me.rdbStockInf0.TabIndex = 2
        Me.rdbStockInf0.TabStop = True
        Me.rdbStockInf0.Text = "Stock < 0"
        Me.rdbStockInf0.UseVisualStyleBackColor = True
        '
        'rdbStockEgal0
        '
        Me.rdbStockEgal0.AutoSize = True
        Me.rdbStockEgal0.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbStockEgal0.Location = New System.Drawing.Point(6, 34)
        Me.rdbStockEgal0.Name = "rdbStockEgal0"
        Me.rdbStockEgal0.Size = New System.Drawing.Size(71, 17)
        Me.rdbStockEgal0.TabIndex = 1
        Me.rdbStockEgal0.TabStop = True
        Me.rdbStockEgal0.Text = "Stock = 0"
        Me.rdbStockEgal0.UseVisualStyleBackColor = True
        '
        'rdbStockSup0
        '
        Me.rdbStockSup0.AutoSize = True
        Me.rdbStockSup0.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbStockSup0.Location = New System.Drawing.Point(6, 12)
        Me.rdbStockSup0.Name = "rdbStockSup0"
        Me.rdbStockSup0.Size = New System.Drawing.Size(71, 17)
        Me.rdbStockSup0.TabIndex = 0
        Me.rdbStockSup0.TabStop = True
        Me.rdbStockSup0.Text = "Stock > 0"
        Me.rdbStockSup0.UseVisualStyleBackColor = True
        '
        'rdbTousLesArticles
        '
        Me.rdbTousLesArticles.AutoSize = True
        Me.rdbTousLesArticles.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdbTousLesArticles.Location = New System.Drawing.Point(77, 34)
        Me.rdbTousLesArticles.Name = "rdbTousLesArticles"
        Me.rdbTousLesArticles.Size = New System.Drawing.Size(49, 17)
        Me.rdbTousLesArticles.TabIndex = 3
        Me.rdbTousLesArticles.TabStop = True
        Me.rdbTousLesArticles.Text = "Tous"
        Me.rdbTousLesArticles.UseVisualStyleBackColor = True
        '
        'cmbDesignation
        '
        Me.cmbDesignation.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbDesignation.Caption = ""
        Me.cmbDesignation.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbDesignation.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbDesignation.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbDesignation.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbDesignation.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbDesignation.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbDesignation.Images.Add(CType(resources.GetObject("cmbDesignation.Images"), System.Drawing.Image))
        Me.cmbDesignation.Location = New System.Drawing.Point(209, 16)
        Me.cmbDesignation.MatchEntryTimeout = CType(2000, Long)
        Me.cmbDesignation.MaxDropDownItems = CType(20, Short)
        Me.cmbDesignation.MaxLength = 32767
        Me.cmbDesignation.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbDesignation.Name = "cmbDesignation"
        Me.cmbDesignation.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbDesignation.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbDesignation.Size = New System.Drawing.Size(492, 22)
        Me.cmbDesignation.TabIndex = 4
        Me.cmbDesignation.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbDesignation.PropBag = resources.GetString("cmbDesignation.PropBag")
        '
        'cmbCategorie
        '
        Me.cmbCategorie.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbCategorie.Caption = ""
        Me.cmbCategorie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbCategorie.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbCategorie.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbCategorie.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbCategorie.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbCategorie.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbCategorie.Images.Add(CType(resources.GetObject("cmbCategorie.Images"), System.Drawing.Image))
        Me.cmbCategorie.Location = New System.Drawing.Point(209, 50)
        Me.cmbCategorie.MatchEntryTimeout = CType(2000, Long)
        Me.cmbCategorie.MaxDropDownItems = CType(20, Short)
        Me.cmbCategorie.MaxLength = 32767
        Me.cmbCategorie.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbCategorie.Name = "cmbCategorie"
        Me.cmbCategorie.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbCategorie.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbCategorie.Size = New System.Drawing.Size(114, 22)
        Me.cmbCategorie.TabIndex = 5
        Me.cmbCategorie.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbCategorie.PropBag = resources.GetString("cmbCategorie.PropBag")
        '
        'cmbCodePCT
        '
        Me.cmbCodePCT.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbCodePCT.Caption = ""
        Me.cmbCodePCT.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbCodePCT.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbCodePCT.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbCodePCT.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbCodePCT.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbCodePCT.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbCodePCT.Images.Add(CType(resources.GetObject("cmbCodePCT.Images"), System.Drawing.Image))
        Me.cmbCodePCT.Location = New System.Drawing.Point(760, 16)
        Me.cmbCodePCT.MatchEntryTimeout = CType(2000, Long)
        Me.cmbCodePCT.MaxDropDownItems = CType(20, Short)
        Me.cmbCodePCT.MaxLength = 32767
        Me.cmbCodePCT.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbCodePCT.Name = "cmbCodePCT"
        Me.cmbCodePCT.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbCodePCT.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbCodePCT.Size = New System.Drawing.Size(92, 22)
        Me.cmbCodePCT.TabIndex = 9
        Me.cmbCodePCT.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbCodePCT.PropBag = resources.GetString("cmbCodePCT.PropBag")
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(702, 20)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(56, 13)
        Me.Label1.TabIndex = 32
        Me.Label1.Text = "Code PCT"
        '
        'cmbForme
        '
        Me.cmbForme.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbForme.Caption = ""
        Me.cmbForme.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbForme.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbForme.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbForme.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbForme.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbForme.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbForme.Images.Add(CType(resources.GetObject("cmbForme.Images"), System.Drawing.Image))
        Me.cmbForme.Location = New System.Drawing.Point(49, 50)
        Me.cmbForme.MatchEntryTimeout = CType(2000, Long)
        Me.cmbForme.MaxDropDownItems = CType(20, Short)
        Me.cmbForme.MaxLength = 32767
        Me.cmbForme.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbForme.Name = "cmbForme"
        Me.cmbForme.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbForme.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbForme.Size = New System.Drawing.Size(93, 22)
        Me.cmbForme.TabIndex = 1
        Me.cmbForme.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbForme.PropBag = resources.GetString("cmbForme.PropBag")
        '
        'bViderLesChamps
        '
        Me.bViderLesChamps.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bViderLesChamps.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bViderLesChamps.Location = New System.Drawing.Point(1033, 74)
        Me.bViderLesChamps.Name = "bViderLesChamps"
        Me.bViderLesChamps.Size = New System.Drawing.Size(117, 31)
        Me.bViderLesChamps.TabIndex = 14
        Me.bViderLesChamps.Text = "Vider les champs"
        Me.bViderLesChamps.UseVisualStyleBackColor = True
        Me.bViderLesChamps.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'LCategorie
        '
        Me.LCategorie.AutoSize = True
        Me.LCategorie.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LCategorie.Location = New System.Drawing.Point(153, 53)
        Me.LCategorie.Name = "LCategorie"
        Me.LCategorie.Size = New System.Drawing.Size(52, 13)
        Me.LCategorie.TabIndex = 12
        Me.LCategorie.Text = "Catégorie"
        '
        'LVille
        '
        Me.LVille.AutoSize = True
        Me.LVille.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LVille.Location = New System.Drawing.Point(11, 53)
        Me.LVille.Name = "LVille"
        Me.LVille.Size = New System.Drawing.Size(36, 13)
        Me.LVille.TabIndex = 11
        Me.LVille.Text = "Forme"
        '
        'LNom
        '
        Me.LNom.AutoSize = True
        Me.LNom.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LNom.Location = New System.Drawing.Point(146, 20)
        Me.LNom.Name = "LNom"
        Me.LNom.Size = New System.Drawing.Size(63, 13)
        Me.LNom.TabIndex = 10
        Me.LNom.Text = "Désignation"
        '
        'bRechercher
        '
        Me.bRechercher.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bRechercher.Image = Global.Pharma2000Premium.My.Resources.Resources.recherche2
        Me.bRechercher.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bRechercher.Location = New System.Drawing.Point(1033, 12)
        Me.bRechercher.Name = "bRechercher"
        Me.bRechercher.Size = New System.Drawing.Size(117, 55)
        Me.bRechercher.TabIndex = 13
        Me.bRechercher.Text = "Rechercher                          F6"
        Me.bRechercher.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bRechercher.UseVisualStyleBackColor = True
        Me.bRechercher.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fArticle
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1362, 566)
        Me.Controls.Add(Me.PAnel)
        Me.Name = "fArticle"
        Me.Text = "fArticle"
        Me.PAnel.ResumeLayout(False)
        Me.PAnel.PerformLayout()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.gArticle, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.cmbDCI, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbFournisseur, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbQteUnit, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbLabo, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        CType(Me.tCode, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbSection, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbRayon, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNonMouvementees, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupStock.ResumeLayout(False)
        Me.GroupStock.PerformLayout()
        CType(Me.cmbDesignation, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbCategorie, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbCodePCT, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbForme, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents PAnel As System.Windows.Forms.Panel
    Friend WithEvents bSuprimerArticle As C1.Win.C1Input.C1Button
    Friend WithEvents gArticle As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bModifierArticle As C1.Win.C1Input.C1Button
    Friend WithEvents bAjouterArticle As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents cmbDesignation As C1.Win.C1List.C1Combo
    Friend WithEvents cmbCategorie As C1.Win.C1List.C1Combo
    Friend WithEvents cmbCodePCT As C1.Win.C1List.C1Combo
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents cmbForme As C1.Win.C1List.C1Combo
    Friend WithEvents bViderLesChamps As C1.Win.C1Input.C1Button
    Friend WithEvents LCategorie As System.Windows.Forms.Label
    Friend WithEvents LVille As System.Windows.Forms.Label
    Friend WithEvents LNom As System.Windows.Forms.Label
    Friend WithEvents bRechercher As C1.Win.C1Input.C1Button
    Friend WithEvents rdbTousLesArticles As System.Windows.Forms.RadioButton
    Friend WithEvents GroupStock As System.Windows.Forms.GroupBox
    Friend WithEvents rdbStockInf0 As System.Windows.Forms.RadioButton
    Friend WithEvents rdbStockEgal0 As System.Windows.Forms.RadioButton
    Friend WithEvents rdbStockSup0 As System.Windows.Forms.RadioButton
    Friend WithEvents rdbSuspendus As System.Windows.Forms.RadioButton
    Friend WithEvents rdbNonSuspendus As System.Windows.Forms.RadioButton
    Friend WithEvents rdbTousArticlesSuspendus As System.Windows.Forms.RadioButton
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
    Friend WithEvents CR As Pharma2000Premium.EtatDesArticles
    Friend WithEvents lNbreDesArticles As System.Windows.Forms.Label
    Friend WithEvents tNonMouvementees As C1.Win.C1Input.C1DateEdit
    Friend WithEvents chMasquerStock As System.Windows.Forms.CheckBox
    Friend WithEvents chbNonMouvementees As System.Windows.Forms.CheckBox
    Friend WithEvents cmbSection As C1.Win.C1List.C1Combo
    Friend WithEvents cmbRayon As C1.Win.C1List.C1Combo
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents tCode As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents lTotalVenteTTC As System.Windows.Forms.Label
    Friend WithEvents lTotalAchatTTC As System.Windows.Forms.Label
    Friend WithEvents lTotalVenteHT As System.Windows.Forms.Label
    Friend WithEvents lTotalAchatHT As System.Windows.Forms.Label
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents bDupliquerArticle As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents chbParLot As System.Windows.Forms.CheckBox
    Friend WithEvents bMonographie As C1.Win.C1Input.C1Button
    Friend WithEvents bCodeABarre As C1.Win.C1Input.C1Button
    Friend WithEvents cmbLabo As C1.Win.C1List.C1Combo
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents cmbQteUnit As C1.Win.C1List.C1Combo
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents cmbFournisseur As C1.Win.C1List.C1Combo
    Friend WithEvents cmbDCI As C1.Win.C1List.C1Combo
    Friend WithEvents lDCi As System.Windows.Forms.Label
    Friend WithEvents bModifierCNAM As C1.Win.C1Input.C1Button
End Class
