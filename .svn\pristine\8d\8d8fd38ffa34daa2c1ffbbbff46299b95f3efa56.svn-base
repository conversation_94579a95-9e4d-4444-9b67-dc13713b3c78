﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Public Class fAffectationDesAutorisations
    Dim cmdPointControle As New SqlCommand
    Dim daPointControle As New SqlDataAdapter
    Dim dsPointControle As New DataSet
    Dim cbPointControle As New SqlCommandBuilder

    Dim CodeUtilisateur As String = ""
    Dim CodeModule As String = ""
    Dim CodePointControle As String = ""
    Dim Designation As String = ""

    Dim CodeModuleFiltre As String = ""
    Public lstText As String = ""
    Public DataRow As DataRow = Nothing

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "114" And bConfirmer.Enabled = True Then
            bConfirmer_Click(sender, e)
        End If

        If argument = "123" And bAnnuler.Enabled = True Then
            bAnnuler_Click(sender, e)
        End If

    End Sub

    Public Sub init()
        Dim StrSQL As String = ""

        'charger les Utilisateurs

        StrSQL = "SELECT CodeUtilisateur,Nom FROM UTILISATEUR WHERE supprime = 0 ORDER BY Nom ASC"

        cmdPointControle.Connection = ConnectionServeur
        cmdPointControle.CommandText = StrSQL
        daPointControle = New SqlDataAdapter(cmdPointControle)
        daPointControle.Fill(dsPointControle, "UTILISATEUR")
        'cmbUtilisateurs.DataSource = dsPointControle.Tables("UTILISATEUR")
        'cmbUtilisateurs.ValueMember = "CodeUtilisateur"
        'cmbUtilisateurs.DisplayMember = "Nom"
        'cmbUtilisateurs.ColumnHeaders = False
        'cmbUtilisateurs.Splits(0).DisplayColumns("CodeUtilisateur").Visible = False
        'cmbUtilisateurs.Splits(0).DisplayColumns("Nom").Width = 10
        'cmbUtilisateurs.ExtendRightColumn = True


        lstutilisateurs.DataSource = dsPointControle.Tables("UTILISATEUR")
        lstutilisateurs.DisplayMember = "Nom"
        lstutilisateurs.ValueMember = "CodeUtilisateur"

        'charger les Utilisateurs pour copier leurs autorisations

        StrSQL = "SELECT CodeUtilisateur,Nom FROM UTILISATEUR WHERE supprime = 0 ORDER BY Nom ASC"

        cmdPointControle.Connection = ConnectionServeur
        cmdPointControle.CommandText = StrSQL
        daPointControle = New SqlDataAdapter(cmdPointControle)
        daPointControle.Fill(dsPointControle, "UTILISATEUR_AUTORISATION")
        cmbUtilisateursAcopierLesAutorisations.DataSource = dsPointControle.Tables("UTILISATEUR_AUTORISATION")
        cmbUtilisateursAcopierLesAutorisations.ValueMember = "CodeUtilisateur"
        cmbUtilisateursAcopierLesAutorisations.DisplayMember = "Nom"
        cmbUtilisateursAcopierLesAutorisations.ColumnHeaders = False
        cmbUtilisateursAcopierLesAutorisations.Splits(0).DisplayColumns("CodeUtilisateur").Visible = False
        cmbUtilisateursAcopierLesAutorisations.Splits(0).DisplayColumns("Nom").Width = 10
        cmbUtilisateursAcopierLesAutorisations.ExtendRightColumn = True

        'charger les Module

        StrSQL = "SELECT CodeModule,Designation FROM MODULES_PHARMA ORDER BY Designation ASC"

        cmdPointControle.Connection = ConnectionServeur
        cmdPointControle.CommandText = StrSQL
        daPointControle = New SqlDataAdapter(cmdPointControle)
        daPointControle.Fill(dsPointControle, "MODULES_PHARMA")
        cmbModule.DataSource = dsPointControle.Tables("MODULES_PHARMA")
        cmbModule.ValueMember = "CodeModule"
        cmbModule.DisplayMember = "Designation"
        cmbModule.ColumnHeaders = False
        cmbModule.Splits(0).DisplayColumns("CodeModule").Visible = False
        cmbModule.Splits(0).DisplayColumns("Designation").Width = 10
        cmbModule.ExtendRightColumn = True

        'charger les profils

        StrSQL = "SELECT CodeProfil,NomProfil FROM PROFIL ORDER BY NomProfil ASC"

        cmdPointControle.Connection = ConnectionServeur
        cmdPointControle.CommandText = StrSQL
        daPointControle = New SqlDataAdapter(cmdPointControle)
        daPointControle.Fill(dsPointControle, "PROFIL")
        cmbProfil.DataSource = dsPointControle.Tables("PROFIL")
        cmbProfil.ValueMember = "CodeProfil"
        cmbProfil.DisplayMember = "NomProfil"
        cmbProfil.ColumnHeaders = False
        cmbProfil.Splits(0).DisplayColumns("CodeProfil").Visible = False
        cmbProfil.Splits(0).DisplayColumns("NomProfil").Width = 10
        cmbProfil.ExtendRightColumn = True

        AfficherPointControle()

    End Sub

    Public Sub AfficherPointControle()

        Dim I As Integer = 0
        Dim StrSQL As String = ""
        Dim Cond As String = " 1=1 AND "

        If (dsPointControle.Tables.IndexOf("POINT_CONTROLE_NON_ATTRIBUE") > -1) Then
            dsPointControle.Tables("POINT_CONTROLE_NON_ATTRIBUE").Clear()
            dsPointControle.Tables("POINT_CONTROLE_NON_ATTRIBUE").AcceptChanges()
        End If
        If (dsPointControle.Tables.IndexOf("AUTORISATION") > -1) Then
            dsPointControle.Tables("AUTORISATION").Clear()
            dsPointControle.Tables("AUTORISATION").AcceptChanges()
        End If
        If lstutilisateurs.SelectedIndices.Count = 0 Then
            lstText = ""
            Exit Sub
        End If

        'charger les Autorisastions qui ne sont pas attribuées à ce utilisateur dans la  1 ERE liste 

        If cmbModule.Text <> "" Then
            Cond += " POINTS_CONTROLE.CodeModule ='" + cmbModule.SelectedValue.ToString + "' AND"
        End If

        StrSQL = " SELECT CodeModule," + _
                     " CodePointControle," + _
                     " Designation " + _
                     " FROM POINTS_CONTROLE " + _
                     " WHERE " & Cond & " CodePointControle NOT IN (SELECT CodePointControle FROM AUTORISATION " + _
                     " WHERE CodeUtilisateur = "
        If lstText <> "" Then
            StrSQL += lstText.Split(",")(0).ToString + _
            ") ORDER BY POINTS_CONTROLE.Designation"
        Else
            StrSQL += "'') ORDER BY POINTS_CONTROLE.Designation"
        End If

        cmdPointControle.Connection = ConnectionServeur
        cmdPointControle.CommandText = StrSQL
        daPointControle = New SqlDataAdapter(cmdPointControle)
        daPointControle.Fill(dsPointControle, "POINT_CONTROLE_NON_ATTRIBUE")

        With gPointsDeControleNonAttribue
            .Columns.Clear()
            .DataSource = dsPointControle
            .DataMember = "POINT_CONTROLE_NON_ATTRIBUE"
            .Rebind(False)
            .Columns("Designation").Caption = "Points de contrôle"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Designation").Width = 200
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("CodeModule").Visible = False
            .Splits(0).DisplayColumns("CodePointControle").Visible = False
            .Splits(0).DisplayColumns("Designation").HeadingStyle.BackColor = System.Drawing.Color.FromArgb(238, 0, 0)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .Style.Font = New Font("Microsoft Sans Serif", 8, FontStyle.Regular)

            'Dim f As Font = New Font("Microsoft Sans Serif", 8, FontStyle.Regular)
            '.ChartArea.AxisX.Font = f
            'Style du Caractere et du grid
            ParametreGrid(gPointsDeControleNonAttribue)
        End With

        '******************************************************************************************
        '******************************************************************************************

        'charger les AUTORISATIONS attribuées à cet utilisateur dans la 2 eme liste 

        cmdPointControle.CommandText = " SELECT " + _
                                " CodeUtilisateur," + _
                                " AUTORISATION.CodePointControle," + _
                                " Designation, " + _
                                " CodeModule " + _
                                " FROM AUTORISATION LEFT OUTER JOIN POINTS_CONTROLE " + _
                                " ON AUTORISATION.CodePointControle=POINTS_CONTROLE.CodePointControle " + _
                                " WHERE " + _
                                " AUTORISATION.CodeUtilisateur ="

        If lstText <> "" Then
            cmdPointControle.CommandText += lstText.Split(",")(0).ToString + _
            " ORDER BY Designation"
        Else
            cmdPointControle.CommandText += "'' ORDER BY Designation"
        End If

        cmdPointControle.Connection = ConnectionServeur
        daPointControle = New SqlDataAdapter(cmdPointControle)
        daPointControle.Fill(dsPointControle, "AUTORISATION")

        With gPointsDeControleAttribue
            .Columns.Clear()
            .DataSource = dsPointControle
            .DataMember = "AUTORISATION"
            .Rebind(False)
            .Columns("Designation").Caption = "Points de contrôle"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Designation").Width = 200
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("CodeUtilisateur").Visible = False
            .Splits(0).DisplayColumns("CodePointControle").Visible = False
            .Splits(0).DisplayColumns("CodeModule").Visible = False
            .Splits(0).DisplayColumns("Designation").HeadingStyle.BackColor = System.Drawing.Color.FromArgb(100, 210, 100)
            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .Style.Font = New Font("Microsoft Sans Serif", 8, FontStyle.Regular)
            'Style du Caractere et du grid
            ParametreGrid(gPointsDeControleAttribue)
        End With

        'gPointsDeControleAttribue = False

    End Sub

    Private Sub bAjoutertous_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjoutertous.Click

        Dim i As Integer = 0
        For i = 0 To gPointsDeControleNonAttribue.RowCount - 1

            If gPointsDeControleNonAttribue(i, "CodePointControle").ToString <> "" Then

                CodePointControle = gPointsDeControleNonAttribue(i, "CodePointControle")
                CodeModule = gPointsDeControleNonAttribue(i, "CodeModule")
                Designation = gPointsDeControleNonAttribue(i, "Designation")

                DataRow = dsPointControle.Tables("AUTORISATION").NewRow()
                DataRow("CodeUtilisateur") = lstText
                DataRow("CodePointControle") = CodePointControle
                DataRow("CodeModule") = CodeModule
                DataRow("Designation") = Designation

                dsPointControle.Tables("AUTORISATION").Rows.Add(DataRow)

            End If
        Next

        For i = 0 To gPointsDeControleNonAttribue.RowCount - 1
            gPointsDeControleNonAttribue.Delete()
        Next
    End Sub

    Private Sub bAjouterUn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterUn.Click

        Dim i As Integer = 0

        If gPointsDeControleNonAttribue(gPointsDeControleNonAttribue.Row, "CodePointControle").ToString = "" Then
            Exit Sub
        End If

        CodePointControle = gPointsDeControleNonAttribue(gPointsDeControleNonAttribue.Row, "CodePointControle")
        CodeModule = gPointsDeControleNonAttribue(gPointsDeControleNonAttribue.Row, "CodeModule")
        Designation = gPointsDeControleNonAttribue(gPointsDeControleNonAttribue.Row, "Designation")

        DataRow = dsPointControle.Tables("AUTORISATION").NewRow()
        DataRow("CodeUtilisateur") = lstText
        DataRow("CodePointControle") = CodePointControle
        DataRow("CodeModule") = CodeModule
        DataRow("Designation") = Designation

        dsPointControle.Tables("AUTORISATION").Rows.Add(DataRow)

        For i = 0 To gPointsDeControleNonAttribue.RowCount - 1
            If gPointsDeControleNonAttribue(gPointsDeControleNonAttribue.Row, "CodePointControle") = CodePointControle Then
                gPointsDeControleNonAttribue.Delete()
            End If
        Next
    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        Dim StrSQL As String = ""
        Dim StrSQLtest As String = ""
        Dim i As Integer = 0

        If lstText = "" Then
            MsgBox("Veuillez sélectionner un Utilisateur !", MsgBoxStyle.Critical, "Erreur")
            lstutilisateurs.Focus()
            Exit Sub
        End If
        If dsPointControle.Tables("AUTORISATION").Rows.Count <= 0 Then
            MsgBox("La liste de points de contrôle à affecter est vide !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        Try
            cmdPointControle.CommandText = "Delete from AUTORISATION WHERE CodeUtilisateur IN (" & lstText & ")"
            cmdPointControle.Connection = ConnectionServeur
            cmdPointControle.ExecuteNonQuery()
            For Each _str As String In lstText.Split(",") 'Pour chaque client selectionné
                For Each drow As DataRow In dsPointControle.Tables("AUTORISATION").Rows
                    cmdPointControle.CommandText = "INSERT INTO AUTORISATION VALUES (" & Quote(drow(1).ToString) & "," & _str & ")"
                    cmdPointControle.Connection = ConnectionServeur
                    cmdPointControle.ExecuteNonQuery()
                Next
            Next

            cmbModule.Text = ""
            AfficherPointControle()
            MsgBox("Modification terminée avec succés !", MsgBoxStyle.Information, "Information")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            'dsPointControle.Reset()
        End Try
        cmbUtilisateursAcopierLesAutorisations.SelectedIndex = -1
        cmbProfil.SelectedIndex = -1
    End Sub

    Private Sub bSupprimerUn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerUn.Click

        Dim i As Integer = 0

        If gPointsDeControleAttribue(gPointsDeControleAttribue.Row, "CodePointControle").ToString = "" Then
            Exit Sub
        End If

        If gPointsDeControleAttribue.Row >= 0 Then
            CodeModule = gPointsDeControleAttribue(gPointsDeControleAttribue.Row, "CodeModule")
            CodePointControle = gPointsDeControleAttribue(gPointsDeControleAttribue.Row, "CodePointControle")
            Designation = gPointsDeControleAttribue(gPointsDeControleAttribue.Row, "Designation")

            DataRow = dsPointControle.Tables("POINT_CONTROLE_NON_ATTRIBUE").NewRow()
            DataRow("CodeModule") = CodeModule
            DataRow("CodePointControle") = CodePointControle
            DataRow("Designation") = Designation

            dsPointControle.Tables("POINT_CONTROLE_NON_ATTRIBUE").Rows.Add(DataRow)

            dsPointControle.Tables("AUTORISATION").Rows.RemoveAt(gPointsDeControleAttribue.Row)
        End If
    End Sub

    Private Sub bSupprimerTous_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerTous.Click
        Dim CodeModule As String = ""
        Dim CodePointDeControle As String = ""
        Dim Designation As String = ""
        Dim i As Integer = 0

        If gPointsDeControleAttribue.RowCount > 0 Then
            For i = 0 To gPointsDeControleAttribue.RowCount - 1

                If gPointsDeControleAttribue(i, "CodePointControle").ToString <> "" Then

                    CodeModule = gPointsDeControleAttribue(i, "CodeModule")
                    CodePointDeControle = gPointsDeControleAttribue(i, "CodePointControle")
                    Designation = gPointsDeControleAttribue(i, "Designation")

                    DataRow = dsPointControle.Tables("POINT_CONTROLE_NON_ATTRIBUE").NewRow()
                    DataRow("CodeModule") = CodeModule
                    DataRow("CodePointControle") = CodePointDeControle
                    DataRow("Designation") = Designation

                    dsPointControle.Tables("POINT_CONTROLE_NON_ATTRIBUE").Rows.Add(DataRow)

                End If
            Next

            dsPointControle.Tables("AUTORISATION").Rows.Clear()
        End If
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub cmbModule_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbModule.KeyUp
        Dim i As Integer = 0

        If e.KeyCode = Keys.Enter Then
            cmbModule.Text = cmbModule.WillChangeToText
            If lstText.Split(",").Length = 1 Then
                AfficherPointControle()
            End If
        Else
            cmbModule.OpenCombo()
        End If
    End Sub

    Private Sub cmbModule_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbModule.TextChanged
        If lstText.Split(",").Length = 1 Then
            AfficherPointControle()
        End If
    End Sub

    Private Sub bAffecterDepuisProfil_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAffecterDepuisProfil.Click
        Dim I As Integer = 0

        If lstText = "" Then
            MsgBox("Veuillez sélectionner un Utilisateur !", MsgBoxStyle.Critical, "Erreur")
            lstutilisateurs.Focus()
            Exit Sub
        End If

        If cmbProfil.Text = "" Then
            MsgBox("Veuillez sélectionner un profil !", MsgBoxStyle.Critical, "Erreur")
            cmbProfil.Focus()
            Exit Sub
        End If

        If (dsPointControle.Tables.IndexOf("AUTORISATION") > -1) Then
            dsPointControle.Tables("AUTORISATION").Clear()
        End If
        If (dsPointControle.Tables.IndexOf("PROFIL_DETAILS") > -1) Then
            dsPointControle.Tables("PROFIL_DETAILS").Clear()
        End If
        If (dsPointControle.Tables.IndexOf("POINT_CONTROLE_NON_ATTRIBUE") > -1) Then
            dsPointControle.Tables("POINT_CONTROLE_NON_ATTRIBUE").Clear()
        End If

        cmdPointControle.CommandText = " SELECT CodeProfil," + _
                                       " PROFIL_DETAILS.CodePointControle," + _
                                       " CodeModule, " + _
                                       " Designation " + _
                                       " FROM PROFIL_DETAILS LEFT OUTER JOIN POINTS_CONTROLE " + _
                                       " ON PROFIL_DETAILS.CodePointControle=POINTS_CONTROLE.CodePointControle" + _
                                       " WHERE CodeProfil=" + cmbProfil.SelectedValue.ToString

        cmdPointControle.Connection = ConnectionServeur
        daPointControle = New SqlDataAdapter(cmdPointControle)
        daPointControle.Fill(dsPointControle, "PROFIL_DETAILS")
        '##
        Dim StrSQL As String = ""
        Dim Cond As String = " 1=1 AND "
        If cmbModule.Text <> "" Then
            Cond += " POINTS_CONTROLE.CodeModule ='" + cmbModule.SelectedValue.ToString + "' AND"
        End If

        StrSQL = " SELECT CodeModule," + _
                     " CodePointControle," + _
                     " Designation " + _
                     " FROM POINTS_CONTROLE " + _
                     " WHERE CodePointControle NOT IN (SELECT CodePointControle FROM AUTORISATION " + _
                     " WHERE " & Cond & " CodeUtilisateur = "
        If lstText <> "" Then
            StrSQL += lstText.Split(",")(0).ToString + _
            ") ORDER BY POINTS_CONTROLE.Designation"
        Else
            StrSQL += "'') ORDER BY POINTS_CONTROLE.Designation"
        End If

        cmdPointControle.Connection = ConnectionServeur
        cmdPointControle.CommandText = StrSQL
        daPointControle = New SqlDataAdapter(cmdPointControle)
        daPointControle.Fill(dsPointControle, "POINT_CONTROLE_NON_ATTRIBUE")
        '##


        I = 0
        Do While I < dsPointControle.Tables("PROFIL_DETAILS").Rows.Count

            DataRow = dsPointControle.Tables("AUTORISATION").NewRow()

            DataRow("CodeUtilisateur") = lstText
            DataRow("CodePointControle") = dsPointControle.Tables("PROFIL_DETAILS").Rows(I).Item("CodePointControle")
            DataRow("CodeModule") = dsPointControle.Tables("PROFIL_DETAILS").Rows(I).Item("CodeModule")
            DataRow("Designation") = dsPointControle.Tables("PROFIL_DETAILS").Rows(I).Item("Designation")

            dsPointControle.Tables("AUTORISATION").Rows.Add(DataRow)
            I += 1
        Loop
        cmbUtilisateursAcopierLesAutorisations.SelectedIndex = -1
    End Sub

    Private Sub bAffecterDepuisUtilisateur_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAffecterDepuisUtilisateur.Click
        Dim I As Integer = 0

        If lstText = "" Then
            MsgBox("Veuillez sélectionner un Utilisateur !", MsgBoxStyle.Critical, "Erreur")
            lstutilisateurs.Focus()
            Exit Sub
        End If

        If cmbUtilisateursAcopierLesAutorisations.Text = "" Then
            MsgBox("Veuillez sélectionner un Utilisateur  !", MsgBoxStyle.Critical, "Erreur")
            cmbUtilisateursAcopierLesAutorisations.Focus()
            Exit Sub
        End If

        If (dsPointControle.Tables.IndexOf("AUTORISATION") > -1) Then
            dsPointControle.Tables("AUTORISATION").Clear()
        End If
        If (dsPointControle.Tables.IndexOf("PROFIL_UTILISATEUR") > -1) Then
            dsPointControle.Tables("PROFIL_UTILISATEUR").Clear()
        End If
        If (dsPointControle.Tables.IndexOf("POINT_CONTROLE_NON_ATTRIBUE") > -1) Then
            dsPointControle.Tables("POINT_CONTROLE_NON_ATTRIBUE").Clear()
        End If

        cmdPointControle.CommandText = " SELECT AUTORISATION.CodePointControle," + _
                                       " CodeModule, " + _
                                       " Designation " + _
                                       " FROM AUTORISATION LEFT OUTER JOIN POINTS_CONTROLE " + _
                                       " ON AUTORISATION.CodePointControle=POINTS_CONTROLE.CodePointControle" + _
                                       " WHERE CodeUtilisateur=" + cmbUtilisateursAcopierLesAutorisations.SelectedValue.ToString

        cmdPointControle.Connection = ConnectionServeur
        daPointControle = New SqlDataAdapter(cmdPointControle)
        daPointControle.Fill(dsPointControle, "PROFIL_UTILISATEUR")

        '##
        Dim StrSQL As String = ""
        Dim Cond As String = " 1=1 AND "
        If cmbModule.Text <> "" Then
            Cond += " POINTS_CONTROLE.CodeModule ='" + cmbModule.SelectedValue.ToString + "' AND"
        End If

        StrSQL = " SELECT CodeModule," + _
                     " CodePointControle," + _
                     " Designation " + _
                     " FROM POINTS_CONTROLE " + _
                     " WHERE CodePointControle NOT IN (SELECT CodePointControle FROM AUTORISATION " + _
                     " WHERE " & Cond & " CodeUtilisateur = "
        If lstText <> "" Then
            StrSQL += lstText.Split(",")(0).ToString + _
            ") ORDER BY POINTS_CONTROLE.Designation"
        Else
            StrSQL += "'') ORDER BY POINTS_CONTROLE.Designation"
        End If

        cmdPointControle.Connection = ConnectionServeur
        cmdPointControle.CommandText = StrSQL
        daPointControle = New SqlDataAdapter(cmdPointControle)
        daPointControle.Fill(dsPointControle, "POINT_CONTROLE_NON_ATTRIBUE")
        '##

        I = 0
        Do While I < dsPointControle.Tables("PROFIL_UTILISATEUR").Rows.Count

            DataRow = dsPointControle.Tables("AUTORISATION").NewRow()

            DataRow("CodeUtilisateur") = lstText
            DataRow("CodePointControle") = dsPointControle.Tables("PROFIL_UTILISATEUR").Rows(I).Item("CodePointControle")
            DataRow("CodeModule") = dsPointControle.Tables("PROFIL_UTILISATEUR").Rows(I).Item("CodeModule")
            DataRow("Designation") = dsPointControle.Tables("PROFIL_UTILISATEUR").Rows(I).Item("Designation")

            dsPointControle.Tables("AUTORISATION").Rows.Add(DataRow)
            I += 1
        Loop
        cmbProfil.SelectedIndex = -1
    End Sub

    Private Sub lstutilisateurs_DoubleClick(sender As Object, e As System.EventArgs) Handles lstutilisateurs.DoubleClick
        lstText = ""
        For Each drvw As DataRowView In lstutilisateurs.CheckedItems
            lstText += IIf(lstText = "", "'", ",'") & drvw(0).ToString & "'"
        Next
        If (dsPointControle.Tables.IndexOf("AUTORISATION") > -1) Then
            dsPointControle.Tables("AUTORISATION").Clear()
            dsPointControle.Tables("AUTORISATION").AcceptChanges()
        End If
        If (dsPointControle.Tables.IndexOf("POINT_CONTROLE_NON_ATTRIBUE") > -1) Then
            dsPointControle.Tables("POINT_CONTROLE_NON_ATTRIBUE").Clear()
            dsPointControle.Tables("POINT_CONTROLE_NON_ATTRIBUE").AcceptChanges()
        End If
        lblitemselected.Text = "(" & lstText.Split(",").Length & ") élément(s)"
        lblitemselected.Visible = IIf(lstText.Split(",")(0).ToString <> "", True, False)
        If lstText.Split(",").Length = 1 Then
            AfficherPointControle()
        End If
    End Sub

    Private Sub lstutilisateurs_SelectedIndexChanged(sender As System.Object, e As System.EventArgs) Handles lstutilisateurs.SelectedIndexChanged
        lstText = ""
        For Each drvw As DataRowView In lstutilisateurs.CheckedItems
            lstText += IIf(lstText = "", "'", ",'") & drvw(0).ToString & "'"
        Next
        If (dsPointControle.Tables.IndexOf("AUTORISATION") > -1) Then
            dsPointControle.Tables("AUTORISATION").Clear()
            dsPointControle.Tables("AUTORISATION").AcceptChanges()
        End If
        If (dsPointControle.Tables.IndexOf("POINT_CONTROLE_NON_ATTRIBUE") > -1) Then
            dsPointControle.Tables("POINT_CONTROLE_NON_ATTRIBUE").Clear()
            dsPointControle.Tables("POINT_CONTROLE_NON_ATTRIBUE").AcceptChanges()
        End If
        lblitemselected.Text = "(" & lstText.Split(",").Length & ") élément(s)"
        lblitemselected.Visible = IIf(lstText.Split(",")(0).ToString <> "", True, False)
        If lstText.Split(",").Length = 1 Then
            AfficherPointControle()
        End If

    End Sub
End Class