using System.Windows;

namespace PharmaModerne.UI
{
    public partial class SimpleTestWindow : Window
    {
        public SimpleTestWindow()
        {
            InitializeComponent();

            // Forcer l'affichage au premier plan
            this.WindowState = WindowState.Normal;
            this.Show();
            this.Activate();
            this.Focus();
            this.BringIntoView();

            // Forcer au premier plan
            this.Topmost = true;
            System.Threading.Thread.Sleep(100);
            this.Topmost = false;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
