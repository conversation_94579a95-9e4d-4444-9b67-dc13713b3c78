//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class COMMANDE_INSTANCE
    {
        public COMMANDE_INSTANCE()
        {
            this.COMMANDE_INSTANCE_DETAILS = new HashSet<COMMANDE_INSTANCE_DETAILS>();
        }
    
        public string NumeroCommandeInstance { get; set; }
        public System.DateTime Date { get; set; }
        public decimal TotalHT { get; set; }
        public decimal TotalTTC { get; set; }
        public decimal TotalTVA { get; set; }
        public string LibellePoste { get; set; }
        public string CodeOperateur { get; set; }
        public string CodeFournisseur { get; set; }
        public string Note { get; set; }
        public string NomCommandeInstance { get; set; }
        public string TypeCommande { get; set; }
    
        public virtual ICollection<COMMANDE_INSTANCE_DETAILS> COMMANDE_INSTANCE_DETAILS { get; set; }
        public virtual FOURNISSEUR FOURNISSEUR { get; set; }
        public virtual UTILISATEUR UTILISATEUR { get; set; }
    }
}
