﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fEquivalentOuVenteEnNegative
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.Label1 = New System.Windows.Forms.Label
        Me.lNomArticle = New System.Windows.Forms.Label
        Me.bEquivalents = New C1.Win.C1Input.C1Button
        Me.bRetour = New C1.Win.C1Input.C1Button
        Me.Panel = New System.Windows.Forms.Panel
        Me.Panel.SuspendLayout()
        Me.SuspendLayout()
        '
        'Label1
        '
        Me.Label1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(3, 31)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(211, 17)
        Me.Label1.TabIndex = 74
        Me.Label1.Text = "n'est pas disponible actuellement !"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lNomArticle
        '
        Me.lNomArticle.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lNomArticle.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNomArticle.Location = New System.Drawing.Point(4, 7)
        Me.lNomArticle.Name = "lNomArticle"
        Me.lNomArticle.Size = New System.Drawing.Size(214, 15)
        Me.lNomArticle.TabIndex = 73
        Me.lNomArticle.Text = "Nom Inst"
        Me.lNomArticle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bEquivalents
        '
        Me.bEquivalents.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bEquivalents.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bEquivalents.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bEquivalents.Location = New System.Drawing.Point(114, 75)
        Me.bEquivalents.Name = "bEquivalents"
        Me.bEquivalents.Size = New System.Drawing.Size(100, 45)
        Me.bEquivalents.TabIndex = 72
        Me.bEquivalents.Text = "Médicament équivalent   F11"
        Me.bEquivalents.UseVisualStyleBackColor = True
        Me.bEquivalents.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bRetour
        '
        Me.bRetour.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bRetour.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bRetour.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bRetour.Location = New System.Drawing.Point(4, 75)
        Me.bRetour.Name = "bRetour"
        Me.bRetour.Size = New System.Drawing.Size(100, 45)
        Me.bRetour.TabIndex = 71
        Me.bRetour.Text = "Retour à la vente   F12"
        Me.bRetour.UseVisualStyleBackColor = True
        Me.bRetour.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.lNomArticle)
        Me.Panel.Controls.Add(Me.Label1)
        Me.Panel.Controls.Add(Me.bRetour)
        Me.Panel.Controls.Add(Me.bEquivalents)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(222, 133)
        Me.Panel.TabIndex = 75
        '
        'fEquivalentOuVenteEnNegative
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(222, 133)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fEquivalentOuVenteEnNegative"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents lNomArticle As System.Windows.Forms.Label
    Friend WithEvents bEquivalents As C1.Win.C1Input.C1Button
    Friend WithEvents bRetour As C1.Win.C1Input.C1Button
    Friend WithEvents Panel As System.Windows.Forms.Panel
End Class
