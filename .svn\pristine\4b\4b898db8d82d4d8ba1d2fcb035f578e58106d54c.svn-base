﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Linq.Dynamic
Imports Microsoft.Reporting.WinForms

Public Class fEtatJournalReleveMutuelle
    Dim _SalesReportService As New Bll.Reporting.SalesReport
    Dim _VOrderBy As String = "Id"
    Dim _VAscDesc As String = "Asc"

    Dim StrSQL As String = ""

    Dim cmdMutuelle As New SqlCommand
    Dim daMutuelle As New SqlDataAdapter
    Dim dsMutuelle As New DataSet

    Dim cmdListeMutuelle As New SqlCommand
    Dim daListeMutuelle As New SqlDataAdapter
    Dim dsListeMutuelle As New DataSet

    Dim MontantTotal As Double = 0
    Dim MontantRegle As Double = 0
    Dim Reste As Double = 0
    Dim CondCrystalReport As String = ""

    Public Initialisation As Boolean = False


    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub Init()

        'charger les mutuelles
        cmdListeMutuelle.CommandText = "SELECT CodeMutuelle,NomMutuelle FROM MUTUELLE WHERE NomMutuelle <>'COMPTOIR' ORDER BY NomMutuelle ASC"
        cmdListeMutuelle.Connection = ConnectionServeur
        daListeMutuelle = New SqlDataAdapter(cmdListeMutuelle)
        daListeMutuelle.Fill(dsListeMutuelle, "NATURE_ENTREE")
        cmbMutuelle.DataSource = dsListeMutuelle.Tables("NATURE_ENTREE")
        cmbMutuelle.ValueMember = "CodeMutuelle"
        cmbMutuelle.DisplayMember = "NomMutuelle"
        cmbMutuelle.ColumnHeaders = False
        cmbMutuelle.ExtendRightColumn = True
        cmbMutuelle.Splits(0).DisplayColumns("CodeMutuelle").Visible = False
        cmbMutuelle.Splits(0).DisplayColumns("NomMutuelle").Width = 10
        cmbMutuelle.ExtendRightColumn = True

        Initialisation = True

        If ModeADMIN = "ADMIN" Then
            Label4.Visible = True
            Label6.Visible = True
            Label5.Visible = True
            tMontantRegle.Visible = True
            tMontantTotal.Visible = True
            tReste.Visible = True
        Else
            Label4.Visible = False
            Label6.Visible = False
            Label5.Visible = False
            tMontantRegle.Visible = False
            tMontantTotal.Visible = False
            tReste.Visible = False
        End If

        AfficherJournalMutuelle()

        dtpDebut.Text = "01/" + Date.Today.Month.ToString.PadLeft(2, "0") + "/" + Date.Now.Year.ToString
        dtpFin.Text = Today
        dtpDebut.Focus()
    End Sub
    Public Sub AfficherJournalMutuelle()

        If dtpDebut.Text = "" Then
            dtpDebut.Value = Date.Now
        End If
        If dtpFin.Text = "" Then
            dtpFin.Value = Date.Now
        End If

        Dim List As New Library.SortableBindingList(Of Data.Reporting.P_Report_EtatJournalReleveMutuelle_Result)(_SalesReportService.GetEtatJournalReleveMutuelle(IIf(IsNothing(cmbMutuelle.SelectedValue), "", cmbMutuelle.SelectedValue), _
                                                                                                                                                                    dtpDebut.Value, _
                                                                                                                                                                    dtpFin.Value))


        With gMutuelle
            .Columns.Clear()
            .DataSource = List
            .Rebind(False)
            .Columns("Date").Caption = "Date"
            .Columns("DateDebut").Caption = "Du"
            .Columns("DateFin").Caption = "Au"
            .Columns("NumeroReleve").Caption = "Numéro"
            .Columns("MontantTotal").Caption = "Montant total"
            .Columns("MontantRegle").Caption = "Montant réglé"
            .Columns("Reste").Caption = "Reste"
            .Columns("Date").NumberFormat = "dd/MM/yyyy"
            .Columns("DateDebut").NumberFormat = "dd/MM/yyyy"
            .Columns("DateFin").NumberFormat = "dd/MM/yyyy"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Date").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Date").Width = 100
            .Splits(0).DisplayColumns("DateDebut").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("DateDebut").Width = 100
            .Splits(0).DisplayColumns("DateFin").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("DateFin").Width = 100
            .Splits(0).DisplayColumns("NumeroReleve").Width = 150
            .Splits(0).DisplayColumns("NumeroReleve").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("MontantTotal").Width = 200
            .Splits(0).DisplayColumns("MontantRegle").Width = 200

            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gMutuelle)
        End With

        CalculValeur()

    End Sub

    Private Sub gMutuelle_AfterSort(sender As Object, e As FilterEventArgs) Handles gMutuelle.AfterSort
        Try
            _VOrderBy = e.Condition.Substring(0, e.Condition.IndexOf(" "))
            _VAscDesc = e.Condition.Substring(e.Condition.IndexOf(" ") + 1, e.Condition.Length - e.Condition.IndexOf(" ") - 1)
        Catch ex As Exception
            _VOrderBy = e.Condition
            _VAscDesc = "Asc"
        End Try
    End Sub

    Private Sub gCNAM_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gMutuelle.FetchRowStyle
        'e.CellStyle.Font = New System.Drawing.Font("Calibri", 9, FontStyle.Regular)
    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter And dtpDebut.Text <> "" Then
            AfficherJournalMutuelle()
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDebut.TextChanged

    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter And dtpFin.Text <> "" Then
            gMutuelle.Focus()
            AfficherJournalMutuelle()
            CalculValeur()
        End If
    End Sub

    Private Sub dtpFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpFin.TextChanged

    End Sub

    Private Sub CalculValeur()
        MontantRegle = 0.0
        MontantTotal = 0.0
        Reste = 0.0
        If gMutuelle.RowCount <> 0 Then
            For I As Integer = 0 To gMutuelle.RowCount - 1
                MontantTotal += gMutuelle(I, "MontantTotal")
                MontantRegle += gMutuelle(I, "MontantRegle")
                Reste += gMutuelle(I, "Reste")
            Next
            tMontantRegle.Text = MontantRegle.ToString("### ### ##0.000") 'Format(MontantRegle, "0.000")
            tMontantTotal.Text = MontantTotal.ToString("### ### ##0.000") 'Format(MontantTotal, "0.000")
            tReste.Text = Reste.ToString("### ### ##0.000") 'Format(Reste, "0.000")
        End If
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Reporting Ventes" Then
                num = I
            End If
        Next
        Dim dt

        Dim _Parameters As New List(Of ReportParameter)()

        Dim _CodeMutuelle As New ReportParameter()
        _CodeMutuelle.Name = "CodeMutuelle"
        _CodeMutuelle.Values.Add(cmbMutuelle.SelectedValue)
        _Parameters.Add(_CodeMutuelle)

        Dim _DateDebut As New ReportParameter()
        _DateDebut.Name = "DateDebut"
        _DateDebut.Values.Add(dtpDebut.Value)
        _Parameters.Add(_DateDebut)

        Dim _DateFin As New ReportParameter()
        _DateFin.Name = "DateFin"
        _DateFin.Values.Add(dtpFin.Value)
        _Parameters.Add(_DateFin)

        Dim MyViewer As New fImpressionReportingVente

        dt = _SalesReportService.GetEtatJournalReleveMutuelle(IIf(IsNothing(cmbMutuelle.SelectedValue), "", cmbMutuelle.SelectedValue), _
                                                                dtpDebut.Value, _
                                                                dtpFin.Value).OrderBy(_VOrderBy + " " + _VAscDesc)

        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_EtatJournalReleveMutuelle", dt))
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Pharmacie", _SalesReportService.GetPharmacie()))
        MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatJournalReleveMutuelle.rdl"

        MyViewer.ReportViewer1.LocalReport.SetParameters(_Parameters)

        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel1)

        MyViewer.ReportViewer1.RefreshReport()

        fMain.Tab.SelectedTab.Text = "Impression Reporting Ventes"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub fEtatJournalReleveMutuelle_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

    End Sub

    Private Sub cmbMutuelle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMutuelle.KeyUp
        If e.KeyCode = Keys.Enter Then
            gMutuelle.Focus()
            AfficherJournalMutuelle()
        End If
    End Sub

    Private Sub cmbMutuelle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbMutuelle.TextChanged

    End Sub
End Class