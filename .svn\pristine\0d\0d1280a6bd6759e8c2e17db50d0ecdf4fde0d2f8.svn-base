//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class RELEVE_CNAM_DETAILS
    {
        public RELEVE_CNAM_DETAILS()
        {
            this.REGLEMENT_CNAM_VENTE = new HashSet<REGLEMENT_CNAM_VENTE>();
        }
    
        public string NumeroReleve { get; set; }
        public string NumeroVente { get; set; }
        public System.DateTime Date { get; set; }
        public string CodeClient { get; set; }
        public System.DateTime DateOrdonnance { get; set; }
        public int CodeAPCI { get; set; }
        public decimal TotalTTC { get; set; }
        public decimal Regle { get; set; }
        public decimal MontantCnam { get; set; }
        public string CodeMedecin { get; set; }
        public Nullable<int> NumeroLigne { get; set; }
    
        public virtual ICollection<REGLEMENT_CNAM_VENTE> REGLEMENT_CNAM_VENTE { get; set; }
        public virtual RELEVE_CNAM RELEVE_CNAM { get; set; }
        public virtual VENTE VENTE { get; set; }
    }
}
