﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Public Class fParametresPoste

    Dim cmdParametres As New SqlCommand
    Dim daParametres As New SqlDataAdapter
    Dim cbParametres As New SqlCommandBuilder
    Dim dsParametres As New DataSet

    Dim StrSQL As String = ""

    Public Sub afficherParametres()

        If (dsParametres.Tables.IndexOf("PARAMETRES") > -1) Then
            dsParametres.Tables("PARAMETRES").Clear()
        End If

        cmdParametres.CommandText = " SELECT * FROM PARAMETRES WHERE POSTE='" + cmbPoste.Text + "'" 'Environment.MachineName.ToString + "'"

        cmdParametres.Connection = ConnectionServeur
        daParametres = New SqlDataAdapter(cmdParametres)
        daParametres.Fill(dsParametres, "PARAMETRES")

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("LecteurUpdate").ToString <> "" Then
            tLecteurUpdate.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("LecteurUpdate")
        Else
            tLecteurUpdate.Value = ""
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("LecteurSauvegarde").ToString <> "" Then
            tLecteurSauvegarde.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("LecteurSauvegarde")
        Else
            tLecteurSauvegarde.Value = ""
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("DelaiSauvegarde").ToString <> "" Then
            tDelaiSauvegarde.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("DelaiSauvegarde")
        Else
            tDelaiSauvegarde.Value = 0
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("AutoriserLesChangementsAutomatiquesDesPrixPreparation").ToString <> "" Then
            chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("AutoriserLesChangementsAutomatiquesDesPrixPreparation")
        Else
            chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.Checked = False
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("ImprimanteATicket").ToString <> "" Then
            chbImprimanteATicket.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("ImprimanteATicket")
        Else
            chbImprimanteATicket.Checked = False
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("Tiroir").ToString <> "" Then
            chbTiroir.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("Tiroir")
        Else
            chbTiroir.Checked = False
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("USB").ToString <> "" Then
            chbUSB.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("USB")
        Else
            chbUSB.Checked = False
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("COM").ToString <> "" Then
            tCom.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("COM")
        Else
            tCom.Value = ""
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("ImprimerLesBons").ToString <> "" Then
            chbImprimerBon.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("ImprimerLesBons")
        Else
            chbImprimerBon.Checked = False
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("ValiderQteEgalA1").ToString <> "" Then
            chbValiderQteEgalA1.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("ValiderQteEgalA1")
        Else
            chbValiderQteEgalA1.Checked = False
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("ControlerLeNombredesUnitesvendues").ToString <> "" Then
            chbControleNombreUnites.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("ControlerLeNombredesUnitesvendues")
        Else
            chbControleNombreUnites.Checked = False
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("NombreDesTentativesPourControlerLeNombreDesArticles").ToString <> "" Then
            tTentatives.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("NombreDesTentativesPourControlerLeNombreDesArticles")
        Else
            tTentatives.Value = 1
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("InscriptionAutomatiqueSurOrdonnacier").ToString <> "" Then
            chbInscriptionSurOrdonnancierAutomatique.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("InscriptionAutomatiqueSurOrdonnacier")
        Else
            chbInscriptionSurOrdonnancierAutomatique.Checked = False
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("InscriptionAutomatiqueSurOrdonnacierTableauB").ToString <> "" Then
            chbTableauB.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("InscriptionAutomatiqueSurOrdonnacierTableauB")
        Else
            chbTableauB.Checked = False
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("InterdirelaVenteDesProduitsPerimes").ToString <> "" Then
            chbInterdireLaVenteDesPerimes.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("InterdirelaVenteDesProduitsPerimes")
        Else
            chbInterdireLaVenteDesPerimes.Checked = False
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("InterdireDeChoisirLesArticlesParDesignation").ToString <> "" Then
            chbInterdireChoisirParDesignation.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("InterdireDeChoisirLesArticlesParDesignation")
        Else
            chbInterdireChoisirParDesignation.Checked = False
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("NomDeLImprimante").ToString <> "" Then
            tNomDeLImprimante.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("NomDeLImprimante")
        Else
            tNomDeLImprimante.Value = ""
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("AffichefVenteDetailImage").ToString <> "" Then
            chbScanner.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("AffichefVenteDetailImage")
        Else
            chbScanner.Checked = False
        End If


        If dsParametres.Tables("PARAMETRES").Rows(0).Item("AfficheurClient").ToString <> "" Then
            chbAfficheurClient.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("AfficheurClient")
        Else
            chbAfficheurClient.Checked = False
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("COMAfficheurClient").ToString <> "" Then
            tComAfficheurClient.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("COMAfficheurClient")
        Else
            tComAfficheurClient.Value = ""
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("LecteurSauvegardeDistant").ToString <> "" Then
            tLecteurSauvegardeDistant.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("LecteurSauvegardeDistant")
        Else
            tLecteurSauvegardeDistant.Value = ""
        End If

        cbParametres = New SqlCommandBuilder(daParametres)

        Tab.TabPages(0).Show()

    End Sub

    Public Sub init()

        'chargement des postes
        StrSQL = "SELECT DISTINCT LibellePoste FROM POSTE ORDER BY LibellePoste ASC"
        cmdParametres.Connection = ConnectionServeur
        cmdParametres.CommandText = StrSQL
        daParametres = New SqlDataAdapter(cmdParametres)
        daParametres.Fill(dsParametres, "POSTE")
        cmbPoste.DataSource = dsParametres.Tables("POSTE")
        cmbPoste.ValueMember = "LibellePoste"
        cmbPoste.DisplayMember = "LibellePoste"
        cmbPoste.ColumnHeaders = False
        cmbPoste.Splits(0).DisplayColumns("LibellePoste").Width = 10
        cmbPoste.ExtendRightColumn = True

        cmbPoste.Text = System.Environment.GetEnvironmentVariable("Poste") ' Environment.MachineName.ToString

        afficherParametres()
    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        Dim dr As DataRow

        Dim StrSQL As String = ""

        With dsParametres.Tables("PARAMETRES")
            dr = .Rows(0)

            dr.Item("LecteurUpdate") = tLecteurUpdate.Text
            dr.Item("LecteurSauvegarde") = tLecteurSauvegarde.Text
            dr.Item("DelaiSauvegarde") = tDelaiSauvegarde.Text

            dr.Item("AutoriserLesChangementsAutomatiquesDesPrixPreparation") = chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.Checked
            dr.Item("ImprimanteATicket") = chbImprimanteATicket.Checked
            dr.Item("Tiroir") = chbTiroir.Checked
            dr.Item("USB") = chbUSB.Checked

            dr.Item("COM") = tCom.Text
            dr.Item("ImprimerLesBons") = chbImprimerBon.Checked
            dr.Item("ValiderQteEgalA1") = chbValiderQteEgalA1.Checked
            dr.Item("ControlerLeNombredesUnitesvendues") = chbControleNombreUnites.Checked

            If tTentatives.Text <> "" Then
                dr.Item("NombreDesTentativesPourControlerLeNombreDesArticles") = tTentatives.Text
            Else
                dr.Item("NombreDesTentativesPourControlerLeNombreDesArticles") = 1
            End If

            dr.Item("InscriptionAutomatiqueSurOrdonnacier") = chbInscriptionSurOrdonnancierAutomatique.Checked
            dr.Item("InscriptionAutomatiqueSurOrdonnacierTableauB") = chbTableauB.Checked
            dr.Item("InterdirelaVenteDesProduitsPerimes") = chbInterdireLaVenteDesPerimes.Checked
            'dr.Item("VerifierSiProduitPrisEnChargeParLaCNAM") = chbVerifierProduitPrisEnChargeParCNAM.Checked

            'dr.Item("PermettreUtiliserLesFrigosEnVente") = chbPermettreUtiliserFrigosEnVente.Checked
            ' dr.Item("DureeAffichageDesAlertes") = tDureeAffichageAlerte.Text
            dr.Item("InterdireDeChoisirLesArticlesParDesignation") = chbInterdireChoisirParDesignation.Checked
            dr.Item("NomDeLImprimante") = tNomDeLImprimante.Text
            'dr.Item("RetrancherDuStockLorsDuneVenteenInstance") = chbRetarancheDuStockLorsDeVenteInstance.Checked
            dr.Item("AffichefVenteDetailImage") = chbScanner.Checked
            dr.Item("AfficheurClient") = chbAfficheurClient.Checked
            dr.Item("COMAfficheurClient") = tComAfficheurClient.Text
            dr.Item("LecteurSauvegardeDistant") = tLecteurSauvegardeDistant.Text
        End With

        Try
            daParametres.Update(dsParametres, "PARAMETRES")
            RelectureDesParametres()
            Me.Hide()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsParametres.Reset()
            Me.init()
            Exit Sub
        End Try

    End Sub
    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        If dsParametres.HasChanges Then
            If MsgBox("Voulez vous vraiment annuler les modifications ?", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                Me.Hide()
            End If
        Else
            Me.Hide()
        End If
    End Sub

    Private Sub tTentatives_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTentatives.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tTentatives.Text), 3)
        Catch ex As Exception


            MsgBox("Valeur invalide !", MsgBoxStyle.Critical, "Erreur")
            tTentatives.Text = "0.000"
            tTentatives.Focus()
            tTentatives.SelectionLength = tTentatives.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tTentatives_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tTentatives.TextChanged

    End Sub

    Private Sub cmbPoste_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbPoste.TextChanged
        afficherParametres()
    End Sub

    Private Sub bAjouterPoste_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterPoste.Click
        Dim cmd As New SqlCommand
        Dim NombreDesPostes As Integer = 0

        cmd.Connection = ConnectionServeur
        cmd.CommandText = "SELECT COUNT(*) FROM PARAMETRES "
        Try
            NombreDesPostes = cmd.ExecuteScalar + 1
        Catch ex As Exception
        End Try

        'dans la table parametre
        cmd.CommandText = "INSERT INTO PARAMETRES(POSTE,HonoraireDeResponsabiliteTA,HonoraireDeResponsabiliteTB,HonoraireDeResponsabiliteTC,MinimumDePerception,AjouterTimbreAFacture) " + _
                          " VALUES('" + NombreDesPostes.ToString + "','0.080','0.100','0.080','0.250','false') "
        Try
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            MessageBox.Show("L'ajout dans la table parametre a rencontré l'erreur suivante :      " + ex.Message)
        End Try

        'dans la table poste
        cmd.CommandText = "INSERT INTO POSTE(LibellePoste) " + _
                          " VALUES('" + NombreDesPostes.ToString + "') "
        Try
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            MessageBox.Show("L'ajout dans la table poste a rencontré l'erreur suivante :      " + ex.Message)
        End Try

    End Sub

    Private Sub chbInscriptionSurOrdonnancierAutomatique_CheckedChanged(sender As Object, e As EventArgs) Handles chbInscriptionSurOrdonnancierAutomatique.CheckedChanged
        If chbInscriptionSurOrdonnancierAutomatique.Checked = False Then
            chbTableauB.Checked = False
        End If
        chbTableauB.Enabled = chbInscriptionSurOrdonnancierAutomatique.Checked
    End Sub
End Class
