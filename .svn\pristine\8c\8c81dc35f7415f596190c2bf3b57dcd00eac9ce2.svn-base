﻿Imports System.IO
Imports System.Data.SqlClient
Imports System.Windows.Forms

Public Class fFicheArticle111
    Public ValidationCodeTest As Boolean = False

    Public ajoutmodif As String = ""
    Public CodeArticle As String = ""

    Dim cmdArticle As New SqlCommand
    Dim daArticle As New SqlDataAdapter
    Dim dsArticle As New DataSet
    Dim cbArticle As New SqlCommandBuilder

    Dim dsRecupereNum As New DataSet
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNumt As New SqlDataAdapter

    Dim CmdCalcul As New SqlCommand

    Public Sub Init()
        Dim StrSQL1 As String = ""
        Dim StrSQLdernierAchat As String = ""
        Dim stock As Integer = 0

        'chargement des Formes
        StrSQL1 = "SELECT DISTINCT CodeForme,LibelleForme FROM FORME_ARTICLE Where SupprimeForme=0 ORDER BY LibelleForme ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "FORME_ARTICLE")
        cmbForme.DataSource = dsArticle.Tables("FORME_ARTICLE")
        cmbForme.ValueMember = "CodeForme"
        cmbForme.DisplayMember = "LibelleForme"
        cmbForme.ColumnHeaders = False
        cmbForme.Splits(0).DisplayColumns("CodeForme").Width = 0
        cmbForme.Splits(0).DisplayColumns("LibelleForme").Width = 160

        'chargement des Categories
        StrSQL1 = "SELECT DISTINCT CodeCategorie,LibelleCategorie FROM Categorie WHERE SupprimeCategorie=0 ORDER BY LibelleCategorie ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "CATEGORIE")
        cmbCategorie.DataSource = dsArticle.Tables("CATEGORIE")
        cmbCategorie.ValueMember = "CodeCategorie"
        cmbCategorie.DisplayMember = "LibelleCategorie"
        cmbCategorie.ColumnHeaders = False
        cmbCategorie.Splits(0).DisplayColumns("CodeCategorie").Width = 0
        cmbCategorie.Splits(0).DisplayColumns("LibelleCategorie").Width = 160

        'chargement des Laboratoires
        StrSQL1 = "SELECT DISTINCT CodeLabo,NomLabo FROM LABORATOIRE WHERE SupprimeLabo=0 ORDER BY NomLabo ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "LABORATOIRE")
        cmbLaboratoire.DataSource = dsArticle.Tables("LABORATOIRE")
        cmbLaboratoire.ValueMember = "CodeLabo"
        cmbLaboratoire.DisplayMember = "NomLabo"
        cmbLaboratoire.ColumnHeaders = False
        cmbLaboratoire.Splits(0).DisplayColumns("CodeLabo").Width = 0
        cmbLaboratoire.Splits(0).DisplayColumns("NomLabo").Width = 160

        'chargement des Situations
        StrSQL1 = "SELECT DISTINCT CodeSituationArticle,LibelleSituationArticle FROM SITUATION_ARTICLE ORDER BY LibelleSituationArticle ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "SITUATION_ARTICLE")
        cmbSituation.DataSource = dsArticle.Tables("SITUATION_ARTICLE")
        cmbSituation.ValueMember = "CodeSituationArticle"
        cmbSituation.DisplayMember = "LibelleSituationArticle"
        cmbSituation.ColumnHeaders = False
        cmbSituation.Splits(0).DisplayColumns("CodeSituationArticle").Width = 0
        cmbSituation.Splits(0).DisplayColumns("LibelleSituationArticle").Width = 160

        'chargement des Rayons
        StrSQL1 = "SELECT DISTINCT CodeRayon,LibelleRayon FROM RAYON ORDER BY LibelleRayon ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "RAYON")
        cmbRayon.DataSource = dsArticle.Tables("RAYON")
        cmbRayon.ValueMember = "CodeRayon"
        cmbRayon.DisplayMember = "LibelleRayon"
        cmbRayon.ColumnHeaders = False
        cmbRayon.Splits(0).DisplayColumns("CodeRayon").Width = 0
        cmbRayon.Splits(0).DisplayColumns("LibelleRayon").Width = 160

        'chargement des Valeurs Tableau
        StrSQL1 = "SELECT DISTINCT LibelleTableau FROM TABLEAU ORDER BY LibelleTableau ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "TABELAU")
        cmbTableau.DataSource = dsArticle.Tables("TABELAU")
        cmbTableau.ValueMember = "LibelleTableau"
        cmbTableau.ColumnHeaders = False
        cmbTableau.Splits(0).DisplayColumns("LibelleTableau").Width = 160

        'chargement des Categories Cnam
        StrSQL1 = "SELECT DISTINCT CodeCategorie,LibelleCategorie FROM CATEGORIE_CNAM ORDER BY LibelleCategorie ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "CATEGORIE_CNAM")
        cmbCategorieCnam.DataSource = dsArticle.Tables("CATEGORIE_CNAM")
        cmbCategorieCnam.ValueMember = "CodeCategorie"
        cmbCategorieCnam.DisplayMember = "LibelleCategorie"
        cmbCategorieCnam.ColumnHeaders = False
        cmbCategorieCnam.Splits(0).DisplayColumns("CodeCategorie").Width = 0
        cmbCategorieCnam.Splits(0).DisplayColumns("LibelleCategorie").Width = 160


        'initialiser les prix a 0
        tPrixAchatHT.Text = "0,000"
        tTVA.Text = "0"
        tPrixAchatTTC.Text = "0,000"
        tMarge.Text = "0"
        tPrixVenteHT.Text = "0,000"
        tHR.Text = "0"
        tPrixVenteTTC.Text = "0,000"

        tDesignation.CharacterCasing = CharacterCasing.Upper

        If ajoutmodif = "A" Then
            StrSQL1 = " SELECT TOP 0 * FROM Article"
        Else
            StrSQL1 = " SELECT * FROM Article WHERE CodeArticle = " + Quote(CodeArticle)
        End If

        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL1
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "Article")
        cbArticle = New SqlCommandBuilder(daArticle)

        If ajoutmodif = "M" Then

            tCodeArticle.Text = dsArticle.Tables("Article").Rows(0)("CodeArticle")
            tDesignation.Text = dsArticle.Tables("Article").Rows(0)("Designation")

            cmbForme.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeForme")
            cmbCategorie.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeCategorie")

            cmbLaboratoire.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeLabo")
            cmbTableau.Text = dsArticle.Tables("Article").Rows(0)("LibelleTableau")

            tDosage.Text = dsArticle.Tables("Article").Rows(0)("Dosage")
            cmbRayon.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeRayon")

            tCodePCT.Text = dsArticle.Tables("Article").Rows(0)("CodePCT")
            cmbCategorieCnam.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeCategorieCNAM")

            chkAccordPrealable.Checked = dsArticle.Tables("Article").Rows(0)("AccordPrealable")
            chkPriseEnCharge.Checked = dsArticle.Tables("Article").Rows(0)("PriseEnCharge")

            tTarifReference.Text = dsArticle.Tables("Article").Rows(0)("TarifDeReference")
            cmbSituation.SelectedValue = dsArticle.Tables("Article").Rows(0)("CodeSituation")

            lOperateur.Text = dsArticle.Tables("Article").Rows(0)("CodeOperateur")
            chkSansVignette.Checked = dsArticle.Tables("Article").Rows(0)("SansVignette")

            chkSansCodeAbarre.Checked = dsArticle.Tables("Article").Rows(0)("SansCodeBarre")
            tQuantiteUnitaire.Text = dsArticle.Tables("Article").Rows(0)("QuantiteUnitaire")

            tContenance.Text = dsArticle.Tables("Article").Rows(0)("ContenanceArticle")
            tstockAlert.Text = dsArticle.Tables("Article").Rows(0)("StockAlerte")

            tQuantiteACommander.Text = dsArticle.Tables("Article").Rows(0)("QteACommander")
            tStockInitial.Text = dsArticle.Tables("Article").Rows(0)("Stockinitial")

            tDateInitial.Text = dsArticle.Tables("Article").Rows(0)("Dateinitiale")
            lDateDernierCommande.Text = dsArticle.Tables("Article").Rows(0)("DateDerniereCommande")

            lQuantiteDernierCommande.Text = dsArticle.Tables("Article").Rows(0)("QuantiteDernierCommande")
            tPrixAchatHT.Text = dsArticle.Tables("Article").Rows(0)("PrixAchatHT")

            tTVA.Text = dsArticle.Tables("Article").Rows(0)("TVA")
            tPrixAchatTTC.Text = dsArticle.Tables("Article").Rows(0)("PrixAchatTTC")

            tMarge.Text = dsArticle.Tables("Article").Rows(0)("Marge")
            chkExonere.Checked = dsArticle.Tables("Article").Rows(0)("Exonorertva")

            tPrixVenteHT.Text = dsArticle.Tables("Article").Rows(0)("PrixVenteHT")
            tHR.Text = dsArticle.Tables("Article").Rows(0)("HR")

            tPrixVenteTTC.Text = dsArticle.Tables("Article").Rows(0)("PrixVenteTTC")
            tSection.Text = dsArticle.Tables("ARTICLE").Rows(0)("Section")
            tDateAlerte.Value = dsArticle.Tables("ARTICLE").Rows(0)("DateAlerte")

            ' récupération de la somme des stocks des lots qui ne sont pas périmés
            StrSQLdernierAchat = "select SUM (QteLotArticle) from [PHARMA].[dbo].[LOT_ARTICLE] where CodeArticle=" + Quote(CodeArticle) + " And [DatePeremptionArticle] > " + System.DateTime.Now.Date
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQLdernierAchat

            Try
                stock = CmdCalcul.ExecuteScalar()
                lStock.Text = stock.ToString.Trim
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try


        End If

        '---------------------- verouillage des champs indisponibles ------------
        tCodeArticle.Focus()

    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click

        Dim dr As DataRow
        Dim StrSQL As String = ""

        If tCodeArticle.Text = "" Then
            MsgBox("Veuillez saisir le code de l'article !", MsgBoxStyle.Critical, "Erreur")
            tCodeArticle.Focus()
            Exit Sub
        End If

        If tDesignation.Text = "" Then
            MsgBox("Veuillez saisir la Designation de l'article !", MsgBoxStyle.Critical, "Erreur")
            tDesignation.Focus()
            Exit Sub
        End If

        If cmbCategorie.Text = "" Then
            MsgBox("Veuillez saisir la catégorie de l'article !", MsgBoxStyle.Critical, "Erreur")
            cmbCategorie.Focus()
            Exit Sub
        End If

        If tPrixVenteTTC.Text = "" Then
            MsgBox("Veuillez saisir le prix de l'article !", MsgBoxStyle.Critical, "Erreur")
            tPrixVenteTTC.Focus()
            Exit Sub
        End If

        If ajoutmodif = "A" Then
            With dsArticle
                dr = .Tables("Article").NewRow

                dr.Item("CodeArticle") = tCodeArticle.Text
                dr.Item("Designation") = tDesignation.Text

                dr.Item("codeForme") = cmbForme.SelectedValue
                dr.Item("CodeCategorie") = cmbCategorie.SelectedValue

                dr.Item("CodeLabo") = cmbLaboratoire.SelectedValue
                dr.Item("Tableau") = cmbTableau.Text

                dr.Item("Dosage") = tDosage.Text
                dr.Item("CodeRayon") = cmbRayon.SelectedValue

                dr.Item("CodePCT") = tCodePCT.Text
                dr.Item("CodeCategorieCNAM") = cmbCategorieCnam.SelectedValue

                dr.Item("AccordPrealable") = chkAccordPrealable.Checked
                dr.Item("PriseEnCharge") = chkPriseEnCharge.Checked
                If tTarifReference.Text <> "" Then
                    dr.Item("TarifDeReference") = tTarifReference.Text
                Else
                    dr.Item("TarifDeReference") = 0.0
                End If

                dr.Item("CodeSituation") = cmbSituation.SelectedValue

                'dr.Item("CodeOperateur") = tOperateur.Text
                dr.Item("SansVignette") = chkSansVignette.Checked

                dr.Item("SansCodeBarre") = chkSansCodeAbarre.Checked
                If tQuantiteUnitaire.Text <> "" Then
                    dr.Item("QuantiteUnitaire") = tQuantiteUnitaire.Text
                    'Else
                    'dr.Item("QuantiteUnitaire") = 0
                End If
                If tContenance.Text <> "" Then
                    dr.Item("ContenanceArticle") = tContenance.Text
                End If
                If tstockAlert.Text <> "" Then
                    dr.Item("StockAlerte") = tstockAlert.Text
                End If
                If tQuantiteACommander.Text <> "" Then
                    dr.Item("QteACommander") = tQuantiteACommander.Text
                End If
                If tStockInitial.Text <> "" Then
                    dr.Item("StockInitial") = tStockInitial.Text
                End If
                If tDateInitial.Text <> "" Then
                    dr.Item("Dateinitiale") = tDateInitial.Text
                End If
                dr.Item("PrixAchatHT") = CDec(tPrixAchatHT.Text)

                dr.Item("TVA") = tTVA.Text
                dr.Item("PrixAchatTTC") = tPrixAchatTTC.Text

                dr.Item("Marge") = tMarge.Text
                dr.Item("Exonorertva") = chkExonere.Checked


                dr.Item("PrixVenteHT") = tPrixVenteHT.Text
                dr.Item("HR") = tHR.Text

                dr.Item("PrixVenteTTC") = tPrixVenteTTC.Text
                dr.Item("Section") = tSection.Text

                .Tables("Article").Rows.Add(dr)
            End With

        ElseIf ajoutmodif = "M" Then
            With dsArticle.Tables("Article")
                dr = .Rows(0)

                dr.Item("CodeArticle") = tCodeArticle.Text
                dr.Item("Designation") = tDesignation.Text

                dr.Item("codeForme") = cmbForme.SelectedValue
                dr.Item("CodeCategorie") = cmbCategorie.SelectedValue

                dr.Item("CodeLabo") = cmbLaboratoire.SelectedValue
                dr.Item("Tableau") = cmbTableau.Text

                dr.Item("Dosage") = tDosage.Text
                dr.Item("CodeRayon") = cmbRayon.SelectedValue

                dr.Item("CodePCT") = tCodePCT.Text
                dr.Item("CodeCategorieCNAM") = cmbCategorieCnam.SelectedValue

                dr.Item("AccordPrealable") = chkAccordPrealable.Checked
                dr.Item("PriseEnCharge") = chkPriseEnCharge.Checked

                dr.Item("TarifDeReference") = Convert.ToDecimal(tTarifReference.Text)
                dr.Item("CodeSituation") = cmbSituation.SelectedValue


                dr.Item("SansVignette") = chkSansVignette.Checked
                dr.Item("SansCodeBarre") = chkSansCodeAbarre.Checked

                dr.Item("QuantiteUnitaire") = tQuantiteUnitaire.Text
                dr.Item("ContenanceArticle") = tContenance.Text

                dr.Item("StockAlerte") = tstockAlert.Text
                dr.Item("QteACommander") = tQuantiteACommander.Text

                dr.Item("StockInitial") = tStockInitial.Text

                dr.Item("Dateinitiale") = tDateInitial.Text

                dr.Item("PrixAchatHT") = CDec(tPrixAchatHT.Text)

                dr.Item("TVA") = tTVA.Text
                dr.Item("PrixAchatTTC") = Convert.ToDecimal(tPrixAchatTTC.Text)

                dr.Item("Marge") = Convert.ToDecimal(tMarge.Text)

                dr.Item("Exonorertva") = chkExonere.Checked


                dr.Item("PrixVenteHT") = Convert.ToDecimal(tPrixVenteHT.Text)
                dr.Item("HR") = Convert.ToDecimal(tHR.Text)

                dr.Item("PrixVenteTTC") = convert.ToDecimal(tPrixVenteTTC.Text)
                dr.Item("Section") = tSection.Text

            End With

        End If

        Try
            daArticle.Update(dsArticle, "Article")
            fMain.TAB.SelectedTab.Dispose()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsArticle.Reset()
            Me.Init()
        End Try

    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        If dsArticle.HasChanges Then
            If MsgBox("Voulez vous vraiment annuler les modifications de ce Article ?", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                fMain.TAB.SelectedTab.Dispose()
            End If
        Else
            fMain.TAB.SelectedTab.Dispose()
        End If
    End Sub

    Private Sub tCodeArticle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeArticle.KeyUp
        If e.KeyCode = Keys.Enter Then
            tDesignation.Focus()
        End If
    End Sub

    Private Sub tCodeArticle_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeArticle.LostFocus
        If lTest.Text = "Code valide" Then
            lTest.Visible = False
        End If
    End Sub

    Private Sub tCodeArticle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeArticle.TextChanged
        Dim StrSQLtest As String = ""
        dsRecupereNum.Clear()
        If ajoutmodif = "A" Then
            StrSQLtest = " SELECT * FROM Article WHERE CodeArticle=" + Quote(tCodeArticle.Text)
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQLtest
            daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
            daRecupereNumt.Fill(dsRecupereNum, "Article")

            If dsRecupereNum.Tables("Article").Rows.Count <> 0 Or tCodeArticle.Text = "" Then
                lTest.Text = "Code non valide déja existe"
                lTest.ForeColor = Color.OrangeRed
                ValidationCodeTest = False
                lTest.Visible = True
            Else
                lTest.Text = "Code valide"
                lTest.ForeColor = Color.LimeGreen
                ValidationCodeTest = True
                lTest.Visible = True
            End If
        End If
        If tCodeArticle.Text = "" Then
            lTest.Visible = False
        End If
    End Sub
    '######################################
    'lors du clic slectionne tt le champ

    Private Sub tPrixAchatHT_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixAchatHT.Click
        tPrixAchatHT.SelectAll()
    End Sub

    Private Sub tTVA_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles tTVA.Click
        tTVA.SelectAll()
    End Sub
    Private Sub tPrixAchatTTC_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixAchatTTC.Click
        tPrixAchatTTC.SelectAll()
    End Sub

    Private Sub tMarge_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles tMarge.Click
        tMarge.SelectAll()
    End Sub

    Private Sub tPrixVenteHT_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixVenteHT.Click
        tPrixVenteHT.SelectAll()
    End Sub

    Private Sub tHR_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles tHR.Click
        tHR.SelectAll()
    End Sub

    Private Sub tPrixVenteTTC_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixVenteTTC.Click
        tPrixVenteTTC.SelectAll()
    End Sub
    '######################################

    '%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    'teste sur le format des prix

    Private Sub tPrixAchatHT_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixAchatHT.LostFocus
        formatprix(tPrixAchatHT)
        tPrixAchatTTC.Text = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tTVA.Text) / 100).ToString
        tPrixVenteHT.Text = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tMarge.Text) / 100).ToString
        tPrixVenteTTC.Text = ((CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tTVA.Text) / 100) + (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tTVA.Text) / 100) * CDbl(tMarge.Text) / 100).ToString
    End Sub

    Private Sub tPrixAchatTTC_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixAchatTTC.LostFocus
        formatprix(tPrixAchatTTC)
    End Sub

    Private Sub tPrixVenteHT_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixVenteHT.LostFocus
        formatprix(tPrixVenteHT)
    End Sub

    Private Sub tPrixVenteTTC_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tPrixVenteTTC.LostFocus
        formatprix(tPrixVenteTTC)
    End Sub

    Private Sub tTVA_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tTVA.LostFocus
        tPrixAchatTTC.Text = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tTVA.Text) / 100).ToString
        tPrixVenteHT.Text = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tMarge.Text) / 100).ToString
        tPrixVenteTTC.Text = ((CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tTVA.Text) / 100) + (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tTVA.Text) / 100) * CDbl(tMarge.Text) / 100).ToString
    End Sub

    Private Sub tMarge_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tMarge.LostFocus
        tPrixVenteHT.Text = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tMarge.Text) / 100).ToString
        tPrixVenteTTC.Text = ((CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tTVA.Text) / 100) + (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tTVA.Text) / 100) * CDbl(tMarge.Text) / 100).ToString
    End Sub

    Private Sub tMarge_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tMarge.KeyUp
        If tMarge.Text <> "" Then
            tPrixVenteHT.Text = (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tMarge.Text) / 100).ToString
            tPrixVenteTTC.Text = ((CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tTVA.Text) / 100) + (CDbl(tPrixAchatHT.Text) + CDbl(tPrixAchatHT.Text) * CDbl(tTVA.Text) / 100) * CDbl(tMarge.Text) / 100).ToString
        End If
    End Sub
    'OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOz
    Private Sub tPrixAchatHT_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tPrixAchatHT.KeyPress
        If IsNumeric(e.KeyChar) = False And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "," And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "." Then
            e.Handled = True
        End If
    End Sub

    Private Sub tPrixAchatTTC_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tPrixAchatTTC.KeyPress
        If IsNumeric(e.KeyChar) = False And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "," And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "." Then
            e.Handled = True
        End If
    End Sub

    Private Sub tPrixVenteHT_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tPrixVenteHT.KeyPress
        If IsNumeric(e.KeyChar) = False And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "," And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "." Then
            e.Handled = True
        End If
    End Sub
    Private Sub tPrixVenteTTC_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tPrixVenteTTC.KeyPress
        If IsNumeric(e.KeyChar) = False And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "," And Asc(e.KeyChar) <> System.Windows.Forms.Keys.Back And e.KeyChar <> "." Then
            e.Handled = True
        End If
    End Sub

    Private Sub Panel_Paint(ByVal sender As System.Object, ByVal e As System.Windows.Forms.PaintEventArgs) Handles Panel.Paint

    End Sub
End Class

