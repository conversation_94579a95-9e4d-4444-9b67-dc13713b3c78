﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2012
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "Pharma2000Premium", "PHARMA\Pharma2000Premium.vbproj", "{082B5048-4D66-49C5-A8C7-2C9CC2B977E5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "DATA", "DATA", "{E36CED30-C443-4BFF-8431-C320117C91CB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "BLL", "BLL", "{74420397-0650-4BAF-99AA-0EF0A6CC47BD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ReportingManagement", "BLL\Reporting\ReportingManagement.csproj", "{F194144E-3099-4CE6-8F15-96A29367D910}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ReportingManagement", "DATA\Reporting\ReportingManagement.csproj", "{0C74776F-83EB-4547-98FB-36FC71C93CDF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "StockManagement", "DATA\StockManagement\StockManagement.csproj", "{CB1323A3-4388-40D6-AA25-95F4A55681A7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "StockManagement", "BLL\StockManagement\StockManagement.csproj", "{C5ECC9AC-FCC5-4012-86E6-DA40E9C07EBD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Library", "Library", "{A5F70A00-FDB4-48BF-A3B9-285650AB0DE6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library", "Library\Library.csproj", "{66D12D6F-433D-429C-98E7-EE10EBC64F6E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "PharmaML", "PharmaML", "{FF1A0B0C-A104-482D-AE5F-CCB25F687310}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaML", "PharmaML\PharmaML.csproj", "{69459627-972C-4F12-AE57-2298EEB06DB8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BusinessManagement", "BLL\BusinessManagement\BusinessManagement.csproj", "{F97F5909-3958-4DCC-AD2D-4D414E9A6BBC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BusinessManagement", "DATA\BusinessManagement\BusinessManagement.csproj", "{6005C983-E7E7-4E2F-8CF2-A3D87812621B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ErrorManagement", "DATA\ErrorManagement\ErrorManagement.csproj", "{01C94F33-E309-4334-A62D-CF8F468421C5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ErrorManagement", "BLL\ErrorManagement\ErrorManagement.csproj", "{BA0BE93E-8550-48F3-A1DE-D93207956C41}"
EndProject
Global
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 12
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://tfs:8080/tfs/next-product
		SccLocalPath0 = .
		SccProjectUniqueName1 = PHARMA\\Pharma2000Premium.vbproj
		SccProjectName1 = PHARMA
		SccLocalPath1 = PHARMA
		SccProjectUniqueName2 = DATA\\StockManagement\\StockManagement.csproj
		SccProjectTopLevelParentUniqueName2 = Pharma2000Premium.sln
		SccProjectName2 = DATA/StockManagement
		SccLocalPath2 = DATA\\StockManagement
		SccProjectUniqueName3 = BLL\\StockManagement\\StockManagement.csproj
		SccProjectTopLevelParentUniqueName3 = Pharma2000Premium.sln
		SccProjectName3 = BLL/StockManagement
		SccLocalPath3 = BLL\\StockManagement
		SccProjectUniqueName4 = Library\\Library.csproj
		SccProjectTopLevelParentUniqueName4 = Pharma2000Premium.sln
		SccProjectName4 = Library
		SccLocalPath4 = Library
		SccProjectUniqueName5 = PharmaML\\PharmaML.csproj
		SccProjectTopLevelParentUniqueName5 = Pharma2000Premium.sln
		SccProjectName5 = PharmaML
		SccLocalPath5 = PharmaML
		SccProjectUniqueName6 = BLL\\BusinessManagement\\BusinessManagement.csproj
		SccProjectTopLevelParentUniqueName6 = Pharma2000Premium.sln
		SccProjectName6 = BLL/BusinessManagement
		SccLocalPath6 = BLL\\BusinessManagement
		SccProjectUniqueName7 = DATA\\BusinessManagement\\BusinessManagement.csproj
		SccProjectTopLevelParentUniqueName7 = Pharma2000Premium.sln
		SccProjectName7 = DATA/BusinessManagement
		SccLocalPath7 = DATA\\BusinessManagement
		SccProjectUniqueName8 = DATA\\ErrorManagement\\ErrorManagement.csproj
		SccProjectTopLevelParentUniqueName8 = Pharma2000Premium.sln
		SccProjectName8 = DATA/ErrorManagement
		SccLocalPath8 = DATA\\ErrorManagement
		SccProjectUniqueName9 = BLL\\ErrorManagement\\ErrorManagement.csproj
		SccProjectTopLevelParentUniqueName9 = Pharma2000Premium.sln
		SccProjectName9 = BLL/ErrorManagement
		SccLocalPath9 = BLL\\ErrorManagement
		SccProjectUniqueName10 = BLL\\Reporting\\ReportingManagement.csproj
		SccProjectTopLevelParentUniqueName10 = Pharma2000Premium.sln
		SccProjectName10 = BLL/Reporting
		SccLocalPath10 = BLL\\Reporting
		SccProjectUniqueName11 = DATA\\Reporting\\ReportingManagement.csproj
		SccProjectTopLevelParentUniqueName11 = Pharma2000Premium.sln
		SccProjectName11 = DATA/Reporting
		SccLocalPath11 = DATA\\Reporting
	EndGlobalSection
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|Mixed Platforms = Debug|Mixed Platforms
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|Mixed Platforms = Release|Mixed Platforms
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{082B5048-4D66-49C5-A8C7-2C9CC2B977E5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{082B5048-4D66-49C5-A8C7-2C9CC2B977E5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{082B5048-4D66-49C5-A8C7-2C9CC2B977E5}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{082B5048-4D66-49C5-A8C7-2C9CC2B977E5}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{082B5048-4D66-49C5-A8C7-2C9CC2B977E5}.Debug|x86.ActiveCfg = Debug|Any CPU
		{082B5048-4D66-49C5-A8C7-2C9CC2B977E5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{082B5048-4D66-49C5-A8C7-2C9CC2B977E5}.Release|Any CPU.Build.0 = Release|Any CPU
		{082B5048-4D66-49C5-A8C7-2C9CC2B977E5}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{082B5048-4D66-49C5-A8C7-2C9CC2B977E5}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{082B5048-4D66-49C5-A8C7-2C9CC2B977E5}.Release|x86.ActiveCfg = Release|Any CPU
		{F194144E-3099-4CE6-8F15-96A29367D910}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F194144E-3099-4CE6-8F15-96A29367D910}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F194144E-3099-4CE6-8F15-96A29367D910}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{F194144E-3099-4CE6-8F15-96A29367D910}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{F194144E-3099-4CE6-8F15-96A29367D910}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F194144E-3099-4CE6-8F15-96A29367D910}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F194144E-3099-4CE6-8F15-96A29367D910}.Release|Any CPU.Build.0 = Release|Any CPU
		{F194144E-3099-4CE6-8F15-96A29367D910}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{F194144E-3099-4CE6-8F15-96A29367D910}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{F194144E-3099-4CE6-8F15-96A29367D910}.Release|x86.ActiveCfg = Release|Any CPU
		{0C74776F-83EB-4547-98FB-36FC71C93CDF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0C74776F-83EB-4547-98FB-36FC71C93CDF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0C74776F-83EB-4547-98FB-36FC71C93CDF}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{0C74776F-83EB-4547-98FB-36FC71C93CDF}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{0C74776F-83EB-4547-98FB-36FC71C93CDF}.Debug|x86.ActiveCfg = Debug|Any CPU
		{0C74776F-83EB-4547-98FB-36FC71C93CDF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0C74776F-83EB-4547-98FB-36FC71C93CDF}.Release|Any CPU.Build.0 = Release|Any CPU
		{0C74776F-83EB-4547-98FB-36FC71C93CDF}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{0C74776F-83EB-4547-98FB-36FC71C93CDF}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{0C74776F-83EB-4547-98FB-36FC71C93CDF}.Release|x86.ActiveCfg = Release|Any CPU
		{CB1323A3-4388-40D6-AA25-95F4A55681A7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CB1323A3-4388-40D6-AA25-95F4A55681A7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CB1323A3-4388-40D6-AA25-95F4A55681A7}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{CB1323A3-4388-40D6-AA25-95F4A55681A7}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{CB1323A3-4388-40D6-AA25-95F4A55681A7}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CB1323A3-4388-40D6-AA25-95F4A55681A7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CB1323A3-4388-40D6-AA25-95F4A55681A7}.Release|Any CPU.Build.0 = Release|Any CPU
		{CB1323A3-4388-40D6-AA25-95F4A55681A7}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{CB1323A3-4388-40D6-AA25-95F4A55681A7}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{CB1323A3-4388-40D6-AA25-95F4A55681A7}.Release|x86.ActiveCfg = Release|Any CPU
		{C5ECC9AC-FCC5-4012-86E6-DA40E9C07EBD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C5ECC9AC-FCC5-4012-86E6-DA40E9C07EBD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C5ECC9AC-FCC5-4012-86E6-DA40E9C07EBD}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{C5ECC9AC-FCC5-4012-86E6-DA40E9C07EBD}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{C5ECC9AC-FCC5-4012-86E6-DA40E9C07EBD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C5ECC9AC-FCC5-4012-86E6-DA40E9C07EBD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C5ECC9AC-FCC5-4012-86E6-DA40E9C07EBD}.Release|Any CPU.Build.0 = Release|Any CPU
		{C5ECC9AC-FCC5-4012-86E6-DA40E9C07EBD}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{C5ECC9AC-FCC5-4012-86E6-DA40E9C07EBD}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{C5ECC9AC-FCC5-4012-86E6-DA40E9C07EBD}.Release|x86.ActiveCfg = Release|Any CPU
		{66D12D6F-433D-429C-98E7-EE10EBC64F6E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{66D12D6F-433D-429C-98E7-EE10EBC64F6E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{66D12D6F-433D-429C-98E7-EE10EBC64F6E}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{66D12D6F-433D-429C-98E7-EE10EBC64F6E}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{66D12D6F-433D-429C-98E7-EE10EBC64F6E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{66D12D6F-433D-429C-98E7-EE10EBC64F6E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{66D12D6F-433D-429C-98E7-EE10EBC64F6E}.Release|Any CPU.Build.0 = Release|Any CPU
		{66D12D6F-433D-429C-98E7-EE10EBC64F6E}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{66D12D6F-433D-429C-98E7-EE10EBC64F6E}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{66D12D6F-433D-429C-98E7-EE10EBC64F6E}.Release|x86.ActiveCfg = Release|Any CPU
		{69459627-972C-4F12-AE57-2298EEB06DB8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{69459627-972C-4F12-AE57-2298EEB06DB8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{69459627-972C-4F12-AE57-2298EEB06DB8}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{69459627-972C-4F12-AE57-2298EEB06DB8}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{69459627-972C-4F12-AE57-2298EEB06DB8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{69459627-972C-4F12-AE57-2298EEB06DB8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{69459627-972C-4F12-AE57-2298EEB06DB8}.Release|Any CPU.Build.0 = Release|Any CPU
		{69459627-972C-4F12-AE57-2298EEB06DB8}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{69459627-972C-4F12-AE57-2298EEB06DB8}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{69459627-972C-4F12-AE57-2298EEB06DB8}.Release|x86.ActiveCfg = Release|Any CPU
		{F97F5909-3958-4DCC-AD2D-4D414E9A6BBC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F97F5909-3958-4DCC-AD2D-4D414E9A6BBC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F97F5909-3958-4DCC-AD2D-4D414E9A6BBC}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{F97F5909-3958-4DCC-AD2D-4D414E9A6BBC}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{F97F5909-3958-4DCC-AD2D-4D414E9A6BBC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F97F5909-3958-4DCC-AD2D-4D414E9A6BBC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F97F5909-3958-4DCC-AD2D-4D414E9A6BBC}.Release|Any CPU.Build.0 = Release|Any CPU
		{F97F5909-3958-4DCC-AD2D-4D414E9A6BBC}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{F97F5909-3958-4DCC-AD2D-4D414E9A6BBC}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{F97F5909-3958-4DCC-AD2D-4D414E9A6BBC}.Release|x86.ActiveCfg = Release|Any CPU
		{6005C983-E7E7-4E2F-8CF2-A3D87812621B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6005C983-E7E7-4E2F-8CF2-A3D87812621B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6005C983-E7E7-4E2F-8CF2-A3D87812621B}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{6005C983-E7E7-4E2F-8CF2-A3D87812621B}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{6005C983-E7E7-4E2F-8CF2-A3D87812621B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6005C983-E7E7-4E2F-8CF2-A3D87812621B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6005C983-E7E7-4E2F-8CF2-A3D87812621B}.Release|Any CPU.Build.0 = Release|Any CPU
		{6005C983-E7E7-4E2F-8CF2-A3D87812621B}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{6005C983-E7E7-4E2F-8CF2-A3D87812621B}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{6005C983-E7E7-4E2F-8CF2-A3D87812621B}.Release|x86.ActiveCfg = Release|Any CPU
		{01C94F33-E309-4334-A62D-CF8F468421C5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{01C94F33-E309-4334-A62D-CF8F468421C5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{01C94F33-E309-4334-A62D-CF8F468421C5}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{01C94F33-E309-4334-A62D-CF8F468421C5}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{01C94F33-E309-4334-A62D-CF8F468421C5}.Debug|x86.ActiveCfg = Debug|Any CPU
		{01C94F33-E309-4334-A62D-CF8F468421C5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{01C94F33-E309-4334-A62D-CF8F468421C5}.Release|Any CPU.Build.0 = Release|Any CPU
		{01C94F33-E309-4334-A62D-CF8F468421C5}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{01C94F33-E309-4334-A62D-CF8F468421C5}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{01C94F33-E309-4334-A62D-CF8F468421C5}.Release|x86.ActiveCfg = Release|Any CPU
		{BA0BE93E-8550-48F3-A1DE-D93207956C41}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BA0BE93E-8550-48F3-A1DE-D93207956C41}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BA0BE93E-8550-48F3-A1DE-D93207956C41}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{BA0BE93E-8550-48F3-A1DE-D93207956C41}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{BA0BE93E-8550-48F3-A1DE-D93207956C41}.Debug|x86.ActiveCfg = Debug|Any CPU
		{BA0BE93E-8550-48F3-A1DE-D93207956C41}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BA0BE93E-8550-48F3-A1DE-D93207956C41}.Release|Any CPU.Build.0 = Release|Any CPU
		{BA0BE93E-8550-48F3-A1DE-D93207956C41}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{BA0BE93E-8550-48F3-A1DE-D93207956C41}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{BA0BE93E-8550-48F3-A1DE-D93207956C41}.Release|x86.ActiveCfg = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{0C74776F-83EB-4547-98FB-36FC71C93CDF} = {E36CED30-C443-4BFF-8431-C320117C91CB}
		{CB1323A3-4388-40D6-AA25-95F4A55681A7} = {E36CED30-C443-4BFF-8431-C320117C91CB}
		{6005C983-E7E7-4E2F-8CF2-A3D87812621B} = {E36CED30-C443-4BFF-8431-C320117C91CB}
		{01C94F33-E309-4334-A62D-CF8F468421C5} = {E36CED30-C443-4BFF-8431-C320117C91CB}
		{F194144E-3099-4CE6-8F15-96A29367D910} = {74420397-0650-4BAF-99AA-0EF0A6CC47BD}
		{C5ECC9AC-FCC5-4012-86E6-DA40E9C07EBD} = {74420397-0650-4BAF-99AA-0EF0A6CC47BD}
		{F97F5909-3958-4DCC-AD2D-4D414E9A6BBC} = {74420397-0650-4BAF-99AA-0EF0A6CC47BD}
		{BA0BE93E-8550-48F3-A1DE-D93207956C41} = {74420397-0650-4BAF-99AA-0EF0A6CC47BD}
		{66D12D6F-433D-429C-98E7-EE10EBC64F6E} = {A5F70A00-FDB4-48BF-A3B9-285650AB0DE6}
		{69459627-972C-4F12-AE57-2298EEB06DB8} = {FF1A0B0C-A104-482D-AE5F-CCB25F687310}
	EndGlobalSection
EndGlobal
