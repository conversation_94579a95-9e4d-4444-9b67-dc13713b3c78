﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fSpecialiteMedecin

    Dim cmdSpecialite As New SqlCommand
    Dim daSpecialite As New SqlDataAdapter
    Dim cbSpecialite As New SqlCommandBuilder
    Dim dsSpecialite As New DataSet

    Dim xSpecialite As Integer
    Dim ModeSpecialite As String
    Dim CodeSpecialite As String

    Dim dsRecupereNum As New DataSet
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNumt As New SqlDataAdapter

    Dim CodeExiste As Boolean = False
    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub
    Public Sub afficherSpecialite()
        Dim I As Integer
        Dim Cond As String = "1=1"
        dsSpecialite.Clear()
        cmdSpecialite.CommandText = " SELECT " + _
                                    " CodeSpecialite, " + _
                                    " LibelleSpecialite " + _
                                    " FROM SPECIALITE_MEDECIN WHERE " + Cond + _
                                    " ORDER BY CodeSpecialite"

        cmdSpecialite.Connection = ConnectionServeur
        daSpecialite = New SqlDataAdapter(cmdSpecialite)
        daSpecialite.Fill(dsSpecialite, "SPECIALITE")

        With gSpecialite
            .Columns.Clear()
            .DataSource = dsSpecialite
            .DataMember = "SPECIALITE"
            .Rebind(False)
            .Columns("CodeSpecialite").Caption = "Code Specialite"
            .Columns("LibelleSpecialite").Caption = "Libelle Specialite"
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("CodeSpecialite").Width = 120
            .Splits(0).DisplayColumns("CodeSpecialite").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("LibelleSpecialite").Width = 80
            .Splits(0).DisplayColumns("LibelleSpecialite").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(0).DisplayColumns("CodeSpecialite").Locked = True
            .Splits(0).DisplayColumns("LibelleSpecialite").Locked = True

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gSpecialite)
        End With
        gSpecialite.MoveRelative(xSpecialite)
        cbSpecialite = New SqlCommandBuilder(daSpecialite)
    End Sub

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click
        Dim dr As DataRow
        Dim StrSQL As String = ""

        If tCodeSpecialte.Text = "" Then
            MsgBox("Veuillez saisir le code de la Spécialité !", MsgBoxStyle.Critical, "Erreur")
            tCodeSpecialte.Focus()
            Exit Sub
        End If
        If tLibelleSpecialite.Text = "" Then
            MsgBox("Veuillez saisir le libelle de la Spécialité !", MsgBoxStyle.Critical, "Erreur")
            tLibelleSpecialite.Focus()
            Exit Sub
        End If

        If CodeExiste = True Then
            MsgBox("Code Spécialité existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
            tCodeSpecialte.Focus()
            Exit Sub
        End If

        With dsSpecialite
            dr = .Tables("SPECIALITE").NewRow
            dr.Item("LibelleSpecialite") = tLibelleSpecialite.Text
            dr.Item("CodeSpecialite") = tCodeSpecialte.Text
            .Tables("SPECIALITE").Rows.Add(dr)
        End With

        Try
            daSpecialite.Update(dsSpecialite, "SPECIALITE")
            afficherSpecialite()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsSpecialite.Reset()
        End Try

        tCodeSpecialte.Text = ""
        tLibelleSpecialite.Text = ""
    End Sub

    Private Sub bSupprimerCategorie_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click
        Dim cmd As New SqlCommand
        If gSpecialite.RowCount > 0 Then
            If MsgBox("Voulez vous vraiment supprimer cette Spécialité " + Quote(gSpecialite(gSpecialite.Row, "LibelleSpecialite")) + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM SPECIALITE_MEDECIN WHERE CodeSpecialite =" + Quote(gSpecialite(gSpecialite.Row, "CodeSpecialite"))
                    cmd.ExecuteNonQuery()
                    afficherSpecialite()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub

    Private Sub gSpecialite_AfterColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gSpecialite.AfterColEdit
        Dim dr As DataRow
        Dim StrSQL As String = ""

        With dsSpecialite.Tables("SPECIALITE_MAJ")
            dr = .Rows(0)
            dr.Item("LibelleSpecialite") = gSpecialite(gSpecialite.Row, "LibelleSpecialite")
        End With

        Try
            daSpecialite.Update(dsSpecialite, "SPECIALITE_MAJ")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            afficherSpecialite()
        End Try
    End Sub

    Private Sub gSpecialite_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gSpecialite.Click
        Dim StrSQL As String = ""
        CodeSpecialite = Quote(gSpecialite(gSpecialite.Row, "CodeSpecialite"))
        If CodeSpecialite = "" Then
            MsgBox("Veuillez sélectionner le libelle de la Spécialité !", MsgBoxStyle.Critical, "Erreur")
            gSpecialite.Focus()
            Exit Sub
        End If
        If (dsSpecialite.Tables.IndexOf("SPECIALITE_MAJ") > -1) Then
            dsSpecialite.Tables("SPECIALITE_MAJ").Clear()
        End If

        StrSQL = " SELECT * FROM SPECIALITE_MEDECIN WHERE CodeSpecialite = " + CodeSpecialite
        cmdSpecialite.Connection = ConnectionServeur
        cmdSpecialite.CommandText = StrSQL
        daSpecialite = New SqlDataAdapter(cmdSpecialite)
        daSpecialite.Fill(dsSpecialite, "SPECIALITE_MAJ")
        cbSpecialite = New SqlCommandBuilder(daSpecialite)
    End Sub

    Private Sub tCodeSpecialte_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeSpecialte.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLibelleSpecialite.Focus()
        End If
    End Sub

    Private Sub tCodeSpecialte_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeSpecialte.LostFocus
        lTest.Visible = False
    End Sub

    Private Sub tCodeSpecialte_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeSpecialte.TextChanged
        If tCodeSpecialte.Text <> "" Then
            If IsNumeric(tCodeSpecialte.Text.Substring(Len(tCodeSpecialte.Text) - 1, 1)) = False Then
                MsgBox("Veuillez introduire des chiffres !", MsgBoxStyle.Critical, "Erreur")
                tCodeSpecialte.Text = tCodeSpecialte.Text.Substring(0, Len(tCodeSpecialte.Text) - 1)
                tCodeSpecialte.Select(Len(tCodeSpecialte.Text), 0)
            End If
        End If

        Dim StrSQLtest As String = ""
        If (dsRecupereNum.Tables.IndexOf("SPECIALITE_MAJ") > -1) Then
            dsRecupereNum.Tables("SPECIALITE_MAJ").Clear()
        End If

        StrSQLtest = " SELECT * FROM SPECIALITE_MEDECIN as SPECIALITE_MAJ WHERE CodeSpecialite=" + Quote(tCodeSpecialte.Text)
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQLtest
        daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
        daRecupereNumt.Fill(dsRecupereNum, "SPECIALITE_MAJ")

        If dsRecupereNum.Tables("SPECIALITE_MAJ").Rows.Count <> 0 Then
            lTest.Text = "Code non valide déja existe"
            lTest.ForeColor = Color.OrangeRed
            lTest.Visible = True
            CodeExiste = True
        Else
            lTest.Text = "Code valide"
            lTest.ForeColor = Color.LawnGreen
            lTest.Visible = True
            CodeExiste = False
        End If
        If tCodeSpecialte.Text = "" Then
            lTest.Visible = False
        End If
    End Sub

    Public Sub Init()
        afficherSpecialite()
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub
End Class