﻿Imports System.IO
Imports System.Drawing.Printing
Imports System.Runtime.InteropServices


Imports System
Imports System.Drawing
Imports System.Windows.Forms

Public Class fTestStatisqtique

    Inherits System.Windows.Forms.Form
    Private WithEvents printButton As System.Windows.Forms.Button
    Private printFont As Font
    Public streamToPrint As StreamReader

    Public Sub init()
    End Sub

    Public Sub Print_Document_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Print_Document.Click
        Try
            streamToPrint = New StreamReader("C:\NextSoftware_EPL.txt")
            Try
                printFont = New Font("Arial", 6)
                Dim pd As New PrintDocument()
                AddHandler pd.PrintPage, AddressOf Me.pd_PrintPage
                pd.PrinterSettings.PrinterName = "PDFCreator" ' "ZDesignerC" '"EPSON LX-300+ /II" '
                pd.DocumentName = "C:\NextSoftware_EPL.tx"

                pd.Print()

                'PrintDocument1.PrinterSettings.PrinterName = "ZDesignerC"
                'PrintDocument1.DocumentName = "C:\NextSoftware_EPL.tx"
                'PrintDocument1.Print()
            Finally
                streamToPrint.Close()
            End Try
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try

        'Dim monProcess As New Process()
        'monProcess.StartInfo.FileName = "C:\NextSoftware_EPL.tx"
        'monProcess.StartInfo.Verb = "Print"
        'monProcess.StartInfo.CreateNoWindow = True
        'monProcess.Start()


    End Sub
    ' The PrintPage event is raised for each page to be printed.
    Public Sub pd_PrintPage(ByVal sender As Object, ByVal ev As PrintPageEventArgs)
        Dim linesPerPage As Single = 0
        Dim yPos As Single = 0
        Dim count As Integer = 0
        Dim leftMargin As Single = ev.MarginBounds.Left
        Dim topMargin As Single = ev.MarginBounds.Top
        Dim line As String = Nothing

        ' Calculate the number of lines per page.
        linesPerPage = ev.MarginBounds.Height / printFont.GetHeight(ev.Graphics)

        ' Print each line of the file.
        While count < linesPerPage
            line = streamToPrint.ReadLine()
            If line Is Nothing Then
                Exit While
            End If
            yPos = topMargin + count * printFont.GetHeight(ev.Graphics)
            ev.Graphics.DrawString(line, printFont, Brushes.Black, leftMargin, yPos, New StringFormat())
            count += 1
        End While

        ' If more lines exist, print another page.
        If (line IsNot Nothing) Then
            ev.HasMorePages = True
        Else
            ev.HasMorePages = False
        End If


    End Sub
    Private Sub PrintDocument1_PrintPage(ByVal sender As System.Object, ByVal e As System.Drawing.Printing.PrintPageEventArgs) Handles PrintDocument1.PrintPage
        e.Graphics.DrawString("Hello", New Font("Arial", 80, FontStyle.Bold), Brushes.Black, 150, 125)
    End Sub

    Private Sub send_file_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles send_file.Click

        ' Allow the user to select a file.
        Dim ofd As New OpenFileDialog()
        If ofd.ShowDialog(Me) Then
            ' Allow the user to select a printer.
            Dim pd As New PrintDialog()
            pd.PrinterSettings = New PrinterSettings()
            If (pd.ShowDialog() = DialogResult.OK) Then
                ' Print the file to the printer.
                RawPrinterHelper.SendFileToPrinter(pd.PrinterSettings.PrinterName, ofd.FileName)
            End If
        End If
    End Sub '

    Private Sub send_a_string_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles send_a_string.Click
        Dim s As String
        Dim pd As New PrintDialog()

        ' You need a string to send.
        s = "Hello, this is a test"
        s = "^XA ^FO50,50^ADN,36,20^FDxxxxxxxxxxx ^FS ^XZ"
        ' Open the printer dialog box, and then allow the user to select a printer.

        'PrintDocument1.DocumentName = "C:\Nouveau document texte.txt"

        'AddHandler PrintDocument1.PrintPage, AddressOf Me.PrintPageHandler
        'PrintDocument1.Print()
        'RemoveHandler PrintDocument1.PrintPage, AddressOf Me.PrintPageHandler

        'PrintDocument1.Print()

        pd.PrinterSettings = New PrinterSettings()
        If (pd.ShowDialog() = DialogResult.OK) Then
            RawPrinterHelper.SendStringToPrinter(pd.PrinterSettings.PrinterName, s)
        End If

    End Sub
    Private Sub PrintPageHandler(ByVal sender As Object, ByVal args As Printing.PrintPageEventArgs)
        Static i As Integer = 0
        i = i + 1
        ' Print a line on this page
        args.Graphics.DrawString("^XA ^F0100,100^BY3^BCN,100,Y,N,N^FD123456^FS ^XZ", New Font(Me.Font, FontStyle.Regular), Brushes.Black, 50, 50)
        args.HasMorePages = False
        Exit Sub
        ' Have we printed enough pages?
        If i >= 5 Then
            ' Done all the pages
            i = 0
            args.HasMorePages = False
        Else
            ' Not finished, yet
            args.HasMorePages = True
        End If
    End Sub

    Private Sub test_cb25_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles test_cb25.Click
        'Shell("cb25.exe")
        'Shell("net use lpt1 \\wassim-pc\Zebra1")
        'Shell("type C:\Users\<USER>\Documents\Visual Studio 2008\Projects\PHARMA\PHARMA\bin\Debug\cb.txt >prn")
    End Sub
End Class

Public Class RawPrinterHelper
    ' Structure and API declarions:
    <StructLayout(LayoutKind.Sequential, CharSet:=CharSet.Unicode)> _
    Structure DOCINFOW
        <MarshalAs(UnmanagedType.LPWStr)> Public pDocName As String
        <MarshalAs(UnmanagedType.LPWStr)> Public pOutputFile As String
        <MarshalAs(UnmanagedType.LPWStr)> Public pDataType As String
    End Structure

    <DllImport("winspool.Drv", EntryPoint:="OpenPrinterW", _
       SetLastError:=True, CharSet:=CharSet.Unicode, _
       ExactSpelling:=True, CallingConvention:=CallingConvention.StdCall)> _
    Public Shared Function OpenPrinter(ByVal src As String, ByRef hPrinter As IntPtr, ByVal pd As Long) As Boolean
    End Function
    <DllImport("winspool.Drv", EntryPoint:="ClosePrinter", _
       SetLastError:=True, CharSet:=CharSet.Unicode, _
       ExactSpelling:=True, CallingConvention:=CallingConvention.StdCall)> _
    Public Shared Function ClosePrinter(ByVal hPrinter As IntPtr) As Boolean
    End Function
    <DllImport("winspool.Drv", EntryPoint:="StartDocPrinterW", _
       SetLastError:=True, CharSet:=CharSet.Unicode, _
       ExactSpelling:=True, CallingConvention:=CallingConvention.StdCall)> _
    Public Shared Function StartDocPrinter(ByVal hPrinter As IntPtr, ByVal level As Int32, ByRef pDI As DOCINFOW) As Boolean
    End Function
    <DllImport("winspool.Drv", EntryPoint:="EndDocPrinter", _
       SetLastError:=True, CharSet:=CharSet.Unicode, _
       ExactSpelling:=True, CallingConvention:=CallingConvention.StdCall)> _
    Public Shared Function EndDocPrinter(ByVal hPrinter As IntPtr) As Boolean
    End Function
    '<DllImport("winspool.Drv", EntryPoint:="StartPagePrinter", _
    '   SetLastError:=True, CharSet:=CharSet.Unicode, _
    '   ExactSpelling:=True, CallingConvention:=CallingConvention.StdCall)> _
    'Public Shared Function StartPagePrinter(ByVal hPrinter As IntPtr) As Boolean
    'End Function
    Private Declare Function StartPagePrinter Lib "winspool.drv" (ByVal hPrinter As Long) As Long
    <DllImport("winspool.Drv", EntryPoint:="EndPagePrinter", _
       SetLastError:=True, CharSet:=CharSet.Unicode, _
       ExactSpelling:=True, CallingConvention:=CallingConvention.StdCall)> _
    Public Shared Function EndPagePrinter(ByVal hPrinter As IntPtr) As Boolean
    End Function
    <DllImport("winspool.Drv", EntryPoint:="WritePrinter", _
       SetLastError:=True, CharSet:=CharSet.Unicode, _
       ExactSpelling:=True, CallingConvention:=CallingConvention.StdCall)> _
    Public Shared Function WritePrinter(ByVal hPrinter As IntPtr, ByVal pBytes As IntPtr, ByVal dwCount As Int32, ByRef dwWritten As Int32) As Boolean
    End Function

    ' SendBytesToPrinter()
    ' When the function is given a printer name and an unmanaged array of  
    ' bytes, the function sends those bytes to the print queue.
    ' Returns True on success or False on failure.
    Public Shared Function SendBytesToPrinter(ByVal szPrinterName As String, ByVal pBytes As IntPtr, ByVal dwCount As Int32) As Boolean
        Dim hPrinter As IntPtr      ' The printer handle.
        Dim dwError As Int32        ' Last error - in case there was trouble.
        Dim di As DOCINFOW          ' Describes your document (name, port, data type).
        Dim dwWritten As Int32      ' The number of bytes written by WritePrinter().
        Dim bSuccess As Boolean     ' Your success code.

        ' Set up the DOCINFO structure.
        With di
            .pDocName = "My Visual Basic .NET RAW Document"
            .pDataType = "RAW"
        End With
        ' Assume failure unless you specifically succeed.
        bSuccess = False
        If OpenPrinter(szPrinterName, hPrinter, 0) Then
            If StartDocPrinter(hPrinter, 1, di) Then
                If StartPagePrinter(hPrinter) Then
                    ' Write your printer-specific bytes to the printer.
                    bSuccess = WritePrinter(hPrinter, pBytes, dwCount, dwWritten)
                    EndPagePrinter(hPrinter)
                End If
                EndDocPrinter(hPrinter)
            End If
            ClosePrinter(hPrinter)
        End If
        ' If you did not succeed, GetLastError may give more information
        ' about why not.
        If bSuccess = False Then
            dwError = Marshal.GetLastWin32Error()
        End If
        Return bSuccess
    End Function ' SendBytesToPrinter()

    ' SendFileToPrinter()
    ' When the function is given a file name and a printer name, 
    ' the function reads the contents of the file and sends the
    ' contents to the printer.
    ' Presumes that the file contains printer-ready data.
    ' Shows how to use the SendBytesToPrinter function.
    ' Returns True on success or False on failure.
    Public Shared Function SendFileToPrinter(ByVal szPrinterName As String, ByVal szFileName As String) As Boolean
        ' Open the file.
        Dim fs As New FileStream(szFileName, FileMode.Open)
        ' Create a BinaryReader on the file.
        Dim br As New BinaryReader(fs)
        ' Dim an array of bytes large enough to hold the file's contents.
        Dim bytes(fs.Length) As Byte
        Dim bSuccess As Boolean
        ' Your unmanaged pointer.
        Dim pUnmanagedBytes As IntPtr

        ' Read the contents of the file into the array.
        bytes = br.ReadBytes(fs.Length)
        ' Allocate some unmanaged memory for those bytes.
        pUnmanagedBytes = Marshal.AllocCoTaskMem(fs.Length)
        ' Copy the managed byte array into the unmanaged array.
        Marshal.Copy(bytes, 0, pUnmanagedBytes, fs.Length)
        ' Send the unmanaged bytes to the printer.
        bSuccess = SendBytesToPrinter(szPrinterName, pUnmanagedBytes, fs.Length)
        ' Free the unmanaged memory that you allocated earlier.
        Marshal.FreeCoTaskMem(pUnmanagedBytes)
        Return bSuccess
    End Function ' SendFileToPrinter()

    ' When the function is given a string and a printer name,
    ' the function sends the string to the printer as raw bytes.
    Public Shared Function SendStringToPrinter(ByVal szPrinterName As String, ByVal szString As String)
        Dim pBytes As IntPtr
        Dim dwCount As Int32
        ' How many characters are in the string?
        dwCount = szString.Length()
        ' Assume that the printer is expecting ANSI text, and then convert
        ' the string to ANSI text.
        pBytes = Marshal.StringToCoTaskMemAnsi(szString)
        ' Send the converted ANSI string to the printer.
        SendBytesToPrinter(szPrinterName, pBytes, dwCount)
        Marshal.FreeCoTaskMem(pBytes)
    End Function
End Class
