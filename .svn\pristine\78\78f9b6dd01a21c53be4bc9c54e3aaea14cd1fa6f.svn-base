﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fStrategieDeStockage
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fStrategieDeStockage))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.GroupeJauge = New System.Windows.Forms.GroupBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.ProgressBar = New System.Windows.Forms.ProgressBar()
        Me.lArticleEnCours = New System.Windows.Forms.Label()
        Me.gListeRecherche = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.lNombreDesArticles = New System.Windows.Forms.Label()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.bSupprimer = New C1.Win.C1Input.C1Button()
        Me.GroupeFournisseur = New System.Windows.Forms.GroupBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.dtpDebut = New C1.Win.C1Input.C1DateEdit()
        Me.lValeurFixee = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.cmbCategorie = New C1.Win.C1List.C1Combo()
        Me.lMatricule = New System.Windows.Forms.Label()
        Me.bModifier = New C1.Win.C1Input.C1Button()
        Me.bImprimer = New C1.Win.C1Input.C1Button()
        Me.bLast = New C1.Win.C1Input.C1Button()
        Me.bNext = New C1.Win.C1Input.C1Button()
        Me.GroupeNumero = New System.Windows.Forms.GroupBox()
        Me.lOperateur = New System.Windows.Forms.Label()
        Me.lDateSimulation = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.lNumeroSimulation = New System.Windows.Forms.Label()
        Me.LNumero = New System.Windows.Forms.Label()
        Me.bFirst = New C1.Win.C1Input.C1Button()
        Me.bPrevious = New C1.Win.C1Input.C1Button()
        Me.bAjouter = New C1.Win.C1Input.C1Button()
        Me.bConfirmer = New C1.Win.C1Input.C1Button()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.lTotalHTAchat = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.lTotalTTCAchat = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.gArticles = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.CR = New Pharma2000Premium.EtatStrategieDeStockage()
        Me.Panel.SuspendLayout()
        Me.GroupeJauge.SuspendLayout()
        CType(Me.gListeRecherche, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupeFournisseur.SuspendLayout()
        CType(Me.dtpDebut, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbCategorie, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupeNumero.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.GroupeJauge)
        Me.Panel.Controls.Add(Me.gListeRecherche)
        Me.Panel.Controls.Add(Me.lNombreDesArticles)
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.bSupprimer)
        Me.Panel.Controls.Add(Me.GroupeFournisseur)
        Me.Panel.Controls.Add(Me.bModifier)
        Me.Panel.Controls.Add(Me.bImprimer)
        Me.Panel.Controls.Add(Me.bLast)
        Me.Panel.Controls.Add(Me.bNext)
        Me.Panel.Controls.Add(Me.GroupeNumero)
        Me.Panel.Controls.Add(Me.bFirst)
        Me.Panel.Controls.Add(Me.bPrevious)
        Me.Panel.Controls.Add(Me.bAjouter)
        Me.Panel.Controls.Add(Me.bConfirmer)
        Me.Panel.Controls.Add(Me.GroupBox2)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.gArticles)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1028, 566)
        Me.Panel.TabIndex = 3
        '
        'GroupeJauge
        '
        Me.GroupeJauge.Controls.Add(Me.Label1)
        Me.GroupeJauge.Controls.Add(Me.Label7)
        Me.GroupeJauge.Controls.Add(Me.ProgressBar)
        Me.GroupeJauge.Controls.Add(Me.lArticleEnCours)
        Me.GroupeJauge.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeJauge.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupeJauge.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.GroupeJauge.Location = New System.Drawing.Point(214, 197)
        Me.GroupeJauge.Name = "GroupeJauge"
        Me.GroupeJauge.Size = New System.Drawing.Size(483, 94)
        Me.GroupeJauge.TabIndex = 37
        Me.GroupeJauge.TabStop = False
        Me.GroupeJauge.Visible = False
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(8, 12)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(469, 25)
        Me.Label1.TabIndex = 73
        Me.Label1.Text = "Analyse en cours "
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.Location = New System.Drawing.Point(18, 42)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(63, 13)
        Me.Label7.TabIndex = 10
        Me.Label7.Text = "Article  :  "
        '
        'ProgressBar
        '
        Me.ProgressBar.Location = New System.Drawing.Point(23, 65)
        Me.ProgressBar.Name = "ProgressBar"
        Me.ProgressBar.Size = New System.Drawing.Size(433, 17)
        Me.ProgressBar.TabIndex = 72
        Me.ProgressBar.Value = 50
        Me.ProgressBar.Visible = False
        '
        'lArticleEnCours
        '
        Me.lArticleEnCours.BackColor = System.Drawing.Color.Transparent
        Me.lArticleEnCours.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lArticleEnCours.Location = New System.Drawing.Point(87, 42)
        Me.lArticleEnCours.Name = "lArticleEnCours"
        Me.lArticleEnCours.Size = New System.Drawing.Size(379, 15)
        Me.lArticleEnCours.TabIndex = 33
        Me.lArticleEnCours.Text = "-------------"
        '
        'gListeRecherche
        '
        Me.gListeRecherche.AllowUpdate = False
        Me.gListeRecherche.AlternatingRows = True
        Me.gListeRecherche.CaptionHeight = 17
        Me.gListeRecherche.GroupByCaption = "Drag a column header here to group by that column"
        Me.gListeRecherche.Images.Add(CType(resources.GetObject("gListeRecherche.Images"), System.Drawing.Image))
        Me.gListeRecherche.LinesPerRow = 2
        Me.gListeRecherche.Location = New System.Drawing.Point(146, 125)
        Me.gListeRecherche.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gListeRecherche.Name = "gListeRecherche"
        Me.gListeRecherche.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gListeRecherche.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gListeRecherche.PreviewInfo.ZoomFactor = 75.0R
        Me.gListeRecherche.PrintInfo.PageSettings = CType(resources.GetObject("gListeRecherche.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gListeRecherche.RowHeight = 15
        Me.gListeRecherche.Size = New System.Drawing.Size(610, 209)
        Me.gListeRecherche.TabIndex = 49
        Me.gListeRecherche.Text = "C1TrueDBGrid1"
        Me.gListeRecherche.Visible = False
        Me.gListeRecherche.PropBag = resources.GetString("gListeRecherche.PropBag")
        '
        'lNombreDesArticles
        '
        Me.lNombreDesArticles.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lNombreDesArticles.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lNombreDesArticles.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNombreDesArticles.Location = New System.Drawing.Point(12, 465)
        Me.lNombreDesArticles.Name = "lNombreDesArticles"
        Me.lNombreDesArticles.Size = New System.Drawing.Size(143, 22)
        Me.lNombreDesArticles.TabIndex = 110
        Me.lNombreDesArticles.Text = "-"
        Me.lNombreDesArticles.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lNombreDesArticles.Visible = False
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(944, 524)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(72, 34)
        Me.bQuitter.TabIndex = 73
        Me.bQuitter.Text = "Quitter F12"
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bSupprimer
        '
        Me.bSupprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bSupprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSupprimer.Location = New System.Drawing.Point(434, 524)
        Me.bSupprimer.Name = "bSupprimer"
        Me.bSupprimer.Size = New System.Drawing.Size(75, 34)
        Me.bSupprimer.TabIndex = 61
        Me.bSupprimer.Text = "Supprimer F7"
        Me.bSupprimer.UseVisualStyleBackColor = True
        Me.bSupprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupeFournisseur
        '
        Me.GroupeFournisseur.Controls.Add(Me.Label4)
        Me.GroupeFournisseur.Controls.Add(Me.dtpDebut)
        Me.GroupeFournisseur.Controls.Add(Me.lValeurFixee)
        Me.GroupeFournisseur.Controls.Add(Me.Label3)
        Me.GroupeFournisseur.Controls.Add(Me.cmbCategorie)
        Me.GroupeFournisseur.Controls.Add(Me.lMatricule)
        Me.GroupeFournisseur.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeFournisseur.Font = New System.Drawing.Font("Calibri", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupeFournisseur.Location = New System.Drawing.Point(222, 8)
        Me.GroupeFournisseur.Name = "GroupeFournisseur"
        Me.GroupeFournisseur.Size = New System.Drawing.Size(458, 79)
        Me.GroupeFournisseur.TabIndex = 3
        Me.GroupeFournisseur.TabStop = False
        '
        'Label4
        '
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.Location = New System.Drawing.Point(20, 20)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(61, 13)
        Me.Label4.TabIndex = 112
        Me.Label4.Text = "Date"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'dtpDebut
        '
        Me.dtpDebut.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDebut.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDebut.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDebut.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebut.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebut.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.dtpDebut.Location = New System.Drawing.Point(23, 40)
        Me.dtpDebut.Name = "dtpDebut"
        Me.dtpDebut.Size = New System.Drawing.Size(141, 18)
        Me.dtpDebut.TabIndex = 111
        Me.dtpDebut.Tag = Nothing
        Me.dtpDebut.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebut.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lValeurFixee
        '
        Me.lValeurFixee.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lValeurFixee.Location = New System.Drawing.Point(170, 36)
        Me.lValeurFixee.Name = "lValeurFixee"
        Me.lValeurFixee.Size = New System.Drawing.Size(74, 22)
        Me.lValeurFixee.TabIndex = 60
        Me.lValeurFixee.Text = "Valeur fixée"
        Me.lValeurFixee.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label3
        '
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.Location = New System.Drawing.Point(324, 17)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(64, 16)
        Me.Label3.TabIndex = 59
        Me.Label3.Text = "Catégorie"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'cmbCategorie
        '
        Me.cmbCategorie.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbCategorie.Caption = ""
        Me.cmbCategorie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbCategorie.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbCategorie.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbCategorie.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbCategorie.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbCategorie.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbCategorie.Images.Add(CType(resources.GetObject("cmbCategorie.Images"), System.Drawing.Image))
        Me.cmbCategorie.Location = New System.Drawing.Point(259, 36)
        Me.cmbCategorie.MatchEntryTimeout = CType(2000, Long)
        Me.cmbCategorie.MaxDropDownItems = CType(5, Short)
        Me.cmbCategorie.MaxLength = 32767
        Me.cmbCategorie.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbCategorie.Name = "cmbCategorie"
        Me.cmbCategorie.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbCategorie.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbCategorie.Size = New System.Drawing.Size(189, 22)
        Me.cmbCategorie.TabIndex = 2
        Me.cmbCategorie.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbCategorie.PropBag = resources.GetString("cmbCategorie.PropBag")
        '
        'lMatricule
        '
        Me.lMatricule.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lMatricule.Location = New System.Drawing.Point(179, 20)
        Me.lMatricule.Name = "lMatricule"
        Me.lMatricule.Size = New System.Drawing.Size(84, 13)
        Me.lMatricule.TabIndex = 56
        Me.lMatricule.Text = "Valeur fixée"
        Me.lMatricule.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'bModifier
        '
        Me.bModifier.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bModifier.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bModifier.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bModifier.Location = New System.Drawing.Point(584, 524)
        Me.bModifier.Name = "bModifier"
        Me.bModifier.Size = New System.Drawing.Size(71, 34)
        Me.bModifier.TabIndex = 58
        Me.bModifier.Text = "Modifier  F8"
        Me.bModifier.UseVisualStyleBackColor = True
        Me.bModifier.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bImprimer
        '
        Me.bImprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bImprimer.Location = New System.Drawing.Point(656, 524)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(71, 34)
        Me.bImprimer.TabIndex = 4
        Me.bImprimer.Text = "Imprimer F11"
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bLast
        '
        Me.bLast.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bLast.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bLast.Image = Global.Pharma2000Premium.My.Resources.Resources.last_dounloaded
        Me.bLast.Location = New System.Drawing.Point(195, 524)
        Me.bLast.Name = "bLast"
        Me.bLast.Size = New System.Drawing.Size(59, 33)
        Me.bLast.TabIndex = 11
        Me.bLast.UseVisualStyleBackColor = True
        Me.bLast.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bNext
        '
        Me.bNext.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bNext.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bNext.Image = Global.Pharma2000Premium.My.Resources.Resources.next_downloaded
        Me.bNext.Location = New System.Drawing.Point(134, 524)
        Me.bNext.Name = "bNext"
        Me.bNext.Size = New System.Drawing.Size(59, 33)
        Me.bNext.TabIndex = 10
        Me.bNext.UseVisualStyleBackColor = True
        Me.bNext.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupeNumero
        '
        Me.GroupeNumero.Controls.Add(Me.lOperateur)
        Me.GroupeNumero.Controls.Add(Me.lDateSimulation)
        Me.GroupeNumero.Controls.Add(Me.Label2)
        Me.GroupeNumero.Controls.Add(Me.lNumeroSimulation)
        Me.GroupeNumero.Controls.Add(Me.LNumero)
        Me.GroupeNumero.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeNumero.Location = New System.Drawing.Point(12, 8)
        Me.GroupeNumero.Name = "GroupeNumero"
        Me.GroupeNumero.Size = New System.Drawing.Size(203, 79)
        Me.GroupeNumero.TabIndex = 13
        Me.GroupeNumero.TabStop = False
        Me.GroupeNumero.Text = "Identification"
        '
        'lOperateur
        '
        Me.lOperateur.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lOperateur.Location = New System.Drawing.Point(6, 57)
        Me.lOperateur.Name = "lOperateur"
        Me.lOperateur.Size = New System.Drawing.Size(191, 13)
        Me.lOperateur.TabIndex = 37
        Me.lOperateur.Text = "-"
        '
        'lDateSimulation
        '
        Me.lDateSimulation.BackColor = System.Drawing.Color.Transparent
        Me.lDateSimulation.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDateSimulation.Location = New System.Drawing.Point(50, 36)
        Me.lDateSimulation.Name = "lDateSimulation"
        Me.lDateSimulation.Size = New System.Drawing.Size(144, 16)
        Me.lDateSimulation.TabIndex = 36
        Me.lDateSimulation.Text = "Date"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(7, 16)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(44, 13)
        Me.Label2.TabIndex = 34
        Me.Label2.Text = "Numéro"
        '
        'lNumeroSimulation
        '
        Me.lNumeroSimulation.AutoSize = True
        Me.lNumeroSimulation.BackColor = System.Drawing.Color.Transparent
        Me.lNumeroSimulation.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNumeroSimulation.Location = New System.Drawing.Point(58, 16)
        Me.lNumeroSimulation.Name = "lNumeroSimulation"
        Me.lNumeroSimulation.Size = New System.Drawing.Size(59, 13)
        Me.lNumeroSimulation.TabIndex = 35
        Me.lNumeroSimulation.Text = "-------------"
        '
        'LNumero
        '
        Me.LNumero.AutoSize = True
        Me.LNumero.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LNumero.Location = New System.Drawing.Point(6, 36)
        Me.LNumero.Name = "LNumero"
        Me.LNumero.Size = New System.Drawing.Size(36, 13)
        Me.LNumero.TabIndex = 10
        Me.LNumero.Text = "Date :"
        '
        'bFirst
        '
        Me.bFirst.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bFirst.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bFirst.Image = Global.Pharma2000Premium.My.Resources.Resources.first_dounloaded
        Me.bFirst.Location = New System.Drawing.Point(12, 524)
        Me.bFirst.Name = "bFirst"
        Me.bFirst.Size = New System.Drawing.Size(59, 34)
        Me.bFirst.TabIndex = 8
        Me.bFirst.UseVisualStyleBackColor = True
        Me.bFirst.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bPrevious
        '
        Me.bPrevious.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bPrevious.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bPrevious.Image = Global.Pharma2000Premium.My.Resources.Resources.previous_downloaded
        Me.bPrevious.Location = New System.Drawing.Point(73, 524)
        Me.bPrevious.Name = "bPrevious"
        Me.bPrevious.Size = New System.Drawing.Size(59, 33)
        Me.bPrevious.TabIndex = 9
        Me.bPrevious.UseVisualStyleBackColor = True
        Me.bPrevious.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouter
        '
        Me.bAjouter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouter.Location = New System.Drawing.Point(728, 524)
        Me.bAjouter.Name = "bAjouter"
        Me.bAjouter.Size = New System.Drawing.Size(71, 34)
        Me.bAjouter.TabIndex = 5
        Me.bAjouter.Text = "Ajouter    F5"
        Me.bAjouter.UseVisualStyleBackColor = True
        Me.bAjouter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bConfirmer
        '
        Me.bConfirmer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bConfirmer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmer.Location = New System.Drawing.Point(800, 524)
        Me.bConfirmer.Name = "bConfirmer"
        Me.bConfirmer.Size = New System.Drawing.Size(71, 34)
        Me.bConfirmer.TabIndex = 6
        Me.bConfirmer.Text = "Confirmer F3"
        Me.bConfirmer.UseVisualStyleBackColor = True
        Me.bConfirmer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox2
        '
        Me.GroupBox2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox2.Controls.Add(Me.lTotalHTAchat)
        Me.GroupBox2.Controls.Add(Me.Label8)
        Me.GroupBox2.Controls.Add(Me.lTotalTTCAchat)
        Me.GroupBox2.Controls.Add(Me.Label6)
        Me.GroupBox2.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupBox2.Location = New System.Drawing.Point(575, 465)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(440, 48)
        Me.GroupBox2.TabIndex = 12
        Me.GroupBox2.TabStop = False
        '
        'lTotalHTAchat
        '
        Me.lTotalHTAchat.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lTotalHTAchat.BackColor = System.Drawing.Color.Transparent
        Me.lTotalHTAchat.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotalHTAchat.ForeColor = System.Drawing.Color.Black
        Me.lTotalHTAchat.Location = New System.Drawing.Point(106, 16)
        Me.lTotalHTAchat.Name = "lTotalHTAchat"
        Me.lTotalHTAchat.Size = New System.Drawing.Size(92, 23)
        Me.lTotalHTAchat.TabIndex = 64
        Me.lTotalHTAchat.Text = "9999.999"
        Me.lTotalHTAchat.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label8
        '
        Me.Label8.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label8.BackColor = System.Drawing.Color.Transparent
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.ForeColor = System.Drawing.Color.Black
        Me.Label8.Location = New System.Drawing.Point(12, 16)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(83, 23)
        Me.Label8.TabIndex = 63
        Me.Label8.Text = "TOTAL HT :"
        Me.Label8.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lTotalTTCAchat
        '
        Me.lTotalTTCAchat.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lTotalTTCAchat.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotalTTCAchat.ForeColor = System.Drawing.Color.Red
        Me.lTotalTTCAchat.Location = New System.Drawing.Point(332, 18)
        Me.lTotalTTCAchat.Name = "lTotalTTCAchat"
        Me.lTotalTTCAchat.Size = New System.Drawing.Size(99, 19)
        Me.lTotalTTCAchat.TabIndex = 35
        Me.lTotalTTCAchat.Text = "999999.999"
        Me.lTotalTTCAchat.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.Location = New System.Drawing.Point(241, 18)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(88, 19)
        Me.Label6.TabIndex = 48
        Me.Label6.Text = "TOTAL TTC :"
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(872, 524)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(71, 34)
        Me.bAnnuler.TabIndex = 7
        Me.bAnnuler.Text = "Annuler F10"
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gArticles
        '
        Me.gArticles.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gArticles.CaptionHeight = 17
        Me.gArticles.GroupByCaption = "Drag a column header here to group by that column"
        Me.gArticles.Images.Add(CType(resources.GetObject("gArticles.Images"), System.Drawing.Image))
        Me.gArticles.LinesPerRow = 2
        Me.gArticles.Location = New System.Drawing.Point(12, 94)
        Me.gArticles.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
        Me.gArticles.Name = "gArticles"
        Me.gArticles.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gArticles.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gArticles.PreviewInfo.ZoomFactor = 75.0R
        Me.gArticles.PrintInfo.PageSettings = CType(resources.GetObject("gArticles.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gArticles.RowHeight = 15
        Me.gArticles.Size = New System.Drawing.Size(1004, 353)
        Me.gArticles.TabIndex = 2
        Me.gArticles.Text = "C1TrueDBGrid1"
        Me.gArticles.PropBag = resources.GetString("gArticles.PropBag")
        '
        'fStrategieDeStockage
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1028, 566)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fStrategieDeStockage"
        Me.Text = "fStrategieDeStockage"
        Me.Panel.ResumeLayout(False)
        Me.GroupeJauge.ResumeLayout(False)
        Me.GroupeJauge.PerformLayout()
        CType(Me.gListeRecherche, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupeFournisseur.ResumeLayout(False)
        Me.GroupeFournisseur.PerformLayout()
        CType(Me.dtpDebut, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbCategorie, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupeNumero.ResumeLayout(False)
        Me.GroupeNumero.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents GroupeJauge As System.Windows.Forms.GroupBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents ProgressBar As System.Windows.Forms.ProgressBar
    Friend WithEvents lArticleEnCours As System.Windows.Forms.Label
    Friend WithEvents gListeRecherche As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents lNombreDesArticles As System.Windows.Forms.Label
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents bSupprimer As C1.Win.C1Input.C1Button
    Friend WithEvents GroupeFournisseur As System.Windows.Forms.GroupBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents cmbCategorie As C1.Win.C1List.C1Combo
    Friend WithEvents bModifier As C1.Win.C1Input.C1Button
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
    Friend WithEvents bLast As C1.Win.C1Input.C1Button
    Friend WithEvents bNext As C1.Win.C1Input.C1Button
    Friend WithEvents GroupeNumero As System.Windows.Forms.GroupBox
    Friend WithEvents LNumero As System.Windows.Forms.Label
    Friend WithEvents bFirst As C1.Win.C1Input.C1Button
    Friend WithEvents bPrevious As C1.Win.C1Input.C1Button
    Friend WithEvents bAjouter As C1.Win.C1Input.C1Button
    Friend WithEvents bConfirmer As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents lTotalHTAchat As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents lTotalTTCAchat As System.Windows.Forms.Label
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents gArticles As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents lNumeroSimulation As System.Windows.Forms.Label
    Friend WithEvents lDateSimulation As System.Windows.Forms.Label
    Friend WithEvents lOperateur As System.Windows.Forms.Label
    Friend WithEvents lValeurFixee As System.Windows.Forms.Label
    Friend WithEvents CR As Pharma2000Premium.EtatStrategieDeStockage
    Friend WithEvents lMatricule As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents dtpDebut As C1.Win.C1Input.C1DateEdit
End Class
