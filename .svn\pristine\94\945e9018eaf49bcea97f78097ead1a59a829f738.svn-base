﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fCorrectionDesLots
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fCorrectionDesLots))
        Me.Panel = New System.Windows.Forms.Panel
        Me.GroupBox8 = New System.Windows.Forms.GroupBox
        Me.tCodePCT = New C1.Win.C1Input.C1TextBox
        Me.Label25 = New System.Windows.Forms.Label
        Me.tCodeArticle = New C1.Win.C1Input.C1TextBox
        Me.Label1 = New System.Windows.Forms.Label
        Me.cmbDesignation = New C1.Win.C1List.C1Combo
        Me.Label26 = New System.Windows.Forms.Label
        Me.tSupprimer = New C1.Win.C1Input.C1Button
        Me.Label24 = New System.Windows.Forms.Label
        Me.bAjouter = New C1.Win.C1Input.C1Button
        Me.gLots = New C1.Win.C1TrueDBGrid.C1TrueDBGrid
        Me.Panel.SuspendLayout()
        Me.GroupBox8.SuspendLayout()
        CType(Me.tCodePCT, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodeArticle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbDesignation, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gLots, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.GroupBox8)
        Me.Panel.Controls.Add(Me.tSupprimer)
        Me.Panel.Controls.Add(Me.Label24)
        Me.Panel.Controls.Add(Me.bAjouter)
        Me.Panel.Controls.Add(Me.gLots)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(902, 475)
        Me.Panel.TabIndex = 1
        '
        'GroupBox8
        '
        Me.GroupBox8.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox8.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox8.Controls.Add(Me.tCodePCT)
        Me.GroupBox8.Controls.Add(Me.Label25)
        Me.GroupBox8.Controls.Add(Me.tCodeArticle)
        Me.GroupBox8.Controls.Add(Me.Label1)
        Me.GroupBox8.Controls.Add(Me.cmbDesignation)
        Me.GroupBox8.Controls.Add(Me.Label26)
        Me.GroupBox8.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox8.Location = New System.Drawing.Point(12, 12)
        Me.GroupBox8.Name = "GroupBox8"
        Me.GroupBox8.Size = New System.Drawing.Size(420, 96)
        Me.GroupBox8.TabIndex = 80
        Me.GroupBox8.TabStop = False
        '
        'tCodePCT
        '
        Me.tCodePCT.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tCodePCT.Location = New System.Drawing.Point(91, 37)
        Me.tCodePCT.Name = "tCodePCT"
        Me.tCodePCT.Size = New System.Drawing.Size(188, 20)
        Me.tCodePCT.TabIndex = 35
        Me.tCodePCT.Tag = Nothing
        '
        'Label25
        '
        Me.Label25.BackColor = System.Drawing.Color.Transparent
        Me.Label25.Location = New System.Drawing.Point(6, 41)
        Me.Label25.Name = "Label25"
        Me.Label25.Size = New System.Drawing.Size(84, 13)
        Me.Label25.TabIndex = 37
        Me.Label25.Text = "Code PCT"
        '
        'tCodeArticle
        '
        Me.tCodeArticle.Location = New System.Drawing.Point(91, 13)
        Me.tCodeArticle.Name = "tCodeArticle"
        Me.tCodeArticle.Size = New System.Drawing.Size(188, 20)
        Me.tCodeArticle.TabIndex = 34
        Me.tCodeArticle.Tag = Nothing
        '
        'Label1
        '
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.Location = New System.Drawing.Point(6, 17)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(79, 13)
        Me.Label1.TabIndex = 36
        Me.Label1.Text = "Code Article"
        '
        'cmbDesignation
        '
        Me.cmbDesignation.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbDesignation.Caption = ""
        Me.cmbDesignation.CaptionHeight = 17
        Me.cmbDesignation.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbDesignation.ColumnCaptionHeight = 17
        Me.cmbDesignation.ColumnFooterHeight = 17
        Me.cmbDesignation.ContentHeight = 16
        Me.cmbDesignation.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbDesignation.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbDesignation.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbDesignation.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbDesignation.EditorHeight = 16
        Me.cmbDesignation.Images.Add(CType(resources.GetObject("cmbDesignation.Images"), System.Drawing.Image))
        Me.cmbDesignation.ItemHeight = 15
        Me.cmbDesignation.Location = New System.Drawing.Point(91, 61)
        Me.cmbDesignation.MatchEntryTimeout = CType(2000, Long)
        Me.cmbDesignation.MaxDropDownItems = CType(5, Short)
        Me.cmbDesignation.MaxLength = 32767
        Me.cmbDesignation.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbDesignation.Name = "cmbDesignation"
        Me.cmbDesignation.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbDesignation.Size = New System.Drawing.Size(303, 22)
        Me.cmbDesignation.TabIndex = 33
        Me.cmbDesignation.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbDesignation.PropBag = resources.GetString("cmbDesignation.PropBag")
        '
        'Label26
        '
        Me.Label26.BackColor = System.Drawing.Color.Transparent
        Me.Label26.Location = New System.Drawing.Point(6, 66)
        Me.Label26.Name = "Label26"
        Me.Label26.Size = New System.Drawing.Size(79, 13)
        Me.Label26.TabIndex = 8
        Me.Label26.Text = "Désignation"
        Me.Label26.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tSupprimer
        '
        Me.tSupprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tSupprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler2
        Me.tSupprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.tSupprimer.Location = New System.Drawing.Point(479, 74)
        Me.tSupprimer.Name = "tSupprimer"
        Me.tSupprimer.Size = New System.Drawing.Size(102, 51)
        Me.tSupprimer.TabIndex = 2
        Me.tSupprimer.Text = "Annuler"
        Me.tSupprimer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.tSupprimer.UseVisualStyleBackColor = True
        Me.tSupprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label24
        '
        Me.Label24.AutoSize = True
        Me.Label24.BackColor = System.Drawing.Color.Transparent
        Me.Label24.Location = New System.Drawing.Point(12, 116)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(68, 13)
        Me.Label24.TabIndex = 79
        Me.Label24.Text = "Liste des lots"
        '
        'bAjouter
        '
        Me.bAjouter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouter.Image = Global.Pharma2000Premium.My.Resources.Resources.nouveau
        Me.bAjouter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouter.Location = New System.Drawing.Point(479, 17)
        Me.bAjouter.Name = "bAjouter"
        Me.bAjouter.Size = New System.Drawing.Size(102, 51)
        Me.bAjouter.TabIndex = 2
        Me.bAjouter.Text = "Confirmer"
        Me.bAjouter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouter.UseVisualStyleBackColor = True
        Me.bAjouter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gLots
        '
        Me.gLots.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gLots.GroupByCaption = "Drag a column header here to group by that column"
        Me.gLots.Images.Add(CType(resources.GetObject("gLots.Images"), System.Drawing.Image))
        Me.gLots.Location = New System.Drawing.Point(12, 132)
        Me.gLots.Name = "gLots"
        Me.gLots.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gLots.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gLots.PreviewInfo.ZoomFactor = 75
        Me.gLots.PrintInfo.PageSettings = CType(resources.GetObject("gLots.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gLots.Size = New System.Drawing.Size(420, 242)
        Me.gLots.TabIndex = 0
        Me.gLots.Text = "C1TrueDBGrid4"
        Me.gLots.PropBag = resources.GetString("gLots.PropBag")
        '
        'fCorrectionDesLots
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(902, 475)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fCorrectionDesLots"
        Me.Text = "fCorrectionDesLots"
        Me.Panel.ResumeLayout(False)
        Me.Panel.PerformLayout()
        Me.GroupBox8.ResumeLayout(False)
        Me.GroupBox8.PerformLayout()
        CType(Me.tCodePCT, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodeArticle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbDesignation, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gLots, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents tSupprimer As C1.Win.C1Input.C1Button
    Friend WithEvents Label24 As System.Windows.Forms.Label
    Friend WithEvents bAjouter As C1.Win.C1Input.C1Button
    Friend WithEvents gLots As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents GroupBox8 As System.Windows.Forms.GroupBox
    Friend WithEvents Label26 As System.Windows.Forms.Label
    Friend WithEvents cmbDesignation As C1.Win.C1List.C1Combo
    Friend WithEvents tCodePCT As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label25 As System.Windows.Forms.Label
    Friend WithEvents tCodeArticle As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
End Class
