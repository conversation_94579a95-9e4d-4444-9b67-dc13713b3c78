﻿Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fPreparation
    Dim cmdChargement As New SqlCommand
    Dim dsChargement As New DataSet
    Dim daChargement As New SqlDataAdapter
    Dim daChargementEntete As New SqlDataAdapter
    Dim cbChargementEntete As New SqlCommandBuilder
    Dim daChargementDetails As New SqlDataAdapter
    Dim cbChargementDetails As New SqlCommandBuilder
    Dim daChargementIndemnite As New SqlDataAdapter
    Dim cbChargementIndemnite As New SqlCommandBuilder

    Dim cmdPreparation As New SqlCommand
    Dim cbPreparation As New SqlCommandBuilder
    Public dsPreparation As New DataSet
    Dim daPreparation As New SqlDataAdapter

    Public Shared CodePreparation As String = "0"
    Public Shared DesignationPreparation As String = ""
    Public Shared TypePreparation As Integer = 0

    Dim StrSQL As String = ""

    Public NouvelArticle As DataRow = Nothing
    Public NouvellePreparation As DataRow = Nothing
    Public NouvelleIndemnite As DataRow = Nothing
    Public dr As DataRow = Nothing
    Public dr1 As DataRow = Nothing

    Dim TotalTTCArticle As Double = 0.0
    Dim TotalTTCIndemnite As Double = 0.0

    Public Shared TotalTTCPreparation As Double = 0.0

    Public Shared Confirme As Boolean = False

    Dim CodeArticle As Int64 = 0
    Public Shared CodeABarre As String = ""

    Public mode As String = ""

    Dim dsRecupereNum As New DataSet
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNumt As New SqlDataAdapter

    Dim CodeExiste As Boolean = False
    Dim gridselectionnee As String = ""
    Public Shared pCodePreparation As String

    Public Sub init()
        '***************************************************************************************************
        '****************************** Initialisation des articles ****************************************
        '***************************************************************************************************
        Dim I As Integer = 0
        Dim cmdRecupereNum As New SqlCommand

        Dim cmdNbreJourValidite As New SqlCommand
        Dim NbreJourValiditeParDefaut As String
        Try

            If (dsChargement.Tables.IndexOf("FORMULE_PREPARATION") > -1) Then
                dsChargement.Tables("FORMULE_PREPARATION").Clear()
            End If
            If (dsChargement.Tables.IndexOf("FORMULE_PREPARATION_DETAILS") > -1) Then
                dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Clear()
            End If
            If (dsChargement.Tables.IndexOf("INDEMNITE_PREPARATION") > -1) Then
                dsChargement.Tables("INDEMNITE_PREPARATION").Clear()
            End If
            If (dsChargement.Tables.IndexOf("INDEMNITE") > -1) Then
                dsChargement.Tables("INDEMNITE").Clear()
            End If

            tDesignation.CharacterCasing = CharacterCasing.Upper
            tCodeArticle.CharacterCasing = CharacterCasing.Upper

            If mode = "A" Then
                tCodeArticle.Enabled = True
                tDesignation.Enabled = True
            Else
                tCodeArticle.Enabled = False
                tDesignation.Enabled = False
            End If

            StrSQL = " SELECT MAX(CASE WHEN ISnumeric(CodeArticle)=1 THEN CONVERT (NUMERIC(18, 0) , CodeArticle ) ELSE 0 END) FROM [ARTICLE] WHERE CASE WHEN ISnumeric(CodeArticle)=1 THEN CONVERT (NUMERIC(18, 0) , CodeArticle ) ELSE 0 END<99999999999"
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQL


            Try
                CodeArticle = cmdRecupereNum.ExecuteScalar() + 1
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

            If mode = "A" Then
                CodePreparation = CodeArticle

                'pour remplir le nbre de jour de validité par défaut
                Try
                    StrSQL = "SELECT NbreJourValiditeParDefaut FROM PARAMETRE_PHARMACIE"
                    cmdNbreJourValidite.Connection = ConnectionServeur
                    cmdNbreJourValidite.CommandText = StrSQL
                    NbreJourValiditeParDefaut = cmdNbreJourValidite.ExecuteScalar()

                    tNbreJourValidite.Text = NbreJourValiditeParDefaut
                Catch ex As Exception
                    MsgBox("Vous devez aller sur [Paramètres Généreaux/Préparation] pour remplir les paramètres")
                    tNbreJourValidite.Text = "365"
                End Try

            End If


            'chargement des Entêtes de la préparation        
            StrSQL = "SELECT * FROM FORMULE_PREPARATION WHERE CodePreparation ='" + CodePreparation + "'" + _
                     " AND CodeTypePreparation ='" + TypePreparation.ToString + "'"

            cmdChargement.Connection = ConnectionServeur
            cmdChargement.CommandText = StrSQL
            daChargementEntete = New SqlDataAdapter(cmdChargement)
            daChargementEntete.Fill(dsChargement, "FORMULE_PREPARATION")
            cbChargementEntete = New SqlCommandBuilder(daChargementEntete)

            If dsChargement.Tables("FORMULE_PREPARATION").Rows.Count > 0 Then

                'tCodeArticle.Value = RecupererValeurExecuteScalaire("CodeABarre", "ARTICLE", "CodeArticle", dsChargement.Tables("FORMULE_PREPARATION").Rows(0)("CodePreparation"))
                '    tDesignation.Value = dsChargement.Tables("FORMULE_PREPARATION").Rows(0)("Designation")
                tCodeArticle.Value = CodeABarre
                tDesignation.Value = DesignationPreparation
                lDate.Text = dsChargement.Tables("FORMULE_PREPARATION").Rows(0)("date")

                lType.Text = RecupererValeurExecuteScalaire("LibelleTypePreparation", "TYPE_PREPARATION", "CodeTypePreparation", dsChargement.Tables("FORMULE_PREPARATION").Rows(0)("CodeTypePreparation"))
                tNumeroOrdonnance.Value = dsChargement.Tables("FORMULE_PREPARATION").Rows(0)("NumeroOrdonnace")

                tTotTTC.Value = dsChargement.Tables("FORMULE_PREPARATION").Rows(0)("TotalTTC")
                lTotArticle.Text = dsChargement.Tables("FORMULE_PREPARATION").Rows(0)("TotalArticle")
                lTotIndemnite.Text = dsChargement.Tables("FORMULE_PREPARATION").Rows(0)("TotalIndemnite")
                tNbreJourValidite.Value = dsChargement.Tables("FORMULE_PREPARATION").Rows(0)("NombreJourValidite").ToString
                tQuantiteUnitaire.Value = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", dsChargement.Tables("FORMULE_PREPARATION").Rows(0)("CodePreparation"))
            Else
                tCodeArticle.Value = CodeABarre
                tDesignation.Value = DesignationPreparation
                lDate.Text = System.DateTime.Today
                tQuantiteUnitaire.Value = 1

                lType.Text = RecupererValeurExecuteScalaire("LibelleTypePreparation", "TYPE_PREPARATION", "CodeTypePreparation", TypePreparation)
                tNumeroOrdonnance.Value = ""

                tTotTTC.Value = "0.000"
                lTotArticle.Text = "0.000"
                lTotIndemnite.Text = "0.000"

            End If

            If mode = "A" And TypePreparation = 3 Then
                StrSQL = " SELECT MAX(CONVERT(int, (SUBSTRING(CodeABarre, 3, LEN(CodeABarre)-2)))) FROM [ARTICLE] WHERE CodeABarre LIKE 'IP%' AND ISNUMERIC(SUBSTRING(CodeABarre, 3, LEN(CodeABarre)-2)) = 1"
                cmdRecupereNum.Connection = ConnectionServeur
                cmdRecupereNum.CommandText = StrSQL

                Try
                    tCodeArticle.Value = "IP" + (cmdRecupereNum.ExecuteScalar() + 1).ToString
                    tDesignation.Value = "PREPARATION MAGISTRALE IP" + (cmdRecupereNum.ExecuteScalar() + 1).ToString
                Catch ex As Exception
                    Console.WriteLine(ex.Message)

                End Try
            End If

            'chargement des Détails de la préparation 
            If dsChargement.Tables("FORMULE_PREPARATION").Rows.Count > 0 Then
                StrSQL = " SELECT CodePreparation," + _
                         " CodeArticle," + _
                         " CodeABarre," + _
                         " Designation," + _
                         " LibelleForme," + _
                         " convert(varchar,Qte) as Qte,stock," + _
                         " PrixVenteTTC," + _
                         " TotTTC," + _
                         " FORMULE_PREPARATION_DETAILS.CodeForme " + _
                         " FROM FORMULE_PREPARATION_DETAILS LEFT OUTER JOIN FORME_ARTICLE ON " + _
                         " FORMULE_PREPARATION_DETAILS.CodeForme=FORME_ARTICLE.CodeForme" + _
                         " WHERE CodePreparation='" + CodePreparation + "'"
            Else
                StrSQL = " SELECT TOP(0) CodePreparation,CodeArticle,CodeABarre,Designation,LibelleForme," + _
                         " convert(varchar,Qte) as Qte,stock,PrixVenteTTC,TotTTC," + _
                         " FORMULE_PREPARATION_DETAILS.CodeForme " + _
                         " FROM FORMULE_PREPARATION_DETAILS LEFT OUTER JOIN FORME_ARTICLE ON " + _
                         " FORMULE_PREPARATION_DETAILS.CodeForme=FORME_ARTICLE.CodeForme" + _
                         " WHERE CodePreparation=0"
            End If


            cmdChargement.Connection = ConnectionServeur
            cmdChargement.CommandText = StrSQL
            daChargementDetails = New SqlDataAdapter(cmdChargement)
            daChargementDetails.Fill(dsChargement, "FORMULE_PREPARATION_DETAILS")
            cbChargementDetails = New SqlCommandBuilder(daChargementDetails)

            With gArticles
                .Columns.Clear()
                Try
                    .DataSource = dsChargement
                Catch ex As Exception
                End Try
                .DataMember = "FORMULE_PREPARATION_DETAILS"
                .Rebind(False)
                .Columns("CodeABarre").Caption = "Code"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Qte").Caption = "Qte"
                .Columns("PrixVenteTTC").Caption = "Prix"
                .Columns("TotTTC").Caption = "TotTTC"
                .Columns("stock").Caption = "stock"

                ' Centrer toutes les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Locked = True
                Next

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

                .Splits(0).DisplayColumns("CodeABarre").Locked = False
                .Splits(0).DisplayColumns("Designation").Locked = False
                .Splits(0).DisplayColumns("Qte").Locked = False
                .Splits(0).DisplayColumns("Stock").Visible = False

                .Splits(0).DisplayColumns("CodePreparation").Width = 0
                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("CodePreparation").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").Visible = False

                .Splits(0).DisplayColumns("CodeABarre").Width = 120 '50
                .Splits(0).DisplayColumns("Designation").Width = 310 '180
                .Splits(0).DisplayColumns("LibelleForme").Width = 80 '50
                .Splits(0).DisplayColumns("Qte").Width = 50
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 100
                .Splits(0).DisplayColumns("TotTTC").Width = 50
                .Splits(0).DisplayColumns("stock").Width = 50
                .Splits(0).DisplayColumns("CodeForme").Width = 0
                .Splits(0).DisplayColumns("CodeForme").Visible = False

                .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("Qte").Style.BackColor = Color.FromArgb(255, 244, 252, 251)


                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                .AllowSort = False
                .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
                'Style du Caractere et du grid
                ParametreGrid(gArticles)
            End With

            Me.gArticles.Splits(0).DisplayColumns("CodePreparation").AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns("LibelleForme").AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns("PrixVenteTTC").AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns("TotTTC").AllowFocus = False
            Me.gArticles.Splits(0).DisplayColumns("Stock").AllowFocus = False


            '---------------------- initialisation d'une ligne dans la table 
            'ajout d'un nouvel enregistrement vide dans les datatables convenables
            NouvelArticle = dsChargement.Tables("FORMULE_PREPARATION_DETAILS").NewRow()
            NouvelArticle("Designation") = ""
            NouvelArticle("CodeArticle") = ""
            NouvelArticle("CodeABarre") = ""
            'NouvelArticle("PrixVenteTTC") = 0.0
            dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Add(NouvelArticle)

            '--------------------initialisation de la datatable article qui est utilisé dans la liste de 
            '--------------------recherche alimenté selon les entrées de l'utilisateur dans la colonne designation
            StrSQL = " SELECT CodeArticle," + _
                     " QuantiteUnitaire, " + _
                     " Designation," + _
                     " LibelleForme," + _
                     " PrixVenteTTC" + _
                     " FROM ARTICLE,FORME_ARTICLE " + _
                     " WHERE ARTICLE.CodeForme=FORME_ARTICLE.CodeForme and " + _
                     " Designation LIKE " + Quote(gArticles.Columns("Designation").Value) + " ORDER BY Designation"

            cmdChargement.Connection = ConnectionServeur
            cmdChargement.CommandText = StrSQL
            daChargement = New SqlDataAdapter(cmdChargement)
            daChargement.Fill(dsChargement, "ARTICLE")

            With gListeRecherche
                .Columns.Clear()
                .DataSource = dsChargement
                .DataMember = "ARTICLE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code Article"
                .Columns("Designation").Caption = "Designation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("PrixVenteTTC").Caption = "Prix de vente"

                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centre tous les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Visible = False
                Next

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

                '.Splits(0).DisplayColumns("CodeArticle").Visible = True
                .Splits(0).DisplayColumns("Designation").Visible = True
                .Splits(0).DisplayColumns("LibelleForme").Visible = True
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

                .Splits(0).DisplayColumns("Designation").Width = 340 '240
                .Splits(0).DisplayColumns("LibelleForme").Width = 70
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 70

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 20
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                'Style du Caractere et du grid
                ParametreGrid(gListeRecherche)
            End With
            gArticles.Refresh()
            '***************************************************************************************************
            '***************************************************************************************************
            '***************************************************************************************************

            '***************************************************************************************************
            '****************************** Initialisation des Indemnités **************************************
            '***************************************************************************************************
            StrSQL = "SELECT CodePreparation,INDEMNITE_PREPARATION.CodeIndemnite,LibelleIndemnite,Prix " + _
                     " FROM INDEMNITE_PREPARATION,INDEMNITE " + _
                     " WHERE INDEMNITE.CodeIndemnite=INDEMNITE_PREPARATION.CodeIndemnite" + _
                     " AND INDEMNITE_PREPARATION.CodePreparation = " + Quote(CodePreparation)

            cmdChargement.Connection = ConnectionServeur
            cmdChargement.CommandText = StrSQL
            daChargementIndemnite = New SqlDataAdapter(cmdChargement)
            daChargementIndemnite.Fill(dsChargement, "INDEMNITE_PREPARATION")
            cbChargementIndemnite = New SqlCommandBuilder(daChargementIndemnite)

            With gIndemnites
                .Columns.Clear()
                Try
                    .DataSource = dsChargement
                Catch ex As Exception
                End Try
                .DataMember = "INDEMNITE_PREPARATION"
                .Rebind(False)
                .Columns("CodeIndemnite").Caption = "Code"
                .Columns("LibelleIndemnite").Caption = "Désignation"
                .Columns("Prix").Caption = "Prix"

                ' Centrer toutes les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                .Splits(0).DisplayColumns(2).Style.HorizontalAlignment = AlignHorzEnum.Near
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Locked = True
                Next
                .Splits(0).DisplayColumns("CodeIndemnite").Locked = False
                .Splits(0).DisplayColumns("LibelleIndemnite").Locked = False
                .Splits(0).DisplayColumns("CodePreparation").Locked = False

                .Splits(0).DisplayColumns("CodePreparation").Width = 0
                .Splits(0).DisplayColumns("CodePreparation").Visible = False
                .Splits(0).DisplayColumns("CodeIndemnite").Width = 110
                .Splits(0).DisplayColumns("LibelleIndemnite").Width = 290
                .Splits(0).DisplayColumns("Prix").Width = 60

                .Splits(0).DisplayColumns("LibelleIndemnite").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                .AllowSort = False
                .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
                'Style du Caractere et du grid
                ParametreGrid(gIndemnites)
            End With

            Me.gIndemnites.Splits(0).DisplayColumns("Prix").AllowFocus = False

            '---------------------- initialisation d'une ligne dans la table 
            'ajout d'un nouvel enregistrement vide dans les datatables convenables
            NouvelleIndemnite = dsChargement.Tables("INDEMNITE_PREPARATION").NewRow()
            NouvelleIndemnite("CodeIndemnite") = ""
            NouvelleIndemnite("LibelleIndemnite") = ""
            dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Add(NouvelleIndemnite)

            '--------------------initialisation de la datatable Indemnite qui est utilisé dans la liste de 
            '--------------------recherche alimenté selon les entrés de l utilisateur dans la colonne designation
            StrSQL = "SELECT CodeIndemnite," + _
                     "LibelleIndemnite," + _
                     "Prix " + _
                     " FROM INDEMNITE " + _
                     "WHERE " + _
                     "LibelleIndemnite LIKE " + Quote(gIndemnites.Columns("LibelleIndemnite").Value) + " ORDER BY LibelleIndemnite"

            cmdChargement.Connection = ConnectionServeur
            cmdChargement.CommandText = StrSQL
            daChargement = New SqlDataAdapter(cmdChargement)
            daChargement.Fill(dsChargement, "INDEMNITE")

            With gListeRecherche1
                .Columns.Clear()
                .DataSource = dsChargement
                .DataMember = "INDEMNITE"
                .Rebind(False)
                .Columns("CodeIndemnite").Caption = "Code Indemnité"
                .Columns("LibelleIndemnite").Caption = "Libélle"
                .Columns("Prix").Caption = "Prix"

                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centre tous les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Visible = False
                Next

                .Splits(0).DisplayColumns("CodeIndemnite").Visible = True
                .Splits(0).DisplayColumns("LibelleIndemnite").Visible = True
                .Splits(0).DisplayColumns("Prix").Visible = True

                .Splits(0).DisplayColumns("CodeIndemnite").Width = 0
                .Splits(0).DisplayColumns("LibelleIndemnite").Width = 300 '170
                .Splits(0).DisplayColumns("Prix").Width = 50

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 20
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                'Style du Caractere et du grid
                ParametreGrid(gListeRecherche1)
            End With
            gIndemnites.Refresh()

            If lType.Text <> "MAGISTRALE" Then
                bProduire.Enabled = False
            ElseIf lType.Text = "MAGISTRALE" Then
                tQuantiteUnitaire.Enabled = False
                tQuantiteUnitaire.Value = 1
            End If

            If tCodeArticle.Enabled = True Then
                tCodeArticle.Focus()
            Else
                gArticles.Focus()
                gArticles.Col = 1
            End If

            '***************************************************************************************************
            '***************************************************************************************************
            '***************************************************************************************************
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " init", ex.Message, "0000338", "Erreur d'exécution de init ", True, True, True)

        End Try
    End Sub

    Private Sub gArticles_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gArticles.Change
        Try


            If (gArticles.Col = 3 And gArticles.Columns("Designation").Value.ToString <> "") Or gArticles.Col = 2 Then
                Dim StrSQL1 As String = ""
                Dim I As Integer
                Dim NbLigne As Integer
                NbLigne = gArticles.RowCount

                With gListeRecherche
                    .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
                    .Top = Me.gArticles.Top + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight
                End With

                Try
                    dsChargement.Tables("ARTICLE").Clear()
                Catch ex As Exception

                End Try

                If gArticles(gArticles.Row + 1, "CodeArticle") = "" And gArticles(gArticles.Row, "CodeArticle") = "" And gArticles.Col = 3 Then
                    'If gArticles.Col = 3 Then 'gArticles.Row = dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1
                    gListeRecherche.Visible = True
                Else
                    gListeRecherche.Visible = False
                End If

                'chargement des articles qui sont mis en jeu
                If gArticles.Col = 3 Then
                    If gArticles.Columns("Designation").Value.ToString.Length > 1 Then
                        If gArticles.Columns("Designation").Value.ToString.IndexOf(".") > 0 Then   'gArticles.Columns("Designation").Value.ToString.Substring(gArticles.Columns("Designation").Value.ToString.Length - 1, 1) Like "." And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1))

                            StrSQL1 = "SELECT CodeArticle," + _
                                      "Designation," + _
                                      "LibelleForme," + _
                                      "PrixVenteTTC" + _
                                      " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON " + _
                                      " ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                      " WHERE ltrim(str(PrixVenteTTC,10,3)) LIKE " + _
                                      Quote(gArticles.Columns("Designation").Value + "%") + " AND Supprime=0 ORDER BY PrixVenteTTC"
                        Else
                            StrSQL1 = "SELECT CodeArticle," + _
                                      "Designation," + _
                                      "LibelleForme," + _
                                      "PrixVenteTTC" + _
                                      " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON " + _
                                      " ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                      " WHERE Designation LIKE " + Quote(gArticles.Columns("Designation").Value + "%") + _
                                      " AND Supprime=0 ORDER BY Designation"
                        End If
                    Else
                        StrSQL1 = "SELECT CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC" + _
                                  " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON " + _
                                  " ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  " WHERE Designation LIKE " + Quote(gArticles.Columns("Designation").Value + "%") + _
                                  " AND Supprime=0 ORDER BY Designation"
                    End If
                ElseIf gArticles.Col = 2 Then
                    StrSQL1 = "SELECT CodeArticle," + _
                              "Designation," + _
                              "LibelleForme," + _
                              "PrixVenteTTC" + _
                              " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON " + _
                              " ARTICLE.CodeForme=FORME_ARTICLE.CodeForme WHERE  " + _
                              "CodeABarre LIKE " + Quote(gArticles.Columns("CodeABarre").Value) + _
                              " AND Supprime=0 ORDER BY Designation"

                End If
                cmdChargement.Connection = ConnectionServeur
                cmdChargement.CommandText = StrSQL1
                daChargement = New SqlDataAdapter(cmdChargement)
                daChargement.Fill(dsChargement, "ARTICLE")

                If dsChargement.Tables("ARTICLE").Rows.Count > 0 Then
                    dr = dsChargement.Tables("ARTICLE").Rows(0)
                End If

                With gListeRecherche
                    .Columns.Clear()
                    .DataSource = dsChargement
                    .DataMember = "ARTICLE"
                    .Rebind(False)
                    .Columns("CodeArticle").Caption = "Code Article"
                    .Columns("Designation").Caption = "Designation"
                    .Columns("LibelleForme").Caption = "Forme"
                    .Columns("PrixVenteTTC").Caption = "Prix de vente"

                    ' Centrer tous les entêtes
                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    Next
                    ' CentreR tous les valeurs
                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
                    Next

                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).Visible = False
                    Next

                    .Splits(0).DisplayColumns("CodeArticle").Visible = True
                    .Splits(0).DisplayColumns("Designation").Visible = True
                    .Splits(0).DisplayColumns("LibelleForme").Visible = True
                    .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

                    .Splits(0).DisplayColumns("CodeArticle").Width = 0
                    .Splits(0).DisplayColumns("CodeArticle").Visible = False
                    .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

                    .Splits(0).DisplayColumns("Designation").Width = 300 '200
                    .Splits(0).DisplayColumns("LibelleForme").Width = 70
                    .Splits(0).DisplayColumns("PrixVenteTTC").Width = 70

                    .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.Aqua
                    .Splits(0).DisplayColumns("LibelleForme").Style.BackColor = Color.Aqua
                    .Splits(0).DisplayColumns("PrixVenteTTC").Style.BackColor = Color.Aqua

                    .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                    .Splits(0).ColumnCaptionHeight = 20
                    .Splits(0).RecordSelectors = False
                    .ExtendRightColumn = True
                    .EmptyRows = True
                    .FetchRowStyles = True
                End With
                Dim Col As New C1.Win.C1TrueDBGrid.C1DataColumn
                Dim dc As C1.Win.C1TrueDBGrid.C1DisplayColumn

                With gListeRecherche
                    .Columns.Insert(0, Col)
                    Col.Caption = "Stock"
                    dc = .Splits(0).DisplayColumns.Item("Stock")
                    dc.Width = 40
                    .Splits(0).DisplayColumns(4).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    .Splits(0).DisplayColumns(4).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    .Splits(0).DisplayColumns("Stock").Style.BackColor = Color.Aqua
                    dc.Visible = True
                    .Rebind(True)
                End With
            End If
            gridselectionnee = "gArticle"

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " gArticles_Change", ex.Message, "0000339", "Erreur d'exécution de gArticles_Change ", True, True, True)

        End Try

    End Sub

    Private Sub gArticles_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyUp
        Dim i As Integer = 0
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim QuantiteAAjouter As Integer = 0

        Try


            If e.KeyCode = Keys.F3 Then
                bConfirmer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F8 And bAnnuler.Enabled = True Then
                bProduire_Click(sender, e)
            ElseIf e.KeyCode = Keys.F10 Then
                bAnnuler_Click(sender, e)
            ElseIf e.KeyCode = Keys.F12 Then
                bFermer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F7 Then
                bSupprimer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F5 Then
                bIndemnites_Click(sender, e)
            End If

            If e.KeyCode = Keys.F1 And gArticles.Columns("CodeArticle").Value <> "" Then
                AfficherFicheArticle(gArticles.Columns("CodeArticle").Value, gArticles.Columns("Stock").Value, gArticles.Columns("Designation").Value)
                Exit Sub
            End If

            '---------------------------------- suppression du dernier ligne vide si on a deux lignes au mm temps vide
            '---------------------------------- cas ou on supprime dernier ligne

            'If dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1 = 1 And gArticles(0, "CodeArticle") = "" And gArticles(1, "CodeArticle") = "" Then
            '    gArticles.MoveLast()
            '    gArticles.MovePrevious()
            '    gArticles.Delete()
            'End If

            '---------------------------------- test du type de la valeur d'entrée dans la colonne quantité (numéric) ------------
            '---------------------------------- test du  valeur d'entrée dans la colonne quantité < 99999 ------------



            If gArticles.Col = 5 Then
                If gArticles.Columns("Qte").Value.ToString <> "-" Then   ' pour les quantité négatives
                    If IsNumeric(gArticles.Columns("Qte").Value) = False Then
                        gArticles.Columns("Qte").Value = ""
                        gArticles.Col = 5
                        gArticles.EditActive = True
                        Exit Sub
                    End If
                    If gArticles.Columns("Qte").Value.ToString = "" Then
                        gArticles.Columns("Qte").Value = "0"
                    End If
                    If (gArticles.Columns("Qte").Value = 0) And e.KeyCode = Keys.Enter Then
                        gArticles.Columns("Qte").Value = ""
                        gArticles.Col = 5
                        gArticles.EditActive = True
                        Exit Sub
                    End If
                    If (gArticles.Columns("Qte").Value > 99999 Or gArticles.Columns("Qte").Value < -99999) And e.KeyCode = Keys.Enter Then
                        gArticles.Columns("Qte").Value = "1"
                        MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                        gArticles.Col = 5
                        gArticles.EditActive = True
                        Exit Sub
                    End If
                End If

            End If

            '---------------------------------- verouillage des lignes déja confirmées -------------------------
            If (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) And gArticles.Row <> dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1 Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
                gArticles.Splits(0).DisplayColumns("Designation").Locked = True
            ElseIf gArticles.Row = dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1 Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
            End If

            If gArticles.Col = 2 And gArticles.Columns("Designation").Value.ToString <> "" Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
                gArticles.Splits(0).DisplayColumns("Designation").Locked = True

            End If
            If gArticles.Col = 3 And gArticles.Columns("CodeArticle").Value.ToString <> "" Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
                gArticles.Splits(0).DisplayColumns("Designation").Locked = True

            End If

            '---------------------------------- suppression de la ligne selectionnées -------------------------
            'If (e.KeyCode = Keys.F7) And gArticles.Row <= dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1 Then
            '    Try
            '    gArticles.Delete()
            '    CalculerMontants()
            '    gArticles.Focus()
            '    gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeArticle"))
            '        gArticles.EditActive = True
            '        Exit Sub
            '    Catch ex As Exception
            '        Exit Sub
            '    End Try
            'End If
            '------------------------------ recherche par code ----------------------------------------------
            If gArticles.Col = 2 And e.KeyCode = Keys.Enter And gArticles.Row = dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1 Then
                ChargerDetailArticle(gArticles.Columns("CodeABarre").Value.ToString)
                Exit Sub
            ElseIf gArticles.Col = 2 And e.KeyCode = Keys.Enter And gArticles.Row < dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1 Then
                gArticles.Col = 3
            End If
            '---------------------------------- masquer la liste de recherche si la designation est vide -----------
            If e.KeyCode = Keys.Back And gArticles.Columns("Designation").Value.ToString = "" And gArticles.Col = 3 Then
                gListeRecherche.Visible = False
            End If
            '---------------------------- pour passer à la navigation dans la petite liste de recherhce ----------------
            If (e.KeyCode = Keys.Down Or e.KeyCode = Keys.Up) And gListeRecherche.Visible = True Then
                gListeRecherche.Focus()
                gListeRecherche.Col = 2
                gListeRecherche.Row = 1
            End If
            '---------------------------- si l'artcile n'existe pas dans la liste de recherhce sa quantité doit etre 0  ----
            If dsChargement.Tables("ARTICLE").Rows.Count <= 0 And gListeRecherche.Visible = True Then '
                gArticles.Columns("Qte").Value = 0
                gArticles.Col = 3
            End If
            '---------------------------- calcul des montants --------------------------------------------------------
            If (gArticles.Col = 5 Or gArticles.Col = 11) And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) Then
                CalculerMontants()
            End If
            'If gArticles.Col = 10 And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) Then
            '    CalculerMontants()
            'End If

            '--------------------- traitement du clique sur le bouton ENTREE selon la colonne ---------------------------

            If e.KeyCode = Keys.Enter Then ' And (dsChargement.Tables("ARTICLE").Rows.Count > 0 Or gArticles.Row <> dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1) Then
                gListeRecherche_KeyUp(sender, e)     ' pour charger les informations de l'article a partir du fiche article

                If gArticles.Col = 3 Then
                    If gArticles.Columns("CodeArticle").Value = "" Then    ' si l'article choisit n'existe pas dans la liste de recherche
                        gArticles.Col = 3
                        gArticles.Columns("Designation").Value = ""
                    Else
                        gArticles.Col = 5
                    End If

                ElseIf gArticles.Col = 5 And e.KeyCode = Keys.Enter Then
                    ' si l'utilisateur a choisit un article on ajoute un nouvel enregistrement dans la datatable puis on passe à la ligne
                    ' suivant dans la gride avec un test si cet article est déja choisi ou non si c'est le cas on ajoute seulement la quntité

                    If gArticles.Columns("Designation").Value <> "" Then
                        QuantiteAAjouter = gArticles.Columns("Qte").Value
                        Dim ArticleCourant As String = ""
                        ArticleCourant = gArticles.Columns("codearticle").Value

                        If gArticles.RowCount >= 1 And gArticles.Row = dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1 Then
                            Do While i < gArticles.RowCount - 1
                                If gArticles(i, "CodeArticle") = ArticleCourant Then
                                    gArticles(i, "Qte") = (Convert.ToInt32(gArticles(i, "Qte")) + QuantiteAAjouter).ToString
                                    gArticles.MoveLast()
                                    gArticles.Delete()
                                End If
                                i = i + 1
                            Loop
                        End If
                    End If

                    If gArticles(gArticles.RowCount - 1, ("CodeArticle")) <> "" Then
                        NouvelArticle = dsChargement.Tables("FORMULE_PREPARATION_DETAILS").NewRow()
                        NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                        NouvelArticle("CodeArticle") = ""
                        NouvelArticle("CodeABarre") = ""
                        dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Add(NouvelArticle)
                        gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
                        gArticles.Splits(0).DisplayColumns("Designation").Locked = False
                    End If
                    gArticles.MoveLast()
                    Try
                        dsChargement.Tables("ARTICLE").Clear()
                    Catch ex As Exception
                    End Try
                    gArticles.Col = 2

                End If
            End If

            gridselectionnee = "gArticle"

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " gArticles_KeyUp", ex.Message, "0000340", "Erreur d'exécution de gArticles_KeyUp ", True, True, True)

        End Try
    End Sub

    Private Sub gListeRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListeRecherche.KeyUp
        ' dans cet évènenment on teste si l évènenment est due à un clique sur le boutton entrée et si l'utilisateur a choisi 
        ' un article et si le curseur est dans la colonne de designation 
        Try

            Dim StrSQL As String = ""
            Dim cmd As New SqlCommand

            Dim QteUnitaireArticle As Integer = 0
            Dim CategorieArticle As Integer = 0
            Dim PreparationArticle As Integer = 0

            If gListeRecherche.Visible = False Then
                Exit Sub
            End If
            If e.KeyCode = Keys.Back Then
                gArticles.Focus()
                gArticles.Col = 2
                gArticles.MoveLast()
                gArticles.EditActive = True
            End If

            Dim j As Integer
            Dim NumeroLigne As Integer
            Dim DataRowRecherche As DataRow
            If e.KeyCode = Keys.Enter And (gArticles.Col = 5 Or gArticles.Col = 3) Then    'And gArticles.Columns("Designation").Value <> ""
                If dsChargement.Tables("ARTICLE").Rows.Count > 0 Then
                    '--------------------- rehcerche de l'index de la ligne qui contient l'article recherché ---------------
                    For j = 0 To dsChargement.Tables("ARTICLE").Rows.Count - 1
                        DataRowRecherche = dsChargement.Tables("ARTICLE").Rows(j)
                        If DataRowRecherche.Item("CodeArticle") = gListeRecherche.Columns("CodeArticle").Value Then
                            NumeroLigne = j
                        End If
                    Next
                    '--------------- test si produit chimique ou prep pharmaceutique
                    CategorieArticle = IIf(RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", dr.Item("CodeArticle")) Is Nothing, 0, RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", dr.Item("CodeArticle")))
                    If CategorieArticle = 9 Then
                        Try
                            PreparationArticle = RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                        Catch ex As Exception
                            PreparationArticle = 0
                        End Try
                    End If

                    '' ''If CategorieArticle = 8 Or (CategorieArticle = 9 And PreparationArticle = 1) Then
                    '' ''    QteUnitaireArticle = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    '' ''Else
                    '' ''    QteUnitaireArticle = 1
                    '' ''End If

                    QteUnitaireArticle = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    If QteUnitaireArticle = 0 Then QteUnitaireArticle = 1

                    '------------------- chargement des données ---------------------------------------------- 
                    dr = dsChargement.Tables("ARTICLE").Rows(NumeroLigne)
                    NouvelArticle("CodePreparation") = CodePreparation
                    NouvelArticle("CodeArticle") = dr.Item("CodeArticle")
                    NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    If RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle")) <> "" Then
                        NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    End If
                    NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle")))
                    NouvelArticle("Qte") = 0

                    NouvelArticle("Stock") = CalculeStock(dr.Item("CodeArticle"))
                    NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                    NouvelArticle("TotTTC") = 0 'NouvelArticle("PrixVenteTTC") * NouvelArticle("Qte") / QteUnitaireArticle

                    gArticles.Refresh()
                End If
                gListeRecherche.Visible = False
                gArticles.Focus()
                If NumeroLigne = 0 Then
                    gArticles.Col = 3
                Else
                    gArticles.Col = 5
                End If
                If gArticles.Row = gArticles.RowCount And NumeroLigne <> 0 Then
                    gArticles.Row = gArticles.Row - 1
                End If
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " gListeRecherche_KeyUp", ex.Message, "0000341", "Erreur d'exécution de gListeRecherche_KeyUp ", True, True, True)

        End Try
    End Sub

    Public Sub ChargerDetailArticle(ByVal CodeABarre As String)
        Dim resultat As String
        Dim Supprime As String = ""
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand

        Dim CodeArticle As String = ""

        Dim QteUnitaireArticle As Integer = 0
        Dim CategorieArticle As Integer = 0
        Dim PreparationArticle As Integer = 0
        Try

            CodeArticle = RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "CodeABarre", CodeABarre)
            resultat = RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle)
            Supprime = RecupererValeurExecuteScalaire("Supprime", "ARTICLE", "CodeArticle", CodeArticle)

            If resultat <> "" And Supprime = "False" Then

                CategorieArticle = RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", CodeArticle)
                If CategorieArticle = 9 Then
                    Try
                        PreparationArticle = RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", CodeArticle)
                    Catch ex As Exception
                        PreparationArticle = 0
                    End Try
                End If
                '' ''If CategorieArticle = 8 Or (CategorieArticle = 9 And PreparationArticle = 1) Then
                '' ''    QteUnitaireArticle = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticle)
                '' ''Else
                '' ''    QteUnitaireArticle = 1
                '' ''End If

                QteUnitaireArticle = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", CodeArticle)
                If QteUnitaireArticle = 0 Then QteUnitaireArticle = 1

                NouvelArticle("CodePreparation") = CodePreparation
                NouvelArticle("CodeArticle") = CodeArticle
                NouvelArticle("CodeABarre") = CodeABarre
                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)
                If RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", NouvelArticle("CodeArticle")) <> "" Then
                    NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                    NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", NouvelArticle("CodeArticle")))
                End If

                NouvelArticle("Qte") = 0

                NouvelArticle("Stock") = CalculeStock(CodeArticle)
                NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                NouvelArticle("TotTTC") = 0

                gArticles.Refresh()
                gArticles.Col = 5
            Else
                gArticles.Columns("CodeABarre").Value = ""
                gArticles.Col = 3
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " ChargerDetailArticle", ex.Message, "0000342", "Erreur d'exécution de ChargerDetailArticle ", True, True, True)

        End Try
    End Sub

    Public Sub CalculerMontants()
        Dim i As Integer = 0
        Dim j As Integer = 0

        TotalTTCArticle = 0.0
        TotalTTCIndemnite = 0.0
        TotalTTCPreparation = 0.0

        Dim QteUnitaireArticle As Integer = 0
        Dim CategorieArticle As Integer = 0
        Dim PreparationArticle As Integer = 0
        Try


            Do While i < gArticles.RowCount
                If gArticles(i, "Designation").ToString <> "" And IsDBNull(gArticles(i, "Qte")) = False Then

                    CategorieArticle = CInt(Val(RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))))
                    If CategorieArticle = 9 Then
                        Try
                            PreparationArticle = CInt(Val(RecupererValeurExecuteScalaire("CodeTypePreparation", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))))
                        Catch ex As Exception
                            PreparationArticle = 0
                        End Try
                    End If
                    '' ''If CategorieArticle = 8 Or (CategorieArticle = 9 And PreparationArticle = 1) Then
                    '' ''    QteUnitaireArticle = CInt(Val(RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))))
                    '' ''Else
                    '' ''    QteUnitaireArticle = 1
                    '' ''End If

                    QteUnitaireArticle = CInt(Val(RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))))
                    If QteUnitaireArticle = 0 Then QteUnitaireArticle = 1

                    gArticles(i, "TotTTC") = Math.Round((gArticles(i, "PrixVenteTTC") * gArticles(i, "Qte")) / QteUnitaireArticle, 3)
                    If gArticles(i, "TotTTC") < MinimumDePerception Then
                        gArticles(i, "TotTTC") = Format(Math.Round(MinimumDePerception, 3), "0.000")
                    End If
                    TotalTTCArticle = TotalTTCArticle + gArticles(i, "TotTTC")
                End If
                i = i + 1
            Loop

            Do While j <= gIndemnites.RowCount
                If gIndemnites(j, "LibelleIndemnite").ToString <> "" And IsDBNull(gIndemnites(j, "Prix")) = False Then
                    TotalTTCIndemnite = TotalTTCIndemnite + gIndemnites(j, "Prix")
                End If
                j = j + 1
            Loop

            TotalTTCPreparation = TotalTTCArticle + TotalTTCIndemnite

            lTotArticle.Text = TotalTTCArticle.ToString("### ### ##0.000")
            lTotIndemnite.Text = TotalTTCIndemnite.ToString("### ### ##0.000")
            tTotTTC.Text = (TotalTTCArticle + TotalTTCIndemnite).ToString("### ### ##0.000")
            'tTotTTC.Text = TotalTTCPreparation.ToString
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " CalculerMontants", ex.Message, "0000343", "Erreur d'exécution de CalculerMontants ", True, True, True)

        End Try
    End Sub

    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)

        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""
        Try
            Try
                Quote(ValeurCle)
            Catch ex As Exception
                Return Nothing
                Exit Function
            End Try

            StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurCle)
            CmdCalcul.Connection = ConnectionServeur
            CmdCalcul.CommandText = StrSQL
            Try
                Valeur = CmdCalcul.ExecuteScalar().ToString
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " RecupererValeurExecuteScalaire", ex.Message, "0000344", "Erreur d'exécution de RecupererValeurExecuteScalaire ", True, True, True)

        End Try
        Return (Valeur)

    End Function


    '************************************************************************************************************
    '************************************************************************************************************
    Private Sub gIndemnites_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gIndemnites.Change
        Try


            If (gIndemnites.Col = 2 And gIndemnites.Columns("LibelleIndemnite").Value <> "") Or gIndemnites.Col = 1 Then
                Dim StrSQL1 As String = ""
                Dim I As Integer
                Dim NbLigne As Integer
                NbLigne = gIndemnites.RowCount

                With gListeRecherche1
                    .Left = Me.gIndemnites.Left + Me.gIndemnites.Splits(0).DisplayColumns(0).Width + Me.gIndemnites.Splits(0).DisplayColumns(1).Width
                    .Top = Me.gIndemnites.Top + Me.gIndemnites.RowTop(Me.gIndemnites.Row) + Me.gIndemnites.Splits(0).ColumnFooterHeight
                End With

                Try
                    dsChargement.Tables("INDEMNITE").Clear()
                Catch ex As Exception

                End Try
                If gIndemnites.Row = dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Count - 1 And gIndemnites.Col = 2 Then
                    gListeRecherche1.Visible = True
                Else
                    gListeRecherche1.Visible = False
                End If

                'chargement des articles qui sont mis en jeu
                If gIndemnites.Col = 2 Then
                    If gIndemnites.Columns("LibelleIndemnite").Value.ToString.Length > 1 Then
                        If gIndemnites.Columns("LibelleIndemnite").Value.ToString.IndexOf(".") > 1 Then

                            StrSQL1 = "SELECT CodeIndemnite," + _
                                      "LibelleIndemnite," + _
                                      "Prix" + _
                                      " FROM INDEMNITE " + _
                                      " WHERE " + _
                                      " ltrim(str(Prix,10,3)) LIKE " + _
                                      Quote(gIndemnites.Columns("LibelleIndemnite").Value + "%") + " ORDER BY Prix"
                        Else
                            StrSQL1 = "SELECT CodeIndemnite," + _
                                        "LibelleIndemnite," + _
                                        "Prix" + _
                                        " FROM INDEMNITE" + _
                                        " WHERE " + _
                                        " LibelleIndemnite LIKE " + Quote(gIndemnites.Columns("LibelleIndemnite").Value + "%") + _
                                        " ORDER BY LibelleIndemnite"

                        End If
                    Else
                        StrSQL1 = "SELECT CodeIndemnite," + _
                                  "LibelleIndemnite," + _
                                  "Prix " + _
                                  " FROM INDEMNITE  ORDER BY LibelleIndemnite"
                    End If
                ElseIf gIndemnites.Col = 1 Then
                    StrSQL1 = "SELECT CodeIndemnite," + _
                              "LibelleIndemnite," + _
                              "Prix " + _
                              " FROM INDEMNITE" + _
                              " WHERE " + _
                              " CodeIndemnite LIKE " + Quote(gIndemnites.Columns("CodeIndemnite").Value) + _
                              " ORDER BY LibelleIndemnite"

                End If
                cmdChargement.Connection = ConnectionServeur
                cmdChargement.CommandText = StrSQL1
                daChargement = New SqlDataAdapter(cmdChargement)
                daChargement.Fill(dsChargement, "INDEMNITE")

                If dsChargement.Tables("INDEMNITE").Rows.Count > 0 Then
                    dr1 = dsChargement.Tables("INDEMNITE").Rows(0)
                End If

                With gListeRecherche
                    .Columns.Clear()
                    .DataSource = dsChargement
                    .DataMember = "INDEMNITE"
                    .Rebind(False)
                    .Columns("CodeIndemnite").Caption = "Code Article"
                    .Columns("LibelleIndemnite").Caption = "Designation"
                    .Columns("Prix").Caption = "Prix"

                    ' Centrer tous les entêtes
                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    Next
                    ' CentreR tous les valeurs
                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                    Next

                    For I = 0 To .Columns.Count - 1
                        .Splits(0).DisplayColumns(I).Visible = False
                    Next

                    .Splits(0).DisplayColumns("LibelleIndemnite").Visible = True

                    .Splits(0).DisplayColumns("CodeIndemnite").Width = 0
                    .Splits(0).DisplayColumns("LibelleIndemnite").Width = 360
                    .Splits(0).DisplayColumns("Prix").Width = 100

                    .Splits(0).DisplayColumns("CodeIndemnite").Style.BackColor = Color.Aqua
                    .Splits(0).DisplayColumns("LibelleIndemnite").Style.BackColor = Color.Aqua
                    .Splits(0).DisplayColumns("Prix").Style.BackColor = Color.Aqua

                    .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                    .Splits(0).ColumnCaptionHeight = 20
                    .Splits(0).RecordSelectors = False
                    .ExtendRightColumn = True
                    .EmptyRows = True
                    .FetchRowStyles = True
                End With
            End If
            gridselectionnee = "gIndemnites"

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " gIndemnites_Change", ex.Message, "0000345", "Erreur d'exécution de gIndemnites_Change ", True, True, True)

        End Try
    End Sub

    Private Sub gIndemnites_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gIndemnites.KeyUp
        Try


            Dim i As Integer = 0
            Dim StrSQL As String = ""
            Dim cmd As New SqlCommand
            Dim QuantiteAAjouter As Integer = 0


            If e.KeyCode = Keys.F3 Then
                bConfirmer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F8 And bAnnuler.Enabled = True Then
                bProduire_Click(sender, e)
            ElseIf e.KeyCode = Keys.F10 Then
                bAnnuler_Click(sender, e)
            ElseIf e.KeyCode = Keys.F12 Then
                bFermer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F7 Then
                bSupprimer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F5 Then
                bIndemnites_Click(sender, e)
            End If
            '---------------------------------- suppression du dernier ligne vide si on a deux lignes au mm temps vide
            '---------------------------------- cas ou on supprime dernier ligne
            If dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Count - 1 = 1 And gIndemnites(0, "CodeIndemnite") = "" And gIndemnites(1, "CodeIndemnite") = "" Then
                gIndemnites.MoveLast()
                gIndemnites.MovePrevious()
                gIndemnites.Delete()
            End If

            '---------------------------------- verouillage des lignes déja confirmées -------------------------
            If (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) And gIndemnites.Row <> dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Count - 1 Then
                gIndemnites.Splits(0).DisplayColumns("CodeIndemnite").Locked = True
                gIndemnites.Splits(0).DisplayColumns("LibelleIndemnite").Locked = True
            ElseIf gIndemnites.Row = dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Count - 1 Then
                gIndemnites.Splits(0).DisplayColumns("CodeIndemnite").Locked = False
                gIndemnites.Splits(0).DisplayColumns("LibelleIndemnite").Locked = False
            End If

            If gIndemnites.Col = 1 And gIndemnites.Columns("LibelleIndemnite").Value <> "" Then
                gIndemnites.Splits(0).DisplayColumns("CodeIndemnite").Locked = True
                gIndemnites.Splits(0).DisplayColumns("LibelleIndemnite").Locked = True

            End If
            If gIndemnites.Col = 2 And gIndemnites.Columns("CodeIndemnite").Value <> "" Then
                gIndemnites.Splits(0).DisplayColumns("CodeIndemnite").Locked = True
                gIndemnites.Splits(0).DisplayColumns("LibelleIndemnite").Locked = True

            End If

            '---------------------------------- suppression de la ligne selectionnées -------------------------
            'If (e.KeyCode = Keys.F7) And gIndemnites.Row <= dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Count - 1 Then
            '    Try
            '        gIndemnites.Delete()
            '        CalculerMontants()
            '        If dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Count - 1 <= 0 Then
            '            NouvelleIndemnite = dsChargement.Tables("INDEMNITE_PREPARATION").NewRow()
            '            NouvelleIndemnite("CodeIndemnite") = ""
            '            NouvelleIndemnite("LibelleIndemnite") = ""
            '            dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Add(NouvelleIndemnite)
            '        End If
            '        Exit Sub
            '    Catch ex As Exception
            '        Exit Sub
            '    End Try
            'End If
            '------------------------------ recherche par code ----------------------------------------------
            If gIndemnites.Col = 1 And e.KeyCode = Keys.Enter And gIndemnites.Row = dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Count - 1 Then
                ChargerDetailArticle1(gIndemnites.Columns("CodeIndemnite").Value)
                CalculerMontants()
                Exit Sub
            ElseIf gIndemnites.Col = 0 And e.KeyCode = Keys.Enter And gIndemnites.Row < dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Count - 1 Then
                gIndemnites.Col = 2
            End If
            '---------------------------------- masquer la liste de recherche si la designation est vide -----------
            If e.KeyCode = Keys.Back And gIndemnites.Columns("LibelleIndemnite").Value = "" And gIndemnites.Col = 2 Then
                gListeRecherche1.Visible = False
            End If
            '---------------------------- pour passer à la navigation dans la petite liste de recherhce ----------------
            If (e.KeyCode = Keys.Down Or e.KeyCode = Keys.Up) And gListeRecherche1.Visible = True Then
                gListeRecherche1.Focus()
                gListeRecherche1.Col = 2
            End If
            '---------------------------- si l'artcile n'existe pas dans la liste de recherhce sa quantité doit etre 0  ----
            If dsChargement.Tables("INDEMNITE").Rows.Count <= 0 And gListeRecherche1.Visible = True Then '
                gIndemnites.Columns("Prix").Value = 0
                gIndemnites.Col = 2
            End If
            '---------------------------- calcul des montants --------------------------------------------------------

            If (gIndemnites.Col = 1) And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) Then
                CalculerMontants()
            End If
            If gIndemnites.Col = 2 And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) Then
                CalculerMontants()
            End If

            '--------------------- traitement du clique sur le bouton ENTREE selon la colonne ---------------------------

            If e.KeyCode = Keys.Enter And (dsChargement.Tables("INDEMNITE").Rows.Count > 0 Or gIndemnites.Row <> dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Count - 1) Then
                gListeRecherche1_KeyUp(sender, e)     ' pour charger les informations de l'article a partir du fiche article
                CalculerMontants()
                If gIndemnites.Col = 2 Then
                    If gIndemnites.Columns("CodeIndemnite").Value = "" Then    ' si l'article choisit n'existe pas dans la liste de recherche
                        gIndemnites.Col = 2
                        gIndemnites.Columns("LibelleIndemnite").Value = ""
                    End If

                    ' si l'utilisateur a choisit un article on ajoute un nouvel enregistrement dans la datatable puis on passe à la ligne
                    ' suivant dans la gride avec un test si cet article est déja choisi ou non si c'est le cas on ajoute seulement la quntité

                    If gIndemnites(gIndemnites.RowCount - 1, ("CodeIndemnite")) <> "" Then
                        NouvelleIndemnite = dsChargement.Tables("INDEMNITE_PREPARATION").NewRow()
                        NouvelleIndemnite("LibelleIndemnite") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                        NouvelleIndemnite("CodeIndemnite") = ""
                        dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Add(NouvelleIndemnite)
                    End If
                    gIndemnites.MoveLast()
                    Try
                        dsChargement.Tables("INDEMNITE").Clear()
                    Catch ex As Exception
                    End Try
                    gIndemnites.Col = 1
                Else
                    gIndemnites.Col = 2
                End If
            End If

            gridselectionnee = "gIndemnites"

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " gIndemnites_KeyUp", ex.Message, "0000346", "Erreur d'exécution de gIndemnites_KeyUp ", True, True, True)

        End Try

    End Sub

    Public Sub ChargerDetailArticle1(ByVal CodeIndemnite As String)
        Try


            Dim resultat As String
            Dim StrSQL As String = ""
            Dim cmd As New SqlCommand

            resultat = RecupererValeurExecuteScalaire("LibelleIndemnite", "INDEMNITE", "CodeIndemnite", CodeIndemnite)
            If resultat <> "" Then
                NouvelleIndemnite("CodePreparation") = CodePreparation
                NouvelleIndemnite("CodeIndemnite") = CodeIndemnite
                NouvelleIndemnite("LibelleIndemnite") = dr1.Item("LibelleIndemnite")
                NouvelleIndemnite("Prix") = dr1.Item("Prix")

                gIndemnites.Refresh()
                gIndemnites.Col = 2
            Else
                gIndemnites.Columns("CodeIndemnite").Value = ""
                gIndemnites.Col = 2
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " ChargerDetailArticle1", ex.Message, "0000347", "Erreur d'exécution de ChargerDetailArticle1 ", True, True, True)

        End Try
    End Sub


    Private Sub gListeRecherche1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListeRecherche1.KeyUp
        ' dans cet évènenment on teste si l évènenment est due à un clique sur le boutton entrée et si l'utilisateur a choisi 
        ' un article et si le curseur est dans la colonne de designation 
        Try


            Dim StrSQL As String = ""
            Dim cmd As New SqlCommand

            If gListeRecherche1.Visible = False Then
                Exit Sub
            End If
            If e.KeyCode = Keys.Back Then
                gIndemnites.Focus()
                gIndemnites.Col = 2
                gIndemnites.MoveLast()
                gIndemnites.EditActive = True
            End If

            Dim j As Integer
            Dim NumeroLigne As Integer
            Dim DataRowRecherche As DataRow
            If e.KeyCode = Keys.Enter And (gIndemnites.Col = 4 Or gIndemnites.Col = 2) Then    'And gArticles.Columns("Designation").Value <> ""
                If dsChargement.Tables("INDEMNITE").Rows.Count > 0 Then
                    '--------------------- rehcerche de l'index de la ligne qui contient l'article recherché ---------------
                    For j = 0 To dsChargement.Tables("INDEMNITE").Rows.Count - 1
                        DataRowRecherche = dsChargement.Tables("INDEMNITE").Rows(j)
                        If DataRowRecherche.Item("CodeIndemnite") = gListeRecherche1.Columns("CodeIndemnite").Value Then
                            NumeroLigne = j
                        End If
                    Next

                    '------------------- chargement des données ---------------------------------------------- 
                    dr1 = dsChargement.Tables("INDEMNITE").Rows(NumeroLigne)
                    NouvelleIndemnite("CodePreparation") = CodePreparation
                    NouvelleIndemnite("CodeIndemnite") = dr1.Item("CodeIndemnite")
                    NouvelleIndemnite("LibelleIndemnite") = dr1.Item("LibelleIndemnite")
                    NouvelleIndemnite("Prix") = dr1.Item("Prix")

                    gIndemnites.Refresh()
                End If
                gListeRecherche1.Visible = False
                gIndemnites.Focus()
                gIndemnites.Col = 2
            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " gListeRecherche1_KeyUp", ex.Message, "0000348", "Erreur d'exécution de gListeRecherche1_KeyUp ", True, True, True)

        End Try
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        'Suivi du scénario
        fMessageException.Show("Preparation", "fPreparation", "bAnnuler_Click", "NoException", "NoError", "Clic sur le bouton Annuler", False, True, False)

        Try

            '    If bAnnuler.Text = "Sortir             F10" Then
            '        Me.Close()
            '        Exit Sub
            '    End If


            If gListeRecherche.Visible = True Then gListeRecherche.Visible = False

            init()

            'If bAnnuler.Text <> "Sortir             F10" Then
            '    Confirme = False

            'End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " bAnnuler_Click", ex.Message, "0000349", "Erreur d'exécution de bAnnuler_Click ", True, True, True)

        End Try
    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        'Suivi du scénario
        fMessageException.Show("Preparation", "fPreparation", "bConfirmer_Click", "NoException", "NoError", "Clic sur le bouton Confirmer", False, True, False)

        Try
            Dim CodeTest As Integer = 0
            Dim TotTTCSave As Double = 0.0
            Dim I = 0
            Dim StrMajLOT As String = ""
            Dim cmdMiseAJoursPrix As New SqlCommand

            Dim cmdNbreJourValidite As New SqlCommand
            Dim NbreJourValiditeParDefaut As String

            'pour remplir le nbre de jour de validité par défaut
            Try
                StrSQL = "select NbreJourValiditeParDefaut from PARAMETRE_PHARMACIE"
                cmdNbreJourValidite.Connection = ConnectionServeur
                cmdNbreJourValidite.CommandText = StrSQL
                NbreJourValiditeParDefaut = cmdNbreJourValidite.ExecuteScalar()
            Catch ex As Exception
                MsgBox("Vous devez aller sur [Paramètres Généreaux/Préparation] pour remplir les paramètres")
                NbreJourValiditeParDefaut = "365"
            End Try

            If tTotTTC.Text <> "0.000" Then
                TotTTCSave = tTotTTC.Text
            End If

            tTotTTC.Text = TotTTCSave

            If tCodeArticle.Text = "" Then
                MsgBox("Code article vide !", MsgBoxStyle.Critical, "Erreur")
                tCodeArticle.Focus()
                Exit Sub
            End If

            If tDesignation.Text = "" Then
                MsgBox("Designation article vide !", MsgBoxStyle.Critical, "Erreur")
                tDesignation.Focus()
                Exit Sub
            End If

            'If tQuantiteUnitaire.Text = "" Then
            '    MsgBox("Quantité unitaire article vide !", MsgBoxStyle.Critical, "Erreur")
            '    tQuantiteUnitaire.Focus()
            '    Exit Sub
            'End If

            If CodeExiste = True Then
                MsgBox("Code article existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
                tCodeArticle.Focus()
                Exit Sub
            End If

            If IsNumeric(tNbreJourValidite.Text) = False Then
                tNbreJourValidite.Text = NbreJourValiditeParDefaut
            End If

            'si la grid est vide
            If dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1 = 0 Then


                '''''''''''''''''''''
                'If TypePreparation = 2 Then
                'If mode = "A" Then
                '    cmdChargement.Connection = ConnectionServeur
                '    cmdChargement.CommandText = "INSERT INTO FORMULE_PREPARATION " + _
                '                "(CodePreparation, Designation, date, CodeTypePreparation, NombreJourValidite) " + _
                '                " VALUES(" + Quote(tCodeArticle.Value) + ", " + Quote(tDesignation.Value) + _
                '                "," + Quote(Now.Date) + ", 2, " + Quote(tNbreJourValidite.Value) + _
                '                ")"
                '    Try
                '        cmdChargement.ExecuteNonQuery()
                '    Catch ex As Exception
                '        Console.WriteLine(ex.Message)
                '    End Try

                '    StrSQL = "INSERT INTO ARTICLE " + _
                '   "(CodeArticle,CodeABarre,Designation,PrixVenteTTC,PrixAchatHT,QuantiteUnitaire,CodeCategorie,CodeTypePreparation) " + _
                '   " VALUES(" + _
                '   Quote(CodePreparation) + _
                '   "," + _
                '   Quote(tCodeArticle.Text) + _
                '   "," + _
                '   Quote(tDesignation.Text) + _
                '   "," + _
                '   Quote(0) + _
                '   "," + _
                '   Quote(0) + _
                '   "," + _
                '   Quote(tQuantiteUnitaire.Text) + _
                '   ",'9'," + Quote(TypePreparation.ToString) + ")"

                '    cmdMiseAJoursPrix.Connection = ConnectionServeur
                '    cmdMiseAJoursPrix.CommandText = StrSQL
                '    Try
                '        cmdMiseAJoursPrix.ExecuteNonQuery()

                '    Catch ex As Exception
                '        Console.WriteLine(ex.Message)
                '    End Try
                'Else

                'End If

                'End If
                ''''''''''''''''''''''


                If TypePreparation <> 2 Then
                    Exit Sub
                End If



            End If
            '-------------------------- contrôle de qte
            I = 0
            Do While I < gArticles.RowCount 'dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count
                If gArticles(I, "CodeArticle").ToString <> "" Then
                    'If dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("CodeArticle") <> "" And (dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("Qte").ToString = "" Or dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("Qte").ToString = "0") Then
                    If gArticles(I, "CodeArticle").ToString <> "" And (gArticles(I, "Qte").ToString = "" Or gArticles(I, "Qte").ToString = "0") Then
                        MsgBox("Qte vide de l'article !" + dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("Designation"), MsgBoxStyle.Critical, "Erreur")
                        gArticles.Focus()
                        gArticles.Col = 4
                        gArticles.MoveRelative(I)
                        Exit Sub
                    End If
                End If
                I = I + 1
            Loop

            'Calcul de prix d'achat selon les prix d'achat des composants et ses quantités utilisé dans la formule

            Dim PrixAchat As Double = 0.0
            Dim PrixAchatTotal As Double = 0.0
            Dim QteUnitaire As Integer = 1
            Dim QtePreparation As Integer = 1

            For I = 0 To dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1
                Try
                    If dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("CodeArticle").ToString <> "" Then
                        QteUnitaire = RecupererValeurExecuteScalaire("QuantiteUnitaire", " ARTICLE", "CodeArticle", dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("CodeArticle"))
                        PrixAchat = RecupererValeurExecuteScalaire("PrixAchatHT", " ARTICLE", "CodeArticle", dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("CodeArticle"))
                        QtePreparation = dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("Qte")

                        PrixAchat = PrixAchat / QteUnitaire * QtePreparation

                        PrixAchatTotal += PrixAchat
                    End If
                Catch ex As Exception
                End Try

            Next

            'Creation et changement des articles
            Dim marge As String
            If PrixAchatTotal <> 0 Then
                marge = (((CDbl(tTotTTC.Text) / CDbl(PrixAchatTotal.ToString)) - 1) * 100).ToString
                marge = (Math.Round(CDbl(marge), 3)).ToString
            Else
                marge = 0
            End If

            If mode = "A" Then
                StrSQL = "INSERT INTO ARTICLE " + _
                   "(""CodeArticle"",""CodeABarre"",""Designation"",""PrixVenteTTC"",""PrixVenteHT"",""PrixAchatHT"",""PrixAchatTTC"",""Marge"",""QuantiteUnitaire"",""CodeCategorie"",""CodeTypePreparation"") " + _
                   " VALUES(" + _
                   Quote(CodePreparation) + _
                   "," + _
                   Quote(tCodeArticle.Text) + _
                   "," + _
                   Quote(tDesignation.Text) + _
                   "," + _
                   Quote(tTotTTC.Text) + _
                    "," + _
                   Quote(tTotTTC.Text) + _
                   "," + _
                   Quote(PrixAchatTotal.ToString) + _
                   "," + _
                    Quote(PrixAchatTotal.ToString) + _
                   "," + _
                   Quote(marge) + _
                   "," + _
                   Quote(tQuantiteUnitaire.Text) + _
                   ",'9'," + Quote(TypePreparation.ToString) + ")"

                cmdMiseAJoursPrix.Connection = ConnectionServeur
                cmdMiseAJoursPrix.CommandText = StrSQL
                Try
                    cmdMiseAJoursPrix.ExecuteNonQuery()

                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

            ElseIf mode = "M" Then
                StrSQL = "UPDATE ARTICLE SET CodeABarre=" + Quote(tCodeArticle.Text) + _
                         ",Designation=" + Quote(tDesignation.Text) + _
                         ",PrixAchatHT=" + PrixAchatTotal.ToString + _
                         ",PrixAchatTTC=" + PrixAchatTotal.ToString + _
                         ",PrixVenteTTC=" + tTotTTC.Text + _
                         ",PrixVenteHT=" + tTotTTC.Text + _
                         ",Marge=" + Quote(marge) + _
                         ",QuantiteUnitaire=" + tQuantiteUnitaire.Text + _
                         " WHERE CodeArticle =" + Quote(CodePreparation)

                cmdMiseAJoursPrix.Connection = ConnectionServeur
                cmdMiseAJoursPrix.CommandText = StrSQL
                Try
                    cmdMiseAJoursPrix.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                StrSQL = "UPDATE FORMULE_PREPARATION SET TotalTTC=" + tTotTTC.Text + _
                        " WHERE CodePreparation =" + Quote(CodePreparation)

                cmdMiseAJoursPrix.Connection = ConnectionServeur
                cmdMiseAJoursPrix.CommandText = StrSQL
                Try
                    cmdMiseAJoursPrix.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

            End If

            If dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle") = "" And RecupererValeurExecuteScalaire("LibelleTypePreparation", "TYPE_PREPARATION", "CodeTypePreparation", TypePreparation) <> "INJECTION" Then
                MsgBox("Préparation Vide !", MsgBoxStyle.Critical, "Erreur")
                '--------------- si la liste est vide et les articles sont supprimer a cause 

                If dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1 < 0 Then
                    NouvelArticle = dsChargement.Tables("FORMULE_PREPARATION_DETAILS").NewRow()
                    NouvelArticle("Designation") = ""
                    NouvelArticle("CodeArticle") = ""
                    NouvelArticle("PrixVenteTTC") = 0
                    dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Add(NouvelArticle)

                End If
                '----------------------
                gArticles.Col = 1
                gArticles.EditActive = True
                Exit Sub

            End If

            'CalculerMontants()
            If mode = "A" Then

                '--------------------------- test de l'existance d'une préparation avec le mm code et sa suppression
                StrSQL = " SELECT count(CodePreparation) FROM FORMULE_PREPARATION " + _
                                         "WHERE CodePreparation=" + _
                                         Quote(CodePreparation) + ""

                cmdChargement.Connection = ConnectionServeur
                cmdChargement.CommandText = StrSQL

                Try
                    CodeTest = cmdChargement.ExecuteScalar()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

                If CodeTest > 0 Then
                    Dim reponse As MsgBoxResult
                    reponse = MsgBox("Cette préparation existe déja! " + Chr(13) + "Voulez vous la remplacer?", MsgBoxStyle.YesNo, "Information")
                    If reponse = MsgBoxResult.Yes Then
                        '---------------------------suppression des details 
                        StrSQL = "DELETE FROM FORMULE_PREPARATION_DETAILS WHERE CodePreparation = " + Quote(CodePreparation)
                        cmdChargement.Connection = ConnectionServeur
                        cmdChargement.CommandText = StrSQL
                        Try
                            cmdChargement.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try
                        '---------------------------suppression des indemnites 
                        StrSQL = "DELETE FROM INDEMNITE_PREPARATION WHERE CodePreparation = " + Quote(CodePreparation)
                        cmdChargement.Connection = ConnectionServeur
                        cmdChargement.CommandText = StrSQL
                        Try
                            cmdChargement.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try
                        '-------------------------- suppression du formule
                        StrSQL = "DELETE FROM FORMULE_PREPARATION WHERE CodePreparation=" + Quote(CodePreparation)
                        cmdChargement.Connection = ConnectionServeur
                        cmdChargement.CommandText = StrSQL
                        Try
                            cmdChargement.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try

                    End If
                End If

            Else
                '---------------------------suppression des details 
                StrSQL = "DELETE FROM FORMULE_PREPARATION_DETAILS WHERE CodePreparation=" + Quote(CodePreparation)
                cmdChargement.Connection = ConnectionServeur
                cmdChargement.CommandText = StrSQL
                Try
                    cmdChargement.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
                '---------------------------suppression des indemnites 
                StrSQL = "DELETE FROM INDEMNITE_PREPARATION WHERE CodePreparation=" + Quote(CodePreparation)
                cmdChargement.Connection = ConnectionServeur
                cmdChargement.CommandText = StrSQL
                Try
                    cmdChargement.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
                '-------------------------- suppression du formule
                StrSQL = "DELETE FROM FORMULE_PREPARATION WHERE CodePreparation=" + Quote(CodePreparation)
                cmdChargement.Connection = ConnectionServeur
                cmdChargement.CommandText = StrSQL
                Try
                    cmdChargement.ExecuteNonQuery()
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try
            End If


            Try
                'CalculerMontants()
            Catch ex As Exception
            End Try

            '---------------- ajout des informations entête de la préparation
            StrSQL = "SELECT * FROM FORMULE_PREPARATION WHERE CodePreparation =" + Quote(CodePreparation) + "" + _
                    " AND CodeTypePreparation =" + Quote(TypePreparation.ToString) + ""

            cmdChargement.Connection = ConnectionServeur
            cmdChargement.CommandText = StrSQL
            daChargementEntete = New SqlDataAdapter(cmdChargement)
            daChargementEntete.Fill(dsChargement, "FORMULE_PREPARATION")
            cbChargementEntete = New SqlCommandBuilder(daChargementEntete)

            NouvellePreparation = dsChargement.Tables("FORMULE_PREPARATION").NewRow()
            NouvellePreparation("CodePreparation") = CodePreparation
            NouvellePreparation("Designation") = tDesignation.Text '  DesignationPreparation
            NouvellePreparation("date") = System.DateTime.Today
            NouvellePreparation("CodeTypePreparation") = TypePreparation
            NouvellePreparation("NumeroOrdonnace") = tNumeroOrdonnance.Text
            NouvellePreparation("TotalTTC") = tTotTTC.Text 'TotalTTCPreparation
            NouvellePreparation("TotalArticle") = TotalTTCArticle
            NouvellePreparation("TotalIndemnite") = TotalTTCIndemnite
            NouvellePreparation("NombreJourValidite") = tNbreJourValidite.Text
            dsChargement.Tables("FORMULE_PREPARATION").Rows.Add(NouvellePreparation)
            '---------------- enregistrement entête
            Try
                daChargementEntete.Update(dsChargement, "FORMULE_PREPARATION")
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                dsChargement.Reset()
                Me.init()
            End Try
            '-------------------------- élémination des lignes vides de la tables détails
            I = 0
            Do While I < gArticles.RowCount 'dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count
                If gArticles(I, "CodeArticle").ToString <> "" Then
                    'If dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("CodeArticle") = "" Then
                    If gArticles(I, "CodeArticle") = "" Then
                        dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Delete()
                    End If
                Else
                    If gArticles(I, "CodeArticle").ToString = "" Then
                        dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Delete()
                    End If
                End If

                I = I + 1
            Loop

            Dim StrMaj As String = ""
            I = 0
            For I = 0 To gArticles.RowCount 'dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1
                If gArticles(I, "CodeArticle") <> "" Then
                    StrMaj = "INSERT INTO FORMULE_PREPARATION_DETAILS " + _
                            "(""CodePreparation"",""CodeArticle"",""CodeABarre"",""Designation"",""CodeForme"",""Qte"",""PrixVenteTTC"",""TotTTC"",""stock"") " + _
                            " VALUES(" + CodePreparation + _
                            "," + Quote(gArticles(I, "CodeArticle")) + _
                            "," + Quote(gArticles(I, "CodeABarre")) + _
                            "," + Quote(gArticles(I, "Designation")) + _
                            "," + Quote(gArticles(I, "CodeForme").ToString) + _
                            "," + Quote(gArticles(I, "Qte").ToString) + _
                            "," + Quote(gArticles(I, "PrixVenteTTC").ToString) + _
                            "," + Quote(gArticles(I, "TotTTC").ToString) + _
                            "," + Quote(gArticles(I, "stock").ToString) + _
                            ")"

                    cmdChargement.Connection = ConnectionServeur
                    cmdChargement.CommandText = StrMaj
                    Try
                        cmdChargement.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try
                End If

            Next

            '-------------------------- élémination des lignes vides de la tables indemnites
            I = 0
            Do While I < gIndemnites.RowCount 'dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Count
                'If dsChargement.Tables("INDEMNITE_PREPARATION").Rows(I).Item("CodeIndemnite") = "" Then
                If gIndemnites(I, "CodeIndemnite") = "" Then
                    dsChargement.Tables("INDEMNITE_PREPARATION").Rows(I).Delete()
                End If
                I = I + 1
            Loop

            StrMaj = ""
            I = 0
            For I = 0 To gIndemnites.RowCount 'dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Count - 1
                If gIndemnites(I, "CodeIndemnite") <> "" Then
                    StrMaj = "INSERT INTO INDEMNITE_PREPARATION " + _
                            "(""CodePreparation"",""CodeIndemnite"") " + _
                            " VALUES(" + Quote(CodePreparation) + _
                            "," + Quote(gIndemnites(I, "CodeIndemnite")) + _
                            ")"

                    cmdChargement.Connection = ConnectionServeur
                    cmdChargement.CommandText = StrMaj
                    Try
                        cmdChargement.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try
                End If

            Next

            TotalTTCPreparation = tTotTTC.Text
            'Confirme = True
            'bAnnuler.Text = "Sortir             F10"
            bConfirmer.Enabled = False

            If TypePreparation = 1 Then
                Me.Hide()
            End If

            pCodePreparation = CodePreparation

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " bConfirmer_Click", ex.Message, "0000350", "Erreur d'exécution de bConfirmer_Click ", True, True, True)

        End Try
    End Sub

    Private Sub fPreparation_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            Dim TotalTTC As Double = 0.0
            Dim TotalArticle As Double = 0.0
            Dim TotalIndemnite As Double = 0.0
            Dim cmd As New SqlCommand
            init()

            'CalculerMontants()
            'tCodeArticle.Focus()

            StrSQL = "SELECT TotalTTC FROM [FORMULE_PREPARATION] WHERE CodePreparation='" + CodePreparation + "'"
            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            TotalTTC = cmd.ExecuteScalar()
            tTotTTC.Text = TotalTTC.ToString("### ### ##0.000")

            StrSQL = "SELECT TotalArticle FROM [FORMULE_PREPARATION] WHERE CodePreparation='" + CodePreparation + "'"
            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            TotalArticle = cmd.ExecuteScalar()
            lTotArticle.Text = TotalArticle.ToString("### ### ##0.000")

            StrSQL = "SELECT TotalIndemnite FROM [FORMULE_PREPARATION] WHERE CodePreparation='" + CodePreparation + "'"
            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            TotalIndemnite = cmd.ExecuteScalar()
            lTotIndemnite.Text = TotalIndemnite.ToString("### ### ##0.000")

        Catch ex As Exception
            ''Gérer l'Exception
            'fMessageException.Show("Preparation", "fPreparation", " fPreparation_Load", ex.Message, "0000351", "Erreur d'exécution de fPreparation_Load ", True, True, True)
        End Try
    End Sub

    Private Sub gArticles_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gArticles.Click
        gridselectionnee = "gArticle"

    End Sub

    Private Sub gIndemnites_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gIndemnites.Click
        gridselectionnee = "gIndemnites"
    End Sub

    Private Sub bProduire_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bProduire.Click
        'Suivi du scénario
        fMessageException.Show("Preparation", "fPreparation", "bProduire_Click", "NoException", "NoError", "Clic sur le bouton Produire", False, True, False)

        Dim I As Integer = 0
        Dim cmd As New SqlCommand
        Dim StrMajLOT As String = ""
        Dim PrixTTCPourVerifier As Double = 0.0

        Try
            If bConfirmer.Enabled = True Then
                'bConfirmer_Click(sender, e)
                bConfirmer.PerformClick()
            End If
            '----------------- comparaison des prix des composants juste avant la production
            For I = 0 To dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1
                If dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("CodeArticle") <> "" Then
                    PrixTTCPourVerifier = RecupererValeurExecuteScalaire("PrixVenteTTC", "ARTICLE", "CodeArticle", dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("CodeArticle"))
                    If PrixTTCPourVerifier <> dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("PrixVenteTTC") Then
                        Dim InstanceVerifierPrix As New fVerifierPrixPreparations
                        InstanceVerifierPrix.NumeroPreparation = dsChargement.Tables("FORMULE_PREPARATION").Rows(0)("CodePreparation")
                        InstanceVerifierPrix.ShowDialog()

                        InstanceVerifierPrix.Dispose()
                        InstanceVerifierPrix.Close()
                    End If
                End If
            Next
            'si la grid est vide
            If dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1 = 0 Then
                Exit Sub
            End If

            '---------------------------- inscription sur ordonnancier

            Dim CodeMedecin As String = ""
            Dim NomClient As String = ""
            Dim Adresse As String = ""
            Dim NCIN As String = ""
            Dim NumeroOrdonnancier As Integer = 0

            Dim MyOrdonnancier As New fOrdonnancier

            MyOrdonnancier.NomClient = ""

            MyOrdonnancier.ShowDialog()

            If MyOrdonnancier.Confirmer = True Then

                CodeMedecin = MyOrdonnancier.CodeMedecin
                NomClient = MyOrdonnancier.NomClient
                Adresse = MyOrdonnancier.Adresse
                NCIN = MyOrdonnancier.NCIN

                NumeroOrdonnancier = RecupereNumeroSequentiel("NumeroSequentielOrdonnancier")

                I = 0
                Do While I < dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count And gArticles(I, "CodeArticle") <> ""

                    'NumeroOrdonnancier = RecupereNumeroSequentiel("NumeroSequentielOrdonnancier")

                    StrMajLOT = "INSERT INTO ORDONNANCIER " + _
                            "(""Numero"",""CodeMedecin"",""NomClient"",""AdresseClient"",""NCIN"",""CodeArticle"",""NumeroVente"",""Date"",""Type"") " + _
                            " VALUES(" + NumeroOrdonnancier.ToString + "," + _
                             CodeMedecin.ToString + "," + Quote(NomClient) + "," + _
                            Quote(Adresse) + "," + Quote(NCIN) + "," + _
                            Quote(dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("CodeArticle")) + "," + Quote(dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("CodePreparation")) + ",'" + System.DateTime.Now + "', 'Préparation magistrale')"
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrMajLOT
                    Try
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                    I = I + 1
                Loop
            End If
            MyOrdonnancier.Close()
            MyOrdonnancier.Dispose()

            '------------------------pour afficher la forme Production Magistrale

            Dim MyProductionMagistrale As New fProductionMagistrale

            MyProductionMagistrale.lNumeroMagistral.Text = "PM" + NumeroOrdonnancier.ToString
            MyProductionMagistrale.pCodeArticleMagistrale = tCodeArticle.Value
            MyProductionMagistrale.pDesignationMagistrale = tDesignation.Value
            MyProductionMagistrale.pCodePreparationMagistrale = CodePreparation
            MyProductionMagistrale.pNomClient = NomClient
            MyProductionMagistrale.ShowDialog()

            MyProductionMagistrale.Dispose()
            MyProductionMagistrale.Close()

            bProduire.Enabled = False

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " bProduire_Click", ex.Message, "0000352", "Erreur d'exécution de bProduire_Click ", True, True, True)
        End Try

    End Sub

    Public Function RecupereNumero()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max([NumeroProduction]) FROM [PRODUCTION]"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If
        Return ValeurRetour
    End Function

    Public Function RecupererValeurPreparationDetails(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurClePrimaire, ByVal CleSecondaire, ByVal ValeurCleSecondaire)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        Try
            Quote(ValeurClePrimaire)
            Quote(ValeurCleSecondaire)
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurClePrimaire) + "And" + " " + CleSecondaire + "=" + Quote(ValeurCleSecondaire)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function

    Public Function RecupereNumeroSequentiel(ByVal Parametre As String)
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As Integer = 0
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Try

            StrSQL = " SELECT " + Parametre + " FROM [PARAMETRES] WHERE POSTE ='" + System.Environment.GetEnvironmentVariable("Poste") + "'"
            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQL
            Try
                ValeurActuel = cmdRecupereNum.ExecuteScalar()
            Catch ex As Exception
                ValeurActuel = 1
                Console.WriteLine(ex.Message)
            End Try
            '----------------------- incrémentation du dernier numero sequenciel    ----
            StrSQL = "Update PARAMETRES SET " + Parametre + "=" + (ValeurActuel + 1).ToString

            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQL

            Try
                cmdRecupereNum.ExecuteNonQuery()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try



        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " RecupereNumeroSequentiel", ex.Message, "0000353", "Erreur d'exécution de RecupereNumeroSequentiel ", True, True, True)

        End Try
        Return ValeurActuel
    End Function

    Public Function RecupereNumeroSortie()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer
        Try
            StrSQL = " SELECT max([NumeroSortie]) FROM [SORTIE]"
            cmdRecupereNum.Connection = ConnectionServeur
            'cmdRecupereNum.Transaction = Transaction
            cmdRecupereNum.CommandText = StrSQL
            Try
                ValeurActuel = cmdRecupereNum.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
            If ValeurActuel.Length > 4 Then
                If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                    Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                    Numero = Numero + 1
                    numeroConvertit = Numero.ToString
                    For i = 0 To 5 - numeroConvertit.Length
                        numeroConvertit = "0" + numeroConvertit
                    Next
                    ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
                Else
                    ValeurRetour = Year(Today).ToString + "/" + "000001"
                End If
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " RecupereNumeroSortie", ex.Message, "0000354", "Erreur d'exécution de RecupereNumeroSortie ", True, True, True)

        End Try

        Return ValeurRetour

    End Function

    Public Function RecupereNumeroEntree()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer
        Try

            StrSQL = " SELECT max([NumeroEntree]) FROM [ENTREE]"
            cmdRecupereNum.Connection = ConnectionServeur
            ' cmdRecupereNum.Transaction = Transaction
            cmdRecupereNum.CommandText = StrSQL
            Try
                ValeurActuel = cmdRecupereNum.ExecuteScalar()
            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
            '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
            If ValeurActuel.Length > 4 Then
                If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                    Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                    Numero = Numero + 1
                    numeroConvertit = Numero.ToString
                    For i = 0 To 5 - numeroConvertit.Length
                        numeroConvertit = "0" + numeroConvertit
                    Next
                    ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
                Else
                    ValeurRetour = Year(Today).ToString + "/" + "000001"
                End If
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " RecupereNumeroEntree", ex.Message, "0000355", "Erreur d'exécution de RecupereNumeroEntree ", True, True, True)

        End Try

        Return ValeurRetour
    End Function

    Private Sub bVerifierPrix_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bVerifierPrix.Click
        'Suivi du scénario
        fMessageException.Show("Preparation", "fPreparation", "bVerifierPrix_Click", "NoException", "NoError", "Clic sur le bouton VerifierPrix", False, True, False)

        Dim PrixTTCPourVerifier As Double = 0.0
        Dim I As Integer = 0
        Dim TROUVE As Boolean = False
        Dim VerifierPrix As String

        Try

            '----------------- comparaison des prix des composants juste avant la production
            For I = 0 To dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1
                If dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("CodeArticle") <> "" Then

                    PrixTTCPourVerifier = RecupererValeurExecuteScalaire("PrixVenteTTC", "ARTICLE", "CodeArticle", dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("CodeArticle"))

                    If PrixTTCPourVerifier <> dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows(I).Item("PrixVenteTTC") Then
                        TROUVE = True
                        Dim InstanceVerifierPrix As New fVerifierPrixPreparations
                        InstanceVerifierPrix.NumeroPreparation = dsChargement.Tables("FORMULE_PREPARATION").Rows(0)("CodePreparation")

                        InstanceVerifierPrix.ShowDialog()

                        'pour tester le choix utilisateur (cliquer sur le btn Annuler ou verifier Prix)
                        VerifierPrix = InstanceVerifierPrix.pVerifierPrix

                        If VerifierPrix = "Changer les prix" Then

                            init()
                            CalculerMontants()

                        Else 'VerifierPrix = "Annuler"

                            'rien à faire 

                        End If
                        InstanceVerifierPrix.Dispose()
                        InstanceVerifierPrix.Close()
                        Exit Sub

                    End If

                End If

            Next

            If TROUVE = False Then
                MsgBox("Les prix sont mis à jour!", MsgBoxStyle.Information, "Information")
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " bVerifierPrix_Click", ex.Message, "0000356", "Erreur d'exécution de bVerifierPrix_Click ", True, True, True)

        End Try
    End Sub

    Private Sub tNumeroOrdonnance_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNumeroOrdonnance.KeyUp
        Try

            If e.KeyCode = Keys.F3 Then
                bConfirmer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F8 And bAnnuler.Enabled = True Then
                bProduire_Click(sender, e)
            ElseIf e.KeyCode = Keys.F10 Then
                bAnnuler_Click(sender, e)
            ElseIf e.KeyCode = Keys.F12 Then
                bFermer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F7 Then
                bSupprimer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F5 Then
                bIndemnites_Click(sender, e)
            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " tNumeroOrdonnance_KeyUp", ex.Message, "0000357", "Erreur d'exécution de tNumeroOrdonnance_KeyUp ", True, True, True)

        End Try
    End Sub

    Private Sub bConfirmer_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bConfirmer.KeyUp
        Try

            If e.KeyCode = Keys.F3 Then
                bConfirmer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F8 And bAnnuler.Enabled = True Then
                bProduire_Click(sender, e)
            ElseIf e.KeyCode = Keys.F10 Then
                bAnnuler_Click(sender, e)
            ElseIf e.KeyCode = Keys.F12 Then
                bFermer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F7 Then
                bSupprimer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F5 Then
                bIndemnites_Click(sender, e)
            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " bConfirmer_KeyUp", ex.Message, "0000358", "Erreur d'exécution de bConfirmer_KeyUp ", True, True, True)

        End Try
    End Sub

    Private Sub bAnnuler_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bAnnuler.KeyUp
        Try

            If e.KeyCode = Keys.F3 Then
                bConfirmer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F8 And bAnnuler.Enabled = True Then
                bProduire_Click(sender, e)
            ElseIf e.KeyCode = Keys.F10 Then
                bAnnuler_Click(sender, e)
            ElseIf e.KeyCode = Keys.F12 Then
                bFermer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F7 Then
                bSupprimer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F5 Then
                bIndemnites_Click(sender, e)
            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " bAnnuler_KeyUp", ex.Message, "0000359", "Erreur d'exécution de bAnnuler_KeyUp ", True, True, True)

        End Try
    End Sub

    Private Sub bProduire_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bProduire.KeyUp
        Try
            If e.KeyCode = Keys.F3 Then
                bConfirmer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F8 And bAnnuler.Enabled = True Then
                bProduire_Click(sender, e)
            ElseIf e.KeyCode = Keys.F10 Then
                bAnnuler_Click(sender, e)
            ElseIf e.KeyCode = Keys.F12 Then
                bFermer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F7 Then
                bSupprimer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F5 Then
                bIndemnites_Click(sender, e)
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " bProduire_KeyUp", ex.Message, "0000360", "Erreur d'exécution de bProduire_KeyUp ", True, True, True)

        End Try
    End Sub

    Private Sub bVerifierPrix_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bVerifierPrix.KeyUp
        Try
            If e.KeyCode = Keys.F3 Then
                bConfirmer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F8 And bAnnuler.Enabled = True Then
                bProduire_Click(sender, e)
            ElseIf e.KeyCode = Keys.F10 Then
                bAnnuler_Click(sender, e)
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " bVerifierPrix_KeyUp", ex.Message, "0000361", "Erreur d'exécution de bVerifierPrix_KeyUp ", True, True, True)

        End Try
    End Sub

    Private Sub tTotTTC_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTotTTC.KeyUp
        Try
            If e.KeyCode = Keys.F3 Then
                bConfirmer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F8 And bAnnuler.Enabled = True Then
                bProduire_Click(sender, e)
            ElseIf e.KeyCode = Keys.F10 Then
                bAnnuler_Click(sender, e)
            ElseIf e.KeyCode = Keys.F12 Then
                bFermer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F7 Then
                bSupprimer_Click(sender, e)
            ElseIf e.KeyCode = Keys.F5 Then
                bIndemnites_Click(sender, e)
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " tTotTTC_KeyUp", ex.Message, "0000362", "Erreur d'exécution de tTotTTC_KeyUp ", True, True, True)

        End Try
    End Sub

    Private Sub gArticles_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gArticles.MouseClick
        Try
            If gArticles.Row <> dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1 Then
                gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = True
                gArticles.Splits(0).DisplayColumns("Designation").Locked = True
            ElseIf gArticles.Row = dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1 Then
                gArticles.Splits(0).DisplayColumns("CodeArticle").Locked = False
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
            End If
            gridselectionnee = "gArticle"
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " gArticles_MouseClick", ex.Message, "0000363", "Erreur d'exécution de gArticles_MouseClick ", True, True, True)

        End Try
    End Sub

    Private Sub gIndemnites_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gIndemnites.MouseClick
        Try
            '---------------------------------- verouillage des lignes déja confirmées -------------------------
            If gIndemnites.Row <> dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Count - 1 Then
                gIndemnites.Splits(0).DisplayColumns("CodeIndemnite").Locked = True
                gIndemnites.Splits(0).DisplayColumns("LibelleIndemnite").Locked = True
            ElseIf gIndemnites.Row = dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Count - 1 Then
                gIndemnites.Splits(0).DisplayColumns("CodeIndemnite").Locked = False
                gIndemnites.Splits(0).DisplayColumns("LibelleIndemnite").Locked = False
            End If
            gridselectionnee = "gIndemnites"
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " gIndemnites_MouseClick", ex.Message, "0000364", "Erreur d'exécution de gIndemnites_MouseClick ", True, True, True)

        End Try
    End Sub

    Private Sub tCodeArticle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeArticle.KeyUp
        If e.KeyCode = Keys.Enter Then
            tDesignation.Focus()
        End If
    End Sub

    Private Sub tCodeArticle_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeArticle.LostFocus
        If lTest.Text = "Code valide" Then
            lTest.Visible = False
        End If
    End Sub

    Private Sub tCodeArticle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeArticle.TextChanged
        Try
            Dim StrSQLtest As String = ""

            dsRecupereNum.Clear()
            If mode = "A" Or mode = "M" Then

                If tDesignation.Text = "" And mode = "M" Then
                    Exit Sub
                End If

                If mode = "A" Or mode = "DUPLIQUER" Then
                    StrSQLtest = " SELECT CodeArticle FROM Article WHERE CodeABarre=" + Quote(tCodeArticle.Text)
                Else
                    StrSQLtest = " SELECT CodeArticle FROM Article WHERE CodeABarre=" + Quote(tCodeArticle.Text) + " AND Designation <> " + Quote(tDesignation.Text) + ""
                End If
                cmdRecupereNum.Connection = ConnectionServeur
                cmdRecupereNum.CommandText = StrSQLtest
                daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
                daRecupereNumt.Fill(dsRecupereNum, "Article")

                If dsRecupereNum.Tables("Article").Rows.Count <> 0 Or tCodeArticle.Text = "" Then
                    lTest.Text = "Code non valide déja existe"
                    lTest.ForeColor = Color.OrangeRed
                    lTest.Visible = True
                    CodeExiste = True
                Else
                    lTest.Text = "Code valide"
                    lTest.ForeColor = Color.LimeGreen
                    lTest.Visible = True
                    CodeExiste = False
                End If
            End If
            If tCodeArticle.Text = "" Then
                lTest.Visible = False
            End If

        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " tCodeArticle_TextChanged", ex.Message, "0000365", "Erreur d'exécution de tCodeArticle_TextChanged ", True, True, True)

        End Try
    End Sub

    Private Sub tDesignation_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tDesignation.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNbreJourValidite.Focus()
        End If
    End Sub

    Private Sub gListeRecherche_UnboundColumnFetch(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gListeRecherche.UnboundColumnFetch
        Try
            Dim y As String
            y = gListeRecherche(e.Row, ("CodeArticle"))
            e.Value = CalculeStock(y)
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " gListeRecherche_UnboundColumnFetch", ex.Message, "0000366", "Erreur d'exécution de gListeRecherche_UnboundColumnFetch ", True, True, True)

        End Try
    End Sub

    Private Sub tQuantiteUnitaire_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tQuantiteUnitaire.KeyUp
        If e.KeyCode = Keys.Enter Then
            gArticles.Focus()
        End If
    End Sub

    Private Sub tNbreJourValidite_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNbreJourValidite.KeyUp
        If e.KeyCode = Keys.Enter Then
            gArticles.Focus()
        End If
    End Sub

    Private Sub bSupprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click
        'Suivi du scénario
        fMessageException.Show("Preparation", "fPreparation", "bSupprimer_Click", "NoException", "NoError", "Clic sur le bouton Supprimer", False, True, False)

        Try
            'if gArticle est selectionnee
            If gridselectionnee = "gArticle" Then

                Try
                    'If gArticles.Row <= dsChargement.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1 Then
                    If gArticles.RowCount > 0 Then

                        If gArticles(gArticles.Row, ("CodeArticle")) <> "" Then
                            gArticles.Delete()
                            CalculerMontants()


                            dsChargement.Tables("FORMULE_PREPARATION_DETAILS").AcceptChanges()
                            'Try
                            ''gArticles.Focus()
                            'gArticles.Col = gArticles.Columns.IndexOf(gArticles.Columns("CodeArticle"))
                            'gArticles.EditActive = True
                            'Exit Sub
                        Else
                            CalculerMontants()
                        End If
                    End If
                Catch ex As Exception
                    Exit Sub
                End Try
            Else
                Try
                    'If gIndemnites.Row <= dsChargement.Tables("INDEMNITE_PREPARATION").Rows.Count - 1 Then
                    If gIndemnites.RowCount > 0 Then
                        If gIndemnites(gIndemnites.Row, ("CodeIndemnite")) <> "" Then
                            gIndemnites.Delete()
                            CalculerMontants()


                            dsChargement.Tables("INDEMNITE_PREPARATION").AcceptChanges()
                            'Try
                            'gArticles.Focus()
                            'gArticles.EditActive = True
                            'Exit Sub
                        End If
                    Else
                        CalculerMontants()
                    End If
                Catch ex As Exception
                    Exit Sub
                End Try
            End If
        Catch ex As Exception
            'Gérer l'Exception
            fMessageException.Show("Preparation", "fPreparation", " bSupprimer_Click", ex.Message, "0000367", "Erreur d'exécution de bSupprimer_Click ", True, True, True)
        End Try
    End Sub

    Private Sub bIndemnites_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bIndemnites.Click
        'Suivi du scénario
        fMessageException.Show("Preparation", "fPreparation", "bIndemnites_Click", "NoException", "NoError", "Clic sur le bouton Indemnites", False, True, False)

        gIndemnites.Focus()
        gIndemnites.Col = 1
        Exit Sub

    End Sub

    Private Sub bFermer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFermer.Click
        'Suivi du scénario
        fMessageException.Show("Preparation", "fPreparation", "bFermer_Click", "NoException", "NoError", "Clic sur le bouton Fermer", False, True, False)

        Me.Close()
    End Sub

    Private Sub gArticles_MouseDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gArticles.MouseDown
        gridselectionnee = "gArticle"
    End Sub

    Private Sub gIndemnites_MouseDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gIndemnites.MouseDown
        gridselectionnee = "gIndemnites"
    End Sub

    Private Sub gArticles_MouseUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gArticles.MouseUp
        gridselectionnee = "gArticle"
    End Sub

    Private Sub gIndemnites_MouseUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gIndemnites.MouseUp
        gridselectionnee = "gIndemnites"
    End Sub

    Private Sub tNbreJourValidite_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNbreJourValidite.TextChanged

        Try
            Dim cmdNbreJourValidite As New SqlCommand
            Dim NbreJourValiditeParDefaut As String = ""

            'pour remplir le nbre de jour de validité par défaut
            StrSQL = "select NbreJourValiditeParDefaut from PARAMETRE_PHARMACIE"
            cmdNbreJourValidite.Connection = ConnectionServeur
            cmdNbreJourValidite.CommandText = StrSQL
            NbreJourValiditeParDefaut = cmdNbreJourValidite.ExecuteScalar()

            If IsNumeric(tNbreJourValidite.Text) = False Then
                tNbreJourValidite.Text = NbreJourValiditeParDefaut
            End If
        Catch ex As Exception
            'Gérer l'Exception
            'fMessageException.Show("Preparation", "fPreparation", " tNbreJourValidite_TextChanged", ex.Message, "0000368", "Erreur d'exécution de tNbreJourValidite_TextChanged ", True, True, True)
            MsgBox("Vous devez aller sur [Paramètres Généreaux/Préparation] pour remplir les paramètres")
            tNbreJourValidite.Text = "365"
            Exit Sub
        End Try
    End Sub

    Public Sub AfficherFicheArticle(ByVal CodeArticle, ByVal StockArticle, ByVal Designation)
        Dim MyFicheArticle As New fFicheArticle

        MyFicheArticle.CodeArticle = CodeArticle
        MyFicheArticle.StockArticle = StockArticle
        MyFicheArticle.DesignationArticle = Designation
        MyFicheArticle.ajoutmodif = "M"

        MyFicheArticle.Init()
        MyFicheArticle.ShowDialog()
        MyFicheArticle.Close()
        MyFicheArticle.Dispose()
    End Sub
End Class