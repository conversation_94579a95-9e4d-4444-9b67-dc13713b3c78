﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fZoomImage
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.bFermer = New C1.Win.C1Input.C1Button()
        Me.PictureBox_ZOOM = New System.Windows.Forms.PictureBox()
        Me.GroupBox1.SuspendLayout()
        CType(Me.PictureBox_ZOOM, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.bFermer)
        Me.GroupBox1.Controls.Add(Me.PictureBox_ZOOM)
        Me.GroupBox1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupBox1.Location = New System.Drawing.Point(0, 0)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(842, 782)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Zoom"
        '
        'bFermer
        '
        Me.bFermer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bFermer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bFermer.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bFermer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bFermer.Location = New System.Drawing.Point(718, 725)
        Me.bFermer.Name = "bFermer"
        Me.bFermer.Size = New System.Drawing.Size(112, 45)
        Me.bFermer.TabIndex = 9
        Me.bFermer.Text = "Fermer"
        Me.bFermer.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bFermer.UseVisualStyleBackColor = True
        Me.bFermer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'PictureBox_ZOOM
        '
        Me.PictureBox_ZOOM.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.PictureBox_ZOOM.Dock = System.Windows.Forms.DockStyle.Fill
        Me.PictureBox_ZOOM.Location = New System.Drawing.Point(3, 16)
        Me.PictureBox_ZOOM.Name = "PictureBox_ZOOM"
        Me.PictureBox_ZOOM.Size = New System.Drawing.Size(836, 763)
        Me.PictureBox_ZOOM.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox_ZOOM.TabIndex = 0
        Me.PictureBox_ZOOM.TabStop = False
        '
        'fZoomImage
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.ClientSize = New System.Drawing.Size(842, 782)
        Me.Controls.Add(Me.GroupBox1)
        Me.Name = "fZoomImage"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "Zoom Image"
        Me.WindowState = System.Windows.Forms.FormWindowState.Maximized
        Me.GroupBox1.ResumeLayout(False)
        CType(Me.PictureBox_ZOOM, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents PictureBox_ZOOM As System.Windows.Forms.PictureBox
    Friend WithEvents bFermer As C1.Win.C1Input.C1Button
End Class
