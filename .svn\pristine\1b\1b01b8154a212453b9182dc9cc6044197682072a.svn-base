var H = 0;
var THEME = 2;

window.onload = function(e) {
	
	try{ parent.document.getElementById("idIf").style.visibility="visible"; } catch(err){}
	
	appliquerTheme();
	redimer();
	var cn =getNavigatorCode();
		
	if(cn.indexOf("IE7")>-1)
	{
		document.getElementById("div_datas_monographie").style.paddingRight="30px";
		/*
		var largeur = document.documentElement.clientWidth;
		var taille = largeur-290;
		if(taille<0) taille = 0;
		document.getElementById("div_datas_monographie").style.width=taille+'px';
		
		/*document.getElementById("signet_div").style.paddingRight="17px";
		document.getElementById("signet_div").style.width="165px";*/
		//document.body.style.display='block';
		//$("body").fadeIn("slow");
		document.body.style.display='block';
	}
	else
	{
		$("body").fadeIn("slow");
	}
	
	//document.body.style.display='block';
	//document.body.style.visibility='visible';
	
}

window.onresize = function (e) {
	redimer();
}

function appliquerTheme()
{
	if(THEME==1) 
	{
		H = 218;
		document.getElementById("header_monographie").className="header_monographie_grand";
	}
	else if(THEME==2)
	{
		H = 53;
		document.getElementById("header_monographie").className="header_monographie_petit";
	}
		
	document.getElementById("monographie_first_tr").style.height = H+"px";
}

function redimer()
{
	var hauteurContenu = document.documentElement.clientHeight;
	
	
	var hauteurDuSignet = hauteurContenu-114;
	var hauteurDuDiv = (hauteurDuSignet+54)-H;
	if(hauteurDuSignet<0) hauteurDuSignet=0;
	if(hauteurDuDiv<0) hauteurDuDiv=0;
	
	document.getElementById("signet_div").style.height=hauteurDuSignet+"px";
	document.getElementById("div_datas_monographie").style.height=hauteurDuDiv+"px";
	
	var cn =getNavigatorCode();
	
	//if(cn.indexOf("IE7")>-1)
	{
		var largeur = document.documentElement.clientWidth;
		var taille = largeur-260;//290
		if(taille<0) taille = 0;
		document.getElementById("div_datas_monographie").style.width=taille+'px';
	}
	
}

function SelectionSignet(element,nomAncre)
{
	var signets = document.getElementsByName("signet_td");
	var signetsPuces = document.getElementsByName("signet_td_puce");
	var signetActiveTd = element.getElementsByTagName("td");
	
	for(var i=0;i<signets.length;i++)
	{
		document.getElementsByName("signet_td")[i].className="signet_td";
	}
	for(var i=0;i<signetsPuces.length;i++)
	{
		document.getElementsByName("signet_td_puce")[i].className="signet_td_puce";
		document.getElementsByName("signet_td_puce")[i].getElementsByTagName("img")[0].src="./images/puce1.png";
	}
	
	for(var i=0;i<signetActiveTd.length;i++)
	{
		if(i==0) 	signetActiveTd[i].className="signet_td_puce signet_td_active";
		else		signetActiveTd[i].className="signet_td signet_td_active";
	}
	
	element.getElementsByTagName("td")[0].getElementsByTagName("img")[0].src="./images/puce2.png";
	document.location.href="#"+nomAncre;
}

function ChangementCurseur(element)
{
	/*alert(element.style.cursor);*/
	if(element.style.cursor=='default' || element.style.cursor=='') element.style.cursor='pointer'; else element.style.cursor='default';
}

function getNavigatorCode() 
{
	var detect = navigator.userAgent.toLowerCase();
	var navCode = "";
	var ver = "";
	var pos = "";
		
	if(detect.indexOf('firefox')>-1)
	{
		pos = detect.indexOf('firefox');
		ver = detect.substring(pos+8,detect.length);
		navCode = "FF"+ver;
	}
	else if(detect.indexOf('msie')>-1)
	{
		try
		{
			pos = detect.indexOf('msie');
			var tmp = detect.substring(pos+5, detect.length);
			pos = tmp.indexOf(";");
			ver = tmp.substring(0,pos);
		}
		catch(err){ ver = ""; }
		
		navCode = "IE"+ver;
	}
	else if(detect.indexOf('chrome')>-1)
	{
		try
		{
			pos = detect.indexOf('chrome');
			var tmp = detect.substring(pos+7, detect.length);
			pos = tmp.indexOf(" ");
			ver = tmp.substring(0,pos);
		}
		catch(err){ ver = ""; }
		
		navCode = "CH"+ver;
	}
	else if(detect.indexOf('safari')>-1 && detect.indexOf('version')>-1)
	{
		try
		{
			pos = detect.indexOf('version');
			var tmp = detect.substring(pos+8, detect.length);
			pos = tmp.indexOf(" ");
			ver = tmp.substring(0,pos);
		}
		catch(err){ ver = ""; }
		
		navCode = "SA"+ver;
	}
	else
	{
		navCode = "AU";
	}
	
	return navCode;
}
