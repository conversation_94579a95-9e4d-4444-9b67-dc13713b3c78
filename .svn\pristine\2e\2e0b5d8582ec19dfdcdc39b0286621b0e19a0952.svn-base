﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fMedecin
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fMedecin))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.tIdCnamRecherce = New C1.Win.C1Input.C1TextBox()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.tCodeRecherche = New C1.Win.C1Input.C1TextBox()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.tNomRecherche = New C1.Win.C1Input.C1TextBox()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.cmbVilleRecherche = New C1.Win.C1List.C1Combo()
        Me.cmbSpecialiteRecherche = New C1.Win.C1List.C1Combo()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.lTitre = New System.Windows.Forms.Label()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.tIdenIdCNAM = New C1.Win.C1Input.C1TextBox()
        Me.tCleIdCNAM = New C1.Win.C1Input.C1TextBox()
        Me.chkBloquer = New System.Windows.Forms.CheckBox()
        Me.cmbVille = New C1.Win.C1List.C1Combo()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.bSupprimerMedecin = New C1.Win.C1Input.C1Button()
        Me.bAnnulerMedecin = New C1.Win.C1Input.C1Button()
        Me.bModifierMedecin = New C1.Win.C1Input.C1Button()
        Me.bAjouterMedecin = New C1.Win.C1Input.C1Button()
        Me.tIdCNAM = New C1.Win.C1Input.C1TextBox()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.tMail = New C1.Win.C1Input.C1TextBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.cmbSpecialiteMedecin = New C1.Win.C1List.C1Combo()
        Me.bConfirmerMedecin = New C1.Win.C1Input.C1Button()
        Me.tAdresseMedein = New C1.Win.C1Input.C1TextBox()
        Me.tFaxMedecin = New C1.Win.C1Input.C1TextBox()
        Me.tTelMedecin = New C1.Win.C1Input.C1TextBox()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.tNomMedecin = New C1.Win.C1Input.C1TextBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.tCodeMedecin = New C1.Win.C1Input.C1TextBox()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.gMedecin = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bImprimer = New C1.Win.C1Input.C1Button()
        Me.Panel.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        CType(Me.tIdCnamRecherce, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodeRecherche, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNomRecherche, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbVilleRecherche, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbSpecialiteRecherche, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox4.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.tIdenIdCNAM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCleIdCNAM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbVille, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tIdCNAM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tMail, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbSpecialiteMedecin, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tAdresseMedein, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tFaxMedecin, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tTelMedecin, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNomMedecin, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodeMedecin, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gMedecin, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.Controls.Add(Me.bImprimer)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Controls.Add(Me.GroupBox4)
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.GroupBox2)
        Me.Panel.Controls.Add(Me.gMedecin)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1121, 516)
        Me.Panel.TabIndex = 1
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.tIdCnamRecherce)
        Me.GroupBox1.Controls.Add(Me.Label14)
        Me.GroupBox1.Controls.Add(Me.tCodeRecherche)
        Me.GroupBox1.Controls.Add(Me.Label13)
        Me.GroupBox1.Controls.Add(Me.Label12)
        Me.GroupBox1.Controls.Add(Me.tNomRecherche)
        Me.GroupBox1.Controls.Add(Me.Label11)
        Me.GroupBox1.Controls.Add(Me.Label10)
        Me.GroupBox1.Controls.Add(Me.cmbVilleRecherche)
        Me.GroupBox1.Controls.Add(Me.cmbSpecialiteRecherche)
        Me.GroupBox1.Location = New System.Drawing.Point(327, 9)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(604, 107)
        Me.GroupBox1.TabIndex = 94
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Critères de Recherche"
        '
        'tIdCnamRecherce
        '
        Me.tIdCnamRecherce.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tIdCnamRecherce.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tIdCnamRecherce.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tIdCnamRecherce.Location = New System.Drawing.Point(91, 63)
        Me.tIdCnamRecherce.Name = "tIdCnamRecherce"
        Me.tIdCnamRecherce.Size = New System.Drawing.Size(88, 18)
        Me.tIdCnamRecherce.TabIndex = 101
        Me.tIdCnamRecherce.Tag = Nothing
        Me.tIdCnamRecherce.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tIdCnamRecherce.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label14
        '
        Me.Label14.AutoSize = True
        Me.Label14.BackColor = System.Drawing.Color.Transparent
        Me.Label14.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label14.Location = New System.Drawing.Point(7, 66)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(50, 13)
        Me.Label14.TabIndex = 102
        Me.Label14.Text = "Id CNAM"
        '
        'tCodeRecherche
        '
        Me.tCodeRecherche.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodeRecherche.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodeRecherche.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodeRecherche.Location = New System.Drawing.Point(94, 29)
        Me.tCodeRecherche.Name = "tCodeRecherche"
        Me.tCodeRecherche.Size = New System.Drawing.Size(88, 18)
        Me.tCodeRecherche.TabIndex = 99
        Me.tCodeRecherche.Tag = Nothing
        Me.tCodeRecherche.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodeRecherche.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label13
        '
        Me.Label13.AutoSize = True
        Me.Label13.BackColor = System.Drawing.Color.Transparent
        Me.Label13.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label13.Location = New System.Drawing.Point(7, 32)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(76, 13)
        Me.Label13.TabIndex = 100
        Me.Label13.Text = "Code Médecin"
        '
        'Label12
        '
        Me.Label12.AutoSize = True
        Me.Label12.BackColor = System.Drawing.Color.Transparent
        Me.Label12.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label12.Location = New System.Drawing.Point(193, 32)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(88, 13)
        Me.Label12.TabIndex = 98
        Me.Label12.Text = "Nom du Médecin"
        '
        'tNomRecherche
        '
        Me.tNomRecherche.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNomRecherche.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNomRecherche.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNomRecherche.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNomRecherche.Location = New System.Drawing.Point(287, 29)
        Me.tNomRecherche.Name = "tNomRecherche"
        Me.tNomRecherche.Size = New System.Drawing.Size(275, 18)
        Me.tNomRecherche.TabIndex = 97
        Me.tNomRecherche.Tag = Nothing
        Me.tNomRecherche.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNomRecherche.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.BackColor = System.Drawing.Color.Transparent
        Me.Label11.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label11.Location = New System.Drawing.Point(193, 65)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(53, 13)
        Me.Label11.TabIndex = 96
        Me.Label11.Text = "Spécialité"
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.BackColor = System.Drawing.Color.Transparent
        Me.Label10.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label10.Location = New System.Drawing.Point(403, 65)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(26, 13)
        Me.Label10.TabIndex = 95
        Me.Label10.Text = "Ville"
        '
        'cmbVilleRecherche
        '
        Me.cmbVilleRecherche.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbVilleRecherche.Caption = ""
        Me.cmbVilleRecherche.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbVilleRecherche.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbVilleRecherche.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbVilleRecherche.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbVilleRecherche.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbVilleRecherche.Images.Add(CType(resources.GetObject("cmbVilleRecherche.Images"), System.Drawing.Image))
        Me.cmbVilleRecherche.Location = New System.Drawing.Point(435, 62)
        Me.cmbVilleRecherche.MatchEntryTimeout = CType(2000, Long)
        Me.cmbVilleRecherche.MaxDropDownItems = CType(5, Short)
        Me.cmbVilleRecherche.MaxLength = 32767
        Me.cmbVilleRecherche.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbVilleRecherche.Name = "cmbVilleRecherche"
        Me.cmbVilleRecherche.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbVilleRecherche.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbVilleRecherche.Size = New System.Drawing.Size(127, 22)
        Me.cmbVilleRecherche.TabIndex = 42
        Me.cmbVilleRecherche.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbVilleRecherche.PropBag = resources.GetString("cmbVilleRecherche.PropBag")
        '
        'cmbSpecialiteRecherche
        '
        Me.cmbSpecialiteRecherche.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbSpecialiteRecherche.Caption = ""
        Me.cmbSpecialiteRecherche.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbSpecialiteRecherche.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbSpecialiteRecherche.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbSpecialiteRecherche.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbSpecialiteRecherche.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbSpecialiteRecherche.Images.Add(CType(resources.GetObject("cmbSpecialiteRecherche.Images"), System.Drawing.Image))
        Me.cmbSpecialiteRecherche.Location = New System.Drawing.Point(257, 62)
        Me.cmbSpecialiteRecherche.MatchEntryTimeout = CType(2000, Long)
        Me.cmbSpecialiteRecherche.MaxDropDownItems = CType(5, Short)
        Me.cmbSpecialiteRecherche.MaxLength = 32767
        Me.cmbSpecialiteRecherche.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbSpecialiteRecherche.Name = "cmbSpecialiteRecherche"
        Me.cmbSpecialiteRecherche.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbSpecialiteRecherche.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbSpecialiteRecherche.Size = New System.Drawing.Size(139, 22)
        Me.cmbSpecialiteRecherche.TabIndex = 40
        Me.cmbSpecialiteRecherche.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbSpecialiteRecherche.PropBag = resources.GetString("cmbSpecialiteRecherche.PropBag")
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.lTitre)
        Me.GroupBox4.Location = New System.Drawing.Point(12, 9)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(305, 107)
        Me.GroupBox4.TabIndex = 93
        Me.GroupBox4.TabStop = False
        Me.GroupBox4.Text = "Liste Des Medecins"
        '
        'lTitre
        '
        Me.lTitre.Font = New System.Drawing.Font("Microsoft Sans Serif", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTitre.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.lTitre.Location = New System.Drawing.Point(21, 12)
        Me.lTitre.Name = "lTitre"
        Me.lTitre.Size = New System.Drawing.Size(271, 87)
        Me.lTitre.TabIndex = 92
        Me.lTitre.Text = "LISTE DES MEDECINS"
        Me.lTitre.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.Location = New System.Drawing.Point(999, 16)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(105, 45)
        Me.bQuitter.TabIndex = 91
        Me.bQuitter.Text = "Fermer            F12"
        Me.bQuitter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox2
        '
        Me.GroupBox2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox2.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox2.Controls.Add(Me.Label16)
        Me.GroupBox2.Controls.Add(Me.Label15)
        Me.GroupBox2.Controls.Add(Me.tIdenIdCNAM)
        Me.GroupBox2.Controls.Add(Me.tCleIdCNAM)
        Me.GroupBox2.Controls.Add(Me.chkBloquer)
        Me.GroupBox2.Controls.Add(Me.cmbVille)
        Me.GroupBox2.Controls.Add(Me.Label3)
        Me.GroupBox2.Controls.Add(Me.bSupprimerMedecin)
        Me.GroupBox2.Controls.Add(Me.bAnnulerMedecin)
        Me.GroupBox2.Controls.Add(Me.bModifierMedecin)
        Me.GroupBox2.Controls.Add(Me.bAjouterMedecin)
        Me.GroupBox2.Controls.Add(Me.tIdCNAM)
        Me.GroupBox2.Controls.Add(Me.Label9)
        Me.GroupBox2.Controls.Add(Me.tMail)
        Me.GroupBox2.Controls.Add(Me.Label2)
        Me.GroupBox2.Controls.Add(Me.Label1)
        Me.GroupBox2.Controls.Add(Me.cmbSpecialiteMedecin)
        Me.GroupBox2.Controls.Add(Me.bConfirmerMedecin)
        Me.GroupBox2.Controls.Add(Me.tAdresseMedein)
        Me.GroupBox2.Controls.Add(Me.tFaxMedecin)
        Me.GroupBox2.Controls.Add(Me.tTelMedecin)
        Me.GroupBox2.Controls.Add(Me.Label8)
        Me.GroupBox2.Controls.Add(Me.Label7)
        Me.GroupBox2.Controls.Add(Me.Label6)
        Me.GroupBox2.Controls.Add(Me.tNomMedecin)
        Me.GroupBox2.Controls.Add(Me.Label4)
        Me.GroupBox2.Controls.Add(Me.tCodeMedecin)
        Me.GroupBox2.Controls.Add(Me.Label5)
        Me.GroupBox2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox2.Location = New System.Drawing.Point(15, 360)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(1094, 139)
        Me.GroupBox2.TabIndex = 1
        Me.GroupBox2.TabStop = False
        '
        'Label16
        '
        Me.Label16.AutoSize = True
        Me.Label16.BackColor = System.Drawing.Color.Transparent
        Me.Label16.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label16.Location = New System.Drawing.Point(225, 23)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(12, 13)
        Me.Label16.TabIndex = 100
        Me.Label16.Text = "/"
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.BackColor = System.Drawing.Color.Transparent
        Me.Label15.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label15.Location = New System.Drawing.Point(132, 23)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(12, 13)
        Me.Label15.TabIndex = 99
        Me.Label15.Text = "/"
        '
        'tIdenIdCNAM
        '
        Me.tIdenIdCNAM.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tIdenIdCNAM.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tIdenIdCNAM.Enabled = False
        Me.tIdenIdCNAM.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tIdenIdCNAM.Location = New System.Drawing.Point(109, 21)
        Me.tIdenIdCNAM.MaxLength = 1
        Me.tIdenIdCNAM.Name = "tIdenIdCNAM"
        Me.tIdenIdCNAM.Size = New System.Drawing.Size(19, 18)
        Me.tIdenIdCNAM.TabIndex = 98
        Me.tIdenIdCNAM.Tag = Nothing
        Me.tIdenIdCNAM.Value = ""
        Me.tIdenIdCNAM.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tIdenIdCNAM.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tCleIdCNAM
        '
        Me.tCleIdCNAM.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCleIdCNAM.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCleIdCNAM.Enabled = False
        Me.tCleIdCNAM.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCleIdCNAM.Location = New System.Drawing.Point(241, 21)
        Me.tCleIdCNAM.MaxLength = 2
        Me.tCleIdCNAM.Name = "tCleIdCNAM"
        Me.tCleIdCNAM.Size = New System.Drawing.Size(34, 18)
        Me.tCleIdCNAM.TabIndex = 97
        Me.tCleIdCNAM.Tag = Nothing
        Me.tCleIdCNAM.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCleIdCNAM.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'chkBloquer
        '
        Me.chkBloquer.Enabled = False
        Me.chkBloquer.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chkBloquer.Location = New System.Drawing.Point(795, 22)
        Me.chkBloquer.Name = "chkBloquer"
        Me.chkBloquer.Size = New System.Drawing.Size(75, 17)
        Me.chkBloquer.TabIndex = 96
        Me.chkBloquer.Text = "Bloquer"
        Me.chkBloquer.UseVisualStyleBackColor = True
        '
        'cmbVille
        '
        Me.cmbVille.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbVille.Caption = ""
        Me.cmbVille.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbVille.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.cmbVille.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbVille.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbVille.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbVille.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbVille.Enabled = False
        Me.cmbVille.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbVille.Images.Add(CType(resources.GetObject("cmbVille.Images"), System.Drawing.Image))
        Me.cmbVille.Location = New System.Drawing.Point(569, 45)
        Me.cmbVille.MatchEntryTimeout = CType(2000, Long)
        Me.cmbVille.MaxDropDownItems = CType(5, Short)
        Me.cmbVille.MaxLength = 32767
        Me.cmbVille.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbVille.Name = "cmbVille"
        Me.cmbVille.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbVille.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbVille.Size = New System.Drawing.Size(214, 21)
        Me.cmbVille.TabIndex = 95
        Me.cmbVille.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbVille.PropBag = resources.GetString("cmbVille.PropBag")
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.BackColor = System.Drawing.Color.Transparent
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.Location = New System.Drawing.Point(507, 48)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(26, 13)
        Me.Label3.TabIndex = 94
        Me.Label3.Text = "Ville"
        '
        'bSupprimerMedecin
        '
        Me.bSupprimerMedecin.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bSupprimerMedecin.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimerMedecin.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler21
        Me.bSupprimerMedecin.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSupprimerMedecin.Location = New System.Drawing.Point(988, 78)
        Me.bSupprimerMedecin.Name = "bSupprimerMedecin"
        Me.bSupprimerMedecin.Size = New System.Drawing.Size(100, 45)
        Me.bSupprimerMedecin.TabIndex = 0
        Me.bSupprimerMedecin.Text = "Supprimer"
        Me.bSupprimerMedecin.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSupprimerMedecin.UseVisualStyleBackColor = True
        Me.bSupprimerMedecin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAnnulerMedecin
        '
        Me.bAnnulerMedecin.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnulerMedecin.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnulerMedecin.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler21
        Me.bAnnulerMedecin.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnulerMedecin.Location = New System.Drawing.Point(988, 78)
        Me.bAnnulerMedecin.Name = "bAnnulerMedecin"
        Me.bAnnulerMedecin.Size = New System.Drawing.Size(100, 45)
        Me.bAnnulerMedecin.TabIndex = 93
        Me.bAnnulerMedecin.Text = "Annuler"
        Me.bAnnulerMedecin.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAnnulerMedecin.UseVisualStyleBackColor = True
        Me.bAnnulerMedecin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bModifierMedecin
        '
        Me.bModifierMedecin.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bModifierMedecin.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bModifierMedecin.Image = Global.Pharma2000Premium.My.Resources.Resources.changer_
        Me.bModifierMedecin.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bModifierMedecin.Location = New System.Drawing.Point(882, 78)
        Me.bModifierMedecin.Name = "bModifierMedecin"
        Me.bModifierMedecin.Size = New System.Drawing.Size(100, 45)
        Me.bModifierMedecin.TabIndex = 92
        Me.bModifierMedecin.Text = "Modifier"
        Me.bModifierMedecin.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bModifierMedecin.UseVisualStyleBackColor = True
        Me.bModifierMedecin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAjouterMedecin
        '
        Me.bAjouterMedecin.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouterMedecin.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouterMedecin.Image = Global.Pharma2000Premium.My.Resources.Resources.nouveau1
        Me.bAjouterMedecin.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouterMedecin.Location = New System.Drawing.Point(882, 21)
        Me.bAjouterMedecin.Name = "bAjouterMedecin"
        Me.bAjouterMedecin.Size = New System.Drawing.Size(100, 45)
        Me.bAjouterMedecin.TabIndex = 91
        Me.bAjouterMedecin.Text = "Ajouter"
        Me.bAjouterMedecin.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouterMedecin.UseVisualStyleBackColor = True
        Me.bAjouterMedecin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tIdCNAM
        '
        Me.tIdCNAM.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tIdCNAM.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tIdCNAM.Enabled = False
        Me.tIdCNAM.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tIdCNAM.Location = New System.Drawing.Point(148, 22)
        Me.tIdCNAM.MaxLength = 8
        Me.tIdCNAM.Name = "tIdCNAM"
        Me.tIdCNAM.Size = New System.Drawing.Size(73, 18)
        Me.tIdCNAM.TabIndex = 7
        Me.tIdCNAM.Tag = Nothing
        Me.tIdCNAM.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tIdCNAM.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.BackColor = System.Drawing.Color.Transparent
        Me.Label9.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.Location = New System.Drawing.Point(11, 23)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(50, 13)
        Me.Label9.TabIndex = 90
        Me.Label9.Text = "Id CNAM"
        '
        'tMail
        '
        Me.tMail.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tMail.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMail.Enabled = False
        Me.tMail.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tMail.Location = New System.Drawing.Point(336, 75)
        Me.tMail.Name = "tMail"
        Me.tMail.Size = New System.Drawing.Size(165, 18)
        Me.tMail.TabIndex = 6
        Me.tMail.Tag = Nothing
        Me.tMail.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tMail.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.BackColor = System.Drawing.Color.Transparent
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(292, 77)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(26, 13)
        Me.Label2.TabIndex = 88
        Me.Label2.Text = "Mail"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(11, 107)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(53, 13)
        Me.Label1.TabIndex = 86
        Me.Label1.Text = "Spécialité"
        '
        'cmbSpecialiteMedecin
        '
        Me.cmbSpecialiteMedecin.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbSpecialiteMedecin.Caption = ""
        Me.cmbSpecialiteMedecin.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbSpecialiteMedecin.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.cmbSpecialiteMedecin.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbSpecialiteMedecin.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbSpecialiteMedecin.EditorFont = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbSpecialiteMedecin.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbSpecialiteMedecin.Enabled = False
        Me.cmbSpecialiteMedecin.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbSpecialiteMedecin.Images.Add(CType(resources.GetObject("cmbSpecialiteMedecin.Images"), System.Drawing.Image))
        Me.cmbSpecialiteMedecin.Location = New System.Drawing.Point(109, 102)
        Me.cmbSpecialiteMedecin.MatchEntryTimeout = CType(2000, Long)
        Me.cmbSpecialiteMedecin.MaxDropDownItems = CType(5, Short)
        Me.cmbSpecialiteMedecin.MaxLength = 32767
        Me.cmbSpecialiteMedecin.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbSpecialiteMedecin.Name = "cmbSpecialiteMedecin"
        Me.cmbSpecialiteMedecin.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbSpecialiteMedecin.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbSpecialiteMedecin.Size = New System.Drawing.Size(392, 21)
        Me.cmbSpecialiteMedecin.TabIndex = 3
        Me.cmbSpecialiteMedecin.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbSpecialiteMedecin.PropBag = resources.GetString("cmbSpecialiteMedecin.PropBag")
        '
        'bConfirmerMedecin
        '
        Me.bConfirmerMedecin.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bConfirmerMedecin.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmerMedecin.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bConfirmerMedecin.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmerMedecin.Location = New System.Drawing.Point(988, 21)
        Me.bConfirmerMedecin.Name = "bConfirmerMedecin"
        Me.bConfirmerMedecin.Size = New System.Drawing.Size(100, 45)
        Me.bConfirmerMedecin.TabIndex = 9
        Me.bConfirmerMedecin.Text = "Confirmer"
        Me.bConfirmerMedecin.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bConfirmerMedecin.UseVisualStyleBackColor = True
        Me.bConfirmerMedecin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tAdresseMedein
        '
        Me.tAdresseMedein.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tAdresseMedein.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tAdresseMedein.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tAdresseMedein.Enabled = False
        Me.tAdresseMedein.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tAdresseMedein.Location = New System.Drawing.Point(569, 72)
        Me.tAdresseMedein.Multiline = True
        Me.tAdresseMedein.Name = "tAdresseMedein"
        Me.tAdresseMedein.Size = New System.Drawing.Size(214, 51)
        Me.tAdresseMedein.TabIndex = 8
        Me.tAdresseMedein.Tag = Nothing
        Me.tAdresseMedein.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tAdresseMedein.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tFaxMedecin
        '
        Me.tFaxMedecin.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tFaxMedecin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tFaxMedecin.Enabled = False
        Me.tFaxMedecin.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tFaxMedecin.Location = New System.Drawing.Point(336, 48)
        Me.tFaxMedecin.Name = "tFaxMedecin"
        Me.tFaxMedecin.Size = New System.Drawing.Size(165, 18)
        Me.tFaxMedecin.TabIndex = 5
        Me.tFaxMedecin.Tag = Nothing
        Me.tFaxMedecin.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tFaxMedecin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tTelMedecin
        '
        Me.tTelMedecin.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tTelMedecin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTelMedecin.Enabled = False
        Me.tTelMedecin.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tTelMedecin.Location = New System.Drawing.Point(336, 21)
        Me.tTelMedecin.Name = "tTelMedecin"
        Me.tTelMedecin.Size = New System.Drawing.Size(165, 18)
        Me.tTelMedecin.TabIndex = 4
        Me.tTelMedecin.Tag = Nothing
        Me.tTelMedecin.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tTelMedecin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.BackColor = System.Drawing.Color.Transparent
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.Location = New System.Drawing.Point(507, 72)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(45, 13)
        Me.Label8.TabIndex = 15
        Me.Label8.Text = "Adresse"
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.BackColor = System.Drawing.Color.Transparent
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.Location = New System.Drawing.Point(292, 50)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(24, 13)
        Me.Label7.TabIndex = 14
        Me.Label7.Text = "Fax"
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.BackColor = System.Drawing.Color.Transparent
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.Location = New System.Drawing.Point(291, 24)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(25, 13)
        Me.Label6.TabIndex = 13
        Me.Label6.Text = "Tel "
        '
        'tNomMedecin
        '
        Me.tNomMedecin.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNomMedecin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNomMedecin.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNomMedecin.Enabled = False
        Me.tNomMedecin.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNomMedecin.Location = New System.Drawing.Point(110, 72)
        Me.tNomMedecin.Name = "tNomMedecin"
        Me.tNomMedecin.Size = New System.Drawing.Size(165, 18)
        Me.tNomMedecin.TabIndex = 2
        Me.tNomMedecin.Tag = Nothing
        Me.tNomMedecin.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNomMedecin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.BackColor = System.Drawing.Color.Transparent
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.Location = New System.Drawing.Point(11, 75)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(88, 13)
        Me.Label4.TabIndex = 12
        Me.Label4.Text = "Nom du Médecin"
        '
        'tCodeMedecin
        '
        Me.tCodeMedecin.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodeMedecin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodeMedecin.Enabled = False
        Me.tCodeMedecin.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodeMedecin.Location = New System.Drawing.Point(618, 18)
        Me.tCodeMedecin.MaxLength = 10
        Me.tCodeMedecin.Name = "tCodeMedecin"
        Me.tCodeMedecin.Size = New System.Drawing.Size(165, 18)
        Me.tCodeMedecin.TabIndex = 0
        Me.tCodeMedecin.Tag = Nothing
        Me.tCodeMedecin.Visible = False
        Me.tCodeMedecin.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodeMedecin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.BackColor = System.Drawing.Color.Transparent
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.Location = New System.Drawing.Point(519, 21)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(76, 13)
        Me.Label5.TabIndex = 8
        Me.Label5.Text = "Code Médecin"
        Me.Label5.Visible = False
        '
        'gMedecin
        '
        Me.gMedecin.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gMedecin.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gMedecin.GroupByCaption = "Drag a column header here to group by that column"
        Me.gMedecin.Images.Add(CType(resources.GetObject("gMedecin.Images"), System.Drawing.Image))
        Me.gMedecin.Location = New System.Drawing.Point(12, 122)
        Me.gMedecin.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gMedecin.Name = "gMedecin"
        Me.gMedecin.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gMedecin.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gMedecin.PreviewInfo.ZoomFactor = 75.0R
        Me.gMedecin.PrintInfo.PageSettings = CType(resources.GetObject("gMedecin.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gMedecin.Size = New System.Drawing.Size(1097, 219)
        Me.gMedecin.TabIndex = 0
        Me.gMedecin.Text = "C1TrueDBGrid4"
        Me.gMedecin.PropBag = resources.GetString("gMedecin.PropBag")
        '
        'bImprimer
        '
        Me.bImprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.imprmante1
        Me.bImprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bImprimer.Location = New System.Drawing.Point(998, 67)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(106, 45)
        Me.bImprimer.TabIndex = 101
        Me.bImprimer.Text = "Imprimer                F9"
        Me.bImprimer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fMedecin
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.ClientSize = New System.Drawing.Size(1121, 516)
        Me.Controls.Add(Me.Panel)
        Me.Font = New System.Drawing.Font("Calibri", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Name = "fMedecin"
        Me.Text = "fMedecin"
        Me.Panel.ResumeLayout(False)
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.tIdCnamRecherce, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodeRecherche, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNomRecherche, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbVilleRecherche, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbSpecialiteRecherche, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.tIdenIdCNAM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCleIdCNAM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbVille, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tIdCNAM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tMail, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbSpecialiteMedecin, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tAdresseMedein, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tFaxMedecin, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tTelMedecin, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNomMedecin, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodeMedecin, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gMedecin, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents bSupprimerMedecin As C1.Win.C1Input.C1Button
    Friend WithEvents gMedecin As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents tAdresseMedein As C1.Win.C1Input.C1TextBox
    Friend WithEvents tFaxMedecin As C1.Win.C1Input.C1TextBox
    Friend WithEvents tTelMedecin As C1.Win.C1Input.C1TextBox
    Friend WithEvents bConfirmerMedecin As C1.Win.C1Input.C1Button
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents tNomMedecin As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents tCodeMedecin As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents tMail As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents cmbSpecialiteMedecin As C1.Win.C1List.C1Combo
    Friend WithEvents tIdCNAM As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents lTitre As System.Windows.Forms.Label
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents bAjouterMedecin As C1.Win.C1Input.C1Button
    Friend WithEvents bModifierMedecin As C1.Win.C1Input.C1Button
    Friend WithEvents bAnnulerMedecin As C1.Win.C1Input.C1Button
    Friend WithEvents cmbVille As C1.Win.C1List.C1Combo
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents tNomRecherche As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents cmbVilleRecherche As C1.Win.C1List.C1Combo
    Friend WithEvents cmbSpecialiteRecherche As C1.Win.C1List.C1Combo
    Friend WithEvents tCodeRecherche As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents tIdCnamRecherce As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents chkBloquer As System.Windows.Forms.CheckBox
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents tIdenIdCNAM As C1.Win.C1Input.C1TextBox
    Friend WithEvents tCleIdCNAM As C1.Win.C1Input.C1TextBox
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
End Class
