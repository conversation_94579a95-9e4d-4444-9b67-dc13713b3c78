@echo off
echo ========================================
echo    LANCEMENT SIMPLE AVEC DEBUG
echo ========================================
echo.

cd /d "%~dp0"

if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe" (
    echo Executable trouve - Lancement...
    echo.
    
    cd "PharmaModerne.UI\bin\Debug\net9.0-windows"
    
    echo Demarrage de l'application...
    start "" "PharmaModerne.UI.exe"
    
    echo.
    echo Application lancee !
    echo.
    echo TESTS A EFFECTUER :
    echo 1. Verifier que l'interface s'ouvre
    echo 2. Cliquer sur le bouton Scanner (orange)
    echo 3. Naviguer dans le menu de gauche
    echo 4. Tester les modules Point de Vente, Clients, Articles
    echo 5. Verifier le Dashboard
    echo.
    echo Si vous voyez des erreurs, notez-les et fermez l'application.
    echo.
    
    cd ..\..\..\..\
    
) else (
    echo Executable non trouve !
    echo Recompilation necessaire...
    dotnet build PharmaModerne.UI\PharmaModerne.UI.csproj --configuration Debug
)

echo.
echo ERREURS COMMUNES ET SOLUTIONS :
echo.
echo 1. Interface ne s'affiche pas :
echo    - Installer .NET 9.0 Runtime
echo    - Redemarrer en administrateur
echo.
echo 2. Boutons ne repondent pas :
echo    - Normal pour certains modules (demo)
echo.
echo 3. Scanner ne fonctionne pas :
echo    - Normal, c'est une simulation
echo.
echo 4. Modules vides :
echo    - Normal, version de demonstration
echo.
echo 5. Erreurs de navigation :
echo    - Recompiler le projet
echo.

pause
