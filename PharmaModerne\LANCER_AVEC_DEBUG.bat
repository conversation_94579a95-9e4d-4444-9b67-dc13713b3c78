@echo off
echo ========================================
echo    LANCEMENT PHARMA2000 AVEC DEBUG
echo    Capture des erreurs d'interface
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 Préparation du lancement avec debug...
echo.

if exist "PharmaModerne.UI\bin\Debug\net9.0-windows\PharmaModerne.UI.exe" (
    echo ✅ Exécutable trouvé
    echo.
    
    echo 🚀 Lancement de PHARMA2000 Moderne avec debug...
    echo.
    echo 💡 INSTRUCTIONS :
    echo 1. L'application va s'ouvrir
    echo 2. Testez les fonctionnalités
    echo 3. Si vous voyez des erreurs, notez-les
    echo 4. Fermez l'application pour revenir ici
    echo.
    echo ⏰ Lancement dans 3 secondes...
    timeout /t 3 /nobreak >nul
    
    cd "PharmaModerne.UI\bin\Debug\net9.0-windows"
    
    REM Créer un script PowerShell pour lancer avec debug
    echo # Script de lancement avec debug > launch_debug.ps1
    echo try { >> launch_debug.ps1
    echo     Write-Host "🚀 Démarrage de PHARMA2000 Moderne..." >> launch_debug.ps1
    echo     $process = Start-Process -FilePath ".\PharmaModerne.UI.exe" -PassThru >> launch_debug.ps1
    echo     Write-Host "✅ Application démarrée (PID: $($process.Id))" >> launch_debug.ps1
    echo     Write-Host "⏳ En attente de fermeture de l'application..." >> launch_debug.ps1
    echo     $process.WaitForExit() >> launch_debug.ps1
    echo     Write-Host "📝 Code de sortie: $($process.ExitCode)" >> launch_debug.ps1
    echo     if ($process.ExitCode -eq 0) { >> launch_debug.ps1
    echo         Write-Host "✅ Application fermée normalement" >> launch_debug.ps1
    echo     } else { >> launch_debug.ps1
    echo         Write-Host "⚠️ Application fermée avec code d'erreur: $($process.ExitCode)" >> launch_debug.ps1
    echo     } >> launch_debug.ps1
    echo } catch { >> launch_debug.ps1
    echo     Write-Host "❌ Erreur lors du lancement:" >> launch_debug.ps1
    echo     Write-Host "Type: $($_.Exception.GetType().Name)" >> launch_debug.ps1
    echo     Write-Host "Message: $($_.Exception.Message)" >> launch_debug.ps1
    echo     Write-Host "Stack Trace:" >> launch_debug.ps1
    echo     Write-Host $_.Exception.StackTrace >> launch_debug.ps1
    echo } >> launch_debug.ps1
    
    powershell -ExecutionPolicy Bypass -File launch_debug.ps1
    
    echo.
    echo ========================================
    echo    RAPPORT POST-EXECUTION
    echo ========================================
    echo.
    
    echo 📋 TESTS À EFFECTUER DANS L'APPLICATION :
    echo.
    echo 1. ✅ Vérifier que l'interface s'affiche correctement
    echo 2. ✅ Tester le bouton "Scanner" en haut à droite
    echo 3. ✅ Naviguer entre les modules du menu de gauche
    echo 4. ✅ Tester la recherche globale
    echo 5. ✅ Vérifier le Dashboard avec les statistiques
    echo 6. ✅ Tester le Point de Vente
    echo 7. ✅ Vérifier la liste des clients
    echo 8. ✅ Tester la liste des articles
    echo.
    
    echo 🔍 ERREURS COMMUNES À RECHERCHER :
    echo.
    echo • Interface qui ne s'affiche pas
    echo • Boutons qui ne répondent pas
    echo • Erreurs dans les modules
    echo • Scanner qui ne fonctionne pas
    echo • Navigation qui plante
    echo • Données qui ne s'affichent pas
    echo.
    
    REM Nettoyer
    del launch_debug.ps1 2>nul
    
    cd ..\..\..\..\
    
    echo 💬 AVEZ-VOUS RENCONTRÉ DES ERREURS ?
    echo.
    echo Si oui, voici les solutions communes :
    echo.
    echo 🔧 SOLUTIONS FRÉQUENTES :
    echo.
    echo 1. ERREUR "Impossible de charger les modules" :
    echo    - Recompiler : dotnet build --configuration Debug
    echo.
    echo 2. ERREUR "Scanner non disponible" :
    echo    - Normal, c'est une simulation pour les tests
    echo.
    echo 3. ERREUR "Base de données non trouvée" :
    echo    - Normal, l'app fonctionne sans BD pour les tests
    echo.
    echo 4. ERREUR "Interface ne s'affiche pas" :
    echo    - Vérifier .NET 9.0 Runtime installé
    echo    - Redémarrer en tant qu'administrateur
    echo.
    echo 5. ERREUR "Modules vides" :
    echo    - Normal, c'est une version de démonstration
    echo.
    
) else (
    echo ❌ Exécutable non trouvé !
    echo.
    echo 🔧 SOLUTION :
    echo Recompiler le projet avec :
    echo dotnet build PharmaModerne.UI\PharmaModerne.UI.csproj --configuration Debug
)

echo.
echo Appuyez sur une touche pour fermer...
pause >nul
