﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fSituationArticle

    Dim cmdSituationArticle As New SqlCommand
    Dim daSituationArticle As New SqlDataAdapter
    Dim cbSituationArticle As New SqlCommandBuilder
    Dim dsSituationArticle As New DataSet

    'une autre dataadapter pour le test de l'existance du clé primaire
    Dim daSituationArticle1 As New SqlDataAdapter

    Dim xSituationArticle As Integer
    Dim ModeSituationArticle As String
    Dim NomSituationArticle As String

    Dim CodeExiste As Boolean = False

    Public Sub Init()
        afficherSituationArticle()
    End Sub

    Public Sub afficherSituationArticle()
        Dim I As Integer
        Dim Cond As String = "1=1"
        dsSituationArticle.Clear()
        cmdSituationArticle.CommandText = " SELECT " + _
                                          " CodeSituationArticle, " + _
                                          " LibelleSituationArticle " + _
                                          " FROM SITUATION_ARTICLE as SITUATION_ARTICLE_LISTE " + _
                                          "WHERE " + Cond + " ORDER BY CodeSituationArticle"

        cmdSituationArticle.Connection = ConnectionServeur
        daSituationArticle = New SqlDataAdapter(cmdSituationArticle)
        daSituationArticle.Fill(dsSituationArticle, "SITUATION_ARTICLE_LISTE")

        With gSituationArticle
            .Columns.Clear()
            .DataSource = dsSituationArticle
            .DataMember = "SITUATION_ARTICLE_LISTE"
            .Rebind(False)
            .Columns("CodeSituationArticle").Caption = "Code de la SituationArticle"
            .Columns("LibelleSituationArticle").Caption = "Libelle de la SituationArticle"
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("CodeSituationArticle").Width = 120
            .Splits(0).DisplayColumns("CodeSituationArticle").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("LibelleSituationArticle").Width = 80
            .Splits(0).DisplayColumns("LibelleSituationArticle").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gSituationArticle)
        End With
        gSituationArticle.MoveRelative(xSituationArticle)
        cbSituationArticle = New SqlCommandBuilder(daSituationArticle)
    End Sub

    Private Sub bAjouterSituationArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterSituationArticle.Click
        Dim dr As DataRow
        Dim StrSQL As String = ""

        If tCodeSituationArticle.Text = "" Then
            MsgBox("Veuillez saisir le code de la Situation Article !", MsgBoxStyle.Critical, "Erreur")
            tCodeSituationArticle.Focus()
            Exit Sub
        End If
        If tLibelleSituationArticle.Text = "" Then
            MsgBox("Veuillez saisir le libelle de la Situation Article !", MsgBoxStyle.Critical, "Erreur")
            tLibelleSituationArticle.Focus()
            Exit Sub
        End If

        TestExiste()
        If CodeExiste = True Then
            MsgBox("Code situation existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
            tCodeSituationArticle.Focus()
            Exit Sub
        End If

        With dsSituationArticle
            dr = .Tables("SITUATION_ARTICLE_LISTE").NewRow
            dr.Item("LibelleSituationArticle") = tLibelleSituationArticle.Text
            dr.Item("CodeSituationArticle") = tCodeSituationArticle.Text
            .Tables("SITUATION_ARTICLE_LISTE").Rows.Add(dr)
        End With

        Try
            daSituationArticle.Update(dsSituationArticle, "SITUATION_ARTICLE_LISTE")
            afficherSituationArticle()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsSituationArticle.Reset()
        End Try

        tCodeSituationArticle.Text = ""
        tLibelleSituationArticle.Text = ""
    End Sub

    Private Sub bSupprimerSituationArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerSituationArticle.Click
        Dim cmd As New SqlCommand
        If gSituationArticle.RowCount > 0 Then
            If MsgBox("Voulez vous vraiment supprimer cette Situation " + Quote(gSituationArticle(gSituationArticle.Row, "LibelleSituationArticle")) + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM SITUATION_ARTICLE WHERE CodeSituationArticle =" + Quote(gSituationArticle(gSituationArticle.Row, "CodeSituationArticle"))
                    cmd.ExecuteNonQuery()
                    afficherSituationArticle()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub

    Private Sub gSituationArticle_AfterColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gSituationArticle.AfterColEdit
        Dim dr As DataRow
        Dim StrSQL As String = ""

        With dsSituationArticle.Tables("SITUATION_ARTICLE")
            dr = .Rows(0)
            dr.Item("LibelleSituationArticle") = gSituationArticle(gSituationArticle.Row, "LibelleSituationArticle")

        End With

        Try
            daSituationArticle.Update(dsSituationArticle, "SITUATION_ARTICLE")

        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            afficherSituationArticle()
        End Try
    End Sub

    Private Sub gSituationArticle_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles gSituationArticle.Click
        Dim StrSQL As String = ""
        NomSituationArticle = Quote(gSituationArticle(gSituationArticle.Row, "LibelleSituationArticle"))
        If NomSituationArticle = "" Then
            MsgBox("Veuillez sélectionner le libelle de la Situation d'Article !", MsgBoxStyle.Critical, "Erreur")
            gSituationArticle.Focus()
            Exit Sub
        End If

        If (dsSituationArticle.Tables.IndexOf("SITUATION_ARTICLE") > -1) Then
            dsSituationArticle.Tables("SITUATION_ARTICLE").Clear()
        End If

        StrSQL = " SELECT * FROM SITUATION_ARTICLE WHERE LibelleSituationArticle = " + NomSituationArticle

        cmdSituationArticle.Connection = ConnectionServeur
        cmdSituationArticle.CommandText = StrSQL
        daSituationArticle = New SqlDataAdapter(cmdSituationArticle)
        daSituationArticle.Fill(dsSituationArticle, "SITUATION_ARTICLE")
        cbSituationArticle = New SqlCommandBuilder(daSituationArticle)
    End Sub
    Private Sub TestExiste()

        If tCodeSituationArticle.Text <> "" Then
            If IsNumeric(tCodeSituationArticle.Text.Substring(Len(tCodeSituationArticle.Text) - 1, 1)) = False Then
                MsgBox("Veuillez introduire des chiffres !", MsgBoxStyle.Critical, "Erreur")
                tCodeSituationArticle.Text = tCodeSituationArticle.Text.Substring(0, Len(tCodeSituationArticle.Text) - 1)
                tCodeSituationArticle.Select(Len(tCodeSituationArticle.Text), 0)
            End If
        End If

        Dim StrSQLtest As String = ""
        If (dsSituationArticle.Tables.IndexOf("SITUATION_ARTICLE_TEST") > -1) Then
            dsSituationArticle.Tables("SITUATION_ARTICLE_TEST").Clear()
        End If

        StrSQLtest = " SELECT * FROM SITUATION_ARTICLE as SITUATION_ARTICLE_TEST WHERE CodeSituationArticle=" + Quote(tCodeSituationArticle.Text)
        cmdSituationArticle.Connection = ConnectionServeur
        cmdSituationArticle.CommandText = StrSQLtest
        daSituationArticle1 = New SqlDataAdapter(cmdSituationArticle)
        daSituationArticle1.Fill(dsSituationArticle, "SITUATION_ARTICLE_TEST")

        If dsSituationArticle.Tables("SITUATION_ARTICLE_TEST").Rows.Count <> 0 Then
            CodeExiste = True
        Else
            CodeExiste = False
        End If

    End Sub

    Private Sub pSituationArticle_Paint(ByVal sender As System.Object, ByVal e As System.Windows.Forms.PaintEventArgs) Handles Panel.Paint

    End Sub
End Class