using PharmaModerne.Core.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace PharmaModerne.Services
{
    /// <summary>
    /// Service de gestion du scanner de codes à barres avec détection automatique
    /// </summary>
    public class ScannerService : IScannerService
    {
        private readonly List<ScanHistoryItem> _scanHistory = new();
        private ScannerConfig _config = new();
        private DateTime _lastInputTime = DateTime.MinValue;
        private string _lastScannedCode = string.Empty;
        private int _scanCount = 0;
        private bool _isActive = false;

        #region Événements

        public event EventHandler<string>? CodeScanned;
        public event EventHandler<object>? ScannerActivated;
        public event EventHandler<object>? ScannerDeactivated;
        public event EventHandler<object>? ScannerError;

        #endregion

        #region Propriétés

        public bool IsActive => _isActive;
        public bool IsConnected => true; // Toujours connecté pour un scanner clavier
        public string LastScannedCode => _lastScannedCode;
        public DateTime? LastScanTime => _lastInputTime != DateTime.MinValue ? _lastInputTime : null;
        public int ScanCount => _scanCount;

        #endregion

        #region Activation/Désactivation

        public async Task<bool> ActivateAsync()
        {
            try
            {
                _isActive = true;
                ScannerActivated?.Invoke(this, "Scanner activé avec succès");

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                ScannerError?.Invoke(this, $"Erreur lors de l'activation du scanner: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeactivateAsync()
        {
            try
            {
                _isActive = false;
                ScannerDeactivated?.Invoke(this, "Scanner désactivé");

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                ScannerError?.Invoke(this, $"Erreur lors de la désactivation du scanner: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> ToggleAsync()
        {
            return _isActive ? await DeactivateAsync() : await ActivateAsync();
        }

        #endregion

        #region Configuration

        public async Task<bool> ConfigureAsync(ScannerConfig config)
        {
            try
            {
                _config = config ?? new ScannerConfig();
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                ScannerError?.Invoke(this, $"Erreur lors de la configuration du scanner: {ex.Message}");
                return false;
            }
        }

        public async Task<ScannerConfig> GetConfigurationAsync()
        {
            return await Task.FromResult(_config);
        }

        public async Task<bool> TestConnectionAsync()
        {
            // Pour un scanner clavier, toujours connecté
            return await Task.FromResult(true);
        }

        #endregion

        #region Détection et traitement

        public bool DetectScannerInput(string input, TimeSpan inputDuration)
        {
            if (string.IsNullOrWhiteSpace(input))
                return false;

            // Détection basée sur la vitesse de saisie
            // Si la saisie est très rapide (< délai configuré), c'est probablement un scanner
            return inputDuration.TotalMilliseconds < _config.ScanDelay;
        }

        public async Task<bool> ProcessInputAsync(string input, TimeSpan inputDuration)
        {
            if (!_isActive)
                return false;

            try
            {
                var isScanner = DetectScannerInput(input, inputDuration);
                
                if (isScanner || _config.AutoProcessing)
                {
                    var result = await ProcessScannedCodeAsync(input);
                    
                    if (result.Success)
                    {
                        _lastScannedCode = result.CleanedCode;
                        _lastInputTime = DateTime.Now;
                        _scanCount++;

                        // Ajouter à l'historique
                        _scanHistory.Add(new ScanHistoryItem
                        {
                            ScanTime = DateTime.Now,
                            Code = result.CleanedCode,
                            Type = result.Type,
                            Success = true
                        });

                        // Déclencher l'événement
                        CodeScanned?.Invoke(this, result.CleanedCode);
                        
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                ScannerError?.Invoke(this, $"Erreur lors du traitement de l'entrée: {ex.Message}");
                return false;
            }
        }

        public async Task<string> CleanScannedCodeAsync(string rawCode)
        {
            if (string.IsNullOrWhiteSpace(rawCode))
                return string.Empty;

            var cleaned = rawCode;

            // Supprimer les espaces en début et fin
            cleaned = cleaned.Trim();

            // Appliquer le format configuré
            cleaned = await FormatCodeAsync(cleaned, _config.DefaultFormat);

            // Supprimer les caractères non autorisés si configuré
            if (_config.ValidationRules.RequireAlphaNumeric)
            {
                cleaned = Regex.Replace(cleaned, @"[^A-Za-z0-9\-_]", "");
            }

            return cleaned;
        }

        public async Task<bool> ValidateScannedCodeAsync(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return false;

            var rules = _config.ValidationRules;

            // Vérifier la longueur
            if (code.Length < rules.MinLength || code.Length > rules.MaxLength)
                return false;

            // Vérifier les caractères autorisés
            if (!string.IsNullOrEmpty(rules.AllowedCharacters))
            {
                if (code.Any(c => !rules.AllowedCharacters.Contains(c)))
                    return false;
            }

            // Vérifier si alphanumérique requis
            if (rules.RequireAlphaNumeric && !code.All(c => char.IsLetterOrDigit(c) || c == '-' || c == '_'))
                return false;

            // Vérifier les espaces
            if (!rules.AllowSpaces && code.Contains(' '))
                return false;

            return await Task.FromResult(true);
        }

        #endregion

        #region Gestion des codes

        public async Task<ScanResult> ProcessScannedCodeAsync(string code)
        {
            var result = new ScanResult
            {
                Code = code,
                ScanTime = DateTime.Now
            };

            var startTime = DateTime.Now;

            try
            {
                // Nettoyer le code
                result.CleanedCode = await CleanScannedCodeAsync(code);

                // Valider le code
                if (!await ValidateScannedCodeAsync(result.CleanedCode))
                {
                    result.Success = false;
                    result.ErrorMessage = "Code invalide selon les règles de validation";
                    return result;
                }

                // Déterminer le type de code
                result.Type = DetermineCodeType(result.CleanedCode);

                result.Success = true;
                result.ProcessingTime = DateTime.Now - startTime;

                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.ProcessingTime = DateTime.Now - startTime;

                // Ajouter à l'historique des erreurs
                _scanHistory.Add(new ScanHistoryItem
                {
                    ScanTime = DateTime.Now,
                    Code = code,
                    Type = ScanType.Unknown,
                    Success = false,
                    ErrorMessage = ex.Message
                });

                return result;
            }
        }

        public async Task<bool> IsValidBarcodeAsync(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return false;

            // Validation basique des codes à barres
            // EAN-13: 13 chiffres
            if (code.Length == 13 && code.All(char.IsDigit))
                return await Task.FromResult(true);

            // EAN-8: 8 chiffres
            if (code.Length == 8 && code.All(char.IsDigit))
                return await Task.FromResult(true);

            // Code 128: caractères alphanumériques
            if (code.Length >= 1 && code.Length <= 48 && code.All(c => char.IsLetterOrDigit(c) || "!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~".Contains(c)))
                return await Task.FromResult(true);

            return await Task.FromResult(false);
        }

        public async Task<string> FormatCodeAsync(string code, CodeFormat format)
        {
            if (string.IsNullOrWhiteSpace(code))
                return string.Empty;

            return await Task.FromResult(format switch
            {
                CodeFormat.UpperCase => code.ToUpper(),
                CodeFormat.LowerCase => code.ToLower(),
                CodeFormat.TitleCase => System.Globalization.CultureInfo.CurrentCulture.TextInfo.ToTitleCase(code.ToLower()),
                CodeFormat.Trimmed => code.Trim(),
                CodeFormat.AlphaNumericOnly => Regex.Replace(code, @"[^A-Za-z0-9]", ""),
                _ => code
            });
        }

        #endregion

        #region Statistiques et historique

        public async Task<int> GetTodayScanCountAsync()
        {
            var today = DateTime.Today;
            var count = _scanHistory.Count(h => h.ScanTime.Date == today && h.Success);
            return await Task.FromResult(count);
        }

        public async Task<int> GetTotalScanCountAsync()
        {
            return await Task.FromResult(_scanCount);
        }

        public async Task<DateTime?> GetLastScanTimeAsync()
        {
            return await Task.FromResult(LastScanTime);
        }

        public async Task<IEnumerable<ScanHistoryItem>> GetScanHistoryAsync(int count = 100)
        {
            var history = _scanHistory
                .OrderByDescending(h => h.ScanTime)
                .Take(count)
                .ToList();

            return await Task.FromResult(history);
        }

        public async Task<bool> ClearHistoryAsync()
        {
            try
            {
                _scanHistory.Clear();
                return await Task.FromResult(true);
            }
            catch
            {
                return await Task.FromResult(false);
            }
        }

        #endregion

        #region Paramètres avancés

        public async Task<bool> SetScanDelayAsync(int milliseconds)
        {
            try
            {
                _config.ScanDelay = Math.Max(50, Math.Min(1000, milliseconds)); // Entre 50ms et 1s
                return await Task.FromResult(true);
            }
            catch
            {
                return await Task.FromResult(false);
            }
        }

        public async Task<bool> SetAutoProcessingAsync(bool enabled)
        {
            try
            {
                _config.AutoProcessing = enabled;
                return await Task.FromResult(true);
            }
            catch
            {
                return await Task.FromResult(false);
            }
        }

        public async Task<bool> SetValidationRulesAsync(ValidationRules rules)
        {
            try
            {
                _config.ValidationRules = rules ?? new ValidationRules();
                return await Task.FromResult(true);
            }
            catch
            {
                return await Task.FromResult(false);
            }
        }

        #endregion

        #region Diagnostic

        public async Task<ScannerStatus> GetStatusAsync()
        {
            return await Task.FromResult(new ScannerStatus
            {
                IsActive = _isActive,
                IsConnected = true,
                Status = _isActive ? "Actif" : "Inactif",
                LastActivity = _lastInputTime,
                TotalScans = _scanCount,
                ErrorCount = _scanHistory.Count(h => !h.Success),
                Version = "1.0.0"
            });
        }

        public async Task<string> GetDiagnosticInfoAsync()
        {
            var status = await GetStatusAsync();
            var todayScans = await GetTodayScanCountAsync();

            return await Task.FromResult($@"
Scanner Status: {status.Status}
Version: {status.Version}
Total Scans: {status.TotalScans}
Today's Scans: {todayScans}
Error Count: {status.ErrorCount}
Last Activity: {status.LastActivity:yyyy-MM-dd HH:mm:ss}
Scan Delay: {_config.ScanDelay}ms
Auto Processing: {_config.AutoProcessing}
");
        }

        public async Task<bool> ResetAsync()
        {
            try
            {
                _scanCount = 0;
                _lastScannedCode = string.Empty;
                _lastInputTime = DateTime.MinValue;
                _scanHistory.Clear();
                _config = new ScannerConfig();
                
                return await Task.FromResult(true);
            }
            catch
            {
                return await Task.FromResult(false);
            }
        }

        #endregion

        #region Méthodes utilitaires privées

        private static ScanType DetermineCodeType(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return ScanType.Unknown;

            // Déterminer le type basé sur le format du code
            if (code.StartsWith("CLI", StringComparison.OrdinalIgnoreCase))
                return ScanType.ClientCode;

            if (code.StartsWith("ART", StringComparison.OrdinalIgnoreCase))
                return ScanType.ArticleCode;

            // Code à barres numérique (EAN-13, EAN-8)
            if (code.All(char.IsDigit) && (code.Length == 8 || code.Length == 13))
                return ScanType.Barcode;

            // Code à barres alphanumérique
            if (code.Length >= 6 && code.All(c => char.IsLetterOrDigit(c) || c == '-' || c == '_'))
                return ScanType.Barcode;

            return ScanType.Manual;
        }

        #endregion
    }
}
