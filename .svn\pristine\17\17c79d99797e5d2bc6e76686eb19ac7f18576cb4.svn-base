﻿Imports System
Imports System.Windows.Forms
Imports System.Data.SqlClient
Imports System.IO
Imports System.Management

Public Class fCleRegistre

    Private Sub tCleRegistre_Load(sender As Object, e As System.EventArgs) Handles Me.Load
        FillNetworkAdapters()
        lblMacAddress.Text = GetSetting("PHARMA", "PHARMA", "AdresseMac", "")
        tCleActivation.Text = GetSetting("PHARMA", "PHARMA", "CleActivation", "")
    End Sub

    'Chargé de lister les noms des adaptateur réseau.
    Private Sub FillNetworkAdapters()
        Dim mc As ManagementClass
        'Creation d'une classe de gestion pour les adaptateurs de réseau 
        mc = New ManagementClass("Win32_NetworkAdapterConfiguration")
        'Collection des adaptateurs gérés par la classe de gestion. 
        Dim moc As ManagementObjectCollection = mc.GetInstances()
        'Recherche des éléments actives qui ont des adresses IP. 
        For Each o As ManagementObject In moc
            ' If DirectCast(o.GetPropertyValue("IPEnabled"), Boolean) = True Then
            Dim strAdapter As String
            ' Prendre le nom de l'adaptateur. 
            strAdapter = o.GetPropertyValue("Caption").ToString()

            ' Ajout dans le combobox 
            cmbAdresseMac.Items.Add(strAdapter)
            ' End If
        Next
    End Sub

    'Cette fonction se charge de chercher l'addresse MAC d'une carte réseau sur la machine locale. 
    Private Function GetMACAddress(ByVal Adapter As String) As String
        Dim mc As ManagementClass
        mc = New ManagementClass("Win32_NetworkAdapterConfiguration")
        Dim moc As ManagementObjectCollection = mc.GetInstances()
        'On cherche l'adaptateur passé en parametre pour la fonction 
        For Each o As ManagementObject In moc
            ' If DirectCast(o.GetPropertyValue("IPEnabled"), Boolean) = True Then
            Dim strAdapter As String
            strAdapter = o.GetPropertyValue("Caption").ToString()
            'S'il est trouvé
            If strAdapter = Adapter Then
                If Not o.GetPropertyValue("MacAddress") Is Nothing Then
                    Return o.GetPropertyValue("MacAddress").ToString()
                End If
            End If
            ' End If
        Next
        ' Sinon 
        Return String.Empty
    End Function


    Private Sub cmbAdresseMac_SelectedIndexChanged(sender As Object, e As System.EventArgs) Handles cmbAdresseMac.SelectedIndexChanged
        'Mise a jour du textBox 
        lblMacAddress.Text = GetMACAddress(cmbAdresseMac.SelectedItem.ToString())
    End Sub

    Private Sub bEnregistrer_Click(sender As System.Object, e As System.EventArgs) Handles bEnregistrer.Click
        SaveSetting("PHARMA", "PHARMA", "AdresseMac", lblMacAddress.Text)
        SaveSetting("PHARMA", "PHARMA", "CleActivation", tCleActivation.Text)
        Me.Close()
    End Sub
End Class