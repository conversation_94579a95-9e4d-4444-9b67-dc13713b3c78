using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;

namespace PharmaModerne.UI
{
    /// <summary>
    /// Fenêtre de test simplifiée pour PHARMA2000 Moderne
    /// </summary>
    public partial class MainWindowSimple : Window, INotifyPropertyChanged
    {
        private readonly DispatcherTimer _timer;
        private string _scannerTestText = "";
        private string _scannerResult = "";
        private string _currentTime = "";

        public MainWindowSimple()
        {
            InitializeComponent();
            DataContext = this;
            
            // Timer pour l'heure
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _timer.Tick += (s, e) => CurrentTime = DateTime.Now.ToString("HH:mm:ss");
            _timer.Start();
            
            CurrentTime = DateTime.Now.ToString("HH:mm:ss");
            
            // Initialiser les commandes
            TestScannerCommand = new RelayCommand(TestScanner);
            OpenClientsCommand = new RelayCommand(() => ShowMessage("Module Clients", "Fonctionnalité en cours de développement"));
            OpenArticlesCommand = new RelayCommand(() => ShowMessage("Module Articles", "Fonctionnalité en cours de développement"));
            OpenVenteCommand = new RelayCommand(() => ShowMessage("Point de Vente", "Fonctionnalité en cours de développement"));
            OpenReportsCommand = new RelayCommand(() => ShowMessage("Rapports", "Fonctionnalité en cours de développement"));
        }

        #region Propriétés

        public string ScannerTestText
        {
            get => _scannerTestText;
            set
            {
                _scannerTestText = value;
                OnPropertyChanged();
            }
        }

        public string ScannerResult
        {
            get => _scannerResult;
            set
            {
                _scannerResult = value;
                OnPropertyChanged();
            }
        }

        public string CurrentTime
        {
            get => _currentTime;
            set
            {
                _currentTime = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region Commandes

        public ICommand TestScannerCommand { get; }
        public ICommand OpenClientsCommand { get; }
        public ICommand OpenArticlesCommand { get; }
        public ICommand OpenVenteCommand { get; }
        public ICommand OpenReportsCommand { get; }

        #endregion

        #region Méthodes

        private void TestScanner()
        {
            if (string.IsNullOrWhiteSpace(ScannerTestText))
            {
                ScannerResult = "❌ Veuillez saisir un code à tester";
                return;
            }

            // Simuler la détection du scanner
            var inputTime = DateTime.Now;
            var isScanner = ScannerTestText.Length > 5; // Simulation simple

            ScannerResult = $"✅ Code testé : '{ScannerTestText}'\n" +
                          $"📱 Type détecté : {(isScanner ? "Scanner" : "Saisie manuelle")}\n" +
                          $"⏰ Heure : {inputTime:HH:mm:ss}\n" +
                          $"📏 Longueur : {ScannerTestText.Length} caractères";

            // Vider le champ pour le prochain test
            ScannerTestText = "";
        }

        private void ShowMessage(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            base.OnClosed(e);
        }
    }

    /// <summary>
    /// Commande simple pour les tests
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter) => _canExecute?.Invoke() ?? true;

        public void Execute(object? parameter) => _execute();
    }
}
