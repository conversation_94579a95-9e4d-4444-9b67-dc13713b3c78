try { 
    Write-Host "Demarrage de l'application..." 
    $process = Start-Process -FilePath ".\PharmaModerne.UI.exe" -PassThru -WindowStyle Normal 
    Start-Sleep -Seconds 5 
    if ($process.HasExited) { 
        Write-Host "PROBLEME: Application fermee immediatement" 
        Write-Host "Code de sortie: $($process.ExitCode)" 
    } else { 
        Write-Host "Application semble demarree (PID: $($process.Id))" 
        Write-Host "Verification si la fenetre est visible..." 
