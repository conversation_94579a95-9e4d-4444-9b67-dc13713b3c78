﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fCommandeDesManquants
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fCommandeDesManquants))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.gListeRecherche = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.GroupeFournisseur = New System.Windows.Forms.GroupBox()
        Me.cmbType = New C1.Win.C1List.C1Combo()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.cmbFournisseur = New C1.Win.C1List.C1Combo()
        Me.lMatricule = New System.Windows.Forms.Label()
        Me.tNumeroBlFact = New C1.Win.C1Input.C1TextBox()
        Me.bFournisseur = New C1.Win.C1Input.C1Button()
        Me.bImprimer = New C1.Win.C1Input.C1Button()
        Me.GroupeNumero = New System.Windows.Forms.GroupBox()
        Me.lOperateur = New System.Windows.Forms.Label()
        Me.lDateCommande = New System.Windows.Forms.Label()
        Me.LNumero = New System.Windows.Forms.Label()
        Me.LVille = New System.Windows.Forms.Label()
        Me.lNumeroCommande = New System.Windows.Forms.Label()
        Me.bAjouter = New C1.Win.C1Input.C1Button()
        Me.bConfirmer = New C1.Win.C1Input.C1Button()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.lTotalTVA = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.lTotHTAchat = New System.Windows.Forms.Label()
        Me.lTotalTTCAchat = New System.Windows.Forms.Label()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.gArticles = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Panel.SuspendLayout()
        CType(Me.gListeRecherche, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupeFournisseur.SuspendLayout()
        CType(Me.cmbType, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbFournisseur, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNumeroBlFact, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupeNumero.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.gListeRecherche)
        Me.Panel.Controls.Add(Me.GroupeFournisseur)
        Me.Panel.Controls.Add(Me.bFournisseur)
        Me.Panel.Controls.Add(Me.bImprimer)
        Me.Panel.Controls.Add(Me.GroupeNumero)
        Me.Panel.Controls.Add(Me.bAjouter)
        Me.Panel.Controls.Add(Me.bConfirmer)
        Me.Panel.Controls.Add(Me.GroupBox2)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.gArticles)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1028, 566)
        Me.Panel.TabIndex = 3
        '
        'gListeRecherche
        '
        Me.gListeRecherche.AllowUpdate = False
        Me.gListeRecherche.AlternatingRows = True
        Me.gListeRecherche.CaptionHeight = 17
        Me.gListeRecherche.GroupByCaption = "Drag a column header here to group by that column"
        Me.gListeRecherche.Images.Add(CType(resources.GetObject("gListeRecherche.Images"), System.Drawing.Image))
        Me.gListeRecherche.LinesPerRow = 2
        Me.gListeRecherche.Location = New System.Drawing.Point(114, 139)
        Me.gListeRecherche.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gListeRecherche.Name = "gListeRecherche"
        Me.gListeRecherche.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gListeRecherche.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gListeRecherche.PreviewInfo.ZoomFactor = 75.0R
        Me.gListeRecherche.PrintInfo.PageSettings = CType(resources.GetObject("gListeRecherche.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gListeRecherche.RowHeight = 15
        Me.gListeRecherche.Size = New System.Drawing.Size(610, 209)
        Me.gListeRecherche.TabIndex = 49
        Me.gListeRecherche.Text = "C1TrueDBGrid1"
        Me.gListeRecherche.Visible = False
        Me.gListeRecherche.PropBag = resources.GetString("gListeRecherche.PropBag")
        '
        'GroupeFournisseur
        '
        Me.GroupeFournisseur.Controls.Add(Me.cmbType)
        Me.GroupeFournisseur.Controls.Add(Me.Label4)
        Me.GroupeFournisseur.Controls.Add(Me.Label3)
        Me.GroupeFournisseur.Controls.Add(Me.cmbFournisseur)
        Me.GroupeFournisseur.Controls.Add(Me.lMatricule)
        Me.GroupeFournisseur.Controls.Add(Me.tNumeroBlFact)
        Me.GroupeFournisseur.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeFournisseur.Font = New System.Drawing.Font("Calibri", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupeFournisseur.Location = New System.Drawing.Point(156, 8)
        Me.GroupeFournisseur.Name = "GroupeFournisseur"
        Me.GroupeFournisseur.Size = New System.Drawing.Size(606, 79)
        Me.GroupeFournisseur.TabIndex = 3
        Me.GroupeFournisseur.TabStop = False
        '
        'cmbType
        '
        Me.cmbType.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbType.Caption = ""
        Me.cmbType.CaptionHeight = 17
        Me.cmbType.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbType.ColumnCaptionHeight = 17
        Me.cmbType.ColumnFooterHeight = 17
        Me.cmbType.ColumnWidth = 120
        Me.cmbType.ContentHeight = 16
        Me.cmbType.DataMode = C1.Win.C1List.DataModeEnum.AddItem
        Me.cmbType.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbType.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbType.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbType.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbType.EditorHeight = 16
        Me.cmbType.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbType.Images.Add(CType(resources.GetObject("cmbType.Images"), System.Drawing.Image))
        Me.cmbType.ItemHeight = 15
        Me.cmbType.Location = New System.Drawing.Point(474, 34)
        Me.cmbType.MatchEntryTimeout = CType(2000, Long)
        Me.cmbType.MaxDropDownItems = CType(5, Short)
        Me.cmbType.MaxLength = 32767
        Me.cmbType.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbType.Name = "cmbType"
        Me.cmbType.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbType.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbType.Size = New System.Drawing.Size(126, 22)
        Me.cmbType.TabIndex = 63
        Me.cmbType.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbType.PropBag = resources.GetString("cmbType.PropBag")
        '
        'Label4
        '
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.Location = New System.Drawing.Point(437, 38)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(32, 13)
        Me.Label4.TabIndex = 62
        Me.Label4.Text = "Type"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label3
        '
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.Location = New System.Drawing.Point(133, 36)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(63, 18)
        Me.Label3.TabIndex = 59
        Me.Label3.Text = "Fournisseur"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'cmbFournisseur
        '
        Me.cmbFournisseur.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbFournisseur.Caption = ""
        Me.cmbFournisseur.CaptionHeight = 17
        Me.cmbFournisseur.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.cmbFournisseur.ColumnCaptionHeight = 17
        Me.cmbFournisseur.ColumnFooterHeight = 17
        Me.cmbFournisseur.ContentHeight = 16
        Me.cmbFournisseur.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbFournisseur.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbFournisseur.EditorFont = New System.Drawing.Font("Verdana", 8.25!)
        Me.cmbFournisseur.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbFournisseur.EditorHeight = 16
        Me.cmbFournisseur.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbFournisseur.Images.Add(CType(resources.GetObject("cmbFournisseur.Images"), System.Drawing.Image))
        Me.cmbFournisseur.ItemHeight = 15
        Me.cmbFournisseur.Location = New System.Drawing.Point(198, 34)
        Me.cmbFournisseur.MatchEntryTimeout = CType(2000, Long)
        Me.cmbFournisseur.MaxDropDownItems = CType(5, Short)
        Me.cmbFournisseur.MaxLength = 32767
        Me.cmbFournisseur.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbFournisseur.Name = "cmbFournisseur"
        Me.cmbFournisseur.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbFournisseur.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbFournisseur.Size = New System.Drawing.Size(199, 22)
        Me.cmbFournisseur.TabIndex = 2
        Me.cmbFournisseur.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbFournisseur.PropBag = resources.GetString("cmbFournisseur.PropBag")
        '
        'lMatricule
        '
        Me.lMatricule.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lMatricule.Location = New System.Drawing.Point(9, 40)
        Me.lMatricule.Name = "lMatricule"
        Me.lMatricule.Size = New System.Drawing.Size(61, 13)
        Me.lMatricule.TabIndex = 56
        Me.lMatricule.Text = "N° BL/Fact"
        Me.lMatricule.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tNumeroBlFact
        '
        Me.tNumeroBlFact.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tNumeroBlFact.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNumeroBlFact.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNumeroBlFact.Enabled = False
        Me.tNumeroBlFact.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tNumeroBlFact.Location = New System.Drawing.Point(70, 37)
        Me.tNumeroBlFact.Name = "tNumeroBlFact"
        Me.tNumeroBlFact.Size = New System.Drawing.Size(50, 18)
        Me.tNumeroBlFact.TabIndex = 3
        Me.tNumeroBlFact.Tag = Nothing
        Me.tNumeroBlFact.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tNumeroBlFact.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bFournisseur
        '
        Me.bFournisseur.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bFournisseur.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bFournisseur.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bFournisseur.Location = New System.Drawing.Point(630, 524)
        Me.bFournisseur.Name = "bFournisseur"
        Me.bFournisseur.Size = New System.Drawing.Size(78, 34)
        Me.bFournisseur.TabIndex = 59
        Me.bFournisseur.Text = "Fournisseur F2"
        Me.bFournisseur.UseVisualStyleBackColor = True
        Me.bFournisseur.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bImprimer
        '
        Me.bImprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bImprimer.Location = New System.Drawing.Point(714, 524)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(72, 34)
        Me.bImprimer.TabIndex = 4
        Me.bImprimer.Text = "Imprimer F6"
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupeNumero
        '
        Me.GroupeNumero.Controls.Add(Me.lOperateur)
        Me.GroupeNumero.Controls.Add(Me.lDateCommande)
        Me.GroupeNumero.Controls.Add(Me.LNumero)
        Me.GroupeNumero.Controls.Add(Me.LVille)
        Me.GroupeNumero.Controls.Add(Me.lNumeroCommande)
        Me.GroupeNumero.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeNumero.Location = New System.Drawing.Point(12, 8)
        Me.GroupeNumero.Name = "GroupeNumero"
        Me.GroupeNumero.Size = New System.Drawing.Size(138, 79)
        Me.GroupeNumero.TabIndex = 13
        Me.GroupeNumero.TabStop = False
        Me.GroupeNumero.Text = "Identification"
        '
        'lOperateur
        '
        Me.lOperateur.AutoSize = True
        Me.lOperateur.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lOperateur.Location = New System.Drawing.Point(6, 58)
        Me.lOperateur.Name = "lOperateur"
        Me.lOperateur.Size = New System.Drawing.Size(11, 13)
        Me.lOperateur.TabIndex = 36
        Me.lOperateur.Text = "-"
        '
        'lDateCommande
        '
        Me.lDateCommande.BackColor = System.Drawing.Color.Transparent
        Me.lDateCommande.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lDateCommande.Location = New System.Drawing.Point(53, 38)
        Me.lDateCommande.Name = "lDateCommande"
        Me.lDateCommande.Size = New System.Drawing.Size(75, 32)
        Me.lDateCommande.TabIndex = 34
        Me.lDateCommande.Text = "Date"
        '
        'LNumero
        '
        Me.LNumero.AutoSize = True
        Me.LNumero.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LNumero.Location = New System.Drawing.Point(4, 20)
        Me.LNumero.Name = "LNumero"
        Me.LNumero.Size = New System.Drawing.Size(44, 13)
        Me.LNumero.TabIndex = 10
        Me.LNumero.Text = "Numéro"
        '
        'LVille
        '
        Me.LVille.AutoSize = True
        Me.LVille.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LVille.Location = New System.Drawing.Point(4, 38)
        Me.LVille.Name = "LVille"
        Me.LVille.Size = New System.Drawing.Size(30, 13)
        Me.LVille.TabIndex = 11
        Me.LVille.Text = "Date"
        '
        'lNumeroCommande
        '
        Me.lNumeroCommande.AutoSize = True
        Me.lNumeroCommande.BackColor = System.Drawing.Color.Transparent
        Me.lNumeroCommande.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lNumeroCommande.Location = New System.Drawing.Point(53, 20)
        Me.lNumeroCommande.Name = "lNumeroCommande"
        Me.lNumeroCommande.Size = New System.Drawing.Size(59, 13)
        Me.lNumeroCommande.TabIndex = 33
        Me.lNumeroCommande.Text = "-------------"
        '
        'bAjouter
        '
        Me.bAjouter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouter.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouter.Location = New System.Drawing.Point(791, 524)
        Me.bAjouter.Name = "bAjouter"
        Me.bAjouter.Size = New System.Drawing.Size(72, 34)
        Me.bAjouter.TabIndex = 5
        Me.bAjouter.Text = "Ajouter F5"
        Me.bAjouter.UseVisualStyleBackColor = True
        Me.bAjouter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bConfirmer
        '
        Me.bConfirmer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bConfirmer.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmer.Location = New System.Drawing.Point(868, 524)
        Me.bConfirmer.Name = "bConfirmer"
        Me.bConfirmer.Size = New System.Drawing.Size(72, 34)
        Me.bConfirmer.TabIndex = 6
        Me.bConfirmer.Text = "Confirmer F3"
        Me.bConfirmer.UseVisualStyleBackColor = True
        Me.bConfirmer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox2
        '
        Me.GroupBox2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox2.Controls.Add(Me.lTotalTVA)
        Me.GroupBox2.Controls.Add(Me.Label6)
        Me.GroupBox2.Controls.Add(Me.Label8)
        Me.GroupBox2.Controls.Add(Me.Label5)
        Me.GroupBox2.Controls.Add(Me.lTotHTAchat)
        Me.GroupBox2.Controls.Add(Me.lTotalTTCAchat)
        Me.GroupBox2.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupBox2.Location = New System.Drawing.Point(436, 465)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(579, 48)
        Me.GroupBox2.TabIndex = 12
        Me.GroupBox2.TabStop = False
        '
        'lTotalTVA
        '
        Me.lTotalTVA.BackColor = System.Drawing.Color.Transparent
        Me.lTotalTVA.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotalTVA.ForeColor = System.Drawing.Color.Black
        Me.lTotalTVA.Location = New System.Drawing.Point(260, 15)
        Me.lTotalTVA.Name = "lTotalTVA"
        Me.lTotalTVA.Size = New System.Drawing.Size(90, 23)
        Me.lTotalTVA.TabIndex = 64
        Me.lTotalTVA.Text = "9999.999"
        Me.lTotalTVA.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.Location = New System.Drawing.Point(380, 18)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(73, 16)
        Me.Label6.TabIndex = 48
        Me.Label6.Text = "TOT TTC"
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.BackColor = System.Drawing.Color.Transparent
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.ForeColor = System.Drawing.Color.Black
        Me.Label8.Location = New System.Drawing.Point(186, 18)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(86, 16)
        Me.Label8.TabIndex = 63
        Me.Label8.Text = "Total TVA :"
        Me.Label8.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label5
        '
        Me.Label5.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.Location = New System.Drawing.Point(6, 17)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(64, 16)
        Me.Label5.TabIndex = 47
        Me.Label5.Text = "TOT HT"
        '
        'lTotHTAchat
        '
        Me.lTotHTAchat.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lTotHTAchat.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotHTAchat.ForeColor = System.Drawing.Color.Black
        Me.lTotHTAchat.Location = New System.Drawing.Point(76, 16)
        Me.lTotHTAchat.Name = "lTotHTAchat"
        Me.lTotHTAchat.Size = New System.Drawing.Size(93, 18)
        Me.lTotHTAchat.TabIndex = 46
        Me.lTotHTAchat.Text = "999999.999"
        Me.lTotHTAchat.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lTotalTTCAchat
        '
        Me.lTotalTTCAchat.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lTotalTTCAchat.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTotalTTCAchat.ForeColor = System.Drawing.Color.Red
        Me.lTotalTTCAchat.Location = New System.Drawing.Point(459, 15)
        Me.lTotalTTCAchat.Name = "lTotalTTCAchat"
        Me.lTotalTTCAchat.Size = New System.Drawing.Size(113, 19)
        Me.lTotalTTCAchat.TabIndex = 35
        Me.lTotalTTCAchat.Text = "999999.999"
        Me.lTotalTTCAchat.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bAnnuler
        '
        Me.bAnnuler.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(945, 524)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(72, 34)
        Me.bAnnuler.TabIndex = 7
        Me.bAnnuler.Text = "Annuler F10"
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gArticles
        '
        Me.gArticles.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gArticles.CaptionHeight = 17
        Me.gArticles.GroupByCaption = "Drag a column header here to group by that column"
        Me.gArticles.Images.Add(CType(resources.GetObject("gArticles.Images"), System.Drawing.Image))
        Me.gArticles.LinesPerRow = 2
        Me.gArticles.Location = New System.Drawing.Point(12, 94)
        Me.gArticles.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
        Me.gArticles.Name = "gArticles"
        Me.gArticles.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gArticles.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gArticles.PreviewInfo.ZoomFactor = 75.0R
        Me.gArticles.PrintInfo.PageSettings = CType(resources.GetObject("gArticles.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gArticles.RowHeight = 15
        Me.gArticles.Size = New System.Drawing.Size(1004, 365)
        Me.gArticles.TabIndex = 2
        Me.gArticles.Text = "C1TrueDBGrid1"
        Me.gArticles.PropBag = resources.GetString("gArticles.PropBag")
        '
        'fCommandeDesManquants
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1028, 566)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fCommandeDesManquants"
        Me.Text = "fCommandeDesManquants"
        Me.Panel.ResumeLayout(False)
        CType(Me.gListeRecherche, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupeFournisseur.ResumeLayout(False)
        Me.GroupeFournisseur.PerformLayout()
        CType(Me.cmbType, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbFournisseur, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNumeroBlFact, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupeNumero.ResumeLayout(False)
        Me.GroupeNumero.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents gListeRecherche As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents GroupeFournisseur As System.Windows.Forms.GroupBox
    Friend WithEvents cmbType As C1.Win.C1List.C1Combo
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents cmbFournisseur As C1.Win.C1List.C1Combo
    Friend WithEvents lMatricule As System.Windows.Forms.Label
    Friend WithEvents tNumeroBlFact As C1.Win.C1Input.C1TextBox
    Friend WithEvents bFournisseur As C1.Win.C1Input.C1Button
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
    Friend WithEvents GroupeNumero As System.Windows.Forms.GroupBox
    Friend WithEvents lOperateur As System.Windows.Forms.Label
    Friend WithEvents lDateCommande As System.Windows.Forms.Label
    Friend WithEvents LNumero As System.Windows.Forms.Label
    Friend WithEvents LVille As System.Windows.Forms.Label
    Friend WithEvents lNumeroCommande As System.Windows.Forms.Label
    Friend WithEvents bAjouter As C1.Win.C1Input.C1Button
    Friend WithEvents bConfirmer As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents lTotalTVA As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents lTotHTAchat As System.Windows.Forms.Label
    Friend WithEvents lTotalTTCAchat As System.Windows.Forms.Label
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents gArticles As C1.Win.C1TrueDBGrid.C1TrueDBGrid
End Class
