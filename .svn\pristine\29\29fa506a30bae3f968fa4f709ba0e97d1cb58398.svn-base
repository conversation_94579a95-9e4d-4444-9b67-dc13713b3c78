﻿Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Public Class fInventaire
    Dim cmdChargement As New SqlCommand
    Dim cbChargement As New SqlCommandBuilder
    Dim dsChargement As New DataSet
    Dim daChargement As New SqlDataAdapter

    Dim cmdInventaire As New SqlCommand
    Dim cbInventaire As New SqlCommandBuilder
    Public dsInventaire As New DataSet
    Dim daInventaire As New SqlDataAdapter

    'Dim cbEnregistrement As New SqlCommandBuilder
    'Dim daEnregistrement As New SqlDataAdapter

    Dim mode As String = ""

    Public NumeroInventaire As String = ""

    Public ValeurActuelleAchatTTC As Double = 0.0
    Public ValeurActuelleVenteTTC As Double = 0.0

    Public ValeurInitialeAchatTTC As Double = 0.0
    Public ValeurInitialeVenteTTC As Double = 0.0

    Public DifferenceAchatTTC As Double = 0.0
    Public DifferenceVenteTTC As Double = 0.0

    Public DifferenceAchatTTCPourcentage As Double = 0.0
    Public DifferenceVenteTTCPourcentage As Double = 0.0

    '------------------------------------------------- les informations de paiement (retour de fenetre paiement)
    Public Operateur As Integer = 0

    Public NouvelleAchat As DataRow = Nothing 'datarow pour charger l'entête dans la datatable ACHAT
    Public NouvelArticle As DataRow = Nothing 'datarow pour charger les détails dans la datatable ACHAT_DETAIL_AFFICHE
    Public NouvelArticleEnregistrement As DataRow = Nothing 'datarow pour charger les détails dans la datatable ACHAT_DETAIL
    Public dr As DataRow = Nothing 'datarow pour charger dans la datatable ARTICLE qui contient les articles recherchés pendant la saisie de la designation

    '---------------------------------------- variable pour récupérer les variables des critères de sélèction
    Public RechercheSelective As Boolean = False
    Public Section As String = ""
    Public DebutIntervalle As String = ""
    Public FinIntervalle As String = ""
    Public Forme As Integer = 0
    Public Categorie As Integer = 0
    Public Labo As Integer = 0
    Public Rayon As String = ""
    Public RayonSelectionne As String = ""
    Public Trie As String = ""
    Public NonMouvemente As Boolean = False

    '---------------------------------------- variables pour confirmer la mise en instance d'un inventaire
    Dim ComfirmerMettreEnINstance As Boolean = False
    Dim ConfirmerInstance As Boolean = False
    Dim NomInstance As String = ""
    Dim CodeOperateurInstance As String = ""

    Dim ConfirmerEnregistrer As Boolean = False
    Dim CodeOperateur As String = ""

    Dim qteAvantModif As String


    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "114" And bConfirmer.Enabled = True Then
            bConfirmer_Click(sender, e)
        End If
        If argument = "116" And bAjouter.Enabled = True Then
            bAjouter_Click(sender, e)
        End If
        If argument = "118" And bRecherche.Visible = True Then
            bRecherche_Click(sender, e)
        End If
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "121" And bAnnuler.Enabled = True Then
            bAnnuler_Click(sender, e)
        End If
        If argument = "113" And bAnnuler.Enabled = True Then
            bAfficherLot_Click(sender, e)
        End If

        '--------------------- boutton close 
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub Init()

        lActuelleAchatTTC.Text = "0.000"
        lActuelleVenteTTC.Text = "0.000"

        lInitialeAchatTTC.Text = "0.000"
        lInitialeVenteTTC.Text = "0.000"

        lDifferenceAchatTTC.Text = "0.000"
        lDifferenceVenteTTC.Text = "0.000"

        lDifferenceAchatTTCPourcentage.Text = "0.000"
        lDifferenceVenteTTCPourcentage.Text = "0.000"

        Dim StrSQL As String = ""
        Dim I As Integer

        Try
            dsChargement.Tables("INVENTAIRE_DETAILS_AFFICHE").Clear()
        Catch ex As Exception
        End Try

        Try
            dsChargement.Tables("INVENTAIRE").Clear()
        Catch ex As Exception
        End Try

        'chargement des Entêtes des Inventaires        
        StrSQL = "SELECT * FROM INVENTAIRE ORDER BY NumeroInventaire ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "INVENTAIRE")
        If dsChargement.Tables("INVENTAIRE").Rows.Count > 0 Then
            NumeroInventaire = CStr(dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1).Item("NumeroInventaire"))

            lNumeroInventaire.Text = CStr(dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("NumeroInventaire"))
            lDateInventaire.Text = CStr(dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("Date"))

            lActuelleAchatTTC.Text = CStr(dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("ValeurAchatActuelle"))
            lActuelleVenteTTC.Text = CStr(dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("ValeurVenteActuelle"))

            lInitialeAchatTTC.Text = CStr(dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("ValeurAchatInitial"))
            lInitialeVenteTTC.Text = CStr(dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("ValeurVenteInitial"))

            lDifferenceAchatTTC.Text = CStr(dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("ValeurAchatDifference"))
            lDifferenceVenteTTC.Text = CStr(dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("ValeurVenteDifference"))

            lOperateur.Text = CStr(RecupererValeurExecuteScalaire("NomOperateur", "OPERATEUR", "CodeOperateur", dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("CodePersonnel")))

        End If
        'chargement des détails des inventaires 
        StrSQL = "SELECT NumeroInventaire," + _
                 "INVENTAIRE_DETAILS.CodeArticle," + _
                 " MAX(INVENTAIRE_DETAILS.CodeABarre) AS CodeABarre," + _
                 " MAX(INVENTAIRE_DETAILS.Designation) AS Designation," + _
                 " MAX(LibelleForme) AS LibelleForme," + _
                 " MAX(INVENTAIRE_DETAILS.CodeForme) AS CodeForme," + _
                 " MAX(INVENTAIRE_DETAILS.Rayon) AS Rayon," + _
                 " SUM(INVENTAIRE_DETAILS.StockInitial) as StockInitial," + _
                 " SUM(StockActuel) as StockActuel," + _
                 " MAX(INVENTAIRE_DETAILS.PrixAchatTTC) AS PrixAchatTTC," + _
                 " SUM(INVENTAIRE_DETAILS.TotalAchatTTC) AS TotalAchatTTC," + _
                 " MAX(INVENTAIRE_DETAILS.PrixVenteTTC) AS PrixVenteTTC, " + _
                 "'' As QteChange " + _
                 "FROM " + _
                 "INVENTAIRE_DETAILS " + _
                 "LEFT OUTER JOIN ARTICLE ON INVENTAIRE_DETAILS.CodeArticle=ARTICLE.CodeArticle " + _
                 "LEFT OUTER JOIN FORME_ARTICLE ON INVENTAIRE_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
                 "WHERE NumeroInventaire =" + Quote(NumeroInventaire) + _
                 " GROUP BY NumeroInventaire,INVENTAIRE_DETAILS.CodeArticle " + _
                 " ORDER BY Designation"


        '" GROUP BY NumeroInventaire,INVENTAIRE_DETAILS.CodeArticle,INVENTAIRE_DETAILS.CodeABarre,INVENTAIRE_DETAILS.Designation," + _
        '         " LibelleForme,INVENTAIRE_DETAILS.CodeForme,INVENTAIRE_DETAILS.Rayon," + _
        '         " INVENTAIRE_DETAILS.PrixAchatTTC,INVENTAIRE_DETAILS.TotalAchatTTC, INVENTAIRE_DETAILS.PrixVenteTTC" + _
        '         " ORDER BY Designation"

        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "INVENTAIRE_DETAILS_AFFICHE")

        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsChargement
            Catch ex As Exception
            End Try
            .DataMember = "INVENTAIRE_DETAILS_AFFICHE"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Rayon").Caption = "Rayon"
            .Columns("StockInitial").Caption = "Stock Initial"
            .Columns("StockActuel").Caption = "Nouveau Stock"
            .Columns("PrixAchatTTC").Caption = "Prix achat TTC"
            .Columns("TotalAchatTTC").Caption = "Total Achat TTC"

            .Columns("QteChange").Caption = "QteChange"
            .Columns("QteChange").DataField = ""

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            'coloriage de la liste 
            'For I = 0 To .Columns.Count - 1
            '    .Columns(I).FilterDropdown = True
            'Next

            .FilterBar = True

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchatTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalAchatTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("NumeroInventaire").Width = 0
            .Splits(0).DisplayColumns("NumeroInventaire").Visible = False
            .Splits(0).DisplayColumns("NumeroInventaire").AllowSizing = False
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 100
            .Splits(0).DisplayColumns("Designation").Width = 320
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Rayon").Width = 80
            .Splits(0).DisplayColumns("CodeForme").Width = 0
            .Splits(0).DisplayColumns("StockInitial").Width = 80
            .Splits(0).DisplayColumns("StockActuel").Width = 80
            .Splits(0).DisplayColumns("PrixAchatTTC").Width = 80
            .Splits(0).DisplayColumns("TotalAchatTTC").Width = 80
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 0
            .Splits(0).DisplayColumns("PrixVenteTTC").Visible = False
            .Splits(0).DisplayColumns("CodeForme").Visible = False

            .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("StockActuel").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).DisplayColumns("QteChange").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = True
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gArticles)
        End With

        '--------------------initialisation de la datatable article qui est utilisé dans la liste de 
        '--------------------recherche alimenté selon les entrés de l utilisateur dans la colonne designation
        StrSQL = "SELECT CodeArticle," + _
                 " Designation," + _
                 " LibelleForme," + _
                 " PrixVenteTTC " + _
                 " FROM ARTICLE,FORME_ARTICLE " + _
                 "WHERE ARTICLE.CodeForme=FORME_ARTICLE.CodeForme  and " + _
                 "Designation LIKE '" + gArticles.Columns("Designation").Value.ToString + "'ORDER BY Designation"

        cmdInventaire.Connection = ConnectionServeur
        cmdInventaire.CommandText = StrSQL
        daInventaire = New SqlDataAdapter(cmdInventaire)
        daInventaire.Fill(dsInventaire, "ARTICLE")

        With gListeRecherche
            .Columns.Clear()
            .DataSource = dsInventaire
            .DataMember = "ARTICLE"
            .Rebind(False)
            .Columns("CodeArticle").Caption = "Code Article"
            .Columns("Designation").Caption = "Designation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("PrixVenteTTC").Caption = "Prix de vente"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centre tous les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Visible = False
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
            .Splits(0).DisplayColumns("Designation").Visible = True
            .Splits(0).DisplayColumns("LibelleForme").Visible = True
            .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("Designation").Width = 300
            .Splits(0).DisplayColumns("LibelleForme").Width = 100
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 20
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gListeRecherche)
        End With

        cbInventaire = New SqlCommandBuilder(daInventaire)

        bConfirmer.Enabled = False
        bAnnuler.Enabled = False
        bAjouter.Enabled = True

        bFirst.Enabled = True
        bPrevious.Enabled = True
        bNext.Enabled = True
        bLast.Enabled = True

        bTerminal.Enabled = False

        GroupeRemarque.Enabled = False
        GroupeRemarque.Visible = False

    End Sub

    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        Try
            Quote(CStr(ValeurCle))
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(CStr(ValeurCle))
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click

        Dim StrSQL As String = ""
        Dim i As Integer = 0
        Dim J As Integer = 0
        Dim Cond As String = ""
        Dim unite As Double = 0.0

        mode = "Ajout"

        lActuelleAchatTTC.Text = "0.000"
        lActuelleVenteTTC.Text = "0.000"

        lInitialeAchatTTC.Text = "0.000"
        lInitialeVenteTTC.Text = "0.000"

        lDifferenceAchatTTC.Text = "0.000"
        lDifferenceVenteTTC.Text = "0.000"

        lDifferenceAchatTTCPourcentage.Text = "0.000"
        lDifferenceVenteTTCPourcentage.Text = "0.000"

        '------------------------------ préparation des datatables vides 

        If (dsInventaire.Tables.IndexOf("INVENTAIRE_DETAILS_AFFICHE") > -1) Then
            dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Clear()
        End If
        If (dsInventaire.Tables.IndexOf("INVENTAIRE") > -1) Then
            dsInventaire.Tables("INVENTAIRE").Clear()
        End If
        If (dsInventaire.Tables.IndexOf("INVENTAIRE_DETAILS_AFFICHE1") > -1) Then
            dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE1").Clear()
        End If
        If (dsInventaire.Tables.IndexOf("INVENTAIRE_DETAILS") > -1) Then
            dsInventaire.Tables("INVENTAIRE_DETAILS").Clear()
        End If

        'chargement des Entêtes des inventaires 
        StrSQL = "SELECT top 0 * FROM INVENTAIRE ORDER BY NumeroInventaire ASC"
        cmdInventaire.Connection = ConnectionServeur
        cmdInventaire.CommandText = StrSQL
        daInventaire = New SqlDataAdapter(cmdInventaire)
        daInventaire.Fill(dsInventaire, "INVENTAIRE")
        cbInventaire = New SqlCommandBuilder(daInventaire)

        'chargement des détails des inventaires juste une table pour formater la gride par les colonnes (vide)
        StrSQL = "SELECT NumeroInventaire," + _
                 "INVENTAIRE_DETAILS.CodeArticle," + _
                 "INVENTAIRE_DETAILS.CodeABarre," + _
                 "INVENTAIRE_DETAILS.Designation," + _
                 "LibelleForme," + _
                 "INVENTAIRE_DETAILS.CodeForme," + _
                 "INVENTAIRE_DETAILS.Rayon," + _
                 "SUM(INVENTAIRE_DETAILS.StockInitial) as StockInitial," + _
                 "SUM(StockActuel)as StockActuel," + _
                 "INVENTAIRE_DETAILS.PrixAchatTTC," + _
                 "INVENTAIRE_DETAILS.TotalAchatTTC," + _
                 "INVENTAIRE_DETAILS.PrixVenteTTC ," + _
                 "'' AS QteChange " + _
                 "FROM " + _
                 "INVENTAIRE_DETAILS " + _
                 "LEFT OUTER JOIN ARTICLE ON INVENTAIRE_DETAILS.CodeArticle=ARTICLE.CodeArticle " + _
                 "LEFT OUTER JOIN FORME_ARTICLE ON INVENTAIRE_DETAILS.CodeForme=ARTICLE.CodeForme " + _
                 "WHERE NumeroInventaire ='0'  AND ARTICLE.supprime = 0 " + _
                 " Group by NumeroInventaire,INVENTAIRE_DETAILS.CodeArticle,INVENTAIRE_DETAILS.CodeABarre,INVENTAIRE_DETAILS.Designation," + _
                 " LibelleForme,INVENTAIRE_DETAILS.CodeForme,INVENTAIRE_DETAILS.Rayon," + _
                 " INVENTAIRE_DETAILS.PrixAchatTTC, INVENTAIRE_DETAILS.TotalAchatTTC, INVENTAIRE_DETAILS.PrixVenteTTC"

        cmdInventaire.Connection = ConnectionServeur
        cmdInventaire.CommandText = StrSQL
        daInventaire.Fill(dsInventaire, "INVENTAIRE_DETAILS_AFFICHE1")
        cbInventaire = New SqlCommandBuilder(daInventaire)

        With gArticles
            .Columns.Clear()
            .DataSource = dsInventaire
            .DataMember = "INVENTAIRE_DETAILS_AFFICHE1"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Rayon").Caption = "Rayon"
            .Columns("StockInitial").Caption = "Stock Initial"
            .Columns("StockActuel").Caption = "nouveau Stock"
            .Columns("PrixAchatTTC").Caption = "Prix achat TTC"
            .Columns("TotalAchatTTC").Caption = "Total Achat TTC"
            .Columns("QteChange").Caption = "QteChange"

            .Columns("QteChange").DataField = ""

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            'coloriage de la liste 
            'For i = 0 To .Columns.Count - 1
            '    .Columns(i).FilterDropdown = True
            'Next

            .FilterBar = True

            .Splits(0).DisplayColumns("CodeABarre").Locked = False
            .Splits(0).DisplayColumns("Designation").Locked = False
            .Splits(0).DisplayColumns("StockActuel").Locked = False

            .Splits(0).DisplayColumns("NumeroInventaire").Width = 0
            .Splits(0).DisplayColumns("NumeroInventaire").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 100
            .Splits(0).DisplayColumns("Designation").Width = 320
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Rayon").Width = 80
            .Splits(0).DisplayColumns("CodeForme").Width = 0
            .Splits(0).DisplayColumns("StockInitial").Width = 80
            .Splits(0).DisplayColumns("StockActuel").Width = 80
            .Splits(0).DisplayColumns("PrixAchatTTC").Width = 80
            .Splits(0).DisplayColumns("TotalAchatTTC").Width = 80
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 0

            .Splits(0).DisplayColumns("NumeroInventaire").Visible = False
            .Splits(0).DisplayColumns("PrixVenteTTC").Visible = False
            .Splits(0).DisplayColumns("CodeForme").Visible = False

            .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("StockActuel").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).DisplayColumns("QteChange").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True

        End With

        Me.gArticles.Splits(0).DisplayColumns(1).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(4).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(5).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(6).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(7).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(9).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(10).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(11).AllowFocus = False

        '************************** création d'une table INVENTAIRE_DETAILS qui doit etre une copie de celle de
        '************************** la base de données et qui contient chaque article avec les lots disponible
        '************************** chaque un dans un enregistrement seul, c'est sur cette table qu'on doit appliquer 
        '************************** la methode update du dataAdapter

        StrSQL = "SELECT NumeroInventaire," + _
                 "INVENTAIRE_DETAILS.CodeArticle," + _
                 "INVENTAIRE_DETAILS.CodeABarre," + _
                 "INVENTAIRE_DETAILS.Designation," + _
                 "NumeroLot," + _
                 "LibelleForme," + _
                 "INVENTAIRE_DETAILS.CodeForme," + _
                 "INVENTAIRE_DETAILS.Rayon," + _
                 "INVENTAIRE_DETAILS.StockInitial," + _
                 "StockActuel," + _
                 "INVENTAIRE_DETAILS.PrixAchatTTC," + _
                 "INVENTAIRE_DETAILS.TotalAchatTTC," + _
                 "INVENTAIRE_DETAILS.PrixVenteTTC, " + _
                 "'' AS QteChange " + _
                 "FROM " + _
                 "INVENTAIRE_DETAILS " + _
                 "LEFT OUTER JOIN ARTICLE ON INVENTAIRE_DETAILS.CodeArticle=ARTICLE.CodeArticle " + _
                 "LEFT OUTER JOIN FORME_ARTICLE ON INVENTAIRE_DETAILS.CodeForme=ARTICLE.CodeForme " + _
                 "WHERE NumeroInventaire ='0' AND ARTICLE.supprime = 0 "

        cmdInventaire.Connection = ConnectionServeur
        cmdInventaire.CommandText = StrSQL
        daInventaire.Fill(dsInventaire, "INVENTAIRE_DETAILS")
        cbInventaire = New SqlCommandBuilder(daInventaire)

        'ajout d'un nouvel enregistrement vide dans les datatables convenables
        NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
        NouvelArticleEnregistrement("Designation") = ""
        NouvelArticleEnregistrement("CodeArticle") = ""
        NouvelArticleEnregistrement("CodeABarre") = ""
        NouvelArticleEnregistrement("NumeroLot") = ""
        dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)

        '*****************************************************************************************************
        '*****************************************************************************************************

        Dim InstanceRechercheInventaire As New fCritereDeRechercheInventaire
        InstanceRechercheInventaire.ShowDialog()

        RechercheSelective = fCritereDeRechercheInventaire.RechercheSelective
        Section = fCritereDeRechercheInventaire.Section
        DebutIntervalle = fCritereDeRechercheInventaire.DebutIntervalle
        FinIntervalle = fCritereDeRechercheInventaire.FinIntervalle
        Forme = fCritereDeRechercheInventaire.Forme
        Categorie = fCritereDeRechercheInventaire.Categorie
        Labo = fCritereDeRechercheInventaire.Labo
        Rayon = fCritereDeRechercheInventaire.Rayon
        RayonSelectionne = fCritereDeRechercheInventaire.RayonSelectionne
        Trie = fCritereDeRechercheInventaire.Trie
        NonMouvemente = fCritereDeRechercheInventaire.NonMouvemente
        lTypeInventaire.Text = fCritereDeRechercheInventaire.TypeInventaire

        fCritereDeRechercheInventaire.Dispose()
        fCritereDeRechercheInventaire.Close()

        NumeroInventaire = CStr(RecupereNumuero())

        'une autre liaison avec la data table qu'on va la remplir par les données 
        'chargement des détails des inventaires 

        StrSQL = "SELECT NumeroInventaire," + _
                 "INVENTAIRE_DETAILS.CodeArticle," + _
                 "MAX(INVENTAIRE_DETAILS.CodeABarre) AS CodeABarre," + _
                 "MAX(INVENTAIRE_DETAILS.Designation) AS Designation," + _
                 "MAX(LibelleForme) AS LibelleForme," + _
                 "MAX(INVENTAIRE_DETAILS.CodeForme) AS CodeForme," + _
                 "MAX(INVENTAIRE_DETAILS.Rayon) AS Rayon," + _
                 "SUM(INVENTAIRE_DETAILS.StockInitial) as StockInitial," + _
                 "SUM(StockActuel)as StockActuel," + _
                 "MAX(INVENTAIRE_DETAILS.PrixAchatTTC) AS PrixAchatTTC," + _
                 "SUM(INVENTAIRE_DETAILS.TotalAchatTTC) AS TotalAchatTTC," + _
                 "MAX(INVENTAIRE_DETAILS.PrixVenteTTC) AS PrixVenteTTC, " + _
                 "'' AS QteChange " + _
                 "FROM " + _
                 "INVENTAIRE_DETAILS " + _
                 "LEFT OUTER JOIN ARTICLE ON INVENTAIRE_DETAILS.CodeArticle=ARTICLE.CodeArticle " + _
                 "LEFT OUTER JOIN FORME_ARTICLE ON INVENTAIRE_DETAILS.CodeForme=ARTICLE.CodeForme " + _
                 "WHERE NumeroInventaire ='0'  AND ARTICLE.supprime = 0  " + _
                 " Group by NumeroInventaire,INVENTAIRE_DETAILS.CodeArticle"


        '" Group by NumeroInventaire,INVENTAIRE_DETAILS.CodeArticle,INVENTAIRE_DETAILS.CodeABarre,INVENTAIRE_DETAILS.Designation," + _
        '         " LibelleForme,INVENTAIRE_DETAILS.CodeForme,INVENTAIRE_DETAILS.Rayon," + _
        '         " INVENTAIRE_DETAILS.PrixAchatTTC, INVENTAIRE_DETAILS.TotalAchatTTC, INVENTAIRE_DETAILS.PrixVenteTTC "

        cmdInventaire.Connection = ConnectionServeur
        cmdInventaire.CommandText = StrSQL
        daInventaire.Fill(dsInventaire, "INVENTAIRE_DETAILS_AFFICHE")
        cbInventaire = New SqlCommandBuilder(daInventaire)

        'ajout d'un nouvel enregistrement vide dans les datatables convenables
        NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
        NouvelArticle("Designation") = ""
        NouvelArticle("CodeArticle") = ""
        NouvelArticle("CodeABarre") = ""
        dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)

        NouvelleAchat = dsInventaire.Tables("INVENTAIRE").NewRow()
        dsInventaire.Tables("INVENTAIRE").Rows.Add(NouvelleAchat)

        '***************************************************************************************************
        '************************************ Cas ou il y a des critères de recherches *********************
        '***************************************************************************************************

        Cond = " 1=1 "
        If RechercheSelective = True Then

            If Section = "INTERVALLE" And DebutIntervalle.ToString <> "" And FinIntervalle.ToString <> "" Then
                Cond = Cond + " AND ARTICLE.Section > " + DebutIntervalle.ToString + " AND ARTICLE.Section < " + FinIntervalle.ToString
            End If

            If Forme <> 0 Then
                Cond = Cond + " AND ARTICLE.CodeForme = " + Forme.ToString
            End If
            If Categorie <> 0 Then
                Cond = Cond + " AND ARTICLE.CodeCategorie = " + Categorie.ToString
            End If
            If Labo <> 0 Then
                Cond = Cond + " AND ARTICLE.CodeLabo = " + Labo.ToString
            End If

            If Rayon = "RAYON" Then
                Cond = Cond + " AND Rayon = '" + RayonSelectionne + "'"
            End If

            '----------------------- chargement de tous les articles 
            If NonMouvemente = True Then  ' charger tous les articles ceux qui ont des lots avec ceux qui n ont pas de lots

                If (dsInventaire.Tables.IndexOf("TOUS_ARTICLES") > -1) Then
                    dsInventaire.Tables("TOUS_ARTICLES").Clear()
                End If

                StrSQL = " SELECT CodeArticle,CodeABarre FROM ARTICLE " + _
                         " LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme = FORME_ARTICLE.CodeForme  " + _
                         " LEFT OUTER JOIN CATEGORIE ON ARTICLE.CodeCategorie =CATEGORIE.CodeCategorie " + _
                         " WHERE ARTICLE.supprime = 0 AND " + Cond + " ORDER BY " + Trie + " " + _
                         ""

                cmdInventaire.Connection = ConnectionServeur
                cmdInventaire.CommandText = StrSQL
                daInventaire = New SqlDataAdapter(cmdInventaire)
                daInventaire.Fill(dsInventaire, "TOUS_ARTICLES")

                'Arrêter la capture d'évènement clavier sur le contrôle 
                RemoveHandler Me.bAjouter.Click, AddressOf Me.bAjouter_Click
                RemoveHandler Me.bQuitter.Click, AddressOf Me.bQuitter_Click

                ProgressBar.Visible = True
                GroupeJauge.Visible = True
                ProgressBar.Value = 0
                unite = 100 / (dsInventaire.Tables("TOUS_ARTICLES").Rows.Count - 1)

                For i = 0 To dsInventaire.Tables("TOUS_ARTICLES").Rows.Count - 1

                    If unite * i < (dsInventaire.Tables("TOUS_ARTICLES").Rows.Count - 1) Then
                        If (unite * i) <= 100 Then
                            ProgressBar.Value = CInt(unite * i)
                        Else
                            ProgressBar.Value = 100
                        End If
                    End If

                    Application.DoEvents()

                    If (dsInventaire.Tables.IndexOf("LOT_ARTICLE") > -1) Then
                        dsInventaire.Tables("LOT_ARTICLE").Clear()
                    End If
                    If (dsInventaire.Tables.IndexOf("LOT_ARTICLE_COMPLET") > -1) Then
                        dsInventaire.Tables("LOT_ARTICLE_COMPLET").Clear()
                    End If

                    '--------------- pour la table d'affichage somme des quantités une seule ligne par article
                    StrSQL = "SELECT CodeArticle,SUM(QteLotArticle) as QteArticle FROM LOT_ARTICLE WHERE " + _
                             "CodeArticle='" + dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeArticle").ToString + _
                             "' GROUP BY CodeArticle"

                    cmdInventaire.Connection = ConnectionServeur
                    cmdInventaire.CommandText = StrSQL
                    daInventaire = New SqlDataAdapter(cmdInventaire)
                    daInventaire.Fill(dsInventaire, "LOT_ARTICLE")

                    '--------------- pour la table d'enregistrement chaque lots dans une ligne
                    StrSQL = "SELECT * FROM LOT_ARTICLE WHERE " + _
                             " CodeArticle='" + _
                             dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeArticle") + "'"

                    cmdInventaire.Connection = ConnectionServeur
                    cmdInventaire.CommandText = StrSQL
                    daInventaire = New SqlDataAdapter(cmdInventaire)
                    daInventaire.Fill(dsInventaire, "LOT_ARTICLE_COMPLET")

                    If dsInventaire.Tables("LOT_ARTICLE").Rows.Count > 0 Then
                        ' cas ou il a au moin un lot
                        For J = 0 To dsInventaire.Tables("LOT_ARTICLE").Rows.Count - 1

                            NouvelArticle("NumeroInventaire") = RecupereNumuero()
                            NouvelArticle("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("CodeArticle")
                            NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("CodeArticle"))
                            NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("CodeArticle"))
                            lArticleEnCours.Text = "   " + NouvelArticle("Designation")

                            Try
                                NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("CodeArticle"))
                            Catch ex As Exception
                            End Try

                            NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                            NouvelArticle("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("CodeArticle"))

                            NouvelArticle("StockInitial") = dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("QteArticle")
                            NouvelArticle("StockActuel") = NouvelArticle("StockInitial")

                            NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("CodeArticle"))
                            NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("CodeArticle"))

                            NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                            NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                            NouvelArticle("CodeArticle") = ""
                            NouvelArticle("CodeABarre") = ""
                            dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)
                        Next

                        '************ enregistrement dans la table INVENTAIRE_DETAILS (juste on ajoute le 
                        '************ numero de lot et chaque lot dans une ligne)

                        For J = 0 To dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows.Count - 1

                            NouvelArticleEnregistrement("NumeroInventaire") = RecupereNumuero()
                            NouvelArticleEnregistrement("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("CodeArticle")
                            NouvelArticleEnregistrement("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("CodeArticle"))
                            NouvelArticleEnregistrement("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("CodeArticle"))
                            lArticleEnCours.Text = "   " + NouvelArticleEnregistrement("Designation")
                            NouvelArticleEnregistrement("NumeroLot") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("NumeroLotArticle")
                            Try
                                NouvelArticleEnregistrement("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("CodeArticle"))
                            Catch ex As Exception
                            End Try
                            NouvelArticleEnregistrement("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                            NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("CodeArticle"))

                            NouvelArticleEnregistrement("StockInitial") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("QteLotArticle")
                            NouvelArticleEnregistrement("StockActuel") = NouvelArticleEnregistrement("StockInitial")

                            NouvelArticleEnregistrement("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("CodeArticle"))
                            NouvelArticleEnregistrement("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("CodeArticle"))

                            NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                            NouvelArticleEnregistrement("Designation") = ""  'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                            NouvelArticleEnregistrement("CodeArticle") = ""
                            NouvelArticleEnregistrement("CodeABarre") = ""
                            NouvelArticleEnregistrement("NumeroLot") = ""
                            dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)
                        Next

                    ElseIf dsInventaire.Tables("LOT_ARTICLE").Rows.Count = 0 Then
                        ' cas ou il n a aucun lot
                        NouvelArticle("NumeroInventaire") = RecupereNumuero()
                        NouvelArticle("CodeArticle") = dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeArticle")
                        NouvelArticle("CodeABarre") = dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeABarre")
                        NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeArticle"))
                        lArticleEnCours.Text = "   " + NouvelArticle("Designation")
                        Try
                            NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeArticle"))
                            NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
                        Catch ex As Exception
                        End Try

                        NouvelArticle("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeArticle"))

                        NouvelArticle("StockInitial") = 0
                        NouvelArticle("StockActuel") = 0

                        NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeArticle"))
                        NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeArticle"))

                        NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                        NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                        NouvelArticle("CodeArticle") = ""
                        NouvelArticle("CodeABarre") = ""
                        dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)

                        '************ enregistrement dans la table INVENTAIRE_DETAILS (juste on ajoute le 
                        '************ numero de lot)

                        NouvelArticleEnregistrement("NumeroInventaire") = RecupereNumuero()
                        NouvelArticleEnregistrement("CodeArticle") = dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeArticle")
                        NouvelArticleEnregistrement("CodeABarre") = dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeABarre")
                        NouvelArticleEnregistrement("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeArticle"))
                        NouvelArticleEnregistrement("NumeroLot") = ""
                        Try
                            NouvelArticleEnregistrement("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeArticle"))
                            NouvelArticleEnregistrement("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))
                        Catch ex As Exception
                        End Try

                        NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeArticle"))

                        NouvelArticleEnregistrement("StockInitial") = 0
                        NouvelArticleEnregistrement("StockActuel") = 0

                        NouvelArticleEnregistrement("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeArticle"))
                        NouvelArticleEnregistrement("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", dsInventaire.Tables("TOUS_ARTICLES").Rows(i).Item("CodeArticle"))

                        NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                        NouvelArticleEnregistrement("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                        NouvelArticleEnregistrement("CodeArticle") = ""
                        NouvelArticleEnregistrement("CodeABarre") = ""
                        NouvelArticleEnregistrement("NumeroLot") = ""
                        dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)
                    End If
                Next

                'Reprendre la capture d'evenement clavier sur le contrôle 
                AddHandler Me.bAjouter.Click, AddressOf Me.bAjouter_Click
                AddHandler Me.bQuitter.Click, AddressOf Me.bQuitter_Click

                ProgressBar.Value = 100
                ProgressBar.Visible = False
                GroupeJauge.Visible = False
            Else
                '----------------------- si on ne charge que les articles qui ont subit des mouvements

                If (dsInventaire.Tables.IndexOf("LOT_ARTICLE") > -1) Then
                    dsInventaire.Tables("LOT_ARTICLE").Clear()
                End If
                If (dsInventaire.Tables.IndexOf("LOT_ARTICLE_COMPLET") > -1) Then
                    dsInventaire.Tables("LOT_ARTICLE_COMPLET").Clear()
                End If

                '------------------ pour la table d'affichage une ligne par article (somme des qtes des lots)
                StrSQL = "SELECT LOT_ARTICLE.CodeArticle,CodeABarre,Designation,SUM(QteLotArticle) as QteArticle" + _
                         ",ARTICLE.CodeForme,ARTICLE.Rayon,ARTICLE.PrixAchatTTC,ARTICLE.PrixVenteTTC  " + _
                         " FROM LOT_ARTICLE " + _
                         "LEFT OUTER JOIN ARTICLE ON LOT_ARTICLE.CodeArticle=ARTICLE.CodeArticle " + _
                         "LEFT OUTER JOIN CATEGORIE ON ARTICLE.CodeCategorie=CATEGORIE.CodeCategorie " + _
                         "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                         "WHERE " + _
                         Cond + _
                         " GROUP BY LOT_ARTICLE.CodeArticle,Designation,ARTICLE.CodeForme,ARTICLE.Rayon, " + _
                         " ARTICLE.PrixAchatTTC,ARTICLE.PrixVenteTTC " + _
                         " ORDER BY " + Trie

                cmdInventaire.Connection = ConnectionServeur
                cmdInventaire.CommandText = StrSQL
                daInventaire = New SqlDataAdapter(cmdInventaire)
                daInventaire.Fill(dsInventaire, "LOT_ARTICLE")
                '------------------ pour la table d'enregistrement une ligne par lot
                StrSQL = "SELECT NumeroLotArticle,LOT_ARTICLE.CodeArticle,CodeABarre,Designation,QteLotArticle" + _
                         ",ARTICLE.CodeForme,ARTICLE.Rayon,ARTICLE.PrixAchatTTC,ARTICLE.PrixVenteTTC  " + _
                         " FROM LOT_ARTICLE " + _
                         "LEFT OUTER JOIN ARTICLE ON LOT_ARTICLE.CodeArticle=ARTICLE.CodeArticle " + _
                         "LEFT OUTER JOIN CATEGORIE ON ARTICLE.CodeCategorie=CATEGORIE.CodeCategorie " + _
                         "LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                         "WHERE " + _
                         Cond + " ORDER BY " + Trie

                cmdInventaire.Connection = ConnectionServeur
                cmdInventaire.CommandText = StrSQL
                daInventaire = New SqlDataAdapter(cmdInventaire)
                daInventaire.Fill(dsInventaire, "LOT_ARTICLE_COMPLET")

                'Arrêter la capture d'evenement clavier sur le contrôle 
                RemoveHandler Me.bAjouter.Click, AddressOf Me.bAjouter_Click
                RemoveHandler Me.bQuitter.Click, AddressOf Me.bQuitter_Click

                ProgressBar.Visible = True
                GroupeJauge.Visible = True
                ProgressBar.Value = 0
                unite = 100 / ((dsInventaire.Tables("LOT_ARTICLE").Rows.Count - 1) * 2)


                For J = 0 To dsInventaire.Tables("LOT_ARTICLE").Rows.Count - 1

                    If unite * J < (dsInventaire.Tables("LOT_ARTICLE").Rows.Count - 1) Then
                        If (unite * J) <= 50 Then
                            ProgressBar.Value = CInt(unite * J)
                        Else
                            ProgressBar.Value = 50
                        End If
                    End If

                    Application.DoEvents()

                    NouvelArticle("NumeroInventaire") = RecupereNumuero()
                    NouvelArticle("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("CodeArticle")
                    NouvelArticle("CodeABarre") = dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("CodeABarre")
                    NouvelArticle("Designation") = dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("Designation") 'RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("CodeArticle"))
                    lArticleEnCours.Text = "   " + NouvelArticle("Designation")
                    Try
                        NouvelArticle("CodeForme") = dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("CodeForme") 'RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("CodeArticle"))
                    Catch ex As Exception
                    End Try

                    NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                    NouvelArticle("Rayon") = dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("Rayon") 'RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

                    NouvelArticle("StockInitial") = dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("QteArticle")
                    NouvelArticle("StockActuel") = NouvelArticle("StockInitial")

                    NouvelArticle("PrixAchatTTC") = dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("PrixAchatTTC") 'RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("CodeArticle"))
                    NouvelArticle("PrixVenteTTC") = dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("PrixVenteTTC") 'RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE").Rows(J).Item("CodeArticle"))

                    NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                    NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                    NouvelArticle("CodeArticle") = ""
                    NouvelArticle("CodeABarre") = ""
                    dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)

                Next

                ProgressBar.Value = 50
                ProgressBar.Visible = False
                GroupeJauge.Visible = False

                ProgressBar.Visible = True
                GroupeJauge.Visible = True
                ProgressBar.Value = 50
                unite = 100 / ((dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows.Count - 1) * 2)

                For J = 0 To dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows.Count - 1
                    '************ enregistrement dans la table INVENTAIRE_DETAILS (juste on ajoute le 
                    '************ numero de lot)
                    If unite * J < (dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows.Count - 1) Then
                        If (unite * J) <= 100 Then
                            ProgressBar.Value = CInt(unite * J + 50)
                        Else
                            ProgressBar.Value = 100
                        End If
                    End If

                    Application.DoEvents()

                    NouvelArticleEnregistrement("NumeroInventaire") = RecupereNumuero()
                    NouvelArticleEnregistrement("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("CodeArticle")
                    NouvelArticleEnregistrement("CodeABarre") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("CodeABarre")
                    NouvelArticleEnregistrement("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("CodeArticle"))
                    lArticleEnCours.Text = "   " + NouvelArticleEnregistrement("Designation")
                    NouvelArticleEnregistrement("NumeroLot") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("NumeroLotArticle")

                    Try
                        NouvelArticleEnregistrement("CodeForme") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("CodeForme") 'RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("CodeArticle"))
                    Catch ex As Exception
                    End Try

                    NouvelArticleEnregistrement("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                    NouvelArticleEnregistrement("Rayon") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("Rayon") 'RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

                    NouvelArticleEnregistrement("StockInitial") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("QteLotArticle")
                    NouvelArticleEnregistrement("StockActuel") = NouvelArticleEnregistrement("StockInitial")

                    NouvelArticleEnregistrement("PrixAchatTTC") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("PrixAchatTTC") 'RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("CodeArticle"))
                    NouvelArticleEnregistrement("PrixVenteTTC") = dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("PrixVenteTTC") 'RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", dsInventaire.Tables("LOT_ARTICLE_COMPLET").Rows(J).Item("CodeArticle"))

                    NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                    NouvelArticleEnregistrement("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                    NouvelArticleEnregistrement("CodeArticle") = ""
                    NouvelArticleEnregistrement("NumeroLot") = ""
                    dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)
                Next
                'Reprendre la capture d'evenement clavier sur le contrôle 
                AddHandler Me.bAjouter.Click, AddressOf Me.bAjouter_Click
                AddHandler Me.bQuitter.Click, AddressOf Me.bQuitter_Click

                ProgressBar.Value = 100
                ProgressBar.Visible = False
                GroupeJauge.Visible = False

            End If
        End If

        ' liaison avec la gride pour l affichage

        With gArticles
            .Columns.Clear()
            .DataSource = dsInventaire
            .DataMember = "INVENTAIRE_DETAILS_AFFICHE"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Rayon").Caption = "Rayon"
            .Columns("StockInitial").Caption = "Stock Initial"
            .Columns("StockActuel").Caption = "nouveau Stock"
            .Columns("PrixAchatTTC").Caption = "Prix achat TTC"
            .Columns("TotalAchatTTC").Caption = "Total Achat TTC"

            .Columns("QteChange").Caption = "QteChange"
            .Columns("QteChange").DataField = ""

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            ''coloriage de la liste 
            'For i = 0 To .Columns.Count - 1
            '    .Columns(i).FilterDropdown = True
            'Next

            .FilterBar = True

            .Splits(0).DisplayColumns("CodeABarre").Locked = False
            .Splits(0).DisplayColumns("Designation").Locked = False
            .Splits(0).DisplayColumns("StockActuel").Locked = False

            .Splits(0).DisplayColumns("NumeroInventaire").Width = 0
            .Splits(0).DisplayColumns("NumeroInventaire").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 100
            .Splits(0).DisplayColumns("Designation").Width = 320
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("Rayon").Width = 80
            .Splits(0).DisplayColumns("CodeForme").Width = 0
            .Splits(0).DisplayColumns("StockInitial").Width = 80
            .Splits(0).DisplayColumns("StockActuel").Width = 80
            .Splits(0).DisplayColumns("PrixAchatTTC").Width = 80
            .Splits(0).DisplayColumns("TotalAchatTTC").Width = 80
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 0
            .Splits(0).DisplayColumns("PrixVenteTTC").Visible = False
            .Splits(0).DisplayColumns("CodeForme").Visible = False

            .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("StockActuel").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).DisplayColumns("QteChange").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True

        End With

        If RechercheSelective = True Then
            i = 0
            Do While i < dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count
                If dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("CodeArticle").ToString = "" Then
                    dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Delete()
                    i = 0
                Else
                    i = i + 1
                End If
            Loop
            'ajout d'un nouvel enregistrement vide dans les datatables convenables
            NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
            NouvelArticle("Designation") = ""
            NouvelArticle("CodeArticle") = ""
            NouvelArticle("CodeABarre") = ""
            dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)

            NouvelleAchat = dsInventaire.Tables("INVENTAIRE").NewRow()
            dsInventaire.Tables("INVENTAIRE").Rows.Add(NouvelleAchat)
        End If

        Me.gArticles.Splits(0).DisplayColumns(1).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(4).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(5).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(6).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(7).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(9).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(10).AllowFocus = False
        Me.gArticles.Splits(0).DisplayColumns(11).AllowFocus = False

        '------------------------------ initialisation des differents zones de textes 
        '------------------------------ initialisation des variables globaux 

        lActuelleAchatTTC.Text = "0.000"
        lActuelleVenteTTC.Text = "0.000"

        lInitialeAchatTTC.Text = "0.000"
        lInitialeVenteTTC.Text = "0.000"

        lDifferenceAchatTTC.Text = "0.000"
        lDifferenceVenteTTC.Text = "0.000"

        lDifferenceAchatTTCPourcentage.Text = "0.000"
        lDifferenceVenteTTCPourcentage.Text = "0.000"

        lOperateur.Text = "-"
        lDateInventaire.Text = System.DateTime.Now
        lNumeroInventaire.Text = "-------------"

        bAnnuler.Enabled = True
        bConfirmer.Enabled = True

        bFirst.Visible = False
        bPrevious.Visible = False
        bNext.Visible = False
        bLast.Visible = False
        bAjouter.Enabled = False

        bAfficherLot.Enabled = True

        GroupeRemarque.Enabled = True

        bTerminal.Enabled = True

        CalculerMontants()

        gArticles.Focus()
        gArticles.Col = 2
        gArticles.Row = gArticles.RowCount - 1
        gArticles.EditActive = True

    End Sub

    Private Sub gArticles_AfterColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gArticles.AfterColEdit
        'If gArticles.Col = 8 Then
        '    If IsNumeric(gArticles.Columns("StockActuel").Value) = True Then
        '        If gArticles.Columns("StockInitial").Value <> gArticles.Columns("StockActuel").Value Then
        '            If PanelAfficherLots.Visible = False Then
        '                ChangerLotsArticle(gArticles.Columns("CodeArticle").Value, gArticles.Columns("StockActuel").Value) 'ne reste que le lot sans numéro et les autres seront 0
        '                'AfficherLotArticle()
        '                If gListeLotArticle.Visible = True Then
        '                    Exit Sub
        '                End If
        '            End If
        '        End If
        '    ElseIf gArticles.Columns("StockActuel").Value.ToString <> "-" Then
        '        gArticles.Columns("StockActuel").Value = ""
        '    End If
        'End If
        CalculerMontants()
    End Sub

    Private Sub gArticles_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gArticles.Change
        If (gArticles.Col = 3 And gArticles.Columns("Designation").Value <> "") Or gArticles.Col = 2 Then
            Dim StrSQL1 As String = ""
            Dim I As Integer
            Dim NbLigne As Integer
            NbLigne = gArticles.RowCount

            With gListeRecherche
                .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
                .Top = Me.gArticles.Top + Me.gArticles.RowTop(Me.gArticles.Row) + Me.gArticles.Splits(0).ColumnFooterHeight
            End With

            Try
                dsInventaire.Tables("ARTICLE").Clear()
            Catch ex As Exception

            End Try
            If gArticles.Row = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1 And gArticles.Col = 3 Then
                gListeRecherche.Visible = True
            Else
                gListeRecherche.Visible = False
            End If

            'chargement des articles qui sont mis en jeu
            If gArticles.Col = 3 Then
                If gArticles.Columns("Designation").Value.ToString.Length > 1 Then
                    If gArticles.Columns("Designation").Value.ToString.IndexOf(".") > 0 Then   'gArticles.Columns("Designation").Value.ToString.Substring(gArticles.Columns("Designation").Value.ToString.Length - 1, 1) Like "." And IsNumeric(gArticles.Columns("Designation").Value.ToString.Substring(0, gArticles.Columns("Designation").Value.ToString.Length - 1))

                        StrSQL1 = "SELECT CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC" + _
                                  " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  " WHERE  Supprime = 0 AND " + _
                                  " ltrim(str(PrixVenteTTC,10,3)) LIKE '" + _
                                  gArticles.Columns("Designation").Value + "%' ORDER BY PrixVenteTTC"
                    Else
                        StrSQL1 = "SELECT CodeArticle," + _
                                  "Designation," + _
                                  "LibelleForme," + _
                                  "PrixVenteTTC" + _
                                  " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                                  " WHERE  Supprime = 0 AND " + _
                                  " Designation LIKE '" + gArticles.Columns("Designation").Value + _
                                  "%' ORDER BY Designation"
                    End If
                Else
                    StrSQL1 = "SELECT CodeArticle," + _
                              "Designation," + _
                              "LibelleForme," + _
                              "PrixVenteTTC" + _
                              " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme " + _
                              "WHERE  Supprime = 0 AND " + _
                              " Designation LIKE '" + gArticles.Columns("Designation").Value + _
                              "%'ORDER BY Designation"
                End If
            ElseIf gArticles.Col = 2 Then
                StrSQL1 = "SELECT CodeArticle," + _
                          "Designation," + _
                          "LibelleForme," + _
                          "PrixVenteTTC" + _
                          " FROM ARTICLE LEFT OUTER JOIN FORME_ARTICLE ON ARTICLE.CodeForme=FORME_ARTICLE.CodeForme" + _
                          " WHERE Supprime = 0 AND " + _
                          " CodeABarre LIKE '" + gArticles.Columns("CodeArticle").Value + _
                          "' ORDER BY Designation"

            End If
            cmdInventaire.Connection = ConnectionServeur
            cmdInventaire.CommandText = StrSQL1
            daInventaire = New SqlDataAdapter(cmdInventaire)
            daInventaire.Fill(dsInventaire, "ARTICLE")

            If dsInventaire.Tables("ARTICLE").Rows.Count > 0 Then
                dr = dsInventaire.Tables("ARTICLE").Rows(0)
            End If

            With gListeRecherche
                .Columns.Clear()
                .DataSource = dsInventaire
                .DataMember = "ARTICLE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code Article"
                .Columns("Designation").Caption = "Designation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("PrixVenteTTC").Caption = "Prix de vente"

                ' Centrer tous les entêtes
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' CentreR tous les valeurs
                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                For I = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(I).Visible = False
                Next

                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("Designation").Visible = True
                .Splits(0).DisplayColumns("LibelleForme").Visible = True
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = True

                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
                .Splits(0).DisplayColumns("Designation").Width = 300
                .Splits(0).DisplayColumns("LibelleForme").Width = 100
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 120


                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.Aqua
                .Splits(0).DisplayColumns("LibelleForme").Style.BackColor = Color.Aqua
                .Splits(0).DisplayColumns("PrixVenteTTC").Style.BackColor = Color.Aqua

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 20
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
            End With
        End If
    End Sub
    Public Function RecupereNumuero()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max([NumeroInventaire]) FROM [INVENTAIRE]"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If
        Return ValeurRetour
    End Function

    Private Sub gArticles_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyUp

        Dim i As Integer = 0
        Dim j As Integer = 0
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        'Dim TestNumeroLot As Integer
        Dim QuantiteAAjouter As Integer = 0
        '---------------------------------- test si on est en mode saisi ou non ---------------------------
        If mode <> "Ajout" Then
            Exit Sub
        End If


        '---------------------------------- afficher fiche article 
        If e.KeyCode = Keys.F1 And mode = "Ajout" And gArticles.Columns("CodeArticle").Value.ToString <> "" Then
            AfficherFicheArticle(gArticles.Columns("CodeArticle").Value, gArticles.Columns("StockInitial").Value, gArticles.Columns("Designation").Value)
            Exit Sub
        End If

        '---------------------------------- suppression du dernier ligne vide si on a deux lignes au mm temps vide
        '---------------------------------- cas ou on supprime dernier ligne
        If dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1 = 1 And gArticles(0, "CodeArticle").ToString = "" And gArticles(1, "CodeArticle").ToString = "" Then
            gArticles.MoveLast()
            gArticles.MovePrevious()
            gArticles.Delete()
        End If
        '---------------------------------- test du type de la valeur d'entrée dans la colonne quantité (numéric) ------------
        '---------------------------------- test du valeur d'entrée dans la colonne quantité < 99999 ------------

        If gArticles.Col = 10 Then
            If gArticles.Columns("StockActuel").Value.ToString <> "-" Then   ' pour les quantité négatives
                If IsNumeric(gArticles.Columns("StockActuel").Value) = False Then
                    gArticles.Columns("StockActuel").Value = ""
                    gArticles.Col = 10
                    gArticles.EditActive = True
                    Exit Sub
                End If
                If (Val(gArticles.Columns("StockActuel").Value) = 0) And e.KeyCode = Keys.Enter Then
                    gArticles.Columns("StockActuel").Value = ""
                    gArticles.Col = 10
                    gArticles.EditActive = True
                    Exit Sub
                End If
                If (Val(gArticles.Columns("StockActuel").Value) > 99999 Or Val(gArticles.Columns("StockActuel").Value) < -99999) And e.KeyCode = Keys.Enter Then
                    gArticles.Columns("StockActuel").Value = "1"
                    MsgBox("Quantité trop grande !", MsgBoxStyle.Critical, "Erreur")
                    gArticles.Col = 10
                    gArticles.EditActive = True
                    Exit Sub
                Else
                    NouvelArticle("StockActuel") = NouvelArticle("StockInitial")

                End If
            End If

        End If

        '---------------------------------- suppression de la ligne selectionnées -------------------------
        If (e.KeyCode = Keys.F7 Or e.KeyCode = Keys.Delete) And gArticles.Row <= dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1 Then
            For i = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
                If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle").ToString = gArticles.Columns("CodeArticle").Value.ToString Then
                    dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Delete()

                    GoTo suivant
                End If
            Next
Suivant:
            gArticles.Delete()





            CalculerMontants()




            If dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1 <= 0 Then
                bAjouter_Click(sender, e)
            End If
            Exit Sub
        End If
        '------------------------------ recherche par code ----------------------------------------------
        If gArticles.Col = 2 And e.KeyCode = Keys.Enter And gArticles.Row = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1 Then
            j = gArticles.Row
            For i = 0 To gArticles.RowCount - 1
                If gArticles(i, "CodeArticle").ToString = gArticles(j, "CodeArticle").ToString And i <> j Then
                    gArticles(j, "CodeArticle") = ""
                    gArticles.Col = j
                    gArticles.EditActive = True
                    Exit Sub
                End If
            Next
            If gArticles.Columns("CodeABarre").Value <> "" Then
                ChargerDetailArticle(CStr(gArticles.Columns("CodeABarre").Value))
                Exit Sub
            Else
                gArticles.Col = 3
            End If
        ElseIf gArticles.Col = 2 And e.KeyCode = Keys.Enter And gArticles.Row < dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1 Then
            gArticles.Col = 3
        End If
        '---------------------------------- masquer la liste de recherche si la designation est vide -----------
        If e.KeyCode = Keys.Back And gArticles.Columns("Designation").Value.ToString = "" And gArticles.Col = 3 Then
            gListeRecherche.Visible = False
        End If
        '---------------------------- pour passer à la navigation dans la petite liste de recherhce ----------------
        If (e.KeyCode = Keys.Down Or e.KeyCode = Keys.Up) And gListeRecherche.Visible = True Then
            gListeRecherche.Focus()
            gListeRecherche.Col = 2
        End If
        '---------------------------- si l'artcile n'existe pas dans la liste de recherhce sa quantité doit etre 0  ----
        'If dsInventaire.Tables("ARTICLE").Rows.Count <= 0 And gListeRecherche.Visible = True Then '
        '    gArticles.Columns("Qte").Value = 0
        '    gArticles.Col = 2
        'End If
        '---------------------------- calcul des montants --------------------------------------------------------
        If (gArticles.Col = 8) And (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right) Then



            'CalculerMontants()



        End If
        'gArticles.Row
        If gArticles.Col = 8 Then
            If IsNumeric(gArticles.Columns("StockActuel").Value) = True Then
                If (e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Tab) And Val(gArticles.Columns("StockInitial").Value) <> Val(gArticles.Columns("StockActuel").Value) Then
                    If PanelAfficherLots.Visible = False Then


                        'ChangerLotsArticle(gArticles.Columns("CodeArticle").Value, gArticles.Columns("StockActuel").Value) 'ne reste que le lot sans numéro et les autres seront 0


                        'AfficherLotArticle()
                        If gListeLotArticle.Visible = True Then
                            Exit Sub
                        End If
                    End If
                End If
            ElseIf gArticles.Columns("StockActuel").Value.ToString <> "-" Then
                gArticles.Columns("StockActuel").Value = ""
            End If
        End If

        '****************************************************************************************
        '********************** verouillage des lignes déja confirmées **************************
        '****************************************************************************************

        If (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right Or e.KeyCode = Keys.Enter) And gArticles.Row <> dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1 Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        ElseIf gArticles.Row = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1 Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
            'gArticles.Columns("CodeABarre")        
            gArticles.Splits(0).DisplayColumns("Designation").Locked = False
        End If

        If (gArticles.Col = 2 Or gArticles.Col = 8) And gArticles.Columns("Designation").Value.ToString <> "" Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        End If
        If gArticles.Col = 3 And gArticles.Columns("CodeArticle").Value.ToString <> "" Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        End If

        '--------------------- traitement du clique sur le bouton ENTREE selon la colonne ---------------------------

        If e.KeyCode = Keys.Enter Then ' And (dsInventaire.Tables("ARTICLE").Rows.Count > 0 Or gArticles.Row <> dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1) Then
            gListeRecherche_KeyUp(sender, e)     ' pour charger les informations de l'article a partir du fiche article

            If gArticles.Col = 3 Then
                If gArticles.Columns("CodeArticle").Value.ToString = "" Then    ' si l'article choisit n'existe pas dans la liste de recherche
                    gArticles.Col = 3
                    gArticles.Columns("Designation").Value = ""
                Else
                    gArticles.Col = 8
                End If
            ElseIf gArticles.Col = 8 Then   ' si on est dans la colonne prix d'achat on passe au nouveau ligne

                If gArticles(gArticles.RowCount - 1, ("CodeArticle")).ToString <> "" Then
                    NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                    NouvelArticle("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                    NouvelArticle("CodeArticle") = ""
                    NouvelArticle("CodeABarre") = ""
                    dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)
                End If
                gArticles.MoveNext()
                Try
                    dsInventaire.Tables("ARTICLE").Clear()
                Catch ex As Exception
                End Try

                'Test Si on va passer vers Designation ou bien la Qte
                If gArticles(gArticles.Row, ("CodeArticle")).ToString <> "" Then
                    gArticles.Col = 8
                Else
                    gArticles.Col = 2
                End If



                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
                'gArticles.EditActive = True
            End If

            If (e.KeyCode = Keys.Up Or e.KeyCode = Keys.Down Or e.KeyCode = Keys.Left Or e.KeyCode = Keys.Right Or e.KeyCode = Keys.Enter) And gArticles.Row <> dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1 Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
                gArticles.Splits(0).DisplayColumns("Designation").Locked = True
            ElseIf gArticles.Row = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1 Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
            End If

        End If
        
    End Sub

    Private Sub gListeRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListeRecherche.KeyUp
        ' dans cet évènenment on teste si l évènenment est due à un clique sur le boutton entrée et si l'utilisateur a choisi 
        ' un article et si le curseur est dans la colonne de designation 

        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim NumeroLot As String = ""
        Dim i As Integer = 0
        Dim j As Integer = 0

        If gListeRecherche.Visible = False Then
            Exit Sub
        End If

        If (dsInventaire.Tables.IndexOf("LOT_ARTICLE") > -1) Then
            dsInventaire.Tables("LOT_ARTICLE").Clear()
        End If
        If (dsInventaire.Tables.IndexOf("LOT_ARTICLE_SELECTION") > -1) Then
            dsInventaire.Tables("LOT_ARTICLE_SELECTION").Clear()
        End If

        If e.KeyCode = Keys.Back Then
            gArticles.Focus()
            gArticles.Col = 3
            gArticles.MoveLast()
            gArticles.EditActive = True
        End If

        Dim NumeroLigne As Integer
        Dim DataRowRecherche As DataRow

        For i = 0 To gArticles.RowCount - 1
            If gArticles(i, "CodeArticle").ToString = gListeRecherche.Columns("CodeArticle").Value.ToString And e.KeyCode = Keys.Enter Then
                gArticles.Columns("CodeArticle").Value = ""
                gArticles.Columns("designation").Value = ""
                gArticles.Columns("CodeABarre").Value = ""
                gListeRecherche.Visible = False
                gArticles.Focus()
                gArticles.Col = 3
                gArticles.EditActive = True
                Exit Sub
            End If
        Next

        If e.KeyCode = Keys.Enter And (gArticles.Col = 5 Or gArticles.Col = 3) Then
            If dsInventaire.Tables("ARTICLE").Rows.Count > 0 Then
                '--------------------- rehcherche de l'index de la ligne qui contient l'article recherché ---------------
                For j = 0 To dsInventaire.Tables("ARTICLE").Rows.Count - 1
                    DataRowRecherche = dsInventaire.Tables("ARTICLE").Rows(j)
                    If DataRowRecherche.Item("CodeArticle").ToString = gListeRecherche.Columns("CodeArticle").Value.ToString Then
                        NumeroLigne = j
                    End If
                Next
                '------------------- chargement des données depuis la liste des lots disponibles ------------- 
                dr = dsInventaire.Tables("ARTICLE").Rows(NumeroLigne)

                '"""""""""""""""""""""""""""""
                If (dsInventaire.Tables.IndexOf("LOT_ARTICLE") > -1) Then
                    dsInventaire.Tables("LOT_ARTICLE").Clear()
                End If

                StrSQL = "SELECT CodeArticle,SUM(QteLotArticle) as QteArticle FROM LOT_ARTICLE" + _
                         " WHERE " + _
                         " CodeArticle='" + dr.Item("CodeArticle").ToString + _
                         "' GROUP BY CodeArticle"
                cmdInventaire.Connection = ConnectionServeur
                cmdInventaire.CommandText = StrSQL
                daInventaire = New SqlDataAdapter(cmdInventaire)
                daInventaire.Fill(dsInventaire, "LOT_ARTICLE")
                '""""""""""""""""""""""""""""""
                '---------- pour les articles qui n'ont pas de lot : possibilité de création d'un nouveau lot
                If dsInventaire.Tables("LOT_ARTICLE").Rows.Count = 0 Then
                    ' pour les articles qui n'ont pas de lot : possibilité de création d'un nouveau lot
                    Dim reponse As MsgBoxResult
                    reponse = MsgBox("Cet Article n'admet pas de lot," + Chr(13) + "Voulez vous créer un nouveau lot!" + Chr(13), MsgBoxStyle.YesNo, "Erreur")
                    If reponse = MsgBoxResult.Yes Then

                        NouvelArticle("NumeroInventaire") = RecupereNumuero()
                        NouvelArticle("CodeArticle") = dr.Item("CodeArticle")
                        NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                        'gArticles.Columns("CodeABarre").Value = NouvelArticle("CodeABarre")
                        NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))

                        Try
                            NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                        Catch ex As Exception
                        End Try

                        NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                        NouvelArticle("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))

                        NouvelArticle("StockInitial") = 0
                        NouvelArticle("StockActuel") = 0

                        NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                        NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))

                        '------------------ ajout dans la table des lots article elle rassemble à celle de la base

                        NouvelArticleEnregistrement("NumeroInventaire") = NouvelArticle("NumeroInventaire")
                        NouvelArticleEnregistrement("CodeArticle") = NouvelArticle("CodeArticle")
                        NouvelArticleEnregistrement("CodeABarre") = NouvelArticle("CodeABarre")
                        NouvelArticleEnregistrement("Designation") = NouvelArticle("Designation")

                        NouvelArticleEnregistrement("NumeroLot") = ""

                        NouvelArticleEnregistrement("CodeForme") = NouvelArticle("CodeForme")
                        NouvelArticleEnregistrement("LibelleForme") = NouvelArticle("LibelleForme")
                        NouvelArticleEnregistrement("Rayon") = NouvelArticle("Rayon")

                        NouvelArticleEnregistrement("StockInitial") = 0
                        NouvelArticleEnregistrement("StockActuel") = 0

                        NouvelArticleEnregistrement("PrixAchatTTC") = NouvelArticle("PrixAchatTTC")
                        NouvelArticleEnregistrement("PrixVenteTTC") = NouvelArticle("PrixVenteTTC")

                        NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                        NouvelArticleEnregistrement("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                        NouvelArticleEnregistrement("CodeArticle") = ""
                        NouvelArticleEnregistrement("CodeABarre") = ""
                        dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)

                    End If
                    gListeRecherche.Visible = False
                    gArticles.Focus()
                    gArticles.Col = 3

                    Exit Sub
                End If

                'dans le cas ou il ya des lots 
                For i = 0 To dsInventaire.Tables("LOT_ARTICLE").Rows.Count - 1

                    NouvelArticle("NumeroInventaire") = RecupereNumuero()
                    NouvelArticle("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE").Rows(i).Item("CodeArticle")
                    NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                    gArticles.Columns("CodeABarre").Value = NouvelArticle("CodeABarre")
                    NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

                    Try
                        NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                    Catch ex As Exception
                    End Try

                    NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                    NouvelArticle("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

                    NouvelArticle("StockInitial") = dsInventaire.Tables("LOT_ARTICLE").Rows(i).Item("QteArticle")
                    NouvelArticle("StockActuel") = NouvelArticle("StockInitial")

                    NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))
                    NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", NouvelArticle("CodeArticle"))

                    '------------------ ajout dans la table des lots article elle rassemble àcelle de la base

                    'chargement des lots
                    StrSQL = "SELECT NumeroLotArticle,CodeArticle,QteLotArticle " + _
                             " FROM LOT_ARTICLE WHERE  LOT_ARTICLE.CodeArticle='" + _
                             gArticles.Columns("CodeArticle").Value.ToString + "'"

                    cmdInventaire.Connection = ConnectionServeur
                    cmdInventaire.CommandText = StrSQL
                    daInventaire.Fill(dsInventaire, "LOT_ARTICLE_SELECTION")
                    cbInventaire = New SqlCommandBuilder(daInventaire)

                    For j = 0 To dsInventaire.Tables("LOT_ARTICLE_SELECTION").Rows.Count - 1
                        NouvelArticleEnregistrement("NumeroInventaire") = NouvelArticle("NumeroInventaire")
                        NouvelArticleEnregistrement("CodeArticle") = NouvelArticle("CodeArticle")
                        NouvelArticleEnregistrement("CodeABarre") = NouvelArticle("CodeABarre")
                        NouvelArticleEnregistrement("Designation") = NouvelArticle("Designation")

                        NouvelArticleEnregistrement("NumeroLot") = dsInventaire.Tables("LOT_ARTICLE_SELECTION").Rows(j).Item("NumeroLotArticle")

                        NouvelArticleEnregistrement("CodeForme") = NouvelArticle("CodeForme")
                        NouvelArticleEnregistrement("LibelleForme") = NouvelArticle("LibelleForme")
                        NouvelArticleEnregistrement("Rayon") = NouvelArticle("Rayon")

                        NouvelArticleEnregistrement("StockInitial") = dsInventaire.Tables("LOT_ARTICLE_SELECTION").Rows(j).Item("QteLotArticle")
                        NouvelArticleEnregistrement("StockActuel") = dsInventaire.Tables("LOT_ARTICLE_SELECTION").Rows(j).Item("QteLotArticle")

                        NouvelArticleEnregistrement("PrixAchatTTC") = NouvelArticle("PrixAchatTTC")
                        NouvelArticleEnregistrement("PrixVenteTTC") = NouvelArticle("PrixVenteTTC")

                        NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                        NouvelArticleEnregistrement("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                        NouvelArticleEnregistrement("CodeArticle") = ""
                        NouvelArticleEnregistrement("CodeABarre") = ""
                        dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)
                    Next

                Next

            End If
            gListeRecherche.Visible = False
            gArticles.Focus()
            gArticles.Col = 3
        End If
    End Sub

    Public Sub ChargerDetailArticle(ByVal CodeABarre As String)
        Dim resultat As String = ""
        Dim StrSQL As String = ""
        Dim cmd As New SqlCommand
        Dim NumeroLot As String = ""
        Dim i As Integer = 0
        Dim j As Integer = 0
        Dim CodeArticle As String = ""

        CodeArticle = CStr(RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "CodeABarre", CodeABarre))

        If CodeArticle = "" Then
            gArticles.Columns("CodeABarre").Value = ""
            gArticles.Col = 3
            Exit Sub
        End If

        If (dsInventaire.Tables.IndexOf("LOT_ARTICLE") > -1) Then
            dsInventaire.Tables("LOT_ARTICLE").Clear()
        End If
        If (dsInventaire.Tables.IndexOf("LOT_ARTICLE_SELECTION") > -1) Then
            dsInventaire.Tables("LOT_ARTICLE_SELECTION").Clear()
        End If

        StrSQL = "SELECT CodeArticle,SUM(QteLotArticle) as QteArticle FROM LOT_ARTICLE " + _
                 "WHERE CodeArticle='" + CodeArticle + _
                 "' GROUP BY CodeArticle"
        cmdInventaire.Connection = ConnectionServeur
        cmdInventaire.CommandText = StrSQL
        daInventaire = New SqlDataAdapter(cmdInventaire)
        daInventaire.Fill(dsInventaire, "LOT_ARTICLE")

        If dsInventaire.Tables("LOT_ARTICLE").Rows.Count = 0 Then
            '----- pour les articles qui n'ont pas de lot : possibilité de création d'un nouveau lot

            resultat = CStr(RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle))
            If resultat <> "" Then
                Dim reponse As MsgBoxResult
                reponse = MsgBox("Cet Article n'admet pas de lot," + Chr(13) + "Voulez vous créer un nouveau lot!" + Chr(13), MsgBoxStyle.YesNo, "Erreur")
                If reponse = MsgBoxResult.Yes Then
                    NouvelArticle("NumeroInventaire") = RecupereNumuero()
                    NouvelArticle("CodeArticle") = CodeArticle
                    NouvelArticle("CodeABarre") = CodeABarre
                    NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)
                    Try
                        NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", dr.Item("CodeArticle"))
                        NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                    Catch ex As Exception
                    End Try

                    NouvelArticle("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", CodeArticle)
                    NouvelArticle("StockInitial") = 0
                    NouvelArticle("StockActuel") = 0

                    NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", CodeArticle)
                    NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", CodeArticle)

                    '------------------ ajout dans la table des lots article elle rassemble à celle de la base

                    NouvelArticleEnregistrement("NumeroInventaire") = NouvelArticle("NumeroInventaire")
                    NouvelArticleEnregistrement("CodeArticle") = NouvelArticle("CodeArticle")
                    NouvelArticleEnregistrement("CodeABarre") = NouvelArticle("CodeABarre")
                    NouvelArticleEnregistrement("Designation") = NouvelArticle("Designation")

                    NouvelArticleEnregistrement("NumeroLot") = ""

                    NouvelArticleEnregistrement("CodeForme") = NouvelArticle("CodeForme")
                    NouvelArticleEnregistrement("LibelleForme") = NouvelArticle("LibelleForme")
                    NouvelArticleEnregistrement("Rayon") = NouvelArticle("Rayon")

                    NouvelArticleEnregistrement("StockInitial") = 0
                    NouvelArticleEnregistrement("StockActuel") = 0

                    NouvelArticleEnregistrement("PrixAchatTTC") = NouvelArticle("PrixAchatTTC")
                    NouvelArticleEnregistrement("PrixVenteTTC") = NouvelArticle("PrixVenteTTC")

                Else
                    gArticles.Columns("CodeArticle").Value = ""
                    gArticles.Columns("CodeABarre").Value = ""
                End If

                'gArticles.Refresh()
                'gArticles.Col = 3

            Else
                gArticles.Columns("CodeArticle").Value = ""
                gArticles.Columns("CodeABarre").Value = ""
                'gArticles.Col = 3
                'gArticles.Refresh()

            End If
            gArticles.Refresh()
            gArticles.Col = 3
            Exit Sub
        End If

        '---------- affichage de tous les lots disponible pour l'article choisie

        For i = 0 To dsInventaire.Tables("LOT_ARTICLE").Rows.Count - 1
            resultat = CStr(RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle))
            If resultat <> "" Then
                '------------------ ajout dans la table qui somme les quantité (juste pour l affichage)
                NouvelArticle("NumeroInventaire") = RecupereNumuero()
                NouvelArticle("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE").Rows(i).Item("CodeArticle")
                NouvelArticle("CodeABarre") = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)
                'NouvelArticle("NumeroLot") = dsInventaire.Tables("LOT_ARTICLE").Rows(i).Item("NumeroLotArticle")

                Try
                    NouvelArticle("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", CodeArticle)
                Catch ex As Exception
                End Try

                NouvelArticle("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticle("CodeForme"))

                NouvelArticle("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", CodeArticle)

                NouvelArticle("StockInitial") = dsInventaire.Tables("LOT_ARTICLE").Rows(i).Item("QteArticle")
                NouvelArticle("StockActuel") = NouvelArticle("StockInitial")

                NouvelArticle("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", CodeArticle)
                NouvelArticle("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", CodeArticle)

                '------------------ ajout dans la table des lots article elle rassemble à celle de la base

                'chargement des lots
                StrSQL = "SELECT NumeroLotArticle,CodeArticle,QteLotArticle " + _
                         " FROM LOT_ARTICLE WHERE  LOT_ARTICLE.CodeArticle='" + _
                         gArticles.Columns("CodeArticle").Value.ToString + "'"

                cmdInventaire.Connection = ConnectionServeur
                cmdInventaire.CommandText = StrSQL
                daInventaire.Fill(dsInventaire, "LOT_ARTICLE_SELECTION")
                cbInventaire = New SqlCommandBuilder(daInventaire)

                For j = 0 To dsInventaire.Tables("LOT_ARTICLE_SELECTION").Rows.Count - 1
                    NouvelArticleEnregistrement("NumeroInventaire") = NouvelArticle("NumeroInventaire")
                    NouvelArticleEnregistrement("CodeArticle") = NouvelArticle("CodeArticle")
                    NouvelArticleEnregistrement("CodeABarre") = NouvelArticle("CodeABarre")
                    NouvelArticleEnregistrement("Designation") = NouvelArticle("Designation")

                    NouvelArticleEnregistrement("NumeroLot") = dsInventaire.Tables("LOT_ARTICLE_SELECTION").Rows(j).Item("NumeroLotArticle")

                    NouvelArticleEnregistrement("CodeForme") = NouvelArticle("CodeForme")
                    NouvelArticleEnregistrement("LibelleForme") = NouvelArticle("LibelleForme")
                    NouvelArticleEnregistrement("Rayon") = NouvelArticle("Rayon")

                    NouvelArticleEnregistrement("StockInitial") = dsInventaire.Tables("LOT_ARTICLE_SELECTION").Rows(j).Item("QteLotArticle")
                    NouvelArticleEnregistrement("StockActuel") = dsInventaire.Tables("LOT_ARTICLE_SELECTION").Rows(j).Item("QteLotArticle")

                    NouvelArticleEnregistrement("PrixAchatTTC") = NouvelArticle("PrixAchatTTC")
                    NouvelArticleEnregistrement("PrixVenteTTC") = NouvelArticle("PrixVenteTTC")

                    NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                    NouvelArticleEnregistrement("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                    NouvelArticleEnregistrement("CodeArticle") = ""
                    dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)
                Next

                NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                NouvelArticle("Designation") = ""                   'juste une initialisation pour fair la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                NouvelArticle("CodeArticle") = ""
                NouvelArticle("CodeABarre") = ""
                dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)

                gArticles.Refresh()
                gArticles.Col = 8
            Else
                gArticles.Columns("CodeArticle").Value = ""
                gArticles.Col = 3
            End If

        Next

    End Sub
    'Public Sub CalculerMontants()

    '    Dim i As Integer = 0

    '    ValeurActuelleAchatTTC = 0.0
    '    ValeurActuelleVenteTTC = 0.0
    '    ValeurInitialeAchatTTC = 0.0
    '    ValeurInitialeVenteTTC = 0.0
    '    DifferenceAchatTTC = 0.0
    '    DifferenceVenteTTC = 0.0
    '    DifferenceAchatTTCPourcentage = 0.0
    '    DifferenceVenteTTCPourcentage = 0.0

    '    Dim MontantHTLigne As Double = 0.0
    '    Dim TVALigne As Double = 0.0
    '    Dim RemiseLigne As Double = 0.0
    '    Dim PrixAchatTTC As Double = 0.0
    '    Dim PrixVenteTTC As Double = 0.0

    '    'Dim TotalArticleCourant As Double = 0.0

    '    Do While i < gArticles.RowCount
    '        If gArticles(i, "Designation").ToString <> "" Then
    '            If IsDBNull(gArticles(i, "StockActuel")) = True Then
    '                gArticles(i, "StockActuel") = 0
    '            End If
    '            If IsDBNull(gArticles(i, "PrixAchatTTC")) = True Then
    '                gArticles(i, "PrixAchatTTC") = 0
    '            End If
    '            If IsDBNull(gArticles(i, "PrixVenteTTC")) = True Then
    '                gArticles(i, "PrixVenteTTC") = 0
    '            End If
    '            If IsDBNull(gArticles(i, "StockInitial")) = True Then
    '                gArticles(i, "StockInitial") = 0
    '            End If

    '            'If IsDBNull(RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))) = False Then
    '            'If Val(RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))) = 8 Then
    '            'gArticles(i, "TotalAchatTTC") = Math.Round((gArticles(i, "PrixAchatTTC") * Val(gArticles(i, "StockActuel")) / RecupererValeurExecuteScalaire("Quantiteunitaire", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))), 3)
    '            'Else
    '            gArticles(i, "TotalAchatTTC") = Math.Round((gArticles(i, "PrixAchatTTC") * gArticles(i, "StockActuel")), 3)
    '            'End If
    '            'End If

    '            'If Val(RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))) = 8 Then
    '            'PrixVenteTTC = (gArticles(i, "PrixVenteTTC") / RecupererValeurExecuteScalaire("Quantiteunitaire", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle")))
    '            'Else
    '            PrixVenteTTC = CDbl(gArticles(i, "PrixVenteTTC"))
    '            'End If

    '            'If RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle")) = 8 Then
    '            'PrixAchatTTC = (gArticles(i, "PrixAchatTTC") / RecupererValeurExecuteScalaire("Quantiteunitaire", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle")))
    '            'Else
    '            PrixAchatTTC = CDbl(gArticles(i, "PrixAchatTTC"))
    '            'End If

    '            ValeurActuelleAchatTTC = ValeurActuelleAchatTTC + gArticles(i, "TotalAchatTTC")
    '            ValeurActuelleVenteTTC = ValeurActuelleVenteTTC + Math.Round((PrixVenteTTC * gArticles(i, "StockActuel")), 3)

    '            ValeurInitialeAchatTTC = ValeurInitialeAchatTTC + Math.Round((PrixAchatTTC * gArticles(i, "StockInitial")), 3)
    '            ValeurInitialeVenteTTC = ValeurInitialeVenteTTC + Math.Round((PrixVenteTTC * gArticles(i, "StockInitial")), 3)

    '        End If
    '        i = i + 1
    '    Loop

    '    lActuelleAchatTTC.Text = CStr(Math.Round(ValeurActuelleAchatTTC, 3))
    '    lActuelleVenteTTC.Text = CStr(Math.Round(ValeurActuelleVenteTTC, 3))
    '    lInitialeAchatTTC.Text = CStr(Math.Round(ValeurInitialeAchatTTC, 3))
    '    lInitialeVenteTTC.Text = CStr(Math.Round(ValeurInitialeVenteTTC, 3))

    '    lDifferenceAchatTTC.Text = CStr(Math.Round(ValeurActuelleAchatTTC - ValeurInitialeAchatTTC, 3))
    '    lDifferenceVenteTTC.Text = CStr(Math.Round(ValeurActuelleVenteTTC - ValeurInitialeVenteTTC, 3))

    '    lDifferenceAchatTTCPourcentage.Text = CStr(Math.Round((100 / ValeurActuelleAchatTTC) * CDbl(lDifferenceAchatTTC.Text), 3))
    '    lDifferenceVenteTTCPourcentage.Text = CStr(Math.Round((100 / ValeurActuelleVenteTTC) * CDbl(lDifferenceVenteTTC.Text), 3))

    '    If lDifferenceAchatTTCPourcentage.Text = "Non Numérique" Then
    '        lDifferenceAchatTTCPourcentage.Text = "0"
    '    End If


    '    If lDifferenceVenteTTCPourcentage.Text = "Non Numérique" Then
    '        lDifferenceVenteTTCPourcentage.Text = "0"
    '    End If

    '    lDifferenceAchatTTCPourcentage.Text = lDifferenceAchatTTCPourcentage.Text + " %"
    '    lDifferenceVenteTTCPourcentage.Text = lDifferenceVenteTTCPourcentage.Text + " %"

    '    lNombreDesArticles.Text = "Nombre des articles : " + (gArticles.RowCount - 1).ToString

    'End Sub

    Public Sub CalculerMontants()

        ValeurActuelleAchatTTC = 0.0
        ValeurActuelleVenteTTC = 0.0
        ValeurInitialeAchatTTC = 0.0
        ValeurInitialeVenteTTC = 0.0
        DifferenceAchatTTC = 0.0
        DifferenceVenteTTC = 0.0
        DifferenceAchatTTCPourcentage = 0.0
        DifferenceVenteTTCPourcentage = 0.0

        Dim MontantHTLigne As Double = 0.0
        Dim TVALigne As Double = 0.0
        Dim RemiseLigne As Double = 0.0
        Dim PrixAchatTTC As Double = 0.0
        Dim PrixVenteTTC As Double = 0.0
        Dim QteUnitaire As Integer = 0
        'Dim TotalArticleCourant As Double = 0.0

        For i As Integer = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1
            If dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("Designation").ToString <> "" Then 'gArticles(i, "Designation").ToString <> "" Then
                'If IsDBNull(gArticles(i, "StockActuel")) = True Then
                If IsDBNull(dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("StockActuel")) = True Then
                    dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("StockActuel") = 0
                    'gArticles(i, "StockActuel") = 0
                End If
                'If IsDBNull(gArticles(i, "PrixAchatTTC")) = True Then
                '    gArticles(i, "PrixAchatTTC") = 0
                If IsDBNull(dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("PrixAchatTTC")) = True Then
                    dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("PrixAchatTTC") = 0
                End If
                'If IsDBNull(gArticles(i, "PrixVenteTTC")) = True Then
                '    gArticles(i, "PrixVenteTTC") = 0
                'End If
                If IsDBNull(dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("PrixVenteTTC")) = True Then
                    dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("PrixVenteTTC") = 0
                End If
                'If IsDBNull(gArticles(i, "StockInitial")) = True Then
                '    gArticles(i, "StockInitial") = 0
                'End If
                If IsDBNull(dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("StockInitial")) = True Then
                    dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("StockInitial") = 0
                End If

                'If IsDBNull(RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))) = False Then
                'If Val(RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))) = 8 Then
                'gArticles(i, "TotalAchatTTC") = Math.Round((gArticles(i, "PrixAchatTTC") * Val(gArticles(i, "StockActuel")) / RecupererValeurExecuteScalaire("Quantiteunitaire", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))), 3)
                'Else

                'gArticles(i, "TotalAchatTTC") = Math.Round((gArticles(i, "PrixAchatTTC") * gArticles(i, "StockActuel")), 3)

                If gArticles(i, "CodeArticle") <> "" Then
                    If dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("StockActuel") <> 0 Then
                        dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("TotalAchatTTC") = Math.Round((dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("PrixAchatTTC") * dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("StockActuel") / RecupererValeurExecuteScalaire("Quantiteunitaire", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))), 3)
                    Else
                        dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("TotalAchatTTC") = 0
                    End If
                End If

                'End If
                'End If

                'If Val(RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle"))) = 8 Then
                'PrixVenteTTC = (gArticles(i, "PrixVenteTTC") / RecupererValeurExecuteScalaire("Quantiteunitaire", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle")))
                'Else

                'PrixVenteTTC = CDbl(gArticles(i, "PrixVenteTTC"))
                PrixVenteTTC = CDbl(dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("PrixVenteTTC"))

                'End If

                'If RecupererValeurExecuteScalaire("CodeCategorie", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle")) = 8 Then
                'PrixAchatTTC = (gArticles(i, "PrixAchatTTC") / RecupererValeurExecuteScalaire("Quantiteunitaire", "ARTICLE", "CodeArticle", gArticles(i, "CodeArticle")))
                'Else


                'PrixAchatTTC = CDbl(gArticles(i, "PrixAchatTTC"))
                PrixAchatTTC = CDbl(dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("PrixAchatTTC"))


                'End If

                'ValeurActuelleAchatTTC = ValeurActuelleAchatTTC + gArticles(i, "TotalAchatTTC")
                'ValeurActuelleVenteTTC = ValeurActuelleVenteTTC + Math.Round((PrixVenteTTC * gArticles(i, "StockActuel")), 3)
                'ValeurInitialeAchatTTC = ValeurInitialeAchatTTC + Math.Round((PrixAchatTTC * gArticles(i, "StockInitial")), 3)
                'ValeurInitialeVenteTTC = ValeurInitialeVenteTTC + Math.Round((PrixVenteTTC * gArticles(i, "StockInitial")), 3)

                If dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("CodeArticle") <> "" Then
                    QteUnitaire = RecupererValeurExecuteScalaire("QuantiteUnitaire", "ARTICLE", "CodeArticle", dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("CodeArticle"))
                    ValeurActuelleAchatTTC = ValeurActuelleAchatTTC + dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("TotalAchatTTC")
                    ValeurActuelleVenteTTC = ValeurActuelleVenteTTC + Math.Round((PrixVenteTTC * dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("StockActuel")), 3)
                    ValeurInitialeAchatTTC = ValeurInitialeAchatTTC + Math.Round((PrixAchatTTC * dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("StockInitial") / QteUnitaire), 3)
                    ValeurInitialeVenteTTC = ValeurInitialeVenteTTC + Math.Round((PrixVenteTTC * dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("StockInitial") / QteUnitaire), 3)
                End If
            End If
        Next

        lActuelleAchatTTC.Text = CStr(Math.Round(ValeurActuelleAchatTTC, 3))
        lActuelleVenteTTC.Text = CStr(Math.Round(ValeurActuelleVenteTTC, 3))
        lInitialeAchatTTC.Text = CStr(Math.Round(ValeurInitialeAchatTTC, 3))
        lInitialeVenteTTC.Text = CStr(Math.Round(ValeurInitialeVenteTTC, 3))

        lDifferenceAchatTTC.Text = CStr(Math.Round(ValeurActuelleAchatTTC - ValeurInitialeAchatTTC, 3))
        lDifferenceVenteTTC.Text = CStr(Math.Round(ValeurActuelleVenteTTC - ValeurInitialeVenteTTC, 3))

        lDifferenceAchatTTCPourcentage.Text = CStr(Math.Round((100 / ValeurActuelleAchatTTC) * CDbl(lDifferenceAchatTTC.Text), 3))
        lDifferenceVenteTTCPourcentage.Text = CStr(Math.Round((100 / ValeurActuelleVenteTTC) * CDbl(lDifferenceVenteTTC.Text), 3))

        If lDifferenceAchatTTCPourcentage.Text = "Non Numérique" Then
            lDifferenceAchatTTCPourcentage.Text = "0"
        End If


        If lDifferenceVenteTTCPourcentage.Text = "Non Numérique" Then
            lDifferenceVenteTTCPourcentage.Text = "0"
        End If

        lDifferenceAchatTTCPourcentage.Text = lDifferenceAchatTTCPourcentage.Text + " %"
        lDifferenceVenteTTCPourcentage.Text = lDifferenceVenteTTCPourcentage.Text + " %"

        lNombreDesArticles.Text = "Nombre des articles : " + (gArticles.RowCount).ToString

    End Sub


    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click

        Dim I As Integer = 0
        Dim J As Integer = 0
        Dim cmd As New SqlCommand
        Dim NumeroLot As Integer = 0
        Dim TestNumeroLot As String = ""
        Dim NouveauNumeroLot As String = ""
        Dim QuantiteLotSansNumero As Integer = 0
        Dim QuantiteLotAInrsere As Integer = 0
        Dim StrSQL As String = ""

        NumeroInventaire = CStr(RecupereNumuero())

        'Affcihre message alerte 
        'reponse = MsgBox("Voulez-vous valider l'inventaire N° " & NumeroInventaire & ", l'opération peut prendre quelques instants ", MessageBoxIcon.Question + MessageBoxButtons.YesNoCancel, "Valider Inventaire")

        'If reponse = vbNo Then
        'Exit Sub
        'End If
        If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1 <= 0 And gArticles(0, "CodeArticle").ToString = "" Then
            MsgBox("Inventaire Vide !", MsgBoxStyle.Critical, "Erreur")
            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1 < 0 Then
                bAjouter_Click(sender, e)
            End If
            '----------------------
            gArticles.Col = 2
            gArticles.EditActive = True
            Exit Sub
        End If
        '------------------------------ demande du mot de passe
        Dim myMotDePasse As New fMotDePasse
        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If

        '------------------------------ enregistrement de l'entête de l'inventaire -------------------------
        '-----------------------------------------------------------------------------------------------
        '-----------------------------------------------------------------------------------------------

        If (dsInventaire.Tables.IndexOf("INVENTAIRE") > -1) Then
            dsInventaire.Tables("INVENTAIRE").Clear()
        End If

        StrSQL = "SELECT top(0) * FROM INVENTAIRE ORDER BY NumeroInventaire ASC"
        cmdInventaire.Connection = ConnectionServeur
        cmdInventaire.CommandText = StrSQL
        daInventaire = New SqlDataAdapter(cmdInventaire)
        daInventaire.Fill(dsInventaire, "INVENTAIRE")
        cbInventaire = New SqlCommandBuilder(daInventaire)
        dr = dsInventaire.Tables("INVENTAIRE").NewRow()

        With dsInventaire

            dr.Item("NumeroInventaire") = NumeroInventaire
            dr.Item("Date") = System.DateTime.Now
            dr.Item("ValeurAchatInitial") = lInitialeAchatTTC.Text
            dr.Item("ValeurVenteInitial") = lInitialeVenteTTC.Text
            dr.Item("ValeurAchatActuelle") = lActuelleAchatTTC.Text
            dr.Item("ValeurVenteActuelle") = lActuelleVenteTTC.Text
            dr.Item("ValeurAchatDifference") = lDifferenceAchatTTC.Text
            dr.Item("ValeurVenteDifference") = lDifferenceVenteTTC.Text
            dr.Item("Remarque") = tRemarqueInventaire.Text
            dr.Item("CodePersonnel") = CodeOperateur

            dsInventaire.Tables("INVENTAIRE").Rows.Add(dr)
        End With
        Try
            daInventaire.Update(dsInventaire, "INVENTAIRE")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsInventaire.Reset()
        End Try

        '------------------------------ contrôle de l'existance des lots : pour les articles non mouvementé
        '------------------------------ on crée des lots avec des numero vides et date de péremption null
        '------------------------------ et pour les anciens lot on corrige les quantités si différentes

        For I = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
            Try
                If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("CodeArticle").ToString = "" Then
                    dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Delete()
                    I = 0
                End If
            Catch ex As Exception
            End Try

        Next

        For I = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1

            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).RowState <> DataRowState.Deleted And dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("CodeArticle") <> "" Then

                If (dsInventaire.Tables.IndexOf("TEST_EXISTANCE_LOT") > -1) Then
                    dsInventaire.Tables("TEST_EXISTANCE_LOT").Clear()
                End If

                StrSQL = "SELECT * FROM LOT_ARTICLE WHERE NumeroLotArticle='" + _
                         dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("NumeroLot") + _
                         "' AND CodeArticle='" + _
                         dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("CodeArticle") + "'"

                cmdInventaire.Connection = ConnectionServeur
                cmdInventaire.CommandText = StrSQL
                daInventaire = New SqlDataAdapter(cmdInventaire)
                daInventaire.Fill(dsInventaire, "TEST_EXISTANCE_LOT")
                '-------------------------- si le lot n'existe pas
                If dsInventaire.Tables("TEST_EXISTANCE_LOT").Rows.Count = 0 Then

                    StrSQL = "INSERT INTO LOT_ARTICLE " + _
                        "(""NumeroLotArticle"",""CodeArticle"",""QteLotArticle"",""DatePeremptionArticle"") " + _
                        " VALUES('" + _
                        dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("NumeroLot") + "','" + _
                        dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("CodeArticle") + _
                        "'," + dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("StockActuel").ToString + _
                        ",NULL)"
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL

                    Try
                        cmd.ExecuteNonQuery()
                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                Else  '----- si il ya une modification des quantités : correction dans la table lot_article
                    If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("StockActuel") <> dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("StockInitial") Then

                        If Trim(dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("NumeroLot")) = "" Then
                            StrSQL = "Update LOT_ARTICLE set QteLotArticle='" + _
                                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("StockActuel").ToString + _
                                "' ,DatePeremptionArticle = NULL " + _
                                " WHERE NumeroLotArticle ='" + _
                                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("NumeroLot") + _
                                "' AND CodeArticle ='" + _
                                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("CodeArticle") + "'"
                        Else
                            StrSQL = "Update LOT_ARTICLE set QteLotArticle='" + _
                                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("StockActuel").ToString + _
                                "' WHERE NumeroLotArticle ='" + _
                                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("NumeroLot") + _
                                "' AND CodeArticle ='" + _
                                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("CodeArticle") + "'"

                        End If

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL

                        Try
                            cmd.ExecuteNonQuery()
                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try
                    End If
                End If

            End If
        Next

        '------------------------------ enregistrement des détails de l'inventaire -------------------------
        '-----------------------------------------------------------------------------------------------
        '-----------------------------------------------------------------------------------------------

        cmdInventaire.Connection = ConnectionServeur
        cmdInventaire.CommandText = "SELECT TOP(0) * FROM INVENTAIRE_DETAILS"
        daInventaire = New SqlDataAdapter(cmdInventaire)
        daInventaire.Fill(dsInventaire, "INVENTAIRE_DETAILS")
        cbInventaire = New SqlCommandBuilder(daInventaire)

        '-------------------------- élémination des lignes vides 
        I = 0

        Do While I < dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count
            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("CodeArticle").ToString = "" Then
                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Delete()
            End If
            I = I + 1
        Loop


        '--------------------------élémination des lignes non changées en cas dinventaire partiel
        If fCritereDeRechercheInventaire.TypeInventaire = "Partiel" Then
            I = 0
            J = 0
            Do While I < gArticles.RowCount
                If gArticles(I, "QteChange") = "0" Then

                    'gArticle est liée a la DS INVENTAIRE_DETAILS_AFFICHE
                    'qui est partitionnée en lot
                    'donc on va uiliser un boucle pour supprimer les rows de la DS INVENTAIRE_DETAILS
                    'qui correspond au articles non changés

                    J = 0
                    Do While J < dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count

                        If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(J).Item("CodeArticle") = gArticles(I, "CodeArticle") Then

                            dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(J).Delete()
                            J = J - 1
                        End If
                        J = J + 1
                    Loop

                End If
                I = I + 1
            Loop

        End If


        For I = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
            dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("TotalAchatTTC") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("PrixAchatTTC") * dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(I).Item("StockActuel")
        Next


        Try
            daInventaire.Update(dsInventaire, "INVENTAIRE_DETAILS")


            cmdInventaire.Connection = ConnectionServeur
            StrSQL = " UPDATE INVENTAIRE SET "
            StrSQL += " ValeurAchatInitial = (SELECT SUM(StockInitial * PrixAchatTTC) FROM INVENTAIRE_DETAILS WHERE numeroInventaire = " + Quote(NumeroInventaire) + ")"
            StrSQL += " ,ValeurVenteInitial = (SELECT SUM(StockInitial * PrixVenteTTC) FROM INVENTAIRE_DETAILS WHERE numeroInventaire = " + Quote(NumeroInventaire) + ")"
            StrSQL += " ,ValeurAchatActuelle = (SELECT SUM(StockActuel * PrixAchatTTC) FROM INVENTAIRE_DETAILS WHERE numeroInventaire = " + Quote(NumeroInventaire) + ")"
            StrSQL += " ,ValeurVenteActuelle = (SELECT SUM(StockActuel * PrixVenteTTC) FROM INVENTAIRE_DETAILS WHERE numeroInventaire = " + Quote(NumeroInventaire) + ")"
            StrSQL += " ,ValeurAchatDifference = ((SELECT SUM(StockActuel * PrixAchatTTC) FROM INVENTAIRE_DETAILS WHERE numeroInventaire = " + Quote(NumeroInventaire) + ")"
            StrSQL += " - (SELECT SUM(StockInitial * PrixAchatTTC) FROM INVENTAIRE_DETAILS WHERE numeroInventaire = " + Quote(NumeroInventaire) + "))"
            StrSQL += " ,ValeurVenteDifference = ((SELECT SUM(StockActuel * PrixVenteTTC) FROM INVENTAIRE_DETAILS WHERE numeroInventaire = " + Quote(NumeroInventaire) + ")"
            StrSQL += " - (SELECT SUM(StockInitial * PrixVenteTTC) FROM INVENTAIRE_DETAILS WHERE numeroInventaire = " + Quote(NumeroInventaire) + "))"
            StrSQL += " WHERE numeroInventaire = " + Quote(NumeroInventaire) + ""
            cmdInventaire.CommandText = StrSQL
            Try
                cmdInventaire.ExecuteNonQuery()
            Catch ex As Exception
                MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
            End Try


            GroupeJauge.Visible = True
            lArticle.Visible = False
            lArticleEnCours.Visible = False
            ProgressBar.Visible = False
            lTitreLabelle.Text = "L'opération en cours veuillez patienter ..."

            Application.DoEvents()

            'Valider l'invenatire et re-calculer le stock
            validerInvenaire()

            GroupeJauge.Visible = False
            lArticle.Visible = True
            lArticleEnCours.Visible = True
            ProgressBar.Visible = True
            lTitreLabelle.Text = "Analyse en cours"

        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsInventaire.Reset()
        End Try

        Init()
        mode = "Consultation"

        bAnnuler.Enabled = False
        bConfirmer.Enabled = False

        bFirst.Visible = True
        bPrevious.Visible = True
        bNext.Visible = True
        bLast.Visible = True
        bAjouter.Enabled = True

    End Sub

    Private Sub validerInvenaire()

        'Changer le status de l'inventaire en Valide
        'Message de confirmation       
        Dim cmd As New SqlCommand
        Dim StrSQL As String

        'Changer le status de l'inventaire en Valide
        Try
            StrSQL = "Update INVENTAIRE  set Valide = 1 WHERE NumeroInventaire = " & Quote(NumeroInventaire)

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL

            cmd.ExecuteNonQuery()

            'Bloquer les BTNs

        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        'Excecuter la Procedure 
        RecalculeDeStockDernierInventaire(NumeroInventaire)


    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Init()
        mode = "Consultation"

        bAnnuler.Enabled = False
        bConfirmer.Enabled = False

        gListeRecherche.Visible = False

        PanelAfficherLots.Visible = False
        gListeLotArticle.Visible = False
        bOkLot.Visible = False
        bAnnulerLot.Visible = False
        bAjouterLot.Visible = False

        bFirst.Visible = True
        bPrevious.Visible = True
        bNext.Visible = True
        bLast.Visible = True
        bAjouter.Enabled = True

        bAfficherLot.Enabled = False

    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim CondCrystal As String = ""
        CondCrystal = "1=1 AND {INVENTAIRE.NumeroInventaire} = '" + lNumeroInventaire.Text + "'"
        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression d'inventaire" Then
                'fMain.Tab.TabPages(I).Show()
                'Exit Sub
                num = I
            End If
        Next
        CR.FileName = Application.StartupPath + "\EtatInventaire.rpt"

        CR.SetParameterValue("NouvelleValeurVente", lActuelleVenteTTC.Text)
        CR.SetParameterValue("AncienneValeurVente", lInitialeVenteTTC.Text)
        CR.SetParameterValue("NouvelleValeurAchat", lActuelleAchatTTC.Text)
        CR.SetParameterValue("AncienneValeurAchat", lInitialeAchatTTC.Text)

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent
        CR.RecordSelectionFormula = CondCrystal

        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression d'inventaire"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If

    End Sub

    Private Sub bLast_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bLast.Click
        Dim j As Integer
        Dim DataRowRecherche As DataRow
        Dim NumeroLigne As Integer
        For j = 0 To dsChargement.Tables("INVENTAIRE").Rows.Count - 1
            DataRowRecherche = dsChargement.Tables("INVENTAIRE").Rows(j)
            If DataRowRecherche.Item("NumeroInventaire").ToString = NumeroInventaire Then
                NumeroLigne = j
            End If
        Next

        If dsChargement.Tables("INVENTAIRE").Rows.Count - 1 > 0 Then
            DataRowRecherche = dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)

            'chargement des informations entête
            lNumeroInventaire.Text = dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("NumeroInventaire").ToString
            lDateInventaire.Text = dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("Date").ToString

            lActuelleAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("ValeurAchatActuelle").ToString
            lActuelleVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("ValeurVenteActuelle").ToString

            lInitialeAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("ValeurAchatInitial").ToString
            lInitialeVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("ValeurVenteInitial").ToString

            lDifferenceAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("ValeurAchatDifference").ToString
            lDifferenceVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("ValeurVenteDifference").ToString

            lOperateur.Text = RecupererValeurExecuteScalaire("NomOperateur", "OPERATEUR", "CodeOperateur", dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("CodePersonnel")).ToString

            tRemarqueInventaire.Value = dsChargement.Tables("INVENTAIRE").Rows(dsChargement.Tables("INVENTAIRE").Rows.Count - 1)("Remarque")

            NumeroInventaire = DataRowRecherche.Item("NumeroInventaire").ToString

            ChargerDetails()

        End If
    End Sub
    Private Sub ChargerDetails()
        Dim StrSQL As String
        Dim I As Integer

        dsChargement.Tables("INVENTAIRE_DETAILS_AFFICHE").Clear()

        'chargement des détails des Achats 

        StrSQL = "SELECT NumeroInventaire," + _
                  "INVENTAIRE_DETAILS.CodeArticle," + _
                  "MAX(INVENTAIRE_DETAILS.CodeABarre) AS CodeABarre," + _
                  "MAX(INVENTAIRE_DETAILS.Designation) AS Designation," + _
                  "MAX(LibelleForme) AS LibelleForme," + _
                  "MAX(INVENTAIRE_DETAILS.CodeForme) AS CodeForme," + _
                  "MAX(INVENTAIRE_DETAILS.Rayon) AS Rayon," + _
                  "SUM(INVENTAIRE_DETAILS.StockInitial) as StockInitial," + _
                  "SUM(StockActuel) as StockActuel," + _
                  "MAX(INVENTAIRE_DETAILS.PrixAchatTTC) AS PrixAchatTTC," + _
                  "SUM(TotalAchatTTC) AS TotalAchatTTC," + _
                  "MAX(INVENTAIRE_DETAILS.PrixVenteTTC) AS PrixVenteTTC, " + _
                  "MAX(ARTICLE.QuantiteUnitaire) AS QuantiteUnitaire, " + _
                  "FROM " + _
                  "INVENTAIRE_DETAILS " + _
                  "LEFT OUTER JOIN ARTICLE ON INVENTAIRE_DETAILS.CodeArticle=ARTICLE.CodeArticle " + _
                  "LEFT OUTER JOIN FORME_ARTICLE ON INVENTAIRE_DETAILS.CodeForme=FORME_ARTICLE.CodeForme " + _
                  "WHERE NumeroInventaire =" + Quote(NumeroInventaire) + " " + _
                  "GROUP BY NumeroInventaire,INVENTAIRE_DETAILS.CodeArticle" + _
                  " order by Designation"


        '"GROUP BY NumeroInventaire,INVENTAIRE_DETAILS.CodeArticle,INVENTAIRE_DETAILS.CodeABarre" + _
        '          ",INVENTAIRE_DETAILS.Designation,LibelleForme,INVENTAIRE_DETAILS.CodeForme,INVENTAIRE_DETAILS.Rayon " + _
        '          ",INVENTAIRE_DETAILS.PrixAchatTTC,TotalAchatTTC,INVENTAIRE_DETAILS.PrixVenteTTC " + _
        '          " order by Designation"


        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "INVENTAIRE_DETAILS_AFFICHE")

        With gArticles
            .Columns.Clear()
            Try
                .DataSource = dsChargement
            Catch ex As Exception
            End Try
            .DataMember = "INVENTAIRE_DETAILS_AFFICHE"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Rayon").Caption = "Rayon"
            '.Columns("NumeroLot").Caption = "Numero Lot"
            .Columns("StockInitial").Caption = "Stock Initial"
            .Columns("StockActuel").Caption = "nouveau Stock"
            .Columns("PrixAchatTTC").Caption = "Prix achat TTC"
            .Columns("TotalAchatTTC").Caption = "Total Achat TTC"

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns(2).Style.HorizontalAlignment = AlignHorzEnum.Near
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("PrixAchatTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalAchatTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far

            .Splits(0).DisplayColumns("NumeroInventaire").Width = 0
            .Splits(0).DisplayColumns("NumeroInventaire").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("QuantiteUnitaire").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 100
            .Splits(0).DisplayColumns("Designation").Width = 320

            .Splits(0).DisplayColumns("LibelleForme").Width = 80
            .Splits(0).DisplayColumns("CodeForme").Width = 0
            .Splits(0).DisplayColumns("Rayon").Width = 80
            .Splits(0).DisplayColumns("StockInitial").Width = 80
            .Splits(0).DisplayColumns("StockActuel").Width = 80
            .Splits(0).DisplayColumns("PrixAchatTTC").Width = 80
            .Splits(0).DisplayColumns("TotalAchatTTC").Width = 80
            .Splits(0).DisplayColumns("PrixVenteTTC").Width = 0
            .Splits(0).DisplayColumns("PrixVenteTTC").Visible = False
            .Splits(0).DisplayColumns("CodeForme").Visible = False

            .Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
            .Splits(0).DisplayColumns("StockActuel").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = True
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

        End With
        lNombreDesArticles.Text = "Nombre des articles : " + (gArticles.RowCount).ToString
    End Sub

    Private Sub bFirst_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bFirst.Click
        Dim j As Integer
        Dim DataRowRecherche As DataRow
        Dim NumeroLigne As Integer
        For j = 0 To dsChargement.Tables("INVENTAIRE").Rows.Count - 1
            DataRowRecherche = dsChargement.Tables("INVENTAIRE").Rows(j)
            If DataRowRecherche.Item("NumeroInventaire").ToString = NumeroInventaire Then
                NumeroLigne = j
            End If
        Next

        If dsChargement.Tables("INVENTAIRE").Rows.Count - 1 > 0 Then
            DataRowRecherche = dsChargement.Tables("INVENTAIRE").Rows(0)
            'chargement des informations entête
            lNumeroInventaire.Text = dsChargement.Tables("INVENTAIRE").Rows(0)("NumeroInventaire").ToString
            lDateInventaire.Text = dsChargement.Tables("INVENTAIRE").Rows(0)("Date").ToString

            lActuelleAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(0)("ValeurAchatActuelle").ToString
            lActuelleVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(0)("ValeurVenteActuelle").ToString

            lInitialeAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(0)("ValeurAchatInitial").ToString
            lInitialeVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(0)("ValeurVenteInitial").ToString

            lDifferenceAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(0)("ValeurAchatDifference").ToString
            lDifferenceVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(0)("ValeurVenteDifference").ToString

            lOperateur.Text = RecupererValeurExecuteScalaire("NomOperateur", "OPERATEUR", "CodeOperateur", dsChargement.Tables("INVENTAIRE").Rows(0)("CodePersonnel")).ToString

            tRemarqueInventaire.Value = dsChargement.Tables("INVENTAIRE").Rows(0)("Remarque")

            NumeroInventaire = DataRowRecherche.Item("NumeroInventaire").ToString

            ChargerDetails()

        End If
    End Sub

    Private Sub bNext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bNext.Click
        Dim j As Integer
        Dim DataRowRecherche As DataRow
        Dim NumeroLigne As Integer
        For j = 0 To dsChargement.Tables("INVENTAIRE").Rows.Count - 1
            DataRowRecherche = dsChargement.Tables("INVENTAIRE").Rows(j)
            If DataRowRecherche.Item("NumeroInventaire").ToString = NumeroInventaire Then
                NumeroLigne = j
            End If
        Next

        If NumeroLigne + 1 <= dsChargement.Tables("INVENTAIRE").Rows.Count - 1 Then
            DataRowRecherche = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne + 1)
            'chargement des informations entête
            lNumeroInventaire.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne + 1)("NumeroInventaire").ToString
            lDateInventaire.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne + 1)("Date").ToString

            lActuelleAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne + 1)("ValeurAchatActuelle").ToString
            lActuelleVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne + 1)("ValeurVenteActuelle").ToString

            lInitialeAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne + 1)("ValeurAchatInitial").ToString
            lInitialeVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne + 1)("ValeurVenteInitial").ToString

            lDifferenceAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne + 1)("ValeurAchatDifference").ToString
            lDifferenceVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne + 1)("ValeurVenteDifference").ToString

            lOperateur.Text = RecupererValeurExecuteScalaire("NomOperateur", "OPERATEUR", "CodeOperateur", dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne + 1)("CodePersonnel")).ToString
            tRemarqueInventaire.Value = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne + 1)("Remarque")

            NumeroInventaire = DataRowRecherche.Item("NumeroInventaire").ToString
            ChargerDetails()

        End If
    End Sub

    Private Sub bPrevious_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bPrevious.Click
        Dim j As Integer
        Dim DataRowRecherche As DataRow
        Dim NumeroLigne As Integer
        For j = 0 To dsChargement.Tables("INVENTAIRE").Rows.Count - 1
            DataRowRecherche = dsChargement.Tables("INVENTAIRE").Rows(j)
            If DataRowRecherche.Item("NumeroInventaire").ToString = NumeroInventaire Then
                NumeroLigne = j
            End If
        Next

        If NumeroLigne - 1 >= 0 Then
            DataRowRecherche = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne - 1)
            'chargement des informations entête
            lNumeroInventaire.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne - 1)("NumeroInventaire").ToString
            lDateInventaire.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne - 1)("Date").ToString

            lActuelleAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne - 1)("ValeurAchatActuelle").ToString
            lActuelleVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne - 1)("ValeurVenteActuelle").ToString

            lInitialeAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne - 1)("ValeurAchatInitial").ToString
            lInitialeVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne - 1)("ValeurVenteInitial").ToString

            lDifferenceAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne - 1)("ValeurAchatDifference").ToString
            lDifferenceVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne - 1)("ValeurVenteDifference").ToString

            lOperateur.Text = RecupererValeurExecuteScalaire("NomOperateur", "OPERATEUR", "CodeOperateur", dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne - 1)("CodePersonnel")).ToString

            NumeroInventaire = DataRowRecherche.Item("NumeroInventaire").ToString

            tRemarqueInventaire.Value = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne - 1)("Remarque")

            ChargerDetails()

        End If

    End Sub


    Public Function RecupereNumueroInstance()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max(NumeroAchatInstance) FROM ACHAT_INSTANCE"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = CStr(cmdRecupereNum.ExecuteScalar())
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero séquenciel de l année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If
        Return ValeurRetour
    End Function

    Private Sub gArticles_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gArticles.Click
        If PanelAfficherLots.Visible = True Then
            bAnnulerLot_Click(sender, e)
        End If
    End Sub

    Private Sub gArticles_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles gArticles.DoubleClick
    End Sub
    Private Sub AfficherLotArticle(ByVal CodeArticle)
        'If gArticles.Columns("CodeArticle").Value = "" Then
        '    Exit Sub
        'End If

        Dim StrSQL As String = ""
        Dim i As Integer = 0
        Dim SommeDesQte As Integer = 0
        Dim DLigneLot As DataRow = Nothing

        If (dsInventaire.Tables.IndexOf("LOT_ARTICLE_DISPONIBLE") > -1) Then
            dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Clear()
        End If

        With PanelAfficherLots
            .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(1).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width + Me.gArticles.Splits(0).DisplayColumns(3).Width
            .Top = Me.gArticles.Top + Me.gArticles.Splits(0).ColumnFooterHeight + Me.gArticles.RowTop(Me.gArticles.Row)
        End With

        'With gListeLotArticle
        '    .Left = Me.gArticles.Left + Me.gArticles.Splits(0).DisplayColumns(0).Width + Me.gArticles.Splits(0).DisplayColumns(1).Width + Me.gArticles.Splits(0).DisplayColumns(2).Width
        '    .Top = Me.gArticles.Top + Me.gArticles.Splits(0).ColumnFooterHeight '+ Me.gArticles.RowTop(Me.gArticles.Row) 
        'End With

        'chargement des détails des inventaires 

        '*******************************************************************************

        StrSQL = "SELECT TOP(0)NumeroLotArticle," + _
                 "LOT_ARTICLE.CodeArticle," + _
                 "Designation," + _
                 "QteLotArticle," + _
                 "DatePeremptionArticle" + _
                 " FROM LOT_ARTICLE LEFT OUTER JOIN ARTICLE ON LOT_ARTICLE.CodeArticle=ARTICLE.CodeArticle WHERE " + _
                 " LOT_ARTICLE.CodeArticle='" + _
                CodeArticle + "'"

        cmdInventaire.Connection = ConnectionServeur
        cmdInventaire.CommandText = StrSQL
        daInventaire.Fill(dsInventaire, "LOT_ARTICLE_DISPONIBLE")
        cbInventaire = New SqlCommandBuilder(daInventaire)

        For i = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle").ToString = CodeArticle Then
                DLigneLot = dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").NewRow()

                DLigneLot("NumeroLotArticle") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroLot")
                DLigneLot("CodeArticle") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle")
                DLigneLot("Designation") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("Designation")
                DLigneLot("QteLotArticle") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel")
                If RecupererDatePeremption(DLigneLot("CodeArticle"), DLigneLot("NumeroLotArticle")).ToString <> "" Then
                    DLigneLot("DatePeremptionArticle") = RecupererDatePeremption(DLigneLot("CodeArticle"), DLigneLot("NumeroLotArticle"))
                End If

                dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows.Add(DLigneLot)
            End If

        Next

        With gListeLotArticle
            .Columns.Clear()
            .DataSource = dsInventaire
            .DataMember = "LOT_ARTICLE_DISPONIBLE"
            .Rebind(False)
            .Columns("NumeroLotArticle").Caption = "Numero Lot"
            .Columns("Designation").Caption = "Désignation"
            .Columns("QteLotArticle").Caption = "Qte"
            .Columns("DatePeremptionArticle").Caption = "Date de péremption"

            ' Centrer toutes les entêtes
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            For i = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(i).Locked = True
            Next

            .Splits(0).DisplayColumns("QteLotArticle").Locked = False

            .Splits(0).DisplayColumns("NumeroLotArticle").Width = 80
            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("Designation").Width = 280
            .Splits(0).DisplayColumns("QteLotArticle").Width = 80
            .Splits(0).DisplayColumns("DatePeremptionArticle").Width = 80

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
        End With

        Me.gListeLotArticle.Splits(0).DisplayColumns(0).AllowFocus = False
        Me.gListeLotArticle.Splits(0).DisplayColumns(1).AllowFocus = False
        Me.gListeLotArticle.Splits(0).DisplayColumns(2).AllowFocus = False
        Me.gListeLotArticle.Splits(0).DisplayColumns(4).AllowFocus = False

        For i = 0 To gListeLotArticle.RowCount - 1
            SommeDesQte += CInt(gListeLotArticle(i, "QteLotArticle"))
        Next

        lSommeQte.Text = "Somme des Qtes : " + SommeDesQte.ToString

        If dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows.Count = 0 Then
            PanelAfficherLots.Visible = False
            gListeLotArticle.Visible = False
        Else
            PanelAfficherLots.Visible = True
            gListeLotArticle.Visible = True
            bOkLot.Visible = True
            bAnnulerLot.Visible = True
            bAjouterLot.Visible = True
            lSommeQte.Visible = True
            gListeLotArticle.Focus()
            gListeLotArticle.Col = 4
        End If

    End Sub

    Public Function RecupererDatePeremption(ByVal CodeArticle, ByVal NumeroLot)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        Try
            Quote(CStr(CodeArticle))
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT DatePeremptionArticle FROM LOT_ARTICLE WHERE CodeArticle =" + Quote(CodeArticle) + " AND NumeroLotArticle=" + Quote(NumeroLot)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function
    Private Sub ChangerLotsArticle(ByVal CodeArticle, ByVal Qte)
        For i = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
            'If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") = CodeArticle And Trim(dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroLot").ToString) <> "" Then
            '    dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel") = 0
            'End If
            If Trim(dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroLot").ToString) = "" And dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle").ToString = CodeArticle Then
                'dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel") = Qte
                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel") = (Qte - qteAvantModif) + dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel").ToString

            End If
        Next

        'CalculerMontants()

    End Sub

    Private Sub gArticles_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles gArticles.LostFocus

    End Sub


    Private Sub gListeLotArticle_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListeLotArticle.KeyDown

    End Sub

    Private Sub gListeLotArticle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gListeLotArticle.KeyUp
        '''''''''''''''''''''''
        Dim NumeroLot As String = ""
        Dim CodeArticle As String = ""
        Dim Qte As Integer = 0
        Dim i As Integer = 0
        Dim SommeQuantite As Integer = 0
        Dim SommeDesQte As Integer = 0

        If IsNumeric(gListeLotArticle.Columns("QteLotArticle").Value) = False Then
            Exit Sub
        End If

        'NumeroLot = gListeLotArticle.Columns("NumeroLotArticle").Value
        'CodeArticle = gListeLotArticle.Columns("CodeArticle").Value
        'If gListeLotArticle.Columns("QteLotArticle").Value.ToString <> "" Then
        '    Qte = gListeLotArticle.Columns("QteLotArticle").Value
        'End If


        'For i = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
        '    If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") <> "" Then
        '        If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroLot") = NumeroLot And dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") = CodeArticle Then
        '            dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel") = Qte
        '        End If
        '    End If

        'Next

        For i = 0 To gListeLotArticle.RowCount - 1
            SommeDesQte += gListeLotArticle(i, "QteLotArticle")
        Next

        lSommeQte.Text = "Somme des Qtes : " + SommeDesQte.ToString

        gArticles.Refresh()

        '''''''''''''''''''''''
    End Sub

    Private Sub gListeLotArticle_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles gListeLotArticle.LostFocus
        'gListeLotArticle.Visible = False
    End Sub

    Private Sub gListeLotArticle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gListeLotArticle.Click

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub gListeRecherche_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gListeRecherche.Click

    End Sub

    Private Sub lOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
    End Sub

    Private Sub lAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub
    Public Sub AfficherFicheArticle(ByVal CodeArticle, ByVal StockArticle, ByVal Designation)
        Dim MyFicheArticle As New fFicheArticle

        MyFicheArticle.CodeArticle = CodeArticle
        MyFicheArticle.StockArticle = StockArticle
        MyFicheArticle.DesignationArticle = Designation
        MyFicheArticle.ajoutmodif = "M"

        MyFicheArticle.Init()
        MyFicheArticle.ShowDialog()
        MyFicheArticle.Close()
        MyFicheArticle.Dispose()
    End Sub

    Private Sub gArticles_Layout(ByVal sender As Object, ByVal e As System.Windows.Forms.LayoutEventArgs) Handles gArticles.Layout

    End Sub

    Private Sub gArticles_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gArticles.MouseClick
        '****************************************************************************************
        '********************** verouillage des lignes déja confirmées **************************
        '****************************************************************************************
        If mode <> "Ajout" Then
            Exit Sub
        End If

        If gArticles.Row <> dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1 Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        ElseIf gArticles.Row = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1 Then
            If gArticles(gArticles.Row, "CodeArticle") = "" Then
                gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = False
            End If
            If gArticles(gArticles.Row, "Designation") = "" Then
                gArticles.Splits(0).DisplayColumns("Designation").Locked = False
            End If
        End If

        If gArticles.Col = 2 And gArticles.Columns("Designation").Value <> "" Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        End If
        If gArticles.Col = 3 And gArticles.Columns("CodeArticle").Value <> "" Then
            gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
            gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        End If
    End Sub

    Private Sub bAfficherLot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAfficherLot.Click
        If mode = "Ajout" Then
            If gArticles.Columns("CodeArticle").Value <> "" Then
                AfficherLotArticle(gArticles.Columns("CodeArticle").Value)
            End If
        End If
    End Sub

    Private Sub bAnnulerLot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnulerLot.Click
        Dim Qte As Integer = 0
        'gArticles(gArticles.Row, "StockActuel") = gArticles(gArticles.Row, "StockInitial")
        For i = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
            If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") = gArticles(gArticles.Row, "CodeArticle") Then
                Qte += dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel")
            End If
        Next

        gArticles(gArticles.Row, "StockActuel") = Qte
        PanelAfficherLots.Visible = False
        gListeLotArticle.Visible = False
        bOkLot.Visible = False
        bAnnulerLot.Visible = False
        bAjouterLot.Visible = False

        gArticles.Focus()
        gArticles.Col = 8
    End Sub

    Private Sub bOkLot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOkLot.Click
        Dim i As Integer = 0
        Dim SommeQuantite As Integer = 0
        Dim LotTrouve As Boolean = False
        Dim QteDuLotSansDatePeremption As Integer = 0
        Dim StrSQL As String = ""
        Dim CmdRecuperation As New SqlCommand

        Dim NumeroLot As String = ""
        Dim CodeArticle As String = ""
        Dim Qte As Integer = 0

        'NumeroLot = gListeLotArticle.Columns("NumeroLotArticle").Value
        'CodeArticle = gListeLotArticle.Columns("CodeArticle").Value
        'Qte = gListeLotArticle.Columns("QteLotArticle").Value

        'For i = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
        '    If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") <> "" Then
        '        If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroLot") = NumeroLot And dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") = CodeArticle Then
        '            dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel") = Qte
        '        End If
        '    End If
        'Next

        For i = 0 To dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows.Count - 1
            SommeQuantite = SommeQuantite + dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows(i).Item("QteLotArticle")
        Next

        If SommeQuantite > gArticles(gArticles.Row, "StockInitial") Then
            If SommeQuantite = gArticles(gArticles.Row, "StockActuel") Then

                ' Si la somme des lots est inférieur au nouveau stock mentionné 
                ' si il y a un lot sans date de péremption on ajoute la différence
                ' si non on créer un nouveau lot sans date de péremption

                'For i = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
                '    If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") <> "" Then
                '        If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") = gListeLotArticle.Columns("CodeArticle").Value And dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroLot") = " " Then

                '            'recupération de la quantité existante dans le lot sans date de péremption 
                '            StrSQL = "SELECT QteLotArticle FROM LOT_ARTICLE WHERE NumeroLotArticle=' '" + _
                '            " AND CodeArticle='" + _
                '            dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") + "'"

                '            CmdRecuperation.Connection = ConnectionServeur
                '            CmdRecuperation.CommandText = StrSQL
                '            Try
                '                QteDuLotSansDatePeremption = CmdRecuperation.ExecuteScalar().ToString
                '            Catch ex As Exception
                '                Console.WriteLine(ex.Message)
                '            End Try

                '            dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel") = QteDuLotSansDatePeremption + (gArticles(gArticles.Row, "StockActuel") - SommeQuantite)
                '            LotTrouve = True
                '        End If
                '    End If
                'Next

                'If LotTrouve = False Then
                '    NouvelArticleEnregistrement("NumeroInventaire") = RecupereNumuero()
                '    NouvelArticleEnregistrement("CodeArticle") = gListeLotArticle.Columns("CodeArticle").Value
                '    NouvelArticleEnregistrement("Designation") = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", NouvelArticleEnregistrement("CodeArticle"))
                '    NouvelArticleEnregistrement("NumeroLot") = " "

                '    Try
                '        NouvelArticleEnregistrement("CodeForme") = RecupererValeurExecuteScalaire("CodeForme", " ARTICLE", "CodeArticle", NouvelArticleEnregistrement("CodeArticle"))
                '        NouvelArticleEnregistrement("LibelleForme") = RecupererValeurExecuteScalaire("LibelleForme", " FORME_ARTICLE", "CodeForme", NouvelArticleEnregistrement("CodeArticle"))
                '    Catch ex As Exception
                '    End Try

                '    NouvelArticleEnregistrement("Rayon") = RecupererValeurExecuteScalaire("Rayon", " ARTICLE", "CodeArticle", NouvelArticleEnregistrement("CodeArticle"))

                '    NouvelArticleEnregistrement("StockInitial") = 0
                '    NouvelArticleEnregistrement("StockActuel") = (gArticles(gArticles.Row, "StockActuel") - SommeQuantite)

                '    NouvelArticleEnregistrement("PrixAchatTTC") = RecupererValeurExecuteScalaire("PrixAchatTTC", " ARTICLE", "CodeArticle", NouvelArticleEnregistrement("CodeArticle"))
                '    NouvelArticleEnregistrement("PrixVenteTTC") = RecupererValeurExecuteScalaire("PrixVenteTTC", " ARTICLE", "CodeArticle", NouvelArticleEnregistrement("CodeArticle"))

                '    NouvelArticleEnregistrement = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                '    NouvelArticleEnregistrement("Designation") = ""                   'juste une initialisation pour faire la comparaison entre deux chaines de caractères (initialement son valeur est de type DBNULL)
                '    NouvelArticleEnregistrement("CodeArticle") = ""
                '    NouvelArticleEnregistrement("NumeroLot") = ""
                '    dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticleEnregistrement)

                'End If
                'PanelAfficherLots.Visible = False
                'gListeLotArticle.Visible = False
                'bOkLot.Visible = False
                'bAnnulerLot.Visible = False
                'bAjouterLot.Visible = False
            ElseIf SommeQuantite > gArticles(gArticles.Row, "StockActuel") Then
                MsgBox("La somme des quantités est différente a celle mentionné dans la fiche !" + Chr(13) + " Vous devez corriger les quantités des lots !", MsgBoxStyle.OkOnly)
                Exit Sub
            End If
            PanelAfficherLots.Visible = False
            gListeLotArticle.Visible = False
            bOkLot.Visible = False
            bAnnulerLot.Visible = False
            bAjouterLot.Visible = False
            gArticles.Focus()
            gArticles.MoveNext()
            gArticles.Col = 2
        ElseIf SommeQuantite <= gArticles(gArticles.Row, "StockInitial") Then
            If SommeQuantite <> gArticles(gArticles.Row, "StockActuel") Then
                MsgBox("Somme des quantités est différente a celle mentionné dans la fiche !" + Chr(13) + " Vous devez corriger les quantités des lots !", MsgBoxStyle.OkOnly)
                Exit Sub
            End If
            PanelAfficherLots.Visible = False
            gListeLotArticle.Visible = False
            bOkLot.Visible = False
            bAnnulerLot.Visible = False
            bAjouterLot.Visible = False
        End If
        PanelAfficherLots.Visible = False
        gListeLotArticle.Visible = False
        bOkLot.Visible = False
        bAnnulerLot.Visible = False
        bAjouterLot.Visible = False

        For j = 0 To dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows.Count - 1
            NumeroLot = dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows(j).Item("NumeroLotArticle")
            CodeArticle = dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows(j).Item("CodeArticle")
            If dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows(j).Item("QteLotArticle").ToString <> "" Then
                Qte = dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows(j).Item("QteLotArticle")
            End If
            For i = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
                If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") <> "" Then
                    If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroLot") = NumeroLot And dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") = CodeArticle Then
                        dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel") = Qte
                    End If
                End If
            Next
        Next


        gArticles.Focus()
        gArticles.Col = 8
        'For i = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1
        '    If dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("CodeArticle") = gListeLotArticle.Columns("CodeArticle").Value Then
        '        dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("StockActuel") = SommeQuantite
        '    End If
        'Next
    End Sub

    Private Sub gArticles_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gArticles.KeyDown
        '---------------------------------- annuler le changemant de code article d'un ancien enregistrement 
        'If gArticles.Row <> dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1 And (gArticles.Col = 2 Or gArticles.Col = 8) Then
        '    gArticles.Splits(0).DisplayColumns("CodeABarre").Locked = True
        '    gArticles.Splits(0).DisplayColumns("Designation").Locked = True
        'End If
    End Sub

    Private Sub gArticles_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gArticles.FetchRowStyle

        If gArticles.Columns("StockInitial").CellText(e.Row) <> gArticles.Columns("StockActuel").CellText(e.Row) Then
            e.CellStyle.BackColor = Color.FromArgb(255, 255, 192, 192)
        End If

    End Sub

    Private Sub bAjouterLot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterLot.Click

        Dim DLigneLot As DataRow = Nothing
        Dim DligneLotInventaireDetails As DataRow = Nothing
        Dim MaxNumeroLot As Integer = 0

        For i = 0 To dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows.Count - 1
            If LTrim(dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows(i).Item("NumeroLotArticle")) <> "" Then
                If IsNumeric(dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows(i).Item("NumeroLotArticle")) Then
                    If MaxNumeroLot < dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows(i).Item("NumeroLotArticle") Then
                        MaxNumeroLot = dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows(i).Item("NumeroLotArticle")
                    End If
                End If
            End If

        Next

        DLigneLot = dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").NewRow()

        DLigneLot("NumeroLotArticle") = MaxNumeroLot + 1
        DLigneLot("CodeArticle") = dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows(0).Item("CodeArticle")
        DLigneLot("Designation") = dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows(0).Item("Designation")
        DLigneLot("QteLotArticle") = 0 'dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel")

        dsInventaire.Tables("LOT_ARTICLE_DISPONIBLE").Rows.Add(DLigneLot)

        DligneLotInventaireDetails = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
        For i = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
            If LTrim(dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle")) = DLigneLot("CodeArticle") Then
                DligneLotInventaireDetails("NumeroInventaire") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroInventaire")
                DligneLotInventaireDetails("CodeArticle") = DLigneLot("CodeArticle")
                DligneLotInventaireDetails("CodeABarre") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeABarre")
                DligneLotInventaireDetails("Designation") = DLigneLot("Designation")
                DligneLotInventaireDetails("NumeroLot") = MaxNumeroLot + 1
                DligneLotInventaireDetails("LibelleForme") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("LibelleForme")
                DligneLotInventaireDetails("CodeForme") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeForme")
                DligneLotInventaireDetails("Rayon") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("Rayon")
                DligneLotInventaireDetails("StockInitial") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockInitial")
                DligneLotInventaireDetails("StockActuel") = 0 '0dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel")
                DligneLotInventaireDetails("PrixAchatTTC") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("PrixAchatTTC")
                DligneLotInventaireDetails("TotalAchatTTC") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("TotalAchatTTC")
                DligneLotInventaireDetails("PrixVenteTTC") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("PrixVenteTTC")
                Exit For
            End If
        Next
        dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(DligneLotInventaireDetails)

    End Sub

    Private Sub gArticles_AfterColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gArticles.AfterColUpdate
        If gArticles.Col = 8 Then
            If IsNumeric(gArticles.Columns("StockActuel").Value) = True Then

                If PanelAfficherLots.Visible = False Then
                    ChangerLotsArticle(gArticles.Columns("CodeArticle").Value, gArticles.Columns("StockActuel").Value) 'ne reste que le lot sans numéro et les autres seront 0
                    'AfficherLotArticle()
                    If gListeLotArticle.Visible = True Then
                        Exit Sub
                    End If
                End If

            ElseIf gArticles.Columns("StockActuel").Value.ToString <> "-" Then
                gArticles.Columns("StockActuel").Value = ""
            End If





            'refresh montants
            CalculerMontants()





        End If
    End Sub


    Private Sub gArticles_UnboundColumnFetch(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gArticles.UnboundColumnFetch

        If gArticles.Columns("StockInitial").CellText(e.Row) <> gArticles.Columns("StockActuel").CellText(e.Row) Then
            'Si on va appliquer l'inventaire partiel, on se base sur cette valeur pour faire la mise a jour de la date de dernier inventaire
            e.Value = "1"
        Else
            e.Value = "0"
        End If

    End Sub

    Private Sub bListe_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bListe.Click
        fListeDesInventaires.ShowDialog()
        If fListeDesInventaires.NumeroInventaire <> "" Then
            NumeroInventaire = fListeDesInventaires.NumeroInventaire
        End If
        rechercheInventaire(NumeroInventaire)
    End Sub

    Private Sub rechercheInventaire(ByVal pNumeroInventaire As String)
        Dim j As Integer
        Dim DataRowRecherche As DataRow
        Dim NumeroLigne As Integer
        Dim cmd As New SqlCommand

        cmd.Connection = ConnectionServeur
        cmd.CommandText = "SELECT COUNT(*) FROM INVENTAIRE WHERE NumeroInventaire=" + Quote(pNumeroInventaire)
        If cmd.ExecuteScalar = 0 Then
            MsgBox("Inventaire inexistant !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        For j = 0 To dsChargement.Tables("INVENTAIRE").Rows.Count - 1
            DataRowRecherche = dsChargement.Tables("INVENTAIRE").Rows(j)
            If DataRowRecherche.Item("NumeroInventaire").ToString = pNumeroInventaire Then
                NumeroLigne = j
            End If
        Next

        If NumeroLigne <= dsChargement.Tables("INVENTAIRE").Rows.Count - 1 Then
            DataRowRecherche = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne)
            'chargement des informations entête
            lNumeroInventaire.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne)("NumeroInventaire").ToString
            lDateInventaire.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne)("Date").ToString

            lActuelleAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne)("ValeurAchatActuelle").ToString
            lActuelleVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne)("ValeurVenteActuelle").ToString

            lInitialeAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne)("ValeurAchatInitial").ToString
            lInitialeVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne)("ValeurVenteInitial").ToString

            lDifferenceAchatTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne)("ValeurAchatDifference").ToString
            lDifferenceVenteTTC.Text = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne)("ValeurVenteDifference").ToString

            lOperateur.Text = RecupererValeurExecuteScalaire("NomOperateur", "OPERATEUR", "CodeOperateur", dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne)("CodePersonnel")).ToString
            tRemarqueInventaire.Value = dsChargement.Tables("INVENTAIRE").Rows(NumeroLigne)("Remarque")

            NumeroInventaire = DataRowRecherche.Item("NumeroInventaire").ToString
            ChargerDetails()

        End If
    End Sub

    Private Sub bRecherche_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRecherche.Click
        Try
            tRecherche.Visible = True
            tRecherche.Text = System.DateTime.Now.Year.ToString + "/"
            tRecherche.Focus()
            tRecherche.Select(tRecherche.Text.Length, 0)
            'FromF6 = True
            bAnnuler.Enabled = True
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Inventaire", "fInventaire", " bRecherche_Click", ex.Message, "0000???", "Erreur d'exécution de bRecherche_Click ", True, True, True)

        End Try
    End Sub

    Private Sub tRecherche_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRecherche.KeyUp
        Try
            If e.KeyCode = Keys.Enter And tRecherche.Text <> "" Then
                rechercheInventaire(tRecherche.Text)
            End If
        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Vente", "fVente", " tRecherche_KeyUp", ex.Message, "0000236", "Erreur d'exécution de tRecherche_KeyUp ", True, True, True)

        End Try
    End Sub

    Private Sub tRecherche_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tRecherche.LostFocus
        tRecherche.Visible = False
    End Sub

    Private Sub bTerminal_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bTerminal.Click

        Dim MyInventaireLectureTerminal As New fInventaireLectureTerminal
        MyInventaireLectureTerminal.Mode = "Inventaire"
        Dim dsNewInventaire As DataSet
        Dim total, j As Integer

        MyInventaireLectureTerminal.Init()
        MyInventaireLectureTerminal.ShowDialog()
        dsNewInventaire = MyInventaireLectureTerminal.dsInventaire

        If MyInventaireLectureTerminal.Valider = True Then
            j = 0
            total = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count
            While j < total
                If dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(j).Item("CodeArticle") = "" Then
                    dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(j).Delete()
                    total -= 1
                End If
                j += 1
            End While
            For i = 0 To dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1

                For j = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Count - 1
                    If dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(j).Item("CodeArticle") <> "" Then
                        If dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("CodeArticle") = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(j).Item("CodeArticle") Then 'And dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("Zone") = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(j).Item("Zone") Then
                            dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(j).Item("StockActuel") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("StockActuel")
                            dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(j).Item("StockInitial") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("StockInitial")
                            GoTo Endfor
                        End If
                    End If
                Next

                NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").NewRow()
                NouvelArticle("NumeroInventaire") = RecupereNumuero()
                NouvelArticle("CodeArticle") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("CodeArticle")
                NouvelArticle("CodeABarre") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("CodeABarre")
                NouvelArticle("Designation") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("Designation")

                NouvelArticle("StockActuel") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("StockActuel")
                NouvelArticle("StockInitial") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("StockInitial")

                NouvelArticle("PrixAchatTTC") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("PrixAchatTTC")
                NouvelArticle("PrixVenteTTC") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("PrixVenteTTC")

                NouvelArticle("TotalAchatTTC") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("TotalAchatTTC")

                NouvelArticle("CodeForme") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("CodeForme")
                NouvelArticle("LibelleForme") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("LibelleForme")
                NouvelArticle("Rayon") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("Rayon")
                'NouvelArticle("Zone") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("Zone")
                'NouvelArticle("CodeZone") = dsNewInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows(i).Item("CodeZone")
                dsInventaire.Tables("INVENTAIRE_DETAILS_AFFICHE").Rows.Add(NouvelArticle)

Endfor:
            Next

            '''''''''' boucle pour INVENTAIRE_DETAILS
            For i = 0 To dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
                For j = 0 To dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Count - 1
                    If dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(j).Item("CodeArticle") <> "" Then
                        If dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(j).Item("CodeArticle") Then
                            If IsDBNull(dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(j).Item("NumeroLot")) = True Then
                                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(j).Item("NumeroLot") = ""
                            End If
                            If dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroLot") = dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(j).Item("NumeroLot") Then
                                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows(j).Item("StockActuel") = dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel")
                                GoTo Endfor2
                            End If
                        End If
                    End If
                Next
                NouvelArticle = dsInventaire.Tables("INVENTAIRE_DETAILS").NewRow()
                NouvelArticle("NumeroInventaire") = RecupereNumuero()
                NouvelArticle("CodeArticle") = dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeArticle")
                NouvelArticle("CodeABarre") = dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeABarre")
                NouvelArticle("NumeroLot") = dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("NumeroLot")
                NouvelArticle("Designation") = dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("Designation")
                NouvelArticle("StockActuel") = dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockActuel")
                NouvelArticle("StockInitial") = dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("StockInitial")
                NouvelArticle("PrixAchatTTC") = dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("PrixAchatTTC")
                NouvelArticle("PrixVenteTTC") = dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("PrixVenteTTC")
                NouvelArticle("TotalAchatTTC") = dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("TotalAchatTTC")
                NouvelArticle("CodeForme") = dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("CodeForme")
                NouvelArticle("LibelleForme") = dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("LibelleForme")
                NouvelArticle("Rayon") = dsNewInventaire.Tables("INVENTAIRE_DETAILS").Rows(i).Item("Rayon")
                dsInventaire.Tables("INVENTAIRE_DETAILS").Rows.Add(NouvelArticle)
Endfor2:
            Next
            '''''''''''''''

            With gArticles

                '.Columns.Clear()
                Try
                    .DataSource = dsInventaire
                Catch ex As Exception
                End Try
                .DataMember = "INVENTAIRE_DETAILS_AFFICHE"
                .Rebind(False)
                .Columns("CodeArticle").Caption = "Code article"
                .Columns("Designation").Caption = "Désignation"
                .Columns("LibelleForme").Caption = "Forme"
                .Columns("Rayon").Caption = "Rayon"
                .Columns("StockInitial").Caption = "Stock Théorique" '"Stock Initial"
                .Columns("StockActuel").Caption = "Stock Réel" '"nouveau Stock"
                .Columns("PrixAchatTTC").Caption = "PU Achat" '"Prix achat TTC"
                .Columns("TotalAchatTTC").Caption = "Valeur Achat Total" '"Total Achat TTC"
                .Columns("QteChange").Caption = "QteChange"
                .Columns("QteChange").DataField = ""
                ' Centrer toutes les entêtes
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                ' Centrer toutes les valeurs
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Locked = True
                Next

                .Splits(0).DisplayColumns("StockActuel").Locked = False

                .FilterBar = True
                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
                .Splits(0).DisplayColumns("PrixAchatTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("TotalAchatTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("PrixVenteTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
                .Splits(0).DisplayColumns("NumeroInventaire").Width = 0
                .Splits(0).DisplayColumns("NumeroInventaire").Visible = False
                .Splits(0).DisplayColumns("NumeroInventaire").AllowSizing = False
                .Splits(0).DisplayColumns("CodeArticle").Width = 0
                '.Splits(0).DisplayColumns("CodeArticle").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False
                .Splits(0).DisplayColumns("CodeArticle").Width = 100
                .Splits(0).DisplayColumns("Designation").Width = 320
                .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
                .Splits(0).DisplayColumns("LibelleForme").Width = 80
                .Splits(0).DisplayColumns("Rayon").Width = 80
                .Splits(0).DisplayColumns("CodeForme").Width = 0
                .Splits(0).DisplayColumns("StockInitial").Width = 80
                .Splits(0).DisplayColumns("StockActuel").Width = 80
                .Splits(0).DisplayColumns("PrixAchatTTC").Width = 80
                '.Columns("PrixAchatTTC").NumberFormat = "#,###" + NombreDeChiffreApresVirgule()
                .Splits(0).DisplayColumns("TotalAchatTTC").Width = 80
                .Splits(0).DisplayColumns("PrixVenteTTC").Width = 0
                .Splits(0).DisplayColumns("PrixVenteTTC").Visible = False
                .Splits(0).DisplayColumns("CodeForme").Visible = False
                .Splits(0).DisplayColumns("CodeArticle").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("Designation").Style.BackColor = Color.FromArgb(255, 244, 252, 251)
                .Splits(0).DisplayColumns("StockActuel").Style.BackColor = Color.FromArgb(255, 244, 252, 251)

                .Splits(0).DisplayColumns("QteChange").Visible = False
                .Splits(0).DisplayColumns("Rayon").Visible = False
                .Splits(0).DisplayColumns("TotalAchatTTC").Visible = False

                .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
                .Splits(0).ColumnCaptionHeight = 40
                .Splits(0).RecordSelectors = False
                .ExtendRightColumn = True
                .EmptyRows = True
                .FetchRowStyles = True
                .AllowSort = True
                .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
                '.Columns.Add(New C1.Win.C1TrueDBGrid.C1DataColumn("Zone", GetType(String)))
                .EditActive = True
            End With
            CalculerMontants()
        End If
    End Sub

    Private Sub gArticles_BeforeColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColEditEventArgs) Handles gArticles.BeforeColEdit
        If gArticles.Columns(gArticles.Col).DataField = "StockActuel" Then
            If Not IsDBNull(gArticles.Columns("StockActuel").Value) Then
                qteAvantModif = gArticles.Columns("StockActuel").Value
            End If
        End If
    End Sub
End Class