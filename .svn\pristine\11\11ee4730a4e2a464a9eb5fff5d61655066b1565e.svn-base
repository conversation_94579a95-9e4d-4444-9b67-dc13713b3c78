﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Public Class fAPCI
    Dim cmdAPCI As New SqlCommand
    Dim daAPCI As New SqlDataAdapter
    Dim cbAPCI As New SqlCommandBuilder
    Dim dsAPCI As New DataSet

    Dim xAPCI As Integer
    Dim ModeAPCI As String
    Dim CodeAPCI As String

    Dim dsRecupereNum As New DataSet
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNumt As New SqlDataAdapter

    Dim CodeExiste As Boolean = False

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub afficherAPCI()
        Dim I As Integer
        Dim Cond As String = "1=1"
        dsAPCI.Clear()
        cmdAPCI.CommandText = " SELECT " + _
                                    " CodeAPCI, " + _
                                    " NomAPCI " + _
                                    " FROM APCI WHERE " + Cond + _
                                    " ORDER BY CodeAPCI"

        cmdAPCI.Connection = ConnectionServeur
        daAPCI = New SqlDataAdapter(cmdAPCI)
        daAPCI.Fill(dsAPCI, "APCI")

        With gAPCI
            .Columns.Clear()
            .DataSource = dsAPCI
            .DataMember = "APCI"
            .Rebind(False)
            .Columns("CodeAPCI").Caption = "Code APCI"
            .Columns("NomAPCI").Caption = "Libelle APCI"
            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns("CodeAPCI").Width = 120
            .Splits(0).DisplayColumns("CodeAPCI").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("NomAPCI").Width = 80
            .Splits(0).DisplayColumns("NomAPCI").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(0).DisplayColumns("CodeAPCI").Locked = True
            .Splits(0).DisplayColumns("NomAPCI").Locked = True

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gAPCI)
        End With
        gAPCI.MoveRelative(xAPCI)
        cbAPCI = New SqlCommandBuilder(daAPCI)
    End Sub

    Private Sub bAjouter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouter.Click
        Dim dr As DataRow
        Dim StrSQL As String = ""

        If tCodeAPCI.Text = "" Then
            MsgBox("Veuillez saisir le code APCi !", MsgBoxStyle.Critical, "Erreur")
            tCodeAPCI.Focus()
            Exit Sub
        End If
        If tLibelleAPCI.Text = "" Then
            MsgBox("Veuillez saisir le libelle APCI !", MsgBoxStyle.Critical, "Erreur")
            tLibelleAPCI.Focus()
            Exit Sub
        End If

        If CodeExiste = True Then
            MsgBox("Code APCI existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
            tCodeAPCI.Focus()
            Exit Sub
        End If

        With dsAPCI
            dr = .Tables("APCI").NewRow
            dr.Item("NomAPCI") = tLibelleAPCI.Text
            dr.Item("CodeAPCI") = tCodeAPCI.Text
            .Tables("APCI").Rows.Add(dr)
        End With

        Try
            daAPCI.Update(dsAPCI, "APCI")
            afficherAPCI()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsAPCI.Reset()
        End Try

        tCodeAPCI.Text = ""
        tLibelleAPCI.Text = ""
    End Sub

    Private Sub bSupprimerAPCI_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimer.Click
        Dim cmd As New SqlCommand
        Dim CodeAPCI As Integer
        If gAPCI.RowCount > 0 Then
            CodeAPCI = gAPCI(gAPCI.Row, "CodeAPCI")
            If MsgBox("Voulez vous vraiment supprimer cette APCI " + Quote(gAPCI(gAPCI.Row, "NomAPCI")) + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM APCI WHERE CodeAPCI =" + Quote(CodeAPCI)
                    cmd.ExecuteNonQuery()
                    afficherAPCI()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub

    Private Sub gAPCI_AfterColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gAPCI.AfterColEdit
        Dim dr As DataRow
        Dim StrSQL As String = ""

        With dsAPCI.Tables("APCI_MAJ")
            dr = .Rows(0)
            dr.Item("NomAPCI") = gAPCI(gAPCI.Row, "NomAPCI")

        End With

        Try
            daAPCI.Update(dsAPCI, "APCI_MAJ")

        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            afficherAPCI()
        End Try
    End Sub

    Private Sub gAPCI_Change(ByVal sender As Object, ByVal e As System.EventArgs) Handles gAPCI.Change

    End Sub

    Private Sub gAPCI_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles gAPCI.Click
        Dim StrSQL As String = ""
        CodeAPCI = Quote(gAPCI(gAPCI.Row, "CodeAPCI"))
        If CodeAPCI = "" Then
            MsgBox("Veuillez sélectionner le libelle de l'APCI !", MsgBoxStyle.Critical, "Erreur")
            gAPCI.Focus()
            Exit Sub
        End If
        If (dsAPCI.Tables.IndexOf("APCI_MAJ") > -1) Then
            dsAPCI.Tables("APCI_MAJ").Clear()
        End If

        StrSQL = " SELECT * FROM APCI WHERE CodeAPCI = " + CodeAPCI
        cmdAPCI.Connection = ConnectionServeur
        cmdAPCI.CommandText = StrSQL
        daAPCI = New SqlDataAdapter(cmdAPCI)
        daAPCI.Fill(dsAPCI, "APCI_MAJ")
        cbAPCI = New SqlCommandBuilder(daAPCI)
    End Sub

    Private Sub tCodeAPCI_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeAPCI.KeyUp
        If e.KeyCode = Keys.Enter Then
            tLibelleAPCI.Focus()
        End If
    End Sub

    Private Sub tCodeAPCI_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeAPCI.LostFocus
        lTest.Visible = False
    End Sub

    Private Sub tCodeAPCI_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeAPCI.TextChanged

        If tCodeAPCI.Text <> "" Then
            If IsNumeric(tCodeAPCI.Text.Substring(Len(tCodeAPCI.Text) - 1, 1)) = False Then
                MsgBox("Veuillez introduire des chiffres !", MsgBoxStyle.Critical, "Erreur")
                tCodeAPCI.Text = tCodeAPCI.Text.Substring(0, Len(tCodeAPCI.Text) - 1)
                tCodeAPCI.Select(Len(tCodeAPCI.Text), 0)
            End If
        End If

        Dim StrSQLtest As String = ""
        If (dsRecupereNum.Tables.IndexOf("APCI_TEST") > -1) Then
            dsRecupereNum.Tables("APCI_TEST").Clear()
        End If

        StrSQLtest = " SELECT * FROM APCI as APCI_TEST WHERE CodeAPCI=" + Quote(tCodeAPCI.Text)
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQLtest
        daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
        daRecupereNumt.Fill(dsRecupereNum, "APCI_TEST")

        If dsRecupereNum.Tables("APCI_TEST").Rows.Count <> 0 Then
            lTest.Text = "Code non valide déja existe"
            lTest.ForeColor = Color.OrangeRed
            lTest.Visible = True
            CodeExiste = True
        Else
            lTest.Text = "Code valide"
            lTest.ForeColor = Color.LawnGreen
            lTest.Visible = True
            CodeExiste = False
        End If
        If tCodeAPCI.Text = "" Then
            lTest.Visible = False
        End If
    End Sub

    Public Sub Init()
        afficherAPCI()
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub
End Class