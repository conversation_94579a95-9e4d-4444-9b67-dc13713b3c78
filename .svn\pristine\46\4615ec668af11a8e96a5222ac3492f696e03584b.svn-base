﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fEtatEntreeArticle
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fEtatEntreeArticle))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.tValeurVente = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.tValeurAchat = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.bImprimer = New C1.Win.C1Input.C1Button()
        Me.gEntree = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.dtpFin = New C1.Win.C1Input.C1DateEdit()
        Me.dtpDebut = New C1.Win.C1Input.C1DateEdit()
        Me.cmbNature = New C1.Win.C1List.C1Combo()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.CR = New Pharma2000Premium.EtatMouvementDuFournisseur()
        Me.Panel.SuspendLayout()
        CType(Me.gEntree, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.dtpFin, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpDebut, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbNature, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.tValeurVente)
        Me.Panel.Controls.Add(Me.Label6)
        Me.Panel.Controls.Add(Me.tValeurAchat)
        Me.Panel.Controls.Add(Me.Label4)
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.bImprimer)
        Me.Panel.Controls.Add(Me.gEntree)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(907, 560)
        Me.Panel.TabIndex = 0
        '
        'tValeurVente
        '
        Me.tValeurVente.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.tValeurVente.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tValeurVente.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tValeurVente.ForeColor = System.Drawing.Color.Green
        Me.tValeurVente.Location = New System.Drawing.Point(86, 525)
        Me.tValeurVente.Name = "tValeurVente"
        Me.tValeurVente.Size = New System.Drawing.Size(111, 24)
        Me.tValeurVente.TabIndex = 37
        Me.tValeurVente.Text = "0"
        Me.tValeurVente.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.Location = New System.Drawing.Point(12, 531)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(68, 13)
        Me.Label6.TabIndex = 36
        Me.Label6.Text = "Valeur Vente"
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'tValeurAchat
        '
        Me.tValeurAchat.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.tValeurAchat.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tValeurAchat.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tValeurAchat.ForeColor = System.Drawing.Color.Green
        Me.tValeurAchat.Location = New System.Drawing.Point(86, 498)
        Me.tValeurAchat.Name = "tValeurAchat"
        Me.tValeurAchat.Size = New System.Drawing.Size(111, 24)
        Me.tValeurAchat.TabIndex = 35
        Me.tValeurAchat.Text = "0"
        Me.tValeurAchat.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label4
        '
        Me.Label4.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.Location = New System.Drawing.Point(12, 504)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(68, 13)
        Me.Label4.TabIndex = 34
        Me.Label4.Text = "Valeur Achat"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(783, 14)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(112, 45)
        Me.bQuitter.TabIndex = 16
        Me.bQuitter.Text = "Fermer                F12"
        Me.bQuitter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bImprimer
        '
        Me.bImprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.imprmante1
        Me.bImprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bImprimer.Location = New System.Drawing.Point(783, 66)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(112, 45)
        Me.bImprimer.TabIndex = 15
        Me.bImprimer.Text = "Imprimer                F9"
        Me.bImprimer.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gEntree
        '
        Me.gEntree.AllowUpdate = False
        Me.gEntree.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gEntree.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.gEntree.GroupByCaption = "Drag a column header here to group by that column"
        Me.gEntree.Images.Add(CType(resources.GetObject("gEntree.Images"), System.Drawing.Image))
        Me.gEntree.Location = New System.Drawing.Point(12, 122)
        Me.gEntree.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gEntree.Name = "gEntree"
        Me.gEntree.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gEntree.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gEntree.PreviewInfo.ZoomFactor = 75.0R
        Me.gEntree.PrintInfo.PageSettings = CType(resources.GetObject("gEntree.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gEntree.Size = New System.Drawing.Size(883, 362)
        Me.gEntree.TabIndex = 1
        Me.gEntree.Text = "C1TrueDBGrid1"
        Me.gEntree.PropBag = resources.GetString("gEntree.PropBag")
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.Controls.Add(Me.dtpFin)
        Me.GroupBox1.Controls.Add(Me.dtpDebut)
        Me.GroupBox1.Controls.Add(Me.cmbNature)
        Me.GroupBox1.Controls.Add(Me.Label3)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox1.Location = New System.Drawing.Point(12, 12)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(573, 104)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Critères de recherche"
        '
        'dtpFin
        '
        Me.dtpFin.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpFin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpFin.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpFin.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFin.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFin.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        Me.dtpFin.Location = New System.Drawing.Point(189, 56)
        Me.dtpFin.Name = "dtpFin"
        Me.dtpFin.Size = New System.Drawing.Size(141, 18)
        Me.dtpFin.TabIndex = 29
        Me.dtpFin.Tag = Nothing
        Me.dtpFin.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFin.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'dtpDebut
        '
        Me.dtpDebut.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDebut.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDebut.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDebut.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebut.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebut.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        Me.dtpDebut.Location = New System.Drawing.Point(26, 56)
        Me.dtpDebut.Name = "dtpDebut"
        Me.dtpDebut.Size = New System.Drawing.Size(141, 18)
        Me.dtpDebut.TabIndex = 28
        Me.dtpDebut.Tag = Nothing
        Me.dtpDebut.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebut.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cmbNature
        '
        Me.cmbNature.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.cmbNature.Caption = ""
        Me.cmbNature.CaptionHeight = 17
        Me.cmbNature.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.cmbNature.ColumnCaptionHeight = 17
        Me.cmbNature.ColumnFooterHeight = 17
        Me.cmbNature.ContentHeight = 17
        Me.cmbNature.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.cmbNature.EditorBackColor = System.Drawing.SystemColors.Window
        Me.cmbNature.EditorFont = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbNature.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.cmbNature.EditorHeight = 17
        Me.cmbNature.Images.Add(CType(resources.GetObject("cmbNature.Images"), System.Drawing.Image))
        Me.cmbNature.ItemHeight = 15
        Me.cmbNature.Location = New System.Drawing.Point(361, 53)
        Me.cmbNature.MatchEntryTimeout = CType(2000, Long)
        Me.cmbNature.MaxDropDownItems = CType(5, Short)
        Me.cmbNature.MaxLength = 32767
        Me.cmbNature.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.cmbNature.Name = "cmbNature"
        Me.cmbNature.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.cmbNature.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.cmbNature.Size = New System.Drawing.Size(194, 23)
        Me.cmbNature.TabIndex = 5
        Me.cmbNature.VisualStyle = C1.Win.C1List.VisualStyle.Office2007Blue
        Me.cmbNature.PropBag = resources.GetString("cmbNature.PropBag")
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Location = New System.Drawing.Point(359, 37)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(39, 13)
        Me.Label3.TabIndex = 4
        Me.Label3.Text = "Nature"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(188, 40)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(20, 13)
        Me.Label2.TabIndex = 1
        Me.Label2.Text = "Au"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(24, 40)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(21, 13)
        Me.Label1.TabIndex = 0
        Me.Label1.Text = "Du"
        '
        'fEtatEntreeArticle
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 14.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(907, 560)
        Me.Controls.Add(Me.Panel)
        Me.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Name = "fEtatEntreeArticle"
        Me.Panel.ResumeLayout(False)
        Me.Panel.PerformLayout()
        CType(Me.gEntree, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.dtpFin, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpDebut, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbNature, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents gEntree As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents cmbNature As C1.Win.C1List.C1Combo
    Friend WithEvents tValeurAchat As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents tValeurVente As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents dtpDebut As C1.Win.C1Input.C1DateEdit
    Friend WithEvents dtpFin As C1.Win.C1Input.C1DateEdit
    Friend WithEvents CR As Pharma2000Premium.EtatMouvementDuFournisseur
End Class
