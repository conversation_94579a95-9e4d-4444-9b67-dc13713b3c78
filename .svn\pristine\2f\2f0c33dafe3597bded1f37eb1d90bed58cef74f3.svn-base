﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fListeDesPreparationAProduire
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fListeDesPreparationAProduire))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bImprimer = New C1.Win.C1Input.C1Button()
        Me.lTitre = New System.Windows.Forms.Label()
        Me.gListe = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.CR = New Pharma2000Premium.EtatDesPreparationAProduire()
        Me.Panel.SuspendLayout()
        CType(Me.gListe, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.bImprimer)
        Me.Panel.Controls.Add(Me.lTitre)
        Me.Panel.Controls.Add(Me.gListe)
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(762, 476)
        Me.Panel.TabIndex = 9
        '
        'bImprimer
        '
        Me.bImprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.Imprimante
        Me.bImprimer.Location = New System.Drawing.Point(522, 424)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(113, 45)
        Me.bImprimer.TabIndex = 32
        Me.bImprimer.Text = "Imprimer          F9"
        Me.bImprimer.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lTitre
        '
        Me.lTitre.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lTitre.Font = New System.Drawing.Font("HandelGotDLig", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lTitre.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.lTitre.Location = New System.Drawing.Point(7, 7)
        Me.lTitre.Name = "lTitre"
        Me.lTitre.Size = New System.Drawing.Size(747, 40)
        Me.lTitre.TabIndex = 31
        Me.lTitre.Text = "PREPARATION A PRODUIRE"
        Me.lTitre.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'gListe
        '
        Me.gListe.AllowUpdate = False
        Me.gListe.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gListe.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gListe.GroupByCaption = "Drag a column header here to group by that column"
        Me.gListe.Images.Add(CType(resources.GetObject("gListe.Images"), System.Drawing.Image))
        Me.gListe.LinesPerRow = 2
        Me.gListe.Location = New System.Drawing.Point(7, 55)
        Me.gListe.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gListe.Name = "gListe"
        Me.gListe.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gListe.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gListe.PreviewInfo.ZoomFactor = 75.0R
        Me.gListe.PrintInfo.PageSettings = CType(resources.GetObject("gListe.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gListe.Size = New System.Drawing.Size(747, 363)
        Me.gListe.TabIndex = 4
        Me.gListe.Text = "C1TrueDBGrid1"
        Me.gListe.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2007Blue
        Me.gListe.PropBag = resources.GetString("gListe.PropBag")
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.Location = New System.Drawing.Point(641, 424)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(113, 45)
        Me.bQuitter.TabIndex = 3
        Me.bQuitter.Text = "Quitter            F12"
        Me.bQuitter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fListeDesPreparationAProduire
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(762, 476)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fListeDesPreparationAProduire"
        Me.ShowInTaskbar = False
        Me.Panel.ResumeLayout(False)
        CType(Me.gListe, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents lTitre As System.Windows.Forms.Label
    Friend WithEvents gListe As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
    Friend WithEvents CR As Pharma2000Premium.EtatDesPreparationAProduire
End Class
