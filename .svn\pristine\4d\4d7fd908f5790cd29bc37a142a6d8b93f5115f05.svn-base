﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fSituationClient
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fSituationClient))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.bSupprimerSituationClient = New C1.Win.C1Input.C1Button()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.tLibelleSituationClient = New C1.Win.C1Input.C1TextBox()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.bAjouterSituationClient = New C1.Win.C1Input.C1Button()
        Me.tCodeSituationClient = New C1.Win.C1Input.C1TextBox()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.gSituationClient = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Panel.SuspendLayout()
        Me.GroupBox4.SuspendLayout()
        CType(Me.tLibelleSituationClient, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodeSituationClient, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gSituationClient, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.Label1)
        Me.Panel.Controls.Add(Me.bSupprimerSituationClient)
        Me.Panel.Controls.Add(Me.GroupBox4)
        Me.Panel.Controls.Add(Me.gSituationClient)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1028, 566)
        Me.Panel.TabIndex = 0
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(916, 9)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(100, 45)
        Me.bQuitter.TabIndex = 89
        Me.bQuitter.Text = "Fermer             F12   "
        Me.bQuitter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("HandelGotDLig", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(13, 15)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(897, 33)
        Me.Label1.TabIndex = 88
        Me.Label1.Text = "LISTE DES SITUATIONS CLIENTS"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bSupprimerSituationClient
        '
        Me.bSupprimerSituationClient.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.bSupprimerSituationClient.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimerSituationClient.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler21
        Me.bSupprimerSituationClient.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSupprimerSituationClient.Location = New System.Drawing.Point(916, 501)
        Me.bSupprimerSituationClient.Name = "bSupprimerSituationClient"
        Me.bSupprimerSituationClient.Size = New System.Drawing.Size(100, 45)
        Me.bSupprimerSituationClient.TabIndex = 2
        Me.bSupprimerSituationClient.Text = "Supprimer"
        Me.bSupprimerSituationClient.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSupprimerSituationClient.UseVisualStyleBackColor = True
        Me.bSupprimerSituationClient.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox4
        '
        Me.GroupBox4.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.GroupBox4.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox4.Controls.Add(Me.tLibelleSituationClient)
        Me.GroupBox4.Controls.Add(Me.Label14)
        Me.GroupBox4.Controls.Add(Me.bAjouterSituationClient)
        Me.GroupBox4.Controls.Add(Me.tCodeSituationClient)
        Me.GroupBox4.Controls.Add(Me.Label15)
        Me.GroupBox4.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox4.Location = New System.Drawing.Point(12, 484)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(898, 70)
        Me.GroupBox4.TabIndex = 1
        Me.GroupBox4.TabStop = False
        '
        'tLibelleSituationClient
        '
        Me.tLibelleSituationClient.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tLibelleSituationClient.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tLibelleSituationClient.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tLibelleSituationClient.Location = New System.Drawing.Point(175, 39)
        Me.tLibelleSituationClient.Name = "tLibelleSituationClient"
        Me.tLibelleSituationClient.Size = New System.Drawing.Size(165, 18)
        Me.tLibelleSituationClient.TabIndex = 1
        Me.tLibelleSituationClient.Tag = Nothing
        Me.tLibelleSituationClient.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tLibelleSituationClient.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label14
        '
        Me.Label14.AutoSize = True
        Me.Label14.BackColor = System.Drawing.Color.Transparent
        Me.Label14.Location = New System.Drawing.Point(62, 41)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(107, 13)
        Me.Label14.TabIndex = 12
        Me.Label14.Text = "Libellé de la Situation"
        '
        'bAjouterSituationClient
        '
        Me.bAjouterSituationClient.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouterSituationClient.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouterSituationClient.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bAjouterSituationClient.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouterSituationClient.Location = New System.Drawing.Point(792, 16)
        Me.bAjouterSituationClient.Name = "bAjouterSituationClient"
        Me.bAjouterSituationClient.Size = New System.Drawing.Size(100, 45)
        Me.bAjouterSituationClient.TabIndex = 2
        Me.bAjouterSituationClient.Text = "Ajouter"
        Me.bAjouterSituationClient.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouterSituationClient.UseVisualStyleBackColor = True
        Me.bAjouterSituationClient.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tCodeSituationClient
        '
        Me.tCodeSituationClient.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodeSituationClient.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodeSituationClient.Location = New System.Drawing.Point(175, 14)
        Me.tCodeSituationClient.Name = "tCodeSituationClient"
        Me.tCodeSituationClient.Size = New System.Drawing.Size(165, 18)
        Me.tCodeSituationClient.TabIndex = 0
        Me.tCodeSituationClient.Tag = Nothing
        Me.tCodeSituationClient.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodeSituationClient.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.BackColor = System.Drawing.Color.Transparent
        Me.Label15.Location = New System.Drawing.Point(67, 16)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(102, 13)
        Me.Label15.TabIndex = 8
        Me.Label15.Text = "Code de la Situation"
        '
        'gSituationClient
        '
        Me.gSituationClient.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gSituationClient.GroupByCaption = "Drag a column header here to group by that column"
        Me.gSituationClient.Images.Add(CType(resources.GetObject("gSituationClient.Images"), System.Drawing.Image))
        Me.gSituationClient.Location = New System.Drawing.Point(12, 60)
        Me.gSituationClient.Name = "gSituationClient"
        Me.gSituationClient.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gSituationClient.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gSituationClient.PreviewInfo.ZoomFactor = 75.0R
        Me.gSituationClient.PrintInfo.PageSettings = CType(resources.GetObject("gSituationClient.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gSituationClient.Size = New System.Drawing.Size(1004, 418)
        Me.gSituationClient.TabIndex = 0
        Me.gSituationClient.Text = "C1TrueDBGrid4"
        Me.gSituationClient.PropBag = resources.GetString("gSituationClient.PropBag")
        '
        'fSituationClient
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1028, 566)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fSituationClient"
        Me.Text = "fSituationClient"
        Me.Panel.ResumeLayout(False)
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.tLibelleSituationClient, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodeSituationClient, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gSituationClient, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents bSupprimerSituationClient As C1.Win.C1Input.C1Button
    Friend WithEvents tLibelleSituationClient As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents bAjouterSituationClient As C1.Win.C1Input.C1Button
    Friend WithEvents tCodeSituationClient As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents gSituationClient As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents Label1 As System.Windows.Forms.Label
End Class
