# 🚀 GUIDE DE DÉVELOPPEMENT - PHARMA2000 MODERNE

## 🎯 **DÉMARRAGE RAPIDE**

### 📋 **ÉTAPE 1 : PRÉPARATION**

#### **1.1 Outils requis**
- ✅ **Visual Studio 2022** (Community/Professional/Enterprise)
- ✅ **.NET 8 SDK** 
- ✅ **SQL Server** (LocalDB/Express/Standard)
- ✅ **Git** pour le contrôle de version

#### **1.2 Extensions Visual Studio recommandées**
- **XAML Styler** - Formatage XAML
- **Productivity Power Tools** - Outils de productivité
- **CodeMaid** - Nettoyage de code
- **GitLens** - Intégration Git avancée

### 📁 **ÉTAPE 2 : CRÉATION DES PROJETS**

#### **2.1 Lancer le script de création**
```bash
# Exécuter le script de création de structure
CREER_PROJET_MODERNE.bat
```

#### **2.2 Créer les projets Visual Studio**

**Dans Visual Studio 2022 :**

1. **PharmaModerne.UI** (WPF Application)
   - Framework : .NET 8
   - Template : WPF Application

2. **PharmaModerne.Core** (Class Library)
   - Framework : .NET 8
   - Template : Class Library

3. **PharmaModerne.Data** (Class Library)
   - Framework : .NET 8
   - Template : Class Library

4. **PharmaModerne.Services** (Class Library)
   - Framework : .NET 8
   - Template : Class Library

5. **PharmaModerne.Shared** (Class Library)
   - Framework : .NET 8
   - Template : Class Library

6. **PharmaModerne.Tests** (Test Project)
   - Framework : .NET 8
   - Template : xUnit Test Project

### 📦 **ÉTAPE 3 : PACKAGES NUGET**

#### **3.1 PharmaModerne.UI**
```xml
<PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
<PackageReference Include="MaterialDesignColors" Version="2.1.4" />
<PackageReference Include="Prism.Unity" Version="8.1.97" />
<PackageReference Include="Prism.Wpf" Version="8.1.97" />
<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
```

#### **3.2 PharmaModerne.Data**
```xml
<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />
```

#### **3.3 PharmaModerne.Core**
```xml
<PackageReference Include="AutoMapper" Version="12.0.1" />
<PackageReference Include="FluentValidation" Version="11.8.0" />
<PackageReference Include="MediatR" Version="12.2.0" />
<PackageReference Include="Serilog" Version="3.1.1" />
```

### 🏗️ **ÉTAPE 4 : ARCHITECTURE DE BASE**

#### **4.1 Modèles de base (PharmaModerne.Shared)**

**Models/BaseEntity.cs**
```csharp
public abstract class BaseEntity
{
    public int Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; }
    public string UpdatedBy { get; set; }
    public bool IsDeleted { get; set; }
}
```

**Models/Client.cs**
```csharp
public class Client : BaseEntity
{
    public string CodeClient { get; set; }
    public string Nom { get; set; }
    public string Prenom { get; set; }
    public string Telephone { get; set; }
    public string Email { get; set; }
    public string Adresse { get; set; }
    public DateTime? DateNaissance { get; set; }
    
    // Navigation properties
    public virtual ICollection<Vente> Ventes { get; set; }
}
```

#### **4.2 Context de base (PharmaModerne.Data)**

**Context/PharmaContext.cs**
```csharp
public class PharmaContext : DbContext
{
    public PharmaContext(DbContextOptions<PharmaContext> options) : base(options) { }
    
    public DbSet<Client> Clients { get; set; }
    public DbSet<Article> Articles { get; set; }
    public DbSet<Vente> Ventes { get; set; }
    public DbSet<Fournisseur> Fournisseurs { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(PharmaContext).Assembly);
        base.OnModelCreating(modelBuilder);
    }
}
```

#### **4.3 Interface principale (PharmaModerne.UI)**

**MainWindow.xaml**
```xml
<Window x:Class="PharmaModerne.UI.MainWindow"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    
    <materialDesign:DialogHost>
        <materialDesign:DrawerHost>
            <materialDesign:DrawerHost.LeftDrawerContent>
                <!-- Menu de navigation -->
            </materialDesign:DrawerHost.LeftDrawerContent>
            
            <Grid>
                <!-- Contenu principal -->
            </Grid>
        </materialDesign:DrawerHost>
    </materialDesign:DialogHost>
</Window>
```

### 🎨 **ÉTAPE 5 : DESIGN MODERNE**

#### **5.1 Thème Material Design**

**App.xaml**
```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <materialDesign:BundledTheme BaseTheme="Light" 
                                       PrimaryColor="Blue" 
                                       SecondaryColor="Teal" />
            <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

#### **5.2 Styles personnalisés**

**Styles/CustomStyles.xaml**
```xml
<ResourceDictionary>
    <!-- Styles pour les cartes -->
    <Style x:Key="ModuleCard" TargetType="materialDesign:Card">
        <Setter Property="Margin" Value="16"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
    </Style>
    
    <!-- Styles pour les boutons -->
    <Style x:Key="PrimaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Height" Value="40"/>
        <Setter Property="Margin" Value="8"/>
    </Style>
</ResourceDictionary>
```

### 🔧 **ÉTAPE 6 : SERVICES ET INJECTION**

#### **6.1 Configuration des services**

**ServiceConfiguration.cs**
```csharp
public static class ServiceConfiguration
{
    public static IServiceCollection ConfigureServices(this IServiceCollection services)
    {
        // Entity Framework
        services.AddDbContext<PharmaContext>(options =>
            options.UseSqlServer(connectionString));
        
        // Repositories
        services.AddScoped<IClientRepository, ClientRepository>();
        services.AddScoped<IArticleRepository, ArticleRepository>();
        
        // Services
        services.AddScoped<IClientService, ClientService>();
        services.AddScoped<IVenteService, VenteService>();
        
        // AutoMapper
        services.AddAutoMapper(typeof(MappingProfile));
        
        return services;
    }
}
```

### 📱 **ÉTAPE 7 : PREMIER MODULE - CLIENTS**

#### **7.1 Vue Client moderne**

**Views/Clients/ClientListView.xaml**
```xml
<UserControl>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Barre de recherche avec scanner -->
        <materialDesign:Card Grid.Row="0" Margin="16">
            <StackPanel Orientation="Horizontal" Margin="16">
                <TextBox x:Name="SearchBox" 
                         materialDesign:HintAssist.Hint="Rechercher ou scanner un client..."
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Width="300"/>
                <Button Content="SCANNER" 
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Margin="8,0"/>
            </StackPanel>
        </materialDesign:Card>
        
        <!-- Liste des clients -->
        <DataGrid Grid.Row="1" 
                  ItemsSource="{Binding Clients}"
                  Style="{StaticResource MaterialDesignDataGrid}"/>
    </Grid>
</UserControl>
```

### 🎯 **ÉTAPE 8 : INTÉGRATION SCANNER**

#### **8.1 Service Scanner**

**Services/ScannerService.cs**
```csharp
public class ScannerService : IScannerService
{
    public event EventHandler<string> CodeScanned;
    
    public bool DetectScanner(string input, TimeSpan inputTime)
    {
        // Logique de détection scanner (< 100ms entre caractères)
        return inputTime.TotalMilliseconds < 100;
    }
    
    public void ProcessScannerInput(string code)
    {
        CodeScanned?.Invoke(this, code.Trim().ToUpper());
    }
}
```

## 🚀 **DÉMARRAGE DU DÉVELOPPEMENT**

### 📅 **Planning suggéré**

#### **Semaine 1 : Setup et fondations**
1. ✅ Créer la structure des projets
2. ✅ Configurer Entity Framework
3. ✅ Implémenter les modèles de base
4. ✅ Créer l'interface principale

#### **Semaine 2 : Module Clients**
1. ✅ CRUD complet des clients
2. ✅ Intégration scanner
3. ✅ Recherche avancée
4. ✅ Interface moderne

#### **Semaine 3 : Module Articles**
1. ✅ Gestion des articles
2. ✅ Scanner de codes à barres
3. ✅ Gestion des stocks
4. ✅ Alertes automatiques

### 🎉 **PRÊT À COMMENCER !**

Exécutez le script `CREER_PROJET_MODERNE.bat` et suivez ce guide pour créer votre PHARMA2000 moderne avec une interface élégante et toutes les fonctionnalités existantes !

---
**Bonne chance pour votre projet ! 🚀**
