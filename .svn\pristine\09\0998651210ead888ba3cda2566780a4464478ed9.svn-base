﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fEtatDesFactures
    Dim Initialisation As Boolean = False

    Dim StrSQL As String = ""

    Dim cmdVente As New SqlCommand
    Dim daVente As New SqlDataAdapter
    Dim dsVente As New DataSet

    Public Source As String = ""

    Dim TotalExonore As Double = 0.0
    Dim TotalBaseTVA As Double = 0.0
    Dim TotalHT As Double = 0.0
    Dim TotalTVA As Double = 0.0
    Dim TotalTTC As Double = 0.0

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If

    End Sub

    Public Sub init()

        dtpDebut.Text = "01/" + Date.Today.Month.ToString.PadLeft(2, "0") + "/" + Date.Now.Year.ToString
        dtpFin.Text = NombreDesJoursDuMois(Date.Today.Month, Date.Today.Year).ToString + "/" + Date.Today.Month.ToString.PadLeft(2, "0") + "/" + Date.Now.Year.ToString

        'If Date.Today.Month <> 2 Then
        '    dtpFin.Text = "30/" + Date.Today.Month.ToString.PadLeft(2, "0") + "/" + Date.Now.Year.ToString
        'Else
        '    dtpFin.Text = "29/" + Date.Today.Month.ToString + "/" + Date.Now.Year.ToString
        'End If

        dtpDebut.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDebut.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpFin.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpFin.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDebut.Focus()

        Initialisation = False

        If ModeADMIN = "ADMIN" Then
            gbTotal.Visible = True
        Else
            gbTotal.Visible = False
        End If

        AfficherVentes()

    End Sub

    Public Sub AfficherVentes()
        Dim I As Integer
        Dim Cond As String = " VENTE.NumeroFacture<> '' "

        Dim NombreVente As Integer = 0

        If (dsVente.Tables.IndexOf("VENTES") > -1) Then
            dsVente.Tables("VENTES").Clear()
        End If

        'Composer la condition de la requête    
        If dtpDebut.Text <> "" And dtpDebut.Text.Length = 10 Then
            Cond += " AND VENTE.Date>'" + dtpDebut.Text + "'"
        End If

        If dtpFin.Text <> "" And dtpFin.Text.Length = 10 Then
            Cond += " AND VENTE.Date<='" + dtpFin.Text.Substring(0, 10) + " 23:59:59' "
        End If

        If dtpDebut.Text = "" Or dtpFin.Text = "" Then
            Cond = " YEAR(VENTE.Date)=1"
        End If

        If Initialisation = False Then
            cmdVente.CommandText = "SELECT TOP(0) "
            Initialisation = True
        Else
            cmdVente.CommandText = "SELECT "
        End If

        cmdVente.CommandText += " VENTE.Date," + _
                               " VENTE.NumeroVente," + _
                               " (SELECT SUM(VENTE_DETAILS.PrixTTC) FROM VENTE_DETAILS WHERE VENTE_DETAILS.NumeroVente=VENTE.NumeroVente AND VENTE_DETAILS.TVA=0) AS Exonore," + _
                               " (SELECT SUM(VENTE_DETAILS.PrixTTC) FROM VENTE_DETAILS WHERE VENTE_DETAILS.NumeroVente=VENTE.NumeroVente AND VENTE_DETAILS.TVA<>0) AS BaseTVA18," + _
                               " VENTE.TotalHT," + _
                               " VENTE.TVA," + _
                               " VENTE.TotalTTC," + _
                               " CLIENT.Nom," + _
                               " VENTE.NumeroFacture ,'' AS Vide " + _
                               " FROM VENTE " + _
                               " LEFT OUTER JOIN VENTE_DETAILS ON VENTE.NumeroVente =VENTE_DETAILS.NumeroVente " + _
                               " LEFT OUTER JOIN CLIENT ON VENTE.CodeClient=CLIENT.CodeClient " + _
                               " WHERE " + Cond + _
                               " GROUP BY VENTE.DATE,VENTE.NumeroVente,VENTE.TotalHT,VENTE.TVA,VENTE.TotalTTC,CLIENT.Nom,VENTE.NumeroFacture "

        cmdVente.Connection = ConnectionServeur
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "VENTES")

        With gVentes
            .Columns.Clear()
            .DataSource = dsVente
            .DataMember = "VENTES"
            .Rebind(False)
            .Columns("Date").Caption = "Date"
            .Columns("Exonore").Caption = "Exonéré"
            .Columns("BaseTVA18").Caption = "Base TVA18"
            .Columns("TotalHT").Caption = "Total HT"
            .Columns("TVA").Caption = "TVA"
            .Columns("TotalTTC").Caption = "Total TTC"
            .Columns("Nom").Caption = "Client"
            .Columns("NumeroFacture").Caption = "Numéro Facture"
            .Columns("Vide").Caption = ""


            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Date").Width = 100            '
            .Splits(0).DisplayColumns("Exonore").Width = 100
            .Splits(0).DisplayColumns("Exonore").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("BaseTVA18").Width = 120
            .Splits(0).DisplayColumns("BaseTVA18").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalHT").Width = 120
            .Splits(0).DisplayColumns("TotalHT").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TVA").Width = 120
            .Splits(0).DisplayColumns("TVA").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("TotalTTC").Width = 120
            .Splits(0).DisplayColumns("TotalTTC").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Nom").Width = 200
            .Splits(0).DisplayColumns("Nom").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("NumeroFacture").Width = 80
            '.Splits(0).DisplayColumns("NumeroVente").Width = 80

            .Splits(0).DisplayColumns("NumeroVente").Visible = False

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gVentes)
        End With

        TotalExonore = 0
        TotalBaseTVA = 0
        TotalHT = 0
        TotalTVA = 0
        TotalTTC = 0
        NombreVente = 0

        For I = 0 To dsVente.Tables("VENTES").Rows.Count - 1
            If dsVente.Tables("VENTES").Rows(I).Item("Exonore").ToString <> "" Then
                TotalExonore = TotalExonore + dsVente.Tables("VENTES").Rows(I).Item("Exonore")
            End If
            If dsVente.Tables("VENTES").Rows(I).Item("BaseTVA18").ToString <> "" Then
                TotalBaseTVA = TotalBaseTVA + dsVente.Tables("VENTES").Rows(I).Item("BaseTVA18")
            End If
            If dsVente.Tables("VENTES").Rows(I).Item("TotalHT").ToString <> "" Then
                TotalHT = TotalHT + dsVente.Tables("VENTES").Rows(I).Item("TotalHT")
            End If
            If dsVente.Tables("VENTES").Rows(I).Item("TVA").ToString <> "" Then
                TotalTVA = TotalTVA + dsVente.Tables("VENTES").Rows(I).Item("TVA")
            End If
            If dsVente.Tables("VENTES").Rows(I).Item("TotalTTC").ToString <> "" Then
                TotalTTC = TotalTTC + dsVente.Tables("VENTES").Rows(I).Item("TotalTTC")
            End If

            If dsVente.Tables("VENTES").Rows(I).Item("NumeroVente").ToString <> "" Then
                NombreVente = NombreVente + 1
            End If

        Next

        lExonore.Text = TotalExonore.ToString("### ### ##0.000")
        lBaseTVA.Text = TotalBaseTVA.ToString("### ### ##0.000")
        lTotalHT.Text = TotalHT.ToString("### ### ##0.000")
        lTotalTTC.Text = TotalTTC.ToString("### ### ##0.000")
        lTVA.Text = TotalTVA.ToString("### ### ##0.000")

        lNombreDesVentes.Text = NombreVente

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherVentes()
            dtpFin.Focus()
        End If
    End Sub


    Private Sub dtpDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDebut.TextChanged

    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherVentes()
        End If
    End Sub

    Private Sub dtpFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpFin.TextChanged

    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        Dim CondCrystal As String = ""
        Dim Somme_Echeance As Double = 0.0
        Dim StrSQLSolde As String = ""

        CondCrystal = "1=1 "

        If dsVente.Tables("VENTES").Rows.Count = 0 Then
            MsgBox("Liste vide !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        If dtpDebut.Text <> "" Then
            CondCrystal = CondCrystal + " AND ({Vue_EtatJournalDesFactures.Date} > Date('" + dtpDebut.Text + "') OR {Vue_EtatJournalDesFactures.Date}=Date('" + dtpDebut.Text + "'))"
        End If

        If dtpFin.Text <> "" Then
            CondCrystal = CondCrystal + " AND ({Vue_EtatJournalDesFactures.Date} < Date('" + dtpFin.Text + "') OR {Vue_EtatJournalDesFactures.Date}=Date('" + dtpFin.Text + "'))"
        End If


        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression de l'état des factures" Then
                num = I
            End If
        Next

        CR.FileName = Application.StartupPath + "\EtatDesFactures.rpt"

        CR.SetParameterValue("pPharmacie", Pharmacie)
        CR.SetParameterValue("Debut", dtpDebut.Text)
        CR.SetParameterValue("Fin", dtpFin.Text)
        CR.SetParameterValue("TotalExonore", TotalExonore)
        CR.SetParameterValue("TotalBaseTVA", TotalBaseTVA)

        CR.SetParameterValue("TVA", TotalTVA)
        CR.SetParameterValue("TotalHT", TotalHT)
        CR.SetParameterValue("TotalTTC", TotalTTC)

        CR.SetParameterValue("NombreDesVentes", lNombreDesVentes.Text)

        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
        For Each tbCurrent In CR.Database.Tables
            tliCurrent = tbCurrent.LogOnInfo
            With tliCurrent.ConnectionInfo
                .ServerName = NomServeur
                .UserID = NomUtilisateurSQL
                .Password = MotDePasseSQL
                .DatabaseName = NomBase
            End With
            tbCurrent.ApplyLogOnInfo(tliCurrent)
        Next tbCurrent
        CR.RecordSelectionFormula = CondCrystal
        Dim MyViewer As New fViewer
        MyViewer.CRViewer.ReportSource = CR
        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
        fMain.Tab.SelectedTab.Text = "Impression de l'état des factures"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If
    End Sub

    Private Sub gVentes_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gVentes.Click

    End Sub

    Private Sub gVentes_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gVentes.KeyUp
        If e.KeyCode = Keys.F1 Then
            Dim MyVenteAffiche As New fVenteJusteAffichage
            MyVenteAffiche.NumeroVente = gVentes(gVentes.Row, "NumeroVente")
            MyVenteAffiche.ShowDialog()
            MyVenteAffiche.Close()
            MyVenteAffiche.Dispose()
        End If
    End Sub
End Class