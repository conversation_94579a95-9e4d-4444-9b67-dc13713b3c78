﻿<?xml version="1.0" encoding="utf-8"?>
<Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
  <EntityContainerMapping StorageEntityContainer="pharmaModelStoreContainer" CdmEntityContainer="BusinessManagementEntities">
    <EntitySetMapping Name="VENTE_NUMERO">
      <EntityTypeMapping TypeName="BusinessManagementModel.VENTE_NUMERO">
        <MappingFragment StoreEntitySet="VENTE_NUMERO">
          <ScalarProperty Name="NumeroVente" ColumnName="NumeroVente" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="VENTE">
      <EntityTypeMapping TypeName="BusinessManagementModel.VENTE">
        <MappingFragment StoreEntitySet="VENTE">
          <ScalarProperty Name="IDFacturationClient" ColumnName="IDFacturationClient" />
          <ScalarProperty Name="DateEcheance" ColumnName="DateEcheance" />
          <ScalarProperty Name="NumeroCheque" ColumnName="NumeroCheque" />
          <ScalarProperty Name="NomInscritSurLeCheque" ColumnName="NomInscritSurLeCheque" />
          <ScalarProperty Name="CodeAppareillage" ColumnName="CodeAppareillage" />
          <ScalarProperty Name="APCI" ColumnName="APCI" />
          <ScalarProperty Name="OMF" ColumnName="OMF" />
          <ScalarProperty Name="DureeTraitement" ColumnName="DureeTraitement" />
          <ScalarProperty Name="NumeroBonAchat" ColumnName="NumeroBonAchat" />
          <ScalarProperty Name="NumeroPriseEnCharge" ColumnName="NumeroPriseEnCharge" />
          <ScalarProperty Name="Vider" ColumnName="Vider" />
          <ScalarProperty Name="Totaliseur" ColumnName="Totaliseur" />
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
          <ScalarProperty Name="Libelle5" ColumnName="Libelle5" />
          <ScalarProperty Name="Libelle4" ColumnName="Libelle4" />
          <ScalarProperty Name="Libelle3" ColumnName="Libelle3" />
          <ScalarProperty Name="Libelle2" ColumnName="Libelle2" />
          <ScalarProperty Name="Libelle1" ColumnName="Libelle1" />
          <ScalarProperty Name="IdentifiantCNAMMedecin" ColumnName="IdentifiantCNAMMedecin" />
          <ScalarProperty Name="Appareillage" ColumnName="Appareillage" />
          <ScalarProperty Name="PriseEnCharge" ColumnName="PriseEnCharge" />
          <ScalarProperty Name="TiersPayant" ColumnName="TiersPayant" />
          <ScalarProperty Name="LibelleLienDeParente" ColumnName="LibelleLienDeParente" />
          <ScalarProperty Name="CodeLienDeParente" ColumnName="CodeLienDeParente" />
          <ScalarProperty Name="DateNaissance" ColumnName="DateNaissance" />
          <ScalarProperty Name="Rang" ColumnName="Rang" />
          <ScalarProperty Name="NomMalade" ColumnName="NomMalade" />
          <ScalarProperty Name="CodeMutuelle" ColumnName="CodeMutuelle" />
          <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
          <ScalarProperty Name="Note" ColumnName="Note" />
          <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
          <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
          <ScalarProperty Name="DateOrdonnance" ColumnName="DateOrdonnance" />
          <ScalarProperty Name="Recu" ColumnName="Recu" />
          <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
          <ScalarProperty Name="CodeMedecinPrescripteur" ColumnName="CodeMedecinPrescripteur" />
          <ScalarProperty Name="CodeMedecinFamille" ColumnName="CodeMedecinFamille" />
          <ScalarProperty Name="CodeDeFamille" ColumnName="CodeDeFamille" />
          <ScalarProperty Name="CodeAPCI" ColumnName="CodeAPCI" />
          <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
          <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
          <ScalarProperty Name="Timbre" ColumnName="Timbre" />
          <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
          <ScalarProperty Name="TVA" ColumnName="TVA" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="NumeroVente" ColumnName="NumeroVente" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="VENTE_DETAILS">
      <EntityTypeMapping TypeName="BusinessManagementModel.VENTE_DETAILS">
        <MappingFragment StoreEntitySet="VENTE_DETAILS">
          <ScalarProperty Name="MontantCNAM" ColumnName="MontantCNAM" />
          <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
          <ScalarProperty Name="DureeTraitement" ColumnName="DureeTraitement" />
          <ScalarProperty Name="TarifDeReference" ColumnName="TarifDeReference" />
          <ScalarProperty Name="AccordPrealable" ColumnName="AccordPrealable" />
          <ScalarProperty Name="PriseEnCharge" ColumnName="PriseEnCharge" />
          <ScalarProperty Name="DateDePeremption" ColumnName="DateDePeremption" />
          <ScalarProperty Name="Stock" ColumnName="Stock" />
          <ScalarProperty Name="Honoraire" ColumnName="Honoraire" />
          <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
          <ScalarProperty Name="TVA" ColumnName="TVA" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="PrixTTC" ColumnName="PrixTTC" />
          <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
          <ScalarProperty Name="PrixHT" ColumnName="PrixHT" />
          <ScalarProperty Name="PrixAchat" ColumnName="PrixAchat" />
          <ScalarProperty Name="Qte" ColumnName="Qte" />
          <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
          <ScalarProperty Name="Ordre" ColumnName="Ordre" />
          <ScalarProperty Name="NumeroLotArticle" ColumnName="NumeroLotArticle" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="NumeroVente" ColumnName="NumeroVente" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="COMMANDE">
      <EntityTypeMapping TypeName="BusinessManagementModel.COMMANDE">
        <MappingFragment StoreEntitySet="COMMANDE">
          <ScalarProperty Name="TypeCommande" ColumnName="TypeCommande" />
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
          <ScalarProperty Name="Note" ColumnName="Note" />
          <ScalarProperty Name="CodeFournisseur" ColumnName="CodeFournisseur" />
          <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
          <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
          <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="NumeroCommande" ColumnName="NumeroCommande" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="COMMANDE_DETAILS">
      <EntityTypeMapping TypeName="BusinessManagementModel.COMMANDE_DETAILS">
        <MappingFragment StoreEntitySet="COMMANDE_DETAILS">
          <ScalarProperty Name="Ordre" ColumnName="Ordre" />
          <ScalarProperty Name="QteUnitaire" ColumnName="QteUnitaire" />
          <ScalarProperty Name="QteACommander" ColumnName="QteACommander" />
          <ScalarProperty Name="EnCours" ColumnName="EnCours" />
          <ScalarProperty Name="StockAlerte" ColumnName="StockAlerte" />
          <ScalarProperty Name="TotalTTCAchat" ColumnName="TotalTTCAchat" />
          <ScalarProperty Name="TVA" ColumnName="TVA" />
          <ScalarProperty Name="PrixAchatHT" ColumnName="PrixAchatHT" />
          <ScalarProperty Name="DatePeremption" ColumnName="DatePeremption" />
          <ScalarProperty Name="Stock" ColumnName="Stock" />
          <ScalarProperty Name="Qte" ColumnName="Qte" />
          <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="NumeroCommande" ColumnName="NumeroCommande" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="SIMULATION_STOCK">
      <EntityTypeMapping TypeName="BusinessManagementModel.SIMULATION_STOCK">
        <MappingFragment StoreEntitySet="SIMULATION_STOCK">
          <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
          <ScalarProperty Name="CodeCategorie" ColumnName="CodeCategorie" />
          <ScalarProperty Name="TotalVTTC" ColumnName="TotalVTTC" />
          <ScalarProperty Name="TotalATTC" ColumnName="TotalATTC" />
          <ScalarProperty Name="TotalAHT" ColumnName="TotalAHT" />
          <ScalarProperty Name="DateImpression" ColumnName="DateImpression" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="NumeroSimulation" ColumnName="NumeroSimulation" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="SIMULATION_STOCK_DETAILS">
      <EntityTypeMapping TypeName="BusinessManagementModel.SIMULATION_STOCK_DETAILS">
        <MappingFragment StoreEntitySet="SIMULATION_STOCK_DETAILS">
          <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
          <ScalarProperty Name="TotalAchatTTC" ColumnName="TotalAchatTTC" />
          <ScalarProperty Name="TotalAchatHT" ColumnName="TotalAchatHT" />
          <ScalarProperty Name="PrixVenteTTC" ColumnName="PrixVenteTTC" />
          <ScalarProperty Name="PrixAchatTTC" ColumnName="PrixAchatTTC" />
          <ScalarProperty Name="PrixAchatHT" ColumnName="PrixAchatHT" />
          <ScalarProperty Name="Qte" ColumnName="Qte" />
          <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="NumeroSimulation" ColumnName="NumeroSimulation" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="ACHAT">
      <EntityTypeMapping TypeName="BusinessManagementModel.ACHAT">
        <MappingFragment StoreEntitySet="ACHAT">
          <ScalarProperty Name="Autre" ColumnName="Autre" />
          <ScalarProperty Name="ValeurVenteTTC" ColumnName="ValeurVenteTTC" />
          <ScalarProperty Name="DateBlFacture" ColumnName="DateBlFacture" />
          <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
          <ScalarProperty Name="NumeroBL_Facture" ColumnName="NumeroBL/Facture" />
          <ScalarProperty Name="Note" ColumnName="Note" />
          <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
          <ScalarProperty Name="CodeFournisseur" ColumnName="CodeFournisseur" />
          <ScalarProperty Name="Timbre" ColumnName="Timbre" />
          <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="TVA" ColumnName="TVA" />
          <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="NumeroAchat" ColumnName="NumeroAchat" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="ACHAT_DETAILS">
      <EntityTypeMapping TypeName="BusinessManagementModel.ACHAT_DETAILS">
        <MappingFragment StoreEntitySet="ACHAT_DETAILS">
          <ScalarProperty Name="QuantiteUnitaire" ColumnName="QuantiteUnitaire" />
          <ScalarProperty Name="DatePeremption" ColumnName="DatePeremption" />
          <ScalarProperty Name="TVA" ColumnName="TVA" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="PrixVenteTTC" ColumnName="PrixVenteTTC" />
          <ScalarProperty Name="TotalAchatHT" ColumnName="TotalAchatHT" />
          <ScalarProperty Name="PrixAchatHT" ColumnName="PrixAchatHT" />
          <ScalarProperty Name="Stock" ColumnName="Stock" />
          <ScalarProperty Name="Qte" ColumnName="Qte" />
          <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
          <ScalarProperty Name="Ordre" ColumnName="Ordre" />
          <ScalarProperty Name="NumeroLotArticle" ColumnName="NumeroLotArticle" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="NumeroAchat" ColumnName="NumeroAchat" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
  </EntityContainerMapping>
</Mapping>