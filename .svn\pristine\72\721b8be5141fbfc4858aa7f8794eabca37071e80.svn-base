﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fSpecialiteMedecin
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fSpecialiteMedecin))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.bSupprimer = New C1.Win.C1Input.C1Button()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.lTest = New System.Windows.Forms.Label()
        Me.tLibelleSpecialite = New C1.Win.C1Input.C1TextBox()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.bAjouter = New C1.Win.C1Input.C1Button()
        Me.tCodeSpecialte = New C1.Win.C1Input.C1TextBox()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.gSpecialite = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Panel.SuspendLayout()
        Me.GroupBox4.SuspendLayout()
        CType(Me.tLibelleSpecialite, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodeSpecialte, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gSpecialite, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.Label1)
        Me.Panel.Controls.Add(Me.bSupprimer)
        Me.Panel.Controls.Add(Me.GroupBox4)
        Me.Panel.Controls.Add(Me.gSpecialite)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1028, 566)
        Me.Panel.TabIndex = 2
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(912, 12)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(104, 45)
        Me.bQuitter.TabIndex = 87
        Me.bQuitter.Text = "Fermer             F12   "
        Me.bQuitter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("HandelGotDLig", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(12, 18)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(897, 33)
        Me.Label1.TabIndex = 86
        Me.Label1.Text = "LISTE DES SPECIALITES"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bSupprimer
        '
        Me.bSupprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bSupprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bSupprimer.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler2
        Me.bSupprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bSupprimer.Location = New System.Drawing.Point(916, 492)
        Me.bSupprimer.Name = "bSupprimer"
        Me.bSupprimer.Size = New System.Drawing.Size(100, 45)
        Me.bSupprimer.TabIndex = 1
        Me.bSupprimer.Text = "Supprimer"
        Me.bSupprimer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bSupprimer.UseVisualStyleBackColor = True
        Me.bSupprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox4
        '
        Me.GroupBox4.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox4.BackColor = System.Drawing.Color.Transparent
        Me.GroupBox4.Controls.Add(Me.lTest)
        Me.GroupBox4.Controls.Add(Me.tLibelleSpecialite)
        Me.GroupBox4.Controls.Add(Me.Label14)
        Me.GroupBox4.Controls.Add(Me.bAjouter)
        Me.GroupBox4.Controls.Add(Me.tCodeSpecialte)
        Me.GroupBox4.Controls.Add(Me.Label15)
        Me.GroupBox4.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupBox4.Location = New System.Drawing.Point(12, 469)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(900, 85)
        Me.GroupBox4.TabIndex = 0
        Me.GroupBox4.TabStop = False
        '
        'lTest
        '
        Me.lTest.AutoSize = True
        Me.lTest.Location = New System.Drawing.Point(140, 36)
        Me.lTest.Name = "lTest"
        Me.lTest.Size = New System.Drawing.Size(0, 13)
        Me.lTest.TabIndex = 82
        '
        'tLibelleSpecialite
        '
        Me.tLibelleSpecialite.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tLibelleSpecialite.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tLibelleSpecialite.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tLibelleSpecialite.Location = New System.Drawing.Point(137, 52)
        Me.tLibelleSpecialite.Name = "tLibelleSpecialite"
        Me.tLibelleSpecialite.Size = New System.Drawing.Size(165, 18)
        Me.tLibelleSpecialite.TabIndex = 1
        Me.tLibelleSpecialite.Tag = Nothing
        Me.tLibelleSpecialite.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tLibelleSpecialite.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label14
        '
        Me.Label14.AutoSize = True
        Me.Label14.BackColor = System.Drawing.Color.Transparent
        Me.Label14.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label14.Location = New System.Drawing.Point(45, 54)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(86, 13)
        Me.Label14.TabIndex = 12
        Me.Label14.Text = "Libellé Spécialité"
        '
        'bAjouter
        '
        Me.bAjouter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bAjouter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAjouter.Image = Global.Pharma2000Premium.My.Resources.Resources.nouveau
        Me.bAjouter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAjouter.Location = New System.Drawing.Point(794, 23)
        Me.bAjouter.Name = "bAjouter"
        Me.bAjouter.Size = New System.Drawing.Size(100, 45)
        Me.bAjouter.TabIndex = 2
        Me.bAjouter.Text = "Ajouter"
        Me.bAjouter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAjouter.UseVisualStyleBackColor = True
        Me.bAjouter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tCodeSpecialte
        '
        Me.tCodeSpecialte.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.tCodeSpecialte.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodeSpecialte.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCodeSpecialte.Location = New System.Drawing.Point(137, 14)
        Me.tCodeSpecialte.Name = "tCodeSpecialte"
        Me.tCodeSpecialte.Size = New System.Drawing.Size(165, 18)
        Me.tCodeSpecialte.TabIndex = 0
        Me.tCodeSpecialte.Tag = Nothing
        Me.tCodeSpecialte.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.tCodeSpecialte.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.BackColor = System.Drawing.Color.Transparent
        Me.Label15.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label15.Location = New System.Drawing.Point(52, 16)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(79, 13)
        Me.Label15.TabIndex = 8
        Me.Label15.Text = "Code Spécialté"
        '
        'gSpecialite
        '
        Me.gSpecialite.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gSpecialite.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gSpecialite.GroupByCaption = "Drag a column header here to group by that column"
        Me.gSpecialite.Images.Add(CType(resources.GetObject("gSpecialite.Images"), System.Drawing.Image))
        Me.gSpecialite.Location = New System.Drawing.Point(12, 69)
        Me.gSpecialite.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gSpecialite.Name = "gSpecialite"
        Me.gSpecialite.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gSpecialite.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gSpecialite.PreviewInfo.ZoomFactor = 75.0R
        Me.gSpecialite.PrintInfo.PageSettings = CType(resources.GetObject("gSpecialite.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gSpecialite.Size = New System.Drawing.Size(1004, 394)
        Me.gSpecialite.TabIndex = 2
        Me.gSpecialite.Text = "C1TrueDBGrid4"
        Me.gSpecialite.PropBag = resources.GetString("gSpecialite.PropBag")
        '
        'fSpecialiteMedecin
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1028, 566)
        Me.Controls.Add(Me.Panel)
        Me.Font = New System.Drawing.Font("Calibri", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Name = "fSpecialiteMedecin"
        Me.Text = "fSpecialite"
        Me.Panel.ResumeLayout(False)
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.tLibelleSpecialite, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodeSpecialte, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gSpecialite, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents bSupprimer As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents lTest As System.Windows.Forms.Label
    Friend WithEvents tLibelleSpecialite As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents bAjouter As C1.Win.C1Input.C1Button
    Friend WithEvents tCodeSpecialte As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents gSpecialite As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents Label1 As System.Windows.Forms.Label
End Class
