﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fUpdateArticle
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fUpdateArticle))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.rdbCode = New System.Windows.Forms.RadioButton()
        Me.rdbDateCirculaire = New System.Windows.Forms.RadioButton()
        Me.rdbDesignation = New System.Windows.Forms.RadioButton()
        Me.lNombreArticle = New System.Windows.Forms.Label()
        Me.GroupeJauge = New System.Windows.Forms.GroupBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.ProgressBar = New System.Windows.Forms.ProgressBar()
        Me.lArticleEnCours = New System.Windows.Forms.Label()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.rbChangPCT = New System.Windows.Forms.RadioButton()
        Me.rdbArticlesAbsents = New System.Windows.Forms.RadioButton()
        Me.rdbChangemenstPrixCNAM = New System.Windows.Forms.RadioButton()
        Me.rdbChangementsCNAM = New System.Windows.Forms.RadioButton()
        Me.rdbTous = New System.Windows.Forms.RadioButton()
        Me.rdbNouveauxArticles = New System.Windows.Forms.RadioButton()
        Me.rdbChangementsPrix = New System.Windows.Forms.RadioButton()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.bQuitter = New C1.Win.C1Input.C1Button()
        Me.bAccepterLesModifications = New C1.Win.C1Input.C1Button()
        Me.bCocherTous = New C1.Win.C1Input.C1Button()
        Me.bDecocherTous = New C1.Win.C1Input.C1Button()
        Me.bImprimer = New C1.Win.C1Input.C1Button()
        Me.gArticles = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.CR = New Pharma2000Premium.EtatPharma2000PremiumUpdate()
        Me.Panel.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        Me.GroupeJauge.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.Panel.Controls.Add(Me.Label9)
        Me.Panel.Controls.Add(Me.Label4)
        Me.Panel.Controls.Add(Me.Label8)
        Me.Panel.Controls.Add(Me.Label3)
        Me.Panel.Controls.Add(Me.Label6)
        Me.Panel.Controls.Add(Me.Label2)
        Me.Panel.Controls.Add(Me.GroupBox1)
        Me.Panel.Controls.Add(Me.lNombreArticle)
        Me.Panel.Controls.Add(Me.GroupeJauge)
        Me.Panel.Controls.Add(Me.GroupBox2)
        Me.Panel.Controls.Add(Me.Label5)
        Me.Panel.Controls.Add(Me.bQuitter)
        Me.Panel.Controls.Add(Me.bAccepterLesModifications)
        Me.Panel.Controls.Add(Me.bCocherTous)
        Me.Panel.Controls.Add(Me.bDecocherTous)
        Me.Panel.Controls.Add(Me.bImprimer)
        Me.Panel.Controls.Add(Me.gArticles)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(1024, 576)
        Me.Panel.TabIndex = 6
        '
        'Label9
        '
        Me.Label9.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label9.AutoSize = True
        Me.Label9.Location = New System.Drawing.Point(595, 551)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(182, 13)
        Me.Label9.TabIndex = 79
        Me.Label9.Text = "Changement CNAM / Article abscent"
        '
        'Label4
        '
        Me.Label4.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label4.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(128, Byte), Integer))
        Me.Label4.Location = New System.Drawing.Point(550, 551)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(39, 13)
        Me.Label4.TabIndex = 79
        '
        'Label8
        '
        Me.Label8.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label8.AutoSize = True
        Me.Label8.Location = New System.Drawing.Point(595, 534)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(106, 13)
        Me.Label8.TabIndex = 79
        Me.Label8.Text = "Changement des prix"
        '
        'Label3
        '
        Me.Label3.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label3.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.Label3.Location = New System.Drawing.Point(550, 534)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(39, 13)
        Me.Label3.TabIndex = 79
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label6.AutoSize = True
        Me.Label6.Location = New System.Drawing.Point(595, 517)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(72, 13)
        Me.Label6.TabIndex = 78
        Me.Label6.Text = "Nouvel article"
        '
        'Label2
        '
        Me.Label2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label2.BackColor = System.Drawing.Color.Cyan
        Me.Label2.Location = New System.Drawing.Point(550, 517)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(39, 13)
        Me.Label2.TabIndex = 78
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.Controls.Add(Me.rdbCode)
        Me.GroupBox1.Controls.Add(Me.rdbDateCirculaire)
        Me.GroupBox1.Controls.Add(Me.rdbDesignation)
        Me.GroupBox1.Location = New System.Drawing.Point(18, 95)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(994, 32)
        Me.GroupBox1.TabIndex = 8
        Me.GroupBox1.TabStop = False
        '
        'rdbCode
        '
        Me.rdbCode.AutoSize = True
        Me.rdbCode.Location = New System.Drawing.Point(14, 10)
        Me.rdbCode.Name = "rdbCode"
        Me.rdbCode.Size = New System.Drawing.Size(89, 17)
        Me.rdbCode.TabIndex = 0
        Me.rdbCode.TabStop = True
        Me.rdbCode.Text = "Trie par Code"
        Me.rdbCode.UseVisualStyleBackColor = True
        '
        'rdbDateCirculaire
        '
        Me.rdbDateCirculaire.AutoSize = True
        Me.rdbDateCirculaire.Location = New System.Drawing.Point(341, 10)
        Me.rdbDateCirculaire.Name = "rdbDateCirculaire"
        Me.rdbDateCirculaire.Size = New System.Drawing.Size(145, 17)
        Me.rdbDateCirculaire.TabIndex = 2
        Me.rdbDateCirculaire.TabStop = True
        Me.rdbDateCirculaire.Text = "Trie par date du circulaire"
        Me.rdbDateCirculaire.UseVisualStyleBackColor = True
        '
        'rdbDesignation
        '
        Me.rdbDesignation.AutoSize = True
        Me.rdbDesignation.Location = New System.Drawing.Point(182, 10)
        Me.rdbDesignation.Name = "rdbDesignation"
        Me.rdbDesignation.Size = New System.Drawing.Size(118, 17)
        Me.rdbDesignation.TabIndex = 1
        Me.rdbDesignation.TabStop = True
        Me.rdbDesignation.Text = "Trie par désignation"
        Me.rdbDesignation.UseVisualStyleBackColor = True
        '
        'lNombreArticle
        '
        Me.lNombreArticle.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lNombreArticle.AutoSize = True
        Me.lNombreArticle.Location = New System.Drawing.Point(865, 534)
        Me.lNombreArticle.Name = "lNombreArticle"
        Me.lNombreArticle.Size = New System.Drawing.Size(16, 13)
        Me.lNombreArticle.TabIndex = 77
        Me.lNombreArticle.Text = "---"
        '
        'GroupeJauge
        '
        Me.GroupeJauge.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupeJauge.Controls.Add(Me.Label1)
        Me.GroupeJauge.Controls.Add(Me.Label7)
        Me.GroupeJauge.Controls.Add(Me.ProgressBar)
        Me.GroupeJauge.Controls.Add(Me.lArticleEnCours)
        Me.GroupeJauge.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupeJauge.ForeColor = System.Drawing.SystemColors.ControlText
        Me.GroupeJauge.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.GroupeJauge.Location = New System.Drawing.Point(234, 234)
        Me.GroupeJauge.Name = "GroupeJauge"
        Me.GroupeJauge.Size = New System.Drawing.Size(523, 94)
        Me.GroupeJauge.TabIndex = 76
        Me.GroupeJauge.TabStop = False
        Me.GroupeJauge.Visible = False
        '
        'Label1
        '
        Me.Label1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(8, 12)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(509, 25)
        Me.Label1.TabIndex = 73
        Me.Label1.Text = "Recherche des changements dans les produits, veuillez patienter...."
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label7
        '
        Me.Label7.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.Location = New System.Drawing.Point(15, 42)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(126, 15)
        Me.Label7.TabIndex = 10
        Me.Label7.Text = "Article en cours  :  "
        '
        'ProgressBar
        '
        Me.ProgressBar.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ProgressBar.Location = New System.Drawing.Point(23, 65)
        Me.ProgressBar.Name = "ProgressBar"
        Me.ProgressBar.Size = New System.Drawing.Size(473, 17)
        Me.ProgressBar.TabIndex = 72
        Me.ProgressBar.Value = 50
        Me.ProgressBar.Visible = False
        '
        'lArticleEnCours
        '
        Me.lArticleEnCours.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lArticleEnCours.BackColor = System.Drawing.Color.Transparent
        Me.lArticleEnCours.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lArticleEnCours.Location = New System.Drawing.Point(178, 42)
        Me.lArticleEnCours.Name = "lArticleEnCours"
        Me.lArticleEnCours.Size = New System.Drawing.Size(318, 15)
        Me.lArticleEnCours.TabIndex = 33
        Me.lArticleEnCours.Text = "-------------"
        '
        'GroupBox2
        '
        Me.GroupBox2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox2.Controls.Add(Me.rbChangPCT)
        Me.GroupBox2.Controls.Add(Me.rdbArticlesAbsents)
        Me.GroupBox2.Controls.Add(Me.rdbChangemenstPrixCNAM)
        Me.GroupBox2.Controls.Add(Me.rdbChangementsCNAM)
        Me.GroupBox2.Controls.Add(Me.rdbTous)
        Me.GroupBox2.Controls.Add(Me.rdbNouveauxArticles)
        Me.GroupBox2.Controls.Add(Me.rdbChangementsPrix)
        Me.GroupBox2.Location = New System.Drawing.Point(18, 62)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(1111, 32)
        Me.GroupBox2.TabIndex = 7
        Me.GroupBox2.TabStop = False
        '
        'rbChangPCT
        '
        Me.rbChangPCT.AutoSize = True
        Me.rbChangPCT.Location = New System.Drawing.Point(860, 9)
        Me.rbChangPCT.Name = "rbChangPCT"
        Me.rbChangPCT.Size = New System.Drawing.Size(109, 17)
        Me.rbChangPCT.TabIndex = 6
        Me.rbChangPCT.TabStop = True
        Me.rbChangPCT.Text = "Changement PCT"
        Me.rbChangPCT.UseVisualStyleBackColor = True
        '
        'rdbArticlesAbsents
        '
        Me.rdbArticlesAbsents.AutoSize = True
        Me.rdbArticlesAbsents.Location = New System.Drawing.Point(991, 9)
        Me.rdbArticlesAbsents.Name = "rdbArticlesAbsents"
        Me.rdbArticlesAbsents.Size = New System.Drawing.Size(99, 17)
        Me.rdbArticlesAbsents.TabIndex = 5
        Me.rdbArticlesAbsents.TabStop = True
        Me.rdbArticlesAbsents.Text = "Articles absents"
        Me.rdbArticlesAbsents.UseVisualStyleBackColor = True
        '
        'rdbChangemenstPrixCNAM
        '
        Me.rdbChangemenstPrixCNAM.AutoSize = True
        Me.rdbChangemenstPrixCNAM.Location = New System.Drawing.Point(679, 9)
        Me.rdbChangemenstPrixCNAM.Name = "rdbChangemenstPrixCNAM"
        Me.rdbChangemenstPrixCNAM.Size = New System.Drawing.Size(153, 17)
        Me.rdbChangemenstPrixCNAM.TabIndex = 4
        Me.rdbChangemenstPrixCNAM.TabStop = True
        Me.rdbChangemenstPrixCNAM.Text = "Changements CNAM + Prix"
        Me.rdbChangemenstPrixCNAM.UseVisualStyleBackColor = True
        '
        'rdbChangementsCNAM
        '
        Me.rdbChangementsCNAM.AutoSize = True
        Me.rdbChangementsCNAM.Location = New System.Drawing.Point(508, 9)
        Me.rdbChangementsCNAM.Name = "rdbChangementsCNAM"
        Me.rdbChangementsCNAM.Size = New System.Drawing.Size(124, 17)
        Me.rdbChangementsCNAM.TabIndex = 3
        Me.rdbChangementsCNAM.TabStop = True
        Me.rdbChangementsCNAM.Text = "Changements CNAM"
        Me.rdbChangementsCNAM.UseVisualStyleBackColor = True
        '
        'rdbTous
        '
        Me.rdbTous.AutoSize = True
        Me.rdbTous.Location = New System.Drawing.Point(14, 10)
        Me.rdbTous.Name = "rdbTous"
        Me.rdbTous.Size = New System.Drawing.Size(132, 17)
        Me.rdbTous.TabIndex = 0
        Me.rdbTous.TabStop = True
        Me.rdbTous.Text = "Tous les changements"
        Me.rdbTous.UseVisualStyleBackColor = True
        '
        'rdbNouveauxArticles
        '
        Me.rdbNouveauxArticles.AutoSize = True
        Me.rdbNouveauxArticles.Location = New System.Drawing.Point(182, 10)
        Me.rdbNouveauxArticles.Name = "rdbNouveauxArticles"
        Me.rdbNouveauxArticles.Size = New System.Drawing.Size(110, 17)
        Me.rdbNouveauxArticles.TabIndex = 1
        Me.rdbNouveauxArticles.TabStop = True
        Me.rdbNouveauxArticles.Text = "Nouveaux articles"
        Me.rdbNouveauxArticles.UseVisualStyleBackColor = True
        '
        'rdbChangementsPrix
        '
        Me.rdbChangementsPrix.AutoSize = True
        Me.rdbChangementsPrix.Location = New System.Drawing.Point(341, 9)
        Me.rdbChangementsPrix.Name = "rdbChangementsPrix"
        Me.rdbChangementsPrix.Size = New System.Drawing.Size(124, 17)
        Me.rdbChangementsPrix.TabIndex = 2
        Me.rdbChangementsPrix.TabStop = True
        Me.rdbChangementsPrix.Text = "Changement des prix"
        Me.rdbChangementsPrix.UseVisualStyleBackColor = True
        '
        'Label5
        '
        Me.Label5.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label5.Location = New System.Drawing.Point(18, 18)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(883, 41)
        Me.Label5.TabIndex = 75
        Me.Label5.Text = "Mise à jour PHARMA2000 Premium"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bQuitter
        '
        Me.bQuitter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bQuitter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bQuitter.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bQuitter.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bQuitter.Location = New System.Drawing.Point(907, 12)
        Me.bQuitter.Name = "bQuitter"
        Me.bQuitter.Size = New System.Drawing.Size(105, 45)
        Me.bQuitter.TabIndex = 74
        Me.bQuitter.Text = "Fermer            F12"
        Me.bQuitter.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bQuitter.UseVisualStyleBackColor = True
        Me.bQuitter.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bAccepterLesModifications
        '
        Me.bAccepterLesModifications.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bAccepterLesModifications.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAccepterLesModifications.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAccepterLesModifications.Location = New System.Drawing.Point(12, 517)
        Me.bAccepterLesModifications.Name = "bAccepterLesModifications"
        Me.bAccepterLesModifications.Size = New System.Drawing.Size(167, 45)
        Me.bAccepterLesModifications.TabIndex = 61
        Me.bAccepterLesModifications.Text = "Accepter les modifications"
        Me.bAccepterLesModifications.UseVisualStyleBackColor = True
        Me.bAccepterLesModifications.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bCocherTous
        '
        Me.bCocherTous.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bCocherTous.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bCocherTous.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bCocherTous.Location = New System.Drawing.Point(185, 517)
        Me.bCocherTous.Name = "bCocherTous"
        Me.bCocherTous.Size = New System.Drawing.Size(86, 45)
        Me.bCocherTous.TabIndex = 5
        Me.bCocherTous.Text = "Cocher tous"
        Me.bCocherTous.UseVisualStyleBackColor = True
        Me.bCocherTous.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bDecocherTous
        '
        Me.bDecocherTous.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bDecocherTous.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bDecocherTous.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bDecocherTous.Location = New System.Drawing.Point(277, 517)
        Me.bDecocherTous.Name = "bDecocherTous"
        Me.bDecocherTous.Size = New System.Drawing.Size(97, 45)
        Me.bDecocherTous.TabIndex = 6
        Me.bDecocherTous.Text = "Décocher tous"
        Me.bDecocherTous.UseVisualStyleBackColor = True
        Me.bDecocherTous.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bImprimer
        '
        Me.bImprimer.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.bImprimer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bImprimer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bImprimer.Location = New System.Drawing.Point(380, 517)
        Me.bImprimer.Name = "bImprimer"
        Me.bImprimer.Size = New System.Drawing.Size(86, 45)
        Me.bImprimer.TabIndex = 7
        Me.bImprimer.Text = "Imprimer"
        Me.bImprimer.UseVisualStyleBackColor = True
        Me.bImprimer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'gArticles
        '
        Me.gArticles.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gArticles.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.gArticles.FilterBar = True
        Me.gArticles.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gArticles.GroupByCaption = "Drag a column header here to group by that column"
        Me.gArticles.Images.Add(CType(resources.GetObject("gArticles.Images"), System.Drawing.Image))
        Me.gArticles.LinesPerRow = 2
        Me.gArticles.Location = New System.Drawing.Point(12, 133)
        Me.gArticles.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gArticles.Name = "gArticles"
        Me.gArticles.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gArticles.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gArticles.PreviewInfo.ZoomFactor = 75.0R
        Me.gArticles.PrintInfo.PageSettings = CType(resources.GetObject("gArticles.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gArticles.Size = New System.Drawing.Size(1000, 375)
        Me.gArticles.TabIndex = 2
        Me.gArticles.Text = "C1TrueDBGrid1"
        Me.gArticles.PropBag = resources.GetString("gArticles.PropBag")
        '
        'fUpdateArticle
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1024, 576)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fUpdateArticle"
        Me.Text = "fUpdateArticle"
        Me.Panel.ResumeLayout(False)
        Me.Panel.PerformLayout()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.GroupeJauge.ResumeLayout(False)
        Me.GroupeJauge.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.gArticles, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents bAccepterLesModifications As C1.Win.C1Input.C1Button
    Friend WithEvents bCocherTous As C1.Win.C1Input.C1Button
    Friend WithEvents bDecocherTous As C1.Win.C1Input.C1Button
    Friend WithEvents bImprimer As C1.Win.C1Input.C1Button
    Friend WithEvents gArticles As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents bQuitter As C1.Win.C1Input.C1Button
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents rdbArticlesAbsents As System.Windows.Forms.RadioButton
    Friend WithEvents rdbChangemenstPrixCNAM As System.Windows.Forms.RadioButton
    Friend WithEvents rdbChangementsCNAM As System.Windows.Forms.RadioButton
    Friend WithEvents rdbTous As System.Windows.Forms.RadioButton
    Friend WithEvents rdbNouveauxArticles As System.Windows.Forms.RadioButton
    Friend WithEvents rdbChangementsPrix As System.Windows.Forms.RadioButton
    Friend WithEvents GroupeJauge As System.Windows.Forms.GroupBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents ProgressBar As System.Windows.Forms.ProgressBar
    Friend WithEvents lArticleEnCours As System.Windows.Forms.Label
    Friend WithEvents CR As Pharma2000Premium.EtatPharma2000PremiumUpdate
    Friend WithEvents lNombreArticle As System.Windows.Forms.Label
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents rdbCode As System.Windows.Forms.RadioButton
    Friend WithEvents rdbDesignation As System.Windows.Forms.RadioButton
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents rdbDateCirculaire As System.Windows.Forms.RadioButton
    Friend WithEvents rbChangPCT As System.Windows.Forms.RadioButton
End Class
