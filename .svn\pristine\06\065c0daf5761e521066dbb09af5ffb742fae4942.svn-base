﻿Imports System.IO
Imports System.Data.SqlClient
Imports System.Windows.Forms

Public Class fAjouterCategorie
    Dim cmd As New SqlCommand
    Public Enregistrer As Boolean = False
    Public Valeur As String = ""

    Private Sub bEnregistrer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bEnregistrer.Click
        Dim StrSQL As String = ""
        Dim StrMajLOT As String = ""
        Dim DernierCodeCategorie As Integer = 0

        StrSQL = " SELECT max(CodeCategorie) FROM [CATEGORIE]"

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL

        Try
            DernierCodeCategorie = cmd.ExecuteScalar()
            If Trim(DernierCodeCategorie) <> "" Then
                DernierCodeCategorie = DernierCodeCategorie + 1
            Else
                DernierCodeCategorie = "0"
            End If
        Catch ex As Exception
            Console.WriteLine(ex.Message)
            DernierCodeCategorie = "0"
        End Try

        StrMajLOT = "INSERT INTO CATEGORIE (""CodeCategorie"",""LibelleCategorie"") " + _
                    " VALUES(" + DernierCodeCategorie.ToString + ",'" + tCategorie.Text + "')"

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrMajLOT
        Try
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        Enregistrer = True
        Me.Hide()
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Enregistrer = False
        Me.Hide()
    End Sub

    Private Sub fAjouterForme_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        tCategorie.Value = Valeur
    End Sub
End Class