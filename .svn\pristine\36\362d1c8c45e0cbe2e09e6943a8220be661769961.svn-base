//------------------------------------------------------------------------------
// <auto-generated>
//    Ce code a été généré à partir d'un modèle.
//
//    Des modifications manuelles apportées à ce fichier peuvent conduire à un comportement inattendu de votre application.
//    Les modifications manuelles apportées à ce fichier sont remplacées si le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Data.BusinessManagement
{
    using System;
    using System.Collections.Generic;
    
    public partial class INVENTAIRE_DETAILS
    {
        public string NumeroInventaire { get; set; }
        public string CodeArticle { get; set; }
        public string NumeroLot { get; set; }
        public string CodeABarre { get; set; }
        public string Designation { get; set; }
        public int CodeForme { get; set; }
        public string Rayon { get; set; }
        public int StockInitial { get; set; }
        public int StockActuel { get; set; }
        public decimal PrixAchatTTC { get; set; }
        public decimal TotalAchatTTC { get; set; }
        public decimal PrixVenteTTC { get; set; }
        public Nullable<int> NumLigne { get; set; }
        public Nullable<System.DateTime> DatePeremption { get; set; }
    
        public virtual LOT_ARTICLE LOT_ARTICLE { get; set; }
    }
}
