﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Public Class fMutuelle

    Dim cmdMutuelle As New SqlCommand
    Dim daMutuelle As New SqlDataAdapter
    Dim dsMutuelle As New DataSet

    Dim x As Integer

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "116" And bAjouterMutuelle.Enabled = True Then
            bAjouterMutuelle_Click(sender, e)
        End If
        If argument = "117" And bRechercher.Enabled = True Then
            bRechercher_Click(sender, e)
        End If
        If argument = "118" And bSuprimerMutuelle.Enabled = True Then
            bSuprimerMutuelle_Click(sender, e)
        End If
        If argument = "119" And bModifierMutuelle.Enabled = True Then
            bModifierMutuelle_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If

    End Sub

    Private Sub Panel1_Paint(ByVal sender As System.Object, ByVal e As System.Windows.Forms.PaintEventArgs) Handles Panel.Paint
        'AfficherMutuelle()
    End Sub

    Public Sub init()
        Dim StrSQL As String = ""
        Dim StrSQL1 As String = ""

        'charger les villes
        StrSQL1 = "SELECT CodeVille,NomVille FROM VILLE WHERE SupprimeVille=0 ORDER BY NomVille ASC"
        cmdMutuelle.Connection = ConnectionServeur
        cmdMutuelle.CommandText = StrSQL1
        daMutuelle = New SqlDataAdapter(cmdMutuelle)
        daMutuelle.Fill(dsMutuelle, "VILLE")
        cmbVille.DataSource = dsMutuelle.Tables("VILLE")
        cmbVille.ValueMember = "CodeVille"
        cmbVille.DisplayMember = "NomVille"
        cmbVille.ColumnHeaders = False
        cmbVille.Splits(0).DisplayColumns("CodeVille").Visible = False
        cmbVille.Splits(0).DisplayColumns("NomVille").Width = 10
        cmbVille.ExtendRightColumn = True

        'chargement des noms Mutuelles
        StrSQL = "SELECT DISTINCT NomMutuelle FROM MUTUELLE AS MUTUELLE_NOM ORDER BY NomMutuelle ASC"
        cmdMutuelle.Connection = ConnectionServeur
        cmdMutuelle.CommandText = StrSQL
        daMutuelle = New SqlDataAdapter(cmdMutuelle)
        daMutuelle.Fill(dsMutuelle, "MUTUELLE_NOM")
        cmbNomMutuelle.DataSource = dsMutuelle.Tables("MUTUELLE_NOM")
        cmbNomMutuelle.DisplayMember = "NomMutuelle"
        cmbNomMutuelle.ColumnHeaders = False
        cmbNomMutuelle.Splits(0).DisplayColumns("NomMutuelle").Width = 10
        cmbNomMutuelle.ExtendRightColumn = True

        'chargement des Code Mutuelles
        StrSQL = "SELECT DISTINCT CodeMutuelle FROM MUTUELLE AS MUTUELLE_CODE ORDER BY CodeMutuelle ASC"
        cmdMutuelle.Connection = ConnectionServeur
        cmdMutuelle.CommandText = StrSQL
        daMutuelle = New SqlDataAdapter(cmdMutuelle)
        daMutuelle.Fill(dsMutuelle, "MUTUELLE_CODE")
        cmbCode.DataSource = dsMutuelle.Tables("MUTUELLE_CODE")
        cmbCode.DisplayMember = "CodeMutuelle"
        cmbCode.ColumnHeaders = False
        cmbCode.Splits(0).DisplayColumns("CodeMutuelle").Width = 10
        cmbCode.ExtendRightColumn = True

        x = 0
        AfficherMutuelle()
    End Sub

    Private Sub bRechercher_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bRechercher.Click
        cmbCode.Focus()
    End Sub
    Public Sub AfficherMutuelle()
        Dim I As Integer
        Dim Cond As String = "1=1"
        Dim TotalSolde As Double = 0.0

        If cmbCode.Text <> "" Then
            If IsNumeric(cmbCode.Text) Then
                Cond += " AND CodeMutuelle = '" + cmbCode.Text.Replace("'", "''") + "'"
            Else
                Cond += " AND CodeMutuelle = -1"
            End If

        End If

        If cmbNomMutuelle.Text <> "" Then
            Cond += " AND NomMutuelle LIKE '%" + cmbNomMutuelle.Text.Replace("'", "''") + "%'"
        End If

        If (dsMutuelle.Tables.IndexOf("MUTUELLE") > -1) Then
            dsMutuelle.Tables("MUTUELLE").Clear()
        End If
        'Composer les conditions de la requête        

        If cmbVille.Text <> "" Then
            Cond += " AND VILLE.NomVille LIKE '" + cmbVille.Text.Replace("'", "''") + "%'"
        End If

        If chbSolde.Checked = True Then
            Cond += " AND ((select SUM(MontantMutuelle) FROM VENTE WHERE VENTE.CodeMutuelle =MUTUELLE.CodeMutuelle) - " + _
                    " (CASE WHEN (SELECT SUM(Montant) FROM REGLEMENT_MUTUELLE " + _
                    "WHERE REGLEMENT_MUTUELLE.CodeMutuelle =MUTUELLE.CodeMutuelle) IS NULL THEN 0 ELSE " + _
                    "(SELECT SUM(Montant) FROM REGLEMENT_MUTUELLE " + _
                    "WHERE REGLEMENT_MUTUELLE.CodeMutuelle=MUTUELLE.CodeMutuelle)END)>0)"
        End If
        cmdMutuelle.CommandText = " SELECT " + _
                                    " CodeMutuelle, " + _
                                    " NomMutuelle, " + _
                                    " PriseEnCharge, " + _
                                    " Tel, " + _
                                    " Fax, " + _
                                    " VILLE.NomVille " + _
                                    " FROM MUTUELLE " + _
                                    " LEFT OUTER JOIN VILLE ON MUTUELLE.CodeVille=VILLE.CodeVille WHERE NomMutuelle <> 'COMPTOIR' AND " + Cond + _
                                    " ORDER BY CodeMutuelle"

        cmdMutuelle.Connection = ConnectionServeur
        daMutuelle = New SqlDataAdapter(cmdMutuelle)
        daMutuelle.Fill(dsMutuelle, "MUTUELLE")

        With gMutuelle
            .Columns.Clear()
            .DataSource = dsMutuelle
            .DataMember = "MUTUELLE"
            .Rebind(False)
            .Columns("CodeMutuelle").Caption = "Code Mutuelle"
            .Columns("NomMutuelle").Caption = "Désignation"
            .Columns("PriseEnCharge").Caption = "Prise en charge"
            .Columns("Tel").Caption = "Téléphone"
            .Columns("Fax").Caption = "Fax"
            .Columns("NomVille").Caption = "Ville"


            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("CodeMutuelle").Width = 80
            .Splits(0).DisplayColumns("CodeMutuelle").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("NomMutuelle").Width = 300
            .Splits(0).DisplayColumns("PriseEnCharge").Width = 100
            .Splits(0).DisplayColumns("Tel").Width = 100
            .Splits(0).DisplayColumns("Fax").Width = 100
            .Splits(0).DisplayColumns("NomVille").Width = 100
            .Splits(0).DisplayColumns("NomVille").Style.HorizontalAlignment = AlignHorzEnum.Center
            

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gMutuelle)
        End With

        Dim Col As New C1.Win.C1TrueDBGrid.C1DataColumn
        Dim dc As C1.Win.C1TrueDBGrid.C1DisplayColumn

        With gMutuelle
            .Columns.Insert(0, Col)
            Col.Caption = "Solde"
            dc = .Splits(0).DisplayColumns.Item("Solde")
            dc.Width = 50
            .Splits(0).DisplayColumns(6).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            .Splits(0).DisplayColumns(6).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Far
            dc.Visible = True
            .Rebind(True)
            .Columns("Solde").NumberFormat = "### ### ##0.000"
        End With

        I = 0
        Do While I < gMutuelle.RowCount
            TotalSolde = TotalSolde + gMutuelle(I, ("Solde"))
            I += 1
        Loop

        lTotalSolde.Text = TotalSolde.ToString("### ### ##0.000")

        lNbreDesMutuelles.Text = dsMutuelle.Tables("MUTUELLE").Rows.Count.ToString + " Mutuelles"

        gMutuelle.MoveRelative(x)
    End Sub
    Private Sub bViderLesChamps_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bViderLesChamps.Click
        cmbVille.Text = ""
        chbSolde.Checked = 0
        cmbCode.Text = ""
        cmbNomMutuelle.Text = ""
        AfficherMutuelle()
    End Sub
    Private Sub bAjouterMutuelle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterMutuelle.Click

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(4, "AJOUT_MUTUELLE") = "False" Then
            Exit Sub
        End If

        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = "SELECT DemandeMotDePasse FROM [POINTS_CONTROLE] WHERE CodePointControle LIKE 'AJOUT_MUTUELLE' AND CodeModule=4"

        'Try
        '    DemandePotDePasse = cmd.ExecuteScControleDAcces(4, "SUPPRESSION_REGLEMENT_MUTUELLE") alar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'If DemandePotDePasse = True Then
        '    '------------------------------ demande du mot de passe
        '    Dim myMotDePasse As New fMotDePasse
        '    myMotDePasse.ShowDialog()

        '    ConfirmerEnregistrer = fMotDePasse.Confirmer
        '    CodeOperateur = fMotDePasse.CodeOperateur

        '    myMotDePasse.Dispose()
        '    myMotDePasse.Close()

        '    If ConfirmerEnregistrer = False Then
        '        Exit Sub
        '    End If

        '    cmd.Connection = ConnectionServeur
        '    cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'AJOUT_MUTUELLE' AND CodeUtilisateur='" + CodeOperateur + "'"

        '    Try
        '        If cmd.ExecuteScalar() < 1 Then
        '            MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
        '            Exit Sub
        '        End If
        '    Catch ex As Exception
        '        Console.WriteLine(ex.Message)
        '    End Try
        'End If

        Dim MyFicheMutuelle = New fFicheMutuelle
        x = gMutuelle.Row
        MyFicheMutuelle.ajoutmodif = "A"
        MyFicheMutuelle.Init()
        MyFicheMutuelle.ShowDialog()
        MyFicheMutuelle.Close()
        MyFicheMutuelle.Dispose()
        AfficherMutuelle()
        'Dim I As Integer
        'For I = 0 To fMain.Tab.TabPages.Count - 1
        '    If fMain.Tab.TabPages(I).Text = "Création d'un nouveau Mutuelle" Then
        '        fMain.Tab.TabPages(I).Show()
        '        Exit Sub
        '    End If
        'Next
        'fMain.MyFicheMutuelle = New fFicheMutuelle
        'x = gMutuelle.Row
        'fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        'fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        'fMain.Tab.SelectedTab.Controls.Add(fMain.MyFicheMutuelle.Panel)
        'fMain.Tab.SelectedTab.Text = "Création d'un nouveau Mutuelle"
        'fMain.MyFicheMutuelle.ajoutmodif = "A"
        'fMain.MyFicheMutuelle.Init()
    End Sub
    Private Sub bModifierMutuelle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifierMutuelle.Click

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""

        If ControleDAcces(4, "MODIFICATION_MUTUELLE") = "False" Then
            Exit Sub
        End If

        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = "SELECT DemandeMotDePasse FROM [POINTS_CONTROLE] WHERE CodePointControle LIKE 'MODIFICATION_MUTUELLE' AND CodeModule=4"

        'Try
        '    DemandePotDePasse = cmd.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'If DemandePotDePasse = True Then
        '    '------------------------------ demande du mot de passe
        '    Dim myMotDePasse As New fMotDePasse
        '    myMotDePasse.ShowDialog()

        '    ConfirmerEnregistrer = fMotDePasse.Confirmer
        '    CodeOperateur = fMotDePasse.CodeOperateur

        '    myMotDePasse.Dispose()
        '    myMotDePasse.Close()

        '    If ConfirmerEnregistrer = False Then
        '        Exit Sub
        '    End If

        '    cmd.Connection = ConnectionServeur
        '    cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'MODIFICATION_MUTUELLE' AND CodeUtilisateur='" + CodeOperateur + "'"

        '    Try
        '        If cmd.ExecuteScalar() < 1 Then
        '            MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
        '            Exit Sub
        '        End If
        '    Catch ex As Exception
        '        Console.WriteLine(ex.Message)
        '    End Try
        'End If

        Dim MyFicheMutuelle = New fFicheMutuelle
        MyFicheMutuelle.CodeMutuelle = gMutuelle(gMutuelle.Row, "CodeMutuelle")
        MyFicheMutuelle.SoldeMutuelle = gMutuelle(gMutuelle.Row, "Solde")
        x = gMutuelle.Row
        MyFicheMutuelle.ajoutmodif = "M"
        MyFicheMutuelle.Init()
        MyFicheMutuelle.ShowDialog()
        MyFicheMutuelle.Close()
        MyFicheMutuelle.Dispose()
        AfficherMutuelle()
        'Dim I As Integer
        'For I = 0 To fMain.Tab.TabPages.Count - 1
        '    If fMain.Tab.TabPages(I).Text = "Modification du Mutuelle" Then
        '        fMain.Tab.TabPages(I).Show()
        '        Exit Sub
        '    End If
        'Next
        'fMain.MyFicheMutuelle = New fFicheMutuelle
        'fMain.MyFicheMutuelle.CodeMutuelle = gMutuelle(gMutuelle.Row, "CodeMutuelle")
        'fMain.MyFicheMutuelle.SoldeMutuelle = gMutuelle(gMutuelle.Row, "Solde")
        'x = gMutuelle.Row
        'fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        'fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        'fMain.Tab.SelectedTab.Controls.Add(fMain.MyFicheMutuelle.Panel)
        'fMain.Tab.SelectedTab.Text = "Modification du Mutuelle"
        'fMain.MyFicheMutuelle.ajoutmodif = "M"
        'fMain.MyFicheMutuelle.Init()
    End Sub
    Private Sub bSuprimerMutuelle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSuprimerMutuelle.Click

        Dim cmd As New SqlCommand
        Dim DemandePotDePasse As Boolean = False
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""


        If ControleDAcces(4, "SUPPRESSION_MUTUELLE") = "False" Then
            Exit Sub
        Else
            CodeOperateur = ControleDAcces(4, "SUPPRESSION_MUTUELLE")
        End If
        If CodeOperateur = "" Then
            CodeOperateur = CodeUtilisateur
        End If

        'cmd.Connection = ConnectionServeur
        'cmd.CommandText = "SELECT DemandeMotDePasse FROM [POINTS_CONTROLE] WHERE CodePointControle LIKE 'SUPPRESSION_MUTUELLE' AND CodeModule=4"

        'Try
        '    DemandePotDePasse = cmd.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'If DemandePotDePasse = True Then
        '    '------------------------------ demande du mot de passe
        '    Dim myMotDePasse As New fMotDePasse
        '    myMotDePasse.ShowDialog()

        '    ConfirmerEnregistrer = fMotDePasse.Confirmer
        '    CodeOperateur = fMotDePasse.CodeOperateur

        '    myMotDePasse.Dispose()
        '    myMotDePasse.Close()

        '    If ConfirmerEnregistrer = False Then
        '        Exit Sub
        '    End If

        '    cmd.Connection = ConnectionServeur
        '    cmd.CommandText = "SELECT COUNT(CodePointControle) FROM [AUTORISATION] WHERE CodePointControle LIKE 'SUPPRESSION_MUTUELLE' AND CodeUtilisateur='" + CodeOperateur + "'"

        '    Try
        '        If cmd.ExecuteScalar() < 1 Then
        '            MsgBox("Vous n'avez pas le droit d'executer cette opération !", MsgBoxStyle.Critical, "Autorisation")
        '            Exit Sub
        '        End If
        '    Catch ex As Exception
        '        Console.WriteLine(ex.Message)
        '    End Try
        'End If

        If gMutuelle.RowCount > 0 Then
            If MsgBox("Voulez vous vraiment supprimer le Mutuelle " + gMutuelle(gMutuelle.Row, "NomMutuelle") + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "DELETE FROM MUTUELLE WHERE CodeMutuelle =" + Quote(gMutuelle(gMutuelle.Row, "CodeMutuelle"))
                    cmd.ExecuteNonQuery()
                    InsertionDansLog("SUPPRESSION_MUTUELLE", "La Suppression du mutuelle " + gMutuelle(gMutuelle.Row, "NomMutuelle"), CodeOperateur, System.DateTime.Now, "MUTUELLE", System.Environment.GetEnvironmentVariable("Poste"), Environment.MachineName.ToString)
                    AfficherMutuelle()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub
    Private Sub cmbCodeClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        AfficherMutuelle()
    End Sub
    Private Sub cmbVille_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbVille.KeyUp
        'Recherche_Automatique_liste(e, cmbVille, cmbVille.Columns("NomVille"))
        If e.KeyCode = Keys.Enter Then
            cmbVille.Text = cmbVille.WillChangeToText
        Else
            cmbVille.OpenCombo()
        End If
    End Sub
    Private Sub cmbVille_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbVille.TextChanged
        AfficherMutuelle()
    End Sub

    Private Sub gMutuelle_DoubleClick(sender As Object, e As System.EventArgs) Handles gMutuelle.DoubleClick
        bModifierMutuelle_Click(sender, e)
    End Sub

    Private Sub gClient_UnboundColumnFetch(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.UnboundColumnFetchEventArgs) Handles gMutuelle.UnboundColumnFetch
        Dim y As String = ""
        y = gMutuelle(e.Row, ("CodeMutuelle"))
        e.Value = Calcule_Solde_Mutuelle(y)
    End Sub
    Public Function Calcule_Solde_Mutuelle(ByVal CodeMutuelle)
        Dim StrSQLSolde As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Somme_Vente As Double = 0.0
        Dim Somme_Reglement As Double = 0.0
        Dim Solde_Intial As Double = 0.0
        Dim difference As Double = 0.0

        ''calcul du solde mutuelle en retranchat la somme des montant des règlements de la somme des montants des ventes mutuelles  
        'StrSQLSolde = "SELECT SUM(Montant) FROM REGLEMENT_MUTUELLE LEFT OUTER JOIN MUTUELLE ON REGLEMENT_MUTUELLE.CodeMutuelle=MUTUELLE.CodeMutuelle WHERE REGLEMENT_MUTUELLE.CodeMutuelle =" + Quote(CodeMutuelle) + " AND REGLEMENT_MUTUELLE.Date>MUTUELLE.DateInitiale"
        'CmdCalcul.Connection = ConnectionServeur
        'CmdCalcul.CommandText = StrSQLSolde
        'Try
        '    Somme_Reglement = CmdCalcul.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'StrSQLSolde = "SELECT SUM(MontantMutuelle) FROM VENTE LEFT OUTER JOIN MUTUELLE ON VENTE.CodeMutuelle=MUTUELLE.CodeMutuelle WHERE VENTE.CodeMutuelle =" + Quote(CodeMutuelle) + " AND VENTE.Date>MUTUELLE.DateInitiale"
        'CmdCalcul.Connection = ConnectionServeur
        'CmdCalcul.CommandText = StrSQLSolde
        'Try
        '    Somme_Vente = CmdCalcul.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'StrSQLSolde = "SELECT SoldeInitial FROM MUTUELLE WHERE CodeMutuelle =" + Quote(CodeMutuelle)
        'CmdCalcul.Connection = ConnectionServeur
        'CmdCalcul.CommandText = StrSQLSolde
        'Try
        '    Solde_Intial = CmdCalcul.ExecuteScalar()
        'Catch ex As Exception
        '    Console.WriteLine(ex.Message)
        'End Try

        'difference = Somme_Vente - Somme_Reglement + Solde_Intial

        StrSQLSolde = "select ISNULL(SUM(Reste), 0) from RELEVE_MUTUELLE WHERE CodeMutuelle=" + Quote(CodeMutuelle.ToString)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQLSolde

        Try
            Somme_Reglement = CmdCalcul.ExecuteScalar()
        Catch ex As Exception
            Somme_Reglement = 0
        End Try


        Return (Convert.ToSingle(Somme_Reglement))
    End Function

    Private Sub chbSolde_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbSolde.CheckedChanged
        AfficherMutuelle()
    End Sub

    Private Sub cmbNomMutuelle_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbNomMutuelle.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbNomMutuelle.Text = cmbNomMutuelle.WillChangeToText
            cmbVille.Focus()
        Else
            cmbNomMutuelle.OpenCombo()
        End If
    End Sub

    Private Sub cmbNomMutuelle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbNomMutuelle.TextChanged
        AfficherMutuelle()
    End Sub

    Private Sub cmbCode_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCode.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbCode.Text = cmbCode.WillChangeToText
            cmbNomMutuelle.Focus()
        Else
            cmbCode.OpenCombo()
        End If

    End Sub

    Private Sub cmbCode_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbCode.TextChanged
        AfficherMutuelle()
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

End Class