﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fParametres
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.Panel = New System.Windows.Forms.Panel()
        Me.Label30 = New System.Windows.Forms.Label()
        Me.Label27 = New System.Windows.Forms.Label()
        Me.bAnnuler = New C1.Win.C1Input.C1Button()
        Me.tPoste = New C1.Win.C1Input.C1TextBox()
        Me.bConfirmer = New C1.Win.C1Input.C1Button()
        Me.Tab = New C1.Win.C1Command.C1DockingTab()
        Me.C1DockingTabPage1 = New C1.Win.C1Command.C1DockingTabPage()
        Me.Label28 = New System.Windows.Forms.Label()
        Me.tMatriculeFiscale = New C1.Win.C1Input.C1TextBox()
        Me.Label29 = New System.Windows.Forms.Label()
        Me.tNCNSS = New C1.Win.C1Input.C1TextBox()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.tTimbre = New C1.Win.C1Input.C1TextBox()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.tMessagederoulant = New C1.Win.C1Input.C1TextBox()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.tCodeTva = New C1.Win.C1Input.C1TextBox()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.tFax = New C1.Win.C1Input.C1TextBox()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.tTelephone = New C1.Win.C1Input.C1TextBox()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.tAdresse = New C1.Win.C1Input.C1TextBox()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.tNumeroAffiliation2 = New C1.Win.C1Input.C1TextBox()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.tNumeroAffiliation1 = New C1.Win.C1Input.C1TextBox()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.tCNAM = New C1.Win.C1Input.C1TextBox()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.tPharmacie = New C1.Win.C1Input.C1TextBox()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.tLecteurUpdate = New C1.Win.C1Input.C1TextBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.tCodePharmacien = New C1.Win.C1Input.C1TextBox()
        Me.chbActiverTransactionelle = New System.Windows.Forms.CheckBox()
        Me.chbAutoriserLesMotsDePasse = New System.Windows.Forms.CheckBox()
        Me.C1DockingTabPage2 = New C1.Win.C1Command.C1DockingTabPage()
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations = New System.Windows.Forms.CheckBox()
        Me.chbAutoriserMiseAjoursEnLigneArticles = New System.Windows.Forms.CheckBox()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.rdbPrixTTC = New System.Windows.Forms.RadioButton()
        Me.rdbDCI = New System.Windows.Forms.RadioButton()
        Me.rdbForme = New System.Windows.Forms.RadioButton()
        Me.Label19 = New System.Windows.Forms.Label()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.tMinimumdePerception = New C1.Win.C1Input.C1TextBox()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.tHonoraireTableauC = New C1.Win.C1Input.C1TextBox()
        Me.Label17 = New System.Windows.Forms.Label()
        Me.tHonoraireTableauB = New C1.Win.C1Input.C1TextBox()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.tHonoraireTableauA = New C1.Win.C1Input.C1TextBox()
        Me.C1DockingTabPage3 = New C1.Win.C1Command.C1DockingTabPage()
        Me.chbRetarancheDuStockLorsDeVenteInstance = New System.Windows.Forms.CheckBox()
        Me.chbInterdireChoisirParDesignation = New System.Windows.Forms.CheckBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.tDureeAffichageAlerte = New C1.Win.C1Input.C1TextBox()
        Me.chbPermettrePreparateursSupprimerVente = New System.Windows.Forms.CheckBox()
        Me.chbPermettreUtiliserFrigosEnVente = New System.Windows.Forms.CheckBox()
        Me.chbVerifierProduitPrisEnChargeParCNAM = New System.Windows.Forms.CheckBox()
        Me.chbInterdireLaVenteDesPerimes = New System.Windows.Forms.CheckBox()
        Me.chbInscriptionSurOrdonnancierAutomatique = New System.Windows.Forms.CheckBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.tTentatives = New C1.Win.C1Input.C1TextBox()
        Me.lMatricule = New System.Windows.Forms.Label()
        Me.tCom = New C1.Win.C1Input.C1TextBox()
        Me.chbUSB = New System.Windows.Forms.CheckBox()
        Me.chbCreationDesClientsCreditDansNiveauPreparateur = New System.Windows.Forms.CheckBox()
        Me.chbImprimerBon = New System.Windows.Forms.CheckBox()
        Me.chbTiroir = New System.Windows.Forms.CheckBox()
        Me.chbImprimanteATicket = New System.Windows.Forms.CheckBox()
        Me.chbControleNombreUnites = New System.Windows.Forms.CheckBox()
        Me.chbValiderQteEgalA1 = New System.Windows.Forms.CheckBox()
        Me.C1DockingTabPage4 = New C1.Win.C1Command.C1DockingTabPage()
        Me.lAnneeProchaine = New System.Windows.Forms.Label()
        Me.lAnneeCourant = New System.Windows.Forms.Label()
        Me.Label26 = New System.Windows.Forms.Label()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.dtpfinAnneeProchaine = New C1.Win.C1Input.C1DateEdit()
        Me.dtpDebutAnneeProchaine = New C1.Win.C1Input.C1DateEdit()
        Me.Label24 = New System.Windows.Forms.Label()
        Me.dtpFinAnneeCourant = New C1.Win.C1Input.C1DateEdit()
        Me.Label25 = New System.Windows.Forms.Label()
        Me.dtpDebutAnneeCourant = New C1.Win.C1Input.C1DateEdit()
        Me.chbQteMultiple5 = New System.Windows.Forms.CheckBox()
        Me.Label22 = New System.Windows.Forms.Label()
        Me.tNePasSortirManquantsDepuis = New C1.Win.C1Input.C1TextBox()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.tCommandeGroupeJ = New C1.Win.C1Input.C1TextBox()
        Me.C1DockingTabPage5 = New C1.Win.C1Command.C1DockingTabPage()
        Me.C1DockingTabPage6 = New C1.Win.C1Command.C1DockingTabPage()
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat = New System.Windows.Forms.CheckBox()
        Me.chbAutoriserSuppressionsDesAchatsAuxPréparateurs = New System.Windows.Forms.CheckBox()
        Me.chbAutoriserModificationsDesAchatsPréparateurs = New System.Windows.Forms.CheckBox()
        Me.GroupBox5 = New System.Windows.Forms.GroupBox()
        Me.rdbInterventionAvecUnAssistant = New System.Windows.Forms.RadioButton()
        Me.rdbNePasMettreAJourLaFicheArticleSiLePrixChange = New System.Windows.Forms.RadioButton()
        Me.Label23 = New System.Windows.Forms.Label()
        Me.tNomOrdinateurImpressionCodeABarre = New C1.Win.C1Input.C1TextBox()
        Me.Panel.SuspendLayout()
        CType(Me.tPoste, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Tab, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Tab.SuspendLayout()
        Me.C1DockingTabPage1.SuspendLayout()
        CType(Me.tMatriculeFiscale, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNCNSS, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tTimbre, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tMessagederoulant, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodeTva, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tFax, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tTelephone, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tAdresse, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNumeroAffiliation2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNumeroAffiliation1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCNAM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tPharmacie, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tLecteurUpdate, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCodePharmacien, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage2.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.tMinimumdePerception, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.tHonoraireTableauC, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tHonoraireTableauB, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tHonoraireTableauA, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage3.SuspendLayout()
        CType(Me.tDureeAffichageAlerte, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tTentatives, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCom, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage4.SuspendLayout()
        Me.GroupBox4.SuspendLayout()
        CType(Me.dtpfinAnneeProchaine, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpDebutAnneeProchaine, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpFinAnneeCourant, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dtpDebutAnneeCourant, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tNePasSortirManquantsDepuis, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tCommandeGroupeJ, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTabPage6.SuspendLayout()
        Me.GroupBox5.SuspendLayout()
        CType(Me.tNomOrdinateurImpressionCodeABarre, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.Label30)
        Me.Panel.Controls.Add(Me.Label27)
        Me.Panel.Controls.Add(Me.bAnnuler)
        Me.Panel.Controls.Add(Me.tPoste)
        Me.Panel.Controls.Add(Me.bConfirmer)
        Me.Panel.Controls.Add(Me.Tab)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(753, 516)
        Me.Panel.TabIndex = 16
        '
        'Label30
        '
        Me.Label30.Font = New System.Drawing.Font("HandelGotDLig", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label30.ForeColor = System.Drawing.Color.FromArgb(CType(CType(100, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.Label30.Location = New System.Drawing.Point(4, 8)
        Me.Label30.Name = "Label30"
        Me.Label30.Size = New System.Drawing.Size(744, 56)
        Me.Label30.TabIndex = 87
        Me.Label30.Text = "Paramétrage Pharma 2000 Premium"
        Me.Label30.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label27
        '
        Me.Label27.AutoSize = True
        Me.Label27.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label27.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label27.Location = New System.Drawing.Point(663, 272)
        Me.Label27.Name = "Label27"
        Me.Label27.Size = New System.Drawing.Size(34, 13)
        Me.Label27.TabIndex = 86
        Me.Label27.Text = "Poste"
        Me.Label27.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bAnnuler
        '
        Me.bAnnuler.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bAnnuler.Image = Global.Pharma2000Premium.My.Resources.Resources.annuler21
        Me.bAnnuler.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bAnnuler.Location = New System.Drawing.Point(625, 131)
        Me.bAnnuler.Name = "bAnnuler"
        Me.bAnnuler.Size = New System.Drawing.Size(111, 45)
        Me.bAnnuler.TabIndex = 3
        Me.bAnnuler.Text = "Annuler"
        Me.bAnnuler.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bAnnuler.UseVisualStyleBackColor = True
        Me.bAnnuler.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'tPoste
        '
        Me.tPoste.AutoSize = False
        Me.tPoste.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tPoste.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tPoste.Font = New System.Drawing.Font("Calibri", 8.25!)
        Me.tPoste.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tPoste.Location = New System.Drawing.Point(625, 292)
        Me.tPoste.Name = "tPoste"
        Me.tPoste.Size = New System.Drawing.Size(111, 19)
        Me.tPoste.TabIndex = 85
        Me.tPoste.Tag = Nothing
        Me.tPoste.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.tPoste.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'bConfirmer
        '
        Me.bConfirmer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bConfirmer.Image = Global.Pharma2000Premium.My.Resources.Resources.valider__
        Me.bConfirmer.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bConfirmer.Location = New System.Drawing.Point(625, 80)
        Me.bConfirmer.Name = "bConfirmer"
        Me.bConfirmer.Size = New System.Drawing.Size(111, 45)
        Me.bConfirmer.TabIndex = 2
        Me.bConfirmer.Text = "Confirmer"
        Me.bConfirmer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bConfirmer.UseVisualStyleBackColor = True
        Me.bConfirmer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Tab
        '
        Me.Tab.Controls.Add(Me.C1DockingTabPage1)
        Me.Tab.Controls.Add(Me.C1DockingTabPage2)
        Me.Tab.Controls.Add(Me.C1DockingTabPage3)
        Me.Tab.Controls.Add(Me.C1DockingTabPage4)
        Me.Tab.Controls.Add(Me.C1DockingTabPage5)
        Me.Tab.Controls.Add(Me.C1DockingTabPage6)
        Me.Tab.Location = New System.Drawing.Point(7, 81)
        Me.Tab.Name = "Tab"
        Me.Tab.SelectedIndex = 5
        Me.Tab.Size = New System.Drawing.Size(604, 420)
        Me.Tab.TabIndex = 0
        Me.Tab.TabsSpacing = 5
        Me.Tab.TabStyle = C1.Win.C1Command.TabStyleEnum.Office2007
        Me.Tab.VisualStyle = C1.Win.C1Command.VisualStyle.Office2007Blue
        Me.Tab.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage1
        '
        Me.C1DockingTabPage1.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.C1DockingTabPage1.Controls.Add(Me.Label28)
        Me.C1DockingTabPage1.Controls.Add(Me.tMatriculeFiscale)
        Me.C1DockingTabPage1.Controls.Add(Me.Label29)
        Me.C1DockingTabPage1.Controls.Add(Me.tNCNSS)
        Me.C1DockingTabPage1.Controls.Add(Me.Label13)
        Me.C1DockingTabPage1.Controls.Add(Me.tTimbre)
        Me.C1DockingTabPage1.Controls.Add(Me.Label14)
        Me.C1DockingTabPage1.Controls.Add(Me.tMessagederoulant)
        Me.C1DockingTabPage1.Controls.Add(Me.Label15)
        Me.C1DockingTabPage1.Controls.Add(Me.tCodeTva)
        Me.C1DockingTabPage1.Controls.Add(Me.Label10)
        Me.C1DockingTabPage1.Controls.Add(Me.tFax)
        Me.C1DockingTabPage1.Controls.Add(Me.Label11)
        Me.C1DockingTabPage1.Controls.Add(Me.tTelephone)
        Me.C1DockingTabPage1.Controls.Add(Me.Label12)
        Me.C1DockingTabPage1.Controls.Add(Me.tAdresse)
        Me.C1DockingTabPage1.Controls.Add(Me.Label9)
        Me.C1DockingTabPage1.Controls.Add(Me.tNumeroAffiliation2)
        Me.C1DockingTabPage1.Controls.Add(Me.Label8)
        Me.C1DockingTabPage1.Controls.Add(Me.tNumeroAffiliation1)
        Me.C1DockingTabPage1.Controls.Add(Me.Label7)
        Me.C1DockingTabPage1.Controls.Add(Me.tCNAM)
        Me.C1DockingTabPage1.Controls.Add(Me.Label6)
        Me.C1DockingTabPage1.Controls.Add(Me.tPharmacie)
        Me.C1DockingTabPage1.Controls.Add(Me.Label5)
        Me.C1DockingTabPage1.Controls.Add(Me.tLecteurUpdate)
        Me.C1DockingTabPage1.Controls.Add(Me.Label4)
        Me.C1DockingTabPage1.Controls.Add(Me.tCodePharmacien)
        Me.C1DockingTabPage1.Controls.Add(Me.chbActiverTransactionelle)
        Me.C1DockingTabPage1.Controls.Add(Me.chbAutoriserLesMotsDePasse)
        Me.C1DockingTabPage1.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage1.Name = "C1DockingTabPage1"
        Me.C1DockingTabPage1.Size = New System.Drawing.Size(602, 395)
        Me.C1DockingTabPage1.TabIndex = 0
        Me.C1DockingTabPage1.Text = "Entête "
        '
        'Label28
        '
        Me.Label28.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label28.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label28.Location = New System.Drawing.Point(14, 249)
        Me.Label28.Name = "Label28"
        Me.Label28.Size = New System.Drawing.Size(98, 13)
        Me.Label28.TabIndex = 88
        Me.Label28.Text = "Matricule Fiscale :"
        Me.Label28.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tMatriculeFiscale
        '
        Me.tMatriculeFiscale.AutoSize = False
        Me.tMatriculeFiscale.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMatriculeFiscale.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tMatriculeFiscale.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tMatriculeFiscale.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tMatriculeFiscale.Location = New System.Drawing.Point(118, 247)
        Me.tMatriculeFiscale.Name = "tMatriculeFiscale"
        Me.tMatriculeFiscale.Size = New System.Drawing.Size(104, 19)
        Me.tMatriculeFiscale.TabIndex = 87
        Me.tMatriculeFiscale.Tag = Nothing
        Me.tMatriculeFiscale.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label29
        '
        Me.Label29.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label29.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label29.Location = New System.Drawing.Point(14, 220)
        Me.Label29.Name = "Label29"
        Me.Label29.Size = New System.Drawing.Size(98, 13)
        Me.Label29.TabIndex = 86
        Me.Label29.Text = "N° CNSS :"
        Me.Label29.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tNCNSS
        '
        Me.tNCNSS.AutoSize = False
        Me.tNCNSS.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNCNSS.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNCNSS.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNCNSS.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tNCNSS.Location = New System.Drawing.Point(118, 218)
        Me.tNCNSS.Name = "tNCNSS"
        Me.tNCNSS.Size = New System.Drawing.Size(104, 19)
        Me.tNCNSS.TabIndex = 85
        Me.tNCNSS.Tag = Nothing
        Me.tNCNSS.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label13
        '
        Me.Label13.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label13.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label13.Location = New System.Drawing.Point(14, 311)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(98, 13)
        Me.Label13.TabIndex = 84
        Me.Label13.Text = "Timbre :"
        Me.Label13.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tTimbre
        '
        Me.tTimbre.AutoSize = False
        Me.tTimbre.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTimbre.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tTimbre.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tTimbre.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tTimbre.Location = New System.Drawing.Point(118, 309)
        Me.tTimbre.Name = "tTimbre"
        Me.tTimbre.Size = New System.Drawing.Size(104, 19)
        Me.tTimbre.TabIndex = 83
        Me.tTimbre.Tag = Nothing
        Me.tTimbre.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label14
        '
        Me.Label14.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label14.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label14.Location = New System.Drawing.Point(1, 279)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(111, 17)
        Me.Label14.TabIndex = 82
        Me.Label14.Text = "Message déroulant :"
        Me.Label14.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tMessagederoulant
        '
        Me.tMessagederoulant.AutoSize = False
        Me.tMessagederoulant.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMessagederoulant.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tMessagederoulant.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tMessagederoulant.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tMessagederoulant.Location = New System.Drawing.Point(118, 277)
        Me.tMessagederoulant.Name = "tMessagederoulant"
        Me.tMessagederoulant.Size = New System.Drawing.Size(303, 19)
        Me.tMessagederoulant.TabIndex = 81
        Me.tMessagederoulant.Tag = Nothing
        Me.tMessagederoulant.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label15
        '
        Me.Label15.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label15.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label15.Location = New System.Drawing.Point(14, 191)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(98, 13)
        Me.Label15.TabIndex = 80
        Me.Label15.Text = "CodeTVA :"
        Me.Label15.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tCodeTva
        '
        Me.tCodeTva.AutoSize = False
        Me.tCodeTva.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodeTva.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tCodeTva.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tCodeTva.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tCodeTva.Location = New System.Drawing.Point(118, 189)
        Me.tCodeTva.Name = "tCodeTva"
        Me.tCodeTva.Size = New System.Drawing.Size(104, 19)
        Me.tCodeTva.TabIndex = 79
        Me.tCodeTva.Tag = Nothing
        Me.tCodeTva.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label10
        '
        Me.Label10.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label10.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label10.Location = New System.Drawing.Point(243, 158)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(110, 13)
        Me.Label10.TabIndex = 78
        Me.Label10.Text = "Fax :"
        Me.Label10.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tFax
        '
        Me.tFax.AutoSize = False
        Me.tFax.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tFax.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tFax.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tFax.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tFax.Location = New System.Drawing.Point(356, 156)
        Me.tFax.Name = "tFax"
        Me.tFax.Size = New System.Drawing.Size(104, 19)
        Me.tFax.TabIndex = 77
        Me.tFax.Tag = Nothing
        Me.tFax.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label11
        '
        Me.Label11.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label11.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label11.Location = New System.Drawing.Point(14, 158)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(98, 13)
        Me.Label11.TabIndex = 76
        Me.Label11.Text = "Tél :"
        Me.Label11.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tTelephone
        '
        Me.tTelephone.AutoSize = False
        Me.tTelephone.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTelephone.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tTelephone.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tTelephone.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tTelephone.Location = New System.Drawing.Point(118, 156)
        Me.tTelephone.Name = "tTelephone"
        Me.tTelephone.Size = New System.Drawing.Size(104, 19)
        Me.tTelephone.TabIndex = 75
        Me.tTelephone.Tag = Nothing
        Me.tTelephone.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label12
        '
        Me.Label12.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label12.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label12.Location = New System.Drawing.Point(14, 127)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(98, 13)
        Me.Label12.TabIndex = 74
        Me.Label12.Text = "Adresse :"
        Me.Label12.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tAdresse
        '
        Me.tAdresse.AutoSize = False
        Me.tAdresse.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tAdresse.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tAdresse.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tAdresse.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tAdresse.Location = New System.Drawing.Point(118, 125)
        Me.tAdresse.Name = "tAdresse"
        Me.tAdresse.Size = New System.Drawing.Size(303, 19)
        Me.tAdresse.TabIndex = 73
        Me.tAdresse.Tag = Nothing
        Me.tAdresse.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label9
        '
        Me.Label9.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label9.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label9.Location = New System.Drawing.Point(466, 97)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(17, 13)
        Me.Label9.TabIndex = 72
        Me.Label9.Text = "/"
        Me.Label9.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tNumeroAffiliation2
        '
        Me.tNumeroAffiliation2.AutoSize = False
        Me.tNumeroAffiliation2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNumeroAffiliation2.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNumeroAffiliation2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNumeroAffiliation2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tNumeroAffiliation2.Location = New System.Drawing.Point(485, 94)
        Me.tNumeroAffiliation2.Name = "tNumeroAffiliation2"
        Me.tNumeroAffiliation2.Size = New System.Drawing.Size(41, 19)
        Me.tNumeroAffiliation2.TabIndex = 71
        Me.tNumeroAffiliation2.Tag = Nothing
        Me.tNumeroAffiliation2.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label8
        '
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label8.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label8.Location = New System.Drawing.Point(243, 96)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(110, 13)
        Me.Label8.TabIndex = 70
        Me.Label8.Text = "N° d'affiliation social :"
        Me.Label8.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tNumeroAffiliation1
        '
        Me.tNumeroAffiliation1.AutoSize = False
        Me.tNumeroAffiliation1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNumeroAffiliation1.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNumeroAffiliation1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNumeroAffiliation1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tNumeroAffiliation1.Location = New System.Drawing.Point(356, 94)
        Me.tNumeroAffiliation1.Name = "tNumeroAffiliation1"
        Me.tNumeroAffiliation1.Size = New System.Drawing.Size(104, 19)
        Me.tNumeroAffiliation1.TabIndex = 69
        Me.tNumeroAffiliation1.Tag = Nothing
        Me.tNumeroAffiliation1.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label7
        '
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label7.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label7.Location = New System.Drawing.Point(14, 96)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(98, 13)
        Me.Label7.TabIndex = 68
        Me.Label7.Text = "N° CNAM :"
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tCNAM
        '
        Me.tCNAM.AutoSize = False
        Me.tCNAM.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCNAM.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tCNAM.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tCNAM.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tCNAM.Location = New System.Drawing.Point(118, 94)
        Me.tCNAM.Name = "tCNAM"
        Me.tCNAM.Size = New System.Drawing.Size(104, 19)
        Me.tCNAM.TabIndex = 67
        Me.tCNAM.Tag = Nothing
        Me.tCNAM.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label6
        '
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label6.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label6.Location = New System.Drawing.Point(14, 64)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(98, 13)
        Me.Label6.TabIndex = 66
        Me.Label6.Text = "Pharmacie :"
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tPharmacie
        '
        Me.tPharmacie.AutoSize = False
        Me.tPharmacie.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tPharmacie.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tPharmacie.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tPharmacie.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tPharmacie.Location = New System.Drawing.Point(118, 62)
        Me.tPharmacie.Name = "tPharmacie"
        Me.tPharmacie.Size = New System.Drawing.Size(303, 19)
        Me.tPharmacie.TabIndex = 65
        Me.tPharmacie.Tag = Nothing
        Me.tPharmacie.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label5
        '
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label5.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label5.Location = New System.Drawing.Point(282, 33)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(91, 13)
        Me.Label5.TabIndex = 64
        Me.Label5.Text = "Lecteur Update :"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tLecteurUpdate
        '
        Me.tLecteurUpdate.AutoSize = False
        Me.tLecteurUpdate.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tLecteurUpdate.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tLecteurUpdate.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tLecteurUpdate.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tLecteurUpdate.Location = New System.Drawing.Point(375, 31)
        Me.tLecteurUpdate.Name = "tLecteurUpdate"
        Me.tLecteurUpdate.Size = New System.Drawing.Size(50, 19)
        Me.tLecteurUpdate.TabIndex = 63
        Me.tLecteurUpdate.Tag = Nothing
        Me.tLecteurUpdate.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label4
        '
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label4.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label4.Location = New System.Drawing.Point(14, 33)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(98, 13)
        Me.Label4.TabIndex = 62
        Me.Label4.Text = "Code Pharmacien :"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tCodePharmacien
        '
        Me.tCodePharmacien.AutoSize = False
        Me.tCodePharmacien.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCodePharmacien.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tCodePharmacien.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tCodePharmacien.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tCodePharmacien.Location = New System.Drawing.Point(118, 31)
        Me.tCodePharmacien.Name = "tCodePharmacien"
        Me.tCodePharmacien.Size = New System.Drawing.Size(50, 19)
        Me.tCodePharmacien.TabIndex = 61
        Me.tCodePharmacien.Tag = Nothing
        Me.tCodePharmacien.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'chbActiverTransactionelle
        '
        Me.chbActiverTransactionelle.BackColor = System.Drawing.Color.Transparent
        Me.chbActiverTransactionelle.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbActiverTransactionelle.Location = New System.Drawing.Point(8, 362)
        Me.chbActiverTransactionelle.Name = "chbActiverTransactionelle"
        Me.chbActiverTransactionelle.Size = New System.Drawing.Size(406, 19)
        Me.chbActiverTransactionelle.TabIndex = 60
        Me.chbActiverTransactionelle.Text = "Activer l'option transactionelle"
        Me.chbActiverTransactionelle.UseVisualStyleBackColor = False
        '
        'chbAutoriserLesMotsDePasse
        '
        Me.chbAutoriserLesMotsDePasse.BackColor = System.Drawing.Color.Transparent
        Me.chbAutoriserLesMotsDePasse.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbAutoriserLesMotsDePasse.Location = New System.Drawing.Point(8, 338)
        Me.chbAutoriserLesMotsDePasse.Name = "chbAutoriserLesMotsDePasse"
        Me.chbAutoriserLesMotsDePasse.Size = New System.Drawing.Size(232, 19)
        Me.chbAutoriserLesMotsDePasse.TabIndex = 59
        Me.chbAutoriserLesMotsDePasse.Text = "Autoriser les demandes des mots de passe"
        Me.chbAutoriserLesMotsDePasse.UseVisualStyleBackColor = False
        '
        'C1DockingTabPage2
        '
        Me.C1DockingTabPage2.Controls.Add(Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations)
        Me.C1DockingTabPage2.Controls.Add(Me.chbAutoriserMiseAjoursEnLigneArticles)
        Me.C1DockingTabPage2.Controls.Add(Me.GroupBox3)
        Me.C1DockingTabPage2.Controls.Add(Me.GroupBox2)
        Me.C1DockingTabPage2.Controls.Add(Me.GroupBox1)
        Me.C1DockingTabPage2.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage2.Name = "C1DockingTabPage2"
        Me.C1DockingTabPage2.Size = New System.Drawing.Size(602, 395)
        Me.C1DockingTabPage2.TabIndex = 1
        Me.C1DockingTabPage2.Text = "Article                                                                          " & _
    "                                                                                " & _
    "            "
        '
        'chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations
        '
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.BackColor = System.Drawing.Color.Transparent
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.ForeColor = System.Drawing.Color.Red
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.Location = New System.Drawing.Point(8, 298)
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.Name = "chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations"
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.Size = New System.Drawing.Size(406, 19)
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.TabIndex = 71
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.Text = "Autoriser le changement automatique des prix des articles des préparations"
        Me.chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations.UseVisualStyleBackColor = False
        '
        'chbAutoriserMiseAjoursEnLigneArticles
        '
        Me.chbAutoriserMiseAjoursEnLigneArticles.BackColor = System.Drawing.Color.Transparent
        Me.chbAutoriserMiseAjoursEnLigneArticles.ForeColor = System.Drawing.Color.Red
        Me.chbAutoriserMiseAjoursEnLigneArticles.Location = New System.Drawing.Point(8, 267)
        Me.chbAutoriserMiseAjoursEnLigneArticles.Name = "chbAutoriserMiseAjoursEnLigneArticles"
        Me.chbAutoriserMiseAjoursEnLigneArticles.Size = New System.Drawing.Size(232, 19)
        Me.chbAutoriserMiseAjoursEnLigneArticles.TabIndex = 70
        Me.chbAutoriserMiseAjoursEnLigneArticles.Text = "Autoriser la mise à jour en ligne des articles "
        Me.chbAutoriserMiseAjoursEnLigneArticles.UseVisualStyleBackColor = False
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.rdbPrixTTC)
        Me.GroupBox3.Controls.Add(Me.rdbDCI)
        Me.GroupBox3.Controls.Add(Me.rdbForme)
        Me.GroupBox3.Controls.Add(Me.Label19)
        Me.GroupBox3.Location = New System.Drawing.Point(330, 28)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(230, 128)
        Me.GroupBox3.TabIndex = 69
        Me.GroupBox3.TabStop = False
        Me.GroupBox3.Text = "Recherche Article"
        '
        'rdbPrixTTC
        '
        Me.rdbPrixTTC.AutoSize = True
        Me.rdbPrixTTC.ForeColor = System.Drawing.Color.Red
        Me.rdbPrixTTC.Location = New System.Drawing.Point(144, 75)
        Me.rdbPrixTTC.Name = "rdbPrixTTC"
        Me.rdbPrixTTC.Size = New System.Drawing.Size(66, 17)
        Me.rdbPrixTTC.TabIndex = 72
        Me.rdbPrixTTC.TabStop = True
        Me.rdbPrixTTC.Text = "Prix TTC"
        Me.rdbPrixTTC.UseVisualStyleBackColor = True
        '
        'rdbDCI
        '
        Me.rdbDCI.AutoSize = True
        Me.rdbDCI.ForeColor = System.Drawing.Color.Red
        Me.rdbDCI.Location = New System.Drawing.Point(81, 75)
        Me.rdbDCI.Name = "rdbDCI"
        Me.rdbDCI.Size = New System.Drawing.Size(43, 17)
        Me.rdbDCI.TabIndex = 71
        Me.rdbDCI.TabStop = True
        Me.rdbDCI.Text = "DCI"
        Me.rdbDCI.UseVisualStyleBackColor = True
        '
        'rdbForme
        '
        Me.rdbForme.AutoSize = True
        Me.rdbForme.ForeColor = System.Drawing.Color.Red
        Me.rdbForme.Location = New System.Drawing.Point(11, 75)
        Me.rdbForme.Name = "rdbForme"
        Me.rdbForme.Size = New System.Drawing.Size(54, 17)
        Me.rdbForme.TabIndex = 70
        Me.rdbForme.TabStop = True
        Me.rdbForme.Text = "Forme"
        Me.rdbForme.UseVisualStyleBackColor = True
        '
        'Label19
        '
        Me.Label19.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label19.ForeColor = System.Drawing.Color.Red
        Me.Label19.Location = New System.Drawing.Point(7, 29)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(195, 13)
        Me.Label19.TabIndex = 69
        Me.Label19.Text = "Curseur par Défaut dans le champ :"
        Me.Label19.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.tMinimumdePerception)
        Me.GroupBox2.Location = New System.Drawing.Point(8, 175)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(303, 74)
        Me.GroupBox2.TabIndex = 69
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "Minimum de Perception"
        '
        'tMinimumdePerception
        '
        Me.tMinimumdePerception.AutoSize = False
        Me.tMinimumdePerception.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tMinimumdePerception.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tMinimumdePerception.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tMinimumdePerception.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tMinimumdePerception.Location = New System.Drawing.Point(80, 31)
        Me.tMinimumdePerception.Name = "tMinimumdePerception"
        Me.tMinimumdePerception.Size = New System.Drawing.Size(119, 19)
        Me.tMinimumdePerception.TabIndex = 63
        Me.tMinimumdePerception.Tag = Nothing
        Me.tMinimumdePerception.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.Label18)
        Me.GroupBox1.Controls.Add(Me.tHonoraireTableauC)
        Me.GroupBox1.Controls.Add(Me.Label17)
        Me.GroupBox1.Controls.Add(Me.tHonoraireTableauB)
        Me.GroupBox1.Controls.Add(Me.Label16)
        Me.GroupBox1.Controls.Add(Me.tHonoraireTableauA)
        Me.GroupBox1.Location = New System.Drawing.Point(8, 28)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(303, 128)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "Honoraire de responsabilité"
        '
        'Label18
        '
        Me.Label18.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label18.ForeColor = System.Drawing.Color.Red
        Me.Label18.Location = New System.Drawing.Point(16, 89)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(98, 13)
        Me.Label18.TabIndex = 68
        Me.Label18.Text = "Tableau C :"
        Me.Label18.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tHonoraireTableauC
        '
        Me.tHonoraireTableauC.AutoSize = False
        Me.tHonoraireTableauC.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tHonoraireTableauC.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tHonoraireTableauC.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tHonoraireTableauC.ForeColor = System.Drawing.Color.Red
        Me.tHonoraireTableauC.Location = New System.Drawing.Point(120, 87)
        Me.tHonoraireTableauC.Name = "tHonoraireTableauC"
        Me.tHonoraireTableauC.Size = New System.Drawing.Size(119, 19)
        Me.tHonoraireTableauC.TabIndex = 67
        Me.tHonoraireTableauC.Tag = Nothing
        Me.tHonoraireTableauC.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label17
        '
        Me.Label17.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label17.ForeColor = System.Drawing.Color.Red
        Me.Label17.Location = New System.Drawing.Point(16, 60)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(98, 13)
        Me.Label17.TabIndex = 66
        Me.Label17.Text = "Tableau B :"
        Me.Label17.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tHonoraireTableauB
        '
        Me.tHonoraireTableauB.AutoSize = False
        Me.tHonoraireTableauB.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tHonoraireTableauB.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tHonoraireTableauB.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tHonoraireTableauB.ForeColor = System.Drawing.Color.Red
        Me.tHonoraireTableauB.Location = New System.Drawing.Point(120, 58)
        Me.tHonoraireTableauB.Name = "tHonoraireTableauB"
        Me.tHonoraireTableauB.Size = New System.Drawing.Size(119, 19)
        Me.tHonoraireTableauB.TabIndex = 65
        Me.tHonoraireTableauB.Tag = Nothing
        Me.tHonoraireTableauB.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label16
        '
        Me.Label16.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label16.ForeColor = System.Drawing.Color.Red
        Me.Label16.Location = New System.Drawing.Point(16, 31)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(98, 13)
        Me.Label16.TabIndex = 64
        Me.Label16.Text = "Tableau A :"
        Me.Label16.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tHonoraireTableauA
        '
        Me.tHonoraireTableauA.AutoSize = False
        Me.tHonoraireTableauA.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tHonoraireTableauA.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tHonoraireTableauA.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tHonoraireTableauA.ForeColor = System.Drawing.Color.Red
        Me.tHonoraireTableauA.Location = New System.Drawing.Point(120, 29)
        Me.tHonoraireTableauA.Name = "tHonoraireTableauA"
        Me.tHonoraireTableauA.Size = New System.Drawing.Size(119, 19)
        Me.tHonoraireTableauA.TabIndex = 63
        Me.tHonoraireTableauA.Tag = Nothing
        Me.tHonoraireTableauA.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage3
        '
        Me.C1DockingTabPage3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.C1DockingTabPage3.Controls.Add(Me.chbRetarancheDuStockLorsDeVenteInstance)
        Me.C1DockingTabPage3.Controls.Add(Me.chbInterdireChoisirParDesignation)
        Me.C1DockingTabPage3.Controls.Add(Me.Label3)
        Me.C1DockingTabPage3.Controls.Add(Me.Label2)
        Me.C1DockingTabPage3.Controls.Add(Me.tDureeAffichageAlerte)
        Me.C1DockingTabPage3.Controls.Add(Me.chbPermettrePreparateursSupprimerVente)
        Me.C1DockingTabPage3.Controls.Add(Me.chbPermettreUtiliserFrigosEnVente)
        Me.C1DockingTabPage3.Controls.Add(Me.chbVerifierProduitPrisEnChargeParCNAM)
        Me.C1DockingTabPage3.Controls.Add(Me.chbInterdireLaVenteDesPerimes)
        Me.C1DockingTabPage3.Controls.Add(Me.chbInscriptionSurOrdonnancierAutomatique)
        Me.C1DockingTabPage3.Controls.Add(Me.Label1)
        Me.C1DockingTabPage3.Controls.Add(Me.tTentatives)
        Me.C1DockingTabPage3.Controls.Add(Me.lMatricule)
        Me.C1DockingTabPage3.Controls.Add(Me.tCom)
        Me.C1DockingTabPage3.Controls.Add(Me.chbUSB)
        Me.C1DockingTabPage3.Controls.Add(Me.chbCreationDesClientsCreditDansNiveauPreparateur)
        Me.C1DockingTabPage3.Controls.Add(Me.chbImprimerBon)
        Me.C1DockingTabPage3.Controls.Add(Me.chbTiroir)
        Me.C1DockingTabPage3.Controls.Add(Me.chbImprimanteATicket)
        Me.C1DockingTabPage3.Controls.Add(Me.chbControleNombreUnites)
        Me.C1DockingTabPage3.Controls.Add(Me.chbValiderQteEgalA1)
        Me.C1DockingTabPage3.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage3.Name = "C1DockingTabPage3"
        Me.C1DockingTabPage3.Size = New System.Drawing.Size(602, 395)
        Me.C1DockingTabPage3.TabIndex = 2
        Me.C1DockingTabPage3.Text = "Vente"
        '
        'chbRetarancheDuStockLorsDeVenteInstance
        '
        Me.chbRetarancheDuStockLorsDeVenteInstance.BackColor = System.Drawing.Color.Transparent
        Me.chbRetarancheDuStockLorsDeVenteInstance.ForeColor = System.Drawing.Color.Red
        Me.chbRetarancheDuStockLorsDeVenteInstance.Location = New System.Drawing.Point(8, 314)
        Me.chbRetarancheDuStockLorsDeVenteInstance.Name = "chbRetarancheDuStockLorsDeVenteInstance"
        Me.chbRetarancheDuStockLorsDeVenteInstance.Size = New System.Drawing.Size(406, 19)
        Me.chbRetarancheDuStockLorsDeVenteInstance.TabIndex = 70
        Me.chbRetarancheDuStockLorsDeVenteInstance.Text = "Retrancher du stock lors d'une vente en instance"
        Me.chbRetarancheDuStockLorsDeVenteInstance.UseVisualStyleBackColor = False
        '
        'chbInterdireChoisirParDesignation
        '
        Me.chbInterdireChoisirParDesignation.BackColor = System.Drawing.Color.Transparent
        Me.chbInterdireChoisirParDesignation.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbInterdireChoisirParDesignation.Location = New System.Drawing.Point(8, 291)
        Me.chbInterdireChoisirParDesignation.Name = "chbInterdireChoisirParDesignation"
        Me.chbInterdireChoisirParDesignation.Size = New System.Drawing.Size(406, 19)
        Me.chbInterdireChoisirParDesignation.TabIndex = 69
        Me.chbInterdireChoisirParDesignation.Text = "Interdire de choisir l'article par désignation lors d'une vente"
        Me.chbInterdireChoisirParDesignation.UseVisualStyleBackColor = False
        '
        'Label3
        '
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label3.ForeColor = System.Drawing.Color.Red
        Me.Label3.Location = New System.Drawing.Point(224, 270)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(59, 13)
        Me.Label3.TabIndex = 68
        Me.Label3.Text = "Secondes"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label2
        '
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label2.ForeColor = System.Drawing.Color.Red
        Me.Label2.Location = New System.Drawing.Point(6, 270)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(153, 13)
        Me.Label2.TabIndex = 67
        Me.Label2.Text = "Durée d 'affichage des alertes"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tDureeAffichageAlerte
        '
        Me.tDureeAffichageAlerte.AutoSize = False
        Me.tDureeAffichageAlerte.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tDureeAffichageAlerte.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tDureeAffichageAlerte.Font = New System.Drawing.Font("Calibri", 8.25!)
        Me.tDureeAffichageAlerte.ForeColor = System.Drawing.Color.Red
        Me.tDureeAffichageAlerte.Location = New System.Drawing.Point(165, 268)
        Me.tDureeAffichageAlerte.Name = "tDureeAffichageAlerte"
        Me.tDureeAffichageAlerte.Size = New System.Drawing.Size(50, 19)
        Me.tDureeAffichageAlerte.TabIndex = 66
        Me.tDureeAffichageAlerte.Tag = Nothing
        Me.tDureeAffichageAlerte.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'chbPermettrePreparateursSupprimerVente
        '
        Me.chbPermettrePreparateursSupprimerVente.BackColor = System.Drawing.Color.Transparent
        Me.chbPermettrePreparateursSupprimerVente.ForeColor = System.Drawing.Color.Red
        Me.chbPermettrePreparateursSupprimerVente.Location = New System.Drawing.Point(8, 245)
        Me.chbPermettrePreparateursSupprimerVente.Name = "chbPermettrePreparateursSupprimerVente"
        Me.chbPermettrePreparateursSupprimerVente.Size = New System.Drawing.Size(406, 19)
        Me.chbPermettrePreparateursSupprimerVente.TabIndex = 65
        Me.chbPermettrePreparateursSupprimerVente.Text = "Permettre la suppression des ventes aux préparateurs"
        Me.chbPermettrePreparateursSupprimerVente.UseVisualStyleBackColor = False
        '
        'chbPermettreUtiliserFrigosEnVente
        '
        Me.chbPermettreUtiliserFrigosEnVente.BackColor = System.Drawing.Color.Transparent
        Me.chbPermettreUtiliserFrigosEnVente.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbPermettreUtiliserFrigosEnVente.Location = New System.Drawing.Point(8, 222)
        Me.chbPermettreUtiliserFrigosEnVente.Name = "chbPermettreUtiliserFrigosEnVente"
        Me.chbPermettreUtiliserFrigosEnVente.Size = New System.Drawing.Size(406, 19)
        Me.chbPermettreUtiliserFrigosEnVente.TabIndex = 64
        Me.chbPermettreUtiliserFrigosEnVente.Text = "Permettre l'utilisation des frigos au niveau des ventes"
        Me.chbPermettreUtiliserFrigosEnVente.UseVisualStyleBackColor = False
        '
        'chbVerifierProduitPrisEnChargeParCNAM
        '
        Me.chbVerifierProduitPrisEnChargeParCNAM.BackColor = System.Drawing.Color.Transparent
        Me.chbVerifierProduitPrisEnChargeParCNAM.ForeColor = System.Drawing.Color.Red
        Me.chbVerifierProduitPrisEnChargeParCNAM.Location = New System.Drawing.Point(8, 200)
        Me.chbVerifierProduitPrisEnChargeParCNAM.Name = "chbVerifierProduitPrisEnChargeParCNAM"
        Me.chbVerifierProduitPrisEnChargeParCNAM.Size = New System.Drawing.Size(406, 19)
        Me.chbVerifierProduitPrisEnChargeParCNAM.TabIndex = 63
        Me.chbVerifierProduitPrisEnChargeParCNAM.Text = "Vérifier si le produit est pris en charge par la CNAM"
        Me.chbVerifierProduitPrisEnChargeParCNAM.UseVisualStyleBackColor = False
        '
        'chbInterdireLaVenteDesPerimes
        '
        Me.chbInterdireLaVenteDesPerimes.BackColor = System.Drawing.Color.Transparent
        Me.chbInterdireLaVenteDesPerimes.ForeColor = System.Drawing.Color.Red
        Me.chbInterdireLaVenteDesPerimes.Location = New System.Drawing.Point(8, 179)
        Me.chbInterdireLaVenteDesPerimes.Name = "chbInterdireLaVenteDesPerimes"
        Me.chbInterdireLaVenteDesPerimes.Size = New System.Drawing.Size(406, 19)
        Me.chbInterdireLaVenteDesPerimes.TabIndex = 62
        Me.chbInterdireLaVenteDesPerimes.Text = "Interdire la vente des produits périmés"
        Me.chbInterdireLaVenteDesPerimes.UseVisualStyleBackColor = False
        '
        'chbInscriptionSurOrdonnancierAutomatique
        '
        Me.chbInscriptionSurOrdonnancierAutomatique.BackColor = System.Drawing.Color.Transparent
        Me.chbInscriptionSurOrdonnancierAutomatique.ForeColor = System.Drawing.Color.Red
        Me.chbInscriptionSurOrdonnancierAutomatique.Location = New System.Drawing.Point(8, 157)
        Me.chbInscriptionSurOrdonnancierAutomatique.Name = "chbInscriptionSurOrdonnancierAutomatique"
        Me.chbInscriptionSurOrdonnancierAutomatique.Size = New System.Drawing.Size(406, 19)
        Me.chbInscriptionSurOrdonnancierAutomatique.TabIndex = 61
        Me.chbInscriptionSurOrdonnancierAutomatique.Text = "Inscription sur ordonnancier Automatique"
        Me.chbInscriptionSurOrdonnancierAutomatique.UseVisualStyleBackColor = False
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label1.Location = New System.Drawing.Point(273, 116)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(63, 13)
        Me.Label1.TabIndex = 60
        Me.Label1.Text = "Tentatives :"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tTentatives
        '
        Me.tTentatives.AutoSize = False
        Me.tTentatives.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tTentatives.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tTentatives.Font = New System.Drawing.Font("Calibri", 8.25!)
        Me.tTentatives.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tTentatives.Location = New System.Drawing.Point(341, 113)
        Me.tTentatives.Name = "tTentatives"
        Me.tTentatives.Size = New System.Drawing.Size(50, 19)
        Me.tTentatives.TabIndex = 59
        Me.tTentatives.Tag = Nothing
        Me.tTentatives.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lMatricule
        '
        Me.lMatricule.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.lMatricule.ForeColor = System.Drawing.Color.Red
        Me.lMatricule.Location = New System.Drawing.Point(283, 50)
        Me.lMatricule.Name = "lMatricule"
        Me.lMatricule.Size = New System.Drawing.Size(43, 13)
        Me.lMatricule.TabIndex = 58
        Me.lMatricule.Text = "COM :"
        Me.lMatricule.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tCom
        '
        Me.tCom.AutoSize = False
        Me.tCom.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCom.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tCom.Font = New System.Drawing.Font("Calibri", 8.25!)
        Me.tCom.ForeColor = System.Drawing.Color.Red
        Me.tCom.Location = New System.Drawing.Point(327, 47)
        Me.tCom.Name = "tCom"
        Me.tCom.Size = New System.Drawing.Size(50, 19)
        Me.tCom.TabIndex = 57
        Me.tCom.Tag = Nothing
        Me.tCom.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'chbUSB
        '
        Me.chbUSB.BackColor = System.Drawing.Color.Transparent
        Me.chbUSB.ForeColor = System.Drawing.Color.Red
        Me.chbUSB.Location = New System.Drawing.Point(174, 46)
        Me.chbUSB.Name = "chbUSB"
        Me.chbUSB.Size = New System.Drawing.Size(88, 19)
        Me.chbUSB.TabIndex = 7
        Me.chbUSB.Text = "USB"
        Me.chbUSB.UseVisualStyleBackColor = False
        '
        'chbCreationDesClientsCreditDansNiveauPreparateur
        '
        Me.chbCreationDesClientsCreditDansNiveauPreparateur.BackColor = System.Drawing.Color.Transparent
        Me.chbCreationDesClientsCreditDansNiveauPreparateur.ForeColor = System.Drawing.Color.Red
        Me.chbCreationDesClientsCreditDansNiveauPreparateur.Location = New System.Drawing.Point(8, 134)
        Me.chbCreationDesClientsCreditDansNiveauPreparateur.Name = "chbCreationDesClientsCreditDansNiveauPreparateur"
        Me.chbCreationDesClientsCreditDansNiveauPreparateur.Size = New System.Drawing.Size(406, 24)
        Me.chbCreationDesClientsCreditDansNiveauPreparateur.TabIndex = 6
        Me.chbCreationDesClientsCreditDansNiveauPreparateur.Text = "Permettre la création des clients crédit dans le niveau préparateur"
        Me.chbCreationDesClientsCreditDansNiveauPreparateur.UseVisualStyleBackColor = False
        '
        'chbImprimerBon
        '
        Me.chbImprimerBon.BackColor = System.Drawing.Color.Transparent
        Me.chbImprimerBon.ForeColor = System.Drawing.Color.Red
        Me.chbImprimerBon.Location = New System.Drawing.Point(8, 64)
        Me.chbImprimerBon.Name = "chbImprimerBon"
        Me.chbImprimerBon.Size = New System.Drawing.Size(210, 24)
        Me.chbImprimerBon.TabIndex = 5
        Me.chbImprimerBon.Text = "Imprimer les bons"
        Me.chbImprimerBon.UseVisualStyleBackColor = False
        '
        'chbTiroir
        '
        Me.chbTiroir.BackColor = System.Drawing.Color.Transparent
        Me.chbTiroir.ForeColor = System.Drawing.Color.Red
        Me.chbTiroir.Location = New System.Drawing.Point(8, 46)
        Me.chbTiroir.Name = "chbTiroir"
        Me.chbTiroir.Size = New System.Drawing.Size(88, 19)
        Me.chbTiroir.TabIndex = 4
        Me.chbTiroir.Text = "Tiroir"
        Me.chbTiroir.UseVisualStyleBackColor = False
        '
        'chbImprimanteATicket
        '
        Me.chbImprimanteATicket.BackColor = System.Drawing.Color.Transparent
        Me.chbImprimanteATicket.ForeColor = System.Drawing.Color.Red
        Me.chbImprimanteATicket.Location = New System.Drawing.Point(8, 21)
        Me.chbImprimanteATicket.Name = "chbImprimanteATicket"
        Me.chbImprimanteATicket.Size = New System.Drawing.Size(428, 24)
        Me.chbImprimanteATicket.TabIndex = 3
        Me.chbImprimanteATicket.Text = "Imprimante à ticket"
        Me.chbImprimanteATicket.UseVisualStyleBackColor = False
        '
        'chbControleNombreUnites
        '
        Me.chbControleNombreUnites.BackColor = System.Drawing.Color.Transparent
        Me.chbControleNombreUnites.ForeColor = System.Drawing.SystemColors.ControlText
        Me.chbControleNombreUnites.Location = New System.Drawing.Point(8, 113)
        Me.chbControleNombreUnites.Name = "chbControleNombreUnites"
        Me.chbControleNombreUnites.Size = New System.Drawing.Size(232, 19)
        Me.chbControleNombreUnites.TabIndex = 2
        Me.chbControleNombreUnites.Text = "Contrôle du nombre des unités vendues"
        Me.chbControleNombreUnites.UseVisualStyleBackColor = False
        '
        'chbValiderQteEgalA1
        '
        Me.chbValiderQteEgalA1.BackColor = System.Drawing.Color.Transparent
        Me.chbValiderQteEgalA1.Location = New System.Drawing.Point(8, 87)
        Me.chbValiderQteEgalA1.Name = "chbValiderQteEgalA1"
        Me.chbValiderQteEgalA1.Size = New System.Drawing.Size(428, 24)
        Me.chbValiderQteEgalA1.TabIndex = 1
        Me.chbValiderQteEgalA1.Text = "Validation automatique d'une quantité  de 1"
        Me.chbValiderQteEgalA1.UseVisualStyleBackColor = False
        '
        'C1DockingTabPage4
        '
        Me.C1DockingTabPage4.Controls.Add(Me.lAnneeProchaine)
        Me.C1DockingTabPage4.Controls.Add(Me.lAnneeCourant)
        Me.C1DockingTabPage4.Controls.Add(Me.Label26)
        Me.C1DockingTabPage4.Controls.Add(Me.GroupBox4)
        Me.C1DockingTabPage4.Controls.Add(Me.chbQteMultiple5)
        Me.C1DockingTabPage4.Controls.Add(Me.Label22)
        Me.C1DockingTabPage4.Controls.Add(Me.tNePasSortirManquantsDepuis)
        Me.C1DockingTabPage4.Controls.Add(Me.Label20)
        Me.C1DockingTabPage4.Controls.Add(Me.tCommandeGroupeJ)
        Me.C1DockingTabPage4.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage4.Name = "C1DockingTabPage4"
        Me.C1DockingTabPage4.Size = New System.Drawing.Size(602, 395)
        Me.C1DockingTabPage4.TabIndex = 3
        Me.C1DockingTabPage4.Text = "Commande"
        '
        'lAnneeProchaine
        '
        Me.lAnneeProchaine.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.lAnneeProchaine.ForeColor = System.Drawing.SystemColors.ControlText
        Me.lAnneeProchaine.Location = New System.Drawing.Point(9, 248)
        Me.lAnneeProchaine.Name = "lAnneeProchaine"
        Me.lAnneeProchaine.Size = New System.Drawing.Size(86, 13)
        Me.lAnneeProchaine.TabIndex = 76
        Me.lAnneeProchaine.Text = "DU"
        Me.lAnneeProchaine.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lAnneeCourant
        '
        Me.lAnneeCourant.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.lAnneeCourant.ForeColor = System.Drawing.SystemColors.ControlText
        Me.lAnneeCourant.Location = New System.Drawing.Point(9, 213)
        Me.lAnneeCourant.Name = "lAnneeCourant"
        Me.lAnneeCourant.Size = New System.Drawing.Size(86, 13)
        Me.lAnneeCourant.TabIndex = 69
        Me.lAnneeCourant.Text = "DU"
        Me.lAnneeCourant.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label26
        '
        Me.Label26.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label26.ForeColor = System.Drawing.Color.Red
        Me.Label26.Location = New System.Drawing.Point(359, 132)
        Me.Label26.Name = "Label26"
        Me.Label26.Size = New System.Drawing.Size(17, 13)
        Me.Label26.TabIndex = 75
        Me.Label26.Text = "J"
        Me.Label26.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.dtpfinAnneeProchaine)
        Me.GroupBox4.Controls.Add(Me.dtpDebutAnneeProchaine)
        Me.GroupBox4.Controls.Add(Me.Label24)
        Me.GroupBox4.Controls.Add(Me.dtpFinAnneeCourant)
        Me.GroupBox4.Controls.Add(Me.Label25)
        Me.GroupBox4.Controls.Add(Me.dtpDebutAnneeCourant)
        Me.GroupBox4.Location = New System.Drawing.Point(101, 167)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(268, 109)
        Me.GroupBox4.TabIndex = 74
        Me.GroupBox4.TabStop = False
        Me.GroupBox4.Text = "Congé"
        '
        'dtpfinAnneeProchaine
        '
        Me.dtpfinAnneeProchaine.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpfinAnneeProchaine.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpfinAnneeProchaine.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpfinAnneeProchaine.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpfinAnneeProchaine.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpfinAnneeProchaine.Location = New System.Drawing.Point(133, 76)
        Me.dtpfinAnneeProchaine.Name = "dtpfinAnneeProchaine"
        Me.dtpfinAnneeProchaine.Size = New System.Drawing.Size(99, 18)
        Me.dtpfinAnneeProchaine.TabIndex = 77
        Me.dtpfinAnneeProchaine.Tag = Nothing
        Me.dtpfinAnneeProchaine.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpfinAnneeProchaine.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'dtpDebutAnneeProchaine
        '
        Me.dtpDebutAnneeProchaine.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDebutAnneeProchaine.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDebutAnneeProchaine.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDebutAnneeProchaine.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutAnneeProchaine.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutAnneeProchaine.Location = New System.Drawing.Point(28, 76)
        Me.dtpDebutAnneeProchaine.Name = "dtpDebutAnneeProchaine"
        Me.dtpDebutAnneeProchaine.Size = New System.Drawing.Size(99, 18)
        Me.dtpDebutAnneeProchaine.TabIndex = 80
        Me.dtpDebutAnneeProchaine.Tag = Nothing
        Me.dtpDebutAnneeProchaine.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutAnneeProchaine.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label24
        '
        Me.Label24.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label24.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label24.Location = New System.Drawing.Point(143, 20)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(86, 13)
        Me.Label24.TabIndex = 66
        Me.Label24.Text = "AU"
        Me.Label24.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'dtpFinAnneeCourant
        '
        Me.dtpFinAnneeCourant.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpFinAnneeCourant.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpFinAnneeCourant.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpFinAnneeCourant.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFinAnneeCourant.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFinAnneeCourant.Location = New System.Drawing.Point(133, 45)
        Me.dtpFinAnneeCourant.Name = "dtpFinAnneeCourant"
        Me.dtpFinAnneeCourant.Size = New System.Drawing.Size(99, 18)
        Me.dtpFinAnneeCourant.TabIndex = 79
        Me.dtpFinAnneeCourant.Tag = Nothing
        Me.dtpFinAnneeCourant.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpFinAnneeCourant.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label25
        '
        Me.Label25.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label25.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label25.Location = New System.Drawing.Point(30, 20)
        Me.Label25.Name = "Label25"
        Me.Label25.Size = New System.Drawing.Size(86, 13)
        Me.Label25.TabIndex = 64
        Me.Label25.Text = "DU"
        Me.Label25.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'dtpDebutAnneeCourant
        '
        Me.dtpDebutAnneeCourant.BorderColor = System.Drawing.Color.FromArgb(CType(CType(177, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(198, Byte), Integer))
        Me.dtpDebutAnneeCourant.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        '
        '
        '
        Me.dtpDebutAnneeCourant.Calendar.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.dtpDebutAnneeCourant.Calendar.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutAnneeCourant.Calendar.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutAnneeCourant.Location = New System.Drawing.Point(28, 44)
        Me.dtpDebutAnneeCourant.Name = "dtpDebutAnneeCourant"
        Me.dtpDebutAnneeCourant.Size = New System.Drawing.Size(99, 18)
        Me.dtpDebutAnneeCourant.TabIndex = 78
        Me.dtpDebutAnneeCourant.Tag = Nothing
        Me.dtpDebutAnneeCourant.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.dtpDebutAnneeCourant.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'chbQteMultiple5
        '
        Me.chbQteMultiple5.BackColor = System.Drawing.Color.Transparent
        Me.chbQteMultiple5.ForeColor = System.Drawing.Color.Red
        Me.chbQteMultiple5.Location = New System.Drawing.Point(31, 93)
        Me.chbQteMultiple5.Name = "chbQteMultiple5"
        Me.chbQteMultiple5.Size = New System.Drawing.Size(232, 19)
        Me.chbQteMultiple5.TabIndex = 73
        Me.chbQteMultiple5.Text = "Qté Multiple de 5"
        Me.chbQteMultiple5.UseVisualStyleBackColor = False
        '
        'Label22
        '
        Me.Label22.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label22.ForeColor = System.Drawing.Color.Red
        Me.Label22.Location = New System.Drawing.Point(34, 131)
        Me.Label22.Name = "Label22"
        Me.Label22.Size = New System.Drawing.Size(182, 13)
        Me.Label22.TabIndex = 72
        Me.Label22.Text = "Ne pas Sortir les manquants depuis :"
        Me.Label22.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tNePasSortirManquantsDepuis
        '
        Me.tNePasSortirManquantsDepuis.AutoSize = False
        Me.tNePasSortirManquantsDepuis.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNePasSortirManquantsDepuis.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNePasSortirManquantsDepuis.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNePasSortirManquantsDepuis.ForeColor = System.Drawing.Color.Red
        Me.tNePasSortirManquantsDepuis.Location = New System.Drawing.Point(221, 129)
        Me.tNePasSortirManquantsDepuis.Name = "tNePasSortirManquantsDepuis"
        Me.tNePasSortirManquantsDepuis.Size = New System.Drawing.Size(119, 19)
        Me.tNePasSortirManquantsDepuis.TabIndex = 71
        Me.tNePasSortirManquantsDepuis.Tag = Nothing
        Me.tNePasSortirManquantsDepuis.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label20
        '
        Me.Label20.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label20.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label20.Location = New System.Drawing.Point(25, 58)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(98, 13)
        Me.Label20.TabIndex = 70
        Me.Label20.Text = "Cmde Groupée/J :"
        Me.Label20.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tCommandeGroupeJ
        '
        Me.tCommandeGroupeJ.AutoSize = False
        Me.tCommandeGroupeJ.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCommandeGroupeJ.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tCommandeGroupeJ.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tCommandeGroupeJ.ForeColor = System.Drawing.SystemColors.ControlText
        Me.tCommandeGroupeJ.Location = New System.Drawing.Point(129, 55)
        Me.tCommandeGroupeJ.Name = "tCommandeGroupeJ"
        Me.tCommandeGroupeJ.Size = New System.Drawing.Size(119, 19)
        Me.tCommandeGroupeJ.TabIndex = 69
        Me.tCommandeGroupeJ.Tag = Nothing
        Me.tCommandeGroupeJ.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1DockingTabPage5
        '
        Me.C1DockingTabPage5.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage5.Name = "C1DockingTabPage5"
        Me.C1DockingTabPage5.Size = New System.Drawing.Size(602, 395)
        Me.C1DockingTabPage5.TabIndex = 4
        Me.C1DockingTabPage5.Visible = False
        '
        'C1DockingTabPage6
        '
        Me.C1DockingTabPage6.Controls.Add(Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat)
        Me.C1DockingTabPage6.Controls.Add(Me.chbAutoriserSuppressionsDesAchatsAuxPréparateurs)
        Me.C1DockingTabPage6.Controls.Add(Me.chbAutoriserModificationsDesAchatsPréparateurs)
        Me.C1DockingTabPage6.Controls.Add(Me.GroupBox5)
        Me.C1DockingTabPage6.Controls.Add(Me.Label23)
        Me.C1DockingTabPage6.Controls.Add(Me.tNomOrdinateurImpressionCodeABarre)
        Me.C1DockingTabPage6.Location = New System.Drawing.Point(1, 24)
        Me.C1DockingTabPage6.Name = "C1DockingTabPage6"
        Me.C1DockingTabPage6.Size = New System.Drawing.Size(602, 395)
        Me.C1DockingTabPage6.TabIndex = 5
        Me.C1DockingTabPage6.Text = "Achat"
        '
        'chbAfficherLesDerniereDDPeremptionDansNouveauAchat
        '
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.BackColor = System.Drawing.Color.Transparent
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.ForeColor = System.Drawing.Color.Red
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.Location = New System.Drawing.Point(36, 229)
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.Name = "chbAfficherLesDerniereDDPeremptionDansNouveauAchat"
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.Size = New System.Drawing.Size(406, 19)
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.TabIndex = 74
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.Text = "Afficher les dernières dates des péremption dans les nouveaux achats "
        Me.chbAfficherLesDerniereDDPeremptionDansNouveauAchat.UseVisualStyleBackColor = False
        '
        'chbAutoriserSuppressionsDesAchatsAuxPréparateurs
        '
        Me.chbAutoriserSuppressionsDesAchatsAuxPréparateurs.BackColor = System.Drawing.Color.Transparent
        Me.chbAutoriserSuppressionsDesAchatsAuxPréparateurs.ForeColor = System.Drawing.Color.Red
        Me.chbAutoriserSuppressionsDesAchatsAuxPréparateurs.Location = New System.Drawing.Point(36, 202)
        Me.chbAutoriserSuppressionsDesAchatsAuxPréparateurs.Name = "chbAutoriserSuppressionsDesAchatsAuxPréparateurs"
        Me.chbAutoriserSuppressionsDesAchatsAuxPréparateurs.Size = New System.Drawing.Size(406, 19)
        Me.chbAutoriserSuppressionsDesAchatsAuxPréparateurs.TabIndex = 73
        Me.chbAutoriserSuppressionsDesAchatsAuxPréparateurs.Text = "Autoriser les suppressions des achats aux préparateurs"
        Me.chbAutoriserSuppressionsDesAchatsAuxPréparateurs.UseVisualStyleBackColor = False
        '
        'chbAutoriserModificationsDesAchatsPréparateurs
        '
        Me.chbAutoriserModificationsDesAchatsPréparateurs.BackColor = System.Drawing.Color.Transparent
        Me.chbAutoriserModificationsDesAchatsPréparateurs.ForeColor = System.Drawing.Color.Red
        Me.chbAutoriserModificationsDesAchatsPréparateurs.Location = New System.Drawing.Point(36, 175)
        Me.chbAutoriserModificationsDesAchatsPréparateurs.Name = "chbAutoriserModificationsDesAchatsPréparateurs"
        Me.chbAutoriserModificationsDesAchatsPréparateurs.Size = New System.Drawing.Size(406, 19)
        Me.chbAutoriserModificationsDesAchatsPréparateurs.TabIndex = 72
        Me.chbAutoriserModificationsDesAchatsPréparateurs.Text = "Autoriser les modifications des achats au préparateurs"
        Me.chbAutoriserModificationsDesAchatsPréparateurs.UseVisualStyleBackColor = False
        '
        'GroupBox5
        '
        Me.GroupBox5.Controls.Add(Me.rdbInterventionAvecUnAssistant)
        Me.GroupBox5.Controls.Add(Me.rdbNePasMettreAJourLaFicheArticleSiLePrixChange)
        Me.GroupBox5.Location = New System.Drawing.Point(36, 66)
        Me.GroupBox5.Name = "GroupBox5"
        Me.GroupBox5.Size = New System.Drawing.Size(486, 93)
        Me.GroupBox5.TabIndex = 71
        Me.GroupBox5.TabStop = False
        '
        'rdbInterventionAvecUnAssistant
        '
        Me.rdbInterventionAvecUnAssistant.AutoSize = True
        Me.rdbInterventionAvecUnAssistant.ForeColor = System.Drawing.SystemColors.ControlText
        Me.rdbInterventionAvecUnAssistant.Location = New System.Drawing.Point(17, 53)
        Me.rdbInterventionAvecUnAssistant.Name = "rdbInterventionAvecUnAssistant"
        Me.rdbInterventionAvecUnAssistant.Size = New System.Drawing.Size(246, 17)
        Me.rdbInterventionAvecUnAssistant.TabIndex = 72
        Me.rdbInterventionAvecUnAssistant.TabStop = True
        Me.rdbInterventionAvecUnAssistant.Text = "Intervention avec un assistant si le prix change"
        Me.rdbInterventionAvecUnAssistant.UseVisualStyleBackColor = True
        '
        'rdbNePasMettreAJourLaFicheArticleSiLePrixChange
        '
        Me.rdbNePasMettreAJourLaFicheArticleSiLePrixChange.AutoSize = True
        Me.rdbNePasMettreAJourLaFicheArticleSiLePrixChange.ForeColor = System.Drawing.SystemColors.ControlText
        Me.rdbNePasMettreAJourLaFicheArticleSiLePrixChange.Location = New System.Drawing.Point(17, 20)
        Me.rdbNePasMettreAJourLaFicheArticleSiLePrixChange.Name = "rdbNePasMettreAJourLaFicheArticleSiLePrixChange"
        Me.rdbNePasMettreAJourLaFicheArticleSiLePrixChange.Size = New System.Drawing.Size(267, 17)
        Me.rdbNePasMettreAJourLaFicheArticleSiLePrixChange.TabIndex = 70
        Me.rdbNePasMettreAJourLaFicheArticleSiLePrixChange.TabStop = True
        Me.rdbNePasMettreAJourLaFicheArticleSiLePrixChange.Text = "Ne pas mettre à jour la fiche article si le prix change"
        Me.rdbNePasMettreAJourLaFicheArticleSiLePrixChange.UseVisualStyleBackColor = True
        '
        'Label23
        '
        Me.Label23.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Label23.ForeColor = System.Drawing.Color.Red
        Me.Label23.Location = New System.Drawing.Point(23, 45)
        Me.Label23.Name = "Label23"
        Me.Label23.Size = New System.Drawing.Size(237, 13)
        Me.Label23.TabIndex = 70
        Me.Label23.Text = "Nom de l'ordinateur d'impression code à barre :"
        Me.Label23.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'tNomOrdinateurImpressionCodeABarre
        '
        Me.tNomOrdinateurImpressionCodeABarre.AutoSize = False
        Me.tNomOrdinateurImpressionCodeABarre.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tNomOrdinateurImpressionCodeABarre.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.tNomOrdinateurImpressionCodeABarre.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.tNomOrdinateurImpressionCodeABarre.ForeColor = System.Drawing.Color.Red
        Me.tNomOrdinateurImpressionCodeABarre.Location = New System.Drawing.Point(266, 43)
        Me.tNomOrdinateurImpressionCodeABarre.Name = "tNomOrdinateurImpressionCodeABarre"
        Me.tNomOrdinateurImpressionCodeABarre.Size = New System.Drawing.Size(119, 19)
        Me.tNomOrdinateurImpressionCodeABarre.TabIndex = 69
        Me.tNomOrdinateurImpressionCodeABarre.Tag = Nothing
        Me.tNomOrdinateurImpressionCodeABarre.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fParametres
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(753, 516)
        Me.Controls.Add(Me.Panel)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "fParametres"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Panel.ResumeLayout(False)
        Me.Panel.PerformLayout()
        CType(Me.tPoste, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Tab, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Tab.ResumeLayout(False)
        Me.C1DockingTabPage1.ResumeLayout(False)
        CType(Me.tMatriculeFiscale, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNCNSS, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tTimbre, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tMessagederoulant, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodeTva, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tFax, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tTelephone, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tAdresse, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNumeroAffiliation2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNumeroAffiliation1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCNAM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tPharmacie, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tLecteurUpdate, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCodePharmacien, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage2.ResumeLayout(False)
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        CType(Me.tMinimumdePerception, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        CType(Me.tHonoraireTableauC, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tHonoraireTableauB, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tHonoraireTableauA, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage3.ResumeLayout(False)
        CType(Me.tDureeAffichageAlerte, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tTentatives, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCom, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage4.ResumeLayout(False)
        Me.GroupBox4.ResumeLayout(False)
        CType(Me.dtpfinAnneeProchaine, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpDebutAnneeProchaine, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpFinAnneeCourant, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dtpDebutAnneeCourant, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tNePasSortirManquantsDepuis, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tCommandeGroupeJ, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTabPage6.ResumeLayout(False)
        Me.GroupBox5.ResumeLayout(False)
        Me.GroupBox5.PerformLayout()
        CType(Me.tNomOrdinateurImpressionCodeABarre, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents Tab As C1.Win.C1Command.C1DockingTab
    Friend WithEvents C1DockingTabPage1 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents C1DockingTabPage2 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents C1DockingTabPage3 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents chbValiderQteEgalA1 As System.Windows.Forms.CheckBox
    Friend WithEvents C1DockingTabPage4 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents C1DockingTabPage5 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents C1DockingTabPage6 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents chbControleNombreUnites As System.Windows.Forms.CheckBox
    Friend WithEvents bAnnuler As C1.Win.C1Input.C1Button
    Friend WithEvents bConfirmer As C1.Win.C1Input.C1Button
    Friend WithEvents chbCreationDesClientsCreditDansNiveauPreparateur As System.Windows.Forms.CheckBox
    Friend WithEvents chbImprimerBon As System.Windows.Forms.CheckBox
    Friend WithEvents chbTiroir As System.Windows.Forms.CheckBox
    Friend WithEvents chbImprimanteATicket As System.Windows.Forms.CheckBox
    Friend WithEvents chbUSB As System.Windows.Forms.CheckBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents tTentatives As C1.Win.C1Input.C1TextBox
    Friend WithEvents lMatricule As System.Windows.Forms.Label
    Friend WithEvents tCom As C1.Win.C1Input.C1TextBox
    Friend WithEvents chbInscriptionSurOrdonnancierAutomatique As System.Windows.Forms.CheckBox
    Friend WithEvents chbPermettrePreparateursSupprimerVente As System.Windows.Forms.CheckBox
    Friend WithEvents chbPermettreUtiliserFrigosEnVente As System.Windows.Forms.CheckBox
    Friend WithEvents chbVerifierProduitPrisEnChargeParCNAM As System.Windows.Forms.CheckBox
    Friend WithEvents chbInterdireLaVenteDesPerimes As System.Windows.Forms.CheckBox
    Friend WithEvents chbRetarancheDuStockLorsDeVenteInstance As System.Windows.Forms.CheckBox
    Friend WithEvents chbInterdireChoisirParDesignation As System.Windows.Forms.CheckBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents tDureeAffichageAlerte As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents tCodePharmacien As C1.Win.C1Input.C1TextBox
    Friend WithEvents chbActiverTransactionelle As System.Windows.Forms.CheckBox
    Friend WithEvents chbAutoriserLesMotsDePasse As System.Windows.Forms.CheckBox
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents tPharmacie As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents tNumeroAffiliation1 As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents tCNAM As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents tNumeroAffiliation2 As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents tFax As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents tTelephone As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents tAdresse As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents tTimbre As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents tMessagederoulant As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents tCodeTva As C1.Win.C1Input.C1TextBox
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents Label18 As System.Windows.Forms.Label
    Friend WithEvents tHonoraireTableauC As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label17 As System.Windows.Forms.Label
    Friend WithEvents tHonoraireTableauB As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents tHonoraireTableauA As C1.Win.C1Input.C1TextBox
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents Label19 As System.Windows.Forms.Label
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents tMinimumdePerception As C1.Win.C1Input.C1TextBox
    Friend WithEvents rdbDCI As System.Windows.Forms.RadioButton
    Friend WithEvents rdbForme As System.Windows.Forms.RadioButton
    Friend WithEvents rdbPrixTTC As System.Windows.Forms.RadioButton
    Friend WithEvents chbAutoriserLesChangementsAutomatiqueDesPrixDesArticlesPreparations As System.Windows.Forms.CheckBox
    Friend WithEvents chbAutoriserMiseAjoursEnLigneArticles As System.Windows.Forms.CheckBox
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents Label24 As System.Windows.Forms.Label
    Friend WithEvents Label25 As System.Windows.Forms.Label
    Friend WithEvents chbQteMultiple5 As System.Windows.Forms.CheckBox
    Friend WithEvents Label22 As System.Windows.Forms.Label
    Friend WithEvents tNePasSortirManquantsDepuis As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label20 As System.Windows.Forms.Label
    Friend WithEvents tCommandeGroupeJ As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label23 As System.Windows.Forms.Label
    Friend WithEvents tNomOrdinateurImpressionCodeABarre As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label26 As System.Windows.Forms.Label
    Friend WithEvents chbAfficherLesDerniereDDPeremptionDansNouveauAchat As System.Windows.Forms.CheckBox
    Friend WithEvents chbAutoriserSuppressionsDesAchatsAuxPréparateurs As System.Windows.Forms.CheckBox
    Friend WithEvents chbAutoriserModificationsDesAchatsPréparateurs As System.Windows.Forms.CheckBox
    Friend WithEvents GroupBox5 As System.Windows.Forms.GroupBox
    Friend WithEvents rdbInterventionAvecUnAssistant As System.Windows.Forms.RadioButton
    Friend WithEvents rdbNePasMettreAJourLaFicheArticleSiLePrixChange As System.Windows.Forms.RadioButton
    Friend WithEvents Label27 As System.Windows.Forms.Label
    Friend WithEvents tPoste As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label28 As System.Windows.Forms.Label
    Friend WithEvents tMatriculeFiscale As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label29 As System.Windows.Forms.Label
    Friend WithEvents tNCNSS As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label30 As System.Windows.Forms.Label
    Friend WithEvents lAnneeProchaine As System.Windows.Forms.Label
    Friend WithEvents lAnneeCourant As System.Windows.Forms.Label
    Friend WithEvents dtpfinAnneeProchaine As C1.Win.C1Input.C1DateEdit
    Friend WithEvents dtpDebutAnneeProchaine As C1.Win.C1Input.C1DateEdit
    Friend WithEvents dtpFinAnneeCourant As C1.Win.C1Input.C1DateEdit
    Friend WithEvents dtpDebutAnneeCourant As C1.Win.C1Input.C1DateEdit
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents tLecteurUpdate As C1.Win.C1Input.C1TextBox
End Class
