@echo off
echo ========================================
echo    CREATION PHARMA2000 MODERNE
echo    Interface Moderne et Elegante
echo ========================================
echo.

cd /d "%~dp0"

echo Creation de la structure du projet...
echo.

REM Créer le dossier principal
if not exist "PharmaModerne" mkdir "PharmaModerne"
cd "PharmaModerne"

echo 1. Creation des dossiers principaux...

REM Structure principale
mkdir "PharmaModerne.UI"
mkdir "PharmaModerne.Core"
mkdir "PharmaModerne.Data"
mkdir "PharmaModerne.Services"
mkdir "PharmaModerne.Reports"
mkdir "PharmaModerne.Security"
mkdir "PharmaModerne.Tests"
mkdir "PharmaModerne.Shared"

echo 2. Creation des sous-dossiers UI...

REM Structure UI
cd "PharmaModerne.UI"
mkdir "Views"
mkdir "ViewModels"
mkdir "Controls"
mkdir "Styles"
mkdir "Resources"
mkdir "Converters"
mkdir "Behaviors"

cd "Views"
mkdir "Ventes"
mkdir "Clients"
mkdir "Articles"
mkdir "Fournisseurs"
mkdir "Rapports"
mkdir "Administration"
mkdir "Caisse"
mkdir "Stock"
cd ..

cd "ViewModels"
mkdir "Ventes"
mkdir "Clients"
mkdir "Articles"
mkdir "Fournisseurs"
mkdir "Rapports"
mkdir "Administration"
mkdir "Caisse"
mkdir "Stock"
cd ..

cd ..

echo 3. Creation des sous-dossiers Core...

REM Structure Core
cd "PharmaModerne.Core"
mkdir "Models"
mkdir "Interfaces"
mkdir "Services"
mkdir "Validators"
mkdir "Extensions"
mkdir "Enums"

cd "Models"
mkdir "Ventes"
mkdir "Clients"
mkdir "Articles"
mkdir "Fournisseurs"
mkdir "Stock"
mkdir "Financier"
cd ..

cd ..

echo 4. Creation des sous-dossiers Data...

REM Structure Data
cd "PharmaModerne.Data"
mkdir "Context"
mkdir "Entities"
mkdir "Repositories"
mkdir "Migrations"
mkdir "Configurations"

cd "Entities"
mkdir "Ventes"
mkdir "Clients"
mkdir "Articles"
mkdir "Fournisseurs"
mkdir "Stock"
mkdir "Financier"
mkdir "Administration"
cd ..

cd ..

echo 5. Creation des fichiers de base...

REM Créer le fichier solution
echo Microsoft Visual Studio Solution File, Format Version 12.00 > "PharmaModerne.sln"
echo # Visual Studio Version 17 >> "PharmaModerne.sln"
echo VisualStudioVersion = 17.0.31903.59 >> "PharmaModerne.sln"
echo MinimumVisualStudioVersion = 10.0.40219.1 >> "PharmaModerne.sln"

echo.
echo ========================================
echo    STRUCTURE CREEE AVEC SUCCES !
echo ========================================
echo.
echo Dossiers crees :
echo ✓ PharmaModerne.UI - Interface utilisateur WPF
echo ✓ PharmaModerne.Core - Logique metier
echo ✓ PharmaModerne.Data - Acces aux donnees
echo ✓ PharmaModerne.Services - Services applicatifs
echo ✓ PharmaModerne.Reports - Rapports et etats
echo ✓ PharmaModerne.Security - Securite et authentification
echo ✓ PharmaModerne.Tests - Tests unitaires
echo ✓ PharmaModerne.Shared - Modeles partages
echo.
echo Prochaines etapes :
echo 1. Ouvrir Visual Studio 2022
echo 2. Creer les projets dans chaque dossier
echo 3. Installer les packages NuGet necessaires
echo 4. Commencer le developpement !
echo.

echo Appuyez sur une touche pour ouvrir le dossier...
pause >nul

explorer .

echo.
echo Projet pret pour le developpement !
echo.
