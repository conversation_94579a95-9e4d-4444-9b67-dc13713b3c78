﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fEtatSortiArticle
    Dim cmdNature As New SqlCommand
    Dim daNature As New SqlDataAdapter
    Dim dsNature As New DataSet

    Dim cmdSortie As New SqlCommand
    Dim daSortie As New SqlDataAdapter
    Dim dsSortie As New DataSet

    Dim ValeurAchat18 As Double = 0
    Dim ValeurVente18 As Double = 0
    Dim ValeurAchat0 As Double = 0
    Dim ValeurVente0 As Double = 0
    Dim CondCrystalReport As String = ""

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub Init()
        'charger les natures
        cmdNature.CommandText = "SELECT LibelleNatureSortie FROM NATURE_SORTIE WHERE SupprimeNatureSortie = 0 ORDER BY LibelleNatureSortie ASC"
        cmdNature.Connection = ConnectionServeur
        daNature = New SqlDataAdapter(cmdNature)
        daNature.Fill(dsNature, "NATURE_SORTIE")
        cmbNature.DataSource = dsNature.Tables("NATURE_SORTIE")
        cmbNature.ValueMember = "LibelleNatureSortie"
        cmbNature.ColumnHeaders = False
        cmbNature.ExtendRightColumn = True

        dtpDebut.Text = Today
        dtpFin.Text = Today
        dtpDebut.Focus()

        'If ModeADMIN = "ADMIN" Then
        '    Label4.Visible = True
        '    Label6.Visible = True
        '    Label9.Visible = True
        '    Label7.Visible = True
        '    tValeurAchat0.Visible = True
        '    tValeurVente0.Visible = True
        '    tValeurAchat18.Visible = True
        '    tValeurVente18.Visible = True
        'Else
        '    Label4.Visible = False
        '    Label6.Visible = False
        '    Label9.Visible = False
        '    Label7.Visible = False
        '    tValeurAchat0.Visible = False
        '    tValeurVente0.Visible = False
        '    tValeurAchat18.Visible = False
        '    tValeurVente18.Visible = False
        'End If

        AfficherSortie()
    End Sub

    Public Sub AfficherSortie()

        Dim I As Integer
        Dim Cond As String = "1=1"
        CondCrystalReport = "1=1"
        dsSortie.Clear()

        If dtpDebut.Text <> "" And dtpDebut.Text.Length = 10 And dtpFin.Text <> "" And dtpFin.Text.Length = 10 Then
            Cond += " AND Date BETWEEN " + Quote(dtpDebut.Text + " 00:00:00") + " AND " + Quote(dtpFin.Text + " 23:59:59")
            CondCrystalReport += " AND {Vue_EtatSortieArticle.Date} >= DateTime('" + dtpDebut.Text + " 00:00:00')"
            CondCrystalReport += " AND {Vue_EtatSortieArticle.Date} <= DateTime('" + dtpFin.Text + " 23:59:59')"
        End If
        If cmbNature.Text <> "" Then
            Cond += " AND LibelleNatureSortie = " + Quote(cmbNature.Text)
            CondCrystalReport += " AND {Vue_EtatSortieArticle.LibelleNatureSortie} = " + Quote(cmbNature.Text)
        End If

        cmdSortie.CommandText = " SELECT " + _
                                " NumeroSortie, " + _
                                " Date, " + _
                                " CodeArticle, " + _
                                " Designation, " + _
                                " LibelleForme, " + _
                                " Qte, " + _
                                " TVA, " + _
                                " TotalVenteTTC, " + _
                                " TotalAchatTTC, " + _
                                " LibelleNatureSortie " + _
                                " FROM Vue_EtatSortieArticle " + _
                                " WHERE " + Cond + " ORDER BY Date ASC"

        cmdSortie.Connection = ConnectionServeur
        daSortie = New SqlDataAdapter(cmdSortie)
        daSortie.Fill(dsSortie, "SORTIE")

        With gSortie
            .Columns.Clear()
            .DataSource = dsSortie
            .DataMember = "SORTIE"
            .Rebind(False)
            .Columns("NumeroSortie").Caption = "Numéro"
            .Columns("Date").Caption = "Date"
            .Columns("CodeArticle").Caption = "Code"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Quantité"
            .Columns("TotalAchatTTC").Caption = "Total Achat TTC"
            .Columns("TotalVenteTTC").Caption = "Total Vente TTC"
            .Columns("LibelleNatureSortie").Caption = "Nature"
            .Columns("Date").NumberFormat = "dd/MM/yyyy"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("TVA").Visible = False
            '.Splits(0).DisplayColumns("TotalAchatTTC").Visible = False
            '.Splits(0).DisplayColumns("TotalVenteTTC").Visible = False
            .Splits(0).DisplayColumns("NumeroSortie").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("NumeroSortie").Width = 100
            .Splits(0).DisplayColumns("Date").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Date").Width = 100
            .Splits(0).DisplayColumns("CodeArticle").Width = 100
            .Splits(0).DisplayColumns("CodeArticle").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Designation").Width = 260
            .Splits(0).DisplayColumns("Qte").Width = 70
            .Splits(0).DisplayColumns("LibelleNatureSortie").Width = 70

            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gSortie)
        End With

        CalculValeur()

    End Sub

    Private Sub gSortie_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gSortie.FetchRowStyle
        e.CellStyle.Font = New System.Drawing.Font("Calibri", 9, FontStyle.Regular)
    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherSortie()
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDebut.TextChanged
        
    End Sub


    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherSortie()
            cmbNature.Focus()
        End If
    End Sub
    Private Sub cmbNature_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbNature.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherSortie()
            gSortie.Focus()
        End If
    End Sub

    Private Sub dtpFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpFin.TextChanged

    End Sub

    Private Sub cmbNature_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbNature.TextChanged

    End Sub

    Private Sub CalculValeur()
        Dim ValeurAchat As Decimal = 0.0
        Dim ValeurVente As Decimal = 0.0
        ValeurAchat18 = 0.0
        ValeurVente18 = 0.0
        ValeurAchat0 = 0.0
        ValeurVente0 = 0.0
        If gSortie.RowCount <> 0 Then
            For I As Integer = 0 To gSortie.RowCount - 1
                ValeurAchat += gSortie(I, "TotalAchatTTC")
                ValeurVente += gSortie(I, "TotalVenteTTC")
                If dsSortie.Tables("SORTIE").Rows(I).Item("TVA") = 18 Then
                    ValeurAchat18 += gSortie(I, "TotalAchatTTC")
                    ValeurVente18 += gSortie(I, "TotalVenteTTC")
                Else
                    ValeurAchat0 += gSortie(I, "TotalAchatTTC")
                    ValeurVente0 += gSortie(I, "TotalVenteTTC")
                End If
            Next
            tValeurAchat18.Text = Format(ValeurAchat, "0.000")
            tValeurVente18.Text = Format(ValeurVente, "0.000")
        End If
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        CalculValeur()
        If gSortie.RowCount > 0 Then
            Dim I As Integer
            For I = 0 To fMain.Tab.TabPages.Count - 1
                If fMain.Tab.TabPages(I).Text = "Imprimer Etat Sortie des articles" Then
                    fMain.Tab.TabPages(I).Show()
                    Exit Sub
                End If
            Next
            CR.FileName = Application.StartupPath + "\EtatSortieArticle.rpt"
            CR.SetParameterValue("debut", dtpDebut.Text)
            CR.SetParameterValue("fin", dtpFin.Text)
            If cmbNature.Text <> "" Then
                CR.SetParameterValue("Nature", cmbNature.Text)
            Else
                CR.SetParameterValue("Nature", "Tous")
            End If
            CR.SetParameterValue("ValeurAchat18", ValeurAchat18)
            CR.SetParameterValue("ValeurVente18", ValeurVente18)
            CR.SetParameterValue("ValeurAchat0", ValeurAchat0)
            CR.SetParameterValue("ValeurVente0", ValeurVente0)

            Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent
            CR.RecordSelectionFormula = CondCrystalReport
            Dim MyViewer As New fViewer
            MyViewer.CRViewer.ReportSource = CR
            fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
            fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
            fMain.Tab.SelectedTab.Text = "Imprimer Etat Sortie des articles"
        End If
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    
End Class