﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.ComponentModel
Imports System.Byte
Imports TraceApp.logFile
Imports ModuleGeneral
Imports System.Data
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports System.Windows.Forms
Imports System.Net.Mail
Imports System.Data.DataRowBuilder
Imports System.Drawing.Text

Public Class fParametresGeneraux
    Dim cmdParametres As New SqlCommand
    Dim daParametres As New SqlDataAdapter
    Dim cbParametres As New SqlCommandBuilder
    Dim dsParametres As New DataSet


    Dim cmdSurveillance As New SqlCommand
    Dim daSurveillance As New SqlDataAdapter
    Dim cbSurveillance As New SqlCommandBuilder
    Dim dsSurveillance As New DataSet

    Dim cmd As New SqlCommand

    Dim cmdArticle As New SqlCommand
    Dim daArticle As New SqlDataAdapter
    Dim cbArticle As New SqlCommandBuilder
    Dim dsArticle As New DataSet

    Dim cmdParametresPharmacien As New SqlCommand
    Dim daParametresPharmacien As New SqlDataAdapter
    Dim cbParametresPharmacien As New SqlCommandBuilder
    Dim dsParametresPharmacien As New DataSet
    Dim titreMessageErreur As String
    Dim textMessageErreur As String
    Dim resultatLogEcrire As String

    Dim nomDuModule As String
    Dim nomDuFome As String
    Dim nomDuFonction As String
    Dim nomException As String
    Dim NumeroMessageErreur As String
    Dim observation As String
    Dim nomUser As String
    Dim codeUser As String
    Dim nomMachineLogique As String
    Dim nomMachinePhysique As String
    Dim dateTime As String
    Dim envoiMail As Boolean
    Dim activerTraceLog As Boolean
    Dim activerTraceEcran As Boolean
    Dim codeDePharmacien As String

    Dim smtpServerMail As String
    Dim adresseMailDestinataire As String
    Dim sujetMail As String
    Dim TexteMail As String
    Dim portMail As Integer
    Dim adresseMailDestinateur As String
    Dim motDePasseMailDestinateur As String
    Dim sendEmail As Boolean
    Dim fichierselectionne As Boolean
    Dim StrSQL As String = ""
    Dim dr As DataRow
    Dim Mode As String = ""

    Public Sub afficherParametres()

        cmbVitesse.ClearItems()
        cmbVitesse.ColumnHeaders = False
        cmbVitesse.AddItem("4800")
        cmbVitesse.AddItem("9600")
        cmbVitesse.AddItem("19200")
        cmbVitesse.AddItem("38400")
        cmbVitesse.AddItem("57600")
        cmbVitesse.AddItem("76800")
        cmbVitesse.AddItem("115200")

        cmbParity.ClearItems()
        cmbParity.ColumnHeaders = False
        cmbParity.AddItem("Paire")
        cmbParity.AddItem("Impaire")
        cmbParity.AddItem("Aucune")

        cmbDataBit.ClearItems()
        cmbDataBit.ColumnHeaders = False
        cmbDataBit.AddItem("8")
        cmbDataBit.AddItem("7")

        cmbStopBit.ClearItems()
        cmbStopBit.ColumnHeaders = False
        cmbStopBit.AddItem("1")

        cmbProtocole.ClearItems()
        cmbProtocole.ColumnHeaders = False
        cmbProtocole.AddItem("Aucune")

        ' ''cmbMeme.ClearItems()
        ' ''cmbMeme.ColumnHeaders = False
        ' ''cmbMeme.AddItem("Oui")
        ' ''cmbMeme.AddItem("Non")

        If GetSetting("PHARMA", "PHARMA", "TypeTerminal", "") = "PORT_SERIE" Then
            chPortSerie.Checked = True
            GroupBoxTerminal.Enabled = True
        ElseIf GetSetting("PHARMA", "PHARMA", "TypeTerminal", "") = "FICHIER_TEXTE" Then
            chFichierTexte.Checked = True
            GroupBoxTerminal.Enabled = False
        ElseIf GetSetting("PHARMA", "PHARMA", "TypeTerminal", "") = "RESEAU" Then
            chReseau.Checked = True
            GroupBoxTerminal.Enabled = False
        End If

        tNomPort.Value = GetSetting("PHARMA", "PHARMA", "NomPort", "")
        cmbVitesse.Text = GetSetting("PHARMA", "PHARMA", "VitessePort", "")
        cmbParity.Text = GetSetting("PHARMA", "PHARMA", "PartiyPort", "")
        cmbDataBit.Text = GetSetting("PHARMA", "PHARMA", "DataBitPort", "")
        cmbStopBit.Text = GetSetting("PHARMA", "PHARMA", "StopBitPort", "")
        cmbProtocole.Text = GetSetting("PHARMA", "PHARMA", "ProtocolePort", "")


        cmbFormatExcel.ClearItems()
        cmbFormatExcel.ColumnHeaders = False
        cmbFormatExcel.AddItem("Excel2007")
        cmbFormatExcel.AddItem("Excel")
        cmbFormatExcel.AddItem("")

        tDossierExcel.Value = GetSetting("PHARMA", "PHARMA", "DossierExcel", "")
        cmbFormatExcel.Text = GetSetting("PHARMA", "PHARMA", "FormatExcel", "")



        'Paramètres généreaux 

        dsParametres.Clear()
        cmdParametres.CommandText = " SELECT * FROM PARAMETRES WHERE POSTE='" + System.Environment.GetEnvironmentVariable("Poste") + "'"

        cmdParametres.Connection = ConnectionServeur
        daParametres = New SqlDataAdapter(cmdParametres)
        daParametres.Fill(dsParametres, "PARAMETRES")

        If dsParametres.Tables("PARAMETRES").Rows.Count = 0 Then
            MsgBox("Vous devez enregistrer vos paramètres POSTE !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("HonoraireDeResponsabiliteTA").ToString <> "" Then
            tHonoraireTableauA.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("HonoraireDeResponsabiliteTA").ToString
        Else
            tHonoraireTableauA.Value = "0.080"
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("HonoraireDeResponsabiliteTB").ToString <> "" Then
            tHonoraireTableauB.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("HonoraireDeResponsabiliteTB").ToString
        Else
            tHonoraireTableauB.Value = "0.100"
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("HonoraireDeResponsabiliteTC").ToString <> "" Then
            tHonoraireTableauC.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("HonoraireDeResponsabiliteTC").ToString
        Else
            tHonoraireTableauC.Value = "0.080"
        End If
        If dsParametres.Tables("PARAMETRES").Rows(0).Item("MinimumDePerception").ToString <> "" Then
            tMinimumdePerception.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("MinimumDePerception").ToString
        Else
            tMinimumdePerception.Value = "0.250"
        End If


        tCommandeGroupeJ.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("CmdGroupeJournaliere")

        tNePasSortirManquantsDepuis.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("NePAsSortirLesManquantsDepuis")

        tNomOrdinateurImpressionCodeABarre.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("NomDeLordinateurDImpressionCodeABarre")

        If dsParametres.Tables("PARAMETRES").Rows(0).Item("AfficherLesDernieresDatesDePeremptionDansLesNouveauxAchats").ToString <> "" Then
            chbAfficherLesDerniereDDPeremptionDansNouveauAchat.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("AfficherLesDernieresDatesDePeremptionDansLesNouveauxAchats")
        Else
            chbAfficherLesDerniereDDPeremptionDansNouveauAchat.Checked = False
        End If

        dtpDebutAnneeCourant.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("CongeDuAnneeCourant")
        dtpFinAnneeCourant.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("CongeAuAnneeCourant")
        dtpDebutAnneeProchaine.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("CongeDuAnneeProchaine")
        dtpfinAnneeProchaine.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("CongeAuAnneeProchaine")

        tFacture.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("NumeroSequentielFacture").ToString
        tBlDevis.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("NumeroSequentielDevisBL").ToString
        tOrdonnancier.Value = dsParametres.Tables("PARAMETRES").Rows(0).Item("NumeroSequentielOrdonnancier").ToString

        chbAjouterMontantTimbreFacture.Checked = dsParametres.Tables("PARAMETRES").Rows(0).Item("AjouterTimbreAFacture")

        cbParametres = New SqlCommandBuilder(daParametres)

        'paramètres Pharmacien

        dsParametresPharmacien.Clear()
        cmdParametresPharmacien.CommandText = " SELECT * FROM PARAMETRE_PHARMACIE "

        cmdParametresPharmacien.Connection = ConnectionServeur
        daParametresPharmacien = New SqlDataAdapter(cmdParametresPharmacien)
        daParametresPharmacien.Fill(dsParametresPharmacien, "PARAMETRE_PHARMACIE")

        tCodePharmacien.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("CodePharmacie")
        tPharmacie.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Pharmacie")
        tCNAM.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("NCnam")
        tNumeroAffiliation1.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Affiliation1")
        tNumeroAffiliation2.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Affiliation2")
        tAdresse.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Adresse")
        tTelephone.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Telephone")
        tFax.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Fax")
        tCodeTva.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("CodeTVA")
        tMessagederoulant1.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Messagederoulant1")
        tMessagederoulant2.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Messagederoulant2")
        tTimbre.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Timbre")
        tRib.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Rib")
        dtpDateMigration.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("DateMigration")
        tSMTPMail.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("SmtpMail")
        tPortMail.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("PortMail")
        tAdresseMail.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("AdresseMailDestinateur")
        tMotDePasseMail.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("MotDePasseDestinateur")
        chbAutoriseEnvoiMail.Checked = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("AutoriserEnvoiMail")
        tLatLong.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Latitude_Longitude")
        tNbreJourValiditeParDefaut.Text = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("NbreJourValiditeParDefaut").ToString
        tTailleCodeCNAM.Text = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("TailleCodeCNAM").ToString
        tTexte.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("Texte").ToString
        tRemise.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("TauxRemise").ToString
        chbGestionBon.Checked = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("GererBon")

        'tTailleListe.Text = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("TailleListe").ToString
        'tTailleCaractere.Text = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("TailleCaractere").ToString

        cmbListe.Text = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("TailleListe").ToString
        cmbCaractere.Text = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("TailleCaractere").ToString
        cmbPolice.Text = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("PoliceCaractere").ToString
        chbActiverOMFAPCI.Checked = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("ActiverOMFAPCI").ToString

        tNbrOrdonnance.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("NombreJoursValiditerOrdonnance").ToString
        tNbrPriseEnCharge.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("NombreJoursValiditerPriseEnCharge").ToString
        tNbrAppareillage.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("NombreJoursValiditerAppareillage").ToString

        tNbrCommande.Value = dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("NbrCommandePourClasserManquant").ToString

        chbMultipleDeCinq.Checked = IIf(dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("NbrCommandePourClasserManquant").ToString = "", False, dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("NbrCommandePourClasserManquant").ToString)



        chbMettreAJourPrixFrigo.Checked = IIf(dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("MettreAJourPrixFrigo").ToString = "", False, dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("MettreAJourPrixFrigo").ToString)
        ChbAfficherReglementsSupprimes.Checked = IIf(dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("AfficherReglementsSupprimes").ToString = "", False, dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("AfficherReglementsSupprimes").ToString)
        ChbAutoriserSaisieNonMembeFamille.Checked = IIf(dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("AutoriserSaisieNonMembeFamille").ToString = "", False, dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("AutoriserSaisieNonMembeFamille").ToString)
        ChbImprimerUnEtiquette.Checked = IIf(dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("ImprimerUnEtiquette").ToString = "", False, dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("ImprimerUnEtiquette").ToString)

        rbManquantJour.Checked = IIf(dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("TypeClassementManquant").ToString = "", True, IIf(dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("TypeClassementManquant").ToString = "0", True, False))
        rbManquantNbrCommande.Checked = IIf(dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("TypeClassementManquant").ToString = "", False, IIf(dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("TypeClassementManquant").ToString = "1", True, False))
        ' ''cmbMeme.Text = IIf(dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE").Rows(0).Item("MemeFontAjoutConsultation") = True, "Oui", "Non")

        'Test sur l'autorisaion d'envoi de mail est cochée
        If chbAutoriseEnvoiMail.Checked Then
            gPramatresMail.Enabled = True
            gEnvoiLog.Enabled = True
        Else
            gPramatresMail.Enabled = False
            gEnvoiLog.Enabled = False
        End If


        cbParametresPharmacien = New SqlCommandBuilder(daParametresPharmacien)
        Tab.TabPages(0).Show()

        Try

            cmdSurveillance.CommandText = " SELECT * FROM MODULES_SURVEILLANCE ORDER BY ID"
            cmdSurveillance.Connection = ConnectionServeur
            daSurveillance = New SqlDataAdapter(cmdSurveillance)
            daSurveillance.Fill(dsSurveillance, "MODULES_SURVEILLANCE")
            ChbModuleArticle1.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(0).Item("ActiverSurveillance")
            ChbModuleArticle2.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(1).Item("ActiverSurveillance")
            ChbModuleArticle3.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(2).Item("ActiverSurveillance")
            ChbModuleArticle4.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(3).Item("ActiverSurveillance")
            ChbModuleArticle5.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(4).Item("ActiverSurveillance")
            ChbModuleClient1.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(5).Item("ActiverSurveillance")
            ChbModuleClient2.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(6).Item("ActiverSurveillance")
            ChbModuleClient3.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(7).Item("ActiverSurveillance")
            ChbModuleVente1.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(8).Item("ActiverSurveillance")
            ChbModuleVente2.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(9).Item("ActiverSurveillance")
            ChbModuleVente3.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(10).Item("ActiverSurveillance")
            ChbModuleVente4.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(11).Item("ActiverSurveillance")
            ChbModuleAchat1.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(12).Item("ActiverSurveillance")
            ChbModuleAchat2.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(13).Item("ActiverSurveillance")
            ChbModuleReglement1.Checked = dsSurveillance.Tables("MODULES_SURVEILLANCE").Rows(14).Item("ActiverSurveillance")


        Catch
        End Try

        Try
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "SELECT TOP(1) Mail FROM LISTE_MAIL  "
            tMail.Value = cmd.ExecuteScalar
        Catch
        End Try

    End Sub
    Public Sub init()
        Dim fonts As New InstalledFontCollection
        Dim font_f() As FontFamily = fonts.Families()
        For Each ft As FontFamily In font_f
            cmbPolice.Items.Add(ft.Name)
        Next

        dtpDebutAnneeCourant.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDebutAnneeCourant.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpFinAnneeCourant.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpFinAnneeCourant.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpDebutAnneeProchaine.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpDebutAnneeProchaine.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        dtpfinAnneeProchaine.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate
        dtpfinAnneeProchaine.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate

        afficherParametres()
    End Sub

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        Dim dr As DataRow
        Dim drPharmacien As DataRow

        Dim StrSQL As String = ""

        If tNumeroAffiliation1.Text.Length <> 10 Then
            tNumeroAffiliation1.Focus()
        End If

        If tNumeroAffiliation2.Text.Length <> 2 Then
            tNumeroAffiliation2.Focus()
        End If

        If Not IsNumeric(tRemise.Value) Then
            MsgBox("Veuillez saisir un nombre !", MsgBoxStyle.Critical, "Erreur")
            tRemise.Text = ""
            tRemise.Focus()
            Exit Sub
        End If
        If CDbl(tRemise.Value) >= 100 Then
            MsgBox("Veuillez saisir un taux inférieur ou égal à 100 !", MsgBoxStyle.Critical, "Erreur")
            tRemise.Focus()
            tRemise.SelectAll()
            Exit Sub
        End If

        'If tNomPort.Text = "" Then
        '    MsgBox("Veuillez saisir le nom du port !", MsgBoxStyle.Critical, "Erreur")
        '    tNomPort.Focus()
        '    Exit Sub
        'End If
        'If cmbVitesse.Text = "" Then
        '    MsgBox("Veuillez saisir la vitesse du port !", MsgBoxStyle.Critical, "Erreur")
        '    cmbVitesse.Focus()
        '    Exit Sub
        'End If
        'If cmbParity.Text = "" Then
        '    MsgBox("Veuillez saisir le parité du port !", MsgBoxStyle.Critical, "Erreur")
        '    cmbParity.Focus()
        '    Exit Sub
        'End If
        'If cmbDataBit.Text = "" Then
        '    MsgBox("Veuillez saisir le nombre du bit de données du port !", MsgBoxStyle.Critical, "Erreur")
        '    cmbDataBit.Focus()
        '    Exit Sub
        'End If
        'If cmbStopBit.Text = "" Then
        '    MsgBox("Veuillez saisir le nombre du bit d'arrêt du port !", MsgBoxStyle.Critical, "Erreur")
        '    cmbStopBit.Focus()
        '    Exit Sub
        'End If
        'If cmbProtocole.Text = "" Then
        '    MsgBox("Veuillez saisir le protocole du port !", MsgBoxStyle.Critical, "Erreur")
        '    cmbProtocole.Focus()
        '    Exit Sub
        'End If
        If chPortSerie.Checked Then
            SaveSetting("PHARMA", "PHARMA", "TypeTerminal", "PORT_SERIE")
        ElseIf chFichierTexte.Checked Then
            SaveSetting("PHARMA", "PHARMA", "TypeTerminal", "FICHIER_TEXTE")
        ElseIf chReseau.Checked Then
            SaveSetting("PHARMA", "PHARMA", "TypeTerminal", "RESEAU")
        End If

        SaveSetting("PHARMA", "PHARMA", "NomPort", tNomPort.Text)
        SaveSetting("PHARMA", "PHARMA", "VitessePort", cmbVitesse.Text)
        SaveSetting("PHARMA", "PHARMA", "PartiyPort", cmbParity.Text)
        SaveSetting("PHARMA", "PHARMA", "DataBitPort", cmbDataBit.Text)
        SaveSetting("PHARMA", "PHARMA", "StopBitPort", cmbStopBit.Text)
        SaveSetting("PHARMA", "PHARMA", "ProtocolePort", cmbProtocole.Text)


        NomPort = tNomPort.Text
        VitessePort = cmbVitesse.Text
        PartiyPort = cmbParity.Text
        DataBitPort = cmbDataBit.Text
        StopBitPort = cmbStopBit.Text
        ProtocolePort = cmbProtocole.Text


        Try
            SaveSetting("PHARMA", "PHARMA", "DossierExcel", tDossierExcel.Text)
            SaveSetting("PHARMA", "PHARMA", "FormatExcel", cmbFormatExcel.Text)

            DossierExcel = tDossierExcel.Text
            FormatExcel = cmbFormatExcel.Text
        Catch ex As Exception

        End Try


        With dsParametres.Tables("PARAMETRES")

            dr = .Rows(0)

            If tHonoraireTableauA.Text <> "" Then
                dr.Item("HonoraireDeResponsabiliteTA") = tHonoraireTableauA.Text
            Else
                dr.Item("HonoraireDeResponsabiliteTA") = "0.080"
            End If
            If tHonoraireTableauB.Text <> "" Then
                dr.Item("HonoraireDeResponsabiliteTB") = tHonoraireTableauB.Text
            Else
                dr.Item("HonoraireDeResponsabiliteTB") = "0.100"
            End If
            If tHonoraireTableauC.Text <> "" Then
                dr.Item("HonoraireDeResponsabiliteTC") = tHonoraireTableauC.Text
            Else
                dr.Item("HonoraireDeResponsabiliteTC") = "0.080"
            End If
            If tMinimumdePerception.Text <> "" Then
                dr.Item("MinimumDePerception") = tMinimumdePerception.Text
            Else
                dr.Item("MinimumDePerception") = "0.250"
            End If

            If IsNumeric(tNbreJourValiditeParDefaut.Text) = False Then
                MsgBox("Le nombre de jour de validité par défaut doit etre numérique", MsgBoxStyle.Critical)
                Exit Sub
            End If
            If tNbreJourValiditeParDefaut.Text <> CInt(tNbreJourValiditeParDefaut.Text) Then
                MsgBox("Le nombre de jour de validité par défaut doit etre numérique", MsgBoxStyle.Critical)
                Exit Sub
            End If

            If IsNumeric(tTailleCodeCNAM.Text) = False Then
                MsgBox("Le Taille Code CNAM doit etre numérique", MsgBoxStyle.Critical)
                Exit Sub
            End If
            If tTailleCodeCNAM.Text <> CInt(tTailleCodeCNAM.Text) Then
                MsgBox("Le Taille Code CNAM doit etre numérique", MsgBoxStyle.Critical)
                Exit Sub
            End If


            'If IsNumeric(tTailleCaractere.Text) = False Then
            '    MsgBox("Le Taille Code CNAM doit etre numérique", MsgBoxStyle.Critical)
            '    Exit Sub
            'End If
            'If tTailleCaractere.Text <> CInt(tTailleCaractere.Text) Then
            '    MsgBox("Le Taille Code CNAM doit etre numérique", MsgBoxStyle.Critical)
            '    Exit Sub
            'End If

            'If IsNumeric(tTailleListe.Text) = False Then
            '    MsgBox("Le Taille de Caractère doit etre numérique", MsgBoxStyle.Critical)
            '    Exit Sub
            'End If
            'If tTailleListe.Text <> CInt(tTailleListe.Text) Then
            '    MsgBox("Le Taille de Liste doit etre numérique", MsgBoxStyle.Critical)
            '    Exit Sub
            'End If

            dr.Item("CmdGroupeJournaliere") = tCommandeGroupeJ.Text

            Try
                cmd.Connection = ConnectionServeur
                cmd.CommandText = "UPDATE PARAMETRES SET NePAsSortirLesManquantsDepuis = " & Quote(tNePasSortirManquantsDepuis.Text)
                cmd.ExecuteNonQuery()
            Catch
            End Try

            dr.Item("NePAsSortirLesManquantsDepuis") = tNePasSortirManquantsDepuis.Text
            dr.Item("NomDeLordinateurDImpressionCodeABarre") = tNomOrdinateurImpressionCodeABarre.Text
            dr.Item("AfficherLesDernieresDatesDePeremptionDansLesNouveauxAchats") = chbAfficherLesDerniereDDPeremptionDansNouveauAchat.Checked

            dr.Item("AjouterTimbreAFacture") = chbAjouterMontantTimbreFacture.Checked

            dr.Item("CongeDuAnneeCourant") = dtpDebutAnneeCourant.Value
            dr.Item("CongeAuAnneeCourant") = dtpFinAnneeCourant.Value
            dr.Item("CongeDuAnneeProchaine") = dtpDebutAnneeProchaine.Value
            dr.Item("CongeAuAnneeProchaine") = dtpfinAnneeProchaine.Value

            If tFacture.Text = "" Then
                tFacture.Value = "0"
            End If
            If tBlDevis.Text = "" Then
                tBlDevis.Value = "0"
            End If
            If tOrdonnancier.Text = "" Then
                tOrdonnancier.Value = "0"
            End If
            dr.Item("NumeroSequentielFacture") = tFacture.Text
            dr.Item("NumeroSequentielDevisBL") = tBlDevis.Text
            dr.Item("NumeroSequentielOrdonnancier") = tOrdonnancier.Text


        End With

        Try
            daParametres.Update(dsParametres, "PARAMETRES")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsParametres.Reset()
            Me.init()
            Exit Sub
        End Try

        With dsParametresPharmacien.Tables("PARAMETRE_PHARMACIE")

            drPharmacien = .Rows(0)

            drPharmacien.Item("CodePharmacie") = tCodePharmacien.Text
            drPharmacien.Item("Pharmacie") = tPharmacie.Text
            drPharmacien.Item("NCnam") = tCNAM.Text

            drPharmacien.Item("Affiliation1") = tNumeroAffiliation1.Text
            drPharmacien.Item("Affiliation2") = tNumeroAffiliation2.Text
            drPharmacien.Item("Adresse") = tAdresse.Text
            drPharmacien.Item("Telephone") = tTelephone.Text

            drPharmacien.Item("Fax") = tFax.Text
            drPharmacien.Item("CodeTVA") = tCodeTva.Text
            drPharmacien.Item("Messagederoulant1") = tMessagederoulant1.Text
            drPharmacien.Item("Messagederoulant2") = tMessagederoulant2.Text
            drPharmacien.Item("SmtpMail") = tSMTPMail.Text
            drPharmacien.Item("PortMail") = tPortMail.Text
            drPharmacien.Item("AdresseMailDestinateur") = tAdresseMail.Text
            drPharmacien.Item("MotDePasseDestinateur") = tMotDePasseMail.Text
            drPharmacien.Item("AutoriserEnvoiMail") = chbAutoriseEnvoiMail.Checked
            drPharmacien.Item("NbreJourValiditeParDefaut") = CInt(tNbreJourValiditeParDefaut.Text)
            drPharmacien.Item("Latitude_Longitude") = tLatLong.Text
            drPharmacien.Item("Texte") = tTexte.Text
            drPharmacien.Item("TauxRemise") = tRemise.Text
            drPharmacien.Item("GererBon") = chbGestionBon.Checked

            drPharmacien.Item("TailleCodeCNAM") = tTailleCodeCNAM.Text

            'drPharmacien.Item("TailleListe") = tTailleListe.Text
            'drPharmacien.Item("TailleCaractere") = tTailleCaractere.Text

            drPharmacien.Item("TailleListe") = cmbListe.Text
            drPharmacien.Item("TailleCaractere") = cmbCaractere.Text
            drPharmacien.Item("PoliceCaractere") = cmbPolice.Text
            drPharmacien.Item("ActiverOMFAPCI") = chbActiverOMFAPCI.Checked

            drPharmacien.Item("NombreJoursValiditerOrdonnance") = tNbrOrdonnance.Text
            drPharmacien.Item("NombreJoursValiditerPriseEnCharge") = tNbrPriseEnCharge.Text
            drPharmacien.Item("NombreJoursValiditerAppareillage") = tNbrAppareillage.Text

            drPharmacien.Item("QuantiteMultipleDeCinq") = chbMultipleDeCinq.Checked

            drPharmacien.Item("MettreAJourPrixFrigo") = chbMettreAJourPrixFrigo.Checked
            drPharmacien.Item("AfficherReglementsSupprimes") = ChbAfficherReglementsSupprimes.Checked
            drPharmacien.Item("AutoriserSaisieNonMembeFamille") = ChbAutoriserSaisieNonMembeFamille.Checked
            drPharmacien.Item("ImprimerUnEtiquette") = ChbImprimerUnEtiquette.Checked

            ' '' ''''''''''''''
            ' ''If cmbMeme.Text <> "" Then
            ' ''    drPharmacien.Item("MemeFontAjoutConsultation") = IIf(cmbMeme.Text.ToUpper = "OUI", True, False)
            ' ''End If
            '''''''''''''''''''''
            If tTimbre.Text <> "" Then
                drPharmacien.Item("Timbre") = tTimbre.Text
            Else
                drPharmacien.Item("Timbre") = 0
            End If
            drPharmacien.Item("Rib") = tRib.Text
            Try
                drPharmacien.Item("DateMigration") = dtpDateMigration.Text
            Catch ex As Exception
            End Try

            drPharmacien.Item("NbrCommandePourClasserManquant") = IIf(tNbrCommande.Text = "", 0, tNbrCommande.Text)
            drPharmacien.Item("TypeClassementManquant") = IIf(rbManquantJour.Checked, 0, 1)

        End With

        Try
            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleArticle1.Checked) + _
                                        "WHERE " + _
                                        "ID = 1 "
            cmdParametres.ExecuteNonQuery()

            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleArticle2.Checked) + _
                                        "WHERE " + _
                                        "ID = 2 "
            cmdParametres.ExecuteNonQuery()

            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleArticle3.Checked) + _
                                        "WHERE " + _
                                        "ID = 3 "
            cmdParametres.ExecuteNonQuery()

            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleArticle4.Checked) + _
                                        "WHERE " + _
                                        "ID = 4 "
            cmdParametres.ExecuteNonQuery()

            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleArticle5.Checked) + _
                                        "WHERE " + _
                                        "ID = 5 "
            cmdParametres.ExecuteNonQuery()

            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleClient1.Checked) + _
                                        "WHERE " + _
                                        "ID = 6 "
            cmdParametres.ExecuteNonQuery()

            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleClient2.Checked) + _
                                        "WHERE " + _
                                        "ID = 7 "
            cmdParametres.ExecuteNonQuery()

            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleClient3.Checked) + _
                                        "WHERE " + _
                                        "ID = 8 "
            cmdParametres.ExecuteNonQuery()

            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleVente1.Checked) + _
                                        "WHERE " + _
                                        "ID = 9 "
            cmdParametres.ExecuteNonQuery()

            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleVente2.Checked) + _
                                        "WHERE " + _
                                        "ID = 10 "
            cmdParametres.ExecuteNonQuery()

            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleVente3.Checked) + _
                                        "WHERE " + _
                                        "ID = 11 "
            cmdParametres.ExecuteNonQuery()

            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleVente4.Checked) + _
                                        "WHERE " + _
                                        "ID = 12 "
            cmdParametres.ExecuteNonQuery()

            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleAchat1.Checked) + _
                                        "WHERE " + _
                                        "ID = 13 "
            cmdParametres.ExecuteNonQuery()

            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleAchat2.Checked) + _
                                        "WHERE " + _
                                        "ID = 14 "
            cmdParametres.ExecuteNonQuery()

            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   MODULES_SURVEILLANCE " + _
                                        "SET " + _
                                        "   ActiverSurveillance = " & Quote(ChbModuleReglement1.Checked) + _
                                        "WHERE " + _
                                        "ID = 15 "
            cmdParametres.ExecuteNonQuery()

        Catch ex As Exception

        End Try

        Try
            cmd.Connection = ConnectionServeur
            cmd.CommandText = "SELECT COUNT(*) FROM LISTE_MAIL "

            If cmd.ExecuteScalar = 0 Then

                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "INSERT INTO LISTE_MAIL (Mail, Status) " + _
                                                "VALUES (" & Quote(tMail.Text.ToString()) & ", '1') "
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                End Try
            Else
                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "UPDATE " + _
                                                "   LISTE_MAIL " + _
                                                "SET " + _
                                                "   Mail = " & Quote(tMail.Text.ToString()) + _
                                                "WHERE " + _
                                                "CodeMail = (SELECT TOP(1) CodeMail FROM LISTE_MAIL)"
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                End Try
            End If
        Catch ex As Exception
        End Try



        Try
            daParametresPharmacien.Update(dsParametresPharmacien, "PARAMETRE_PHARMACIE")
            RelectureDesParametres()
            Me.Hide()
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            dsParametresPharmacien.Reset()
            Me.init()
        End Try

        Try
            cmdParametres.Connection = ConnectionServeur
            cmdParametres.CommandText = "UPDATE " + _
                                        "   PARAMETRES " + _
                                        "SET " + _
                                        "   AjouterTimbreAFacture = " & Quote(AjouterTimbreALaFacture)
            cmdParametres.ExecuteNonQuery()
        Catch 
        End Try

    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        If dsParametres.HasChanges Then
            If MsgBox("Voulez vous vraiment annuler les modifications ?", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "Annulation") = MsgBoxResult.Yes Then
                Me.Hide()
            End If
        Else
            Me.Hide()
        End If
    End Sub

    Private Sub tTimbre_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTimbre.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tTimbre.Text), 3)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tTimbre.Text = "0.000"
            tTimbre.Focus()
            tTimbre.SelectionLength = tTimbre.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tHonoraireTableauA_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tHonoraireTableauA.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tHonoraireTableauA.Text), 3)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tHonoraireTableauA.Text = "0.000"
            tHonoraireTableauA.Focus()
            tHonoraireTableauA.SelectionLength = tHonoraireTableauA.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tHonoraireTableauB_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tHonoraireTableauB.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tHonoraireTableauB.Text), 3)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tHonoraireTableauB.Text = "0.000"
            tHonoraireTableauB.Focus()
            tHonoraireTableauB.SelectionLength = tHonoraireTableauB.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tHonoraireTableauC_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tHonoraireTableauC.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tHonoraireTableauC.Text), 3)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tHonoraireTableauC.Text = "0.000"
            tHonoraireTableauC.Focus()
            tHonoraireTableauC.SelectionLength = tHonoraireTableauC.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tMinimumdePerception_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tMinimumdePerception.KeyUp
        If tMinimumdePerception.Text = "" Then
            Exit Sub
        End If
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tMinimumdePerception.Text), 3)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tMinimumdePerception.Text = "0.000"
            tMinimumdePerception.Focus()
            tMinimumdePerception.SelectionLength = tMinimumdePerception.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tCommandeGroupeJ_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCommandeGroupeJ.KeyUp
        If tCommandeGroupeJ.Text = "" Then
            Exit Sub
        End If
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tCommandeGroupeJ.Text), 3)
        Catch ex As Exception
            MsgBox("Nombre invalide !", MsgBoxStyle.Critical, "Erreur")
            tCommandeGroupeJ.Text = "0"
            tCommandeGroupeJ.Focus()
            tCommandeGroupeJ.SelectionLength = tCommandeGroupeJ.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tNePasSortirManquantsDepuis_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNePasSortirManquantsDepuis.KeyUp
        Try   ' test si un valeur numerique ou non
            Math.Round(CDbl(tNePasSortirManquantsDepuis.Text), 3)
        Catch ex As Exception
            MsgBox("Montant invalide !", MsgBoxStyle.Critical, "Erreur")
            tNePasSortirManquantsDepuis.Text = "0.000"
            tNePasSortirManquantsDepuis.Focus()
            tNePasSortirManquantsDepuis.SelectionLength = tNePasSortirManquantsDepuis.Text.Length
            Exit Sub
        End Try
    End Sub

    Private Sub tNumeroAffiliation1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNumeroAffiliation1.KeyUp

        If IsNumeric(tNumeroAffiliation1.Text) = False And tNumeroAffiliation1.Text <> "" Then
            MsgBox("le premier numéro d'affiliation est composé de 10 chiffres !", MsgBoxStyle.Critical, "Erreur")
            If tNumeroAffiliation1.Text.Length > 0 Then
                tNumeroAffiliation1.Text = tNumeroAffiliation1.Text.Substring(0, tNumeroAffiliation1.Text.Length - 1)
            End If
            tNumeroAffiliation1.Select(tNumeroAffiliation1.Text.Length, 1)
        End If
        If (tNumeroAffiliation1.Text.Length <= 10 Or tNumeroAffiliation1.Text.Length = 0) And e.KeyCode = Keys.Enter Then
            tNumeroAffiliation2.Focus()
        End If
    End Sub

    Private Sub tNumeroAffiliation1_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tNumeroAffiliation1.LostFocus
        Dim Chaine As String = ""

        If tNumeroAffiliation1.Text.Length > 10 And tNumeroAffiliation1.Text.Length <> 0 Then
            tNumeroAffiliation1.Focus()
        ElseIf tNumeroAffiliation1.Text.Length < 10 Then
            tNumeroAffiliation1.Text = Chaine.PadLeft(10 - Len(tNumeroAffiliation1.Text), "0") + tNumeroAffiliation1.Text
        End If
    End Sub

    Private Sub tNumeroAffiliation1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles tNumeroAffiliation1.Validated
        If tNumeroAffiliation1.Text.Length <> 10 And tNumeroAffiliation1.Text.Length <> 0 Then
            tNumeroAffiliation1.Focus()
        End If
    End Sub

    Private Sub tNumeroAffiliation2_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNumeroAffiliation2.KeyUp
        'If tNumeroAffiliation2.Text.Length = 2 Then
        '    tAdresse.Focus()
        'End If
        If IsNumeric(tNumeroAffiliation2.Text) = False And tNumeroAffiliation2.Text <> "" And e.KeyCode = Keys.Enter Then
            MsgBox("le deuxième numéro d'affiliation est composé de é chiffres !", MsgBoxStyle.Critical, "Erreur")
            If tNumeroAffiliation2.Text.Length > 0 Then
                tNumeroAffiliation2.Text = tNumeroAffiliation2.Text.Substring(0, tNumeroAffiliation2.Text.Length - 1)
            End If
            tNumeroAffiliation2.Select(tNumeroAffiliation2.Text.Length, 1)
            Exit Sub
        End If
        If tNumeroAffiliation2.Text.Length = 2 And e.KeyCode = Keys.Enter Then
            tAdresse.Focus()
        End If
    End Sub

    Private Sub tCodePharmacien_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodePharmacien.KeyUp
        If e.KeyCode = Keys.Enter Then
            tPharmacie.Focus()
        End If
    End Sub

    Private Sub tPharmacie_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tPharmacie.KeyUp
        If e.KeyCode = Keys.Enter Then
            tCNAM.Focus()
        End If
    End Sub


    Private Sub tCNAM_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCNAM.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNumeroAffiliation1.Focus()
        End If
    End Sub

    Private Sub tAdresse_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tAdresse.KeyUp
        If e.KeyCode = Keys.Enter Then
            tTelephone.Focus()
        End If
    End Sub

    Private Sub tTelephone_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tTelephone.KeyUp
        If e.KeyCode = Keys.Enter Then
            tFax.Focus()
        End If
    End Sub

    Private Sub tFax_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tFax.KeyUp
        If e.KeyCode = Keys.Enter Then
            tCodeTva.Focus()
        End If
    End Sub

    Private Sub tCodeTva_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeTva.KeyUp
        If e.KeyCode = Keys.Enter Then
            tMessagederoulant1.Focus()
        End If
    End Sub

    Private Sub tMessagederoulant_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tMessagederoulant2.KeyUp
        If e.KeyCode = Keys.Enter Then
            tRib.Focus()
        End If
    End Sub

    Private Sub tRib_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRib.KeyUp
        If e.KeyCode = Keys.Enter Then
            tTimbre.Focus()
        End If
    End Sub

    Private Sub tSMTPMail_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tSMTPMail.KeyUp
        If e.KeyCode = Keys.Enter Then
            tPortMail.Focus()
        End If
    End Sub

    Private Sub tPortMail_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tPortMail.KeyUp
        If e.KeyCode = Keys.Enter Then
            tAdresseMail.Focus()
        End If
    End Sub

    Private Sub tAdresseMail_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tAdresseMail.KeyUp
        If e.KeyCode = Keys.Enter Then
            tMotDePasseMail.Focus()
        End If
    End Sub

    Private Sub bEnvoiLog_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bEnvoiLog.Click
        'pour envoyer asynchrone l'e-mail
        sendEmailEnvoyer("Fichier de suivi des Erreurs de la pharmacie :'" + Pharmacie + "'")


        If fichierselectionne = True Then   'si il y a un fichier sélectionné

            pbTestEnvoiFichierLog.Visible = True
            CheckedListBox1.Enabled = False
            bEnvoiLog.Enabled = False
            bTestEmail.Enabled = False
            LTestEnvoi.Text = ""

        Else       'si aucun fichier sélectionné

            LTestEnvoi.Text = ""
            Exit Sub

        End If

    End Sub

    Private Sub chbAutoriseEnvoiMail_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbAutoriseEnvoiMail.CheckedChanged

        'Test sur l'autorisaion d'envoi de mail est cochée
        If chbAutoriseEnvoiMail.Checked Then

            gPramatresMail.Enabled = True
            gEnvoiLog.Enabled = True

        Else

            gPramatresMail.Enabled = False
            gEnvoiLog.Enabled = False

        End If

    End Sub

    Private Sub bTestEmail_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bTestEmail.Click
        Dim test As Boolean = False


        If tSMTPMail.Text = "" Then

            LTestSMTPMail.Text = "Vous devez remplir : SMTP Mail "

            LTestEnvoiEmail.Text = ""

            pbTestEnvoiEmail.Visible = False

        Else
            LTestSMTPMail.Text = ""

        End If

        If tPortMail.Text = "" Then

            LTestPort.Text = "Vous devez remplir : Port "

            LTestEnvoiEmail.Text = ""

            pbTestEnvoiEmail.Visible = False

            bTestEmail.Enabled = True

        Else
            LTestPort.Text = ""

        End If

        If tAdresseMail.Text = "" Then

            LTestMail.Text = "Vous devez remplir : Mail"

            LTestEnvoiEmail.Text = ""

            pbTestEnvoiEmail.Visible = False


        Else
            LTestMail.Text = ""

        End If

        If tMotDePasseMail.Text = "" Then

            LTestMoTdePass.Text = "Vous devez remplir : Mot de passe"

            LTestEnvoiEmail.Text = ""

            pbTestEnvoiEmail.Visible = False

        Else
            LTestMoTdePass.Text = ""

        End If

        If tSMTPMail.Text <> "" And tPortMail.Text <> "" And tAdresseMail.Text <> "" And tMotDePasseMail.Text <> "" Then

            test = True

        End If

        If test = True Then

            LTestEnvoiEmail.Text = ""

            pbTestEnvoiEmail.Visible = True

            bTestEmail.Enabled = False

            bEnvoiLog.Enabled = False

            sendEmailTest("Test d'Envoi :'" + Pharmacie + "' ")

        End If
    End Sub

    Public Function sendEmailTest(Optional ByVal msg As String = "") As Boolean
        'Declaration
        Dim avecFichierLog As Boolean = True
        Dim mailSent As Boolean = False
        Dim mail As Net.Mail.MailMessage = Nothing
        Dim smtp As Net.Mail.SmtpClient = Nothing

        'lorsque en clique sur le btn Tester
        sendEmail = False

        Try

            mail = New Net.Mail.MailMessage

            mail.From = New Net.Mail.MailAddress(tAdresseMail.Text)

            mail.Priority = Net.Mail.MailPriority.High

            mail.To.Add(tAdresseMail.Text)

            mail.Subject = "Test d'Envoi"

            mail.Body = msg

            smtp = New Net.Mail.SmtpClient(tSMTPMail.Text)

            smtp.Port = tPortMail.Text

            smtp.Credentials = New System.Net.NetworkCredential(tAdresseMail.Text, tMotDePasseMail.Text)

            smtp.EnableSsl = True

            AddHandler smtp.SendCompleted, AddressOf SendCompletedCallback

            smtp.SendAsync(mail, mail)

            mailSent = True


        Catch ex As Exception

            Debug.Print(ex.Message)

        End Try

        If mail IsNot Nothing Then

            mail = Nothing

        End If

        If smtp IsNot Nothing Then

            smtp = Nothing

        End If

        Return mailSent


    End Function

    Public Function sendEmailEnvoyer(Optional ByVal msg As String = "") As Boolean

        'Declaration
        Dim avecFichierLog As Boolean = True
        Dim fichierLogJoint As System.Net.Mail.Attachment
        Dim mailSent As Boolean = False
        Dim mail As Net.Mail.MailMessage = Nothing
        Dim smtp As Net.Mail.SmtpClient = Nothing
        Dim fichierLog As String
        Dim fichiers As String

        'lorsque en clique sur le btn Envoyer
        sendEmail = True

        Dim nbreFichier As Integer = 0

        For i = 0 To CheckedListBox1.Items.Count - 1

            If CheckedListBox1.GetItemChecked(i) Then

                fichierselectionne = True

                nbreFichier = nbreFichier + 1

                Try
                    'pour indiquer le nombre total de liste


                    fichiers = CheckedListBox1.Items(i)

                    fichierLog = Environment.CurrentDirectory & "\" & "Log\Fichier\" + fichiers

                    fichierLogJoint = New System.Net.Mail.Attachment(fichierLog)


                    mail = New Net.Mail.MailMessage

                    mail.From = New Net.Mail.MailAddress(tAdresseMail.Text)

                    mail.Priority = Net.Mail.MailPriority.High

                    mail.To.Add(ModuleGeneral.adresseMailDestinataire)

                    mail.Subject = "Fichier des Erreurs"

                    mail.Body = msg

                    mail.Attachments.Add(fichierLogJoint)

                    smtp = New Net.Mail.SmtpClient(tSMTPMail.Text)

                    smtp.Port = tPortMail.Text

                    smtp.Credentials = New System.Net.NetworkCredential(tAdresseMail.Text, tMotDePasseMail.Text)

                    smtp.EnableSsl = True

                    AddHandler smtp.SendCompleted, AddressOf SendCompletedCallback

                    smtp.SendAsync(mail, mail)

                    mailSent = True


                Catch ex As Exception

                    Debug.Print(ex.Message)

                End Try

                If mail IsNot Nothing Then

                    mail = Nothing

                End If

                If smtp IsNot Nothing Then

                    smtp = Nothing

                End If

                Return mailSent

            End If

        Next

        If nbreFichier = 0 Then
            MsgBox("aucun fichier sélectionné", MsgBoxStyle.Information)
            fichierselectionne = False
            Exit Function
        End If

    End Function

    Private Sub SendCompletedCallback(ByVal sender As Object, ByVal e As AsyncCompletedEventArgs)
        Dim myform As New fMain
        Try

            Dim test As Boolean = False

            Dim i As Integer

            Dim mail As Net.Mail.MailMessage = CType(e.UserState, Net.Mail.MailMessage)

            'pour tester l'envoi email asynchrone
            If e.Cancelled Then

                Throw New Exception("Send mail got cancelled")

            ElseIf e.Error IsNot Nothing Then

                Throw e.Error

            End If

            For i = (mail.Attachments.Count - 1) To 0 Step -1

                mail.Attachments(i).Dispose()

            Next

            mail.Dispose()

            'si l'e-mail a été envoyé avec succès
            RemoveHandler CType(sender, Net.Mail.SmtpClient).SendCompleted, AddressOf SendCompletedCallback

            'lorsque en clique sur le btn Envoyer
            If sendEmail = True Then

                LTestEnvoi.ForeColor = Color.Green
                LTestEnvoi.Text = "Votre E-mail à été envoyé avec succès"
                pbTestEnvoiFichierLog.Visible = False
                CheckedListBox1.Enabled = True
                bEnvoiLog.Enabled = True
                bTestEmail.Enabled = True

                'lorsque en clique sur le btn Tester
            Else

                LTestEnvoiEmail.ForeColor = Color.Green
                LTestEnvoiEmail.Text = "Votre E-mail à été envoyé avec succès"
                pbTestEnvoiEmail.Visible = False
                bTestEmail.Enabled = True
                bEnvoiLog.Enabled = True

            End If
            myform.AfficheMessageNotification(True)

            'si il'y a une erreur d'envoi
        Catch ex As Exception

            'lorsque en clique sur le btn Envoyer
            If sendEmail = True Then

                LTestEnvoi.ForeColor = Color.Red
                LTestEnvoi.Text = "Erreur d'Envoi, vous pouvez vérifier les paramètres de la messagerie"
                pbTestEnvoiFichierLog.Visible = False
                CheckedListBox1.Enabled = True
                bEnvoiLog.Enabled = True
                bTestEmail.Enabled = True

                'lorsque en clique sur le btn Tester
            Else

                LTestEnvoiEmail.ForeColor = Color.Red
                pbTestEnvoiEmail.Visible = False
                LTestEnvoiEmail.Text = "Erreur d'Envoi, vous pouvez vérifier les paramètres de la messagerie"
                bTestEmail.Enabled = True
                bEnvoiLog.Enabled = True

            End If
            myform.AfficheMessageNotification(False)
        End Try

    End Sub

    Private Sub fParametresGeneraux_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Me.Load
        'declaration
        Dim nbre As Integer



        '----Gestion des erreurs
        Dim fichierLog As String = Environment.CurrentDirectory & "\" & "Log\Fichier\"

        Dim listedossier As String() = Directory.GetFiles(fichierLog)

        For Each dossier As String In listedossier

            Me.CheckedListBox1.Items.Add(Microsoft.VisualBasic.Right(dossier, (Len(dossier) - Len(fichierLog))))

        Next

        'pour checked le dernier element
        nbre = CheckedListBox1.Items.Count - 1
        CheckedListBox1.SetItemChecked(nbre, True)

        initArticle()
        initArticleSurveillerDetails()
        initgArticles()


    End Sub

    Private Sub initArticle()

        'chargement des noms Articles
        StrSQL = "SELECT DISTINCT Designation FROM ARTICLE  ORDER BY Designation ASC"
        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "ARTICLE_DESIGNATION_LISTE")
        cmbArticle.DataSource = dsArticle.Tables("ARTICLE_DESIGNATION_LISTE")
        cmbArticle.DisplayMember = "Designation"
        cmbArticle.ColumnHeaders = False
        cmbArticle.Splits(0).DisplayColumns("Designation").Width = 10
        cmbArticle.ExtendRightColumn = True

    End Sub

    'chargement des détails des productions 
    Sub initArticleSurveillerDetails()

        If dsArticle.Tables("ARTICLE_SURVEILLER") IsNot Nothing Then
            dsArticle.Tables("ARTICLE_SURVEILLER").Clear()
        End If
        StrSQL = "SELECT  NumeroArticleSurveiller," + _
                 "CodeArticle," + _
                 "CodeABarre," + _
                 "Designation," + _
                 "ScannerOrdonnance," + _
                 "CapturerPhoto," + _
                 "EnvoyerNotification " + _
                 " FROM  ARTICLE_SURVEILLER  "

        cmdArticle.Connection = ConnectionServeur
        cmdArticle.CommandText = StrSQL
        daArticle = New SqlDataAdapter(cmdArticle)
        daArticle.Fill(dsArticle, "ARTICLE_SURVEILLER")
        cbArticle = New SqlCommandBuilder(daArticle)

    End Sub
    'pour initialiser gArticles
    Private Sub initgArticles()

        Dim I As Integer
        With gArticle
            .Columns.Clear()
            Try
                .DataSource = dsArticle
            Catch ex As Exception
            End Try
            .DataMember = "ARTICLE_SURVEILLER"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("ScannerOrdonnance").Caption = "Scanner Ordonnance"
            .Columns("CapturerPhoto").Caption = "Capturer Photo"
            .Columns("EnvoyerNotification").Caption = "Envoyer Notification"

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near


            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near

            .Splits(0).DisplayColumns("NumeroArticleSurveiller").Width = 0
            .Splits(0).DisplayColumns("NumeroArticleSurveiller").Visible = False
            .Splits(0).DisplayColumns("NumeroArticleSurveiller").AllowSizing = False

            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 60
            .Splits(0).DisplayColumns("Designation").Width = 260

            .Splits(0).DisplayColumns("ScannerOrdonnance").Width = 120
            .Splits(0).DisplayColumns("CapturerPhoto").Width = 120
            .Splits(0).DisplayColumns("EnvoyerNotification").Width = 120




            'Couleur de la Grid
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.BackColor = Color.MistyRose '
            Next

            '.Splits(0).DisplayColumns("CodeABarre").Style.BackColor = Color.Linen
            '.Splits(0).DisplayColumns("Designation").Style.BackColor = Color.Linen


            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone

        End With
    End Sub


    Private Sub bConfirmerP_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmerP.Click
        Dim ArticleValide As Integer = 0
        Dim CodeArticle As String = ""
        Dim Designation As String = ""
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""
        Dim ArticleSurveiller As Integer = 0

        If tCode.Text = "" Then
            MsgBox("Code article vide !", MsgBoxStyle.Critical, "Erreur")
            tCode.Focus()
            Exit Sub
        End If

        If cmbArticle.Text = "" Then
            MsgBox("Désignation article vide !", MsgBoxStyle.Critical, "Erreur")
            tCode.Focus()
            Exit Sub
        End If

        If chbScannerOrdonnance.Checked = False And chbEnvoyerNotification.Checked = False And chbCapturerPhoto.Checked = False Then
            MsgBox("Vous devez choisir au moins un outil de surveillance !", MsgBoxStyle.Critical, "Erreur")
            tCode.Focus()
            Exit Sub
        End If

        'test si l'article est existe ou nn
        If dsArticle.Tables("VERIFICATION_ARTICLE") IsNot Nothing Then
            dsArticle.Tables("VERIFICATION_ARTICLE").Clear()
        End If

        Try
            StrSQL = "select CodeABarre from ARTICLE"
            cmdArticle.Connection = ConnectionServeur
            cmdArticle.CommandText = StrSQL
            daArticle = New SqlDataAdapter(cmdArticle)
            daArticle.Fill(dsArticle, "VERIFICATION_ARTICLE")
            cbArticle = New SqlCommandBuilder(daArticle)

            For i = 0 To dsArticle.Tables("VERIFICATION_ARTICLE").Rows.Count - 1
                If tCode.Value = dsArticle.Tables("VERIFICATION_ARTICLE").Rows(i).Item("CodeABarre") Then
                    ArticleValide = ArticleValide + 1
                End If
            Next

        Catch ex As Exception
            WriteLine(ex.Message)
        End Try

        If ArticleValide = 0 Then
            LArticleValide.Visible = True
            LArticleValide.Text = "Code article non valide"
            tCode.Focus()
            Exit Sub
        End If
        LArticleValide.Visible = False

        '----test si l'article est déja surveiller
        If Mode = "Ajout" Then

            If dsArticle.Tables("VERIFICATION_ARTICLE_SURVEILLER") IsNot Nothing Then
                dsArticle.Tables("VERIFICATION_ARTICLE_SURVEILLER").Clear()
            End If

            Try
                StrSQL = "select CodeABarre from ARTICLE_SURVEILLER"
                cmdArticle.Connection = ConnectionServeur
                cmdArticle.CommandText = StrSQL
                daArticle = New SqlDataAdapter(cmdArticle)
                daArticle.Fill(dsArticle, "VERIFICATION_ARTICLE_SURVEILLER")
                cbArticle = New SqlCommandBuilder(daArticle)

                For i = 0 To dsArticle.Tables("VERIFICATION_ARTICLE_SURVEILLER").Rows.Count - 1
                    If tCode.Value = dsArticle.Tables("VERIFICATION_ARTICLE_SURVEILLER").Rows(i).Item("CodeABarre") Then
                        ArticleSurveiller = ArticleSurveiller + 1
                    End If
                Next

            Catch ex As Exception
                WriteLine(ex.Message)
            End Try

            If ArticleSurveiller > 0 Then
                MsgBox("L'article ' " + cmbArticle.Text + " ' est dèja surveillé !", MsgBoxStyle.Critical, "Erreur")
                tCode.Focus()
                Exit Sub
            End If

        End If


        'test si la désignation est valide
        CodeArticle = RecupererValeurExecuteScalaire("CodeArticle", " ARTICLE", "CodeABarre", tCode.Value)
        Designation = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)

        If Designation <> cmbArticle.Text Then
            MsgBox("Désignation article Non Valide !", MsgBoxStyle.Critical, "Erreur")
            tCode.Focus()
            Exit Sub
        End If

        '------------------------------ demande du mot de passe
        Dim myMotDePasse As New fMotDePasse
        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If

        If dsArticle.Tables("ARTICLE_SURVEILLER_INSERTION") IsNot Nothing Then
            dsArticle.Tables("ARTICLE_SURVEILLER_INSERTION").Clear()
        End If

        If Mode = "Ajout" Then

            '----------insertion dans la table Article Surveiller
            StrSQL = "SELECT  * FROM  ARTICLE_SURVEILLER  "

            cmdArticle.Connection = ConnectionServeur
            cmdArticle.CommandText = StrSQL
            daArticle = New SqlDataAdapter(cmdArticle)
            daArticle.Fill(dsArticle, "ARTICLE_SURVEILLER_INSERTION")
            cbArticle = New SqlCommandBuilder(daArticle)

            With dsArticle
                dr = .Tables("ARTICLE_SURVEILLER_INSERTION").NewRow

                dr.Item("CodeArticle") = CodeArticle

                dr.Item("CodeABarre") = tCode.Value

                dr.Item("Designation") = cmbArticle.Text

                dr.Item("ScannerOrdonnance") = chbScannerOrdonnance.Checked

                dr.Item("CapturerPhoto") = chbCapturerPhoto.Checked

                dr.Item("EnvoyerNotification") = chbEnvoyerNotification.Checked

                .Tables("ARTICLE_SURVEILLER_INSERTION").Rows.Add(dr)

            End With

        ElseIf Mode = "Modif" Then

            '----------insertion dans la table Article Surveiller
            StrSQL = "SELECT  * FROM  ARTICLE_SURVEILLER where CodeABarre='" + gArticle.Columns("CodeABarre").Value + "'  "

            cmdArticle.Connection = ConnectionServeur
            cmdArticle.CommandText = StrSQL
            daArticle = New SqlDataAdapter(cmdArticle)
            daArticle.Fill(dsArticle, "ARTICLE_SURVEILLER_INSERTION")
            cbArticle = New SqlCommandBuilder(daArticle)

            With dsArticle.Tables("ARTICLE_SURVEILLER_INSERTION")
                dr = .Rows(0)

                dr.Item("CodeArticle") = CodeArticle

                dr.Item("CodeABarre") = tCode.Value

                dr.Item("Designation") = cmbArticle.Text

                dr.Item("ScannerOrdonnance") = chbScannerOrdonnance.Checked

                dr.Item("CapturerPhoto") = chbCapturerPhoto.Checked

                dr.Item("EnvoyerNotification") = chbEnvoyerNotification.Checked

            End With

        End If

        daArticle.Update(dsArticle, "ARTICLE_SURVEILLER_INSERTION")

        '------Vider les champs
        tCode.Text = ""
        cmbArticle.Text = ""
        chbScannerOrdonnance.Checked = False
        chbCapturerPhoto.Checked = False
        chbEnvoyerNotification.Checked = False

        initArticleSurveillerDetails()
        initgArticles()
        GBProduitSurveiller.Visible = False
    End Sub

    Public Sub ChargerDetailArticleCodeABarre(ByVal CodeABarre As String)
        Dim resultat As String
        Dim CodeArticle As String = ""
        Dim CodeForme As String = ""
        Try

            CodeArticle = RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "CodeABarre", CodeABarre)
            resultat = RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle)
            If resultat <> "" Then

                tCode.Value = CodeABarre
                cmbArticle.Text = RecupererValeurExecuteScalaire("Designation", " ARTICLE", "CodeArticle", CodeArticle)

            Else
                tCode.Value = ""
                cmbArticle.Text = ""


            End If
        Catch ex As Exception

        End Try
    End Sub

    Public Sub ChargerDetailArticleDesignation(ByVal Designation As String)
        Dim resultat As String
        Dim CodeArticle As String = ""
        Dim CodeForme As String = ""
        Try

            CodeArticle = RecupererValeurExecuteScalaire("CodeArticle", "ARTICLE", "designation", Designation)
            resultat = RecupererValeurExecuteScalaire("designation", "ARTICLE", "CodeArticle", CodeArticle)
            If resultat <> "" Then

                tCode.Value = RecupererValeurExecuteScalaire("CodeABarre", " ARTICLE", "CodeArticle", CodeArticle)
                cmbArticle.Text = Designation

            Else
                tCode.Value = ""
                cmbArticle.Text = ""


            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub tCode_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCode.KeyUp
        If e.KeyCode = Keys.Enter Then
            ChargerDetailArticleCodeABarre(tCode.Text)
            cmbArticle.Focus()
        End If
    End Sub

    Private Sub cmbArticle_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbArticle.KeyUp
        Try
            If e.KeyCode = Keys.Enter Then
                cmbArticle.Text = cmbArticle.WillChangeToText
                ChargerDetailArticleDesignation(cmbArticle.Text)

                chbScannerOrdonnance.Focus()
            Else
                cmbArticle.OpenCombo()
            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub chbScannerOrdonnance_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles chbScannerOrdonnance.KeyUp
        If e.KeyCode = Keys.Enter Then
            chbCapturerPhoto.Focus()
        End If
    End Sub

    Private Sub chbCapturerPhoto_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles chbCapturerPhoto.KeyUp
        If e.KeyCode = Keys.Enter Then
            chbEnvoyerNotification.Focus()
        End If
    End Sub

    Private Sub bAjouterP_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterP.Click
        Mode = "Ajout"
        GBProduitSurveiller.Visible = True

        '------Vider les champs
        tCode.Text = ""
        cmbArticle.Text = ""
        chbScannerOrdonnance.Checked = False
        chbCapturerPhoto.Checked = False
        chbEnvoyerNotification.Checked = False

    End Sub

    Private Sub bModifierP_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bModifierP.Click
        Mode = "Modif"
        GBProduitSurveiller.Visible = True

        tCode.Text = gArticle.Columns("CodeABarre").Value
        cmbArticle.Text = gArticle.Columns("Designation").Value
        chbScannerOrdonnance.Checked = gArticle.Columns("ScannerOrdonnance").Value
        chbCapturerPhoto.Checked = gArticle.Columns("CapturerPhoto").Value
        chbEnvoyerNotification.Checked = gArticle.Columns("EnvoyerNotification").Value

    End Sub

    Private Sub bAnnulerP_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnulerP.Click

        '------Vider les champs
        GBProduitSurveiller.Visible = False

        tCode.Value = ""
        cmbArticle.Text = ""
        chbScannerOrdonnance.Checked = False
        chbCapturerPhoto.Checked = False
        chbEnvoyerNotification.Checked = False

    End Sub

    Private Sub bSupprimerP_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bSupprimerP.Click

        '------Vider les champs
        GBProduitSurveiller.Visible = False

        tCode.Value = ""
        cmbArticle.Text = ""
        chbScannerOrdonnance.Checked = False
        chbCapturerPhoto.Checked = False
        chbEnvoyerNotification.Checked = False

        If MsgBox("Voulez-vous supprimer l'Article :  '" + gArticle.Columns("Designation").Value + "' de la liste des Articles surveillés ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Photo") = MsgBoxResult.Yes Then

            StrSQL = "delete from ARTICLE_SURVEILLER where CodeABarre ='" + gArticle.Columns("CodeABarre").Value + "'"

            cmdArticle.Connection = ConnectionServeur
            cmdArticle.CommandText = StrSQL

            Try
                cmdArticle.ExecuteNonQuery()
            Catch ex As Exception
                WriteLine(ex.Message)
            End Try


        End If
        initArticleSurveillerDetails()
        initgArticles()

    End Sub

    Private Sub bQuitterP_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitterP.Click
        Me.Close()
    End Sub

    Private Sub cmbCaractere_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles cmbCaractere.KeyPress
        e.Handled = True
    End Sub

    Private Sub cmbListe_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles cmbListe.KeyPress
        e.Handled = True
    End Sub

    Private Sub cmbPolice_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles cmbPolice.KeyPress
        e.Handled = True
    End Sub

    Private Sub chPortSerie_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chPortSerie.CheckedChanged
        If chPortSerie.Checked Then
            GroupBoxTerminal.Enabled = True
        End If
    End Sub

    Private Sub chFichierTexte_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chFichierTexte.CheckedChanged
        If chFichierTexte.Checked Then
            GroupBoxTerminal.Enabled = False
        End If
    End Sub

    Private Sub chReseau_CheckedChanged(sender As Object, e As System.EventArgs) Handles chReseau.CheckedChanged
        If chReseau.Checked Then
            GroupBoxTerminal.Enabled = False
        End If
    End Sub

    Private Sub Label63_Click(sender As System.Object, e As System.EventArgs) Handles Label63.Click

    End Sub

    Private Sub rbManquantJour_CheckedChanged(sender As Object, e As EventArgs) Handles rbManquantJour.CheckedChanged
        Label22.Enabled = True
        tNePasSortirManquantsDepuis.Enabled = True
        Label26.Enabled = True

        Label66.Enabled = False
        tNbrCommande.Enabled = False
        Label67.Enabled = False
    End Sub

    Private Sub rbManquantNbrCommande_CheckedChanged(sender As Object, e As EventArgs) Handles rbManquantNbrCommande.CheckedChanged
        Label22.Enabled = False
        tNePasSortirManquantsDepuis.Enabled = False
        Label26.Enabled = False

        Label66.Enabled = True
        tNbrCommande.Enabled = True
        Label67.Enabled = True
    End Sub
End Class