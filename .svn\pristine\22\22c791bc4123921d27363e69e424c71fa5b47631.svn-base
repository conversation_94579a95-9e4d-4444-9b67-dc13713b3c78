﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fLaboratoire

    Dim cmdLaboratoire As New SqlCommand
    Dim daLaboratoire As New SqlDataAdapter
    Dim cbLaboratoire As New SqlCommandBuilder
    Dim dsLaboratoire As New DataSet

    Dim xLaboratoire As Integer
    Dim ModeLaboratoire As String
    Dim NomLaboratoire As String

    Dim dsRecupereNum As New DataSet
    Dim cmdRecupereNum As New SqlCommand
    Dim daRecupereNumt As New SqlDataAdapter

    Dim CodeExiste As Boolean = False

    Public Sub Init()
        afficherLaboratoire()
    End Sub
    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub
    Public Sub afficherLaboratoire()
        Dim I As Integer
        Dim Cond As String = "1=1 and CodeLabo <> 0"
        dsLaboratoire.Clear()

        If tRecherche.Text <> "" Then
            Cond += " and NomLabo like " + Quote("%" + tRecherche.Text + "%")
        End If
        cmdLaboratoire.CommandText = " SELECT * " + _
                                    " FROM LABORATOIRE as LABORATOIRE_LISTE where SupprimeLabo=0 AND " + Cond + _
                                    " ORDER BY CodeLabo"

        cmdLaboratoire.Connection = ConnectionServeur
        daLaboratoire = New SqlDataAdapter(cmdLaboratoire)
        daLaboratoire.Fill(dsLaboratoire, "LABORATOIRE_LISTE")

        With gLaboratoire
            .Columns.Clear()
            .DataSource = dsLaboratoire
            .DataMember = "LABORATOIRE_LISTE"
            .Rebind(False)
            .Columns("CodeLabo").Caption = "Code du Laboratoire"
            .Columns("NomLabo").Caption = "Nom du Laboratoire"
            .Columns("Adresse").Caption = "Adresse"
            .Columns("Tel").Caption = "Tel"
            .Columns("Fax").Caption = "Fax"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("CodeLabo").Width = 80
            .Splits(0).DisplayColumns("CodeLabo").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("NomLabo").Width = 330
            .Splits(0).DisplayColumns("Tel").Width = 100
            .Splits(0).DisplayColumns("Tel").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Fax").Width = 100
            .Splits(0).DisplayColumns("Fax").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Adresse").Width = 200
            .Splits(0).DisplayColumns("SupprimeLabo").Visible = False
            .Splits(0).DisplayColumns("CodeLabo").Locked = True
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gLaboratoire)
        End With
        gLaboratoire.MoveRelative(xLaboratoire)
        cbLaboratoire = New SqlCommandBuilder(daLaboratoire)
    End Sub

    Private Sub bAjouterLaboratoire_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAjouterLaboratoire.Click

        ModeLaboratoire = "Ajout"

        tCodeLaboratoire.Value = ""
        tNomLaboratoire.Value = ""
        tTelLaboratoire.Value = ""
        tFaxLaboratoire.Value = ""
        tAdresseLaboratoire.Value = ""
        tCodeLaboratoire.Enabled = True
        tNomLaboratoire.Enabled = True
        tTelLaboratoire.Enabled = True
        tFaxLaboratoire.Enabled = True
        tAdresseLaboratoire.Enabled = True


        'Dim dr As DataRow
        'Dim StrSQL As String = ""

        'If tCodeLaboratoire.Text = "" Then
        '    MsgBox("Veuillez saisir le Code du Laboratoire !", MsgBoxStyle.Critical, "Erreur")
        '    tCodeLaboratoire.Focus()
        '    Exit Sub
        'End If
        'If tNomLaboratoire.Text = "" Then
        '    MsgBox("Veuillez saisir le nom du Laboratoire !", MsgBoxStyle.Critical, "Erreur")
        '    tNomLaboratoire.Focus()
        '    Exit Sub
        'End If

        'If CodeExiste = True Then
        '    MsgBox("Code laboratoire existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
        '    tCodeLaboratoire.Focus()
        '    Exit Sub
        'End If

        'With dsLaboratoire
        '    dr = .Tables("LABORATOIRE_LISTE").NewRow
        '    dr.Item("CodeLabo") = tCodeLaboratoire.Text
        '    dr.Item("NomLabo") = tNomLaboratoire.Text
        '    dr.Item("Adresse") = tAdresseLaboratoire.Text
        '    dr.Item("tel") = tTelLaboratoire.Text
        '    dr.Item("Fax") = tFaxLaboratoire.Text
        '    .Tables("LABORATOIRE_LISTE").Rows.Add(dr)
        'End With

        'Try
        '    daLaboratoire.Update(dsLaboratoire, "LABORATOIRE_LISTE")
        '    afficherLaboratoire()
        'Catch ex As Exception
        '    MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
        '    dsLaboratoire.Reset()
        'End Try
        'tCodeLaboratoire.Text = ""
        'tNomLaboratoire.Text = ""
        'tTelLaboratoire.Text = ""
        'tFaxLaboratoire.Text = ""
        'tAdresseLaboratoire.Text = ""
    End Sub

    Private Sub cSupprimerBanque_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cSupprimerLaboratoire.Click
        Dim cmd As New SqlCommand
        Dim NbArticle As Integer = 0
        If gLaboratoire.RowCount > 0 Then
            If MsgBox("Voulez vous vraiment supprimer ce Laboratoire " + Quote(gLaboratoire(gLaboratoire.Row, "NomLabo")) + " ?", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "Suppression") = MsgBoxResult.Yes Then

                'cmd.Connection = ConnectionServeur
                'cmd.CommandText = "SELECT COUNT(CodeArticle) FROM ARTICLE " + _
                '        "WHERE CodeLabo =" + Quote(gLaboratoire(gLaboratoire.Row, "CodeLabo")) + ""

                'Try
                '    NbArticle = cmd.ExecuteScalar()
                'Catch ex As Exception
                '    Console.WriteLine(ex.Message)
                'End Try

                'If NbArticle <> 0 Then
                '    MsgBox("Il ya des articles affectés a ce laboratoire , Suppression réfusé !", MsgBoxStyle.Critical, "Erreur")
                '    Exit Sub
                'End If

                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "UPDATE LABORATOIRE SET SupprimeLabo=1 WHERE CodeLabo =" + Quote(gLaboratoire(gLaboratoire.Row, "CodeLabo"))
                    cmd.ExecuteNonQuery()
                    afficherLaboratoire()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
            End If
        End If
    End Sub

    Private Sub gBanque_AfterColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles gLaboratoire.AfterColEdit
        Dim dr As DataRow
        Dim StrSQL As String = ""

        With dsLaboratoire.Tables("LABORATOIRE")
            dr = .Rows(0)
            dr.Item("NomLabo") = gLaboratoire(gLaboratoire.Row, "NomLabo")
            dr.Item("Tel") = gLaboratoire(gLaboratoire.Row, "Tel")
            dr.Item("Fax") = gLaboratoire(gLaboratoire.Row, "Fax")
            dr.Item("Adresse") = gLaboratoire(gLaboratoire.Row, "Adresse")

        End With
        Try
            daLaboratoire.Update(dsLaboratoire, "LABORATOIRE")
        Catch ex As Exception
            MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
            afficherLaboratoire()
        End Try
    End Sub

    Private Sub gBanque_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles gLaboratoire.Click
        Dim StrSQL As String = ""
        NomLaboratoire = Quote(gLaboratoire(gLaboratoire.Row, "NomLabo"))
        If NomLaboratoire = "" Then
            MsgBox("Veuillez sélectionner le nom du Laboratoire !", MsgBoxStyle.Critical, "Erreur")
            gLaboratoire.Focus()
            Exit Sub
        End If

        If (dsLaboratoire.Tables.IndexOf("LABORATOIRE") > -1) Then
            dsLaboratoire.Tables("LABORATOIRE").Clear()
        End If

        StrSQL = " SELECT * FROM LABORATOIRE WHERE NomLabo = " + NomLaboratoire

        cmdLaboratoire.Connection = ConnectionServeur
        cmdLaboratoire.CommandText = StrSQL
        daLaboratoire = New SqlDataAdapter(cmdLaboratoire)
        daLaboratoire.Fill(dsLaboratoire, "LABORATOIRE")
        cbLaboratoire = New SqlCommandBuilder(daLaboratoire)
    End Sub

    Private Sub tCodeLaboratoire_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tCodeLaboratoire.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNomLaboratoire.Focus()
        End If
    End Sub

    Private Sub tCodeLaboratoire_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles tCodeLaboratoire.LostFocus
        lTest.Visible = False
    End Sub

    Private Sub tCodeBanque_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tCodeLaboratoire.TextChanged
        If tCodeLaboratoire.Text <> "" Then
            If IsNumeric(tCodeLaboratoire.Text.Substring(Len(tCodeLaboratoire.Text) - 1, 1)) = False Then
                MsgBox("Veuillez introduire des chiffres !", MsgBoxStyle.Critical, "Erreur")
                tCodeLaboratoire.Text = tCodeLaboratoire.Text.Substring(0, Len(tCodeLaboratoire.Text) - 1)
                tCodeLaboratoire.Select(Len(tCodeLaboratoire.Text), 0)
            End If
        End If

        Dim StrSQLtest As String = ""
        If (dsRecupereNum.Tables.IndexOf("LABORATOIRE_TEST") > -1) Then
            dsRecupereNum.Tables("LABORATOIRE_TEST").Clear()
        End If


        StrSQLtest = " SELECT * FROM LABORATOIRE as LABORATOIRE_TEST WHERE CodeLabo=" + Quote(tCodeLaboratoire.Text)
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQLtest
        daRecupereNumt = New SqlDataAdapter(cmdRecupereNum)
        daRecupereNumt.Fill(dsRecupereNum, "LABORATOIRE_TEST")

        If dsRecupereNum.Tables("LABORATOIRE_TEST").Rows.Count <> 0 Then
            lTest.Text = "Code non valide déja existe"
            lTest.ForeColor = Color.OrangeRed
            lTest.Visible = True
            CodeExiste = True
        Else
            lTest.Text = "Code valide"
            lTest.ForeColor = Color.LawnGreen
            lTest.Visible = True
            CodeExiste = False
        End If
        If tCodeLaboratoire.Text = "" Then
            lTest.Visible = False
        End If
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub bModifierForme_Click(sender As Object, e As EventArgs) Handles bModifierForme.Click
        ModeLaboratoire = "Modif"

        tNomLaboratoire.Value = gLaboratoire(gLaboratoire.Row, "NomLabo")
        tCodeLaboratoire.Value = gLaboratoire(gLaboratoire.Row, "CodeLabo")
        tTelLaboratoire.Value = gLaboratoire(gLaboratoire.Row, "Tel")
        tFaxLaboratoire.Value = gLaboratoire(gLaboratoire.Row, "Fax")
        tAdresseLaboratoire.Value = gLaboratoire(gLaboratoire.Row, "Adresse")

        tNomLaboratoire.Enabled = True
        tTelLaboratoire.Enabled = True
        tFaxLaboratoire.Enabled = True
        tAdresseLaboratoire.Enabled = True
        tCodeLaboratoire.Enabled = False

        lTest.Text = ""
    End Sub

    Private Sub tRecherche_TextChanged(sender As Object, e As EventArgs) Handles tRecherche.TextChanged
        afficherLaboratoire()
    End Sub

    Private Sub bConfirmerMedecin_Click(sender As Object, e As EventArgs) Handles bConfirmerMedecin.Click
        Dim dr As DataRow
        Dim StrSQL As String = ""

        If ModeLaboratoire = "Ajout" Then

            If tCodeLaboratoire.Text = "" Then
                MsgBox("Veuillez saisir le code du Laboratoire !", MsgBoxStyle.Critical, "Erreur")
                tCodeLaboratoire.Focus()
                Exit Sub
            End If

            If tNomLaboratoire.Text = "" Then
                MsgBox("Veuillez saisir le nom du Laboratoire !", MsgBoxStyle.Critical, "Erreur")
                tNomLaboratoire.Focus()
                Exit Sub
            End If

            If CodeExiste = True Then
                MsgBox("Code Laboratoire existe déja, veuillez le changer !", MsgBoxStyle.Critical, "Erreur")
                tCodeLaboratoire.Focus()
                Exit Sub
            End If

            With dsLaboratoire
                dr = .Tables("LABORATOIRE_LISTE").NewRow
                dr.Item("CodeLabo") = tCodeLaboratoire.Text
                dr.Item("NomLabo") = tNomLaboratoire.Text
                dr.Item("Adresse") = tAdresseLaboratoire.Text
                dr.Item("tel") = tTelLaboratoire.Text
                dr.Item("Fax") = tFaxLaboratoire.Text
                dr.Item("SupprimeLabo") = False
                .Tables("LABORATOIRE_LISTE").Rows.Add(dr)
            End With

            Try
                daLaboratoire.Update(dsLaboratoire, "LABORATOIRE_LISTE")
                afficherLaboratoire()
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                dsLaboratoire.Reset()
            End Try

            tCodeLaboratoire.Text = ""
            tNomLaboratoire.Text = ""
            tTelLaboratoire.Text = ""
            tFaxLaboratoire.Text = ""
            tAdresseLaboratoire.Text = ""


        ElseIf ModeLaboratoire = "Modif" Then

            Dim cmd As New SqlCommand

            If gLaboratoire.RowCount = 0 Then
                MsgBox("Liste Vide", MsgBoxStyle.OkOnly)
            Else

                Try
                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = "UPDATE Laboratoire SET NomLabo = " + Quote(tNomLaboratoire.Text) + ", Adresse = " + Quote(tAdresseLaboratoire.Text) + ", tel = " + Quote(tTelLaboratoire.Text) + ", Fax = " + Quote(tFaxLaboratoire.Text) + " WHERE CodeLabo = " + Quote(tCodeLaboratoire.Value)
                    cmd.ExecuteNonQuery()
                Catch ex As Exception
                    MsgBox(ex.Message, MsgBoxStyle.Critical, "Erreur")
                End Try
                afficherLaboratoire()
                tCodeLaboratoire.Value = ""
                tNomLaboratoire.Value = ""
                tTelLaboratoire.Value = ""
                tFaxLaboratoire.Value = ""
                tAdresseLaboratoire.Value = ""

            End If


        End If

    End Sub
End Class