﻿Imports System.Data.SqlClient

Public Class fInformationClient

    Public Class ListInformation
        Public Id As Integer
        Public Code As String
        Public Libelle As String

        Sub New(id As Integer, code As String, libelle As String)
            Me.Id = Id
            Me.Code = code
            Me.Libelle = libelle
        End Sub
    End Class

    Public ListRecherchePathologie As New List(Of ListInformation)
    Public ListRechercheAllergie As New List(Of ListInformation)
    Public ListSelectPathologie As New List(Of ListInformation)
    Public ListSelectAllergie As New List(Of ListInformation)

    Private Sub fInformationClient_FormClosed(sender As Object, e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed

    End Sub

    Private Sub bFermer_Click(sender As System.Object, e As System.EventArgs) Handles bFermer.Click
        Me.Hide()
    End Sub

    Private Sub fInformationClient_Load(sender As Object, e As System.EventArgs) Handles Me.Load
        Dim cmdVente As New SqlCommand
        Dim daVente As New SqlDataAdapter
        Dim dsVente As New DataSet
        cmdVente.Connection = ConnectionServeur
        cmdVente.CommandText = "SELECT 'M' AS Code, 'Masculin' AS Libelle UNION SELECT 'F', 'Féminin' "
        daVente = New SqlDataAdapter(cmdVente)
        daVente.Fill(dsVente, "Sexe")
        cmbSexe.DataSource = dsVente.Tables("Sexe")
        cmbSexe.ValueMember = "Code"
        cmbSexe.DisplayMember = "Libelle"
        cmbSexe.ColumnHeaders = False
        cmbSexe.Splits(0).DisplayColumns("Code").Visible = False
        cmbSexe.Splits(0).DisplayColumns("Libelle").Width = 10
        cmbSexe.ExtendRightColumn = True

        tAge.Focus()
    End Sub

    Private Sub tPathologie_KeyUp(sender As System.Object, e As System.Windows.Forms.KeyEventArgs) Handles tPathologie.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbPathologie.Focus()
        End If
    End Sub

    Private Sub tPathologie_Validated(sender As System.Object, e As System.EventArgs) Handles tPathologie.Validated
        Dim listProduit As New List(Of Integer)

        Dim BCBDextherEtrClient As New ServiceBCB.BCBDextherEtrClient()
        Dim Cle As String = generateKey("NEXT", "")

        Dim bcbSecurity As ServiceBCB.bcbSecurity = New ServiceBCB.bcbSecurity
        bcbSecurity.codeEditeur = "NEXT"
        bcbSecurity.idPS = ""
        bcbSecurity.secretEditeur = Cle

        Dim PathologieList = BCBDextherEtrClient.rechercheBCB(tPathologie.Text, 16384, 0, bcbSecurity)

        Dim Index As Integer = 0
        cmbPathologie.Items.Clear()
        ListRecherchePathologie.Clear()
        If PathologieList.listePathologies IsNot Nothing Then
            For Each item In PathologieList.listePathologies
                cmbPathologie.Items.Insert(Index, item.libelle)
                ListRecherchePathologie.Add(New ListInformation(0, item.codeMotClef, item.libelle))
                Index += 1
            Next
        End If

    End Sub

    Private Sub bAjouter_Click(sender As System.Object, e As System.EventArgs) Handles bAjouterPathologie.Click
        For j = 0 To lbPathologies.Items.Count - 1
            If lbPathologies.Items(j) = cmbPathologie.Text Then
                Exit Sub
            End If
        Next
        lbPathologies.Items.Add(cmbPathologie.Text)
        ListSelectPathologie.Add(ListRecherchePathologie.Find(Function(Item) Item.Libelle = cmbPathologie.Text))
    End Sub

    Private Sub tAllergie_KeyUp(sender As System.Object, e As System.Windows.Forms.KeyEventArgs) Handles tAllergie.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbAllergie.Focus()
        End If
    End Sub

    Private Sub tAllergie_Validated(sender As System.Object, e As System.EventArgs) Handles tAllergie.Validated
        Dim listProduit As New List(Of Integer)

        Dim BCBDextherEtrClient As New ServiceBCB.BCBDextherEtrClient()
        Dim Cle As String = generateKey("NEXT", "")

        Dim bcbSecurity As ServiceBCB.bcbSecurity = New ServiceBCB.bcbSecurity
        bcbSecurity.codeEditeur = "NEXT"
        bcbSecurity.idPS = ""
        bcbSecurity.secretEditeur = Cle

        Dim AllergieList = BCBDextherEtrClient.rechercheBCB(tAllergie.Text, 4096, 0, bcbSecurity)

        Dim Index As Integer = 0
        cmbAllergie.Items.Clear()
        ListRechercheAllergie.Clear()
        If AllergieList.listeComposants IsNot Nothing Then
            For Each item In AllergieList.listeComposants
                cmbAllergie.Items.Insert(Index, item.libelle)
                ListRechercheAllergie.Add(New ListInformation(item.id, "", item.libelle))
                Index += 1
            Next
        End If

    End Sub

    Private Sub bAjouterAllergie_Click(sender As System.Object, e As System.EventArgs) Handles bAjouterAllergie.Click
        For j = 0 To lbAllergie.Items.Count - 1
            If lbAllergie.Items(j) = cmbAllergie.Text Then
                Exit Sub
            End If
        Next
        lbAllergie.Items.Add(cmbAllergie.Text)
        ListSelectAllergie.Add(ListRechercheAllergie.Find(Function(Item) Item.Libelle = cmbAllergie.Text))
    End Sub

    Private Sub bSupprimerPathologie_Click(sender As System.Object, e As System.EventArgs) Handles bSupprimerPathologie.Click
        Try
            ListSelectPathologie.Remove(ListRecherchePathologie.Find(Function(Item) Item.Libelle = lbPathologies.SelectedItem))
            lbPathologies.Items.RemoveAt(lbPathologies.SelectedIndex)
        Catch
        End Try
    End Sub

    Private Sub bSupprimerAllergie_Click(sender As System.Object, e As System.EventArgs) Handles bSupprimerAllergie.Click
        Try
            ListSelectAllergie.Remove(ListRechercheAllergie.Find(Function(Item) Item.Libelle = lbAllergie.SelectedItem))
            lbAllergie.Items.RemoveAt(lbAllergie.SelectedIndex)
        Catch
        End Try
    End Sub

    Private Sub tAge_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles tAge.KeyDown
        If e.KeyCode = Keys.Enter Then
            tPoids.Focus()
        End If
    End Sub

    Private Sub tPoids_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles tPoids.KeyDown
        If e.KeyCode = Keys.Enter Then
            tGrossesse.Focus()
        End If
    End Sub

    Private Sub tGrossesse_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles tGrossesse.KeyDown
        If e.KeyCode = Keys.Enter Then
            cmbSexe.Focus()
        End If
    End Sub

    Private Sub tAns_TextChanged(sender As System.Object, e As System.EventArgs) Handles tAns.TextChanged
        Try
            tAge.Text = Convert.ToInt32(tAns.Text) * 12
        Catch ex As Exception
            tAge.Text = 0
        End Try
    End Sub
End Class