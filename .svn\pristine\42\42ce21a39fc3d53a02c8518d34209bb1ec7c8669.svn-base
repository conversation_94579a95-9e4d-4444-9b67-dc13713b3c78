﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid

Public Class fEtatEntreeArticle
    Dim cmdNature As New SqlCommand
    Dim daNature As New SqlDataAdapter
    Dim dsNature As New DataSet

    Dim cmdEntree As New SqlCommand
    Dim daEntree As New SqlDataAdapter
    Dim dsEntree As New DataSet

    Dim ValeurAchat As Double = 0
    Dim ValeurVente As Double = 0
    Dim CondCrystalReport As String = ""

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)
        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If
    End Sub

    Public Sub Init()

        'charger les natures
        cmdNature.CommandText = "SELECT LibelleNatureEntre FROM NATURE_ENTREE WHERE SupprimeNatureEntre=0 ORDER BY LibelleNatureEntre ASC"
        cmdNature.Connection = ConnectionServeur
        daNature = New SqlDataAdapter(cmdNature)
        daNature.Fill(dsNature, "NATURE_ENTREE")
        cmbNature.DataSource = dsNature.Tables("NATURE_ENTREE")
        cmbNature.ValueMember = "LibelleNatureEntre"
        cmbNature.ColumnHeaders = False
        cmbNature.Splits(0).DisplayColumns("LibelleNatureEntre").Width = 10
        cmbNature.ExtendRightColumn = True

        dtpDebut.Text = Today
        dtpFin.Text = Today
        dtpDebut.Focus()

        If ModeADMIN = "ADMIN" Then
            Label4.Visible = True
            Label6.Visible = True
            tValeurAchat.Visible = True
            tValeurVente.Visible = True
        Else
            Label4.Visible = False
            Label6.Visible = False
            tValeurAchat.Visible = False
            tValeurVente.Visible = False
        End If

        AfficherEntree()
    End Sub

    Public Sub AfficherEntree()
        Dim I As Integer
        Dim Cond As String = "1=1"
        CondCrystalReport = "1=1"
        dsEntree.Clear()

        If dtpDebut.Text <> "" And dtpFin.Text <> "" Then
            Cond += " AND Date BETWEEN " + Quote(dtpDebut.Text + " 00:00:00") + " AND " + Quote(dtpFin.Text + " 23:59:59")
            CondCrystalReport += " AND {Vue_EtatEntreeArticle.Date} >= DateTime('" + dtpDebut.Text + " 00:00:00')"
            CondCrystalReport += " AND {Vue_EtatEntreeArticle.Date} <= DateTime('" + dtpFin.Text + " 23:59:59')"
        End If
        If cmbNature.Text <> "" Then
            Cond += " AND LibelleNatureEntre = " + Quote(cmbNature.Text)
            CondCrystalReport += " AND {Vue_EtatEntreeArticle.LibelleNatureEntre} = " + Quote(cmbNature.Text)
        End If

        cmdEntree.CommandText = " SELECT " + _
                                " Date, " + _
                                " CodeArticle, " + _
                                " Designation, " + _
                                " LibelleForme, " + _
                                " Qte, " + _
                                " TotalVenteTTC, " + _
                                " TotalAchatTTC, " + _
                                " LibelleNatureEntre " + _
                                " FROM Vue_EtatEntreeArticle " + _
                                " WHERE " + Cond + " ORDER BY Date ASC"

        cmdEntree.Connection = ConnectionServeur
        daEntree = New SqlDataAdapter(cmdEntree)
        daEntree.Fill(dsEntree, "ENTREE")

        With gEntree
            .Columns.Clear()
            .DataSource = dsEntree
            .DataMember = "ENTREE"
            .Rebind(False)
            .Columns("Date").Caption = "Date"
            .Columns("CodeArticle").Caption = "Code"
            .Columns("Designation").Caption = "Désignation"
            .Columns("LibelleForme").Caption = "Forme"
            .Columns("Qte").Caption = "Quantité"
            .Columns("LibelleNatureEntre").Caption = "Nature"
            .Columns("Date").NumberFormat = "dd/MM/yyyy"
            .Columns("TotalAchatTTC").Caption = "Total Achat TTC"
            .Columns("TotalVenteTTC").Caption = "Total Vente TTC"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            '.Splits(0).DisplayColumns("TotalVenteTTC").Visible = False
            '.Splits(0).DisplayColumns("TotalAchatTTC").Visible = False
            .Splits(0).DisplayColumns("Date").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Date").Width = 100
            .Splits(0).DisplayColumns("CodeArticle").Width = 100
            .Splits(0).DisplayColumns("CodeArticle").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Designation").Width = 300
            .Splits(0).DisplayColumns("Qte").Width = 70

            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gEntree)
        End With
        CalculValeur()

    End Sub

    Private Sub gEntree_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gEntree.FetchRowStyle
        e.CellStyle.Font = New System.Drawing.Font("Calibri", 9, FontStyle.Regular)
    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherEntree()
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDebut.TextChanged

    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherEntree()
            cmbNature.Focus()
        End If
    End Sub
    Private Sub cmbNature_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbNature.KeyUp
        If e.KeyCode = Keys.Enter Then
            gEntree.Focus()
            AfficherEntree()
            CalculValeur()
        End If
    End Sub

    Private Sub dtpFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpFin.TextChanged

    End Sub

    Private Sub cmbNature_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbNature.TextChanged

    End Sub

    Private Sub CalculValeur()
        ValeurAchat = 0.0
        ValeurVente = 0.0
        If gEntree.RowCount <> 0 Then
            For I As Integer = 0 To gEntree.RowCount - 1
                ValeurAchat += gEntree(I, "TotalAchatTTC")
                ValeurVente += gEntree(I, "TotalVenteTTC")
            Next
            tValeurAchat.Text = Format(ValeurAchat, "0.000")
            tValeurVente.Text = Format(ValeurVente, "0.000")
        End If
    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click
        If gEntree.RowCount > 0 Then
            Dim I As Integer
            For I = 0 To fMain.Tab.TabPages.Count - 1
                If fMain.Tab.TabPages(I).Text = "Imprimer Etat Entrée des articles" Then
                    fMain.Tab.TabPages(I).Show()
                    Exit Sub
                End If
            Next
            CR.FileName = Application.StartupPath + "\EtatEntreeArticle.rpt"
            CR.SetParameterValue("debut", dtpDebut.Text)
            CR.SetParameterValue("fin", dtpFin.Text)
            If cmbNature.Text <> "" Then
                CR.SetParameterValue("Nature", cmbNature.Text)
            Else
                CR.SetParameterValue("Nature", "Tous")
            End If
            CR.SetParameterValue("ValeurVente", ValeurVente)
            CR.SetParameterValue("ValeurAchat", ValeurAchat)

            Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
            Dim tliCurrent As CrystalDecisions.Shared.TableLogOnInfo
            For Each tbCurrent In CR.Database.Tables
                tliCurrent = tbCurrent.LogOnInfo
                With tliCurrent.ConnectionInfo
                    .ServerName = NomServeur
                    .UserID = NomUtilisateurSQL
                    .Password = MotDePasseSQL
                    .DatabaseName = NomBase
                End With
                tbCurrent.ApplyLogOnInfo(tliCurrent)
            Next tbCurrent
            CR.RecordSelectionFormula = CondCrystalReport
            Dim MyViewer As New fViewer
            MyViewer.CRViewer.ReportSource = CR
            fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
            fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
            fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel)
            fMain.Tab.SelectedTab.Text = "Imprimer Etat Entrée des articles"
        End If
    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub cmbNature_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbNature.Validated
        AfficherEntree()
    End Sub
End Class