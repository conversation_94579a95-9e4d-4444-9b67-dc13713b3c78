﻿Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared

Public Class fProductionMagistrale

    'Déclaration des variables
    '---------------------------------------------------------
    Public NumeroProduction As String = ""
    Dim cmdProduction As New SqlCommand
    Dim cbProduction As New SqlCommandBuilder
    Dim dsProduction As New DataSet
    Dim daProduction As New SqlDataAdapter
    Public dr As DataRow = Nothing
    Dim NumeroLotArticle As String
    Public pCodeArticleMagistrale As String
    Public pDesignationMagistrale As String
    Public pCodePreparationMagistrale As String
    Public pNomClient As String

    Dim cmdPreparation As New SqlCommand
    Dim cbPreparation As New SqlCommandBuilder
    Dim dsPreparation As New DataSet
    Dim daPreparation As New SqlDataAdapter

    Dim StrSQL As String

    Dim cmdParametres As New SqlCommand
    Dim daParametres As New SqlDataAdapter
    Dim cbParametres As New SqlCommandBuilder
    Dim dsParametres As New DataSet
    Dim drPharmacien As DataRow

    Dim cmdRecupereNum As New SqlCommand
    Public Shared CodePreparation As String = "0"
    Dim NumeroMagistral As String = ""
    '---------------------------------------------------------

    Private Sub bConfirmer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bConfirmer.Click
        'Déclaration
        Dim ConfirmerEnregistrer As Boolean = False
        Dim CodeOperateur As String = ""
        Dim CmdNumeoLot As New SqlCommand
        Dim CodeArticle As String = ""
        Dim CodeArticleComposant As String = ""
        Dim NumeroLotArticleComposant As String = ""
        Dim DatePeremptionComposant As Date
        Dim QteComposant As Double
        Dim cmd As New SqlCommand
        Dim QteParLot As Integer = 0
        Dim Qte As Integer

        'si la qte est vide
        If tQte.Text = "" Or IsNumeric(tQte.Text) = False Then
            MsgBox("La Quantité Vide !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        If tQte.Text <> CInt(tQte.Text) Then
            MsgBox("La Quantité doit être : Un Nombre entier sans virgule !", MsgBoxStyle.Critical, "Erreur")
            Exit Sub
        End If

        'pour ajouter une nouvelle production
        NumeroProduction = RecupereNumero()

        '----------------------- contrôle des dates de péremption si il y a un qui est périmé
        If dtpDatePeremption.Value.ToString <> "" Then
            If dtpDatePeremption.Value < Date.Today Then
                MsgBox("la Préparation admet une date de péremption déja dépassée ! veuillez corriger sa date de péremption", MsgBoxStyle.Critical, "Erreur")
                Exit Sub
            End If

        End If

        '------------------------------ demande du mot de passe
        Dim myMotDePasse As New fMotDePasse
        myMotDePasse.ShowDialog()

        ConfirmerEnregistrer = fMotDePasse.Confirmer
        CodeOperateur = fMotDePasse.CodeOperateur

        myMotDePasse.Dispose()
        myMotDePasse.Close()

        If ConfirmerEnregistrer = False Then
            Exit Sub
        End If

        '------------------------------ enregistrement de l'entête de Producion -------------------------
        Try

            '----------Vider la DS Production

            If dsProduction.Tables("PRODUCTION") IsNot Nothing Then
                dsProduction.Tables("PRODUCTION").Clear()
            End If

            StrSQL = "SELECT top(0) * FROM PRODUCTION ORDER BY NumeroProduction ASC"
            cmdProduction.Connection = ConnectionServeur
            cmdProduction.CommandText = StrSQL
            daProduction = New SqlDataAdapter(cmdProduction)
            daProduction.Fill(dsProduction, "PRODUCTION")
            cbProduction = New SqlCommandBuilder(daProduction)
            dr = dsProduction.Tables("PRODUCTION").NewRow()

            With dsProduction
                dr.Item("NumeroProduction") = NumeroProduction
                dr.Item("Date") = System.DateTime.Now
                dr.Item("Remarque") = tRemarque.Text
                dr.Item("CodePersonnel") = CodeOperateur

                dsProduction.Tables("PRODUCTION").Rows.Add(dr)
            End With
            Try
                daProduction.Update(dsProduction, "PRODUCTION")
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                dsProduction.Reset()
            End Try

        Catch ex As Exception
            MsgBox(ex.Message)
        End Try


        '------------------------------ enregistrement les détails de Production -------------------------
        Try

            '----------Vider la DS Production

            If dsProduction.Tables("PRODUCTION_DETAILS") IsNot Nothing Then
                dsProduction.Tables("PRODUCTION_DETAILS").Clear()
            End If

            StrSQL = "SELECT top(0) * FROM PRODUCTION_DETAILS"
            cmdProduction.Connection = ConnectionServeur
            cmdProduction.CommandText = StrSQL
            daProduction = New SqlDataAdapter(cmdProduction)
            daProduction.Fill(dsProduction, "PRODUCTION_DETAILS")
            cbProduction = New SqlCommandBuilder(daProduction)
            dr = dsProduction.Tables("PRODUCTION_DETAILS").NewRow()
            With dsProduction
                dr.Item("NumeroProduction") = NumeroProduction
                dr.Item("CodeArticle") = pCodePreparationMagistrale
                dr.Item("CodeABarre") = pCodeArticleMagistrale
                dr.Item("Designation") = pDesignationMagistrale
                dr.Item("CodeForme") = 0
                dr.Item("NumeroLotArticle") = IncrementerNumeroLotArticle()
                dr.Item("DatePeremption") = dtpDatePeremption.Value
                dr.Item("Qte") = tQte.Text


                dsProduction.Tables("PRODUCTION_DETAILS").Rows.Add(dr)
            End With
            Try
                daProduction.Update(dsProduction, "PRODUCTION_DETAILS")
            Catch ex As Exception
                MsgBox("Erreur de mise à jour des données !" + Chr(13) + ex.Message, MsgBoxStyle.Critical, "Erreur")
                dsProduction.Reset()
            End Try

        Catch ex As Exception
            MsgBox(ex.Message)
        End Try

        'pour récuperer les valeurs de la forme
        RecupererValeurProductionMagistrale()

        'Insertion dans la table Production détails formule


        CodeArticle = RecupererValeurExecuteScalaire("CodeArticle", " PRODUCTION_DETAILS", "NumeroProduction", NumeroProduction)
        Qte = tQte.Text

        If dsProduction.Tables("FORMULE_PREPARATION_DETAILS") IsNot Nothing Then
            dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Clear()
        End If

        cmdProduction.Connection = ConnectionServeur
        cmdProduction.CommandText = "Select  * FROM FORMULE_PREPARATION_DETAILS where CodePreparation=" + Quote(CodeArticle) + ""
        daProduction = New SqlDataAdapter(cmdProduction)
        daProduction.Fill(dsProduction, "FORMULE_PREPARATION_DETAILS")
        cbProduction = New SqlCommandBuilder(daProduction)


        For J = 0 To dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Rows.Count - 1

            CodeArticleComposant = RecupererValeurPreparationDetails("CodeArticle", "FORMULE_PREPARATION_DETAILS", "CodeArticle", dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Rows(J).Item("CodeArticle"), "CodePreparation", CodeArticle)

            QteComposant = RecupererValeurPreparationDetails("Qte", "FORMULE_PREPARATION_DETAILS", "CodeArticle", dsProduction.Tables("FORMULE_PREPARATION_DETAILS").Rows(J).Item("CodeArticle"), "CodePreparation", CodeArticle)

            NumeroLotArticleComposant = RecupererValeurExecuteScalaire("NumeroLotArticle", " LOT_ARTICLE", "CodeArticle", CodeArticleComposant)

            QteComposant = QteComposant * Qte

            '------------------ gestion des numeros de lot
            If dsProduction.Tables("LOT_ARTICLE") IsNot Nothing Then
                dsProduction.Tables("LOT_ARTICLE").Clear()
            End If

            StrSQL = " SELECT  NumeroLotArticle, DatePeremptionArticle, QteLotArticle, CodeArticle FROM LOT_ARTICLE " + _
                     " WHERE DatePeremptionArticle >'" + _
                     System.DateTime.Now.Date.ToString & "'" + _
                     " AND CodeArticle= " & Quote(CodeArticleComposant) & "" + _
                     " AND QteLotArticle > 0   " & "" + _
                     " UNION  SELECT  NumeroLotArticle, DatePeremptionArticle, QteLotArticle, CodeArticle FROM LOT_ARTICLE " + _
                     " WHERE DatePeremptionArticle is null " + _
                     " AND CodeArticle= " & Quote(CodeArticleComposant) & "" + _
                     " AND QteLotArticle > 0   " + _
                     " ORDER BY DatePeremptionArticle ASC"

            cmdProduction.Connection = ConnectionServeur
            cmdProduction.CommandText = StrSQL
            daProduction = New SqlDataAdapter(cmdProduction)
            daProduction.Fill(dsProduction, "LOT_ARTICLE")
            cbProduction = New SqlCommandBuilder(daProduction)

            For k = 0 To dsProduction.Tables("LOT_ARTICLE").Rows.Count - 1

                QteParLot = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("QteLotArticle")

                If (dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString = "" And
                        dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "") Then

                    If QteComposant <= QteParLot Then

                        NumeroLotArticleComposant = ""

                        DatePeremptionComposant = #12:00:00 AM#

                        StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                        StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                        StrSQL = StrSQL & " VALUES('"
                        StrSQL = StrSQL & NumeroProduction
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & CodeArticle
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & CodeArticleComposant
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & NumeroLotArticleComposant
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & DatePeremptionComposant
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & QteComposant
                        StrSQL = StrSQL & "')"

                        QteComposant = 0

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()

                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try

                    Else

                        NumeroLotArticleComposant = ""

                        DatePeremptionComposant = #12:00:00 AM#

                        StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                        StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                        StrSQL = StrSQL & " VALUES('"
                        StrSQL = StrSQL & NumeroProduction
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & CodeArticle
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & CodeArticleComposant
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & NumeroLotArticleComposant
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & DatePeremptionComposant
                        StrSQL = StrSQL & "','"
                        StrSQL = StrSQL & QteComposant
                        StrSQL = StrSQL & "')"

                        cmd.Connection = ConnectionServeur
                        cmd.CommandText = StrSQL
                        Try
                            cmd.ExecuteNonQuery()

                        Catch ex As Exception
                            Console.WriteLine(ex.Message)
                        End Try

                    End If

                    Exit For

                End If

                If QteComposant <= QteParLot Then


                    NumeroLotArticleComposant = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString

                    If dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "" Then
                        DatePeremptionComposant = #12:00:00 AM#

                    Else
                        DatePeremptionComposant = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString
                    End If

                    StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                    StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                    StrSQL = StrSQL & " VALUES('"
                    StrSQL = StrSQL & NumeroProduction
                    StrSQL = StrSQL & "','"
                    StrSQL = StrSQL & CodeArticle
                    StrSQL = StrSQL & "','"
                    StrSQL = StrSQL & CodeArticleComposant
                    StrSQL = StrSQL & "','"
                    StrSQL = StrSQL & NumeroLotArticleComposant
                    StrSQL = StrSQL & "','"
                    StrSQL = StrSQL & DatePeremptionComposant
                    StrSQL = StrSQL & "','"
                    StrSQL = StrSQL & QteComposant
                    StrSQL = StrSQL & "')"

                    QteComposant = 0

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL
                    Try
                        cmd.ExecuteNonQuery()

                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                    Exit For

                Else

                    QteComposant = QteComposant - QteParLot

                    NumeroLotArticleComposant = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("NumeroLotArticle").ToString

                    If dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString = "" Then
                        DatePeremptionComposant = #12:00:00 AM#

                    Else
                        DatePeremptionComposant = dsProduction.Tables("LOT_ARTICLE").Rows(k).Item("DatePeremptionArticle").ToString
                    End If

                    StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                    StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                    StrSQL = StrSQL & " VALUES('"
                    StrSQL = StrSQL & NumeroProduction
                    StrSQL = StrSQL & "','"
                    StrSQL = StrSQL & CodeArticle
                    StrSQL = StrSQL & "','"
                    StrSQL = StrSQL & CodeArticleComposant
                    StrSQL = StrSQL & "','"
                    StrSQL = StrSQL & NumeroLotArticleComposant
                    StrSQL = StrSQL & "','"
                    StrSQL = StrSQL & DatePeremptionComposant
                    StrSQL = StrSQL & "','"
                    StrSQL = StrSQL & QteParLot
                    StrSQL = StrSQL & "')"

                    cmd.Connection = ConnectionServeur
                    cmd.CommandText = StrSQL
                    Try
                        cmd.ExecuteNonQuery()

                    Catch ex As Exception
                        Console.WriteLine(ex.Message)
                    End Try

                End If

            Next

            If QteComposant > 0 Then

                NumeroLotArticleComposant = ""

                DatePeremptionComposant = #12:00:00 AM#


                StrSQL = "INSERT INTO PRODUCTION_DETAILS_FORMULE "
                StrSQL = StrSQL & "(""NumeroProduction"",""CodeArticle"",""CodeArticleComposant"",""NumeroLotArticle"",""DatePeremptionArticle"",""Qte"") "
                StrSQL = StrSQL & " VALUES('"
                StrSQL = StrSQL & NumeroProduction
                StrSQL = StrSQL & "','"
                StrSQL = StrSQL & CodeArticle
                StrSQL = StrSQL & "','"
                StrSQL = StrSQL & CodeArticleComposant
                StrSQL = StrSQL & "','"
                StrSQL = StrSQL & NumeroLotArticleComposant
                StrSQL = StrSQL & "','"
                StrSQL = StrSQL & DatePeremptionComposant
                StrSQL = StrSQL & "','"
                StrSQL = StrSQL & QteComposant
                StrSQL = StrSQL & "')"

                cmd.Connection = ConnectionServeur
                cmd.CommandText = StrSQL
                Try
                    cmd.ExecuteNonQuery()

                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

            End If

        Next


        Try

        Catch ex As Exception
        End Try

        cmd.Connection = ConnectionServeur
        cmd.CommandText = "UPDATE ARTICLE SET Designation = " + Quote(pNomClient.ToUpper()) + " + ' ' + Designation WHERE CodeABarre = " + Quote(pCodeArticleMagistrale)
        Try
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        cmd.Connection = ConnectionServeur
        cmd.CommandText = "UPDATE ARTICLE SET CodeABarre = " + Quote(lNumeroMagistral.Text) + " WHERE CodeABarre = " + Quote(pCodeArticleMagistrale)
        Try
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try


        'initialisation les btns et les champs
        bConfirmer.Enabled = False
        bAnnuler.Enabled = False
        tRemarque.Enabled = False
        tQte.Enabled = False
        dtpDatePeremption.Enabled = False

    End Sub

    Public Function IncrementerNumeroLotArticle()
        Dim cmd As New SqlCommand
        Dim NumeroLot As String = ""
        Dim NumeroLotIncrementer As String = ""

        NumeroLot = RecupereNumeroLotArticle()

        Try
            StrSQL = " UPDATE PARAMETRE_PHARMACIE SET NumeroLotProduction = '" + NumeroLot + "'where Code='1' "

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            cmd.ExecuteNonQuery()

        Catch ex As Exception
            MsgBox(ex.Message)
        End Try

        Try
            StrSQL = " SELECT NumeroLotProduction FROM PARAMETRE_PHARMACIE  "

            cmd.Connection = ConnectionServeur
            cmd.CommandText = StrSQL
            NumeroLotIncrementer = cmd.ExecuteScalar

        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
        Return NumeroLotIncrementer
    End Function

    Public Function RecupereNumeroLotArticle()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT NumeroLotProduction FROM PARAMETRE_PHARMACIE"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d'un nouveau compteur  ----
        If ValeurActuel = "" Then
            ValeurActuel = "000000"
        End If
        Numero = ValeurActuel
        Numero = Numero + 1
        numeroConvertit = Numero.ToString
        For i = 0 To 5 - numeroConvertit.Length
            numeroConvertit = "0" + numeroConvertit
        Next
        ValeurRetour = numeroConvertit

        Return ValeurRetour
    End Function

    Public Function RecupereNumero()
        Dim StrSQL As String = ""
        Dim cmdRecupereNum As New SqlCommand
        Dim ValeurActuel As String = ""
        Dim Numero As Integer = 0
        Dim numeroConvertit As String = ""
        Dim ValeurRetour As String = ""
        Dim i As Integer

        StrSQL = " SELECT max([NumeroProduction]) FROM [PRODUCTION]"
        cmdRecupereNum.Connection = ConnectionServeur
        cmdRecupereNum.CommandText = StrSQL
        Try
            ValeurActuel = cmdRecupereNum.ExecuteScalar()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        '----------------------- récupération du dernier numero séquenciel de l'année en cours ou initialisation d un nouveau compteur  ----
        If ValeurActuel.Length > 4 Then
            If ValeurActuel.Substring(0, 4) = Year(Today).ToString Then
                Numero = Convert.ToInt32(ValeurActuel.Substring(5, 6))
                Numero = Numero + 1
                numeroConvertit = Numero.ToString
                For i = 0 To 5 - numeroConvertit.Length
                    numeroConvertit = "0" + numeroConvertit
                Next
                ValeurRetour = Year(Today).ToString + "/" + numeroConvertit
            Else
                ValeurRetour = Year(Today).ToString + "/" + "000001"
            End If
        Else
            ValeurRetour = Year(Today).ToString + "/" + "000001"
        End If
        Return ValeurRetour
    End Function

    Private Sub fProductionMagistrale_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Dim ch As String = ""
        Dim codepreparation As String = ""
        Dim Cmd As New SqlCommand
        Dim nbrejour As Integer = 0
        Try

            Dim myfPreparation As New fPreparation
            codepreparation = fPreparation.pCodePreparation

            ch = "select NombreJourValidite from FORMULE_PREPARATION where CodePreparation ='" + codepreparation + "'"
            Cmd.Connection = ConnectionServeur
            Cmd.CommandText = ch
            Try
                nbrejour = Cmd.ExecuteScalar
            Catch ex As Exception
                nbrejour = 0
            End Try

            lOperateur.Text = "-"
            dtpDatePeremption.Text = System.DateTime.Now.AddDays(nbrejour)
            lNumeroProduction.Text = "-------------"
            tRemarque.Enabled = True
            tRemarque.Text = ""


        Catch ex As Exception

        End Try

    End Sub


    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        fProductionMagistrale_Load(sender, e)
    End Sub

    Private Sub tRemarque_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tRemarque.KeyUp
        If e.KeyCode = Keys.Enter Then
            tQte.Focus()
        End If
    End Sub

    Private Sub tQte_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tQte.KeyUp
        If e.KeyCode = Keys.Enter Then
            bConfirmer.Focus()
        End If
    End Sub

    'pour récuperer les valeurs  de la forme production mogistrale
    Sub RecupererValeurProductionMagistrale()

        Dim codepersonnel As String

        codepersonnel = RecupererValeurExecuteScalaire("CodePersonnel", "PRODUCTION", "NumeroProduction", NumeroProduction)
        lNumeroProduction.Text = RecupererValeurExecuteScalaire("NumeroProduction", "PRODUCTION", "NumeroProduction", NumeroProduction)
        lDateProduction.Text = RecupererValeurExecuteScalaire("Date", "PRODUCTION", "NumeroProduction", NumeroProduction)
        lOperateur.Text = RecupererValeurExecuteScalaire("Nom", "UTILISATEUR", "CodeUtilisateur", codepersonnel)
        tRemarque.Text = RecupererValeurExecuteScalaire("Remarque", "PRODUCTION", "NumeroProduction", NumeroProduction)
        tQte.Text = RecupererValeurExecuteScalaire("Qte", "PRODUCTION_DETAILS", "NumeroProduction", NumeroProduction)
        dtpDatePeremption.Value = RecupererValeurExecuteScalaire("DatePeremption", "PRODUCTION_DETAILS", "NumeroProduction", NumeroProduction)
        lNumeroLotArticle.Text = RecupererValeurExecuteScalaire("NumeroLotProduction", "PARAMETRE_PHARMACIE", "Code", 1)

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        Me.Hide()
    End Sub

    Private Sub dtpDatePeremption_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            bConfirmer.Focus()
        End If
    End Sub

    Private Sub tNumeroLotArticle_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            bConfirmer.Focus()
        End If
    End Sub

    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        Try
            Quote(ValeurCle)
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurCle)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function

    Public Function RecupererValeurPreparationDetails(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurClePrimaire, ByVal CleSecondaire, ByVal ValeurCleSecondaire)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        Try
            Quote(ValeurClePrimaire)
            Quote(ValeurCleSecondaire)
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurClePrimaire) + "And" + " " + CleSecondaire + "=" + Quote(ValeurCleSecondaire)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function


End Class