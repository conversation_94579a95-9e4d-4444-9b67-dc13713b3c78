﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="SaleReportModel.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2008" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityContainer Name="SaleReportModelStoreContainer">
          <EntitySet Name="CAISSE" EntityType="SaleReportModel.Store.CAISSE" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="MOUVEMENT_ETATS" EntityType="SaleReportModel.Store.MOUVEMENT_ETATS" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="PARAMETRE_PHARMACIE" EntityType="SaleReportModel.Store.PARAMETRE_PHARMACIE" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="V_Report_EtatDesVentes" EntityType="SaleReportModel.Store.V_Report_EtatDesVentes" store:Type="Views" store:Schema="dbo" store:Name="V_Report_EtatDesVentes">
            <DefiningQuery>SELECT 
      [V_Report_EtatDesVentes].[Id] AS [Id], 
      [V_Report_EtatDesVentes].[NumeroOperation] AS [NumeroOperation], 
      [V_Report_EtatDesVentes].[Date] AS [Date], 
      [V_Report_EtatDesVentes].[Type] AS [Type], 
      [V_Report_EtatDesVentes].[Nom] AS [Nom], 
      [V_Report_EtatDesVentes].[MP] AS [MP], 
      [V_Report_EtatDesVentes].[TotalRemise] AS [TotalRemise], 
      [V_Report_EtatDesVentes].[TotalTTC] AS [TotalTTC], 
      [V_Report_EtatDesVentes].[LibellePoste] AS [LibellePoste], 
      [V_Report_EtatDesVentes].[NomUtilisateur] AS [NomUtilisateur], 
      [V_Report_EtatDesVentes].[CodeClient] AS [CodeClient], 
      [V_Report_EtatDesVentes].[TypeOperation] AS [TypeOperation], 
      [V_Report_EtatDesVentes].[NumeroFacture] AS [NumeroFacture], 
      [V_Report_EtatDesVentes].[CodeNatureReglement] AS [CodeNatureReglement], 
      [V_Report_EtatDesVentes].[CodePersonnel] AS [CodePersonnel], 
      [V_Report_EtatDesVentes].[CodeMutuelle] AS [CodeMutuelle], 
      [V_Report_EtatDesVentes].[Mutuelle] AS [Mutuelle], 
      [V_Report_EtatDesVentes].[MontantCnam] AS [MontantCnam], 
      [V_Report_EtatDesVentes].[MontantMutuelle] AS [MontantMutuelle], 
      [V_Report_EtatDesVentes].[Vider] AS [Vider], 
      [V_Report_EtatDesVentes].[Credit] AS [Credit], 
      [V_Report_EtatDesVentes].[Debit] AS [Debit], 
      [V_Report_EtatDesVentes].[Marge] AS [Marge]
      FROM [dbo].[V_Report_EtatDesVentes] AS [V_Report_EtatDesVentes]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="V_Report_EtatDetailDesVentes" EntityType="SaleReportModel.Store.V_Report_EtatDetailDesVentes" store:Type="Views" store:Schema="dbo" store:Name="V_Report_EtatDetailDesVentes">
            <DefiningQuery>SELECT 
      [V_Report_EtatDetailDesVentes].[Id] AS [Id], 
      [V_Report_EtatDetailDesVentes].[NumeroOperation] AS [NumeroOperation], 
      [V_Report_EtatDetailDesVentes].[Date] AS [Date], 
      [V_Report_EtatDetailDesVentes].[Designation] AS [Designation], 
      [V_Report_EtatDetailDesVentes].[LibelleForme] AS [LibelleForme], 
      [V_Report_EtatDetailDesVentes].[LibelleCategorie] AS [LibelleCategorie], 
      [V_Report_EtatDetailDesVentes].[Nom] AS [Nom], 
      [V_Report_EtatDetailDesVentes].[Quantite] AS [Quantite], 
      [V_Report_EtatDetailDesVentes].[Tva] AS [Tva], 
      [V_Report_EtatDetailDesVentes].[TotalTVA] AS [TotalTVA], 
      [V_Report_EtatDetailDesVentes].[Remise] AS [Remise], 
      [V_Report_EtatDetailDesVentes].[TotalVenteTTC] AS [TotalVenteTTC], 
      [V_Report_EtatDetailDesVentes].[TotalMutuelle] AS [TotalMutuelle], 
      [V_Report_EtatDetailDesVentes].[TotalCNAM] AS [TotalCNAM], 
      [V_Report_EtatDetailDesVentes].[CodeArticle] AS [CodeArticle], 
      [V_Report_EtatDetailDesVentes].[CodeABarre] AS [CodeABarre], 
      [V_Report_EtatDetailDesVentes].[CodeClient] AS [CodeClient], 
      [V_Report_EtatDetailDesVentes].[TotalVenteHT] AS [TotalVenteHT], 
      [V_Report_EtatDetailDesVentes].[NumeroFacture] AS [NumeroFacture], 
      [V_Report_EtatDetailDesVentes].[CodeCategorie] AS [CodeCategorie], 
      [V_Report_EtatDetailDesVentes].[CodeForme] AS [CodeForme]
      FROM [dbo].[V_Report_EtatDetailDesVentes] AS [V_Report_EtatDetailDesVentes]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="V_Report_EtatHitParade" EntityType="SaleReportModel.Store.V_Report_EtatHitParade" store:Type="Views" store:Schema="dbo" store:Name="V_Report_EtatHitParade">
            <DefiningQuery>SELECT 
      [V_Report_EtatHitParade].[Id] AS [Id], 
      [V_Report_EtatHitParade].[Date] AS [Date], 
      [V_Report_EtatHitParade].[CodeABarre] AS [CodeABarre], 
      [V_Report_EtatHitParade].[Designation] AS [Designation], 
      [V_Report_EtatHitParade].[LibelleForme] AS [LibelleForme], 
      [V_Report_EtatHitParade].[LibelleCategorie] AS [LibelleCategorie], 
      [V_Report_EtatHitParade].[Quantite] AS [Quantite], 
      [V_Report_EtatHitParade].[Stock] AS [Stock], 
      [V_Report_EtatHitParade].[TotalVenteTTC] AS [TotalVenteTTC], 
      [V_Report_EtatHitParade].[Rayon] AS [Rayon], 
      [V_Report_EtatHitParade].[nom] AS [nom], 
      [V_Report_EtatHitParade].[DatePeremption] AS [DatePeremption], 
      [V_Report_EtatHitParade].[CodePersonnel] AS [CodePersonnel], 
      [V_Report_EtatHitParade].[CodeArticle] AS [CodeArticle], 
      [V_Report_EtatHitParade].[CodeLabo] AS [CodeLabo], 
      [V_Report_EtatHitParade].[CodeForme] AS [CodeForme], 
      [V_Report_EtatHitParade].[CodeCategorie] AS [CodeCategorie]
      FROM [dbo].[V_Report_EtatHitParade] AS [V_Report_EtatHitParade]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="V_Report_EtatJournalDesVentes" EntityType="SaleReportModel.Store.V_Report_EtatJournalDesVentes" store:Type="Views" store:Schema="dbo" store:Name="V_Report_EtatJournalDesVentes">
            <DefiningQuery>SELECT 
      [V_Report_EtatJournalDesVentes].[Id] AS [Id], 
      [V_Report_EtatJournalDesVentes].[Date] AS [Date], 
      [V_Report_EtatJournalDesVentes].[Exonore] AS [Exonore], 
      [V_Report_EtatJournalDesVentes].[BaseTVA6] AS [BaseTVA6], 
      [V_Report_EtatJournalDesVentes].[TVA6] AS [TVA6], 
      [V_Report_EtatJournalDesVentes].[BaseTVA12] AS [BaseTVA12], 
      [V_Report_EtatJournalDesVentes].[TVA12] AS [TVA12], 
      [V_Report_EtatJournalDesVentes].[BaseTVA18] AS [BaseTVA18], 
      [V_Report_EtatJournalDesVentes].[TVA18] AS [TVA18], 
      [V_Report_EtatJournalDesVentes].[TotalHT] AS [TotalHT], 
      [V_Report_EtatJournalDesVentes].[TotalTVA] AS [TotalTVA], 
      [V_Report_EtatJournalDesVentes].[TotalTTC] AS [TotalTTC], 
      [V_Report_EtatJournalDesVentes].[HR] AS [HR], 
      [V_Report_EtatJournalDesVentes].[Nom] AS [Nom], 
      [V_Report_EtatJournalDesVentes].[NumeroFacture] AS [NumeroFacture]
      FROM [dbo].[V_Report_EtatJournalDesVentes] AS [V_Report_EtatJournalDesVentes]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="V_Report_EtatJournalDesVentesDetaillee" EntityType="SaleReportModel.Store.V_Report_EtatJournalDesVentesDetaillee" store:Type="Views" store:Schema="dbo" store:Name="V_Report_EtatJournalDesVentesDetaillee">
            <DefiningQuery>SELECT 
      [V_Report_EtatJournalDesVentesDetaillee].[Id] AS [Id], 
      [V_Report_EtatJournalDesVentesDetaillee].[Date] AS [Date], 
      [V_Report_EtatJournalDesVentesDetaillee].[Exonore] AS [Exonore], 
      [V_Report_EtatJournalDesVentesDetaillee].[BaseTVA6] AS [BaseTVA6], 
      [V_Report_EtatJournalDesVentesDetaillee].[TVA6] AS [TVA6], 
      [V_Report_EtatJournalDesVentesDetaillee].[BaseTVA12] AS [BaseTVA12], 
      [V_Report_EtatJournalDesVentesDetaillee].[TVA12] AS [TVA12], 
      [V_Report_EtatJournalDesVentesDetaillee].[BaseTVA18] AS [BaseTVA18], 
      [V_Report_EtatJournalDesVentesDetaillee].[TVA18] AS [TVA18], 
      [V_Report_EtatJournalDesVentesDetaillee].[TotalHT] AS [TotalHT], 
      [V_Report_EtatJournalDesVentesDetaillee].[TotalTVA] AS [TotalTVA], 
      [V_Report_EtatJournalDesVentesDetaillee].[TotalTTC] AS [TotalTTC], 
      [V_Report_EtatJournalDesVentesDetaillee].[HR] AS [HR], 
      [V_Report_EtatJournalDesVentesDetaillee].[Nom] AS [Nom], 
      [V_Report_EtatJournalDesVentesDetaillee].[NumeroFacture] AS [NumeroFacture]
      FROM [dbo].[V_Report_EtatJournalDesVentesDetaillee] AS [V_Report_EtatJournalDesVentesDetaillee]</DefiningQuery>
          </EntitySet>
        </EntityContainer>
        <EntityType Name="CAISSE">
          <Key>
            <PropertyRef Name="Poste" />
          </Key>
          <Property Name="Poste" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="MontantCaisse" Type="decimal" Nullable="false" Scale="3" />
        </EntityType>
        <EntityType Name="MOUVEMENT_ETATS">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="TypeOperation" Type="varchar" MaxLength="255" />
          <Property Name="Date" Type="datetime" />
          <Property Name="NumeroOperation" Type="varchar" MaxLength="255" />
          <Property Name="Credit" Type="decimal" Scale="3" />
          <Property Name="Debit" Type="decimal" Scale="3" />
          <Property Name="CodeClient" Type="varchar" MaxLength="255" />
          <Property Name="CodeMutuelle" Type="varchar" MaxLength="255" />
          <Property Name="TotalVenteHT" Type="decimal" Scale="3" />
          <Property Name="TotalVenteTTC" Type="decimal" Scale="3" />
          <Property Name="MontantCnam" Type="decimal" Scale="3" />
          <Property Name="MontantMutuelle" Type="decimal" Scale="3" />
          <Property Name="CodeFournisseur" Type="varchar" MaxLength="255" />
          <Property Name="TotalAchatHT" Type="decimal" Scale="3" />
          <Property Name="TotalAchatTTC" Type="decimal" Scale="3" />
          <Property Name="CodeArticle" Type="varchar" MaxLength="255" />
          <Property Name="Designation" Type="varchar" MaxLength="255" />
          <Property Name="CodeCategorie" Type="int" />
          <Property Name="CodeForme" Type="int" />
          <Property Name="Quantite" Type="int" />
          <Property Name="QuantiteUnitaire" Type="int" />
          <Property Name="PrixVenteHT" Type="decimal" Scale="3" />
          <Property Name="PrixVenteTTC" Type="decimal" Scale="3" />
          <Property Name="Honoraire" Type="decimal" Scale="3" />
          <Property Name="Exonorertva" Type="bit" />
          <Property Name="Tva" Type="decimal" Scale="3" />
          <Property Name="TotalTVA" Type="decimal" Scale="3" />
          <Property Name="PrixAchatHT" Type="decimal" Scale="3" />
          <Property Name="PrixAchatTTC" Type="decimal" Scale="3" />
          <Property Name="Remise" Type="decimal" Scale="3" />
          <Property Name="TotalRemise" Type="decimal" Scale="3" />
          <Property Name="NumeroReglement" Type="varchar" MaxLength="255" />
          <Property Name="CodeNatureReglement" Type="int" />
          <Property Name="MontantRegle" Type="decimal" Scale="3" />
          <Property Name="NumeroFacture" Type="varchar" MaxLength="255" />
          <Property Name="DateEcheance" Type="date" />
          <Property Name="CodePersonnel" Type="varchar" MaxLength="50" />
          <Property Name="LibellePoste" Type="varchar" MaxLength="255" />
          <Property Name="Vider" Type="bit" />
          <Property Name="Recu" Type="decimal" Scale="3" />
          <Property Name="Supprimer" Type="bit" />
          <Property Name="NumeroLot" Type="varchar" MaxLength="255" />
        </EntityType>
        <EntityType Name="PARAMETRE_PHARMACIE">
          <Key>
            <PropertyRef Name="Code" />
          </Key>
          <Property Name="Code" Type="nchar" Nullable="false" MaxLength="10" />
          <Property Name="CodePharmacie" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Pharmacie" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="NCnam" Type="varchar" MaxLength="255" />
          <Property Name="Affiliation1" Type="varchar" MaxLength="255" />
          <Property Name="Affiliation2" Type="varchar" MaxLength="255" />
          <Property Name="Adresse" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Telephone" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Fax" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="CodeTVA" Type="varchar" MaxLength="255" />
          <Property Name="Rib" Type="varchar" Nullable="false" MaxLength="255" />
          <Property Name="Messagederoulant1" Type="varchar" MaxLength="255" />
          <Property Name="Messagederoulant2" Type="varchar" MaxLength="255" />
          <Property Name="Timbre" Type="decimal" Scale="3" />
          <Property Name="DemandeMotDePasse" Type="bit" />
          <Property Name="DateMigration" Type="date" />
          <Property Name="SmtpMail" Type="varchar" MaxLength="255" />
          <Property Name="PortMail" Type="varchar" MaxLength="50" />
          <Property Name="AdresseMailDestinateur" Type="varchar" MaxLength="255" />
          <Property Name="SujetMail" Type="varchar" MaxLength="255" />
          <Property Name="TexteMail" Type="varchar" MaxLength="550" />
          <Property Name="MotDePasseDestinateur" Type="varchar" MaxLength="255" />
          <Property Name="AutoriserEnvoiMail" Type="bit" Nullable="false" />
          <Property Name="NbreJourValiditeParDefaut" Type="int" />
          <Property Name="NumeroLotProduction" Type="nvarchar" Nullable="false" MaxLength="50" />
          <Property Name="Latitude_Longitude" Type="varchar" MaxLength="255" />
          <Property Name="TailleCodeCNAM" Type="int" />
          <Property Name="TailleListe" Type="int" />
          <Property Name="TailleCaractere" Type="int" />
          <Property Name="PoliceCaractere" Type="varchar" MaxLength="50" />
          <Property Name="Texte" Type="varchar" MaxLength="255" />
          <Property Name="TauxRemise" Type="smallint" Nullable="false" />
          <Property Name="CodeGSU" Type="varchar" MaxLength="5" />
          <Property Name="ImageCodeABarre" Type="image" />
          <Property Name="ActiverOMFAPCI" Type="bit" Nullable="false" />
          <Property Name="ActiverBCB" Type="bit" Nullable="false" />
          <Property Name="ActiverBCBDateFin" Type="datetime" />
          <Property Name="Version" Type="varchar" MaxLength="50" />
          <Property Name="GererBon" Type="bit" />
          <Property Name="NombreJoursValiditerOrdonnance" Type="int" />
          <Property Name="NombreJoursValiditerPriseEnCharge" Type="int" />
          <Property Name="NombreJoursValiditerAppareillage" Type="int" />
          <Property Name="QuantiteMultipleDeCinq" Type="int" Nullable="false" />
          <Property Name="ImpressionDirectApresVente" Type="bit" />
          <Property Name="HistoriqueMouvementArticle" Type="bit" />
          <Property Name="NbrCommandePourClasserManquant" Type="int" />
          <Property Name="TypeClassementManquant" Type="int" />
          <Property Name="DateDerniereMiseAJour" Type="date" />
          <Property Name="NombreCopieImpBon" Type="int" />
          <Property Name="AutoriserModificationPrixDansAchat" Type="bit" />
          <Property Name="MettreAJourPrixFrigo" Type="bit" />
          <Property Name="AfficherReglementsSupprimes" Type="bit" />
          <Property Name="AutoriserSaisieNonMembeFamille" Type="bit" />
          <Property Name="ImprimerUnEtiquette" Type="bit" />
        </EntityType>
        <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « pharma.dbo.V_Report_EtatDesVentes » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
        <EntityType Name="V_Report_EtatDesVentes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="bigint" Nullable="false" />
          <Property Name="NumeroOperation" Type="varchar(max)" />
          <Property Name="Date" Type="date" />
          <Property Name="Type" Type="varchar(max)" />
          <Property Name="Nom" Type="varchar(max)" />
          <Property Name="MP" Type="varchar(max)" />
          <Property Name="TotalRemise" Type="decimal" Scale="3" />
          <Property Name="TotalTTC" Type="decimal" Scale="3" />
          <Property Name="LibellePoste" Type="varchar(max)" />
          <Property Name="NomUtilisateur" Type="varchar(max)" />
          <Property Name="CodeClient" Type="varchar(max)" />
          <Property Name="TypeOperation" Type="varchar(max)" />
          <Property Name="NumeroFacture" Type="varchar(max)" />
          <Property Name="CodeNatureReglement" Type="int" />
          <Property Name="CodePersonnel" Type="varchar(max)" />
          <Property Name="CodeMutuelle" Type="varchar(max)" />
          <Property Name="Mutuelle" Type="varchar(max)" />
          <Property Name="MontantCnam" Type="decimal" Scale="3" />
          <Property Name="MontantMutuelle" Type="decimal" Scale="3" />
          <Property Name="Vider" Type="bit" />
          <Property Name="Credit" Type="decimal" Scale="3" />
          <Property Name="Debit" Type="decimal" Scale="3" />
          <Property Name="Marge" Type="decimal" Scale="3" />
        </EntityType>
        <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « pharma.dbo.V_Report_EtatDetailDesVentes » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
        <EntityType Name="V_Report_EtatDetailDesVentes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="NumeroOperation" Type="varchar(max)" />
          <Property Name="Date" Type="date" />
          <Property Name="Designation" Type="varchar(max)" />
          <Property Name="LibelleForme" Type="varchar(max)" />
          <Property Name="LibelleCategorie" Type="varchar(max)" />
          <Property Name="Nom" Type="varchar(max)" />
          <Property Name="Quantite" Type="int" />
          <Property Name="Tva" Type="decimal" Scale="3" />
          <Property Name="TotalTVA" Type="decimal" Scale="3" />
          <Property Name="Remise" Type="decimal" Scale="3" />
          <Property Name="TotalVenteTTC" Type="decimal" Scale="3" />
          <Property Name="TotalMutuelle" Type="decimal" Scale="3" />
          <Property Name="TotalCNAM" Type="decimal" Scale="3" />
          <Property Name="CodeArticle" Type="varchar(max)" />
          <Property Name="CodeABarre" Type="varchar(max)" />
          <Property Name="CodeClient" Type="varchar(max)" />
          <Property Name="TotalVenteHT" Type="decimal" Scale="3" />
          <Property Name="NumeroFacture" Type="varchar(max)" />
          <Property Name="CodeCategorie" Type="int" />
          <Property Name="CodeForme" Type="int" />
        </EntityType>
        <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « pharma.dbo.V_Report_EtatHitParade » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
        <EntityType Name="V_Report_EtatHitParade">
          <Key>
            
            <PropertyRef Name="nom" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="Date" Type="datetime" />
          <Property Name="CodeABarre" Type="varchar(max)" />
          <Property Name="Designation" Type="varchar(max)" />
          <Property Name="LibelleForme" Type="varchar(max)" />
          <Property Name="LibelleCategorie" Type="varchar(max)" />
          <Property Name="Quantite" Type="int" />
          <Property Name="Stock" Type="int" />
          <Property Name="TotalVenteTTC" Type="decimal" Scale="3" />
          <Property Name="Rayon" Type="varchar(max)" />
          <Property Name="nom" Type="varchar" Nullable="false" MaxLength="1" />
          <Property Name="DatePeremption" Type="date" />
          <Property Name="CodePersonnel" Type="varchar(max)" />
          <Property Name="CodeArticle" Type="int" />
          <Property Name="CodeLabo" Type="int" />
          <Property Name="CodeForme" Type="int" />
          <Property Name="CodeCategorie" Type="int" />
        </EntityType>
        <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « pharma.dbo.V_Report_EtatJournalDesVentes » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
        <EntityType Name="V_Report_EtatJournalDesVentes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="Date" Type="date" />
          <Property Name="Exonore" Type="decimal" Scale="3" />
          <Property Name="BaseTVA6" Type="decimal" Scale="3" />
          <Property Name="TVA6" Type="decimal" Scale="3" />
          <Property Name="BaseTVA12" Type="decimal" Scale="3" />
          <Property Name="TVA12" Type="decimal" Scale="3" />
          <Property Name="BaseTVA18" Type="decimal" Scale="3" />
          <Property Name="TVA18" Type="decimal" Scale="3" />
          <Property Name="TotalHT" Type="decimal" Scale="3" />
          <Property Name="TotalTVA" Type="decimal" Scale="3" />
          <Property Name="TotalTTC" Type="decimal" Scale="3" />
          <Property Name="HR" Type="decimal" Scale="3" />
          <Property Name="Nom" Type="varchar" MaxLength="255" />
          <Property Name="NumeroFacture" Type="varchar" MaxLength="255" />
        </EntityType>
        <!--Erreurs détectées durant la génération :
      Avertissement 6002: La table/vue « pharma.dbo.V_Report_EtatJournalDesVentesDetaillee » n'a pas de clé primaire définie. La clé a été déduite et la définition a été créée en tant que table/vue en lecture seule.
      -->
        <EntityType Name="V_Report_EtatJournalDesVentesDetaillee">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="Date" Type="date" />
          <Property Name="Exonore" Type="decimal" Scale="3" />
          <Property Name="BaseTVA6" Type="decimal" Scale="3" />
          <Property Name="TVA6" Type="decimal" Scale="3" />
          <Property Name="BaseTVA12" Type="decimal" Scale="3" />
          <Property Name="TVA12" Type="decimal" Scale="3" />
          <Property Name="BaseTVA18" Type="decimal" Scale="3" />
          <Property Name="TVA18" Type="decimal" Scale="3" />
          <Property Name="TotalHT" Type="decimal" Scale="3" />
          <Property Name="TotalTVA" Type="decimal" Scale="3" />
          <Property Name="TotalTTC" Type="decimal" Scale="3" />
          <Property Name="HR" Type="decimal" Scale="3" />
          <Property Name="Nom" Type="varchar" MaxLength="255" />
          <Property Name="NumeroFacture" Type="varchar" MaxLength="255" />
        </EntityType>
        <Function Name="DecimalEnLettres" ReturnType="varchar" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="true" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Nombre" Type="decimal" Mode="In" />
        </Function>
        <Function Name="P_Etat_ListeDesReleveCNAM" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
          <Parameter Name="Type" Type="varchar(max)" Mode="In" />
          <Parameter Name="Numero" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_RecapCaisse_CNAM" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Vide" Type="bit" Mode="In" />
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
          <Parameter Name="Poste" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_RecapCaisse_Credit" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Vide" Type="bit" Mode="In" />
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
          <Parameter Name="Poste" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_RecapCaisse_Mutuelle" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Vide" Type="bit" Mode="In" />
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
          <Parameter Name="Poste" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_RecapCaisse_ReglementCreditMutuelle" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Vide" Type="bit" Mode="In" />
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
          <Parameter Name="Poste" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_RecapCaisse_RemiseRglement" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Vide" Type="bit" Mode="In" />
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
          <Parameter Name="Poste" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_RecapCaisse_RemiseVente" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Vide" Type="bit" Mode="In" />
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
          <Parameter Name="Poste" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_RecapCaisse_RetourVente" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Vide" Type="bit" Mode="In" />
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
          <Parameter Name="Poste" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_RecapCaisse_VenteAuComptant" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Vide" Type="bit" Mode="In" />
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
          <Parameter Name="Poste" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_Relever_CNAM_MaladieOrdinaire" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="NumeroReleve" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_Report_DetailsTVA" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="DateDebut" Type="varchar(max)" Mode="In" />
          <Parameter Name="DateFin" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatDesFactures" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Exonorertva" Type="int" Mode="In" />
          <Parameter Name="DateDebut" Type="varchar(max)" Mode="In" />
          <Parameter Name="DateFin" Type="varchar(max)" Mode="In" />
          <Parameter Name="TenirCompteHonoraire" Type="bit" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatDesVentes" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Vide" Type="bit" Mode="In" />
          <Parameter Name="DateDebut" Type="date" Mode="In" />
          <Parameter Name="DateFin" Type="date" Mode="In" />
          <Parameter Name="CodeClient" Type="varchar(max)" Mode="In" />
          <Parameter Name="ModePaiement" Type="int" Mode="In" />
          <Parameter Name="Type" Type="varchar(max)" Mode="In" />
          <Parameter Name="CodePersonnel" Type="int" Mode="In" />
          <Parameter Name="Poste" Type="int" Mode="In" />
          <Parameter Name="Facturer" Type="int" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatDetailDesVentes" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="DateDebut" Type="varchar(max)" Mode="In" />
          <Parameter Name="DateFin" Type="varchar(max)" Mode="In" />
          <Parameter Name="CodeArticle" Type="varchar(max)" Mode="In" />
          <Parameter Name="CodeCategorie" Type="int" Mode="In" />
          <Parameter Name="CodeForme" Type="int" Mode="In" />
          <Parameter Name="CodeClient" Type="varchar(max)" Mode="In" />
          <Parameter Name="RetourUniquement" Type="bit" Mode="In" />
          <Parameter Name="CodePersonnel" Type="int" Mode="In" />
          <Parameter Name="Facturer" Type="int" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatDetailsCaisse" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Vide" Type="bit" Mode="In" />
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
          <Parameter Name="ModePaiement" Type="int" Mode="In" />
          <Parameter Name="Type" Type="varchar(max)" Mode="In" />
          <Parameter Name="CodePersonnel" Type="int" Mode="In" />
          <Parameter Name="Poste" Type="int" Mode="In" />
          <Parameter Name="CodeClient" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatDetailsTVA" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
          <Parameter Name="Tva" Type="decimal" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatFacureJournaliere" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="NumeroFacture" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatHitParade" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
          <Parameter Name="CodeCategorie" Type="int" Mode="In" />
          <Parameter Name="CodeForme" Type="int" Mode="In" />
          <Parameter Name="CodeLabo" Type="int" Mode="In" />
          <Parameter Name="Rayon" Type="varchar(max)" Mode="In" />
          <Parameter Name="CodePersonnel" Type="int" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatJournalDesAchats" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="DateDebut" Type="date" Mode="In" />
          <Parameter Name="DateFin" Type="date" Mode="In" />
          <Parameter Name="CodeFournisseur" Type="varchar(max)" Mode="In" />
          <Parameter Name="TypeDate" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatJournalDesVentes" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Exonorertva" Type="int" Mode="In" />
          <Parameter Name="DateDebut" Type="varchar(max)" Mode="In" />
          <Parameter Name="DateFin" Type="varchar(max)" Mode="In" />
          <Parameter Name="TenirCompteHonoraire" Type="bit" Mode="In" />
          <Parameter Name="Facturer" Type="int" Mode="In" />
          <Parameter Name="Detail" Type="bit" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatJournalReleveMutuelle" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="CodeMutuelle" Type="varchar(max)" Mode="In" />
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatListeDesMedecins" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Code" Type="varchar(max)" Mode="In" />
          <Parameter Name="IdCnam" Type="varchar(max)" Mode="In" />
          <Parameter Name="NomMedecin" Type="varchar(max)" Mode="In" />
          <Parameter Name="Specialite" Type="varchar(max)" Mode="In" />
          <Parameter Name="Ville" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatOrdonnancier" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="DateDebut" Type="varchar" Mode="In" />
          <Parameter Name="DateFin" Type="varchar" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatRecapCaisse" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Vide" Type="bit" Mode="In" />
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
          <Parameter Name="Poste" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatStatistiqueFournisseurs" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="DateDebut" Type="date" Mode="In" />
          <Parameter Name="DateFin" Type="date" Mode="In" />
          <Parameter Name="TypeDate" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatStockParCategorieIntervalleMarge" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="LibelleCategorie" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="P_Report_EtatValeurTVAVente" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="DateDebut" Type="datetime" Mode="In" />
          <Parameter Name="DateFin" Type="datetime" Mode="In" />
          <Parameter Name="Exonere" Type="bit" Mode="In" />
        </Function>
        <Function Name="P_Report_VentesAnnuelles" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" />
        <Function Name="P_Report_VentesMensuelles" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Annee" Type="int" Mode="In" />
        </Function>
        <Function Name="P_Report_VentesQuotidienne" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="Mois" Type="int" Mode="In" />
          <Parameter Name="Annee" Type="int" Mode="In" />
        </Function>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="SaleReportModel" Alias="Self" p1:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:p1="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="SaleReportEntities" p1:LazyLoadingEnabled="true">
          <EntitySet Name="PARAMETRE_PHARMACIE" EntityType="SaleReportModel.PARAMETRE_PHARMACIE" />
          <EntitySet Name="MOUVEMENT_ETATS" EntityType="SaleReportModel.MOUVEMENT_ETATS" />
          <EntitySet Name="CAISSE" EntityType="SaleReportModel.CAISSE" />
          <FunctionImport Name="P_Report_EtatDesFactures">
            <Parameter Name="Exonorertva" Mode="In" Type="Int32" />
            <Parameter Name="DateDebut" Mode="In" Type="String" />
            <Parameter Name="DateFin" Mode="In" Type="String" />
            <Parameter Name="TenirCompteHonoraire" Mode="In" Type="Boolean" />
          </FunctionImport>
          <FunctionImport Name="P_Report_EtatDetailsTVA" ReturnType="Collection(SaleReportModel.P_Report_EtatDetailsTVA_Result)">
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="Tva" Mode="In" Type="Decimal" />
          </FunctionImport>
          <FunctionImport Name="P_Report_EtatValeurTVAVente" ReturnType="Collection(SaleReportModel.P_Report_EtatValeurTVAVente_Result)">
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="Exonere" Mode="In" Type="Boolean" />
          </FunctionImport>
          <FunctionImport Name="P_Report_VentesAnnuelles" ReturnType="Collection(SaleReportModel.P_Report_VentesAnnuelles_Result)" />
          <FunctionImport Name="P_Report_VentesMensuelles" ReturnType="Collection(SaleReportModel.P_Report_VentesMensuelles_Result)">
          <Parameter Name="Annee" Mode="In" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="P_Report_VentesQuotidienne" ReturnType="Collection(SaleReportModel.P_Report_VentesQuotidienne_Result)">
            <Parameter Name="Mois" Mode="In" Type="Int32" />
            <Parameter Name="Annee" Mode="In" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="P_Relever_CNAM_MaladieOrdinaire" ReturnType="Collection(SaleReportModel.P_Relever_CNAM_MaladieOrdinaire_Result)">
          <Parameter Name="NumeroReleve" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_Report_DetailsTVA" ReturnType="Collection(SaleReportModel.P_Report_DetailsTVA_Result)">
            <Parameter Name="DateDebut" Mode="In" Type="String" />
            <Parameter Name="DateFin" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_RecapCaisse_Mutuelle" ReturnType="Collection(SaleReportModel.P_RecapCaisse_Mutuelle_Result)">
            <Parameter Name="Vide" Mode="In" Type="Boolean" />
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="Poste" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_RecapCaisse_ReglementCreditMutuelle" ReturnType="Collection(SaleReportModel.P_RecapCaisse_ReglementCreditMutuelle_Result)">
            <Parameter Name="Vide" Mode="In" Type="Boolean" />
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="Poste" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_RecapCaisse_RemiseRglement" ReturnType="Collection(SaleReportModel.P_RecapCaisse_RemiseRglement_Result)">
            <Parameter Name="Vide" Mode="In" Type="Boolean" />
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="Poste" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_RecapCaisse_RemiseVente" ReturnType="Collection(SaleReportModel.P_RecapCaisse_RemiseVente_Result)">
            <Parameter Name="Vide" Mode="In" Type="Boolean" />
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="Poste" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_RecapCaisse_RetourVente" ReturnType="Collection(SaleReportModel.P_RecapCaisse_RetourVente_Result)">
            <Parameter Name="Vide" Mode="In" Type="Boolean" />
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="Poste" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_RecapCaisse_CNAM" ReturnType="Collection(SaleReportModel.P_RecapCaisse_CNAM_Result)">
            <Parameter Name="Vide" Mode="In" Type="Boolean" />
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="Poste" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_RecapCaisse_VenteAuComptant" ReturnType="Collection(SaleReportModel.P_RecapCaisse_VenteAuComptant_Result)">
            <Parameter Name="Vide" Mode="In" Type="Boolean" />
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="Poste" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_Report_EtatRecapCaisse" ReturnType="Collection(SaleReportModel.P_Report_EtatRecapCaisse_Result)">
            <Parameter Name="Vide" Mode="In" Type="Boolean" />
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="Poste" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_Report_EtatHitParade" ReturnType="Collection(SaleReportModel.P_Report_EtatHitParade_Result)">
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="CodeCategorie" Mode="In" Type="Int32" />
            <Parameter Name="CodeForme" Mode="In" Type="Int32" />
            <Parameter Name="CodeLabo" Mode="In" Type="Int32" />
            <Parameter Name="Rayon" Mode="In" Type="String" />
            <Parameter Name="CodePersonnel" Mode="In" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="P_Etat_ListeDesReleveCNAM" ReturnType="Collection(SaleReportModel.P_Etat_ListeDesReleveCNAM_Result)">
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="Type" Mode="In" Type="String" />
            <Parameter Name="Numero" Mode="In" Type="String" />
          </FunctionImport>
          <EntitySet Name="V_Report_EtatDetailDesVentes" EntityType="SaleReportModel.V_Report_EtatDetailDesVentes" />
          <EntitySet Name="V_Report_EtatDesVentes" EntityType="SaleReportModel.V_Report_EtatDesVentes" />
          <EntitySet Name="V_Report_EtatHitParade" EntityType="SaleReportModel.V_Report_EtatHitParade" />
          <FunctionImport Name="P_Report_EtatJournalDesVentes" ReturnType="Collection(SaleReportModel.P_Report_EtatJournalDesVentes_Result)">
            <Parameter Name="Exonorertva" Mode="In" Type="Int32" />
            <Parameter Name="DateDebut" Mode="In" Type="String" />
            <Parameter Name="DateFin" Mode="In" Type="String" />
            <Parameter Name="TenirCompteHonoraire" Mode="In" Type="Boolean" />
            <Parameter Name="Facturer" Mode="In" Type="Int32" />
            <Parameter Name="Detail" Mode="In" Type="Boolean" />
          </FunctionImport>
          <EntitySet Name="V_Report_EtatJournalDesVentes" EntityType="SaleReportModel.V_Report_EtatJournalDesVentes" />
          <EntitySet Name="V_Report_EtatJournalDesVentesDetaillee" EntityType="SaleReportModel.V_Report_EtatJournalDesVentesDetaillee" />
          <FunctionImport Name="P_Report_EtatStatistiqueFournisseurs" ReturnType="Collection(SaleReportModel.P_Report_EtatStatistiqueFournisseurs_Result)">
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="TypeDate" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_Report_EtatJournalDesAchats" ReturnType="Collection(SaleReportModel.P_Report_EtatJournalDesAchats_Result)">
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="CodeFournisseur" Mode="In" Type="String" />
            <Parameter Name="TypeDate" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_Report_EtatDesVentes" ReturnType="Collection(SaleReportModel.P_Report_EtatDesVentes_Result)">
            <Parameter Name="Vide" Mode="In" Type="Boolean" />
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="CodeClient" Mode="In" Type="String" />
            <Parameter Name="ModePaiement" Mode="In" Type="Int32" />
            <Parameter Name="Type" Mode="In" Type="String" />
            <Parameter Name="CodePersonnel" Mode="In" Type="Int32" />
            <Parameter Name="Poste" Mode="In" Type="Int32" />
            <Parameter Name="Facturer" Mode="In" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="P_Report_EtatDetailDesVentes" ReturnType="Collection(SaleReportModel.P_Report_EtatDetailDesVentes_Result)">
            <Parameter Name="DateDebut" Mode="In" Type="String" />
            <Parameter Name="DateFin" Mode="In" Type="String" />
            <Parameter Name="CodeArticle" Mode="In" Type="String" />
            <Parameter Name="CodeCategorie" Mode="In" Type="Int32" />
            <Parameter Name="CodeForme" Mode="In" Type="Int32" />
            <Parameter Name="CodeClient" Mode="In" Type="String" />
            <Parameter Name="RetourUniquement" Mode="In" Type="Boolean" />
            <Parameter Name="CodePersonnel" Mode="In" Type="Int32" />
            <Parameter Name="Facturer" Mode="In" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="P_Report_EtatJournalReleveMutuelle" ReturnType="Collection(SaleReportModel.P_Report_EtatJournalReleveMutuelle_Result)">
            <Parameter Name="CodeMutuelle" Mode="In" Type="String" />
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
          </FunctionImport>
          <FunctionImport Name="P_Report_EtatFacureJournaliere" ReturnType="Collection(SaleReportModel.P_Report_EtatFacureJournaliere_Result)">
          <Parameter Name="NumeroFacture" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_Report_EtatStockParCategorieIntervalleMarge" ReturnType="Collection(SaleReportModel.P_Report_EtatStockParCategorieIntervalleMarge_Result)">
          <Parameter Name="LibelleCategorie" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_Report_EtatListeDesMedecins" ReturnType="Collection(SaleReportModel.P_Report_EtatListeDesMedecins_Result)">
            <Parameter Name="Code" Mode="In" Type="String" />
            <Parameter Name="IdCnam" Mode="In" Type="String" />
            <Parameter Name="NomMedecin" Mode="In" Type="String" />
            <Parameter Name="Specialite" Mode="In" Type="String" />
            <Parameter Name="Ville" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_Report_EtatDetailsCaisse" ReturnType="Collection(SaleReportModel.P_Report_EtatDetailsCaisse_Result)">
            <Parameter Name="Vide" Mode="In" Type="Boolean" />
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="ModePaiement" Mode="In" Type="Int32" />
            <Parameter Name="Type" Mode="In" Type="String" />
            <Parameter Name="CodePersonnel" Mode="In" Type="Int32" />
            <Parameter Name="Poste" Mode="In" Type="Int32" />
            <Parameter Name="CodeClient" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_Report_EtatOrdonnancier" ReturnType="Collection(SaleReportModel.P_Report_EtatOrdonnancier_Result)">
            <Parameter Name="DateDebut" Mode="In" Type="String" />
            <Parameter Name="DateFin" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="P_RecapCaisse_Credit" ReturnType="Collection(SaleReportModel.P_RecapCaisse_Credit_Result)">
            <Parameter Name="Vide" Mode="In" Type="Boolean" />
            <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
            <Parameter Name="DateFin" Mode="In" Type="DateTime" />
            <Parameter Name="Poste" Mode="In" Type="String" />
          </FunctionImport>
        </EntityContainer>
        <EntityType Name="PARAMETRE_PHARMACIE">
          <Key>
            <PropertyRef Name="Code" />
          </Key>
          <Property Type="String" Name="Code" Nullable="false" MaxLength="10" FixedLength="true" Unicode="true" />
          <Property Type="String" Name="CodePharmacie" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Pharmacie" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NCnam" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Affiliation1" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Affiliation2" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Adresse" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Telephone" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Fax" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodeTVA" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Rib" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Messagederoulant1" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Messagederoulant2" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Decimal" Name="Timbre" Precision="18" Scale="3" />
          <Property Type="Boolean" Name="DemandeMotDePasse" />
          <Property Type="DateTime" Name="DateMigration" Precision="0" />
          <Property Type="String" Name="SmtpMail" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="PortMail" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="AdresseMailDestinateur" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="SujetMail" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="TexteMail" MaxLength="550" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="MotDePasseDestinateur" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Boolean" Name="AutoriserEnvoiMail" Nullable="false" />
          <Property Type="Int32" Name="NbreJourValiditeParDefaut" />
          <Property Type="String" Name="NumeroLotProduction" Nullable="false" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Type="String" Name="Latitude_Longitude" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="TailleCodeCNAM" />
          <Property Type="Int32" Name="TailleListe" />
          <Property Type="Int32" Name="TailleCaractere" />
          <Property Type="String" Name="PoliceCaractere" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Texte" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int16" Name="TauxRemise" Nullable="false" />
          <Property Type="String" Name="CodeGSU" MaxLength="5" FixedLength="false" Unicode="false" />
          <Property Type="Binary" Name="ImageCodeABarre" MaxLength="Max" FixedLength="false" />
          <Property Type="Boolean" Name="ActiverOMFAPCI" Nullable="false" />
          <Property Type="Boolean" Name="ActiverBCB" Nullable="false" />
          <Property Type="DateTime" Name="ActiverBCBDateFin" Precision="3" />
          <Property Type="String" Name="Version" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="Boolean" Name="GererBon" />
          <Property Type="Int32" Name="NombreJoursValiditerOrdonnance" />
          <Property Type="Int32" Name="NombreJoursValiditerPriseEnCharge" />
          <Property Type="Int32" Name="NombreJoursValiditerAppareillage" />
          <Property Type="Int32" Name="QuantiteMultipleDeCinq" Nullable="false" />
          <Property Type="Boolean" Name="ImpressionDirectApresVente" />
          <Property Type="Boolean" Name="HistoriqueMouvementArticle" />
          <Property Type="Int32" Name="NbrCommandePourClasserManquant" />
          <Property Type="Int32" Name="TypeClassementManquant" />
          <Property Type="DateTime" Name="DateDerniereMiseAJour" Precision="0" />
          <Property Type="Int32" Name="NombreCopieImpBon" />
          <Property Type="Boolean" Name="AutoriserModificationPrixDansAchat" />
          <Property Type="Boolean" Name="MettreAJourPrixFrigo" />
          <Property Type="Boolean" Name="AfficherReglementsSupprimes" />
          <Property Type="Boolean" Name="AutoriserSaisieNonMembeFamille" />
          <Property Type="Boolean" Name="ImprimerUnEtiquette" />
        </EntityType>
        <EntityType Name="MOUVEMENT_ETATS">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Type="Int32" Name="Id" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Type="String" Name="TypeOperation" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="DateTime" Name="Date" Precision="3" />
          <Property Type="String" Name="NumeroOperation" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Decimal" Name="Credit" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Debit" Precision="18" Scale="3" />
          <Property Type="String" Name="CodeClient" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodeMutuelle" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Decimal" Name="TotalVenteHT" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalVenteTTC" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantCnam" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantMutuelle" Precision="18" Scale="3" />
          <Property Type="String" Name="CodeFournisseur" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Decimal" Name="TotalAchatHT" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalAchatTTC" Precision="18" Scale="3" />
          <Property Type="String" Name="CodeArticle" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Designation" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="CodeCategorie" />
          <Property Type="Int32" Name="CodeForme" />
          <Property Type="Int32" Name="Quantite" />
          <Property Type="Int32" Name="QuantiteUnitaire" />
          <Property Type="Decimal" Name="PrixVenteHT" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="PrixVenteTTC" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Honoraire" Precision="18" Scale="3" />
          <Property Type="Boolean" Name="Exonorertva" />
          <Property Type="Decimal" Name="Tva" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTVA" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="PrixAchatHT" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="PrixAchatTTC" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Remise" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalRemise" Precision="18" Scale="3" />
          <Property Type="String" Name="NumeroReglement" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="CodeNatureReglement" />
          <Property Type="Decimal" Name="MontantRegle" Precision="18" Scale="3" />
          <Property Type="String" Name="NumeroFacture" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="DateTime" Name="DateEcheance" Precision="0" />
          <Property Type="String" Name="CodePersonnel" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="LibellePoste" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Boolean" Name="Vider" />
          <Property Type="Decimal" Name="Recu" Precision="18" Scale="3" />
          <Property Type="Boolean" Name="Supprimer" />
          <Property Type="String" Name="NumeroLot" MaxLength="255" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="CAISSE">
          <Key>
            <PropertyRef Name="Poste" />
          </Key>
          <Property Type="String" Name="Poste" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="Decimal" Name="MontantCaisse" Nullable="false" Precision="18" Scale="3" />
        </EntityType>
        <ComplexType Name="P_Report_EtatDetailsTVA_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="String" Name="Designation" Nullable="true" />
          <Property Type="Int32" Name="Quantite" Nullable="true" />
          <Property Type="Decimal" Name="PrixVenteHT" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalVenteHT" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTVA" Nullable="true" Precision="18" Scale="3" />
        </ComplexType>
        <ComplexType Name="P_Report_EtatValeurTVAVente_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="Decimal" Name="TVA" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Base" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantTVA" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Honoraire" Nullable="true" Precision="18" Scale="3" />
        </ComplexType>
        <ComplexType Name="P_Report_VentesAnnuelles_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="Int32" Name="Annee" Nullable="true" />
          <Property Type="Decimal" Name="Espece" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Cheque" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Carte" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Remise" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Autre" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="ReglementClient" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Reglementcnammutuelle" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Caisse" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
        </ComplexType>
        <ComplexType Name="P_Report_VentesMensuelles_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="Int32" Name="Mois" Nullable="true" />
          <Property Type="Decimal" Name="Espece" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Cheque" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Carte" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Remise" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Autre" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="ReglementClient" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Reglementcnammutuelle" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Caisse" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
        </ComplexType>
        <ComplexType Name="P_Report_VentesQuotidienne_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="Int32" Name="NumeroJour" Nullable="true" />
          <Property Type="Decimal" Name="Espece" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Cheque" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Carte" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Remise" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Autre" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="ReglementClient" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Reglementcnammutuelle" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Caisse" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
        </ComplexType>
        <ComplexType Name="P_Relever_CNAM_MaladieOrdinaire_Result">
          <Property Type="String" Name="NumeroVente" Nullable="false" MaxLength="255" />
          <Property Type="String" Name="Ligne" Nullable="true" />
        </ComplexType>
        <ComplexType Name="P_Report_DetailsTVA_Result">
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="String" Name="Designation" Nullable="true" MaxLength="255" />
          <Property Type="Int32" Name="Quantite" Nullable="true" />
          <Property Type="Decimal" Name="PrixVenteHT" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalVenteHT" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTVA" Nullable="true" Precision="18" Scale="3" />
        </ComplexType>
        <ComplexType Name="P_RecapCaisse_Mutuelle_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="Numero" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="String" Name="Client" Nullable="true" />
          <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="MP" Nullable="true" />
          <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
          <Property Type="String" Name="Poste" Nullable="true" />
          <Property Type="String" Name="Utilisateur" Nullable="true" />
        </ComplexType>
        <ComplexType Name="P_RecapCaisse_ReglementCreditMutuelle_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="Numero" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="String" Name="Client" Nullable="true" />
          <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="MP" Nullable="true" />
          <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
          <Property Type="String" Name="Libelle" Nullable="true" />
          <Property Type="Decimal" Name="Solde" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="Poste" Nullable="true" />
          <Property Type="String" Name="Utilisateur" Nullable="true" />
        </ComplexType>
        <ComplexType Name="P_RecapCaisse_RemiseRglement_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="Numero" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="String" Name="Client" Nullable="true" />
          <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="MP" Nullable="true" />
          <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
          <Property Type="String" Name="Libelle" Nullable="true" />
          <Property Type="String" Name="Poste" Nullable="true" />
          <Property Type="String" Name="Utilisateur" Nullable="true" />
        </ComplexType>
        <ComplexType Name="P_RecapCaisse_RemiseVente_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="Numero" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="String" Name="Client" Nullable="true" />
          <Property Type="Decimal" Name="Remise" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="MP" Nullable="true" />
          <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
          <Property Type="String" Name="Poste" Nullable="true" />
          <Property Type="String" Name="Utilisateur" Nullable="true" />
        </ComplexType>
        <ComplexType Name="P_RecapCaisse_RetourVente_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="Numero" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="String" Name="Client" Nullable="true" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="MP" Nullable="true" />
          <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
          <Property Type="String" Name="Poste" Nullable="true" />
          <Property Type="String" Name="Utilisateur" Nullable="true" />
        </ComplexType>
        <ComplexType Name="P_RecapCaisse_CNAM_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="Numero" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="String" Name="Client" Nullable="true" />
          <Property Type="Decimal" Name="MontantCNAM" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="MP" Nullable="true" />
          <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
          <Property Type="String" Name="Poste" Nullable="true" />
          <Property Type="String" Name="Utilisateur" Nullable="true" />
        </ComplexType>
        <ComplexType Name="P_RecapCaisse_VenteAuComptant_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="Numero" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="String" Name="Client" Nullable="true" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="MP" Nullable="true" />
          <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
          <Property Type="String" Name="Type" Nullable="true" />
          <Property Type="String" Name="Poste" Nullable="true" />
          <Property Type="String" Name="Utilisateur" Nullable="true" />
        </ComplexType>
        <ComplexType Name="P_Report_EtatRecapCaisse_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="NumeroOperation" Nullable="true" />
          <Property Type="String" Name="TypeOperation" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="String" Name="Designation" Nullable="true" />
          <Property Type="Int32" Name="Quantite" Nullable="true" />
          <Property Type="Decimal" Name="Tva" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTVA" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Remise" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalVenteTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Debit" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalVenteHT" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Int32" Name="CodeNatureReglement" Nullable="true" />
        </ComplexType>
        <ComplexType Name="P_Report_EtatHitParade_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="CodeABarre" Nullable="true" />
          <Property Type="String" Name="Designation" Nullable="true" />
          <Property Type="String" Name="LibelleForme" Nullable="true" />
          <Property Type="String" Name="LibelleCategorie" Nullable="true" />
          <Property Type="Int32" Name="Quantite" Nullable="true" />
          <Property Type="Int32" Name="Stock" Nullable="true" />
          <Property Type="Decimal" Name="TotalVenteTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="Rayon" Nullable="true" />
          <Property Type="String" Name="nom" Nullable="false" MaxLength="1" />
          <Property Type="DateTime" Name="DatePeremption" Nullable="true" />
        </ComplexType>
        <ComplexType Name="P_Etat_ListeDesReleveCNAM_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="NumeroReleve" Nullable="true" />
          <Property Type="String" Name="Type" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="DateTime" Name="DateDebut" Nullable="true" Precision="23" />
          <Property Type="DateTime" Name="DateFin" Nullable="true" Precision="23" />
          <Property Type="Decimal" Name="MontantARembourser" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Reste" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantTotal" Nullable="true" Precision="18" Scale="3" />
        </ComplexType>
        <EntityType Name="V_Report_EtatDetailDesVentes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Type="Int32" Name="Id" Nullable="false" />
          <Property Type="String" Name="NumeroOperation" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="DateTime" Name="Date" Precision="0" />
          <Property Type="String" Name="Designation" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="LibelleForme" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="LibelleCategorie" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Nom" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="Quantite" />
          <Property Type="Decimal" Name="Tva" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTVA" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Remise" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalVenteTTC" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalMutuelle" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalCNAM" Precision="18" Scale="3" />
          <Property Type="String" Name="CodeArticle" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodeABarre" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodeClient" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="Decimal" Name="TotalVenteHT" Precision="18" Scale="3" />
          <Property Type="String" Name="NumeroFacture" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="CodeCategorie" />
          <Property Type="Int32" Name="CodeForme" />
        </EntityType>
        <EntityType Name="V_Report_EtatDesVentes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Type="Int64" Name="Id" Nullable="false" />
          <Property Type="String" Name="NumeroOperation" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="DateTime" Name="Date" Precision="0" />
          <Property Type="String" Name="Type" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Nom" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="MP" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="Decimal" Name="TotalRemise" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Precision="18" Scale="3" />
          <Property Type="String" Name="LibellePoste" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NomUtilisateur" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodeClient" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="TypeOperation" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NumeroFacture" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="CodeNatureReglement" />
          <Property Type="String" Name="CodePersonnel" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="CodeMutuelle" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Mutuelle" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="Decimal" Name="MontantCnam" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantMutuelle" Precision="18" Scale="3" />
          <Property Type="Boolean" Name="Vider" />
          <Property Type="Decimal" Name="Credit" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Debit" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Marge" Precision="18" Scale="3" />
        </EntityType>
        <EntityType Name="V_Report_EtatHitParade">
          <Key>
            <PropertyRef Name="nom" />
          </Key>
          <Property Type="Int32" Name="Id" Nullable="false" />
          <Property Type="String" Name="CodeABarre" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Designation" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="LibelleForme" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="LibelleCategorie" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="Quantite" />
          <Property Type="Int32" Name="Stock" />
          <Property Type="Decimal" Name="TotalVenteTTC" Precision="18" Scale="3" />
          <Property Type="String" Name="Rayon" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="nom" Nullable="false" MaxLength="1" FixedLength="false" Unicode="false" />
          <Property Type="DateTime" Name="DatePeremption" Precision="0" />
          <Property Type="DateTime" Name="Date" Precision="3" />
          <Property Type="Int32" Name="CodeArticle" />
          <Property Type="Int32" Name="CodeLabo" />
          <Property Type="Int32" Name="CodeForme" />
          <Property Type="Int32" Name="CodeCategorie" />
          <Property Type="String" Name="CodePersonnel" MaxLength="Max" FixedLength="false" Unicode="false" />
        </EntityType>
        <ComplexType Name="P_Report_EtatJournalDesVentes_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" />
          <Property Type="Decimal" Name="Exonore" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="BaseTVA6" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA6" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="BaseTVA12" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA12" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="BaseTVA18" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA18" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalHT" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTVA" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="38" Scale="3" />
          <Property Type="Decimal" Name="HR" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="Nom" Nullable="true" MaxLength="255" />
          <Property Type="String" Name="NumeroFacture" Nullable="true" MaxLength="255" />
        </ComplexType>
        <EntityType Name="V_Report_EtatJournalDesVentes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Type="Int32" Name="Id" Nullable="false" />
          <Property Type="DateTime" Name="Date" Precision="0" />
          <Property Type="Decimal" Name="Exonore" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="BaseTVA6" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA6" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="BaseTVA12" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA12" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="BaseTVA18" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA18" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalHT" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTVA" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="HR" Precision="18" Scale="3" />
          <Property Type="String" Name="Nom" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NumeroFacture" MaxLength="255" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="V_Report_EtatJournalDesVentesDetaillee">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Type="Int32" Name="Id" Nullable="false" />
          <Property Type="DateTime" Name="Date" Precision="0" />
          <Property Type="Decimal" Name="Exonore" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="BaseTVA6" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA6" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="BaseTVA12" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA12" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="BaseTVA18" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA18" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalHT" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTVA" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="HR" Precision="18" Scale="3" />
          <Property Type="String" Name="Nom" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="NumeroFacture" MaxLength="255" FixedLength="false" Unicode="false" />
        </EntityType>
        <ComplexType Name="P_Report_EtatStatistiqueFournisseurs_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="NomFournisseur" Nullable="true" MaxLength="255" />
          <Property Type="Decimal" Name="HT" Nullable="true" Precision="38" Scale="3" />
          <Property Type="Decimal" Name="TVA" Nullable="true" Precision="38" Scale="3" />
          <Property Type="Decimal" Name="TTC" Nullable="true" Precision="38" Scale="3" />
          <Property Type="Decimal" Name="Remise" Nullable="true" Precision="38" Scale="3" />
          <Property Type="Decimal" Name="VenteTTC" Nullable="true" Precision="38" Scale="3" />
          <Property Type="Decimal" Name="ResteAPayer" Nullable="true" Precision="38" Scale="3" />
        </ComplexType>
        <ComplexType Name="P_Report_EtatJournalDesAchats_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="NumeroAchat" Nullable="false" MaxLength="255" />
          <Property Type="DateTime" Name="Date" Nullable="false" Precision="23" />
          <Property Type="DateTime" Name="DateBlFacture" Nullable="false" />
          <Property Type="String" Name="NomFournisseur" Nullable="true" MaxLength="255" />
          <Property Type="String" Name="NumeroBL_Facture" Nullable="true" MaxLength="255" />
          <Property Type="Decimal" Name="TotalHT" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalRemise" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TVA" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="String" Name="CodeFournisseur" Nullable="true" MaxLength="255" />
          <Property Type="Decimal" Name="ValeurVenteTTC" Nullable="false" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTVAVente" Nullable="true" Precision="38" Scale="6" />
        </ComplexType>
        <ComplexType Name="P_Report_EtatDesVentes_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="NumeroOperation" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="String" Name="Type" Nullable="true" />
          <Property Type="String" Name="Nom" Nullable="true" />
          <Property Type="String" Name="MP" Nullable="true" />
          <Property Type="Decimal" Name="TotalRemise" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="LibellePoste" Nullable="true" />
          <Property Type="String" Name="NomUtilisateur" Nullable="true" />
          <Property Type="String" Name="CodeClient" Nullable="true" />
          <Property Type="String" Name="TypeOperation" Nullable="true" />
          <Property Type="String" Name="NumeroFacture" Nullable="true" />
          <Property Type="Int32" Name="CodeNatureReglement" Nullable="true" />
          <Property Type="String" Name="CodePersonnel" Nullable="true" />
          <Property Type="String" Name="CodeMutuelle" Nullable="true" />
          <Property Type="String" Name="Mutuelle" Nullable="true" />
          <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Boolean" Name="Vider" Nullable="true" />
          <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Debit" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Marge" Nullable="true" Precision="18" Scale="3" />
        </ComplexType>
        <ComplexType Name="P_Report_EtatDetailDesVentes_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="NumeroOperation" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="String" Name="Designation" Nullable="true" />
          <Property Type="String" Name="LibelleForme" Nullable="true" />
          <Property Type="String" Name="LibelleCategorie" Nullable="true" />
          <Property Type="String" Name="Nom" Nullable="true" />
          <Property Type="Int32" Name="Quantite" Nullable="true" />
          <Property Type="Decimal" Name="Tva" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTVA" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Remise" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalVenteTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalMutuelle" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalCNAM" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="CodeArticle" Nullable="true" />
          <Property Type="String" Name="CodeABarre" Nullable="true" />
          <Property Type="String" Name="CodeClient" Nullable="true" />
          <Property Type="Decimal" Name="TotalVenteHT" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="NumeroFacture" Nullable="true" />
          <Property Type="Int32" Name="CodeCategorie" Nullable="true" />
          <Property Type="Int32" Name="CodeForme" Nullable="true" />
          <Property Type="Decimal" Name="PrixVenteHT" Nullable="true" Precision="18" Scale="3" />
        </ComplexType>
        <ComplexType Name="P_Report_EtatJournalReleveMutuelle_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="NumeroReleve" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="DateTime" Name="DateDebut" Nullable="true" Precision="23" />
          <Property Type="DateTime" Name="DateFin" Nullable="true" Precision="23" />
          <Property Type="Decimal" Name="MontantTotal" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantRegle" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Reste" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="CodeMutuelle" Nullable="true" />
          <Property Type="String" Name="NomMutuelle" Nullable="true" />
        </ComplexType>
        <ComplexType Name="P_Report_EtatFacureJournaliere_Result">
          <Property Type="DateTime" Name="Date" Nullable="true" />
          <Property Type="Decimal" Name="TotalHT" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTVA" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Caisse" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
        </ComplexType>
        <ComplexType Name="P_Report_EtatStockParCategorieIntervalleMarge_Result">
          <Property Type="Decimal" Name="Marge" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="ValeurAchatHT" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="ValeurAchatHTP" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="ValeurVenteHT" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="ValeurVenteHTP" Nullable="true" Precision="18" Scale="3" />
        </ComplexType>
        <ComplexType Name="P_Report_EtatListeDesMedecins_Result">
          <Property Type="String" Name="CodeMedecin" Nullable="false" MaxLength="255" />
          <Property Type="String" Name="NomMedecin" Nullable="false" MaxLength="255" />
          <Property Type="String" Name="LibelleSpecialite" Nullable="true" MaxLength="255" />
          <Property Type="Int32" Name="CodeSpecialite" Nullable="true" />
          <Property Type="String" Name="Adresse" Nullable="false" MaxLength="255" />
          <Property Type="String" Name="Tel" Nullable="false" MaxLength="255" />
          <Property Type="String" Name="Fax" Nullable="false" MaxLength="255" />
          <Property Type="String" Name="Email" Nullable="false" MaxLength="255" />
          <Property Type="String" Name="IdentifiantCNAM" Nullable="false" MaxLength="255" />
          <Property Type="String" Name="NomVille" Nullable="true" MaxLength="255" />
          <Property Type="Int32" Name="CodeVille" Nullable="true" />
          <Property Type="Boolean" Name="bloquer" Nullable="false" />
        </ComplexType>
        <ComplexType Name="P_Report_EtatDetailsCaisse_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="Operation" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="String" Name="Nom" Nullable="true" />
          <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="NatureReglement" Nullable="true" />
          <Property Type="DateTime" Name="DateEcheance" Nullable="true" />
          <Property Type="String" Name="Vendeur" Nullable="true" />
          <Property Type="String" Name="Poste" Nullable="true" />
          <Property Type="Int32" Name="CodeNatureReglement" Nullable="true" />
          <Property Type="String" Name="Type" Nullable="true" />
          <Property Type="String" Name="TypeOperation" Nullable="true" />
          <Property Type="String" Name="CodeClient" Nullable="true" />
          <Property Type="Decimal" Name="TotalVenteTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Remise" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="CodePersonnel" Nullable="true" />
          <Property Type="String" Name="NumeroOperation" Nullable="true" />
          <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Debit" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
        </ComplexType>
        <ComplexType Name="P_Report_EtatOrdonnancier_Result">
          <Property Type="DateTime" Name="DateVente" Nullable="false" Precision="23" />
          <Property Type="DateTime" Name="DateOrdonnancier" Nullable="false" Precision="23" />
          <Property Type="String" Name="NomMedecin" Nullable="true" MaxLength="255" />
          <Property Type="Int32" Name="Numero" Nullable="false" />
          <Property Type="Int32" Name="NumeroOrdonnacier" Nullable="false" />
          <Property Type="String" Name="LibelleForme" Nullable="true" MaxLength="255" />
          <Property Type="String" Name="Designation" Nullable="true" MaxLength="255" />
          <Property Type="Int32" Name="Quantite" Nullable="true" />
          <Property Type="String" Name="Malade" Nullable="false" MaxLength="100" />
          <Property Type="String" Name="CIN" Nullable="false" MaxLength="50" />
          <Property Type="String" Name="Adresse" Nullable="false" MaxLength="255" />
          <Property Type="String" Name="TypeOrdonnance" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="NumeroVente" Nullable="true" MaxLength="50" />
          <Property Type="Boolean" Name="Supprimer" Nullable="false" />
          <Property Type="Int64" Name="Row" Nullable="true" />
        </ComplexType>
        <ComplexType Name="P_RecapCaisse_Credit_Result">
          <Property Type="Int32" Name="Id" Nullable="true" />
          <Property Type="String" Name="Numero" Nullable="true" />
          <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
          <Property Type="String" Name="Client" Nullable="true" />
          <Property Type="Decimal" Name="Solde" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="MontantCNAM" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Reglement" Nullable="true" Precision="18" Scale="3" />
          <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
          <Property Type="String" Name="MP" Nullable="true" />
          <Property Type="DateTime" Name="Echeance" Nullable="true" Precision="23" />
          <Property Type="String" Name="Poste" Nullable="true" />
          <Property Type="String" Name="Utilisateur" Nullable="true" />
        </ComplexType>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="SaleReportModelStoreContainer" CdmEntityContainer="SaleReportEntities">
          <EntitySetMapping Name="PARAMETRE_PHARMACIE">
            <EntityTypeMapping TypeName="SaleReportModel.PARAMETRE_PHARMACIE">
              <MappingFragment StoreEntitySet="PARAMETRE_PHARMACIE">
                <ScalarProperty Name="ImprimerUnEtiquette" ColumnName="ImprimerUnEtiquette" />
                <ScalarProperty Name="AutoriserSaisieNonMembeFamille" ColumnName="AutoriserSaisieNonMembeFamille" />
                <ScalarProperty Name="AfficherReglementsSupprimes" ColumnName="AfficherReglementsSupprimes" />
                <ScalarProperty Name="MettreAJourPrixFrigo" ColumnName="MettreAJourPrixFrigo" />
                <ScalarProperty Name="AutoriserModificationPrixDansAchat" ColumnName="AutoriserModificationPrixDansAchat" />
                <ScalarProperty Name="NombreCopieImpBon" ColumnName="NombreCopieImpBon" />
                <ScalarProperty Name="DateDerniereMiseAJour" ColumnName="DateDerniereMiseAJour" />
                <ScalarProperty Name="TypeClassementManquant" ColumnName="TypeClassementManquant" />
                <ScalarProperty Name="NbrCommandePourClasserManquant" ColumnName="NbrCommandePourClasserManquant" />
                <ScalarProperty Name="HistoriqueMouvementArticle" ColumnName="HistoriqueMouvementArticle" />
                <ScalarProperty Name="ImpressionDirectApresVente" ColumnName="ImpressionDirectApresVente" />
                <ScalarProperty Name="QuantiteMultipleDeCinq" ColumnName="QuantiteMultipleDeCinq" />
                <ScalarProperty Name="NombreJoursValiditerAppareillage" ColumnName="NombreJoursValiditerAppareillage" />
                <ScalarProperty Name="NombreJoursValiditerPriseEnCharge" ColumnName="NombreJoursValiditerPriseEnCharge" />
                <ScalarProperty Name="NombreJoursValiditerOrdonnance" ColumnName="NombreJoursValiditerOrdonnance" />
                <ScalarProperty Name="GererBon" ColumnName="GererBon" />
                <ScalarProperty Name="Version" ColumnName="Version" />
                <ScalarProperty Name="ActiverBCBDateFin" ColumnName="ActiverBCBDateFin" />
                <ScalarProperty Name="ActiverBCB" ColumnName="ActiverBCB" />
                <ScalarProperty Name="ActiverOMFAPCI" ColumnName="ActiverOMFAPCI" />
                <ScalarProperty Name="ImageCodeABarre" ColumnName="ImageCodeABarre" />
                <ScalarProperty Name="CodeGSU" ColumnName="CodeGSU" />
                <ScalarProperty Name="TauxRemise" ColumnName="TauxRemise" />
                <ScalarProperty Name="Texte" ColumnName="Texte" />
                <ScalarProperty Name="PoliceCaractere" ColumnName="PoliceCaractere" />
                <ScalarProperty Name="TailleCaractere" ColumnName="TailleCaractere" />
                <ScalarProperty Name="TailleListe" ColumnName="TailleListe" />
                <ScalarProperty Name="TailleCodeCNAM" ColumnName="TailleCodeCNAM" />
                <ScalarProperty Name="Latitude_Longitude" ColumnName="Latitude_Longitude" />
                <ScalarProperty Name="NumeroLotProduction" ColumnName="NumeroLotProduction" />
                <ScalarProperty Name="NbreJourValiditeParDefaut" ColumnName="NbreJourValiditeParDefaut" />
                <ScalarProperty Name="AutoriserEnvoiMail" ColumnName="AutoriserEnvoiMail" />
                <ScalarProperty Name="MotDePasseDestinateur" ColumnName="MotDePasseDestinateur" />
                <ScalarProperty Name="TexteMail" ColumnName="TexteMail" />
                <ScalarProperty Name="SujetMail" ColumnName="SujetMail" />
                <ScalarProperty Name="AdresseMailDestinateur" ColumnName="AdresseMailDestinateur" />
                <ScalarProperty Name="PortMail" ColumnName="PortMail" />
                <ScalarProperty Name="SmtpMail" ColumnName="SmtpMail" />
                <ScalarProperty Name="DateMigration" ColumnName="DateMigration" />
                <ScalarProperty Name="DemandeMotDePasse" ColumnName="DemandeMotDePasse" />
                <ScalarProperty Name="Timbre" ColumnName="Timbre" />
                <ScalarProperty Name="Messagederoulant2" ColumnName="Messagederoulant2" />
                <ScalarProperty Name="Messagederoulant1" ColumnName="Messagederoulant1" />
                <ScalarProperty Name="Rib" ColumnName="Rib" />
                <ScalarProperty Name="CodeTVA" ColumnName="CodeTVA" />
                <ScalarProperty Name="Fax" ColumnName="Fax" />
                <ScalarProperty Name="Telephone" ColumnName="Telephone" />
                <ScalarProperty Name="Adresse" ColumnName="Adresse" />
                <ScalarProperty Name="Affiliation2" ColumnName="Affiliation2" />
                <ScalarProperty Name="Affiliation1" ColumnName="Affiliation1" />
                <ScalarProperty Name="NCnam" ColumnName="NCnam" />
                <ScalarProperty Name="Pharmacie" ColumnName="Pharmacie" />
                <ScalarProperty Name="CodePharmacie" ColumnName="CodePharmacie" />
                <ScalarProperty Name="Code" ColumnName="Code" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="MOUVEMENT_ETATS">
            <EntityTypeMapping TypeName="SaleReportModel.MOUVEMENT_ETATS">
              <MappingFragment StoreEntitySet="MOUVEMENT_ETATS">
                <ScalarProperty Name="NumeroLot" ColumnName="NumeroLot" />
                <ScalarProperty Name="Supprimer" ColumnName="Supprimer" />
                <ScalarProperty Name="Recu" ColumnName="Recu" />
                <ScalarProperty Name="Vider" ColumnName="Vider" />
                <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
                <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
                <ScalarProperty Name="DateEcheance" ColumnName="DateEcheance" />
                <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
                <ScalarProperty Name="MontantRegle" ColumnName="MontantRegle" />
                <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
                <ScalarProperty Name="NumeroReglement" ColumnName="NumeroReglement" />
                <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
                <ScalarProperty Name="Remise" ColumnName="Remise" />
                <ScalarProperty Name="PrixAchatTTC" ColumnName="PrixAchatTTC" />
                <ScalarProperty Name="PrixAchatHT" ColumnName="PrixAchatHT" />
                <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
                <ScalarProperty Name="Tva" ColumnName="Tva" />
                <ScalarProperty Name="Exonorertva" ColumnName="Exonorertva" />
                <ScalarProperty Name="Honoraire" ColumnName="Honoraire" />
                <ScalarProperty Name="PrixVenteTTC" ColumnName="PrixVenteTTC" />
                <ScalarProperty Name="PrixVenteHT" ColumnName="PrixVenteHT" />
                <ScalarProperty Name="QuantiteUnitaire" ColumnName="QuantiteUnitaire" />
                <ScalarProperty Name="Quantite" ColumnName="Quantite" />
                <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
                <ScalarProperty Name="CodeCategorie" ColumnName="CodeCategorie" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
                <ScalarProperty Name="TotalAchatTTC" ColumnName="TotalAchatTTC" />
                <ScalarProperty Name="TotalAchatHT" ColumnName="TotalAchatHT" />
                <ScalarProperty Name="CodeFournisseur" ColumnName="CodeFournisseur" />
                <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
                <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
                <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
                <ScalarProperty Name="TotalVenteHT" ColumnName="TotalVenteHT" />
                <ScalarProperty Name="CodeMutuelle" ColumnName="CodeMutuelle" />
                <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
                <ScalarProperty Name="Debit" ColumnName="Debit" />
                <ScalarProperty Name="Credit" ColumnName="Credit" />
                <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="TypeOperation" ColumnName="TypeOperation" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CAISSE">
            <EntityTypeMapping TypeName="SaleReportModel.CAISSE">
              <MappingFragment StoreEntitySet="CAISSE">
                <ScalarProperty Name="MontantCaisse" ColumnName="MontantCaisse" />
                <ScalarProperty Name="Poste" ColumnName="Poste" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatDesFactures" FunctionName="SaleReportModel.Store.P_Report_EtatDesFactures">
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatDetailsTVA" FunctionName="SaleReportModel.Store.P_Report_EtatDetailsTVA">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatDetailsTVA_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="Quantite" ColumnName="Quantite" />
                <ScalarProperty Name="PrixVenteHT" ColumnName="PrixVenteHT" />
                <ScalarProperty Name="TotalVenteHT" ColumnName="TotalVenteHT" />
                <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatValeurTVAVente" FunctionName="SaleReportModel.Store.P_Report_EtatValeurTVAVente">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatValeurTVAVente_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="TVA" ColumnName="TVA" />
                <ScalarProperty Name="Base" ColumnName="Base" />
                <ScalarProperty Name="MontantTVA" ColumnName="MontantTVA" />
                <ScalarProperty Name="Honoraire" ColumnName="Honoraire" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_VentesAnnuelles" FunctionName="SaleReportModel.Store.P_Report_VentesAnnuelles">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_VentesAnnuelles_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Annee" ColumnName="Annee" />
                <ScalarProperty Name="Espece" ColumnName="Espece" />
                <ScalarProperty Name="Cheque" ColumnName="Cheque" />
                <ScalarProperty Name="Carte" ColumnName="Carte" />
                <ScalarProperty Name="Credit" ColumnName="Credit" />
                <ScalarProperty Name="Remise" ColumnName="Remise" />
                <ScalarProperty Name="Autre" ColumnName="Autre" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="ReglementClient" ColumnName="ReglementClient" />
                <ScalarProperty Name="Reglementcnammutuelle" ColumnName="Reglementcnammutuelle" />
                <ScalarProperty Name="Caisse" ColumnName="Caisse" />
                <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
                <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_VentesMensuelles" FunctionName="SaleReportModel.Store.P_Report_VentesMensuelles">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_VentesMensuelles_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Mois" ColumnName="Mois" />
                <ScalarProperty Name="Espece" ColumnName="Espece" />
                <ScalarProperty Name="Cheque" ColumnName="Cheque" />
                <ScalarProperty Name="Carte" ColumnName="Carte" />
                <ScalarProperty Name="Credit" ColumnName="Credit" />
                <ScalarProperty Name="Remise" ColumnName="Remise" />
                <ScalarProperty Name="Autre" ColumnName="Autre" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="ReglementClient" ColumnName="ReglementClient" />
                <ScalarProperty Name="Reglementcnammutuelle" ColumnName="Reglementcnammutuelle" />
                <ScalarProperty Name="Caisse" ColumnName="Caisse" />
                <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
                <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_VentesQuotidienne" FunctionName="SaleReportModel.Store.P_Report_VentesQuotidienne">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_VentesQuotidienne_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="NumeroJour" ColumnName="NumeroJour" />
                <ScalarProperty Name="Espece" ColumnName="Espece" />
                <ScalarProperty Name="Cheque" ColumnName="Cheque" />
                <ScalarProperty Name="Carte" ColumnName="Carte" />
                <ScalarProperty Name="Credit" ColumnName="Credit" />
                <ScalarProperty Name="Remise" ColumnName="Remise" />
                <ScalarProperty Name="Autre" ColumnName="Autre" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="ReglementClient" ColumnName="ReglementClient" />
                <ScalarProperty Name="Reglementcnammutuelle" ColumnName="Reglementcnammutuelle" />
                <ScalarProperty Name="Caisse" ColumnName="Caisse" />
                <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
                <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Relever_CNAM_MaladieOrdinaire" FunctionName="SaleReportModel.Store.P_Relever_CNAM_MaladieOrdinaire">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Relever_CNAM_MaladieOrdinaire_Result">
                <ScalarProperty Name="NumeroVente" ColumnName="NumeroVente" />
                <ScalarProperty Name="Ligne" ColumnName="Ligne" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_DetailsTVA" FunctionName="SaleReportModel.Store.P_Report_DetailsTVA">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_DetailsTVA_Result">
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="Quantite" ColumnName="Quantite" />
                <ScalarProperty Name="PrixVenteHT" ColumnName="PrixVenteHT" />
                <ScalarProperty Name="TotalVenteHT" ColumnName="TotalVenteHT" />
                <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_RecapCaisse_Mutuelle" FunctionName="SaleReportModel.Store.P_RecapCaisse_Mutuelle">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_Mutuelle_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Numero" ColumnName="Numero" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Client" ColumnName="Client" />
                <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="Reglement" ColumnName="Reglement" />
                <ScalarProperty Name="MP" ColumnName="MP" />
                <ScalarProperty Name="Echeance" ColumnName="Echeance" />
                <ScalarProperty Name="Poste" ColumnName="Poste" />
                <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_RecapCaisse_ReglementCreditMutuelle" FunctionName="SaleReportModel.Store.P_RecapCaisse_ReglementCreditMutuelle">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_ReglementCreditMutuelle_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Numero" ColumnName="Numero" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Client" ColumnName="Client" />
                <ScalarProperty Name="Reglement" ColumnName="Reglement" />
                <ScalarProperty Name="MP" ColumnName="MP" />
                <ScalarProperty Name="Echeance" ColumnName="Echeance" />
                <ScalarProperty Name="Libelle" ColumnName="Libelle" />
                <ScalarProperty Name="Solde" ColumnName="Solde" />
                <ScalarProperty Name="Poste" ColumnName="Poste" />
                <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_RecapCaisse_RemiseRglement" FunctionName="SaleReportModel.Store.P_RecapCaisse_RemiseRglement">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_RemiseRglement_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Numero" ColumnName="Numero" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Client" ColumnName="Client" />
                <ScalarProperty Name="Reglement" ColumnName="Reglement" />
                <ScalarProperty Name="MP" ColumnName="MP" />
                <ScalarProperty Name="Echeance" ColumnName="Echeance" />
                <ScalarProperty Name="Libelle" ColumnName="Libelle" />
                <ScalarProperty Name="Poste" ColumnName="Poste" />
                <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_RecapCaisse_RemiseVente" FunctionName="SaleReportModel.Store.P_RecapCaisse_RemiseVente">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_RemiseVente_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Numero" ColumnName="Numero" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Client" ColumnName="Client" />
                <ScalarProperty Name="Remise" ColumnName="Remise" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="MP" ColumnName="MP" />
                <ScalarProperty Name="Echeance" ColumnName="Echeance" />
                <ScalarProperty Name="Poste" ColumnName="Poste" />
                <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_RecapCaisse_RetourVente" FunctionName="SaleReportModel.Store.P_RecapCaisse_RetourVente">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_RetourVente_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Numero" ColumnName="Numero" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Client" ColumnName="Client" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="Reglement" ColumnName="Reglement" />
                <ScalarProperty Name="MP" ColumnName="MP" />
                <ScalarProperty Name="Echeance" ColumnName="Echeance" />
                <ScalarProperty Name="Poste" ColumnName="Poste" />
                <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_RecapCaisse_CNAM" FunctionName="SaleReportModel.Store.P_RecapCaisse_CNAM">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_CNAM_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Numero" ColumnName="Numero" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Client" ColumnName="Client" />
                <ScalarProperty Name="MontantCNAM" ColumnName="MontantCNAM" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="Reglement" ColumnName="Reglement" />
                <ScalarProperty Name="MP" ColumnName="MP" />
                <ScalarProperty Name="Echeance" ColumnName="Echeance" />
                <ScalarProperty Name="Poste" ColumnName="Poste" />
                <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_RecapCaisse_VenteAuComptant" FunctionName="SaleReportModel.Store.P_RecapCaisse_VenteAuComptant">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_VenteAuComptant_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Numero" ColumnName="Numero" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Client" ColumnName="Client" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="Reglement" ColumnName="Reglement" />
                <ScalarProperty Name="MP" ColumnName="MP" />
                <ScalarProperty Name="Echeance" ColumnName="Echeance" />
                <ScalarProperty Name="Type" ColumnName="Type" />
                <ScalarProperty Name="Poste" ColumnName="Poste" />
                <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatRecapCaisse" FunctionName="SaleReportModel.Store.P_Report_EtatRecapCaisse">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatRecapCaisse_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
                <ScalarProperty Name="TypeOperation" ColumnName="TypeOperation" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="Quantite" ColumnName="Quantite" />
                <ScalarProperty Name="Tva" ColumnName="Tva" />
                <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
                <ScalarProperty Name="Remise" ColumnName="Remise" />
                <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
                <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
                <ScalarProperty Name="Credit" ColumnName="Credit" />
                <ScalarProperty Name="Debit" ColumnName="Debit" />
                <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
                <ScalarProperty Name="TotalVenteHT" ColumnName="TotalVenteHT" />
                <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatHitParade" FunctionName="SaleReportModel.Store.P_Report_EtatHitParade">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatHitParade_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="LibelleForme" ColumnName="LibelleForme" />
                <ScalarProperty Name="LibelleCategorie" ColumnName="LibelleCategorie" />
                <ScalarProperty Name="Quantite" ColumnName="Quantite" />
                <ScalarProperty Name="Stock" ColumnName="Stock" />
                <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
                <ScalarProperty Name="Rayon" ColumnName="Rayon" />
                <ScalarProperty Name="nom" ColumnName="nom" />
                <ScalarProperty Name="DatePeremption" ColumnName="DatePeremption" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Etat_ListeDesReleveCNAM" FunctionName="SaleReportModel.Store.P_Etat_ListeDesReleveCNAM">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Etat_ListeDesReleveCNAM_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="NumeroReleve" ColumnName="NumeroReleve" />
                <ScalarProperty Name="Type" ColumnName="Type" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="DateDebut" ColumnName="DateDebut" />
                <ScalarProperty Name="DateFin" ColumnName="DateFin" />
                <ScalarProperty Name="MontantARembourser" ColumnName="MontantARembourser" />
                <ScalarProperty Name="Reste" ColumnName="Reste" />
                <ScalarProperty Name="MontantTotal" ColumnName="MontantTotal" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <EntitySetMapping Name="V_Report_EtatDetailDesVentes">
            <EntityTypeMapping TypeName="SaleReportModel.V_Report_EtatDetailDesVentes">
              <MappingFragment StoreEntitySet="V_Report_EtatDetailDesVentes">
                <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
                <ScalarProperty Name="CodeCategorie" ColumnName="CodeCategorie" />
                <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
                <ScalarProperty Name="TotalVenteHT" ColumnName="TotalVenteHT" />
                <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
                <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
                <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
                <ScalarProperty Name="TotalCNAM" ColumnName="TotalCNAM" />
                <ScalarProperty Name="TotalMutuelle" ColumnName="TotalMutuelle" />
                <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
                <ScalarProperty Name="Remise" ColumnName="Remise" />
                <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
                <ScalarProperty Name="Tva" ColumnName="Tva" />
                <ScalarProperty Name="Quantite" ColumnName="Quantite" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="LibelleCategorie" ColumnName="LibelleCategorie" />
                <ScalarProperty Name="LibelleForme" ColumnName="LibelleForme" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="V_Report_EtatDesVentes">
            <EntityTypeMapping TypeName="SaleReportModel.V_Report_EtatDesVentes">
              <MappingFragment StoreEntitySet="V_Report_EtatDesVentes">
                <ScalarProperty Name="Marge" ColumnName="Marge" />
                <ScalarProperty Name="Debit" ColumnName="Debit" />
                <ScalarProperty Name="Credit" ColumnName="Credit" />
                <ScalarProperty Name="Vider" ColumnName="Vider" />
                <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
                <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
                <ScalarProperty Name="Mutuelle" ColumnName="Mutuelle" />
                <ScalarProperty Name="CodeMutuelle" ColumnName="CodeMutuelle" />
                <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
                <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
                <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
                <ScalarProperty Name="TypeOperation" ColumnName="TypeOperation" />
                <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
                <ScalarProperty Name="NomUtilisateur" ColumnName="NomUtilisateur" />
                <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
                <ScalarProperty Name="MP" ColumnName="MP" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="Type" ColumnName="Type" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="V_Report_EtatHitParade">
            <EntityTypeMapping TypeName="SaleReportModel.V_Report_EtatHitParade">
              <MappingFragment StoreEntitySet="V_Report_EtatHitParade">
                <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
                <ScalarProperty Name="CodeCategorie" ColumnName="CodeCategorie" />
                <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
                <ScalarProperty Name="CodeLabo" ColumnName="CodeLabo" />
                <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="DatePeremption" ColumnName="DatePeremption" />
                <ScalarProperty Name="nom" ColumnName="nom" />
                <ScalarProperty Name="Rayon" ColumnName="Rayon" />
                <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
                <ScalarProperty Name="Stock" ColumnName="Stock" />
                <ScalarProperty Name="Quantite" ColumnName="Quantite" />
                <ScalarProperty Name="LibelleCategorie" ColumnName="LibelleCategorie" />
                <ScalarProperty Name="LibelleForme" ColumnName="LibelleForme" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatJournalDesVentes" FunctionName="SaleReportModel.Store.P_Report_EtatJournalDesVentes">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatJournalDesVentes_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Exonore" ColumnName="Exonore" />
                <ScalarProperty Name="BaseTVA6" ColumnName="BaseTVA6" />
                <ScalarProperty Name="TVA6" ColumnName="TVA6" />
                <ScalarProperty Name="BaseTVA12" ColumnName="BaseTVA12" />
                <ScalarProperty Name="TVA12" ColumnName="TVA12" />
                <ScalarProperty Name="BaseTVA18" ColumnName="BaseTVA18" />
                <ScalarProperty Name="TVA18" ColumnName="TVA18" />
                <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
                <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="HR" ColumnName="HR" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <EntitySetMapping Name="V_Report_EtatJournalDesVentes">
            <EntityTypeMapping TypeName="SaleReportModel.V_Report_EtatJournalDesVentes">
              <MappingFragment StoreEntitySet="V_Report_EtatJournalDesVentes">
                <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="HR" ColumnName="HR" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
                <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
                <ScalarProperty Name="TVA18" ColumnName="TVA18" />
                <ScalarProperty Name="BaseTVA18" ColumnName="BaseTVA18" />
                <ScalarProperty Name="TVA12" ColumnName="TVA12" />
                <ScalarProperty Name="BaseTVA12" ColumnName="BaseTVA12" />
                <ScalarProperty Name="TVA6" ColumnName="TVA6" />
                <ScalarProperty Name="BaseTVA6" ColumnName="BaseTVA6" />
                <ScalarProperty Name="Exonore" ColumnName="Exonore" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="V_Report_EtatJournalDesVentesDetaillee">
            <EntityTypeMapping TypeName="SaleReportModel.V_Report_EtatJournalDesVentesDetaillee">
              <MappingFragment StoreEntitySet="V_Report_EtatJournalDesVentesDetaillee">
                <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="HR" ColumnName="HR" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
                <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
                <ScalarProperty Name="TVA18" ColumnName="TVA18" />
                <ScalarProperty Name="BaseTVA18" ColumnName="BaseTVA18" />
                <ScalarProperty Name="TVA12" ColumnName="TVA12" />
                <ScalarProperty Name="BaseTVA12" ColumnName="BaseTVA12" />
                <ScalarProperty Name="TVA6" ColumnName="TVA6" />
                <ScalarProperty Name="BaseTVA6" ColumnName="BaseTVA6" />
                <ScalarProperty Name="Exonore" ColumnName="Exonore" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatStatistiqueFournisseurs" FunctionName="SaleReportModel.Store.P_Report_EtatStatistiqueFournisseurs">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatStatistiqueFournisseurs_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="NomFournisseur" ColumnName="NomFournisseur" />
                <ScalarProperty Name="HT" ColumnName="HT" />
                <ScalarProperty Name="TVA" ColumnName="TVA" />
                <ScalarProperty Name="TTC" ColumnName="TTC" />
                <ScalarProperty Name="Remise" ColumnName="Remise" />
                <ScalarProperty Name="VenteTTC" ColumnName="VenteTTC" />
                <ScalarProperty Name="ResteAPayer" ColumnName="ResteAPayer" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatJournalDesAchats" FunctionName="SaleReportModel.Store.P_Report_EtatJournalDesAchats">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatJournalDesAchats_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="NumeroAchat" ColumnName="NumeroAchat" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="DateBlFacture" ColumnName="DateBlFacture" />
                <ScalarProperty Name="NomFournisseur" ColumnName="NomFournisseur" />
                <ScalarProperty Name="NumeroBL_Facture" ColumnName="NumeroBL_Facture" />
                <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
                <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
                <ScalarProperty Name="TVA" ColumnName="TVA" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="CodeFournisseur" ColumnName="CodeFournisseur" />
                <ScalarProperty Name="ValeurVenteTTC" ColumnName="ValeurVenteTTC" />
                <ScalarProperty Name="TotalTVAVente" ColumnName="TotalTVAVente" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatDesVentes" FunctionName="SaleReportModel.Store.P_Report_EtatDesVentes">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatDesVentes_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Type" ColumnName="Type" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="MP" ColumnName="MP" />
                <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
                <ScalarProperty Name="NomUtilisateur" ColumnName="NomUtilisateur" />
                <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
                <ScalarProperty Name="TypeOperation" ColumnName="TypeOperation" />
                <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
                <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
                <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
                <ScalarProperty Name="CodeMutuelle" ColumnName="CodeMutuelle" />
                <ScalarProperty Name="Mutuelle" ColumnName="Mutuelle" />
                <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
                <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
                <ScalarProperty Name="Vider" ColumnName="Vider" />
                <ScalarProperty Name="Credit" ColumnName="Credit" />
                <ScalarProperty Name="Debit" ColumnName="Debit" />
                <ScalarProperty Name="Marge" ColumnName="Marge" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatDetailDesVentes" FunctionName="SaleReportModel.Store.P_Report_EtatDetailDesVentes">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatDetailDesVentes_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="LibelleForme" ColumnName="LibelleForme" />
                <ScalarProperty Name="LibelleCategorie" ColumnName="LibelleCategorie" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="Quantite" ColumnName="Quantite" />
                <ScalarProperty Name="Tva" ColumnName="Tva" />
                <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
                <ScalarProperty Name="Remise" ColumnName="Remise" />
                <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
                <ScalarProperty Name="TotalMutuelle" ColumnName="TotalMutuelle" />
                <ScalarProperty Name="TotalCNAM" ColumnName="TotalCNAM" />
                <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
                <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
                <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
                <ScalarProperty Name="TotalVenteHT" ColumnName="TotalVenteHT" />
                <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
                <ScalarProperty Name="CodeCategorie" ColumnName="CodeCategorie" />
                <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
                <ScalarProperty Name="PrixVenteHT" ColumnName="PrixVenteHT" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatJournalReleveMutuelle" FunctionName="SaleReportModel.Store.P_Report_EtatJournalReleveMutuelle">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatJournalReleveMutuelle_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="NumeroReleve" ColumnName="NumeroReleve" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="DateDebut" ColumnName="DateDebut" />
                <ScalarProperty Name="DateFin" ColumnName="DateFin" />
                <ScalarProperty Name="MontantTotal" ColumnName="MontantTotal" />
                <ScalarProperty Name="MontantRegle" ColumnName="MontantRegle" />
                <ScalarProperty Name="Reste" ColumnName="Reste" />
                <ScalarProperty Name="CodeMutuelle" ColumnName="CodeMutuelle" />
                <ScalarProperty Name="NomMutuelle" ColumnName="NomMutuelle" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatFacureJournaliere" FunctionName="SaleReportModel.Store.P_Report_EtatFacureJournaliere">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatFacureJournaliere_Result">
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
                <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="Caisse" ColumnName="Caisse" />
                <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
                <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatStockParCategorieIntervalleMarge" FunctionName="SaleReportModel.Store.P_Report_EtatStockParCategorieIntervalleMarge">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatStockParCategorieIntervalleMarge_Result">
                <ScalarProperty Name="Marge" ColumnName="Marge" />
                <ScalarProperty Name="ValeurAchatHT" ColumnName="ValeurAchatHT" />
                <ScalarProperty Name="ValeurAchatHTP" ColumnName="ValeurAchatHTP" />
                <ScalarProperty Name="ValeurVenteHT" ColumnName="ValeurVenteHT" />
                <ScalarProperty Name="ValeurVenteHTP" ColumnName="ValeurVenteHTP" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatListeDesMedecins" FunctionName="SaleReportModel.Store.P_Report_EtatListeDesMedecins">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatListeDesMedecins_Result">
                <ScalarProperty Name="CodeMedecin" ColumnName="CodeMedecin" />
                <ScalarProperty Name="NomMedecin" ColumnName="NomMedecin" />
                <ScalarProperty Name="LibelleSpecialite" ColumnName="LibelleSpecialite" />
                <ScalarProperty Name="CodeSpecialite" ColumnName="CodeSpecialite" />
                <ScalarProperty Name="Adresse" ColumnName="Adresse" />
                <ScalarProperty Name="Tel" ColumnName="Tel" />
                <ScalarProperty Name="Fax" ColumnName="Fax" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="IdentifiantCNAM" ColumnName="IdentifiantCNAM" />
                <ScalarProperty Name="NomVille" ColumnName="NomVille" />
                <ScalarProperty Name="CodeVille" ColumnName="CodeVille" />
                <ScalarProperty Name="bloquer" ColumnName="bloquer" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatDetailsCaisse" FunctionName="SaleReportModel.Store.P_Report_EtatDetailsCaisse">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatDetailsCaisse_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Operation" ColumnName="Operation" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="Reglement" ColumnName="Reglement" />
                <ScalarProperty Name="NatureReglement" ColumnName="NatureReglement" />
                <ScalarProperty Name="DateEcheance" ColumnName="DateEcheance" />
                <ScalarProperty Name="Vendeur" ColumnName="Vendeur" />
                <ScalarProperty Name="Poste" ColumnName="Poste" />
                <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
                <ScalarProperty Name="Type" ColumnName="Type" />
                <ScalarProperty Name="TypeOperation" ColumnName="TypeOperation" />
                <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
                <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
                <ScalarProperty Name="Remise" ColumnName="Remise" />
                <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
                <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
                <ScalarProperty Name="Credit" ColumnName="Credit" />
                <ScalarProperty Name="Debit" ColumnName="Debit" />
                <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
                <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_Report_EtatOrdonnancier" FunctionName="SaleReportModel.Store.P_Report_EtatOrdonnancier">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_Report_EtatOrdonnancier_Result">
                <ScalarProperty Name="DateVente" ColumnName="DateVente" />
                <ScalarProperty Name="DateOrdonnancier" ColumnName="DateOrdonnancier" />
                <ScalarProperty Name="NomMedecin" ColumnName="NomMedecin" />
                <ScalarProperty Name="Numero" ColumnName="Numero" />
                <ScalarProperty Name="NumeroOrdonnacier" ColumnName="NumeroOrdonnacier" />
                <ScalarProperty Name="LibelleForme" ColumnName="LibelleForme" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="Quantite" ColumnName="Quantite" />
                <ScalarProperty Name="Malade" ColumnName="Malade" />
                <ScalarProperty Name="CIN" ColumnName="CIN" />
                <ScalarProperty Name="Adresse" ColumnName="Adresse" />
                <ScalarProperty Name="TypeOrdonnance" ColumnName="TypeOrdonnance" />
                <ScalarProperty Name="NumeroVente" ColumnName="NumeroVente" />
                <ScalarProperty Name="Supprimer" ColumnName="Supprimer" />
                <ScalarProperty Name="Row" ColumnName="Row" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="P_RecapCaisse_Credit" FunctionName="SaleReportModel.Store.P_RecapCaisse_Credit">
            <ResultMapping>
              <ComplexTypeMapping TypeName="SaleReportModel.P_RecapCaisse_Credit_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="Numero" ColumnName="Numero" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="Client" ColumnName="Client" />
                <ScalarProperty Name="Solde" ColumnName="Solde" />
                <ScalarProperty Name="MontantCNAM" ColumnName="MontantCNAM" />
                <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
                <ScalarProperty Name="Reglement" ColumnName="Reglement" />
                <ScalarProperty Name="Credit" ColumnName="Credit" />
                <ScalarProperty Name="MP" ColumnName="MP" />
                <ScalarProperty Name="Echeance" ColumnName="Echeance" />
                <ScalarProperty Name="Poste" ColumnName="Poste" />
                <ScalarProperty Name="Utilisateur" ColumnName="Utilisateur" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="False" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="True" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="Aucun" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>