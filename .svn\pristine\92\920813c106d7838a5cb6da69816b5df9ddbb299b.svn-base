﻿Imports Neodynamic.SDK.Printing

Public Class PrintJobDialog

    Dim _printOrientation As PrintOrientation = PrintOrientation.Portrait
    Dim _printerSettings As New PrinterSettings()
    Dim _copies As Integer = 1


    Public ReadOnly Property PrinterSettings As PrinterSettings
        Get
            Return _printerSettings
        End Get
    End Property
    
    Public ReadOnly Property Copies As Integer
        Get
            Return _copies
        End Get
    End Property

    Public ReadOnly Property PrintOrientation As PrintOrientation
        Get
            Return _printOrientation
        End Get
    End Property

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.DialogResult = DialogResult.Cancel
    End Sub

    Private Sub btnOk_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOk.Click

        If Me.cboProgLang.SelectedItem.ToString() = "" Then
            Me.ErrorProvider1.SetError(Me.cboProgLang, "Please select the printer's programming language")
            Return
        Else
            Me.ErrorProvider1.SetError(Me.cboProgLang, "")
        End If


        Me.DialogResult = DialogResult.OK

        Try
            'Update printer comm object...
            If (Me.tabControl1.SelectedIndex = 0) Then
                'USB
                _printerSettings.Communication.CommunicationType = CommunicationType.USB
                _printerSettings.PrinterName = Me.cboPrinters.SelectedItem.ToString()

            ElseIf (Me.tabControl1.SelectedIndex = 1) Then
                'Parallel
                _printerSettings.Communication.CommunicationType = CommunicationType.Parallel
                _printerSettings.Communication.ParallelPortName = Me.txtParallelPort.Text
            ElseIf (Me.tabControl1.SelectedIndex = 2) Then
                'Serial
                _printerSettings.Communication.CommunicationType = CommunicationType.Serial
                _printerSettings.Communication.SerialPortName = Me.cboSerialPorts.SelectedItem.ToString()
                _printerSettings.Communication.SerialPortBaudRate = Integer.Parse(Me.txtBaudRate.Text)
                _printerSettings.Communication.SerialPortDataBits = Integer.Parse(Me.txtDataBits.Text)
                _printerSettings.Communication.SerialPortFlowControl = DirectCast(System.Enum.Parse(GetType(System.IO.Ports.Handshake), Me.cboFlowControl.SelectedItem.ToString()), System.IO.Ports.Handshake)
                _printerSettings.Communication.SerialPortParity = DirectCast(System.Enum.Parse(GetType(System.IO.Ports.Parity), Me.cboParity.SelectedItem.ToString()), System.IO.Ports.Parity)
                _printerSettings.Communication.SerialPortStopBits = DirectCast(System.Enum.Parse(GetType(System.IO.Ports.StopBits), Me.cboStopBits.SelectedItem.ToString()), System.IO.Ports.StopBits)
            ElseIf (Me.tabControl1.SelectedIndex = 3) Then
                'Network
                _printerSettings.Communication.CommunicationType = CommunicationType.Network

                Dim ipAddress As System.Net.IPAddress = System.Net.IPAddress.None
                Try
                    ipAddress = System.Net.IPAddress.Parse(Me.txtIPAddress.Text)
                Catch ex As Exception

                End Try

                If ipAddress Is System.Net.IPAddress.None Then
                    _printerSettings.PrinterName = Me.txtIPAddress.Text 'try Host Name
                Else
                    _printerSettings.Communication.NetworkIPAddress = ipAddress ' use IP
                End If

                _printerSettings.Communication.NetworkPort = Integer.Parse(Me.txtIPPort.Text)
            End If

            _printerSettings.Dpi = CDbl(Me.nudDpi.Value)
            _printerSettings.ProgrammingLanguage = DirectCast(System.Enum.Parse(GetType(ProgrammingLanguage), Me.cboProgLang.SelectedItem.ToString()), ProgrammingLanguage)


            _copies = CInt(Me.nudCopies.Value)
            _printOrientation = DirectCast(System.Enum.Parse(GetType(PrintOrientation), Me.cboPrintOrientation.SelectedItem.ToString()), PrintOrientation)


        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Me.DialogResult = DialogResult.Abort
        End Try


    End Sub

    Private Sub PrintJobDialog_FormClosing(ByVal sender As System.Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles MyBase.FormClosing
        If (Me.DialogResult = DialogResult.Abort) Then
            e.Cancel = True
        End If
    End Sub

    Private Sub PrintJobDialog_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Init()
    End Sub

    Private Sub Init()

        Me.cboProgLang.SelectedIndex = 0
        Me.cboPrintOrientation.SelectedIndex = 0

        'Load installed printers...
        Dim installedPrinters(System.Drawing.Printing.PrinterSettings.InstalledPrinters.Count) As String
        System.Drawing.Printing.PrinterSettings.InstalledPrinters.CopyTo(installedPrinters, 0)
        Me.cboPrinters.DataSource = installedPrinters

        'Load Serial Comm settings...
        Me.cboSerialPorts.DataSource = System.IO.Ports.SerialPort.GetPortNames()
        Me.cboParity.DataSource = System.Enum.GetNames(GetType(System.IO.Ports.Parity))
        Me.cboStopBits.DataSource = System.Enum.GetNames(GetType(System.IO.Ports.StopBits))
        Me.cboFlowControl.DataSource = System.Enum.GetNames(GetType(System.IO.Ports.Handshake))

    End Sub

End Class