﻿Imports System.Data.SqlClient
Public Class fCritereDeRechercheInventaire
    Dim cmdChargement As New SqlCommand
    Dim cbChargement As New SqlCommandBuilder
    Dim dsChargement As New DataSet
    Dim daChargement As New SqlDataAdapter
    Dim StrSQL As String = ""

    Public Shared RechercheSelective As Boolean = False
    Public Shared Section As String = ""
    Public Shared DebutIntervalle As String = ""
    Public Shared FinIntervalle As String = ""
    Public Shared Forme As Integer = 0
    Public Shared Categorie As Integer = 0
    Public Shared Labo As Integer = 0
    Public Shared Rayon As String = ""
    Public Shared RayonSelectionne As String = ""
    Public Shared Trie As String = ""
    Public Shared NonMouvemente As Boolean = False
    Public Shared TypeInventaire As String = ""
    Public Shared Fournisseur As String = ""
    Public Shared ArticleCommance As String = ""

    Public Shared Stock As String = ""
    Public Shared Suspendu As String = ""

    Public Import As Boolean = False


    Private Sub fCritereDeRechercheInventaire_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'chargement des Formes
        Try
            dsChargement.Tables("FORME_ARTICLE").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT CodeForme,LibelleForme FROM FORME_ARTICLE where SupprimeForme = 0 ORDER BY CodeForme ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "FORME_ARTICLE")
        cmbForme.DataSource = dsChargement.Tables("FORME_ARTICLE")
        cmbForme.ValueMember = "CodeForme"
        cmbForme.DisplayMember = "LibelleForme"
        cmbForme.ColumnHeaders = False
        cmbForme.Splits(0).DisplayColumns("CodeForme").Visible = False
        cmbForme.Splits(0).DisplayColumns("LibelleForme").Width = 10
        cmbForme.ExtendRightColumn = True

        'chargement des Categories
        Try
            dsChargement.Tables("CATEGORIE").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT CodeCategorie,LibelleCategorie FROM CATEGORIE WHERE SupprimeCategorie=0 ORDER BY CodeCategorie ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "CATEGORIE")
        cmbCategorie.DataSource = dsChargement.Tables("CATEGORIE")
        cmbCategorie.ValueMember = "CodeCategorie"
        cmbCategorie.DisplayMember = "LibelleCategorie"
        cmbCategorie.ColumnHeaders = False
        cmbCategorie.Splits(0).DisplayColumns("CodeCategorie").Visible = False
        cmbCategorie.Splits(0).DisplayColumns("LibelleCategorie").Width = 10
        cmbCategorie.ExtendRightColumn = True

        'chargement des Labo
        Try
            dsChargement.Tables("dbo.LABORATOIRE").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT CodeLabo,NomLabo FROM dbo.LABORATOIRE WHERE SupprimeLabo=0 ORDER BY CodeLabo ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "dbo.LABORATOIRE")
        cmbLabo.DataSource = dsChargement.Tables("dbo.LABORATOIRE")
        cmbLabo.ValueMember = "CodeLabo"
        cmbLabo.DisplayMember = "NomLabo"
        cmbLabo.ColumnHeaders = False
        cmbLabo.Splits(0).DisplayColumns("CodeLabo").Visible = False
        cmbLabo.Splits(0).DisplayColumns("NomLabo").Width = 10
        cmbLabo.ExtendRightColumn = True

        'chargement des Labo
        Try
            dsChargement.Tables("FOURNISSEUR").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT CodeFournisseur, NomFournisseur FROM FOURNISSEUR WHERE Supprimer = 0 ORDER BY CodeFournisseur ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "FOURNISSEUR")
        cmbFournisseur.DataSource = dsChargement.Tables("FOURNISSEUR")
        cmbFournisseur.ValueMember = "CodeFournisseur"
        cmbFournisseur.DisplayMember = "NomFournisseur"
        cmbFournisseur.ColumnHeaders = False
        cmbFournisseur.Splits(0).DisplayColumns("CodeFournisseur").Visible = False
        cmbFournisseur.Splits(0).DisplayColumns("NomFournisseur").Width = 10
        cmbFournisseur.ExtendRightColumn = True

        'chargement des Rayons
        Try
            dsChargement.Tables("RAYON").Clear()
        Catch ex As Exception
        End Try

        StrSQL = "SELECT DISTINCT Rayon FROM ARTICLE ORDER BY Rayon ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "RAYON")
        cmbRayon.DataSource = dsChargement.Tables("RAYON")
        cmbRayon.ValueMember = "Rayon"
        cmbRayon.DisplayMember = "Rayon"
        cmbRayon.ColumnHeaders = False
        cmbRayon.Splits(0).DisplayColumns("Rayon").Width = 10
        cmbRayon.ExtendRightColumn = True


        rdbToutesSection.Checked = True
        rdbTousRayon.Checked = True
        rdbDesignation.Checked = True

        rdbTousLesArticles.Checked = True
        rdbTousArticlesSuspendus.Checked = True

        ArticleCommance = ""
        tDebutIntervalle.Value = ""
        tFinIntervalle.Value = ""
        cmbForme.Text = ""
        cmbCategorie.Text = ""
        cmbLabo.Text = ""

        cmbRayon.Text = ""

        chbNonMouvemente.Checked = True



        Me.KeyPreview = True
        AddHandler Me.KeyDown, AddressOf KeyDownHandler

    End Sub
    Private Sub KeyDownHandler(ByVal o As Object, ByVal e As KeyEventArgs)

        If e.KeyCode = "121" Then
            e.SuppressKeyPress = True
        Else
            e.SuppressKeyPress = False
        End If

        If e.KeyCode = Keys.F3 Then
            bOk_Click(o, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(o, e)
            Exit Sub
        End If
    End Sub

    Private Sub rdbToutesSection_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbToutesSection.CheckedChanged
        If rdbToutesSection.Checked = True Then
            tDebutIntervalle.Enabled = False
            tFinIntervalle.Enabled = False
        End If
    End Sub

    Private Sub rdbIntervalleSection_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbIntervalleSection.CheckedChanged
        If rdbIntervalleSection.Checked = True Then
            tDebutIntervalle.Enabled = True
            tFinIntervalle.Enabled = True
        End If
    End Sub

    Private Sub rdbTousRayon_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbTousRayon.CheckedChanged
        If rdbTousRayon.Checked = True Then
            cmbRayon.Enabled = False
        End If
    End Sub

    Private Sub rdbRayonRayon_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbRayonRayon.CheckedChanged
        If rdbRayonRayon.Checked = True Then
            cmbRayon.Enabled = True
        End If
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        RechercheSelective = False
        Me.Hide()
    End Sub

    Private Sub bOk_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOk.Click

        RechercheSelective = True

        If rdbToutesSection.Checked = True Then
            Section = "TOUTES"
        ElseIf rdbIntervalleSection.Checked = True Then
            Section = "INTERVALLE"
        End If

        DebutIntervalle = tDebutIntervalle.Value
        FinIntervalle = tFinIntervalle.Value
        Forme = cmbForme.SelectedValue
        Categorie = cmbCategorie.SelectedValue
        Labo = cmbLabo.SelectedValue
        Fournisseur = cmbFournisseur.SelectedValue

        If rdbTousRayon.Checked = True Then
            Rayon = "TOUS"
        ElseIf rdbRayonRayon.Checked = True Then
            Rayon = "RAYON"
        End If

        RayonSelectionne = cmbRayon.Text

        If rdbDesignation.Checked = True Then
            Trie = "DESIGNATION"
        ElseIf rdbForme.Checked = True Then
            Trie = "LibelleForme"
        ElseIf rdbCategorie.Checked = True Then
            Trie = "LibelleCategorie"
        ElseIf rdbLabo.Checked = True Then
            Trie = "CodeLabo"
        ElseIf rdbSection.Checked = True Then
            Trie = "SECTION"
        ElseIf rdbRayon.Checked = True Then
            Trie = "Rayon"
        End If


        If rdbTousLesArticles.Checked = True Then
            Stock = "TOUS"
        ElseIf rdbStockEgal0.Checked = True Then
            Stock = "Egal0"
        ElseIf rdbStockSup0.Checked = True Then
            Stock = "Sup0"
        ElseIf rdbStockInf0.Checked = True Then
            Stock = "Inf0"
        End If


        If rdbTousArticlesSuspendus.Checked = True Then
            Suspendu = "TOUS"
        ElseIf rdbSuspendus.Checked = True Then
            Suspendu = "Suspendu"
        ElseIf rdbNonSuspendus.Checked = True Then
            Suspendu = "NonSuspendu"
        End If

        ArticleCommance = tArticleCommance.Text


        TypeInventaire = "Total"


        NonMouvemente = chbNonMouvemente.Checked

        Me.Hide()
    End Sub

    Private Sub chbNonMouvemente_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbNonMouvemente.CheckedChanged

    End Sub

    Private Sub tFinIntervalle_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles tFinIntervalle.KeyDown
        If (e.KeyValue = Keys.Enter) Then
            cmbForme.Focus()
        End If
    End Sub

    Private Sub tFinIntervalle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tFinIntervalle.TextChanged

    End Sub

    Private Sub tDebutIntervalle_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles tDebutIntervalle.KeyDown
        If (e.KeyValue = Keys.Enter) Then
            tFinIntervalle.Focus()
        End If
    End Sub

    Private Sub tDebutIntervalle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tDebutIntervalle.TextChanged

    End Sub

    Private Sub cmbForme_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbForme.KeyDown
        If (e.KeyValue = Keys.Enter) Then
            cmbForme.Text = cmbForme.WillChangeToText
            cmbLabo.Focus()
        Else
            cmbForme.OpenCombo()
        End If
        If e.KeyCode = Keys.F3 Then
            cmbForme.CloseCombo()
        End If
    End Sub

    Private Sub cmbLabo_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbLabo.KeyDown
        If (e.KeyValue = Keys.Enter) Then
            cmbLabo.Text = cmbLabo.WillChangeToText
            cmbFournisseur.Focus()
        Else
            cmbLabo.OpenCombo()
        End If
        If e.KeyCode = Keys.F3 Then
            cmbLabo.CloseCombo()
        End If
    End Sub

    Private Sub cmbCategorie_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles cmbCategorie.KeyDown
        If (e.KeyValue = Keys.Enter) Then
            cmbCategorie.Text = cmbCategorie.WillChangeToText
            bOk.Focus()
        Else
            cmbCategorie.OpenCombo()
        End If
        If e.KeyCode = Keys.F3 Then
            cmbCategorie.CloseCombo()
        End If
    End Sub


    Private Sub bImport_Click(sender As System.Object, e As System.EventArgs) Handles bImport.Click
        Import = True
        Me.Hide()

    End Sub

    Private Sub cmbFournisseur_KeyDown(sender As Object, e As KeyEventArgs) Handles cmbFournisseur.KeyDown
        If (e.KeyValue = Keys.Enter) Then
            cmbFournisseur.Text = cmbFournisseur.WillChangeToText
            bOk.Focus()
        Else
            cmbFournisseur.OpenCombo()
        End If
        If e.KeyCode = Keys.F3 Then
            cmbFournisseur.CloseCombo()
        End If
    End Sub
End Class