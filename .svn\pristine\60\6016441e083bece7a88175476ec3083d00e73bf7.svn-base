﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fListeDesVentesEnInstance

    Dim cmdVenteInstance As New SqlCommand
    Dim cbVenteInstance As New SqlCommandBuilder
    Dim dsVenteInstance As New DataSet
    Dim daVenteInstance As New SqlDataAdapter
    Dim StrSQL As String = ""

    Public Shared ComfirmerMettreEnINstance As Boolean = False
    Public Shared NumeroVente As String = ""
    Public Sub init()
        Dim I As Integer
        Try
            dsVenteInstance.Tables("VENTE_INSTANCE").Clear()
        Catch ex As Exception

        End Try
        'intialisation de la gride      
        StrSQL = "SELECT NomVenteInstance," + _
                 "CLIENT.Nom," + _
                 "Date," + _
                 "TotalTTC," + _
                 "TotalHT," + _
               " TotalRemise," + _
                 "UTILISATEUR.Nom AS NomVendeur " + _
                "FROM VENTE_INSTANCE " + _
                "LEFT OUTER JOIN CLIENT ON VENTE_INSTANCE.CodeClient=CLIENT.CodeClient " + _
                "LEFT OUTER JOIN UTILISATEUR ON VENTE_INSTANCE.CodeOperateur=UTILISATEUR.CodeUtilisateur "

        cmdVenteInstance.Connection = ConnectionServeur
        cmdVenteInstance.CommandText = StrSQL
        daVenteInstance = New SqlDataAdapter(cmdVenteInstance)
        daVenteInstance.Fill(dsVenteInstance, "VENTE_INSTANCE")
        With gVenteInstance
            .Columns.Clear()
            Try
                .DataSource = dsVenteInstance
            Catch ex As Exception
            End Try
            .DataMember = "VENTE_INSTANCE"
            .Rebind(False)
            .Columns("NomVenteInstance").Caption = "Vente"
            .Columns("Nom").Caption = "Client"
            .Columns("Date").Caption = "Date"
            .Columns("TotalTTC").Caption = "Total TTC"
            .Columns("TotalHT").Caption = "Total HT"
            .Columns("NomVendeur").Caption = "Vendeur"
            .Columns("TotalRemise").Caption = "Total Remise"

            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns(0).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("NomVenteInstance").Width = 180
            .Splits(0).DisplayColumns("Nom").Width = 350
            .Splits(0).DisplayColumns("Date").Width = 110
            .Splits(0).DisplayColumns("TotalTTC").Width = 120
            .Splits(0).DisplayColumns("TotalHT").Width = 120
            .Splits(0).DisplayColumns("TotalRemise").Width = 120
            .Splits(0).DisplayColumns("NomVendeur").Width = 120

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gVenteInstance)
        End With
        NumeroVente = RecupererValeurExecuteScalaire("NumeroVenteInstance", "VENTE_INSTANCE", "NomVenteInstance", gVenteInstance(0, "NomVenteInstance"))
        AfficherDetailsVenteInstance(NumeroVente)

    End Sub
    Private Sub Panel_Paint(ByVal sender As System.Object, ByVal e As System.Windows.Forms.PaintEventArgs) Handles Panel.Paint

    End Sub

    Private Sub fListeDesVentesEnInstance_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        init()
    End Sub

    Private Sub gVenteInstance_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gVenteInstance.Click

    End Sub
    Public Function RecupererValeurExecuteScalaire(ByVal ValeurRecherche, ByVal Table, ByVal ClePrimaire, ByVal ValeurCle)
        Dim StrSQL As String = ""
        Dim CmdCalcul As New SqlCommand
        Dim Valeur As String = ""

        Try
            Quote(ValeurCle)
        Catch ex As Exception
            Return Nothing
            Exit Function
        End Try

        StrSQL = "SELECT " + ValeurRecherche + " FROM " + Table + " WHERE " + ClePrimaire + " =" + Quote(ValeurCle)
        CmdCalcul.Connection = ConnectionServeur
        CmdCalcul.CommandText = StrSQL
        Try
            Valeur = CmdCalcul.ExecuteScalar().ToString
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try
        Return (Valeur)
    End Function

    Private Sub gVenteInstance_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gVenteInstance.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If

        If e.KeyData = Keys.Down Or e.KeyData = Keys.Up Then
            NumeroVente = RecupererValeurExecuteScalaire("NumeroVenteInstance", "VENTE_INSTANCE", "NomVenteInstance", gVenteInstance(gVenteInstance.Row, "NomVenteInstance"))
            AfficherDetailsVenteInstance(NumeroVente)
        End If
    End Sub

    Private Sub gVenteInstance_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gVenteInstance.MouseClick
        NumeroVente = RecupererValeurExecuteScalaire("NumeroVenteInstance", "VENTE_INSTANCE", "NomVenteInstance", gVenteInstance(gVenteInstance.Row, "NomVenteInstance"))
        AfficherDetailsVenteInstance(NumeroVente)
    End Sub
    Public Sub AfficherDetailsVenteInstance(ByVal NumeroVenteInstance)
        Dim I As Integer
        Try
            dsVenteInstance.Tables("VENTE_INSTANCE_DETAILS").Clear()
        Catch ex As Exception

        End Try
        'intialisation de la gride      
        StrSQL = "SELECT CodeArticle,CodeABarre,Designation,Qte,Remise FROM VENTE_INSTANCE_DETAILS " + _
        "WHERE NumeroVenteInstance ='" + NumeroVenteInstance + "' ORDER BY Ordre"

        cmdVenteInstance.Connection = ConnectionServeur
        cmdVenteInstance.CommandText = StrSQL
        daVenteInstance = New SqlDataAdapter(cmdVenteInstance)
        daVenteInstance.Fill(dsVenteInstance, "VENTE_INSTANCE_DETAILS")

        With gDetailsVenteInstance
            .Columns.Clear()
            Try
                .DataSource = dsVenteInstance
            Catch ex As Exception
            End Try
            .DataMember = "VENTE_INSTANCE_DETAILS"
            .Rebind(False)
            .Columns("CodeABarre").Caption = "Code Article"
            .Columns("Designation").Caption = "Désignation"
            .Columns("Qte").Caption = "Qte"
            .Columns("Remise").Caption = "Remise"


            ' Centrer toutes les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            ' Centrer toutes les valeurs
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next
            .Splits(0).DisplayColumns(1).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = AlignHorzEnum.Near
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).Locked = True
            Next

            .Splits(0).DisplayColumns("CodeArticle").Width = 0
            .Splits(0).DisplayColumns("CodeArticle").Visible = False
            .Splits(0).DisplayColumns("CodeArticle").AllowSizing = False

            .Splits(0).DisplayColumns("CodeABarre").Width = 200
            .Splits(0).DisplayColumns("Designation").Width = 550
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
            .Splits(0).DisplayColumns("Qte").Width = 100
            .Splits(0).DisplayColumns("Remise").Width = 120
            .Splits(0).DisplayColumns("Designation").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center

            .Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            .AllowSort = False
            .DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gDetailsVenteInstance)
        End With
    End Sub

    Private Sub bOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOK.Click
        NumeroVente = RecupererValeurExecuteScalaire("NumeroVenteInstance", "VENTE_INSTANCE", "NomVenteInstance", gVenteInstance(gVenteInstance.Row, "NomVenteInstance"))
        ComfirmerMettreEnINstance = True
        Me.Hide()
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        ComfirmerMettreEnINstance = False
        Me.Hide()
    End Sub

    Private Sub gDetailsVenteInstance_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gDetailsVenteInstance.Click

    End Sub

    Private Sub gDetailsVenteInstance_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gDetailsVenteInstance.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub GroupBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GroupBox1.Enter

    End Sub

    Private Sub GroupBox1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles GroupBox1.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub GroupBox5_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GroupBox5.Enter

    End Sub

    Private Sub GroupBox5_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles GroupBox5.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOK_Click(sender, e)
            Exit Sub
        ElseIf e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub
End Class