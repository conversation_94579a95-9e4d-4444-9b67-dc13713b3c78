﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports Microsoft.Reporting.WinForms
Imports System.ComponentModel
Imports System.Linq.Dynamic

Public Class fEtatRegelement
    Dim _SalesReportService As New Bll.Reporting.SalesReport
    Dim _VOrderBy As String = "Id"
    Dim _VAscDesc As String = "Asc"

    Dim cmdModePaiement As New SqlCommand
    Dim daModePaiement As New SqlDataAdapter
    Dim dsModePaiement As New DataSet

    Dim cmdVendeur As New SqlCommand
    Dim daVendeur As New SqlDataAdapter
    Dim dsVendeur As New DataSet

    Dim cmdClient As New SqlCommand
    Dim daClient As New SqlDataAdapter
    Dim dsClient As New DataSet

    Dim cmdType As New SqlCommand
    Dim daType As New SqlDataAdapter
    Dim dsType As New DataSet

    Dim cmdPoste As New SqlCommand
    Dim daPoste As New SqlDataAdapter
    Dim dsPoste As New DataSet

    Dim cmdPourcentage As New SqlCommand
    Dim daPourcentage As New SqlDataAdapter
    Dim dsPourcentage As New DataSet

    Dim TotalEspece As Double = 0

    Dim TotalCredit As Double = 0

    Dim TotalCheque As Double = 0
    Dim TotalCarte As Double = 0
    Dim TotalVirement As Double = 0
    Dim TotalTraite As Double = 0
    Dim TotalMultiple As Double = 0
    Dim TotalRemise As Double = 0
    Dim Total As Double = 0
    Dim TotalVente As Double = 0
    Dim TotalVenteVendeur As Double = 0
    Dim Pourcentage As Double = 0
    Dim CondCrystalReport As String = ""

    Dim Initialisation As Boolean = False

    Public Sub fonctionsF(ByVal argument As String, ByVal sender As System.Object, ByVal e As System.EventArgs)

        If argument = "120" And bImprimer.Enabled = True Then
            bImprimer_Click(sender, e)
        End If
        If argument = "123" And bQuitter.Enabled = True Then
            bQuitter_Click(sender, e)
        End If

    End Sub

    Public Sub Init()

        'charger les vendeurs
        cmdVendeur.CommandText = "SELECT CodeUtilisateur,Nom FROM UTILISATEUR WHERE supprime = 0 ORDER BY Nom ASC"
        cmdVendeur.Connection = ConnectionServeur
        daVendeur = New SqlDataAdapter(cmdVendeur)
        daVendeur.Fill(dsVendeur, "OPERATEUR")
        cmbVendeur.DataSource = dsVendeur.Tables("OPERATEUR")
        cmbVendeur.ValueMember = "CodeUtilisateur"
        cmbVendeur.DisplayMember = "Nom"
        cmbVendeur.ColumnHeaders = False
        cmbVendeur.ExtendRightColumn = True
        cmbVendeur.ColumnHeaders = False
        cmbVendeur.Splits(0).DisplayColumns("CodeUtilisateur").Visible = False
        cmbVendeur.Splits(0).DisplayColumns("Nom").Width = cmbVendeur.Width - 20
        cmbVendeur.ExtendRightColumn = True

        'charger les modes de paiements
        cmdModePaiement.CommandText = "SELECT CodeNatureReglement, LibelleNatureReglement FROM Nature_REGLEMENT WHERE LibelleNatureReglement <> 'CREDIT' ORDER BY LibelleNatureReglement ASC"
        cmdModePaiement.Connection = ConnectionServeur
        daModePaiement = New SqlDataAdapter(cmdModePaiement)
        daModePaiement.Fill(dsModePaiement, "Nature_REGLEMENT")
        cmbModePaiement.DataSource = dsModePaiement.Tables("Nature_REGLEMENT")
        cmbModePaiement.ValueMember = "CodeNatureReglement"
        cmbModePaiement.DisplayMember = "LibelleNatureReglement"
        cmbModePaiement.ColumnHeaders = False
        cmbModePaiement.Splits(0).DisplayColumns("CodeNatureReglement").Visible = False
        cmbModePaiement.Splits(0).DisplayColumns("LibelleNatureReglement").Width = 10
        cmbModePaiement.ExtendRightColumn = True

        'charger les types
        cmdType.CommandText = "SELECT 'Reglement client' AS TypeOperation UNION SELECT 'Reglement CNAM' AS TypeOperation UNION SELECT 'Reglement mutuelle' AS TypeOperation UNION SELECT 'Reglement vente' AS TypeOperation"
        cmdType.Connection = ConnectionServeur
        daType = New SqlDataAdapter(cmdType)
        daType.Fill(dsType, "MOUVEMENT_ETATS")
        cmbType.DataSource = dsType.Tables("MOUVEMENT_ETATS")
        cmbType.ValueMember = "TypeOperation"
        cmbType.ColumnHeaders = False
        cmbType.ExtendRightColumn = True

        'charger les modes de paiements
        cmdPoste.CommandText = "SELECT DISTINCT LibellePoste FROM POSTE ORDER BY LibellePoste ASC"
        cmdPoste.Connection = ConnectionServeur
        daPoste = New SqlDataAdapter(cmdPoste)
        daPoste.Fill(dsPoste, "POSTE")
        cmbPoste.DataSource = dsPoste.Tables("POSTE")
        cmbPoste.ValueMember = "LibellePoste"
        cmbPoste.ColumnHeaders = False
        cmbPoste.Splits(0).DisplayColumns("LibellePoste").Width = 10
        cmbPoste.ExtendRightColumn = True


        'chargement des noms Clients
        cmdClient.Connection = ConnectionServeur
        cmdClient.CommandText = "SELECT DISTINCT CodeClient,Nom FROM  CLIENT WHERE Supprime = 0 ORDER BY Nom ASC"
        daClient = New SqlDataAdapter(cmdClient)
        daClient.Fill(dsClient, "CLIENT")
        CmbClient.DataSource = dsClient.Tables("CLIENT")
        CmbClient.ValueMember = "CodeClient"
        CmbClient.DisplayMember = "Nom"
        CmbClient.ColumnHeaders = False
        CmbClient.Splits(0).DisplayColumns("CodeClient").Visible = False
        CmbClient.Splits(0).DisplayColumns("Nom").Width = 10
        CmbClient.ExtendRightColumn = True


        dtpDebut.Value = System.DateTime.Today + " 00:00:00"
        dtpFin.Value = System.DateTime.Today + " 23:59:59"
        dtpDebut.Focus()
        Initialisation = True
        AfficherCaisse()
        chbNonVidees.Checked = True
    End Sub

    Public Sub AfficherCaisse()
        
        Dim List As New Library.SortableBindingList(Of Data.Reporting.P_Report_EtatDetailsCaisse_Result)(_SalesReportService.GetEtatDetailCaisse(chbNonVidees.Checked,
                                                                dtpDebut.Value, _
                                                                dtpFin.Value, _
                                                                cmbModePaiement.SelectedValue, _
                                                                cmbType.SelectedValue, _
                                                                IIf(IsNothing(cmbVendeur.SelectedValue), -1, cmbVendeur.SelectedValue), _
                                                                cmbPoste.SelectedValue, _
                                                                CmbClient.SelectedValue))

        With gCaisse
            .Columns.Clear()
            .DataSource =  List
            .Refresh()
            .AllowSort = True

            .Rebind(False)
            .Columns("Credit").Caption = "Reglement"
            .Columns("Date").NumberFormat = "dd/MM/yyyy HH:mm:ss"
            .Columns("DateEcheance").NumberFormat = "dd/MM/yyyy"
            .Columns("NatureReglement").Caption = "Nature Reglement"
            .Columns("DateEcheance").Caption = "Date Echeance"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("Id").Visible = False
            .Splits(0).DisplayColumns("CodeNatureReglement").Visible = False
            .Splits(0).DisplayColumns("Type").Visible = False
            .Splits(0).DisplayColumns("TypeOperation").Visible = False
            .Splits(0).DisplayColumns("CodeClient").Visible = False
            .Splits(0).DisplayColumns("TotalVenteTTC").Visible = False
            .Splits(0).DisplayColumns("Remise").Visible = False
            .Splits(0).DisplayColumns("CodePersonnel").Visible = False
            .Splits(0).DisplayColumns("NumeroOperation").Visible = False
            .Splits(0).DisplayColumns("Credit").Visible = False
            .Splits(0).DisplayColumns("Debit").Visible = False
            .Splits(0).DisplayColumns("MontantCnam").Visible = False
            .Splits(0).DisplayColumns("MontantMutuelle").Visible = False


            .Splits(0).DisplayColumns("DateEcheance").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("DateEcheance").Width = 100
            .Splits(0).DisplayColumns("Operation").Width = 200
            .Splits(0).DisplayColumns("Nom").Width = 170
            .Splits(0).DisplayColumns("Date").Width = 170
            .Splits(0).DisplayColumns("Date").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Credit").Width = 150
            .Splits(0).DisplayColumns("Credit").Width = 150
            .Splits(0).DisplayColumns("Type").Width = 150
            .Splits(0).DisplayColumns("Reglement").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Reglement").Width = 120

            .Splits(0).ColumnCaptionHeight = 40
            .Splits(0).RecordSelectors = False
            .ExtendRightColumn = True
            .EmptyRows = True
            .FetchRowStyles = True
            'Style du Caractere et du grid
            ParametreGrid(gCaisse)
        End With
        CalculValeur()

    End Sub

    Private Sub gCaisse_AfterSort(sender As Object, e As FilterEventArgs) Handles gCaisse.AfterSort
        Try
            _VOrderBy = e.Condition.Substring(0, e.Condition.IndexOf(" "))
            _VAscDesc = e.Condition.Substring(e.Condition.IndexOf(" ") + 1, e.Condition.Length - e.Condition.IndexOf(" ") - 1)
        Catch ex As Exception
            _VOrderBy = e.Condition
            _VAscDesc = "Asc"
        End Try
    End Sub

    Private Sub gCNAM_FetchRowStyle(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles gCaisse.FetchRowStyle
        e.CellStyle.Font = New System.Drawing.Font("Calibri", 9, FontStyle.Regular)
    End Sub

    Private Sub dtpDebut_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDebut.KeyUp
        If e.KeyCode = Keys.Enter Then
            dtpFin.Focus()
        End If
    End Sub

    Private Sub dtpDebut_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpDebut.TextChanged

    End Sub

    Private Sub dtpFin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpFin.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbVendeur.Focus()
        End If
    End Sub

    Private Sub dtpFin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpFin.TextChanged

    End Sub

    Private Sub CalculValeur()
        Dim cmdSQL As New SqlCommand
        TotalEspece = 0

        TotalCredit = 0

        TotalCheque = 0
        TotalCarte = 0
        TotalVirement = 0
        TotalTraite = 0
        TotalMultiple = 0
        TotalRemise = 0
        Total = 0
        Pourcentage = 0

        If gCaisse.RowCount <> 0 Then
            For I As Integer = 0 To gCaisse.RowCount - 1
                If IsDBNull(gCaisse(I, "Credit")) Then
                    gCaisse(I, "Credit") = 0
                End If

                If gCaisse(I, "NatureReglement") = "ESPECE" Then 'Or gCaisse(I, "Reglement") = "CREDIT" Then
                    TotalEspece += gCaisse(I, "Credit")
                End If


                If gCaisse(I, "NatureReglement") = "CREDIT" Then
                    TotalCredit += gCaisse(I, "Credit")
                End If


                If gCaisse(I, "NatureReglement") = "CHEQUE" Then
                    TotalCheque += gCaisse(I, "Credit")
                End If
                If gCaisse(I, "NatureReglement") = "CARTE" Then
                    TotalCarte += gCaisse(I, "Credit")
                End If
                If gCaisse(I, "Type") = "REMISE" Then
                    TotalRemise += gCaisse(I, "Credit")
                End If
                If gCaisse(I, "NatureReglement") = "VIREMENT" Then
                    TotalVirement += gCaisse(I, "Credit")
                End If
                If gCaisse(I, "NatureReglement") = "TRAITE" Then
                    TotalTraite += gCaisse(I, "Credit")
                End If
                If gCaisse(I, "NatureReglement") = "MULTIPLE" Then
                    TotalMultiple += gCaisse(I, "Credit")
                End If
                TotalRemise += gCaisse(I, "Remise")
            Next
            'LnbrLigne.Text = dsCaisse.Tables("CAISSE").Rows.Count


            ''Pourcentage des ventes
            'If cmbVendeur.Text <> "" Then

            '    cmdSQL.Connection = ConnectionServeur
            '    cmdSQL.CommandText = "SELECT SUM(Credit) AS Somme FROM Vue_EtatReglement WHERE Type = 'VENTE'"
            '    If Not IsDBNull(cmdSQL.ExecuteScalar) Then
            '        TotalVente = cmdSQL.ExecuteScalar
            '    End If
            '    cmdSQL.CommandText = "SELECT SUM(Credit) AS Somme FROM Vue_EtatReglement WHERE Type = 'VENTE' AND CodePersonnel = " + Quote(cmbVendeur.SelectedValue)
            '    If Not IsDBNull(cmdSQL.ExecuteScalar) Then
            '        TotalVenteVendeur = cmdSQL.ExecuteScalar
            '        Pourcentage = TotalVenteVendeur / TotalVente * 100
            '    End If
            '    tPourcentageVente.Text = Pourcentage.ToString + " %"
            'Else
            '    tPourcentageVente.Text = "100 %"
            'End If
        End If


        tEspece.Text = Format(TotalEspece, "# ### ##0.000")

        tCredit.Text = Format(TotalCredit, "# ### ##0.000")

        tCheque.Text = Format(TotalCheque, "# ### ##0.000")
        tCarte.Text = Format(TotalCarte, "# ### ##0.000")
        tRemise.Text = Format(TotalRemise, "# ### ##0.000")
        tVirement.Text = Format(TotalVirement, "# ### ##0.000")
        tTraite.Text = Format(TotalTraite, "# ### ##0.000")
        tMultiple.Text = Format(TotalMultiple, "# ### ##0.000")
        tTotal.Text = Format(TotalEspece + TotalCheque + TotalCarte + TotalTraite, "# ### ##0.000")

    End Sub

    Private Sub bImprimer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bImprimer.Click

        Dim _Parameters As New List(Of ReportParameter)()

        Dim I As Integer
        Dim num As Integer = 999
        For I = 0 To fMain.Tab.TabPages.Count - 1
            If fMain.Tab.TabPages(I).Text = "Impression Reporting Ventes" Then
                num = I
            End If
        Next
        Dim dt

        Dim MyViewer As New fImpressionReportingVente

        Dim _Vide As New ReportParameter()
        _Vide.Name = "Vide"
        _Vide.Values.Add(chbNonVidees.Checked)
        _Parameters.Add(_Vide)

        Dim _DateDebut As New ReportParameter()
        _DateDebut.Name = "DateDebut"
        _DateDebut.Values.Add(dtpDebut.Value)
        _Parameters.Add(_DateDebut)

        Dim _DateFin As New ReportParameter()
        _DateFin.Name = "DateFin"
        _DateFin.Values.Add(dtpFin.Value)
        _Parameters.Add(_DateFin)

        Dim _ModePaiement As New ReportParameter()
        _ModePaiement.Name = "ModePaiement"
        _ModePaiement.Values.Add(IIf(cmbModePaiement.Text <> "", cmbModePaiement.SelectedValue, Nothing))
        _Parameters.Add(_ModePaiement)

        Dim _TypeMutuelle As New ReportParameter()
        _TypeMutuelle.Name = "Type"
        _TypeMutuelle.Values.Add(IIf(cmbType.Text <> "", cmbType.SelectedValue, Nothing))
        _Parameters.Add(_TypeMutuelle)

        Dim _CodePersonnel As New ReportParameter()
        _CodePersonnel.Name = "CodePersonnel"
        _CodePersonnel.Values.Add(IIf(cmbVendeur.Text <> "", cmbVendeur.SelectedValue, Nothing))
        _Parameters.Add(_CodePersonnel)

        Dim _Poste As New ReportParameter()
        _Poste.Name = "Poste"
        _Poste.Values.Add(IIf(cmbPoste.Text <> "", cmbPoste.SelectedValue, Nothing))
        _Parameters.Add(_Poste)

        dt = _SalesReportService.GetEtatDetailCaisse(chbNonVidees.Checked,
                                                                dtpDebut.Value, _
                                                                dtpFin.Value, _
                                                                cmbModePaiement.SelectedValue, _
                                                                cmbType.SelectedValue, _
                                                                IIf(IsNothing(cmbVendeur.SelectedValue), -1, cmbVendeur.SelectedValue), _
                                                                cmbPoste.SelectedValue, CmbClient.SelectedValue).OrderBy(_VOrderBy + " " + _VAscDesc)

        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Detailscaissse", dt))
        MyViewer.ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("DS_Pharmacie", _SalesReportService.GetPharmacie()))
        MyViewer.ReportViewer1.LocalReport.ReportPath = Application.StartupPath + "\EtatDetailCaisse.rdl"



        MyViewer.ReportViewer1.LocalReport.SetParameters(_Parameters)

        fMain.Tab.TabPages.Add(New C1.Win.C1Command.C1DockingTabPage)
        fMain.Tab.SelectedIndex = fMain.Tab.TabPages.Count - 1
        fMain.Tab.SelectedTab.Controls.Add(MyViewer.Panel1)

        MyViewer.ReportViewer1.RefreshReport()

        fMain.Tab.SelectedTab.Text = "Impression Reporting Ventes"
        If num <> 999 Then
            fMain.Tab.TabPages(num).Dispose()
        End If

    End Sub

    Private Sub bQuitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bQuitter.Click
        fMain.Tab.SelectedTab.Dispose()
    End Sub

    Private Sub cmbVendeur_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbVendeur.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbModePaiement.Focus()
        End If
    End Sub

    Private Sub cmbModePaiement_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbModePaiement.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbType.Focus()

        End If
    End Sub

    Private Sub cmbType_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbType.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbPoste.Focus()
        End If
    End Sub

    Private Sub chbNonVidees_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbNonVidees.CheckedChanged

        If chbNonVidees.Checked = True Then
            dtpDebut.Enabled = False
            dtpFin.Enabled = False
        Else
            dtpDebut.Enabled = True
            dtpFin.Enabled = True
            dtpDebut.Text = Date.Today
            dtpFin.Value = System.DateTime.Today + " 23:59:59"
            dtpDebut.Focus()
        End If

        AfficherCaisse()

    End Sub

    Private Sub gCaisse_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gCaisse.KeyUp
        If e.KeyCode = Keys.F1 Then
            Dim MyVenteAffiche As New fVenteJusteAffichage
            MyVenteAffiche.NumeroVente = gCaisse(gCaisse.Row, "Operation").ToString.Replace("Reglement vente - ", "").Replace("Vente - ", "")  'Trim(gCaisse(gCaisse.Row, "n").ToString.Substring(4, gCaisse(gCaisse.Row, "Type").ToString.Length - 4))
            MyVenteAffiche.ShowDialog()
            MyVenteAffiche.Close()
            MyVenteAffiche.Dispose()
        End If
    End Sub

    Private Sub cmbPoste_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbPoste.KeyUp
        If e.KeyCode = Keys.Enter Then
            gCaisse.Focus()
            AfficherCaisse()
            'CalculValeur()
        End If
    End Sub

    Private Sub cmbVendeur_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbVendeur.TextChanged
        AfficherCaisse()
    End Sub

    Private Sub cmbModePaiement_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbModePaiement.TextChanged
        AfficherCaisse()
    End Sub

    Private Sub cmbType_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbType.TextChanged
        AfficherCaisse()
    End Sub

    Private Sub cmbPoste_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbPoste.TextChanged
        AfficherCaisse()
    End Sub

    Private Sub BindingSource1_CurrentChanged(sender As Object, e As EventArgs)

    End Sub

    Private Sub CmbClient_KeyUp(sender As Object, e As KeyEventArgs) Handles CmbClient.KeyUp
        If e.KeyCode = Keys.Enter Then
            AfficherCaisse()
        End If
    End Sub
End Class