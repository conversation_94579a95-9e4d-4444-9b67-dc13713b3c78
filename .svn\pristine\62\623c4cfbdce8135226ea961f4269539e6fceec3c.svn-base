﻿Imports GMap.NET.WindowsForms
Imports GMap.NET.WindowsForms.Markers
Imports GMap.NET.WindowsForms.ToolTips
Imports GMap.NET
Imports GMap.NET.MapProviders

Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Pharma2000Premium.ServiceOneKey



Public Class fMesVoisinage
    Dim cmd As New SqlCommand
    Dim connect As New ServiceOneClient()
    Dim ds As DataSet
    Dim CodeGSU As String = ""

    Dim cmdChargement As New SqlCommand
    Dim dsChargement As New DataSet
    Dim daChargement As New SqlDataAdapter
    Dim cbChargement As New SqlCommandBuilder

    Dim cmdMesVoisinages As New SqlCommand
    Dim dsMesVoisinages As New DataSet
    Dim daMesVoisinages As New SqlDataAdapter
    Dim cbMesVoisinages As New SqlCommandBuilder

    Dim overlayOne As GMap.NET.WindowsForms.GMapOverlay
    Dim circle As clsCircle

    Dim StrSQL As String

    Public typeConnexion As String
    Public typeCarte As String

    Dim InitLatitude As String = ""
    Dim InitLongitude As String = ""

    Dim mapMaxZoom As Integer

    Dim ArrayMarkers As GMapMarker_Custom

    Dim currentMarkerCrossIcon As GMapMarker
    Dim currentMarker As GMapMarker
    Dim currentPositionInedex As Integer


    Public Sub Init()

        Try

        ds = connect.RechercheClient(CodePharmacien)

        If ds.Tables("Table").Rows.Count = 0 Then
            btnRechercher.Enabled = False
            MsgBox("Vous n'etes pas inscrit au service OneKey!")
        Else

            cmd.Connection = ConnectionServeur
            cmd.CommandText = "SELECT CodeGSU FROM PARAMETRE_PHARMACIE"
            CodeGSU = cmd.ExecuteScalar.ToString

            If CodeGSU = "" Then
                btnRechercher.Enabled = False
                Exit Sub
            End If

            'Initialiser control
            initControls()

         
            'Initlaiser LEs Type d'eatablissment
            initTypeEtablissement()

            'Initiliser les Specialites
            'initSpecialite()

            'Initaliser les villes
            initVille()

            'Initialiser type affichage de la carte MAP
            initTypeCarteMap()

            'Initaliser la Map
            initMap()

            'Recuperer les coordonnees de la phramacie
            InitCoordonnees()
            End If

        Catch ex As Exception
            MsgBox(ex.Message)
        End Try

    End Sub

    Private Sub InitCoordonnees()
        'Recuperer les coordonnees de la phramacie
        InitLatitude = getLatLangPharmacie().ToString.Split(",")(0)
        InitLongitude = getLatLangPharmacie().ToString.Split(",")(1)
    End Sub

    Private Sub initControls()
        updMetres.Enabled = False
        lblNbrResultat.Visible = False
        mapMaxZoom = 20

        myMap.MaxZoom = mapMaxZoom
        tkbZoom.Maximum = mapMaxZoom

    End Sub


    Private Sub initSpecialite()

        Try

            '//specialité

            ds = connect.ListeSpecialte(CodeGSU, cmbEtablissement.SelectedValue)
            cmbSpecialite.DataSource = ds.Tables(0)
            cmbSpecialite.DisplayMember = "Ind_Specialty1"
            cmbSpecialite.ValueMember = "Ind_Specialty1"


            ''Chargement des fournisseurs
            ''StrSQL = "SELECT CodeSpecialite, LibelleSpecialite FROM OneKeySpecialite ORDER BY LibelleSpecialite ASC"
            'StrSQL = "  select distinct Ind_Specialty1 as Ind_Specialty1,Ind_Specialty1 as Ind_Specialty2  from OneKey ORDER BY 1 ASC"
            'cmdChargement.Connection = ConnectionServeur
            'cmdChargement.CommandText = StrSQL
            'daChargement = New SqlDataAdapter(cmdChargement)
            'daChargement.Fill(dsChargement, "ONEKEYSPECIALITE")

            'cmbSpecialite.DataSource = dsChargement.Tables("ONEKEYSPECIALITE")
            'cmbSpecialite.ValueMember = "Ind_Specialty1"
            'cmbSpecialite.DisplayMember = "Ind_Specialty2"
            cmbSpecialite.ColumnHeaders = False
            'cmbSpecialite.Splits(0).DisplayColumns("Ind_Specialty1").Visible = False
            'cmbSpecialite.Splits(0).DisplayColumns("Ind_Specialty2").Width = 10
            cmbSpecialite.ExtendRightColumn = True

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Mes Voisinages", "fMesVoisinages", "initSpecialite", ex.Message, "0000016", "Erreur d'excution de initSpecialite", True, True, True)
            Return

        End Try
    End Sub

    Private Sub initTypeEtablissement()
        Try

            '// Type Etablissement
            ds = connect.ListTypeEtablissement(CodeGSU)
            cmbEtablissement.DataSource = ds.Tables(0)
            cmbEtablissement.DisplayMember = "wkp_Location"
            cmbEtablissement.ValueMember = "wkp_Location"

            'Chargement des fournisseurs
            'StrSQL = "SELECT CodeTypeEtablissement, LibelleTypeEtablissement FROM OneKeyTypeEtablissement ORDER BY LibelleTypeEtablissement ASC"
            'StrSQL = "  select distinct Parent_Wkp_Usual_Name as Parent_Wkp_Usual_Name, Parent_Wkp_Usual_Name as Parent_Wkp_Usual_Name2 from OneKey order by 1 asc"
            'cmdChargement.Connection = ConnectionServeur
            'cmdChargement.CommandText = StrSQL
            'daChargement = New SqlDataAdapter(cmdChargement)
            'daChargement.Fill(dsChargement, "ONEKEYTYPEETABLISSEMENT")

            'cmbEtablissement.DataSource = dsChargement.Tables("ONEKEYTYPEETABLISSEMENT")
            'cmbEtablissement.ValueMember = "Parent_Wkp_Usual_Name"
            'cmbEtablissement.DisplayMember = "Parent_Wkp_Usual_Name2"
            cmbEtablissement.ColumnHeaders = False
            'cmbEtablissement.Splits(0).DisplayColumns("Parent_Wkp_Usual_Name").Visible = False
            'cmbEtablissement.Splits(0).DisplayColumns("Parent_Wkp_Usual_Name2").Width = 10
            cmbEtablissement.ExtendRightColumn = True

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Mes Voisinages", "fMesVoisinages", "initTypeEtablissement", ex.Message, "0000016", "Erreur d'excution de initTypeEtablissement", True, True, True)
            Return

        End Try
    End Sub

    

    Private Sub initVille()
        Try
            '//ville
            ds = connect.ListeVille(CodeGSU)
            cmbVille.DataSource = ds.Tables(0)
            cmbVille.DisplayMember = "Parent_Addr_City"
            cmbVille.ValueMember = "Parent_Addr_City"
            'Chargement des fournisseurs
            'StrSQL = "SELECT CodeVille, NomVille FROM OneKeyVille ORDER BY NomVille ASC"
            'StrSQL = " select distinct parent_addr_city  as parent_addr_city , parent_addr_city as parent_addr_city2 from OneKey ORDER BY 1 asc"
            'cmdChargement.Connection = ConnectionServeur
            'cmdChargement.CommandText = StrSQL
            'daChargement = New SqlDataAdapter(cmdChargement)
            'daChargement.Fill(dsChargement, "ONEKEYVILLE")

            'cmbVille.DataSource = dsChargement.Tables("ONEKEYVILLE")
            'cmbVille.ValueMember = "parent_addr_city"
            'cmbVille.DisplayMember = "parent_addr_city2"
            cmbVille.ColumnHeaders = False
            'cmbVille.Splits(0).DisplayColumns("parent_addr_city").Visible = False
            'cmbVille.Splits(0).DisplayColumns("parent_addr_city2").Width = 10
            cmbVille.ExtendRightColumn = True

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Mes Voisinages", "fMesVoisinages", "initVille", ex.Message, "0000016", "Erreur d'excution de initVille", True, True, True)
            Return

        End Try
    End Sub

    Private Sub initMap()

        Dim InitLatitude As String
        Dim InitLongitude As String

        With myMap

            'Choisir un provider
            'typeCarte = RecupererValeurExecuteScalaire("TypeCarte", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)

            'If typeCarte = "GoogleHybridMap".ToUpper Then
            '    .MapProvider = GMapProviders.GoogleHybridMap
            'ElseIf typeCarte = "GoogleMap".ToUpper Then
            '    .MapProvider = GMapProviders.GoogleMap
            'ElseIf typeCarte = "GoogleSatelliteMap".ToUpper Then
            '    .MapProvider = GMapProviders.GoogleSatelliteMap
            'ElseIf typeCarte = "GoogleTerrainMap".ToUpper Then
            '    .MapProvider = GMapProviders.GoogleTerrainMap
            'ElseIf typeCarte = "YahooHybridMap".ToUpper Then
            '    .MapProvider = GMapProviders.YahooHybridMap
            'ElseIf typeCarte = "YahooSatelliteMap".ToUpper Then
            '    .MapProvider = GMapProviders.YahooSatelliteMap
            'ElseIf typeCarte = "OpenStreetMap".ToUpper Then
            '    .MapProvider = GMapProviders.OpenStreetMap
            'ElseIf typeCarte = "OviHybridMap".ToUpper Then
            '    .MapProvider = GMapProviders.OviHybridMap
            'ElseIf typeCarte = "OpenCycleMap".ToUpper Then
            '    .MapProvider = GMapProviders.OpenCycleMap
            'ElseIf typeCarte = "YahooMap".ToUpper Then
            '    .MapProvider = GMapProviders.YahooMap
            'End If


            If cmbTypeCarte.Text = "GoogleHybridMap".ToUpper Then
                .MapProvider = GMapProviders.GoogleHybridMap
            ElseIf cmbTypeCarte.Text = "GoogleMap".ToUpper Then
                .MapProvider = GMapProviders.GoogleMap
            ElseIf cmbTypeCarte.Text = "GoogleSatelliteMap".ToUpper Then
                .MapProvider = GMapProviders.GoogleSatelliteMap
            ElseIf cmbTypeCarte.Text = "GoogleTerrainMap".ToUpper Then
                .MapProvider = GMapProviders.GoogleTerrainMap
            ElseIf cmbTypeCarte.Text = "YahooHybridMap".ToUpper Then
                .MapProvider = GMapProviders.YahooHybridMap
            ElseIf cmbTypeCarte.Text = "YahooSatelliteMap".ToUpper Then
                .MapProvider = GMapProviders.YahooSatelliteMap
            ElseIf cmbTypeCarte.Text = "OpenStreetMap".ToUpper Then
                .MapProvider = GMapProviders.OpenStreetMap
            ElseIf cmbTypeCarte.Text = "OviHybridMap".ToUpper Then
                .MapProvider = GMapProviders.OviHybridMap
            ElseIf cmbTypeCarte.Text = "OpenCycleMap".ToUpper Then
                .MapProvider = GMapProviders.OpenCycleMap
            ElseIf cmbTypeCarte.Text = "YahooMap".ToUpper Then
                .MapProvider = GMapProviders.YahooMap
            End If

            'Parametre de Zoom par defaut
            .Zoom = 17

            'Type de chargement image
            'typeConnexion = RecupererValeurExecuteScalaire("TypeConnexion", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
            typeConnexion = "ServerAndCache".ToUpper

            If typeConnexion = "ServerAndCache".ToUpper Then
                .Manager.Mode = AccessMode.ServerAndCache
            ElseIf typeConnexion = "CacheOnly".ToUpper Then
                .Manager.Mode = AccessMode.CacheOnly
            ElseIf typeConnexion = "ServerOnly".ToUpper Then
                .Manager.Mode = AccessMode.ServerOnly
            End If

        End With

        'Recuperer les coordonnees de la phramacie
        InitLatitude = getLatLangPharmacie().ToString.Split(",")(0)
        InitLongitude = getLatLangPharmacie().ToString.Split(",")(1)
        myMap.Position = New GMap.NET.PointLatLng(InitLatitude, InitLongitude)

        overlayOne = New GMap.NET.WindowsForms.GMapOverlay(myMap, "OverlayOne")

        'Center le Map à notre pharmacie
        overlayOne.Markers.Add(New GMap.NET.WindowsForms.Markers.GMapMarkerGoogleRed(New GMap.NET.PointLatLng(InitLatitude, InitLongitude)))
        overlayOne.Markers.Add(New GMap.NET.WindowsForms.Markers.GMapMarkerCross(New GMap.NET.PointLatLng(InitLatitude, InitLongitude)))

        currentMarkerCrossIcon = New GMap.NET.WindowsForms.Markers.GMapMarkerCross(New GMap.NET.PointLatLng(InitLatitude, InitLongitude))
        overlayOne.Markers.Add(currentMarkerCrossIcon)

        overlayOne.Markers.Item(0).ToolTipText = "Ma Pharmacie"
        overlayOne.Markers.Item(0).ToolTipMode = MarkerTooltipMode.Always

        'Ajouter le Map
        myMap.Overlays.Add(overlayOne)

    End Sub

    Private Sub myMap_OnMarkerClick(ByVal item As GMap.NET.WindowsForms.GMapMarker, ByVal e As System.Windows.Forms.MouseEventArgs)
        MessageBox.Show(item.LocalPosition.ToString)
    End Sub

    Private Sub initOneKey()

        Try
            With gListeOneKey
                .Columns("Parent_Wkp_Usual_Name").Caption = "Etablissement"
                .Columns("Parent_WKP_Local_Onekey_ID").Caption = "Parent_WKP_Local_Onekey_ID"
                .Columns("Ind_Gender_EN").Caption = "Genre"
                .Columns("Ind_Specialty1").Caption = "Spécialité"
                .Columns("Parent_Addr_Address").Caption = "Adresse"
                .Columns("Parent_Addr_City").Caption = "Ville"
                .Columns("Aff_Phone_Number").Caption = "Tél"
                .Columns("Latitude").Caption = "Latitude"
                .Columns("Longitude").Caption = "Longitude"

                .Splits(0).ColumnCaptionHeight = 20

                ' Centrer toutes les entêtes
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Next

                'coloriage de la liste 
                For i = 0 To .Columns.Count - 1
                    .Splits(0).DisplayColumns(i).Style.BackColor = Color.AliceBlue
                Next

                .MultiSelect = MultiSelectEnum.Extended
                '.FilterBar = True

                .Splits(0).DisplayColumns("CodeGSU75").Visible = False
                .Splits(0).DisplayColumns("Ind_Specialty1").Visible = False
                .Splits(0).DisplayColumns("Parent_WKP_Local_Onekey_ID").Visible = False
                .Splits(0).DisplayColumns("Wkp_Location").Visible = False
                .Splits(0).DisplayColumns("Ind_Gender_EN").Visible = False
                .Splits(0).DisplayColumns("Latitude").Visible = False
                .Splits(0).DisplayColumns("Longitude").Visible = False
                .Splits(0).DisplayColumns("Parent_Addr_Address").Visible = False
                .Splits(0).DisplayColumns("Aff_Phone_Number").Visible = False
                .Splits(0).DisplayColumns("Parent_Wkp_Usual_Name").Width = 200
                .Splits(0).DisplayColumns("Parent_Addr_City").Width = 80

                .Splits(0).ColumnCaptionHeight = 40
                .ExtendRightColumn = True
            End With
            ParametreGrid(gListeOneKey)
            gListeOneKey.HeadingStyle.Font = New Font("", 8)
        Catch ex As Exception

            MsgBox("Une erreur s'est produite ! " + ex.Message, MsgBoxStyle.Critical, "Erreur N°0007")
            Return

        End Try

    End Sub

    Private Sub btnRechercher_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRechercher.Click
        Dim cmd As New SqlCommand
        Dim rechercheSexe As String = ""

        'Il faut saisir au moins le type d'établissement

        If cmbEtablissement.SelectedValue = "" Then
            MsgBox("Merci de choisir le type d'établissement ", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "Erreur de saisie")
            cmbEtablissement.Focus()
            Exit Sub
        End If

        If cmbEtablissement.SelectedValue <> "Cabinet" And cmbEtablissement.SelectedValue <> "Cabinet de groupe" Then
            cmbSpecialite.Text = " "
        End If

        ''Il faut saisir au moins la spécialité
        If cmbSpecialite.SelectedValue = "" Then
            MsgBox("Merci de choisir la spécialité ", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "Erreur de saisie")
            cmbSpecialite.Focus()
            Exit Sub
        End If

        'affciher resultat dans la grid
        If rdbFemme.Checked Then
            rechercheSexe = "Female"
        End If

        If rdbHomme.Checked Then
            rechercheSexe = "Male"
        End If

        If cmbEtablissement.SelectedValue <> "Cabinet" And cmbEtablissement.SelectedValue <> "Cabinet de groupe" Then
            rechercheSexe = ""
        End If


        'initOneKey("1=1", cmbSpecialite.SelectedValue, cmbEtablissement.SelectedValue, "", cmbVille.SelectedValue, tNom.Text, rechercheSexe)

        ds = connect.RechercheOneKey(CodeGSU, cmbSpecialite.Text, cmbEtablissement.Text, cmbVille.Text, tNom.Text, rechercheSexe)
        gListeOneKey.DataSource = ds
        gListeOneKey.DataMember = "Table"
        initOneKey()
        'afficher resultat dans le Map
        searchMap()

        'Afficher le nombre des resultats trouves
        lblNbrResultat.Visible = True
        lblNbrResultat.Text = "Nombre des résultats trouvés: " & gListeOneKey.RowCount
    End Sub

    Private Function getLatLangPharmacie()

        Dim cmdRecupereNum As New SqlCommand

        Try

            StrSQL = " SELECT TOP(1) Latitude_Longitude FROM PARAMETRE_PHARMACIE "

            cmdRecupereNum.Connection = ConnectionServeur
            cmdRecupereNum.CommandText = StrSQL

            getLatLangPharmacie = cmdRecupereNum.ExecuteScalar()

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Mes Voisinages", "fMesVoisinages", "getLatLang", ex.Message, "0000022", "Erreur d'excution de getLatLang", True, True, True)
            getLatLangPharmacie = "0,0"

        End Try

    End Function

    Private Sub searchMap()

        Dim InitLatitude As String
        Dim InitLongitude As String
        Dim cptListe As Integer



        With myMap

            'Effacer les donnees sur le Map
            myMap.Overlays.Clear()

            'Choisir un provider
            'Choisir un provider
            'typeCarte = RecupererValeurExecuteScalaire("TypeCarte", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)

            If cmbTypeCarte.Text = "GoogleHybridMap".ToUpper Then
                .MapProvider = GMapProviders.GoogleHybridMap
            ElseIf cmbTypeCarte.Text = "GoogleMap".ToUpper Then
                .MapProvider = GMapProviders.GoogleMap
            ElseIf cmbTypeCarte.Text = "GoogleSatelliteMap".ToUpper Then
                .MapProvider = GMapProviders.GoogleSatelliteMap
            ElseIf cmbTypeCarte.Text = "GoogleTerrainMap".ToUpper Then
                .MapProvider = GMapProviders.GoogleTerrainMap
            ElseIf cmbTypeCarte.Text = "YahooHybridMap".ToUpper Then
                .MapProvider = GMapProviders.YahooHybridMap
            ElseIf cmbTypeCarte.Text = "YahooSatelliteMap".ToUpper Then
                .MapProvider = GMapProviders.YahooSatelliteMap
            ElseIf cmbTypeCarte.Text = "OpenStreetMap".ToUpper Then
                .MapProvider = GMapProviders.OpenStreetMap
            ElseIf cmbTypeCarte.Text = "OviHybridMap".ToUpper Then
                .MapProvider = GMapProviders.OviHybridMap
            ElseIf cmbTypeCarte.Text = "OpenCycleMap".ToUpper Then
                .MapProvider = GMapProviders.OpenCycleMap
            ElseIf cmbTypeCarte.Text = "YahooMap".ToUpper Then
                .MapProvider = GMapProviders.YahooMap
            End If

            'If typeCarte = "GoogleHybridMap".ToUpper Then
            '    .MapProvider = GMapProviders.GoogleHybridMap
            'ElseIf typeCarte = "GoogleMap".ToUpper Then
            '    .MapProvider = GMapProviders.GoogleMap
            'ElseIf typeCarte = "GoogleSatelliteMap".ToUpper Then
            '    .MapProvider = GMapProviders.GoogleSatelliteMap
            'ElseIf typeCarte = "GoogleTerrainMap".ToUpper Then
            '    .MapProvider = GMapProviders.GoogleTerrainMap
            'ElseIf typeCarte = "YahooHybridMap".ToUpper Then
            '    .MapProvider = GMapProviders.YahooHybridMap
            'ElseIf typeCarte = "YahooSatelliteMap".ToUpper Then
            '    .MapProvider = GMapProviders.YahooSatelliteMap
            'ElseIf typeCarte = "OpenStreetMap".ToUpper Then
            '    .MapProvider = GMapProviders.OpenStreetMap
            'ElseIf typeCarte = "OviHybridMap".ToUpper Then
            '    .MapProvider = GMapProviders.OviHybridMap
            'ElseIf typeCarte = "OpenCycleMap".ToUpper Then
            '    .MapProvider = GMapProviders.OpenCycleMap
            'ElseIf typeCarte = "YahooMap".ToUpper Then
            '    .MapProvider = GMapProviders.YahooMap
            'End If

            'Parametre de Zoom par defaut
            '.Zoom = 17

            'Type de chargement image
            typeConnexion = RecupererValeurExecuteScalaire("TypeConnexion", "PARAMETRE_PHARMACIE", "CodePharmacie", CodePharmacien)
            If typeConnexion = "ServerAndCache".ToUpper Then
                .Manager.Mode = AccessMode.ServerAndCache
            ElseIf typeConnexion = "CacheOnly".ToUpper Then
                .Manager.Mode = AccessMode.CacheOnly
            ElseIf typeConnexion = "ServerOnly".ToUpper Then
                .Manager.Mode = AccessMode.ServerOnly
            End If

        End With

        'Recuperer les coordonnees de la phramacie
        InitLatitude = getLatLangPharmacie().ToString.Split(",")(0)
        InitLongitude = getLatLangPharmacie().ToString.Split(",")(1)
        myMap.Position = New GMap.NET.PointLatLng(InitLatitude, InitLongitude)

        overlayOne = New GMap.NET.WindowsForms.GMapOverlay(myMap, "OverlayOne")

        'Center Map à notre pharmacie
        overlayOne.Markers.Add(New GMap.NET.WindowsForms.Markers.GMapMarkerGoogleRed(New GMap.NET.PointLatLng(getLatLangPharmacie().ToString.Split(",")(0), getLatLangPharmacie().ToString.Split(",")(1))))

        currentMarkerCrossIcon = New GMap.NET.WindowsForms.Markers.GMapMarkerCross(New GMap.NET.PointLatLng(getLatLangPharmacie().ToString.Split(",")(0), getLatLangPharmacie().ToString.Split(",")(1)))
        overlayOne.Markers.Add(currentMarkerCrossIcon)

        overlayOne.Markers.Item(0).ToolTipText = "Ma Pharmacie"
        overlayOne.Markers.Item(0).ToolTipMode = MarkerTooltipMode.Always
        overlayOne.Markers.Item(0).Tag = "0"

        cptListe = 0
        While cptListe < gListeOneKey.RowCount

            overlayOne.Markers.Add(New GMap.NET.WindowsForms.Markers.GMapMarkerGoogleGreen(New GMap.NET.PointLatLng(gListeOneKey(cptListe, "Latitude"), gListeOneKey(cptListe, "Longitude"))))
            overlayOne.Markers.Item(cptListe + 2).ToolTipText = gListeOneKey(cptListe, "Parent_Wkp_Usual_Name")
            'overlayOne.Markers.Item(cptListe + 2).Tag = gListeOneKey(cptListe, "Ind_Local_Onekey_ID")
            'cptListe + 2 : parce que on a ajouter on haut 2 markers
            cptListe = cptListe + 1
        End While

        'Si on choisi mes voisinages avcec criteres rayon par metres
        If chkMesVoisinages.Checked = True Then

            circle = New clsCircle(New GMap.NET.PointLatLng(InitLatitude, InitLongitude), myMap)
            circle.Radius = updMetres.Value

            'Ajouter
            overlayOne.Markers.Add(circle)
            'overlayOne.Markers
        End If

        'Creer Cercle
        createCircle()

        'Ajouter le Map
        myMap.Overlays.Add(overlayOne)

    End Sub

    Private Sub gArticles_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles gListeOneKey.DoubleClick
        'afficher la carte 
        Try
            connect.LOG(gListeOneKey.Columns("Parent_WKP_Local_Onekey_ID").Value.ToString, CodePharmacien)
        Catch ex As Exception
            MsgBox("Une erreur s'est produite : " + ex.Message)
        End Try
        afficherCarteVisite()
    End Sub

    Private Sub afficherCarteVisite()

        If gListeOneKey.RowCount > 0 Then
            'Center la Map vers l'element sellectione
            myMap.Position = New GMap.NET.PointLatLng(gListeOneKey.Columns("Latitude").Value, gListeOneKey.Columns("Longitude").Value)

            'Changer le croix vers le Marker sellectione
            'currentMarkerCrossIcon.Position = New PointLatLng(gArticles.Columns("Latitude").Value, gArticles.Columns("Longitude").Value)

            fFicheDeContact.lNom.Text = gListeOneKey.Columns("Parent_Wkp_Usual_Name").Value.ToString
            fFicheDeContact.lSpecialite.Text = gListeOneKey.Columns("Ind_Specialty1").Value
            fFicheDeContact.lTypeEtablissement.Text = gListeOneKey.Columns("wkp_Location").Value
            fFicheDeContact.lTel.Text = gListeOneKey.Columns("Aff_Phone_Number").Value
            fFicheDeContact.lSexe.Text = gListeOneKey.Columns("Ind_Gender_EN").Value
            fFicheDeContact.lAdresse.Text = gListeOneKey.Columns("Parent_Addr_Address").Value
            fFicheDeContact.lVille.Text = gListeOneKey.Columns("Parent_Addr_City").Value

            createCircle()

            fFicheDeContact.ShowDialog()
        End If

    End Sub

    Private Sub chkMesVoisinages_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkMesVoisinages.CheckedChanged

        If chkMesVoisinages.Checked Then
            updMetres.Enabled = True
        Else
            updMetres.Enabled = False
        End If

        'Creer une cercle 
        createCircle()

    End Sub

    Private Sub createCircle()

        Try

            If chkMesVoisinages.Checked Then

                'Supprimer la cercle
                overlayOne.Markers.Remove(circle)

                'Si on choisi mes voisinages avcec criteres rayon par metres
                If chkMesVoisinages.Checked = True Then

                    circle = New clsCircle(New GMap.NET.PointLatLng(InitLatitude, InitLongitude), myMap)
                    circle.Radius = updMetres.Value

                    'Ajouter
                    overlayOne.Markers.Add(circle)
                    'overlayOne.Markers
                End If

            Else
                'Supprimer la cercle
                overlayOne.Markers.Remove(circle)
            End If

        Catch ex As Exception

        End Try

    End Sub

    Private Sub cmbSpecialite_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbSpecialite.KeyUp
        If e.KeyCode = Keys.Enter Then
            cmbEtablissement.Focus()
        End If
    End Sub

    Private Sub cmbEtablissement_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbEtablissement.KeyUp
        If e.KeyCode = Keys.Enter Then
            'cmbGouvernorat.Focus()
        End If
    End Sub

    Private Sub cmbVille_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbVille.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNom.Focus()
        End If
    End Sub

    Private Sub tNom_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNom.KeyUp
        If e.KeyCode = Keys.Enter Then
            btnRechercher.Focus()
        End If
    End Sub

    Private Sub updMetres_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles updMetres.KeyUp
        If e.KeyCode = Keys.Enter Then
            btnRechercher.Focus()
        End If
    End Sub

    Private Sub myMap_MouseCaptureChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles myMap.MouseCaptureChanged

    End Sub

    Private Sub tkbZoom_Scroll(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tkbZoom.Scroll
        myMap.Zoom = tkbZoom.Value
    End Sub


    Private Sub initTypeCarteMap()
        Try

            'Ajouter les diffrents types des cates MAP
            cmbTypeCarte.DataMode = C1.Win.C1List.DataModeEnum.AddItem

            cmbTypeCarte.AddItem("GoogleHybridMap")
            cmbTypeCarte.AddItem("GoogleMap")
            cmbTypeCarte.AddItem("GoogleSatelliteMap")
            cmbTypeCarte.AddItem("GoogleTerrainMap")
            cmbTypeCarte.AddItem("YahooHybridMap")
            cmbTypeCarte.AddItem("YahooMap")
            cmbTypeCarte.AddItem("YahooSatelliteMap")
            cmbTypeCarte.AddItem("OpenStreetMap")
            cmbTypeCarte.AddItem("OviHybridMap")
            cmbTypeCarte.AddItem("OpenCycleMap")

            cmbTypeCarte.ColumnHeaders = False

            cmbTypeCarte.ExtendRightColumn = True

            cmbTypeCarte.Text = "GoogleMap".ToUpper

        Catch ex As Exception

            'Gérer l'Exception
            fMessageException.Show("Mes Voisinages", "fMesVoisinages", "initSpecialite", ex.Message, "0000016", "Erreur d'excution de initSpecialite", True, True, True)
            Return

        End Try
    End Sub


    Private Sub myMap_OnMapZoomChanged() Handles myMap.OnMapZoomChanged

        tkbZoom.Value = myMap.Zoom
        createCircle()

    End Sub

    Private Sub lklMapharmacie_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lklMapharmacie.LinkClicked

        'Initialiser l'ecran sur ma pharmacie
        selectionPharmacie()

        'recreer le cercle 
        createCircle()

    End Sub

    Private Sub selectionPharmacie()

        'Recuperer les coordonnees de la phramacie
        InitLatitude = getLatLangPharmacie().ToString.Split(",")(0)
        InitLongitude = getLatLangPharmacie().ToString.Split(",")(1)
        myMap.Position = New GMap.NET.PointLatLng(InitLatitude, InitLongitude)

    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click

        InitLatitude = getLatLangPharmacie().ToString.Split(",")(0)
        InitLongitude = getLatLangPharmacie().ToString.Split(",")(1)

        ArrayMarkers = New GMapMarker_Custom(New PointLatLng(InitLatitude, InitLongitude), "saisie")
        ArrayMarkers.ToolTipText = "custoom "
        overlayOne.Markers.Add(ArrayMarkers)


    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        'ArrayMarkers = CType(overlayOne.Markers.Item(0), GMapMarker_Custom)
        'ArrayMarkers.ChangeImage("CDBAD")
    End Sub

    Private Sub myMap_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles myMap.DoubleClick

    End Sub

    Private Sub myMap_MouseClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles myMap.MouseClick

    End Sub


    Private Sub myMap_MouseDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles myMap.MouseDoubleClick
        'afficher la carte 
        afficherCarteVisite()
    End Sub


    Private Sub myMap_OnMapDrag() Handles myMap.OnMapDrag

        'Reecrer la cercle 
        createCircle()

    End Sub

    Private Sub myMap_DragDrop(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DragEventArgs) Handles myMap.DragDrop
        MsgBox(myMap.Position.Lat & " / " & myMap.Position.Lng)
    End Sub

    Private Sub cmbTypeCarte_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbTypeCarte.TextChanged

        Try

            With myMap
                'Choisir un provider
                If cmbTypeCarte.Text.ToUpper = "GoogleHybridMap".ToUpper Then
                    .MapProvider = GMapProviders.GoogleHybridMap
                ElseIf cmbTypeCarte.Text.ToUpper = "GoogleMap".ToUpper Then
                    .MapProvider = GMapProviders.GoogleMap
                ElseIf cmbTypeCarte.Text.ToUpper = "GoogleSatelliteMap".ToUpper Then
                    .MapProvider = GMapProviders.GoogleSatelliteMap
                ElseIf cmbTypeCarte.Text.ToUpper = "GoogleTerrainMap".ToUpper Then
                    .MapProvider = GMapProviders.GoogleTerrainMap
                ElseIf cmbTypeCarte.Text.ToUpper = "YahooHybridMap".ToUpper Then
                    .MapProvider = GMapProviders.YahooHybridMap
                ElseIf cmbTypeCarte.Text.ToUpper = "YahooSatelliteMap".ToUpper Then
                    .MapProvider = GMapProviders.YahooSatelliteMap
                ElseIf cmbTypeCarte.Text.ToUpper = "OpenStreetMap".ToUpper Then
                    .MapProvider = GMapProviders.OpenStreetMap
                ElseIf cmbTypeCarte.Text.ToUpper = "OviHybridMap".ToUpper Then
                    .MapProvider = GMapProviders.OviHybridMap
                ElseIf cmbTypeCarte.Text.ToUpper = "OpenCycleMap".ToUpper Then
                    .MapProvider = GMapProviders.OpenCycleMap
                ElseIf cmbTypeCarte.Text.ToUpper = "YahooMap".ToUpper Then
                    .MapProvider = GMapProviders.YahooMap
                End If

                .Refresh()

            End With

        Catch ex As Exception
            MsgBox("Une erreur s'est produite", MsgBoxStyle.Critical, "Erreur N°0013")
        End Try

    End Sub

    Private Sub updMetres_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles updMetres.ValueChanged

        'creer cercle
        createCircle()

    End Sub

    Private Sub myMap_OnMarkerClick_1(ByVal item As GMap.NET.WindowsForms.GMapMarker, ByVal e As System.Windows.Forms.MouseEventArgs) Handles myMap.OnMarkerClick
        Try
            currentMarkerCrossIcon.Position = item.Position

            'Modifier l'emplacement
            currentPositionInedex = GetMarkerItemID(item.Tag)

            'Selectinne l'element dans la liste
            gListeOneKey.Row = indexMarkerInGrid(item.Tag)

        Catch ex As Exception
            MsgBox("Une erreur s'est produite", MsgBoxStyle.Critical, "Erreur N°0014")
        End Try
    End Sub
    Private Function indexMarkerInGrid(ByVal pKeyNone As String)

        Try

            Dim cpt As Integer = 0

            For cpt = 0 To gListeOneKey.RowCount

                If gListeOneKey(cpt, "Ind_Local_Onekey_ID") = pKeyNone Then
                    Return cpt
                    Exit Function
                End If

            Next

            indexMarkerInGrid = "0"

        Catch ex As Exception
            MsgBox("Une erreur s'est produite", MsgBoxStyle.Critical, "Erreur N°0014")
            indexMarkerInGrid = "0"
        End Try

    End Function

    Private Function GetMarkerItemID(ByVal pKeyOne As String)
        Try

            For cpt = 0 To overlayOne.Markers.Count - 1
                If overlayOne.Markers.Item(cpt).Tag = pKeyOne Then
                    Return cpt
                    Exit Function
                End If
            Next

            GetMarkerItemID = "0"

        Catch ex As Exception
            MsgBox("Une erreur s'est produite", MsgBoxStyle.Critical, "Erreur N°0015")
            GetMarkerItemID = "0"
        End Try

    End Function

    Private Sub cmbGouvernorat_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            cmbVille.Focus()
        End If
    End Sub

    Private Sub fMesVoisinage_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

    End Sub

    Private Sub cmbEtablissement_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbEtablissement.TextChanged
        'Initiliser les Specialites
        initSpecialite()
    End Sub
End Class
