﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fCleRegistre
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.GroupTaux = New System.Windows.Forms.GroupBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.tCleActivation = New C1.Win.C1Input.C1TextBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.cmbAdresseMac = New System.Windows.Forms.ComboBox()
        Me.bEnregistrer = New C1.Win.C1Input.C1Button()
        Me.lblMacAddress = New C1.Win.C1Input.C1TextBox()
        Me.GroupTaux.SuspendLayout()
        CType(Me.tCleActivation, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lblMacAddress, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupTaux
        '
        Me.GroupTaux.Controls.Add(Me.lblMacAddress)
        Me.GroupTaux.Controls.Add(Me.bEnregistrer)
        Me.GroupTaux.Controls.Add(Me.Label3)
        Me.GroupTaux.Controls.Add(Me.tCleActivation)
        Me.GroupTaux.Controls.Add(Me.Label2)
        Me.GroupTaux.Controls.Add(Me.Label1)
        Me.GroupTaux.Controls.Add(Me.cmbAdresseMac)
        Me.GroupTaux.ForeColor = System.Drawing.Color.DodgerBlue
        Me.GroupTaux.Location = New System.Drawing.Point(12, 11)
        Me.GroupTaux.Name = "GroupTaux"
        Me.GroupTaux.Size = New System.Drawing.Size(356, 230)
        Me.GroupTaux.TabIndex = 0
        Me.GroupTaux.TabStop = False
        Me.GroupTaux.Text = "Activation"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.ForeColor = System.Drawing.SystemColors.WindowText
        Me.Label3.Location = New System.Drawing.Point(13, 92)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(85, 13)
        Me.Label3.TabIndex = 4
        Me.Label3.Text = "Clé d'activation :"
        '
        'tCleActivation
        '
        Me.tCleActivation.BackColor = System.Drawing.Color.White
        Me.tCleActivation.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.tCleActivation.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tCleActivation.Location = New System.Drawing.Point(16, 108)
        Me.tCleActivation.Multiline = True
        Me.tCleActivation.Name = "tCleActivation"
        Me.tCleActivation.Size = New System.Drawing.Size(325, 69)
        Me.tCleActivation.TabIndex = 4
        Me.tCleActivation.Tag = Nothing
        Me.tCleActivation.TextDetached = True
        Me.tCleActivation.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2010Blue
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.ForeColor = System.Drawing.SystemColors.WindowText
        Me.Label2.Location = New System.Drawing.Point(13, 19)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(72, 13)
        Me.Label2.TabIndex = 1
        Me.Label2.Text = "Cartes réseau"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.ForeColor = System.Drawing.SystemColors.WindowText
        Me.Label1.Location = New System.Drawing.Point(12, 63)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(74, 13)
        Me.Label1.TabIndex = 2
        Me.Label1.Text = "Adresse mac :"
        '
        'cmbAdresseMac
        '
        Me.cmbAdresseMac.FormattingEnabled = True
        Me.cmbAdresseMac.Location = New System.Drawing.Point(15, 35)
        Me.cmbAdresseMac.Name = "cmbAdresseMac"
        Me.cmbAdresseMac.Size = New System.Drawing.Size(326, 21)
        Me.cmbAdresseMac.TabIndex = 2
        '
        'bEnregistrer
        '
        Me.bEnregistrer.BackColor = System.Drawing.Color.FromArgb(CType(CType(213, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.bEnregistrer.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bEnregistrer.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.bEnregistrer.Location = New System.Drawing.Point(221, 183)
        Me.bEnregistrer.Name = "bEnregistrer"
        Me.bEnregistrer.Size = New System.Drawing.Size(120, 32)
        Me.bEnregistrer.TabIndex = 5
        Me.bEnregistrer.Text = "&Enregistrer"
        Me.bEnregistrer.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText
        Me.bEnregistrer.UseVisualStyleBackColor = False
        Me.bEnregistrer.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'lblMacAddress
        '
        Me.lblMacAddress.BackColor = System.Drawing.Color.White
        Me.lblMacAddress.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.lblMacAddress.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblMacAddress.Location = New System.Drawing.Point(92, 59)
        Me.lblMacAddress.Multiline = True
        Me.lblMacAddress.Name = "lblMacAddress"
        Me.lblMacAddress.Size = New System.Drawing.Size(249, 21)
        Me.lblMacAddress.TabIndex = 3
        Me.lblMacAddress.Tag = Nothing
        Me.lblMacAddress.TextDetached = True
        Me.lblMacAddress.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2010Blue
        '
        'tCleRegistre
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(239, Byte), Integer), CType(CType(246, Byte), Integer), CType(CType(253, Byte), Integer))
        Me.ClientSize = New System.Drawing.Size(382, 253)
        Me.Controls.Add(Me.GroupTaux)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "tCleRegistre"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.GroupTaux.ResumeLayout(False)
        Me.GroupTaux.PerformLayout()
        CType(Me.tCleActivation, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lblMacAddress, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents GroupTaux As System.Windows.Forms.GroupBox
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents cmbAdresseMac As System.Windows.Forms.ComboBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents tCleActivation As C1.Win.C1Input.C1TextBox
    Friend WithEvents bEnregistrer As C1.Win.C1Input.C1Button
    Friend WithEvents lblMacAddress As C1.Win.C1Input.C1TextBox
End Class
