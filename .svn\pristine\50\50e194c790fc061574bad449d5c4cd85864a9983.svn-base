﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fListeReglement

    Dim cmdReglement As New SqlCommand
    Dim cbReglement As New SqlCommandBuilder
    Dim dsReglement As New DataSet
    Dim daReglement As New SqlDataAdapter
    Public NuemroReleve As String = ""
    Dim StrSQL As String = ""

    Public Sub init()

        cmdReglement.CommandText = "SELECT " + _
                                "	NATURE_REGLEMENT.LibelleNatureReglement, " + _
                                "	REGLEMENT_CNAM.Date, " + _
                                "	REGLEMENT_CNAM.DateEcheance, " + _
                                "	REGLEMENT_CNAM.Montant, " + _
                                "	REGLEMENT_CNAM.LibelleReglement, " + _
                                "	REGLEMENT_CNAM.NumeroCheque, " + _
                                "	REGLEMENT_CNAM.NomInscritSurLeCheque, " + _
                                "	BANQUE.NomBanque, " + _
                                "	REGLEMENT_CNAM.Encaisse " + _
                                "FROM  " + _
                                "	REGLEMENT_CNAM " + _
                                "	LEFT OUTER JOIN NATURE_REGLEMENT ON NATURE_REGLEMENT.CodeNatureReglement = REGLEMENT_CNAM.CodeNatureReglement " + _
                                "	LEFT OUTER JOIN BANQUE ON BANQUE.CodeBanque = REGLEMENT_CNAM.CodeBanque " + _
                                "WHERE " + _
                                "	REGLEMENT_CNAM.NumeroReglementCnam IN ( " + _
                                "											SELECT " + _
                                "												DiSTINCT NumeroReglementCnam " + _
                                "											FROM " + _
                                "												REGLEMENT_CNAM_VENTE " + _
                                "												INNER JOIN RELEVE_CNAM_DETAILS ON RELEVE_CNAM_DETAILS.NumeroReleve = REGLEMENT_CNAM_VENTE.NumeroReleve " + _
                                "											WHERE " + _
                                "												REGLEMENT_CNAM_VENTE.NumeroReleve = '" + NuemroReleve + "' " + _
                                "											) "

        cmdReglement.Connection = ConnectionServeur
        daReglement = New SqlDataAdapter(cmdReglement)
        daReglement.Fill(dsReglement, "Regelements")

        With gReglement
            .Columns.Clear()
            .DataSource = dsReglement
            .DataMember = "Regelements"
            .Rebind(False)
            .Columns("LibelleNatureReglement").Caption = "Nature"
            .Columns("Date").Caption = "Date"
            .Columns("DateEcheance").Caption = "Date échéance"
            .Columns("Montant").Caption = "Montant"
            .Columns("LibelleReglement").Caption = "Libellé"
            .Columns("NumeroCheque").Caption = "N° Chèque"
            .Columns("NomInscritSurLeCheque").Caption = "Nom inscrit sur le chèque"
            .Columns("NomBanque").Caption = "Nom banque"
            .Columns("Encaisse").Caption = "Encaisse"

            ' Centrer tous les entêtes
            For I = 0 To .Columns.Count - 1
                .Splits(0).DisplayColumns(I).HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
            Next

            .Splits(0).DisplayColumns("LibelleNatureReglement").Width = 160
            .Splits(0).DisplayColumns("LibelleNatureReglement").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("Date").Width = 100
            .Splits(0).DisplayColumns("Date").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("DateEcheance").Width = 100
            .Splits(0).DisplayColumns("DateEcheance").Style.HorizontalAlignment = AlignHorzEnum.Center

            .Splits(0).DisplayColumns("Montant").Width = 350
            .Splits(0).DisplayColumns("Montant").Style.HorizontalAlignment = AlignHorzEnum.Center

            .Splits(0).DisplayColumns("LibelleReglement").Width = 100
            .Splits(0).DisplayColumns("LibelleReglement").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("NumeroCheque").Width = 100
            .Splits(0).DisplayColumns("NumeroCheque").Style.HorizontalAlignment = AlignHorzEnum.Center
            .Splits(0).DisplayColumns("NomInscritSurLeCheque").Width = 140
            .Splits(0).DisplayColumns("NomInscritSurLeCheque").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("NomBanque").Width = 140
            .Splits(0).DisplayColumns("NomBanque").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Encaisse").Width = 130
            .Splits(0).DisplayColumns("Encaisse").Style.HorizontalAlignment = AlignHorzEnum.Far
            .Splits(0).DisplayColumns("Encaisse").Visible = False
            '.Splits(0).SplitSizeMode = SizeModeEnum.Scalable
            '.Splits(0).ColumnCaptionHeight = 40
            '.Splits(0).RecordSelectors = False
            '.ExtendRightColumn = True
            '.EmptyRows = True
            '.FetchRowStyles = True
            '.AllowSort = False
            '.DirectionAfterEnter = DirectionAfterEnterEnum.MoveNone
            'Style du Caractere et du grid
            ParametreGrid(gReglement)
        End With

    End Sub

    Private Sub bannulerReglement_Click(sender As System.Object, e As System.EventArgs) Handles bannulerReglement.Click
        Me.Hide()
    End Sub

End Class