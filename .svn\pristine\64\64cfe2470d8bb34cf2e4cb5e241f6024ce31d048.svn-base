﻿<?xml version="1.0" encoding="utf-8"?>
<Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
  <EntityContainerMapping StorageEntityContainer="VentesReportsModelStoreContainer" CdmEntityContainer="VentesReportsEntities">
    <EntitySetMapping Name="PARAMETRE_PHARMACIE">
      <EntityTypeMapping TypeName="VentesReportsModel.PARAMETRE_PHARMACIE">
        <MappingFragment StoreEntitySet="PARAMETRE_PHARMACIE">
          <ScalarProperty Name="ActiverOMFAPCI" ColumnName="ActiverOMFAPCI" />
          <ScalarProperty Name="ImageCodeABarre" ColumnName="ImageCodeABarre" />
          <ScalarProperty Name="CodeGSU" ColumnName="CodeGSU" />
          <ScalarProperty Name="TauxRemise" ColumnName="TauxRemise" />
          <ScalarProperty Name="Texte" ColumnName="Texte" />
          <ScalarProperty Name="PoliceCaractere" ColumnName="PoliceCaractere" />
          <ScalarProperty Name="TailleCaractere" ColumnName="TailleCaractere" />
          <ScalarProperty Name="TailleListe" ColumnName="TailleListe" />
          <ScalarProperty Name="TailleCodeCNAM" ColumnName="TailleCodeCNAM" />
          <ScalarProperty Name="Latitude_Longitude" ColumnName="Latitude_Longitude" />
          <ScalarProperty Name="NumeroLotProduction" ColumnName="NumeroLotProduction" />
          <ScalarProperty Name="NbreJourValiditeParDefaut" ColumnName="NbreJourValiditeParDefaut" />
          <ScalarProperty Name="AutoriserEnvoiMail" ColumnName="AutoriserEnvoiMail" />
          <ScalarProperty Name="MotDePasseDestinateur" ColumnName="MotDePasseDestinateur" />
          <ScalarProperty Name="TexteMail" ColumnName="TexteMail" />
          <ScalarProperty Name="SujetMail" ColumnName="SujetMail" />
          <ScalarProperty Name="AdresseMailDestinateur" ColumnName="AdresseMailDestinateur" />
          <ScalarProperty Name="PortMail" ColumnName="PortMail" />
          <ScalarProperty Name="SmtpMail" ColumnName="SmtpMail" />
          <ScalarProperty Name="DateMigration" ColumnName="DateMigration" />
          <ScalarProperty Name="DemandeMotDePasse" ColumnName="DemandeMotDePasse" />
          <ScalarProperty Name="Timbre" ColumnName="Timbre" />
          <ScalarProperty Name="Messagederoulant2" ColumnName="Messagederoulant2" />
          <ScalarProperty Name="Messagederoulant1" ColumnName="Messagederoulant1" />
          <ScalarProperty Name="Rib" ColumnName="Rib" />
          <ScalarProperty Name="CodeTVA" ColumnName="CodeTVA" />
          <ScalarProperty Name="Fax" ColumnName="Fax" />
          <ScalarProperty Name="Telephone" ColumnName="Telephone" />
          <ScalarProperty Name="Adresse" ColumnName="Adresse" />
          <ScalarProperty Name="Affiliation2" ColumnName="Affiliation2" />
          <ScalarProperty Name="Affiliation1" ColumnName="Affiliation1" />
          <ScalarProperty Name="NCnam" ColumnName="NCnam" />
          <ScalarProperty Name="Pharmacie" ColumnName="Pharmacie" />
          <ScalarProperty Name="CodePharmacie" ColumnName="CodePharmacie" />
          <ScalarProperty Name="Code" ColumnName="Code" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="V_Report_EtatDesFactures">
      <EntityTypeMapping TypeName="VentesReportsModel.V_Report_EtatDesFactures">
        <MappingFragment StoreEntitySet="V_Report_EtatDesFactures">
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
          <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
          <ScalarProperty Name="Tva" ColumnName="Tva" />
          <ScalarProperty Name="TotalVenteHT" ColumnName="TotalVenteHT" />
          <ScalarProperty Name="Honoraire" ColumnName="Honoraire" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
          <ScalarProperty Name="Nom" ColumnName="Nom" />
          <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="V_Report_EtatDetailDesVentes">
      <EntityTypeMapping TypeName="VentesReportsModel.V_Report_EtatDetailDesVentes">
        <MappingFragment StoreEntitySet="V_Report_EtatDetailDesVentes">
          <ScalarProperty Name="CodeForme" ColumnName="CodeForme" />
          <ScalarProperty Name="CodeCategorie" ColumnName="CodeCategorie" />
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
          <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="Tva" ColumnName="Tva" />
          <ScalarProperty Name="Quantite" ColumnName="Quantite" />
          <ScalarProperty Name="Nom" ColumnName="Nom" />
          <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
          <ScalarProperty Name="LibelleCategorie" ColumnName="LibelleCategorie" />
          <ScalarProperty Name="LibelleForme" ColumnName="LibelleForme" />
          <ScalarProperty Name="Designation" ColumnName="Designation" />
          <ScalarProperty Name="CodeABarre" ColumnName="CodeABarre" />
          <ScalarProperty Name="CodeArticle" ColumnName="CodeArticle" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="V_Report_EtatDetailsCaisse">
      <EntityTypeMapping TypeName="VentesReportsModel.V_Report_EtatDetailsCaisse">
        <MappingFragment StoreEntitySet="V_Report_EtatDetailsCaisse">
          <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
          <ScalarProperty Name="Poste" ColumnName="Poste" />
          <ScalarProperty Name="Vendeur" ColumnName="Vendeur" />
          <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
          <ScalarProperty Name="DateEcheance" ColumnName="DateEcheance" />
          <ScalarProperty Name="Reglement" ColumnName="Reglement" />
          <ScalarProperty Name="Credit" ColumnName="Credit" />
          <ScalarProperty Name="TotalVenteTTC" ColumnName="TotalVenteTTC" />
          <ScalarProperty Name="Nom" ColumnName="Nom" />
          <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Type" ColumnName="Type" />
          <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="V_Report_EtatJournalDesVentes">
      <EntityTypeMapping TypeName="VentesReportsModel.V_Report_EtatJournalDesVentes">
        <MappingFragment StoreEntitySet="V_Report_EtatJournalDesVentes">
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
          <ScalarProperty Name="HR" ColumnName="HR" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="TotalTVA" ColumnName="TotalTVA" />
          <ScalarProperty Name="TotalHT" ColumnName="TotalHT" />
          <ScalarProperty Name="TVA18" ColumnName="TVA18" />
          <ScalarProperty Name="BaseTVA18" ColumnName="BaseTVA18" />
          <ScalarProperty Name="TVA12" ColumnName="TVA12" />
          <ScalarProperty Name="BaseTVA12" ColumnName="BaseTVA12" />
          <ScalarProperty Name="TVA6" ColumnName="TVA6" />
          <ScalarProperty Name="BaseTVA6" ColumnName="BaseTVA6" />
          <ScalarProperty Name="Exonore" ColumnName="Exonore" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="V_Report_VentesAnnuelles">
      <EntityTypeMapping TypeName="VentesReportsModel.V_Report_VentesAnnuelles">
        <MappingFragment StoreEntitySet="V_Report_VentesAnnuelles">
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="Caisse" ColumnName="Caisse" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="Cnam" ColumnName="Cnam" />
          <ScalarProperty Name="Mutuelle" ColumnName="Mutuelle" />
          <ScalarProperty Name="Credit" ColumnName="Credit" />
          <ScalarProperty Name="Comptant" ColumnName="Comptant" />
          <ScalarProperty Name="Annee" ColumnName="Annee" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="V_Report_VentesMensuelles">
      <EntityTypeMapping TypeName="VentesReportsModel.V_Report_VentesMensuelles">
        <MappingFragment StoreEntitySet="V_Report_VentesMensuelles">
          <ScalarProperty Name="NumYear" ColumnName="NumYear" />
          <ScalarProperty Name="NumMois" ColumnName="NumMois" />
          <ScalarProperty Name="Caisse" ColumnName="Caisse" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="Remise" ColumnName="Remise" />
          <ScalarProperty Name="Cnam" ColumnName="Cnam" />
          <ScalarProperty Name="Mutuelle" ColumnName="Mutuelle" />
          <ScalarProperty Name="Credit" ColumnName="Credit" />
          <ScalarProperty Name="Comptant" ColumnName="Comptant" />
          <ScalarProperty Name="NomMois" ColumnName="NomMois" />
          <ScalarProperty Name="NumeroMois" ColumnName="NumeroMois" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="V_Report_VentesQuotidiennes">
      <EntityTypeMapping TypeName="VentesReportsModel.V_Report_VentesQuotidiennes">
        <MappingFragment StoreEntitySet="V_Report_VentesQuotidiennes">
          <ScalarProperty Name="NumYear" ColumnName="NumYear" />
          <ScalarProperty Name="NumMois" ColumnName="NumMois" />
          <ScalarProperty Name="NumJour" ColumnName="NumJour" />
          <ScalarProperty Name="Caisse" ColumnName="Caisse" />
          <ScalarProperty Name="Reglement" ColumnName="Reglement" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="Cnam" ColumnName="Cnam" />
          <ScalarProperty Name="Mutuelle" ColumnName="Mutuelle" />
          <ScalarProperty Name="Comptant" ColumnName="Comptant" />
          <ScalarProperty Name="NumeroJour" ColumnName="NumeroJour" />
          <ScalarProperty Name="Id" ColumnName="Id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatDesFactures" FunctionName="VentesReportsModel.Store.P_Report_EtatDesFactures" />
    <FunctionImportMapping FunctionImportName="P_Report_EtatDesVentes" FunctionName="VentesReportsModel.Store.P_Report_EtatDesVentes" />
    <FunctionImportMapping FunctionImportName="P_Report_EtatDetailDesVentes" FunctionName="VentesReportsModel.Store.P_Report_EtatDetailDesVentes" />
    <FunctionImportMapping FunctionImportName="P_Report_EtatDetailsCaisse" FunctionName="VentesReportsModel.Store.P_Report_EtatDetailsCaisse" />
    <FunctionImportMapping FunctionImportName="P_Report_EtatJournalDesVentes" FunctionName="VentesReportsModel.Store.P_Report_EtatJournalDesVentes" />
    <FunctionImportMapping FunctionImportName="P_Report_VentesAnnuelles" FunctionName="VentesReportsModel.Store.P_Report_VentesAnnuelles" />
    <FunctionImportMapping FunctionImportName="P_Report_VentesMensuelles" FunctionName="VentesReportsModel.Store.P_Report_VentesMensuelles" />
    <FunctionImportMapping FunctionImportName="P_Report_VentesQuotidienne" FunctionName="VentesReportsModel.Store.P_Report_VentesQuotidienne" />
  </EntityContainerMapping>
</Mapping>