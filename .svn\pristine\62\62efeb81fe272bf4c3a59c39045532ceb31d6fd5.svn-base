﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class fListeReglement
    Inherits System.Windows.Forms.Form

    'Form remplace la méthode Dispose pour nettoyer la liste des composants.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requise par le Concepteur Windows Form
    Private components As System.ComponentModel.IContainer

    'REMARQUE : la procédure suivante est requise par le Concepteur Windows Form
    'Elle peut être modifiée à l'aide du Concepteur Windows Form.  
    'Ne la modifiez pas à l'aide de l'éditeur de code.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(fListeReglement))
        Me.Panel = New System.Windows.Forms.Panel()
        Me.gReglement = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.bannulerReglement = New C1.Win.C1Input.C1Button()
        Me.Panel.SuspendLayout()
        CType(Me.gReglement, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel
        '
        Me.Panel.BackColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(241, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.Panel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel.Controls.Add(Me.gReglement)
        Me.Panel.Controls.Add(Me.bannulerReglement)
        Me.Panel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel.Location = New System.Drawing.Point(0, 0)
        Me.Panel.Name = "Panel"
        Me.Panel.Size = New System.Drawing.Size(837, 415)
        Me.Panel.TabIndex = 4
        '
        'gReglement
        '
        Me.gReglement.AllowUpdate = False
        Me.gReglement.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gReglement.FetchRowStyles = True
        Me.gReglement.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gReglement.GroupByCaption = "Drag a column header here to group by that column"
        Me.gReglement.Images.Add(CType(resources.GetObject("gReglement.Images"), System.Drawing.Image))
        Me.gReglement.LinesPerRow = 2
        Me.gReglement.Location = New System.Drawing.Point(3, 11)
        Me.gReglement.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.gReglement.Name = "gReglement"
        Me.gReglement.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.gReglement.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.gReglement.PreviewInfo.ZoomFactor = 75.0R
        Me.gReglement.PrintInfo.PageSettings = CType(resources.GetObject("gAchats.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.gReglement.Size = New System.Drawing.Size(828, 337)
        Me.gReglement.TabIndex = 70
        Me.gReglement.Text = "C1TrueDBGrid1"
        Me.gReglement.PropBag = resources.GetString("gReglement.PropBag")
        '
        'bannulerReglement
        '
        Me.bannulerReglement.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.bannulerReglement.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bannulerReglement.Image = Global.Pharma2000Premium.My.Resources.Resources.Sortir
        Me.bannulerReglement.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.bannulerReglement.Location = New System.Drawing.Point(725, 357)
        Me.bannulerReglement.Name = "bannulerReglement"
        Me.bannulerReglement.Size = New System.Drawing.Size(107, 45)
        Me.bannulerReglement.TabIndex = 68
        Me.bannulerReglement.Text = "Annuler        F10"
        Me.bannulerReglement.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage
        Me.bannulerReglement.UseVisualStyleBackColor = True
        Me.bannulerReglement.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'fListeReglement
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(837, 415)
        Me.Controls.Add(Me.Panel)
        Me.Name = "fListeReglement"
        Me.Text = "fListeReglement"
        Me.Panel.ResumeLayout(False)
        CType(Me.gReglement, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel As System.Windows.Forms.Panel
    Friend WithEvents gReglement As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents bannulerReglement As C1.Win.C1Input.C1Button
End Class
