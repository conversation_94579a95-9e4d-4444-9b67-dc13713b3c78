﻿<?xml version="1.0" encoding="utf-8"?>
<Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
  <EntityContainerMapping StorageEntityContainer="PharmaModelStoreContainer" CdmEntityContainer="PharmaEntities">
    <EntitySetMapping Name="V_Report_EtatDesVentes">
      <EntityTypeMapping TypeName="PharmaModel.V_Report_EtatDesVentes">
        <MappingFragment StoreEntitySet="V_Report_EtatDesVentes">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="Type" ColumnName="Type" />
          <ScalarProperty Name="Nom" ColumnName="Nom" />
          <ScalarProperty Name="MP" ColumnName="MP" />
          <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
          <ScalarProperty Name="NomUtilisateur" ColumnName="NomUtilisateur" />
          <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
          <ScalarProperty Name="TypeOperation" ColumnName="TypeOperation" />
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
          <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
          <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
          <ScalarProperty Name="CodeMutuelle" ColumnName="CodeMutuelle" />
          <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
          <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
          <ScalarProperty Name="Mutuelle" ColumnName="Mutuelle" />
          <ScalarProperty Name="Vide" ColumnName="Vide" />
          <ScalarProperty Name="Credit" ColumnName="Credit" />
          <ScalarProperty Name="Debit" ColumnName="Debit" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <FunctionImportMapping FunctionImportName="P_Report_EtatDesVentes" FunctionName="PharmaModel.Store.P_Report_EtatDesVentes">
      <ResultMapping>
        <ComplexTypeMapping TypeName="PharmaModel.P_Report_EtatDesVentes_Result">
          <ScalarProperty Name="Id" ColumnName="Id" />
          <ScalarProperty Name="CodeClient" ColumnName="CodeClient" />
          <ScalarProperty Name="Nom" ColumnName="Nom" />
          <ScalarProperty Name="NumeroOperation" ColumnName="NumeroOperation" />
          <ScalarProperty Name="TypeOperation" ColumnName="TypeOperation" />
          <ScalarProperty Name="Date" ColumnName="Date" />
          <ScalarProperty Name="TotalTTC" ColumnName="TotalTTC" />
          <ScalarProperty Name="TotalRemise" ColumnName="TotalRemise" />
          <ScalarProperty Name="NumeroFacture" ColumnName="NumeroFacture" />
          <ScalarProperty Name="CodeNatureReglement" ColumnName="CodeNatureReglement" />
          <ScalarProperty Name="MP" ColumnName="MP" />
          <ScalarProperty Name="CodePersonnel" ColumnName="CodePersonnel" />
          <ScalarProperty Name="NomUtilisateur" ColumnName="NomUtilisateur" />
          <ScalarProperty Name="LibellePoste" ColumnName="LibellePoste" />
          <ScalarProperty Name="Type" ColumnName="Type" />
          <ScalarProperty Name="CodeMutuelle" ColumnName="CodeMutuelle" />
          <ScalarProperty Name="Mutuelle" ColumnName="Mutuelle" />
          <ScalarProperty Name="MontantCnam" ColumnName="MontantCnam" />
          <ScalarProperty Name="MontantMutuelle" ColumnName="MontantMutuelle" />
          <ScalarProperty Name="Vide" ColumnName="Vide" />
          <ScalarProperty Name="Credit" ColumnName="Credit" />
          <ScalarProperty Name="Debit" ColumnName="Debit" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
  </EntityContainerMapping>
</Mapping>