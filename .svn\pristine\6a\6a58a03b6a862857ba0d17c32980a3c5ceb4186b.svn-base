﻿Imports System.IO
Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms

Public Class fOrdonnancier

    Public CodeMedecin As String = ""
    Public NomClient As String = ""
    Public Adresse As String = ""
    Public NCIN As String = ""

    Dim cmdChargement As New SqlCommand
    Dim cbChargement As New SqlCommandBuilder
    Dim dsChargement As New DataSet
    Dim daChargement As New SqlDataAdapter

    Public Confirmer As Boolean = False
    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Confirmer = False
        Me.Hide()
    End Sub

    Private Sub fOrdonnancier_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        tNomClient.Value = NomClient


        Dim StrSQL As String = ""

        'chargement des Medecins
        StrSQL = "SELECT CodeMedecin,NomMedecin FROM MEDECIN WHERE supprimer = 0 ORDER BY NomMedecin ASC"
        cmdChargement.Connection = ConnectionServeur
        cmdChargement.CommandText = StrSQL
        daChargement = New SqlDataAdapter(cmdChargement)
        daChargement.Fill(dsChargement, "MEDECIN")
        cmbMedecin.DataSource = dsChargement.Tables("MEDECIN")
        cmbMedecin.ValueMember = "CodeMedecin"
        cmbMedecin.DisplayMember = "NomMedecin"
        cmbMedecin.ColumnHeaders = False
        cmbMedecin.Splits(0).DisplayColumns("CodeMedecin").Visible = False
        cmbMedecin.Splits(0).DisplayColumns("NomMedecin").Width = 10
        cmbMedecin.ExtendRightColumn = True

        cmbMedecin.SelectedValue = CodeMedecin

    End Sub

    Private Sub bOk_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bOk.Click

        If cmbMedecin.Text = "" Then
            MsgBox("Veuillez choisir un medecin !", MsgBoxStyle.Critical, "Erreur")
            cmbMedecin.Focus()
            Exit Sub
        End If
        If tNomClient.Text = "" Then
            MsgBox("Veuillez introduire un client !", MsgBoxStyle.Critical, "Erreur")
            tNomClient.Focus()
            Exit Sub
        End If

        CodeMedecin = cmbMedecin.SelectedValue
        NomClient = tNomClient.Text
        Adresse = tAdresseClient.Text
        NCIN = tNCIN.Text

        Confirmer = True
        Me.Hide()
    End Sub

    Private Sub cmbMedecin_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMedecin.KeyUp

        If e.KeyCode = Keys.Enter And cmbMedecin.Text <> "" Then
            If cmbMedecin.WillChangeToText <> "" Then
                cmbMedecin.Text = cmbMedecin.WillChangeToText
            Else
                Dim Enregistrer As Boolean = False

                Dim MyAjouteMedecin As New fAjouterMedecin

                MyAjouteMedecin.Valeur = cmbMedecin.Text
                MyAjouteMedecin.InsertUpdate = True
                MyAjouteMedecin.ShowDialog()

                Enregistrer = MyAjouteMedecin.Enregistrer

                MyAjouteMedecin.Close()
                MyAjouteMedecin.Dispose()
                If Enregistrer = False Then
                    cmbMedecin.Text = ""
                Else

                    If (dsChargement.Tables.IndexOf("MEDECIN") > -1) Then
                        dsChargement.Tables("MEDECIN").Clear()
                    End If

                    Dim NouvelleCategorie As String = cmbMedecin.Text
                    dsChargement.Tables("MEDECIN").Clear()
                    Dim StrSQL1 As String = ""
                    'chargement des Formes
                    StrSQL1 = "SELECT DISTINCT CodeMedecin,NomMedecin FROM MEDECIN ORDER BY NomMedecin ASC"
                    cmdChargement.Connection = ConnectionServeur
                    cmdChargement.CommandText = StrSQL1
                    daChargement = New SqlDataAdapter(cmdChargement)
                    daChargement.Fill(dsChargement, "MEDECIN")
                    cmbMedecin.DataSource = dsChargement.Tables("MEDECIN")
                    cmbMedecin.ValueMember = "CodeMedecin"
                    cmbMedecin.DisplayMember = "NomMedecin"
                    cmbMedecin.ColumnHeaders = False
                    cmbMedecin.Splits(0).DisplayColumns("CodeMedecin").Width = 0
                    cmbMedecin.Splits(0).DisplayColumns("NomMedecin").Width = 160
                    cmbMedecin.Text = NouvelleCategorie
                End If
            End If
        Else
            cmbMedecin.OpenCombo()
        End If

        If e.KeyCode = Keys.Enter Then
            tNomClient.Focus()
            Exit Sub
        End If
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub cmbMedecin_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbMedecin.LostFocus

        If cmbMedecin.Text <> "" Then
            If cmbMedecin.WillChangeToText <> "" Then
                cmbMedecin.Text = cmbMedecin.WillChangeToText
                tNomClient.Focus()
            Else
                cmbMedecin.Focus()
            End If
        Else
            cmbMedecin.Focus()
        End If

    End Sub

    Private Sub cmbMedecin_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbMedecin.TextChanged

    End Sub

    Private Sub tNomClient_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNomClient.KeyDown
        
    End Sub

    Private Sub tNomClient_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles tNomClient.KeyPress
        
    End Sub

    Private Sub tNomClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNomClient.KeyUp
        If e.KeyCode = Keys.Enter And tNomClient.Text <> "" Then
            tAdresseClient.Focus()
            Exit Sub
        End If
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub tNomClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNomClient.TextChanged

    End Sub

    Private Sub tAdresseClient_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tAdresseClient.KeyUp
        If e.KeyCode = Keys.Enter Then
            tNCIN.Focus()
            Exit Sub
        End If
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub tAdresseClient_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tAdresseClient.TextChanged

    End Sub

    Private Sub tNCIN_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tNCIN.KeyUp
        If e.KeyCode = Keys.Enter Then
            bOk.Focus()
            Exit Sub
        End If
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub tNCIN_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tNCIN.TextChanged

    End Sub

    Private Sub bOk_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bOk.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub

    Private Sub bAnnuler_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles bAnnuler.KeyUp
        If e.KeyCode = Keys.F3 Then
            bOk_Click(sender, e)
            Exit Sub
        End If
        If e.KeyCode = Keys.F10 Then
            bAnnuler_Click(sender, e)
            Exit Sub
        End If
    End Sub
End Class