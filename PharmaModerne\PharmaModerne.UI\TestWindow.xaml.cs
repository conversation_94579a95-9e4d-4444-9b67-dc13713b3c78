using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;

namespace PharmaModerne.UI
{
    /// <summary>
    /// Fenêtre de test ultra-simple pour vérifier que l'application fonctionne
    /// </summary>
    public partial class TestWindow : Window
    {
        private readonly DispatcherTimer _timer;

        public TestWindow()
        {
            InitializeComponent();
            
            // Timer pour l'heure
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _timer.Tick += Timer_Tick;
            _timer.Start();
            
            UpdateTime();
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            UpdateTime();
        }

        private void UpdateTime()
        {
            TimeTextBlock.Text = DateTime.Now.ToString("HH:mm:ss");
        }

        private void TestButton_Click(object sender, RoutedEventArgs e)
        {
            var input = TestTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(input))
            {
                ResultTextBlock.Text = "❌ Veuillez saisir un code à tester";
                return;
            }

            // Simulation de détection du scanner
            var isScanner = input.Length > 5 && !input.Contains(" ");
            var currentTime = DateTime.Now;

            ResultTextBlock.Text = $"✅ Test réussi !\n" +
                                 $"📱 Code : '{input}'\n" +
                                 $"🔍 Type : {(isScanner ? "Scanner détecté" : "Saisie manuelle")}\n" +
                                 $"⏰ Heure : {currentTime:HH:mm:ss}\n" +
                                 $"📏 Longueur : {input.Length} caractères";

            // Vider le champ
            TestTextBox.Text = "";
            TestTextBox.Focus();
        }

        private void ModuleButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string module)
            {
                MessageBox.Show(
                    $"Module {module} prêt !\n\n" +
                    "✅ Architecture implémentée\n" +
                    "✅ Services disponibles\n" +
                    "✅ Scanner intégré\n\n" +
                    "Le développement des interfaces utilisateur peut continuer.",
                    $"Module {module}",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            base.OnClosed(e);
        }
    }
}
