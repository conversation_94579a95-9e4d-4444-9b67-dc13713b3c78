﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="PharmaModel" Alias="Self" p1:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:p1="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
  <EntityContainer Name="PharmaEntities" p1:LazyLoadingEnabled="true">
    <EntitySet Name="V_Report_EtatDesVentes" EntityType="PharmaModel.V_Report_EtatDesVentes" />
    <FunctionImport Name="P_Report_EtatDesVentes" ReturnType="Collection(PharmaModel.P_Report_EtatDesVentes_Result)">
      <Parameter Name="DateDebut" Mode="In" Type="DateTime" />
      <Parameter Name="DateFin" Mode="In" Type="DateTime" />
      <Parameter Name="Type" Mode="In" Type="String" />
      <Parameter Name="Facturer" Mode="In" Type="Int32" />
    </FunctionImport>
  </EntityContainer>
  <EntityType Name="V_Report_EtatDesVentes">
    <Key>
      <PropertyRef Name="Id" />
    </Key>
    <Property Name="Id" Type="Int32" Nullable="false" />
    <Property Name="NumeroOperation" Type="String" MaxLength="Max" Unicode="false" FixedLength="false" />
    <Property Name="Date" Type="DateTime" Precision="3" />
    <Property Name="Type" Type="String" MaxLength="Max" Unicode="false" FixedLength="false" />
    <Property Name="Nom" Type="String" MaxLength="Max" Unicode="false" FixedLength="false" />
    <Property Name="MP" Type="String" MaxLength="Max" Unicode="false" FixedLength="false" />
    <Property Name="TotalRemise" Type="Decimal" Precision="18" Scale="3" />
    <Property Name="TotalTTC" Type="Decimal" Precision="18" Scale="3" />
    <Property Name="LibellePoste" Type="String" MaxLength="Max" Unicode="false" FixedLength="false" />
    <Property Name="NomUtilisateur" Type="String" MaxLength="Max" Unicode="false" FixedLength="false" />
    <Property Name="CodeClient" Type="String" MaxLength="Max" Unicode="false" FixedLength="false" />
    <Property Name="TypeOperation" Type="String" MaxLength="Max" Unicode="false" FixedLength="false" />
    <Property Name="NumeroFacture" Type="String" MaxLength="Max" Unicode="false" FixedLength="false" />
    <Property Name="CodeNatureReglement" Type="Int32" />
    <Property Name="CodePersonnel" Type="String" MaxLength="Max" Unicode="false" FixedLength="false" />
    <Property Name="CodeMutuelle" Type="String" MaxLength="Max" Unicode="false" FixedLength="false" />
    <Property Name="MontantCnam" Type="Decimal" Precision="18" Scale="3" />
    <Property Name="MontantMutuelle" Type="Decimal" Precision="18" Scale="3" />
    <Property Name="Mutuelle" Type="String" MaxLength="Max" Unicode="false" FixedLength="false" />
    <Property Name="Vide" Type="Boolean" />
    <Property Name="Credit" Type="Decimal" Precision="18" Scale="3" />
    <Property Name="Debit" Type="Decimal" Precision="18" Scale="3" />
  </EntityType>
  <ComplexType Name="P_Report_EtatDesVentes_Result">
    <Property Type="Int32" Name="Id" Nullable="true" />
    <Property Type="String" Name="CodeClient" Nullable="true" />
    <Property Type="String" Name="Nom" Nullable="true" />
    <Property Type="String" Name="NumeroOperation" Nullable="true" />
    <Property Type="String" Name="TypeOperation" Nullable="true" />
    <Property Type="DateTime" Name="Date" Nullable="true" Precision="23" />
    <Property Type="Decimal" Name="TotalTTC" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="TotalRemise" Nullable="true" Precision="18" Scale="3" />
    <Property Type="String" Name="NumeroFacture" Nullable="true" />
    <Property Type="Int32" Name="CodeNatureReglement" Nullable="true" />
    <Property Type="String" Name="MP" Nullable="true" />
    <Property Type="String" Name="CodePersonnel" Nullable="true" />
    <Property Type="String" Name="NomUtilisateur" Nullable="true" />
    <Property Type="String" Name="LibellePoste" Nullable="true" />
    <Property Type="String" Name="Type" Nullable="true" />
    <Property Type="String" Name="CodeMutuelle" Nullable="true" />
    <Property Type="String" Name="Mutuelle" Nullable="true" />
    <Property Type="Decimal" Name="MontantCnam" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="MontantMutuelle" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Boolean" Name="Vide" Nullable="true" />
    <Property Type="Decimal" Name="Credit" Nullable="true" Precision="18" Scale="3" />
    <Property Type="Decimal" Name="Debit" Nullable="true" Precision="18" Scale="3" />
  </ComplexType>
</Schema>