﻿Imports System.IO
Imports System.Data.SqlClient
Imports System.Windows.Forms

Public Class fAjouterForme

    Dim cmd As New SqlCommand
    Public Enregistrer As Boolean = False
    Public Valeur As String = ""


    Private Sub bEnregistrer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bEnregistrer.Click
        Dim StrSQL As String = ""
        Dim StrMajLOT As String = ""
        Dim DernierCodeForme As Integer = 0

        StrSQL = " SELECT max(CodeForme) FROM [FORME_ARTICLE]"

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrSQL
        Try
            DernierCodeForme = cmd.ExecuteScalar()
            If Trim(DernierCodeForme) <> "" Then
                DernierCodeForme = DernierCodeForme + 1
            Else
                DernierCodeForme = "0"
            End If

        Catch ex As Exception
            Console.WriteLine(ex.Message)
            DernierCodeForme = "0"
        End Try

        StrMajLOT = "INSERT INTO FORME_ARTICLE (""CodeForme"",""LibelleForme"") " + _
                    " VALUES(" + DernierCodeForme.ToString + ",'" + tForme.Text + "')"

        cmd.Connection = ConnectionServeur
        cmd.CommandText = StrMajLOT
        Try
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

        Enregistrer = True
        Me.Hide()
    End Sub

    Private Sub bAnnuler_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bAnnuler.Click
        Enregistrer = False
        Me.Hide()
    End Sub

    Private Sub fAjouterForme_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        tForme.Value = Valeur
    End Sub
End Class